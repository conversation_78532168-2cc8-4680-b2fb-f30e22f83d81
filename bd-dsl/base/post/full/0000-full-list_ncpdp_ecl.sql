BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_ncpdp_ecl;
INSERT INTO form_list_ncpdp_ecl (id,field,code,name,created_on,created_by,auto_name) VALUES
 (1,'102-A2','10','Version 1.0','2025-03-14 17:30:12',1, '10 - Version 1.0')
,(2,'102-A2','20','Version 2.0','2025-03-14 17:30:12',1, '20 - Version 2.0')
,(3,'102-A2','30','Version 3.0','2025-03-14 17:30:12',1, '30 - Version 3.0')
,(4,'102-A2','31','Version 3.1','2025-03-14 17:30:12',1, '31 - Version 3.1')
,(5,'102-A2','32','Version 3.2','2025-03-14 17:30:12',1, '32 - Version 3.2')
,(6,'102-A2','33','Version 3.3','2025-03-14 17:30:12',1, '33 - Version 3.3')
,(7,'102-A2','34','Version 3.4','2025-03-14 17:30:12',1, '34 - Version 3.4')
,(8,'102-A2','35','Version 3.5','2025-03-14 17:30:12',1, '35 - Version 3.5')
,(9,'102-A2','40','Version 4.0','2025-03-14 17:30:12',1, '40 - Version 4.0')
,(10,'102-A2','41','Version 4.1','2025-03-14 17:30:12',1, '41 - Version 4.1')
,(11,'102-A2','42','Version 4.2','2025-03-14 17:30:12',1, '42 - Version 4.2')
,(12,'102-A2','50','Version 5.0','2025-03-14 17:30:12',1, '50 - Version 5.0')
,(13,'102-A2','51','Version 5.1','2025-03-14 17:30:12',1, '51 - Version 5.1')
,(14,'102-A2','52','Version 5.2','2025-03-14 17:30:12',1, '52 - Version 5.2')
,(15,'102-A2','53','Version 5.3','2025-03-14 17:30:12',1, '53 - Version 5.3')
,(16,'102-A2','54','Version 5.4','2025-03-14 17:30:12',1, '54 - Version 5.4')
,(17,'102-A2','55','Version 5.5','2025-03-14 17:30:12',1, '55 - Version 5.5')
,(18,'102-A2','56','Version 5.6','2025-03-14 17:30:12',1, '56 - Version 5.6')
,(19,'102-A2','60','Version 6.0','2025-03-14 17:30:12',1, '60 - Version 6.0')
,(20,'102-A2','70','Version 7.0','2025-03-14 17:30:12',1, '70 - Version 7.0')
,(21,'102-A2','71','Version 7.1','2025-03-14 17:30:12',1, '71 - Version 7.1')
,(22,'102-A2','80','Version 8.0','2025-03-14 17:30:12',1, '80 - Version 8.0')
,(23,'102-A2','81','Version 8.1','2025-03-14 17:30:12',1, '81 - Version 8.1')
,(24,'102-A2','82','Version 8.2','2025-03-14 17:30:12',1, '82 - Version 8.2')
,(25,'102-A2','83','Version 8.3','2025-03-14 17:30:12',1, '83 - Version 8.3')
,(26,'102-A2','90','Version 9.0','2025-03-14 17:30:12',1, '90 - Version 9.0')
,(27,'102-A2','A0','Version A.0','2025-03-14 17:30:12',1, 'A0 - Version A.0')
,(28,'102-A2','A1','Version A.1','2025-03-14 17:30:12',1, 'A1 - Version A.1')
,(29,'102-A2','B0','Version B.0','2025-03-14 17:30:12',1, 'B0 - Version B.0')
,(30,'102-A2','C0','Version C.0','2025-03-14 17:30:12',1, 'C0 - Version C.0')
,(31,'102-A2','C1','Version C.1','2025-03-14 17:30:12',1, 'C1 - Version C.1')
,(32,'102-A2','C2','Version C.2','2025-03-14 17:30:12',1, 'C2 - Version C.2')
,(33,'102-A2','C3','Version C.3','2025-03-14 17:30:12',1, 'C3 - Version C.3')
,(34,'102-A2','C4','Version C.4','2025-03-14 17:30:12',1, 'C4 - Version C.4')
,(35,'102-A2','D0','Version D.0','2025-03-14 17:30:12',1, 'D0 - Version D.0')
,(36,'102-A2','D1','Version D.1','2025-03-14 17:30:12',1, 'D1 - Version D.1')
,(37,'102-A2','D2','Version D.2','2025-03-14 17:30:12',1, 'D2 - Version D.2')
,(38,'102-A2','D3','Version D.3','2025-03-14 17:30:12',1, 'D3 - Version D.3')
,(39,'102-A2','D4','Version D.4','2025-03-14 17:30:12',1, 'D4 - Version D.4')
,(40,'102-A2','D5','Version D.5','2025-03-14 17:30:12',1, 'D5 - Version D.5')
,(41,'102-A2','D6','Version D.6','2025-03-14 17:30:12',1, 'D6 - Version D.6')
,(42,'102-A2','D7','Version D.7','2025-03-14 17:30:12',1, 'D7 - Version D.7')
,(43,'102-A2','D8','Version D.8','2025-03-14 17:30:12',1, 'D8 - Version D.8')
,(44,'102-A2','D9','Version D.9','2025-03-14 17:30:12',1, 'D9 - Version D.9')
,(45,'102-A2','E0','Version E.0','2025-03-14 17:30:12',1, 'E0 - Version E.0')
,(46,'102-A2','E1','Version E.1','2025-03-14 17:30:12',1, 'E1 - Version E.1')
,(47,'102-A2','E2','Version E.2','2025-03-14 17:30:12',1, 'E2 - Version E.2')
,(48,'102-A2','E3','Version E.3','2025-03-14 17:30:12',1, 'E3 - Version E.3')
,(49,'102-A2','E4','Version E.4','2025-03-14 17:30:12',1, 'E4 - Version E.4')
,(50,'102-A2','E5','Version E5','2025-03-14 17:30:12',1, 'E5 - Version E5')
,(51,'102-A2','E6','Version E6','2025-03-14 17:30:12',1, 'E6 - Version E6')
,(52,'102-A2','E7','Version E7','2025-03-14 17:30:12',1, 'E7 - Version E7')
,(53,'102-A2','E8','Version E8','2025-03-14 17:30:12',1, 'E8 - Version E8')
,(54,'102-A2','E9','Version E9','2025-03-14 17:30:12',1, 'E9 - Version E9')
,(55,'102-A2','EB','Version EB','2025-03-14 17:30:12',1, 'EB - Version EB')
,(56,'102-A2','F2','Version F2','2025-03-14 17:30:12',1, 'F2 - Version F2')
,(57,'103-A3','B1','Billing','2025-03-14 17:30:12',1, 'B1 - Billing')
,(58,'103-A3','B2','Reversal','2025-03-14 17:30:12',1, 'B2 - Reversal')
,(59,'103-A3','B3','Rebill','2025-03-14 17:30:12',1, 'B3 - Rebill')
,(60,'103-A3','C1','Controlled Substance Reporting','2025-03-14 17:30:12',1, 'C1 - Controlled Substance Reporting')
,(61,'103-A3','C2','Controlled Substance Reporting Reversal','2025-03-14 17:30:12',1, 'C2 - Controlled Substance Reporting Reversal')
,(62,'103-A3','C3','Controlled Substance Reporting Rebill','2025-03-14 17:30:12',1, 'C3 - Controlled Substance Reporting Rebill')
,(63,'103-A3','D1','Predetermination of Benefits','2025-03-14 17:30:12',1, 'D1 - Predetermination of Benefits')
,(64,'103-A3','E1','Eligibility Verification','2025-03-14 17:30:12',1, 'E1 - Eligibility Verification')
,(65,'103-A3','N1','Information Reporting','2025-03-14 17:30:12',1, 'N1 - Information Reporting')
,(66,'103-A3','N2','Information Reporting Reversal','2025-03-14 17:30:12',1, 'N2 - Information Reporting Reversal')
,(67,'103-A3','N3','Information Reporting Rebill','2025-03-14 17:30:12',1, 'N3 - Information Reporting Rebill')
,(68,'103-A3','P1','P.A. Request & Billing','2025-03-14 17:30:12',1, 'P1 - P.A. Request & Billing')
,(69,'103-A3','P2','P.A. Reversal','2025-03-14 17:30:12',1, 'P2 - P.A. Reversal')
,(70,'103-A3','P3','P.A. Inquiry','2025-03-14 17:30:12',1, 'P3 - P.A. Inquiry')
,(71,'103-A3','P4','P.A. Request Only','2025-03-14 17:30:12',1, 'P4 - P.A. Request Only')
,(72,'103-A3','S1','Service Billing','2025-03-14 17:30:12',1, 'S1 - Service Billing')
,(73,'103-A3','S2','Service Reversal','2025-03-14 17:30:12',1, 'S2 - Service Reversal')
,(74,'103-A3','S3','Service Rebill','2025-03-14 17:30:12',1, 'S3 - Service Rebill')
,(75,'109-A9','1','One Occurrence','2025-03-14 17:30:12',1, '1 - One Occurrence')
,(76,'109-A9','2','Two Occurrences','2025-03-14 17:30:12',1, '2 - Two Occurrences')
,(77,'109-A9','3','Three Occurrences','2025-03-14 17:30:12',1, '3 - Three Occurrences')
,(78,'109-A9','4','Four Occurrences','2025-03-14 17:30:12',1, '4 - Four Occurrences')
,(79,'111-AM','10','Compound','2025-03-14 17:30:12',1, '10 - Compound')
,(80,'111-AM','11','Pricing','2025-03-14 17:30:12',1, '11 - Pricing')
,(81,'111-AM','12','Prior Authorization','2025-03-14 17:30:12',1, '12 - Prior Authorization')
,(82,'111-AM','13','Clinical','2025-03-14 17:30:12',1, '13 - Clinical')
,(83,'111-AM','14','Additional Documentation','2025-03-14 17:30:12',1, '14 - Additional Documentation')
,(84,'111-AM','15','Facility','2025-03-14 17:30:12',1, '15 - Facility')
,(85,'111-AM','16','Narrative','2025-03-14 17:30:12',1, '16 - Narrative')
,(86,'111-AM','17','Purchaser','2025-03-14 17:30:12',1, '17 - Purchaser')
,(87,'111-AM','18','Service Provider','2025-03-14 17:30:12',1, '18 - Service Provider')
,(88,'111-AM','19','Intermediary','2025-03-14 17:30:12',1, '19 - Intermediary')
,(89,'111-AM','20','Response Message','2025-03-14 17:30:12',1, '20 - Response Message')
,(90,'111-AM','21','Response Status','2025-03-14 17:30:12',1, '21 - Response Status')
,(91,'111-AM','22','Response Claim','2025-03-14 17:30:12',1, '22 - Response Claim')
,(92,'111-AM','23','Response Pricing','2025-03-14 17:30:12',1, '23 - Response Pricing')
,(93,'111-AM','24','Response DUR/PPS','2025-03-14 17:30:12',1, '24 - Response DUR/PPS')
,(94,'111-AM','25','Response Insurance','2025-03-14 17:30:12',1, '25 - Response Insurance')
,(95,'111-AM','26','Response Prior Authorization','2025-03-14 17:30:12',1, '26 - Response Prior Authorization')
,(96,'111-AM','27','Response Insurance Additional Information','2025-03-14 17:30:12',1, '27 - Response Insurance Additional Information')
,(97,'111-AM','28','Response Other Payers','2025-03-14 17:30:12',1, '28 - Response Other Payers')
,(98,'111-AM','29','Response Patient','2025-03-14 17:30:12',1, '29 - Response Patient')
,(99,'111-AM','36','Response Intermediary','2025-03-14 17:30:12',1, '36 - Response Intermediary')
,(100,'111-AM','37','Last Known 4Rx Segment','2025-03-14 17:30:12',1, '37 - Last Known 4Rx Segment')
,(101,'111-AM','38','N Transaction Payer Identification','2025-03-14 17:30:12',1, '38 - N Transaction Payer Identification')
,(102,'111-AM','39','Response Other Related Benefit Detail','2025-03-14 17:30:12',1, '39 - Response Other Related Benefit Detail')
,(103,'111-AM','01','Patient','2025-03-14 17:30:12',1, '01 - Patient')
,(104,'111-AM','02','Pharmacy Provider','2025-03-14 17:30:12',1, '02 - Pharmacy Provider')
,(105,'111-AM','03','Prescriber','2025-03-14 17:30:12',1, '03 - Prescriber')
,(106,'111-AM','04','Insurance','2025-03-14 17:30:12',1, '04 - Insurance')
,(107,'111-AM','05','Coordination of Benefits/Other Payments','2025-03-14 17:30:12',1, '05 - Coordination of Benefits/Other Payments')
,(108,'111-AM','06','Workers'' Compensation','2025-03-14 17:30:12',1, '06 - Workers'' Compensation')
,(109,'111-AM','07','Claim','2025-03-14 17:30:12',1, '07 - Claim')
,(110,'111-AM','08','DUR/PPS','2025-03-14 17:30:12',1, '08 - DUR/PPS')
,(111,'111-AM','09','Coupon','2025-03-14 17:30:12',1, '09 - Coupon')
,(112,'112-AN','A','Approved','2025-03-14 17:30:12',1, 'A - Approved')
,(113,'112-AN','B','Benefit','2025-03-14 17:30:12',1, 'B - Benefit')
,(114,'112-AN','C','Captured','2025-03-14 17:30:12',1, 'C - Captured')
,(115,'112-AN','D','Duplicate of Paid','2025-03-14 17:30:12',1, 'D - Duplicate of Paid')
,(116,'112-AN','F','PA Deferred','2025-03-14 17:30:12',1, 'F - PA Deferred')
,(117,'112-AN','P','Paid','2025-03-14 17:30:12',1, 'P - Paid')
,(118,'112-AN','Q','Duplicate of Capture','2025-03-14 17:30:12',1, 'Q - Duplicate of Capture')
,(119,'112-AN','R','Rejected','2025-03-14 17:30:12',1, 'R - Rejected')
,(120,'112-AN','S','Duplicate of Approved','2025-03-14 17:30:12',1, 'S - Duplicate of Approved')
,(121,'117-TR','0','Provider Submitted - Pay to Provider','2025-03-14 17:30:12',1, '0 - Provider Submitted - Pay to Provider')
,(122,'117-TR','1','Provider Submitted - Pay to Another Party','2025-03-14 17:30:12',1, '1 - Provider Submitted - Pay to Another Party')
,(123,'117-TR','2','Agent Submitted - Pay to Agent','2025-03-14 17:30:12',1, '2 - Agent Submitted - Pay to Agent')
,(124,'117-TR','3','Agent Submitted - Pay to Another Party','2025-03-14 17:30:12',1, '3 - Agent Submitted - Pay to Another Party')
,(125,'118-TS','11','Federal Tax ID','2025-03-14 17:30:12',1, '11 - Federal Tax ID')
,(126,'118-TS','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(127,'118-TS','01','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '01 - National Provider Identifier (NPI)')
,(128,'123-TX','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(129,'123-TX','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(130,'123-TX','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(131,'123-TX','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(132,'123-TX','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(133,'123-TX','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(134,'123-TX','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(135,'123-TX','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(136,'123-TX','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(137,'123-TX','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(138,'123-TX','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(139,'123-TX','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(140,'123-TX','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(141,'123-TX','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(142,'123-TX','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(143,'123-TX','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(144,'123-TX','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(145,'123-TX','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(146,'123-TX','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(147,'123-TX','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(148,'123-TX','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(149,'123-TX','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(150,'123-TX','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(151,'123-TX','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(152,'123-TX','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(153,'123-TX','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(154,'123-TX','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(155,'123-TX','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(156,'123-TX','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(157,'123-TX','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(158,'123-TX','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(159,'123-TX','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(160,'123-TX','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(161,'123-TX','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(162,'123-TX','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(163,'123-TX','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(164,'123-TX','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(165,'123-TX','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(166,'123-TX','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(167,'123-TX','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(168,'123-TX','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(169,'123-TX','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(170,'123-TX','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(171,'123-TX','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(172,'123-TX','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(173,'123-TX','56','California','2025-03-14 17:30:12',1, '56 - California')
,(174,'123-TX','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(175,'123-TX','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(176,'123-TX','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(177,'123-TX','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(178,'123-TX','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(179,'123-TX','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(180,'123-TX','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(181,'123-TX','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(182,'123-TX','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(183,'123-TX','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(184,'123-TX','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(185,'123-TX','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(186,'123-TX','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(187,'123-TX','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(188,'123-TX','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(189,'123-TX','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(190,'123-TX','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(191,'123-TX','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(192,'123-TX','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(193,'123-TX','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(194,'123-TX','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(195,'123-TX','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(196,'123-TX','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(197,'123-TX','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(198,'123-TX','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(199,'123-TX','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(200,'123-TX','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(201,'123-TX','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(202,'123-TX','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(203,'123-TX','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(204,'123-TX','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(205,'123-TX','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(206,'123-TX','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(207,'123-TX','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(208,'123-TX','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(209,'123-TX','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(210,'123-TX','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(211,'123-TX','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(212,'123-TX','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(213,'123-TX','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(214,'123-TX','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(215,'123-TX','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(216,'123-TX','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(217,'123-TX','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(218,'123-TX','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(219,'123-TX','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(220,'123-TX','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(221,'123-TX','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(222,'123-TX','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(223,'123-TX','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(224,'123-TX','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(225,'123-TX','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(226,'123-TX','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(227,'123-TX','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(228,'123-TX','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(229,'123-TX','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(230,'123-TX','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(231,'123-TX','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(232,'123-TX','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(233,'123-TX','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(234,'123-TX','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(235,'123-TX','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(236,'123-TX','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(237,'123-TX','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(238,'123-TX','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(239,'123-TX','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(240,'123-TX','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(241,'123-TX','05','California','2025-03-14 17:30:12',1, '05 - California')
,(242,'123-TX','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(243,'123-TX','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(244,'123-TX','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(245,'123-TX','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(246,'123-TX','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(247,'123-TX','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(248,'123-TX','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(249,'123-TX','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(250,'123-TX','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(251,'123-TX','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(252,'123-TX','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(253,'123-TX','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(254,'123-TX','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(255,'123-TX','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(256,'123-TX','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(257,'123-TX','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(258,'123-TX','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(259,'123-TX','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(260,'123-TX','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(261,'123-TX','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(262,'125-TZ','10','Pharmacy Practice Activity Classification (PPAC)','2025-03-14 17:30:12',1, '10 - Pharmacy Practice Activity Classification (PPAC)')
,(263,'125-TZ','11','National Pharmaceutical Product Interface Code (NAPPI)','2025-03-14 17:30:12',1, '11 - National Pharmaceutical Product Interface Code (NAPPI)')
,(264,'125-TZ','12','Global Trade Identification Number (GTIN)','2025-03-14 17:30:12',1, '12 - Global Trade Identification Number (GTIN)')
,(265,'125-TZ','15','First DataBank Formulation ID (GCN)','2025-03-14 17:30:12',1, '15 - First DataBank Formulation ID (GCN)')
,(266,'125-TZ','28','First DataBank Medication Name ID (FDB Med Name ID)','2025-03-14 17:30:12',1, '28 - First DataBank Medication Name ID (FDB Med Name ID)')
,(267,'125-TZ','29','First DataBank Routed Medication ID (FDB Routed Med ID)','2025-03-14 17:30:12',1, '29 - First DataBank Routed Medication ID (FDB Routed Med ID)')
,(268,'125-TZ','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','2025-03-14 17:30:12',1, '30 - First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)')
,(269,'125-TZ','31','First DataBank Medication ID (FDB MedID)','2025-03-14 17:30:12',1, '31 - First DataBank Medication ID (FDB MedID)')
,(270,'125-TZ','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','2025-03-14 17:30:12',1, '32 - First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)')
,(271,'125-TZ','33','First DataBank Ingredient List ID (HICL_SEQNO)','2025-03-14 17:30:12',1, '33 - First DataBank Ingredient List ID (HICL_SEQNO)')
,(272,'125-TZ','34','Universal Product Number (UPIN)','2025-03-14 17:30:12',1, '34 - Universal Product Number (UPIN)')
,(273,'125-TZ','44','Gold Standard Specific Product Identifier (SPID)','2025-03-14 17:30:12',1, '44 - Gold Standard Specific Product Identifier (SPID)')
,(274,'125-TZ','45','Device Identifier (DI)','2025-03-14 17:30:12',1, '45 - Device Identifier (DI)')
,(275,'125-TZ','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(276,'125-TZ','01','Universal Product Code (UPC)','2025-03-14 17:30:12',1, '01 - Universal Product Code (UPC)')
,(277,'125-TZ','02','Health Related Item (HRI)','2025-03-14 17:30:12',1, '02 - Health Related Item (HRI)')
,(278,'125-TZ','03','National Drug Code (NDC)','2025-03-14 17:30:12',1, '03 - National Drug Code (NDC)')
,(279,'125-TZ','04','Health Industry Business Communications Council (HIBCC)','2025-03-14 17:30:12',1, '04 - Health Industry Business Communications Council (HIBCC)')
,(280,'125-TZ','06','Drug Use Review/ Professional Pharmacy Service (DUR/PPS)','2025-03-14 17:30:12',1, '06 - Drug Use Review/ Professional Pharmacy Service (DUR/PPS)')
,(281,'125-TZ','07','Current Procedural Terminology (CPT4)','2025-03-14 17:30:12',1, '07 - Current Procedural Terminology (CPT4)')
,(282,'125-TZ','08','Current Procedural Terminology (CPT5)','2025-03-14 17:30:12',1, '08 - Current Procedural Terminology (CPT5)')
,(283,'125-TZ','09','Healthcare Common Procedure Coding System (HCPCS)','2025-03-14 17:30:12',1, '09 - Healthcare Common Procedure Coding System (HCPCS)')
,(284,'131-UG','+','Current text continues','2025-03-14 17:30:12',1, '+ - Current text continues')
,(285,'132-UH','10','Next Available Fill Date - Next Date of Service on which this claim can be submitted.','2025-03-14 17:30:12',1, '10 - Next Available Fill Date - Next Date of Service on which this claim can be submitted.')
,(286,'132-UH','11','Date The Prior Authorization Ends - Required when prior authorization end date is known.','2025-03-14 17:30:12',1, '11 - Date The Prior Authorization Ends - Required when prior authorization end date is known.')
,(287,'132-UH','12','Maximum Quantity Allowed Over The Designated Time Period. e.g.: Maximum Qty. of 200 tablets per 90-day period would be returned as "200,90".','2025-03-14 17:30:12',1, '12 - Maximum Quantity Allowed Over The Designated Time Period. e.g.: Maximum Qty. of 200 tablets per 90-day period would be returned as "200,90".')
,(288,'132-UH','13','Maximum Days Supply Allowed Over The Designated Time Period. e.g.: 90 day supply allowed per year would be returned as "90,365".','2025-03-14 17:30:12',1, '13 - Maximum Days Supply Allowed Over The Designated Time Period. e.g.: 90 day supply allowed per year would be returned as "90,365".')
,(289,'132-UH','14','Maximum Age','2025-03-14 17:30:12',1, '14 - Maximum Age')
,(290,'132-UH','15','Maximum Quantity','2025-03-14 17:30:12',1, '15 - Maximum Quantity')
,(291,'132-UH','16','Maximum Days Supply','2025-03-14 17:30:12',1, '16 - Maximum Days Supply')
,(292,'132-UH','17','Maximum Fills','2025-03-14 17:30:12',1, '17 - Maximum Fills')
,(293,'132-UH','18','Maximum Dollar Amount','2025-03-14 17:30:12',1, '18 - Maximum Dollar Amount')
,(294,'132-UH','19','Remaining Quantity','2025-03-14 17:30:12',1, '19 - Remaining Quantity')
,(295,'132-UH','20','Remaining Days Supply','2025-03-14 17:30:12',1, '20 - Remaining Days Supply')
,(296,'132-UH','21','Remaining Fills','2025-03-14 17:30:12',1, '21 - Remaining Fills')
,(297,'132-UH','22','Minimum Age','2025-03-14 17:30:12',1, '22 - Minimum Age')
,(298,'132-UH','23','Minimum Quantity','2025-03-14 17:30:12',1, '23 - Minimum Quantity')
,(299,'132-UH','24','Minimum Day Supply','2025-03-14 17:30:12',1, '24 - Minimum Day Supply')
,(300,'132-UH','25','Minimum Dollar Amount','2025-03-14 17:30:12',1, '25 - Minimum Dollar Amount')
,(301,'132-UH','01','Used for first line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '01 - Used for first line of free form text with no pre-defined structure.')
,(302,'132-UH','02','Used for second line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '02 - Used for second line of free form text with no pre-defined structure.')
,(303,'132-UH','03','Used for third line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '03 - Used for third line of free form text with no pre-defined structure.')
,(304,'132-UH','04','Used for fourth line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '04 - Used for fourth line of free form text with no pre-defined structure.')
,(305,'132-UH','05','Used for fifth line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '05 - Used for fifth line of free form text with no pre-defined structure.')
,(306,'132-UH','06','Used for sixth line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '06 - Used for sixth line of free form text with no pre-defined structure.')
,(307,'132-UH','07','Used for seventh line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '07 - Used for seventh line of free form text with no pre-defined structure.')
,(308,'132-UH','08','Used for eighth line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '08 - Used for eighth line of free form text with no pre-defined structure.')
,(309,'132-UH','09','Used for ninth line of free form text with no pre-defined structure.','2025-03-14 17:30:12',1, '09 - Used for ninth line of free form text with no pre-defined structure.')
,(310,'139-UR','1','Primary - First','2025-03-14 17:30:12',1, '1 - Primary - First')
,(311,'139-UR','2','Secondary - Second','2025-03-14 17:30:12',1, '2 - Secondary - Second')
,(312,'139-UR','3','Tertiary - Third','2025-03-14 17:30:12',1, '3 - Tertiary - Third')
,(313,'139-UR','4','Quaternary - Fourth','2025-03-14 17:30:12',1, '4 - Quaternary - Fourth')
,(314,'139-UR','5','Quinary - Fifth','2025-03-14 17:30:12',1, '5 - Quinary - Fifth')
,(315,'139-UR','6','Senary - Sixth','2025-03-14 17:30:12',1, '6 - Senary - Sixth')
,(316,'139-UR','7','Septenary - Seventh','2025-03-14 17:30:12',1, '7 - Septenary - Seventh')
,(317,'139-UR','8','Octonary - Eighth','2025-03-14 17:30:12',1, '8 - Octonary - Eighth')
,(318,'139-UR','9','Nonary - Ninth','2025-03-14 17:30:12',1, '9 - Nonary - Ninth')
,(319,'143-UW','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(320,'143-UW','1','Cardholder','2025-03-14 17:30:12',1, '1 - Cardholder')
,(321,'143-UW','2','Spouse','2025-03-14 17:30:12',1, '2 - Spouse')
,(322,'143-UW','3','Child','2025-03-14 17:30:12',1, '3 - Child')
,(323,'143-UW','4','Other','2025-03-14 17:30:12',1, '4 - Other')
,(324,'147-U7','1','Community/Retail Pharmacy Services','2025-03-14 17:30:12',1, '1 - Community/Retail Pharmacy Services')
,(325,'147-U7','2','Compounding Pharmacy Services','2025-03-14 17:30:12',1, '2 - Compounding Pharmacy Services')
,(326,'147-U7','3','Home Infusion Therapy Provider Services','2025-03-14 17:30:12',1, '3 - Home Infusion Therapy Provider Services')
,(327,'147-U7','4','Institutional Pharmacy Services','2025-03-14 17:30:12',1, '4 - Institutional Pharmacy Services')
,(328,'147-U7','5','Long Term Care Pharmacy Services','2025-03-14 17:30:12',1, '5 - Long Term Care Pharmacy Services')
,(329,'147-U7','6','Mail Order Pharmacy Services','2025-03-14 17:30:12',1, '6 - Mail Order Pharmacy Services')
,(330,'147-U7','7','Managed Care Organization Pharmacy Services','2025-03-14 17:30:12',1, '7 - Managed Care Organization Pharmacy Services')
,(331,'147-U7','8','Specialty Care Pharmacy Services','2025-03-14 17:30:12',1, '8 - Specialty Care Pharmacy Services')
,(332,'147-U7','9','Not used.','2025-03-14 17:30:12',1, '9 - Not used.')
,(333,'147-U7','10','Not used.','2025-03-14 17:30:12',1, '10 - Not used.')
,(334,'147-U7','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(335,'202-B2','10','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '10 - Health Industry Number (HIN)')
,(336,'202-B2','11','Federal Tax ID','2025-03-14 17:30:12',1, '11 - Federal Tax ID')
,(337,'202-B2','12','Drug Enforcement Administration (DEA) Number','2025-03-14 17:30:12',1, '12 - Drug Enforcement Administration (DEA) Number')
,(338,'202-B2','13','State Issued','2025-03-14 17:30:12',1, '13 - State Issued')
,(339,'202-B2','14','Plan Specific','2025-03-14 17:30:12',1, '14 - Plan Specific')
,(340,'202-B2','15','HCIdea','2025-03-14 17:30:12',1, '15 - HCIdea')
,(341,'202-B2','16','Combat Methamphetamine Epidemic Act (CMEA) Certificate ID','2025-03-14 17:30:12',1, '16 - Combat Methamphetamine Epidemic Act (CMEA) Certificate ID')
,(342,'202-B2','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(343,'202-B2','01','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '01 - National Provider Identifier (NPI)')
,(344,'202-B2','02','Blue Cross','2025-03-14 17:30:12',1, '02 - Blue Cross')
,(345,'202-B2','03','Blue Shield','2025-03-14 17:30:12',1, '03 - Blue Shield')
,(346,'202-B2','04','Medicare','2025-03-14 17:30:12',1, '04 - Medicare')
,(347,'202-B2','05','Medicaid','2025-03-14 17:30:12',1, '05 - Medicaid')
,(348,'202-B2','06','UPIN (Unique Physician/Practitioner Identification Number)','2025-03-14 17:30:12',1, '06 - UPIN (Unique Physician/Practitioner Identification Number)')
,(349,'202-B2','07','NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)','2025-03-14 17:30:12',1, '07 - NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)')
,(350,'202-B2','08','State License','2025-03-14 17:30:12',1, '08 - State License')
,(351,'202-B2','09','TRICARE','2025-03-14 17:30:12',1, '09 - TRICARE')
,(352,'202-B2','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(353,'305-C5','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(354,'305-C5','1','Male','2025-03-14 17:30:12',1, '1 - Male')
,(355,'305-C5','2','Female','2025-03-14 17:30:12',1, '2 - Female')
,(356,'306-C6','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(357,'306-C6','1','Cardholder','2025-03-14 17:30:12',1, '1 - Cardholder')
,(358,'306-C6','2','Spouse','2025-03-14 17:30:12',1, '2 - Spouse')
,(359,'306-C6','3','Child','2025-03-14 17:30:12',1, '3 - Child')
,(360,'306-C6','4','Other','2025-03-14 17:30:12',1, '4 - Other')
,(361,'308-C8','0','Not Specified by patient','2025-03-14 17:30:12',1, '0 - Not Specified by patient')
,(362,'308-C8','1','No other coverage','2025-03-14 17:30:12',1, '1 - No other coverage')
,(363,'308-C8','2','Other coverage exists - payment collected','2025-03-14 17:30:12',1, '2 - Other coverage exists - payment collected')
,(364,'308-C8','3','Other Coverage Billed - claim not covered','2025-03-14 17:30:12',1, '3 - Other Coverage Billed - claim not covered')
,(365,'308-C8','4','Other coverage exists - payment not collected','2025-03-14 17:30:12',1, '4 - Other coverage exists - payment not collected')
,(366,'308-C8','5','Managed care plan denial','2025-03-14 17:30:12',1, '5 - Managed care plan denial')
,(367,'308-C8','6','Other coverage denied - not participating provider','2025-03-14 17:30:12',1, '6 - Other coverage denied - not participating provider')
,(368,'308-C8','7','Other coverage exists - not in effect on DOS','2025-03-14 17:30:12',1, '7 - Other coverage exists - not in effect on DOS')
,(369,'308-C8','8','Claim is billing for patient financial responsibility only','2025-03-14 17:30:12',1, '8 - Claim is billing for patient financial responsibility only')
,(370,'309-C9','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(371,'309-C9','1','No Override','2025-03-14 17:30:12',1, '1 - No Override')
,(372,'309-C9','2','Override','2025-03-14 17:30:12',1, '2 - Override')
,(373,'309-C9','3','Full Time Student','2025-03-14 17:30:12',1, '3 - Full Time Student')
,(374,'309-C9','4','Disabled Dependent','2025-03-14 17:30:12',1, '4 - Disabled Dependent')
,(375,'309-C9','5','Dependent Parent','2025-03-14 17:30:12',1, '5 - Dependent Parent')
,(376,'309-C9','6','Significant Other','2025-03-14 17:30:12',1, '6 - Significant Other')
,(377,'318-CI','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(378,'318-CI','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(379,'318-CI','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(380,'318-CI','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(381,'318-CI','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(382,'318-CI','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(383,'318-CI','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(384,'318-CI','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(385,'318-CI','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(386,'318-CI','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(387,'318-CI','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(388,'318-CI','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(389,'318-CI','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(390,'318-CI','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(391,'318-CI','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(392,'318-CI','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(393,'318-CI','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(394,'318-CI','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(395,'318-CI','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(396,'318-CI','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(397,'318-CI','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(398,'318-CI','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(399,'318-CI','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(400,'318-CI','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(401,'318-CI','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(402,'318-CI','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(403,'318-CI','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(404,'318-CI','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(405,'318-CI','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(406,'318-CI','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(407,'318-CI','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(408,'318-CI','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(409,'318-CI','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(410,'318-CI','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(411,'318-CI','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(412,'318-CI','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(413,'318-CI','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(414,'318-CI','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(415,'318-CI','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(416,'318-CI','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(417,'318-CI','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(418,'318-CI','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(419,'318-CI','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(420,'318-CI','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(421,'318-CI','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(422,'318-CI','56','California','2025-03-14 17:30:12',1, '56 - California')
,(423,'318-CI','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(424,'318-CI','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(425,'318-CI','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(426,'318-CI','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(427,'318-CI','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(428,'318-CI','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(429,'318-CI','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(430,'318-CI','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(431,'318-CI','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(432,'318-CI','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(433,'318-CI','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(434,'318-CI','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(435,'318-CI','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(436,'318-CI','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(437,'318-CI','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(438,'318-CI','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(439,'318-CI','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(440,'318-CI','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(441,'318-CI','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(442,'318-CI','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(443,'318-CI','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(444,'318-CI','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(445,'318-CI','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(446,'318-CI','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(447,'318-CI','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(448,'318-CI','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(449,'318-CI','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(450,'318-CI','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(451,'318-CI','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(452,'318-CI','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(453,'318-CI','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(454,'318-CI','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(455,'318-CI','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(456,'318-CI','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(457,'318-CI','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(458,'318-CI','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(459,'318-CI','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(460,'318-CI','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(461,'318-CI','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(462,'318-CI','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(463,'318-CI','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(464,'318-CI','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(465,'318-CI','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(466,'318-CI','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(467,'318-CI','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(468,'318-CI','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(469,'318-CI','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(470,'318-CI','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(471,'318-CI','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(472,'318-CI','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(473,'318-CI','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(474,'318-CI','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(475,'318-CI','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(476,'318-CI','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(477,'318-CI','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(478,'318-CI','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(479,'318-CI','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(480,'318-CI','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(481,'318-CI','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(482,'318-CI','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(483,'318-CI','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(484,'318-CI','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(485,'318-CI','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(486,'318-CI','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(487,'318-CI','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(488,'318-CI','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(489,'318-CI','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(490,'318-CI','05','California','2025-03-14 17:30:12',1, '05 - California')
,(491,'318-CI','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(492,'318-CI','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(493,'318-CI','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(494,'318-CI','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(495,'318-CI','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(496,'318-CI','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(497,'318-CI','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(498,'318-CI','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(499,'318-CI','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(500,'318-CI','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(501,'318-CI','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(502,'318-CI','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(503,'318-CI','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(504,'318-CI','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(505,'318-CI','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(506,'318-CI','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(507,'318-CI','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(508,'318-CI','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(509,'318-CI','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(510,'318-CI','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(511,'324-CO','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(512,'324-CO','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(513,'324-CO','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(514,'324-CO','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(515,'324-CO','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(516,'324-CO','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(517,'324-CO','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(518,'324-CO','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(519,'324-CO','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(520,'324-CO','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(521,'324-CO','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(522,'324-CO','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(523,'324-CO','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(524,'324-CO','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(525,'324-CO','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(526,'324-CO','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(527,'324-CO','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(528,'324-CO','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(529,'324-CO','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(530,'324-CO','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(531,'324-CO','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(532,'324-CO','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(533,'324-CO','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(534,'324-CO','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(535,'324-CO','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(536,'324-CO','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(537,'324-CO','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(538,'324-CO','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(539,'324-CO','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(540,'324-CO','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(541,'324-CO','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(542,'324-CO','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(543,'324-CO','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(544,'324-CO','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(545,'324-CO','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(546,'324-CO','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(547,'324-CO','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(548,'324-CO','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(549,'324-CO','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(550,'324-CO','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(551,'324-CO','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(552,'324-CO','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(553,'324-CO','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(554,'324-CO','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(555,'324-CO','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(556,'324-CO','56','California','2025-03-14 17:30:12',1, '56 - California')
,(557,'324-CO','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(558,'324-CO','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(559,'324-CO','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(560,'324-CO','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(561,'324-CO','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(562,'324-CO','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(563,'324-CO','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(564,'324-CO','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(565,'324-CO','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(566,'324-CO','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(567,'324-CO','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(568,'324-CO','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(569,'324-CO','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(570,'324-CO','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(571,'324-CO','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(572,'324-CO','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(573,'324-CO','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(574,'324-CO','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(575,'324-CO','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(576,'324-CO','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(577,'324-CO','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(578,'324-CO','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(579,'324-CO','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(580,'324-CO','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(581,'324-CO','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(582,'324-CO','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(583,'324-CO','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(584,'324-CO','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(585,'324-CO','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(586,'324-CO','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(587,'324-CO','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(588,'324-CO','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(589,'324-CO','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(590,'324-CO','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(591,'324-CO','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(592,'324-CO','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(593,'324-CO','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(594,'324-CO','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(595,'324-CO','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(596,'324-CO','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(597,'324-CO','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(598,'324-CO','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(599,'324-CO','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(600,'324-CO','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(601,'324-CO','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(602,'324-CO','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(603,'324-CO','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(604,'324-CO','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(605,'324-CO','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(606,'324-CO','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(607,'324-CO','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(608,'324-CO','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(609,'324-CO','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(610,'324-CO','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(611,'324-CO','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(612,'324-CO','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(613,'324-CO','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(614,'324-CO','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(615,'324-CO','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(616,'324-CO','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(617,'324-CO','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(618,'324-CO','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(619,'324-CO','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(620,'324-CO','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(621,'324-CO','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(622,'324-CO','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(623,'324-CO','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(624,'324-CO','05','California','2025-03-14 17:30:12',1, '05 - California')
,(625,'324-CO','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(626,'324-CO','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(627,'324-CO','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(628,'324-CO','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(629,'324-CO','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(630,'324-CO','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(631,'324-CO','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(632,'324-CO','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(633,'324-CO','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(634,'324-CO','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(635,'324-CO','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(636,'324-CO','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(637,'324-CO','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(638,'324-CO','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(639,'324-CO','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(640,'324-CO','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(641,'324-CO','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(642,'324-CO','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(643,'324-CO','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(644,'324-CO','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(645,'331-CX','10','Employer Assigned ID','2025-03-14 17:30:12',1, '10 - Employer Assigned ID')
,(646,'331-CX','11','Payer/PBM Assigned ID','2025-03-14 17:30:12',1, '11 - Payer/PBM Assigned ID')
,(647,'331-CX','12','Alien Number (Government Permanent Residence Number)','2025-03-14 17:30:12',1, '12 - Alien Number (Government Permanent Residence Number)')
,(648,'331-CX','13','Government Student VISA Number','2025-03-14 17:30:12',1, '13 - Government Student VISA Number')
,(649,'331-CX','14','Indian Tribal ID','2025-03-14 17:30:12',1, '14 - Indian Tribal ID')
,(650,'331-CX','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(651,'331-CX','01','Social Security Number','2025-03-14 17:30:12',1, '01 - Social Security Number')
,(652,'331-CX','1J','Facility ID Number','2025-03-14 17:30:12',1, '1J - Facility ID Number')
,(653,'331-CX','02','Driver''s License Number','2025-03-14 17:30:12',1, '02 - Driver''s License Number')
,(654,'331-CX','03','U.S. Military ID','2025-03-14 17:30:12',1, '03 - U.S. Military ID')
,(655,'331-CX','04','Non-SSN-based patient identifier assigned by health plan','2025-03-14 17:30:12',1, '04 - Non-SSN-based patient identifier assigned by health plan')
,(656,'331-CX','05','SSN-based patient identifier assigned by health plan','2025-03-14 17:30:12',1, '05 - SSN-based patient identifier assigned by health plan')
,(657,'331-CX','06','Medicaid ID','2025-03-14 17:30:12',1, '06 - Medicaid ID')
,(658,'331-CX','07','State Issued ID','2025-03-14 17:30:12',1, '07 - State Issued ID')
,(659,'331-CX','08','Passport ID','2025-03-14 17:30:12',1, '08 - Passport ID')
,(660,'331-CX','09','Medicare HIC#','2025-03-14 17:30:12',1, '09 - Medicare HIC#')
,(661,'331-CX','EA','Medical Record Identification Number (EHR)','2025-03-14 17:30:12',1, 'EA - Medical Record Identification Number (EHR)')
,(662,'334-1C','1','Non-Smoker','2025-03-14 17:30:12',1, '1 - Non-Smoker')
,(663,'334-1C','2','Smoker','2025-03-14 17:30:12',1, '2 - Smoker')
,(664,'335-2C','1','Not pregnant','2025-03-14 17:30:12',1, '1 - Not pregnant')
,(665,'335-2C','2','Pregnant','2025-03-14 17:30:12',1, '2 - Pregnant')
,(666,'338-5C','98','Coupon','2025-03-14 17:30:12',1, '98 - Coupon')
,(667,'338-5C','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(668,'338-5C','UK','Other Payer Order Unknown','2025-03-14 17:30:12',1, 'UK - Other Payer Order Unknown')
,(669,'338-5C','01','Primary - First','2025-03-14 17:30:12',1, '01 - Primary - First')
,(670,'338-5C','02','Secondary - Second','2025-03-14 17:30:12',1, '02 - Secondary - Second')
,(671,'338-5C','03','Tertiary - Third','2025-03-14 17:30:12',1, '03 - Tertiary - Third')
,(672,'338-5C','04','Quaternary - Fourth','2025-03-14 17:30:12',1, '04 - Quaternary - Fourth')
,(673,'338-5C','05','Quinary - Fifth','2025-03-14 17:30:12',1, '05 - Quinary - Fifth')
,(674,'338-5C','06','Senary - Sixth','2025-03-14 17:30:12',1, '06 - Senary - Sixth')
,(675,'338-5C','07','Septenary - Seventh','2025-03-14 17:30:12',1, '07 - Septenary - Seventh')
,(676,'338-5C','08','Octonary - Eighth','2025-03-14 17:30:12',1, '08 - Octonary - Eighth')
,(677,'338-5C','09','Nonary - Ninth','2025-03-14 17:30:12',1, '09 - Nonary - Ninth')
,(678,'338-5C','NA','Not Applicable','2025-03-14 17:30:12',1, 'NA - Not Applicable')
,(679,'339-6C','10','Payer Name','2025-03-14 17:30:12',1, '10 - Payer Name')
,(680,'339-6C','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(681,'339-6C','01','Standard Unique Health Plan Identifier','2025-03-14 17:30:12',1, '01 - Standard Unique Health Plan Identifier')
,(682,'339-6C','1C','Medicare Number','2025-03-14 17:30:12',1, '1C - Medicare Number')
,(683,'339-6C','1D','Medicaid Number','2025-03-14 17:30:12',1, '1D - Medicaid Number')
,(684,'339-6C','02','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '02 - Health Industry Number (HIN)')
,(685,'339-6C','03','Issuer Identification Number (IIN)','2025-03-14 17:30:12',1, '03 - Issuer Identification Number (IIN)')
,(686,'339-6C','04','National Association of Insurance Commissioners (NAIC)','2025-03-14 17:30:12',1, '04 - National Association of Insurance Commissioners (NAIC)')
,(687,'339-6C','05','Medicare Carrier Number','2025-03-14 17:30:12',1, '05 - Medicare Carrier Number')
,(688,'339-6C','09','Coupon','2025-03-14 17:30:12',1, '09 - Coupon')
,(689,'342-HC','10','Percentage Tax','2025-03-14 17:30:12',1, '10 - Percentage Tax')
,(690,'342-HC','11','Medication Administration','2025-03-14 17:30:12',1, '11 - Medication Administration')
,(691,'342-HC','12','Regulatory Fee','2025-03-14 17:30:12',1, '12 - Regulatory Fee')
,(692,'342-HC','01','Delivery Cost','2025-03-14 17:30:12',1, '01 - Delivery Cost')
,(693,'342-HC','02','Shipping Cost','2025-03-14 17:30:12',1, '02 - Shipping Cost')
,(694,'342-HC','03','Postage Cost','2025-03-14 17:30:12',1, '03 - Postage Cost')
,(695,'342-HC','04','Administrative Cost','2025-03-14 17:30:12',1, '04 - Administrative Cost')
,(696,'342-HC','05','Incentive','2025-03-14 17:30:12',1, '05 - Incentive')
,(697,'342-HC','06','Cognitive Service','2025-03-14 17:30:12',1, '06 - Cognitive Service')
,(698,'342-HC','07','Drug Benefit','2025-03-14 17:30:12',1, '07 - Drug Benefit')
,(699,'342-HC','09','Compound Preparation Cost Submitted','2025-03-14 17:30:12',1, '09 - Compound Preparation Cost Submitted')
,(700,'343-HD','P','Partial Fill','2025-03-14 17:30:12',1, 'P - Partial Fill')
,(701,'343-HD','C','Completion of Partial Fill','2025-03-14 17:30:12',1, 'C - Completion of Partial Fill')
,(702,'346-HH','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(703,'346-HH','01','Quantity Dispensed','2025-03-14 17:30:12',1, '01 - Quantity Dispensed')
,(704,'346-HH','02','Quantity Intended To Be Dispensed','2025-03-14 17:30:12',1, '02 - Quantity Intended To Be Dispensed')
,(705,'346-HH','03','Usual and Customary/Prorated','2025-03-14 17:30:12',1, '03 - Usual and Customary/Prorated')
,(706,'346-HH','04','Waived Due To Partial Fill','2025-03-14 17:30:12',1, '04 - Waived Due To Partial Fill')
,(707,'346-HH','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(708,'347-HJ','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(709,'347-HJ','01','Quantity Dispensed','2025-03-14 17:30:12',1, '01 - Quantity Dispensed')
,(710,'347-HJ','02','Quantity Intended To Be Dispensed','2025-03-14 17:30:12',1, '02 - Quantity Intended To Be Dispensed')
,(711,'347-HJ','03','Usual and Customary/Prorated','2025-03-14 17:30:12',1, '03 - Usual and Customary/Prorated')
,(712,'347-HJ','04','Waived Due To Partial Fill','2025-03-14 17:30:12',1, '04 - Waived Due To Partial Fill')
,(713,'347-HJ','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(714,'348-HK','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(715,'348-HK','01','Quantity Dispensed','2025-03-14 17:30:12',1, '01 - Quantity Dispensed')
,(716,'348-HK','02','Quantity Intended To Be Dispensed','2025-03-14 17:30:12',1, '02 - Quantity Intended To Be Dispensed')
,(717,'349-HM','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(718,'349-HM','01','Quantity Dispensed','2025-03-14 17:30:12',1, '01 - Quantity Dispensed')
,(719,'349-HM','02','Quantity Intended To Be Dispensed','2025-03-14 17:30:12',1, '02 - Quantity Intended To Be Dispensed')
,(720,'351-NP','10','Amount Attributed to Provider Network Selection as reported by previous payer.','2025-03-14 17:30:12',1, '10 - Amount Attributed to Provider Network Selection as reported by previous payer.')
,(721,'351-NP','11','Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection as reported by previous payer.','2025-03-14 17:30:12',1, '11 - Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection as reported by previous payer.')
,(722,'351-NP','12','Amount Attributed to Coverage Gap that was to be collected from the patient due to a coverage gap as reported by previous payer.','2025-03-14 17:30:12',1, '12 - Amount Attributed to Coverage Gap that was to be collected from the patient due to a coverage gap as reported by previous payer.')
,(723,'351-NP','13','Amount Attributed to Processor Fee as reported by previous payer.','2025-03-14 17:30:12',1, '13 - Amount Attributed to Processor Fee as reported by previous payer.')
,(724,'351-NP','14','Amount Attributed to Grace Period as reported by previous payer.','2025-03-14 17:30:12',1, '14 - Amount Attributed to Grace Period as reported by previous payer.')
,(725,'351-NP','15','Amount Attributed to Catastrophic Benefit as reported by previous payer.','2025-03-14 17:30:12',1, '15 - Amount Attributed to Catastrophic Benefit as reported by previous payer.')
,(726,'351-NP','16','Amount Attributed to Unbalanced Patient Pay Response Received from Previous Payer. The dollar amount representing the difference between the Patient Pay Amount (505-F5) and the sum of the reported Patient Pay Component Amounts (C93-KN).','2025-03-14 17:30:12',1, '16 - Amount Attributed to Unbalanced Patient Pay Response Received from Previous Payer. The dollar amount representing the difference between the Patient Pay Amount (505-F5) and the sum of the reported Patient Pay Component Amounts (C93-KN).')
,(727,'351-NP','17','Amount attributed to Regulatory Fee as reported by previous payer.','2025-03-14 17:30:12',1, '17 - Amount attributed to Regulatory Fee as reported by previous payer.')
,(728,'351-NP','01','Amount Applied to Periodic Deductible as reported by previous payer.','2025-03-14 17:30:12',1, '01 - Amount Applied to Periodic Deductible as reported by previous payer.')
,(729,'351-NP','02','Amount Attributed to Product Selection/Brand Drug as reported by previous payer.','2025-03-14 17:30:12',1, '02 - Amount Attributed to Product Selection/Brand Drug as reported by previous payer.')
,(730,'351-NP','03','Amount Attributed to Percentage Tax as reported by previous payer.','2025-03-14 17:30:12',1, '03 - Amount Attributed to Percentage Tax as reported by previous payer.')
,(731,'351-NP','04','Amount Exceeding Periodic Benefit Maximum as reported by previous payer.','2025-03-14 17:30:12',1, '04 - Amount Exceeding Periodic Benefit Maximum as reported by previous payer.')
,(732,'351-NP','05','Amount of Copay as reported by previous payer.','2025-03-14 17:30:12',1, '05 - Amount of Copay as reported by previous payer.')
,(733,'351-NP','06','Patient Pay Amount (505-F5) as reported by previous payer.','2025-03-14 17:30:12',1, '06 - Patient Pay Amount (505-F5) as reported by previous payer.')
,(734,'351-NP','07','Amount of Coinsurance as reported by previous payer.','2025-03-14 17:30:12',1, '07 - Amount of Coinsurance as reported by previous payer.')
,(735,'351-NP','08','Amount Attributed to Product Selection/Non-Preferred Formulary Selection as reported by previous payer.','2025-03-14 17:30:12',1, '08 - Amount Attributed to Product Selection/Non-Preferred Formulary Selection as reported by previous payer.')
,(736,'351-NP','09','Amount Attributed to Health Plan Assistance Amount as reported by previous payer.','2025-03-14 17:30:12',1, '09 - Amount Attributed to Health Plan Assistance Amount as reported by previous payer.')
,(737,'357-NV','1','Proof of eligibility unknown or unavailable','2025-03-14 17:30:12',1, '1 - Proof of eligibility unknown or unavailable')
,(738,'357-NV','2','Litigation','2025-03-14 17:30:12',1, '2 - Litigation')
,(739,'357-NV','3','Authorization delays','2025-03-14 17:30:12',1, '3 - Authorization delays')
,(740,'357-NV','4','Delay in certifying provider','2025-03-14 17:30:12',1, '4 - Delay in certifying provider')
,(741,'357-NV','5','Delay in supplying billing forms','2025-03-14 17:30:12',1, '5 - Delay in supplying billing forms')
,(742,'357-NV','6','Delay in delivery of custom-made appliances','2025-03-14 17:30:12',1, '6 - Delay in delivery of custom-made appliances')
,(743,'357-NV','7','Third party processing delay','2025-03-14 17:30:12',1, '7 - Third party processing delay')
,(744,'357-NV','8','Delay in eligibility determination','2025-03-14 17:30:12',1, '8 - Delay in eligibility determination')
,(745,'357-NV','9','Original claims rejected or denied due to a reason unrelated to the billing limitation rules','2025-03-14 17:30:12',1, '9 - Original claims rejected or denied due to a reason unrelated to the billing limitation rules')
,(746,'357-NV','10','Administration delay in the prior approval process','2025-03-14 17:30:12',1, '10 - Administration delay in the prior approval process')
,(747,'357-NV','11','Other','2025-03-14 17:30:12',1, '11 - Other')
,(748,'357-NV','12','Received late with no exceptions','2025-03-14 17:30:12',1, '12 - Received late with no exceptions')
,(749,'357-NV','13','Substantial damage by fire, etc. to provider records','2025-03-14 17:30:12',1, '13 - Substantial damage by fire, etc. to provider records')
,(750,'357-NV','14','Theft, sabotage/other willful acts by employee','2025-03-14 17:30:12',1, '14 - Theft, sabotage/other willful acts by employee')
,(751,'360-2B','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(752,'360-2B','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(753,'360-2B','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(754,'360-2B','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(755,'360-2B','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(756,'360-2B','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(757,'360-2B','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(758,'360-2B','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(759,'360-2B','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(760,'360-2B','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(761,'360-2B','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(762,'360-2B','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(763,'360-2B','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(764,'360-2B','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(765,'360-2B','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(766,'360-2B','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(767,'360-2B','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(768,'360-2B','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(769,'360-2B','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(770,'360-2B','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(771,'360-2B','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(772,'360-2B','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(773,'360-2B','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(774,'360-2B','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(775,'360-2B','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(776,'360-2B','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(777,'360-2B','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(778,'360-2B','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(779,'360-2B','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(780,'360-2B','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(781,'360-2B','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(782,'360-2B','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(783,'360-2B','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(784,'360-2B','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(785,'360-2B','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(786,'360-2B','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(787,'360-2B','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(788,'360-2B','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(789,'360-2B','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(790,'360-2B','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(791,'360-2B','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(792,'360-2B','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(793,'360-2B','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(794,'360-2B','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(795,'360-2B','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(796,'360-2B','56','California','2025-03-14 17:30:12',1, '56 - California')
,(797,'360-2B','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(798,'360-2B','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(799,'360-2B','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(800,'360-2B','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(801,'360-2B','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(802,'360-2B','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(803,'360-2B','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(804,'360-2B','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(805,'360-2B','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(806,'360-2B','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(807,'360-2B','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(808,'360-2B','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(809,'360-2B','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(810,'360-2B','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(811,'360-2B','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(812,'360-2B','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(813,'360-2B','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(814,'360-2B','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(815,'360-2B','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(816,'360-2B','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(817,'360-2B','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(818,'360-2B','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(819,'360-2B','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(820,'360-2B','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(821,'360-2B','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(822,'360-2B','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(823,'360-2B','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(824,'360-2B','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(825,'360-2B','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(826,'360-2B','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(827,'360-2B','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(828,'360-2B','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(829,'360-2B','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(830,'360-2B','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(831,'360-2B','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(832,'360-2B','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(833,'360-2B','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(834,'360-2B','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(835,'360-2B','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(836,'360-2B','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(837,'360-2B','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(838,'360-2B','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(839,'360-2B','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(840,'360-2B','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(841,'360-2B','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(842,'360-2B','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(843,'360-2B','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(844,'360-2B','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(845,'360-2B','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(846,'360-2B','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(847,'360-2B','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(848,'360-2B','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(849,'360-2B','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(850,'360-2B','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(851,'360-2B','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(852,'360-2B','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(853,'360-2B','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(854,'360-2B','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(855,'360-2B','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(856,'360-2B','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(857,'360-2B','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(858,'360-2B','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(859,'360-2B','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(860,'360-2B','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(861,'360-2B','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(862,'360-2B','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(863,'360-2B','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(864,'360-2B','05','California','2025-03-14 17:30:12',1, '05 - California')
,(865,'360-2B','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(866,'360-2B','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(867,'360-2B','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(868,'360-2B','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(869,'360-2B','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(870,'360-2B','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(871,'360-2B','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(872,'360-2B','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(873,'360-2B','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(874,'360-2B','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(875,'360-2B','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(876,'360-2B','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(877,'360-2B','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(878,'360-2B','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(879,'360-2B','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(880,'360-2B','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(881,'360-2B','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(882,'360-2B','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(883,'360-2B','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(884,'360-2B','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(885,'361-2D','Y','Assigned','2025-03-14 17:30:12',1, 'Y - Assigned')
,(886,'361-2D','N','Not Assigned','2025-03-14 17:30:12',1, 'N - Not Assigned')
,(887,'367-2N','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(888,'367-2N','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(889,'367-2N','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(890,'367-2N','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(891,'367-2N','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(892,'367-2N','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(893,'367-2N','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(894,'367-2N','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(895,'367-2N','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(896,'367-2N','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(897,'367-2N','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(898,'367-2N','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(899,'367-2N','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(900,'367-2N','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(901,'367-2N','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(902,'367-2N','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(903,'367-2N','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(904,'367-2N','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(905,'367-2N','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(906,'367-2N','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(907,'367-2N','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(908,'367-2N','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(909,'367-2N','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(910,'367-2N','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(911,'367-2N','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(912,'367-2N','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(913,'367-2N','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(914,'367-2N','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(915,'367-2N','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(916,'367-2N','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(917,'367-2N','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(918,'367-2N','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(919,'367-2N','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(920,'367-2N','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(921,'367-2N','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(922,'367-2N','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(923,'367-2N','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(924,'367-2N','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(925,'367-2N','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(926,'367-2N','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(927,'367-2N','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(928,'367-2N','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(929,'367-2N','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(930,'367-2N','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(931,'367-2N','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(932,'367-2N','56','California','2025-03-14 17:30:12',1, '56 - California')
,(933,'367-2N','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(934,'367-2N','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(935,'367-2N','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(936,'367-2N','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(937,'367-2N','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(938,'367-2N','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(939,'367-2N','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(940,'367-2N','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(941,'367-2N','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(942,'367-2N','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(943,'367-2N','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(944,'367-2N','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(945,'367-2N','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(946,'367-2N','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(947,'367-2N','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(948,'367-2N','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(949,'367-2N','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(950,'367-2N','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(951,'367-2N','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(952,'367-2N','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(953,'367-2N','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(954,'367-2N','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(955,'367-2N','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(956,'367-2N','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(957,'367-2N','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(958,'367-2N','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(959,'367-2N','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(960,'367-2N','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(961,'367-2N','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(962,'367-2N','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(963,'367-2N','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(964,'367-2N','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(965,'367-2N','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(966,'367-2N','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(967,'367-2N','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(968,'367-2N','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(969,'367-2N','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(970,'367-2N','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(971,'367-2N','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(972,'367-2N','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(973,'367-2N','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(974,'367-2N','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(975,'367-2N','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(976,'367-2N','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(977,'367-2N','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(978,'367-2N','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(979,'367-2N','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(980,'367-2N','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(981,'367-2N','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(982,'367-2N','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(983,'367-2N','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(984,'367-2N','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(985,'367-2N','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(986,'367-2N','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(987,'367-2N','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(988,'367-2N','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(989,'367-2N','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(990,'367-2N','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(991,'367-2N','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(992,'367-2N','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(993,'367-2N','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(994,'367-2N','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(995,'367-2N','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(996,'367-2N','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(997,'367-2N','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(998,'367-2N','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(999,'367-2N','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(1000,'367-2N','05','California','2025-03-14 17:30:12',1, '05 - California')
,(1001,'367-2N','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(1002,'367-2N','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(1003,'367-2N','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(1004,'367-2N','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(1005,'367-2N','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(1006,'367-2N','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(1007,'367-2N','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(1008,'367-2N','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(1009,'367-2N','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(1010,'367-2N','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(1011,'367-2N','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(1012,'367-2N','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(1013,'367-2N','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(1014,'367-2N','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(1015,'367-2N','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(1016,'367-2N','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(1017,'367-2N','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(1018,'367-2N','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(1019,'367-2N','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(1020,'367-2N','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(1021,'369-2Q','006','Medicare = 04.04B Lymphedema Pumps','2025-03-14 17:30:12',1, '006 - Medicare = 04.04B Lymphedema Pumps')
,(1022,'369-2Q','007','Medicare = 04.05C Osteogenesis Stimulator','2025-03-14 17:30:12',1, '007 - Medicare = 04.05C Osteogenesis Stimulator')
,(1023,'369-2Q','008','Medicare = 06.03B Transcutaneous Electrical NerveStimulator (TENS)','2025-03-14 17:30:12',1, '008 - Medicare = 06.03B Transcutaneous Electrical NerveStimulator (TENS)')
,(1024,'369-2Q','009','Medicare = 07.03A Seat Lift Mechanisms','2025-03-14 17:30:12',1, '009 - Medicare = 07.03A Seat Lift Mechanisms')
,(1025,'369-2Q','012','Medicare = 09.02 Infusion Pump','2025-03-14 17:30:12',1, '012 - Medicare = 09.02 Infusion Pump')
,(1026,'369-2Q','015','Medicare = 484.03 Oxygen','2025-03-14 17:30:12',1, '015 - Medicare = 484.03 Oxygen')
,(1027,'369-2Q','016','Medicare = 10.03 Enteral and Parenteral Nutrition','2025-03-14 17:30:12',1, '016 - Medicare = 10.03 Enteral and Parenteral Nutrition')
,(1028,'369-2Q','017','Medicare = 11.02 Section C Continuation Form','2025-03-14 17:30:12',1, '017 - Medicare = 11.02 Section C Continuation Form')
,(1029,'371-2S','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1030,'371-2S','1','Hours','2025-03-14 17:30:12',1, '1 - Hours')
,(1031,'371-2S','2','Days','2025-03-14 17:30:12',1, '2 - Days')
,(1032,'371-2S','3','Weeks','2025-03-14 17:30:12',1, '3 - Weeks')
,(1033,'371-2S','4','Months','2025-03-14 17:30:12',1, '4 - Months')
,(1034,'371-2S','5','Years','2025-03-14 17:30:12',1, '5 - Years')
,(1035,'371-2S','6','Lifetime','2025-03-14 17:30:12',1, '6 - Lifetime')
,(1036,'373-2U','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1037,'373-2U','1','Initial','2025-03-14 17:30:12',1, '1 - Initial')
,(1038,'373-2U','2','Revision','2025-03-14 17:30:12',1, '2 - Revision')
,(1039,'373-2U','3','Recertification','2025-03-14 17:30:12',1, '3 - Recertification')
,(1040,'384-4X','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1041,'384-4X','1','Home','2025-03-14 17:30:12',1, '1 - Home')
,(1042,'384-4X','2','Skilled Nursing Facility','2025-03-14 17:30:12',1, '2 - Skilled Nursing Facility')
,(1043,'384-4X','3','Nursing Facility','2025-03-14 17:30:12',1, '3 - Nursing Facility')
,(1044,'384-4X','4','Assisted Living Facility','2025-03-14 17:30:12',1, '4 - Assisted Living Facility')
,(1045,'384-4X','5','Custodial Care Facility','2025-03-14 17:30:12',1, '5 - Custodial Care Facility')
,(1046,'384-4X','6','Group Home','2025-03-14 17:30:12',1, '6 - Group Home')
,(1047,'384-4X','7','Inpatient Psychiatric Facility','2025-03-14 17:30:12',1, '7 - Inpatient Psychiatric Facility')
,(1048,'384-4X','8','Psychiatric Facility - Partial Hospitalization','2025-03-14 17:30:12',1, '8 - Psychiatric Facility - Partial Hospitalization')
,(1049,'384-4X','9','Intermediate Care Facility/Individuals with Intellectual Disabilities','2025-03-14 17:30:12',1, '9 - Intermediate Care Facility/Individuals with Intellectual Disabilities')
,(1050,'384-4X','10','Residential Substance Abuse Treatment Facility','2025-03-14 17:30:12',1, '10 - Residential Substance Abuse Treatment Facility')
,(1051,'384-4X','11','Hospice','2025-03-14 17:30:12',1, '11 - Hospice')
,(1052,'384-4X','12','Psychiatric Residential Treatment Facility','2025-03-14 17:30:12',1, '12 - Psychiatric Residential Treatment Facility')
,(1053,'384-4X','13','Comprehensive Inpatient Rehabilitation Facility','2025-03-14 17:30:12',1, '13 - Comprehensive Inpatient Rehabilitation Facility')
,(1054,'384-4X','14','Homeless Shelter','2025-03-14 17:30:12',1, '14 - Homeless Shelter')
,(1055,'384-4X','15','Correctional Institution','2025-03-14 17:30:12',1, '15 - Correctional Institution')
,(1056,'387-3V','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(1057,'387-3V','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(1058,'387-3V','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(1059,'387-3V','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(1060,'387-3V','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(1061,'387-3V','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(1062,'387-3V','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(1063,'387-3V','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(1064,'387-3V','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(1065,'387-3V','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(1066,'387-3V','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(1067,'387-3V','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(1068,'387-3V','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(1069,'387-3V','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(1070,'387-3V','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(1071,'387-3V','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(1072,'387-3V','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(1073,'387-3V','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(1074,'387-3V','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(1075,'387-3V','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(1076,'387-3V','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(1077,'387-3V','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(1078,'387-3V','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(1079,'387-3V','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(1080,'387-3V','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(1081,'387-3V','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(1082,'387-3V','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(1083,'387-3V','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(1084,'387-3V','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(1085,'387-3V','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(1086,'387-3V','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(1087,'387-3V','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(1088,'387-3V','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(1089,'387-3V','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(1090,'387-3V','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(1091,'387-3V','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(1092,'387-3V','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(1093,'387-3V','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(1094,'387-3V','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(1095,'387-3V','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(1096,'387-3V','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(1097,'387-3V','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(1098,'387-3V','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(1099,'387-3V','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(1100,'387-3V','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(1101,'387-3V','56','California','2025-03-14 17:30:12',1, '56 - California')
,(1102,'387-3V','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(1103,'387-3V','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(1104,'387-3V','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(1105,'387-3V','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(1106,'387-3V','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(1107,'387-3V','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(1108,'387-3V','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(1109,'387-3V','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(1110,'387-3V','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(1111,'387-3V','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(1112,'387-3V','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(1113,'387-3V','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(1114,'387-3V','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(1115,'387-3V','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(1116,'387-3V','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(1117,'387-3V','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(1118,'387-3V','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(1119,'387-3V','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(1120,'387-3V','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(1121,'387-3V','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(1122,'387-3V','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(1123,'387-3V','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(1124,'387-3V','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(1125,'387-3V','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(1126,'387-3V','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(1127,'387-3V','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(1128,'387-3V','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(1129,'387-3V','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(1130,'387-3V','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(1131,'387-3V','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(1132,'387-3V','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(1133,'387-3V','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(1134,'387-3V','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(1135,'387-3V','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(1136,'387-3V','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(1137,'387-3V','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(1138,'387-3V','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(1139,'387-3V','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(1140,'387-3V','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(1141,'387-3V','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(1142,'387-3V','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(1143,'387-3V','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(1144,'387-3V','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(1145,'387-3V','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(1146,'387-3V','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(1147,'387-3V','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(1148,'387-3V','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(1149,'387-3V','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(1150,'387-3V','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(1151,'387-3V','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(1152,'387-3V','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(1153,'387-3V','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(1154,'387-3V','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(1155,'387-3V','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(1156,'387-3V','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(1157,'387-3V','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(1158,'387-3V','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(1159,'387-3V','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(1160,'387-3V','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(1161,'387-3V','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(1162,'387-3V','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(1163,'387-3V','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(1164,'387-3V','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(1165,'387-3V','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(1166,'387-3V','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(1167,'387-3V','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(1168,'387-3V','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(1169,'387-3V','05','California','2025-03-14 17:30:12',1, '05 - California')
,(1170,'387-3V','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(1171,'387-3V','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(1172,'387-3V','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(1173,'387-3V','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(1174,'387-3V','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(1175,'387-3V','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(1176,'387-3V','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(1177,'387-3V','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(1178,'387-3V','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(1179,'387-3V','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(1180,'387-3V','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(1181,'387-3V','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(1182,'387-3V','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(1183,'387-3V','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(1184,'387-3V','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(1185,'387-3V','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(1186,'387-3V','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(1187,'387-3V','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(1188,'387-3V','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(1189,'387-3V','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(1190,'391-MT','Y','Patient assigns benefits','2025-03-14 17:30:12',1, 'Y - Patient assigns benefits')
,(1191,'391-MT','N','Patient does not assign benefits','2025-03-14 17:30:12',1, 'N - Patient does not assign benefits')
,(1192,'403-D3','0','Original prescription fill for the associated Prescription Service Reference Number.','2025-03-14 17:30:12',1, '0 - Original prescription fill for the associated Prescription Service Reference Number.')
,(1193,'403-D3','1-99','Prescription fill subsequent to the original fill for the associated Prescription Service reference Number.','2025-03-14 17:30:12',1, '1-99 - Prescription fill subsequent to the original fill for the associated Prescription Service reference Number.')
,(1194,'406-D6','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1195,'406-D6','1','Not a Compound','2025-03-14 17:30:12',1, '1 - Not a Compound')
,(1196,'406-D6','2','Compound','2025-03-14 17:30:12',1, '2 - Compound')
,(1197,'408-D8','0','No Product Selection Indicated','2025-03-14 17:30:12',1, '0 - No Product Selection Indicated')
,(1198,'408-D8','1','Substitution Not Allowed by Prescriber','2025-03-14 17:30:12',1, '1 - Substitution Not Allowed by Prescriber')
,(1199,'408-D8','2','Substitution Allowed - Patient Requested Product Dispensed','2025-03-14 17:30:12',1, '2 - Substitution Allowed - Patient Requested Product Dispensed')
,(1200,'408-D8','3','Substitution Allowed - Pharmacist Selected Product Dispensed','2025-03-14 17:30:12',1, '3 - Substitution Allowed - Pharmacist Selected Product Dispensed')
,(1201,'408-D8','4','Substitution Allowed - Generic Drug Not in Stock','2025-03-14 17:30:12',1, '4 - Substitution Allowed - Generic Drug Not in Stock')
,(1202,'408-D8','5','Substitution Allowed - Brand Drug Dispensed as a Generic','2025-03-14 17:30:12',1, '5 - Substitution Allowed - Brand Drug Dispensed as a Generic')
,(1203,'408-D8','6','Override','2025-03-14 17:30:12',1, '6 - Override')
,(1204,'408-D8','7','Substitution Not Allowed - Brand Drug Mandated by Law','2025-03-14 17:30:12',1, '7 - Substitution Not Allowed - Brand Drug Mandated by Law')
,(1205,'408-D8','8','Substitution Allowed - Generic Drug Not Available in Marketplace','2025-03-14 17:30:12',1, '8 - Substitution Allowed - Generic Drug Not Available in Marketplace')
,(1206,'408-D8','9','Substitution Allowed By Prescriber but Plan Requests Brand - Patient''s Plan Requested Brand Product To Be Dispensed','2025-03-14 17:30:12',1, '9 - Substitution Allowed By Prescriber but Plan Requests Brand - Patient''s Plan Requested Brand Product To Be Dispensed')
,(1207,'415-DF','0','No refills authorized','2025-03-14 17:30:12',1, '0 - No refills authorized')
,(1208,'415-DF','1-99','Authorized ReFill #  - with 99 being as needed, refills unlimited','2025-03-14 17:30:12',1, '1-99 - Authorized ReFill #  - with 99 being as needed, refills unlimited')
,(1209,'418-DI','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1210,'418-DI','1','Patient Consultation','2025-03-14 17:30:12',1, '1 - Patient Consultation')
,(1211,'418-DI','2','Home Delivery','2025-03-14 17:30:12',1, '2 - Home Delivery')
,(1212,'418-DI','3','Emergency','2025-03-14 17:30:12',1, '3 - Emergency')
,(1213,'418-DI','4','24 Hour Service','2025-03-14 17:30:12',1, '4 - 24 Hour Service')
,(1214,'418-DI','5','Patient Consultation regarding generic product selection','2025-03-14 17:30:12',1, '5 - Patient Consultation regarding generic product selection')
,(1215,'418-DI','6','In-Home Service','2025-03-14 17:30:12',1, '6 - In-Home Service')
,(1216,'418-DI','7','Medical at home with special pharmacy services identical to Long Term Care beneficiaries with the exception of emergency kits.','2025-03-14 17:30:12',1, '7 - Medical at home with special pharmacy services identical to Long Term Care beneficiaries with the exception of emergency kits.')
,(1217,'419-DJ','0','Not Known','2025-03-14 17:30:12',1, '0 - Not Known')
,(1218,'419-DJ','1','Written','2025-03-14 17:30:12',1, '1 - Written')
,(1219,'419-DJ','2','Telephone','2025-03-14 17:30:12',1, '2 - Telephone')
,(1220,'419-DJ','3','Electronic','2025-03-14 17:30:12',1, '3 - Electronic')
,(1221,'419-DJ','4','Facsimile','2025-03-14 17:30:12',1, '4 - Facsimile')
,(1222,'419-DJ','5','Pharmacy','2025-03-14 17:30:12',1, '5 - Pharmacy')
,(1223,'420-DK','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1224,'420-DK','1','No Override','2025-03-14 17:30:12',1, '1 - No Override')
,(1225,'420-DK','2','Other Override','2025-03-14 17:30:12',1, '2 - Other Override')
,(1226,'420-DK','3','Vacation Supply','2025-03-14 17:30:12',1, '3 - Vacation Supply')
,(1227,'420-DK','4','Lost/Damaged Prescription','2025-03-14 17:30:12',1, '4 - Lost/Damaged Prescription')
,(1228,'420-DK','5','Therapy Change','2025-03-14 17:30:12',1, '5 - Therapy Change')
,(1229,'420-DK','6','Starter Dose','2025-03-14 17:30:12',1, '6 - Starter Dose')
,(1230,'420-DK','7','Medically Necessary','2025-03-14 17:30:12',1, '7 - Medically Necessary')
,(1231,'420-DK','8','Process Compound For Approved Ingredients','2025-03-14 17:30:12',1, '8 - Process Compound For Approved Ingredients')
,(1232,'420-DK','9','Encounters','2025-03-14 17:30:12',1, '9 - Encounters')
,(1233,'420-DK','10','Meets Plan Limitations','2025-03-14 17:30:12',1, '10 - Meets Plan Limitations')
,(1234,'420-DK','11','Certification on File','2025-03-14 17:30:12',1, '11 - Certification on File')
,(1235,'420-DK','12','DME Replacement Indicator','2025-03-14 17:30:12',1, '12 - DME Replacement Indicator')
,(1236,'420-DK','13','Payer-Recognized Emergency/Disaster Assistance Request','2025-03-14 17:30:12',1, '13 - Payer-Recognized Emergency/Disaster Assistance Request')
,(1237,'420-DK','14','Long Term Care Leave of Absence','2025-03-14 17:30:12',1, '14 - Long Term Care Leave of Absence')
,(1238,'420-DK','15','Long Term Care Replacement Medication','2025-03-14 17:30:12',1, '15 - Long Term Care Replacement Medication')
,(1239,'420-DK','16','Long Term Care Emergency box (kit) or automated dispensing machine','2025-03-14 17:30:12',1, '16 - Long Term Care Emergency box (kit) or automated dispensing machine')
,(1240,'420-DK','17','Long Term Care Emergency supply remainder','2025-03-14 17:30:12',1, '17 - Long Term Care Emergency supply remainder')
,(1241,'420-DK','18','Long Term Care Patient Admit/Readmit Indicator','2025-03-14 17:30:12',1, '18 - Long Term Care Patient Admit/Readmit Indicator')
,(1242,'420-DK','19','Split Billing','2025-03-14 17:30:12',1, '19 - Split Billing')
,(1243,'420-DK','20','340B','2025-03-14 17:30:12',1, '20 - 340B')
,(1244,'420-DK','21','LTC dispensing: 14 days or less not applicable','2025-03-14 17:30:12',1, '21 - LTC dispensing: 14 days or less not applicable')
,(1245,'420-DK','22','LTC dispensing: 7 days','2025-03-14 17:30:12',1, '22 - LTC dispensing: 7 days')
,(1246,'420-DK','23','LTC dispensing: 4 days','2025-03-14 17:30:12',1, '23 - LTC dispensing: 4 days')
,(1247,'420-DK','24','LTC dispensing: 3 days','2025-03-14 17:30:12',1, '24 - LTC dispensing: 3 days')
,(1248,'420-DK','25','LTC dispensing: 2 days','2025-03-14 17:30:12',1, '25 - LTC dispensing: 2 days')
,(1249,'420-DK','26','LTC dispensing: 1 day','2025-03-14 17:30:12',1, '26 - LTC dispensing: 1 day')
,(1250,'420-DK','27','LTC dispensing: 4-3 days','2025-03-14 17:30:12',1, '27 - LTC dispensing: 4-3 days')
,(1251,'420-DK','28','LTC dispensing: 2-2-3 days','2025-03-14 17:30:12',1, '28 - LTC dispensing: 2-2-3 days')
,(1252,'420-DK','29','LTC dispensing: daily and 3-day weekend','2025-03-14 17:30:12',1, '29 - LTC dispensing: daily and 3-day weekend')
,(1253,'420-DK','30','LTC dispensing: Per shift dispensing','2025-03-14 17:30:12',1, '30 - LTC dispensing: Per shift dispensing')
,(1254,'420-DK','31','LTC dispensing: Per med pass dispensing','2025-03-14 17:30:12',1, '31 - LTC dispensing: Per med pass dispensing')
,(1255,'420-DK','32','LTC dispensing: PRN on demand','2025-03-14 17:30:12',1, '32 - LTC dispensing: PRN on demand')
,(1256,'420-DK','33','LTC dispensing: 7 day or less cycle not otherwise represented','2025-03-14 17:30:12',1, '33 - LTC dispensing: 7 day or less cycle not otherwise represented')
,(1257,'420-DK','34','LTC dispensing: 14 days dispensing','2025-03-14 17:30:12',1, '34 - LTC dispensing: 14 days dispensing')
,(1258,'420-DK','35','LTC dispensing: 8-14 day dispensing method not listed above','2025-03-14 17:30:12',1, '35 - LTC dispensing: 8-14 day dispensing method not listed above')
,(1259,'420-DK','36','LTC dispensing: dispensed outside short cycle','2025-03-14 17:30:12',1, '36 - LTC dispensing: dispensed outside short cycle')
,(1260,'420-DK','42','Prescriber ID Submitted is valid and prescribing requirements have been validated.','2025-03-14 17:30:12',1, '42 - Prescriber ID Submitted is valid and prescribing requirements have been validated.')
,(1261,'420-DK','43','Prescriber''s DEA is active with DEA Authorized Prescriptive Right.','2025-03-14 17:30:12',1, '43 - Prescriber''s DEA is active with DEA Authorized Prescriptive Right.')
,(1262,'420-DK','44','For prescriber ID submitted, associated prescriber DEA recently licensed or re-activated','2025-03-14 17:30:12',1, '44 - For prescriber ID submitted, associated prescriber DEA recently licensed or re-activated')
,(1263,'420-DK','45','Prescriber''s DEA is a valid Hospital DEA with Suffix and has prescriptive authority for this drug DEA Schedule','2025-03-14 17:30:12',1, '45 - Prescriber''s DEA is a valid Hospital DEA with Suffix and has prescriptive authority for this drug DEA Schedule')
,(1264,'420-DK','46','Prescriber''s DEA has prescriptive authority for this drug DEA Schedule','2025-03-14 17:30:12',1, '46 - Prescriber''s DEA has prescriptive authority for this drug DEA Schedule')
,(1265,'420-DK','47','Shortened Days Supply Dispensed','2025-03-14 17:30:12',1, '47 - Shortened Days Supply Dispensed')
,(1266,'420-DK','48','Dispensed Subsequent to a Shortened Days Supply Dispensing','2025-03-14 17:30:12',1, '48 - Dispensed Subsequent to a Shortened Days Supply Dispensing')
,(1267,'420-DK','49','Prescriber does not currently have an active Type 1 NPI','2025-03-14 17:30:12',1, '49 - Prescriber does not currently have an active Type 1 NPI')
,(1268,'420-DK','50','Prescriber''s active Medicare Fee For Service enrollment status has been validated','2025-03-14 17:30:12',1, '50 - Prescriber''s active Medicare Fee For Service enrollment status has been validated')
,(1269,'420-DK','51','Pharmacy''s active Medicare Fee For Service enrollment status has been validated','2025-03-14 17:30:12',1, '51 - Pharmacy''s active Medicare Fee For Service enrollment status has been validated')
,(1270,'420-DK','52','Prescriber''s state license with prescriptive authority has been validated','2025-03-14 17:30:12',1, '52 - Prescriber''s state license with prescriptive authority has been validated')
,(1271,'420-DK','53','Prescriber NPI active and valid','2025-03-14 17:30:12',1, '53 - Prescriber NPI active and valid')
,(1272,'420-DK','54','CMS Other Authorized Prescriber (OAP)','2025-03-14 17:30:12',1, '54 - CMS Other Authorized Prescriber (OAP)')
,(1273,'420-DK','55','Prescriber Enrollment in State Medicaid Program has been validated.','2025-03-14 17:30:12',1, '55 - Prescriber Enrollment in State Medicaid Program has been validated.')
,(1274,'420-DK','56','Pharmacy Enrollment in State Medicaid Program has been validated.','2025-03-14 17:30:12',1, '56 - Pharmacy Enrollment in State Medicaid Program has been validated.')
,(1275,'420-DK','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1276,'423-DN','10','ASP (Average Sales Price)','2025-03-14 17:30:12',1, '10 - ASP (Average Sales Price)')
,(1277,'423-DN','11','AMP (Average Manufacturer Price)','2025-03-14 17:30:12',1, '11 - AMP (Average Manufacturer Price)')
,(1278,'423-DN','12','WAC (Wholesale Acquisition Cost)','2025-03-14 17:30:12',1, '12 - WAC (Wholesale Acquisition Cost)')
,(1279,'423-DN','13','Special Patient Pricing','2025-03-14 17:30:12',1, '13 - Special Patient Pricing')
,(1280,'423-DN','14','Cost basis on unreportable quantities.','2025-03-14 17:30:12',1, '14 - Cost basis on unreportable quantities.')
,(1281,'423-DN','15','Free product or no associated cost.','2025-03-14 17:30:12',1, '15 - Free product or no associated cost.')
,(1282,'423-DN','00','Default','2025-03-14 17:30:12',1, '00 - Default')
,(1283,'423-DN','01','AWP (Average Wholesale Price)','2025-03-14 17:30:12',1, '01 - AWP (Average Wholesale Price)')
,(1284,'423-DN','02','Local Wholesaler','2025-03-14 17:30:12',1, '02 - Local Wholesaler')
,(1285,'423-DN','03','Direct','2025-03-14 17:30:12',1, '03 - Direct')
,(1286,'423-DN','04','EAC (Estimated Acquisition Cost)','2025-03-14 17:30:12',1, '04 - EAC (Estimated Acquisition Cost)')
,(1287,'423-DN','05','Acquisition','2025-03-14 17:30:12',1, '05 - Acquisition')
,(1288,'423-DN','06','MAC (Maximum Allowable Cost)','2025-03-14 17:30:12',1, '06 - MAC (Maximum Allowable Cost)')
,(1289,'423-DN','07','Usual & Customary','2025-03-14 17:30:12',1, '07 - Usual & Customary')
,(1290,'423-DN','08','340B/Disproportionate Share Pricing/Public Health Service','2025-03-14 17:30:12',1, '08 - 340B/Disproportionate Share Pricing/Public Health Service')
,(1291,'423-DN','09','Other','2025-03-14 17:30:12',1, '09 - Other')
,(1292,'429-DT','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1293,'429-DT','1','Not Unit Dose','2025-03-14 17:30:12',1, '1 - Not Unit Dose')
,(1294,'429-DT','2','Manufacturer Unit Dose','2025-03-14 17:30:12',1, '2 - Manufacturer Unit Dose')
,(1295,'429-DT','3','Pharmacy Unit Dose','2025-03-14 17:30:12',1, '3 - Pharmacy Unit Dose')
,(1296,'429-DT','4','Pharmacy Unit Dose Patient Compliance Packaging','2025-03-14 17:30:12',1, '4 - Pharmacy Unit Dose Patient Compliance Packaging')
,(1297,'429-DT','5','Pharmacy Multi-drug Patient Compliance Packaging','2025-03-14 17:30:12',1, '5 - Pharmacy Multi-drug Patient Compliance Packaging')
,(1298,'429-DT','6','Remote Device Unit Dose','2025-03-14 17:30:12',1, '6 - Remote Device Unit Dose')
,(1299,'429-DT','7','Remote Device Multi-drug Compliance','2025-03-14 17:30:12',1, '7 - Remote Device Multi-drug Compliance')
,(1300,'429-DT','8','Manufacturer Unit of Use Package (not unit dose)','2025-03-14 17:30:12',1, '8 - Manufacturer Unit of Use Package (not unit dose)')
,(1301,'436-E1','10','Pharmacy Practice Activity Classification (PPAC)','2025-03-14 17:30:12',1, '10 - Pharmacy Practice Activity Classification (PPAC)')
,(1302,'436-E1','11','National Pharmaceutical Product Interface Code (NAPPI)','2025-03-14 17:30:12',1, '11 - National Pharmaceutical Product Interface Code (NAPPI)')
,(1303,'436-E1','12','Global Trade Identification Number (GTIN)','2025-03-14 17:30:12',1, '12 - Global Trade Identification Number (GTIN)')
,(1304,'436-E1','13','Drug Identification Number (DIN)','2025-03-14 17:30:12',1, '13 - Drug Identification Number (DIN)')
,(1305,'436-E1','15','First DataBank Formulation ID (GCN)','2025-03-14 17:30:12',1, '15 - First DataBank Formulation ID (GCN)')
,(1306,'436-E1','28','First DataBank Medication Name ID (FDB Med Name ID)','2025-03-14 17:30:12',1, '28 - First DataBank Medication Name ID (FDB Med Name ID)')
,(1307,'436-E1','29','First DataBank Routed Medication ID (FDB Routed Med ID)','2025-03-14 17:30:12',1, '29 - First DataBank Routed Medication ID (FDB Routed Med ID)')
,(1308,'436-E1','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','2025-03-14 17:30:12',1, '30 - First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)')
,(1309,'436-E1','31','First DataBank Medication ID (FDB MedID)','2025-03-14 17:30:12',1, '31 - First DataBank Medication ID (FDB MedID)')
,(1310,'436-E1','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','2025-03-14 17:30:12',1, '32 - First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)')
,(1311,'436-E1','33','First DataBank Ingredient List ID (HICL_SEQNO)','2025-03-14 17:30:12',1, '33 - First DataBank Ingredient List ID (HICL_SEQNO)')
,(1312,'436-E1','34','Universal Product Number (UPIN)','2025-03-14 17:30:12',1, '34 - Universal Product Number (UPIN)')
,(1313,'436-E1','36','Representative National Drug Code (NDC)','2025-03-14 17:30:12',1, '36 - Representative National Drug Code (NDC)')
,(1314,'436-E1','42','Gold Standard Marketed Product Identifier (MPid)','2025-03-14 17:30:12',1, '42 - Gold Standard Marketed Product Identifier (MPid)')
,(1315,'436-E1','43','Gold Standard Product Identifier (ProdID)','2025-03-14 17:30:12',1, '43 - Gold Standard Product Identifier (ProdID)')
,(1316,'436-E1','44','Gold Standard Specific Product Identifier (SPID)','2025-03-14 17:30:12',1, '44 - Gold Standard Specific Product Identifier (SPID)')
,(1317,'436-E1','45','Device Identifier (DI)','2025-03-14 17:30:12',1, '45 - Device Identifier (DI)')
,(1318,'436-E1','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1319,'436-E1','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(1320,'436-E1','01','Universal Product Code (UPC)','2025-03-14 17:30:12',1, '01 - Universal Product Code (UPC)')
,(1321,'436-E1','02','Health Related Item (HRI)','2025-03-14 17:30:12',1, '02 - Health Related Item (HRI)')
,(1322,'436-E1','03','National Drug Code (NDC)','2025-03-14 17:30:12',1, '03 - National Drug Code (NDC)')
,(1323,'436-E1','04','Health Industry Business Communications Council (HIBCC)','2025-03-14 17:30:12',1, '04 - Health Industry Business Communications Council (HIBCC)')
,(1324,'436-E1','06','Drug Use Review/ Professional Pharmacy Service (DUR/PPS)','2025-03-14 17:30:12',1, '06 - Drug Use Review/ Professional Pharmacy Service (DUR/PPS)')
,(1325,'436-E1','07','Current Procedural Terminology (CPT4)','2025-03-14 17:30:12',1, '07 - Current Procedural Terminology (CPT4)')
,(1326,'436-E1','08','Current Procedural Terminology (CPT5)','2025-03-14 17:30:12',1, '08 - Current Procedural Terminology (CPT5)')
,(1327,'436-E1','09','Healthcare Common Procedure Coding System (HCPCS)','2025-03-14 17:30:12',1, '09 - Healthcare Common Procedure Coding System (HCPCS)')
,(1328,'436-E1','05','Department of Defense (DOD)','2025-03-14 17:30:12',1, '05 - Department of Defense (DOD)')
,(1329,'439-E4','AD','Additional Drug Needed','2025-03-14 17:30:12',1, 'AD - Additional Drug Needed')
,(1330,'439-E4','AN','Prescription Authentication','2025-03-14 17:30:12',1, 'AN - Prescription Authentication')
,(1331,'439-E4','AR','Adverse Drug Reaction','2025-03-14 17:30:12',1, 'AR - Adverse Drug Reaction')
,(1332,'439-E4','AT','Additive Toxicity','2025-03-14 17:30:12',1, 'AT - Additive Toxicity')
,(1333,'439-E4','CD','Chronic Disease Management','2025-03-14 17:30:12',1, 'CD - Chronic Disease Management')
,(1334,'439-E4','CH','Call Help Desk','2025-03-14 17:30:12',1, 'CH - Call Help Desk')
,(1335,'439-E4','CS','Patient Complaint/Symptom','2025-03-14 17:30:12',1, 'CS - Patient Complaint/Symptom')
,(1336,'439-E4','DA','Drug-Allergy','2025-03-14 17:30:12',1, 'DA - Drug-Allergy')
,(1337,'439-E4','DC','Drug-Disease (Inferred)','2025-03-14 17:30:12',1, 'DC - Drug-Disease (Inferred)')
,(1338,'439-E4','DD','Drug-Drug Interaction','2025-03-14 17:30:12',1, 'DD - Drug-Drug Interaction')
,(1339,'439-E4','DF','Drug-Food Interaction','2025-03-14 17:30:12',1, 'DF - Drug-Food Interaction')
,(1340,'439-E4','DI','Drug Incompatibility','2025-03-14 17:30:12',1, 'DI - Drug Incompatibility')
,(1341,'439-E4','DL','Drug-Lab Conflict','2025-03-14 17:30:12',1, 'DL - Drug-Lab Conflict')
,(1342,'439-E4','DM','Apparent Drug Misuse','2025-03-14 17:30:12',1, 'DM - Apparent Drug Misuse')
,(1343,'439-E4','DR','Dose Range Conflict','2025-03-14 17:30:12',1, 'DR - Dose Range Conflict')
,(1344,'439-E4','DS','Tobacco Use','2025-03-14 17:30:12',1, 'DS - Tobacco Use')
,(1345,'439-E4','ED','Patient Education/Instruction','2025-03-14 17:30:12',1, 'ED - Patient Education/Instruction')
,(1346,'439-E4','ER','Overuse','2025-03-14 17:30:12',1, 'ER - Overuse')
,(1347,'439-E4','EX','Excessive Quantity','2025-03-14 17:30:12',1, 'EX - Excessive Quantity')
,(1348,'439-E4','HD','High Dose','2025-03-14 17:30:12',1, 'HD - High Dose')
,(1349,'439-E4','IC','Iatrogenic Condition','2025-03-14 17:30:12',1, 'IC - Iatrogenic Condition')
,(1350,'439-E4','ID','Ingredient Duplication','2025-03-14 17:30:12',1, 'ID - Ingredient Duplication')
,(1351,'439-E4','LD','Low Dose','2025-03-14 17:30:12',1, 'LD - Low Dose')
,(1352,'439-E4','LK','Lock In Recipient','2025-03-14 17:30:12',1, 'LK - Lock In Recipient')
,(1353,'439-E4','LR','Underuse','2025-03-14 17:30:12',1, 'LR - Underuse')
,(1354,'439-E4','MC','Drug-Disease (Reported)','2025-03-14 17:30:12',1, 'MC - Drug-Disease (Reported)')
,(1355,'439-E4','MN','Insufficient Duration','2025-03-14 17:30:12',1, 'MN - Insufficient Duration')
,(1356,'439-E4','MS','Missing Information/Clarification','2025-03-14 17:30:12',1, 'MS - Missing Information/Clarification')
,(1357,'439-E4','MX','Excessive Duration','2025-03-14 17:30:12',1, 'MX - Excessive Duration')
,(1358,'439-E4','NA','Drug Not Available','2025-03-14 17:30:12',1, 'NA - Drug Not Available')
,(1359,'439-E4','NC','Non-covered Drug Purchase','2025-03-14 17:30:12',1, 'NC - Non-covered Drug Purchase')
,(1360,'439-E4','ND','New Disease/Diagnosis','2025-03-14 17:30:12',1, 'ND - New Disease/Diagnosis')
,(1361,'439-E4','NF','Non-Formulary Drug','2025-03-14 17:30:12',1, 'NF - Non-Formulary Drug')
,(1362,'439-E4','NN','Unnecessary Drug','2025-03-14 17:30:12',1, 'NN - Unnecessary Drug')
,(1363,'439-E4','NP','New Patient Processing','2025-03-14 17:30:12',1, 'NP - New Patient Processing')
,(1364,'439-E4','NR','Lactation/Nursing Interaction','2025-03-14 17:30:12',1, 'NR - Lactation/Nursing Interaction')
,(1365,'439-E4','NS','Insufficient Quantity','2025-03-14 17:30:12',1, 'NS - Insufficient Quantity')
,(1366,'439-E4','OH','Alcohol Conflict','2025-03-14 17:30:12',1, 'OH - Alcohol Conflict')
,(1367,'439-E4','PA','Drug-Age','2025-03-14 17:30:12',1, 'PA - Drug-Age')
,(1368,'439-E4','PC','Patient Question/Concern','2025-03-14 17:30:12',1, 'PC - Patient Question/Concern')
,(1369,'439-E4','PG','Drug-Pregnancy','2025-03-14 17:30:12',1, 'PG - Drug-Pregnancy')
,(1370,'439-E4','PH','Preventive Health Care','2025-03-14 17:30:12',1, 'PH - Preventive Health Care')
,(1371,'439-E4','PN','Prescriber Consultation','2025-03-14 17:30:12',1, 'PN - Prescriber Consultation')
,(1372,'439-E4','PP','Plan Protocol','2025-03-14 17:30:12',1, 'PP - Plan Protocol')
,(1373,'439-E4','PR','Prior Adverse Reaction','2025-03-14 17:30:12',1, 'PR - Prior Adverse Reaction')
,(1374,'439-E4','PS','Product Selection Opportunity','2025-03-14 17:30:12',1, 'PS - Product Selection Opportunity')
,(1375,'439-E4','RE','Suspected Environmental Risk','2025-03-14 17:30:12',1, 'RE - Suspected Environmental Risk')
,(1376,'439-E4','RF','Health Provider Referral','2025-03-14 17:30:12',1, 'RF - Health Provider Referral')
,(1377,'439-E4','SC','Suboptimal Compliance','2025-03-14 17:30:12',1, 'SC - Suboptimal Compliance')
,(1378,'439-E4','SD','Suboptimal Drug/Indication','2025-03-14 17:30:12',1, 'SD - Suboptimal Drug/Indication')
,(1379,'439-E4','SE','Side Effect','2025-03-14 17:30:12',1, 'SE - Side Effect')
,(1380,'439-E4','SF','Suboptimal Dosage Form','2025-03-14 17:30:12',1, 'SF - Suboptimal Dosage Form')
,(1381,'439-E4','SR','Suboptimal Regimen','2025-03-14 17:30:12',1, 'SR - Suboptimal Regimen')
,(1382,'439-E4','SX','Drug-Gender','2025-03-14 17:30:12',1, 'SX - Drug-Gender')
,(1383,'439-E4','TD','Therapeutic','2025-03-14 17:30:12',1, 'TD - Therapeutic')
,(1384,'439-E4','TN','Laboratory Test Needed','2025-03-14 17:30:12',1, 'TN - Laboratory Test Needed')
,(1385,'439-E4','TP','Payer/Processor Question','2025-03-14 17:30:12',1, 'TP - Payer/Processor Question')
,(1386,'439-E4','UD','Duplicate Drug','2025-03-14 17:30:12',1, 'UD - Duplicate Drug')
,(1387,'440-E5','00','No intervention','2025-03-14 17:30:12',1, '00 - No intervention')
,(1388,'440-E5','AS','Patient assessment','2025-03-14 17:30:12',1, 'AS - Patient assessment')
,(1389,'440-E5','CC','Coordination of care','2025-03-14 17:30:12',1, 'CC - Coordination of care')
,(1390,'440-E5','DE','Dosing evaluation/determination','2025-03-14 17:30:12',1, 'DE - Dosing evaluation/determination')
,(1391,'440-E5','DP','Dosage evaluated','2025-03-14 17:30:12',1, 'DP - Dosage evaluated')
,(1392,'440-E5','FE','Formulary enforcement','2025-03-14 17:30:12',1, 'FE - Formulary enforcement')
,(1393,'440-E5','GP','Generic product selection','2025-03-14 17:30:12',1, 'GP - Generic product selection')
,(1394,'440-E5','M0','Prescriber consulted','2025-03-14 17:30:12',1, 'M0 - Prescriber consulted')
,(1395,'440-E5','MA','Medication administration','2025-03-14 17:30:12',1, 'MA - Medication administration')
,(1396,'440-E5','MB','Overriding benefit','2025-03-14 17:30:12',1, 'MB - Overriding benefit')
,(1397,'440-E5','MP','Patient will be monitored','2025-03-14 17:30:12',1, 'MP - Patient will be monitored')
,(1398,'440-E5','MR','Medication review','2025-03-14 17:30:12',1, 'MR - Medication review')
,(1399,'440-E5','PA','Previous patient tolerance','2025-03-14 17:30:12',1, 'PA - Previous patient tolerance')
,(1400,'440-E5','PE','Patient education/instruction','2025-03-14 17:30:12',1, 'PE - Patient education/instruction')
,(1401,'440-E5','PH','Patient medication history','2025-03-14 17:30:12',1, 'PH - Patient medication history')
,(1402,'440-E5','PM','Patient monitoring','2025-03-14 17:30:12',1, 'PM - Patient monitoring')
,(1403,'440-E5','P0','Patient consulted','2025-03-14 17:30:12',1, 'P0 - Patient consulted')
,(1404,'440-E5','PT','Perform laboratory test','2025-03-14 17:30:12',1, 'PT - Perform laboratory test')
,(1405,'440-E5','R0','Pharmacist consulted other source','2025-03-14 17:30:12',1, 'R0 - Pharmacist consulted other source')
,(1406,'440-E5','RT','Recommend laboratory test','2025-03-14 17:30:12',1, 'RT - Recommend laboratory test')
,(1407,'440-E5','SC','Self-care consultation','2025-03-14 17:30:12',1, 'SC - Self-care consultation')
,(1408,'440-E5','SW','Literature search/review','2025-03-14 17:30:12',1, 'SW - Literature search/review')
,(1409,'440-E5','TC','Payer/processor consulted','2025-03-14 17:30:12',1, 'TC - Payer/processor consulted')
,(1410,'440-E5','TH','Therapeutic product interchange','2025-03-14 17:30:12',1, 'TH - Therapeutic product interchange')
,(1411,'441-E6','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(1412,'441-E6','1A','Dispensed As Is, False Positive','2025-03-14 17:30:12',1, '1A - Dispensed As Is, False Positive')
,(1413,'441-E6','1B','Dispensed Prescription As Is','2025-03-14 17:30:12',1, '1B - Dispensed Prescription As Is')
,(1414,'441-E6','1C','Dispensed, With Different Dose','2025-03-14 17:30:12',1, '1C - Dispensed, With Different Dose')
,(1415,'441-E6','1D','Dispensed, With Different Directions','2025-03-14 17:30:12',1, '1D - Dispensed, With Different Directions')
,(1416,'441-E6','1E','Dispensed, With Different Drug','2025-03-14 17:30:12',1, '1E - Dispensed, With Different Drug')
,(1417,'441-E6','1F','Dispensed, With Different Quantity','2025-03-14 17:30:12',1, '1F - Dispensed, With Different Quantity')
,(1418,'441-E6','1G','Dispensed, With Prescriber Approval','2025-03-14 17:30:12',1, '1G - Dispensed, With Prescriber Approval')
,(1419,'441-E6','1H','Brand-to-Generic Change','2025-03-14 17:30:12',1, '1H - Brand-to-Generic Change')
,(1420,'441-E6','1J','Rx-to-OTC Change','2025-03-14 17:30:12',1, '1J - Rx-to-OTC Change')
,(1421,'441-E6','1K','Dispensed with Different Dosage Form','2025-03-14 17:30:12',1, '1K - Dispensed with Different Dosage Form')
,(1422,'441-E6','2A','Prescription Not Dispensed','2025-03-14 17:30:12',1, '2A - Prescription Not Dispensed')
,(1423,'441-E6','2B','Not Dispensed, Directions Clarified','2025-03-14 17:30:12',1, '2B - Not Dispensed, Directions Clarified')
,(1424,'441-E6','3A','Recommendation Accepted','2025-03-14 17:30:12',1, '3A - Recommendation Accepted')
,(1425,'441-E6','3B','Recommendation Not Accepted','2025-03-14 17:30:12',1, '3B - Recommendation Not Accepted')
,(1426,'441-E6','3C','Discontinued Drug','2025-03-14 17:30:12',1, '3C - Discontinued Drug')
,(1427,'441-E6','3D','Regimen Changed','2025-03-14 17:30:12',1, '3D - Regimen Changed')
,(1428,'441-E6','3E','Therapy Changed','2025-03-14 17:30:12',1, '3E - Therapy Changed')
,(1429,'441-E6','3F','Therapy Changed-cost increased acknowledged','2025-03-14 17:30:12',1, '3F - Therapy Changed-cost increased acknowledged')
,(1430,'441-E6','3G','Drug Therapy Unchanged','2025-03-14 17:30:12',1, '3G - Drug Therapy Unchanged')
,(1431,'441-E6','3H','Follow-Up/Report','2025-03-14 17:30:12',1, '3H - Follow-Up/Report')
,(1432,'441-E6','3J','Patient Referral','2025-03-14 17:30:12',1, '3J - Patient Referral')
,(1433,'441-E6','3K','Instructions Understood','2025-03-14 17:30:12',1, '3K - Instructions Understood')
,(1434,'441-E6','3M','Compliance Aid Provided','2025-03-14 17:30:12',1, '3M - Compliance Aid Provided')
,(1435,'441-E6','3N','Medication Administered','2025-03-14 17:30:12',1, '3N - Medication Administered')
,(1436,'441-E6','4A','Prescribed With Acknowledgments','2025-03-14 17:30:12',1, '4A - Prescribed With Acknowledgments')
,(1437,'450-EF','10','Tablet','2025-03-14 17:30:12',1, '10 - Tablet')
,(1438,'450-EF','11','Solution','2025-03-14 17:30:12',1, '11 - Solution')
,(1439,'450-EF','12','Suspension','2025-03-14 17:30:12',1, '12 - Suspension')
,(1440,'450-EF','13','Lotion','2025-03-14 17:30:12',1, '13 - Lotion')
,(1441,'450-EF','14','Shampoo','2025-03-14 17:30:12',1, '14 - Shampoo')
,(1442,'450-EF','15','Elixir','2025-03-14 17:30:12',1, '15 - Elixir')
,(1443,'450-EF','16','Syrup','2025-03-14 17:30:12',1, '16 - Syrup')
,(1444,'450-EF','17','Lozenge','2025-03-14 17:30:12',1, '17 - Lozenge')
,(1445,'450-EF','18','Enema','2025-03-14 17:30:12',1, '18 - Enema')
,(1446,'450-EF','01','Capsule','2025-03-14 17:30:12',1, '01 - Capsule')
,(1447,'450-EF','02','Ointment','2025-03-14 17:30:12',1, '02 - Ointment')
,(1448,'450-EF','03','Cream','2025-03-14 17:30:12',1, '03 - Cream')
,(1449,'450-EF','04','Suppository','2025-03-14 17:30:12',1, '04 - Suppository')
,(1450,'450-EF','05','Powder','2025-03-14 17:30:12',1, '05 - Powder')
,(1451,'450-EF','06','Emulsion','2025-03-14 17:30:12',1, '06 - Emulsion')
,(1452,'450-EF','07','Liquid','2025-03-14 17:30:12',1, '07 - Liquid')
,(1453,'451-EG','1','Each','2025-03-14 17:30:12',1, '1 - Each')
,(1454,'451-EG','2','Grams','2025-03-14 17:30:12',1, '2 - Grams')
,(1455,'451-EG','3','Milliliters','2025-03-14 17:30:12',1, '3 - Milliliters')
,(1456,'453-EJ','10','Pharmacy Practice Activity Classification (PPAC)','2025-03-14 17:30:12',1, '10 - Pharmacy Practice Activity Classification (PPAC)')
,(1457,'453-EJ','11','National Pharmaceutical Product Interface Code (NAPPI)','2025-03-14 17:30:12',1, '11 - National Pharmaceutical Product Interface Code (NAPPI)')
,(1458,'453-EJ','12','Global Trade Identification Number (GTIN)','2025-03-14 17:30:12',1, '12 - Global Trade Identification Number (GTIN)')
,(1459,'453-EJ','13','Drug Identification Number (DIN)','2025-03-14 17:30:12',1, '13 - Drug Identification Number (DIN)')
,(1460,'453-EJ','15','First DataBank Formulation ID (GCN)','2025-03-14 17:30:12',1, '15 - First DataBank Formulation ID (GCN)')
,(1461,'453-EJ','28','First DataBank Medication Name ID (FDB Med Name ID)','2025-03-14 17:30:12',1, '28 - First DataBank Medication Name ID (FDB Med Name ID)')
,(1462,'453-EJ','29','First DataBank Routed Medication ID (FDB Routed Med ID)','2025-03-14 17:30:12',1, '29 - First DataBank Routed Medication ID (FDB Routed Med ID)')
,(1463,'453-EJ','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','2025-03-14 17:30:12',1, '30 - First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)')
,(1464,'453-EJ','31','First DataBank Medication ID (FDB MedID)','2025-03-14 17:30:12',1, '31 - First DataBank Medication ID (FDB MedID)')
,(1465,'453-EJ','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','2025-03-14 17:30:12',1, '32 - First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)')
,(1466,'453-EJ','33','First DataBank Ingredient List ID (HICL_SEQNO)','2025-03-14 17:30:12',1, '33 - First DataBank Ingredient List ID (HICL_SEQNO)')
,(1467,'453-EJ','38','RxNorm Semantic Clinical Drug (SCD)','2025-03-14 17:30:12',1, '38 - RxNorm Semantic Clinical Drug (SCD)')
,(1468,'453-EJ','39','RxNorm Semantic Branded Drug (SBD)','2025-03-14 17:30:12',1, '39 - RxNorm Semantic Branded Drug (SBD)')
,(1469,'453-EJ','40','RxNorm Generic Package (GPCK)','2025-03-14 17:30:12',1, '40 - RxNorm Generic Package (GPCK)')
,(1470,'453-EJ','41','RxNorm Branded Package (BPCK)','2025-03-14 17:30:12',1, '41 - RxNorm Branded Package (BPCK)')
,(1471,'453-EJ','42','Gold Standard Marketed Product Identifier (MPid)','2025-03-14 17:30:12',1, '42 - Gold Standard Marketed Product Identifier (MPid)')
,(1472,'453-EJ','43','Gold Standard Product Identifier (ProdID)','2025-03-14 17:30:12',1, '43 - Gold Standard Product Identifier (ProdID)')
,(1473,'453-EJ','44','Gold Standard Specific Product Identifier (SPID)','2025-03-14 17:30:12',1, '44 - Gold Standard Specific Product Identifier (SPID)')
,(1474,'453-EJ','45','Device Identifier (DI)','2025-03-14 17:30:12',1, '45 - Device Identifier (DI)')
,(1475,'453-EJ','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1476,'453-EJ','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(1477,'453-EJ','01','Universal Product Code (UPC)','2025-03-14 17:30:12',1, '01 - Universal Product Code (UPC)')
,(1478,'453-EJ','02','Health Related Item (HRI)','2025-03-14 17:30:12',1, '02 - Health Related Item (HRI)')
,(1479,'453-EJ','03','National Drug Code (NDC)','2025-03-14 17:30:12',1, '03 - National Drug Code (NDC)')
,(1480,'453-EJ','04','Health Industry Business Communications Council (HIBCC)','2025-03-14 17:30:12',1, '04 - Health Industry Business Communications Council (HIBCC)')
,(1481,'453-EJ','06','Drug Use Review/ Professional Pharmacy Service (DUR/PPS)','2025-03-14 17:30:12',1, '06 - Drug Use Review/ Professional Pharmacy Service (DUR/PPS)')
,(1482,'453-EJ','07','Current Procedural Terminology (CPT4)','2025-03-14 17:30:12',1, '07 - Current Procedural Terminology (CPT4)')
,(1483,'453-EJ','08','Current Procedural Terminology (CPT5)','2025-03-14 17:30:12',1, '08 - Current Procedural Terminology (CPT5)')
,(1484,'453-EJ','09','Healthcare Common Procedure Coding System (HCPCS)','2025-03-14 17:30:12',1, '09 - Healthcare Common Procedure Coding System (HCPCS)')
,(1485,'453-EJ','05','Department of Defense (DOD)','2025-03-14 17:30:12',1, '05 - Department of Defense (DOD)')
,(1486,'455-EM','1','Rx Billing','2025-03-14 17:30:12',1, '1 - Rx Billing')
,(1487,'455-EM','2','Service Billing','2025-03-14 17:30:12',1, '2 - Service Billing')
,(1488,'455-EM','3','Non Prescription Product','2025-03-14 17:30:12',1, '3 - Non Prescription Product')
,(1489,'461-EU','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1490,'461-EU','1','Prior Authorization','2025-03-14 17:30:12',1, '1 - Prior Authorization')
,(1491,'461-EU','2','Medical Certification','2025-03-14 17:30:12',1, '2 - Medical Certification')
,(1492,'461-EU','3','EPSDT (Early Periodic Screening Diagnosis Treatment)','2025-03-14 17:30:12',1, '3 - EPSDT (Early Periodic Screening Diagnosis Treatment)')
,(1493,'461-EU','4','Exemption from Copay and/or Coinsurance','2025-03-14 17:30:12',1, '4 - Exemption from Copay and/or Coinsurance')
,(1494,'461-EU','5','Exemption from RX','2025-03-14 17:30:12',1, '5 - Exemption from RX')
,(1495,'461-EU','6','Family Planning Indicator','2025-03-14 17:30:12',1, '6 - Family Planning Indicator')
,(1496,'461-EU','7','TANF (Temporary Assistance for Needy Families)','2025-03-14 17:30:12',1, '7 - TANF (Temporary Assistance for Needy Families)')
,(1497,'461-EU','8','Payer Defined Exemption','2025-03-14 17:30:12',1, '8 - Payer Defined Exemption')
,(1498,'461-EU','9','Emergency Preparedness','2025-03-14 17:30:12',1, '9 - Emergency Preparedness')
,(1499,'462-EV','91100000000','Emergency Preparedness (EP) Refill Extension Override','2025-03-14 17:30:12',1, '91100000000 - Emergency Preparedness (EP) Refill Extension Override')
,(1500,'462-EV','91100000001','Emergency Preparedness (EP) Refill Too Soon Edit Override','2025-03-14 17:30:12',1, '91100000001 - Emergency Preparedness (EP) Refill Too Soon Edit Override')
,(1501,'462-EV','91100000002','Emergency Preparedness (EP) Prior Authorization Requirement Override','2025-03-14 17:30:12',1, '91100000002 - Emergency Preparedness (EP) Prior Authorization Requirement Override')
,(1502,'462-EV','91100000003','Emergency Preparedness (EP) Accumulated Quantity Override','2025-03-14 17:30:12',1, '91100000003 - Emergency Preparedness (EP) Accumulated Quantity Override')
,(1503,'462-EV','91100000004','Emergency Preparedness (EP) Step Therapy Override','2025-03-14 17:30:12',1, '91100000004 - Emergency Preparedness (EP) Step Therapy Override')
,(1504,'462-EV','91100000005','Emergency Preparedness (EP) override all of the above','2025-03-14 17:30:12',1, '91100000005 - Emergency Preparedness (EP) override all of the above')
,(1505,'465-EY','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1506,'465-EY','01','Drug Enforcement Administration (DEA)','2025-03-14 17:30:12',1, '01 - Drug Enforcement Administration (DEA)')
,(1507,'465-EY','02','State License','2025-03-14 17:30:12',1, '02 - State License')
,(1508,'465-EY','03','Social Security Number (SSN)','2025-03-14 17:30:12',1, '03 - Social Security Number (SSN)')
,(1509,'465-EY','04','Name','2025-03-14 17:30:12',1, '04 - Name')
,(1510,'465-EY','05','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '05 - National Provider Identifier (NPI)')
,(1511,'465-EY','06','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '06 - Health Industry Number (HIN)')
,(1512,'465-EY','07','State Issued','2025-03-14 17:30:12',1, '07 - State Issued')
,(1513,'466-EZ','10','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '10 - Health Industry Number (HIN)')
,(1514,'466-EZ','11','Federal Tax ID','2025-03-14 17:30:12',1, '11 - Federal Tax ID')
,(1515,'466-EZ','12','Drug Enforcement Administration (DEA) Number','2025-03-14 17:30:12',1, '12 - Drug Enforcement Administration (DEA) Number')
,(1516,'466-EZ','13','State Issued','2025-03-14 17:30:12',1, '13 - State Issued')
,(1517,'466-EZ','14','Plan Specific','2025-03-14 17:30:12',1, '14 - Plan Specific')
,(1518,'466-EZ','15','HCIdea','2025-03-14 17:30:12',1, '15 - HCIdea')
,(1519,'466-EZ','17','Foreign Prescriber Identifier','2025-03-14 17:30:12',1, '17 - Foreign Prescriber Identifier')
,(1520,'466-EZ','18','No Prescriber Required','2025-03-14 17:30:12',1, '18 - No Prescriber Required')
,(1521,'466-EZ','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1522,'466-EZ','01','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '01 - National Provider Identifier (NPI)')
,(1523,'466-EZ','02','Blue Cross','2025-03-14 17:30:12',1, '02 - Blue Cross')
,(1524,'466-EZ','03','Blue Shield','2025-03-14 17:30:12',1, '03 - Blue Shield')
,(1525,'466-EZ','04','Medicare','2025-03-14 17:30:12',1, '04 - Medicare')
,(1526,'466-EZ','05','Medicaid','2025-03-14 17:30:12',1, '05 - Medicaid')
,(1527,'466-EZ','06','UPIN (Unique Physician/Practitioner Identification Number)','2025-03-14 17:30:12',1, '06 - UPIN (Unique Physician/Practitioner Identification Number)')
,(1528,'466-EZ','08','State License','2025-03-14 17:30:12',1, '08 - State License')
,(1529,'466-EZ','09','TRICARE','2025-03-14 17:30:12',1, '09 - TRICARE')
,(1530,'466-EZ','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(1531,'466-EZ','07','NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)','2025-03-14 17:30:12',1, '07 - NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)')
,(1532,'468-2E','10','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '10 - Health Industry Number (HIN)')
,(1533,'468-2E','11','Federal Tax ID','2025-03-14 17:30:12',1, '11 - Federal Tax ID')
,(1534,'468-2E','12','Drug Enforcement Administration (DEA) Number','2025-03-14 17:30:12',1, '12 - Drug Enforcement Administration (DEA) Number')
,(1535,'468-2E','13','State Issued','2025-03-14 17:30:12',1, '13 - State Issued')
,(1536,'468-2E','14','Plan Specific','2025-03-14 17:30:12',1, '14 - Plan Specific')
,(1537,'468-2E','15','HCIdea','2025-03-14 17:30:12',1, '15 - HCIdea')
,(1538,'468-2E','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1539,'468-2E','01','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '01 - National Provider Identifier (NPI)')
,(1540,'468-2E','02','Blue Cross','2025-03-14 17:30:12',1, '02 - Blue Cross')
,(1541,'468-2E','03','Blue Shield','2025-03-14 17:30:12',1, '03 - Blue Shield')
,(1542,'468-2E','04','Medicare','2025-03-14 17:30:12',1, '04 - Medicare')
,(1543,'468-2E','05','Medicaid','2025-03-14 17:30:12',1, '05 - Medicaid')
,(1544,'468-2E','06','UPIN (Unique Physician/Practitioner Identification Number)','2025-03-14 17:30:12',1, '06 - UPIN (Unique Physician/Practitioner Identification Number)')
,(1545,'468-2E','08','State License','2025-03-14 17:30:12',1, '08 - State License')
,(1546,'468-2E','09','TRICARE','2025-03-14 17:30:12',1, '09 - TRICARE')
,(1547,'468-2E','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(1548,'468-2E','07','NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)','2025-03-14 17:30:12',1, '07 - NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)')
,(1549,'474-8E','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(1550,'474-8E','11','Level 1 (Lowest) = Straightforward','2025-03-14 17:30:12',1, '11 - Level 1 (Lowest) = Straightforward')
,(1551,'474-8E','12','Level 2 (Low Complexity)','2025-03-14 17:30:12',1, '12 - Level 2 (Low Complexity)')
,(1552,'474-8E','13','Level 3 (Moderate Complexity)','2025-03-14 17:30:12',1, '13 - Level 3 (Moderate Complexity)')
,(1553,'474-8E','14','Level 4 (High Complexity)','2025-03-14 17:30:12',1, '14 - Level 4 (High Complexity)')
,(1554,'474-8E','15','Level 5 (Highest) = Comprehensive','2025-03-14 17:30:12',1, '15 - Level 5 (Highest) = Comprehensive')
,(1555,'474-8E','16','Low Level Complexity','2025-03-14 17:30:12',1, '16 - Low Level Complexity')
,(1556,'474-8E','17','Mid-Level Complexity Non-Hazardous','2025-03-14 17:30:12',1, '17 - Mid-Level Complexity Non-Hazardous')
,(1557,'474-8E','18','Mid-Level Complexity Hazardous','2025-03-14 17:30:12',1, '18 - Mid-Level Complexity Hazardous')
,(1558,'474-8E','19','High Level Non-Hazardous','2025-03-14 17:30:12',1, '19 - High Level Non-Hazardous')
,(1559,'474-8E','20','High Level Hazardous','2025-03-14 17:30:12',1, '20 - High Level Hazardous')
,(1560,'474-8E','21','High Level Non-Hazardous Sterile','2025-03-14 17:30:12',1, '21 - High Level Non-Hazardous Sterile')
,(1561,'474-8E','22','High level Hazardous Sterile','2025-03-14 17:30:12',1, '22 - High level Hazardous Sterile')
,(1562,'475-J9','11','National Pharmaceutical Product Interface Code (NAPPI)','2025-03-14 17:30:12',1, '11 - National Pharmaceutical Product Interface Code (NAPPI)')
,(1563,'475-J9','12','Global Trade Identification Number (GTIN)','2025-03-14 17:30:12',1, '12 - Global Trade Identification Number (GTIN)')
,(1564,'475-J9','13','Drug Identification Number (DIN)','2025-03-14 17:30:12',1, '13 - Drug Identification Number (DIN)')
,(1565,'475-J9','14','Medi Span''s Generic Product Identifier (GPI)','2025-03-14 17:30:12',1, '14 - Medi Span''s Generic Product Identifier (GPI)')
,(1566,'475-J9','15','First DataBank Formulation ID (GCN)','2025-03-14 17:30:12',1, '15 - First DataBank Formulation ID (GCN)')
,(1567,'475-J9','16','Truven/Micromedex Generic Formulation Code (GFC)','2025-03-14 17:30:12',1, '16 - Truven/Micromedex Generic Formulation Code (GFC)')
,(1568,'475-J9','17','Medi Span''s Drug Descriptor ID (DDID)','2025-03-14 17:30:12',1, '17 - Medi Span''s Drug Descriptor ID (DDID)')
,(1569,'475-J9','18','First DataBank SmartKey','2025-03-14 17:30:12',1, '18 - First DataBank SmartKey')
,(1570,'475-J9','19','Truven/Micromedex Generic Master (GM)','2025-03-14 17:30:12',1, '19 - Truven/Micromedex Generic Master (GM)')
,(1571,'475-J9','20','International Classification of Diseases (ICD9)','2025-03-14 17:30:12',1, '20 - International Classification of Diseases (ICD9)')
,(1572,'475-J9','21','International Classification of Diseases-10-Clinical Modifications (ICD-10-CM)','2025-03-14 17:30:12',1, '21 - International Classification of Diseases-10-Clinical Modifications (ICD-10-CM)')
,(1573,'475-J9','23','National Criteria Care Institute (NCCI)','2025-03-14 17:30:12',1, '23 - National Criteria Care Institute (NCCI)')
,(1574,'475-J9','24','The Systematized Nomenclature of Medicine Clinical Terms® (SNOMED)','2025-03-14 17:30:12',1, '24 - The Systematized Nomenclature of Medicine Clinical Terms® (SNOMED)')
,(1575,'475-J9','25','Current Dental Terminology (CDT)','2025-03-14 17:30:12',1, '25 - Current Dental Terminology (CDT)')
,(1576,'475-J9','26','American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM IV)','2025-03-14 17:30:12',1, '26 - American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM IV)')
,(1577,'475-J9','27','International Classification of Diseases-10-Procedure Coding System (ICD-10-PCS)','2025-03-14 17:30:12',1, '27 - International Classification of Diseases-10-Procedure Coding System (ICD-10-PCS)')
,(1578,'475-J9','28','First DataBank Medication Name ID (FDB Med Name ID)','2025-03-14 17:30:12',1, '28 - First DataBank Medication Name ID (FDB Med Name ID)')
,(1579,'475-J9','29','First DataBank Routed Medication ID (FDB Routed Med ID)','2025-03-14 17:30:12',1, '29 - First DataBank Routed Medication ID (FDB Routed Med ID)')
,(1580,'475-J9','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','2025-03-14 17:30:12',1, '30 - First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)')
,(1581,'475-J9','31','First DataBank Medication ID (FDB MedID)','2025-03-14 17:30:12',1, '31 - First DataBank Medication ID (FDB MedID)')
,(1582,'475-J9','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','2025-03-14 17:30:12',1, '32 - First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)')
,(1583,'475-J9','33','First DataBank Ingredient List ID (HICL_SEQNO)','2025-03-14 17:30:12',1, '33 - First DataBank Ingredient List ID (HICL_SEQNO)')
,(1584,'475-J9','35','Logical Observation Identifier Names and Codes (LOINC)','2025-03-14 17:30:12',1, '35 - Logical Observation Identifier Names and Codes (LOINC)')
,(1585,'475-J9','37','American Hospital Formulary Service (AHFS)','2025-03-14 17:30:12',1, '37 - American Hospital Formulary Service (AHFS)')
,(1586,'475-J9','38','RxNorm Semantic Clinical Drug (SCD)','2025-03-14 17:30:12',1, '38 - RxNorm Semantic Clinical Drug (SCD)')
,(1587,'475-J9','39','RxNorm Semantic Branded Drug (SBD)','2025-03-14 17:30:12',1, '39 - RxNorm Semantic Branded Drug (SBD)')
,(1588,'475-J9','40','RxNorm Generic Package (GPCK)','2025-03-14 17:30:12',1, '40 - RxNorm Generic Package (GPCK)')
,(1589,'475-J9','41','RxNorm Branded Package (BPCK)','2025-03-14 17:30:12',1, '41 - RxNorm Branded Package (BPCK)')
,(1590,'475-J9','45','Device Identifier (DI)','2025-03-14 17:30:12',1, '45 - Device Identifier (DI)')
,(1591,'475-J9','46','American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM 5)','2025-03-14 17:30:12',1, '46 - American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM 5)')
,(1592,'475-J9','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1593,'475-J9','01','Universal Product Code (UPC)','2025-03-14 17:30:12',1, '01 - Universal Product Code (UPC)')
,(1594,'475-J9','02','Health Related Item (HRI)','2025-03-14 17:30:12',1, '02 - Health Related Item (HRI)')
,(1595,'475-J9','03','National Drug Code (NDC)','2025-03-14 17:30:12',1, '03 - National Drug Code (NDC)')
,(1596,'475-J9','04','Health Industry Business Communications Council (HIBCC)','2025-03-14 17:30:12',1, '04 - Health Industry Business Communications Council (HIBCC)')
,(1597,'475-J9','07','Current Procedural Terminology (CPT4)','2025-03-14 17:30:12',1, '07 - Current Procedural Terminology (CPT4)')
,(1598,'475-J9','08','Current Procedural Terminology (CPT5)','2025-03-14 17:30:12',1, '08 - Current Procedural Terminology (CPT5)')
,(1599,'475-J9','09','Healthcare Common Procedure Coding System (HCPCS)','2025-03-14 17:30:12',1, '09 - Healthcare Common Procedure Coding System (HCPCS)')
,(1600,'475-J9','05','Department of Defense (DOD)','2025-03-14 17:30:12',1, '05 - Department of Defense (DOD)')
,(1601,'479-H8','11','Medication Administration','2025-03-14 17:30:12',1, '11 - Medication Administration')
,(1602,'479-H8','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1603,'479-H8','01','Delivery Cost','2025-03-14 17:30:12',1, '01 - Delivery Cost')
,(1604,'479-H8','02','Shipping Cost','2025-03-14 17:30:12',1, '02 - Shipping Cost')
,(1605,'479-H8','03','Postage Cost','2025-03-14 17:30:12',1, '03 - Postage Cost')
,(1606,'479-H8','04','Administrative Cost','2025-03-14 17:30:12',1, '04 - Administrative Cost')
,(1607,'479-H8','09','Compound Preparation Cost Submitted','2025-03-14 17:30:12',1, '09 - Compound Preparation Cost Submitted')
,(1608,'484-JE','02','Ingredient Cost','2025-03-14 17:30:12',1, '02 - Ingredient Cost')
,(1609,'484-JE','03','Ingredient Cost + Dispensing Fee','2025-03-14 17:30:12',1, '03 - Ingredient Cost + Dispensing Fee')
,(1610,'484-JE','04','Professional Service Fee - The dollar amount/value for the professional service.','2025-03-14 17:30:12',1, '04 - Professional Service Fee - The dollar amount/value for the professional service.')
,(1611,'488-RE','11','National Pharmaceutical Product Interface Code (NAPPI)','2025-03-14 17:30:12',1, '11 - National Pharmaceutical Product Interface Code (NAPPI)')
,(1612,'488-RE','12','Global Trade Identification Number (GTIN)','2025-03-14 17:30:12',1, '12 - Global Trade Identification Number (GTIN)')
,(1613,'488-RE','13','Drug Identification Number (DIN)','2025-03-14 17:30:12',1, '13 - Drug Identification Number (DIN)')
,(1614,'488-RE','15','First DataBank Formulation ID (GCN)','2025-03-14 17:30:12',1, '15 - First DataBank Formulation ID (GCN)')
,(1615,'488-RE','28','First DataBank Medication Name ID (FDB Med Name ID)','2025-03-14 17:30:12',1, '28 - First DataBank Medication Name ID (FDB Med Name ID)')
,(1616,'488-RE','29','First DataBank Routed Medication ID (FDB Routed Med ID)','2025-03-14 17:30:12',1, '29 - First DataBank Routed Medication ID (FDB Routed Med ID)')
,(1617,'488-RE','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','2025-03-14 17:30:12',1, '30 - First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)')
,(1618,'488-RE','31','First DataBank Medication ID (FDB MedID)','2025-03-14 17:30:12',1, '31 - First DataBank Medication ID (FDB MedID)')
,(1619,'488-RE','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','2025-03-14 17:30:12',1, '32 - First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)')
,(1620,'488-RE','33','First DataBank Ingredient List ID (HICL_SEQNO)','2025-03-14 17:30:12',1, '33 - First DataBank Ingredient List ID (HICL_SEQNO)')
,(1621,'488-RE','45','Device Identifier (DI)','2025-03-14 17:30:12',1, '45 - Device Identifier (DI)')
,(1622,'488-RE','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1623,'488-RE','01','Universal Product Code (UPC)','2025-03-14 17:30:12',1, '01 - Universal Product Code (UPC)')
,(1624,'488-RE','02','Health Related Item (HRI)','2025-03-14 17:30:12',1, '02 - Health Related Item (HRI)')
,(1625,'488-RE','03','National Drug Code (NDC)','2025-03-14 17:30:12',1, '03 - National Drug Code (NDC)')
,(1626,'488-RE','04','Health Industry Business Communications Council (HIBCC)','2025-03-14 17:30:12',1, '04 - Health Industry Business Communications Council (HIBCC)')
,(1627,'488-RE','05','Department of Defense (DOD)','2025-03-14 17:30:12',1, '05 - Department of Defense (DOD)')
,(1628,'490-UE','10','ASP (Average Sales Price)','2025-03-14 17:30:12',1, '10 - ASP (Average Sales Price)')
,(1629,'490-UE','11','AMP (Average Manufacturer Price)','2025-03-14 17:30:12',1, '11 - AMP (Average Manufacturer Price)')
,(1630,'490-UE','12','WAC (Wholesale Acquisition Cost)','2025-03-14 17:30:12',1, '12 - WAC (Wholesale Acquisition Cost)')
,(1631,'490-UE','13','Special Patient Pricing','2025-03-14 17:30:12',1, '13 - Special Patient Pricing')
,(1632,'490-UE','14','Cost basis on unreportable quantities.','2025-03-14 17:30:12',1, '14 - Cost basis on unreportable quantities.')
,(1633,'490-UE','15','Free product or no associated cost.','2025-03-14 17:30:12',1, '15 - Free product or no associated cost.')
,(1634,'490-UE','00','Default','2025-03-14 17:30:12',1, '00 - Default')
,(1635,'490-UE','01','AWP (Average Wholesale Price)','2025-03-14 17:30:12',1, '01 - AWP (Average Wholesale Price)')
,(1636,'490-UE','02','Local Wholesaler','2025-03-14 17:30:12',1, '02 - Local Wholesaler')
,(1637,'490-UE','03','Direct','2025-03-14 17:30:12',1, '03 - Direct')
,(1638,'490-UE','04','EAC (Estimated Acquisition Cost)','2025-03-14 17:30:12',1, '04 - EAC (Estimated Acquisition Cost)')
,(1639,'490-UE','05','Acquisition','2025-03-14 17:30:12',1, '05 - Acquisition')
,(1640,'490-UE','06','MAC (Maximum Allowable Cost)','2025-03-14 17:30:12',1, '06 - MAC (Maximum Allowable Cost)')
,(1641,'490-UE','07','Usual & Customary','2025-03-14 17:30:12',1, '07 - Usual & Customary')
,(1642,'490-UE','08','340B/Disproportionate Share Pricing/Public Health Service','2025-03-14 17:30:12',1, '08 - 340B/Disproportionate Share Pricing/Public Health Service')
,(1643,'490-UE','09','Other','2025-03-14 17:30:12',1, '09 - Other')
,(1644,'492-WE','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(1645,'492-WE','01','International Classification of Diseases (ICD-9)','2025-03-14 17:30:12',1, '01 - International Classification of Diseases (ICD-9)')
,(1646,'492-WE','02','International Classification of Diseases-10-Clinical Modifications(ICD-10-CM)','2025-03-14 17:30:12',1, '02 - International Classification of Diseases-10-Clinical Modifications(ICD-10-CM)')
,(1647,'492-WE','03','National Criteria Care Institute (NCCI)','2025-03-14 17:30:12',1, '03 - National Criteria Care Institute (NCCI)')
,(1648,'492-WE','04','The Systematized Nomenclature of Medicine Clinical Terms (SNOMED)','2025-03-14 17:30:12',1, '04 - The Systematized Nomenclature of Medicine Clinical Terms (SNOMED)')
,(1649,'492-WE','05','Current Dental Terminology (CDT)','2025-03-14 17:30:12',1, '05 - Current Dental Terminology (CDT)')
,(1650,'492-WE','07','American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-IV)','2025-03-14 17:30:12',1, '07 - American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-IV)')
,(1651,'492-WE','08','American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-5)','2025-03-14 17:30:12',1, '08 - American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-5)')
,(1652,'496-H2','10','Serum Glutamic-Pyruvic Transaminase (SGPT)','2025-03-14 17:30:12',1, '10 - Serum Glutamic-Pyruvic Transaminase (SGPT)')
,(1653,'496-H2','11','Alkaline Phosphatase','2025-03-14 17:30:12',1, '11 - Alkaline Phosphatase')
,(1654,'496-H2','12','Theophylline','2025-03-14 17:30:12',1, '12 - Theophylline')
,(1655,'496-H2','13','Digoxin','2025-03-14 17:30:12',1, '13 - Digoxin')
,(1656,'496-H2','14','Weight','2025-03-14 17:30:12',1, '14 - Weight')
,(1657,'496-H2','15','Body Surface Area (BSA)','2025-03-14 17:30:12',1, '15 - Body Surface Area (BSA)')
,(1658,'496-H2','16','Height','2025-03-14 17:30:12',1, '16 - Height')
,(1659,'496-H2','17','Creatinine Clearance (CrCl)','2025-03-14 17:30:12',1, '17 - Creatinine Clearance (CrCl)')
,(1660,'496-H2','18','Cholesterol','2025-03-14 17:30:12',1, '18 - Cholesterol')
,(1661,'496-H2','19','Low Density Lipoprotein (LDL)','2025-03-14 17:30:12',1, '19 - Low Density Lipoprotein (LDL)')
,(1662,'496-H2','20','High Density Lipoprotein (HDL)','2025-03-14 17:30:12',1, '20 - High Density Lipoprotein (HDL)')
,(1663,'496-H2','21','Triglycerides (TG)','2025-03-14 17:30:12',1, '21 - Triglycerides (TG)')
,(1664,'496-H2','22','Bone Mineral Density (BMD T-Score)','2025-03-14 17:30:12',1, '22 - Bone Mineral Density (BMD T-Score)')
,(1665,'496-H2','23','Prothrombin Time (PT)','2025-03-14 17:30:12',1, '23 - Prothrombin Time (PT)')
,(1666,'496-H2','24','Hemoglobin (Hb; Hgb)','2025-03-14 17:30:12',1, '24 - Hemoglobin (Hb; Hgb)')
,(1667,'496-H2','25','Hematocrit (Hct)','2025-03-14 17:30:12',1, '25 - Hematocrit (Hct)')
,(1668,'496-H2','26','White Blood Cell Count (WBC)','2025-03-14 17:30:12',1, '26 - White Blood Cell Count (WBC)')
,(1669,'496-H2','27','Red Blood Cell Count (RBC)','2025-03-14 17:30:12',1, '27 - Red Blood Cell Count (RBC)')
,(1670,'496-H2','28','Heart Rate','2025-03-14 17:30:12',1, '28 - Heart Rate')
,(1671,'496-H2','29','Absolute Neutrophil Count (ANC)','2025-03-14 17:30:12',1, '29 - Absolute Neutrophil Count (ANC)')
,(1672,'496-H2','30','Activated Partial Thromboplastin Time (APTT)','2025-03-14 17:30:12',1, '30 - Activated Partial Thromboplastin Time (APTT)')
,(1673,'496-H2','31','CD4 Count (also T4 Count, T-helper cells)','2025-03-14 17:30:12',1, '31 - CD4 Count (also T4 Count, T-helper cells)')
,(1674,'496-H2','32','Partial Thromboplastin Time (PTT)','2025-03-14 17:30:12',1, '32 - Partial Thromboplastin Time (PTT)')
,(1675,'496-H2','33','T-Cell Count','2025-03-14 17:30:12',1, '33 - T-Cell Count')
,(1676,'496-H2','34','INR-International Normalized Ratio','2025-03-14 17:30:12',1, '34 - INR-International Normalized Ratio')
,(1677,'496-H2','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(1678,'496-H2','01','Blood Pressure (BP)','2025-03-14 17:30:12',1, '01 - Blood Pressure (BP)')
,(1679,'496-H2','02','Blood Glucose','2025-03-14 17:30:12',1, '02 - Blood Glucose')
,(1680,'496-H2','03','Temperature','2025-03-14 17:30:12',1, '03 - Temperature')
,(1681,'496-H2','04','Serum Creatinine (SCr)','2025-03-14 17:30:12',1, '04 - Serum Creatinine (SCr)')
,(1682,'496-H2','05','Glycosylated Hemoglobin (HbA1c)','2025-03-14 17:30:12',1, '05 - Glycosylated Hemoglobin (HbA1c)')
,(1683,'496-H2','06','Sodium (Na+)','2025-03-14 17:30:12',1, '06 - Sodium (Na+)')
,(1684,'496-H2','07','Potassium (K+)','2025-03-14 17:30:12',1, '07 - Potassium (K+)')
,(1685,'496-H2','08','Calcium (Ca++)','2025-03-14 17:30:12',1, '08 - Calcium (Ca++)')
,(1686,'496-H2','09','Serum Glutamic-Oxaloacetic Transaminase (SGOT)','2025-03-14 17:30:12',1, '09 - Serum Glutamic-Oxaloacetic Transaminase (SGOT)')
,(1687,'497-H3','10','Millimeters of mercury (mmHg)','2025-03-14 17:30:12',1, '10 - Millimeters of mercury (mmHg)')
,(1688,'497-H3','11','Centimeters squared (cm2)','2025-03-14 17:30:12',1, '11 - Centimeters squared (cm2)')
,(1689,'497-H3','12','Milliliters per minute (ml/min)','2025-03-14 17:30:12',1, '12 - Milliliters per minute (ml/min)')
,(1690,'497-H3','13','Percent (%)','2025-03-14 17:30:12',1, '13 - Percent (%)')
,(1691,'497-H3','14','Milliequivalents per milliliter (mEq/ml)','2025-03-14 17:30:12',1, '14 - Milliequivalents per milliliter (mEq/ml)')
,(1692,'497-H3','15','International units per liter (IU/L)','2025-03-14 17:30:12',1, '15 - International units per liter (IU/L)')
,(1693,'497-H3','16','Micrograms per milliliter (mcg/ml)','2025-03-14 17:30:12',1, '16 - Micrograms per milliliter (mcg/ml)')
,(1694,'497-H3','17','Nanograms per milliliter (ng/ml)','2025-03-14 17:30:12',1, '17 - Nanograms per milliliter (ng/ml)')
,(1695,'497-H3','18','Milligrams per milliliter (mg/ml)','2025-03-14 17:30:12',1, '18 - Milligrams per milliliter (mg/ml)')
,(1696,'497-H3','19','Ratio','2025-03-14 17:30:12',1, '19 - Ratio')
,(1697,'497-H3','20','SI Units','2025-03-14 17:30:12',1, '20 - SI Units')
,(1698,'497-H3','21','Millimoles/liter (mmol/l)','2025-03-14 17:30:12',1, '21 - Millimoles/liter (mmol/l)')
,(1699,'497-H3','22','Seconds','2025-03-14 17:30:12',1, '22 - Seconds')
,(1700,'497-H3','23','Grams per deciliter (g/dl)','2025-03-14 17:30:12',1, '23 - Grams per deciliter (g/dl)')
,(1701,'497-H3','24','Cells per cubic millimeter (cells/cu mm)','2025-03-14 17:30:12',1, '24 - Cells per cubic millimeter (cells/cu mm)')
,(1702,'497-H3','25','1,000,000 cells per cubic millimeter (million cells/cu mm)','2025-03-14 17:30:12',1, '25 - 1,000,000 cells per cubic millimeter (million cells/cu mm)')
,(1703,'497-H3','26','Standard deviation','2025-03-14 17:30:12',1, '26 - Standard deviation')
,(1704,'497-H3','27','Beats per minute','2025-03-14 17:30:12',1, '27 - Beats per minute')
,(1705,'497-H3','01','Inches (In)','2025-03-14 17:30:12',1, '01 - Inches (In)')
,(1706,'497-H3','02','Centimeters (cm)','2025-03-14 17:30:12',1, '02 - Centimeters (cm)')
,(1707,'497-H3','03','Pounds (lb)','2025-03-14 17:30:12',1, '03 - Pounds (lb)')
,(1708,'497-H3','04','Kilograms (kg)','2025-03-14 17:30:12',1, '04 - Kilograms (kg)')
,(1709,'497-H3','05','Celsius (C)','2025-03-14 17:30:12',1, '05 - Celsius (C)')
,(1710,'497-H3','06','Fahrenheit (F)','2025-03-14 17:30:12',1, '06 - Fahrenheit (F)')
,(1711,'497-H3','07','Meters squared (m2)','2025-03-14 17:30:12',1, '07 - Meters squared (m2)')
,(1712,'497-H3','08','Milligrams per deciliter (mg/dl)','2025-03-14 17:30:12',1, '08 - Milligrams per deciliter (mg/dl)')
,(1713,'497-H3','09','Units per milliliter (U/ml)','2025-03-14 17:30:12',1, '09 - Units per milliliter (U/ml)')
,(1714,'498-PA','1','Initial','2025-03-14 17:30:12',1, '1 - Initial')
,(1715,'498-PA','2','Reauthorization','2025-03-14 17:30:12',1, '2 - Reauthorization')
,(1716,'498-PA','3','Deferred','2025-03-14 17:30:12',1, '3 - Deferred')
,(1717,'498-PD','ME','Medical Exception','2025-03-14 17:30:12',1, 'ME - Medical Exception')
,(1718,'498-PD','PR','Plan Requirement','2025-03-14 17:30:12',1, 'PR - Plan Requirement')
,(1719,'498-PD','PL','Increase Plan Limitation','2025-03-14 17:30:12',1, 'PL - Increase Plan Limitation')
,(1720,'498-PJ','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(1721,'498-PJ','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(1722,'498-PJ','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(1723,'498-PJ','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(1724,'498-PJ','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(1725,'498-PJ','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(1726,'498-PJ','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(1727,'498-PJ','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(1728,'498-PJ','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(1729,'498-PJ','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(1730,'498-PJ','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(1731,'498-PJ','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(1732,'498-PJ','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(1733,'498-PJ','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(1734,'498-PJ','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(1735,'498-PJ','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(1736,'498-PJ','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(1737,'498-PJ','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(1738,'498-PJ','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(1739,'498-PJ','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(1740,'498-PJ','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(1741,'498-PJ','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(1742,'498-PJ','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(1743,'498-PJ','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(1744,'498-PJ','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(1745,'498-PJ','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(1746,'498-PJ','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(1747,'498-PJ','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(1748,'498-PJ','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(1749,'498-PJ','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(1750,'498-PJ','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(1751,'498-PJ','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(1752,'498-PJ','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(1753,'498-PJ','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(1754,'498-PJ','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(1755,'498-PJ','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(1756,'498-PJ','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(1757,'498-PJ','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(1758,'498-PJ','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(1759,'498-PJ','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(1760,'498-PJ','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(1761,'498-PJ','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(1762,'498-PJ','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(1763,'498-PJ','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(1764,'498-PJ','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(1765,'498-PJ','56','California','2025-03-14 17:30:12',1, '56 - California')
,(1766,'498-PJ','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(1767,'498-PJ','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(1768,'498-PJ','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(1769,'498-PJ','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(1770,'498-PJ','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(1771,'498-PJ','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(1772,'498-PJ','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(1773,'498-PJ','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(1774,'498-PJ','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(1775,'498-PJ','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(1776,'498-PJ','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(1777,'498-PJ','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(1778,'498-PJ','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(1779,'498-PJ','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(1780,'498-PJ','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(1781,'498-PJ','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(1782,'498-PJ','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(1783,'498-PJ','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(1784,'498-PJ','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(1785,'498-PJ','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(1786,'498-PJ','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(1787,'498-PJ','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(1788,'498-PJ','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(1789,'498-PJ','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(1790,'498-PJ','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(1791,'498-PJ','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(1792,'498-PJ','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(1793,'498-PJ','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(1794,'498-PJ','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(1795,'498-PJ','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(1796,'498-PJ','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(1797,'498-PJ','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(1798,'498-PJ','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(1799,'498-PJ','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(1800,'498-PJ','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(1801,'498-PJ','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(1802,'498-PJ','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(1803,'498-PJ','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(1804,'498-PJ','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(1805,'498-PJ','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(1806,'498-PJ','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(1807,'498-PJ','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(1808,'498-PJ','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(1809,'498-PJ','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(1810,'498-PJ','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(1811,'498-PJ','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(1812,'498-PJ','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(1813,'498-PJ','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(1814,'498-PJ','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(1815,'498-PJ','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(1816,'498-PJ','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(1817,'498-PJ','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(1818,'498-PJ','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(1819,'498-PJ','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(1820,'498-PJ','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(1821,'498-PJ','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(1822,'498-PJ','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(1823,'498-PJ','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(1824,'498-PJ','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(1825,'498-PJ','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(1826,'498-PJ','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(1827,'498-PJ','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(1828,'498-PJ','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(1829,'498-PJ','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(1830,'498-PJ','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(1831,'498-PJ','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(1832,'498-PJ','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(1833,'498-PJ','05','California','2025-03-14 17:30:12',1, '05 - California')
,(1834,'498-PJ','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(1835,'498-PJ','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(1836,'498-PJ','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(1837,'498-PJ','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(1838,'498-PJ','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(1839,'498-PJ','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(1840,'498-PJ','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(1841,'498-PJ','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(1842,'498-PJ','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(1843,'498-PJ','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(1844,'498-PJ','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(1845,'498-PJ','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(1846,'498-PJ','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(1847,'498-PJ','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(1848,'498-PJ','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(1849,'498-PJ','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(1850,'498-PJ','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(1851,'498-PJ','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(1852,'498-PJ','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(1853,'498-PJ','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(1854,'501-F1','A','Accepted','2025-03-14 17:30:12',1, 'A - Accepted')
,(1855,'501-F1','R','Rejected','2025-03-14 17:30:12',1, 'R - Rejected')
,(1856,'511-FB','10','M/I Patient Gender Code','2025-03-14 17:30:12',1, '10 - M/I Patient Gender Code')
,(1857,'511-FB','11','M/I Patient Relationship Code','2025-03-14 17:30:12',1, '11 - M/I Patient Relationship Code')
,(1858,'511-FB','12','M/I Place of Service','2025-03-14 17:30:12',1, '12 - M/I Place of Service')
,(1859,'511-FB','13','M/I Other Coverage Code','2025-03-14 17:30:12',1, '13 - M/I Other Coverage Code')
,(1860,'511-FB','14','M/I Eligibility Clarification Code','2025-03-14 17:30:12',1, '14 - M/I Eligibility Clarification Code')
,(1861,'511-FB','15','M/I Date of Service','2025-03-14 17:30:12',1, '15 - M/I Date of Service')
,(1862,'511-FB','16','M/I Prescription/Service Reference Number','2025-03-14 17:30:12',1, '16 - M/I Prescription/Service Reference Number')
,(1863,'511-FB','17','M/I Fill #','2025-03-14 17:30:12',1, '17 - M/I Fill #')
,(1864,'511-FB','19','M/I Days Supply','2025-03-14 17:30:12',1, '19 - M/I Days Supply')
,(1865,'511-FB','20','M/I Compound Code','2025-03-14 17:30:12',1, '20 - M/I Compound Code')
,(1866,'511-FB','21','M/I Product/Service ID','2025-03-14 17:30:12',1, '21 - M/I Product/Service ID')
,(1867,'511-FB','22','M/I Dispense As Written (DAW)/Product Selection Code','2025-03-14 17:30:12',1, '22 - M/I Dispense As Written (DAW)/Product Selection Code')
,(1868,'511-FB','23','M/I Ingredient Cost Submitted','2025-03-14 17:30:12',1, '23 - M/I Ingredient Cost Submitted')
,(1869,'511-FB','25','M/I Prescriber ID','2025-03-14 17:30:12',1, '25 - M/I Prescriber ID')
,(1870,'511-FB','26','M/I Unit Of Measure','2025-03-14 17:30:12',1, '26 - M/I Unit Of Measure')
,(1871,'511-FB','27','Product Identifier Not FDA/NSDE Listed','2025-03-14 17:30:12',1, '27 - Product Identifier Not FDA/NSDE Listed')
,(1872,'511-FB','28','M/I Date Prescription Written','2025-03-14 17:30:12',1, '28 - M/I Date Prescription Written')
,(1873,'511-FB','29','M/I Number Of Refills Authorized','2025-03-14 17:30:12',1, '29 - M/I Number Of Refills Authorized')
,(1874,'511-FB','30','Reversal Request Outside Processor Reversal Window','2025-03-14 17:30:12',1, '30 - Reversal Request Outside Processor Reversal Window')
,(1875,'511-FB','31','No Matching Paid Claim Found For Reversal Request','2025-03-14 17:30:12',1, '31 - No Matching Paid Claim Found For Reversal Request')
,(1876,'511-FB','32','M/I Level Of Service','2025-03-14 17:30:12',1, '32 - M/I Level Of Service')
,(1877,'511-FB','33','M/I Prescription Origin Code','2025-03-14 17:30:12',1, '33 - M/I Prescription Origin Code')
,(1878,'511-FB','34','M/I Submission Clarification Code','2025-03-14 17:30:12',1, '34 - M/I Submission Clarification Code')
,(1879,'511-FB','35','M/I Primary Care Provider ID','2025-03-14 17:30:12',1, '35 - M/I Primary Care Provider ID')
,(1880,'511-FB','38','M/I Basis Of Cost Determination','2025-03-14 17:30:12',1, '38 - M/I Basis Of Cost Determination')
,(1881,'511-FB','39','M/I Diagnosis Code','2025-03-14 17:30:12',1, '39 - M/I Diagnosis Code')
,(1882,'511-FB','40','Pharmacy Not Contracted With Plan/Processor On Date Of Service','2025-03-14 17:30:12',1, '40 - Pharmacy Not Contracted With Plan/Processor On Date Of Service')
,(1883,'511-FB','41','Submit Bill To Other Processor Or Primary Payer','2025-03-14 17:30:12',1, '41 - Submit Bill To Other Processor Or Primary Payer')
,(1884,'511-FB','42','Plan''s Prescriber Data Base Indicates The Prescriber ID Submitted Is Inactive Or Expired','2025-03-14 17:30:12',1, '42 - Plan''s Prescriber Data Base Indicates The Prescriber ID Submitted Is Inactive Or Expired')
,(1885,'511-FB','43','Plan''s Prescriber Data Base Indicates The Submitted Prescriber DEA Number Is Inactive Or Expired','2025-03-14 17:30:12',1, '43 - Plan''s Prescriber Data Base Indicates The Submitted Prescriber DEA Number Is Inactive Or Expired')
,(1886,'511-FB','44','Plan''s Prescriber Data Base Indicates The Submitted Prescriber DEA Number Is Not Found','2025-03-14 17:30:12',1, '44 - Plan''s Prescriber Data Base Indicates The Submitted Prescriber DEA Number Is Not Found')
,(1887,'511-FB','46','Plan''s Prescriber Data Base Indicates The Submitted Prescriber DEA Number Does Not Allow This Drug DEA Class','2025-03-14 17:30:12',1, '46 - Plan''s Prescriber Data Base Indicates The Submitted Prescriber DEA Number Does Not Allow This Drug DEA Class')
,(1888,'511-FB','50','Non-Matched Pharmacy Number','2025-03-14 17:30:12',1, '50 - Non-Matched Pharmacy Number')
,(1889,'511-FB','51','Non-Matched Group ID','2025-03-14 17:30:12',1, '51 - Non-Matched Group ID')
,(1890,'511-FB','52','Non-Matched Cardholder ID','2025-03-14 17:30:12',1, '52 - Non-Matched Cardholder ID')
,(1891,'511-FB','53','Non-Matched Person Code','2025-03-14 17:30:12',1, '53 - Non-Matched Person Code')
,(1892,'511-FB','54','Non-Matched Product/Service ID Number','2025-03-14 17:30:12',1, '54 - Non-Matched Product/Service ID Number')
,(1893,'511-FB','55','Non-Matched Product Package Size','2025-03-14 17:30:12',1, '55 - Non-Matched Product Package Size')
,(1894,'511-FB','56','Non-Matched Prescriber ID','2025-03-14 17:30:12',1, '56 - Non-Matched Prescriber ID')
,(1895,'511-FB','58','Non-Matched Primary Prescriber','2025-03-14 17:30:12',1, '58 - Non-Matched Primary Prescriber')
,(1896,'511-FB','60','Product/Service Not Covered For Patient Age','2025-03-14 17:30:12',1, '60 - Product/Service Not Covered For Patient Age')
,(1897,'511-FB','61','Product/Service Not Covered For Patient Gender','2025-03-14 17:30:12',1, '61 - Product/Service Not Covered For Patient Gender')
,(1898,'511-FB','62','Patient/Card Holder ID Name Mismatch','2025-03-14 17:30:12',1, '62 - Patient/Card Holder ID Name Mismatch')
,(1899,'511-FB','63','Product/Service ID Not Covered For Institutionalized Patient','2025-03-14 17:30:12',1, '63 - Product/Service ID Not Covered For Institutionalized Patient')
,(1900,'511-FB','64','Claim Submitted Does Not Match Prior Authorization','2025-03-14 17:30:12',1, '64 - Claim Submitted Does Not Match Prior Authorization')
,(1901,'511-FB','65','Patient Is Not Covered','2025-03-14 17:30:12',1, '65 - Patient Is Not Covered')
,(1902,'511-FB','66','Patient Age Exceeds Maximum Age','2025-03-14 17:30:12',1, '66 - Patient Age Exceeds Maximum Age')
,(1903,'511-FB','67','Date Of Service Before Coverage Effective','2025-03-14 17:30:12',1, '67 - Date Of Service Before Coverage Effective')
,(1904,'511-FB','68','Date Of Service After Coverage Expired','2025-03-14 17:30:12',1, '68 - Date Of Service After Coverage Expired')
,(1905,'511-FB','69','Date Of Service After Coverage Terminated','2025-03-14 17:30:12',1, '69 - Date Of Service After Coverage Terminated')
,(1906,'511-FB','70','Product/Service Not Covered - Plan/Benefit Exclusion','2025-03-14 17:30:12',1, '70 - Product/Service Not Covered - Plan/Benefit Exclusion')
,(1907,'511-FB','71','Prescriber ID Is Not Covered','2025-03-14 17:30:12',1, '71 - Prescriber ID Is Not Covered')
,(1908,'511-FB','72','Primary Prescriber Is Not Covered','2025-03-14 17:30:12',1, '72 - Primary Prescriber Is Not Covered')
,(1909,'511-FB','73','Additional Fills Are Not Covered','2025-03-14 17:30:12',1, '73 - Additional Fills Are Not Covered')
,(1910,'511-FB','74','Other Carrier Payment Meets Or Exceeds Payable','2025-03-14 17:30:12',1, '74 - Other Carrier Payment Meets Or Exceeds Payable')
,(1911,'511-FB','75','Prior Authorization Required','2025-03-14 17:30:12',1, '75 - Prior Authorization Required')
,(1912,'511-FB','76','Plan Limitations Exceeded','2025-03-14 17:30:12',1, '76 - Plan Limitations Exceeded')
,(1913,'511-FB','77','Discontinued Product/Service ID Number','2025-03-14 17:30:12',1, '77 - Discontinued Product/Service ID Number')
,(1914,'511-FB','78','Cost Exceeds Maximum','2025-03-14 17:30:12',1, '78 - Cost Exceeds Maximum')
,(1915,'511-FB','79','Fill Too Soon','2025-03-14 17:30:12',1, '79 - Fill Too Soon')
,(1916,'511-FB','80','Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria','2025-03-14 17:30:12',1, '80 - Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria')
,(1917,'511-FB','81','Claim Too Old','2025-03-14 17:30:12',1, '81 - Claim Too Old')
,(1918,'511-FB','82','Claim Is Post-Dated','2025-03-14 17:30:12',1, '82 - Claim Is Post-Dated')
,(1919,'511-FB','83','Duplicate Paid/Captured Claim','2025-03-14 17:30:12',1, '83 - Duplicate Paid/Captured Claim')
,(1920,'511-FB','84','Claim Has Not Been Paid/Captured','2025-03-14 17:30:12',1, '84 - Claim Has Not Been Paid/Captured')
,(1921,'511-FB','85','Claim Not Processed','2025-03-14 17:30:12',1, '85 - Claim Not Processed')
,(1922,'511-FB','86','Submit Manual Reversal','2025-03-14 17:30:12',1, '86 - Submit Manual Reversal')
,(1923,'511-FB','87','Reversal Not Processed','2025-03-14 17:30:12',1, '87 - Reversal Not Processed')
,(1924,'511-FB','88','DUR Reject Error','2025-03-14 17:30:12',1, '88 - DUR Reject Error')
,(1925,'511-FB','89','Rejected Claim Fees Paid','2025-03-14 17:30:12',1, '89 - Rejected Claim Fees Paid')
,(1926,'511-FB','90','Host Hung Up','2025-03-14 17:30:12',1, '90 - Host Hung Up')
,(1927,'511-FB','91','Host Response Error','2025-03-14 17:30:12',1, '91 - Host Response Error')
,(1928,'511-FB','92','System Unavailable/Host Unavailable','2025-03-14 17:30:12',1, '92 - System Unavailable/Host Unavailable')
,(1929,'511-FB','95','Time Out','2025-03-14 17:30:12',1, '95 - Time Out')
,(1930,'511-FB','96','Scheduled Downtime','2025-03-14 17:30:12',1, '96 - Scheduled Downtime')
,(1931,'511-FB','97','Payer Unavailable','2025-03-14 17:30:12',1, '97 - Payer Unavailable')
,(1932,'511-FB','98','Connection To Payer Is Down','2025-03-14 17:30:12',1, '98 - Connection To Payer Is Down')
,(1933,'511-FB','99','Host Processing Error','2025-03-14 17:30:12',1, '99 - Host Processing Error')
,(1934,'511-FB','201','Patient Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '201 - Patient Segment Is Not Used For This Transaction Code')
,(1935,'511-FB','202','Insurance Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '202 - Insurance Segment Is Not Used For This Transaction Code')
,(1936,'511-FB','203','Claim Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '203 - Claim Segment Is Not Used For This Transaction Code')
,(1937,'511-FB','204','Pharmacy Provider Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '204 - Pharmacy Provider Segment Is Not Used For This Transaction Code')
,(1938,'511-FB','205','Prescriber Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '205 - Prescriber Segment Is Not Used For This Transaction Code')
,(1939,'511-FB','206','Coordination Of Benefits/Other Payments Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '206 - Coordination Of Benefits/Other Payments Segment Is Not Used For This Transaction Code')
,(1940,'511-FB','207','Workers'' Compensation Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '207 - Workers'' Compensation Segment Is Not Used For This Transaction Code')
,(1941,'511-FB','208','DUR/PPS Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '208 - DUR/PPS Segment Is Not Used For This Transaction Code')
,(1942,'511-FB','209','Pricing Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '209 - Pricing Segment Is Not Used For This Transaction Code')
,(1943,'511-FB','210','Coupon Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '210 - Coupon Segment Is Not Used For This Transaction Code')
,(1944,'511-FB','211','Compound Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '211 - Compound Segment Is Not Used For This Transaction Code')
,(1945,'511-FB','212','Prior Authorization Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '212 - Prior Authorization Segment Is Not Used For This Transaction Code')
,(1946,'511-FB','213','Clinical Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '213 - Clinical Segment Is Not Used For This Transaction Code')
,(1947,'511-FB','214','Additional Documentation Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '214 - Additional Documentation Segment Is Not Used For This Transaction Code')
,(1948,'511-FB','215','Facility Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '215 - Facility Segment Is Not Used For This Transaction Code')
,(1949,'511-FB','216','Narrative Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '216 - Narrative Segment Is Not Used For This Transaction Code')
,(1950,'511-FB','217','Purchaser Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '217 - Purchaser Segment Is Not Used For This Transaction Code')
,(1951,'511-FB','218','Service Provider Segment Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '218 - Service Provider Segment Is Not Used For This Transaction Code')
,(1952,'511-FB','219','Patient ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '219 - Patient ID Qualifier Is Not Used For This Transaction Code')
,(1953,'511-FB','220','Patient ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '220 - Patient ID Is Not Used For This Transaction Code')
,(1954,'511-FB','221','Date Of Birth Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '221 - Date Of Birth Is Not Used For This Transaction Code')
,(1955,'511-FB','222','Patient Gender Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '222 - Patient Gender Code Is Not Used For This Transaction Code')
,(1956,'511-FB','223','Patient First Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '223 - Patient First Name Is Not Used For This Transaction Code')
,(1957,'511-FB','224','Patient Last Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '224 - Patient Last Name Is Not Used For This Transaction Code')
,(1958,'511-FB','225','Patient Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '225 - Patient Street Address Is Not Used For This Transaction Code')
,(1959,'511-FB','226','Patient City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '226 - Patient City Address Is Not Used For This Transaction Code')
,(1960,'511-FB','227','Patient State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '227 - Patient State/Province Address Is Not Used For This Transaction Code')
,(1961,'511-FB','228','Patient ZIP/Postal Zone Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '228 - Patient ZIP/Postal Zone Is Not Used For This Transaction Code')
,(1962,'511-FB','229','Patient Phone Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '229 - Patient Phone Number Is Not Used For This Transaction Code')
,(1963,'511-FB','230','Place Of Service Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '230 - Place Of Service Is Not Used For This Transaction Code')
,(1964,'511-FB','231','Employer ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '231 - Employer ID Is Not Used For This Transaction Code')
,(1965,'511-FB','232','Smoker/Non-Smoker Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '232 - Smoker/Non-Smoker Code Is Not Used For This Transaction Code')
,(1966,'511-FB','233','Pregnancy Indicator Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '233 - Pregnancy Indicator Is Not Used For This Transaction Code')
,(1967,'511-FB','234','Patient E-Mail Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '234 - Patient E-Mail Address Is Not Used For This Transaction Code')
,(1968,'511-FB','235','Patient Residence Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '235 - Patient Residence Is Not Used For This Transaction Code')
,(1969,'511-FB','236','Patient ID Associated State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '236 - Patient ID Associated State/Province Address Is Not Used For This Transaction Code')
,(1970,'511-FB','237','Cardholder First Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '237 - Cardholder First Name Is Not Used For This Transaction Code')
,(1971,'511-FB','238','Cardholder Last Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '238 - Cardholder Last Name Is Not Used For This Transaction Code')
,(1972,'511-FB','239','Home Plan Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '239 - Home Plan Is Not Used For This Transaction Code')
,(1973,'511-FB','240','Plan ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '240 - Plan ID Is Not Used For This Transaction Code')
,(1974,'511-FB','241','Eligibility Clarification Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '241 - Eligibility Clarification Code Is Not Used For This Transaction Code')
,(1975,'511-FB','242','Group ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '242 - Group ID Is Not Used For This Transaction Code')
,(1976,'511-FB','243','Person Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '243 - Person Code Is Not Used For This Transaction Code')
,(1977,'511-FB','244','Patient Relationship Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '244 - Patient Relationship Code Is Not Used For This Transaction Code')
,(1978,'511-FB','245','Other Payer BIN Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '245 - Other Payer BIN Number Is Not Used For This Transaction Code')
,(1979,'511-FB','246','Other Payer Processor Control Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '246 - Other Payer Processor Control Number Is Not Used For This Transaction Code')
,(1980,'511-FB','247','Other Payer Cardholder ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '247 - Other Payer Cardholder ID Is Not Used For This Transaction Code')
,(1981,'511-FB','248','Other Payer Group ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '248 - Other Payer Group ID Is Not Used For This Transaction Code')
,(1982,'511-FB','249','Medigap ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '249 - Medigap ID Is Not Used For This Transaction Code')
,(1983,'511-FB','250','Medicaid Indicator Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '250 - Medicaid Indicator Is Not Used For This Transaction Code')
,(1984,'511-FB','251','Provider Accept Assignment Indicator Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '251 - Provider Accept Assignment Indicator Is Not Used For This Transaction Code')
,(1985,'511-FB','252','CMS Part D Defined Qualified Facility Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '252 - CMS Part D Defined Qualified Facility Is Not Used For This Transaction Code')
,(1986,'511-FB','253','Medicaid ID Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '253 - Medicaid ID Number Is Not Used For This Transaction Code')
,(1987,'511-FB','254','Medicaid Agency Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '254 - Medicaid Agency Number Is Not Used For This Transaction Code')
,(1988,'511-FB','255','Associated Prescription/Service Reference Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '255 - Associated Prescription/Service Reference Number Is Not Used For This Transaction Code')
,(1989,'511-FB','256','Associated Prescription/Service Date Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '256 - Associated Prescription/Service Date Is Not Used For This Transaction Code')
,(1990,'511-FB','257','Procedure Modifier Code Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '257 - Procedure Modifier Code Count Is Not Used For This Transaction Code')
,(1991,'511-FB','258','Procedure Modifier Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '258 - Procedure Modifier Code Is Not Used For This Transaction Code')
,(1992,'511-FB','259','Quantity Dispensed Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '259 - Quantity Dispensed Is Not Used For This Transaction Code')
,(1993,'511-FB','260','Fill # Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '260 - Fill # Is Not Used For This Transaction Code')
,(1994,'511-FB','261','Days Supply Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '261 - Days Supply Is Not Used For This Transaction Code')
,(1995,'511-FB','262','Compound Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '262 - Compound Code Is Not Used For This Transaction Code')
,(1996,'511-FB','263','Dispense As Written(DAW)/Product Selection Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '263 - Dispense As Written(DAW)/Product Selection Code Is Not Used For This Transaction Code')
,(1997,'511-FB','264','Date Prescription Written Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '264 - Date Prescription Written Is Not Used For This Transaction Code')
,(1998,'511-FB','265','Number Of Refills Authorized Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '265 - Number Of Refills Authorized Is Not Used For This Transaction Code')
,(1999,'511-FB','266','Prescription Origin Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '266 - Prescription Origin Code Is Not Used For This Transaction Code')
,(2000,'511-FB','267','Submission Clarification Code Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '267 - Submission Clarification Code Count Is Not Used For This Transaction Code')
,(2001,'511-FB','268','Submission Clarification Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '268 - Submission Clarification Code Is Not Used For This Transaction Code')
,(2002,'511-FB','269','Quantity Prescribed Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '269 - Quantity Prescribed Is Not Used For This Transaction Code')
,(2003,'511-FB','270','Other Coverage Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '270 - Other Coverage Code Is Not Used For This Transaction Code')
,(2004,'511-FB','271','Special Packaging Indicator Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '271 - Special Packaging Indicator Is Not Used For This Transaction Code')
,(2005,'511-FB','272','Originally Prescribed Product/Service ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '272 - Originally Prescribed Product/Service ID Qualifier Is Not Used For This Transaction Code')
,(2006,'511-FB','273','Originally Prescribed Product/Service Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '273 - Originally Prescribed Product/Service Code Is Not Used For This Transaction Code')
,(2007,'511-FB','274','Originally Prescribed Quantity Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '274 - Originally Prescribed Quantity Is Not Used For This Transaction Code')
,(2008,'511-FB','275','Alternate ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '275 - Alternate ID Is Not Used For This Transaction Code')
,(2009,'511-FB','276','Scheduled Prescription ID Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '276 - Scheduled Prescription ID Number Is Not Used For This Transaction Code')
,(2010,'511-FB','277','Unit Of Measure Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '277 - Unit Of Measure Is Not Used For This Transaction Code')
,(2011,'511-FB','278','Level Of Service Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '278 - Level Of Service Is Not Used For This Transaction Code')
,(2012,'511-FB','279','Prior Authorization Type Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '279 - Prior Authorization Type Code Is Not Used For This Transaction Code')
,(2013,'511-FB','280','Prior Authorization ID Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '280 - Prior Authorization ID Submitted Is Not Used For This Transaction Code')
,(2014,'511-FB','281','Intermediary Authorization Type ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '281 - Intermediary Authorization Type ID Is Not Used For This Transaction Code')
,(2015,'511-FB','282','Intermediary Authorization ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '282 - Intermediary Authorization ID Is Not Used For This Transaction Code')
,(2016,'511-FB','283','Dispensing Status Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '283 - Dispensing Status Is Not Used For This Transaction Code')
,(2017,'511-FB','284','Quantity Intended To Be Dispensed Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '284 - Quantity Intended To Be Dispensed Is Not Used For This Transaction Code')
,(2018,'511-FB','285','Days Supply Intended To Be Dispensed Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '285 - Days Supply Intended To Be Dispensed Is Not Used For This Transaction Code')
,(2019,'511-FB','286','Delay Reason Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '286 - Delay Reason Code Is Not Used For This Transaction Code')
,(2020,'511-FB','287','Transaction Reference Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '287 - Transaction Reference Number Is Not Used For This Transaction Code')
,(2021,'511-FB','288','Patient Assignment Indicator (Direct Member Reimbursement Indicator) Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '288 - Patient Assignment Indicator (Direct Member Reimbursement Indicator) Is Not Used For This Transaction Code')
,(2022,'511-FB','289','Route of Administration Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '289 - Route of Administration Is Not Used For This Transaction Code')
,(2023,'511-FB','290','Compound Type Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '290 - Compound Type Is Not Used For This Transaction Code')
,(2024,'511-FB','291','Medicaid Subrogation Internal Control Number/Transaction Control Number (ICN/TCN) Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '291 - Medicaid Subrogation Internal Control Number/Transaction Control Number (ICN/TCN) Is Not Used For This Transaction Code')
,(2025,'511-FB','292','Pharmacy Service Type Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '292 - Pharmacy Service Type Is Not Used For This Transaction Code')
,(2026,'511-FB','293','Associated Prescription/Service Provider ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '293 - Associated Prescription/Service Provider ID Qualifier Is Not Used For This Transaction Code')
,(2027,'511-FB','294','Associated Prescription/Service Provider ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '294 - Associated Prescription/Service Provider ID Is Not Used For This Transaction Code')
,(2028,'511-FB','295','Associated Prescription/Service Reference Number Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '295 - Associated Prescription/Service Reference Number Qualifier Is Not Used For This Transaction Code')
,(2029,'511-FB','296','Associated Prescription/Service Reference Fill # Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '296 - Associated Prescription/Service Reference Fill # Is Not Used For This Transaction Code')
,(2030,'511-FB','297','Time of Service Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '297 - Time of Service Is Not Used For This Transaction Code')
,(2031,'511-FB','298','Sales Transaction ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '298 - Sales Transaction ID Is Not Used For This Transaction Code')
,(2032,'511-FB','299','Reported Adjudicated Program Type Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '299 - Reported Adjudicated Program Type Is Not Used For This Transaction Code')
,(2033,'511-FB','300','Provider ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '300 - Provider ID Qualifier Is Not Used For This Transaction Code')
,(2034,'511-FB','301','Provider ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '301 - Provider ID Is Not Used For This Transaction Code')
,(2035,'511-FB','302','Prescriber ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '302 - Prescriber ID Qualifier Is Not Used For This Transaction Code')
,(2036,'511-FB','303','Prescriber ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '303 - Prescriber ID Is Not Used For This Transaction Code')
,(2037,'511-FB','304','Prescriber ID Associated State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '304 - Prescriber ID Associated State/Province Address Is Not Used For This Transaction Code')
,(2038,'511-FB','305','Prescriber Last Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '305 - Prescriber Last Name Is Not Used For This Transaction Code')
,(2039,'511-FB','306','Prescriber Phone Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '306 - Prescriber Phone Number Is Not Used For This Transaction Code')
,(2040,'511-FB','307','Primary Care Provider ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '307 - Primary Care Provider ID Qualifier Is Not Used For This Transaction Code')
,(2041,'511-FB','308','Primary Care Provider ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '308 - Primary Care Provider ID Is Not Used For This Transaction Code')
,(2042,'511-FB','309','Primary Care Provider Last Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '309 - Primary Care Provider Last Name Is Not Used For This Transaction Code')
,(2043,'511-FB','310','Prescriber First Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '310 - Prescriber First Name Is Not Used For This Transaction Code')
,(2044,'511-FB','311','Prescriber Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '311 - Prescriber Street Address Is Not Used For This Transaction Code')
,(2045,'511-FB','312','Prescriber City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '312 - Prescriber City Address Is Not Used For This Transaction Code')
,(2046,'511-FB','313','Prescriber State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '313 - Prescriber State/Province Address Is Not Used For This Transaction Code')
,(2047,'511-FB','314','Prescriber ZIP/Postal Zone Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '314 - Prescriber ZIP/Postal Zone Is Not Used For This Transaction Code')
,(2048,'511-FB','315','Prescriber Alternate ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '315 - Prescriber Alternate ID Qualifier Is Not Used For This Transaction Code')
,(2049,'511-FB','316','Prescriber Alternate ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '316 - Prescriber Alternate ID Is Not Used For This Transaction Code')
,(2050,'511-FB','317','Prescriber Alternate ID Associated State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '317 - Prescriber Alternate ID Associated State/Province Address Is Not Used For This Transaction Code')
,(2051,'511-FB','318','Other Payer ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '318 - Other Payer ID Qualifier Is Not Used For This Transaction Code')
,(2052,'511-FB','319','Other Payer ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '319 - Other Payer ID Is Not Used For This Transaction Code')
,(2053,'511-FB','320','Other Payer Date Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '320 - Other Payer Date Is Not Used For This Transaction Code')
,(2054,'511-FB','321','Internal Control Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '321 - Internal Control Number Is Not Used For This Transaction Code')
,(2055,'511-FB','322','Other Payer Amount Paid Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '322 - Other Payer Amount Paid Count Is Not Used For This Transaction Code')
,(2056,'511-FB','323','Other Payer Amount Paid Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '323 - Other Payer Amount Paid Qualifier Is Not Used For This Transaction Code')
,(2057,'511-FB','324','Other Payer Amount Paid Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '324 - Other Payer Amount Paid Is Not Used For This Transaction Code')
,(2058,'511-FB','325','Other Payer Reject Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '325 - Other Payer Reject Count Is Not Used For This Transaction Code')
,(2059,'511-FB','326','Other Payer Reject Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '326 - Other Payer Reject Code Is Not Used For This Transaction Code')
,(2060,'511-FB','327','Other Payer-Patient Responsibility Amount Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '327 - Other Payer-Patient Responsibility Amount Count Is Not Used For This Transaction Code')
,(2061,'511-FB','328','Other Payer-Patient Responsibility Amount Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '328 - Other Payer-Patient Responsibility Amount Qualifier Is Not Used For This Transaction Code')
,(2062,'511-FB','329','Other Payer-Patient Responsibility Amount Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '329 - Other Payer-Patient Responsibility Amount Is Not Used For This Transaction Code')
,(2063,'511-FB','330','Benefit Stage Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '330 - Benefit Stage Count Is Not Used For This Transaction Code')
,(2064,'511-FB','331','Benefit Stage Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '331 - Benefit Stage Qualifier Is Not Used For This Transaction Code')
,(2065,'511-FB','332','Benefit Stage Amount Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '332 - Benefit Stage Amount Is Not Used For This Transaction Code')
,(2066,'511-FB','333','Employer Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '333 - Employer Name Is Not Used For This Transaction Code')
,(2067,'511-FB','334','Employer Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '334 - Employer Street Address Is Not Used For This Transaction Code')
,(2068,'511-FB','335','Employer City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '335 - Employer City Address Is Not Used For This Transaction Code')
,(2069,'511-FB','336','Employer State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '336 - Employer State/Province Address Is Not Used For This Transaction Code')
,(2070,'511-FB','337','Employer ZIP/Postal Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '337 - Employer ZIP/Postal Code Is Not Used For This Transaction Code')
,(2071,'511-FB','338','Employer Phone Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '338 - Employer Phone Number Is Not Used For This Transaction Code')
,(2072,'511-FB','339','Employer Contact Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '339 - Employer Contact Name Is Not Used For This Transaction Code')
,(2073,'511-FB','340','Carrier ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '340 - Carrier ID Is Not Used For This Transaction Code')
,(2074,'511-FB','341','Claim/Reference ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '341 - Claim/Reference ID Is Not Used For This Transaction Code')
,(2075,'511-FB','342','Billing Entity Type Indicator Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '342 - Billing Entity Type Indicator Is Not Used For This Transaction Code')
,(2076,'511-FB','343','Pay To Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '343 - Pay To Qualifier Is Not Used For This Transaction Code')
,(2077,'511-FB','344','Pay To ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '344 - Pay To ID Is Not Used For This Transaction Code')
,(2078,'511-FB','345','Pay To Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '345 - Pay To Name Is Not Used For This Transaction Code')
,(2079,'511-FB','346','Pay To Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '346 - Pay To Street Address Is Not Used For This Transaction Code')
,(2080,'511-FB','347','Pay To City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '347 - Pay To City Address Is Not Used For This Transaction Code')
,(2081,'511-FB','348','Pay To State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '348 - Pay To State/Province Address Is Not Used For This Transaction Code')
,(2082,'511-FB','349','Pay To ZIP/Postal Zone Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '349 - Pay To ZIP/Postal Zone Is Not Used For This Transaction Code')
,(2083,'511-FB','350','Generic Equivalent Product ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '350 - Generic Equivalent Product ID Qualifier Is Not Used For This Transaction Code')
,(2084,'511-FB','351','Generic Equivalent Product ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '351 - Generic Equivalent Product ID Is Not Used For This Transaction Code')
,(2085,'511-FB','352','DUR/PPS Code Counter Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '352 - DUR/PPS Code Counter Is Not Used For This Transaction Code')
,(2086,'511-FB','353','Reason For Service Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '353 - Reason For Service Code Is Not Used For This Transaction Code')
,(2087,'511-FB','354','Professional Service Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '354 - Professional Service Code Is Not Used For This Transaction Code')
,(2088,'511-FB','355','Result Of Service Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '355 - Result Of Service Code Is Not Used For This Transaction Code')
,(2089,'511-FB','356','DUR/PPS Level Of Effort Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '356 - DUR/PPS Level Of Effort Is Not Used For This Transaction Code')
,(2090,'511-FB','357','DUR Co-Agent ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '357 - DUR Co-Agent ID Qualifier Is Not Used For This Transaction Code')
,(2091,'511-FB','358','DUR Co-Agent ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '358 - DUR Co-Agent ID Is Not Used For This Transaction Code')
,(2092,'511-FB','359','Ingredient Cost Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '359 - Ingredient Cost Submitted Is Not Used For This Transaction Code')
,(2093,'511-FB','360','Dispensing Fee Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '360 - Dispensing Fee Submitted Is Not Used For This Transaction Code')
,(2094,'511-FB','361','Professional Service Fee Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '361 - Professional Service Fee Submitted Is Not Used For This Transaction Code')
,(2095,'511-FB','362','Patient Paid Amount Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '362 - Patient Paid Amount Submitted Is Not Used For This Transaction Code')
,(2096,'511-FB','363','Incentive Amount Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '363 - Incentive Amount Submitted Is Not Used For This Transaction Code')
,(2097,'511-FB','364','Other Amount Claimed Submitted Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '364 - Other Amount Claimed Submitted Count Is Not Used For This Transaction Code')
,(2098,'511-FB','365','Other Amount Claimed Submitted Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '365 - Other Amount Claimed Submitted Qualifier Is Not Used For This Transaction Code')
,(2099,'511-FB','366','Other Amount Claimed Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '366 - Other Amount Claimed Submitted Is Not Used For This Transaction Code')
,(2100,'511-FB','367','Flat Sales Tax Amount Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '367 - Flat Sales Tax Amount Submitted Is Not Used For This Transaction Code')
,(2101,'511-FB','368','Percentage Sales Tax Amount Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '368 - Percentage Sales Tax Amount Submitted Is Not Used For This Transaction Code')
,(2102,'511-FB','369','Percentage Sales Tax Rate Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '369 - Percentage Sales Tax Rate Submitted Is Not Used For This Transaction Code')
,(2103,'511-FB','370','Percentage Sales Tax Basis Submitted Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '370 - Percentage Sales Tax Basis Submitted Is Not Used For This Transaction Code')
,(2104,'511-FB','371','Usual And Customary Charge Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '371 - Usual And Customary Charge Is Not Used For This Transaction Code')
,(2105,'511-FB','372','Gross Amount Due Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '372 - Gross Amount Due Is Not Used For This Transaction Code')
,(2106,'511-FB','373','Basis Of Cost Determination Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '373 - Basis Of Cost Determination Is Not Used For This Transaction Code')
,(2107,'511-FB','374','Medicaid Paid Amount Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '374 - Medicaid Paid Amount Is Not Used For This Transaction Code')
,(2108,'511-FB','375','Coupon Value Amount Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '375 - Coupon Value Amount Is Not Used For This Transaction Code')
,(2109,'511-FB','376','Compound Ingredient Drug Cost Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '376 - Compound Ingredient Drug Cost Is Not Used For This Transaction Code')
,(2110,'511-FB','377','Compound Ingredient Basis Of Cost Determination Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '377 - Compound Ingredient Basis Of Cost Determination Is Not Used For This Transaction Code')
,(2111,'511-FB','378','Compound Ingredient Modifier Code Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '378 - Compound Ingredient Modifier Code Count Is Not Used For This Transaction Code')
,(2112,'511-FB','379','Compound Ingredient Modifier Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '379 - Compound Ingredient Modifier Code Is Not Used For This Transaction Code')
,(2113,'511-FB','380','Authorized Representative First Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '380 - Authorized Representative First Name Is Not Used For This Transaction Code')
,(2114,'511-FB','381','Authorized Rep. Last Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '381 - Authorized Rep. Last Name Is Not Used For This Transaction Code')
,(2115,'511-FB','382','Authorized Rep. Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '382 - Authorized Rep. Street Address Is Not Used For This Transaction Code')
,(2116,'511-FB','383','Authorized Rep. City Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '383 - Authorized Rep. City Is Not Used For This Transaction Code')
,(2117,'511-FB','384','Authorized Rep. State/Province Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '384 - Authorized Rep. State/Province Is Not Used For This Transaction Code')
,(2118,'511-FB','385','Authorized Rep. ZIP/Postal Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '385 - Authorized Rep. ZIP/Postal Code Is Not Used For This Transaction Code')
,(2119,'511-FB','386','Prior Authorization ID Assigned Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '386 - Prior Authorization ID Assigned Is Not Used For This Transaction Code')
,(2120,'511-FB','387','Authorization Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '387 - Authorization Number Is Not Used For This Transaction Code')
,(2121,'511-FB','388','Prior Authorization Supporting Documentation Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '388 - Prior Authorization Supporting Documentation Is Not Used For This Transaction Code')
,(2122,'511-FB','389','Diagnosis Code Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '389 - Diagnosis Code Count Is Not Used For This Transaction Code')
,(2123,'511-FB','390','Diagnosis Code Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '390 - Diagnosis Code Qualifier Is Not Used For This Transaction Code')
,(2124,'511-FB','391','Diagnosis Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '391 - Diagnosis Code Is Not Used For This Transaction Code')
,(2125,'511-FB','392','Clinical Information Counter Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '392 - Clinical Information Counter Is Not Used For This Transaction Code')
,(2126,'511-FB','393','Measurement Date Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '393 - Measurement Date Is Not Used For This Transaction Code')
,(2127,'511-FB','394','Measurement Time Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '394 - Measurement Time Is Not Used For This Transaction Code')
,(2128,'511-FB','395','Measurement Dimension Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '395 - Measurement Dimension Is Not Used For This Transaction Code')
,(2129,'511-FB','396','Measurement Unit Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '396 - Measurement Unit Is Not Used For This Transaction Code')
,(2130,'511-FB','397','Measurement Value Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '397 - Measurement Value Is Not Used For This Transaction Code')
,(2131,'511-FB','398','Request Period Begin Date Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '398 - Request Period Begin Date Is Not Used For This Transaction Code')
,(2132,'511-FB','399','Request Period Recert/Revised Date Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '399 - Request Period Recert/Revised Date Is Not Used For This Transaction Code')
,(2133,'511-FB','400','Request Status Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '400 - Request Status Is Not Used For This Transaction Code')
,(2134,'511-FB','401','Length Of Need Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '401 - Length Of Need Qualifier Is Not Used For This Transaction Code')
,(2135,'511-FB','402','Length Of Need Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '402 - Length Of Need Is Not Used For This Transaction Code')
,(2136,'511-FB','403','Prescriber/Supplier Date Signed Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '403 - Prescriber/Supplier Date Signed Is Not Used For This Transaction Code')
,(2137,'511-FB','404','Supporting Documentation Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '404 - Supporting Documentation Is Not Used For This Transaction Code')
,(2138,'511-FB','405','Question Number/Letter Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '405 - Question Number/Letter Count Is Not Used For This Transaction Code')
,(2139,'511-FB','406','Question Number/Letter Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '406 - Question Number/Letter Is Not Used For This Transaction Code')
,(2140,'511-FB','407','Question Percent Response Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '407 - Question Percent Response Is Not Used For This Transaction Code')
,(2141,'511-FB','408','Question Date Response Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '408 - Question Date Response Is Not Used For This Transaction Code')
,(2142,'511-FB','409','Question Dollar Amount Response Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '409 - Question Dollar Amount Response Is Not Used For This Transaction Code')
,(2143,'511-FB','410','Question Numeric Response Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '410 - Question Numeric Response Is Not Used For This Transaction Code')
,(2144,'511-FB','411','Question Alphanumeric Response Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '411 - Question Alphanumeric Response Is Not Used For This Transaction Code')
,(2145,'511-FB','412','Facility ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '412 - Facility ID Is Not Used For This Transaction Code')
,(2146,'511-FB','413','Facility Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '413 - Facility Name Is Not Used For This Transaction Code')
,(2147,'511-FB','414','Facility Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '414 - Facility Street Address Is Not Used For This Transaction Code')
,(2148,'511-FB','415','Facility City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '415 - Facility City Address Is Not Used For This Transaction Code')
,(2149,'511-FB','416','Facility State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '416 - Facility State/Province Address Is Not Used For This Transaction Code')
,(2150,'511-FB','417','Facility ZIP/Postal Zone Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '417 - Facility ZIP/Postal Zone Is Not Used For This Transaction Code')
,(2151,'511-FB','418','Purchaser ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '418 - Purchaser ID Qualifier Is Not Used For This Transaction Code')
,(2152,'511-FB','419','Purchaser ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '419 - Purchaser ID Is Not Used For This Transaction Code')
,(2153,'511-FB','420','Purchaser ID Associated State Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '420 - Purchaser ID Associated State Code Is Not Used For This Transaction Code')
,(2154,'511-FB','421','Purchaser Date Of Birth Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '421 - Purchaser Date Of Birth Is Not Used For This Transaction Code')
,(2155,'511-FB','422','Purchaser Gender Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '422 - Purchaser Gender Code Is Not Used For This Transaction Code')
,(2156,'511-FB','423','Purchaser First Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '423 - Purchaser First Name Is Not Used For This Transaction Code')
,(2157,'511-FB','424','Purchaser Last Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '424 - Purchaser Last Name Is Not Used For This Transaction Code')
,(2158,'511-FB','425','Purchaser Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '425 - Purchaser Street Address Is Not Used For This Transaction Code')
,(2159,'511-FB','426','Purchaser City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '426 - Purchaser City Address Is Not Used For This Transaction Code')
,(2160,'511-FB','427','Purchaser State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '427 - Purchaser State/Province Address Is Not Used For This Transaction Code')
,(2161,'511-FB','428','Purchaser ZIP/Postal Zone Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '428 - Purchaser ZIP/Postal Zone Is Not Used For This Transaction Code')
,(2162,'511-FB','429','Purchaser Country Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '429 - Purchaser Country Code Is Not Used For This Transaction Code')
,(2163,'511-FB','430','Purchaser Relationship Code Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '430 - Purchaser Relationship Code Is Not Used For This Transaction Code')
,(2164,'511-FB','431','Released Date Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '431 - Released Date Is Not Used For This Transaction Code')
,(2165,'511-FB','432','Released Time Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '432 - Released Time Is Not Used For This Transaction Code')
,(2166,'511-FB','433','Service Provider Name Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '433 - Service Provider Name Is Not Used For This Transaction Code')
,(2167,'511-FB','434','Service Provider Street Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '434 - Service Provider Street Address Is Not Used For This Transaction Code')
,(2168,'511-FB','435','Service Provider City Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '435 - Service Provider City Address Is Not Used For This Transaction Code')
,(2169,'511-FB','436','Service Provider State/Province Address Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '436 - Service Provider State/Province Address Is Not Used For This Transaction Code')
,(2170,'511-FB','437','Service Provider ZIP/Postal Zone Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '437 - Service Provider ZIP/Postal Zone Is Not Used For This Transaction Code')
,(2171,'511-FB','438','Seller ID Qualifier Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '438 - Seller ID Qualifier Is Not Used For This Transaction Code')
,(2172,'511-FB','439','Seller ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '439 - Seller ID Is Not Used For This Transaction Code')
,(2173,'511-FB','440','Seller Initials Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '440 - Seller Initials Is Not Used For This Transaction Code')
,(2174,'511-FB','441','Other Amount Claimed Submitted Grouping Incorrect','2025-03-14 17:30:12',1, '441 - Other Amount Claimed Submitted Grouping Incorrect')
,(2175,'511-FB','442','Other Payer Amount Paid Grouping Incorrect','2025-03-14 17:30:12',1, '442 - Other Payer Amount Paid Grouping Incorrect')
,(2176,'511-FB','443','Other Payer-Patient Responsibility Amount Grouping Incorrect','2025-03-14 17:30:12',1, '443 - Other Payer-Patient Responsibility Amount Grouping Incorrect')
,(2177,'511-FB','444','Benefit Stage Amount Grouping Incorrect','2025-03-14 17:30:12',1, '444 - Benefit Stage Amount Grouping Incorrect')
,(2178,'511-FB','445','Diagnosis Code Grouping Incorrect','2025-03-14 17:30:12',1, '445 - Diagnosis Code Grouping Incorrect')
,(2179,'511-FB','446','COB/Other Payments Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '446 - COB/Other Payments Segment Incorrectly Formatted')
,(2180,'511-FB','447','Additional Documentation Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '447 - Additional Documentation Segment Incorrectly Formatted')
,(2181,'511-FB','448','Clinical Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '448 - Clinical Segment Incorrectly Formatted')
,(2182,'511-FB','449','Patient Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '449 - Patient Segment Incorrectly Formatted')
,(2183,'511-FB','450','Insurance Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '450 - Insurance Segment Incorrectly Formatted')
,(2184,'511-FB','451','Transaction Header Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '451 - Transaction Header Segment Incorrectly Formatted')
,(2185,'511-FB','452','Claim Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '452 - Claim Segment Incorrectly Formatted')
,(2186,'511-FB','453','Pharmacy Provider Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '453 - Pharmacy Provider Segment Incorrectly Formatted')
,(2187,'511-FB','454','Prescriber Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '454 - Prescriber Segment Incorrectly Formatted')
,(2188,'511-FB','455','Workers'' Compensation Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '455 - Workers'' Compensation Segment Incorrectly Formatted')
,(2189,'511-FB','456','Pricing Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '456 - Pricing Segment Incorrectly Formatted')
,(2190,'511-FB','457','Coupon Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '457 - Coupon Segment Incorrectly Formatted')
,(2191,'511-FB','458','Prior Authorization Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '458 - Prior Authorization Segment Incorrectly Formatted')
,(2192,'511-FB','459','Facility Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '459 - Facility Segment Incorrectly Formatted')
,(2193,'511-FB','460','Narrative Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '460 - Narrative Segment Incorrectly Formatted')
,(2194,'511-FB','461','Purchaser Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '461 - Purchaser Segment Incorrectly Formatted')
,(2195,'511-FB','462','Service Provider Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '462 - Service Provider Segment Incorrectly Formatted')
,(2196,'511-FB','463','Pharmacy Not Contracted In Assisted Living Network','2025-03-14 17:30:12',1, '463 - Pharmacy Not Contracted In Assisted Living Network')
,(2197,'511-FB','464','Service Provider ID Qualifier Does Not Precede Service Provider ID','2025-03-14 17:30:12',1, '464 - Service Provider ID Qualifier Does Not Precede Service Provider ID')
,(2198,'511-FB','465','Patient ID Qualifier Does Not Precede Patient ID','2025-03-14 17:30:12',1, '465 - Patient ID Qualifier Does Not Precede Patient ID')
,(2199,'511-FB','466','Prescription/Service Reference Number Qualifier Does Not Precede Prescription/Service Reference Number','2025-03-14 17:30:12',1, '466 - Prescription/Service Reference Number Qualifier Does Not Precede Prescription/Service Reference Number')
,(2200,'511-FB','467','Product/Service ID Qualifier Does Not Precede Product/Service ID','2025-03-14 17:30:12',1, '467 - Product/Service ID Qualifier Does Not Precede Product/Service ID')
,(2201,'511-FB','468','Procedure Modifier Code Count Does Not Precede Procedure Modifier Code','2025-03-14 17:30:12',1, '468 - Procedure Modifier Code Count Does Not Precede Procedure Modifier Code')
,(2202,'511-FB','469','Submission Clarification Code Count Does Not Precede Submission Clarification Code','2025-03-14 17:30:12',1, '469 - Submission Clarification Code Count Does Not Precede Submission Clarification Code')
,(2203,'511-FB','470','Originally Prescribed Product/Service ID Qualifier Does Not Precede Originally Prescribed Product/Service Code','2025-03-14 17:30:12',1, '470 - Originally Prescribed Product/Service ID Qualifier Does Not Precede Originally Prescribed Product/Service Code')
,(2204,'511-FB','471','Other Amount Claimed Submitted Count Does Not Precede Other Amount Claimed Amount And/Or Qualifier','2025-03-14 17:30:12',1, '471 - Other Amount Claimed Submitted Count Does Not Precede Other Amount Claimed Amount And/Or Qualifier')
,(2205,'511-FB','472','Other Amount Claimed Submitted Qualifier Does Not Precede Other Amount Claimed Submitted','2025-03-14 17:30:12',1, '472 - Other Amount Claimed Submitted Qualifier Does Not Precede Other Amount Claimed Submitted')
,(2206,'511-FB','473','Provider ID Qualifier Does Not Precede Provider ID','2025-03-14 17:30:12',1, '473 - Provider ID Qualifier Does Not Precede Provider ID')
,(2207,'511-FB','474','Prescriber ID Qualifier  Does Not Precede Prescriber ID','2025-03-14 17:30:12',1, '474 - Prescriber ID Qualifier  Does Not Precede Prescriber ID')
,(2208,'511-FB','475','Primary Care Provider ID Qualifier Does Not Precede Primary Care Provider ID','2025-03-14 17:30:12',1, '475 - Primary Care Provider ID Qualifier Does Not Precede Primary Care Provider ID')
,(2209,'511-FB','476','Coordination Of Benefits/Other Payments Count Does Not Precede Other Payer Coverage Type','2025-03-14 17:30:12',1, '476 - Coordination Of Benefits/Other Payments Count Does Not Precede Other Payer Coverage Type')
,(2210,'511-FB','477','Other Payer ID Count Does Not Precede Other Payer ID Data Fields','2025-03-14 17:30:12',1, '477 - Other Payer ID Count Does Not Precede Other Payer ID Data Fields')
,(2211,'511-FB','478','Other Payer ID Qualifier Does Not Precede Other Payer ID','2025-03-14 17:30:12',1, '478 - Other Payer ID Qualifier Does Not Precede Other Payer ID')
,(2212,'511-FB','479','Other Payer Amount Paid Count Does Not Precede Other Payer Amount Paid And/Or Qualifier','2025-03-14 17:30:12',1, '479 - Other Payer Amount Paid Count Does Not Precede Other Payer Amount Paid And/Or Qualifier')
,(2213,'511-FB','480','Other Payer Amount Paid Qualifier Does Not Precede Other Payer Amount Paid','2025-03-14 17:30:12',1, '480 - Other Payer Amount Paid Qualifier Does Not Precede Other Payer Amount Paid')
,(2214,'511-FB','481','Other Payer Reject Count Does Not Precede Other Payer Reject Code','2025-03-14 17:30:12',1, '481 - Other Payer Reject Count Does Not Precede Other Payer Reject Code')
,(2215,'511-FB','482','Other Payer-Patient Responsibility Amount Count Does Not Precede Other Payer-Patient Responsibility Amount and/or Qualifier','2025-03-14 17:30:12',1, '482 - Other Payer-Patient Responsibility Amount Count Does Not Precede Other Payer-Patient Responsibility Amount and/or Qualifier')
,(2216,'511-FB','483','Other Payer-Patient Responsibility Amount Qualifier Does Not Precede Other Payer-Patient Responsibility Amount','2025-03-14 17:30:12',1, '483 - Other Payer-Patient Responsibility Amount Qualifier Does Not Precede Other Payer-Patient Responsibility Amount')
,(2217,'511-FB','484','Benefit Stage Count Does Not Precede Benefit Stage Amount and/or Qualifier','2025-03-14 17:30:12',1, '484 - Benefit Stage Count Does Not Precede Benefit Stage Amount and/or Qualifier')
,(2218,'511-FB','485','Benefit Stage Qualifier Does Not Precede Benefit Stage Amount','2025-03-14 17:30:12',1, '485 - Benefit Stage Qualifier Does Not Precede Benefit Stage Amount')
,(2219,'511-FB','486','Pay To Qualifier Does Not Precede Pay To ID','2025-03-14 17:30:12',1, '486 - Pay To Qualifier Does Not Precede Pay To ID')
,(2220,'511-FB','487','Generic Equivalent Product ID Qualifier Does Not Precede Generic Equivalent Product ID','2025-03-14 17:30:12',1, '487 - Generic Equivalent Product ID Qualifier Does Not Precede Generic Equivalent Product ID')
,(2221,'511-FB','488','DUR/PPS Code Counter Does Not Precede DUR Data Fields','2025-03-14 17:30:12',1, '488 - DUR/PPS Code Counter Does Not Precede DUR Data Fields')
,(2222,'511-FB','489','DUR Co-Agent ID Qualifier Does Not Precede DUR Co-Agent ID','2025-03-14 17:30:12',1, '489 - DUR Co-Agent ID Qualifier Does Not Precede DUR Co-Agent ID')
,(2223,'511-FB','490','Compound Ingredient Component Count Does Not Precede Compound Product ID And/Or Qualifier','2025-03-14 17:30:12',1, '490 - Compound Ingredient Component Count Does Not Precede Compound Product ID And/Or Qualifier')
,(2224,'511-FB','491','Compound Product ID Qualifier  Does Not Precede Compound Product ID','2025-03-14 17:30:12',1, '491 - Compound Product ID Qualifier  Does Not Precede Compound Product ID')
,(2225,'511-FB','492','Compound Ingredient Modifier Code Count Does Not Precede Compound Ingredient Modifier Code','2025-03-14 17:30:12',1, '492 - Compound Ingredient Modifier Code Count Does Not Precede Compound Ingredient Modifier Code')
,(2226,'511-FB','493','Diagnosis Code Count Does Not Precede Diagnosis Code And/Or Qualifier','2025-03-14 17:30:12',1, '493 - Diagnosis Code Count Does Not Precede Diagnosis Code And/Or Qualifier')
,(2227,'511-FB','494','Diagnosis Code Qualifier Does Not Precede Diagnosis Code','2025-03-14 17:30:12',1, '494 - Diagnosis Code Qualifier Does Not Precede Diagnosis Code')
,(2228,'511-FB','495','Clinical Information Counter Does Not Precede Clinical Measurement Data','2025-03-14 17:30:12',1, '495 - Clinical Information Counter Does Not Precede Clinical Measurement Data')
,(2229,'511-FB','496','Length Of Need Qualifier Does Not Precede Length Of Need','2025-03-14 17:30:12',1, '496 - Length Of Need Qualifier Does Not Precede Length Of Need')
,(2230,'511-FB','497','Question Number/Letter Count Does Not Precede Question Number/Letter','2025-03-14 17:30:12',1, '497 - Question Number/Letter Count Does Not Precede Question Number/Letter')
,(2231,'511-FB','498','Accumulator Month Count Does Not Precede Accumulator Month','2025-03-14 17:30:12',1, '498 - Accumulator Month Count Does Not Precede Accumulator Month')
,(2232,'511-FB','504','Benefit Stage Qualifier Value Not Supported','2025-03-14 17:30:12',1, '504 - Benefit Stage Qualifier Value Not Supported')
,(2233,'511-FB','505','Other Payer Coverage Type Value Not Supported','2025-03-14 17:30:12',1, '505 - Other Payer Coverage Type Value Not Supported')
,(2234,'511-FB','506','Prescription/Service Reference Number Qualifier Value Not Supported','2025-03-14 17:30:12',1, '506 - Prescription/Service Reference Number Qualifier Value Not Supported')
,(2235,'511-FB','507','Additional Documentation Type ID Value Not Supported','2025-03-14 17:30:12',1, '507 - Additional Documentation Type ID Value Not Supported')
,(2236,'511-FB','508','Authorized Representative State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '508 - Authorized Representative State/Province Address Value Not Supported')
,(2237,'511-FB','509','Basis Of Request Value Not Supported','2025-03-14 17:30:12',1, '509 - Basis Of Request Value Not Supported')
,(2238,'511-FB','510','Billing Entity Type Indicator Value Not Supported','2025-03-14 17:30:12',1, '510 - Billing Entity Type Indicator Value Not Supported')
,(2239,'511-FB','511','CMS Part D Defined Qualified Facility Value Not Supported','2025-03-14 17:30:12',1, '511 - CMS Part D Defined Qualified Facility Value Not Supported')
,(2240,'511-FB','512','Compound Code Value Not Supported','2025-03-14 17:30:12',1, '512 - Compound Code Value Not Supported')
,(2241,'511-FB','513','Compound Dispensing Unit Form Indicator Value Not Supported','2025-03-14 17:30:12',1, '513 - Compound Dispensing Unit Form Indicator Value Not Supported')
,(2242,'511-FB','514','Compound Ingredient Basis Of Cost Determination Value Not Supported','2025-03-14 17:30:12',1, '514 - Compound Ingredient Basis Of Cost Determination Value Not Supported')
,(2243,'511-FB','515','Compound Product ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '515 - Compound Product ID Qualifier Value Not Supported')
,(2244,'511-FB','516','Compound Type Value Not Supported','2025-03-14 17:30:12',1, '516 - Compound Type Value Not Supported')
,(2245,'511-FB','517','Coupon Type Value Not Supported','2025-03-14 17:30:12',1, '517 - Coupon Type Value Not Supported')
,(2246,'511-FB','518','DUR Co-Agent ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '518 - DUR Co-Agent ID Qualifier Value Not Supported')
,(2247,'511-FB','519','DUR/PPS Level Of Effort Value Not Supported','2025-03-14 17:30:12',1, '519 - DUR/PPS Level Of Effort Value Not Supported')
,(2248,'511-FB','520','Delay Reason Code Value Not Supported','2025-03-14 17:30:12',1, '520 - Delay Reason Code Value Not Supported')
,(2249,'511-FB','521','Diagnosis Code Qualifier Value Not Supported','2025-03-14 17:30:12',1, '521 - Diagnosis Code Qualifier Value Not Supported')
,(2250,'511-FB','522','Dispensing Status Value Not Supported','2025-03-14 17:30:12',1, '522 - Dispensing Status Value Not Supported')
,(2251,'511-FB','523','Eligibility Clarification Code Value Not Supported','2025-03-14 17:30:12',1, '523 - Eligibility Clarification Code Value Not Supported')
,(2252,'511-FB','524','Employer State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '524 - Employer State/Province Address Value Not Supported')
,(2253,'511-FB','525','Facility State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '525 - Facility State/Province Address Value Not Supported')
,(2254,'511-FB','526','Header Response Status Value Not Supported','2025-03-14 17:30:12',1, '526 - Header Response Status Value Not Supported')
,(2255,'511-FB','527','Intermediary Authorization Type ID Value Not Supported','2025-03-14 17:30:12',1, '527 - Intermediary Authorization Type ID Value Not Supported')
,(2256,'511-FB','528','Length of Need Qualifier Value Not Supported','2025-03-14 17:30:12',1, '528 - Length of Need Qualifier Value Not Supported')
,(2257,'511-FB','529','Level Of Service Value Not Supported','2025-03-14 17:30:12',1, '529 - Level Of Service Value Not Supported')
,(2258,'511-FB','530','Measurement Dimension Value Not Supported','2025-03-14 17:30:12',1, '530 - Measurement Dimension Value Not Supported')
,(2259,'511-FB','531','Measurement Unit Value Not Supported','2025-03-14 17:30:12',1, '531 - Measurement Unit Value Not Supported')
,(2260,'511-FB','532','Medicaid Indicator Value Not Supported','2025-03-14 17:30:12',1, '532 - Medicaid Indicator Value Not Supported')
,(2261,'511-FB','533','Originally Prescribed Product/Service ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '533 - Originally Prescribed Product/Service ID Qualifier Value Not Supported')
,(2262,'511-FB','534','Other Amount Claimed Submitted Qualifier Value Not Supported','2025-03-14 17:30:12',1, '534 - Other Amount Claimed Submitted Qualifier Value Not Supported')
,(2263,'511-FB','535','Other Coverage Code Value Not Supported','2025-03-14 17:30:12',1, '535 - Other Coverage Code Value Not Supported')
,(2264,'511-FB','536','Other Payer-Patient Responsibility Amount Qualifier Value Not Supported','2025-03-14 17:30:12',1, '536 - Other Payer-Patient Responsibility Amount Qualifier Value Not Supported')
,(2265,'511-FB','537','Patient Assignment Indicator (Direct Member Reimbursement Indicator) Value Not Supported','2025-03-14 17:30:12',1, '537 - Patient Assignment Indicator (Direct Member Reimbursement Indicator) Value Not Supported')
,(2266,'511-FB','538','Patient Gender Code Value Not Supported','2025-03-14 17:30:12',1, '538 - Patient Gender Code Value Not Supported')
,(2267,'511-FB','539','Patient State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '539 - Patient State/Province Address Value Not Supported')
,(2268,'511-FB','540','Pay to State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '540 - Pay to State/Province Address Value Not Supported')
,(2269,'511-FB','541','Percentage Sales Tax Basis Submitted Value Not Supported','2025-03-14 17:30:12',1, '541 - Percentage Sales Tax Basis Submitted Value Not Supported')
,(2270,'511-FB','542','Pregnancy Indicator Value Not Supported','2025-03-14 17:30:12',1, '542 - Pregnancy Indicator Value Not Supported')
,(2271,'511-FB','543','Prescriber ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '543 - Prescriber ID Qualifier Value Not Supported')
,(2272,'511-FB','544','Prescriber State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '544 - Prescriber State/Province Address Value Not Supported')
,(2273,'511-FB','545','Prescription Origin Code Value Not Supported','2025-03-14 17:30:12',1, '545 - Prescription Origin Code Value Not Supported')
,(2274,'511-FB','546','Primary Care Provider ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '546 - Primary Care Provider ID Qualifier Value Not Supported')
,(2275,'511-FB','547','Prior Authorization Type Code Value Not Supported','2025-03-14 17:30:12',1, '547 - Prior Authorization Type Code Value Not Supported')
,(2276,'511-FB','548','Provider Accept Assignment Indicator Value Not Supported','2025-03-14 17:30:12',1, '548 - Provider Accept Assignment Indicator Value Not Supported')
,(2277,'511-FB','549','Provider ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '549 - Provider ID Qualifier Value Not Supported')
,(2278,'511-FB','550','Request Status Value Not Supported','2025-03-14 17:30:12',1, '550 - Request Status Value Not Supported')
,(2279,'511-FB','551','Request Type Value Not Supported','2025-03-14 17:30:12',1, '551 - Request Type Value Not Supported')
,(2280,'511-FB','552','Route of Administration Value Not Supported','2025-03-14 17:30:12',1, '552 - Route of Administration Value Not Supported')
,(2281,'511-FB','553','Smoker/Non-Smoker Code Value Not Supported','2025-03-14 17:30:12',1, '553 - Smoker/Non-Smoker Code Value Not Supported')
,(2282,'511-FB','554','Special Packaging Indicator Value Not Supported','2025-03-14 17:30:12',1, '554 - Special Packaging Indicator Value Not Supported')
,(2283,'511-FB','555','Transaction Count Value Not Supported','2025-03-14 17:30:12',1, '555 - Transaction Count Value Not Supported')
,(2284,'511-FB','556','Unit Of Measure Value Not Supported','2025-03-14 17:30:12',1, '556 - Unit Of Measure Value Not Supported')
,(2285,'511-FB','557','COB Segment Present On A Non-COB Claim','2025-03-14 17:30:12',1, '557 - COB Segment Present On A Non-COB Claim')
,(2286,'511-FB','558','Part D Plan Cannot Coordinate Benefits With Another Part D Plan','2025-03-14 17:30:12',1, '558 - Part D Plan Cannot Coordinate Benefits With Another Part D Plan')
,(2287,'511-FB','559','ID Submitted Is Associated With A Sanctioned Pharmacy','2025-03-14 17:30:12',1, '559 - ID Submitted Is Associated With A Sanctioned Pharmacy')
,(2288,'511-FB','560','Pharmacy Not Contracted In Retail Network','2025-03-14 17:30:12',1, '560 - Pharmacy Not Contracted In Retail Network')
,(2289,'511-FB','561','Pharmacy Not Contracted In Mail Order Network','2025-03-14 17:30:12',1, '561 - Pharmacy Not Contracted In Mail Order Network')
,(2290,'511-FB','562','Pharmacy Not Contracted In Hospice Network','2025-03-14 17:30:12',1, '562 - Pharmacy Not Contracted In Hospice Network')
,(2291,'511-FB','563','Pharmacy Not Contracted In Veterans Administration Network','2025-03-14 17:30:12',1, '563 - Pharmacy Not Contracted In Veterans Administration Network')
,(2292,'511-FB','564','Pharmacy Not Contracted In Military Network','2025-03-14 17:30:12',1, '564 - Pharmacy Not Contracted In Military Network')
,(2293,'511-FB','565','Patient Country Code Value Not Supported','2025-03-14 17:30:12',1, '565 - Patient Country Code Value Not Supported')
,(2294,'511-FB','566','Patient Country Code Not Used For This Transaction','2025-03-14 17:30:12',1, '566 - Patient Country Code Not Used For This Transaction')
,(2295,'511-FB','567','M/I Veterinary Use Indicator','2025-03-14 17:30:12',1, '567 - M/I Veterinary Use Indicator')
,(2296,'511-FB','568','Veterinary Use Indicator Value Not Supported','2025-03-14 17:30:12',1, '568 - Veterinary Use Indicator Value Not Supported')
,(2297,'511-FB','569','Provide Notice: Medicare Prescription Drug Coverage And Your Rights','2025-03-14 17:30:12',1, '569 - Provide Notice: Medicare Prescription Drug Coverage And Your Rights')
,(2298,'511-FB','570','Veterinary Use Indicator Not Used For This Transaction','2025-03-14 17:30:12',1, '570 - Veterinary Use Indicator Not Used For This Transaction')
,(2299,'511-FB','571','Patient ID Associated State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '571 - Patient ID Associated State/Province Address Value Not Supported')
,(2300,'511-FB','572','Medigap ID Not Covered','2025-03-14 17:30:12',1, '572 - Medigap ID Not Covered')
,(2301,'511-FB','573','Prescriber Alternate ID Associated State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '573 - Prescriber Alternate ID Associated State/Province Address Value Not Supported')
,(2302,'511-FB','574','Compound Ingredient Modifier Code Not Covered','2025-03-14 17:30:12',1, '574 - Compound Ingredient Modifier Code Not Covered')
,(2303,'511-FB','575','Purchaser State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '575 - Purchaser State/Province Address Value Not Supported')
,(2304,'511-FB','576','Service Provider State/Province Address Value Not Supported','2025-03-14 17:30:12',1, '576 - Service Provider State/Province Address Value Not Supported')
,(2305,'511-FB','577','M/I Other Payer ID','2025-03-14 17:30:12',1, '577 - M/I Other Payer ID')
,(2306,'511-FB','578','Other Payer ID Count Does Not Match Number of Repetitions','2025-03-14 17:30:12',1, '578 - Other Payer ID Count Does Not Match Number of Repetitions')
,(2307,'511-FB','579','Other Payer ID Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, '579 - Other Payer ID Count Exceeds Number Of Occurrences Supported')
,(2308,'511-FB','580','Other Payer ID Count Grouping Incorrect','2025-03-14 17:30:12',1, '580 - Other Payer ID Count Grouping Incorrect')
,(2309,'511-FB','581','Other Payer ID Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '581 - Other Payer ID Count Is Not Used For This Transaction Code')
,(2310,'511-FB','582','M/I Fill #','2025-03-14 17:30:12',1, '582 - M/I Fill #')
,(2311,'511-FB','583','Provider ID Not Covered','2025-03-14 17:30:12',1, '583 - Provider ID Not Covered')
,(2312,'511-FB','584','Purchaser ID Associated State/Province Code Value Not Supported','2025-03-14 17:30:12',1, '584 - Purchaser ID Associated State/Province Code Value Not Supported')
,(2313,'511-FB','585','Fill # Value Not Supported','2025-03-14 17:30:12',1, '585 - Fill # Value Not Supported')
,(2314,'511-FB','586','Facility ID Not Covered','2025-03-14 17:30:12',1, '586 - Facility ID Not Covered')
,(2315,'511-FB','587','Carrier ID Not Covered','2025-03-14 17:30:12',1, '587 - Carrier ID Not Covered')
,(2316,'511-FB','588','Alternate ID Not Covered','2025-03-14 17:30:12',1, '588 - Alternate ID Not Covered')
,(2317,'511-FB','589','Patient ID Not Covered','2025-03-14 17:30:12',1, '589 - Patient ID Not Covered')
,(2318,'511-FB','590','Compound Dosage Form Not Covered','2025-03-14 17:30:12',1, '590 - Compound Dosage Form Not Covered')
,(2319,'511-FB','591','Plan ID Not Covered','2025-03-14 17:30:12',1, '591 - Plan ID Not Covered')
,(2320,'511-FB','592','DUR Co-Agent ID Not Covered','2025-03-14 17:30:12',1, '592 - DUR Co-Agent ID Not Covered')
,(2321,'511-FB','593','M/I Date Of Service','2025-03-14 17:30:12',1, '593 - M/I Date Of Service')
,(2322,'511-FB','594','Pay To ID Not Covered','2025-03-14 17:30:12',1, '594 - Pay To ID Not Covered')
,(2323,'511-FB','595','Associated Prescription/Service Provider ID Not Covered','2025-03-14 17:30:12',1, '595 - Associated Prescription/Service Provider ID Not Covered')
,(2324,'511-FB','596','Compound Preparation Time Not Used For This Transaction Code','2025-03-14 17:30:12',1, '596 - Compound Preparation Time Not Used For This Transaction Code')
,(2325,'511-FB','597','LTC Dispensing Type Does Not Support The Packaging Type','2025-03-14 17:30:12',1, '597 - LTC Dispensing Type Does Not Support The Packaging Type')
,(2326,'511-FB','598','More Than One Patient Found','2025-03-14 17:30:12',1, '598 - More Than One Patient Found')
,(2327,'511-FB','599','Cardholder ID Matched But Last Name Did Not','2025-03-14 17:30:12',1, '599 - Cardholder ID Matched But Last Name Did Not')
,(2328,'511-FB','600','Coverage Outside Submitted Date Of Service','2025-03-14 17:30:12',1, '600 - Coverage Outside Submitted Date Of Service')
,(2329,'511-FB','601','Intermediary Authorization Type ID Does Not Precede Intermediary Authorization ID','2025-03-14 17:30:12',1, '601 - Intermediary Authorization Type ID Does Not Precede Intermediary Authorization ID')
,(2330,'511-FB','602','Associated Prescription/Service Provider ID Qualifier Does Not Precede Associated Prescription/Service Provider ID','2025-03-14 17:30:12',1, '602 - Associated Prescription/Service Provider ID Qualifier Does Not Precede Associated Prescription/Service Provider ID')
,(2331,'511-FB','603','Prescriber Alternate ID Qualifier Does Not Precede Prescriber Alternate ID','2025-03-14 17:30:12',1, '603 - Prescriber Alternate ID Qualifier Does Not Precede Prescriber Alternate ID')
,(2332,'511-FB','604','Purchaser ID Qualifier Does Not Precede Purchaser ID','2025-03-14 17:30:12',1, '604 - Purchaser ID Qualifier Does Not Precede Purchaser ID')
,(2333,'511-FB','605','Seller ID Qualifier Does Not Precede Seller ID','2025-03-14 17:30:12',1, '605 - Seller ID Qualifier Does Not Precede Seller ID')
,(2334,'511-FB','606','Brand Drug/Specific Labeler Code Required','2025-03-14 17:30:12',1, '606 - Brand Drug/Specific Labeler Code Required')
,(2335,'511-FB','607','Information Reporting (N1/N3) Transaction Cannot Be Matched To A Claim (B1/B3)','2025-03-14 17:30:12',1, '607 - Information Reporting (N1/N3) Transaction Cannot Be Matched To A Claim (B1/B3)')
,(2336,'511-FB','608','Step Therapy, Alternate Drug Therapy Required Prior To Use Of Submitted Product Service ID','2025-03-14 17:30:12',1, '608 - Step Therapy, Alternate Drug Therapy Required Prior To Use Of Submitted Product Service ID')
,(2337,'511-FB','609','COB Claim Not Required, Patient Liability Amount Submitted Was Zero','2025-03-14 17:30:12',1, '609 - COB Claim Not Required, Patient Liability Amount Submitted Was Zero')
,(2338,'511-FB','610','Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN','2025-03-14 17:30:12',1, '610 - Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN')
,(2339,'511-FB','611','Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D','2025-03-14 17:30:12',1, '611 - Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D')
,(2340,'511-FB','612','LTC Appropriate Dispensing Invalid Submission Clarification Code (SCC) Combination','2025-03-14 17:30:12',1, '612 - LTC Appropriate Dispensing Invalid Submission Clarification Code (SCC) Combination')
,(2341,'511-FB','613','The Packaging Methodology  Or Dispensing Frequency Is Missing Or Inappropriate For LTC Short Cycle','2025-03-14 17:30:12',1, '613 - The Packaging Methodology  Or Dispensing Frequency Is Missing Or Inappropriate For LTC Short Cycle')
,(2342,'511-FB','614','Uppercase Character(s) Required','2025-03-14 17:30:12',1, '614 - Uppercase Character(s) Required')
,(2343,'511-FB','615','Compound Ingredient Basis Of Cost Determination Value 14 Required When Compound Ingredient Quantity Is 0 But Cost Is Greater Than $0','2025-03-14 17:30:12',1, '615 - Compound Ingredient Basis Of Cost Determination Value 14 Required When Compound Ingredient Quantity Is 0 But Cost Is Greater Than $0')
,(2344,'511-FB','616','Submission Clarification Code 8 Required When Compound Ingredient Quantity Is 0','2025-03-14 17:30:12',1, '616 - Submission Clarification Code 8 Required When Compound Ingredient Quantity Is 0')
,(2345,'511-FB','617','Compound Ingredient Drug Cost Cannot Be Negative Amount','2025-03-14 17:30:12',1, '617 - Compound Ingredient Drug Cost Cannot Be Negative Amount')
,(2346,'511-FB','618','Plan''s Prescriber Data Base Indicates The Submitted Prescriber''s DEA Does Not Allow This Drug DEA Schedule','2025-03-14 17:30:12',1, '618 - Plan''s Prescriber Data Base Indicates The Submitted Prescriber''s DEA Does Not Allow This Drug DEA Schedule')
,(2347,'511-FB','619','Prescriber Type 1 NPI Required','2025-03-14 17:30:12',1, '619 - Prescriber Type 1 NPI Required')
,(2348,'511-FB','620','This Product/Service May Be Covered Under Medicare Part D','2025-03-14 17:30:12',1, '620 - This Product/Service May Be Covered Under Medicare Part D')
,(2349,'511-FB','621','This Medicaid Patient Is Medicare Eligible','2025-03-14 17:30:12',1, '621 - This Medicaid Patient Is Medicare Eligible')
,(2350,'511-FB','622','COB Claim Not Required, Patient Liability Amount Submitted Was Zero','2025-03-14 17:30:12',1, '622 - COB Claim Not Required, Patient Liability Amount Submitted Was Zero')
,(2351,'511-FB','623','M/I Authorized Representative Country Code','2025-03-14 17:30:12',1, '623 - M/I Authorized Representative Country Code')
,(2352,'511-FB','624','M/I Employer Country Code','2025-03-14 17:30:12',1, '624 - M/I Employer Country Code')
,(2353,'511-FB','625','M/I Entity Country Code','2025-03-14 17:30:12',1, '625 - M/I Entity Country Code')
,(2354,'511-FB','627','M/I Facility Country Code','2025-03-14 17:30:12',1, '627 - M/I Facility Country Code')
,(2355,'511-FB','628','M/I Patient ID Associated Country Code','2025-03-14 17:30:12',1, '628 - M/I Patient ID Associated Country Code')
,(2356,'511-FB','629','M/I Pay To Country Code','2025-03-14 17:30:12',1, '629 - M/I Pay To Country Code')
,(2357,'511-FB','630','M/I Prescriber Alternate ID Associated Country Code','2025-03-14 17:30:12',1, '630 - M/I Prescriber Alternate ID Associated Country Code')
,(2358,'511-FB','631','M/I Prescriber ID Associated Country Code','2025-03-14 17:30:12',1, '631 - M/I Prescriber ID Associated Country Code')
,(2359,'511-FB','632','M/I Prescriber Country Code','2025-03-14 17:30:12',1, '632 - M/I Prescriber Country Code')
,(2360,'511-FB','633','M/I Purchaser ID Associated Country Code','2025-03-14 17:30:12',1, '633 - M/I Purchaser ID Associated Country Code')
,(2361,'511-FB','634','Authorized Representative Country Code Value Not Supported','2025-03-14 17:30:12',1, '634 - Authorized Representative Country Code Value Not Supported')
,(2362,'511-FB','635','Employer Country Code Value Not Supported','2025-03-14 17:30:12',1, '635 - Employer Country Code Value Not Supported')
,(2363,'511-FB','637','Entity Country Code Value Not Supported','2025-03-14 17:30:12',1, '637 - Entity Country Code Value Not Supported')
,(2364,'511-FB','638','Facility Country Code Value Not Supported','2025-03-14 17:30:12',1, '638 - Facility Country Code Value Not Supported')
,(2365,'511-FB','639','Patient ID Associated Country Code Value Not Supported','2025-03-14 17:30:12',1, '639 - Patient ID Associated Country Code Value Not Supported')
,(2366,'511-FB','640','Pay To Country Code Value Not Supported','2025-03-14 17:30:12',1, '640 - Pay To Country Code Value Not Supported')
,(2367,'511-FB','641','Prescriber Alternate ID Associated Country Code Value Not Supported','2025-03-14 17:30:12',1, '641 - Prescriber Alternate ID Associated Country Code Value Not Supported')
,(2368,'511-FB','642','Prescriber ID Associated Country Code Value Not Supported','2025-03-14 17:30:12',1, '642 - Prescriber ID Associated Country Code Value Not Supported')
,(2369,'511-FB','643','Prescriber Country Code Value Not Supported','2025-03-14 17:30:12',1, '643 - Prescriber Country Code Value Not Supported')
,(2370,'511-FB','644','Purchaser ID Associated Country Code Value Not Supported','2025-03-14 17:30:12',1, '644 - Purchaser ID Associated Country Code Value Not Supported')
,(2371,'511-FB','645','Repackaged Product Is Not Covered By The Contract','2025-03-14 17:30:12',1, '645 - Repackaged Product Is Not Covered By The Contract')
,(2372,'511-FB','646','Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan','2025-03-14 17:30:12',1, '646 - Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan')
,(2373,'511-FB','647','Quantity Prescribed Required For CII Prescription','2025-03-14 17:30:12',1, '647 - Quantity Prescribed Required For CII Prescription')
,(2374,'511-FB','648','Quantity Prescribed Does Not Match Quantity Prescribed On Original CII Dispensing','2025-03-14 17:30:12',1, '648 - Quantity Prescribed Does Not Match Quantity Prescribed On Original CII Dispensing')
,(2375,'511-FB','649','Cumulative Quantity For This CII Rx Number Exceeds  Quantity Prescribed','2025-03-14 17:30:12',1, '649 - Cumulative Quantity For This CII Rx Number Exceeds  Quantity Prescribed')
,(2376,'511-FB','650','Date Of Service Greater Than 60 Days From CII Date Prescription Written (414-DE)','2025-03-14 17:30:12',1, '650 - Date Of Service Greater Than 60 Days From CII Date Prescription Written (414-DE)')
,(2377,'511-FB','651','REMS: Mandatory Data Element(s) Missing','2025-03-14 17:30:12',1, '651 - REMS: Mandatory Data Element(s) Missing')
,(2378,'511-FB','652','REMS: Prescriber Not Matched Or May Not Be Enrolled','2025-03-14 17:30:12',1, '652 - REMS: Prescriber Not Matched Or May Not Be Enrolled')
,(2379,'511-FB','653','REMS: Patient Not Matched Or May Not Be Enrolled','2025-03-14 17:30:12',1, '653 - REMS: Patient Not Matched Or May Not Be Enrolled')
,(2380,'511-FB','654','REMS: Pharmacy Not Matched Or May Not Be Enrolled','2025-03-14 17:30:12',1, '654 - REMS: Pharmacy Not Matched Or May Not Be Enrolled')
,(2381,'511-FB','655','REMS: Multiple Patient Matches','2025-03-14 17:30:12',1, '655 - REMS: Multiple Patient Matches')
,(2382,'511-FB','656','REMS: Patient Age Not Matched','2025-03-14 17:30:12',1, '656 - REMS: Patient Age Not Matched')
,(2383,'511-FB','657','REMS: Patient Gender Not Matched','2025-03-14 17:30:12',1, '657 - REMS: Patient Gender Not Matched')
,(2384,'511-FB','658','REMS: Pharmacy Has Not Enrolled','2025-03-14 17:30:12',1, '658 - REMS: Pharmacy Has Not Enrolled')
,(2385,'511-FB','659','REMS: Pharmacy Has Not Renewed Enrollment','2025-03-14 17:30:12',1, '659 - REMS: Pharmacy Has Not Renewed Enrollment')
,(2386,'511-FB','660','REMS: Pharmacy Has Not Submitted Agreement Form','2025-03-14 17:30:12',1, '660 - REMS: Pharmacy Has Not Submitted Agreement Form')
,(2387,'511-FB','661','REMS: Pharmacy Has Been Suspended Due To Non-compliance','2025-03-14 17:30:12',1, '661 - REMS: Pharmacy Has Been Suspended Due To Non-compliance')
,(2388,'511-FB','662','REMS: Prescriber Has Not Enrolled','2025-03-14 17:30:12',1, '662 - REMS: Prescriber Has Not Enrolled')
,(2389,'511-FB','663','REMS: Prescriber Has Not Completed A Knowledge Assessment','2025-03-14 17:30:12',1, '663 - REMS: Prescriber Has Not Completed A Knowledge Assessment')
,(2390,'511-FB','664','REMS: Prescriber Has Been Suspended Due To Non-compliance','2025-03-14 17:30:12',1, '664 - REMS: Prescriber Has Been Suspended Due To Non-compliance')
,(2391,'511-FB','665','REMS: Excessive Days Supply','2025-03-14 17:30:12',1, '665 - REMS: Excessive Days Supply')
,(2392,'511-FB','666','REMS: Insufficient Days Supply','2025-03-14 17:30:12',1, '666 - REMS: Insufficient Days Supply')
,(2393,'511-FB','667','REMS: Excessive Dosage','2025-03-14 17:30:12',1, '667 - REMS: Excessive Dosage')
,(2394,'511-FB','668','REMS: Insufficient Dosage','2025-03-14 17:30:12',1, '668 - REMS: Insufficient Dosage')
,(2395,'511-FB','669','REMS: Additional Fills Not Permitted','2025-03-14 17:30:12',1, '669 - REMS: Additional Fills Not Permitted')
,(2396,'511-FB','670','REMS: Laboratory Test Results Not Documented','2025-03-14 17:30:12',1, '670 - REMS: Laboratory Test Results Not Documented')
,(2397,'511-FB','671','REMS: Laboratory Test Not Conducted Within Specified Time Period','2025-03-14 17:30:12',1, '671 - REMS: Laboratory Test Not Conducted Within Specified Time Period')
,(2398,'511-FB','672','REMS: Dispensing Not Authorized Due To Laboratory Test Results','2025-03-14 17:30:12',1, '672 - REMS: Dispensing Not Authorized Due To Laboratory Test Results')
,(2399,'511-FB','673','REMS: Prescriber Counseling Of Patient Not Documented','2025-03-14 17:30:12',1, '673 - REMS: Prescriber Counseling Of Patient Not Documented')
,(2400,'511-FB','674','REMS: Prescriber Has Not Documented Safe Use Conditions','2025-03-14 17:30:12',1, '674 - REMS: Prescriber Has Not Documented Safe Use Conditions')
,(2401,'511-FB','675','REMS: Prescriber Has Not Documented Patient Opioid Tolerance','2025-03-14 17:30:12',1, '675 - REMS: Prescriber Has Not Documented Patient Opioid Tolerance')
,(2402,'511-FB','676','REMS: Prescriber Has Not Documented Patient Contraceptive Use','2025-03-14 17:30:12',1, '676 - REMS: Prescriber Has Not Documented Patient Contraceptive Use')
,(2403,'511-FB','677','REMS: Lack Of Contraindicated Therapy Not Documented','2025-03-14 17:30:12',1, '677 - REMS: Lack Of Contraindicated Therapy Not Documented')
,(2404,'511-FB','678','REMS: Step Therapy Not Documented','2025-03-14 17:30:12',1, '678 - REMS: Step Therapy Not Documented')
,(2405,'511-FB','679','REMS: Prescriber Has Not Enrolled Patient','2025-03-14 17:30:12',1, '679 - REMS: Prescriber Has Not Enrolled Patient')
,(2406,'511-FB','680','REMS: Prescriber Must Renew Patient Enrollment','2025-03-14 17:30:12',1, '680 - REMS: Prescriber Must Renew Patient Enrollment')
,(2407,'511-FB','681','REMS: Patient Enrollment Requirements Have Not Been Met','2025-03-14 17:30:12',1, '681 - REMS: Patient Enrollment Requirements Have Not Been Met')
,(2408,'511-FB','682','REMS: Prescriber Has Not Submitted Patient Agreement','2025-03-14 17:30:12',1, '682 - REMS: Prescriber Has Not Submitted Patient Agreement')
,(2409,'511-FB','683','REMS: Prescriber Has Not Verified Patient''s Reproductive Potential','2025-03-14 17:30:12',1, '683 - REMS: Prescriber Has Not Verified Patient''s Reproductive Potential')
,(2410,'511-FB','684','REMS: Patient Has Not Documented Safe Use Conditions','2025-03-14 17:30:12',1, '684 - REMS: Patient Has Not Documented Safe Use Conditions')
,(2411,'511-FB','685','REMS: Patient Has Not Documented Completed Education','2025-03-14 17:30:12',1, '685 - REMS: Patient Has Not Documented Completed Education')
,(2412,'511-FB','686','REMS: Patient Has Not Documented Contraceptive Use','2025-03-14 17:30:12',1, '686 - REMS: Patient Has Not Documented Contraceptive Use')
,(2413,'511-FB','687','REMS: Administrator Denied','2025-03-14 17:30:12',1, '687 - REMS: Administrator Denied')
,(2414,'511-FB','688','REMS: Service Billing Denied','2025-03-14 17:30:12',1, '688 - REMS: Service Billing Denied')
,(2415,'511-FB','689','PDMP: Administrator Denied','2025-03-14 17:30:12',1, '689 - PDMP: Administrator Denied')
,(2416,'511-FB','690','PDMP: Pharmacy Not Contracted','2025-03-14 17:30:12',1, '690 - PDMP: Pharmacy Not Contracted')
,(2417,'511-FB','691','PDMP: Pharmacy Contract Not Renewed','2025-03-14 17:30:12',1, '691 - PDMP: Pharmacy Contract Not Renewed')
,(2418,'511-FB','692','PDMP: M/I Patient First Name','2025-03-14 17:30:12',1, '692 - PDMP: M/I Patient First Name')
,(2419,'511-FB','693','PDMP: M/I Patient Last Name','2025-03-14 17:30:12',1, '693 - PDMP: M/I Patient Last Name')
,(2420,'511-FB','694','PDMP: M/I Patient Street Address','2025-03-14 17:30:12',1, '694 - PDMP: M/I Patient Street Address')
,(2421,'511-FB','695','PDMP: M/I Patient City','2025-03-14 17:30:12',1, '695 - PDMP: M/I Patient City')
,(2422,'511-FB','696','PDMP: M/I Patient State Or Province','2025-03-14 17:30:12',1, '696 - PDMP: M/I Patient State Or Province')
,(2423,'511-FB','697','PDMP: M/I Patient ZIP/Postal Code','2025-03-14 17:30:12',1, '697 - PDMP: M/I Patient ZIP/Postal Code')
,(2424,'511-FB','698','PDMP: M/I Prescriber ID','2025-03-14 17:30:12',1, '698 - PDMP: M/I Prescriber ID')
,(2425,'511-FB','699','PDMP: M/I Prescriber Last Name','2025-03-14 17:30:12',1, '699 - PDMP: M/I Prescriber Last Name')
,(2426,'511-FB','700','PDMP: M/I Patient ID','2025-03-14 17:30:12',1, '700 - PDMP: M/I Patient ID')
,(2427,'511-FB','701','PDMP: M/I Patient Date Of Birth','2025-03-14 17:30:12',1, '701 - PDMP: M/I Patient Date Of Birth')
,(2428,'511-FB','702','PDMP: M/I Patient Gender','2025-03-14 17:30:12',1, '702 - PDMP: M/I Patient Gender')
,(2429,'511-FB','703','PDMP: M/I Prescription Origin Code','2025-03-14 17:30:12',1, '703 - PDMP: M/I Prescription Origin Code')
,(2430,'511-FB','704','PDMP: M/I Scheduled Rx Serial Number','2025-03-14 17:30:12',1, '704 - PDMP: M/I Scheduled Rx Serial Number')
,(2431,'511-FB','705','PDMP: M/I Product/Service ID','2025-03-14 17:30:12',1, '705 - PDMP: M/I Product/Service ID')
,(2432,'511-FB','706','PDMP: M/I Compound Code','2025-03-14 17:30:12',1, '706 - PDMP: M/I Compound Code')
,(2433,'511-FB','707','PDMP: M/I Patient Phone Number','2025-03-14 17:30:12',1, '707 - PDMP: M/I Patient Phone Number')
,(2434,'511-FB','708','PDMP: M/I Reported Adjudicated Program Type','2025-03-14 17:30:12',1, '708 - PDMP: M/I Reported Adjudicated Program Type')
,(2435,'511-FB','709','M/I Record Type','2025-03-14 17:30:12',1, '709 - M/I Record Type')
,(2436,'511-FB','710','Date Received After Requested Response Date','2025-03-14 17:30:12',1, '710 - Date Received After Requested Response Date')
,(2437,'511-FB','711','M/I Transmission Date','2025-03-14 17:30:12',1, '711 - M/I Transmission Date')
,(2438,'511-FB','712','M/I Sending Entity Identifier','2025-03-14 17:30:12',1, '712 - M/I Sending Entity Identifier')
,(2439,'511-FB','713','M/I Receiver ID','2025-03-14 17:30:12',1, '713 - M/I Receiver ID')
,(2440,'511-FB','714','M/I Transmission File Type','2025-03-14 17:30:12',1, '714 - M/I Transmission File Type')
,(2441,'511-FB','715','M/I Transmission Type','2025-03-14 17:30:12',1, '715 - M/I Transmission Type')
,(2442,'511-FB','716','Transmission File Type Not Supported','2025-03-14 17:30:12',1, '716 - Transmission File Type Not Supported')
,(2443,'511-FB','717','M/I Submission Number','2025-03-14 17:30:12',1, '717 - M/I Submission Number')
,(2444,'511-FB','718','M/I Audit Request Type','2025-03-14 17:30:12',1, '718 - M/I Audit Request Type')
,(2445,'511-FB','719','Audit Request Type Not Supported','2025-03-14 17:30:12',1, '719 - Audit Request Type Not Supported')
,(2446,'511-FB','720','M/I Service Provider Chain Code','2025-03-14 17:30:12',1, '720 - M/I Service Provider Chain Code')
,(2447,'511-FB','721','M/I Entity Name','2025-03-14 17:30:12',1, '721 - M/I Entity Name')
,(2448,'511-FB','722','M/I Entity Contact First Name','2025-03-14 17:30:12',1, '722 - M/I Entity Contact First Name')
,(2449,'511-FB','723','M/I Entity Contact Last Name','2025-03-14 17:30:12',1, '723 - M/I Entity Contact Last Name')
,(2450,'511-FB','724','M/I Entity Address Line 1','2025-03-14 17:30:12',1, '724 - M/I Entity Address Line 1')
,(2451,'511-FB','725','M/I Entity Address Line 2','2025-03-14 17:30:12',1, '725 - M/I Entity Address Line 2')
,(2452,'511-FB','726','M/I Entity City','2025-03-14 17:30:12',1, '726 - M/I Entity City')
,(2453,'511-FB','727','M/I Entity State/Province Address','2025-03-14 17:30:12',1, '727 - M/I Entity State/Province Address')
,(2454,'511-FB','728','M/I Entity ZIP/Postal Code','2025-03-14 17:30:12',1, '728 - M/I Entity ZIP/Postal Code')
,(2455,'511-FB','729','M/I Entity Fax Number','2025-03-14 17:30:12',1, '729 - M/I Entity Fax Number')
,(2456,'511-FB','730','M/I Entity Email','2025-03-14 17:30:12',1, '730 - M/I Entity Email')
,(2457,'511-FB','731','Header Response Status Not Supported For This Transmission File Type','2025-03-14 17:30:12',1, '731 - Header Response Status Not Supported For This Transmission File Type')
,(2458,'511-FB','732','Reject Code Not Supported For This Transmission File Type','2025-03-14 17:30:12',1, '732 - Reject Code Not Supported For This Transmission File Type')
,(2459,'511-FB','733','M/I Claim Sequence Number','2025-03-14 17:30:12',1, '733 - M/I Claim Sequence Number')
,(2460,'511-FB','734','M/I Audit Control Identification','2025-03-14 17:30:12',1, '734 - M/I Audit Control Identification')
,(2461,'511-FB','735','M/I Audit Range Qualifier','2025-03-14 17:30:12',1, '735 - M/I Audit Range Qualifier')
,(2462,'511-FB','736','Audit Range Qualifier Not Supported For This Audit Request Type','2025-03-14 17:30:12',1, '736 - Audit Range Qualifier Not Supported For This Audit Request Type')
,(2463,'511-FB','737','M/I Audit Range Start','2025-03-14 17:30:12',1, '737 - M/I Audit Range Start')
,(2464,'511-FB','738','Audit Range Start Not Supported For This Audit Request Type','2025-03-14 17:30:12',1, '738 - Audit Range Start Not Supported For This Audit Request Type')
,(2465,'511-FB','739','M/I Audit Range End','2025-03-14 17:30:12',1, '739 - M/I Audit Range End')
,(2466,'511-FB','740','Audit Range End Not Supported For This Audit Request Type','2025-03-14 17:30:12',1, '740 - Audit Range End Not Supported For This Audit Request Type')
,(2467,'511-FB','741','Exceeds Range Start Limitations','2025-03-14 17:30:12',1, '741 - Exceeds Range Start Limitations')
,(2468,'511-FB','742','Exceeds Range End Limitations','2025-03-14 17:30:12',1, '742 - Exceeds Range End Limitations')
,(2469,'511-FB','743','M/I Requested Response Date','2025-03-14 17:30:12',1, '743 - M/I Requested Response Date')
,(2470,'511-FB','744','Response Date Requires Rescheduling','2025-03-14 17:30:12',1, '744 - Response Date Requires Rescheduling')
,(2471,'511-FB','745','M/I Estimated Arrival Time Description','2025-03-14 17:30:12',1, '745 - M/I Estimated Arrival Time Description')
,(2472,'511-FB','746','Estimated Arrival Time Requires Rescheduling','2025-03-14 17:30:12',1, '746 - Estimated Arrival Time Requires Rescheduling')
,(2473,'511-FB','747','M/I Audit Sponsor','2025-03-14 17:30:12',1, '747 - M/I Audit Sponsor')
,(2474,'511-FB','748','Non-Matched Processor Control Number','2025-03-14 17:30:12',1, '748 - Non-Matched Processor Control Number')
,(2475,'511-FB','749','M/I Audit Element Type 1','2025-03-14 17:30:12',1, '749 - M/I Audit Element Type 1')
,(2476,'511-FB','750','M/I Audit Element Type 2','2025-03-14 17:30:12',1, '750 - M/I Audit Element Type 2')
,(2477,'511-FB','751','M/I Audit Element Type 3','2025-03-14 17:30:12',1, '751 - M/I Audit Element Type 3')
,(2478,'511-FB','752','M/I Audit Element Type 4','2025-03-14 17:30:12',1, '752 - M/I Audit Element Type 4')
,(2479,'511-FB','753','M/I Audit Element Type 5','2025-03-14 17:30:12',1, '753 - M/I Audit Element Type 5')
,(2480,'511-FB','754','Audit Element Type Not Allowable Per State Regulation','2025-03-14 17:30:12',1, '754 - Audit Element Type Not Allowable Per State Regulation')
,(2481,'511-FB','755','Audit Element Type Not Required For Dispensing','2025-03-14 17:30:12',1, '755 - Audit Element Type Not Required For Dispensing')
,(2482,'511-FB','756','M/I Audit Element Response Type 1','2025-03-14 17:30:12',1, '756 - M/I Audit Element Response Type 1')
,(2483,'511-FB','757','M/I Audit Element Response Type 2','2025-03-14 17:30:12',1, '757 - M/I Audit Element Response Type 2')
,(2484,'511-FB','758','M/I Audit Element Response Type 3','2025-03-14 17:30:12',1, '758 - M/I Audit Element Response Type 3')
,(2485,'511-FB','759','M/I Audit Element Response Type 4','2025-03-14 17:30:12',1, '759 - M/I Audit Element Response Type 4')
,(2486,'511-FB','760','M/I Audit Element Response Type 5','2025-03-14 17:30:12',1, '760 - M/I Audit Element Response Type 5')
,(2487,'511-FB','761','M/I Discrepancy Code 1','2025-03-14 17:30:12',1, '761 - M/I Discrepancy Code 1')
,(2488,'511-FB','762','M/I Discrepancy Code 2','2025-03-14 17:30:12',1, '762 - M/I Discrepancy Code 2')
,(2489,'511-FB','763','M/I Discrepancy Code 3','2025-03-14 17:30:12',1, '763 - M/I Discrepancy Code 3')
,(2490,'511-FB','764','M/I Discrepancy Message','2025-03-14 17:30:12',1, '764 - M/I Discrepancy Message')
,(2491,'511-FB','765','M/I Discrepancy Amount','2025-03-14 17:30:12',1, '765 - M/I Discrepancy Amount')
,(2492,'511-FB','766','Discrepancy Amount In Excess Of Claimed Amount','2025-03-14 17:30:12',1, '766 - Discrepancy Amount In Excess Of Claimed Amount')
,(2493,'511-FB','767','M/I Record Count','2025-03-14 17:30:12',1, '767 - M/I Record Count')
,(2494,'511-FB','768','Pharmacy Location Has Closed','2025-03-14 17:30:12',1, '768 - Pharmacy Location Has Closed')
,(2495,'511-FB','769','Paid Billing Transaction (B1/B3) Submitted Under The Part D IIN PCN Found But Information Reporting Reversal (N2) Cannot Be Matched To An Information Reporting Transaction (N1/N3) In An Approved Status; Reversal (N2) Not Processed','2025-03-14 17:30:12',1, '769 - Paid Billing Transaction (B1/B3) Submitted Under The Part D IIN PCN Found But Information Reporting Reversal (N2) Cannot Be Matched To An Information Reporting Transaction (N1/N3) In An Approved Status; Reversal (N2) Not Processed')
,(2496,'511-FB','770','Paid Billing Transaction (B1/B3) Submitted Under The Part D IIN PCN Not Found And Information Reporting Reversal (N2) Cannot Be Matched To An Information Reporting Transaction (N1/N3) In Approved Status; Reversal (N2) Not Processed','2025-03-14 17:30:12',1, '770 - Paid Billing Transaction (B1/B3) Submitted Under The Part D IIN PCN Not Found And Information Reporting Reversal (N2) Cannot Be Matched To An Information Reporting Transaction (N1/N3) In Approved Status; Reversal (N2) Not Processed')
,(2497,'511-FB','771','Compound Contains Unidentifiable Ingredient(s); Submission Clarification Code Override Not Allowed','2025-03-14 17:30:12',1, '771 - Compound Contains Unidentifiable Ingredient(s); Submission Clarification Code Override Not Allowed')
,(2498,'511-FB','772','Compound Not Payable Due To Non-covered Ingredient(s); Submission Clarification Code Override Not Allowed','2025-03-14 17:30:12',1, '772 - Compound Not Payable Due To Non-covered Ingredient(s); Submission Clarification Code Override Not Allowed')
,(2499,'511-FB','773','Prescriber Is Not Listed On Medicare Enrollment File','2025-03-14 17:30:12',1, '773 - Prescriber Is Not Listed On Medicare Enrollment File')
,(2500,'511-FB','774','Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service','2025-03-14 17:30:12',1, '774 - Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service')
,(2501,'511-FB','775','Pharmacy Not Listed Within Medicare Fee For Service Active Enrollment File','2025-03-14 17:30:12',1, '775 - Pharmacy Not Listed Within Medicare Fee For Service Active Enrollment File')
,(2502,'511-FB','776','Pharmacy Enrollment With Medicare Fee For Service Has Terminated','2025-03-14 17:30:12',1, '776 - Pharmacy Enrollment With Medicare Fee For Service Has Terminated')
,(2503,'511-FB','777','Plan''s Prescriber Data Base Not Able To Verify Active State License With Prescriptive Authority For Prescriber ID Submitted','2025-03-14 17:30:12',1, '777 - Plan''s Prescriber Data Base Not Able To Verify Active State License With Prescriptive Authority For Prescriber ID Submitted')
,(2504,'511-FB','778','Invalid Transmission File Type','2025-03-14 17:30:12',1, '778 - Invalid Transmission File Type')
,(2505,'511-FB','779','Invalid Document Reference Number','2025-03-14 17:30:12',1, '779 - Invalid Document Reference Number')
,(2506,'511-FB','780','M/I Transmission Time','2025-03-14 17:30:12',1, '780 - M/I Transmission Time')
,(2507,'511-FB','781','Corrupted Transmission Control Number','2025-03-14 17:30:12',1, '781 - Corrupted Transmission Control Number')
,(2508,'511-FB','782','M/I Sender ID','2025-03-14 17:30:12',1, '782 - M/I Sender ID')
,(2509,'511-FB','783','M/I Receiver ID','2025-03-14 17:30:12',1, '783 - M/I Receiver ID')
,(2510,'511-FB','784','M/I File Type','2025-03-14 17:30:12',1, '784 - M/I File Type')
,(2511,'511-FB','785','M/I Submission Number','2025-03-14 17:30:12',1, '785 - M/I Submission Number')
,(2512,'511-FB','786','M/I Transmission Date','2025-03-14 17:30:12',1, '786 - M/I Transmission Date')
,(2513,'511-FB','787','M/I Accumulator Balance Count','2025-03-14 17:30:12',1, '787 - M/I Accumulator Balance Count')
,(2514,'511-FB','788','M/I Accumulator Network Indicator','2025-03-14 17:30:12',1, '788 - M/I Accumulator Network Indicator')
,(2515,'511-FB','789','M/I Accumulator Action Code','2025-03-14 17:30:12',1, '789 - M/I Accumulator Action Code')
,(2516,'511-FB','790','M/I Benefit Type','2025-03-14 17:30:12',1, '790 - M/I Benefit Type')
,(2517,'511-FB','791','M/I In Network Status','2025-03-14 17:30:12',1, '791 - M/I In Network Status')
,(2518,'511-FB','792','Duplicate Record','2025-03-14 17:30:12',1, '792 - Duplicate Record')
,(2519,'511-FB','793','Retry Limit Exceeded','2025-03-14 17:30:12',1, '793 - Retry Limit Exceeded')
,(2520,'511-FB','794','Deductible Over Accumulated','2025-03-14 17:30:12',1, '794 - Deductible Over Accumulated')
,(2521,'511-FB','795','Out Of Pocket Over Accumulated','2025-03-14 17:30:12',1, '795 - Out Of Pocket Over Accumulated')
,(2522,'511-FB','796','Maximum Benefit Amount (CAP) Over Accumulated','2025-03-14 17:30:12',1, '796 - Maximum Benefit Amount (CAP) Over Accumulated')
,(2523,'511-FB','797','Corrupted Transmission Control Number','2025-03-14 17:30:12',1, '797 - Corrupted Transmission Control Number')
,(2524,'511-FB','798','SA Over Accumulated','2025-03-14 17:30:12',1, '798 - SA Over Accumulated')
,(2525,'511-FB','799','LTC Over Accumulated','2025-03-14 17:30:12',1, '799 - LTC Over Accumulated')
,(2526,'511-FB','800','RXC Over Accumulated','2025-03-14 17:30:12',1, '800 - RXC Over Accumulated')
,(2527,'511-FB','801','M/I Total Amount Paid','2025-03-14 17:30:12',1, '801 - M/I Total Amount Paid')
,(2528,'511-FB','802','M/I Amount Of Copay','2025-03-14 17:30:12',1, '802 - M/I Amount Of Copay')
,(2529,'511-FB','803','M/I Patient Pay Amount','2025-03-14 17:30:12',1, '803 - M/I Patient Pay Amount')
,(2530,'511-FB','804','M/I Amount Attributed To Product Selection/Brand','2025-03-14 17:30:12',1, '804 - M/I Amount Attributed To Product Selection/Brand')
,(2531,'511-FB','805','M/I Amount Attributed To Sales Tax','2025-03-14 17:30:12',1, '805 - M/I Amount Attributed To Sales Tax')
,(2532,'511-FB','806','M/I Amount Attributed To Process Fee','2025-03-14 17:30:12',1, '806 - M/I Amount Attributed To Process Fee')
,(2533,'511-FB','807','M/I Invoiced Amount','2025-03-14 17:30:12',1, '807 - M/I Invoiced Amount')
,(2534,'511-FB','808','M/I Penalty Amount','2025-03-14 17:30:12',1, '808 - M/I Penalty Amount')
,(2535,'511-FB','809','Mismatched Original Authorization','2025-03-14 17:30:12',1, '809 - Mismatched Original Authorization')
,(2536,'511-FB','810','M/I Partner Eligibility Data','2025-03-14 17:30:12',1, '810 - M/I Partner Eligibility Data')
,(2537,'511-FB','811','Partner Eligibility Mismatch','2025-03-14 17:30:12',1, '811 - Partner Eligibility Mismatch')
,(2538,'511-FB','812','M/I Record Length','2025-03-14 17:30:12',1, '812 - M/I Record Length')
,(2539,'511-FB','813','M/I Action Code','2025-03-14 17:30:12',1, '813 - M/I Action Code')
,(2540,'511-FB','814','Not Supported Accumulator Action Code','2025-03-14 17:30:12',1, '814 - Not Supported Accumulator Action Code')
,(2541,'511-FB','815','Balance Mismatch','2025-03-14 17:30:12',1, '815 - Balance Mismatch')
,(2542,'511-FB','816','Pharmacy Benefit Exclusion, May Be Covered Under Patient''s Medical Benefit','2025-03-14 17:30:12',1, '816 - Pharmacy Benefit Exclusion, May Be Covered Under Patient''s Medical Benefit')
,(2543,'511-FB','817','Pharmacy Benefit Exclusion, Covered Under Patient''s Medical Benefit','2025-03-14 17:30:12',1, '817 - Pharmacy Benefit Exclusion, Covered Under Patient''s Medical Benefit')
,(2544,'511-FB','818','Medication Administration Not Covered, Plan Benefit Exclusion','2025-03-14 17:30:12',1, '818 - Medication Administration Not Covered, Plan Benefit Exclusion')
,(2545,'511-FB','819','Plan Enrollment File Indicates Medicare As Primary Coverage','2025-03-14 17:30:12',1, '819 - Plan Enrollment File Indicates Medicare As Primary Coverage')
,(2546,'511-FB','820','Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Not Submitted Under Part D IIN PCN','2025-03-14 17:30:12',1, '820 - Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Not Submitted Under Part D IIN PCN')
,(2547,'511-FB','821','Information Reporting (N1/N3) Transaction Matched To Paid Claim Not Submitted Under Part D IIN PCN','2025-03-14 17:30:12',1, '821 - Information Reporting (N1/N3) Transaction Matched To Paid Claim Not Submitted Under Part D IIN PCN')
,(2548,'511-FB','822','Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.','2025-03-14 17:30:12',1, '822 - Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.')
,(2549,'511-FB','823','Drug Is Beneficiary''s Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.','2025-03-14 17:30:12',1, '823 - Drug Is Beneficiary''s Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.')
,(2550,'511-FB','824','Multi-transaction Transmission Not Allowed In Current NCPDP Standard','2025-03-14 17:30:12',1, '824 - Multi-transaction Transmission Not Allowed In Current NCPDP Standard')
,(2551,'511-FB','825','Claim Date Of Service Is Outside Of Product''s FDA/NSDE Marketing Dates','2025-03-14 17:30:12',1, '825 - Claim Date Of Service Is Outside Of Product''s FDA/NSDE Marketing Dates')
,(2552,'511-FB','826','Prescriber NPI Submitted Not Found Within Processor''s NPI File','2025-03-14 17:30:12',1, '826 - Prescriber NPI Submitted Not Found Within Processor''s NPI File')
,(2553,'511-FB','827','Pharmacy Service Provider Is Temporarily Suspended From Processing Claims By Payer/Processor','2025-03-14 17:30:12',1, '827 - Pharmacy Service Provider Is Temporarily Suspended From Processing Claims By Payer/Processor')
,(2554,'511-FB','828','Plan/Beneficiary Case Management Restriction In Place','2025-03-14 17:30:12',1, '828 - Plan/Beneficiary Case Management Restriction In Place')
,(2555,'511-FB','829','Pharmacy Must Notify Beneficiary: Claim Not Covered Due To Failure To Meet Medicare Part D Active, Valid Prescriber NPI Requirements','2025-03-14 17:30:12',1, '829 - Pharmacy Must Notify Beneficiary: Claim Not Covered Due To Failure To Meet Medicare Part D Active, Valid Prescriber NPI Requirements')
,(2556,'511-FB','830','Workers'' Comp Or P&C Adjuster Authorization Required -- Patient Must Directly Contact Their Adjuster','2025-03-14 17:30:12',1, '830 - Workers'' Comp Or P&C Adjuster Authorization Required -- Patient Must Directly Contact Their Adjuster')
,(2557,'511-FB','831','Product Service ID Carve-Out, Bill Medicaid Fee For Service','2025-03-14 17:30:12',1, '831 - Product Service ID Carve-Out, Bill Medicaid Fee For Service')
,(2558,'511-FB','832','Prescriber NPI Not Found, Therefore NPI Active Status, MEDICARE Enrollment, Prescriptive Authority Could Not Be Validated','2025-03-14 17:30:12',1, '832 - Prescriber NPI Not Found, Therefore NPI Active Status, MEDICARE Enrollment, Prescriptive Authority Could Not Be Validated')
,(2559,'511-FB','833','Accumulator Year Is Not Within ATBT Timeframe','2025-03-14 17:30:12',1, '833 - Accumulator Year Is Not Within ATBT Timeframe')
,(2560,'511-FB','834','M/I Provider First Name','2025-03-14 17:30:12',1, '834 - M/I Provider First Name')
,(2561,'511-FB','835','M/I Provider Last Name','2025-03-14 17:30:12',1, '835 - M/I Provider Last Name')
,(2562,'511-FB','836','M/I Facility ID Qualifier','2025-03-14 17:30:12',1, '836 - M/I Facility ID Qualifier')
,(2563,'511-FB','837','Facility ID Value Not Supported','2025-03-14 17:30:12',1, '837 - Facility ID Value Not Supported')
,(2564,'511-FB','838','M/I Original Manufacturer Product  ID','2025-03-14 17:30:12',1, '838 - M/I Original Manufacturer Product  ID')
,(2565,'511-FB','839','M/I Original Manufacturer Product  ID Qualifier','2025-03-14 17:30:12',1, '839 - M/I Original Manufacturer Product  ID Qualifier')
,(2566,'511-FB','840','Original Manufacturer Product  ID Value Not Supported','2025-03-14 17:30:12',1, '840 - Original Manufacturer Product  ID Value Not Supported')
,(2567,'511-FB','841','Record Is Locked.','2025-03-14 17:30:12',1, '841 - Record Is Locked.')
,(2568,'511-FB','842','Record Is Not Locked.','2025-03-14 17:30:12',1, '842 - Record Is Not Locked.')
,(2569,'511-FB','843','M/I Transmission ID','2025-03-14 17:30:12',1, '843 - M/I Transmission ID')
,(2570,'511-FB','844','M/I Other Payer Adjudicated Program Type','2025-03-14 17:30:12',1, '844 - M/I Other Payer Adjudicated Program Type')
,(2571,'511-FB','845','Other Payer Reconciliation ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '845 - Other Payer Reconciliation ID Is Not Used For This Transaction Code')
,(2572,'511-FB','846','Benefit Stage Indicator Count Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '846 - Benefit Stage Indicator Count Is Not Used For This Transaction Code')
,(2573,'511-FB','847','Benefit Stage Indicator Count Does Not Precede Benefit Stage Indicator','2025-03-14 17:30:12',1, '847 - Benefit Stage Indicator Count Does Not Precede Benefit Stage Indicator')
,(2574,'511-FB','848','M/I Benefit Stage Indicator Count','2025-03-14 17:30:12',1, '848 - M/I Benefit Stage Indicator Count')
,(2575,'511-FB','849','Benefit Stage Indicator Count Does Not Match Number of Repetitions','2025-03-14 17:30:12',1, '849 - Benefit Stage Indicator Count Does Not Match Number of Repetitions')
,(2576,'511-FB','850','Benefit Stage Indicator Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '850 - Benefit Stage Indicator Is Not Used For This Transaction Code')
,(2577,'511-FB','851','Benefit Stage Indicator Value Not Supported','2025-03-14 17:30:12',1, '851 - Benefit Stage Indicator Value Not Supported')
,(2578,'511-FB','852','M/I Benefit Stage Indicator','2025-03-14 17:30:12',1, '852 - M/I Benefit Stage Indicator')
,(2579,'511-FB','853','N Payer IIN Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '853 - N Payer IIN Is Not Used For This Transaction Code')
,(2580,'511-FB','854','M/I N Payer IIN','2025-03-14 17:30:12',1, '854 - M/I N Payer IIN')
,(2581,'511-FB','855','Non-Matched N Payer IIN','2025-03-14 17:30:12',1, '855 - Non-Matched N Payer IIN')
,(2582,'511-FB','856','N Payer Processor Control Number Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '856 - N Payer Processor Control Number Is Not Used For This Transaction Code')
,(2583,'511-FB','857','M/I N Payer Processor Control Number','2025-03-14 17:30:12',1, '857 - M/I N Payer Processor Control Number')
,(2584,'511-FB','858','Non-Matched N Payer Processor Control Number','2025-03-14 17:30:12',1, '858 - Non-Matched N Payer Processor Control Number')
,(2585,'511-FB','859','N Payer Group ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '859 - N Payer Group ID Is Not Used For This Transaction Code')
,(2586,'511-FB','860','M/I N Payer Group ID','2025-03-14 17:30:12',1, '860 - M/I N Payer Group ID')
,(2587,'511-FB','861','Non-Matched N Payer Group ID','2025-03-14 17:30:12',1, '861 - Non-Matched N Payer Group ID')
,(2588,'511-FB','862','N Payer Cardholder ID Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '862 - N Payer Cardholder ID Is Not Used For This Transaction Code')
,(2589,'511-FB','863','M/I N Payer Cardholder ID','2025-03-14 17:30:12',1, '863 - M/I N Payer Cardholder ID')
,(2590,'511-FB','864','N Payer Cardholder ID Is Not Covered','2025-03-14 17:30:12',1, '864 - N Payer Cardholder ID Is Not Covered')
,(2591,'511-FB','865','N Payer Adjudicated Program Type Is Not Used For This Transaction Code','2025-03-14 17:30:12',1, '865 - N Payer Adjudicated Program Type Is Not Used For This Transaction Code')
,(2592,'511-FB','866','M/I N Payer Adjudicated Program Type','2025-03-14 17:30:12',1, '866 - M/I N Payer Adjudicated Program Type')
,(2593,'511-FB','867','N Payer Adjudicated Program Type Value Not Supported','2025-03-14 17:30:12',1, '867 - N Payer Adjudicated Program Type Value Not Supported')
,(2594,'511-FB','868','M/I N Transaction Reconciliation ID','2025-03-14 17:30:12',1, '868 - M/I N Transaction Reconciliation ID')
,(2595,'511-FB','869','M/I N Transaction Source Type','2025-03-14 17:30:12',1, '869 - M/I N Transaction Source Type')
,(2596,'511-FB','870','M/I Prescriber DEA Number','2025-03-14 17:30:12',1, '870 - M/I Prescriber DEA Number')
,(2597,'511-FB','871','M/I Compound Level Of Complexity','2025-03-14 17:30:12',1, '871 - M/I Compound Level Of Complexity')
,(2598,'511-FB','872','Mismatch Between Compound Level Of Complexity And Preparation Environment Type','2025-03-14 17:30:12',1, '872 - Mismatch Between Compound Level Of Complexity And Preparation Environment Type')
,(2599,'511-FB','873','M/I Preparation Environment Type','2025-03-14 17:30:12',1, '873 - M/I Preparation Environment Type')
,(2600,'511-FB','874','M/I Preparation Environment Event Code','2025-03-14 17:30:12',1, '874 - M/I Preparation Environment Event Code')
,(2601,'511-FB','875','M/I Total Prescribed Quantity Remaining','2025-03-14 17:30:12',1, '875 - M/I Total Prescribed Quantity Remaining')
,(2602,'511-FB','876','Prescriptive Authority Restrictions Apply, Criteria Not Met','2025-03-14 17:30:12',1, '876 - Prescriptive Authority Restrictions Apply, Criteria Not Met')
,(2603,'511-FB','877','Service Provider ID Terminated  On NPPES File','2025-03-14 17:30:12',1, '877 - Service Provider ID Terminated  On NPPES File')
,(2604,'511-FB','878','Service Provider ID Not Found On NPPES File','2025-03-14 17:30:12',1, '878 - Service Provider ID Not Found On NPPES File')
,(2605,'511-FB','879','Service Provider ID Excluded From Receiving CMS Enrollment Data','2025-03-14 17:30:12',1, '879 - Service Provider ID Excluded From Receiving CMS Enrollment Data')
,(2606,'511-FB','880','M/I Submission Type Code','2025-03-14 17:30:12',1, '880 - M/I Submission Type Code')
,(2607,'511-FB','881','Missing Submission Type Code Count','2025-03-14 17:30:12',1, '881 - Missing Submission Type Code Count')
,(2608,'511-FB','882','M/I Do Not Dispense Before Date','2025-03-14 17:30:12',1, '882 - M/I Do Not Dispense Before Date')
,(2609,'511-FB','883','Date of Service Prior To Do Not Dispense Before Date','2025-03-14 17:30:12',1, '883 - Date of Service Prior To Do Not Dispense Before Date')
,(2610,'511-FB','884','M/I Multiple RX Order Group Reason Code','2025-03-14 17:30:12',1, '884 - M/I Multiple RX Order Group Reason Code')
,(2611,'511-FB','885','M/I Multiple RX Order Group ID','2025-03-14 17:30:12',1, '885 - M/I Multiple RX Order Group ID')
,(2612,'511-FB','886','M/I Prescriber Place of Service','2025-03-14 17:30:12',1, '886 - M/I Prescriber Place of Service')
,(2613,'511-FB','887','A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed','2025-03-14 17:30:12',1, '887 - A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed')
,(2614,'511-FB','888','Beneficiary Is Enrolled In Excluded Federal Health Care Program','2025-03-14 17:30:12',1, '888 - Beneficiary Is Enrolled In Excluded Federal Health Care Program')
,(2615,'511-FB','889','Prescriber Not Enrolled in State Medicaid Program','2025-03-14 17:30:12',1, '889 - Prescriber Not Enrolled in State Medicaid Program')
,(2616,'511-FB','890','Pharmacy Not Enrolled in State Medicaid Program','2025-03-14 17:30:12',1, '890 - Pharmacy Not Enrolled in State Medicaid Program')
,(2617,'511-FB','891','Days Supply Is Less Than Plan Minimum','2025-03-14 17:30:12',1, '891 - Days Supply Is Less Than Plan Minimum')
,(2618,'511-FB','892','Pharmacy Must Attest FDA REMS Requirements Have Been Met','2025-03-14 17:30:12',1, '892 - Pharmacy Must Attest FDA REMS Requirements Have Been Met')
,(2619,'511-FB','893','Pharmacy Must Attest Required Patient Form Is On File','2025-03-14 17:30:12',1, '893 - Pharmacy Must Attest Required Patient Form Is On File')
,(2620,'511-FB','894','Pharmacy Must Attest Plan Medical Necessity Criteria Has Been Met','2025-03-14 17:30:12',1, '894 - Pharmacy Must Attest Plan Medical Necessity Criteria Has Been Met')
,(2621,'511-FB','895','Allowed Number of Overrides Exhausted','2025-03-14 17:30:12',1, '895 - Allowed Number of Overrides Exhausted')
,(2622,'511-FB','896','Other Adjudicated Program Type Of Unknown Is Not Covered','2025-03-14 17:30:12',1, '896 - Other Adjudicated Program Type Of Unknown Is Not Covered')
,(2623,'511-FB','01','M/I IIN Number','2025-03-14 17:30:12',1, '01 - M/I IIN Number')
,(2624,'511-FB','02','M/I Version/Release Number','2025-03-14 17:30:12',1, '02 - M/I Version/Release Number')
,(2625,'511-FB','03','M/I Transaction Code','2025-03-14 17:30:12',1, '03 - M/I Transaction Code')
,(2626,'511-FB','04','M/I Processor Control Number','2025-03-14 17:30:12',1, '04 - M/I Processor Control Number')
,(2627,'511-FB','05','M/I Service Provider Number','2025-03-14 17:30:12',1, '05 - M/I Service Provider Number')
,(2628,'511-FB','06','M/I Group ID','2025-03-14 17:30:12',1, '06 - M/I Group ID')
,(2629,'511-FB','07','M/I Cardholder ID','2025-03-14 17:30:12',1, '07 - M/I Cardholder ID')
,(2630,'511-FB','08','M/I Person Code','2025-03-14 17:30:12',1, '08 - M/I Person Code')
,(2631,'511-FB','09','M/I Date Of Birth','2025-03-14 17:30:12',1, '09 - M/I Date Of Birth')
,(2632,'511-FB','1C','M/I Smoker/Non-Smoker Code','2025-03-14 17:30:12',1, '1C - M/I Smoker/Non-Smoker Code')
,(2633,'511-FB','1E','M/I Prescriber Location Code','2025-03-14 17:30:12',1, '1E - M/I Prescriber Location Code')
,(2634,'511-FB','1K','M/I Patient Country Code','2025-03-14 17:30:12',1, '1K - M/I Patient Country Code')
,(2635,'511-FB','1R','Version/Release Value Not Supported','2025-03-14 17:30:12',1, '1R - Version/Release Value Not Supported')
,(2636,'511-FB','1S','Transaction Code/Type Value Not Supported','2025-03-14 17:30:12',1, '1S - Transaction Code/Type Value Not Supported')
,(2637,'511-FB','1T','PCN Must Contain Processor/Payer Assigned Value','2025-03-14 17:30:12',1, '1T - PCN Must Contain Processor/Payer Assigned Value')
,(2638,'511-FB','1U','Transaction Count Does Not Match Number of Transactions','2025-03-14 17:30:12',1, '1U - Transaction Count Does Not Match Number of Transactions')
,(2639,'511-FB','1V','Multiple Transactions Not Supported','2025-03-14 17:30:12',1, '1V - Multiple Transactions Not Supported')
,(2640,'511-FB','1X','Vendor Not Certified For Processor/Payer','2025-03-14 17:30:12',1, '1X - Vendor Not Certified For Processor/Payer')
,(2641,'511-FB','1Y','Claim Segment Required For Adjudication','2025-03-14 17:30:12',1, '1Y - Claim Segment Required For Adjudication')
,(2642,'511-FB','1Z','Clinical Segment Required For Adjudication','2025-03-14 17:30:12',1, '1Z - Clinical Segment Required For Adjudication')
,(2643,'511-FB','2A','M/I Medigap ID','2025-03-14 17:30:12',1, '2A - M/I Medigap ID')
,(2644,'511-FB','2B','M/I Medicaid Indicator','2025-03-14 17:30:12',1, '2B - M/I Medicaid Indicator')
,(2645,'511-FB','2C','M/I Pregnancy Indicator','2025-03-14 17:30:12',1, '2C - M/I Pregnancy Indicator')
,(2646,'511-FB','2D','M/I Provider Accept Assignment Indicator','2025-03-14 17:30:12',1, '2D - M/I Provider Accept Assignment Indicator')
,(2647,'511-FB','2E','M/I Primary Care Provider ID Qualifier','2025-03-14 17:30:12',1, '2E - M/I Primary Care Provider ID Qualifier')
,(2648,'511-FB','2G','M/I Compound Ingredient Modifier Code Count','2025-03-14 17:30:12',1, '2G - M/I Compound Ingredient Modifier Code Count')
,(2649,'511-FB','2H','M/I Compound Ingredient Modifier Code','2025-03-14 17:30:12',1, '2H - M/I Compound Ingredient Modifier Code')
,(2650,'511-FB','2J','M/I Prescriber First Name','2025-03-14 17:30:12',1, '2J - M/I Prescriber First Name')
,(2651,'511-FB','2K','M/I Prescriber Street Address','2025-03-14 17:30:12',1, '2K - M/I Prescriber Street Address')
,(2652,'511-FB','2M','M/I Prescriber City Address','2025-03-14 17:30:12',1, '2M - M/I Prescriber City Address')
,(2653,'511-FB','2N','M/I Prescriber State/Province Address','2025-03-14 17:30:12',1, '2N - M/I Prescriber State/Province Address')
,(2654,'511-FB','2P','M/I Prescriber Zip/Postal Zone','2025-03-14 17:30:12',1, '2P - M/I Prescriber Zip/Postal Zone')
,(2655,'511-FB','2Q','M/I Additional Documentation Type ID','2025-03-14 17:30:12',1, '2Q - M/I Additional Documentation Type ID')
,(2656,'511-FB','2R','M/I Length Of Need','2025-03-14 17:30:12',1, '2R - M/I Length Of Need')
,(2657,'511-FB','2S','M/I Length Of Need Qualifier','2025-03-14 17:30:12',1, '2S - M/I Length Of Need Qualifier')
,(2658,'511-FB','2T','M/I Prescriber/Supplier Date Signed','2025-03-14 17:30:12',1, '2T - M/I Prescriber/Supplier Date Signed')
,(2659,'511-FB','2U','M/I Request Status','2025-03-14 17:30:12',1, '2U - M/I Request Status')
,(2660,'511-FB','2V','M/I Request Period Begin Date','2025-03-14 17:30:12',1, '2V - M/I Request Period Begin Date')
,(2661,'511-FB','2W','M/I Request Period Recert/Revised Date','2025-03-14 17:30:12',1, '2W - M/I Request Period Recert/Revised Date')
,(2662,'511-FB','2X','M/I Supporting Documentation','2025-03-14 17:30:12',1, '2X - M/I Supporting Documentation')
,(2663,'511-FB','2Z','M/I Question Number/Letter Count','2025-03-14 17:30:12',1, '2Z - M/I Question Number/Letter Count')
,(2664,'511-FB','3A','M/I Request Type','2025-03-14 17:30:12',1, '3A - M/I Request Type')
,(2665,'511-FB','3B','M/I Request Period Date-Begin','2025-03-14 17:30:12',1, '3B - M/I Request Period Date-Begin')
,(2666,'511-FB','3C','M/I Request Period Date-End','2025-03-14 17:30:12',1, '3C - M/I Request Period Date-End')
,(2667,'511-FB','3D','M/I Basis Of Request','2025-03-14 17:30:12',1, '3D - M/I Basis Of Request')
,(2668,'511-FB','3E','M/I Authorized Representative First Name','2025-03-14 17:30:12',1, '3E - M/I Authorized Representative First Name')
,(2669,'511-FB','3F','M/I Authorized Representative Last Name','2025-03-14 17:30:12',1, '3F - M/I Authorized Representative Last Name')
,(2670,'511-FB','3G','M/I Authorized Representative Street Address','2025-03-14 17:30:12',1, '3G - M/I Authorized Representative Street Address')
,(2671,'511-FB','3H','M/I Authorized Representative City Address','2025-03-14 17:30:12',1, '3H - M/I Authorized Representative City Address')
,(2672,'511-FB','3J','M/I Authorized Representative State/Province Address','2025-03-14 17:30:12',1, '3J - M/I Authorized Representative State/Province Address')
,(2673,'511-FB','3K','M/I Authorized Representative Zip/Postal Zone','2025-03-14 17:30:12',1, '3K - M/I Authorized Representative Zip/Postal Zone')
,(2674,'511-FB','3M','M/I Prescriber Phone Number','2025-03-14 17:30:12',1, '3M - M/I Prescriber Phone Number')
,(2675,'511-FB','3N','M/I Prior Authorization ID Assigned','2025-03-14 17:30:12',1, '3N - M/I Prior Authorization ID Assigned')
,(2676,'511-FB','3P','M/I Authorization Number','2025-03-14 17:30:12',1, '3P - M/I Authorization Number')
,(2677,'511-FB','3Q','M/I Facility Name','2025-03-14 17:30:12',1, '3Q - M/I Facility Name')
,(2678,'511-FB','3R','Prior Authorization Not Required','2025-03-14 17:30:12',1, '3R - Prior Authorization Not Required')
,(2679,'511-FB','3S','M/I Prior Authorization Supporting Documentation','2025-03-14 17:30:12',1, '3S - M/I Prior Authorization Supporting Documentation')
,(2680,'511-FB','3T','Active Prior Authorization Exists Resubmit At Expiration Of Prior Authorization','2025-03-14 17:30:12',1, '3T - Active Prior Authorization Exists Resubmit At Expiration Of Prior Authorization')
,(2681,'511-FB','3U','M/I Facility Street Address','2025-03-14 17:30:12',1, '3U - M/I Facility Street Address')
,(2682,'511-FB','3V','M/I Facility State/Province Address','2025-03-14 17:30:12',1, '3V - M/I Facility State/Province Address')
,(2683,'511-FB','3W','Prior Authorization In Process','2025-03-14 17:30:12',1, '3W - Prior Authorization In Process')
,(2684,'511-FB','3X','Authorization Number Not Found','2025-03-14 17:30:12',1, '3X - Authorization Number Not Found')
,(2685,'511-FB','3Y','Prior Authorization Denied','2025-03-14 17:30:12',1, '3Y - Prior Authorization Denied')
,(2686,'511-FB','4B','M/I Question Number/Letter','2025-03-14 17:30:12',1, '4B - M/I Question Number/Letter')
,(2687,'511-FB','4C','M/I Coordination Of Benefits/Other Payments Count','2025-03-14 17:30:12',1, '4C - M/I Coordination Of Benefits/Other Payments Count')
,(2688,'511-FB','4D','M/I Question Percent Response','2025-03-14 17:30:12',1, '4D - M/I Question Percent Response')
,(2689,'511-FB','4E','M/I Primary Care Provider Last Name','2025-03-14 17:30:12',1, '4E - M/I Primary Care Provider Last Name')
,(2690,'511-FB','4G','M/I Question Date Response','2025-03-14 17:30:12',1, '4G - M/I Question Date Response')
,(2691,'511-FB','4H','M/I Question Dollar Amount Response','2025-03-14 17:30:12',1, '4H - M/I Question Dollar Amount Response')
,(2692,'511-FB','4J','M/I Question Numeric Response','2025-03-14 17:30:12',1, '4J - M/I Question Numeric Response')
,(2693,'511-FB','4K','M/I Question Alphanumeric Response','2025-03-14 17:30:12',1, '4K - M/I Question Alphanumeric Response')
,(2694,'511-FB','4M','Compound Ingredient Modifier Code Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, '4M - Compound Ingredient Modifier Code Count Does Not Match Number Of Repetitions')
,(2695,'511-FB','4N','Question Number/Letter Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, '4N - Question Number/Letter Count Does Not Match Number Of Repetitions')
,(2696,'511-FB','4P','Question Number/Letter Not Valid For Identified Document','2025-03-14 17:30:12',1, '4P - Question Number/Letter Not Valid For Identified Document')
,(2697,'511-FB','4Q','Question Response Not Appropriate For Question Number/Letter','2025-03-14 17:30:12',1, '4Q - Question Response Not Appropriate For Question Number/Letter')
,(2698,'511-FB','4R','Required Question Number/Letter Response For Indicated Document Missing','2025-03-14 17:30:12',1, '4R - Required Question Number/Letter Response For Indicated Document Missing')
,(2699,'511-FB','4S','Compound Product ID Requires A Modifier Code','2025-03-14 17:30:12',1, '4S - Compound Product ID Requires A Modifier Code')
,(2700,'511-FB','4T','M/I Additional Documentation Segment','2025-03-14 17:30:12',1, '4T - M/I Additional Documentation Segment')
,(2701,'511-FB','4W','Must Dispense Through Specialty Pharmacy','2025-03-14 17:30:12',1, '4W - Must Dispense Through Specialty Pharmacy')
,(2702,'511-FB','4X','M/I Patient Residence','2025-03-14 17:30:12',1, '4X - M/I Patient Residence')
,(2703,'511-FB','4Y','Patient Residence Value Not Supported','2025-03-14 17:30:12',1, '4Y - Patient Residence Value Not Supported')
,(2704,'511-FB','4Z','Place of Service Not Supported By Plan','2025-03-14 17:30:12',1, '4Z - Place of Service Not Supported By Plan')
,(2705,'511-FB','5C','M/I Other Payer Coverage Type','2025-03-14 17:30:12',1, '5C - M/I Other Payer Coverage Type')
,(2706,'511-FB','5E','M/I Other Payer Reject Count','2025-03-14 17:30:12',1, '5E - M/I Other Payer Reject Count')
,(2707,'511-FB','5J','M/I Facility City Address','2025-03-14 17:30:12',1, '5J - M/I Facility City Address')
,(2708,'511-FB','6C','M/I Other Payer ID Qualifier','2025-03-14 17:30:12',1, '6C - M/I Other Payer ID Qualifier')
,(2709,'511-FB','6D','M/I Facility ZIP/Postal Zone','2025-03-14 17:30:12',1, '6D - M/I Facility ZIP/Postal Zone')
,(2710,'511-FB','6E','M/I Other Payer Reject Code','2025-03-14 17:30:12',1, '6E - M/I Other Payer Reject Code')
,(2711,'511-FB','6G','Coordination Of Benefits/Other Payments Segment Required For Adjudication','2025-03-14 17:30:12',1, '6G - Coordination Of Benefits/Other Payments Segment Required For Adjudication')
,(2712,'511-FB','6H','Coupon Segment Required For Adjudication','2025-03-14 17:30:12',1, '6H - Coupon Segment Required For Adjudication')
,(2713,'511-FB','6J','Insurance Segment Required For Adjudication','2025-03-14 17:30:12',1, '6J - Insurance Segment Required For Adjudication')
,(2714,'511-FB','6K','Patient Segment Required For Adjudication','2025-03-14 17:30:12',1, '6K - Patient Segment Required For Adjudication')
,(2715,'511-FB','6M','Pharmacy Provider Segment Required For Adjudication','2025-03-14 17:30:12',1, '6M - Pharmacy Provider Segment Required For Adjudication')
,(2716,'511-FB','6N','Prescriber Segment Required For Adjudication','2025-03-14 17:30:12',1, '6N - Prescriber Segment Required For Adjudication')
,(2717,'511-FB','6P','Pricing Segment Required For Adjudication','2025-03-14 17:30:12',1, '6P - Pricing Segment Required For Adjudication')
,(2718,'511-FB','6Q','Prior Authorization Segment Required For Adjudication','2025-03-14 17:30:12',1, '6Q - Prior Authorization Segment Required For Adjudication')
,(2719,'511-FB','6R','Worker''s Compensation Segment Required For Adjudication','2025-03-14 17:30:12',1, '6R - Worker''s Compensation Segment Required For Adjudication')
,(2720,'511-FB','6S','Transaction Segment Required For Adjudication','2025-03-14 17:30:12',1, '6S - Transaction Segment Required For Adjudication')
,(2721,'511-FB','6T','Compound Segment Required For Adjudication','2025-03-14 17:30:12',1, '6T - Compound Segment Required For Adjudication')
,(2722,'511-FB','6U','Compound Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '6U - Compound Segment Incorrectly Formatted')
,(2723,'511-FB','6V','Multi-ingredient Compounds Not Supported','2025-03-14 17:30:12',1, '6V - Multi-ingredient Compounds Not Supported')
,(2724,'511-FB','6W','DUR/PPS Segment Required For Adjudication','2025-03-14 17:30:12',1, '6W - DUR/PPS Segment Required For Adjudication')
,(2725,'511-FB','6X','DUR/PPS Segment Incorrectly Formatted','2025-03-14 17:30:12',1, '6X - DUR/PPS Segment Incorrectly Formatted')
,(2726,'511-FB','6Y','Not Authorized To Submit Electronically','2025-03-14 17:30:12',1, '6Y - Not Authorized To Submit Electronically')
,(2727,'511-FB','6Z','Provider Not Eligible To Perform Service/Dispense Product','2025-03-14 17:30:12',1, '6Z - Provider Not Eligible To Perform Service/Dispense Product')
,(2728,'511-FB','7A','Provider Does Not Match Authorization On File','2025-03-14 17:30:12',1, '7A - Provider Does Not Match Authorization On File')
,(2729,'511-FB','7B','Service Provider ID Qualifier Value Not Supported For Processor/Payer','2025-03-14 17:30:12',1, '7B - Service Provider ID Qualifier Value Not Supported For Processor/Payer')
,(2730,'511-FB','7C','M/I Other Payer ID','2025-03-14 17:30:12',1, '7C - M/I Other Payer ID')
,(2731,'511-FB','7D','Non-Matched DOB','2025-03-14 17:30:12',1, '7D - Non-Matched DOB')
,(2732,'511-FB','7E','M/I DUR/PPS Code Counter','2025-03-14 17:30:12',1, '7E - M/I DUR/PPS Code Counter')
,(2733,'511-FB','7G','Future Date Not Allowed For DOB','2025-03-14 17:30:12',1, '7G - Future Date Not Allowed For DOB')
,(2734,'511-FB','7H','Non-Matched Gender Code','2025-03-14 17:30:12',1, '7H - Non-Matched Gender Code')
,(2735,'511-FB','7J','Patient Relationship Code Value Not Supported','2025-03-14 17:30:12',1, '7J - Patient Relationship Code Value Not Supported')
,(2736,'511-FB','7K','Discrepancy Between Other Coverage Code And Other Payer Amount','2025-03-14 17:30:12',1, '7K - Discrepancy Between Other Coverage Code And Other Payer Amount')
,(2737,'511-FB','7M','Discrepancy Between Other Coverage Code And Other Coverage Information On File','2025-03-14 17:30:12',1, '7M - Discrepancy Between Other Coverage Code And Other Coverage Information On File')
,(2738,'511-FB','7N','Patient ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '7N - Patient ID Qualifier Value Not Supported')
,(2739,'511-FB','7P','Coordination Of Benefits/Other Payments Count Exceeds Number of Supported Payers','2025-03-14 17:30:12',1, '7P - Coordination Of Benefits/Other Payments Count Exceeds Number of Supported Payers')
,(2740,'511-FB','7Q','Other Payer ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '7Q - Other Payer ID Qualifier Value Not Supported')
,(2741,'511-FB','7R','Other Payer Amount Paid Count Exceeds Number Of Supported Groupings','2025-03-14 17:30:12',1, '7R - Other Payer Amount Paid Count Exceeds Number Of Supported Groupings')
,(2742,'511-FB','7S','Other Payer Amount Paid Qualifier Value Not Supported','2025-03-14 17:30:12',1, '7S - Other Payer Amount Paid Qualifier Value Not Supported')
,(2743,'511-FB','7T','Quantity Intended To Be Dispensed Required For Partial Fill Transaction','2025-03-14 17:30:12',1, '7T - Quantity Intended To Be Dispensed Required For Partial Fill Transaction')
,(2744,'511-FB','7U','Days Supply Intended To Be Dispensed Required For Partial Fill Transaction','2025-03-14 17:30:12',1, '7U - Days Supply Intended To Be Dispensed Required For Partial Fill Transaction')
,(2745,'511-FB','7V','Duplicate Fill #','2025-03-14 17:30:12',1, '7V - Duplicate Fill #')
,(2746,'511-FB','7W','Number Of Refills Authorized Exceed Allowable Refills','2025-03-14 17:30:12',1, '7W - Number Of Refills Authorized Exceed Allowable Refills')
,(2747,'511-FB','7X','Days Supply Exceeds Plan Limitation','2025-03-14 17:30:12',1, '7X - Days Supply Exceeds Plan Limitation')
,(2748,'511-FB','7Y','Compounds Not Covered','2025-03-14 17:30:12',1, '7Y - Compounds Not Covered')
,(2749,'511-FB','7Z','Compound Requires Two Or More Ingredients','2025-03-14 17:30:12',1, '7Z - Compound Requires Two Or More Ingredients')
,(2750,'511-FB','8A','Compound Requires At Least One Covered Ingredient','2025-03-14 17:30:12',1, '8A - Compound Requires At Least One Covered Ingredient')
,(2751,'511-FB','8B','Compound Segment Missing On A Compound Claim','2025-03-14 17:30:12',1, '8B - Compound Segment Missing On A Compound Claim')
,(2752,'511-FB','8C','M/I Facility ID','2025-03-14 17:30:12',1, '8C - M/I Facility ID')
,(2753,'511-FB','8D','Compound Segment Present On A Non-Compound Claim','2025-03-14 17:30:12',1, '8D - Compound Segment Present On A Non-Compound Claim')
,(2754,'511-FB','8E','M/I DUR/PPS Level Of Effort','2025-03-14 17:30:12',1, '8E - M/I DUR/PPS Level Of Effort')
,(2755,'511-FB','8G','Product/Service ID (407-D7) Must Be A Single Zero For Compounds','2025-03-14 17:30:12',1, '8G - Product/Service ID (407-D7) Must Be A Single Zero For Compounds')
,(2756,'511-FB','8H','Product/Service Only Covered On Compound Claim','2025-03-14 17:30:12',1, '8H - Product/Service Only Covered On Compound Claim')
,(2757,'511-FB','8J','Incorrect Product/Service ID For Processor/Payer','2025-03-14 17:30:12',1, '8J - Incorrect Product/Service ID For Processor/Payer')
,(2758,'511-FB','8K','DAW Code Value Not Supported','2025-03-14 17:30:12',1, '8K - DAW Code Value Not Supported')
,(2759,'511-FB','8M','Sum Of Compound Ingredient Costs Does Not Equal Ingredient Cost Submitted','2025-03-14 17:30:12',1, '8M - Sum Of Compound Ingredient Costs Does Not Equal Ingredient Cost Submitted')
,(2760,'511-FB','8N','Future Date Prescription Written Not Allowed','2025-03-14 17:30:12',1, '8N - Future Date Prescription Written Not Allowed')
,(2761,'511-FB','8P','Date Written Different On Previous Fill','2025-03-14 17:30:12',1, '8P - Date Written Different On Previous Fill')
,(2762,'511-FB','8Q','Excessive Refills Authorized','2025-03-14 17:30:12',1, '8Q - Excessive Refills Authorized')
,(2763,'511-FB','8R','Submission Clarification Code Value Not Supported','2025-03-14 17:30:12',1, '8R - Submission Clarification Code Value Not Supported')
,(2764,'511-FB','8S','Basis Of Cost  Determination Value Not Supported','2025-03-14 17:30:12',1, '8S - Basis Of Cost  Determination Value Not Supported')
,(2765,'511-FB','8T','U&C Must Be Greater Than Zero','2025-03-14 17:30:12',1, '8T - U&C Must Be Greater Than Zero')
,(2766,'511-FB','8U','GAD Must Be Greater Than Zero','2025-03-14 17:30:12',1, '8U - GAD Must Be Greater Than Zero')
,(2767,'511-FB','8W','Discrepancy Between Other Coverage Code And Other Payer Amount Paid','2025-03-14 17:30:12',1, '8W - Discrepancy Between Other Coverage Code And Other Payer Amount Paid')
,(2768,'511-FB','8X','Collection From Cardholder Not Allowed','2025-03-14 17:30:12',1, '8X - Collection From Cardholder Not Allowed')
,(2769,'511-FB','8Y','Excessive Amount Collected','2025-03-14 17:30:12',1, '8Y - Excessive Amount Collected')
,(2770,'511-FB','8Z','Product/Service ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, '8Z - Product/Service ID Qualifier Value Not Supported')
,(2771,'511-FB','9B','Reason For Service Code Value Not Supported','2025-03-14 17:30:12',1, '9B - Reason For Service Code Value Not Supported')
,(2772,'511-FB','9C','Professional Service Code Value Not Supported','2025-03-14 17:30:12',1, '9C - Professional Service Code Value Not Supported')
,(2773,'511-FB','9D','Result Of Service Code Value Not Supported','2025-03-14 17:30:12',1, '9D - Result Of Service Code Value Not Supported')
,(2774,'511-FB','9E','Quantity Does Not Match Dispensing Unit','2025-03-14 17:30:12',1, '9E - Quantity Does Not Match Dispensing Unit')
,(2775,'511-FB','9G','Quantity Dispensed Exceeds Maximum Allowed','2025-03-14 17:30:12',1, '9G - Quantity Dispensed Exceeds Maximum Allowed')
,(2776,'511-FB','9H','Quantity Not Valid For Product/Service ID Submitted','2025-03-14 17:30:12',1, '9H - Quantity Not Valid For Product/Service ID Submitted')
,(2777,'511-FB','9J','Future Other Payer Date Not Allowed','2025-03-14 17:30:12',1, '9J - Future Other Payer Date Not Allowed')
,(2778,'511-FB','9K','Compound Ingredient Component Count Exceeds Number Of Ingredients Supported','2025-03-14 17:30:12',1, '9K - Compound Ingredient Component Count Exceeds Number Of Ingredients Supported')
,(2779,'511-FB','9M','Minimum Of Two Ingredients Required','2025-03-14 17:30:12',1, '9M - Minimum Of Two Ingredients Required')
,(2780,'511-FB','9N','Compound Ingredient Quantity Exceeds Maximum Allowed','2025-03-14 17:30:12',1, '9N - Compound Ingredient Quantity Exceeds Maximum Allowed')
,(2781,'511-FB','9P','Compound Ingredient Drug Cost Must Be Greater Than Zero','2025-03-14 17:30:12',1, '9P - Compound Ingredient Drug Cost Must Be Greater Than Zero')
,(2782,'511-FB','9Q','Route Of Administration Submitted Not Covered','2025-03-14 17:30:12',1, '9Q - Route Of Administration Submitted Not Covered')
,(2783,'511-FB','9R','Prescription/Service Reference Number Qualifier Submitted Not Covered','2025-03-14 17:30:12',1, '9R - Prescription/Service Reference Number Qualifier Submitted Not Covered')
,(2784,'511-FB','9S','Future Associated Prescription/Service Date Not Allowed','2025-03-14 17:30:12',1, '9S - Future Associated Prescription/Service Date Not Allowed')
,(2785,'511-FB','9T','Prior Authorization Type Code Submitted Not Covered','2025-03-14 17:30:12',1, '9T - Prior Authorization Type Code Submitted Not Covered')
,(2786,'511-FB','9U','Provider ID Qualifier Submitted Not Covered','2025-03-14 17:30:12',1, '9U - Provider ID Qualifier Submitted Not Covered')
,(2787,'511-FB','9V','Prescriber ID Qualifier Submitted Not Covered','2025-03-14 17:30:12',1, '9V - Prescriber ID Qualifier Submitted Not Covered')
,(2788,'511-FB','9W','DUR/PPS Code Counter Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, '9W - DUR/PPS Code Counter Exceeds Number Of Occurrences Supported')
,(2789,'511-FB','9X','Coupon Type Submitted Not Covered','2025-03-14 17:30:12',1, '9X - Coupon Type Submitted Not Covered')
,(2790,'511-FB','9Y','Compound Product ID Qualifier Submitted Not Covered','2025-03-14 17:30:12',1, '9Y - Compound Product ID Qualifier Submitted Not Covered')
,(2791,'511-FB','9Z','Duplicate Product ID In Compound','2025-03-14 17:30:12',1, '9Z - Duplicate Product ID In Compound')
,(2792,'511-FB','AA','Patient Spenddown Not Met','2025-03-14 17:30:12',1, 'AA - Patient Spenddown Not Met')
,(2793,'511-FB','AB','Date Written Is After Date Of Service','2025-03-14 17:30:12',1, 'AB - Date Written Is After Date Of Service')
,(2794,'511-FB','AC','Product Not Covered Non-Participating Manufacturer','2025-03-14 17:30:12',1, 'AC - Product Not Covered Non-Participating Manufacturer')
,(2795,'511-FB','AD','Billing Provider Not Eligible To Bill This Claim Type','2025-03-14 17:30:12',1, 'AD - Billing Provider Not Eligible To Bill This Claim Type')
,(2796,'511-FB','AE','QMB (Qualified Medicare Beneficiary)-Bill Medicare','2025-03-14 17:30:12',1, 'AE - QMB (Qualified Medicare Beneficiary)-Bill Medicare')
,(2797,'511-FB','AF','Patient Enrolled Under Managed Care','2025-03-14 17:30:12',1, 'AF - Patient Enrolled Under Managed Care')
,(2798,'511-FB','AG','Days Supply Limitation For Product/Service','2025-03-14 17:30:12',1, 'AG - Days Supply Limitation For Product/Service')
,(2799,'511-FB','AH','Unit Dose Packaging Only Payable For Nursing Home Recipients','2025-03-14 17:30:12',1, 'AH - Unit Dose Packaging Only Payable For Nursing Home Recipients')
,(2800,'511-FB','AJ','Generic Drug Required','2025-03-14 17:30:12',1, 'AJ - Generic Drug Required')
,(2801,'511-FB','AK','M/I Software Vendor/Certification ID','2025-03-14 17:30:12',1, 'AK - M/I Software Vendor/Certification ID')
,(2802,'511-FB','AM','M/I Segment Identification','2025-03-14 17:30:12',1, 'AM - M/I Segment Identification')
,(2803,'511-FB','AQ','M/I Facility Segment','2025-03-14 17:30:12',1, 'AQ - M/I Facility Segment')
,(2804,'511-FB','A1','ID Submitted Is Associated With An Excluded Prescriber','2025-03-14 17:30:12',1, 'A1 - ID Submitted Is Associated With An Excluded Prescriber')
,(2805,'511-FB','A2','ID Submitted Is Associated To A Deceased Prescriber','2025-03-14 17:30:12',1, 'A2 - ID Submitted Is Associated To A Deceased Prescriber')
,(2806,'511-FB','A5','Not Covered Under Part D Law','2025-03-14 17:30:12',1, 'A5 - Not Covered Under Part D Law')
,(2807,'511-FB','A6','This Product/Service May Be Covered Under Medicare Part B','2025-03-14 17:30:12',1, 'A6 - This Product/Service May Be Covered Under Medicare Part B')
,(2808,'511-FB','A7','M/I Internal Control Number','2025-03-14 17:30:12',1, 'A7 - M/I Internal Control Number')
,(2809,'511-FB','A9','M/I Transaction Count','2025-03-14 17:30:12',1, 'A9 - M/I Transaction Count')
,(2810,'511-FB','BA','Compound Basis Of Cost Determination Submitted Not Covered','2025-03-14 17:30:12',1, 'BA - Compound Basis Of Cost Determination Submitted Not Covered')
,(2811,'511-FB','BB','Diagnosis Code Qualifier Submitted Not Covered','2025-03-14 17:30:12',1, 'BB - Diagnosis Code Qualifier Submitted Not Covered')
,(2812,'511-FB','BC','Future Measurement Date Not Allowed','2025-03-14 17:30:12',1, 'BC - Future Measurement Date Not Allowed')
,(2813,'511-FB','BE','M/I Professional Service Fee Submitted','2025-03-14 17:30:12',1, 'BE - M/I Professional Service Fee Submitted')
,(2814,'511-FB','BM','M/I Narrative Message','2025-03-14 17:30:12',1, 'BM - M/I Narrative Message')
,(2815,'511-FB','B2','M/I Service Provider ID Qualifier','2025-03-14 17:30:12',1, 'B2 - M/I Service Provider ID Qualifier')
,(2816,'511-FB','CA','M/I Patient First Name','2025-03-14 17:30:12',1, 'CA - M/I Patient First Name')
,(2817,'511-FB','CB','M/I Patient Last Name','2025-03-14 17:30:12',1, 'CB - M/I Patient Last Name')
,(2818,'511-FB','CC','M/I Cardholder First Name','2025-03-14 17:30:12',1, 'CC - M/I Cardholder First Name')
,(2819,'511-FB','CD','M/I Cardholder Last Name','2025-03-14 17:30:12',1, 'CD - M/I Cardholder Last Name')
,(2820,'511-FB','CE','M/I Home Plan','2025-03-14 17:30:12',1, 'CE - M/I Home Plan')
,(2821,'511-FB','CF','M/I Employer Name','2025-03-14 17:30:12',1, 'CF - M/I Employer Name')
,(2822,'511-FB','CG','M/I Employer Street Address','2025-03-14 17:30:12',1, 'CG - M/I Employer Street Address')
,(2823,'511-FB','CH','M/I Employer City Address','2025-03-14 17:30:12',1, 'CH - M/I Employer City Address')
,(2824,'511-FB','CI','M/I Employer State/Province Address','2025-03-14 17:30:12',1, 'CI - M/I Employer State/Province Address')
,(2825,'511-FB','CJ','M/I Employer ZIP Postal Zone','2025-03-14 17:30:12',1, 'CJ - M/I Employer ZIP Postal Zone')
,(2826,'511-FB','CK','M/I Employer Phone Number','2025-03-14 17:30:12',1, 'CK - M/I Employer Phone Number')
,(2827,'511-FB','CL','M/I Employer Contact Name','2025-03-14 17:30:12',1, 'CL - M/I Employer Contact Name')
,(2828,'511-FB','CM','M/I Patient Street Address','2025-03-14 17:30:12',1, 'CM - M/I Patient Street Address')
,(2829,'511-FB','CN','M/I Patient City Address','2025-03-14 17:30:12',1, 'CN - M/I Patient City Address')
,(2830,'511-FB','CO','M/I Patient State/Province Address','2025-03-14 17:30:12',1, 'CO - M/I Patient State/Province Address')
,(2831,'511-FB','CP','M/I Patient ZIP/Postal Zone','2025-03-14 17:30:12',1, 'CP - M/I Patient ZIP/Postal Zone')
,(2832,'511-FB','CQ','M/I Patient Phone Number','2025-03-14 17:30:12',1, 'CQ - M/I Patient Phone Number')
,(2833,'511-FB','CR','M/I Carrier ID','2025-03-14 17:30:12',1, 'CR - M/I Carrier ID')
,(2834,'511-FB','CW','M/I Alternate ID','2025-03-14 17:30:12',1, 'CW - M/I Alternate ID')
,(2835,'511-FB','CX','M/I Patient ID Qualifier','2025-03-14 17:30:12',1, 'CX - M/I Patient ID Qualifier')
,(2836,'511-FB','CY','M/I Patient ID','2025-03-14 17:30:12',1, 'CY - M/I Patient ID')
,(2837,'511-FB','CZ','M/I Employer ID','2025-03-14 17:30:12',1, 'CZ - M/I Employer ID')
,(2838,'511-FB','DC','M/I Dispensing Fee Submitted','2025-03-14 17:30:12',1, 'DC - M/I Dispensing Fee Submitted')
,(2839,'511-FB','DN','M/I Basis Of Cost Determination','2025-03-14 17:30:12',1, 'DN - M/I Basis Of Cost Determination')
,(2840,'511-FB','DQ','M/I Usual And Customary Charge','2025-03-14 17:30:12',1, 'DQ - M/I Usual And Customary Charge')
,(2841,'511-FB','DR','M/I Prescriber Last Name','2025-03-14 17:30:12',1, 'DR - M/I Prescriber Last Name')
,(2842,'511-FB','DT','M/I Special Packaging Indicator','2025-03-14 17:30:12',1, 'DT - M/I Special Packaging Indicator')
,(2843,'511-FB','DU','M/I Gross Amount Due','2025-03-14 17:30:12',1, 'DU - M/I Gross Amount Due')
,(2844,'511-FB','DV','M/I Other Payer Amount Paid','2025-03-14 17:30:12',1, 'DV - M/I Other Payer Amount Paid')
,(2845,'511-FB','DX','M/I Patient Paid Amount Submitted','2025-03-14 17:30:12',1, 'DX - M/I Patient Paid Amount Submitted')
,(2846,'511-FB','DY','M/I Date Of Injury','2025-03-14 17:30:12',1, 'DY - M/I Date Of Injury')
,(2847,'511-FB','DZ','M/I Claim/Reference ID','2025-03-14 17:30:12',1, 'DZ - M/I Claim/Reference ID')
,(2848,'511-FB','EA','M/I Originally Prescribed Product/Service Code','2025-03-14 17:30:12',1, 'EA - M/I Originally Prescribed Product/Service Code')
,(2849,'511-FB','EB','M/I Originally Prescribed Quantity','2025-03-14 17:30:12',1, 'EB - M/I Originally Prescribed Quantity')
,(2850,'511-FB','EC','M/I Compound Ingredient Component Count','2025-03-14 17:30:12',1, 'EC - M/I Compound Ingredient Component Count')
,(2851,'511-FB','ED','M/I Compound Ingredient Quantity','2025-03-14 17:30:12',1, 'ED - M/I Compound Ingredient Quantity')
,(2852,'511-FB','EE','M/I Compound Ingredient Drug Cost','2025-03-14 17:30:12',1, 'EE - M/I Compound Ingredient Drug Cost')
,(2853,'511-FB','EF','M/I Compound Dosage Form Description Code','2025-03-14 17:30:12',1, 'EF - M/I Compound Dosage Form Description Code')
,(2854,'511-FB','EG','M/I Compound Dispensing Unit Form Indicator','2025-03-14 17:30:12',1, 'EG - M/I Compound Dispensing Unit Form Indicator')
,(2855,'511-FB','EJ','M/I Originally Prescribed Product/Service ID Qualifier','2025-03-14 17:30:12',1, 'EJ - M/I Originally Prescribed Product/Service ID Qualifier')
,(2856,'511-FB','EK','M/I Scheduled Prescription ID Number','2025-03-14 17:30:12',1, 'EK - M/I Scheduled Prescription ID Number')
,(2857,'511-FB','EM','M/I Prescription/Service Reference Number Qualifier','2025-03-14 17:30:12',1, 'EM - M/I Prescription/Service Reference Number Qualifier')
,(2858,'511-FB','EN','M/I Associated Prescription/Service Reference Number','2025-03-14 17:30:12',1, 'EN - M/I Associated Prescription/Service Reference Number')
,(2859,'511-FB','EP','M/I Associated Prescription/Service Date','2025-03-14 17:30:12',1, 'EP - M/I Associated Prescription/Service Date')
,(2860,'511-FB','ER','M/I Procedure Modifier Code','2025-03-14 17:30:12',1, 'ER - M/I Procedure Modifier Code')
,(2861,'511-FB','ET','M/I Quantity Prescribed','2025-03-14 17:30:12',1, 'ET - M/I Quantity Prescribed')
,(2862,'511-FB','EU','M/I Prior Authorization Type Code','2025-03-14 17:30:12',1, 'EU - M/I Prior Authorization Type Code')
,(2863,'511-FB','EV','M/I Prior Authorization ID Submitted','2025-03-14 17:30:12',1, 'EV - M/I Prior Authorization ID Submitted')
,(2864,'511-FB','EY','M/I Provider ID Qualifier','2025-03-14 17:30:12',1, 'EY - M/I Provider ID Qualifier')
,(2865,'511-FB','EZ','M/I Prescriber ID Qualifier','2025-03-14 17:30:12',1, 'EZ - M/I Prescriber ID Qualifier')
,(2866,'511-FB','E1','M/I Product/Service ID Qualifier','2025-03-14 17:30:12',1, 'E1 - M/I Product/Service ID Qualifier')
,(2867,'511-FB','E2','M/I Route Of Administration','2025-03-14 17:30:12',1, 'E2 - M/I Route Of Administration')
,(2868,'511-FB','E3','M/I Incentive Amount Submitted','2025-03-14 17:30:12',1, 'E3 - M/I Incentive Amount Submitted')
,(2869,'511-FB','E4','M/I Reason For Service Code','2025-03-14 17:30:12',1, 'E4 - M/I Reason For Service Code')
,(2870,'511-FB','E5','M/I Professional Service Code','2025-03-14 17:30:12',1, 'E5 - M/I Professional Service Code')
,(2871,'511-FB','E6','M/I Result Of Service Code','2025-03-14 17:30:12',1, 'E6 - M/I Result Of Service Code')
,(2872,'511-FB','E7','M/I Quantity Dispensed','2025-03-14 17:30:12',1, 'E7 - M/I Quantity Dispensed')
,(2873,'511-FB','E8','M/I Other Payer Date','2025-03-14 17:30:12',1, 'E8 - M/I Other Payer Date')
,(2874,'511-FB','E9','M/I Provider ID','2025-03-14 17:30:12',1, 'E9 - M/I Provider ID')
,(2875,'511-FB','FO','M/I Plan ID','2025-03-14 17:30:12',1, 'FO - M/I Plan ID')
,(2876,'511-FB','GE','M/I Percentage Sales Tax Amount Submitted','2025-03-14 17:30:12',1, 'GE - M/I Percentage Sales Tax Amount Submitted')
,(2877,'511-FB','G1','M/I Compound Type','2025-03-14 17:30:12',1, 'G1 - M/I Compound Type')
,(2878,'511-FB','G2','M/I CMS Part D Defined Qualified Facility','2025-03-14 17:30:12',1, 'G2 - M/I CMS Part D Defined Qualified Facility')
,(2879,'511-FB','G4','Physician Must Contact Plan','2025-03-14 17:30:12',1, 'G4 - Physician Must Contact Plan')
,(2880,'511-FB','G5','Pharmacist Must Contact Plan','2025-03-14 17:30:12',1, 'G5 - Pharmacist Must Contact Plan')
,(2881,'511-FB','G6','Pharmacy Not Contracted In Specialty Network','2025-03-14 17:30:12',1, 'G6 - Pharmacy Not Contracted In Specialty Network')
,(2882,'511-FB','G7','Pharmacy Not Contracted In Home Infusion Network','2025-03-14 17:30:12',1, 'G7 - Pharmacy Not Contracted In Home Infusion Network')
,(2883,'511-FB','G8','Pharmacy Not Contracted In Long Term Care Network','2025-03-14 17:30:12',1, 'G8 - Pharmacy Not Contracted In Long Term Care Network')
,(2884,'511-FB','G9','Pharmacy Not Contracted In 90 Day Retail Network','2025-03-14 17:30:12',1, 'G9 - Pharmacy Not Contracted In 90 Day Retail Network')
,(2885,'511-FB','HA','M/I Flat Sales Tax Amount Submitted','2025-03-14 17:30:12',1, 'HA - M/I Flat Sales Tax Amount Submitted')
,(2886,'511-FB','HB','M/I Other Payer Amount Paid Count','2025-03-14 17:30:12',1, 'HB - M/I Other Payer Amount Paid Count')
,(2887,'511-FB','HC','M/I Other Payer Amount Paid Qualifier','2025-03-14 17:30:12',1, 'HC - M/I Other Payer Amount Paid Qualifier')
,(2888,'511-FB','HD','M/I Dispensing Status','2025-03-14 17:30:12',1, 'HD - M/I Dispensing Status')
,(2889,'511-FB','HE','M/I Percentage Sales Tax Rate Submitted','2025-03-14 17:30:12',1, 'HE - M/I Percentage Sales Tax Rate Submitted')
,(2890,'511-FB','HF','M/I Quantity Intended To Be Dispensed','2025-03-14 17:30:12',1, 'HF - M/I Quantity Intended To Be Dispensed')
,(2891,'511-FB','HG','M/I Days Supply Intended To Be Dispensed','2025-03-14 17:30:12',1, 'HG - M/I Days Supply Intended To Be Dispensed')
,(2892,'511-FB','HN','M/I Patient E-Mail Address','2025-03-14 17:30:12',1, 'HN - M/I Patient E-Mail Address')
,(2893,'511-FB','H1','M/I Measurement Time','2025-03-14 17:30:12',1, 'H1 - M/I Measurement Time')
,(2894,'511-FB','H2','M/I Measurement Dimension','2025-03-14 17:30:12',1, 'H2 - M/I Measurement Dimension')
,(2895,'511-FB','H3','M/I Measurement Unit','2025-03-14 17:30:12',1, 'H3 - M/I Measurement Unit')
,(2896,'511-FB','H4','M/I Measurement Value','2025-03-14 17:30:12',1, 'H4 - M/I Measurement Value')
,(2897,'511-FB','H5','M/I Primary Care Provider Location Code','2025-03-14 17:30:12',1, 'H5 - M/I Primary Care Provider Location Code')
,(2898,'511-FB','H6','M/I DUR Co-Agent ID','2025-03-14 17:30:12',1, 'H6 - M/I DUR Co-Agent ID')
,(2899,'511-FB','H7','M/I Other Amount Claimed Submitted Count','2025-03-14 17:30:12',1, 'H7 - M/I Other Amount Claimed Submitted Count')
,(2900,'511-FB','H8','M/I Other Amount Claimed Submitted Qualifier','2025-03-14 17:30:12',1, 'H8 - M/I Other Amount Claimed Submitted Qualifier')
,(2901,'511-FB','H9','M/I Other Amount Claimed Submitted','2025-03-14 17:30:12',1, 'H9 - M/I Other Amount Claimed Submitted')
,(2902,'511-FB','JE','M/I Percentage Sales Tax Basis Submitted','2025-03-14 17:30:12',1, 'JE - M/I Percentage Sales Tax Basis Submitted')
,(2903,'511-FB','J9','M/I DUR Co-Agent ID Qualifier','2025-03-14 17:30:12',1, 'J9 - M/I DUR Co-Agent ID Qualifier')
,(2904,'511-FB','KE','M/I Coupon Type','2025-03-14 17:30:12',1, 'KE - M/I Coupon Type')
,(2905,'511-FB','K5','M/I Transaction Reference Number','2025-03-14 17:30:12',1, 'K5 - M/I Transaction Reference Number')
,(2906,'511-FB','M1','Patient Not Covered In This Aid Category','2025-03-14 17:30:12',1, 'M1 - Patient Not Covered In This Aid Category')
,(2907,'511-FB','M2','Recipient Locked In','2025-03-14 17:30:12',1, 'M2 - Recipient Locked In')
,(2908,'511-FB','M3','Host PA/MC Error','2025-03-14 17:30:12',1, 'M3 - Host PA/MC Error')
,(2909,'511-FB','M4','Prescription/Service Reference Number/Time Limit Exceeded','2025-03-14 17:30:12',1, 'M4 - Prescription/Service Reference Number/Time Limit Exceeded')
,(2910,'511-FB','M5','Requires Manual Claim','2025-03-14 17:30:12',1, 'M5 - Requires Manual Claim')
,(2911,'511-FB','M6','Host Eligibility Error','2025-03-14 17:30:12',1, 'M6 - Host Eligibility Error')
,(2912,'511-FB','M7','Host Drug File Error','2025-03-14 17:30:12',1, 'M7 - Host Drug File Error')
,(2913,'511-FB','M8','Host Provider File Error','2025-03-14 17:30:12',1, 'M8 - Host Provider File Error')
,(2914,'511-FB','ME','M/I Coupon Number','2025-03-14 17:30:12',1, 'ME - M/I Coupon Number')
,(2915,'511-FB','MG ','M/I Other Payer BIN Number','2025-03-14 17:30:12',1, 'MG - M/I Other Payer BIN Number')
,(2916,'511-FB','MH','M/I Other Payer Processor Control Number','2025-03-14 17:30:12',1, 'MH - M/I Other Payer Processor Control Number')
,(2917,'511-FB','MJ','M/I Other Payer Group ID','2025-03-14 17:30:12',1, 'MJ - M/I Other Payer Group ID')
,(2918,'511-FB','MK','Non-Matched Other Payer BIN Number','2025-03-14 17:30:12',1, 'MK - Non-Matched Other Payer BIN Number')
,(2919,'511-FB','MM','Non-Matched Other Payer Processor Control Number','2025-03-14 17:30:12',1, 'MM - Non-Matched Other Payer Processor Control Number')
,(2920,'511-FB','MN','Non-Matched Other Payer Group ID','2025-03-14 17:30:12',1, 'MN - Non-Matched Other Payer Group ID')
,(2921,'511-FB','MP','Other Payer Cardholder ID Not Covered','2025-03-14 17:30:12',1, 'MP - Other Payer Cardholder ID Not Covered')
,(2922,'511-FB','MR','Product Not On Formulary','2025-03-14 17:30:12',1, 'MR - Product Not On Formulary')
,(2923,'511-FB','MS','More than 1 Cardholder Found -- Narrow Search Criteria','2025-03-14 17:30:12',1, 'MS - More than 1 Cardholder Found -- Narrow Search Criteria')
,(2924,'511-FB','MT','M/I Patient Assignment Indicator (Direct Member Reimbursement Indicator)','2025-03-14 17:30:12',1, 'MT - M/I Patient Assignment Indicator (Direct Member Reimbursement Indicator)')
,(2925,'511-FB','MU','M/I Benefit Stage Count','2025-03-14 17:30:12',1, 'MU - M/I Benefit Stage Count')
,(2926,'511-FB','MV','M/I Benefit Stage Qualifier','2025-03-14 17:30:12',1, 'MV - M/I Benefit Stage Qualifier')
,(2927,'511-FB','MW','M/I Benefit Stage Amount','2025-03-14 17:30:12',1, 'MW - M/I Benefit Stage Amount')
,(2928,'511-FB','MX','Benefit Stage Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'MX - Benefit Stage Count Does Not Match Number Of Repetitions')
,(2929,'511-FB','MZ','Error Overflow','2025-03-14 17:30:12',1, 'MZ - Error Overflow')
,(2930,'511-FB','NE','M/I Coupon Value Amount','2025-03-14 17:30:12',1, 'NE - M/I Coupon Value Amount')
,(2931,'511-FB','NN','Transaction Rejected At Switch Or Intermediary','2025-03-14 17:30:12',1, 'NN - Transaction Rejected At Switch Or Intermediary')
,(2932,'511-FB','NP','M/I Other Payer-Patient Responsibility Amount Qualifier','2025-03-14 17:30:12',1, 'NP - M/I Other Payer-Patient Responsibility Amount Qualifier')
,(2933,'511-FB','NQ','M/I Other Payer-Patient Responsibility Amount','2025-03-14 17:30:12',1, 'NQ - M/I Other Payer-Patient Responsibility Amount')
,(2934,'511-FB','NR','M/I Other Payer-Patient Responsibility Amount Count','2025-03-14 17:30:12',1, 'NR - M/I Other Payer-Patient Responsibility Amount Count')
,(2935,'511-FB','NU','M/I Other Payer Cardholder ID','2025-03-14 17:30:12',1, 'NU - M/I Other Payer Cardholder ID')
,(2936,'511-FB','NV','M/I Delay Reason Code','2025-03-14 17:30:12',1, 'NV - M/I Delay Reason Code')
,(2937,'511-FB','NX','M/I Submission Clarification Code Count','2025-03-14 17:30:12',1, 'NX - M/I Submission Clarification Code Count')
,(2938,'511-FB','N1','No Patient Match Found','2025-03-14 17:30:12',1, 'N1 - No Patient Match Found')
,(2939,'511-FB','N3','M/I Medicaid Paid Amount','2025-03-14 17:30:12',1, 'N3 - M/I Medicaid Paid Amount')
,(2940,'511-FB','N4','M/I Medicaid Subrogation Internal Control Number/Transaction Control Number (ICN/TCN)','2025-03-14 17:30:12',1, 'N4 - M/I Medicaid Subrogation Internal Control Number/Transaction Control Number (ICN/TCN)')
,(2941,'511-FB','N5','M/I Medicaid ID Number','2025-03-14 17:30:12',1, 'N5 - M/I Medicaid ID Number')
,(2942,'511-FB','N6','M/I Medicaid Agency Number','2025-03-14 17:30:12',1, 'N6 - M/I Medicaid Agency Number')
,(2943,'511-FB','N7','Use Prior Authorization ID Provided During Transition Period','2025-03-14 17:30:12',1, 'N7 - Use Prior Authorization ID Provided During Transition Period')
,(2944,'511-FB','N8','Use Prior Authorization ID Provided For Emergency Supply','2025-03-14 17:30:12',1, 'N8 - Use Prior Authorization ID Provided For Emergency Supply')
,(2945,'511-FB','N9','Use Prior Authorization ID Provided For Level of Care Change','2025-03-14 17:30:12',1, 'N9 - Use Prior Authorization ID Provided For Level of Care Change')
,(2946,'511-FB','PA','PA Exhausted/Not Renewable','2025-03-14 17:30:12',1, 'PA - PA Exhausted/Not Renewable')
,(2947,'511-FB','PB','Invalid Transaction Count For This Transaction Code','2025-03-14 17:30:12',1, 'PB - Invalid Transaction Count For This Transaction Code')
,(2948,'511-FB','PC','M/I Request Claim Segment','2025-03-14 17:30:12',1, 'PC - M/I Request Claim Segment')
,(2949,'511-FB','PD','M/I Request Clinical Segment','2025-03-14 17:30:12',1, 'PD - M/I Request Clinical Segment')
,(2950,'511-FB','PE','M/I Request Coordination Of Benefits/Other Payments Segment','2025-03-14 17:30:12',1, 'PE - M/I Request Coordination Of Benefits/Other Payments Segment')
,(2951,'511-FB','PF','M/I Request Compound Segment','2025-03-14 17:30:12',1, 'PF - M/I Request Compound Segment')
,(2952,'511-FB','PG','M/I Request Coupon Segment','2025-03-14 17:30:12',1, 'PG - M/I Request Coupon Segment')
,(2953,'511-FB','PH','M/I Request DUR/PPS Segment','2025-03-14 17:30:12',1, 'PH - M/I Request DUR/PPS Segment')
,(2954,'511-FB','PJ','M/I Request Insurance Segment','2025-03-14 17:30:12',1, 'PJ - M/I Request Insurance Segment')
,(2955,'511-FB','PK','M/I Request Patient Segment','2025-03-14 17:30:12',1, 'PK - M/I Request Patient Segment')
,(2956,'511-FB','PM','M/I Request Pharmacy Provider Segment','2025-03-14 17:30:12',1, 'PM - M/I Request Pharmacy Provider Segment')
,(2957,'511-FB','PN','M/I Request Prescriber Segment','2025-03-14 17:30:12',1, 'PN - M/I Request Prescriber Segment')
,(2958,'511-FB','PP','M/I Request Pricing Segment','2025-03-14 17:30:12',1, 'PP - M/I Request Pricing Segment')
,(2959,'511-FB','PQ','M/I Narrative Segment','2025-03-14 17:30:12',1, 'PQ - M/I Narrative Segment')
,(2960,'511-FB','PR','M/I Request Prior Authorization Segment','2025-03-14 17:30:12',1, 'PR - M/I Request Prior Authorization Segment')
,(2961,'511-FB','PS','M/I Transaction Header Segment','2025-03-14 17:30:12',1, 'PS - M/I Transaction Header Segment')
,(2962,'511-FB','PT','M/I Request Workers Compensation Segment','2025-03-14 17:30:12',1, 'PT - M/I Request Workers Compensation Segment')
,(2963,'511-FB','PV','Non-Matched Associated Prescription/Service Date','2025-03-14 17:30:12',1, 'PV - Non-Matched Associated Prescription/Service Date')
,(2964,'511-FB','PW','Employer ID Not Covered','2025-03-14 17:30:12',1, 'PW - Employer ID Not Covered')
,(2965,'511-FB','PX','Other Payer ID Not Covered','2025-03-14 17:30:12',1, 'PX - Other Payer ID Not Covered')
,(2966,'511-FB','PY','Non-Matched Unit Form/Route of Administration','2025-03-14 17:30:12',1, 'PY - Non-Matched Unit Form/Route of Administration')
,(2967,'511-FB','PZ','Non-Matched Unit Of Measure To Product/Service ID','2025-03-14 17:30:12',1, 'PZ - Non-Matched Unit Of Measure To Product/Service ID')
,(2968,'511-FB','P0','Non-zero Value Required for Vaccine Administration','2025-03-14 17:30:12',1, 'P0 - Non-zero Value Required for Vaccine Administration')
,(2969,'511-FB','P1','Associated Prescription/Service Reference Number Not Found','2025-03-14 17:30:12',1, 'P1 - Associated Prescription/Service Reference Number Not Found')
,(2970,'511-FB','P2','Clinical Information Counter Out Of Sequence','2025-03-14 17:30:12',1, 'P2 - Clinical Information Counter Out Of Sequence')
,(2971,'511-FB','P3','Compound Ingredient Component Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'P3 - Compound Ingredient Component Count Does Not Match Number Of Repetitions')
,(2972,'511-FB','P4','Coordination Of Benefits/Other Payments Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'P4 - Coordination Of Benefits/Other Payments Count Does Not Match Number Of Repetitions')
,(2973,'511-FB','P5','Coupon Expired','2025-03-14 17:30:12',1, 'P5 - Coupon Expired')
,(2974,'511-FB','P6','Date Of Service Prior To Date Of Birth','2025-03-14 17:30:12',1, 'P6 - Date Of Service Prior To Date Of Birth')
,(2975,'511-FB','P7','Diagnosis Code Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'P7 - Diagnosis Code Count Does Not Match Number Of Repetitions')
,(2976,'511-FB','P8','DUR/PPS Code Counter Out Of Sequence','2025-03-14 17:30:12',1, 'P8 - DUR/PPS Code Counter Out Of Sequence')
,(2977,'511-FB','P9','Field Is Non-Repeatable','2025-03-14 17:30:12',1, 'P9 - Field Is Non-Repeatable')
,(2978,'511-FB','RA','PA Reversal Out Of Order','2025-03-14 17:30:12',1, 'RA - PA Reversal Out Of Order')
,(2979,'511-FB','RB','Multiple Partials Not Allowed','2025-03-14 17:30:12',1, 'RB - Multiple Partials Not Allowed')
,(2980,'511-FB','RC','Different Drug Entity Between Partial & Completion','2025-03-14 17:30:12',1, 'RC - Different Drug Entity Between Partial & Completion')
,(2981,'511-FB','RD','Mismatched Cardholder/Group ID-Partial To Completion','2025-03-14 17:30:12',1, 'RD - Mismatched Cardholder/Group ID-Partial To Completion')
,(2982,'511-FB','RE','M/I Compound Product ID Qualifier','2025-03-14 17:30:12',1, 'RE - M/I Compound Product ID Qualifier')
,(2983,'511-FB','RF','Improper Order Of Dispensing Status Code On Partial Fill Transaction','2025-03-14 17:30:12',1, 'RF - Improper Order Of Dispensing Status Code On Partial Fill Transaction')
,(2984,'511-FB','RG','M/I Associated Prescription/Service Reference Number On Completion Transaction','2025-03-14 17:30:12',1, 'RG - M/I Associated Prescription/Service Reference Number On Completion Transaction')
,(2985,'511-FB','RH','M/I Associated Prescription/Service Date On Completion Transaction','2025-03-14 17:30:12',1, 'RH - M/I Associated Prescription/Service Date On Completion Transaction')
,(2986,'511-FB','RJ','Associated Partial Fill Transaction Not On File','2025-03-14 17:30:12',1, 'RJ - Associated Partial Fill Transaction Not On File')
,(2987,'511-FB','RK','Partial Fill Transaction Not Supported','2025-03-14 17:30:12',1, 'RK - Partial Fill Transaction Not Supported')
,(2988,'511-FB','RL','Transitional Benefit/Resubmit Claim','2025-03-14 17:30:12',1, 'RL - Transitional Benefit/Resubmit Claim')
,(2989,'511-FB','RM','Completion Transaction Not Permitted With Same Date Of Service As Partial Transaction','2025-03-14 17:30:12',1, 'RM - Completion Transaction Not Permitted With Same Date Of Service As Partial Transaction')
,(2990,'511-FB','RN','Plan Limits Exceeded On Intended Partial Fill Field Limitations','2025-03-14 17:30:12',1, 'RN - Plan Limits Exceeded On Intended Partial Fill Field Limitations')
,(2991,'511-FB','RP','Out Of Sequence Reversal On Partial Fill Transaction','2025-03-14 17:30:12',1, 'RP - Out Of Sequence Reversal On Partial Fill Transaction')
,(2992,'511-FB','RS','M/I Associated Prescription/Service Date On Partial Transaction','2025-03-14 17:30:12',1, 'RS - M/I Associated Prescription/Service Date On Partial Transaction')
,(2993,'511-FB','RT','M/I Associated Prescription/Service Reference Number On Partial Transaction','2025-03-14 17:30:12',1, 'RT - M/I Associated Prescription/Service Reference Number On Partial Transaction')
,(2994,'511-FB','RU','Mandatory Data Elements Must Occur Before Optional Data Elements In A Segment','2025-03-14 17:30:12',1, 'RU - Mandatory Data Elements Must Occur Before Optional Data Elements In A Segment')
,(2995,'511-FB','R0','Professional Service Code of MA required for Vaccine Incentive Fee Submitted','2025-03-14 17:30:12',1, 'R0 - Professional Service Code of MA required for Vaccine Incentive Fee Submitted')
,(2996,'511-FB','R1','Other Amount Claimed Submitted Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'R1 - Other Amount Claimed Submitted Count Does Not Match Number Of Repetitions')
,(2997,'511-FB','R2','Other Payer Reject Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'R2 - Other Payer Reject Count Does Not Match Number Of Repetitions')
,(2998,'511-FB','R3','Procedure Modifier Code Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'R3 - Procedure Modifier Code Count Does Not Match Number Of Repetitions')
,(2999,'511-FB','R4','Procedure Modifier Code Invalid For Product/Service ID','2025-03-14 17:30:12',1, 'R4 - Procedure Modifier Code Invalid For Product/Service ID')
,(3000,'511-FB','R5','Product/Service ID Must Be Zero When Product/Service ID Qualifier Equals 06','2025-03-14 17:30:12',1, 'R5 - Product/Service ID Must Be Zero When Product/Service ID Qualifier Equals 06')
,(3001,'511-FB','R6','Product/Service Not Appropriate For This Location','2025-03-14 17:30:12',1, 'R6 - Product/Service Not Appropriate For This Location')
,(3002,'511-FB','R7','Repeating Segment Not Allowed In Same Transaction','2025-03-14 17:30:12',1, 'R7 - Repeating Segment Not Allowed In Same Transaction')
,(3003,'511-FB','R8','Syntax Error','2025-03-14 17:30:12',1, 'R8 - Syntax Error')
,(3004,'511-FB','R9','Value In Gross Amount Due Does Not Follow Pricing Formulae','2025-03-14 17:30:12',1, 'R9 - Value In Gross Amount Due Does Not Follow Pricing Formulae')
,(3005,'511-FB','S0','Accumulator Month Count Does Not Match Number of Repetitions','2025-03-14 17:30:12',1, 'S0 - Accumulator Month Count Does Not Match Number of Repetitions')
,(3006,'511-FB','S1','M/I Accumulator Year','2025-03-14 17:30:12',1, 'S1 - M/I Accumulator Year')
,(3007,'511-FB','S2','M/I Transaction Identifier','2025-03-14 17:30:12',1, 'S2 - M/I Transaction Identifier')
,(3008,'511-FB','S3','M/I Accumulated Patient True Out Of Pocket Amount','2025-03-14 17:30:12',1, 'S3 - M/I Accumulated Patient True Out Of Pocket Amount')
,(3009,'511-FB','S4','M/I Accumulated Gross Covered Drug Cost  Amount','2025-03-14 17:30:12',1, 'S4 - M/I Accumulated Gross Covered Drug Cost  Amount')
,(3010,'511-FB','S5','M/I DateTime','2025-03-14 17:30:12',1, 'S5 - M/I DateTime')
,(3011,'511-FB','S6','M/I Accumulator Month','2025-03-14 17:30:12',1, 'S6 - M/I Accumulator Month')
,(3012,'511-FB','S7','M/I Accumulator Month Count','2025-03-14 17:30:12',1, 'S7 - M/I Accumulator Month Count')
,(3013,'511-FB','S8','Non-Matched Transaction Identifier','2025-03-14 17:30:12',1, 'S8 - Non-Matched Transaction Identifier')
,(3014,'511-FB','S9','M/I Financial Information Reporting Transaction Header Segment','2025-03-14 17:30:12',1, 'S9 - M/I Financial Information Reporting Transaction Header Segment')
,(3015,'511-FB','SE','M/I Procedure Modifier Code Count','2025-03-14 17:30:12',1, 'SE - M/I Procedure Modifier Code Count')
,(3016,'511-FB','SF','Other Payer Amount Paid Count Does Not Match Number Of Repetitions','2025-03-14 17:30:12',1, 'SF - Other Payer Amount Paid Count Does Not Match Number Of Repetitions')
,(3017,'511-FB','SG','Submission Clarification Code Count Does Not Match Number of Repetitions','2025-03-14 17:30:12',1, 'SG - Submission Clarification Code Count Does Not Match Number of Repetitions')
,(3018,'511-FB','SH','Other Payer-Patient Responsibility Amount Count Does Not Match Number of Repetitions','2025-03-14 17:30:12',1, 'SH - Other Payer-Patient Responsibility Amount Count Does Not Match Number of Repetitions')
,(3019,'511-FB','SW','Accumulated Patient True Out Of Pocket Must Be Equal To Or Greater Than Zero','2025-03-14 17:30:12',1, 'SW - Accumulated Patient True Out Of Pocket Must Be Equal To Or Greater Than Zero')
,(3020,'511-FB','TE','Missing/Invalid Compound Product ID','2025-03-14 17:30:12',1, 'TE - Missing/Invalid Compound Product ID')
,(3021,'511-FB','TN','Emergency Supply/Resubmit Claim','2025-03-14 17:30:12',1, 'TN - Emergency Supply/Resubmit Claim')
,(3022,'511-FB','TP','Level Of Care Change/Resubmit Claim','2025-03-14 17:30:12',1, 'TP - Level Of Care Change/Resubmit Claim')
,(3023,'511-FB','TQ','Dosage Exceeds Product Labeling Limit','2025-03-14 17:30:12',1, 'TQ - Dosage Exceeds Product Labeling Limit')
,(3024,'511-FB','TR','M/I Billing Entity Type Indicator','2025-03-14 17:30:12',1, 'TR - M/I Billing Entity Type Indicator')
,(3025,'511-FB','TS','M/I Pay To Qualifier','2025-03-14 17:30:12',1, 'TS - M/I Pay To Qualifier')
,(3026,'511-FB','TT','M/I Pay To ID','2025-03-14 17:30:12',1, 'TT - M/I Pay To ID')
,(3027,'511-FB','TU','M/I Pay To Name','2025-03-14 17:30:12',1, 'TU - M/I Pay To Name')
,(3028,'511-FB','TV','M/I Pay To Street Address','2025-03-14 17:30:12',1, 'TV - M/I Pay To Street Address')
,(3029,'511-FB','TW','M/I Pay To City Address','2025-03-14 17:30:12',1, 'TW - M/I Pay To City Address')
,(3030,'511-FB','TX','M/I Pay To State/ Province Address','2025-03-14 17:30:12',1, 'TX - M/I Pay To State/ Province Address')
,(3031,'511-FB','TY','M/I Pay To ZIP/Postal Zone','2025-03-14 17:30:12',1, 'TY - M/I Pay To ZIP/Postal Zone')
,(3032,'511-FB','TZ','M/I Generic Equivalent Product ID Qualifier','2025-03-14 17:30:12',1, 'TZ - M/I Generic Equivalent Product ID Qualifier')
,(3033,'511-FB','T0','Accumulator Month Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'T0 - Accumulator Month Count Exceeds Number Of Occurrences Supported')
,(3034,'511-FB','T1','Request Financial Segment Required For Financial Information Reporting','2025-03-14 17:30:12',1, 'T1 - Request Financial Segment Required For Financial Information Reporting')
,(3035,'511-FB','T2','M/I Request Reference Segment','2025-03-14 17:30:12',1, 'T2 - M/I Request Reference Segment')
,(3036,'511-FB','T3','Out Of Order DateTime','2025-03-14 17:30:12',1, 'T3 - Out Of Order DateTime')
,(3037,'511-FB','T4','Duplicate DateTime','2025-03-14 17:30:12',1, 'T4 - Duplicate DateTime')
,(3038,'511-FB','UA','M/I Generic Equivalent Product ID','2025-03-14 17:30:12',1, 'UA - M/I Generic Equivalent Product ID')
,(3039,'511-FB','UE','M/I Compound Ingredient Basis Of Cost Determination','2025-03-14 17:30:12',1, 'UE - M/I Compound Ingredient Basis Of Cost Determination')
,(3040,'511-FB','UU','DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics','2025-03-14 17:30:12',1, 'UU - DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics')
,(3041,'511-FB','UZ','Other Payer Coverage Type (338-5C) Required On Reversals To Downstream Payers. Resubmit Reversal With This Field.','2025-03-14 17:30:12',1, 'UZ - Other Payer Coverage Type (338-5C) Required On Reversals To Downstream Payers. Resubmit Reversal With This Field.')
,(3042,'511-FB','U7','M/I Pharmacy Service Type','2025-03-14 17:30:12',1, 'U7 - M/I Pharmacy Service Type')
,(3043,'511-FB','VA','Pay To Qualifier  Value Not Supported','2025-03-14 17:30:12',1, 'VA - Pay To Qualifier  Value Not Supported')
,(3044,'511-FB','VB','Generic Equivalent Product ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, 'VB - Generic Equivalent Product ID Qualifier Value Not Supported')
,(3045,'511-FB','VC','Pharmacy Service Type Value Not Supported','2025-03-14 17:30:12',1, 'VC - Pharmacy Service Type Value Not Supported')
,(3046,'511-FB','VD','Eligibility Search Time Frame Exceeded','2025-03-14 17:30:12',1, 'VD - Eligibility Search Time Frame Exceeded')
,(3047,'511-FB','VE','M/I Diagnosis Code Count','2025-03-14 17:30:12',1, 'VE - M/I Diagnosis Code Count')
,(3048,'511-FB','WE','M/I Diagnosis Code Qualifier','2025-03-14 17:30:12',1, 'WE - M/I Diagnosis Code Qualifier')
,(3049,'511-FB','W9','Accumulated Gross Covered Drug Cost Amount Must Be Equal To Or Greater Than Zero','2025-03-14 17:30:12',1, 'W9 - Accumulated Gross Covered Drug Cost Amount Must Be Equal To Or Greater Than Zero')
,(3050,'511-FB','XE','M/I Clinical Information Counter','2025-03-14 17:30:12',1, 'XE - M/I Clinical Information Counter')
,(3051,'511-FB','XZ','M/I Associated Prescription/Service Reference Number Qualifier','2025-03-14 17:30:12',1, 'XZ - M/I Associated Prescription/Service Reference Number Qualifier')
,(3052,'511-FB','X1','Accumulated Patient True Out Of Pocket Exceeds Maximum','2025-03-14 17:30:12',1, 'X1 - Accumulated Patient True Out Of Pocket Exceeds Maximum')
,(3053,'511-FB','X2','Accumulated Gross Covered Drug Cost Exceeds Maximum','2025-03-14 17:30:12',1, 'X2 - Accumulated Gross Covered Drug Cost Exceeds Maximum')
,(3054,'511-FB','X3','Out Of Order Accumulator Months','2025-03-14 17:30:12',1, 'X3 - Out Of Order Accumulator Months')
,(3055,'511-FB','X4','Accumulator Year Not Current Or Prior Year','2025-03-14 17:30:12',1, 'X4 - Accumulator Year Not Current Or Prior Year')
,(3056,'511-FB','X5','M/I Financial Information Reporting Request Insurance Segment','2025-03-14 17:30:12',1, 'X5 - M/I Financial Information Reporting Request Insurance Segment')
,(3057,'511-FB','X6','M/I Request Financial Segment','2025-03-14 17:30:12',1, 'X6 - M/I Request Financial Segment')
,(3058,'511-FB','X7','Financial Information Reporting Request Insurance Segment Required For Financial Reporting','2025-03-14 17:30:12',1, 'X7 - Financial Information Reporting Request Insurance Segment Required For Financial Reporting')
,(3059,'511-FB','X8','Procedure Modifier Code Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'X8 - Procedure Modifier Code Count Exceeds Number Of Occurrences Supported')
,(3060,'511-FB','X9','Diagnosis Code Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'X9 - Diagnosis Code Count Exceeds Number Of Occurrences Supported')
,(3061,'511-FB','X0','M/I Associated Prescription/Service Fill #','2025-03-14 17:30:12',1, 'X0 - M/I Associated Prescription/Service Fill #')
,(3062,'511-FB','YA','Compound Ingredient Modifier Code Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'YA - Compound Ingredient Modifier Code Count Exceeds Number Of Occurrences Supported')
,(3063,'511-FB','YB','Other Amount Claimed Submitted Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'YB - Other Amount Claimed Submitted Count Exceeds Number Of Occurrences Supported')
,(3064,'511-FB','YC','Other Payer Reject Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'YC - Other Payer Reject Count Exceeds Number Of Occurrences Supported')
,(3065,'511-FB','YD','Other Payer-Patient Responsibility Amount Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'YD - Other Payer-Patient Responsibility Amount Count Exceeds Number Of Occurrences Supported')
,(3066,'511-FB','YE','Submission Clarification Code Count Exceeds Number of Occurrences Supported','2025-03-14 17:30:12',1, 'YE - Submission Clarification Code Count Exceeds Number of Occurrences Supported')
,(3067,'511-FB','YF','Question Number/Letter Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'YF - Question Number/Letter Count Exceeds Number Of Occurrences Supported')
,(3068,'511-FB','YG','Benefit Stage Count Exceeds Number Of Occurrences Supported','2025-03-14 17:30:12',1, 'YG - Benefit Stage Count Exceeds Number Of Occurrences Supported')
,(3069,'511-FB','YH','Clinical Information Counter Exceeds Number of Occurrences Supported','2025-03-14 17:30:12',1, 'YH - Clinical Information Counter Exceeds Number of Occurrences Supported')
,(3070,'511-FB','YJ','Medicaid Agency Number Not Supported','2025-03-14 17:30:12',1, 'YJ - Medicaid Agency Number Not Supported')
,(3071,'511-FB','YK','M/I Service Provider Name','2025-03-14 17:30:12',1, 'YK - M/I Service Provider Name')
,(3072,'511-FB','YM','M/I Service Provider Street Address','2025-03-14 17:30:12',1, 'YM - M/I Service Provider Street Address')
,(3073,'511-FB','YN','M/I Service Provider City Address','2025-03-14 17:30:12',1, 'YN - M/I Service Provider City Address')
,(3074,'511-FB','YP','M/I Service Provider State/Province Code Address','2025-03-14 17:30:12',1, 'YP - M/I Service Provider State/Province Code Address')
,(3075,'511-FB','YQ','M/I Service Provider ZIP/Postal Code','2025-03-14 17:30:12',1, 'YQ - M/I Service Provider ZIP/Postal Code')
,(3076,'511-FB','YR','M/I Patient ID Associated State/Province Address','2025-03-14 17:30:12',1, 'YR - M/I Patient ID Associated State/Province Address')
,(3077,'511-FB','YS','M/I Purchaser Relationship Code','2025-03-14 17:30:12',1, 'YS - M/I Purchaser Relationship Code')
,(3078,'511-FB','YT','M/I Seller Initials','2025-03-14 17:30:12',1, 'YT - M/I Seller Initials')
,(3079,'511-FB','YU','M/I Purchaser ID Qualifier','2025-03-14 17:30:12',1, 'YU - M/I Purchaser ID Qualifier')
,(3080,'511-FB','YV','M/I Purchaser ID','2025-03-14 17:30:12',1, 'YV - M/I Purchaser ID')
,(3081,'511-FB','YW','M/I Purchaser ID Associated State/Province Code','2025-03-14 17:30:12',1, 'YW - M/I Purchaser ID Associated State/Province Code')
,(3082,'511-FB','YX','M/I Purchaser Date of Birth','2025-03-14 17:30:12',1, 'YX - M/I Purchaser Date of Birth')
,(3083,'511-FB','YY','M/I Purchaser Gender Code','2025-03-14 17:30:12',1, 'YY - M/I Purchaser Gender Code')
,(3084,'511-FB','YZ','M/I Purchaser First Name','2025-03-14 17:30:12',1, 'YZ - M/I Purchaser First Name')
,(3085,'511-FB','Y0','M/I Purchaser Last Name','2025-03-14 17:30:12',1, 'Y0 - M/I Purchaser Last Name')
,(3086,'511-FB','Y1','M/I Purchaser Street Address','2025-03-14 17:30:12',1, 'Y1 - M/I Purchaser Street Address')
,(3087,'511-FB','Y2','M/I Purchaser City Address','2025-03-14 17:30:12',1, 'Y2 - M/I Purchaser City Address')
,(3088,'511-FB','Y3','M/I Purchaser State/Province Code','2025-03-14 17:30:12',1, 'Y3 - M/I Purchaser State/Province Code')
,(3089,'511-FB','Y4','M/I Purchaser ZIP/Postal Code','2025-03-14 17:30:12',1, 'Y4 - M/I Purchaser ZIP/Postal Code')
,(3090,'511-FB','Y5','M/I Purchaser Country Code','2025-03-14 17:30:12',1, 'Y5 - M/I Purchaser Country Code')
,(3091,'511-FB','Y6','M/I Time Of Service','2025-03-14 17:30:12',1, 'Y6 - M/I Time Of Service')
,(3092,'511-FB','Y7','M/I Associated Prescription/Service Provider ID Qualifier','2025-03-14 17:30:12',1, 'Y7 - M/I Associated Prescription/Service Provider ID Qualifier')
,(3093,'511-FB','Y8','M/I Associated Prescription/Service Provider ID','2025-03-14 17:30:12',1, 'Y8 - M/I Associated Prescription/Service Provider ID')
,(3094,'511-FB','Y9','M/I Seller ID','2025-03-14 17:30:12',1, 'Y9 - M/I Seller ID')
,(3095,'511-FB','Z0','Purchaser Country Code Value Not Supported For Processor/Payer','2025-03-14 17:30:12',1, 'Z0 - Purchaser Country Code Value Not Supported For Processor/Payer')
,(3096,'511-FB','Z1','Prescriber Alternate ID Qualifier Value Not Supported','2025-03-14 17:30:12',1, 'Z1 - Prescriber Alternate ID Qualifier Value Not Supported')
,(3097,'511-FB','Z2','M/I Purchaser Segment','2025-03-14 17:30:12',1, 'Z2 - M/I Purchaser Segment')
,(3098,'511-FB','Z3','Purchaser Segment Present On A Non-Controlled Substance Reporting Transaction','2025-03-14 17:30:12',1, 'Z3 - Purchaser Segment Present On A Non-Controlled Substance Reporting Transaction')
,(3099,'511-FB','Z4','Purchaser Segment Required On A Controlled Substance Reporting Transaction','2025-03-14 17:30:12',1, 'Z4 - Purchaser Segment Required On A Controlled Substance Reporting Transaction')
,(3100,'511-FB','Z5','M/I Service Provider Segment','2025-03-14 17:30:12',1, 'Z5 - M/I Service Provider Segment')
,(3101,'511-FB','Z6','Service Provider Segment Present On A Non-Controlled Substance Reporting Transaction','2025-03-14 17:30:12',1, 'Z6 - Service Provider Segment Present On A Non-Controlled Substance Reporting Transaction')
,(3102,'511-FB','Z7','Service Provider Segment Required On A Controlled Substance Reporting Transaction','2025-03-14 17:30:12',1, 'Z7 - Service Provider Segment Required On A Controlled Substance Reporting Transaction')
,(3103,'511-FB','Z8','Purchaser Relationship Code Value Not Supported','2025-03-14 17:30:12',1, 'Z8 - Purchaser Relationship Code Value Not Supported')
,(3104,'511-FB','Z9','Prescriber Alternate ID Not Covered','2025-03-14 17:30:12',1, 'Z9 - Prescriber Alternate ID Not Covered')
,(3105,'511-FB','ZB','M/I Seller ID Qualifier','2025-03-14 17:30:12',1, 'ZB - M/I Seller ID Qualifier')
,(3106,'511-FB','ZC','Associated Prescription/Service Provider ID Qualifier Value Not Supported For Processor/Payer','2025-03-14 17:30:12',1, 'ZC - Associated Prescription/Service Provider ID Qualifier Value Not Supported For Processor/Payer')
,(3107,'511-FB','ZD','Associated Prescription/Service Reference Number Qualifier Value Not Supported','2025-03-14 17:30:12',1, 'ZD - Associated Prescription/Service Reference Number Qualifier Value Not Supported')
,(3108,'511-FB','ZE','M/I Measurement Date','2025-03-14 17:30:12',1, 'ZE - M/I Measurement Date')
,(3109,'511-FB','ZF','M/I Sales Transaction ID','2025-03-14 17:30:12',1, 'ZF - M/I Sales Transaction ID')
,(3110,'511-FB','ZK','M/I Prescriber ID Associated State/Province Address','2025-03-14 17:30:12',1, 'ZK - M/I Prescriber ID Associated State/Province Address')
,(3111,'511-FB','ZM','M/I Prescriber Alternate ID Qualifier','2025-03-14 17:30:12',1, 'ZM - M/I Prescriber Alternate ID Qualifier')
,(3112,'511-FB','ZN','Purchaser ID Qualifier Value Not Supported For Processor/Payer','2025-03-14 17:30:12',1, 'ZN - Purchaser ID Qualifier Value Not Supported For Processor/Payer')
,(3113,'511-FB','ZP','M/I Prescriber Alternate ID','2025-03-14 17:30:12',1, 'ZP - M/I Prescriber Alternate ID')
,(3114,'511-FB','ZQ','M/I Prescriber Alternate ID Associated State/Province Address','2025-03-14 17:30:12',1, 'ZQ - M/I Prescriber Alternate ID Associated State/Province Address')
,(3115,'511-FB','ZS','M/I Reported Adjudicated Program Type','2025-03-14 17:30:12',1, 'ZS - M/I Reported Adjudicated Program Type')
,(3116,'511-FB','ZT','M/I Released Date','2025-03-14 17:30:12',1, 'ZT - M/I Released Date')
,(3117,'511-FB','ZU','M/I Released Time','2025-03-14 17:30:12',1, 'ZU - M/I Released Time')
,(3118,'511-FB','ZV','Reported Adjudicated ProgramType Value Not Supported','2025-03-14 17:30:12',1, 'ZV - Reported Adjudicated ProgramType Value Not Supported')
,(3119,'511-FB','ZW','M/I Compound Preparation Time','2025-03-14 17:30:12',1, 'ZW - M/I Compound Preparation Time')
,(3120,'511-FB','ZX','M/I CMS Part D Contract ID','2025-03-14 17:30:12',1, 'ZX - M/I CMS Part D Contract ID')
,(3121,'511-FB','ZY','M/I Medicare Part D Plan Benefit Package (PBP)','2025-03-14 17:30:12',1, 'ZY - M/I Medicare Part D Plan Benefit Package (PBP)')
,(3122,'511-FB','ZZ','Cardholder ID Submitted Is Inactive. New Cardholder ID On File.','2025-03-14 17:30:12',1, 'ZZ - Cardholder ID Submitted Is Inactive. New Cardholder ID On File.')
,(3123,'511-FB','A3','This Product May Be Covered Under Hospice - Medicare A','2025-03-14 17:30:12',1, 'A3 - This Product May Be Covered Under Hospice - Medicare A')
,(3124,'511-FB','A4','This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility','2025-03-14 17:30:12',1, 'A4 - This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility')
,(3125,'511-FB','1W','Multi-Ingredient Compound Must Be A Single Transaction','2025-03-14 17:30:12',1, '1W - Multi-Ingredient Compound Must Be A Single Transaction')
,(3126,'511-FB','7F','Future Date Not Allowed For Date Of Birth','2025-03-14 17:30:12',1, '7F - Future Date Not Allowed For Date Of Birth')
,(3127,'511-FB','8V','Negative Dollar Amount Is Not Supported In The Other Payer Amount Paid Field','2025-03-14 17:30:12',1, '8V - Negative Dollar Amount Is Not Supported In The Other Payer Amount Paid Field')
,(3128,'511-FB','EW','M/I Intermediary Authorization Type ID','2025-03-14 17:30:12',1, 'EW - M/I Intermediary Authorization Type ID')
,(3129,'511-FB','EX','M/I Intermediary Authorization ID','2025-03-14 17:30:12',1, 'EX - M/I Intermediary Authorization ID')
,(3130,'511-FB','RV','Multiple Reversals Per Transmission Not Supported','2025-03-14 17:30:12',1, 'RV - Multiple Reversals Per Transmission Not Supported')
,(3131,'511-FB','ZA','The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer','2025-03-14 17:30:12',1, 'ZA - The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer')
,(3132,'522-FM','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(3133,'522-FM','1','Ingredient Cost Paid as Submitted','2025-03-14 17:30:12',1, '1 - Ingredient Cost Paid as Submitted')
,(3134,'522-FM','2','Ingredient Cost Reduced to AWP Pricing','2025-03-14 17:30:12',1, '2 - Ingredient Cost Reduced to AWP Pricing')
,(3135,'522-FM','3','Ingredient Cost Reduced to AWP Less X% Pricing','2025-03-14 17:30:12',1, '3 - Ingredient Cost Reduced to AWP Less X% Pricing')
,(3136,'522-FM','4','Usual & Customary Paid as Submitted','2025-03-14 17:30:12',1, '4 - Usual & Customary Paid as Submitted')
,(3137,'522-FM','5','Paid Lower of Ingredient Cost Plus Fees Versus Usual & Customary','2025-03-14 17:30:12',1, '5 - Paid Lower of Ingredient Cost Plus Fees Versus Usual & Customary')
,(3138,'522-FM','6','MAC Pricing Ingredient Cost Paid','2025-03-14 17:30:12',1, '6 - MAC Pricing Ingredient Cost Paid')
,(3139,'522-FM','7','MAC Pricing Ingredient Cost Reduced to MAC','2025-03-14 17:30:12',1, '7 - MAC Pricing Ingredient Cost Reduced to MAC')
,(3140,'522-FM','8','Contract Pricing','2025-03-14 17:30:12',1, '8 - Contract Pricing')
,(3141,'522-FM','9','Acquisition Pricing','2025-03-14 17:30:12',1, '9 - Acquisition Pricing')
,(3142,'522-FM','10','ASP (Average Sales Price)','2025-03-14 17:30:12',1, '10 - ASP (Average Sales Price)')
,(3143,'522-FM','11','AMP (Average Manufacturer Price)','2025-03-14 17:30:12',1, '11 - AMP (Average Manufacturer Price)')
,(3144,'522-FM','12','340B/Disproportionate Share/Public Health Service Pricing','2025-03-14 17:30:12',1, '12 - 340B/Disproportionate Share/Public Health Service Pricing')
,(3145,'522-FM','13','WAC (Wholesale Acquisition Cost)','2025-03-14 17:30:12',1, '13 - WAC (Wholesale Acquisition Cost)')
,(3146,'522-FM','14','Other Payer-Patient Responsibility Amount','2025-03-14 17:30:12',1, '14 - Other Payer-Patient Responsibility Amount')
,(3147,'522-FM','15','Patient Pay Amount','2025-03-14 17:30:12',1, '15 - Patient Pay Amount')
,(3148,'522-FM','16','Coupon Payment','2025-03-14 17:30:12',1, '16 - Coupon Payment')
,(3149,'522-FM','17','Special Patient Reimbursement','2025-03-14 17:30:12',1, '17 - Special Patient Reimbursement')
,(3150,'522-FM','18','Direct Price (DP)','2025-03-14 17:30:12',1, '18 - Direct Price (DP)')
,(3151,'522-FM','19','State Fee Schedule (SFS) Reimbursement','2025-03-14 17:30:12',1, '19 - State Fee Schedule (SFS) Reimbursement')
,(3152,'522-FM','20','National Average Drug Acquisition Cost (NADAC)','2025-03-14 17:30:12',1, '20 - National Average Drug Acquisition Cost (NADAC)')
,(3153,'522-FM','21','State Average Acquisition Cost (AAC)','2025-03-14 17:30:12',1, '21 - State Average Acquisition Cost (AAC)')
,(3154,'522-FM','22','Ingredient cost paid based on submitted Basis of Cost Free Product.','2025-03-14 17:30:12',1, '22 - Ingredient cost paid based on submitted Basis of Cost Free Product.')
,(3155,'522-FM','23','Indicates the reimbursement was based on the contracted or state fee schedule rate for the Original Manufacturer Product ID for the repackaged drug.','2025-03-14 17:30:12',1, '23 - Indicates the reimbursement was based on the contracted or state fee schedule rate for the Original Manufacturer Product ID for the repackaged drug.')
,(3156,'522-FM','24','Federal Upper Limit (FUL)','2025-03-14 17:30:12',1, '24 - Federal Upper Limit (FUL)')
,(3157,'528-FS','1','Major','2025-03-14 17:30:12',1, '1 - Major')
,(3158,'528-FS','2','Moderate','2025-03-14 17:30:12',1, '2 - Moderate')
,(3159,'528-FS','3','Minor','2025-03-14 17:30:12',1, '3 - Minor')
,(3160,'528-FS','9','Undetermined','2025-03-14 17:30:12',1, '9 - Undetermined')
,(3161,'529-FT','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(3162,'529-FT','1','Your Pharmacy','2025-03-14 17:30:12',1, '1 - Your Pharmacy')
,(3163,'529-FT','2','Other Pharmacy in Same Chain','2025-03-14 17:30:12',1, '2 - Other Pharmacy in Same Chain')
,(3164,'529-FT','3','Other Pharmacy','2025-03-14 17:30:12',1, '3 - Other Pharmacy')
,(3165,'532-FW','1','First DataBank','2025-03-14 17:30:12',1, '1 - First DataBank')
,(3166,'532-FW','2','Medi-Span Product Line','2025-03-14 17:30:12',1, '2 - Medi-Span Product Line')
,(3167,'532-FW','3','Micromedex/Medical Economics','2025-03-14 17:30:12',1, '3 - Micromedex/Medical Economics')
,(3168,'532-FW','4','Processor Developed','2025-03-14 17:30:12',1, '4 - Processor Developed')
,(3169,'532-FW','5','Other','2025-03-14 17:30:12',1, '5 - Other')
,(3170,'532-FW','6','Redbook','2025-03-14 17:30:12',1, '6 - Redbook')
,(3171,'532-FW','7','Multum','2025-03-14 17:30:12',1, '7 - Multum')
,(3172,'533-FX','0','Not Specified','2025-03-14 17:30:12',1, '0 - Not Specified')
,(3173,'533-FX','1','Same Prescriber','2025-03-14 17:30:12',1, '1 - Same Prescriber')
,(3174,'533-FX','2','Other Prescriber','2025-03-14 17:30:12',1, '2 - Other Prescriber')
,(3175,'548-6F','001','Generic Available','2025-03-14 17:30:12',1, '001 - Generic Available')
,(3176,'548-6F','002','Non-Formulary Drug','2025-03-14 17:30:12',1, '002 - Non-Formulary Drug')
,(3177,'548-6F','003','Maintenance Drug','2025-03-14 17:30:12',1, '003 - Maintenance Drug')
,(3178,'548-6F','004','Claim paid under plan''s transition benefit period, otherwise claim would have rejected.','2025-03-14 17:30:12',1, '004 - Claim paid under plan''s transition benefit period, otherwise claim would have rejected.')
,(3179,'548-6F','005','Claim paid under the plan''s transition benefit period, otherwise claim would have rejected as prior authorization required.','2025-03-14 17:30:12',1, '005 - Claim paid under the plan''s transition benefit period, otherwise claim would have rejected as prior authorization required.')
,(3180,'548-6F','006','Claim paid under the plan''s transition benefit period, otherwise claim would have rejected as non-formulary drug.','2025-03-14 17:30:12',1, '006 - Claim paid under the plan''s transition benefit period, otherwise claim would have rejected as non-formulary drug.')
,(3181,'548-6F','007','Claim paid under the plan''s transition benefit period, otherwise claim would have rejected based on plan benefit restrictions.','2025-03-14 17:30:12',1, '007 - Claim paid under the plan''s transition benefit period, otherwise claim would have rejected based on plan benefit restrictions.')
,(3182,'548-6F','008','Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected.','2025-03-14 17:30:12',1, '008 - Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected.')
,(3183,'548-6F','009','Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected as prior authorization required.','2025-03-14 17:30:12',1, '009 - Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected as prior authorization required.')
,(3184,'548-6F','010','Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected as non-formulary drug.','2025-03-14 17:30:12',1, '010 - Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected as non-formulary drug.')
,(3185,'548-6F','011','Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected based on plan benefit restrictions.','2025-03-14 17:30:12',1, '011 - Claim paid under the plan''s emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected based on plan benefit restrictions.')
,(3186,'548-6F','012','Claim paid under plan''s level of care change benefit, otherwise claim would have rejected.','2025-03-14 17:30:12',1, '012 - Claim paid under plan''s level of care change benefit, otherwise claim would have rejected.')
,(3187,'548-6F','013','Claim paid under plan''s level of care change benefit, otherwise claim would have rejected for prior authorization required.','2025-03-14 17:30:12',1, '013 - Claim paid under plan''s level of care change benefit, otherwise claim would have rejected for prior authorization required.')
,(3188,'548-6F','014','Claim paid under plan''s level of care change benefit, otherwise claim would have rejected for non-formulary drug.','2025-03-14 17:30:12',1, '014 - Claim paid under plan''s level of care change benefit, otherwise claim would have rejected for non-formulary drug.')
,(3189,'548-6F','015','Claim paid under plan''s level of care change benefit, otherwise claim would have rejected due to plan benefit restrictions.','2025-03-14 17:30:12',1, '015 - Claim paid under plan''s level of care change benefit, otherwise claim would have rejected due to plan benefit restrictions.')
,(3190,'548-6F','016','PMP Reportable Required','2025-03-14 17:30:12',1, '016 - PMP Reportable Required')
,(3191,'548-6F','017','PMP Reporting Completed','2025-03-14 17:30:12',1, '017 - PMP Reporting Completed')
,(3192,'548-6F','018','Provide Notice: Medicare Prescription Drug Coverage and Your Rights','2025-03-14 17:30:12',1, '018 - Provide Notice: Medicare Prescription Drug Coverage and Your Rights')
,(3193,'548-6F','019','The Submitted Prescriber ID is inactive or expired -- Flagged for Retrospective Review','2025-03-14 17:30:12',1, '019 - The Submitted Prescriber ID is inactive or expired -- Flagged for Retrospective Review')
,(3194,'548-6F','020','Prescriber DEA number is not found in processor''s system. Paid claim flagged for retrospective.','2025-03-14 17:30:12',1, '020 - Prescriber DEA number is not found in processor''s system. Paid claim flagged for retrospective.')
,(3195,'548-6F','021','Prescriber DEA number in the processor''s system is inactive/expired. Paid claim flagged for retrospective review.','2025-03-14 17:30:12',1, '021 - Prescriber DEA number in the processor''s system is inactive/expired. Paid claim flagged for retrospective review.')
,(3196,'548-6F','022','Prescriber DEA number in the processor''s system does not allow for this drug DEA schedule. Paid claim flagged for retrospective review.','2025-03-14 17:30:12',1, '022 - Prescriber DEA number in the processor''s system does not allow for this drug DEA schedule. Paid claim flagged for retrospective review.')
,(3197,'548-6F','023','Prorated copayment applied based on days supply. Plan has prorated the copayment based on days supply.','2025-03-14 17:30:12',1, '023 - Prorated copayment applied based on days supply. Plan has prorated the copayment based on days supply.')
,(3198,'548-6F','024','The submitted Prescriber ID is Not Found - Flagged for Retrospective Review','2025-03-14 17:30:12',1, '024 - The submitted Prescriber ID is Not Found - Flagged for Retrospective Review')
,(3199,'548-6F','025','The submitted Prescriber ID is associated to a Deceased Prescriber -- Flagged for Retrospective Review','2025-03-14 17:30:12',1, '025 - The submitted Prescriber ID is associated to a Deceased Prescriber -- Flagged for Retrospective Review')
,(3200,'548-6F','026','Prescriber Type 1 NPI Required - Flagged for Retrospective Review','2025-03-14 17:30:12',1, '026 - Prescriber Type 1 NPI Required - Flagged for Retrospective Review')
,(3201,'548-6F','027','The submitted Prescriber DEA does not allow this drug DEA Schedule -- Flagged for Retrospective Review','2025-03-14 17:30:12',1, '027 - The submitted Prescriber DEA does not allow this drug DEA Schedule -- Flagged for Retrospective Review')
,(3202,'548-6F','028','Type 1 NPI Required, Claim Paid Based on Plan''s Prescriber NPI Data.','2025-03-14 17:30:12',1, '028 - Type 1 NPI Required, Claim Paid Based on Plan''s Prescriber NPI Data.')
,(3203,'548-6F','029','Grace period claim. Patient required to pay for the full cost of the prescription.','2025-03-14 17:30:12',1, '029 - Grace period claim. Patient required to pay for the full cost of the prescription.')
,(3204,'548-6F','030','Prescriber active enrollment with Medicare Fee For Service required. Flagged for retrospective review','2025-03-14 17:30:12',1, '030 - Prescriber active enrollment with Medicare Fee For Service required. Flagged for retrospective review')
,(3205,'548-6F','031','Pharmacy active enrollment with Medicare Fee For Service required. Flagged for retrospective review','2025-03-14 17:30:12',1, '031 - Pharmacy active enrollment with Medicare Fee For Service required. Flagged for retrospective review')
,(3206,'548-6F','032','Plan''s Prescriber data base not able to verify active state license with prescriptive authority for Prescriber ID Submitted, flagged for retrospective review.','2025-03-14 17:30:12',1, '032 - Plan''s Prescriber data base not able to verify active state license with prescriptive authority for Prescriber ID Submitted, flagged for retrospective review.')
,(3207,'548-6F','033','Hospice Compassionate First Initial Dispensing','2025-03-14 17:30:12',1, '033 - Hospice Compassionate First Initial Dispensing')
,(3208,'548-6F','034','Prior Authorization Approval On File','2025-03-14 17:30:12',1, '034 - Prior Authorization Approval On File')
,(3209,'548-6F','035','Quantity Limit Per Specific Time Period','2025-03-14 17:30:12',1, '035 - Quantity Limit Per Specific Time Period')
,(3210,'548-6F','036','Days Supply Limit Per Specific Time Period','2025-03-14 17:30:12',1, '036 - Days Supply Limit Per Specific Time Period')
,(3211,'548-6F','037','Preferred Formulary Alternative Available','2025-03-14 17:30:12',1, '037 - Preferred Formulary Alternative Available')
,(3212,'548-6F','038','Preferred Network Pharmacy','2025-03-14 17:30:12',1, '038 - Preferred Network Pharmacy')
,(3213,'548-6F','039','Non-Preferred Network Pharmacy','2025-03-14 17:30:12',1, '039 - Non-Preferred Network Pharmacy')
,(3214,'548-6F','040','Specialty Pharmacy Network Available For This Medication','2025-03-14 17:30:12',1, '040 - Specialty Pharmacy Network Available For This Medication')
,(3215,'548-6F','041','Filled as a Medicare Part D Provisional Supply. Active prescriber Medicare enrollment or Other Authorized Prescriber status not found.','2025-03-14 17:30:12',1, '041 - Filled as a Medicare Part D Provisional Supply. Active prescriber Medicare enrollment or Other Authorized Prescriber status not found.')
,(3216,'548-6F','042','The submitted Prescriber NPI not found, therefore NPI active status, Medicare enrollment, prescriptive authority could not be validated. Flagged for retrospective review.','2025-03-14 17:30:12',1, '042 - The submitted Prescriber NPI not found, therefore NPI active status, Medicare enrollment, prescriptive authority could not be validated. Flagged for retrospective review.')
,(3217,'548-6F','043','The submitted Prescriber ID could not be validated as an Other Authorized Prescriber (OAP) and is not found on the Medicare Enrollment file. Flagged for retrospective review.','2025-03-14 17:30:12',1, '043 - The submitted Prescriber ID could not be validated as an Other Authorized Prescriber (OAP) and is not found on the Medicare Enrollment file. Flagged for retrospective review.')
,(3218,'548-6F','044','Plan''s Prescriber data base determined prescriptive authority criteria not met, flagged for retrospective review.','2025-03-14 17:30:12',1, '044 - Plan''s Prescriber data base determined prescriptive authority criteria not met, flagged for retrospective review.')
,(3219,'548-6F','045','Prescriber active enrollment with Medicaid Fee For Service/MCO required. Flagged for retrospective review.','2025-03-14 17:30:12',1, '045 - Prescriber active enrollment with Medicaid Fee For Service/MCO required. Flagged for retrospective review.')
,(3220,'548-6F','046','Pharmacy active enrollment with Medicaid Fee For Service/MCO required. Flagged for retrospective review.','2025-03-14 17:30:12',1, '046 - Pharmacy active enrollment with Medicaid Fee For Service/MCO required. Flagged for retrospective review.')
,(3221,'552-AP','11','National Pharmaceutical Product Interface Code (NAPPI)','2025-03-14 17:30:12',1, '11 - National Pharmaceutical Product Interface Code (NAPPI)')
,(3222,'552-AP','12','Global Trade Identification Number (GTIN)','2025-03-14 17:30:12',1, '12 - Global Trade Identification Number (GTIN)')
,(3223,'552-AP','13','Drug Identification Number (DIN)','2025-03-14 17:30:12',1, '13 - Drug Identification Number (DIN)')
,(3224,'552-AP','14','Medi Span''s Generic Product Identifier (GPI)','2025-03-14 17:30:12',1, '14 - Medi Span''s Generic Product Identifier (GPI)')
,(3225,'552-AP','15','First DataBank Formulation ID (GCN)','2025-03-14 17:30:12',1, '15 - First DataBank Formulation ID (GCN)')
,(3226,'552-AP','16','Truven/Micromedex Generic Formulation Code (GFC)','2025-03-14 17:30:12',1, '16 - Truven/Micromedex Generic Formulation Code (GFC)')
,(3227,'552-AP','17','Medi Span''s Drug Descriptor ID (DDID)','2025-03-14 17:30:12',1, '17 - Medi Span''s Drug Descriptor ID (DDID)')
,(3228,'552-AP','18','First DataBank SmartKey','2025-03-14 17:30:12',1, '18 - First DataBank SmartKey')
,(3229,'552-AP','19','Truven/Micromedex Generic Master (GM)','2025-03-14 17:30:12',1, '19 - Truven/Micromedex Generic Master (GM)')
,(3230,'552-AP','28','First DataBank Medication Name ID (FDB Med Name ID)','2025-03-14 17:30:12',1, '28 - First DataBank Medication Name ID (FDB Med Name ID)')
,(3231,'552-AP','29','First DataBank Routed Medication ID (FDB Routed Med ID)','2025-03-14 17:30:12',1, '29 - First DataBank Routed Medication ID (FDB Routed Med ID)')
,(3232,'552-AP','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','2025-03-14 17:30:12',1, '30 - First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)')
,(3233,'552-AP','31','First DataBank Medication ID (FDB MedID)','2025-03-14 17:30:12',1, '31 - First DataBank Medication ID (FDB MedID)')
,(3234,'552-AP','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','2025-03-14 17:30:12',1, '32 - First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)')
,(3235,'552-AP','33','First DataBank Ingredient List ID (HICL_SEQNO)','2025-03-14 17:30:12',1, '33 - First DataBank Ingredient List ID (HICL_SEQNO)')
,(3236,'552-AP','37','American Hospital Formulary Service (AHFS)','2025-03-14 17:30:12',1, '37 - American Hospital Formulary Service (AHFS)')
,(3237,'552-AP','42','Gold Standard Marketed Product Identifier (MPid)','2025-03-14 17:30:12',1, '42 - Gold Standard Marketed Product Identifier (MPid)')
,(3238,'552-AP','43','Gold Standard Product Identifier (ProdID)','2025-03-14 17:30:12',1, '43 - Gold Standard Product Identifier (ProdID)')
,(3239,'552-AP','44','Gold Standard Specific Product Identifier (SPID)','2025-03-14 17:30:12',1, '44 - Gold Standard Specific Product Identifier (SPID)')
,(3240,'552-AP','45','Device Identifier (DI)','2025-03-14 17:30:12',1, '45 - Device Identifier (DI)')
,(3241,'552-AP','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3242,'552-AP','01','Universal Product Code (UPC)','2025-03-14 17:30:12',1, '01 - Universal Product Code (UPC)')
,(3243,'552-AP','02','Health Related Item (HRI)','2025-03-14 17:30:12',1, '02 - Health Related Item (HRI)')
,(3244,'552-AP','03','National Drug Code (NDC)','2025-03-14 17:30:12',1, '03 - National Drug Code (NDC)')
,(3245,'552-AP','04','Health Industry Business Communications Council (HIBCC)','2025-03-14 17:30:12',1, '04 - Health Industry Business Communications Council (HIBCC)')
,(3246,'552-AP','05','Department of Defense (DOD)','2025-03-14 17:30:12',1, '05 - Department of Defense (DOD)')
,(3247,'557-AV','1','Payer/Plan is Tax Exempt','2025-03-14 17:30:12',1, '1 - Payer/Plan is Tax Exempt')
,(3248,'557-AV','2','Not Tax Exempt','2025-03-14 17:30:12',1, '2 - Not Tax Exempt')
,(3249,'557-AV','3','Patient is Tax Exempt','2025-03-14 17:30:12',1, '3 - Patient is Tax Exempt')
,(3250,'557-AV','4','Payer/Plan and Patient are Tax Exempt','2025-03-14 17:30:12',1, '4 - Payer/Plan and Patient are Tax Exempt')
,(3251,'557-AV','5','Religious Organization','2025-03-14 17:30:12',1, '5 - Religious Organization')
,(3252,'557-AV','6','Tax Exempt Certificate','2025-03-14 17:30:12',1, '6 - Tax Exempt Certificate')
,(3253,'561-AZ','02','Ingredient Cost','2025-03-14 17:30:12',1, '02 - Ingredient Cost')
,(3254,'561-AZ','03','Ingredient Cost + Dispensing Fee','2025-03-14 17:30:12',1, '03 - Ingredient Cost + Dispensing Fee')
,(3255,'561-AZ','04','Professional Service Fee','2025-03-14 17:30:12',1, '04 - Professional Service Fee')
,(3256,'561-AZ','01','Gross Amount Due','2025-03-14 17:30:12',1, '01 - Gross Amount Due')
,(3257,'564-J3','11','Medication Administration','2025-03-14 17:30:12',1, '11 - Medication Administration')
,(3258,'564-J3','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3259,'564-J3','01','Delivery Cost','2025-03-14 17:30:12',1, '01 - Delivery Cost')
,(3260,'564-J3','02','Shipping Cost','2025-03-14 17:30:12',1, '02 - Shipping Cost')
,(3261,'564-J3','03','Postage Cost','2025-03-14 17:30:12',1, '03 - Postage Cost')
,(3262,'564-J3','04','Administrative Cost','2025-03-14 17:30:12',1, '04 - Administrative Cost')
,(3263,'564-J3','09','Compound Preparation Cost Submitted','2025-03-14 17:30:12',1, '09 - Compound Preparation Cost Submitted')
,(3264,'568-J7','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3265,'568-J7','01','Standard Unique Health Plan Identifier','2025-03-14 17:30:12',1, '01 - Standard Unique Health Plan Identifier')
,(3266,'568-J7','02','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '02 - Health Industry Number (HIN)')
,(3267,'568-J7','03','Issuer Identification Number (IIN)','2025-03-14 17:30:12',1, '03 - Issuer Identification Number (IIN)')
,(3268,'568-J7','04','National Association of Insurance Commissioners (NAIC)','2025-03-14 17:30:12',1, '04 - National Association of Insurance Commissioners (NAIC)')
,(3269,'568-J7','05','Medicare Part D Contract Number','2025-03-14 17:30:12',1, '05 - Medicare Part D Contract Number')
,(3270,'573-4V','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3271,'573-4V','01','Quantity Dispensed','2025-03-14 17:30:12',1, '01 - Quantity Dispensed')
,(3272,'573-4V','02','Quantity Intended To Be Dispensed','2025-03-14 17:30:12',1, '02 - Quantity Intended To Be Dispensed')
,(3273,'573-4V','03','Usual and Customary/Prorated','2025-03-14 17:30:12',1, '03 - Usual and Customary/Prorated')
,(3274,'573-4V','04','Waived Due To Partial Fill','2025-03-14 17:30:12',1, '04 - Waived Due To Partial Fill')
,(3275,'573-4V','00','Not Specified','2025-03-14 17:30:12',1, '00 - Not Specified')
,(3276,'579-XX','10','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '10 - Health Industry Number (HIN)')
,(3277,'579-XX','11','Federal Tax ID','2025-03-14 17:30:12',1, '11 - Federal Tax ID')
,(3278,'579-XX','12','Drug Enforcement Administration (DEA) Number','2025-03-14 17:30:12',1, '12 - Drug Enforcement Administration (DEA) Number')
,(3279,'579-XX','13','State Issued','2025-03-14 17:30:12',1, '13 - State Issued')
,(3280,'579-XX','14','Plan Specific','2025-03-14 17:30:12',1, '14 - Plan Specific')
,(3281,'579-XX','15','HCIdea','2025-03-14 17:30:12',1, '15 - HCIdea')
,(3282,'579-XX','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3283,'579-XX','01','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '01 - National Provider Identifier (NPI)')
,(3284,'579-XX','02','Blue Cross','2025-03-14 17:30:12',1, '02 - Blue Cross')
,(3285,'579-XX','03','Blue Shield','2025-03-14 17:30:12',1, '03 - Blue Shield')
,(3286,'579-XX','04','Medicare','2025-03-14 17:30:12',1, '04 - Medicare')
,(3287,'579-XX','05','Medicaid','2025-03-14 17:30:12',1, '05 - Medicaid')
,(3288,'579-XX','06','UPIN (Unique Physician/Practitioner Identification Number)','2025-03-14 17:30:12',1, '06 - UPIN (Unique Physician/Practitioner Identification Number)')
,(3289,'579-XX','07','NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)','2025-03-14 17:30:12',1, '07 - NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)')
,(3290,'579-XX','08','State License','2025-03-14 17:30:12',1, '08 - State License')
,(3291,'579-XX','09','TRICARE','2025-03-14 17:30:12',1, '09 - TRICARE')
,(3292,'581-XZ','01','Rx Billing','2025-03-14 17:30:12',1, '01 - Rx Billing')
,(3293,'581-XZ','02','Service Billing','2025-03-14 17:30:12',1, '02 - Service Billing')
,(3294,'586-YP','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(3295,'586-YP','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(3296,'586-YP','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(3297,'586-YP','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(3298,'586-YP','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(3299,'586-YP','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(3300,'586-YP','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(3301,'586-YP','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(3302,'586-YP','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(3303,'586-YP','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(3304,'586-YP','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(3305,'586-YP','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(3306,'586-YP','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(3307,'586-YP','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(3308,'586-YP','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(3309,'586-YP','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(3310,'586-YP','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(3311,'586-YP','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(3312,'586-YP','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(3313,'586-YP','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(3314,'586-YP','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(3315,'586-YP','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(3316,'586-YP','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(3317,'586-YP','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(3318,'586-YP','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(3319,'586-YP','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(3320,'586-YP','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(3321,'586-YP','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(3322,'586-YP','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(3323,'586-YP','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(3324,'586-YP','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(3325,'586-YP','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(3326,'586-YP','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(3327,'586-YP','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(3328,'586-YP','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(3329,'586-YP','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(3330,'586-YP','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(3331,'586-YP','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(3332,'586-YP','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(3333,'586-YP','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(3334,'586-YP','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(3335,'586-YP','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(3336,'586-YP','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(3337,'586-YP','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(3338,'586-YP','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(3339,'586-YP','56','California','2025-03-14 17:30:12',1, '56 - California')
,(3340,'586-YP','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(3341,'586-YP','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(3342,'586-YP','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(3343,'586-YP','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(3344,'586-YP','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(3345,'586-YP','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(3346,'586-YP','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(3347,'586-YP','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(3348,'586-YP','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(3349,'586-YP','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(3350,'586-YP','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(3351,'586-YP','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(3352,'586-YP','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(3353,'586-YP','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(3354,'586-YP','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(3355,'586-YP','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(3356,'586-YP','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(3357,'586-YP','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(3358,'586-YP','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(3359,'586-YP','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(3360,'586-YP','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(3361,'586-YP','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(3362,'586-YP','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(3363,'586-YP','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(3364,'586-YP','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(3365,'586-YP','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(3366,'586-YP','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(3367,'586-YP','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(3368,'586-YP','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(3369,'586-YP','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(3370,'586-YP','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(3371,'586-YP','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(3372,'586-YP','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(3373,'586-YP','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(3374,'586-YP','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(3375,'586-YP','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(3376,'586-YP','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(3377,'586-YP','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(3378,'586-YP','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(3379,'586-YP','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(3380,'586-YP','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(3381,'586-YP','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(3382,'586-YP','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(3383,'586-YP','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(3384,'586-YP','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(3385,'586-YP','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(3386,'586-YP','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(3387,'586-YP','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(3388,'586-YP','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(3389,'586-YP','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(3390,'586-YP','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(3391,'586-YP','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(3392,'586-YP','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(3393,'586-YP','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(3394,'586-YP','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(3395,'586-YP','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(3396,'586-YP','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(3397,'586-YP','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(3398,'586-YP','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(3399,'586-YP','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(3400,'586-YP','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(3401,'586-YP','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(3402,'586-YP','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(3403,'586-YP','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(3404,'586-YP','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(3405,'586-YP','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(3406,'586-YP','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(3407,'586-YP','05','California','2025-03-14 17:30:12',1, '05 - California')
,(3408,'586-YP','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(3409,'586-YP','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(3410,'586-YP','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(3411,'586-YP','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(3412,'586-YP','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(3413,'586-YP','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(3414,'586-YP','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(3415,'586-YP','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(3416,'586-YP','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(3417,'586-YP','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(3418,'586-YP','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(3419,'586-YP','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(3420,'586-YP','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(3421,'586-YP','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(3422,'586-YP','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(3423,'586-YP','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(3424,'586-YP','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(3425,'586-YP','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(3426,'586-YP','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(3427,'586-YP','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(3428,'591-YU','1','State Issued ID','2025-03-14 17:30:12',1, '1 - State Issued ID')
,(3429,'591-YU','2','Drivers License','2025-03-14 17:30:12',1, '2 - Drivers License')
,(3430,'591-YU','3','U.S. Military ID','2025-03-14 17:30:12',1, '3 - U.S. Military ID')
,(3431,'591-YU','4','Passport','2025-03-14 17:30:12',1, '4 - Passport')
,(3432,'591-YU','5','Alien Number (Government Permanent Residence Number)','2025-03-14 17:30:12',1, '5 - Alien Number (Government Permanent Residence Number)')
,(3433,'591-YU','6','Government Student VISA Number','2025-03-14 17:30:12',1, '6 - Government Student VISA Number')
,(3434,'591-YU','7','Indian Tribal ID','2025-03-14 17:30:12',1, '7 - Indian Tribal ID')
,(3435,'591-YU','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3436,'593-YW','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(3437,'593-YW','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(3438,'593-YW','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(3439,'593-YW','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(3440,'593-YW','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(3441,'593-YW','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(3442,'593-YW','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(3443,'593-YW','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(3444,'593-YW','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(3445,'593-YW','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(3446,'593-YW','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(3447,'593-YW','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(3448,'593-YW','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(3449,'593-YW','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(3450,'593-YW','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(3451,'593-YW','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(3452,'593-YW','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(3453,'593-YW','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(3454,'593-YW','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(3455,'593-YW','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(3456,'593-YW','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(3457,'593-YW','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(3458,'593-YW','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(3459,'593-YW','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(3460,'593-YW','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(3461,'593-YW','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(3462,'593-YW','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(3463,'593-YW','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(3464,'593-YW','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(3465,'593-YW','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(3466,'593-YW','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(3467,'593-YW','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(3468,'593-YW','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(3469,'593-YW','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(3470,'593-YW','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(3471,'593-YW','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(3472,'593-YW','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(3473,'593-YW','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(3474,'593-YW','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(3475,'593-YW','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(3476,'593-YW','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(3477,'593-YW','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(3478,'593-YW','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(3479,'593-YW','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(3480,'593-YW','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(3481,'593-YW','56','California','2025-03-14 17:30:12',1, '56 - California')
,(3482,'593-YW','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(3483,'593-YW','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(3484,'593-YW','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(3485,'593-YW','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(3486,'593-YW','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(3487,'593-YW','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(3488,'593-YW','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(3489,'593-YW','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(3490,'593-YW','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(3491,'593-YW','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(3492,'593-YW','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(3493,'593-YW','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(3494,'593-YW','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(3495,'593-YW','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(3496,'593-YW','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(3497,'593-YW','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(3498,'593-YW','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(3499,'593-YW','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(3500,'593-YW','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(3501,'593-YW','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(3502,'593-YW','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(3503,'593-YW','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(3504,'593-YW','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(3505,'593-YW','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(3506,'593-YW','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(3507,'593-YW','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(3508,'593-YW','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(3509,'593-YW','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(3510,'593-YW','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(3511,'593-YW','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(3512,'593-YW','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(3513,'593-YW','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(3514,'593-YW','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(3515,'593-YW','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(3516,'593-YW','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(3517,'593-YW','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(3518,'593-YW','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(3519,'593-YW','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(3520,'593-YW','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(3521,'593-YW','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(3522,'593-YW','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(3523,'593-YW','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(3524,'593-YW','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(3525,'593-YW','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(3526,'593-YW','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(3527,'593-YW','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(3528,'593-YW','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(3529,'593-YW','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(3530,'593-YW','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(3531,'593-YW','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(3532,'593-YW','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(3533,'593-YW','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(3534,'593-YW','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(3535,'593-YW','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(3536,'593-YW','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(3537,'593-YW','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(3538,'593-YW','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(3539,'593-YW','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(3540,'593-YW','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(3541,'593-YW','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(3542,'593-YW','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(3543,'593-YW','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(3544,'593-YW','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(3545,'593-YW','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(3546,'593-YW','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(3547,'593-YW','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(3548,'593-YW','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(3549,'593-YW','05','California','2025-03-14 17:30:12',1, '05 - California')
,(3550,'593-YW','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(3551,'593-YW','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(3552,'593-YW','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(3553,'593-YW','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(3554,'593-YW','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(3555,'593-YW','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(3556,'593-YW','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(3557,'593-YW','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(3558,'593-YW','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(3559,'593-YW','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(3560,'593-YW','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(3561,'593-YW','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(3562,'593-YW','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(3563,'593-YW','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(3564,'593-YW','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(3565,'593-YW','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(3566,'593-YW','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(3567,'593-YW','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(3568,'593-YW','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(3569,'593-YW','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(3570,'595-YY','0','Unknown','2025-03-14 17:30:12',1, '0 - Unknown')
,(3571,'595-YY','1','Male','2025-03-14 17:30:12',1, '1 - Male')
,(3572,'595-YY','2','Female','2025-03-14 17:30:12',1, '2 - Female')
,(3573,'600-28','EA','Each','2025-03-14 17:30:12',1, 'EA - Each')
,(3574,'600-28','GM','Grams','2025-03-14 17:30:12',1, 'GM - Grams')
,(3575,'600-28','ML','Milliliters','2025-03-14 17:30:12',1, 'ML - Milliliters')
,(3576,'675-Y3','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(3577,'675-Y3','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(3578,'675-Y3','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(3579,'675-Y3','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(3580,'675-Y3','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(3581,'675-Y3','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(3582,'675-Y3','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(3583,'675-Y3','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(3584,'675-Y3','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(3585,'675-Y3','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(3586,'675-Y3','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(3587,'675-Y3','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(3588,'675-Y3','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(3589,'675-Y3','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(3590,'675-Y3','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(3591,'675-Y3','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(3592,'675-Y3','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(3593,'675-Y3','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(3594,'675-Y3','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(3595,'675-Y3','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(3596,'675-Y3','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(3597,'675-Y3','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(3598,'675-Y3','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(3599,'675-Y3','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(3600,'675-Y3','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(3601,'675-Y3','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(3602,'675-Y3','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(3603,'675-Y3','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(3604,'675-Y3','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(3605,'675-Y3','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(3606,'675-Y3','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(3607,'675-Y3','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(3608,'675-Y3','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(3609,'675-Y3','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(3610,'675-Y3','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(3611,'675-Y3','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(3612,'675-Y3','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(3613,'675-Y3','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(3614,'675-Y3','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(3615,'675-Y3','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(3616,'675-Y3','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(3617,'675-Y3','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(3618,'675-Y3','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(3619,'675-Y3','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(3620,'675-Y3','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(3621,'675-Y3','56','California','2025-03-14 17:30:12',1, '56 - California')
,(3622,'675-Y3','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(3623,'675-Y3','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(3624,'675-Y3','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(3625,'675-Y3','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(3626,'675-Y3','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(3627,'675-Y3','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(3628,'675-Y3','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(3629,'675-Y3','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(3630,'675-Y3','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(3631,'675-Y3','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(3632,'675-Y3','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(3633,'675-Y3','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(3634,'675-Y3','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(3635,'675-Y3','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(3636,'675-Y3','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(3637,'675-Y3','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(3638,'675-Y3','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(3639,'675-Y3','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(3640,'675-Y3','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(3641,'675-Y3','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(3642,'675-Y3','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(3643,'675-Y3','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(3644,'675-Y3','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(3645,'675-Y3','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(3646,'675-Y3','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(3647,'675-Y3','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(3648,'675-Y3','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(3649,'675-Y3','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(3650,'675-Y3','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(3651,'675-Y3','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(3652,'675-Y3','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(3653,'675-Y3','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(3654,'675-Y3','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(3655,'675-Y3','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(3656,'675-Y3','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(3657,'675-Y3','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(3658,'675-Y3','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(3659,'675-Y3','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(3660,'675-Y3','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(3661,'675-Y3','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(3662,'675-Y3','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(3663,'675-Y3','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(3664,'675-Y3','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(3665,'675-Y3','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(3666,'675-Y3','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(3667,'675-Y3','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(3668,'675-Y3','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(3669,'675-Y3','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(3670,'675-Y3','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(3671,'675-Y3','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(3672,'675-Y3','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(3673,'675-Y3','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(3674,'675-Y3','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(3675,'675-Y3','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(3676,'675-Y3','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(3677,'675-Y3','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(3678,'675-Y3','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(3679,'675-Y3','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(3680,'675-Y3','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(3681,'675-Y3','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(3682,'675-Y3','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(3683,'675-Y3','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(3684,'675-Y3','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(3685,'675-Y3','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(3686,'675-Y3','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(3687,'675-Y3','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(3688,'675-Y3','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(3689,'675-Y3','05','California','2025-03-14 17:30:12',1, '05 - California')
,(3690,'675-Y3','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(3691,'675-Y3','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(3692,'675-Y3','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(3693,'675-Y3','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(3694,'675-Y3','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(3695,'675-Y3','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(3696,'675-Y3','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(3697,'675-Y3','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(3698,'675-Y3','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(3699,'675-Y3','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(3700,'675-Y3','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(3701,'675-Y3','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(3702,'675-Y3','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(3703,'675-Y3','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(3704,'675-Y3','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(3705,'675-Y3','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(3706,'675-Y3','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(3707,'675-Y3','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(3708,'675-Y3','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(3709,'675-Y3','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(3710,'680-ZB','1','Employee ID as determined by the employer.','2025-03-14 17:30:12',1, '1 - Employee ID as determined by the employer.')
,(3711,'729-TA','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(3712,'729-TA','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(3713,'729-TA','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(3714,'729-TA','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(3715,'729-TA','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(3716,'729-TA','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(3717,'729-TA','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(3718,'729-TA','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(3719,'729-TA','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(3720,'729-TA','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(3721,'729-TA','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(3722,'729-TA','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(3723,'729-TA','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(3724,'729-TA','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(3725,'729-TA','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(3726,'729-TA','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(3727,'729-TA','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(3728,'729-TA','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(3729,'729-TA','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(3730,'729-TA','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(3731,'729-TA','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(3732,'729-TA','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(3733,'729-TA','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(3734,'729-TA','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(3735,'729-TA','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(3736,'729-TA','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(3737,'729-TA','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(3738,'729-TA','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(3739,'729-TA','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(3740,'729-TA','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(3741,'729-TA','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(3742,'729-TA','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(3743,'729-TA','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(3744,'729-TA','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(3745,'729-TA','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(3746,'729-TA','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(3747,'729-TA','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(3748,'729-TA','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(3749,'729-TA','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(3750,'729-TA','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(3751,'729-TA','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(3752,'729-TA','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(3753,'729-TA','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(3754,'729-TA','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(3755,'729-TA','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(3756,'729-TA','56','California','2025-03-14 17:30:12',1, '56 - California')
,(3757,'729-TA','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(3758,'729-TA','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(3759,'729-TA','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(3760,'729-TA','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(3761,'729-TA','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(3762,'729-TA','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(3763,'729-TA','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(3764,'729-TA','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(3765,'729-TA','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(3766,'729-TA','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(3767,'729-TA','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(3768,'729-TA','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(3769,'729-TA','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(3770,'729-TA','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(3771,'729-TA','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(3772,'729-TA','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(3773,'729-TA','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(3774,'729-TA','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(3775,'729-TA','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(3776,'729-TA','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(3777,'729-TA','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(3778,'729-TA','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(3779,'729-TA','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(3780,'729-TA','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(3781,'729-TA','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(3782,'729-TA','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(3783,'729-TA','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(3784,'729-TA','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(3785,'729-TA','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(3786,'729-TA','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(3787,'729-TA','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(3788,'729-TA','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(3789,'729-TA','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(3790,'729-TA','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(3791,'729-TA','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(3792,'729-TA','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(3793,'729-TA','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(3794,'729-TA','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(3795,'729-TA','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(3796,'729-TA','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(3797,'729-TA','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(3798,'729-TA','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(3799,'729-TA','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(3800,'729-TA','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(3801,'729-TA','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(3802,'729-TA','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(3803,'729-TA','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(3804,'729-TA','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(3805,'729-TA','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(3806,'729-TA','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(3807,'729-TA','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(3808,'729-TA','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(3809,'729-TA','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(3810,'729-TA','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(3811,'729-TA','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(3812,'729-TA','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(3813,'729-TA','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(3814,'729-TA','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(3815,'729-TA','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(3816,'729-TA','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(3817,'729-TA','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(3818,'729-TA','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(3819,'729-TA','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(3820,'729-TA','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(3821,'729-TA','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(3822,'729-TA','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(3823,'729-TA','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(3824,'729-TA','05','California','2025-03-14 17:30:12',1, '05 - California')
,(3825,'729-TA','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(3826,'729-TA','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(3827,'729-TA','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(3828,'729-TA','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(3829,'729-TA','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(3830,'729-TA','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(3831,'729-TA','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(3832,'729-TA','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(3833,'729-TA','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(3834,'729-TA','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(3835,'729-TA','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(3836,'729-TA','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(3837,'729-TA','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(3838,'729-TA','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(3839,'729-TA','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(3840,'729-TA','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(3841,'729-TA','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(3842,'729-TA','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(3843,'729-TA','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(3844,'729-TA','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(3845,'931-F8','D','Days','2025-03-14 17:30:12',1, 'D - Days')
,(3846,'931-F8','Y','Years','2025-03-14 17:30:12',1, 'Y - Years')
,(3847,'934-GC','DL','Dollar Amount','2025-03-14 17:30:12',1, 'DL - Dollar Amount')
,(3848,'934-GC','DS','Days Supply','2025-03-14 17:30:12',1, 'DS - Days Supply')
,(3849,'934-GC','FL','Fills','2025-03-14 17:30:12',1, 'FL - Fills')
,(3850,'934-GC','QY','Quantity','2025-03-14 17:30:12',1, 'QY - Quantity')
,(3851,'935-GF','CM','Calendar Month','2025-03-14 17:30:12',1, 'CM - Calendar Month')
,(3852,'935-GF','CQ','Calendar Quarter','2025-03-14 17:30:12',1, 'CQ - Calendar Quarter')
,(3853,'935-GF','CY','Calendar Year','2025-03-14 17:30:12',1, 'CY - Calendar Year')
,(3854,'935-GF','DY','Days','2025-03-14 17:30:12',1, 'DY - Days')
,(3855,'935-GF','LT','Lifetime','2025-03-14 17:30:12',1, 'LT - Lifetime')
,(3856,'935-GF','PD','Per Dispensing','2025-03-14 17:30:12',1, 'PD - Per Dispensing')
,(3857,'935-GF','SP','Specific Date Range','2025-03-14 17:30:12',1, 'SP - Specific Date Range')
,(3858,'943-GQ','D','Days','2025-03-14 17:30:12',1, 'D - Days')
,(3859,'943-GQ','Y','Years','2025-03-14 17:30:12',1, 'Y - Years')
,(3860,'996-G1','10','Z0792','2025-03-14 17:30:12',1, '10 - Z0792')
,(3861,'996-G1','11','Z0793','2025-03-14 17:30:12',1, '11 - Z0793')
,(3862,'996-G1','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(3863,'996-G1','01','Anti-infective','2025-03-14 17:30:12',1, '01 - Anti-infective')
,(3864,'996-G1','02','Ionotropic','2025-03-14 17:30:12',1, '02 - Ionotropic')
,(3865,'996-G1','03','Chemotherapy','2025-03-14 17:30:12',1, '03 - Chemotherapy')
,(3866,'996-G1','04','Pain Management','2025-03-14 17:30:12',1, '04 - Pain Management')
,(3867,'996-G1','05','TPN/PPN (Hepatic, Renal, Pediatric) Total Parenteral Nutrition/Peripheral Parenteral Nutrition','2025-03-14 17:30:12',1, '05 - TPN/PPN (Hepatic, Renal, Pediatric) Total Parenteral Nutrition/Peripheral Parenteral Nutrition')
,(3868,'996-G1','06','Hydration','2025-03-14 17:30:12',1, '06 - Hydration')
,(3869,'996-G1','07','Ophthalmic','2025-03-14 17:30:12',1, '07 - Ophthalmic')
,(3870,'996-G1','08','Z0790','2025-03-14 17:30:12',1, '08 - Z0790')
,(3871,'996-G1','09','Z0791','2025-03-14 17:30:12',1, '09 - Z0791')
,(3872,'997-G2','Y','Yes=CMS qualified facility','2025-03-14 17:30:12',1, 'Y - Yes=CMS qualified facility')
,(3873,'997-G2','N','No=Not a CMS qualified facility','2025-03-14 17:30:12',1, 'N - No=Not a CMS qualified facility')
,(3874,'A22-YR','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(3875,'A22-YR','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(3876,'A22-YR','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(3877,'A22-YR','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(3878,'A22-YR','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(3879,'A22-YR','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(3880,'A22-YR','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(3881,'A22-YR','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(3882,'A22-YR','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(3883,'A22-YR','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(3884,'A22-YR','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(3885,'A22-YR','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(3886,'A22-YR','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(3887,'A22-YR','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(3888,'A22-YR','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(3889,'A22-YR','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(3890,'A22-YR','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(3891,'A22-YR','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(3892,'A22-YR','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(3893,'A22-YR','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(3894,'A22-YR','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(3895,'A22-YR','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(3896,'A22-YR','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(3897,'A22-YR','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(3898,'A22-YR','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(3899,'A22-YR','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(3900,'A22-YR','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(3901,'A22-YR','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(3902,'A22-YR','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(3903,'A22-YR','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(3904,'A22-YR','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(3905,'A22-YR','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(3906,'A22-YR','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(3907,'A22-YR','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(3908,'A22-YR','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(3909,'A22-YR','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(3910,'A22-YR','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(3911,'A22-YR','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(3912,'A22-YR','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(3913,'A22-YR','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(3914,'A22-YR','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(3915,'A22-YR','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(3916,'A22-YR','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(3917,'A22-YR','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(3918,'A22-YR','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(3919,'A22-YR','56','California','2025-03-14 17:30:12',1, '56 - California')
,(3920,'A22-YR','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(3921,'A22-YR','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(3922,'A22-YR','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(3923,'A22-YR','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(3924,'A22-YR','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(3925,'A22-YR','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(3926,'A22-YR','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(3927,'A22-YR','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(3928,'A22-YR','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(3929,'A22-YR','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(3930,'A22-YR','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(3931,'A22-YR','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(3932,'A22-YR','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(3933,'A22-YR','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(3934,'A22-YR','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(3935,'A22-YR','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(3936,'A22-YR','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(3937,'A22-YR','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(3938,'A22-YR','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(3939,'A22-YR','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(3940,'A22-YR','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(3941,'A22-YR','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(3942,'A22-YR','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(3943,'A22-YR','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(3944,'A22-YR','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(3945,'A22-YR','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(3946,'A22-YR','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(3947,'A22-YR','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(3948,'A22-YR','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(3949,'A22-YR','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(3950,'A22-YR','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(3951,'A22-YR','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(3952,'A22-YR','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(3953,'A22-YR','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(3954,'A22-YR','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(3955,'A22-YR','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(3956,'A22-YR','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(3957,'A22-YR','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(3958,'A22-YR','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(3959,'A22-YR','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(3960,'A22-YR','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(3961,'A22-YR','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(3962,'A22-YR','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(3963,'A22-YR','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(3964,'A22-YR','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(3965,'A22-YR','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(3966,'A22-YR','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(3967,'A22-YR','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(3968,'A22-YR','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(3969,'A22-YR','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(3970,'A22-YR','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(3971,'A22-YR','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(3972,'A22-YR','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(3973,'A22-YR','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(3974,'A22-YR','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(3975,'A22-YR','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(3976,'A22-YR','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(3977,'A22-YR','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(3978,'A22-YR','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(3979,'A22-YR','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(3980,'A22-YR','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(3981,'A22-YR','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(3982,'A22-YR','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(3983,'A22-YR','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(3984,'A22-YR','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(3985,'A22-YR','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(3986,'A22-YR','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(3987,'A22-YR','05','California','2025-03-14 17:30:12',1, '05 - California')
,(3988,'A22-YR','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(3989,'A22-YR','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(3990,'A22-YR','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(3991,'A22-YR','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(3992,'A22-YR','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(3993,'A22-YR','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(3994,'A22-YR','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(3995,'A22-YR','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(3996,'A22-YR','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(3997,'A22-YR','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(3998,'A22-YR','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(3999,'A22-YR','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(4000,'A22-YR','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(4001,'A22-YR','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(4002,'A22-YR','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(4003,'A22-YR','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(4004,'A22-YR','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(4005,'A22-YR','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(4006,'A22-YR','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(4007,'A22-YR','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(4008,'A23-YS','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4009,'A23-YS','01','Patient','2025-03-14 17:30:12',1, '01 - Patient')
,(4010,'A23-YS','02','Parent','2025-03-14 17:30:12',1, '02 - Parent')
,(4011,'A23-YS','03','Spouse','2025-03-14 17:30:12',1, '03 - Spouse')
,(4012,'A23-YS','04','Caregiver','2025-03-14 17:30:12',1, '04 - Caregiver')
,(4013,'A23-YS','05','Legal Guardian','2025-03-14 17:30:12',1, '05 - Legal Guardian')
,(4014,'A23-YS','06','Dependent','2025-03-14 17:30:12',1, '06 - Dependent')
,(4015,'A24-ZK','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(4016,'A24-ZK','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(4017,'A24-ZK','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(4018,'A24-ZK','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(4019,'A24-ZK','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(4020,'A24-ZK','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(4021,'A24-ZK','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(4022,'A24-ZK','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(4023,'A24-ZK','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(4024,'A24-ZK','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(4025,'A24-ZK','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(4026,'A24-ZK','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(4027,'A24-ZK','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(4028,'A24-ZK','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(4029,'A24-ZK','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(4030,'A24-ZK','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(4031,'A24-ZK','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(4032,'A24-ZK','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(4033,'A24-ZK','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(4034,'A24-ZK','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(4035,'A24-ZK','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(4036,'A24-ZK','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(4037,'A24-ZK','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(4038,'A24-ZK','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(4039,'A24-ZK','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(4040,'A24-ZK','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(4041,'A24-ZK','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(4042,'A24-ZK','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(4043,'A24-ZK','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(4044,'A24-ZK','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(4045,'A24-ZK','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(4046,'A24-ZK','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(4047,'A24-ZK','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(4048,'A24-ZK','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(4049,'A24-ZK','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(4050,'A24-ZK','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(4051,'A24-ZK','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(4052,'A24-ZK','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(4053,'A24-ZK','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(4054,'A24-ZK','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(4055,'A24-ZK','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(4056,'A24-ZK','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(4057,'A24-ZK','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(4058,'A24-ZK','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(4059,'A24-ZK','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(4060,'A24-ZK','56','California','2025-03-14 17:30:12',1, '56 - California')
,(4061,'A24-ZK','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(4062,'A24-ZK','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(4063,'A24-ZK','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(4064,'A24-ZK','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(4065,'A24-ZK','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(4066,'A24-ZK','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(4067,'A24-ZK','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(4068,'A24-ZK','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(4069,'A24-ZK','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(4070,'A24-ZK','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(4071,'A24-ZK','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(4072,'A24-ZK','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(4073,'A24-ZK','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(4074,'A24-ZK','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(4075,'A24-ZK','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(4076,'A24-ZK','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(4077,'A24-ZK','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(4078,'A24-ZK','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(4079,'A24-ZK','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(4080,'A24-ZK','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(4081,'A24-ZK','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(4082,'A24-ZK','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(4083,'A24-ZK','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(4084,'A24-ZK','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(4085,'A24-ZK','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(4086,'A24-ZK','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(4087,'A24-ZK','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(4088,'A24-ZK','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(4089,'A24-ZK','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(4090,'A24-ZK','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(4091,'A24-ZK','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(4092,'A24-ZK','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(4093,'A24-ZK','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(4094,'A24-ZK','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(4095,'A24-ZK','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(4096,'A24-ZK','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(4097,'A24-ZK','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(4098,'A24-ZK','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(4099,'A24-ZK','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(4100,'A24-ZK','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(4101,'A24-ZK','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(4102,'A24-ZK','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(4103,'A24-ZK','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(4104,'A24-ZK','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(4105,'A24-ZK','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(4106,'A24-ZK','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(4107,'A24-ZK','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(4108,'A24-ZK','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(4109,'A24-ZK','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(4110,'A24-ZK','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(4111,'A24-ZK','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(4112,'A24-ZK','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(4113,'A24-ZK','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(4114,'A24-ZK','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(4115,'A24-ZK','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(4116,'A24-ZK','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(4117,'A24-ZK','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(4118,'A24-ZK','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(4119,'A24-ZK','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(4120,'A24-ZK','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(4121,'A24-ZK','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(4122,'A24-ZK','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(4123,'A24-ZK','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(4124,'A24-ZK','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(4125,'A24-ZK','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(4126,'A24-ZK','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(4127,'A24-ZK','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(4128,'A24-ZK','05','California','2025-03-14 17:30:12',1, '05 - California')
,(4129,'A24-ZK','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(4130,'A24-ZK','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(4131,'A24-ZK','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(4132,'A24-ZK','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(4133,'A24-ZK','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(4134,'A24-ZK','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(4135,'A24-ZK','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(4136,'A24-ZK','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(4137,'A24-ZK','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(4138,'A24-ZK','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(4139,'A24-ZK','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(4140,'A24-ZK','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(4141,'A24-ZK','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(4142,'A24-ZK','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(4143,'A24-ZK','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(4144,'A24-ZK','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(4145,'A24-ZK','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(4146,'A24-ZK','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(4147,'A24-ZK','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(4148,'A24-ZK','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(4149,'A25-ZM','10','Health Industry Number (HIN)','2025-03-14 17:30:12',1, '10 - Health Industry Number (HIN)')
,(4150,'A25-ZM','11','Federal Tax ID','2025-03-14 17:30:12',1, '11 - Federal Tax ID')
,(4151,'A25-ZM','12','Drug Enforcement Administration (DEA) Number','2025-03-14 17:30:12',1, '12 - Drug Enforcement Administration (DEA) Number')
,(4152,'A25-ZM','13','State Issued','2025-03-14 17:30:12',1, '13 - State Issued')
,(4153,'A25-ZM','14','Plan Specific','2025-03-14 17:30:12',1, '14 - Plan Specific')
,(4154,'A25-ZM','15','HCID (HCIdea)','2025-03-14 17:30:12',1, '15 - HCID (HCIdea)')
,(4155,'A25-ZM','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4156,'A25-ZM','01','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '01 - National Provider Identifier (NPI)')
,(4157,'A25-ZM','02','Blue Cross','2025-03-14 17:30:12',1, '02 - Blue Cross')
,(4158,'A25-ZM','03','Blue Shield','2025-03-14 17:30:12',1, '03 - Blue Shield')
,(4159,'A25-ZM','04','Medicare','2025-03-14 17:30:12',1, '04 - Medicare')
,(4160,'A25-ZM','05','Medicaid','2025-03-14 17:30:12',1, '05 - Medicaid')
,(4161,'A25-ZM','06','UPIN (Unique Physician/Practitioner Identification Number)','2025-03-14 17:30:12',1, '06 - UPIN (Unique Physician/Practitioner Identification Number)')
,(4162,'A25-ZM','07','NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)','2025-03-14 17:30:12',1, '07 - NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)')
,(4163,'A25-ZM','08','State License','2025-03-14 17:30:12',1, '08 - State License')
,(4164,'A25-ZM','09','TRICARE','2025-03-14 17:30:12',1, '09 - TRICARE')
,(4165,'A27-ZQ','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(4166,'A27-ZQ','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(4167,'A27-ZQ','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(4168,'A27-ZQ','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(4169,'A27-ZQ','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(4170,'A27-ZQ','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(4171,'A27-ZQ','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(4172,'A27-ZQ','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(4173,'A27-ZQ','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(4174,'A27-ZQ','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(4175,'A27-ZQ','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(4176,'A27-ZQ','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(4177,'A27-ZQ','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(4178,'A27-ZQ','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(4179,'A27-ZQ','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(4180,'A27-ZQ','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(4181,'A27-ZQ','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(4182,'A27-ZQ','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(4183,'A27-ZQ','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(4184,'A27-ZQ','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(4185,'A27-ZQ','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(4186,'A27-ZQ','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(4187,'A27-ZQ','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(4188,'A27-ZQ','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(4189,'A27-ZQ','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(4190,'A27-ZQ','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(4191,'A27-ZQ','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(4192,'A27-ZQ','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(4193,'A27-ZQ','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(4194,'A27-ZQ','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(4195,'A27-ZQ','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(4196,'A27-ZQ','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(4197,'A27-ZQ','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(4198,'A27-ZQ','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(4199,'A27-ZQ','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(4200,'A27-ZQ','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(4201,'A27-ZQ','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(4202,'A27-ZQ','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(4203,'A27-ZQ','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(4204,'A27-ZQ','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(4205,'A27-ZQ','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(4206,'A27-ZQ','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(4207,'A27-ZQ','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(4208,'A27-ZQ','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(4209,'A27-ZQ','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(4210,'A27-ZQ','56','California','2025-03-14 17:30:12',1, '56 - California')
,(4211,'A27-ZQ','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(4212,'A27-ZQ','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(4213,'A27-ZQ','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(4214,'A27-ZQ','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(4215,'A27-ZQ','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(4216,'A27-ZQ','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(4217,'A27-ZQ','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(4218,'A27-ZQ','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(4219,'A27-ZQ','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(4220,'A27-ZQ','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(4221,'A27-ZQ','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(4222,'A27-ZQ','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(4223,'A27-ZQ','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(4224,'A27-ZQ','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(4225,'A27-ZQ','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(4226,'A27-ZQ','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(4227,'A27-ZQ','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(4228,'A27-ZQ','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(4229,'A27-ZQ','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(4230,'A27-ZQ','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(4231,'A27-ZQ','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(4232,'A27-ZQ','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(4233,'A27-ZQ','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(4234,'A27-ZQ','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(4235,'A27-ZQ','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(4236,'A27-ZQ','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(4237,'A27-ZQ','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(4238,'A27-ZQ','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(4239,'A27-ZQ','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(4240,'A27-ZQ','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(4241,'A27-ZQ','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(4242,'A27-ZQ','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(4243,'A27-ZQ','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(4244,'A27-ZQ','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(4245,'A27-ZQ','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(4246,'A27-ZQ','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(4247,'A27-ZQ','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(4248,'A27-ZQ','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(4249,'A27-ZQ','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(4250,'A27-ZQ','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(4251,'A27-ZQ','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(4252,'A27-ZQ','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(4253,'A27-ZQ','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(4254,'A27-ZQ','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(4255,'A27-ZQ','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(4256,'A27-ZQ','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(4257,'A27-ZQ','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(4258,'A27-ZQ','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(4259,'A27-ZQ','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(4260,'A27-ZQ','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(4261,'A27-ZQ','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(4262,'A27-ZQ','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(4263,'A27-ZQ','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(4264,'A27-ZQ','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(4265,'A27-ZQ','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(4266,'A27-ZQ','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(4267,'A27-ZQ','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(4268,'A27-ZQ','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(4269,'A27-ZQ','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(4270,'A27-ZQ','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(4271,'A27-ZQ','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(4272,'A27-ZQ','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(4273,'A27-ZQ','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(4274,'A27-ZQ','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(4275,'A27-ZQ','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(4276,'A27-ZQ','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(4277,'A27-ZQ','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(4278,'A27-ZQ','05','California','2025-03-14 17:30:12',1, '05 - California')
,(4279,'A27-ZQ','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(4280,'A27-ZQ','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(4281,'A27-ZQ','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(4282,'A27-ZQ','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(4283,'A27-ZQ','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(4284,'A27-ZQ','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(4285,'A27-ZQ','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(4286,'A27-ZQ','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(4287,'A27-ZQ','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(4288,'A27-ZQ','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(4289,'A27-ZQ','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(4290,'A27-ZQ','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(4291,'A27-ZQ','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(4292,'A27-ZQ','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(4293,'A27-ZQ','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(4294,'A27-ZQ','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(4295,'A27-ZQ','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(4296,'A27-ZQ','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(4297,'A27-ZQ','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(4298,'A27-ZQ','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(4299,'A28-ZR','1','Medicaid Title XIX','2025-03-14 17:30:12',1, '1 - Medicaid Title XIX')
,(4300,'A28-ZR','2','Medicare','2025-03-14 17:30:12',1, '2 - Medicare')
,(4301,'A28-ZR','3','Commercial','2025-03-14 17:30:12',1, '3 - Commercial')
,(4302,'A28-ZR','4','Workers Compensation','2025-03-14 17:30:12',1, '4 - Workers Compensation')
,(4303,'A28-ZR','5','Self-Pay: Discount Program','2025-03-14 17:30:12',1, '5 - Self-Pay: Discount Program')
,(4304,'A28-ZR','6','Manufacturer Sponsored Patient Pay Reduction Program','2025-03-14 17:30:12',1, '6 - Manufacturer Sponsored Patient Pay Reduction Program')
,(4305,'A28-ZR','7','Manufacturer Free Product','2025-03-14 17:30:12',1, '7 - Manufacturer Free Product')
,(4306,'A28-ZR','8','Veterans Health Administration (VA)','2025-03-14 17:30:12',1, '8 - Veterans Health Administration (VA)')
,(4307,'A28-ZR','9','Unknown','2025-03-14 17:30:12',1, '9 - Unknown')
,(4308,'A28-ZR','10','Hospice - Non Medicare','2025-03-14 17:30:12',1, '10 - Hospice - Non Medicare')
,(4309,'A28-ZR','11','Medicaid Managed Care','2025-03-14 17:30:12',1, '11 - Medicaid Managed Care')
,(4310,'A28-ZR','12','Medicare Part A','2025-03-14 17:30:12',1, '12 - Medicare Part A')
,(4311,'A28-ZR','13','Medicare Advantage','2025-03-14 17:30:12',1, '13 - Medicare Advantage')
,(4312,'A28-ZR','14','Medicare Part D PDP','2025-03-14 17:30:12',1, '14 - Medicare Part D PDP')
,(4313,'A28-ZR','15','Self-Pay: Cash','2025-03-14 17:30:12',1, '15 - Self-Pay: Cash')
,(4314,'A28-ZR','16','Medicare Part B','2025-03-14 17:30:12',1, '16 - Medicare Part B')
,(4315,'A28-ZR','17','Indian Health Services','2025-03-14 17:30:12',1, '17 - Indian Health Services')
,(4316,'A28-ZR','18','ADAP/Ryan White','2025-03-14 17:30:12',1, '18 - ADAP/Ryan White')
,(4317,'A28-ZR','19','Black Lung','2025-03-14 17:30:12',1, '19 - Black Lung')
,(4318,'A28-ZR','20','Casualty Insurance','2025-03-14 17:30:12',1, '20 - Casualty Insurance')
,(4319,'A28-ZR','21','CHIP Title XXI','2025-03-14 17:30:12',1, '21 - CHIP Title XXI')
,(4320,'A28-ZR','22','Health Marketplace Exchange Qualified Health Plan','2025-03-14 17:30:12',1, '22 - Health Marketplace Exchange Qualified Health Plan')
,(4321,'A28-ZR','23','HRSA 340B Indigent Program','2025-03-14 17:30:12',1, '23 - HRSA 340B Indigent Program')
,(4322,'A28-ZR','24','Independent Charity Patient Assistance Program','2025-03-14 17:30:12',1, '24 - Independent Charity Patient Assistance Program')
,(4323,'A28-ZR','25','Manufacturer Patient Assistance Program','2025-03-14 17:30:12',1, '25 - Manufacturer Patient Assistance Program')
,(4324,'A28-ZR','26','Medicare - Medicaid Plan (MMP)','2025-03-14 17:30:12',1, '26 - Medicare - Medicaid Plan (MMP)')
,(4325,'A28-ZR','27','SPAP','2025-03-14 17:30:12',1, '27 - SPAP')
,(4326,'A28-ZR','28','Tricare','2025-03-14 17:30:12',1, '28 - Tricare')
,(4327,'A28-ZR','29','Other Federal Payer','2025-03-14 17:30:12',1, '29 - Other Federal Payer')
,(4328,'A28-ZR','30','Programs of All-Inclusive Care for the Elderly (PACE)','2025-03-14 17:30:12',1, '30 - Programs of All-Inclusive Care for the Elderly (PACE)')
,(4329,'A28-ZR','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4330,'A29-ZS','0','Cash','2025-03-14 17:30:12',1, '0 - Cash')
,(4331,'A29-ZS','1','Medicaid Title XIX','2025-03-14 17:30:12',1, '1 - Medicaid Title XIX')
,(4332,'A29-ZS','2','Medicare','2025-03-14 17:30:12',1, '2 - Medicare')
,(4333,'A29-ZS','3','Commercial','2025-03-14 17:30:12',1, '3 - Commercial')
,(4334,'A29-ZS','4','Workers Compensation','2025-03-14 17:30:12',1, '4 - Workers Compensation')
,(4335,'A29-ZS','5','Self-Pay: Discount Program','2025-03-14 17:30:12',1, '5 - Self-Pay: Discount Program')
,(4336,'A29-ZS','6','Manufacturer Sponsored Patient Pay Reduction Program','2025-03-14 17:30:12',1, '6 - Manufacturer Sponsored Patient Pay Reduction Program')
,(4337,'A29-ZS','7','Manufacturer Free Product','2025-03-14 17:30:12',1, '7 - Manufacturer Free Product')
,(4338,'A29-ZS','8','Veterans Health Administration (VA)','2025-03-14 17:30:12',1, '8 - Veterans Health Administration (VA)')
,(4339,'A29-ZS','9','Unknown','2025-03-14 17:30:12',1, '9 - Unknown')
,(4340,'A29-ZS','10','Hospice - Non Medicare','2025-03-14 17:30:12',1, '10 - Hospice - Non Medicare')
,(4341,'A29-ZS','11','Medicaid Managed Care','2025-03-14 17:30:12',1, '11 - Medicaid Managed Care')
,(4342,'A29-ZS','12','Medicare Part A','2025-03-14 17:30:12',1, '12 - Medicare Part A')
,(4343,'A29-ZS','13','Medicare Advantage','2025-03-14 17:30:12',1, '13 - Medicare Advantage')
,(4344,'A29-ZS','14','Medicare Part D PDP','2025-03-14 17:30:12',1, '14 - Medicare Part D PDP')
,(4345,'A29-ZS','15','Self-Pay: Cash','2025-03-14 17:30:12',1, '15 - Self-Pay: Cash')
,(4346,'A29-ZS','16','Medicare Part B','2025-03-14 17:30:12',1, '16 - Medicare Part B')
,(4347,'A29-ZS','17','Indian Health Services','2025-03-14 17:30:12',1, '17 - Indian Health Services')
,(4348,'A29-ZS','18','ADAP/Ryan White','2025-03-14 17:30:12',1, '18 - ADAP/Ryan White')
,(4349,'A29-ZS','19','Black Lung','2025-03-14 17:30:12',1, '19 - Black Lung')
,(4350,'A29-ZS','20','Casualty Insurance','2025-03-14 17:30:12',1, '20 - Casualty Insurance')
,(4351,'A29-ZS','21','CHIP Title XXI','2025-03-14 17:30:12',1, '21 - CHIP Title XXI')
,(4352,'A29-ZS','22','Health Marketplace Exchange Qualified Health Plan','2025-03-14 17:30:12',1, '22 - Health Marketplace Exchange Qualified Health Plan')
,(4353,'A29-ZS','23','HRSA 340B Indigent Program','2025-03-14 17:30:12',1, '23 - HRSA 340B Indigent Program')
,(4354,'A29-ZS','24','Independent Charity Patient Assistance Program','2025-03-14 17:30:12',1, '24 - Independent Charity Patient Assistance Program')
,(4355,'A29-ZS','25','Manufacturer Patient Assistance Program','2025-03-14 17:30:12',1, '25 - Manufacturer Patient Assistance Program')
,(4356,'A29-ZS','26','Medicare - Medicaid Plan (MMP)','2025-03-14 17:30:12',1, '26 - Medicare - Medicaid Plan (MMP)')
,(4357,'A29-ZS','27','SPAP','2025-03-14 17:30:12',1, '27 - SPAP')
,(4358,'A29-ZS','28','Tricare','2025-03-14 17:30:12',1, '28 - Tricare')
,(4359,'A29-ZS','30','Programs of All-Inclusive Care for the Elderly (PACE)','2025-03-14 17:30:12',1, '30 - Programs of All-Inclusive Care for the Elderly (PACE)')
,(4360,'A29-ZS','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4361,'A45-1R','Y','Yes - Prescription for non-human use.','2025-03-14 17:30:12',1, 'Y - Yes - Prescription for non-human use.')
,(4362,'A45-1R','N','No - Prescription for human use.','2025-03-14 17:30:12',1, 'N - No - Prescription for human use.')
,(4363,'B45-8H','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4364,'B45-8H','01','Intermediary','2025-03-14 17:30:12',1, '01 - Intermediary')
,(4365,'B45-8H','02','Prescription Drug Monitoring Program (PDMP)','2025-03-14 17:30:12',1, '02 - Prescription Drug Monitoring Program (PDMP)')
,(4366,'B45-8H','03','Risk Evaluation and Mitigation Strategy (REMS)','2025-03-14 17:30:12',1, '03 - Risk Evaluation and Mitigation Strategy (REMS)')
,(4367,'B46-8J','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4368,'B46-8J','01','Intermediary','2025-03-14 17:30:12',1, '01 - Intermediary')
,(4369,'B46-8J','02','Patient','2025-03-14 17:30:12',1, '02 - Patient')
,(4370,'B46-8J','03','Pharmacist','2025-03-14 17:30:12',1, '03 - Pharmacist')
,(4371,'B46-8J','04','Prescriber','2025-03-14 17:30:12',1, '04 - Prescriber')
,(4372,'B46-8J','05','Pharmacy','2025-03-14 17:30:12',1, '05 - Pharmacy')
,(4373,'B46-8J','06','Patient Representative','2025-03-14 17:30:12',1, '06 - Patient Representative')
,(4374,'B47-8K','10','Passport ID','2025-03-14 17:30:12',1, '10 - Passport ID')
,(4375,'B47-8K','11','State Issued ID','2025-03-14 17:30:12',1, '11 - State Issued ID')
,(4376,'B47-8K','12','State Issued','2025-03-14 17:30:12',1, '12 - State Issued')
,(4377,'B47-8K','13','State License','2025-03-14 17:30:12',1, '13 - State License')
,(4378,'B47-8K','14','Risk Evaluation and Mitigation Strategy (REMS) Entity ID','2025-03-14 17:30:12',1, '14 - Risk Evaluation and Mitigation Strategy (REMS) Entity ID')
,(4379,'B47-8K','15','U.S. Military ID','2025-03-14 17:30:12',1, '15 - U.S. Military ID')
,(4380,'B47-8K','16','Risk Evaluation and Mitigation Strategy (REMS) Authorization ID','2025-03-14 17:30:12',1, '16 - Risk Evaluation and Mitigation Strategy (REMS) Authorization ID')
,(4381,'B47-8K','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4382,'B47-8K','01','Alien Number (Government Permanent Residence Number)','2025-03-14 17:30:12',1, '01 - Alien Number (Government Permanent Residence Number)')
,(4383,'B47-8K','02','Driver''s License Number','2025-03-14 17:30:12',1, '02 - Driver''s License Number')
,(4384,'B47-8K','03','Drug Enforcement Administration (DEA) Number','2025-03-14 17:30:12',1, '03 - Drug Enforcement Administration (DEA) Number')
,(4385,'B47-8K','04','Government Student VISA Number','2025-03-14 17:30:12',1, '04 - Government Student VISA Number')
,(4386,'B47-8K','05','Indian Tribal ID','2025-03-14 17:30:12',1, '05 - Indian Tribal ID')
,(4387,'B47-8K','06','Intermediary Authorization','2025-03-14 17:30:12',1, '06 - Intermediary Authorization')
,(4388,'B47-8K','07','Medical Record Identification Number (EHR)','2025-03-14 17:30:12',1, '07 - Medical Record Identification Number (EHR)')
,(4389,'B47-8K','08','NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)','2025-03-14 17:30:12',1, '08 - NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)')
,(4390,'B47-8K','09','National Provider Identifier (NPI)','2025-03-14 17:30:12',1, '09 - National Provider Identifier (NPI)')
,(4391,'B49-8N','10','Florida','2025-03-14 17:30:12',1, '10 - Florida')
,(4392,'B49-8N','11','Georgia','2025-03-14 17:30:12',1, '11 - Georgia')
,(4393,'B49-8N','12','Hawaii','2025-03-14 17:30:12',1, '12 - Hawaii')
,(4394,'B49-8N','13','Idaho','2025-03-14 17:30:12',1, '13 - Idaho')
,(4395,'B49-8N','14','Illinois','2025-03-14 17:30:12',1, '14 - Illinois')
,(4396,'B49-8N','15','Indiana','2025-03-14 17:30:12',1, '15 - Indiana')
,(4397,'B49-8N','16','Iowa','2025-03-14 17:30:12',1, '16 - Iowa')
,(4398,'B49-8N','17','Kansas','2025-03-14 17:30:12',1, '17 - Kansas')
,(4399,'B49-8N','18','Kentucky','2025-03-14 17:30:12',1, '18 - Kentucky')
,(4400,'B49-8N','19','Louisiana','2025-03-14 17:30:12',1, '19 - Louisiana')
,(4401,'B49-8N','20','Maine','2025-03-14 17:30:12',1, '20 - Maine')
,(4402,'B49-8N','21','Maryland','2025-03-14 17:30:12',1, '21 - Maryland')
,(4403,'B49-8N','22','Massachusetts','2025-03-14 17:30:12',1, '22 - Massachusetts')
,(4404,'B49-8N','23','Michigan','2025-03-14 17:30:12',1, '23 - Michigan')
,(4405,'B49-8N','24','Minnesota','2025-03-14 17:30:12',1, '24 - Minnesota')
,(4406,'B49-8N','25','Mississippi','2025-03-14 17:30:12',1, '25 - Mississippi')
,(4407,'B49-8N','26','Missouri','2025-03-14 17:30:12',1, '26 - Missouri')
,(4408,'B49-8N','27','Montana','2025-03-14 17:30:12',1, '27 - Montana')
,(4409,'B49-8N','28','Nebraska','2025-03-14 17:30:12',1, '28 - Nebraska')
,(4410,'B49-8N','29','Nevada','2025-03-14 17:30:12',1, '29 - Nevada')
,(4411,'B49-8N','30','New Hampshire','2025-03-14 17:30:12',1, '30 - New Hampshire')
,(4412,'B49-8N','31','New Jersey','2025-03-14 17:30:12',1, '31 - New Jersey')
,(4413,'B49-8N','32','New Mexico','2025-03-14 17:30:12',1, '32 - New Mexico')
,(4414,'B49-8N','33','New York','2025-03-14 17:30:12',1, '33 - New York')
,(4415,'B49-8N','34','North Carolina','2025-03-14 17:30:12',1, '34 - North Carolina')
,(4416,'B49-8N','35','North Dakota','2025-03-14 17:30:12',1, '35 - North Dakota')
,(4417,'B49-8N','36','Ohio','2025-03-14 17:30:12',1, '36 - Ohio')
,(4418,'B49-8N','37','Oklahoma','2025-03-14 17:30:12',1, '37 - Oklahoma')
,(4419,'B49-8N','38','Oregon','2025-03-14 17:30:12',1, '38 - Oregon')
,(4420,'B49-8N','39','Pennsylvania','2025-03-14 17:30:12',1, '39 - Pennsylvania')
,(4421,'B49-8N','40','Puerto Rico','2025-03-14 17:30:12',1, '40 - Puerto Rico')
,(4422,'B49-8N','41','Rhode Island','2025-03-14 17:30:12',1, '41 - Rhode Island')
,(4423,'B49-8N','42','South Carolina','2025-03-14 17:30:12',1, '42 - South Carolina')
,(4424,'B49-8N','43','South Dakota','2025-03-14 17:30:12',1, '43 - South Dakota')
,(4425,'B49-8N','44','Tennessee','2025-03-14 17:30:12',1, '44 - Tennessee')
,(4426,'B49-8N','45','Texas','2025-03-14 17:30:12',1, '45 - Texas')
,(4427,'B49-8N','46','Utah','2025-03-14 17:30:12',1, '46 - Utah')
,(4428,'B49-8N','47','Vermont','2025-03-14 17:30:12',1, '47 - Vermont')
,(4429,'B49-8N','48','Virginia','2025-03-14 17:30:12',1, '48 - Virginia')
,(4430,'B49-8N','49','Washington','2025-03-14 17:30:12',1, '49 - Washington')
,(4431,'B49-8N','50','West Virginia','2025-03-14 17:30:12',1, '50 - West Virginia')
,(4432,'B49-8N','51','Wisconsin','2025-03-14 17:30:12',1, '51 - Wisconsin')
,(4433,'B49-8N','52','Wyoming','2025-03-14 17:30:12',1, '52 - Wyoming')
,(4434,'B49-8N','53','Virgin Islands','2025-03-14 17:30:12',1, '53 - Virgin Islands')
,(4435,'B49-8N','54','Guam','2025-03-14 17:30:12',1, '54 - Guam')
,(4436,'B49-8N','56','California','2025-03-14 17:30:12',1, '56 - California')
,(4437,'B49-8N','57','Florida','2025-03-14 17:30:12',1, '57 - Florida')
,(4438,'B49-8N','58','New York','2025-03-14 17:30:12',1, '58 - New York')
,(4439,'B49-8N','59','Texas','2025-03-14 17:30:12',1, '59 - Texas')
,(4440,'B49-8N','60','Pennsylvania','2025-03-14 17:30:12',1, '60 - Pennsylvania')
,(4441,'B49-8N','AL','Alabama','2025-03-14 17:30:12',1, 'AL - Alabama')
,(4442,'B49-8N','AK','Alaska','2025-03-14 17:30:12',1, 'AK - Alaska')
,(4443,'B49-8N','AZ','Arizona','2025-03-14 17:30:12',1, 'AZ - Arizona')
,(4444,'B49-8N','AR','Arkansas','2025-03-14 17:30:12',1, 'AR - Arkansas')
,(4445,'B49-8N','AS','American Samoa','2025-03-14 17:30:12',1, 'AS - American Samoa')
,(4446,'B49-8N','CA','California','2025-03-14 17:30:12',1, 'CA - California')
,(4447,'B49-8N','CO','Colorado','2025-03-14 17:30:12',1, 'CO - Colorado')
,(4448,'B49-8N','CT','Connecticut','2025-03-14 17:30:12',1, 'CT - Connecticut')
,(4449,'B49-8N','DE','Delaware','2025-03-14 17:30:12',1, 'DE - Delaware')
,(4450,'B49-8N','DC','District Of Columbia','2025-03-14 17:30:12',1, 'DC - District Of Columbia')
,(4451,'B49-8N','FM','Federated States Of Micronesia','2025-03-14 17:30:12',1, 'FM - Federated States Of Micronesia')
,(4452,'B49-8N','FL','Florida','2025-03-14 17:30:12',1, 'FL - Florida')
,(4453,'B49-8N','GA','Georgia','2025-03-14 17:30:12',1, 'GA - Georgia')
,(4454,'B49-8N','GU','Guam','2025-03-14 17:30:12',1, 'GU - Guam')
,(4455,'B49-8N','HI','Hawaii','2025-03-14 17:30:12',1, 'HI - Hawaii')
,(4456,'B49-8N','ID','Idaho','2025-03-14 17:30:12',1, 'ID - Idaho')
,(4457,'B49-8N','IL','Illinois','2025-03-14 17:30:12',1, 'IL - Illinois')
,(4458,'B49-8N','IN','Indiana','2025-03-14 17:30:12',1, 'IN - Indiana')
,(4459,'B49-8N','IA','Iowa','2025-03-14 17:30:12',1, 'IA - Iowa')
,(4460,'B49-8N','KS','Kansas','2025-03-14 17:30:12',1, 'KS - Kansas')
,(4461,'B49-8N','KY','Kentucky','2025-03-14 17:30:12',1, 'KY - Kentucky')
,(4462,'B49-8N','LA','Louisiana','2025-03-14 17:30:12',1, 'LA - Louisiana')
,(4463,'B49-8N','ME','Maine','2025-03-14 17:30:12',1, 'ME - Maine')
,(4464,'B49-8N','MH','Marshall Islands','2025-03-14 17:30:12',1, 'MH - Marshall Islands')
,(4465,'B49-8N','MD','Maryland','2025-03-14 17:30:12',1, 'MD - Maryland')
,(4466,'B49-8N','MA','Massachusetts','2025-03-14 17:30:12',1, 'MA - Massachusetts')
,(4467,'B49-8N','MI','Michigan','2025-03-14 17:30:12',1, 'MI - Michigan')
,(4468,'B49-8N','MN','Minnesota','2025-03-14 17:30:12',1, 'MN - Minnesota')
,(4469,'B49-8N','MS','Mississippi','2025-03-14 17:30:12',1, 'MS - Mississippi')
,(4470,'B49-8N','MO','Missouri','2025-03-14 17:30:12',1, 'MO - Missouri')
,(4471,'B49-8N','MT','Montana','2025-03-14 17:30:12',1, 'MT - Montana')
,(4472,'B49-8N','NE','Nebraska','2025-03-14 17:30:12',1, 'NE - Nebraska')
,(4473,'B49-8N','NV','Nevada','2025-03-14 17:30:12',1, 'NV - Nevada')
,(4474,'B49-8N','NH','New Hampshire','2025-03-14 17:30:12',1, 'NH - New Hampshire')
,(4475,'B49-8N','NJ','New Jersey','2025-03-14 17:30:12',1, 'NJ - New Jersey')
,(4476,'B49-8N','NM','New Mexico','2025-03-14 17:30:12',1, 'NM - New Mexico')
,(4477,'B49-8N','NY','New York','2025-03-14 17:30:12',1, 'NY - New York')
,(4478,'B49-8N','NC','North Carolina','2025-03-14 17:30:12',1, 'NC - North Carolina')
,(4479,'B49-8N','ND','North Dakota','2025-03-14 17:30:12',1, 'ND - North Dakota')
,(4480,'B49-8N','MP','Northern Mariana Islands','2025-03-14 17:30:12',1, 'MP - Northern Mariana Islands')
,(4481,'B49-8N','OH','Ohio','2025-03-14 17:30:12',1, 'OH - Ohio')
,(4482,'B49-8N','OK','Oklahoma','2025-03-14 17:30:12',1, 'OK - Oklahoma')
,(4483,'B49-8N','OR','Oregon','2025-03-14 17:30:12',1, 'OR - Oregon')
,(4484,'B49-8N','PW','Palau','2025-03-14 17:30:12',1, 'PW - Palau')
,(4485,'B49-8N','PA','Pennsylvania','2025-03-14 17:30:12',1, 'PA - Pennsylvania')
,(4486,'B49-8N','PR','Puerto Rico','2025-03-14 17:30:12',1, 'PR - Puerto Rico')
,(4487,'B49-8N','RI','Rhode Island','2025-03-14 17:30:12',1, 'RI - Rhode Island')
,(4488,'B49-8N','SC','South Carolina','2025-03-14 17:30:12',1, 'SC - South Carolina')
,(4489,'B49-8N','SD','South Dakota','2025-03-14 17:30:12',1, 'SD - South Dakota')
,(4490,'B49-8N','TN','Tennessee','2025-03-14 17:30:12',1, 'TN - Tennessee')
,(4491,'B49-8N','TX','Texas','2025-03-14 17:30:12',1, 'TX - Texas')
,(4492,'B49-8N','UT','Utah','2025-03-14 17:30:12',1, 'UT - Utah')
,(4493,'B49-8N','VT','Vermont','2025-03-14 17:30:12',1, 'VT - Vermont')
,(4494,'B49-8N','VA','Virginia','2025-03-14 17:30:12',1, 'VA - Virginia')
,(4495,'B49-8N','VI','Virgin Islands','2025-03-14 17:30:12',1, 'VI - Virgin Islands')
,(4496,'B49-8N','WA','Washington','2025-03-14 17:30:12',1, 'WA - Washington')
,(4497,'B49-8N','WV','West Virginia','2025-03-14 17:30:12',1, 'WV - West Virginia')
,(4498,'B49-8N','WI','Wisconsin','2025-03-14 17:30:12',1, 'WI - Wisconsin')
,(4499,'B49-8N','WY','Wyoming','2025-03-14 17:30:12',1, 'WY - Wyoming')
,(4500,'B49-8N','01','Alabama','2025-03-14 17:30:12',1, '01 - Alabama')
,(4501,'B49-8N','02','Alaska','2025-03-14 17:30:12',1, '02 - Alaska')
,(4502,'B49-8N','03','Arizona','2025-03-14 17:30:12',1, '03 - Arizona')
,(4503,'B49-8N','04','Arkansas','2025-03-14 17:30:12',1, '04 - Arkansas')
,(4504,'B49-8N','05','California','2025-03-14 17:30:12',1, '05 - California')
,(4505,'B49-8N','06','Colorado','2025-03-14 17:30:12',1, '06 - Colorado')
,(4506,'B49-8N','07','Connecticut','2025-03-14 17:30:12',1, '07 - Connecticut')
,(4507,'B49-8N','08','Delaware','2025-03-14 17:30:12',1, '08 - Delaware')
,(4508,'B49-8N','09','District Of Columbia','2025-03-14 17:30:12',1, '09 - District Of Columbia')
,(4509,'B49-8N','AA','Armed Forces Americas  (except Canada)','2025-03-14 17:30:12',1, 'AA - Armed Forces Americas  (except Canada)')
,(4510,'B49-8N','AE','Armed Forces Middle East','2025-03-14 17:30:12',1, 'AE - Armed Forces Middle East')
,(4511,'B49-8N','AP','Armed Forces Pacific','2025-03-14 17:30:12',1, 'AP - Armed Forces Pacific')
,(4512,'B49-8N','AB','Alberta','2025-03-14 17:30:12',1, 'AB - Alberta')
,(4513,'B49-8N','BC','British Columbia','2025-03-14 17:30:12',1, 'BC - British Columbia')
,(4514,'B49-8N','MB','Manitoba','2025-03-14 17:30:12',1, 'MB - Manitoba')
,(4515,'B49-8N','NB','New Brunswick','2025-03-14 17:30:12',1, 'NB - New Brunswick')
,(4516,'B49-8N','NL','Newfoundland and Labrador','2025-03-14 17:30:12',1, 'NL - Newfoundland and Labrador')
,(4517,'B49-8N','NS','Nova Scotia','2025-03-14 17:30:12',1, 'NS - Nova Scotia')
,(4518,'B49-8N','NT','Northwest Territories','2025-03-14 17:30:12',1, 'NT - Northwest Territories')
,(4519,'B49-8N','NU','Nunavut','2025-03-14 17:30:12',1, 'NU - Nunavut')
,(4520,'B49-8N','ON','Ontario','2025-03-14 17:30:12',1, 'ON - Ontario')
,(4521,'B49-8N','PE','Prince Edward Island','2025-03-14 17:30:12',1, 'PE - Prince Edward Island')
,(4522,'B49-8N','QC','Quebec','2025-03-14 17:30:12',1, 'QC - Quebec')
,(4523,'B49-8N','SK','Saskatchewan','2025-03-14 17:30:12',1, 'SK - Saskatchewan')
,(4524,'B49-8N','YT','Yukon','2025-03-14 17:30:12',1, 'YT - Yukon')
,(4525,'B53-8S','1','Intermediary Authorization','2025-03-14 17:30:12',1, '1 - Intermediary Authorization')
,(4526,'B53-8S','2','Prescription Drug Monitoring Program (PDMP)','2025-03-14 17:30:12',1, '2 - Prescription Drug Monitoring Program (PDMP)')
,(4527,'B53-8S','3','Risk Evaluation and Mitigation Strategy (REMS) Authorization','2025-03-14 17:30:12',1, '3 - Risk Evaluation and Mitigation Strategy (REMS) Authorization')
,(4528,'B53-8S','99','Other Override','2025-03-14 17:30:12',1, '99 - Other Override')
,(4529,'B95-3Z','1','Facility Type 2 NPI','2025-03-14 17:30:12',1, '1 - Facility Type 2 NPI')
,(4530,'B95-3Z','2','Other','2025-03-14 17:30:12',1, '2 - Other')
,(4531,'C02-4P','3','NDC-National Drug Code','2025-03-14 17:30:12',1, '3 - NDC-National Drug Code')
,(4532,'C47-9T','1','Medicaid Title XIX','2025-03-14 17:30:12',1, '1 - Medicaid Title XIX')
,(4533,'C47-9T','2','Medicare','2025-03-14 17:30:12',1, '2 - Medicare')
,(4534,'C47-9T','3','Commercial','2025-03-14 17:30:12',1, '3 - Commercial')
,(4535,'C47-9T','4','Workers Compensation','2025-03-14 17:30:12',1, '4 - Workers Compensation')
,(4536,'C47-9T','5','Self-Pay: Discount Program','2025-03-14 17:30:12',1, '5 - Self-Pay: Discount Program')
,(4537,'C47-9T','6','Manufacturer Sponsored Patient Pay Reduction Program','2025-03-14 17:30:12',1, '6 - Manufacturer Sponsored Patient Pay Reduction Program')
,(4538,'C47-9T','7','Manufacturer Free Product','2025-03-14 17:30:12',1, '7 - Manufacturer Free Product')
,(4539,'C47-9T','8','Veterans Health Administration (VA)','2025-03-14 17:30:12',1, '8 - Veterans Health Administration (VA)')
,(4540,'C47-9T','9','Unknown','2025-03-14 17:30:12',1, '9 - Unknown')
,(4541,'C47-9T','10','Hospice - Non Medicare','2025-03-14 17:30:12',1, '10 - Hospice - Non Medicare')
,(4542,'C47-9T','11','Medicaid Managed Care','2025-03-14 17:30:12',1, '11 - Medicaid Managed Care')
,(4543,'C47-9T','12','Medicare Part A','2025-03-14 17:30:12',1, '12 - Medicare Part A')
,(4544,'C47-9T','13','Medicare Advantage','2025-03-14 17:30:12',1, '13 - Medicare Advantage')
,(4545,'C47-9T','14','Medicare Part D PDP','2025-03-14 17:30:12',1, '14 - Medicare Part D PDP')
,(4546,'C47-9T','15','Self-Pay: Cash','2025-03-14 17:30:12',1, '15 - Self-Pay: Cash')
,(4547,'C47-9T','16','Medicare Part B','2025-03-14 17:30:12',1, '16 - Medicare Part B')
,(4548,'C47-9T','17','Indian Health Services','2025-03-14 17:30:12',1, '17 - Indian Health Services')
,(4549,'C47-9T','18','ADAP/Ryan White','2025-03-14 17:30:12',1, '18 - ADAP/Ryan White')
,(4550,'C47-9T','19','Black Lung','2025-03-14 17:30:12',1, '19 - Black Lung')
,(4551,'C47-9T','20','Casualty Insurance','2025-03-14 17:30:12',1, '20 - Casualty Insurance')
,(4552,'C47-9T','21','CHIP Title XXI','2025-03-14 17:30:12',1, '21 - CHIP Title XXI')
,(4553,'C47-9T','22','Health Marketplace Exchange Qualified Health Plan','2025-03-14 17:30:12',1, '22 - Health Marketplace Exchange Qualified Health Plan')
,(4554,'C47-9T','23','HRSA 340B Indigent Program','2025-03-14 17:30:12',1, '23 - HRSA 340B Indigent Program')
,(4555,'C47-9T','24','Independent Charity Patient Assistance Program','2025-03-14 17:30:12',1, '24 - Independent Charity Patient Assistance Program')
,(4556,'C47-9T','25','Manufacturer Patient Assistance Program','2025-03-14 17:30:12',1, '25 - Manufacturer Patient Assistance Program')
,(4557,'C47-9T','26','Medicare - Medicaid Plan (MMP)','2025-03-14 17:30:12',1, '26 - Medicare - Medicaid Plan (MMP)')
,(4558,'C47-9T','27','SPAP','2025-03-14 17:30:12',1, '27 - SPAP')
,(4559,'C47-9T','28','Tricare','2025-03-14 17:30:12',1, '28 - Tricare')
,(4560,'C47-9T','29','Other Federal Payer','2025-03-14 17:30:12',1, '29 - Other Federal Payer')
,(4561,'C47-9T','30','Programs of All-Inclusive Care for the Elderly (PACE)','2025-03-14 17:30:12',1, '30 - Programs of All-Inclusive Care for the Elderly (PACE)')
,(4562,'C47-9T','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4563,'C48-9U','1','Medicaid Title XIX','2025-03-14 17:30:12',1, '1 - Medicaid Title XIX')
,(4564,'C48-9U','2','Medicare','2025-03-14 17:30:12',1, '2 - Medicare')
,(4565,'C48-9U','3','Commercial','2025-03-14 17:30:12',1, '3 - Commercial')
,(4566,'C48-9U','4','Workers Compensation','2025-03-14 17:30:12',1, '4 - Workers Compensation')
,(4567,'C48-9U','5','Self-Pay: Discount Program','2025-03-14 17:30:12',1, '5 - Self-Pay: Discount Program')
,(4568,'C48-9U','6','Manufacturer Sponsored Patient Pay Reduction Program','2025-03-14 17:30:12',1, '6 - Manufacturer Sponsored Patient Pay Reduction Program')
,(4569,'C48-9U','7','Manufacturer Free Product','2025-03-14 17:30:12',1, '7 - Manufacturer Free Product')
,(4570,'C48-9U','8','Veterans Health Administration (VA)','2025-03-14 17:30:12',1, '8 - Veterans Health Administration (VA)')
,(4571,'C48-9U','9','Unknown','2025-03-14 17:30:12',1, '9 - Unknown')
,(4572,'C48-9U','10','Hospice - Non Medicare','2025-03-14 17:30:12',1, '10 - Hospice - Non Medicare')
,(4573,'C48-9U','11','Medicaid Managed Care','2025-03-14 17:30:12',1, '11 - Medicaid Managed Care')
,(4574,'C48-9U','12','Medicare Part A','2025-03-14 17:30:12',1, '12 - Medicare Part A')
,(4575,'C48-9U','13','Medicare Advantage','2025-03-14 17:30:12',1, '13 - Medicare Advantage')
,(4576,'C48-9U','14','Medicare Part D PDP','2025-03-14 17:30:12',1, '14 - Medicare Part D PDP')
,(4577,'C48-9U','15','Self-Pay: Cash','2025-03-14 17:30:12',1, '15 - Self-Pay: Cash')
,(4578,'C48-9U','16','Medicare Part B','2025-03-14 17:30:12',1, '16 - Medicare Part B')
,(4579,'C48-9U','17','Indian Health Services','2025-03-14 17:30:12',1, '17 - Indian Health Services')
,(4580,'C48-9U','18','ADAP/Ryan White','2025-03-14 17:30:12',1, '18 - ADAP/Ryan White')
,(4581,'C48-9U','19','Black Lung','2025-03-14 17:30:12',1, '19 - Black Lung')
,(4582,'C48-9U','20','Casualty Insurance','2025-03-14 17:30:12',1, '20 - Casualty Insurance')
,(4583,'C48-9U','21','CHIP Title XXI','2025-03-14 17:30:12',1, '21 - CHIP Title XXI')
,(4584,'C48-9U','22','Health Marketplace Exchange Qualified Health Plan','2025-03-14 17:30:12',1, '22 - Health Marketplace Exchange Qualified Health Plan')
,(4585,'C48-9U','23','HRSA 340B Indigent Program','2025-03-14 17:30:12',1, '23 - HRSA 340B Indigent Program')
,(4586,'C48-9U','24','Independent Charity Patient Assistance Program','2025-03-14 17:30:12',1, '24 - Independent Charity Patient Assistance Program')
,(4587,'C48-9U','25','Manufacturer Patient Assistance Program','2025-03-14 17:30:12',1, '25 - Manufacturer Patient Assistance Program')
,(4588,'C48-9U','26','Medicare - Medicaid Plan (MMP)','2025-03-14 17:30:12',1, '26 - Medicare - Medicaid Plan (MMP)')
,(4589,'C48-9U','27','SPAP','2025-03-14 17:30:12',1, '27 - SPAP')
,(4590,'C48-9U','28','Tricare','2025-03-14 17:30:12',1, '28 - Tricare')
,(4591,'C48-9U','30','Programs of All-Inclusive Care for the Elderly (PACE)','2025-03-14 17:30:12',1, '30 - Programs of All-Inclusive Care for the Elderly (PACE)')
,(4592,'C48-9U','99','Other','2025-03-14 17:30:12',1, '99 - Other')
,(4593,'C51-9X','1','Medicare Part D Deductible','2025-03-14 17:30:12',1, '1 - Medicare Part D Deductible')
,(4594,'C51-9X','2','Medicare Part D Initial Benefit','2025-03-14 17:30:12',1, '2 - Medicare Part D Initial Benefit')
,(4595,'C51-9X','3','Medicare Part D Coverage Gap (donut hole)','2025-03-14 17:30:12',1, '3 - Medicare Part D Coverage Gap (donut hole)')
,(4596,'C51-9X','4','Medicare Part D Catastrophic Coverage','2025-03-14 17:30:12',1, '4 - Medicare Part D Catastrophic Coverage')
,(4597,'C51-9X','50','Not paid under Part D, paid under Part C benefit (for MA-PD plan)','2025-03-14 17:30:12',1, '50 - Not paid under Part D, paid under Part C benefit (for MA-PD plan)')
,(4598,'C51-9X','51','Not paid under Part D, paid under Part C benefit (for MA-PD plan). Beneficiary is a Qualified Medicare Beneficiary - pharmacy should not attempt to collect cost-share, but instead should attempt to bill COB to Medicaid coverage','2025-03-14 17:30:12',1, '51 - Not paid under Part D, paid under Part C benefit (for MA-PD plan). Beneficiary is a Qualified Medicare Beneficiary - pharmacy should not attempt to collect cost-share, but instead should attempt to bill COB to Medicaid coverage')
,(4599,'C51-9X','61','Part D drug not paid by Part D plan benefit, paid as or under a co-administered insured benefit only.','2025-03-14 17:30:12',1, '61 - Part D drug not paid by Part D plan benefit, paid as or under a co-administered insured benefit only.')
,(4600,'C51-9X','62','Non-Part D/non-qualified drug not paid by Part D plan benefit. Paid as or under a co-administered benefit only.','2025-03-14 17:30:12',1, '62 - Non-Part D/non-qualified drug not paid by Part D plan benefit. Paid as or under a co-administered benefit only.')
,(4601,'C51-9X','63','Non-Part D/non-qualified drug not paid by Part D plan benefit. Paid under Medicaid benefit only of the Medicare/Medicaid (MMP) plan.','2025-03-14 17:30:12',1, '63 - Non-Part D/non-qualified drug not paid by Part D plan benefit. Paid under Medicaid benefit only of the Medicare/Medicaid (MMP) plan.')
,(4602,'C51-9X','70','Part D drug not paid by Part D plan benefit, paid by the beneficiary under plan-sponsored negotiated pricing.','2025-03-14 17:30:12',1, '70 - Part D drug not paid by Part D plan benefit, paid by the beneficiary under plan-sponsored negotiated pricing.')
,(4603,'C51-9X','80','Non-Part D/non-qualified drug not paid by Part D plan benefit, hospice benefit, or any other component of Medicare; paid by the beneficiary under plan-sponsored negotiated pricing.','2025-03-14 17:30:12',1, '80 - Non-Part D/non-qualified drug not paid by Part D plan benefit, hospice benefit, or any other component of Medicare; paid by the beneficiary under plan-sponsored negotiated pricing.')
,(4604,'C51-9X','90','Enhance or OTC drug (PDE value of E/O) not applicable to the Part D drug spend, but is covered by the Part D plan.','2025-03-14 17:30:12',1, '90 - Enhance or OTC drug (PDE value of E/O) not applicable to the Part D drug spend, but is covered by the Part D plan.')
,(4605,'C56-AC','1','Real-time','2025-03-14 17:30:12',1, '1 - Real-time')
,(4606,'C56-AC','2','Batch','2025-03-14 17:30:12',1, '2 - Batch')
,(4607,'C58-AE','1','Flu Vaccine Benefit','2025-03-14 17:30:12',1, '1 - Flu Vaccine Benefit')
,(4608,'C58-AE','2','90 Day At Retail','2025-03-14 17:30:12',1, '2 - 90 Day At Retail')
,(4609,'C60-AG','1','Low Level Complexity','2025-03-14 17:30:12',1, '1 - Low Level Complexity')
,(4610,'C60-AG','10','Mid-Level Complexity Non-Hazardous','2025-03-14 17:30:12',1, '10 - Mid-Level Complexity Non-Hazardous')
,(4611,'C60-AG','20','Mid-Level Complexity Hazardous','2025-03-14 17:30:12',1, '20 - Mid-Level Complexity Hazardous')
,(4612,'C60-AG','30','High Level Non-Hazardous','2025-03-14 17:30:12',1, '30 - High Level Non-Hazardous')
,(4613,'C60-AG','40','High Level Hazardous','2025-03-14 17:30:12',1, '40 - High Level Hazardous')
,(4614,'C60-AG','50','High Level Non-Hazardous Sterile','2025-03-14 17:30:12',1, '50 - High Level Non-Hazardous Sterile')
,(4615,'C60-AG','60','High Level Hazardous Sterile','2025-03-14 17:30:12',1, '60 - High Level Hazardous Sterile')
,(4616,'C63-A5','0','No End-Stage Renal Disease','2025-03-14 17:30:12',1, '0 - No End-Stage Renal Disease')
,(4617,'C63-A5','1','End-Stage Renal Disease','2025-03-14 17:30:12',1, '1 - End-Stage Renal Disease')
,(4618,'C63-A5','NA','Not Applicable','2025-03-14 17:30:12',1, 'NA - Not Applicable')
,(4619,'C66-BA','1','Pharmacy Help Desk','2025-03-14 17:30:12',1, '1 - Pharmacy Help Desk')
,(4620,'C66-BA','2','Clinical/PA','2025-03-14 17:30:12',1, '2 - Clinical/PA')
,(4621,'C66-BA','3','Health Plan','2025-03-14 17:30:12',1, '3 - Health Plan')
,(4622,'C66-BA','4','Eligibility - Third Party Liability','2025-03-14 17:30:12',1, '4 - Eligibility - Third Party Liability')
,(4623,'C66-BA','5','Other','2025-03-14 17:30:12',1, '5 - Other')
,(4624,'C70-BF','1','Telephone Number','2025-03-14 17:30:12',1, '1 - Telephone Number')
,(4625,'C70-BF','2','Fax Number','2025-03-14 17:30:12',1, '2 - Fax Number')
,(4626,'C70-BF','3','URL','2025-03-14 17:30:12',1, '3 - URL')
,(4627,'C70-BF','4','Other','2025-03-14 17:30:12',1, '4 - Other')
,(4628,'C71-BG','1','Pharmacy','2025-03-14 17:30:12',1, '1 - Pharmacy')
,(4629,'C71-BG','2','Prescriber','2025-03-14 17:30:12',1, '2 - Prescriber')
,(4630,'C71-BG','3','Member Services','2025-03-14 17:30:12',1, '3 - Member Services')
,(4631,'C71-BG','4','Other Payer - N1 Reporting','2025-03-14 17:30:12',1, '4 - Other Payer - N1 Reporting')
,(4632,'C71-BG','5','Other','2025-03-14 17:30:12',1, '5 - Other')
,(4633,'C73-BJ','0','No Institutional','2025-03-14 17:30:12',1, '0 - No Institutional')
,(4634,'C73-BJ','1','Institutional','2025-03-14 17:30:12',1, '1 - Institutional')
,(4635,'C73-BJ','2','NHC - Nursing Home Certifiable','2025-03-14 17:30:12',1, '2 - NHC - Nursing Home Certifiable')
,(4636,'C73-BJ','3','HCBS - Home and Community Based','2025-03-14 17:30:12',1, '3 - HCBS - Home and Community Based')
,(4637,'C73-BJ','NA','Not Applicable','2025-03-14 17:30:12',1, 'NA - Not Applicable')
,(4638,'C80-G8','1','Intermediary/Switch','2025-03-14 17:30:12',1, '1 - Intermediary/Switch')
,(4639,'C80-G8','2','REMS - Risk Evaluation Mitigation Strategy','2025-03-14 17:30:12',1, '2 - REMS - Risk Evaluation Mitigation Strategy')
,(4640,'C80-G8','3','PDMP - Prescription Drug Monitoring Program','2025-03-14 17:30:12',1, '3 - PDMP - Prescription Drug Monitoring Program')
,(4641,'C80-G8','4','Other','2025-03-14 17:30:12',1, '4 - Other')
,(4642,'C84-KA','1','Telephone Number','2025-03-14 17:30:12',1, '1 - Telephone Number')
,(4643,'C84-KA','2','Fax Number','2025-03-14 17:30:12',1, '2 - Fax Number')
,(4644,'C84-KA','3','URL','2025-03-14 17:30:12',1, '3 - URL')
,(4645,'C84-KA','4','Other','2025-03-14 17:30:12',1, '4 - Other')
,(4646,'C85-KB','1','Pharmacy','2025-03-14 17:30:12',1, '1 - Pharmacy')
,(4647,'C85-KB','2','Prescriber','2025-03-14 17:30:12',1, '2 - Prescriber')
,(4648,'C85-KB','3','Member Services','2025-03-14 17:30:12',1, '3 - Member Services')
,(4649,'C85-KB','4','Other','2025-03-14 17:30:12',1, '4 - Other')
,(4650,'C88-KF','0','None- Not low income','2025-03-14 17:30:12',1, '0 - None- Not low income')
,(4651,'C88-KF','1','High','2025-03-14 17:30:12',1, '1 - High')
,(4652,'C88-KF','2','Low','2025-03-14 17:30:12',1, '2 - Low')
,(4653,'C88-KF','3','0 (zero)','2025-03-14 17:30:12',1, '3 - 0 (zero)')
,(4654,'C88-KF','4','15%','2025-03-14 17:30:12',1, '4 - 15%')
,(4655,'C88-KF','5','Unknown','2025-03-14 17:30:12',1, '5 - Unknown')
,(4656,'C90-KH','1','Full quantity dispensed on date of service.','2025-03-14 17:30:12',1, '1 - Full quantity dispensed on date of service.')
,(4657,'C90-KH','2','Post-consumption where date of service represents date of earliest dispensing. One or more dispensings make up the total quantity on the claim and the total quantity on the claim has been dispensed.','2025-03-14 17:30:12',1, '2 - Post-consumption where date of service represents date of earliest dispensing. One or more dispensings make up the total quantity on the claim and the total quantity on the claim has been dispensed.')
,(4658,'C90-KH','3','Pre-consumption where date of service represents date of earliest dispensing. One or more dispensings make up the total quantity on the claim, but all dispensings that make up the total quantity on the claim have not yet occurred.','2025-03-14 17:30:12',1, '3 - Pre-consumption where date of service represents date of earliest dispensing. One or more dispensings make up the total quantity on the claim, but all dispensings that make up the total quantity on the claim have not yet occurred.')
,(4659,'C91-KK','1','Medication dispensed in a day-supply increment equal to the billed days supply (for example: medication dispensed for a 30-day supply and billed for a 30-day supply).','2025-03-14 17:30:12',1, '1 - Medication dispensed in a day-supply increment equal to the billed days supply (for example: medication dispensed for a 30-day supply and billed for a 30-day supply).')
,(4660,'C91-KK','2','7 days - dispenses medication in 7-day supplies.','2025-03-14 17:30:12',1, '2 - 7 days - dispenses medication in 7-day supplies.')
,(4661,'C91-KK','3','4 days - dispenses medication in 4-day supplies.','2025-03-14 17:30:12',1, '3 - 4 days - dispenses medication in 4-day supplies.')
,(4662,'C91-KK','4','3 days - dispenses medication in 3-day supplies.','2025-03-14 17:30:12',1, '4 - 3 days - dispenses medication in 3-day supplies.')
,(4663,'C91-KK','5','2 days - dispenses medication in 2-day supplies.','2025-03-14 17:30:12',1, '5 - 2 days - dispenses medication in 2-day supplies.')
,(4664,'C91-KK','6','1 day - dispenses medication in 1-day supplies.','2025-03-14 17:30:12',1, '6 - 1 day - dispenses medication in 1-day supplies.')
,(4665,'C91-KK','7','4-3 days - dispenses medication in 4-day, then 3-day supplies.','2025-03-14 17:30:12',1, '7 - 4-3 days - dispenses medication in 4-day, then 3-day supplies.')
,(4666,'C91-KK','8','2-2-3 days - dispenses medication in 2-day, then 2-day, then 3-day supplies.','2025-03-14 17:30:12',1, '8 - 2-2-3 days - dispenses medication in 2-day, then 2-day, then 3-day supplies.')
,(4667,'C91-KK','9','Daily and 3-day weekend - dispensed daily during the week and combines multiple days dispensing for weekends.','2025-03-14 17:30:12',1, '9 - Daily and 3-day weekend - dispensed daily during the week and combines multiple days dispensing for weekends.')
,(4668,'C91-KK','10','Per shift dispensing (multiple med passes).','2025-03-14 17:30:12',1, '10 - Per shift dispensing (multiple med passes).')
,(4669,'C91-KK','11','Per med pass dispensing.','2025-03-14 17:30:12',1, '11 - Per med pass dispensing.')
,(4670,'C91-KK','12','PRN on demand.','2025-03-14 17:30:12',1, '12 - PRN on demand.')
,(4671,'C91-KK','13','7-day or less cycle not otherwise represented.','2025-03-14 17:30:12',1, '13 - 7-day or less cycle not otherwise represented.')
,(4672,'C91-KK','14','14 days dispensing - dispenses medication in 14-day supplies.','2025-03-14 17:30:12',1, '14 - 14 days dispensing - dispenses medication in 14-day supplies.')
,(4673,'C91-KK','15','8-14-Day dispensing cycle not otherwise represented.','2025-03-14 17:30:12',1, '15 - 8-14-Day dispensing cycle not otherwise represented.')
,(4674,'C95-KQ','1','Amount Applied to Periodic Deductible','2025-03-14 17:30:12',1, '1 - Amount Applied to Periodic Deductible')
,(4675,'C95-KQ','2','Amount Attributed to Product Selection/Brand Drug','2025-03-14 17:30:12',1, '2 - Amount Attributed to Product Selection/Brand Drug')
,(4676,'C95-KQ','3','Amount Attributed to Percentage Tax','2025-03-14 17:30:12',1, '3 - Amount Attributed to Percentage Tax')
,(4677,'C95-KQ','4','Amount Exceeding Periodic Benefit Maximum','2025-03-14 17:30:12',1, '4 - Amount Exceeding Periodic Benefit Maximum')
,(4678,'C95-KQ','5','Amount of Copay','2025-03-14 17:30:12',1, '5 - Amount of Copay')
,(4679,'C95-KQ','7','Amount of Coinsurance','2025-03-14 17:30:12',1, '7 - Amount of Coinsurance')
,(4680,'C95-KQ','8','Amount Attributed to Product Selection/Non-Preferred Formulary Selection','2025-03-14 17:30:12',1, '8 - Amount Attributed to Product Selection/Non-Preferred Formulary Selection')
,(4681,'C95-KQ','9','Amount Attributed to Health Plan Assistance Amount','2025-03-14 17:30:12',1, '9 - Amount Attributed to Health Plan Assistance Amount')
,(4682,'C95-KQ','10','Amount Attributed to Provider Network Selection','2025-03-14 17:30:12',1, '10 - Amount Attributed to Provider Network Selection')
,(4683,'C95-KQ','11','Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection','2025-03-14 17:30:12',1, '11 - Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection')
,(4684,'C95-KQ','12','Amount Attributed to Coverage Gap','2025-03-14 17:30:12',1, '12 - Amount Attributed to Coverage Gap')
,(4685,'C95-KQ','13','Amount Attributed to Processor Fee','2025-03-14 17:30:12',1, '13 - Amount Attributed to Processor Fee')
,(4686,'C95-KQ','14','Amount Attributed to Grace Period','2025-03-14 17:30:12',1, '14 - Amount Attributed to Grace Period')
,(4687,'C95-KQ','15','Amount Attributed to Catastrophic Benefit','2025-03-14 17:30:12',1, '15 - Amount Attributed to Catastrophic Benefit')
,(4688,'C95-KQ','16','Amount Attributed to Unbalanced Patient Pay OPPRA','2025-03-14 17:30:12',1, '16 - Amount Attributed to Unbalanced Patient Pay OPPRA')
,(4689,'C95-KQ','17','Amount Attributed to Regulatory Fee.','2025-03-14 17:30:12',1, '17 - Amount Attributed to Regulatory Fee.')
,(4690,'C97-KS','COST','1876 Cost','2025-03-14 17:30:12',1, 'COST - 1876 Cost')
,(4691,'C97-KS','EGWP','Employer Group Waiver Plan','2025-03-14 17:30:12',1, 'EGWP - Employer Group Waiver Plan')
,(4692,'C97-KS','LNET','Point-of-Sale Contractor','2025-03-14 17:30:12',1, 'LNET - Point-of-Sale Contractor')
,(4693,'C97-KS','MAPD','Medicare Advantage Prescription Drug Plan','2025-03-14 17:30:12',1, 'MAPD - Medicare Advantage Prescription Drug Plan')
,(4694,'C97-KS','MMP','Medicare-Medicaid Plan HMO/HMOPOS','2025-03-14 17:30:12',1, 'MMP - Medicare-Medicaid Plan HMO/HMOPOS')
,(4695,'C97-KS','PACE','National Programs of All-Inclusive Care for the Elderly','2025-03-14 17:30:12',1, 'PACE - National Programs of All-Inclusive Care for the Elderly')
,(4696,'C97-KS','PDP','Medicare Prescription Drug Plan','2025-03-14 17:30:12',1, 'PDP - Medicare Prescription Drug Plan')
,(4697,'C98-KT','1','Pill Splitting','2025-03-14 17:30:12',1, '1 - Pill Splitting')
,(4698,'C98-KT','2','Other','2025-03-14 17:30:12',1, '2 - Other')
,(4699,'C99-KU','1','Specific Pressure Environment Not Required/Not Used','2025-03-14 17:30:12',1, '1 - Specific Pressure Environment Not Required/Not Used')
,(4700,'C99-KU','2','Positive Pressure Non-sterile/Non-hazardous','2025-03-14 17:30:12',1, '2 - Positive Pressure Non-sterile/Non-hazardous')
,(4701,'C99-KU','3','Negative Pressure Non-sterile/Hazardous','2025-03-14 17:30:12',1, '3 - Negative Pressure Non-sterile/Hazardous')
,(4702,'C99-KU','4','Positive Pressure Sterile/Non-hazardous','2025-03-14 17:30:12',1, '4 - Positive Pressure Sterile/Non-hazardous')
,(4703,'C99-KU','5','Negative Pressure Sterile/Hazardous','2025-03-14 17:30:12',1, '5 - Negative Pressure Sterile/Hazardous')
,(4704,'D51-P7','1','Other Payer/Plan Is Tax Exempt','2025-03-14 17:30:12',1, '1 - Other Payer/Plan Is Tax Exempt')
,(4705,'D51-P7','2','Other Payer Religious Organization','2025-03-14 17:30:12',1, '2 - Other Payer Religious Organization')
,(4706,'D51-P7','3','Other Payer Tax Exempt Certificate','2025-03-14 17:30:12',1, '3 - Other Payer Tax Exempt Certificate')
,(4707,'D52-P8','1','Other Payer/Plan Is Regulatory Fee Exempt','2025-03-14 17:30:12',1, '1 - Other Payer/Plan Is Regulatory Fee Exempt')
,(4708,'D52-P8','2','Other Payer Religious Organization','2025-03-14 17:30:12',1, '2 - Other Payer Religious Organization')
,(4709,'D52-P8','3','Other Payer Regulatory Fee Exempt Certificate','2025-03-14 17:30:12',1, '3 - Other Payer Regulatory Fee Exempt Certificate')
,(4710,'D63-RN','AA','LA RS 46:2625','2025-03-14 17:30:12',1, 'AA - LA RS 46:2625')
,(4711,'D63-RN','AB','Other','2025-03-14 17:30:12',1, 'AB - Other')
,(4712,'D62-RM','1','Payer/Plan Is Regulatory Fee Exempt','2025-03-14 17:30:12',1, '1 - Payer/Plan Is Regulatory Fee Exempt')
,(4713,'D62-RM','2','Religious Organization','2025-03-14 17:30:12',1, '2 - Religious Organization')
,(4714,'D62-RM','3','Regulatory Fee Exempt Certificate','2025-03-14 17:30:12',1, '3 - Regulatory Fee Exempt Certificate')
,(4715,'D61-RL','AA','LA RS 46:2625','2025-03-14 17:30:12',1, 'AA - LA RS 46:2625')
,(4716,'D61-RL','AB','Other','2025-03-14 17:30:12',1, 'AB - Other')
,(4717,'D20-M2','MDL','Minimum Dollar Amount','2025-03-14 17:30:12',1, 'MDL - Minimum Dollar Amount')
,(4718,'D20-M2','MDS','Minimum Day Supply','2025-03-14 17:30:12',1, 'MDS - Minimum Day Supply')
,(4719,'D20-M2','MFL','Minimum Fills','2025-03-14 17:30:12',1, 'MFL - Minimum Fills')
,(4720,'D20-M2','MQY','Minimum Quantity','2025-03-14 17:30:12',1, 'MQY - Minimum Quantity')
,(4721,'D25-M7','RDS','Remaining Days Supply','2025-03-14 17:30:12',1, 'RDS - Remaining Days Supply')
,(4722,'D25-M7','RFL','Remaining Fills','2025-03-14 17:30:12',1, 'RFL - Remaining Fills')
,(4723,'D25-M7','RQY','Remaining Quantity','2025-03-14 17:30:12',1, 'RQY - Remaining Quantity')
,(4724,'D42-PV','1','Tier 1','2025-03-14 17:30:12',1, '1 - Tier 1')
,(4725,'D42-PV','2','Tier 2','2025-03-14 17:30:12',1, '2 - Tier 2')
,(4726,'D42-PV','3','Tier 3','2025-03-14 17:30:12',1, '3 - Tier 3')
,(4727,'D42-PV','4','Tier 4','2025-03-14 17:30:12',1, '4 - Tier 4')
,(4728,'D42-PV','5','Tier 5','2025-03-14 17:30:12',1, '5 - Tier 5')
,(4729,'D42-PV','9','Other','2025-03-14 17:30:12',1, '9 - Other')
,(4730,'D43-PZ','1','Step Therapy Required','2025-03-14 17:30:12',1, '1 - Step Therapy Required')
,(4731,'D43-PZ','2','Prescribed Drug Is Non-Formulary','2025-03-14 17:30:12',1, '2 - Prescribed Drug Is Non-Formulary')
,(4732,'D43-PZ','3','Prescribed Drug Requires Prior Authorization','2025-03-14 17:30:12',1, '3 - Prescribed Drug Requires Prior Authorization')
,(4733,'D43-PZ','4','Preferred Product','2025-03-14 17:30:12',1, '4 - Preferred Product')
,(4734,'D45-P1','1','Required Treatment, not otherwise specified','2025-03-14 17:30:12',1, '1 - Required Treatment, not otherwise specified')
,(4735,'D45-P1','2','Required Treatment, minimum time period duration required','2025-03-14 17:30:12',1, '2 - Required Treatment, minimum time period duration required')
,(4736,'D45-P1','3','Required Treatment within specified Time Period Range','2025-03-14 17:30:12',1, '3 - Required Treatment within specified Time Period Range')
,(4737,'D46-P2','1','Days','2025-03-14 17:30:12',1, '1 - Days')
,(4738,'D46-P2','2','Calendar Month','2025-03-14 17:30:12',1, '2 - Calendar Month')
,(4739,'D46-P2','3','Calendar Quarters','2025-03-14 17:30:12',1, '3 - Calendar Quarters')
,(4740,'D46-P2','4','Calendar Years','2025-03-14 17:30:12',1, '4 - Calendar Years')
,(4741,'D46-P2','5','Specified Date Range','2025-03-14 17:30:12',1, '5 - Specified Date Range')
,(4742,'D22-M4','1','Injectable Therapy','2025-03-14 17:30:12',1, '1 - Injectable Therapy')
,(4743,'D22-M4','2','Loading Quantity','2025-03-14 17:30:12',1, '2 - Loading Quantity')
,(4744,'D22-M4','3','Maintenance Quantity','2025-03-14 17:30:12',1, '3 - Maintenance Quantity')
,(4745,'D22-M4','4','Unbreakable Package Multiple Locations','2025-03-14 17:30:12',1, '4 - Unbreakable Package Multiple Locations')
,(4746,'D22-M4','5','Trial Fill','2025-03-14 17:30:12',1, '5 - Trial Fill')
,(4747,'D22-M4','6','Non-Commercially Available Dose','2025-03-14 17:30:12',1, '6 - Non-Commercially Available Dose')
,(4748,'D22-M4','7','Bundled Health Care Service','2025-03-14 17:30:12',1, '7 - Bundled Health Care Service')
,(4749,'D32-MS','1','Dual Status Level','2025-03-14 17:30:12',1, '1 - Dual Status Level')
,(4750,'D40-PN','1','Dialysis','2025-03-14 17:30:12',1, '1 - Dialysis')
,(4751,'D40-PN','2','Medicaid','2025-03-14 17:30:12',1, '2 - Medicaid')
,(4752,'D50-P6','BEHAVIORAL','Behavioral Health Benefit','2025-03-14 17:30:12',1, 'BEHAVIORAL - Behavioral Health Benefit')
,(4753,'D50-P6','DENTAL','Dental Benefit','2025-03-14 17:30:12',1, 'DENTAL - Dental Benefit')
,(4754,'D50-P6','DME','Durable Medical Equipment Benefit','2025-03-14 17:30:12',1, 'DME - Durable Medical Equipment Benefit')
,(4755,'D50-P6','MEDICAL','Medical Benefit','2025-03-14 17:30:12',1, 'MEDICAL - Medical Benefit')
,(4756,'D50-P6','RX','Prescription Benefit','2025-03-14 17:30:12',1, 'RX - Prescription Benefit')
,(4757,'D50-P6','VISION','Vision Benefit','2025-03-14 17:30:12',1, 'VISION - Vision Benefit')
,(4758,'D50-P6','UNKNOWN','Benefit Classification Unknown','2025-03-14 17:30:12',1, 'UNKNOWN - Benefit Classification Unknown')
,(4759,'D41-PQ','CP','COB Coverage Prior To Responding Payer','2025-03-14 17:30:12',1, 'CP - COB Coverage Prior To Responding Payer')
,(4760,'D41-PQ','CS','COB Coverage Subsequent To Responding Payer','2025-03-14 17:30:12',1, 'CS - COB Coverage Subsequent To Responding Payer')
,(4761,'D41-PQ','MX','Mutually Exclusive Benefits','2025-03-14 17:30:12',1, 'MX - Mutually Exclusive Benefits')
,(4762,'D41-PQ','CC','Change in Coverage','2025-03-14 17:30:12',1, 'CC - Change in Coverage')
,(4763,'D41-PQ','RP','Responding Payer','2025-03-14 17:30:12',1, 'RP - Responding Payer')
,(4764,'D41-PQ','CE','Centralized Eligibility','2025-03-14 17:30:12',1, 'CE - Centralized Eligibility')
,(4765,'D17-K8','AA','340B','2025-03-14 17:30:12',1, 'AA - 340B')
,(4766,'D17-K8','AB','Split Billing','2025-03-14 17:30:12',1, 'AB - Split Billing')
,(4767,'D17-K8','AC','Encounter','2025-03-14 17:30:12',1, 'AC - Encounter')
;

ALTER SEQUENCE "form_list_ncpdp_ecl_id_seq" RESTART WITH 4768;
SET session_replication_role = DEFAULT;
COMMIT;
