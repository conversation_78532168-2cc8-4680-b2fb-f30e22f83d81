BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_service_level;
INSERT INTO form_list_ss_service_level ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:31:10',1,'New Prescription','New','New Prescription'),
(2,'4/16/2024, 06:31:10',1,'Prescription Renewal','Refill','Prescription Renewal'),
(3,'4/16/2024, 06:31:10',1,'Prescription Change','Change','Prescription Change'),
(4,'4/16/2024, 06:31:10',1,'Prescription Cancel','Cancel','Prescription Cancel'),
(5,'4/16/2024, 06:31:10',1,'Electronic Prescribing of Controlled Substance.','ControlledSubstance','Electronic Prescribing of Controlled Substance.'),
(6,'4/16/2024, 06:31:10',1,'Prescription Fill Status','RxFill','Prescription Fill Status'),
(7,'4/16/2024, 06:31:10',1,'RxFill Indicator Change','RxFillIndicatorChange','RxFill Indicator Change'),
(8,'4/16/2024, 06:31:10',1,'Prescription Transfer','RxTransfer','Prescription Transfer'),
(9,'4/16/2024, 06:31:10',1,'New Prescription Request','NewRxRequest','New Prescription Request'),
(10,'4/16/2024, 06:31:10',1,'Deny New Prescription Request','NewRxResponseDenied','Deny New Prescription Request'),
(11,'4/16/2024, 06:31:10',1,'Temporary Prescription Hold','DrugAdministration','Temporary Prescription Hold'),
(12,'4/16/2024, 06:31:10',1,'Prescription Off Hold','Recertification','Prescription Off Hold'),
(13,'4/16/2024, 06:31:10',1,'Electronic Prior Authorization','ePA','Electronic Prior Authorization'),
(14,'4/16/2024, 06:31:10',1,'Record Locator and Exchange','RLE','Record Locator and Exchange'),
(15,'4/16/2024, 06:31:10',1,'Patient Medication Benefit Check','PatMedBenefitCheck','Patient Medication Benefit Check'),
(16,'4/16/2024, 06:31:10',1,'Specialty Patient Enrollment','SpecialtyPatientEnrollment','Specialty Patient Enrollment')
;
ALTER SEQUENCE "form_list_ss_service_level_id_seq" RESTART WITH 17;
SET session_replication_role = DEFAULT;
COMMIT;