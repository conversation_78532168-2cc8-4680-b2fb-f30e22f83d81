BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_service_reason_code;
INSERT INTO form_list_ss_service_reason_code ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:37:35',1,'Patient Question/Concern','PC','Patient Question/Concern'),
(2,'4/16/2024, 06:37:35',1,'Plan Protocol','PP','Plan Protocol'),
(3,'4/16/2024, 06:37:35',1,'Excessive Quantity','EX','Excessive Quantity'),
(4,'4/16/2024, 06:37:35',1,'Alcohol Conflict','OH','Alcohol Conflict'),
(5,'4/16/2024, 06:37:35',1,'Drug-Food interaction','DF','Drug-Food interaction'),
(6,'4/16/2024, 06:37:35',1,'Insufficient Quantity','NS','Insufficient Quantity'),
(7,'4/16/2024, 06:37:35',1,'Prescription Authentication','AN','Prescription Authentication'),
(8,'4/16/2024, 06:37:35',1,'High Dose','HD','High Dose'),
(9,'4/16/2024, 06:37:35',1,'Suboptimal Compliance','SC','Suboptimal Compliance'),
(10,'4/16/2024, 06:37:35',1,'Drug-Age','PA','Drug-Age'),
(11,'4/16/2024, 06:37:35',1,'Drug-Pregnancy','PG','Drug-Pregnancy'),
(12,'4/16/2024, 06:37:35',1,'Drug-Allergy','DA','Drug-Allergy'),
(13,'4/16/2024, 06:37:35',1,'Iatrogenic Condition','IC','Iatrogenic Condition'),
(14,'4/16/2024, 06:37:35',1,'Insufficient Duration','MN','Insufficient Duration'),
(15,'4/16/2024, 06:37:35',1,'Drug Incompatibility','DI','Drug Incompatibility'),
(16,'4/16/2024, 06:37:35',1,'Drug-Lab Conflict','DL','Drug-Lab Conflict'),
(17,'4/16/2024, 06:37:35',1,'Adverse Drug Reaction','AR','Adverse Drug Reaction'),
(18,'4/16/2024, 06:37:35',1,'Drug-Gender','SX','Drug-Gender'),
(19,'4/16/2024, 06:37:35',1,'Prior Adverse Reaction','PR','Prior Adverse Reaction'),
(20,'4/16/2024, 06:37:35',1,'Drug-Disease (Inferred)','DC','Drug-Disease (Inferred)'),
(21,'4/16/2024, 06:37:35',1,'Missing Information/Clarification','MS','Missing Information/Clarification'),
(22,'4/16/2024, 06:37:35',1,'Therapeutic','TD','Therapeutic'),
(23,'4/16/2024, 06:37:35',1,'Product Selection Opportunity','PS','Product Selection Opportunity'),
(24,'4/16/2024, 06:37:35',1,'Excessive Duration','MX','Excessive Duration'),
(25,'4/16/2024, 06:37:35',1,'Low Dose','LD','Low Dose'),
(26,'4/16/2024, 06:37:35',1,'Lock In Recipient','LK','Lock In Recipient'),
(27,'4/16/2024, 06:37:35',1,'Patient Education/Instruction','ED','Patient Education/Instruction'),
(28,'4/16/2024, 06:37:35',1,'Preventive Health Care','PH','Preventive Health Care'),
(29,'4/16/2024, 06:37:35',1,'Chronic Disease Management','CD','Chronic Disease Management'),
(30,'4/16/2024, 06:37:35',1,'Non-Formulary Drug','NF','Non-Formulary Drug'),
(31,'4/16/2024, 06:37:35',1,'Non-covered Drug Purchase','NC','Non-covered Drug Purchase'),
(32,'4/16/2024, 06:37:35',1,'Drug Not Available','NA','Drug Not Available'),
(33,'4/16/2024, 06:37:35',1,'Apparent Drug Misuse','DM','Apparent Drug Misuse'),
(34,'4/16/2024, 06:37:35',1,'Call Help Desk','CH','Call Help Desk'),
(35,'4/16/2024, 06:37:35',1,'Patient Complaint/Symptom','CS','Patient Complaint/Symptom'),
(36,'4/16/2024, 06:37:35',1,'Drug-Drug Interaction','DD','Drug-Drug Interaction'),
(37,'4/16/2024, 06:37:35',1,'Suboptimal Drug/Indication','SD','Suboptimal Drug/Indication'),
(38,'4/16/2024, 06:37:35',1,'Suspected Environmental Risk','RE','Suspected Environmental Risk'),
(39,'4/16/2024, 06:37:35',1,'Laboratory Test Needed','TN','Laboratory Test Needed'),
(40,'4/16/2024, 06:37:35',1,'Dose Range Conflict','DR','Dose Range Conflict'),
(41,'4/16/2024, 06:37:35',1,'Overuse','ER','Overuse'),
(42,'4/16/2024, 06:37:35',1,'Side Effect','SE','Side Effect'),
(43,'4/16/2024, 06:37:35',1,'Additional Drug Needed','AD','Additional Drug Needed'),
(44,'4/16/2024, 06:37:35',1,'New Patient Processing','NP','New Patient Processing'),
(45,'4/16/2024, 06:37:35',1,'Payer/Processor Question','TP','Payer/Processor Question'),
(46,'4/16/2024, 06:37:35',1,'Underuse','LR','Underuse'),
(47,'4/16/2024, 06:37:35',1,'New Disease/Diagnosis','ND','New Disease/Diagnosis'),
(48,'4/16/2024, 06:37:35',1,'Suboptimal Dosage Form','SF','Suboptimal Dosage Form'),
(49,'4/16/2024, 06:37:35',1,'Unnecessary Drug','NN','Unnecessary Drug'),
(50,'4/16/2024, 06:37:35',1,'Duplicate Drug','UD','Duplicate Drug'),
(51,'4/16/2024, 06:37:35',1,'Prescriber Consultation','PN','Prescriber Consultation'),
(52,'4/16/2024, 06:37:35',1,'Tobacco Use','DS','Tobacco Use'),
(53,'4/16/2024, 06:37:35',1,'Drug-Disease (Reported)','MC','Drug-Disease (Reported)'),
(54,'4/16/2024, 06:37:35',1,'Health Provider Referral','RF','Health Provider Referral'),
(55,'4/16/2024, 06:37:35',1,'Suboptimal Regimen','SR','Suboptimal Regimen'),
(56,'4/16/2024, 06:37:35',1,'Lactation/Nursing Interaction','NR','Lactation/Nursing Interaction')
;
ALTER SEQUENCE "form_list_ss_service_reason_code_id_seq" RESTART WITH 57;
SET session_replication_role = DEFAULT;
COMMIT;