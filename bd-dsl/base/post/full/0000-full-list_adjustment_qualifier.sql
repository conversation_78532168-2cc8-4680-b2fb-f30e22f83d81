BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_adjustment_qualifier;
INSERT INTO form_list_adjustment_qualifier ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 08:59:28',1,'Diagnosis','BF','Diagnosis'),
(2,'4/16/2024, 08:59:28',1,'Principal Diagnosis','BK','Principal Diagnosis'),
(3,'4/16/2024, 08:59:28',1,'Mutually Defined','ZZ','Mutually Defined'),
(4,'4/16/2024, 08:59:28',1,'Deductible Amount','1','Deductible Amount'),
(5,'4/16/2024, 08:59:28',1,'The diagnosis is inconsistent with the patient''s gender. Note Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','10','The diagnosis is inconsistent with the patient''s gender. Note Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(6,'4/16/2024, 08:59:28',1,'Payment made to patient/insured/responsible party/employer.','100','Payment made to patient/insured/responsible party/employer.'),
(7,'4/16/2024, 08:59:28',1,'Predetermination - anticipated payment upon completion of services or claim adjudication.','101','Predetermination - anticipated payment upon completion of services or claim adjudication.'),
(8,'4/16/2024, 08:59:28',1,'Major Medical Adjustment.','102','Major Medical Adjustment.'),
(9,'4/16/2024, 08:59:28',1,'Provider promotional discount (e.g., Senior citizen discount).','103','Provider promotional discount (e.g., Senior citizen discount).'),
(10,'4/16/2024, 08:59:28',1,'Managed care withholding.','104','Managed care withholding.'),
(11,'4/16/2024, 08:59:28',1,'Tax withholding.','105','Tax withholding.'),
(12,'4/16/2024, 08:59:28',1,'Patient payment option/election not in effect.','106','Patient payment option/election not in effect.'),
(13,'4/16/2024, 08:59:28',1,'The related or qualifying claim/service was not identified on this claim. Note Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','107','The related or qualifying claim/service was not identified on this claim. Note Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(14,'4/16/2024, 08:59:28',1,'Rent/purchase guidelines were not met. Note Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','108','Rent/purchase guidelines were not met. Note Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(15,'4/16/2024, 08:59:28',1,'Claim/service not covered by this payer/contractor. You must send the claim/service to the correct payer/contractor.','109','Claim/service not covered by this payer/contractor. You must send the claim/service to the correct payer/contractor.'),
(16,'4/16/2024, 08:59:28',1,'The diagnosis is inconsistent with the procedure. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','11','The diagnosis is inconsistent with the procedure. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(17,'4/16/2024, 08:59:28',1,'Billing date predates service date.','110','Billing date predates service date.'),
(18,'4/16/2024, 08:59:28',1,'Not covered unless the provider accepts assignment.','111','Not covered unless the provider accepts assignment.'),
(19,'4/16/2024, 08:59:28',1,'Service not furnished directly to the patient and/or not documented.','112','Service not furnished directly to the patient and/or not documented.'),
(20,'4/16/2024, 08:59:28',1,'Procedure/product not approved by the Food and Drug Administration.','114','Procedure/product not approved by the Food and Drug Administration.'),
(21,'4/16/2024, 08:59:28',1,'Procedure postponed, canceled, or delayed.','115','Procedure postponed, canceled, or delayed.'),
(22,'4/16/2024, 08:59:28',1,'The advance indemnification notice signed by the patient did not comply with requirements.','116','The advance indemnification notice signed by the patient did not comply with requirements.'),
(23,'4/16/2024, 08:59:28',1,'Transportation is only covered to the closest facility that can provide the necessary care.','117','Transportation is only covered to the closest facility that can provide the necessary care.'),
(24,'4/16/2024, 08:59:28',1,'ESRD network support adjustment.','118','ESRD network support adjustment.'),
(25,'4/16/2024, 08:59:28',1,'Benefit maximum for this time period or occurrence has been reached.','119','Benefit maximum for this time period or occurrence has been reached.'),
(26,'4/16/2024, 08:59:28',1,'The diagnosis is inconsistent with the provider type. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','12','The diagnosis is inconsistent with the provider type. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(27,'4/16/2024, 08:59:28',1,'Indemnification adjustment - compensation for outstanding member responsibility.','121','Indemnification adjustment - compensation for outstanding member responsibility.'),
(28,'4/16/2024, 08:59:28',1,'Psychiatric reduction.','122','Psychiatric reduction.'),
(29,'4/16/2024, 08:59:28',1,'Submission/billing error(s). At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','125','Submission/billing error(s). At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(30,'4/16/2024, 08:59:28',1,'Newborn''s services are covered in the mother''s Allowance.','128','Newborn''s services are covered in the mother''s Allowance.'),
(31,'4/16/2024, 08:59:28',1,'Prior processing information appears incorrect. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','129','Prior processing information appears incorrect. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(32,'4/16/2024, 08:59:28',1,'The date of death precedes the date of service.','13','The date of death precedes the date of service.'),
(33,'4/16/2024, 08:59:28',1,'Claim submission fee.','130','Claim submission fee.'),
(34,'4/16/2024, 08:59:28',1,'Claim specific negotiated discount.','131','Claim specific negotiated discount.'),
(35,'4/16/2024, 08:59:28',1,'Prearranged demonstration project adjustment.','132','Prearranged demonstration project adjustment.'),
(36,'4/16/2024, 08:59:28',1,'The disposition of the claim/service is pending further review.','133','The disposition of the claim/service is pending further review.'),
(37,'4/16/2024, 08:59:28',1,'Technical fees removed from charges.','134','Technical fees removed from charges.'),
(38,'4/16/2024, 08:59:28',1,'Interim bills cannot be processed.','135','Interim bills cannot be processed.'),
(39,'4/16/2024, 08:59:28',1,'Failure to follow prior payer''s coverage rules. (Use Group Code OA).','136','Failure to follow prior payer''s coverage rules. (Use Group Code OA).'),
(40,'4/16/2024, 08:59:28',1,'Regulatory Surcharges, Assessments, Allowances or Health Related Taxes.','137','Regulatory Surcharges, Assessments, Allowances or Health Related Taxes.'),
(41,'4/16/2024, 08:59:28',1,'Appeal procedures not followed or time limits not met.','138','Appeal procedures not followed or time limits not met.'),
(42,'4/16/2024, 08:59:28',1,'Contracted funding agreement - Subscriber is employed by the provider of services.','139','Contracted funding agreement - Subscriber is employed by the provider of services.'),
(43,'4/16/2024, 08:59:28',1,'The date of birth follows the date of service.','14','The date of birth follows the date of service.'),
(44,'4/16/2024, 08:59:28',1,'Patient/Insured health identification number and name do not match.','140','Patient/Insured health identification number and name do not match.'),
(45,'4/16/2024, 08:59:28',1,'Monthly Medicaid patient liability amount.','142','Monthly Medicaid patient liability amount.'),
(46,'4/16/2024, 08:59:28',1,'Portion of payment deferred.','143','Portion of payment deferred.'),
(47,'4/16/2024, 08:59:28',1,'Incentive adjustment, e.g. preferred product/service.','144','Incentive adjustment, e.g. preferred product/service.'),
(48,'4/16/2024, 08:59:28',1,'Diagnosis was invalid for the date(s) of service reported.','146','Diagnosis was invalid for the date(s) of service reported.'),
(49,'4/16/2024, 08:59:28',1,'Provider contracted/negotiated rate expired or not on file.','147','Provider contracted/negotiated rate expired or not on file.'),
(50,'4/16/2024, 08:59:28',1,'Information from another provider was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','148','Information from another provider was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(51,'4/16/2024, 08:59:28',1,'Lifetime benefit maximum has been reached for this service/benefit category.','149','Lifetime benefit maximum has been reached for this service/benefit category.'),
(52,'4/16/2024, 08:59:28',1,'The authorization number is missing, invalid, or does not apply to the billed services or provider.','15','The authorization number is missing, invalid, or does not apply to the billed services or provider.'),
(53,'4/16/2024, 08:59:28',1,'Payer deems the information submitted does not support this level of service.','150','Payer deems the information submitted does not support this level of service.'),
(54,'4/16/2024, 08:59:28',1,'Payment adjusted because the payer deems the information submitted does not support this many/frequency of services.','151','Payment adjusted because the payer deems the information submitted does not support this many/frequency of services.'),
(55,'4/16/2024, 08:59:28',1,'Payer deems the information submitted does not support this length of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','152','Payer deems the information submitted does not support this length of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(56,'4/16/2024, 08:59:28',1,'Payer deems the information submitted does not support this dosage.','153','Payer deems the information submitted does not support this dosage.'),
(57,'4/16/2024, 08:59:28',1,'Payer deems the information submitted does not support this day''s supply.','154','Payer deems the information submitted does not support this day''s supply.'),
(58,'4/16/2024, 08:59:28',1,'This claim is denied because the patient refused the service/procedure.','155','This claim is denied because the patient refused the service/procedure.'),
(59,'4/16/2024, 08:59:28',1,'Payment denied/reduced because service/procedure was provided as a result of an act of war.','157','Payment denied/reduced because service/procedure was provided as a result of an act of war.'),
(60,'4/16/2024, 08:59:28',1,'Payment denied/reduced because the service/procedure was provided outside of the United States.','158','Payment denied/reduced because the service/procedure was provided outside of the United States.'),
(61,'4/16/2024, 08:59:28',1,'Payment denied/reduced because the service/procedure was provided as a result of terrorism.','159','Payment denied/reduced because the service/procedure was provided as a result of terrorism.'),
(62,'4/16/2024, 08:59:28',1,'Claim/service lacks information which is needed for adjudication. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','16','Claim/service lacks information which is needed for adjudication. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(63,'4/16/2024, 08:59:28',1,'Payment denied/reduced because injury/illness was the result of an activity that is a benefit exclusion.','160','Payment denied/reduced because injury/illness was the result of an activity that is a benefit exclusion.'),
(64,'4/16/2024, 08:59:28',1,'Provider performance bonus','161','Provider performance bonus'),
(65,'4/16/2024, 08:59:28',1,'State-mandated Requirement for Property and Casualty, see Claim Payment Remarks Code for specific explanation.','162','State-mandated Requirement for Property and Casualty, see Claim Payment Remarks Code for specific explanation.'),
(66,'4/16/2024, 08:59:28',1,'Claim/Service adjusted because the attachment referenced on the claim was not received.','163','Claim/Service adjusted because the attachment referenced on the claim was not received.'),
(67,'4/16/2024, 08:59:28',1,'Claim/Service adjusted because the attachment referenced on the claim was not received in a timely fashion.','164','Claim/Service adjusted because the attachment referenced on the claim was not received in a timely fashion.'),
(68,'4/16/2024, 08:59:28',1,'Payment denied /reduced for absence of, or exceeded referral','165','Payment denied /reduced for absence of, or exceeded referral'),
(69,'4/16/2024, 08:59:28',1,'These services were submitted after this payer''s responsibility for processing claims under this plan ended.','166','These services were submitted after this payer''s responsibility for processing claims under this plan ended.'),
(70,'4/16/2024, 08:59:28',1,'This (these) diagnosis(es) is (are) not covered.','167','This (these) diagnosis(es) is (are) not covered.'),
(71,'4/16/2024, 08:59:28',1,'Payments denied as Service(s) have been considered under the patient''s medical plan. Benefits are not available under this dental plan','168','Payments denied as Service(s) have been considered under the patient''s medical plan. Benefits are not available under this dental plan'),
(72,'4/16/2024, 08:59:28',1,'Payment adjusted because an alternate benefit has been provided','169','Payment adjusted because an alternate benefit has been provided'),
(73,'4/16/2024, 08:59:28',1,'Payment is denied when performed/billed by this type of provider.','170','Payment is denied when performed/billed by this type of provider.'),
(74,'4/16/2024, 08:59:28',1,'Payment is denied when performed/billed by this type of provider in this type of facility.','171','Payment is denied when performed/billed by this type of provider in this type of facility.'),
(75,'4/16/2024, 08:59:28',1,'Payment is adjusted when performed/billed by a provider of this specialty','172','Payment is adjusted when performed/billed by a provider of this specialty'),
(76,'4/16/2024, 08:59:28',1,'Payment adjusted because this service/equipment was not prescribed by a physician','173','Payment adjusted because this service/equipment was not prescribed by a physician'),
(77,'4/16/2024, 08:59:28',1,'Payment denied because this service was not prescribed prior to delivery','174','Payment denied because this service was not prescribed prior to delivery'),
(78,'4/16/2024, 08:59:28',1,'Payment denied because the prescription is incomplete','175','Payment denied because the prescription is incomplete'),
(79,'4/16/2024, 08:59:28',1,'Payment denied because the prescription is not current','176','Payment denied because the prescription is not current'),
(80,'4/16/2024, 08:59:28',1,'Payment denied because the patient has not met the required eligibility requirements','177','Payment denied because the patient has not met the required eligibility requirements'),
(81,'4/16/2024, 08:59:28',1,'Payment adjusted because the patient has not met the required spend down requirements.','178','Payment adjusted because the patient has not met the required spend down requirements.'),
(82,'4/16/2024, 08:59:28',1,'Payment adjusted because the patient has not met the required waiting requirements','179','Payment adjusted because the patient has not met the required waiting requirements'),
(83,'4/16/2024, 08:59:28',1,'Duplicate claim/service. This change effective 1/1/2013 - Exact duplicate claim/service (Use with Group Code OA).','18','Duplicate claim/service. This change effective 1/1/2013 - Exact duplicate claim/service (Use with Group Code OA).'),
(84,'4/16/2024, 08:59:28',1,'Payment adjusted because the patient has not met the required residency requirements','180','Payment adjusted because the patient has not met the required residency requirements'),
(85,'4/16/2024, 08:59:28',1,'Payment adjusted because this procedure code was invalid on the date of service','181','Payment adjusted because this procedure code was invalid on the date of service'),
(86,'4/16/2024, 08:59:28',1,'Payment adjusted because the procedure modifier was invalid on the date of service','182','Payment adjusted because the procedure modifier was invalid on the date of service'),
(87,'4/16/2024, 08:59:28',1,'The referring provider is not eligible to refer to the service billed.','183','The referring provider is not eligible to refer to the service billed.'),
(88,'4/16/2024, 08:59:28',1,'The prescribing/ordering provider is not eligible to prescribe/order the service billed.','184','The prescribing/ordering provider is not eligible to prescribe/order the service billed.'),
(89,'4/16/2024, 08:59:28',1,'The rendering provider is not eligible to perform the service billed.','185','The rendering provider is not eligible to perform the service billed.'),
(90,'4/16/2024, 08:59:28',1,'Payment adjusted since the level of care changed','186','Payment adjusted since the level of care changed'),
(91,'4/16/2024, 08:59:28',1,'Health Savings account payments','187','Health Savings account payments'),
(92,'4/16/2024, 08:59:28',1,'This product/procedure is only covered when used according to FDA recommendations.','188','This product/procedure is only covered when used according to FDA recommendations.'),
(93,'4/16/2024, 08:59:28',1,'"Not otherwise classified" or "unlisted" procedure code (CPT/HCPCS) was billed when there is a specific procedure code for this procedure/service','189','"Not otherwise classified" or "unlisted" procedure code (CPT/HCPCS) was billed when there is a specific procedure code for this procedure/service'),
(94,'4/16/2024, 08:59:28',1,'This is a work-related injury/illness and thus the liability of the Worker''s Compensation Carrier.','19','This is a work-related injury/illness and thus the liability of the Worker''s Compensation Carrier.'),
(95,'4/16/2024, 08:59:28',1,'Payment is included in the allowance for a Skilled Nursing Facility (SNF) qualified stay.','190','Payment is included in the allowance for a Skilled Nursing Facility (SNF) qualified stay.'),
(96,'4/16/2024, 08:59:28',1,'Claim denied because this is not a work related injury/illness and thus not the liability of the worker''s compensation carrier.','191','Claim denied because this is not a work related injury/illness and thus not the liability of the worker''s compensation carrier.'),
(97,'4/16/2024, 08:59:28',1,'Non standard adjustment code from paper remittance advice.','192','Non standard adjustment code from paper remittance advice.'),
(98,'4/16/2024, 08:59:28',1,'Original payment decision is being maintained. This claim was processed properly the first time.','193','Original payment decision is being maintained. This claim was processed properly the first time.'),
(99,'4/16/2024, 08:59:28',1,'Payment adjusted when anesthesia is performed by the operating physician, the assistant surgeon or the attending physician','194','Payment adjusted when anesthesia is performed by the operating physician, the assistant surgeon or the attending physician'),
(100,'4/16/2024, 08:59:28',1,'Claim denied charges.','195','Claim denied charges.'),
(101,'4/16/2024, 08:59:28',1,'Precertification/authorization/notification absent.','197','Precertification/authorization/notification absent.'),
(102,'4/16/2024, 08:59:28',1,'Precertification/authorization exceeded.','198','Precertification/authorization exceeded.'),
(103,'4/16/2024, 08:59:28',1,'Revenue code and Procedure code do not match.','199','Revenue code and Procedure code do not match.'),
(104,'4/16/2024, 08:59:28',1,'Coinsurance Amount','2','Coinsurance Amount'),
(105,'4/16/2024, 08:59:28',1,'This injury/illness is covered by the liability carrier.','20','This injury/illness is covered by the liability carrier.'),
(106,'4/16/2024, 08:59:28',1,'Expenses incurred during lapse in coverage','200','Expenses incurred during lapse in coverage'),
(107,'4/16/2024, 08:59:28',1,'Patient is responsible for the amount of this claim/service through ''set aside arrangement'' or other agreement.','201','Patient is responsible for the amount of this claim/service through ''set aside arrangement'' or other agreement.'),
(108,'4/16/2024, 08:59:28',1,'Non-covered personal comfort or convenience services.','202','Non-covered personal comfort or convenience services.'),
(109,'4/16/2024, 08:59:28',1,'Discontinued or reduced service.','203','Discontinued or reduced service.'),
(110,'4/16/2024, 08:59:28',1,'This service/equipment/drug is not covered under the patient''s current benefit plan','204','This service/equipment/drug is not covered under the patient''s current benefit plan'),
(111,'4/16/2024, 08:59:28',1,'Pharmacy discount card processing fee','205','Pharmacy discount card processing fee'),
(112,'4/16/2024, 08:59:28',1,'National Provider Identifier - missing.','206','National Provider Identifier - missing.'),
(113,'4/16/2024, 08:59:28',1,'National Provider identifier - Invalid format','207','National Provider identifier - Invalid format'),
(114,'4/16/2024, 08:59:28',1,'National Provider Identifier - Not matched.','208','National Provider Identifier - Not matched.'),
(115,'4/16/2024, 08:59:28',1,'Per regulatory or other agreement. The provider cannot collect this amount from the patient. However, this amount may be billed to subsequent payers. Refund to patients if collected. (Use Group code OA)','209','Per regulatory or other agreement. The provider cannot collect this amount from the patient. However, this amount may be billed to subsequent payers. Refund to patients if collected. (Use Group code OA)'),
(116,'4/16/2024, 08:59:28',1,'This injury/illness is the liability of the no-fault carrier.','21','This injury/illness is the liability of the no-fault carrier.'),
(117,'4/16/2024, 08:59:28',1,'Payment adjusted because pre-certification/authorization not received in a timely fashion','210','Payment adjusted because pre-certification/authorization not received in a timely fashion'),
(118,'4/16/2024, 08:59:28',1,'National Drug Codes (NDC) not eligible for rebate, are not covered.','211','National Drug Codes (NDC) not eligible for rebate, are not covered.'),
(119,'4/16/2024, 08:59:28',1,'Administrative surcharges are not covered','212','Administrative surcharges are not covered'),
(120,'4/16/2024, 08:59:28',1,'Non-compliance with the physician self referral prohibition legislation or payer policy.','213','Non-compliance with the physician self referral prohibition legislation or payer policy.'),
(121,'4/16/2024, 08:59:28',1,'Workers'' Compensation claim adjudicated as non-compensable. This Payer is not liable for claim or service/treatment. (Note  To be used for Workers'' Compensation only)','214','Workers'' Compensation claim adjudicated as non-compensable. This Payer is not liable for claim or service/treatment. (Note  To be used for Workers'' Compensation only)'),
(122,'4/16/2024, 08:59:28',1,'Based on subrogation of a third party settlement','215','Based on subrogation of a third party settlement'),
(123,'4/16/2024, 08:59:28',1,'Based on the findings of a review organization','216','Based on the findings of a review organization'),
(124,'4/16/2024, 08:59:28',1,'Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. (Note  To be used for Property and Casualty only)','217','Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. (Note  To be used for Property and Casualty only)'),
(125,'4/16/2024, 08:59:28',1,'Based on entitlement to benefits (Note  To be used for Workers'' Compensation only)','218','Based on entitlement to benefits (Note  To be used for Workers'' Compensation only)'),
(126,'4/16/2024, 08:59:28',1,'Based on extent of injury (Note  To be used for Workers'' Compensation only)','219','Based on extent of injury (Note  To be used for Workers'' Compensation only)'),
(127,'4/16/2024, 08:59:28',1,'This care may be covered by another payer per coordination of benefits.','22','This care may be covered by another payer per coordination of benefits.'),
(128,'4/16/2024, 08:59:28',1,'The applicable fee schedule/fee database does not contain the billed code. Please resubmit a bill with the appropriate fee schedule/fee database code(s) that best describe the service(s) provided and supporting documentation if required.','220','The applicable fee schedule/fee database does not contain the billed code. Please resubmit a bill with the appropriate fee schedule/fee database code(s) that best describe the service(s) provided and supporting documentation if required.'),
(129,'4/16/2024, 08:59:28',1,'Claim is under investigation. (Note  To be used for Property and Casualty only.)','221','Claim is under investigation. (Note  To be used for Property and Casualty only.)'),
(130,'4/16/2024, 08:59:28',1,'Exceeds the contracted maximum number of hours/days/units by this provider for this period. This is not patient specific. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','222','Exceeds the contracted maximum number of hours/days/units by this provider for this period. This is not patient specific. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(131,'4/16/2024, 08:59:28',1,'Adjustment code for mandated federal, state or local law/regulation that is not already covered by another code and is mandated before a new code can be created.','223','Adjustment code for mandated federal, state or local law/regulation that is not already covered by another code and is mandated before a new code can be created.'),
(132,'4/16/2024, 08:59:28',1,'Patient identification compromised by identity theft. Identity verification required for processing this and future claims.','224','Patient identification compromised by identity theft. Identity verification required for processing this and future claims.'),
(133,'4/16/2024, 08:59:28',1,'Penalty or Interest Payment by Payer (Only used for plan to plan encounter reporting within the 837)','225','Penalty or Interest Payment by Payer (Only used for plan to plan encounter reporting within the 837)'),
(134,'4/16/2024, 08:59:28',1,'Information requested from the Billing/Rendering Provider was not provided timely or was insufficient/incomplete. At least one Remark Code must be provided (may be either the Remittance Advice Remark Code or NCPDP Reject Reason code.)','226','Information requested from the Billing/Rendering Provider was not provided timely or was insufficient/incomplete. At least one Remark Code must be provided (may be either the Remittance Advice Remark Code or NCPDP Reject Reason code.)'),
(135,'4/16/2024, 08:59:28',1,'Information requested from the patient/insured/responsible party was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be either the Remittance Advice Remark Code or NCPDP Reject Reason Code.)','227','Information requested from the patient/insured/responsible party was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be either the Remittance Advice Remark Code or NCPDP Reject Reason Code.)'),
(136,'4/16/2024, 08:59:28',1,'Denied for failure of this provider, another provider or the subscriber to supply requested information to a previous payer for their adjudication','228','Denied for failure of this provider, another provider or the subscriber to supply requested information to a previous payer for their adjudication'),
(137,'4/16/2024, 08:59:28',1,'Partial charge amount not considered by Medicare due to the initial claim Type of Bill being 12X.','229','Partial charge amount not considered by Medicare due to the initial claim Type of Bill being 12X.'),
(138,'4/16/2024, 08:59:28',1,'The impact of prior payer(s) adjudication including payments and/or adjustments.','23','The impact of prior payer(s) adjudication including payments and/or adjustments.'),
(139,'4/16/2024, 08:59:28',1,'No available or correlating CPT/HCPCS code to describe this service.','230','No available or correlating CPT/HCPCS code to describe this service.'),
(140,'4/16/2024, 08:59:28',1,'Mutually exclusive procedures cannot be done on the same day/setting.','231','Mutually exclusive procedures cannot be done on the same day/setting.'),
(141,'4/16/2024, 08:59:28',1,'Institutional Transfer Amount. Note - Applies to institutional claims only and explains the DRG amount difference when the patient care crosses multiple institutions.','232','Institutional Transfer Amount. Note - Applies to institutional claims only and explains the DRG amount difference when the patient care crosses multiple institutions.'),
(142,'4/16/2024, 08:59:28',1,'Services/charges related to the treatment of a hospital-acquired condition or preventable medical error.','233','Services/charges related to the treatment of a hospital-acquired condition or preventable medical error.'),
(143,'4/16/2024, 08:59:28',1,'This procedure is not paid separately. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','234','This procedure is not paid separately. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(144,'4/16/2024, 08:59:28',1,'Sales Tax','235','Sales Tax'),
(145,'4/16/2024, 08:59:28',1,'This procedure or procedure/modifier combination is not compatible with another procedure or procedure/modifier combination provided on the same day according to the National Correct Coding Initiative or workers compensation requirements.','236','This procedure or procedure/modifier combination is not compatible with another procedure or procedure/modifier combination provided on the same day according to the National Correct Coding Initiative or workers compensation requirements.'),
(146,'4/16/2024, 08:59:28',1,'Legislated/Regulatory Penalty. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','237','Legislated/Regulatory Penalty. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(147,'4/16/2024, 08:59:28',1,'Claim spans eligible and ineligible periods of coverage, this is the reduction for the ineligible period (use Group Code PR).','238','Claim spans eligible and ineligible periods of coverage, this is the reduction for the ineligible period (use Group Code PR).'),
(148,'4/16/2024, 08:59:28',1,'Claim spans eligible and ineligible periods of coverage. Rebill separate claims.','239','Claim spans eligible and ineligible periods of coverage. Rebill separate claims.'),
(149,'4/16/2024, 08:59:28',1,'Charges are covered under a capitation agreement/managed care plan.','24','Charges are covered under a capitation agreement/managed care plan.'),
(150,'4/16/2024, 08:59:28',1,'Services not provided by network/primary care providers.','242','Services not provided by network/primary care providers.'),
(151,'4/16/2024, 08:59:28',1,'Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property & Casualty only.','244','Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property & Casualty only.'),
(152,'4/16/2024, 08:59:28',1,'Provider performance program withhold.','245','Provider performance program withhold.'),
(153,'4/16/2024, 08:59:28',1,'This non-payable code is for required reporting only.','246','This non-payable code is for required reporting only.'),
(154,'4/16/2024, 08:59:28',1,'Deductible for Professional service rendered in an Institutional setting and billed on an Institutional claim.','247','Deductible for Professional service rendered in an Institutional setting and billed on an Institutional claim.'),
(155,'4/16/2024, 08:59:28',1,'Coinsurance for Professional service rendered in an Institutional setting and billed on an Institutional claim.','248','Coinsurance for Professional service rendered in an Institutional setting and billed on an Institutional claim.'),
(156,'4/16/2024, 08:59:28',1,'This claim has been identified as a resubmission. (Use only with Group Code CO)','249','This claim has been identified as a resubmission. (Use only with Group Code CO)'),
(157,'4/16/2024, 08:59:28',1,'The attachment content received is inconsistent with the expected content.','250','The attachment content received is inconsistent with the expected content.'),
(158,'4/16/2024, 08:59:28',1,'The attachment content received did not contain the content required to process this claim or service.','251','The attachment content received did not contain the content required to process this claim or service.'),
(159,'4/16/2024, 08:59:28',1,'An attachment is required to adjudicate this claim/service. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT).','252','An attachment is required to adjudicate this claim/service. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT).'),
(160,'4/16/2024, 08:59:28',1,'Sequestration - reduction in federal payment','253','Sequestration - reduction in federal payment'),
(161,'4/16/2024, 08:59:28',1,'The disposition of the claim/service is pending during the premium payment grace period, per Health Insurance Exchange requirements. (Use only with Group Code OA)','257','The disposition of the claim/service is pending during the premium payment grace period, per Health Insurance Exchange requirements. (Use only with Group Code OA)'),
(162,'4/16/2024, 08:59:28',1,'Claim/service not covered when patient is in custody/incarcerated. Applicable federal, state or local authority may cover the claim/service.','258','Claim/service not covered when patient is in custody/incarcerated. Applicable federal, state or local authority may cover the claim/service.'),
(163,'4/16/2024, 08:59:28',1,'Additional payment for Dental/Vision service utilization','259','Additional payment for Dental/Vision service utilization'),
(164,'4/16/2024, 08:59:28',1,'Expenses incurred prior to coverage.','26','Expenses incurred prior to coverage.'),
(165,'4/16/2024, 08:59:28',1,'Processed under Medicaid ACA Enhanced Fee Schedule.','260','Processed under Medicaid ACA Enhanced Fee Schedule.'),
(166,'4/16/2024, 08:59:28',1,'Adjustment for delivery cost. Note  To be used for pharmaceuticals only.','262','Adjustment for delivery cost. Note  To be used for pharmaceuticals only.'),
(167,'4/16/2024, 08:59:28',1,'Adjustment for shipping cost. Note  To be used for pharmaceuticals only.','263','Adjustment for shipping cost. Note  To be used for pharmaceuticals only.'),
(168,'4/16/2024, 08:59:28',1,'Adjustment for postage cost. Note  To be used for pharmaceuticals only.','264','Adjustment for postage cost. Note  To be used for pharmaceuticals only.'),
(169,'4/16/2024, 08:59:28',1,'Adjustment for administrative cost. Note  To be used for pharmaceuticals only.','265','Adjustment for administrative cost. Note  To be used for pharmaceuticals only.'),
(170,'4/16/2024, 08:59:28',1,'Adjustment for compound preparation cost. Note  To be used for pharmaceuticals only.','266','Adjustment for compound preparation cost. Note  To be used for pharmaceuticals only.'),
(171,'4/16/2024, 08:59:28',1,'Claim spans multiple months. Rebill separate claim/service.','267','Claim spans multiple months. Rebill separate claim/service.'),
(172,'4/16/2024, 08:59:28',1,'Claim spans 2 calendar years. Please resubmit one claim per calendar year.','268','Claim spans 2 calendar years. Please resubmit one claim per calendar year.'),
(173,'4/16/2024, 08:59:28',1,'Expenses incurred after coverage terminated.','27','Expenses incurred after coverage terminated.'),
(174,'4/16/2024, 08:59:28',1,'The time limit for filing has expired.','29','The time limit for filing has expired.'),
(175,'4/16/2024, 08:59:28',1,'Co-payment Amount','3','Co-payment Amount'),
(176,'4/16/2024, 08:59:28',1,'Patient cannot be identified as our insured.','31','Patient cannot be identified as our insured.'),
(177,'4/16/2024, 08:59:28',1,'Our records indicate that this dependent is not an eligible dependent as defined.','32','Our records indicate that this dependent is not an eligible dependent as defined.'),
(178,'4/16/2024, 08:59:28',1,'Insured has no dependent coverage.','33','Insured has no dependent coverage.'),
(179,'4/16/2024, 08:59:28',1,'Insured has no coverage for newborns.','34','Insured has no coverage for newborns.'),
(180,'4/16/2024, 08:59:28',1,'Lifetime benefit maximum has been reached.','35','Lifetime benefit maximum has been reached.'),
(181,'4/16/2024, 08:59:28',1,'Services not provided or authorized by designated (network/primary care) providers.','38','Services not provided or authorized by designated (network/primary care) providers.'),
(182,'4/16/2024, 08:59:28',1,'Services denied at the time authorization/pre-certification was requested.','39','Services denied at the time authorization/pre-certification was requested.'),
(183,'4/16/2024, 08:59:28',1,'The procedure code is inconsistent with the modifier used or a required modifier is missing. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','4','The procedure code is inconsistent with the modifier used or a required modifier is missing. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(184,'4/16/2024, 08:59:28',1,'Charges do not meet qualifications for emergent/urgent care. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','40','Charges do not meet qualifications for emergent/urgent care. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(185,'4/16/2024, 08:59:28',1,'Prompt-pay discount.','44','Prompt-pay discount.'),
(186,'4/16/2024, 08:59:28',1,'Charge exceeds fee schedule/maximum allowable or contracted/legislated fee arrangement. (Use Group Codes PR or CO depending upon liability).','45','Charge exceeds fee schedule/maximum allowable or contracted/legislated fee arrangement. (Use Group Codes PR or CO depending upon liability).'),
(187,'4/16/2024, 08:59:28',1,'These are non-covered services because this is a routine exam or screening procedure done in conjunction with a routine exam. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','49','These are non-covered services because this is a routine exam or screening procedure done in conjunction with a routine exam. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(188,'4/16/2024, 08:59:28',1,'The procedure code/bill type is inconsistent with the place of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','5','The procedure code/bill type is inconsistent with the place of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(189,'4/16/2024, 08:59:28',1,'These are non-covered services because this is not deemed a ''medical necessity'' by the payer. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','50','These are non-covered services because this is not deemed a ''medical necessity'' by the payer. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(190,'4/16/2024, 08:59:28',1,'These are non-covered services because this is a pre-existing condition. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','51','These are non-covered services because this is a pre-existing condition. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(191,'4/16/2024, 08:59:28',1,'Services by an immediate relative or a member of the same household are not covered.','53','Services by an immediate relative or a member of the same household are not covered.'),
(192,'4/16/2024, 08:59:28',1,'Multiple physicians/assistants are not covered in this case. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','54','Multiple physicians/assistants are not covered in this case. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(193,'4/16/2024, 08:59:28',1,'Procedure/treatment is deemed experimental/investigational by the payer. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','55','Procedure/treatment is deemed experimental/investigational by the payer. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(194,'4/16/2024, 08:59:28',1,'Procedure/treatment has not been deemed ''proven to be effective'' by the payer. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','56','Procedure/treatment has not been deemed ''proven to be effective'' by the payer. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(195,'4/16/2024, 08:59:28',1,'Treatment was deemed by the payer to have been rendered in an inappropriate or invalid place of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','58','Treatment was deemed by the payer to have been rendered in an inappropriate or invalid place of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(196,'4/16/2024, 08:59:28',1,'Processed based on multiple or concurrent procedure rules. (For example, multiple surgery or diagnostic imaging, concurrent anesthesia.) Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if p','59','Processed based on multiple or concurrent procedure rules. (For example, multiple surgery or diagnostic imaging, concurrent anesthesia.) Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if p'),
(197,'4/16/2024, 08:59:28',1,'The procedure/revenue code is inconsistent with the patient''s age. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','6','The procedure/revenue code is inconsistent with the patient''s age. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(198,'4/16/2024, 08:59:28',1,'Charges for outpatient services are not covered when performed within a period of time prior to or after inpatient services.','60','Charges for outpatient services are not covered when performed within a period of time prior to or after inpatient services.'),
(199,'4/16/2024, 08:59:28',1,'Penalty for failure to obtain a second surgical opinion. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','61','Penalty for failure to obtain a second surgical opinion. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(200,'4/16/2024, 08:59:28',1,'Blood Deductible.','66','Blood Deductible.'),
(201,'4/16/2024, 08:59:28',1,'Day outlier amount.','69','Day outlier amount.'),
(202,'4/16/2024, 08:59:28',1,'The procedure/revenue code is inconsistent with the patient''s gender. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','7','The procedure/revenue code is inconsistent with the patient''s gender. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(203,'4/16/2024, 08:59:28',1,'Cost outlier - Adjustment to compensate for additional costs.','70','Cost outlier - Adjustment to compensate for additional costs.'),
(204,'4/16/2024, 08:59:28',1,'Indirect Medical Education Adjustment.','74','Indirect Medical Education Adjustment.'),
(205,'4/16/2024, 08:59:28',1,'Direct Medical Education Adjustment.','75','Direct Medical Education Adjustment.'),
(206,'4/16/2024, 08:59:28',1,'Disproportionate Share Adjustment.','76','Disproportionate Share Adjustment.'),
(207,'4/16/2024, 08:59:28',1,'Non-Covered days/Room charge adjustment.','78','Non-Covered days/Room charge adjustment.'),
(208,'4/16/2024, 08:59:28',1,'The procedure code is inconsistent with the provider type/specialty (taxonomy). Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','8','The procedure code is inconsistent with the provider type/specialty (taxonomy). Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(209,'4/16/2024, 08:59:28',1,'Patient Interest Adjustment (Use Only Group code PR)','85','Patient Interest Adjustment (Use Only Group code PR)'),
(210,'4/16/2024, 08:59:28',1,'Professional fees removed from charges.','89','Professional fees removed from charges.'),
(211,'4/16/2024, 08:59:28',1,'The diagnosis is inconsistent with the patient''s age. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.','9','The diagnosis is inconsistent with the patient''s age. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.'),
(212,'4/16/2024, 08:59:28',1,'Ingredient cost adjustment. Note  To be used for pharmaceuticals only.','90','Ingredient cost adjustment. Note  To be used for pharmaceuticals only.'),
(213,'4/16/2024, 08:59:28',1,'Dispensing fee adjustment.','91','Dispensing fee adjustment.'),
(214,'4/16/2024, 08:59:28',1,'Processed in Excess of charges.','94','Processed in Excess of charges.'),
(215,'4/16/2024, 08:59:28',1,'Plan procedures not followed.','95','Plan procedures not followed.'),
(216,'4/16/2024, 08:59:28',1,'Non-covered charge(s). At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2)','96','Non-covered charge(s). At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2)'),
(217,'4/16/2024, 08:59:28',1,'The benefit for this service is included in the payment/allowance for another service/procedure that has already been adjudicated. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present','97','The benefit for this service is included in the payment/allowance for another service/procedure that has already been adjudicated. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present'),
(218,'4/16/2024, 08:59:28',1,'Patient refund amount.','A0','Patient refund amount.'),
(219,'4/16/2024, 08:59:28',1,'Claim/Service denied. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)','A1','Claim/Service denied. At least one Remark Code must be provided (may be either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)'),
(220,'4/16/2024, 08:59:28',1,'Medicare Claim PPS Capital Cost Outlier Amount.','A5','Medicare Claim PPS Capital Cost Outlier Amount.'),
(221,'4/16/2024, 08:59:28',1,'Prior hospitalization or 30 day transfer requirement not met.','A6','Prior hospitalization or 30 day transfer requirement not met.'),
(222,'4/16/2024, 08:59:28',1,'Presumptive Payment Adjustment','A7','Presumptive Payment Adjustment'),
(223,'4/16/2024, 08:59:28',1,'Ungroupable DRG.','A8','Ungroupable DRG.'),
(224,'4/16/2024, 08:59:28',1,'Non-covered visits.','B1','Non-covered visits.'),
(225,'4/16/2024, 08:59:28',1,'Allowed amount has been reduced because a component of the basic procedure/test was paid. The beneficiary is not liable for more than the charge limit for the basic procedure/test.','B10','Allowed amount has been reduced because a component of the basic procedure/test was paid. The beneficiary is not liable for more than the charge limit for the basic procedure/test.'),
(226,'4/16/2024, 08:59:28',1,'The claim/service has been transferred to the proper payer/processor for processing. Claim/service not covered by this payer/processor.','B11','The claim/service has been transferred to the proper payer/processor for processing. Claim/service not covered by this payer/processor.'),
(227,'4/16/2024, 08:59:28',1,'Services not documented in patients'' medical records.','B12','Services not documented in patients'' medical records.'),
(228,'4/16/2024, 08:59:28',1,'Previously paid. Payment for this claim/service may have been provided in a previous payment.','B13','Previously paid. Payment for this claim/service may have been provided in a previous payment.'),
(229,'4/16/2024, 08:59:28',1,'Only one visit or consultation per physician per day is covered.','B14','Only one visit or consultation per physician per day is covered.'),
(230,'4/16/2024, 08:59:28',1,'This service/procedure requires that a qualifying service/procedure be received and covered. The qualifying other service/procedure has not been received/adjudicated. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service','B15','This service/procedure requires that a qualifying service/procedure be received and covered. The qualifying other service/procedure has not been received/adjudicated. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service'),
(231,'4/16/2024, 08:59:28',1,'''New Patient'' qualifications were not met.','B16','''New Patient'' qualifications were not met.'),
(232,'4/16/2024, 08:59:28',1,'Procedure/service was partially or fully furnished by another provider.','B20','Procedure/service was partially or fully furnished by another provider.'),
(233,'4/16/2024, 08:59:28',1,'This payment is adjusted based on the diagnosis.','B22','This payment is adjusted based on the diagnosis.'),
(234,'4/16/2024, 08:59:28',1,'Procedure billed is not authorized per your Clinical Laboratory Improvement Amendment (CLIA) proficiency test.','B23','Procedure billed is not authorized per your Clinical Laboratory Improvement Amendment (CLIA) proficiency test.'),
(235,'4/16/2024, 08:59:28',1,'Late filing penalty.','B4','Late filing penalty.'),
(236,'4/16/2024, 08:59:28',1,'Coverage/program guidelines were not met or were exceeded.','B5','Coverage/program guidelines were not met or were exceeded.'),
(237,'4/16/2024, 08:59:28',1,'This provider was not certified/eligible to be paid for this procedure/service on this date of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','B7','This provider was not certified/eligible to be paid for this procedure/service on this date of service. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(238,'4/16/2024, 08:59:28',1,'Alternative services were available, and should have been utilized. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.','B8','Alternative services were available, and should have been utilized. Note  Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.'),
(239,'4/16/2024, 08:59:28',1,'Patient is enrolled in a Hospice.','B9','Patient is enrolled in a Hospice.'),
(240,'4/16/2024, 08:59:28',1,'X-ray not taken within the past 12 months or near enough to the start of treatment.','M1','X-ray not taken within the past 12 months or near enough to the start of treatment.'),
(241,'4/16/2024, 08:59:28',1,'Equipment purchases are limited to the first or the tenth month of medical necessity.','M10','Equipment purchases are limited to the first or the tenth month of medical necessity.'),
(242,'4/16/2024, 08:59:28',1,'We do not pay for an oral anti-emetic drug that is not administered for use immediately before, at, or within 48 hours of administration of a covered chemotherapy drug.','M100','We do not pay for an oral anti-emetic drug that is not administered for use immediately before, at, or within 48 hours of administration of a covered chemotherapy drug.'),
(243,'4/16/2024, 08:59:28',1,'Service not performed on equipment approved by the FDA for this purpose.','M102','Service not performed on equipment approved by the FDA for this purpose.'),
(244,'4/16/2024, 08:59:28',1,'Information supplied supports a break in therapy.  However, the medical information we have for this patient does not support the need for this item as billed.  We have approved payment for this item at a reduced level, and a new capped rental period','M103','Information supplied supports a break in therapy.  However, the medical information we have for this patient does not support the need for this item as billed.  We have approved payment for this item at a reduced level, and a new capped rental period'),
(245,'4/16/2024, 08:59:28',1,'Information supplied supports a break in therapy.  A new capped rental period will begin with delivery of the equipment.  This is the maximum approved under the fee schedule for this item or service.','M104','Information supplied supports a break in therapy.  A new capped rental period will begin with delivery of the equipment.  This is the maximum approved under the fee schedule for this item or service.'),
(246,'4/16/2024, 08:59:28',1,'Information supplied does not support a break in therapy.  The medical information we have for this patient does not support the need for this item as billed. We have approved payment for this item at a reduced level, and a new capped rental period w','M105','Information supplied does not support a break in therapy.  The medical information we have for this patient does not support the need for this item as billed. We have approved payment for this item at a reduced level, and a new capped rental period w'),
(247,'4/16/2024, 08:59:28',1,'Payment reduced as 90-day rolling average hematocrit for ESRD patient exceeded 36.5%.','M107','Payment reduced as 90-day rolling average hematocrit for ESRD patient exceeded 36.5%.'),
(248,'4/16/2024, 08:59:28',1,'We have provided you with a bundled payment for a teleconsultation. You must send 25','M109','We have provided you with a bundled payment for a teleconsultation. You must send 25'),
(249,'4/16/2024, 08:59:28',1,'DME, orthotics and prosthetics must be billed to the DME carrier who services the patient''s zip code.','M11','DME, orthotics and prosthetics must be billed to the DME carrier who services the patient''s zip code.'),
(250,'4/16/2024, 08:59:28',1,'We do not pay for chiropractic manipulative treatment when the patient refuses to have an x-ray taken.','M111','We do not pay for chiropractic manipulative treatment when the patient refuses to have an x-ray taken.'),
(251,'4/16/2024, 08:59:28',1,'Reimbursement for this item is based on the single payment amount required under the DMEPOS Competitive Bidding Program for the area where the patient resides.','M112','Reimbursement for this item is based on the single payment amount required under the DMEPOS Competitive Bidding Program for the area where the patient resides.'),
(252,'4/16/2024, 08:59:28',1,'Our records indicate that this patient began using this item/service prior to the current contract period for the DMEPOS Competitive Bidding Program.','M113','Our records indicate that this patient began using this item/service prior to the current contract period for the DMEPOS Competitive Bidding Program.'),
(253,'4/16/2024, 08:59:28',1,'This service was processed in accordance with rules and guidelines under the DMEPOS Competitive Bidding Program or a Demonstration Project.  For more information regarding these projects, contact your local contractor.','M114','This service was processed in accordance with rules and guidelines under the DMEPOS Competitive Bidding Program or a Demonstration Project.  For more information regarding these projects, contact your local contractor.'),
(254,'4/16/2024, 08:59:28',1,'This item is denied when provided to this patient by a non-contract or non-demonstration supplier.','M115','This item is denied when provided to this patient by a non-contract or non-demonstration supplier.'),
(255,'4/16/2024, 08:59:28',1,'Processed under a demonstration project or program. Project or program is ending and additional services may not be paid under this project or program.','M116','Processed under a demonstration project or program. Project or program is ending and additional services may not be paid under this project or program.'),
(256,'4/16/2024, 08:59:28',1,'Not covered unless submitted via electronic claim.','M117','Not covered unless submitted via electronic claim.'),
(257,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid/ deactivated/withdrawn National Drug Code (NDC).','M119','Missing/incomplete/invalid/ deactivated/withdrawn National Drug Code (NDC).'),
(258,'4/16/2024, 08:59:28',1,'Diagnostic tests performed by a physician must indicate whether purchased services are included on the claim.','M12','Diagnostic tests performed by a physician must indicate whether purchased services are included on the claim.'),
(259,'4/16/2024, 08:59:28',1,'We pay for this service only when performed with a covered cryosurgical ablation.','M121','We pay for this service only when performed with a covered cryosurgical ablation.'),
(260,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid level of subluxation.','M122','Missing/incomplete/invalid level of subluxation.'),
(261,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid name, strength, or dosage of the drug furnished.','M123','Missing/incomplete/invalid name, strength, or dosage of the drug furnished.'),
(262,'4/16/2024, 08:59:28',1,'Missing indication of whether the patient owns the equipment that requires the part or supply.','M124','Missing indication of whether the patient owns the equipment that requires the part or supply.'),
(263,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid information on the period of time for which the service/supply/equipment will be needed.','M125','Missing/incomplete/invalid information on the period of time for which the service/supply/equipment will be needed.'),
(264,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid individual lab codes included in the test.','M126','Missing/incomplete/invalid individual lab codes included in the test.'),
(265,'4/16/2024, 08:59:28',1,'Missing patient medical record for this service.','M127','Missing patient medical record for this service.'),
(266,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid indicator of x-ray availability for review.','M129','Missing/incomplete/invalid indicator of x-ray availability for review.'),
(267,'4/16/2024, 08:59:28',1,'Only one initial visit is covered per specialty per medical group.','M13','Only one initial visit is covered per specialty per medical group.'),
(268,'4/16/2024, 08:59:28',1,'Missing invoice or statement certifying the actual cost of the lens, less discounts, and/or the type of intraocular lens used.','M130','Missing invoice or statement certifying the actual cost of the lens, less discounts, and/or the type of intraocular lens used.'),
(269,'4/16/2024, 08:59:28',1,'Missing physician financial relationship form.','M131','Missing physician financial relationship form.'),
(270,'4/16/2024, 08:59:28',1,'Missing pacemaker registration form.','M132','Missing pacemaker registration form.'),
(271,'4/16/2024, 08:59:28',1,'Claim did not identify who performed the purchased diagnostic test or the amount you were charged for the test.','M133','Claim did not identify who performed the purchased diagnostic test or the amount you were charged for the test.'),
(272,'4/16/2024, 08:59:28',1,'Performed by a facility/supplier in which the provider has a financial interest.','M134','Performed by a facility/supplier in which the provider has a financial interest.'),
(273,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid plan of treatment.','M135','Missing/incomplete/invalid plan of treatment.'),
(274,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid indication that the service was supervised or evaluated by a physician.','M136','Missing/incomplete/invalid indication that the service was supervised or evaluated by a physician.'),
(275,'4/16/2024, 08:59:28',1,'Part B coinsurance under a demonstration project.','M137','Part B coinsurance under a demonstration project.'),
(276,'4/16/2024, 08:59:28',1,'Patient identified as a demonstration participant but the patient was not enrolled in the demonstration at the time services were rendered.  Coverage is limited to demonstration participants.','M138','Patient identified as a demonstration participant but the patient was not enrolled in the demonstration at the time services were rendered.  Coverage is limited to demonstration participants.'),
(277,'4/16/2024, 08:59:28',1,'Denied services exceed the coverage limit for the demonstration.','M139','Denied services exceed the coverage limit for the demonstration.'),
(278,'4/16/2024, 08:59:28',1,'No separate payment for an injection administered during an office visit, and no payment for a full office visit if the patient only received an injection.','M14','No separate payment for an injection administered during an office visit, and no payment for a full office visit if the patient only received an injection.'),
(279,'4/16/2024, 08:59:28',1,'Missing physician certified plan of care.','M141','Missing physician certified plan of care.'),
(280,'4/16/2024, 08:59:28',1,'Missing American Diabetes Association Certificate of Recognition.','M142','Missing American Diabetes Association Certificate of Recognition.'),
(281,'4/16/2024, 08:59:28',1,'The provider must update license information with the payer.','M143','The provider must update license information with the payer.'),
(282,'4/16/2024, 08:59:28',1,'Pre-/post-operative care payment is included in the allowance for the surgery/procedure.','M144','Pre-/post-operative care payment is included in the allowance for the surgery/procedure.'),
(283,'4/16/2024, 08:59:28',1,'Separately billed services/tests have been bundled as they are considered components of the same procedure. Separate payment is not allowed.','M15','Separately billed services/tests have been bundled as they are considered components of the same procedure. Separate payment is not allowed.'),
(284,'4/16/2024, 08:59:28',1,'Alert Please see our web site, mailings, or bulletins for more details concerning this policy/procedure/decision.','M16','Alert Please see our web site, mailings, or bulletins for more details concerning this policy/procedure/decision.'),
(285,'4/16/2024, 08:59:28',1,'Alert Payment approved as you did not know, and could not reasonably have been expected to know, that this would not normally have been covered for this patient.  In the future, you will be liable for charges for the same service(s) under the same o','M17','Alert Payment approved as you did not know, and could not reasonably have been expected to know, that this would not normally have been covered for this patient.  In the future, you will be liable for charges for the same service(s) under the same o'),
(286,'4/16/2024, 08:59:28',1,'Certain services may be approved for home use.  Neither a hospital nor a Skilled Nursing Facility (SNF) is considered to be a patient''s home.','M18','Certain services may be approved for home use.  Neither a hospital nor a Skilled Nursing Facility (SNF) is considered to be a patient''s home.'),
(287,'4/16/2024, 08:59:28',1,'Missing oxygen certification/re-certification.','M19','Missing oxygen certification/re-certification.'),
(288,'4/16/2024, 08:59:28',1,'Not paid separately when the patient is an inpatient.','M2','Not paid separately when the patient is an inpatient.'),
(289,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid HCPCS.','M20','Missing/incomplete/invalid HCPCS.'),
(290,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid place of residence for this service/item provided in a home.','M21','Missing/incomplete/invalid place of residence for this service/item provided in a home.'),
(291,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid number of miles traveled.','M22','Missing/incomplete/invalid number of miles traveled.'),
(292,'4/16/2024, 08:59:28',1,'Missing invoice.','M23','Missing invoice.'),
(293,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid number of doses per vial.','M24','Missing/incomplete/invalid number of doses per vial.'),
(294,'4/16/2024, 08:59:28',1,'The information furnished does not substantiate the need for this level of service. If you believe the service should have been fully covered as billed, or if you did not know and could not reasonably have been expected to know that we would not pay','M25','The information furnished does not substantiate the need for this level of service. If you believe the service should have been fully covered as billed, or if you did not know and could not reasonably have been expected to know that we would not pay'),
(295,'4/16/2024, 08:59:28',1,'The information furnished does not substantiate the need for this level of service. If you have collected any amount from the patient for this level of service /any amount that exceeds the limiting charge for the less extensive service, the law requi','M26','The information furnished does not substantiate the need for this level of service. If you have collected any amount from the patient for this level of service /any amount that exceeds the limiting charge for the less extensive service, the law requi'),
(296,'4/16/2024, 08:59:28',1,'Alert The patient has been relieved of liability of payment of these items and services under the limitation of liability provision of the law. The provider is ultimately liable for the patient''s waived charges, including any charges for coinsuranc','M27','Alert The patient has been relieved of liability of payment of these items and services under the limitation of liability provision of the law. The provider is ultimately liable for the patient''s waived charges, including any charges for coinsuranc'),
(297,'4/16/2024, 08:59:28',1,'This does not qualify for payment under Part B when Part A coverage is exhausted or not otherwise available.','M28','This does not qualify for payment under Part B when Part A coverage is exhausted or not otherwise available.'),
(298,'4/16/2024, 08:59:28',1,'Missing operative note/report.','M29','Missing operative note/report.'),
(299,'4/16/2024, 08:59:28',1,'Equipment is the same or similar to equipment already being used.','M3','Equipment is the same or similar to equipment already being used.'),
(300,'4/16/2024, 08:59:28',1,'Missing pathology report.','M30','Missing pathology report.'),
(301,'4/16/2024, 08:59:28',1,'Missing radiology report.','M31','Missing radiology report.'),
(302,'4/16/2024, 08:59:28',1,'Alert This is a conditional payment made pending a decision on this service by the patient''s primary payer. This payment may be subject to refund upon your receipt of any additional payment for this service from another payer. You must contact this','M32','Alert This is a conditional payment made pending a decision on this service by the patient''s primary payer. This payment may be subject to refund upon your receipt of any additional payment for this service from another payer. You must contact this'),
(303,'4/16/2024, 08:59:28',1,'This is the 11th rental month.  We cannot pay for this until you indicate that the patient has been given the option of changing the rental to a purchase.','M36','This is the 11th rental month.  We cannot pay for this until you indicate that the patient has been given the option of changing the rental to a purchase.'),
(304,'4/16/2024, 08:59:28',1,'Not covered when the patient is under age 35.','M37','Not covered when the patient is under age 35.'),
(305,'4/16/2024, 08:59:28',1,'The patient is liable for the charges for this service as you informed the patient in writing before the service was furnished that we would not pay for it, and the patient agreed to pay.','M38','The patient is liable for the charges for this service as you informed the patient in writing before the service was furnished that we would not pay for it, and the patient agreed to pay.'),
(306,'4/16/2024, 08:59:28',1,'The patient is not liable for payment for this service as the advance notice of non-coverage you provided the patient did not comply with program requirements.','M39','The patient is not liable for payment for this service as the advance notice of non-coverage you provided the patient did not comply with program requirements.'),
(307,'4/16/2024, 08:59:28',1,'Alert This is the last monthly installment payment for this durable medical equipment.','M4','Alert This is the last monthly installment payment for this durable medical equipment.'),
(308,'4/16/2024, 08:59:28',1,'Claim must be assigned and must be filed by the practitioner''s employer.','M40','Claim must be assigned and must be filed by the practitioner''s employer.'),
(309,'4/16/2024, 08:59:28',1,'We do not pay for this as the patient has no legal obligation to pay for this.','M41','We do not pay for this as the patient has no legal obligation to pay for this.'),
(310,'4/16/2024, 08:59:28',1,'The medical necessity form must be personally signed by the attending physician.','M42','The medical necessity form must be personally signed by the attending physician.'),
(311,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid condition code.','M44','Missing/incomplete/invalid condition code.'),
(312,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid occurrence code(s).','M45','Missing/incomplete/invalid occurrence code(s).'),
(313,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid occurrence span code(s).','M46','Missing/incomplete/invalid occurrence span code(s).'),
(314,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid internal or document control number.','M47','Missing/incomplete/invalid internal or document control number.'),
(315,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid value code(s) or amount(s).','M49','Missing/incomplete/invalid value code(s) or amount(s).'),
(316,'4/16/2024, 08:59:28',1,'Monthly rental payments can continue until the earlier of the 15th month from the first rental month, or the month when the equipment is no longer needed.','M5','Monthly rental payments can continue until the earlier of the 15th month from the first rental month, or the month when the equipment is no longer needed.'),
(317,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid revenue code(s).','M50','Missing/incomplete/invalid revenue code(s).'),
(318,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid procedure code(s).','M51','Missing/incomplete/invalid procedure code(s).'),
(319,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid “from” date(s) of service.','M52','Missing/incomplete/invalid “from” date(s) of service.'),
(320,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid days or units of service.','M53','Missing/incomplete/invalid days or units of service.'),
(321,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid total charges.','M54','Missing/incomplete/invalid total charges.'),
(322,'4/16/2024, 08:59:28',1,'We do not pay for self-administered anti-emetic drugs that are not administered with a covered oral anti-cancer drug.','M55','We do not pay for self-administered anti-emetic drugs that are not administered with a covered oral anti-cancer drug.'),
(323,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid payer identifier.','M56','Missing/incomplete/invalid payer identifier.'),
(324,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid “to” date(s) of service.','M59','Missing/incomplete/invalid “to” date(s) of service.'),
(325,'4/16/2024, 08:59:28',1,'Alert You must furnish and service this item for any period of medical need for the remainder of the reasonable useful lifetime of the equipment.','M6','Alert You must furnish and service this item for any period of medical need for the remainder of the reasonable useful lifetime of the equipment.'),
(326,'4/16/2024, 08:59:28',1,'Missing Certificate of Medical Necessity.','M60','Missing Certificate of Medical Necessity.'),
(327,'4/16/2024, 08:59:28',1,'We cannot pay for this as the approval period for the FDA clinical trial has expired.','M61','We cannot pay for this as the approval period for the FDA clinical trial has expired.'),
(328,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid treatment authorization code.','M62','Missing/incomplete/invalid treatment authorization code.'),
(329,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other diagnosis.','M64','Missing/incomplete/invalid other diagnosis.'),
(330,'4/16/2024, 08:59:28',1,'One interpreting physician charge can be submitted per claim when a purchased diagnostic test is indicated. Please submit a separate claim for each interpreting physician.','M65','One interpreting physician charge can be submitted per claim when a purchased diagnostic test is indicated. Please submit a separate claim for each interpreting physician.'),
(331,'4/16/2024, 08:59:28',1,'Our records indicate that you billed diagnostic tests subject to price limitations and the procedure code submitted includes a professional component. Only the technical component is subject to price limitations.  Please submit the technical and prof','M66','Our records indicate that you billed diagnostic tests subject to price limitations and the procedure code submitted includes a professional component. Only the technical component is subject to price limitations.  Please submit the technical and prof'),
(332,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other procedure code(s).','M67','Missing/incomplete/invalid other procedure code(s).'),
(333,'4/16/2024, 08:59:28',1,'Paid at the regular rate as you did not submit documentation to justify the modified procedure code.','M69','Paid at the regular rate as you did not submit documentation to justify the modified procedure code.'),
(334,'4/16/2024, 08:59:28',1,'No rental payments after the item is purchased, or after the total of issued rental payments equals the purchase price.','M7','No rental payments after the item is purchased, or after the total of issued rental payments equals the purchase price.'),
(335,'4/16/2024, 08:59:28',1,'Alert The NDC code submitted for this service was translated to a HCPCS code for processing, but please continue to submit the NDC on future claims for this item.','M70','Alert The NDC code submitted for this service was translated to a HCPCS code for processing, but please continue to submit the NDC on future claims for this item.'),
(336,'4/16/2024, 08:59:28',1,'Total payment reduced due to overlap of tests billed.','M71','Total payment reduced due to overlap of tests billed.'),
(337,'4/16/2024, 08:59:28',1,'The HPSA/Physician Scarcity bonus can only be paid on the professional component of this service. Rebill as separate professional and technical components.','M73','The HPSA/Physician Scarcity bonus can only be paid on the professional component of this service. Rebill as separate professional and technical components.'),
(338,'4/16/2024, 08:59:28',1,'This service does not qualify for a HPSA/Physician Scarcity bonus payment.','M74','This service does not qualify for a HPSA/Physician Scarcity bonus payment.'),
(339,'4/16/2024, 08:59:28',1,'Multiple automated multichannel tests performed on the same day combined for payment.','M75','Multiple automated multichannel tests performed on the same day combined for payment.'),
(340,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid diagnosis or condition.','M76','Missing/incomplete/invalid diagnosis or condition.'),
(341,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid place of service.','M77','Missing/incomplete/invalid place of service.'),
(342,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid charge.','M79','Missing/incomplete/invalid charge.'),
(343,'4/16/2024, 08:59:28',1,'We do not accept blood gas test results when the test was conducted by a medical supplier or taken while the patient is on oxygen.','M8','We do not accept blood gas test results when the test was conducted by a medical supplier or taken while the patient is on oxygen.'),
(344,'4/16/2024, 08:59:28',1,'Not covered when performed during the same session/date as a previously processed service for the patient.','M80','Not covered when performed during the same session/date as a previously processed service for the patient.'),
(345,'4/16/2024, 08:59:28',1,'You are required to code to the highest level of specificity.','M81','You are required to code to the highest level of specificity.'),
(346,'4/16/2024, 08:59:28',1,'Service is not covered when a patient is under age 50.','M82','Service is not covered when a patient is under age 50.'),
(347,'4/16/2024, 08:59:28',1,'Service is not covered unless the patient is classified as at high risk.','M83','Service is not covered unless the patient is classified as at high risk.'),
(348,'4/16/2024, 08:59:28',1,'Medical code sets used must be the codes in effect at the time of service','M84','Medical code sets used must be the codes in effect at the time of service'),
(349,'4/16/2024, 08:59:28',1,'Subjected to review of physician evaluation and management services.','M85','Subjected to review of physician evaluation and management services.'),
(350,'4/16/2024, 08:59:28',1,'Service denied because payment already made for the same/similar procedure within a set time frame.','M86','Service denied because payment already made for the same/similar procedure within a set time frame.'),
(351,'4/16/2024, 08:59:28',1,'Claim/service(s) subjected to CFO-CAP prepayment review.','M87','Claim/service(s) subjected to CFO-CAP prepayment review.'),
(352,'4/16/2024, 08:59:28',1,'Not covered more than once under age 40.','M89','Not covered more than once under age 40.'),
(353,'4/16/2024, 08:59:28',1,'Alert This is the tenth rental month. You must offer the patient the choice of changing the rental to a purchase agreement.','M9','Alert This is the tenth rental month. You must offer the patient the choice of changing the rental to a purchase agreement.'),
(354,'4/16/2024, 08:59:28',1,'Not covered more than once in a 12 month period.','M90','Not covered more than once in a 12 month period.'),
(355,'4/16/2024, 08:59:28',1,'Lab procedures with different CLIA certification numbers must be billed on separate claims.','M91','Lab procedures with different CLIA certification numbers must be billed on separate claims.'),
(356,'4/16/2024, 08:59:28',1,'Information supplied supports a break in therapy.  A new capped rental period began with delivery of this equipment.','M93','Information supplied supports a break in therapy.  A new capped rental period began with delivery of this equipment.'),
(357,'4/16/2024, 08:59:28',1,'Information supplied does not support a break in therapy.  A new capped rental period will not begin.','M94','Information supplied does not support a break in therapy.  A new capped rental period will not begin.'),
(358,'4/16/2024, 08:59:28',1,'Services subjected to Home Health Initiative medical review/cost report audit.','M95','Services subjected to Home Health Initiative medical review/cost report audit.'),
(359,'4/16/2024, 08:59:28',1,'The technical component of a service furnished to an inpatient may only be billed by that inpatient facility. You must contact the inpatient facility for technical component reimbursement.  If not already billed, you should bill us for the profession','M96','The technical component of a service furnished to an inpatient may only be billed by that inpatient facility. You must contact the inpatient facility for technical component reimbursement.  If not already billed, you should bill us for the profession'),
(360,'4/16/2024, 08:59:28',1,'Not paid to the practitioner when provided to the patient in this place of service.  Payment included in the reimbursement issued to the facility.','M97','Not paid to the practitioner when provided to the patient in this place of service.  Payment included in the reimbursement issued to the facility.'),
(361,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Universal Product Number/Serial Number.','M99','Missing/incomplete/invalid Universal Product Number/Serial Number.'),
(362,'4/16/2024, 08:59:28',1,'Alert If you do not agree with what we approved for these services, you may appeal our decision.  To make sure that we are fair to you, we require another individual that did not process your initial claim to conduct the appeal.  However, in order t','MA01','Alert If you do not agree with what we approved for these services, you may appeal our decision.  To make sure that we are fair to you, we require another individual that did not process your initial claim to conduct the appeal.  However, in order t'),
(363,'4/16/2024, 08:59:28',1,'Alert If you do not agree with this determination, you have the right to appeal. You must file a written request for an appeal within 180 days of the date you receive this notice.','MA02','Alert If you do not agree with this determination, you have the right to appeal. You must file a written request for an appeal within 180 days of the date you receive this notice.'),
(364,'4/16/2024, 08:59:28',1,'Secondary payment cannot be considered without the identity of or payment information from the primary payer.  The information was either not reported or was illegible.','MA04','Secondary payment cannot be considered without the identity of or payment information from the primary payer.  The information was either not reported or was illegible.'),
(365,'4/16/2024, 08:59:28',1,'Alert The claim information has also been forwarded to Medicaid for review.','MA07','Alert The claim information has also been forwarded to Medicaid for review.'),
(366,'4/16/2024, 08:59:28',1,'Alert Claim information was not forwarded because the supplemental coverage is not with a Medigap plan, or you do not participate in Medicare.','MA08','Alert Claim information was not forwarded because the supplemental coverage is not with a Medigap plan, or you do not participate in Medicare.'),
(367,'4/16/2024, 08:59:28',1,'Claim submitted as unassigned but processed as assigned in accordance with our current assignment/participation agreement.','MA09','Claim submitted as unassigned but processed as assigned in accordance with our current assignment/participation agreement.'),
(368,'4/16/2024, 08:59:28',1,'Alert The patient''s payment was in excess of the amount owed.  You must refund the overpayment to the patient.','MA10','Alert The patient''s payment was in excess of the amount owed.  You must refund the overpayment to the patient.'),
(369,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid date of current illness or symptoms','MA100','Missing/incomplete/invalid date of current illness or symptoms'),
(370,'4/16/2024, 08:59:28',1,'Hemophilia Add On.','MA103','Hemophilia Add On.'),
(371,'4/16/2024, 08:59:28',1,'PIP (Periodic Interim Payment) claim.','MA106','PIP (Periodic Interim Payment) claim.'),
(372,'4/16/2024, 08:59:28',1,'Paper claim contains more than three separate data items in field 19.','MA107','Paper claim contains more than three separate data items in field 19.'),
(373,'4/16/2024, 08:59:28',1,'Paper claim contains more than one data item in field 23.','MA108','Paper claim contains more than one data item in field 23.'),
(374,'4/16/2024, 08:59:28',1,'Claim processed in accordance with ambulatory surgical guidelines.','MA109','Claim processed in accordance with ambulatory surgical guidelines.'),
(375,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid information on whether the diagnostic test(s) were performed by an outside entity or if no purchased tests are included on the claim.','MA110','Missing/incomplete/invalid information on whether the diagnostic test(s) were performed by an outside entity or if no purchased tests are included on the claim.'),
(376,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid purchase price of the test(s) and/or the performing laboratory''s name and address.','MA111','Missing/incomplete/invalid purchase price of the test(s) and/or the performing laboratory''s name and address.'),
(377,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid group practice information.','MA112','Missing/incomplete/invalid group practice information.'),
(378,'4/16/2024, 08:59:28',1,'Incomplete/invalid taxpayer identification number (TIN) submitted by you per the Internal Revenue Service. Your claims cannot be processed without your correct TIN, and you may not bill the patient pending correction of your TIN.  There are no appeal','MA113','Incomplete/invalid taxpayer identification number (TIN) submitted by you per the Internal Revenue Service. Your claims cannot be processed without your correct TIN, and you may not bill the patient pending correction of your TIN.  There are no appeal'),
(379,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid information on where the services were furnished.','MA114','Missing/incomplete/invalid information on where the services were furnished.'),
(380,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid physical location (name and address, or PIN) where the service(s) were rendered in a Health Professional Shortage Area (HPSA).','MA115','Missing/incomplete/invalid physical location (name and address, or PIN) where the service(s) were rendered in a Health Professional Shortage Area (HPSA).'),
(381,'4/16/2024, 08:59:28',1,'Did not complete the statement ''Homebound'' on the claim to validate whether laboratory services were performed at home or in an institution.','MA116','Did not complete the statement ''Homebound'' on the claim to validate whether laboratory services were performed at home or in an institution.'),
(382,'4/16/2024, 08:59:28',1,'This claim has been assessed a $1.00 user fee.','MA117','This claim has been assessed a $1.00 user fee.'),
(383,'4/16/2024, 08:59:28',1,'Alert No Medicare payment issued for this claim for services or supplies furnished to a Medicare-eligible veteran through a facility of the Department of Veterans Affairs. Coinsurance and/or deductible are applicable.','MA118','Alert No Medicare payment issued for this claim for services or supplies furnished to a Medicare-eligible veteran through a facility of the Department of Veterans Affairs. Coinsurance and/or deductible are applicable.'),
(384,'4/16/2024, 08:59:28',1,'You have not established that you have the right under the law to bill for services furnished by the person(s) that furnished this (these) service(s).','MA12','You have not established that you have the right under the law to bill for services furnished by the person(s) that furnished this (these) service(s).'),
(385,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid CLIA certification number.','MA120','Missing/incomplete/invalid CLIA certification number.'),
(386,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid x-ray date.','MA121','Missing/incomplete/invalid x-ray date.'),
(387,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid initial treatment date.','MA122','Missing/incomplete/invalid initial treatment date.'),
(388,'4/16/2024, 08:59:28',1,'Your center was not selected to participate in this study, therefore, we cannot pay for these services.','MA123','Your center was not selected to participate in this study, therefore, we cannot pay for these services.'),
(389,'4/16/2024, 08:59:28',1,'Per legislation governing this program, payment constitutes payment in full.','MA125','Per legislation governing this program, payment constitutes payment in full.'),
(390,'4/16/2024, 08:59:28',1,'Pancreas transplant not covered unless kidney transplant performed.','MA126','Pancreas transplant not covered unless kidney transplant performed.'),
(391,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid FDA approval number.','MA128','Missing/incomplete/invalid FDA approval number.'),
(392,'4/16/2024, 08:59:28',1,'Alert You may be subject to penalties if you bill the patient for amounts not reported with the PR (patient responsibility) group code.','MA13','Alert You may be subject to penalties if you bill the patient for amounts not reported with the PR (patient responsibility) group code.'),
(393,'4/16/2024, 08:59:28',1,'Your claim contains incomplete and/or invalid information, and no appeal rights are afforded because the claim is unprocessable.  Please submit a new claim with the complete/correct information.','MA130','Your claim contains incomplete and/or invalid information, and no appeal rights are afforded because the claim is unprocessable.  Please submit a new claim with the complete/correct information.'),
(394,'4/16/2024, 08:59:28',1,'Physician already paid for services in conjunction with this demonstration claim.  You must have the physician withdraw that claim and refund the payment before we can process your claim.','MA131','Physician already paid for services in conjunction with this demonstration claim.  You must have the physician withdraw that claim and refund the payment before we can process your claim.'),
(395,'4/16/2024, 08:59:28',1,'Adjustment to the pre-demonstration rate.','MA132','Adjustment to the pre-demonstration rate.'),
(396,'4/16/2024, 08:59:28',1,'Claim overlaps inpatient stay. Rebill only those services rendered outside the inpatient stay.','MA133','Claim overlaps inpatient stay. Rebill only those services rendered outside the inpatient stay.'),
(397,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid provider number of the facility where the patient resides.','MA134','Missing/incomplete/invalid provider number of the facility where the patient resides.'),
(398,'4/16/2024, 08:59:28',1,'Alert The patient is a member of an employer-sponsored prepaid health plan. Services from outside that health plan are not covered.  However, as you were not previously notified of this, we are paying this time.  In the future, we will not pay you f','MA14','Alert The patient is a member of an employer-sponsored prepaid health plan. Services from outside that health plan are not covered.  However, as you were not previously notified of this, we are paying this time.  In the future, we will not pay you f'),
(399,'4/16/2024, 08:59:28',1,'Alert Your claim has been separated to expedite handling. You will receive a separate notice for the other services reported.','MA15','Alert Your claim has been separated to expedite handling. You will receive a separate notice for the other services reported.'),
(400,'4/16/2024, 08:59:28',1,'The patient is covered by the Black Lung Program.  Send this claim to the Department of Labor, Federal Black Lung Program, P.O. Box 828, Lanham-Seabrook MD 20703.','MA16','The patient is covered by the Black Lung Program.  Send this claim to the Department of Labor, Federal Black Lung Program, P.O. Box 828, Lanham-Seabrook MD 20703.'),
(401,'4/16/2024, 08:59:28',1,'We are the primary payer and have paid at the primary rate.  You must contact the patient''s other insurer to refund any excess it may have paid due to its erroneous primary payment.','MA17','We are the primary payer and have paid at the primary rate.  You must contact the patient''s other insurer to refund any excess it may have paid due to its erroneous primary payment.'),
(402,'4/16/2024, 08:59:28',1,'Alert The claim information is also being forwarded to the patient''s supplemental insurer. Send any questions regarding supplemental benefits to them.','MA18','Alert The claim information is also being forwarded to the patient''s supplemental insurer. Send any questions regarding supplemental benefits to them.'),
(403,'4/16/2024, 08:59:28',1,'Alert  Information was not sent to the Medigap insurer due to incorrect/invalid information you submitted concerning that insurer. Please verify your information and submit your secondary claim directly to that insurer.','MA19','Alert  Information was not sent to the Medigap insurer due to incorrect/invalid information you submitted concerning that insurer. Please verify your information and submit your secondary claim directly to that insurer.'),
(404,'4/16/2024, 08:59:28',1,'Skilled Nursing Facility (SNF) stay not covered when care is primarily related to the use of an urethral catheter for convenience or the control of incontinence.','MA20','Skilled Nursing Facility (SNF) stay not covered when care is primarily related to the use of an urethral catheter for convenience or the control of incontinence.'),
(405,'4/16/2024, 08:59:28',1,'SSA records indicate mismatch with name and sex.','MA21','SSA records indicate mismatch with name and sex.'),
(406,'4/16/2024, 08:59:28',1,'Payment of less than $1.00 suppressed.','MA22','Payment of less than $1.00 suppressed.'),
(407,'4/16/2024, 08:59:28',1,'Demand bill approved as result of medical review.','MA23','Demand bill approved as result of medical review.'),
(408,'4/16/2024, 08:59:28',1,'Christian Science Sanitarium/ Skilled Nursing Facility (SNF) bill in the same benefit period.','MA24','Christian Science Sanitarium/ Skilled Nursing Facility (SNF) bill in the same benefit period.'),
(409,'4/16/2024, 08:59:28',1,'A patient may not elect to change a hospice provider more than once in a benefit period.','MA25','A patient may not elect to change a hospice provider more than once in a benefit period.'),
(410,'4/16/2024, 08:59:28',1,'Alert Our records indicate that you were previously informed of this rule.','MA26','Alert Our records indicate that you were previously informed of this rule.'),
(411,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid entitlement number or name shown on the claim.','MA27','Missing/incomplete/invalid entitlement number or name shown on the claim.'),
(412,'4/16/2024, 08:59:28',1,'Alert Receipt of this notice by a physician or supplier who did not accept assignment is for information only and does not make the physician or supplier a party to the determination.  No additional rights to appeal this decision, above those rights','MA28','Alert Receipt of this notice by a physician or supplier who did not accept assignment is for information only and does not make the physician or supplier a party to the determination.  No additional rights to appeal this decision, above those rights'),
(413,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid type of bill.','MA30','Missing/incomplete/invalid type of bill.'),
(414,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid beginning and ending dates of the period billed.','MA31','Missing/incomplete/invalid beginning and ending dates of the period billed.'),
(415,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid number of covered days during the billing period.','MA32','Missing/incomplete/invalid number of covered days during the billing period.'),
(416,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid noncovered days during the billing period.','MA33','Missing/incomplete/invalid noncovered days during the billing period.'),
(417,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid number of coinsurance days during the billing period.','MA34','Missing/incomplete/invalid number of coinsurance days during the billing period.'),
(418,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid number of lifetime reserve days.','MA35','Missing/incomplete/invalid number of lifetime reserve days.'),
(419,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient name.','MA36','Missing/incomplete/invalid patient name.'),
(420,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient''s address.','MA37','Missing/incomplete/invalid patient''s address.'),
(421,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid gender.','MA39','Missing/incomplete/invalid gender.'),
(422,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid admission date.','MA40','Missing/incomplete/invalid admission date.'),
(423,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid admission type.','MA41','Missing/incomplete/invalid admission type.'),
(424,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid admission source.','MA42','Missing/incomplete/invalid admission source.'),
(425,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient status.','MA43','Missing/incomplete/invalid patient status.'),
(426,'4/16/2024, 08:59:28',1,'Alert No appeal rights. Adjudicative decision based on law.','MA44','Alert No appeal rights. Adjudicative decision based on law.'),
(427,'4/16/2024, 08:59:28',1,'Alert As previously advised, a portion or all of your payment is being held in a special account.','MA45','Alert As previously advised, a portion or all of your payment is being held in a special account.'),
(428,'4/16/2024, 08:59:28',1,'The new information was considered but additional payment will not be issued.','MA46','The new information was considered but additional payment will not be issued.'),
(429,'4/16/2024, 08:59:28',1,'Our records show you have opted out of Medicare, agreeing with the patient not to bill Medicare for services/tests/supplies furnished.  As a result, we cannot pay this claim. The patient is responsible for payment.','MA47','Our records show you have opted out of Medicare, agreeing with the patient not to bill Medicare for services/tests/supplies furnished.  As a result, we cannot pay this claim. The patient is responsible for payment.'),
(430,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid name or address of responsible party or primary payer.','MA48','Missing/incomplete/invalid name or address of responsible party or primary payer.'),
(431,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Investigational Device Exemption number for FDA-approved clinical trial services.','MA50','Missing/incomplete/invalid Investigational Device Exemption number for FDA-approved clinical trial services.'),
(432,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Competitive Bidding Demonstration Project identification.','MA53','Missing/incomplete/invalid Competitive Bidding Demonstration Project identification.'),
(433,'4/16/2024, 08:59:28',1,'Physician certification or election consent for hospice care not received timely.','MA54','Physician certification or election consent for hospice care not received timely.'),
(434,'4/16/2024, 08:59:28',1,'Not covered as patient received medical health care services, automatically revoking his/her election to receive religious nonmedical health care services.','MA55','Not covered as patient received medical health care services, automatically revoking his/her election to receive religious nonmedical health care services.'),
(435,'4/16/2024, 08:59:28',1,'Our records show you have opted out of Medicare, agreeing with the patient not to bill Medicare for services/tests/supplies furnished.  As a result, we cannot pay this claim. The patient is responsible for payment, but under Federal law, you cannot cha','MA56','Our records show you have opted out of Medicare, agreeing with the patient not to bill Medicare for services/tests/supplies furnished.  As a result, we cannot pay this claim. The patient is responsible for payment, but under Federal law, you cannot cha'),
(436,'4/16/2024, 08:59:28',1,'Patient submitted a written request to revoke his/her election for religious nonmedical health care services.','MA57','Patient submitted a written request to revoke his/her election for religious nonmedical health care services.'),
(437,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid release of information indicator.','MA58','Missing/incomplete/invalid release of information indicator.'),
(438,'4/16/2024, 08:59:28',1,'Alert The patient overpaid you for these services. You must issue the patient a refund within 30 days for the difference between his/her payment and the total amount shown as patient responsibility on this notice.','MA59','Alert The patient overpaid you for these services. You must issue the patient a refund within 30 days for the difference between his/her payment and the total amount shown as patient responsibility on this notice.'),
(439,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient relationship to insured.','MA60','Missing/incomplete/invalid patient relationship to insured.'),
(440,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid social security number or health insurance Claim #.','MA61','Missing/incomplete/invalid social security number or health insurance Claim #.'),
(441,'4/16/2024, 08:59:28',1,'Alert This is a telephone review decision.','MA62','Alert This is a telephone review decision.'),
(442,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid principal diagnosis.','MA63','Missing/incomplete/invalid principal diagnosis.'),
(443,'4/16/2024, 08:59:28',1,'Our records indicate that we should be the third payer for this claim.  We cannot process this claim until we have received payment information from the primary and secondary payers.','MA64','Our records indicate that we should be the third payer for this claim.  We cannot process this claim until we have received payment information from the primary and secondary payers.'),
(444,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid admitting diagnosis.','MA65','Missing/incomplete/invalid admitting diagnosis.'),
(445,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid principal procedure code.','MA66','Missing/incomplete/invalid principal procedure code.'),
(446,'4/16/2024, 08:59:28',1,'Correction to a prior claim.','MA67','Correction to a prior claim.'),
(447,'4/16/2024, 08:59:28',1,'Alert We did not crossover this claim because the secondary insurance information on the claim was incomplete. Please supply complete information or use the PLANID of the insurer to assure correct and timely routing of the claim.','MA68','Alert We did not crossover this claim because the secondary insurance information on the claim was incomplete. Please supply complete information or use the PLANID of the insurer to assure correct and timely routing of the claim.'),
(448,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid remarks.','MA69','Missing/incomplete/invalid remarks.'),
(449,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid provider representative signature.','MA70','Missing/incomplete/invalid provider representative signature.'),
(450,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid provider representative signature date.','MA71','Missing/incomplete/invalid provider representative signature date.'),
(451,'4/16/2024, 08:59:28',1,'Alert The patient overpaid you for these assigned services.  You must issue the patient a refund within 30 days for the difference between his/her payment to you and the total of the amount shown as patient responsibility and as paid to the patient','MA72','Alert The patient overpaid you for these assigned services.  You must issue the patient a refund within 30 days for the difference between his/her payment to you and the total of the amount shown as patient responsibility and as paid to the patient'),
(452,'4/16/2024, 08:59:28',1,'Informational remittance associated with a Medicare demonstration.  No payment issued under fee-for-service Medicare as the patient has elected managed care.','MA73','Informational remittance associated with a Medicare demonstration.  No payment issued under fee-for-service Medicare as the patient has elected managed care.'),
(453,'4/16/2024, 08:59:28',1,'This payment replaces an earlier payment for this claim that was either lost, damaged or returned.','MA74','This payment replaces an earlier payment for this claim that was either lost, damaged or returned.'),
(454,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient or authorized representative signature.','MA75','Missing/incomplete/invalid patient or authorized representative signature.'),
(455,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid provider identifier for home health agency or hospice when physician is performing care plan oversight services.','MA76','Missing/incomplete/invalid provider identifier for home health agency or hospice when physician is performing care plan oversight services.'),
(456,'4/16/2024, 08:59:28',1,'Alert The patient overpaid you. You must issue the patient a refund within 30 days for the difference between the patient''s payment less the total of our and other payer payments and the amount shown as patient responsibility on this notice.','MA77','Alert The patient overpaid you. You must issue the patient a refund within 30 days for the difference between the patient''s payment less the total of our and other payer payments and the amount shown as patient responsibility on this notice.'),
(457,'4/16/2024, 08:59:28',1,'Billed in excess of interim rate.','MA79','Billed in excess of interim rate.'),
(458,'4/16/2024, 08:59:28',1,'Informational notice. No payment issued for this claim with this notice. Payment issued to the hospital by its intermediary for all services for this encounter under a demonstration project.','MA80','Informational notice. No payment issued for this claim with this notice. Payment issued to the hospital by its intermediary for all services for this encounter under a demonstration project.'),
(459,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid provider/supplier signature.','MA81','Missing/incomplete/invalid provider/supplier signature.'),
(460,'4/16/2024, 08:59:28',1,'Did not indicate whether we are the primary or secondary payer.','MA83','Did not indicate whether we are the primary or secondary payer.'),
(461,'4/16/2024, 08:59:28',1,'Patient identified as participating in the National Emphysema Treatment Trial but our records indicate that this patient is either not a participant, or has not yet been approved for this phase of the study.  Contact Johns Hopkins University, the stu','MA84','Patient identified as participating in the National Emphysema Treatment Trial but our records indicate that this patient is either not a participant, or has not yet been approved for this phase of the study.  Contact Johns Hopkins University, the stu'),
(462,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid insured''s address and/or telephone number for the primary payer.','MA88','Missing/incomplete/invalid insured''s address and/or telephone number for the primary payer.'),
(463,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient''s relationship to the insured for the primary payer.','MA89','Missing/incomplete/invalid patient''s relationship to the insured for the primary payer.'),
(464,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid employment status code for the primary insured.','MA90','Missing/incomplete/invalid employment status code for the primary insured.'),
(465,'4/16/2024, 08:59:28',1,'This determination is the result of the appeal you filed.','MA91','This determination is the result of the appeal you filed.'),
(466,'4/16/2024, 08:59:28',1,'Missing plan information for other insurance.','MA92','Missing plan information for other insurance.'),
(467,'4/16/2024, 08:59:28',1,'Non-PIP (Periodic Interim Payment) claim.','MA93','Non-PIP (Periodic Interim Payment) claim.'),
(468,'4/16/2024, 08:59:28',1,'Did not enter the statement “Attending physician not hospice employee” on the claim form to certify that the rendering physician is not an employee of the hospice.','MA94','Did not enter the statement “Attending physician not hospice employee” on the claim form to certify that the rendering physician is not an employee of the hospice.'),
(469,'4/16/2024, 08:59:28',1,'Claim rejected. Coded as a Medicare Managed Care Demonstration but the patient is not enrolled in a Medicare managed care plan.','MA96','Claim rejected. Coded as a Medicare Managed Care Demonstration but the patient is not enrolled in a Medicare managed care plan.'),
(470,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Medicare Managed Care Demonstration contract number or clinical trial registry number.','MA97','Missing/incomplete/invalid Medicare Managed Care Demonstration contract number or clinical trial registry number.'),
(471,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Medigap information.','MA99','Missing/incomplete/invalid Medigap information.'),
(472,'4/16/2024, 08:59:28',1,'Alert You may appeal this decision in writing within the required time limits following receipt of this notice by following the instructions included in your contract or plan benefit documents.','N1','Alert You may appeal this decision in writing within the required time limits following receipt of this notice by following the instructions included in your contract or plan benefit documents.'),
(473,'4/16/2024, 08:59:28',1,'Payment based on the findings of a review organization/professional consult/manual adjudication/medical or dental advisor.','N10','Payment based on the findings of a review organization/professional consult/manual adjudication/medical or dental advisor.'),
(474,'4/16/2024, 08:59:28',1,'PPS (Prospect Payment System) code corrected during adjudication.','N100','PPS (Prospect Payment System) code corrected during adjudication.'),
(475,'4/16/2024, 08:59:28',1,'This claim has been denied without reviewing the medical record because the requested records were not received or were not received timely.','N102','This claim has been denied without reviewing the medical record because the requested records were not received or were not received timely.'),
(476,'4/16/2024, 08:59:28',1,'Social Security records indicate that this patient was a prisoner when the service was rendered.  This payer does not cover items and services furnished to an  individual while they are in State or local custody under a penal authority, unless under','N103','Social Security records indicate that this patient was a prisoner when the service was rendered.  This payer does not cover items and services furnished to an  individual while they are in State or local custody under a penal authority, unless under'),
(477,'4/16/2024, 08:59:28',1,'This claim/service is not payable under our claims jurisdiction area. You can identify the correct Medicare contractor to process this claim/service through the CMS website at www.cms.gov.','N104','This claim/service is not payable under our claims jurisdiction area. You can identify the correct Medicare contractor to process this claim/service through the CMS website at www.cms.gov.'),
(478,'4/16/2024, 08:59:28',1,'This is a misdirected claim/service for an RRB beneficiary. Submit paper claims to the RRB carrier - Palmetto GBA, P.O. Box 10066, Augusta, GA 30999. Call ************ for RRB EDI information for electronic claims processing.','N105','This is a misdirected claim/service for an RRB beneficiary. Submit paper claims to the RRB carrier - Palmetto GBA, P.O. Box 10066, Augusta, GA 30999. Call ************ for RRB EDI information for electronic claims processing.'),
(479,'4/16/2024, 08:59:28',1,'Payment for services furnished to Skilled Nursing Facility (SNF) inpatients (except for excluded services) can only be made to the SNF. You must request payment from the SNF rather than the patient for this service.','N106','Payment for services furnished to Skilled Nursing Facility (SNF) inpatients (except for excluded services) can only be made to the SNF. You must request payment from the SNF rather than the patient for this service.'),
(480,'4/16/2024, 08:59:28',1,'Services furnished to Skilled Nursing Facility (SNF) inpatients must be billed on the inpatient claim. They cannot be billed separately as outpatient services.','N107','Services furnished to Skilled Nursing Facility (SNF) inpatients must be billed on the inpatient claim. They cannot be billed separately as outpatient services.'),
(481,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid upgrade information.','N108','Missing/incomplete/invalid upgrade information.'),
(482,'4/16/2024, 08:59:28',1,'This claim/service was chosen for complex review and was denied after reviewing the medical records.','N109','This claim/service was chosen for complex review and was denied after reviewing the medical records.'),
(483,'4/16/2024, 08:59:28',1,'Denial reversed because of medical review.','N11','Denial reversed because of medical review.'),
(484,'4/16/2024, 08:59:28',1,'This facility is not certified for film mammography.','N110','This facility is not certified for film mammography.'),
(485,'4/16/2024, 08:59:28',1,'No appeal right except duplicate claim/service issue. This service was included in a claim that has been previously billed and adjudicated.','N111','No appeal right except duplicate claim/service issue. This service was included in a claim that has been previously billed and adjudicated.'),
(486,'4/16/2024, 08:59:28',1,'This claim is excluded from your electronic remittance advice.','N112','This claim is excluded from your electronic remittance advice.'),
(487,'4/16/2024, 08:59:28',1,'Only one initial visit is covered per physician, group practice or provider.','N113','Only one initial visit is covered per physician, group practice or provider.'),
(488,'4/16/2024, 08:59:28',1,'During the transition to the Ambulance Fee Schedule, payment is based on the lesser of a blended amount calculated using a percentage of the reasonable charge/cost and fee schedule amounts, or the submitted charge for the service.  You will be notifi','N114','During the transition to the Ambulance Fee Schedule, payment is based on the lesser of a blended amount calculated using a percentage of the reasonable charge/cost and fee schedule amounts, or the submitted charge for the service.  You will be notifi'),
(489,'4/16/2024, 08:59:28',1,'This decision was based on a Local Coverage Determination (LCD).  An LCD provides a guide to assist in determining whether a particular item or service is covered. A copy of this policy is available at www.cms.gov/mcd, or if you do not have web acces','N115','This decision was based on a Local Coverage Determination (LCD).  An LCD provides a guide to assist in determining whether a particular item or service is covered. A copy of this policy is available at www.cms.gov/mcd, or if you do not have web acces'),
(490,'4/16/2024, 08:59:28',1,'This payment is being made conditionally because the service was provided in the home, and it is possible that the patient is under a home health episode of care.  When a patient is treated under a home health episode of care, consolidated billing re','N116','This payment is being made conditionally because the service was provided in the home, and it is possible that the patient is under a home health episode of care.  When a patient is treated under a home health episode of care, consolidated billing re'),
(491,'4/16/2024, 08:59:28',1,'This service is paid only once in a patient''s lifetime.','N117','This service is paid only once in a patient''s lifetime.'),
(492,'4/16/2024, 08:59:28',1,'This service is not paid if billed more than once every 28 days.','N118','This service is not paid if billed more than once every 28 days.'),
(493,'4/16/2024, 08:59:28',1,'This service is not paid if billed once every 28 days, and the patient has spent 5 or more consecutive days in any inpatient or Skilled /nursing Facility (SNF) within those 28 days.','N119','This service is not paid if billed once every 28 days, and the patient has spent 5 or more consecutive days in any inpatient or Skilled /nursing Facility (SNF) within those 28 days.'),
(494,'4/16/2024, 08:59:28',1,'Policy provides coverage supplemental to Medicare. As the member does not appear to be enrolled in the applicable part of Medicare, the member is responsible for payment of the portion of the charge that would have been covered by Medicare.','N12','Policy provides coverage supplemental to Medicare. As the member does not appear to be enrolled in the applicable part of Medicare, the member is responsible for payment of the portion of the charge that would have been covered by Medicare.'),
(495,'4/16/2024, 08:59:28',1,'Payment is subject to home health prospective payment system partial episode payment adjustment. Patient was transferred/discharged/readmitted during the payment episode.','N120','Payment is subject to home health prospective payment system partial episode payment adjustment. Patient was transferred/discharged/readmitted during the payment episode.'),
(496,'4/16/2024, 08:59:28',1,'Medicare Part B does not pay for items or services provided by this type of practitioner for beneficiaries in a Medicare Part A covered Skilled Nursing Facility (SNF) stay.','N121','Medicare Part B does not pay for items or services provided by this type of practitioner for beneficiaries in a Medicare Part A covered Skilled Nursing Facility (SNF) stay.'),
(497,'4/16/2024, 08:59:28',1,'Add-on code cannot be billed by itself.','N122','Add-on code cannot be billed by itself.'),
(498,'4/16/2024, 08:59:28',1,'This is a split service and represents a portion of the units from the originally submitted service.','N123','This is a split service and represents a portion of the units from the originally submitted service.'),
(499,'4/16/2024, 08:59:28',1,'Payment has been denied for the/made only for a less extensive service/item because the information furnished does not substantiate the need for the (more extensive) service/item. The patient is liable for the charges for this service/item as you inf','N124','Payment has been denied for the/made only for a less extensive service/item because the information furnished does not substantiate the need for the (more extensive) service/item. The patient is liable for the charges for this service/item as you inf'),
(500,'4/16/2024, 08:59:28',1,'Payment has been (denied for the/made only for a less extensive) service/item because the information furnished does not substantiate the need for the (more extensive) service/item. If you have collected any amount from the patient, you must refund t','N125','Payment has been (denied for the/made only for a less extensive) service/item because the information furnished does not substantiate the need for the (more extensive) service/item. If you have collected any amount from the patient, you must refund t'),
(501,'4/16/2024, 08:59:28',1,'Social Security Records indicate that this individual has been deported. This payer does not cover items and services furnished to individuals who have been deported.','N126','Social Security Records indicate that this individual has been deported. This payer does not cover items and services furnished to individuals who have been deported.'),
(502,'4/16/2024, 08:59:28',1,'This is a misdirected claim/service for a United Mine Workers of America (UMWA) beneficiary. Please submit claims to them.','N127','This is a misdirected claim/service for a United Mine Workers of America (UMWA) beneficiary. Please submit claims to them.'),
(503,'4/16/2024, 08:59:28',1,'This amount represents the prior to coverage portion of the allowance.','N128','This amount represents the prior to coverage portion of the allowance.'),
(504,'4/16/2024, 08:59:28',1,'Not eligible due to the patient''s age.','N129','Not eligible due to the patient''s age.'),
(505,'4/16/2024, 08:59:28',1,'Payment based on professional/technical component modifier(s).','N13','Payment based on professional/technical component modifier(s).'),
(506,'4/16/2024, 08:59:28',1,'Consult plan benefit documents/guidelines for information about restrictions for this service.','N130','Consult plan benefit documents/guidelines for information about restrictions for this service.'),
(507,'4/16/2024, 08:59:28',1,'Total payments under multiple contracts cannot exceed the allowance for this service.','N131','Total payments under multiple contracts cannot exceed the allowance for this service.'),
(508,'4/16/2024, 08:59:28',1,'Alert Payments will cease for services rendered by this US Government debarred or excluded provider after the 30 day grace period as previously notified.','N132','Alert Payments will cease for services rendered by this US Government debarred or excluded provider after the 30 day grace period as previously notified.'),
(509,'4/16/2024, 08:59:28',1,'Alert Services for predetermination and services requesting payment are being processed separately.','N133','Alert Services for predetermination and services requesting payment are being processed separately.'),
(510,'4/16/2024, 08:59:28',1,'Alert This represents your scheduled payment for this service. If treatment has been discontinued, please contact Customer Service.','N134','Alert This represents your scheduled payment for this service. If treatment has been discontinued, please contact Customer Service.'),
(511,'4/16/2024, 08:59:28',1,'Record fees are the patient''s responsibility and limited to the specified co-payment.','N135','Record fees are the patient''s responsibility and limited to the specified co-payment.'),
(512,'4/16/2024, 08:59:28',1,'Alert To obtain information on the process to file an appeal in Arizona, call the Department''s Consumer Assistance Office at (************* or (*************.','N136','Alert To obtain information on the process to file an appeal in Arizona, call the Department''s Consumer Assistance Office at (************* or (*************.'),
(513,'4/16/2024, 08:59:28',1,'Alert The provider acting on the Member''s behalf, may file an appeal with the Payer. The provider, acting on the Member''s behalf, may file a complaint with the State Insurance Regulatory Authority without first filing an appeal, if the coverage de','N137','Alert The provider acting on the Member''s behalf, may file an appeal with the Payer. The provider, acting on the Member''s behalf, may file a complaint with the State Insurance Regulatory Authority without first filing an appeal, if the coverage de'),
(514,'4/16/2024, 08:59:28',1,'Alert In the event you disagree with the Dental Advisor''s opinion and have additional information relative to the case, you may submit radiographs to the Dental Advisor Unit at the subscriber''s dental insurance carrier for a second Independent Den','N138','Alert In the event you disagree with the Dental Advisor''s opinion and have additional information relative to the case, you may submit radiographs to the Dental Advisor Unit at the subscriber''s dental insurance carrier for a second Independent Den'),
(515,'4/16/2024, 08:59:28',1,'Alert Under the Code of Federal Regulations, Chapter 32, Section 199.13 a non-participating provider is not an appropriate appealing party. Therefore, if you disagree with the Dental Advisor''s opinion, you may appeal the determination if appointed','N139','Alert Under the Code of Federal Regulations, Chapter 32, Section 199.13 a non-participating provider is not an appropriate appealing party. Therefore, if you disagree with the Dental Advisor''s opinion, you may appeal the determination if appointed'),
(516,'4/16/2024, 08:59:28',1,'Alert You have not been designated as an authorized OCONUS provider therefore are not considered an appropriate appealing party. If the beneficiary has appointed you, in writing, to act as his/her representative and you disagree with the Dental Advi','N140','Alert You have not been designated as an authorized OCONUS provider therefore are not considered an appropriate appealing party. If the beneficiary has appointed you, in writing, to act as his/her representative and you disagree with the Dental Advi'),
(517,'4/16/2024, 08:59:28',1,'The patient was not residing in a long-term care facility during all or part of the service dates billed.','N141','The patient was not residing in a long-term care facility during all or part of the service dates billed.'),
(518,'4/16/2024, 08:59:28',1,'The original claim was denied.  Resubmit a new claim, not a replacement claim.','N142','The original claim was denied.  Resubmit a new claim, not a replacement claim.'),
(519,'4/16/2024, 08:59:28',1,'The patient was not in a hospice program during all or part of the service dates billed.','N143','The patient was not in a hospice program during all or part of the service dates billed.'),
(520,'4/16/2024, 08:59:28',1,'The rate changed during the dates of service billed.','N144','The rate changed during the dates of service billed.'),
(521,'4/16/2024, 08:59:28',1,'Missing screening document.','N146','Missing screening document.'),
(522,'4/16/2024, 08:59:28',1,'Long term care case mix or per diem rate cannot be determined because the patient ID number is missing, incomplete, or invalid on the assignment request.','N147','Long term care case mix or per diem rate cannot be determined because the patient ID number is missing, incomplete, or invalid on the assignment request.'),
(523,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid date of last menstrual period.','N148','Missing/incomplete/invalid date of last menstrual period.'),
(524,'4/16/2024, 08:59:28',1,'Rebill all applicable services on a single claim.','N149','Rebill all applicable services on a single claim.'),
(525,'4/16/2024, 08:59:28',1,'Services for a newborn must be billed separately.','N15','Services for a newborn must be billed separately.'),
(526,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid model number.','N150','Missing/incomplete/invalid model number.'),
(527,'4/16/2024, 08:59:28',1,'Telephone contact services will not be paid until the face-to-face contact requirement has been met.','N151','Telephone contact services will not be paid until the face-to-face contact requirement has been met.'),
(528,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid replacement claim information.','N152','Missing/incomplete/invalid replacement claim information.'),
(529,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid room and board rate.','N153','Missing/incomplete/invalid room and board rate.'),
(530,'4/16/2024, 08:59:28',1,'Alert This payment was delayed for correction of the provider''s mailing address.','N154','Alert This payment was delayed for correction of the provider''s mailing address.'),
(531,'4/16/2024, 08:59:28',1,'Alert Our records do not indicate that other insurance is on file.  Please submit other insurance information for our records.','N155','Alert Our records do not indicate that other insurance is on file.  Please submit other insurance information for our records.'),
(532,'4/16/2024, 08:59:28',1,'Alert The patient is responsible for the difference between the approved treatment and the elective treatment.','N156','Alert The patient is responsible for the difference between the approved treatment and the elective treatment.'),
(533,'4/16/2024, 08:59:28',1,'Transportation to/from this destination is not covered.','N157','Transportation to/from this destination is not covered.'),
(534,'4/16/2024, 08:59:28',1,'Transportation in a vehicle other than an ambulance is not covered.','N158','Transportation in a vehicle other than an ambulance is not covered.'),
(535,'4/16/2024, 08:59:28',1,'Payment denied/reduced because mileage is not covered when the patient is not in the ambulance.','N159','Payment denied/reduced because mileage is not covered when the patient is not in the ambulance.'),
(536,'4/16/2024, 08:59:28',1,'Family/member Out-of-Pocket maximum has been met. Payment based on a higher percentage.','N16','Family/member Out-of-Pocket maximum has been met. Payment based on a higher percentage.'),
(537,'4/16/2024, 08:59:28',1,'The patient must choose an option before a payment can be made for this procedure/ equipment/ supply/ service.','N160','The patient must choose an option before a payment can be made for this procedure/ equipment/ supply/ service.'),
(538,'4/16/2024, 08:59:28',1,'This drug/service/supply is covered only when the associated service is covered.','N161','This drug/service/supply is covered only when the associated service is covered.'),
(539,'4/16/2024, 08:59:28',1,'Alert Although your claim was paid, you have billed for a test/specialty not included in your Laboratory Certification.  Your failure to correct the laboratory certification information will result in a denial of payment in the near future.','N162','Alert Although your claim was paid, you have billed for a test/specialty not included in your Laboratory Certification.  Your failure to correct the laboratory certification information will result in a denial of payment in the near future.'),
(540,'4/16/2024, 08:59:28',1,'Medical record does not support code billed per the code definition.','N163','Medical record does not support code billed per the code definition.'),
(541,'4/16/2024, 08:59:28',1,'Charges exceed the post-transplant coverage limit.','N167','Charges exceed the post-transplant coverage limit.'),
(542,'4/16/2024, 08:59:28',1,'A new/revised/renewed certificate of medical necessity is needed.','N170','A new/revised/renewed certificate of medical necessity is needed.'),
(543,'4/16/2024, 08:59:28',1,'Payment for repair or replacement is not covered or has exceeded the purchase price.','N171','Payment for repair or replacement is not covered or has exceeded the purchase price.'),
(544,'4/16/2024, 08:59:28',1,'The patient is not liable for the denied/adjusted charge(s) for receiving any updated service/item.','N172','The patient is not liable for the denied/adjusted charge(s) for receiving any updated service/item.'),
(545,'4/16/2024, 08:59:28',1,'No qualifying hospital stay dates were provided for this episode of care.','N173','No qualifying hospital stay dates were provided for this episode of care.'),
(546,'4/16/2024, 08:59:28',1,'This is not a covered service/procedure/ equipment/bed, however patient liability is limited to amounts shown in the adjustments under group ''PR''.','N174','This is not a covered service/procedure/ equipment/bed, however patient liability is limited to amounts shown in the adjustments under group ''PR''.'),
(547,'4/16/2024, 08:59:28',1,'Missing review organization approval.','N175','Missing review organization approval.'),
(548,'4/16/2024, 08:59:28',1,'Services provided aboard a ship are covered only when the ship is of United States registry and is in United States waters. In addition, a doctor licensed to practice in the United States must provide the service.','N176','Services provided aboard a ship are covered only when the ship is of United States registry and is in United States waters. In addition, a doctor licensed to practice in the United States must provide the service.'),
(549,'4/16/2024, 08:59:28',1,'Alert We did not send this claim to the patient''s other insurer. They have indicated no additional payment can be made.','N177','Alert We did not send this claim to the patient''s other insurer. They have indicated no additional payment can be made.'),
(550,'4/16/2024, 08:59:28',1,'Missing pre-operative photos or visual field results.','N178','Missing pre-operative photos or visual field results.'),
(551,'4/16/2024, 08:59:28',1,'Additional information has been requested from the member.  The charges will be reconsidered upon receipt of that information.','N179','Additional information has been requested from the member.  The charges will be reconsidered upon receipt of that information.'),
(552,'4/16/2024, 08:59:28',1,'This item or service does not meet the criteria for the category under which it was billed.','N180','This item or service does not meet the criteria for the category under which it was billed.'),
(553,'4/16/2024, 08:59:28',1,'Additional information is required from another provider involved in this service.','N181','Additional information is required from another provider involved in this service.'),
(554,'4/16/2024, 08:59:28',1,'This claim/service must be billed according to the schedule for this plan.','N182','This claim/service must be billed according to the schedule for this plan.'),
(555,'4/16/2024, 08:59:28',1,'Alert This is a predetermination advisory message, when this service is submitted for payment additional documentation as specified in plan documents will be required to process benefits.','N183','Alert This is a predetermination advisory message, when this service is submitted for payment additional documentation as specified in plan documents will be required to process benefits.'),
(556,'4/16/2024, 08:59:28',1,'Rebill technical and professional components separately.','N184','Rebill technical and professional components separately.'),
(557,'4/16/2024, 08:59:28',1,'Alert Do not resubmit this claim/service.','N185','Alert Do not resubmit this claim/service.'),
(558,'4/16/2024, 08:59:28',1,'Non-Availability Statement (NAS) required for this service. Contact the nearest Military Treatment Facility (MTF) for assistance.','N186','Non-Availability Statement (NAS) required for this service. Contact the nearest Military Treatment Facility (MTF) for assistance.'),
(559,'4/16/2024, 08:59:28',1,'Alert You may request a review in writing within the required time limits following receipt of this notice by following the instructions included in your contract or plan benefit documents.','N187','Alert You may request a review in writing within the required time limits following receipt of this notice by following the instructions included in your contract or plan benefit documents.'),
(560,'4/16/2024, 08:59:28',1,'The approved level of care does not match the procedure code submitted.','N188','The approved level of care does not match the procedure code submitted.'),
(561,'4/16/2024, 08:59:28',1,'Alert This service has been paid as a one-time exception to the plan''s benefit restrictions.','N189','Alert This service has been paid as a one-time exception to the plan''s benefit restrictions.'),
(562,'4/16/2024, 08:59:28',1,'Procedure code incidental to primary procedure.','N19','Procedure code incidental to primary procedure.'),
(563,'4/16/2024, 08:59:28',1,'Missing contract indicator.','N190','Missing contract indicator.'),
(564,'4/16/2024, 08:59:28',1,'The provider must update insurance information directly with the payer.','N191','The provider must update insurance information directly with the payer.'),
(565,'4/16/2024, 08:59:28',1,'Patient is a Medicaid/Qualified Medicare Beneficiary.','N192','Patient is a Medicaid/Qualified Medicare Beneficiary.'),
(566,'4/16/2024, 08:59:28',1,'Specific federal/state/local program may cover this service through another payer.','N193','Specific federal/state/local program may cover this service through another payer.'),
(567,'4/16/2024, 08:59:28',1,'Technical component not paid if provider does not own the equipment used.','N194','Technical component not paid if provider does not own the equipment used.'),
(568,'4/16/2024, 08:59:28',1,'The technical component must be billed separately.','N195','The technical component must be billed separately.'),
(569,'4/16/2024, 08:59:28',1,'Alert Patient eligible to apply for other coverage which may be primary.','N196','Alert Patient eligible to apply for other coverage which may be primary.'),
(570,'4/16/2024, 08:59:28',1,'The subscriber must update insurance information directly with the payer.','N197','The subscriber must update insurance information directly with the payer.'),
(571,'4/16/2024, 08:59:28',1,'Rendering provider must be affiliated with the pay-to provider.','N198','Rendering provider must be affiliated with the pay-to provider.'),
(572,'4/16/2024, 08:59:28',1,'Additional payment/recoupment approved based on payer-initiated review/audit.','N199','Additional payment/recoupment approved based on payer-initiated review/audit.'),
(573,'4/16/2024, 08:59:28',1,'This allowance has been made in accordance with the most appropriate course of treatment provision of the plan.','N2','This allowance has been made in accordance with the most appropriate course of treatment provision of the plan.'),
(574,'4/16/2024, 08:59:28',1,'Service not payable with other service rendered on the same date.','N20','Service not payable with other service rendered on the same date.'),
(575,'4/16/2024, 08:59:28',1,'The professional component must be billed separately.','N200','The professional component must be billed separately.'),
(576,'4/16/2024, 08:59:28',1,'Additional information/explanation will be sent separately','N202','Additional information/explanation will be sent separately'),
(577,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid anesthesia time/units','N203','Missing/incomplete/invalid anesthesia time/units'),
(578,'4/16/2024, 08:59:28',1,'Services under review for possible pre-existing condition. Send medical records for prior 12 months','N204','Services under review for possible pre-existing condition. Send medical records for prior 12 months'),
(579,'4/16/2024, 08:59:28',1,'Information provided was illegible','N205','Information provided was illegible'),
(580,'4/16/2024, 08:59:28',1,'The supporting documentation does not match the claim','N206','The supporting documentation does not match the claim'),
(581,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid weight.','N207','Missing/incomplete/invalid weight.'),
(582,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid DRG code','N208','Missing/incomplete/invalid DRG code'),
(583,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid taxpayer identification number (TIN).','N209','Missing/incomplete/invalid taxpayer identification number (TIN).'),
(584,'4/16/2024, 08:59:28',1,'Alert Your line item has been separated into multiple lines to expedite handling.','N21','Alert Your line item has been separated into multiple lines to expedite handling.'),
(585,'4/16/2024, 08:59:28',1,'Alert You may appeal this decision','N210','Alert You may appeal this decision'),
(586,'4/16/2024, 08:59:28',1,'Alert You may not appeal this decision','N211','Alert You may not appeal this decision'),
(587,'4/16/2024, 08:59:28',1,'Charges processed under a Point of Service benefit','N212','Charges processed under a Point of Service benefit'),
(588,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid facility/discrete unit DRG/DRG exempt status information','N213','Missing/incomplete/invalid facility/discrete unit DRG/DRG exempt status information'),
(589,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid history of the related initial surgical procedure(s)','N214','Missing/incomplete/invalid history of the related initial surgical procedure(s)'),
(590,'4/16/2024, 08:59:28',1,'Alert A payer providing supplemental or secondary coverage shall not require a claims determination for this service from a primary payer as a condition of making its own claims determination.','N215','Alert A payer providing supplemental or secondary coverage shall not require a claims determination for this service from a primary payer as a condition of making its own claims determination.'),
(591,'4/16/2024, 08:59:28',1,'We do not offer coverage for this type of service or the patient is not enrolled in this portion of our benefit package','N216','We do not offer coverage for this type of service or the patient is not enrolled in this portion of our benefit package'),
(592,'4/16/2024, 08:59:28',1,'We pay only one site of service per provider per claim','N217','We pay only one site of service per provider per claim'),
(593,'4/16/2024, 08:59:28',1,'You must furnish and service this item for as long as the patient continues to need it.  We can pay for maintenance and/or servicing for the time period specified in the contract or coverage manual.','N218','You must furnish and service this item for as long as the patient continues to need it.  We can pay for maintenance and/or servicing for the time period specified in the contract or coverage manual.'),
(594,'4/16/2024, 08:59:28',1,'Payment based on previous payer''s allowed amount.','N219','Payment based on previous payer''s allowed amount.'),
(595,'4/16/2024, 08:59:28',1,'This procedure code was added/changed because it more accurately describes the services rendered.','N22','This procedure code was added/changed because it more accurately describes the services rendered.'),
(596,'4/16/2024, 08:59:28',1,'Alert See the payer''s web site or contact the payer''s Customer Service department to obtain forms and instructions for filing a provider dispute.','N220','Alert See the payer''s web site or contact the payer''s Customer Service department to obtain forms and instructions for filing a provider dispute.'),
(597,'4/16/2024, 08:59:28',1,'Missing Admitting History and Physical report.','N221','Missing Admitting History and Physical report.'),
(598,'4/16/2024, 08:59:28',1,'Incomplete/invalid Admitting History and Physical report.','N222','Incomplete/invalid Admitting History and Physical report.'),
(599,'4/16/2024, 08:59:28',1,'Missing documentation of benefit to the patient during the initial treatment period.','N223','Missing documentation of benefit to the patient during the initial treatment period.'),
(600,'4/16/2024, 08:59:28',1,'Incomplete/invalid documentation of benefit to the patient during initial treatment period.','N224','Incomplete/invalid documentation of benefit to the patient during initial treatment period.'),
(601,'4/16/2024, 08:59:28',1,'Incomplete/invalid documentation/orders/notes/summary/report/chart.','N225','Incomplete/invalid documentation/orders/notes/summary/report/chart.'),
(602,'4/16/2024, 08:59:28',1,'Incomplete/invalid American Diabetes Association Certificate of Recognition.','N226','Incomplete/invalid American Diabetes Association Certificate of Recognition.'),
(603,'4/16/2024, 08:59:28',1,'Incomplete/invalid Certificate of Medical Necessity.','N227','Incomplete/invalid Certificate of Medical Necessity.'),
(604,'4/16/2024, 08:59:28',1,'Incomplete/invalid consent form.','N228','Incomplete/invalid consent form.'),
(605,'4/16/2024, 08:59:28',1,'Incomplete/invalid contract indicator.','N229','Incomplete/invalid contract indicator.'),
(606,'4/16/2024, 08:59:28',1,'Alert Patient liability may be affected due to coordination of benefits with other carriers and/or maximum benefit provisions.','N23','Alert Patient liability may be affected due to coordination of benefits with other carriers and/or maximum benefit provisions.'),
(607,'4/16/2024, 08:59:28',1,'Incomplete/invalid indication of whether the patient owns the equipment that requires the part or supply.','N230','Incomplete/invalid indication of whether the patient owns the equipment that requires the part or supply.'),
(608,'4/16/2024, 08:59:28',1,'Incomplete/invalid invoice or statement certifying the actual cost of the lens, less discounts, and/or the type of intraocular lens used.','N231','Incomplete/invalid invoice or statement certifying the actual cost of the lens, less discounts, and/or the type of intraocular lens used.'),
(609,'4/16/2024, 08:59:28',1,'Incomplete/invalid itemized bill/statement.','N232','Incomplete/invalid itemized bill/statement.'),
(610,'4/16/2024, 08:59:28',1,'Incomplete/invalid operative note/report.','N233','Incomplete/invalid operative note/report.'),
(611,'4/16/2024, 08:59:28',1,'Incomplete/invalid oxygen certification/recertification.','N234','Incomplete/invalid oxygen certification/recertification.'),
(612,'4/16/2024, 08:59:28',1,'Incomplete/invalid pacemaker registration form.','N235','Incomplete/invalid pacemaker registration form.'),
(613,'4/16/2024, 08:59:28',1,'Incomplete/invalid pathology report.','N236','Incomplete/invalid pathology report.'),
(614,'4/16/2024, 08:59:28',1,'Incomplete/invalid patient medical record for this service.','N237','Incomplete/invalid patient medical record for this service.'),
(615,'4/16/2024, 08:59:28',1,'Incomplete/invalid physician certified plan of care','N238','Incomplete/invalid physician certified plan of care'),
(616,'4/16/2024, 08:59:28',1,'Incomplete/invalid physician financial relationship form.','N239','Incomplete/invalid physician financial relationship form.'),
(617,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Electronic Funds Transfer (EFT) banking information.','N24','Missing/incomplete/invalid Electronic Funds Transfer (EFT) banking information.'),
(618,'4/16/2024, 08:59:28',1,'Incomplete/invalid radiology report.','N240','Incomplete/invalid radiology report.'),
(619,'4/16/2024, 08:59:28',1,'Incomplete/invalid review organization approval.','N241','Incomplete/invalid review organization approval.'),
(620,'4/16/2024, 08:59:28',1,'Incomplete/invalid radiology film(s)/image(s).','N242','Incomplete/invalid radiology film(s)/image(s).'),
(621,'4/16/2024, 08:59:28',1,'Incomplete/invalid/not approved screening document.','N243','Incomplete/invalid/not approved screening document.'),
(622,'4/16/2024, 08:59:28',1,'Incomplete/invalid pre-operative photos/visual field results.','N244','Incomplete/invalid pre-operative photos/visual field results.'),
(623,'4/16/2024, 08:59:28',1,'Incomplete/invalid plan information for other insurance','N245','Incomplete/invalid plan information for other insurance'),
(624,'4/16/2024, 08:59:28',1,'State regulated patient payment limitations apply to this service.','N246','State regulated patient payment limitations apply to this service.'),
(625,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid assistant surgeon taxonomy.','N247','Missing/incomplete/invalid assistant surgeon taxonomy.'),
(626,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid assistant surgeon name.','N248','Missing/incomplete/invalid assistant surgeon name.'),
(627,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid assistant surgeon primary identifier.','N249','Missing/incomplete/invalid assistant surgeon primary identifier.'),
(628,'4/16/2024, 08:59:28',1,'This company has been contracted by your benefit plan to provide administrative claims payment services only.  This company does not assume financial risk or obligation with respect to claims processed on behalf of your benefit plan.','N25','This company has been contracted by your benefit plan to provide administrative claims payment services only.  This company does not assume financial risk or obligation with respect to claims processed on behalf of your benefit plan.'),
(629,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid assistant surgeon secondary identifier.','N250','Missing/incomplete/invalid assistant surgeon secondary identifier.'),
(630,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid attending provider taxonomy.','N251','Missing/incomplete/invalid attending provider taxonomy.'),
(631,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid attending provider name.','N252','Missing/incomplete/invalid attending provider name.'),
(632,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid attending provider primary identifier.','N253','Missing/incomplete/invalid attending provider primary identifier.'),
(633,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid attending provider secondary identifier.','N254','Missing/incomplete/invalid attending provider secondary identifier.'),
(634,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid billing provider taxonomy.','N255','Missing/incomplete/invalid billing provider taxonomy.'),
(635,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid billing provider/supplier name.','N256','Missing/incomplete/invalid billing provider/supplier name.'),
(636,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid billing provider/supplier primary identifier.','N257','Missing/incomplete/invalid billing provider/supplier primary identifier.'),
(637,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid billing provider/supplier address.','N258','Missing/incomplete/invalid billing provider/supplier address.'),
(638,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid billing provider/supplier secondary identifier.','N259','Missing/incomplete/invalid billing provider/supplier secondary identifier.'),
(639,'4/16/2024, 08:59:28',1,'Missing itemized bill/statement.','N26','Missing itemized bill/statement.'),
(640,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid billing provider/supplier contact information.','N260','Missing/incomplete/invalid billing provider/supplier contact information.'),
(641,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid operating provider name.','N261','Missing/incomplete/invalid operating provider name.'),
(642,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid operating provider primary identifier.','N262','Missing/incomplete/invalid operating provider primary identifier.'),
(643,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid operating provider secondary identifier.','N263','Missing/incomplete/invalid operating provider secondary identifier.'),
(644,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid ordering provider name.','N264','Missing/incomplete/invalid ordering provider name.'),
(645,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid ordering provider primary identifier.','N265','Missing/incomplete/invalid ordering provider primary identifier.'),
(646,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid ordering provider address.','N266','Missing/incomplete/invalid ordering provider address.'),
(647,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid ordering provider secondary identifier.','N267','Missing/incomplete/invalid ordering provider secondary identifier.'),
(648,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid ordering provider contact information.','N268','Missing/incomplete/invalid ordering provider contact information.'),
(649,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other provider name.','N269','Missing/incomplete/invalid other provider name.'),
(650,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid treatment number.','N27','Missing/incomplete/invalid treatment number.'),
(651,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other provider primary identifier.','N270','Missing/incomplete/invalid other provider primary identifier.'),
(652,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other provider secondary identifier.','N271','Missing/incomplete/invalid other provider secondary identifier.'),
(653,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer attending provider identifier.','N272','Missing/incomplete/invalid other payer attending provider identifier.'),
(654,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer operating provider identifier.','N273','Missing/incomplete/invalid other payer operating provider identifier.'),
(655,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer other provider identifier.','N274','Missing/incomplete/invalid other payer other provider identifier.'),
(656,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer purchased service provider identifier.','N275','Missing/incomplete/invalid other payer purchased service provider identifier.'),
(657,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer referring provider identifier.','N276','Missing/incomplete/invalid other payer referring provider identifier.'),
(658,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer rendering provider identifier.','N277','Missing/incomplete/invalid other payer rendering provider identifier.'),
(659,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other payer service facility provider identifier.','N278','Missing/incomplete/invalid other payer service facility provider identifier.'),
(660,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid pay-to provider name.','N279','Missing/incomplete/invalid pay-to provider name.'),
(661,'4/16/2024, 08:59:28',1,'Consent form requirements not fulfilled.','N28','Consent form requirements not fulfilled.'),
(662,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid pay-to provider primary identifier.','N280','Missing/incomplete/invalid pay-to provider primary identifier.'),
(663,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid pay-to provider address.','N281','Missing/incomplete/invalid pay-to provider address.'),
(664,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid pay-to provider secondary identifier.','N282','Missing/incomplete/invalid pay-to provider secondary identifier.'),
(665,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid purchased service provider identifier.','N283','Missing/incomplete/invalid purchased service provider identifier.'),
(666,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid referring provider taxonomy.','N284','Missing/incomplete/invalid referring provider taxonomy.'),
(667,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid referring provider name.','N285','Missing/incomplete/invalid referring provider name.'),
(668,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid referring provider primary identifier.','N286','Missing/incomplete/invalid referring provider primary identifier.'),
(669,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid referring provider secondary identifier.','N287','Missing/incomplete/invalid referring provider secondary identifier.'),
(670,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid rendering provider taxonomy.','N288','Missing/incomplete/invalid rendering provider taxonomy.'),
(671,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid rendering provider name.','N289','Missing/incomplete/invalid rendering provider name.'),
(672,'4/16/2024, 08:59:28',1,'Missing documentation/orders/notes/summary/report/chart.','N29','Missing documentation/orders/notes/summary/report/chart.'),
(673,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid rendering provider primary identifier.','N290','Missing/incomplete/invalid rendering provider primary identifier.'),
(674,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid rendering provider secondary identifier.','N291','Missing/incomplete/invalid rendering provider secondary identifier.'),
(675,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid service facility name.','N292','Missing/incomplete/invalid service facility name.'),
(676,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid service facility primary identifier.','N293','Missing/incomplete/invalid service facility primary identifier.'),
(677,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid service facility primary address.','N294','Missing/incomplete/invalid service facility primary address.'),
(678,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid service facility secondary identifier.','N295','Missing/incomplete/invalid service facility secondary identifier.'),
(679,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid supervising provider name.','N296','Missing/incomplete/invalid supervising provider name.'),
(680,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid supervising provider primary identifier.','N297','Missing/incomplete/invalid supervising provider primary identifier.'),
(681,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid supervising provider secondary identifier.','N298','Missing/incomplete/invalid supervising provider secondary identifier.'),
(682,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid occurrence date(s).','N299','Missing/incomplete/invalid occurrence date(s).'),
(683,'4/16/2024, 08:59:28',1,'Missing consent form.','N3','Missing consent form.'),
(684,'4/16/2024, 08:59:28',1,'Patient ineligible for this service.','N30','Patient ineligible for this service.'),
(685,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid occurrence span date(s).','N300','Missing/incomplete/invalid occurrence span date(s).'),
(686,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid procedure date(s).','N301','Missing/incomplete/invalid procedure date(s).'),
(687,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other procedure date(s).','N302','Missing/incomplete/invalid other procedure date(s).'),
(688,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid principal procedure date.','N303','Missing/incomplete/invalid principal procedure date.'),
(689,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid dispensed date.','N304','Missing/incomplete/invalid dispensed date.'),
(690,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid accident date.','N305','Missing/incomplete/invalid accident date.'),
(691,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid acute manifestation date.','N306','Missing/incomplete/invalid acute manifestation date.'),
(692,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid adjudication or payment date.','N307','Missing/incomplete/invalid adjudication or payment date.'),
(693,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid appliance placement date.','N308','Missing/incomplete/invalid appliance placement date.'),
(694,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid assessment date.','N309','Missing/incomplete/invalid assessment date.'),
(695,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prescribing provider identifier.','N31','Missing/incomplete/invalid prescribing provider identifier.'),
(696,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid assumed or relinquished care date.','N310','Missing/incomplete/invalid assumed or relinquished care date.'),
(697,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid authorized to return to work date.','N311','Missing/incomplete/invalid authorized to return to work date.'),
(698,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid begin therapy date.','N312','Missing/incomplete/invalid begin therapy date.'),
(699,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid certification revision date.','N313','Missing/incomplete/invalid certification revision date.'),
(700,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid diagnosis date.','N314','Missing/incomplete/invalid diagnosis date.'),
(701,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid disability from date.','N315','Missing/incomplete/invalid disability from date.'),
(702,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid disability to date.','N316','Missing/incomplete/invalid disability to date.'),
(703,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid discharge hour.','N317','Missing/incomplete/invalid discharge hour.'),
(704,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid discharge or end of care date.','N318','Missing/incomplete/invalid discharge or end of care date.'),
(705,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid hearing or vision prescription date.','N319','Missing/incomplete/invalid hearing or vision prescription date.'),
(706,'4/16/2024, 08:59:28',1,'Claim must be submitted by the provider who rendered the code','N32','Claim must be submitted by the provider who rendered the code'),
(707,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Home Health Certification Period.','N320','Missing/incomplete/invalid Home Health Certification Period.'),
(708,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid last admission period.','N321','Missing/incomplete/invalid last admission period.'),
(709,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid last certification date.','N322','Missing/incomplete/invalid last certification date.'),
(710,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid last contact date.','N323','Missing/incomplete/invalid last contact date.'),
(711,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid last seen/visit date.','N324','Missing/incomplete/invalid last seen/visit date.'),
(712,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid last worked date.','N325','Missing/incomplete/invalid last worked date.'),
(713,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid last x-ray date.','N326','Missing/incomplete/invalid last x-ray date.'),
(714,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid other insured birth date.','N327','Missing/incomplete/invalid other insured birth date.'),
(715,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Oxygen Saturation Test date.','N328','Missing/incomplete/invalid Oxygen Saturation Test date.'),
(716,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient birth date.','N329','Missing/incomplete/invalid patient birth date.'),
(717,'4/16/2024, 08:59:28',1,'No record of health check prior to initiation of treatment.','N33','No record of health check prior to initiation of treatment.'),
(718,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient death date.','N330','Missing/incomplete/invalid patient death date.'),
(719,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid physician order date.','N331','Missing/incomplete/invalid physician order date.'),
(720,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prior hospital discharge date.','N332','Missing/incomplete/invalid prior hospital discharge date.'),
(721,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prior placement date.','N333','Missing/incomplete/invalid prior placement date.'),
(722,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid re-evaluation date','N334','Missing/incomplete/invalid re-evaluation date'),
(723,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid referral date.','N335','Missing/incomplete/invalid referral date.'),
(724,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid replacement date.','N336','Missing/incomplete/invalid replacement date.'),
(725,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid secondary diagnosis date.','N337','Missing/incomplete/invalid secondary diagnosis date.'),
(726,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid shipped date.','N338','Missing/incomplete/invalid shipped date.'),
(727,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid similar illness or symptom date.','N339','Missing/incomplete/invalid similar illness or symptom date.'),
(728,'4/16/2024, 08:59:28',1,'Incorrect claim form/format for this service.','N34','Incorrect claim form/format for this service.'),
(729,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid subscriber birth date.','N340','Missing/incomplete/invalid subscriber birth date.'),
(730,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid surgery date.','N341','Missing/incomplete/invalid surgery date.'),
(731,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid test performed date.','N342','Missing/incomplete/invalid test performed date.'),
(732,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Transcutaneous Electrical Nerve Stimulator (TENS) trial start date.','N343','Missing/incomplete/invalid Transcutaneous Electrical Nerve Stimulator (TENS) trial start date.'),
(733,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid Transcutaneous Electrical Nerve Stimulator (TENS) trial end date.','N344','Missing/incomplete/invalid Transcutaneous Electrical Nerve Stimulator (TENS) trial end date.'),
(734,'4/16/2024, 08:59:28',1,'Date range not valid with units submitted.','N345','Date range not valid with units submitted.'),
(735,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid oral cavity designation code.','N346','Missing/incomplete/invalid oral cavity designation code.'),
(736,'4/16/2024, 08:59:28',1,'Your claim for a referred or purchased service cannot be paid because payment has already been made for this same service to another provider by a payment contractor representing the payer.','N347','Your claim for a referred or purchased service cannot be paid because payment has already been made for this same service to another provider by a payment contractor representing the payer.'),
(737,'4/16/2024, 08:59:28',1,'You chose that this service/supply/drug would be rendered/supplied and billed by a different practitioner/supplier.','N348','You chose that this service/supply/drug would be rendered/supplied and billed by a different practitioner/supplier.'),
(738,'4/16/2024, 08:59:28',1,'The administration method and drug must be reported to adjudicate this service.','N349','The administration method and drug must be reported to adjudicate this service.'),
(739,'4/16/2024, 08:59:28',1,'Program integrity/utilization review decision.','N35','Program integrity/utilization review decision.'),
(740,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid description of service for a Not Otherwise Classified (NOC) code or for an Unlisted/By Report procedure.','N350','Missing/incomplete/invalid description of service for a Not Otherwise Classified (NOC) code or for an Unlisted/By Report procedure.'),
(741,'4/16/2024, 08:59:28',1,'Service date outside of the approved treatment plan service dates.','N351','Service date outside of the approved treatment plan service dates.'),
(742,'4/16/2024, 08:59:28',1,'Alert There are no scheduled payments for this service. Submit a claim for each patient visit.','N352','Alert There are no scheduled payments for this service. Submit a claim for each patient visit.'),
(743,'4/16/2024, 08:59:28',1,'Alert Benefits have been estimated, when the actual services have been rendered, additional payment will be considered based on the submitted claim.','N353','Alert Benefits have been estimated, when the actual services have been rendered, additional payment will be considered based on the submitted claim.'),
(744,'4/16/2024, 08:59:28',1,'Incomplete/invalid invoice','N354','Incomplete/invalid invoice'),
(745,'4/16/2024, 08:59:28',1,'Alert The law permits exceptions to the refund requirement in two cases - If you did not know, and could not have reasonably been expected to know, that we would not pay for this service; or - If you notified the patient in writing before providing','N355','Alert The law permits exceptions to the refund requirement in two cases - If you did not know, and could not have reasonably been expected to know, that we would not pay for this service; or - If you notified the patient in writing before providing'),
(746,'4/16/2024, 08:59:28',1,'Not covered when performed with, or subsequent to, a non-covered service.','N356','Not covered when performed with, or subsequent to, a non-covered service.'),
(747,'4/16/2024, 08:59:28',1,'Time frame requirements between this service/procedure/supply and a related service/procedure/supply have not been met.','N357','Time frame requirements between this service/procedure/supply and a related service/procedure/supply have not been met.'),
(748,'4/16/2024, 08:59:28',1,'Alert This decision may be reviewed if additional documentation as described in the contract or plan benefit documents is submitted.','N358','Alert This decision may be reviewed if additional documentation as described in the contract or plan benefit documents is submitted.'),
(749,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid height.','N359','Missing/incomplete/invalid height.'),
(750,'4/16/2024, 08:59:28',1,'Claim must meet primary payer''s processing requirements before we can consider payment.','N36','Claim must meet primary payer''s processing requirements before we can consider payment.'),
(751,'4/16/2024, 08:59:28',1,'Alert Coordination of benefits has not been calculated when estimating benefits for this predetermination. Submit payment information from the primary payer with the secondary claim.','N360','Alert Coordination of benefits has not been calculated when estimating benefits for this predetermination. Submit payment information from the primary payer with the secondary claim.'),
(752,'4/16/2024, 08:59:28',1,'The number of Days or Units of Service exceeds our acceptable maximum.','N362','The number of Days or Units of Service exceeds our acceptable maximum.'),
(753,'4/16/2024, 08:59:28',1,'Alert in the near future we are implementing new policies/procedures that would affect this determination.','N363','Alert in the near future we are implementing new policies/procedures that would affect this determination.'),
(754,'4/16/2024, 08:59:28',1,'Alert According to our agreement, you must waive the deductible and/or coinsurance amounts.','N364','Alert According to our agreement, you must waive the deductible and/or coinsurance amounts.'),
(755,'4/16/2024, 08:59:28',1,'This procedure code is not payable. It is for reporting/information purposes only.','N365','This procedure code is not payable. It is for reporting/information purposes only.'),
(756,'4/16/2024, 08:59:28',1,'Requested information not provided. The claim will be reopened if the information previously requested is submitted within one year after the date of this denial notice.','N366','Requested information not provided. The claim will be reopened if the information previously requested is submitted within one year after the date of this denial notice.'),
(757,'4/16/2024, 08:59:28',1,'Alert The claim information has been forwarded to a Consumer Spending Account processor for review; for example, flexible spending account or health savings account.','N367','Alert The claim information has been forwarded to a Consumer Spending Account processor for review; for example, flexible spending account or health savings account.'),
(758,'4/16/2024, 08:59:28',1,'You must appeal the determination of the previously adjudicated claim.','N368','You must appeal the determination of the previously adjudicated claim.'),
(759,'4/16/2024, 08:59:28',1,'Alert Although this claim has been processed, it is deficient according to state legislation/regulation.','N369','Alert Although this claim has been processed, it is deficient according to state legislation/regulation.'),
(760,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid tooth number/letter.','N37','Missing/incomplete/invalid tooth number/letter.'),
(761,'4/16/2024, 08:59:28',1,'Billing exceeds the rental months covered/approved by the payer.','N370','Billing exceeds the rental months covered/approved by the payer.'),
(762,'4/16/2024, 08:59:28',1,'Alert title of this equipment must be transferred to the patient.','N371','Alert title of this equipment must be transferred to the patient.'),
(763,'4/16/2024, 08:59:28',1,'Only reasonable and necessary maintenance/service charges are covered.','N372','Only reasonable and necessary maintenance/service charges are covered.'),
(764,'4/16/2024, 08:59:28',1,'It has been determined that another payer paid the services as primary when they were not the primary payer. Therefore, we are refunding to the payer that paid as primary on your behalf.','N373','It has been determined that another payer paid the services as primary when they were not the primary payer. Therefore, we are refunding to the payer that paid as primary on your behalf.'),
(765,'4/16/2024, 08:59:28',1,'Primary Medicare Part A insurance has been exhausted and a Part B Remittance Advice is required.','N374','Primary Medicare Part A insurance has been exhausted and a Part B Remittance Advice is required.'),
(766,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid questionnaire/information required to determine dependent eligibility.','N375','Missing/incomplete/invalid questionnaire/information required to determine dependent eligibility.'),
(767,'4/16/2024, 08:59:28',1,'Subscriber/patient is assigned to active military duty, therefore primary coverage may be TRICARE.','N376','Subscriber/patient is assigned to active military duty, therefore primary coverage may be TRICARE.'),
(768,'4/16/2024, 08:59:28',1,'Payment based on a processed replacement claim.','N377','Payment based on a processed replacement claim.'),
(769,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prescription quantity.','N378','Missing/incomplete/invalid prescription quantity.'),
(770,'4/16/2024, 08:59:28',1,'Claim level information does not match line level information.','N379','Claim level information does not match line level information.'),
(771,'4/16/2024, 08:59:28',1,'The original claim  has been processed, submit a corrected claim.','N380','The original claim  has been processed, submit a corrected claim.'),
(772,'4/16/2024, 08:59:28',1,'Consult our contractual agreement for restrictions/billing/payment information related to these charges.','N381','Consult our contractual agreement for restrictions/billing/payment information related to these charges.'),
(773,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient identifier.','N382','Missing/incomplete/invalid patient identifier.'),
(774,'4/16/2024, 08:59:28',1,'Not covered when deemed cosmetic.','N383','Not covered when deemed cosmetic.'),
(775,'4/16/2024, 08:59:28',1,'Records indicate that the referenced body part/tooth has been removed in a previous procedure.','N384','Records indicate that the referenced body part/tooth has been removed in a previous procedure.'),
(776,'4/16/2024, 08:59:28',1,'Notification of admission was not timely according to published plan procedures.','N385','Notification of admission was not timely according to published plan procedures.'),
(777,'4/16/2024, 08:59:28',1,'This decision was based on a National Coverage Determination (NCD). An NCD provides a coverage determination as to whether a particular item or service is covered. A copy of this policy is available at www.cms.gov/mcd/search.asp. If you do not have w','N386','This decision was based on a National Coverage Determination (NCD). An NCD provides a coverage determination as to whether a particular item or service is covered. A copy of this policy is available at www.cms.gov/mcd/search.asp. If you do not have w'),
(778,'4/16/2024, 08:59:28',1,'Alert Submit this claim to the patient''s other insurer for potential payment of supplemental benefits. We did not forward the claim information.','N387','Alert Submit this claim to the patient''s other insurer for potential payment of supplemental benefits. We did not forward the claim information.'),
(779,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prescription number','N388','Missing/incomplete/invalid prescription number'),
(780,'4/16/2024, 08:59:28',1,'Duplicate prescription number submitted.','N389','Duplicate prescription number submitted.'),
(781,'4/16/2024, 08:59:28',1,'Procedure code is not compatible with tooth number/letter.','N39','Procedure code is not compatible with tooth number/letter.'),
(782,'4/16/2024, 08:59:28',1,'This service/report cannot be billed separately.','N390','This service/report cannot be billed separately.'),
(783,'4/16/2024, 08:59:28',1,'Missing emergency department records.','N391','Missing emergency department records.'),
(784,'4/16/2024, 08:59:28',1,'Incomplete/invalid emergency department records.','N392','Incomplete/invalid emergency department records.'),
(785,'4/16/2024, 08:59:28',1,'Missing progress notes/report.','N393','Missing progress notes/report.'),
(786,'4/16/2024, 08:59:28',1,'Incomplete/invalid progress notes/report.','N394','Incomplete/invalid progress notes/report.'),
(787,'4/16/2024, 08:59:28',1,'Missing laboratory report.','N395','Missing laboratory report.'),
(788,'4/16/2024, 08:59:28',1,'Incomplete/invalid laboratory report.','N396','Incomplete/invalid laboratory report.'),
(789,'4/16/2024, 08:59:28',1,'Benefits are not available for incomplete service(s)/undelivered item(s).','N397','Benefits are not available for incomplete service(s)/undelivered item(s).'),
(790,'4/16/2024, 08:59:28',1,'Missing elective consent form.','N398','Missing elective consent form.'),
(791,'4/16/2024, 08:59:28',1,'Incomplete/invalid elective consent form.','N399','Incomplete/invalid elective consent form.'),
(792,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prior insurance carrier EOB.','N4','Missing/incomplete/invalid prior insurance carrier EOB.'),
(793,'4/16/2024, 08:59:28',1,'Missing radiology film(s)/image(s).','N40','Missing radiology film(s)/image(s).'),
(794,'4/16/2024, 08:59:28',1,'Alert Electronically enabled providers should submit claims electronically.','N400','Alert Electronically enabled providers should submit claims electronically.'),
(795,'4/16/2024, 08:59:28',1,'Missing periodontal charting.','N401','Missing periodontal charting.'),
(796,'4/16/2024, 08:59:28',1,'Incomplete/invalid periodontal charting.','N402','Incomplete/invalid periodontal charting.'),
(797,'4/16/2024, 08:59:28',1,'Missing facility certification.','N403','Missing facility certification.'),
(798,'4/16/2024, 08:59:28',1,'Incomplete/invalid facility certification.','N404','Incomplete/invalid facility certification.'),
(799,'4/16/2024, 08:59:28',1,'This service is only covered when the donor''s insurer(s) do not provide coverage for the service.','N405','This service is only covered when the donor''s insurer(s) do not provide coverage for the service.'),
(800,'4/16/2024, 08:59:28',1,'This service is only covered when the recipient''s insurer(s) do not provide coverage for the service.','N406','This service is only covered when the recipient''s insurer(s) do not provide coverage for the service.'),
(801,'4/16/2024, 08:59:28',1,'You are not an approved submitter for this transmission format.','N407','You are not an approved submitter for this transmission format.'),
(802,'4/16/2024, 08:59:28',1,'This payer does not cover deductibles assessed by a previous payer.','N408','This payer does not cover deductibles assessed by a previous payer.'),
(803,'4/16/2024, 08:59:28',1,'This service is related to an accidental injury and is not covered unless provided within a specific time frame from the date of the accident.','N409','This service is related to an accidental injury and is not covered unless provided within a specific time frame from the date of the accident.'),
(804,'4/16/2024, 08:59:28',1,'Not covered unless the prescription changes.','N410','Not covered unless the prescription changes.'),
(805,'4/16/2024, 08:59:28',1,'Misrouted claim.  See the payer''s claim submission instructions.','N418','Misrouted claim.  See the payer''s claim submission instructions.'),
(806,'4/16/2024, 08:59:28',1,'Claim payment was the result of a payer''s retroactive adjustment due to a retroactive rate change.','N419','Claim payment was the result of a payer''s retroactive adjustment due to a retroactive rate change.'),
(807,'4/16/2024, 08:59:28',1,'Missing mental health assessment.','N42','Missing mental health assessment.'),
(808,'4/16/2024, 08:59:28',1,'Claim payment was the result of a payer''s retroactive adjustment due to a Coordination of Benefits or Third Party Liability Recovery.','N420','Claim payment was the result of a payer''s retroactive adjustment due to a Coordination of Benefits or Third Party Liability Recovery.'),
(809,'4/16/2024, 08:59:28',1,'Claim payment was the result of a payer''s retroactive adjustment due to a review organization decision.','N421','Claim payment was the result of a payer''s retroactive adjustment due to a review organization decision.'),
(810,'4/16/2024, 08:59:28',1,'Claim payment was the result of a payer''s retroactive adjustment due to a payer''s contract incentive program.','N422','Claim payment was the result of a payer''s retroactive adjustment due to a payer''s contract incentive program.'),
(811,'4/16/2024, 08:59:28',1,'Claim payment was the result of a payer''s retroactive adjustment due to a non standard program.','N423','Claim payment was the result of a payer''s retroactive adjustment due to a non standard program.'),
(812,'4/16/2024, 08:59:28',1,'Patient does not reside in the geographic area required for this type of payment.','N424','Patient does not reside in the geographic area required for this type of payment.'),
(813,'4/16/2024, 08:59:28',1,'Statutorily excluded service(s).','N425','Statutorily excluded service(s).'),
(814,'4/16/2024, 08:59:28',1,'No coverage when self-administered.','N426','No coverage when self-administered.'),
(815,'4/16/2024, 08:59:28',1,'Payment for eyeglasses or contact lenses can be made only after cataract surgery.','N427','Payment for eyeglasses or contact lenses can be made only after cataract surgery.'),
(816,'4/16/2024, 08:59:28',1,'Not covered when performed in this place of service.','N428','Not covered when performed in this place of service.'),
(817,'4/16/2024, 08:59:28',1,'Not covered when considered routine.','N429','Not covered when considered routine.'),
(818,'4/16/2024, 08:59:28',1,'Bed hold or leave days exceeded.','N43','Bed hold or leave days exceeded.'),
(819,'4/16/2024, 08:59:28',1,'Procedure code is inconsistent with the units billed.','N430','Procedure code is inconsistent with the units billed.'),
(820,'4/16/2024, 08:59:28',1,'Not covered with this procedure.','N431','Not covered with this procedure.'),
(821,'4/16/2024, 08:59:28',1,'Adjustment based on a Recovery Audit.','N432','Adjustment based on a Recovery Audit.'),
(822,'4/16/2024, 08:59:28',1,'Resubmit this claim using only your National Provider Identifier (NPI)','N433','Resubmit this claim using only your National Provider Identifier (NPI)'),
(823,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid Present on Admission indicator.','N434','Missing/Incomplete/Invalid Present on Admission indicator.'),
(824,'4/16/2024, 08:59:28',1,'Exceeds number/frequency approved /allowed within time period without support documentation.','N435','Exceeds number/frequency approved /allowed within time period without support documentation.'),
(825,'4/16/2024, 08:59:28',1,'The injury claim has not been accepted and a mandatory medical reimbursement has been made.','N436','The injury claim has not been accepted and a mandatory medical reimbursement has been made.'),
(826,'4/16/2024, 08:59:28',1,'Alert If the injury claim is accepted, these charges will be reconsidered.','N437','Alert If the injury claim is accepted, these charges will be reconsidered.'),
(827,'4/16/2024, 08:59:28',1,'This jurisdiction only accepts paper claims','N438','This jurisdiction only accepts paper claims'),
(828,'4/16/2024, 08:59:28',1,'Missing anesthesia physical status report/indicators.','N439','Missing anesthesia physical status report/indicators.'),
(829,'4/16/2024, 08:59:28',1,'Incomplete/invalid anesthesia physical status report/indicators.','N440','Incomplete/invalid anesthesia physical status report/indicators.'),
(830,'4/16/2024, 08:59:28',1,'This missed appointment is not covered.','N441','This missed appointment is not covered.'),
(831,'4/16/2024, 08:59:28',1,'Payment based on an alternate fee schedule.','N442','Payment based on an alternate fee schedule.'),
(832,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid total time or begin/end time.','N443','Missing/incomplete/invalid total time or begin/end time.'),
(833,'4/16/2024, 08:59:28',1,'Alert This facility has not filed the Election for High Cost Outlier form with the Division of Workers'' Compensation.','N444','Alert This facility has not filed the Election for High Cost Outlier form with the Division of Workers'' Compensation.'),
(834,'4/16/2024, 08:59:28',1,'Missing document for actual cost or paid amount.','N445','Missing document for actual cost or paid amount.'),
(835,'4/16/2024, 08:59:28',1,'Incomplete/invalid document for actual cost or paid amount.','N446','Incomplete/invalid document for actual cost or paid amount.'),
(836,'4/16/2024, 08:59:28',1,'Payment is based on a generic equivalent as required documentation was not provided.','N447','Payment is based on a generic equivalent as required documentation was not provided.'),
(837,'4/16/2024, 08:59:28',1,'This drug/service/supply is not included in the fee schedule or contracted/legislated fee arrangement','N448','This drug/service/supply is not included in the fee schedule or contracted/legislated fee arrangement'),
(838,'4/16/2024, 08:59:28',1,'Payment based on a comparable drug/service/supply.','N449','Payment based on a comparable drug/service/supply.'),
(839,'4/16/2024, 08:59:28',1,'Payment based on authorized amount.','N45','Payment based on authorized amount.'),
(840,'4/16/2024, 08:59:28',1,'Covered only when performed by the primary treating physician or the designee.','N450','Covered only when performed by the primary treating physician or the designee.'),
(841,'4/16/2024, 08:59:28',1,'Missing Admission Summary Report.','N451','Missing Admission Summary Report.'),
(842,'4/16/2024, 08:59:28',1,'Incomplete/invalid Admission Summary Report.','N452','Incomplete/invalid Admission Summary Report.'),
(843,'4/16/2024, 08:59:28',1,'Missing Consultation Report.','N453','Missing Consultation Report.'),
(844,'4/16/2024, 08:59:28',1,'Incomplete/invalid Consultation Report.','N454','Incomplete/invalid Consultation Report.'),
(845,'4/16/2024, 08:59:28',1,'Missing Physician Order.','N455','Missing Physician Order.'),
(846,'4/16/2024, 08:59:28',1,'Incomplete/invalid Physician Order.','N456','Incomplete/invalid Physician Order.'),
(847,'4/16/2024, 08:59:28',1,'Missing Diagnostic Report.','N457','Missing Diagnostic Report.'),
(848,'4/16/2024, 08:59:28',1,'Incomplete/invalid Diagnostic Report.','N458','Incomplete/invalid Diagnostic Report.'),
(849,'4/16/2024, 08:59:28',1,'Missing Discharge Summary.','N459','Missing Discharge Summary.'),
(850,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid admission hour.','N46','Missing/incomplete/invalid admission hour.'),
(851,'4/16/2024, 08:59:28',1,'Incomplete/invalid Discharge Summary.','N460','Incomplete/invalid Discharge Summary.'),
(852,'4/16/2024, 08:59:28',1,'Missing Nursing Notes.','N461','Missing Nursing Notes.'),
(853,'4/16/2024, 08:59:28',1,'Incomplete/invalid Nursing Notes.','N462','Incomplete/invalid Nursing Notes.'),
(854,'4/16/2024, 08:59:28',1,'Missing support data for claim.','N463','Missing support data for claim.'),
(855,'4/16/2024, 08:59:28',1,'Incomplete/invalid support data for claim.','N464','Incomplete/invalid support data for claim.'),
(856,'4/16/2024, 08:59:28',1,'Missing Physical Therapy Notes/Report.','N465','Missing Physical Therapy Notes/Report.'),
(857,'4/16/2024, 08:59:28',1,'Incomplete/invalid Physical Therapy Notes/Report.','N466','Incomplete/invalid Physical Therapy Notes/Report.'),
(858,'4/16/2024, 08:59:28',1,'Missing Report of Tests and Analysis Report.','N467','Missing Report of Tests and Analysis Report.'),
(859,'4/16/2024, 08:59:28',1,'Incomplete/invalid Report of Tests and Analysis Report.','N468','Incomplete/invalid Report of Tests and Analysis Report.'),
(860,'4/16/2024, 08:59:28',1,'Alert Claim/Service(s) subject to appeal process, see section 935 of Medicare Prescription Drug, Improvement, and Modernization Act of 2003 (MMA).','N469','Alert Claim/Service(s) subject to appeal process, see section 935 of Medicare Prescription Drug, Improvement, and Modernization Act of 2003 (MMA).'),
(861,'4/16/2024, 08:59:28',1,'Claim conflicts with another inpatient stay.','N47','Claim conflicts with another inpatient stay.'),
(862,'4/16/2024, 08:59:28',1,'This payment will complete the mandatory medical reimbursement limit.','N470','This payment will complete the mandatory medical reimbursement limit.'),
(863,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid HIPPS Rate Code.','N471','Missing/incomplete/invalid HIPPS Rate Code.'),
(864,'4/16/2024, 08:59:28',1,'Payment for this service has been issued to another provider.','N472','Payment for this service has been issued to another provider.'),
(865,'4/16/2024, 08:59:28',1,'Missing certification.','N473','Missing certification.'),
(866,'4/16/2024, 08:59:28',1,'Incomplete/invalid certification','N474','Incomplete/invalid certification'),
(867,'4/16/2024, 08:59:28',1,'Missing completed referral form.','N475','Missing completed referral form.'),
(868,'4/16/2024, 08:59:28',1,'Incomplete/invalid completed referral form','N476','Incomplete/invalid completed referral form'),
(869,'4/16/2024, 08:59:28',1,'Missing Dental Models.','N477','Missing Dental Models.'),
(870,'4/16/2024, 08:59:28',1,'Incomplete/invalid Dental Models','N478','Incomplete/invalid Dental Models'),
(871,'4/16/2024, 08:59:28',1,'Missing Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payer).','N479','Missing Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payer).'),
(872,'4/16/2024, 08:59:28',1,'Claim information does not agree with information received from other insurance carrier.','N48','Claim information does not agree with information received from other insurance carrier.'),
(873,'4/16/2024, 08:59:28',1,'Incomplete/invalid Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payer).','N480','Incomplete/invalid Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payer).'),
(874,'4/16/2024, 08:59:28',1,'Missing Models.','N481','Missing Models.'),
(875,'4/16/2024, 08:59:28',1,'Incomplete/invalid Models','N482','Incomplete/invalid Models'),
(876,'4/16/2024, 08:59:28',1,'Missing Physical Therapy Certification.','N485','Missing Physical Therapy Certification.'),
(877,'4/16/2024, 08:59:28',1,'Incomplete/invalid Physical Therapy Certification.','N486','Incomplete/invalid Physical Therapy Certification.'),
(878,'4/16/2024, 08:59:28',1,'Missing Prosthetics or Orthotics Certification.','N487','Missing Prosthetics or Orthotics Certification.'),
(879,'4/16/2024, 08:59:28',1,'Incomplete/invalid Prosthetics or Orthotics Certification','N488','Incomplete/invalid Prosthetics or Orthotics Certification'),
(880,'4/16/2024, 08:59:28',1,'Missing referral form.','N489','Missing referral form.'),
(881,'4/16/2024, 08:59:28',1,'Court ordered coverage information needs validation.','N49','Court ordered coverage information needs validation.'),
(882,'4/16/2024, 08:59:28',1,'Incomplete/invalid referral form','N490','Incomplete/invalid referral form'),
(883,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid Exclusionary Rider Condition.','N491','Missing/Incomplete/Invalid Exclusionary Rider Condition.'),
(884,'4/16/2024, 08:59:28',1,'Alert A network provider may bill the member for this service if the member requested the service and agreed in writing, prior to receiving the service, to be financially responsible for the billed charge.','N492','Alert A network provider may bill the member for this service if the member requested the service and agreed in writing, prior to receiving the service, to be financially responsible for the billed charge.'),
(885,'4/16/2024, 08:59:28',1,'Missing Doctor First Report of Injury.','N493','Missing Doctor First Report of Injury.'),
(886,'4/16/2024, 08:59:28',1,'Incomplete/invalid Doctor First Report of Injury.','N494','Incomplete/invalid Doctor First Report of Injury.'),
(887,'4/16/2024, 08:59:28',1,'Missing Supplemental Medical Report.','N495','Missing Supplemental Medical Report.'),
(888,'4/16/2024, 08:59:28',1,'Incomplete/invalid Supplemental Medical Report.','N496','Incomplete/invalid Supplemental Medical Report.'),
(889,'4/16/2024, 08:59:28',1,'Missing Medical Permanent Impairment or Disability Report.','N497','Missing Medical Permanent Impairment or Disability Report.'),
(890,'4/16/2024, 08:59:28',1,'Incomplete/invalid Medical Permanent Impairment or Disability Report.','N498','Incomplete/invalid Medical Permanent Impairment or Disability Report.'),
(891,'4/16/2024, 08:59:28',1,'Missing Medical Legal Report.','N499','Missing Medical Legal Report.'),
(892,'4/16/2024, 08:59:28',1,'EOB received from previous payer.  Claim not on file.','N5','EOB received from previous payer.  Claim not on file.'),
(893,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid discharge information.','N50','Missing/incomplete/invalid discharge information.'),
(894,'4/16/2024, 08:59:28',1,'Incomplete/invalid Medical Legal Report.','N500','Incomplete/invalid Medical Legal Report.'),
(895,'4/16/2024, 08:59:28',1,'Missing Vocational Report.','N501','Missing Vocational Report.'),
(896,'4/16/2024, 08:59:28',1,'Incomplete/invalid Vocational Report.','N502','Incomplete/invalid Vocational Report.'),
(897,'4/16/2024, 08:59:28',1,'Missing Work Status Report.','N503','Missing Work Status Report.'),
(898,'4/16/2024, 08:59:28',1,'Incomplete/invalid Work Status Report.','N504','Incomplete/invalid Work Status Report.'),
(899,'4/16/2024, 08:59:28',1,'Alert This response includes only services that could be estimated in real time. No estimate will be provided for the services that could not be estimated in real time.','N505','Alert This response includes only services that could be estimated in real time. No estimate will be provided for the services that could not be estimated in real time.'),
(900,'4/16/2024, 08:59:28',1,'Alert This is an estimate of the member''s liability based on the information available at the time the estimate was processed. Actual coverage and member liability amounts will be determined when the claim is processed. This is not a pre-authorization','N506','Alert This is an estimate of the member''s liability based on the information available at the time the estimate was processed. Actual coverage and member liability amounts will be determined when the claim is processed. This is not a pre-authorization'),
(901,'4/16/2024, 08:59:28',1,'Plan distance requirements have not been met.','N507','Plan distance requirements have not been met.'),
(902,'4/16/2024, 08:59:28',1,'Alert This real time claim adjudication response represents the member responsibility to the provider for services reported. The member will receive an Explanation of Benefits electronically or in the mail.  Contact the insurer if there are any ques','N508','Alert This real time claim adjudication response represents the member responsibility to the provider for services reported. The member will receive an Explanation of Benefits electronically or in the mail.  Contact the insurer if there are any ques'),
(903,'4/16/2024, 08:59:28',1,'Alert A current inquiry shows the member''s Consumer Spending Account contains sufficient funds to cover the member liability for this claim/service.  Actual payment from the Consumer Spending Account will depend on the availability of funds and dete','N509','Alert A current inquiry shows the member''s Consumer Spending Account contains sufficient funds to cover the member liability for this claim/service.  Actual payment from the Consumer Spending Account will depend on the availability of funds and dete'),
(904,'4/16/2024, 08:59:28',1,'Electronic interchange agreement not on file for provider/submitter.','N51','Electronic interchange agreement not on file for provider/submitter.'),
(905,'4/16/2024, 08:59:28',1,'Alert A current inquiry shows the member''s Consumer Spending Account does not contain sufficient funds to cover the member''s liability for this claim/service. Actual payment from the Consumer Spending Account will depend on the availability of fund','N510','Alert A current inquiry shows the member''s Consumer Spending Account does not contain sufficient funds to cover the member''s liability for this claim/service. Actual payment from the Consumer Spending Account will depend on the availability of fund'),
(906,'4/16/2024, 08:59:28',1,'Alert Information on the availability of Consumer Spending Account funds to cover the member liability on this claim/service is not available at this time.','N511','Alert Information on the availability of Consumer Spending Account funds to cover the member liability on this claim/service is not available at this time.'),
(907,'4/16/2024, 08:59:28',1,'Alert This is the initial remit of a non-NCPDP claim originally submitted real-time without change to the adjudication.','N512','Alert This is the initial remit of a non-NCPDP claim originally submitted real-time without change to the adjudication.'),
(908,'4/16/2024, 08:59:28',1,'Alert This is the initial remit of a non-NCPDP claim originally submitted real-time with a change to the adjudication.','N513','Alert This is the initial remit of a non-NCPDP claim originally submitted real-time with a change to the adjudication.'),
(909,'4/16/2024, 08:59:28',1,'Records indicate a mismatch between the submitted NPI and EIN.','N516','Records indicate a mismatch between the submitted NPI and EIN.'),
(910,'4/16/2024, 08:59:28',1,'Resubmit a new claim with the requested information.','N517','Resubmit a new claim with the requested information.'),
(911,'4/16/2024, 08:59:28',1,'No separate payment for accessories when furnished for use with oxygen equipment.','N518','No separate payment for accessories when furnished for use with oxygen equipment.'),
(912,'4/16/2024, 08:59:28',1,'Invalid combination of HCPCS modifiers.','N519','Invalid combination of HCPCS modifiers.'),
(913,'4/16/2024, 08:59:28',1,'Patient not enrolled in the billing provider''s managed care plan on the date of service.','N52','Patient not enrolled in the billing provider''s managed care plan on the date of service.'),
(914,'4/16/2024, 08:59:28',1,'Alert Payment made from a Consumer Spending Account.','N520','Alert Payment made from a Consumer Spending Account.'),
(915,'4/16/2024, 08:59:28',1,'Mismatch between the submitted provider information and the provider information stored in our system.','N521','Mismatch between the submitted provider information and the provider information stored in our system.'),
(916,'4/16/2024, 08:59:28',1,'Duplicate of a claim processed, or to be processed, as a crossover claim.','N522','Duplicate of a claim processed, or to be processed, as a crossover claim.'),
(917,'4/16/2024, 08:59:28',1,'The limitation on outlier payments defined by this payer for this service period has been met. The outlier payment otherwise applicable to this claim has not been paid.','N523','The limitation on outlier payments defined by this payer for this service period has been met. The outlier payment otherwise applicable to this claim has not been paid.'),
(918,'4/16/2024, 08:59:28',1,'Based on policy this payment constitutes payment in full.','N524','Based on policy this payment constitutes payment in full.'),
(919,'4/16/2024, 08:59:28',1,'These services are not covered when performed within the global period of another service.','N525','These services are not covered when performed within the global period of another service.'),
(920,'4/16/2024, 08:59:28',1,'Not qualified for recovery based on employer size.','N526','Not qualified for recovery based on employer size.'),
(921,'4/16/2024, 08:59:28',1,'We processed this claim as the primary payer prior to receiving the recovery demand.','N527','We processed this claim as the primary payer prior to receiving the recovery demand.'),
(922,'4/16/2024, 08:59:28',1,'Patient is entitled to benefits for Institutional Services.','N528','Patient is entitled to benefits for Institutional Services.'),
(923,'4/16/2024, 08:59:28',1,'Patient is entitled to benefits for Professional Services.','N529','Patient is entitled to benefits for Professional Services.'),
(924,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid point of pick-up address.','N53','Missing/incomplete/invalid point of pick-up address.'),
(925,'4/16/2024, 08:59:28',1,'Our records indicate a mismatch in enrollment information for this patient.','N530','Our records indicate a mismatch in enrollment information for this patient.'),
(926,'4/16/2024, 08:59:28',1,'Not qualified for recovery based on direct payment of premium.','N531','Not qualified for recovery based on direct payment of premium.'),
(927,'4/16/2024, 08:59:28',1,'Not qualified for recovery based on disability and working status.','N532','Not qualified for recovery based on disability and working status.'),
(928,'4/16/2024, 08:59:28',1,'Services performed in an Indian Health Services facility under a self-insured tribal Group Health Plan.','N533','Services performed in an Indian Health Services facility under a self-insured tribal Group Health Plan.'),
(929,'4/16/2024, 08:59:28',1,'This is an individual policy, the employer does not participate in plan sponsorship.','N534','This is an individual policy, the employer does not participate in plan sponsorship.'),
(930,'4/16/2024, 08:59:28',1,'Payment is adjusted when procedure is performed in this place of service based on the submitted procedure code and place of service.','N535','Payment is adjusted when procedure is performed in this place of service based on the submitted procedure code and place of service.'),
(931,'4/16/2024, 08:59:28',1,'We are not changing the prior payer''s determination of patient responsibility, which you may collect, as this service is not covered by us.','N536','We are not changing the prior payer''s determination of patient responsibility, which you may collect, as this service is not covered by us.'),
(932,'4/16/2024, 08:59:28',1,'We have examined claims history and no records of the services have been found.','N537','We have examined claims history and no records of the services have been found.'),
(933,'4/16/2024, 08:59:28',1,'A facility is responsible for payment to outside providers who furnish these services/supplies/drugs to its patients/residents.','N538','A facility is responsible for payment to outside providers who furnish these services/supplies/drugs to its patients/residents.'),
(934,'4/16/2024, 08:59:28',1,'Alert We processed appeals/waiver requests on your behalf and that request has been denied.','N539','Alert We processed appeals/waiver requests on your behalf and that request has been denied.'),
(935,'4/16/2024, 08:59:28',1,'Claim information is inconsistent with pre-certified/authorized services.','N54','Claim information is inconsistent with pre-certified/authorized services.'),
(936,'4/16/2024, 08:59:28',1,'Payment adjusted based on the interrupted stay policy.','N540','Payment adjusted based on the interrupted stay policy.'),
(937,'4/16/2024, 08:59:28',1,'Mismatch between the submitted insurance type code and the information stored in our system.','N541','Mismatch between the submitted insurance type code and the information stored in our system.'),
(938,'4/16/2024, 08:59:28',1,'Missing income verification.','N542','Missing income verification.'),
(939,'4/16/2024, 08:59:28',1,'Incomplete/invalid income verification','N543','Incomplete/invalid income verification'),
(940,'4/16/2024, 08:59:28',1,'Alert Although this was paid, you have billed with a referring/ordering provider that does not match our system record. Unless corrected, this will not be paid in the future.','N544','Alert Although this was paid, you have billed with a referring/ordering provider that does not match our system record. Unless corrected, this will not be paid in the future.'),
(941,'4/16/2024, 08:59:28',1,'Payment reduced based on status as an unsuccessful eprescriber per the Electronic Prescribing (eRx) Incentive Program.','N545','Payment reduced based on status as an unsuccessful eprescriber per the Electronic Prescribing (eRx) Incentive Program.'),
(942,'4/16/2024, 08:59:28',1,'Payment represents a previous reduction based on the Electronic Prescribing (eRx) Incentive Program.','N546','Payment represents a previous reduction based on the Electronic Prescribing (eRx) Incentive Program.'),
(943,'4/16/2024, 08:59:28',1,'A refund request (Frequency Type Code 8) was processed previously.','N547','A refund request (Frequency Type Code 8) was processed previously.'),
(944,'4/16/2024, 08:59:28',1,'Alert Patient''s calendar year deductible has been met.','N548','Alert Patient''s calendar year deductible has been met.'),
(945,'4/16/2024, 08:59:28',1,'Alert Patient''s calendar year out-of-pocket maximum has been met.','N549','Alert Patient''s calendar year out-of-pocket maximum has been met.'),
(946,'4/16/2024, 08:59:28',1,'Procedures for billing with group/referring/performing providers were not followed.','N55','Procedures for billing with group/referring/performing providers were not followed.'),
(947,'4/16/2024, 08:59:28',1,'Alert You have not responded to requests to revalidate your provider/supplier enrollment information.','N550','Alert You have not responded to requests to revalidate your provider/supplier enrollment information.'),
(948,'4/16/2024, 08:59:28',1,'Payment adjusted based on the Ambulatory Surgical Center (ASC) Quality Reporting Program.','N551','Payment adjusted based on the Ambulatory Surgical Center (ASC) Quality Reporting Program.'),
(949,'4/16/2024, 08:59:28',1,'Payment adjusted to reverse a previous withhold/bonus amount.','N552','Payment adjusted to reverse a previous withhold/bonus amount.'),
(950,'4/16/2024, 08:59:28',1,'Payment adjusted based on a Low Income Subsidy (LIS) retroactive coverage or status change.','N553','Payment adjusted based on a Low Income Subsidy (LIS) retroactive coverage or status change.'),
(951,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid Family Planning Indicator.','N554','Missing/Incomplete/Invalid Family Planning Indicator.'),
(952,'4/16/2024, 08:59:28',1,'Missing medication list.','N555','Missing medication list.'),
(953,'4/16/2024, 08:59:28',1,'Incomplete/invalid medication list.','N556','Incomplete/invalid medication list.'),
(954,'4/16/2024, 08:59:28',1,'This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the specimen was collected.','N557','This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the specimen was collected.'),
(955,'4/16/2024, 08:59:28',1,'This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the equipment was received.','N558','This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the equipment was received.'),
(956,'4/16/2024, 08:59:28',1,'This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the Ordering Physician is located.','N559','This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the Ordering Physician is located.'),
(957,'4/16/2024, 08:59:28',1,'Procedure code billed is not correct/valid for the services billed or the date of service billed.','N56','Procedure code billed is not correct/valid for the services billed or the date of service billed.'),
(958,'4/16/2024, 08:59:28',1,'The pilot program requires an interim or final claim within 60 days of the Notice of Admission. A claim was not received.','N560','The pilot program requires an interim or final claim within 60 days of the Notice of Admission. A claim was not received.'),
(959,'4/16/2024, 08:59:28',1,'The bundled claim originally submitted for this episode of care includes related readmissions. You may resubmit the original claim to receive a corrected payment based on this readmission.','N561','The bundled claim originally submitted for this episode of care includes related readmissions. You may resubmit the original claim to receive a corrected payment based on this readmission.'),
(960,'4/16/2024, 08:59:28',1,'The provider number of your incoming claim does not match the provider number on the processed Notice of Admission (NOA) for this bundled payment.','N562','The provider number of your incoming claim does not match the provider number on the processed Notice of Admission (NOA) for this bundled payment.'),
(961,'4/16/2024, 08:59:28',1,'Missing required provider/supplier issuance of advance patient notice of non-coverage. The patient is not liable for payment for this service.','N563','Missing required provider/supplier issuance of advance patient notice of non-coverage. The patient is not liable for payment for this service.'),
(962,'4/16/2024, 08:59:28',1,'Patient did not meet the inclusion criteria for the demonstration project or pilot program.','N564','Patient did not meet the inclusion criteria for the demonstration project or pilot program.'),
(963,'4/16/2024, 08:59:28',1,'Alert This non-payable reporting code requires a modifier.','N565','Alert This non-payable reporting code requires a modifier.'),
(964,'4/16/2024, 08:59:28',1,'Alert This procedure code requires functional reporting.','N566','Alert This procedure code requires functional reporting.'),
(965,'4/16/2024, 08:59:28',1,'Not covered when considered preventative.','N567','Not covered when considered preventative.'),
(966,'4/16/2024, 08:59:28',1,'Alert Initial payment based on the Notice of Admission (NOA) under the Bundled Payment Model IV initiative.','N568','Alert Initial payment based on the Notice of Admission (NOA) under the Bundled Payment Model IV initiative.'),
(967,'4/16/2024, 08:59:28',1,'Not covered when performed for the reported diagnosis.','N569','Not covered when performed for the reported diagnosis.'),
(968,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prescribing date.','N57','Missing/incomplete/invalid prescribing date.'),
(969,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid credentialing data.','N570','Missing/incomplete/invalid credentialing data.'),
(970,'4/16/2024, 08:59:28',1,'Alert Payment will be issued quarterly by another payer/contractor.','N571','Alert Payment will be issued quarterly by another payer/contractor.'),
(971,'4/16/2024, 08:59:28',1,'This procedure is not payable unless non-payable reporting codes and appropriate modifiers are submitted.','N572','This procedure is not payable unless non-payable reporting codes and appropriate modifiers are submitted.'),
(972,'4/16/2024, 08:59:28',1,'Alert You have been overpaid and must refund the overpayment. The refund will be requested separately by another payer/contractor.','N573','Alert You have been overpaid and must refund the overpayment. The refund will be requested separately by another payer/contractor.'),
(973,'4/16/2024, 08:59:28',1,'Our records indicate the ordering/referring provider is of a type/specialty that cannot order or refer.','N574','Our records indicate the ordering/referring provider is of a type/specialty that cannot order or refer.'),
(974,'4/16/2024, 08:59:28',1,'Mismatch between the submitted ordering/referring provider name and the ordering/referring provider name stored in our records.','N575','Mismatch between the submitted ordering/referring provider name and the ordering/referring provider name stored in our records.'),
(975,'4/16/2024, 08:59:28',1,'Services not related to the specific incident/claim/accident/loss being reported.','N576','Services not related to the specific incident/claim/accident/loss being reported.'),
(976,'4/16/2024, 08:59:28',1,'Personal Injury Protection (PIP) Coverage.','N577','Personal Injury Protection (PIP) Coverage.'),
(977,'4/16/2024, 08:59:28',1,'Coverages do not apply to this loss.','N578','Coverages do not apply to this loss.'),
(978,'4/16/2024, 08:59:28',1,'Medical Payments Coverage (MPC).','N579','Medical Payments Coverage (MPC).'),
(979,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid patient liability amount.','N58','Missing/incomplete/invalid patient liability amount.'),
(980,'4/16/2024, 08:59:28',1,'Determination based on the provisions of the insurance policy.','N580','Determination based on the provisions of the insurance policy.'),
(981,'4/16/2024, 08:59:28',1,'Investigation of coverage eligibility is pending.','N581','Investigation of coverage eligibility is pending.'),
(982,'4/16/2024, 08:59:28',1,'Benefits suspended pending the patient''s cooperation.','N582','Benefits suspended pending the patient''s cooperation.'),
(983,'4/16/2024, 08:59:28',1,'Patient was not an occupant of our insured vehicle and therefore, is not an eligible injured person.','N583','Patient was not an occupant of our insured vehicle and therefore, is not an eligible injured person.'),
(984,'4/16/2024, 08:59:28',1,'Not covered based on the insured''s noncompliance with policy or statutory conditions.','N584','Not covered based on the insured''s noncompliance with policy or statutory conditions.'),
(985,'4/16/2024, 08:59:28',1,'Benefits are no longer available based on a final injury settlement.','N585','Benefits are no longer available based on a final injury settlement.'),
(986,'4/16/2024, 08:59:28',1,'The injured party does not qualify for benefits.','N586','The injured party does not qualify for benefits.'),
(987,'4/16/2024, 08:59:28',1,'Policy benefits have been exhausted.','N587','Policy benefits have been exhausted.'),
(988,'4/16/2024, 08:59:28',1,'The patient has instructed that medical claims/bills are not to be paid.','N588','The patient has instructed that medical claims/bills are not to be paid.'),
(989,'4/16/2024, 08:59:28',1,'Coverage is excluded to any person injured as a result of operating a motor vehicle while in an intoxicated condition or while the ability to operate such a vehicle is impaired by the use of a drug.','N589','Coverage is excluded to any person injured as a result of operating a motor vehicle while in an intoxicated condition or while the ability to operate such a vehicle is impaired by the use of a drug.'),
(990,'4/16/2024, 08:59:28',1,'Please refer to your provider manual for additional program and provider information.','N59','Please refer to your provider manual for additional program and provider information.'),
(991,'4/16/2024, 08:59:28',1,'Missing independent medical exam detailing the cause of injuries sustained and medical necessity of services rendered.','N590','Missing independent medical exam detailing the cause of injuries sustained and medical necessity of services rendered.'),
(992,'4/16/2024, 08:59:28',1,'Payment based on an Independent Medical Examination (IME) or Utilization Review (UR).','N591','Payment based on an Independent Medical Examination (IME) or Utilization Review (UR).'),
(993,'4/16/2024, 08:59:28',1,'Adjusted because this is not the initial prescription or exceeds the amount allowed for the initial prescription.','N592','Adjusted because this is not the initial prescription or exceeds the amount allowed for the initial prescription.'),
(994,'4/16/2024, 08:59:28',1,'Not covered based on failure to attend a scheduled Independent Medical Exam (IME).','N593','Not covered based on failure to attend a scheduled Independent Medical Exam (IME).'),
(995,'4/16/2024, 08:59:28',1,'Records reflect the injured party did not complete an Application for Benefits for this loss.','N594','Records reflect the injured party did not complete an Application for Benefits for this loss.'),
(996,'4/16/2024, 08:59:28',1,'Records reflect the injured party did not complete an Assignment of Benefits for this loss.','N595','Records reflect the injured party did not complete an Assignment of Benefits for this loss.'),
(997,'4/16/2024, 08:59:28',1,'Records reflect the injured party did not complete a Medical Authorization for this loss.','N596','Records reflect the injured party did not complete a Medical Authorization for this loss.'),
(998,'4/16/2024, 08:59:28',1,'Adjusted based on a medical/dental provider''s apportionment of care between related injuries and other unrelated medical/dental conditions/injuries.','N597','Adjusted based on a medical/dental provider''s apportionment of care between related injuries and other unrelated medical/dental conditions/injuries.'),
(999,'4/16/2024, 08:59:28',1,'Health care policy coverage is primary.','N598','Health care policy coverage is primary.'),
(1000,'4/16/2024, 08:59:28',1,'The payment for this service is based upon 200% of the Participating Level of Medicare Part B fee schedule for the locale in which the services were rendered.','N599','The payment for this service is based upon 200% of the Participating Level of Medicare Part B fee schedule for the locale in which the services were rendered.'),
(1001,'4/16/2024, 08:59:28',1,'Under FEHB law (U.S.C. 8904(b)), we cannot pay more for covered care than the amount Medicare would have allowed if the patient were enrolled in Medicare Part A and/or Medicare Part B.','N6','Under FEHB law (U.S.C. 8904(b)), we cannot pay more for covered care than the amount Medicare would have allowed if the patient were enrolled in Medicare Part A and/or Medicare Part B.'),
(1002,'4/16/2024, 08:59:28',1,'Adjusted based on the applicable fee schedule for the region in which the service was rendered.','N600','Adjusted based on the applicable fee schedule for the region in which the service was rendered.'),
(1003,'4/16/2024, 08:59:28',1,'In accordance with Hawaii Administrative Rules, Title 16, Chapter 23 Motor Vehicle Insurance Law payment is recommended based on Medicare Resource Based Relative Value Scale System applicable to Hawaii.','N601','In accordance with Hawaii Administrative Rules, Title 16, Chapter 23 Motor Vehicle Insurance Law payment is recommended based on Medicare Resource Based Relative Value Scale System applicable to Hawaii.'),
(1004,'4/16/2024, 08:59:28',1,'Adjusted based on the Redbook maximum allowance.','N602','Adjusted based on the Redbook maximum allowance.'),
(1005,'4/16/2024, 08:59:28',1,'This fee is calculated according to the New Jersey medical fee schedules for Automobile Personal Injury Protection and Motor Bus Medical Expense Insurance Coverage.','N603','This fee is calculated according to the New Jersey medical fee schedules for Automobile Personal Injury Protection and Motor Bus Medical Expense Insurance Coverage.'),
(1006,'4/16/2024, 08:59:28',1,'In accordance with New York No-Fault Law, Regulation 68, this base fee was calculated according to the New York Workers'' Compensation Board Schedule of Medical Fees, pursuant to Regulation 83 and / or Appendix 17-C of 11 NYCRR.','N604','In accordance with New York No-Fault Law, Regulation 68, this base fee was calculated according to the New York Workers'' Compensation Board Schedule of Medical Fees, pursuant to Regulation 83 and / or Appendix 17-C of 11 NYCRR.'),
(1007,'4/16/2024, 08:59:28',1,'This fee was calculated based upon New York All Patients Refined Diagnosis Related Groups (APR-DRG), pursuant to Regulation 68.','N605','This fee was calculated based upon New York All Patients Refined Diagnosis Related Groups (APR-DRG), pursuant to Regulation 68.'),
(1008,'4/16/2024, 08:59:28',1,'The Oregon allowed amount for this procedure is based upon the Workers Compensation Fee Schedule (OAR 436-009). The allowed amount has been calculated in accordance with Section 4 of ORS 742.524.','N606','The Oregon allowed amount for this procedure is based upon the Workers Compensation Fee Schedule (OAR 436-009). The allowed amount has been calculated in accordance with Section 4 of ORS 742.524.'),
(1009,'4/16/2024, 08:59:28',1,'Service provided for non-compensable condition(s).','N607','Service provided for non-compensable condition(s).'),
(1010,'4/16/2024, 08:59:28',1,'The fee schedule amount allowed is calculated at 110% of the Medicare Fee Schedule for this region, specialty and type of service. This fee is calculated in compliance with Act 6.','N608','The fee schedule amount allowed is calculated at 110% of the Medicare Fee Schedule for this region, specialty and type of service. This fee is calculated in compliance with Act 6.'),
(1011,'4/16/2024, 08:59:28',1,'80% of the provider''s billed amount is being recommended for payment according to Act 6.','N609','80% of the provider''s billed amount is being recommended for payment according to Act 6.'),
(1012,'4/16/2024, 08:59:28',1,'Rebill services on separate claims.','N61','Rebill services on separate claims.'),
(1013,'4/16/2024, 08:59:28',1,'Alert Payment based on an appropriate level of care.','N610','Alert Payment based on an appropriate level of care.'),
(1014,'4/16/2024, 08:59:28',1,'Claim in litigation. Contact the insurer for more information.','N611','Claim in litigation. Contact the insurer for more information.'),
(1015,'4/16/2024, 08:59:28',1,'Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction.','N612','Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction.'),
(1016,'4/16/2024, 08:59:28',1,'Alert Although this was paid, you have billed with an ordering provider that needs to update their enrollment record.','N613','Alert Although this was paid, you have billed with an ordering provider that needs to update their enrollment record.'),
(1017,'4/16/2024, 08:59:28',1,'Alert Additional information is included in the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information).','N614','Alert Additional information is included in the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information).'),
(1018,'4/16/2024, 08:59:28',1,'Alert This enrollee receiving advance payments of the premium tax credit is in the grace period of three consecutive months for non-payment of premium.','N615','Alert This enrollee receiving advance payments of the premium tax credit is in the grace period of three consecutive months for non-payment of premium.'),
(1019,'4/16/2024, 08:59:28',1,'Alert This enrollee is in the first month of the advance premium tax credit grace period.','N616','Alert This enrollee is in the first month of the advance premium tax credit grace period.'),
(1020,'4/16/2024, 08:59:28',1,'This enrollee is in the second or third month of the advance premium tax credit grace period.','N617','This enrollee is in the second or third month of the advance premium tax credit grace period.'),
(1021,'4/16/2024, 08:59:28',1,'Alert This claim will automatically be reprocessed if the enrollee pays their premiums.','N618','Alert This claim will automatically be reprocessed if the enrollee pays their premiums.'),
(1022,'4/16/2024, 08:59:28',1,'Coverage terminated for non-payment of premium.','N619','Coverage terminated for non-payment of premium.'),
(1023,'4/16/2024, 08:59:28',1,'Dates of service span multiple rate periods. Resubmit separate claims.','N62','Dates of service span multiple rate periods. Resubmit separate claims.'),
(1024,'4/16/2024, 08:59:28',1,'Alert This procedure code is for quality reporting/informational purposes only.','N620','Alert This procedure code is for quality reporting/informational purposes only.'),
(1025,'4/16/2024, 08:59:28',1,'Charges for Jurisdiction required forms, reports, or chart notes are not payable.','N621','Charges for Jurisdiction required forms, reports, or chart notes are not payable.'),
(1026,'4/16/2024, 08:59:28',1,'Not covered based on the date of injury/accident.','N622','Not covered based on the date of injury/accident.'),
(1027,'4/16/2024, 08:59:28',1,'Not covered when deemed unscientific/unproven/outmoded/experimental/excessive/inappropriate.','N623','Not covered when deemed unscientific/unproven/outmoded/experimental/excessive/inappropriate.'),
(1028,'4/16/2024, 08:59:28',1,'The associated Workers'' Compensation claim has been withdrawn.','N624','The associated Workers'' Compensation claim has been withdrawn.'),
(1029,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid Workers'' Compensation Claim #.','N625','Missing/Incomplete/Invalid Workers'' Compensation Claim #.'),
(1030,'4/16/2024, 08:59:28',1,'New or established patient E/M codes are not payable with chiropractic care codes.','N626','New or established patient E/M codes are not payable with chiropractic care codes.'),
(1031,'4/16/2024, 08:59:28',1,'Service not payable per managed care contract.','N627','Service not payable per managed care contract.'),
(1032,'4/16/2024, 08:59:28',1,'Out-patient follow up visits on the same date of service as a scheduled test or treatment is disallowed.','N628','Out-patient follow up visits on the same date of service as a scheduled test or treatment is disallowed.'),
(1033,'4/16/2024, 08:59:28',1,'Reviews/documentation/notes/summaries/reports/charts not requested.','N629','Reviews/documentation/notes/summaries/reports/charts not requested.'),
(1034,'4/16/2024, 08:59:28',1,'Rebill services on separate claim lines.','N63','Rebill services on separate claim lines.'),
(1035,'4/16/2024, 08:59:28',1,'Referral not authorized by attending physician.','N630','Referral not authorized by attending physician.'),
(1036,'4/16/2024, 08:59:28',1,'Medical Fee Schedule does not list this code. An allowance was made for a comparable service.','N631','Medical Fee Schedule does not list this code. An allowance was made for a comparable service.'),
(1037,'4/16/2024, 08:59:28',1,'According to the Official Medical Fee Schedule this service has a relative value of zero and therefore no payment is due.','N632','According to the Official Medical Fee Schedule this service has a relative value of zero and therefore no payment is due.'),
(1038,'4/16/2024, 08:59:28',1,'Additional anesthesia time units are not allowed.','N633','Additional anesthesia time units are not allowed.'),
(1039,'4/16/2024, 08:59:28',1,'The allowance is calculated based on anesthesia time units.','N634','The allowance is calculated based on anesthesia time units.'),
(1040,'4/16/2024, 08:59:28',1,'The Allowance is calculated based on the anesthesia base units plus time.','N635','The Allowance is calculated based on the anesthesia base units plus time.'),
(1041,'4/16/2024, 08:59:28',1,'Adjusted because this is reimbursable only once per injury.','N636','Adjusted because this is reimbursable only once per injury.'),
(1042,'4/16/2024, 08:59:28',1,'Consultations are not allowed once treatment has been rendered by the same provider.','N637','Consultations are not allowed once treatment has been rendered by the same provider.'),
(1043,'4/16/2024, 08:59:28',1,'Reimbursement has been made according to the home health fee schedule.','N638','Reimbursement has been made according to the home health fee schedule.'),
(1044,'4/16/2024, 08:59:28',1,'Reimbursement has been made according to the inpatient rehabilitation facilities fee schedule.','N639','Reimbursement has been made according to the inpatient rehabilitation facilities fee schedule.'),
(1045,'4/16/2024, 08:59:28',1,'The “from” and “to” dates must be different.','N64','The “from” and “to” dates must be different.'),
(1046,'4/16/2024, 08:59:28',1,'Exceeds number/frequency approved/allowed within time period.','N640','Exceeds number/frequency approved/allowed within time period.'),
(1047,'4/16/2024, 08:59:28',1,'Reimbursement has been based on the number of body areas rated.','N641','Reimbursement has been based on the number of body areas rated.'),
(1048,'4/16/2024, 08:59:28',1,'Adjusted when billed as individual tests instead of as a panel.','N642','Adjusted when billed as individual tests instead of as a panel.'),
(1049,'4/16/2024, 08:59:28',1,'The services billed are considered Not Covered or Non-Covered (NC) in the applicable state fee schedule.','N643','The services billed are considered Not Covered or Non-Covered (NC) in the applicable state fee schedule.'),
(1050,'4/16/2024, 08:59:28',1,'Reimbursement has been made according to the bilateral procedure rule.','N644','Reimbursement has been made according to the bilateral procedure rule.'),
(1051,'4/16/2024, 08:59:28',1,'Mark-up allowance.','N645','Mark-up allowance.'),
(1052,'4/16/2024, 08:59:28',1,'Reimbursement has been adjusted based on the guidelines for an assistant.','N646','Reimbursement has been adjusted based on the guidelines for an assistant.'),
(1053,'4/16/2024, 08:59:28',1,'Adjusted based on diagnosis-related group (DRG).','N647','Adjusted based on diagnosis-related group (DRG).'),
(1054,'4/16/2024, 08:59:28',1,'Adjusted based on Stop Loss.','N648','Adjusted based on Stop Loss.'),
(1055,'4/16/2024, 08:59:28',1,'Payment based on invoice.','N649','Payment based on invoice.'),
(1056,'4/16/2024, 08:59:28',1,'Procedure code or procedure rate count cannot be determined, or was not on file, for the date of service/provider.','N65','Procedure code or procedure rate count cannot be determined, or was not on file, for the date of service/provider.'),
(1057,'4/16/2024, 08:59:28',1,'This policy was not in effect for this date of loss. No coverage is available.','N650','This policy was not in effect for this date of loss. No coverage is available.'),
(1058,'4/16/2024, 08:59:28',1,'No Personal Injury Protection/Medical Payments Coverage on the policy at the time of the loss.','N651','No Personal Injury Protection/Medical Payments Coverage on the policy at the time of the loss.'),
(1059,'4/16/2024, 08:59:28',1,'The date of service is before the date of loss.','N652','The date of service is before the date of loss.'),
(1060,'4/16/2024, 08:59:28',1,'The date of injury does not match the reported date of loss.','N653','The date of injury does not match the reported date of loss.'),
(1061,'4/16/2024, 08:59:28',1,'Adjusted based on achievement of maximum medical improvement (MMI).','N654','Adjusted based on achievement of maximum medical improvement (MMI).'),
(1062,'4/16/2024, 08:59:28',1,'Payment based on provider''s geographic region.','N655','Payment based on provider''s geographic region.'),
(1063,'4/16/2024, 08:59:28',1,'An interest payment is being made because benefits are being paid outside the statutory requirement.','N656','An interest payment is being made because benefits are being paid outside the statutory requirement.'),
(1064,'4/16/2024, 08:59:28',1,'This should be billed with the appropriate code for these services.','N657','This should be billed with the appropriate code for these services.'),
(1065,'4/16/2024, 08:59:28',1,'The billed service(s) are not considered medical expenses.','N658','The billed service(s) are not considered medical expenses.'),
(1066,'4/16/2024, 08:59:28',1,'This item is exempt from sales tax.','N659','This item is exempt from sales tax.'),
(1067,'4/16/2024, 08:59:28',1,'Sales tax has been included in the reimbursement.','N660','Sales tax has been included in the reimbursement.'),
(1068,'4/16/2024, 08:59:28',1,'Documentation does not support that the services rendered were medically necessary.','N661','Documentation does not support that the services rendered were medically necessary.'),
(1069,'4/16/2024, 08:59:28',1,'Alert Consideration of payment will be made upon receipt of a final bill.','N662','Alert Consideration of payment will be made upon receipt of a final bill.'),
(1070,'4/16/2024, 08:59:28',1,'Adjusted based on an agreed amount.','N663','Adjusted based on an agreed amount.'),
(1071,'4/16/2024, 08:59:28',1,'Adjusted based on a legal settlement.','N664','Adjusted based on a legal settlement.'),
(1072,'4/16/2024, 08:59:28',1,'Services by an unlicensed provider are not reimbursable.','N665','Services by an unlicensed provider are not reimbursable.'),
(1073,'4/16/2024, 08:59:28',1,'Only one evaluation and management code at this service level is covered during the course of care.','N666','Only one evaluation and management code at this service level is covered during the course of care.'),
(1074,'4/16/2024, 08:59:28',1,'Missing prescription.','N667','Missing prescription.'),
(1075,'4/16/2024, 08:59:28',1,'Incomplete/invalid prescription.','N668','Incomplete/invalid prescription.'),
(1076,'4/16/2024, 08:59:28',1,'Adjusted based on the Medicare fee schedule.','N669','Adjusted based on the Medicare fee schedule.'),
(1077,'4/16/2024, 08:59:28',1,'Professional provider services not paid separately. Included in facility payment under a demonstration project.  Apply to that facility for payment, or resubmit your claim if the facility notifies you the patient was excluded from this demonstration','N67','Professional provider services not paid separately. Included in facility payment under a demonstration project.  Apply to that facility for payment, or resubmit your claim if the facility notifies you the patient was excluded from this demonstration'),
(1078,'4/16/2024, 08:59:28',1,'This service code has been identified as the primary procedure code subject to the Medicare Multiple Procedure Payment Reduction (MPPR) rule.','N670','This service code has been identified as the primary procedure code subject to the Medicare Multiple Procedure Payment Reduction (MPPR) rule.'),
(1079,'4/16/2024, 08:59:28',1,'Payment based on a jurisdiction cost-charge ratio.','N671','Payment based on a jurisdiction cost-charge ratio.'),
(1080,'4/16/2024, 08:59:28',1,'Alert Amount applied to Health Insurance Offset.','N672','Alert Amount applied to Health Insurance Offset.'),
(1081,'4/16/2024, 08:59:28',1,'Reimbursement has been calculated based on an outpatient per diem or an outpatient factor and/or fee schedule amount.','N673','Reimbursement has been calculated based on an outpatient per diem or an outpatient factor and/or fee schedule amount.'),
(1082,'4/16/2024, 08:59:28',1,'Not covered unless a prerequisite procedure/service has been provided.','N674','Not covered unless a prerequisite procedure/service has been provided.'),
(1083,'4/16/2024, 08:59:28',1,'Additional information is required from the injured party.','N675','Additional information is required from the injured party.'),
(1084,'4/16/2024, 08:59:28',1,'Service does not qualify for payment under the Outpatient Facility Fee Schedule.','N676','Service does not qualify for payment under the Outpatient Facility Fee Schedule.'),
(1085,'4/16/2024, 08:59:28',1,'Alert Films/Images will not be returned.','N677','Alert Films/Images will not be returned.'),
(1086,'4/16/2024, 08:59:28',1,'Missing post-operative images/visual field results.','N678','Missing post-operative images/visual field results.'),
(1087,'4/16/2024, 08:59:28',1,'Incomplete/Invalid post-operative images/visual field results.','N679','Incomplete/Invalid post-operative images/visual field results.'),
(1088,'4/16/2024, 08:59:28',1,'Prior payment being canceled as we were subsequently notified this patient was covered by a demonstration project in this site of service.  Professional services were included in the payment made to the facility. You must contact the facility for yo','N68','Prior payment being canceled as we were subsequently notified this patient was covered by a demonstration project in this site of service.  Professional services were included in the payment made to the facility. You must contact the facility for yo'),
(1089,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid date of previous dental extractions.','N680','Missing/Incomplete/Invalid date of previous dental extractions.'),
(1090,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid full arch series.','N681','Missing/Incomplete/Invalid full arch series.'),
(1091,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid history of prior periodontal therapy/maintenance.','N682','Missing/Incomplete/Invalid history of prior periodontal therapy/maintenance.'),
(1092,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid prior treatment documentation.','N683','Missing/Incomplete/Invalid prior treatment documentation.'),
(1093,'4/16/2024, 08:59:28',1,'Payment denied as this is a specialty claim submitted as a general claim.','N684','Payment denied as this is a specialty claim submitted as a general claim.'),
(1094,'4/16/2024, 08:59:28',1,'Missing/Incomplete/Invalid Prosthesis, Crown or Inlay Code.','N685','Missing/Incomplete/Invalid Prosthesis, Crown or Inlay Code.'),
(1095,'4/16/2024, 08:59:28',1,'Missing/incomplete/Invalid questionnaire needed to complete payment determination.','N686','Missing/incomplete/Invalid questionnaire needed to complete payment determination.'),
(1096,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a retroactive disenrollment.','N687','Alert This reversal is due to a retroactive disenrollment.'),
(1097,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a medical or utilization review decision.','N688','Alert This reversal is due to a medical or utilization review decision.'),
(1098,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a retroactive rate change.','N689','Alert This reversal is due to a retroactive rate change.'),
(1099,'4/16/2024, 08:59:28',1,'PPS (Prospective Payment System) code changed by claims processing system.  Insufficient visits or therapies.','N69','PPS (Prospective Payment System) code changed by claims processing system.  Insufficient visits or therapies.'),
(1100,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a provider submitted appeal.','N690','Alert This reversal is due to a provider submitted appeal.'),
(1101,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a patient submitted appeal.','N691','Alert This reversal is due to a patient submitted appeal.'),
(1102,'4/16/2024, 08:59:28',1,'Alert This reversal is due to an incorrect rate on the initial adjudication.','N692','Alert This reversal is due to an incorrect rate on the initial adjudication.'),
(1103,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a cancellation of the claim by the provider.','N693','Alert This reversal is due to a cancellation of the claim by the provider.'),
(1104,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a resubmission/change to the claim by the provider.','N694','Alert This reversal is due to a resubmission/change to the claim by the provider.'),
(1105,'4/16/2024, 08:59:28',1,'Alert This reversal is due to incorrect patient financial responsibility information on the initial adjudication.','N695','Alert This reversal is due to incorrect patient financial responsibility information on the initial adjudication.'),
(1106,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a Coordination of Benefits or Third Party Liability Recovery retroactive adjustment.','N696','Alert This reversal is due to a Coordination of Benefits or Third Party Liability Recovery retroactive adjustment.'),
(1107,'4/16/2024, 08:59:28',1,'Alert This reversal is due to a payer''s retroactive contract incentive program adjustment.','N697','Alert This reversal is due to a payer''s retroactive contract incentive program adjustment.'),
(1108,'4/16/2024, 08:59:28',1,'Alert This reversal is due to non-payment of the Health Insurance Exchange premiums by the end of the premium payment grace period, resulting in loss of coverage.','N698','Alert This reversal is due to non-payment of the Health Insurance Exchange premiums by the end of the premium payment grace period, resulting in loss of coverage.'),
(1109,'4/16/2024, 08:59:28',1,'Payment adjusted based on the Physician Quality Reporting System (PQRS) Incentive Program.','N699','Payment adjusted based on the Physician Quality Reporting System (PQRS) Incentive Program.'),
(1110,'4/16/2024, 08:59:28',1,'Processing of this claim/service has included consideration under Major Medical provisions.','N7','Processing of this claim/service has included consideration under Major Medical provisions.'),
(1111,'4/16/2024, 08:59:28',1,'Consolidated billing and payment applies.','N70','Consolidated billing and payment applies.'),
(1112,'4/16/2024, 08:59:28',1,'Payment adjusted based on the Electronic Health Records (EHR) Incentive Program.','N700','Payment adjusted based on the Electronic Health Records (EHR) Incentive Program.'),
(1113,'4/16/2024, 08:59:28',1,'Payment adjusted based on the Value-based Payment Modifier.','N701','Payment adjusted based on the Value-based Payment Modifier.'),
(1114,'4/16/2024, 08:59:28',1,'Decision based on review of previously adjudicated claims or for claims in process for the same/similar type of services.','N702','Decision based on review of previously adjudicated claims or for claims in process for the same/similar type of services.'),
(1115,'4/16/2024, 08:59:28',1,'This service is incompatible with previously adjudicated claims or claims in process.','N703','This service is incompatible with previously adjudicated claims or claims in process.'),
(1116,'4/16/2024, 08:59:28',1,'Alert You may not appeal this decision but can resubmit this claim/service with corrected information if warranted.','N704','Alert You may not appeal this decision but can resubmit this claim/service with corrected information if warranted.'),
(1117,'4/16/2024, 08:59:28',1,'Incomplete/invalid documentation.','N705','Incomplete/invalid documentation.'),
(1118,'4/16/2024, 08:59:28',1,'Missing documentation.','N706','Missing documentation.'),
(1119,'4/16/2024, 08:59:28',1,'Incomplete/invalid orders.','N707','Incomplete/invalid orders.'),
(1120,'4/16/2024, 08:59:28',1,'Missing orders.','N708','Missing orders.'),
(1121,'4/16/2024, 08:59:28',1,'Incomplete/invalid notes.','N709','Incomplete/invalid notes.'),
(1122,'4/16/2024, 08:59:28',1,'Your unassigned claim for a drug or biological, clinical diagnostic laboratory services or ambulance service was processed as an assigned claim. You are required by law to accept assignments for these types of claims.','N71','Your unassigned claim for a drug or biological, clinical diagnostic laboratory services or ambulance service was processed as an assigned claim. You are required by law to accept assignments for these types of claims.'),
(1123,'4/16/2024, 08:59:28',1,'Missing notes.','N710','Missing notes.'),
(1124,'4/16/2024, 08:59:28',1,'Incomplete/invalid summary.','N711','Incomplete/invalid summary.'),
(1125,'4/16/2024, 08:59:28',1,'Missing summary.','N712','Missing summary.'),
(1126,'4/16/2024, 08:59:28',1,'Incomplete/invalid report.','N713','Incomplete/invalid report.'),
(1127,'4/16/2024, 08:59:28',1,'Missing report.','N714','Missing report.'),
(1128,'4/16/2024, 08:59:28',1,'Incomplete/invalid chart.','N715','Incomplete/invalid chart.'),
(1129,'4/16/2024, 08:59:28',1,'Missing chart.','N716','Missing chart.'),
(1130,'4/16/2024, 08:59:28',1,'Incomplete/Invalid documentation of face-to-face examination.','N717','Incomplete/Invalid documentation of face-to-face examination.'),
(1131,'4/16/2024, 08:59:28',1,'Missing documentation of face-to-face examination.','N718','Missing documentation of face-to-face examination.'),
(1132,'4/16/2024, 08:59:28',1,'Penalty applied based on plan requirements not being met.','N719','Penalty applied based on plan requirements not being met.'),
(1133,'4/16/2024, 08:59:28',1,'PPS (Prospective Payment System)  code changed by medical reviewers.  Not supported by clinical records.','N72','PPS (Prospective Payment System)  code changed by medical reviewers.  Not supported by clinical records.'),
(1134,'4/16/2024, 08:59:28',1,'The patient overpaid you. You may need to issue the patient a refund for the difference between the patient''s payment and the amount shown as patient responsibility on this notice.','N720','The patient overpaid you. You may need to issue the patient a refund for the difference between the patient''s payment and the amount shown as patient responsibility on this notice.'),
(1135,'4/16/2024, 08:59:28',1,'This service is only covered when performed as part of a clinical trial.','N721','This service is only covered when performed as part of a clinical trial.'),
(1136,'4/16/2024, 08:59:28',1,'Patient must use Workers'' Compensation Set-Aside (WCSA) funds to pay for the medical service or item.','N722','Patient must use Workers'' Compensation Set-Aside (WCSA) funds to pay for the medical service or item.'),
(1137,'4/16/2024, 08:59:28',1,'Patient must use Liability set-aside (LSA) funds to pay for the medical service or item.','N723','Patient must use Liability set-aside (LSA) funds to pay for the medical service or item.'),
(1138,'4/16/2024, 08:59:28',1,'Patient must use No-Fault set-aside (NFSA) funds to pay for the medical service or item.','N724','Patient must use No-Fault set-aside (NFSA) funds to pay for the medical service or item.'),
(1139,'4/16/2024, 08:59:28',1,'A liability insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.','N725','A liability insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.'),
(1140,'4/16/2024, 08:59:28',1,'A conditional payment is not allowed.','N726','A conditional payment is not allowed.'),
(1141,'4/16/2024, 08:59:28',1,'A no-fault insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.','N727','A no-fault insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.'),
(1142,'4/16/2024, 08:59:28',1,'A workers'' compensation insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.','N728','A workers'' compensation insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.'),
(1143,'4/16/2024, 08:59:28',1,'Missing patient medical/dental record for this service.','N729','Missing patient medical/dental record for this service.'),
(1144,'4/16/2024, 08:59:28',1,'Incomplete/invalid patient medical/dental record for this service.','N730','Incomplete/invalid patient medical/dental record for this service.'),
(1145,'4/16/2024, 08:59:28',1,'Incomplete/Invalid mental health assessment.','N731','Incomplete/Invalid mental health assessment.'),
(1146,'4/16/2024, 08:59:28',1,'Services performed at an unlicensed facility are not reimbursable.','N732','Services performed at an unlicensed facility are not reimbursable.'),
(1147,'4/16/2024, 08:59:28',1,'Regulatory surcharges are paid directly to the state.','N733','Regulatory surcharges are paid directly to the state.'),
(1148,'4/16/2024, 08:59:28',1,'The patient is eligible for these medical services only when unable to work or perform normal activities due to an illness or injury.','N734','The patient is eligible for these medical services only when unable to work or perform normal activities due to an illness or injury.'),
(1149,'4/16/2024, 08:59:28',1,'Resubmit with multiple claims, each claim covering services provided in only one calendar month.','N74','Resubmit with multiple claims, each claim covering services provided in only one calendar month.'),
(1150,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid tooth surface information.','N75','Missing/incomplete/invalid tooth surface information.'),
(1151,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid number of riders.','N76','Missing/incomplete/invalid number of riders.'),
(1152,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid designated provider number.','N77','Missing/incomplete/invalid designated provider number.'),
(1153,'4/16/2024, 08:59:28',1,'The necessary components of the child and teen checkup (EPSDT) were not completed.','N78','The necessary components of the child and teen checkup (EPSDT) were not completed.'),
(1154,'4/16/2024, 08:59:28',1,'Service billed is not compatible with patient location information.','N79','Service billed is not compatible with patient location information.'),
(1155,'4/16/2024, 08:59:28',1,'Crossover claim denied by previous payer and complete claim data not forwarded. Resubmit this claim to this payer to provide adequate data for adjudication.','N8','Crossover claim denied by previous payer and complete claim data not forwarded. Resubmit this claim to this payer to provide adequate data for adjudication.'),
(1156,'4/16/2024, 08:59:28',1,'Missing/incomplete/invalid prenatal screening information.','N80','Missing/incomplete/invalid prenatal screening information.'),
(1157,'4/16/2024, 08:59:28',1,'Procedure billed is not compatible with tooth surface code.','N81','Procedure billed is not compatible with tooth surface code.'),
(1158,'4/16/2024, 08:59:28',1,'Provider must accept insurance payment as payment in full when a third party payer contract specifies full reimbursement.','N82','Provider must accept insurance payment as payment in full when a third party payer contract specifies full reimbursement.'),
(1159,'4/16/2024, 08:59:28',1,'No appeal rights. Adjudicative decision based on the provisions of a demonstration project.','N83','No appeal rights. Adjudicative decision based on the provisions of a demonstration project.'),
(1160,'4/16/2024, 08:59:28',1,'Alert Further installment payments are forthcoming.','N84','Alert Further installment payments are forthcoming.'),
(1161,'4/16/2024, 08:59:28',1,'Alert This is the final installment payment.','N85','Alert This is the final installment payment.'),
(1162,'4/16/2024, 08:59:28',1,'A failed trial of pelvic muscle exercise training is required in order for biofeedback training for the treatment of urinary incontinence to be covered.','N86','A failed trial of pelvic muscle exercise training is required in order for biofeedback training for the treatment of urinary incontinence to be covered.'),
(1163,'4/16/2024, 08:59:28',1,'Home use of biofeedback therapy is not covered.','N87','Home use of biofeedback therapy is not covered.'),
(1164,'4/16/2024, 08:59:28',1,'Alert This payment is being made conditionally.  An HHA episode of care notice has been filed for this patient. When a patient is treated under a HHA episode of care, consolidated billing requires that certain therapy services and supplies, such as','N88','Alert This payment is being made conditionally.  An HHA episode of care notice has been filed for this patient. When a patient is treated under a HHA episode of care, consolidated billing requires that certain therapy services and supplies, such as'),
(1165,'4/16/2024, 08:59:28',1,'Alert Payment information for this claim has been forwarded to more than one other payer, but format limitations permit only one of the secondary payers to be identified in this remittance advice.','N89','Alert Payment information for this claim has been forwarded to more than one other payer, but format limitations permit only one of the secondary payers to be identified in this remittance advice.'),
(1166,'4/16/2024, 08:59:28',1,'Adjustment represents the estimated amount a previous payer may pay.','N9','Adjustment represents the estimated amount a previous payer may pay.'),
(1167,'4/16/2024, 08:59:28',1,'Covered only when performed by the attending physician.','N90','Covered only when performed by the attending physician.'),
(1168,'4/16/2024, 08:59:28',1,'Services not included in the appeal review.','N91','Services not included in the appeal review.'),
(1169,'4/16/2024, 08:59:28',1,'This facility is not certified for digital mammography.','N92','This facility is not certified for digital mammography.'),
(1170,'4/16/2024, 08:59:28',1,'A separate claim must be submitted for each place of service. Services furnished at multiple sites may not be billed in the same claim.','N93','A separate claim must be submitted for each place of service. Services furnished at multiple sites may not be billed in the same claim.'),
(1171,'4/16/2024, 08:59:28',1,'Claim/Service denied because a more specific taxonomy code is required for adjudication.','N94','Claim/Service denied because a more specific taxonomy code is required for adjudication.'),
(1172,'4/16/2024, 08:59:28',1,'This provider type/provider specialty may not bill this service.','N95','This provider type/provider specialty may not bill this service.'),
(1173,'4/16/2024, 08:59:28',1,'Patient must be refractory to conventional therapy (documented behavioral, pharmacologic and/or surgical corrective therapy) and be an appropriate surgical candidate such that implantation with anesthesia can occur.','N96','Patient must be refractory to conventional therapy (documented behavioral, pharmacologic and/or surgical corrective therapy) and be an appropriate surgical candidate such that implantation with anesthesia can occur.'),
(1174,'4/16/2024, 08:59:28',1,'Patients with stress incontinence, urinary obstruction, and specific neurologic diseases (e.g., diabetes with peripheral nerve involvement) which are associated with secondary manifestations of the above three indications are excluded.','N97','Patients with stress incontinence, urinary obstruction, and specific neurologic diseases (e.g., diabetes with peripheral nerve involvement) which are associated with secondary manifestations of the above three indications are excluded.'),
(1175,'4/16/2024, 08:59:28',1,'Patient must have had a successful test stimulation in order to support subsequent implantation. Before a patient is eligible for permanent implantation, he/she must demonstrate a 50 percent or greater improvement through test stimulation. Improvement','N98','Patient must have had a successful test stimulation in order to support subsequent implantation. Before a patient is eligible for permanent implantation, he/she must demonstrate a 50 percent or greater improvement through test stimulation. Improvement'),
(1176,'4/16/2024, 08:59:28',1,'Patient must be able to demonstrate adequate ability to record voiding diary data such that clinical results of the implant procedure can be properly evaluated.','N99','Patient must be able to demonstrate adequate ability to record voiding diary data such that clinical results of the implant procedure can be properly evaluated.'),
(1177,'4/16/2024, 08:59:28',1,'State-mandated Requirement for Property and Casualty, see Claim Payment Remarks Code for specific explanation. To be used for Property and Casualty only.','P1','State-mandated Requirement for Property and Casualty, see Claim Payment Remarks Code for specific explanation. To be used for Property and Casualty only.'),
(1178,'4/16/2024, 08:59:28',1,'Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property and Casualty only.','P10','Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property and Casualty only.'),
(1179,'4/16/2024, 08:59:28',1,'The disposition of the related Property & Casualty claim (injury or illness) is pending due to litigation. To be used for Property and Casualty only. (Use only with Group Code OA)','P11','The disposition of the related Property & Casualty claim (injury or illness) is pending due to litigation. To be used for Property and Casualty only. (Use only with Group Code OA)'),
(1180,'4/16/2024, 08:59:28',1,'Workers'' compensation jurisdiction fee schedule adjustment.  To be used for Workers'' Compensation only.','P12','Workers'' compensation jurisdiction fee schedule adjustment.  To be used for Workers'' Compensation only.'),
(1181,'4/16/2024, 08:59:28',1,'Payment reduced or denied based on workers'' compensation jurisdictional regulations or payment policies, use only if no other code is applicable.','P13','Payment reduced or denied based on workers'' compensation jurisdictional regulations or payment policies, use only if no other code is applicable.'),
(1182,'4/16/2024, 08:59:28',1,'The Benefit for this Service is included in the payment/allowance for another service/procedure that has been performed on the same day.  To be used for Property and Casualty only.','P14','The Benefit for this Service is included in the payment/allowance for another service/procedure that has been performed on the same day.  To be used for Property and Casualty only.'),
(1183,'4/16/2024, 08:59:28',1,'Workers'' Compensation Medical Treatment Guideline Adjustment. To be used for Workers'' Compensation only.','P15','Workers'' Compensation Medical Treatment Guideline Adjustment. To be used for Workers'' Compensation only.'),
(1184,'4/16/2024, 08:59:28',1,'Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction. To be used for Workers'' Compensation only. (Use with Group Code CO or OA)','P16','Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction. To be used for Workers'' Compensation only. (Use with Group Code CO or OA)'),
(1185,'4/16/2024, 08:59:28',1,'Referral not authorized by attending physician per regulatory requirement. To be used for Property and Casualty only','P17','Referral not authorized by attending physician per regulatory requirement. To be used for Property and Casualty only'),
(1186,'4/16/2024, 08:59:28',1,'Procedure is not listed in the jurisdiction fee schedule. An allowance has been made for a comparable service. To be used for Property and Casualty only.','P18','Procedure is not listed in the jurisdiction fee schedule. An allowance has been made for a comparable service. To be used for Property and Casualty only.'),
(1187,'4/16/2024, 08:59:28',1,'Procedure has a relative value of zero in the jurisdiction fee schedule, therefore no payment is due. To be used for Property and Casualty only.','P19','Procedure has a relative value of zero in the jurisdiction fee schedule, therefore no payment is due. To be used for Property and Casualty only.'),
(1188,'4/16/2024, 08:59:28',1,'Not a work related injury/illness and thus not the liability of the workers'' compensation carrier . To be used for Workers'' Compensation only.','P2','Not a work related injury/illness and thus not the liability of the workers'' compensation carrier . To be used for Workers'' Compensation only.'),
(1189,'4/16/2024, 08:59:28',1,'Service not paid under jurisdiction allowed outpatient facility fee schedule. To be used for Property and Casualty only.','P20','Service not paid under jurisdiction allowed outpatient facility fee schedule. To be used for Property and Casualty only.'),
(1190,'4/16/2024, 08:59:28',1,'Payment denied based on Medical Payments Coverage or Personal Injury Protection Benefits jurisdictional regulations or payment policies.  Used for Property and Casualty Auto only.','P21','Payment denied based on Medical Payments Coverage or Personal Injury Protection Benefits jurisdictional regulations or payment policies.  Used for Property and Casualty Auto only.'),
(1191,'4/16/2024, 08:59:28',1,'Payment adjusted based on Medical Payments Coverage or Personal Injury Protection Benefits jurisdictional regulations or payment policies.  Used for Property and Casualty Auto only.','P22','Payment adjusted based on Medical Payments Coverage or Personal Injury Protection Benefits jurisdictional regulations or payment policies.  Used for Property and Casualty Auto only.'),
(1192,'4/16/2024, 08:59:28',1,'Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional fee schedule adjustment.  To be used for Property and Casualty Auto only.','P23','Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional fee schedule adjustment.  To be used for Property and Casualty Auto only.'),
(1193,'4/16/2024, 08:59:28',1,'Workers'' Compensation case settled. Patient is responsible for the amount of this claim/service through WC ''Medicare set aside arrangement'' or other agreement.','P3','Workers'' Compensation case settled. Patient is responsible for the amount of this claim/service through WC ''Medicare set aside arrangement'' or other agreement.'),
(1194,'4/16/2024, 08:59:28',1,'Workers'' Compensation claim adjudicated as non-compensable. This Payer is not liable for claim or service/treatment.  To be used for Workers'' Compensation only','P4','Workers'' Compensation claim adjudicated as non-compensable. This Payer is not liable for claim or service/treatment.  To be used for Workers'' Compensation only'),
(1195,'4/16/2024, 08:59:28',1,'Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. To be used for Property and Casualty only.','P5','Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. To be used for Property and Casualty only.'),
(1196,'4/16/2024, 08:59:28',1,'Based on entitlement to benefits. To be used for Property and Casualty only.','P6','Based on entitlement to benefits. To be used for Property and Casualty only.'),
(1197,'4/16/2024, 08:59:28',1,'The applicable fee schedule/fee database does not contain the billed code.  To be used for Property and Casualty only.','P7','The applicable fee schedule/fee database does not contain the billed code.  To be used for Property and Casualty only.'),
(1198,'4/16/2024, 08:59:28',1,'Claim is under investigation.  To be used for Property and Casualty only.','P8','Claim is under investigation.  To be used for Property and Casualty only.'),
(1199,'4/16/2024, 08:59:28',1,'No available or correlating CPT/HCPCS code to describe this service. To be used for Property and Casualty only.','P9','No available or correlating CPT/HCPCS code to describe this service. To be used for Property and Casualty only.'),
(1200,'4/16/2024, 08:59:28',1,'Workers'' compensation jurisdiction fee schedule adjustment. Note  If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Inform','W1','Workers'' compensation jurisdiction fee schedule adjustment. Note  If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Inform'),
(1201,'4/16/2024, 08:59:28',1,'Payment reduced or denied based on workers'' compensation jurisdictional regulations or payment policies, use only if no other code is applicable.  Note  If adjustment is at the Claim Level, the payer must send and the provider should refer to the 83','W2','Payment reduced or denied based on workers'' compensation jurisdictional regulations or payment policies, use only if no other code is applicable.  Note  If adjustment is at the Claim Level, the payer must send and the provider should refer to the 83'),
(1202,'4/16/2024, 08:59:28',1,'The Benefit for this Service is included in the payment/allowance for another service/procedure that has been performed on the same day.  For use by Property and Casualty only.','W3','The Benefit for this Service is included in the payment/allowance for another service/procedure that has been performed on the same day.  For use by Property and Casualty only.'),
(1203,'4/16/2024, 08:59:28',1,'Workers'' Compensation Medical Treatment Guideline Adjustment.','W4','Workers'' Compensation Medical Treatment Guideline Adjustment.'),
(1204,'4/16/2024, 08:59:28',1,'Payment denied based on Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional regulations or payment policies, use only if no other code is applicable.','Y1','Payment denied based on Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional regulations or payment policies, use only if no other code is applicable.'),
(1205,'4/16/2024, 08:59:28',1,'Payment adjusted based on Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional regulations or payment policies, use only if no other code is applicable.','Y2','Payment adjusted based on Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional regulations or payment policies, use only if no other code is applicable.'),
(1206,'4/16/2024, 08:59:28',1,'Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional fee schedule adjustment.  To be used for P&C Auto only.','Y3','Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional fee schedule adjustment.  To be used for P&C Auto only.')
;
ALTER SEQUENCE "form_list_adjustment_qualifier_id_seq" RESTART WITH 1207;
SET session_replication_role = DEFAULT;
COMMIT;