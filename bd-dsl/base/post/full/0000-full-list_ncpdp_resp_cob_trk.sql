BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_ncpdp_resp_cob_trk;
INSERT INTO form_list_ncpdp_resp_cob_trk ("rank","code","name","id","created_on","created_by","auto_name") VALUES (1,'41','Submit Bill To Other Processor Or Primary Payer',1,'2024-05-29 18:13:27',1,'41 - Submit Bill To Other Processor Or Primary Payer')
,(2,'572','Medigap ID Not Covered',2,'2024-05-29 18:13:27',1,'572 - Medigap ID Not Covered')
,(3,'573','Prescriber Alternate ID Associated State/Province Address Value Not Supported',3,'2024-05-29 18:13:27',1,'573 - Prescriber Alternate ID Associated State/Province Address Value Not Supported')
,(4,'574','Compound Ingredient Modifier Code Not Covered',4,'2024-05-29 18:13:27',1,'574 - Compound Ingredient Modifier Code Not Covered')
,(5,'583','Provider ID Not Covered',5,'2024-05-29 18:13:27',1,'583 - Provider ID Not Covered')
,(6,'584','Purchaser ID Associated State/Province Code Value Not Supported',6,'2024-05-29 18:13:27',1,'584 - Purchaser ID Associated State/Province Code Value Not Supported')
,(7,'587','Carrier ID Not Covered',7,'2024-05-29 18:13:27',1,'587 - Carrier ID Not Covered')
,(8,'589','Patient ID Not Covered',8,'2024-05-29 18:13:27',1,'589 - Patient ID Not Covered')
,(9,'590','Compound Dosage Form Not Covered',9,'2024-05-29 18:13:27',1,'590 - Compound Dosage Form Not Covered')
,(10,'591','Plan ID Not Covered',10,'2024-05-29 18:13:27',1,'591 - Plan ID Not Covered')
,(11,'594','Pay To ID Not Covered',11,'2024-05-29 18:13:27',1,'594 - Pay To ID Not Covered')
,(12,'595','Associated Prescription/Service Provider ID Not Covered',12,'2024-05-29 18:13:27',1,'595 - Associated Prescription/Service Provider ID Not Covered')
,(13,'6G','Coordination Of Benefits/Other Payments Segment Required For Adjudication',13,'2024-05-29 18:13:27',1,'6G - Coordination Of Benefits/Other Payments Segment Required For Adjudication')
,(14,'60','Product/Service Not Covered For Patient Age',14,'2024-05-29 18:13:27',1,'60 - Product/Service Not Covered For Patient Age')
,(15,'61','Product/Service Not Covered For Patient Gender',15,'2024-05-29 18:13:27',1,'61 - Product/Service Not Covered For Patient Gender')
,(16,'63','Product/Service ID Not Covered For Institutionalized Patient',16,'2024-05-29 18:13:27',1,'63 - Product/Service ID Not Covered For Institutionalized Patient')
,(17,'65','Patient Is Not Covered',17,'2024-05-29 18:13:27',1,'65 - Patient Is Not Covered')
,(18,'66','Patient Age Exceeds Maximum Age',18,'2024-05-29 18:13:27',1,'66 - Patient Age Exceeds Maximum Age')
,(19,'67','Date Of Service Before Coverage Effective',19,'2024-05-29 18:13:27',1,'67 - Date Of Service Before Coverage Effective')
,(20,'68','Date Of Service After Coverage Expired',20,'2024-05-29 18:13:27',1,'68 - Date Of Service After Coverage Expired')
,(21,'69','Date Of Service After Coverage Terminated',21,'2024-05-29 18:13:27',1,'69 - Date Of Service After Coverage Terminated')
,(22,'600','Coverage Outside Submitted Date Of Service',22,'2024-05-29 18:13:27',1,'600 - Coverage Outside Submitted Date Of Service')
,(23,'610','Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN',23,'2024-05-29 18:13:27',1,'610 - Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN')
,(24,'611','Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D',24,'2024-05-29 18:13:27',1,'611 - Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D')
,(25,'620','This Product/Service May Be Covered Under Medicare Part D',25,'2024-05-29 18:13:27',1,'620 - This Product/Service May Be Covered Under Medicare Part D')
,(26,'621','This Medicaid Patient Is Medicare Eligible',26,'2024-05-29 18:13:27',1,'621 - This Medicaid Patient Is Medicare Eligible')
,(27,'645','Repackaged Product Is Not Covered By The Contract',27,'2024-05-29 18:13:27',1,'645 - Repackaged Product Is Not Covered By The Contract')
,(28,'646','Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan',28,'2024-05-29 18:13:27',1,'646 - Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan')
,(29,'7X','Days Supply Exceeds Plan Limitation',29,'2024-05-29 18:13:27',1,'7X - Days Supply Exceeds Plan Limitation')
,(30,'7Y','Compounds Not Covered',30,'2024-05-29 18:13:27',1,'7Y - Compounds Not Covered')
,(31,'70','Product/Service Not Covered - Plan/Benefit Exclusion',31,'2024-05-29 18:13:27',1,'70 - Product/Service Not Covered - Plan/Benefit Exclusion')
,(32,'73','Additional Fills Are Not Covered',32,'2024-05-29 18:13:27',1,'73 - Additional Fills Are Not Covered')
,(33,'74','Other Carrier Payment Meets Or Exceeds Payable',33,'2024-05-29 18:13:27',1,'74 - Other Carrier Payment Meets Or Exceeds Payable')
,(34,'76','Plan Limitations Exceeded',34,'2024-05-29 18:13:27',1,'76 - Plan Limitations Exceeded')
,(35,'78','Cost Exceeds Maximum',35,'2024-05-29 18:13:27',1,'78 - Cost Exceeds Maximum')
,(36,'79','Fill Too Soon',36,'2024-05-29 18:13:27',1,'79 - Fill Too Soon')
,(37,'8H','Product/Service Only Covered On Compound Claim',37,'2024-05-29 18:13:27',1,'8H - Product/Service Only Covered On Compound Claim')
,(38,'8J','Incorrect Product/Service ID For Processor/Payer',38,'2024-05-29 18:13:27',1,'8J - Incorrect Product/Service ID For Processor/Payer')
,(39,'8Q','Excessive Refills Authorized',39,'2024-05-29 18:13:27',1,'8Q - Excessive Refills Authorized')
,(40,'8X','Collection From Cardholder Not Allowed',40,'2024-05-29 18:13:27',1,'8X - Collection From Cardholder Not Allowed')
,(41,'8Y','Excessive Amount Collected',41,'2024-05-29 18:13:27',1,'8Y - Excessive Amount Collected')
,(42,'80','Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria',42,'2024-05-29 18:13:27',1,'80 - Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria')
,(43,'81','Claim Too Old',43,'2024-05-29 18:13:27',1,'81 - Claim Too Old')
,(44,'82','Claim Is Post-Dated',44,'2024-05-29 18:13:27',1,'82 - Claim Is Post-Dated')
,(45,'85','Claim Not Processed',45,'2024-05-29 18:13:27',1,'85 - Claim Not Processed')
,(46,'9G','Quantity Dispensed Exceeds Maximum Allowed',46,'2024-05-29 18:13:27',1,'9G - Quantity Dispensed Exceeds Maximum Allowed')
,(47,'9N','Compound Ingredient Quantity Exceeds Maximum Allowed',47,'2024-05-29 18:13:27',1,'9N - Compound Ingredient Quantity Exceeds Maximum Allowed')
,(48,'9Q','Route Of Administration Submitted Not Covered',48,'2024-05-29 18:13:27',1,'9Q - Route Of Administration Submitted Not Covered')
,(49,'9R','Prescription/Service Reference Number Qualifier Submitted Not Covered',49,'2024-05-29 18:13:27',1,'9R - Prescription/Service Reference Number Qualifier Submitted Not Covered')
,(50,'9S','Future Associated Prescription/Service Date Not Allowed',50,'2024-05-29 18:13:27',1,'9S - Future Associated Prescription/Service Date Not Allowed')
,(51,'9U','Provider ID Qualifier Submitted Not Covered',51,'2024-05-29 18:13:27',1,'9U - Provider ID Qualifier Submitted Not Covered')
,(52,'9X','Coupon Type Submitted Not Covered',52,'2024-05-29 18:13:27',1,'9X - Coupon Type Submitted Not Covered')
,(53,'9Y','Compound Product ID Qualifier Submitted Not Covered',53,'2024-05-29 18:13:27',1,'9Y - Compound Product ID Qualifier Submitted Not Covered')
,(54,'AC','Product Not Covered Non-Participating Manufacturer',54,'2024-05-29 18:13:27',1,'AC - Product Not Covered Non-Participating Manufacturer')
,(55,'AE','QMB (Qualified Medicare Beneficiary)-Bill Medicare',55,'2024-05-29 18:13:27',1,'AE - QMB (Qualified Medicare Beneficiary)-Bill Medicare')
,(56,'AF','Patient Enrolled Under Managed Care',56,'2024-05-29 18:13:27',1,'AF - Patient Enrolled Under Managed Care')
,(57,'AG','Days Supply Limitation For Product/Service',57,'2024-05-29 18:13:27',1,'AG - Days Supply Limitation For Product/Service')
,(58,'AJ','Generic Drug Required',58,'2024-05-29 18:13:27',1,'AJ - Generic Drug Required')
,(59,'A5','Not Covered Under Part D Law',59,'2024-05-29 18:13:27',1,'A5 - Not Covered Under Part D Law')
,(60,'A6','This Product/Service May Be Covered Under Medicare Part B',60,'2024-05-29 18:13:27',1,'A6 - This Product/Service May Be Covered Under Medicare Part B')
,(61,'BA','Compound Basis Of Cost Determination Submitted Not Covered',61,'2024-05-29 18:13:27',1,'BA - Compound Basis Of Cost Determination Submitted Not Covered')
,(62,'BB','Diagnosis Code Qualifier Submitted Not Covered',62,'2024-05-29 18:13:27',1,'BB - Diagnosis Code Qualifier Submitted Not Covered')
,(63,'M1','Patient Not Covered In This Aid Category',63,'2024-05-29 18:13:27',1,'M1 - Patient Not Covered In This Aid Category')
,(64,'M2','Recipient Locked In',64,'2024-05-29 18:13:27',1,'M2 - Recipient Locked In')
,(65,'MP','Other Payer Cardholder ID Not Covered',65,'2024-05-29 18:13:27',1,'MP - Other Payer Cardholder ID Not Covered')
,(66,'MR','Product Not On Formulary',66,'2024-05-29 18:13:27',1,'MR - Product Not On Formulary')
,(67,'PA','PA Exhausted/Not Renewable',67,'2024-05-29 18:13:27',1,'PA - PA Exhausted/Not Renewable')
,(68,'PW','Employer ID Not Covered',68,'2024-05-29 18:13:27',1,'PW - Employer ID Not Covered')
,(69,'PX','Other Payer ID Not Covered',69,'2024-05-29 18:13:27',1,'PX - Other Payer ID Not Covered')
,(70,'P5','Coupon Expired',70,'2024-05-29 18:13:27',1,'P5 - Coupon Expired')
,(71,'RL','Transitional Benefit/Resubmit Claim',71,'2024-05-29 18:13:27',1,'RL - Transitional Benefit/Resubmit Claim')
,(72,'RN','Plan Limits Exceeded On Intended Partial Fill Field Limitations',72,'2024-05-29 18:13:27',1,'RN - Plan Limits Exceeded On Intended Partial Fill Field Limitations')
,(73,'TQ','Dosage Exceeds Product Labeling Limit',73,'2024-05-29 18:13:27',1,'TQ - Dosage Exceeds Product Labeling Limit')
,(74,'UU','DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics',74,'2024-05-29 18:13:27',1,'UU - DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics')
,(75,'A3','This Product May Be Covered Under Hospice - Medicare A',75,'2024-05-29 18:13:27',1,'A3 - This Product May Be Covered Under Hospice - Medicare A')
,(76,'A4','This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility',76,'2024-05-29 18:13:27',1,'A4 - This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility')
,(77,'665','REMS - Excessive Days Supply',77,'2024-05-29 18:13:27',1,'665 - REMS - Excessive Days Supply')
,(78,'666','REMS - Insufficient Days Supply',78,'2024-05-29 18:13:27',1,'666 - REMS - Insufficient Days Supply')
,(79,'667','REMS - Excessive Dosage',79,'2024-05-29 18:13:27',1,'667 - REMS - Excessive Dosage')
,(80,'668','REMS - Insufficient Dosage',80,'2024-05-29 18:13:27',1,'668 - REMS - Insufficient Dosage')
,(81,'669','REMS - Additional Fills Not Permitted',81,'2024-05-29 18:13:27',1,'669 - REMS - Additional Fills Not Permitted')
,(82,'687','REMS - Administrator Denied',82,'2024-05-29 18:13:27',1,'687 - REMS - Administrator Denied')
,(83,'688','REMS - Service Billing Denied',83,'2024-05-29 18:13:27',1,'688 - REMS - Service Billing Denied')
,(84,'689','PDMP - Administrator Denied',84,'2024-05-29 18:13:27',1,'689 - PDMP - Administrator Denied')
,(85,'741','Exceeds Range Start Limitations',85,'2024-05-29 18:13:27',1,'741 - Exceeds Range Start Limitations')
,(86,'742','Exceeds Range End Limitations',86,'2024-05-29 18:13:27',1,'742 - Exceeds Range End Limitations')
,(87,'766','Discrepancy Amount In Excess Of Claimed Amount',87,'2024-05-29 18:13:27',1,'766 - Discrepancy Amount In Excess Of Claimed Amount')
,(88,'774','Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service',88,'2024-05-29 18:13:27',1,'774 - Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service')
,(89,'822','Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.',89,'2024-05-29 18:13:27',1,'822 - Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.')
,(90,'823','Drug Is Beneficiary''s Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.',90,'2024-05-29 18:13:27',1,'823 - Drug Is Beneficiary''s Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.')
,(91,'816','Pharmacy Benefit Exclusion May Be Covered Under Patient''s Medical Benefit',91,'2024-05-29 18:13:27',1,'816 - Pharmacy Benefit Exclusion May Be Covered Under Patient''s Medical Benefit')
,(92,'817','Pharmacy Benefit Exclusion Covered Under Patient''s Medical Benefit',92,'2024-05-29 18:13:27',1,'817 - Pharmacy Benefit Exclusion Covered Under Patient''s Medical Benefit')
,(93,'818','Medication Administration Not Covered Plan Benefit Exclusion',93,'2024-05-29 18:13:27',1,'818 - Medication Administration Not Covered Plan Benefit Exclusion')
,(94,'819','Plan Enrollment File Indicates Medicare As Primary Coverage',94,'2024-05-29 18:13:27',1,'819 - Plan Enrollment File Indicates Medicare As Primary Coverage')
,(95,'794','Deductible Over Accumulated',95,'2024-05-29 18:13:27',1,'794 - Deductible Over Accumulated')
,(96,'795','Out Of Pocket Over Accumulated',96,'2024-05-29 18:13:27',1,'795 - Out Of Pocket Over Accumulated')
,(97,'796','Maximum Benefit Amount (CAP) Over Accumulated',97,'2024-05-29 18:13:27',1,'796 - Maximum Benefit Amount (CAP) Over Accumulated')
,(98,'798','SA Over Accumulated',98,'2024-05-29 18:13:27',1,'798 - SA Over Accumulated')
,(99,'799','LTC Over Accumulated',99,'2024-05-29 18:13:27',1,'799 - LTC Over Accumulated')
,(100,'800','RXC Over Accumulated',100,'2024-05-29 18:13:27',1,'800 - RXC Over Accumulated')
,(101,'829','Pharmacy Must Notify Beneficiary',101,'2024-05-29 18:13:27',1,'829 - Pharmacy Must Notify Beneficiary')
,(102,'84','Claim Has Not Been Paid/Captured',102,'2024-05-29 18:13:27',1,'84 - Claim Has Not Been Paid/Captured')
,(103,'833','Accumulator Year Is Not Within ATBT Timeframe',103,'2024-05-29 18:13:27',1,'833 - Accumulator Year Is Not Within ATBT Timeframe')
,(104,'831','Product Service ID Carve-Out Bill Medicaid Fee For Service',104,'2024-05-29 18:13:27',1,'831 - Product Service ID Carve-Out Bill Medicaid Fee For Service')
,(105,'ZA','The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer',105,'2024-05-29 18:13:27',1,'ZA - The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer')
,(106,'876','Prescriptive Authority Restrictions Apply Criteria Not Met',106,'2024-05-29 18:13:27',1,'876 - Prescriptive Authority Restrictions Apply Criteria Not Met')
,(107,'887','A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed',107,'2024-05-29 18:13:27',1,'887 - A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed')
,(108,'888','Beneficiary Is Enrolled In Excluded Federal Health Care Program',108,'2024-05-29 18:13:27',1,'888 - Beneficiary Is Enrolled In Excluded Federal Health Care Program')
,(109,'891','Days Supply Is Less Than Plan Minimum',109,'2024-05-29 18:13:27',1,'891 - Days Supply Is Less Than Plan Minimum')
,(110,'895','Allowed Number of Overrides Exhausted',110,'2024-05-29 18:13:27',1,'895 - Allowed Number of Overrides Exhausted')
,(111,'896','Other Adjudicated Program Type Of Unknown Is Not Covered',111,'2024-05-29 18:13:27',1,'896 - Other Adjudicated Program Type Of Unknown Is Not Covered')
,(112,'804','M/I Amount Attributed To Product Selection/Brand',112,'2024-05-29 18:13:27',1,'804 - M/I Amount Attributed To Product Selection/Brand')
,(113,'805','M/I Amount Attributed To Sales Tax',113,'2024-05-29 18:13:27',1,'805 - M/I Amount Attributed To Sales Tax')
,(114,'806','M/I Amount Attributed To Process Fee',114,'2024-05-29 18:13:27',1,'806 - M/I Amount Attributed To Process Fee')
;
ALTER SEQUENCE "form_list_ncpdp_resp_cob_trk_id_seq" RESTART WITH 115;
SET session_replication_role = DEFAULT;
 COMMIT;
