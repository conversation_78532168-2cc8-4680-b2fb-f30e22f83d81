BEGIN;
SET session_replication_role = replica;
DELETE FROM form_payer_contract;

INSERT INTO public.form_payer_contract (id, created_by, change_by, updated_by, reviewed_by, assigned_matrix_id, contract_type_code, reviewed_on, deleted, archived, updated_on, auto_name, change_type, change_data, created_on, change_on, name, update_special_prices, update_expected_prices, contract_code, contract_version_identifier, contract_percentage, terms_discount_percentage, notes, sys_period, external_id) VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,'2025-04-12T22:45:32.000Z','Default',NULL,NULL,'2025-04-12T22:42:52.000Z',NULL,'Default','Yes','Yes',NULL,NULL,NULL,NULL,'Default pricing matrix used as fall back override to all others.',NULL,NULL);

ALTER SEQUENCE "form_payer_contract_id_seq" RESTART WITH 2;
SET session_replication_role = DEFAULT;
COMMIT;