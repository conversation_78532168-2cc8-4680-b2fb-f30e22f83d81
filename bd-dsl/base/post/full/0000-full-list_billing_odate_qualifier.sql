BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_odate_qualifier;
INSERT INTO form_list_billing_odate_qualifier ("code","name","id","created_on","created_by","auto_name") VALUES ('329','Accident',1,'2024-03-19 22:39:32',1,'329 - Accident')
,('453','Acute Manifestation of a Chronic Condition',2,'2024-03-19 22:39:32',1,'453 - Acute Manifestation of a Chronic Condition')
,('444','First Visit or Consultation',3,'2024-03-19 22:39:32',1,'444 - First Visit or Consultation')
,('454','Initial Treatment',4,'2024-03-19 22:39:32',1,'454 - Initial Treatment')
,('455','Last X-Ray',5,'2024-03-19 22:39:32',1,'455 - Last X-Ray')
,('304','Latest Visit or Consultation',6,'2024-03-19 22:39:32',1,'304 - Latest Visit or Consultation')
,('471','Prescription',7,'2024-03-19 22:39:32',1,'471 - Prescription')
,('091','Report End (Relinquished Care Date)',8,'2024-03-19 22:39:32',1,'091 - Report End (Relinquished Care Date)')
,('090','Report Start (Assumed Care Date)',9,'2024-03-19 22:39:32',1,'090 - Report Start (Assumed Care Date)')
;
ALTER SEQUENCE "form_list_billing_odate_qualifier_id_seq" RESTART WITH 10;
SET session_replication_role = DEFAULT;
 COMMIT;
