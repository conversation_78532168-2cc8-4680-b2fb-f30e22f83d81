BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_product_qualifier;
INSERT INTO form_list_ss_product_qualifier ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:44:40',1,'Device Identifier','DI','Device Identifier'),
(2,'4/16/2024, 06:44:40',1,'National Drug Code (NDC)','ND','National Drug Code (NDC)'),
(3,'4/16/2024, 06:44:40',1,'National Drug File Reference (NDF-RT)','RT','National Drug File Reference (NDF-RT)'),
(4,'4/16/2024, 06:44:40',1,'Unique Ingredient Identifier (UNII)','UN','Unique Ingredient Identifier (UNII)'),
(5,'4/16/2024, 06:44:40',1,'Universal Product Code (UPC)','UP','Universal Product Code (UPC)'),
(6,'4/16/2024, 06:44:40',1,'Health Related Item (HRI)','NH','Health Related Item (HRI)'),
(7,'4/16/2024, 06:44:40',1,'Gold Standard Marketed Product Identifier (MPid)','GMP','Gold Standard Marketed Product Identifier (MPid)'),
(8,'4/16/2024, 06:44:40',1,'Gold Standard Product Identifier (ProdID)','GPI','Gold Standard Product Identifier (ProdID)'),
(9,'4/16/2024, 06:44:40',1,'Gold Standard Specific Product Identifier (SPID)','GSP','Gold Standard Specific Product Identifier (SPID)')
;
ALTER SEQUENCE "form_list_ss_product_qualifier_id_seq" RESTART WITH 10;
SET session_replication_role = DEFAULT;
COMMIT;