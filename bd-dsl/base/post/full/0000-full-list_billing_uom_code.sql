BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_billing_uom_code;
INSERT INTO form_list_billing_uom_code ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'EA - Each',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'EA','Each'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'GM - Grams',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'GM','Grams'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ML - Milliliters',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ML','Milliliters');
SET session_replication_role = DEFAULT;
COMMIT;
