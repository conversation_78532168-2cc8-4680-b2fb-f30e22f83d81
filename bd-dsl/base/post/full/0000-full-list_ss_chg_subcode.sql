BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_chg_subcode;
INSERT INTO form_list_ss_chg_subcode ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:30:24',1,'Prescriber must confirm their State license status.','A','Prescriber must confirm their State license status.'),
(2,'4/16/2024, 06:30:24',1,'Prescriber must confirm their DEA license status in prescribing state.','B','Prescriber must confirm their DEA license status in prescribing state.'),
(3,'4/16/2024, 06:30:24',1,'Prescriber must confirm their DEA registration by DEA class.','C','Prescriber must confirm their DEA registration by DEA class.'),
(4,'4/16/2024, 06:30:24',1,'Prescriber must confirm their State Controlled Substance Registration license status.','D','Prescriber must confirm their State Controlled Substance Registration license status.'),
(5,'4/16/2024, 06:30:24',1,'Prescriber must confirm their registration by State Controlled Substance Registration class.','E','Prescriber must confirm their registration by State Controlled Substance Registration class.'),
(6,'4/16/2024, 06:30:24',1,'Prescriber must confirm their NADEAN license status.','F','Prescriber must confirm their NADEAN license status.'),
(7,'4/16/2024, 06:30:24',1,'Prescriber must obtain/validate Type1 NPI.','G','Prescriber must obtain/validate Type1 NPI.'),
(8,'4/16/2024, 06:30:24',1,'Prescriber must enroll/re-enroll with prescription benefit plan.','H','Prescriber must enroll/re-enroll with prescription benefit plan.'),
(9,'4/16/2024, 06:30:24',1,'Prescriber must confirm prescriptive authority criteria for prescribed medication is met.','I','Prescriber must confirm prescriptive authority criteria for prescribed medication is met.'),
(10,'4/16/2024, 06:30:24',1,'Prescriber must enroll/re-enroll in REMS.','J','Prescriber must enroll/re-enroll in REMS.'),
(11,'4/16/2024, 06:30:24',1,'Prescriber must confirm their assignment as patient’s lock-in prescriber.','K','Prescriber must confirm their assignment as patient’s lock-in prescriber.'),
(12,'4/16/2024, 06:30:24',1,'Prescriber must obtain/validate their supervising prescriber.','L','Prescriber must obtain/validate their supervising prescriber.'),
(13,'4/16/2024, 06:30:24',1,'Prescriber must confirm their Certificate to Prescribe Number status.','M','Prescriber must confirm their Certificate to Prescribe Number status.')
;
ALTER SEQUENCE "form_list_ss_chg_subcode_id_seq" RESTART WITH 14;
SET session_replication_role = DEFAULT;
COMMIT;