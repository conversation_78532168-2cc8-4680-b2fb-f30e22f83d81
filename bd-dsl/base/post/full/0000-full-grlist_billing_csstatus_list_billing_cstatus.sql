BEGIN;
 SET session_replication_role = replica;
 DELETE FROM gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id;
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (1,1,'1');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (2,2,'5');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (3,2,'6');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (4,3,'5');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (5,3,'6');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (6,4,'5');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (7,4,'6');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (8,5,'6');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (9,6,'6');
INSERT INTO gr_form_list_billing_csstatus_sid_to_list_billing_cstatus_id (id,form_list_billing_csstatus_fk,form_list_billing_cstatus_fk) VALUES (10,7,'6');
SET session_replication_role = DEFAULT;
 COMMIT;
