BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_billing_claim_type;
INSERT INTO form_list_billing_claim_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'09 - Self-pay',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'09','Self-pay'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'10 - Central Certification',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'10','Central Certification'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'11 - Other Non-Federal Programs',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'11','Other Non-Federal Programs'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'12 - Preferred Provider Organization (PPO)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'12','Preferred Provider Organization (PPO)'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'13 - Point of Service (POS)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'13','Point of Service (POS)'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'14 - Exclusive Provider Organization (EPO)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'14','Exclusive Provider Organization (EPO)'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'15 - Indemnity Insurance',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'15','Indemnity Insurance'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'16 - Health Maintenance Organization (HMO) Medicare Risk',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'16','Health Maintenance Organization (HMO) Medicare Risk'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'17 - Dental Maintenance Organization (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'17','Dental Maintenance Organization (5010)'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AM - Automobile Medical',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'AM','Automobile Medical'),
(11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'BL - Blue Cross/Blue Shield',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'BL','Blue Cross/Blue Shield'),
(12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CH - Champus',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CH','Champus'),
(13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CI - Commercial Insurance Co.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CI','Commercial Insurance Co.'),
(14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'DS - Disability',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'DS','Disability'),
(15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'FI - Federal Employees Program (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'FI','Federal Employees Program (5010)'),
(16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'HM - Health Maintenance Organization',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'HM','Health Maintenance Organization'),
(17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'LI - Liability',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'LI','Liability'),
(18,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'LM - Liability Medical',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'LM','Liability Medical'),
(19,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MA - Medicare Part A',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MA','Medicare Part A'),
(20,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MB - Medicare Part B',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MB','Medicare Part B'),
(21,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MC - Medicaid',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MC','Medicaid'),
(22,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'OF - Other Federal Program',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'OF','Other Federal Program'),
(23,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'TV - Title V',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'TV','Title V'),
(24,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'VA - Veteran Administration Plan',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'VA','Veteran Administration Plan'),
(25,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'WC - Workers'' Compensation Health Claim',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'WC','Workers'' Compensation Health Claim'),
(26,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ZZ - Mutually Defined Unknown',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ZZ','Mutually Defined Unknown');
SET session_replication_role = DEFAULT;
COMMIT;
