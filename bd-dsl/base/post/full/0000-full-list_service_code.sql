BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_service_code;
INSERT INTO form_list_service_code ("code","name","id","created_on","created_by","auto_name") VALUES ('1','Medical Care',1,'2024-03-19 22:39:32',1,'1 - Medical Care')
,('10','Blood Charges',2,'2024-03-19 22:39:32',1,'10 - Blood Charges')
,('11','Used Durable Medical Equipment',3,'2024-03-19 22:39:32',1,'11 - Used Durable Medical Equipment')
,('12','Durable Medical Equipment Purchase',4,'2024-03-19 22:39:32',1,'12 - Durable Medical Equipment Purchase')
,('13','Ambulatory Service Center Facility',5,'2024-03-19 22:39:32',1,'13 - Ambulatory Service Center Facility')
,('14','Rental Supplies in the House',6,'2024-03-19 22:39:32',1,'14 - Rental Supplies in the House')
,('15','Alternate Method Dialysis',7,'2024-03-19 22:39:32',1,'15 - Alternate Method Dialysis')
,('16','Chronic Renal Disease (CRD)',8,'2024-03-19 22:39:32',1,'16 - Chronic Renal Disease (CRD)')
,('17','Pre-Admission Testing',9,'2024-03-19 22:39:32',1,'17 - Pre-Admission Testing')
,('18','Durable Medical Equipment Rental',10,'2024-03-19 22:39:32',1,'18 - Durable Medical Equipment Rental')
,('19','Pneumonia Vaccine',11,'2024-03-19 22:39:32',1,'19 - Pneumonia Vaccine')
,('2','Surgical',12,'2024-03-19 22:39:32',1,'2 - Surgical')
,('20','Second Surgical Opinion',13,'2024-03-19 22:39:32',1,'20 - Second Surgical Opinion')
,('21','Third Surgical Opinion',14,'2024-03-19 22:39:32',1,'21 - Third Surgical Opinion')
,('22','Social Work',15,'2024-03-19 22:39:32',1,'22 - Social Work')
,('23','Diagnostic Dental',16,'2024-03-19 22:39:32',1,'23 - Diagnostic Dental')
,('24','Periodontics',17,'2024-03-19 22:39:32',1,'24 - Periodontics')
,('25','Restorative',18,'2024-03-19 22:39:32',1,'25 - Restorative')
,('26','Endodontics',19,'2024-03-19 22:39:32',1,'26 - Endodontics')
,('27','Maxillofacial Prosthetics',20,'2024-03-19 22:39:32',1,'27 - Maxillofacial Prosthetics')
,('28','Adjunctive Dental Services',21,'2024-03-19 22:39:32',1,'28 - Adjunctive Dental Services')
,('3','Consultation',22,'2024-03-19 22:39:32',1,'3 - Consultation')
,('30','Health Benefit Plan Coverage',23,'2024-03-19 22:39:32',1,'30 - Health Benefit Plan Coverage')
,('32','Plan Waiting Period',24,'2024-03-19 22:39:32',1,'32 - Plan Waiting Period')
,('33','Chiropractic',25,'2024-03-19 22:39:32',1,'33 - Chiropractic')
,('34','Chiropractic Office Visit',26,'2024-03-19 22:39:32',1,'34 - Chiropractic Office Visit')
,('35','Dental Care',27,'2024-03-19 22:39:32',1,'35 - Dental Care')
,('36','Dental Crowns',28,'2024-03-19 22:39:32',1,'36 - Dental Crowns')
,('37','Dental Accident',29,'2024-03-19 22:39:32',1,'37 - Dental Accident')
,('38','Orthodontics',30,'2024-03-19 22:39:32',1,'38 - Orthodontics')
,('39','Prosthodontics',31,'2024-03-19 22:39:32',1,'39 - Prosthodontics')
,('4','Diagnostic X-Ray',32,'2024-03-19 22:39:32',1,'4 - Diagnostic X-Ray')
,('40','Oral Surgery',33,'2024-03-19 22:39:32',1,'40 - Oral Surgery')
,('41','Routine (Preventive) Dental',34,'2024-03-19 22:39:32',1,'41 - Routine (Preventive) Dental')
,('42','Home Health Care',35,'2024-03-19 22:39:32',1,'42 - Home Health Care')
,('43','Home Health Prescriptions',36,'2024-03-19 22:39:32',1,'43 - Home Health Prescriptions')
,('44','Home Health Visits',37,'2024-03-19 22:39:32',1,'44 - Home Health Visits')
,('45','Hospice',38,'2024-03-19 22:39:32',1,'45 - Hospice')
,('46','Respite Care',39,'2024-03-19 22:39:32',1,'46 - Respite Care')
,('47','Hospital',40,'2024-03-19 22:39:32',1,'47 - Hospital')
,('48','Hospital - Inpatient',41,'2024-03-19 22:39:32',1,'48 - Hospital - Inpatient')
,('49','Hospital - Room and Board',42,'2024-03-19 22:39:32',1,'49 - Hospital - Room and Board')
,('5','Diagnostic Lab',43,'2024-03-19 22:39:32',1,'5 - Diagnostic Lab')
,('50','Hospital - Outpatient',44,'2024-03-19 22:39:32',1,'50 - Hospital - Outpatient')
,('51','Hospital - Emergency Accident',45,'2024-03-19 22:39:32',1,'51 - Hospital - Emergency Accident')
,('52','Hospital - Emergency Medical',46,'2024-03-19 22:39:32',1,'52 - Hospital - Emergency Medical')
,('53','Hospital Emergency Surgical',47,'2024-03-19 22:39:32',1,'53 - Hospital Emergency Surgical')
,('54','Long Term Care',48,'2024-03-19 22:39:32',1,'54 - Long Term Care')
,('55','Major Medical',49,'2024-03-19 22:39:32',1,'55 - Major Medical')
,('56','Medically Related Transportation',50,'2024-03-19 22:39:32',1,'56 - Medically Related Transportation')
,('57','Air Transportation',51,'2024-03-19 22:39:32',1,'57 - Air Transportation')
,('58','Cabulance',52,'2024-03-19 22:39:32',1,'58 - Cabulance')
,('59','Licensed Ambulance',53,'2024-03-19 22:39:32',1,'59 - Licensed Ambulance')
,('6','Radiation Therapy',54,'2024-03-19 22:39:32',1,'6 - Radiation Therapy')
,('60','General Benefits',55,'2024-03-19 22:39:32',1,'60 - General Benefits')
,('61','In-vitro Fertilization',56,'2024-03-19 22:39:32',1,'61 - In-vitro Fertilization')
,('62','MRI/CAT Scan',57,'2024-03-19 22:39:32',1,'62 - MRI/CAT Scan')
,('63','Donor Procedures',58,'2024-03-19 22:39:32',1,'63 - Donor Procedures')
,('64','Acupuncture',59,'2024-03-19 22:39:32',1,'64 - Acupuncture')
,('65','Newborn Care',60,'2024-03-19 22:39:32',1,'65 - Newborn Care')
,('66','Pathology',61,'2024-03-19 22:39:32',1,'66 - Pathology')
,('67','Smoking Cessation',62,'2024-03-19 22:39:32',1,'67 - Smoking Cessation')
,('68','Well Baby Care',63,'2024-03-19 22:39:32',1,'68 - Well Baby Care')
,('69','Maternity',64,'2024-03-19 22:39:32',1,'69 - Maternity')
,('7','Anesthesia',65,'2024-03-19 22:39:32',1,'7 - Anesthesia')
,('70','Transplants',66,'2024-03-19 22:39:32',1,'70 - Transplants')
,('71','Audiology Exam',67,'2024-03-19 22:39:32',1,'71 - Audiology Exam')
,('72','Inhalation Therapy',68,'2024-03-19 22:39:32',1,'72 - Inhalation Therapy')
,('73','Diagnostic Medical',69,'2024-03-19 22:39:32',1,'73 - Diagnostic Medical')
,('74','Private Duty Nursing',70,'2024-03-19 22:39:32',1,'74 - Private Duty Nursing')
,('75','Prosthetic Device',71,'2024-03-19 22:39:32',1,'75 - Prosthetic Device')
,('76','Dialysis',72,'2024-03-19 22:39:32',1,'76 - Dialysis')
,('77','Otological Exam',73,'2024-03-19 22:39:32',1,'77 - Otological Exam')
,('78','Chemotherapy',74,'2024-03-19 22:39:32',1,'78 - Chemotherapy')
,('79','Allergy Testing',75,'2024-03-19 22:39:32',1,'79 - Allergy Testing')
,('8','Surgical Assistance',76,'2024-03-19 22:39:32',1,'8 - Surgical Assistance')
,('80','Immunizations',77,'2024-03-19 22:39:32',1,'80 - Immunizations')
,('81','Routine Physical',78,'2024-03-19 22:39:32',1,'81 - Routine Physical')
,('82','Family Planning',79,'2024-03-19 22:39:32',1,'82 - Family Planning')
,('83','Infertility',80,'2024-03-19 22:39:32',1,'83 - Infertility')
,('84','Abortion',81,'2024-03-19 22:39:32',1,'84 - Abortion')
,('85','AIDS',82,'2024-03-19 22:39:32',1,'85 - AIDS')
,('86','Emergency Services',83,'2024-03-19 22:39:32',1,'86 - Emergency Services')
,('87','Cancer',84,'2024-03-19 22:39:32',1,'87 - Cancer')
,('88','Pharmacy',85,'2024-03-19 22:39:32',1,'88 - Pharmacy')
,('89','Free Standing Prescription Drug',86,'2024-03-19 22:39:32',1,'89 - Free Standing Prescription Drug')
,('9','Other Medical',87,'2024-03-19 22:39:32',1,'9 - Other Medical')
,('90','Mail Order Prescription Drug',88,'2024-03-19 22:39:32',1,'90 - Mail Order Prescription Drug')
,('91','Brand Name Prescription Drug',89,'2024-03-19 22:39:32',1,'91 - Brand Name Prescription Drug')
,('92','Generic Prescription Drug',90,'2024-03-19 22:39:32',1,'92 - Generic Prescription Drug')
,('93','Podiatry',91,'2024-03-19 22:39:32',1,'93 - Podiatry')
,('94','Podiatry - Office Visits',92,'2024-03-19 22:39:32',1,'94 - Podiatry - Office Visits')
,('95','Podiatry - Nursing Home Visits',93,'2024-03-19 22:39:32',1,'95 - Podiatry - Nursing Home Visits')
,('96','Professional (Physician)',94,'2024-03-19 22:39:32',1,'96 - Professional (Physician)')
,('97','Anesthesiologist',95,'2024-03-19 22:39:32',1,'97 - Anesthesiologist')
,('98','Professional (Physician) Visit - Office',96,'2024-03-19 22:39:32',1,'98 - Professional (Physician) Visit - Office')
,('99','Professional (Physician) Visit - Inpatient',97,'2024-03-19 22:39:32',1,'99 - Professional (Physician) Visit - Inpatient')
,('A0','Professional (Physician) Visit - Outpatient',98,'2024-03-19 22:39:32',1,'A0 - Professional (Physician) Visit - Outpatient')
,('A1','Professional (Physician) Visit - Nursing Home',99,'2024-03-19 22:39:32',1,'A1 - Professional (Physician) Visit - Nursing Home')
,('A2','Professional (Physician) Visit - Skilled Nursing Facility',100,'2024-03-19 22:39:32',1,'A2 - Professional (Physician) Visit - Skilled Nursing Facility')
,('A3','Professional (Physician) Visit - Home',101,'2024-03-19 22:39:32',1,'A3 - Professional (Physician) Visit - Home')
,('A4','Psychiatric',102,'2024-03-19 22:39:32',1,'A4 - Psychiatric')
,('A5','Psychiatric - Room and Board',103,'2024-03-19 22:39:32',1,'A5 - Psychiatric - Room and Board')
,('A6','Psychotherapy',104,'2024-03-19 22:39:32',1,'A6 - Psychotherapy')
,('A7','Psychiatric - Inpatient',105,'2024-03-19 22:39:32',1,'A7 - Psychiatric - Inpatient')
,('A8','Psychiatric - Outpatient',106,'2024-03-19 22:39:32',1,'A8 - Psychiatric - Outpatient')
,('A9','Rehabilitation',107,'2024-03-19 22:39:32',1,'A9 - Rehabilitation')
,('AA','Rehabilitation - Room and Board',108,'2024-03-19 22:39:32',1,'AA - Rehabilitation - Room and Board')
,('AB','Rehabilitation - Inpatient',109,'2024-03-19 22:39:32',1,'AB - Rehabilitation - Inpatient')
,('AC','Rehabilitation - Outpatient',110,'2024-03-19 22:39:32',1,'AC - Rehabilitation - Outpatient')
,('AE','Physical Medicine',111,'2024-03-19 22:39:32',1,'AE - Physical Medicine')
,('AF','Speech Therapy',112,'2024-03-19 22:39:32',1,'AF - Speech Therapy')
,('AG','Skilled Nursing Care',113,'2024-03-19 22:39:32',1,'AG - Skilled Nursing Care')
,('AH','Skilled Nursing Care  - Room and Board',114,'2024-03-19 22:39:32',1,'AH - Skilled Nursing Care  - Room and Board')
,('AI','Substance Abuse',115,'2024-03-19 22:39:32',1,'AI - Substance Abuse')
,('AJ','Alcoholism',116,'2024-03-19 22:39:32',1,'AJ - Alcoholism')
,('AK','Drug Addiction',117,'2024-03-19 22:39:32',1,'AK - Drug Addiction')
,('AL','Vision (Optometry)',118,'2024-03-19 22:39:32',1,'AL - Vision (Optometry)')
,('AM','Frames',119,'2024-03-19 22:39:32',1,'AM - Frames')
,('AN','Routine Exam',120,'2024-03-19 22:39:32',1,'AN - Routine Exam')
,('AO','Lenses',121,'2024-03-19 22:39:32',1,'AO - Lenses')
,('AQ','Non-medically Necessary Physical',122,'2024-03-19 22:39:32',1,'AQ - Non-medically Necessary Physical')
,('AR','Experimental Drug Therapy',123,'2024-03-19 22:39:32',1,'AR - Experimental Drug Therapy')
,('B1','Bum Care (5010)',124,'2024-03-19 22:39:32',1,'B1 - Bum Care (5010)')
,('B2','Brand Name Prescription Drug - Formulary (5010)',125,'2024-03-19 22:39:32',1,'B2 - Brand Name Prescription Drug - Formulary (5010)')
,('B3','Brand Name Prescription Drug  - Non-Formulary (5010)',126,'2024-03-19 22:39:32',1,'B3 - Brand Name Prescription Drug  - Non-Formulary (5010)')
,('BA','Independent Medical Evaluation',127,'2024-03-19 22:39:32',1,'BA - Independent Medical Evaluation')
,('BB','Partial Hospitalization (Psychiatric)',128,'2024-03-19 22:39:32',1,'BB - Partial Hospitalization (Psychiatric)')
,('BC','Day Care (Psychiatric)',129,'2024-03-19 22:39:32',1,'BC - Day Care (Psychiatric)')
,('BD','Cognitive Therapy',130,'2024-03-19 22:39:32',1,'BD - Cognitive Therapy')
,('BE','Massage Therapy',131,'2024-03-19 22:39:32',1,'BE - Massage Therapy')
,('BF','Pulmonary Rehabilitation',132,'2024-03-19 22:39:32',1,'BF - Pulmonary Rehabilitation')
,('BG','Cardiac Rehabilitation',133,'2024-03-19 22:39:32',1,'BG - Cardiac Rehabilitation')
,('BH','Pediatric',134,'2024-03-19 22:39:32',1,'BH - Pediatric')
,('BI','Nursery',135,'2024-03-19 22:39:32',1,'BI - Nursery')
,('BJ','Skin',136,'2024-03-19 22:39:32',1,'BJ - Skin')
,('BK','Orthopedic',137,'2024-03-19 22:39:32',1,'BK - Orthopedic')
,('BL','Cardiac',138,'2024-03-19 22:39:32',1,'BL - Cardiac')
,('BM','Lymphatic',139,'2024-03-19 22:39:32',1,'BM - Lymphatic')
,('BN','Gastrointestinal',140,'2024-03-19 22:39:32',1,'BN - Gastrointestinal')
,('BP','Endocrine',141,'2024-03-19 22:39:32',1,'BP - Endocrine')
,('BQ','Neurology',142,'2024-03-19 22:39:32',1,'BQ - Neurology')
,('BR','Eye',143,'2024-03-19 22:39:32',1,'BR - Eye')
,('BS','Invasive Procedures',144,'2024-03-19 22:39:32',1,'BS - Invasive Procedures')
,('BT','Gynecological (5010)',145,'2024-03-19 22:39:32',1,'BT - Gynecological (5010)')
,('BU','Obstetrical (5010)',146,'2024-03-19 22:39:32',1,'BU - Obstetrical (5010)')
,('BV','Obstetrical/Gynecological (5010)',147,'2024-03-19 22:39:32',1,'BV - Obstetrical/Gynecological (5010)')
,('BW','Mail Order Prescription Drug-Brand Name (5010)',148,'2024-03-19 22:39:32',1,'BW - Mail Order Prescription Drug-Brand Name (5010)')
,('BX','Mail Order Prescription Drug-Generic (5010)',149,'2024-03-19 22:39:32',1,'BX - Mail Order Prescription Drug-Generic (5010)')
,('BY','Physician Visit - Office Sick (5010)',150,'2024-03-19 22:39:32',1,'BY - Physician Visit - Office Sick (5010)')
,('BZ','Physician Visit - Office Well (5010)',151,'2024-03-19 22:39:32',1,'BZ - Physician Visit - Office Well (5010)')
,('C1','Coronary Care (5010)',152,'2024-03-19 22:39:32',1,'C1 - Coronary Care (5010)')
,('CA','Private Duty Nursing - Inpatient (5010)',153,'2024-03-19 22:39:32',1,'CA - Private Duty Nursing - Inpatient (5010)')
,('CB','Private Duty Nursing - Home (5010)',154,'2024-03-19 22:39:32',1,'CB - Private Duty Nursing - Home (5010)')
,('CC','Surgical Benefits - Processional (Physician) (5010)',155,'2024-03-19 22:39:32',1,'CC - Surgical Benefits - Processional (Physician) (5010)')
,('CD','Surgical Benefits -Facility (5010)',156,'2024-03-19 22:39:32',1,'CD - Surgical Benefits -Facility (5010)')
,('CE','Mental Health Provider - Inpatient  (5010)',157,'2024-03-19 22:39:32',1,'CE - Mental Health Provider - Inpatient  (5010)')
,('CF','Mental Health Provider - Outpatient  (5010)',158,'2024-03-19 22:39:32',1,'CF - Mental Health Provider - Outpatient  (5010)')
,('CG','Mental Health Facility - Inpatient  (5010)',159,'2024-03-19 22:39:32',1,'CG - Mental Health Facility - Inpatient  (5010)')
,('CH','Mental Health Facility - Outpatient  (5010)',160,'2024-03-19 22:39:32',1,'CH - Mental Health Facility - Outpatient  (5010)')
,('CI','Substance Abuse Facility - Inpatient  (5010)',161,'2024-03-19 22:39:32',1,'CI - Substance Abuse Facility - Inpatient  (5010)')
,('CJ','Substance Abuse Facility - Outpatient  (5010)',162,'2024-03-19 22:39:32',1,'CJ - Substance Abuse Facility - Outpatient  (5010)')
,('CK','Screening X-ray (5010)',163,'2024-03-19 22:39:32',1,'CK - Screening X-ray (5010)')
,('CL','Screening laboratory (5010)',164,'2024-03-19 22:39:32',1,'CL - Screening laboratory (5010)')
,('CM','Mammogram, High Risk Patient  (5010)',165,'2024-03-19 22:39:32',1,'CM - Mammogram, High Risk Patient  (5010)')
,('CN','Mammogram, Low Risk Patient  (5010)',166,'2024-03-19 22:39:32',1,'CN - Mammogram, Low Risk Patient  (5010)')
,('CO','Flu Vaccine (5010)',167,'2024-03-19 22:39:32',1,'CO - Flu Vaccine (5010)')
,('CP','Eyewear and Eyewear Accessories (5010)',168,'2024-03-19 22:39:32',1,'CP - Eyewear and Eyewear Accessories (5010)')
,('CQ','Case Management (5010)',169,'2024-03-19 22:39:32',1,'CQ - Case Management (5010)')
,('DG','Dermatology (5010)',170,'2024-03-19 22:39:32',1,'DG - Dermatology (5010)')
,('DM','Durable Medical Equipment (5010)',171,'2024-03-19 22:39:32',1,'DM - Durable Medical Equipment (5010)')
,('DS','Diabetic Supplies (5010)',172,'2024-03-19 22:39:32',1,'DS - Diabetic Supplies (5010)')
,('GF','Generic Prescription Drug - Formulary (5010)',173,'2024-03-19 22:39:32',1,'GF - Generic Prescription Drug - Formulary (5010)')
,('GN','Generic Prescription Drug - Non-Formulary (5010)',174,'2024-03-19 22:39:32',1,'GN - Generic Prescription Drug - Non-Formulary (5010)')
,('GY','Allergy (5010)',175,'2024-03-19 22:39:32',1,'GY - Allergy (5010)')
,('IC','Intensive Care (5010)',176,'2024-03-19 22:39:32',1,'IC - Intensive Care (5010)')
,('MH','Mental Health (5010)',177,'2024-03-19 22:39:32',1,'MH - Mental Health (5010)')
,('NI','Neonatal Intensive Care (5010)',178,'2024-03-19 22:39:32',1,'NI - Neonatal Intensive Care (5010)')
,('ON','Oncology (5010)',179,'2024-03-19 22:39:32',1,'ON - Oncology (5010)')
,('PT','Physical Therapy (5010)',180,'2024-03-19 22:39:32',1,'PT - Physical Therapy (5010)')
,('PU','Pulmonary (5010)',181,'2024-03-19 22:39:32',1,'PU - Pulmonary (5010)')
,('RN','Renal (5010)',182,'2024-03-19 22:39:32',1,'RN - Renal (5010)')
,('RT','Residential Psychiatric Treatment (5010)',183,'2024-03-19 22:39:32',1,'RT - Residential Psychiatric Treatment (5010)')
,('TC','Transitional Care (5010)',184,'2024-03-19 22:39:32',1,'TC - Transitional Care (5010)')
,('TN','Transitional Nursery Care (5010)',185,'2024-03-19 22:39:32',1,'TN - Transitional Nursery Care (5010)')
,('UC','Urgent Care (5010)',186,'2024-03-19 22:39:32',1,'UC - Urgent Care (5010)')
;
ALTER SEQUENCE "form_list_service_code_id_seq" RESTART WITH 187;
SET session_replication_role = DEFAULT;
 COMMIT;
