BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_service_result_code;
INSERT INTO form_list_ss_service_result_code ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:36:09',1,'Prescribed With Acknowledgments','4A','Prescribed With Acknowledgments'),
(2,'4/16/2024, 06:36:09',1,'Discontinued Drug-','3C','Discontinued Drug-'),
(3,'4/16/2024, 06:36:09',1,'Filled, With Different Dose','1C','Filled, With Different Dose'),
(4,'4/16/2024, 06:36:09',1,'Therapy Changed','3E','Therapy Changed'),
(5,'4/16/2024, 06:36:09',1,'Compliance Aid Provided','3M','Compliance Aid Provided'),
(6,'4/16/2024, 06:36:09',1,'Filled, With Different Drug','1E','Filled, With Different Drug'),
(7,'4/16/2024, 06:36:09',1,'Prescription Not Filled','2A','Prescription Not Filled'),
(8,'4/16/2024, 06:36:09',1,'Filled Prescription As Is','1B','Filled Prescription As Is'),
(9,'4/16/2024, 06:36:09',1,'Recommendation Accepted','3A','Recommendation Accepted'),
(10,'4/16/2024, 06:36:09',1,'Regimen Changed','3D','Regimen Changed'),
(11,'4/16/2024, 06:36:09',1,'Brand-to-Generic Change','1H','Brand-to-Generic Change'),
(12,'4/16/2024, 06:36:09',1,'Recommendation Not Accepted','3B','Recommendation Not Accepted'),
(13,'4/16/2024, 06:36:09',1,'Patient Referral','3J','Patient Referral'),
(14,'4/16/2024, 06:36:09',1,'Filled, With Different Quantity','1F','Filled, With Different Quantity'),
(15,'4/16/2024, 06:36:09',1,'Filled with Different Dosage Form','1K','Filled with Different Dosage Form'),
(16,'4/16/2024, 06:36:09',1,'Follow-Up/Report','3H','Follow-Up/Report'),
(17,'4/16/2024, 06:36:09',1,'Filled, With Different Directions','1D','Filled, With Different Directions'),
(18,'4/16/2024, 06:36:09',1,'Not Filled, Directions Clarified','2B','Not Filled, Directions Clarified'),
(19,'4/16/2024, 06:36:09',1,'Drug Therapy Unchanged','3G','Drug Therapy Unchanged'),
(20,'4/16/2024, 06:36:09',1,'Medication Administered','3N','Medication Administered'),
(21,'4/16/2024, 06:36:09',1,'Filled As Is, False Positive','1A','Filled As Is, False Positive'),
(22,'4/16/2024, 06:36:09',1,'Not Specified','00','Not Specified'),
(23,'4/16/2024, 06:36:09',1,'Therapy Changed-cost increased acknowledged','3F','Therapy Changed-cost increased acknowledged'),
(24,'4/16/2024, 06:36:09',1,'Instructions Understood','3K','Instructions Understood'),
(25,'4/16/2024, 06:36:09',1,'Rx-to-OTC Change','1J','Rx-to-OTC Change'),
(26,'4/16/2024, 06:36:09',1,'Filled, With Prescriber Approval','1G','Filled, With Prescriber Approval')
;
ALTER SEQUENCE "form_list_ss_service_result_code_id_seq" RESTART WITH 27;
SET session_replication_role = DEFAULT;
COMMIT;