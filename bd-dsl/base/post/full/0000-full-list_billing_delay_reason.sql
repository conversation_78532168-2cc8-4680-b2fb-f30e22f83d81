BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_delay_reason;
INSERT INTO form_list_billing_delay_reason ("code","name","id","created_on","created_by","auto_name") VALUES ('1','Proof of eligibility unknown or unavailable',1,'2024-03-19 22:39:32',1,'1 - Proof of eligibility unknown or unavailable')
,('10','Administration delay in the prior approval process',2,'2024-03-19 22:39:32',1,'10 - Administration delay in the prior approval process')
,('11','Other',3,'2024-03-19 22:39:32',1,'11 - Other')
,('12','Received late with no exceptions',4,'2024-03-19 22:39:32',1,'12 - Received late with no exceptions')
,('13','Substantial damage by fire, etc to provider records',5,'2024-03-19 22:39:32',1,'13 - Substantial damage by fire, etc to provider records')
,('14','Theft, sabotage/other willful acts by employee',6,'2024-03-19 22:39:32',1,'14 - Theft, sabotage/other willful acts by employee')
,('2','Litigation',7,'2024-03-19 22:39:32',1,'2 - Litigation')
,('3','Authorization Delays',8,'2024-03-19 22:39:32',1,'3 - Authorization Delays')
,('4','Delay in certifying provider',9,'2024-03-19 22:39:32',1,'4 - Delay in certifying provider')
,('5','Delay in supplying billing forms',10,'2024-03-19 22:39:32',1,'5 - Delay in supplying billing forms')
,('6','Delay in delivery of custom-made appliances',11,'2024-03-19 22:39:32',1,'6 - Delay in delivery of custom-made appliances')
,('7','Third party processing delay',12,'2024-03-19 22:39:32',1,'7 - Third party processing delay')
,('8','Delay in eligibility determination',13,'2024-03-19 22:39:32',1,'8 - Delay in eligibility determination')
,('9','Original claims rejected or denied due to a reason unrelated to the billing limitation rules',14,'2024-03-19 22:39:32',1,'9 - Original claims rejected or denied due to a reason unrelated to the billing limitation rules')
;
ALTER SEQUENCE "form_list_billing_delay_reason_id_seq" RESTART WITH 15;
SET session_replication_role = DEFAULT;
 COMMIT;
