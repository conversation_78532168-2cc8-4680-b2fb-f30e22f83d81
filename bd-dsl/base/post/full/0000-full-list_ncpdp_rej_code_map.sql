BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_ncpdp_rej_code_map;
INSERT INTO form_list_ncpdp_rej_code_map ("code","name","field","id","created_on","created_by","auto_name") VALUES ('01', 'M/I Bin Number', '101-A1', 1,'2024-06-11 22:59:07',1,'1 - 101-A1')
,('04', 'M/I Processor Control Number', '104-A4', 2,'2024-06-11 22:59:07',1,'4 - 104-A4')
,('05', 'M/I Service Provider Number', '201-B1', 3,'2024-06-11 22:59:07',1,'5 - 201-B1')
,('06', 'M/I Group ID', '301-C1', 4,'2024-06-11 22:59:07',1,'6 - 301-C1')
,('07', 'M/I Cardholder ID', '302-C2', 5,'2024-06-11 22:59:07',1,'7 - 302-C2')
,('08', 'M/I Person Code', '303-C3', 6,'2024-06-11 22:59:07',1,'8 - 303-C3')
,('09', 'M/I Date Of Birth', '304-C4', 7,'2024-06-11 22:59:07',1,'9 - 304-C4')
,('10', 'M/I Patient Gender Code', '305-C5', 8,'2024-06-11 22:59:07',1,'10 - 305-C5')
,('1T', 'PCN Must Contain Processor/Payer Assigned Value', '104-A4', 9,'2024-06-11 22:59:07',1,'1T - 104-A4')
,('2H', 'M/I Compound Ingredient Modifier Code', '363-2H', 10,'2024-06-11 22:59:07',1,'2H - 363-2H')
,('2J', 'M/I Prescriber First Name', '364-2J', 11,'2024-06-11 22:59:07',1,'2J - 364-2J')
,('2K', 'M/I Prescriber Street Address', '365-2K', 12,'2024-06-11 22:59:07',1,'2K - 365-2K')
,('2M', 'M/I Prescriber City Address', '366-2M', 13,'2024-06-11 22:59:07',1,'2M - 366-2M')
,('2N', 'M/I Prescriber State/Province Address', '367-2N', 14,'2024-06-11 22:59:07',1,'2N - 367-2N')
,('2P', 'M/I Prescriber Zip/Postal Zone', '368-2P', 15,'2024-06-11 22:59:07',1,'2P - 368-2P')
,('2Q', 'M/I Additional Documentation Type ID', '369-2Q', 16,'2024-06-11 22:59:07',1,'2Q - 369-2Q')
,('2R', 'M/I Length of Need', '370-2R', 17,'2024-06-11 22:59:07',1,'2R - 370-2R')
,('2S', 'M/I Length of Need Qualifier', '371-2S', 18,'2024-06-11 22:59:07',1,'2S - 371-2S')
,('2U', 'M/I Request Status', '373-2U', 19,'2024-06-11 22:59:07',1,'2U - 373-2U')
,('2V', 'M/I Request Period Begin Date', '374-2V', 20,'2024-06-11 22:59:07',1,'2V - 374-2V')
,('27', 'Product Identifier not FDA/NSDE Listed', '407-D7', 21,'2024-06-11 22:59:07',1,'27 - 407-D7')
,('28', 'M/I Date Prescription Written', '414-DE', 22,'2024-06-11 22:59:07',1,'28 - 414-DE')
,('29', 'M/I Number Of Refills Authorized', '415-DF', 23,'2024-06-11 22:59:07',1,'29 - 415-DF')
,('3M', 'M/I Prescriber Phone Number', '498-PM', 24,'2024-06-11 22:59:07',1,'3M - 498-PM')
,('3N', 'M/I Prior Authorized Number-Assigned', '462-EV', 25,'2024-06-11 22:59:07',1,'3N - 462-EV')
,('3P', 'M/I Authorization Number', '462-EV', 26,'2024-06-11 22:59:07',1,'3P - 462-EV')
,('3Q', 'M/I Facility Name', '385-3Q', 27,'2024-06-11 22:59:07',1,'3Q - 385-3Q')
,('3R', 'Prior Authorization Not Required', '462-EV', 28,'2024-06-11 22:59:07',1,'3R - 462-EV')
,('3T', 'Active Prior Authorization Exists Resubmit At Expiration Of Prior Authorization', '462-EV', 29,'2024-06-11 22:59:07',1,'3T - 462-EV')
,('3U', 'M/I Facility Street Address', '386-3U', 30,'2024-06-11 22:59:07',1,'3U - 386-3U')
,('39', 'M/I Diagnosis Code', '424-DO', 31,'2024-06-11 22:59:07',1,'39 - 424-DO')
,('4B', 'M/I Question Number/Letter', '378-4B', 32,'2024-06-11 22:59:07',1,'4B - 378-4B')
,('4D', 'M/I Question Percent Response', '379-4D', 33,'2024-06-11 22:59:07',1,'4D - 379-4D')
,('4E', 'M/I Primary Care Provider Last Name', '470-4E', 34,'2024-06-11 22:59:07',1,'4E - 470-4E')
,('4G', 'M/I Question Date Response', '380-4G', 35,'2024-06-11 22:59:07',1,'4G - 380-4G')
,('4S', 'Compound Product ID Requires a Modifier Code', '363-2H', 36,'2024-06-11 22:59:07',1,'4S - 363-2H')
,('4X', 'M/I Patient Residence', '384-4X', 37,'2024-06-11 22:59:07',1,'4X - 384-4X')
,('4Y', 'Patient Residence Value Not Supported', '384-4X', 38,'2024-06-11 22:59:07',1,'4Y - 384-4X')
,('4Z', 'Place of Service Not Supported By Plan', '307-C7', 39,'2024-06-11 22:59:07',1,'4Z - 307-C7')
,('40', 'Pharmacy Not Contracted With Plan On Date Of Service', '401-D1', 40,'2024-06-11 22:59:07',1,'40 - 401-D1')
,('42', 'Plan''s Prescriber data base indicates the Prescriber ID Submitted is inactive or expired', '411-DB', 41,'2024-06-11 22:59:07',1,'42 - 411-DB')
,('463', 'Pharmacy not contracted in Assisted Living Network', '444-E9', 42,'2024-06-11 22:59:07',1,'463 - 444-E9')
,('5C', 'M/I Other Payer Coverage Type', '338-5C', 43,'2024-06-11 22:59:07',1,'5C - 338-5C')
,('5J', 'M/I Facility City Address', '388-5J', 44,'2024-06-11 22:59:07',1,'5J - 388-5J')
,('50', 'Non-Matched Pharmacy Number', '444-E9', 45,'2024-06-11 22:59:07',1,'50 - 444-E9')
,('51', 'Non-Matched Group ID', '301-C1', 46,'2024-06-11 22:59:07',1,'51 - 301-C1')
,('52', 'Non-Matched Cardholder ID', '302-C2', 47,'2024-06-11 22:59:07',1,'52 - 302-C2')
,('53', 'Non-Matched Person Code', '303-C3', 48,'2024-06-11 22:59:07',1,'53 - 303-C3')
,('510', 'Billing Entity Type Indicator Value Not Supported', '117-TR', 49,'2024-06-11 22:59:07',1,'510 - 117-TR')
,('511', 'CMS Part D Defined Qualified Facility Value Not Supported', '997-G2', 50,'2024-06-11 22:59:07',1,'511 - 997-G2')
,('512', 'Compound Code Value Not Supported', '406-D6', 51,'2024-06-11 22:59:07',1,'512 - 406-D6')
,('513', 'Compound Dispensing Unit Form Indicator Value Not Supported', '451-EG', 52,'2024-06-11 22:59:07',1,'513 - 451-EG')
,('514', 'Compound Ingredient Basis of Cost Determination Value Not Supported', '490-UE', 53,'2024-06-11 22:59:07',1,'514 - 490-UE')
,('515', 'Compound Product ID Qualifier Value Not Supported', '488-RE', 54,'2024-06-11 22:59:07',1,'515 - 488-RE')
,('516', 'Compound Type Value Not Supported', '996-G1', 55,'2024-06-11 22:59:07',1,'516 - 996-G1')
,('517', 'Coupon Type Value Not Supported', '485-KE', 56,'2024-06-11 22:59:07',1,'517 - 485-KE')
,('518', 'DUR Co-Agent ID Qualifier Value Not Supported', '475-J9', 57,'2024-06-11 22:59:07',1,'518 - 475-J9')
,('528', 'Length of Need Qualifier Value Not Supported', '371-2S', 58,'2024-06-11 22:59:07',1,'528 - 371-2S')
,('529', 'Level Of Service Value Not Supported', '418-DI', 59,'2024-06-11 22:59:07',1,'529 - 418-DI')
,('530', 'Measurement Dimension Value Not Supported', '496-H2', 60,'2024-06-11 22:59:07',1,'530 - 496-H2')
,('531', 'Measurement Unit Value Not Supported', '497-H3', 61,'2024-06-11 22:59:07',1,'531 - 497-H3')
,('532', 'Medicaid Indicator Value Not Supported', '360-2B', 62,'2024-06-11 22:59:07',1,'532 - 360-2B')
,('533', 'Originally Prescribed Product/Service ID Qualifier Value Not Supported', '453-EJ', 63,'2024-06-11 22:59:07',1,'533 - 453-EJ')
,('534', 'Other Amount Claimed Submitted Qualifier Value Not Supported', '479-H8', 64,'2024-06-11 22:59:07',1,'534 - 479-H8')
,('535', 'Other Coverage Code Value Not Supported', '308-C8', 65,'2024-06-11 22:59:07',1,'535 - 308-C8')
,('536', 'Other Payer-Patient Responsibility Amount Qualifier Value Not Supported', '351-NP', 66,'2024-06-11 22:59:07',1,'536 - 351-NP')
,('546', 'Primary Care Provider ID Qualifier Value Not Supported', '468-2E', 67,'2024-06-11 22:59:07',1,'546 - 468-2E')
,('547', 'Prior Authorization Type Code Value Not Supported', '461-EU', 68,'2024-06-11 22:59:07',1,'547 - 461-EU')
,('548', 'Provider Accept Assignment Indicator Value Not Supported', '361-2D', 69,'2024-06-11 22:59:07',1,'548 - 361-2D')
,('549', 'Provider ID Qualifier Value Not Supported', '465-EY', 70,'2024-06-11 22:59:07',1,'549 - 465-EY')
,('552', 'Route of Administration Value Not Supported', '995-E2', 71,'2024-06-11 22:59:07',1,'552 - 995-E2')
,('554', 'Special Packaging Indicator Value Not Supported', '429-DT', 72,'2024-06-11 22:59:07',1,'554 - 429-DT')
,('563', 'Pharmacy Not Contracted in Veterans Administration Network', '444-E9', 73,'2024-06-11 22:59:07',1,'563 - 444-E9')
,('564', 'Pharmacy Not Contracted in Military Network', '444-E9', 74,'2024-06-11 22:59:07',1,'564 - 444-E9')
,('577', 'M/I Other Payer ID', '340-7C', 75,'2024-06-11 22:59:07',1,'577 - 340-7C')
,('582', 'M/I Fill #', '403-D3', 76,'2024-06-11 22:59:07',1,'582 - 403-D3')
,('583', 'Provider ID Not Covered', '444-E9', 77,'2024-06-11 22:59:07',1,'583 - 444-E9')
,('585', 'Fill # Value Not Supported', '403-D3', 78,'2024-06-11 22:59:07',1,'585 - 403-D3')
,('599', 'Cardholder ID Matched But Last Name Did Not', '313-CD', 79,'2024-06-11 22:59:07',1,'599 - 313-CD')
,('6C', 'M/I Other Payer ID Qualifier', '339-6C', 80,'2024-06-11 22:59:07',1,'6C - 339-6C')
,('6D', 'M/I Facility Zip/Postal Zone', '389-6D', 81,'2024-06-11 22:59:07',1,'6D - 389-6D')
,('6E', 'M/I Other Payer Reject Code', '472-6E', 82,'2024-06-11 22:59:07',1,'6E - 472-6E')
,('6V', 'Multi-ingredient Compounds Not Supported', '406-D6', 83,'2024-06-11 22:59:07',1,'6V - 406-D6')
,('6Y', 'Not Authorized To Submit Electronically', '201-B1', 84,'2024-06-11 22:59:07',1,'6Y - 201-B1')
,('6Z', 'Provider Not Eligible To Perform Service/Dispense Product', '201-B1', 85,'2024-06-11 22:59:07',1,'6Z - 201-B1')
,('60', 'Product/Service Not Covered For Patient Age', '304-C4', 86,'2024-06-11 22:59:07',1,'60 - 304-C4')
,('61', 'Product/Service Not Covered For Patient Gender', '305-C5', 87,'2024-06-11 22:59:07',1,'61 - 305-C5')
,('62', 'Patient/Card Holder ID Name Mismatch', '302-C2', 88,'2024-06-11 22:59:07',1,'62 - 302-C2')
,('618', 'Plan''s Prescriber Data Base Indicates The Submitted Prescriber’s DEA Does Not Allow This Drug DEA Schedule', '411-DB', 89,'2024-06-11 22:59:07',1,'618 - 411-DB')
,('619', 'Prescriber Type 1 NPI Required', '411-DB', 90,'2024-06-11 22:59:07',1,'619 - 411-DB')
,('620', 'This Product/Service May Be Covered Under Medicare Part D', '407-D7', 91,'2024-06-11 22:59:07',1,'620 - 407-D7')
,('650', 'Fill Date Greater Than 60 Days From CII Date Prescription Written (414-DE).', '414-DE', 92,'2024-06-11 22:59:07',1,'650 - 414-DE')
,('7M', 'Discrepancy Between Other Coverage Code And Other Coverage Information On File', '308-C8', 93,'2024-06-11 22:59:07',1,'7M - 308-C8')
,('7N', 'Patient ID Qualifier Value Not Supported', '331-CX', 94,'2024-06-11 22:59:07',1,'7N - 331-CX')
,('7Q', 'Other Payer ID Qualifier Value Not Supported', '339-6C', 95,'2024-06-11 22:59:07',1,'7Q - 339-6C')
,('7S', 'Other Payer Amount Paid Qualifier Value Not Supported', '342-HC', 96,'2024-06-11 22:59:07',1,'7S - 342-HC')
,('7T', 'Quantity Intended To Be Dispensed Required For Partial Fill Transaction', '344-HF', 97,'2024-06-11 22:59:07',1,'7T - 344-HF')
,('74', 'Other Carrier Payment Meets Or Exceeds Payable', '431-DV', 98,'2024-06-11 22:59:07',1,'74 - 431-DV')
,('75', 'Prior Authorization Required', '462-EV', 99,'2024-06-11 22:59:07',1,'75 - 462-EV')
,('76', 'Plan Limitations Exceeded', '442-E7', 100,'2024-06-11 22:59:07',1,'76 - 442-E7')
,('77', 'Discontinued Product/Service ID Number', '407-D7', 101,'2024-06-11 22:59:07',1,'77 - 407-D7')
,('78', 'Cost Exceeds Maximum', '409-D9', 102,'2024-06-11 22:59:07',1,'78 - 409-D9')
,('79', 'Refill Too Soon', '401-D1', 103,'2024-06-11 22:59:07',1,'79 - 401-D1')
,('771', 'Compound contains unidentifiable ingredient(s); Submission Clarification Code override not allowed', '489-TE', 104,'2024-06-11 22:59:07',1,'771 - 489-TE')
,('772', 'Compound not payable due to non-covered ingredient(s); Submission Clarification Code override not allowed', '489-TE', 105,'2024-06-11 22:59:07',1,'772 - 489-TE')
,('773', 'Prescriber Is Not Listed On Medicare Enrollment File', '411-DB', 106,'2024-06-11 22:59:07',1,'773 - 411-DB')
,('774', 'Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service', '411-DB', 107,'2024-06-11 22:59:07',1,'774 - 411-DB')
,('775', 'Pharmacy not listed within Medicare Fee For Service active enrollment file', '444-E9', 108,'2024-06-11 22:59:07',1,'775 - 444-E9')
,('8G', 'Product/Service ID (407-D7) Must Be A Single Zero For Compounds', '407-D7', 109,'2024-06-11 22:59:07',1,'8G - 407-D7')
,('8H', 'Product/Service Only Covered On Compound Claim', '407-D7', 110,'2024-06-11 22:59:07',1,'8H - 407-D7')
,('8J', 'Incorrect Product/Service ID For Processor/Payer', '407-D7', 111,'2024-06-11 22:59:07',1,'8J - 407-D7')
,('8K', 'DAW Code Value Not Supported', '408-D8', 112,'2024-06-11 22:59:07',1,'8K - 408-D8')
,('8M', 'Sum Of Compound Ingredient Costs Does Not Equal Ingredient Cost Submitted', '409-D9', 113,'2024-06-11 22:59:07',1,'8M - 409-D9')
,('8N', 'Future Date Prescription Written Not Allowed', '414-DE', 114,'2024-06-11 22:59:07',1,'8N - 414-DE')
,('8P', 'Date Written Different On Previous Filling', '414-DE', 115,'2024-06-11 22:59:07',1,'8P - 414-DE')
,('8Q', 'Excessive Refills Authorized', '415-DF', 116,'2024-06-11 22:59:07',1,'8Q - 415-DF')
,('8R', 'Submission Clarification Code Value Not Supported', '420-DK', 117,'2024-06-11 22:59:07',1,'8R - 420-DK')
,('82', 'Claim Is Post-Dated', '401-D1', 118,'2024-06-11 22:59:07',1,'82 - 401-D1')
,('816', 'Pharmacy Benefit Exclusion,May Be Covered Under Patient’s Medical Benefit', '407-D7', 119,'2024-06-11 22:59:07',1,'816 - 407-D7')
,('817', 'Pharmacy Benefit Exclusion,Covered Under Patient’s Medical Benefit', '407-D7', 120,'2024-06-11 22:59:07',1,'817 - 407-D7')
,('822', 'Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.', '407-D7', 121,'2024-06-11 22:59:07',1,'822 - 407-D7')
,('823', 'Drug Is Beneficiary’s Liability -Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.', '407-D7', 122,'2024-06-11 22:59:07',1,'823 - 407-D7')
,('831', 'Product Service ID Carve-Out,Bill Medicaid Fee For Service', '407-D7', 123,'2024-06-11 22:59:07',1,'831 - 407-D7')
,('832', 'Prescriber NPI Not Found, therefore NPI Active Status, MEDICARE Enrollment, Prescriptive Authority Could Not Be Validated', '411-DB', 124,'2024-06-11 22:59:07',1,'832 - 411-DB')
,('837', 'Facility ID Value Not Supported', '336-8C', 125,'2024-06-11 22:59:07',1,'837 - 336-8C')
,('878', 'Service Provider ID Not Found On NPPES File', '202-B2', 126,'2024-06-11 22:59:07',1,'878 - 202-B2')
,('879', 'Service Provider ID Excluded From Receiving CMS Enrollment Data', '202-B2', 127,'2024-06-11 22:59:07',1,'879 - 202-B2')
,('9B', 'Reason For Service Code Value Not Supported', '439-E4', 128,'2024-06-11 22:59:07',1,'9B - 439-E4')
,('9C', 'Professional Service Code Value Not Supported', '440-E5', 129,'2024-06-11 22:59:07',1,'9C - 440-E5')
,('9D', 'Result Of Service Code Value Not Supported', '441-E6', 130,'2024-06-11 22:59:07',1,'9D - 441-E6')
,('9E', 'Quantity Does Not Match Dispensing Unit', '442-E7', 131,'2024-06-11 22:59:07',1,'9E - 442-E7')
,('9G', 'Quantity Dispensed Exceeds Maximum Allowed', '442-E7', 132,'2024-06-11 22:59:07',1,'9G - 442-E7')
,('9H', 'Quantity Not Valid For Product/Service ID Submitted', '442-E7', 133,'2024-06-11 22:59:07',1,'9H - 442-E7')
,('9J', 'Future Other Payer Date Not Allowed', '443-E8', 134,'2024-06-11 22:59:07',1,'9J - 443-E8')
,('9U', 'Provider ID Qualifier Submitted Not Covered', '465-EY', 135,'2024-06-11 22:59:07',1,'9U - 465-EY')
,('9V', 'Prescriber ID Qualifier Submitted Not Covered', '466-EZ', 136,'2024-06-11 22:59:07',1,'9V - 466-EZ')
,('9X', 'Coupon Type Submitted Not Covered', '485-KE', 137,'2024-06-11 22:59:07',1,'9X - 485-KE')
,('9Y', 'Compound Product ID Qualifier Submitted Not Covered', '488-RE', 138,'2024-06-11 22:59:07',1,'9Y - 488-RE')
,('9Z', 'Duplicate Product ID In Compound', '489-TE', 139,'2024-06-11 22:59:07',1,'9Z - 489-TE')
,('AD', 'Billing Provider Not Eligible To Bill This Claim Type', '201-B1', 140,'2024-06-11 22:59:07',1,'AD - 201-B1')
,('AG', 'Days Supply Limitation For Product/Service', '405-D5', 141,'2024-06-11 22:59:07',1,'AG - 405-D5')
,('AH', 'Unit Dose Packaging Only Payable For Nursing Home Recipients', '407-D7', 142,'2024-06-11 22:59:07',1,'AH - 407-D7')
,('AJ', 'Generic Drug Required', '407-D7', 143,'2024-06-11 22:59:07',1,'AJ - 407-D7')
,('A1', 'ID Submitted is associated with a Sanctioned Prescriber', '411-DB', 144,'2024-06-11 22:59:07',1,'A1 - 411-DB')
,('A6', 'This Product/Service May Be Covered Under Medicare Part B', '407-D7', 145,'2024-06-11 22:59:07',1,'A6 - 407-D7')
,('A7', 'M/I Internal Control Number', '993-A7', 146,'2024-06-11 22:59:07',1,'A7 - 993-A7')
,('BA', 'Compound Basis of Cost Determination Submitted Not Covered', '490-UE', 147,'2024-06-11 22:59:07',1,'BA - 490-UE')
,('BB', 'Diagnosis Code Qualifier Submitted Not Covered', '492-WE', 148,'2024-06-11 22:59:07',1,'BB - 492-WE')
,('BC', 'Future Measurement Date Not Allowed', '494-ZE', 149,'2024-06-11 22:59:07',1,'BC - 494-ZE')
,('BE', 'M/I Professional Service Fee Submitted', '477-BE', 150,'2024-06-11 22:59:07',1,'BE - 477-BE')
,('BM', 'M/I Narrative Message', '390-BM', 151,'2024-06-11 22:59:07',1,'BM - 390-BM')
,('B2', 'M/I Service Provider ID Qualifier', '202-B2', 152,'2024-06-11 22:59:07',1,'B2 - 202-B2')
,('CN', 'M/I Patient City Address', '323-CN', 153,'2024-06-11 22:59:07',1,'CN - 323-CN')
,('CO', 'M/I Patient State/Province Address', '324-CO', 154,'2024-06-11 22:59:07',1,'CO - 324-CO')
,('CP', 'M/I Patient Zip/Postal Zone', '325-CP', 155,'2024-06-11 22:59:07',1,'CP - 325-CP')
,('CQ', 'M/I Patient Phone Number', '326-CQ', 156,'2024-06-11 22:59:07',1,'CQ - 326-CQ')
,('CX', 'M/I Patient ID Qualifier', '331-CX', 157,'2024-06-11 22:59:07',1,'CX - 331-CX')
,('CY', 'M/I Patient ID', '332-CY', 158,'2024-06-11 22:59:07',1,'CY - 332-CY')
,('CZ', 'M/I Employer ID', '333-CZ', 159,'2024-06-11 22:59:07',1,'CZ - 333-CZ')
,('DC', 'M/I Dispensing Fee Submitted', '412-DC', 160,'2024-06-11 22:59:07',1,'DC - 412-DC')
,('DN', 'M/I Basis Of Cost Determination', '423-DN', 161,'2024-06-11 22:59:07',1,'DN - 423-DN')
,('DQ', 'M/I Usual And Customary Charge', '426-DQ', 162,'2024-06-11 22:59:07',1,'DQ - 426-DQ')
,('DR', 'M/I Prescriber Last Name', '427-DR', 163,'2024-06-11 22:59:07',1,'DR - 427-DR')
,('DT', 'M/I Special Packaging Indicator', '429-DT', 164,'2024-06-11 22:59:07',1,'DT - 429-DT')
,('EJ', 'M/I Originally Prescribed Product/Service ID Qualifier', '453-EJ', 165,'2024-06-11 22:59:07',1,'EJ - 453-EJ')
,('EK', 'M/I Scheduled Prescription ID Number', '454-EK', 166,'2024-06-11 22:59:07',1,'EK - 454-EK')
,('EM', 'M/I Prescription/Service Reference Number Qualifier', '455-EM', 167,'2024-06-11 22:59:07',1,'EM - 455-EM')
,('EN', 'M/I Associated Prescription/Service Reference Number', '456-EN', 168,'2024-06-11 22:59:07',1,'EN - 456-EN')
,('EP', 'M/I Associated Prescription/Service Date', '457-EP', 169,'2024-06-11 22:59:07',1,'EP - 457-EP')
,('ER', 'M/I Procedure Modifier Code', '459-ER', 170,'2024-06-11 22:59:07',1,'ER - 459-ER')
,('ET', 'M/I Quantity Prescribed', '460-ET', 171,'2024-06-11 22:59:07',1,'ET - 460-ET')
,('EU', 'M/I Prior Authorization Type Code', '461-EU', 172,'2024-06-11 22:59:07',1,'EU - 461-EU')
,('EV', 'M/I Prior Authorization ID Submitted', '462-EV', 173,'2024-06-11 22:59:07',1,'EV - 462-EV')
,('E7', 'M/I Quantity Dispensed', '442-E7', 174,'2024-06-11 22:59:07',1,'E7 - 442-E7')
,('E8', 'M/I Other Payer Date', '443-E8', 175,'2024-06-11 22:59:07',1,'E8 - 443-E8')
,('E9', 'M/I Provider ID', '444-E9', 176,'2024-06-11 22:59:07',1,'E9 - 444-E9')
,('FO', 'M/I Plan ID', '524-FO', 177,'2024-06-11 22:59:07',1,'FO - 524-FO')
,('GE', 'M/I Percentage Sales Tax Amount Submitted', '482-GE', 178,'2024-06-11 22:59:07',1,'GE - 482-GE')
,('G1', 'M/I Compound Type', '996-G1', 179,'2024-06-11 22:59:07',1,'G1 - 996-G1')
,('G2', 'M/I CMS Part D Defined Qualified Facility', '997-G2', 180,'2024-06-11 22:59:07',1,'G2 - 997-G2')
,('G6', 'Pharmacy Not Contracted in Specialty Network', '201-B1', 181,'2024-06-11 22:59:07',1,'G6 - 201-B1')
,('G7', 'Pharmacy Not Contracted in Home Infusion Network', '201-B1', 182,'2024-06-11 22:59:07',1,'G7 - 201-B1')
,('G8', 'Pharmacy Not Contracted in Long Term Care Network', '201-B1', 183,'2024-06-11 22:59:07',1,'G8 - 201-B1')
,('H2', 'M/I Measurement Dimension', '496-H2', 184,'2024-06-11 22:59:07',1,'H2 - 496-H2')
,('H3', 'M/I Measurement Unit', '497-H3', 185,'2024-06-11 22:59:07',1,'H3 - 497-H3')
,('H4', 'M/I Measurement Value', '499-H4', 186,'2024-06-11 22:59:07',1,'H4 - 499-H4')
,('H6', 'M/I DUR Co-Agent ID', '476-H6', 187,'2024-06-11 22:59:07',1,'H6 - 476-H6')
,('H8', 'M/I Other Amount Claimed Submitted Qualifier', '479-H8', 188,'2024-06-11 22:59:07',1,'H8 - 479-H8')
,('H9', 'M/I Other Amount Claimed Submitted', '480-H9', 189,'2024-06-11 22:59:07',1,'H9 - 480-H9')
,('JE', 'M/I Percentage Sales Tax Basis Submitted', '484-JE', 190,'2024-06-11 22:59:07',1,'JE - 484-JE')
,('J9', 'M/I DUR Co-Agent ID Qualifier', '475-J9', 191,'2024-06-11 22:59:07',1,'J9 - 475-J9')
,('KE', 'M/I Coupon Type', '485-KE', 192,'2024-06-11 22:59:07',1,'KE - 485-KE')
,('MR', 'Product Not On Formulary', '407-D7', 193,'2024-06-11 22:59:07',1,'MR - 407-D7')
,('MT', 'M/I Patient Assignment Indicator (Direct Member Reimbursement Indicator)', '391-MT', 194,'2024-06-11 22:59:07',1,'MT - 391-MT')
,('MV', 'M/I Benefit Stage Qualifier', '393-MV', 195,'2024-06-11 22:59:07',1,'MV - 393-MV')
,('MW', 'M/I Benefit Stage Amount', '394-MW', 196,'2024-06-11 22:59:07',1,'MW - 394-MW')
,('N5', 'M/I Medicaid ID Number', '115-N5', 197,'2024-06-11 22:59:07',1,'N5 - 115-N5')
,('N7', 'Use Prior Authorization ID Provided During Transition Period', '462-EV', 198,'2024-06-11 22:59:07',1,'N7 - 462-EV')
,('N8', 'Use Prior Authorization ID Provided For Emergency Fill', '462-EV', 199,'2024-06-11 22:59:07',1,'N8 - 462-EV')
,('N9', 'Use Prior Authorization ID Provided For Level of Care Change', '462-EV', 200,'2024-06-11 22:59:07',1,'N9 - 462-EV')
,('PA', 'PA Exhausted/Not Renewable', '462-EV', 201,'2024-06-11 22:59:07',1,'PA - 462-EV')
,('PV', 'Non-Matched Associated Prescription/Service Date', '401-D1', 202,'2024-06-11 22:59:07',1,'PV - 401-D1')
,('PW', 'Employer ID Not Covered', '333-CZ', 203,'2024-06-11 22:59:07',1,'PW - 333-CZ')
,('PX', 'Other Payer ID Not Covered', '340-7C', 204,'2024-06-11 22:59:07',1,'PX - 340-7C')
,('PY', 'Non-Matched Unit Form/Route of Administration', '995-E2', 205,'2024-06-11 22:59:07',1,'PY - 995-E2')
,('RB', 'Multiple Partials Not Allowed', '343-HD', 206,'2024-06-11 22:59:07',1,'RB - 343-HD')
,('RC', 'Different Drug Entity Between Partial & Completion', '407-D7', 207,'2024-06-11 22:59:07',1,'RC - 407-D7')
,('RD', 'Mismatched Cardholder/Group ID-Partial To Completion', '301-C1', 208,'2024-06-11 22:59:07',1,'RD - 301-C1')
,('RE', 'M/I Compound Product ID Qualifier', '488-RE', 209,'2024-06-11 22:59:07',1,'RE - 488-RE')
,('RS', 'M/I Associated Prescription/Service Date On Partial Transaction', '457-EP', 210,'2024-06-11 22:59:07',1,'RS - 457-EP')
,('RT', 'M/I Associated Prescription/Service Reference Number On Partial Transaction', '456-EN', 211,'2024-06-11 22:59:07',1,'RT - 456-EN')
,('R9', 'Value In Gross Amount Due Does Not Follow Pricing Formula', '430-DU', 212,'2024-06-11 22:59:07',1,'R9 - 430-DU')
,('TE', 'Missing/Invalid Compound Product ID', '489-TE', 213,'2024-06-11 22:59:07',1,'TE - 489-TE')
,('TQ', 'Dosage Exceeds Product Labeling Limit', '442-E7', 214,'2024-06-11 22:59:07',1,'TQ - 442-E7')
,('TR', 'M/I Billing Entity Type Indicator', '117-TR', 215,'2024-06-11 22:59:07',1,'TR - 117-TR')
,('TS', 'M/I Pay To Qualifier', '118-TS', 216,'2024-06-11 22:59:07',1,'TS - 118-TS')
,('TT', 'M/I Pay To ID', '119-TT', 217,'2024-06-11 22:59:07',1,'TT - 119-TT')
,('TU', 'M/I Pay To Name', '120-TU', 218,'2024-06-11 22:59:07',1,'TU - 120-TU')
,('TV', 'M/I Pay To Street Address', '121-TV', 219,'2024-06-11 22:59:07',1,'TV - 121-TV')
,('TW', 'M/I Pay To City Address', '122-TW', 220,'2024-06-11 22:59:07',1,'TW - 122-TW')
,('TX', 'M/I Pay to State/ Province Address', '123-TX', 221,'2024-06-11 22:59:07',1,'TX - 123-TX')
,('UU', 'DAW 0 cannot be submitted on a multi-source drug with available generics', '408-D8', 222,'2024-06-11 22:59:07',1,'UU - 408-D8')
,('UZ', 'Other Payer Coverage Type (338-5C) required on reversals to downstream payers. Resubmit reversal with this field.', '338-5C', 223,'2024-06-11 22:59:07',1,'UZ - 338-5C')
,('U7', 'M/I Pharmacy Service Type', '147-U7', 224,'2024-06-11 22:59:07',1,'U7 - 147-U7')
,('VA', 'Pay To Qualifier  Value Not Supported', '118-TS', 225,'2024-06-11 22:59:07',1,'VA - 118-TS')
,('VB', 'Generic Equivalent Product ID Qualifier Value Not Supported', '125-TZ', 226,'2024-06-11 22:59:07',1,'VB - 125-TZ')
,('VC', 'Pharmacy Service Type Value Not Supported', '147-U7', 227,'2024-06-11 22:59:07',1,'VC - 147-U7')
,('WE', 'M/I Diagnosis Code Qualifier', '492-WE', 228,'2024-06-11 22:59:07',1,'WE - 492-WE')
,('YR', 'M/I Patient ID Associated State/Province Address', '324-CO', 229,'2024-06-11 22:59:07',1,'YR - 324-CO')
,('Y7', 'M/I Associated Prescription/Service Provider ID Qualifier', '202-B2', 230,'2024-06-11 22:59:07',1,'Y7 - 202-B2')
,('Y8', 'M/I Associated Prescription/Service Provider ID', '201-B1', 231,'2024-06-11 22:59:07',1,'Y8 - 201-B1')
,('ZC', 'Associated Prescription/Service Provider ID Qualifier Value Not Supported For Processor/Payer', '202-B2', 232,'2024-06-11 22:59:07',1,'ZC - 202-B2')
,('ZD', 'Associated Prescription/Service Reference Number Qualifier Value Not Supported', '455-EM', 233,'2024-06-11 22:59:07',1,'ZD - 455-EM')
,('ZE', 'M/I Measurement Date', '494-ZE', 234,'2024-06-11 22:59:07',1,'ZE - 494-ZE')
,('ZK', 'M/I Prescriber ID Associated State/Province Address', '367-2N', 235,'2024-06-11 22:59:07',1,'ZK - 367-2N')
,('11', 'M/I Patient Relationship Code', '306-C6', 236,'2024-06-11 22:59:07',1,'11 - 306-C6')
,('12', 'M/I Place of Service', '307-C7', 237,'2024-06-11 22:59:07',1,'12 - 307-C7')
,('13', 'M/I Other Coverage Code', '308-C8', 238,'2024-06-11 22:59:07',1,'13 - 308-C8')
,('14', 'M/I Eligibility Clarification Code', '309-C9', 239,'2024-06-11 22:59:07',1,'14 - 309-C9')
,('15', 'M/I Date of Service', '401-D1', 240,'2024-06-11 22:59:07',1,'15 - 401-D1')
,('16', 'M/I Prescription/Service Reference Number', '402-D2', 241,'2024-06-11 22:59:07',1,'16 - 402-D2')
,('17', 'M/I Fill #', '403-D3', 242,'2024-06-11 22:59:07',1,'17 - 403-D3')
,('19', 'M/I Days Supply', '405-D5', 243,'2024-06-11 22:59:07',1,'19 - 405-D5')
,('1W', 'Multi-Ingredient Compound Must Be A Single Transaction', '406-D6', 244,'2024-06-11 22:59:07',1,'1W - 406-D6')
,('2A', 'M/I Medigap ID', '359-2A', 245,'2024-06-11 22:59:07',1,'2A - 359-2A')
,('2B', 'M/I Medicaid Indicator', '360-2B', 246,'2024-06-11 22:59:07',1,'2B - 360-2B')
,('2C', 'M/I Pregnancy Indicator', '335-2C', 247,'2024-06-11 22:59:07',1,'2C - 335-2C')
,('2D', 'M/I Provider Accept Assignment Indicator', '361-2D', 248,'2024-06-11 22:59:07',1,'2D - 361-2D')
,('2E', 'M/I Primary Care Provider ID Qualifier', '468-2E', 249,'2024-06-11 22:59:07',1,'2E - 468-2E')
,('20', 'M/I Compound Code', '406-D6', 250,'2024-06-11 22:59:07',1,'20 - 406-D6')
,('21', 'M/I Product/Service ID', '407-D7', 251,'2024-06-11 22:59:07',1,'21 - 407-D7')
,('22', 'M/I Dispense As Written (DAW)/Product Selection Code', '408-D8', 252,'2024-06-11 22:59:07',1,'22 - 408-D8')
,('23', 'M/I Ingredient Cost Submitted', '409-D9', 253,'2024-06-11 22:59:07',1,'23 - 409-D9')
,('25', 'M/I Prescriber ID', '411-DB', 254,'2024-06-11 22:59:07',1,'25 - 411-DB')
,('26', 'M/I Unit Of Measure', '600-28', 255,'2024-06-11 22:59:07',1,'26 - 600-28')
,('3V', 'M/I Facility State/Province Address', '387-3V', 256,'2024-06-11 22:59:07',1,'3V - 387-3V')
,('3W', 'Prior Authorization In Process', '462-EV', 257,'2024-06-11 22:59:07',1,'3W - 462-EV')
,('3X', 'Authorization Number Not Found', '462-EV', 258,'2024-06-11 22:59:07',1,'3X - 462-EV')
,('3Y', 'Prior Authorization Denied', '462-EV', 259,'2024-06-11 22:59:07',1,'3Y - 462-EV')
,('32', 'M/I Level Of Service', '418-DI', 260,'2024-06-11 22:59:07',1,'32 - 418-DI')
,('33', 'M/I Prescription Origin Code', '419-DJ', 261,'2024-06-11 22:59:07',1,'33 - 419-DJ')
,('34', 'M/I Submission Clarification Code', '420-DK', 262,'2024-06-11 22:59:07',1,'34 - 420-DK')
,('35', 'M/I Primary Care Provider ID', '421-DL', 263,'2024-06-11 22:59:07',1,'35 - 421-DL')
,('38', 'M/I Basis Of Cost Determination', '423-DN', 264,'2024-06-11 22:59:07',1,'38 - 423-DN')
,('4H', 'M/I Question Dollar Amount Response', '381-4H', 265,'2024-06-11 22:59:07',1,'4H - 381-4H')
,('4J', 'M/I Question Numeric Response', '382-4J', 266,'2024-06-11 22:59:07',1,'4J - 382-4J')
,('4K', 'M/I Question Alphanumeric Response', '383-4K', 267,'2024-06-11 22:59:07',1,'4K - 383-4K')
,('4P', 'Question Number/Letter Not Valid for Identified Document', '378-4B', 268,'2024-06-11 22:59:07',1,'4P - 378-4B')
,('4Q', 'Question Response Not Appropriate for Question Number/Letter', '378-4B', 269,'2024-06-11 22:59:07',1,'4Q - 378-4B')
,('4R', 'Required Question Number/Letter Response for Indicated Document Missing', '378-4B', 270,'2024-06-11 22:59:07',1,'4R - 378-4B')
,('43', 'Plan''s Prescriber data base indicates the associated DEA to submitted Prescriber ID is inactive', '411-DB', 271,'2024-06-11 22:59:07',1,'43 - 411-DB')
,('44', 'Plan''s Prescriber data base indicates the associated DEA to submitted Prescriber ID Is not found', '411-DB', 272,'2024-06-11 22:59:07',1,'44 - 411-DB')
,('46', 'Plan''s Prescriber data base indicates associated DEA to submitted Prescriber ID does not allow this drug DEA Schedule', '411-DB', 273,'2024-06-11 22:59:07',1,'46 - 411-DB')
,('54', 'Non-Matched Product/Service ID Number', '407-D7', 274,'2024-06-11 22:59:07',1,'54 - 407-D7')
,('55', 'Non-Matched Product Package Size', '407-D7', 275,'2024-06-11 22:59:07',1,'55 - 407-D7')
,('56', 'Non-Matched Prescriber ID', '411-DB', 276,'2024-06-11 22:59:07',1,'56 - 411-DB')
,('58', 'Non-Matched Primary Prescriber', '421-DL', 277,'2024-06-11 22:59:07',1,'58 - 421-DL')
,('504', 'Benefit Stage Qualifier Value Not Supported', '393-MV', 278,'2024-06-11 22:59:07',1,'504 - 393-MV')
,('505', 'Other Payer Coverage Type Value Not Supported', '338-5C', 279,'2024-06-11 22:59:07',1,'505 - 338-5C')
,('506', 'Prescription/Service Reference Number Qualifier Value Not Supported', '455-EM', 280,'2024-06-11 22:59:07',1,'506 - 455-EM')
,('507', 'Additional Documentation Type ID Value Not Supported', '369-2Q', 281,'2024-06-11 22:59:07',1,'507 - 369-2Q')
,('519', 'DUR/PPS Level Of Effort Value Not Supported', '474-8E', 282,'2024-06-11 22:59:07',1,'519 - 474-8E')
,('520', 'Delay Reason Code Value Not Supported', '357-NV', 283,'2024-06-11 22:59:07',1,'520 - 357-NV')
,('521', 'Diagnosis Code Qualifier Value Not Supported', '492-WE', 284,'2024-06-11 22:59:07',1,'521 - 492-WE')
,('522', 'Dispensing Status Value Not Supported', '343-HD', 285,'2024-06-11 22:59:07',1,'522 - 343-HD')
,('523', 'Eligibility Clarification Code Value Not Supported', '309-C9', 286,'2024-06-11 22:59:07',1,'523 - 309-C9')
,('524', 'Employer State/Province Address Value Not Supported', '318-CI', 287,'2024-06-11 22:59:07',1,'524 - 318-CI')
,('525', 'Facility State/Province Address Value Not Supported', '387-3V', 288,'2024-06-11 22:59:07',1,'525 - 387-3V')
,('527', 'Intermediary Authorization Type ID Value Not Supported', '461-EU', 289,'2024-06-11 22:59:07',1,'527 - 461-EU')
,('537', 'Patient Assignment Indicator (Direct Member Reimbursement Indicator) Value Not Supported', '391-MT', 290,'2024-06-11 22:59:07',1,'537 - 391-MT')
,('538', 'Patient Gender Code Value Not Supported', '305-C5', 291,'2024-06-11 22:59:07',1,'538 - 305-C5')
,('539', 'Patient State/Province Address Value Not Supported', '324-CO', 292,'2024-06-11 22:59:07',1,'539 - 324-CO')
,('540', 'Pay to State/Province Address Value Not Supported', '123-TX', 293,'2024-06-11 22:59:07',1,'540 - 123-TX')
,('541', 'Percentage Sales Tax Basis Submitted Value Not Supported', '484-JE', 294,'2024-06-11 22:59:07',1,'541 - 484-JE')
,('542', 'Pregnancy Indicator Value Not Supported', '335-2C', 295,'2024-06-11 22:59:07',1,'542 - 335-2C')
,('543', 'Prescriber ID Qualifier Value Not Supported', '466-EZ', 296,'2024-06-11 22:59:07',1,'543 - 466-EZ')
,('544', 'Prescriber State/Province Address Value Not Supported', '367-2N', 297,'2024-06-11 22:59:07',1,'544 - 367-2N')
,('545', 'Prescription Origin Code Value Not Supported', '419-DJ', 298,'2024-06-11 22:59:07',1,'545 - 419-DJ')
,('556', 'Unit Of Measure Value Not Supported', '600-28', 299,'2024-06-11 22:59:07',1,'556 - 600-28')
,('557', 'COB Segment Present On A Non-COB Claim', '308-C8', 300,'2024-06-11 22:59:07',1,'557 - 308-C8')
,('559', 'ID Submitted is associated with a Sanctioned Pharmacy', '201-B1', 301,'2024-06-11 22:59:07',1,'559 - 201-B1')
,('560', 'Pharmacy Not Contracted in Retail Network', '201-B1', 302,'2024-06-11 22:59:07',1,'560 - 201-B1')
,('561', 'Pharmacy Not Contracted in Mail Order Network', '201-B1', 303,'2024-06-11 22:59:07',1,'561 - 201-B1')
,('562', 'Pharmacy Not Contracted in Hospice Network', '201-B1', 304,'2024-06-11 22:59:07',1,'562 - 201-B1')
,('571', 'Patient ID Associated State/Province Address Value Not Supported', '324-CO', 305,'2024-06-11 22:59:07',1,'571 - 324-CO')
,('572', 'Medigap ID Not Covered', '359-2A', 306,'2024-06-11 22:59:07',1,'572 - 359-2A')
,('574', 'Compound Ingredient Modifier Code Not Covered', '363-2H', 307,'2024-06-11 22:59:07',1,'574 - 363-2H')
,('586', 'Facility ID Not Covered', '336-8C', 308,'2024-06-11 22:59:07',1,'586 - 336-8C')
,('587', 'Carrier ID Not Covered', '327-CR', 309,'2024-06-11 22:59:07',1,'587 - 327-CR')
,('589', 'Patient ID Not Covered', '332-CY', 310,'2024-06-11 22:59:07',1,'589 - 332-CY')
,('590', 'Compound Dosage Form Not Covered', '450-EF', 311,'2024-06-11 22:59:07',1,'590 - 450-EF')
,('591', 'Plan ID Not Covered', '524-FO', 312,'2024-06-11 22:59:07',1,'591 - 524-FO')
,('592', 'DUR Co-Agent ID Not Covered', '476-H6', 313,'2024-06-11 22:59:07',1,'592 - 476-H6')
,('593', 'M/I Date of Service', '401-D1', 314,'2024-06-11 22:59:07',1,'593 - 401-D1')
,('594', 'Pay To ID Not Covered', '119-TT', 315,'2024-06-11 22:59:07',1,'594 - 119-TT')
,('595', 'Associated Prescription/Service Provider ID Not Covered', '201-B1', 316,'2024-06-11 22:59:07',1,'595 - 201-B1')
,('63', 'Product/Service ID Not Covered For Institutionalized Patient', '407-D7', 317,'2024-06-11 22:59:07',1,'63 - 407-D7')
,('64', 'Claim Submitted Does Not Match Prior Authorization', '462-EV', 318,'2024-06-11 22:59:07',1,'64 - 462-EV')
,('65', 'Patient Is Not Covered', '332-CY', 319,'2024-06-11 22:59:07',1,'65 - 332-CY')
,('66', 'Patient Age Exceeds Maximum Age', '304-C4', 320,'2024-06-11 22:59:07',1,'66 - 304-C4')
,('67', 'Filled Before Coverage Effective', '401-D1', 321,'2024-06-11 22:59:07',1,'67 - 401-D1')
,('68', 'Filled After Coverage Expired', '401-D1', 322,'2024-06-11 22:59:07',1,'68 - 401-D1')
,('69', 'Filled After Coverage Terminated', '401-D1', 323,'2024-06-11 22:59:07',1,'69 - 401-D1')
,('600', 'Coverage Outside Submitted Date Of Service', '401-D1', 324,'2024-06-11 22:59:07',1,'600 - 401-D1')
,('608', 'Step Therapy,Alternate Drug Therapy Required Prior To Use Of Submitted Product Service ID', '407-D7', 325,'2024-06-11 22:59:07',1,'608 - 407-D7')
,('615', 'Compound Ingredient Basis Of Cost Determination Value 14 Required When Compound Ingredient Quantity Is 0 But Cost Is Greater Than $0', '490-UE', 326,'2024-06-11 22:59:07',1,'615 - 490-UE')
,('616', 'Submission Clarification Code 8 Required When Compound Ingredient Quantity Is 0', '448-ED', 327,'2024-06-11 22:59:07',1,'616 - 448-ED')
,('617', 'Compound Ingredient Drug Cost Cannot Be Negative Amount', '449-EE', 328,'2024-06-11 22:59:07',1,'617 - 449-EE')
,('645', 'Repackaged product is not covered by the contract', '407-D7', 329,'2024-06-11 22:59:07',1,'645 - 407-D7')
,('647', 'Quantity Prescribed Required For CII Prescription', '460-ET', 330,'2024-06-11 22:59:07',1,'647 - 460-ET')
,('648', 'Quantity Prescribed Does Not Match Quantity Prescribed On Original CII Dispensing', '460-ET', 331,'2024-06-11 22:59:07',1,'648 - 460-ET')
,('649', 'Cumulative Quantity For This CII Rx Number Exceeds Quantity Prescribed', '442-E7', 332,'2024-06-11 22:59:07',1,'649 - 442-E7')
,('7A', 'Provider Does Not Match Authorization On File', '201-B1', 333,'2024-06-11 22:59:07',1,'7A - 201-B1')
,('7B', 'Service Provider ID Qualifier Value Not Supported For Processor/Payer', '202-B2', 334,'2024-06-11 22:59:07',1,'7B - 202-B2')
,('7C', 'M/I Other Payer ID', '340-7C', 335,'2024-06-11 22:59:07',1,'7C - 340-7C')
,('7D', 'Non-Matched DOB', '304-C4', 336,'2024-06-11 22:59:07',1,'7D - 304-C4')
,('7F', 'Future date not allowed for Date of Birth', '304-C4', 337,'2024-06-11 22:59:07',1,'7F - 304-C4')
,('7G', 'Future Date Not Allowed For DOB', '304-C4', 338,'2024-06-11 22:59:07',1,'7G - 304-C4')
,('7H', 'Non-Matched Gender Code', '305-C5', 339,'2024-06-11 22:59:07',1,'7H - 305-C5')
,('7J', 'Patient Relationship Code Value Not Supported', '306-C6', 340,'2024-06-11 22:59:07',1,'7J - 306-C6')
,('7K', 'Discrepancy Between Other Coverage Code And Other Payer Amount', '308-C8', 341,'2024-06-11 22:59:07',1,'7K - 308-C8')
,('7U', 'Days Supply Intended To Be Dispensed Required For Partial Fill Transaction', '345-HG', 342,'2024-06-11 22:59:07',1,'7U - 345-HG')
,('7V', 'Duplicate Refills', '403-D3', 343,'2024-06-11 22:59:07',1,'7V - 403-D3')
,('7W', 'Refills Exceed allowable Refills', '403-D3', 344,'2024-06-11 22:59:07',1,'7W - 403-D3')
,('7X', 'Days Supply Exceeds Plan Limitation', '405-D5', 345,'2024-06-11 22:59:07',1,'7X - 405-D5')
,('7Y', 'Compounds Not Covered', '406-D6', 346,'2024-06-11 22:59:07',1,'7Y - 406-D6')
,('7Z', 'Compound Requires Two Or More Ingredients', '406-D6', 347,'2024-06-11 22:59:07',1,'7Z - 406-D6')
,('70', 'Product/Service Not Covered – Plan/Benefit Exclusion', '407-D7', 348,'2024-06-11 22:59:07',1,'70 - 407-D7')
,('71', 'Prescriber ID Is Not Covered', '421-DL', 349,'2024-06-11 22:59:07',1,'71 - 421-DL')
,('72', 'Primary Prescriber Is Not Covered', '421-DL', 350,'2024-06-11 22:59:07',1,'72 - 421-DL')
,('73', 'Refills Are Not Covered', '403-D3', 351,'2024-06-11 22:59:07',1,'73 - 403-D3')
,('748', 'Non-Matched Processor Control Number', '104-A4', 352,'2024-06-11 22:59:07',1,'748 - 104-A4')
,('8A', 'Compound Requires At Least One Covered Ingredient', '406-D6', 353,'2024-06-11 22:59:07',1,'8A - 406-D6')
,('8B', 'Compound Segment Missing On A Compound Claim', '406-D6', 354,'2024-06-11 22:59:07',1,'8B - 406-D6')
,('8C', 'M/I Facility ID', '336-8C', 355,'2024-06-11 22:59:07',1,'8C - 336-8C')
,('8D', 'Compound Segment Present On A Non-Compound Claim', '406-D6', 356,'2024-06-11 22:59:07',1,'8D - 406-D6')
,('8S', 'Basis Of Cost Determination Value Not Supported', '423-DN', 357,'2024-06-11 22:59:07',1,'8S - 423-DN')
,('8T', 'U&C Must Be Greater Than Zero', '426-DQ', 358,'2024-06-11 22:59:07',1,'8T - 426-DQ')
,('8U', 'GAD Must Be Greater Than Zero', '430-DU', 359,'2024-06-11 22:59:07',1,'8U - 430-DU')
,('8V', 'Negative Dollar Amount Is Not Supported In The Other Payer Amount Paid Field', '431-DV', 360,'2024-06-11 22:59:07',1,'8V - 431-DV')
,('8W', 'Discrepancy Between Other Coverage Code and Other Payer Amount Paid', '308-C8', 361,'2024-06-11 22:59:07',1,'8W - 308-C8')
,('8X', 'Collection From Cardholder Not Allowed', '433-DX', 362,'2024-06-11 22:59:07',1,'8X - 433-DX')
,('8Y', 'Excessive Amount Collected', '433-DX', 363,'2024-06-11 22:59:07',1,'8Y - 433-DX')
,('8Z', 'Product/Service ID Qualifier Value Not Supported', '436-E1', 364,'2024-06-11 22:59:07',1,'8Z - 436-E1')
,('80', 'Drug-Diagnosis Mismatch', '424-DO', 365,'2024-06-11 22:59:07',1,'80 - 424-DO')
,('825', 'Claim Date Of Service Is Outside Of Product’s FDA/NSDE Marketing Dates', '401-D1', 366,'2024-06-11 22:59:07',1,'825 - 401-D1')
,('826', 'Prescriber NPI Submitted Not Found Within Processor’s NPI File', '421-DL', 367,'2024-06-11 22:59:07',1,'826 - 421-DL')
,('829', 'Pharmacy Must Notify Beneficiary - Claim Not Covered Due To Failure To Meet Medicare Part D Active,Valid Prescriber NPI Requirements', '421-DL', 368,'2024-06-11 22:59:07',1,'829 - 421-DL')
,('840', 'Original Manufacturer Product ID Value Not Supported', '436-E1', 369,'2024-06-11 22:59:07',1,'840 - 436-E1')
,('877', 'Service Provider ID Terminated On NPPES File', '202-B2', 370,'2024-06-11 22:59:07',1,'877 - 202-B2')
,('9N', 'Compound Ingredient Quantity Exceeds Maximum Allowed', '448-ED', 371,'2024-06-11 22:59:07',1,'9N - 448-ED')
,('9P', 'Compound Ingredient Drug Cost Must Be Greater Than Zero', '449-EE', 372,'2024-06-11 22:59:07',1,'9P - 449-EE')
,('9Q', 'Route Of Administration Submitted Not Covered', '995-E2', 373,'2024-06-11 22:59:07',1,'9Q - 995-E2')
,('9R', 'Prescription/Service Reference Number Qualifier Submitted Not Covered', '455-EM', 374,'2024-06-11 22:59:07',1,'9R - 455-EM')
,('9S', 'Future Associated Prescription/Service Date Not Allowed', '457-EP', 375,'2024-06-11 22:59:07',1,'9S - 457-EP')
,('9T', 'Prior Authorization Type Code Submitted Not Covered', '461-EU', 376,'2024-06-11 22:59:07',1,'9T - 461-EU')
,('AB', 'Date Written Is After Date Filled', '414-DE', 377,'2024-06-11 22:59:07',1,'AB - 414-DE')
,('AC', 'Product Not Covered Non-Participating Manufacturer', '407-D7', 378,'2024-06-11 22:59:07',1,'AC - 407-D7')
,('A2', 'ID Submitted is associated to a Deceased Prescriber', '411-DB', 379,'2024-06-11 22:59:07',1,'A2 - 411-DB')
,('A3', 'This Product May Be Covered Under Hospice – Medicare A', '436-E1', 380,'2024-06-11 22:59:07',1,'A3 - 436-E1')
,('A4', 'This Product May Be Covered Under The Medicare-B Bundled Payment To An ESRD Dialysis Facility', '436-E1', 381,'2024-06-11 22:59:07',1,'A4 - 436-E1')
,('A5', 'Not Covered Under Part D Law', '436-E1', 382,'2024-06-11 22:59:07',1,'A5 - 436-E1')
,('CA', 'M/I Patient First Name', '310-CA', 383,'2024-06-11 22:59:07',1,'CA - 310-CA')
,('CB', 'M/I Patient Last Name', '311-CB', 384,'2024-06-11 22:59:07',1,'CB - 311-CB')
,('CC', 'M/I Cardholder First Name', '312-CC', 385,'2024-06-11 22:59:07',1,'CC - 312-CC')
,('CD', 'M/I Cardholder Last Name', '313-CD', 386,'2024-06-11 22:59:07',1,'CD - 313-CD')
,('CF', 'M/I Employer Name', '315-CF', 387,'2024-06-11 22:59:07',1,'CF - 315-CF')
,('CG', 'M/I Employer Street Address', '316-CG', 388,'2024-06-11 22:59:07',1,'CG - 316-CG')
,('CH', 'M/I Employer City Address', '317-CH', 389,'2024-06-11 22:59:07',1,'CH - 317-CH')
,('CI', 'M/I Employer State/Province Address', '318-CI', 390,'2024-06-11 22:59:07',1,'CI - 318-CI')
,('CJ', 'M/I Employer Zip Postal Zone', '319-CJ', 391,'2024-06-11 22:59:07',1,'CJ - 319-CJ')
,('CK', 'M/I Employer Phone Number', '320-CK', 392,'2024-06-11 22:59:07',1,'CK - 320-CK')
,('CL', 'M/I Employer Contact Name', '321-CL', 393,'2024-06-11 22:59:07',1,'CL - 321-CL')
,('CM', 'M/I Patient Street Address', '322-CM', 394,'2024-06-11 22:59:07',1,'CM - 322-CM')
,('DU', 'M/I Gross Amount Due', '430-DU', 395,'2024-06-11 22:59:07',1,'DU - 430-DU')
,('DV', 'M/I Other Payer Amount Paid', '431-DV', 396,'2024-06-11 22:59:07',1,'DV - 431-DV')
,('DX', 'M/I Patient Paid Amount Submitted', '433-DX', 397,'2024-06-11 22:59:07',1,'DX - 433-DX')
,('DY', 'M/I Date Of Injury', '434-DY', 398,'2024-06-11 22:59:07',1,'DY - 434-DY')
,('DZ', 'M/I Claim/Reference ID', '435-DZ', 399,'2024-06-11 22:59:07',1,'DZ - 435-DZ')
,('EA', 'M/I Originally Prescribed Product/Service Code', '445-EA', 400,'2024-06-11 22:59:07',1,'EA - 445-EA')
,('EB', 'M/I Originally Prescribed Quantity', '446-EB', 401,'2024-06-11 22:59:07',1,'EB - 446-EB')
,('ED', 'M/I Compound Ingredient Quantity', '448-ED', 402,'2024-06-11 22:59:07',1,'ED - 448-ED')
,('EE', 'M/I Compound Ingredient Drug Cost', '449-EE', 403,'2024-06-11 22:59:07',1,'EE - 449-EE')
,('EF', 'M/I Compound Dosage Form Description Code', '450-EF', 404,'2024-06-11 22:59:07',1,'EF - 450-EF')
,('EG', 'M/I Compound Dispensing Unit Form Indicator', '451-EG', 405,'2024-06-11 22:59:07',1,'EG - 451-EG')
,('EW', 'M/I Intermediary Authorization Type ID', '461-EU', 406,'2024-06-11 22:59:07',1,'EW - 461-EU')
,('EX', 'M/I Intermediary Authorization ID', '462-EV', 407,'2024-06-11 22:59:07',1,'EX - 462-EV')
,('EY', 'M/I Provider ID Qualifier', '465-EY', 408,'2024-06-11 22:59:07',1,'EY - 465-EY')
,('EZ', 'M/I Prescriber ID Qualifier', '466-EZ', 409,'2024-06-11 22:59:07',1,'EZ - 466-EZ')
,('E1', 'M/I Product/Service ID Qualifier', '436-E1', 410,'2024-06-11 22:59:07',1,'E1 - 436-E1')
,('E2', 'M/I Route of Administration', '995-E2', 411,'2024-06-11 22:59:07',1,'E2 - 995-E2')
,('E3', 'M/I Incentive Amount Submitted', '438-E3', 412,'2024-06-11 22:59:07',1,'E3 - 438-E3')
,('E4', 'M/I Reason For Service Code', '439-E4', 413,'2024-06-11 22:59:07',1,'E4 - 439-E4')
,('E5', 'M/I Professional Service Code', '440-E5', 414,'2024-06-11 22:59:07',1,'E5 - 440-E5')
,('E6', 'M/I Result Of Service Code', '441-E6', 415,'2024-06-11 22:59:07',1,'E6 - 441-E6')
,('G9', 'Pharmacy Not Contracted in 90 Day Retail Network', '405-D5', 416,'2024-06-11 22:59:07',1,'G9 - 405-D5')
,('HA', 'M/I Flat Sales Tax Amount Submitted', '481-HA', 417,'2024-06-11 22:59:07',1,'HA - 481-HA')
,('HC', 'M/I Other Payer Amount Paid Qualifier', '342-HC', 418,'2024-06-11 22:59:07',1,'HC - 342-HC')
,('HD', 'M/I Dispensing Status', '343-HD', 419,'2024-06-11 22:59:07',1,'HD - 343-HD')
,('HE', 'M/I Percentage Sales Tax Rate Submitted', '483-HE', 420,'2024-06-11 22:59:07',1,'HE - 483-HE')
,('HF', 'M/I Quantity Intended To Be Dispensed', '344-HF', 421,'2024-06-11 22:59:07',1,'HF - 344-HF')
,('HG', 'M/I Days Supply Intended To Be Dispensed', '345-HG', 422,'2024-06-11 22:59:07',1,'HG - 345-HG')
,('HN', 'M/I Patient E-Mail Address', '350-HN', 423,'2024-06-11 22:59:07',1,'HN - 350-HN')
,('H1', 'M/I Measurement Time', '495-H1', 424,'2024-06-11 22:59:07',1,'H1 - 495-H1')
,('ME', 'M/I Coupon Number', '486-ME', 425,'2024-06-11 22:59:07',1,'ME - 486-ME')
,('MG', 'M/I Other Payer BIN Number', '340-7C', 426,'2024-06-11 22:59:07',1,'MG - 340-7C')
,('MH', 'M/I Other Payer Processor Control Number', '991-MH', 427,'2024-06-11 22:59:07',1,'MH - 991-MH')
,('MJ', 'M/I Other Payer Group ID', '992-MJ', 428,'2024-06-11 22:59:07',1,'MJ - 992-MJ')
,('MK', 'Non-Matched Other Payer BIN Number', '990-MG', 429,'2024-06-11 22:59:07',1,'MK - 990-MG')
,('NE', 'M/I Coupon Value Amount', '487-NE', 430,'2024-06-11 22:59:07',1,'NE - 487-NE')
,('NP', 'M/I Other Payer-Patient Responsibility Amount Qualifier', '342-HC', 431,'2024-06-11 22:59:07',1,'NP - 342-HC')
,('NQ', 'M/I Other Payer-Patient Responsibility Amount', '431-DV', 432,'2024-06-11 22:59:07',1,'NQ - 431-DV')
,('NV', 'M/I Delay Reason Code', '357-NV', 433,'2024-06-11 22:59:07',1,'NV - 357-NV')
,('N1', 'No patient match found', '302-C2', 434,'2024-06-11 22:59:07',1,'N1 - 302-C2')
,('PZ', 'Non-Matched Unit Of Measure To Product/Service ID', '600-28', 435,'2024-06-11 22:59:07',1,'PZ - 600-28')
,('P1', 'Associated Prescription/Service Reference Number Not Found', '402-D2', 436,'2024-06-11 22:59:07',1,'P1 - 402-D2')
,('P5', 'Coupon Expired', '486-ME', 437,'2024-06-11 22:59:07',1,'P5 - 486-ME')
,('P6', 'Date Of Service Prior To Date Of Birth', '401-D1', 438,'2024-06-11 22:59:07',1,'P6 - 401-D1')
,('RF', 'Improper Order Of Dispensing Status Code On Partial Fill Transaction', '343-HD', 439,'2024-06-11 22:59:07',1,'RF - 343-HD')
,('RG', 'M/I Associated Prescription/Service Reference Number On Completion Transaction', '456-EN', 440,'2024-06-11 22:59:07',1,'RG - 456-EN')
,('RH', 'M/I Associated Prescription/Service Date On Completion Transaction', '457-EP', 441,'2024-06-11 22:59:07',1,'RH - 457-EP')
,('RJ', 'Associated Partial Fill Transaction Not On File', '344-HF', 442,'2024-06-11 22:59:07',1,'RJ - 344-HF')
,('RK', 'Partial Fill Transaction Not Supported', '344-HF', 443,'2024-06-11 22:59:07',1,'RK - 344-HF')
,('RM', 'Completion Transaction Not Permitted With Same Date Of Service As Partial Transaction', '457-EP', 444,'2024-06-11 22:59:07',1,'RM - 457-EP')
,('RN', 'Plan Limits Exceeded On Intended Partial Fill Field Limitations', '344-HF', 445,'2024-06-11 22:59:07',1,'RN - 344-HF')
,('R4', 'Procedure Modifier Code Invalid For Product/Service ID', '459-ER', 446,'2024-06-11 22:59:07',1,'R4 - 459-ER')
,('R5', 'Product/Service ID Must Be Zero When Product/Service ID Qualifier Equals 06', '407-D7', 447,'2024-06-11 22:59:07',1,'R5 - 407-D7')
,('R6', 'Product/Service Not Appropriate For This Location', '407-D7', 448,'2024-06-11 22:59:07',1,'R6 - 407-D7')
,('TY', 'M/I Pay To Zip/Postal Zone', '124-TY', 449,'2024-06-11 22:59:07',1,'TY - 124-TY')
,('TZ', 'M/I Generic Equivalent Product ID Qualifier', '125-TZ', 450,'2024-06-11 22:59:07',1,'TZ - 125-TZ')
,('UA', 'M/I Generic Equivalent Product ID', '126-UA', 451,'2024-06-11 22:59:07',1,'UA - 126-UA')
,('UE', 'M/I Compound Ingredient Basis Of Cost Determination', '423-DN', 452,'2024-06-11 22:59:07',1,'UE - 423-DN')
,('W9', 'Accumulated Gross Covered Drug Cost Amount Must Be Equal To Or Greater Than Zero', '430-DU', 453,'2024-06-11 22:59:07',1,'W9 - 430-DU')
,('XZ', 'M/I Associated Prescription/Service Reference Number Qualifier', '455-EM', 454,'2024-06-11 22:59:07',1,'XZ - 455-EM')
,('X0', 'M/I Associated Prescription/Service Fill #', '403-D3', 455,'2024-06-11 22:59:07',1,'X0 - 403-D3')
,('X2', 'Accumulated Gross Covered Drug Cost exceeds maximum', '430-DU', 456,'2024-06-11 22:59:07',1,'X2 - 430-DU')
,('ZY', 'M/I Medicare Part D Plan Benefit Package (PBP)', '301-C1', 457,'2024-06-11 22:59:07',1,'ZY - 301-C1')
,('ZZ', 'Cardholder ID submitted is inactive. New Cardholder ID on file.', '302-C2', 458,'2024-06-11 22:59:07',1,'ZZ - 302-C2')
;
ALTER SEQUENCE "form_list_ncpdp_rej_code_map_id_seq" RESTART WITH 459;
SET session_replication_role = DEFAULT;
 COMMIT;
