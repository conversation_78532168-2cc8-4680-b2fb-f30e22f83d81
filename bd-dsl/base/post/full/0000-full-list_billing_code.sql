BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_code;
 INSERT INTO form_list_billing_code ("code_type","code","name","id","created_on","created_by","auto_name") VALUES ('G-Code','G0089','Admin Subq drug 1st home visit (15 minutes)',1,'2024-03-19 23:14:43',1,'G0089 (HCPC) Admin Subq drug 1st home visit (15 minutes)')
,('CPT','99601','Home infusion/specialty drug administration, per visit (up to 2 hours)',2,'2024-03-19 23:14:43',1,'99601 (CPT) Home infusion/specialty drug administration, per visit (up to 2 hours)')
,('CPT','99602','Home infusion/specialty drug administration (1 hour additional)',3,'2024-03-19 23:14:43',1,'99602 (CPT) Home infusion/specialty drug administration (1 hour additional)')
,('G-Code','G0069','Admin Subq infusion drug in home (15 minutes)',4,'2024-03-19 23:14:43',1,'G0069 (HCPC) Admin Subq infusion drug in home (15 minutes)')
,('A-Code','A4222','Admin Kit with pump',5,'2024-03-19 23:14:43',1,'A4222 (A-Code) Admin Kit with pump')
,('A-Code','A4223','Admin Kit no pump',6,'2024-03-19 23:14:43',1,'A4223 (A-Code) Admin Kit no pump')
,('E-Code','E0781','Curlin Pump',7,'2024-03-19 23:14:43',1,'E0781 (E-Code) Curlin Pump')
,('E-Code','E0779','Freedom 60',8,'2024-03-19 23:14:43',1,'E0779 (E-Code) Freedom 60')
,('S-Code','S9338','Hit immunotherapy per diem',9,'2024-03-19 23:14:43',1,'S9338 (S-Code) Hit immunotherapy per diem')
,('Q-Code','Q2052','IVIG Demo service/supplies',10,'2024-03-19 23:14:43',1,'Q2052 (Q-Code) IVIG Demo service/supplies')
,('K-Code','K0552','SQIG Supply Kit',11,'2024-03-19 23:14:43',1,'K0552 (K-Code) SQIG Supply Kit')
,('S-Code','S9345','Home infusion therapy, antihemophilic agent infusion therapy per diem',12,'2024-03-19 23:14:43',1,'S9345 (S-Code) Home infusion therapy, antihemophilic agent infusion therapy per diem')
,('S-Code','S9379','IV NOC Infusion',13,'2024-03-19 23:14:43',1,'S9379 (S-Code) IV NOC Infusion')
,('S-Code','S9490','Home infusion therapy, infusion therapy, not otherwise classified',14,'2024-03-19 23:14:43',1,'S9490 (S-Code) Home infusion therapy, infusion therapy, not otherwise classified')
;
ALTER SEQUENCE "form_list_billing_code_id_seq" RESTART WITH 15;
SET session_replication_role = DEFAULT;
 COMMIT;
