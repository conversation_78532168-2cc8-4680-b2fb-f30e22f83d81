BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_drug_db_qualifier;
INSERT INTO form_list_ss_drug_db_qualifier ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:44:05',1,'Truven/Micromedex Generic Formulation Code (GFC)','E','Truven/Micromedex Generic Formulation Code (GFC)'),
(2,'4/16/2024, 06:44:05',1,'Truven/Micromedex Generic Master (GM)','G','Truven/Micromedex Generic Master (GM)'),
(3,'4/16/2024, 06:44:05',1,'American Hospital Formulary Service (AHFS)','AF','American Hospital Formulary Service (AHFS)'),
(4,'4/16/2024, 06:44:05',1,'First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','FG','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)'),
(5,'4/16/2024, 06:44:05',1,'First Databank Smartkey','FS','First Databank Smartkey'),
(6,'4/16/2024, 06:44:05',1,'Multum Drug ID','MC','Multum Drug ID'),
(7,'4/16/2024, 06:44:05',1,'Medi Span''s Drug Descriptor ID (DDID)','MD','Medi Span''s Drug Descriptor ID (DDID)'),
(8,'4/16/2024, 06:44:05',1,'Medi Span''s Generic Product Identifier (GPI)','MG','Medi Span''s Generic Product Identifier (GPI)'),
(9,'4/16/2024, 06:44:05',1,'Multum MMDC','MM','Multum MMDC'),
(10,'4/16/2024, 06:44:05',1,'First DataBank Ingredient List ID (HICL_SEQNO','FL','First DataBank Ingredient List ID (HICL_SEQNO'),
(11,'4/16/2024, 06:44:05',1,'First DataBank Medication ID (FDB MedID)','FM','First DataBank Medication ID (FDB MedID - FM)'),
(12,'4/16/2024, 06:44:05',1,'First DataBank Medication Name ID (FDB Med Name ID)','FN','First DataBank Medication Name ID (FDB Med Name ID)'),
(13,'4/16/2024, 06:44:05',1,'First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','FD','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)'),
(14,'4/16/2024, 06:44:05',1,'RxNorm Generic Package (GPCK)','GPK','RxNorm Generic Package (GPCK)'),
(15,'4/16/2024, 06:44:05',1,'RxNorm Semantic Clinical Drug (SCD)','SCD','RxNorm Semantic Clinical Drug (SCD)'),
(16,'4/16/2024, 06:44:05',1,'RxNorm Branded Package (BPCK)','BPK','RxNorm Branded Package (BPCK)'),
(17,'4/16/2024, 06:44:05',1,'RxNorm Semantic Branded Drug (SBD)','SBD','RxNorm Semantic Branded Drug (SBD)'),
(18,'4/16/2024, 06:44:05',1,'First DataBank Medication ID (FDB MedID)','FI','First DataBank Medication ID (FDB MedID - FI)'),
(19,'4/16/2024, 06:44:05',1,'First DataBank Routed Medication ID (FDB Routed Med ID)','FR','First DataBank Routed Medication ID (FDB Routed Med ID)'),
(20,'4/16/2024, 06:44:05',1,'Gold Standard Product Item Collection','GS','Gold Standard Product Item Collection'),
(21,'4/16/2024, 06:44:05',1,'Gold Standard Marketed Product Identifier (MPid)','GMP','Gold Standard Marketed Product Identifier (MPid)'),
(22,'4/16/2024, 06:44:05',1,'Gold Standard Product Identifier (ProdID)','GPI','Gold Standard Product Identifier (ProdID)'),
(23,'4/16/2024, 06:44:05',1,'Gold Standard Specific Product Identifier (SPID)','GSP','Gold Standard Specific Product Identifier (SPID)'),
(24,'4/16/2024, 06:44:05',1,'U.S. Pharmacopoeia (USP)','US','U.S. Pharmacopoeia (USP)')
;
ALTER SEQUENCE "form_list_ss_drug_db_qualifier_id_seq" RESTART WITH 25;
SET session_replication_role = DEFAULT;
COMMIT;