BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_insurance_type;
INSERT INTO form_list_insurance_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'AP - Auto Insurance Policy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'AP','Auto Insurance Policy'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'C1 - Commercial',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'C1','Commercial'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CP - Medicare Conditionally Primary',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CP','Medicare Conditionally Primary'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'GP - Group Policy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'GP','Group Policy'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'HM - Health Maintenance Organization (HMO)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'HM','Health Maintenance Organization (HMO)'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'IP - Individual Policy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'IP','Individual Policy'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'LD - Long Term Policy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'LD','Long Term Policy'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'LT - Litigation',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'LT','Litigation'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MB - Medicare Part B',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MB','Medicare Part B'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MC - Medicaid',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MC','Medicaid'),
(11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MI - Medigap Part B',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MI','Medigap Part B'),
(12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MP - Medicare Primary',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MP','Medicare Primary'),
(13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'OT - Other',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'OT','Other'),
(14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PP - Personal Payment (Cash - No Insurance)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PP','Personal Payment (Cash - No Insurance)'),
(15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'SP - Supplemental Policy',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'SP','Supplemental Policy');
SET session_replication_role = DEFAULT;
COMMIT;
