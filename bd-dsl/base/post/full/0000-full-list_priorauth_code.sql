BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_priorauth_code;
INSERT INTO form_list_priorauth_code ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'0 - Not Specified',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'0','Not Specified'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1 - Prior Authorization',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1','Prior Authorization'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2 - Medical Certification',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2','Medical Certification'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3 - Early Periodic Screening Diagnosis Treatment',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3','Early Periodic Screening Diagnosis Treatment'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4 - Exemption from CoPay',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4','Exemption from CoPay'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'5 - Exemption from RX',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'5','Exemption from RX'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'6 - Family Plan Indic.',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'6','Family Plan Indic.'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'7 - Aid To Families With Dependant Childen',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'7','Aid To Families With Dependant Childen'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8 - Payor Defined Exemption',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'8','Payor Defined Exemption'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9 - Emergency Preparedness (D.0)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'9','Emergency Preparedness (D.0)');
SET session_replication_role = DEFAULT;
COMMIT;
