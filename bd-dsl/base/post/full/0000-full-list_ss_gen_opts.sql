BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_ss_gen_opts;
INSERT INTO form_list_ss_gen_opts ("code","name","id","created_on","created_by","auto_name") VALUES ('patient','Patient',1,'2024-03-19 22:39:32',1,'Patient')
,('diagnosis','Patient Diagnoses',2,'2024-03-19 22:39:32',1,'Patient Diagnoses')
,('physician','Physician',3,'2024-03-19 22:39:32',1,'Physician')
,('prescriber','Patient Prescriber',4,'2024-03-19 22:39:32',1,'Patient Prescriber')
,('insurance','Patient Insurance',5,'2024-03-19 22:39:32',1,'Patient Insurance')
,('pa','Prior Auth',6,'2024-03-19 22:39:32',1,'Prior Auth')
,('intake','Intake',7,'2024-03-19 22:39:32',1,'Intake')
,('careplan','Careplan',8,'2024-03-19 22:39:32',1,'Careplan')
,('order','Careplan Order',9,'2024-03-19 22:39:32',1,'Careplan Order')
,('order_item','Careplan Prescription',10,'2024-03-19 22:39:32',1,'Careplan Prescription')

;
ALTER SEQUENCE "form_list_ss_gen_opts_id_seq" RESTART WITH 11;
SET session_replication_role = DEFAULT;
 COMMIT;