BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_daw_qualifier;
INSERT INTO form_list_daw_qualifier ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'0 - No Product Selection Indicated',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'0','No Product Selection Indicated'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1 - Substitution Not Allowed By Prescriber',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1','Substitution Not Allowed By Prescriber'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'2 - Substitution Allowed - Patient Required Product Dispensed',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'2','Substitution Allowed - Patient Required Product Dispensed'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3 - Substitution Allowed - Pharmacist Selected Product Dispensed',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3','Substitution Allowed - Pharmacist Selected Product Dispensed'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4 - Substitution Allowed - Generic Drug Not In Stock',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4','Substitution Allowed - Generic Drug Not In Stock'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'5 - Substitution Allowed - Brand Drug Dispensed As A Generic',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'5','Substitution Allowed - Brand Drug Dispensed As A Generic'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'6 - Override',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'6','Override'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'7 - Substitution Not Allowed - Brand Drug Mandated By Law',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'7','Substitution Not Allowed - Brand Drug Mandated By Law'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'8 - Substitution Not Allowed - Generic Drug Not Available In Marketplace',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'8','Substitution Not Allowed - Generic Drug Not Available In Marketplace'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'9 - Other',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'9','Other');
SET session_replication_role = DEFAULT;
COMMIT;
