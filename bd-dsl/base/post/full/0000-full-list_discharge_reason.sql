BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_discharge_reason;
INSERT INTO form_list_discharge_reason ("code","name","id","created_on","created_by","auto_name") VALUES ('1','Benefit Coverage',1,'2024-03-19 22:39:32',1,'1 - Benefit Coverage')
,('2','Carve Out',2,'2024-03-19 22:39:32',1,'2 - Carve Out')
,('3','Deceased',3,'2024-03-19 22:39:32',1,'3 - Deceased')
,('4','Error',4,'2024-03-19 22:39:32',1,'4 - Error')
,('5','Canceled',5,'2024-03-19 22:39:32',1,'5 - Canceled')
,('6','Hospitalized',6,'2024-03-19 22:39:32',1,'6 - Hospitalized')
,('7','Limited Distribution product',7,'2024-03-19 22:39:32',1,'7 - Limited Distribution product')
,('8','New Insurance',8,'2024-03-19 22:39:32',1,'8 - New Insurance')
,('9','New Order',9,'2024-03-19 22:39:32',1,'9 - New Order')
,('10','No Insurance/Termed',10,'2024-03-19 22:39:32',1,'10 - No Insurance/Termed')
,('11','Non-Compliant',11,'2024-03-19 22:39:32',1,'11 - Non-Compliant')
,('12','Not appropriate for home care',12,'2024-03-19 22:39:32',1,'12 - Not appropriate for home care')
,('13','Order change',13,'2024-03-19 22:39:32',1,'13 - Order change')
,('14','Other',14,'2024-03-19 22:39:32',1,'14 - Other')
,('15','Outpatient',15,'2024-03-19 22:39:32',1,'15 - Outpatient')
,('16','Patient RX on hold',16,'2024-03-19 22:39:32',1,'16 - Patient RX on hold')
,('17','Patient/Provider Cancel',17,'2024-03-19 22:39:32',1,'17 - Patient/Provider Cancel')
,('18','Pending Future Orders',18,'2024-03-19 22:39:32',1,'18 - Pending Future Orders')
,('19','Refills Exhausted',19,'2024-03-19 22:39:32',1,'19 - Refills Exhausted')
,('20','Refills Expired',20,'2024-03-19 22:39:32',1,'20 - Refills Expired')
,('21','Reimbursement-related',21,'2024-03-19 22:39:32',1,'21 - Reimbursement-related')
,('22','Therapy change',22,'2024-03-19 22:39:32',1,'22 - Therapy change')
,('23','Therapy completed',23,'2024-03-19 22:39:32',1,'23 - Therapy completed')
,('24','Therapy initiated',24,'2024-03-19 22:39:32',1,'24 - Therapy initiated')
,('25','Therapy Resumed',25,'2024-03-19 22:39:32',1,'25 - Therapy Resumed')
,('26','Transfer out',26,'2024-03-19 22:39:32',1,'26 - Transfer out')
;
ALTER SEQUENCE "form_list_discharge_reason_id_seq" RESTART WITH 27;
SET session_replication_role = DEFAULT;
 COMMIT;
