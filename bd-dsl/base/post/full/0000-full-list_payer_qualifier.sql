BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_payer_qualifier;
INSERT INTO form_list_payer_qualifier ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'0B - State License Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'0B','State License Number'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'18 - Plan Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'18','Plan Number'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1A - Blue Cross Provider Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1A','Blue Cross Provider Number'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1B - Blue Shield Provider Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1B','Blue Shield Provider Number'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1C - Medicare Provider Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1C','Medicare Provider Number'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1D - Medicaid Provider Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1D','Medicaid Provider Number'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1G - Provider UPIN Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1G','Provider UPIN Number'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1H - CHAMPUS Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1H','CHAMPUS Identification Number'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1J - Facility ID Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1J','Facility ID Number'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1L - Group or Policy Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1L','Group or Policy Number'),
(11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'1W - Member Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'1W','Member Identification Number'),
(12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'3H - Subscriber Case Number (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'3H','Subscriber Case Number (5010)'),
(13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'49 - Facility Unit Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'49','Facility Unit Number'),
(14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'4A - Personal Identification Number (PIN)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'4A','Personal Identification Number (PIN)'),
(15,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'6P - Group Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'6P','Group Number'),
(16,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'A6 - Employee Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'A6','Employee Identification Number'),
(17,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ALS - Alternative List ID (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ALS','Alternative List ID (5010)'),
(18,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'B3 - Preferred Provider Organization Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'B3','Preferred Provider Organization Number'),
(19,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'BQ - Health Maintenance Organization Code Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'BQ','Health Maintenance Organization Code Number'),
(20,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CE - Subscriber Contact Number (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CE','Subscriber Contact Number (5010)'),
(21,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CLI - Coverage List ID (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CLI','Coverage List ID (5010)'),
(22,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CT - Contract Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'CT','Contract Number'),
(23,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'EA - Medical Record Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'EA','Medical Record Identification Number'),
(24,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'EI - Employer''s Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'EI','Employer''s Identification Number'),
(25,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'EJ - Patient Account Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'EJ','Patient Account Number'),
(26,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'EL - Electronic device pin number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'EL','Electronic device pin number'),
(27,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'EO - Submitter Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'EO','Submitter Identification Number'),
(28,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'F6 - Health Insurance Claim (HIC) Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'F6','Health Insurance Claim (HIC) Number'),
(29,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'FH - Clinic Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'FH','Clinic Number'),
(30,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'F0 - Drug Formulary Number (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'F0','Drug Formulary Number (5010)'),
(31,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'G1 - Prior Authorization Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'G1','Prior Authorization Number'),
(32,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'G2 - Provider Commercial Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'G2','Provider Commercial Number'),
(33,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'G5 - Provider Site Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'G5','Provider Site Number'),
(34,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'GH - Identification Card Serial Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'GH','Identification Card Serial Number'),
(35,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'HJ - Identity Card Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'HJ','Identity Card Number'),
(36,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'HPI - Health Care Financing Administration National Provider Identifier',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'HPI','Health Care Financing Administration National Provider Identifier'),
(37,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'IG - Insurance Policy Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'IG','Insurance Policy Number'),
(38,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'JD - User Identification',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'JD','User Identification'),
(39,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'LU - Location Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'LU','Location Number'),
(40,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'M7 - Medical Assistance Category (501)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'M7','Medical Assistance Category (501)'),
(41,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'MRC - Eligibility Category (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'MRC','Eligibility Category (5010)'),
(42,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'N6 - Plan Network Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'N6','Plan Network Identification Number'),
(43,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'N7 - Facility Network Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'N7','Facility Network Identification Number'),
(44,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'NQ - Medicaid Recipient Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'NQ','Medicaid Recipient Identification Number'),
(45,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PXC - Taxonomy Code',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PXC','Taxonomy Code'),
(46,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Q4 - Prior Identification Code',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Q4','Prior Identification Code'),
(47,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'SY - Social Security Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'SY','Social Security Number'),
(48,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'TJ - Federal Taxpayer''s Identification Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'TJ','Federal Taxpayer''s Identification Number'),
(49,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'U3 - Unique Supplier Identification Number (USIN)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'U3','Unique Supplier Identification Number (USIN)'),
(50,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'X5 - State Industrial Accident Provider Number',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'X5','State Industrial Accident Provider Number'),
(51,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Y4 - Subscriber Agency Claim # (5010)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Y4','Subscriber Agency Claim # (5010)'),
(52,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ZZ - Taxonomy Code',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'ZZ','Taxonomy Code');
SET session_replication_role = DEFAULT;
COMMIT;
