BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_coagent_qualifier;
INSERT INTO form_list_ss_coagent_qualifier ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:35:17',1,'Universal Product Code (UPC)','01','Universal Product Code (UPC)'),
(2,'4/16/2024, 06:35:17',1,'Health Related Item (HRI)','02','Health Related Item (HRI)'),
(3,'4/16/2024, 06:35:17',1,'National Drug Code (NDC)','03','National Drug Code (NDC)'),
(4,'4/16/2024, 06:35:17',1,'Health Industry Business Communications Council (HIBCC)','04','Health Industry Business Communications Council (HIBCC)'),
(5,'4/16/2024, 06:35:17',1,'Common Procedure Terminology (CPT4)','07','Common Procedure Terminology (CPT4)'),
(6,'4/16/2024, 06:35:17',1,'Common Procedure Terminology (CPT5)','08','Common Procedure Terminology (CPT5)'),
(7,'4/16/2024, 06:35:17',1,'Health Care Financing Administration Common Procedural Coding System (HCPCS)','09','Health Care Financing Administration Common Procedural Coding System (HCPCS)'),
(8,'4/16/2024, 06:35:17',1,'National Pharmaceutical Product Interface Code (NAPPI)','11','National Pharmaceutical Product Interface Code (NAPPI)'),
(9,'4/16/2024, 06:35:17',1,'Global Trade Identification Number (GTIN)','12','Global Trade Identification Number (GTIN)'),
(10,'4/16/2024, 06:35:17',1,'Med-Span GPI','14','Med-Span GPI'),
(11,'4/16/2024, 06:35:17',1,'First DataBank GCN','15','First DataBank GCN'),
(12,'4/16/2024, 06:35:17',1,'Truven/Micromedex Generic Formulation Code (GFC)','16','Truven/Micromedex Generic Formulation Code (GFC)'),
(13,'4/16/2024, 06:35:17',1,'Medi-Span DDID','17','Medi-Span DDID'),
(14,'4/16/2024, 06:35:17',1,'First DataBank SmartKey','18','First DataBank SmartKey'),
(15,'4/16/2024, 06:35:17',1,'Truven/Micromedex Generic Master (GM)','19','Truven/Micromedex Generic Master (GM)'),
(16,'4/16/2024, 06:35:17',1,'International Classification of Diseases (ICD9)','20','International Classification of Diseases (ICD9)'),
(17,'4/16/2024, 06:35:17',1,'International Classification of Diseases (ICD10)','21','International Classification of Diseases (ICD10)'),
(18,'4/16/2024, 06:35:17',1,'Medi-Span Diagnosis Code','22','Medi-Span Diagnosis Code'),
(19,'4/16/2024, 06:35:17',1,'National Criteria Care Institute (NCCI)','23','National Criteria Care Institute (NCCI)'),
(20,'4/16/2024, 06:35:17',1,'The Systematized Nomenclature of Human and Veterinary Medicine (SNOMED)','24','The Systematized Nomenclature of Human and Veterinary Medicine (SNOMED)'),
(21,'4/16/2024, 06:35:17',1,'Common Dental Terminology (CDT)','25','Common Dental Terminology (CDT)'),
(22,'4/16/2024, 06:35:17',1,'American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM IV)','26','American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM IV)'),
(23,'4/16/2024, 06:35:17',1,'International Classification of Diseases-10-Procedure Coding System (ICD-10-PCS)','27','International Classification of Diseases-10-Procedure Coding System (ICD-10-PCS)'),
(24,'4/16/2024, 06:35:17',1,'First DataBank Medication Name ID (FDB Med Name ID)','28','First DataBank Medication Name ID (FDB Med Name ID)'),
(25,'4/16/2024, 06:35:17',1,'First DataBank Routed Medication ID (FDB Routed Med ID)','29','First DataBank Routed Medication ID (FDB Routed Med ID)'),
(26,'4/16/2024, 06:35:17',1,'First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)','30','First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)'),
(27,'4/16/2024, 06:35:17',1,'First DataBank Medication ID (FDB MedID)','31','First DataBank Medication ID (FDB MedID)'),
(28,'4/16/2024, 06:35:17',1,'First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)','32','First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)'),
(29,'4/16/2024, 06:35:17',1,'Unique six character code','33','Unique six character code'),
(30,'4/16/2024, 06:35:17',1,'Suite of products providing peer-reviewed information','37','Suite of products providing peer-reviewed information'),
(31,'4/16/2024, 06:35:17',1,'RxNorm Semantic Clinical Drug (SCD)','38','RxNorm Semantic Clinical Drug (SCD)'),
(32,'4/16/2024, 06:35:17',1,'RxNorm Semantic Branded Drug (SBD)','39','RxNorm Semantic Branded Drug (SBD)'),
(33,'4/16/2024, 06:35:17',1,'RxNorm Generic Package (GPCK)','40','RxNorm Generic Package (GPCK)'),
(34,'4/16/2024, 06:35:17',1,'RxNorm Branded Package (BPCK)','41','RxNorm Branded Package (BPCK)'),
(35,'4/16/2024, 06:35:17',1,'Other','99','Other'),
(36,'4/16/2024, 06:35:17',1,'The Device Identifier (DI) portion of the Unique Device Identifier (UDI)','DI','The Device Identifier (DI) portion of the Unique Device Identifier (UDI)')
;
ALTER SEQUENCE "form_list_ss_coagent_qualifier_id_seq" RESTART WITH 37;
SET session_replication_role = DEFAULT;
COMMIT;