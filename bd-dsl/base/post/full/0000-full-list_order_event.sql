BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_order_event;
INSERT INTO form_list_order_event ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'CREATE - Order Created',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'CREATE','Order Created'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'VOID - Order Voided',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'VOID','Order Voided'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ORDERSTATUS - Order Status Change',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'ORDERSTATUS','Order Status Change'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ORDERSUBSTATUS - Order Substatus Change',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'ORDERSUBSTATUS','Order Substatus Change'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'ORDERITEMADD - Order Item Added',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'ORDERITEMADD','Order Item Added'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'DELIVERYTICKET - Delivery Ticket Created',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'DELIVERYTICKET','Delivery Ticket Created'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'DELIVERYTICKETCONF - Delivery Ticket Confirmed',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'DELIVERYTICKETCONF','Delivery Ticket Confirmed'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'DELIVERYTICKETVOID - Delivery Ticket Voided',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'DELIVERYTICKETVOID','Delivery Ticket Voided'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'WORKTICKET - Work Ticket Created',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'WORKTICKET','Work Ticket Created'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'WORKTICKETVOID - Work Ticket Voided',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'WORKTICKETVOID','Work Ticket Voided'),
(11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'WORKTICKETVER - Work Ticket Verified',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'WORKTICKETVER','Work Ticket Verified'),
(12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'DELIVERY - Delivery Log Created',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'DELIVERY','Delivery Log Created'),
(13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'SHIPPINGLABEL - Shipping Label Created',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'SHIPPINGLABEL','Shipping Label Created'),
(14,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'LABELSPRINTED - Labels Printed',NULL,NULL,'2024-04-29T18:30:26.000Z',NULL,'LABELSPRINTED','Labels Printed');
SET session_replication_role = DEFAULT;
COMMIT;
