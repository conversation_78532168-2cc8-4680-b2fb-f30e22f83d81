BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_error_desc;
INSERT INTO form_list_ss_error_desc ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 13:13:50',1,'Request timed out before response could be received','008','Request timed out before response could be received'),
(2,'4/16/2024, 13:13:50',1,'COO cardholder last name is invalid','103','COO cardholder last name is invalid'),
(3,'4/16/2024, 13:13:50',1,'Sending a Quantity Sufficient with Quantity of 0 is invalid for this pharmacy','134','Sending a Quantity Sufficient with Quantity of 0 is invalid for this pharmacy'),
(4,'4/16/2024, 13:13:50',1,'Number of refills invalid','144','Number of refills invalid'),
(5,'4/16/2024, 13:13:50',1,'Unable to process transaction. Please resubmit','210','Unable to process transaction. Please resubmit'),
(6,'4/16/2024, 13:13:50',1,'Transaction is a duplicate','220','Transaction is a duplicate'),
(7,'4/16/2024, 13:13:50',1,'XML syntax error – Parser error (error that would be caught by the XML parser. Xpath of the element must accompany)','500','XML syntax error – Parser error (error that would be caught by the XML parser. Xpath of the element must accompany)'),
(8,'4/16/2024, 13:13:50',1,'Unable to identify based on the information submitted (Xpath of the element must accompany)','1000','Unable to identify based on the information submitted (Xpath of the element must accompany)'),
(9,'4/16/2024, 13:13:50',1,'Data format is valid for the element, but content is invalid for the situation/context (Xpath of the element must accompany)','2000','Data format is valid for the element, but content is invalid for the situation/context (Xpath of the element must accompany)'),
(10,'4/16/2024, 13:13:50',1,'Does not follow NCPDP standard or implementation guide rules (Xpath of the element must accompany','3000','Does not follow NCPDP standard or implementation guide rules (Xpath of the element must accompany'),
(11,'4/16/2024, 13:13:50',1,'Intermediary is unable to deliver transaction to the recipient','4000','Intermediary is unable to deliver transaction to the recipient'),
(12,'4/16/2024, 13:13:50',1,'Intermediary is unable to process response from recipient','4010','Intermediary is unable to process response from recipient'),
(13,'4/16/2024, 13:13:50',1,'Intermediary system error','4020','Intermediary system error'),
(14,'4/16/2024, 13:13:50',1,'Sender not allowed to send this transaction type','4030','Sender not allowed to send this transaction type'),
(15,'4/16/2024, 13:13:50',1,'Receiver does not support receiving this transaction type','4040','Receiver does not support receiving this transaction type')
;
ALTER SEQUENCE "form_list_ss_error_desc_id_seq" RESTART WITH 16;
SET session_replication_role = DEFAULT;
COMMIT;