BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_payer_type;
INSERT INTO form_list_payer_type ("code","name","id","created_on","created_by","auto_name") VALUES ('MCRB','Medicare Part B',1,'2024-03-19 22:39:32',1,'Medicare Part B')
,('MCRD','Medicare Part D',2,'2024-03-19 22:39:32',1,'Medicare Part D')
,('MEDI','Medicaid',3,'2024-03-19 22:39:32',1,'Medicaid')
,('CMMED','Commercial Medical',4,'2024-03-19 22:39:32',1,'Commercial Medical')
,('CMPBM','Commercial PBM',5,'2024-03-19 22:39:32',1,'Commercial PBM')
,('SELF','Self Pay',6,'2024-03-19 22:39:32',1,'Self Pay')
,('COPAY','Copay Assistance',7,'2024-03-19 22:39:32',1,'Copay Assistance')
,('PAP','PAP Program',8,'2024-03-19 22:39:32',1,'PAP Program')
,('FOUND','Foundational Assistance',9,'2024-03-19 22:39:32',1,'Foundational Assistance')
,('HARD','Hardship Waiver',10,'2024-03-19 22:39:32',1,'Hardship Waiver')
,('OTHER','Other',11,'2024-03-19 22:39:32',1,'Other')
,('MCRC','Medicare Part C',12,'2024-03-19 22:39:32',1,'Medicare Part C')
,('COUPON','Coupon',13,'2024-03-19 22:39:32',1,'Coupon')
;
ALTER SEQUENCE "form_list_payer_type_id_seq" RESTART WITH 14;
SET session_replication_role = DEFAULT;
 COMMIT;