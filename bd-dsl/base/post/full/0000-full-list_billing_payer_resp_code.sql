BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_payer_resp_code;
INSERT INTO form_list_billing_payer_resp_code ("code","name","id","created_on","created_by","auto_name") VALUES ('A','Payer Responsibility Four',1,'2024-03-19 22:39:32',1,'A - Payer Responsibility Four')
,('B','Payer Responsibility Five',2,'2024-03-19 22:39:32',1,'B - Payer Responsibility Five')
,('C','Payer Responsibility Six',3,'2024-03-19 22:39:32',1,'C - Payer Responsibility Six')
,('D','Payer Responsibility Seven',4,'2024-03-19 22:39:32',1,'D - Payer Responsibility Seven')
,('E','Payer Responsibility Eight',5,'2024-03-19 22:39:32',1,'E - Payer Responsibility Eight')
,('F','Payer Responsibility Nine',6,'2024-03-19 22:39:32',1,'F - Payer Responsibility Nine')
,('G','Payer Responsibility Ten',7,'2024-03-19 22:39:32',1,'G - Payer Responsibility Ten')
,('H','Payer Responsibility Eleven',8,'2024-03-19 22:39:32',1,'H - Payer Responsibility Eleven')
,('P','Primary',9,'2024-03-19 22:39:32',1,'P - Primary')
,('S','Secondary',10,'2024-03-19 22:39:32',1,'S - Secondary')
,('T','Tertiary',11,'2024-03-19 22:39:32',1,'T - Tertiary')
,('U','Unknown',12,'2024-03-19 22:39:32',1,'U - Unknown')
;
ALTER SEQUENCE "form_list_billing_payer_resp_code_id_seq" RESTART WITH 13;
SET session_replication_role = DEFAULT;
 COMMIT;
