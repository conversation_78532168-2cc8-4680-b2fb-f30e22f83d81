BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_provider_ref_qualifier;
INSERT INTO form_list_provider_ref_qualifier ("code","name","id","created_on","created_by","auto_name") VALUES ('D3','National Association of Boards of Pharmacy Number',1,'2024-03-19 22:39:32',1,'D3 - National Association of Boards of Pharmacy Number')
,('EI','Employer''s Identification Number',2,'2024-03-19 22:39:32',1,'EI - Employer''s Identification Number')
,('HPI','Health Care Financing Administration National Provider Identification Number',3,'2024-03-19 22:39:32',1,'HPI - Health Care Financing Administration National Provider Identification Number')
,('PXC','Taxonomy Code (5010)',4,'2024-03-19 22:39:32',1,'PXC - Taxonomy Code (5010)')
,('SY','Social Security Number',5,'2024-03-19 22:39:32',1,'SY - Social Security Number')
,('TJ','Federal Taxpayer''s Identification Number',6,'2024-03-19 22:39:32',1,'TJ - Federal Taxpayer''s Identification Number')
,('ZZ','Health Care Provider Taxonomy Code',7,'2024-03-19 22:39:32',1,'ZZ - Health Care Provider Taxonomy Code')
;
ALTER SEQUENCE "form_list_provider_ref_qualifier_id_seq" RESTART WITH 8;
SET session_replication_role = DEFAULT;
 COMMIT;
