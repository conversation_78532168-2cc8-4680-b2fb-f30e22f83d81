BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_ncpdp_segment;
INSERT INTO form_list_ncpdp_segment ("code", "name", "transaction_code", "id","created_on","created_by","auto_name","allow_sync","active") VALUES 
('pharmacy','Pharmacy','{"B1","B3", "S1", "S3", "E1"}', 1,'2024-07-01 20:02:21',1,'Pharmacy', 'Yes', 'Yes')
,('clinical','Clinical','{"B1","B3", "S1", "S3"}', 2,'2024-07-01 20:02:21',1,'Clinical', 'Yes', 'Yes')
,('facility','Facility','{"B1","B3", "S1", "S3"}', 3,'2024-07-01 20:02:21',1,'Facility', 'Yes', 'Yes')
,('dur_pps','DUR/PPS','{"B1", "B2", "B3", "S1", "S3"}', 4,'2024-07-01 20:02:21',1,'DUR/PPS', 'Yes', 'Yes')
,('coupon','Coupon','{"B1","B3", "S1", "S3"}', 5,'2024-07-01 20:02:21',1,'Coupon', 'Yes', 'Yes')
,('addl_docs','Addl. Docs','{"B1","B3", "S1", "S3", "E1"}', 6,'2024-07-01 20:02:21',1,'Addl. Docs', 'Yes', 'Yes')
,('workers_comp','Worker''s Comp','{"B1","B3", "S1", "S3"}', 7,'2024-07-01 20:02:21',1,'Worker''s Comp', 'Yes', 'Yes')
,('narrative','Narrative','{"B1","B3", "S1", "S3"}', 8,'2024-07-01 20:02:21',1,'Narrative', 'Yes', 'Yes')
,('cob','COB','{"B1", "B2", "B3", "S1", "S2", "S3"}', 9,'2024-07-01 20:02:21',1,'COB', 'Yes', 'Yes')
;
ALTER SEQUENCE "form_list_ncpdp_segment_id_seq" RESTART WITH 10;
SET session_replication_role = DEFAULT;
 COMMIT;
