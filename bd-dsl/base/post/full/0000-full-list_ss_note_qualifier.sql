BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_note_qualifier;
INSERT INTO form_list_ss_note_qualifier ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:34:48',1,'Dose(s) administered from Emergency box (kit) or automated dispensing machine.','AA','Dose(s) administered from Emergency box (kit) or automated dispensing machine.'),
(2,'4/16/2024, 06:34:48',1,'Product has been contaminated during administration.','AB','Product has been contaminated during administration.'),
(3,'4/16/2024, 06:34:48',1,'Patient requires oral solid medications to be crushed before administration.','AC','Patient requires oral solid medications to be crushed before administration.'),
(4,'4/16/2024, 06:34:48',1,'Patient requests/requires oral solid medications to be smallest tablet/capsule.','AD','Patient requests/requires oral solid medications to be smallest tablet/capsule.'),
(5,'4/16/2024, 06:34:48',1,'Medications administered via gastric tube.','AE','Medications administered via gastric tube.'),
(6,'4/16/2024, 06:34:48',1,'Patient requests product to be dispensed as written.','AF','Patient requests product to be dispensed as written.'),
(7,'4/16/2024, 06:34:48',1,'Compound requested – allergy to inactive ingredient found in commercially available product.','AG','Compound requested – allergy to inactive ingredient found in commercially available product.'),
(8,'4/16/2024, 06:34:48',1,'Compound requested – dosage form not commercially available.','AH','Compound requested – dosage form not commercially available.'),
(9,'4/16/2024, 06:34:48',1,'Compound requested – strength not commercially available.','AJ','Compound requested – strength not commercially available.'),
(10,'4/16/2024, 06:34:48',1,'Incremental Dispensing – prescriber allows pharmacy to dispense in quantities less than the prescribed quantity.','AK','Incremental Dispensing – prescriber allows pharmacy to dispense in quantities less than the prescribed quantity.'),
(11,'4/16/2024, 06:34:48',1,'Patient needs labs, appointment, or office visit.','AL','Patient needs labs, appointment, or office visit.'),
(12,'4/16/2024, 06:34:48',1,'Patient bringing drug coupon, discount, or other benefit card.','AM','Patient bringing drug coupon, discount, or other benefit card.'),
(13,'4/16/2024, 06:34:48',1,'This prescription is a mail-order, vacation, lost, stolen, or replacement supply.','AN','This prescription is a mail-order, vacation, lost, stolen, or replacement supply.')
;
ALTER SEQUENCE "form_list_ss_note_qualifier_id_seq" RESTART WITH 14;
SET session_replication_role = DEFAULT;
COMMIT;