BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_responsibility_code;
INSERT INTO form_list_responsibility_code ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name") VALUES
(1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'01 - Amount Applied to Periodic Deductible (517-FH)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'01','Amount Applied to Periodic Deductible (517-FH)'),
(2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'02 - Amount Applied to Product Selection/Brand Drug (134-UK)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'02','Amount Applied to Product Selection/Brand Drug (134-UK)'),
(3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'03 - Amount Applied to Sales Tax (523-FN)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'03','Amount Applied to Sales Tax (523-FN)'),
(4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'04 - Amount Exceeding Periodic Benefit Maximum (520-FK)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'04','Amount Exceeding Periodic Benefit Maximum (520-FK)'),
(5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'05 - Amount of Copay (518-FI)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'05','Amount of Copay (518-FI)'),
(6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'06 - Patient Pay Amount (505-F5)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'06','Patient Pay Amount (505-F5)'),
(7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'07 - Amount of Coinsurance (572-4U)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'07','Amount of Coinsurance (572-4U)'),
(8,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'08 - Amount Attributed to Product Selection/Non-Preferred Formulary Selection (135-UM)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'08','Amount Attributed to Product Selection/Non-Preferred Formulary Selection (135-UM)'),
(9,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'09 - Amount Attributed to Health Plan Assistance Amount (129-UJ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'09','Amount Attributed to Health Plan Assistance Amount (129-UJ)'),
(10,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'10 - Amount Attributed to Provider Network Selection (133-UJ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'10','Amount Attributed to Provider Network Selection (133-UJ)'),
(11,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'11 - Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection (136-UN)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'11','Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection (136-UN)'),
(12,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'12 - Amount Attributed to Coverage Gap (137-UP) that was to be collected from the due patient due to a coverage gap',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'12','Amount Attributed to Coverage Gap (137-UP) that was to be collected from the due patient due to a coverage gap'),
(13,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'13 - Amount Attributed to Processor Fee (571-NZ)',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'13','Amount Attributed to Processor Fee (571-NZ)');
SET session_replication_role = DEFAULT;
COMMIT;
