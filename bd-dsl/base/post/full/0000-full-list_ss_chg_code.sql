BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_chg_code;
INSERT INTO form_list_ss_chg_code ("id","created_on","created_by","auto_name","code","name","dea_restricted","linked_action") VALUES (1,'4/16/2024, 06:29:55',1,'Prior Authorization Request','P','Prior Authorization Request','No','pa'),
(2,'4/16/2024, 06:29:55',1,'Generic Substitution','G','Generic Substitution','Yes','generic'),
(3,'4/16/2024, 06:29:55',1,'Therapeutic Interchange/Substitution','T','Therapeutic Interchange/Substitution','Yes','therapeutic'),
(4,'4/16/2024, 06:29:55',1,'DUE - Drug Utilization Evaluation','D','DUE - Drug Utilization Evaluation','Yes','allergy'),
(5,'4/16/2024, 06:29:55',1,'Script Clarification','S','Script Clarification','Yes','clarification'),
(6,'4/16/2024, 06:29:55',1,'Out of Stock','OS','Out of Stock','Yes','oos'),
(7,'4/16/2024, 06:29:55',1,'Prescriber Authorization (i.e. confirm DEA number or enrollment with Insurance Plan)','U','Prescriber Authorization (i.e. confirm DEA number or enrollment with Insurance Plan)','No','provider')
;
ALTER SEQUENCE "form_list_ss_chg_code_id_seq" RESTART WITH 8;
SET session_replication_role = DEFAULT;
COMMIT;