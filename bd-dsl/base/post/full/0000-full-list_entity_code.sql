BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_entity_code;
INSERT INTO form_list_entity_code ("code","name","id","created_on","created_by","auto_name") VALUES ('03','Dependent',1,'2024-03-19 22:39:32',1,'03 - Dependent')
,('13','Contracted Service Provider',2,'2024-03-19 22:39:32',1,'13 - Contracted Service Provider')
,('1I','Preferred Provider Organization',3,'2024-03-19 22:39:32',1,'1I - Preferred Provider Organization')
,('1P','Provider',4,'2024-03-19 22:39:32',1,'1P - Provider')
,('2B','Third-Party Administrator',5,'2024-03-19 22:39:32',1,'2B - Third-Party Administrator')
,('36','Employer',6,'2024-03-19 22:39:32',1,'36 - Employer')
,('73','Other Physician',7,'2024-03-19 22:39:32',1,'73 - Other Physician')
,('80','Hospital',8,'2024-03-19 22:39:32',1,'80 - Hospital')
,('FA','Facility',9,'2024-03-19 22:39:32',1,'FA - Facility')
,('GP','Gateway Provider',10,'2024-03-19 22:39:32',1,'GP - Gateway Provider')
,('GW','Group',11,'2024-03-19 22:39:32',1,'GW - Group')
,('I3','Independent Physicians Association',12,'2024-03-19 22:39:32',1,'I3 - Independent Physicians Association')
,('IL','Insured or Subscriber',13,'2024-03-19 22:39:32',1,'IL - Insured or Subscriber')
,('LR','Legal Representative',14,'2024-03-19 22:39:32',1,'LR - Legal Representative')
,('OC','Origin Carrier',15,'2024-03-19 22:39:32',1,'OC - Origin Carrier')
,('P3','Primary Care Provider',16,'2024-03-19 22:39:32',1,'P3 - Primary Care Provider')
,('P4','Prior Insurance Carrier',17,'2024-03-19 22:39:32',1,'P4 - Prior Insurance Carrier')
,('P5','Plan Sponsor',18,'2024-03-19 22:39:32',1,'P5 - Plan Sponsor')
,('PR','Payer',19,'2024-03-19 22:39:32',1,'PR - Payer')
,('PRP','Primary Payer',20,'2024-03-19 22:39:32',1,'PRP - Primary Payer')
,('SEP','Secondary Payer',21,'2024-03-19 22:39:32',1,'SEP - Secondary Payer')
,('TTP','Tertiary Payer',22,'2024-03-19 22:39:32',1,'TTP - Tertiary Payer')
,('VN','Vendor',23,'2024-03-19 22:39:32',1,'VN - Vendor')
,('VY','Organization Completing Configuration Change',24,'2024-03-19 22:39:32',1,'VY - Organization Completing Configuration Change')
,('X3','Utilization Management Organization',25,'2024-03-19 22:39:32',1,'X3 - Utilization Management Organization')
;
ALTER SEQUENCE "form_list_entity_code_id_seq" RESTART WITH 26;
SET session_replication_role = DEFAULT;
 COMMIT;
