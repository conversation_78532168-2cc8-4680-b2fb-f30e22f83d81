BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_group_reason;
INSERT INTO form_list_ss_group_reason ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:24:57',1,'Additional Services','AdditionalServices','Additional Services'),
(2,'4/16/2024, 06:24:57',1,'IV Therapy','IVTherapy','IV Therapy'),
(3,'4/16/2024, 06:24:57',1,'Loading Quantity','LoadingQuantity','Loading Quantity'),
(4,'4/16/2024, 06:24:57',1,'Maintenance Quantity','MaintenanceQuantity','Maintenance Quantity'),
(5,'4/16/2024, 06:24:57',1,'Multiple Products Prescribed At Same Time','MultipleProductsPrescribed','Multiple Products Prescribed At Same Time'),
(6,'4/16/2024, 06:24:57',1,'Unbreakable Package Multiple Administration Locations Quantity','UnbreakablePkgMultipleLoc','Unbreakable Package Multiple Administration Locations Quantity'),
(7,'4/16/2024, 06:24:57',1,'New RX','NewRx','New RX'),
(8,'4/16/2024, 06:24:57',1,'Subsequent Order','SubsequentOrder','Subsequent Order'),
(9,'4/16/2024, 06:24:57',1,'Trial Fill','TrialFill','Trial Fill'),
(10,'4/16/2024, 06:24:57',1,'Other','Other','Other'),
(11,'4/16/2024, 06:24:57',1,'Non-Commercially Available Dose','NonCommerciallyAvailableDose','Non-Commercially Available Dose')
;
ALTER SEQUENCE "form_list_ss_group_reason_id_seq" RESTART WITH 12;
SET session_replication_role = DEFAULT;
COMMIT;