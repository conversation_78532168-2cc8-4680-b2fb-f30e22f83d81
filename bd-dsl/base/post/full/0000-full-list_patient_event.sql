BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_patient_event;
INSERT INTO form_list_patient_event ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 07:12:25',1,'Patient Record Created','CREATE','Patient Record Created'),
(2,'4/16/2024, 07:12:25',1,'Patient Status Change','STATUS','Patient Status Change'),
(3,'4/16/2024, 07:12:25',1,'Patient First Dispense','FIRSTDISP','Patient First Dispense'),
(4,'4/16/2024, 07:12:25',1,'Patient Site Transfer','TRANSFER','Patient Site Transfer'),
(5,'4/16/2024, 07:12:25',1,'Patient ER Visit','ERVISIT','Patient ER Visit'),
(6,'4/16/2024, 07:12:25',1,'Patient Hospitalization','HOSPITAL','Patient Hospitalization'),
(7,'4/16/2024, 07:12:25',1,'Patient ADR','ADR','Patient ADR'),
(8,'4/16/2024, 07:12:25',1,'Patient Complaint','COMPLAINT','Patient Complaint'),
(9,'4/16/2024, 07:12:25',1,'Patient Catheter Event','CATHETER','Patient Catheter Event'),
(10,'4/16/2024, 07:12:25',1,'Patient Response to Therapy','RESPONSE','Patient Response to Therapy'),
(11,'4/16/2024, 07:12:25',1,'SMS Sign Up','SMSOPTIN','SMS Sign Up'),
(12,'4/16/2024, 07:12:25',1,'SMS Opt Out','SMSOPTOUT','SMS Opt Out'),
(13,'4/16/2024, 07:12:25',1,'Portal Sign Up','PORTALSIGNUP','Portal Sign Up'),
(14,'4/16/2024, 07:12:25',1,'Portal First Sign','PORTALSIGNIN','Portal First Sign')
;
ALTER SEQUENCE "form_list_patient_event_id_seq" RESTART WITH 15;
SET session_replication_role = DEFAULT;
COMMIT;