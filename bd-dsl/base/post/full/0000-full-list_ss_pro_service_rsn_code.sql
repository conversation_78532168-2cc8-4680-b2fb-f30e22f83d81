BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_pro_service_rsn_code;
INSERT INTO form_list_ss_pro_service_rsn_code ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:36:41',1,'Self-care consultation','SC','Self-care consultation'),
(2,'4/16/2024, 06:36:41',1,'Other Acknowledgment','ZZ','Other Acknowledgment'),
(3,'4/16/2024, 06:36:41',1,'Pharmacist consulted other source','RO','Pharmacist consulted other source'),
(4,'4/16/2024, 06:36:41',1,'Payer/processor consulted','TC','Payer/processor consulted'),
(5,'4/16/2024, 06:36:41',1,'Medication review','MR','Medication review'),
(6,'4/16/2024, 06:36:41',1,'Formulary enforcement','FE','Formulary enforcement'),
(7,'4/16/2024, 06:36:41',1,'Coordination of care','CC','Coordination of care'),
(8,'4/16/2024, 06:36:41',1,'Patient monitoring','PM','Patient monitoring'),
(9,'4/16/2024, 06:36:41',1,'Patient education/instruction','PE','Patient education/instruction'),
(10,'4/16/2024, 06:36:41',1,'Prescriber consulted','MO','Prescriber consulted'),
(11,'4/16/2024, 06:36:41',1,'Therapeutic product interchange','TH','Therapeutic product interchange'),
(12,'4/16/2024, 06:36:41',1,'Previous patient tolerance','PA','Previous patient tolerance'),
(13,'4/16/2024, 06:36:41',1,'Recommend laboratory test','RT','Recommend laboratory test'),
(14,'4/16/2024, 06:36:41',1,'Patient assessment','AS','Patient assessment'),
(15,'4/16/2024, 06:36:41',1,'Dosing evaluation/determination','DE','Dosing evaluation/determination'),
(16,'4/16/2024, 06:36:41',1,'Patient consulted','P0','Patient consulted'),
(17,'4/16/2024, 06:36:41',1,'Medication administration','MA','Medication administration'),
(18,'4/16/2024, 06:36:41',1,'Literature search/review','SW','Literature search/review'),
(19,'4/16/2024, 06:36:41',1,'Overriding benefit','MB','Overriding benefit'),
(20,'4/16/2024, 06:36:41',1,'No intervention','00','No intervention'),
(21,'4/16/2024, 06:36:41',1,'Perform laboratory test','PT','Perform laboratory test'),
(22,'4/16/2024, 06:36:41',1,'Generic product selection','GP','Generic product selection'),
(23,'4/16/2024, 06:36:41',1,'Patient medication history','PH','Patient medication history'),
(24,'4/16/2024, 06:36:41',1,'Dosage evaluated','DP','Dosage evaluated'),
(25,'4/16/2024, 06:36:41',1,'Patient will be monitored','MP','Patient will be monitored')
;
ALTER SEQUENCE "form_list_ss_pro_service_rsn_code_id_seq" RESTART WITH 26;
SET session_replication_role = DEFAULT;
COMMIT;