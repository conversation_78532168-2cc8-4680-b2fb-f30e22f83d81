BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_ncpdp_response_code;
INSERT INTO form_list_ncpdp_response_code ("code","name","id","created_on","created_by","auto_name") VALUES ('0','Transaction approved or completed successfully',1,'2024-03-19 22:39:32',1,'0 - Transaction approved or completed successfully')
,('1','Call issuer for further information',2,'2024-03-19 22:39:32',1,'1 - Call issuer for further information')
,('2','Call security for further information',3,'2024-03-19 22:39:32',1,'2 - Call security for further information')
,('3','Invalid merchant',4,'2024-03-19 22:39:32',1,'3 - Invalid merchant')
,('4','Pickup card',5,'2024-03-19 22:39:32',1,'4 - Pickup card')
,('5','Do not honor',6,'2024-03-19 22:39:32',1,'5 - Do not honor')
,('6','Error',7,'2024-03-19 22:39:32',1,'6 - Error')
,('7','Pick up card, special condition',8,'2024-03-19 22:39:32',1,'7 - Pick up card, special condition')
,('8','Honor with identification',9,'2024-03-19 22:39:32',1,'8 - Honor with identification')
,('9','Request in progress',10,'2024-03-19 22:39:32',1,'9 - Request in progress')
,('10','Approved for partial amount',11,'2024-03-19 22:39:32',1,'10 - Approved for partial amount')
,('11','Approved, VIP',12,'2024-03-19 22:39:32',1,'11 - Approved, VIP')
,('12','Invalid transaction',13,'2024-03-19 22:39:32',1,'12 - Invalid transaction')
,('13','Invalid amount',14,'2024-03-19 22:39:32',1,'13 - Invalid amount')
,('14','Invalid card number',15,'2024-03-19 22:39:32',1,'14 - Invalid card number')
,('15','No such issuer',16,'2024-03-19 22:39:32',1,'15 - No such issuer')
,('16','Approved, update track 3',17,'2024-03-19 22:39:32',1,'16 - Approved, update track 3')
,('17','Customer cancellation',18,'2024-03-19 22:39:32',1,'17 - Customer cancellation')
,('18','Customer dispute',19,'2024-03-19 22:39:32',1,'18 - Customer dispute')
,('19','Re-enter transaction',20,'2024-03-19 22:39:32',1,'19 - Re-enter transaction')
,('20','Invalid response',21,'2024-03-19 22:39:32',1,'20 - Invalid response')
,('21','No action taken',22,'2024-03-19 22:39:32',1,'21 - No action taken')
,('22','Suspected malfunction',23,'2024-03-19 22:39:32',1,'22 - Suspected malfunction')
,('23','Unacceptable transaction fee',24,'2024-03-19 22:39:32',1,'23 - Unacceptable transaction fee')
,('24','File update not supported',25,'2024-03-19 22:39:32',1,'24 - File update not supported')
,('25','Unable to locate record',26,'2024-03-19 22:39:32',1,'25 - Unable to locate record')
,('26','Duplicate record, old record replaced',27,'2024-03-19 22:39:32',1,'26 - Duplicate record, old record replaced')
,('27','File update field edit error',28,'2024-03-19 22:39:32',1,'27 - File update field edit error')
,('28','File update file locked',29,'2024-03-19 22:39:32',1,'28 - File update file locked')
,('29','File update failed',30,'2024-03-19 22:39:32',1,'29 - File update failed')
,('30','Format error',31,'2024-03-19 22:39:32',1,'30 - Format error')
,('31','Bank not supported by switch',32,'2024-03-19 22:39:32',1,'31 - Bank not supported by switch')
,('32','Completed partially',33,'2024-03-19 22:39:32',1,'32 - Completed partially')
,('33','Expired card, pick up',34,'2024-03-19 22:39:32',1,'33 - Expired card, pick up')
,('34','Suspected fraud, pick up',35,'2024-03-19 22:39:32',1,'34 - Suspected fraud, pick up')
,('35','Card acceptor contact acquirer, pick up',36,'2024-03-19 22:39:32',1,'35 - Card acceptor contact acquirer, pick up')
,('36','Restricted card, pick up',37,'2024-03-19 22:39:32',1,'36 - Restricted card, pick up')
,('37','Call acquirer security, pick up',38,'2024-03-19 22:39:32',1,'37 - Call acquirer security, pick up')
,('38','Allowable PIN tries exceeded, pick up',39,'2024-03-19 22:39:32',1,'38 - Allowable PIN tries exceeded, pick up')
,('39','No credit account',40,'2024-03-19 22:39:32',1,'39 - No credit account')
,('40','Function not supported',41,'2024-03-19 22:39:32',1,'40 - Function not supported')
,('41','Lost card, pick up',42,'2024-03-19 22:39:32',1,'41 - Lost card, pick up')
,('42','No universal account',43,'2024-03-19 22:39:32',1,'42 - No universal account')
,('43','Stolen card, pick up',44,'2024-03-19 22:39:32',1,'43 - Stolen card, pick up')
,('44','No investment account',45,'2024-03-19 22:39:32',1,'44 - No investment account')
,('45','Account closed',46,'2024-03-19 22:39:32',1,'45 - Account closed')
,('46','Identification required',47,'2024-03-19 22:39:32',1,'46 - Identification required')
,('47','Identification cross-reference error',48,'2024-03-19 22:39:32',1,'47 - Identification cross-reference error')
,('48','No savings account',49,'2024-03-19 22:39:32',1,'48 - No savings account')
,('49','Invalid PIN',50,'2024-03-19 22:39:32',1,'49 - Invalid PIN')
,('50','No checking account',51,'2024-03-19 22:39:32',1,'50 - No checking account')
,('51','Insufficient funds',52,'2024-03-19 22:39:32',1,'51 - Insufficient funds')
,('52','No savings account',53,'2024-03-19 22:39:32',1,'52 - No savings account')
,('53','No checking account',54,'2024-03-19 22:39:32',1,'53 - No checking account')
,('54','Expired card',55,'2024-03-19 22:39:32',1,'54 - Expired card')
,('55','Incorrect PIN',56,'2024-03-19 22:39:32',1,'55 - Incorrect PIN')
,('56','No card record',57,'2024-03-19 22:39:32',1,'56 - No card record')
,('57','Transaction not permitted to cardholder',58,'2024-03-19 22:39:32',1,'57 - Transaction not permitted to cardholder')
,('58','Transaction not permitted to terminal',59,'2024-03-19 22:39:32',1,'58 - Transaction not permitted to terminal')
,('59','Suspected fraud',60,'2024-03-19 22:39:32',1,'59 - Suspected fraud')
,('60','Contact acquirer',61,'2024-03-19 22:39:32',1,'60 - Contact acquirer')
,('61','Exceeds withdrawal limit',62,'2024-03-19 22:39:32',1,'61 - Exceeds withdrawal limit')
,('62','Restricted card',63,'2024-03-19 22:39:32',1,'62 - Restricted card')
,('63','Security violation',64,'2024-03-19 22:39:32',1,'63 - Security violation')
,('64','Original amount incorrect',65,'2024-03-19 22:39:32',1,'64 - Original amount incorrect')
,('65','Exceeds withdrawal frequency limit',66,'2024-03-19 22:39:32',1,'65 - Exceeds withdrawal frequency limit')
,('66','Call acquirer''s security department',67,'2024-03-19 22:39:32',1,'66 - Call acquirer''s security department')
,('67','Hard capture (requires card to be picked up at ATM)',68,'2024-03-19 22:39:32',1,'67 - Hard capture (requires card to be picked up at ATM)')
,('68','Response received too late',69,'2024-03-19 22:39:32',1,'68 - Response received too late')
,('75','PIN tries exceeded',70,'2024-03-19 22:39:32',1,'75 - PIN tries exceeded')
,('76','Unable to locate previous message',71,'2024-03-19 22:39:32',1,'76 - Unable to locate previous message')
,('77','Repeat message',72,'2024-03-19 22:39:32',1,'77 - Repeat message')
,('78','Blocked, first used',73,'2024-03-19 22:39:32',1,'78 - Blocked, first used')
,('79','Already reversed',74,'2024-03-19 22:39:32',1,'79 - Already reversed')
,('80','Network error',75,'2024-03-19 22:39:32',1,'80 - Network error')
,('81','Cryptographic error',76,'2024-03-19 22:39:32',1,'81 - Cryptographic error')
,('82','Issuer signed-off',77,'2024-03-19 22:39:32',1,'82 - Issuer signed-off')
,('83','No key exchange key',78,'2024-03-19 22:39:32',1,'83 - No key exchange key')
,('84','No response from host',79,'2024-03-19 22:39:32',1,'84 - No response from host')
,('85','No reason to decline a request for account number verification',80,'2024-03-19 22:39:32',1,'85 - No reason to decline a request for account number verification')
,('91','Issuer unavailable',81,'2024-03-19 22:39:32',1,'91 - Issuer unavailable')
,('92','Destination not found',82,'2024-03-19 22:39:32',1,'92 - Destination not found')
,('93','Transaction cannot be completed',83,'2024-03-19 22:39:32',1,'93 - Transaction cannot be completed')
,('94','Duplicate transmission',84,'2024-03-19 22:39:32',1,'94 - Duplicate transmission')
,('95','Reconcile error',85,'2024-03-19 22:39:32',1,'95 - Reconcile error')
,('96','System malfunction',86,'2024-03-19 22:39:32',1,'96 - System malfunction')
,('97','Auth unavailable',87,'2024-03-19 22:39:32',1,'97 - Auth unavailable')
,('98','Refund timeout',88,'2024-03-19 22:39:32',1,'98 - Refund timeout')
,('99','Reserved for national use',89,'2024-03-19 22:39:32',1,'99 - Reserved for national use')
;
ALTER SEQUENCE "form_list_ncpdp_response_code_id_seq" RESTART WITH 90;
SET session_replication_role = DEFAULT;
 COMMIT;
