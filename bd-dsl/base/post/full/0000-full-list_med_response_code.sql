BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_med_response_code;
INSERT INTO form_list_med_response_code ("code","name","id","created_on","created_by","auto_name") VALUES ('AA','Accepted - Successfully processed.',1,'2024-03-19 22:39:32',1,'AA - Accepted - Successfully processed.')
,('AE','Accepted with Errors - Processed with some errors, but still accepted.',2,'2024-03-19 22:39:32',1,'AE - Accepted with Errors - Processed with some errors, but still accepted.')
,('AR','Accepted with Revisions - Accepted after some revisions were made.',3,'2024-03-19 22:39:32',1,'AR - Accepted with Revisions - Accepted after some revisions were made.')
,('CE','Contractual Obligations - Claim processed as per contractual agreements.',4,'2024-03-19 22:39:32',1,'CE - Contractual Obligations - Claim processed as per contractual agreements.')
,('CR','Complete Resolution - Claim processed and resolved completely.',5,'2024-03-19 22:39:32',1,'CR - Complete Resolution - Claim processed and resolved completely.')
,('D0','Claim sent for additional documentation or clarification.',6,'2024-03-19 22:39:32',1,'D0 - Claim sent for additional documentation or clarification.')
,('D1','Claim sent for primary carrier''s explanation of benefits (EOB).',7,'2024-03-19 22:39:32',1,'D1 - Claim sent for primary carrier''s explanation of benefits (EOB).')
,('D2','Claim sent for secondary carrier''s EOB.',8,'2024-03-19 22:39:32',1,'D2 - Claim sent for secondary carrier''s EOB.')
,('D3','Claim sent for tertiary carrier''s EOB.',9,'2024-03-19 22:39:32',1,'D3 - Claim sent for tertiary carrier''s EOB.')
,('DA','Claim denied, appeal may be requested.',10,'2024-03-19 22:39:32',1,'DA - Claim denied, appeal may be requested.')
,('DD','Denied - Claim rejected or denied.',11,'2024-03-19 22:39:32',1,'DD - Denied - Claim rejected or denied.')
,('DI','Claim denied as patient is not eligible for benefits.',12,'2024-03-19 22:39:32',1,'DI - Claim denied as patient is not eligible for benefits.')
,('DJ','Claim denied as patient is deceased.',13,'2024-03-19 22:39:32',1,'DJ - Claim denied as patient is deceased.')
,('DK','Claim denied due to lack of authorization.',14,'2024-03-19 22:39:32',1,'DK - Claim denied due to lack of authorization.')
,('DN','Claim denied as subscriber is not eligible.',15,'2024-03-19 22:39:32',1,'DN - Claim denied as subscriber is not eligible.')
,('DU','Claim denied as services are not covered.',16,'2024-03-19 22:39:32',1,'DU - Claim denied as services are not covered.')
,('E1','Claim has been converted from paper to electronic format.',17,'2024-03-19 22:39:32',1,'E1 - Claim has been converted from paper to electronic format.')
,('E2','Claim has been converted from electronic to paper format.',18,'2024-03-19 22:39:32',1,'E2 - Claim has been converted from electronic to paper format.')
,('E3','Claim has been forwarded to another entity for processing.',19,'2024-03-19 22:39:32',1,'E3 - Claim has been forwarded to another entity for processing.')
,('EA','Adjustment - Claim adjusted.',20,'2024-03-19 22:39:32',1,'EA - Adjustment - Claim adjusted.')
,('EC','Coordination of Benefits (COB) Amount.',21,'2024-03-19 22:39:32',1,'EC - Coordination of Benefits (COB) Amount.')
,('ED','No COB Amount.',22,'2024-03-19 22:39:32',1,'ED - No COB Amount.')
,('EH','Health Coverage Discrepancy - Information in claim differs from health plan''s records.',23,'2024-03-19 22:39:32',1,'EH - Health Coverage Discrepancy - Information in claim differs from health plan''s records.')
,('EI','Coverage Gap - Claim not covered due to coverage gap.',24,'2024-03-19 22:39:32',1,'EI - Coverage Gap - Claim not covered due to coverage gap.')
,('EJ','Payment was made, but is less than expected.',25,'2024-03-19 22:39:32',1,'EJ - Payment was made, but is less than expected.')
,('EK','Subscriber not found.',26,'2024-03-19 22:39:32',1,'EK - Subscriber not found.')
,('EL','Claim lacks information, resubmission requested.',27,'2024-03-19 22:39:32',1,'EL - Claim lacks information, resubmission requested.')
,('EM','Medicare must be billed first.',28,'2024-03-19 22:39:32',1,'EM - Medicare must be billed first.')
,('EN','Subscriber and patient are the same.',29,'2024-03-19 22:39:32',1,'EN - Subscriber and patient are the same.')
,('EO','Benefits coordinated, patient has no liability.',30,'2024-03-19 22:39:32',1,'EO - Benefits coordinated, patient has no liability.')
,('EP','Benefits coordinated, patient has liability.',31,'2024-03-19 22:39:32',1,'EP - Benefits coordinated, patient has liability.')
,('EQ','Claim lacks information, additional submission not allowed.',32,'2024-03-19 22:39:32',1,'EQ - Claim lacks information, additional submission not allowed.')
,('ER','Missing/incomplete/invalid prior authorization.',33,'2024-03-19 22:39:32',1,'ER - Missing/incomplete/invalid prior authorization.')
,('ES','Claim lacks information, resubmission allowed.',34,'2024-03-19 22:39:32',1,'ES - Claim lacks information, resubmission allowed.')
,('EV','Third party information is required.',35,'2024-03-19 22:39:32',1,'EV - Third party information is required.')
,('EW','Services are not covered under plan benefits.',36,'2024-03-19 22:39:32',1,'EW - Services are not covered under plan benefits.')
,('EX','Patient not enrolled in this plan.',37,'2024-03-19 22:39:32',1,'EX - Patient not enrolled in this plan.')
,('EZ','Workers'' compensation jurisdictional regulations prohibit this service and it is not covered.',38,'2024-03-19 22:39:32',1,'EZ - Workers'' compensation jurisdictional regulations prohibit this service and it is not covered.')
,('F2','Finalized - Claim finalized and closed.',39,'2024-03-19 22:39:32',1,'F2 - Finalized - Claim finalized and closed.')
,('N2','Incomplete/invalid information on claim, clarification needed.',40,'2024-03-19 22:39:32',1,'N2 - Incomplete/invalid information on claim, clarification needed.')
,('N4','Incorrect or missing provider identification number.',41,'2024-03-19 22:39:32',1,'N4 - Incorrect or missing provider identification number.')
,('N6','Entity not eligible for benefits.',42,'2024-03-19 22:39:32',1,'N6 - Entity not eligible for benefits.')
,('N8','Incorrect claim/format for this payer.',43,'2024-03-19 22:39:32',1,'N8 - Incorrect claim/format for this payer.')
,('P0','Policy/Plan Prohibits Provider - Provider not eligible to receive payment for services rendered.',44,'2024-03-19 22:39:32',1,'P0 - Policy/Plan Prohibits Provider - Provider not eligible to receive payment for services rendered.')
,('P1','Contractual Obligation - Payer''s process is dependent on another payer''s decision.',45,'2024-03-19 22:39:32',1,'P1 - Contractual Obligation - Payer''s process is dependent on another payer''s decision.')
,('P2','Technical Fees - Payer''s fee schedule does not cover this procedure/service.',46,'2024-03-19 22:39:32',1,'P2 - Technical Fees - Payer''s fee schedule does not cover this procedure/service.')
,('P3','Patient Responsibility - Payer''s policy is not to pay for this procedure/service.',47,'2024-03-19 22:39:32',1,'P3 - Patient Responsibility - Payer''s policy is not to pay for this procedure/service.')
,('P4','Payment reduced, patient had non-covered service.',48,'2024-03-19 22:39:32',1,'P4 - Payment reduced, patient had non-covered service.')
,('P5','Payment reduced, as this is considered pre-existing condition.',49,'2024-03-19 22:39:32',1,'P5 - Payment reduced, as this is considered pre-existing condition.')
,('P6','Prior Procedure - Payer''s policy is to not pay for this procedure/service more than once in the same day.',50,'2024-03-19 22:39:32',1,'P6 - Prior Procedure - Payer''s policy is to not pay for this procedure/service more than once in the same day.')
;
ALTER SEQUENCE "form_list_med_response_code_id_seq" RESTART WITH 51;
SET session_replication_role = DEFAULT;
 COMMIT;
