BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_generic_service;
INSERT INTO form_list_billing_generic_service ("name","id","created_on","created_by","auto_name") VALUES ('2nd request for payment',1,'2024-03-19 22:39:32',1,'2nd request for payment')
,('3rd request  for payment',2,'2024-03-19 22:39:32',1,'3rd request  for payment')
,('4th request for payment',3,'2024-03-19 22:39:32',1,'4th request for payment')
,('Contact pharmacy branch for payment plan',4,'2024-03-19 22:39:32',1,'Contact pharmacy branch for payment plan')
,('Copay assitance funds exhausted',5,'2024-03-19 22:39:32',1,'Copay assitance funds exhausted')
,('Copay charge per treatment',6,'2024-03-19 22:39:32',1,'Copay charge per treatment')
,('Credit Card on File',7,'2024-03-19 22:39:32',1,'Credit Card on File')
,('For Home Infusion Therapy Services',8,'2024-03-19 22:39:32',1,'For Home Infusion Therapy Services')
,('For Pain Management Therapy',9,'2024-03-19 22:39:32',1,'For Pain Management Therapy')
,('MEDICARE DEDUCTIBLE NOT COVERED BY AARP',10,'2024-03-19 22:39:32',1,'MEDICARE DEDUCTIBLE NOT COVERED BY AARP')
,('MEDICARE PART B DEDUCTIBLE NOT COVERED',11,'2024-03-19 22:39:32',1,'MEDICARE PART B DEDUCTIBLE NOT COVERED')
,('NO BENEFIT FOR THE MEDICARE PART B DEDUCTIBLE',12,'2024-03-19 22:39:32',1,'NO BENEFIT FOR THE MEDICARE PART B DEDUCTIBLE')
,('OOP expense',13,'2024-03-19 22:39:32',1,'OOP expense')
,('PAST DUE COPAYS',14,'2024-03-19 22:39:32',1,'PAST DUE COPAYS')
,('PLEASE CALL IN REFERENCE TO PAST DUE COPAYS',15,'2024-03-19 22:39:32',1,'PLEASE CALL IN REFERENCE TO PAST DUE COPAYS')
,('PLEASE RETURN HARDSHIP FORM',16,'2024-03-19 22:39:32',1,'PLEASE RETURN HARDSHIP FORM')
;
ALTER SEQUENCE "form_list_billing_generic_service_id_seq" RESTART WITH 17;
SET session_replication_role = DEFAULT;
 COMMIT;
