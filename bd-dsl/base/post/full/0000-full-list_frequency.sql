BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_frequency;
INSERT INTO form_list_frequency ("code","name","multiplier","id","created_on","created_by","auto_name", "label_string") VALUES 
('1X','One Time Only','1.00',1,'2024-03-19 22:39:32',1,'1X One Time Only', 'one time only'),
('1XW1','Time a Week','1.00',2,'2024-03-19 22:39:32',1,'1XW1 Time a Week', 'once a week'),
('2CD/15D2','2 cons days per 15d','0.93',3,'2024-03-19 22:39:32',1,'2CD/15D2 cons days per 15d', 'for 2 consecutive days every 15 days'),
('2CD/21D2','2 cons days per 21d','0.67',4,'2024-03-19 22:39:32',1,'2CD/21D2 cons days per 21d', 'for 2 consecutive days every 21 days'),
('2CD/28D2','2 cons days per 28 days','0.50',5,'2024-03-19 22:39:32',1,'2CD/28D2 cons days per 28 days', 'for 2 consecutive days every 28 days'),
('2CD/2W','2cons days q 2weeks','1.00',6,'2024-03-19 22:39:32',1,'2CD/2W 2con days q 2weeks', 'for 2 consecutive days every 2 weeks'),
('2CD/3W','2cons days per 3w','0.67',7,'2024-03-19 22:39:32',1,'2CD/3W 2cons days per 3w', 'for 2 consecutive days every 3 weeks'),
('2D/14D','2 days per 14 days','1.00',8,'2024-03-19 22:39:32',1,'2D/14D 2 days per 14 days', 'for 2 days every 14 days'),
('2D/21D','2 days per 21 days','0.67',9,'2024-03-19 22:39:32',1,'2D/21D 2 days per 21 days', 'for 2 days every 21 days'),
('2D/28D','2 days per 28 days','0.50',10,'2024-03-19 22:39:32',1,'2D/28D 2 days per 28 days', 'for 2 days every 28 days'),
('2D/30D','2 days per 30 days','0.47',11,'2024-03-19 22:39:32',1,'2D/30D 2 days per 30 days', 'for 2 days every 30 days'),
('2D/6W','2 days per 6 weeks','0.33',12,'2024-03-19 22:39:32',1,'2D/6W 2 days per 6 weeks', 'for 2 days every 6 weeks'),
('2D/Q5W','daily x2 days every 5wks','0.40',13,'2024-03-19 22:39:32',1,'2D/Q5W daily x2 days every 5wks', 'for 2 days every 5 weeks'),
('2XM','twice monthly','0.50',14,'2024-03-19 22:39:32',1,'2XM twice monthly', 'twice monthly'),
('2XW','2 times a Week','2.00',15,'2024-03-19 22:39:32',1,'2XW 2 times a Week', 'twice a week'),
('3CD/21D','3 cons. day/21 day','1.00',16,'2024-03-19 22:39:32',1,'3CD/21D 3 consecutive/21 day', 'for 3 consecutive days every 21 days'),
('3CD/28D','3 cons. day/28 days','0.75',17,'2024-03-19 22:39:32',1,'3CD/28D 3 cons.day/28 days', 'for 3 consecutive days every 28 days'),
('3CD/42D','3 days every 6 weeks','0.50',18,'2024-03-19 22:39:32',1,'3CD/42D 3 days every 6 weeks', 'for 3 days every 6 weeks'),
('3D/21D','3 days per 21 days','1.00',19,'2024-03-19 22:39:32',1,'3D/21D 3 days per 21 days', 'for 3 days every 21 days'),
('3D/28D','3 days per 28 days','0.75',20,'2024-03-19 22:39:32',1,'3D/28D 3 days per 28 days', 'for 3 days every 28 days'),
('3XM','3 times a month','0.75',21,'2024-03-19 22:39:32',1,'3XM 3 times a month', 'three times a month'),
('3XW','3 Times a Week','3.00',22,'2024-03-19 22:39:32',1,'3XW 3 Times a Week', 'three times a week'),
('4CD/28D','4 cons days per 28 d','1.00',23,'2024-03-19 22:39:32',1,'4CD/28D 4 cons days per 28 d', 'for 4 consecutive days every 28 days'),
('4CD/8W','4 cons days per 8w','0.50',24,'2024-03-19 22:39:32',1,'4CD/8W 4 cons days per 8w', 'for 4 consecutive days every 8 weeks'),
('4CD/M','4 cons days per mon','1.00',25,'2024-03-19 22:39:32',1,'4CD/M 4 cons days per mon', 'for 4 consecutive days every month'),
('4D/28D','4 days per 28 days','1.00',26,'2024-03-19 22:39:32',1,'4D/28D 4 days per 28 days', 'for 4 days every 28 days'),
('BID','Twice daily','14.00',27,'2024-03-19 22:39:32',1,'BID Twice daily', 'twice daily'),
('HS','nightly at bedtime','7.00',28,'2024-03-19 22:39:32',1,'HS nightly at bedtime', 'nightly at bedtime'),
('over2D','over 2 days','3.50',29,'2024-03-19 22:39:32',1,'over2D over 2 days', 'over 2 days'),
('over3D','over 3 days','2.33',30,'2024-03-19 22:39:32',1,'over3D over 3 days', 'over 3 days'),
('over4D','over 4 days','1.75',31,'2024-03-19 22:39:32',1,'over4D over 4 days', 'over 4 days'),
('over5D','over 5 days','1.40',32,'2024-03-19 22:39:32',1,'over5D over 5 days', 'over 5 days'),
('over6D','over 6 days','1.17',33,'2024-03-19 22:39:32',1,'over6D over 6 days', 'over 6 days'),
('PRN','as needed','1.00',34,'2024-03-19 22:39:32',1,'PRN as needed', 'as needed'),
('Q10D','every 10 days','0.70',35,'2024-03-19 22:39:32',1,'Q10D every 10 days', 'every 10 days'),
('Q10W','every 10 Weeks','0.10',36,'2024-03-19 22:39:32',1,'Q10W every 10 Weeks', 'every 10 weeks'),
('Q12H','every 12 Hours','14.00',37,'2024-03-19 22:39:32',1,'Q12H every 12 Hours', 'every 12 hours'),
('Q12W','every 12 weeks','0.08',38,'2024-03-19 22:39:32',1,'Q12W every 12 weeks', 'every 12 weeks'),
('Q14-17D','every 14 to 17 days','0.45',39,'2024-03-19 22:39:32',1,'Q14-17D every 14 to 17 days', 'every 14 to 17 days'),
('Q14-19D','every 14-19 days','0.42',40,'2024-03-19 22:39:32',1,'Q14-19D every 14-19 days', 'every 14-19 days'),
('Q14D','every 14 days','0.50',41,'2024-03-19 22:39:32',1,'Q14D every 14 days', 'every 14 days'),
('Q15D','every 15 days','0.47',42,'2024-03-19 22:39:32',1,'Q15D every 15 days', 'every 15 days'),
('Q18D','every 18 days','0.39',43,'2024-03-19 22:39:32',1,'Q18D every 18 days', 'every 18 days'),
('Q1M','every 1 month','0.23',44,'2024-03-19 22:39:32',1,'Q1M every 1 month', 'every 1 month'),
('Q1W','weekly','1.00',45,'2024-03-19 22:39:32',1,'Q1W weekly', 'weekly'),
('Q24H','every 24 hours','7.00',46,'2024-03-19 22:39:32',1,'Q24H every 24 hours', 'every 24 hours'),
('Q28D','every 28 days','0.25',47,'2024-03-19 22:39:32',1,'Q28D every 28 days', 'every 28 days'),
('Q2M','every 2 months','0.12',48,'2024-03-19 22:39:32',1,'Q2M every 2 months', 'every 2 months'),
('Q2W','every 2 weeks','0.50',49,'2024-03-19 22:39:32',1,'Q2W every 2 weeks', 'every 2 weeks'),
('Q3-4W','every 3 to 4 weeks','0.25',50,'2024-03-19 22:39:32',1,'Q3-4W every 3 to 4 weeks', 'every 3 to 4 weeks'),
('Q30D','every 30 days','0.23',51,'2024-03-19 22:39:32',1,'Q30D every 30 days', 'every 30 days'),
('Q3D','every 3 days','2.33',52,'2024-03-19 22:39:32',1,'Q3D every 3 days', 'every 3 days'),
('Q3H','every 3 hours','56.00',53,'2024-03-19 22:39:32',1,'Q3H every 3 hours', 'every 3 hours'),
('Q3M','every 3 months','0.08',54,'2024-03-19 22:39:32',1,'Q3M every 3 months', 'every 3 months'),
('Q3W','every 3 weeks','0.33',55,'2024-03-19 22:39:32',1,'Q3W every 3 weeks', 'every 3 weeks'),
('Q4-5W','every 4-5 weeks','0.20',56,'2024-03-19 22:39:32',1,'Q4-5W every 4-5 weeks', 'every 4-5 weeks'),
('Q4H','every 4 hours','42.00',57,'2024-03-19 22:39:32',1,'Q4H every 4 hours', 'every 4 hours'),
('Q4W','every 4 weeks','0.25',58,'2024-03-19 22:39:32',1,'Q4W every 4 weeks', 'every 4 weeks'),
('Q5D','every 5 days','1.40',59,'2024-03-19 22:39:32',1,'Q5D every 5 days', 'every 5 days'),
('Q5W','every 5 weeks','0.20',60,'2024-03-19 22:39:32',1,'Q5W every 5 weeks', 'every 5 weeks'),
('Q6-8W','every 6-8 weeks','0.13',61,'2024-03-19 22:39:32',1,'Q6-8W every 6-8 weeks', 'every 6-8 weeks'),
('Q6H','every 6 hours','28.00',62,'2024-03-19 22:39:32',1,'Q6H every 6 hours', 'every 6 hours'),
('Q6M','once every 6 months','0.04',63,'2024-03-19 22:39:32',1,'Q6M once every 6 months', 'once every 6 months'),
('Q6W','every 6 weeks','0.17',64,'2024-03-19 22:39:32',1,'Q6W every 6 weeks', 'every 6 weeks'),
('Q7D','every 7 days','1.00',65,'2024-03-19 22:39:32',1,'Q7D every 7 days', 'every 7 days'),
('Q8H','every 8 hours','21.00',66,'2024-03-19 22:39:32',1,'Q8H every 8 hours', 'every 8 hours'),
('Q8W','every 8 weeks','0.13',67,'2024-03-19 22:39:32',1,'Q8W every 8 weeks', 'every 8 weeks'),
('QDAY','daily','7.00',68,'2024-03-19 22:39:32',1,'QDAY daily', 'daily'),
('QDAYx2D','daily for 2 days','2.00',69,'2024-03-19 22:39:32',1,'QDAYx2D daily for 2 days', 'daily for 2 days'),
('QDAYx3D','once daily x 3 days','3.00',70,'2024-03-19 22:39:32',1,'QDAYx3D once daily x 3 days', 'once daily x 3 days'),
('QDAYx4D','daily for 4 days','4.00',71,'2024-03-19 22:39:32',1,'QDAYx4D daily for 4 days', 'daily for 4 days'),
('QDAYx5D','daily for 5 days','5.00',72,'2024-03-19 22:39:32',1,'QDAYx5D daily for 5 days', 'daily for 5 days'),
('qdx2dq15','daily x2 days every 15 day','0.93',73,'2024-03-19 22:39:32',1,'qdx2dq15 daily x2 days every 15 day', 'daily x2 days every 15 day'),
('QHS','at bedtime','7.00',74,'2024-03-19 22:39:32',1,'QHS at bedtime', 'at bedtime'),
('QID','four times daily','28.00',75,'2024-03-19 22:39:32',1,'QID four times daily', 'four times daily'),
('QOD','every other day','3.50',76,'2024-03-19 22:39:32',1,'QOD every other day', 'every other day'),
('QOW','every other week','0.50',77,'2024-03-19 22:39:32',1,'QOW every other week', 'every other week'),
('TID','3 times a day','21.00',78,'2024-03-19 22:39:32',1,'TID 3 times a day', 'three times a day'),
('UAD','use as directed','1.00',79,'2024-03-19 22:39:32',1,'UAD use as directed', 'use as directed'),
('UAD ANA','use as directed ANA','0.10',80,'2024-03-19 22:39:32',1,'UAD ANA use as directed ANA', 'use as directed by nurse');
ALTER SEQUENCE "form_list_frequency_id_seq" RESTART WITH 81;
SET session_replication_role = DEFAULT;
 COMMIT;
