BEGIN;
 DELETE FROM form_list_rx_template;
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'PO',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'PO','PO','PO','Refills','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'IV',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'IV','IV','IV','Doses','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Injection',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Injection','Injection','Injection','Refills','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Factor',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Factor','Factor','Factor','Doses','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Compound',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Compound','Compound','Compound','Doses','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'Reconstituted',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'Reconstituted','Reconstituted','Reconstituted','Doses','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
INSERT INTO form_list_rx_template ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_type", "change_data", "created_on", "change_on", "code", "name", "template_type", "refill_tracking", "allow_sync", "active", "sys_period") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,NULL,'TPN',NULL,NULL,'2024-03-19T22:39:32.000Z',NULL,'TPN','TPN','TPN','Doses','Yes','Yes','["2024-09-04 22:30:19.547488+00",)') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_type" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "code" = EXCLUDED."code", "name" = EXCLUDED."name", "template_type" = EXCLUDED."template_type", "refill_tracking" = EXCLUDED."refill_tracking", "sys_period" = EXCLUDED."sys_period" WHERE form_list_rx_template.allow_sync = 'Yes';
COMMIT;
