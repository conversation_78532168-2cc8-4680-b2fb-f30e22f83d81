BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_outcome;
INSERT INTO form_list_outcome ("name","type","id","created_on","created_by","auto_name") VALUES ('Change Nurse','Nursing',1,'2024-03-19 22:39:32',1,'Change Nurse')
,('Change Shipping Provider','Shipping',2,'2024-03-19 22:39:32',1,'Change Shipping Provider')
,('Change Shipping Speed','Shipping',3,'2024-03-19 22:39:32',1,'Change Shipping Speed')
,('Discontinue an inappropriate therapy','Pharmacy',4,'2024-03-19 22:39:32',1,'Discontinue an inappropriate therapy')
,('Eliminate duplicate therapy','Pharmacy',5,'2024-03-19 22:39:32',1,'Eliminate duplicate therapy')
,('Improve adherence','Pharmacy',6,'2024-03-19 22:39:32',1,'Improve adherence')
,('Improve treatment efficacy/outcomes','Pharmacy',7,'2024-03-19 22:39:32',1,'Improve treatment efficacy/outcomes')
,('Patient received needed emergency care','Pharmacy',8,'2024-03-19 22:39:32',1,'Patient received needed emergency care')
,('Prevent disease progression','Pharmacy',9,'2024-03-19 22:39:32',1,'Prevent disease progression')
,('Prevent product wastage','Nursing',10,'2024-03-19 22:39:32',1,'Prevent product wastage')
,('Prevent treatment Delays','Shipping',11,'2024-03-19 22:39:32',1,'Prevent treatment Delays')
,('Prevent unplanned MD visit','Pharmacy',12,'2024-03-19 22:39:32',1,'Prevent unplanned MD visit')
,('Report to Shipping Provider','Shipping',13,'2024-03-19 22:39:32',1,'Report to Shipping Provider')
,('Speak to Nurse','Nursing',14,'2024-03-19 22:39:32',1,'Speak to Nurse')
,('Other','Nursing',15,'2024-03-19 22:39:32',1,'Other')
,('Other','Pharmacy',16,'2024-03-19 22:39:32',1,'Other')
,('Other','Shipping',17,'2024-03-19 22:39:32',1,'Other')
;
ALTER SEQUENCE "form_list_outcome_id_seq" RESTART WITH 18;
SET session_replication_role = DEFAULT;
 COMMIT;
