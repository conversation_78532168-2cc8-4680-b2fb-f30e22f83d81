BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_270_qualifier;
INSERT INTO form_list_270_qualifier ("code","name","id","created_on","created_by","auto_name") VALUES ('24','Employer''s Identification Number',1,'2024-03-19 22:39:32',1,'24 - Employer''s Identification Number')
,('34','Social Security Number',2,'2024-03-19 22:39:32',1,'34 - Social Security Number')
,('46','Electronic Transmitter Identification Number (ETIN)',3,'2024-03-19 22:39:32',1,'46 - Electronic Transmitter Identification Number (ETIN)')
,('FI','Federal Taxpayer''s Identification Number',4,'2024-03-19 22:39:32',1,'FI - Federal Taxpayer''s Identification Number')
,('II','Standard Unique Health Identifier (5010)',5,'2024-03-19 22:39:32',1,'II - Standard Unique Health Identifier (5010)')
,('MI','Member Identification Number',6,'2024-03-19 22:39:32',1,'MI - Member Identification Number')
,('NI','National Association of Insurance Commissioners (NAIC) Identification',7,'2024-03-19 22:39:32',1,'NI - National Association of Insurance Commissioners (NAIC) Identification')
,('PI','Payor Identification',8,'2024-03-19 22:39:32',1,'PI - Payor Identification')
,('PP','Pharmacy Processor Number',9,'2024-03-19 22:39:32',1,'PP - Pharmacy Processor Number')
,('SV','Service Provider Number',10,'2024-03-19 22:39:32',1,'SV - Service Provider Number')
,('XV','Health Care Financing Administration National PlanID',11,'2024-03-19 22:39:32',1,'XV - Health Care Financing Administration National PlanID')
,('XX','Health Care Financing Administration National',12,'2024-03-19 22:39:32',1,'XX - Health Care Financing Administration National')
,('ZZ','Mutually Defined',13,'2024-03-19 22:39:32',1,'ZZ - Mutually Defined')
;
ALTER SEQUENCE "form_list_270_qualifier_id_seq" RESTART WITH 14;
SET session_replication_role = DEFAULT;
 COMMIT;
