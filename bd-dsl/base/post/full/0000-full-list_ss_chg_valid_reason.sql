BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_chg_valid_reason;
INSERT INTO form_list_ss_chg_valid_reason ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:27:59',1,'Active Registration Status','GM','Active Registration Status'),
(2,'4/16/2024, 06:27:59',1,'In-Active License with prescriptive authority based on state/federal regulations','GN','In-Active License with prescriptive authority based on state/federal regulations'),
(3,'4/16/2024, 06:27:59',1,'Active with Prescriptive Authority – prescribed product class','GP','Active with Prescriptive Authority – prescribed product class'),
(4,'4/16/2024, 06:27:59',1,'Active with Prescriptive Authority – Prescriber Type','GQ','Active with Prescriptive Authority – Prescriber Type'),
(5,'4/16/2024, 06:27:59',1,'Active with Prescriptive Authority – Supervising Prescriber Type','GR','Active with Prescriptive Authority – Supervising Prescriber Type'),
(6,'4/16/2024, 06:27:59',1,'Registered','GS','Registered'),
(7,'4/16/2024, 06:27:59',1,'Enrolled/Re-Enrolled','GT','Enrolled/Re-Enrolled'),
(8,'4/16/2024, 06:27:59',1,'Assigned','GU','Assigned')
;
ALTER SEQUENCE "form_list_ss_chg_valid_reason_id_seq" RESTART WITH 9;
SET session_replication_role = DEFAULT;
COMMIT;