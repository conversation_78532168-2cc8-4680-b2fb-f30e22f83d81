BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_generic_terms;
INSERT INTO form_list_billing_generic_terms ("name","id","created_on","created_by","auto_name") VALUES ('Credit Card on File',1,'2024-03-19 22:39:32',1,'Credit Card on File')
,('Due net 10 days.',2,'2024-03-19 22:39:32',1,'Due net 10 days.')
,('Due net 15 days.',3,'2024-03-19 22:39:32',1,'Due net 15 days.')
,('Due net 30 days.',4,'2024-03-19 22:39:32',1,'Due net 30 days.')
,('Due net 45 days.',5,'2024-03-19 22:39:32',1,'Due net 45 days.')
,('Due net 60 days.',6,'2024-03-19 22:39:32',1,'Due net 60 days.')
,('Due net 7 days.',7,'2024-03-19 22:39:32',1,'Due net 7 days.')
,('Due net 90 days.',8,'2024-03-19 22:39:32',1,'Due net 90 days.')
,('First Notice',9,'2024-03-19 22:39:32',1,'First Notice')
,('HAPPY HOLIDAYS',10,'2024-03-19 22:39:32',1,'HAPPY HOLIDAYS')
,('NO PAYMENT REQUIRED AT THIS TIME',11,'2024-03-19 22:39:32',1,'NO PAYMENT REQUIRED AT THIS TIME')
,('PAST DUE',12,'2024-03-19 22:39:32',1,'PAST DUE')
,('Patient Paid in Full by Credit Card',13,'2024-03-19 22:39:32',1,'Patient Paid in Full by Credit Card')
,('Payable on receipt.',14,'2024-03-19 22:39:32',1,'Payable on receipt.')
,('Please give us a call with new Credit Card',15,'2024-03-19 22:39:32',1,'Please give us a call with new Credit Card')
;
ALTER SEQUENCE "form_list_billing_generic_terms_id_seq" RESTART WITH 16;
SET session_replication_role = DEFAULT;
 COMMIT;
