BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_submission_class_code;
INSERT INTO form_list_submission_class_code ("code","name","id","created_on","created_by","auto_name") VALUES ('1','No Override',1,'2024-03-19 22:39:32',1,'1 - No Override')
,('10','Meets Plan Limitations',2,'2024-03-19 22:39:32',1,'10 - Meets Plan Limitations')
,('11','Certification on File (D.0)',3,'2024-03-19 22:39:32',1,'11 - Certification on File (D.0)')
,('12','DME Replacement Indicator (D.0)',4,'2024-03-19 22:39:32',1,'12 - DME Replacement Indicator (D.0)')
,('13','Payer-Recognized Emergency/Disaster Assistance Request (D.0)',5,'2024-03-19 22:39:32',1,'13 - Payer-Recognized Emergency/Disaster Assistance Request (D.0)')
,('14','Long Term Care Leave of Absence (D.0)',6,'2024-03-19 22:39:32',1,'14 - Long Term Care Leave of Absence (D.0)')
,('15','Long Term Care Replacement Medication (D.0)',7,'2024-03-19 22:39:32',1,'15 - Long Term Care Replacement Medication (D.0)')
,('16','Long Term Care Emergency box (kit) or automated dispensing machine (D.0)',8,'2024-03-19 22:39:32',1,'16 - Long Term Care Emergency box (kit) or automated dispensing machine (D.0)')
,('17','Long Term Care Emergency supply remainder (D.0)',9,'2024-03-19 22:39:32',1,'17 - Long Term Care Emergency supply remainder (D.0)')
,('18','Longer Term Care Patient Admit/Readmint Indicator (D.0)',10,'2024-03-19 22:39:32',1,'18 - Longer Term Care Patient Admit/Readmint Indicator (D.0)')
,('19','Split Billing (D.0)',11,'2024-03-19 22:39:32',1,'19 - Split Billing (D.0)')
,('2','Other Override',12,'2024-03-19 22:39:32',1,'2 - Other Override')
,('20','340B Drug Classification',13,'2024-03-19 22:39:32',1,'20 - 340B Drug Classification')
,('21','LTC dispensing-14 days or less not applicable',14,'2024-03-19 22:39:32',1,'21 - LTC dispensing-14 days or less not applicable')
,('22','LTC dispensing-7 days',15,'2024-03-19 22:39:32',1,'22 - LTC dispensing-7 days')
,('23','LTC dispensing-4 days',16,'2024-03-19 22:39:32',1,'23 - LTC dispensing-4 days')
,('24','LTC dispensing-3 days',17,'2024-03-19 22:39:32',1,'24 - LTC dispensing-3 days')
,('25','LTC dispensing-2 days',18,'2024-03-19 22:39:32',1,'25 - LTC dispensing-2 days')
,('26','LTC dispensing-1 day',19,'2024-03-19 22:39:32',1,'26 - LTC dispensing-1 day')
,('27','LTC dispensing-4-3 days',20,'2024-03-19 22:39:32',1,'27 - LTC dispensing-4-3 days')
,('28','LTC dispensing-2-2-3 days',21,'2024-03-19 22:39:32',1,'28 - LTC dispensing-2-2-3 days')
,('29','LTC dispensing-daily and 3-day weekend',22,'2024-03-19 22:39:32',1,'29 - LTC dispensing-daily and 3-day weekend')
,('3','Vacation Supply',23,'2024-03-19 22:39:32',1,'3 - Vacation Supply')
,('30','LTC dispensing-Per shift dispensing',24,'2024-03-19 22:39:32',1,'30 - LTC dispensing-Per shift dispensing')
,('31','LTC dispensing-Per med pass dispensing',25,'2024-03-19 22:39:32',1,'31 - LTC dispensing-Per med pass dispensing')
,('32','LTC dispensing-PRN on demand',26,'2024-03-19 22:39:32',1,'32 - LTC dispensing-PRN on demand')
,('33','LTC dispensing-7 day or less cycle not otherwise represented',27,'2024-03-19 22:39:32',1,'33 - LTC dispensing-7 day or less cycle not otherwise represented')
,('34','LTC dispensing-14 days dispensing',28,'2024-03-19 22:39:32',1,'34 - LTC dispensing-14 days dispensing')
,('35','LTC dispensing-8-14 days dispensing method not listed above',29,'2024-03-19 22:39:32',1,'35 - LTC dispensing-8-14 days dispensing method not listed above')
,('36','LTC dispensing-8-14 days dispensing outside short cycle',30,'2024-03-19 22:39:32',1,'36 - LTC dispensing-8-14 days dispensing outside short cycle')
,('4','Lost Prescription',31,'2024-03-19 22:39:32',1,'4 - Lost Prescription')
,('42','Prescriber ID Submitted has been validated, is active',32,'2024-03-19 22:39:32',1,'42 - Prescriber ID Submitted has been validated, is active')
,('43','For ID submitted, associated DEA Renewed, or In Progress, DEA Authorized',33,'2024-03-19 22:39:32',1,'43 - For ID submitted, associated DEA Renewed, or In Progress, DEA Authorized')
,('44','For ID submitted, associated DEA recently licensed or re-activated',34,'2024-03-19 22:39:32',1,'44 - For ID submitted, associated DEA recently licensed or re-activated')
,('45','For ID submitted, associated DEA is a valid Hospital DEA with suffix',35,'2024-03-19 22:39:32',1,'45 - For ID submitted, associated DEA is a valid Hospital DEA with suffix')
,('46','For ID submitted, DEA has authorized prescriptive right',36,'2024-03-19 22:39:32',1,'46 - For ID submitted, DEA has authorized prescriptive right')
,('5','Therapy Change',37,'2024-03-19 22:39:32',1,'5 - Therapy Change')
,('6','Starter Dose',38,'2024-03-19 22:39:32',1,'6 - Starter Dose')
,('7','Medically Necessary',39,'2024-03-19 22:39:32',1,'7 - Medically Necessary')
,('8','Process Compound for Approved Ingredients',40,'2024-03-19 22:39:32',1,'8 - Process Compound for Approved Ingredients')
,('9','Encounter',41,'2024-03-19 22:39:32',1,'9 - Encounter')
,('99','Other',42,'2024-03-19 22:39:32',1,'99 - Other')
;
ALTER SEQUENCE "form_list_submission_class_code_id_seq" RESTART WITH 43;
SET session_replication_role = DEFAULT;
 COMMIT;
