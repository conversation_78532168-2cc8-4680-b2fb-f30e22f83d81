BEGIN;
SET session_replication_role = replica;
DELETE FROM form_list_ss_denial_reason;
INSERT INTO form_list_ss_denial_reason ("id","created_on","created_by","auto_name","code","name") VALUES (1,'4/16/2024, 06:28:39',1,'Patient unknown to the Provider','AA','Patient unknown to the Provider'),
(2,'4/16/2024, 06:28:39',1,'Patient never under Provider care','AB','Patient never under Provider care'),
(3,'4/16/2024, 06:28:39',1,'Patient no longer under Provider care','AC','Patient no longer under Provider care'),
(4,'4/16/2024, 06:28:39',1,'<PERSON><PERSON> has requested refill too soon','AD','<PERSON><PERSON> has requested refill too soon'),
(5,'4/16/2024, 06:28:39',1,'Medication never prescribed for the patient','AE','Medication never prescribed for the patient'),
(6,'4/16/2024, 06:28:39',1,'Patient should contact <PERSON>vider first','AF','<PERSON>ient should contact Provider first'),
(7,'4/16/2024, 06:28:39',1,'Fill/Refill not appropriate','AG','Fill/Refill not appropriate'),
(8,'4/16/2024, 06:28:39',1,'Patient needs appointment','AM','Patient needs appointment'),
(9,'4/16/2024, 06:28:39',1,'Request already responded to by other means (e.g. phone or fax)','AP','Request already responded to by other means (e.g. phone or fax)'),
(10,'4/16/2024, 06:28:39',1,'Medication denied at patient request','BE','Medication denied at patient request'),
(11,'4/16/2024, 06:28:39',1,'Patient had allergy to requested medication','CZ','Patient had allergy to requested medication'),
(12,'4/16/2024, 06:28:39',1,'Medication has been discontinued','DA','Medication has been discontinued')
;
ALTER SEQUENCE "form_list_ss_denial_reason_id_seq" RESTART WITH 13;
SET session_replication_role = DEFAULT;
COMMIT;