BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_billing_med_mod;
INSERT INTO form_list_billing_med_mod ("code","name","id","created_on","created_by","auto_name") VALUES ('BP','Beneficiary has elected to purchase the item',1,'2024-03-19 22:39:32',1,'BP - Beneficiary has elected to purchase the item')
,('BR','Beneficiary has elected to rent the item',2,'2024-03-19 22:39:32',1,'BR - Beneficiary has elected to rent the item')
,('BU','Beneficiary, after 30 days, not informed supplier of decision',3,'2024-03-19 22:39:32',1,'BU - Beneficiary, after 30 days, not informed supplier of decision')
,('KH','DMEPOS Item, initial claim, purchase or 1st month rental',4,'2024-03-19 22:39:32',1,'KH - DMEPOS Item, initial claim, purchase or 1st month rental')
,('KI','DMEPOS Item, second or third month rental',5,'2024-03-19 22:39:32',1,'KI - DMEPOS Item, second or third month rental')
,('KJ','DMEPOS Item, PEN pump, month 4-15',6,'2024-03-19 22:39:32',1,'KJ - DMEPOS Item, PEN pump, month 4-15')
,('MS','DME maintenance and service',7,'2024-03-19 22:39:32',1,'MS - DME maintenance and service')
,('NU','New DME',8,'2024-03-19 22:39:32',1,'NU - New DME')
,('RP','Replacement',9,'2024-03-19 22:39:32',1,'RP - Replacement')
,('RR','Rental of DME. Use this modifier when DME is to be rented',10,'2024-03-19 22:39:32',1,'RR - Rental of DME. Use this modifier when DME is to be rented')
,('XA','IV pole is used in conjunction with PEN.',11,'2024-03-19 22:39:32',1,'XA - IV pole is used in conjunction with PEN.')
,('ZU','Advanced notice of possible medical necessity denial on file',12,'2024-03-19 22:39:32',1,'ZU - Advanced notice of possible medical necessity denial on file')
;
ALTER SEQUENCE "form_list_billing_med_mod_id_seq" RESTART WITH 13;
SET session_replication_role = DEFAULT;
 COMMIT;
