BEGIN;
 SET session_replication_role = replica;
 DELETE FROM form_list_provider_type;
INSERT INTO form_list_provider_type ("code","name","id","created_on","created_by","auto_name") VALUES ('AD','Admitting',1,'2024-03-19 22:39:32',1,'AD - Admitting')
,('AT','Attending',2,'2024-03-19 22:39:32',1,'AT - Attending')
,('BI','Billing',3,'2024-03-19 22:39:32',1,'BI - Billing')
,('CO','Consulting',4,'2024-03-19 22:39:32',1,'CO - Consulting')
,('CV','Covering',5,'2024-03-19 22:39:32',1,'CV - Covering')
,('H','Hospital',6,'2024-03-19 22:39:32',1,'H - Hospital')
,('HH','Home Health Care',7,'2024-03-19 22:39:32',1,'HH - Home Health Care')
,('LA','Laboratory',8,'2024-03-19 22:39:32',1,'LA - Laboratory')
,('OT','Other Physician',9,'2024-03-19 22:39:32',1,'OT - Other Physician')
,('P1','Pharmacist',10,'2024-03-19 22:39:32',1,'P1 - Pharmacist')
,('P2','Pharmacy',11,'2024-03-19 22:39:32',1,'P2 - Pharmacy')
,('PC','Primary Care Physician',12,'2024-03-19 22:39:32',1,'PC - Primary Care Physician')
,('PE','Performing',13,'2024-03-19 22:39:32',1,'PE - Performing')
,('R','Rural Health Clinic',14,'2024-03-19 22:39:32',1,'R - Rural Health Clinic')
,('RF','Referring',15,'2024-03-19 22:39:32',1,'RF - Referring')
,('SB','Submitting',16,'2024-03-19 22:39:32',1,'SB - Submitting')
,('SK','Skilled Nursing Facility',17,'2024-03-19 22:39:32',1,'SK - Skilled Nursing Facility')
,('SU','Supervising',18,'2024-03-19 22:39:32',1,'SU - Supervising')
;
ALTER SEQUENCE "form_list_provider_type_id_seq" RESTART WITH 19;
SET session_replication_role = DEFAULT;
 COMMIT;
