{"license": "UNLICENSED", "name": "dsl", "repository": {"url": "**************:envoylabs/bd-dsl.git", "type": "git"}, "private": true, "version": "1.0.0", "devDependencies": {"checksum": "^0.1.1", "coffeescript": "^2.7.0", "cson": "~3", "csv-parser": "^3.0.0", "delete": "~0", "dropbox": "^2.5.13", "eslint": "^6.0.0", "eslint-plugin-coffee": "^0.1.13", "eval2": "~0", "glob": "^7.1.6", "grunt": "^1.6.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-coffee": "^1.0.0", "load-grunt-config": "^0.19.2", "mime-types": "^2.1.35", "moment": "^2.30.1", "node-etcd": "~5", "node-persist": "0.0.11", "node-rsa": "^1.0.8", "node-stream-zip": "^1.15.0", "pg-escape": "^0.2.0", "request": "^2.88.2", "sync-request": "^6.1.0"}, "description": "Clara DSL Manager", "dependencies": {"@faker-js/faker": "^8.4.1", "axios": "^1.6.8", "better-sqlite3": "^9.4.3", "cheerio": "^1.0.0-rc.12", "lodash": "^4.17.21", "moment-random": "^1.1.0", "pg": "^8.11.3", "pg-promise": "^11.6.0", "puppeteer": "^22.6.1", "split-file": "^2.3.0", "sqlite-xsv": "^0.2.1-alpha.11", "unzip": "^0.1.11"}}