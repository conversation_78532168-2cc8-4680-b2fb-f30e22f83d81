fields:

	cust_comment:
		view:
			columns: 3
			control: 'area'
			label: 'Comments to the clinical team'

	review_by_pharmacist:
		model:
			source: ['No', 'Yes']
			access:
				write: ['-patient']
		view:
			columns: 3
			findfilter: 'Yes'
			control: 'radio'
			label: 'Reviewed By Pharmacist?'

	cust_type:
		model:
			multi: false
			required: true
			source: ['Ongoing', 'Resolved']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Bleed Type'

	cust_site:
		model:
			multi: false
			source: ['Bleed Joint', 'Muscle', 'Soft Tissue Bleed']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Site of Bleed'

	cust_total_iu:
		view:
			columns: 3
			label: 'Total number of IU’s administered'

	cust_lot_num:
		view:
			columns: 3
			label: 'Lot Number'

	cust_add_steps:
		model:
			multi: true
			source: ['Pain Medication', 'RICE', 'ED Visit', 'Hospital Stay', 'Other']
			if:
				'Other':
					fields: ['cust_add_steps_details']
		view:
			columns: 3
			control: 'checkbox'
			label: 'Additional Steps Required'

	cust_add_steps_details:
		view:
			columns: 3
			label: 'Details'

	duration_display:
		view:
			label: 'Duration'
			offscreen: true
			readonly: true

	cause_display:
		view:
			label: 'Cause'
			offscreen: true

	site_display:
		view:
			label: 'Site'
			offscreen: true

	duration_bleed:
		model:
			source: ['0','1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14+']
		view:
			columns: 3
			control: 'radio'
			label: 'Duration of bleed (days)'

	duration_type:
		model:
			source: ['Hours', 'Days']
		view:
			control: 'radio'
			label: 'Duration Type'
			offscreen: true
			readonly: true

	cust_track_bleed_prophylaxis:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_track_bleed_demand']
		view:
			columns: 3
			control: 'radio'
			label: 'Did the bleed resolve with Prophylaxis dosing only?'
	
	cust_track_bleed_demand:
		model:
			source: ['0', '1' ,'2', '3', '4', '5', '6', '7', '8', '9' ,'10']
		view:
			columns: 3
			control: 'radio'
			label: 'How many on-demand doses were used?'

	site_location:
		model:
			required: true
		view:
			columns: 3
			label: "Location Details"

	site_other:
		model:
			required: true
		view:
			columns: 3
			label: "Other Site"

	severity:
		model:
			required: true
		view:
			columns: 3

	target_joint:
		model:
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Is this bleed in a target joint?'

model:
	access:
		create:     ['patient']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		delete:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
		request:    []
		update:     ['patient']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'patient']
	bundle: ['patient']
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'Bleed Event Details':
					fields: ['date', 'cause', 'cause_other', 'severity', 'target_joint', 'cust_type', 'site',
					'site_location', 'site_other', 'cust_site', 'duration_type','duration_bleed',
					'doses_used', 'cust_total_iu', 'cust_lot_num', 'cust_add_steps', 'cust_add_steps_details',
					'cust_track_bleed_prophylaxis', 'cust_track_bleed_demand', 'cust_comment', 'review_by_pharmacist']

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Bleed Event"
	]
view:
	comment: 'Patient > Intake > Assessment > Factor > Bleed'
	find:
		basic: ['review_by_pharmacist', 'severity', 'site', 'date']
	grid:
		fields: ['date', 'cause_display', 'severity', 'site_display', 'duration_display', 'doses_used', 'cust_comment']
		sort: ['-created_on']
		style:
			cust_comment:
				style:
					width:'20%'
	label: 'Assessment Questionnaire: Factor Bleed Event'
