fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	therapy_id:
		model:
			max: 64
			multi: true
			source: 'list_therapy'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Therapy'

	brand_name_id:
		model:
			source: 'list_fdb_drug_brand'
			sourceid: 'code'
		view:
			columns: 3
			label: 'Drug Brand'

	source_id:
		model:
			prefill: ['patient.referrer_id']
			source: 'physician'
			type: 'int'
		view:
			label: 'Referring Physician'
			columns: 3

	denial_date:
		model:
			type: 'date'
		view:
			label: 'Denial Date'
			columns: 3

	insurance:
		view:
			label: 'Insurance:'
			columns: 3

	denial_reason:
		model:
			source: ['Benefit Coverage', 'Carve Out', 'Deceased', 'HB Canceled',
			'Limited Distribution', 'Not Contracted', 'Provider Cancelled',
			'Patient Cancelled', 'Payer Error', 'Other']
			if:
				'HB Canceled':
					fields: ['hb_reason']
		view:
			control: 'radio'
			label: 'Denial Reason'
			columns: 3

	hb_reason:
		model:
			source: ['Reimbursement', 'Therapy not provided']
			required: true
		view:
			label: 'Reason HB Canceled'
			columns: 3

	denial_reason_notes:
		view:
			label: 'Denial Reason Comments'
			columns: 3

	poa:
		view:
			label: 'Plan of Action:'
			columns: 3


model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Denied Referral':
			hide_header: true
			indent: false
			fields: ['therapy_id', 'brand_name_id', 'source_id', 'denial_date', 'insurance',
			'denial_reason', 'hb_reason', 'denial_reason_notes', 'poa']

view:
	comment: 'Patient > Billing Assessment Audit'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Denied Referral'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']