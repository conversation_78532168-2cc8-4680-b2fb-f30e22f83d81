fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	qol_survey:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['qol_date']
		view:
			columns: 2
			control: 'radio'
			label: 'QOL Survey sent'

	qol_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'When QOL survey sent?'
			template: '{{now}}'

	rv_survey:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['rvs_date']
		view:
			columns: 2
			control: 'radio'
			label: 'Rare Voice Survey sent'

	rvs_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'When Rare Voice survey sent?'
			template: '{{now}}'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm', 'csr']
		delete:     ['admin']
		read:       ['admin', 'nurse', 'pharm', 'patient', 'csr']
		read_all:   ['admin', 'nurse', 'pharm', 'patient', 'csr']
		request:    []
		update:     ['admin', 'nurse', 'pharm', 'csr']
		review:     []
		update_all: ['admin', 'nurse', 'pharm', 'csr']
		write:      ['admin', 'nurse', 'pharm', 'patient', 'csr']
	bundle: ['patient']
	name: ['patient_id', 'qol_date']
	sections:
		'PX Survey':
			fields: ['qol_survey', 'qol_date', 'rv_survey', 'rvs_date' ]

view:
	comment: 'Patient > Patient Experience Survey'
	grid:
		fields: ['created_on', 'created_by', 'qol_survey', 'rv_survey']
		sort: ['-created_on']
	label: 'Patient Experience Survey'
