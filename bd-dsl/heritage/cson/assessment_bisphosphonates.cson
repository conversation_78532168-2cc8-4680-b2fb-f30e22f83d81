fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	pregnancy_risk:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Pregnancy Risk'

	recent_crcl_value:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Recent Crcl Value'

	recent_crcl_date:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Recent Crcl Date'

	recent_scr_value:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Recent Scr Value'

	recent_scr_date:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Recent Scr Date'

	hydration_status:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Hydration Status'

	taking_calcium:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Taking Calcium'

	seen_dentist:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Seen Dentist'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']

view:
	comment: 'Patient > Careplan > Assessment > Bisphosphonates'
	label: 'Assessment: Bisphosphonates'
	open: 'read'
