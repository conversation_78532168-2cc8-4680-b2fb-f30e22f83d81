fields:

	therapy_outcome_questionare_feeling:
		model:
			source: ['Poor', 'Fair','Good','Very Good','Excellent']
		view:
			control: 'radio'
			label: 'How are you feeling today?'

	sym_change:
		model:
			max: 128
			required: true
			source:
				no: 'No'
				yes_decline: 'Yes, Condition continues to decline since last treatment'
				yes_small_improve: 'Yes, slight-gradual improvement'
				yes_sig_improve: 'Yes, significant improvement'
		view:
			control: 'select'
			label: 'Has there been any changes in your symptoms?'

	cust_recent_infections:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did the patient report any recent serious infections?'

	cust_hearing_changes:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_hearing_changes_details']
		view:
			control: 'radio'
			label: 'Have you noticed any changes in hearing?'

	cust_hearing_changes_details:
		view:
			label: 'Hearing change details'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		clinical_outcomes:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'careplan_id']
	sections:
		'Clinical Therapy Outcomes':
			hide_header: true
			indent: false
			fields: ['therapy_outcome_questionare_feeling', 'followup_time', 'followup_time_other', 'feeling', 'days_missed',
			'days_missed_reason', 'days_missed_count', 'unplanned_dr_visit',
			'unplanned_dr_visit_reason', 'unplanned_dr_visit_reason_event',
			'unplanned_dr_visit_reason_event_other', 'unplanned_dr_visit_date',
			'unplanned_dr_visit_outcome', 'unplanned_dr_visit_details',
			'unplanned_er_visit', 'unplanned_er_visit_date', 'unplanned_er_visit_reason',
			'unplanned_er_visit_reason_event', 'unplanned_er_visit_reason_event_other',
			'unplanned_er_visit_outcome', 'unplanned_er_visit_details', 'hospitalized',
			'hospitalized_date', 'hospitalized_where', 'hospitalized_phone', 'hospitalized_discharge', 'hospitalized_services', 'hospitalized_services_note', 'hospitalized_reason', 'hospitalized_reason_event',
			'hospitalized_reason_event_other', 'hospitalized_outcome', 'provider_visit',
			'provider_visit_date', 'sym_change', 'cust_recent_infections', 'cust_hearing_changes',
			'cust_hearing_changes_details']

view:
	comment: 'Patient > Careplan > Clinical > Clinical Outcomes'
	label: 'Clinical Outcomes'
