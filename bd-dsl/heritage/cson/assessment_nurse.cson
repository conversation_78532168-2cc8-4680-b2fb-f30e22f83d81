fields:

	agency_id:
		model:
			required: true
			source: 'nurse_agency'
		view:
			label: 'Nursing Agency'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	symptoms_filter:
		model:
			multi: true
			source: 'list_symptom'
			if:
				'*':
					fields: ['symptoms', 'symptom_review_date']
					sections: ['Active Symptoms Review', 'Symptoms Review']
		view:
			label: 'Symptoms'
			readonly: true
			offscreen: true

	route:
		model:
			required: true
			source: ['SQ', 'IV', 'Injection']
			if:
				'Injection':
					fields: ['naive_injection']
				'*':
					fields: ['naive']
		view:
			columns: 3
			control: 'radio'
			label: 'Route'

	naive:
		model:
			required: true
			source: ['Therapy-naive', 'Continuing']
		view:
			columns: 3
			control: 'radio'
			label: 'Are you therapy-naive or continuing therapy?'

	naive_injection:
		model:
			required: true
			source: ['Therapy-naive', 'Continuing']
			if:
				'Therapy-naive':
					fields: ['had_dose_dr_office', 'need_virtual_training']
				'Continuing':
					fields: ['pt_competent']
		view:
			columns: 3
			control: 'radio'
			label: 'Are you therapy-naive or continuing therapy?'

	had_dose_dr_office:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Has patient received a dose in physician’s office?'

	need_virtual_training:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Virtual Injection Teaching Needed?'

	pt_competent:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Is Patient/Caregiver competent to administer?'

	route_comment:
		view:
			control: 'area'
			label: 'Comments'

	cust_educate_glucometer:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Overview of how to use the glucometer (the patient will have to self-perform)?'

	cust_educate_procedures:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			control: 'radio'
			label: 'Overview of nursing procedures related to therapy (RN administration or self-administration)?'

	cust_educate_tollfree:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 3
			control: 'radio'
			label: 'Heritage Biologics Toll-Free contact #?'
	
	cust_line_maint:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 3
			control: 'radio'
			label: 'Line Maintenance?'

	cust_pump_education:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 3
			control: 'radio'
			label: 'Pump Education?'

	cust_schedule_visit:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 3
			control: 'radio'
			label: 'Scheduled Nursing Visit?'

	cust_nursing_agency:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 3
			control: 'radio'
			label: 'Nursing Agency Contact Info / Heritage Biologics On Call?'

	cust_patient_participates:
		model:
			max: 3
			source: ['No', 'Yes', 'N/A']
		view:
			control: 'radio'
			label: 'Patient agrees to Participate in Therapy / Appropriate for home Care?'

	billing_assmt:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Patient Admissions Contact']
					fields: ['subform_admissions']
		view:
			control: 'radio'
			label: 'Will the billing assessment be completed?'

	subform_admissions:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_admissions'
		view:
			label: 'Patient Admissions Contact'

	cust_safe_env:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have a safe home environment (running water, safe electrical outlets, pets in the home)?'

	cust_safe_home:
		model:
			source: ['No', 'Yes']
		view:
			label: 'Do you feel safe in your home?'
			control: 'radio'
	
	cust_runing_water:
		model:
			source: ['No', 'Yes']
		view:
			label: 'Do you have running water, safe electrical outlets and a refrigerator?'
			control: 'radio'

	cust_workspace:
		model:
			source: ['No', 'Yes']
		view:
			columns: 3
			label: 'Do you have a clean workspace for medication preparation?'
			control: 'radio'

	cust_workspace_details:
		view:
			control: 'area'
			label: 'Please indicate if patient has a preference for where RN should set up in the home'

	cust_clean_space:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'room temperature or refrigerated, if required'
			label: 'Do you have a clean space to store your medication?'

	cust_emergency_access:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you have access to Emergency Medical Services?'

	cust_assistive_device:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_assistive_device_details']
		view:
			columns: 3
			control: 'radio'
			label: 'Do you have any functional limitations?'

	cust_assistive_device_details:
		view:
			columns: 3
			label: 'Details'

	cust_advanced_direction:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Advanced Directive?'

	cust_advanced_directives_education:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 3
			control: 'radio'
			label: 'Was patient educated on Advanced Directives?'

	cust_code_status:
		model:
			max: 3
			source: ['FULL', 'DNAR', 'Limited Attempt at Resuscitation']
		view:
			control: 'radio'
			label: 'Code Status?'

	# Nursing

	hobbies_exist:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hobbies_box']
		view:
			columns: 2
			label: 'Do you have hobbies you enjoy?'
			control: 'radio'

	hobbies_box:
		view:
			columns: 2
			control: 'area'
			label: 'List your Hobbies'

	cust_pets:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['specify_types']
		view:
			columns: -2
			label: 'Do you have pets in the home?'
			control: 'radio'

	specify_types:
		view:
			columns: 2
			control: 'area'
			label: 'Specify type of animals, pets name'

	cust_caregiver:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_caregiver_detail']
		view:
			columns: -2
			label: 'Does the patient have a caregiver/someone who helps you?'
			control: 'radio'

	cust_caregiver_detail:
		model:
			required: true
		view:
			columns: 2
			label: 'Caregiver details'
			control: 'area'

	parking_pref:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['parking_details']
		view:
			columns: -2
			label: 'Do you have a parking preference?'
			control: 'radio'

	parking_details:
		view:
			columns: 2
			control: 'area'
			label: 'Specify, parking details'

	other_details:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['explain_details']
		view:
			columns: -2
			label: 'Any other specific requests the nurse needs to know before coming into your home? i.e: shoes on or off, etc?'
			control: 'radio'

	explain_details:
		view:
			columns: 2
			control: 'area'
			label: 'Specify your requests'

	notes:
		view:
			control: 'area'
			label: 'Notes'

	symptom_review_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 3
			label: 'Symptoms Review Date'
			template: '{{now}}'

	ms_relapse_symptoms_alert:
		model:
			multi: true
			source: ['When you are having a relapse, what symptoms do you experience?']
		view:
			class: 'list'
			label: 'MS Relapse Symptoms'
			control: 'checkbox'
			readonly: true

	ms_relapse_comments:
		view:
			label: 'Comments'
			control: 'area'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections_group: [
		'Nursing Agency':
			indent: false
			tab: 'H&P'
			fields: ['agency_id','therapy_1', 'therapy_2', 'therapy_3', 'therapy_4',
			'therapy_5', 'disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5', 'symptoms_filter']

		'Medications':
			indent: false
			tab: 'H&P'
			fields: ['patient_medications']

		'Allergies':
			indent: false
			tab: 'H&P'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
				hide_header: true
				indent: false
				tab: 'H&P'
				fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'H&P'
			fields: ['patient_interactions']

		'Measurement Log':
			indent: false
			tab: 'H&P'
			fields: ['measurement_log']

		'Catheter Requirements':
			hide_header: true
			indent: false
			tab: 'Catheter'
			note: 'Please review Patient Catheter Info before adding new Catheter'
			fields: ['catheter_log']

		'Route':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['route', 'naive', 'naive_injection','had_dose_dr_office', 'need_virtual_training','pt_competent','route_comment']
		'Patient Education':
			indent: false
			tab: 'Assessment'
			fields: ['cust_educate_glucometer', 'cust_educate_procedures', 'cust_educate_tollfree',
			'cust_line_maint', 'cust_pump_education',
			'cust_schedule_visit', 'cust_nursing_agency', 'cust_patient_participates', 'billing_assmt']

		'Patient Admissions Contact':
			hide_header: true
			indent: false
			tab: 'Admissions'
			fields: ['subform_admissions']

		'Symptoms Review':
			hide_header: true
			indent: false
			tab: 'Symptoms Review'
			fields: ['symptom_review_date']
		'Active Symptoms Review - MS':
			indent: false
			tab: 'Symptoms Review'
			fields: ['ms_relapse_symptoms_alert', 'ms_relapse_comments']
		'Active Symptoms Review':
			indent: false
			tab: 'Symptoms Review'
			fields: ['symptoms']

		'Additional Information':
			indent: false
			tab: 'Addtl Information'
			fields: ['cust_safe_home' ,'cust_runing_water','cust_workspace',
			'cust_workspace_details','cust_clean_space', 'cust_emergency_access',  'cust_assistive_device', 'cust_assistive_device_details',
			'cust_advanced_direction', 'cust_advanced_directives_education', 'cust_code_status']
		'Information for the Nurse':
			note: 'Information for the Nurse'
			hide_header: true
			indent: false
			tab: 'Nursing'
			fields: ['hobbies_exist','hobbies_box','cust_pets','specify_types','cust_caregiver',
			'cust_caregiver_detail','parking_pref','parking_details','other_details',
			'explain_details','notes']
	]
