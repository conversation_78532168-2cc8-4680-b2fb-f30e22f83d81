fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	purpose:
		view:
			control: 'area'
			label: 'Purpose of Call'
			columns: 2

	narrative:
		view:
			control: 'area'
			label: 'Narrative'
			columns: 2

	outcome:
		view:
			control: 'area'
			label: 'Outcome'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Patient Call':
			hide_header: true
			indent: false
			fields: ['purpose', 'narrative', 'outcome']

view:
	comment: 'Patient > Billing Assessment Patient Call'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Patient Call'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']