fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	completed:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					require_fields: ['intervention']
			required: true
		view:
			columns: 2
			label: 'Have you completed a DUR on this patient?'
			control: 'radio'

	intervention:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['intervention_type', 'intervention_details']
				'No':
					fields: ['no_intervention_script']
		view:
			columns: 2
			label: 'Intervention needed?'
			control: 'radio'

	no_intervention_script:
		model:
			multi: true
			source: ["Drug utilization review complete.  No significant drug-disease or drug-drug interactions found."]
		view:
			control: 'checkbox'
			label: 'Reminder'
			class: 'list'
			readonly: true

	intervention_type:
		model:
			multi: true
			required: true
			source: ['Drug-drug interaction','Duplicate therapy', 'Drug-food / drug-alcohol interaction', 'Contraindication', 'Precaution', 'Drug-disease interaction', 'Drug-allergy interaction', 'Prior adverse reactions']
		view:
			control: 'checkbox'
			label: 'Intervention Types'

	intervention_details:
		view:
			control: 'area'
			label: 'Intervention Details'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'pharm']
		read_all:   ['admin', 'pharm']
		request:    []
		update:     []
		update_all:  ['admin', 'pharm']
		write:       ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'

	sections: 
		'DUR Assessment':
			fields: [
						'completed', 'intervention', 'no_intervention_script',
						'intervention_type', 'intervention_details'
					]
		
	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "DUR Assessment"
	]

view:
	comment: 'Patient > Careplan > Assessment DUR'
	grid:
		fields: ['created_on', 'created_by', 'intervention', 'intervention_type']
		sort: ['-id']
	label: 'DUR Assessment'
	open: 'read'
