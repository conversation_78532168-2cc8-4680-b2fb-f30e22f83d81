fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	medication:
		view:
			label: 'Medication'
			columns: 2

	dx_id:
		model:
			source: 'patient_diagnosis'
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				active:
					'static': 'Yes'
		view:
			control: 'select'
			label: 'Patient Diagnosis'
			columns: 2

	pump:
		view:
			label: 'External Infusion Pump LCD L33794'
			columns: 2

	ig:
		view:
			label: 'Intravenous Immune Globulin LCD L33610'
			columns: 2

	payment:
		view:
			label: 'Transitional Payment for SQIG Professional Services (nursing)'
			columns: 2

	qualifications:
		view:
			control: 'area'
			label: 'Qualifications'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Medicare B DME Qualification':
			hide_header: true
			indent: false
			fields: ['medication', 'dx_id', 'pump', 'ig', 'payment', 'qualifications']

view:
	comment: 'Patient > Billing Assessment Medicare B DME Qualification'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Medicare B DME Qualification'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']