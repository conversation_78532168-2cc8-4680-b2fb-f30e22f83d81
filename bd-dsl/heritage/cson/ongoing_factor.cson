fields:

	cust_htc_care:
		model:
			max: 3
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Does the patient obtain care from an HTC?'

	cust_htc_details:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
		view:
			columns: 2
			control: 'area'
			label: 'HTC comment:'

	cust_referrer_name:
		model:
			source: ['Children’s Hospital New Orleans – Louisiana HTC', 'Cure 4 the kids', 'Iowa City HTC', 'Oklahoma HTC', 'Kansas City HTC', 'St. Louis HTC', 
			'Arkansas HTC', 'Nebraska HTC', 'Sugar House Hematology']
			prefill: ['ongoing_factor', 'assessment_factor']
		view:
			columns: -2
			control: 'radio'
			label: 'Referrer Location'
			validate: [
				name: 'AddHTCAddress'
			]

	cust_referrer_address:
		model:
			if:
				"1200 Children's Ave. Suite 10000":
					note: 'Patient is an OK HTC Patient'
		view:
			columns: 2
			label: 'Referrer Location Address'
			readonly: true

	cust_diagnosis:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ['Hemophilia A (Factor VIII Deficiency)', 'Hemophilia B (Factor IX Deficiency)',
			'Hemophilia C (Factor XI Deficiency, Rosenthal syndrome)', 'Von Willebrand Disease',
			'Factor XIII Deficiency', 'Other']
			if:
				'Hemophilia A (Factor VIII Deficiency)':
					fields: ['cust_diagnosis_a']
				'Hemophilia B (Factor IX Deficiency)':
					fields: ['cust_diagnosis_b']
				'Von Willebrand Disease':
					fields: ['cust_diagnosis_vwd']
				'Other':
					fields: ['cust_diagnosis_other']
		view:
			columns: -2
			control: 'radio'
			label: 'Diagnosis'

	cust_diagnosis_a:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ['Mild (baseline FVIII >5%)', 'Moderate (baseline FVIII between 1-5%)',
			'Severe (baseline FVIII <1%)']
		view:
			columns: 2
			control: 'radio'
			label: 'Hemophilia A (Factor VIII Deficiency)'

	cust_diagnosis_b:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ['Mild (baseline IX >5%)', 'Moderate (baseline IX between 1-5%)',
			'Severe (baseline IX <1%)']
		view:
			columns: 2
			control: 'radio'
			label: 'Hemophilia B (Factor IX Deficiency)'

	cust_diagnosis_vwd:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			source: ['Type 1', 'Type 2A', 'Type 2B', 'Type 2M', 'Type 2N', 'Type 3']
		view:
			columns: 2
			control: 'radio'
			label: 'Von Willebrand Disease'

	cust_diagnosis_other:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
		view:
			columns: 2
			control: 'area'
			label: 'Other Diagnosis Details'

	cust_track_bleeds_how:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			required: false
		view:
			control: 'area'
			label: 'If so, what method do you use and how often are you tracking them?'

	cust_track_bleeds_why:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			required: false
		view:
			control: 'area'
			label: 'If not, what is the reason?'
			
	cust_therapy_regimen:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			multi: false
			source: ['Prophylaxis for Factor Replacement', 'On-Demand', 'Prophylaxis and On-Demand', 'Immune Tolerance Induction', 'Surgery', 'Prophylaxis for Non-Factor Replacement (i.e. Hemlibra)']
		view:
			control: 'checkbox'
			label: 'Therapy Regimen'

	cust_preferred_package:
		model:
			prefill: ['ongoing_factor', 'assessment_factor']
			multi: true
			source: ['Separate Doses', 'Separate Supply Kits', 'Bulk Supply Bags', 'Dose and Supplies together']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Preferred Packaging'

	cust_pain_scale:
		model:
			required: false
			type: 'int'
			max: 10
			min: 0
		view:
			label: 'What is your pain on a scale 0-10'
			offscreen: true
			readonly: true

	cust_pain_scale_new:
		model:
			required: true
			source: ['N/A','0','1','2','3','4','5','6','7','8','9','10']
			if:
				'N/A':
					fields: ['cust_pain_scale_why']
		view:
			columns: -2
			control: 'radio'
			label: 'What is your pain on a scale 0-10'

	cust_pain_scale_why:
		view:
			columns: 2
			control: 'area'
			label: 'Details'

	cust_rom:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_rom_change']
		view:
			columns: -2
			control: 'radio'
			label: 'Have you noticed a change in your range of motion?'

	cust_rom_change:
		model:
			max: 3
			required: true
			source: ['Improved', 'Declined']
		view:
			columns: 2
			control: 'radio'
			label: 'Range of motion change:'

	cust_educate_tracking:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: -2
			control: 'radio'
			label: 'Patient educated about importance of tracking bleeds?'
	
	cust_educate_tracking_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Bleed Tracking Education Comment'

	cust_educate_reporting:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: -2
			control: 'radio'
			label: 'Does the patient report their monthly bleeds to the pharmacy?'
	
	cust_educate_reporting_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Bleed Reporting Education Comment'

	cust_track_bleeds:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			note: '(bleed log, notebook, phone app, etc.)'
			label: 'Does the patient track their bleeds?'
	
	cust_review_bleeds:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Did you review the bleed log for any patient reported bleeds?'

	cust_track_bleeds_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Patient Bleed Tracking Comment'

	# Base assessment overrides

	rom:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'ROM'

	rom_change:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'ROM Change'
	
	pain_scale:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Pain Scale'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		ongoing_factor:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		assessment_factor:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Factor Pre-Assessment':
			area: 'preassessment'
			fields: ['cust_htc_care', 'cust_htc_details', 'cust_referrer_name', 'cust_referrer_address',
			'cust_diagnosis', 'cust_diagnosis_a', 'cust_diagnosis_b', 'cust_diagnosis_vwd',
			'cust_diagnosis_other', 'cust_therapy_regimen', 'cust_preferred_package']
		'Factor Patient Assessment':
			prefill: 'ongoing_factor'
			fields: ['target_joints', 'target_joints_other', 'cust_pain_scale_new', 'cust_pain_scale_why', 'bleed_history', 'cust_rom', 'cust_rom_change']
		'Hemophilia Bleed Reporting':
			area: 'footer'
			fields: ['cust_educate_tracking', 'cust_educate_tracking_comment',
			'cust_educate_reporting', 'cust_educate_reporting_comment', 'cust_track_bleeds', 'cust_track_bleeds_how', 'cust_track_bleeds_why', 'cust_track_bleeds_comment', 'cust_review_bleeds']
