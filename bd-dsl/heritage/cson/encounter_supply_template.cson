fields:

	supplies:
		model:
			subfields:
				supplies:
					label: 'Supply'
					type: 'text'
					style:
						width: '50%'
				needed:
					label: 'Quantity Needed'
					type: 'text'
					style:
						width: '50%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Supplies'

	prn_meds:
		model:
			subfields:
				medication:
					label: 'Medication'
					type: 'text'
					style:
						width: '30%'
				inhome:
					label: 'Quantity In Home'
					type: 'text'
					style:
						width: '25%'
				needed:
					label: 'Quantity Needed'
					type: 'text'
					style:
						width: '25%'
				expiration:
					label: 'Expiration Date'
					type: 'date'
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'PRN Medications'

	emergency_meds:
		model:
			subfields:
				medication:
					label: 'Medication'
					type: 'text'
					style:
						width: '30%'
				inhome:
					label: 'Quantity In Home'
					type: 'text'
					style:
						width: '25%'
				needed:
					label: 'Quantity Needed'
					type: 'text'
					style:
						width: '25%'
				expiration:
					label: 'Expiration Date'
					type: 'date'
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Emergency Medications'

	active:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			control: 'radio'
			label: 'Active'
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'csr', 'pharm']
	bundle: ['manage']
	name: 'Supply Template'
	sections:
		'Patient Supply Template':
			fields: ['supplies', 'prn_meds', 'emergency_meds', 'active']
view:
	comment: 'Manage > Patient Supply Template'
	find:
		basic: ['active']
	grid:
		fields: ['created_on','created_by','updated_on','updated_by','active']
		sort: ['-created_on']
	label: 'Patient Supply Template'
