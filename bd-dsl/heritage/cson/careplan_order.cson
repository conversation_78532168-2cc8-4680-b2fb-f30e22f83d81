fields:

	validated:
		model:
			source: ['Yes']
			if:
				'Yes':
					fields: ['validated_by', 'validation_datetime']
					require_fields: ['written_date', 'dx_ids', 'written_date',
					'expiration_date', 'day_supply','start_date', 'nursing_status',
					'nursing_agency_id', 'nurse_billable_id', 'ep_priority_level']

	status_id:
		view:
			class: 'check-field'

	site_id:
		view:
			class: 'check-field'

	therapy_id:
		view:
			class: 'check-field'

	refill_order_id:
		view:
			class: 'check-field'

	nogo_date:
		view:
			class: 'check-field'

	discontinued_date:
		view:
			class: 'check-field'

	prescriber_id:
		view:
			class: 'check-field'

	corrections_needed:
		view:
			class: 'check-field'

	intake_substatus_id:
		view:
			class: 'check-field'

	date_received:
		view:
			class: 'check-field'

	validation_datetime:
		view:
			class: 'check-field'

	# Required only when validated
	ep_priority_level:
		model:
			required: false
		view:
			class: 'check-field'

	dx_ids:
		model:
			required: false
		view:
			class: 'check-field'

	written_date:
		model:
			required: false
		view:
			class: 'check-field'

	expiration_date:
		model:
			required: false
		view:
			class: 'check-field'

	day_supply:
		model:
			required: false
		view:
			class: 'check-field'

	start_date:
		model:
			required: false
		view:
			class: 'check-field'

	nursing_status:
		model:
			required: false
		view:
			class: 'check-field'

	nursing_agency_id:
		model:
			required: false
			if:
				'*':
					fields: ['nurse_billable_id']
					require_fields: ['payer_ids']
		view:
			class: 'check-field'

	nurse_billable_id:
		model:
			required: false
		view:
			class: 'check-field'

	patient_assistance:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_assistance'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				rank: 'none'
				edit: false
				fields: ['created_by', 'date', 'pt_eligible', 'type', 'assistance_status']
				width: [20, 15, 15, 30, 20]
			label: 'Patient Assistance'

	subform_tat:
		model:
			multi: true
			required: false
			source: 'careplan_order_tat'
			type: 'subform'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['step', 'datetime_start', 'datetime_end']
				width: [40, 30, 30]
			label: 'Turn Around Time'
			readonly: true

	infusion_suite_id:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	branded_unbranded:
		model:
				required: false
			view:
				offscreen: true
				readonly: true

	brand_name_id:
		model:
			required: false
		view:
			columns: 3
			note: 'If known'
			label: 'Brand Name'
model:
	sections_group: [
		'Intake Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['intake_completed']
			tab: 'Intake'
		'Referral Details':
			hide_header: true
			indent: false
			fields: ['created_by', 'status_id', 'fltr_status', 'site_id','next_fill_number', 'therapy_id',
			'nogo_date', 'discontinued_date', 'corrections_needed', 'prescriber_id', 'supervising_id', 'infusion_suite_id',
			'physician_id', 'territory_id', 'commissioned_sales_rep',
			'written_date', 'expiration_date', 'summary']
			tab: 'Intake'
		'Diagnosis':
			hide_header: true
			indent: false
			tab: 'Intake'
			fields: ['dx_ids']
		'Intake Status':
			hide_header: true
			indent: false
			fields: ['intake_substatus_id', 'date_received', 'time_received',
			'brand_name_id', 'ep_priority_level', 'requires_nursing']
			tab: 'Intake'
		'Billing Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['billing_completed']
			tab: 'Billing'
		'Billing':
			hide_header: true
			indent: false
			fields: ['requires_assistance', 'assistance_notes', 'bill_supplies', 'supply_billable_id']
			tab: 'Billing'
		'Payers':
			hide_header: true
			indent: false
			fields: ['payer_ids']
			tab: 'Billing'
		'Patient Assistance':
			hide_header: true
			indent: false
			fields: ['patient_assistance']
			tab: 'Billing'
		'Nursing Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['nursing_completed']
			tab: 'Nursing'
		'Nursing':
			hide_header: true
			indent: false
			fields: ['nursing_status', 'nursing_agency_id', 'first_visit_date', 'nurse_billable_id']
			tab: 'Nursing'
		'Pharmacy Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['pharmacy_completed']
			tab: 'Pharmacy'
		'Pharmacy Status':
			hide_header: true
			indent: false
			fields: ['substatus_id', 'template_id', 'supply_kit_id','day_supply',
			'start_date', 'stop_date', 'is_refill', 'refill_order_id','validated',
			'validated_by', 'validation_datetime']
			tab: 'Pharmacy'
		'Prescriptions Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['prescription_completed']
			tab: 'Prescriptions'
		'Pending Prescriptions':
			hide_header: true
			indent: false
			fields: ['subform_items']
			tab: 'Prescriptions'
		'Active Prescriptions':
			hide_header: true
			indent: false
			fields: ['active_items']
			tab: 'Prescriptions'
		'Discontinued Prescriptions':
			hide_header: true
			indent: false
			fields: ['discontinued_items']
			tab: 'Prescriptions'
		'Supplies Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['supplies_completed']
			tab: 'Supplies'
		'Supplies':
			indent: false
			fields: ['subform_supply']
			tab: 'Supplies'
		'Rentals':
			indent: false
			fields: ['subform_rental']
			tab: 'Billing'
		'Labs Completed':
			hide_header: true
			indent: false
			compact: true
			fields: ['labs_completed', 'no_labs_orders']
			tab: 'Labs'
		'Labs':
			indent: false
			fields: ['subform_lab']
			tab: 'Labs'
		'Void':
			modal: true
			fields: ['show_void_warnings', 'void_warnings', 'void', 'voided_datetime', 'void_reason_id']
	]