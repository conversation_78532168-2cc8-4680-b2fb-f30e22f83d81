fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	understand_med:
		model:
			required: true
			max: 3
			min: 1

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'This medication is called Vyepti (eptinezumab-jjmr) and is given IV for prevention of migraine headaches. The infusion will take 30 minutes. Do you understand what this medication does for your body or how it is administered?'

	have_questions:
		model:
			required: true
			max: 3
			min: 1

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Discuss administration (IV infusion), frequency (every 90 days) and nursing involvement for administration. Do you have any questions about how it will be administered?'

	reactions:
		model:
			required: true
			max: 3
			min: 1

			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Its more of a rare reaction, but this medication can cause serious hypersensitivity reactions, including anaphylaxis. This is why we recommend having an Epipen available in your home (discuss preferred retail pharmacy if must transfer out for preferred network). Do you have any questions about that?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections_group: [
		'Vyepti Questionnaire':
			sections: [
				'Vyepti Patient Assessment':
					fields: ['understand_med', 'have_questions', 'reactions']
			]
		]
view:
	comment: 'Patient > Intake > Assessment > Vyepti'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Vyepti'
	open: 'read'
