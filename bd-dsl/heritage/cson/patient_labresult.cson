fields:

	review_by_pharmacist:
		model:
			source: ['No', 'Yes']
			access:
				write: ['-cm', '-cma', '-csr', '-nurse']
		view:
			findfilter: 'Yes'
			control: 'radio'
			label: 'Reviewed By Pharmacist?'

	components:
		model:
			subfields:
				component:
					label: 'Component'
				unit:
					label: 'Unit'
				value:
					label: 'Value'
					type: 'decimal'
				comment:
					label: 'Comment'
			type: 'json'
		view:
			control: 'grid'
			label: 'Result Components'

	subform_attachment:
		model:
			access:
				read: ['-patient']
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr','nurse', 'pharm', 'physician', 'payor']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']

	sections_group: [
		'Lab':
			fields: ['draw_date', 'receive_date', 'lab_id', 'type']
		'Results':
			fields: ['components', 'result', 'result_other', 'result_comments', 'review_by_pharmacist']
		'File Attachments':
			fields: ['subform_attachment']
	]

view:
	grid:
		fields: ['receive_date', 'lab_id', 'result_comments', 'review_by_pharmacist']
