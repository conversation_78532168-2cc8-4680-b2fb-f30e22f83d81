fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	sig_optout:
		model:
			multi: true
			source: ['Patient requested to opt-out of signature required deliveries. The Opt-out waiver will be sent for patient signature prior to next shipment.']
		view:
			control: 'checkbox'
			label: 'Therapy Alert'
			class: 'list'
			readonly: true

	sig_optout_educated:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: '<PERSON><PERSON> was educated on the importance of signature required and still wishes to opt-out?'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Delivery Signature Opt-Out':
			hide_header: true
			indent: false
			fields: ['sig_optout', 'sig_optout_educated']

view:
	comment: 'Patient > Billing Assessment Delivery Signature Opt-Out'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Delivery Signature Opt-Out'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']