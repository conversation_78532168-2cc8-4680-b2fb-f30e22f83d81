fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'


	therapy_id:
		model:
			source: 'list_therapy'
			sourceid: 'code'
		view:
			label: 'Therapy Type'
			columns: 2

	reason:
		model:
			source: ['Yes','No']
		view:
			control: 'radio'
			label: 'Identified yourself and the reason for the call?'
			columns: 2

	confirm_contact_info:
		model:
			source: ['Yes','No']
		view:
			control: 'radio'
			label: 'Confirmed the best contact number to reach the patient?'
			columns: 2

	confirm_best_time:
		model:
			source: ['Yes','No']
		view:
			control: 'radio'
			label: 'Confirmed the best times and days to reach the patient?'
			columns: 2

	confirm_questions:
		model:
			source: ['Yes','No']
		view:
			control: 'radio'
			label: 'Confirmed if the patient has any questions about their therapy?'
			columns: 2

	confirm_main_phone:
		model:
			source: ['Yes','No']
		view:
			control: 'radio'
			label: 'Provided our main phone number?'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Introduction Call':
			hide_header: true
			indent: false
			fields: ['therapy_id', 'reason', 'confirm_contact_info',
			'confirm_best_time', 'confirm_questions', 'confirm_main_phone']

view:
	comment: 'Patient > Billing Assessment Introduction Call'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Introduction Call'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']