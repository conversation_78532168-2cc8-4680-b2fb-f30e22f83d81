fields:

	patient_dob:
		model:
			prefill: ['patient.dob']
			type: 'date'
		view:
			label: 'DOB'
			readonly: true
			offscreen: true

	patient_screened:
		model:
			max: 3
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'The patient has been screened and deemed competent to administer factor replacement therapy without medical supervision.'

	preferred_package:
		model:
			prefill: ['assessment_factor']
			multi: true
			source: ['Separate Doses', 'Separate Supply Kits', 'Bulk Supply Bags', 'Do<PERSON> and Supplies together']
		view:
			control: 'checkbox'
			label: 'Preferred Packaging'

	cust_htc_care:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient obtain care from an HTC?'

	cust_htc_details:
		view:
			columns: 2
			control: 'area'
			label: 'HTC comment:'

	cust_referrer_name:
		model:
			source: ['Children’s Hospital New Orleans – Louisiana HTC', 'Cure 4 the kids', 'Iowa City HTC', 'Oklahoma HTC', 'Kansas City HTC', 'St. Louis HTC', 
			'Arkansas HTC', 'Nebraska HTC', 'Sugar House Hematology']
			prefill: ['assessment_factor']
		view:
			control: 'radio'
			label: 'Referrer Location'
			validate: [
				name: 'AddHTCAddress'
			]

	cust_referrer_address:
		model:
			if:
				"1200 Children's Ave. Suite 10000":
					note: 'Patient is an OK HTC Patient'
		view:
			label: 'Referrer Location Address'
			readonly: true

	cust_diagnosis:
		model:
			source: ['Hemophilia A (Factor VIII Deficiency)', 'Hemophilia B (Factor IX Deficiency)',
			'Hemophilia C (Factor XI Deficiency, Rosenthal syndrome)', 'Von Willebrand Disease',
			'Factor XIII Deficiency', 'Other']
			if:
				'Hemophilia A (Factor VIII Deficiency)':
					fields: ['cust_diagnosis_a']
				'Hemophilia B (Factor IX Deficiency)':
					fields: ['cust_diagnosis_b']
				'Von Willebrand Disease':
					fields: ['cust_diagnosis_vwd']
				'Other':
					fields: ['cust_diagnosis_other']
		view:
			control: 'radio'
			label: 'Diagnosis'

	cust_diagnosis_a:
		model:
			source: ['Mild (baseline FVIII >5%)', 'Moderate (baseline FVIII between 1-5%)',
			'Severe (baseline FVIII <1%)']
		view:
			control: 'radio'
			label: 'Hemophilia A (Factor VIII Deficiency)'

	cust_diagnosis_b:
		model:
			source: ['Mild (baseline IX >5%)', 'Moderate (baseline IX between 1-5%)',
			'Severe (baseline IX <1%)']
		view:
			control: 'radio'
			label: 'Hemophilia B (Factor IX Deficiency)'

	cust_diagnosis_vwd:
		model:
			source: ['Type 1', 'Type 2A', 'Type 2B', 'Type 2M', 'Type 2N', 'Type 3']
		view:
			control: 'radio'
			label: 'Von Willebrand Disease'

	cust_diagnosis_other:
		view:
			control: 'area'
			label: 'Other Diagnosis Details'

	track_bleeds_how:
		model:
			required: true
		view:
			control: 'area'
			label: 'If so, what method do you use and how often are you tracking them?'

	track_bleeds_why:
		model:
			required: true
		view:
			control: 'area'
			label: 'If not, what is the reason?'
			
	cust_therapy_regimen:
		model:
			multi: false
			source: ['Prophylaxis for Factor Replacement', 'On-Demand', 'Prophylaxis and On-Demand', 'Immune Tolerance Induction', 'Surgery', 'Prophylaxis for Non-Factor Replacement (i.e. Hemlibra)']
		view:
			control: 'checkbox'
			label: 'Therapy Regimen'

	excercise:
		view:
			label: 'Exercise/Activity level'
	
	cust_admin_method:
		model:
			source: ['Self', 'Parent or Caregiver', 'Healthcare professional', 'Other']
			if:
				'Other':
					fields: ['cust_admin_method_details']
		view:
			columns: 2
			control: 'radio'
			label: 'How does the patient administer factor?'

	cust_admin_method_details:
		model:
			required: true
		view:
			columns: 2
			label: 'Administration Method Details:'
	
	educate_tracking:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: 2
			control: 'radio'
			label: 'Patient educated about importance of tracking bleeds?'
	
	educate_tracking_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Bleed Tracking Education Comment'

	educate_reporting:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient report their monthly bleeds to the pharmacy?'
	
	educate_reporting_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Bleed Reporting Education Comment'

	track_bleeds:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			note: '(bleed log, notebook, phone app, etc.)'
			label: 'Does the patient track their bleeds?'
	
	track_bleeds_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Patient Bleed Tracking Comment'

	cust_general_education_packet:
		model:
			max: 3
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Did you include general education material in the admissions packet?'
	
model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	sections:
		'Factor Pre-Assessment':
			area: 'preassessment'
			fields: ['patient_dob', 'cust_htc_care', 'cust_htc_details', 'cust_referrer_name', 'cust_referrer_address',
			'cust_diagnosis', 'cust_diagnosis_a', 'cust_diagnosis_b', 'cust_diagnosis_vwd', 'cust_diagnosis_other',
			'activity_level', 'cust_therapy_regimen', 'has_hcv', 'has_hcv_details', 'has_hiv', 'hiv_drugs', 'has_hiv_details',
			'cust_general_education_packet'
			]
		'Factor Patient Assessment':
			area: 'admin'
			fields: ['excercise', 'have_inhibitors_history', 'have_inhibitors_history_details',  'took_immune_tolerance_treatment', 'immune_tolerance_comment', 'current_inhib_pt','target_joints', 'target_joints_other', 'bleed_history']
		'Factor Dose Tracking':
			fields: ['dose_tracking']
		'Factor Administration Assessment':
			area: 'admin'
			fields: ['cust_admin_method', 'cust_admin_method_details', 'nurse_support', 'patient_trained', 'patient_screened']
		'Factor Medication Storage Assessment':
			fields: ['preferred_package', 'medication_storage', 'medication_storage_other', 'check_expiration_reminder', 'check_expiration_reminder_details']
		'Factor Hemophilia Bleed Reporting':
			area: 'footer'
			fields: ['educate_tracking', 'educate_tracking_comment', 'educate_reporting', 'educate_reporting_comment'
			'track_bleeds', 'track_bleeds_how', 'track_bleeds_why', 'track_bleeds_comment']
