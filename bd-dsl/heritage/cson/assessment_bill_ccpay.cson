fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	approved_amt:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			label: 'Credit Card Approved Amt $'
			class: 'numeral money'
			format: '$0,0.00'
			columns: 3

	ref:
		view:
			label: 'Reference #'
			columns: 3

	auth:
		view:
			label: 'Auth Code'
			columns: 3

	receipt:
		model:
			source: ['Mail Receipt', 'Email Receipt', 'No Receipt to be sent']
		view:
			control: 'radio'
			label: 'Verbal authorization by'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections:
		'Credit Card Payment':
			hide_header: true
			indent: false
			fields: ['approved_amt', 'ref', 'auth', 'receipt']

view:
	comment: 'Patient > Billing Assessment Credit Card Payment'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Credit Card Payment'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']