fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	dob:
		model:
			prefill: ['patient']
			type: 'date'
		view:
			label: 'DOB'
			readonly: true

	pediatric:
		model:
			source: ['2-4', '5-7', '8-12', '13-18']
			if:
				'5-7':
					fields: ['participate5_7']
				'8-12':
					fields: ['participate8_12']
				'13-18':
					fields: ['participate13_18']
				'2-4':
					sections: ['2-4 Parent Physical Functioning', '2-4 Parent Emotional Functioning', '2-4 Parent Social Functioning', '2-4 Parent School Functioning']
			access:
				read: ['patient']
		view:
			control: 'radio'
			readonly: false
			label: 'Pediatric Age'

	participate5_7:
		model:
			source: ['Parent', 'Child']
			if:
				'Parent':
					sections: ['5-7 Parent Physical Functioning', '5-7 Parent Emotional Functioning', '5-7 Parent Social Functioning', '5-7 Parent School Functioning']
				'Child':
					sections: ['5-7 Child Physical Functioning', '5-7 Child Emotional Functioning', '5-7 Child Social Functioning', '5-7 Child School Functioning']
			access:
				read: ['patient']
		view:
			control: 'radio'
			label: 'Participate'

	participate8_12:
		model:
			source: ['Parent', 'Child']
			if:
				'Parent':
					sections: ['8-12 Parent Physical Functioning', '8-12 Parent Emotional Functioning', '8-12 Parent Social Functioning', '8-12 Parent School Functioning']
				'Child':
					sections: ['8-12 Child Health and Activities', '8-12 Child About my Feelings', '8-12 Child How I get Along with Others', '8-12 Child About School']
			access:
				read: ['patient']
		view:
			control: 'radio'
			label: 'Participate'

	participate13_18:
		model:
			source: ['Parent', 'Child']
			if:
				'Parent':
					sections: ['13-18 Parent Physical Functioning', '13-18 Parent Emotional Functioning', '13-18 Parent Social Functioning', '13-18 Parent School Functioning']
				'Child':
					sections: ['13-18 Child Health and Activities', '13-18 Child About my Feelings', '13-18 Child How I get Along with Others', '13-18 Child About School']
			access:
				read: ['patient']
		view:
			control: 'radio'
			label: 'Participate'

	age:
		model:
			type: 'int'
		view:
			label: 'Age'
			readonly: true
			offscreen: true

	assessment_date:
		model:
			type: 'date'
		view:
			label: 'Assessment Date'
			template: '{{now}}'

	last_assessment_date:
		model:
			type: 'date'
			prefill: ['clinical_sf12v2.assessment_date']
		view:
			label: 'Last Assessment Date'
			readonly: true

	# Survey Participation
	pt_qol_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					fields: ['assessment_date', 'pediatric']
				'No':
					fields: ['cust_pt_refuses']
			prefill: ['clinical_sf12v2']
		view:
			findfilter: 'Yes'
			control: 'radio'
			note: 'Pediatric Quality Of Life is given for patients 5-18 years old and parents of children 2-18'
			label: 'Will a Pediatric Quality of Life survey be completed today?'

	cust_pt_refuses:
		model:
			required: false
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pt_refuses_date', 'cust_pt_refuses_confirmation']
		view:
			control: 'radio'
			label: 'Is the patient refusing to complete?'

	cust_pt_refuses_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'Date Refused'
			template: '{{now}}'

	cust_pt_refuses_confirmation:
		model:
			required: false
			multi: false
			source: ['OK']
		view:
			control: 'checkbox'
			label: 'This will be documented that patient is refusing completion of the Pediatric Quality of Life. Please try to collect each quarter. Please notify Px of this refusal'

# Child 5-7
	running5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'It is hard for you to run'

	play_sports5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'It is hard for you to play sports or exercise'

	bathing5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'It is hard for you to take a bath or shower'

	chores5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'It is hard for you to do chores (like pick up your toys)'

	aches5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
			if:
				'*':
					fields: ['aches_where5_7']
		view:
			control: 'radio'
			label: 'Do you have aches?'

	aches_where5_7:
		model:
			required: false
		view:
			label: 'Where?'

	tired5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'You feel too tired to play'

	sad5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'You feel sad'

	sleep5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'You have trouble sleeping'

	social5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'It is hard for you to get along with other kids'

	keep_up5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'It is hard for you to keep up when you play with other kids'

	forgetful5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'You forget things'

	miss_school5_7:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time','Some of the time','All of the time']
		view:
			control: 'radio'
			label: 'You miss school because of not feeling good'

# Child 8-12
	running8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to run'

	play_sports8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to do sports activities or exercise'

	bathing8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to take a bath or shower by myself'

	chores8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to do chores around the house'

	aches8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I hurt or ache'

	tired8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I have low energy'

	sad8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I feel sad or blue'

	sleep8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I have trouble sleeping'

	social8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I have trouble getting along with other children'

	keep_up8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard to keep up when I play with other children'

	forgetful8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I forget things'

	miss_school8_12:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I miss school because of not feeling well'

# Child 13-18
	running13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to run'

	play_sports13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to do sports activities or exercise'

	bathing13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to take a bath or shower by myself'

	chores13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'It is hard for me to do chores around the house'

	aches13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I hurt or ache'

	tired13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I have low energy'

	sad13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I feel sad or blue'

	sleep13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I have trouble sleeping'

	social13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I have trouble getting along with other teens'

	keep_up13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I cannot do things that other teens my age can do'

	forgetful13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I forget things'

	miss_school13_18:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'I miss school because of not feeling well'

# Partent 2-4
	running2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Running'

	play_sports2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Participating in active play or exercise'

	bathing2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Bathing'

	chores2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Helping to pick up his or her toys'

	aches2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Having hurts or aches'

	tired2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Low energy level'

	sad2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Feeling sad or blue'

	sleep2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Trouble Sleeping'

	social2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Playing with other children'

	keep_up2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Not able to do things that other children his or her age can do'

	forgetful2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Doing the same school activities as peers'

	miss_school2_4p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Missing school/daycare because of not feeling well'

# Partent 5-7
	running5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Running'

	play_sports5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Participating in active play or exercise'

	bathing5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Taking a bath or shower by himself or herself'

	chores5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Doing chores, like picking up his or her toys'

	aches5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Having hurts or aches'

	tired5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Low energy level'

	sad5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Feeling sad or blue'

	sleep5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Trouble Sleeping'

	social5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Getting along with other children'

	keep_up5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Not able to do things that other children his or her age can do'

	forgetful5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Forgetting things'

	miss_school5_7p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Missing school because of not feeling well'

# Partent 8-12
	running8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Running'

	play_sports8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Participating in active play or exercise'

	bathing8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Taking a bath or shower by himself or herself'

	chores8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Doing chores around the house'

	aches8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Having hurts or aches'

	tired8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Low energy level'

	sad8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Feeling sad or blue'

	sleep8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Trouble Sleeping'

	social8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Getting along with other children'

	keep_up8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Not able to do things that other children his or her age can do'

	forgetful8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Forgetting things'

	miss_school8_12p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Missing school because of not feeling well'

# Partent 13-18
	running13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Running'

	play_sports13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Participating in active play or exercise'

	bathing13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Taking a bath or shower by himself or herself'

	chores13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Doing chores around the house'

	aches13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Having hurts or aches'

	tired13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Low energy level'

	sad13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Feeling sad or blue'

	sleep13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Trouble Sleeping'

	social13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Getting along with other teens'

	keep_up13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Not able to do things that other teens his or her age can do'

	forgetful13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Forgetting things'

	miss_school13_18p:
		model:
			required: false
			access:
				read: ['patient']
			max: 32
			source: ['None of the time', 'A little of the time', 'Some of the time', 'Most of the time', 'All of the time']
		view:
			control: 'radio'
			label: 'Missing school because of not feeling well'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'assessment_date']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		clinical_qol_peds:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Pediatric Quality Of Life Survey Participation':
			fields: ['age', 'dob', 'last_assessment_date', 'pt_qol_particiates', 'cust_pt_refuses', 'cust_pt_refuses_date', 'cust_pt_refuses_confirmation', 'pediatric', 'participate5_7', 'participate8_12', 'participate13_18', 'assessment_date']
		'5-7 Child Physical Functioning':
			note: 'Think about how you have been doing the last few weeks. Please listen carefully to each sentence and tell me how much of a problem this is for you. \n(After reading the item, gesture to the template. If the child hesitates or does not seem to understand how to answer read the response options while pointing at the faces.) \nProblems with...'
			fields: ['running5_7', 'play_sports5_7', 'bathing5_7', 'chores5_7', 'aches5_7', 'aches_where5_7', 'tired5_7']
			prefill: 'clinical_qol_peds'
		'5-7 Child Emotional Functioning':
			note: 'Remember, tell me how much of a problem this has been for you the last few weeks. Problems with...'
			fields: ['sad5_7', 'sleep5_7']
			prefill: 'clinical_qol_peds'
		'5-7 Child Social Functioning':
			note: 'Problems with...'
			fields: ['social5_7', 'keep_up5_7']
			prefill: 'clinical_qol_peds'
		'5-7 Child School Functioning':
			note: 'Problems with...'
			fields: ['forgetful5_7', 'miss_school5_7']
			prefill: 'clinical_qol_peds'

		'8-12 Child Health and Activities':
			note: 'Think about how you have been doing the last few weeks. Please listen carefully to each sentence and tell me how much of a problem this is for you. \nIn the Past Month, how much of a problem has this been for you...'
			fields: ['running8_12', 'play_sports8_12', 'bathing8_12', 'chores8_12', 'aches8_12', 'tired8_12']
			prefill: 'clinical_qol_peds'
		'8-12 Child About my Feelings':
			note: 'In the Past Month, how much of a problem has this been for you...'
			fields: ['sad8_12', 'sleep8_12']
			prefill: 'clinical_qol_peds'
		'8-12 Child How I get Along with Others':
			note: 'In the Past Month, how much of a problem has this been for you...'
			fields: ['social8_12', 'keep_up8_12']
			prefill: 'clinical_qol_peds'
		'8-12 Child About School':
			note: 'In the Past Month, how much of a problem has this been for you...'
			fields: ['forgetful8_12', 'miss_school8_12']
			prefill: 'clinical_qol_peds'

		'13-18 Child Health and Activities':
			note: 'Think about how you have been doing the last few weeks. Please listen carefully to each sentence and tell me how much of a problem this is for you. \nIn the Past Month, how much of a problem has this been for you...'
			fields: ['running13_18', 'play_sports13_18', 'bathing13_18', 'chores13_18', 'aches13_18', 'tired13_18']
			prefill: 'clinical_qol_peds'
		'13-18 Child About my Feelings':
			note: 'In the Past Month, how much of a problem has this been for you...'
			fields: ['sad13_18', 'sleep13_18']
			prefill: 'clinical_qol_peds'
		'13-18 Child How I get Along with Others':
			note: 'In the Past Month, how much of a problem has this been for you...'
			fields: ['social13_18', 'keep_up13_18']
			prefill: 'clinical_qol_peds'
		'13-18 Child About School':
			note: 'In the Past Month, how much of a problem has this been for you...'
			fields: ['forgetful13_18', 'miss_school13_18']
			prefill: 'clinical_qol_peds'

		'2-4 Parent Physical Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['running2_4p', 'play_sports2_4p', 'bathing2_4p', 'chores2_4p', 'aches2_4p', 'tired2_4p']
			prefill: 'clinical_qol_peds'
		'2-4 Parent Emotional Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['sad2_4p', 'sleep2_4p']
			prefill: 'clinical_qol_peds'
		'2-4 Parent Social Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['social2_4p', 'keep_up2_4p']
			prefill: 'clinical_qol_peds'
		'2-4 Parent School Functioning':
			note: '**Please complete this next section if your child attends school or daycare** \n In the past ONE month, how much of a problem has your child had with...'
			fields: ['forgetful2_4p', 'miss_school2_4p']
			prefill: 'clinical_qol_peds'

		'5-7 Parent Physical Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['running5_7p', 'play_sports5_7p', 'bathing5_7p', 'chores5_7p', 'aches5_7p', 'tired5_7p']
			prefill: 'clinical_qol_peds'
		'5-7 Parent Emotional Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['sad5_7p', 'sleep5_7p']
			prefill: 'clinical_qol_peds'
		'5-7 Parent Social Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['social5_7p', 'keep_up5_7p']
			prefill: 'clinical_qol_peds'
		'5-7 Parent School Functioning':
			note: '**Please complete this next section if your child attends school or daycare** \n In the past ONE month, how much of a problem has your child had with...'
			fields: ['forgetful5_7p', 'miss_school5_7p']
			prefill: 'clinical_qol_peds'

		'8-12 Parent Physical Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['running8_12p', 'play_sports8_12p', 'bathing8_12p', 'chores8_12p', 'aches8_12p', 'tired8_12p']
			prefill: 'clinical_qol_peds'
		'8-12 Parent Emotional Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['sad8_12p', 'sleep8_12p']
			prefill: 'clinical_qol_peds'
		'8-12 Parent Social Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['social8_12p', 'keep_up8_12p']
			prefill: 'clinical_qol_peds'
		'8-12 Parent School Functioning':
			note: '**Please complete this next section if your child attends school or daycare** \n In the past ONE month, how much of a problem has your child had with...'
			fields: ['forgetful8_12p', 'miss_school8_12p']
			prefill: 'clinical_qol_peds'

		'13-18 Parent Physical Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['running13_18p', 'play_sports13_18p', 'bathing13_18p', 'chores13_18p', 'aches13_18p', 'tired13_18p']
			prefill: 'clinical_qol_peds'
		'13-18 Parent Emotional Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['sad13_18p', 'sleep13_18p']
			prefill: 'clinical_qol_peds'
		'13-18 Parent Social Functioning':
			note: 'In the past ONE month, how much of a problem has your child had with...'
			fields: ['social13_18p', 'keep_up13_18p']
			prefill: 'clinical_qol_peds'
		'13-18 Parent School Functioning':
			note: '**Please complete this next section if your child attends school or daycare** \n In the past ONE month, how much of a problem has your child had with...'
			fields: ['forgetful13_18p', 'miss_school13_18p']
			prefill: 'clinical_qol_peds'

view:
	comment: 'Patient > Intake >  Pediatric Quality of Life'
	find:
		basic: ['pt_qol_particiates']
	grid:
		fields: ['created_on', 'assessment_date', 'created_by']
		sort: ['-id']
	label: 'Clinical: Pediatric Quality of Life'