


fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	ship_required:
		model:
			prefill: ['assessment_bill_htcauth.ship_required']
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Authorization to Ship required from HTC?'
			columns: 2

	ship_auth_approved:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ship_app_datetime']
		view:
			control: 'radio'
			label: 'Authorization to Ship Approved?'
			columns: 2

	ship_app_datetime:
		model:
			type: 'datetime'
		view:
			label: 'When the request was received back from HTC'
			columns: 2

	contact:
		model:
			prefill: ['assessment_bill_htcauth.contact']
			multi: true
			source: ['HTC Name: Jimmy Everest Center for Cancer & Blood Diseases in Children',
			'HTC Contact Person: TJ <PERSON>/<PERSON>',
			'HTC Phone Number: ************',
			'HTC Fax Number: ************']
		view:
			control: 'checkbox'
			label: 'HTC Contact'
			class: 'list'
			readonly: true

	request_mode:
		model:
			prefill: ['assessment_bill_htcauth.request_mode']
			required: false
			source: ['Phone', 'Fax']
		view:
			control: 'radio'
			label: 'Mode of Request'
			columns: 4

	patient_name:
		model:
			prefill: ['assessment_bill_htcauth.patient_name']
			required: true
		view:
			label: 'Patient Name'
			columns: 2

	dob:
		model:
			prefill: ['assessment_bill_htcauth.dob']
			required: true
			type: 'date'
		view:
			label: 'Date of Birth'
			columns: 4
			validate: [{
					name: 'DOBValidator'
			}
			]

	drug:
		model:
			prefill: ['assessment_bill_htcauth.drug']
			required: true
		view:
			label: 'Drug (Brand Name)'
			columns: 2

	hcpc:
		model:
			prefill: ['assessment_bill_htcauth.hcpc']
			required: false
		view:
			label: 'HCPC'
			columns: 4

	dose:
		model:
			prefill: ['assessment_bill_htcauth.dose']
			required: false
		view:
			label: 'Target Dose'
			columns: 4

	vial_assay:
		model:
			prefill: ['assessment_bill_htcauth.vial_assay']
			required: false
		view:
			label: 'Vial Assay'
			columns: 2

	quantity:
		model:
			prefill: ['assessment_bill_htcauth.quantity']
			required: false
		view:
			label: 'Quantity to Ship'
			columns: 2

	lotnumber:
		model:
			prefill: ['assessment_bill_htcauth.lotnumber']
			required: false
		view:
			label: 'Lot Number'
			columns: 2

	expiration:
		model:
			prefill: ['assessment_bill_htcauth.expiration']
			required: false
		view:
			label: 'Expiration Date'
			columns: 2

	other_meds:
		model:
			prefill: ['assessment_bill_htcauth.other_meds']
			required: false
		view:
			label: 'Any other Medication/Supplies Requested'
			columns: 2

	drug2:
		model:
			prefill: ['assessment_bill_htcauth.drug2']
			required: true
		view:
			label: 'Drug (Brand Name)'
			columns: 2

	hcpc2:
		model:
			prefill: ['assessment_bill_htcauth.hcpc2']
			required: false
		view:
			label: 'HCPC'
			columns: 4

	ship_date:
		model:
			prefill: ['assessment_bill_htcauth.ship_date']
			required: false
		view:
			label: 'Expected Date to Ship'
			columns: 4

	docs_attached:
		model:
			prefill: ['assessment_bill_htcauth.docs_attached']
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Documents attached to patient chart'
			columns: 2

	add_notes:
		model:
			prefill: ['assessment_bill_htcauth.add_notes']
			required: false
		view:
			control: 'area'
			label: 'Additional Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	prefill:
		assessment_bill_htcauth:
			link:
				patient_id: 'patient_id'
			filter:
				ship_required: 'Yes'
			max: 'created_on'
	name: ['patient_id']
	sections: 
		'HTC Authorization to Ship Request Approval':
			hide_header: true
			indent: false
			fields: ['ship_required', 'ship_auth_approved', 'ship_app_datetime',
					'contact', 'request_mode', 'patient_name', 'dob',
					'drug', 'hcpc', 'dose', 'vial_assay', 'quantity',
					'lotnumber', 'expiration', 'other_meds', 'drug2',
					'hcpc2', 'ship_date', 'docs_attached', 'add_notes']

view:
	comment: 'Patient > Billing Assessment HTC Authorization to Ship Request Approval'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment HTC Authorization to Ship Request Approval'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']