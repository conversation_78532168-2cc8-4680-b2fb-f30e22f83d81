fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date of Transfer'
			columns: 4

	demo_sheet:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Demographics sheet?'
			columns: 4

	insurance:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Insurance cards?'
			columns: 4

	copy_order:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "COPY of prescription orders?"
			columns: 4

	iv_mix:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Pharmacist Assessment?'
			columns: 4

	poc:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Plan of Care?'
			columns: 4

	med_hist:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: "Patient's current medication regimen, health history and progression towards achieving therapy goals and care plan"
			label: 'Medical History and Physical?'
			columns: 2

	summary:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Nursing Visit Notes Provided?'
			columns: 2

	pharm_review:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Pharmacist reviewed prior to sending?'
			columns: 2

	where_to:
		view:
			label: 'Where transferring patient to?'
			columns: 2

	phone:
		view:
			format: 'us_phone'
			label: "Phone #"
			columns: 4

	fax:
		view:
			format: 'us_phone'
			label: "Fax #"
			columns: 4

	reason:
		view:
			label: 'Reason for transfer?'
			columns: 2

	consent_dr:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Physician notified/consent?'
			columns: 4

	consent_pt:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient notified/consent?'
			columns: 4

	instr_pt:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Pharmacy referrals and instructions provided to the patient?'
			columns: 2

	add_docs:
		view:
			label: 'Additional Documentation?'
			columns: 2

	comments:
		view:
			label: 'Comments'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Patient Transfer':
			hide_header: true
			indent: false
			fields: ['date', 'demo_sheet', 'insurance','copy_order',
			'iv_mix', 'poc', 'med_hist', 'summary',
			'pharm_review', 'where_to',
			'phone', 'fax', 'reason', 'consent_dr',
			'consent_pt', 'instr_pt', 'add_docs', 'comments']

view:
	comment: 'Patient > Billing Assessment Patient Transfer'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Patient Transfer'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']