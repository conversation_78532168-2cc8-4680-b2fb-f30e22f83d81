fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	adr_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Event Date'
			template: '{{now}}'

	adr_reported:
		model:
			required: true
			multi: true
			source: 'list_reaction'
			if:
				'Other':
					fields: ['adr_reported_other']
		view:
			columns: 2
			label: 'Symptoms Reported'

	adr_reported_other:
		view:
			columns: 2
			label: 'Symptoms Other'

	adr_reaction:
		model:
			required: true
			source: ["No", "Yes"]
			if:
				'Yes':
					fields: ['adr_severity', 'adr_hospitalization', 'adr_admin_date', 'adr_drug_stopped', 'adr_abated_after_stopping', 'adr_reported_manufacture','adr_reported_fda', 'adr_phy_contact']
					sections: ['ADR Review']
		view:
			columns: -2
			control: 'radio'
			label: 'Is this an Adverse Drug Reaction (ADR)?'

	adr_severity:
		model:
			required: true
			source:
				mild: 'Mild'
				moderate: 'Moderate'
				severe: 'Severe'
			if:
				moderate:
					fields: ['adr_hospitalization', 'adr_drug_stopped']
				severe:
					fields: ['adr_hospitalization', 'adr_drug_stopped']
		view:
			columns: 2
			control: 'radio'
			label: 'ADR Severity'

	adr_hospitalization:
		model:
			required: true
			source: ["No", "Yes"]
			if:
				'Yes':
					fields: ['adr_admin_date']
		view:
			columns: -2
			control: 'radio'
			label: 'ADR Required Hospitalization?'

	adr_admin_date:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Date of administration'

	adr_drug_stopped:
		model:
			required: true
			source: ["No", "Yes"]
			if:
				'Yes':
					fields: ['adr_abated_after_stopping']
		view:
			columns: -2
			control: 'radio'
			label: 'Was the drug stopped because of the ADR?'

	adr_abated_after_stopping:
		model:
			required: true
			source: ["No", "Yes", "Maybe"]
		view:
			columns: 2
			control: 'radio'
			label: 'Did reaction abate after stopping the drug?'

	adr_reported_manufacture:
		model:
			required: true
			source: ["No", "Yes"]
		view:
			columns: 2
			control: 'radio'
			label: 'Was the reaction reported to the manufacturer?'
	
	adr_reported_fda:
		model:
			required: false
			source: ["No", "Yes"]
		view:
			columns: 2
			control: 'radio'
			label: 'Was this reported to FDA MedWatch?'

	adr_phy_contact:
		model:
			required: false
			source: ["No", "Yes"]
		view:
			columns: 2
			control: 'radio'
			label: 'Was the physician contacted?'
	
	physician_notified:
		model:
			required: false
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['phy_datetime']
		view:
			columns: -2
			control: 'radio'
			label: 'If not previously contacted, was it determined that the physician should be notified?'
	
	phy_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'When physician contact was made?'
			note: 'Please select date'

	manufacturer_notified:
		model:
			required: false
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['manu_datetime']
		view:
			columns: -2
			control: 'radio'
			label: 'If not previously contacted, was it determined that the manufacturer should be notified?'
	
	manu_datetime:
		model:
			type: 'datetime'
		view:
			columns: 2
			label: 'When manufacturer contact was made?'
			note: 'Please select date'

	summary:
		view:
			control: 'area'
			label: 'Summary'

	comments:
		view:
			control: 'area'
			label: 'Comments'

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['physician']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'ADR Details':
			hide_header: true
			indent: false
			fields: ['adr_date', 'adr_reported', 'adr_reaction','adr_reported_other', 'adr_severity', 'adr_hospitalization', 'adr_admin_date', 'adr_drug_stopped', 'adr_abated_after_stopping', 'adr_reported_manufacture', 'adr_reported_fda', 'adr_phy_contact']
		'ADR Review':
			indent: false
			fields: ['physician_notified', 'phy_datetime', 'manufacturer_notified', 'manu_datetime', 'summary']
		'Comments':
			indent: false
			fields: ['comments']

	transform_post: [
			name: "AutoNote"
			arguments:
				subject: "ADR"
	]

view:
	comment: 'Patient > Careplan > Reported ADR'
	grid:
		fields: ['adr_date', 'adr_reported', 'adr_severity', 'adr_hospitalization', 'adr_admin_date']
		sort: ['-id']
	label: 'Reported ADR'
	open: 'read'
