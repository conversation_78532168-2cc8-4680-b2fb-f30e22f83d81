fields:

	# Renal Assessment
	has_diabetes:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['has_insulin']
		view:
			columns: 2
			control: 'radio'
			label: 'History of diabetes?'

	has_insulin:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Does patient depend on insulin to regulate blood sugar?'

	had_kidney_disease:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'History of kidney disease?'

	meds_taking:
		model:
			max: 32
			min: 1
			multi: true
			source: ['Acyclovir','Aminoglycosides','Amphotericin','Atripla','Cisplatin','Diuretics (loops, thiazides)',
				'Prograf','Tenofovir','Viread','Truvada','Other','None']
			if:
				'Other':
					fields: ['meds_taking_other']
		view:
			control: 'checkbox'
			label: 'Any of the following concomitant nephrotoxic drugs?'

	meds_taking_other:
		view:
			label: 'Other Drugs'

	has_htn:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pressure_controlled']
		view:
			columns: 2
			control: 'radio'
			label: 'History of hypertension?'

	pressure_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Is patient's blood pressure currently controlled?"

	high_chol:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['chol_controlled']
		view:
			columns: 2
			control: 'radio'
			label: 'History of hyperlipidemia?'

	chol_controlled:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Is patient's cholesterol currently controlled?"

	cust_comorbid_cond:
		model:
			max: 64
			min: 1
			multi: true
			source: ['Congestive heart failure (CHF)', 'Cardiomyopathy', 'Valve Disease', 'Congenital Defects',
			'Atrial Fibrillation', 'Angina', 'Pulmonary disease', 'Cancer/Chemotherapy', 'Sickle Cell Anemia',
			'Thrombosis', 'Coronary Artery Disease (CAD/Atherosclerotic)', 'Pulmonary Embolism', 'Deep Vein Thrombosis (DVT)',
			'Cerebral Infarction', 'Myocardial Infarction', 'Other', 'None']
			if:
				'Other':
					fields: ['cust_comorbid_cond_other']
					note: 'Discuss risks in clinical huddle and offer options to prescriber. Counsel patient on this.'
				'None':
					note: ' '
				'*':
					note: 'Discuss risks in clinical huddle and offer options to prescriber. Counsel patient on this.'
		view:
			control: 'checkbox'
			label: 'History of any of the following co-morbidities?'

	cust_comorbid_cond_other:
		view:
			control: 'area'
			label: 'Other Co-morbidities'

	cust_ig_appropriate:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_ig_appropriate_details']
		view:
			columns: 2
			control: 'radio'
			label: "Is IG dose appropriate?"

	cust_ig_appropriate_details:
		view:
			columns: 2
			control: 'area'
			label: "IG dose appropriateness"

	cust_ig_rate_table:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Infusion Rate Table Created and scanned into profile?"

	cust_ig_infusion_pump:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_ig_pump_rental']
		view:
			columns: 2
			control: 'radio'
			label: "Infusion on pump?"

	cust_ig_pump_rental:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Pump Rental Agreement Form complete?"

	cust_ig_ephinephrine:
		view:
			columns: 2
			control: 'area'
			label: "Epinephrine autoinjector brand, dispensing location, and cost?"

	cust_ig_understands:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'This medication comes from a few different manufacturers that use different trade names. The product that was prescribed to you is ______. It is oftentimes abbreviated as IVIG (or SubQIG, if applicable). This is short for intravenous immune globulin (or subcutaneous, if applicable). Immune globulin is a scientific word more commonly called an antibody, which you may be more familiar with. Antibodies are proteins that are a part of your immune system. Do you understand what this medication does for your body?'

	cust_ig_admin_questions:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Discuss administration (IV/SC - needle qty), frequency, and nursing involvement (education or administration). Do you have any questions about how it will be administrated?'

	cust_ig_process:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'This medication comes from people that donate their plasma. It is processed, concentrated, tested, and treated to remove possible infectious agents by the manufacturer. Do you have any questions about that?'

	cust_ig_headaches:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_ig_headaches_prevention']
		view:
			control: 'radio'
			label: 'The most commmon adverse reactions to this medication are headache and fatigue. Do you have a history of headaches?'

	cust_ig_headaches_prevention:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Counsel on how to prevent and treat headaches?'

	cust_ig_hydration:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Counsel on importance of hydration to reduce side effects?'

	cust_ig_clots:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'It is a more rare reaction, but this medication also increases the risk of blood clots or anaphylaxis. This is why we recommend having an Epipen available in your home (discuss preferred retail pharmacy if must transfer out for preferred network). Do you have any questions about that?'

	#Base form overrides
	delivery_date:
		model:
			required: false

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		assessment_ivig:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections_group: [
		'IG Questionnaire':
			sections: [
				'IG Renal Disease Risk Assessment':
					area: 'preassessment'
					fields: ['has_diabetes', 'has_insulin', 'had_kidney_disease', 'meds_taking', 'meds_taking_other']
				'IG Thromboembolic Risk Assessment':
					area: 'preassessment'
					fields: ['has_htn', 'pressure_controlled', 'high_chol', 'chol_controlled', 'cust_comorbid_cond', 'cust_comorbid_cond_other']
				'IG Calculations':
					area: 'preassessment'
					fields: ['cust_ig_appropriate', 'cust_ig_appropriate_details', 'cust_ig_rate_table',
					'cust_ig_infusion_pump', 'cust_ig_pump_rental', 'cust_ig_ephinephrine']
				'IG Patient Assessment':
					fields: ['cust_ig_understands', 'cust_ig_admin_questions', 'cust_ig_process',
					'cust_ig_headaches', 'cust_ig_headaches_prevention', 'cust_ig_hydration', 'cust_ig_clots']
			]
		]
view:
	comment: 'Patient > Careplan > Assessment > IG'
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
	label: 'Assessment Questionnaire: IG'
	open: 'read'
