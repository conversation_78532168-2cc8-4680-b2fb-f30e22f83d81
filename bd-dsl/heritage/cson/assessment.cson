fields:

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['cust_storage', 'cust_educate_management_program', 'medication_in_stock', 'has_ems', 'subform_bleed_events' ,'cust_nursing_services', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_bio_waste_diposal', 'cust_counseled_treatment_logs', 'cust_counseled_compliance', 'cust_counseled_importantance_treating_bleed', 'cust_factor_labs_reviewed', 'cust_factor_halflife', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_factor_consignment', 'cust_factor_consignment_comment', 'pt_participate_poc']
					sections: ['Nursing Services', 'Factor Bleed Events Log', 'Factor Day Supply']
				'ig':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_side_effects', 'cust_counseled_thrombotic_risk', 'cust_counseled_subq_skin_reaction', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['cust_storage', 'cust_educate_management_program', 'medication_in_stock', 'has_ems', 'subform_bleed_events' ,'cust_nursing_services', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_bio_waste_diposal', 'cust_counseled_treatment_logs', 'cust_counseled_compliance', 'cust_counseled_importantance_treating_bleed', 'cust_factor_labs_reviewed', 'cust_factor_halflife', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_factor_consignment', 'cust_factor_consignment_comment', 'pt_participate_poc']
					sections: ['Nursing Services', 'Factor Bleed Events Log', 'Factor Day Supply']
				'ig':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_side_effects', 'cust_counseled_thrombotic_risk', 'cust_counseled_subq_skin_reaction', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['cust_storage', 'cust_educate_management_program', 'medication_in_stock', 'has_ems', 'subform_bleed_events' ,'cust_nursing_services', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_bio_waste_diposal', 'cust_counseled_treatment_logs', 'cust_counseled_compliance', 'cust_counseled_importantance_treating_bleed', 'cust_factor_labs_reviewed', 'cust_factor_halflife', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_factor_consignment', 'cust_factor_consignment_comment', 'pt_participate_poc']
					sections: ['Nursing Services', 'Factor Bleed Events Log', 'Factor Day Supply']
				'ig':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_side_effects', 'cust_counseled_thrombotic_risk', 'cust_counseled_subq_skin_reaction', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['cust_storage', 'cust_educate_management_program', 'medication_in_stock', 'has_ems', 'subform_bleed_events' ,'cust_nursing_services', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_bio_waste_diposal', 'cust_counseled_treatment_logs', 'cust_counseled_compliance', 'cust_counseled_importantance_treating_bleed', 'cust_factor_labs_reviewed', 'cust_factor_halflife', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_factor_consignment', 'cust_factor_consignment_comment', 'pt_participate_poc']
					sections: ['Nursing Services', 'Factor Bleed Events Log', 'Factor Day Supply']
				'ig':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_side_effects', 'cust_counseled_thrombotic_risk', 'cust_counseled_subq_skin_reaction', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['cust_storage', 'cust_educate_management_program', 'medication_in_stock', 'has_ems', 'subform_bleed_events' ,'cust_nursing_services', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_bio_waste_diposal', 'cust_counseled_treatment_logs', 'cust_counseled_compliance', 'cust_counseled_importantance_treating_bleed', 'cust_factor_labs_reviewed', 'cust_factor_halflife', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_factor_consignment', 'cust_factor_consignment_comment', 'pt_participate_poc']
					sections: ['Nursing Services', 'Factor Bleed Events Log', 'Factor Day Supply']
				'ig':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_side_effects', 'cust_counseled_thrombotic_risk', 'cust_counseled_subq_skin_reaction', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['cust_storage','cust_educate_management_program','medication_in_stock', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_route', 'cust_counseled_medication_purpose', 'cust_counseled_supply_disposal', 'cust_counseled_additional', 'cust_labs_reviewed', 'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	subform_therapy_1:
		model:
			source: 'assessment_{therapy_1}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
				'assessment_nursing': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'assessment_{therapy_2}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
				'assessment_nursing': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'assessment_{therapy_3}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
				'assessment_nursing': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'assessment_{therapy_4}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
				'assessment_nursing': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'assessment_{therapy_5}'
			sourcefilter:
				'assessment_factor': {}
				'assessment_aat': {}
				'assessment_ig': {}
				'assessment_subqig': {}
				'assessment_tnf': {}
				'assessment_tpn': {}
				'assessment_steroid': {}
				'assessment_inotrope': {}
				'assessment_chemotherapy': {}
				'assessment_bisphosphonates': {}
				'assessment_nursing': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['cust_storage','cust_sched_next_infusion', 'cust_educate_management_program', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyepti':
					fields: ['cust_storage','medication_in_stock', 'cust_counsel_therap_manage', 'cust_counsel_therap_manage_comm', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_supply_disposal',
					'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_additional', 'cust_labs_reviewed', 
					'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed',
					'cust_counsel_90day_call', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyvgart':
					fields: ['cust_storage','cust_vitals_reviewed', 'cust_allergies', 'cust_history_physical_reviewed', 'medication_in_stock',
					'cust_counseled_route', 'cust_counseled_medication_purpose',
					'cust_counseled_risk_headache', 'cust_counseled_risk_anaphylaxis', 'cust_counseled_supply_disposal',
					'cust_counsel_90day_call', 'cust_counsel_storage_shipping', 'cust_nursing_services',
					'cust_ongoing_labs', 'cust_medication_list_reviewed', 'pt_participate_poc']
					sections: ['Nursing Services']
				'tepezza':
					fields: ['cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_history_physical_reviewed', 'cust_careplan_created',
					'medication_in_stock', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseling_ibd',
					'cust_counseling_hyperglycemia','cust_counseling_contraception', 'cust_educate_management_program_3wk',
					'cust_storage_tepezza', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_ongoing_labs',
					'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_counsel_initial_shipment','cust_counseled_additional', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['cust_storage','cust_sched_next_infusion', 'cust_educate_management_program', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyepti':
					fields: ['cust_storage','medication_in_stock', 'cust_counsel_therap_manage', 'cust_counsel_therap_manage_comm', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_supply_disposal',
					'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_additional', 'cust_labs_reviewed', 
					'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed',
					'cust_counsel_90day_call', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyvgart':
					fields: ['cust_storage','cust_vitals_reviewed', 'cust_allergies', 'cust_history_physical_reviewed', 'medication_in_stock',
					'cust_counseled_route', 'cust_counseled_medication_purpose',
					'cust_counseled_risk_headache', 'cust_counseled_risk_anaphylaxis', 'cust_counseled_supply_disposal',
					'cust_counsel_90day_call', 'cust_counsel_storage_shipping', 'cust_nursing_services',
					'cust_ongoing_labs', 'cust_medication_list_reviewed', 'pt_participate_poc']
					sections: ['Nursing Services']
				'tepezza':
					fields: ['cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_history_physical_reviewed', 'cust_careplan_created',
					'medication_in_stock', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseling_ibd',
					'cust_counseling_hyperglycemia','cust_counseling_contraception', 'cust_educate_management_program_3wk',
					'cust_storage_tepezza', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_ongoing_labs',
					'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_counsel_initial_shipment','cust_counseled_additional', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['cust_storage','cust_sched_next_infusion', 'cust_educate_management_program', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyepti':
					fields: ['cust_storage','medication_in_stock', 'cust_counsel_therap_manage', 'cust_counsel_therap_manage_comm', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_supply_disposal',
					'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_additional', 'cust_labs_reviewed', 
					'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed',
					'cust_counsel_90day_call', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyvgart':
					fields: ['cust_storage','cust_vitals_reviewed', 'cust_allergies', 'cust_history_physical_reviewed', 'medication_in_stock',
					'cust_counseled_route', 'cust_counseled_medication_purpose',
					'cust_counseled_risk_headache', 'cust_counseled_risk_anaphylaxis', 'cust_counseled_supply_disposal',
					'cust_counsel_90day_call', 'cust_counsel_storage_shipping', 'cust_nursing_services',
					'cust_ongoing_labs', 'cust_medication_list_reviewed', 'pt_participate_poc']
					sections: ['Nursing Services']
				'tepezza':
					fields: ['cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_history_physical_reviewed', 'cust_careplan_created',
					'medication_in_stock', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseling_ibd',
					'cust_counseling_hyperglycemia','cust_counseling_contraception', 'cust_educate_management_program_3wk',
					'cust_storage_tepezza', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_ongoing_labs',
					'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_counsel_initial_shipment','cust_counseled_additional', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['cust_storage','cust_sched_next_infusion', 'cust_educate_management_program', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyepti':
					fields: ['cust_storage','medication_in_stock', 'cust_counsel_therap_manage', 'cust_counsel_therap_manage_comm', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_supply_disposal',
					'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_additional', 'cust_labs_reviewed', 
					'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed',
					'cust_counsel_90day_call', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyvgart':
					fields: ['cust_storage','cust_vitals_reviewed', 'cust_allergies', 'cust_history_physical_reviewed', 'medication_in_stock',
					'cust_counseled_route', 'cust_counseled_medication_purpose',
					'cust_counseled_risk_headache', 'cust_counseled_risk_anaphylaxis', 'cust_counseled_supply_disposal',
					'cust_counsel_90day_call', 'cust_counsel_storage_shipping', 'cust_nursing_services',
					'cust_ongoing_labs', 'cust_medication_list_reviewed', 'pt_participate_poc']
					sections: ['Nursing Services']
				'tepezza':
					fields: ['cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_history_physical_reviewed', 'cust_careplan_created',
					'medication_in_stock', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseling_ibd',
					'cust_counseling_hyperglycemia','cust_counseling_contraception', 'cust_educate_management_program_3wk',
					'cust_storage_tepezza', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_ongoing_labs',
					'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_counsel_initial_shipment','cust_counseled_additional', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['cust_storage','cust_sched_next_infusion', 'cust_educate_management_program', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyepti':
					fields: ['cust_storage','medication_in_stock', 'cust_counsel_therap_manage', 'cust_counsel_therap_manage_comm', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_supply_disposal',
					'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_counseled_additional', 'cust_labs_reviewed', 
					'cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_allergies', 'cust_history_physical_reviewed',
					'cust_counsel_90day_call', 'cust_ongoing_labs', 'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_careplan_created', 'pt_participate_poc']
					sections: ['Nursing Services']
				'vyvgart':
					fields: ['cust_storage','cust_vitals_reviewed', 'cust_allergies', 'cust_history_physical_reviewed', 'medication_in_stock',
					'cust_counseled_route', 'cust_counseled_medication_purpose',
					'cust_counseled_risk_headache', 'cust_counseled_risk_anaphylaxis', 'cust_counseled_supply_disposal',
					'cust_counsel_90day_call', 'cust_counsel_storage_shipping', 'cust_nursing_services',
					'cust_ongoing_labs', 'cust_medication_list_reviewed', 'pt_participate_poc']
					sections: ['Nursing Services']
				'tepezza':
					fields: ['cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_history_physical_reviewed', 'cust_careplan_created',
					'medication_in_stock', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseling_ibd',
					'cust_counseling_hyperglycemia','cust_counseling_contraception', 'cust_educate_management_program_3wk',
					'cust_storage_tepezza', 'cust_counsel_storage_shipping', 'cust_nursing_services', 'cust_ongoing_labs',
					'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_counsel_initial_shipment','cust_counseled_additional', 'pt_participate_poc']
					sections: ['Nursing Services']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'assessment_{brand_1}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
				'assessment_tepezza': {}
				'assessment_vyepti': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'assessment_{brand_2}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
				'assessment_tepezza': {}
				'assessment_vyepti': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'assessment_{brand_3}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
				'assessment_tepezza': {}
				'assessment_vyepti': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'assessment_{brand_4}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
				'assessment_tepezza': {}
				'assessment_vyepti': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'assessment_{brand_5}'
			sourcefilter:
				'assessment_cimzia': {}
				'assessment_dupixent': {}
				'assessment_enbrel': {}
				'assessment_humira': {}
				'assessment_krystexxa': {}
				'assessment_lemtrada': {}
				'assessment_ocrevus': {}
				'assessment_orencia': {}
				'assessment_radicava': {}
				'assessment_remicade': {}
				'assessment_rituxan': {}
				'assessment_simponi': {}
				'assessment_simponiaria': {}
				'assessment_soliris': {}
				'assessment_stelara': {}
				'assessment_tysabri': {}
				'assessment_vyvgart': {}
				'assessment_vancomycin': {}
				'assessment_methyl': {}
				'assessment_tepezza': {}
				'assessment_vyepti': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'asthma':
					fields: ['parasitic_infection_history', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_disposal_medication', 'cust_childbearing_age', 'cust_counsel_initial_shipment_signed', 'cust_questions_prior_dipense', 'cust_storage', 'cust_counsel_storage_shipping', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_counseled_additional', 'medication_list']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'asthma':
					fields: ['parasitic_infection_history', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_disposal_medication', 'cust_childbearing_age', 'cust_counsel_initial_shipment_signed', 'cust_questions_prior_dipense', 'cust_storage', 'cust_counsel_storage_shipping', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_counseled_additional', 'medication_list']

		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'asthma':
					fields: ['parasitic_infection_history', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_disposal_medication', 'cust_childbearing_age', 'cust_counsel_initial_shipment_signed', 'cust_questions_prior_dipense', 'cust_storage', 'cust_counsel_storage_shipping', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_counseled_additional', 'medication_list']

		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'asthma':
					fields: ['parasitic_infection_history', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_disposal_medication', 'cust_childbearing_age', 'cust_counsel_initial_shipment_signed', 'cust_questions_prior_dipense', 'cust_storage', 'cust_counsel_storage_shipping', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_counseled_additional', 'medication_list']

		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'asthma':
					fields: ['parasitic_infection_history', 'cust_counseled_route', 'cust_counsel_hypersensitivity', 'cust_counseled_disposal_medication', 'cust_childbearing_age', 'cust_counsel_initial_shipment_signed', 'cust_questions_prior_dipense', 'cust_storage', 'cust_counsel_storage_shipping', 'cust_therapy_duplication', 'cust_careplan_created', 'cust_counseled_additional', 'medication_list']

		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	cust_labs_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_no_labs_dispense_anyway']
		view:
			columns: 2
			control: 'radio'
			label: 'Laboratory results reviewed?'

	cust_factor_labs_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_no_labs_dispense_anyway']
				'Yes':
					fields: ['cust_factor_labs']
		view:
			columns: 2
			control: 'radio'
			label: 'Laboratory results reviewed?'

	cust_factor_labs:
		model:
			source: ['Factor VIII', 'Factor IX', 'Other']
			if:
				'*':
					fields: ['cust_factor_trough', 'cust_factor_trough_comment', 'cust_factor_peak', 'cust_factor_peak_comment']
		view:
			columns: 2
			control: 'radio'
			label: "Factor Labs"

	cust_factor_trough:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -2
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Factor trough'

	cust_factor_trough_comment:
		view:
			columns: 2
			label: 'Factor trough comment'

	cust_factor_peak:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: -2
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'Factor peak'

	cust_factor_peak_comment:
		view:
			columns: 2
			label: 'Factor peak comment'

	cust_factor_halflife:
		model:
			type: 'int'
		view:
			columns: -2
			label: 'Factor half-life (hours)'

	cust_no_labs_dispense_anyway:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'If no labs, is it acceptable to dispense medication anyways?'

	cust_labs_comment:
		view:
			control: 'area'
			label: 'Labs Comment'

	cust_vitals_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Vital signs reviewed?'

	cust_diagnosis_accepted:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_indicated_on_label']
		view:
			columns: 2
			control: 'radio'
			label: 'Diagnosis acceptable and entered?'

	cust_indicated_on_label:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_diagnosis_appropriate']
		view:
			columns: 2
			control: 'radio'
			label: 'Diagnosis indicated on PI for medication?'

	cust_diagnosis_appropriate:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'If diagnosis off label, clinically appropriate?'

	orders_appropriate:
		model:
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Ancillary and ADR orders appropriate?'

	cust_allergies:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Allergies double checked?'

	cust_history_physical_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'History and Physical reviewed?'

	medication_in_stock:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['medication_ship_details']
		view:
			columns: 2
			control: 'radio'
			label: 'Is all medication and supplies in stock?'

	medication_ship_details:
		model:
			required: true
		view:
			columns: 2
			label: 'How soon could we ship?'


	parasitic_infection_history:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'History of currently being treated for a parasitic infection'

	cust_allergies_rec:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Allergies reconciled with patient?'

	cust_allergies_rec_comment:
		view:
			control: 'area'
			label: 'Allergies reconciliation comment'

	cust_chronic_cond_rec:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_chronic_condition']
		view:
			columns: 2
			control: 'radio'
			label: 'Chronic conditions reconciled with patient?'

	cust_chronic_condition:
		model:
			multi: true
			source: {
					alchol_ab: 'Alcohol Abuse', 
					alzheimer: "Alzheimer's Disease/Dementia", 
					arthritis: 'Arthritis', 
					asthma: 'Asthma', 
					atrial: 'Atrial fibrillation', 
					autism: 'Autism spectrum disorders', 
					cancer: 'Cancer', 
					kidney_disease: 'Chronic Kidney Disease', 
					copd: 'COPD', 
					depression: 'Depression', 
					diabetes: 'Diabetes', 
					drug_abuse: 'Drug Abuse/Substance Abuse', 
					heart_failure: 'Heart Failure', 
					hepatitis: 'Hepatitis (Chronic Viral B&C)', 
					hiv_aids: 'HIV/AIDS', 
					hyperlipidemia: 'Hyperlipidemia', 
					hypertension: 'Hypertension', 
					isc_heart_dise: 'Ischemic Heart Disease', 
					osteoporosis: 'Osteoporosis',
					schizophrenia: 'Schizophrenia/Other Psychotic Disorder', 
					stroke: 'Stroke'
			}
		view:
			control: 'checkbox'
			label: 'Chronic conditions'

	cust_chronic_cond_rec_comment:
		view:
			control: 'area'
			label: 'Chronic conditions reconciliation comment?'

	cust_chronic_dx:
		model:
			source: 'patient_diagnosis'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			label: 'Diagnosis'

	cust_med_rec:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			note: '(prescription, OTC, and herbal/dietary supplements)'
			label: 'Medications reconciled with patient?'

	cust_med_rec_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Medication reconciliation comment'

	has_ems:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Does patient have access to EMS (w/in 15 minutes)?"

	drugs:
		model:
			max: 3
			multi: true
			source: ['Alcohol', 'Recreational Drug Use', 'Nicotine']
			if:
				'Nicotine':
					fields: ['cessation_opt_in']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'History Of:'

	cessation_opt_in:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					note: 'We want to make you aware our nursing staff is equipped with personal protective gear including a mask and gown they may wear during your infusion if you chose to continue to smoke. We still do encourage all of our patients to let us partner with you to quit smoking. If at any point you change your mind please let us know, and know we are here for you'
				'Yes':
					fields: ['cessation_comment']
		view:
			control: 'radio'
			label: 'At Heritage Biologics we respect and honor you as a patient, and thank you for allowing us into your home to take care of you. To maximize health outcomes, we offer smoking cessation to all of our patients who smoke. While we want you to feel as comfortable as possible in your home, we ask during your infusion to refrain from smoking. We appreciate your understanding and willingness to help make this the best experience possible for both you and our staff. Does this sound like something you would be willing to do?'

	cessation_comment:
		view:
			label: 'Cessation program comment'

	medical_history:
		model:
			prefill: ['patient']
		view:
			control: 'area'
			label: 'Medical History'

	subform_previous_treatment:
		model:
			source: 'previous_treatment'
			multi: true
			type: 'subform'
		view:
			label: 'Previous Treatment Information Details'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['brand_name_id', 'duration', 'duration_type', 'response']
				label: ['Drug', 'Duration', 'Duration Type', 'Response']
				width: [35, 15, 25, 25]

	cust_counsel_therap_manage:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Therapeutic Management Program explained to patient?'

	cust_counsel_therap_manage_comm:
		view:
			columns: 2
			label: 'Therapeutic Management Comment'

	cust_tmp_explained:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Therapeutic Management Program explained to patient?'

	cust_tmp_explained_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Therapeutic Management Program Comment:'

	cust_counseled_route:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on method of administration?'

	cust_counseled_medication_purpose:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on what medication does for their body?'

	cust_why_not_counseled:
		view:
			label: 'Why was the patient not counseled?'

	cust_counsel_hypersensitivity:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on possible hypersensitivity reactions?'

	cust_counseled_disposal_medication:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled on proper disposal of medication?'

	cust_childbearing_age:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pregnancy_registry']
		view:
			columns: 2
			control: 'radio'
			label: 'Is the patient of childbearing age?'

	cust_pregnancy_registry:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'This medication is unknown if it will harm your unborn baby but there is a pregnancy registry you can contact. This registry’s purpose is to collect information about your health and your baby’s health. We can provide this information to you if requested. Did you inform the patient about the Pregnancy Registry Information'

	cust_counseled_side_effects:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on risk of post infusion headache and nausea?'

	cust_counseling_ibd:
		model:
			max: 3
			source: ['NA', 'No', 'Yes']
		view:
			control: 'radio'
			label: 'Counseled patient on exacerbation of preexisting inflammatory bowel disease (IBD)?'

	cust_counseling_hyperglycemia:
		model:
			max: 3
			source: ['NA', 'No', 'Yes']
		view:
			control: 'radio'
			note: 'SOB, Nausea, Vomiting, Confusion, Weakness, Abdominal pain, etc.'
			label: 'Counseled patient on elevated blood glucose and symptoms of hyperglycemia?'

	cust_counseling_contraception:
		model:
			max: 3
			source: ['NA', 'No', 'Yes']
		view:
			control: 'radio'
			label: 'Counseled about the need for appropriate forms of contraception prior to initiation, during treatment, and for 6 months following the last dose of Tepezza?'

	cust_counseled_thrombotic_risk:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on risk of thrombotic event?'

	cust_counseled_subq_skin_reaction:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'If SubQ, counseled patient on possible skin reactions?'

	cust_counseled_supply_disposal:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled on proper disposal of supplies/sharps?'

	cust_bio_waste_diposal:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Do you have access to a bio hazard waste disposal unit?'

	cust_counseled_treatment_logs:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled on current treatment records/logs?'

	cust_counseled_compliance:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on importance of compliance?'

	cust_counseled_importantance_treating_bleed:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_why_not_counseled']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on importance of treating bleeds?'

	cust_counseled_risk_headache:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on risk of infections and post infusion headache?'

	cust_counseled_risk_anaphylaxis:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Counseled patient on risk of anaphylaxis?'

	cust_therapy_goals:
		view:
			control: 'area'
			label: 'What are your goals for this therapy?'

	cust_healthcare_goals:
		view:
			control: 'area'
			label: 'What is your biggest healthcare goal or need?'
			columns: 2

	cust_counsel_shipment_signature:
		model:
			required: true
			source: ['Email', 'Text', 'Paper']
		view:
			control: 'radio'
			label: 'Counsel on paper work in each shipment requiring signed and returned via electronic signature link. Discuss delivery method of signature link.'
			columns: 2

	cust_counsel_initial_shipment_signed:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Counsel on paperwork in initial shipment requiring signed.'
			columns: 2

	cust_questions_prior_dipense:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Prior to each dispense, we will call you to ask some questions to assess your response to therapy and document information for your physician. This is part of our Therapeutic Management Program that we offer to everyone, does this sound okay to you?'
			columns: 2

	cust_counsel_initial_shipment:
		model:
			required: true
			source: ['No', 'Yes', 'Paper']
		view:
			control: 'radio'
			label: 'Counsel on paperwork in initial shipment requiring signed and returned via prepaid envelope?'
			columns: 2

	cust_counsel_90day_call:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Every 90 days we will call you to ask some questions to assess your response to therapy and document information for your physician. This is part of our Therapeutic Management Program that we offer to everyone, does this sound okay to you?'
			columns: 2

	cust_educate_management_program:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Every month we will call you to ask some questions to assess your response to therapy and document information for your physician. This is part of our Therapeutic Management Program that we offer to everyone, does this sound okay to you?'
			columns: 2

	cust_educate_management_program_3wk:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Every 3 weeks we will call you to ask some questions to assess your response to therapy and document information for your physician. This is part of our Therapeutic Management Program that we offer to everyone, does this sound okay to you?'
			columns: 2

	confirmation_personnel_changes:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'It is also important to update us to any other major health care changes, pregnancy, medication/herbal/supplement changes, hospital/Emergency Room visits, changes in insurance, or home address. Do you have any questions about this?'
			columns: 2

	understands_on_call_program:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					note: 'We want to make you aware our nursing staff is equipped with personal protective gear including a mask and gown they may wear during your infusion if you chose to continue to smoke. We still do encourage all of our patients to let us partner with you to quit smoking. If at any point you change your mind please let us know, and know we are here for you'
		view:
			control: 'radio'
			label: 'Confirm patient has our toll-free number and understands our on-call program?'
			columns: 2

	cust_storage:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Medication storage discussed with patient?"

	cust_storage_tepezza:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Medication storage discussed with patient (refrigerated)? '

	cust_storage_comment:
		view:
			columns: 2
			label: "Medication storage comment"

	cust_counsel_storage_shipping:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Counsel on medication shipping in cooler with ice packs to maintain temperature in all weather?'
			columns: 2

	confirm_shipping:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Confirm shipping address?'

	cust_storage_understands:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'The medication is to be kept at room temperature and kept in the overwrap until it is intended for use.  There is a pink oxygen indicator inside.  Please contact us if it is ever blue/purple, not pink.  Do you understand how it should be stored?'
			columns: 2

	delivery_date:
		model:
			type: 'date'
			required: false
		view:
			label: 'Drug delivery date'
			columns: 2

	delivery_delay:
		model:
			required: false
			default: 'No'
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['delivery_delay_reason']
		view:
			control: 'radio'
			label: 'Will medication be dispensed at time of assessment?'

	delivery_delay_reason:
		model:
			required: false
		view:
			control: 'area'
			label: 'Delay Reason'

	cust_counsel_fedex_overnight:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'or see if preference for courier, if local'
			label: 'Counsel on FedEx/UPS overnight shipping?'

	cust_counsel_fedex_overnight_details:
		view:
			control: 'area'
			label: 'Overnight shipping details:'

	cust_delivery_method:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Delivery method discussed with patient?"

	cust_delivery_method_comment:
		view:
			label: "Delivery method comment"

	cust_delivery_care_plan:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_delivery_change_location']
		view:
			control: 'radio'
			label: "It is our responsibility to educate each patient that we need a signature upon delivery to ensure our medications are successfully delivered to each patient. Due to the high dollar amount and nature of the medications we dispense, it is our policy to ensure each patient is available to accept their deliveries and sign for the package. Do you accept this plan of care for your deliveries?"

	cust_delivery_change_location:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_delivery_no_signature', 'cust_sent_waiver']
		view:
			control: 'radio'
			label: "If we were to change the delivery location to another address such as work, family members home, etc., would that be better for you to adhere to our recommendation of signature required deliveries? We want to do whatever we can to accommodate your needs and protect your medication"

	cust_delivery_no_signature:
		model:
			multi: true
			source: ['Ok, we will make note of this and will send your delivery without signature required. Thank you for your consideration of this. Please note once a package is delivered, it is considered to be in your possession and you will be responsible for proper storage. Once delivered your shipment will be billed and is no longer considered property of our pharmacy, and cannot be returned. We will be sending a waiver for you to review, sign and return to opt-out of the signature requirement. We will provide an envelope for return and will need this before your next delivery goes out']
		view:
			control: 'checkbox'
			label: "Patient Notification"
			class: 'list'
			readonly: true

	cust_sent_waiver:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: "Did you notify Patient services to send the waiver?"

	cust_supplies_shipping:
		view:
			control: 'area'
			label: 'Supplies and Shipping Information'

	pharm_questions:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pharm_questions_details']
		view:
			columns: 2
			control: 'radio'
			label: 'Do you have any questions?'

	pharm_questions_details:
		view:
			columns: 2
			control: 'area'
			label: 'Question details'

	cust_nursing_services:
		model:
			source: ['HB', 'HHA', 'Contracted', 'None']
			if:
				'Contracted':
					fields: ['cust_agency_id']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Nursing Services'

	cust_agency_id:
		model:
			required: true
			type: 'int'
			multi: false
			source: 'nurse_agency'
		view:
			columns: 2
			label: 'Nursing Agency'

	cust_sched_next_infusion:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_sched_next_infusion_dt']
		view:
			columns: -2
			control: 'radio'
			label: 'Have you scheduled your next infusion?'

	cust_sched_next_infusion_dt:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Date'

	cust_scheduled_delivery:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Schedule delivery and next call in the calendar?'

	cust_notify_technicians:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Notify technicians of any product that needs to be ordered before delivery?'

	cust_admissions_complete:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Admissions packet complete and acceptable?'

	medication_list:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			control: 'radio'
			label: 'Medication list entered, reviewed with patient, printed for admission package AND DUR note complete?'

	cust_ongoing_labs:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['cust_ongoing_labs_source']
		view:
			columns: 2
			control: 'radio'
			label: 'Ongoing lab orders?'

	cust_ongoing_labs_source:
		model:
			source: ['HB', 'Outside Agency']
		view:
			columns: 2
			control: 'radio'
			label: 'Who draws the labs?'

	cust_medication_list_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			control: 'radio'
			label: 'Medications list entered, reviewed with patient, printed for admissions packet, AND DUR note complete?'

	cust_therapy_duplication:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Any duplication of therapy?'

	cust_compliance_barriers:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['cust_compliance_barriers_details', 'cust_identified_issue_in_careplan']
		view:
			columns: 2
			control: 'radio'
			label: 'Any barriers to compliance?'

	cust_compliance_barriers_details:
		view:
			columns: 2
			control: 'area'
			label: 'Barrier to compliance'

	cust_identified_issue_in_careplan:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Identified issues and specific interventions in care plan?'

	cust_careplan_created:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Care plan created?'

	pt_participate_poc:
		model:
			default: 'Yes'
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient or caregiver participate in the care plan?'

	cust_factor_consignment:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Consignment dose ordered for inventory?'

	cust_factor_consignment_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Consignment dose comment'

	cust_disease_material:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: -2
			control: 'radio'
			label: 'Disease State Education Materials provided?'
	
	cust_disease_material_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Disease State Education Materials Comment'

	cust_medication_material:
		model:
			max: 3
			required: false
			source: ['No', 'Yes', 'NA']
		view:
			columns: -2
			control: 'radio'
			label: 'Medication Education Materials provided?'

	cust_counseled_additional:
		view:
			label: 'Any other additional counseling points you gave the patient?'

	subform_bleed_events:
		model:
			access:
				read: ['patient']
			source: 'factor_bleed'
			multi: true
			type: 'subform'
		view:
			label: 'Factor Bleed Events Log'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['cust_type', 'cust_site', 'duration_bleed','severity', 'cust_comment']
				label: ['Bleed Type', 'Site of Bleed', 'Dur (days)', 'Severity', 'Comments']
				width: [20, 20, 15, 20, 25]


	factor_propy_and_ondemand:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['factor_propy_doses', 'factor_propy_days', 'factor_propy_days_corrected']
				'No':
					fields: ['factor_propy_only']
		view:
			columns: -2
			control: 'radio'
			label: 'Is this patient on Prophylaxis and On Demand?'

	factor_propy_doses:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'How many doses are being used for Prophylaxis?'

	factor_propy_days:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'How many days are covered with Prophylaxis?'

	factor_propy_days_corrected:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'What is the corrected day supply?'

	factor_propy_only:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['factor_propy_doses', 'factor_propy_days']
				'No':
					fields: ['factor_ondemand_only']
		view:
			columns: 2
			control: 'radio'
			label: 'Is this Prophylaxis only?'

	factor_ondemand_only:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Is this On-Demand?'

# Parent form overrides
	admin_location:
		model:
			required: false
			if:
				'*':
					fields: ['reviewed_by']

	counseling_performed:
		model:
			required: false

	pharmacist_signature:
		model:
			required: false
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'payor']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']
	sections_group: [

			'Associated Records':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				fields: ['order_id', 'order_item_id',
				'therapy_1', 'therapy_2', 'therapy_3',
				'therapy_4', 'therapy_5', 'brand_1',
				'brand_2', 'brand_3', 'brand_4',
				'brand_5', 'disease_1', 'disease_2', 'disease_3',
				'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3',
				'clinical_4', 'clinical_5']

			'H&P':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				fields: ['measurement_log']

			'Primary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Pre-Assessment':
				hide_header: true
				indent: false
				tab: 'Pre-Assessment'
				area: 'preassessment'
				fields: ['subform_brand_5']

			'Previous Treatment Information':
				indent: false
				tab: 'Pre-Assessment'
				fields: ['subform_previous_treatment']

			'Labs':
				indent: false
				tab: 'Pre-Assessment'
				fields: ['cust_labs_reviewed', 'cust_factor_labs_reviewed', 'cust_factor_labs',
				'cust_factor_trough', 'cust_factor_trough_comment', 'cust_factor_peak',
				'cust_factor_peak_comment', 'cust_factor_halflife', 'cust_no_labs_dispense_anyway']
			'Compliance':
				indent: false
				tab: 'Compliance'
				fields: ['cust_vitals_reviewed', 'cust_diagnosis_accepted', 'cust_indicated_on_label',
				'cust_diagnosis_appropriate', 'orders_appropriate',
				'cust_allergies', 'cust_history_physical_reviewed', 'medication_in_stock', 'medication_ship_details']
			'Health Risk Assessment':
				indent: false
				tab: 'Compliance'
				fields: ['parasitic_infection_history', 'cust_allergies_rec', 'cust_allergies_rec_comment',
				'cust_chronic_cond_rec', 'cust_chronic_condition', 'cust_chronic_cond_rec_comment',
				'cust_med_rec', 'cust_chronic_dx', 'cust_med_rec_comment',
				'has_ems', 'drugs', 'cessation_opt_in', 'cessation_comment', 'medical_history']

			'Primary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_therapy_5']

			'Primary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Administration Assessment':
				hide_header: true
				indent: false
				tab: 'Admin'
				area: 'admin'
				fields: ['subform_brand_5']

			'Primary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Patient Assessment':
				hide_header: true
				indent: false
				tab: 'Counseling'
				fields: ['subform_brand_5']

			'Patient Counseling':
				indent: false
				tab: 'Counseling'
				area: 'consuling'
				fields: ['cust_counsel_therap_manage', 'cust_counsel_therap_manage_comm',
				'cust_tmp_explained', 'cust_tmp_explained_comment', 'cust_counseled_route',
				'cust_counseled_medication_purpose', 'cust_why_not_counseled',
				'cust_counsel_hypersensitivity', 'cust_counseled_disposal_medication', 'cust_childbearing_age',
				'cust_pregnancy_registry', 'cust_counseled_side_effects',
				'cust_counseling_ibd', 'cust_counseling_hyperglycemia', 'cust_counseling_contraception',
				'cust_counseled_thrombotic_risk','cust_counseled_subq_skin_reaction', 'cust_counseled_supply_disposal',
				'cust_bio_waste_diposal', 'cust_counseled_treatment_logs', 'cust_counseled_compliance',
				'cust_counseled_importantance_treating_bleed', 'cust_counseled_risk_headache',
				'cust_counseled_risk_anaphylaxis', 'cust_therapy_goals', 'cust_healthcare_goals',
				'cust_counsel_shipment_signature', 'cust_counsel_initial_shipment_signed',
				'cust_questions_prior_dipense','cust_counsel_initial_shipment',
				'cust_counsel_90day_call', 'cust_educate_management_program',
				'cust_educate_management_program_3wk', 'confirmation_personnel_changes',
				'understands_on_call_program']

			'Therapeutic Management Program Enrollment':
				indent: false
				tab: 'Assessment'
				fields: ['subform_pmp']

			'Supplies And Shipping':
				indent: false
				tab: 'Assessment'
				fields: ['cust_storage', 'cust_storage_tepezza', 'cust_storage_comment',
				'cust_counsel_storage_shipping', 'confirm_shipping', 'cust_storage_understands',
				'delivery_date', 'delivery_delay', 'delivery_delay_reason',
				'cust_counsel_fedex_overnight', 'cust_counsel_fedex_overnight_details',
				'cust_delivery_method', 'cust_delivery_method_comment', 'cust_delivery_care_plan',
				'cust_delivery_change_location', 'cust_delivery_no_signature', 'cust_sent_waiver',
				'cust_supplies_shipping', 'pharm_questions', 'pharm_questions_details']

			'Primary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				fields: ['subform_clinical_1'] # subform
			'Secondary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				area: 'admin'
				fields: ['subform_clinical_2'] # subform
			'Tertiary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				area: 'admin'
				fields: ['subform_clinical_3'] # subform
			'Quaternary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				area: 'admin'
				fields: ['subform_clinical_4'] # subform
			'Quinary Clinical Assessment':
				hide_header: true
				indent: false
				tab: 'Clinical'
				area: 'admin'
				fields: ['subform_clinical_5']

			'Factor Bleed Events Log':
				indent: false
				tab: 'Clinical'
				fields: ['subform_bleed_events']
			'Factor Day Supply':
				indent: false
				tab: 'Clinical'
				fields: ['factor_propy_and_ondemand', 'factor_propy_only', 'factor_ondemand_only', 'factor_propy_doses', 'factor_propy_days', 'factor_propy_days_corrected']

			'DUR - Medications':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_medications']

			'DUR - Allergies':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_allergies']

			'DUR - DD DA Interaction':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_interaction_btn']

			'DUR - Interaction':
				hide_header: true
				indent: false
				tab: 'DUR'
				fields: ['patient_interactions']

			'Primary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_therapy_1'] # subform
			'Secondary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_therapy_2'] # subform
			'Tertiary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_therapy_3'] # subform
			'Quaternary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_therapy_4'] # subform
			'Quinary Therapy Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_therapy_5'] # subform

			'Primary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_brand_1'] # subform
			'Secondary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_brand_2'] # subform
			'Tertiary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_brand_3'] # subform
			'Quaternary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_brand_4'] # subform
			'Quinary Drug Brand Post-Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				area: 'footer'
				fields: ['subform_brand_5']

			'Nursing Services':
				indent: false
				tab: 'Post Assessment'
				fields: ['cust_nursing_services', 'cust_agency_id']
			'Post Assessment':
				hide_header: true
				indent: false
				tab: 'Post Assessment'
				fields: ['contact_notes', 'cust_sched_next_infusion', 'cust_sched_next_infusion_dt',
				'cust_scheduled_delivery', 'cust_notify_technicians', 'cust_admissions_complete',
				'medication_list', 'cust_ongoing_labs', 'cust_ongoing_labs_source',
				'cust_medication_list_reviewed', 'cust_therapy_duplication', 'cust_compliance_barriers',
				'cust_compliance_barriers_details','cust_identified_issue_in_careplan',
				'cust_careplan_created', 'pt_participate_poc', 'cust_factor_consignment',
				'cust_factor_consignment_comment', 'cust_disease_material', 'cust_disease_material_comment',
				'cust_medication_material', 'cust_counseled_additional']
	]

view:
	block:
		update:
			except: ['admin', 'self']