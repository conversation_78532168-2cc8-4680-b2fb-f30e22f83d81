fields:

	segment_coupon:
		view:
			offscreen: true
			readonly: true

	segment_compounds:
		view:
			offscreen: true
			readonly: true

	segment_docs:
		view:
			readonly: true
			offscreen: true

	segment_facility:
		view:
			readonly: true
			offscreen: true

model:
	sections_group: [
		'NCPDP Transaction':
			hide_header: true
			sections: [
				'Header':
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['invoice_no', 'claim_no', 'transaction_code', 
							'hide_prescriber_in_e1', 'trans_show_comp','never_comp_seg',
							'status', 'inventory_id', 'site_id', 'patient_id', 'insurance_id', 'payer_id',
							'pt_rel_code', 'software_vendor_id', 'version_number',
							'date_of_service', 'bin_number', 'process_control_number', 'svc_prov_id_qualifier', 'svc_prov_id']
				'Totals':
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['expected', 'paid', 'copay', 'tax']
				'Patient':
					hide_header: true
					indent: false
					tab: 'Patient'
					fields: ['segment_patient']
				'Prescriber':
					hide_header: true
					indent: false
					tab: 'Prescriber'
					fields: ['segment_prescriber']
				'Insurance':
					hide_header: true
					indent: false
					tab: 'Insurance'
					fields: ['segment_insurance']
				'Claim':
					hide_header: true
					indent: false
					tab: 'Claim'
					fields: ['segment_claim']
				'Pricing':
					hide_header: true
					indent: false
					tab: 'Pricing'
					fields: ['segment_pricing']
				'Pharmacy':
					hide_header: true
					indent: false
					tab: 'Pharmacy'
					note: 'If required by payer, this would be the site PIC information'
					fields: ['segment_pharmacy']
				'COB':
					hide_header: true
					indent: false
					tab_toggle: true
					tab: 'COB'
					fields: ['segment_cob']
				'Responses':
					hide_header: true
					indent: false
					tab: 'Header'
					fields: ['responses']
				'Last Request - JSON':
					hide_header: true
					indent: false
					tab: 'Responses'
					fields: ['request_json_data']
				'Last Response - JSON':
					hide_header: true
					indent: false
					tab: 'Responses'
					fields: ['response_json_data']
				'Last Request - D0':
					hide_header: true
					indent: false
					tab: 'Responses'
					fields: ['request_d0_raw']
				'Last Response - D0':
					hide_header: true
					indent: false
					tab: 'Responses'
					fields: ['response_d0_raw']
				'Responses':
					hide_header: true
					indent: false
					tab: 'Responses'
					fields: ['responses']
				'Section Break':
					modal: true
					fields: []
				'Clinical':
					hide_header: true
					indent: false
					tab: 'Clinical'
					tab_toggle: true
					note: "Measure for Height/Weight Required if necessary when billing Medicare for a claim that includes a Certificate of Medical Necessity (CMN)."
					fields: ['segment_clinical']
				'Narrative':
					hide_header: true
					indent: false
					tab_toggle: true
					tab: 'Narrative'
					fields: ['segment_narrative']
				'DUR/PPS':
					hide_header: true
					indent: false
					tab_toggle: true
					tab: 'DUR/PPS'
					fields: ['segment_dur']
			]
	]
