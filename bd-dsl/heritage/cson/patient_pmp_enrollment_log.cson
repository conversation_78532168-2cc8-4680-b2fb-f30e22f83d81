fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	pmp_benefits:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Therapeutic Management Program explained to patient?'

	pmp_benefits_optout:
		model:
			max: 12
			source: ['Opt-Out', 'Opt-In']
			required: true
			default: 'Opt-In'
		view:
			columns: 2
			control: 'radio'
			label: 'Did the patient Opt into the Therapeutic Management Program?'

	cust_tmp_explained_comment:
		view:
			label: 'Therapeutic Management Program Comment:'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient-form']
	name: ['patient_id']
	indexes:
		many: [
			['patient_id']
		]
	sections:
		'Therapeutic Management Program Enrollment Log':
			fields: ['pmp_benefits', 'pmp_benefits_optout', 'cust_tmp_explained_comment']

view:
	comment: 'Patient > Therapeutic Management ProgramEnrollment Log'
	grid:
		fields: ['created_by', 'created_on', 'pmp_benefits', 'pmp_benefits_optout', 'cust_tmp_explained_comment']
		sort: ['-id']
	label: 'Therapeutic Management Program Enrollment Log'
	open: 'read'
