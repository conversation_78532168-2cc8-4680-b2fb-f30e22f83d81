fields:

	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	step:
		model:
			required: true
			source: ['PA', 'Pre-Determination', 'Medicare', 'IVIG Demonstration', 'Mfg Approval']
		view:
			control: 'radio'
			label: 'Step'
			columns: 3

	datetime_start:
		model:
			type: 'datetime'
		view:
			label: 'PA Submitted Date/Time'
			columns: 3

	datetime_end:
		model:
			type: 'datetime'
		view:
			label: 'PA Approval Received Date/Time'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: []
	name: ['patient_id', 'careplan_id']
	reportable: true
	sections:
		'Turn Around Time':
			fields: ['step', 'datetime_start', 'datetime_end']

view:
	validate: [
		{
			name: "DateOrderValidator"
			fields: [
				"datetime_start"
				"datetime_end"
			]
			error: "Start Date/Time cannot be after End Date/Time"
		}
	]
	comment: 'Careplan Referral > Turn Around Time'
	grid:
		fields: ['step', 'datetime_start', 'datetime_end']
		sort: ['-created_on']
	label: 'Turn Around Time'
