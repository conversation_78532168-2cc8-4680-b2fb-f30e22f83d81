fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	payer:
		view:
			label: 'Payer'
			columns: 3

	phone:
		view:
			format: 'us_phone'
			label: "Phone #"
			columns: 3

	time:
		model:
			type: 'time'
		view:
			label: 'Time call begin'
			columns: 3

	spoke_with:
		view:
			label: 'Spoke with:'
			columns: 3

	claim_no:
		view:
			label: 'Claim #'
			columns: 3

	date_rec:
		model:
			type: 'date'
		view:
			label: "Date Rec'd"
			columns: 3

	ref_no:
		view:
			label: 'Ref #'
			columns: 3

	status:
		view:
			label: 'Claim Status'
			columns: 3
model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Claim Status':
			hide_header: true
			indent: false
			fields: ['payer', 'phone', 'time', 'spoke_with',
					'claim_no', 'date_rec', 'ref_no', 'status']

view:
	comment: 'Patient > Billing Assessment Claim Status'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Claim Status'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']