fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	# Billing Audit – Prospective
	note:
		view:
			control: 'area'
			label: 'Note to biller'

	reviewed_present:
		model:
			default: 'Reviewed / Present'
		view:
			label: 'Note:'
			readonly: true
			columns: 3

	auth_required:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Auth Required?'
			columns: 3

	abn:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'ABN Required?'
			columns: 3

	dxs_pt:
		model:
			source: 'patient_diagnosis'
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			control: 'select'
			label: 'Patient Diagnosis'
			columns: 3

	pt_advised:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Patient advised of charge?'
			columns: 3

	aob:
		view:
			label: 'AOB'
			columns: 3

	pod:
		view:
			label: 'POD'
			columns: 3

	ptid_group:
		view:
			label: 'Pt ID/Group'
			columns: 3

	auth:
		view:
			label: 'Auth'
			columns: 3

	ordering_dr_pt:
		model:
			source: 'patient_prescriber'
			type: 'int'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			label: 'Ordering Dr (Pt)' 
			columns: 3

	dos:
		view:
			label: 'DOS'
			columns: 3

	drug_hcpc_qty:
		view:
			label: 'Drug / NDC, Qty'
			columns: 3

	pd_hcpc_qty:
		view:
			label: 'Supply HCPC, Qty'
			columns: 3

	matches_order:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Matches Order?'
			columns: 3

	expected_loa:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Payable/Expected?'
			columns: 3

	nursing:
		view:
			note: 'if applicable'
			label: 'Nursing'
			columns: 3

	modifiers:
		view:
			note: 'if applicable'
			label: 'Modifiers'
			columns: 3

	written_order:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'if applicable'
			label: 'Standard Written Order'
			columns: 3

	dt_matched:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Delivery Tickets matched to Invoices'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Billing Audit':
			hide_header: true
			indent: false
			fields: ['note', 'reviewed_present', 'auth_required',
			'abn', 'dxs_pt', 'pt_advised', 'aob',
			'pod', 'ptid_group', 'auth', 'ordering_dr_pt', 'dos',
			'drug_hcpc_qty', 'pd_hcpc_qty', 'matches_order',
			'expected_loa', 'nursing', 'modifiers', 'written_order', 'dt_matched']

view:
	comment: 'Patient > Billing Assessment Audit'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Audit'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']