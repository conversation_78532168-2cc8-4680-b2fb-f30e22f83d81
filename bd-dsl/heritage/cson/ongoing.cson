fields:
	requires_nursing:
		model:
			multi: false
			source: ['Yes']
			if:
				'Yes':
					sections: ['Nursing Notes', 'Nursing Services']
					fields: ['nursing_notes', 'cust_nursing_note_reviewed', 'cust_nursing_services']
		view:
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'Requires nursing?'
			readonly: true
			offscreen: true

	route_id:
		model:
			required: false
			source: 'list_route'
			sourceid: 'code'
			if:
				'IV':
					sections: ['Catheter Log']
					fields: ['catheter_log']
				'PEG':
					sections: ['Catheter Log']
					fields: ['catheter_log']
				'IVP':
					sections: ['Catheter Log']
					fields: ['catheter_log']
		view:
			readonly: true
			offscreen: true
			label: 'Primary Drug Route'

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['remain_suitable', 'cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'medicare_status', 'subform_bleed_events', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'day_supply_on_hand','cust_prec_instr', 'had_se', 'last_symptom_review_date',  'cust_express_scripts']
					sections: ['Factor Bleed Events Log Review','Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control', 'Factor Day Supply']
				'ig':
					fields: ['remain_suitable', 'cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date',  'cust_express_scripts']
					sections: ['Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['infection_control_logs', 'cust_changed_since_fill',
					'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Infection Control']
		view:
			label: 'Primary Therapy'
			offscreen: true
			readonly: true

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'medicare_status', 'subform_bleed_events', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'day_supply_on_hand','cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Factor Bleed Events Log Review','Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control', 'Factor Day Supply']
				'ig':
					fields: ['remain_suitable', 'cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date',  'cust_express_scripts']
					sections: ['Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['infection_control_logs', 'cust_changed_since_fill',
					'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Infection Control']
		view:
			label: 'Secondary Therapy'
			offscreen: true
			readonly: true

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'medicare_status', 'subform_bleed_events', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'day_supply_on_hand','cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Factor Bleed Events Log Review','Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control', 'Factor Day Supply']
				'ig':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['infection_control_logs', 'cust_changed_since_fill',
					'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'last_symptom_review_date','cust_express_scripts']
					sections: ['Patient Evaluation', 'Infection Control']
		view:
			label: 'Tertiary Therapy'
			offscreen: true
			readonly: true

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'medicare_status', 'subform_bleed_events', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'day_supply_on_hand','cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Factor Bleed Events Log Review','Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control', 'Factor Day Supply']
				'ig':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['infection_control_logs', 'cust_changed_since_fill',
					'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Infection Control']
		view:
			label: 'Quaternary Therapy'
			offscreen: true
			readonly: true

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'medicare_status', 'subform_bleed_events', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'day_supply_on_hand','cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Factor Bleed Events Log Review','Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control', 'Factor Day Supply']
				'ig':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs', 'latest_qol', 'cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Labs', 'Patient Labs Review', 'Latest QOL', 'Infection Control']
				'mono':
					fields: ['reviewed_by']
				'biologic':
					fields: ['reviewed_by']
				'*':
					fields: ['infection_control_logs', 'cust_changed_since_fill',
					'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Patient Evaluation', 'Infection Control']
		view:
			label: 'Quinary Therapy'
			offscreen: true
			readonly: true


	subform_therapy_1:
		model:
			source: 'ongoing_{therapy_1}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
				'ongoing_nursing': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'assessment_{therapy_2}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
				'ongoing_nursing': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'assessment_{therapy_3}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
				'ongoing_nursing': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'assessment_{therapy_4}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
				'ongoing_nursing': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'assessment_{therapy_5}'
			sourcefilter:
				'ongoing_factor': {}
				'ongoing_aat': {}
				'ongoing_ig': {}
				'ongoing_subqig': {}
				'ongoing_tnf': {}
				'ongoing_tpn': {}
				'ongoing_steroid': {}
				'ongoing_inotrope': {}
				'ongoing_chemotherapy': {}
				'ongoing_bisphosphonates': {}
				'ongoing_nursing': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['remain_suitable', 'last_symptom_review_date', 'cust_sched_next_infusion', 'cust_catheter_changes', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyepti':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs','cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log',  'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyvgart':
					fields: ['remain_suitable','last_symptom_review_date','cust_catheter_changes','cust_nursing_services', 'cust_add_next_callin',
					'cust_notify_technicians', 'cust_admissions_complete', 'cust_ongoing_labs', 'cust_compliance_barriers',
					'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'tepezza':
					fields: ['remain_suitable', 'cust_catheter_changes', 'cust_reviewed_qol',
					'infection_control_logs', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['remain_suitable', 'last_symptom_review_date', 'cust_sched_next_infusion', 'cust_catheter_changes', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyepti':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs','cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log',  'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyvgart':
					fields: ['remain_suitable','last_symptom_review_date','cust_catheter_changes','cust_nursing_services', 'cust_add_next_callin',
					'cust_notify_technicians', 'cust_admissions_complete', 'cust_ongoing_labs', 'cust_compliance_barriers',
					'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'tepezza':
					fields: ['remain_suitable', 'cust_catheter_changes', 'cust_reviewed_qol',
					'infection_control_logs', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['remain_suitable', 'last_symptom_review_date', 'cust_sched_next_infusion', 'cust_catheter_changes', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyepti':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs','cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log',  'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyvgart':
					fields: ['remain_suitable','last_symptom_review_date','cust_catheter_changes','cust_nursing_services', 'cust_add_next_callin',
					'cust_notify_technicians', 'cust_admissions_complete', 'cust_ongoing_labs', 'cust_compliance_barriers',
					'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log','Patient Evaluation', 'Labs', 'Patient Labs Review']
				'tepezza':
					fields: ['remain_suitable', 'cust_catheter_changes', 'cust_reviewed_qol',
					'infection_control_logs', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['remain_suitable', 'last_symptom_review_date', 'cust_sched_next_infusion', 'cust_catheter_changes', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyepti':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs','cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log',  'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyvgart':
					fields: ['remain_suitable','last_symptom_review_date','cust_catheter_changes','cust_nursing_services', 'cust_add_next_callin',
					'cust_notify_technicians', 'cust_admissions_complete', 'cust_ongoing_labs', 'cust_compliance_barriers',
					'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log','Patient Evaluation', 'Labs', 'Patient Labs Review']
				'tepezza':
					fields: ['remain_suitable', 'cust_catheter_changes', 'cust_reviewed_qol',
					'infection_control_logs', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'ocrevus':
					fields: ['remain_suitable', 'last_symptom_review_date', 'cust_sched_next_infusion', 'cust_catheter_changes', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyepti':
					fields: ['remain_suitable','cust_catheter_changes', 'infection_control_logs','cust_reviewed_qol', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log',  'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'vyvgart':
					fields: ['remain_suitable','last_symptom_review_date','cust_catheter_changes','cust_nursing_services', 'cust_add_next_callin',
					'cust_notify_technicians', 'cust_admissions_complete', 'cust_ongoing_labs', 'cust_compliance_barriers',
					'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
				'tepezza':
					fields: ['remain_suitable', 'cust_catheter_changes', 'cust_reviewed_qol',
					'infection_control_logs', 'cust_labs_reviewed', 'cust_changed_since_fill', 'cust_medication_list_reviewed', 'cust_careplan_updated', 'cust_additional_counseling', 'cust_prec_instr', 'had_se', 'last_symptom_review_date', 'cust_express_scripts']
					sections: ['Catheter Log', 'Patient Evaluation', 'Labs', 'Patient Labs Review']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'ongoing_{brand_1}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
				'ongoing_tepezza': {}
				'ongoing_vyepti': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'ongoing_{brand_2}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
				'ongoing_tepezza': {}
				'ongoing_vyepti': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'ongoing_{brand_3}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
				'ongoing_tepezza': {}
				'ongoing_vyepti': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'ongoing_{brand_4}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
				'ongoing_tepezza': {}
				'ongoing_vyepti': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'ongoing_{brand_5}'
			sourcefilter:
				'ongoing_cimzia': {}
				'ongoing_dupixent': {}
				'ongoing_enbrel': {}
				'ongoing_humira': {}
				'ongoing_krystexxa': {}
				'ongoing_lemtrada': {}
				'ongoing_ocrevus': {}
				'ongoing_orencia': {}
				'ongoing_radicava': {}
				'ongoing_remicade': {}
				'ongoing_rituxan': {}
				'ongoing_simponi': {}
				'ongoing_simponiaria': {}
				'ongoing_soliris': {}
				'ongoing_stelara': {}
				'ongoing_tysabri': {}
				'ongoing_vyvgart': {}
				'ongoing_vancomycin': {}
				'ongoing_methyl': {}
				'ongoing_tepezza': {}
				'ongoing_vyepti': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'

	subform_clinical_1:
		model:
			source: 'clinical_{clinical_1}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Primary Clinical Assessment'

	subform_clinical_2:
		model:
			source: 'clinical_{clinical_2}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Secondary Clinical Assessment'

	subform_clinical_3:
		model:
			source: 'clinical_{clinical_3}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Tertiary Clinical Assessment'

	subform_clinical_4:
		model:
			source: 'clinical_{clinical_4}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Quaternary Clinical Assessment'

	subform_clinical_5:
		model:
			source: 'clinical_{clinical_5}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Quinary Clinical Assessment'

	disease_1:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Disease'

	disease_2:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Disease'

	disease_3:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Disease'

	disease_4:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Disease'

	disease_5:
		model:
			source: 'list_dx_asmt'
			sourceid: 'code'
			if:
				'ms':
					sections: ['Active Symptoms Review - MS']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Disease'

	ms_relapse_symptoms_alert:
		model:
			multi: true
			source: ['When you are having a relapse, what symptoms do you experience?']
		view:
			class: 'list'
			label: 'MS Relapse Symptoms'
			control: 'checkbox'
			readonly: true

	ms_relapse_comments:
		view:
			label: 'Comments'
			control: 'area'

	factor_bleed_created_on:
		model:
			prefill: ['factor_bleed.created_by']
			if:
				'*':
					fields: ['needs_review']
		view:
			label: 'Reviewed by pharmacist?'
			offscreen: true
			readonly: true

	bleeds_for_review:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				review_by_pharmacist:
					'static': null
		view:
			embed:
				form: 'factor_bleed'
				selectable: false
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['cust_type', 'cust_site', 'duration_bleed','severity', 'cust_comment']
				label: ['Bleed Type', 'Site of Bleed', 'Dur (days)', 'Severity', 'Comments']
				width: [20, 20, 15, 20, 25]
			label: 'Bleed Logs For Review'
			readonly: true

	subform_bleed_events:
		model:
			access:
				read: ['patient']
			source: 'factor_bleed'
			multi: true
			type: 'subform'
		view:
			label: 'Factor Bleed Events Log'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['cust_type', 'cust_site', 'duration_bleed','severity', 'cust_comment']
				label: ['Bleed Type', 'Site of Bleed', 'Dur (days)', 'Severity', 'Comments']
				width: [20, 20, 15, 20, 25]

	lab_result_created_on:
		model:
			prefill: ['patient_labresult.created_by']
			if:
				'*':
					fields: ['needs_review_lab', 'labs_for_review']
					sections: ['Lab Results']
		view:
			label: 'Reviewed by pharmacist?'
			offscreen: true
			readonly: true

	labs_for_review:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				review_by_pharmacist:
					'static': null
		view:
			embed:
				form: 'patient_labresult'
				selectable: false
			grid:
				add: 'none'
				edit: true
				rank: 'none'
				fields: ['draw_date', 'receive_date', 'result', 'result_comments']
				label: ['Draw Dt', 'Receive Dt', 'Result', 'Result Comments']
				width: [20, 20, 20, 40]
			label: 'Lab Results For Review'

	missed_dose:
		model:
			max: 3
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					sections: ['Missed Medications']
					fields: ['missed_medications', 'missed_medications_doses', 'missed_medications_reason_list']
		view:
			columns: 2
			control: 'radio'
			label: 'Have you missed any doses of your medication?'

	missed_medications_doses:
		model:
			required: true
		view:
			columns: 2
			label: 'How many doses did you miss?'

	missed_medications_reason_list:
		model:
			multi: true
			source: ['Could not afford them', 'Forgot to take them', 'Forgot to pick up a refill', "Missed a doctor’s appointment", 'Other']
			if:
				'Other':
					fields: ['missed_medications_reason']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Reason missed'

	missed_medications_reason:
		model:
			required: true
		view:
			label: 'Reason Missed Other'
	needs_review:
		model:
			source: ['There are outstanding patient bleed logs that need reviewed']
		view:
			label: 'Alert'
			readonly: true
			control: 'checkbox'
			class: 'list'

	needs_review_lab:
		model:
			source: ['There are outstanding patient lab results that need reviewed']
		view:
			label: 'Alert'
			readonly: true
			control: 'checkbox'
			class: 'list'

	infection_control_logs:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				review_by_pharmacist:
					'static': null
		view:
			embed:
				form: 'intervention_disease'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				tooltip: ['comments']
				fields: ['created_on', 'created_by', 'type', 'outcome', 'disease_id']
				label: ['Created On', 'Created By', 'Type', 'Outcome', 'Infectious Disease']
				width: [20, 20, 15, 15, 30]
			label: 'Infection Control Logs For Review'

	cust_labs_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_no_labs_dispense_anyway']
		view:
			columns: 2
			control: 'radio'
			label: 'Laboratory results reviewed?'

	cust_no_labs_dispense_anyway:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'If no labs, is it acceptable to dispense medication anyways?'

	cust_nursing_note_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Nursing note reviewed from last infusion?'

	cust_express_scripts:
		model:
			prefill: ['ongoing']
			source: ['No', 'Yes']
			if:
				'Yes':
					note: '**Patient will be unable to opt out of signature upon delivery due to payor**'
					fields: ['cust_signature_reminder_2']
				'*':
					fields: ['cust_signature_reminder']
		view:
			columns: 2
			control: 'radio'
			label: 'Is this patient an Express Scripts patient?'

	cust_label_double_checked:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: "Label previewed in CAPS and double-checked by another pharmacist?"

	cust_medication_list_reviewed:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Medications list entered, reviewed with patient AND DUR note complete?'

	cust_careplan_updated:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Care plan updated?'

	cust_taking_per_rx:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'No':
					fields: ['cust_identified_issue_in_careplan']
		view:
			columns: 2
			control: 'radio'
			label: 'Are you taking your medications as prescribed?'

	cust_identified_issue_in_careplan:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Identified issues and specific interventions in care plan?'

	cust_reviewed_qol:
		model:
			source: ['No', 'Yes', 'N/A']
		view:
			columns: 2
			control: 'radio'
			label: 'Have you reviewed the most recent Quality Of Life?'

	latest_qol:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'clinical_sf12v2'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['created_on', 'created_by', 'pt_qol_particiates', 'contact_date']
				label: ['Created On', 'Created By', 'Particiates', 'Contact Date']
				width: [25, 25, 25, 25]
			label: 'Quality Of Life Survey'
			readonly: true

	cust_changed_since_fill:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['cust_changed_since_fill_details']
		view:
			columns: -2
			control: 'radio'
			label: 'Has anything changed since the last time this medication was filled?'

	cust_changed_since_fill_details:
		view:
			columns: 2
			label: 'Change details'

	cust_additional_counseling:
		view:
			columns: 2
			control: 'area'
			label: 'Any other additional counseling points you gave the patient?'

	cust_sched_next_infusion:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_sched_next_infusion_dt']
		view:
			columns: -2
			control: 'radio'
			label: 'Have you scheduled your next infusion?'

	cust_sched_next_infusion_dt:
		model:
			type: 'date'
		view:
			columns: 2
			label: 'Date'

	cust_add_next_callin:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Schedule delivery and next call in calendar?'

	cust_supplies_shipping:
		view:
			columns: 2
			control: 'area'
			label: 'Supplies and Shipping Information'

	cust_verified_shipping:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Verified Shipping Address'

	cust_signature_reminder_2:
		model:
			source: ['No', 'Yes']
			prefill: ['ongoing']
			required: true
		view:
			control: 'radio'
			label: 'As a reminder, we require all shipments to be sent with signature upon delivery. Will you or someone else be available to sign for this shipment?'

	cust_signature_reminder:
		model:
			source: ['No', 'Yes']
			prefill: ['ongoing']
			required: true
			if:
				'No':
					fields: ['cust_waiver_on_file']
		view:
			control: 'radio'
			label: 'As a reminder, we require all shipments to be sent with signature upon delivery. Will you or someone else be available to sign for this shipment?'

	cust_waiver_on_file:
		model:
			prefill: ['ongoing']
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_delivery_change_location']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient have a waiver on file?'

	cust_delivery_change_location:
		model:
			prefill: ['ongoing']
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['cust_delivery_no_signature', 'cust_sent_waiver']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'If we were to change the delivery to another address, such as work, family members home, etc, would that be better for you to adhere to our signature required deliveries? We want to do whatever we can to accommodate your needs and protect your medication'

	cust_delivery_no_signature:
		model:
			multi: true
			source: ['Ok, we will make note of this and will send your delivery without signature required. Thank you for your consideration of this. Please note once a package is delivered, it is considered to be in your possession and you will be responsible for proper storage. Once delivered your shipment will be billed and is no longer considered property of our pharmacy, and cannot be returned. We will be sending a waiver for you to review, sign and return to opt-out of the signature requirement. We will provide an envelope for return and will need this before your next delivery goes out']
		view:
			control: 'checkbox'
			label: "Patient Notification"
			class: 'list'
			readonly: true

	delivery_delay:
		model:
			required: false
			default: 'No'
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['delivery_delay_reason']
		view:
			columns: 2
			control: 'radio'
			label: 'Will medication be dispensed at time of assessment?'

	delivery_delay_reason:
		model:
			default: 'Proactive Scheduling'
			required: false
			source: ['Proactive Scheduling', 'Drug not needed', 'Drug unavailable', 'Patient Choice']
		view:
			columns: 2
			control: 'radio'
			label: 'Delay Reason'

	cust_sent_waiver:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Did you notify Patient services to send the waiver?"

	therapy_outcomes:
		model:
			max: 3
			default: 'Yes'
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Therapy Outcomes Questionnaire']
		view:
			columns: 2
			control: 'radio'
			label: 'Will a therapy outcomes questionnaire be completed?'

	subform_outcomes:
		model:
			multi: false
			source: 'clinical_outcomes'
			type: 'subform'
		view:
			label: 'Therapy Outcomes'

	cust_care_plan_reviewed:
		model:
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Care plan reviewed for follow up items prior to call?'

	cust_care_plan_reviewed_comment:
		view:
			columns: 2
			control: 'area'
			label: 'Care plan reviewed comment'

	cust_catheter_changes:
		model:
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['cust_catheter_route', 'cust_catheter_changes_details', 'cust_catheter_discharged']
		view:
			columns: -2
			control: 'radio'
			label: 'Has there been any changes to the catheter?'

	cust_catheter_route:
		model:
			required: true
		view:
			columns: 2
			label: 'What is the route?'

	cust_catheter_changes_details:
		view:
			columns: 2
			label: 'Details'

	cust_catheter_discharged:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_catheter_newline']
		view:
			columns: -2
			control: 'radio'
			label: 'Will the old line be discharged?'

	cust_catheter_newline:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Will a new line be placed?'

	subform_adr:
		model:
			multi: true
			source: 'intervention_adr'
			type: 'subform'
		view:
			label: 'ADR Details'
			grid:
				add: 'flyout'
				edit: false
				hide_cardmenu: true
				fields: ['adr_date', 'adr_reported', 'adr_reaction', 'adr_severity']
				label: ['Date', 'ADR', 'Reaction', 'Severity']
				width: [20, 30, 30, 20]

	subform_dur:
		model:
			multi: false
			source: 'assessment_dur'
			type: 'subform'
		view:
			label: 'DUR Assessments'
			grid:
				add: 'inline'
				edit: true
				fields: ['created_on', 'created_by', 'completed', 'intervention']
				label: ['Created On', 'Created By', 'Comp?', 'Interv?']
				width: [35, 35, 15, 15]

	catheter_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_catheter'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['date_placed', 'date_discontinued', 'device', 'type_id']
				label: ['Date Placed', 'Date Discontinued', 'Device', 'Type']
				width: [15, 15, 35, 35]
			label: 'Catheter Log'
			readonly: true

	cust_prec_instr:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Discussed prescription instructions with patient?'

	cust_prec_instr_comment:
		view:
			columns: 2
			label: 'Prescription instructions comment'

	doses_on_hand:
		model:
			rounding: 1
			type: 'decimal'
		view:
			columns: -2
			label: 'Number of doses on hand'

	day_supply_on_hand:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			columns: 2
			label: 'Day supply on hand'

	medicare_status:
		model:
			source: ['No', 'Yes']
			prefill: ['ongoing']
			required: true
			if:
				'Yes':
					fields: ['medicare_doses_allowed', 'medicare_doses_dispensed']
					note: 'Medicare patients must only be dispensed the number of doses used from the previous refill.'
		view:
			columns: -2
			control: 'radio'
			label: 'Is the patient a medicare patient or on a medicare replacement plan?'

	medicare_doses_allowed:
		model:
			rounding: 1
			type: 'decimal'
			prefill: ['ongoing']
		view:
			columns: 2
			label: 'Number of doses allowed on prescription'

	medicare_doses_dispensed:
		model:
			rounding: 1
			type: 'decimal'
		view:
			columns: 2
			label: 'Number of doses to dispense this refill'

	factor_propy_and_ondemand:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['factor_propy_doses', 'factor_propy_days', 'factor_propy_days_corrected']
				'No':
					fields: ['factor_propy_only']
		view:
			columns: -2
			control: 'radio'
			label: 'Is this patient on Prophylaxis and On Demand?'

	factor_propy_doses:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'How many doses are being used for Prophylaxis?'

	factor_propy_days:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'How many days are covered with Prophylaxis?'

	factor_propy_days_corrected:
		model:
			required: true
			type: 'int'
		view:
			columns: 2
			label: 'What is the corrected day supply?'

	factor_propy_only:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['factor_propy_doses', 'factor_propy_days']
				'No':
					fields: ['factor_ondemand_only']
		view:
			columns: 2
			control: 'radio'
			label: 'Is this Prophylaxis only?'

	factor_ondemand_only:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Is this On-Demand?'

	# Care Plan Mirrored
	careplan:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				id:
					'dynamic': '{careplan_id}'
		view:
			embed:
				form: 'careplan'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				rank: 'none'
				fields: ['created_on', 'status_id', 'note', 'updated_on', 'reviewed_on']
				label: ['Created On', 'Status', 'Note', 'Updated On', 'Reviewed On']
				width: [20, 15, 25, 20, 20]
			label: 'Careplan'

	pt_participate_poc:
		model:
			prefill: ['ongoing', 'assessment']
			default: 'Yes'
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient participate or caregiver in the care plan?'

	had_se:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['had_se_type']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Have you experienced any side effects during or after your last infusion/injection?'

	had_se_type:
		model:
			source: ['ADR', 'Other']
			if:
				'ADR':
					sections: ['ADR Details']
				'Other':
					fields: ['had_se_details']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Type'

	had_se_details:
		model:
			required: true
		view:
			control: 'area'
			label: 'Side effects details'

	cust_nursing_services:
		model:
			prefill: ['ongoing', 'assessment']
			source: ['HB', 'HHA', 'Contracted', 'None']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Nursing Services'

	cust_notify_technicians:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Notify technicians of any product that needs to be ordered before delivery?'

	cust_admissions_complete:
		model:
			max: 3
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Admissions packet complete and acceptable?'

	cust_ongoing_labs:
		model:
			source: ['No', 'Yes', 'N/A']
			prefill: ['ongoing', 'assessment']
			if:
				'Yes':
					fields: ['cust_ongoing_labs_source']
		view:
			columns: 2
			control: 'radio'
			label: 'Ongoing lab orders?'

	cust_ongoing_labs_source:
		model:
			source: ['HB', 'Outside Agency']
			prefill: ['ongoing', 'assessment']
		view:
			columns: 2
			control: 'radio'
			label: 'Who draws the labs?'

	cust_compliance_barriers:
		model:
			source: ['No', 'Yes', 'N/A']
			if:
				'Yes':
					fields: ['cust_compliance_barriers_details', 'cust_identified_issue_in_careplan']
		view:
			columns: -2
			control: 'radio'
			label: 'Any barriers to compliance?'

	cust_compliance_barriers_details:
		view:
			columns: 2
			control: 'area'
			label: 'Barrier to compliance'

	symptoms_filter:
		model:
			multi: true
			source: 'list_symptom'
			if:
				'*':
					fields: ['symptoms', 'symptom_review_date']
					sections: ['Symptoms Review']
		view:
			label: 'Symptoms'
			readonly: true
			offscreen: true

	last_symptom_review_date:
		model:
			prefill: ['ongoing.symptom_review_date', 'assessment_nurse.symptom_review_date','ongoing.last_symptom_review_date']
			type: 'date'
		view:
			columns: -2
			label: 'Last Symptoms Review completed on:'
			readonly: true

	comp_symptom_review:
		model:
			max: 3
			min: 1
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					sections: ['Active Symptoms Review']
					fields: ['symptoms', 'disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5']
		view:
			columns: 2
			control: 'radio'
			label: 'Will you be completing a Symptoms Review today?'

	symptom_review_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Symptoms Review Date'
			template: '{{now}}'

	home_changes:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['home_changes_notes']
		view:
			columns: 2
			control: 'radio'
			label: 'Do you have any changes to your home environment?'

	home_changes_notes:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Details'

	insurance_changes:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['insurance_changes_notes']
		view:
			columns: -2
			control: 'radio'
			label: 'Do you have any changes to insurance coverage or ability to pay for your medication?'

	insurance_changes_notes:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Details'

	remain_appropriate:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Does the administration route remain appropriate?'

	remain_suitable:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes', 'NA']
			if:
				'No':
					fields: ['remain_suitable_notes']
		view:
			columns: -2
			control: 'radio'
			label: 'Does patient remain suitable for home infusion?'

	remain_suitable_notes:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Why?'

	eval_notes:
		view:
			control: 'area'
			label: 'Notes'

	embed_patient_address:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'patient_address'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['address_type', 'addresslabel']
				label: ['Type', 'Address']
				width: [20, 80]
			label: 'Address Review'
			readonly: true

	subform_intervention:
		model:
			source: 'patient_event'
			multi: true
			type: 'subform'
		view:
			label: 'Intervention'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['inter_type', 'physician_contacted', 'resolved', 'continue']
				label: ['Type(s)', 'Dr Contacted?', 'Resolved?', 'Cont Therapy?']
				width: [40, 20, 20, 20]

	# Base form overrides

	experiencing_pain:
		model:
			required: false 
		view:
			offscreen: true
			readonly: true

	pain_scale:
		model:
			required: false 
		view:
			offscreen: true
			readonly: true

	pain_management:
		model:
			required: false 
		view:
			offscreen: true
			readonly: true

	response_to_therapy:
		model:
			required: false 
		view:
			offscreen: true
			readonly: true

	response_to_therapy_rn:
		model:
			required: false 
		view:
			offscreen: true
			readonly: true

	new_events:
		model:
			if:
				'*':
					fields: ['reviewed_by']
		view:
			control: 'checkbox'
			label: 'Any new medical events?'
			offscreen: true
			readonly: true

	new_events_details:
		model:
			required: false 
		view:
			offscreen: true
			readonly: true
			label: 'New Events Details'

	counseling_performed:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Counseling Performed'

	pharmacist_signature:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
			label: 'Pharmacist Signature'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		assessment:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		ongoing:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		factor_bleed:
			link:
				patient_id: 'patient_id'
			filter:
				review_by_pharmacist: null
		patient_labresult:
			link:
				patient_id: 'patient_id'
			filter:
				review_by_pharmacist: null
			max: 'created_on'
		assessment_nurse:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician', 'payor']
		request:    []
		update:     []
		update_all: ['admin', 'pharm']
		write:      ['admin', 'pharm']

	sections_group: [
		'Associated Records':
			fields: ['order_id', 'order_item_id', 'requires_nursing',
			'route_id', 'therapy_1', 'therapy_2', 'therapy_3',
			'therapy_4', 'therapy_5', 'brand_1',
			'brand_2', 'brand_3', 'brand_4',
			'brand_5', 'disease_1', 'disease_2', 'disease_3',
			'disease_4', 'disease_5', 'clinical_1', 'clinical_2', 'clinical_3',
			'clinical_4', 'clinical_5', 'symptoms_filter']

		'Nursing Notes':
			hide_header: true
			indent: false
			tab: 'Nursing'
			fields: ['nursing_notes']

		'Nursing Services':
			indent: false
			tab: 'Nursing'
			fields: ['cust_nursing_services']

		'Symptoms Review':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			fields: ['last_symptom_review_date', 'comp_symptom_review']

		'Active Symptoms Review - MS':
			indent: false
			tab: 'Assessment'
			fields: ['ms_relapse_symptoms_alert', 'ms_relapse_comments']

		'Active Symptoms Review':
			indent: false
			tab: 'Pre-Assessment'
			fields: ['symptoms']

		'H&P':
			indent: false
			tab: 'Pre-Assessment'
			fields: ['measurement_log']

		'Primary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_therapy_5'] # subform

		'Primary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_brand_1'] # subform
		'Secondary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_brand_2'] # subform
		'Tertiary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_brand_3'] # subform
		'Quaternary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_brand_4'] # subform
		'Quinary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			area: 'preassessment'
			fields: ['subform_brand_5']

		'Latest QOL':
			indent: false
			tab: 'Pre-Assessment'
			fields: ['latest_qol'] # subform

		'Care Plan':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			fields: ['careplan']

		'Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Assessment'
			fields: ['cust_reviewed_qol', 'cust_care_plan_reviewed', 'cust_care_plan_reviewed_comment',
			'pt_participate_poc', 'cust_nursing_note_reviewed', 'cust_express_scripts']

		'Patient Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['cust_catheter_changes', 'cust_catheter_changes_details', 'cust_catheter_route', 'cust_catheter_discharged', 'cust_catheter_newline']

		'Catheter Log':
			indent: false
			tab: 'Assessment'
			fields: ['catheter_log']

		'Primary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_5'] # subform

		'Primary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_1'] # subform
		'Secondary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_2'] # subform
		'Tertiary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_3'] # subform
		'Quaternary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_4'] # subform
		'Quinary Drug Brand Pre-Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_5']

		'Therapy Outcomes':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['therapy_outcomes']
		'Therapy Outcomes Questionnaire':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_outcomes']
		'Therapy Outcomes Side Effects':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['had_se', 'had_se_type', 'had_se_details']

		'ADR Details':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_adr']

		'Primary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_1'] # subform
		'Secondary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_2'] # subform
		'Tertiary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_3'] # subform
		'Quaternary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_4'] # subform
		'Quinary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Clinical'
			fields: ['subform_clinical_5']

		'Factor Bleed Events Log Review':
			indent: false
			tab: 'Clinical'
			fields: ['factor_bleed_created_on', 'needs_review']
		'Factor Bleed Events For Review':
			indent: false
			tab: 'Clinical'
			fields: ['bleeds_for_review']
		'Factor Bleed Events Log':
			indent: false
			tab: 'Clinical'
			fields: ['subform_bleed_events']
		'Factor Day Supply':
			indent: false
			tab: 'Clinical'
			fields: ['factor_propy_and_ondemand', 'factor_propy_only', 'factor_ondemand_only', 'factor_propy_doses', 'factor_propy_days', 'factor_propy_days_corrected']
		'Patient Labs Review':
			indent: false
			tab: 'Clinical'
			fields: ['lab_result_created_on', 'needs_review_lab']
		'Lab Results':
			indent: false
			tab: 'Clinical'
			fields: ['labs_for_review']

		'Infection Control':
			indent: false
			tab: 'Clinical'
			fields: ['infection_control_logs']

		'Adherence':
			hide_header: true
			indent: false
			tab: 'Adherence'
			fields: ['new_meds', 'new_allergies_check',
			'missed_dose', 'missed_medications_doses',  'missed_medications_reason_list',
			'missed_medications_reason', 'cust_prec_instr', 'cust_prec_instr_comment',
			'doses_on_hand', 'day_supply_on_hand', 'cust_taking_per_rx', 'cust_identified_issue_in_careplan']

		'Missed Medications':
			indent: false
			tab: 'Adherence'
			fields: ['missed_medications']

		'DUR - Medications':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_medications']

		'DUR - Allergies':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_interactions']

		'DUR':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['subform_dur']

		'Patient Evaluation':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['home_changes', 'home_changes_notes','insurance_changes','insurance_changes_notes',
			'remain_suitable','remain_suitable_notes','remain_appropriate','eval_notes']

		'Address Verification':
			indent: false
			tab: 'Post-Assessment'
			fields: ['embed_patient_address']
		'Shipping And Supplies':
			indent: false
			tab: 'Post-Assessment'
			fields: ['cust_supplies_shipping', 'cust_verified_shipping', 'cust_signature_reminder', 'cust_signature_reminder_2',
			'cust_waiver_on_file',  'cust_delivery_change_location', 'cust_delivery_no_signature', 'cust_sent_waiver',
			'delivery_delay', 'delivery_delay_reason']
		'Pharmacist Questions':
			indent: false
			tab: 'Post-Assessment'
			fields: ['pharmacist_questions', 'pharm_quest_dtl']

		'Post-Assessment Notes':
			indent: false
			tab: 'Post-Assessment'
			fields: ['contact_notes']

		'Primary Therapy Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_therapy_5'] # subform

		'Primary Drug Brand Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_brand_1'] # subform
		'Secondary Drug Brand Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_brand_2'] # subform
		'Tertiary Drug Brand Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_brand_3'] # subform
		'Quaternary Drug Brand Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_brand_4'] # subform
		'Quinary Drug Brand Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_brand_5']

		'Primary Disease Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Patient Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			area: 'footer'
			fields: ['subform_disease_5']

		'Pharmacy Post-Assessment':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			fields: ['cust_sched_next_infusion', 'cust_sched_next_infusion_dt', 'cust_additional_counseling',
			'medicare_status', 'medicare_doses_allowed', 'medicare_doses_dispensed',
			'cust_add_next_callin', 'cust_notify_technicians',
			'cust_admissions_complete', 'cust_ongoing_labs', 'cust_ongoing_labs_source', 'cust_compliance_barriers',
			'cust_compliance_barriers_details','cust_identified_issue_in_careplan']
		'Labs':
			indent: false
			tab: 'Post-Assessment'
			fields: ['cust_labs_reviewed', 'cust_no_labs_dispense_anyway', 'cust_label_double_checked']
		'Compliance Post-Assessment':
			indent: false
			tab: 'Post-Assessment'
			fields: ['cust_changed_since_fill', 'cust_changed_since_fill_details', 'cust_medication_list_reviewed', 'cust_careplan_updated']
		'Interventions':
			indent: false
			tab: 'Post-Assessment'
			fields: ['intervention']
		'Intervention Details':
			hide_header: true
			indent: false
			tab: 'Post-Assessment'
			note: 'Document any ADR or Catheter related event as an intervention'
			fields: ['subform_intervention']

	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Pharmacist Ongoing Assessment"
	]

view:
	block:
		update:
			except: ['admin', 'self']
	comment: 'Patient > Careplan > Ongoing'
	grid:
		fields: ['created_on', 'contact_notes', 'cust_supplies_shipping']
		sort: ['-created_on']
	label: 'Pharmacist Ongoing Assessment'
	open: 'read'
