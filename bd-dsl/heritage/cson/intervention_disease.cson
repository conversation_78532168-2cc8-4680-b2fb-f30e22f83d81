fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	type:
		model:
			required: true
			source: ['Catheter Infection', 'Infectious Disease']
			if:
				'Infectious Disease':
					fields: ['disease_id', 'infectious_disease_confirmed', 'infection_disease_treatment', 'infection_disease_route']
				'Catheter Infection':
					fields: ['line_type', 'infection_date', 'outcome']
		view:
			columns: 2
			control: 'radio'
			label: 'Type'

	line_type:
		model:
			required: true
			source: ["Central", "Peripheral"]
		view:
			columns: 2
			control: 'radio'
			label: 'Line Type'

	infection_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Date of reported infection'

	outcome:
		model:
			required: true
			multi: true
			source: ["Hospitalization", "Line removed", "Antibiotics given"]
		view:
			columns: 2
			control: 'checkbox'
			label: 'Outcome'

	disease_id:
		model:
			multi: true
			required: true
			source: 'list_disease'
			sourceid: 'code'
			if:
				'other':
					fields: ['infectious_disease_other']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Infectious Disease'

	infectious_disease_other:
		model:
			required: true
		view:
			columns: 2
			label: 'Infectious Disease Other'

	infection_disease_treatment:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: ' Did this infection require treatment with medication?'

	infection_disease_route:
		model:
			source: ['IV', 'Oral']
			if:
				'IV':
					fields: ['assoc_meds']
					sections: ['Associated Medications']
				'Oral':
					fields: ['assoc_meds']
					sections: ['Associated Medications']
		view:
			columns: 2
			control: 'radio'
			label: ' Medication Route'

	infectious_disease_confirmed:
		model:
			required: true
			source: ['Confirmed', 'Unconfirmed']
		view:
			columns: 2
			control: 'radio'
			label: 'Confirmed or unconfirmed illness?'

	assoc_meds:
		model:
			multi: true
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status:
					'static': 'Active'
		view:
			embed:
				form: 'patient_medication'
			grid:
				edit: false
				add: 'none'
				rank: 'none'
				fields: ['fdb_id', 'medication_dose', 'medication_frequency', 'start_date']
				label: ['Medication', 'Dose', 'Frequency', 'Start Dt']
				width: [35, 25, 25, 15]
			label: 'Medications'

	patient_interactions:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			readonly: true
			embed:
				form: 'interactions'
				selectable: false
			grid:
				add: 'none'
				hide_cardmenu: true
				edit:true
				rank: 'none'
				fields: ['created_by', 'has_da', 'has_dd']
				label: ['Created By','Drug Allergy', 'Drug Drug']
				width: [30, 30, 30]
			label: 'Interactions'

	patient_interaction_btn:
		model:
			source: ['Get Interactions']
		view:
			columns: 2
			class: 'dsl-button'
			control: 'checkbox'
			label: 'Patient Interaction'
			validate: [
				name: 'DrugAllergyInteraction'
			]

	comments:
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['physician']
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections_group: [
		'Infection Control Details':
			hide_header: true
			indent: false
			fields: ['type', 'line_type', 'infection_date', 'outcome',
			'disease_id', 'infectious_disease_other', 'infection_disease_treatment',
			'infection_disease_route', 'infectious_disease_confirmed']
		'Associated Medications':
			indent: false
			fields: ['assoc_meds']
		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_interaction_btn']
		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'DUR'
			fields: ['patient_interactions']
		'Comments':
			indent: false
			fields: ['comments']
	]

	transform_post: [
			name: "AutoNote"
			arguments:
				subject: "Infection Control"
	]

view:
	comment: 'Patient > Careplan > Infection Control'
	grid:
		fields: ['created_on', 'created_by', 'type', 'outcome', 'disease_id', 'comments']
		sort: ['-id']
	label: 'Infection Control'
	open: 'read'
