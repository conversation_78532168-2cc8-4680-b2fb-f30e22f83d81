fields:

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Referral'
			readonly: true
			columns: -2
			validate: [
				name: 'PrefillNursingNotes'
			]

	therapy_1:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Primary Therapy Questionnaire', 'Primary Therapy Assessment', 'Primary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_1']
				'*':
					sections: ['Primary Therapy Questionnaire', 'Primary Therapy Assessment', 'Primary Therapy Post Visit']
					fields: ['subform_therapy_1']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Therapy'

	therapy_2:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Secondary Therapy Questionnaire', 'Secondary Therapy Assessment', 'Secondary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_2']
				'*':
					sections: ['Secondary Therapy Questionnaire', 'Secondary Therapy Assessment', 'Secondary Therapy Post Visit']
					fields: ['subform_therapy_2']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Therapy'

	therapy_3:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Tertiary Therapy Questionnaire', 'Tertiary Therapy Assessment', 'Tertiary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_3']
				'*':
					sections: ['Tertiary Therapy Questionnaire', 'Tertiary Therapy Assessment', 'Tertiary Therapy Post Visit']
					fields: ['subform_therapy_3']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Therapy'

	therapy_4:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Quaternary Therapy Questionnaire', 'Quaternary Therapy Assessment', 'Quaternary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_4']
				'*':
					sections: ['Quaternary Therapy Questionnaire', 'Quaternary Therapy Assessment', 'Quaternary Therapy Post Visit']
					fields: ['subform_therapy_4']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Therapy'

	therapy_5:
		model:
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'factor':
					sections: ['Bleed Log', 'Quinary Therapy Questionnaire', 'Quinary Therapy Assessment', 'Quinary Therapy Post Visit']
					fields: ['bleed_log', 'subform_therapy_5']
				'*':
					sections: ['Quinary Therapy Questionnaire', 'Quinary Therapy Assessment', 'Quinary Therapy Post Visit']
					fields: ['subform_therapy_5']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Therapy'

	subform_therapy_1:
		model:
			source: 'encounter_{therapy_1}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
				'encounter_inotrope': {}
				'encounter_chemotherapy': {}
				'encounter_bisphosphonates': {}
				'encounter_nursing': {}
			type: 'subform'
		view:
			label: 'Primary Therapy Assessment'

	subform_therapy_2:
		model:
			source: 'encounter_{therapy_2}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
				'encounter_inotrope': {}
				'encounter_chemotherapy': {}
				'encounter_bisphosphonates': {}
				'encounter_nursing': {}
			type: 'subform'
		view:
			label: 'Secondary Therapy Assessment'

	subform_therapy_3:
		model:
			source: 'encounter_{therapy_3}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
				'encounter_inotrope': {}
				'encounter_chemotherapy': {}
				'encounter_bisphosphonates': {}
				'encounter_nursing': {}
			type: 'subform'
		view:
			label: 'Tertiary Therapy Assessment'

	subform_therapy_4:
		model:
			source: 'encounter_{therapy_4}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
				'encounter_inotrope': {}
				'encounter_chemotherapy': {}
				'encounter_bisphosphonates': {}
				'encounter_nursing': {}
			type: 'subform'
		view:
			label: 'Quaternary Therapy Assessment'

	subform_therapy_5:
		model:
			source: 'encounter_{therapy_5}'
			sourcefilter:
				'encounter_factor': {}
				'encounter_aat': {}
				'encounter_ig': {}
				'encounter_subqig': {}
				'encounter_tnf': {}
				'encounter_tpn': {}
				'encounter_steroid': {}
				'encounter_inotrope': {}
				'encounter_chemotherapy': {}
				'encounter_bisphosphonates': {}
				'encounter_nursing': {}
			type: 'subform'
		view:
			label: 'Quinary Therapy Assessment'

	brand_1:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Primary Brand Questionnaire', 'Primary Brand Assessment', 'Primary Brand Post Visit']
					fields: ['subform_brand_1']
		view:
			offscreen: true
			readonly: true
			label: 'Primary Brand'

	brand_2:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Secondary Brand Questionnaire', 'Secondary Brand Assessment', 'Secondary Brand Post Visit']
					fields: ['subform_brand_2']
		view:
			offscreen: true
			readonly: true
			label: 'Secondary Brand'

	brand_3:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Tertiary Brand Questionnaire', 'Tertiary Brand Assessment', 'Tertiary Brand Post Visit']
					fields: ['subform_brand_3']
		view:
			offscreen: true
			readonly: true
			label: 'Tertiary Brand'

	brand_4:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quaternary Brand Questionnaire', 'Quaternary Brand Assessment', 'Quaternary Brand Post Visit']
					fields: ['subform_brand_4']
		view:
			offscreen: true
			readonly: true
			label: 'Quaternary Brand'

	brand_5:
		model:
			source: 'list_brand_asmt'
			sourceid: 'code'
			if:
				'*':
					sections: ['Quinary Brand Questionnaire', 'Quinary Brand Assessment', 'Quinary Brand Post Visit']
					fields: ['subform_brand_5']
		view:
			offscreen: true
			readonly: true
			label: 'Quinary Brand'

	subform_brand_1:
		model:
			source: 'encounter_{brand_1}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
				'encounter_tepezza': {}
			type: 'subform'
		view:
			label: 'Primary Drug Brand Assessment'

	subform_brand_2:
		model:
			source: 'encounter_{brand_2}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
				'encounter_tepezza': {}
			type: 'subform'
		view:
			label: 'Secondary Drug Brand Assessment'

	subform_brand_3:
		model:
			source: 'encounter_{brand_3}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
				'encounter_tepezza': {}
			type: 'subform'
		view:
			label: 'Tertiary Drug Brand Assessment'

	subform_brand_4:
		model:
			source: 'encounter_{brand_4}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
				'encounter_tepezza': {}
			type: 'subform'
		view:
			label: 'Quaternary Drug Brand Assessment'

	subform_brand_5:
		model:
			source: 'encounter_{brand_5}'
			sourcefilter:
				'encounter_cimzia': {}
				'encounter_dupixent': {}
				'encounter_enbrel': {}
				'encounter_humira': {}
				'encounter_krystexxa': {}
				'encounter_lemtrada': {}
				'encounter_ocrevus': {}
				'encounter_orencia': {}
				'encounter_radicava': {}
				'encounter_remicade': {}
				'encounter_rituxan': {}
				'encounter_simponi': {}
				'encounter_simponiaria': {}
				'encounter_soliris': {}
				'encounter_stelara': {}
				'encounter_tysabri': {}
				'encounter_vyvgart': {}
				'encounter_vancomycin': {}
				'encounter_methyl': {}
				'encounter_tepezza': {}
			type: 'subform'
		view:
			label: 'Quinary Drug Brand Assessment'


	subform_clinical_1:
		model:
			source: 'clinical_{clinical_1}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Primary Clinical Assessment'

	subform_clinical_2:
		model:
			source: 'clinical_{clinical_2}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Secondary Clinical Assessment'

	subform_clinical_3:
		model:
			source: 'clinical_{clinical_3}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Tertiary Clinical Assessment'

	subform_clinical_4:
		model:
			source: 'clinical_{clinical_4}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Quaternary Clinical Assessment'

	subform_clinical_5:
		model:
			source: 'clinical_{clinical_5}'
			sourcefilter:
				'clinical_aghda': {}
				'clinical_alsfrs': {}
				'clinical_basdai': {}
				'clinical_braden': {}
				'clinical_cidp': {}
				'clinical_dlqi': {}
				'clinical_edss': {}
				'clinical_epworth': {}
				'clinical_grip_strength': {}
				'clinical_hat_qol': {}
				'clinical_hbi': {}
				'clinical_hfqol': {}
				'clinical_hmq': {}
				'clinical_hpq': {}
				'clinical_incat': {}
				'clinical_mfis5': {}
				'clinical_mgadl': {}
				'clinical_mhaq': {}
				'clinical_mmas8': {}
				'clinical_mmn': {}
				'clinical_moqlq': {}
				'clinical_ms': {}
				'clinical_mygrav': {}
				'clinical_myositis': {}
				'clinical_padqol': {}
				'clinical_pas2': {}
				'clinical_pes': {}
				'clinical_phq': {}
				'clinical_poem': {}
				'clinical_qlsh': {}
				'clinical_radai': {}
				'clinical_rods': {}
				'clinical_sf12v2': {}
				'clinical_sibdq': {}
				'clinical_stiffps': {}
				'clinical_wat': {}
				'clinical_wellness_iv': {}
				'clinical_wellness_si': {}
				'clinical_wpai': {}
				'clinical_wat': {}
				'clinical_pain': {}
				'clinical_qol_peds': {}
				'clinical_qol_factor': {}
				'clinical_rarevoice_survey': {}
				'clinical_outcomes': {}
			type: 'subform'
		view:
			label: 'Quinary Clinical Assessment'

	contact_reason:
		model:
			required: true
			multi: true
			source:
				admission: 'Admission'
				assessment: 'Assessment'
				lab: 'Lab Work'
				teach: 'Teach'
				prn: 'PRN'
				troubleshoot: 'Trouble Shooting'
				drug_admin: 'Drug Admin'
				access: 'Access Related'
				unscheduled: 'Unscheduled'
				homebound: 'Homebound'
				teaching: 'Teaching Visit'
				cath: 'Catheter Care and Maintenance'
				other: 'Other'
			if:
				'unscheduled':
					sections: ['Signatures Requested']
					fields: ['contact_details']
				'other':
					sections: ['Signatures Requested']
					fields: ['contact_details']
				'assessment':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Signatures Requested']
					fields: ['subform_ros','measurement_log']
				'lab':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Signatures Requested', 'Labs']
					fields: ['subform_ros', 'measurement_log', 'subform_lab_draw']
				'admission':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'homebound':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'prn':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Measurement Log', 'Review Of Systems', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'troubleshoot':
					sections: ['Vital Signs (Baseline)', 'Measurement Log',
					'Review Of Systems', 'Signatures Requested']
					fields: ['subform_ros','measurement_log']
				'cath':
					sections: ['Vital Signs (Baseline)','Measurement Log',
					'Review Of Systems', 'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'access':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments',
					'Measurement Log', 'Review Of Systems',
					'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'drug_admin':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments', 'Lots Used',
					'Infusion/Vitals Log', 'Measurement Log', 'Drug Admin', 'Review Of Systems',
					'Signatures Requested']
					fields: ['subform_ros', 'measurement_log'
					'therapy_1', 'therapy_2', 'therapy_3', 'therapy_4', 'therapy_5',
					'clinical_1', 'clinical_2', 'clinical_3', 'clinical_4', 'clinical_5',
					'disease_1', 'disease_2', 'disease_3', 'disease_4', 'disease_5',
					'brand_1', 'brand_2', 'brand_3', 'brand_4', 'brand_5',
					'subform_route_admin', 'subform_vital', 'subform_lots', 'route_id']
				'teach':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments',
					'Measurement Log', 'Review Of Systems',
					'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
				'teaching':
					sections: ['Vital Signs (Baseline)', 'Vital Signs Comments',
					'Measurement Log', 'Review Of Systems',
					'Signatures Requested']
					fields: ['subform_ros', 'measurement_log']
		view:
			control: 'checkbox'
			note: 'Select all that apply'
			label: 'Reason for Visit'
			columns: -2

	subform_outcomes:
		model:
			multi: false
			source: 'clinical_outcomes'
			type: 'subform'
		view:
			label: 'Therapy Outcomes'

	# Careplan
	nurse_careplan_type:
		model:
			prefill: ['company.nurse_careplan_type']
			source: ['Nursing Care Plan', 'Multidisciplinary Care Plan']
			if:
				'Nursing Care Plan':
					fields: ['insurance_type_id']
				'Multidisciplinary Care Plan':
					fields: ['careplan']
					sections: ['Multidisciplinary Care Plan']
		view:
			control: 'radio'
			label: 'Nursing Care Plan Type'
			offscreen: true
			readonly: true

	insurance_type_id:
		model:
			prefill: ['patient_insurance.type_id']
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'*':
					fields: ['cms485_careplan']
					sections: ['Nursing Care Plan (485)']
				'!':
					fields: ['treatment_plan']
					sections: ['Treatment Plan']
		view:
			label: 'Insurance Type'
			readonly: true
			offscreen: true

	careplan:
		model:
			required: false
			sourcefilter:
				id:
					'dynamic': '{careplan_id}'
		view:
			embed:
				form: 'careplan'
				selectable: false
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['status_id', 'therapy_start', 'therapy_end', 'updated_on', 'updated_by']
				label: ['Status', 'Start', 'End', 'Updated On', 'Updated By']
				width: [20, 15, 15, 25, 25]
			label: 'Multidisciplinary Careplan'

	cms485_careplan:
		model:
			required: false
			sourcefilter:
				order_id:
					'dynamic': '{order_id}'
		view:
			embed:
				form: 'careplan_nursing_485'
				selectable: false
				add_preset:
					order_id: '{order_id}'
					order_no: '{order_no}'
					site_id: '{site_id}'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['cert_start', 'cert_end', 'prognosis', 'note']
				label: ['Cert Start', 'Cert End', 'Prognosis', 'Note']
				width: [15, 15, 25, 45]
			label: 'Nursing Care Plan (485)'

	treatment_plan:
		model:
			required: false
			sourcefilter:
				order_id:
					'dynamic': '{order_id}'
		view:
			embed:
				form: 'treatment_plan_nursing'
				selectable: false
				add_preset:
					order_id: '{order_id}'
					order_no: '{order_no}'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['start_of_care_date', 'certification_period', 'prognosis', 'note']
				label: ['SOC', 'Cert Per (Mth)', 'Prognosis', 'Note']
				width: [15, 20, 25, 40]
			label: 'Treatment Plan'
			readonly: true

	catheter_log:
		model:
			required: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				date_discontinued:
					'static': null
		view:
			embed:
				form: 'patient_catheter'
				selectable: true
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['date_placed', 'date_discontinued', 'device', 'type_id']
				label: ['Date Placed', 'Date Discontinued', 'Device', 'Type']
				width: [15, 15, 35, 35]
			label: 'Device Accessed'

	subform_access:
		model:
			required: false
			multi: true
			source: 'encounter_cath_access'
		view:
			label: 'Catheter Access Assessment'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['site_cond', 'problems_noted', 'sticks', 'comments']
				label: ['Site Cond', 'Problems?', '# Sticks', 'Comments']
				width: [25, 15, 20, 40]

	measurement_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{id}'
		view:
			embed:
				form: 'patient_measurement_log'
			grid:
				add: 'inline'
				fields: ['created_by', 'date', 'height', 'weight']
				width: [40, 20, 20, 20]
			label: 'Measurement Log'
			readonly: true

	# Vitals (baseline)
	bp:
		view:
			note: 'systolic / diastolic'
			label: 'Blood Pressure'
			columns: 4
			validate: [{
				name: 'RegExValidator'
				pattern: '^\\s*(\\d{2,3})\\s*\\/\\s*(\\d{2,3})\\s*$'
				error: 'Invalid Blood Pressure, must be systolic / diastolic'
			}]

	pulse:
		model:
			type: 'int'
			max: 220
			min: 60
		view:
			note: 'bpm (60-220)'
			label: 'Pulse'
			columns: 4

	temp:
		model:
			min: 89
			max: 110
			type: 'decimal'
		view:
			note: 'F (89-110)'
			label: 'Temp'
			columns: 4

	vital_comments:
		view:
			control: 'area'
			label: 'Comments'

	premeds:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
				type_id:
					'static': 'Premedication'
		view:
			embed:
				form: 'careplan_order_rx'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['brand_name_id', 'dose', 'dose_unit_id', 'route_id']
				label: ['Drug', 'Dose', 'Unit', 'Route']
				width: [20, 15, 35, 30]
			label: 'Select Pre-Meds Taken'

	prehydration:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
				type_id:
					'static': 'Hydration'
		view:
			embed:
				form: 'careplan_order_rx'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['brand_name_id', 'dose', 'dose_unit_id', 'route_id']
				label: ['Drug', 'Dose', 'Unit', 'Route']
				width: [20, 15, 35, 30]
			label: 'Select Pre-Hydration Given'

	subform_lots:
		model:
			required: false
			multi: true
			source: 'encounter_lot_used'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['lot_no', 'quantity', 'expiration']
				label: ['Lot', 'Quantity', 'Exp']
				width: [30, 30, 40]
			label: 'Lots Used'

	subform_flush:
		model:
			required: false
			multi: true
			source: 'encounter_admin_flush'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['time_given', 'flush_id', 'step']
				label: ['Time', 'Flush', 'Step']
				width: [20, 55, 25]
			label: 'Select Flushes Used'

	subform_vital:
		model:
			source: 'encounter_vital'
			multi: true
			type: 'subform'
		view:
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: true
				fields: ['time_taken', 'temp', 'pulse', 'titration', 'titration_type']
				label: ['Time', 'Temp', 'Pulse', 'Titr', 'Titr Type']
				width: [20, 20, 15, 20, 25]
			label: 'Infusion Log'

	posthydration:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
				type_id:
					'static': 'Hydration'
		view:
			embed:
				form: 'careplan_order_rx'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['brand_name_id', 'dose', 'dose_unit_id', 'route_id']
				label: ['Drug', 'Dose', 'Unit', 'Route']
				width: [20, 15, 35, 30]
			label: 'Select Post-Hydration Given'

	postmeds:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
				type_id:
					'static': 'Postmedication'
		view:
			embed:
				form: 'careplan_order_rx'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['brand_name_id', 'dose', 'dose_unit_id', 'route_id']
				label: ['Drug', 'Dose', 'Unit', 'Route']
				width: [20, 15, 35, 30]
			label: 'Select Post-Meds Taken'

	subform_ros:
		model:
			multi: false
			source: 'encounter_ros'
			type: 'subform'
		view:
			label: 'Review of Systems'

	bleed_log:
		model:
			required: false
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
		view:
			embed:
				form: 'factor_bleed'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['cust_type', 'cust_site', 'duration_bleed','severity', 'cust_comment']
				label: ['Bleed Type', 'Site of Bleed', 'Dur (days)', 'Severity', 'Comments']
				width: [20, 20, 15, 20, 25]
			label: 'Bleed Log'
			readonly: true

	# Heritage Specific
	contact_location:
		model:
			source:
				ats: 'ATS'
				home: "Patient's Home"
				other: 'Other'
			if:
				'home':
					fields: ['external_note_contact_check']
		view:
			control: 'radio'
			label: 'Contact Location'
			columns: 2

	safety_needs:
		model:
			access:
				read: ['patient']
			prefill: ['encounter']
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['safety_needs_com']
		view:
			control: 'radio'
			label: 'Safety Needs?'
			columns: -2

	safety_needs_com:
		model:
			access:
				read: ['patient']
			prefill: ['encounter']
		view:
			label: 'Safety Needs Comment'
			columns: 2

	subjective:
		model:
			max: 4096
		view:
			control: 'area'
			note: "Patient's perception of care"
			label: 'Subjective'

	pt_poc:
		model:
			prefill: ['encounter']
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pt_poc_relation', 'pt_poc_name']
		view:
			control: 'radio'
			label: 'Does the patient or caregiver participate in POC?'
			columns: -2

	pt_poc_relation:
		model:
			prefill: ['encounter']
			source: ['Parent', 'Grandparent', 'Foster Parent','Sibling', 'Spouse', 'Self', 'Other']
		view:
			control: 'radio'
			label: 'Caregiver relationship to patient:'
			columns: 2

	pt_poc_name:
		model:
			prefill: ['encounter']
		view:
			label: 'Caregiver name:'
			columns: 2

	objective:
		model:
			required: false
			max: 4096
		view:
			control: 'area'
			note: "What is observed"
			label: 'Objective'

	subform_funct_lim:
		model:
			multi: false
			source: 'clinical_funct_limits'
			type: 'subform'
		view:
			label: 'Functional Limitations'

	subform_chief_complaint:
		model:
			multi: false
			source: 'encounter_complaint'
			type: 'subform'
		view:
			label: 'Chief Complaint'

	epinephrine_expire:
		model:
			prefill: ['encounter']
			type: 'date'
		view:
			control: 'radio'
			label: 'Epinephrine Expiration Date'
			columns: 2

	diphenhydramine_expire:
		model:
			prefill: ['encounter']
			type: 'date'
		view:
			control: 'radio'
			label: 'Diphenhydramine Expiration Date'
			columns: 2

	fluids_expire:
		model:
			prefill: ['encounter']
			type: 'date'
		view:
			control: 'radio'
			label: 'Fluids Expiration Date'
			columns: 2

	anaphylaxis_note:
		model:
			prefill: ['encounter']
		view:
			label: 'Comments'
			control: 'area'
			columns: 2

	presc_done:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['incomp_reason']
		view:
			control: 'radio'
			label: 'Was the treatment completed as prescribed?'
			columns: -2

	incomp_reason:
		model:
			max: 8
			source: ['ADR', 'Other']
			if:
				'ADR':
					sections: ['ADR Details']
					fields: ['subform_adr']
				'Other':
					fields: ['incomp_reason_other']
		view:
			control: 'radio'
			label: 'Why was the treatment not completed?'
			columns: 2

	incomp_reason_other:
		model:
			max: 64
			source:
				no_access: 'Inability to achieve an IV access'
				patient_no_avail: 'Patient availabilty'
				nurse_not_avail: 'Nursing availability'
				delivery: 'Delivery issue'
				patient_independent: 'Patient Independent'
				other: 'Other'
			if:
				other:
					fields: ['incomp_reason_details']
		view:
			control: 'select'
			label: 'Incomplete Infusion Other Reason'
			columns: 2

	incomp_reason_details:
		model:
			required: true
		view:
			label: 'Incomplete Infusion Other Reason Details'
			columns: 2

	subform_adr:
		model:
			source: 'intervention_adr'
			type: 'subform'
		view:
			label: 'Document ADR'

	add_supply_list:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subform_supply_list']
					sections: ['Patient Supply List']
		view:
			control: 'radio'
			label: 'Was a supply list received?'

	subform_supply_list:
		model:
			source: 'encounter_supply_list'
			type: 'subform'
		view:
			label: 'Patient Supply List'

	cust_review_education:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Reviewed Drug Monograph and/or Manufacturer specific education with the patient?'

	cust_review_adr_management:
		model:
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Side Effects, Adverse Reactions and Management reviewed with patient during this visit?'


	pharm_notification:
		model:
			required: true
			access:
				read: ['patient']
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pharm_notification_list','pharm_notification_note']
		view:
			control: 'radio'
			label: 'Does the pharmacist need to be notified of anything related to this visit?'

	pharm_notification_list:
		model:
			required: true
			multi: true
			source: ['Specific or extra supplies requested', 'PRN medications administered',
			'Reaction kit medication was administered or noted to be expired', 'Other']
		view:
			label: 'Notification Reason'

	pharm_notification_note:
		view:
			label: 'Note to Pharmacist'
			control: 'area'

	prognosis:
		model:
			prefill: ['encounter']
			max: 32
			source: ['Poor', 'Guarded', 'Fair', 'Good', 'Excellent']
		view:
			control: 'radio'
			label: 'Prognosis'

	# Base overrides

	travel_time_st:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	travel_time_ed:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	travel_time_from_st:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	travel_time_from_ed:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	travel_time_total:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	total_mileage:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	time_out:
		view:
			columns: -2

	anaphylaxis_kit_expire:
		model:
			required: false
		view:
			readonly: true
			offscreen: false

	anaphylaxis_kit:
		model:
			required: false
			if:
				'*':
					fields: ['reviewed_by']
		view:
			readonly: true
			offscreen: true

	anaphylaxis_kit_why:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	anaphylaxis_kit_notified:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	check_fall_score:
		model:
			if:
				'*':
					fields: ['reviewed_by']

	subform_checklist_fall:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	fall_risk_score:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	fall_risk_score_by:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	fall_risk_score_datetime:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	post_observations:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	post_observations_note:
		model:
			required: false
		view:
			offscreen: true
			readonly: true
	new_problems:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	new_problems_list:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	new_problems_notified:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	new_problems_comment:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	teaching:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	subform_intervention:
		model:
			required: false
		view:
			offscreen: true
			readonly: true

	approved:
		model:
			required: false
			source: ['Yes', 'No']
			if:
				'Yes':
					sections: ['Approve']
				'No':
					fields: ['denied_by', 'denied_datetime', 'denied_reason']
		view:
			control: 'radio'
			class: 'checkbox-only'
			label: 'Approved?'
			columns: 2
			validate: [
				{
					name: 'PrefillCurrentUser'
					condition:
						approved: 'Yes'
					dest: 'approved_by'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						approved: 'Yes'
					dest: 'approved_datetime'
				},
				{
					name: 'PrefillCurrentUser'
					condition:
						approved: 'No'
					dest: 'denied_by'
				},
				{
					name: 'PrefillCurrentDateTime'
					condition:
						approved: 'No'
					dest: 'denied_datetime'
				}
			]


model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		careplan_delivery_tick:
			link:
				patient_id: 'patient_id'
			filter:
				status: ['ready_to_bill', 'billed']
				void: '!Yes'
			max: 'created_on'
		encounter_environment:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
		company:
			filter:
				id: 1
			max: 'created_on'
		patient_insurance:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
				type_id: ['MCRB', 'MCRD']
			max: 'created_on'
	access:
		create:     ['admin', 'pharm', 'csr', 'cm', 'nurse']
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm', 'csr', 'nurse']
		read:       ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison', 'crn', 'patient','physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm', 'liaison','physician']
		request:    []
		update:     ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
		update_all: ['admin', 'pharm','physician']
		write:      ['admin', 'pharm', 'csr', 'cm', 'nurse','liaison', 'crn','physician']
	bundle: ['patient-form']
	name: ['contact_date', 'contact_reason']
	sections_group: [
		'Visit Info':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['agency_id', 'agency_does_billing', 'site_id', 'environment_assessment_id',
			'delivery_ticket_id', 'visit_number', 'order_id', 'order_no', 'route_id',
			'nurse_careplan_type', 'insurance_type_id',
			'ready_to_bill', 'labs_are_due', 'has_premeds',
			'has_postmeds', 'has_hydration', 'has_flushes']

		'Visit Start':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['contact_location', 'contact_date', 'time_in', 'contact_reason',
			'contact_details', 'external_note_contact_check', 'uses_external_nursing_note']

		'Measurement Log':
			note: 'Include any updated measurements'
			indent: false
			tab: 'Pre-Visit'
			fields: ['measurement_log']

		'Visit Details':
			fields: ['subjective', 'pt_poc', 'pt_poc_relation', 'pt_poc_name', 'objective']

		'Medical History':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_medical_hx']

		'Labs':
			indent: false
			tab: 'Pre-Visit'
			fields: ['subform_lab_draw']

		'Medication Profile':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_medications']

		'Allergies':
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_allergies']

		'DUR - DD DA Interaction':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_interaction_btn']

		'DUR - Interaction':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['patient_interactions']

		'Previous Environment Assessments':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['embed_environment']

		'Environment Assessment':
			hide_header: true
			indent: false
			tab: 'Pre-Visit'
			fields: ['subform_environment']

		'Patient Questionnaire':
			indent: false
			tab: 'Questions'
			fields: ['subform_outcomes'] 

		'Safety Needs':
			hide_header: true
			indent: false
			tab: 'Questions'
			fields: ['safety_needs', 'safety_needs_com'] # subform
		
		'Functional Limitations':
			hide_header: true
			indent: false
			tab: 'Questions'
			fields: ['subform_funct_lim'] # subform
		
		'Primary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_therapy_5'] # subform

		'Primary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_disease_5'] # subform

		'Primary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_1'] # subform
		'Secondary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_2'] # subform
		'Tertiary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_3'] # subform
		'Quaternary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_4'] # subform
		'Quinary Brand Questionnaire':
			hide_header: true
			indent: false
			tab: 'Questions'
			area: 'questions'
			fields: ['subform_brand_5'] # subform

		'Chief Complaint':
			hide_header: true
			indent: false
			tab: 'Questions'
			fields: ['subform_chief_complaint'] # subform

		'Review Of Systems':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ros'] # subform

		'Bleed Log':
			hide_header: true
			indent: false
			tab: 'Bleed Log'
			fields: ['bleed_log'] # subform

		#'POC Review and Care Coordination':
		'Multidisciplinary Care Plan':
			hide_header: true
			indent: false
			tab: 'Care Plan'
			fields: ['careplan']
		'Nursing Care Plan (485)':
			hide_header: true
			indent: false
			tab: 'Care Plan'
			fields: ['cms485_careplan']
		'Treatment Plan':
			hide_header: true
			indent: false
			tab: 'Treatment Plan'
			fields: ['treatment_plan']

		'Catheter Log':
			hide_header: true
			indent: false
			tab: 'Access'
			fields: ['catheter_log']
		'Catheter Access Assessment':
			hide_header: true
			indent: false
			tab: 'Access'
			fields: ['subform_access']

		'Analphylaxis Kit':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['epinephrine_expire', 'diphenhydramine_expire', 'fluids_expire', 'anaphylaxis_note']

		'Vital Signs (Baseline)':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['bp', 'pulse', 'temp']
		'Vital Signs Comments':
			hide_header: true
			indent: false
			tab: 'Vitals/Admin'
			fields: ['vital_comments']

		'Pre-Medications':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['premeds']
		'Pre-Hydration':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['prehydration']

		'Drug Admin':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_route_admin'] # subform

		'Lots Used':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_lots']

		'Flushes':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_flush']

		'Infusion/Vitals Log':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['subform_vital']

		'Post-Hydration':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['posthydration']
		'Post-Medications':
			indent: false
			tab: 'Vitals/Admin'
			fields: ['postmeds']

		'Primary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_therapy_5'] # subform

		'Primary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_disease_5'] # subform

		'Primary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_1'] # subform
		'Secondary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_2'] # subform
		'Tertiary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_3'] # subform
		'Quaternary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_4'] # subform
		'Quinary Brand Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_brand_5'] # subform

		'Primary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_1'] # subform
		'Secondary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_2'] # subform
		'Tertiary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_3'] # subform
		'Quaternary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_4'] # subform
		'Quinary Clinical Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_clinical_5']

		'Primary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_therapy_1'] # subform
		'Secondary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_therapy_2'] # subform
		'Tertiary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_therapy_3'] # subform
		'Quaternary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_therapy_4'] # subform
		'Quinary Therapy Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_therapy_5'] # subform

		'Primary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_disease_1'] # subform
		'Secondary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_disease_2'] # subform
		'Tertiary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_disease_3'] # subform
		'Quaternary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_disease_4'] # subform
		'Quinary Disease Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_disease_5'] # subform

		'Primary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_brand_1'] # subform
		'Secondary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_brand_2'] # subform
		'Tertiary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_brand_3'] # subform
		'Quaternary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_brand_4'] # subform
		'Quinary Brand Post Visit':
			hide_header: true
			indent: false
			tab: 'Post Visit'
			area: 'footer'
			fields: ['subform_brand_5'] # subform

		'Post Visit':
			hide_header: true
			indent: false
			tab: 'Review'
			fields: ['prognosis', 'time_out', 'time_total', 'presc_done', 'incomp_reason', 'incomp_reason_other',
			'incomp_reason_details', 'next_visit_scheduled', 'next_infusion',
			'cust_review_education', 'cust_review_adr_management', 'additional_notes']

		'Pharmacist Notification':
			indent: false
			tab: 'Review'
			fields: ['pharm_notification','pharm_notification_list','pharm_notification_note', 'add_supply_list']
		
		'Patient Supply List':
			indent: false
			tab: 'Review'
			fields: ['subform_supply_list']

		'Signatures Requested':
			indent: false
			tab: 'Review'
			fields: ['client_signature', 'rn_signature']

		'Post Visit Review':
			indent: false
			tab: 'Review'
			fields: ['pharm_warning', 'pharm_notes', 'ready_for_review', 'approved', 'review_status']

		'Approve':
			modal: false #making it false because have to show in section and there is also no server action right now
			indent: false
			tab: 'Review'
			fields: ['approved_by', 'approved_datetime', 'cc_signature']
		'Denied':
			modal: true
			indent: false
			fields: ['denied_by', 'denied_datetime', 'denied_reason']
		'Locked/Unlock History':
			indent: false
			tab: 'Review'
			fields: ['subform_lock_history']

		'Billable':
			hide_header: true
			indent: false
			tab: 'Bill'
			fields: ['subform_bill']

		'ADR Details':
			fields: ['subform_adr']

		'File Attachments':
			note: 'Attach scans of labels if available'
			fields: ['subform_attachment'] # subform

	]

view:
	comment: 'Patient > Careplan > Encounter'
	grid:
		fields: ['created_by', 'contact_date', 'time_in', 'time_out', 'contact_reason', 'time_total']
		sort: ['-id']
	label: 'Patient Encounter'
	open: 'read'
	block:
		validate: [
			name: 'EncounterBlock'
		]