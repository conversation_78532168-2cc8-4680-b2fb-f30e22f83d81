fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	cust_pt_refuses:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pt_refuses_confirmation', 'cust_pt_refuses_date']
		view:
			control: 'radio'
			label: 'Is the patient refusing to complete?'

	cust_pt_refuses_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date Refused'
			template: '{{now}}'

	cust_pt_refuses_confirmation:
		model:
			required: true
			multi: false
			source: ['OK']
		view:
			control: 'checkbox'
			label: 'This will be documented that patient is refusing completion of QOL. Please try to collect each quarter. Please notify Px of this refusal'

	pt_factor_qol_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Bleeding Disorder Quality Of Life']
					fields: ['contact_date']
				'No':
					fields: ['cust_pt_refuses']
		view:
			control: 'radio'
			label: 'Will a Bleeding Disorder Quailty of Life survey be completed today?'
			findfilter: 'Yes'

	contact_date:
		model:
			access:
				read: ['patient']
			type: 'date'
		view:
			label: 'Contact Date'
			template: '{{now}}'

	factor_loss_mobility:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'Loss of joint mobility affects how I walk'

	factor_fear_hospital_distance:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I am afraid of being far away from a hospital or healthcare center with emergency facilities'

	factor_fear_accidents:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I worry about accidents'

	factor_fear_bump:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I am afraid of being bumped or hit'

	factor_in_control:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I am in control of my life'

	factor_feel_frustrated:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I feel frustrated because I can’t do what I want to do'

	factor_fear_jobloss:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I worry about finding or losing a job'

	factor_experience_restrictions:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I experience restrictions at work or school'

	factor_interfere_relationships:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'Hemophilia interferes with my relationships'

	factor_fear_crowds:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I am afraid to go to crowded places like concerts or bars for fear of being injured or bumped'

	factor_interfere_activities:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'My hemophilia treatment interferes with my daily activities'

	factor_worry_safety:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I worry about the safety of my treatment'

	factor_worry_healthcare_providers:
		model:
			access:
				read: ['patient']
			max: 32
			source: ['Never', 'Rarely', 'Sometimes', 'Often', 'Always']
		view:
			control: 'radio'
			label: 'I worry about being treated by healthcare providers who do not know how to treat hemophilia'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'csr', 'nurse', 'patient', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	reportable: true
	prefill:
		patient:
			link:
				id: 'patient_id'
		clinical_qol_factor:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Factor Quality Of Life Survey Participation':
			fields: ['pt_factor_qol_particiates', 'cust_pt_refuses', 'cust_pt_refuses_date', 'cust_pt_refuses_confirmation']
		'Contact Date':
			fields: ['contact_date']
		'Bleeding Disorder Quality Of Life':
			fields: ['factor_loss_mobility', 'factor_fear_hospital_distance', 'factor_fear_accidents', 'factor_fear_bump', 'factor_in_control', 'factor_feel_frustrated', 'factor_fear_jobloss', 'factor_experience_restrictions', 'factor_interfere_relationships', 'factor_fear_crowds', 'factor_interfere_activities', 'factor_worry_safety', 'factor_worry_healthcare_providers']

view:
	comment: 'Patient > Careplan >  Factor Quality of Life'
	grid:
		fields: ['created_on', 'created_by', 'contact_date', 'cust_pt_refuses']
		sort: ['-id']
	label: 'Clinical: Factor Quality of Life'
