fields:

	date:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Assessment Date'
			template: '{{now}}'

	pt_eligible:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pt_be_enrolled']
				'No':
					fields: ['pt_not_eligible']
		view:
			columns: 4
			control: 'radio'
			label: 'Eligible?'

	pt_be_enrolled:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['enrollment_method', 'assistance_type']
				'No':
					fields: ['pt_be_enrolled_why']
		view:
			columns: 4
			control: 'radio'
			label: 'Will be enrolled?'

	enrollment_method:
		model:
			multi: false
			source: ['Phone', 'Website', 'Fax']
			if:
				'Phone':
					fields: ['contact']
				'Website':
					fields: ['url']
			required: false
		view:
			columns: 4
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Enrollment Method'

	contact:
		model:
			required: false
		view:
			columns: 2
			label: 'Contact'

	url:
		model:
			required: false
		view:
			columns: 2
			format: 'url'
			label: 'Website'

	pt_be_enrolled_why:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Why not?'

	type:
		model:
			required: true
			source:
				copay: 'Manufacturer Copay Program'
				pap: 'Manufacturer Assistance Program'
				hardship: 'Hardship Waiver'
				grant: 'Foundation/Grant Enrollment'
				mental: 'Mental Health'
				emergency: 'Emergency Assistance'
				item: 'Item Reimbursement'
				medical: 'Medical Expenses'
				physical: 'Physical Therapy or Exercise Equipment/Gym Memberships'
				scholarship: 'Scholarship'
			if:
				'copay':
					fields: ['manufacturer_id', 'coverage', 'embed_copay_program']
					require_fields: ['embed_copay_program']
				'pap':
					fields: ['manufacturer_id', 'coverage', 'enrollment_method', 'embed_pap_program']
					require_fields: ['embed_pap_program']
				'grant':
					fields: ['coverage', 'enrollment_method', 'embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'mental':
					fields: ['enrollment_method', 'embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'emergency':
					fields: ['coverage', 'embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'item':
					fields: ['embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'medical':
					fields: ['embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'mental':
					fields: ['embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'physical':
					fields: ['embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'scholarship':
					fields: ['embed_alt_payer']
					require_fields: ['embed_alt_payer']
				'hardship':
					fields: ['hardship_waiver_notes']
		view:
			columns: 2
			control: 'select'
			label: 'Type'

	manufacturer_id:
		model:
			required: true
			source: 'list_manufacturer'
			sourceid: 'code'
		view:
			label: 'Manufacturer'
			columns: 2

	coverage:
		model:
			required: true
			source: ['Drug Only', 'Drug and Administration']
		view:
			columns: 2
			control: 'radio'
			label: 'Coverage'

	pt_not_eligible:
		model:
			required: true
		view:
			columns: 2
			label: 'Why not?'

	notes:
		view:
			control: 'area'
			label: 'Additional Notes'

	assistance_type:
		model:
			source: ['Copay', 'Premium', 'Rent', 'Phone', 'Transportation', 'Nursing', 'Other']
			if:
				'Other':
					fields: ['assistance_type_other']
			required: false
			multi: true
		view:
			columns: 2
			class: 'checkbox checkbox-4'
			control: 'checkbox'
			label: 'Assistance Type'

	assistance_type_other:
		model:
			required: false
		view:
			columns: 2
			label: 'Details'

	assistance_status:
		model:
			multi: false
			source: ['Pending', 'Applied', 'Denied', 'Appealing', 'Withdrawn', 'Approved']
			default: 'Pending'
			required: true
			if:
				'Applied':
					fields: ['reference_number']
				'Appealing':
					fields: ['reference_number']
				'Denied':
					fields: ['reference_number', 'denied_date', 'denied_reason_list']
				'Withdrawn':
					fields: ['reference_number', 'withdrawn_date']
		view:
			columns: 2
			control: 'radio'
			label: 'Assistance Program Status'
			validate: [
				{
					name: 'PrefillCurrentDate'
					condition:
						assistance_status: 'Denied'
					dest: 'denied_date'
				},
				{
					name: 'PrefillCurrentDate'
					condition:
						assistance_status: 'Withdrawn'
					dest: 'withdrawn_date'
				}
			]

	reference_number:
		view:
			columns: 2
			label: 'Reference #'

	denied_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Date Denied'

	withdrawn_date:
		model:
			type: 'date'
			required: true
		view:
			columns: 4
			label: 'Date Withdrawn'

	denied_reason_list:
		model:
			required: true
			multi: false
			source: ['Income', 'Off Label', 'Covered under medical plan', 'Generic Available/Preferred', 'Member has secondary', 'Other']
			if:
				'Other':
					fields: ['denied_reason']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Reason Denied'

	denied_reason:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Reason Denied Details'

	assistance_program_type:
		model:
			required: true
			source: ['Medical', 'Pharmacy', 'Not Applicable']
			if:
				'Medical':
					fields: ['eob_submitted']
		view:
			columns: 2
			control: 'radio'
			label: 'Assistance Program Type'

	eob_submitted:
		model:
			source: ['Yes']
			required: false
		view:
			columns: 4
			control: 'checkbox'
			class: 'checkbox-only'
			label: 'EOB Submitted?'

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
			['order_id']
			['careplan_id']
			['brand_name_id']
			['manufacturer_id']
		]
	reportable: true
	sections_group: [
		'Patient Insurances':
			hide_header: true
			indent: false
			fields: ['embed_insurances']
		'Details':
			hide_header: true
			indent: false
			fields: ['order_id', 'brand_name_id', 'type', 'date', 'manufacturer_id',
			'pt_eligible', 'pt_not_eligible', 'pt_be_enrolled', 'pt_be_enrolled_why',
			'assistance_type', 'assistance_type_other', 'enrollment_method', 'contact', 'url', 'assistance_program_type', 'eob_submitted',
			'assistance_status', 'denied_date', 'denied_reason_list', 'denied_reason', 'withdrawn_date', 'reference_number', 'coverage', 'embed_copay_program',
			'embed_pap_program', 'embed_alt_payer', 'hardship_waiver_notes',
			]
		'Notes':
			hide_header: true
			indent: false
			fields: ['notes']
		'Attachments':
			indent: false
			fields: ['subform_attachment']
	]
view:
	comment: 'Patient > Patient Assistance'
	grid:
		fields: ['brand_name_id', 'type', 'assistance_type', 'assistance_program_type', 'assistance_status']
		sort: ['-created_on']
	find:
		basic: ['brand_name_id', 'assistance_type', 'assistance_program_type', 'assistance_status']
	label: 'Patient Assistance'
	open: 'read'