fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	cust_infusion_pump:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pump_rental']
		view:
			columns: 2
			control: 'radio'
			label: "Infusion on pump?"

	cust_pump_rental:
		model:
			max: 3
			min: 1
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: "Pump Rental Agreement Form complete?"

	cust_ephinephrine:
		view:
			control: 'area'
			label: "Epinephrine autoinjector brand, dispensing location, and cost?"

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		assessment_nursing:
			link:
				patient_id: 'patient_id'
			max: 'created_on'
	sections_group: [
		'Nursing Questionnaire':
			sections: [
				'Nursing Calculations':
					area: 'preassessment'
					fields: ['cust_infusion_pump', 'cust_pump_rental', 'cust_ephinephrine']
			]
		]
view:
	comment: 'Patient > Intake > Assessment > Nursing'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Nursing'
	open: 'read'
