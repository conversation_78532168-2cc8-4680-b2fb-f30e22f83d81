fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	current_infection:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Current infection?'
			control: 'radio'

	reviewed_mgqol:
		model:
			source: ['No', 'Yes', 'NA']
			required: true
		view:
			columns: 2
			label: 'Have you reviewed the most recent MG-QOL?'
			control: 'radio'

	reviewed_mgadl:
		model:
			source: ['No', 'Yes', 'NA']
			required: true
		view:
			columns: 2
			label: 'Have you reviewed the most recent MG-ADL?'
			control: 'radio'

	reviewed_careplan:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['careplan_details']
			required: true
		view:
			columns: -2
			label: 'Care plan reviewed for follow up items prior to call?'
			control: 'radio'

	careplan_details:
		view:
			columns: 2
			control: 'area'
			label: 'Care plan reviewed comments'

	reviewed_nursingnote:
		model:
			source: ['No', 'Yes', 'NA']
			required: true
		view:
			columns: 2
			label: 'Nursing note reviewed from last infusion?'
			control: 'radio'

model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Vyvgart Risk Assessment':
			area: 'preassessment'
			fields: ['current_infection']
		'Vyvgart Pre-Assessment':
			area: 'preassessment'
			fields: ['reviewed_mgqol', 'reviewed_mgadl',
			'reviewed_careplan', 'careplan_details',
				'reviewed_nursingnote']
view:
	comment: 'Patient > Careplan > Ongoing > Vyvgart'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Ongoing Questionnaire: Vyvgart'
	open: 'read'
