fields:

	# Links
	patient_id:
		model:
			access:
				read: ['nurse', 'pharm']
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	intervention_type:
		model:
			required: true
			source: ["Allergy Intervention/Screening", "Administration Education", "Appointment Coordination",
					"Counseling","Dose Coordination", "Dose Adjustment, Frequency Change and Adjustment based on labs",
					"Drug Optimization", "Drug Utilization Review", "Drug Change", "Drug Reaction", "Identified Drug-Drug Interaction",
					"Infusion schedule change", "Lab Review", "Line Troubleshooting","Mental Health", "Other",
					"Pain Management", "Provider/Department Collaboration", 'Quality of Life Reviewed', "Rate Adjustment",  "Therapy Adjustment",
					"Senior Clinical Staff Review", "Side Effect Management", "Supply Management", 
					"Troubleshooting Infusions","Vein Visualization Device Utilization for IV access", 'Pharmacist Reviewed']
			if:
				'Allergy Intervention/Screening':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Dose Coordination':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Dose Adjustment, Frequency Change and Adjustment based on labs':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Drug Optimization':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Drug Utilization Review':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Identified Drug-Drug Interaction':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Lab Review':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Therapy Adjustment':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Supply Management':
					prefill:
						outcome_1: 'Therapy Optimization'
				'Counseling':
					prefill:
						outcome_1: 'Increased/Improved Adherence'
				'Drug Change':
					prefill:
						outcome_1: 'Improved Tolerability'
				'Drug Reaction':
					prefill:
						outcome_1: 'Improved Tolerability'
				'Line Troubleshooting':
					prefill:
						outcome_1: 'Improved Tolerability'
				'Rate Adjustment':
					prefill:
						outcome_1: 'Improved Tolerability'
				'Side Effect Management':
					prefill:
						outcome_1: 'Improved Tolerability'
				'Troubleshooting Infusions':
					prefill:
						outcome_1: 'Improved Tolerability'
				'Mental Health':
					prefill:
						outcome_1: 'Improved Quality of Life'
				'Pain Management':
					prefill:
						outcome_1: 'Improved Quality of Life'
				'Vein Visualization Device Utilization for IV access':
					prefill:
						outcome_1: 'Improved Quality of Life'
		view:
			control: 'select'
			label: 'Type'

	outcome_1:
		model:
			source: ["Therapy Optimization", "Improved Tolerability",
			"Increased/Improved Adherence", "Improved Quality of Life",
			"Improvement in chronic disease-related symptoms"]
		view:
			control: 'select'
			label: 'Perceived Outcome 1'

	outcome_2:
		model:
			source: ["Therapy Optimization", "Improved Tolerability",
			"Increased/Improved Adherence", "Improved Quality of Life",
			"Improvement in chronic disease-related symptoms"]
		view:
			control: 'select'
			label: 'Perceived Outcome 2'

model:
	name: ['patient_id', 'intervention_type']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm', 'csr']
		delete:     ['admin']
		read:       ['admin', 'nurse', 'pharm', 'patient', 'csr']
		read_all:   ['admin', 'nurse', 'pharm', 'patient', 'csr']
		request:    []
		update:     ['admin', 'nurse', 'pharm', 'csr']
		review:     []
		update_all: ['admin', 'nurse', 'pharm', 'csr']
		write:      ['admin', 'nurse', 'pharm', 'patient', 'csr']
	sections:
		'Intervention Pharmacy':
			fields: ['intervention_type', 'outcome_1', 'outcome_2']

view:
	find:
		basic: ['intervention_type', 'outcome_1', 'outcome_2']
	grid:
		fields: ['intervention_type', 'outcome_1', 'outcome_2']
	label: 'Intervention Pharmacy'
	open: 'read'