fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	ref_date:
		model:
			type: 'date'
		view:
			label: 'Date of Referral'
			columns: 3

	source:
		view:
			label: 'Referral Source'
			columns: 3

	phone:
		view:
			format: 'us_phone'
			label: "Referral Phone #"
			columns: 3

	contact:
		view:
			label: 'Referral Contact'
			columns: 3
	reason:
		view:
			control: 'area'
			label: 'Reason for hand-off'
	
	provider:
		view:
			label: 'Selected Alt. Provider'
			columns: 3

	alt_phone:
		view:
			format: 'us_phone'
			label: "Alt. Provider Phone #"
			columns: 3

	alt_fax:
		view:
			format: 'us_phone'
			label: "Alt. Provider Fax #"
			columns: 3

	alt_contact:
		view:
			label: 'Alt. Provider Contact'
			columns: 3

	followup:
		model:
			type: 'date'
		view:
			label: 'Follow up date'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Patient Triage':
			hide_header: true
			indent: false
			fields: ['ref_date', 'source', 'phone', 'contact',
					'reason', 'provider', 'alt_phone', 'alt_fax',
					'alt_contact', 'followup']

view:
	comment: 'Patient > Billing Assessment Patient Triage'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Patient Triage'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']