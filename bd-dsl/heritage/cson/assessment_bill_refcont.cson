fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	contact_purpose:
		view:
			control: 'area'
			label: 'Purpose of Call'
			columns: 2

	contact_narrative:
		view:
			control: 'area'
			label: 'Narrative'
			columns: 2

	contact_outcome:
		view:
			control: 'area'
			label: 'Outcome'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Referral Contact':
			hide_header: true
			indent: false
			fields: ['contact_purpose', 'contact_narrative', 'contact_outcome']

view:
	comment: 'Patient > Billing Assessment Referral Contact'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Referral Contact'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']