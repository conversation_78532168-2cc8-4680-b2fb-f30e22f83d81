fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	chief_complaint:
		model:
			prefill: ['encounter_complaint']
			required: true
			max: 128
			source: 'list_chief_complaint'
			if:
				'Other':
					fields: ['chief_complaint_other']
				'Pain scale (1 to 10)':
					fields: ['pain_scale_cc']
		view:
			control: 'select'
			label: 'Chief <PERSON><PERSON><PERSON>t'
			columns: 2

	chief_complaint_other:
		model:
			prefill: ['encounter_complaint']
			required: true
		view:
			label: 'Other Chief Complaint Details'
			columns: 2

	pain_scale_cc:
		model:
			max: 2
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Pain Scale'
			columns: 2

	chief_complaint_duration:
		model:
			type: 'int'
		view:
			label: 'Duration'
			columns: -2

	chief_complaint_duration_type:
		model:
			max: 16
			default: 'Days'
			source: ['Days', 'Months', 'Years']
		view:
			control: 'radio'
			label: 'Duration Type'
			columns: 2

	ocomps:
		model:
			prefill: ['encounter_complaint']
			max: 256
			multi: true
			source: 'list_chief_complaint'
			if:
				'Other':
					fields: ['other_complaint_other']
				'Pain scale (1 to 10)':
					fields: ['pain_scale_other']
		view:
			control: 'select'
			label: 'Other Complaints'
			columns: -2

	other_complaint_other:
		model:
			prefill: ['encounter_complaint']
			required: true
		view:
			label: 'Other Complaint Details'
			columns: 2

	pain_scale_other:
		model:
			max: 2
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			control: 'radio'
			label: 'Other Complaint Pain Scale'
			columns: 2

	envoy_external_tag:
		model:
			type: 'text'
		view:
			label: 'Envoy External Tag'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			max: 'created_on'
		encounter_complaint:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	name: ['patient_id', 'chief_complaint']
	sections:
		'Chief Complaint':
			fields: ['chief_complaint', 'chief_complaint_other',
			'pain_scale_cc', 'chief_complaint_duration',
			'chief_complaint_duration_type', 'ocomps',
			'other_complaint_other', 'pain_scale_other']
view:
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-id']
	comment: 'Patient > Careplan > Encounter > Complaint'
	label: 'Complaint'
