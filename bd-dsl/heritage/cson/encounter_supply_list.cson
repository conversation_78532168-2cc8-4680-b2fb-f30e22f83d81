fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	supplies:
		model:
			prefill: ['encounter_supply_template']
			subfields:
				supplies:
					label: 'Supply'
					type: 'text'
					style:
						width: '50%'
				needed:
					label: 'Quantity Needed'
					type: 'text'
					style:
						width: '50%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Supplies'

	prn_meds:
		model:
			prefill: ['encounter_supply_template']
			subfields:
				medication:
					label: 'Medication'
					type: 'text'
					style:
						width: '30%'
				inhome:
					label: 'Quantity In Home'
					type: 'text'
					style:
						width: '25%'
				needed:
					label: 'Quantity Needed'
					type: 'text'
					style:
						width: '25%'
				expiration:
					label: 'Expiration Date'
					type: 'date'
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'PRN Medications'

	emergency_meds:
		model:
			prefill: ['encounter_supply_template']
			subfields:
				medication:
					label: 'Medication'
					type: 'text'
					style:
						width: '30%'
				inhome:
					label: 'Quantity In Home'
					type: 'text'
					style:
						width: '25%'
				needed:
					label: 'Quantity Needed'
					type: 'text'
					style:
						width: '25%'
				expiration:
					label: 'Expiration Date'
					type: 'date'
					style:
						width: '20%'
			type: 'json'
		view:
			control: 'grid'
			label: 'Emergency Medications'

	comment:
		model:
			prefill: ['encounter_supply_list']
		view:
			control: 'area'
			label: 'Comments'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'csr', 'nurse', 'patient', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_supply_list:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
		encounter_supply_template:
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Additional Supplies Needed':
			fields: ['supplies', 'prn_meds', 'emergency_meds', 'comment']

view:
	comment: 'Patient > Careplan > Encounter > Patient Supply List'
	grid:
		fields: ['created_by', 'created_on', 'updated_on', 'updated_by']
		sort: ['-id']
	label: 'Patient Encounter: Patient Supply List'
