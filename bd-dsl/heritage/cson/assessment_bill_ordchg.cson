fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	change_drug:
		view:
			label: 'Drug'
			columns: 3

	change_dose:
		view:
			label: 'Dose'
			columns: 3

	change_insurance:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Insurance Verification Completed?'
			columns: -3

	change_pa:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Prior Authorization?'
			columns: 3

	change_call:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Call to patient?'
			columns: 3

	change_aob:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'AOB completed?'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Order Change':
			hide_header: true
			indent: false
			fields: ['change_drug', 'change_dose', 'change_insurance', 'change_pa', 'change_call', 'change_aob']

view:
	comment: 'Patient > Billing Assessment Order Change'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Order Change'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']