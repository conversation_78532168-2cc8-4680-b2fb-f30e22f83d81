fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'
	# Survey Participation
	pt_qol_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					sections: ['Health Improvement', 'Contact Date', 'Limitations', 'Daily Activities (Physical)', 'Daily Activities (Emotional)', 'Pain', '4-Week Review', 'Social']
				'No':
					fields: ['cust_pt_refuses']
		view:
			label: 'Patient QOL Participates'

	cust_pt_refuses:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pt_refuses_confirmation', 'cust_pt_refuses_date']
		view:
			control: 'radio'
			label: 'Is the patient refusing to complete?'

	cust_pt_refuses_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date Refused'
			template: '{{now}}'

	cust_pt_refuses_confirmation:
		model:
			required: true
			multi: false
			source: ['OK']
		view:
			columns: 2
			control: 'checkbox'
			label: 'This will be documented that patient is refusing completion of QOL. Please try to collect each quarter. Please notify Px of this refusal'

	contact_date:
		model:
			access:
				read: ['patient']
			type: 'date'
		view:
			label: 'Contact Date'
			template: '{{now}}'

	# Health Improvement
	health_general:
		view:
			label: 'In general, would you say your health is (select one):'

	# Limitations

	limitation_moderate:
		view:
			label: 'Does your health now limit you in moderate activities?'

	limitation_climbing_many:
		view:
			label: 'Does your health now limit you in climbing several flights of stairs?'

	# Daily Activities (Physical)
	phys_accomplish:
		view:
			label: 'During the past 4 weeks, have you as a result of your physical health accomplished less than you would like?'

	phys_limited:
		view:
			label: 'During the past 4 weeks, were you as a result of your physical health limited in the kind of work or other activities?'

	# Daily Activities (Emotional)
	emo_accomplish:
		view:
			label: 'During the past 4 weeks, have you as a result of emotional problems, accomplished less than you would like?'

	emo_careless:
		view:
			label: 'During the past 4 weeks, have you as a result of emotional problems, done work or other activities less carefully than usual?'

	# General
	gen_pain_interfere:
		view:
			label: 'During the past 4 weeks, how much did pain interfere with your normal work (including both work outside the home and housework)?'

	# 4-Week Review
	wkchk_calm:
		view:
			label: 'How much of the time during the past 4 weeks have you felt calm and peaceful?'

	wkchk_energy:
		view:
			label: 'How much of the time during the past 4 weeks did you have a lot of energy?'

	wkchk_downhearted:
		view:
			label: 'How much of the time during the past 4 weeks have you felt downhearted and blue?'

	gen_social_interfere:
		view:
			label: 'During the past 4 weeks, how much of the time has your physical health or emotional problems interfered with your social activities?'

model:
	access:
		create:     []
		create_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'csr', 'nurse', 'patient', 'pharm']
		write:      ['admin', 'csr', 'nurse', 'patient', 'pharm']
	bundle: ['patient', 'careplan', 'encounter']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		clinical_sf12v2:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Quality Of Life Survey Participation':
			fields: ['cust_pt_refuses', 'cust_pt_refuses_date', 'cust_pt_refuses_confirmation']
		'Contact Date':
			fields: ['contact_date']
		'Health Improvement':
			fields: ['health_general']
		'Limitations':
			fields: ['limitation_moderate', 'limitation_climbing_many']
		'Daily Activities (Physical)':
			fields: ['phys_accomplish', 'phys_limited']
		'Daily Activities (Emotional)':
			fields: ['emo_accomplish', 'emo_careless']
		'Pain':
			fields: ['gen_pain_interfere']
		'4-Week Review':
			fields: ['wkchk_calm', 'wkchk_energy', 'wkchk_downhearted']
		'Social':
			fields: ['gen_social_interfere']

view:
	comment: 'Patient > Intake >  Quality of Life'
	grid:
		fields: ['created_on', 'created_by', 'contact_date', 'cust_pt_refuses']
		sort: ['-id']
	label: 'Clinical: Quality of Life'
