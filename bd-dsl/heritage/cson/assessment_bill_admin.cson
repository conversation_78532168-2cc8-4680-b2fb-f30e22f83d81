fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	spoke_pt:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['spoke_specify']
		view:
			control: 'radio'
			label: 'Spoke with patient?'
			columns: 4

	spoke_specify:
		view:
			control: 'area'
			label: 'Specify'
			columns: 2

	demo:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Demographics Verified?'
			columns: 4

	bill_adr:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Billing Address Verified?'
			columns: 4

	ship_adr:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Shipping Address Verified?'
			columns: 4

	emergency_cont:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Emergency Contact Name and Telephone Number verified?'
			columns: 2

	sig_req:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['sig_optout']
		view:
			control: 'radio'
			label: 'Pt agrees to sign for deliveries?'
			columns: 4

	sig_optout:
		model:
			multi: false
			required: true
			source: ['Signature Opt-Out on Delivery Form will be sent to patient']
		view:
			control: 'checkbox'
			label: 'Opt-Out'
			columns: 2

	insurance:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Insurance Discussed?'
			columns: 4

	insurance_card:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['insurance_requested']
		view:
			control: 'radio'
			label: 'Insurance cards?'
			columns: 4

	insurance_requested:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Requested?'
			columns: 4

	copay_asst:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Copay Assistance Discussed?'
			columns: 4

	copay_accum:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Copay Accumulator Discussed?'
			columns: 4

	copay_appt:
		view:
			label: 'Copay Assistance Appointment?'
			columns: 4

	est_cost:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['est_cost_details']
		view:
			control: 'radio'
			label: 'Estimated Cost of 1st Shipment Discussed?'
			columns: 2

	est_cost_details:
		view:
			control: 'area'
			label: 'Things discussed with the patient'
			columns: 2

	payor_drug:
		view:
			label: 'Payor Name / Drug?'
			columns: 2

	payor_supples_diem:
		view:
			label: 'Payor Name / Supplies / Per Diem?'
			columns: 2

	payor_nursing:
		view:
			label: 'Payor Name / Nursing?'
			columns: 2

	payor_ancillary:
		view:
			label: 'Payor Name / Ancillary Medications?'
			columns: 2

	first:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['prev_provider']
		view:
			control: 'radio'
			label: 'First time receiving this therapy?'
			columns: 2

	prev_provider:
		model:
			required: true
		view:
			label: 'Previous Provider?'
			columns: 2

	prev_provider_contact:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is it okay to contact this provider if we need additional information?'
			columns: 2

	paperwork_packet:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Admission Paperwork Packet Discussed?'
			columns: 2

	disclosure:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Designated Disclosure Reviewed?'
			columns: 2

	consent:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Do you consent to services provided by Heritage Biologics?'
			columns: 2

	transfer_to:
		view:
			label: 'Transferred to:'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Admissions Contact':
			hide_header: true
			indent: false
			fields: ['spoke_pt', 'spoke_specify', 'demo', 'bill_adr',
					'ship_adr', 'emergency_cont', 'sig_req', 'sig_optout', 'insurance',
					'insurance_card', 'insurance_requested',
					'copay_asst', 'copay_accum', 'copay_appt', 'est_cost', 'est_cost_details',
					'payor_drug', 'payor_supples_diem', 'payor_nursing',
					'payor_ancillary', 'first', 'prev_provider', 'prev_provider_contact',
					'paperwork_packet', 'disclosure', 'consent', 'transfer_to']

view:
	comment: 'Patient > Billing Assessment Admissions Contact'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Admissions Contact'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']