fields:
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	# Admissions Patient Contact

	admin_spoke_pt:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['admin_spoke_specify']
		view:
			columns: -2
			control: 'radio'
			label: 'Spoke with patient?'

	admin_spoke_specify:
		view:
			columns: 2
			control: 'area'
			label: 'Specify'

	admin_demo:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Demographics Verified?'

	admin_bill_adr:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Billing Address Verified?'

	admin_ship_adr:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Shipping Address Verified?'

	admin_emergency_cont:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Emergency Contact Name and Telephone Number verified?'

	admin_sig_req:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['admin_sig_optout']
		view:
			columns: -2
			control: 'radio'
			label: 'Pt agrees to sign for deliveries?'

	admin_sig_optout:
		model:
			multi: false
			required: true
			source: ['Signature Opt-Out on Delivery Form will be sent to patient']
		view:
			columns: 2
			control: 'checkbox'
			label: 'Opt-Out'

	admin_insurance:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Insurance Discussed?'

	admin_insurance_card:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['admin_insurance_requested']
		view:
			columns: -2
			control: 'radio'
			label: 'Insurance cards?'

	admin_insurance_requested:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Requested?'

	admin_copay_asst:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Copay Assistance Discussed?'

	admin_copay_accum:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Copay Accumulator Discussed?'

	admin_copay_appt:
		view:
			columns: 2
			label: 'Copay Assistance Appointment?'

	admin_est_cost:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['admin_est_cost_details']
		view:
			columns: -2
			control: 'radio'
			label: 'Estimated Cost of 1st Shipment Discussed?'

	admin_est_cost_details:
		view:
			columns: 2
			control: 'area'
			label: 'Things discussed with the patient'
 
	admin_payor_drug:
		view:
			label: 'Payor Name / Drug?'

	admin_payor_supples_diem:
		view:
			label: 'Payor Name / Supplies / Per Diem?'

	admin_payor_nursing:
		view:
			label: 'Payor Name / Nursing?'

	admin_payor_ancillary:
		view:
			label: 'Payor Name / Ancillary Medications?'

	admin_first:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['admin_prev_provider']
		view:
			columns: -2
			control: 'radio'
			label: 'First time receiving this therapy?'

	admin_prev_provider:
		model:
			required: true
		view:
			columns: 2
			label: 'Previous Provider?'

	admin_prev_provider_contact:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Is it okay to contact this provider if we need additional information?'
			columns: 2

	admin_paperwork_packet:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Admission Paperwork Packet Discussed?'

	admin_disclosure:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Designated Disclosure Reviewed?'

	admin_transfer_to:
		view:
			label: 'Transferred to:'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

	progress_note_id:
		model:
			type: 'int'
		view:
			label: 'Progress Note'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr','nurse', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'nurse', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Patient Admissions Contact':
			fields: ['admin_spoke_pt', 'admin_spoke_specify', 'admin_demo', 'admin_bill_adr',
			'admin_ship_adr', 'admin_emergency_cont', 'admin_sig_req', 'admin_sig_optout', 'admin_insurance',
			'admin_insurance_card', 'admin_insurance_requested',
			'admin_copay_asst', 'admin_copay_accum', 'admin_copay_appt', 'admin_est_cost', 'admin_est_cost_details',
			'admin_payor_drug', 'admin_payor_supples_diem', 'admin_payor_nursing',
			'admin_payor_ancillary', 'admin_first', 'admin_prev_provider', 'admin_prev_provider_contact',
			'admin_paperwork_packet', 'admin_disclosure', 'admin_transfer_to']


	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Admission Contact"
	]

view:
	comment: 'Patient > Careplan > Patient Admission Contact'
	grid:
		fields: ['created_on', 'created_by']
		sort: ['-created_on']
	label: 'Patient Admission Contact'
	open: 'read'
