fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	current_therapy:
		model:
			required: true
		view:
			label: 'Current Therapy'

	current_dose:
		model:
			required: true
		view:
			label: 'Current Dose'

	last_therapy_date:
		model:
			type: 'date'
		view:
			label: 'When did the patient receive therapy last?'

	last_therapy_response:
		view:
			label: 'What was the response to treatment?'

	tepezza_warning1:
		model:
			multi: true
			source: ['This medication is called Tepezza (teprotumumab-trbw) and is given IV for treatment of thyroid eye disease. The infusion will take between 60-90 minutes.']
		view:
			control: 'checkbox'
			label: "Tepezza Call Script"
			class: 'list'
			readonly: true

	tepezza_warning1_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you understand what this medication does for your body and how it is administered?'
			control: 'radio'

	tepezza_warning2:
		model:
			multi: true
			source: ['Discuss administration (IV infusion), frequency (every 3 weeks for 8 total doses) and nursing involvement for administration']
		view:
			control: 'checkbox'
			label: "Tepezza Call Script"
			class: 'list'
			readonly: true

	tepezza_warning2_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you have any questions about how it will be administered?'
			control: 'radio'

	diabetes:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['insulin_dependent', 'diabetes_controlled']
			required: true
		view:
			columns: 2
			label: 'History of Diabetes?'
			control: 'radio'

	insulin_dependent:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Are you insulin dependent?'
			control: 'radio'

	diabetes_controlled:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Is your diabetes Controlled?'
			control: 'radio'

	ibd:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			note: 'If yes discuss potential flare of disease state.'
			label: 'History of Inflammatory Bowel Disease (IBD)?'
			control: 'radio'

	tepezza_warning3:
		model:
			multi: true
			source: ['The most common adverse reactions to this medication are muscle spasms (25%), nausea (17%), alopecia (13%), diarrhea (12%) and fatigue (12%).']
		view:
			control: 'checkbox'
			label: "Tepezza Reactions"
			class: 'list'
			readonly: true

	tepezza_warning3_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you have questions about some of the common adverse reactions to the medication?'
			control: 'radio'

	hearing_loss:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['hearing_loss_dr_informed']
			required: true
		view:
			label: 'Counsel on risk of hearing changes. Does patient have preexisting hearing loss?'
			control: 'radio'

	hearing_loss_dr_informed:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Was physician informed of preexisting hearing loss?'
			control: 'radio'

	hydration:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Counsel on important of hydration to reduce side effects?'
			control: 'radio'

	tepezza_warning4:
		model:
			multi: true
			source: ['Anaphylaxis is a rare adverse reaction that can happen with any infusion. Tepezza does not have an increased risk for anaphylaxis over other infusions however this could still occur']
		view:
			control: 'checkbox'
			label: "Tepezza Anaphylaxis"
			class: 'list'
			readonly: true

	tepezza_warning4_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Do you have any questions regarding Anaphylaxis?'
			control: 'radio'

	gender:
		model:
			prefill: ['patient']
			if:
				'Female':
					fields: ['patient_pregnant']
		view:
			offscreen: true
			readonly: true
			label: 'Sex'

	patient_pregnant:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['tepezza_warning5']
		view:
			label: 'Are you currently pregnant or planning to become pregnant?'

	tepezza_warning5:
		model:
			multi: true
			source: ['This medication is contraindicated in pregnancy and should not be used without proper contraception before, during and for at least 6 months following use.']
		view:
			control: 'checkbox'
			label: "Tepezza Pregnancy Warning"
			class: 'list'
			readonly: true

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	name: ['patient_id', 'careplan_id']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Tepezza Pre-Assessment':
			area: 'preassessment'
			fields: ['current_therapy','current_dose','last_therapy_date','last_therapy_response'
			]
		'Tepezza Patient Assessment':
			area: 'admin'
			fields: ['tepezza_warning1', 'tepezza_warning1_confirm', 'tepezza_warning2', 'tepezza_warning2_confirm',
			'diabetes','insulin_dependent', 'diabetes_controlled', 'ibd', 'tepezza_warning3', 'tepezza_warning3_confirm',
			'hearing_loss', 'hearing_loss_dr_informed', 'hydration', 'tepezza_warning4', 'tepezza_warning4_confirm', 'gender',
			'patient_pregnant', 'tepezza_warning5']
view:
	comment: 'Initial Tepezza'
	label: 'Initial Tepezza'