fields:

	# Links
	patient_id:
		model:
			access:
				read: ['nurse', 'pharm']
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		model:
			required: true
			source: 'careplan_order'
			type: 'int'
		view:
			label: 'Order'
			class: 'select_prefill'
			readonly: true
			offscreen: true
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'therapy_id': ['therapy_id']
			]

# Factor
	dob:
		model:
			prefill: ['patient']
			type: 'date'
		view:
			label: 'DOB'
			readonly: true
			offscreen: true
			validate: [{
					name: 'DOBValidator'
			},
			{
				name: 'PediatricCheck'
				age: 18
			}
			]

	pediatric:
		model:
			default: 'No'
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['subform_qol_peds']
					sections: ['Pediatric QOL Survey']
				'No':
					fields: ['subform_qol']
					sections: ['QOL Survey']
		view:
			control: 'radio'
			label: "Pediatric patient?"
			readonly: true
			offscreen: true

	patient_reached:
		model:
			source: ['Yes', 'No']
			if:
				'No':
					fields: ['left_voicemail']
				'Yes':
					fields: ['order_id', 'subform_rarevoice']
					sections: ['Rarevoice Survey']
			default: 'Yes'
		view:
			columns: 4
			control: 'radio'
			label: 'Patient Reached?'

	left_voicemail:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			columns: 4
			control: 'radio'
			label: 'Left Voicemail'

	therapy_id:
		model:
			max: 64
			multi: true
			source: 'list_therapy'
			sourceid: 'code'
			if:
				'Factor':
					fields: ['type_factor']
				'*':
					fields: ['route']
		view:
			columns: 4
			label: 'Therapy'

	route:
		model:
			prefill: ['px_call']
			source: ['Intravenous', 'Subcutaneous']
			if:
				'Subcutaneous':
					fields: ['type_subq']
				'Intravenous':
					fields: ['type']
		view:
			columns: 4
			control: 'radio'
			label: 'Route'

	type:
		model:
			source: ['Initial', 'Ongoing']
			required: true
			if:
				'Initial':
					sections: ['Initial', 'Pharmacist Questions', 'Patient Experience Interventions', 'Initial QOL']
				'Ongoing':
					sections: ['Ongoing', 'Last Rarevoice', 'Outstanding Documents', 'Pharmacist Questions', 'Patient Experience Interventions', 'Last QOL']
		view:
			columns: 4
			control: 'radio'
			label: 'Call Type'

	type_subq:
		model:
			source: ['Initial', 'Ongoing']
			required: true
			if:
				'Initial':
					sections: ['Initial SubQ', 'Pharmacist Questions', 'Patient Experience Interventions', 'Initial QOL']
				'Ongoing':
					sections: ['Ongoing SubQ', 'Last Rarevoice', 'Outstanding Documents', 'Pharmacist Questions', 'Patient Experience Interventions', 'Last QOL']
		view:
			columns: 4
			control: 'radio'
			label: 'SubQ Call Type'

	type_factor:
		model:
			source: ['Initial', 'Ongoing']
			required: true
			if:
				'Initial':
					fields: ['receive_infusion']
					sections: ['Initial Factor',  'Pharmacist Questions','Initial QOL','Patient Experience Interventions']
				'Ongoing':
					fields: ['receive_infusion']
					sections: ['Ongoing Factor', 'Last Rarevoice', 'Outstanding Documents', 'Last QOL','Pharmacist Questions', 'Patient Experience Interventions']
		view:
			columns: 4
			control: 'radio'
			label: 'Factor Call Type'

	introduction:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Introduced yourself and discussed your role in the Patient Experience department. Discussed frequency of PX call and what will be discussed with ongoing calls.'

	receive_infusion:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['when_scheduled']
		view:
			columns: 2
			control: 'radio'
			label: 'Did you receive your infusion?'

	when_scheduled:
		model:
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'When is the infusion scheduled'

	progress_note:
		view:
			control: 'area'
			label: 'Progress Note'

	add_needs:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['px_request']
		view:
			columns: 2
			control: 'radio'
			label: 'Any additional PX needs today?'

	px_request:
		model:
			required: true
		view:
			control: 'area'
			label: 'PX Requests'

	reviewed_px_items:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Reviewed HB Patient Experience items sent with initial delivery.'

	have_questions:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['have_questions_txt']
		view:
			control: 'radio'
			label: 'Does the patient have any questions about the Admission Packet or any of the documents that need to be signed and returned?'

	have_questions_txt:
		model:
			required: true
		view:
			control: 'area'
			label: 'Notes'

	reviewed_esign:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Reviewed method to sign and return documents using the e-signature platform?'

	reviewed_telehealth:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Explained and offered HB telehealth services (HBNow)'

	subform_rarevoice:
		model:
			multi: false
			source: 'clinical_rarevoice_survey'
			type: 'subform'
		view:
			label: 'Rare Voice Survey'

	pharmacist_questions:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['questions_response']
		view:
			columns: -2
			control: 'radio'
			label: 'Any questions for the pharmacist?'

	questions_response:
		model:
			required: true
			source: ['Call transferred to pharmacist', 'Information relayed to the pharmacist']
		view:
			columns: 2
			control: 'radio'
			label: 'Response'

# Ongoing
	last_rarevoice:
		model:
			prefill: ['clinical_rarevoice_survey.date']
			type: 'date'
		view:
			columns: 2
			label: 'Last Rare Voice Survey completed:'
			readonly: true 

	has_outstanding_docs:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['methods_obtain']
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient have outstanding documents?'

	methods_obtain:
		model:
			required: true
			source: ['Paper', 'Text', 'Email']
		view:
			columns: 2
			control: 'radio'
			label: 'Method discussed with patient to obtain documents.'

# SubQ
	receive_teaching:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Did you receive your first teaching visit?'

	receive_add_teaching:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['teaching_received']
				'No':
					fields: ['pt_independant']
		view:
			columns: 2
			control: 'radio'
			label: 'Does the patient have any additional teaching visits?'

	pt_independant:
		model:
			prefill: ['px_call_subq']
			required: true
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['pt_independant_notes']
		view:
			columns: 2
			control: 'radio'
			label: 'Is the patient independent with their SQIG infusions?'

	pt_independant_notes:
		model:
			required: true
		view:
			columns: 2
			label: 'Notes'

	teaching_received:
		model:
			required: true
			source: ['2nd teaching visit', '3rd teaching visit', '4th teaching visit']
		view:
			columns: 2
			control: 'radio'
			label: 'Teaching Received'

	perform_qol:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['pediatric']
				'No':
					fields: ['sent_to_patient', 'date_of_sent']
		view:
			columns: 2
			control: 'radio'
			label: 'Will a QOL survey be completed?'
			validate: [
					name: 'CheckAge'
			]

	sent_to_patient:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['date_of_sent']
		view:
			columns: 2
			label: 'Will a Quality of Life survey be sent to the patient?'
			control: 'radio'
			validate: [
				{
					name: 'PrefillCurrentDate'
					condition:
						sent_to_patient: 'Yes'
					dest: 'date_of_sent'
				}
			]

	date_of_sent:
		model:
			type: 'date'
		view:
			columns: 4
			label: 'Date sent to patient'

	last_qol:
		model:
			prefill: ['clinical_sf12v2.contact_date','clinical_qol_peds.assessment_date']
			type: 'date'
		view:
			columns: 4
			label: 'Last QOL Survey completed:'
			readonly: true 

	subform_qol:
		model:
			multi: false
			type: 'subform'
			source: 'clinical_sf12v2'
		view:
			label: 'Quality of Life Survey'

	subform_qol_peds:
		model:
			multi: false
			source: 'clinical_qol_peds'
			type: 'subform'
		view:
			label: 'Pediatric Quality of Life'

	interventions:
		model:
			required: false
			multi: true
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				intervention_type:
					'static': 'Patient Experience'
		view:
			embed:
				form: 'patient_event'
				selectable: false
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				rank: 'none'
				fields: ['intervention_date', 'intervention_time', 'created_by', 'reason_prx', 'resolved']
				label: ['Date', 'Time', 'By', 'Reason', 'Resolved']
				width: [15, 15, 20, 35, 15]
			label: 'Patient Experience Interventions'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm', 'csr']
		delete:     ['admin']
		read:       ['admin', 'nurse', 'pharm', 'patient', 'csr']
		read_all:   ['admin', 'nurse', 'pharm', 'patient', 'csr']
		request:    []
		update:     ['admin', 'nurse', 'pharm', 'csr']
		review:     []
		update_all: ['admin', 'nurse', 'pharm', 'csr']
		write:      ['admin', 'nurse', 'pharm', 'patient', 'csr']
	bundle: ['patient']
	name: ['patient_id', 'type']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				patient_id: 'patient_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		clinical_rarevoice_survey:
			link:
				patient_id: 'patient_id'
			filter:
				pt_particiates: 'Yes'
			max: 'date'
		clinical_sf12v2:
			link:
				patient_id: 'patient_id'
			filter:
				pt_qol_particiates: 'Yes'
			max: 'contact_date'
		clinical_qol_peds:
			link:
				patient_id: 'patient_id'
			filter:
				pt_qol_particiates: 'Yes'
			max: 'assessment_date'
	sections_group: [
		'Type':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['order_id', 'dob', 'patient_reached', 'left_voicemail', 'therapy_id', 'route', 'type', 'type_subq', 'type_factor']
		'Initial':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['introduction', 'when_scheduled',
			'progress_note', 'add_needs', 'px_request',
			'reviewed_px_items', 'have_questions', 'have_questions_txt',
			'reviewed_esign', 'reviewed_telehealth']
		'Initial Factor':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['introduction', 
			'progress_note', 'add_needs', 'px_request',
			'reviewed_px_items', 'have_questions', 'have_questions_txt',
			'reviewed_esign', 'reviewed_telehealth']
		'Initial SubQ':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['introduction', 'receive_teaching',
			'progress_note', 'add_needs', 'px_request',
			'reviewed_px_items', 'have_questions', 'have_questions_txt',
			'reviewed_esign', 'reviewed_telehealth']
		'Ongoing':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['receive_infusion', 'when_scheduled',
			'progress_note','add_needs', 'px_request']
		'Ongoing Factor':
			fields: ['progress_note','add_needs', 'px_request']
			hide_header: true
			indent: true
			tab: 'Assessment'
		'Ongoing SubQ':
			fields: ['receive_add_teaching', 'pt_independant', 'pt_independant_notes'
			'teaching_received', 'progress_note','add_needs', 'px_request']
			hide_header: true
			indent: true
			tab: 'Assessment'
		'Initial QOL':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['perform_qol','pediatric']
		'Last Rarevoice':
			hide_header: true
			indent: true
			tab: 'Rarevoice Survey'
			fields: ['last_rarevoice']
		'Rarevoice Survey':
			hide_header: true
			indent: true
			tab: 'Rarevoice Survey'
			fields: ['subform_rarevoice']
		'Last QOL':
			hide_header: true
			indent: true
			tab: 'QOL'
			fields: ['last_qol','perform_qol','pediatric', 'sent_to_patient', 'date_of_sent']
		'QOL Survey':
			hide_header: true
			indent: true
			tab: 'QOL'
			fields: ['subform_qol']
		'Pediatric QOL Survey':
			hide_header: true
			indent: true
			tab: 'QOL'
			fields: ['subform_qol_peds']
		'Outstanding Documents':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['has_outstanding_docs', 'methods_obtain']
		'Pharmacist Questions':
			hide_header: true
			indent: true
			tab: 'Assessment'
			fields: ['pharmacist_questions','questions_response']
		'Patient Experience Interventions':
			hide_header: true
			indent: true
			tab: 'Interventions'
			fields: ['interventions']
	]

	transform_post: [
		name: "AutoNote"
		arguments:
			subject: "Patient Experience Navigator"
	]

view:
	comment: 'Patient > Patient Experience Call'
	grid:
		fields: ['created_on', 'created_by', 'type', 'receive_infusion', 'pharmacist_questions']
		width: [15, 15, 15, 15, 40]
		sort: ['-created_on']
	label: 'Patient Experience Call'
	open: 'read'
