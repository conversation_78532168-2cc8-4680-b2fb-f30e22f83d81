fields:
	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	infection_symptoms:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Has the patient had any recent infections, fevers or symptoms of infection?'

	injection_reactions:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Has the patient experienced any injection site reactions?'

	asthma_exacerbation:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['exacerbations_indication', 'steroids_treatment', 'required_er_visit', 'required_hospitalization']
		view:
			columns: -2
			control: 'radio'
			label: 'Has the patient had an asthma exacerbation in the last month?'

	exacerbations_indication:
		model:
			multi: true
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			columns: 2
			control: 'radio'
			label: 'Number of exacerbations'

	steroids_treatment:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Required treatment with steroids?'

	required_er_visit:
		model:
			source: ['No', 'Yes']
		view:
			columns: -2
			control: 'radio'
			label: 'Required ER visit?'

	required_hospitalization:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Required hospitalization?'

	rescue_inhaler:
		model:
			multi: true
			source: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
		view:
			columns: -2
			control: 'radio'
			label: 'How many times have you used your rescue inhaler in the past month?'

	last_lung_function_test:
		view:
			columns: 2
			control: 'area'
			label: 'When was your last lung function test completed?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	name: ['patient_id', 'careplan_id']
	prefill:
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Assessment':
			fields: ['infection_symptoms', 'injection_reactions', 'asthma_exacerbation',
			'exacerbations_indication', 'steroids_treatment', 'required_er_visit',
			'required_hospitalization', 'rescue_inhaler', 'last_lung_function_test']

view:
	block:
		update:
			except: ['admin', 'self']
	comment: 'Patient > Careplan > Ongoing > Asthma'
	grid:
		fields: ['created_on', 'created_by', 'infection_symptoms', 'asthma_exacerbation']
		sort: ['-created_on']
	label: 'Ongoing Assessment: Asthma'
	open: 'read'
