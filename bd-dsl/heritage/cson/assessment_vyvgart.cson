fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	achr_positive:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Anti-acetylcholine receptor (AChR) Antibody positive?'
			control: 'radio'

	current_infection:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			columns: 2
			label: 'Current infection?'
			control: 'radio'

	vaccinations_upd:
		model:
			source: ['No', 'Yes', 'Unknown']
			required: true
		view:
			columns: 2
			label: 'Are you up to date on all your vaccinations?'
			control: 'radio'

	dose_approp:
		model:
			source: ['No', 'Yes']
			required: true
			if:
				'No':
					fields: ['dose_approp_no']
		view:
			columns: 2
			label: 'Is the Vyvgart dose appropriate?'
			control: 'radio'

	dose_approp_no:
		model:
			required: true
		view:
			columns: 2
			label: 'Why not?'

	other_details:
		view:
			label: 'Epinephrine autoinjector brand, dispensing location, and cost?'

	vyvgart_warning1:
		model:
			multi: true
			source: ['Vyvgart is a medication that is indicated for the treatment of generalized MG in adult patients who are anti-acetylcholine receptor antibody positive. It works by blocking a receptor in your body called the neonatal Fc receptor which reduces the IgG antibodies in your body.',
			'Vyvgart is a medication that is administered based on a series of weekly infusions for 4 weeks that make up 1 cycle. This medication will be administered weekly for 4 weeks via IV',
			'Discuss administration, frequency, and nursing involvement. This medication is given via IV and takes about 1 hour to infuse. A nurse would be present during your infusion and will monitor you for 1 hour after your infusion',
			'It is not recommended to administer this medication at the same time as live vaccination due to the reduction in IgG levels in your body when using Vyvgart. Live vaccinations should be administered prior to starting a treatment cycle of Vyvgart based on age-appropriate vaccination schedules',
			'This medication can increase your risk of infections due to the mechanism by which it works in your body',
			'The most common adverse reactions include headache and increased risk of infections',
			'It is a more rare reaction, but this medication also increases the risk of anaphylaxis. This is why we recommend having an Epipen available in your home (discuss preferred retail pharmacy for transfer of Epipen).']
		view:
			control: 'checkbox'
			label: "Vyvgart Warning"
			class: 'list'
			readonly: true

	vyvgart_warning1_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you understand what this medication does for your body?'
			control: 'radio'

	vyvgart_warning2_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you have any questions about how it will be administered, side effects, or risks involved?'
			control: 'radio'

	vyvgart_warning3_confirm:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Do you have a history of headaches?'
			control: 'radio'

	mgadl_participate:
		model:
			source: ['No', 'Yes']
			required: true
		view:
			label: 'Will a Myasthenia Gravis Activities of Daily Living (MG-ADL) be completed on this visit?'
			control: 'radio'
model:
	access:
		create:     []
		create_all: ['admin', 'pharm']
		delete:     ['admin', 'pharm']
		read:       ['admin', 'csr', 'nurse', 'pharm', 'physician']
		read_all:   ['admin', 'csr', 'nurse', 'pharm', 'physician']
		request:    ['csr']
		update:     []
		update_all: ['admin', 'csr', 'pharm']
		write:      ['admin', 'pharm']
	bundle: ['patient', 'careplan']
	indexes:
		many: [
			['patient_id']
			['careplan_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
	sections:
		'Vyvgart Risk Assessment':
			area: 'preassessment'
			fields: ['achr_positive', 'current_infection', 'vaccinations_upd']
		'Vyvgart Calculations':
			area: 'preassessment'
			fields: ['dose_approp', 'dose_approp_no', 'other_details']
		'Vyvgart Questionnaire':
			fields: ['vyvgart_warning1', 'vyvgart_warning1_confirm', 'vyvgart_warning2_confirm', 'vyvgart_warning3_confirm']
		'Questionnaire Participation':
			fields: ['mgadl_participate']
view:
	comment: 'Patient > Careplan > Assessment > Vyvgart'
	grid:
		fields: ['created_on', 'updated_on']
	label: 'Assessment Questionnaire: Vyvgart'
	open: 'read'
