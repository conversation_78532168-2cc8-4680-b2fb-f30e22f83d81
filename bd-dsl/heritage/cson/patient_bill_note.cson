fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	final_type_id:
		model:
			source: 'list_bill_asmt_type'
			sourceid: 'code'
			required: true
		view:
			control: 'radio'
			label: 'Type'
			readonly: true
			offscreen: true

	type_short_id:
		model:
			source: 'list_bill_asmt_type'
			sourceid: 'code'
			required: true
			sourcefilter:
				active:
					'static': 'Yes'
		view:
			label: 'Type'
			class: 'select_prefill'
			columns: 2
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_bill_asmt_type/?limit=1&fields=list&sort=id&page_number=0&filter=code:'
				fields:
					'note': ['note_template']
			]
			validate: [
				{
					name: 'CopyForwardValue'
					overwrite: true
					field: 'final_type_id'
				}
			]

	type_id:
		model:
			source: 'list_bill_asmt_type'
			sourceid: 'code'
			required: true
			sourcefilter:
				active:
					'static': "Yes"
			if:
				'deniedreferral':
					sections: ['Denied Referral', 'Attachments']
					fields: ['subform_dref', 'subform_attachment', 'insurance_id']
				'patientdc':
					sections: ['Attachments']
					fields: ['subform_attachment', 'insurance_id']
				'billaudit':
					sections: ['Billing Audit', 'Attachments']
					fields: ['subform_attachment', 'subform_aud', 'insurance_id']
				'ccpayment':
					sections: ['Credit Card Payment', 'Attachments']
					fields: ['subform_ccpay', 'subform_attachment', 'insurance_id']
				'deliverysigoptout':
					sections: ['Delivery Signature Opt-Out', 'Attachments']
					fields: ['subform_doptout', 'subform_attachment', 'insurance_id']
				'partbdmequal':
					sections: ['Medicare B DME Qualification', 'Attachments']
					fields: ['subform_mcrbdme', 'subform_attachment', 'insurance_id']
				'newadmin':
					sections: ['New Admission', 'Attachments']
					fields: ['subform_nadmin', 'subform_attachment']
				'orderchange':
					sections: ['Order Change', 'Attachments']
					fields: ['subform_ordchg', 'subform_attachment', 'insurance_id']
				'patientcall':
					sections: ['Patient Call', 'Attachments']
					fields: ['subform_ptcall', 'subform_attachment', 'insurance_id']
				'ptestimatecost':
					sections: ['Patient Estimate of Cost', 'Attachments']
					fields: ['subform_ptest', 'subform_attachment', 'insurance_id']
				'paymentplan':
					sections: ['Payment Plan', 'Attachments']
					fields: ['subform_pmtplan', 'subform_attachment', 'insurance_id']
				'pttransfer':
					sections: ['Patient Transfer', 'Attachments']
					fields: ['subform_pttran', 'subform_attachment', 'insurance_id']
				'refcontact':
					sections: ['Referral Contact', 'Attachments']
					fields: ['subform_refcont', 'subform_attachment', 'insurance_id']
				'pttriage':
					sections: ['Patient Triage']
					fields: ['subform_pttriage', 'insurance_id']
				'admissionscontact':
					sections: ['Admissions Patient Contact', 'Attachments']
					fields: ['subform_admin', 'subform_attachment', 'insurance_id']
				'claimstatus':
					sections: ['Claim Status', 'Attachments']
					fields: ['subform_cstatus', 'subform_attachment', 'insurance_id']
				'htcauthtoship':
					sections: ['HTC Authorization to Ship Request', 'Attachments']
					fields: ['subform_htcauth', 'subform_attachment', 'insurance_id']
				'htcapproveship':
					sections: ['HTC Authorization to Ship Approval', 'Attachments']
					fields: ['subform_htcapprove', 'subform_attachment', 'insurance_id']
				'introcall':
					sections: ['Introduction Call']
					fields: ['subform_intro']
				'*':
					sections: ['Attachments']
					fields: ['subform_attachment', 'insurance_id']
		view:
			control: 'radio'
			label: 'Type'
			columns: 2
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/list_bill_asmt_type/?limit=1&fields=list&sort=id&page_number=0&filter=code:'
				fields:
					'note': ['note_template']
			]
			validate: [
				{
					name: 'CopyForwardValue'
					overwrite: true
					field: 'final_type_id'
				}
			]

	full_assessment:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
			if:
				'Yes':
					fields: ['type_id']
				'No':
					fields: ['type_short_id']
		view:
			label: 'Is this going to be a full assessment?'
			control: 'radio'
			columns: 2

	subform_dref:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_dref'
		view:
			label: 'Denied Referral'

	# Billing Audit – Prospective
	subform_aud:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_aud'
		view:
			label: 'Billing Audit'

	# Introduction Call
	subform_intro:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_intro'
		view:
			label: 'Introduction Call'

	# Credit Card Payment
	subform_ccpay:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_ccpay'
		view:
			label: 'Credit Card Payment'

	# Claim Status
	subform_cstatus:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_cstatus'
		view:
			label: 'Claim Status'

	# Delivery Signature Opt-out
	subform_doptout:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_doptout'
		view:
			label: 'Delivery Signature Opt-Out'

	# Medicare B DME Qualification
	subform_mcrbdme:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_mcrbdme'
		view:
			label: 'Medicare B DME Qualification'

	# New Referral
	subform_nadmin:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_nadmin'
		view:
			label: 'New Admission'

	# Order Change
	subform_ordchg:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_ordchg'
		view:
			label: 'Order Change'

	# Patient Call
	subform_ptcall:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_ptcall'
		view:
			label: 'Patient Call'

	# Patient Estimate of Cost
	subform_ptest:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_ptest'
		view:
			label: 'Patient Estimate of Cost'

	# Payment Plan
	subform_pmtplan:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_pmtplan'
		view:
			label: 'Payment Plan'

	# Patient Transfer
	subform_pttran:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_pttran'
		view:
			label: 'Patient Transfer'

	# Referral Contact
	subform_refcont:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_refcont'
		view:
			label: 'Referral Contact'

	# Patient Triage
	subform_pttriage:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_pttriage'
		view:
			label: 'Patient Triage'

	# Admissions Contact
	subform_admin:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_admin'
		view:
			label: 'Admissions Contact'

	#HTC Authorization to Ship Request
	subform_htcauth:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_htcauth'
		view:
			label: 'HTC Authorization to Ship Request'

	#HTC Authorization to Ship Approval
	subform_htcapprove:
		model:
			multi: false
			type: 'subform'
			source: 'assessment_bill_htcauthapv'
		view:
			label: 'HTC Authorization to Ship Approval'

	subform_intervention:
		model:
			source: 'patient_event'
			multi: true
			type: 'subform'
		view:
			label: 'Intervention'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['inter_type', 'physician_contacted', 'resolved', 'continue']
				label: ['Type(s)', 'Dr Contacted?', 'Resolved?', 'Cont Therapy?']
				width: [40, 20, 20, 20]

	subform_attachment:
		model:
			multi: true
			source: 'patient_attachment'
			type: 'subform'
		view:
			label: 'File Attachments'
			note: 'Max 100MB. Only documents, images, and archives supported.'
			grid:
				add: 'flyout'
				hide_cardmenu: true
				edit: false
				fields: ['flag_id', 'patient_file', 'comments']
				label: ['Flag(s)', 'Document / File', 'Comments']
				width: [25, 35, 40]

	insurance_id:
		model:
			source: 'patient_insurance'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				id:
					'dynamic': '{ins_filter}'
				active:
					'static': 'Yes'
				type_id:
					'static': '!SELF'
				billing_method_id:
					'static': ['mm', 'cms1500', 'ncpdp']
			if:
				'*':
					sections: ['Associated Invoices']
					fields: ['embed_invoice']
		view:
			label: 'Insurance'
			columns: 3

	embed_invoice:
		model:
			multi: true
			sourcefilter:
				insurance_id:
					'dynamic': '{insurance_id}'
				order_id:
					'dynamic': '{order_id}'
				void:
					'static': '!Yes'
				status:
					'static': ['On-Hold', 'Open']
		view:
			embed:
				form: 'billing_invoice'
				selectable: true
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['invoice_no', 'status', 'date_of_service', 'total_expected', 'total_paid']
				label: ['Invoice No', 'Status', 'DOS', 'Exp $', 'Paid $']
				width: [20, 20, 20, 20, 20]
			label: 'Invoice(s)'

	pa_id:
		model:
			multi: false
			source: 'patient_prior_auth'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				insurance_id:
					'dynamic': '{insurance_id}'
		view:
			label: 'Prior Authorization'
			columns: 3

	order_id:
		model:
			required: false
			source: 'careplan_order'
			sourcefilter:
				patient_id:
					'dynamic': '{patient_id}'
				status_id:
					'static': '!G'
			if:
				'*':
					fields: ['active_prescriptions', 'pending_prescriptions']
					sections: ['Associated Active Prescriptions', 'Associated Pending Prescriptions']
		view:
			label: 'Order'
			columns: 3
			class: 'select_prefill'
			transform: [
				name: 'SelectPrefill'
				url: '/form/careplan_order/?limit=1&fields=list&sort=id&page_number=0&filter=id:'
				fields:
					'order_no': ['order_no']
					'ins_filter': ['insurance_id']
			]

	ins_filter:
		model:
			source: 'patient_insurance'
			multi: true
			type: 'int'
		view:
			label: 'Filtered order insurance records'
			offscreen: true
			readonly: true

	order_no:
		view:
			label: 'Order #'
			readonly: true
			offscreen: true

	active_rx:
		model:
			multi: true
			sourcefilter:
				order_no:
					'dynamic': '{order_no}'
				discontinued:
					'static': '!Yes'
		view:
			embed:
				form: 'careplan_order_rx'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['brand_name_id', 'type_id', 'dose', 'dose_unit_id', 'frequency_id']
				label: ['Drug', 'Type', 'Dose', 'Unit', 'Freq']
				width: [30, 15, 20, 15, 20]
				selectall: true
			label: 'Active Prescriptions'

	pending_prescriptions:
		model:
			multi: true
			sourcefilter:
				parent_id:
					'dynamic': '{order_id}'
				parent_form:
					'static': 'careplan_order'
				discontinued:
					'static': '!Yes'
		view:
			embed:
				form: 'careplan_order_item'
			grid:
				add: 'none'
				edit: false
				rank: 'none'
				fields: ['brand_name_id', 'type_id', 'dose', 'dose_unit_id', 'frequency_id']
				label: ['Drug', 'Type', 'Dose', 'Unit', 'Freq']
				width: [30, 15, 20, 15, 20]
				selectall: true
			label: 'Pending Prescriptions'

	note:
		view:
			control: 'area'
			label: 'Note'

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	sections_group: [
		'Billing Assessment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['type_id', 'type_short_id', 'final_type_id', 'full_assessment']
		'Denied Referral':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_dref']
		'Billing Audit':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_aud']
		'Credit Card Payment':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ccpay']
		'Claim Status':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_cstatus']
		'Delivery Signature Opt-Out':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_doptout']
		'Medicare B DME Qualification':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_mcrbdme']
		'New Admission':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_nadmin']
		'Order Change':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ordchg']
		'Patient Call':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ptcall']
		'Patient Estimate of Cost':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_ptest']
		'Payment Plan':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_pmtplan']
		'Patient Transfer':
			hide_header: true
			indent: false
			tab: 'Assessment'
			note: 'Documents sent to new pharmacy'
			fields: ['subform_pttran']
		'Referral Contact':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_refcont']
		'Patient Triage':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_pttriage']
		'HTC Authorization to Ship Request':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_htcauth']
		'HTC Authorization to Ship Approval':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_htcapprove']
		'Introduction Call':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_intro']
		'Admissions Patient Contact':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['subform_admin']
		'Note':
			hide_header: true
			indent: false
			tab: 'Assessment'
			fields: ['note']
		'Associated Records':
			hide_header: true
			indent: false
			tab: 'Associated Records'
			fields: ['ins_filter', 'insurance_id', 'pa_id', 'order_id', 'order_no']
		'Associated Invoices':
			hide_header: true
			indent: false
			tab: 'Associated Records'
			fields: ['embed_invoice']
		'Associated Active Prescriptions':
			hide_header: true
			indent: false
			tab: 'Associated Records'
			fields: ['active_rx']
		'Associated Pending Prescriptions':
			hide_header: true
			indent: false
			tab: 'Associated Records'
			fields: ['pending_prescriptions']
		'Interventions':
			hide_header: true
			indent: false
			tab: 'Interventions'
			fields: ['subform_intervention']
		'Attachments':
			hide_header: true
			indent: false
			tab: 'Attachments'
			fields: ['subform_attachment']
	]

view:
	comment: 'Patient > Billing Assessment'
	find:
		basic: ['created_on', 'created_by', 'final_type_id', 'archived']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by', 'final_type_id']
		sort: ['-created_on']
	label: 'Billing Assessment'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']