fields:

	patient_stage:
		model:
			source: ['Referral Received Missing Info', 'Referral Received', 'Benefits Investigation Completed', 'Patient Discharged', 'Discontinued by Prescriber', 'Patient Deceased',  'Prior Auth Submission Pending', 'Prior Authorization Submitted', 'Prior Auth Requires Additional Info', 
			'Prior Auth Submitted with Additional Info', 'Prior Auth Follow Up', 'Appeal Submitted', 'Appeal Required', 'Nursing Coordination', 'On Service', 'Forced to Competitor', 'Denied-Medical Necessity', 'No Coverage-Closed', 'Patient Refused Therapy', 'Therapy Completed', 'Appeal Denied', 'Patient Assistance Required',
			'Shipping - Delivery Confirmation Pending', 'Shipping - Delivered to Patient home', 'Shipping - Delivered to Patient work', 'Shipping - Delivered per Patient instructions', 'Shipping - Package left at Patient door', 'Shipping – Deviation, see notes']
		view:
			columns: 3
			label: 'Stage'
	
	phone_cell:
		view:
			columns: 4

	phone_home:	
		view:
			columns: 4
	
	phone_work:
		view:
			columns: 4

	phone_other:
		model:
			max: 21
		view:
			columns: 4
			format: 'us_phone'
			label: 'Other Phone'

	phone_primary:
		model:
			max: 16
			source: ['Cell Phone', 'Home Phone', 'Work Phone', 'Other Phone']
		view:
			control: 'select'
			columns: 3
			label: 'Primary Phone'

	cust_phone_message:
		model:
			multi: true
			max: 16
			source: ['Cell Phone', 'Home Phone', 'Work Phone', 'Other Phone']
		view:
			control: 'select'
			columns: 3
			label: 'Preferred number for leave a message'
	
	cust_call_perm:
		model:
			source: ['No', 'Yes']
		view:
			control: "radio"
			columns: 2
			label: "Does patient give Heritage Biologics permission to communicate with them about their health at the following telephone numbers?"

	cust_health_perm:
		model:
			source: ['No', 'Yes']
		view:
			control: "radio"
			columns: 2
			label: "Does patient give Heritage Biologics permission to discuss their health with the following individuals?"

	cust_phone_text:
		model:
			source: ['Cell Phone', 'Home Phone', 'Work Phone', 'Other Phone']
			max: 21
		view:
			control: 'select'
			columns: 3
			format: 'us_phone'
			label: 'Preferred Phone To Receive Text'
	
	cust_elec_perm:
		model:
			source: ['No', 'Yes']
		view:
			control: "radio"
			label: "Does patient request that Heritage Biologics communicate with them using electronic forms of communication such as email, text, or other electronic means?"

	cust_dpoa:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_dpoa_who']
		view:
			control: "radio"
			columns: 2
			label: "Does the patient have a DPOA?"

	cust_dpoa_who:
		model:
			required: true
		view:
			columns: 2
			label: "Who?"

	created_by:
		model:
			source: 'user'
			type: 'int'
			if:
				'*':
					fields: ['measurement_log', 'embed_contacts', 'embed_address']
					sections: ['Measurement Log', 'Address Embedded', 'Emergency Contacts Embedded']
				'!':
					fields: ['subform_contacts', 'subform_address']
					sections: ['Address Subform', 'Emergency Contacts Subform']
		view:
			label: 'Created By'
			readonly: true
			offscreen: true

model:
	sections:
		'Chart State':
			hide_header: true
			indent: false
			tab: 'Demographics'
			fields: ['created_by', 'status_id', 'patient_stage', 'cancellation_reason', 'death_date', 'death_confirm', 'death_cause', 'patient_tag_id',
			'is_test', 'is_prn', 'is_prn_set_date']
		'Name':
			hide_header: true
			indent: false
			tab: 'Demographics'
			fields: ['firstname', 'lastname', 'middlename']
		'Identity':
			hide_header: true
			indent: false
			tab: 'Demographics'
			fields: ['mrn', 'ssn', 'dob', 'gender', 'identify_gender',
			'language', 'marital_status', 'diabetic', 'diet_id', 'activity_id',
			'code_status', 'code_status_other', 'health_declaration',
			'power_of_attorney_details', 'external_ids','cust_dpoa', 'cust_dpoa_who']
		'Measurement Log':
			hide_header: true
			indent: false
			tab: 'Demographics'
			fields: ['measurement_log']
		'Care Team':
			indent: false
			tab: 'Demographics'
			fields: ['careteam_pharmacist_id', 'careteam_nurse_id', 'careteam_pt_advocate_id']
		'Contact':
			indent: false
			hide_header: true
			tab: 'Contacts'
			fields: [ 'cust_elec_perm', 'phone_cell', 'phone_home', 'phone_work', 'phone_other', 'phone_primary', 'cust_phone_text', 'cust_phone_message', 'email']
		'Health Contacts Permissions':
			indent: false
			hide_header: true
			tab: 'Contacts'
			fields: ['cust_call_perm', 'cust_health_perm']
		'Emergency Contacts Embedded':
			indent: false
			hide_header: true
			tab: 'Contact'
			fields: ['embed_contacts']
		'Emergency Contacts Subform':
			indent: false
			hide_header: true
			tab: 'Contact'
			fields: ['subform_contacts']
		'Alerts':
			indent: false
			hide_header: true
			tab: 'Alerts'
			fields: ['clinical_alert', 'billing_alert']
		'Address Embedded':
			indent: false
			hide_header: true
			tab: 'Address'
			fields: ['embed_address']
		'Address Subform':
			indent: false
			hide_header: true
			tab: 'Address'
			fields: ['subform_address']
		'Service':
			hide_header: true
			indent: false
			tab: 'Service'
			fields: ['site_id', 'category_id', 'team_id',  'referral_date',
			'referral_source_id', 'referrer_id', 'territory_id', 'facility_id']
		'Account':
			hide_header: true
			indent: false
			tab: 'Service'
			fields: ['user_id']
