fields:

	site_asymptomatic:
		model:
			required: false

	site_asymptomatic_details:
		model:
			required: false

	site_irritation:
		model:
			required: false

	site_cond:
		model:
			max: 32
			source: ['Clean and Dry', 'Drainage']
			if:
				'Drainage':
					fields: ['site_cond_details']
		view:
			control: 'radio'
			label: 'Site Condition'
			columns: -2

	site_cond_details:
		model:
			max: 32
			multi: true
			source: ['Red', 'Tender', 'Bruised', 'Infiltrated', 'Sutures']
			if:
				'Red':
					fields: ['ointment', 'ointment_com']
				'Tender':
					fields: ['ointment', 'ointment_com']
				'Infiltrated':
					fields: ['ointment', 'ointment_com']
		view:
			control: 'checkbox'
			label: 'Site Condition Details'
			columns: 2

	problems_noted:
		model:
			max: 3
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['complications']
		view:
			control: 'radio'
			label: 'Were any IV complications noted?'
			columns: -2

	complications:
		model:
			max: 32
			multi: true
			source: ['Migration/Malposition', 'Dislodgement', 'Access Device Occlusion', 'Skin Integrity Impairment', 'Suspected Access Device Related Bloodstream Infection', 'Damage/Breakage', 'Suspected Thrombosis/DVT', 'Tender', 'Erythema', 'Edema', 'Drainage', 'Leaking', 'Infiltration', 'Phlebitis', 'Occlusion', 'Other']
			if:
				'Phlebitis':
					fields: ['phlebitis', 'complications_interventions', 'complications_outcomes']
				'Other':
					fields: ['complications_other', 'complications_interventions', 'complications_outcomes']
				'Suspected Access Device Related Bloodstream Infection':
					fields: ['complications_blood_infection_accessed_by', 'complications_blood_infection_confirmed', 'complications_interventions', 'complications_outcomes']
				'*':
					fields: ['complications_interventions', 'complications_outcomes']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Complications'
			columns: 2

	complications_other:
		model:
			required: true
		view:
			control: 'area'
			label: 'Complications Other'

	complications_blood_infection_accessed_by:
		model:
			multi: true
			required: true
			source: ['Patient/caregiver', 'Home infusion company', 'Home care agency', 'Physician', 'Out-patient clinic', 'Hospital', 'Other', 'None']
		view:
			control: 'checkbox'
			note: 'Select any that apply'
			label: 'Identify all provider types that accessed the catheter during the 2 days prior to the date of initial sign of infection including:'
			columns: 2

	complications_blood_infection_confirmed:
		model:
			required: true
			source: ['Yes', 'No', 'Unknown']
		view:
			control: 'radio'
			label: 'Was the suspected access-device related infection laboratory confirmed?'
			columns: 2

	complications_interventions:
		model:
			multi: true
			source: ['Provided additional teaching/education', 'Access device repaired/repositioned', 'Access device removed', 'Systemic anti-infectives administered', 'De-clotting procedure performed', 'Other adjunctive treatment', 'Discontinued home infusion therapy', 'Unscheduled nursing visit performed', 'Unplanned hospitalization', 'Emergency department use', 'Cultures drawn', 'Additional tests (x-ray, labs)', 'Access device replaced', 'Other']
			if:
				'Other':
					fields: ['complications_interventions_other']
		view:
			control: 'checkbox'
			note: 'Select any problems that apply'
			label: 'Complication Interventions'
			columns: 2

	complications_interventions_other:
		model:
			required: true
		view:
			label: 'Complication Interventions Other'
			columns: 2

	complications_outcomes:
		model:
			required: true
			source: ['Continuation of home infusion services with no interruption',
			'Interruption of services, followed by resumption of care with therapy changes',
			'Interruption of services, followed by resumption of care without therapy changes', 'Home Infusion services discontinued']
		view:
			label: 'Device Complication Outcomes'
			columns: -2

	comments:
		model:
			max: 4086
		view:
			control: 'area'
			label: 'IV Comments/Pertinent History'

	ointment:
		model:
			max: 32
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'AB Ointment?'
			columns: -2

	ointment_com:
		model:
			max: 1024
		view:
			control: 'area'
			label: 'AB Ointment Comment'
			columns: 2

	dress_changed:
		model:
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['dress_change_used', 'cap_changed','discontinued', 'tube_changed']
				'No':
					fields: ['dress_change_used','cap_changed','discontinued', 'tube_changed']
				'NA':
					fields: ['dress_change_used', 'tube_changed']
		view:
			control: 'radio'
			label: 'Was the dressing initiated/changed using sterile technique?'
			columns: -2

	dress_change_used:
		model:
			multi: true
			source: ['Silvasorb Disc', 'Bio Patch', 'Opsite', 'Tegaderm', 'IV 3000', 'Duoderm', '3M PICC Securement Device']
		view:
			control: 'checkbox'
			label: 'Dressing Changed Used'
			columns: 2

	cap_changed:
		model:
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['cap_changed_lumens']
		view:
			control: 'radio'
			label: 'Were any cap(s) initiated/changed?'
			columns: 2

	cap_changed_lumens:
		model:
			type: 'int'
			required: true
			default: 1
			min: 1
			max: 5
		view:
			label: 'Caps Changed Lumens'
			note: 'Please add a value from 1 to 5'
			columns: 2

	tube_changed:
		model:
			max: 32
			source: ['No', 'Yes', 'NA']
			if:
				'Yes':
					fields: ['tube_changed_lumens']
		view:
			control: 'radio'
			label: 'Was any extension tubing initiated/changed?'
			columns: 2

	tube_changed_lumens:
		model:
			type: 'int'
			required: true
			min: 1
			max: 5
		view:
			label: 'Extension Tubing Changed Lumens'
			note: 'Please add a value from 1 to 5'
			columns: 2

	discontinued:
		model:
			max: 64
			multi: true
			source: ['PICC', 'Midline', 'Non-tunneled CVC', 'PIV', 'Port-a-cath decannulation (deaccessed)']
			if:
				'Non-tunneled CVC':
					fields: ['discontinued_protocol', 'discontinued_reason']
				'PICC':
					fields: ['discontinued_reason', 'discontinued_picc_length', 'discontinued_picc_bleed', 'discontinued_picc_teach']
				'Midline':
					fields: ['discontinued_reason']
				'PIV':
					fields: ['discontinued_reason']
				'Port-a-cath decannulation (deaccessed)':
					fields: ['discontinued_reason']
		view:
			control: 'checkbox'
			note: 'Select any items that were discontinued'
			label: 'Discontinued'
			columns: 2

	discontinued_protocol:
		model:
			max: 1024
			required: true
		view:
			label: 'Discontinued Non-tunneled CVC Per Protocol'
			columns: 2

	discontinued_reason:
		model:
			max: 64
			required: true
			source:
				rotated: 'Site rotated'
				thrombosis: 'Occluded'
				therapy_completed: 'Therapy completed'
				infusion_completed: 'Infusion completed'
				presc_notified: 'Prescriber notified'
				infiltration: 'Infiltration'
				infection: 'Catheter infection'
				port_access: 'Port Reaccess'
				needle_change: 'Needle Change'
		view:
			control: 'radio'
			label: 'Reason For Discontinuation'
			columns: 2

	discontinued_picc_length:
		model:
			rounding: 0.01
			type: 'decimal'
		view:
			class: 'numeral'
			format: '0,0.[000000]'
			label: 'PICC/CL length (cm)'
			columns: 2

	discontinued_picc_bleed:
		model:
			max: 32
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Bleeding at insertion upon removal?'
			columns: -2

	discontinued_picc_teach:
		model:
			max: 32
			source: ['No', 'Yes']
		view:
			control: 'radio'
			note: 'report to ER if excessive bleeding at site/swelling of arm, keep dressing clean and dry for 24 hours'
			label: 'Teaching completed regarding care of removal site?'
			columns: -2

model:
	prefill:
		patient:
			link:
				id: 'patient_id'
		careplan:
			link:
				id: 'careplan_id'
			filter:
				active: 'Yes'
			max: 'created_on'
		encounter_access:
			link:
				careplan_id: 'careplan_id'
			max: 'created_on'
	sections:
		'Details':
			hide_header: true
			indent: false
			fields: ['site_cond', 'site_cond_details',
			'problems_noted', 'sticks', 'complications',
			'complications_other', 'complications_blood_infection_accessed_by',
			'complications_blood_infection_confirmed', 'complications_interventions',
			'complications_interventions_other', 'complications_outcomes',
			'phlebitis', 'comments']
		'Catheter Maintenance':
			fields: ['ointment', 'ointment_com', 'dress_changed',
			'dress_change_used','cap_changed', 'cap_changed_lumens', 'tube_changed', 'tube_changed_lumens',
			'discontinued', 'discontinued_protocol', 'discontinued_reason',
			'discontinued_picc_length',
			'discontinued_picc_bleed', 'discontinued_picc_teach']
view:
	comment: 'Patient > Careplan > Encounter > Catheter Access'
	grid:
		fields: ['site_cond', 'phlebitis', 'site_asymptomatic', 'site_irritation']
	label: 'Catheter Access'
