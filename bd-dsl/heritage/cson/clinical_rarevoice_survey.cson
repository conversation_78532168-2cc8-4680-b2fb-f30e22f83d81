fields:

	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	pt_particiates:
		model:
			max: 32
			source: ['No', 'Yes']
			required: true
			if:
				'Yes':
					sections: ['Rare Voice Survey']
				'No':
					fields: ['cust_pt_refuses', 'sent_to_patient', 'date_of_sent']
		view:
			control: 'radio'
			label: 'Will a Rare Voice Survey be completed today?'

	cust_pt_refuses:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['cust_pt_refuses_date', 'cust_pt_refuses_confirmation']
		view:
			control: 'radio'
			label: 'Is the patient refusing to complete?'

	cust_pt_refuses_date:
		model:
			type: 'date'
			required: true
		view:
			label: 'Date Refused'
			template: '{{now}}'
	
	sent_to_patient:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['date_of_sent']
		view:
			label: 'Will a Rare Voice survey be sent to the patient?'
			control: 'radio'
			validate: [
					name: 'PrefillCurrentDate'
					condition:
						sent_to_patient: 'Yes'
					dest: 'date_of_sent'
			]

	date_of_sent:
		model:
			type: 'date'
		view:
			label: 'Date sent to patient'


	cust_pt_refuses_confirmation:
		model:
			required: true
			multi: false
			source: ['OK']
		view:
			control: 'checkbox'
			label: 'This will be documented that patient is refusing completion of the Rare Voice Survey. Please try to collect each quarter. Please notify Px of this refusal'

	date:
		model:
			access:
				read: ['nurse', 'pharm']
			required: true
			type: 'date'
		view:
			columns: 2
			label: 'Survey Date'

	type:
		model:
			required: true
			source: ['Initial', 'Ongoing']
			if:
				'Initial':
					fields: ['pharmacist_option', 'disease_information']
		view:
			columns: 2
			control: 'radio'
			label: 'Survey Type'

	felt_informed:
		model:
			required: false
			multi: false
			source: ['Strongly Agree', 'Agree', 'Disagree', 'Strongly disagree']
		view:
			control: 'checkbox'
			label: 'When I left the hospital (or other healthcare provider), I felt well informed and confident in my treatment.'
			offscreen: true
			readonly: true

	careteam_introduced:
		model:
			required: false
			multi: false
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Members of your care team at Heritage Biologics introduced themselves and explained their role in your care?'
			offscreen: true
			readonly: true

	aware_of_needs:
		model:
			required: true
			multi: false
			source: ['Strongly Agree', 'Agree', 'Disagree', 'Strongly disagree']
		view:
			class: 'checkbox checkbox-4'
			control: 'checkbox'
			label: 'All members of Heritage Biologics staff were pleasant, courteous and sensitive to my needs.'

	pharmacist_option:
		model:
			required: true
			multi: false
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'With Heritage Biologics, I was given the option to talk to a pharmacist?'

	disease_information:
		model:
			required: true
			multi: false
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'My healthcare provider gave me easy to understand information regarding my disease state and my treatment'

	friends_or_family_score:
		model:
			required: true
			source: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
			if:
				'10':
					fields: ['high_rank']
				'9':
					fields: ['high_rank']
				'8':
					fields: ['medium_rank']
				'7':
					fields: ['medium_rank']
				'6':
					fields: ['medium_rank']
				'5':
					fields: ['medium_rank']
				'*':
					fields: ['low_rank']
		view:
			control: 'radio'
			note: '10 = Most likely to recommend, 0 = Would not recommend'
			label: 'How likely is it that you would recommend Heritage Biologics to a friend or family member, if they needed this care?'
			validate: [
					name: 'ConvertStringToInt'
					field: 'friends_or_family'
			]

	friends_or_family:
		model:
			type: 'int'
			min: 1
			max: 10
		view:
			label: 'Promoter Score'
			readonly: true
			offscreen: true

	high_rank:
		view:
			control: 'area'
			label: 'What did you like about your experience today and/or would you like to recognize anyone?'

	medium_rank:
		view:
			control: 'area'
			label: 'How could we improve your experience?'

	low_rank:
		view:
			control: 'area'
			label: 'Did something go wrong with your experience today? How can we fix it?'

	veinviewer:
		model:
			source: ['Yes', 'No']
			if:
				'Yes':
					fields: ['thorough_evaluation', 'skill_confidence', 'positive_impact', 'veinviewer_comments']
		view:
			control: 'radio'
			label: 'Was the VeinViewer utilized for IV access during your visit?'
			offscreen: true
			readonly: true

	thorough_evaluation:
		model:
			required: false
			source: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree']
		view:
			control: 'radio'
			label: 'The VeinViewer allowed my nurse to provide a more thorough evaluation of my vein options.'
			offscreen: true
			readonly: true

	skill_confidence:
		model:
			required: false
			source: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree']
			if:
				'*':
					fields: ['skill_confidence_comment']
		view:
			control: 'radio'
			label: 'With the VeinViewer, I am more confident in the skills of my nurse.'
			offscreen: true
			readonly: true

	skill_confidence_comment:
		view:
			label: 'Additional Comments'
			offscreen: true
			readonly: true

	positive_impact:
		model:
			required: false
			source: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree']
			if:
				'*':
					fields: ['skill_confidence_comment']
		view:
			control: 'radio'
			label: 'I feel that the VeinViewer had a positive impact on my patient experience.'
			offscreen: true
			readonly: true

	veinviewer_comments:
		view:
			control: 'area'
			label: 'VeinViewer Comments'
			offscreen: true
			readonly: true

	comments:
		view:
			control: 'area'
			label: 'How else can we be a resource for you?'

	legacy_data:
		model:
			type: 'json'
		view:
			label: 'Legacy Data'
			readonly: true
			offscreen: true

	envoy_external_id:
		model:
			type: 'int'
		view:
			label: 'Envoy External Id'
			readonly: true
			offscreen: true

model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm', 'patient']
		delete:     ['admin']
		read:       ['admin', 'nurse', 'pharm', 'patient']
		read_all:   ['admin', 'nurse', 'pharm', 'patient']
		request:    []
		update:     ['admin']
		review:     []
		update_all: ['admin']
		write:      ['admin', 'nurse', 'pharm', 'patient']
	bundle: ['patient']
	name: ['patient_id', 'date']
	indexes:
		many: [
			['patient_id']
		]
	prefill:
		patient:
			link:
				id: 'patient_id'

	sections:
		'Rare Voice Survey Participation':
			fields: ['pt_particiates', 'cust_pt_refuses', 'cust_pt_refuses_date', 'cust_pt_refuses_confirmation', 'sent_to_patient', 'date_of_sent']
		'Rare Voice Survey':
			fields: ['date', 'type', 'disease_information','aware_of_needs',
			'pharmacist_option', 'friends_or_family_score',
			'friends_or_family', 'high_rank', 'medium_rank', 'low_rank', 'comments']

view:
	comment: 'Patient > Rare Voice Survey'
	grid:
		fields: ['created_on', 'created_by', 'date', 'cust_pt_refuses']
		sort: ['-created_on', '-date']
	label: 'Rare Voice Survey'
	open: 'read'
