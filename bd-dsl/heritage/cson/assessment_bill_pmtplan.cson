fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	balance:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			label: 'Current Outstanding Patient Balance $'
			class: 'numeral money'
			format: '$0,0.00'
			columns: 3

	amount:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			label: 'Amount $'
			class: 'numeral money'
			format: '$0,0.00'
			columns: 3

	terms:
		view:
			label: 'Terms'
			columns: 3

	dom:
		view:
			label: 'Day of Month'
			columns: 3

	date:
		model:
			type: 'date'
		view:
			label: 'Date'
			columns: 3

	approved:
		view:
			label: 'Approved By'
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Payment Plan':
			hide_header: true
			indent: false
			fields: ['balance', 'amount', 'terms', 'dom', 'date', 'approved']

view:
	comment: 'Patient > Billing Assessment Payment Plan'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Payment Plan'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']