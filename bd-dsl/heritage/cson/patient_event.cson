fields:

	# Links
	patient_id:
		model:
			access:
				read: ['nurse', 'pharm']
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	careplan_id:
		model:
			required: true
			source: 'careplan'
			type: 'int'
		view:
			label: 'Careplan Id'

	order_id:
		view:
			note: 'PHARMACISTS ONLY. This affects Turn Around Time'

	order_item_id:
		view:
			note: 'PHARMACISTS ONLY. This affects Turn Around Time'

	delivery_tick_id:
		view:
			note: 'PHARMACISTS ONLY. This affects Turn Around Time'

	inter_type:
		model:
			required: true
			multi: true
			source: 'list_intervention_type'
			sourceid: 'code'
			sourcefilter:
				active:
					'static': 'Yes'
			if:
				'Clinical':
					sections: ['Pharmacy Interventions']
					fields: ['subform_pharmacy', 'order_id', 'delivery_tick_id']
				'PtExperience':
					fields: ['reason_prx']
				'PtEducation':
					fields: ['education_who', "education_type", 'education_type_details', 'order_id', 'delivery_tick_id']
				'HBNow':
					fields: ['hbnow_status', 'hbnow_comment', 'order_id', 'delivery_tick_id']
				'PtServices':
					fields: ['reason_services', 'order_id', 'delivery_tick_id']
				'PtComplaint':
					fields: ['complaint', 'intervention_time', 'complaint_resolution_datetime', 'complaint_resolution', 'complaint_px', 'order_id', 'delivery_tick_id']
				'SvcRecovery':
					note: 'For Management Only'
					fields: ['order_id', 'delivery_tick_id']
				'CnclRounding':
					fields: ['clinical_rounding', 'order_id', 'delivery_tick_id']
				'Reporting':
					fields: ['reporting_type', 'reporting_details', 'order_id', 'delivery_tick_id']
		view:
			columns: 1
			class: 'checkbox checkbox-3'
			control: 'checkbox'
			label: 'Type'

	subform_pharmacy:
		model:
			multi: true
			type: 'subform'
			required: true
			source: 'patient_event_pharmacy'
		view:
			grid:
				add: 'inline'
				edit: true
				fields: ['intervention_type', 'outcome_1', 'outcome_2']
				label: ['Intervention', 'Perc Outcome 1', 'Perc Outcome 2']
				width: [50, 25, 25]
			label: 'Pharmacy Interventions'

	reporting_type:
		model:
			multi: false
			source: ['FDA', 'Medwatch', 'ISMP', 'Board of Pharmacy', 'Manufacture', 'Recall', 'Other']
			required: true
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Reporting Type'

	reporting_details:
		view:
			label: 'Reporting Description/Lot Number'

	intervention_date:
		model:
			required: true
			type: 'date'
		view:
			columns: 4
			label: 'Date'

	intervention_time:
		model:
			required: true
			type: 'time'
		view:
			columns: 4
			label: 'Time'

	clinical_rounding:
		model:
			source: ['Quality of Life']
			required: false
		view:
			columns: 2
			control: 'checkbox'
			label: 'Clinical Rounding Subtype'

	complaint:
		model:
			required: true
		view:
			columns: 2
			control: 'area'
			label: 'Patient Complaints'

	complaint_resolution_datetime:
		model:
			required: false
			type: 'datetime'
		view:
			columns: 4
			label: 'Complaint Resolution Datetime'

	complaint_resolution:
		model:
			required: false
		view:
			columns: 2
			control: 'area'
			label: 'Complaint Resolution'

	complaint_px:
		model:
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['complaint_px_sign']
			required: true
		view:
			columns: 2
			control: 'radio'
			label: 'Notified PX for review?'

	complaint_px_sign:
		model:
			type: 'json'
		view:
			columns: 2
			control: 'esign'
			label: 'PX Reviwer E-Signature'

	physician_contacted:
		model:
			source: ["No", "Yes"]
			if:
				'Yes':
					fields: ['physician_contacted_date', 'physician_contacted_time']
		view:
			control: 'radio'
			label: 'Physician Contacted?'
			columns: 2

	physician_contacted_date:
		model:
			type: 'date'
		view:
			label: 'Physician Contacted Date'
			columns: 4

	physician_contacted_time:
		model:
			type: 'time'
		view:
			label: 'Physician Contacted Time'
			columns: 4

	hbnow_status:
		model:
			source: ['Offered', 'Enrolled', 'Case Generated']
		view:
			columns: 2
			control: 'radio'
			label: 'HBNow Status'

	hbnow_comment:
		view:
			columns: 2
			control: 'area'
			label: 'HBNow Comment'

	reason_prx:
		model:
			multi: true
			required: true
			source: ["Ancillary Resources",  "Appointment Coordination", "Back to school card", "Birthday Card", "Certificate of Achievement", "Copay Assistance",
			"Discharge from service card", "Financial Assistance Coordination",  "Foundation Assistance", 'Get Well Card', "In-service", "Insurance Assistance", "Infusion schedule change",
			"Items Reimbursed", "Manufacturer Assistance Coordination", "Mental Health", "Other", "Provider Search", "Provider/Department Collaboration","Patient Assistance",  "Premium Assistance", "PX Bag", 
			"Scholarship Assistance", "Snapshot",'Sympathy Card',"Travel Letter", "Transportation Assistance"]
			if:
				"Provider/Department Collaboration":
					fields: ["collaboration_type"]
				'Patient Assistance':
					fields: ['pa_type']
				'Other':
					fields: ['reason_prx_other']
		view:
			class: 'checkbox checkbox-3'
			control: 'checkbox'
			label: 'Patient Experience Interventions'

	pa_type:
		model:
			required: false
			source: ["Free Trial", "Gap Program (Free drug)"]
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Patient Assistance Type'

	reason_prx_other:
		view:
			columns: 2
			control: 'area'
			label: 'Patient Experience Other'

	reason_services:
		model:
			multi: true
			required: true
			source: ["Insurance Verification", "Collaboration with Physician", "Admission of Patient",
			"Denied / Cancellation of Referral", "Patient Contact", "Financial Hardship",
			"Prior Authorization", "Patient Transfer", "Patient Triage", "Copay Assistance", "Claim Status", "NDC Not Covered", "Patient Call", "Other"]
			if:
				"Prior Authorization":
					fields: ['prior_auth', 'prior_auth_details']
				"Patient Triage":
					fields: ['triage', 'triage_details']
				"Patient Transfer":
					fields: ['transfer', 'transfer_details']
				"Copay Assistance":
					fields: ['copay_type']
				"Claim Status":
					fields: ['claim_status']
				'Other':
					fields: ['reason_services_other']
		view:
			class: 'checkbox checkbox-3'
			control: 'checkbox'
			label: 'Patient Services Interventions'

	reason_services_other:
		view:
			columns: 2
			control: 'area'
			label: 'Patient Services Other'

	prior_auth:
		model:
			required: false
			source: ["Submission", "Approval", "Denial", "Follow Up", "Appeal", "Peer to Peer"]
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Prior Auth'

	prior_auth_details:
		view:
			columns: 2
			control: 'area'
			label: 'Prior Auth Details'

	triage:
		model:
			multi: false
			required: true
			source: ["Intial", "Follow Up", "Confirmed"]
		view:
			columns: 2
			control: 'radio'
			label: 'Patient Triage'

	triage_details:
		view:
			columns: 2
			label: 'Patient Triage Details'

	transfer:
		model:
			required: true
			source: ["Intial", "Follow Up", "Confirmed"]
		view:
			columns: 2
			control: 'radio'
			label: 'Patient Transfer'

	transfer_details:
		view:
			columns: 2
			label: 'Patient Transfer Details'

	copay_type:
		model:
			source: ['Manufacturer Enrollment', 'Manufacturer Billing', 'Foundation Enrollment', 'Foundation Billing']
		view:
			columns: 2
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Copay Assistance Type'

	claim_status:
		model:
			source: ['Follow up', 'Denied', 'Overpayment', 'Refund', 'Audited']
		view:
			columns: 2
			control: 'radio'
			label: 'Claim Status'

	collaboration_type:
		model:
			required: true
			multi: true
			source: ["PX", "RX", "NSG", "Billing", "ER", "MD"]
		view:
			columns: 2
			class: 'checkbox checkbox-3'
			control: 'checkbox'
			label: 'Collaboration Type'

	education_who:
		model:
			multi: true
			required: true
			source: ["Caregiver/Family", "Patient"]
		view:
			class: 'checkbox checkbox-2'
			columns: 2
			control: 'checkbox'
			label: 'Education Given To'

	education_type:
		model:
			required: true
			multi: true
			source: ["Handout", "Portal Video", "Portal Handout", 'Verbal Education']
		view:
			class: 'checkbox checkbox-2'
			columns: 2
			control: 'checkbox'
			label: 'Education Type'

	education_type_details:
		view:
			columns: 2
			label: 'Education Material Details'

	line_type:
		model:
			required: true
			source: ["Central", "Peripheral", 'Subcutaneous']
		view:
			columns: 2
			control: 'radio'
			label: 'Line Type'

	resolved:
		model:
			source: ['No', 'Yes']
			default: 'Yes'
		view:
			columns: 2
			control: 'radio'
			label: 'Resolved?'

	continue:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Patient to continue on Therapy?'

	physician_accepted:
		model:
			required: true
			source: ["No", "Yes"]
			if:
				'No':
					fields: ['physician_accepted_details']
		view:
			control: 'radio'
			label: 'Physician accepted outcome?'

	physician_accepted_details:
		model:
			required: true
		view:
			label: 'Details'

	#Parent form overrides
	resolution_date:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	other_details:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	outcome_nursing:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
	
	outcome_shipping:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	outcome_pharmacy:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	complaint_type:
		model:
			required: false
			if:
				'*':
					fields: ['reviewed_by']

	category:
		model:
			required: false
			if:
				'*':
					fields: ['reviewed_by']

	ae_date:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	ae_type:
		model:
			required: false
			if:
				'*':
					fields: ['reviewed_by']
		view:
			readonly: true
			offscreen: true

	nursing_issue_complaint:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	pharmacy_issue_complaint:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	nursing_issue_ae_code:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	description_pharmacy:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	description_shipping:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
	affect_health:
		model:
			required: false
		view:
			readonly: true
			offscreen: true

	cold_chain_management:
		model:
			required: false
		view:
			readonly: true
			offscreen: true
model:
	access:
		create:     []
		create_all: ['admin', 'nurse', 'pharm', 'csr']
		delete:     ['admin']
		read:       ['admin', 'nurse', 'pharm', 'patient', 'csr']
		read_all:   ['admin', 'nurse', 'pharm', 'patient', 'csr']
		request:    []
		update:     ['admin', 'nurse', 'pharm', 'csr']
		review:     []
		update_all: ['admin', 'nurse', 'pharm', 'csr']
		write:      ['admin', 'nurse', 'pharm', 'patient', 'csr']
	sections_group: [
		'Intervention':
			sections: [
				'Intervention Details':
					hide_header: true
					indent: false
					tab: 'Intervention'
					fields: ['order_id', 'delivery_tick_id', 'inter_type', 'intr_typ_ftr',
					'intervention_date', 'intervention_time', 'reason_services', 'reason_services_other', 'reporting_type',
					"prior_auth", "prior_auth_details", "triage", "triage_details", 'transfer', 'transfer_details', "copay_type", "claim_status",
					'reporting_details', 'clinical_rounding', 'complaint',
					'reason_prx', 'reason_prx_other', 'pa_type', "collaboration_type",
					'education_who', "education_type", 'education_type_details', "line_type"]
				'Pharmacy Interventions':
					hide_header: true
					indent: false
					tab: 'Intervention'
					fields: ['subform_pharmacy']
				'HBNow':
					hide_header: true
					indent: false
					tab: 'Intervention'
					fields: ['hbnow_status', 'hbnow_comment']
				'Intervention Actions':
					hide_header: true
					indent: false
					tab: 'Actions'
					fields: ['complaint_resolution_datetime', 'complaint_resolution',
					'complaint_px', 'complaint_px_sign', 'physician_contacted',
					'physician_accepted', 'physician_contacted_date', 'physician_contacted_time',
					'resolved', 'continue']
				'Comments':
					hide_header: true
					indent: false
					tab: 'Comments'
					fields: ['comments']
			]
	]

	transform_post: []

view:
	find:
		basic: ['inter_type', 'reason_prx']
	grid:
		fields: ['intervention_date', 'created_by', 'inter_type', 'reason_prx', 'resolved']
	label: 'Intervention'
	open: 'read'
