fields:

	expected_tat:
		model:
			required: true
			source: ['2', '5', '10', '15', '20', '25', '30']
		view:
			columns: 2
			note: 'Days'
			control: 'radio'
			label: 'Expected TAT for auth approval:'

	billing_method_id:
		model:
			source: 'list_billing_method'
			sourceid: 'code'
			if:
				'ncpdp':
					prefill:
						pa_type: 'Drug'
					fields: ['open_gpi', 'pa_type']
				'*':
					fields: ['pa_type']
		view:
			label: 'Billing Method'

	auth_prior_disp:
		model:
			source: ['No', 'Yes']
		view:
			columns: 2
			control: 'radio'
			label: 'Is approval needed prior to dispensing to patient?'

	open_gpi:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['open_gpi_why']
				'Yes':
					fields: ['open_gpi_approve']
		view:
			columns: 4
			control: 'radio'
			label: 'Open GPI request?'

	open_gpi_why:
		view:
			columns: 2
			label: 'Why not?'

	open_gpi_approve:
		model:
			source: ['No', 'Yes']
			if:
				'No':
					fields: ['open_gpi_denied_why']
		view:
			columns: 4
			control: 'radio'
			label: 'Open GPI request approved?'

	open_gpi_denied_why:
		view:
			columns: 2
			label: 'Why not?'

	summary:
		view:
			control: 'area'
			label: 'Summary'
model:
	access:
		create:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		create_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		delete:     ['admin', 'cm', 'cma', 'liaison', 'nurse', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'payer', 'physician']
		request:    []
		update:     ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		update_all: ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	prefill:
		patient:
			link:
				id: 'patient_id'
	name: '{insurance_id_auto_name} {date} {status_id}'

	sections_group: [
		'Prior Authorization':
			hide_header: true
			indent: false
			tab: 'Authorization'
			fields: ['ins_filter', 'insurance_id', 'auth_type', 'billing_method_id', 'status_id', 'pa_type', 'order_id', 'order_no',
			'require_order_item_id', 'order_item_status', 'active_order_item_id', 'pending_order_item_id', 'rx_no',
			'rental_id', 'auth_prior_disp', 'expected_tat', 'open_gpi', 'open_gpi_why',
			'clinic_docs_requested', 'clinic_doc_status_id', 'clc_dc_id',
			'denied_datetime','denial_letter', 'denial_letter_status_id',
			'pa_denied_id', 'appeal_eligible', 'appeal_datetime', 'appeal_docs',
			'appeal_docs_status_id', 'appeal_letter_status_id',
			'appeal_status_id', 'appeal_denied_upheld_rsn_id',
			'submission_datetime', 'submission_method', 'contact_phone', 'contact_fax', 'contact_representative',
			'request_id', 'cmm_number', 'comment']
		'Prior Auth Billing Information':
			hide_header: true
			indent: false
			tab: 'Approval'
			fields: ['approval_method', 'approval_letter_requested',
			'drug_approval_date', 'drug_effective_date', 'dose_approved', 'hc_id', 
			'nurse_approval_date', 'nurse_effective_date', 'nc_id',
			'dme_approval_date', 'dme_effective_date',
			'supplies_approval_date', 'supplies_effective_date', 'sc_id',
			'open_gpi_approve', 'open_gpi_denied_why', 'number', 'expire_date']
		'Limits':
			hide_header: true
			indent: false
			tab: 'Approval'
			fields: ['limits', 'unit_limit', 'refills_limit', 'nursing_limits', 'visit_limit',
			'visit_limit_freq', 'limit_freq', 'expected_price', 'use_negotiated_rate', 'billing_comment']
		'Rental':
			hide_header: true
			indent: false
			tab: 'Approval'
			fields: ['rental_coverage_type', 'rental_price_approved', 'rental_purchase_price_approved']
		'Attachments':
			hide_header: true
			indent: true
			tab: 'Attachments'
			fields: ['subform_attachment']
		]

view:
	comment: 'Patient > Prior Authorization'
	grid:
		fields: ['created_on','insurance_id', 'status_id', 'order_id', 'number', 'expire_date', 'summary']
		sort: ['-created_on']
	find:
		basic: ['status_id', 'insurance_id', 'order_id']

	label: 'Prior Authorization'
	open: 'read'