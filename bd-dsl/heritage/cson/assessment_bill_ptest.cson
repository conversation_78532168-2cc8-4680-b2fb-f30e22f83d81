fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	test_claim:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Test claim?'
			columns: 3

	pt_resp:
		model:
			type: 'decimal'
			rounding: 0.01
		view:
			label: 'Pt responsibility amount $'
			class: 'numeral money'
			format: '$0,0.00'
			columns: 3

	summary:
		view:
			control: 'area'
			label: 'Summary of estimate'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'Patient Estimate of Cost':
			hide_header: true
			indent: false
			fields: ['test_claim', 'pt_resp', 'summary']

view:
	comment: 'Patient > Billing Assessment Patient Estimate of Cost'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment Patient Estimate of Cost'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']