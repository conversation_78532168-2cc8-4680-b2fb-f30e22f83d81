fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	received_datetime:
		model:
			type: 'datetime'
		view:
			label: 'Referral Received Date/Time'
			columns: 3

	review_time:
		model:
			type: 'time'
		view:
			label: 'Referral Review Start Time'
			columns: 3

	therapy:
		view:
			label: 'Therapy Type/Drug'
			columns: 3

	source:
		view:
			label: 'Referral Source'
			columns: 3

	case_manager:
		view:
			label: 'Nurse / Case Manager Name'
			columns: 3

	cm_phone:
		view:
			format: 'us_phone'
			label: "Nurse / Case Manager Phone"
			columns: 3

	cm_fax:
		view:
			format: 'us_phone'
			label: "Nurse / Case Manager Fax"
			columns: 3

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'New Admission':
			hide_header: true
			indent: false
			fields: ['received_datetime', 'review_time', 'therapy', 'source',
			'case_manager', 'cm_phone', 'cm_fax']

view:
	comment: 'Patient > Billing Assessment New Admission'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment New Admission'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']