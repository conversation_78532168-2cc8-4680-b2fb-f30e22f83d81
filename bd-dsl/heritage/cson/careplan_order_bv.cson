fields:

	therapy_id:
		model:
			if:
				'ig':
					fields: ['route']
				'factor':
					fields: ['supplies_hemo', 'nurse_hemo']

	type_id:
		model:
			required: true
			source: 'list_payer_type'
			sourceid: 'code'
			if:
				'MCRB':
					fields: ['group_number', 'inn_oon', 'medicare_hits']
					sections: ['Payer For']
				'MEDI':
					fields: ['group_number', 'inn_oon']
					sections: ['Payer For']
				'CMMMED':
					fields: ['group_number', 'inn_oon']
					sections: ['Payer For']
				'CMPBM':
					fields: ['plan_name', 'bin', 'pcn']
					sections: ['Medication']
				'MCRD':
					fields: ['bin', 'pcn', 'inn_oon', 'medicare_hits', 'med_auth_required', 'hcpc_id']
					sections: ['Medication']
				'*':
					fields: ['inn_oon']
					sections: ['Payer For']
		view:
			columns: 2
			control: 'select'
			label: 'Type'
			offscreen: true
			readonly: true

	payer_for:
		model:
			multi: true
			source: ['Medication', 'Supplies', 'Nursing', 'DME']
			if:
				'Medication':
					sections: ['Medication']
				'Supplies':
					sections: ['Supplies']
				'Nursing':
					sections: ['Nursing']
				'DME':
					sections: ['DME']
		view:
			control: 'checkbox'
			label: 'Payor for:'
			columns: 2

	route:
		model:
			required: true
			source: ['Intravenous', 'Subcutaneous']
			if:
				'Subcutaneous':
					fields: ['supplies_scig', 'nurse_scig']
					sections: ['Medicare HITS']
				'Intravenous':
					fields: ['supplies_ivig', 'nurse_ivig', 'nurse_ivig_demo']
		view:
			control: 'radio'
			columns: 2
			label: 'Route'

	supplies_ivig:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			required: false
			source:
				'A422': 'A4222 - Admin Kit with pump'
				'A4223': 'A4223 - Admin Kit no pump'
				'E0781': 'E0781 – Curlin Pump'
				'S9338': 'S9338 - IVIG/SQIG'
				'Q2052': 'Q2052 - IVIG Demo Project'
		view:
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Supplies (IVIG)'
			columns: 2

	supplies_scig:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			required: false
			source: 
				'A4222': 'A4222 - Admin Kit with pump'
				'K0552': 'K0552 – SQIG Supply Kit'
				'E0781': 'E0781 – Curlin Pump'
				'E0779': 'E0779 – Freedom 60'
				'S9338': 'S9338 - Home infusion therapy, per diem'
		view:
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Supplies (SCIG)'
			columns: 2

	supplies_ocrevus:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			required: false
			source: 
				'A4222': 'A4222 - Admin Kit with pump'
				'A4223': 'A4223 – Admin Kit no pump'
				'S9379': 'S9379 – IV NOC Infusion'
		view:
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Supplies (Ocrevus)'
			columns: 2

	supplies_hemo:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			required: false
			source:
				'S9345': 'S9345 - Hemophilia'
		view:
			control: 'checkbox'
			label: 'Supplies (Hemophilia)'
			columns: 2

	nurse_ivig:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 
				'99601': '99601 - IVIG Infusion'
				'99602': '99602 - IVIG Infusion'
				'G0089': 'G0089 (Initial visit)'
				'G0069': 'G0069 (Subsequent visit)'
		view:
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Nursing Code'
			columns: 2

	nurse_scig:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 
				'99601': '99601 - SQIG Infusion'
				'99602': '99602 - SQIG Infusion'
				'G0089': 'G0089 (Initial visit)'
				'G0069': 'G0069 (Subsequent visit)'
		view:
			class: 'checkbox checkbox-2'
			control: 'checkbox'
			label: 'Nursing Code'
			columns: 2

	nurse_ivig_demo:
		model:
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Q2052 IVIG Demostration Project?'
			columns: 2

	nurse_ocrevus:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 
				'99601': '99601 - Ocrevus Infusion'
				'99602': '99602 - Ocrevus Infusion'
		view:
			control: 'checkbox'
			label: 'Nursing Code'
			columns: 2

	nurse_hemo:
		model:
			prefill: ['careplan_order_bv']
			multi: true
			source: 
				'99601': '99601 - Hemophilia Infusion'
				'99602': '99602 - Hemophilia Infusion'
		view:
			control: 'checkbox'
			label: 'Nursing Code'
			columns: 2

model:
	access:
		create:     []
		create_all: ['admin', 'cm', 'cma', 'pharm']
		delete:     ['admin', 'csr', 'pharm']
		read:       ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		read_all:   ['admin', 'cm', 'cma', 'csr', 'liaison', 'nurse', 'patient', 'pharm', 'physician']
		request:    []
		update:     []
		update_all: ['admin', 'cm', 'cma', 'csr', 'pharm']
		write:      ['admin', 'cm', 'cma', 'csr', 'pharm']
	name: '{insurance_id} {type_id} ({inn_oon})'
	sections:
		'Payer Details':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['order_id', 'therapy_id', 'brand_name_id', 'route', 'ins_filter', 'insurance_id', 'payer_id', 'type_id',
			'req_auth_for','inn_oon', 'payer_phone', 'payer_fax','payer_representative', 'call_reference',
			'group_number', 'bin', 'pcn', 'person_code']
		'Benefits Summary':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['effective_date', 'beneficiary_fname', 'beneficiary_lname', 'subscriber', 'plan_name', 'plan_year', 'days_timely_filing',
			'inn_deductible', 'inn_ded_met', 'inn_coinsurance', 'inn_oop_max', 'inn_oop_met',
			'onn_deductible', 'onn_ded_met', 'onn_coinsurance', 'onn_oop_max',
			'onn_oop_met', 'onn_cross_accum', 'copay_accumulator', 'copay_accumulator_amount']
		'Medicare HITS':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['medicare_hits']
		'Comments':
			hide_header: true
			indent: true
			tab: 'Verification'
			fields: ['comment']
		'Payer For':
			hide_header: true
			indent: true
			tab: 'Coverage'
			fields: ['payer_for']
		'Medication':
			hide_header: true
			indent: true
			tab: 'Coverage'
			fields: ['med_auth_required', 'hcpc_id']
		'Nursing':
			hide_header: true
			indent: true
			tab: 'Coverage'
			fields: ['nurse_ivig_demo', 'nurse_ivig', 'nurse_scig', 'nurse_hemo', 'nurse_ocrevus', 'nurse_auth_required', 'nurse_code_id']
		'Supplies':
			hide_header: true
			indent: true
			tab: 'Coverage'
			fields: ['supplies_ocrevus', 'supplies_scig', 'supplies_hemo', 'supplies_ivig', 'supplies_auth_required', 'sply_code_id']
		'DME':
			hide_header: true
			indent: true
			tab: 'Coverage'
			fields: ['dme_auth_required', 'dme_code_id']
		'Genentech Access Solutions':
			hide_header: true
			indent: true
			tab: 'Genentech'
			fields: ['genentech_enrolled', 'genentech_id','genentech_no_why', 'genentech_comment']
		'Attachments':
			hide_header: true
			indent: true
			tab: 'Attachments'
			fields: ['subform_attachment']
view:
	comment: 'Patient > Intake Benefits Verification'
	find:
		basic: ['insurance_id', 'type_id', 'inn_oon']
	grid:
		fields: ['created_on', 'created_by', 'insurance_id', 'type_id', 'inn_oon']
	label: 'Patient Intake Benefits Verification'
	open: 'read'
