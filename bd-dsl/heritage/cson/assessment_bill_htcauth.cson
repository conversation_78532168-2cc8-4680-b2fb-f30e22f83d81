fields:
	# Links
	patient_id:
		model:
			required: true
			source: 'patient'
			type: 'int'
		view:
			label: 'Patient Id'

	ship_required:
		model:
			required: true
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Authorization to Ship required from HTC?'
			columns: 2

	ship_auth_requested:
		model:
			required: true
			source: ['No', 'Yes']
			if:
				'Yes':
					fields: ['ship_req_datetime']
		view:
			control: 'radio'
			label: 'Authorization to Ship Requested?'
			columns: 2

	ship_req_datetime:
		model:
			type: 'datetime'
		view:
			label: 'When the request is submitted to HTC'
			columns: 2

	contact:
		model:
			multi: true
			source: ['HTC Name: Jimmy Everest Center for Cancer & Blood Diseases in Children',
			'HTC Contact Person: TJ <PERSON>/<PERSON>',
			'HTC Phone Number: ************',
			'HTC Fax Number: ************']
		view:
			control: 'checkbox'
			label: 'HTC Contact'
			class: 'list'
			readonly: true

	request_mode:
		model:
			required: false
			source: ['Phone', 'Fax']
		view:
			control: 'radio'
			label: 'Mode of Request'
			columns: 4

	patient_name:
		model:
			required: true
		view:
			label: 'Patient Name'
			columns: 2

	dob:
		model:
			required: true
			type: 'date'
		view:
			label: 'Date of Birth'
			columns: 4
			validate: [{
					name: 'DOBValidator'
			}
			]

	drug:
		model:
			required: true
		view:
			label: 'Drug (Brand Name)'
			columns: 2

	hcpc:
		model:
			required: false
		view:
			label: 'HCPC'
			columns: 4

	dose:
		model:
			required: false
		view:
			label: 'Target Dose'
			columns: 4

	vial_assay:
		model:
			required: false
		view:
			label: 'Vial Assay'
			columns: 2

	quantity:
		model:
			required: false
		view:
			label: 'Quantity to Ship'
			columns: 2

	lotnumber:
		model:
			required: false
		view:
			label: 'Lot Number'
			columns: 2

	expiration:
		model:
			required: false
		view:
			label: 'Expiration Date'
			columns: 2

	other_meds:
		model:
			required: false
		view:
			label: 'Any other Medication/Supplies Requested'
			columns: 2

	drug2:
		model:
			required: true
		view:
			label: 'Drug (Brand Name)'
			columns: 2

	hcpc2:
		model:
			required: false
		view:
			label: 'HCPC'
			columns: 4

	ship_date:
		model:
			required: false
		view:
			label: 'Expected Date to Ship'
			columns: 4

	docs_attached:
		model:
			required: false
			source: ['No', 'Yes']
		view:
			control: 'radio'
			label: 'Documents attached to patient chart'
			columns: 2

	add_notes:
		model:
			required: false
		view:
			control: 'area'
			label: 'Additional Notes'

model:
	access:
		create:     []
		create_all: ['admin', 'nurse','pharm','csr']
		delete:     ['admin']
		read:       ['admin','nurse','pharm','csr']
		read_all:   ['admin','nurse','pharm','csr']
		request:    []
		update:     []
		update_all: ['admin','nurse','pharm','csr']
		write:      ['admin','nurse','pharm','csr']
	bundle: ['patient']
	indexes:
		many: [
			['patient_id']
		]
	reportable: true
	name: ['patient_id']
	sections: 
		'HTC Authorization to Ship Request':
			hide_header: true
			indent: false
			fields: ['ship_required', 'ship_auth_requested', 'ship_req_datetime',
					'contact', 'request_mode', 'patient_name', 'dob',
					'drug', 'hcpc', 'dose', 'vial_assay', 'quantity',
					'lotnumber', 'expiration', 'other_meds', 'drug2',
					'hcpc2', 'ship_date', 'docs_attached', 'add_notes']

view:
	comment: 'Patient > Billing Assessment HTC Authorization to Ship Request'
	find:
		basic: ['created_on', 'created_by', 'updated_on', 'updated_by']
	grid:
		fields: ['created_on', 'created_by', 'updated_on', 'updated_by']
		sort: ['-created_on']
	label: 'Billing Assessment HTC Authorization to Ship Request'
	open: 'read'
	block:
		update:
			except: ['admin', 'self']