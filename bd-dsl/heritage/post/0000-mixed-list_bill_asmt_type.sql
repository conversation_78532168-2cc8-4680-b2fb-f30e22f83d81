BEGIN;

INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'ABN - Advanced Beneficiary Notice', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ABN - Advanced Beneficiary Notice','abnnotice','["2024-09-25 19:51:12.807777+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:31.000Z','Account Referred to Collections', NULL,'2024-03-19T22:39:32.000Z',NULL,'Account Referred to Collections', 'collections','["2024-09-25 19:51:31.260825+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:41.000Z','Admissions Patient Contact', NULL,'2024-03-19T22:39:32.000Z',NULL,'Admissions Patient Contact','admissionscontact', '["2024-09-25 19:51:41.905524+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:55.000Z','Bankruptcy', NULL,'2024-03-19T22:39:32.000Z',NULL,'Bankruptcy', 'bankruptcy','["2024-09-25 19:51:55.742926+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:52:04.000Z','Billing Note', NULL,'2024-03-19T22:39:32.000Z',NULL,'Billing Note', 'billnote','["2024-09-25 19:52:04.295416+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Billing Audit', NULL,'2024-03-19T22:39:32.000Z',NULL,'Billing Audit', 'billaudit','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Cash Posting Notes', NULL,'2024-03-19T22:39:32.000Z',NULL,'Cash Posting Notes', 'postingnote','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Claim Status', NULL,'2024-03-19T22:39:32.000Z',NULL,'Claim Status', 'claimstatus', '["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Credit Card Payment', NULL,'2024-03-19T22:39:32.000Z',NULL,'Credit Card Payment', 'ccpayment','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Copay Assistance', NULL,'2024-03-19T22:39:32.000Z',NULL,'Copay Assistance', 'copayassist', '["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Delivery Signature Opt-Out', NULL,'2024-03-19T22:39:32.000Z',NULL,'Delivery Signature Opt-Out', 'deliverysigoptout','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Denied Referral', NULL,'2024-03-19T22:39:32.000Z',NULL,'Denied Referral', 'deniedreferral','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Document Sent to Patient', NULL,'2024-03-19T22:39:32.000Z',NULL,'Document Sent to Patient', 'docsenttopt','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Durable Power of Attorney', NULL,'2024-03-19T22:39:32.000Z',NULL,'Durable Power of Attorney', 'dpoa','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','SWO', NULL,'2024-03-19T22:39:32.000Z',NULL,'SWO', 'swo', '["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Financial Hardship', NULL,'2024-03-19T22:39:32.000Z',NULL,'Financial Hardship', 'hardship','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','HTC Authorization to Ship Request', NULL,'2024-03-19T22:39:32.000Z',NULL,'HTC Authorization to Ship Request', 'htcauthtoship', '["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','HTC Authorization to Ship Approval', NULL,'2024-03-19T22:39:32.000Z',NULL,'HTC Authorization to Ship Approval', 'htcapproveship','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Information', NULL,'2024-03-19T22:39:32.000Z',NULL,'Information', 'info','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','INITIAL INSURANCE ELIGIBILITY/BENEFITS NOTE', NULL,'2024-03-19T22:39:32.000Z',NULL,'INITIAL INSURANCE ELIGIBILITY/BENEFITS NOTE', 'initialeligibilitynote','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medicare B DME Qualification', NULL,'2024-03-19T22:39:32.000Z',NULL,'Medicare B DME Qualification', 'partbdmequal','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medicare IVIG Demonstration', NULL,'2024-03-19T22:39:32.000Z',NULL,'Medicare IVIG Demonstration', 'ivigdemo','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Medical Record Request', NULL,'2024-03-19T22:39:32.000Z',NULL,'Medical Record Request', 'medrecrequest','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','MONTHLY INSURANCE ELIGIBILITY/BENEFITS NOTE', NULL,'2024-03-19T22:39:32.000Z',NULL,'MONTHLY INSURANCE ELIGIBILITY/BENEFITS NOTE', 'monthlyinsureligibility','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','New Admission', NULL,'2024-03-19T22:39:32.000Z',NULL,'New Admission', 'newadmin','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Nursing Visit', NULL,'2024-03-19T22:39:32.000Z',NULL,'Nursing Visit', 'nursevisit','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','On-Call Contact', NULL,'2024-03-19T22:39:32.000Z',NULL,'On-Call Contact', 'oncallcontact','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Order Change', NULL,'2024-03-19T22:39:32.000Z',NULL,'Order Change', 'orderchange','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Overpayment', NULL,'2024-03-19T22:39:32.000Z',NULL,'Overpayment', 'overpayment','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Introduction Call', NULL,'2024-03-19T22:39:32.000Z',NULL,'Introduction Call', 'introcall','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Assistance', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Assistance', 'patientassistance','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Call', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Call', 'patientcall','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Discharge', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Discharge', 'patientdc','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Estimate of Cost', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Estimate of Cost', 'ptestimatecost','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Transfer', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Transfer', 'pttransfer','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Payer Correspondence', NULL,'2024-03-19T22:39:32.000Z',NULL,'Payer Correspondence', 'ptcorrespondence','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Payor Audit', NULL,'2024-03-19T22:39:32.000Z',NULL,'Payor Audit', 'payeraudit','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Payment Plan', NULL,'2024-03-19T22:39:32.000Z',NULL,'Payment Plan', 'paymentplan','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Authorization', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Authorization', 'pa','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Authorization Approval', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Authorization Approval', 'paapprove','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Requested-Pending', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Auth / Re-Auth / GAP Requested-Pending', 'pareauthgappend','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Status Check (Still Pending)', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Auth / Re-Auth / GAP Status Check (Still Pending)', 'pareauthgapcheck','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (43,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Cancelation', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Auth / Re-Auth / GAP Cancelation', 'pareauthgapcancel','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (44,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Approved', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Auth / Re-Auth / GAP Approved', 'pareauthgapapprove','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (45,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Prior Auth / Re-Auth / GAP Denied', NULL,'2024-03-19T22:39:32.000Z',NULL,'Prior Auth / Re-Auth / GAP Denied', 'pareauthgapapdeny','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (46,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Demo Change Request', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Demo Change Request', 'ptdemochangereq','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (47,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Referral Contact', NULL,'2024-03-19T22:39:32.000Z',NULL,'Referral Contact', 'refcontact','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (48,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Patient Triage', NULL,'2024-03-19T22:39:32.000Z',NULL,'Patient Triage', 'pttriage','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (49,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Refund', NULL,'2024-03-19T22:39:32.000Z',NULL,'Refund', 'refund','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (50,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','SNF/LTAC Contact', NULL,'2024-03-19T22:39:32.000Z',NULL,'SNF/LTAC Contact', 'snfltaccontact','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (51,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Insurance Eligibility/Benefits Note', NULL,'2024-03-19T22:39:32.000Z',NULL,'Insurance Eligibility/Benefits Note', 'eligibilitynote','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';
INSERT INTO form_list_bill_asmt_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "name", "code", "sys_period", "allow_sync", "active") VALUES (52,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-23T17:32:42.000Z','Provider Communication', NULL,'2024-03-19T22:39:32.000Z',NULL,'Provider Communication', 'provcomm','["2024-09-23 17:32:42.168055+00",)','Yes','Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name" = EXCLUDED."change_type", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_asmt_type.allow_sync = 'Yes';

COMMIT;
