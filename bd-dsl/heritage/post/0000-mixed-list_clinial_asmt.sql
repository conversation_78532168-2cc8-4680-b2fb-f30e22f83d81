BEGIN;

INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Adult Growth Hormone Deficiency Assessment (AGHDA)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'aghda', 'Adult Growth Hormone Deficiency Assessment (AGHDA)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'ALS C.A.R.E. (ALSFRS-R)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'alsfrs', 'ALS C.A.R.E. (ALSFRS-R)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Bath Ankylosing Spondylitis Disease Activity Index (BASDAI)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'basdai', 'Bath Ankylosing Spondylitis Disease Activity Index (BASDAI)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Braden Scale', NULL,'2024-03-19T22:39:32.000Z',NULL, 'braden', 'Braden Scale','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'CIDP Difficulties Survey', NULL,'2024-03-19T22:39:32.000Z',NULL, 'cidp', 'CIDP Difficulties Survey','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Dermatology Life Quality Index (DLQI)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'dlqi', 'Dermatology Life Quality Index (DLQI)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Expanded Disability Status Scale (EDSS)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'edss', 'Expanded Disability Status Scale (EDSS)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Epworth Sleepiness Scale (ESS)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'epworth', 'Epworth Sleepiness Scale (ESS)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Grip Strength Measurements', NULL,'2024-03-19T22:39:32.000Z',NULL, 'grip_strength', 'Grip Strength Measurements','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'HIV/AIDS-targeted Quality of Life (HAT-QOL)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'hat_qol', 'HIV/AIDS-targeted Quality of Life (HAT-QOL)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Harvey-Bradshaw Index', NULL,'2024-03-19T22:39:32.000Z',NULL, 'hbi', 'Harvey-Bradshaw Index','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Heart Failure Quality Of Life', NULL,'2024-03-19T22:39:32.000Z',NULL, 'hfqol', 'Heart Failure Quality Of Life','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Headache Management Questionnaire', NULL,'2024-03-19T22:39:32.000Z',NULL, 'hmq', 'Headache Management Questionnaire','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Health and Work Performance Questionnaire (HPQ)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'hpq', 'Health and Work Performance Questionnaire (HPQ)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'INCAT', NULL,'2024-03-19T22:39:32.000Z',NULL, 'incat', 'INCAT','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Modified Fatigue Impact Scale (MFIS-5)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mfis5', 'Modified Fatigue Impact Scale (MFIS-5)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Myasthenia Gravis Activities of Daily Living (MG-ADL)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mgadl', 'Myasthenia Gravis Activities of Daily Living (MG-ADL)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Modified Health Assessment Questionnaire (mHAQ)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mhaq', 'Modified Health Assessment Questionnaire (mHAQ)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Morisky Medication Adherence (MMAS-8)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mmas8', 'Morisky Medication Adherence (MMAS-8)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Multifocal Motor Neuropathy', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mmn', 'Multifocal Motor Neuropathy','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'moqlq', 'Mini-Osteoporosis Quality of Life Questionnaire (mini-OQLQ)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Multiple Sclerosis Difficulties', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ms', 'Multiple Sclerosis Difficulties','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Myasthenia Gravis Difficulties', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mygrav', 'Myasthenia Gravis Difficulties','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Myositis Difficulties', NULL,'2024-03-19T22:39:32.000Z',NULL, 'myositis', 'Myositis Difficulties','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'PID Quality Of Life (PADQOL-16)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'padqol', 'PID Quality Of Life (PADQOL-16)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Health/Depression Questionnaire (PHQ-9)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'phq', 'Patient Health/Depression Questionnaire (PHQ-9)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Activity Scale-II (PAS-II)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'pas2', 'Patient Activity Scale-II (PAS-II)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'MOS Pain Effects Scale (PES)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'pes', 'MOS Pain Effects Scale (PES)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient-Oriented Eczema Measure (POEM)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'poem', 'Patient-Oriented Eczema Measure (POEM)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Questions on Life Satisfaction – Hypopituitarism (QLS-H)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'qlsh', 'Questions on Life Satisfaction – Hypopituitarism (QLS-H)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Rheumatoid Arthritis Disease Activity Index (RADAI)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'radai', 'Rheumatoid Arthritis Disease Activity Index (RADAI)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'RODS', NULL,'2024-03-19T22:39:32.000Z',NULL, 'rods', 'RODS','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Quality of Life (SF12)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'sf12v2', 'Quality of Life (SF12)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Short Inflammatory Bowel Disease Questionnaire (SIBDQ)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'sibdq', 'Short Inflammatory Bowel Disease Questionnaire (SIBDQ)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Stiff Person Syndrome', NULL,'2024-03-19T22:39:32.000Z',NULL, 'stiffps', 'Stiff Person Syndrome','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Withdrawal Assessment Tool', NULL,'2024-03-19T22:39:32.000Z',NULL, 'wat', 'Withdrawal Assessment Tool','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Clinical Wellness (IV)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'wellness_iv', 'Patient Clinical Wellness (IV)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Clinical Wellness (Self Injectable)', NULL,'2024-03-19T22:39:32.000Z',NULL, 'wellness_si', 'Clinical Wellness (Self Injectable)','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Factor Quality of Life', NULL,'2024-03-19T22:39:32.000Z',NULL, 'qol_factor', 'Factor Quality of Life','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Pediatric Quality of Life', NULL,'2024-03-19T22:39:32.000Z',NULL, 'qol_peds', 'Pediatric Quality of Life','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Rarevoice Survey', NULL,'2024-03-19T22:39:32.000Z',NULL, 'rarevoice_survey', 'Rarevoice Survey','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';
INSERT INTO form_list_clinical_asmt ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "description", "sys_period", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Clinical Outcomes', NULL,'2024-03-19T22:39:32.000Z',NULL, 'outcomes', 'Clinical Outcomes','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on","sys_period" = EXCLUDED."sys_period" WHERE form_list_clinical_asmt.allow_sync = 'Yes';

COMMIT;
