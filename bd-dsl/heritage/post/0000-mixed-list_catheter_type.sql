BEGIN;

INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','Peripheral', NULL,'2024-03-19T22:39:32.000Z',NULL, 'piv', 'Peripheral','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';
INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'PICC', NULL,'2024-03-19T22:39:32.000Z',NULL, 'picc_midline', 'PICC','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';
INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient to place and DC', NULL,'2024-03-19T22:39:32.000Z',NULL, 'patient_dc', 'Patient to place and DC','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';

INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'RN to place and DC', NULL,'2024-03-19T22:39:32.000Z',NULL, 'rn_dc', 'RN to place and DC','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';
INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Subcutaneous', NULL,'2024-03-19T22:39:32.000Z',NULL, 'subq', 'Subcutaneous','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';
INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'S.L. Port', NULL,'2024-03-19T22:39:32.000Z',NULL, 'sl_port', 'S.L. Port','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';
INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'D.L. Port', NULL,'2024-03-19T22:39:32.000Z',NULL, 'dl_port', 'D.L. Port','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';
INSERT INTO form_list_catheter_type ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Other', NULL,'2024-03-19T22:39:32.000Z',NULL, 'other', 'Other','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_catheter_type.allow_sync = 'Yes';

COMMIT;
