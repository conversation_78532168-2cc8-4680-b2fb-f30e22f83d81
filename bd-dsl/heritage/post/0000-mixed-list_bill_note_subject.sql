BEGIN;

INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (1,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z','ABN - Advanced Beneficiary Notice', NULL,'2024-03-19T22:39:32.000Z',NULL, 'abn', 'ABN - Advanced Beneficiary Notice','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (2,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Account Referred to Collections', NULL,'2024-03-19T22:39:32.000Z',NULL, 'collections', 'Account Referred to Collections','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (3,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Adjustment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'adjustment', 'Adjustment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (4,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Adjustment Approval', NULL,'2024-03-19T22:39:32.000Z',NULL, 'adjustmentaprv', 'Adjustment Approval','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (5,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Adjustment Request', NULL,'2024-03-19T22:39:32.000Z',NULL, 'adjustmentrequest', 'Adjustment Request','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (6,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Admissions Patient Contact', NULL,'2024-03-19T22:39:32.000Z',NULL, 'adminptcontact', 'Admissions Patient Contact','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (7,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Bankruptcy', NULL,'2024-03-19T22:39:32.000Z',NULL, 'bankruptcy', 'Bankruptcy','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (8,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Billing Audit', NULL,'2024-03-19T22:39:32.000Z',NULL, 'billingaudit', 'Billing Audit','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (9,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Billing Information', NULL,'2024-03-19T22:39:32.000Z',NULL, 'billinginfo', 'Billing Information','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (10,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Cash Posting Notes', NULL,'2024-03-19T22:39:32.000Z',NULL, 'cashposting', 'Cash Posting Notes','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (11,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Claim Accepted but not Submitted', NULL,'2024-03-19T22:39:32.000Z',NULL, 'claimacceptednotsubmitted', 'Claim Accepted but not Submitted','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (12,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Claim Rejection - Emdeon', NULL,'2024-03-19T22:39:32.000Z',NULL, 'claimrejectionemdeon', 'Claim Rejection - Emdeon','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (13,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Claim Review', NULL,'2024-03-19T22:39:32.000Z',NULL, 'claimreview', 'Claim Review','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (14,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Claim Status', NULL,'2024-03-19T22:39:32.000Z',NULL, 'claimstatus', 'Claim Status','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (15,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Clean Referral', NULL,'2024-03-19T22:39:32.000Z',NULL, 'claimreferral', 'Clean Referral','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (16,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Copay Assistance', NULL,'2024-03-19T22:39:32.000Z',NULL, 'copayassist', 'Copay Assistance','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (17,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Credit Card Payment', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ccpayment', 'Credit Card Payment','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (18,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Delivery Signature Opt-Out', NULL,'2024-03-19T22:39:32.000Z',NULL, 'deliverysigoptout', 'Delivery Signature Opt-Out','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (19,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Denied Referral', NULL,'2024-03-19T22:39:32.000Z',NULL, 'deniedref', 'Denied Referral','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (20,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Document Sent to Patient', NULL,'2024-03-19T22:39:32.000Z',NULL, 'docsenttopt', 'Document Sent to Patient','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (21,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Durable Power of Attorney', NULL,'2024-03-19T22:39:32.000Z',NULL, 'durablepoa', 'Durable Power of Attorney','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (22,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'DWO', NULL,'2024-03-19T22:39:32.000Z',NULL, 'dwo', 'DWO','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (23,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Final Notice Sent with Statement', NULL,'2024-03-19T22:39:32.000Z',NULL, 'finalnotice', 'Final Notice Sent with Statement','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (24,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Financial Hardship', NULL,'2024-03-19T22:39:32.000Z',NULL, 'hardship', 'Financial Hardship','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (25,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Introduction Call', NULL,'2024-03-19T22:39:32.000Z',NULL, 'introcall', 'Introduction Call','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (26,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Information', NULL,'2024-03-19T22:39:32.000Z',NULL, 'info', 'Information','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (27,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Insurance Verification', NULL,'2024-03-19T22:39:32.000Z',NULL, 'insuranceveri', 'Insurance Verification','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (28,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'INVOICE NOT MAILED', NULL,'2024-03-19T22:39:32.000Z',NULL, 'invoicenotmailed', 'INVOICE NOT MAILED','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (29,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Medical Record Request', NULL,'2024-03-19T22:39:32.000Z',NULL, 'medrecrequest', 'Medical Record Request','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (30,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Medicare B DME Qualification', NULL,'2024-03-19T22:39:32.000Z',NULL, 'partbdmequal', 'Medicare B DME Qualification','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (31,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Medicare IVIG Demonstration', NULL,'2024-03-19T22:39:32.000Z',NULL, 'mcrigdemo', 'Medicare IVIG Demonstration','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (32,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'New Admission', NULL,'2024-03-19T22:39:32.000Z',NULL, 'newadmin', 'New Admission','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (33,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'New Order', NULL,'2024-03-19T22:39:32.000Z',NULL, 'neworder', 'New Order','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (34,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Nursing Visit', NULL,'2024-03-19T22:39:32.000Z',NULL, 'nursingvisit', 'Nursing Visit','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (35,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'On-Call Contact', NULL,'2024-03-19T22:39:32.000Z',NULL, 'oncall', 'On-Call Contact','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (36,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Order Change', NULL,'2024-03-19T22:39:32.000Z',NULL, 'orderchange', 'Order Change','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (37,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'OVERPAYMENT', NULL,'2024-03-19T22:39:32.000Z',NULL, 'overpayment', 'OVERPAYMENT','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (38,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Assistance', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ptassistance', 'Patient Assistance','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (39,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Call', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ptcall', 'Patient Call','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (40,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Discharge', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ptdc', 'Patient Discharge','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (41,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Estimate of Cost', NULL,'2024-03-19T22:39:32.000Z',NULL, 'ptcostest', 'Patient Estimate of Cost','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (42,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Transfer', NULL,'2024-03-19T22:39:32.000Z',NULL, 'pttransfer', 'Patient Transfer','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (43,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Payer Correspondence', NULL,'2024-03-19T22:39:32.000Z',NULL, 'payercorrespondence', 'Payer Correspondence','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (44,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Payment Plan', NULL,'2024-03-19T22:39:32.000Z',NULL, 'paymentplan', 'Payment Plan','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (45,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Payor Audit', NULL,'2024-03-19T22:39:32.000Z',NULL, 'payeraudit', 'Payor Audit','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (46,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Prior Authorization', NULL,'2024-03-19T22:39:32.000Z',NULL, 'pa', 'Prior Authorization','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (47,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Prior Authorization Approval', NULL,'2024-03-19T22:39:32.000Z',NULL, 'paapprov', 'Prior Authorization Approval','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (48,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'RECEIPT MAILED', NULL,'2024-03-19T22:39:32.000Z',NULL, 'receiptmailed', 'RECEIPT MAILED','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (49,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Referral Contact', NULL,'2024-03-19T22:39:32.000Z',NULL, 'refcontact', 'Referral Contact','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (50,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Patient Triage', NULL,'2024-03-19T22:39:32.000Z',NULL, 'pttriage', 'Patient Triage','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (51,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Refund', NULL,'2024-03-19T22:39:32.000Z',NULL, 'refund', 'Refund','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (52,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'Release of Medical Records', NULL,'2024-03-19T22:39:32.000Z',NULL, 'releasemedrec', 'Release of Medical Records','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (53,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'SNF/LTAC Contact', NULL,'2024-03-19T22:39:32.000Z',NULL, 'snfltaccontact', 'SNF/LTAC Contact','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';
INSERT INTO form_list_bill_note_subject ("id", "created_by", "change_by", "updated_by", "reviewed_by", "reviewed_on", "deleted", "archived", "updated_on", "auto_name", "change_data", "created_on", "change_on", "code", "name", "sys_period", "allow_sync", "active") VALUES (54,1,NULL,1,NULL,NULL,NULL,NULL,'2024-09-25T19:51:12.000Z', 'STATEMENT NOT MAILED', NULL,'2024-03-19T22:39:32.000Z',NULL, 'statementnotmailed', 'STATEMENT NOT MAILED','["2024-09-25T19:51:12.000Z",]','Yes', 'Yes') ON CONFLICT (id) DO UPDATE SET "created_by" = EXCLUDED."created_by", "change_by" = EXCLUDED."change_by", "updated_by" = EXCLUDED."updated_by", "reviewed_by" = EXCLUDED."reviewed_by", "reviewed_on" = EXCLUDED."reviewed_on", "deleted" = EXCLUDED."deleted", "archived" = EXCLUDED."archived", "updated_on" = EXCLUDED."updated_on", "auto_name" = EXCLUDED."auto_name", "change_data" = EXCLUDED."change_data", "created_on" = EXCLUDED."created_on", "change_on" = EXCLUDED."change_on", "name" = EXCLUDED."name", "sys_period" = EXCLUDED."sys_period" WHERE form_list_bill_note_subject.allow_sync = 'Yes';

COMMIT;
