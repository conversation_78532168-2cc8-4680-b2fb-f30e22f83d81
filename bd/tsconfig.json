{
    "extends": "@clara/config/tsconfig/base.json",
    "files": [],
    "compilerOptions": {
        "paths": {
            // apps
            "@clara/cli/*": ["apps/cli/*"],
            "@clara/homebase/*": ["apps/homebase/*"],
            // packages
            "@clara/config/*": ["packages/config/*"],
            "@clara/dsl/*": ["packages/dsl/*"],
            "@clara/tslib/*": ["packages/tslib/*"]
        }
    },
    "references": [
        {
            "path": "./apps/cli"
        },
        {
            "path": "./apps/homebase"
        },
        {
            "path": "./packages/config"
        },
        {
            "path": "./packages/dsl"
        },
        {
            "path": "./packages/tslib"
        }
    ]
}
