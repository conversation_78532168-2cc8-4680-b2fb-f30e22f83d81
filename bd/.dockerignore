# Manually update the exclude list to handle same cases as:
#   bd/.gitignore
#   bd/**/.gitignore
#   bd/cli/src/commands/fly/watch.ts
#   bd/nes/container/exec/nes.watcher

# Exclude non-deployable bd/ folders
./admin-console
./ansible
./bi
./cmigrate
./configs
./db-manager
./edoc
./fly-builder
./integration-server
./jshomebase
./jsshared
./kubernetes
./tshomebase

# Exclude compiled/dev/temp root bd/ folders
./.cursor
./build
./dsl
./localdev

# Exclude non-deployable bd/ root files
./.aider*
./.augment-*
./.dockerignore
./.eslint*
./.tmux*
./*server.js
./BrewFile
./eslint*
./README

# Exclude build/package folders & files
**/.DS_Store
**/.env*
**/.git*
**/*-lock.json
**/*-lock.y*ml
**/*-pipelines.y*ml
**/dist
**/docker-compose.yml
**/Dockerfile
**/fly.toml*
**/node_modules
**/www

# Project-Specific
./packages/dsl/src/base/*
