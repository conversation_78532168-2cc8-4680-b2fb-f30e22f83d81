import type { Context } from "../types/base.d.ts"
import type { Response, NextFunction } from "npm:express"
// Todo: Dynamic Error Parsing
export const errorHandler = (err: any, ctx: Context, res: Response, next: NextFunction) => {
    ctx.shared.logger.error(err.message)
    res.status(err.status || 500).json({ status: err.status || 500, message: err.message })
}

export const errorCatch = (_ctx: Context, _res: Response, next: NextFunction) => {
    try {
        next()
    } catch (err) {
        next()
    }
}