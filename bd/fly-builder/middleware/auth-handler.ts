import type { Context } from "../types/base.d.ts"
import type { Response, NextFunction } from "npm:express"
// Todo: Dynamic Error Parsing

export const authHandler = async (ctx: Context, res: Response, next: NextFunction) => {
    const { baseUrl, body, headers, method, originalUrl, params, query } = ctx

    let ekey = headers['clara-api-key']
    if (ekey && Deno.env.get('CLARA_AUTH_TOKEN') == ekey) {
        next()
        ctx?.shared?.logger?.debug({ baseUrl, body, method, originalUrl, params, query })
        return
    }
    res.status(403).json({ status: 403, message: 'Unauthorized.' })
}
