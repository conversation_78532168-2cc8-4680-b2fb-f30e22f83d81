import * as path from "https://deno.land/std@0.57.0/path/mod.ts";
import type { Express, Handler } from "npm:express"
import { Router } from "npm:express"
import * as asyncp from "npm:async-middleware"
/*
    express-file-routing 2.0.3
    Converted for Deno Support
*/
export interface Options {
    directory?: string
    additionalMethods?: string[]
}

export interface File {
    name: string
    path: string
    rel: string
}

export interface Route {
    url: string
    priority: number
    exports: {
        default?: Handler
        get?: Handler
        post?: Handler
        put?: Handler
        patch?: Handler
        delete?: Handler
        [x: string]: Handler
    }
}

const REQUIRE_MAIN_FILE = path.dirname(path.fromFileUrl(import.meta.url))
const config = {
    VALID_FILE_EXTENSIONS: [".ts"],
    INVALID_NAME_SUFFIXES: [".d.ts"],
    IGNORE_PREFIX_CHAR: "_",
    DEFAULT_METHOD_EXPORTS: [
        "get",
        "post",
        "put",
        "patch",
        "delete",
        "head",
        "connect",
        "options",
        "trace"
    ]
}

export const walkTree = (directory: string, tree: string[] = []) => {
    const results: File[] = []

    for (const file of Deno.readDirSync(directory)) {
        const fileName = file.name
        const filePath = path.join(directory, fileName)
        const fileStats = Deno.statSync(filePath)
        if (fileStats.isDirectory) {
            results.push(...walkTree(filePath, [...tree, fileName]))
        } else {
            results.push({
                name: fileName,
                path: directory,
                rel: mergePaths(...tree, fileName)
            })
        }
    }

    return results
}

export const generateRoutes = async (files: File[]) => {
    const routes: Route[] = []

    for (const file of files) {
        const parsedFile = path.parse(file.rel)

        if (isFileIgnored(parsedFile)) continue

        const directory = parsedFile.dir === parsedFile.root ? "" : parsedFile.dir
        const name = parsedFile.name.startsWith("index")
            ? parsedFile.name.replace("index", "")
            : `/${parsedFile.name}`

        const url = convertParamSyntax(directory + name)
        const priority = calculatePriority(url)
        const exports = await import(path.join(file.path, file.name))

        routes.push({
            url,
            priority,
            exports
        })
    }

    return prioritizeRoutes(routes)
}
const createRouter = async <T = Express>(app: T, options: Options = {}): T => {
    const files = walkTree(
        options.directory || path.join(REQUIRE_MAIN_FILE, "routes")
    )

    const routes = await generateRoutes(files)

    for (const { url, exports } of routes) {
        const exportedMethods = Object.entries(exports)

        for (const [method, handler] of exportedMethods) {
            const methodKey = getMethodKey(method)
            let handlers = getHandlers(handler)

            if (
                !options.additionalMethods?.includes(methodKey) &&
                !config.DEFAULT_METHOD_EXPORTS.includes(methodKey)
            )
                continue
            handlers = handlers.map((handler)=> asyncp.wrap(handler))
            app[methodKey](url, ...handlers)
        }

        // wildcard default export route matching
        if (typeof exports.default !== "undefined") {
            ; (app as unknown as Express).all(url, ...getHandlers(exports.default))
        }
    }

    return app
}

export default createRouter

export const router = async (options: Options = {}) => {
    return await createRouter(Router(), options)
}



export const isFileIgnored = (parsedFile: path.ParsedPath) =>
    !config.VALID_FILE_EXTENSIONS.includes(parsedFile.ext.toLowerCase()) ||
    config.INVALID_NAME_SUFFIXES.some(suffix =>
        parsedFile.base.toLowerCase().endsWith(suffix)
    ) ||
    parsedFile.name.startsWith(config.IGNORE_PREFIX_CHAR) ||
    parsedFile.dir.startsWith(`/${config.IGNORE_PREFIX_CHAR}`)


export const prioritizeRoutes = (routes: Route[]) =>
    routes.sort((a, b) => a.priority - b.priority)

export const mergePaths = (...paths: string[]) =>
    `/${paths.filter(path => path !== "").join("/")}`

const regBackets = /\[([^}]*)\]/g

const transformBrackets = (value: string) =>
    regBackets.test(value) ? value.replace(regBackets, (_, s) => `:${s}`) : value

export const convertParamSyntax = (path: string) => {
    const subpaths: string[] = []

    for (const subpath of path.split("/")) {
        subpaths.push(transformBrackets(subpath))
    }

    return mergePaths(...subpaths)
}

export const calculatePriority = (url: string) => {
    const depth = url.match(/\/.+?/g)?.length || 0
    const specifity = url.match(/\/:.+?/g)?.length || 0

    return depth + specifity
}

export const getHandlers = handler => {
    if (!Array.isArray(handler)) return [handler]

    return handler
}

export const getMethodKey = (method: string) => {
    let methodKey = method.toLowerCase()

    if (methodKey === "del") return "delete"

    return methodKey
}

