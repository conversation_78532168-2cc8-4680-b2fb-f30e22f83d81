import type { Request } from "npm:express"
export interface Context extends Request {
    shared: Shared
    body: ReqBody
}

export interface ReqBody{
    [key:string]: String | Array<String> | Object
}
interface ImageRef {
    registry: string;
    repository: string;
    tag?: string;
    digest: string;
  }
  
  interface Event {
    type: string;
    status: string;
    source: string;
    timestamp: number;
  }
  
  interface FlyMachine {
    id: string;
    name: string;
    state: string;
    region: string;
    image_ref: ImageRef;
    instance_id: string;
    private_ip: string;
    created_at: string;
    updated_at: string;
    config: any; // Replace with the actual type if known
    events: Event[];
  }
export interface DBConnection {
    execute: Function
    exists: Function
    fetch: Function
}

export interface ExecuteOptions {
    dump: boolean,
}
export interface DBFilter {
    //field, cond, val
    where: Array<[string, string, any]>,
    limit?: number
}
export interface Logger {
    info: Function,
    debug: Function,
    warn: Function,
    error: Function
}
export interface Shared {
    db: DBConnection,
    logger: Logger,
}