const REQUIRED_CONFIGS = ['CLARA_AUTH_TOKEN', 'GIT_AUTH_TOKEN', 'FLY_AUTH_TOKEN']
const flyGQLServer = "https://api.fly.io/graphql";
const flyWGServer = "http://_api.internal:4280";
const flyServer = "https://api.machines.dev";
export const missingConfig = () => {
  let missing = []
  REQUIRED_CONFIGS.forEach((k) => {
    if (!Deno.env.get(k)) {
      missing.push(k)
    }
  })
  return missing
}

export const getType = (val: any): string => {
  if (val === null) {
    return 'null'
  }
  if (typeof val === 'boolean') {
    return 'boolean'
  }
  if (typeof val === 'string') {
    return 'string'
  }
  if (typeof val === 'number') {
    return 'number'
  }
  if ((typeof val === 'object') && (val instanceof Array)) {
    return 'array'
  }
  if ((typeof val === 'object') && !(val instanceof Array)) {
    return 'object'
  }
  return 'undefined'
}

export const isFalsy = (val: any): boolean => {
  const tp: string = getType(val)
  let falsy = false
  if (['null', 'boolean', 'string', 'number', 'undefined'].includes(tp)) {
    falsy = !val
  } else {
    if (tp == 'array' && val.length == 0) {
      falsy = true
    } else if (tp == 'object' && Object.keys(val).length == 0) {
      falsy = true
    }
  }
  return falsy
}

export const equalIgnoreCase = (...args: Array<string>) => {
  return new Set(args.map(v => v.toLowerCase())).size == 1
}

export const sendFlyRequest = async (url, token = '' ,method = 'GET', body = null) => {
  let t = Deno.env.get('FLY_AUTH_TOKEN');
  let options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
      'Authorization': `Bearer fo1_8nCYRdIsyDubsq8Dj3p1tkD9LnOKyAgkM0Yhg0BoRTY`,
    }
  }
  if(token && typeof token === 'string' && token !== '') {
    options.headers['Authorization'] = `Bearer ${token}`
  }
  if(body) {
    options.body = JSON.stringify(body)
  }
  let res = await fetch(flyServer + url, options )
  return res;
}