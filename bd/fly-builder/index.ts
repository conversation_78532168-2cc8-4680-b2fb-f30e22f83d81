import express from "npm:express";
import type { Application, Response, NextFunction } from "npm:express";
import * as path from "https://deno.land/std@0.57.0/path/mod.ts";

import { router } from "./helper/fs-router.ts";
import { errorHandler } from "./middleware/error-handler.ts";
import { ELog } from "./helper/logger.ts";
import { Context, Shared } from "./types/base.d.ts";
import { loggingHandler } from "./middleware/log-handler.ts";
import { authHandler } from "./middleware/auth-handler.ts";
import { missingConfig } from "./utils/fx.ts";

import { dotEnvConfig } from './deps.ts';
dotEnvConfig({ export: true, safe: true });



const app: Application = express();

const routes = await router({
  directory: path.join(path.dirname(path.fromFileUrl(import.meta.url)), "api")
})

const shared: Shared = {
  logger: await new ELog()
}

const mc = missingConfig()
if (mc.length > 0) {
  shared.logger?.info(`MISSING AUTH TOKEN CONFIG IN .env FILE ${mc}`);
  Deno.exit(1)
}
app.use(authHandler)
app.use(express.json())

//Binding Debugger and Connection
app.use((ctx: Context, _res: Response, next: NextFunction) => {
  ctx.shared = shared
  next()
})
app.use(loggingHandler)
app.use('/', routes)
app.use(errorHandler)
const PORT = Deno.env.get('SERVER_PORT') || 3000
app.listen(PORT)

console.log(`listening on http://localhost:${PORT}/`)