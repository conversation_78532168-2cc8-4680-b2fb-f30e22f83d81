import type { Context, FlyMachine } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import { sendFlyRequest } from "../../utils/fx.ts";

import { gql, GraphQLClient } from "https://deno.land/x/graphql_request/mod.ts";

const getValidateSchema: AnyZodObject = z.object({
    query: z.object({
        slug: z.string(),
        env: z.enum(['prod', 'staging', 'testing', 'dev']).transform((val) => val.toLowerCase().trim()),
        fly_nes: z.string()
    }),
})
export const get = [
    async (ctx: Context, resp: Response) => {
        resp.status(200).send({ 'method': 'post 2' })
    }
]
async function getMachinesByAppName(appName: string): Promise<FlyMachine[]> {
    let url: string = '/v1/apps/' + appName + '/machines';
    let machinesArray: FlyMachine[] = [];
    let resp = await sendFlyRequest(url);
    if (!resp.ok) {
        throw new Error("Fly Api Error: " + resp.statusText)
    } else {
        machinesArray = await resp.json();
    }
    return machinesArray;
}

async function updateMachines(appName: string, secrets, token, machinesArray) {
    for (let machine of machinesArray) {
        let url = `/v1/apps/${appName}/machines/${machine.id}`;
        let config = machine.config;
        let body = {
            "config": config
        }
        console.log("Updating machine with id: " + machine.id);
        let resp = await sendFlyRequest(url, '', 'POST', body);
        if (!resp.ok) {
            console.error(resp)
            throw new Error("Update Failed App Name: " + appName + " Machine ID: " + machine.id)
        } else {
            console.log(`App Name ${appName} Machine ID: ${machine.id} Updated`)
            resp = await resp.json();
        }
    }
    return { error: false, message: `Machines for App ${appName} updated successfully` };
}
async function setSecrets(appID, secrets, token) {
    // we don't have rest API at the time of using this
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
        headers: {
            authorization: `Bearer ${token}`,
        },
    });

    const query = gql`
    mutation($input: SetSecretsInput!) {
        setSecrets(input: $input) {
            app {
                id
            }
            }
        }
    `;
    const variables = { input: { appId: appID, secrets: secrets } };
    const data = await graphQLClient.request(query, variables);
    return data;
}
async function restartMachinesArray(appName: string, machinesArray: FlyMachine[]) {
    await updateMachines(appName, {}, '', machinesArray);
    return { error: false, message: `Machines for App ${appName} restarted successfully` };
}

async function restartMachinebyID(appName, machineID) {
    let stopURL = `/v1/apps/${appName}/machines/${machineID}/stop`;
    let startURL = `/v1/apps/${appName}/machines/${machineID}/start`;
    let resp = await sendFlyRequest(stopURL, '', 'POST');
    if (!resp.ok) {
        throw new Error("Fly Api error stopping machine ID: " + machineID)
    } else {
        await new Promise(resolve => setTimeout(resolve, 1000));
        let resp = await sendFlyRequest(startURL, '', 'POST');
        if (!resp.ok) {
            throw new Error("Fly Api error starting machine Error: " + resp.statusText)
        }
        resp = await resp.json();
    }
    return resp
}

export const post = [
    async (ctx: Context, resp: Response) => {
        const token = Deno.env.get('FLY_AUTH_TOKEN')
        const data = ctx.body;
        const appName = `${data.env[0].toLowerCase()}-${data.customer}-nes`
        try {
            await setSecrets(appName, data.secrets, token);
            let machinesArray: FlyMachine[] = await getMachinesByAppName(appName);
            await restartMachinesArray(appName, machinesArray);
            console.log("Machines restarted successfully")
            resp.status(200).send({ "status": 200, "message": "App secrets updated." })
        } catch (error) {
            resp.status(500).send({ "message": error.message })
        }
    }
]
