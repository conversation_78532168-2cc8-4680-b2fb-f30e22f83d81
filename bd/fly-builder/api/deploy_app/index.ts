import type { Context } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { validate } from "../../middleware/validator.ts"
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import { isFalsy } from "../../utils/fx.ts";

import { gql, GraphQLClient } from "https://deno.land/x/graphql_request/mod.ts";
import { execSequence, OutputMode } from "https://deno.land/x/exec/mod.ts";

const getValidateSchema: AnyZodObject = z.object({
    query: z.object({
        slug: z.string(),
        env: z.enum(['prod', 'staging', 'testing', 'dev']).transform((val) => val.toLowerCase().trim()),
        fly_nes: z.string()
    }),
})
export const get = [
    async (ctx: Context, resp: Response) => {
        resp.status(200).send({'method':'get'})
    }
]
async function getapps(token){
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        query {
            apps {
                nodes {
                    id
                    name
                    hostname
                    }
                }
            }
        `;
        console.log("I'm here")
        const data = await graphQLClient.request(query);
        console.log(data)
        return data;
}

async function getappsbyid(appId,token){
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        query($appId: String!) {
            app(id: $appId) {
                    id
                    name
                }
            }
        `;
        const variables = { appId: appId };
        // console.log("I'm here")
        const data = await graphQLClient.request(query, variables);
        // console.log(data)
        return data;
}

async function getappsbyname(appName,token){
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        query($appName: String!) {
            app(name: $appName) {
                    id
                    name
                }
            }
        `;
        const variables = { appName: appName };
        const data = await graphQLClient.request(query, variables);
        // console.log("This is : " + data)
        return data;
}

async function setsecrets(appID,secrets,token){
    
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        mutation($input: SetSecretsInput!) {
            setSecrets(input: $input) {
                app {
                    id
                }
                }
            }
        `;
        const variables = {input: { appId: appID, secrets:secrets}};
        // console.log("I'm here")
        const data = await graphQLClient.request(query, variables);
        console.log(data)
        return data;
}

async function createapp(appName,token){
    try {
        const dt = await getappsbyname(appName,token)
        return "App already exists"
    } catch (error) {
        const endpoint = "https://api.fly.io/graphql";
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        mutation($input: CreateAppInput!) {
            createApp(input: $input) {
                app {
                    id
                    name
                    organization {
                        slug
                    }
                    config {
                        definition
                    }
                    regions {
                        name
                        code
                    }
                }
            }
        }
        `;
        const variables = {
            "input": {
            "organizationId": "D307G6NwgR0z2u4vPG23jYy8a3cw2wVbR",
            "name": appName,
            "preferredRegion": "ord"
            }
        };
        const data = await graphQLClient.request(query, variables);
        return data;
    }
}

async function deleteapp(appName,token){
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
    const query = gql`
    mutation($appId: ID!) {
        deleteApp(appId: $appId){
        __typename
        }
    }
    `;
    const variables = {"appId": appName};
    const data = await graphQLClient.request(query, variables);
    return data;
}

async function deployapp(appName,image,token){
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
    const query = gql`
    mutation($input: DeployImageInput!){
        deployImage(input: $input){
            app{
                name,
                status
            }
            release{
                description,
                id
            }
        }
    }
    `;
    const variables = {
        "input": {
            "appId": appName,
            "image": image
            }
        }
        ;
    const data = await graphQLClient.request(query, variables);
    console.log(data)
    return data;
}

async function allocateip(appName,token){
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
    const query = gql`
    mutation($input: AllocateIPAddressInput!){
        allocateIpAddress(input: $input){
        ipAddress{
                address
                createdAt
                id
                region
            }
            app{
                appUrl
            }
        }  
    }
    `;
    const variables = {
        "input": {
            "appId": appName,
            "type": 'v4'
            }
        }
        ;
    const data = await graphQLClient.request(query, variables);
    return data;
}



export const post = [
    async (ctx: Context, resp: Response) => {
        const token = Deno.env.get('FLY_AUTH_TOKEN')
        
        const data = ctx.body;
        
        const appname = `${data.env[0].toLowerCase()}-${data.customer}-nes`
        try {
            const dt = await getappsbyname(appname,token)
        } catch (error) {
            try {
                const capp = await createapp(appname,token)
                const allip = await allocateip(appname,token)
            } catch (error) {
                resp.status(404).send({"status": 500, "message": "App creation or allocate ip does not succeed."})
            }
        }
        try {
            const up_secrets = await setsecrets(appname,data.secrets,token)
            try {
                const da = await deployapp(appname,`registry.fly.io/elabs-images:${data.tag}`,token)
                resp.status(200).send({"status": 200, "message": "App related docker is being building."})
            } catch (error) {
                console.log("There is an issue with image deployment")
                resp.status(404).send({"status": 500, "message": `Image with ${data.tag} does not exist in fly registry you first need to build the image and push it to fly registry.`})
            }
        } catch (error) {
            if(error.response.errors[0].extensions.code == "UNCHANGED"){
                try {
                    const da = await deployapp(appname,`registry.fly.io/elabs-images:${data.tag}`,token)
                    resp.status(200).send({"status": 200, "message": "Image with the reluctant tag has been deployed."})
                } catch (error) {
                    resp.status(404).send({"status": 500, "message": `Image with ${data.tag} does not exist in fly registry you first need to build the image and push it to fly registry.`})
                }
            }
            resp.status(404).send({"status": 500, "message": "App secrets failed to get updated."})
        }
        // resp.status(200).send({'method':'post'})
    }
]
