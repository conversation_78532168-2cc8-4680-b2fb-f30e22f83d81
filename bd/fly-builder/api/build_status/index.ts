import type { Context } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { validate } from "../../middleware/validator.ts"
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import { isFalsy } from "../../utils/fx.ts";


const getValidateSchema: AnyZodObject = z.object({
    query: z.object({
        slug: z.string(),
        env: z.enum(['prod', 'staging', 'testing', 'dev']).transform((val) => val.toLowerCase().trim()),
        fly_nes: z.string()
    }),
})
export const get = [
    async (ctx: Context, resp: Response) => {
        const tag = ctx.res.req.body;
        console.log(tag)
        resp.status(200).send({'method':'post 2'})
    }
]

export const post = [
    async (ctx: Context, resp: Response) => {
        resp.status(200).send({'method':'post'})
    }
]
