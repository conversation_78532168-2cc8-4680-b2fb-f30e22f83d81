import type { Context } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { validate } from "../../middleware/validator.ts"
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import { isFalsy } from "../../utils/fx.ts";

import { gql, GraphQLClient } from "https://deno.land/x/graphql_request/mod.ts";
import { execSequence, OutputMode } from "https://deno.land/x/exec/mod.ts";

const getValidateSchema: AnyZodObject = z.object({
    query: z.object({
        slug: z.string(),
        env: z.enum(['prod', 'staging', 'testing', 'dev']).transform((val) => val.toLowerCase().trim()),
        fly_nes: z.string()
    }),
})
export const get = [
    async (ctx: Context, resp: Response) => {
        resp.status(200).send({'method':'get'})
    }
]
async function getapps(token){
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        query {
            apps {
                nodes {
                    id
                    name
                    hostname
                    }
                }
            }
        `;
        console.log("I'm here")
        const data = await graphQLClient.request(query);
        console.log(data)
        return data;
}

async function getappsbyid(appId,token){
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        query($appId: String!) {
            app(id: $appId) {
                    id
                    name
                }
            }
        `;
        const variables = { appId: appId };
        // console.log("I'm here")
        const data = await graphQLClient.request(query, variables);
        // console.log(data)
        return data;
}

async function getappsbyname(appName,token){
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        query($appName: String!) {
            app(name: $appName) {
                    id
                    name
                }
            }
        `;
        const variables = { appName: appName };
        const data = await graphQLClient.request(query, variables);
        // console.log("This is : " + data)
        return data;
}

async function setsecrets(appID,secrets,token){
    
    const endpoint = "https://api.fly.io/graphql";
        
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        mutation($input: SetSecretsInput!) {
            setSecrets(input: $input) {
                app {
                    id
                }
                }
            }
        `;
        const variables = {input: { appId: appID, secrets:secrets}};
        // console.log("I'm here")
        const data = await graphQLClient.request(query, variables);
        // console.log(data)
        return data;
}

async function createapp(appName,token){
    try {
        const dt = await getappsbyname(appName,token)
        return "App already exists"
    } catch (error) {
        const endpoint = "https://api.fly.io/graphql";
        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
        
        const query = gql`
        mutation($input: CreateAppInput!) {
            createApp(input: $input) {
                app {
                    id
                    name
                    organization {
                        slug
                    }
                    config {
                        definition
                    }
                    regions {
                        name
                        code
                    }
                }
            }
        }
        `;
        const variables = {
            "input": {
            "organizationId": "D307G6NwgR0z2u4vPG23jYy8a3cw2wVbR",
            "name": appName,
            "preferredRegion": "ord"
            }
        };
        const data = await graphQLClient.request(query, variables);
        return data;
    }
}

async function deleteapp(appName,token){
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
    const query = gql`
    mutation($appId: ID!) {
        deleteApp(appId: $appId){
        __typename
        }
    }
    `;
    const variables = {"appId": appName};
    const data = await graphQLClient.request(query, variables);
    return data;
}

async function deployapp(appName,image,token){
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
    const query = gql`
    mutation($input: DeployImageInput!){
        deployImage(input: $input){
            app{
                name,
                status
            }
            release{
                description,
                id
            }
        }
    }
    `;
    const variables = {
        "input": {
            "appId": appName,
            "image": image
            }
        }
        ;
    const data = await graphQLClient.request(query, variables);
    return data;
}

async function allocateip(appName,token){
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
            authorization: `Bearer ${token}`,
            },
        });
    const query = gql`
    mutation($input: AllocateIPAddressInput!){
        allocateIpAddress(input: $input){
        ipAddress{
                address
                createdAt
                id
                region
            }
            app{
                appUrl
            }
        }  
    }
    `;
    const variables = {
        "input": {
            "appId": appName,
            "type": 'v4'
            }
        }
        ;
    const data = await graphQLClient.request(query, variables);
    return data;
}



export const post = [
    async (ctx: Context, resp: Response) => {
        const access_token = Deno.env.get('GIT_AUTH_TOKEN')
        const data = ctx.body;
        console.log(data)
        const appname = `${data.env[0].toLowerCase()}-${data.customer}-nes`
        // const data = {"customer":"eahmad", "env":"test", "tag":"HB-4626", "secrets":[{key: "DATABASE_URL", value: "postgresql://hb:<EMAIL>:5432/hb"},{key: "FLY_NES", value: "D39ix7l0YGOsz1wtf0LQd67VX9F9xpj8"},{key:"FLY_ID",value:"d-etayyab-nes"}]}
        // const appname = 'elabs-images'
        let cmdsList: Array<String> = []

        let build_str = `sudo docker build -t registry.fly.io/elabs-images:${data.tag} . --build-arg TAG=${data.tag} --build-arg TOKEN=${access_token}`
        let fly_auth_str = `flyctl auth docker`;
        let d_push = `docker push registry.fly.io/elabs-images:${data.tag}`
        let doc_prune = `docker system prune -a -f`
        // ctx.shared.db.insertBuildStatus(`registry.fly.io/elabs-images:${data.tag}`, "in progress")
        resp.status(200).send({"status": 200, "message": `${data.tag} related docker is being building.`})
        cmdsList = cmdsList.concat([build_str, fly_auth_str, d_push, doc_prune])
        let res_build = execSequence(cmdsList, {output: OutputMode.Capture, continueOnError: false});
        
        // let response = exec(`ls`,{output: OutputMode.Capture});
        res_build.then((result) => {
            console.log(JSON.stringify(result))
            if(result[0].status.success && result[1].status.success && result[2].status.success && result[3].status.success){
                console.log("Image has been build and pushed and pruned successfully.")
                // resp.status(424).send(error)
                // ctx.shared.db.insertBuildStatus(`registry.fly.io/elabs-images:${data.tag}`, "build successful")
            }
        })
    }
]