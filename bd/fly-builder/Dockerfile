FROM node:19.6.0


# Installing deps

ARG TOKEN
ARG TAG
RUN apt-get update -y

RUN apt-get install nano -y

RUN apt-get install lsof -y

RUN apt-get install rsync -y

RUN apt-get install openssh-client -y

RUN apt-get install supervisor -y


# Installing global node packages

RUN npm install -g npm@9.4.1

RUN npm install -g grunt-cli

RUN npm install -g grunt

RUN npm install -g nodemon


# Making directories and copying over sfuff

RUN mkdir -p /opt/clara

RUN mkdir -p /var/log/nes

WORKDIR /opt/clara 

RUN git clone -b $TAG https://flybuild:${TOKEN}@gitlab.com/envoy-labs/bd.git .

RUN cp supervisord.conf /etc/supervisor/

RUN rm supervisord.conf

# Installing deps for clients and building frontend

WORKDIR /opt/clara/tshomebase/

RUN yarn install

WORKDIR /opt/clara/jsshared

RUN yarn install

WORKDIR /opt/clara/jshomebase

RUN yarn install


# Updating node perms

WORKDIR /opt/clara

RUN chown node:node /var/log/nes

RUN chown node:node /opt/clara/nes

# Installing deps for NES

WORKDIR /opt/clara/nes

RUN npm install --unsafe-perm

RUN npm install pdfmake --unsafe-perm


# starting NES

WORKDIR /opt/clara/nes

EXPOSE 8080 5173

CMD supervisord

