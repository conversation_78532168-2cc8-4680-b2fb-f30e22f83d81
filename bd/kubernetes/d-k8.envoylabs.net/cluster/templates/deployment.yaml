apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: isolate-{{ .Values.clusterName }}
  namespace: crx-db
spec:
  endpointSelector: {}
  ingress:
    - fromEntities:
        - cluster
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: "5432"
    - fromEndpoints:
        - matchLabels:
            cnpg.io/cluster: "{{ .Values.clusterName }}"
  egress:
    - toEntities:
        - cluster
    - toEndpoints:
        - matchLabels:
            io.kubernetes.pod.namespace: kube-system
            k8s-app: kube-dns
    - toEndpoints:
        - matchLabels:
            cnpg.io/cluster: "{{ .Values.clusterName }}"
---
apiVersion: postgresql.cnpg.io/v1
kind: Pooler
metadata:
  name: pooler-{{ .Values.clusterName }}-rw
  namespace: crx-db
spec:
  cluster:
    name: {{ .Values.clusterName }}
  instances: 1
  type: rw
  pgbouncer:
    poolMode: session
    parameters:
      max_client_conn: "1000"
      default_pool_size: "1000"
---
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: {{ .Values.clusterName }}
  namespace: crx-db
spec:
  instances: 3
  postgresql:
    parameters:
      idle_session_timeout: "0"
      idle_in_transaction_session_timeout: "0"
      statement_timeout: "0"
      max_connections: "1000"
      shared_buffers: "1024MB"
  resources:
    requests:
      memory: "8Gi"
      cpu: 1
    limits:
      memory: "8Gi"
      cpu: 1
  bootstrap:
    initdb:
      database: hb
      owner: hb
      dataChecksums: true
      postInitSQL:
        - ALTER USER "hb" WITH CREATEDB SUPERUSER REPLICATION BYPASSRLS
  storage:
    storageClass: rook-ceph-block
    size: {{ .Values.dbSize }}
  walStorage:
    storageClass: rook-ceph-block
    size: {{ .Values.walSize }}
  backup:
    volumeSnapshot:
      className: csi-rbdplugin-snapclass
---
apiVersion: postgresql.cnpg.io/v1
kind: Pooler
metadata:
  name: pooler-{{ .Values.clusterName }}-ro
  namespace: crx-db
  labels:
    network: {{ .Values.clusterName }}
spec:
  cluster:
    name: {{ .Values.clusterName }}
  instances: 1
  type: ro
  pgbouncer:
    poolMode: session
    parameters:
      max_client_conn: "1000"
      default_pool_size: "1000"
---
apiVersion: v1
kind: Service
metadata:
  name: ingress-{{ .Values.clusterName }}-rw
  namespace: crx-db
  labels:
    bgp-policy: a
spec:
  type: LoadBalancer
  externalIPs:
    - {{ .Values.wanIP }}
  ports:
    - name: postgres
      port: {{ .Values.rwPort }}
      targetPort: 5432
      protocol: TCP
  selector:
    cnpg.io/poolerName: pooler-{{ .Values.clusterName }}-rw
---

apiVersion: v1
kind: Service
metadata:
  name: ingress-{{ .Values.clusterName }}-ro
  namespace: crx-db
  labels:
    bgp-policy: a
spec:
  type: LoadBalancer
  externalIPs:
    - {{ .Values.wanIP }}
  ports:
    - name: postgres
      port: {{ .Values.roPort }}
      targetPort: 5432
      protocol: TCP
  selector:
    cnpg.io/poolerName: pooler-{{ .Values.clusterName }}-ro
