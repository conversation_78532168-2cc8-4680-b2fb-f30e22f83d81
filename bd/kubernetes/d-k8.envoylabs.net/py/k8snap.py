#!/usr/bin/env python3

from kubernetes import client, config
import sys
from datetime import datetime

if len(sys.argv) != 3:
  print(f'{sys.argv[0]} <src> <name>')
  sys.exit(1)

config.load_kube_config()
api = client.CustomObjectsApi()

# Get the current date and time, and format it as a string
current_time = datetime.now().strftime("%Y%m%d%H%M%S")

rsrc = {
  "apiVersion": "postgresql.cnpg.io/v1",
  "kind": "Backup",
  "metadata": {
    "name": f'{sys.argv[2]}-{current_time}'
  },
  "spec": {
    "cluster": {
      "name": sys.argv[1]
    },
    "method": 'volumeSnapshot'
  }
}

api.create_namespaced_custom_object(
  group='postgresql.cnpg.io',
  version='v1',
  namespace='crx-db',
  plural='backups',
  body=rsrc
)
