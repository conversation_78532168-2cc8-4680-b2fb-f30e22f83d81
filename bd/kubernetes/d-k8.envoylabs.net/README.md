# DB CLUSTER NAMING CONVENTION

```
 dev:
   d-customer-db  # pg db for 'customer' name
   d-eusername-db #       for 'username' employee
test:
   t-customer-db  #       for 'customer' name
   t-eusername-db #       for 'username' employee
stage:
   s-customer-db  #       for 'customer' name
   s-eusername-db #       for 'username' employee
prod:
   p-customer-db  #       for 'customer' name
   p-eusername-db #       for 'username' employee
```

# DB CLUSTER DEFAULTS

```
db pods:
  1x master: read-write (rw)
  2x read-replicas: read-only (ro)
pooler pods:
  1x read-write: rw to master
  1x read-only: ro load-balanced to replicas
service:
  1x rw port
  1x ro port
```

# REQUIREMENTS

## OSX brew installs

```
brew install helm
brew install jq
brew install kubectl
brew install kubectl-cnpg
```

## Install python3 in .venv with kubernetes

```
python3 -m venv .venv
source .venv/bin/activate
python3 -m pip install kubernetes
```

# KUBERNETES

## Show all db pods

```
./k8pod.sh
```

## Show running db pods with cpu/ram usage

```
./k8pod.sh top
```

## Show all ports assigned to db pods (sorted by port desc)

```
./k8port.sh
```

# SNAPSHOTS

## List snapshots of dbs

```
./k8snap.sh
```

## Create snapshot

```
./k8snap.sh <db_cluster_name> <snapshot_name>
```

## Delete snapshot

```
./k8snaprm.sh <snapshot_name>
```

# DATABASE CLUSTER

## Make new empty pg db cluster

```
./k8dbnew.sh <db_new_cluster_name> <rw_port> <ro_port>
```

## Restore new pg db cluster from snapshot

```
./k8dbcopy.sh <db_new_cluster_name> <rw_port> <ro_port> <snapshot_name>
```

## Delete pg cluster

```
./k8dbrm.sh <db_cluster_name>
```

## Get pass

```
./k8dbpass.sh <db_cluster_name>
```

## Fix pass (fixes pass on dbs copied from src into dst)

```
./k8dbpass.sh <db_src_cluster_name> <db_dst_cluster_name>
```

# K8 MANAGEMENT COMMAND EXAMPLES

## Show Helm list/status

```
helm list
```

## Detailed info about a physical HW node

```
kubectl describe node d-k8-01
```

## Show resources allocated per node

```
kubectl describe nodes
```

## Show cnpg status for <db_src_cluster_name>

```
kubectl cnpg status d-customer-db -n crx-db
```

## Search through the PG logs of cluster <db_src_cluster_name>

```
kubectl cnpg logs cluster d-customer-db -n crx-db | grep FATAL
```

## Detailed info about a pod <db_src_cluster_name-#>

```
kubectl describe pod d-customer-db-1 -n crx-db
```

## Shell into a specific pod <db_src_cluster_name-#>

```
kubectl exec -it d-customer-db-1 -n crx-db -- /bin/sh
```

## Delete (only way to "reboot") a pod <db_src_cluster_name-#>

```
kubectl delete pod d-customer-db-1 -n crx-db --grace-period=0 --force
```

## Manually change DB password + kube secret to: NEW_PASSWORD

psql [current_full_postgresql_connection_string] -c "ALTER USER hb WITH PASSWORD 'NEW_PASSWORD';"

### Make sure to append -app to <db_src_cluster_name>

kubectl create secret generic d-customer-db-app -n crx-db --from-literal=password='NEW_PASSWORD' --dry-run=client -o yaml | kubectl apply -f -

# K8 TROUBLESHOOTING

## Delete a DB that is not installing properly

```
helm uninstall d-customer-db
```

## Delete a stuck job

```
kubectl delete job d-customer-db-1-snapshot-recovery -n crx-db
```

## CEPH commands, useful when troubleshooting

```
kubectl exec -it -n rook-ceph rook-ceph-tools-66b77b8df5-6g2j4 -- bash
ceph -s
ceph df
ceph osd tree
```