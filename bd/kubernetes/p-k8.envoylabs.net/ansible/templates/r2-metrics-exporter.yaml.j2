apiVersion: apps/v1
kind: Deployment
metadata:
  name: r2-metrics-exporter
  namespace: monitoring
  labels:
    app: r2-metrics-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: r2-metrics-exporter
  template:
    metadata:
      labels:
        app: r2-metrics-exporter
        monitoring: enabled
    spec:
      containers:
      - name: exporter
        image: prom/cloudwatch-exporter:latest
        args:
          - '--config.file=/etc/r2-metrics/config.yml'
        ports:
          - containerPort: 9106
            name: metrics
        volumeMounts:
          - name: config
            mountPath: /etc/r2-metrics
        env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: r2-backup-secret
                key: AWS_ACCESS_KEY_ID
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: r2-backup-secret
                key: AWS_SECRET_ACCESS_KEY
          - name: AWS_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: r2-backup-secret
                key: AWS_ENDPOINT
      volumes:
      - name: config
        configMap:
          name: r2-metrics-config
---
apiVersion: v1
kind: Service
metadata:
  name: r2-metrics-exporter
  namespace: monitoring
  labels:
    app: r2-metrics-exporter
spec:
  ports:
    - port: 9106
      name: metrics
  selector:
    app: r2-metrics-exporter
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: r2-metrics
  namespace: monitoring
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: r2-metrics-exporter
  endpoints:
    - port: metrics
      interval: 5m