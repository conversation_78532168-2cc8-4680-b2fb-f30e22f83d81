cephClusterSpec:
  mon:
    count: 3
  mgr:
    count: 2
  
  storage:
    useAllNodes: true
    useAllDevices: false  # Critical: prevent automatic device discovery
    config:
      # Enable encryption
      encryptedDevice: "true"
      # One OSD per device for better performance
      osdsPerDevice: "1"
      # Optimize for NVMe
      osdMaxBackfills: "2"
      osdRecoveryMaxActive: "2"
      osdRecoveryOpPriority: "2"
      # Database specific tuning
      osdMemoryTarget: "4294967296"  # 4GB for OSD memory caching
      osdObjectStore: "bluestore"
      bluestore_block_db_size: "107374182400"  # 100GB for BlueStore DB
      bluestore_block_wal_size: "107374182400"  # 100GB for BlueStore WAL
      # Add tuning for modern NVMe drives
      osdMaxPGPerOSD: "600"
      bluefs_buffered_io: "true"
      bluestore_default_buffered_read: "true"
      # Add crash recovery settings
      osd_recovery_max_active_hdd: "3"
      osd_recovery_max_active_ssd: "10"
      osd_recovery_sleep: "0.1"
    # Explicitly specify only the 6.4TB NVMe drives
    devices:
      - name: "nvme2n1"  # 6.4TB NVMe for Ceph
      - name: "nvme3n1"  # 6.4TB NVMe for Ceph
    # Add filters to ensure RAID devices are never touched
    filter:
      devicePathFilter:
        - "!nvme[0-1]n[0-9]"  # Exclude nvme0n1 and nvme1n1
        - "!md[0-9]"          # Exclude any MD RAID devices

  # Resource configuration for OSDs
  resources:
    osd:
      limits:
        cpu: "4"
        memory: "8Gi"
      requests:
        cpu: "2"
        memory: "4Gi"

  # Network configuration
  network:
    provider: host
    connections:
      compression:
        enabled: true

  encryption:
    enable: true
    kms:
      connectionDetails:
        KMS_PROVIDER: "aws-kms"
        KMS_REGION: "{{ aws_kms.region }}"
        ACCESS_KEY_ID: "{{ aws_kms.access_key }}"
        SECRET_ACCESS_KEY: "{{ aws_kms.secret_key }}"
      tokenSecretName: aws-kms-credentials

storageClass:
  enabled: true
  isDefault: true
  reclaimPolicy: Delete
  allowVolumeExpansion: true
  volumeBindingMode: WaitForFirstConsumer

cephBlockPools:
  - name: replicapool
    spec:
      failureDomain: host
      replicated:
        size: 3
        # Optimize for database workloads
        requireSafeReplicaSize: true
        compression_mode: "none"  # Disable compression for databases
      parameters:
        # Optimize scrubbing for production
        scrub_max_interval: "1209600"  # 2 weeks
        scrub_begin_hour: "1"   # Start at 1 AM
        scrub_end_hour: "5"     # End at 5 AM
        scrub_sleep: "0.1"      # Reduce impact
        scrub_chunk_max: "16"   # Smaller chunks
    storageClass:
      enabled: true
      name: ceph-block
      isDefault: true
      parameters:
        # RBD configuration for databases
        imageFeatures: layering,deep-flatten,exclusive-lock,fast-diff,object-map
        # Tune QoS
        rbd_qos_iops_limit: "5000"  # Limit IOPS per volume
        rbd_qos_bps_limit: "500M"   # Limit bandwidth per volume
        # CSI parameters
        csi.storage.k8s.io/fstype: "xfs"  # XFS for better performance
        csi.storage.k8s.io/provisioner-secret-name: rook-csi-rbd-provisioner
        csi.storage.k8s.io/controller-expand-secret-name: rook-csi-rbd-provisioner
        csi.storage.k8s.io/node-stage-secret-name: rook-csi-rbd-node
        encrypted: "true"
        # Volume expansion
        allowVolumeExpansion: "true"
        # Mounting options
        mountOptions: "noatime,nodiratime,discard"

snapshotClass:
  enabled: true
  name: ceph-snapshot
  parameters:
    clusterID: rook-ceph
    csi.storage.k8s.io/snapshotter-secret-name: rook-csi-rbd-provisioner
    csi.storage.k8s.io/snapshotter-secret-namespace: rook-ceph

# Dashboard configuration
dashboard:
  enabled: true
  ssl: false
