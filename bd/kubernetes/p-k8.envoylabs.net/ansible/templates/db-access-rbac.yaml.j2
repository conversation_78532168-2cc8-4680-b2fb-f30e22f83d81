apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: db-credential-reader
  namespace: crx-db
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames: ["*-superuser"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get"]
  resourceNames: ["*-rw-pooler", "*-ro-pooler"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: db-credential-reader-binding
  namespace: crx-db
subjects:
- kind: Group
  name: db-admins
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: db-credential-reader
  apiGroup: rbac.authorization.k8s.io