apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: backup-credential-reader
  namespace: crx-db
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames: ["r2-backup-secret-*", "etcd-backup-secret"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: backup-credential-reader-binding
  namespace: crx-db
subjects:
- kind: Group
  name: backup-operators
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: backup-credential-reader
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: backup-operator
rules:
- apiGroups: ["postgresql.cnpg.io"]
  resources: ["backups"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames: ["etcd-backup-secret"]
  namespaces: ["kube-system"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: backup-operator-binding
subjects:
- kind: Group
  name: backup-operators
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: ClusterRole
  name: backup-operator
  apiGroup: rbac.authorization.k8s.io