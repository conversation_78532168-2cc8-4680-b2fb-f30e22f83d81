#!/bin/bash
set -eo pipefail

if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup-name>"
    exit 1
fi

BACKUP_NAME=$1
BACKUP_PATH="/var/lib/etcd/backup"
TEMP_BACKUP="${BACKUP_PATH}/temp_decrypted.db"

# AWS KMS configuration
export AWS_DEFAULT_REGION="{{ aws_kms.region }}"
export AWS_ACCESS_KEY_ID="{{ aws_kms.access_key }}"
export AWS_SECRET_ACCESS_KEY="{{ aws_kms.secret_key }}"

# Download encrypted backup
aws s3 cp \
    "s3://${BUCKET}/etcd-backup/${BACKUP_NAME}" \
    "${BACKUP_PATH}/${BACKUP_NAME}" \
    --endpoint-url ${R2_ENDPOINT}

# Decrypt backup using AWS KMS
aws kms decrypt \
    --ciphertext-blob fileb://${BAC<PERSON>UP_PATH}/${BACKUP_NAME} \
    --output text \
    --query Plaintext \
    > "${TEMP_BACKUP}"

# Verify backup
if ! ETCDCTL_API=3 etcdctl snapshot status "${TEMP_BACKUP}"; then
    echo "Backup verification failed"
    rm -f "${TEMP_BACKUP}"
    exit 1
fi

# Stop etcd and other control plane components
systemctl stop kubelet kube-apiserver kube-controller-manager kube-scheduler etcd

# Restore backup
ETCDCTL_API=3 etcdctl snapshot restore "${TEMP_BACKUP}" \
    --data-dir /var/lib/etcd/new \
    --name {{ cluster_name }} \
    --initial-cluster {{ etcd_initial_cluster }} \
    --initial-cluster-token {{ etcd_token }} \
    --initial-advertise-peer-urls https://{{ ansible_host }}:2380

# Update etcd data directory
mv /var/lib/etcd/member /var/lib/etcd/member.bak
mv /var/lib/etcd/new/* /var/lib/etcd/
rmdir /var/lib/etcd/new

# Start services
systemctl start etcd
systemctl start kubelet kube-apiserver kube-controller-manager kube-scheduler

# Cleanup
rm -f "${TEMP_BACKUP}"