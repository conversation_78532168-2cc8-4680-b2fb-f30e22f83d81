apiVersion: kubeadm.k8s.io/v1beta3
kind: ClusterConfiguration
kubernetesVersion: v{{ kubernetes_version }}
controlPlaneEndpoint: "{{ load_balancer.vip }}:{{ load_balancer.port }}"
networking:
  podSubnet: "10.0.0.0/16"
  serviceSubnet: "*********/12"
apiServer:
  extraArgs:
    encryption-provider-config: /etc/kubernetes/encryption-config.yaml
etcd:
  local:
    extraArgs:
      cipher-suites: TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
    serverCertSANs:
      - "{{ load_balancer.vip }}"
    peerCertSANs:
      - "{{ load_balancer.vip }}"
