prometheus:
  prometheusSpec:
    retention: 15d
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: ceph-block
          resources:
            requests:
              storage: 50Gi
    
    serviceMonitorSelector:
      matchLabels:
        monitoring: enabled
    
    podMonitorSelector:
      matchLabels:
        monitoring: enabled

grafana:
  persistence:
    enabled: true
    storageClassName: ceph-block
    size: 10Gi
  
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: default
          orgId: 1
          folder: 'PostgreSQL'
          type: file
          disableDeletion: true
          editable: false
          options:
            path: /var/lib/grafana/dashboards

  dashboards:
    default:
      postgresql-overview:
        url: https://raw.githubusercontent.com/cloudnative-pg/cloudnative-pg/main/docs/src/samples/monitoring/grafana-dashboard.json
        datasource: Prometheus

  adminPassword: "{{ grafana_admin_password }}"

  service:
    type: NodePort
    nodePort: 30900

alertmanager:
  enabled: true
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: ceph-block
          resources:
            requests:
              storage: 10Gi