# {{ client_name }} Database Access Configuration

frontend {{ client_name }}_rw
    bind *:{{ rw_port }}
    mode tcp
    option tcplog
    default_backend {{ client_name }}_rw_backend

frontend {{ client_name }}_ro
    bind *:{{ ro_port }}
    mode tcp
    option tcplog
    default_backend {{ client_name }}_ro_backend

backend {{ client_name }}_rw_backend
    mode tcp
    option tcp-check
    balance roundrobin
    {% for ip in worker_ips %}
    server worker{{ loop.index }} {{ ip }}:{{ rw_port }} check
    {% endfor %}

backend {{ client_name }}_ro_backend
    mode tcp
    option tcp-check
    balance roundrobin
    {% for ip in worker_ips %}
    server worker{{ loop.index }} {{ ip }}:{{ ro_port }} check
    {% endfor %}
