apiVersion: v1
kind: ConfigMap
metadata:
  name: r2-metrics-config
  namespace: monitoring
data:
  config.yml: |
    region: auto
    metrics:
      - aws_namespace: "R2"
        aws_metric_name: "BucketSizeBytes"
        aws_dimensions: ["BucketName", "StorageType"]
        aws_statistics: ["Average"]
      - aws_namespace: "R2"
        aws_metric_name: "NumberOfObjects"
        aws_dimensions: ["BucketName"]
        aws_statistics: ["Average"]
      - aws_namespace: "R2"
        aws_metric_name: "BytesUploaded"
        aws_dimensions: ["BucketName"]
        aws_statistics: ["Sum"]
      - aws_namespace: "R2"
        aws_metric_name: "BytesDownloaded"
        aws_dimensions: ["BucketName"]
        aws_statistics: ["Sum"]