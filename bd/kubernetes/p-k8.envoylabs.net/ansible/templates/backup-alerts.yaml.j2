apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: backup-alerts
  namespace: monitoring
spec:
  groups:
  - name: backup.rules
    rules:
    - alert: PostgreSQLBackupVerificationFailed
      expr: cnpg_backup_verification_status != 1
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: "PostgreSQL backup verification failed"
        description: "Backup verification failed for cluster {{ $labels.cluster }}"

    - alert: PostgreSQLBackupMissing
      expr: time() - cnpg_backup_last_success_time_seconds > 86400
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: "PostgreSQL backup is missing"
        description: "No successful backup in the last 24 hours for cluster {{ $labels.cluster }}"

    - alert: PostgreSQLBackupTooOld
      expr: time() - cnpg_backup_last_success_time_seconds > 43200
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "PostgreSQL backup is old"
        description: "Last successful backup is over 12 hours old for cluster {{ $labels.cluster }}"

    - alert: EtcdBackupMissing
      expr: time() - max(etcd_backup_latest_timestamp) > 86400
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: "etcd backup is missing"
        description: "No successful etcd backup in the last 24 hours"

    - alert: EtcdBackupFailed
      expr: etcd_backup_success != 1
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: "etcd backup failed"
        description: "The latest etcd backup attempt failed"
