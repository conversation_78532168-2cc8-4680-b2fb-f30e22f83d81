crds:
  enabled: true

monitoring:
  enabled: true

# Optimize operator resources
resources:
  requests:
    cpu: "1"
    memory: "2Gi"
  limits:
    cpu: "2"
    memory: "4Gi"

# CSI configuration
csi:
  enableRbdDriver: true
  enableCephfsDriver: false  # Disable CephFS as we only need RBD
  provisionerReplicas: 2
  pluginPriorityClassName: system-node-critical
  provisionerPriorityClassName: system-cluster-critical
  # Optimize CSI resources
  rbdPlugin:
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"
  provisioner:
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"
