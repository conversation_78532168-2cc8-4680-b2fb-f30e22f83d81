{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "text": "Failed"}, "1": {"color": "green", "text": "Success"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.3", "title": "Backup Verification Status", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 43200}, {"color": "red", "value": 86400}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "postgresql_backup_age_seconds", "refId": "A"}], "title": "Backup Age", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1800}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "postgresql_backup_verification_duration_seconds", "legendFormat": "{{cluster}}", "refId": "A"}], "title": "Verification Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "mappings", "value": [{"options": {"0": {"color": "red", "text": "Failed"}, "1": {"color": "green", "text": "Success"}}, "type": "value"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Age"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "id": 4, "options": {"footer": {"enablePagination": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "postgresql_backup_verification_status", "format": "table", "instant": true, "legendFormat": "Status", "refId": "Status"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "postgresql_backup_age_seconds", "format": "table", "instant": true, "legendFormat": "Age", "refId": "Age"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "postgresql_backup_verification_duration_seconds", "format": "table", "instant": true, "legendFormat": "Duration", "refId": "Duration"}], "title": "Backup Overview", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "instance": true, "job": true}, "indexByName": {}, "renameByName": {"Value #Age": "Age", "Value #Duration": "Duration", "Value #Status": "Status", "cluster": "Cluster"}}}], "type": "table"}], "refresh": "1m", "schemaVersion": 38, "style": "dark", "tags": ["postgresql", "backup"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "PostgreSQL Backup Monitoring", "uid": "postgresql-backup-monitoring", "version": 1, "weekStart": ""}