#!/bin/bash
set -e

# Optional client name for client-specific rotation
CLIENT_NAME="${1:-}"

# AWS KMS configuration
export AWS_DEFAULT_REGION="{{ aws_kms.region }}"
export AWS_ACCESS_KEY_ID="{{ aws_kms.access_key }}"
export AWS_SECRET_ACCESS_KEY="{{ aws_kms.secret_key }}"

# Generate new credentials
NEW_ACCESS_KEY=$(openssl rand -hex 16)
NEW_SECRET_KEY=$(openssl rand -base64 32)

# Encrypt new credentials with KMS
ENCRYPTED_ACCESS_KEY=$(aws kms encrypt \
  --key-id {{ aws_kms.key_id }} \
  --plaintext "$NEW_ACCESS_KEY" \
  --output text --query CiphertextBlob)

ENCRYPTED_SECRET_KEY=$(aws kms encrypt \
  --key-id {{ aws_kms.key_id }} \
  --plaintext "$NEW_SECRET_KEY" \
  --output text --query CiphertextBlob)

if [ -n "$CLIENT_NAME" ]; then
    # Client-specific rotation
    echo "Rotating credentials for client: $CLIENT_NAME"
    
    # Update client-specific Kubernetes secret
    kubectl create secret generic r2-backup-secret-${CLIENT_NAME} \
      --namespace crx-db \
      --from-literal=AWS_ACCESS_KEY_ID="$NEW_ACCESS_KEY" \
      --from-literal=AWS_SECRET_ACCESS_KEY="$NEW_SECRET_KEY" \
      --from-literal=AWS_ENDPOINT="{{ cloudflare_r2.endpoint }}" \
      -o yaml --dry-run=client | kubectl replace -f -

    # Update specific CNPG cluster to use new credentials
    kubectl annotate cluster/${CLIENT_NAME} \
      -n crx-db \
      cnpg.io/reload-credentials="$(date +%s)" \
      --overwrite
else
    # Default etcd backup credentials rotation
    echo "Rotating etcd backup credentials"
    
    # Update etcd backup secret
    kubectl create secret generic etcd-backup-secret \
      --namespace kube-system \
      --from-literal=AWS_ACCESS_KEY_ID="$NEW_ACCESS_KEY" \
      --from-literal=AWS_SECRET_ACCESS_KEY="$NEW_SECRET_KEY" \
      --from-literal=AWS_ENDPOINT="{{ cloudflare_r2.endpoint }}" \
      -o yaml --dry-run=client | kubectl replace -f -
fi

# Update R2 with new credentials (requires cloudflare API token)
if [ -n "$CF_API_TOKEN" ]; then
  curl -X PUT "https://api.cloudflare.com/client/v4/accounts/$CF_ACCOUNT_ID/r2/keys" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "{\"access_key\": \"$NEW_ACCESS_KEY\", \"secret_key\": \"$NEW_SECRET_KEY\"}"
fi

# Store encrypted credentials in AWS SSM for backup
aws ssm put-parameter \
  --name "/r2/backup-credentials/access-key" \
  --value "$ENCRYPTED_ACCESS_KEY" \
  --type SecureString \
  --overwrite

aws ssm put-parameter \
  --name "/r2/backup-credentials/secret-key" \
  --value "$ENCRYPTED_SECRET_KEY" \
  --type SecureString \
  --overwrite

# Log rotation event
logger -t r2-credentials-rotation "R2 credentials rotated successfully"
