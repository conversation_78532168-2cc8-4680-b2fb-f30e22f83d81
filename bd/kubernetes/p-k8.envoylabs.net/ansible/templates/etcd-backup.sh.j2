#!/bin/bash
set -eo pipefail

BACKUP_PATH="/var/lib/etcd/backup"
BACKUP_NAME="etcd-snapshot-$(date +%Y%m%d-%H%M%S).db"
ENCRYPTED_BACKUP_NAME="${BACKUP_NAME}.encrypted"
METRICS_FILE="/var/lib/node_exporter/etcd_backup_metrics.prom"

# AWS KMS configuration
export AWS_DEFAULT_REGION="{{ aws_kms.region }}"
export AWS_ACCESS_KEY_ID="{{ aws_kms.access_key }}"
export AWS_SECRET_ACCESS_KEY="{{ aws_kms.secret_key }}"

# Ensure backup directory exists
mkdir -p "${BACKUP_PATH}"

backup_start_time=$(date +%s)
backup_status=0

# Take snapshot
if ETCDCTL_API=3 etcdctl snapshot save "${BACKUP_PATH}/${BACKUP_NAME}" \
  --endpoints=https://127.0.0.1:2379 \
  --cacert=/etc/kubernetes/pki/etcd/ca.crt \
  --cert=/etc/kubernetes/pki/etcd/server.crt \
  --key=/etc/kubernetes/pki/etcd/server.key; then

    # Verify backup
    if ETCDCTL_API=3 etcdctl snapshot status "${BACKUP_PATH}/${BACKUP_NAME}" > /dev/null; then
        # Encrypt backup using AWS KMS
        if aws kms encrypt \
            --key-id {{ aws_kms.key_id }} \
            --plaintext fileb://${BACKUP_PATH}/${BACKUP_NAME} \
            --output text \
            --query CiphertextBlob \
            > "${BACKUP_PATH}/${ENCRYPTED_BACKUP_NAME}"; then

            # Upload encrypted backup to R2
            if aws s3 cp "${BACKUP_PATH}/${ENCRYPTED_BACKUP_NAME}" \
                "s3://${BUCKET}/etcd-backup/${ENCRYPTED_BACKUP_NAME}" \
                --endpoint-url ${R2_ENDPOINT}; then
                backup_status=1
            fi
        fi
    fi
fi

backup_duration=$(($(date +%s) - backup_start_time))

# Update Prometheus metrics
cat > "${METRICS_FILE}" << EOF
# HELP etcd_backup_success Whether the backup was successful (1 for success, 0 for failure)
# TYPE etcd_backup_success gauge
etcd_backup_success ${backup_status}

# HELP etcd_backup_duration_seconds Time taken to complete backup
# TYPE etcd_backup_duration_seconds gauge
etcd_backup_duration_seconds ${backup_duration}

# HELP etcd_backup_latest_timestamp Unix timestamp of the latest backup
# TYPE etcd_backup_latest_timestamp gauge
etcd_backup_latest_timestamp $(date +%s)

# HELP etcd_backup_size_bytes Size of the backup in bytes
# TYPE etcd_backup_size_bytes gauge
etcd_backup_size_bytes $(stat -f %z "${BACKUP_PATH}/${ENCRYPTED_BACKUP_NAME}" 2>/dev/null || echo 0)
EOF

# Cleanup old backups (keep last 7 days)
find "${BACKUP_PATH}" -type f -mtime +7 -delete

exit $((1 - backup_status))
