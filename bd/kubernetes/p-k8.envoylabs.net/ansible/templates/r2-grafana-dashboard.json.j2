{"annotations": {"list": []}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 70}, {"color": "red", "value": 85}]}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "title": "R2 Storage Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "title": "Number of Objects", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "spanNulls": false}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "title": "Backup Size Over Time", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 38, "style": "dark", "tags": ["r2", "backups"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "R2 Backup Metrics", "version": 1, "weekStart": ""}