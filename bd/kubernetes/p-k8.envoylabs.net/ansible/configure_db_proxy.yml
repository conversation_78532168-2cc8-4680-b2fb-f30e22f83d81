---
- name: Configure HAProxy for database access
  hosts: loadbalancer
  become: true
  vars_prompt:
    - name: client_name
      prompt: "Enter client name"
      private: no

  tasks:
    - name: Get worker node IPs
      set_fact:
        worker_ips: "{{ groups['workers'] | map('extract', hostvars, ['ansible_host']) | list }}"
      run_once: true

    - name: Get database ports from Kubernetes
      delegate_to: "{{ groups['control_plane'][0] }}"
      command: >
        kubectl get service -n crx-db 
        {{ client_name }}-rw-pooler,{{ client_name }}-ro-pooler 
        -o jsonpath='{.items[*].spec.ports[0].nodePort}'
      register: db_ports
      run_once: true

    - name: Set port facts
      set_fact:
        rw_port: "{{ db_ports.stdout.split()[0] }}"
        ro_port: "{{ db_ports.stdout.split()[1] }}"

    - name: Create HAProxy configuration for client
      template:
        src: templates/haproxy/db-client.cfg.j2
        dest: /etc/haproxy/conf.d/{{ client_name }}.cfg
      notify: reload haproxy

    - name: Ensure conf.d is included in main config
      lineinfile:
        path: /etc/haproxy/haproxy.cfg
        line: "include conf.d/*.cfg"
        insertafter: "^global"
        state: present

  handlers:
    - name: reload haproxy
      service:
        name: haproxy
        state: reloaded