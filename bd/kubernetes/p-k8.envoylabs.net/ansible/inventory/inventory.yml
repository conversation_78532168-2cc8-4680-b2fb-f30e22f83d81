all:
  children:
    control_plane:
      hosts:
        cp1:
          ansible_host: ************
        cp2:
          ansible_host: ************
        cp3:
          ansible_host: ************
    workers:
      hosts:
        worker1:
          ansible_host: ************
        worker2:
          ansible_host: ************
        worker3:
          ansible_host: ************
    loadbalancer:
      hosts:
        lb:
          ansible_host: ************
  vars:
    ansible_user: root
    ansible_become: true
    ansible_python_interpreter: /usr/bin/python3