---
- name: Remove HAProxy configuration for database client
  hosts: loadbalancer
  become: true
  vars_prompt:
    - name: client_name
      prompt: "Enter client name to remove"
      private: no

  tasks:
    - name: Check if client config exists
      stat:
        path: "/etc/haproxy/conf.d/{{ client_name }}.cfg"
      register: config_file

    - name: Fail if client config doesn't exist
      fail:
        msg: "No HAProxy configuration found for client '{{ client_name }}'"
      when: not config_file.stat.exists

    - name: Remove client HAProxy configuration
      file:
        path: "/etc/haproxy/conf.d/{{ client_name }}.cfg"
        state: absent
      notify: reload haproxy
      when: config_file.stat.exists

    - name: Check if conf.d is empty
      find:
        paths: /etc/haproxy/conf.d
        patterns: "*.cfg"
      register: remaining_configs

    - name: Remove conf.d include if no more clients
      lineinfile:
        path: /etc/haproxy/haproxy.cfg
        line: "include conf.d/*.cfg"
        state: absent
      notify: reload haproxy
      when: remaining_configs.matched == 0

  handlers:
    - name: reload haproxy
      service:
        name: haproxy
        state: reloaded