---
- name: Deploy Kubernetes Cluster
  hosts: all
  become: true
  tasks:
    - name: Install common packages
      apt:
        name:
          - apt-transport-https
          - ca-certificates
          - curl
          - software-properties-common
        state: present
        update_cache: yes

- name: Configure Load Balancer
  hosts: loadbalancer
  become: true
  tasks:
    - name: Install and configure HAProxy
      include_tasks: tasks/haproxy.yml

- name: Setup Kubernetes Control Plane
  hosts: control_plane
  become: true
  tasks:
    - name: Setup control plane nodes
      include_tasks: tasks/k8s_common.yml

    - name: Initialize Kubernetes cluster
      include_tasks: tasks/k8s_control_plane.yml

    - name: Install CNI (First control plane node only)
      include_tasks: tasks/setup_cni.yml
      when: inventory_hostname == groups['control_plane'][0]

    - name: Configure network policies (First control plane node only)
      include_tasks: tasks/setup_network_policies.yml
      when: inventory_hostname == groups['control_plane'][0]

    - name: Setup etcd backups
      include_tasks: tasks/setup_etcd_backup.yml
      when: inventory_hostname == groups['control_plane'][0]

    - name: Setup Rook-Ceph storage
      include_tasks: tasks/setup_rook_ceph.yml
      when: inventory_hostname == groups['control_plane'][0]

    - name: Setup CloudNativePG
      include_tasks: tasks/setup_cloudnativepg.yml
      when: inventory_hostname == groups['control_plane'][0]

    - name: Setup Monitoring
      include_tasks: tasks/setup_monitoring.yml
      when: inventory_hostname == groups['control_plane'][0]

    - name: Setup backup verification
      include_tasks: tasks/setup_backup_verification.yml
      when: inventory_hostname == groups['control_plane'][0]

- name: Setup Kubernetes Workers
  hosts: workers
  become: true
  tasks:
    - name: Setup worker nodes
      include_tasks: tasks/k8s_common.yml

    - name: Join workers to cluster
      include_tasks: tasks/k8s_workers.yml

- name: Verify Cluster
  hosts: control_plane[0]
  become: true
  tasks:
    - name: Verify cluster setup
      include_tasks: tasks/verify_cluster.yml
