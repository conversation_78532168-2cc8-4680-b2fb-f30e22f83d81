---
- name: Configure hostname
  block:
    - name: Set hostname
      hostname:
        name: "{{ inventory_hostname }}"
    
    - name: Update /etc/hosts
      lineinfile:
        path: /etc/hosts
        regexp: "^*********"
        line: "********* {{ inventory_hostname }}"
        state: present

- name: Verify load balancer
  wait_for:
    host: "{{ load_balancer.vip }}"
    port: "{{ load_balancer.port }}"
    timeout: 300
  delegate_to: localhost

- name: Initialize first control plane node
  when: inventory_hostname == groups['control_plane'][0]
  block:
    - name: Generate kubeadm config
      template:
        src: templates/kubeadm-config.yaml.j2
        dest: /tmp/kubeadm-config.yaml

    - name: Initialize cluster
      command: kubeadm init --config /tmp/kubeadm-config.yaml --upload-certs
      register: kubeadm_init

    - name: Store certificate key
      command: kubeadm init phase upload-certs --upload-certs
      register: cert_key

    - name: Save join command
      command: kubeadm token create --print-join-command
      register: join_command

    - name: Set facts for other nodes
      set_fact:
        certificate_key: "{{ cert_key.stdout_lines[-1] }}"
        join_command: "{{ join_command.stdout }}"
      delegate_to: localhost
      delegate_facts: true

- name: Join additional control plane nodes
  when: inventory_hostname != groups['control_plane'][0]
  command: >
    {{ hostvars['localhost']['join_command'] }}
    --control-plane
    --certificate-key {{ hostvars['localhost']['certificate_key'] }}
