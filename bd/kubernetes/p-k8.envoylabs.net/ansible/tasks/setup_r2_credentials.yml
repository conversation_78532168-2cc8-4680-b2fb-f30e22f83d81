---
- name: <PERSON>ida<PERSON> required variables
  assert:
    that:
      - client_name is defined
    fail_msg: "client_name variable is required"

- name: C<PERSON> <PERSON> encrypted R2 credentials secret
  command: >
    k<PERSON><PERSON><PERSON> create secret generic r2-backup-secret-{{ client_name }}
    --namespace crx-db
    --from-literal=AWS_ACCESS_KEY_ID={{ cloudflare_r2.access_key }}
    --from-literal=AWS_SECRET_ACCESS_KEY={{ cloudflare_r2.secret_key }}
    --from-literal=AWS_ENDPOINT={{ cloudflare_r2.endpoint }}
  args:
    # Only create if it doesn't exist
    creates: /tmp/.r2-secret-created-{{ client_name }}

- name: Create credential rotation script
  template:
    src: templates/rotate-r2-credentials.sh.j2
    dest: /usr/local/bin/rotate-r2-credentials
    mode: '0755'

- name: Setup etcd backup credential rotation cronjob
  cron:
    name: "R2 credential rotation - etcd backup"
    minute: "0"
    hour: "*/6"
    job: "/usr/local/bin/rotate-r2-credentials"

- name: Setup client-specific credential rotation cronjob (if client_name is provided)
  cron:
    name: "R2 credential rotation - {{ client_name }}"
    minute: "0"
    hour: "*/6"
    job: "/usr/local/bin/rotate-r2-credentials {{ client_name }}"
  when: client_name is defined
