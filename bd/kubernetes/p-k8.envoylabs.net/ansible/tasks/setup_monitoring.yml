---
- name: Add Prometheus community Helm repo
  command: helm repo add prometheus-community https://prometheus-community.github.io/helm-charts

- name: Update Helm repositories
  command: helm repo update

- name: Create monitoring namespace
  command: kubectl create namespace monitoring
  ignore_errors: yes

- name: Create backup monitoring ConfigMap
  command: >
    kubectl create configmap backup-dashboard
    --from-file=backup-dashboard.json=/tmp/grafana-backup-dashboard.json
    -n monitoring
  ignore_errors: yes

- name: Deploy Prometheus Operator stack
  command: >
    helm install prometheus prometheus-community/kube-prometheus-stack
    --namespace monitoring
    --version {{ prometheus_stack_version }}
    --values /tmp/prometheus-values.yaml
  register: prometheus_install
  until: prometheus_install.rc == 0
  retries: 3
  delay: 10

- name: Wait for Prometheus deployment
  command: >
    kubectl rollout status deployment/prometheus-kube-prometheus-operator
    -n monitoring
    --timeout=300s

- name: Apply ServiceMonitor for backup metrics
  template:
    src: templates/backup-servicemonitor.yaml.j2
    dest: /tmp/backup-servicemonitor.yaml

- name: Apply backup ServiceMonitor
  command: kubectl apply -f /tmp/backup-servicemonitor.yaml

- name: Apply backup alert rules
  template:
    src: templates/backup-alerts.yaml.j2
    dest: /tmp/backup-alerts.yaml

- name: Apply backup alerts
  command: kubectl apply -f /tmp/backup-alerts.yaml

- name: Wait for Grafana deployment
  command: >
    kubectl rollout status deployment/prometheus-grafana
    -n monitoring
    --timeout=300s
