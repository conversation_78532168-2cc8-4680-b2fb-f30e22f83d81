---
- name: Add Rook-Ceph repository
  command: helm repo add rook-release https://charts.rook.io/release

- name: Update Helm repositories
  command: helm repo update

- name: Create rook-ceph namespace
  command: kube<PERSON>l create namespace rook-ceph
  ignore_errors: yes

- name: Deploy Rook-Ceph operator
  command: >
    helm install rook-ceph rook-release/rook-ceph
    --namespace rook-ceph
    --version {{ rook_ceph_version }}
    --values /tmp/rook-operator-values.yaml
  register: operator_install
  until: operator_install.rc == 0
  retries: 3
  delay: 10

- name: Wait for operator deployment
  command: >
    kubectl rollout status deployment/rook-ceph-operator 
    -n rook-ceph 
    --timeout=300s

- name: Deploy Ceph cluster
  command: >
    helm install rook-ceph-cluster rook-release/rook-ceph-cluster
    --namespace rook-ceph
    --values /tmp/rook-cluster-values.yaml
  register: cluster_install
  until: cluster_install.rc == 0
  retries: 3
  delay: 10

- name: Wait for Ceph cluster health
  command: kubectl -n rook-ceph wait --for=condition=Ready cephcluster/rook-ceph
  register: ceph_ready
  until: ceph_ready.rc == 0
  retries: 30
  delay: 10