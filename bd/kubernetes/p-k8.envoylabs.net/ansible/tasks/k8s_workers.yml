---
- name: Configure hostname
  block:
    - name: Set hostname
      hostname:
        name: "{{ inventory_hostname }}"
    
    - name: Update /etc/hosts
      lineinfile:
        path: /etc/hosts
        regexp: "^*********"
        line: "********* {{ inventory_hostname }}"
        state: present

- name: Install required packages
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common
    state: present
    update_cache: yes

- name: Add Kubernetes apt key
  apt_key:
    url: https://packages.cloud.google.com/apt/doc/apt-key.gpg
    state: present

- name: Add Kubernetes repository
  apt_repository:
    repo: "deb https://apt.kubernetes.io/ kubernetes-{{ ansible_distribution_release }} main"
    state: present
    filename: kubernetes

- name: Install Kubernetes packages
  apt:
    name:
      - kubelet
      - kubeadm
      - kubectl
    state: present
    update_cache: yes

- name: Configure containerd
  block:
    - name: Create containerd config directory
      file:
        path: /etc/containerd
        state: directory

    - name: Configure containerd
      copy:
        dest: /etc/containerd/config.toml
        content: |
          version = 2
          [plugins."io.containerd.grpc.v1.cri"]
            systemd_cgroup = true

    - name: Restart containerd
      service:
        name: containerd
        state: restarted

- name: Configure kernel modules
  block:
    - name: Load br_netfilter module
      modprobe:
        name: br_netfilter
        state: present

    - name: Load overlay module
      modprobe:
        name: overlay
        state: present

    - name: Configure sysctl parameters
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        sysctl_file: /etc/sysctl.d/k8s.conf
      with_items:
        - { name: 'net.bridge.bridge-nf-call-iptables', value: '1' }
        - { name: 'net.bridge.bridge-nf-call-ip6tables', value: '1' }
        - { name: 'net.ipv4.ip_forward', value: '1' }

- name: Wait for HAProxy to be available
  wait_for:
    host: "{{ load_balancer.vip }}"
    port: "{{ load_balancer.port }}"
    timeout: 300
  delegate_to: localhost

- name: Get join command from first control plane node
  command: kubeadm token create --print-join-command
  register: join_command
  delegate_to: "{{ groups['control_plane'][0] }}"
  run_once: true

- name: Join worker to cluster
  command: "{{ hostvars['localhost']['join_command'] }}"
  args:
    creates: /etc/kubernetes/kubelet.conf

- name: Label worker node
  command: kubectl label node {{ inventory_hostname }} node-role.kubernetes.io/worker=''
  delegate_to: "{{ groups['control_plane'][0] }}"
  when: not ansible_check_mode

- name: Verify worker node status
  command: kubectl get node {{ inventory_hostname }}
  register: node_status
  until: "'Ready' in node_status.stdout"
  retries: 30
  delay: 10
  delegate_to: "{{ groups['control_plane'][0] }}"
  changed_when: false

- name: Prepare NVMe drives for Rook-Ceph
  block:
    - name: Identify Ceph NVMe drives
      shell: ls -1 /dev/nvme[2-3]n1 || true
      register: ceph_drives
      changed_when: false

    - name: Wipe filesystem signatures from Ceph drives
      command: wipefs -af {{ item }}
      with_items: "{{ ceph_drives.stdout_lines }}"
      when: ceph_drives.stdout_lines | length > 0

    - name: Clear partition tables from Ceph drives
      command: sgdisk --zap-all {{ item }}
      with_items: "{{ ceph_drives.stdout_lines }}"
      when: ceph_drives.stdout_lines | length > 0

    - name: Clear device mapper entries
      shell: |
        ls /dev/mapper/ceph-* 2>/dev/null | xargs -I% -- dmsetup remove % || true
      changed_when: false

    - name: Trigger kernel to rescan devices
      shell: |
        partprobe
        udevadm settle
      changed_when: false
