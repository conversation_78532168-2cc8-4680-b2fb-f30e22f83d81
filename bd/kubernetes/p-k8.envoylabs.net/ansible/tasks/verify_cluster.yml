---
- name: Verify cluster health
  block:
    - name: Check node status
      command: kubectl get nodes
      register: node_status
      failed_when: "'NotReady' in node_status.stdout"

    - name: Verify storage cluster health
      command: kubectl -n rook-ceph get cephcluster -o jsonpath='{.items[0].status.ceph.health}'
      register: ceph_health
      failed_when: ceph_health.stdout != 'HEALTH_OK'

    - name: Check database operator status
      command: kubectl -n crx-db get pods -l app.kubernetes.io/name=cloudnative-pg
      register: db_operator
      failed_when: "'Running' not in db_operator.stdout"

    - name: Verify monitoring stack
      command: kubectl -n monitoring get pods
      register: monitoring_status
      failed_when: "'Running' not in monitoring_status.stdout"

    - name: Check backup system
      block:
        - name: Check if any backups exist
          command: kubectl get backup -n crx-db
          register: backup_exists
          failed_when: false
          changed_when: false

        - name: Get latest backup status
          command: kubectl get backup -n crx-db --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[-1].status.phase}'
          register: backup_status
          changed_when: false
          when: backup_exists.rc == 0 and backup_exists.stdout != ""

        - name: Check CloudNativePG metrics
          command: >
            kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=cnpg_backup_verification_status
          register: backup_metrics
          changed_when: false

        - name: Fail if backup system is unhealthy
          fail:
            msg: "Backup system health check failed"
          when: >
            (backup_exists.rc == 0 and backup_exists.stdout != "" and backup_status.stdout != 'completed') or
            '"value":[' not in backup_metrics.stdout or
            '"1"' not in backup_metrics.stdout

      delegate_to: "{{ groups['control_plane'][0] }}"
      changed_when: false

  delegate_to: "{{ groups['control_plane'][0] }}"
  changed_when: false

- name: Verify Cilium connectivity
  command: cilium connectivity test
  register: cilium_connectivity
  delegate_to: "{{ groups['control_plane'][0] }}"
  changed_when: false

- name: Show cluster status
  command: kubectl get nodes -o wide
  register: cluster_status
  delegate_to: "{{ groups['control_plane'][0] }}"
  changed_when: false
