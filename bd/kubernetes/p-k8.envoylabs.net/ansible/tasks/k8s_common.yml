---
- name: Install common utilities
  apt:
    name:
      - htop
    state: present
    update_cache: yes

- name: Add Kubernetes apt key
  apt_key:
    url: https://packages.cloud.google.com/apt/doc/apt-key.gpg
    state: present

- name: Add Kubernetes repository
  apt_repository:
    repo: "deb https://apt.kubernetes.io/ kubernetes-{{ ansible_distribution_release }} main"
    state: present
    filename: kubernetes

- name: Install Kubernetes packages
  apt:
    name:
      - kubelet
      - kubeadm
      - kubectl
    state: present
    update_cache: yes

- name: Hold Kubernetes packages
  dpkg_selections:
    name: "{{ item }}"
    selection: hold
  with_items:
    - kubelet
    - kubeadm
    - kubectl

- name: Configure containerd
  block:
    - name: Create containerd config directory
      file:
        path: /etc/containerd
        state: directory

    - name: Configure containerd
      copy:
        dest: /etc/containerd/config.toml
        content: |
          version = 2
          [plugins."io.containerd.grpc.v1.cri"]
            systemd_cgroup = true

    - name: Restart containerd
      service:
        name: containerd
        state: restarted

- name: Configure kernel modules and sysctl
  block:
    - name: Load required modules
      modprobe:
        name: "{{ item }}"
        state: present
      with_items:
        - br_netfilter
        - overlay

    - name: Configure sysctl parameters
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        sysctl_file: /etc/sysctl.d/k8s.conf
      with_items:
        - { name: 'net.bridge.bridge-nf-call-iptables', value: '1' }
        - { name: 'net.bridge.bridge-nf-call-ip6tables', value: '1' }
        - { name: 'net.ipv4.ip_forward', value: '1' }

- name: Configure NVMe drives
  include_tasks: configure_nvme.yml
  when: nvme_drives is defined
