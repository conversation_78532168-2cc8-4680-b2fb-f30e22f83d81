---
- name: Create etcd backup script
  template:
    src: templates/etcd-backup.sh.j2
    dest: /usr/local/bin/etcd-backup.sh
    mode: '0755'

- name: Create etcd backup service
  template:
    src: templates/etcd-backup.service.j2
    dest: /etc/systemd/system/etcd-backup.service
    mode: '0644'

- name: Create etcd backup timer
  template:
    src: templates/etcd-backup.timer.j2
    dest: /etc/systemd/system/etcd-backup.timer
    mode: '0644'

- name: Create metrics directory
  file:
    path: /var/lib/node_exporter
    state: directory
    mode: '0755'

- name: Reload systemd
  systemd:
    daemon_reload: yes

- name: Enable and start etcd backup timer
  systemd:
    name: etcd-backup.timer
    enabled: yes
    state: started

- name: Run initial backup
  command: /usr/local/bin/etcd-backup.sh
  register: initial_backup
  changed_when: false