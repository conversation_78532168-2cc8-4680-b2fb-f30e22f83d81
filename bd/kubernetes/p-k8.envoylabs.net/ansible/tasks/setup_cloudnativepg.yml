s---
- name: Add CloudNativePG Helm repository
  command: helm repo add cloudnative-pg https://cloudnative-pg.github.io/charts

- name: Update Helm repositories
  command: helm repo update

- name: Create crx-db namespace if not exists
  command: kube<PERSON>l create namespace crx-db
  when: namespace_check.rc != 0

- name: Setup R2 credentials
  include_tasks: tasks/setup_r2_credentials.yml

- name: Deploy CloudNativePG operator
  command: >
    helm install cnpg cloudnative-pg/cloudnative-pg
    --namespace crx-db
    --version {{ cloudnative_pg_version }}
    --values /tmp/cloudnativepg-values.yaml
  register: operator_install
  until: operator_install.rc == 0
  retries: 3
  delay: 10

- name: Create DB access RBAC configuration
  template:
    src: templates/db-access-rbac.yaml.j2
    dest: /tmp/db-access-rbac.yaml

- name: Create backup access RBAC configuration
  template:
    src: templates/backup-access-rbac.yaml.j2
    dest: /tmp/backup-access-rbac.yaml

- name: Apply RBAC configurations
  command: kubectl apply -f {{ item }}
  with_items:
    - /tmp/db-access-rbac.yaml
    - /tmp/backup-access-rbac.yaml
  register: rbac_result
  until: rbac_result.rc == 0
  retries: 3
  delay: 5
