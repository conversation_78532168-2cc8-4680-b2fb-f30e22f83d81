---
- name: Create R2 metrics configuration
  template:
    src: templates/r2-metrics-config.yaml.j2
    dest: /tmp/r2-metrics-config.yaml

- name: Apply R2 metrics configuration
  command: kubectl apply -f /tmp/r2-metrics-config.yaml

- name: Deploy R2 metrics exporter
  template:
    src: templates/r2-metrics-exporter.yaml.j2
    dest: /tmp/r2-metrics-exporter.yaml

- name: Apply R2 metrics exporter
  command: kubectl apply -f /tmp/r2-metrics-exporter.yaml

- name: Create R2 Grafana dashboard
  template:
    src: templates/r2-grafana-dashboard.json.j2
    dest: /tmp/r2-grafana-dashboard.json

- name: Import R2 dashboard to Grafana
  command: >
    kubectl create configmap r2-grafana-dashboard
    --from-file=r2-dashboard.json=/tmp/r2-grafana-dashboard.json
    -n monitoring
    --dry-run=client -o yaml |
    kubectl apply -f -

- name: Label R2 dashboard ConfigMap
  command: >
    kubectl label configmap r2-grafana-dashboard
    -n monitoring
    grafana_dashboard=true