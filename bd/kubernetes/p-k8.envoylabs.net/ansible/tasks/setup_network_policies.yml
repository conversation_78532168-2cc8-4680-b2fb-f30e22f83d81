---
- name: Create crx-db namespace
  command: kube<PERSON>l create namespace crx-db
  ignore_errors: yes
  changed_when: false

- name: Create network policies directory
  file:
    path: /tmp/network-policies
    state: directory

- name: Copy network policy templates
  template:
    src: "templates/network-policies/{{ item }}.yaml.j2"
    dest: "/tmp/network-policies/{{ item }}.yaml"
  with_items:
    - default-deny
    - allow-system-namespaces
    - allow-monitoring

- name: Apply network policies
  command: kubectl apply -f /tmp/network-policies/{{ item }}.yaml
  with_items:
    - default-deny
    - allow-system-namespaces
    - allow-monitoring
  register: policy_result
  until: policy_result.rc == 0
  retries: 3
  delay: 5

- name: Verify network policies
  command: kubectl get cnp -n crx-db
  register: cnp_status
  changed_when: false

- name: Wait for policies to be ready
  command: >
    kubectl wait --for=condition=ready 
    cnp -n crx-db 
    --timeout=60s
  register: policy_ready
  retries: 5
  delay: 10
  until: policy_ready.rc == 0