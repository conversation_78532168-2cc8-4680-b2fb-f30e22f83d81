---
- name: Install Helm
  block:
    - name: Download and install He<PERSON>
      get_url:
        url: https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
        dest: /tmp/get_helm.sh
        mode: '0700'
    - name: Execute Helm installer
      command: /tmp/get_helm.sh

- name: Install Cilium
  block:
    - name: Add Cilium repository
      command: helm repo add cilium https://helm.cilium.io/

    - name: Update repositories
      command: helm repo update

    - name: Deploy Cilium
      command: >
        helm install cilium cilium/cilium
        --version {{ cilium_version }}
        --namespace kube-system
        --values /tmp/cilium-values.yaml
      register: cilium_install
      until: cilium_install.rc == 0
      retries: 3
      delay: 10

- name: Wait for CNI readiness
  command: >
    kubectl rollout status 
    {{ item }} 
    -n kube-system 
    --timeout=300s
  loop:
    - daemonset/cilium
    - deployment/cilium-operator
  register: cni_ready
  until: cni_ready.rc == 0
  retries: 10
  delay: 30
