# KMS Setup for New Database Clusters

## Overview
Each database cluster requires its own KMS key for backup encryption. This guide explains how to generate and configure KMS credentials for new database deployments.

## Prerequisites
- AWS CLI installed and configured
- Access to AWS KMS service
- `kubectl` with cluster access
- Helm 3.x installed

## Steps

1. Generate KMS Key
```bash
# Create a unique KMS key for the database cluster
aws kms create-key \
  --description "Backup encryption for ${CLIENT_NAME} database" \
  --tags TagKey=Client,TagValue=${CLIENT_NAME} \
         TagKey=Environment,TagValue=${ENVIRONMENT} \
  --region ${AWS_REGION}

# Store the KeyId and ARN from the output
# Example output:
# {
#   "KeyMetadata": {
#     "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab",
#     "Arn": "arn:aws:kms:region:123456789012:key/1234abcd-12ab-34cd-56ef-1234567890ab"
#   }
# }
```

2. Create Descriptive Alias
```bash
# Create an alias for easy key identification
aws kms create-alias \
  --alias-name "alias/${CLIENT_NAME}-db-backup" \
  --target-key-id ${KEY_ID} \
  --region ${AWS_REGION}
```

3. Create values.yaml for Helm Chart
```yaml
client:
  name: "${CLIENT_NAME}"

cluster:
  instances: 3
  storage:
    size: 100Gi

backup:
  s3:
    bucket: "${R2_BUCKET}"
    region: "${R2_REGION}"
    endpoint: "${R2_ENDPOINT}"
    accessKeyId: "${R2_ACCESS_KEY}"
    secretAccessKey: "${R2_SECRET_KEY}"
  kms:
    keyId: "${KMS_KEY_ID}"
    region: "${AWS_REGION}"
    accessKey: "${AWS_ACCESS_KEY}"
    secretKey: "${AWS_SECRET_KEY}"
```

4. Deploy Database Cluster
```bash
# Deploy using client-db Helm chart
helm install ${CLIENT_NAME} ./helm/client-db \
  --namespace crx-db \
  --values values.yaml
```

5. Verify KMS Integration
```bash
# Check if KMS secret was created
kubectl get secret ${CLIENT_NAME}-kms-key -n crx-db

# Verify backup encryption status
kubectl get cluster ${CLIENT_NAME} -n crx-db -o jsonpath='{.status.backup.encryption}'
```

## Backup Verification

1. Trigger Test Backup
```bash
# Create manual backup
kubectl cnpg backup ${CLIENT_NAME} -n crx-db

# Check backup status
kubectl get backup -n crx-db -l cnpg.io/cluster=${CLIENT_NAME}
```

2. Verify Encryption
```bash
# Get backup metadata
kubectl describe backup ${CLIENT_NAME}-backup-1 -n crx-db

# Check encryption status in the output
# Look for: "Encryption: enabled"
```

## Key Rotation

KMS keys are automatically rotated according to the schedule in `setup_r2_credentials.yml`. To manually rotate:

```bash
# Update KMS key
aws kms update-key-description \
  --key-id ${KEY_ID} \
  --description "Backup encryption for ${CLIENT_NAME} database - rotated $(date +%Y-%m-%d)" \
  --region ${AWS_REGION}

# Trigger credentials update
ansible-playbook ansible/tasks/setup_r2_credentials.yml -e "client_name=${CLIENT_NAME}"
```

## Monitoring

Monitor KMS key usage through:

1. AWS CloudTrail logs
2. Prometheus metrics:
```bash
# KMS operation metrics
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=kms_operation_total

# Encryption status
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=postgresql_backup_encryption_status
```

## Troubleshooting

1. If backup encryption fails:
```bash
# Check KMS key status
aws kms get-key-policy --key-id ${KEY_ID} --policy-name default

# Verify IAM permissions
aws iam simulate-principal-policy \
  --policy-source-arn ${ROLE_ARN} \
  --action-names kms:Encrypt kms:Decrypt

# Check operator logs
kubectl logs -n crx-db deployment/cnpg-controller-manager
```

2. If key rotation fails:
```bash
# Check rotation logs
kubectl logs -n crx-db job/rotate-kms-keys-${CLIENT_NAME}

# Verify KMS permissions
aws kms get-key-rotation-status --key-id ${KEY_ID}
```

## Security Best Practices

1. Use unique KMS keys per database cluster
2. Enable AWS CloudTrail logging for KMS operations
3. Regularly audit key usage
4. Monitor failed encryption attempts
5. Maintain separate keys for different environments
6. Document key IDs and aliases in secure location