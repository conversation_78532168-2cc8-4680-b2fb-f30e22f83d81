# AWS Management Machine Setup

## Prerequisites
- Linux/macOS/Windows with admin access
- Python 3.x installed
- Internet connection
- AWS account with administrative access

## Installation Steps

### 1. Install AWS CLI

#### On Ubuntu/Debian
```bash
sudo apt update
sudo apt install -y awscli
```

#### On macOS
```bash
brew install awscli
```

#### On Other Systems
```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

### 2. Verify Installation
```bash
aws --version
```

### 3. Configure AWS CLI

#### Option A: Using AWS Configure
```bash
aws configure
```
Enter the following when prompted:
- AWS Access Key ID
- AWS Secret Access Key
- Default region (e.g., us-west-2)
- Default output format (json)

#### Option B: Using Environment Variables
Add to `~/.bashrc` or `~/.zshrc`:
```bash
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-west-2"
```

### 4. Install Session Manager Plugin (Required for SSM)
```bash
# Ubuntu/Debian
curl "https://s3.amazonaws.com/session-manager-downloads/plugin/latest/ubuntu_64bit/session-manager-plugin.deb" -o "session-manager-plugin.deb"
sudo dpkg -i session-manager-plugin.deb

# macOS
brew install session-manager-plugin
```

## Verification

### 1. Test AWS Access
```bash
# List S3 buckets
aws s3 ls

# List KMS keys
aws kms list-keys

# List SSM parameters
aws ssm describe-parameters
```

### 2. Test Required Permissions
```bash
# Test KMS access
aws kms describe-key --key-id <your-key-id>

# Test SSM access
aws ssm get-parameter \
  --name "/r2/backup-credentials/access-key" \
  --with-decryption

# Test R2 access
aws s3 ls s3://<your-bucket> \
  --endpoint-url <r2-endpoint>
```

## Required IAM Permissions

Ensure your AWS user has the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "kms:Encrypt",
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:CreateKey",
                "kms:ListKeys",
                "kms:CreateAlias",
                "kms:DeleteAlias",
                "kms:UpdateAlias"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ssm:GetParameter",
                "ssm:GetParameters",
                "ssm:PutParameter",
                "ssm:DeleteParameter",
                "ssm:DescribeParameters"
            ],
            "Resource": "arn:aws:ssm:*:*:parameter/r2/*"
        }
    ]
}
```

## Multiple Profiles Setup

If you need to manage multiple AWS accounts:

### 1. Create Named Profiles
```bash
aws configure --profile prod
aws configure --profile staging
```

### 2. Using Profiles
```bash
# Command with specific profile
aws s3 ls --profile prod

# Set default profile
export AWS_PROFILE=prod
```

### 3. Profile Configuration
Edit `~/.aws/config`:
```ini
[profile prod]
region = us-west-2
output = json

[profile staging]
region = us-east-1
output = json
```

## Troubleshooting

### 1. Authentication Issues
```bash
# Verify credentials
aws sts get-caller-identity

# Check AWS CLI configuration
aws configure list
```

### 2. Permission Issues
```bash
# Test specific permissions
aws iam simulate-principal-policy \
  --policy-source-arn <your-arn> \
  --action-names kms:Encrypt ssm:GetParameter
```

### 3. Common Problems

#### Invalid Credentials
```bash
# Verify credential file permissions
ls -l ~/.aws/credentials
chmod 600 ~/.aws/credentials
```

#### Region Issues
```bash
# Verify current region
aws configure get region

# List available regions
aws ec2 describe-regions
```

## Security Best Practices

1. Credential Management
   - Never commit AWS credentials to version control
   - Use environment variables for temporary access
   - Regularly rotate access keys

2. Access Control
   - Use least-privilege permissions
   - Enable MFA for AWS account
   - Regular audit of used permissions

3. Configuration Security
   - Secure storage of config files
   - Use separate profiles for different environments
   - Regular backup of AWS configuration

## Additional Resources

- [AWS CLI Documentation](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html)
- [AWS IAM Best Practices](https://docs.aws.amazon.com/IAM/latest/UserGuide/best-practices.html)
- [AWS KMS Documentation](https://docs.aws.amazon.com/kms/latest/developerguide/overview.html)