# Infrastructure Architecture

## Storage Architecture

### Physical Layout
- **OS Storage** (per node)
  - Device paths: `/dev/nvme0n1`, `/dev/nvme1n1`
  - Purpose: Operating system, container storage

- **Ceph Storage** (per node)
  - 2x NVMe
  - Device paths: `/dev/nvme2n1`, `/dev/nvme3n1`
  - Purpose: Database storage, backups

### Rook-Ceph Configuration
- BlueStore optimized for database workloads
  - DB size: 100GB per OSD
  - WAL size: 100GB per OSD
  - Memory target: 4GB per OSD
- Encryption at rest via AWS KMS
- Snapshot support for database cloning

## Network Architecture

### Cilium CNI
- WireGuard encryption for pod traffic
- Network policy enforcement
- Load balancing for services
- Monitoring integration

### Network Policies
1. Database Access
   - Metrics ports (9187, 8080) for Prometheus
   - PostgreSQL port (5432) for connection pooler
   - Implicit deny for all other traffic

2. Backup Traffic
   - R2 endpoint access
   - KMS service access
   - Monitoring service access

## Database Management

### CloudNativePG Features
- High availability clustering
- Automated failover
- Connection pooling
  - Read-write separation
  - Connection limiting
  - SSL enforcement
- Backup automation
  - Daily full backups
  - Continuous WAL archiving
  - Point-in-time recovery

### Resource Management
```yaml
Standard Database Profile:
  CPU: 2 cores
  Memory: 8Gi
  Storage: 100Gi
  Connections: 300
```

## Backup System

### Components
1. Database Backups
   - Daily full backups to R2
   - Continuous WAL archiving
   - 30-day retention
   - Automated verification

2. etcd Backups
   - 6-hour backup schedule
   - KMS encryption
   - R2 storage
   - Automated verification

### Retention Policy
| Type | Retention | Count | Storage |
|------|-----------|-------|---------|
| Hourly | 24 hours | 24 | R2 |
| Daily | 7 days | 7 | R2 |
| Weekly | 4 weeks | 4 | R2 |
| Monthly | 12 months | 12 | R2 |

## Security Implementation

### Encryption
1. Data at Rest
   - AWS KMS for Ceph volumes
   - Encrypted backups in R2
   - Encrypted secrets

2. Data in Transit
   - WireGuard for pod traffic
   - TLS for service communication
   - SSL for database connections

### Access Control
1. Network Level
   - Cilium network policies
   - Service mesh authentication
   - Load balancer restrictions

2. Application Level
   - RBAC for Kubernetes resources
   - Database user management
   - Connection pooler authentication

### Credential Management
- 6-hour rotation cycle
- AWS KMS integration
- Secure distribution via Kubernetes secrets

## Monitoring Stack

### Core Metrics
1. Infrastructure
   - Node resources
   - Network performance
   - Storage capacity

2. Database
   - Connection stats
   - Query performance
   - Backup status

### Alert Rules
1. Critical Alerts
   - Node failures
   - Database unavailability
   - Backup failures
   - Storage issues

2. Warning Alerts
   - High resource usage
   - Slow queries
   - Certificate expiration
   - Backup delays

## Deployment Process

### Prerequisites
- Kubernetes 1.32+
- Helm 3.13+
- AWS KMS access
- R2 storage bucket

### Installation Steps
1. Core Infrastructure
```bash
ansible-playbook site.yml
```

2. Database Deployment
```bash
helm install client-db ./helm/client-db \
  --namespace crx-db \
  --values values.yaml
```

## Maintenance Procedures

### Regular Tasks
1. Daily Checks
   - Monitor system health
   - Verify backups
   - Check alerts

2. Weekly Tasks
   - Review resource usage
   - Update security policies
   - Verify recovery procedures

3. Monthly Tasks
   - System updates
   - Performance optimization
   - Security audit

### Emergency Procedures
- [Node Failure Recovery](../maintenance/node-recovery.md)
- [Database Recovery](../maintenance/database-recovery.md)
- [Backup Restoration](../maintenance/backup-restore.md)
