# Credential Management

## Overview
This project uses AWS KMS for encrypting sensitive credentials, particularly for R2 backup storage access. Credentials are automatically rotated and stored securely in AWS SSM Parameter Store.

## Architecture

### Components
- AWS KMS: Encryption of credentials
- AWS SSM Parameter Store: Secure storage of encrypted credentials
- Kubernetes Secrets: Runtime access to decrypted credentials
- Automated rotation script: Regular credential updates

### Credential Flow
1. R2 credentials are encrypted using AWS KMS
2. Encrypted credentials are stored in SSM Parameter Store
3. Ansible retrieves and decrypts credentials during deployment
4. Kubernetes secrets are created with decrypted values
5. Automated rotation updates all components every 6 hours

## Setup

### 1. KMS Configuration
Follow instructions in `docs/infrastructure/KMS-SETUP.md` to:
- Create KMS key
- Configure key policies
- Set up necessary IAM roles

### 2. SSM Parameter Store
Required parameters in AWS Systems Manager Parameter Store:

```bash
# Parameter name for R2 access key
/r2/backup-credentials/access-key
    Type: SecureString
    Description: Cloudflare R2 access key for backup storage
    KMS Key: Use key created in step 1
    Value: Your R2 access key

# Parameter name for R2 secret key
/r2/backup-credentials/secret-key
    Type: SecureString
    Description: Cloudflare R2 secret key for backup storage
    KMS Key: Use key created in step 1
    Value: Your R2 secret key
```

These parameters can be created using the AWS CLI:

```bash
# Create access key parameter
aws ssm put-parameter \
    --name "/r2/backup-credentials/access-key" \
    --type "SecureString" \
    --value "your-r2-access-key" \
    --key-id "alias/db-backup-key" \
    --description "Cloudflare R2 access key for backup storage"

# Create secret key parameter
aws ssm put-parameter \
    --name "/r2/backup-credentials/secret-key" \
    --type "SecureString" \
    --value "your-r2-secret-key" \
    --key-id "alias/db-backup-key" \
    --description "Cloudflare R2 secret key for backup storage"
```

Note: These parameters:
- Must be created as SecureString type for KMS encryption
- Will be automatically rotated every 6 hours
- Are used by Ansible for creating Kubernetes secrets
- Should only be accessible by authorized IAM roles

### 3. Ansible Configuration
In `ansible/inventory/group_vars/all.yml`:
```yaml
backup:
  provider: "r2"
  bucket: "k8s-backups-{{ cluster_name }}"
  credentials: "{{ lookup('aws_ssm', '/r2/backup-credentials/access-key', region='us-west-2') }}"
```

## Credential Rotation

### Automated Rotation
The system automatically rotates credentials every 6 hours:
1. Generates new R2 credentials
2. Encrypts using KMS
3. Updates SSM parameters
4. Updates Kubernetes secrets
5. Triggers database operator reload

### Manual Rotation
To manually trigger rotation:
```bash
# For all clusters
ansible-playbook ansible/tasks/setup_r2_credentials.yml

# For specific client
ansible-playbook ansible/tasks/setup_r2_credentials.yml -e "client_name=<client>"
```

## Verification

### Check Current Credentials
```bash
# View SSM parameter metadata
aws ssm describe-parameters --parameter-filters "Key=Name,Values=/r2/backup-credentials/*"

# Check Kubernetes secrets
kubectl get secret r2-backup-secret -n crx-db

# Verify database operator status
kubectl get clusters -n crx-db -o yaml | grep cnpg.io/reload-credentials
```

### Monitor Rotation
```bash
# Check rotation logs
journalctl -u rotate-r2-credentials

# View Prometheus metrics
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=credential_rotation_status
```

## Troubleshooting

### Common Issues

1. KMS Access
```bash
# Verify KMS key status
aws kms describe-key --key-id <key-id>

# Check IAM permissions
aws iam simulate-principal-policy \
  --policy-source-arn <role-arn> \
  --action-names kms:Decrypt kms:Encrypt
```

2. SSM Access
```bash
# Test SSM parameter access
aws ssm get-parameter \
  --name "/r2/backup-credentials/access-key" \
  --with-decryption
```

3. Rotation Failures
```bash
# Check rotation service
systemctl status rotate-r2-credentials.timer
systemctl status rotate-r2-credentials.service

# View detailed logs
journalctl -u rotate-r2-credentials -n 100
```

## Security Best Practices

1. Access Control
- Use least-privilege IAM roles
- Implement strict KMS key policies
- Regular audit of access patterns

2. Monitoring
- Alert on rotation failures
- Monitor credential usage
- Track access attempts

3. Disaster Recovery
- Document credential recovery procedures
- Regular testing of rotation system
- Backup access procedures

## Related Documentation
- `docs/infrastructure/KMS-SETUP.md`: KMS configuration details
- `docs/backup/backup-retention.md`: Backup system overview
- `docs/maintenance/PROCEDURES.md`: Maintenance procedures
