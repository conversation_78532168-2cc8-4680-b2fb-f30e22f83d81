# User Management Guide

## Creating Database Administrators

This guide explains how to create new users and grant them database administration privileges through the `db-admins` group.

### Prerequisites
- `kubectl` access to the cluster
- Access to create ClusterRoleBindings
- AWS IAM permissions (if using AWS IAM authentication)

## Steps

### 1. Create Kubernetes Authentication Resources

Create a new file `user-auth.yaml`:

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: <username>
  namespace: crx-db
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Group
metadata:
  name: db-admins
subjects:
- kind: ServiceAccount
  name: <username>
  namespace: crx-db
```

Apply the configuration:
```bash
kubectl apply -f user-auth.yaml
```

### 2. Add User to db-admins Group

```bash
kubectl create clusterrolebinding <username>-db-admin \
  --clusterrole=db-credential-reader \
  --serviceaccount=crx-db:<username>
```

### 3. Generate kubeconfig

```bash
# Get service account token
SECRET_NAME=$(kubectl get serviceaccount <username> -n crx-db \
  -o jsonpath='{.secrets[0].name}')

TOKEN=$(kubectl get secret $SECRET_NAME -n crx-db \
  -o jsonpath='{.data.token}' | base64 -d)

# Get cluster details
CLUSTER_NAME=$(kubectl config current-context)
CLUSTER_CA=$(kubectl config view --raw -o jsonpath='{.clusters[0].cluster.certificate-authority-data}')
API_SERVER=$(kubectl config view --raw -o jsonpath='{.clusters[0].cluster.server}')

# Create kubeconfig
cat << EOF > ${username}-kubeconfig.yaml
apiVersion: v1
kind: Config
clusters:
- name: ${CLUSTER_NAME}
  cluster:
    certificate-authority-data: ${CLUSTER_CA}
    server: ${API_SERVER}
contexts:
- name: ${username}@${CLUSTER_NAME}
  context:
    cluster: ${CLUSTER_NAME}
    user: ${username}
current-context: ${username}@${CLUSTER_NAME}
users:
- name: ${username}
  user:
    token: ${TOKEN}
EOF
```

### 4. Verify Access

The new user should now be able to:

```bash
# List database clusters
kubectl get clusters -n crx-db

# Get database credentials
kubectl get secret <client-name>-superuser -n crx-db

# Get connection pooler endpoints
kubectl get svc <client-name>-rw-pooler -n crx-db
kubectl get svc <client-name>-ro-pooler -n crx-db
```

### 5. Access Database

```bash
# Get superuser password
PASSWORD=$(kubectl get secret <client-name>-superuser -n crx-db \
  -o jsonpath='{.data.password}' | base64 -d)

# Get connection pooler endpoint
HOST=$(kubectl get svc <client-name>-rw-pooler -n crx-db \
  -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

# Connect using psql
PGPASSWORD=$PASSWORD psql -h $HOST -U postgres -d postgres
```

## Permissions Overview

Members of the `db-admins` group have access to:

- Read database credentials (`*-superuser` secrets)
- View connection pooler services (`*-rw-pooler`, `*-ro-pooler`)
- List and describe database clusters
- View backup status and logs

## Security Notes

1. Token Rotation
   - Service account tokens are automatically rotated every 24 hours
   - New kubeconfig should be generated after rotation

2. Access Audit
   - All actions are logged in Kubernetes audit logs
   - Database access is logged in PostgreSQL logs

3. Best Practices
   - Use separate service accounts for each user
   - Regularly review group memberships
   - Follow least privilege principle

## Troubleshooting

### Common Issues

1. Permission Denied
```bash
# Verify group membership
kubectl get groups db-admins -o yaml

# Check RBAC bindings
kubectl get clusterrolebinding <username>-db-admin -o yaml
```

2. Invalid Token
```bash
# Regenerate service account token
kubectl delete secret $SECRET_NAME -n crx-db
kubectl create token <username> -n crx-db
```

3. Connection Issues
```bash
# Verify network policies
kubectl get networkpolicies -n crx-db

# Check service endpoints
kubectl get endpoints <client-name>-rw-pooler -n crx-db
```

## Related Documentation
- [Database Access Control](./DATABASE-ACCESS.md)
- [Security Policies](./SECURITY.md)
- [Audit Logging](./AUDIT.md)