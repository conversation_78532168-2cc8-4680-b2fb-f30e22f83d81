# AWS KMS Setup for etcd Backups

## Overview
This document describes the AWS KMS configuration used for encrypting etcd cluster backups. The KMS key is used by the automated backup system to encrypt etcd snapshots before storing them in Cloudflare R2.

## Prerequisites
- AWS CLI installed and configured
- Appropriate AWS IAM permissions
- `kubectl` with cluster access
- Access to etcd control plane nodes

## Steps

1. Create KMS Key for etcd Backups
```bash
# Create a KMS key specifically for etcd backup encryption
aws kms create-key \
  --description "etcd backup encryption key" \
  --tags TagKey=Service,TagValue=etcd \
         TagKey=Purpose,TagValue=Backup \
         TagKey=Environment,TagValue=Production \
  --region <your-region>

# Save the KeyId and ARN from the output
```

2. Create Key Alias
```bash
aws kms create-alias \
  --alias-name "alias/etcd-backup-key" \
  --target-key-id <key-id-from-previous-step> \
  --region <your-region>
```

3. Configure Key Policy
```bash
aws kms put-key-policy \
  --key-id <key-id> \
  --policy-name default \
  --policy '{
    "Version": "2025-03-22",
    "Statement": [
        {
            "Sid": "Enable IAM User Permissions",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::<account-id>:root"
            },
            "Action": "kms:*",
            "Resource": "*"
        },
        {
            "Sid": "Allow etcd Backup Encryption",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::<account-id>:role/<k8s-role>"
            },
            "Action": [
                "kms:Encrypt",
                "kms:Decrypt",
                "kms:GenerateDataKey"
            ],
            "Resource": "*"
        }
    ]
}'
```

4. Update Ansible Variables
Add the following to `ansible/inventory/group_vars/all.yml`:
```yaml
aws_kms:
  region: "<your-region>"
  key_id: "<your-key-id>"
  access_key: "<access-key>"
  secret_key: "<secret-key>"
```

5. Apply Configuration
```bash
# Deploy etcd backup configuration
ansible-playbook ansible/tasks/setup_etcd_backup.yml

# Verify the backup script is working
kubectl -n kube-system exec -it etcd-<node-name> -- /usr/local/bin/etcd-backup.sh

# Check for encrypted backup file
kubectl -n kube-system exec -it etcd-<node-name> -- ls -l /var/lib/etcd/backup/
```

## Backup Schedule

etcd backups are automatically taken every 6 hours via the systemd timer configured in `ansible/templates/etcd-backup.timer.j2`. Each backup is:
1. Encrypted using this KMS key
2. Stored in Cloudflare R2
3. Verified for integrity
4. Monitored via Prometheus metrics

## Verification

1. Check KMS key status:
```bash
aws kms describe-key --key-id <key-id>
```

2. Verify backup encryption is working:
```bash
# Test encryption of sample etcd data
echo "etcd-test-data" | aws kms encrypt \
  --key-id <key-id> \
  --plaintext fileb:///dev/stdin \
  --output text --query CiphertextBlob

# Verify decryption works
aws kms decrypt \
  --ciphertext-blob fileb:///dev/stdin \
  --output text --query Plaintext | base64 -d
```

3. Verify backup process:
```bash
# Check backup metrics
curl -s http://localhost:9100/metrics | grep etcd_backup

# Check latest backup status
kubectl -n kube-system exec -it etcd-<node-name> -- \
  etcdctl snapshot status /var/lib/etcd/backup/latest.db
```

## Troubleshooting

1. If encryption fails:
   - Verify KMS key is active
   - Check IAM permissions
   - Ensure correct region is set

2. If rotation fails:
   - Check cron logs: `grep CRON /var/log/syslog`
   - Verify script permissions: `ls -l /usr/local/bin/rotate-r2-credentials`
   - Check script execution: `/usr/local/bin/rotate-r2-credentials`

## Security Considerations

- Store KMS credentials securely
- Use separate keys for different environments
- Regularly audit key usage
- Monitor failed encryption/decryption attempts

## Additional Security Considerations

### Key Deletion Protection
1. Enable deletion protection:
```bash
aws kms enable-key-deletion-protection --key-id <key-id>
```

2. Configure key tags for tracking:
```bash
aws kms tag-resource \
  --key-id <key-id> \
  --tags TagKey=Purpose,TagValue=DatabaseBackup \
         TagKey=Environment,TagValue=Production \
         TagKey=DeletionProtection,TagValue=Required
```

### Multi-Region Configuration
For disaster recovery scenarios:

1. Create multi-region key:
```bash
aws kms create-key \
  --multi-region \
  --description "Multi-region database backup key" \
  --tags TagKey=Purpose,TagValue=DatabaseBackup
```

2. Replicate to secondary region:
```bash
aws kms replicate-key \
  --key-id <primary-key-id> \
  --replica-region <secondary-region>
```

### Key Usage Monitoring

1. Enable AWS CloudTrail logging:
```bash
aws cloudtrail create-trail \
  --name kms-audit-trail \
  --s3-bucket-name <your-bucket> \
  --kms-key-id <key-id> \
  --is-multi-region-trail
```

2. Create CloudWatch alarms for suspicious activity:
```bash
aws cloudwatch put-metric-alarm \
  --alarm-name KMSKeyUsageSpike \
  --metric-name DecryptionsExceeded \
  --namespace AWS/KMS \
  --threshold 1000 \
  --period 300 \
  --evaluation-periods 2 \
  --comparison-operator GreaterThanThreshold
```

### Automated Key Auditing

1. Create daily key audit script:
```bash
#!/bin/bash
aws kms list-keys --query 'Keys[*].KeyId' --output text | while read -r key_id; do
  aws kms get-key-rotation-status --key-id "$key_id"
  aws kms list-key-policies --key-id "$key_id"
  aws kms get-key-policy --key-id "$key_id" --policy-name default
done > /var/log/kms-audit/daily-audit-$(date +%Y%m%d).log
```

2. Schedule automated policy compliance check:
```bash
aws kms list-keys --query 'Keys[*].KeyId' --output text | while read -r key_id; do
  aws iam simulate-principal-policy \
    --policy-source-arn "arn:aws:kms:region:account:key/$key_id" \
    --action-names "kms:Decrypt" "kms:Encrypt" "kms:GenerateDataKey"
done
```

### Emergency Access Procedures

1. Create break-glass access policy:
```json
{
    "Sid": "EmergencyAccess",
    "Effect": "Allow",
    "Principal": {
        "AWS": "arn:aws:iam::account:role/EmergencyAccess"
    },
    "Action": [
        "kms:Decrypt",
        "kms:DescribeKey"
    ],
    "Resource": "*",
    "Condition": {
        "StringEquals": {
            "aws:PrincipalTag/EmergencyAccess": "true"
        }
    }
}
```

2. Document emergency access procedure:
```bash
# 1. Activate emergency access role
aws iam update-role \
  --role-name EmergencyAccess \
  --tags Key=EmergencyAccess,Value=true

# 2. Decrypt emergency backup
aws kms decrypt \
  --key-id <key-id> \
  --ciphertext-blob fileb://encrypted-backup \
  --role-arn arn:aws:iam::account:role/EmergencyAccess

# 3. Deactivate emergency access
aws iam update-role \
  --role-name EmergencyAccess \
  --tags Key=EmergencyAccess,Value=false
```

### Key Rotation Verification

1. Verify automatic rotation status:
```bash
aws kms get-key-rotation-status --key-id <key-id>
```

2. Monitor rotation events:
```bash
aws cloudwatch get-metric-statistics \
  --namespace AWS/KMS \
  --metric-name KeyRotation \
  --dimensions Name=KeyId,Value=<key-id> \
  --start-time $(date -d '24 hours ago' --iso-8601=seconds) \
  --end-time $(date --iso-8601=seconds) \
  --period 3600 \
  --statistics Sum
```

### Backup Key Material

1. Export key metadata:
```bash
aws kms describe-key --key-id <key-id> \
  --output json > "key-metadata-$(date +%Y%m%d).json"
```

2. Create encrypted backup of key policy:
```bash
aws kms get-key-policy \
  --key-id <key-id> \
  --policy-name default \
  | aws kms encrypt \
    --key-id <backup-key-id> \
    --plaintext fileb:///dev/stdin \
    --output text \
    --query CiphertextBlob > "key-policy-$(date +%Y%m%d).encrypted"
```

3. Store backup information in SSM Parameter Store:
```bash
aws ssm put-parameter \
  --name "/kms/backup/key-id-$(date +%Y%m%d)" \
  --value "$(cat key-metadata-$(date +%Y%m%d).json)" \
  --type SecureString \
  --key-id <backup-key-id>
```

4. Verify backup:
```bash
# Verify metadata backup
aws ssm get-parameter \
  --name "/kms/backup/key-id-$(date +%Y%m%d)" \
  --with-decryption

# Verify policy backup
aws kms decrypt \
  --key-id <backup-key-id> \
  --ciphertext-blob fileb://key-policy-$(date +%Y%m%d).encrypted
```

## Compliance Requirements

1. Regular key auditing (monthly)
2. Access review (quarterly)
3. Rotation verification (weekly)
4. Emergency access testing (quarterly)
5. Backup verification (monthly)

## Related Documentation
- `docs/disaster-recovery/KMS-RECOVERY.md`: Recovery procedures
- `docs/security/KEY-MANAGEMENT.md`: Key management policies
- `docs/compliance/KMS-AUDIT.md`: Audit requirements
