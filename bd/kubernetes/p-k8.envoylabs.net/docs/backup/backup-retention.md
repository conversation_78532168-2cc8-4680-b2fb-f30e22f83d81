# PostgreSQL Backup Retention Policy

## Overview
Our PostgreSQL backup strategy uses CloudNativePG with Cloudflare R2 storage, implementing a comprehensive retention policy:

| Backup Type | Retention Period | Count | Purpose |
|------------|------------------|-------|---------|
| Hourly     | 24 hours        | 24    | Fine-grained recovery within last day |
| Daily      | 7 days          | 7     | Recent point-in-time recovery |
| Weekly     | 4 weeks         | 4     | Medium-term recovery points |
| Monthly    | 12 months       | 12    | Long-term recovery points |
| Yearly     | 2 years         | 2     | Compliance and archival |

## Implementation Details

### CloudNativePG Configuration
Required R2 Configuration:
```yaml
backup:
  s3:
    bucket: ""      # R2 bucket name
    region: ""      # R2 region
    endpoint: ""    # R2 endpoint URL
    accessKeyId: "" # R2 access key
    secretAccessKey: "" # R2 secret key
```

Storage Structure:
- Backups stored in R2: `s3://{{ bucket }}/{{ cluster_name }}`
- Compression: gzip
- Schedule: Daily at midnight
- Target: Primary instance only
- Immediate backup on cluster creation

### Monitoring
- Prometheus alerts for:
  - Failed backups
  - Missing backup types
  - Retention violations
  - R2 storage usage

### Security
- AWS KMS encryption
- R2 credentials rotated every 6 hours
- Backup metadata encrypted

## Verification
Automated verification is handled by CloudNativePG operator, which:
1. Creates test restores daily
2. Verifies backup integrity
3. Validates WAL archives
4. Checks retention policy compliance
5. Monitors storage consumption

### Monitoring
Verification status can be checked via:
```bash
# Check verification status
kubectl get backup -n crx-db

# Query metrics
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=cnpg_backup_verification_status

# View recent verification jobs
kubectl get pods -n crx-db -l cnpg.io/verifyBackup
```

### Alerts
Prometheus alerts will fire when:
- Backup verification fails
- No successful backup in 24 hours
- Backup is older than 12 hours
- Storage quota exceeded

## Recovery Testing
Monthly recovery tests should verify:
1. Full cluster recovery
2. Point-in-time recovery
3. Individual database recovery
