# Detailed Recovery Tutorial

## 1. PostgreSQL Database Cluster Recovery

### Scenario A: Restoring a Single Database Cluster

#### Prerequisites
- Access to Kubernetes cluster
- `kubectl` configured
- CloudNativePG operator running
- Access to R2 backup storage
- R2 credentials configured:
  - Bucket name
  - Region
  - Endpoint URL
  - Access key
  - Secret key

#### Step-by-Step Recovery
1. Create a values file for the restored database:
   ```yaml
   source:
     name: "<original-cluster-name>"
     namespace: "crx-db"
   
   clone:
     name: "restored-db"
     namespace: "crx-db"
     instances: 2
   
   storage:
     size: "100Gi"
     storageClass: "ceph-block"
     snapshotClass: "ceph-snapshot"
   
   backup:
     s3:
       bucket: ""      # Required: R2 bucket name
       region: ""      # Required: R2 region
       endpoint: ""    # Required: R2 endpoint URL
       accessKeyId: "" # Required: R2 access key
       secretAccessKey: "" # Required: R2 secret key
   ```

2. Identify available backups:
   ```bash
   # List all backups for the cluster
   kubectl get backup -n crx-db -l cnpg.io/cluster=<original-cluster-name>
   
   # Get details of specific backup
   kubectl describe backup <backup-name> -n crx-db
   ```

3. Deploy recovery cluster using db-clone chart:
   ```bash
   helm install restored-db ./helm/db-clone \
     --namespace crx-db \
     --values values.yaml
   ```

4. Monitor recovery progress:
   ```bash
   # Watch the recovery cluster status
   kubectl get cluster -n crx-db restored-db -w
   
   # Check recovery logs
   kubectl logs -n crx-db restored-db-1 -c postgres
   ```

5. Verify recovered data:
   ```bash
   # Get superuser password
   kubectl get secret restored-db-superuser -n crx-db \
     -o jsonpath='{.data.password}' | base64 -d
   
   # Connect to database
   kubectl exec -it restored-db-1 -n crx-db -- psql
   ```

6. Access restored database:
   Read-Write endpoint:
   ```bash
   kubectl get svc restored-db-rw-pooler -n crx-db
   ```
   
   Read-Only endpoint:
   ```bash
   kubectl get svc restored-db-ro-pooler -n crx-db
   ```

### Scenario B: Point-in-Time Recovery (PITR)

#### Step-by-Step PITR
1. Identify target recovery time:
   ```bash
   # List available WAL archives
   kubectl exec -it ${CLUSTER_NAME}-1 -n crx-db -- \
     barman-cloud-wal-archive list \
     s3://${BUCKET}/${CLUSTER_NAME}/wal/
   ```

2. Create PITR cluster:
   ```bash
   export RECOVERY_TIME="2024-01-20 15:30:00 UTC"
   export PITR_NAME="${CLUSTER_NAME}-pitr-$(date +%s)"
   
   kubectl cnpg restore ${PITR_NAME} \
     --cluster ${CLUSTER_NAME} \
     --backup-name <backup-name> \
     --recovery-target-time "${RECOVERY_TIME}" \
     -n crx-db
   ```

3. Follow steps 3-5 from Scenario A for monitoring and verification

## 2. etcd Cluster Recovery

### Prerequisites
- SSH access to control plane nodes
- AWS CLI configured with KMS access
- etcdctl installed

### Step-by-Step Recovery
1. List available backups:
   ```bash
   aws s3 ls s3://${BUCKET}/etcd-backup/ \
     --endpoint-url ${R2_ENDPOINT}
   ```

2. Choose and restore backup:
   ```bash
   /usr/local/bin/etcd-restore.sh etcd-snapshot-20240101-120000.db.encrypted
   ```

3. Verify cluster health:
   ```bash
   kubectl get nodes
   kubectl get pods -A
   ```

Note: Backups are encrypted using AWS KMS key {{ aws_kms.key_id }}. 
The restore script handles decryption automatically using the same KMS key.

### Scenario B: Single etcd Member Recovery

#### Step-by-Step Recovery
1. Remove failed member:
   ```bash
   # Get member ID
   ETCDCTL_API=3 etcdctl member list
   
   # Remove failed member
   ETCDCTL_API=3 etcdctl member remove <member-id>
   ```

2. Add new member:
   ```bash
   # Add new member
   ETCDCTL_API=3 etcdctl member add ${NODE_NAME} \
     --peer-urls="https://${NODE_IP}:2380"
   ```

3. Initialize new member:
   ```bash
   # On new member node
   rm -rf /var/lib/etcd
   
   systemctl stop etcd
   
   # Start etcd with new configuration
   systemctl start etcd
   ```

4. Verify cluster health:
   ```bash
   ETCDCTL_API=3 etcdctl endpoint health --cluster
   ```

## Post-Recovery Procedures

1. Verify Application Health:
   ```bash
   # Check all pods
   kubectl get pods -A
   
   # Check persistent volumes
   kubectl get pv
   
   # Check storage
   kubectl -n rook-ceph exec -it deploy/rook-ceph-tools -- ceph status
   ```

2. Update Documentation:
   - Record recovery time
   - Document any issues encountered
   - Update procedures if needed

3. Validate Backups:
   ```bash
   # Trigger new database backup
   kubectl cnpg backup ${CLUSTER_NAME} -n crx-db
   
   # Verify etcd backup
   etcdctl snapshot status /tmp/etcd-snapshot-latest.db
   ```

4. Update Monitoring:
   ```bash
   # Check Prometheus alerts
   kubectl get prometheusrule -A
   
   # Verify metrics collection
   kubectl port-forward svc/prometheus-operated 9090:9090
   # Access localhost:9090 in browser
   ```

## Common Issues and Solutions

1. Backup Corruption
   - Verify backup integrity before restore
   - Use previous backup if latest is corrupted
   - Check R2 storage logs for errors

2. Network Issues
   - Verify R2 endpoint accessibility
   - Check network policies
   - Validate credentials

3. Permission Issues
   - Verify service account permissions
   - Check RBAC configurations
   - Validate node access rights

## Recovery Time Estimates

| Scenario | Estimated Time | Factors |
|----------|---------------|----------|
| Single Database | 15-30 mins | Backup size, network speed |
| PITR Recovery | 30-60 mins | WAL archive size, target time |
| Full etcd | 30-45 mins | Backup size, node count |
| Single etcd member | 15-20 mins | Node setup time |
