# Ansible Tasks Documentation

## Core Tasks

### 1. k8s_common.yml
Purpose: Sets up base Kubernetes requirements on all nodes
Key Operations:
- Installs common utilities (htop)
- Configures Kubernetes repositories
- Installs kubelet, kubeadm, and kubectl packages
- Updates system packages

### 2. k8s_control_plane.yml
Purpose: Initializes Kubernetes control plane nodes
Key Operations:
- Sets system hostname
- Updates /etc/hosts with hostname
- Generates kubeadm configuration
- Initializes first control plane node
- Stores certificate keys
- Creates join commands for other nodes
- Configures high availability setup

### 3. k8s_workers.yml
Purpose: Configures worker nodes
Key Operations:
- Sets system hostname
- Updates /etc/hosts with hostname
- Prepares NVMe drives for Rook-Ceph storage
  - Wipes drives /dev/nvme2n1 and /dev/nvme3n1
  - Clears partition tables
  - Removes old device mapper entries
- Joins node to cluster

### 4. setup_network_policies.yml
Purpose: Implements network security policies
Key Operations:
- Creates crx-db namespace
- Applies network policies:
  - default-deny
  - allow-system-namespaces
  - allow-monitoring
- Ensures policy application with retries

### 5. setup_etcd_backup.yml
Purpose: Configures automated etcd backups
Key Operations:
- Creates backup script from template
- Sets up systemd service and timer
- Creates metrics directory
- Enables automated backups
- Configures backup monitoring

### 6. verify_cluster.yml
Purpose: Validates cluster health
Key Operations:
- Checks node status
- Verifies API server health
- Tests CNI functionality
- Validates Cilium connectivity
- Displays cluster status

### 7. setup_cloudnativepg.yml
Purpose: Deploys database operator
Key Operations:
- Installs CloudNativePG operator
- Configures operator settings
- Sets up backup integration
- Establishes monitoring
- Creates required secrets

### 8. setup_cni.yml
Purpose: Deploys network solution
Key Operations:
- Installs Cilium CNI
- Configures WireGuard encryption
- Sets up network policies
- Enables monitoring integration
- Configures pod networking

### 9. setup_rook_ceph.yml
Purpose: Configures storage system
Key Operations:
- Deploys Rook operator
- Configures Ceph cluster
- Sets up storage classes
- Enables encryption at rest
- Configures monitoring

### 10. setup_monitoring.yml
Purpose: Implements monitoring stack
Key Operations:
- Deploys Prometheus operator
- Configures Grafana
- Sets up ServiceMonitors
- Creates alert rules
- Configures dashboards

### 11. setup_r2_credentials.yml
Purpose: Manages backup storage access
Key Operations:
- Creates R2 credentials
- Configures AWS KMS integration
- Sets up credential rotation
- Implements backup permissions
- Configures secret management

### 12. setup_r2_metrics.yml
Purpose: Configures backup monitoring
Key Operations:
- Sets up storage metrics
- Configures Prometheus rules
- Creates backup dashboards
- Implements alerts
- Enables verification metrics

### configure_db_proxy.yml
Purpose: Configures HAProxy for database access
Key Operations:
- Retrieves worker node IPs from inventory
- Gets database NodePort services from Kubernetes
- Creates client-specific HAProxy configuration
- Sets up read-write and read-only endpoints
- Configures TCP load balancing for database connections
- Ensures configuration is included in main HAProxy config
- Reloads HAProxy service when configuration changes

### remove_db_proxy.yml
Purpose: Removes HAProxy configuration for a database client
Key Operations:
- Validates existence of client configuration
- Removes client-specific HAProxy configuration
- Cleans up main HAProxy config if no clients remain
- Reloads HAProxy service after configuration removal

## Usage Guidelines

### Order of Execution
1. k8s_common.yml (all nodes)
2. k8s_control_plane.yml (control plane nodes)
3. k8s_workers.yml (worker nodes)
4. setup_network_policies.yml
5. setup_cni.yml
6. setup_rook_ceph.yml
7. setup_cloudnativepg.yml
8. setup_monitoring.yml
9. setup_etcd_backup.yml
10. setup_r2_credentials.yml
11. setup_r2_metrics.yml
12. verify_cluster.yml

### Verification
After each task:
```bash
# Check for errors
kubectl get events --sort-by='.lastTimestamp'

# Verify pod status
kubectl get pods -A

# Check specific component
kubectl -n <namespace> describe pod <pod-name>
```

### Common Issues and Solutions

#### Network Policies
- Issue: Pods can't communicate
- Solution: Verify policies in setup_network_policies.yml
- Check: `kubectl get networkpolicies -A`

#### Storage Setup
- Issue: PVCs pending
- Solution: Check Rook-Ceph status
- Check: `kubectl -n rook-ceph get cephcluster`

#### Monitoring
- Issue: Missing metrics
- Solution: Verify ServiceMonitor configuration
- Check: `kubectl get servicemonitor -A`

### Maintenance Tasks

#### Regular Checks
```bash
# Verify etcd backups
ETCDCTL_API=3 etcdctl snapshot status /var/lib/etcd/backup/latest.db

# Check R2 credentials
kubectl get secret r2-credentials -n crx-db

# Verify monitoring
kubectl -n monitoring get pods
```

#### Updates
1. Review task changes
2. Backup configurations
3. Apply updates sequentially
4. Verify each component
5. Update documentation

### Security Considerations

#### Credentials
- Store securely in Kubernetes secrets
- Rotate regularly
- Monitor access logs

#### Network Security
- Maintain strict network policies
- Regular security audits
- Monitor unauthorized access attempts

#### Encryption
- Verify KMS integration
- Check encryption status
- Monitor encryption metrics

## HAProxy Configuration Playbooks

### Configure Database Proxy
To set up HAProxy for a new database client:

```bash
ansible-playbook configure_db_proxy.yml
```

The playbook will:
1. Prompt for the client name (matches the name in `crx-db` namespace)
2. Configure HAProxy endpoints for the client's connection poolers
3. Set up both read-write and read-only access
4. Automatically reload HAProxy

### Remove Database Proxy
To remove HAProxy configuration for a client:

```bash
ansible-playbook remove_db_proxy.yml
```

The playbook will:
1. Prompt for the client name to remove
2. Remove the client's HAProxy configuration
3. Clean up the main config if no clients remain
4. Automatically reload HAProxy

### Verification
After running either playbook, verify the configuration:
```bash
# Check HAProxy status
kubectl get service -n crx-db <client-name>-rw-pooler,<client-name>-ro-pooler

# Verify HAProxy configuration on load balancer
ansible loadbalancer -a "haproxy -c -f /etc/haproxy/haproxy.cfg"
```
