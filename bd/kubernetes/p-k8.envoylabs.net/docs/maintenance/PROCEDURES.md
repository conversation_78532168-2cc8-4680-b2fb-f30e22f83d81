# Maintenance Procedures

## Regular Tasks

### 1. Backup Verification
Frequency: Daily (automated)
Steps:
1. Check backup status:
```bash
# Database backups
kubectl -n crx-db get backups
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=cnpg_backup_verification_status

# etcd backups
etcdctl snapshot status /var/lib/etcd/backup/latest.db
```

2. Verify backup metrics:
```bash
# Check Prometheus alerts
kubectl -n monitoring get prometheusrule
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/alerts

# View verification history
kubectl get pods -n crx-db -l cnpg.io/verifyBackup
```

3. Validate R2 storage:
```bash
# Check storage metrics
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=r2_storage_usage_bytes
```

### 2. Storage Monitoring
Frequency: Daily
Steps:
1. Check Rook-Ceph status:
```bash
kubectl -n rook-ceph get cephcluster
kubectl -n rook-ceph get cephblockpools
```

2. Monitor storage usage:
```bash
# Check PV/PVC status
kubectl get pv,pvc -A

# Verify storage metrics
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=ceph_cluster_total_used_bytes
```

3. Review storage alerts:
```bash
# Check storage-related alerts
kubectl -n monitoring get prometheusrule -l component=storage
```

### 3. Cluster Health Checks
Frequency: Daily
Steps:
1. Node status verification:
```bash
kubectl get nodes
kubectl describe nodes | grep -i pressure
```

2. Control plane health:
```bash
kubectl get componentstatuses
kubectl -n kube-system get pods
```

3. CNI verification:
```bash
cilium status --wait
kubectl -n kube-system get pods -l k8s-app=cilium
```

### 4. Security Policy Review
Frequency: Weekly
Steps:
1. Network policy verification:
```bash
kubectl get networkpolicies -A
kubectl describe networkpolicies -n crx-db
```

2. RBAC audit:
```bash
kubectl auth can-i --list --namespace crx-db
kubectl get roles,rolebindings,clusterroles,clusterrolebindings -A
```

3. Secret management check:
```bash
kubectl get secrets -A
kubectl get secret r2-backup-secret -n crx-db -o yaml
```

### 5. Credential Rotation
Frequency: Automated (6-hourly)
Verification Steps:
1. Check rotation status:
```bash
systemctl status rotate-r2-credentials.timer
journalctl -u rotate-r2-credentials
```

2. Verify current credentials:
```bash
kubectl get secret r2-backup-secret -n crx-db
kubectl get clusters -n crx-db -o yaml | grep cnpg.io/reload-credentials
```

3. Monitor rotation metrics:
```bash
kubectl get --raw /api/v1/namespaces/monitoring/services/prometheus:9090/proxy/api/v1/query?query=credential_rotation_status
```

## Update Process

### 1. Release Note Review
Pre-update Tasks:
1. Review Kubernetes release notes
2. Check operator compatibility:
   - CloudNativePG
   - Rook-Ceph
   - Cilium
3. Document breaking changes
4. Update version variables in `ansible/inventory/group_vars/versions.yml`

### 2. Critical Data Backup
Pre-update Backup:
1. Create etcd snapshot:
```bash
etcdctl snapshot save pre-update-$(date +%Y%m%d).db
```

2. Verify database backups:
```bash
kubectl -n crx-db get backups
kubectl -n crx-db get clusters -o yaml
```

3. Export critical resources:
```bash
kubectl get all -A -o yaml > pre-update-resources.yaml
```

### 3. Ansible Playbook Execution
Update Steps:
1. Apply updates in order:
```bash
ansible-playbook k8s_common.yml
ansible-playbook k8s_control_plane.yml
ansible-playbook k8s_workers.yml
ansible-playbook setup_cni.yml
```

2. Update components:
```bash
ansible-playbook setup_cloudnativepg.yml
ansible-playbook setup_rook_ceph.yml
ansible-playbook setup_monitoring.yml
```

### 4. Health Verification
Post-update Checks:
1. Verify cluster status:
```bash
ansible-playbook verify_cluster.yml
kubectl get nodes
kubectl get pods -A
```

2. Check component health:
```bash
kubectl -n rook-ceph get cephcluster
kubectl -n crx-db get clusters
cilium status
```

### 5. Functionality Testing
Validation Steps:
1. Database operations:
```bash
# Test database creation
kubectl apply -f test/database.yaml
# Verify connectivity
kubectl -n crx-db exec -it <pod-name> -- psql -c "\l"
```

2. Backup functionality:
```bash
# Trigger test backup
kubectl -n crx-db annotate cluster/<cluster-name> cnpg.io/force-backup=true
# Verify backup completion
kubectl -n crx-db get backups
```

3. Network connectivity:
```bash
# Test pod communication
kubectl -n crx-db exec -it <pod-name> -- ping <service-name>
# Verify network policies
kubectl -n crx-db exec -it <pod-name> -- nc -vz <service-name> <port>
```

## Troubleshooting

### Common Issues
1. Failed updates:
   - Check logs: `journalctl -xe`
   - Review events: `kubectl get events --sort-by='.lastTimestamp'`
2. Component failures:
   - Check pod status: `kubectl get pods -A | grep -v Running`
   - Review logs: `kubectl logs -n <namespace> <pod-name>`
3. Network issues:
   - Check CNI: `cilium status`
   - Review policies: `kubectl get networkpolicies -A`
