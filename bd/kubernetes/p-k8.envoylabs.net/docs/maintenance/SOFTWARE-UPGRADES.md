# Node Software Upgrade Procedures

## Overview
This document outlines the procedures for performing software updates on cluster nodes. Updates should be performed in a controlled manner to maintain cluster stability.

## Prerequisites
- Maintenance window scheduled and communicated
- Recent backup verification completed
- Cluster health verified using `verify_cluster.yml`
- Access to all nodes via SSH

## Pre-Update Tasks

1. Verify cluster health:
```bash
# Check node status
kubectl get nodes

# Verify all pods are running
kubectl get pods -A | grep -v Running

# Check for any pending operations
kubectl get all -A | grep -i pending
```

2. Create pre-update backup:
```bash
# Backup etcd
etcdctl snapshot save pre-update-$(date +%Y%m%d).db

# Force database backup
kubectl -n crx-db annotate cluster/cluster-name cnpg.io/force-backup=true
```

## Update Process

### 1. Worker Nodes
Update worker nodes one at a time:

1. Drain the node:
```bash
kubectl drain node-name --ignore-daemonsets --delete-emptydir-data
```

2. Connect to node and perform updates:
```bash
ssh node-name

# Update package list
sudo apt-get update

# Check what updates are available
sudo apt-get --just-print upgrade

# Perform the upgrade
sudo apt-get upgrade -y

# If kernel updates are available
sudo apt-get dist-upgrade -y
```

3. Reboot if required:
```bash
sudo shutdown -r now
```

4. Verify node status:
```bash
# Wait for node to be ready
kubectl get node node-name -w

# Verify pods are running
kubectl get pods -A -o wide | grep node-name
```

5. Uncordon the node:
```bash
kubectl uncordon node-name
```

6. Wait for all pods to be running before proceeding to next node.

### 2. Control Plane Nodes
Update control plane nodes one at a time:

1. Verify etcd health:
```bash
kubectl -n kube-system exec -it etcd-control-plane-1 -- etcdctl endpoint health
```

2. Drain the node:
```bash
kubectl drain control-plane-name --ignore-daemonsets
```

3. Perform updates (same as worker nodes)

4. Verify control plane component status:
```bash
kubectl get componentstatuses
kubectl -n kube-system get pods | grep control-plane-name
```

5. Uncordon the node:
```bash
kubectl uncordon control-plane-name
```

## Post-Update Tasks

1. Verify cluster health:
```bash
# Run verification playbook
ansible-playbook verify_cluster.yml

# Check node versions
kubectl get nodes -o wide

# Verify CNI
cilium status

# Check storage
kubectl -n rook-ceph get cephcluster
```

2. Verify application health:
```bash
# Check database clusters
kubectl -n crx-db get clusters

# Verify monitoring stack
kubectl -n monitoring get pods
```

3. Create post-update backup:
```bash
etcdctl snapshot save post-update-$(date +%Y%m%d).db
```

## Handling Held Packages

Kubernetes packages (kubelet, kubeadm, kubectl) are held by apt. To upgrade these:

1. Unhold packages:
```bash
sudo apt-mark unhold kubelet kubeadm kubectl
```

2. Upgrade specific packages:
```bash
sudo apt-get update
sudo apt-get install -y kubelet=<version> kubeadm=<version> kubectl=<version>
```

3. Re-hold packages:
```bash
sudo apt-mark hold kubelet kubeadm kubectl
```

## Rollback Procedure

If issues occur during updates:

1. Stop the update process immediately

2. Document the issue and gather logs:
```bash
journalctl -xeu kubelet > kubelet-logs.txt
kubectl get events --sort-by='.lastTimestamp' > cluster-events.txt
```

3. If node is unstable after update:
   - Do not uncordon the node
   - Restore from backup if necessary
   - Contact support if needed

## Troubleshooting

### Common Issues

1. Node fails to rejoin cluster:
```bash
# Check kubelet status
systemctl status kubelet

# View kubelet logs
journalctl -xeu kubelet
```

2. Pod scheduling issues:
```bash
# Check node taints
kubectl describe node node-name | grep Taints

# Verify node labels
kubectl get node node-name --show-labels
```

3. Control plane component issues:
```bash
# Check apiserver logs
kubectl -n kube-system logs kube-apiserver-control-plane-name

# Verify etcd health
kubectl -n kube-system exec -it etcd-control-plane-name -- etcdctl endpoint health
```

## Maintenance Window Requirements

- Minimum 2-hour window per node
- Perform updates during low-usage periods
- Have rollback plan ready
- Maintain communication channel with stakeholders

## Documentation Updates

After completing updates:
1. Record all updated package versions
2. Document any issues encountered
3. Update runbooks if needed
4. Record maintenance window duration