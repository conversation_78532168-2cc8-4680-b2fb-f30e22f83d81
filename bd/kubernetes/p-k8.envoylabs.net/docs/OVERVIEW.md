# Infrastructure Documentation

## Overview

This infrastructure implements a production-grade Kubernetes cluster optimized for database workloads. The system provides high availability, automated backups, comprehensive monitoring, and robust security features.

## Key Features

- High Availability
  - 3 control plane nodes with HA etcd cluster
  - 3 worker nodes with workload distribution
  - Automated failover capabilities

- Storage Architecture
  - Rook-Ceph storage with encryption at rest
  - Dedicated 6.4TB NVMe drives (2 per node)
  - BlueStore optimization for database workloads
  - AWS KMS integration for encryption

- Database Infrastructure
  - CloudNativePG operator for PostgreSQL management
  - Connection pooling with read-write separation
  - Automated backup system with verification
  - Point-in-time recovery capabilities

- Security
  - Network encryption via WireGuard (Cilium)
  - Storage encryption at rest (AWS KMS)
  - Automated credential rotation (6-hour cycle)
  - Comprehensive network policies

- Monitoring & Backup
  - Full Prometheus/Grafana stack
  - Automated backup verification
  - Custom metrics for all components
  - R2 storage monitoring

## Directory Structure

```
.
├── ansible/                 # Infrastructure automation
│   ├── inventory/          # Cluster configuration
│   ├── tasks/             # Individual component setup
│   └── templates/         # Configuration templates
├── cluster-setup/          # Kubernetes manifests
│   ├── base/              # Core cluster configuration
│   └── overlays/          # Environment-specific configs
├── docs/                   # Documentation
│   ├── infrastructure/    # Detailed component docs
│   ├── maintenance/       # Operational procedures
│   └── backup/           # Backup configuration
└── helm/                   # Helm charts
    └── client-db/         # Database deployment chart
```

## Hardware Requirements-ish

### Control Plane Nodes (3x)
- CPU: 4 dedicated cores
- Memory: 16GB RAM
- Storage:
  - 2x 1TB NVMe (RAID-1) for OS

### Worker Nodes (3x)
- CPU: 8 dedicated cores
- Memory: 32GB RAM
- Storage:
  - 2x 1TB NVMe (RAID-1) for OS
  - 2x 6.4TB NVMe for Rook-Ceph

## Getting Started

1. Initial Setup
```bash
# Deploy full infrastructure
ansible-playbook site.yml

# Verify deployment
ansible-playbook verify_cluster.yml
```

The deployment will:
- Configure hostnames for all nodes
- Prepare storage drives on worker nodes
  - OS: /dev/nvme0n1, /dev/nvme1n1 (RAID-1)
  - Ceph: /dev/nvme2n1, /dev/nvme3n1 (automatically prepared)
- Set up all required Kubernetes components

2. Database Deployment
```bash
# Deploy new database cluster
helm install <client-name> ./helm/client-db \
  --namespace crx-db \
  --values values.yaml
```

## Documentation Sections

- [Infrastructure Details](infrastructure/README.md)
  - Detailed component architecture
  - Configuration specifications
  - Security implementation

- [Maintenance Procedures](maintenance/PROCEDURES.md)
  - Daily operations
  - Update procedures
  - Troubleshooting guides

- [Backup System](backup/backup-retention.md)
  - Backup configuration
  - Retention policies
  - Recovery procedures

## Security Architecture

### Encryption Layers
1. Network Layer
   - WireGuard encryption for pod traffic
   - TLS for service communication

2. Storage Layer
   - AWS KMS for encryption at rest
   - Encrypted OSD devices
   - Secure backup storage

3. Access Control
   - Namespace isolation
   - Network policies
   - RBAC implementation

### Credential Management
- Automated 6-hour credential rotation
- AWS KMS for key management
- Secure secret distribution

## Monitoring & Alerts

### Core Metrics
- Node health and resource usage
- Storage performance and capacity
- Database health and performance
- Backup status and verification

### Alert Categories
1. Critical
   - Node failures
   - Storage issues
   - Backup failures

2. Warning
   - Resource pressure
   - Performance degradation
   - Certificate expiration

## Support and Maintenance

### Regular Tasks
1. Daily
   - Health checks
   - Backup verification
   - Performance monitoring

2. Weekly
   - Security audit
   - Resource optimization
   - Policy review

3. Monthly
   - Full system audit
   - Recovery testing
   - Performance analysis

### Contact Information
- Infrastructure Team: <EMAIL>
- Emergency Support: +1-xxx-xxx-xxxx
