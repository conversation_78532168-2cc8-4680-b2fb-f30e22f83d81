# Database Clone Helm Chart

This Helm chart creates a clone of an existing CloudNativePG database cluster using volume snapshots. The clone maintains the same configuration as the source database while allowing customization of resources and instance count.

## Prerequisites

- Kubernetes cluster with CloudNativePG operator
- Source database cluster in running state
- Storage class with snapshot support (`ceph-snapshot`)
- Helm 3.x
- Cloudflare R2 credentials

## Quick Start

Use the provided script:
```bash
./scripts/clone-database.sh -s source-db -c clone-name
```

Or manually install using Helm:
```bash
helm install clone-name ./db-clone \
  --namespace crx-db \
  --values values.yaml
```

## Configuration

### Required Values

| Parameter | Description |
|-----------|-------------|
| `source.name` | Name of the source database cluster |
| `clone.name` | Name for the new cloned database |
| `backup.s3.bucket` | R2 bucket name for backups |
| `backup.s3.region` | R2 region |
| `backup.s3.endpoint` | R2 endpoint URL |
| `backup.s3.accessKeyId` | R2 access key |
| `backup.s3.secretAccessKey` | R2 secret key |

### Optional Values

| Parameter | Description | Default |
|-----------|-------------|---------|
| `source.namespace` | Namespace of source database | `crx-db` |
| `clone.namespace` | Namespace for clone | `crx-db` |
| `clone.instances` | Number of instances | `3` |
| `storage.size` | Storage size per instance | `100Gi` |
| `storage.storageClass` | Storage class name | `ceph-block` |
| `storage.snapshotClass` | Snapshot class name | `ceph-snapshot` |

### Resource Configuration

Resources match the client-db defaults:
```yaml
resources:
  requests:
    cpu: "2"
    memory: "8Gi"
  limits:
    cpu: "2"
    memory: "8Gi"
```

## Security

### Network Policies

The chart automatically creates a Cilium network policy that:
- Allows Prometheus access to metrics ports (9187 and 8080)
- Allows connection pooler access to PostgreSQL port (5432)
- Implicitly denies all other traffic

The policy is created with the same name as your clone: `<clone-name>-policy`

To verify the policy:
```bash
kubectl get cnp -n crx-db <clone-name>-policy
```

To describe the policy:
```bash
kubectl describe cnp -n crx-db <clone-name>-policy
```

## Example values.yaml

```yaml
source:
  name: "prod-db"
  namespace: "crx-db"

clone:
  name: "staging-db"
  namespace: "crx-db"
  instances: 2

storage:
  size: "100Gi"
  storageClass: "ceph-block"
  snapshotClass: "ceph-snapshot"

backup:
  s3:
    bucket: ""      # Required: R2 bucket name
    region: ""      # Required: R2 region
    endpoint: ""    # Required: R2 endpoint
    accessKeyId: "" # Required: R2 access key
    secretAccessKey: "" # Required: R2 secret key
```

## Usage

### Monitor Clone Progress

Watch the cluster status:
```bash
kubectl get cluster -n crx-db <clone-name> -w
```

Check clone logs:
```bash
kubectl logs -n crx-db <clone-name>-1 -c postgres
```

### Access Clone

Get superuser password:
```bash
kubectl get secret <clone-name>-superuser -n crx-db \
  -o jsonpath='{.data.password}' | base64 -d
```

Connect to database:
```bash
kubectl exec -it <clone-name>-1 -n crx-db -- psql
```

### Connection Pooler Access

Read-Write endpoint:
```bash
kubectl get svc <clone-name>-rw-pooler -n crx-db
```

Read-Only endpoint:
```bash
kubectl get svc <clone-name>-ro-pooler -n crx-db
```

## Uninstallation

```bash
helm uninstall <clone-name> -n crx-db
```

Note: PVCs and snapshots are retained after uninstallation.

## Architecture

The clone process:
1. Creates a volume snapshot of the source database
2. Initializes new cluster using the snapshot
3. Configures the same PostgreSQL parameters as source
4. Sets up connection poolers and monitoring

## Backup Configuration

The cloned database inherits the same backup infrastructure as the source but maintains its own separate backup history and retention policy.

### Retention Policy
Configurable backup retention with multiple backup types:

| Type     | Default Count | Default Age | Purpose |
|----------|--------------|-------------|---------|
| Hourly   | 24          | 24h        | Fine-grained recovery within last day |
| Daily    | 7           | 7d         | Recent point-in-time recovery |
| Weekly   | 4           | 28d        | Medium-term recovery points |
| Monthly  | 12          | 365d       | Long-term recovery points |
| Yearly   | 2           | 730d       | Compliance and archival |

Configure in `values.yaml`:
```yaml
backup:
  retentionPolicy:
    hourly:
      count: 24
      age: "24h"
    daily:
      count: 7
      age: "7d"
    # ... etc
```

### Backup Schedule
Two types of backups are supported:
- Full backup: Daily at midnight by default
- Incremental backup: Hourly by default

Configure schedules in `values.yaml`:
```yaml
backup:
  schedule:
    full: "0 0 * * *"     # Daily at midnight
    incremental: "0 * * * *"  # Every hour
```

### Important Notes
- The clone maintains its own separate backup history
- Backups are stored in a separate path within the R2 bucket
- Backup verification runs independently from the source database
- Monitoring and alerts are configured separately for the clone

## Limitations

- Source database must be in the same namespace
- Storage class must support snapshots
- Clone inherits source database configuration
