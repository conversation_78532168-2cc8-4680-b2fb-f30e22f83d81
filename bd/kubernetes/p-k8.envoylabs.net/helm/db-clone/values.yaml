# Source database configuration
source:
  name: ""  # Required: source cluster name
  namespace: "crx-db"

# Clone configuration
clone:
  name: ""  # Required: new cluster name
  namespace: "crx-db"
  instances: 3  # Match source cluster default

# Storage configuration
storage:
  size: 100Gi
  storageClass: "ceph-block"
  snapshotClass: "ceph-snapshot"

# Backup configuration
backup:
  retentionPolicy:
    hourly:
      count: 24
      age: "24h"
    daily:
      count: 7
      age: "7d"
    weekly:
      count: 4
      age: "28d"
    monthly:
      count: 12
      age: "365d"
    yearly:
      count: 2
      age: "730d"
  schedule:
    full: "0 0 * * *"     # Daily at midnight
    incremental: "0 * * * *"  # Every hour
  s3:
    bucket: ""  # Required: backup bucket name
    region: ""  # Required: R2 region
    endpoint: "" # Required: R2 endpoint
    accessKeyId: "" # Required: R2 access key
    secretAccessKey: "" # Required: R2 secret key
  verification:
    enabled: true
    schedule: "0 1 * * *"  # Run at 1 AM daily
    retention: "7d"
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "1"
        memory: "2Gi"

# Resource configuration matches client-db
resources:
  requests:
    cpu: "2"
    memory: "8Gi"
  limits:
    cpu: "2"
    memory: "8Gi"

# PostgreSQL configuration matches client-db
postgresql:
  parameters:
    shared_buffers: "2GB"
    max_connections: "500"
    work_mem: "4MB"
    maintenance_work_mem: "256MB"
    effective_cache_size: "6GB"
    random_page_cost: "1.1"
    checkpoint_completion_target: "0.9"
    wal_buffers: "16MB"
    default_statistics_target: "100"

# Connection pooler configuration
pooler:
  enabled: true
  instances: 2
  mode: "session"
  resources:
    requests:
      cpu: "500m"
      memory: "1Gi"
    limits:
      cpu: "1"
      memory: "2Gi"

# Monitoring configuration
monitoring:
  enabled: true
