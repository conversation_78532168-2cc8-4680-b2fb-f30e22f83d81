{{- if .Values.pooler.enabled }}
apiVersion: postgresql.cnpg.io/v1
kind: Pooler
metadata:
  name: {{ .Values.clone.name }}-rw
  namespace: {{ .Values.clone.namespace }}
spec:
  cluster:
    name: {{ .Values.clone.name }}
  instances: {{ .Values.pooler.instances }}
  type: rw
  pgbouncer:
    poolMode: {{ .Values.pooler.mode }}
    parameters:
      max_client_conn: "1000"
      default_pool_size: "50"
  resources:
    requests:
      cpu: {{ .Values.pooler.resources.requests.cpu }}
      memory: {{ .Values.pooler.resources.requests.memory }}
    limits:
      cpu: {{ .Values.pooler.resources.limits.cpu }}
      memory: {{ .Values.pooler.resources.limits.memory }}
---
apiVersion: postgresql.cnpg.io/v1
kind: Pooler
metadata:
  name: {{ .Values.clone.name }}-ro
  namespace: {{ .Values.clone.namespace }}
spec:
  cluster:
    name: {{ .Values.clone.name }}
  instances: {{ .Values.pooler.instances }}
  type: ro
  pgbouncer:
    poolMode: {{ .Values.pooler.mode }}
    parameters:
      max_client_conn: "1000"
      default_pool_size: "50"
  resources:
    requests:
      cpu: {{ .Values.pooler.resources.requests.cpu }}
      memory: {{ .Values.pooler.resources.requests.memory }}
    limits:
      cpu: {{ .Values.pooler.resources.limits.cpu }}
      memory: {{ .Values.pooler.resources.limits.memory }}
{{- end }}