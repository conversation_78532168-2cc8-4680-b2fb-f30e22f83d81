apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: {{ .Values.clone.name }}-policy
  namespace: {{ .Values.clone.namespace }}
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/instance: {{ .Values.clone.name }}
spec:
  description: "Allow monitoring and pooler access to PostgreSQL cluster {{ .Values.clone.name }}"
  endpointSelector:
    matchLabels:
      cnpg.io/cluster: {{ .Values.clone.name }}
  ingress:
    - fromEndpoints:
        - matchLabels:
            app.kubernetes.io/name: prometheus
            app.kubernetes.io/component: monitoring
            io.kubernetes.pod.namespace: monitoring
      toPorts:
        - ports:
            - port: "9187"
              protocol: TCP
              name: "metrics"
            - port: "8080"
              protocol: TCP
              name: "cnpg-metrics"
    
    - fromEndpoints:
        - matchLabels:
            cnpg.io/pooler: {{ .Values.clone.name }}
            cnpg.io/cluster: {{ .Values.clone.name }}
            io.kubernetes.pod.namespace: {{ .Values.clone.namespace }}
      toPorts:
        - ports:
            - port: "5432"
              protocol: TCP
              name: "postgresql"
  egress:
    - toFQDNs:
        - matchPattern: "*.r2.cloudflarestorage.com"
      toPorts:
        - ports:
            - port: "443"
              protocol: TCP
