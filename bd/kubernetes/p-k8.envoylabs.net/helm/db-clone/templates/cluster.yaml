apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: {{ .Values.clone.name }}
  namespace: {{ .Values.clone.namespace }}
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/instance: {{ .Values.clone.name }}
spec:
  instances: {{ .Values.clone.instances }}

  bootstrap:
    recovery:
      source: {{ .Values.source.name }}
      snapshot:
        name: {{ .Values.clone.name }}-snapshot

  storage:
    size: {{ .Values.storage.size }}
    storageClass: {{ .Values.storage.storageClass }}
    
  resources:
    requests:
      cpu: {{ .Values.resources.requests.cpu }}
      memory: {{ .Values.resources.requests.memory }}
    limits:
      cpu: {{ .Values.resources.limits.cpu }}
      memory: {{ .Values.resources.limits.memory }}

  backup:
    retentionPolicy: |
      hourly={{ .Values.backup.retentionPolicy.hourly.count }}:{{ .Values.backup.retentionPolicy.hourly.age }}
      daily={{ .Values.backup.retentionPolicy.daily.count }}:{{ .Values.backup.retentionPolicy.daily.age }}
      weekly={{ .Values.backup.retentionPolicy.weekly.count }}:{{ .Values.backup.retentionPolicy.weekly.age }}
      monthly={{ .Values.backup.retentionPolicy.monthly.count }}:{{ .Values.backup.retentionPolicy.monthly.age }}
      yearly={{ .Values.backup.retentionPolicy.yearly.count }}:{{ .Values.backup.retentionPolicy.yearly.age }}
    barmanObjectStore:
      destinationPath: "s3://{{ .Values.backup.s3.bucket }}/{{ .Values.clone.name }}"
      endpointURL: {{ .Values.backup.s3.endpoint }}
      s3Credentials:
        accessKeyId:
          name: {{ .Values.clone.name }}-backup-creds
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: {{ .Values.clone.name }}-backup-creds
          key: SECRET_ACCESS_KEY
      wal:
        compression: bzip2
        maxParallel: 8
    
    scheduledBackup:
      enabled: true
      immediate: true
      schedule:
        - name: "full"
          schedule: {{ .Values.backup.schedule.full | quote }}
        - name: "incremental"
          schedule: {{ .Values.backup.schedule.incremental | quote }}

  postgresql:
    parameters:
      {{- range $key, $value := .Values.postgresql.parameters }}
      {{ $key }}: {{ $value | quote }}
      {{- end }}
