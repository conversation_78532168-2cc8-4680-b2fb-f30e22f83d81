apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ .Values.clone.name }}-backup-alerts
  namespace: {{ .Values.clone.namespace }}
spec:
  groups:
  - name: backup.rules
    rules:
    - alert: DatabaseBackupMissing
      expr: time() - max(cnpg_backup_latest_timestamp{cluster="{{ .Values.clone.name }}"}) > 86400
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: "Database backup is missing for {{ .Values.clone.name }}"
        description: "No successful backup in the last 24 hours"

    - alert: BackupVerificationFailed
      expr: cnpg_backup_verification_status{cluster="{{ .Values.clone.name }}"} != 1
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: "Backup verification failed for {{ .Values.clone.name }}"
        description: "The latest backup verification attempt failed"