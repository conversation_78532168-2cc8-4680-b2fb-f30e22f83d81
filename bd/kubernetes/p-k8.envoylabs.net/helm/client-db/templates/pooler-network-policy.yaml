{{- if .Values.pooler.enabled }}
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: {{ .Values.client.name }}-pooler-policy
  namespace: {{ .Values.client.namespace }}
  labels:
    app.kubernetes.io/name: postgres-pooler
    app.kubernetes.io/instance: {{ .Values.client.name }}
spec:
  description: "Allow external LoadBalancer access to PostgreSQL pooler for {{ .Values.client.name }}"
  endpointSelector:
    matchLabels:
      cnpg.io/pooler: {{ .Values.client.name }}
  ingress:
    # Allow access from worker nodes for NodePort services
    - fromEntities:
        - host
      toPorts:
        - ports:
            # These ports will be populated by the NodePort assigned to the pooler services
            - port: "5432"
              protocol: TCP
              name: "postgresql"

  egress:
    # Allow access to PostgreSQL cluster
    - toEndpoints:
        - matchLabels:
            cnpg.io/cluster: {{ .Values.client.name }}
      toPorts:
        - ports:
            - port: "5432"
              protocol: TCP
              name: "postgresql"

    # Allow DNS resolution
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: kube-system
            k8s-app: kube-dns
      toPorts:
        - ports:
            - port: "53"
              protocol: UDP
            - port: "53"
              protocol: TCP
{{- end }}