apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: {{ .Values.client.name }}
  namespace: {{ .Values.client.namespace }}
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/instance: {{ .Values.client.name }}
spec:
  instances: {{ .Values.cluster.instances }}
  
  storage:
    size: {{ .Values.cluster.storage.size }}
    storageClass: {{ .Values.cluster.storage.storageClass }}
    snapshotClass: ceph-snapshot  # Reference existing snapshot class
  
  resources:
    requests:
      cpu: {{ .Values.cluster.resources.requests.cpu }}
      memory: {{ .Values.cluster.resources.requests.memory }}
    limits:
      cpu: {{ .Values.cluster.resources.limits.cpu }}
      memory: {{ .Values.cluster.resources.limits.memory }}

  backup:
    retentionPolicy: |
      hourly={{ .Values.backup.retentionPolicy.hourly.count }}:{{ .Values.backup.retentionPolicy.hourly.age }}
      daily={{ .Values.backup.retentionPolicy.daily.count }}:{{ .Values.backup.retentionPolicy.daily.age }}
      weekly={{ .Values.backup.retentionPolicy.weekly.count }}:{{ .Values.backup.retentionPolicy.weekly.age }}
      monthly={{ .Values.backup.retentionPolicy.monthly.count }}:{{ .Values.backup.retentionPolicy.monthly.age }}
      yearly={{ .Values.backup.retentionPolicy.yearly.count }}:{{ .Values.backup.retentionPolicy.yearly.age }}
    barmanObjectStore:
      destinationPath: "s3://{{ .Values.backup.s3.bucket }}/{{ .Values.client.name }}"
      endpointURL: {{ .Values.backup.s3.endpoint }}
      s3Credentials:
        accessKeyId:
          name: {{ .Values.client.name }}-backup-creds
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: {{ .Values.client.name }}-backup-creds
          key: SECRET_ACCESS_KEY
      wal:
        compression: bzip2
        maxParallel: 8
    
  scheduledBackup:
    enabled: true
    immediate: true
    schedule:
      - name: "full"
        schedule: {{ .Values.backup.schedule.full | quote }}
      - name: "incremental"
        schedule: {{ .Values.backup.schedule.incremental | quote }}

  monitoring:
    enablePodMonitor: {{ .Values.monitoring.enabled }}

  postgresql:
    parameters:
      shared_buffers: "2GB"
      max_connections: "500"
      work_mem: "4MB"
      maintenance_work_mem: "256MB"
      effective_cache_size: "6GB"
      random_page_cost: "1.1"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
