apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ .Values.client.name }}-database-alerts
spec:
  groups:
  - name: database.rules
    rules:
    # Add connection pooler alerts
    - alert: ConnectionPoolerSaturated
      expr: |
        avg_over_time(pgbouncer_pools_server_active_connections{database!="pgbouncer"}[5m]) 
        / 
        pgbouncer_pools_server_maximum_connections{database!="pgbouncer"} 
        > 0.8
      for: 15m
      labels:
        severity: warning
    # Add replication lag alert
    - alert: ReplicationLagHigh
      expr: pg_replication_lag_bytes > 1073741824  # 1GB
      for: 10m
      labels:
        severity: warning
