{{- if .Values.pooler.enabled }}
apiVersion: postgresql.cnpg.io/v1
kind: Pooler
metadata:
  name: {{ .Values.client.name }}-rw
  namespace: {{ .Values.client.namespace }}
spec:
  cluster:
    name: {{ .Values.client.name }}
  instances: {{ .Values.pooler.instances }}
  type: {{ .Values.pooler.mode }}
  mode: "rw"
  pgbouncer:
    poolMode: "session"
    parameters:
      max_client_conn: "1000"
      default_pool_size: "50"
  resources:
    requests:
      cpu: {{ .Values.pooler.resources.requests.cpu }}
      memory: {{ .Values.pooler.resources.requests.memory }}
    limits:
      cpu: {{ .Values.pooler.resources.limits.cpu }}
      memory: {{ .Values.pooler.resources.limits.memory }}
---
apiVersion: postgresql.cnpg.io/v1
kind: Pooler
metadata:
  name: {{ .Values.client.name }}-ro
  namespace: {{ .Values.client.namespace }}
spec:
  cluster:
    name: {{ .Values.client.name }}
  instances: {{ .Values.pooler.instances }}
  type: {{ .Values.pooler.mode }}
  mode: "ro"
  pgbouncer:
    poolMode: "session"
    parameters:
      max_client_conn: "1000"
      default_pool_size: "50"
  resources:
    requests:
      cpu: {{ .Values.pooler.resources.requests.cpu }}
      memory: {{ .Values.pooler.resources.requests.memory }}
    limits:
      cpu: {{ .Values.pooler.resources.limits.cpu }}
      memory: {{ .Values.pooler.resources.limits.memory }}
{{- end }}