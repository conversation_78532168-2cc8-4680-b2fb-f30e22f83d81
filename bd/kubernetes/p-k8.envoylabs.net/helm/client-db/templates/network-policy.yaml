apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: {{ .Values.client.name }}-policy
  namespace: {{ .Values.client.namespace }}
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/instance: {{ .Values.client.name }}
spec:
  description: "Allow monitoring and pooler access to PostgreSQL cluster {{ .Values.client.name }}"
  endpointSelector:
    matchLabels:
      cnpg.io/cluster: {{ .Values.client.name }}
  ingress:
    - fromEndpoints:
        - matchLabels:
            app.kubernetes.io/name: prometheus
            app.kubernetes.io/component: monitoring
            io.kubernetes.pod.namespace: monitoring
      toPorts:
        - ports:
            - port: "9187"
              protocol: TCP
              name: "metrics"
            - port: "8080"
              protocol: TCP
              name: "cnpg-metrics"
    
    - fromEndpoints:
        - matchLabels:
            cnpg.io/pooler: {{ .Values.client.name }}
            cnpg.io/cluster: {{ .Values.client.name }}
            io.kubernetes.pod.namespace: {{ .Values.client.namespace }}
      toPorts:
        - ports:
            - port: "5432"
              protocol: TCP
              name: "postgresql"
  egress:
    - toFQDNs:
        - matchPattern: "*.r2.cloudflarestorage.com"
      toPorts:
        - ports:
            - port: "443"
              protocol: TCP
