# Client Database Helm Chart

This Helm chart deploys a highly available PostgreSQL cluster using CloudNativePG operator with the following features:
- Automated backups to Cloudflare R2
- Connection pooling (read-write and read-only endpoints)
- Monitoring integration
- Network policies
- Resource management

## Prerequisites

- Kubernetes cluster with CloudNativePG operator installed
- Helm 3.x
- Cloudflare R2 credentials
- Storage class `ceph-block` available

## Installation

1. Create values file for your client:

```yaml
client:
  name: "client1"  # Required: unique client identifier

backup:
  s3:
    bucket: "your-backup-bucket"
    region: "auto"
    endpoint: "https://xxx.r2.cloudflarestorage.com"
    accessKeyId: "your-access-key"
    secretAccessKey: "your-secret-key"
```

2. Install the chart:

```bash
helm install <release-name> ./client-db \
  --namespace crx-db \
  --values values.yaml
```

## Configuration

### Required Values

| Parameter | Description |
|-----------|-------------|
| `client.name` | Client identifier (used for cluster name) |
| `backup.s3.bucket` | R2 bucket name for backups |
| `backup.s3.region` | R2 region |
| `backup.s3.endpoint` | R2 endpoint URL |
| `backup.s3.accessKeyId` | R2 access key |
| `backup.s3.secretAccessKey` | R2 secret key |

### Optional Values

| Parameter | Description | Default |
|-----------|-------------|---------|
| `cluster.instances` | Number of PostgreSQL instances | `3` |
| `cluster.storage.size` | Storage size per instance | `100Gi` |
| `pooler.enabled` | Enable connection pooling | `true` |
| `pooler.instances` | Number of pooler instances | `2` |
| `monitoring.enabled` | Enable Prometheus monitoring | `true` |

## Backup Configuration

### Retention Policy
The backup retention policy supports multiple backup types with different retention periods:

| Type     | Default Count | Default Age | Purpose |
|----------|--------------|-------------|---------|
| Hourly   | 24          | 24h        | Fine-grained recovery within last day |
| Daily    | 7           | 7d         | Recent point-in-time recovery |
| Weekly   | 4           | 28d        | Medium-term recovery points |
| Monthly  | 12          | 365d       | Long-term recovery points |
| Yearly   | 2           | 730d       | Compliance and archival |

Configure retention in `values.yaml`:
```yaml
backup:
  retentionPolicy:
    hourly:
      count: 24
      age: "24h"
    daily:
      count: 7
      age: "7d"
    # ... etc
```

### Backup Schedule
Two types of backups are configured:
- Full backup: Daily at midnight by default
- Incremental backup: Hourly by default

Configure schedules in `values.yaml`:
```yaml
backup:
  schedule:
    full: "0 0 * * *"     # Daily at midnight
    incremental: "0 * * * *"  # Every hour
```

## Access Credentials

Get the superuser password:
```bash
kubectl get secret <client-name>-superuser -n crx-db \
  -o jsonpath='{.data.password}' | base64 -d
```

## Connection Details

Read-Write connection:
```bash
kubectl get svc <client-name>-rw-pooler -n crx-db
```

Read-Only connection:
```bash
kubectl get svc <client-name>-ro-pooler -n crx-db
```

## Backup Management

Backups are scheduled daily by default. To trigger a manual backup:
```bash
kubectl cnpg backup <client-name> -n crx-db
```

## Monitoring

When enabled, the following metrics are available:
- PostgreSQL metrics on port 9187
- CloudNativePG metrics on port 8080

Access metrics through Prometheus in the monitoring namespace.

## Network Policies

The chart creates two Cilium network policies:

1. Database Policy (`<name>-policy`):
   - Allows monitoring system access
   - Allows pooler connections
   - Enables cluster replication
   - Permits backup operations

2. Pooler Policy (`<name>-pooler-policy`):
   - Allows external access through NodePorts
   - Restricts pooler to database communication
   - Enables DNS resolution

Both policies follow the principle of least privilege, only allowing necessary traffic flows.

## Resource Requirements

Default resource allocation per component:

PostgreSQL instances:
- Requests: 2 CPU, 8Gi memory
- Limits: 2 CPU, 8Gi memory

Connection poolers:
- Requests: 500m CPU, 1Gi memory
- Limits: 1 CPU, 2Gi memory

## Uninstallation

```bash
helm uninstall <release-name> -n crx-db
```

Note: PVCs and backups are retained after uninstallation.
