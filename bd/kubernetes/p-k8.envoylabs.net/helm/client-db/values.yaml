# Client configuration
client:
  name: ""  # Required: client name
  namespace: "crx-db"

# Database cluster configuration
cluster:
  instances: 3
  storage:
    size: 100Gi
    storageClass: "ceph-block"
  
  resources:
    requests:
      cpu: "2"
      memory: "8Gi"
    limits:
      cpu: "2"
      memory: "8Gi"
  bootstrap:
    initdb:
      database: "hb"
      owner: "hb"
      dataChecksums: true
      postInitSQL:
       - ALTER USER "hb" WITH CREATEDB SUPERUSER REPLICATION BYPASSRLS;

# Backup configuration
backup:
  compression: "bzip2"
  encryption: true
  retention:
    hourly: 24
    daily: 7
    weekly: 4
    monthly: 12
  schedule:
    full: "0 0 * * *"     # Daily at midnight
    incremental: "0 * * * *"  # Every hour
  s3:
    bucket: ""  # Required: backup bucket name
    region: ""  # Required: R2 region
    endpoint: "" # Required: R2 endpoint
    accessKeyId: "" # Required: R2 access key
    secretAccessKey: "" # Required: R2 secret key
  verification:
    enabled: true
    schedule: "0 3 * * *"  # Daily at 3am
    resources:
      requests:
        cpu: "1"
        memory: "2Gi"
      limits:
        cpu: "2"
        memory: "4Gi"
    retention: "7d"  # Keep verification job history for 7 days

# Connection pooler configuration
pooler:
  enabled: true
  instances: 2
  mode: "session"
  resources:
    requests:
      cpu: "500m"
      memory: "1Gi"
    limits:
      cpu: "1"
      memory: "2Gi"

monitoring:
  enabled: true
