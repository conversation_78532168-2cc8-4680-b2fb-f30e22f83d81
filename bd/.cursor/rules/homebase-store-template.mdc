---
description: Template for new Zustand store files in homebase
globs: **/homebase/src/core/store/**/*.ts
---
# Zustand Store File Template

Generates a basic template for new Zustand store files within the `homebase/src/core/store` directory.

<rule>
name: homebase_zustand_store_template
description: Provides a template for new Zustand store files
filters:
  # Match TypeScript files in the homebase/src/core/store directory and subdirectories
  - type: file_path
    pattern: "homebase/src/core/store/.*\\.ts$"
  # Match file creation events
  - type: event
    pattern: "file_create"
    
actions:
  - type: suggest
    message: |
      ```typescript
      import { create } from "zustand";

      /**
       * Define the state structure for the ${StoreName} store
       */
      interface ${StoreName}State {
        // State properties here
        exampleProperty: string;
      }

      /**
       * Define the actions and state for the ${StoreName} store
       */
      interface ${StoreName}Store extends ${StoreName}State {
        // Action functions here
        setExampleProperty: (value: string) => void;
      }

      /**
       * Initial state for the ${StoreName} store
       * @returns Initial state object
       */
      const create${StoreName}State = (): ${StoreName}State => ({
        exampleProperty: "",
      });

      /**
       * Zustand store for ${StoreName} data and actions
       */
      export const use${StoreName}Store = create<${StoreName}Store>((set) => ({
        ...create${StoreName}State(),
        setExampleProperty: (value) => set({ exampleProperty: value }),
      }));

      export default use${StoreName}Store;
      ```

      To use this template:

      1.  Create a new `.ts` file in the `homebase/src/core/store` directory or a subdirectory.
      2.  Name the file appropriately (e.g., `myNewStore.ts`).
      3.  Replace `${StoreName}` with the name of your store (e.g., `MyNewStore`).
      4.  Define your state properties in `${StoreName}State` interface.
      5.  Define your action functions in `${StoreName}Store` interface.
      6.  Implement the initial state in `create${StoreName}State`.
      7.  Implement the action functions within the `create` block using `set`.

examples:
  - input: "Creating a new file: homebase/src/core/store/myNewStore.ts"
    output: |
      ```typescript
      import { create } from "zustand";

      /**
       * Define the state structure for the MyNewStore store
       */
      interface MyNewStoreState {
        // State properties here
        exampleProperty: string;
      }

      /**
       * Define the actions and state for the MyNewStore store
       */
      interface MyNewStoreStore extends MyNewStoreState {
        // Action functions here
        setExampleProperty: (value: string) => void;
      }

      /**
       * Initial state for the MyNewStore store
       * @returns Initial state object
       */
      const createMyNewStoreState = (): MyNewStoreState => ({
        exampleProperty: "",
      });

      /**
       * Zustand store for MyNewStore data and actions
       */
      export const useMyNewStoreStore = create<MyNewStoreStore>((set) => ({
        ...createMyNewStoreState(),
        setExampleProperty: (value) => set({ exampleProperty: value }),
      }));

      export default useMyNewStoreStore;
      ```
      "Suggested template for a new Zustand store file"

meta
  priority: low
  version: 1.0
</rule>