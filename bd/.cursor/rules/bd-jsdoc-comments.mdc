---
description: JSDoc Documentation Standards
globs: **/*.{ts,tsx}
---
# TypeScript Documentation Standards

Enforce comprehensive JSDoc documentation for all TypeScript/React components and functions.

<rule>
name: jsdoc_standards
description: Standards for JSDoc documentation in TypeScript files
filters:
  # Match TypeScript files in repo
  - type: file_path
    pattern: ".*\\.(ts|tsx)$"
  # Match exported items and function declarations
  - type: content
    pattern: "(export|function|interface|type|class)\\s+\\w+"

actions:
  - type: suggest
    message: |
      All TypeScript declarations should have comprehensive JSDoc documentation that explains:
      
      1. Purpose and intention of the code
      2. Expected behavior and side effects
      3. Explicit parameter and return types
      4. Examples where appropriate

      Exclude: index.ts files that only have export * statements
      
      Documentation format:

      ```typescript
      /**
       * Brief description of purpose/intention
       * 
       * Detailed explanation of behavior if needed
       * 
       * @param paramName - Description of parameter purpose/requirements
       * @param {SpecificType} paramName - Use type when not obvious from TS
       * @returns Description of return value and format
       * @throws Description of potential errors
       * @example
       * const result = someFunction('input');
       * // => expected output
       */
      ```

      For React components:
      ```typescript
      /**
       * Brief description of component's purpose
       * 
       * Detailed explanation of component behavior/usage
       * 
       * @param props - Component props
       * @param {Type} props.specificProp - Description of prop purpose
       * @returns JSX component
       * @example
       * <MyComponent
       *   specificProp="value"
       * />
       */
      ```

      For types/interfaces:
      ```typescript
      /**
       * Description of what this type represents
       * 
       * @property propName - Description of property purpose
       * @property {SpecificType} propName - Use type when not obvious
       */
      ```

examples:
  - input: |
      // Bad: Missing or insufficient documentation
      export function calculateTotal(items: CartItem[]): number {
        return items.reduce((sum, item) => sum + item.price, 0);
      }

      // Good: Comprehensive documentation
      /**
       * Calculates the total price of all items in the shopping cart
       * 
       * Sums the individual prices of each cart item, applying any
       * per-item discounts before summing
       * 
       * @param items - Array of cart items to total
       * @returns Total price in cents
       * @throws {InvalidPriceError} If any item has a negative price
       * @example
       * const total = calculateTotal([
       *   { id: 1, price: 1000 }, // $10.00
       *   { id: 2, price: 2000 }  // $20.00
       * ]);
       * // => 3000 ($30.00)
       */
      export function calculateTotal(items: CartItem[]): number {
        return items.reduce((sum, item) => sum + item.price, 0);
      }

      // Bad: Vague component documentation
      const UserCard = ({ user }: Props) => {
        return <div>{user.name}</div>;
      }

      // Good: Clear component documentation
      /**
       * Displays user information in a card format with avatar
       * 
       * Handles both authenticated and anonymous users, showing
       * appropriate placeholder content for anonymous users
       * 
       * @param props - Component properties
       * @param {User} props.user - User object containing profile data
       * @param {boolean} [props.showAvatar=true] - Whether to display user avatar
       * @returns Card component displaying user information
       * @example
       * <UserCard
       *   user={{ id: 1, name: "John Doe", avatar: "/avatar.jpg" }}
       *   showAvatar={true}
       * />
       */
      const UserCard = ({ user, showAvatar = true }: Props) => {
        return <div>{user.name}</div>;
      }

metadata:
  priority: high
  version: 1.0
</rule>