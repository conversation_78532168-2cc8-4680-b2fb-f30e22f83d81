---
description: Standards for creating mock CSON files for DSL validation testing
globs: bd/cli/__mocks__/dsl/**/*.cson
---
# CLI DSL Mocks

Standards for creating mock CSON files for testing DSL validation functions.

<rule>
name: cli_dsl_mocks
description: Provides guidance for creating mock CSON files for DSL validation testing
filters:
	# Match CSON files in the mocks directory
	- type: file_path
		pattern: "bd/cli/__mocks__/dsl/.*\\.cson$"
	# Match file creation events
	- type: event
		pattern: "file_create"

actions:
	- type: suggest
		message: |
			For DSL validation testing, create the following structure:

			1. Create a subfolder named after the validation function being tested
			2. For multiple test cases, append a dash and number (e.g., `validate_model_source-1`)
			3. Include these files in each test case:
				 - `base/cson/form1.cson` - Contains an error condition for the validation function
				 - `base/cson/form2.cson` - Shows proper usage without errors
				 - `base/cson/form3.cson` - Contains an error condition that will be fixed in the custom version
				 - `cust/cson/form3.cson` - Resolves the error by overriding parts of the base form3.cson

            When creating the CSONs, follow these guidelines:

			1. SELF-CONTAINED: Each set of mock CSON files must be self-contained and should not reference any external forms, tables, or sources outside of form1, form2, and form3. Use only simple, self-contained examples that demonstrate the validation function being tested. Do not create any new CSON files outside of the base and cust folders and the only valid names are form1, form2, and form3.

			2. TABS: Use tabs instead of spaces for indenting the CSON.

			3. MINIMAL CSON: Only include properties that are necessary for testing the specific validation/warning/transform rule. For example, if `field.active` or `field.required` is not needed for the test, do not include it in the mock CSON. Same with `view.label`.

			4. COMMENTING: Place all comments at the end of the line they describe, not on a separate line above. For example:
			```cson
			source: 'invalid_source' # Error: This will fail validation
			```
			instead of:
			```cson
			# Error: This will fail validation
			source: 'invalid_source'
			```

			Example structure for a mock CSON file:

			```cson
			fields:
				field_name:
					model:
						source: 'valid_source' # Include ONLY model properties needed for the test
					view:
						label: 'Field Label' # Include ONLY view properties needed for the test

			model:
				name: '{field_name}' # Include ONLY model properties needed for the test

			view:
				label: 'Form Label' # Include ONLY view properties needed for the test
			```

			Refer to `bd/jsshared/src/coffee/models/dsl/dsl-defaults.coffee` for the base DSL specification and defaults.

examples:
	- input: "Creating a new file: bd/cli/__mocks__/dsl/validate_model_source-1/base/cson/form1.cson"
		output: |
			```cson
			fields:
				code:
					model:
						source: 'invalid_source' # Error: Invalid source that will fail validation
					view:
						label: 'Code'

			model:
				name: '{code}'
			```

	- input: "Creating a new file: bd/cli/__mocks__/dsl/validate_model_source-1/base/cson/form2.cson"
		output: |
			```cson
			fields:
				code:
					model:
						source: 'form1' # Correct: Valid source that will pass validation (self-contained reference)
					view:
						label: 'Code'

			model:
				name: '{code}'
			```
</rule>
