---
description: Prefer using mantine core components
globs: **/homebase/**
---
# React Component Preferences

Prefer using Mantine core components and styles for all React components in the homebase project.

<rule>
name: prefer_mantine_components
description: Standards for using Mantine components and styles in React files
filters:
  # Match React files in homebase directory
  - type: file_path
    pattern: "homebase/.*\\.(tsx|jsx)$"
  # Match component imports
  - type: content
    pattern: "import.*from\\s+'[^']*'"

actions:
  - type: suggest
    conditions:
      # Look for non-Mantine UI component imports
      - pattern: "import.*from\\s+'(@mui|tailwindcss|@chakra-ui|@material-ui|react-bootstrap).*'"
        message: |
          Please use Mantine core components instead of other UI libraries.

          We should:
          1. Use components from @mantine/core
          2. Use styles from @mantine/core
          3. Avoid introducing new UI library dependencies

          Example conversion:
          ```typescript
          // Instead of:
          import { Button } from '@mui/material';

          // Prefer:
          import { Button } from '@mantine/core';
          ```

examples:
  - input: |
      // Bad: Using non-Mantine components
      import { Button } from '@mui/material';
      import { Box } from '@chakra-ui/react';
      import 'tailwindcss/tailwind.css';

      // Good: Using Mantine components
      import { Button, Box } from '@mantine/core';
      import { createStyles } from '@mantine/core';
    output: "Correctly using Mantine components and styles"

  - input: |
      // Bad: Mixing UI libraries
      import { Button } from '@mantine/core';
      import { Dialog } from '@mui/material';

      // Good: Consistent Mantine usage
      import { Button, Modal } from '@mantine/core';
    output: "Maintaining consistency with Mantine components"

metadata:
  priority: high
  version: 1.0