---
description: Template for new Mantine UI components in homebase
globs: **/homebase/src/components/**/*.tsx
---
# Mantine UI Component Template

Generates a template for new Mantine UI components within the `homebase/src/components` directory.

<rule>
name: homebase_mantine_component_template
description: Provides a template for new Mantine UI components
filters:
  # Match TypeScript React files in the homebase/src/components directory and subdirectories
  - type: file_path
    pattern: "homebase/src/components/.*\\.tsx$"
  # Match file creation events
  - type: event
    pattern: "file_create"
    
actions:
  - type: suggest
    message: |
      ```typescript
      import { createStyles } from "@mantine/core";
      import type { ComponentPropsWithoutRef } from "react";

      /**
       * Props for the ${ComponentName} component
       */
      type ${ComponentName}Props = ComponentPropsWithoutRef<"div"> & {
        // Add custom props here
      };

      /**
       * Styles for the ${ComponentName} component
       */
      const use${ComponentName}Styles = createStyles((theme) => ({
        root: {
          // Base styles
        },

        // Add additional style classes here
      }));

      /**
       * ${ComponentName} component
       */
      export function ${ComponentName}({ className, ...props }: ${ComponentName}Props) {
        const { classes, cx } = use${ComponentName}Styles();

        return (
          <div className={cx(classes.root, className)} {...props}>
            {/* Component content */}
          </div>
        );
      }

      export default ${ComponentName};
      ```

      To use this template:

      1. Create a new `.tsx` file in `homebase/src/components` or a subdirectory
      2. Name the file appropriately (e.g., `MyComponent.tsx`)
      3. Replace `${ComponentName}` with your component name
      4. Define your props in `${ComponentName}Props`
      5. Add styles using `use${ComponentName}Styles`
      6. Implement your component logic
      7. Create a matching `.module.scss` file if needed

      Remember:
      - Use Mantine components from @mantine/core
      - Leverage Mantine's theme and style system
      - Follow TypeScript best practices
      - Keep components focused and single-purpose

examples:
  - input: "Creating a new file: homebase/src/components/cards/InfoCard.tsx"
    output: |
      ```typescript
      import { createStyles, Paper, Text, Title } from "@mantine/core";
      import type { ComponentPropsWithoutRef } from "react";

      type InfoCardProps = ComponentPropsWithoutRef<typeof Paper> & {
        title: string;
        content: string;
      };

      const useInfoCardStyles = createStyles((theme) => ({
        root: {
          padding: theme.spacing.md,
        },
        title: {
          marginBottom: theme.spacing.sm,
          color: theme.colorScheme === "dark" ? theme.white : theme.black,
        },
        content: {
          color: theme.colorScheme === "dark" 
            ? theme.colors.dark[0] 
            : theme.colors.gray[7],
        },
      }));

      export function InfoCard({ 
        title, 
        content, 
        className, 
        ...props 
      }: InfoCardProps) {
        const { classes, cx } = useInfoCardStyles();

        return (
          <Paper className={cx(classes.root, className)} {...props}>
            <Title order={3} className={classes.title}>
              {title}
            </Title>
            <Text className={classes.content}>
              {content}
            </Text>
          </Paper>
        );
      }

      export default InfoCard;
      ```

metadata:
  priority: high
  version: 1.0
</rule>