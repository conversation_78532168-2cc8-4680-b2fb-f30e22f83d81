---
description: Prefer using `type` over `interface`
globs: **/homebase/**/*.{ts,tsx}
---
# TypeScript Type Preferences

Prefer using `type` over `interface` in TypeScript files within the homebase project.

<rule>
name: prefer_types_over_interfaces
description: Standards for using types vs interfaces in TypeScript files
filters:
  # Match TypeScript files in homebase directory
  - type: file_path
    pattern: "homebase/.*\\.(ts|tsx)$"
  # Match interface declarations
  - type: content
    pattern: "interface\\s+\\w+"

actions:
  - type: suggest
    conditions:
      # Look for interfaces that don't have multiple methods/functions
      - pattern: "interface\\s+\\w+\\s*\\{[^}]*\\}"
        message: |
          Consider using a `type` instead of `interface` here.

          Interfaces should only be used when:
          1. You need to extend or implement the interface
          2. The interface contains multiple methods/functions

          Example conversion:
          ```typescript
          // Instead of:
          interface User {
            id: string;
            name: string;
          }

          // Prefer:
          type User = {
            id: string;
            name: string;
          }
          ```

examples:
  - input: |
      // Bad: Using interface for simple object shape
      interface SimpleConfig {
        key: string;
        value: number;
      }

      // Good: Using type for simple object shape
      type SimpleConfig = {
        key: string;
        value: number;
      }

      // Good: Using interface for class implementation
      interface DataService {
        fetch(): Promise<void>;
        save(): Promise<void>;
        delete(): Promise<void>;
      }
    output: "Correctly using types and interfaces based on use case"

metadata:
  priority: medium
  version: 1.0
</rule>