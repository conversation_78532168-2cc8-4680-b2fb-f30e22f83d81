services:
  nes:
    volumes:
      - ../../../:/root/bd
    environment: # these override .env files
      - BASE_URL=https://dev.local.clararx.com
    env_file:
      - .env # this gets created and deleted automatically by apps/cli `clara container` script
    build:
      context: ../../..
      dockerfile: packages/containers/nes/Dockerfile
      target: ${NODE_ENV:-development}
      args:
        NODE_ENV: ${NODE_ENV:-development}
    labels:
      - dev.orbstack.http-port=8000
    ports:
      - 80:8000
      - 443:8443
      - 8080:8080
      - 5173:5173
      - 9229:9229