# Clara Local Docker Dev

## Prerequisites
- Node.js (version 20 or higher)
- pnpm (recommended package manager)
- <PERSON><PERSON> (for macOS/Linux users)

## Installation

1. Run `brew bundle` in the root directory to install all dependencies.
2. Follow [Wiki instructions](https://admin.envoylabs.net/resource/docs/tools/Setting%20up%20pnpm) to install pnpm, Clara CLI, and VSCode.
3. Make sure you have the following installed:

- **OrbStack**: Recommended instead of Docker Desktop
- **mkcert**: For local SSL certificates (`brew bundle` should install this for you)
  * Verify installation by running `mkcert -version`

## Generate Certificates
### Only need to do this once on each machine

```
mkcert -install
mkcert dev.local.clararx.com
```

Do not commit the generated certificates.