remotecfg {
    url            = "https://fleet-management-prod-014.grafana.net"
    id             = sys.env("GCLOUD_FM_COLLECTOR_ID")
    poll_frequency = "60s"

    basic_auth {
        username = "1227797"
        password = sys.env("GCLOUD_RW_API_KEY")
    }

    attributes = {
        tenant_id     = sys.env("GCLOUD_TENANT_ID"),
        environment   = sys.env("GCLOUD_ENVIRONMENT"),
        service_group = sys.env("GCLOUD_SERVICE_GROUP"),
        platform      = sys.env("GCLOUD_PLATFORM"),
        collector_id  = sys.env("GCLOUD_FM_COLLECTOR_ID"),
    }
}