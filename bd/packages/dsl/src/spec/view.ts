/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-object-type */
import type { DSLReq as DSL } from "./req.ts";
import type * as D from "./req.ts";
import type { DSLForm, DSLMethodsType, Params } from "./index.ts";

/* -------------------------------------------------------------------------- */
/*  view level                                                                */
/* -------------------------------------------------------------------------- */

export interface DSLFormView<S extends DSL>
    extends Omit<D.DSLReqView, "block" | "find" | "grid">,
        Params<"view", S, <PERSON>LForm<S>> {
    block: DSLFormViewBlock<S>;
    find: DSLFormViewFind<S>;
    grid: DSLFormViewGrid<S>;
}

export interface DSLFormViewBlock<S extends DSL>
    extends Omit<D.DSLReqViewBlock, "print" | "update">,
        Params<"view.block", S, DSLFormView<S>> {
    print: DSLFormViewBlockPrint<S>;
    update: DSLFormViewBlockUpdate<S>;
}

export interface DSLFormViewBlockPrint<S extends DSL>
    extends D.DSLReqViewBlockPrint,
        Params<"view.block.print", S, DSLFormViewBlock<S>> {}

export interface DSLFormViewBlockUpdate<S extends DSL>
    extends D.DSLReqViewBlockUpdate,
        Params<"view.block.update", S, DSLFormViewBlock<S>> {}

export interface DSLFormViewFind<S extends DSL>
    extends D.DSLReqViewFind,
        Params<"view.find", S, DSLFormView<S>> {}

export interface DSLFormViewGrid<S extends DSL>
    extends Omit<D.DSLReqViewGrid, "aggregate" | "style">,
        Params<"view.grid", S, DSLFormView<S>> {
    aggregate: DSLFormViewGridAggregate<S>;
    style: DSLFormViewGridStyle<S>;
}

/* ------------------------ view.grid.aggregate ----------------------------- */

export type DSLFormViewGridAggregate<
    S extends DSL,
    F extends D.DSLReqViewGridAggregate = S["view"]["grid"]["aggregate"],
> = F &
    DSLMethodViewGridAggregate<S, F> & {
        [K in keyof F]: DSLFormViewGridAggregateDefaults<S, F, K & string> &
            F[K];
    };

export interface DSLMethodViewGridAggregate<
    S extends DSL,
    F extends D.DSLReqViewGridAggregate,
> extends Params<"view.grid.aggregate", S, DSLFormViewGrid<S>> {}

export interface DSLFormViewGridAggregateDefaults<
    S extends DSL,
    F extends D.DSLReqViewGridAggregate,
    FK extends keyof F & string,
> extends D.DSLReqViewGridAggregateDefaults,
        Params<"view.grid.aggregate.*", S, DSLFormViewGridAggregate<S, F>> {}

/* --------------------------- view.grid.style ------------------------------ */

export type DSLFormViewGridStyle<
    S extends DSL,
    F extends D.DSLReqViewGridStyle = S["view"]["grid"]["style"],
> = F &
    DSLMethodViewGridStyle<S, F> & {
        [K in keyof F]: DSLFormViewGridStyleDefaults<S, F, K & string> & F[K];
    };

export interface DSLMethodViewGridStyle<
    S extends DSL,
    F extends D.DSLReqViewGridStyle,
> extends Params<"view.grid.style", S, DSLFormViewGrid<S>> {}

export interface DSLFormViewGridStyleDefaults<
    S extends DSL,
    F extends D.DSLReqViewGridStyle,
    FK extends keyof F & string,
> extends D.DSLReqViewGridStyleDefaults,
        Params<"view.grid.style.*", S, DSLFormViewGridStyle<S, F>> {}

export const DSLMethodsView = {
    view: {},
    "view.block": {},
    "view.block.print": {},
    "view.block.update": {},
    "view.find": {},
    "view.grid": {},
    "view.grid.aggregate": {},
    "view.grid.aggregate.*": {},
    "view.grid.style": {},
    "view.grid.style.*": {},
} as const satisfies DSLMethodsType;
