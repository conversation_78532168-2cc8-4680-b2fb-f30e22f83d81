/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-object-type */
import type { DSLReq as DSL } from "./req.ts";
import type * as D from "./req.ts";
import type { DSLForm, DSLMethodsType, Params } from "./index.ts";

/* -------------------------------------------------------------------------- */
/*  fields                                                                    */
/* -------------------------------------------------------------------------- */

export type DSLFormFields<
    S extends DSL,
    F extends D.DSLReqFields = S["fields"],
> = F &
    DSLMethodFields<S, F> & {
        [K in keyof F]: DSLFormFieldsDefaults<S, F, K & string> & F[K];
    };

export interface DSLMethodFields<S extends DSL, F extends D.DSLReqFields>
    extends Params<"fields", S, DSLForm<S>> {}

export interface DSLFormFieldsDefaults<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends Omit<D.DSLReqFieldsDefaults, "model" | "view">,
        Params<"fields.*", S, DSLFormFields<S, F>> {
    model: DSLFormFieldsModel<S, F, FK>;
    view: DSLFormFieldsView<S, F, FK>;
}

/* -------------------------- fields model ---------------------------------- */

export interface DSLFormFieldsModel<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends Omit<
            D.DSLReqFieldsModel,
            "access" | "dynamic" | "if" | "sourcefilter" | "subfields"
        >,
        Params<"fields.*.model", S, DSLFormFieldsDefaults<S, F, FK>> {
    access: DSLFormFieldsModelAccess<S, F, FK>;
    dynamic: DSLFormFieldsModelDynamic<S, F, FK>;
    if: DSLFormFieldsModelIf<S, F, FK>;
    sourcefilter: DSLFormFieldsModelSourceFilter<S, F, FK>;
    subfields: DSLFormFieldsModelSubfields<S, F, FK>;
}

export interface DSLFormFieldsModelAccess<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends D.DSLReqFieldsModelAccess,
        Params<"fields.*.model.access", S, DSLFormFieldsModel<S, F, FK>> {}

export interface DSLFormFieldsModelDynamic<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends D.DSLReqFieldsModelDynamic,
        Params<"fields.*.model.dynamic", S, DSLFormFieldsModel<S, F, FK>> {}

/* ------------------------ fields model.if --------------------------------- */

export type DSLFormFieldsModelIf<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelIf = F[FK]["model"]["if"],
> = G &
    DSLMethodFieldsModelIf<S, F, FK, G> & {
        [K in keyof G]: DSLFormFieldsModelIfDefaults<S, F, FK, G, K & string> &
            G[K];
    };

export interface DSLMethodFieldsModelIf<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelIf,
> extends Params<"fields.*.model.if", S, DSLFormFieldsModel<S, F, FK>> {}

export interface DSLFormFieldsModelIfDefaults<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelIf,
    GK extends keyof G & string,
> extends Omit<D.DSLReqFieldsModelIfDefaults, "readonly">,
        Params<"fields.*.model.if.*", S, DSLFormFieldsModelIf<S, F, FK, G>> {
    readonly: DSLFormFieldsModelIfReadonly<S, F, FK, G, GK>;
}

export interface DSLFormFieldsModelIfReadonly<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelIf,
    GK extends keyof G & string,
> extends D.DSLReqFieldsModelIfReadonly,
        Params<
            "fields.*.model.if.*.readonly",
            S,
            DSLFormFieldsModelIfDefaults<S, F, FK, G, GK>
        > {}

/* --------------------- fields model.sourcefilter -------------------------- */

export type DSLFormFieldsModelSourceFilter<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSourceFilter = F[FK]["model"]["sourcefilter"],
> = G &
    DSLMethodFieldsModelSourceFilter<S, F, FK, G> & {
        [K in keyof G]: DSLFormFieldsModelSourceFilterDefaults<
            S,
            F,
            FK,
            G,
            K & string
        > &
            G[K];
    };

export interface DSLMethodFieldsModelSourceFilter<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSourceFilter,
> extends Params<
        "fields.*.model.sourcefilter",
        S,
        DSLFormFieldsModel<S, F, FK>
    > {}

export interface DSLFormFieldsModelSourceFilterDefaults<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSourceFilter,
    GK extends keyof G & string,
> extends D.DSLReqFieldsModelSourceFilterDefaults,
        Params<
            "fields.*.model.sourcefilter.*",
            S,
            DSLFormFieldsModelSourceFilter<S, F, FK, G>
        > {}

/* ---------------------- fields model.subfields ---------------------------- */

export type DSLFormFieldsModelSubfields<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSubfields = F[FK]["model"]["subfields"],
> = G &
    DSLMethodFieldsModelSubfields<S, F, FK, G> & {
        [K in keyof G]: DSLFormFieldsModelSubfieldsDefaults<
            S,
            F,
            FK,
            G,
            K & string
        > &
            G[K];
    };

export interface DSLMethodFieldsModelSubfields<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSubfields,
> extends Params<"fields.*.model.subfields", S, DSLFormFieldsModel<S, F, FK>> {}

export interface DSLFormFieldsModelSubfieldsDefaults<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSubfields,
    GK extends keyof G & string,
> extends Omit<D.DSLReqFieldsModelSubfieldsDefaults, "sourcefilter">,
        Params<
            "fields.*.model.subfields.*",
            S,
            DSLFormFieldsModelSubfields<S, F, FK, G>
        > {
    sourcefilter: DSLFormFieldsModelSubfieldsSourceFilter<S, F, FK, G, GK>;
}

export type DSLFormFieldsModelSubfieldsSourceFilter<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSubfields,
    GK extends keyof G & string,
    H extends D.DSLReqFieldsModelSubfieldsSourceFilter = G[GK]["sourcefilter"],
> = H &
    DSLMethodFieldsModelSubfieldsSourceFilter<S, F, FK, G, GK, H> & {
        [K in keyof H]: DSLFormFieldsModelSubfieldsSourceFilterDefaults<
            S,
            F,
            FK,
            G,
            GK,
            H,
            K & string
        > &
            H[K];
    };

export interface DSLMethodFieldsModelSubfieldsSourceFilter<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSubfields,
    GK extends keyof G & string,
    H extends D.DSLReqFieldsModelSubfieldsSourceFilter,
> extends Params<
        "fields.*.model.subfields.*.sourcefilter",
        S,
        DSLFormFieldsModelSubfieldsDefaults<S, F, FK, G, GK>
    > {}

export interface DSLFormFieldsModelSubfieldsSourceFilterDefaults<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
    G extends D.DSLReqFieldsModelSubfields,
    GK extends keyof G & string,
    H extends D.DSLReqFieldsModelSubfieldsSourceFilter,
    HK extends keyof H & string,
> extends D.DSLReqFieldsModelSubfieldsSourceFilterDefaults,
        Params<
            "fields.*.model.subfields.*.sourcefilter.*",
            S,
            DSLFormFieldsModelSubfieldsSourceFilter<S, F, FK, G, GK, H>
        > {}

/* -------------------------- fields view ----------------------------------- */

export interface DSLFormFieldsView<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends Omit<D.DSLReqFieldsView, "embed" | "grid">,
        Params<"fields.*.view", S, DSLFormFieldsDefaults<S, F, FK>> {
    embed: DSLFormFieldsViewEmbed<S, F, FK>;
    grid: DSLFormFieldsViewGrid<S, F, FK>;
}

export interface DSLFormFieldsViewEmbed<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends D.DSLReqFieldsViewEmbed,
        Params<"fields.*.view.embed", S, DSLFormFieldsView<S, F, FK>> {}

export interface DSLFormFieldsViewGrid<
    S extends DSL,
    F extends D.DSLReqFields,
    FK extends keyof F & string,
> extends D.DSLReqFieldsViewGrid,
        Params<"fields.*.view.grid", S, DSLFormFieldsView<S, F, FK>> {}

export const DSLMethodsFields = {
    fields: {
        ˆget:
            ({ store }) =>
            () =>
                Object.fromEntries(store.entries()) ?? {},
        ˆclear:
            ({ store }) =>
            () => {
                store.clear();
            },
    },
    "fields.*": {
        ˆget:
            ({ target, field, store }) =>
            () => {
                if (!field) return undefined;
                return store.get(field) ?? target.model?.default;
            },
        ˆset:
            ({ store, field }) =>
            (v: unknown) => {
                if (!field) return undefined;
                store.set(field, v);
                return v;
            },
        ˆclear:
            ({ field, store }) =>
            () => {
                if (!field) return undefined;
                store.delete(field);
            },
    },
    "fields.*.model": {},
    "fields.*.model.access": {},
    "fields.*.model.dynamic": {},
    "fields.*.model.if": {},
    "fields.*.model.if.*": {},
    "fields.*.model.if.*.readonly": {},
    "fields.*.model.sourcefilter": {},
    "fields.*.model.sourcefilter.*": {},
    "fields.*.model.subfields": {},
    "fields.*.model.subfields.*": {},
    "fields.*.model.subfields.*.sourcefilter": {},
    "fields.*.model.subfields.*.sourcefilter.*": {},
    "fields.*.view": {},
    "fields.*.view.embed": {},
    "fields.*.view.grid": {},
} as const satisfies DSLMethodsType;
