import type { DSLReq as DSL } from "./req.ts";
import type * as D from "./req.ts";
import type {
    <PERSON><PERSON><PERSON>Fields,
    DSLFormModel,
    DSLFormView,
    DSLMethodsType,
    Params,
} from "./index.ts";

export interface DSLForm<S extends DSL = DSL>
    extends Omit<D.DSLReq, "fields" | "model" | "view">,
        Params<"", S, DSLForm<S>> {
    fields: DSLFormFields<S>;
    model: DSLFormModel<S>;
    view: DSLFormView<S>;
}

export const DSLMethodsForm = {
    "": {},
} as const satisfies DSLMethodsType;
