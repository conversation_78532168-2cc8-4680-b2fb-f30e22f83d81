/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-object-type */
import type { DSLReq as DSL } from "./req.ts";
import type * as D from "./req.ts";
import type { DSLForm, DSLMethodsType, Params } from "./index.ts";

/* -------------------------------------------------------------------------- */
/*  model level                                                               */
/* -------------------------------------------------------------------------- */

export interface DSLFormModel<S extends DSL>
    extends Omit<D.DSLReqModel, "access" | "indexes" | "prefill" | "sections">,
        Params<"model", S, DSLForm<S>> {
    access: DSLFormModelAccess<S>;
    indexes: DSLFormModelIndexes<S>;
    prefill: DSLFormModelPrefill<S>;
    sections: DSLFormModelSections<S>;
}

export interface DSLFormModelAccess<S extends DSL>
    extends D.DSLReqModelAccess,
        Params<"model.access", S, DSLFormModel<S>> {}

export interface DSLFormModelIndexes<S extends DSL>
    extends D.DSLReqModelIndexes,
        Params<"model.indexes", S, DSLFormModel<S>> {}

/* --------------------------- model.prefill -------------------------------- */

export type DSLFormModelPrefill<
    S extends DSL,
    F extends D.DSLReqModelPrefill = S["model"]["prefill"],
> = F &
    DSLMethodModelPrefill<S, F> & {
        [K in keyof F]: DSLFormModelPrefillDefaults<S, F, K & string> & F[K];
    };

export interface DSLMethodModelPrefill<
    S extends DSL,
    F extends D.DSLReqModelPrefill,
> extends Params<"model.prefill", S, DSLFormModel<S>> {}

export interface DSLFormModelPrefillDefaults<
    S extends DSL,
    F extends D.DSLReqModelPrefill,
    FK extends keyof F & string,
> extends D.DSLReqModelPrefillDefaults,
        Params<"model.prefill.*", S, DSLFormModelPrefill<S, F>> {}

/* -------------------------- model.sections -------------------------------- */

export type DSLFormModelSections<
    S extends DSL,
    F extends D.DSLReqModelSections = S["model"]["sections"],
> = F &
    DSLMethodModelSections<S, F> & {
        [K in keyof F]: DSLFormModelSectionsDefaults<S, F, K & string> & F[K];
    };

export interface DSLMethodModelSections<
    S extends DSL,
    F extends D.DSLReqModelSections,
> extends Params<"model.sections", S, DSLFormModel<S>> {}

export interface DSLFormModelSectionsDefaults<
    S extends DSL,
    F extends D.DSLReqModelSections,
    FK extends keyof F & string,
> extends D.DSLReqModelSectionsDefaults,
        Params<"model.sections.*", S, DSLFormModelSections<S, F>> {}

export const DSLMethodsModel = {
    model: {},
    "model.access": {},
    "model.indexes": {},
    "model.prefill": {},
    "model.prefill.*": {},
    "model.sections": {},
    "model.sections.*": {},
} as const satisfies DSLMethodsType;
