import type { <PERSON>LReq as DS<PERSON> } from "./req.ts";
import { type DSLForm, DSLMethodsForm } from "./form.ts";
import { DSLMethodsFields } from "./fields.ts";
import { DSLMethodsModel } from "./model.ts";
import { DSLMethodsView } from "./view.ts";

export type { DSLOpt } from "./opt.ts";
export type { DSLReq, DSLReq as DSL } from "./req.ts";
export { type DSLForm } from "./form.ts";
export { type DSLFormFields } from "./fields.ts";
export { type DSLFormModel } from "./model.ts";
export { type DSLFormView } from "./view.ts";

/* -------------------------------------------------------------------------- */
/*  Implementation → API helper                                               */
/* -------------------------------------------------------------------------- */

type API<K extends DSLMethodsKeys> = APILoop<(typeof DSLMethods)[K]>;

type APILoop<T extends Record<string, (ctx: any) => (...args: any) => any>> = {
    [K in keyof T]: ReturnType<T[K]>;
};

export type Params<K extends DSLMethodsKeys, S extends DSL, P> = API<K> & {
    readonly ˆparent: P;
    readonly ˆpath: string;
    readonly ˆroot: DSLForm<S>;
    readonly ˆtype: K;
};

/* -------------------------------------------------------------------------- */
/*  Shared runtime DSL generators                                             */
/* -------------------------------------------------------------------------- */

export type dslFormStore = Map<string, unknown>;

export type DSLForms = Record<Lowercase<string>, DSLForm<any>>;

export type DSLFormFactories = Record<Lowercase<string>, DSLFormFactory<any>>;

export type DSLFormFactory<S extends DSL> = (
    store?: dslFormStore
) => DSLForm<S>;

/* -------------------------------------------------------------------------- */
/*  Shared runtime context helpers                                            */
/* -------------------------------------------------------------------------- */

export type DSLFormContext<S extends DSL> = {
    form: string;
    parent: any;
    root: DSLForm<S> | null;
    store: dslFormStore;
    path: string;
    type: DSLMethodsKeys;
};

export type DSLMethodsContext<S extends DSL> = {
    form: string;
    parent: any;
    root: DSLForm<S>;
    store: dslFormStore;
    target: any;
    field?: string;
};

export type DSLMethodsKeys = keyof typeof DSLMethods;

export type DSLMethodsType = Record<
    string,
    Record<string, (ctx: DSLMethodsContext<any>) => unknown>
>;

export const DSLMethods = {
    ...DSLMethodsForm,
    ...DSLMethodsFields,
    ...DSLMethodsModel,
    ...DSLMethodsView,
} as const satisfies DSLMethodsType;

const DSLMethodsPatterns = (Object.keys(DSLMethods) as [DSLMethodsKeys]).map(
    (methodKey) => {
        const pattern = methodKey
            .replace(/\./g, "\\.")
            .replace(/\*/g, "([^.]+)");
        const regex = new RegExp(`^${methodKey === "" ? "$" : `${pattern}$`}`);
        return { key: methodKey, regex };
    }
);

function dslFormNested<S extends DSL>(
    obj: any,
    context: DSLFormContext<S>
): any {
    if (obj === null || typeof obj !== "object" || Array.isArray(obj)) {
        return obj;
    }

    const proxy = new Proxy(obj, {
        get(target, prop, receiver) {
            if (prop === "ˆparent") return context.parent ?? receiver;
            if (prop === "ˆpath") return context.path;
            if (prop === "ˆroot") return context.root ?? receiver;
            if (prop === "ˆtype") return context.type;

            const propStr = String(prop);

            const methodsForType = DSLMethods[context.type];

            if (methodsForType && propStr in methodsForType) {
                const method = methodsForType[
                    propStr as keyof typeof methodsForType
                ] as ((ctx: DSLMethodsContext<any>) => unknown) | undefined;

                if (method) {
                    const fieldForMethod = parseField(
                        context.form,
                        context.path,
                        context.type
                    );

                    return method({
                        form: context.form,
                        parent: receiver,
                        root: (context.root ?? receiver) as DSLForm<S>,
                        store: context.store,
                        target: target,
                        field: fieldForMethod,
                    });
                }
            }

            const v = Reflect.get(target, prop, receiver);

            if (
                v &&
                typeof v === "object" &&
                prop !== "ˆroot" &&
                prop !== "ˆparent" &&
                prop !== "ˆpath" &&
                prop !== "ˆform"
            ) {
                const childPath = `${context.path}.${propStr}`;
                const pathSegmentForChildTypeLookup = childPath.substring(
                    context.form.length + 1
                );

                let childType: DSLMethodsKeys = "";
                for (const { key, regex } of DSLMethodsPatterns) {
                    if (regex.test(pathSegmentForChildTypeLookup)) {
                        childType = key;
                        break;
                    }
                }

                return dslFormNested<S>(v, {
                    form: context.form,
                    parent: proxy,
                    root: (context.root ?? proxy) as DSLForm<S>,
                    store: context.store,
                    path: childPath,
                    type: childType,
                });
            }
            return v;
        },
        getOwnPropertyDescriptor(t, k) {
            return Reflect.getOwnPropertyDescriptor(t, k);
        },
    });

    if (!context.root) {
        proxy.ˆroot = proxy;
    }
    return proxy;
}

function dslFormRoot<S extends DSL>(
    form: string,
    raw: S,
    store: dslFormStore = new Map()
): DSLForm<S> {
    const instance = dslFormNested<S>(raw, {
        form,
        parent: null,
        root: null,
        store,
        path: form,
        type: "",
    });
    instance.ˆparent = instance;
    return instance as DSLForm<S>;
}

function parseField(
    form: string,
    path: string,
    type: DSLMethodsKeys
): string | undefined {
    if (!type.includes("*")) return undefined;

    const typeParts = type.split(".");
    const pathRelativeToForm = path.substring(form.length + 1);
    const pathParts = pathRelativeToForm.split(".");

    if (typeParts.length === pathParts.length) {
        for (let i = typeParts.length - 1; i >= 0; i--) {
            if (typeParts[i] === "*") {
                return pathParts[i];
            }
        }
    }
    return undefined;
}

export function dslForm<S extends DSL>(
    form: string,
    raw: S,
    baseStore: dslFormStore = new Map()
): DSLFormFactory<S> {
    const initial = new Map(baseStore);
    return (store: dslFormStore = new Map(initial)): DSLForm<S> =>
        dslFormRoot<S>(form, raw, store);
}
