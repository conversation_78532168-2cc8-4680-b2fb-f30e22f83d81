{"name": "@clara/dsl", "version": "1.0.0", "description": "Clara DSL Schema", "license": "UNLICENSED", "private": true, "type": "module", "main": "./src/index.ts", "packageManager": "pnpm@10.12.1", "exports": {".": "./src/index.ts", "./spec": "./src/spec/index.ts", "./spec/*": "./src/spec/*.ts", "./*": "./src/base/*.ts"}, "scripts": {"lint": "pnpm lint:js", "lint:js": "tsc --noEmit && eslint .", "lint:fix": "pnpm lint:js --fix "}, "keywords": [], "author": "<PERSON><PERSON>", "devDependencies": {"@eslint/js": "catalog:", "@types/node": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-yml": "catalog:", "prettier": "catalog:", "stylelint": "catalog:", "stylelint-config-clean-order": "catalog:", "stylelint-config-standard": "catalog:", "stylelint-config-standard-scss": "catalog:", "stylelint-order": "catalog:", "stylelint-prettier": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "prettier": "@clara/config/prettier/base.js"}