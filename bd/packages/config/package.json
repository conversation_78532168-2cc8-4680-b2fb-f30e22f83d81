{"name": "@clara/config", "version": "1.0.0", "description": "<PERSON>", "license": "UNLICENSED", "private": true, "type": "module", "packageManager": "pnpm@10.12.1", "scripts": {"lint": "tsc --noEmit && eslint .", "lint:fix": "eslint . --fix"}, "keywords": [], "author": "<PERSON><PERSON>", "dependencies": {"@clara/dsl": "workspace:*", "@clara/tslib": "workspace:*", "strip-json-comments": "catalog:"}, "devDependencies": {"@eslint/js": "catalog:", "@jest/globals": "catalog:", "@swc/core": "catalog:", "@swc/jest": "catalog:", "@types/jest": "catalog:", "@types/node": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-yml": "catalog:", "eslint": "catalog:", "jest": "catalog:", "prettier": "catalog:", "stylelint-config-clean-order": "catalog:", "stylelint-config-standard-scss": "catalog:", "stylelint-config-standard": "catalog:", "stylelint-order": "catalog:", "stylelint-prettier": "catalog:", "stylelint": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "prettier": "@clara/config/prettier/base.js"}