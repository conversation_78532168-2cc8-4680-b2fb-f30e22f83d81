// ESLint configuration for backend services
import { createBaseConfig, extendTypeScriptConfig } from "./base.js";
import pluginImport from "eslint-plugin-import";

// Define shared service rules
const serviceRules = {
    // Node-specific rules
    "no-process-exit": "error",
    "no-sync": "warn",

    // Import rules for services
    "import/no-unresolved": "error",
    "import/namespace": ["error"],
    "import/no-deprecated": ["warn"],
};

// Define shared service plugins
const servicePlugins = {
    import: pluginImport,
};

export const createServiceConfig = (
    projects = ["./tsconfig.json"],
    extra_ignores = []
) => {
    const config = createBaseConfig(projects, extra_ignores);

    // Add service rules for JavaScript files
    config.push({
        files: ["**/*.js", "**/*.jsx", "**/*.mjs", "**/*.cjs"],
        plugins: servicePlugins,
        rules: serviceRules,
    });

    // Extend TypeScript config with service rules if it exists
    return extendTypeScriptConfig(config, (tsConfig) => ({
        ...tsConfig,
        plugins: {
            ...tsConfig.plugins,
            ...servicePlugins,
        },
        rules: {
            ...tsConfig.rules,
            ...serviceRules,
        },
    }));
};

export default createServiceConfig();
