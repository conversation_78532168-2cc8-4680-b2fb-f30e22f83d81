import {
    extendTypeScriptConfig,
    filterExtendsConfigs,
    restrictToJavaScript,
    restrictToTypeScript,
} from "./base.js";
import { createBaseConfig } from "./base.js";
import nounsanitized from "eslint-plugin-no-unsanitized";
import sdl from "@microsoft/eslint-plugin-sdl";

// Define shared security rules
const securityRules = {
    "no-eval": "error",
    "no-undef": "error",
    "no-prototype-builtins": "error",
    "no-template-curly-in-string": "error",
    "no-unsanitized/method": "error",
    "no-unsanitized/property": "error",
    "no-script-url": "error",
    "sdl/no-inner-html": "error",
    "sdl/no-insecure-url": "error",
};

// Define shared security plugins
const securityPlugins = {
    "no-unsanitized": nounsanitized,
    sdl: sdl,
};

// Security enhancements for any ESLint configuration
export const applySecurityRules = (config) => {
    // Make a copy to avoid mutating the original
    const enhancedConfig = [...config];

    // Add security rules for JavaScript files
    enhancedConfig.push(
        restrictToJavaScript({
            plugins: securityPlugins,
            rules: securityRules,
        })
    );

    // Add SDL recommended configs, ensuring we don't use 'extends'
    const sdlConfigs = filterExtendsConfigs(sdl.configs.recommended);
    for (const sdlConfig of sdlConfigs) {
        enhancedConfig.push(restrictToTypeScript(sdlConfig));
    }

    // Extend TypeScript config with security rules if it exists
    return extendTypeScriptConfig(enhancedConfig, (tsConfig) => ({
        ...tsConfig,
        plugins: {
            ...tsConfig.plugins,
            ...securityPlugins,
        },
        rules: {
            ...tsConfig.rules,
            ...securityRules,
        },
    }));
};

// Helper function to create a secure config from any base config creator
export const createSecureConfig = (configCreator, ...args) => {
    return applySecurityRules(configCreator(...args));
};

// Default export creates a secure base config
export default (projects, extra_ignores = []) =>
    createSecureConfig(createBaseConfig, projects, extra_ignores);
