// Base ESLint configuration for all projects
import eslint from "@eslint/js";
// eslint-disable-next-line import/no-unresolved
import tseslint from "typescript-eslint";

// syntax check plugins for: json, yaml
import json from "eslint-plugin-jsonc";
import yml from "eslint-plugin-yml";

// prettier plugin for formatting
import prettier from "eslint-plugin-prettier";
import prettierRecommended from "eslint-plugin-prettier/recommended";

// Helper to find and modify JavaScript config
export const extendJavaScriptConfig = (config, extender) => {
    const jsConfigIndex = config.findIndex(
        (cfg) => cfg.files && cfg.files.includes("**/*.js")
    );

    if (jsConfigIndex !== -1) {
        const jsConfig = config[jsConfigIndex];
        config[jsConfigIndex] = extender(jsConfig);
    }

    return config;
};

// Helper to find and modify TypeScript config
export const extendTypeScriptConfig = (config, extender) => {
    const tsConfigIndex = config.findIndex(
        (cfg) => cfg.files && cfg.files.includes("**/*.ts")
    );

    if (tsConfigIndex !== -1) {
        const tsConfig = config[tsConfigIndex];
        config[tsConfigIndex] = extender(tsConfig);
    }

    return config;
};

// Helper to extract rules from configs
export const extractRulesFromConfigs = (configs) => {
    const rules = {};
    if (configs && Array.isArray(configs)) {
        for (const config of configs) {
            if (config.rules) {
                Object.assign(rules, config.rules);
            }
        }
    }
    return rules;
};

// Helper to filter out configs with 'extends' property
export const filterExtendsConfigs = (configs) => {
    if (!configs || !Array.isArray(configs)) {
        return [];
    }
    return configs.filter((cfg) => !cfg.extends);
};

// Helper to restrict rules to JavaScript files only
export const restrictToJavaScript = (config, options = {}) => {
    return {
        ...config,
        files: ["**/*.js", "**/*.jsx", "**/*.mjs", "**/*.cjs"],
        ...options,
    };
};

// Helper to restrict rules to TypeScript files only
export const restrictToTypeScript = (config, options = {}) => {
    return {
        ...config,
        files: ["**/*.ts", "**/*.tsx", "**/*/index.ts", "**/*/index.tsx"],
        ...options,
    };
};

// Common configuration factory with consistent API
// https://typescript-eslint.io/packages/typescript-eslint/#config
export const createBaseConfig = (
    projects = ["./tsconfig.json"],
    extra_ignores = []
) => {
    const hasTypeScriptProjects =
        Array.isArray(projects) && projects.length > 0;

    const allowedGlobals = {
        __dirname: "readonly",
        console: "readonly",
        document: "readonly",
        fetch: "readonly",
        window: "readonly",
    };

    // Start with common configs for all projects
    const config = [
        {
            ignores: [
                "bin/",
                "dist/",
                "node_modules/",
                "pnpm-lock.yaml",
                ...extra_ignores,
            ],
        },
        eslint.configs.recommended,
        {
            files: ["**/*.js", "**/*.jsx", "**/*.mjs", "**/*.cjs"],
            languageOptions: {
                globals: allowedGlobals,
            },
        },
        {
            files: ["**/*.json"],
            languageOptions: {
                parser: json,
            },
            plugins: {
                prettier: prettier,
            },
            rules: {
                "prettier/prettier": "error",
            },
        },
    ];

    // Add YAML config if it doesn't use extends
    if (yml.configs["flat/recommended"]) {
        config.push(...filterExtendsConfigs(yml.configs["flat/recommended"]));
    }

    // Add prettier config if it doesn't use extends
    if (prettierRecommended && !prettierRecommended.extends) {
        config.push(prettierRecommended);
    } else {
        // Add a simple prettier config
        config.push({
            plugins: {
                prettier: prettier,
            },
            rules: {
                "prettier/prettier": "error",
            },
        });
    }

    // Only add TypeScript configs if projects are specified
    if (hasTypeScriptProjects) {
        // Get the recommended TypeScript configs and restrict to TS files
        const tsRecommendedConfigs = filterExtendsConfigs(
            tseslint.configs.recommended
        ).map((cfg) => restrictToTypeScript(cfg));
        config.push(...tsRecommendedConfigs);

        // Get the strict TypeScript rules and restrict to TS files
        const tsStrictRules = extractRulesFromConfigs(
            tseslint.configs.strictTypeChecked
        );

        // Add TypeScript-specific configs
        config.push(
            restrictToTypeScript({
                languageOptions: {
                    parser: tseslint.parser,
                    parserOptions: {
                        project: projects,
                    },
                },
                plugins: {
                    "@typescript-eslint": tseslint.plugin,
                },
                rules: {
                    ...tsStrictRules,
                },
            }),
            restrictToTypeScript({
                plugins: {
                    prettier: prettier,
                },
                settings: {
                    "import/resolver": {
                        typescript: {
                            project: projects,
                            noWarnOnMultipleProjects: true,
                        },
                    },
                },
                rules: {
                    "@typescript-eslint/no-dynamic-delete": "off",
                    "@typescript-eslint/no-explicit-any": "off",
                    "@typescript-eslint/no-non-null-assertion": "off",
                    "@typescript-eslint/no-unnecessary-condition": "off",
                    "@typescript-eslint/no-unsafe-argument": "off",
                    "@typescript-eslint/no-unsafe-assignment": "off",
                    "@typescript-eslint/no-unsafe-call": "off",
                    "@typescript-eslint/no-unsafe-member-access": "off",
                    "@typescript-eslint/no-unsafe-return": "off",
                    "@typescript-eslint/no-unused-vars": [
                        "error",
                        {
                            argsIgnorePattern: "^_",
                            varsIgnorePattern: "^_",
                        },
                    ],
                    "@typescript-eslint/require-await": "off",
                    "@typescript-eslint/restrict-template-expressions": "off",
                },
            })
        );
    }

    return config;
};

// Simple default export for basic usage
export default createBaseConfig();
