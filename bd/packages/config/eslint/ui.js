// ESLint configuration for frontend UI applications
import {
    createBaseConfig,
    extendTypeScriptConfig,
    extractRulesFromConfigs,
    filterExtendsConfigs,
} from "./base.js";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import reactCompiler from "eslint-plugin-react-compiler";
import reactPerf from "eslint-plugin-react-perf";
import pluginImport from "eslint-plugin-import";

export const createUIConfig = (
    projects = ["./tsconfig.json"],
    extra_ignores = []
) => {
    const config = createBaseConfig(projects, extra_ignores);

    // Extract rules from React configs
    const reactRecommendedRules = extractRulesFromConfigs([
        react.configs.recommended,
    ]);
    const reactJsxRuntimeRules = extractRulesFromConfigs([
        react.configs["jsx-runtime"],
    ]);

    // Extend TypeScript config with React rules
    const configWithReact = extendTypeScriptConfig(config, (tsConfig) => ({
        ...tsConfig,
        plugins: {
            ...tsConfig.plugins,
            "react-hooks": reactHooks,
            "react-compiler": reactCompiler,
            "react-perf": reactPerf,
            import: pluginImport,
        },
        settings: {
            ...tsConfig.settings,
            react: {
                version: "detect",
            },
            "import/parsers": {
                "@typescript-eslint/parser": [".ts", ".tsx"],
            },
        },
        rules: {
            ...tsConfig.rules,
            // React Hooks rules
            "react-hooks/rules-of-hooks": "error",
            "react-hooks/exhaustive-deps": ["off"],

            // React rules - directly include them instead of using extends
            ...reactRecommendedRules,
            ...reactJsxRuntimeRules,

            // React Compiler rules
            "react-compiler/react-compiler": "error",

            // Import rules
            "import/no-unresolved": "error",
            "import/namespace": ["error"],
            "import/default-index": ["off"],
            "import/no-deprecated": ["warn"],

            // Restricted syntax rules for component optimization
            "no-restricted-syntax": [
                "error",
                {
                    selector: `
                        FunctionDeclaration > BlockStatement > VariableDeclaration >
                        VariableDeclarator[init.type!=/CallExpression$/]
                        [init.type!='LogicalExpression']
                        [id.name!=/^use[A-Z]/]
                    `,
                    message: "Move static declaration outside component",
                },
                {
                    selector:
                        "FunctionDeclaration[id.name=/^[A-Z]/] > BlockStatement > FunctionDeclaration",
                    message: "Move static function outside component",
                },
                {
                    selector:
                        "FunctionDeclaration[id.name=/^[A-Z]/] > BlockStatement > VariableDeclaration > VariableDeclarator[init.type='ArrayExpression']",
                    message: "Move static array outside component",
                },
                {
                    selector:
                        "FunctionDeclaration[id.name=/^[A-Z]/] > BlockStatement > VariableDeclaration > VariableDeclarator[init.type='ObjectExpression']",
                    message: "Move static object outside component",
                },
            ],
        },
    }));

    // Add React Refresh config without using extends
    const refreshConfigs = filterExtendsConfigs([reactRefresh.configs.vite]);
    if (refreshConfigs.length > 0) {
        configWithReact.push(...refreshConfigs);
    } else {
        // Add a simplified version without extends
        configWithReact.push({
            files: ["**/*.jsx", "**/*.tsx"],
            plugins: {
                "react-refresh": reactRefresh,
            },
            rules: {
                "react-refresh/only-export-components": "warn",
            },
        });
    }

    return configWithReact;
};

export default createUIConfig();
