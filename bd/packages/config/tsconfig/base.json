{"compilerOptions": {"tsBuildInfoFile": "${configDir}/node_modules/.tmp/tsconfig.tsbuildinfo", "skipLibCheck": true, "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "allowImportingTsExtensions": true, "useDefineForClassFields": true, "isolatedModules": true, "noEmit": true, "baseUrl": "${configDir}", "rootDir": "${configDir}", "rootDirs": ["${configDir}/src", "${configDir}/__tests__", "${configDir}"], "paths": {"@/*": ["${configDir}/src/*"]}, "outDir": "${configDir}/dist", "target": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"]}, "exclude": ["${configDir}/**/.cache", "${configDir}/**/.tmp", "${configDir}/bin", "${configDir}/build", "${configDir}/dist", "${configDir}/../../**/node_modules"]}