import {
    existsSync,
    lstatSync,
    mkdirSync,
    readFileSync,
    unlinkSync,
    writeFileSync,
} from "fs";
import { globSync } from "glob";
import path from "path";
import type { AppLogger, FileMatchStats } from "../type/index.ts";

export function fileExt(filepath: string = ""): string {
    return path.extname(filepath);
}

export function isDir(file: string): boolean {
    return existsSync(file) && lstatSync(file).isDirectory();
}

export function isFile(file: string): boolean {
    return existsSync(file) && lstatSync(file).isFile();
}

export function glob(
    pattern: string[] | string,
    options: object = {}
): string[] {
    if (!Array.isArray(pattern)) pattern = [pattern];
    return pattern.flatMap((file) => globSync(file, options)).sort();
}

export function mkdir(dir: string): void {
    if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
    }
}

// caller should handle nulls on failure
export function read(file: string, appLogger: AppLogger): string | null {
    let data = null;
    try {
        data = readFileSync(file, "utf8");
    } catch (err) {
        appLogger.fail(String(err));
    }
    return data;
}

export function rm(file: string, appLogger: AppLogger): void {
    try {
        unlinkSync(file);
    } catch (err) {
        appLogger.fail(String(err));
    }
}

export function splitPath(filepath: string = ""): string[] {
    return path.normalize(filepath).split(path.sep);
}

export function stats(file: string): FileMatchStats {
    const { mtimeMs, size } = lstatSync(file);
    return {
        mtime: mtimeMs,
        size: size,
    };
}

export function write(file: string, data: string, appLogger: AppLogger): void {
    try {
        writeFileSync(file, data, "utf8");
    } catch (err) {
        appLogger.fail(String(err));
    }
}
