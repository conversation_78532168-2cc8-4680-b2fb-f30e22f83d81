import { TypePartial } from "../type/index.ts";

export const deepMerge = <T extends Record<string, any>>(
    target: T,
    source: TypePartial<T>
): T => {
    const result = { ...target };

    (Object.keys(source) as (keyof TypePartial<T>)[]).forEach((key) => {
        const sourceValue = source[key];
        const targetValue = target[key as keyof T];

        if (
            sourceValue &&
            typeof sourceValue === "object" &&
            !Array.isArray(sourceValue)
        ) {
            result[key as keyof T] = deepMerge(
                targetValue,
                sourceValue as TypePartial<typeof targetValue>
            );
        } else if (sourceValue !== undefined) {
            result[key as keyof T] = sourceValue as T[keyof T];
        }
    });

    return result;
};
