/* eslint-disable no-eval */
import ts from "typescript";
import coffee from "coffeescript";
import cson from "cson";
import type { DSLOpt } from "@clara/dsl";
import type { AppLogger, CompileResult, FileCache } from "../type/index.ts";
import { isFile, read } from "../utils/file.ts";

/***************************************************************************
 * App Interface - Minimal interface needed from the App class
 **************************************************************************/

export default class CSONCompiler {
    constructor(
        private appLogger: AppLogger,
        private fileCache: FileCache
    ) {}

    public ast(file: string): ts.SourceFile | null {
        if (!isFile(file)) {
            this.appLogger.fail(`Cannot find .js: ${file}`);
            return null;
        }
        const js = read(file, this.appLogger);
        if (!js) {
            this.appLogger.fail(`Cannot load .js: ${file}`);
            return null;
        }

        const sourceFile = ts.createSourceFile(
            "file.ts",
            js,
            ts.ScriptTarget.Latest,
            true,
            ts.ScriptKind.TS
        );
        return sourceFile;
    }

    public coffee(cf: string, raw: boolean = false): CompileResult {
        try {
            if (!raw && !isFile(cf)) {
                return {
                    status: "failed",
                    code: null,
                    error: { message: `Cannot find .coffee: ${cf}` },
                    continue: this.appLogger.forceContinue || false,
                };
            }

            // Compile CoffeeScript to JavaScript string
            const cfData = raw ? cf : read(cf, this.appLogger);
            if (!cfData) {
                return {
                    status: "failed",
                    code: null,
                    error: { message: `Cannot compile .coffee: ${cf}` },
                    continue: this.appLogger.forceContinue || false,
                };
            }
            const cfCode = coffee.compile(cfData, { bare: true });

            // Evaluate JavaScript string to object
            const jsCode = eval(cfCode);
            return {
                status: "compiled",
                code: jsCode as DSLOpt,
                error: null,
                continue: true,
            };
        } catch (err) {
            return {
                status: "failed",
                code: null,
                error: err,
                continue: this.appLogger.forceContinue || false,
            };
        }
    }

    public cson(
        cs: string,
        raw: boolean = false,
        cached: boolean = false
    ): CompileResult {
        if (raw && cached) {
            this.appLogger.fatal(
                `Cannot use "raw" and "cached" options together: ${cs}`
            );
        }

        if (!raw && !isFile(cs)) {
            return {
                status: "failed",
                code: null,
                error: { message: `Cannot find .cson: ${cs}` },
                continue: this.appLogger.forceContinue || false,
            };
        }

        if (cached && this.fileCache) {
            const cachedCSON = this.fileCache.getFileCache(cs);
            if (cachedCSON) {
                return {
                    status: "unmodified",
                    code: cachedCSON as DSLOpt,
                    error: null,
                    continue: true,
                };
            }
        }

        const csCode = raw ? cs : read(cs, this.appLogger);
        if (!csCode) {
            return {
                status: "failed",
                code: null,
                error: { message: `Cannot load .cson: ${cs}` },
                continue: this.appLogger.forceContinue || false,
            };
        }

        // eslint-disable-next-line @typescript-eslint/unbound-method
        const originalPrepareStackTrace = Error.prepareStackTrace;
        const jsCode = csCode ? (cson.parse(csCode) as DSLOpt) : null;
        Error.prepareStackTrace = originalPrepareStackTrace;

        if (jsCode === null) {
            return {
                status: "failed",
                code: null,
                error: { message: `Failed to parse CSON: ${cs}` },
                continue: this.appLogger.forceContinue || false,
            };
        } else if (
            (typeof jsCode === "object" && "location" in jsCode) ||
            jsCode instanceof Error
        ) {
            return {
                status: "failed",
                code: null,
                error: jsCode,
                continue: this.appLogger.fail(
                    `\n× CSON Error: ${cs}\n  ∙ ${JSON.stringify(jsCode)}\n`
                ),
            };
        } else {
            if (this.fileCache) {
                this.fileCache.setFileCache(cs, jsCode);
            }
            return {
                status: "compiled",
                code: jsCode,
                error: null,
                continue: true,
            };
        }
    }

    public findNode(node: ts.Node, identifier: string): ts.Node | undefined {
        if (
            ts.isExpressionStatement(node) &&
            ts.isBinaryExpression(node.expression) &&
            node.expression.operatorToken.kind === ts.SyntaxKind.EqualsToken &&
            node.expression.left.getText() === identifier
        ) {
            return node.expression.right;
        }

        let matchedNode: ts.Node | undefined;
        node.forEachChild((childNode) => {
            if (!matchedNode) {
                matchedNode = this.findNode(childNode, identifier);
            }
        });

        return matchedNode;
    }

    public getNodeValue(node: ts.Node, identifier: string): any {
        let node_value = null;
        const foundNode = this.findNode(node, identifier);
        if (foundNode) {
            eval("node_value = " + foundNode.getText());
        } else {
            node_value = null;
        }
        return node_value;
    }
}
