import type { AppLogger, LogLevel, OutlogItems } from "../type/index.ts";

/***************************************************************************
 * DSL Output handler for validation errors and warnings
 **************************************************************************/

export default class DSLOutput {
    private logs: Record<LogLevel, OutlogItems> = {
        info: new Map(),
        warn: new Map(),
        error: new Map(),
    };

    constructor(private appLogger: AppLogger) {}

    /***************************************************************************
     * Status checks
     **************************************************************************/
    //#region Status

    public hasInfo(form: string): boolean {
        return this.logs.info.has(form);
    }

    public hasWarning(form: string): boolean {
        return this.logs.warn.has(form);
    }

    public hasError(form: string): boolean {
        return this.logs.error.has(form);
    }

    //#endregion

    /***************************************************************************
     * Logging methods
     **************************************************************************/
    //#region Logging

    private log(
        level: LogLevel,
        form: string,
        message: string,
        field?: string,
        items?: string[]
    ) {
        if (!this.logs[level].has(form)) {
            this.logs[level].set(form, []);
        }
        this.logs[level].get(form)!.push({ field, message, items });

        if (this.appLogger.jsonOutput) {
            const method = level === "error" ? "fail" : level;
            this.appLogger[method]({
                form: form,
                field: field,
                message: message,
                items: items,
            });
        }
    }

    public info(
        form: string,
        message: string,
        field?: string,
        items?: string[]
    ) {
        this.log("info", form, message, field, items);
    }

    public warn(
        form: string,
        message: string,
        field?: string,
        items?: string[]
    ) {
        this.log("warn", form, message, field, items);
    }

    public fail(
        form: string,
        message: string,
        field?: string,
        items?: string[]
    ) {
        this.log("error", form, message, field, items);
    }

    //#endregion

    /***************************************************************************
     * Output formatting
     **************************************************************************/
    //#region Output

    private output(title: string, icon: string, messages: OutlogItems): void {
        if (messages.size === 0) return;

        this.appLogger.info(`\nDSL ${title}:`);

        // Sort forms alphabetically
        const sortedForms = Array.from(messages.keys()).sort();

        for (const form of sortedForms) {
            const issues = messages.get(form)!;

            // Group by field and sort fields
            const itemsByField: OutlogItems = new Map();

            for (const issue of issues) {
                const field = issue.field || "";
                if (!itemsByField.has(field)) {
                    itemsByField.set(field, []);
                }
                itemsByField.get(field)!.push(issue);
            }

            // Print form name once with provided prefix
            this.appLogger.info(`${icon} ${form}:`);

            // Sort fields and print their messages
            const sortedFields = Array.from(itemsByField.keys()).sort();

            // First handle messages without a field that have items
            if (itemsByField.has("")) {
                const noFieldIssues = itemsByField.get("")!;
                for (const issue of noFieldIssues) {
                    if (!issue.items?.length) {
                        this.appLogger.info(`  ∙ ${issue.message}`);
                    }
                }
                for (const issue of noFieldIssues) {
                    if (issue.items?.length) {
                        this.appLogger.info(`  ∙ ${issue.message}`);
                        for (const item of issue.items) {
                            this.appLogger.info(`    ◦ ${item}`);
                        }
                    }
                }
            }

            // Then handle messages with fields
            for (const field of sortedFields) {
                if (!field) continue; // Skip empty field as we handled it above

                const issues = itemsByField.get(field)!;
                // Print field name once
                this.appLogger.info(`  ∙ ${field}:`);
                // Print all messages under this field
                for (const issue of issues) {
                    this.appLogger.info(`    - ${issue.message}`);
                    if (issue.items?.length) {
                        for (const item of issue.items) {
                            this.appLogger.info(`      ◦ ${item}`);
                        }
                    }
                }
            }
        }
    }

    private readonly outputConfig: Record<
        LogLevel,
        { title: string; icon: string }
    > = {
        info: { title: "INFO", icon: "ℹ" },
        warn: { title: "WARNING", icon: "*" },
        error: { title: "ERROR", icon: "×" },
    };

    public results(): void {
        if (this.appLogger.jsonOutput) {
            return;
        }

        // Display logs in order: info -> warn -> error
        (["warn", "error"] as const).forEach((level) => {
            const config = this.outputConfig[level];
            this.output(config.title, config.icon, this.logs[level]);
        });
    }

    //#endregion
}
