import z from "zod/v4";

import type { <PERSON><PERSON><PERSON><PERSON>, DSLReq } from "@clara/dsl";
import type {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DSLCheckModules,
    DSLFiles,
    DSLInterface,
    DSLParams,
} from "../type/index.ts";
import DSLOutput from "./output.ts";
import type { DSLOptFieldsDefaults } from "@clara/dsl/spec/opt";

export default class DSLParser {
    /***************************************************************************
     * Properties
     **************************************************************************/
    //#region Properties

    private dsl!: DSLInterface;
    public output: DSLOutput;

    public DEFAULTS = "DEFAULTS" as const;
    public TAB_TOGGLES = "Tab Toggles" as const;

    public MERGE_STOPAT = [
        "if",
        "sourcefilter",
        "subfields",
        "prefill",
        "sections",
    ]; // Stop merge at these keys
    public RESERVED_KEYWORDS = [
        "_meta",
        "_force",
        "array",
        "def",
        "limit",
        "group",
        "object",
        "select",
    ]; // Reserved keywords
    public RESERVED_PREFIXES = ["tabif_"] as const; // Reserved prefixes for field keys
    public SYNC_MODE_REQUIRED_FIELDS = ["allow_sync", "active"] as const; // Required fields for sync_mode: mixed

    private schemaReq!: z.ZodType;
    private schemaOpt!: z.ZodType;

    public files!: { keys: Set<string>; cson: DSLFiles };

    //#endregion

    /***************************************************************************
     * Initialization
     **************************************************************************/
    //#region Init

    constructor(
        private appLogger: AppLogger,
        private dslCheckModules: DSLCheckModules
    ) {
        this.output = new DSLOutput(appLogger);
    }

    public async init(dsl: DSLInterface): Promise<void> {
        this.dsl = dsl;
        this.dsl.DSL_AutoInsert = this.autoInsert(this.dsl.DSL_AutoInsert);

        this.schemaOpt = this.spec(this.dsl.DSL_Specification, "DSL", false);
        this.schemaReq = this.spec(this.dsl.DSL_Specification, "DSL", true);
        // console.dir(z.toJSONSchema(this.schemaReq), { depth: null });
    }

    //#endregion

    /***************************************************************************
     * CSON Processing
     **************************************************************************/
    //#region Processing

    private autoInsert(ai: DSLOpt): DSLOpt {
        const allowed = {
            autoinsert: true,
            label: "",
            readonly: true,
            source: null,
            type: "",
        };
        const r: DSLParams = {};
        if (ai.fields) {
            for (const [fieldName, field] of Object.entries(ai.fields)) {
                r[fieldName] = {};
                for (const fieldType of ["model", "view"] as Array<
                    keyof DSLOptFieldsDefaults
                >) {
                    r[fieldName][fieldType] = {};
                    const fieldSub = field[fieldType];
                    if (fieldSub) {
                        for (const [k, v] of Object.entries(fieldSub)) {
                            let ve = v;
                            if (
                                k === "type" &&
                                (v === "boolean" || v === "string")
                            ) {
                                ve = "text";
                            }
                            if (k in allowed) {
                                r[fieldName][fieldType][k] = ve;
                            }
                        }
                    }
                }
            }
        }
        return { fields: r } as DSLOpt;
    }

    private fill(form: string, cson: DSLOpt): DSLReq {
        const result = this.schemaOpt.safeParse(cson);
        if (!result.success) {
            // Record all validation errors
            result.error.issues.forEach((e) => {
                this.output.fail(form, e.message, e.path.join("."));
            });
            // Still throw to halt parsing
            throw new Error("Validation failed during fill");
        }

        // Helper utilities for Zod v4 internals
        const getDef = (t: any) => t?._zod?.def ?? t?._def ?? {};
        const unwrap = (t: z.ZodType): z.ZodType => {
            let cur: any = t;
            let guard = 0;
            while (guard++ < 16) {
                // # of levels deep to unwrap
                const d = getDef(cur);
                if (d.schema) {
                    cur = d.schema;
                    continue;
                }
                if (d.innerType) {
                    cur = d.innerType;
                    continue;
                }
                break;
            }
            return cur;
        };

        const getShape = (obj: z.ZodType): Record<string, z.ZodType> => {
            const def = getDef(obj);
            const shapeVal = def.shape ?? def.shapeFn ?? def.shape_;
            return typeof shapeVal === "function" ? shapeVal() : shapeVal;
        };

        // Recursively fill in default values while preserving existing values
        const defaults = (obj: any, zodType: z.ZodType): any => {
            let shape: Record<string, z.ZodType> | undefined;
            let items: Record<string, any> | undefined;

            if (zodType instanceof z.ZodObject) {
                shape = getShape(zodType);
                items = { "": obj };
            } else if (
                zodType instanceof z.ZodRecord &&
                getShape(getDef(zodType).valueType)
            ) {
                shape = getShape(getDef(zodType).valueType);
                items = obj;
            } else if (zodType instanceof z.ZodDefault) {
                // Only use default if value is undefined
                return obj === undefined ? zodType.parse(undefined) : obj;
            } else {
                // unwrap pipes / transforms / etc.
                const inner = unwrap(zodType);
                if (
                    inner !== zodType &&
                    (inner instanceof z.ZodObject ||
                        inner instanceof z.ZodRecord)
                ) {
                    return defaults(obj, inner);
                }
                // primitive or unknown composite → return as-is
                return obj;
            }

            const ret: Record<string, any> = {};
            for (const item in items) {
                if (zodType instanceof z.ZodRecord) {
                    ret[item] = {};
                }

                const target = zodType instanceof z.ZodRecord ? ret[item] : ret;
                const source =
                    zodType instanceof z.ZodRecord ? items[item] : obj;

                for (const key in shape) {
                    if (zodType instanceof z.ZodObject && key in source) {
                        // Preserve existing values while recursively checking nested objects
                        target[key] = defaults(source[key], shape[key]);
                    } else if (shape[key] instanceof z.ZodDefault) {
                        // Only use default if value is undefined
                        target[key] =
                            source[key] === undefined
                                ? shape[key].parse(undefined)
                                : source[key];
                    } else {
                        target[key] = defaults(source[key] ?? {}, shape[key]);
                    }
                }
            }

            return ret;
        };

        return defaults({ ...(result.data as any) }, this.schemaReq);
    }

    public merge<T extends DSLParams, S extends DSLParams>(
        form: string,
        target: T,
        source: S,
        stopat: string[] = [],
        autoinsert_target: boolean = false
    ): T & S {
        const output = { ...target } as T & S;

        if (
            target &&
            source &&
            typeof target === "object" &&
            typeof source === "object" &&
            !Array.isArray(target) &&
            !Array.isArray(source)
        ) {
            Object.keys(source).forEach((key: keyof S) => {
                const keyT = key as keyof T;
                const keyO = key as keyof (T & S);
                if (
                    !(
                        this.dsl.DSL_AutoInsert.fields &&
                        key in this.dsl.DSL_AutoInsert.fields
                    )
                ) {
                    if (source[key]?.model?.autoinsert) {
                        delete source[key].model.autoinsert;
                    }
                    if (!autoinsert_target) {
                        if (keyT in target && target[keyT]?.model?.autoinsert) {
                            delete target[keyT].model.autoinsert;
                        }
                    }
                }

                if (stopat.includes(key as string)) {
                    output[keyO] = source[key];
                } else if (Object.hasOwn(target, keyT)) {
                    if (
                        target[keyT] &&
                        source[key] &&
                        typeof target[keyT] === "object" &&
                        typeof source[key] === "object" &&
                        !Array.isArray(target[keyT]) &&
                        !Array.isArray(source[key])
                    ) {
                        output[keyO] = this.merge(
                            form,
                            target[keyT] as T,
                            source[key] as S,
                            stopat,
                            autoinsert_target
                        ) as (T & S)[keyof S] & (T & S)[keyof T];
                    } else {
                        output[keyO] = source[key];
                    }
                } else {
                    output[keyO] = source[key];
                }
            });
        } else {
            return { ...source, ...target }; // simple merge if not objects
        }

        return output;
    }

    public parse(form: string, rawJson: DSLOpt): DSLReq | null {
        let filled: DSLReq;
        try {
            // 1. Initial fill with defaults (non-strict)
            filled = this.fill(form, rawJson);
        } catch (error) {
            if (error instanceof z.ZodError) {
                error.issues.forEach((e) => {
                    this.output.fail(form, e.message, e.path.join("."));
                });
            }
            return null;
        }

        try {
            // 2. Run all transforms
            filled = this.run_transform(form, filled);

            // 3. Re-fill with defaults to handle any new fields/sections
            filled = this.fill(form, filled);

            // 4. Run all transforms again
            filled = this.run_transform(form, filled);

            // 5. Final re-fill with defaults to handle any missing properties
            filled = this.fill(form, filled);

            return filled;
        } catch (error) {
            if (error instanceof z.ZodError) {
                error.issues.forEach((e) => {
                    this.output.fail(form, e.message, e.path.join("."));
                });
            }
            return null;
        }
    }

    //#endregion

    /***************************************************************************
     * Validation
     **************************************************************************/
    //#region Validate

    public results(): void {
        this.output.results();
    }

    private run_transform(form: string, cson: DSLReq): DSLReq {
        // Collect all transformation functions
        const transformModules = this.dslCheckModules.transform;

        // Run transformations
        Object.entries(transformModules).forEach(([name, fn]) => {
            if (name.startsWith(`transform_`)) {
                // Set transformedData directly to the transform function's return value
                cson = fn(this, form, cson);
            }
        });

        return cson;
    }

    public run_validate(form: string, cson: DSLReq): boolean {
        const validationModules = this.dslCheckModules.validate;

        try {
            const valid = this.schemaReq.safeParse(cson);
            if (!valid.success || this.output.hasError(form)) {
                return false;
            }

            Object.entries(validationModules).forEach(([name, fn]) => {
                if (name.startsWith("validate_")) {
                    fn(this, form, valid.data as DSLReq); // keep existing validators
                }
            });

            if (this.output.hasError(form)) {
                return false;
            }

            this.output.info(form, "Validation successful");
            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                error.issues.forEach((e) => {
                    this.output.fail(form, e.message, e.path.join("."));
                });
            } else {
                this.output.fail(
                    form,
                    error instanceof Error ? error.message : String(error)
                );
            }
            return false;
        }
    }

    public run_warn(form: string, cson: DSLReq): void {
        const warningModules = this.dslCheckModules.warn;

        Object.entries(warningModules).forEach(([name, fn]) => {
            if (name.startsWith("warn_")) {
                fn(this, form, cson);
            }
        });
    }

    //#endregion

    /***************************************************************************
     * Schema Specification
     **************************************************************************/
    //#region Spec

    private spec(
        dslSpec: any,
        path: string = "DSL",
        required: boolean = true
    ): z.ZodType {
        if (!dslSpec || typeof dslSpec !== "object") {
            // fail with error
            this.appLogger.fail(`Unknown property spec at path "${path}"`);
        }

        const shape: Record<string, z.ZodType> = {};

        for (const key in dslSpec) {
            const currentPath = path ? `${path}.${key}` : key; // Construct breadcrumb path

            if (key === this.DEFAULTS) {
                const defaultsSpec = dslSpec[key];
                if (!defaultsSpec || typeof defaultsSpec !== "object") {
                    continue;
                }
                const valueSchema = this.spec(
                    defaultsSpec,
                    currentPath,
                    required
                ); // Recurse with updated path
                return z.record(z.string(), valueSchema).describe(currentPath); // Describe Record node
            } else {
                const propertySpec = dslSpec[key];
                if (
                    propertySpec &&
                    typeof propertySpec === "object" &&
                    "def" in propertySpec
                ) {
                    // Handle type definitions
                    let baseZodType: z.ZodType;
                    const defValue = propertySpec.def;
                    const type = propertySpec.type;
                    const source = propertySpec.source;

                    const typeArray = Array.isArray(type)
                        ? [...new Set(type)]
                        : [type];
                    const nonNullTypes = typeArray.filter((t) => t !== "null");
                    const sourceArray =
                        source && Array.isArray(source)
                            ? ["", ...new Set(source)]
                            : [];

                    if (sourceArray.length > 0) {
                        baseZodType = z
                            .enum(
                                sourceArray.filter((s) => s != "null") as [
                                    string,
                                    ...string[],
                                ]
                            )
                            .nullable();
                    } else {
                        // Create an array of Zod types based on each type in nonNullTypes
                        const zodTypes = nonNullTypes.map((t) => {
                            switch (t) {
                                case "string":
                                    return z.string();
                                case "number":
                                    return z.number();
                                case "boolean":
                                    return z.boolean();
                                case "array":
                                    return z.array(z.any());
                                case "object":
                                    return z.looseObject({});
                                default:
                                    return z.any();
                            }
                        });

                        // Create union of all types
                        baseZodType =
                            zodTypes.length > 1
                                ? z.union(
                                      zodTypes as unknown as [
                                          z.ZodType,
                                          z.ZodType,
                                          ...z.ZodType[],
                                      ]
                                  )
                                : zodTypes[0] || z.any();

                        if (
                            type == "string" ||
                            type == "object" ||
                            typeArray.includes("null") ||
                            typeArray.includes("number") ||
                            typeArray.includes("string")
                        ) {
                            baseZodType = baseZodType.nullable();
                        }
                    }

                    let zodType = baseZodType;
                    if (defValue !== undefined) {
                        zodType = zodType.default(defValue);
                    }

                    shape[key] = zodType.describe(currentPath); // Describe type definition node
                } else if (propertySpec && typeof propertySpec === "object") {
                    // Recursive call for nested objects
                    shape[key] = this.spec(propertySpec, currentPath, required); // Recurse with updated path
                } else {
                    // fail with error
                    this.appLogger.fail(
                        `Unknown property spec at path "${currentPath}"`
                    );
                }
            }
        }

        const schema = z.strictObject(shape).describe(path);
        return required ? schema : schema.partial();
    }

    //#endregion
}
