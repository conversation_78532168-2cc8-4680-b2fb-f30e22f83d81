// used for comparing cached file mtime/sizes in cli/core/cache.ts
export type FileMatchStats = {
    mtime: number; // in milliseconds
    size: number;
};

export type FileProcessing = {
    Destination: string;
    Failed: number;
    Unmodified: number;
    Compiled: number;
    Total: number;
};

export interface FileCache {
    getFileCache(file: string): object | string | null;
    setFileCache(file: string, data: any, expiration?: number): void;
}
