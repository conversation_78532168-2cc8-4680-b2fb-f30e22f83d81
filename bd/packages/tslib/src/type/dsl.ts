import type { DSLOpt, DSLReq } from "@clara/dsl";
import type DSLParser from "../dsl/parser.ts";

/***************************************************************************
 * DSL Definitions
 **************************************************************************/
//#region DSL

// Spec exports
export type DSLFiles = Record<string, DSLReq>;
export type DSLFolders = Record<string, DSLFiles>;

export type DSLParams = Record<string, any>;

export interface DSLInterface {
    DSL_Specification: object;
    DSL_AutoInsert: DSLOpt;
    MAX_COLLECTIONS: number;
}

//#endregion

/***************************************************************************
 * DSL Check
 **************************************************************************/
//#region DSLCheck

export type DSLCheckModule<T = void> = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
) => T;

export type DSLCheckTransform = DSLCheckModule<DSLReq>;
export type DSLCheckValidate = DSLCheckModule;
export type DSLCheckWarn = DSLCheckModule;

export type DSLCheckModules = {
    transform: Record<string, DSLCheckTransform>;
    validate: Record<string, DSLCheckValidate>;
    warn: Record<string, DSLCheckWarn>;
};

//#endregion

/***************************************************************************
 * Compile Result
 **************************************************************************/
//#region Compile

export type CompileStatus = "compiled" | "failed" | "unmodified";
export type CompileResult = {
    status: CompileStatus;
    code: DSLOpt | null;
    error: any;
    continue: boolean;
};

export type CSONForm = {
    cson: DSLOpt;
    status: Omit<CompileStatus, "failed">;
};
export type CSONForms = Record<string, CSONForm>;
export type CSONFolders = Record<string, CSONForms>;

//#endregion
