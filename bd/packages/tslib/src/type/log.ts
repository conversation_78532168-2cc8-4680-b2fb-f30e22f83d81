import type { TypeReturn } from "./generics.ts";

/***************************************************************************
 * Log
 **************************************************************************/
//#region Log

export type AppLoggerItem = (message: string | object) => boolean;

export interface AppLogger {
    info: AppLoggerItem;
    warn: AppLoggerItem;
    fail: AppLoggerItem;
    fatal: TypeReturn<AppLoggerItem, void>;
    forceContinue: boolean;
    jsonOutput: boolean;
}

export type LogItem = any;

export type LogItems = {
    info: LogItem[];
    warn: LogItem[];
    error: LogItem[];
};

export type LogLevel = keyof LogItems;

export type JSONLog = {
    data: string;
    log: LogItems;
};

//#endregion

/***************************************************************************
 * Output Result
 **************************************************************************/
//#region Output

export type OutlogItem = {
    field?: string;
    message: string;
    items?: string[];
};

export type OutlogItems = Map<string, OutlogItem[]>;

//#endregion
