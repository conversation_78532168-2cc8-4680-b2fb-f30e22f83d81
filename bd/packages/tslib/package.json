{"name": "@clara/tslib", "version": "1.0.0", "description": "Clara TS Library", "license": "UNLICENSED", "private": true, "type": "module", "main": "./src/index.ts", "packageManager": "pnpm@10.12.1", "exports": {".": "./src/index.ts", "./dsl": "./src/dsl/index.ts", "./dsl/*": "./src/dsl/*.ts", "./styles": "./src/styles/index.scss", "./styles/*": "./src/styles/*.scss", "./type": "./src/type/index.ts", "./type/*": "./src/type/*.ts", "./utils": "./src/utils/index.ts", "./utils/*": "./src/utils/*.ts"}, "scripts": {"build": "tsc -b", "dev": "tsc --watch", "lint": "pnpm lint:js && pnpm lint:css", "lint:js": "tsc --noEmit && eslint .", "lint:css": "stylelint \"**/*.{css,less,scss}\"", "lint:fix": "pnpm lint:js --fix && pnpm lint:css --fix"}, "keywords": [], "author": "<PERSON><PERSON>", "dependencies": {"@clara/dsl": "workspace:*"}, "devDependencies": {"@eslint/js": "catalog:", "@types/coffeescript": "catalog:", "@types/cson": "catalog:", "@types/node": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "coffeescript": "catalog:", "cson": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-yml": "catalog:", "prettier": "catalog:", "source-map": "catalog:", "stylelint": "catalog:", "stylelint-config-clean-order": "catalog:", "stylelint-config-standard": "catalog:", "stylelint-config-standard-scss": "catalog:", "stylelint-order": "catalog:", "stylelint-prettier": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "prettier": "@clara/config/prettier/base.js"}