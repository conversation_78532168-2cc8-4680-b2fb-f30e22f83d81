fields:
	control_field:
		model:
			if:
				'*':
					fields: ['dependent_field']
		view:
			label: 'Control Field'

	dependent_field:
		view:
			label: 'Dependent Field'

	unrelated_field:
		model:
			if:
				'*':
					sections: ['Section 2']
		view:
			label: 'Unrelated Field'

model:
	name: ['control_field', 'dependent_field']
	sections:
		'Section 1':
			fields: ['control_field', 'unrelated_field']
		'Section 2':
			fields: ['dependent_field']