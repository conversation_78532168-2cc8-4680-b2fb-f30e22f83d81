{"name": "@clara/cli", "version": "1.1.0", "description": "Clara CLI", "license": "UNLICENSED", "private": true, "type": "module", "main": "./src/index.ts", "packageManager": "pnpm@10.12.1", "scripts": {"build": "tsc -b", "lint": "tsc --noEmit && eslint .", "lint:fix": "eslint . --fix", "dev": "tsx src/index.ts", "test": "NODE_OPTIONS='--experimental-vm-modules --no-warnings' NODE_ENV=testing jest --colors --no-cache"}, "keywords": [], "author": "<PERSON><PERSON>", "dependencies": {"@clara/dsl": "workspace:*", "@clara/tslib": "workspace:*", "@oclif/core": "catalog:", "@oclif/plugin-not-found": "catalog:", "better-sqlite3": "catalog:", "bindings": "catalog:", "blessed": "catalog:", "chalk": "catalog:", "execa": "catalog:", "glob": "catalog:", "strip-json-comments": "catalog:", "toml": "catalog:", "zod": "catalog:"}, "bin": {"clara": "./src/index.ts"}, "oclif": {"bin": "clara", "commands": "./src/commands", "dirname": "./src", "plugins": ["@oclif/plugin-*"], "topicSeparator": " "}, "devDependencies": {"@eslint/js": "catalog:", "@jest/globals": "catalog:", "@swc/core": "catalog:", "@swc/jest": "catalog:", "@types/better-sqlite3": "catalog:", "@types/blessed": "catalog:", "@types/jest": "catalog:", "@types/node": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-yml": "catalog:", "jest": "catalog:", "prettier": "catalog:", "ts-node": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "prettier": "@clara/config/prettier/base.js"}