/* eslint-disable no-sync */
import { test, beforeAll } from "@jest/globals";
import { Config } from "@oclif/core";
import fs from "fs";
import path from "path";

import type { JSONLog } from "@clara/tslib/type/log";
import DSL from "@commands/dsl/index";

const TIMEOUT = 30 * 1000;

let config: Config;
beforeAll(async () => {
    config = await Config.load({ root: "." });
});

// Get all subdirectories in the __mocks__/dsl directory
const mockDslPath = path.join("__mocks__", "dsl");
const dslFolders = fs
    .readdirSync(mockDslPath)
    .filter(
        (file) =>
            file.startsWith("validate_") &&
            fs.statSync(path.join(mockDslPath, file)).isDirectory()
    )
    .sort((a, b) => {
        // Natural sort implementation
        return a.localeCompare(b, undefined, {
            numeric: true,
            sensitivity: "base",
        });
    });

// Create parallel test cases for each folder
test.concurrent.each(dslFolders)(
    "Validates CSON: %s",
    async (folder) => {
        const command = new DSL(
            [
                "--json",
                "--clear",
                "--dslpath",
                path.join("__mocks__", "dsl", folder),
                "--customer",
                "cust",
            ],
            config
        );

        await command.init();
        const result = (await command.run()) as JSONLog;

        try {
            expect(result).not.toBeFalsy();
            expect(result).toHaveProperty("data");
            expect(result).toHaveProperty("log");
            expect(result).not.toHaveProperty("data.error");

            // info should always contain form2 and form3 but not form1 but can be in any index
            expect(
                result.log.info.some(
                    (item: any) =>
                        item.message === "Validation successful" &&
                        item.form === "form2"
                )
            ).toBe(true);
            expect(
                result.log.info.some(
                    (item: any) =>
                        item.message === "Validation successful" &&
                        item.form === "form3"
                )
            ).toBe(true);

            expect(
                result.log.info.some(
                    (item: any) =>
                        item.form === "form1" && item.message.length > 0
                )
            ).toBe(false);

            // there should only be no warnings
            expect(result.log.warn.length).toEqual(0);

            // error should always contain form1 only
            expect(result.log.error[0].form).toEqual("form1");

            // there should only be 1 error
            expect(result.log.error.length).toEqual(1);

            // data should always contain form2 and form3 only
            expect(result.data).toEqual(["form2", "form3"]);
        } catch (error) {
            command.app.errorJSONLog(error, result);
        }
    },
    TIMEOUT
);
