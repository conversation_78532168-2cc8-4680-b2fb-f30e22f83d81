import { BaseCommand } from "@core/base";
import { Help } from "@oclif/core";

export default class NESIndex extends BaseCommand<typeof NESIndex> {
    static description = `Manage NES container:
    docker: Launch local docker-compose stack
    fly: Deploy to fly.io`;

    static examples = [
        `$ clara container nes docker --app echirag --env development`,
        `$ clara container nes fly --app p-pbx-nes --env production --local-only`,
    ];

    public async process(): Promise<object> {
        // Display help for the nes command
        const help = new Help(this.config);
        await help.showCommandHelp(this.config.findCommand("container:nes"));

        return {
            message: "NES container help displayed",
        };
    }
}
