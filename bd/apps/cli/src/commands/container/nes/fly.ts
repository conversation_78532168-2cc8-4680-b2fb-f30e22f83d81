import { Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";
import * as path from "path";

export default class NESFly extends BaseCommand<typeof NESFly> {
    static description = `Deploy NES container to fly.io`;

    static examples = [
        `$ clara container nes fly --app p-pbx-nes --env production --local-only`,
        `$ clara container nes fly --app p-pbx-nes --env production --local-only --no-cache`,
    ];

    static override flags = {
        app: Flags.string({
            description: "App name (app name for fly)",
            required: true,
        }),
        env: Flags.string({
            description: "Environment",
            required: false,
            default: "development",
            options: ["development", "testing", "staging", "production"],
        }),
        "local-only": Flags.boolean({
            description: "Build locally (default)",
            required: false,
            default: true,
            exclusive: ["remote-only"],
        }),
        "remote-only": Flags.boolean({
            description: "Build remotely",
            required: false,
            default: false,
            exclusive: ["local-only"],
        }),
        "no-cache": Flags.boolean({
            description: "Build without using cache",
            required: false,
            default: false,
        }),
    };

    public async process(): Promise<object> {
        const { flags } = await this.parse(NESFly);
        const app = flags.app;
        const env = flags.env;
        const localOnly = flags["local-only"];
        const remoteOnly = flags["remote-only"];
        const noCache = flags["no-cache"];

        try {
            await this.runFly(app, env, localOnly, remoteOnly, noCache);
            return {
                success: true,
                app,
                env,
                localOnly,
                remoteOnly,
                noCache,
                message: "Deployment completed successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    private async runFly(
        app: string,
        env: string,
        localOnly: boolean,
        remoteOnly: boolean,
        noCache: boolean
    ): Promise<void> {
        try {
            // Set environment
            process.env.NODE_ENV = env;

            // Set paths
            const flyToml = "fly.toml";
            const pkgPath = "packages/containers/nes";
            const contextPath = this.app.repo_root;
            const dockerFilePath = this.app.repoPath(`${pkgPath}/Dockerfile`);
            const dockerIgnorePath = this.app.repoPath(`.dockerignore`);
            const pkgTomlPath = this.app.repoPath(path.join(pkgPath, flyToml));
            const rootTomlPath = this.app.repoPath(flyToml);

            let configPath;
            if (this.app.isFile(pkgTomlPath)) {
                configPath = pkgTomlPath;
                this.app.info(`Using fly.toml from package: ${pkgTomlPath}`);
            } else if (this.app.isFile(rootTomlPath)) {
                configPath = rootTomlPath;
                this.app.info(`Using fly.toml from repo root: ${rootTomlPath}`);
            } else {
                this.app.fatal(
                    `Could not find fly.toml in either ${pkgTomlPath} or ${rootTomlPath}.` +
                        " Please create a fly.toml file in the repo root or in the package path."
                );
                return;
            }

            // Build fly deploy command
            let flyCommand = `fly deploy "${contextPath}" --build-arg NODE_ENV="${env}" --build-target "${env}" --config "${configPath}" --dockerfile "${dockerFilePath}" --ignorefile "${dockerIgnorePath}" --dns-checks=0 --strategy immediate --now -y -a "${app}"`;

            // Add local/remote flag
            if (remoteOnly) {
                flyCommand += " --remote-only";
            } else {
                flyCommand += " --local-only";
            }

            // Add no-cache flag if specified
            if (noCache) {
                flyCommand += " --no-cache";
            }

            // Run fly deploy
            this.app.info(`Deploying to fly.io: \n  ${flyCommand}\n`);
            this.app.$exec(flyCommand);

            this.app.info("Deployment completed successfully");
        } catch (error) {
            this.app.fatal(`Failed to deploy to fly.io: ${error}`);
        }
    }
}
