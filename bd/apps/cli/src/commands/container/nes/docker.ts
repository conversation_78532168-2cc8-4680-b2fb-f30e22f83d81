/* eslint-disable no-sync */
import { Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";
import * as fs from "fs";
import * as path from "path";

export default class NESDocker extends BaseCommand<typeof NESDocker> {
    static description = `Launch local NES docker-compose stack`;

    static examples = [
        `$ clara container nes docker --app echirag --env development`,
    ];

    static override flags = {
        app: Flags.string({
            description: "App name (username for docker)",
            required: true,
        }),
        env: Flags.string({
            description: "Environment",
            required: false,
            default: "development",
            options: ["development", "testing", "staging", "production"],
        }),
        "no-cache": Flags.boolean({
            description: "Build without using cache",
            required: false,
            default: false,
        }),
    };

    public async process(): Promise<object> {
        const { flags } = await this.parse(NESDocker);
        const app = flags.app;
        const env = flags.env;
        const noCache = flags["no-cache"];

        try {
            await this.runDocker(app, env, noCache);
            return {
                success: true,
                app,
                env,
                message: "Docker containers started successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    private async runDocker(
        app: string,
        env: string,
        noCache: boolean
    ): Promise<void> {
        // Set paths
        const pkgPath = "packages/containers/nes";
        const fullPkgPath = this.app.repoPath(pkgPath);
        const configPath = this.app.repoPath("configs");
        const dockerComposePath = this.app.repoPath(
            `${pkgPath}/docker-compose.yml`
        );
        const keysPath = path.join(configPath, ".env.keys");
        const encPath = path.join(configPath, `.env.d${app}`);
        const tmpEnvPath = path.join(fullPkgPath, ".env");

        // Check if files exist
        if (!this.app.isFile(encPath) || !this.app.isFile(keysPath)) {
            this.app.fatal(`Missing ${encPath} or ${keysPath}`);
            return;
        }

        try {
            // Decrypt env file
            this.app.info(`Decrypting environment file...`);
            this.app.$exec(
                `dotenvx decrypt -f "${encPath}" -fk "${keysPath}" --stdout > "${tmpEnvPath}"`
            );

            // Append NODE_ENV
            fs.appendFileSync(tmpEnvPath, `\nNODE_ENV=${env}\n`);

            // Common docker compose parameters
            const dockerComposeParams = `docker compose -f ${dockerComposePath} --env-file "${tmpEnvPath}" --project-name "clararx"`;

            // Run docker compose
            this.app.info(
                `Starting docker compose with${noCache ? "out" : ""} cache...`
            );
            process.env.DOCKER_BUILDKIT = "1";
            process.env.BUILDKIT_PROGRESS = "plain";
            process.env.NODE_ENV = env;

            if (noCache) {
                this.app.$exec(`${dockerComposeParams} build --no-cache nes`);
                this.app.$exec(`${dockerComposeParams} up -d nes`);
            } else {
                this.app.$exec(`${dockerComposeParams} up --build -d nes`);
            }

            this.app.info("Docker containers started successfully");
        } catch (error) {
            this.app.fatal(`Failed to start docker containers: ${error}`);
        } finally {
            // Clean up temp env file
            if (this.app.isFile(tmpEnvPath)) {
                fs.unlinkSync(tmpEnvPath);
            }
        }
    }
}
