import { Args } from "@oclif/core";
import { BaseCommand } from "@core/base";

export default class CLI extends BaseCommand<typeof CLI> {
    static description = `Perform Clara CLI-related commands:
    [no arguments]: Compile /README.md`;
    static examples = [
        `$ clara cli
replacing <!-- usage --> in ~/bd/cli/README.md
replacing <!-- commands --> in ~/bd/cli/README.md
replacing <!-- toc --> in ~/bd/cli/README.md
`,
    ];

    static override args = {
        readme: Args.string(),
    };

    public async process(): Promise<object> {
        const { args } = await this.parse(CLI);
        if (Object.keys(args).length === 0 || args.readme) {
            this.app.info("Generating README.md...");
            // switch to cli since $shell runs in the root of the repo
            const { stdout, stderr } = await this.app.$shell`
                cd cli;
                pnpm --silent oclif readme;
                sed -i.bak -e '/$ npm install -g clara/d' README.md;
                rm README.md.bak`;

            if (stdout) this.app.info(stdout);
            if (stderr) {
                // Don't fail on the dist directory warning
                if (!stderr.includes("No compiled source found at")) {
                    this.app.fail(stderr);
                    return { error: stderr };
                }
            }
            return { message: "Readme regenerated." };
        }
        return { message: "Nothing to do." };
    }
}
