import { Args, Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";

export default class Deploy extends BaseCommand<typeof Deploy> {
    static description = `Deploy latest code to given apps:
        [no arguments]: Deploy the code to app in fly.toml`;
    static examples = [
        `$ clara fly deploy appName:hap  --buildTarget=testing`,
        `$ clara fly deploy d-eumer-nes:3accbc59d9ded396f7931d819b5e43f4`,
    ];
    static override args = {
        app: Args.string({
            description: "Fly.io Application Name:HAP",
            required: true,
        }),
    };
    static override flags = {
        buildTarget: Flags.string({
            description: "Build target to deploy",
            required: false,
        }),
        image: Flags.string({
            description: "Docker Image to deploy",
            required: false,
        }),
    };
    public async process(): Promise<object> {
        const { args, flags } = await this.parse(Deploy);
        let apps: string = "",
            verifiedApps: string[] = [];
        const appsObj: Record<string, string> = {};
        let image: string = "";
        let buildTarget: string = "";
        if (this.app.fly) {
            await this.app.fly.init();
        } else {
            this.app.warn(
                "Fly module is only available in development environment."
            );
            return {
                message: "Fly module not loaded.",
            };
        }
        this.app.info("git pull origin develop (bd)...");
        await this.app.$shell`git pull origin develop`;
        this.app.info("\t DONE ");
        if (args && args.app) apps = args.app;
        if (flags && flags.image) image = flags.image;
        if (flags && flags.buildTarget) buildTarget = flags.buildTarget;
        apps.split(",").map((ahap) => {
            appsObj[ahap.split(":")[0]] = ahap.split(":")[1];
        });
        verifiedApps = await this.app.fly.getCommaSeparatedApp(
            Object.keys(appsObj)
        );
        this.app.info("\t DONE");

        await this.app.fly.deployFly(verifiedApps, image, buildTarget);

        this.app.info("git pull origin develop (bd-dsl)...");
        // TODO: change .pipe to .shell when shell have ability to accept cwd
        await this.app.$pipe(
            (_line: string) => {},
            (_err: string) => {},
            "bd-dsl" //command cwd
        )`git pull origin develop`;
        this.app.info("\t DONE ");

        await this.app.fly.deployDSL(verifiedApps, appsObj);

        return {};
    }
}
