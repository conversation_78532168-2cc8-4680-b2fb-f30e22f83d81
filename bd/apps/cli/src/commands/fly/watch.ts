/* eslint-disable @typescript-eslint/no-misused-promises */
/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable no-process-exit */
import { Args } from "@oclif/core";
import blessed from "blessed";
import chalk from "chalk";
import { watch as fs_watch } from "fs";
import { BaseCommand } from "@core/base";
import type { LogLevel } from "@clara/tslib/type/log";

type ListbarCommand = blessed.Widgets.ListbarOptions["commands"][number];

export default class Watch extends BaseCommand<typeof Watch> {
    static description = `Watch & Sync local dev folders to Fly:
    [no arguments]: Watch & Sync`;
    static examples = [`$ clara fly dev`];

    static override args = {
        app: Args.string({
            description: "Fly.io Application Name",
            required: false,
        }),
    };

    // Manually maintain the sync folder list to be same as:
    //   bd/Dockerfile
    //   bd/packages/containers/nes/exec/local.watcher
    static SYNC_FOLDERS = ["apps/cli", "apps/homebase", "nes"];

    // Manually update the exclude list to handle same cases as:
    //   bd/.dockerignore
    //   bd/.gitignore
    //   bd/**/.gitignore
    //   bd/packages/containers/nes/exec/local.watcher
    //
    // NOTE:
    //   Wilcards NOT supported in exlude list here.
    static SYNC_EXCLUDE = [
        ".cache",
        ".DS_Store",
        ".git",
        ".tmp",
        "dist",
        "node_modules",
    ];

    // This caches list of excludes to be used in rsync command
    private excludes: string | null = null;

    // n-curses controls
    private screen!: blessed.Widgets.Screen;
    private mlogs: blessed.Widgets.BoxElement[] = [];
    private serverLogs: blessed.Widgets.BoxElement[] = [];
    private dlog!: blessed.Widgets.BoxElement;
    private menu!: blessed.Widgets.ListbarElement;
    private isServerLogsVisible: boolean = false;

    /***************************************************************************
     * Initialization
     **************************************************************************/
    //#region Init

    public async process(): Promise<object> {
        const { args } = await this.parse(Watch);
        if (this.app.fly) {
            await this.app.fly.init();
        } else {
            this.app.warn(
                "Fly module is only available in development environment."
            );
            return {
                message: "Fly module not loaded.",
            };
        }

        // Prepare rsync exclude list
        this.excludes =
            "--exclude '" +
            Watch.SYNC_EXCLUDE.join(`*' \\
  --exclude '`) +
            "*'";

        // Load the app name and machines from Fly.io
        // Throw an error if no appname or machines found.
        await this.app.fly.initAppMachines(args);

        // Clear .ssh/known_hosts entries and fly proxy
        //   for all machines from prior sessions
        await this.app.fly.clearKnownHosts();

        // Create the blessed/n-curses screen
        await this.draw();
        this.clearLogs();

        // Override the default log handler
        this.app.override_handler = (
            message: string,
            level: LogLevel
        ): void => {
            this.logBox(message, this.dlog, level);
        };

        // Connect with new Fly.io certificate, proxy with ssh-agent on load
        await this.menuReconnect();

        // Start watching the local dev folders to sync to Fly.io
        await this.watch();

        // Sync all on start
        this.logBox("Press F to run a full sync.", this.dlog, "warn");

        // Wait forever
        await new Promise(() => {});

        // This 👇 never gets called
        return { message: "Watch ended." };
    }
    //#endregion

    /***************************************************************************
     * UI functions
     **************************************************************************/
    //#region UI

    // Clear scrollable textboxes
    public clearLogs(): void {
        const machines = this.app.fly.getMachines();
        for (let i = 0; i < machines.length; i++) {
            this.mlogs[i].setContent("");
            this.mlogs[i].scrollTo(0);
            this.serverLogs[i].setContent("");
            this.serverLogs[i].scrollTo(0);
        }
        this.dlog.setContent("");
        this.dlog.scrollTo(0);
        this.screen.render();
    }

    // Render blessed screen, hook keyboard
    public async draw(): Promise<void> {
        /* Screen:
            [] [] [] []
            [         ]
            Menu...
        */
        this.screen = blessed.screen({
            smartCSR: true,
            terminal: "screen-256color",
            title: "Clara CLI: Fly.io Watch",
            mouse: true,
        });

        const appname = this.app.fly.getAppName();
        const machines = this.app.fly.getMachines();
        const mcount = machines.length;
        const mboxpercent = 70;

        for (let i = 0; i < mcount; i++) {
            const machine = machines[i];
            const mTopPercent = Math.floor((i / mcount) * mboxpercent);
            const mHeightPercent = Math.floor((1 / mcount) * mboxpercent);

            const machineContainer = blessed.box({
                top: `${mTopPercent}%`,
                height: `${mHeightPercent}%+1`,
            });

            this.mlogs[i] = blessed.box({
                parent: machineContainer,
                left: 0,
                width: "100%",
                height: "100%-1",
                label: `${appname}/${"id" in machine ? machine["id"] : ""} #${i + 1}: ${"name" in machine ? machine["name"] : ""}`,
                scrollable: true,
                mouse: true,
                border: { type: "line" },
                style: {
                    border: {
                        fg: "blue",
                    },
                },
            });

            this.serverLogs[i] = blessed.box({
                parent: machineContainer,
                right: 0,
                width: "50%",
                height: "100%-1",
                hidden: true,
                label: `${appname}/${"name" in machine ? machine["name"] : ""} #${i + 1}: Server Logs`,
                scrollable: true,
                mouse: true,
                border: { type: "line" },
                style: {
                    border: {
                        fg: "green",
                    },
                },
                content: "SERVER LOGS WILL APPEAR HERE",
            });

            this.screen.append(machineContainer);
        }

        const dlogTop = mboxpercent;
        const dlogHeight = 100 - dlogTop;
        this.dlog = blessed.box({
            top: `${dlogTop}%`,
            width: "100%",
            height: `${dlogHeight}%`,
            label: this.screen.title,
            border: { type: "line" },
            scrollable: true,
            mouse: true,
            style: {
                border: {
                    fg: "blue",
                },
            },
        });

        this.screen.append(this.dlog);

        this.menu = blessed.listbar({
            bottom: 0,
            height: "shrink",
            mouse: true,
            keys: true,
            commands: [],
            items: [],
            autoCommandKeys: false,
        });

        this.menu.setItems({
            Reconnect: {
                keys: ["r"],
                callback: async () => {
                    this.clearLogs();
                    await this.menuReconnect();
                },
            },
            "Install PNPM": {
                keys: ["i"],
                callback: async () => {
                    await this.menuInstallPNPM();
                },
            },
            "Full Sync": {
                keys: ["f"],
                callback: async () => {
                    // eslint-disable-next-line no-sync
                    await this.menuFullSync();
                },
            },
            "PM2 Restart": {
                keys: ["p"],
                callback: async () => {
                    await this.menuPM2Restart();
                },
            },
            "Server Logs": {
                keys: ["l"],
                callback: async () => {
                    await this.toggleServerLogs();
                },
            },
            Quit: {
                keys: ["q"],
                callback: () => {
                    this.screen.destroy();
                    process.exit(0);
                },
            },
        } as unknown as ListbarCommand[]);

        // bind the 'c' key to clear all the logBox
        this.screen.key(["c"], () => {
            this.clearLogs();
        });

        this.screen.append(this.menu);
        this.screen.render();
    }

    // Show timestamped log to any box in loglevel-specific color
    public logBox(
        line: string,
        box: blessed.Widgets.BoxElement | null = null,
        level: LogLevel = "info"
    ): void {
        if (!box) box = this.dlog;

        const now = new Date();
        const hours = String(now.getHours()).padStart(2, "0");
        const minutes = String(now.getMinutes()).padStart(2, "0");
        const seconds = String(now.getSeconds()).padStart(2, "0");
        const milliseconds = String(Math.round(now.getMilliseconds())).padStart(
            3,
            "0"
        );

        const paddedTime = `${hours}:${minutes}:${seconds}.${milliseconds}`;
        const lineText = line
            .toString()
            .trim()
            .replace(/\n/g, "\n             ");

        switch (level) {
            case "error":
                box.pushLine(
                    `${chalk.hex("#aa2211")(paddedTime)} ${chalk.hex("#cc1100")(lineText)}`
                );
                break;
            case "warn":
                box.pushLine(
                    `${chalk.hex("#cc9955")(paddedTime)} ${chalk.hex("#cc7711")(lineText)}`
                );
                break;
            case "info":
            default:
                box.pushLine(`${chalk.hex("#888888")(paddedTime)} ${lineText}`);
        }

        box.setScrollPerc(100);
        this.screen.render();
    }

    // Clear full screen and redraw
    public redraw(): void {
        this.screen.realloc();
        this.screen.render();
    }

    //#endregion

    /***************************************************************************
     * Menu actions
     **************************************************************************/
    //#region Menu

    public async menuReconnect(): Promise<void> {
        this.logBox("Connecting...");

        // get new certificate + ssh-agent
        await this.app.fly.issueCertificate(
            (line: string) => {
                this.logBox(line, this.dlog, "info");
            },
            (line: string) => {
                this.logBox(line, this.dlog, "warn");
            },
            (line: string) => {
                this.logBox(line, this.dlog, "error");
            }
        );

        await this.app.fly.onAllMachines(async (machine, index) => {
            const log = (line: string) => {
                this.logBox(line, this.mlogs[index], "info");
            };
            const wrn = (line: string) => {
                this.logBox(line, this.mlogs[index], "warn");
            };
            const err = (line: string) => {
                this.logBox(line, this.mlogs[index], "error");
            };

            // stop, start, test proxy
            await this.app.fly.restartProxy(machine, index, log, wrn, err);
        });

        // reconnect to server logs
        this.toggleServerLogs(false);
    }

    public async menuInstallPNPM(): Promise<void> {
        this.logBox("PNPM Install...");
        this.app.fly.onAllMachines(async (machine, index) => {
            const log = (line: string, level: LogLevel = "info") => {
                this.logBox(line, this.mlogs[index], level);
            };

            log(`Clearing packages...`);
            const rmdirs =
                "rm -rf ./" +
                Watch.SYNC_FOLDERS.join(`/node_modules; rm -rf ./`) +
                "/node_modules;";

            await this.app.fly.sshPipe(
                (stdout) => {
                    log(stdout);
                },
                (stderr) => {
                    log(stderr, "error");
                },
                `cd $CLARA_HOME;
                    pm2 stop vite;
                    ${rmdirs}
                    pnpm store prune;`,
                index
            );

            log(`Installing: pnpm i -r...`);
            await this.app.fly.sshPipe(
                (stdout) => {
                    log(stdout);
                },
                (stderr) => {
                    log(stderr, "error");
                },
                `cd $CLARA_HOME;
                pnpm i -r;
                pm2 start vite;`,
                index
            );
            log(`PNPM installed.`, "warn");
        });
    }

    public async menuFullSync(): Promise<void> {
        this.logBox("Full Sync...");
        Watch.SYNC_FOLDERS.forEach((folder) => {
            const watch_folder = this.app.repoPath(folder) + "/";
            this.rsync(folder, watch_folder, "", true);
        });
    }

    public async menuPM2Restart(): Promise<void> {
        this.logBox("PM2 Caddy & Vite Restart...");
        this.app.fly.onAllMachines(async (machine, index) => {
            const log = (line: string, level: LogLevel = "info") => {
                this.logBox(line, this.mlogs[index], level);
            };

            log(`Restarting...`);
            await this.app.fly.sshPipe(
                (stdout) => {
                    log(stdout);
                },
                (stderr) => {
                    log(stderr, "error");
                },
                `cd $CLARA_HOME;
                pm2 restart caddy vite;`,
                index
            );
            log(
                `PM2 Restarted. NOTE: Vite takes 5-10s to rebuild cache.`,
                "warn"
            );
        });
    }
    //#endregion

    /***************************************************************************
     * I/O calls
     **************************************************************************/
    //#region I/O

    // Build local js* if applicable, then rsync to Fly
    public async build_sync(
        folder: string,
        watch_folder: string,
        filename: string
    ): Promise<void> {
        this.rsync(folder, watch_folder, filename);
    }

    // Rsync selected local folder to all connected machines in parallel
    public async rsync(
        folder: string,
        watch_folder: string,
        filename: string,
        full_sync = false
    ): Promise<void> {
        const appname = this.app.fly.getAppName();
        const machines = this.app.fly.getMachines();
        this.logBox("→ " + folder + (filename ? "/" : "") + filename);

        // When doing full-sync, we want to delete all extra remote
        //   folders except homebase/dist, where vite stores its
        //   dynamically named files for serving
        const full_sync_params =
            full_sync && folder == "homebase"
                ? "--delete-delay --exclude 'dist/*'"
                : "";

        machines.forEach(async (machine, index) => {
            const log = (line: string, level: LogLevel = "info") => {
                this.logBox(line, this.mlogs[index], level);
            };
            const err = (line: string) => {
                this.logBox(line, this.mlogs[index], "error");
            };

            const proxy_port = this.app.fly.getProxyPort(index);
            const rsync_cmd = `${this.app.fly.getEnvironment()} APP_NAME=${appname} rsync -vazi --delay-updates ${full_sync_params} --force ${this.excludes} "${watch_folder}${filename}" rsync://localhost:${proxy_port + 1}/clara/${folder ? folder + "/" : ""}${filename}`;

            const start = Date.now();
            await this.app.$pipe(log, err)`${rsync_cmd}`;
            log(`Sync ${folder} completed in ${Date.now() - start}ms.`, "warn");
        });
    }

    // Hook local file watcher for build, rsync
    public async watch(): Promise<void> {
        this.logBox("Watching Paths:");
        const watch_timer: Record<string, NodeJS.Timeout> = {};
        const watch_files: Record<string, string[]> = {};

        Watch.SYNC_FOLDERS.forEach((folder) => {
            const watch_folder = this.app.repoPath(folder) + "/";
            this.logBox(`• ${this.app.basePath(watch_folder)}...`);

            fs_watch(
                watch_folder,
                { recursive: true, encoding: "utf-8" },
                async (eventType: string, filename: string | null) => {
                    if (!filename) return;
                    // don't sync files on the exclude list
                    let valid_sync = true;
                    Watch.SYNC_EXCLUDE.forEach((exclude) => {
                        if (filename.includes(exclude)) {
                            this.logBox(
                                `× ${exclude}: ${this.app.basePath(watch_folder + filename)}`,
                                this.dlog,
                                "error"
                            );
                            valid_sync = false;
                            return;
                        }
                    });
                    if (!valid_sync) return;

                    // clear previously scheduled syncs
                    if (folder in watch_timer) {
                        clearTimeout(watch_timer[folder]);
                    }

                    // log file event
                    this.logBox(
                        `${chalk.hex("#5588AA")(
                            eventType.toUpperCase()
                        )} ${this.app.basePath(watch_folder + filename)}`
                    );

                    // queue changed file to sync list
                    if (!(folder in watch_files)) {
                        watch_files[folder] = [];
                    }
                    watch_files[folder].push(filename);

                    // run sync soon
                    watch_timer[folder] = setTimeout(async () => {
                        if (watch_files[folder].length == 1) {
                            this.build_sync(
                                folder,
                                watch_folder,
                                watch_files[folder][0]
                            );
                        } else {
                            this.build_sync(folder, watch_folder, "");
                        }
                        // ok to do this immediately upon starting build_sync
                        delete watch_timer[folder];
                        delete watch_files[folder];
                    }, 250);
                }
            );
        });
    }
    //#endregion

    /***************************************************************************
     * Server Logs
     **************************************************************************/
    //#region Server Logs

    // New method to toggle server logs
    public async toggleServerLogs(toggle = true): Promise<void> {
        if (toggle) this.isServerLogsVisible = !this.isServerLogsVisible;

        const machines = this.app.fly.getMachines();
        for (let i = 0; i < machines.length; i++) {
            if (this.isServerLogsVisible) {
                this.mlogs[i].width = "50%";
                this.serverLogs[i].show();
                this.serverLogs[i].setFront();
                this.tailServerLogs(i);
            } else {
                this.mlogs[i].width = "100%";
                this.serverLogs[i].hide();
                this.stopTailingLogs(i);
            }
            this.mlogs[i].render();
            this.serverLogs[i].render();
        }
        this.screen.render();
    }

    async tailServerLogs(index: number): Promise<void> {
        const sessionId = `serverlogs`;
        const log = (line: string, level: LogLevel = "info") => {
            this.logBox(line, this.serverLogs[index], level);
        };

        try {
            await this.app.fly.sshPipe(
                (stdout) => {
                    log(stdout);
                },
                (stderr) => {
                    log(stderr, "error");
                },
                `tail -F /var/log/clara/nes-*.log`,
                index,
                sessionId
            );
        } catch (error) {
            log(
                `Error tailing logs: ${error instanceof Error ? error.message : String(error)}`,
                "error"
            );
        }
    }

    async stopTailingLogs(index: number): Promise<void> {
        const sessionId = `serverlogs`;
        this.app.fly.stopSshSession(index, sessionId);
    }
    //#endregion
}
