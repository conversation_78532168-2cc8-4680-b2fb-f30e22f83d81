import { Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";

export default class DSLTest extends BaseCommand<typeof DSLTest> {
    static description = `Run DSL tests`;
    static examples = [`$ clara dsl test`];

    static override flags = {
        ...BaseCommand.baseFlags,
        force: Flags.boolean({
            ...BaseCommand.baseFlags.force,
            default: true, // we need this to be true in order to prevent app.fail from halting the process
        }),
    };

    public async process(): Promise<object> {
        try {
            this.app.$exec(
                "pnpm --silent test commands/dsl",
                this.app.app_root
            );
            return { success: true };
        } catch (error) {
            this.app.fail(
                `Test execution failed: ${error instanceof Error ? error.message : String(error)}`
            );
            return {
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }
}
