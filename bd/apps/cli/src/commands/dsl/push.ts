import { BaseCommand } from "@core/base";
import { Flags } from "@oclif/core";

export default class DSL extends BaseCommand<typeof DSL> {
    static description = `Validate, Compile, and Push DSL`;
    static examples = [
        `$ clara dsl push --host=xxx --key=[NES_API_KEY]`,
        `$ clara dsl push --host=xxx --key=[NES_API_KEY] --clear`,
        `$ clara dsl push --host=xxx --key=[NES_API_KEY] --force`,
        `$ clara dsl push --host=xxx --key=[NES_API_KEY] --clear --force`,
        `$ clara dsl push --forms=patient --host=xxx --key=[NES_API_KEY]`,
        `$ clara dsl push --forms=patient,encounter --host=xxx --key=[NES_API_KEY] --force`,
        `$ clara dsl push --forms=patient,list_us_state --host=xxx --key=[NES_API_KEY] --clear --force`,
    ];

    static override flags = {
        clear: Flags.boolean({
            char: "C",
            summary: "Clear DSL cache",
            required: false,
            description: "Clear DSL cache (.cson, .json files etc.)",
        }),
        customer: Flags.string({
            char: "c",
            summary: "Specify customer",
            required: false,
            description: "Set the customer identifier (e.g., abc)",
        }),
        forms: Flags.string({
            char: "f",
            summary: "Forms to push",
            required: false,
            description: "Forms to push (e.g., --forms=patient,encounter)",
        }),
        host: Flags.string({
            char: "h",
            summary: "NES Host URL",
            required: true,
            description: "NES URL to push DSL to",
        }),
        key: Flags.string({
            char: "k",
            summary: "Specify API key",
            required: true,
            description: "Set the API key for authentication",
        }),
        dslpath: Flags.string({
            char: "p",
            summary: "Specify path",
            required: false,
            description: "Set the path to the DSL file",
        }),
    };

    public async process(): Promise<object> {
        const { flags } = await this.parse(DSL);
        if (!this.app.dsl) {
            this.app.warn(
                "DSL module is only available in development/test environments."
            );
            return {
                error: "DSL module not loaded.",
            };
        }

        await this.app.dsl.init();
        const forms = this.app.dsl.make(flags);
        const formsToPush = flags.forms ? flags.forms.split(",") : forms;
        if (flags.forms && formsToPush.length > 0) {
            const missingForms = formsToPush.filter(function (n) {
                return forms.indexOf(n) === -1;
            });
            if (missingForms.length > 0) {
                this.app.fail(`\nDSL MISSING:\n× ${missingForms.join("\n× ")}`);
            }
        }
        //TODO - push forms to NES
        return forms;
    }
}
