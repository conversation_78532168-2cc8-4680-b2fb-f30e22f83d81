import { Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";

export default class DSL extends BaseCommand<typeof DSL> {
    static description = `Validate & Compile DSL:
        [no arguments]: Compile changed CSON`;
    static examples = [
        `$ clara dsl`,
        `$ clara dsl --clear`,
        `$ clara dsl --customer=csp`,
        `$ clara dsl --customer=csp --clear`,
        `$ clara dsl --warnings`,
    ];

    static override flags = {
        clear: Flags.boolean({
            char: "C",
            summary: "Clear DSL cache",
            required: false,
            description: "Clear DSL cache (.cson, .json files etc.)",
        }),
        customer: Flags.string({
            char: "c",
            summary: "Specify customer",
            required: false,
            description: "Set the customer identifier (e.g., abc)",
        }),
        dslpath: Flags.string({
            char: "p",
            summary: "Specify path",
            required: false,
            description: "Set the path to the DSL file",
        }),
        warnings: Flags.boolean({
            char: "w",
            summary: "Run parser warnings checks",
            required: false,
            description:
                "Enable running of parser warnings checks during validation",
            default: false,
        }),
    };

    public async process(): Promise<object> {
        const { flags } = await this.parse(DSL);
        if (!this.app.dsl) {
            this.app.warn(
                "DSL module is only available in development/test environments."
            );
            return {
                error: "DSL module not loaded.",
            };
        }

        await this.app.dsl.init();
        const forms = this.app.dsl.make(flags);
        //TODO - obsolete this when bd-dsl repo goes away
        // this.app.dsl.compareCSON(flags);
        return forms;
    }
}
