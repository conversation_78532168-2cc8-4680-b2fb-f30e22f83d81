import { Args, Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";

export default class C<PERSON> extends BaseCommand<typeof Cache> {
    static description = `Manage cache state`;
    static examples = [
        `$ clara cache`,
        `$ clara cache key1`,
        `$ clara cache key2 val2`,
        `$ clara cache key3 val3 3600`,
        `$ clara cache --clear`,
        `$ clara cache --status`,
    ];

    static override args = {
        key: Args.string({
            description: "Key to get/set",
            required: false,
        }),
        value: Args.string({
            description: "Value to set",
            required: false,
        }),
        expire: Args.integer({
            description: "Expire time in seconds",
            required: false,
        }),
    };

    static override flags = {
        clear: Flags.boolean({
            char: "C",
            summary: "Clear cache",
            required: false,
            description: "Clear cache",
        }),
        status: Flags.boolean({
            char: "s",
            summary: "Show status",
            required: false,
            description: "Show cache status (default)",
        }),
    };

    public async process(): Promise<object> {
        const { args, flags } = await this.parse(Cache);

        if (flags.clear) {
            // --clear

            this.app.info("Clearing cache...");
            this.app.cache.clear();
            this.app.info("Cache cleared.");
            return {};
        } else if (flags.status || Object.keys(args).length === 0) {
            // --status or no args

            this.app.info("Cache status:");
            const keys = this.app.cache.getAll("");
            this.app.info(`  Keys : ${Object.keys(keys).length}`);
            this.app.info(`  Bytes: ${JSON.stringify(keys).length - 2}`);
            return keys;
        } else if (args.key) {
            // key [value] [expire]

            this.app.info(`Key ${args.key}:`);
            if (args.value) {
                // key value [expire]

                this.app.info(`New Value: ${args.value}`);
                if (args.expire) {
                    this.app.cache.set(args.key, args.value, args.expire);
                } else {
                    this.app.cache.set(args.key, args.value);
                }
                const ret: Record<string, any> = {};
                ret[args.key] = args.value;
                return ret;
            } else {
                // key

                const kv = this.app.cache.getAll(args.key)[args.key];
                const ret: Record<string, any> = {};
                if (kv) {
                    this.app.info(`Value: ${kv.value}`);
                    ret[args.key] = kv;
                } else {
                    this.app.info(`Not found: ${args.key}`);
                }
                return ret;
            }
        }
        return {};
    }
}
