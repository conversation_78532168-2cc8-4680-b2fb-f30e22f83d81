import { BaseCommand } from "@core/base";

export default class Auth extends BaseCommand<typeof Auth> {
    static description = `Manage credentials to access Clara stack:
      [no arguments]: Show existing auth`;
    static examples = [
        `$ clara auth`,
        `$ clara auth admin`,
        `$ clara auth fly`,
        `$ clara auth nes`,
    ];

    public async process(): Promise<object> {
        const { args } = await this.parse(Auth);
        if (Object.keys(args).length === 0) {
            this.app.info("Clara CLI auth:");
            //TODO: Implement
        }
        return {};
    }
}
