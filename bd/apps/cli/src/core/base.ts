/********************************************************************************
 * This is the base Command class that all commands inherit from. This gives
 * all commands access to the global this.app module, similar to NES this.shared.
 * It also sets up global flags and handles try/catch blocks for all commands.
 *******************************************************************************/

import { Command, Flags, Interfaces } from "@oclif/core";
import App from "./app.ts";
import { TypeMerge } from "@clara/tslib/type/generics";

/***************************************************************************
 * Type Definitions
 **************************************************************************/
//#region Types
export type Flags<T extends typeof Command> = Interfaces.InferredFlags<
    (typeof BaseCommand)["baseFlags"] & T["flags"]
>;
export type Args<T extends typeof Command> = Interfaces.InferredArgs<T["args"]>;
//#endregion

export abstract class BaseCommand<T extends typeof Command> extends Command {
    // support the --json flag in (almost) all commands
    static enableJsonFlag = true;

    // define flags that can be inherited by any command that extends BaseCommand
    static baseFlags = {
        force: Flags.boolean({
            default: false,
            helpGroup: "GLOBAL",
            summary: "Force the operation to continue after errors.",
        }),
        dev: Flags.boolean({
            default: false,
            helpGroup: "GLOBAL",
            summary: "Enable developer mode for debugging.",
        }),
    };

    protected flags!: Flags<T>;
    protected args!: Args<T>;

    public dev: boolean = false;
    public forced: boolean = false;
    public jsonOutput: boolean = false;

    /***************************************************************************
     * Initiate the app
     **************************************************************************/
    //#region Init

    public app: App;

    constructor(
        public argv: string[],
        public config: any
    ) {
        super(argv, config);
        this.app = new App(this);
    }

    public async init(): Promise<void> {
        // called before the command is executed, set up global state
        await super.init();

        // add any custom logic to parse arguments or flags
        const { args, flags } = await this.parse({
            flags: this.ctor.flags,
            baseFlags: (super.ctor as typeof BaseCommand).baseFlags,
            enableJsonFlag: this.ctor.enableJsonFlag,
            args: this.ctor.args,
            strict: this.ctor.strict,
        });
        this.flags = flags as Flags<T>;
        this.args = args as Args<T>;

        // set dev mode if run via ./dev.js which sets development:true
        // or if --dev is passed as a flag
        if (
            ("NODE_ENV" in process.env &&
                process.env.NODE_ENV == "development") ||
            (this.flags && "dev" in this.flags && this.flags.dev)
        ) {
            this.dev = true;
        }

        if (this.flags) {
            // set forced mode if --force is passed as a flag
            this.forced = "force" in this.flags && this.flags.force;
            // set json mode if --json is passed as a flag
            this.jsonOutput =
                "json" in this.flags && (this.flags.json || false);
        }

        // initialize the global app once args/flags are available
        // since app uses this.flags
        await this.app.init();
    }
    //#endregion

    /***************************************************************************
     * Getters
     **************************************************************************/
    //#region Getters

    public getArgs(): any {
        return this.args;
    }

    public getFlags(): any {
        return this.flags;
    }

    //#endregion

    /***************************************************************************
     * Command entry points
     **************************************************************************/
    //#region Command

    // all command .ts files should implement this as their main entry point.
    // return values will be logged as JSON if --json is passed.
    public abstract process(): Promise<object>;

    // we override the run() here to allow us to return the full log with
    // return values if --json is passed
    public async run(): Promise<object> {
        const time_start = performance.now();
        const result = await this.process();
        const time_end = performance.now();

        if (this.dev) {
            this.app.info(
                {
                    type: "duration",
                    start: time_start,
                    end: time_end,
                    duration: time_end - time_start,
                    message: `Command completed in ${(
                        time_end - time_start
                    ).toFixed(2)}ms`,
                },
                `\nCommand completed in ${(time_end - time_start).toFixed(2)}ms`
            );
        }
        return this.app.getJSONLog(result);
    }
    //#endregion

    /***************************************************************************
     * Error handling
     **************************************************************************/
    //#region Error

    protected async catch(
        err: TypeMerge<Error & { exitCode?: number }>
    ): Promise<any> {
        // add any custom logic to handle errors from the command
        // or simply return the parent class error handling
        return super.catch(err);
    }

    protected async finally(_: Error | undefined): Promise<any> {
        // called after run and catch regardless of whether or not the command errored
        return super.finally(_);
    }
    //#endregion
}
