/* eslint-disable no-process-exit */
/********************************************************************************
 *  This is the shared, globally available "this.app" object for all commands.
 *  Standard utility functions or references to custom modules can be added here.
 *******************************************************************************/

import { BaseCommand } from "./base.ts";
import Cache from "./cache.ts";
import DSL from "./dsl/index.ts";
import Fly from "./fly.ts";
import { fileURLToPath } from "url";

import { Command } from "@oclif/core";
import { execa } from "execa";
import { execSync } from "child_process";
import path from "path";
import util from "util";

import { copyFileSync, existsSync, lstatSync, rmSync, utimesSync } from "fs";
import type {
    AppLogger,
    FileMatchStats,
    FileProcessing,
    JSONLog,
    LogItems,
    LogLevel,
} from "@clara/tslib";
import {
    fileExt,
    isDir,
    isFile,
    glob,
    mkdir,
    read,
    rm,
    splitPath,
    stats,
    write,
} from "@clara/tslib/utils/file";

/***************************************************************************
 * Type Definitions
 **************************************************************************/
//#region Types

const repoRootPaths = {
    "bd-dsl": "bd-dsl",
    bd: "bd",
} as const;

type repoRootKey = keyof typeof repoRootPaths | null;

//#endregion

export default class App implements AppLogger {
    // mirroring these from base.ts for convenience
    public dev: boolean = false;
    public forceContinue: boolean = false;
    public jsonOutput: boolean = false;

    // Modules
    public cache!: Cache;
    public dsl!: DSL;
    public fly!: Fly;

    // App/Module paths
    public app_root!: string;
    public repo_root!: string;
    public repo_parent!: string;

    private paths: Record<string, string> = {
        cache: "./.cache",
        temp: "./.tmp",
    };

    // Maintain full message log for JSON
    private error_log: LogItems = {
        error: [],
        warn: [],
        info: [],
    };

    // Override error handling
    public override_handler:
        | ((message: string, level: LogLevel) => void)
        | null = null;

    /***************************************************************************
     * Initiate the app
     **************************************************************************/
    //#region Init

    // App is always called with the called command object
    constructor(public command: BaseCommand<typeof Command>) {}

    public async init(): Promise<void> {
        // Set app / repo paths
        this.app_root = path.join(path.resolve(this.command.config.root), "./");
        this.repo_root = path.join(this.app_root, "../../");
        this.repo_parent = path.join(this.repo_root, "../");

        // Mirror state/flags from base command
        ["dev", "forceContinue", "jsonOutput"].forEach((state) => {
            if (state in this.command) {
                (this as any)[state] = (this.command as any)[state];
            }
        });

        // Create app directories (cache, temp etc.) if they don't exist.
        const rundir =
            path.dirname(new URL(import.meta.url).pathname) + "/../../";
        for (const [dirtype, dirpath] of Object.entries(this.paths)) {
            const pathdir = path.resolve(rundir, dirpath);
            try {
                this.mkdir(pathdir);
                const stats = lstatSync(pathdir);
                if (!stats.isDirectory()) {
                    this.fatal(`Path must be a directory: ${pathdir}`);
                }
                this.paths[dirtype] = pathdir;
            } catch (err) {
                this.fatal(
                    `Error creating directory: ${err instanceof Error ? err.message : String(err)}`
                );
            }
        }

        // REQUIREMENT: ALWAYS send `this` as the first argument to the constructor
        // of all custom modules, so that they can access the app object.

        // SQLite cache for storing auth, CSON etc. data.
        this.cache = new Cache(this, this.paths.cache);
        await this.cache.init();

        // Modules only for development/test environments on dev machines
        if (
            (this.isNodeEnv("development") || this.isNodeEnv("testing")) &&
            process.env["CLARA_HOME"] != "/opt/clara"
        ) {
            // DSL compiler & deployer
            this.dsl = new DSL(
                this,
                this.repoPath(
                    "jsshared/src/coffee/models/dsl/dsl-defaults.coffee"
                ),
                this.repoPath("nes/src/core/modules/dsl.js"),
                [this.repoPath("dsl/"), this.repoPath("../bd-dsl/")],
                "packages/dsl/src/base" //CM:2025-05-23 - do not add trailing slash to this
            );

            // Fly.io task handler
            this.fly = new Fly(this);
        }
    }
    //#endregion

    /***************************************************************************
     * Command line
     **************************************************************************/
    //#region Command

    // Use execa to run shell commands
    public $ = execa;

    // Use child_process to run shell commands
    public $exec(cmd: string, cwd?: string): void {
        const options: Record<string, any> = { stdio: "inherit" };
        if (cwd) {
            options.cwd = cwd;
        }
        try {
            execSync(cmd, options);
        } catch (err) {
            this.fail(
                `Error executing command: ${err instanceof Error ? err.message : String(err)}`
            );
        }
    }

    // execa shorthand with:
    //   i/o pipes, shell: true, reject: true
    //   try/catch for error handling
    public $pipe(
        outlog: (data: string) => void,
        errlog: (data: string) => void,
        repo: repoRootKey = null,
        checkExitCode: boolean = true
    ) {
        return async (
            strings: TemplateStringsArray,
            ...values: any[]
        ): Promise<void> => {
            try {
                const targetDir =
                    repo && repo in repoRootPaths
                        ? this.repo_parent + repoRootPaths[repo]
                        : this.app_root;
                const command = String.raw(strings, ...values);
                const proc = this.$(command, {
                    shell: true,
                    reject: checkExitCode,
                    stdio: ["ignore", "pipe", "pipe"],
                    cwd: targetDir,
                    env: {
                        ...process.env,
                        FORCE_COLOR: "1",
                    },
                });

                if (proc.stdout) {
                    proc.stdout.on("data", (data: Buffer | string) => {
                        outlog(Buffer.isBuffer(data) ? data.toString() : data);
                    });
                }

                if (proc.stderr) {
                    proc.stderr.on("data", (data: Buffer | string) => {
                        errlog(Buffer.isBuffer(data) ? data.toString() : data);
                    });
                }

                await proc;
            } catch (error) {
                errlog(error instanceof Error ? error.message : String(error));
            }
        };
    }

    // execa shorthand with:
    //   shell: true, reject: true
    //   try/catch for error handling
    public async $shell(
        strings: TemplateStringsArray,
        ...values: any[]
    ): Promise<{
        stdout: string;
        stderr: string;
    }> {
        let stdout = "",
            stderr = "";

        try {
            const __filename = fileURLToPath(import.meta.url);
            const __dirname = path.dirname(__filename);
            const targetDir = path.resolve(__dirname, "../../../");

            const options = {
                shell: true,
                reject: true,
                cwd: targetDir,
            };
            const command = String.raw(strings, ...values);
            const { stdout: cmdout, stderr: cmderr } = await execa(
                command,
                options
            );

            stdout = cmdout;
            stderr = cmderr;
        } catch (err) {
            stderr = String(err);
        }
        return { stdout, stderr };
    }

    // This has nothing to do with --prod or --dev params
    // It is matching against the NODE_ENV; default is development
    public isNodeEnv(type: string): boolean {
        return (process.env["NODE_ENV"] || "development") == type;
    }
    //#endregion

    /***************************************************************************
     * File system shared
     **************************************************************************/
    //#region FSShared

    public fileExt = fileExt;
    public glob = glob;
    public isDir = isDir;
    public isFile = isFile;
    public mkdir = mkdir;
    public splitPath = splitPath;
    public stats = stats;

    // caller should handle nulls on failure
    public read(file: string): string | null {
        return read(file, this);
    }

    public rm(file: string): void {
        rm(file, this);
    }

    public write(file: string, data: string): void {
        write(file, data, this);
    }

    //#endregion

    /***************************************************************************
     * File system CLI specific
     **************************************************************************/
    //#region FSCLI

    public appPath(filepath: string = ""): string {
        return path.join(this.app_root, filepath);
    }

    public basePath(filepath: string = ""): string {
        return filepath.replace(this.repo_parent, "");
    }

    public chdir(dir = ""): string {
        if (dir) {
            process.chdir(dir);
        } else {
            // this will set current dir to the home ../ directory of the script
            process.chdir(this.app_root);
        }
        return process.cwd();
    }

    public copyNewer(src: string, dst: string): FileProcessing {
        const src_stat = existsSync(src) ? this.stats(src) : null;
        const dst_stat = existsSync(dst) ? this.stats(dst) : null;
        let dst_total = 0,
            dst_failed = 0,
            dst_unmodified = 0,
            dst_compiled = 0;

        const match =
            src_stat &&
            dst_stat &&
            Math.abs(src_stat.mtime - dst_stat.mtime) < 200 &&
            src_stat.size == dst_stat.size;
        if (!match) {
            try {
                copyFileSync(src, dst);
                utimesSync(
                    dst,
                    src_stat?.mtime ? src_stat.mtime / 1000 : 0,
                    src_stat?.mtime ? src_stat.mtime / 1000 : 0
                );
                dst_compiled++;
                dst_total++;
            } catch (err) {
                this.fail(err);
                dst_failed++;
                dst_total++;
            }
        } else {
            dst_unmodified++;
            dst_total++;
        }

        return {
            Destination: dst,
            Failed: dst_failed,
            Unmodified: dst_unmodified,
            Compiled: dst_compiled,
            Total: dst_total,
        };
    }

    public copyNewerRecursive(src: string, dst: string): FileProcessing {
        const src_files = this.glob(src + "**");
        let dst_total = 0,
            dst_failed = 0,
            dst_unmodified = 0,
            dst_compiled = 0;

        for (const file of src_files) {
            const rel_file = file.replace(src, "");
            if (file.length > rel_file.length) {
                if (this.isDir(file)) {
                    if (!this.isDir(dst + rel_file)) {
                        this.mkdir(dst + rel_file);
                        dst_compiled++;
                    } else {
                        dst_unmodified++;
                    }
                } else {
                    const copy_stat = this.copyNewer(file, dst + rel_file);
                    dst_failed += copy_stat["Failed"];
                    dst_unmodified += copy_stat["Unmodified"];
                    dst_compiled += copy_stat["Compiled"];
                }
            } else {
                dst_unmodified++;
            }
            dst_total++;
        }

        return {
            Destination: dst,
            Failed: dst_failed,
            Unmodified: dst_unmodified,
            Compiled: dst_compiled,
            Total: dst_total,
        };
    }

    // Glob an array of patterns and return mtime and file count.
    // If destCache is provided, compare results with previous
    //   and return Date.now() if there is any difference
    public globLatest(
        pattern: string[] | string,
        options: object = {},
        destCache: string | null = null
    ): FileMatchStats {
        // find all files
        const globs: Record<string, FileMatchStats> = {};
        const file_matches = this.glob(pattern, options);

        // find latest mtime and build file list if destCache
        let latest_mtime = file_matches.reduce((latest, current) => {
            const cur_stats = this.stats(current);
            const cur_mtime = cur_stats.mtime;
            if (destCache) globs[current] = cur_stats;
            return latest ? Math.max(latest, cur_mtime) : cur_mtime;
        }, 0);

        // overwrite the latest_mtime if anything changed vs. cached dest
        if (destCache) {
            const key_glob = "glob_" + destCache;
            const new_glob = JSON.stringify(globs);
            const get_glob = this.cache.getAll(key_glob);
            const old_glob =
                get_glob && key_glob in get_glob
                    ? get_glob[key_glob].value
                    : null;
            if (new_glob != old_glob) {
                this.cache.set(key_glob, new_glob);
                latest_mtime = Date.now();
            }
        }

        return {
            mtime: latest_mtime,
            size: file_matches.length,
        };
    }

    public repoPath(filepath: string = ""): string {
        return path.join(this.repo_root, filepath);
    }

    public rmdir(dir: string): Array<string> {
        const removed = [];
        const rmpath = path.resolve(dir);
        if (rmpath.indexOf(this.repo_root) < 0) {
            this.fail(
                `Cannot remove path outside of repo subfolders: ${rmpath}`
            );
        }
        if (existsSync(rmpath)) {
            try {
                rmSync(rmpath, { recursive: true, force: true });
                removed.push(rmpath);
            } catch (err) {
                this.fail(
                    `Cannot remove path: ${rmpath}\n${JSON.stringify(err)}`
                );
            }
        }
        return removed;
    }

    public async sleep(timeMs = 1000): Promise<void> {
        await new Promise((resolve) => setTimeout(resolve, timeMs));
    }

    //#endregion

    /***************************************************************************
     * Logging
     **************************************************************************/
    //#region Log

    // Main logging function - do not use anything else!
    // This will handle --json and --force properly
    // Returns true if --force, so caller can continue operation
    private handleLog(
        e: any,
        m: string = "",
        level: LogLevel = "info"
    ): boolean {
        if (this.jsonOutput) {
            // if e is string and level is info, convert to {message:e}
            this.error_log[level].push(
                typeof e === "string" && level === "info" ? { message: e } : e
            );
            return this.forceContinue;
        } else if (m) {
            e = m;
        }

        const log = this.override_handler
            ? JSON.stringify(e)
            : this.dev
              ? e
              : JSON.parse(JSON.stringify(e));

        switch (level) {
            case "error":
                if (this.override_handler) {
                    this.override_handler(log, "error");
                } else {
                    this.command.logToStderr(log);
                    if (!this.forceContinue) {
                        // call node process instead of this.command.exit(1)
                        // because we do not want any more error logging at this time
                        // since --json gets returned above and there is no --force here
                        process.exit(1);
                    }
                }
                break;
            case "warn":
                if (this.override_handler) {
                    this.override_handler(log, "warn");
                } else {
                    this.command.warn(log);
                }
                break;
            case "info":
                if (this.override_handler) {
                    this.override_handler(log, "info");
                } else {
                    this.command.log(log);
                }
                break;
        }

        return this.forceContinue;
    }

    public errorJSONLog(
        // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
        error: Error | unknown,
        results: JSONLog | null
    ): never {
        const formattedResults = results
            ? util.inspect(results, {
                  depth: null,
                  colors: true,
                  maxArrayLength: null,
              })
            : "No results available";

        const errorMessage =
            error instanceof Error ? error.message : String(error);

        const enhancedMessage = `${errorMessage}\n\nTest Results:\n${formattedResults}`;

        if (error instanceof Error) {
            error.message = enhancedMessage;
            throw error;
        }

        throw new Error(enhancedMessage);
    }

    public getJSONLog(data: any = null): JSONLog {
        return {
            data: data,
            log: this.error_log,
        };
    }

    // log error and immediately exit even if --force
    // if --json, print JSON log and exit since command return won't be evaluated
    public fatal(e: any, m: string = ""): void {
        this.handleLog(e, m, "error");
        if (this.jsonOutput) {
            console.log(JSON.stringify(this.getJSONLog()));
        }
        process.exit(1);
    }

    // log error but continue if --force
    public fail(e: any, m: string = ""): boolean {
        return this.handleLog(e, m, "error");
    }

    // log message
    public info(e: any, m: string = ""): boolean {
        return this.handleLog(e, m, "info");
    }

    // log warning, don't error out
    public warn(e: any, m: string = ""): boolean {
        return this.handleLog(e, m, "warn");
    }
    //#endregion
}
