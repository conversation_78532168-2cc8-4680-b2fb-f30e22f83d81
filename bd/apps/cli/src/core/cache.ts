/*****************************************************************************
 * This is the globally available "this.app.cache" object for all commands.
 * It uses SQLite 3 to store data in a file called "clara-cli.sqlite" in the
 * /.cache directory. Runs GC on startup to remove expired entries.
 *****************************************************************************/

import Database, { Database as SQLite } from "better-sqlite3";
import { existsSync, unlinkSync } from "fs";
import path from "path";

import type { FileCache, FileMatchStats } from "@clara/tslib/type/file";
import type App from "./app.ts";

/***************************************************************************
 * Type Definitions
 **************************************************************************/
//#region Types

type CacheEntry = {
    key: string;
    value: string;
    expires_at: number;
    meta: object;
};
type CacheMap = Record<string, CacheEntry>;
//#endregion

export default class Cache implements FileCache {
    private app: App;
    private DB_PATH: string;

    private db: SQLite | null = null;
    private runtime_cache: CacheMap = {};

    /***************************************************************************
     * Setup the cache with indexes and GC.
     **************************************************************************/
    //#region Init
    constructor(app: App, rundir: string) {
        this.app = app;
        this.DB_PATH = path.resolve(rundir, "clara-cli.sqlite");
    }

    public async init(): Promise<void> {
        try {
            this.db = new Database(this.DB_PATH);
            await this.db.pragma("journal_mode = WAL");
            this.db
                .prepare(
                    `CREATE TABLE IF NOT EXISTS cache (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        expires_at INTEGER,
                        meta TEXT
                        );`
                )
                .run();
            this.db
                .prepare(
                    `CREATE INDEX IF NOT EXISTS idx_cache_expires_at
                         ON cache (expires_at);`
                )
                .run();
            this.gc();
        } catch (err) {
            this.app.fatal(`Error initializing the database: ${err}`);
        }
    }

    //#endregion

    /***************************************************************************
     * Standard key/value store opeartions.
     **************************************************************************/
    //#region Key/Value

    public clear(prefix: string = ""): void {
        if (prefix) {
            if (!this.db) {
                this.app.fatal("Database not initialized");
                return;
            }
            // Delete the database entries with matching prefix
            const stmt = this.db.prepare(`DELETE FROM cache WHERE key LIKE ?;`);
            stmt.run(`${prefix}%`);

            // Clear the runtime cache entries with matching prefix
            Object.keys(this.runtime_cache).forEach((key) => {
                if (key.startsWith(prefix)) {
                    delete this.runtime_cache[key];
                }
            });
        } else {
            this.close();
            ["", "-shm", "-wal"].map((suffix) => {
                const dbfile = `${this.DB_PATH}${suffix}`;
                if (existsSync(dbfile)) {
                    try {
                        unlinkSync(dbfile);
                    } catch (err) {
                        this.app.fatal(
                            `Error deleting the database file: ${err}`
                        );
                    }
                }
            });
        }
    }

    public gc(): void {
        const expiresAt = Date.now() - 5000; // 5 seconds ago
        if (!this.db) {
            this.app.fatal("Database not initialized");
            return;
        }
        this.db
            .prepare(`DELETE FROM cache WHERE expires_at < ?;`)
            .run([expiresAt]);
        this.runtime_cache = {};
    }

    public get(key: string): null | string {
        if (!this.db) {
            this.app.fatal("Database not initialized");
            return null;
        }
        if (key in this.runtime_cache) {
            // Check if non-wildcard key is in the runtime cache
            return this.runtime_cache[key].value;
        }

        // Query the DB if key not in runtime cache
        const stmt = this.db.prepare(
            `SELECT key, value, expires_at, meta
                        FROM cache
                        WHERE key = ? AND expires_at > ?;`
        );
        const row: any = stmt.get(key, Date.now());
        if (row) {
            const { key, value, meta, expires_at } = row;
            const meta_json = JSON.parse(meta ? meta : {});
            const cache_entry = {
                key,
                value,
                expires_at,
                meta: meta_json,
            };
            this.runtime_cache[key] = cache_entry;
            return value;
        }
        return null;
    }

    public getAll(key: string): CacheMap {
        if (!this.db) {
            this.app.fatal("Database not initialized");
            return {} as CacheMap;
        }

        const results: CacheMap = {};

        // Always query the DB
        const stmt = this.db.prepare(
            `SELECT key, value, expires_at, meta
                        FROM cache
                        WHERE key LIKE ? AND expires_at > ?;`
        );

        stmt.all(`${key}%`, Date.now()).forEach((row: any) => {
            const { key, value, meta, expires_at } = row;
            const meta_json = JSON.parse(meta ? meta : {});
            const cache_entry = {
                key,
                value,
                expires_at,
                meta: meta_json,
            };
            results[key] = cache_entry;
            this.runtime_cache[key] = cache_entry;
        });
        return results;
    }

    public set(
        key: string,
        value: string,
        expiration: number = 86400 * 3650, // 10 years in seconds
        meta: object = {}
    ): void {
        const expires_at = Date.now() + expiration * 1000;
        const metaFlat = JSON.stringify(meta ? meta : {});
        if (!this.db) {
            this.app.fatal("Database not initialized");
            return;
        }
        this.db
            .prepare(
                `INSERT INTO cache (key, value, expires_at, meta) VALUES (?, ?, ?, ?)
                    ON CONFLICT(key)
                        DO UPDATE SET
                            value = excluded.value,
                            meta = excluded.meta,
                            expires_at = excluded.expires_at;`
            )
            .run([key, value, expires_at, metaFlat]);
        const cache_entry = { key, value, expires_at, meta };
        this.runtime_cache[key] = cache_entry;
    }
    //#endregion

    /***************************************************************************
     * File cache based on meta-data
     **************************************************************************/
    //#region File Cache
    public fileCacheStats(file: string): {
        key: string;
        stats: FileMatchStats;
    } {
        const key = this.app.basePath(file);
        const stats = this.app.stats(file);
        return { key, stats };
    }

    public getFileCache(file: string): object | string | null {
        const { key, stats: stats_now } = this.fileCacheStats(file);

        // cache everything in RAM first time this is called
        // because we don't want to keep looking up the file cache
        // when code is run in a loop for thousands of files
        let cache_primed = false;
        for (const _ in this.runtime_cache) {
            cache_primed = true;
            break;
        }
        if (!cache_primed) {
            this.getAll("");
        }

        // check if the file is in the cache and not dirty
        let cached: CacheEntry;
        if (key in this.runtime_cache) {
            cached = this.runtime_cache[key];
        } else {
            const cachedAll = this.getAll(key);
            if (key in cachedAll) {
                cached = cachedAll[key];
            } else {
                return null;
            }
        }

        if (cached) {
            const stats_cached = cached.meta;
            if (
                stats_cached &&
                "mtime" in stats_cached &&
                "size" in stats_cached
            ) {
                const nmodt = stats_now.mtime;
                const nsize = stats_now.size;
                const cmodt = stats_cached.mtime as number;
                const csize = stats_cached.size as number;
                if (
                    nmodt <= cmodt + 1000 && // nearly same modified time
                    nsize === csize // same size
                ) {
                    return JSON.parse(cached.value);
                }
            }
        }
        return null;
    }

    public setFileCache(
        file: string,
        data: any,
        expiration: number = 86400 * 30 // 1 month
    ): void {
        const { key, stats } = this.fileCacheStats(file);
        const expires_at = Date.now() + expiration * 1000;
        this.set(key, JSON.stringify(data), expiration, stats);
        this.runtime_cache[key] = { key, value: data, expires_at, meta: stats };
    }
    //#endregion

    /***************************************************************************
     * OPTIONAL: Manually close .sqlite database.
     **************************************************************************/
    //#region Close
    public close() {
        if (this.db) {
            this.db.close();
        }
    }
    //#endregion
}
