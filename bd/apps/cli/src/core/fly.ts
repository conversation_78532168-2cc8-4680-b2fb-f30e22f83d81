/* eslint-disable @typescript-eslint/no-floating-promises */
/*****************************************************************************
 * This is the globally available "this.app.fly" object for all Fly.io tasks.
 * It loads fly.toml in repo root OR uses --app parameter if provided.
 * It caches App-related data like Machine ID list for an hour.
 *****************************************************************************/

import toml from "toml";

import path from "path";
import type App from "./app.ts";
import { ChildProcess } from "child_process";

export default class Fly {
    // Starting port for Fly Proxy processes, one per app machine
    // First proxy will be on this port, second on this + 1, etc.
    static PROXY_START_PORT = 10022;
    static noAppErr = `No Fly.io Application Name specified!
    Please make sure fly.toml file is in the root folder of the repo
      OR
    Pass the Fly.io app name (e.g. d-echirag-nes) as the command argument.`;
    static SSH_FLAGS =
        "-o StrictHostKeyChecking=no -o PreferredAuthentications=publickey -o ControlMaster=no -o ControlPath=none -o ForwardAgent=no -o LogLevel=ERROR";

    private app: App;
    private TOML_PATH: string | null = null;
    private appname: string | null = null;
    private machines: Array<Record<string, any>> = [];
    private env: Record<string, string> = {};
    private my_start_port: number = 0;
    private dsl_deploy_queue: Array<string> = [];
    private sshSessions: Map<string, ChildProcess> = new Map();

    /***************************************************************************
     * Setup the Fly handler with Toml path and App object.
     **************************************************************************/
    //#region Init

    constructor(app: App) {
        this.app = app;

        const fly_toml = path.resolve(this.app.repo_root, "fly.toml");
        if (this.app.isFile(fly_toml)) {
            this.TOML_PATH = fly_toml;
        }
    }

    // This figures out default appname via the TOML file.
    // Even if TOML file has valid appname, caller MUST run
    //   initAappMachines() to load the machine list and optionally
    //   pass args.app to override appname.
    public async init(): Promise<void> {
        try {
            if (this.TOML_PATH) {
                const toml_data = this.app.read(this.TOML_PATH);
                const data = toml.parse(toml_data || "");
                this.appname = data.app;
            }

            // Proxy port calculation is done once on load
            // It will work with multiple "fly watch" sessions
            //   for different Fly apps simultaneously as long as
            //   all the sessions are opened in a row WITHOUT
            //   closing any sessions in between.
            // In other words, for this to work, you will need to
            //   close and reopen all sessions even if you just
            //   want to close one session.
            const watches =
                Object.keys(await this.concurrentWatches()).length - 1;
            this.my_start_port = Fly.PROXY_START_PORT + watches * 100;
        } catch (err) {
            this.app.fatal(`Error loading fly.toml file: ${err}`);
        }
    }

    //#endregion

    /***************************************************************************
     * App/Machine callers
     **************************************************************************/
    //#region Get/Run

    public getAppName(): string | null {
        return this.appname;
    }

    // prepend this to all ssh and fly shell commands
    public getEnvironment(): string {
        return Object.entries(this.env)
            .map(([key, value]) => `${key}=${value}`)
            .join(" ");
    }

    public getMachines(): object[] {
        return this.machines;
    }

    public getProxyPort(index: number): number {
        return this.my_start_port + index * 2;
    }

    public async onAllMachines(
        fx: (machine: object, index: number) => Promise<void>
    ): Promise<void> {
        await Promise.all(
            this.machines.map((machine, index) =>
                fx.bind(this, machine, index)()
            )
        );
    }

    //#endregion

    /***************************************************************************
     * Fly command-line based API calls, optionally using --json flag.
     **************************************************************************/
    //#region API

    // clear .ssh/known_hosts entries and fly proxy from prior sessions
    public async clearKnownHost(
        index: number,
        log: ((message: string) => void) | null = null
    ): Promise<void> {
        const outlog = log ?? this.app.info.bind(this.app);
        const proxy_port = this.getProxyPort(index);
        outlog(`Clearing proxy ports ${proxy_port}-${proxy_port + 1}...`);

        // remove proxy from known hosts, ignore logs/errors
        await this.app.$shell`
                ssh-keygen -R '[localhost]:${proxy_port}';
                ssh-keygen -R '[localhost]:${proxy_port + 1}';
                lsof -t -iTCP:${proxy_port} -sTCP:LISTEN | xargs -r kill -9;
                lsof -t -iTCP:${proxy_port + 1} -sTCP:LISTEN | xargs -r kill -9;
                `;
    }

    public async clearKnownHosts(): Promise<void> {
        for (let i = 0; i < this.machines.length; i++) {
            await this.clearKnownHost(i);
        }
    }

    public async concurrentWatches(): Promise<Record<string, any>> {
        const { stdout, stderr } = await this.app
            .$shell`ps | grep node | grep "[f]ly watch" | awk '{print $1, $2}'`;
        if (stderr) {
            this.app.fatal(stderr);
        }
        const lines = stdout.trim().split("\n");
        const watches = lines.map((line) => {
            const [pid, cmd] = line.split(" ");
            return { pid, cmd };
        });
        return watches;
    }

    public async initAppMachines(
        args: any = null,
        reload: boolean = true,
        log: ((message: string) => void) | null = null,
        err: ((message: string) => void) | null = null
    ): Promise<void> {
        const outlog = log ?? this.app.info.bind(this.app);
        const errlog = err ?? this.app.fatal.bind(this.app);

        if (args && args.app) this.appname = args.app;
        if (!this.appname) this.app.fatal(Fly.noAppErr);
        outlog(`Using Fly.io app: ${this.appname}`);

        // load machine list from cache if available
        const machine_key = `${this.appname}_machine`;
        if (!reload) {
            const machine_list = this.app.cache.get(machine_key);
            if (machine_list) {
                this.machines = JSON.parse(machine_list);
                outlog(`Loaded ${this.machines.length} machines from cache.`);
                return;
            }
        }

        // load machine list from Fly.io API using Fly command
        const { stdout, stderr } = await this.app
            .$shell`fly machine list ${this.appname ? "--app=" + this.appname : ""} --json`;

        if (stderr) {
            errlog(
                `Error getting Fly.io machine list for ${this.appname}: ${stderr}`
            );
            return;
        }

        const machines = JSON.parse(stdout);
        if (!machines) {
            errlog(`Error getting Fly.io machine list for ${this.appname}!`);
            return;
        }
        if (machines.length === 0) {
            errlog(`No Fly.io Machines found for ${this.appname}!
Please make sure the Fly.io app is running on at least one machine instance.`);
            return;
        }

        machines.sort((a: any, b: any) => a.id.localeCompare(b.id));
        this.app.cache.set(machine_key, JSON.stringify(machines), 60 * 60);
        this.machines = machines;
        outlog(
            `Found ${this.machines.length} Fly.io Machines for ${this.appname}`
        );
    }

    public async getCommaSeparatedApp(apps: string[]): Promise<string[]> {
        const appsArray = apps;
        const verifiedApps = [];
        if (appsArray.length < 1) {
            this.app.fatal(Fly.noAppErr);
        }
        for (const app of appsArray) {
            this.app.info(`Verifying app: ${app}`);
            const { stdout, stderr } = await this.app
                .$shell`fly machine list ${this.appname ? "--app=" + this.appname : ""} --json`;
            if (stderr) {
                continue;
            }
            const machines = JSON.parse(stdout);
            if (!machines || machines.length === 0) {
                continue;
            }
            this.app.info(
                `App: ${app} Verified - ${machines.length} machines found`
            );
            verifiedApps.push(app);
        }
        if (verifiedApps.length === 0) {
            this.app.fatal("No apps found for apps:" + appsArray.join(", "));
        }
        return verifiedApps;
    }
    public getHapMap(hap: string[], verifiedApps: string[]): object {
        const hapArray = hap;
        if (hapArray.length != verifiedApps.length) {
            this.app.fatal("App names should be equal to haps");
        }
        const hapMap: Record<string, string> = {};
        // TODO: ideally get hap of apps from appSmith
        for (const [idx, hap] of hapArray.entries()) {
            hapMap[verifiedApps[idx]] = hap;
        }
        return hapMap;
    }
    public async deployFly(
        apps: string[],
        image?: string,
        buildTarget?: string
    ): Promise<void> {
        if (apps.length < 1) {
            this.app.fatal(Fly.noAppErr);
        }
        for (const app of apps) {
            this.app.info(`Deploying ${image} to Fly.io app ${app}...`);

            const log = (line: string) => {
                line = line.toString();
                if (
                    line.includes(
                        "Error: failed to fetch an image or build from source:"
                    ) ||
                    line.includes(
                        "ExecaError: Command failed with exit code"
                    ) ||
                    line.toLowerCase().includes("error")
                ) {
                    this.app.info("Error: " + line);
                    this.app.fatal(line);
                }
                if (
                    line.includes("image: registry.fly.io") &&
                    line.includes("image size:")
                ) {
                    image = line.split("image: ")[1].split("image size")[0];
                }
            };
            const err = (err: string) => {
                err = err.toString();
                if (
                    err.includes(
                        "Unrecoverable error: timeout reached waiting for health checks to pass for machine"
                    ) ||
                    err.toLowerCase().includes("error")
                ) {
                    this.app.info("Error: " + err);
                    this.app.fatal(err);
                }
            };
            const command = `fly deploy -a=${app} --build-arg NODE_ENV=${buildTarget ? buildTarget : "development"} --build-target=${buildTarget ? buildTarget : "development"}${image ? ` --image ${image}` : ""} `;
            this.app.info(command + "...");
            await this.app.$pipe(
                err,
                log,
                "bd"
            )`fly deploy -a=${app} --build-arg NODE_ENV=${buildTarget ? buildTarget : "development"} --build-target=${buildTarget ? buildTarget : "development"}${image ? ` --image ${image}` : ""} `;
            if (image) {
                this.dsl_deploy_queue.push(app);
            }
        }
        this.app.info(
            "Fly Deploy Done For " + apps.join(", ") + " using image "
        );
        this.app.info("\t" + (image ? image : "-") + "\n");
    }

    public async deployDSL(
        apps: string[],
        hap?: Record<string, string>
    ): Promise<void> {
        const dsl_deployed = [];
        for (const app of apps) {
            const log = (_line: string) => {};
            const err = (_err: string) => {};
            if (hap && app in hap && hap[app] && hap[app].length > 1) {
                try {
                    this.app.info(
                        `grunt deploy --fqdn=${app}.fly.dev --hap=${hap[app]} --customer=xxx...`
                    );
                    await this.app.$pipe(
                        err,
                        log,
                        "bd-dsl" //command cwd
                    )`grunt deploy --fqdn=${app}.fly.dev --hap=${hap[app]} --customer=xxx `;
                    dsl_deployed.push(app);
                } catch (error) {
                    this.app.info("Error: " + String(error));
                }
            } else {
                this.app.info(
                    `\n No hap found to ${app}. Ignoring DSL Deployment \n`
                );
            }
        }
        if (dsl_deployed.length > 0)
            this.app.info(`Grunt Deploy Done for ${dsl_deployed}`);
    }
    // Reissues SSH key for our organization, to be used per machine per app
    //   otherwise parallel SSH calls will not work
    public async issueCertificate(
        log: ((message: string) => void) | null = null,
        wrn: ((message: string) => void) | null = null,
        err: ((message: string) => void) | null = null
    ): Promise<void> {
        const outlog = log ?? this.app.info.bind(this.app);
        const wrnlog = wrn ?? this.app.warn.bind(this.app);
        const errlog = err ?? this.app.fail.bind(this.app);

        try {
            // start a new ssh-agent and add the key to it
            // get the SSH_ env vars and later use it to run ssh
            // NOTE: Avoid app.$pipe since we want to capture the output
            const { stdout, stderr } = await this.app.$shell`
                    eval "$(ssh-agent -s)";
                    fly ssh issue --agent --hours=24 -o=envoy-labs;
                    ssh-add -l;
                    echo SSH_AGENT_PID=$SSH_AGENT_PID;
                    echo SSH_AUTH_SOCK=$SSH_AUTH_SOCK`;

            if (stdout) outlog(stdout);
            if (stderr) {
                errlog(stderr);
                return;
            }

            // Extract environment variables from the output
            const env = stdout
                .split("\n")
                .reduce((ev: Record<string, string>, line: string) => {
                    const match = line.match(/^([^=]+)=(.+)$/);
                    if (match && match.length && match[1].includes("SSH_")) {
                        ev[match[1]] = match[2];
                    }
                    return ev;
                }, {});

            if (env["SSH_AGENT_PID"] && env["SSH_AUTH_SOCK"]) {
                wrnlog("SSH Key issued.");
                this.env = env;
                return;
            } else {
                errlog("Failed to capture SSH_AGENT_PID and SSH_AUTH_SOCK.");
            }
        } catch (err) {
            errlog(`ERROR: ${err}`);
        }
        return;
    }

    public async restartProxy(
        machine: Record<string, any>,
        index: number = 0,
        log: ((message: string) => void) | null = null,
        wrn: ((message: string) => void) | null = null,
        err: ((message: string) => void) | null = null
    ): Promise<boolean> {
        const outlog = log ?? this.app.info.bind(this.app);
        const wrnlog = wrn ?? this.app.warn.bind(this.app);
        const errlog = err ?? this.app.fail.bind(this.app);

        // clear .ssh/known_hosts entries and fly proxy from prior sessions
        await this.clearKnownHost(index, outlog);

        // starting local SSH proxy to Fly machine
        const proxy_port = this.getProxyPort(index);
        outlog(`Starting SSH proxy on port ${proxy_port}...`);
        let proxyStatus = "off";
        const sub_ssh = this.app.$({
            reject: false,
            detached: true,
            stdio: ["ignore", "pipe", "pipe"],
        })`
            fly proxy ${proxy_port}:22 ${machine["private_ip"]}
                -o=envoy-labs ${this.appname ? "--app=" + this.appname : ""}`;

        sub_ssh.stdout?.on("data", (data) => {
            outlog(`${data}`.trim());
            proxyStatus = "on";
        });

        sub_ssh.stderr?.on("data", (data) => {
            errlog(`Error starting SSH proxy on port ${proxy_port}: ${data}!`);
            proxyStatus = "err";
        });

        sub_ssh.unref();

        await new Promise((resolve) => {
            const interval = setInterval(() => {
                if (proxyStatus != "off") {
                    clearInterval(interval);
                    resolve(true);
                }
            }, 10);
        });
        if (proxyStatus == "err") return false;

        // starting local rsync proxy to Fly machine
        outlog(`Starting rsync proxy on port ${proxy_port + 1}...`);
        proxyStatus = "off";
        const sub_rsync = this.app.$({
            reject: false,
            detached: true,
            stdio: ["ignore", "pipe", "pipe"],
        })`
            fly proxy ${proxy_port + 1}:873 ${machine["private_ip"]}
                -o=envoy-labs ${this.appname ? "--app=" + this.appname : ""}`;

        sub_rsync.stdout?.on("data", (data) => {
            outlog(`${data}`.trim());
            proxyStatus = "on";
        });

        sub_rsync.stderr?.on("data", (data) => {
            errlog(
                `Error starting rsync proxy on port ${proxy_port + 1}: ${data}!`
            );
            proxyStatus = "err";
        });

        sub_rsync.unref();

        await new Promise((resolve) => {
            const interval = setInterval(() => {
                if (proxyStatus != "off") {
                    clearInterval(interval);
                    resolve(true);
                }
            }, 10);
        });
        if (proxyStatus == "err") return false;

        outlog(`Testing connection on proxy port ${proxy_port}...`);
        const { stdout, stderr } = await this.app.fly.ssh(
            `cd $CLARA_HOME;
            ls`,
            index
        );
        if (stdout) outlog("  ls: " + stdout.split("\n").join(" "));
        if (stderr) {
            errlog(
                `Error connecting to proxy on port ${proxy_port}: ${stderr}!`
            );
            return false;
        }

        // stream remote parcel.log
        // -- ignore errors, esp. after reconnect
        this.sshPipe(
            outlog,
            (_: string) => {},
            `tail -Fn0 /var/log/clara/parcel.log`,
            index,
            "parcel"
        );

        wrnlog(`Connected to proxy on port ${proxy_port}.`);

        return true;
    }

    // Run commands on remote machines using ssh over fly proxy
    public async ssh(
        command: string,
        index: number
    ): Promise<{
        stdout: string;
        stderr: string;
    }> {
        const proxy_port = this.getProxyPort(index);
        return await this.app
            .$shell`${this.getEnvironment()} ssh ${Fly.SSH_FLAGS} -p ${proxy_port} root@localhost 'sh -c "${command}"'`;
    }

    // Streaming commands run on remote machines using ssh over fly proxy
    public async sshPipe(
        outlog: (data: string) => void,
        errlog: (data: string) => void,
        command: string,
        index: number,
        sessionId: string = ""
    ): Promise<void> {
        const proxy_port = this.getProxyPort(index);
        if (!sessionId) {
            sessionId = Buffer.from(command).toString("base64");
        }
        const fullSessionId = `${index}-${sessionId}`;
        // Construct the SSH command
        const sshCommand = `ssh ${Fly.SSH_FLAGS} -p ${proxy_port} root@localhost 'sh -c "${command}"'`;
        outlog(`Executing SSH command: ${sshCommand}`);
        const sshProcess = this.app.$({
            reject: false,
            stdio: ["ignore", "pipe", "pipe"],
            shell: true, // Use shell to execute the command
            env: { ...process.env, ...this.env },
        })`${sshCommand}`;

        if (sshProcess.stdout && sshProcess.stderr) {
            sshProcess.stdout.on("data", (data) => {
                outlog(data.toString());
            });
            sshProcess.stderr.on("data", (data) => {
                errlog(data.toString());
            });
        }

        // Store the SSH process
        this.sshSessions.set(fullSessionId, sshProcess);

        try {
            await new Promise<void>((resolve, reject) => {
                sshProcess.on("close", (code) => {
                    if (code === 0 || code === null) {
                        resolve();
                    } else {
                        reject(
                            new Error(
                                `SSH session ${fullSessionId} exited with code ${code}`
                            )
                        );
                    }
                });

                sshProcess.on("error", (error) => {
                    reject(
                        new Error(
                            `SSH session ${fullSessionId} error: ${JSON.stringify(error)}`
                        )
                    );
                });
            });
        } catch (error) {
            errlog(
                `SSH session error: ${error instanceof Error ? error.message : String(error)}`
            );
        } finally {
            // Remove the SSH process from the map when it's done
            this.sshSessions.delete(fullSessionId);
        }
    }

    public stopSshSession(index: number, sessionId: string): void {
        const fullSessionId = `${index}-${sessionId}`;
        const sshProcess = this.sshSessions.get(fullSessionId);
        if (sshProcess) {
            sshProcess.kill();
            this.sshSessions.delete(fullSessionId);
        }
    }
    //#endregion
}
