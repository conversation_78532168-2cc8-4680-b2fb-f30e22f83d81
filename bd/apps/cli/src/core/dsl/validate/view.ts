import type { DSLReq } from "@clara/dsl";
import type { <PERSON><PERSON><PERSON>ckValidate, DSLParser } from "@clara/tslib";

export const validate_view_find: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    const find = cson.view.find;
    if (!find) return;

    const erroredFields = new Set<string>();

    for (const findType of ["basic", "advanced"] as const) {
        for (const field of find[findType] || []) {
            if (typeof field === "string" && !field.includes(".")) {
                // Skip if we've already reported an error for this field
                if (erroredFields.has(field)) continue;

                if (!cson.fields?.[field]) {
                    erroredFields.add(field);
                    parser.output.fail(
                        form,
                        `view.find.${findType} references non-existent field '${field}'`
                    );
                } else if (!cson.fields[field].model?.active) {
                    erroredFields.add(field);
                    parser.output.fail(
                        form,
                        `view.find.${findType} references inactive field '${field}'`
                    );
                }
            }
        }
    }
};

export const validate_view_grid: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    // Early return if no grid or fields
    if (!cson?.view?.grid || !cson?.fields) return;

    // PART 1: Validate main form grid
    validateGrid(cson.view.grid, "view.grid");

    // PART 2: Validate all fields with view.grid
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        if (!field.view?.grid) continue;

        let linkedForms: string[] = [form];

        if (typeof field.model.source == "string") {
            if (field.model.source.includes("{") && field.model?.sourcefilter)
                linkedForms = Object.keys(field.model.sourcefilter);
            else if (!field.model.source.includes("{"))
                linkedForms = [field.model.source];
        } else if (field.view.embed?.form) {
            // If it's an embedded form, add the single form to the array
            linkedForms = [field.view.embed.form];
        } else if (field.view.embed?.query) {
            // Cannot validate dynamic queries (field.view.embed.query) or other sources
            continue;
        }

        // Loop through all linked forms and validate grid for each
        for (const currentLinkedForm of linkedForms) {
            const linkedCSON = parser.files.cson[currentLinkedForm];
            if (!linkedCSON) continue; // Skip if form doesn't exist

            // Update sourceFields for the current form
            const sourceFields = new Set([
                ...Object.keys(linkedCSON.fields || {}),
                ...Object.keys(field.model?.subfields || {}),
            ]);

            validateGrid(
                field.view.grid,
                "view.grid",
                sourceFields,
                fieldName,
                currentLinkedForm
            );
        }
    }

    // Helper function to validate grid fields against a source
    function validateGrid(
        gridConfig: any,
        contextPrefix: string,
        sourceFields?: Set<string>,
        parentField?: string,
        linkedForm?: string
    ) {
        const validFields = new Set<string>();

        // Process both fields and sort arrays
        for (const arrayType of ["fields", "sort"]) {
            for (const field of gridConfig[arrayType] || []) {
                const fieldName =
                    typeof field === "string"
                        ? field.replace(/^[-+]/, "")
                        : Object.keys(field)[0];

                if (!fieldName.includes(".")) {
                    // For embedded grids, check against source fields
                    if (sourceFields) {
                        if (!sourceFields.has(fieldName)) {
                            parser.output.fail(
                                form,
                                `${contextPrefix}.${arrayType} references field '${fieldName}' not present in embed/source ${linkedForm}`,
                                parentField
                            );
                        } else if (
                            linkedForm &&
                            parser.files.cson[linkedForm]?.fields?.[fieldName]
                        ) {
                            // Check if the field is active in the linked form
                            const linkedField =
                                parser.files.cson[linkedForm].fields[fieldName];
                            if (!linkedField?.model?.active) {
                                parser.output.fail(
                                    form,
                                    `${contextPrefix}.${arrayType} references inactive field '${fieldName}' in embed/source ${linkedForm}`,
                                    parentField
                                );
                            }
                        }
                    }
                    // For main grid, check against form fields
                    else {
                        if (!cson.fields?.[fieldName]) {
                            parser.output.fail(
                                form,
                                `${contextPrefix}.${arrayType} references non-existent field '${fieldName}'`
                            );
                        } else if (!cson.fields[fieldName]?.model?.active) {
                            parser.output.fail(
                                form,
                                `${contextPrefix}.${arrayType} references inactive field '${fieldName}'`
                            );
                        }
                    }
                    // Only add to validFields if it's from the fields array
                    if (arrayType === "fields") {
                        validFields.add(fieldName);
                    }
                }
            }
        }

        // Validate width length
        if (Array.isArray(gridConfig.width) && gridConfig.width.length > 0) {
            if (
                Array.isArray(gridConfig.fields) &&
                gridConfig.fields.length > 0
            ) {
                if (gridConfig.width.length !== gridConfig.fields.length) {
                    parser.output.fail(
                        form,
                        !parentField
                            ? `${contextPrefix}.width must have same number of elements as ${contextPrefix}.fields`
                            : `view.grid.width must have same number of elements as view.grid.fields`,
                        parentField,
                        [
                            `size of width : ${gridConfig.width.length}`,
                            `size of fields: ${gridConfig.fields.length} `,
                        ]
                    );
                }
            } else if (
                linkedForm &&
                parser.files.cson[linkedForm]?.view?.grid?.fields
            ) {
                if (
                    !Array.isArray(
                        parser.files.cson[linkedForm].view.grid.fields
                    ) ||
                    parser.files.cson[linkedForm].view.grid.fields.length === 0
                ) {
                    parser.output.fail(
                        form,
                        `Referenced form '${linkedForm}' does not have valid grid fields`,
                        parentField
                    );
                } else if (
                    gridConfig.width.length !==
                    parser.files.cson[linkedForm].view.grid.fields.length
                ) {
                    parser.output.fail(
                        form,
                        `view.grid.width and ${linkedForm}.view.grid.fields must have same number of elements or define custom view.grid.fields for this view.grid.width`,
                        parentField,
                        [
                            `size of width : ${gridConfig.width.length}`,
                            `size of fields: ${parser.files.cson[linkedForm].view.grid.fields.length} `,
                        ]
                    );
                }
            }
        }

        // Validate labels length
        if (
            Array.isArray(gridConfig.label) &&
            Array.isArray(gridConfig.fields) &&
            gridConfig.label.length > gridConfig.fields.length
        ) {
            parser.output.fail(
                form,
                `${contextPrefix}.labels has more entries than ${contextPrefix}.fields`,
                parentField
            );
        }

        // Validate hide_columns against grid fields
        if (
            Array.isArray(gridConfig.hide_columns) &&
            gridConfig.hide_columns.length
        ) {
            // Skip validation if fields array is empty
            if (validFields.size) {
                for (const field of gridConfig.hide_columns) {
                    const fieldName =
                        typeof field === "string"
                            ? field.replace(/^[-+]/, "")
                            : Object.keys(field)[0];

                    if (
                        !fieldName.includes(".") &&
                        !validFields.has(fieldName)
                    ) {
                        parser.output.fail(
                            form,
                            `${contextPrefix}.hide_columns references field '${fieldName}' not in ${contextPrefix}.fields`,
                            parentField
                        );
                    }
                }
            }
        }

        // Validate tooltip and copy against source fields
        if (parentField) {
            for (const type of ["tooltip", "copy"]) {
                if (
                    Array.isArray(gridConfig[type]) &&
                    gridConfig[type].length
                ) {
                    for (const fieldName of gridConfig[type]) {
                        if (
                            !fieldName.includes(".") &&
                            (!sourceFields || !sourceFields.has(fieldName))
                        ) {
                            parser.output.fail(
                                form,
                                `${contextPrefix}.${type} references field '${fieldName}' not present in embed/source ${linkedForm}`,
                                parentField
                            );
                        }
                    }
                }
            }
        }

        return validFields;
    }
};
