import type { DSLReq } from "@clara/dsl";
import type { <PERSON><PERSON>he<PERSON>Validate, DSLParser } from "@clara/tslib";

export const validate_fields_form_offscreen_readonly: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        if (
            field.model?.active &&
            field.view?.offscreen &&
            !field.view?.readonly &&
            field.model?.prefill?.length > 0 &&
            typeof field.model?.if === "object"
        ) {
            parser.output.fail(
                form,
                "must be readonly since it is offscreen and uses if/prefill",
                fieldName
            );
        }
    }
};

export const validate_fields_reserved_keywords: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    for (const key of Object.keys(cson.fields)) {
        if (parser.RESERVED_KEYWORDS.includes(key)) {
            parser.output.fail(form, "is a reserved keyword", key);
        }
        for (const prefix of parser.RESERVED_PREFIXES) {
            if (key.startsWith(prefix) && !cson.fields[key].model?.autoinsert) {
                parser.output.fail(
                    form,
                    `starts with reserved prefix '${prefix}'`,
                    key
                );
            }
        }
    }
};

export const validate_fields_form_sources: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        // Check embedded form references
        if (typeof field?.view?.embed?.form === "string") {
            if (!parser.files.keys.has(field.view.embed.form)) {
                parser.output.fail(
                    form,
                    `has invalid embedded form reference:`,
                    fieldName,
                    [field.view.embed.form]
                );
            }
        }

        // Check source form references
        if (typeof field?.model?.source === "string") {
            if (field.model.source.includes("{")) {
                if (field.model?.sourcefilter) {
                    if (field.model.sourceid != "id")
                        parser.output.fail(
                            form,
                            `has invalid sourceid, must be 'id' since source is a template string with sourcefilter list`,
                            fieldName,
                            [field.model.sourceid]
                        );
                }
            } else if (
                field.model.source != "" &&
                field.model.source != "user" // needed for tests that do not include user
            ) {
                if (!parser.files.keys.has(field.model.source)) {
                    parser.output.fail(
                        form,
                        `has invalid source form reference:`,
                        fieldName,
                        [field.model.source]
                    );
                }
            }
        }

        // Validate source_order matches source keys
        if (
            typeof field?.model?.source === "object" &&
            Array.isArray(field?.model?.source_order)
        ) {
            const sourceKeys = Array.isArray(field.model.source)
                ? field.model.source.sort()
                : Object.keys(field.model.source).sort();
            const orderKeys = field.model.source_order.sort();
            if (JSON.stringify(sourceKeys) !== JSON.stringify(orderKeys)) {
                parser.output.fail(
                    form,
                    "source_order does not match source keys",
                    fieldName
                );
            }
        }
    }
};

export const validate_fields_model_if_conditions: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    const renderableFields = new Set<string>();

    // Collect renderable fields from sections
    for (const section of Object.values<any>(cson.model?.sections || {})) {
        section.fields?.forEach((f: string) => renderableFields.add(f));
    }

    // Validate if conditions
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        for (const [condition, ifData] of Object.entries<any>(
            field.model?.if || {}
        )) {
            // Validate fields in if conditions exist and are active
            for (const f of ifData.fields || []) {
                if (!cson.fields[f]) {
                    parser.output.fail(
                        form,
                        `if condition '${condition}' references non-existent field '${f}'`,
                        fieldName
                    );
                } else if (!cson.fields[f].model?.active) {
                    parser.output.fail(
                        form,
                        `if condition '${condition}' references inactive field '${f}'`,
                        fieldName
                    );
                }
            }

            // Validate readonly fields exist and are active
            for (const f of ifData.readonly?.fields || []) {
                if (!cson.fields[f]) {
                    parser.output.fail(
                        form,
                        `if condition '${condition}' readonly references non-existent field '${f}'`,
                        fieldName
                    );
                } else if (!cson.fields[f].model?.active) {
                    parser.output.fail(
                        form,
                        `if condition '${condition}' readonly references inactive field '${f}'`,
                        fieldName
                    );
                }
            }

            // Validate sections and readonly sections in if conditions exist
            for (const s of ifData.sections || []) {
                if (!cson.model?.sections?.[s]) {
                    parser.output.fail(
                        form,
                        `if condition '${condition}' references non-existent section '${s}'`,
                        fieldName
                    );
                }
            }

            for (const s of ifData.readonly?.sections || []) {
                if (!cson.model?.sections?.[s]) {
                    parser.output.fail(
                        form,
                        `if condition '${condition}' readonly references non-existent section '${s}'`,
                        fieldName
                    );
                }
            }
        }
    }
};

export const validate_fields_model_source_indexes: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        if (
            field.model?.source &&
            field.model?.sourceid &&
            field.model?.sourceid != "id"
        ) {
            const sourceForm = parser.files.cson[field.model.source];
            if (sourceForm) {
                const allIndexes = [
                    ...(sourceForm.model.indexes.many || []),
                    ...(sourceForm.model.indexes.unique || []),
                ];
                const hasIndex = allIndexes.some(
                    (idx) => idx[0] === field.model.sourceid
                );
                if (!hasIndex) {
                    parser.output.fail(
                        form,
                        `links to non-indexed field '${field.model.sourceid}' in form '${field.model.source}'`,
                        fieldName
                    );
                }
            }
        }
    }
};

export const validate_fields_model_sync_mode: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        if (
            typeof field.model?.source === "string" &&
            !field.model.source.includes("{") // Ignore template strings
        ) {
            const sourceForm = parser.files.cson[field.model.source];
            if (sourceForm?.model?.sync_mode === "full") {
                if (field.model.sourceid !== "code") {
                    parser.output.fail(
                        form,
                        `links to full sync form '${field.model.source}' but has sourceid '${field.model.sourceid}' instead of 'code'`,
                        fieldName
                    );
                }
            }
        }
    }
};
