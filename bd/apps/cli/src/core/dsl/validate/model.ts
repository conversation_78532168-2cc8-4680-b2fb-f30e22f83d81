import type { DSLReq } from "@clara/dsl";
import type { <PERSON><PERSON><PERSON><PERSON>Validate, DSLParser } from "@clara/tslib";

export const validate_model_name_fields: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    if (typeof cson.model.name === "string" && cson.model.name.length === 0) {
        parser.output.fail(form, "model.name string cannot be empty");
    }
    if (Array.isArray(cson.model.name) && cson.model.name.length === 0) {
        parser.output.fail(form, "model.name array cannot be empty");
    }

    // check if model name array contains fields that are missing or inactive
    if (Array.isArray(cson.model.name)) {
        for (const fieldName of cson.model.name) {
            if (!cson.fields?.[fieldName]) {
                parser.output.fail(
                    form,
                    `model.name references non-existent field '${fieldName}'`
                );
            } else if (!cson.fields[fieldName].model?.active) {
                parser.output.fail(
                    form,
                    `model.name references inactive field '${fieldName}'`
                );
            }
        }
    }
};

export const validate_model_sections: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    if (!Array.isArray(cson.model.sections_group)) {
        parser.output.fail(form, "model.sections_group must be an array");
    }

    if (
        typeof cson.model.sections !== "object" ||
        Array.isArray(cson.model.sections)
    ) {
        parser.output.fail(form, "model.sections must be an object");
    }

    // Validate sections_group if present
    if (cson.model.sections_group.length > 0) {
        for (const group of cson.model.sections_group) {
            for (const [groupName, groupData] of Object.entries<any>(group)) {
                if (groupData.fields) {
                    for (const field of groupData.fields) {
                        if (!cson.fields?.[field]) {
                            parser.output.fail(
                                form,
                                `Section group '${groupName}' references non-existent field '${field}'`
                            );
                        } else if (!cson.fields[field].model?.active) {
                            parser.output.fail(
                                form,
                                `Section group '${groupName}' references inactive field '${field}'`
                            );
                        }
                    }
                }
            }
        }
    } else {
        for (const [sectionName, section] of Object.entries<any>(
            cson.model.sections
        )) {
            for (const field of section.fields || []) {
                if (!cson.fields?.[field]) {
                    parser.output.fail(
                        form,
                        `Section '${sectionName}' references non-existent field '${field}'`
                    );
                } else if (
                    !cson.fields[field].model?.active &&
                    cson.fields[field].model?.type !== "subform"
                ) {
                    parser.output.fail(
                        form,
                        `Section '${sectionName}' references inactive field '${field}'`
                    );
                }
            }
        }
    }

    // Validate sections_order matches all sections
    const pref_order = Array.isArray(cson.model.sections_order)
        ? cson.model.sections_order
        : null;
    if (pref_order && pref_order?.length > 0) {
        const missed_sections = Object.keys(cson.model.sections).filter(
            (sectionName: string) => !pref_order.includes(sectionName)
        );
        if (missed_sections.length > 0) {
            // no easy way to mock this since transform_model_sections_order
            //   auto generates sections_order from sections/sections_group
            parser.output.fail(
                form,
                `Some Sections are missing from sections_order:`,
                undefined,
                missed_sections
            );
        }
    }

    for (const [sectionName, section] of Object.entries<any>(
        cson.model.sections
    )) {
        if (section.prefill != null && typeof section.prefill !== "string") {
            parser.output.fail(
                form,
                `Section '${sectionName}' prefill must be string or null`
            );
        }

        const subformFields = (section.fields || []).filter(
            (f: string) => cson.fields?.[f]?.model?.type === "subform"
        );
        if (subformFields.length > 1) {
            parser.output.fail(
                form,
                `Section '${sectionName}' contains multiple subform fields:`,
                undefined,
                subformFields
            );
        }
    }
};

export const validate_model_sync_mode: DSLCheckValidate = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    if (cson.model.sync_mode === "mixed") {
        const missingFields = parser.SYNC_MODE_REQUIRED_FIELDS.filter(
            (field) => !cson.fields || !Object.keys(cson.fields).includes(field)
        );
        if (missingFields.length > 0) {
            parser.output.fail(
                form,
                `Forms with sync_mode 'mixed' must have fields:`,
                undefined,
                missingFields
            );
        }
    }
};
