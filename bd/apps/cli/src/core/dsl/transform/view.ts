import type { DSLReq } from "@clara/dsl";
import type { DS<PERSON>heckTransform, DSLParser } from "@clara/tslib";

export const transform_view_advanced_find: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): DSLReq => {
    if (
        cson.view.find.advanced.length === 0 &&
        cson.view.find.basic.length > 0
    ) {
        cson.view.find.advanced = cson.view.find.basic;
    }
    return cson;
};
