import type { DSLReq } from "@clara/dsl";
import type { DSLCheckTransform, DSLParser } from "@clara/tslib";

// Transform negative permissions into positive ones
export const transform_fields_model_negative_permissions: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): DSLReq => {
    const negperm: Record<string, Record<string, Record<string, boolean>>> = {
        read: {},
        write: {},
    };

    // First pass: collect negative permissions
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        for (const acc of Object.keys(negperm)) {
            const facc: string[] = [];
            for (const [_, nr] of Object.entries<any>(
                field.model.access[acc]
            )) {
                if (nr.startsWith("-")) {
                    const role = nr.substring(1);
                    negperm[acc][role] = negperm[acc][role] || {};
                    negperm[acc][role][fieldName] = true;
                } else {
                    if (!facc.includes(nr)) facc.push(nr);
                }
            }
            field.model.access[acc] = facc;
        }
    }

    // Second pass: auto-add field access if not in negative permissions
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        for (const [acc, roles] of Object.entries(negperm)) {
            for (const [role, excludedFields] of Object.entries<any>(roles)) {
                if (!excludedFields[fieldName]) {
                    if (!field.model.access[acc].includes(role))
                        field.model.access[acc].push(role);
                }
            }
        }
    }

    return cson;
};

export const transform_fields_model_subfields_sort: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): DSLReq => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        if (field.model.subfields && field.model.subfields_sort.length === 0) {
            cson.fields[fieldName].model.subfields_sort = Object.keys(
                field.model.subfields
            );
        }
    }
    return cson;
};

export const transform_fields_view_control: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): DSLReq => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        // Set appropriate view.control
        if (field.view.control === "") {
            if (field.model.source !== null) {
                cson.fields[fieldName].view.control = "select";
            } else if (
                ["date", "datetime", "time"].includes(field.model.type)
            ) {
                cson.fields[fieldName].view.control = "picker";
            } else {
                cson.fields[fieldName].view.control = "input";
            }
        }

        // Convert radio to select for table sources
        if (
            field.view.control === "radio" &&
            typeof field.model.source === "string" &&
            field.model.type !== "subform" &&
            !field.model.source.includes("{")
        ) {
            cson.fields[fieldName].view.control = "select";
            parser.output.warn(
                form,
                "view.control converted to 'select' since model.source is a form",
                fieldName
            );
        }

        // Set subform controls
        if (field.model.type === "subform") {
            cson.fields[fieldName].view.control = field.model.multi
                ? "subform"
                : "inline";
        }

        // Set embedded_table control
        if (field.view.embed.form || field.view.embed.query) {
            cson.fields[fieldName].view.control = "embedded_table";
            cson.fields[fieldName].model.source = null;
            cson.fields[fieldName].model.type = "json";
        }

        // Handle media-preview for file controls
        if (
            field.view.control === "file" &&
            field.view.class?.includes("media-preview")
        ) {
            cson.fields[fieldName].view.columns = 1;
        }
    }
    return cson;
};

export const transform_fields_view_findmulti: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): DSLReq => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        // Mark multi true if findfilter is array
        if (Array.isArray(field.view.findfilter)) {
            cson.fields[fieldName].view.findmulti = true;
        }
    }
    return cson;
};
