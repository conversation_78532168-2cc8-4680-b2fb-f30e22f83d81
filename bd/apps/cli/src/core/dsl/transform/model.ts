import type { DS<PERSON>O<PERSON>, DSLReq } from "@clara/dsl";
import type { DSLCheckTransform, DSLParser } from "@clara/tslib";

export const transform_model_auto_name_index: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): DSLReq => {
    let hasAutoNameIndex = false;
    for (const idx of cson.model.indexes.many) {
        if (idx.length === 1 && idx[0] === "auto_name") {
            hasAutoNameIndex = true;
            break;
        }
    }

    if (!hasAutoNameIndex) {
        cson.model.indexes.many.push(["auto_name"]);
    }
    return cson;
};

export const transform_model_sections_order: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLOpt
): DSLReq => {
    const modelData = cson.model;
    const pref_order = Array.isArray(modelData.sections_order)
        ? modelData.sections_order
        : null;

    cson.model.sections_order = [];

    if (modelData.sections_group && modelData.sections_group.length > 0) {
        const oldSections = cson.model.sections;
        cson.model.sections = {};
        for (const group of modelData.sections_group) {
            for (const [groupName, groupValue] of Object.entries<any>(group)) {
                if (groupValue.fields) {
                    // merge old section if exists with new group value
                    if (oldSections && groupName in oldSections) {
                        cson.model.sections[groupName] = {
                            ...oldSections[groupName],
                            ...groupValue,
                        };
                    } else {
                        cson.model.sections[groupName] = groupValue;
                    }
                    cson.model.sections[groupName].group = {
                        label: groupName,
                        note: groupValue.note || "",
                        hide_header: groupValue.hide_header || false,
                    };
                    cson.model.sections_order.push(groupName);
                } else if (groupValue.sections) {
                    for (const sectionContainer of groupValue.sections) {
                        for (const [
                            sectionName,
                            sectionValue,
                        ] of Object.entries<any>(sectionContainer)) {
                            // merge old section if exists with new group value
                            if (oldSections && sectionName in oldSections) {
                                cson.model.sections[sectionName] = {
                                    ...oldSections[sectionName],
                                    ...sectionValue,
                                };
                            } else {
                                cson.model.sections[sectionName] = sectionValue;
                            }
                            cson.model.sections[sectionName].group = {
                                label: groupName,
                                note: groupValue.note || "",
                                hide_header: groupValue.hide_header || false,
                            };
                            cson.model.sections_order.push(sectionName);
                        }
                    }
                }
            }
        }
    } else if (modelData.sections) {
        cson.model.sections_order = Object.keys(modelData.sections);
    }

    // Apply preferred order if specified
    if (pref_order && pref_order?.length > 0) {
        // import the order from the pref_order and then add the missing sections
        const missing_sections = cson.model.sections_order.filter(
            (sectionName: string) => !pref_order.includes(sectionName)
        );
        cson.model.sections_order = [...pref_order, ...missing_sections];
    }

    return cson as DSLReq;
};

// Add new function to handle tab toggle fields
export const transform_model_tab_toggles: DSLCheckTransform = (
    parser: DSLParser,
    form: string,
    cson: DSLOpt
): DSLReq => {
    const insertTabIfField = (
        section: string,
        sectionFields: string[] | undefined
    ): string => {
        const name = "tabif_" + section.replace(/[^\w]|\s/gi, "").toLowerCase();
        // check if the field already exists
        if (cson.fields[name]) {
            return name;
        }
        cson.fields[name] = {
            model: {
                autoinsert: true,
                source: ["Yes"],
                default: "Yes",
                if: {
                    Yes: {
                        sections: [section],
                        fields: sectionFields || [],
                    },
                },
            },
            view: {
                label: "Tab If For " + section,
                offscreen: true,
                readonly: true,
                validate: [
                    {
                        name: "RemoveSectionTabs",
                    },
                ],
            },
        };
        return name;
    };

    if (cson.model.sections_group && cson.model.sections_group.length > 0) {
        for (const group of cson.model.sections_group) {
            const fields: string[] = [];
            for (const [groupName, groupValue] of Object.entries<any>(group)) {
                if (groupValue.tab_toggle) {
                    fields.push(insertTabIfField(groupName, groupValue.fields));
                } else if (groupValue.sections) {
                    for (const section of groupValue.sections) {
                        for (const [
                            sectionName,
                            sectionValue,
                        ] of Object.entries<any>(section)) {
                            if (sectionValue.tab_toggle) {
                                fields.push(
                                    insertTabIfField(
                                        sectionName,
                                        sectionValue.fields
                                    )
                                );
                            }
                        }
                    }
                }
            }
            if (fields.length > 0) {
                cson.model.sections_group = cson.model.sections_group.filter(
                    (group: any) => !group[parser.TAB_TOGGLES]
                );
                const tabtog: Record<string, any> = {};
                tabtog[parser.TAB_TOGGLES] = {
                    fields,
                    hide_header: true,
                };
                cson.model.sections_group.push(tabtog);
            }
        }
    } else if (cson.model.sections) {
        const fields: string[] = [];
        for (const [sectionName, sectionValue] of Object.entries<any>(
            cson.model.sections
        )) {
            if (sectionValue.tab_toggle) {
                fields.push(insertTabIfField(sectionName, sectionValue.fields));
            }
        }
        if (fields.length > 0) {
            if (parser.TAB_TOGGLES in cson.model.sections) {
                delete cson.model.sections[parser.TAB_TOGGLES];
            }
            cson.model.sections["Tab Toggles"] = {
                fields,
                hide_header: true,
            };
        }
    }

    return transform_model_sections_order(parser, form, cson as DSLReq);
};
