import type { DSLReq } from "@clara/dsl";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>ar<PERSON>, DSLParser } from "@clara/tslib";

export const warn_fields_conditional_conflicts: DSLCheckWarn = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    if (!cson?.fields || !cson?.model?.sections) return;

    // Collect all renderable fields from sections
    const renderableFields: string[] = [];
    Object.values<any>(cson.model.sections).forEach((section) => {
        if (section.fields) {
            renderableFields.push(...section.fields);
        }
    });

    // Map field and section conditions
    const mapFields: Record<string, string[]> = {};
    const mapSections: Record<string, string[]> = {};
    const ifedSections: string[] = [];

    // Collect all if conditions
    Object.entries<any>(cson.fields).forEach(([fieldName, field]) => {
        Object.entries<any>(field?.model?.if || {}).forEach(
            ([condition, ifData]) => {
                if (ifData.fields?.length) {
                    mapFields[`${fieldName}.if.${condition}`] = ifData.fields;
                }
                if (ifData.sections?.length) {
                    mapSections[`${fieldName}.if.${condition}`] =
                        ifData.sections;
                    ifedSections.push(...ifData.sections);
                }
            }
        );
    });

    // Check each section against field conditions
    Object.entries<any>(cson.model.sections).forEach(
        ([sectionName, section]) => {
            Object.entries(mapFields).forEach(([ifKey, fields]) => {
                const controlField = ifKey.split(".")[0];

                fields.forEach((field) => {
                    // Skip if field is not renderable
                    if (!renderableFields.includes(field)) {
                        return;
                    }

                    // Check if field is in section but control field isn't
                    if (
                        section.fields?.includes(field) &&
                        !section.fields?.includes(controlField)
                    ) {
                        // Skip if section is explicitly shown by this condition
                        if (mapSections[ifKey]?.includes(sectionName)) {
                            return;
                        }

                        // Skip if section is not controlled by any if condition
                        if (!ifedSections.includes(sectionName)) {
                            return;
                        }

                        parser.output.warn(
                            form,
                            `is shown by if condition, section is hidden by other(s)`,
                            field,
                            [ifKey]
                        );
                    }
                });
            });
        }
    );
};

export const warn_fields_in_duplicate_sections: DSLCheckWarn = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    const sections = cson.model.sections || {};
    const fieldCounts: Record<string, string[]> = {};

    Object.entries(sections).forEach(
        ([sectionName, section]: [string, any]) => {
            (section.fields || []).forEach((field: string) => {
                if (!fieldCounts[field]) {
                    fieldCounts[field] = [];
                }
                fieldCounts[field].push(sectionName);
            });
        }
    );

    Object.entries(fieldCounts).forEach(([field, sectionList]) => {
        if (sectionList.length > 1) {
            parser.output.warn(
                form,
                `x${sectionList.length} in:`,
                field,
                [...new Set(sectionList)].sort()
            );
        }
    });
};

export const warn_fields_model_sourcefilter: DSLCheckWarn = (
    parser: DSLParser,
    form: string,
    cson: DSLReq
): void => {
    for (const [fieldName, field] of Object.entries<any>(cson.fields)) {
        if (
            typeof field.model?.source === "string" &&
            field.model.source.includes("{") &&
            field.model?.sourcefilter
        ) {
            // Check each form referenced in sourcefilter exists
            const invalid_forms = [];
            for (const formKey of Object.keys(field.model.sourcefilter)) {
                if (!parser.files.keys.has(formKey)) {
                    invalid_forms.push(formKey);
                }
            }
            if (invalid_forms.length > 0) {
                parser.output.warn(
                    form,
                    "sourcefilter references non-existent forms",
                    fieldName,
                    invalid_forms.sort()
                );
            }
        }
    }
};
