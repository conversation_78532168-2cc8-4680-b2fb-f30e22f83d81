/******************************************************************************
 * This is the globally available "this.app.dsl" object for all commands.
 * It can validate, compile, and push DSL to any host specified by --host flag.
 * It uses this.app.cache for compiled DSL to speed up repeated compilations.
 *****************************************************************************/

import type App from "../app.ts";
import type { DSLOpt, DSLReq } from "@clara/dsl";
import {
    <PERSON><PERSON><PERSON>ompiler,
    DSLInterface,
    DSLParser,
    type DSLCheckModules,
    type DSLFiles,
    type DSLFolders,
    type CSONForm,
    type CSONForms,
    type CSONFolders,
} from "@clara/tslib";

import * as transformFields from "./transform/fields.ts";
import * as transformModel from "./transform/model.ts";
import * as transformView from "./transform/view.ts";
import * as validateFields from "./validate/fields.ts";
import * as validateModel from "./validate/model.ts";
import * as validateView from "./validate/view.ts";
import * as warnFields from "./warn/fields.ts";
import * as warnModel from "./warn/model.ts";
import * as warnView from "./warn/view.ts";

const dslCheckModules: DSLCheckModules = {
    transform: {
        ...transformFields,
        ...transformModel,
        ...transformView,
    },
    validate: {
        ...validateFields,
        ...validateModel,
        ...validateView,
    },
    warn: {
        ...warnFields,
        ...warnModel,
        ...warnView,
    },
};

export default class DSL implements DSLInterface {
    private parser: DSLParser;
    private compiler: CSONCompiler;

    private BASE_FOLDER = "base" as const;

    // Use DSL specs & autoinserts from jsshared & NES
    public DSL_Specification!: object;
    public DSL_AutoInsert!: DSLOpt;
    public MAX_COLLECTIONS!: number;

    private dslpath!: string;

    /***************************************************************************
     * On startup, load DSL Specification from Coffee (jsshared) and AutoInsert,
     * and MAX_COLLECTIONS from NES Core dsl.js using TypeScript/JS AST.
     * Also verify source DSL CSON path exists.
     **************************************************************************/
    //#region Init

    constructor(
        private app: App,
        dslspec: string,
        nesdsl: string,
        dslpaths: string[],
        private exportpath: string
    ) {
        this.compiler = new CSONCompiler(app, app.cache);
        this.parser = new DSLParser(app, dslCheckModules);

        try {
            // 1. Load DSL Specification
            const dslspecjs = this.compiler.coffee(dslspec);
            if ("error" in dslspecjs && dslspecjs.error) {
                this.app.fail(
                    `DSL Specification compilation error: ${dslspecjs.error}`
                );
                this.app.fatal(dslspecjs);
            }

            this.DSL_Specification = dslspecjs.code!;

            // 2. Load DSL AutoInsert & MAX_COLLECTIONS
            const js = this.compiler.ast(nesdsl);
            if (!js) {
                this.app.fatal(`Cannot load NES Core dsl.js file: ${nesdsl}`);
                return;
            }
            this.DSL_AutoInsert = this.compiler.getNodeValue(
                js,
                "this.shared.DSL_AutoInsert"
            ) as DSLOpt;
            if (!this.DSL_AutoInsert) {
                this.app.fatal(
                    `Cannot extract DSL_AutoInsert from NES Core dsl.js file: ${nesdsl}`
                );
            }
            this.MAX_COLLECTIONS = this.compiler.getNodeValue(
                js,
                "this.MAX_COLLECTIONS"
            ) as number;
            if (!this.MAX_COLLECTIONS) {
                this.app.fatal(
                    `Cannot extract MAX_COLLECTIONS from NES Core dsl.js file: ${nesdsl}`
                );
            }

            // 3. Find DSL Base
            const flags = this.app.command.getFlags();
            if (flags.dslpath) {
                dslpaths = [this.app.appPath(flags.dslpath)];
            }
            const dslfound = [...new Set(dslpaths)]
                .map((path) => {
                    // Ensure dslpath ends with a forward slash
                    if (!path.endsWith("/")) {
                        path += "/";
                    }
                    return path;
                })
                .filter((path) => {
                    return this.app.isDir(path + "base/cson/");
                });

            if (dslfound.length > 0) {
                this.dslpath = dslfound[0];
            } else {
                this.app.fatal(
                    `Cannot find DSL base folder in: ${"\n" + dslpaths.join("\n")}`
                );
            }
        } catch (err) {
            this.app.fatal(err);
        }
    }

    public async init(): Promise<void> {
        await this.parser.init(this);
    }
    //#endregion

    /***************************************************************************
     * DSL Compilation
     **************************************************************************/
    //#region DSL

    private loadCSONs(flags: any): CSONFolders {
        // load/compile all possible cson files
        //   for base and all customers
        const compiled: CSONFolders = {};
        let cson_total = 0,
            cson_failed = 0,
            cson_unmodified = 0,
            cson_compiled = 0;

        const customer = flags.customer as string;
        const csons = this.app.glob(this.dslpath + "*/cson/*.cson");

        // --clear
        if (flags.clear) {
            this.app.cache.clear(this.app.basePath(this.dslpath));
            this.app.info("Cache cleared.");
        }

        for (const [_, csonfile] of Object.entries(csons)) {
            const [cust, __, key] = this.app.splitPath(
                csonfile.replace(this.dslpath, "")
            );

            if (customer) {
                if (customer !== cust && cust !== this.BASE_FOLDER) {
                    continue;
                }
            } else {
                if (cust !== this.BASE_FOLDER) {
                    continue;
                }
            }

            const cs = this.compiler.cson(csonfile, false, true);

            if (cs.status === "failed") {
                this.app.fail(cs.error.message);
            }

            switch (cs.status) {
                case "failed":
                    cson_failed++;
                    break;
                case "unmodified":
                case "compiled":
                    if (!(cust in compiled)) compiled[cust] = {} as CSONForms;
                    if (cs.code) {
                        compiled[cust][key.replace(".cson", "")] = {
                            cson: cs.code,
                            status: cs.status,
                        } as CSONForm;
                        if (cs.status === "unmodified") {
                            cson_unmodified++;
                        } else {
                            cson_compiled++;
                        }
                    }
                    break;
            }
            cson_total++;
        }

        this.app.info(
            {
                message: "CSONs loaded.",
                csons: {
                    failed: cson_failed,
                    unmodified: cson_unmodified,
                    compiled: cson_compiled,
                    total: cson_total,
                },
            },
            `CSONs: ${cson_failed} failed, ${cson_unmodified} unmodified, ${cson_compiled} compiled, ${cson_total} total.`
        );

        // Validation checks moved from make()
        if (Object.keys(compiled).length === 0) {
            this.app.fatal(
                `Cannot load DSL CSONs. Cannot run any DSL-related commands.`
            );
            return {} as CSONFolders;
        }

        if (!(this.BASE_FOLDER in compiled)) {
            this.app.fatal(
                `Cannot find base DSL CSONs. Cannot run any DSL-related commands.`
            );
            return {} as CSONFolders;
        }

        if (customer && !(customer in compiled)) {
            this.app.fatal(
                `DSL for customer '${customer}' not found.\n\nValid customers: ${Object.keys(
                    compiled
                )
                    .filter((cust) => cust !== this.BASE_FOLDER)
                    .join(", ")}`
            );
            return {} as CSONFolders;
        }

        return compiled;
    }

    private loadParsedCache(flags: any, csonFolders: CSONFolders): DSLFolders {
        const parsed: DSLFolders = {};

        // Initialize parsed structure
        for (const cust of Object.keys(csonFolders)) {
            if (!(cust in parsed)) parsed[cust] = {} as DSLFiles;
        }
        if (flags.clear) {
            this.app.cache.clear(`parsed/`);
            return parsed;
        }

        const modifiedForms: Set<string> = new Set();
        for (const [cust, csonForms] of Object.entries(csonFolders)) {
            for (const [form, csonForm] of Object.entries(csonForms)) {
                if (modifiedForms.has(form)) continue;

                if (csonForm.status === "unmodified") {
                    const cachedForm = this.app.cache.get(
                        `parsed/${cust}/${form}.cson`
                    );
                    if (cachedForm) {
                        parsed[cust][form] = JSON.parse(
                            cachedForm
                        ) as unknown as DSLReq;

                        if (parsed[cust][form] && parsed[cust][form].fields)
                            continue;
                    }
                }

                modifiedForms.add(form);
            }
        }

        for (const form of modifiedForms) {
            this.app.info(`Compiling: ${form}...`);
            this.app.cache.clear(`parsed/%/${form}.cson`);
            if (form in parsed[this.BASE_FOLDER]) {
                delete parsed[this.BASE_FOLDER][form];
            }
            if (flags.customer && form in parsed[flags.customer]) {
                delete parsed[flags.customer][form];
            }
        }

        return parsed;
    }

    public make(flags: any): string[] {
        const customer = flags.customer as string;

        // Phase 1: Load CSONs
        const csonFolders = this.loadCSONs(flags);

        // Phase 2: Cache load forms parsed previously, that have not changed
        const parsed = this.loadParsedCache(flags, csonFolders);

        // Phase 3: Merge all forms - base first, then customer if available
        for (const [cust, csonForms] of Object.entries(csonFolders)) {
            for (const [form, csonForm] of Object.entries(csonForms)) {
                if (form in parsed[cust]) continue;

                if (cust == this.BASE_FOLDER) {
                    // First, merge DSL_AutoInsert into all base forms
                    csonFolders[cust][form].cson = this.parser.merge(
                        form,
                        this.DSL_AutoInsert,
                        csonForm.cson as object,
                        this.parser.MERGE_STOPAT,
                        true
                    );
                } else {
                    // Then optionally, merge customer forms into base forms
                    const missingBaseForm = !(
                        form in csonFolders[this.BASE_FOLDER]
                    );
                    const baseForm = missingBaseForm
                        ? this.DSL_AutoInsert
                        : form in parsed[this.BASE_FOLDER]
                          ? parsed[this.BASE_FOLDER][form]
                          : csonFolders[this.BASE_FOLDER][form].cson;

                    csonFolders[cust][form].cson = this.parser.merge(
                        form,
                        baseForm,
                        csonForm.cson,
                        this.parser.MERGE_STOPAT,
                        missingBaseForm // true if using DSL_AutoInsert when no base form exists
                    );
                }
            }
        }

        // Phase 4: Parse, cache, and then merge all forms
        for (const [cust, csonForms] of Object.entries(csonFolders)) {
            for (const [form, csonForm] of Object.entries(csonForms)) {
                if (
                    cust in parsed &&
                    form in parsed[cust] &&
                    parsed[cust][form]
                ) {
                    continue;
                }
                const parsedForm = this.parser.parse(form, csonForm.cson);
                if (parsedForm) {
                    parsed[cust][form] = parsedForm;
                    this.app.cache.set(
                        `parsed/${cust}/${form}.cson`,
                        JSON.stringify(parsedForm)
                    );
                }
            }
        }

        const merged =
            customer && customer in parsed
                ? { ...parsed[this.BASE_FOLDER], ...parsed[customer] }
                : parsed[this.BASE_FOLDER];

        // Phase 5: Set parsed form data & keys to parser so validate_*() can use them
        this.parser.files = {
            keys: new Set<string>(Object.keys(merged).toSorted()),
            cson: merged,
        };

        // Phase 6: Validate forms and run warnings
        const validatedForms: string[] = [];
        for (const [form, csonForm] of Object.entries(merged)) {
            if (this.parser.run_validate(form, csonForm)) {
                validatedForms.push(form);
                if (flags.warnings) {
                    this.parser.run_warn(form, csonForm);
                }
            }
        }

        // Phase 7: Save all parsed base JSON to .ts files
        if (!this.app.isNodeEnv("testing")) {
            for (const [form, csonForm] of Object.entries(
                parsed[this.BASE_FOLDER]
            )) {
                const tsFile = this.app.repoPath(
                    `${this.exportpath}/${form}.ts`
                );
                const jsForm = `import { dslForm } from "../spec/index.ts";
const d = dslForm("${form}", ${JSON.stringify(csonForm)} as const);
const _d = d();
export type d = typeof _d;
export type dsl_${form} = typeof _d;
export default d;`;
                if (
                    !this.app.isFile(tsFile) ||
                    this.app.read(tsFile) !== jsForm
                ) {
                    this.app.info("Writing form TS Interface: " + form);
                    this.app.write(tsFile, jsForm);
                }
            }

            //  Remove any base JSON .ts files that are no longer in DSL
            for (const tsFile of this.app.glob(
                this.app.repoPath(`${this.exportpath}/*.ts`)
            )) {
                const form = tsFile
                    .replace(this.app.repoPath(this.exportpath) + "/", "")
                    .replace(".ts", "");
                if (!(form in parsed[this.BASE_FOLDER])) {
                    this.app.info(
                        "Deleting obsolete form TS Interface: " + form
                    );
                    this.app.rm(tsFile);
                }
            }
        }

        // Display all results after processing is complete
        this.parser.results();

        return validatedForms;
    }

    //#endregion

    /***************************************************************************
     * DSL Compare: //TODO: This should be obsoleted once bd-dsl repo goes away
     **************************************************************************/
    //#region Compare

    public compareCSON(flags: any): void {
        const oldPath = this.dslpath + "store/.tmp/store/dsl-";
        const customer = flags.customer
            ? (flags.customer as string)
            : this.BASE_FOLDER;
        for (const form of this.parser.files.keys) {
            let oldJSON = {};
            const oldCustPath = oldPath + customer + "-" + form + ".json";
            const oldBasePath = oldPath + "base-" + form + ".json";
            if (this.app.isFile(oldCustPath)) {
                oldJSON = JSON.parse(this.app.read(oldCustPath) || "{}");
            } else if (
                oldCustPath != oldBasePath &&
                this.app.isFile(oldBasePath)
            ) {
                oldJSON = JSON.parse(this.app.read(oldBasePath) || "{}");
            } else {
                this.app.fail(
                    `Cannot find old JSON:\n- ${oldCustPath}${oldCustPath != oldBasePath ? "\n- " + oldBasePath : ""}`
                );
            }
            const newJSON = this.parser.files.cson[form];
            this.compareJSON(flags, form, oldJSON, newJSON);
        }
    }

    public compareJSON(
        flags: any,
        path: string,
        obj1: object,
        obj2: object
    ): boolean {
        if (Array.isArray(obj1) && Array.isArray(obj2)) {
            // unique array values
            const sobj1 = [...new Set(obj1)].toSorted();
            const sobj2 = [...new Set(obj2)].toSorted();

            if (sobj1.length !== sobj2.length) {
                this.app.info(path + ":");
                this.app.info(`  - Array length mismatch`);
                this.app.info(`    - ${sobj1.length} !== ${sobj2.length}`);
                this.app.info(`    - ${JSON.stringify(sobj1)}`);
                this.app.info(`    - ${JSON.stringify(sobj2)}`);
                return false;
            }
            for (let i = 0; i < sobj1.length; i++) {
                if (
                    !this.compareJSON(
                        flags,
                        `${path}[${i}]`,
                        sobj1[i],
                        sobj2[i]
                    )
                ) {
                    return false;
                }
            }
            return true;
        }
        if (typeof obj1 !== "object" || typeof obj2 !== "object") {
            if (
                // allow tabif_ autoinsert fields to be false in oldJSON and true in newJSON
                typeof obj1 == "boolean" &&
                typeof obj2 == "boolean" &&
                obj1 === false &&
                obj2 === true
            ) {
                return path.endsWith(".model.autoinsert");
            }
            if (obj1 !== obj2) {
                this.app.info(path + ":");
                this.app.info(`  - Non-object comparison`);
                this.app.info(`    - ${JSON.stringify(obj1)}`);
                this.app.info(`    - ${JSON.stringify(obj2)}`);
            }
            return obj1 === obj2;
        }

        if (obj1 === null || obj2 === null) {
            if (obj1 !== obj2) {
                this.app.info(path + ":");
                this.app.info(`  - Null comparison`);
                this.app.info(`    - ${JSON.stringify(obj1)}`);
                this.app.info(`    - ${JSON.stringify(obj2)}`);
            }
            return obj1 === obj2;
        }

        const keys1 = [...new Set(Object.keys(obj1))].toSorted();
        const keys2 = [...new Set(Object.keys(obj2))].toSorted();
        const allKeys = new Set(keys1.concat(keys2).toSorted());

        if (keys1.length !== keys2.length) {
            if (path.includes("model.sections")) {
                // ignore sections/sections_order mismatches
                return true;
            }
            this.app.info(path + ":");
            this.app.info(`  - Object key length mismatch`);
            this.app.info(`    - ${keys1.length} !== ${keys2.length}`);
            this.app.info(`    - ${JSON.stringify(keys1)}`);
            this.app.info(`    - ${JSON.stringify(keys2)}`);
            return false;
        }

        for (const key of allKeys) {
            if (!Object.hasOwn(obj1, key)) {
                this.app.info(path + ":");
                this.app.info(`  - Object key mismatch`);
                this.app.info(`    - ${key} not found in oldJSON`);
                return false;
            }
            if (!Object.hasOwn(obj2, key)) {
                this.app.info(path + ":");
                this.app.info(`  - Object key mismatch`);
                this.app.info(`    - ${key} not found in newJSON`);
                return false;
            }
            if (
                !this.compareJSON(
                    flags,
                    path + "." + key,
                    obj1[key as keyof typeof obj1],
                    obj2[key as keyof typeof obj2]
                )
            ) {
                return false;
            }
        }

        return true;
    }

    //#endregion
}
