import type { Config } from "jest";
import { readFileSync } from "fs";
import { resolve } from "path";
import stripJsonComments from "strip-json-comments";

function getPathsFromTsConfig() {
    const tsconfig = JSON.parse(
        stripJsonComments(
            readFileSync(resolve(process.cwd(), "tsconfig.json"), "utf-8")
        )
    );

    const paths = tsconfig.compilerOptions.paths as Record<string, string[]>;
    return Object.entries(paths).reduce<Record<string, string>>(
        (acc, [alias, paths]) => {
            if (paths && paths.length > 0) {
                const kv: Record<string, string> = {};
                if (alias.length > 0) {
                    const key = `^${alias.replace(/\*$/, "(.*)\\.[jt]s?$")}`;
                    const value = `<rootDir>/${paths[0].replace(/\*$/, "$1")}`;
                    kv[key] = value;
                }
                if (alias.includes("*")) {
                    const key = `^${alias.replace(/\*$/, "(.*)$")}`;
                    const value = `<rootDir>/${paths[0].replace(/\*$/, "$1")}.ts`;
                    kv[key] = value;
                }
                return { ...acc, ...kv };
            }
            return acc;
        },
        {}
    );
}

const config: Config = {
    testEnvironment: "node",
    extensionsToTreatAsEsm: [".ts", ".tsx"],
    moduleNameMapper: {
        "^(\\.{1,2}/.*)\\.[jt]s$": "$1",
        ...getPathsFromTsConfig(),
    },
    transform: {
        "^.+\\.(t|j)sx?$": "@swc/jest",
    },
    testMatch: ["<rootDir>/__tests__/**/*.test.ts"],
    setupFilesAfterEnv: ["<rootDir>/__tests__/setup.ts"],
} as const;
export default config;
