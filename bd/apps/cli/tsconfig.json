{"extends": "@clara/config/tsconfig/node.json", "compilerOptions": {"tsBuildInfoFile": "${configDir}/node_modules/.tmp/tsconfig.cli.tsbuildinfo", "paths": {"@/*": ["src/*", "src/*.ts", "src/*/index.ts"], "@commands/*": ["src/commands/*", "src/commands/*.ts", "src/commands/*/index.ts"], "@core/*": ["src/core/*", "src/core/*.ts", "src/core/*/index.ts"], "@dsl": ["src/core/dsl/index.ts"], "@dsl/*": ["src/core/dsl/*", "src/core/dsl/*.ts", "src/core/dsl/*/index.ts"]}}}