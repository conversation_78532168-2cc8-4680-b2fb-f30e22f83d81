import { ConfigEnv, defineConfig } from "vite";
import { readFileSync } from "fs";
import path from "path";
import { patchCssModules } from "vite-css-modules";
import react from "@vitejs/plugin-react";
import { ViteImageOptimizer } from "vite-plugin-image-optimizer";
import stripJsonComments from "strip-json-comments";

type TSConfig = {
    compilerOptions: {
        paths: Record<string, string[]>;
    };
};

const ReactCompilerConfig = {
    target: "19",
};

const getPathsFromTSConfig = (): Record<string, string> => {
    const tsconfig = JSON.parse(
        stripJsonComments(readFileSync("./tsconfig.json", "utf-8"))
    ) as TSConfig;
    return Object.entries(tsconfig.compilerOptions.paths).reduce(
        (acc, [key, [value]]) => ({
            ...acc,
            [key.replace("/*", "")]: path.resolve(
                __dirname,
                "./" + value.replace("/*", "")
            ),
        }),
        {}
    );
};

const isDev = (config: ConfigEnv): boolean => {
    return (
        config.command === "serve" ||
        ["development", "testing"].includes(config.mode)
    );
};

// https://vite.dev/config/
export default defineConfig((config: ConfigEnv) => ({
    css: {
        preprocessorOptions: {
            scss: {
                additionalData: '@use "sass:math"; @use "sass:map";',
            },
        },
    },
    plugins: [
        patchCssModules({
            generateSourceTypes: isDev(config),
        }),
        react({
            babel: {
                plugins: [["babel-plugin-react-compiler", ReactCompilerConfig]],
            },
        }),
        ViteImageOptimizer(),
    ],
    resolve: {
        alias: {
            ...getPathsFromTSConfig(),
        },
    },
    build: {
        assetsInlineLimit: 65535,
        sourcemap: isDev(config),
        target: "esnext",
        rollupOptions: {
            output: {
                manualChunks: (id) => {
                    const node_chunks = {
                        mantine: ["@mantine"],
                        react: ["react"],
                        state: ["swr", "usehooks", "zustand"],
                    };

                    for (const [key, patterns] of Object.entries(node_chunks)) {
                        if (
                            patterns.some((p) =>
                                id.includes("node_modules/" + p)
                            )
                        ) {
                            return key;
                        }
                    }

                    if (id.includes("node_modules/")) {
                        return "react";
                    }

                    return "main";
                },
            },
        },
    },
    optimizeDeps: {
        exclude: ["react-scan"],
    },
    server: {
        allowedHosts: [".local", ".localhost", "localhost", ".clararx.com"],
    },
}));
