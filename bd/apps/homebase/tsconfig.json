{"extends": "@clara/config/tsconfig/base.json", "compilerOptions": {"paths": {"@/*": ["src/*", "src/*.ts[x]?", "src/*/index.ts[x]?"], "@assets/*": ["src/assets/*"], "@blocks": ["src/blocks/index.ts"], "@blocks/*": ["src/blocks/*", "src/blocks/*.ts[x]?", "src/blocks/*/index.ts[x]?"], "@components": ["src/components/index.ts"], "@components/*": ["src/components/*", "src/components/*.ts[x]?", "src/components/*/index.ts[x]?"], "@core": ["src/core/index.ts"], "@core/*": ["src/core/*", "src/core/*.ts[x]?", "src/core/*/index.ts[x]?"], "@dsl": ["src/dsl/index.ts"], "@dsl/*": ["src/dsl/*", "src/dsl/*.ts[x]?", "src/dsl/*/index.ts[x]?"], "@dsl-fields": ["src/dsl/fields/index.ts"], "@dsl-fields/*": ["src/dsl/fields/*", "src/dsl/fields/*.ts[x]?", "src/dsl/fields/*/index.ts[x]?"], "@dsl-forms": ["src/dsl/forms/index.ts"], "@dsl-forms/*": ["src/dsl/forms/*", "src/dsl/forms/*.ts[x]?", "src/dsl/forms/*/index.ts[x]?"], "@fonts/*": ["src/assets/fonts/*"], "@hooks": ["src/hooks/index.ts"], "@hooks/*": ["src/hooks/*", "src/hooks/*.ts[x]?", "src/hooks/*/index.ts[x]?"], "@icons/*": ["src/assets/icons/*"], "@images/*": ["src/assets/images/*"], "@modules/*": ["src/modules/*", "src/modules/*.ts[x]?", "src/modules/*/index.ts[x]?", "src/modules/*.[s]css", "src/modules/*/index.[s]css"], "@styles": ["src/styles/index.scss"], "@styles/*": ["src/styles/*", "src/styles/*.[s]css", "src/styles/*/index.[s]css"], "@type": ["src/type/index.ts"], "@type/*": ["src/type/*", "src/type/*.ts[x]?", "src/type/*/index.ts[x]?"], "@utils": ["src/utils/index.ts"], "@utils/*": ["src/utils/*", "src/utils/*.ts[x]?", "src/utils/*/index.ts[x]?"]}}, "files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}]}