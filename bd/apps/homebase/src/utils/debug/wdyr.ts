import React from "react";

if (import.meta.env.DEV && typeof window !== "undefined") {
    const whyDidYouRender = await import(
        "@welldone-software/why-did-you-render"
    );

    whyDidYouRender.default(React, {
        trackAllPureComponents: false,
        include: [/.x*/],
        exclude: [
            /^@mantine/,
            /^ComposedProviders/,
            /^Mantine/,
            /^React/,
            /^ReduxProvider/,
            /^SWRProvider/,
            /^UIProvider/,
        ],
        collapseGroups: true,
        trackHooks: true,
        logOnDifferentValues: true,
    });
}
