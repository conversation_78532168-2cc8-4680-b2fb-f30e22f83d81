import { create } from "zustand";
import { createTheme, MantineThemeOverride } from "@mantine/core";

import { deepMerge } from "@clara/tslib/utils/deep";
import {
    ThemeCompact,
    ThemeConfig,
    ThemeDark,
    ThemeDevice,
    ThemeState,
    ThemeOptions,
    THEME_DEFAULTS,
} from "@type";
import { mapThemeToMantine } from "./map";
import { themeBase } from "@styles/theme/var-base";
import { themeCompact } from "@styles/theme/var-compact";
import { themeDark } from "@styles/theme/var-dark";
import { themeMobile } from "@styles/theme/var-mobile";
import { themeTablet } from "@styles/theme/var-tablet";

const generateCSSVariables = (
    obj: unknown,
    prefix = ""
): Record<string, string> => {
    const vars: Record<string, string> = {};

    if (typeof obj !== "object" || obj === null) return vars;

    Object.entries(obj).forEach(([key, value]) => {
        const newPrefix = prefix ? `${prefix}-${key}` : key;
        if (typeof value === "string") {
            vars[`--${newPrefix}`] = value;
        } else {
            Object.assign(vars, generateCSSVariables(value, newPrefix));
        }
    });

    return vars;
};

const generateMergedTheme = ({
    compact = THEME_DEFAULTS.compact,
    dark = THEME_DEFAULTS.dark,
    device = THEME_DEFAULTS.device,
}: ThemeOptions): ThemeConfig => {
    // First merge all our themes
    let mergedTheme = themeBase;
    if (compact === "compact")
        mergedTheme = deepMerge(mergedTheme, themeCompact);
    if (dark === "dark") mergedTheme = deepMerge(mergedTheme, themeDark);
    if (device === "mobile") mergedTheme = deepMerge(mergedTheme, themeMobile);
    else if (device === "tablet")
        mergedTheme = deepMerge(mergedTheme, themeTablet);

    return mergedTheme;
};

const createThemeState = (state: ThemeOptions): ThemeState => {
    const mergedTheme = generateMergedTheme(state);
    return {
        ...state,
        config: mergedTheme,
        variables: generateCSSVariables(mergedTheme),
    };
};

type ThemeStore = ThemeState & {
    setDarkMode: (dark: ThemeDark) => void;
    setCompactMode: (compact: ThemeCompact) => void;
    setDeviceMode: (device: ThemeDevice) => void;
    updateTheme: (updates: Partial<ThemeOptions>) => void;
};

export const useThemeStore = create<ThemeStore>((set) => ({
    ...createThemeState(THEME_DEFAULTS),
    setDarkMode: (dark: ThemeDark) => {
        set((state) => {
            if (state.dark === dark) return state;
            return createThemeState({ ...state, dark });
        });
    },
    setCompactMode: (compact: ThemeCompact) => {
        set((state) => {
            if (state.compact === compact) return state;
            return createThemeState({ ...state, compact });
        });
    },
    setDeviceMode: (device: ThemeDevice) => {
        set((state) => {
            if (state.device === device) return state;
            return createThemeState({ ...state, device });
        });
    },
    updateTheme: (updates) => {
        set((state) => createThemeState({ ...state, ...updates }));
    },
}));

export const createMantineTheme = (config: ThemeConfig): MantineThemeOverride =>
    createTheme(mapThemeToMantine(config));

export const useThemeActions = () => {
    const { setDarkMode, setCompactMode, setDeviceMode, updateTheme } =
        useThemeStore();
    return { setDarkMode, setCompactMode, setDeviceMode, updateTheme };
};

export const useThemeCompact = () =>
    useThemeStore((state: ThemeStore) => state.compact);
export const useThemeDark = () =>
    useThemeStore((state: ThemeStore) => state.dark);
export const useThemeDevice = () =>
    useThemeStore((state: ThemeStore) => state.device);
export const useThemeConfig = () =>
    useThemeStore((state: ThemeStore) => state.config);
export const useThemeVariables = () =>
    useThemeStore((state: ThemeStore) => state.variables);

export default useThemeStore;
