import { DEFAULT_THEME, MantineTheme } from "@mantine/core";
import { ThemeConfig } from "@type";

export const mapThemeToMantine = (theme: ThemeConfig): MantineTheme => ({
    ...DEFAULT_THEME,
    breakpoints: {
        xs: theme.breakpoints.xs,
        sm: theme.breakpoints.sm,
        md: theme.breakpoints.md,
        lg: theme.breakpoints.lg,
        xl: theme.breakpoints.xl,
    },
    colors: {
        ...DEFAULT_THEME.colors,
        primary: [
            theme.color.primary.default,
            theme.color.primary.c25,
            theme.color.primary.c50,
            theme.color.primary.c100,
            theme.color.primary.c200,
            theme.color.primary.c300,
            theme.color.primary.c400,
            theme.color.primary.c500,
            theme.color.primary.c600,
            theme.color.primary.c700,
            theme.color.primary.c800,
            theme.color.primary.c900,
        ],
        secondary: [
            theme.color.secondary.default,
            theme.color.secondary.c25,
            theme.color.secondary.c50,
            theme.color.secondary.c100,
            theme.color.secondary.c200,
            theme.color.secondary.c300,
            theme.color.secondary.c400,
            theme.color.secondary.c500,
            theme.color.secondary.c600,
            theme.color.secondary.c700,
            theme.color.secondary.c800,
            theme.color.secondary.c900,
        ],
    },
    fontSizes: {
        xs: theme.font.size.xsmall,
        sm: theme.font.size.small,
        md: theme.font.size.standard,
        lg: theme.font.size.large,
        xl: theme.font.size.xlarge,
    },
    spacing: {
        xs: theme.spacing.xsmall,
        sm: theme.spacing.small,
        md: theme.spacing.standard,
        lg: theme.spacing.large,
        xl: theme.spacing.xlarge,
    },
    radius: {
        xs: theme.radius.xsmall,
        sm: theme.radius.small,
        md: theme.radius.standard,
        lg: theme.radius.large,
        xl: theme.radius.xlarge,
    },
    lineHeights: {
        xs: theme.line.height.xsmall,
        sm: theme.line.height.small,
        md: theme.line.height.standard,
        lg: theme.line.height.large,
        xl: theme.line.height.xlarge,
    },
});

export default mapThemeToMantine;
