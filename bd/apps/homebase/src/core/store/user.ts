import { create } from "zustand";

import { UserOptions, UserState } from "@type";

export interface UserStore extends UserState {
    setLoggedIn: (loggedIn: boolean) => void;
}

const createUserState = (base: UserOptions): UserState => ({
    ...base,
    username: "",
});

export const useUserStore = create<UserStore>((set) => ({
    ...createUserState({ loggedIn: false }),
    setLoggedIn: (loggedIn) => {
        set((state) =>
            state.loggedIn === loggedIn ? state : createUserState({ loggedIn })
        );
    },
}));

export default useUserStore;
