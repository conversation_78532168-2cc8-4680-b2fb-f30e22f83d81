import React from "react";
import { UIProvider } from "./mantine";
import { SWRProvider } from "./swr";

const composeProviders = (
    ...providers: React.FC<React.PropsWithChildren>[]
) => {
    const ComposedComponent = providers.reduce((Accumulated, Current) => {
        const Component = ({ children }: React.PropsWithChildren) => (
            <Accumulated>
                <Current>{children}</Current>
            </Accumulated>
        );
        Component.displayName = `ComposedProvider(${Current.displayName ?? Current.name})`;
        return Component;
    });

    return ComposedComponent;
};

export const ComposedProviders = composeProviders(SWRProvider, UIProvider);
export default ComposedProviders;
