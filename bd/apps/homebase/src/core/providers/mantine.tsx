import { PropsWithChildren, useEffect, useState } from "react";
import { MantineProvider, useMantineColorScheme } from "@mantine/core";
import { useDebounceValue } from "usehooks-ts";

import {
    createMantineTheme,
    useThemeActions,
    useThemeCompact,
    useThemeConfig,
    useThemeDark,
    useThemeDevice,
    useThemeVariables,
} from "@core/store";
import { ThemeState } from "@type";

import "@mantine/core/styles.css";

const getDeviceMode = (
    width: number,
    breakpoints: ThemeState["config"]["breakpoints"]
) => {
    if (width <= parseInt(breakpoints.xs.replace("px", ""))) return "mobile";
    if (width <= parseInt(breakpoints.md.replace("px", ""))) return "tablet";
    return "desktop";
};

const getSystemColorScheme = () =>
    window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark"
        : "light";

const useWindowWidth = () => {
    const [width, setWidth] = useState(window.innerWidth);
    const [debouncedWidth] = useDebounceValue(width, 100);

    useEffect(() => {
        const handler = () => {
            setWidth(window.innerWidth);
        };
        window.addEventListener("resize", handler);
        return () => {
            window.removeEventListener("resize", handler);
        };
    }, []);

    return debouncedWidth;
};

const ColorSchemeUpdater = () => {
    const { setColorScheme } = useMantineColorScheme();
    const themeDark = useThemeDark();
    console.log("mantine color scheme", themeDark);

    useEffect(() => {
        setColorScheme(themeDark);
    }, [themeDark]);

    return null;
};

export function UIProvider({ children }: PropsWithChildren) {
    const width = useWindowWidth();
    const themeActions = useThemeActions();
    const themeCompact = useThemeCompact();
    const themeDark = useThemeDark();
    const themeDevice = useThemeDevice();
    const themeConfig = useThemeConfig();
    const themeVariables = useThemeVariables();
    console.log("mantine ui", themeCompact, themeDark, themeDevice);

    const deviceMode = getDeviceMode(width, themeConfig.breakpoints);
    useEffect(() => {
        themeActions.setDeviceMode(deviceMode);
    }, [deviceMode]);

    useEffect(() => {
        const html = document.documentElement;

        html.classList.remove(
            "theme-compact",
            "theme-standard",

            "theme-dark",
            "theme-light",

            "theme-desktop",
            "theme-mobile",
            "theme-tablet"
        );

        html.classList.add(`theme-${themeCompact}`);
        html.classList.add(
            `theme-${themeDark == "auto" ? getSystemColorScheme() : themeDark}`
        );
        html.classList.add(`theme-${themeDevice}`);
    }, [themeCompact, themeDark, themeDevice]);

    return (
        <>
            <style>
                {`:root { ${Object.entries(themeVariables)
                    .map(([key, value]) => `${key}: ${value}`)
                    .join(";")}; }`}
            </style>
            <MantineProvider
                defaultColorScheme={themeDark}
                withCssVariables={true}
                theme={createMantineTheme(themeConfig)}
            >
                <ColorSchemeUpdater />
                {children}
            </MantineProvider>
        </>
    );
}

export default UIProvider;
