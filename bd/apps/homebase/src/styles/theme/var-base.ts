import type { ThemeConfig } from "@type";

export const themeBase: ThemeConfig = {
    //  Breakpoints for device width //
    breakpoints: {
        xs: "576px",
        sm: "768px",
        md: "992px",
        lg: "1200px",
        xl: "1400px",
    },

    // Color Palette //
    color: {
        // Primary Color Palette //
        primary: {
            default: "#837BB2",
            c25: "#F6F5F9",
            c50: "#F6F5F9",
            c100: "#F6F5F9",
            c200: "#DFDDE3",
            c300: "#C8C5D6",
            c400: "#B1ACC9",
            c500: "#9A94BF",
            c600: "#837BB2",
            c700: "#6C63A5",
            c800: "#5C548C",
            c900: "#4B4573",
            c950: "#433D66",
        },

        // Secondary Color Palette //
        secondary: {
            default: "#5B83B0",
            c25: "#C7E7FF",
            c50: "#B5DCFB",
            c100: "#A3CBED",
            c200: "#91B9DE",
            c300: "#7FA7CF",
            c400: "#6D95BF",
            c500: "#5B83B0",
            c600: "#4971A1",
            c700: "#406591",
            c800: "#305582",
            c900: "#2E4E72",
            c950: "#264362",
        },

        // Success Color Palette //
        success: {
            default: "#7DB8A5",
            c25: "#F6FEF9",
            c50: "#ECFDF3",
            c100: "#DCFAE6",
            c200: "#ABEFC6",
            c300: "#89C9B5",
            c400: "#7DB8A5",
            c500: "#7DB8A5",
            c600: "#529A82",
            c700: "#7DB8A5",
            c800: "#4B8C76",
            c900: "#3D7361",
            c950: "#366656",
        },

        // Warning Color Palette //
        warning: {
            default: "#E89E64",
            c25: "#FEEDE2",
            c50: "#FDE0CE",
            c100: "#F9D0B6",
            c200: "#F6C3A2",
            c300: "#F2B78E",
            c400: "#EEAA7A",
            c500: "#E89E64",
            c600: "#D48656",
            c700: "#E89E64",
            c800: "#BF724A",
            c900: "#AA5F3F",
            c950: "#8F4F33",
        },

        // Error Color Palette //
        error: {
            default: "#E58787",
            c25: "#FEF5F5",
            c50: "#FDEAEA",
            c100: "#FBD8D8",
            c200: "#F7C9C9",
            c300: "#F1B3B3",
            c400: "#EB9D9D",
            c500: "#E58787",
            c600: "#D07676",
            c700: "#BA6666",
            c800: "#A45555",
            c900: "#8F4545",
            c950: "#7F3535",
        },

        // Color Text Palette //
        text: {
            default: "#717680",
            c25: "#FDFDFD",
            c50: "#FAFAFA",
            c100: "#F5F5F5",
            c200: "#E9EAEB",
            c300: "#D5D7DA",
            c400: "#A4A7AE",
            c500: "#717680",
            c600: "#535862",
            c700: "#414651",
            c800: "#252B37",
            c900: "#181D27",
            c950: "#11131A",
        },

        // Background Color Palette //
        background: {
            default: "#CCCCC8",
            c25: "#FCFCFC",
            c50: "#FCFCFC",
            c100: "#FCFCFC",
            c200: "#FAFAF9",
            c300: "#F7F7F5",
            c400: "#F5F5F3",
            c500: "#E5E5E0",
            c600: "#D9D9D4",
            c700: "#C4C4BF",
            c800: "#B2B2AF",
            c900: "#A6A6A2",
            c950: "#999996",
        },

        // Border Color Palette //
        border: {
            default: "#CCCCC8",
            c25: "#FCFCFC",
            c50: "#FCFCFC",
            c100: "#FCFCFC",
            c200: "#FAFAF9",
            c300: "#F7F7F5",
            c400: "#F5F5F3",
            c500: "#E5E5E0",
            c600: "#D9D9D4",
            c700: "#CCCCC8",
            c800: "#B2B2AF",
            c900: "#A6A6A2",
            c950: "#999996",
        },
    },

    // Font Styles //
    font: {
        // Font Sizes //
        size: {
            xxsmall: "12px",
            xsmall: "14px",
            small: "16px",
            standard: "18px",
            medium: "20px",
            large: "24px",
            xlarge: "30px",
            xxlarge: "36px",
            xxxlarge: "42px",
            xxxxlarge: "48px",
            xxxxxlarge: "54px",
            xxxxxxlarge: "60px",
            xxxxxxxlarge: "66px",
            xxxxxxxxlarge: "72px",
            xxxxxxxxxlarge: "84px",
        },

        // Font Weights //
        weight: {
            light: "300",
            regular: "400",
            medium: "500",
            semibold: "600",
            bold: "700",
            bolder: "800",
        },
    },

    // Line Heights //
    line: {
        height: {
            xxsmall: "12px",
            xsmall: "14px",
            small: "16px",
            standard: "18px",
            medium: "20px",
            large: "22px",
            xlarge: "24px",
            xxlarge: "26px",
            xxxlarge: "28px",
            xxxxlarge: "30px",
            xxxxxlarge: "32px",
            xxxxxxlarge: "34px",
            xxxxxxxlarge: "36px",
            xxxxxxxxlarge: "38px",
            xxxxxxxxxlarge: "40px",
        },
    },

    // Basic Dimensions //
    spacing: {
        xxsmall: "2px",
        xsmall: "4px",
        small: "6px",
        standard: "8px",
        medium: "9px",
        large: "10px",
        xlarge: "12px",
        xxlarge: "14px",
        xxxlarge: "16px",
        xxxxlarge: "18px",
        xxxxxlarge: "20px",
        xxxxxxlarge: "24px",
        xxxxxxxlarge: "26px",
        xxxxxxxxlarge: "28px",
        xxxxxxxxxlarge: "32px",
    },

    // Basic Radiuses //
    radius: {
        xxsmall: "2px",
        xsmall: "4px",
        small: "6px",
        standard: "8px",
        medium: "9px",
        large: "10px",
        xlarge: "12px",
        xxlarge: "14px",
        xxxlarge: "16px",
        xxxxlarge: "18px",
        xxxxxlarge: "20px",
        xxxxxxlarge: "24px",
        xxxxxxxlarge: "28px",
        xxxxxxxxlarge: "32px",
        xxxxxxxxxlarge: "36px",
    },
} as const;

export default themeBase;
