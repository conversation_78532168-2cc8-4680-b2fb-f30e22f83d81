/* stylelint-disable selector-pseudo-class-no-unknown */
@mixin compact() {
    :global(html.theme-compact:not(.theme-mobile, .theme-tablet)) & {
        @content;
    }
}

@mixin compact-dark() {
    :global(html.theme-compact.theme-dark:not(.theme-mobile, .theme-tablet)) & {
        @content;
    }
}

@mixin dark() {
    :global(html.theme-dark) & {
        @content;
    }
}

@mixin mobile() {
    :global(html.theme-mobile) & {
        @content;
    }
}

@mixin mobile-dark() {
    :global(html.theme-mobile.theme-dark) & {
        @content;
    }
}

@mixin tablet() {
    :global(html.theme-tablet) & {
        @content;
    }
}

@mixin tablet-dark() {
    :global(html.theme-tablet.theme-dark) & {
        @content;
    }
}

@mixin landscape() {
    @media (orientation: landscape) {
        @content;
    }
}

@mixin portrait() {
    @media (orientation: portrait) {
        @content;
    }
}
