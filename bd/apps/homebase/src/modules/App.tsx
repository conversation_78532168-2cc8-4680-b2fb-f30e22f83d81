import { lazy, Suspense, useState } from "react";
import { Box } from "@mantine/core";

import { useThemeConfig } from "@core/store/theme";
const ThemeControls = lazy(() => import("@components/theme/ThemeControls"));

import reactLogo from "@images/react.svg";
import viteLogo from "@images/vite.svg";
import "./App.scss";

import { dslForm, type DSLReq } from "@clara/dsl";

// get /api/dsl
const getDSL = async () => {
    const res = await fetch("/api/dsl/");
    const dsl = await res.json();
    return dsl satisfies Record<Lowercase<string>, object>;
};

window.DSL = {};
window.DSLNew = {};
window.DSLRaw = await getDSL();

console.clear();
console.time("dsl");

for (const [form, raw] of Object.entries(window.DSLRaw)) {
    const f = form.toLowerCase() as Lowercase<string>;
    window.DSLNew[f] = dslForm(form, raw as DSLReq);
    window.DSL[f] = window.DSLNew[f]();
}

console.log(Object.keys(window.DSL));

import type { d as dsl_patient } from "@clara/dsl/patient";
import type { dsl_assessment } from "@clara/dsl/assessment";

const q = window.DSLNew.patient() as dsl_patient;
const a = window.DSLNew.assessment() as dsl_assessment;

console.log(
    a.fields.therapy_1.model.ˆparent.ˆparent.ˆroot.model.ˆparent.fields
        .therapy_1.model.if.factor.fields
);

console.log(q.fields.archived.ˆset("ok"));
console.log("archived get", q.fields.archived.ˆget());
console.log("auto_dc_pt label", q.fields.auto_dc_pt.view.label);
console.log("all fields", q.fields.ˆget());
console.log("model.sections", JSON.parse(JSON.stringify(q.model.sections)));
console.log(
    "model.sections.Clinical",
    q.model.sections["Clinical Alert"].fields
);

console.timeEnd("dsl");

export function App() {
    const [count, setCount] = useState(0);
    const themeConfig = useThemeConfig();
    console.log("app", count);

    return (
        <>
            <Suspense fallback={<div>Loading...</div>}>
                <ThemeControls />
            </Suspense>
            {/* using mantine theme vars */}
            <div
                style={{
                    backgroundColor: themeConfig.color.success.c900,
                }}
            >
                <a href="https://vite.dev" target="_blank" rel="noreferrer">
                    <img src={viteLogo} className="logo" alt="Vite logo" />
                </a>
                <a href="https://react.dev" target="_blank" rel="noreferrer">
                    <img
                        src={reactLogo}
                        className="logo react"
                        alt="React logo"
                    />
                </a>
            </div>
            {/* using style tsx value */}
            <h1 color={themeConfig.color.primary.default}>Vite + React</h1>
            <Box className="card" p="lg" m="lg">
                {/* using styles/theme/css vars */}
                <button
                    onClick={() => {
                        setCount((count) => count + 1);
                    }}
                    style={{ backgroundColor: "var(--color-primary-default)" }}
                >
                    count is {count}
                </button>
                <p>
                    Edit <code>src/App.tsx</code> and save to test HMR
                </p>
            </Box>
            <p className="read-the-docs">
                Click on the Vite and React logos to learn more
            </p>
        </>
    );
}

export default App;
