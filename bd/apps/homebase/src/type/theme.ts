import { MantineBreakpointsValues, MantineColorScheme } from "@mantine/core";
import { TypeMerge, TypePartial } from "@clara/tslib/type";

export type ColorScale = {
    default: string;
    c25: string;
    c50: string;
    c100: string;
    c200: string;
    c300: string;
    c400: string;
    c500: string;
    c600: string;
    c700: string;
    c800: string;
    c900: string;
    c950: string;
};

export type SizeScale = {
    xxsmall: string;
    xsmall: string;
    small: string;
    standard: string;
    medium: string;
    large: string;
    xlarge: string;
    xxlarge: string;
    xxxlarge: string;
    xxxxlarge: string;
    xxxxxlarge: string;
    xxxxxxlarge: string;
    xxxxxxxlarge: string;
    xxxxxxxxlarge: string;
    xxxxxxxxxlarge: string;
};

export type ThemeConfig = {
    breakpoints: MantineBreakpointsValues;
    color: {
        primary: ColorScale;
        secondary: ColorScale;
        success: ColorScale;
        warning: ColorScale;
        error: ColorScale;
        text: ColorScale;
        background: ColorScale;
        border: ColorScale;
    };
    font: {
        size: SizeScale;
        weight: Record<string, string>;
    };
    line: {
        height: SizeScale;
    };
    spacing: SizeScale;
    radius: SizeScale;
};

export type ThemeOverride = TypePartial<ThemeConfig>;

export type ThemeCompact = "compact" | "standard";

export type ThemeDark = MantineColorScheme;

export type ThemeDevice = "desktop" | "mobile" | "tablet";

export type ThemeOptions = {
    compact: ThemeCompact;
    dark: ThemeDark;
    device: ThemeDevice;
};

export type ThemeState = TypeMerge<
    ThemeOptions & {
        config: ThemeConfig;
        variables: Record<string, string>;
    }
>;

export const THEME_DEFAULTS: ThemeOptions = {
    compact: "standard",
    dark: "auto",
    device: "tablet",
} as const;
