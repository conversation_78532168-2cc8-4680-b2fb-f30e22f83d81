import { ChangeEvent, useCallback } from "react";
import { Box, Group, SegmentedControl, Stack, Switch } from "@mantine/core";

import { ThemeDark, ThemeDevice } from "@type";
import {
    useThemeActions,
    useThemeCompact,
    useThemeDark,
    useThemeDevice,
} from "@core/store/theme";

import styles from "./ThemeControls.module.scss";

const deviceList: { label: string; value: ThemeDevice }[] = [
    { label: "Mobile", value: "mobile" },
    { label: "Tablet", value: "tablet" },
    { label: "Desktop", value: "desktop" },
];

const themeList: { label: string; value: ThemeDark }[] = [
    { label: "Light", value: "light" },
    { label: "Dark", value: "dark" },
    { label: "Auto", value: "auto" },
];

export function ThemeControls() {
    const themeActions = useThemeActions();
    const themeCompact = useThemeCompact();
    const themeDark = useThemeDark();
    const themeDevice = useThemeDevice();
    console.log("theme controls", themeCompact, themeDark, themeDevice);

    const handleCompactChange = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            themeActions.setCompactMode(
                event.currentTarget.checked ? "compact" : "standard"
            );
        },
        [themeActions]
    );

    const handleDeviceChange = useCallback(
        (value: string) => {
            themeActions.setDeviceMode(value as ThemeDevice);
        },
        [themeActions]
    );

    const handleThemeChange = useCallback(
        (value: string) => {
            themeActions.setDarkMode(value as ThemeDark);
        },
        [themeActions]
    );

    return (
        <Box className={styles.root} p="md" maw={400} w="50%" mx="auto">
            <Stack>
                <Group justify="center" w="100%">
                    <Switch
                        className={styles.label}
                        label={
                            "Compact mode: " +
                            (themeCompact == "compact" ? "On" : "Off")
                        }
                        checked={themeCompact == "compact"}
                        onChange={handleCompactChange}
                        mb="sm"
                    />
                </Group>

                <SegmentedControl
                    value={themeDark}
                    data={themeList}
                    onChange={handleThemeChange}
                    mb="sm"
                />

                <SegmentedControl
                    value={themeDevice}
                    data={deviceList}
                    onChange={handleDeviceChange}
                />
            </Stack>
        </Box>
    );
}

export default ThemeControls;
