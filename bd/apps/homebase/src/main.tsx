//import "@/utils/debug";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";

import { ComposedProviders } from "@core/providers";
import App from "@modules/App";

const rootElement = document.getElementById("root");
if (rootElement) {
    createRoot(rootElement).render(
        <StrictMode>
            <ComposedProviders>
                <App />
            </ComposedProviders>
        </StrictMode>
    );
} else {
    console.error("Root element not found");
}
