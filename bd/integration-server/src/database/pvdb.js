import Database from "better-sqlite3";
import path from "path";

export class PVerifyDB {
    /**
     * Constructs a new instance of the Ncpdpdb class.
     * @param {Object} opts - The options for the Ncpdpdb instance.
     * @param {Object} app - The application object.
     * @throws {Error} Throws an error if no opts is specified.
     */
    constructor(app, opts) {
        this.app = app;
        this.dbdir = app.db_root + "/customer";
        this.db_name = path.join(this.dbdir, "default.db");
        this.app.log.debug(
            `PVerifyDB initialized with db_name: ${this.db_name}`
        );
        this.db_clients = this.init();
    }

    init() {
        const dbc = [];
        const dbp = path.join(this.dbdir, "default.db");
        this.app.log.info(`Initializing database at path: ${dbp}`);

        try {
            const db = new Database(dbp, {});
            db.exec(`
                CREATE TABLE if not exists pverify_request(
                    [id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    [request_type] TEXT,
                    [customer_id] TEXT NOT NULL,
                    [customer_name] TEXT,
                    [clara_mrn] TEXT,
                    [member_id] TEXT,
                    [member_type] TEXT,
                    [request_json] BLOB,
                    [response_json] BLOB,
                    [pv_request_id] TEXT,
                    [pv_response_code] TEXT,
                    [s3_filehash] BLOB,
                    [errors] TEXT,
                    [latest_request_dt] DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);
            for (const field of this.indexes) {
                db.exec(
                    `CREATE INDEX IF NOT EXISTS idx_pverify_request_${field} ON pverify_request(${field});`
                );
            }
            dbc.push(db);
        } catch (err) {
            this.app.log.error(`Error initializing db: ${err.message}`);
            throw new Error(err);
        }
        return dbc;
    }

    insert_new(data) {
        if (!data) {
            throw new Error("No data passed for insert.");
        }
        for (let db of this.db_clients) {
            let d;
            let mrn = data.clara_mrn ? data.clara_mrn : null;
            db = db.open ? db : new Database(db.name);
            const dbn = db.name?.split("/").pop();
            if (!mrn) {
                try {
                    d = JSON.parse(data);
                    mrn = d.request_json?.body?.internalID
                        ? d.request_json.body.internalID
                        : null;
                } catch (err) {
                    this.app.log.error(
                        "Error parsing data trying to get clara_mrn: " +
                            JSON.stringify(err)
                    );
                }
            }
            let result;
            try {
                result = db
                    .prepare(
                        "INSERT INTO pverify_request (" +
                            "request_type, customer_id, customer_name, clara_mrn, member_id, member_type, " +
                            "request_json, response_json, pv_request_id, pv_response_code, " +
                            "s3_filehash, errors) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
                    )
                    .run(
                        data.request_type,
                        data.customer_id ? data.customer_id : this.customer_id,
                        data.customer_name
                            ? data.customer_name
                            : this.customer_name,
                        mrn,
                        data.member_id,
                        data.member_type,
                        data.request_json,
                        data.response_json,
                        data.pv_request_id,
                        data.pv_response_code,
                        data.s3_filehash,
                        data.errors
                    );
                this.app.log.info(
                    `Inserted into db: ${dbn}, with result:  ${JSON.stringify(result)}`
                );
            } catch (err) {
                this.app.log.error(
                    "error inserting into db: " +
                        dbn +
                        ": " +
                        JSON.stringify(err)
                );
                throw new Error(err);
            } finally {
                db.close();
            }
            if (result?.changes && dbn != "default.db") {
                return result;
            }
        }
    }

    check_filehash(fhash) {
        this.init();

        const db = new Database(
            this.db_clients.length > 1
                ? this.db_clients[1].name
                : this.db_clients[0].name,
            {}
        );
        try {
            const row = db
                .prepare(
                    `
                SELECT s3_filehash 
                FROM pverify_request 
                WHERE s3_filehash like ? 
            `
                )
                .get("%" + fhash + "%");
            this.app.log.info(
                `Checked filehash: ${fhash} with result: ${JSON.stringify(row)}`
            );
            if (row && row.s3_filehash) {
                return row.s3_filehash;
            }

            return row;
        } catch (err) {
            this.app.log.error("error checking filehash: " + err.message);
            throw new Error(err);
        } finally {
            db.close((err) => {
                if (err) {
                    this.app.log.error(err);
                }
            });
        }
    }
}
