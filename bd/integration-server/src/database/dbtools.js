import Database from "better-sqlite3";
import path from "path";

/**
 * Creates an index on the specified table and fields in the database.
 *
 * @param {string} table - The name of the table.
 * @param {string[]} fields - An array of field names to create the index on.
 * @throws {Error} If an error occurs while creating the index.
 */
export function create_indexes(app, db, table_name, columns) {
    if (!db || typeof db.prepare !== "function") {
        throw new Error(
            `Invalid database object passed to create_indexes for table ${table_name}`
        );
    }

    app.log.debug(
        `Creating indexes for table ${table_name} in database ${db.name}`
    );

    for (const column of columns) {
        const indexName = `idx_${table_name}_${column}`;
        const query = `CREATE INDEX IF NOT EXISTS ${indexName} ON ${table_name} (${column})`;

        try {
            app.log.debug(`Executing query: ${query}`);
            db.prepare(query).run();
            app.log.debug(`Successfully created index ${indexName}`);
        } catch (err) {
            app.log.error(`<PERSON>rror creating index ${indexName}: ${err.message}`);
            app.log.error(`Failed query: ${query}`);
            throw err; // Re-throw the error to be caught in the calling function
        }
    }
}

/**
 * Retrieves filtered rows from the database based on the provided parameters.
 *
 * @param {Object} params - The parameters used for filtering the rows.
 * @returns {Array} - An array of rows that match the filter criteria.
 * @throws {Error} - If there is an error executing the database query.
 */
export function select_filtered(app, opts) {
    try {
        if (!opts.table_name) {
            throw new Error("Table name is required");
        }
        if (!opts.params) {
            throw new Error("Params are required");
        }
        if (!opts.customer_name) {
            throw new Error("Customer name is required");
        }
        let db;
        if (!opts.db) {
            const db_path = path.join(
                opts.db_root,
                opts.customer_name,
                opts.customer_name + ".db"
            );
            db = new Database(db_path);
        } else {
            db = opts.db;
        }
        if (!db) {
            throw new Error("Database is required");
        }
        const keys = Object.keys(opts.params);
        const values = Object.values(opts.params).map(
            (value) => "%" + value + "%"
        );
        const whereClause = keys
            .map((key, _i) => `${key} like ?`)
            .join(" AND ");
        const limit = opts.limit || 1000;
        const offset = opts.offset || 0;
        const stmt = db.prepare(
            `SELECT * FROM ${opts.table_name} WHERE ${whereClause} ORDER BY created_at LIMIT ${limit} OFFSET ${offset}`
        );
        app.log.info(
            `SQL: SELECT * FROM ${opts.table_name} WHERE ${whereClause} with values: ${values} LIMIT ${limit} OFFSET ${offset}`
        );
        const rows = stmt.all(...values);
        return rows;
    } catch (err) {
        app.log.error(`Error selecting filtered rows: ${err}`);
        throw err;
    }
}
