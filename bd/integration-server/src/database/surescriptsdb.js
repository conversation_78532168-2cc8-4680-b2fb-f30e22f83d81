import Database from "better-sqlite3";
import path from "path";
import { create_indexes } from "./dbtools.js";

class SSErxDB {
    /**
     * Constructs a new instance of the Ncpdpdb class.
     * @param {Object} opts - The options for the Ncpdpdb instance.
     * @param {Object} app - The application object.
     * @throws {Error} Throws an error if no opts is specified.
     * @throws {Error} Throws an error if no dbdir is specified.
     * @throws {Error} Throws an error if no db_name is specified.
     */
    constructor(opts, app) {
        if (!opts) {
            throw new Error("No opts specified");
        }
        this.data = opts.rdata;
        this.rxml = opts.rawBody;
        this.dbdir = app.db_root + "customer/";
        this.db_name = "default.db";
        this.db_path = path.join(this.dbdir, this.db_name);
        this.app = app;
        this.tx_direction = opts.direction;
        this.errors = opts.errors;
        this.message_header = this.data.Message.Header;
        this.message_hash = opts.message_hash;
        this.message_body = this.data.Message.Body;
        this.mid = this.message_header.MessageID;
        this.rmid = this.message_header.RelatedMessageID;
        this.mtype = Object.keys(this.data.Message.Body)[0];
        this.ncpdp_to = this.message_header.To;
        this.ncpdp_from = this.message_header.From;
        this.customer_id = opts.customer_id;
        this.customer_name = opts.customer_name;
    }

    /**
     * Inserts a new record into the database.
     * @throws {Error} If no direction is specified for inbound/outbound.
     * @returns {Object} The result of the database insertion.
     */
    insert_new() {
        if (!this.tx_direction) {
            throw new Error("No direction specified for inbound/outbound");
        }
        const dbs = ["default.db", this.db_name].filter(
            (x, i, a) => a.indexOf(x) === i
        );
        for (const dbn of dbs) {
            const dbp = path.join(this.dbdir, dbn);
            const db = new Database(dbp, {});
            let result;
            try {
                db.exec(`
                    CREATE TABLE if not exists ss_message(
                        [id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        [tx_direction] TEXT,
                        [message_id] TEXT NOT NULL,
                        [customer_id] TEXT,
                        [customer_name] TEXT,
                        [related_message_id] TEXT,
                        [source_ncpdp_id] TEXT,
                        [dest_ncpdp_id] TEXT,
                        [message_type] TEXT,
                        [message_header] blob,
                        [message_body] blob,
                        [raw_json] blob,
                        [raw_xml] blob,
                        [message_hash] blob,
                        [errors] TEXT,
                        [processed] INTEGER DEFAULT 0,
                        [delivered] INTEGER DEFAULT 0,
                        UNIQUE(message_id, tx_direction)
                    )
                `);
                // create indexes if they dont exist
                create_indexes(this.app, db, "ss_message", [
                    "message_id",
                    "customer_id",
                    "customer_name",
                    "source_ncpdp_id",
                    "dest_ncpdp_id",
                    "message_type",
                ]);
                result = db
                    .prepare(
                        `
                    INSERT INTO ss_message (
                        tx_direction,
                        message_id,
                        customer_id,
                        customer_name,
                        related_message_id,
                        source_ncpdp_id,
                        dest_ncpdp_id,
                        message_type,
                        message_header,
                        message_body,
                        raw_json,
                        raw_xml,
                        message_hash,
                        errors
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `
                    )
                    .run(
                        this.tx_direction,
                        this.mid,
                        this.customer_id,
                        this.customer_name,
                        this.rmid,
                        String(this.ncpdp_to),
                        String(this.ncpdp_from),
                        this.mtype,
                        JSON.stringify(this.message_header),
                        JSON.stringify(this.message_body),
                        JSON.stringify(this.data),
                        this.rxml,
                        JSON.stringify(this.message_hash),
                        JSON.stringify(this.errors)
                    );
                this.app.log.info(
                    `Inserted into db: ${dbn}, with result:  ${JSON.stringify(result)}`
                );
            } catch (err) {
                this.app.log.error(
                    "error inserting into db: " + dbn + ": " + err.message
                );
                //                if (!db.inTransaction && !err.message.includes('UNIQUE constraint failed')) throw err;
                throw new Error(err);
            } finally {
                db.close((err) => {
                    if (
                        err &&
                        !err.message.includes("UNIQUE constraint failed")
                    ) {
                        this.app.log.error(err);
                    }
                });
            }
            if (result?.changes && dbn != "default.db") {
                return result;
            }
        }
    }
    /**
     * Retrieves all rows from the ss_message table based on the provided NCPDP ID and direction.
     *
     * @param {string} ncpdp_id - The NCPDP ID to filter the results.
     * @param {string} direction - The direction to filter the results.
     * @returns {Array} An array of rows from the ss_message table.
     */
    select_all(ncpdp_id, direction) {
        const db = new Database(this.db_path);
        try {
            const stmt = db.prepare(
                "SELECT * FROM ss_message WHERE source_ncpdp_id = ? AND tx_direction = ?"
            );
            stmt.bind(ncpdp_id, direction);
            const rows = stmt.all();
            return rows;
        } catch (err) {
            this.app.log.error(err);
        } finally {
            db.close((err) => {
                if (err) {
                    this.app.log.error(err);
                }
            });
        }
    }

    /**
     * Retrieves a message from the database based on the provided message ID.
     *
     * @param {number} message_id - The ID of the message to retrieve.
     * @returns {object} - The retrieved message object from the database.
     */
    select_message(message_id) {
        const db = new Database(this.db_path);
        try {
            const stmt = db.prepare(
                "SELECT * FROM ss_message WHERE message_id = ?"
            );
            stmt.bind(message_id);
            const row = stmt.get();
            return row;
        } catch (err) {
            this.app.log.error(err);
        } finally {
            db.close((err) => {
                if (err) {
                    this.app.log.error(err);
                }
            });
        }
    }

    /**
     * Inserts an error into the database.
     *
     * @param {Error} err - The error object.
     * @param {string} xml - The XML data associated with the error.
     * @returns {void}
     */
    insert_error(err, xml) {
        const db = new Database(this.db_path);
        try {
            db.exec(
                `CREATE TABLE if not exists ss_errors([id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP, [message_id] TEXT NOT NULL, [related_message_id] TEXT, [ncpdp_from] TEXT , [ncpdp_to] TEXT, [errors] TEXT, [xmld] TEXT, [jsond] BLOB, UNIQUE(message_id, error))`
            );
            const stmt = db.prepare(
                "INSERT INTO validation_errors (message_id, customer_id, customer_name, related_message_id, ncpdp_from, ncpdp_to, errors, xmld, jsond) VALUES (?, ?, ?, ?, ?)"
            );
            stmt.run(
                this.mid,
                this.customer_id,
                this.customer_name,
                this.ncpdp_from,
                this.ncpdp_to,
                err,
                xml
            );
        } catch (err) {
            this.app.log.error(err);
        } finally {
            db.close((err) => {
                if (err) {
                    this.app.log.error(err);
                }
            });
        }
    }
}

class SSDirectoryDB {
    constructor(opts, app) {
        if (!opts) {
            throw new Error("No opts specified");
        }
        if (!opts.dir_type) {
            throw new Error(
                "No dir_type specified, must be organization or provider"
            );
        }
        this.app = app;
        this.dbdir = opts.db_dir
            ? opts.db_dir
            : this.app.db_root + "directory/";
        this.dir_type = opts.dir_type;
        this.db_name = "directory.db";
        this.db_path = path.join(this.dbdir, this.db_name);

        if (!this.dbdir) {
            throw new Error("No dbdir specified");
        }
        if (!this.db_name) {
            throw new Error("No db_name specified");
        }
        this.db = this.init();
        this.table_name =
            this.dir_type == "organization"
                ? "organizations"
                : this.dir_type == "provider"
                  ? "providers"
                  : null;
    }

    /**
     * Initializes the NCPDP database by creating the necessary tables and indexes.
     * @returns {Database} The initialized database object.
     * @throws {Error} If there is an error creating the tables.
     */
    init() {
        const db = new Database(this.db_path, {});
        const indexes = {
            organizations: [
                "OrganizationID",
                "NCPDPID",
                "OrganizationName",
                "NPI",
            ],
            providers: [
                "SPI",
                "OrganizationID",
                "NPI",
                "FirstName",
                "LastName",
            ],
        };

        const org_table = `
        CREATE TABLE IF NOT EXISTS organizations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            NCPDPID ANY,
            StoreNumber ANY,
            OrganizationName ANY,
            AddressLine1 ANY,
            AddressLine2 ANY,
            City ANY,
            StateProvince ANY,
            PostalCode ANY,
            CountryCode ANY,
            StandardizedAddressLine1 ANY,
            StandardizedAddressLine2 ANY,
            StandardizedCity ANY,
            StandardizedStateProvince ANY,
            StandardizedPostal ANY,
            PrimaryTelephone ANY,
            Fax ANY,
            ElectronicMail ANY,
            AlternatePhoneNumbers ANY,
            ActiveStartTime ANY,
            ActiveEndTime ANY,
            ServiceLevel ANY,
            PartnerAccount ANY,
            LastModifiedDate ANY,
            CrossStreet ANY,
            RecordChange ANY,
            OldServiceLevel ANY,
            Version ANY,
            NPI ANY,
            DirectorySpecialtyName ANY,
            ReplaceNCPDPID ANY,
            StateLicenseNumber ANY,
            UPIN ANY,
            FacilityID ANY,
            MedicareNumber ANY,
            MedicaidNumber ANY,
            PayerID ANY,
            DEANumber ANY,
            HIN ANY,
            MutuallyDefined ANY,
            DirectAddress ANY,
            OrganizationType ANY,
            OrganizationID ANY,
            ParentOrganizationID ANY,
            Latitude ANY,
            Longitude ANY,
            Precise ANY,
            UseCase ANY,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(OrganizationID, NPI, AddressLine1, LastModifiedDate)
        )`;

        const provider_table = `
        CREATE TABLE IF NOT EXISTS providers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            SPI ANY,
            NPI ANY,
            DEANumber ANY,
            StateLicenseNumber ANY,
            Specialty ANY,
            Prefix ANY,
            LastName ANY,
            FirstName ANY,
            MiddleName ANY,
            Suffix ANY,
            BusinessName ANY,
            AddressLine1 ANY,
            AddressLine2 ANY,
            City ANY,
            StateProvince ANY,
            PostalCode ANY,
            CountryCode ANY,
            StandardizedAddressLine1 ANY,
            StandardizedAddressLine2 ANY,
            StandardizedCity ANY,
            StandardizedStateProvince ANY,
            StandardizedPostalCode ANY,
            PrimaryTelephone ANY,
            Fax ANY,
            ElectronicMail ANY,
            AlternatePhoneNumbers ANY,
            ActiveStartTime ANY,
            ActiveEndTime ANY,
            ServiceLevel ANY,
            PartnerAccount ANY,
            LastModifiedDate ANY,
            RecordChange ANY,
            OldServiceLevel ANY,
            Version ANY,
            DirectorySpecialtyName ANY,
            MedicareNumber ANY,
            MedicaidNumber ANY,
            UPIN ANY,
            CertificateToPrescribe ANY,
            Data2000WaiverID ANY,
            REMSHealthCareProviderEnrollmentID ANY,
            StateControlSubstanceNumber ANY,
            MutuallyDefined ANY,
            DirectAddress ANY,
            UseCases ANY,
            AvailableRoutes ANY,
            OrganizationID ANY,
            Latitude ANY,
            Longitude ANY,
            Precise ANY,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(SPI, NPI, OrganizationID, LastModifiedDate)
        )
        `;

        try {
            db.exec(org_table);
            db.exec(provider_table);
            this.app.log.info(
                `Created table: organizations in db: ${this.db_name}`
            );
            this.app.log.info(
                `Created table: providers in db: ${this.db_name}`
            );
            create_indexes(
                this,
                this.db_path,
                "providers",
                indexes["providers"]
            );
            create_indexes(
                this,
                this.db_path,
                "organizations",
                indexes["organizations"]
            );

            return db;
        } catch (err) {
            this.app.log.error("error creating table: " + err.message);
            throw new Error(err);
        }
    }
    /**
     * Inserts or updates records into the specified table in the database.
     *
     * @param {Array<Object>} dataArray - An array of objects representing the data to be inserted or updated.
     * @returns {number} - The total number of records inserted or updated.
     * @throws {Error} - If an error occurs during the insertion or update process.
     */
    insert_or_update(dataArray) {
        const uniqueIndexes = {
            organizations: [
                "OrganizationID",
                "NPI",
                "AddressLine1",
                "LastModifiedDate",
            ],
            providers: ["SPI", "NPI", "OrganizationID", "LastModifiedDate"],
        };
        const db = new Database(this.db_path);
        const BATCH_SIZE = 500; // Adjust this value as needed

        db.exec("PRAGMA journal_mode=WAL");

        const SCHEMA = Object.keys(dataArray[0]);
        const updateFields = SCHEMA.filter(
            (key) => key !== "createdAt" && key !== "updatedAt"
        );

        const insertTransaction = db.transaction((data) => {
            const uniqueIndex = uniqueIndexes[this.table_name].join(", ");
            const sql = `INSERT INTO ${this.table_name} (${SCHEMA.join(", ")}) VALUES (${SCHEMA.map(
                () => "?"
            ).join(", ")}) 
                     ON CONFLICT(${uniqueIndex}) DO UPDATE SET 
                     ${updateFields.map((key) => `${key}=excluded.${key}`).join(", ")}, updatedAt=CURRENT_TIMESTAMP`;
            const stmt = db.prepare(sql);

            for (const item of data) {
                const values = SCHEMA.map((key) => item[key] || null);
                stmt.run(values);
            }

            this.app.log.info(
                `Inserted or updated ${data.length} records into table: ${this.table_name}`
            );
            if (data.length > 0) {
                this.app.log.info(
                    `Last inserted or updated record: ${JSON.stringify(data[data.length - 1])}`
                );
                return data.length;
            }
        });

        try {
            // Split the data array into batches
            for (let i = 0; i < dataArray.length; i += BATCH_SIZE) {
                const batch = dataArray.slice(i, i + BATCH_SIZE);
                insertTransaction(batch);
            }
            return dataArray.length;
        } catch (err) {
            this.app.log.error(err);
            throw new Error(err);
        } finally {
            if (db.open) {
                db.close();
            }
        }
    }
    /**
     * Retrieves filtered rows from the database based on the provided parameters.
     *
     * @param {Object} params - The parameters used for filtering the rows.
     * @returns {Array} - An array of rows that match the filter criteria.
     * @throws {Error} - If there is an error executing the database query.
     */
    select_filtered(params) {
        const db = new Database(this.db_path);
        try {
            const keys = Object.keys(params);
            const values = Object.values(params).map(
                (value) => "%" + value + "%"
            );
            const whereClause = keys
                .map((key, _i) => `${key} like ?`)
                .join(" AND ");
            const stmt = db.prepare(
                `SELECT * FROM ${this.table_name} WHERE ${whereClause} ORDER BY LastModifiedDate LIMIT 1000`
            );
            this.app.log.info(
                `SQL: SELECT * FROM ${this.table_name} WHERE ${whereClause} with values: ${values} LIMIT 1000`
            );
            const rows = stmt.all(...values);
            return rows;
        } catch (err) {
            this.app.log.error(err);
            throw new Error(err);
        } finally {
            if (db.open) {
                db.close();
            }
        }
    }
    /**
     * Creates an index on the specified table and fields in the database.
     *
     * @param {string} table - The name of the table.
     * @param {string[]} fields - An array of field names to create the index on.
     * @throws {Error} If an error occurs while creating the index.
     */
    create_index(table, fields) {
        const db = new Database(this.db_path);
        try {
            for (const field of fields) {
                db.exec(
                    `CREATE INDEX IF NOT EXISTS idx_${table}_${field} ON ${table}(${field});`
                );
                const indexExists = db
                    .prepare(
                        `SELECT name FROM sqlite_master WHERE type='index' AND name='idx_${table}_${field}'`
                    )
                    .get();
                if (!indexExists) {
                    this.app.log.info(`Index idx_${table}_${field} created.`);
                }
            }
        } catch (err) {
            this.app.log.error(err);
            throw new Error(err);
        } finally {
            if (db.open) {
                db.close();
            }
        }
    }
}

export { SSErxDB, SSDirectoryDB };
