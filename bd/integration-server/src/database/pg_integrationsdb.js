import { tableSchemas, tableIndexes } from "./pg_schemas.js";
import { safeJSONStringify } from "../utils/tools.js";

export class PgIntegrationsDB {
    constructor(app, opts = {}) {
        this.app = app;
        this.opts = opts;
        this.pool = app.pg.integrations;

        if (!this.pool) {
            throw new Error(
                "PostgreSQL pool not available at app.pg.integrations"
            );
        }

        // Define schemas
        this.schemas = {
            system: "is_system", // system tables (control numbers etc)
            default: "is_default", // default claims
            customer: opts.customer_env_name || opts.customer_name || null, // customer schema (no prefix)
            directory: "is_directory", // surescripts directory data
        };

        // Log schema selection for debugging
        this.app.log.debug(
            `Initialized schemas with customer: ${this.schemas.customer}, from env: ${opts.customer_env_name}, name: ${opts.customer_name}`
        );

        // Define all tables with their schemas
        this.tables = {
            // System tables
            control_numbers: `${this.schemas.system}.control_numbers`,

            // Claims tables
            ncpdp_claims: "ncpdp_claims",
            medical_claims: "medical_claims",
            medical_claims_reports: "medical_claims_reports",
            gr_medical_claims_reports: "gr_medical_claims_reports",

            // PVerify tables
            pverify_request: "pverify_request",

            // Surescripts tables
            ss_message: "ss_message",
            organizations: "organizations",
            providers: "providers",

            // Credentials table
            credentials: "credentials",
        };

        this.indexes = tableIndexes;
    }

    // Database Initialization Methods
    async init() {
        // Initialize system, default, and directory schemas first
        await this._initializeBaseSchemas();

        // Then initialize customer schemas
        if (this.opts.customer_env_name) {
            await this._initializeCustomerSchema(this.opts.customer_env_name);
        }
    }

    async _initializeBaseSchemas() {
        const client = await this.app.pg.integrations.connect();
        try {
            await client.query("BEGIN");

            // 1. Create all schemas first
            await this._createSchemas(client);

            // 2. Create tables in system schema
            await this._createTableInSchema(client, this.schemas.system, [
                "control_numbers",
            ]);
            this.app.log.info("Created system tables");

            // 3. Create tables in default schema
            const defaultTables = [
                "ncpdp_claims",
                "medical_claims",
                "medical_claims_reports",
                "gr_medical_claims_reports",
                "pverify_request",
                "ss_message",
                "credentials",
            ];
            await this._createTableInSchema(
                client,
                this.schemas.default,
                defaultTables
            );
            this.app.log.info(
                `Created default tables: ${defaultTables.join(", ")}`
            );

            // 4. Create directory tables
            const directoryTables = ["organizations", "providers"];
            await this._createTableInSchema(
                client,
                this.schemas.directory,
                directoryTables
            );
            this.app.log.info(
                `Created directory tables: ${directoryTables.join(", ")}`
            );

            await client.query("COMMIT");
            this.app.log.info(
                "Initialized system, default, and directory schemas"
            );
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(
                `Error initializing base schemas: ${err.message}`
            );
            throw err;
        } finally {
            client.release();
        }
    }

    async _initializeCustomerSchema(customerSchema) {
        const client = await this.app.pg.integrations.connect();
        try {
            await client.query("BEGIN");

            // Create customer schema
            await this._createSchemas(client);

            // Create tables in customer schema
            const customerTables = [
                "ncpdp_claims",
                "medical_claims",
                "medical_claims_reports",
                "gr_medical_claims_reports",
                "pverify_request",
                "ss_message",
                "credentials",
            ];
            await this._createTableInSchema(
                client,
                customerSchema,
                customerTables
            );
            this.app.log.info(`Created customer tables in ${customerSchema}`);

            await client.query("COMMIT");
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(
                `Failed to initialize schema for environment ${customerSchema}: ${err.message}`
            );
            throw err;
        } finally {
            client.release();
        }
    }

    async _createSchemas(client) {
        for (const [name, schema] of Object.entries(this.schemas)) {
            if (schema) {
                try {
                    // Log the exact SQL being executed
                    const sql = `CREATE SCHEMA IF NOT EXISTS "${schema}"`;
                    this.app.log.info(`Executing SQL: ${sql}`);

                    await client.query(sql);

                    // Verify the schema was created
                    const { rows } = await client.query(
                        `
                        SELECT schema_name 
                        FROM information_schema.schemata 
                        WHERE schema_name = $1
                    `,
                        [schema]
                    );

                    if (rows.length === 0) {
                        throw new Error(`Failed to create schema: ${schema}`);
                    }

                    this.app.log.info(`Created/Verified schema: ${schema}`);
                } catch (err) {
                    this.app.log.error(
                        `Error creating schema ${schema}: ${err.message}`
                    );
                    this.app.log.error(`Error details:`, err);
                    throw err;
                }
            }
        }
    }

    async _createTableInSchema(client, schema, tableNames) {
        for (const tableName of tableNames) {
            try {
                const createTableSQL = tableSchemas[tableName].replace(
                    /:schema/g,
                    schema
                );
                await client.query(createTableSQL);
            } catch (err) {
                this.app.log.error(
                    `Error creating table ${schema}.${tableName}: ${err.message}`
                );
                throw err;
            }
        }
    }

    async _createIndexes() {
        const client = await this.app.pg.integrations.connect();
        try {
            for (const [tableName, columns] of Object.entries(this.indexes)) {
                const schema = ["organizations", "providers"].includes(
                    tableName
                )
                    ? this.schemas.directory
                    : tableName === "control_numbers"
                      ? this.schemas.system
                      : this.schemas.default;

                // Create indexes in primary schema
                for (const column of columns) {
                    const indexName = `idx_${tableName}_${column.toLowerCase()}`;
                    await client.query(`
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS ${indexName} 
                        ON ${schema}.${tableName} (${column})
                    `);
                }

                // Create indexes in customer schema if needed
                if (schema === this.schemas.default && this.schemas.customer) {
                    for (const column of columns) {
                        const indexName = `idx_${tableName}_${column.toLowerCase()}`;
                        await client.query(`
                            CREATE INDEX CONCURRENTLY IF NOT EXISTS ${indexName} 
                            ON ${this.schemas.customer}.${tableName} (${column})
                        `);
                    }
                }
            }
        } finally {
            client.release();
        }
    }

    // Control Number Methods
    async generate_med_controlnum() {
        const client = await this.app.pg.integrations.connect();
        try {
            let controlNum;
            let isUnique = false;

            while (!isUnique) {
                controlNum = Math.floor(
                    100000000 + Math.random() * 900000000
                ).toString();
                try {
                    await client.query("BEGIN");
                    await client.query(
                        `INSERT INTO ${this.tables.control_numbers} (control_number) VALUES ($1)`,
                        [controlNum]
                    );
                    await client.query("COMMIT");
                    isUnique = true;
                } catch (error) {
                    await client.query("ROLLBACK");
                    if (!error.code === "23505") {
                        // PostgreSQL unique violation code
                        throw error;
                    }
                }
            }
            return controlNum;
        } finally {
            client.release();
        }
    }

    // Claims Methods
    async insert_new_medical_claim(data) {
        if (!data) {
            throw new Error("No data passed for insert.");
        }

        const client = await this.app.pg.integrations.connect();
        const results = [];

        try {
            await client.query("BEGIN");

            // Insert into default schema
            const defaultResult = await this._insertClaimToSchema(
                client,
                this.schemas.default,
                data
            );
            results.push(defaultResult);

            // If customer schema exists, insert there too
            if (this.schemas.customer) {
                const customerResult = await this._insertClaimToSchema(
                    client,
                    this.schemas.customer,
                    data
                );
                results.push(customerResult);
            }

            await client.query("COMMIT");
            return results;
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(`Error inserting medical claim: ${err.message}`);
            throw err;
        } finally {
            client.release();
        }
    }

    async _insertClaimToSchema(client, schema, data) {
        const result = await client.query(
            `
            INSERT INTO ${schema}.${this.tables.medical_claims} (
                request_type,
                transaction_id,
                trace_id,
                control_number,
                claims_provider,
                clara_mrn,
                clara_claim_uuid,
                customer_id,
                customer_name,
                customer_env_name,
                request_json_data,
                response_json_data,
                validated_claim_object,
                claim_reference_json,
                claim_reference_number,
                status,
                edit_status,
                payer_info,
                errors,
                x12_data
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
            RETURNING id
        `,
            [
                data.request_type || "Claim",
                data.transaction_id,
                data.response_json_data?.meta?.traceId,
                data.control_number,
                data.claims_provider || "CHC",
                data.clara_mrn || data.request_json_data?._meta?.clara_mrn,
                data._meta?.clara_claim_uuid,
                data._meta?.customer_id || this.opts.customer_id,
                data._meta?.customer_name || this.opts.customer_name,
                data._meta?.customer_env_name || this.opts.customer_env_name,
                JSON.stringify(data.request_json_data),
                JSON.stringify(data.response_json_data),
                JSON.stringify(data.validated_claim_object),
                JSON.stringify(data.response_json_data?.claimReference),
                data.response_json_data?.claimReference?.correlationId,
                data.response_json_data?.status,
                data.response_json_data?.editStatus,
                JSON.stringify(data.response_json_data?.payer),
                data.errors,
                JSON.stringify(data.response_json_data?.x12),
            ]
        );

        this.app.log.info(
            `Inserted into schema: ${schema}, with result: ${JSON.stringify(result.rows[0])}`
        );
        return result.rows[0];
    }

    // NCPDP Claims Methods
    async insert_new_ncpdp_claim(data) {
        if (!data) {
            throw new Error("No data passed for insert.");
        }

        const client = await this.app.pg.integrations.connect();
        const results = [];

        try {
            // Initialize schemas if customer schema exists
            if (this.schemas.customer) {
                await this.init();
            }

            await client.query("BEGIN");

            // Log schema selection
            this.app.log.debug(
                `Using schemas - default: ${this.schemas.default}, customer: ${this.schemas.customer}`
            );

            // Insert into default schema
            const defaultResult = await this._insertNcpdpClaimToSchema(
                client,
                this.schemas.default,
                data
            );
            results.push(defaultResult);

            // If customer schema exists, insert there too
            if (this.schemas.customer) {
                const customerResult = await this._insertNcpdpClaimToSchema(
                    client,
                    this.schemas.customer,
                    data
                );
                results.push(customerResult);
            }

            await client.query("COMMIT");
            return results;
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(`Error inserting NCPDP claim: ${err.message}`);
            throw err;
        } finally {
            client.release();
        }
    }

    async _insertNcpdpClaimToSchema(client, schema, data) {
        const result = await client.query(
            `
            INSERT INTO ${schema}.${this.tables.ncpdp_claims} (
                request_type,
                transaction_id,
                transmission_id,
                switch_provider,
                customer_id,
                customer_name,
                customer_env_name,
                clara_mrn,
                svc_provider_id,
                svc_date,
                request_json,
                request_d0,
                response_json,
                response_d0,
                errors
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            RETURNING id
        `,
            [
                data.request_type,
                data.transaction_id,
                data.transmission_id,
                data.switch_provider,
                data.customer_id || this.opts.customer_id,
                data.customer_name || this.opts.customer_name,
                data.customer_env_name || this.opts.customer_env_name,
                data.clara_mrn || data.request_json_data?._meta?.clara_mrn,
                data.svc_provider_id,
                data.svc_date,
                JSON.stringify(data.request_json_data),
                data.request_d0_b64,
                JSON.stringify(data.response_json_data),
                data.response_d0_b64,
                data.errors,
            ]
        );

        this.app.log.info(
            `Inserted NCPDP claim into schema: ${schema}, with result: ${JSON.stringify(result.rows[0])}`
        );
        return result.rows[0];
    }

    // PVerify Methods
    async insert_new_pverify_request(data) {
        const client = await this.app.pg.integrations.connect();
        try {
            // Log the incoming data
            this.app.log.debug(
                "PG Insert Data:",
                JSON.stringify(data, null, 2)
            );

            // Insert into default schema
            try {
                await this._insertPVerifyToSchema(
                    client,
                    this.schemas.default,
                    data
                );
            } catch (err) {
                this.app.log.error(
                    "Default schema insert failed:",
                    err.message,
                    "\nDetails:",
                    err
                );
                throw err;
            }

            // Insert into customer schema if it exists
            if (this.schemas.customer) {
                try {
                    await this._insertPVerifyToSchema(
                        client,
                        this.schemas.customer,
                        data
                    );
                } catch (err) {
                    this.app.log.error(
                        "Customer schema insert failed:",
                        err.message,
                        "\nDetails:",
                        err
                    );
                    throw err;
                }
            }
        } catch (err) {
            this.app.log.error(
                "PG Insert Error:",
                err.message,
                "\nStack:",
                err.stack
            );
            throw err;
        } finally {
            client.release();
        }
    }

    async _insertPVerifyToSchema(client, schema, data) {
        const mrn = data.clara_mrn || data.request_json_data?._meta?.clara_mrn;
        try {
            const query = `
                INSERT INTO ${schema}.${this.tables.pverify_request} (
                    request_type,
                    customer_id,
                    customer_name,
                    customer_env_name,
                    clara_mrn,
                    member_id,
                    member_type,
                    request_json,
                    response_json,
                    pv_request_id,
                    pv_response_code,
                    s3_filehash,
                    errors
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                RETURNING id
            `;

            const values = [
                data.request_type,
                data.customer_id || this.opts.customer_id,
                data.customer_name || this.opts.customer_name,
                data.customer_env_name || this.opts.customer_env_name,
                mrn,
                data.member_id,
                data.member_type,
                data.request_json,
                data.response_json,
                data.pv_request_id,
                data.pv_response_code,
                data.s3_filehash,
                data.errors,
            ];

            // Log the query and values
            this.app.log.debug("PG Query:", query);
            this.app.log.debug("PG Values:", JSON.stringify(values, null, 2));

            const result = await client.query(query, values);

            this.app.log.info(
                `Inserted into schema: ${schema}, with result: ${JSON.stringify(result.rows[0])}`
            );
            return result.rows[0];
        } catch (err) {
            this.app.log.error(
                `Schema ${schema} insert error:`,
                err.message,
                "\nDetails:",
                err
            );
            throw err;
        }
    }

    async check_filehash(fhash) {
        const client = await this.app.pg.integrations.connect();
        try {
            // Use customer schema if available, otherwise default
            const schema = this.schemas.customer || this.schemas.default;

            const result = await client.query(
                `
                SELECT s3_filehash 
                FROM ${schema}.${this.tables.pverify_request} 
                WHERE s3_filehash LIKE $1
                `,
                [`%${fhash}%`]
            );

            this.app.log.info(
                `Checked filehash: ${fhash} with result: ${JSON.stringify(result.rows[0])}`
            );

            return result.rows[0]?.s3_filehash || null;
        } catch (err) {
            this.app.log.error(`Error checking filehash: ${err.message}`);
            throw err;
        } finally {
            client.release();
        }
    }

    // Surescripts Methods
    async insert_surescripts_message(data) {
        if (!data) {
            throw new Error("No data passed for insert.");
        }

        const client = await this.app.pg.integrations.connect();
        const results = [];

        try {
            await client.query("BEGIN");

            // Insert into default schema
            const defaultResult = await this._insertSurescriptsToSchema(
                client,
                this.schemas.default,
                data
            );
            results.push(defaultResult);

            // If customer schema exists, insert there too
            if (this.schemas.customer) {
                const customerResult = await this._insertSurescriptsToSchema(
                    client,
                    this.schemas.customer,
                    data
                );
                results.push(customerResult);
            }

            await client.query("COMMIT");
            return results;
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(
                `Error inserting surescripts message: ${err.message}`
            );
            throw err;
        } finally {
            client.release();
        }
    }

    async _insertSurescriptsToSchema(client, schema, data) {
        const result = await client.query(
            `
            INSERT INTO ${schema}.${this.tables.ss_message} (
                tx_direction,
                message_id,
                customer_id,
                customer_name,
                customer_env_name,
                related_message_id,
                source_ncpdp_id,
                dest_ncpdp_id,
                message_type,
                message_header,
                message_body,
                raw_json,
                raw_xml,
                message_hash,
                errors,
                processed,
                delivered
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            ON CONFLICT (message_id, tx_direction) DO UPDATE SET
                customer_id = EXCLUDED.customer_id,
                customer_name = EXCLUDED.customer_name,
                customer_env_name = EXCLUDED.customer_env_name,
                processed = EXCLUDED.processed,
                delivered = EXCLUDED.delivered
            RETURNING id
        `,
            [
                data.tx_direction,
                data.message_id,
                data.customer_id || this.opts.customer_id,
                data.customer_name || this.opts.customer_name,
                data.customer_env_name || this.opts.customer_env_name,
                data.related_message_id,
                data.source_ncpdp_id,
                data.dest_ncpdp_id,
                data.message_type,
                JSON.stringify(data.message_header),
                JSON.stringify(data.message_body),
                JSON.stringify(data.raw_json),
                data.raw_xml,
                JSON.stringify(data.message_hash),
                data.errors,
                data.processed || 0,
                data.delivered || 0,
            ]
        );

        this.app.log.info(
            `Inserted/Updated surescripts message in schema: ${schema}, with result: ${JSON.stringify(result.rows[0])}`
        );
        return result.rows[0];
    }

    // Directory Methods
    async insert_or_update_organizations(dataArray) {
        if (!Array.isArray(dataArray) || dataArray.length === 0) {
            throw new Error("No data array passed for insert.");
        }

        const client = await this.app.pg.integrations.connect();
        const results = [];
        const BATCH_SIZE = 500;

        try {
            await client.query("BEGIN");

            // Process in batches
            for (let i = 0; i < dataArray.length; i += BATCH_SIZE) {
                const batch = dataArray.slice(i, i + BATCH_SIZE);
                const batchResults = await Promise.all(
                    batch.map((org) => this._upsertOrganization(client, org))
                );
                results.push(...batchResults);
            }

            await client.query("COMMIT");
            return results;
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(`Error inserting organizations: ${err.message}`);
            throw err;
        } finally {
            client.release();
        }
    }

    async _upsertOrganization(client, org) {
        const result = await client.query(
            `
            INSERT INTO ${this.schemas.directory}.${this.tables.organizations} (
                OrganizationID,
                OrganizationName,
                NCPDPID,
                NPI,
                DEA,
                AddressLine1,
                AddressLine2,
                City,
                State,
                ZIP,
                PhoneNumber,
                FaxNumber,
                LastModifiedDate
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            ON CONFLICT (OrganizationID, NPI, AddressLine1, LastModifiedDate) 
            DO UPDATE SET
                OrganizationName = EXCLUDED.OrganizationName,
                NCPDPID = EXCLUDED.NCPDPID,
                DEA = EXCLUDED.DEA,
                AddressLine2 = EXCLUDED.AddressLine2,
                City = EXCLUDED.City,
                State = EXCLUDED.State,
                ZIP = EXCLUDED.ZIP,
                PhoneNumber = EXCLUDED.PhoneNumber,
                FaxNumber = EXCLUDED.FaxNumber,
                updatedAt = CURRENT_TIMESTAMP
            RETURNING id
        `,
            [
                org.OrganizationID,
                org.OrganizationName,
                org.NCPDPID,
                org.NPI,
                org.DEA,
                org.AddressLine1,
                org.AddressLine2,
                org.City,
                org.State,
                org.ZIP,
                org.PhoneNumber,
                org.FaxNumber,
                org.LastModifiedDate,
            ]
        );

        return result.rows[0];
    }

    async insert_or_update_providers(dataArray) {
        if (!Array.isArray(dataArray) || dataArray.length === 0) {
            throw new Error("No data array passed for insert.");
        }

        const client = await this.app.pg.integrations.connect();
        const results = [];
        const BATCH_SIZE = 500;

        try {
            await client.query("BEGIN");

            // Process in batches
            for (let i = 0; i < dataArray.length; i += BATCH_SIZE) {
                const batch = dataArray.slice(i, i + BATCH_SIZE);
                const batchResults = await Promise.all(
                    batch.map((provider) =>
                        this._upsertProvider(client, provider)
                    )
                );
                results.push(...batchResults);
            }

            await client.query("COMMIT");
            return results;
        } catch (err) {
            await client.query("ROLLBACK");
            this.app.log.error(`Error inserting providers: ${err.message}`);
            throw err;
        } finally {
            client.release();
        }
    }

    async _upsertProvider(client, provider) {
        const result = await client.query(
            `
            INSERT INTO ${this.schemas.directory}.${this.tables.providers} (
                SPI,
                OrganizationID,
                NPI,
                DEA,
                FirstName,
                LastName,
                MiddleName,
                Prefix,
                Suffix,
                Specialty,
                AddressLine1,
                AddressLine2,
                City,
                State,
                ZIP,
                PhoneNumber,
                FaxNumber,
                PrimarySpecialty,
                SecondarySpecialty,
                LastModifiedDate
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21)
            ON CONFLICT (SPI, NPI, OrganizationID, LastModifiedDate) 
            DO UPDATE SET
                DEA = EXCLUDED.DEA,
                FirstName = EXCLUDED.FirstName,
                LastName = EXCLUDED.LastName,
                MiddleName = EXCLUDED.MiddleName,
                Prefix = EXCLUDED.Prefix,
                Suffix = EXCLUDED.Suffix,
                Specialty = EXCLUDED.Specialty,
                AddressLine1 = EXCLUDED.AddressLine1,
                AddressLine2 = EXCLUDED.AddressLine2,
                City = EXCLUDED.City,
                State = EXCLUDED.State,
                ZIP = EXCLUDED.ZIP,
                PhoneNumber = EXCLUDED.PhoneNumber,
                FaxNumber = EXCLUDED.FaxNumber,
                PrimarySpecialty = EXCLUDED.PrimarySpecialty,
                SecondarySpecialty = EXCLUDED.SecondarySpecialty,
                updatedAt = CURRENT_TIMESTAMP
            RETURNING id
        `,
            [
                provider.SPI,
                provider.OrganizationID,
                provider.NPI,
                provider.DEA,
                provider.FirstName,
                provider.LastName,
                provider.MiddleName,
                provider.Prefix,
                provider.Suffix,
                provider.Specialty,
                provider.AddressLine1,
                provider.AddressLine2,
                provider.City,
                provider.State,
                provider.ZIP,
                provider.PhoneNumber,
                provider.FaxNumber,
                provider.PrimarySpecialty,
                provider.SecondarySpecialty,
                provider.LastModifiedDate,
            ]
        );

        return result.rows[0];
    }

    async select_filtered_directory(table, params) {
        const client = await this.app.pg.integrations.connect();
        try {
            const conditions = [];
            const values = [];
            let paramCount = 1;

            for (const [key, value] of Object.entries(params)) {
                conditions.push(`${key} ILIKE $${paramCount}`);
                values.push(`%${value}%`);
                paramCount++;
            }

            const whereClause =
                conditions.length > 0
                    ? `WHERE ${conditions.join(" AND ")}`
                    : "";

            const result = await client.query(
                `
                SELECT * 
                FROM ${this.schemas.directory}.${table} 
                ${whereClause}
                ORDER BY LastModifiedDate DESC
                LIMIT 1000
            `,
                values
            );

            return result.rows;
        } finally {
            client.release();
        }
    }

    static async initializeAllSchemas(app) {
        try {
            const db = new PgIntegrationsDB(app);
            await db.pool.query(`
                CREATE TABLE IF NOT EXISTS medical_claims_reports (
                    id SERIAL PRIMARY KEY,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    raw_json JSONB,
                    raw_x12 TEXT,
                    converted_report_url TEXT,
                    report_type TEXT,
                    raw_report_url TEXT,
                    s3_filepath TEXT,
                    s3_filehash TEXT,
                    control_number TEXT,
                    payer_name TEXT,
                    payer_info JSONB,
                    type TEXT,
                    customer_id INTEGER,
                    customer_name TEXT,
                    customer_env_name TEXT,
                    submitter_id TEXT
                );
            `);
            app.log.info("PostgreSQL schemas initialized");
        } catch (error) {
            app.log.error(
                `Failed to initialize PostgreSQL schemas: ${error.message}`
            );
            throw error;
        }
    }

    // Add a method to check if schemas exist
    async ensureSchema() {
        const client = await this.app.pg.integrations.connect();
        try {
            // Check if schemas exist
            const schemas = [this.schemas.system, this.schemas.default];
            if (this.schemas.customer) {
                schemas.push(this.schemas.customer);
            }

            for (const schema of schemas) {
                await client.query(`CREATE SCHEMA IF NOT EXISTS "${schema}"`);

                // Create the table with proper constraints
                await client.query(`
                    CREATE TABLE IF NOT EXISTS "${schema}".medical_claims_reports (
                        id SERIAL PRIMARY KEY,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        raw_json JSONB,
                        raw_x12 TEXT,
                        converted_report_url TEXT,
                        report_type TEXT,
                        raw_report_url TEXT,
                        s3_filepath TEXT,
                        s3_filehash TEXT,
                        control_number TEXT,
                        payer_name TEXT,
                        payer_info JSONB,
                        type TEXT,
                        customer_id INTEGER,
                        customer_name TEXT,
                        customer_env_name TEXT,
                        submitter_id TEXT,
                        UNIQUE(control_number, report_type, customer_name)
                    );
                `);
            }
        } finally {
            client.release();
        }
    }

    // Add methods for managing credentials
    async upsert_credentials(schema, data) {
        const client = await this.app.pg.integrations.connect();
        try {
            const result = await client.query(
                `
                INSERT INTO ${schema}.${this.tables.credentials}
                (name, type, username, password, api_key, environment)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (name, type) 
                DO UPDATE SET
                    username = EXCLUDED.username,
                    password = EXCLUDED.password,
                    api_key = EXCLUDED.api_key,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING *
                `,
                [
                    data.name,
                    data.type,
                    data.username,
                    data.password,
                    data.api_key,
                    data.environment,
                ]
            );
            return result.rows[0];
        } finally {
            client.release();
        }
    }

    async get_credentials(type) {
        const client = await this.app.pg.integrations.connect();
        try {
            // Only check customer schema
            if (!this.schemas.customer) {
                throw new Error(
                    `No customer schema defined for credentials type: ${type}`
                );
            }

            const result = await client.query(
                `
                SELECT * FROM "${this.schemas.customer}".${this.tables.credentials}
                WHERE type = $1
                `,
                [type]
            );

            if (!result.rows[0]) {
                this.app.log.error(
                    `No credentials found for type ${type} in customer schema ${this.schemas.customer}`
                );
                throw new Error(
                    `No credentials found for type ${type} in customer schema`
                );
            }

            this.app.log.info(
                `Found credentials for type ${type} in customer schema ${this.schemas.customer}`
            );
            return result.rows[0];
        } finally {
            client.release();
        }
    }

    async delete_credentials(schema, name, type) {
        const client = await this.app.pg.integrations.connect();
        try {
            const result = await client.query(
                `
                DELETE FROM ${schema}.${this.tables.credentials}
                WHERE name = $1 AND type = $2
                RETURNING *
                `,
                [name, type]
            );
            return result.rows[0];
        } finally {
            client.release();
        }
    }

    async insert_medical_claim_report(data, control_numbers) {
        try {
            // Ensure proper JSON handling with safeJSONStringify
            const jsonData = {
                raw_json: data.raw_json
                    ? safeJSONStringify(data.raw_json)
                    : null,
                payer_info: data.payer_info
                    ? safeJSONStringify(data.payer_info)
                    : null,
            };

            const result = await this.pool.query(
                `
                INSERT INTO "${this.schemas.customer}".medical_claims_reports (
                    raw_json,
                    raw_x12,
                    converted_report_url,
                    report_type,
                    raw_report_url,
                    control_number,
                    payer_name,
                    payer_info,
                    type,
                    customer_id,
                    customer_name,
                    customer_env_name,
                    submitter_id
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (control_number, report_type, customer_name) 
                DO UPDATE SET
                    raw_json = EXCLUDED.raw_json,
                    raw_x12 = EXCLUDED.raw_x12,
                    converted_report_url = EXCLUDED.converted_report_url,
                    raw_report_url = EXCLUDED.raw_report_url,
                    payer_name = EXCLUDED.payer_name,
                    payer_info = EXCLUDED.payer_info,
                    type = EXCLUDED.type,
                    customer_id = EXCLUDED.customer_id,
                    customer_env_name = EXCLUDED.customer_env_name,
                    submitter_id = EXCLUDED.submitter_id,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING *
                `,
                [
                    jsonData.raw_json,
                    data.raw_x12,
                    data.converted_report_url,
                    data.report_type,
                    data.raw_report_url,
                    data.control_number,
                    data.payer_name,
                    jsonData.payer_info,
                    data.type,
                    data.customer_id,
                    data.customer_name,
                    data.customer_env_name,
                    data.submitter_id,
                ]
            );

            this.app.log.info(
                `Inserted/Updated record in schema ${this.schemas.customer}: ${safeJSONStringify(result.rows[0])}`
            );
            return result.rows;
        } catch (error) {
            this.app.log.error("PostgreSQL Error:");
            this.app.log.error(
                `Failed to insert into customer schema: ${safeJSONStringify(error)}`
            );
            throw error;
        }
    }

    async update_medical_claim_report_delivered(data) {
        const { control_number, report_type } = data;
        if (!control_number || !report_type) {
            throw new Error("Control number and report type are required");
        }
        let where_clause = "";
        let queryParams = [];

        try {
            if (
                data.stopFetching &&
                data.stopFetching_control_numbers.length > 0
            ) {
                // Create numbered parameters for each control number
                where_clause = `control_number IN (${data.stopFetching_control_numbers
                    .map((_, index) => `$${index + 1}`)
                    .join(",")})`;
                queryParams = data.stopFetching_control_numbers;
            } else {
                where_clause = `control_number = $1 AND report_type = $2`;
                queryParams = [data.control_number, data.report_type];
            }

            const result = await this.pool.query(
                `
                UPDATE "${this.schemas.customer}".medical_claims_reports
                SET delivered = TRUE, updated_at = CURRENT_TIMESTAMP,
                clara_delivered_dt = CURRENT_TIMESTAMP
                WHERE ${where_clause}
                RETURNING *
                `,
                queryParams
            );
            if (result.rowCount === 0) {
                this.app.log.warn(
                    `No record found for update with control_number: ${data.control_number}, report_type: ${data.report_type}, customer_name: ${data.customer_name}`
                );
                return false;
            }

            this.app.log.info(
                `Updated PostgreSQL record: ${safeJSONStringify(result.rows[0])}`
            );
            return result.rows[0];
        } catch (error) {
            this.app.log.error(
                `Error updating claim file in PostgreSQL: ${safeJSONStringify(error)}`
            );
            throw error;
        }
    }
    async get_medical_claim_report_delivered(control_number, report_type) {
        const result = await this.pool.query(
            `SELECT clara_delivered, clara_delivered_dt FROM "${this.schemas.customer}".medical_claims_reports WHERE control_number = $1 AND report_type = $2`,
            [control_number, report_type]
        );
        const row = result.rows[0];
        const delivered = row?.clara_delivered;
        const delivered_dt = row?.clara_delivered_dt;
        return { delivered, delivered_dt };
    }
    async update_medical_claim_report(data) {
        try {
            const result = await this.pool.query(
                `
                UPDATE "${this.schemas.customer}".medical_claims_reports
                SET 
                    s3_filepath = $1,
                    s3_filehash = $2,
                    updated_at = CURRENT_TIMESTAMP
                WHERE 
                    control_number = $3 
                    AND report_type = $4 
                    AND customer_name = $5
                RETURNING *
                `,
                [
                    data.s3_filepath,
                    data.s3_filehash,
                    data.control_number,
                    data.report_type,
                    data.customer_name,
                ]
            );

            if (result.rowCount === 0) {
                this.app.log.warn(
                    `No record found for update with control_number: ${data.control_number}, report_type: ${data.report_type}, customer_name: ${data.customer_name}`
                );
                return false;
            }

            this.app.log.info(
                `Updated PostgreSQL record: ${safeJSONStringify(result.rows[0])}`
            );
            return result.rows[0];
        } catch (error) {
            this.app.log.error(
                `Error updating claim file in PostgreSQL: ${safeJSONStringify(error)}`
            );
            throw error;
        }
    }

    async fetch_medical_claim_reports(filters) {
        try {
            const {
                report_type = "all",
                start_date,
                end_date,
                control_number,
            } = filters;

            // Build the query conditions and params dynamically
            const conditions = ["customer_id = $1"];
            const params = [this.opts.customer_id];
            let paramCount = 2;

            if (start_date) {
                conditions.push(`created_at >= $${paramCount}`);
                params.push(start_date);
                paramCount++;
            }

            if (end_date) {
                conditions.push(`created_at <= $${paramCount}`);
                params.push(end_date);
                paramCount++;
            }

            if (control_number) {
                conditions.push(`control_number = $${paramCount}`);
                params.push(control_number);
                paramCount++;
            }

            if (report_type !== "all") {
                conditions.push(`type = $${paramCount}`);
                params.push(report_type);
            }

            const query = `
                SELECT 
                    id,
                    type as report_type,
                    raw_json,
                    raw_x12,
                    converted_report_url,
                    raw_report_url,
                    s3_filepath,
                    control_number,
                    submitter_id,
                    customer_id,
                    customer_name,
                    payer_name,
                    payer_info,
                    created_at,
                    updated_at
                FROM ${this.schemas.customer}.medical_claims_reports
                WHERE ${conditions.join(" AND ")}
                ORDER BY created_at DESC
            `;

            this.app.log.info(
                `Fetching reports with query: ${query} and params: ${params}`
            );
            const results = await this.pool.query(query, params);

            // Transform results
            const transformedResults = results.rows.map((row) => {
                // Extract patient information based on report type
                let patientId = null;
                let patientFirstName = null;
                let patientLastName = null;
                let dependentFirstName = null;
                let dependentLastName = null;
                let dependentId = null;

                if (row.report_type === "277") {
                    // For 277 reports, check the patientClaimStatusDetails path
                    const patientInfo =
                        row.raw_json?.transactions?.[0]?.payers?.[0]
                            ?.claimStatusTransactions?.[0]
                            ?.claimStatusDetails?.[0]
                            ?.patientClaimStatusDetails?.[0];

                    patientId =
                        patientInfo?.subscriber?.memberId ||
                        row.raw_json?.transactions?.[0]?.patientMemberId;
                    patientFirstName = patientInfo?.subscriber?.firstName;
                    patientLastName = patientInfo?.subscriber?.lastName;
                    dependentFirstName = patientInfo?.dependent?.firstName;
                    dependentLastName = patientInfo?.dependent?.lastName;
                    dependentId = patientInfo?.dependent?.memberId;
                } else if (row.report_type === "835") {
                    // For 835 reports, check the NM1 segments
                    const claimInfo = row.raw_json?.transactions?.[0];
                    const patientSegment =
                        claimInfo?.claims?.[0]?.patientInfo ||
                        claimInfo?.beneficiary;

                    patientId =
                        patientSegment?.memberId ||
                        row.raw_json?.transactions?.[0]?.patientMemberId;
                    patientFirstName = patientSegment?.firstName;
                    patientLastName = patientSegment?.lastName;
                }

                // Create initial payload with all fields
                return {
                    report_id: row.id,
                    report_type: row.report_type,
                    control_number: row.control_number,
                    submitter_id: row.submitter_id,
                    customer_id: row.customer_id,
                    customer_name: row.customer_name,
                    payer_name: row.payer_name,
                    payer_info: row.payer_info,
                    created_at: row.created_at,
                    patient_id: patientId,
                    patient_first_name: patientFirstName,
                    patient_last_name: patientLastName,
                    dependent_id: dependentId,
                    dependent_first_name: dependentFirstName,
                    dependent_last_name: dependentLastName,
                    raw_json: row.raw_json,
                    raw_x12: row.raw_x12,
                    converted_report_url: row.converted_report_url,
                    raw_report_url: row.raw_report_url,
                    s3_filehash: row.s3_filepath
                        ? Buffer.from(row.s3_filepath).toString("base64")
                        : null,
                };
            });

            // Group results by report type
            const groupedResults = transformedResults.reduce((acc, report) => {
                const type = report.report_type;
                if (!acc[type]) {
                    acc[type] = [];
                }
                acc[type].push(report);
                return acc;
            }, {});

            return {
                total_reports: transformedResults.length,
                reports: groupedResults,
            };
        } catch (error) {
            this.app.log.error("Error fetching medical claim reports:", error);
            throw error;
        }
    }
}
// Helper function
export async function generate_control_number(app) {
    const db = new PgIntegrationsDB(app, {});
    return await db.generate_med_controlnum();
}
