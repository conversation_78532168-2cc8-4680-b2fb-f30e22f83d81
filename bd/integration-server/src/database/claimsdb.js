import Database from "better-sqlite3";
import path from "path";
import { create_indexes } from "./dbtools.js";
import fs from "fs";
import { formatToClaraDT } from "../utils/tools.js";

export class ControlNumberDB {
    /**
     * Constructs a ControlNumberDB object.
     * @param {Object} app - The application object.
     */
    constructor(app) {
        this.app = app;
        this.db_name = path.join(this.app.db_root, "control_numbers.db");
        this.db = null;
        this.init_control_number_db();
    }

    /**
     * Initializes the control number database.
     * @returns {Object} - The control number database object.
     * @throws {Error} - If failed to initialize the database.
     */
    init_control_number_db() {
        if (this.db) {
            return this.db;
        }

        try {
            this.db = new Database(this.db_name);
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS control_numbers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    control_number TEXT UNIQUE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);
            create_indexes(this.app, this.db, "control_numbers", [
                "control_number",
            ]);
            return this.db;
        } catch (error) {
            this.app.log.error(
                `Failed to initialize control number database: ${error.message}`
            );
            throw error;
        }
    }

    /**
     * Generates a unique control number.
     * @returns {string} - The generated control number.
     * @throws {Error} - If failed to generate the control number.
     */
    generate_med_controlnum() {
        let controlNum;
        let isUnique = false;

        if (!this.db) {
            this.db = this.init_control_number_db();
        }

        while (!isUnique) {
            controlNum = Math.floor(
                100000000 + Math.random() * 900000000
            ).toString();

            try {
                const stmt = this.db.prepare(
                    "INSERT INTO control_numbers (control_number) VALUES (?)"
                );
                stmt.run(controlNum);
                isUnique = true;
            } catch (error) {
                if (error.code !== "SQLITE_CONSTRAINT_UNIQUE") {
                    this.app.log.error(
                        `Error generating control number: ${error.message}`
                    );
                    throw error;
                }
                this.app.log.error(`Control number already exists: ${error}`);
                // retry if already exists
            }
        }
        this.app.log.debug(`Generated control number: ${controlNum}`);
        return controlNum;
    }
}

export class ClaimsDB {
    constructor(app, opts) {
        this.app = app;
        this.opts = opts;
        this.dbdir = app.db_root;
        this.db_name = path.join(this.dbdir, "default.db");
        this.app.log.debug(
            `ClaimsDB initialized with db_name: ${this.db_name}`
        );
        this.db_clients = this.init();
    }

    init() {
        const dbc = [];
        const dbp = path.join(this.dbdir, "default.db");
        this.app.log.info(`Initializing database at path: ${dbp}`);

        // Ensure the directory exists
        fs.mkdirSync(path.dirname(dbp), { recursive: true });

        try {
            const db = new Database(dbp, {});
            this.create_tables(db);

            for (const tableName in this.indexes) {
                create_indexes(
                    this.app,
                    db,
                    tableName,
                    this.indexes[tableName]
                );
            }

            dbc.push(db);
        } catch (err) {
            this.app.log.error(`Error initializing db: ${err.message}`);
            throw new Error(
                `Failed to initialize database: ${err.message}\n${err.stack}`
            );
        }
        return dbc;
    }

    create_tables(db) {
        db.exec(`
            CREATE TABLE if not exists ncpdp_claims(
                [id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                [request_type] TEXT,
                [transaction_id] TEXT,
                [transmission_id] TEXT,
                [switch_provider] TEXT,
                [customer_id] INTEGER,
                [customer_name] TEXT,
                [clara_mrn] TEXT,
                [svc_provider_id] TEXT,
                [svc_date] TEXT,
                [request_json] BLOB,
                [request_d0] BLOB,
                [response_json] BLOB,
                [response_d0] BLOB,
                [errors] TEXT,
                [latest_request_dt] DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
        db.exec(`
            CREATE TABLE if not exists medical_claims(
                [id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                [request_type] TEXT,
                [transaction_id] TEXT,
                [trace_id] TEXT,
                [control_number] TEXT,
                [claims_provider] TEXT,
                [clara_mrn] TEXT,
                [clara_claim_uuid] TEXT,
                [customer_id] INTEGER,
                [customer_name] TEXT,
                [request_json_data] BLOB,
                [response_json_data] BLOB,
                [validated_claim_object] BLOB,
                [claim_reference_json] BLOB,
                [claim_reference_number] TEXT,
                [status] TEXT,
                [edit_status] TEXT,
                [payer_info] BLOB,
                [errors] TEXT,
                [x12_data] BLOB,
                [has_reports] BOOLEAN DEFAULT FALSE,
                [clara_delivered_dt] DATETIME,
                [clara_delivered] BOOLEAN DEFAULT FALSE,
                [latest_request_dt] DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        db.exec(`
            CREATE TABLE if not exists medical_claims_reports(
                [id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                [raw_json] BLOB,
                [raw_x12] BLOB,
                [converted_report_url] TEXT,
                [report_type] TEXT,
                [raw_report_url] TEXT,
                [s3_filepath] TEXT,
                [s3_filehash] TEXT,
                [control_number] TEXT,
                [payer_name] TEXT,
                [payer_info] BLOB,
                UNIQUE(control_number, report_type, raw_report_url)
            )
        `);

        db.exec(`
            CREATE TABLE if not exists gr_medical_claims_reports(
                [medical_claim_id] INTEGER,
                [medical_claim_report_id] INTEGER,
                PRIMARY KEY (medical_claim_id, medical_claim_report_id),
                FOREIGN KEY (medical_claim_id) REFERENCES medical_claims(id),
                FOREIGN KEY (medical_claim_report_id) REFERENCES medical_claims_reports(id)
            )
        `);
    }
    insert_new_ncpdp_claim(data) {
        if (!data) {
            throw new Error("No data passed for insert.");
        }
        for (let db of this.db_clients) {
            let mrn = data.clara_mrn ? data.clara_mrn : null;
            db = db.open ? db : new Database(db.name);
            const dbn = db.name?.split("/").pop();
            if (!mrn) {
                try {
                    mrn = data.request_json_data?.patient?.patient_id
                        ? data.request_json_data.patient.patient_id
                        : data.request_json_data?._meta?.clara_mrn
                          ? data.request_json_data._meta.clara_mrn
                          : null;
                } catch (err) {
                    this.app.log.error(
                        "Error parsing data trying to get clara_mrn: " + err
                    );
                }
            }
            let result;
            try {
                result = db
                    .prepare(
                        `
                    INSERT INTO ncpdp_claims (
                        request_type, 
                        transaction_id,
                        transmission_id,
                        switch_provider,
                        customer_id, 
                        customer_name, 
                        clara_mrn, 
                        svc_provider_id,
                        svc_date,
                        request_json,
                        request_d0,
                        response_json,
                        response_d0,
                        errors
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `
                    )
                    .run(
                        data.request_type,
                        data.transaction_id,
                        data.transmission_id,
                        data.switch_provider,
                        data.customer_id ? data.customer_id : this.customer_id,
                        data.customer_name
                            ? data.customer_name
                            : this.customer_name,
                        mrn,
                        data.svc_provider_id,
                        data.svc_date,
                        JSON.stringify(data.request_json_data),
                        data.request_d0_b64,
                        JSON.stringify(data.response_json_data),
                        data.response_d0_b64,
                        data.errors
                    );
                this.app.log.info(
                    `Inserted into db: ${dbn}, with result:  ${JSON.stringify(result)}`
                );
            } catch (err) {
                this.app.log.error(
                    "error inserting into db: " +
                        dbn +
                        ": " +
                        JSON.stringify(err)
                );
                throw new Error(err);
            } finally {
                db.close();
            }
            if (result?.changes && dbn != "default.db") {
                return result;
            }
        }
    }

    insert_new_medical_claim(data) {
        if (!data) {
            throw new Error("No data passed for insert.");
        }
        for (let db of this.db_clients) {
            let mrn = data.clara_mrn ? data.clara_mrn : null;
            db = db.open ? db : new Database(db.name);
            const dbn = db.name?.split("/").pop();
            if (!mrn) {
                try {
                    mrn = data.request_json_data?.patient?.patient_id
                        ? data.request_json_data.patient.patient_id
                        : data.request_json_data?._meta?.clara_mrn
                          ? data.request_json_data._meta.clara_mrn
                          : data._meta?.clara_mrn
                            ? data._meta.clara_mrn
                            : null;
                } catch (err) {
                    this.app.log.error(
                        "Error parsing data trying to get clara_mrn: " + err
                    );
                }
            }
            const control_number = data._meta?.controlNumber
                ? data._meta.controlNumber
                : data.response_json_data?.controlNumber
                  ? data.response_json_data.controlNumber
                  : data.request_json_data?.controlNumber
                    ? data.request_json_data.controlNumber
                    : null;
            let result;
            try {
                result = db
                    .prepare(
                        `
                    insert into medical_claims (
                        request_type,
                        transaction_id,
                        trace_id,
                        control_number,
                        claims_provider,
                        clara_mrn,
                        clara_claim_uuid,
                        customer_id,
                        customer_name,
                        request_json_data,
                        response_json_data,
                        validated_claim_object,
                        claim_reference_json,
                        claim_reference_number,
                        status,
                        edit_status,
                        payer_info,
                        errors,
                        x12_data
                    ) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `
                    )
                    .run(
                        data.request_type || "Claim",
                        data.transaction_id,
                        data.response_json_data?.meta?.traceId,
                        control_number,
                        data.claims_provider || "CHC",
                        mrn,
                        data._meta?.clara_claim_uuid,
                        data._meta?.customer_id
                            ? data._meta.customer_id
                            : this.customer_id,
                        data._meta?.customer_name
                            ? data._meta.customer_name
                            : this.customer_name,
                        JSON.stringify(data.request_json_data),
                        JSON.stringify(data.response_json_data),
                        JSON.stringify(data.validated_claim_object),
                        JSON.stringify(data.response_json_data?.claimReference),
                        data.response_json_data?.claimReference?.correlationId,
                        data.response_json_data?.status,
                        data.response_json_data?.editStatus,
                        JSON.stringify(data.response_json_data?.payer),
                        data.errors,
                        JSON.stringify(data.response_json_data?.x12_data)
                    );
                this.app.log.info(
                    `Inserted into db: ${dbn}, with result:  ${JSON.stringify(result)}`
                );
            } catch (err) {
                this.app.log.error(
                    "error inserting into db: " +
                        dbn +
                        ": " +
                        JSON.stringify(err)
                );
                if (
                    !db.inTransaction &&
                    !err.message.includes("UNIQUE constraint failed")
                )
                    throw new Error(err);
            } finally {
                db.close();
            }
            if (result?.changes && dbn != "default.db") {
                return result;
            }
        }
    }
    get_claim_by_control_number(control_number) {
        if (!control_number) {
            throw new Error("No control number specified.");
        }

        const db = this.db_clients[0];
        if (!db) {
            throw new Error("No database connection.");
        }

        // Ensure control_number is a string
        const result = db
            .prepare(
                `
            SELECT * FROM medical_claims WHERE control_number = ?
        `
            )
            .get(control_number);
        return result;
    }

    insert_medical_claim_report(reportData, associatedControlNumbers) {
        if (!reportData) {
            throw new Error("No report data specified.");
        }

        const defaultDb = new Database(
            path.join(this.customer_db_root, "default.db")
        );
        let customerName = null;
        let customerId = null;
        const associatedClaimIds = [];
        const reportDataList = [];

        for (const controlNumber of associatedControlNumbers) {
            this.app.log.info(`Control number: ${controlNumber}`);
            const claim = this.get_claim_by_control_number(controlNumber);
            this.app.log.info(`Claim: ${JSON.stringify(claim)}`);
            if (claim) {
                associatedClaimIds.push(claim.id);
                if (claim.customer_name && !customerName) {
                    customerName = claim.customer_name;
                    customerId = claim.customer_id;
                }
            }
        }

        defaultDb.close();
        const dbsToUpdate = [path.join(this.customer_db_root, "default.db")];
        if (customerName) {
            const customerDbPath = path.join(
                this.customer_db_root,
                `${customerName}.db`
            );
            this.app.log.debug(`Adding customer database: ${customerDbPath}`);
            dbsToUpdate.push(customerDbPath);
        }

        this.app.log.debug(
            `Databases to update: ${JSON.stringify(dbsToUpdate)}`
        );

        for (const dbPath of dbsToUpdate) {
            this.app.log.debug(`Processing database: ${dbPath}`);
            this.app.log.debug(`Database path type: ${typeof dbPath}`);

            if (typeof dbPath !== "string") {
                this.app.log.error(
                    `Invalid database path: ${JSON.stringify(dbPath)}. Expected a string.`
                );
                continue;
            }

            let db;
            try {
                db = new Database(dbPath);
                this.app.log.debug(`Successfully opened database: ${dbPath}`);
            } catch (err) {
                this.app.log.error(
                    `Failed to open database at ${dbPath}: ${err.message}`
                );
                continue;
            }

            try {
                reportData.control_number =
                    reportData.control_number || associatedControlNumbers[0];

                this.create_tables(db);

                for (const tableName in this.indexes) {
                    if (this.tableExists(db, tableName)) {
                        create_indexes(
                            this.app,
                            db,
                            tableName,
                            this.indexes[tableName]
                        );
                    } else {
                        this.app.log.warn(
                            `Table ${tableName} does not exist in ${dbPath}. Skipping index creation.`
                        );
                    }
                }

                this.insert_medical_claim_report_into_db(
                    db,
                    reportData,
                    associatedClaimIds
                );
                this.app.log.info(
                    `Inserted medical claim report into db: ${dbPath} for control number: ${reportData.control_number}`
                );

                reportDataList.push({
                    ...reportData,
                    control_number: reportData.control_number,
                    customer_id: customerId,
                    customer_name: customerName,
                });
            } catch (err) {
                this.app.log.error(
                    `Error inserting medical claim report into db: ${dbPath} for control number: ${reportData.control_number}: ${err}`
                );
                this.app.log.error(err.stack);
            } finally {
                if (db) db.close();
            }
        }

        return reportDataList;
    }

    insert_medical_claim_report_into_db(db, reportData, associatedClaimIds) {
        const insertReport = db.prepare(`
            INSERT INTO medical_claims_reports (raw_json, raw_x12, converted_report_url, report_type, raw_report_url, control_number, payer_name, payer_info)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const insertRelation = db.prepare(`
            INSERT INTO gr_medical_claims_reports (medical_claim_id, medical_claim_report_id)
            VALUES (?, ?)
        `);

        let insertedReportId;

        try {
            db.transaction(() => {
                const reportResult = insertReport.run(
                    JSON.stringify(reportData.raw_json),
                    JSON.stringify(reportData.raw_x12),
                    reportData.converted_report_url,
                    reportData.report_type,
                    reportData.raw_report_url,
                    reportData.control_number,
                    reportData.payer_name,
                    JSON.stringify(reportData.payer_info)
                );
                insertedReportId = reportResult.lastInsertRowid;

                for (const claimId of associatedClaimIds) {
                    insertRelation.run(claimId, insertedReportId);
                }
            })();
        } catch (err) {
            this.app.log.error(
                `Error in transaction for db: ${db.name}: ${err}`
            );
            if (
                !db.inTransaction &&
                !err.message.includes("UNIQUE constraint failed")
            )
                throw new Error(err);
        }

        return insertedReportId;
    }
    update_claim_file_s3(data) {
        if (!data) {
            throw new Error("No data passed for update.");
        }
        this.app.log.info(
            `data in update_claim_file_s3: ${JSON.stringify(data)}`
        );
        if (!data.control_number || !data.report_type) {
            this.app.log.error(
                `Missing control_number or report_type in data.`
            );
            throw new Error("Missing control_number or report_type in data.");
        }
        const results = [];
        for (let db of this.db_clients) {
            db = db.open ? db : new Database(db.name);
            const upd = db
                .prepare(
                    `UPDATE medical_claims_reports SET s3_filepath = ?, s3_filehash = ? WHERE control_number = ? AND report_type = ?`
                )
                .run(
                    data.s3_filepath,
                    data.s3_filehash,
                    data.control_number,
                    data.report_type
                );
            this.app.log.info(
                `Updated claim file uri in db: ${db.name} for control number ${data.control_number} with file info: ${JSON.stringify(data)}`
            );
            results.push(upd);
            db.close();
        }
        return results;
    }

    tableExists(db, tableName) {
        const result = db
            .prepare(
                `SELECT name FROM sqlite_master WHERE type='table' AND name=?`
            )
            .get(tableName);
        return !!result;
    }
    select_reports(opts) {
        const db = this.db_clients[0];
        if (!db) {
            throw new Error("No database connection.");
        }

        const whereConditions = [];
        const queryParams = [];

        // Build where conditions
        if (opts.params.control_number) {
            whereConditions.push("mc.control_number = ?");
            queryParams.push(opts.params.control_number);
        }
        if (opts.params.clara_claim_uuid) {
            whereConditions.push("mc.clara_claim_uuid = ?");
            queryParams.push(opts.params.clara_claim_uuid);
        }
        if (opts.params.clara_mrn) {
            whereConditions.push("mc.clara_mrn = ?");
            queryParams.push(opts.params.clara_mrn);
        }

        const claimWhereClause =
            whereConditions.length > 0
                ? `WHERE ${whereConditions.join(" AND ")}`
                : "";

        // Query for original claim
        const claimQuery = `
            SELECT id, control_number, clara_mrn, clara_claim_uuid, customer_id, customer_name, 
                   request_json_data, response_json_data
            FROM medical_claims mc
            ${claimWhereClause}
            LIMIT 1
        `;
        this.app.log.info(`Claim query: ${claimQuery}`);
        const originalClaim = db.prepare(claimQuery).get(...queryParams);

        if (!originalClaim) {
            return { error: "No claim found matching the provided criteria." };
        }

        // Parse JSON fields
        ["request_json_data", "response_json_data"].forEach((field) => {
            if (originalClaim[field]) {
                try {
                    originalClaim[field] = JSON.parse(originalClaim[field]);
                } catch (e) {
                    this.app.log.warn(
                        `Failed to parse ${field} for claim ${originalClaim.id}: ${e.message}`
                    );
                }
                // Convert dates to Clara format for original claim response
                if (field === "response_json_data") {
                    originalClaim[field] = formatToClaraDT(
                        originalClaim[field]
                    );
                }
            }
        });

        // Add report-specific conditions
        if (opts.params.start_date) {
            whereConditions.push("mcr.created_at >= ?");
            queryParams.push(opts.params.start_date);
        }
        if (opts.params.end_date) {
            whereConditions.push("mcr.created_at <= ?");
            queryParams.push(opts.params.end_date);
        }
        if (opts.params.submitter_id) {
            whereConditions.push("mcr.submitter_id = ?");
            queryParams.push(opts.params.submitter_id);
        }
        if (opts.params.report_type && opts.params.report_type !== "all") {
            whereConditions.push("mcr.report_type = ?");
            queryParams.push(opts.params.report_type);
        }

        const reportWhereClause =
            whereConditions.length > 0
                ? `WHERE ${whereConditions.join(" AND ")}`
                : "";
        const limit = opts.params.limit || 100;
        const offset = opts.params.offset || 0;

        const reportsQuery = `
            SELECT mcr.id, mcr.report_type, mcr.created_at, mcr.raw_report_url, mcr.converted_report_url, 
                   mcr.s3_filepath, mcr.s3_filehash, mcr.control_number,
                   mcr.raw_json, mcr.raw_x12, mcr.payer_name, mcr.payer_info
            FROM medical_claims_reports mcr 
            JOIN gr_medical_claims_reports gmc ON mcr.id = gmc.medical_claim_report_id 
            JOIN medical_claims mc ON gmc.medical_claim_id = mc.id 
            ${reportWhereClause}
            ORDER BY mcr.created_at DESC
            LIMIT ? OFFSET ?
        `;
        this.app.log.info(`Reports query: ${reportsQuery}`);
        const reports = db
            .prepare(reportsQuery)
            .all(...queryParams, limit, offset);

        // Parse JSON fields in reports
        reports.forEach((report) => {
            ["raw_json", "raw_x12", "payer_info"].forEach((field) => {
                if (report[field]) {
                    try {
                        report[field] = JSON.parse(report[field]);
                    } catch (e) {
                        this.app.log.warn(
                            `Failed to parse ${field} for report ${report.id}: ${e.message}`
                        );
                    }
                    // Convert dates to Clara format
                    report[field] = formatToClaraDT(report[field]);
                }
            });
        });

        const groupedReports = {
            835: [],
            277: [],
        };

        reports.forEach((r) => {
            if (r.report_type === "835") {
                groupedReports["835"].push(r);
            } else if (r.report_type === "277") {
                groupedReports["277"].push(r);
            }
        });

        const resp = {
            original_claim: originalClaim,
            reports: groupedReports,
            total: reports.length,
            limit: limit,
            offset: offset,
        };

        return resp;
    }
}
export async function generate_control_number(app) {
    const cdb = new ControlNumberDB(app);
    return cdb.generate_med_controlnum();
}
