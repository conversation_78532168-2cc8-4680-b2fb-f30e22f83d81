export class PgMigrations {
    constructor(app) {
        this.app = app;
    }

    // Define migrations
    migrations = [
        {
            name: "2024_03_19_add_submitter_ncpdp_to_credentials",
            up: async (client) => {
                // Get schemas that have credentials table
                const { rows: schemas } = await client.query(`
                    SELECT DISTINCT schema_name 
                    FROM information_schema.tables 
                    WHERE table_name = 'credentials'
                    AND schema_name NOT IN (
                        'information_schema', 'pg_catalog', 'pg_toast', 'public'
                    )
                `);

                this.app.log.info(
                    `Found credential schemas to update: ${schemas.map((s) => s.schema_name).join(", ")}`
                );

                for (const schema of schemas) {
                    await client.query(`
                        ALTER TABLE ${schema.schema_name}.credentials 
                        ADD COLUMN IF NOT EXISTS submitter_id VARCHAR(255),
                        ADD COLUMN IF NOT EXISTS ncpdp_id VARCHAR(255)
                    `);
                }
            },
        },
        {
            name: "2024_11_26_update_medical_claims_reports",
            up: async (client) => {
                this.app.log.info(
                    "Starting medical_claims_reports table update migration"
                );

                // Fixed schema query
                const { rows: schemas } = await client.query(`
                    SELECT nspname as schema_name
                    FROM pg_catalog.pg_namespace
                    WHERE nspname NOT IN (
                        'information_schema', 'pg_catalog', 'pg_toast', 'public'
                    )
                `);

                this.app.log.info(
                    `Found schemas to update: ${schemas.map((s) => s.schema_name).join(", ")}`
                );

                for (const schema of schemas) {
                    this.app.log.info(
                        `Checking table in schema: ${schema.schema_name}`
                    );

                    // First check if the table exists in this schema
                    const { rows: tableExists } = await client.query(
                        `
                        SELECT EXISTS (
                            SELECT FROM pg_tables
                            WHERE schemaname = $1
                            AND tablename = 'medical_claims_reports'
                        )
                    `,
                        [schema.schema_name]
                    );

                    if (tableExists[0].exists) {
                        this.app.log.info(
                            `Altering table in schema: ${schema.schema_name}`
                        );
                        try {
                            await client.query(`
                                ALTER TABLE "${schema.schema_name}".medical_claims_reports 
                                ADD COLUMN IF NOT EXISTS type VARCHAR(255),
                                ADD COLUMN IF NOT EXISTS customer_id INTEGER,
                                ADD COLUMN IF NOT EXISTS customer_name VARCHAR(255),
                                ADD COLUMN IF NOT EXISTS customer_env_name VARCHAR(255),
                                ADD COLUMN IF NOT EXISTS submitter_id VARCHAR(255),
                                ALTER COLUMN raw_x12 TYPE TEXT USING raw_x12::TEXT
                            `);

                            this.app.log.info(
                                `Successfully updated ${schema.schema_name}.medical_claims_reports schema`
                            );
                        } catch (err) {
                            this.app.log.error(
                                `Error updating schema ${schema.schema_name}:`,
                                {
                                    error: {
                                        message: err.message,
                                        detail: err.detail,
                                        hint: err.hint,
                                        code: err.code,
                                    },
                                }
                            );
                            throw err;
                        }
                    } else {
                        this.app.log.info(
                            `Table medical_claims_reports does not exist in schema: ${schema.schema_name}`
                        );
                    }
                }

                this.app.log.info(
                    "Completed medical_claims_reports table update migration"
                );
            },
        },
        {
            name: "2024_03_20_add_updated_at_and_mcr_index",
            up: async (client) => {
                // Get all schemas excluding system schemas
                const { rows: schemas } = await client.query(`
                    SELECT nspname as schema_name
                    FROM pg_catalog.pg_namespace
                    WHERE nspname NOT IN (
                        'information_schema', 'pg_catalog', 'pg_toast', 'public'
                    )
                `);

                this.app.log.info(
                    `Found schemas to update: ${schemas.map((s) => s.schema_name).join(", ")}`
                );

                for (const schema of schemas) {
                    this.app.log.info(
                        `Processing schema: ${schema.schema_name}`
                    );

                    // Get all tables in the schema
                    const { rows: tables } = await client.query(
                        `
                        SELECT tablename 
                        FROM pg_tables 
                        WHERE schemaname = $1
                    `,
                        [schema.schema_name]
                    );

                    // Add updated_at to all tables
                    for (const table of tables) {
                        await client.query(`
                            ALTER TABLE "${schema.schema_name}"."${table.tablename}"
                            ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;

                            -- Add trigger to automatically update the updated_at timestamp
                            CREATE OR REPLACE FUNCTION "${schema.schema_name}".update_updated_at_column()
                            RETURNS TRIGGER AS $$
                            BEGIN
                                NEW.updated_at = CURRENT_TIMESTAMP;
                                RETURN NEW;
                            END;
                            $$ language 'plpgsql';

                            DROP TRIGGER IF EXISTS update_${table.tablename}_updated_at 
                            ON "${schema.schema_name}"."${table.tablename}";

                            CREATE TRIGGER update_${table.tablename}_updated_at
                                BEFORE UPDATE ON "${schema.schema_name}"."${table.tablename}"
                                FOR EACH ROW
                                EXECUTE FUNCTION "${schema.schema_name}".update_updated_at_column();
                        `);
                    }

                    // Add unique index to medical_claims_reports if it exists
                    const { rows: mcrExists } = await client.query(
                        `
                        SELECT EXISTS (
                            SELECT FROM pg_tables
                            WHERE schemaname = $1
                            AND tablename = 'medical_claims_reports'
                        )
                    `,
                        [schema.schema_name]
                    );

                    if (mcrExists[0].exists) {
                        this.app.log.info(
                            `Adding unique index to medical_claims_reports in ${schema.schema_name}`
                        );
                        await client.query(`
                            CREATE UNIQUE INDEX IF NOT EXISTS mcr_control_report_payer_idx 
                            ON "${schema.schema_name}".medical_claims_reports (control_number, customer_id, report_type, payer_name);
                        `);
                    }
                }

                this.app.log.info(
                    "Completed adding updated_at columns and medical_claims_reports index"
                );
            },
        },
        {
            name: "2024_03_21_update_mcr_constraints",
            up: async (client) => {
                const { rows: schemas } = await client.query(`
                    SELECT nspname as schema_name
                    FROM pg_catalog.pg_namespace
                    WHERE nspname NOT IN (
                        'information_schema', 'pg_catalog', 'pg_toast', 'public'
                    )
                `);

                this.app.log.info(
                    `Found schemas to update: ${schemas.map((s) => s.schema_name).join(", ")}`
                );

                for (const schema of schemas) {
                    // Check if medical_claims_reports exists in this schema
                    const { rows: tableExists } = await client.query(
                        `
                        SELECT EXISTS (
                            SELECT FROM pg_tables
                            WHERE schemaname = $1
                            AND tablename = 'medical_claims_reports'
                        )
                    `,
                        [schema.schema_name]
                    );

                    if (tableExists[0].exists) {
                        this.app.log.info(
                            `Adding constraints to medical_claims_reports in ${schema.schema_name}`
                        );

                        try {
                            // Drop existing index if it exists
                            await client.query(`
                                DROP INDEX IF EXISTS "${schema.schema_name}".mcr_control_report_payer_idx;
                            `);

                            // Add the unique constraint
                            await client.query(`
                                ALTER TABLE "${schema.schema_name}".medical_claims_reports
                                DROP CONSTRAINT IF EXISTS mcr_unique_control_report_customer,
                                ADD CONSTRAINT mcr_unique_control_report_customer 
                                UNIQUE (control_number, report_type, customer_name);
                            `);

                            this.app.log.info(
                                `Successfully added constraints to ${schema.schema_name}.medical_claims_reports`
                            );
                        } catch (err) {
                            this.app.log.error(
                                `Error updating schema ${schema.schema_name}:`,
                                {
                                    error: {
                                        message: err.message,
                                        detail: err.detail,
                                        hint: err.hint,
                                        code: err.code,
                                    },
                                }
                            );
                            throw err;
                        }
                    }
                }

                this.app.log.info(
                    "Completed adding constraints to medical_claims_reports tables"
                );
            },
        },
        {
            name: "2024_03_22_add_clara_delivered_columns",
            up: async (client) => {
                // Get all schemas excluding system schemas
                const { rows: schemas } = await client.query(`
                    SELECT nspname as schema_name
                    FROM pg_catalog.pg_namespace
                    WHERE nspname NOT IN (
                        'information_schema', 'pg_catalog', 'pg_toast', 'public'
                    )
                `);

                this.app.log.info(
                    `Found schemas to update: ${schemas.map((s) => s.schema_name).join(", ")}`
                );

                for (const schema of schemas) {
                    // Check if medical_claims_reports exists in this schema
                    const { rows: tableExists } = await client.query(
                        `
                        SELECT EXISTS (
                            SELECT FROM pg_tables
                            WHERE schemaname = $1
                            AND tablename = 'medical_claims_reports'
                        )
                    `,
                        [schema.schema_name]
                    );

                    if (tableExists[0].exists) {
                        this.app.log.info(
                            `Adding clara_delivered columns to medical_claims_reports in ${schema.schema_name}`
                        );

                        try {
                            await client.query(`
                                ALTER TABLE "${schema.schema_name}".medical_claims_reports 
                                ADD COLUMN IF NOT EXISTS clara_delivered_dt TIMESTAMP WITH TIME ZONE,
                                ADD COLUMN IF NOT EXISTS clara_delivered BOOLEAN DEFAULT FALSE
                            `);

                            this.app.log.info(
                                `Successfully updated ${schema.schema_name}.medical_claims_reports`
                            );
                        } catch (err) {
                            this.app.log.error(
                                `Error updating schema ${schema.schema_name}:`,
                                {
                                    error: {
                                        message: err.message,
                                        detail: err.detail,
                                        hint: err.hint,
                                        code: err.code,
                                    },
                                }
                            );
                            throw err;
                        }
                    }
                }

                this.app.log.info(
                    "Completed adding clara_delivered columns to medical_claims_reports tables"
                );
            },
        },
    ];

    async runMigrations() {
        const client = await this.app.pg.integrations.connect();
        try {
            this.app.log.info("Starting migrations process...");

            await client.query(`CREATE SCHEMA IF NOT EXISTS is_system`);
            this.app.log.info("Verified is_system schema exists");

            await client.query(`
                CREATE TABLE IF NOT EXISTS is_system.migrations (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL UNIQUE,
                    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            `);
            this.app.log.info("Verified migrations table exists");

            for (const migration of this.migrations) {
                try {
                    const { rows } = await client.query(
                        "SELECT id FROM is_system.migrations WHERE name = $1",
                        [migration.name]
                    );

                    if (rows.length === 0) {
                        this.app.log.info(
                            `Starting migration: ${migration.name}`
                        );

                        await client.query("BEGIN");
                        await migration.up(client);

                        await client.query(
                            "INSERT INTO is_system.migrations (name) VALUES ($1)",
                            [migration.name]
                        );

                        await client.query("COMMIT");
                        this.app.log.info(
                            `Completed migration: ${migration.name}`
                        );
                    }
                } catch (err) {
                    await client.query("ROLLBACK");
                    this.app.log.error(
                        `Migration ${migration.name} failed: ${err.message}`,
                        {
                            error: {
                                name: err.name,
                                message: err.message,
                                code: err.code,
                                detail: err.detail,
                                position: err.position,
                                hint: err.hint,
                                schema: err.schema,
                                table: err.table,
                                routine: err.routine,
                                severity: err.severity,
                                stack: err.stack,
                            },
                        }
                    );
                    throw err;
                }
            }
        } catch (err) {
            this.app.log.error(`Migration process failed: ${err.message}`, {
                error: {
                    name: err.name,
                    message: err.message,
                    code: err.code,
                    detail: err.detail,
                    position: err.position,
                    hint: err.hint,
                    schema: err.schema,
                    table: err.table,
                    routine: err.routine,
                    severity: err.severity,
                    stack: err.stack,
                },
            });
            throw err;
        } finally {
            client.release();
        }
    }
}
