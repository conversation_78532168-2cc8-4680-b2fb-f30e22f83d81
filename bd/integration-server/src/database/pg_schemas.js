export const tableSchemas = {
    // Control Numbers Table
    control_numbers: `
        CREATE TABLE IF NOT EXISTS :schema.control_numbers (
            id SERIAL PRIMARY KEY,
            control_number VARCHAR(255) UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `,

    // NCPDP Claims Table
    ncpdp_claims: `
        CREATE TABLE IF NOT EXISTS :schema.ncpdp_claims (
            id SERIAL PRIMARY KEY,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            request_type VARCHAR(255),
            transaction_id VARCHAR(255),
            transmission_id VARCHAR(255),
            switch_provider VARCHAR(255),
            customer_id INTEGER,
            customer_name VARCHAR(255),
            customer_env_name VARCHAR(255),
            clara_mrn VARCHAR(255),
            svc_provider_id VARCHAR(255),
            svc_date VARCHAR(255),
            request_json JSONB,
            request_d0 BYTEA,
            response_json JSONB,
            response_d0 BYTEA,
            errors TEXT,
            latest_request_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `,

    // Medical Claims Table
    medical_claims: `
        CREATE TABLE IF NOT EXISTS :schema.medical_claims (
            id SERIAL PRIMARY KEY,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            request_type VARCHAR(255),
            transaction_id VARCHAR(255),
            trace_id VARCHAR(255),
            control_number VARCHAR(255),
            claims_provider VARCHAR(255),
            clara_mrn VARCHAR(255),
            clara_claim_uuid VARCHAR(255),
            customer_id INTEGER,
            customer_name VARCHAR(255),
            customer_env_name VARCHAR(255),
            request_json_data JSONB,
            response_json_data JSONB,
            validated_claim_object JSONB,
            claim_reference_json JSONB,
            claim_reference_number VARCHAR(255),
            status VARCHAR(255),
            edit_status VARCHAR(255),
            payer_info JSONB,
            errors TEXT,
            x12_data JSONB,
            has_reports BOOLEAN DEFAULT FALSE,
            clara_delivered_dt TIMESTAMP,
            clara_delivered BOOLEAN DEFAULT FALSE,
            latest_request_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `,

    // Medical Claims Reports Table
    medical_claims_reports: `
        CREATE TABLE IF NOT EXISTS :schema.medical_claims_reports (
            id SERIAL PRIMARY KEY,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            type VARCHAR(255),
            raw_json JSONB,
            raw_x12 TEXT,
            converted_report_url TEXT,
            raw_report_url TEXT,
            s3_filepath TEXT,
            s3_filehash TEXT,
            control_number VARCHAR(255),
            submitter_id VARCHAR(255),
            customer_id INTEGER,
            customer_name VARCHAR(255),
            customer_env_name VARCHAR(255),
            payer_name VARCHAR(255),
            payer_info JSONB,
            UNIQUE(control_number, type, raw_report_url)
        )
    `,

    // Medical Claims Reports Relationship Table
    gr_medical_claims_reports: `
        CREATE TABLE IF NOT EXISTS :schema.gr_medical_claims_reports (
            medical_claim_id INTEGER REFERENCES :schema.medical_claims(id),
            medical_claim_report_id INTEGER REFERENCES :schema.medical_claims_reports(id),
            PRIMARY KEY (medical_claim_id, medical_claim_report_id)
        )
    `,

    // PVerify Request Table
    pverify_request: `
        CREATE TABLE IF NOT EXISTS :schema.pverify_request (
            id SERIAL PRIMARY KEY,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            request_type VARCHAR(255),
            customer_id VARCHAR(255) NOT NULL,
            customer_name VARCHAR(255),
            customer_env_name VARCHAR(255),
            clara_mrn VARCHAR(255),
            member_id VARCHAR(255),
            member_type VARCHAR(255),
            request_json JSONB,
            response_json JSONB,
            pv_request_id VARCHAR(255),
            pv_response_code VARCHAR(255),
            s3_filehash BYTEA,
            errors TEXT,
            latest_request_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `,

    // Surescripts Message Table
    ss_message: `
        CREATE TABLE IF NOT EXISTS :schema.ss_message (
            id SERIAL PRIMARY KEY,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tx_direction VARCHAR(255),
            message_id VARCHAR(255) NOT NULL,
            customer_id VARCHAR(255),
            customer_name VARCHAR(255),
            customer_env_name VARCHAR(255),
            related_message_id VARCHAR(255),
            source_ncpdp_id VARCHAR(255),
            dest_ncpdp_id VARCHAR(255),
            message_type VARCHAR(255),
            message_header JSONB,
            message_body JSONB,
            raw_json JSONB,
            raw_xml TEXT,
            message_hash JSONB,
            errors TEXT,
            processed INTEGER DEFAULT 0,
            delivered INTEGER DEFAULT 0,
            UNIQUE(message_id, tx_direction)
        )
    `,

    // Surescripts Organizations Table
    organizations: `
        CREATE TABLE IF NOT EXISTS :schema.organizations (
            id SERIAL PRIMARY KEY,
            OrganizationID VARCHAR(255),
            OrganizationName VARCHAR(255),
            NCPDPID VARCHAR(255),
            NPI VARCHAR(255),
            DEA VARCHAR(255),
            AddressLine1 VARCHAR(255),
            AddressLine2 VARCHAR(255),
            City VARCHAR(255),
            State VARCHAR(255),
            ZIP VARCHAR(255),
            PhoneNumber VARCHAR(255),
            FaxNumber VARCHAR(255),
            LastModifiedDate TIMESTAMP,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(OrganizationID, NPI, AddressLine1, LastModifiedDate)
        )
    `,

    // Surescripts Providers Table
    providers: `
        CREATE TABLE IF NOT EXISTS :schema.providers (
            id SERIAL PRIMARY KEY,
            SPI VARCHAR(255),
            OrganizationID VARCHAR(255),
            NPI VARCHAR(255),
            DEA VARCHAR(255),
            FirstName VARCHAR(255),
            LastName VARCHAR(255),
            MiddleName VARCHAR(255),
            Prefix VARCHAR(255),
            Suffix VARCHAR(255),
            Specialty VARCHAR(255),
            AddressLine1 VARCHAR(255),
            AddressLine2 VARCHAR(255),
            City VARCHAR(255),
            State VARCHAR(255),
            ZIP VARCHAR(255),
            PhoneNumber VARCHAR(255),
            FaxNumber VARCHAR(255),
            PrimarySpecialty VARCHAR(255),
            SecondarySpecialty VARCHAR(255),
            LastModifiedDate TIMESTAMP,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(SPI, NPI, OrganizationID, LastModifiedDate)
        )
    `,

    credentials: `
        CREATE TABLE IF NOT EXISTS :schema.credentials (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            type VARCHAR(50) NOT NULL,
            username VARCHAR(255),
            password VARCHAR(255),
            environment VARCHAR(255),
            api_key VARCHAR ,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(name, type)
        )
    `,
};

export const tableIndexes = {
    ncpdp_claims: [
        "customer_id",
        "clara_mrn",
        "svc_provider_id",
        "request_type",
    ],
    medical_claims: [
        "customer_id",
        "clara_mrn",
        "trace_id",
        "control_number",
        "claims_provider",
        "claim_reference_number",
    ],
    gr_medical_claims_reports: ["medical_claim_id", "medical_claim_report_id"],
    medical_claims_reports: ["control_number", "type"],
    pverify_request: [
        "customer_id",
        "pv_request_id",
        "request_type",
        "member_id",
        "clara_mrn",
    ],
    ss_message: [
        "message_id",
        "customer_id",
        "customer_name",
        "source_ncpdp_id",
        "dest_ncpdp_id",
        "message_type",
    ],
    organizations: ["organizationid", "ncpdpid", "organizationname", "npi"],
    providers: ["spi", "organizationid", "npi", "firstname", "lastname"],
    credentials: [
        "CREATE INDEX IF NOT EXISTS idx_{schema}_credentials_name ON {schema}.credentials(name)",
        "CREATE INDEX IF NOT EXISTS idx_{schema}_credentials_type ON {schema}.credentials(type)",
    ],
};
