"use strict";
import Database from "better-sqlite3";
import path from "path";
import fs from "fs";
import moment from "moment";
import { CopyObjectOutputFilterSensitiveLog } from "@aws-sdk/client-s3";
import crypto from "node:crypto";
function escapeXml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&apos;")
        .replace(/\//g, "-");
}
function lowercaseKeys(obj) {
    if (typeof obj !== "object" || obj === null) {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(lowercaseKeys);
    }
    return Object.keys(obj).reduce((acc, key) => {
        acc[key.toLowerCase()] = lowercaseKeys(obj[key]);
        return acc;
    }, {});
}

// This function takes a dict that you can pass to env.render and escapes special chars..
function escape_template_data(d) {
    if (typeof d === "string") {
        if (/\(\d{3}\)\s??\d{3}[-.\s]??\d{4}/g.test(d)) {
            // Check if string is a phone number
            d = d.replace(/\D/g, "");
            console.log(d);
        }
        return escapeXml(d);
    } else if (Array.isArray(d)) {
        return d.map(escape_template_data);
    } else if (typeof d === "object" && d !== null) {
        const escapedData = {};
        Object.keys(d).forEach(function (key) {
            escapedData[key] = escape_template_data(d[key]);
        });
        return escapedData;
    } else {
        return d;
    }
}

async function store_error_message(app, body, err, d, rxml) {
    // store the error message in the db
    // send the error message to the outbound queue
    // return the error message
    const db_name = d.customer_name ? d.customer_name + ".db" : "default.db";
    const db_path = path.join(app.db_root + "customer/", db_name);
    const db = new Database(db_path, { verbose: console.log });
    const message_id = body?.Message?.Header?.MessageID || app.uuid();
    const err_type = d.error_type || "validation_error";

    try {
        app.log.info(
            `Storing error message in validation_errors table in db: ${db_name}`
        );
        db.exec(`
      CREATE TABLE if not exists validation_errors (
        [id] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
        inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        [message_id] TEXT NOT NULL,
        [err_type] TEXT,
        [err_stack] TEXT,
        [err_msg] TEXT,
        [err_props] TEXT,
        [response_xml] TEXT
      )
    `);
        const row = await db
            .prepare(
                `
      SELECT id, inserted_at, message_id, err_msg
      FROM validation_errors
      WHERE message_id = ? and err_type = ?
    `
            )
            .get(message_id, err_type);
        if (row) {
            app.log.info(
                `Error message already stored in validation_errors table in db: ${db_name}, with result: ${JSON.stringify(row)}`
            );
        }
        const stmt = db.prepare(`
      INSERT INTO validation_errors (message_id, err_type, err_stack, err_msg, err_props, response_xml)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
        const res = stmt.run(
            message_id,
            err_type,
            err.stack,
            err.message,
            JSON.stringify(err),
            rxml
        );
        if (res?.changes) {
            app.log.info(
                `Inserted ${err_type} into validation_errors table in db: ${db_name}, with result:  ${JSON.stringify(res)}`
            );
        }
    } catch (err) {
        app.log.error(err);
        throw new Error(
            `Error storing error message in validation_errors table in db: ${db_name}`
        );
    } finally {
        db.close((err) => {
            if (err) {
                app.log.error(err);
            }
        });
    }
}

async function getCustomerDetails(app, ncpdpid) {
    app.log.info(`Getting customer details for NCPDP ID: ${ncpdpid}`);
    const client = await app.pg.cadmin.connect();

    try {
        const query = `WITH ndcl AS (
        SELECT
            cs.customer_id,
            c.appname as customer_name,
            array_agg(DISTINCT n.ncpdp_id) AS ncpdpids
        FROM
            ncpdp n
            INNER JOIN customer_secrets cs ON n.customer_id = cs.customer_id
    JOIN customers c ON cs.customer_id = c.id
        GROUP BY
            cs.customer_id,
            c.appname
    )
    SELECT
        cs.customer_id,
       ndcl.customer_name,
        json_object_agg(cs.code, cs.secret) as secrets,
        ndcl.ncpdpids
    FROM
        customer_secrets cs
        INNER JOIN ncpdp n ON cs.customer_id = n.customer_id
        INNER JOIN ndcl ON cs.customer_id = ndcl.customer_id
    WHERE
        n.ncpdp_id = $1
    GROUP BY
        cs.customer_id,
        ndcl.customer_name,
        ndcl.ncpdpids;`;

        const { rows } = await client.query(query, [ncpdpid]);

        if (!rows || rows.length === 0) {
            app.log.warn(`No customer details found for NCPDP ID: ${ncpdpid}`);
            return null;
        }
        app.log.info(`Customer details found for NCPDP ID: ${ncpdpid}`);
        app.log.info(`Customer details: ${JSON.stringify(rows)}`);
        return rows[0];
    } catch (err) {
        app.log.error(`Error getting customer details: ${err.message}`);
        throw err; // Re-throw the error to be handled by the caller
    } finally {
        client.release();
    }
}
async function getCustomerDetailsbyCID(app, customer_id) {
    app.log.info(`Getting customer details for customer ID: ${customer_id}`);
    const client = await app.pg.cadmin.connect();

    try {
        const query = `
        SELECT
            cs.customer_id,
            c.appname as customer_name,
            json_object_agg(cs.code, cs.secret) as secrets
        FROM
            customer_secrets cs
            INNER JOIN customers c ON cs.customer_id = c.id
        WHERE
            cs.customer_id = $1
        GROUP BY
            cs.customer_id,
            c.appname;`;

        const { rows } = await client.query(query, [customer_id]);

        if (!rows || rows.length === 0) {
            app.log.warn(
                `No customer details found for customer ID: ${customer_id}`
            );
            return null;
        }
        return rows[0];
    } catch (err) {
        app.log.error(`Error getting customer details: ${err.message}`);
        throw err; // Re-throw the error to be handled by the caller
    } finally {
        client.release();
    }
}
async function getCustomerDetailsbySID(app, submitter_id) {
    app.log.info(`Getting customer details for submiter ID: ${submitter_id}`);
    const client = await app.pg.cadmin.connect();

    try {
        const query = `
        SELECT
            cs.customer_id,
            c.appname as customer_name,
            json_object_agg(cs.code, cs.secret) as secrets
        FROM
            customer_secrets cs
            INNER JOIN customers c ON cs.customer_id = c.id
        WHERE
            cs.code == 'CHANGE_SUBMITTER_ID'
            and cs.secret = $1
        GROUP BY
            cs.customer_id,
            c.appname;`;

        const { rows } = await client.query(query, [submitter_id]);

        if (!rows || rows.length === 0) {
            app.log.warn(
                `No customer details found for submiter ID: ${submitter_id}`
            );
            return null;
        }
        app.log.info(`Customer details found for submiter ID: ${submitter_id}`);
        app.log.info(`Customer details: ${JSON.stringify(rows)}`);
        return rows[0];
    } catch (err) {
        app.log.error(`Error getting customer details: ${err.message}`);
        throw err; // Re-throw the error to be handled by the caller
    } finally {
        client.release();
    }
}
async function register_route_schemas(app, schemas) {
    for (let i = 0; i < schemas.length; i++) {
        const schemaName = Object.keys(schemas[i])[0];
        app.log.info(`Registering schema: ${schemaName}`);
        app.addSchema(schemas[i][schemaName]);
    }
}

async function generate_signature(messagePromise) {
    // Await the message if it's a Promise
    const message = await Promise.resolve(messagePromise);

    if (typeof message !== "string" && !Buffer.isBuffer(message)) {
        throw new TypeError("Message must be a string or Buffer");
    }

    const hash = crypto.createHash("sha256").update(message).digest();
    const privateKey = fs.readFileSync(
        "src/modules/surescripts/fixtures/certs/key.pem",
        "utf8"
    );

    // Sign the hash with the RSA private key
    const signature = crypto.sign("sha256", hash, {
        key: privateKey,
        padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
    });

    return {
        signature: signature.toString("base64"),
        hash: hash.toString("base64"),
    };
}

async function verify_signature(hashBase64, signature) {
    const publicKey = fs.readFileSync(
        "src/modules/surescripts/fixtures/certs/cert.pem",
        "utf8"
    );
    // Convert the provided Base64 hash to a Buffer, as crypto.verify expects a Buffer or TypedArray
    const hashBuffer = Buffer.from(hashBase64, "base64"); // Changed from hex to base64
    // Verify the signature with the RSA public key
    const isVerified = crypto.verify(
        "sha256",
        hashBuffer,
        {
            key: publicKey,
            padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
        },
        Buffer.from(signature, "base64")
    );
    return isVerified;
}

function formatToClaraDT(obj) {
    if (Array.isArray(obj)) {
        return obj.map(formatToClaraDT);
    }

    if (typeof obj !== "object" || obj === null) {
        return obj;
    }

    const formattedObj = {};

    for (const [key, value] of Object.entries(obj)) {
        const lowercaseKey = key.toLowerCase();
        if (
            typeof value === "string" &&
            (lowercaseKey.includes("date") || lowercaseKey.includes("time"))
        ) {
            formattedObj[key] = formatDateString(value);
        } else if (typeof value === "object") {
            formattedObj[key] = formatToClaraDT(value);
        } else {
            formattedObj[key] = value;
        }
    }

    return formattedObj;
}

function formatDateString(dateString) {
    try {
        console.debug(`formatting date string: ${dateString}`);
        if (dateString.match(/^\d{6}$/)) {
            const date = moment(dateString, "HHmmss");
            return date.isValid() ? date.format("hh:mm:ss a") : dateString;
        } else if (dateString.match(/T\d{2}:\d{2}:\d{2}.\d{3}/)) {
            const date = moment(dateString);
            return date.isValid()
                ? date.format("MM/DD/YYYY hh:mm:ss a")
                : dateString;
        } else {
            const date = moment(dateString);
            return date.isValid() ? date.format("MM/DD/YYYY") : dateString;
        }
    } catch (error) {
        console.error(`Failed to parse date: ${dateString}`, error);
        return dateString;
    }
}

function isFipsRequired() {
    // Check command line args for --enable-fips
    const hasFipsFlag = process.execArgv.includes("--enable-fips");

    // Check environment variable
    const hasFipsEnv = "OPENSSL_FIPS" in process.env;

    return hasFipsFlag || hasFipsEnv;
}

function validateFipsMode() {
    const fipsRequired = isFipsRequired();

    try {
        if (!fipsRequired) {
            return {
                enabled: false,
                message: "FIPS mode not requested",
            };
        }

        // Try to enable FIPS
        crypto.setFips(true);

        // Verify FIPS is actually enabled
        if (!crypto.getFips()) {
            throw new Error("FIPS mode could not be enabled");
        }

        return {
            enabled: true,
            message: "FIPS mode successfully enabled",
        };
    } catch (error) {
        if (fipsRequired) {
            // If FIPS was requested but failed, this is an error
            throw new Error(
                `FIPS mode required but failed to enable: ${error.message}`
            );
        }

        return {
            enabled: false,
            error: error.message,
            message: "FIPS mode not available",
        };
    }
}

/**
 * Safely stringify JSON data
 * @param {any} data - Data to stringify
 * @returns {string} - Stringified JSON
 */
function safeJSONStringify(data) {
    try {
        // Handle null/undefined
        if (data == null) return null;

        // If it's already a string, try parsing and re-stringifying to validate
        if (typeof data === "string") {
            JSON.parse(data); // Will throw if invalid JSON
            return data;
        }

        // Otherwise stringify the data
        return JSON.stringify(data);
    } catch (err) {
        console.error("JSON Stringify Error:", err);
        return null;
    }
}

export {
    safeJSONStringify,
    escape_template_data,
    escapeXml,
    store_error_message,
    register_route_schemas,
    generate_signature,
    verify_signature,
    getCustomerDetails,
    getCustomerDetailsbyCID,
    getCustomerDetailsbySID,
    lowercaseKeys,
    formatToClaraDT,
    validateFipsMode,
};
