import pino from "pino";
import fs from "fs";
import path from "path";

// Required Pino interface methods
const REQUIRED_METHODS = [
    "info",
    "error",
    "debug",
    "fatal",
    "warn",
    "trace",
    "silent",
    "child",
];

// Helper to create log directory if it doesn't exist
function ensureLogDirectory(logDir) {
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
}

// Base configuration for pretty printing
const prettyPrintConfig = {
    colorize: true,
    levelFirst: true,
    translateTime: "yyyy-mm-dd HH:MM:ss Z",
    ignore: "pid,hostname,name",
    messageFormat: "[{component}] {msg}",
    singleLine: true,
    customColors: "error:red,warn:yellow,info:blue,debug:green,trace:gray",
};

// File format configuration (no colors, but same structure)
const fileFormatConfig = {
    ...prettyPrintConfig,
    colorize: false,
};

// Helper to safely stringify objects with circular references
function safeStringify(obj) {
    const seen = new WeakSet();
    return JSON.stringify(
        obj,
        (key, value) => {
            if (key === "socket" || key === "parser") return undefined;
            if (typeof value === "object" && value !== null) {
                if (seen.has(value)) {
                    return "[Circular]";
                }
                seen.add(value);
            }
            return value;
        },
        2
    );
}

// Create base logger configuration
const baseLoggerConfig = {
    level: process.env.LOG_LEVEL || "debug",
    name: "Integration Server",
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
        bindings: (bindings) => {
            return {
                component: bindings.component || "Integration Server",
            };
        },
        log: (obj) => {
            try {
                // Handle string input
                if (typeof obj === "string") {
                    return { msg: obj };
                }

                // Handle non-object input
                if (typeof obj !== "object" || obj === null) {
                    return { msg: String(obj) };
                }

                // Handle debug objects with properties
                if (obj.msg && typeof obj.msg === "string") {
                    const { msg, ...rest } = obj;

                    // Format any nested objects
                    const formattedRest = Object.entries(rest).reduce(
                        (acc, [key, value]) => {
                            if (typeof value === "object" && value !== null) {
                                acc[key] = JSON.stringify(value, null, 2);
                            } else {
                                acc[key] = value;
                            }
                            return acc;
                        },
                        {}
                    );

                    // Create details string from remaining properties
                    const details = Object.entries(formattedRest)
                        .map(([k, v]) => `${k}: ${v}`)
                        .join(", ");

                    return {
                        msg: msg + (details ? ` (${details})` : ""),
                        ...formattedRest,
                    };
                }

                // Handle debug objects with key/value pairs
                if (obj.msg === "toJSON" || obj.msg?.startsWith("Processing")) {
                    const { msg, key, value, mappedk, ...rest } = obj;
                    const details = Object.entries(rest)
                        .map(([k, v]) => `${k}: ${v}`)
                        .join(", ");

                    return {
                        msg: msg + (details ? ` (${details})` : ""),
                        ...(key && { key }),
                        ...(value && { value }),
                        ...(mappedk && { mappedk }),
                    };
                }

                // Handle request objects like any other object
                if (
                    obj.msg === "incoming request" ||
                    obj.msg === "request completed"
                ) {
                    const { req, res, ...rest } = obj;

                    return {
                        msg: obj.msg,
                        ...rest,
                        ...(req && { request: req }),
                        ...(res && { response: res }),
                    };
                }

                // If it's an object with a msg property, use it directly
                if (obj.msg2) {
                    return { msg: obj.msg2 };
                }
                if (obj.msg) {
                    return { msg: obj.msg };
                }

                // Format objects nicely
                if (obj.toJSON) {
                    return { msg: JSON.stringify(obj.toJSON(), null, 2) };
                }

                // Otherwise stringify the object
                const stringified = safeStringify(obj);
                if (stringified === "{}") return { msg: "" };

                // Try to format the stringified object nicely
                try {
                    const parsed = JSON.parse(stringified);
                    return { msg: JSON.stringify(parsed, null, 2) };
                } catch {
                    return { msg: stringified };
                }
            } catch (err) {
                return {
                    msg: "Error stringifying log object",
                    error: err.message,
                    originalMsg: String(obj.msg || ""),
                };
            }
        },
    },
};

// Ensure logs directory exists
ensureLogDirectory("logs");

// Create main application logger
export const logger = pino({
    ...baseLoggerConfig,
    transport: {
        targets: [
            {
                target: "pino-pretty",
                level: "trace",
                options: prettyPrintConfig,
            },
            {
                target: "pino-pretty",
                level: "trace",
                options: {
                    ...fileFormatConfig,
                    destination: `logs/integration_server.log`,
                },
            },
        ],
    },
});

// Verify logger has required methods
REQUIRED_METHODS.forEach((method) => {
    if (typeof logger[method] !== "function") {
        throw new Error(`Logger missing required method: ${method}`);
    }
});

// Store original child method
const originalChild = logger.child.bind(logger);

// Child loggers should write to the same transport
logger.child = function childLogger(bindings) {
    return originalChild({ 
        component: bindings.component || "Integration Server" 
    });
};
