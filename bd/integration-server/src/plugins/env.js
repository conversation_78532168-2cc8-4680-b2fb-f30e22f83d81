import fastifyEnv from "@fastify/env";
import { join } from "desm";
import fastifyPlugin from "fastify-plugin";
import * as uuid from "uuid";

async function configPlugin(app, options, done) {
    const schema = {
        type: "object",
        required: ["HTTP_PORT"],
        properties: {
            HTTP_PORT: {
                type: "string",
                default: 3000,
            },
            HTTP_HOST: {
                type: "string",
                default: "0.0.0.0",
            },
            ADMIN_DB_URI: {
                type: "string",
            },
            LOG_BASE_PATH: {
                type: "string",
                default: "logs",
            },
            IS_DB_URI: {
                type: "string",
            },
            USE_POSTGRES: {
                type: "string",
                default: "true",
            },
            AWS_S3_ACCESS_KEY: {
                type: "string",
            },
            AWS_S3_SECRET_KEY: {
                type: "string",
            },
            AWS_S3_BUCKET: {
                type: "string",
            },
            CHANGE_MED_STAGING_BASE_URL: {
                type: "string",
                default: "https://sandbox-apigw.optum.com",
            },
            <PERSON><PERSON><PERSON>_MED_PROD_BASE_URL: {
                type: "string",
                default: "https://apigw.optum.com",
            },
            CHANGE_MED_STAGING_CLIENT_ID: {
                type: "string",
            },
            CHANGE_MED_STAGING_CLIENT_SECRET: {
                type: "string",
            },
            CHANGE_MED_PROD_CLIENT_ID: {
                type: "string",
            },
            CHANGE_MED_PROD_CLIENT_SECRET: {
                type: "string",
            },
            CHANGE_PHARM_STAGING_PASSWORD: {
                type: "string",
            },
            CHANGE_PHARM_PROD_USERNAME: {
                type: "string",
            },
            CHANGE_PHARM_PROD_PASSWORD: {
                type: "string",
            },
            CHANGE_PHARM_PROD_SOAP_URL: {
                type: "string",
                default:
                    "https://rxnsconnect.changehealthcare.com/xmlws.asmx?wsdl",
            },
            CHANGE_PHARM_PROD_BASE_URL: {
                type: "string",
                default: "https://rxnsconnect.changehealthcare.com/xmlws.asmx",
            },
            PVERIFY_STAGING_CLIENT_SECRET: {
                type: "string",
            },
            PVERIFY_STAGING_OAUTH_URL: {
                type: "string",
                default: "https://api.pverify.com/test/Token",
            },
            PVERIFY_STAGING_BASE_URL: {
                type: "string",
                default: "https://api.pverify.com/test/API",
            },
            PVERIFY_STAGING_CLIENT_ID: {
                type: "string",
                default: "06728887-e258-42db-9d16-7ad15c2f29ba",
            },
            PVERIFY_PROD_CLIENT_ID: {
                type: "string",
                default: "06728887-e258-42db-9d16-7ad15c2f29ba",
            },
            PVERIFY_PROD_OAUTH_URL: {
                type: "string",
                default: "https://api.pverify.com/test/Token",
            },
            PVERIFY_PROD_BASE_URL: {
                type: "string",
                default: "https://api.pverify.com/test/API",
            },
            PVERIFY_PROD_CLIENT_SECRET: {
                type: "string",
            },
            POWERLINE_STAGING_CLIENT_ID: {
                type: "string",
                default: "envoylabs-dev",
            },
            POWERLINE_STAGING_OAUTH_URL: {
                type: "string",
                default: "https://jwt.trx-processor.io/jwt-provider/token",
            },
            POWERLINE_STAGING_BASE_URL: {
                type: "string",
                default:
                    "https://claimprocessor.trx-processor.io/claim-processor/api/v1",
            },
            POWERLINE_STAGING_CLIENT_SECRET: {
                type: "string",
            },
            POWERLINE_PROD_CLIENT_ID: {
                type: "string",
                default: "envoylabs",
            },
            POWERLINE_PROD_CLIENT_SECRET: {
                type: "string",
            },
            POWERLINE_PROD_BASE_URL: {
                type: "string",
                default:
                    "https://claimprocessor.trx-processor.net/claim-processor/api/v1",
            },
            POWERLINE_PROD_OAUTH_URL: {
                type: "string",
                default: "https://jwt.trx-processor.net/jwt-provider/token",
            },
            SURESCRIPTS_ENVIRONMENT: {
                type: "string",
                default: "sandbox",
            },
            SURESCRIPTS_SYSTEM_ID: {
                type: "string",
            },
            SURESCRIPTS_ACCOUNT_ID: {
                type: "string",
            },
            SURESCRIPTS_PORTAL_ID: {
                type: "string",
            },
            SURESCRIPTS_USERNAME: {
                type: "string",
            },
            SURESCRIPTS_PASSWORD: {
                type: "string",
            },
            SURESCRIPTS_MTLS_CERT_B64: {
                type: "string",
            },
            SURESCRIPTS_MTLS_KEY_B64: {
                type: "string",
            },
            SURESCRIPTS_MTLS_CA_B64: {
                type: "string",
            },
            GOOGLE_PROJECT_ID: {
                type: "string",
                default: "test-fax-conversion",
            },
            GOOGLE_PROCESSOR_ID: {
                type: "string",
                default: "b671591e46bbb505",
            },
            GOOGLE_APPLICATION_CREDENTIALS: {
                type: "string",
                default: app.module_root + "/ai/google-creds.json",
            },
            OPENAI_API_KEY: {
                type: "string",
            },
            NCPDP_SCHEMA_VERSION: {
                type: "string",
                default: "20170715",
            },
            CLAIMS_ENVIRONMENT: {
                type: "string",
                default: "testing",
            },
        },
    };

    const configOptions = {
        confKey: "config",
        schema: schema,
        data: process.env,
        dotenv: true,
        removeAdditional: true,
    };

    app.decorate("uuid", () => {
        return uuid.v4().replaceAll("-", "");
    });
    app.decorate("rnum", (length) => {
        return Math.floor(
            Math.pow(10, length - 1) +
                Math.random() * 9 * Math.pow(10, length - 1)
        );
    });
    return fastifyEnv(app, configOptions, done);
}
export default fastifyPlugin(configPlugin);
