import bearerAuthPlugin from "@fastify/bearer-auth";
import fastifyPlugin from "fastify-plugin";

async function AuthPlugin(app, options, done) {
    app.log.info("loading bearer auth plugin");
    const bearopts = {
        auth: async (key, req) => {
            const client = await app.pg.cadmin.connect();
            try {
                const { rows } = await client.query(
                    `SELECT 
                        s.customer_id, 
                        c.appname,
                        s.code,
                        s.env,
                        (
                            SELECT 
                                SPLIT_PART(fly.secret, '-', 1) || SPLIT_PART(fly.secret, '-', 2)
                            FROM customer_secrets fly
                            WHERE fly.customer_id = s.customer_id 
                            AND fly.code = 'FLY_ID'
                            AND fly.env = s.env
                            LIMIT 1
                        ) as env_name
                    FROM customers c 
                    JOIN customer_secrets s ON s.customer_id = c.id 
                    WHERE s.secret = $1 
                    LIMIT 1`,
                    [key]
                );

                app.log.debug(rows);

                if (rows.length === 0) {
                    throw new Error("Unauthorized");
                }

                if (rows.length === 1) {
                    const row = rows[0];
                    const envName = row.env_name || row.appname.toLowerCase();

                    app.log.info(`Authorized: ${row.appname} (${envName})`);

                    req.customer_id = row.customer_id;
                    req.customer_name = row.appname.toLowerCase();
                    req.customer_env_name = envName;

                    return true;
                }
            } finally {
                client.release();
            }
        },
        addHook: false,
    };
    return bearerAuthPlugin(app, bearopts, done);
}

export default fastifyPlugin(AuthPlugin);
