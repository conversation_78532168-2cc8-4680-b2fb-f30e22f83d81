import fastifyPlugin from "fastify-plugin";
import fastifyPostgres from "@fastify/postgres";
async function PGdbPlugin(app, options, done) {
    console.log("loading db plugin");
    const opts = {
        name: app.config.DB_NAME || "integrations",
        connectionString: app.config.IS_DB_URI,
    };
    console.log(opts);
    return fastifyPostgres(app, opts, done);
}

export default fastifyPlugin(PGdbPlugin);
