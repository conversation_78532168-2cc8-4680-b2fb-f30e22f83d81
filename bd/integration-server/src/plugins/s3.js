import fastifyPlugin from "fastify-plugin";
import {
    S3Client,
    PutObjectCommand,
    GetObjectCommand,
    HeadObjectCommand,
} from "@aws-sdk/client-s3";
import mime from "mime";

async function awsS3(app, options, next) {
    app.log.info("Registering S3 Plugin");
    if (
        !app.config.AWS_S3_ACCESS_KEY ||
        !app.config.AWS_S3_SECRET_KEY ||
        !app.config.AWS_S3_BUCKET
    ) {
        return next(
            new Error(
                "AWS_SECRET_KEY, AWS_ACCESS_KEY and AWS_S3_BUCKET are required."
            )
        );
    }
    const bucket = app.config.AWS_S3_BUCKET;
    const opts = {
        credentials: {
            accessKeyId: app.config.AWS_S3_ACCESS_KEY,
            secretAccessKey: app.config.AWS_S3_SECRET_KEY,
        },
        region: "us-east-1", // Replace with your AWS region
    };
    const client = new S3Client(opts);

    client.get = async function (key, opts = {}) {
        if (!opts.godmode) {
            // Override for admin user only to get any file from s3
            if (!opts.customer_name) {
                throw new Error("Unauthorized");
            }
            if (!key.includes(opts.customer_name)) {
                app.log.error(
                    `Unauthorized access to file: ${key} from customer: ${opts.customer_name}`
                );
                throw new Error("Unauthorized");
            }
        }
        if (!key) {
            throw new Error("No key passed for s3 GET.");
        }

        const params = {
            Bucket: bucket,
            Key: key,
        };
        try {
            app.log.info(`Fetching file from s3: ${key}`);
            const command = new GetObjectCommand(params);
            const { Body } = await client.send(command);
            const chunks = [];
            for await (const chunk of Body) {
                chunks.push(chunk);
            }
            const data = Buffer.concat(chunks);

            return {
                body: data,
                mimetype: Body.ContentType,
                headers: {
                    "Content-Disposition": `attachment;filename=${key}`,
                },
            };
        } catch (err) {
            app.log.error("An error occured fetching the file from s3: " + err);
            throw new Error(err.message);
        }
    };

    client.fetch_uri = async function (uri) {
        if (!uri) {
            throw new Error("No uri passed for s3 GET.");
        }
        if (uri) {
            let decodedUri = uri;
            try {
                decodedUri = Buffer.from(uri, "base64").toString("utf-8");
            } catch (error) {
                app.log.warn(
                    "Failed to decode URI as base64, using original URI " +
                        error.message
                );
            }

            if (!decodedUri.startsWith("s3://")) {
                throw new Error("Invalid S3 URI format");
            }
            app.log.info(`Fetching file from s3: ${decodedUri}`);
            const bucket = decodedUri.split("/")[2];
            const key = decodedUri.split("/").slice(3).join("/");
            const command = new GetObjectCommand({ Bucket: bucket, Key: key });
            const { Body } = await client.send(command);
            const chunks = [];
            for await (const chunk of Body) {
                chunks.push(chunk);
            }
            const data = Buffer.concat(chunks);
            return data;
        }
    };

    client.put = async function (opts, data) {
        if (!data) {
            app.log.error(
                `No data passed for upload to s3 with opts: ${JSON.stringify(opts)}`
            );
            throw new Error("No Data passed for upload.");
        }
        if (!opts.filename || !opts.base_path) {
            throw new Error("Both filename and base_path are required");
        }
        const pkey = `${opts.base_path}/${opts.filename}`;
        const contentType = opts.contentType
            ? opts.contentType
            : mime.getType(opts.filename);

        const params = {
            Bucket: bucket,
            Key: pkey,
            Body: data,
            ServerSideEncryption: "AES256",
            StorageClass: "STANDARD_IA",
            Metadata: {
                filename: opts.filename,
                contentType: contentType,
                customer_id: String(opts.customer_id),
            },
        };
        if (opts.filehash) {
            params.Metadata.filehash = opts.filehash;
        }
        const command = new PutObjectCommand(params);
        try {
            app.log.info(
                `Uploading file to s3 with opts: ${JSON.stringify(opts)}`
            );
            const response = await client.send(command);
            app.log.info("File uploaded to s3: " + JSON.stringify(response));
            return response;
        } catch (err) {
            app.log.error("Error uploading to S3", err);
            throw new Error(err.message);
        }
    };

    client.fileExists = async function (filehash) {
        const params = {
            Bucket: bucket,
            Key: filehash,
        };
        const command = new HeadObjectCommand(params);

        try {
            await client.send(command);
            return { exists: true, error: null };
        } catch (error) {
            if (error.name === "NotFound") {
                return { exists: false, error: null };
            }
            return { exists: false, error };
        }
    };

    app.decorate("s3", client);
    next();
}

export default fastifyPlugin(awsS3);
