import fastifyPlugin from "fastify-plugin";
import <PERSON>g<PERSON><PERSON> from "pg-boss";
import {
    SSIn<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    SSDirectory<PERSON><PERSON><PERSON><PERSON><PERSON>,
    SSClaraResponse<PERSON><PERSON>,
    MedicalClaimReportsHandler,
    ReportsDeliveryManager,
} from "../pgboss/inbound-pgboss-handler.js";
import { createPrefixedLogger } from "../utils/pre_logger.js";
import EventEmitter from "events";

async function pgbPlugin(app, options, next) {
    const log = createPrefixedLogger(app.log, "[PGBOSS] - ");
    const alog = createPrefixedLogger(app.log, "[ASYNC HANDLER] - ");
    const logEmitter = new EventEmitter();
    log.info("loading PG Boss plugin");
    const opts = {
        application_name: "surescripts",
        connectionString: app.config.IS_DB_URI,
    };
    const boss = new PgBoss(opts, next);
    let errConnectionRetries = 0;

    boss.on("error", (error) => {
        log.error(JSON.stringify(error));
        if (error.code === "ECONNREFUSED") {
            errConnectionRetries++;
        }
        if (errConnectionRetries > 5) {
            log.error(
                `Connection lost to postgres after ${errConnectionRetries} retries.  Stopping.`
            );
            boss.stop().catch(console.error);
        }
    });

    await boss.start();

    if (app.pgboss) {
        if (app.pgboss.pool) {
            return next(
                new Error("fastify-pgboss has already been registered")
            );
        }
        Object.assign(app.pgboss, boss);
    } else {
        app.decorate("pgboss", boss);
    }

    const consumer_opts = {
        teamSize: 1,
        teamConcurrency: 1,
        newJobCheckInterval: 1000,
    };

    logEmitter.on("async_log", (message) => {
        alog.debug(message);
    });

    await boss.work("ss_inbound", consumer_opts, async (job) => {
        try {
            log.info(
                `job ${job.id} received with data ${Object.keys(job.data)} `
            );
            const jobResult = await SSInboundJobHandler(app, job, logEmitter);
            log.info(
                `job ${job.id} completed with data: ${JSON.stringify(jobResult)}`
            );
        } catch (error) {
            log.error(`job ${job.id} failed with error: ${error}`);
            job.fail(error);
        }
    });
    await boss.work("ss_directory", consumer_opts, async (job) => {
        try {
            log.info(
                `job ${job.id} received with data ${Object.keys(job.data)} `
            );
            const jobResult = await SSDirectoryJobHandler(app, job, logEmitter);
            log.info(
                `job ${job.id} completed with data: ${JSON.stringify(jobResult)}`
            );
        } catch (error) {
            log.error(`job ${job.id} failed with error: ${error}`);
            job.fail(error);
        }
    });

    await boss.work("clara_ss_response", consumer_opts, async (job) => {
        try {
            log.info(
                `job ${job.id} for clara_ss_response received with data ${Object.keys(job.data)} `
            );
            const jobResult = await SSClaraResponseHandler(
                app,
                job,
                logEmitter
            );
            log.info(
                `job ${job.id} completed with data: ${JSON.stringify(jobResult)}`
            );
        } catch (error) {
            log.error(`job ${job.id} failed with error: ${error}`);
            job.fail(error);
        }
    });
    await boss.work("medical_claim_reports", consumer_opts, async (job) => {
        try {
            log.info(
                `job ${job.id} for medical_claim_reports received with data ${Object.keys(job.data)} `
            );
            const jobResult = await MedicalClaimReportsHandler(
                app,
                job,
                logEmitter
            );
            log.info(
                `job ${job.id} completed with data: ${JSON.stringify(jobResult)}`
            );
        } catch (error) {
            log.error(`job ${job.id} failed with error: ${error}`);
            job.fail(error);
        }
    });
    await boss.work("report_delivery_manager", consumer_opts, async (job) => {
        try {
            log.info(
                `job ${job.id} for report_delivery_manager received with data ${JSON.stringify(job.data)}`
            );
            const jobResult = await ReportsDeliveryManager(
                app,
                job,
                logEmitter
            );
            log.info(
                `job ${job.id} completed with data: ${JSON.stringify(jobResult)}`
            );
        } catch (error) {
            log.error(`job ${job.id} failed with error: ${error}`);
            job.fail(error);
        }
    });
    next();
}

export default fastifyPlugin(pgbPlugin);
