import rawbody from "fastify-raw-body";
import fastifyPlugin from "fastify-plugin";

async function RawBodyPlugin(app, options, done) {
    console.log("loading raw body config plugin");
    const rbodyopts = {
        field: "rawBody", // change the default request.rawBody property name
        global: false, // add the rawBody to every request. **Default true**
        encoding: "utf8", // set it to false to set rawBody as a Buffer **Default utf8**
        runFirst: false, // get the body before any preParsing hook change/uncompress it. **Default false**
        routes: [], // array of routes, **`global`** will be ignored, wildcard routes not supported
    };
    return rawbody(app, rbodyopts, done);
}
export default fastifyPlugin(RawBodyPlugin);
