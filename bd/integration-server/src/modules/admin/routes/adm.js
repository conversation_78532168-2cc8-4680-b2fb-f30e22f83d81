/**
 * Retrieves a list of customers supported by the Integration Server.
 * This list is used to determine if a customer is supported by the Integration Server.
 *
 * @param {object} app - The Fastify app instance.
 * @param {object} opts - The options object.
 * @returns {Promise<Array<object>>} - A promise that resolves to an array of customer objects.
 */
export default async function (app, _opts) {
    app.get(
        "/customers",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                hide: true,
                description:
                    "Get a list of customers that are supported by the Integration Server. This list is used to determine if a customer is supported by the Integration Server.",
                tags: ["Admin"],
                summary:
                    "Get a list of customers supported by the Integration Server.",
                response: {
                    200: {
                        description: "Successful response",
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                customer_id: { type: "number" },
                                customer_name: { type: "string" },
                                ncpdp_ids: {
                                    type: "array",
                                    items: { type: "string" },
                                },
                            },
                        },
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                    },
                },
            },
        },
        async (request, reply) => {
            app.log.info(
                `Getting customers for request: ${JSON.stringify(request)}`
            );
            const client = await app.pg.cadmin.connect();
            try {
                const { rows } = await client.query(
                    `SELECT c.id as customer_id, c.name as customer_name, ARRAY_AGG(n.ncpdp_id) as ncpdp_ids from customers c join ncpdp n on c.id = n.customer_id GROUP BY c.id, c.name ORDER BY c.id`
                );
                console.log(rows);
                reply.send(rows);
                return reply;
            } finally {
                client.release();
            }
        }
    );
}
