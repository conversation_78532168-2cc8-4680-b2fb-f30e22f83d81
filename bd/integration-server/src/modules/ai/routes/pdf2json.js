"use strict";
import { OpenAI } from "openai";
import json5 from "json5";
import { DocumentProcessorServiceClient } from "@google-cloud/documentai";
import fs from "fs/promises";
const { parse } = json5;
import {
    sanitizeJSON,
    extractTextFromPDFGoogle,
    generatePrompt,
    cleanupExtractedData,
} from "../utils.js";

export default async function (app, opts) {
    app.get(
        "/pdf2json",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: `This endpoint is used to take a s3 filepath , download the file and use google DocumentAI / ChatGPT to extract the data and return a JSON object.
        `,
                tags: ["AI"],
                summary:
                    "Extract data from a fax image and return a JSON object.",
                querystring: {
                    type: "object",
                    properties: {
                        s3_uri: {
                            type: "string",
                            description:
                                "Base64 encoded s3 uri of the file to extract data from.",
                        },
                        system_prompt: {
                            type: "string",
                            description:
                                "System prompt to use for the chat model. If not specified, a default prompt will be used.",
                        },
                        user_prompt: {
                            type: "string",
                            description:
                                "User prompt to use for the chat model.",
                        },
                        max_pages: {
                            type: "number",
                            description:
                                "Maximum number of pages to process. If not specified, all pages will be processed.",
                        },
                        gpt_model: {
                            type: "string",
                            description: "GPT model to use for the chat model.",
                            default: "gpt-4o-mini",
                        },
                    },
                    required: ["s3_uri"],
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            error_description_code: { type: "number" },
                            message: { type: "string" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            if (!app.config.OPENAI_API_KEY) {
                reply.code(500).send({ error: "OPENAI_API_KEY is not set" });
                return;
            }
            if (!app.config.GOOGLE_APPLICATION_CREDENTIALS) {
                reply.code(500).send({
                    error: "GOOGLE_APPLICATION_CREDENTIALS is not set",
                });
                return;
            }
            if (
                !app.config.GOOGLE_PROJECT_ID ||
                !app.config.GOOGLE_PROCESSOR_ID
            ) {
                reply.code(500).send({
                    error: "GOOGLE_PROJECT_ID or GOOGLE_PROCESSOR_ID is not set",
                });
                return;
            }
            const s3_uri = request.query.s3_uri;
            let system_prompt = request.query.system_prompt;
            let user_prompt = request.query.user_prompt;
            const max_pages = request.query.max_pages;
            const gpt_model = request.query.gpt_model;
            const openai = new OpenAI({
                apiKey: app.config.OPENAI_API_KEY,
            });
            let client;

            try {
                const credentialsContent = await fs.readFile(
                    app.config.GOOGLE_APPLICATION_CREDENTIALS,
                    "utf8"
                );
                const credentials = JSON.parse(credentialsContent);

                if (!credentials.client_email) {
                    throw new Error(
                        "client_email is missing in the credentials file"
                    );
                }

                client = new DocumentProcessorServiceClient({
                    credentials: credentials,
                    projectId: app.config.GOOGLE_PROJECT_ID,
                });
            } catch (error) {
                app.log.error(
                    "Error reading or parsing Google credentials:",
                    error
                );
                reply.code(500).send({
                    error: "Failed to initialize Google client",
                    details: error.message,
                });
                return;
            }
            const s3_file = await app.s3.fetch_uri(s3_uri);
            const extracted_text = await extractTextFromPDFGoogle(
                app,
                client,
                s3_file,
                max_pages
            );

            if (!user_prompt) {
                user_prompt = await generatePrompt(extracted_text);
            }
            if (!system_prompt) {
                system_prompt =
                    "You are a helpful assistant that extracts structured information from medical documents. Always respond with only a valid JSON object, without any additional text or formatting.";
            }
            const response = await openai.chat.completions.create({
                model: gpt_model,
                messages: [
                    {
                        role: "system",
                        content: system_prompt,
                    },
                    { role: "user", content: user_prompt },
                ],
                max_tokens: 2000,
                temperature: 0.3,
            });

            let content = response.choices[0].message.content.trim();

            // Remove any markdown formatting if present
            if (content.startsWith("```json")) {
                content = content
                    .replace(/```json\n/, "")
                    .replace(/\n```$/, "");
            }

            try {
                content = sanitizeJSON(content);
                let extractedData = parse(content);
                extractedData = cleanupExtractedData(extractedData);
                app.log.info(
                    `Extracted data: ${JSON.stringify(extractedData)}`
                );
                reply.send(JSON.stringify(extractedData));
                return reply;
            } catch (error) {
                console.error("Error parsing JSON:", content);
                reply.code(500).send({ error: error.message });
                return reply;
            }
        }
    );
}
