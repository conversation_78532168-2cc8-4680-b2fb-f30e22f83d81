"use strict";
import { OpenAI } from "openai";
import { getAssistants } from "../handlers/getAssistants.js";

export default async function (app, opts) {
    app.post(
        "/run",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: `Use OpenAI assistant functions to generate a response to a user's message. from defined assistant functions
        EXAMPLE: 
        `,
                tags: ["AI"],
                summary: "Use OpenAI to generate a response to a user's messag",
                body: {
                    type: "object",
                    properties: {
                        message: {
                            type: "string",
                        },
                        assistant: {
                            type: "string",
                        },
                    },
                    required: ["message", "assistant"],
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            error_description_code: { type: "number" },
                            message: { type: "string" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            if (!app.config.OPENAI_API_KEY) {
                reply.code(500).send({ error: "OPENAI_API_KEY is not set" });
                return;
            }
            const assis = request.body.assistant;
            const amap = await getAssistants(app);
            if (!amap[assis]) {
                reply
                    .code(400)
                    .send({ error: `Assistant ${assis} not found in ${amap}` });
                return;
            }
            const assistantId = amap[assis];

            try {
                const client = new OpenAI(app.config.OPENAI_API_KEY);

                //        const assistantId = "asst_LNb0HcaTEK9HRVf0eCfeCEp5";

                const run = await client.beta.threads.createAndRun({
                    assistant_id: assistantId,
                    thread: {
                        messages: [
                            { role: "user", content: request.body.message },
                        ],
                    },
                });
                console.log(run);
                let runStatus = run.status;
                while (runStatus !== "completed") {
                    const runDetails = await client.beta.threads.runs.retrieve(
                        run.thread_id,
                        run.id
                    );
                    runStatus = runDetails.status;
                    await new Promise((resolve) => setTimeout(resolve, 500)); // wait for 1 second before checking again
                }

                const threadMessages = await client.beta.threads.messages.list(
                    run.thread_id
                );
                let result = threadMessages.data[0];
                if (result.role == "assistant") {
                    result = threadMessages.data[0]?.content[0]?.text?.value;
                }
                console.log(result);

                reply.code(200).send(result);

                return reply;
            } catch (error) {
                app.log.error(error);
                reply.code(500).send({ error: error.message });
                return reply;
            }
        }
    ),
        app.get(
            "/assistants",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description: "List all available assistants.",
                    tags: ["AI"],
                    summary: "List all available assistants.",
                    response: {
                        200: {
                            description:
                                "Successful response containing object of {assistant_name: assistant_id} pairs.",
                            type: "object",
                            additionalProperties: {
                                type: "string",
                            },
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "number" },
                                error_description_code: { type: "number" },
                                message: { type: "string" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                if (!app.config.OPENAI_API_KEY) {
                    reply
                        .code(500)
                        .send({ error: "OPENAI_API_KEY is not set" });
                    return reply;
                }
                try {
                    const assmap = await getAssistants(app);
                    reply.code(200).send(assmap);
                    return reply;
                } catch (error) {
                    reply.code(500).send({ error: error.message });
                    return reply;
                }
            }
        );
}
