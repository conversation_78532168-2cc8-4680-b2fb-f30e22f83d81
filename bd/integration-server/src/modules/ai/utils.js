import { create<PERSON><PERSON><PERSON> } from "tesseract.js";
import pdfImgConvert from "pdf-img-convert";

function sanitizeJSON(jsonString) {
    jsonString = jsonString.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    if (!jsonString.trim().endsWith("}")) {
        jsonString += "}";
    }
    jsonString = jsonString.replace(
        /("(?:\\.|[^"\\])*")|(\s*[\r\n]\s*)/g,
        function (match, string) {
            if (string) {
                return string;
            } else {
                return " ";
            }
        }
    );

    return jsonString;
}
async function generatePrompt(text) {
    const prompt = `
      Extract all relevant key-value pairs from the following medical document text.
      Include patient information, vital signs, medications, addresses, and any other important details you can find.
      Present the result as a JSON object. If a key has multiple related values, group them into a nested object or array as appropriate.
  
      Text:
      ${text}
  
      Instructions:
      1. Extract all relevant information, not just predefined fields.
      2. Use descriptive key names for extracted information.
      3. Group related information (e.g., address details, medication details) into nested objects or arrays.
      4. If a piece of information doesn't fit into a simple key-value pair, structure it appropriately.
      5. Include any dates, codes, or identifiers you find.
      6. Return only the JSON object, without any additional text or formatting.
      7. Ensure all string values are properly escaped, especially if they contain line breaks or quotes.
  
      Example format (but don't limit to just these fields, include all relevant information):
      {
        "patientInfo": {
          "name": "",
          "mrn": "",
          "dob": "",
          "age": "",
          "gender": "",
          "phone": "",
          "email": ""
        },
        "vitalSigns": {
          "height": "",
          "weight": "",
          "bmi": ""
        },
        "allergies": "",
        "medications": [
          {
            "name": "",
            "dose": "",
            "form": "",
            "instructions": ""
          }
        ],
        "address": {
          "street": "",
          "city": "",
          "state": "",
          "zipCode": ""
        },
        "encounterInfo": {
          "date": "",
          "provider": "",
          "location": ""
        },
        "diagnoses": [
          {
            "code": "",
            "description": ""
          }
        ],
        "procedures": [
          {
            "code": "",
            "description": "",
            "date": ""
          }
        ],
        "additionalInfo": {}
      }
    `;
    return prompt;
}

async function extractTextFromPDFLocal(pdfbytes) {
    const worker = await createWorker("eng");
    let extractedText = "";
    console.log("Extracting text from PDF using local OCR");
    try {
        // Convert PDF to images
        const pdfImgPages = await pdfImgConvert.convert(pdfbytes);

        // Process each page
        for (const pdfImgPage of pdfImgPages) {
            const {
                data: { text },
            } = await worker.recognize(Buffer.from(pdfImgPage));
            extractedText += text + "\n\n";
        }
    } finally {
        await worker.terminate();
    }

    return extractedText;
}

async function extractTextFromPDFGoogle(app, client, pdfbytes, pages) {
    const name = `projects/${app.config.GOOGLE_PROJECT_ID}/locations/us/processors/${app.config.GOOGLE_PROCESSOR_ID}`;
    console.log("Extracting text from PDF using Google OCR");
    if (pages) {
        console.log(`Processing only ${pages} pages`);
        // Limit the PDF to the specified number of pages
        const pdfImgPages = await pdfImgConvert.convert(pdfbytes);
        const limitedPages = pdfImgPages.slice(0, pages);
        let extractedText = "";
        for (const page of limitedPages) {
            const encodedImage = Buffer.from(page).toString("base64");
            const request = {
                name,
                rawDocument: {
                    content: encodedImage,
                    mimeType: "image/png",
                },
            };
            const [result] = await client.processDocument(request);
            extractedText += result.document.text + "\n\n";
        }

        app.log.info("Processed limited pages");
        return extractedText;
    }

    // If no page limit is specified, process the entire PDF
    const encodedImage = Buffer.from(pdfbytes).toString("base64");
    const request = {
        name,
        rawDocument: {
            content: encodedImage,
            mimeType: "application/pdf",
        },
    };

    const [result] = await client.processDocument(request);
    app.log.info(result.document.entities);
    return result.document.text;
}
function cleanupExtractedData(data) {
    Object.keys(data).forEach((key) => {
        if (typeof data[key] === "string") {
            data[key] = data[key].replace(/\s+/g, " ").trim();
        } else if (typeof data[key] === "object" && data[key] !== null) {
            data[key] = cleanupExtractedData(data[key]);
        }
    });

    return data;
}

export {
    sanitizeJSON,
    extractTextFromPDFLocal,
    extractTextFromPDFGoogle,
    generatePrompt,
    cleanupExtractedData,
};
