"use strict";
import { OpenAI } from "openai";

export async function getAssistants(app) {
    try {
        const client = new OpenAI(app.config.OPENAI_API_KEY);
        const assistants = await client.beta.assistants.list({ order: "desc" });
        app.log.info("Retrieved assistants", assistants);
        const assistantMap = assistants.data.reduce((obj, assistant) => {
            if (assistant.name) {
                obj[assistant.name] = assistant.id;
            }
            return obj;
        }, {});
        app.log.info(`Retrieved assistants: ${JSON.stringify(assistantMap)}`);
        return assistantMap;
    } catch (error) {
        app.log.error(error);
    }
}
