import fp from "fastify-plugin";
import autoload from "@fastify/autoload";
import { join } from "desm";
import { urlmap } from "./fixtures/maps.js";
import PverifyReq from "./handlers/pverifyreq.js";

async function PVerifyErrorHandler(err, req, reply) {
    if (!req.raw.url.includes("pverify")) {
        throw err;
    }
    const pverror = new Error(err);
    req.log.info("PVERIFY - PVerifyErrorHandler called " + err);
    pverror.error_code = err.error_code
        ? err.error_code
        : err.code
          ? err.code
          : null;
    pverror.error_description = err.error_description
        ? err.error_description
        : err.message;
    if (err.payer_error) {
        pverror.payer_error = err.payer_error;
    }
    reply.code(400).send(pverror);
    return reply;
}

async function pverify(app, opts) {
    app.decorate("pverify_urlmap", urlmap);
    app.decorate("pverify", new PverifyReq(opts, app));
    app.decorate("pverify_db_dir", join(import.meta.url, "db/customers/"));

    app.register(autoload, {
        dir: join(import.meta.url, "routes"),
        options: {
            prefix: opts.prefix,
        },
    });
    app.setErrorHandler(PVerifyErrorHandler);
}

export default fp(pverify);
