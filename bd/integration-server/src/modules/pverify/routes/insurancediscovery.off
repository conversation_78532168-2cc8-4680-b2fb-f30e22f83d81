"use strict";

import { urlmap } from "../fixtures/maps.js";

export default async function (app, opts) {
    app.post(
        "/insurance/discovery",
        {
            schema: {
                hide: true,
                description: `
          The discovery endpoint is used to determine the payer for a given patient.
          `,
                tags: ["PVerify"],
                summary: "Perform a Discovery request to PVERIFY.",
                //          body: {$ref: "DiscoveryRequest"},

                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        //              $ref: "DiscoveryResponse#",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {}
    ),
        app.get(
            "/insurance/discovery/:dreqid",
            {
                schema: {
                    hide: true,
                    description: `
            The discovery endpoint is used to determine the payer for a given patient.
            `,
                    tags: ["PVerify"],
                    summary: "Perform a Discovery request to PVERIFY.",
                    params: {
                        type: "object",
                        properties: {
                            dreqid: {
                                type: "string",
                                description: "Discovery Request ID",
                            },
                        },
                        required: ["dreqid"],
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            //                $ref: "DiscoveryResponse#",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "number" },
                                stack: { type: "string" },
                                error_description_code: { type: "number" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {}
        );
}
