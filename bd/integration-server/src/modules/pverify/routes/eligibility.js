import { PVerifyRequest } from "../handlers/pverifyreq.js";
import { lowercaseKeys } from "../../../utils/tools.js";

async function pverify_req(app, cdata, req_type, method = "POST") {
    const env = cdata._meta?.request_environment || "staging";
    const PVerifyReq = new PVerifyRequest(
        {
            env: env,
        },
        app
    );

    app.log.info(
        `Making request to PVerify ${env}: ${req_type} with data: ${JSON.stringify(cdata)}`
    );
    return await PVerifyReq.make_req(cdata, req_type, method);
}

export default async function (app, opts) {
    app.post(
        "/eligibility/summary",
        {
            preHandler: app.verifyBearerAuth,
            preValidation: async (request, _reply) => {
                request.body = lowercaseKeys(request.body);
            },
            schema: {
                description: `
        Eligibility summary is our 2nd gen eligibility endpoint designed to cover the vast majority of payers and return
        simplified objects with key data such as copay for your practice type. For example, when you send as practice type
        PT, we will return a Physical Therapy object with key PT benefits.

        The s3_filehash will be returned when we have a PDF report available for the request. The filehash can be used to
        retrieve the PDF report using the /eligibility/report endpoint. The filehash is a base64 encoded string that can be
        decoded to get the key to retrieve the PDF report from S3.
        `,
                tags: ["PVerify", "eligibility"],
                summary: "Perform an Eligibility Summary request to PVERIFY.",
                body: {
                    $ref: "EligibilitySummaryRequest",
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        $ref: "ClaraTxResponse#",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "string" },
                            error_description: { type: "string" },
                            payer_error: { type: "string" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            const data = Object.assign({}, request.body);
            data.customer_id = request.customer_id;
            data.customer_name = request.customer_name;
            data.customer_env_name = request.customer_env_name;
            data.mrn = data?.internalID;

            const response = await pverify_req(app, data, "eligibility_v2");
            app.log.info(
                `Response from PVERIFY api: ${JSON.stringify(response)}`
            );
            if (response && response.message) {
                app.log.error(
                    `Error making request to PVERIFY api: ${response.message}`
                );
                throw new Error(response.message);
            }
            reply.send(response);

            return reply;
        }
    ),
        app.get(
            "/eligibility/summary/:request_id",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description: `
        GetEligibilitySummary returns the response from the EligibilitySummary
        POST call using a unique request id provided in the EligibilitySummary response.
        EligibilitySummary is not supported for back office payers.`,
                    summary:
                        "Get eligibility summary response from PVERIFY given a request ID.",
                    tags: ["PVerify", "eligibility"],
                    params: {
                        type: "object",
                        properties: {
                            request_id: {
                                type: "string",
                                description: "Eligibility summary Request ID",
                            },
                        },
                        required: ["request_id"],
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "ClaraTxResponse#",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "string" },
                                error_description: { type: "string" },
                                payer_error: { type: "string" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                const request_id = request.params.request_id;
                if (!request_id) {
                    app.log.error(
                        "No request_id provided: " +
                            JSON.stringify(request.params)
                    );
                    reply
                        .code(400)
                        .send("Invalid request: No request_id provided");
                    return reply;
                }
                const d = {
                    request_id: request_id,
                    customer_id: request.customer_id,
                    customer_name: request.customer_name,
                    customer_env_name: request.customer_env_name,
                };

                const response = await pverify_req(
                    app,
                    d,
                    "eligibility_v2_response",
                    "GET"
                );
                reply.send(response);

                return reply;
            }
        ),
        app.get(
            "/eligibility/report",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description: `GetEligibilityReport returns the response from the EligibilityInquiry as a PDF
                    The filehash is a base64 encoded string that can be decoded to get the key to retrieve the PDF report from S3.
                    If request_id is provided, the response will be fetched from PVERIFY directly.
                    `,
                    summary:
                        "Get eligibility report PDF if filehash is provided return from s3 if request_id is provided return from PVERIFY.",
                    tags: ["PVerify", "eligibility"],
                    querystring: {
                        filehash: { type: "string" },
                        request_id: { type: "integer" },
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            content: {
                                "application/pdf": {
                                    schema: {
                                        type: "string",
                                        format: "binary",
                                    },
                                },
                            },
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "string" },
                                error_description: { type: "string" },
                                payer_error: { type: "string" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                const filehash = request.query.filehash;
                const request_id = request.query.request_id;
                if (!filehash && !request_id) {
                    app.log.error(
                        "No filehash or request_id provided: " +
                            JSON.stringify(request.query)
                    );
                    reply
                        .code(400)
                        .send(
                            "Invalid request: No filehash or request_id provided"
                        );
                    return reply;
                }
                await request.query;
                if (filehash) {
                    const key = Buffer.from(filehash, "base64").toString(
                        "utf-8"
                    );
                    const opts = {};
                    opts.customer_id = request.customer_id;
                    opts.customer_name = request.customer_name;
                    opts.customer_env_name = request.customer_env_name;
                    const response = await app.s3.get(key, opts);
                    reply
                        .header("Content-Type", "application/pdf")
                        .send(response.body);
                }
                if (request_id) {
                    const d = {
                        request_id: request_id,
                        customer_id: request.customer_id,
                        customer_name: request.customer_name,
                        customer_env_name: request.customer_env_name,
                    };
                    try {
                        const response = await pverify_req(
                            app,
                            d,
                            "eligibility_report",
                            "GET"
                        );
                        if (Buffer.isBuffer(response)) {
                            reply
                                .header("Content-Type", "application/pdf")
                                .send(response);
                        } else {
                            if (response && response.Error) {
                                app.log.error(
                                    `Error making request to PVERIFY api: ${response.Error}`
                                );
                                reply.code(500).send(response.Error);
                            }
                        }
                    } catch (err) {
                        reply.send(err);
                        return reply;
                    }
                }
                return reply;
            }
        ),
        app.post(
            "/eligibility/inquiry",
            {
                preHandler: app.verifyBearerAuth,
                preValidation: async (request, _reply) => {
                    request.body = lowercaseKeys(request.body);
                },
                schema: {
                    description: `
        The EligibilityInquiry endpoint is the main method used get an eligibility report 
        for a specific subscriber / dependent. This requires customers to post a request, 
        via REST to a designated URL address. Once a request is received by the service 
        an immediate response is sent for each request posted.

        Status Text	Meaning
        - Processed: We received a valid response from the clearinghouse
        - Rejected: The transaction is rejected due to bad data (ie: missing memberId)
        - Pending+:	The transaction is for back office payer and has been queued
            Pending is returned when the payer is non-EDI -- pVerify uses manual process (phone/web-login) to get results or in some
            cases uses screen scraping technology to fetch the eligibility and benefits for such payers -- the delay in getting the 
            results can be anywhere from 1 min. to 12 hours based on payer and agreed terms. These "Delayed Payers" have prefix of either BO or PL
        - Canceled:	The transaction has been canceled via API endpoint
        `,
                    tags: ["PVerify", "eligibility"],
                    summary: "Perform eligibility Inquiry from PVERIFY",
                    body: {
                        $ref: "EligibilityInquiry",
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "EligibilityInquiryResponse#",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "string" },
                                error_description: { type: "string" },
                                payer_error: { type: "string" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                try {
                    const data = Object.assign({}, request.body);
                    data.customer_id = request.customer_id;
                    data.customer_name = request.customer_name;
                    data.customer_env_name = request.customer_env_name;
                    const response = await pverify_req(
                        app,
                        data,
                        "eligibility_v1"
                    );
                    app.log.info(
                        `Response from PVERIFY api: ${JSON.stringify(response)}`
                    );
                    if (response && response.message) {
                        app.log.error(
                            `Error making request to PVERIFY api: ${response.message}`
                        );
                        throw new Error(response.message);
                    }
                    reply.send(response);
                } catch (err) {
                    reply.send(err);
                }
                return reply;
            }
        ),
        app.get(
            "/eligibility/inquiry/:request_id",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description: `This GET method is similar to the Eligibility Inquiry except that it is a GET call with the Eligibility Request ID as a parameter.
        This call allows you to get the eligibility verification result by a unique transaction ID. It is designed for use with non-EDI (back office) payers
        which require human intervention and are thus time delayed in the response.`,
                    tags: ["PVerify", "eligibility"],
                    summary:
                        "Get eligibility Inquiry response from PVERIFY given a request ID.",
                    params: {
                        type: "object",
                        properties: {
                            request_id: {
                                type: "string",
                                description: "Eligibility request ID",
                            },
                        },
                        required: ["request_id"],
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "EligibilityInquiryResponse#",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "string" },
                                error_description: { type: "string" },
                                payer_error: { type: "string" },
                            },
                        },
                    },
                },
            },

            async (request, reply) => {
                app.log.info(`Request: ${JSON.stringify(request)}`);
                reply.send("Not implemented");
                return reply;
            }
        ),
        app.post(
            "/easyeligibility",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description: `This POST call will return the Eligibility Status of a requested provider. The EasyEligibility endpoint is designed to facilitate easy access to Eligibility Information in just one step.`,
                    tags: ["PVerify", "eligibility"],
                    summary:
                        "Perform an Easy Eligibility request to PVERIFY of a requested provider.",
                    body: {
                        $ref: "EasyEligibilityRequest",
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "EasyEligibilityResponse#",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "string" },
                                error_description: { type: "string" },
                                payer_error: { type: "string" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                app.log.info(`Request: ${JSON.stringify(request)}`);
                reply.send("Not implemented");
                return reply;
            }
        ),
        app.post(
            "/easyeligibility/summary",
            {
                schema: {
                    description:
                        "The Easy Eligibility Summary call is intended to generate an Eligibility Summary Response in just one step.",
                    tags: ["PVerify", "eligibility"],
                    summary:
                        "Perform an Easy Eligibility Summary request to PVERIFY.",
                    body: {
                        $ref: "EasyEligibilitySummaryRequest",
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "EasyEligibilitySummaryResponse#",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "string" },
                                error_description: { type: "string" },
                                payer_error: { type: "string" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                app.log.info(`Request: ${JSON.stringify(request)}`);
                reply.send("Not implemented");
                return reply;
            }
        );
}
