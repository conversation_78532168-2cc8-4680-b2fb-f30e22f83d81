"use strict";

import { PVerifyRequest } from "../handlers/pverifyreq.js";

export default async function (app, opts) {
    const PVerifyReq = new PVerifyRequest(opts, app);
    app.get(
        "/payers",
        {
            schema: {
                description:
                    "Get a list of payers that are supported by PVERIFY. This list is used to determine if a payer is supported by PVERIFY.",
                tags: ["PVerify"],
                summary: "Get a list of payers supported by PVERIFY.",
                response: {
                    200: {
                        description: "Successful response",
                        type: "array",
                        items: {
                            $ref: "PayerListResponse#",
                        },
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            const r = await PVerifyReq.make_req({}, "payer_list", "GET");
            if (r instanceof Error) {
                app.log.error("Error in making request to pverify api: " + r);
                throw r;
            }
            if (r) {
                reply.send(r);
            }
            return reply;
        }
    );
}
