export const urlmap = {
    insurance_discovery: "/InsuranceDiscovery",
    insurance_discovery_response:
        "/GetInsuranceDiscoverySummaryResponse/{request_id}",
    eligibility_v2: "/EligibilitySummary",
    eligibility_v2_response: "/GetEligibilitySummary/{request_id}",
    eligibility_v1: "/EligibilityInquiry",
    eligibility_v1_response: "/GetEligibilityResponse/{request_id}",
    eligibility_report: "/Report/EligibilityPDFReport/{request_id}",
    easy_eligibility_status: "/EasyEligibility",
    easy_eligibility_summary: "/EasyEligibilitySummary",
    payer_list: "/GetAllPayers",
    payer_status: "/GetPayerStatus",
};

export const serviceTypemap = {
    1: "Medical Care",
    2: "Surgical",
    3: "Consultation",
    4: "Diagnostic X-Ray",
    5: "Diagnostic Lab",
    6: "Radiation Therapy",
    7: "Anesthesia",
    8: "Surgical Assistance",
    9: "Other Medical",
    10: "Blood Charges",
    11: "Used Durable Medical Equipment",
    12: "Durable Medical Equipment Purchase",
    13: "Ambulatory Service Center Facility",
    14: "Renal Supplies in the Home",
    15: "Alternate Method Dialysis",
    16: "Chronic Renal Disease (CRD) Equipment",
    17: "Pre-Admission Testing",
    18: "Durable Medical Equipment Rental",
    19: "Pneumonia Vaccine",
    20: "Second Surgical Opinion",
    21: "Third Surgical Opinion",
    22: "Social Work",
    23: "Diagnostic Dental",
    24: "Periodontics",
    25: "Restorative",
    26: "Endodontics",
    27: "Maxillofacial Prosthetics",
    28: "Adjunctive Dental Services",
    30: "Health Benefit Plan Coverage",
    32: "Plan Waiting Period",
    33: "Chiropractic",
    34: "Chiropractic Office Visits",
    35: "Dental Care",
    36: "Dental Crowns",
    37: "Dental Accident",
    38: "Orthodontics",
    39: "Prosthodontics",
    40: "Oral Surgery",
    41: "Routine (Preventive) Dental",
    42: "Home Health Care",
    43: "Home Health Prescriptions",
    44: "Home Health Visits",
    45: "Hospice",
    46: "Respite Care",
    47: "Hospital",
    48: "Hospital - Inpatient",
    49: "Hospital - Room and Board",
    50: "Hospital - Outpatient",
    51: "Hospital - Emergency Accident",
    52: "Hospital - Emergency Medical",
    53: "Hospital - Ambulatory Surgical",
    54: "Long Term Care",
    55: "Major Medical",
    56: "Medically Related Transportation",
    57: "Air Transportation",
    58: "Cabulance",
    59: "Licensed Ambulance Licensed Ambulance",
    60: "General Benefits",
    61: "In-vitro Fertilization",
    62: "MRI/CAT Scan",
    63: "Donor Procedures",
    64: "Acupuncture",
    65: "Newborn Care",
    66: "Pathology",
    67: "Smoking Cessation",
    68: "Well Baby Care",
    69: "Maternity",
    70: "Transplants",
    71: "Audiology Exam",
    72: "Inhalation Therapy",
    73: "Diagnostic Medical",
    74: "Private Duty Nursing",
    75: "Prosthetic Device",
    76: "Dialysis",
    77: "Otological Exam",
    78: "Chemotherapy",
    79: "Allergy Testing",
    80: "Immunizations",
    81: "Routine Physical",
    82: "Family Planning",
    83: "Infertility",
    84: "Abortion",
    85: "AIDS",
    86: "Emergency Services",
    87: "Cancer",
    88: "Pharmacy",
    89: "Free Standing Prescription Drug",
    90: "Mail Order Prescription Drug",
    91: "Brand Name Prescription Drug",
    92: "Generic Prescription Drug",
    93: "Podiatry",
    94: "Podiatry - Office Visits",
    95: "Podiatry - Nursing Home Visits",
    96: "Professional (Physician)",
    97: "Anesthesiologist",
    98: "Professional (Physician) Visit - Office",
    99: "Professional (Physician) Visit - Inpatient",
    A0: "Professional (Physician) Visit - Outpatient",
    A1: "Professional (Physician) Visit - Nursing Home",
    A2: "Professional (Physician) Visit - Skilled Nursing Facility",
    A3: "Professional (Physician) Visit - Home",
    A4: "Psychiatric",
    A5: "Psychiatric - Room and Board",
    A6: "Psychotherapy",
    A7: "Psychiatric - Inpatient",
    A8: "Psychiatric - Outpatient",
    A9: "Rehabilitation",
    AA: "Rehabilitation - Room and Board",
    AB: "Rehabilitation - Inpatient",
    AC: "Rehabilitation - Outpatient",
    AD: "Occupational Therapy",
    AE: "Physical Medicine",
    AF: "Speech Therapy",
    AG: "Skilled Nursing Care",
    AH: "Skilled Nursing Care - Room and Board",
    AI: "Substance Abuse",
    AJ: "Alcoholism",
    AK: "Drug Addiction",
    AL: "Vision (Optometry)",
    AM: "Frames",
    AN: "Routine Exam",
    AO: "Lenses",
    AQ: "Nonmedically Necessary Physical",
    AR: "Experimental Drug Therapy",
    B1: "Burn Care",
    B2: "Bran Name Prescription Drug - Formulary",
    B3: "Bran Name Prescription Drug - Non -Formulary",
    BA: "Independent Medical Evaluation",
    BB: "Partial Hospitalization (Psychiatric)",
    BC: "Day Care (Psychiatric)",
    BD: "Cognitive Therapy",
    BE: "Massage Therapy",
    BF: "Pulmonary Rehabilitation",
    BG: "Cardiac Rehabilitation",
    BH: "Pediatric",
    BI: "Nursery",
    BJ: "Skin",
    BK: "Orthopedic",
    BL: "Cardiac",
    BM: "Lymphatic",
    BN: "Gastrointestinal",
    BP: "Endocrine",
    BQ: "Neurology",
    BR: "Eye",
    BS: "Invasive Procedures",
    BT: "Gynecological",
    BU: "Obsterical",
    BV: "Obsterical/Gynecological",
    BW: "Mail Order Prescription Drug : Brand Name",
    BX: "Mail Order Prescription Drug : Generic",
    BY: "Physician Visit - Office : Sick",
    BZ: "Physician Visit - Office : Well",
    C1: "Coronary Care",
    CA: "Private Duty Nursing - Inpatient",
    CB: "Private Duty Nursing - Home",
    CC: "Surgical Benefits - professional (physician)",
    CD: "Surgical Benefits - Facility",
    CE: "Mental Health Provider - Inpatient",
    CF: "Mental Health Provider - Outpatient",
    CG: "Mental Health Facility - Inpatient",
    CH: "Mental Health Facility - Outpatient",
    CI: "Substance Abuse Facility - Inpatient",
    CJ: "Substance Abuse Facility - Outpatient",
    CK: "Screening X - Ray",
    CL: "Screening Laboratory",
    CM: "Mammomogram, High Risk Patient",
    CN: "Mammomogram, Low Risk Patient",
    CO: "Flu Vaccination",
    CP: "Eyewear And Eywear Accessories",
    CQ: "Case Management",
    DG: "Dermatology",
    DM: "Durable Medical Equipment",
    DS: "Diabetic Supplies",
    GF: "Generic Presciption Drug - Formulary",
    GN: "Generic Presciption Drug - Non -Formulary",
    GY: "Allergy",
    IC: "Intesive Care",
    MH: "Mental Health",
    NI: "Neonatal Intesive Care",
    ON: "Oncology",
    PT: "Physical Therapy",
    PU: "Pulmonary",
    RN: "Renal",
    RT: "Residential Psychiatric Treatment",
    TC: "Transitional Care",
    TN: "Transitional Nursery Care",
    UC: "Urgent Care",
};

export const practice_type_map = {
    1: "DME",
    2: "Chemotherapy",
    3: "VisionOptometry",
    4: "Physical Therapy",
    5: "Primary Care",
    6: "Specialist - Office",
    7: "Mental Health",
    8: "Surgical -Office",
    9: "Urgent Care",
    10: "Diagnostic Lab",
    11: "Specialist(Office)_Surgical(Office)_Diagnostic Lab",
    12: "ASC Facility",
    13: "Chiropractic",
    14: "MRI/CAT Scan",
    15: "X-Ray",
    16: "Speech Therapy",
    17: "Occupational Therapy",
    18: "ASC_Specialist(Office)_VisionOptometry",
    19: "Specialist(Office)_Chemotherapy",
    20: "Physical Therapy_Chiropractic",
    21: "Physical Therapy_DME",
    22: "Physical Therapy_Occupational Therapy_DME",
    23: "Physical Therapy_Occupational Therapy_Speech Therapy",
    24: "Specialist(Office)_VisionOptometry",
    25: "Specialist(Office)_DME",
    26: "Specialist(Office)_Surgical(Office)",
    27: "Specialist(Office)_ASC Facility",
    28: "Physical Therapy_XRay",
    29: "XRay_MRI/CAT Scan",
    30: "Speech Therapy_Occupational Therapy",
    31: "Emergency Services",
    32: "Routine Physical",
    33: "Podiatry Office",
    34: "Professional - Outpatient Physician",
    35: "Anesthesia",
    36: "Substance Abuse",
    37: "Substance Abuse Facility-InPatient",
    38: "Substance Abuse Facility-OutPatient",
    39: "Telemedicine",
    40: "Flu Vaccination",
    41: "Mental Health_Sub Abuse Professional",
    42: "Mental Health_Sub Abuse_In&OutFacility",
    43: "Sub Abuse_In&OutFacility",
    44: "Mental Health_SubAbuse_In-NetFacility",
    45: "Specialist(Office)_ASC_Professional OutPatient",
    46: "ASC Facility_Professional OutPatient",
    47: "General_Office_Benefits(30)",
    48: "Podiatry_XRay_DME",
    49: "Podiatry_DME",
    50: "Hospital-InPatient",
    51: "Emergency Services_Hospital-InPatient",
    52: "Speech Therapy_Occupational Therapy_MentalHealth",
    53: "DME_Diagnostic Lab",
    54: "Pharmacy",
    55: "Specialist(Office)_Pharmacy",
    56: "Physical Therapy_Occupational Therapy_Mental Health",
    57: "Specialist(Office)_DME_ASC Facility",
    58: "Specialist(Office)_DME_Surgical(Office)",
    59: "Specialist(Office)_DME_Hospital-OutPatient",
    60: "Hospital-OutPatient",
    61: "Physical Therapy_Occupational Therapy",
    62: "Specialist(Office)_Surgery(Office)_DME",
    63: "SpecialistOffice_DME_X-Ray",
    64: "SpecialistOffice_Physical Therapy",
    65: "Telemedicine-Primary Care",
    66: "Telemedicine-Specialist Services",
    67: "Telemedicine-Urgent Care",
    68: "Telemedicine-Physical Therapy",
    69: "Telemedicine-Mental Health",
    70: "Psychotherapy",
    71: "Telemedicine Primary + Telemedicine Specialist",
    72: "Telemedicine Primary + Telemedicine Specialist+Telemedicine Urgent care",
    73: "Specialist(Office)_Psychotherapy",
    74: "Hospital-Outpatient_Surgical(Office)_Specialist(Office)",
    75: "SNF(AG)",
    76: "SNF-Room_and_Board(AH)",
    77: "SNF_SNF-Room_and_Board",
    78: "Specialist(Office)_Mental Health",
    79: "Specialist(office)_Primary Care_Chiropractic",
    80: "Occupational Therapy_DME",
    81: "Professional (Physician) Visit - Inpatient(99)",
    82: "Home Health Care(42)",
    83: "Hospice(45)",
    84: "Home Health (42)_Hospice (45)",
    85: "Mental Health_Telemedicine- Mental Health (MH)",
    86: "Allergies",
    87: "Diagnostic Medical",
    88: "Maternity",
    89: "Immunizations",
    90: "Primary Care_Diagnostic Lab",
    91: "Routine Physical_Immunizations_Diagnostic Medical",
    92: "Physical Therapy & Telemedicine physical therapy",
    93: "Telemedicine-Occupational Therapy(AD)",
    94: "Telemedicine-Speech Therapy(AF)",
    95: "Occupational Therapy & Telemedicine Occupational Therapy",
    96: "Speech Therapy & Telemedicine Speech Therapy",
    97: "PrimaryCare_Immunizations_Diagnostic Lab",
    98: "PrimaryCare_Immunizations_Diagnostic Medical",
    99: "Physical Therapy_Mental Health",
    100: "Radiation Therapy",
    101: "Specialist(Office)_Chemotherapy_RadiationTherapy",
    102: "Infertility(83)",
    103: "Allergy Testing(79)",
    104: "Specialist Office(98)_Allergy Testing(79)",
    105: "Specialist Office(98)_Infertility(83)",
    106: "In-vitro Fertilization(61)",
    107: "In-vitro Fertilization(61)_Infertility(83)_Specialist Office(98)",
    108: "Psychiatric (A4)",
    109: "Mental Health_Telemedicine-Mental Health(MH)_Psychotherapy(A6)_Psychiatric (A4)",
    110: "Primary Care (30)_Urgent Care (UC)_Diagnostic Lab (5)",
    111: "SNF(AG)_SNF-RoomAndBoard(AH)_PhysicalTherapy(PT)",
    112: "Acupuncture(64)",
    113: "Acupuncture(64)_PhysicalTherapy(PT)",
    114: "Gynecological(BT)",
    115: "Obstetrical(BU)",
    116: "Obstetrical/Gynecological(BV)",
    117: "Family Planning(82)",
    118: "Surgical Benefits-Professional Physician(CC)",
    119: "Surgical Benefits-Facility(CD)",
    120: "GeneralOfficeBenefits_Maternity",
    121: "Dialysis(76)",
    122: "Mental Health Facility-Outpatient(CH)",
    123: "SpecialistOffice_DME_X-Ray_Surgical(office)",
    124: "Mental Health_SubAbuse_Out Net Facility",
    125: "Mental Health_Mental Health OP Facility _Sub_Abuse_In&Out Net Facility",
    126: "Mental Health (MH)_Mental Health Facility Outpatient(CH)",
    127: "Telemedicine-Primary Care (30)_Diagnostic Medical (73)_Allergy Testing (79)",
    128: "Primary Care(30)_Routine Physical(81)",
    129: "XRay (4)_VisionOptometry (AL)",
    130: "Diagnostic Medical(73)_Allergy Testing(79)_Specialist Office Visit(98)",
};

export const practice_type_map_test = {
    3: "DME",
    12: "Physical Therapy",
    18: "SpecialistOV-Professional Services",
    27: "OV_DxLab_Sx(Office)",
    38: "PT_DME",
    40: "PT_OT_ST",
};

export const response_code_map = {
    0: "Processed",
    1: "Rejected",
    2: "NoFunds",
    3: "Pending",
    4: "New",
};
