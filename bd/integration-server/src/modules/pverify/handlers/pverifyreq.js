"use strict";
import fetch from "node-fetch";
import { PVerifyDB } from "../../../database/pvdb.js";
import { PgIntegrationsDB } from "../../../database/pg_integrationsdb.js";
import { lowercaseKeys } from "../../../utils/tools.js";

export class PVerifyRequest {
    /**
     * Represents a PVerifyReq object.
     * @constructor
     * @param {Object} opts - The options for the PVerifyReq object.
     * @param {Object} app - The application object.
     */
    constructor(opts, app) {
        this.app = app;
        if (!opts) {
            opts = {};
        }
        opts.env = opts.env || "staging";
        this.pverify_config = {
            staging: {
                client_id: this.app.config.PVERIFY_STAGING_CLIENT_ID,
                client_secret: this.app.config.PVERIFY_STAGING_CLIENT_SECRET,
                oauth_url: this.app.config.PVERIFY_STAGING_OAUTH_URL,
                base_url: this.app.config.PVERIFY_STAGING_BASE_URL,
            },
            prod: {
                client_id: this.app.config.PVERIFY_PROD_CLIENT_ID,
                client_secret: this.app.config.PVERIFY_PROD_CLIENT_SECRET,
                oauth_url: this.app.config.PVERIFY_PROD_OAUTH_URL,
                base_url: this.app.config.PVERIFY_PROD_BASE_URL,
            },
        };
        if (!this.pverify_config[opts.env].client_id) {
            app.log.error("PVERIFY_CLIENT_ID is not set");
            return;
        }
        if (!this.pverify_config[opts.env].client_secret) {
            app.log.error("PVERIFY CLIENT ID  is not set");
            return;
        }
        this.baseurl = this.pverify_config[opts.env].base_url;
        this.client_id = this.pverify_config[opts.env].client_id;
        this.client_secret = this.pverify_config[opts.env].client_secret;
        this.oauth_url = this.pverify_config[opts.env].oauth_url;
        this.app.log.info(
            `PVerify config: ${JSON.stringify(this.pverify_config[opts.env])}`
        );
    }

    /**
     * Fetches the report from PVERIFY for the given request ID.
     * @param {string} reqid - The request ID for which to fetch the report.
     * @returns {Promise<Buffer>} - A promise that resolves to the PDF data of the report.
     */
    async fetch_report(reqid) {
        try {
            if (!reqid) {
                this.app.log.error("No request_id provided to fetch_report");
                return;
            }
            const authToken = await this.init_auth();
            if (!authToken) {
                this.app.log.error(
                    "There is no authtoken, check request to PVERIFY for auth."
                );
                return;
            }
            const headers = {
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
                "Client-API-Id": this.client_id,
                "Client-Secret": this.client_secret,
            };
            const options = {
                method: "GET",
                headers: headers,
            };
            const url = `https://premium.pverify.com/Report/EligibilityPDFReport/${reqid}`;
            const resp = await fetch(url, options);

            if (resp.headers.get("content-type").includes("application/json")) {
                const json = await resp.json();
                if (json.Error === "Error during report generation") {
                    this.app.log.error(
                        "Error during report generation, this is usually 404 , no report found."
                    );
                    return;
                }
            }

            const pdfdata = await resp.buffer();
            return pdfdata;
        } catch (err) {
            this.app.log.error("Error fetching report from PVERIFY: " + err);
            return;
        }
    }

    /**
     * Initializes the authentication process by obtaining an access token from the PVERIFY API.
     * @returns {Promise<string|Error>} A promise that resolves to the access token if successful, or an Error object if there was an error obtaining the access token.
     */
    async init_auth() {
        const body = {
            client_id: this.client_id,
            client_secret: this.client_secret,
            grant_type: "client_credentials",
        };

        // Convert the body to URL-encoded format
        let formBody = [];
        for (const property in body) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(body[property]);
            formBody.push(encodedKey + "=" + encodedValue);
        }
        formBody = formBody.join("&");

        const headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Client-API-Id": this.client_id,
        };
        const options = {
            method: "POST",
            headers: headers,
            body: formBody,
        };
        const url = this.oauth_url;
        const resp = await fetch(url, options);
        const data = await resp.json();
        if (data && data.access_token) {
            return data.access_token;
        } else {
            return Error(
                `Error getting access token from PVERIFY  api. Status: ${resp.status} ${resp.statusText}`
            );
        }
    }

    /**
     * Makes a request to the PVERIFY API.
     * @param {Object} data - The data to be sent in the request.
     * @param {string} ep - The endpoint of the API.
     * @param {string} [op="POST"] - The HTTP method to be used (default is "POST").
     * @returns {Promise<Object>} - A promise that resolves to the response data.
     * @throws {Error} - If there is an error in the response from the PVERIFY API.
     */
    async make_req(data, ep, op = "POST") {
        const authToken = await this.init_auth();
        const customer_id = data.customer_id;
        const customer_name = data.customer_name;
        const customer_env_name = data.customer_env_name;
        let dbfhash, s3_exists, fhash, rdata, err;

        // Create db instances outside try/catch for wider scope
        const sqliteDb = new PVerifyDB(this.app, {
            customer_id: customer_id,
            customer_name: customer_name,
        });

        const pgDb = new PgIntegrationsDB(this.app, {
            customer_id: customer_id,
            customer_name: customer_name,
            customer_env_name: customer_env_name,
        });

        delete data.customer_id;
        delete data.customer_name;
        delete data.customer_env_name;

        if (!authToken) {
            this.app.log.error(
                "There is no authtoken, check request to PVERIFY for auth."
            );
            return;
        }
        const headers = {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
            "Client-API-Id": this.client_id,
            "Client-Secret": this.client_secret,
        };
        const options = {
            method: op,
            headers: headers,
        };
        let url = this.baseurl + this.app.pverify_urlmap[ep];
        if (op === "POST") {
            options.body = JSON.stringify(data);
        }

        if (op === "GET" && url.includes("{request_id}") && data.request_id) {
            url = url.replace("{request_id}", data.request_id);
        }
        if (
            ep === "eligibility_report" &&
            (data.request_id || data.pv_request_id)
        ) {
            url = `https://premium.pverify.com/Report/EligibilityPDFReport/${data.request_id ? data.request_id : data.pv_request_id}`;
        }
        this.app.log.info(
            `Making request to PVERIFY' api: ${url} with data: ${JSON.stringify(data)}`
        );
        const resp = await fetch(url, options);
        const contentType = resp.headers.get("content-type");

        if (contentType && contentType.includes("application/json")) {
            rdata = await resp.json();
            rdata = lowercaseKeys(rdata);
            this.app.log.info(`[PVerify Response] - ${JSON.stringify(rdata)}`);
        } else if (contentType && contentType.includes("application/pdf")) {
            rdata = await resp.buffer();
            return rdata;
        } else {
            // deal with this better
            this.app.log.error(
                `Bad Response from pverify api: ${resp.status} ${resp.statusText} ${resp.url} Content-Type: ${contentType}`
            );
            return;
        }
        await rdata;
        if ((rdata && rdata.ProcessedWithError) || rdata.ErrorCode) {
            this.app.log.error(
                `Error in response from PVERIFY api: ${JSON.stringify(rdata)}`
            );
            err = new Error(rdata.ErrorDescription);
            err.statusCode = 400;
            if (rdata.APIResponseCode == 1 || rdata.ProcessedWithError) {
                // This was rejected by payer, return the EDIErrorMessage and ExceptionNotes
                err.error_code = rdata.ErrorCode;
                err.message = rdata.ErrorDescription;
                err.error_description = rdata.ErrorDescription;
                err.payer_error = rdata.EDIErrorMessage
                    ? rdata.EDIErrorMessage
                    : rdata.ExceptionNotes;
            }
        }

        const dbd = {};
        const mrn = data.mrn
            ? data.mrn
            : data.internalid
              ? data.internalid
              : rdata.internalid
                ? rdata.internalid
                : null;
        dbd.customer_id = customer_id;
        dbd.customer_name = customer_name;
        dbd.customer_env_name = customer_env_name;
        dbd.request_json = JSON.stringify(data);
        dbd.response_json = JSON.stringify(rdata);
        dbd.pv_request_id = rdata.requestid
            ? rdata.requestid
            : data.request_id
              ? data.request_id
              : "";
        dbd.request_type = ep;
        dbd.member_type = "self";
        dbd.clara_mrn = mrn;

        if (rdata && !err) {
            dbd.pv_response_code = rdata.APIResponseCode;
            let dep;
            this.app.log.debug(
                `Received response from PVERIFY api: ${JSON.stringify(rdata)}`
            );
            const member_id =
                rdata.demographicinfo?.subscriber?.identification.filter(
                    (item) => item.type === "Member ID"
                )[0]?.code || "";

            dbd.member_id = member_id;
            if (rdata.VerificationType == "Dependent Verification") {
                // this was run as a dependent lookup
                dep =
                    rdata.DemographicInfo?.Dependent?.Relationship ||
                    "dependent";
                dbd.member_type = dep;
            }
            let fname = `eligibility_${rdata.requestid}`;
            let base_path = `pverify/${customer_name}/${mrn}`;
            if (!dbd.clara_mrn) {
                this.app.log.info(
                    "No MRN provided, storing in unknown folder for request_id: " +
                        rdata.requestid
                );
                base_path = `pverify/${customer_name}/unknown`;
            }
            if (dep) {
                fname = `${fname}_${member_id}_${dep}.pdf`;
            }
            if (member_id) {
                fname = `${fname}_${member_id}.pdf`;
            }
            const full_path = `${base_path}/${fname}`;
            fhash = Buffer.from(full_path).toString("base64");

            // Use appropriate DB for filehash check
            if (this.app.config.USE_POSTGRES === "true") {
                try {
                    dbfhash = await pgDb.check_filehash(fhash);
                } catch (pgError) {
                    this.app.log.warn(
                        "PostgreSQL check failed, falling back to SQLite",
                        pgError
                    );
                    dbfhash = sqliteDb.check_filehash(fhash);
                }
            } else {
                dbfhash = sqliteDb.check_filehash(fhash);
            }

            s3_exists = await this.app.s3.fileExists(full_path);

            this.app.log.info(
                `Checked if file exists in s3: ${full_path} ${s3_exists.exists}`
            );
            if (!dbfhash) {
                // There is no filehash in the db in the past three months that matches , if the APIResponse code was 0,
                // We should be able to immediately fetch and store the pdf and return the filehash for the client to download from the server
                if (rdata.APIResponseCode == 0) {
                    this.app.log.info(
                        `No filehash found in db for ${fhash}, fetching pdf from PVERIFY api`
                    );
                    const pdf = await this.fetch_report(rdata.requestid);

                    if (!pdf || !Buffer.isBuffer(pdf)) {
                        this.app.log.error("No pdf returned from PVERIFY api");
                    } else {
                        const pdf_opts = {
                            filename: fname,
                            base_path: base_path,
                            filehash: fhash,
                        };
                        try {
                            const res = await this.app.s3.put(pdf_opts, pdf);

                            if (res) {
                                dbd.s3_filehash = fhash;
                                dbd.filehash = fhash;
                            }
                        } catch (err) {
                            this.app.log.error(
                                "Error uploading pdf to s3: " + err
                            );
                        }
                    }
                }
            }
        }
        if (!dbd.member_id) {
            dbd.member_id = data.subscriber?.memberid
                ? data.subscriber.memberid
                : "";
        }
        dbd.errors = rdata.errors
            ? JSON.stringify(rdata.errors)
            : err
              ? JSON.stringify(err)
              : null;

        // Check if PostgreSQL is enabled via config
        const usePg = this.app.config.USE_POSTGRES === "true";

        if (usePg) {
            try {
                const pgDb = new PgIntegrationsDB(this.app, {
                    customer_id: customer_id,
                    customer_name: customer_name,
                    customer_env_name: customer_env_name,
                });

                // Ensure schemas exist before inserting
                await pgDb.ensureSchema();

                // Log the data being sent to PostgreSQL
                this.app.log.debug(
                    "Attempting PostgreSQL insert with data:",
                    JSON.stringify(dbd, null, 2)
                );

                await pgDb.insert_new_pverify_request(dbd);
                this.app.log.info(
                    "Successfully inserted PVerify request into PostgreSQL"
                );
            } catch (pgError) {
                this.app.log.error(
                    "PostgreSQL insert failed with error:",
                    pgError.message,
                    "\nStack:",
                    pgError.stack,
                    "\nData:",
                    JSON.stringify(dbd, null, 2)
                );
                this.app.log.warn("Falling back to SQLite");

                sqliteDb.insert_new(dbd);
            }
        } else {
            // Use existing SQLite implementation
            const res = sqliteDb.insert_new(dbd);
            if (res && res.changes) {
                this.app.log.info(
                    "Inbound message stored in sqlitedb: " + JSON.stringify(res)
                );
            }
            // Use SQLite for filehash check
            dbfhash = sqliteDb.check_filehash(fhash);
        }

        if (err) {
            throw err;
        } else {
            let request_json_data;
            try {
                request_json_data = JSON.parse(options.body);
            } catch (e) {
                this.app.log.error(`Error parsing request body: ${e}`);
                request_json_data = options.body;
            }

            this.app.log.info(`filehash is in db: ${fhash} ${dbfhash}`);
            const full_path = Buffer.from(fhash, "base64").toString("ascii");
            s3_exists = await this.app.s3.fileExists(full_path);
            this.app.log.info(
                `Checked if file exists in s3: ${full_path} ${s3_exists.exists}`
            );

            return {
                request_json_data: request_json_data,
                response_json_data: rdata,
                s3_filehash: s3_exists.exists && dbfhash ? fhash : null,
            };
        }
    }
}

export default PVerifyRequest;
