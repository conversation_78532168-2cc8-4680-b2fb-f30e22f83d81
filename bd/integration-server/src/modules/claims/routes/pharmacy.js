"use strict";
import { ChangeRequestPharm } from "../handlers/changereq.js";
import { PowerlineRequestPharm } from "../handlers/powerlinereq.js";
import { Request, Response } from "../handlers/pharmacy/NcpdpTX.js";

/**
 * Sets the switch for handling NCPDP claims based on the request body.
 *
 * @param {Object} app - The application object.
 * @param {Object} req - The request object.
 * @returns {Promise<Object>} - The request handler object.
 */

async function set_switch(app, req) {
    let ncpdp_switch = "change";
    let req_handler;
    const opts = {};
    opts.rtype = "pharmacy";
    opts.env = req.body?._meta?.request_environment || "staging";
    app.log.info(`NCPDP environment: ${opts.env}`);
    if (
        req.body?._meta &&
        req.body?._meta?.ncpdp_claims_processor === "powerline"
    ) {
        req_handler = new PowerlineRequestPharm(opts, app);
        ncpdp_switch = "powerline";
    } else {
        req_handler = new ChangeRequestPharm(opts, app);
    }

    app.log.info(`NCPDP Claims handler: ${ncpdp_switch}`);
    return req_handler;
}
/**
 * Reverses a claim.
 *
 * @param {Object} app - The application object.
 * @param {Object} CReq - The CReq object.
 * @param {Object} data - The claim data.
 * @returns {Promise<Object>} - The response object.
 */

async function reverse_claim(app, CReq, data) {
    const req = new Request();
    let revresp;
    const revdata = {};
    const rd = {};

    Object.assign(revdata, data);
    app.log.info("Reversing claim for : " + JSON.stringify(revdata.claim));
    // Change header from B1 to B2 to process the reversal TX
    revdata.header.transaction_code = "B2";
    const remove_fields = {
        insurance: [
            "card_holder_first_name", // cc
            "card_holder_last_name", // cd
            "home_plan", // ce
            "plan_id", // fo
            "elig_clar_code", // c9
            "person_code", // c3
            "pt_rel_code", // c6
            "dr_accept_indicator", // 361-2d
            "mcd_indicator", // 360-2b
            "mcd_id_no", // 115-n5
            "partd_facility", // 997-G2
        ],
        claim: [
            "proc_mod_code_count", // se
            "proc_mod_code", // er
            "quantity_dispensed", // e7
            "quantity_prescribed", // et
            "day_supply", // d5
            "compound_code", // d6
            "daw_code", // d8
            "date_rx_written", // de
            "number_of_refills_authorized", // df
            "sub_clar_code_count", // nx
            "sub_clar_code", // dk
            "rx_origin_code", // dj
            "sp_pk_indicator", // dt
            "sched_rx_no", // ek
            "unit_of_measure", // 28
            "level_of_service", // di
            "pa_type_code", // eu
            "pa_no_submitted", // ev
            "inter_auth_type_id", // ew
            "inter_auth_id", // ex
            "pt_assign_indicator", // 391-mt
            "admin_route", // 995-e2
        ],
        pricing: [
            "ing_cst_sub", // d9
            "disp_fee_sub", // dc
            "pt_pd_amt_sub", // dx
            "flat_tax_amt", // ha
            "sales_tax", // ge
            "sales_tax_basis", // he
            "per_sales_tax_rate_used", // je
            "u_and_c_charge", // dq
            "cost_basis", // dn
        ],
        dur: {
            subform_dur: ["co_agt_id_qualifier", "co_agt_id"],
        },
        patient: {},
        pharmacy: {},
        prescriber: {},
        clinical: {},

        cob: {
            _directFields: ["pt_resp_amt_count"],
            subform_opayer: [
                "other_id_qualifier",
                "other_id",
                "other_date",
                "internal_control_number",
            ],
            subform_resp: [],
        },
    };

    function removeFieldsFromObject(obj, fields) {
        if (Array.isArray(obj)) {
            // Map items, remove fields from each, and filter out any null results
            return obj
                .map((item) => removeFieldsFromObject(item, fields))
                .filter((item) => item !== null);
        } else if (typeof obj === "object" && obj !== null) {
            // If fields is empty, return null to indicate the object should be removed
            if (Array.isArray(fields) && fields.length === 0) {
                return null;
            }
            // Create a new object with only the fields we want to keep
            const newObj = {};
            for (const key in obj) {
                if (!fields.includes(key)) {
                    newObj[key] = obj[key];
                }
            }
            return Object.keys(newObj).length > 0 ? newObj : null;
        }
        return obj;
    }

    for (const [segment, fieldsToRemove] of Object.entries(remove_fields)) {
        if (revdata[segment]) {
            // Check if the instruction is to remove the entire segment (empty object)
            if (
                typeof fieldsToRemove === "object" &&
                !Array.isArray(fieldsToRemove) &&
                Object.keys(fieldsToRemove).length === 0
            ) {
                delete revdata[segment];
                continue; // Move to the next segment
            }

            if (
                typeof fieldsToRemove === "object" &&
                !Array.isArray(fieldsToRemove)
            ) {
                // Handle mixed direct fields and sub-segments
                // 1. Remove direct fields if specified
                if (
                    fieldsToRemove._directFields &&
                    Array.isArray(fieldsToRemove._directFields)
                ) {
                    for (const fieldKey of fieldsToRemove._directFields) {
                        if (revdata[segment].hasOwnProperty(fieldKey)) {
                            delete revdata[segment][fieldKey];
                        }
                    }
                }

                // 2. Process sub-segments (excluding _directFields key)
                for (const [subSegment, subFields] of Object.entries(
                    fieldsToRemove
                )) {
                    if (subSegment === "_directFields") continue; // Skip our special key

                    if (revdata[segment][subSegment]) {
                        const result = removeFieldsFromObject(
                            revdata[segment][subSegment],
                            subFields
                        );
                        if (result === null) {
                            delete revdata[segment][subSegment];
                        } else {
                            revdata[segment][subSegment] = result;
                        }
                    }
                }

                // If the segment is now empty after removing direct fields and potentially sub-segments, remove it
                if (Object.keys(revdata[segment]).length === 0) {
                    delete revdata[segment];
                }
            } else if (Array.isArray(fieldsToRemove)) {
                // Process fields directly if fieldsToRemove is an array
                const result = removeFieldsFromObject(
                    revdata[segment],
                    fieldsToRemove
                );
                if (result === null) {
                    delete revdata[segment];
                } else {
                    revdata[segment] = result;
                }
            }
        }
    }

    rd.request_json_data = revdata;
    const rparsed = req.fromJSON(revdata);
    const rrawd0 = rparsed.toString("utf8");
    const rb64data = Buffer.from(rrawd0).toString("base64");

    rd.request_d0_raw = rrawd0;
    rd.request_d0_b64 = rb64data;
    rd.customer_id = revdata._meta?.customer_id;
    rd.customer_name = revdata._meta?.customer_name;
    rd.customer_env_name = revdata._meta?.customer_env_name;
    app.log.info(`Transmitting reversal: ${JSON.stringify(rd)}`);
    if (rparsed && rparsed.transaction_groups && rrawd0) {
        try {
            revresp = await CReq.make_req(rd);
            if (
                revresp?.response_json_data?.response_stat
                    ?.transaction_response_status == "A"
            ) {
                app.log.info(
                    "Successfully reversed claim: " +
                        JSON.stringify(
                            revresp.response_json_data?.response_stat
                        )
                );
            } else {
                app.log.error(
                    "Error reversing claim: " + JSON.stringify(revresp)
                );
                revresp = {
                    error: "Error reversing claim",
                    response: revresp,
                    request: rd,
                };
            }
        } catch (e) {
            app.log.error("Error reversing claim: " + JSON.stringify(e));
            revresp = { error: e.message };
        }
    } else {
        app.log.error(
            "Error reversing claim, reversal tx could not be parsed to D0: " +
                JSON.stringify(rd)
        );
        revresp = {
            error: "Error reversing claim, reversal tx could not be parsed to D0",
            request: rd,
        };
    }

    return revresp;
}
export default async function (app, opts) {
    app.post(
        "/pharm",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: `This is the inbound route for all Pharmacy NCPDP Claim messages. It will accept JSON and translate it to the NCPDP d0 edi format
                and submit it to the Change or Powerline processor.
                You can pass parameters in _meta that will be used to determine the processor to use, patient MRN and is_test, if is_test:true is passed and the B1 claim is successful,
                it will be reversed automatically with a B2 claim and the reversal information will be returned in the response`,
                tags: ["claims", "pharmacy"],
                summary:
                    "Submit a NCPDP D0 message to Change or Powerline processor",
                body: { $ref: "NCPDPClaimsRequest" },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        $ref: "ClaraClaimTxResponse",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                            error_description: { type: "string" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            const CReq = await set_switch(app, request);
            const rdata = request.body;
            if (!rdata) {
                app.log.error("invalid request: " + JSON.stringify(rdata));
                throw new Error("Invalid request");
            }
            app.log.info("Inbound message received: " + JSON.stringify(rdata));

            const req = new Request();
            let resp;

            const is_test = rdata?._meta?.is_test === true ? true : false;
            app.log.debug(`is_test ?: ${is_test}`);
            // temp force to is_test
            // const is_test = false;
            const d = {};
            const parsed = req.fromJSON(rdata);
            const rawd0 = parsed.toString("utf8");
            const cust_env = request.customer_env_name;

            d.request_json_data = request.body;
            d.request_d0_raw = rawd0;
            d.customer_id = request.customer_id;
            d.customer_name = request.customer_name;
            d.customer_env_name = request.customer_env_name;

            if (parsed && parsed.transaction_groups && rawd0) {
                app.log.debug(
                    `Parsed D0 ${JSON.stringify(rawd0)} from json: ${JSON.stringify(
                        parsed
                    )}`
                );
                const b64data = Buffer.from(rawd0).toString("base64");
                d.request_d0_b64 = b64data;
                resp = await CReq.make_req(d);
            }
            if (resp) {
                if (
                    is_test &&
                    resp?.response_json_data?.response_status == "A" &&
                    ["P", "A", "C"].includes(
                        resp?.response_json_data?.response_stat
                            ?.transaction_response_status
                    ) &&
                    resp?.response_json_data?.transaction_code == "B1"
                ) {
                    // This is a test claim, if we get a successful response to a B1 claim we need to reverse it
                    const rdata = resp.request_json_data;
                    if (!rdata._meta) {
                        rdata._meta = {};
                    }
                    rdata._meta.customer_id = request.customer_id;
                    rdata._meta.customer_name = request.customer_name;
                    rdata._meta.customer_env_name = request.customer_env_name;
                    const revresp = await reverse_claim(app, CReq, rdata);
                    resp.reversal_information = revresp;
                }
                reply.send(resp);
                return reply;
            }
        }
    );

    app.post(
        "/pharm/reverse",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: "Reverse a claim",
                body: { $ref: "NCPDPClaimsRequest" },
            },
        },
        async (request, reply) => {
            const CReq = await set_switch(app, request);
            const rdata = request.body;
            if (!rdata) {
                app.log.error("invalid request: " + JSON.stringify(rdata));
                throw new Error("Invalid request");
            }
            const revresp = await reverse_claim(app, CReq, rdata);
            console.log(`Reverse response: ${JSON.stringify(revresp)}`);
            reply.send(revresp);
            return reply;
        }
    );
    app.post(
        "/pharm/validate",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: `This is the validation route for all Pharmacy NCPDP Claim messages. It will accept JSON and validate it against the NCPDP schema`,
                tags: ["claims", "pharmacy"],
                summary: "Validate a NCPDP D0 message",
                body: { $ref: "NCPDPClaimsRequest" },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        $ref: "ClaraClaimTxResponse",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            if (!request.body) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.body)
                );
                throw new Error("Invalid request");
            }

            app.log.info(
                "Inbound message received: " + JSON.stringify(request.body)
            );
            const req = new Request();
            try {
                const parsed = await req.fromJSON(request.body);
                const rawd0 = parsed.toString();
                if (parsed && parsed.transaction_groups && rawd0) {
                    reply.send(JSON.stringify(rawd0));
                }
            } catch (e) {
                const errorDetails = {
                    message: e.message,
                    stack: e.stack,
                };
                app.log.error(
                    "Error parsing request: " + JSON.stringify(errorDetails)
                );
                throw e;
            }
        }
    );

    app.post(
        "/pharm/d02json",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: `This route will accept a base64 encoded NCPDP D0 message and convert it to JSON`,
                tags: ["claims", "pharmacy"],
                summary: "Convert a NCPDP D0 message to JSON",
            },
        },
        async (request, reply) => {
            if (!request.body) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.data)
                );
                throw new Error("Invalid request");
            }

            const message = request.body.d0;
            app.log.info(
                "Inbound message received: " + JSON.stringify(request.body)
            );
            let req = new Request();
            if (request.body?.type == "response") {
                req = new Response();
            }

            const raw = Buffer.from(message, "base64");
            const d = raw.toString("utf8");
            app.log.info("Raw message: " + JSON.stringify(d));

            const treq = req.parse(d);
            const resp = treq.toJSON({
                groupsegments: true,
                readablekeys: true,
                flatten: true,
            });
            reply.send(resp);
            return reply;
        }
    );
    app.post(
        "/pharm/json2d0",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description: `This route will accept a JSON NCPDP message and convert it to a base64 encoded NCPDP D0 message`,
                tags: ["claims", "pharmacy"],
                summary: "Convert a JSON NCPDP message to D0",
            },
        },
        async (request, reply) => {
            if (!request.body) {
                app.log.error("invalid request: No body in data");
                throw new Error("Invalid request");
            }

            app.log.info("Starting json2d0 conversion");
            app.log.debug({
                msg: "Inbound message",
                body: request.body,
            });

            const req = new Request();
            try {
                app.log.info("Parsing request to D0 format");
                const parsed = req.fromJSON(request.body);
                app.log.debug({
                    msg: "Parsed JSON",
                    parsed: parsed,
                });
                const rawd0 = parsed.toString("utf8");

                if (parsed && parsed.transaction_groups && rawd0) {
                    const buf = Buffer.from(rawd0, "utf-8");
                    const b64d0 = buf.toString("base64");

                    const response = {
                        b64d0,
                        raw: rawd0,
                        readable: rawd0
                            .replace(/\x1D/g, "▼") // Group Separator
                            .replace(/\x1E/g, "|") // Segment Separator
                            .replace(/\x1C/g, "^"), // Field Separator
                        segments: rawd0
                            .replace(/\x1D/g, "▼")
                            .replace(/\x1E/g, "|")
                            .replace(/\x1C/g, "^")
                            .split("|")
                            .filter(Boolean)
                            .map((segment) => segment.trim()),
                        original_json: request.body,
                    };
                    app.log.debug(`Raw D0: ${JSON.stringify(response)}`);
                    reply.send(JSON.stringify(response, null, 2));
                }
            } catch (e) {
                app.log.error("Error parsing request: " + e);
                throw e;
            }
        }
    );
}
