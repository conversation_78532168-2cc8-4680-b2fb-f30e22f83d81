"use strict";
import { ChangeRequestMed } from "../handlers/changereq.js";
import { MedicalClaim } from "../handlers/medical/changeClaims.js";
import { MedicalEligibility } from "../handlers/medical/changeElig.js";
import { report_type_map, change_urlmap } from "../fixtures/maps.js";
import { lowercaseKeys } from "../../../utils/tools.js";
import { PgIntegrationsDB } from "../../../database/pg_integrationsdb.js";

async function change_med_req(app, cdata, req_type, method = "POST") {
    const env = cdata._meta?.request_environment || "staging";
    const CReq = new ChangeRequestMed(
        {
            environment: env,
        },
        app
    );

    if (cdata._meta?.is_test) {
        cdata.usageindicator = "T";
    }
    app.log.info(
        `Making request to change med ${env}: ${req_type} with data: ${JSON.stringify(cdata)}`
    );
    return await CReq.make_req(cdata, req_type, method);
}

export default async function (app, opts) {
    app.log = app.log.child({
        name: "Claims/Medical",
        component: "Routes",
    });
    app.post(
        "/med/eligibility",
        {
            preHandler: app.verifyBearerAuth,
            preValidation: async (request, _reply) => {
                request.body = lowercaseKeys(request.body);
            },
            schema: {
                description:
                    "This is the inbound route to run CHC Medical Eligibility",
                tags: ["eligibility", "medical"],
                summary: "Inbound route to run CHC Medical Eligibility checks",
                body: {
                    $ref: "ChangeMedicalEligRequest",
                },
            },
        },
        async (request, reply) => {
            if (!request.body) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.body)
                );
                throw new Error("Invalid request");
            }
            const cdata = {};
            cdata._meta = {};
            cdata.request_json_data = request.body;
            const control_number = await app.gen_control_number();
            app.log.info("Control number generated: " + control_number);
            cdata._meta = request.body._meta || {};
            cdata._meta.customer_id = request.customer_id;
            cdata._meta.customer_name = request.customer_name;
            cdata._meta.customer_env_name = request.customer_env_name;
            cdata._meta.controlNumber = control_number;
            cdata.request_json_data.controlNumber = control_number;
            const claim_valid = new MedicalEligibility(cdata.request_json_data);
            cdata.validated_claim_object = claim_valid.toJSON();
            cdata.request_type = "eligibility";

            const response = await change_med_req(
                app,
                cdata,
                "med_eligibility"
            );
            reply.send(response);
            return reply;
        }
    );

    app.post(
        "/med/validate",
        {
            preHandler: app.verifyBearerAuth,
            preValidation: async (request, _reply) => {
                request.body = lowercaseKeys(request.body);
            },
            schema: {
                description:
                    "This is the inbound route to validate CHC Medical Claims",
                tags: ["claims", "medical"],
                summary: "Inbound route to validate CHC Medical Claims",
                body: {
                    $ref: "ChangeMedicalClaimRequest",
                },
            },
        },
        async (request, reply) => {
            if (!request.body) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.body)
                );
                throw new Error("Invalid request");
            }
            const cdata = {};
            cdata._meta = {};
            cdata.request_json_data = request.body;
            const control_number = await app.gen_control_number();
            app.log.info("Control number generated: " + control_number);
            cdata._meta = request.body._meta || {};
            cdata._meta.customer_id = request.customer_id;
            cdata._meta.customer_name = request.customer_name;
            cdata._meta.customer_env_name = request.customer_env_name;
            cdata._meta.controlNumber = control_number;
            cdata.request_json_data.controlNumber = control_number;
            const claim_valid = new MedicalClaim(cdata.request_json_data);
            cdata.validated_claim_object = claim_valid.toJSON();
            cdata.request_type = "claim";

            const response = await change_med_req(
                app,
                cdata,
                "med_claim_validate"
            );
            reply.send(response);
            return reply;
        }
    );

    app.post(
        "/med/submit",
        {
            preHandler: app.verifyBearerAuth,
            preValidation: async (request, _reply) => {
                request.body = lowercaseKeys(request.body);
            },
            schema: {
                description:
                    "This is the inbound route to submit CHC Medical Claims",
                tags: ["claims", "medical"],
                summary: "Inbound route to submit CHC Medical Claims",
                body: {
                    $ref: "ChangeMedicalClaimRequest",
                },
            },
        },
        async (request, reply) => {
            if (!request.body) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.body)
                );
                throw new Error("Invalid request");
            }
            const cdata = {};
            cdata._meta = {};
            cdata.request_json_data = request.body;
            const control_number = await app.gen_control_number();
            app.log.info("Control number generated: " + control_number);
            cdata._meta = request.body._meta || {};
            cdata._meta.customer_id = request.customer_id;
            cdata._meta.customer_name = request.customer_name;
            cdata._meta.customer_env_name = request.customer_env_name;
            cdata._meta.controlNumber = control_number;
            cdata.request_json_data.controlNumber = control_number;
            const claim_valid = new MedicalClaim(cdata.request_json_data);
            cdata.validated_claim_object = claim_valid.toJSON();
            cdata.request_type = "claim";

            const response = await change_med_req(
                app,
                cdata,
                "med_claim_submit"
            );
            reply.send(response);
            return reply;
        }
    );

    app.get(
        "/med/status/:correlation_id",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description:
                    "This is the inbound route to check CHC Medical Claims status",
                tags: ["claims", "medical"],
                summary: "Inbound route to check CHC Medical Claims status",
                params: {
                    type: "object",
                    properties: {
                        correlation_id: { type: "string" },
                    },
                },
            },
        },
        async (request, reply) => {
            const cdata = {};
            cdata._meta = {};
            cdata._meta.customer_id = request.customer_id;
            cdata._meta.customer_name = request.customer_name;
            cdata._meta.customer_env_name = request.customer_env_name;
            const response = await change_med_req(
                app,
                cdata,
                "med_claim_status",
                request.params.correlation_id
            );
            reply.send(response);
            return reply;
        }
    );

    app.get(
        "/med/reports",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description:
                    "This is the inbound route to get CHC Medical Claims reports",
                tags: ["claims", "medical"],
                summary: "Inbound route to get CHC Medical Claims reports",
                querystring: {
                    type: "object",
                    properties: {
                        report_type: { type: "string" },
                        start_date: { type: "string" },
                        end_date: { type: "string" },
                    },
                },
            },
        },
        async (request, reply) => {
            let reps = [];
            const report_type = request.query.report_type || "all";
            const d = {};
            d._meta = request.body?._meta || {};
            d._meta.customer_id = request.customer_id;
            d._meta.customer_name = request.customer_name;
            d._meta.customer_env_name = request.customer_env_name;

            const resp = await change_med_req(
                app,
                d,
                "med_claim_reports",
                "GET",
                {
                    startDate: request.query.start_date,
                    endDate: request.query.end_date,
                }
            );
            if (resp instanceof Error) {
                app.log.error(
                    "Error in making request to change med api: " + resp
                );
                throw resp;
            } else {
                app.log.info(
                    "Received response from change med api: " +
                        JSON.stringify(resp)
                );
                if (
                    resp &&
                    resp.response_json_data &&
                    resp.response_json_data.reports &&
                    resp.response_json_data.reports.length > 0
                ) {
                    app.log.info(
                        "Reports available to download: " +
                            JSON.stringify(resp.response_json_data.reports)
                    );
                    for (const r of resp.response_json_data.reports) {
                        let rdata = {};
                        const ftype = r.substring(0, 2);
                        const rtype = report_type_map[ftype] || "unknown";
                        const submitter_id = r.substring(2, 8);
                        // build download urls to use in pgboss so we dont have to lookup again
                        const raw_report_url =
                            d._meta.request_environment == "staging"
                                ? app.config.CHANGE_MED_STAGING_BASE_URL +
                                  change_urlmap["med_claim_reports"] +
                                  `/${r}`
                                : app.config.CHANGE_MED_PROD_BASE_URL +
                                  change_urlmap["med_claim_reports"] +
                                  `/${r}`;
                        const report_convert_url =
                            rtype == "835"
                                ? raw_report_url.replace(`/${r}`, `${r}/835`)
                                : rtype == "277"
                                  ? raw_report_url.replace(`/${r}`, `${r}/277`)
                                  : null;

                        rdata = {
                            _meta: d._meta,
                            report_filename: r,
                            report_type: rtype,
                            submitter_id: submitter_id,
                            raw_report_url: raw_report_url,
                        };
                        if (report_convert_url) {
                            rdata.report_convert_url = report_convert_url;
                        }
                        reps.push(rdata);
                    }
                    app.log.info(
                        "Reports available to download: " + JSON.stringify(reps)
                    );
                    // Send a report download request to pgboss to fetch and store the reports
                    if (report_type != "all") {
                        // Filter the reps array to only include the report type that is not all
                        reps = reps.filter((r) => r.report_type == report_type);
                    }
                    //reps = [reps[0]];
                    const report_req = {
                        type: "claims_report_download",
                        customer_id: request.customer_id,
                        customer_name: request.customer_name,
                        customer_env_name: request.customer_env_name,
                        reports: reps,
                        environment: d._meta.request_environment,
                    };
                    const options = {
                        retryLimit: 5,
                        retryBackoff: true,
                    };
                    const jobid = await app.pgboss.send(
                        "medical_claim_reports",
                        report_req,
                        options
                    );
                    app.log.info(
                        `Claim download request sent to medical_claim_reports queue with jobid: ${jobid}`
                    );
                    reply.send({
                        message:
                            "Downloading available reports, with jobid: " +
                            jobid,
                        reports: reps,
                    });
                } else {
                    app.log.error("No reports available to download");
                    reply.send({
                        message: "No new reports available to download " + resp,
                    });
                }
                return reply;
            }
        }
    );

    app.get(
        "/med/reports/list",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description:
                    "This is the inbound route to list CHC Medical Claims reports",
                tags: ["claims", "medical"],
                summary: "Inbound route to list CHC Medical Claims reports",
                querystring: {
                    type: "object",
                    properties: {
                        report_type: {
                            type: "string",
                            enum: ["835", "277", "all"],
                        },
                        start_date: { type: "string", format: "date" },
                        end_date: { type: "string", format: "date" },
                        control_number: { type: "string" },
                    },
                    anyOf: [
                        { required: ["start_date"] },
                        { required: ["end_date"] },
                        { required: ["control_number"] },
                    ],
                },
                response: {
                    200: {
                        description: "Successful response",
                        $ref: "BaseClaraResponse#",
                    },
                },
            },
        },
        async (request, reply) => {
            const {
                report_type = "all",
                start_date,
                end_date,
                control_number,
            } = request.query;

            try {
                const pgDb = new PgIntegrationsDB(app, {
                    customer_id: request.customer_id,
                    customer_name: request.customer_name,
                    customer_env_name: request.customer_env_name,
                });

                const result = await pgDb.fetch_medical_claim_reports({
                    report_type,
                    start_date,
                    end_date,
                    control_number,
                });

                reply.send({
                    _meta: {
                        total_reports: result.total_reports,
                        request_timestamp: new Date().toISOString(),
                    },
                    request_json_data: {
                        report_type,
                        start_date,
                        end_date,
                        control_number,
                    },
                    response_json_data: {
                        reports: result.reports,
                    },
                });
                return reply;
            } catch (error) {
                app.log.error("Error fetching medical claim reports:", error);
                reply.code(500).send({
                    error: "Internal Server Error",
                    error_code: "INTERNAL_SERVER_ERROR",
                    error_description: "Error fetching medical claim reports",
                });
            }
        }
    );
}
