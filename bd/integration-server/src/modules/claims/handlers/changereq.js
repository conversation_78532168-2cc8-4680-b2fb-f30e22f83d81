"use strict";
import fetch from "node-fetch";
import { Response } from "./pharmacy/NcpdpTX.js";
import * as soap from "soap";
import { ClaimsDB } from "../../../database/claimsdb.js";
import { PgIntegrationsDB } from "../../../database/pg_integrationsdb.js";
import { change_urlmap } from "../fixtures/maps.js";
import { formatToClaraDT } from "../../../utils/tools.js";

export class ChangeRequestMed {
    constructor(opts, app) {
        this.app = app;
        if (!opts) {
            opts = {};
        }
        opts.environment = opts.environment || "staging";
        this.change_config = {
            staging: {
                client_id: this.app.config.CHANGE_MED_STAGING_CLIENT_ID,
                client_secret: this.app.config.CHANGE_MED_STAGING_CLIENT_SECRET,
                base_url: this.app.config.CHANGE_MED_STAGING_BASE_URL,
            },
            prod: {
                client_id: this.app.config.CHANGE_MED_PROD_CLIENT_ID,
                client_secret: this.app.config.CHANGE_MED_PROD_CLIENT_SECRET,
                base_url: this.app.config.CHANGE_MED_PROD_BASE_URL,
            },
        };
        if (!this.change_config[opts.environment].client_id) {
            app.log.error("CHANGE_MED_CLIENT_ID is not set");
            return;
        }
        if (!this.change_config[opts.environment].client_secret) {
            app.log.error("CHANGE_MED_CLIENT_SECRET is not set");
            return;
        }
        this.baseurl = this.change_config[opts.environment].base_url;
        this.client_id = this.change_config[opts.environment].client_id;
        this.client_secret = this.change_config[opts.environment].client_secret;
    }

    async init_auth(req_data) {
        if (this.cachedToken && this.tokenExpiration > Date.now()) {
            return {
                token: this.cachedToken,
                credentials: this.cachedCredentials,
            };
        }

        // Handle both direct metadata and nested _meta structure
        const metadata = req_data._meta || req_data;
        const isProd = metadata.environment === "prod";

        let creds = {};
        // Only get credentials for prod environment
        if (isProd) {
            // Get credentials from customer schema
            const db_opts = {
                customer_id: metadata.customer_id,
                customer_name: metadata.customer_name,
                customer_env_name: metadata.customer_env_name,
            };
            const pgDb = new PgIntegrationsDB(this.app, db_opts);
            creds = await pgDb.get_credentials("change_medical_claims");

            if (!creds?.username || !creds?.password || !creds?.submitter_id) {
                throw new Error(
                    "Missing required Change Healthcare credentials"
                );
            }
        }

        const headers = {
            "Content-Type": "application/json",
        };

        const body = {
            client_id: this.client_id,
            client_secret: this.client_secret,
            grant_type: "client_credentials",
        };

        const options = {
            method: "POST",
            headers: headers,
            body: JSON.stringify(body),
        };

        const url = this.baseurl + "/apip/auth/v2/token";

        this.app.log.info(`Auth request URL: ${url}`);
        this.app.log.info(`Auth request headers: ${JSON.stringify(headers)}`);
        this.app.log.info(`Auth request body : ${JSON.stringify(body)}`);

        try {
            const resp = await fetch(url, options);
            const data = await resp.json();

            if (!resp.ok) {
                this.app.log.error(`Auth failed with status: ${resp.status}`);
                this.app.log.error(
                    `Auth error response: ${JSON.stringify(data)}`
                );
                throw new Error(
                    `Auth failed: ${data.error_description || data.error}`
                );
            }

            if (data && data.access_token) {
                this.cachedToken = data.access_token;
                this.tokenExpiration = Date.now() + data.expires_in * 1000;
                this.cachedCredentials = creds;

                return {
                    token: this.cachedToken,
                    credentials: this.cachedCredentials,
                };
            } else {
                throw new Error("No access token in response");
            }
        } catch (error) {
            this.app.log.error(`Auth request failed: ${error.message}`);
            throw error;
        }
    }

    async simple_req(data = {}, url, req_method = "GET", retryCount = 0) {
        const { token, credentials } = await this.init_auth(data);

        // Handle both direct metadata and nested _meta structure
        const metadata = data._meta || data;
        const isProd = metadata.environment === "prod";

        const headers = {
            Accept: "application/json",
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        };

        // Only add credential headers for prod environment
        if (isProd) {
            Object.assign(headers, {
                "X-CHC-MedicalEligibility-Username": credentials.username,
                "X-CHC-MedicalEligibility-Pwd": credentials.password,
                "X-CHC-MedicalEligibility-SubmitterId":
                    credentials.submitter_id,
                "X-CHC-MedicalEligibility-BillerId":
                    credentials.billing_id || "396615",
            });
        }

        const options = {
            method: req_method,
            headers: headers,
        };

        this.app.log.info(
            `Making request to change med api: ${url} with headers: ${JSON.stringify(
                {
                    ...headers,
                    "X-CHC-MedicalEligibility-Pwd": "***REDACTED***",
                }
            )}`
        );

        const resp = await fetch(url, options);
        let rdata = await resp.json();

        if (resp.status === 401 && retryCount < 1) {
            this.app.log.warn(
                "Received 401 error. Attempting to refresh token and retry."
            );
            // Clear any cached token
            this.cachedToken = null;
            // Retry the request with a new token
            return this.simple_req(data, url, req_method, retryCount + 1);
        }

        if (!resp.ok) {
            this.app.log.error(
                `Error response from change med api: ${JSON.stringify(rdata)}`
            );
            throw new Error(
                `API request failed with status ${resp.status}: ${rdata.error_description || rdata.error}`
            );
        }

        rdata = formatToClaraDT(rdata);

        this.app.log.info(
            `Received response from change med api: ${JSON.stringify(rdata)}`
        );
        return rdata;
    }

    async make_req(data, ep, req_method = "POST", params = {}) {
        const data_body = data.validated_claim_object
            ? data.validated_claim_object
            : data;
        const { token, credentials } = await this.init_auth(data);
        if (!token) {
            this.app.log.error(
                "There is no authtoken, check request to change for auth."
            );
            return;
        }

        // Handle both direct metadata and nested _meta structure
        const metadata = data._meta || data;
        const isProd = metadata.environment === "prod";

        const headers = {
            Accept: "application/json",
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        };

        // Only add credential headers for prod environment
        if (isProd) {
            Object.assign(headers, {
                "X-CHC-MedicalEligibility-Username": credentials.username,
                "X-CHC-MedicalEligibility-Pwd": credentials.password,
                "X-CHC-MedicalEligibility-SubmitterId":
                    credentials.submitter_id,
                "X-CHC-MedicalEligibility-BillerId":
                    credentials.billing_id || "396615",
            });
        }

        this.app.log.info(`Headers: ${JSON.stringify(headers, null, 2)}`);
        let options = {};
        if (req_method == "GET") {
            options = {
                method: "GET",
                headers: headers,
            };
        } else {
            options = {
                method: "POST",
                headers: headers,
                body: JSON.stringify(data_body),
            };
        }
        let url = this.baseurl + change_urlmap[ep];
        if (Object.keys(params).length > 0) {
            url =
                url +
                "?" +
                Object.entries(params)
                    .map(([key, value]) => `${key}=${value}`)
                    .join("&");
        }
        this.app.log.info(
            `Making request to change med api: ${url} with data: ${JSON.stringify(
                {
                    ...data_body,
                    ...options.headers,
                }
            )}`
        );
        const resp = await fetch(url, options);
        let rdata = await resp.json();
        this.app.log.debug(
            `Received response from change med api: ${JSON.stringify(rdata)}`
        );
        if (!rdata.errors) {
            rdata = formatToClaraDT(rdata);
        }

        const emeta = {};
        const cdata = {};
        let eobj = {};
        let errors = [];
        Object.assign(cdata, data);

        if (
            (rdata.errors && rdata.errors.length > 0) ||
            (rdata.errorResponse && rdata.errorResponse.errors.length > 0)
        ) {
            Object.assign(emeta, rdata.meta);
            emeta.customer_id = cdata._meta.customer_id;
            emeta.customer_name = cdata._meta.customer_name;
            emeta.customer_env_name = cdata._meta.customer_env_name;
            errors = rdata.errors ? rdata.errors : rdata.errorResponse.errors;
            this.app.log.error(
                "Error in making request to change med api: " +
                    JSON.stringify(errors)
            );
            eobj = {
                name: "ChangeHealthError",
                message: "Bad Request",
                error_code: resp.status_code,
                type: "request",
                description: errors,
                error_meta: emeta,
            };
        }
        cdata.response_json_data = rdata;
        const d = {};
        Object.assign(d, cdata);
        d.errors = JSON.stringify(errors);
        if (
            cdata?.request_type == "eligibility" ||
            cdata?.request_type == "claim"
        ) {
            // Check if PostgreSQL is enabled via config
            const usePg = this.app.config.USE_POSTGRES === "true";

            if (usePg) {
                try {
                    const pgDb = new PgIntegrationsDB(this.app, {
                        customer_id: d._meta.customer_id,
                        customer_name: d._meta.customer_name,
                        customer_env_name: d._meta.customer_env_name,
                    });

                    await pgDb.insert_new_medical_claim(d);
                    this.app.log.info(
                        "Successfully inserted claim into PostgreSQL"
                    );
                } catch (pgError) {
                    this.app.log.error("PostgreSQL insert failed", pgError);
                    throw pgError; // or handle as needed
                }
            } else {
                // Use existing SQLite implementation
                const sqliteDb = new ClaimsDB(this.app, {
                    customer_id: d._meta.customer_id,
                    customer_name: d._meta.customer_name,
                });
                this.app.log.info(
                    "Inserting claim into SQLite database: " + JSON.stringify(d)
                );
                sqliteDb.insert_new_medical_claim(d);
            }
        }

        if (Object.keys(eobj).length > 0) {
            throw eobj;
        } else {
            return cdata;
        }
    }

    insert_claim(data) {
        const db = new ClaimsDB(this.app, {
            customer_id: data.customer_id,
            customer_name: data.customer_name,
            customer_env_name: data.customer_env_name,
        });
        db.insert_new_medical_claim(data);
    }
}

export class ChangeRequestPharm {
    constructor(opts, app) {
        this.app = app;
        if (!opts) {
            opts = {};
        }
        // for change ncpdp there is no staging env
        opts.environment = opts.environment || "prod";
        this.change_config = {
            staging: {
                base_url: this.app.config.CHANGE_PHARM_STAGING_BASE_URL,
                username: this.app.config.CHANGE_PHARM_STAGING_USERNAME,
                password: this.app.config.CHANGE_PHARM_STAGING_PASSWORD,
                soap_url: this.app.config.CHANGE_PHARM_STAGING_SOAP_URL,
            },
            prod: {
                base_url: this.app.config.CHANGE_PHARM_PROD_BASE_URL,
                username: this.app.config.CHANGE_PHARM_PROD_USERNAME,
                password: this.app.config.CHANGE_PHARM_PROD_PASSWORD,
                soap_url: this.app.config.CHANGE_PHARM_PROD_SOAP_URL,
            },
        };
        if (!this.change_config[opts.environment].base_url) {
            app.log.error("CHANGE_PHARM_BASE_URL is not set");
            return;
        }
        if (!this.change_config[opts.environment].username) {
            app.log.error("CHANGE_PHARM_USERNAME is not set");
            return;
        }
        this.baseurl = this.change_config[opts.environment].base_url;
        this.username = this.change_config[opts.environment].username;
        this.password = this.change_config[opts.environment].password;
        this.wsdurl = this.change_config[opts.environment].soap_url;
    }

    async initializeSoapClient() {
        const soapClient = await soap.createClientAsync(this.wsdurl, {
            forceSoap12Headers: true,
        });
        try {
            soapClient.on("message", (xml) => {
                this.app.log.info("SOAP envelope: " + xml);
            });
            soapClient.on("request", (request) => {
                this.app.log.info(`SOAP Request: ${request}`);
            });
            soapClient.on("response", (response) => {
                this.app.log.info(`SOAP Response: ${JSON.stringify(response)}`);
            });
            soapClient.on("soapError", (error) => {
                this.app.log.error(`SOAP Error: ${error}`);
            });
        } catch (error) {
            this.app.log.error("Error creating SOAP client:", error);
            throw error;
        }
        return soapClient;
    }

    async make_req(data) {
        try {
            this.soapClient = await this.initializeSoapClient();
            this.app.log.debug(
                `Change NCPDP Request : ${JSON.stringify(data)}`
            );
            const args = {
                sUserID: this.username,
                sPassword: this.password,
                sMessageType: "PHM",
                sEncodedRequest: data.request_d0_b64,
            };
            this.app.log.info(
                `Making request to change pharm NCPDP API: ${JSON.stringify(
                    args
                )}`
            );
            const eobj = {};
            const result = await this.soapClient.SendRequestAsync(args);
            const rdata = result[0]?.SendRequestResult;

            if (rdata && rdata.ErrorCode) {
                eobj.error_code = rdata.ErrorCode;
                eobj.error_description = rdata.Response;
            } else {
                if (rdata && rdata.Response && rdata.ErrorCode === 0) {
                    // this is a successful response from Change
                    const rmess = Buffer.from(
                        rdata.Response,
                        "base64"
                    ).toString("utf8");
                    this.app.log.info(
                        `Received RAW response from change pharm api: ${JSON.stringify(rmess)}`
                    );
                    data.response_d0_raw = rmess;
                    data.response_d0_b64 = rdata.Response;

                    const ChangeResp = new Response();
                    const cresp = ChangeResp.parse(rmess);
                    const rjson = cresp.toJSON({
                        readablekeys: true,
                        groupsegments: true,
                        flatten: true,
                    });
                    this.app.log.info(
                        `Received response from change pharm api: ${JSON.stringify(rjson)}`
                    );
                    if (
                        rjson &&
                        rjson.header?.header_response_status == "Rejected"
                    ) {
                        // this is a rejection response from Change
                        eobj.error_code_description =
                            rjson.transaction_groups[0]?.response_status?.reject_code[0];
                        eobj.error_code =
                            cresp.transaction_groups[0]?.segments[0]?.data?.FB;
                        eobj.error_description =
                            rjson.transmission_group?.response_message?.message;
                    }
                    data.response_json_data = rjson;
                    const d = {};
                    Object.assign(d, data);
                    d.switch_provider = "change";
                    d.request_type =
                        data.request_json_data?.header?.transaction_code;
                    d.svc_provider_id =
                        data.request_json_data?.header?.svc_prov_id;
                    d.svc_date =
                        data.request_json_data?.header?.date_of_service;
                    d.errors = JSON.stringify(eobj);
                    d.customer_id = data.customer_id;
                    d.customer_name = data.customer_name;
                    d.customer_env_name = data.customer_env_name;
                    delete data.customer_id;
                    delete data.customer_name;
                    delete data.customer_env_name;
                    // Check if PostgreSQL is enabled via config
                    const usePg = this.app.config.USE_POSTGRES === "true";

                    if (usePg) {
                        try {
                            const pgDb = new PgIntegrationsDB(this.app, {
                                customer_id: d.customer_id,
                                customer_name: d.customer_name,
                                customer_env_name: d.customer_env_name,
                            });

                            // Ensure schemas exist before inserting
                            await pgDb.ensureSchema();

                            await pgDb.insert_new_ncpdp_claim(d);
                            this.app.log.info(
                                "Successfully inserted pharmacy claim into PostgreSQL"
                            );
                        } catch (pgError) {
                            this.app.log.warn(
                                "PostgreSQL insert failed, falling back to SQLite",
                                pgError
                            );

                            const sqliteDb = new ClaimsDB(this.app, {
                                customer_id: d.customer_id,
                                customer_name: d.customer_name,
                            });
                            sqliteDb.insert_new_ncpdp_claim(d);
                        }
                    } else {
                        // Use existing SQLite implementation
                        const sqliteDb = new ClaimsDB(this.app, {
                            customer_id: d.customer_id,
                            customer_name: d.customer_name,
                        });
                        this.app.log.info("Using SQLite for pharmacy claim");
                        sqliteDb.insert_new_ncpdp_claim(d);
                    }
                }
            }

            if (Object.keys(eobj).length > 0) {
                throw eobj;
            } else {
                return data;
            }
        } catch (error) {
            console.error(
                "Change NCPDP Request Exception: " +
                    JSON.stringify(error, null, 2)
            );
            throw error;
        }
    }
}
