"use strict";

import { Response } from "./pharmacy/NcpdpTX.js";
import { ClaimsDB } from "../../../database/claimsdb.js";
import { powerline_map } from "../fixtures/maps.js";
import fetch from "node-fetch";
import { PgIntegrationsDB } from "../../../database/pg_integrationsdb.js";

export class PowerlineRequestPharm {
    constructor(opts, app) {
        this.app = app;
        this.app.log.child({
            name: "NCPDP/Powerline",
            component: "PowerlineRequestPharm",
        });
        const environment = opts?.env || "staging";
        this.app.log.info(`Powerline environment: ${environment}`);
        if (environment == "prod") {
            if (!this.app.config.POWERLINE_PROD_CLIENT_SECRET) {
                this.app.log.error("POWERLINE_PROD_CLIENT_SECRET is not set");
                throw new Error("Powerline not configured");
            }
            if (!this.app.config.POWERLINE_PROD_CLIENT_ID) {
                this.app.log.error("POWERLINE_PROD_CLIENT_ID is not set");
                throw new Error("Powerline not configured");
            }
            this.opts = opts || {};
            this.baseurl = this.app.config.POWERLINE_PROD_BASE_URL;
            this.oauth_url = this.app.config.POWERLINE_PROD_OAUTH_URL;
            this.client_id = this.app.config.POWERLINE_PROD_CLIENT_ID;
            this.client_secret = this.app.config.POWERLINE_PROD_CLIENT_SECRET;
        } else {
            if (!this.app.config.POWERLINE_STAGING_CLIENT_SECRET) {
                this.app.log.error(
                    "POWERLINE_STAGING_CLIENT_SECRET is not set"
                );
                throw new Error("Powerline not configured");
            }
            if (!this.app.config.POWERLINE_STAGING_CLIENT_ID) {
                this.app.log.error("POWERLINE_STAGING_CLIENT_ID is not set");
                throw new Error("Powerline not configured");
            }
            this.opts = opts || {};
            this.baseurl = this.app.config.POWERLINE_STAGING_BASE_URL;
            this.oauth_url = this.app.config.POWERLINE_STAGING_OAUTH_URL;
            this.client_id = this.app.config.POWERLINE_STAGING_CLIENT_ID;
            this.client_secret =
                this.app.config.POWERLINE_STAGING_CLIENT_SECRET;
        }
    }

    async init_auth() {
        const body = {
            grant_type: "client_credentials",
        };
        // Encode clientId and clientSecret
        const credentials = `${this.client_id}:${this.client_secret}`;
        const credentialsBase64 = Buffer.from(credentials).toString("base64");

        // Convert the body to URL-encoded format
        let formBody = [];
        for (const property in body) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(body[property]);
            formBody.push(encodedKey + "=" + encodedValue);
        }
        formBody = formBody.join("&");

        const headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Basic ${credentialsBase64}`,
        };
        const options = {
            method: "POST",
            headers: headers,
            body: formBody,
        };
        const url = this.oauth_url;
        const resp = await fetch(url, options);
        const data = await resp.json();
        if (data && data.access_token) {
            return data.access_token;
        } else {
            return Error(
                `Error getting access token from POWERLINE  api. Status: ${resp.status} ${resp.statusText}`
            );
        }
    }

    async make_req(data) {
        try {
            const authToken = await this.init_auth();
            await authToken;

            if (!authToken) {
                this.app.log.error(
                    "There is no authtoken, check request to powerline for auth."
                );
                return;
            }
            const headers = {
                Accept: "application/json",
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
            };
            const d = {
                requestEdi: data.request_d0_raw,
                locationId: data.customer_id,
            };

            const options = {
                method: "POST",
                headers: headers,
                body: JSON.stringify(d),
            };
            const url = this.baseurl + powerline_map.claims;
            this.app.log.info(
                `Making request to powerline claims api: ${url} with data: ${JSON.stringify(d)}`
            );
            let rdata;
            const eobj = {};
            try {
                const resp = await fetch(url, options);
                if (!resp.ok) {
                    // Handle non-2xx responses
                    rdata = await resp.text(); // Using .text() to handle non-JSON responses
                    eobj.error_code = resp.status;
                    eobj.error_description = resp.statusText;
                    eobj.statusCode = resp.status;
                    this.app.log.error(
                        `Error response from powerline api. Status: ${resp.status}, Body: ${rdata}`
                    );
                } else {
                    rdata = await resp.json();
                    this.app.log.info(
                        `Received response from powerline api: ${JSON.stringify(rdata)}`
                    );
                }
            } catch (error) {
                this.app.log.error(
                    `Exception making request to powerline api: ${error}`
                );
            }
            if (rdata && rdata.errorMessage) {
                eobj.error_code = 400;
                eobj.error_description = rdata.errorMessage;
            } else {
                if (rdata && rdata.data && rdata.transactionId) {
                    // this is a successful response from Powerline
                    const rmess = rdata.data;
                    const r64 = Buffer.from(rmess).toString("base64");
                    this.app.log.info(
                        `Received RAW response from powerline api: ${JSON.stringify(rdata.data)}`
                    );
                    data.response_d0_raw = rmess;
                    data.response_d0_b64 = r64;

                    const ClaimResp = new Response();
                    const cresp = ClaimResp.parse(rmess);
                    const rjson = cresp.toJSON({
                        readablekeys: true,
                        groupsegments: true,
                        flatten: true,
                    });
                    this.app.log.info(
                        `Received response from powerline api - tojson: ${JSON.stringify(rjson)}`
                    );
                    if (
                        rjson &&
                        rjson.header?.header_response_status == "Rejected"
                    ) {
                        // this is a rejection response from Change
                        eobj.error_code_description =
                            rjson.transaction_groups[0]?.response_status?.reject_code[0];
                        eobj.error_code =
                            cresp.transaction_groups[0]?.segments[0]?.data?.FB;
                        eobj.error_description =
                            rjson.transmission_group?.response_message?.message;
                    }
                    data.response_json_data = rjson;
                    const d = {};
                    Object.assign(d, data);
                    d.transaction_id = rdata.transactionId;
                    d.transmission_id = rdata.transmissionId;
                    d.switch_provider = "powerline";
                    d.request_type =
                        data.request_json_data?.header?.transaction_code;
                    d.svc_provider_id =
                        data.request_json_data?.header?.svc_prov_id;
                    d.svc_date =
                        data.request_json_data?.header?.date_of_service;
                    d.errors = JSON.stringify(eobj);
                    d.customer_id = data.customer_id;
                    d.customer_name = data.customer_name;
                    d.customer_env_name = data.customer_env_name;
                    delete data.customer_id;
                    delete data.customer_name;
                    delete data.customer_env_name;
                    delete data._meta;

                    // Check if PostgreSQL is enabled via config
                    const usePg = this.app.config.USE_POSTGRES === "true";

                    if (usePg) {
                        try {
                            const pgDb = new PgIntegrationsDB(this.app, {
                                customer_id: d.customer_id,
                                customer_name: d.customer_name,
                                customer_env_name: d.customer_env_name,
                            });

                            // Ensure schemas exist before inserting
                            await pgDb.ensureSchema();

                            await pgDb.insert_new_ncpdp_claim(d);
                            this.app.log.info(
                                "Successfully inserted pharmacy claim into PostgreSQL"
                            );
                        } catch (pgError) {
                            this.app.log.warn(
                                "PostgreSQL insert failed, falling back to SQLite",
                                pgError
                            );

                            const sqliteDb = new ClaimsDB(this.app, {
                                customer_id: d.customer_id,
                                customer_name: d.customer_name,
                            });
                            sqliteDb.insert_new_ncpdp_claim(d);
                        }
                    } else {
                        // Use existing SQLite implementation
                        const sqliteDb = new ClaimsDB(this.app, {
                            customer_id: d.customer_id,
                            customer_name: d.customer_name,
                        });
                        this.app.log.info("Using SQLite for pharmacy claim");
                        sqliteDb.insert_new_ncpdp_claim(d);
                    }
                }
                // TODO: Powerline returns a retryableFailure" key in the JSON response they send back, need to
                // add code to create a pg-boss retry job for this with a max attempts ..etc and then a callback to the
                // client for claims status.
            }

            if (Object.keys(eobj).length > 0) {
                throw eobj;
            } else {
                return data;
            }
        } catch (error) {
            this.app.log.error(
                "Powerline Exception: " + JSON.stringify(error, null, 2)
            );
            throw error;
        }
    }
}
