export const FIELD_SEPARATOR = "\x1C";
export const GROUP_SEPARATOR = "\x1D";
export const SEGMENT_SEPARATOR = "\x1E";

export const REJECTION_CODES = {
    1: "missing_or_invalid_bin",
    2: "missing_or_invalid_version_number",
    3: "missing_or_invalid_transaction_code",
    4: "missing_or_invalid_processor_control_number",
    5: "missing_or_invalid_pharmacy_number",
    6: "missing_or_invalid_group_number",
    7: "missing_or_invalid_cardholder_id_number",
    8: "missing_or_invalid_person_code",
    9: "missing_or_invalid_birth_date",
    10: "missing_or_invalid_patient_gender_code",
    11: "missing_or_invalid_pt_rel_code",
    12: "missing_or_invalid_patient_location",
    13: "missing_or_invalid_other_coverage_code",
    14: "missing_or_invalid_elig_clar_code",
    15: "missing_or_invalid_date_of_service",
    16: "missing_or_invalid_rx_svc_no",
    17: "missing_or_invalid_fill_number",
    19: "missing_or_invalid_day_supply",
    "1C": "missing_or_invalid_smoker_code",
    "1E": "missing_or_invalid_prescriber_location_code",
    20: "missing_or_invalid_compound_code",
    21: "missing_or_invalid_prod_svc_id",
    22: "missing_or_invalid_dispense_as_written_daw_product_selection_code",
    23: "missing_or_invalid_ing_cst_sub",
    25: "missing_or_invalid_dr_id",
    26: "missing_or_invalid_unit_of_measure",
    28: "missing_or_invalid_date_rx_written",
    29: "missing_or_invalid_number_refills_authorized",
    "2C": "missing_or_invalid_pregnancy_indicator",
    "2E": "missing_or_invalid_pri_dr_id_qualifier",
    32: "missing_or_invalid_level_of_service",
    33: "missing_or_invalid_rx_origin_code",
    34: "missing_or_invalid_sub_clar_code",
    35: "missing_or_invalid_pri_dr_id",
    38: "missing_or_invalid_basis_of_cost",
    39: "missing_or_invalid_dx_code",
    "3A": "missing_or_invalid_request_type",
    "3B": "missing_or_invalid_request_period_date_begin",
    "3C": "missing_or_invalid_request_period_date_end",
    "3D": "missing_or_invalid_basis_of_request",
    "3E": "missing_or_invalid_authorized_representative_first_name",
    "3F": "missing_or_invalid_authorized_representative_last_name",
    "3G": "missing_or_invalid_authorized_representative_street_address",
    "3H": "missing_or_invalid_authorized_representative_city_address",
    "3J": "missing_or_invalid_authorized_representative_state_province_address",
    "3K": "missing_or_invalid_authorized_representative_zip_postal_zone",
    "3M": "missing_or_invalid_dr_phone",
    "3N": "missing_or_invalid_prior_authorized_number_assigned",
    "3P": "missing_or_invalid_authorization_number",
    "3R": "prior_authorization_not_required",
    "3S": "missing_or_invalid_prior_authorization_supporting_documentation",
    "3T": "active_prior_authorization_exists_resubmit_at_expiration_of_prior_authorization",
    "3W": "prior_authorization_in_process",
    "3X": "authorization_number_not_found",
    "3Y": "prior_authorization_denied",
    40: "pharmacy_not_contracted_with_plan_on_date_of_service",
    41: "submit_bill_to_other_processor_or_primary_payer",
    "4C": "missing_or_invalid_coordination_of_benefits_other_payments_count",
    "4E": "missing_or_invalid_pri_dr_last_name",
    50: "non_matched_pharmacy_number",
    51: "non_matched_group_id",
    52: "non_matched_cardholder_id",
    53: "non_matched_person_code",
    54: "non_matched_prod_svc_id_number",
    55: "non_matched_product_package_size",
    56: "non_matched_dr_id",
    58: "non_matched_primary_prescriber",
    "5C": "missing_or_invalid_other_coverage_type",
    "5E": "missing_or_invalid_other_payer_reject_count",
    60: "product_service_not_covered_for_patient_age",
    61: "product_service_not_covered_for_patient_gender",
    62: "patient_card_holder_id_name_mismatch",
    63: "institutionalized_patient_prod_svc_id_not_covered",
    64: "claim_submitted_does_not_match_prior_authorization",
    65: "patient_is_not_covered",
    66: "patient_age_exceeds_maximum_age",
    67: "filled_before_coverage_effective",
    68: "filled_after_coverage_expired",
    69: "filled_after_coverage_terminated",
    "6C": "missing_or_invalid_other_id_qualifier",
    "6E": "missing_or_invalid_other_reject_code",
    70: "product_service_not_covered",
    71: "prescriber_is_not_covered",
    72: "primary_prescriber_is_not_covered",
    73: "refills_are_not_covered",
    74: "other_carrier_payment_meets_or_exceeds_payable",
    75: "prior_authorization_required",
    76: "plan_limitations_exceeded",
    77: "discontinued_prod_svc_id_number",
    78: "cost_exceeds_maximum",
    79: "refill_too_soon",
    "7C": "missing_or_invalid_other_id",
    "7E": "missing_or_invalid_dur_pps_code_counter",
    80: "drug_diagnosis_mismatch",
    81: "claim_too_old",
    82: "claim_is_post_dated",
    83: "duplicate_paid_captured_claim",
    84: "claim_has_not_been_paid_captured",
    85: "claim_not_processed",
    86: "submit_manual_reversal",
    87: "reversal_not_processed",
    88: "dur_reject_error",
    89: "rejected_claim_fees_paid",
    "8C": "missing_or_invalid_facility_id",
    "8E": "missing_or_invalid_dur_pps_loe",
    90: "host_hung_up",
    91: "host_response_error",
    92: "system_unavailable_host_unavailable",
    95: "time_out",
    96: "scheduled_downtime",
    97: "payer_unavailable",
    98: "connection_to_payer_is_down",
    99: "host_processing_error",
    A9: "missing_or_invalid_transaction_count",
    AA: "patient_spenddown_not_met",
    AB: "date_written_is_after_date_filled",
    AC: "product_not_covered_non_participating_manufacturer",
    AD: "billing_provider_not_eligible_to_bill_this_claim_type",
    AE: "qmb_qualified_medicare_beneficiary_bill_medicare",
    AF: "patient_enrolled_under_managed_care",
    AG: "day_supply_limitation_for_product_service",
    AH: "unit_dose_packaging_only_payable_for_nursing_home_recipients",
    AJ: "generic_drug_required",
    AK: "missing_or_invalid_software_vendor_certification_id",
    AM: "missing_or_invalid_segment_identification",
    B2: "missing_or_invalid_svc_prov_id_qualifier",
    BE: "missing_or_invalid_pro_svc_fee_sub",
    CA: "missing_or_invalid_patient_first_name",
    CB: "missing_or_invalid_patient_last_name",
    CC: "missing_or_invalid_cardholder_first_name",
    CD: "missing_or_invalid_cardholder_last_name",
    CE: "missing_or_invalid_home_plan",
    CF: "missing_or_invalid_employer_name",
    CG: "missing_or_invalid_employer_address",
    CH: "missing_or_invalid_employer_city",
    CI: "missing_or_invalid_employer_state",
    CJ: "missing_or_invalid_employer_zip",
    CK: "missing_or_invalid_employer_phone",
    CL: "missing_or_invalid_employer_contact_name",
    CM: "missing_or_invalid_patient_street_address",
    CN: "missing_or_invalid_patient_city_address",
    CO: "missing_or_invalid_patient_state_province_address",
    CP: "missing_or_invalid_patient_zip_postal_zone",
    CQ: "missing_or_invalid_patient_phone",
    CR: "missing_or_invalid_carrier_id",
    CW: "missing_or_invalid_alternate_id",
    CX: "missing_or_invalid_patient_id_qualifier",
    CY: "missing_or_invalid_patient_id",
    CZ: "missing_or_invalid_employer_id",
    DC: "missing_or_invalid_disp_fee_sub",
    DN: "missing_or_invalid_cost_basis",
    DQ: "missing_or_invalid_u_and_c_charge",
    DR: "missing_or_invalid_dr_last_name",
    DT: "missing_or_invalid_unit_dose_indicator",
    DU: "missing_or_invalid_gross_amount_due",
    DV: "missing_or_invalid_paid_amount",
    DX: "missing_or_invalid_pt_pd_amt_sub",
    DY: "missing_or_invalid_date_of_injury",
    DZ: "missing_or_invalid_claim_reference_id",
    E1: "missing_or_invalid_prod_svc_id_qualifier",
    E3: "missing_or_invalid_incv_amt_sub",
    E4: "missing_or_invalid_rsn_for_svc_code",
    E5: "missing_or_invalid_prf_svc_code",
    E6: "missing_or_invalid_rst_svc_code",
    E7: "missing_or_invalid_quantity_dispensed",
    E8: "missing_or_invalid_other_date",
    E9: "missing_or_invalid_provider_id",
    EA: "missing_or_invalid_og_rx_id",
    EB: "missing_or_invalid_og_rx_quantity",
    EC: "missing_or_invalid_compound_ingredient_component_count",
    ED: "missing_or_invalid_cmp_ing_qty",
    EE: "missing_or_invalid_cmp_ing_cost",
    EF: "missing_or_invalid_comp_dsg_fm_code",
    EG: "missing_or_invalid_comp_disp_unit",
    EH: "missing_or_invalid_compound_admin_route",
    EJ: "missing_or_invalid_og_rx_id_qualifier",
    EK: "missing_or_invalid_sched_rx_no",
    EM: "missing_or_invalid_rx_svc_no_ref_qualifier",
    EN: "missing_or_invalid_associated_rx_svc_no",
    EP: "missing_or_invalid_associated_rx_service_date",
    ER: "missing_or_invalid_proc_mod_code",
    ET: "missing_or_invalid_quantity_prescribed",
    EU: "missing_or_invalid_pa_type_code",
    EV: "missing_or_invalid_pa_no_submitted",
    EW: "missing_or_invalid_intermediary_authorization_type_id",
    EX: "missing_or_invalid_intermediary_authorization_id",
    EY: "missing_or_invalid_provider_id_qualifier",
    EZ: "missing_or_invalid_dr_id_qualifier",
    FO: "missing_or_invalid_plan_id",
    GE: "missing_or_invalid_per_sales_tax",
    H1: "missing_or_invalid_measurement_time",
    H2: "missing_or_invalid_measurement_dimension",
    H3: "missing_or_invalid_measurement_unit",
    H4: "missing_or_invalid_measurement_value",
    H5: "missing_or_invalid_primary_care_provider_location_code",
    H6: "missing_or_invalid_co_agt_id",
    H7: "missing_or_invalid_o_amt_sub_count",
    H8: "missing_or_invalid_o_amt_sub_qualifier",
    H9: "missing_or_invalid_o_amt_sub",
    HA: "missing_or_invalid_flat_tax_amt",
    HB: "missing_or_invalid_paid_amount_count",
    HC: "missing_or_invalid_paid_qualifier",
    HD: "missing_or_invalid_dispensing_status",
    HE: "missing_or_invalid_per_sales_tax_rate_used",
    HF: "missing_or_invalid_qty_to_disp",
    HG: "missing_or_invalid_ds_to_disp",
    J9: "missing_or_invalid_co_agt_id_qualifier",
    JE: "missing_or_invalid_sales_tax_basis",
    KE: "missing_or_invalid_coupon_type",
    M1: "patient_not_covered_in_this_aid_category",
    M2: "recipient_locked_in",
    M3: "host_pa_mc_error",
    M4: "rx_svc_no_time_limit_exceeded",
    M5: "requires_manual_claim",
    M6: "host_eligibility_error",
    M7: "host_drug_file_error",
    M8: "host_provider_file_error",
    ME: "missing_or_invalid_coupon_number",
    MZ: "error_overflow",
    NE: "missing_or_invalid_coupon_value_amount",
    NN: "transaction_rejected_at_switch_or_intermediary",
    P1: "associated_rx_svc_no_not_found",
    P2: "clinical_information_counter_out_of_sequence",
    P3: "compound_ingredient_component_count_does_not_match_number_of_repetitions",
    P4: "coordination_of_benefits_other_payments_count_does_not_match_number_of_repetitions",
    P5: "coupon_expired",
    P6: "date_of_service_prior_to_date_of_birth",
    P7: "dx_code_count_does_not_match_number_of_repetitions",
    P8: "dur_pps_code_counter_out_of_sequence",
    P9: "field_is_non_repeatable",
    PA: "pa_exhausted_not_renewable",
    PB: "invalid_transaction_count_for_this_transaction_code",
    PC: "missing_or_invalid_claim_segment",
    PD: "missing_or_invalid_clinical_segment",
    PE: "missing_or_invalid_cob_other_payments_segment",
    PF: "missing_or_invalid_compound_segment",
    PG: "missing_or_invalid_coupon_segment",
    PH: "missing_or_invalid_dur_pps_segment",
    PJ: "missing_or_invalid_insurance_segment",
    PK: "missing_or_invalid_patient_segment",
    PM: "missing_or_invalid_pharmacy_provider_segment",
    PN: "missing_or_invalid_prescriber_segment",
    PP: "missing_or_invalid_pricing_segment",
    PR: "missing_or_invalid_prior_authorization_segment",
    PS: "missing_or_invalid_transaction_header_segment",
    PT: "missing_or_invalid_workers_compensation_segment",
    PV: "non_matched_associated_rx_service_date",
    PW: "non_matched_employer_id",
    PX: "non_matched_other_id",
    PY: "non_matched_unit_form_admin_route",
    PZ: "non_matched_unit_of_measure_to_prod_svc_id",
    R1: "o_amt_sub_count_does_not_match_number_of_repetitions",
    R2: "other_payer_reject_count_does_not_match_number_of_repetitions",
    R3: "proc_mod_code_count_does_not_match_number_of_repetitions",
    R4: "proc_mod_code_invalid_for_prod_svc_id",
    R5: "prod_svc_id_must_be_zero_when_prod_svc_id_qualifier_equals_ø6",
    R6: "product_service_not_appropriate_for_this_location",
    R7: "repeating_segment_not_allowed_in_same_transaction",
    R8: "syntax_error",
    R9: "value_in_gross_amount_due_does_not_follow_pricing_formulae",
    RA: "pa_reversal_out_of_order",
    RB: "multiple_partials_not_allowed",
    RC: "different_drug_entity_between_partial_completion",
    RD: "mismatched_cardholder_group_id_partial_to_completion",
    RE: "missing_or_invalid_cmp_id_qualifier",
    RF: "improper_order_of_dispensing_status_code_on_partial_fill_transaction",
    RG: "missing_or_invalid_associated_rx_svc_no_on_completion_transaction",
    RH: "missing_or_invalid_associated_rx_service_date_on_completion_transaction",
    RJ: "associated_partial_fill_transaction_not_on_file",
    RK: "partial_fill_transaction_not_supported",
    RM: "completion_transaction_not_permitted_with_same_date_of_service_as_partial_transaction",
    RN: "plan_limits_exceeded_on_intended_partial_fill_values",
    RP: "out_of_sequence_p_reversal_on_partial_fill_transaction",
    RS: "missing_or_invalid_associated_rx_service_date_on_partial_transaction",
    RT: "missing_or_invalid_associated_rx_svc_no_on_partial_transaction",
    RU: "mandatory_data_elements_must_occur_before_optional_data_elements_in_a_segment",
    SE: "missing_or_invalid_proc_mod_code_count",
    TE: "missing_or_invalid_cmp_id",
    UE: "missing_or_invalid_cmp_ing_cost_basis",
    VE: "missing_or_invalid_dx_code_count",
    WE: "missing_or_invalid_dx_code_qualifier",
    XE: "missing_or_invalid_clinical_information_counter",
    ZE: "missing_or_invalid_measurement_date",
};
