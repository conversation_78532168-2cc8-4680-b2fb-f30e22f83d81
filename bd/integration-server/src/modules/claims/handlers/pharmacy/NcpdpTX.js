import {
    GROUP_SEPARATOR,
    SEGMENT_SEPARATOR,
    FIELD_SEPARATOR,
} from "./constants.js";
import { Segments } from "./NCPDPSegments.js";
import {
    parseFixedWidth,
    parseCodeMap,
    toFixedWidth,
    format<PERSON><PERSON><PERSON><PERSON><PERSON>,
    validate<PERSON><PERSON>er<PERSON><PERSON>,
} from "./funs.js";
import { readFileSync } from "fs";
const schema_url = new URL(
    "../../fixtures/schema/pharm/NCPDPSchema.json",
    import.meta.url
);
const NCPDPSchema = readFileSync(schema_url, "utf8");
import moment from "moment";
import { logger } from "../../../../utils/logger.js";

export class BaseTX {
    constructor({
        header = {},
        transmissionGroup = new TransmissionGroup(),
        transactionGroups = [],
        segments = [],
    }) {
        this.fieldschema = BaseTX.fieldschema;
        this.header = header;
        this.transmission_group = transmissionGroup;
        this.transaction_groups = transactionGroups;
        this.headerSchema = this.constructor.headerSchema;

        // Initialize logger directly
        this.log = logger.child({
            name: "Claims/NCPDP_TX",
            component: "NcpdpTX",
        });

        // Add this debug log
        this.log.debug({
            msg: "Constructor called",
            hasSegments: segments.length > 0,
            segmentsLength: segments.length,
        });

        if (segments.length > 0 && this.header?.transaction_code != "E1") {
            this.sortSegments(segments);
        }
    }
    static fieldschema = NCPDPSchema;
    static headerSchema = {};
    static TRANSMISSION_LEVEL_SEGMENTS = ["header", "insurance", "patient"];

    static RESPONSE_TRANSMISSION_LEVEL_SEGMENTS = [
        "response_header",
        "response_message",
        "response_insurance",
    ];

    static TRANSACTION_LEVEL_SEGMENTS = [
        "claim",
        "pricing",
        "pharmacy",
        "prescriber",
        "cob",
        "workers_compensation",
        "dur",
        "coupon",
        "compound",
        "clinical",
        "facility",
        "narrative",
    ];

    static RESPONSE_TRANSACTION_LEVEL_SEGMENTS = [
        "response_status",
        "response_claim",
        "response_pricing",
        "response_dur",
        "response_prior_authorization",
        "response_additional_documentation",
        "response_coord_of_benefits",
    ];

    static getSegmentRequirement(segmentSchema, transactionType) {
        if (!segmentSchema?.transaction_requirements) {
            return "N";
        }
        this.log.debug(
            `NCPDP Debug - Getting segment requirement for ${segmentSchema.field_code} with transaction type ${transactionType} - ${segmentSchema.transaction_requirements[transactionType] || "N"}`
        );
        return segmentSchema.transaction_requirements[transactionType] || "N";
    }

    static getSegmentOrder(schema, transactionType) {
        // Get all segments and their requirements for current transaction type
        const segments = Object.values(schema).filter((s) =>
            s.field_code?.includes("AM")
        );

        // Sort segments by:
        // 1. Mandatory (M) first
        // 2. Situational (S) second
        // 3. Not Required (N) last
        // Within each group, sort by default order number
        return segments.sort((a, b) => {
            const reqA = this.getSegmentRequirement(a, transactionType);
            const reqB = this.getSegmentRequirement(b, transactionType);

            // Priority order: M > S > N
            const priority = { M: 0, S: 1, N: 2 };

            if (priority[reqA] !== priority[reqB]) {
                return priority[reqA] - priority[reqB];
            }

            // If same requirement level, sort by default order
            return parseInt(a.default) - parseInt(b.default);
        });
    }

    parse(string) {
        this.log.debug({
            msg: "Parsing string",
            string: string,
        });
        const headerLength = Object.values(this.headerSchema).reduce(
            (a, b) => a + b,
            0
        );
        const header = parseFixedWidth(
            this.headerSchema,
            string.slice(0, headerLength)
        );
        this.log.debug({
            msg: "Parsed header",
            header: header,
        });
        // format date_of_service back to MM/DD/YYYY
        header.date_of_service = header.date_of_service
            ? moment(header.date_of_service, "YYYYMMDD").format("MM/DD/YYYY")
            : header.date_of_service;
        string = string.slice(headerLength);
        const rawTransactionGroups = string.split(GROUP_SEPARATOR);
        const rawTransmissionGroup = rawTransactionGroups.shift();

        const transmissionGroup = TransmissionGroup.parse(rawTransmissionGroup);
        const transactionGroups = rawTransactionGroups.map(
            (rawTransactionGroup) => TransactionGroup.parse(rawTransactionGroup)
        );

        return new BaseTX({
            header: header,
            transmissionGroup: transmissionGroup,
            transactionGroups: transactionGroups,
        });
    }

    getSegment(segmentClass) {
        let segment =
            this.transmission_group.segments.find(
                (s) => s instanceof segmentClass
            ) ||
            this.transaction_groups[0].segments.find(
                (s) => s instanceof segmentClass
            );

        if (!segment) {
            segment = new segmentClass({});
            this.segments.push(segment);
        }

        return segment;
    }

    sortSegments(segments) {
        // Clear existing segments
        this.transmission_group.segments = [];
        this.transaction_groups[0].segments = [];

        // Separate transmission level segments (patient, insurance)
        const transmissionSegments = segments.filter((segment) =>
            BaseTX.TRANSMISSION_LEVEL_SEGMENTS.includes(
                segment.constructor.symbol
            )
        );
        this.transmission_group.segments = transmissionSegments;

        // Get transaction segments
        const transactionSegments = segments.filter(
            (segment) =>
                !BaseTX.TRANSMISSION_LEVEL_SEGMENTS.includes(
                    segment.constructor.symbol
                )
        );

        // Find claim segment (AM07) and other transaction segments
        const claimSegment = transactionSegments.find(
            (s) => s.constructor.symbol === "claim"
        );
        const otherTransactionSegments = transactionSegments.filter(
            (s) => s.constructor.symbol !== "claim"
        );

        // Add claim segment first, then other transaction segments
        if (claimSegment) {
            this.transaction_groups[0].segments.push(claimSegment);
        }
        this.transaction_groups[0].segments.push(...otherTransactionSegments);

        this.log.debug({
            msg: "Segments sorted",
            transmissionGroupCount: this.transmission_group.segments.length,
            transactionGroupCount: this.transaction_groups[0].segments.length,
            transmissionSegments: this.transmission_group.segments.map(
                (s) => s.constructor.symbol
            ),
            transactionSegments: this.transaction_groups[0].segments.map(
                (s) => s.constructor.symbol
            ),
        });
    }

    get segments() {
        let all = [];
        all = all.concat(this.transmission_group.segments);
        all = all.concat(
            this.transaction_groups.flatMap((group) => group.segments)
        );
        return all.filter((segment) => segment !== null);
    }
    set segments(segments) {
        segments.forEach((segment) => this.segments.push(segment));
    }
    toString() {
        // Start with header
        let string = toFixedWidth(this.header, this.headerSchema);

        // Add segment separator after header
        string += SEGMENT_SEPARATOR;

        // Add transmission segments (insurance, patient, etc)
        const transmissionString = this.transmission_group.segments
            .map((segment) => segment.toString())
            .join("");

        if (transmissionString) {
            string += transmissionString;
        }

        // If we have transaction segments, add group separator then field separator
        if (this.transaction_groups?.[0]?.segments?.length > 0) {
            // Remove any segment separator before adding group separator
            string = string.replace(/\x1E$/, "");
            string += GROUP_SEPARATOR + SEGMENT_SEPARATOR + FIELD_SEPARATOR; // <1D><1E><1C>

            // Add transaction segments starting with AM07
            const transactionString = this.transaction_groups[0].segments
                .map((segment, index) => {
                    const segStr = segment.toString().replace(/\x1E$/, ""); // Remove trailing segment separator
                    // Only remove field separator for first segment (AM07) since we added it above
                    return index === 0 && segStr.startsWith(FIELD_SEPARATOR)
                        ? segStr.slice(1)
                        : segStr;
                })
                .join(SEGMENT_SEPARATOR);

            if (transactionString) {
                string += transactionString;
            }
        }

        // Remove any trailing segment separator
        return string.replace(/\x1E$/, "");
    }

    fromJSON(data) {
        if (!data.header) {
            data.header = {};
        }

        // Move non-header fields to header if they belong there
        Object.keys(data).forEach((key) => {
            if (
                key !== "_meta" &&
                key !== "header" &&
                typeof data[key] !== "object"
            ) {
                if (!this.headerSchema[key]) {
                    delete data[key];
                    return;
                }
                data.header[key] = data[key];
                delete data[key];
            } else if (key.startsWith("segment_")) {
                const newKey = key.replace("segment_", "");
                data[newKey] = data[key];
                delete data[key];
            }
        });

        const header_defaults = {
            version_number: "D0",
            software: "D012500028",
        };

        // Merge defaults with provided header
        data.header = { ...header_defaults, ...data.header };

        // Format and validate each header field
        const formattedHeader = {};
        for (const [fieldName, length] of Object.entries(this.headerSchema)) {
            if (!data.header[fieldName]) {
                throw new Error(`Missing required header field: ${fieldName}`);
            }

            // Format the field according to spec
            const formattedValue = formatHeaderField(
                data.header[fieldName],
                fieldName,
                length
            );

            // Validate the formatted length
            validateHeaderField(fieldName, formattedValue, length);

            formattedHeader[fieldName] = formattedValue;
        }

        // Replace the header with formatted values
        data.header = formattedHeader;

        this.log.debug({
            msg: "Formatted header fields",
            header: formattedHeader,
            headerString: Object.values(formattedHeader).join(""),
            expectedTotalLength: Object.values(this.headerSchema).reduce(
                (a, b) => a + b,
                0
            ),
        });

        const header = data.header;
        const keys = Object.keys(data).filter(
            (key) => key !== "header" && key !== "_meta"
        );

        keys.sort(
            (a, b) =>
                data[a].segment_identification - data[b].segment_identification
        );

        const sortedData = keys.reduce(
            (obj, key) => {
                if (Array.isArray(data[key])) {
                    obj[key] = data[key][0];
                } else {
                    obj[key] = data[key];
                }
                return obj;
            },
            { header: header }
        );

        const transactionType = data.header?.transaction_code;
        this.log.debug(`Transaction Type: ${transactionType}`);

        const segments = Segments.build_from_object(sortedData);

        // Use transaction type when getting segment order
        const orderedSegments = this.constructor.getSegmentOrder(
            this.fieldschema,
            transactionType
        );

        this.log.debug(
            `Built segments: ${segments
                .map((s) =>
                    JSON.stringify({
                        symbol: s.symbol,
                        identifier: s.constructor?.segment_identifier,
                        constructor: s.constructor?.name,
                        raw: s,
                    })
                )
                .join(", ")}`
        );

        // make sure header formatted correctly for date fields
        for (const [_key, initialValue] of Object.entries(header)) {
            const key = _key;
            let value = initialValue;

            if (key === "date_of_service") {
                this.log.debug(`Formatting date_of_service: ${key} - ${value}`);
                value = moment(value).format("YYYYMMDD");
                header[key] = value;
            }
        }

        // Create the transmission group
        const transmissionGroup = TransmissionGroup.parseSegments(
            segments.filter((segment) =>
                BaseTX.TRANSMISSION_LEVEL_SEGMENTS.includes(
                    segment.constructor.symbol
                )
            )
        );

        // Create the transaction groups
        const transactionGroups = segments
            .filter((segment) =>
                BaseTX.TRANSACTION_LEVEL_SEGMENTS.includes(
                    segment.constructor.symbol
                )
            )
            .map((segment) => TransactionGroup.parseSegments([segment]));

        // Pass segments to the constructor
        return new Request({
            header: header,
            transmissionGroup: transmissionGroup,
            transactionGroups: transactionGroups,
            segments: segments,
        });
    }
    toJSON(options = {}) {
        if (options.mapkeys) {
            const hschema = this.constructor.fieldschema.header;
            for (const [key, value] of Object.entries(this.header)) {
                this.log.debug(
                    `Mapping HEADER key: ${key} with value: ${value}`
                );
                const fh = hschema?.filter((o) => o.field == key)[0];
                if (fh && fh.code_map) {
                    this.header[key] = parseCodeMap(value, fh.code_map);
                }
            }
        }
        if (options.flatten) {
            return {
                ...this.header,
                ...this.transmission_group.segments.reduce((seg, segment) => {
                    const segmentSymbol = segment.constructor.symbol;
                    return { ...seg, [segmentSymbol]: segment.toJSON(options) };
                }, {}),
                ...this.transaction_groups[0].segments.reduce(
                    (seg, segment) => {
                        const segmentSymbol = segment.constructor.symbol;
                        return {
                            ...seg,
                            [segmentSymbol]: segment.toJSON(options),
                        };
                    },
                    {}
                ),
            };
        }
        return {
            header: this.header,
            transmission_group: this.transmission_group.toJSON(options),
            transaction_groups: this.transaction_groups.map((group) =>
                group.toJSON(options)
            ),
        };
    }
}

export class Request extends BaseTX {
    constructor(data = {}) {
        super(data);
    }

    static headerSchema = {
        bin_number: 6,
        version_number: 2,
        transaction_code: 2,
        process_control_number: 10,
        transaction_count: 1,
        svc_prov_id_qualifier: 2,
        svc_prov_id: 15,
        date_of_service: 8,
        software: 10,
    };
}

export class Response extends BaseTX {
    constructor(data = {}) {
        super(data);
    }

    static TRANSMISSION_LEVEL_SEGMENTS = [
        "response_header",
        "response_message",
        "response_insurance",
        "response_patient",
    ];

    static TRANSACTION_LEVEL_SEGMENTS = [
        "response_status", // Mandatory first
        "response_claim", // Mandatory first
        "response_pricing", // Mandatory first
        "response_dur", // Situational
        "response_cob", // Situational
    ];

    static headerSchema = {
        version_number: 2, // 102-A2 VERSION/RELEASE NUMBER (D0)
        transaction_code: 2, // 103-A3 TRANSACTION CODE (B1)
        transaction_count: 1, // 129-A9 TRANSACTION COUNT (1)
        response_status: 1, // 501-F1 HEADER RESPONSE STATUS (A)
        svc_prov_id_qualifier: 2, // 202-B2 SERVICE PROVIDER ID QUALIFIER (01)
        svc_prov_id: 15, // 201-B1 SERVICE PROVIDER ID
        date_of_service: 8, // 401-D1 DATE OF SERVICE (CCYYMMDD)
    };

    static parse(string) {
        // Create new instance first
        const response = new Response();

        response.log.debug({
            msg: "Response parsing string",
            string: string,
        });

        // Parse header first
        const headerLength = Object.values(Response.headerSchema).reduce(
            (a, b) => a + b,
            0
        );
        const header = parseFixedWidth(
            Response.headerSchema,
            string.slice(0, headerLength)
        );

        // Format date if present
        if (header.date_of_service) {
            header.date_of_service = moment(
                header.date_of_service,
                "YYYYMMDD"
            ).format("MM/DD/YYYY");
        }

        response.header = header;
        string = string.slice(headerLength);

        // Split into transmission and transaction groups
        const rawTransactionGroups = string.split(GROUP_SEPARATOR);
        const rawTransmissionGroup = rawTransactionGroups.shift();

        response.transmission_group =
            TransmissionGroup.parse(rawTransmissionGroup);
        response.transaction_groups = rawTransactionGroups.map(
            (rawTransactionGroup) => TransactionGroup.parse(rawTransactionGroup)
        );

        response.log.debug({
            msg: "Parsed response",
            header: response.header,
            transmissionGroup: response.transmission_group,
            transactionGroups: response.transaction_groups,
        });

        return response;
    }
}

export class Groups {
    constructor(segments = []) {
        this.segments = segments;
    }

    static parse(string) {
        const rawSegments = string.split(SEGMENT_SEPARATOR);
        const segments = rawSegments
            .filter((rawSegment) => rawSegment.trim() !== "")
            .map((rawSegment) => Segments.parse(rawSegment));

        return new this(segments);
    }

    static parseSegments(segments) {
        return new this(segments);
    }

    toString() {
        return this.segments
            .map((segment) => segment.toString())
            .join(SEGMENT_SEPARATOR);
    }

    toJSON(options = {}) {
        if (options.groupsegments === true) {
            return this.segments.filter(Boolean).reduce((seg, segment) => {
                const segmentSymbol = segment.constructor.symbol;
                return { ...seg, [segmentSymbol]: segment.toJSON(options) };
            }, {});
        } else {
            return {
                segments: this.segments.map((segment) =>
                    segment.toJSON(options)
                ),
            };
        }
    }
}

export class TransmissionGroup extends Groups {
    constructor(segments = []) {
        super(segments);
    }
}

export class TransactionGroup extends Groups {
    constructor(segments = []) {
        super(segments);
    }
}

Object.entries(Segments.segment_id_to_symbol).forEach(
    ([segmentIdentifier, segmentNameSym]) => {
        const segmentClass =
            Segments.get_class_by_identifier(segmentIdentifier);

        Object.defineProperty(Groups.prototype, `${segmentNameSym}_segment`, {
            get: function () {
                return this.getSegment(segmentClass);
            },
        });
    }
);
