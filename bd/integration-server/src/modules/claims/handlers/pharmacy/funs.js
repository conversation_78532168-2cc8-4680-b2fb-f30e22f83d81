"use strict";

import moment from "moment";
import { logger } from "../../../../utils/logger.js";

// Create child logger with component name for NCPDP Parser
const log = logger.child({ component: "NCPDP Parser" });

export function flattenObject(data, flattenEverything = false) {
    const result = {};

    function flatten(obj) {
        const temp = {};
        for (const key in obj) {
            if (Array.isArray(obj[key])) {
                temp[key] = obj[key];
            } else if (typeof obj[key] === "object" && obj[key] !== null) {
                Object.assign(temp, flatten(obj[key]));
            } else {
                temp[key] = obj[key];
            }
        }
        return temp;
    }

    if (flattenEverything) {
        Object.assign(result, flatten(data));
    } else {
        for (const key in data) {
            if (key === "transmission_group") {
                for (const subKey in data[key]) {
                    result[subKey] = flatten(data[key][subKey]);
                }
            } else if (key === "transaction_groups") {
                data[key].forEach((item, index) => {
                    for (const subKey in item) {
                        if (!result[subKey]) {
                            result[subKey] = flatten(item[subKey]);
                        } else {
                            result[`${subKey}_${index}`] = flatten(
                                item[subKey]
                            );
                        }
                    }
                });
            } else {
                result[key] = flatten(data[key]);
            }
        }
    }

    return result;
}

export function parseCodeMap(code, code_map) {
    let result = "";
    for (const key in code_map) {
        if (key === code) {
            const value = code_map[key];
            if (value && typeof value === "string") {
                // If the value is a string, just append it to the result
                result = value;
            } else if (value && typeof value === "object") {
                // If the value is an object, generate a string for each property

                result = value;
            }
        }
    }
    return result;
}

export const parseFixedWidth = (fields, string) => {
    const headers = Object.keys(fields);
    const fieldSizes = Object.values(fields);
    const values = [];
    let currentIndex = 0;

    for (const size of fieldSizes) {
        const value = string.slice(currentIndex, currentIndex + size).trim();
        values.push(value);
        currentIndex += size;
    }

    return Object.fromEntries(headers.map((header, i) => [header, values[i]]));
};

export const toFixedWidth = (source, schema) => {
    return Object.entries(schema)
        .map(([field, size]) => {
            const value = source[field] || "";
            return value.toString().padEnd(size, " ");
        })
        .join("");
};

export function reverseObject(object) {
    return Object.fromEntries(
        Object.entries(object).map(([key, value]) => [value, key])
    );
}

// Numeric Field Processing
export function reverseFormatField(formattedValue, lcode, fieldName = "") {
    log.debug(
        `Field: ${fieldName}, Input: ${formattedValue}, Format: ${lcode}`
    );

    if (!formattedValue || !lcode) {
        log.error(`Missing input for field ${fieldName}`);
        return formattedValue;
    }

    // Handle date fields
    if (
        (lcode === "9(8)" && formattedValue.length === 8) ||
        fieldName.includes("date")
    ) {
        try {
            const date = moment(formattedValue);
            if (date.isValid()) {
                const result = date.format("MM/DD/YYYY");
                log.debug(`📅 Date: ${formattedValue} → ${result}`);
                return result;
            }
        } catch (error) {
            log.error(`❌ Date error: ${error.message}`);
        }
    }

    // Handle datetime fields
    if (lcode === "9(14)" && formattedValue.length === 14) {
        try {
            const datetime = moment(formattedValue, "YYYYMMDDHHmmss");
            if (datetime.isValid()) {
                const result = datetime.format("YYYY-MM-DD HH:mm:ss");
                log.debug(`📅 DateTime: ${formattedValue} → ${result}`);
                return result;
            }
        } catch (error) {
            log.error(`❌ DateTime error: ${error.message}`);
        }
    }

    // Handle alphanumeric fields
    if (lcode.includes("x") || lcode.includes("X")) {
        const result = formattedValue.trim();
        log.debug(`📝 Alpha: ${formattedValue} → ${result}`);
        return result;
    }

    // Handle numeric fields
    if (lcode.includes("9")) {
        const hasSign = lcode.startsWith("s");
        const hasDecimals = lcode.includes("v");

        try {
            const result = hasSign
                ? processSignedNumeric(formattedValue, lcode, fieldName)
                : processUnsignedNumeric(formattedValue, lcode, fieldName);

            log.debug(`🔢 Numeric: ${formattedValue} → ${result}`);
            return result;
        } catch (error) {
            log.error(`❌ Numeric error: ${error.message}`);
            return formattedValue;
        }
    }

    return formattedValue;
}

function processSignedNumeric(value, lcode, fieldName) {
    const { value: numericValue, sign } = Overpunch.extract(value);
    log.debug(`🔍 Extracted from overpunch:
    Value: ${numericValue}
    Sign:  ${sign}`);

    if (lcode.includes("v")) {
        const [intPart, decPart] = lcode.split("v");
        let decLen;
        if (decPart === "4") {
            decLen = 4;
        } else {
            decLen = (decPart.match(/9/g) || []).length;
        }

        const adjustedValue = parseInt(numericValue) / Math.pow(10, decLen);
        const finalValue = sign === "-" ? -adjustedValue : adjustedValue;

        log.debug(
            `🔢 Decimal conversion: field ${fieldName}, value ${numericValue}, decimals ${decLen}, final value ${finalValue}`
        );

        return finalValue;
    }

    return sign === "-" ? -parseInt(numericValue) : parseInt(numericValue);
}

function processUnsignedNumeric(value, lcode, fieldName) {
    if (lcode.includes("v")) {
        const [, decPart] = lcode.split("v");
        const decLen = (decPart.match(/[0-9]/g) || []).length;
        const numericValue = value.replace(/^0+/, "");

        log.debug(
            `🔢 Processing unsigned decimal: field ${fieldName}, value ${value}, decimals ${decLen}`
        );

        if (numericValue.endsWith("-")) {
            const val =
                parseFloat(numericValue.slice(0, -1)) / Math.pow(10, decLen);
            log.debug(`➖ Negative value detected: ${-val}`);
            return -val;
        }

        const result = parseFloat(numericValue) / Math.pow(10, decLen);
        log.debug(`✅ Decimal value: ${result}`);
        return result;
    }

    const result = parseInt(value.replace(/^0+/, "") || "0");
    log.debug(`✅ Integer value: ${result}`);
    return result;
}

export function formatField(value, lcode, length) {
    const tmap = function (lcode) {
        return lcode.startsWith("s9")
            ? "signed"
            : lcode.startsWith("9")
              ? "unsigned"
              : "alphanumeric";
    };
    log.debug(
        `🔄 Format: ${value} | Code: ${lcode} | Length: ${length} | Type: ${tmap(lcode)}`
    );

    if (value === undefined || value === null) {
        log.debug("Null or undefined value, returning empty string");
        return "";
    }

    value = String(value);
    if (!lcode) {
        log.debug("No format code provided, returning original value");
        return value;
    }

    try {
        let result;
        if (lcode.startsWith("s9")) {
            result = formatSignedNumeric(value, lcode);
        } else if (lcode.startsWith("9")) {
            result = formatUnsignedNumeric(value, lcode, length);
        } else if (lcode.includes("x") || lcode.includes("X")) {
            result = formatAlphanumeric(value, lcode);
        }
        log.debug(`✅ Formatted: ${value} → ${result}`);

        return result;
    } catch (error) {
        log.error(`❌ Format error: ${error.message}`);
        return value;
    }
}

function formatSignedNumeric(value, lcode) {
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
        throw new Error(`Invalid numeric value: ${value}`);
    }

    if (lcode.includes("v")) {
        const [intPart, decPart] = lcode.split("v");
        let decLen;
        if (decPart === "4") {
            decLen = 4; // Special case for v4
        } else {
            decLen = (decPart.match(/9/g) || []).length; // Normal case for v99
        }

        const plainValue = numericValue
            .toFixed(decLen)
            .replace(".", "")
            .replace(/^0+(?=\d)/, ""); // Only remove leading zeros if there are other digits

        // If we ended up with an empty string (all zeros), return "0"
        if (!plainValue) {
            return "0";
        }

        return Overpunch.format(plainValue);
    }

    return Overpunch.format(value.replace(/^0+(?=\d)/, "")); // Same fix for non-decimal case
}

function formatUnsignedNumeric(value, lcode, length) {
    if (lcode.includes("v")) {
        const [intPart, decPart] = lcode.split("v");
        const intLen = parseInt(intPart.match(/\d+/)[0]);
        const decLen = (decPart.match(/9/g) || []).length;

        let sign = "";
        if (parseFloat(value) < 0) {
            value = String(Math.abs(parseFloat(value)));
            sign = "-";
        }

        const [intVal, decVal] = value.padStart(intLen, "0").split(".");
        if (decVal) {
            return `${intVal.replace(/^0+/, "")}${decVal.padEnd(decLen, "0")}${sign}`;
        }
        return `${intVal.replace(/^0+/, "")}${sign}`;
    }
    /* if (lcode === "9(4)" && value.length === 4) {
        try {
            const timefield = moment(value, "HHmm");
            if (timefield.isValid()) {
                return timefield.format("HH:mm");
            }
        } catch (error) {
            logger.error(`❌ Error processing time: ${error.message}`);
        }
    }*/
    if (lcode === "9(8)") {
        try {
            const posdate = moment(value);
            if (posdate.isValid()) {
                return posdate.format("YYYYMMDD");
            }
        } catch (error) {
            logger.error(`❌ Error processing date: ${error.message}`);
        }
    }

    return value
        .toString()
        .padStart(length || parseInt(lcode.match(/\d+/)[0]), "0");
}

function formatAlphanumeric(value, lcode) {
    const len = parseInt(lcode.match(/\d+/)[0]);
    return value.padEnd(len, " ");
}

// Overpunch Class
export class Overpunch {
    static EXTRACT_REF = new Map([
        ["0", ["+", "0"]],
        ["1", ["+", "1"]],
        ["2", ["+", "2"]],
        ["3", ["+", "3"]],
        ["4", ["+", "4"]],
        ["5", ["+", "5"]],
        ["6", ["+", "6"]],
        ["7", ["+", "7"]],
        ["8", ["+", "8"]],
        ["9", ["+", "9"]],
        ["{", ["+", "0"]],
        ["A", ["+", "1"]],
        ["B", ["+", "2"]],
        ["C", ["+", "3"]],
        ["D", ["+", "4"]],
        ["E", ["+", "5"]],
        ["F", ["+", "6"]],
        ["G", ["+", "7"]],
        ["H", ["+", "8"]],
        ["I", ["+", "9"]],
        ["}", ["-", "0"]],
        ["J", ["-", "1"]],
        ["K", ["-", "2"]],
        ["L", ["-", "3"]],
        ["M", ["-", "4"]],
        ["N", ["-", "5"]],
        ["O", ["-", "6"]],
        ["P", ["-", "7"]],
        ["Q", ["-", "8"]],
        ["R", ["-", "9"]],
    ]);

    static FORMAT_REF = new Map([
        [["+", "0"], "{"],
        [["+", "1"], "A"],
        [["+", "2"], "B"],
        [["+", "3"], "C"],
        [["+", "4"], "D"],
        [["+", "5"], "E"],
        [["+", "6"], "F"],
        [["+", "7"], "G"],
        [["+", "8"], "H"],
        [["+", "9"], "I"],
        [["-", "0"], "}"],
        [["-", "1"], "J"],
        [["-", "2"], "K"],
        [["-", "3"], "L"],
        [["-", "4"], "M"],
        [["-", "5"], "N"],
        [["-", "6"], "O"],
        [["-", "7"], "P"],
        [["-", "8"], "Q"],
        [["-", "9"], "R"],
    ]);

    static extract(value) {
        const lastChar = value.slice(-1);
        const [sign, digit] = this.EXTRACT_REF.get(lastChar) || ["+", lastChar];
        return {
            value: parseInt(value.slice(0, -1) + digit),
            sign,
        };
    }

    static format(value) {
        const parts = value.split("");
        const lastDigit = parts.pop();
        const sign = value.startsWith("-") ? "-" : "+";

        for (const [key, value] of this.FORMAT_REF) {
            if (key[0] === sign && key[1] === lastDigit) {
                parts[parts.length] = value;
                break;
            }
        }

        return parts.join("");
    }
}

export function formatHeaderField(value, fieldName, length) {
    if (value === undefined || value === null) {
        return "".padEnd(length, " ");
    }

    value = String(value);

    // All header fields in both Request and Response must be exact length
    // Preserve any leading spaces, pad trailing spaces to reach exact length
    switch (fieldName) {
        case "bin_number":
            // Request only - should be exact 6 chars
            return value;
        case "version_number":
            // Both Request (D0) and Response - 2 chars
            return value.padEnd(length, " ");
        case "transaction_code":
            // Both Request (B1) and Response - 2 chars
            return value.padEnd(length, " ");
        case "process_control_number":
            // Request only - 10 chars
            return value.padEnd(length, " ");
        case "transaction_count":
            // Both Request and Response - 1 char
            return value.padEnd(length, " ");
        case "service_provider_id_qualifier":
        case "svc_prov_id_qualifier":
            // Both Request and Response - 2 chars
            return value.padEnd(length, " ");

        case "service_provider_id":
        case "svc_prov_id":
            // Both Request and Response - 15 chars
            return value.padEnd(length, " ");

        case "date_of_service":
            // Both Request and Response - 8 chars YYYYMMDD
            if (value.includes("/")) {
                value = moment(value, "MM/DD/YYYY").format("YYYYMMDD");
            }
            return value.padEnd(length, " ");
        case "software":
            // Request only - 10 chars
            return value.padEnd(length, " ");
        case "header_response_status":
            // Response only - 1 char
            return value.padEnd(length, " ");
        default:
            return value.padEnd(length, " ");
    }
}

export function validateHeaderField(fieldName, value, expectedLength) {
    const actualLength = value.length;
    if (actualLength !== expectedLength) {
        throw new Error(
            `Header field ${fieldName} has incorrect length. Expected ${expectedLength}, got ${actualLength}. Value: "${value}"`
        );
    } else {
        logger.debug(
            `✅ Header field ${fieldName} is valid length: ${expectedLength}`
        );
    }
}
