"use strict";
import { FIELD_SEPARATOR, SEGMENT_SEPARATOR } from "./constants.js";
import { parseCodeMap, flattenObject } from "./funs.js";
import { reverseObject, formatField, reverseFormatField } from "./funs.js";
import moment from "moment";
import { readFileSync } from "fs";
const schema_url = new URL(
    "../../fixtures/schema/pharm/NCPDPSchema.json",
    import.meta.url
);
import { logger } from "../../../../utils/logger.js";
const NCPDPSchema = readFileSync(schema_url, "utf8");

export class Segments {
    constructor(data = {}) {
        this.header = data.header;
        delete data.header;
        this.data = data;
        this.fieldschema = Segments.fieldschema;
        this.header = data.header;

        // Instance logger
        this.log = logger.child({
            component: "Segments",
        });
        delete this.data.header;
    }

    static fieldschema = JSON.parse(NCPDPSchema);
    static segment_id_to_class = {};
    static segment_id_to_symbol = {};

    static register_segment(cls) {
        const identifier = cls.segment_identifier;
        this.segment_id_to_class[identifier] = cls;
        this.segment_id_to_symbol[identifier] = cls.symbol;
        this[cls.symbol] = cls;
    }

    get_field_types(_segment_id) {
        const schema = this.fieldschema[this.segment_id_to_symbol[_segment_id]];
        const fields = {};
        const map_fields = schema?.filter((o) => Object.hasOwn(o, "code_map"));
        const date_fields = schema?.filter((o) => o.type == "date");
        const count_fields = this.get_count_info(_segment_id);
        const required_fields = schema?.filter(
            (o) =>
                o.segment_requirements &&
                _segment_id in o.segment_requirements &&
                (o.segment_requirements[_segment_id] == "M" ||
                    o.segment_requirements[_segment_id] == "R")
        );
        fields["count_fields"] = count_fields;
        fields["date_fields"] = date_fields;
        fields["map_fields"] = map_fields;
        fields["required_fields"] = required_fields;
        return fields;
    }

    get_field_schema(key) {
        if (Object.hasOwn(this.fieldschema, this.constructor.symbol)) {
            return this.fieldschema[this.constructor.symbol].find((o) =>
                o.field_code.includes(key)
            );
        } else {
            return null;
        }
    }
    static get field_id_to_symbol() {
        return { AM: "segment_identification" };
    }

    static get symbol_to_field_id() {
        return reverseObject(this.field_id_to_symbol);
    }

    static get_symbol_by_field(sym) {
        const mapping = this.field_id_to_symbol;
        return mapping[sym];
    }
    static validateData(data) {
        const errs = [];
        const segmentSchema = this.fieldschema[this.symbol];
        const tx_type = data?.header?.transaction_code;
        const requiredFields = this.get_required_fields(tx_type, segmentSchema);

        for (const field of requiredFields) {
            const fcode = field.field_code.split("-")[1];
            // Use static class logger
            console.error(
                "validating data for " +
                    this.symbol +
                    " with tx type " +
                    tx_type +
                    " and required fields " +
                    field.field
            );
            const e = {};
            if (!Object.hasOwn(data, fcode)) {
                e["segment"] = this.symbol;
                e["field"] = fcode;
                e["tx_type"] = tx_type;
                e["error"] =
                    `Field (${fcode}) ${field.field} is required for transaction type ${tx_type}`;
                errs.push(e);
                console.error(errs);
            }
        }
        if (errs.length > 0) {
            throw new Error(JSON.stringify(errs));
        }
    }

    static get_required_fields(tx_type, schema) {
        //      const schema = this.fieldschema[this.symbol];
        const fields = schema?.filter(
            (o) =>
                o.segment_requirements &&
                tx_type in o.segment_requirements &&
                (o.segment_requirements[tx_type] == "M" ||
                    o.segment_requirements[tx_type] == "R")
        );
        return fields;
    }

    static get_count_info(segment_identifier) {
        const schema =
            this.fieldschema[this.segment_id_to_symbol[segment_identifier]];
        if (!schema) {
            return {
                count_fields: [],
                subform_fields: {},
            };
        }

        const fref = {
            count_fields: [],
            subform_fields: {},
        };

        // Only process if we have a schema
        const fields = schema.filter(
            (f) => f.field?.includes("_count") || f.type === "total_count"
        );

        schema.forEach((f) => {
            // Handle count fields
            if (f.field?.includes("_count") || f.type === "total_count") {
                fref.count_fields.push(f.field);
            }

            // Handle subform fields
            if (f.subform_field) {
                if (!fref.subform_fields[f.subform_field]) {
                    fref.subform_fields[f.subform_field] = [];
                }
                const fcode = f.field_code.split("-")[1];
                fref.subform_fields[f.subform_field].push(fcode);
            }
        });

        fields.forEach((f) => {
            // Preserve existing logic for ref_field and continuity
            const base_field = f.field.split("_count")[0];
            const urefs = (f.field.match(/_/g) || []).length;
            let ref_field =
                urefs && urefs > 1
                    ? base_field
                    : f.qualifier_field
                      ? f.qualifier_field
                      : null;
            if (urefs && urefs === 1) {
                for (const t of ["code", "id"]) {
                    const testf = base_field + "_" + t;
                    const testfschema = schema.filter((o) => o.field == testf);
                    if (testfschema && testfschema.length > 0) {
                        ref_field = testf;
                        break;
                    }
                }
            }
            const refschema = schema.filter((o) => o.field == ref_field)?.[0];
            if (refschema) {
                const scode = refschema.field_code.split("-")[1];
                fref[scode] = refschema;
                fref[scode]["count_field_schema"] = f;
                fref[scode]["continuity"] = f.continuity_field;
            }
        });

        return fref;
    }

    static get_class_by_identifier(identifier) {
        return this.segment_id_to_class[identifier] || this;
    }

    static build(data = {}) {
        let given_identifier = data["AM"];
        if (!given_identifier) {
            given_identifier = data["segment_identification"];
        }
        const segment_class = this.get_class_by_identifier(given_identifier);
        if (segment_class) {
            return new segment_class(data);
        } else {
            return new this(data);
        }
    }

    static build_from_object(obj = {}) {
        const flattenedObj = flattenObject(obj);
        const builtSegments = [];

        for (const segment in flattenedObj) {
            if (segment === "header") continue;

            const segmentId = flattenedObj[segment].segment_identification;
            if (this.segment_id_to_class[segmentId]) {
                const segment_class = this.segment_id_to_class[segmentId];
                const data = { header: obj.header };
                const processedFields = new Set();

                const schema = segment_class.fieldschema[segment_class.symbol];
                const countInfo = segment_class.get_count_info(segmentId);

                // Handle both regular arrays and subform arrays
                Object.entries(obj[segment]).forEach(
                    ([fieldName, fieldValue]) => {
                        // Handle regular arrays first
                        if (
                            Array.isArray(fieldValue) &&
                            !fieldName.startsWith("subform_")
                        ) {
                            const fieldId =
                                segment_class.symbol_to_field_id[fieldName];
                            if (fieldId) {
                                data[fieldId] = fieldValue;
                                processedFields.add(fieldId);
                            }
                        }
                        // Handle subform arrays
                        else if (
                            fieldName.startsWith("subform_") &&
                            Array.isArray(fieldValue)
                        ) {
                            // Find the count field for this subform
                            const countField = schema.find(
                                (f) =>
                                    f.subform_field === fieldName &&
                                    (f.type === "total_count" ||
                                        f.field?.includes("_count"))
                            );

                            if (countField) {
                                const counterId =
                                    countField.field_code.split("-")[1];
                                data[counterId] = fieldValue.length.toString();
                                processedFields.add(counterId);

                                // Process each field in the subform
                                fieldValue.forEach((item) => {
                                    Object.entries(item).forEach(
                                        ([subFieldName, subFieldValue]) => {
                                            const fieldId =
                                                segment_class
                                                    .symbol_to_field_id[
                                                    subFieldName
                                                ];
                                            if (fieldId) {
                                                if (
                                                    !Array.isArray(
                                                        data[fieldId]
                                                    )
                                                ) {
                                                    data[fieldId] = [];
                                                }
                                                data[fieldId].push(
                                                    subFieldValue
                                                );
                                                processedFields.add(fieldId);
                                            }
                                        }
                                    );
                                });
                            }
                        }
                    }
                );

                // Handle regular fields
                for (const fieldSymbol in segment_class.symbol_to_field_id) {
                    if (flattenedObj[segment].hasOwnProperty(fieldSymbol)) {
                        const fieldId =
                            segment_class.symbol_to_field_id[fieldSymbol];
                        if (!processedFields.has(fieldId)) {
                            data[fieldId] = flattenedObj[segment][fieldSymbol];
                        }
                    }
                }

                if (segment_class) {
                    // Use static class logger
                    console.error({
                        msg: `validating data for ${segment_class.symbol} built segment`,
                        data,
                    });
                    segment_class.validateData(data);
                    builtSegments.push(new segment_class(data));
                }
            }
        }
        return builtSegments;
    }

    static parse(source) {
        const elements = source
            .split(/[\x1C\x1E\x03\x1D]/)
            .map((e) => e.trim())
            .filter((e) => e);
        const segmentData = {};
        const segident = {};

        // First pass to identify segment and get count info
        for (const element of elements) {
            const key = element.substring(0, 2);
            const value = element.substring(2);
            if (key == "AM") {
                segident.segment_id = value;
                const sc = this.get_class_by_identifier(value);
                if (sc) {
                    segident.segment_class = sc;
                }
                const cinfo = this.get_count_info(value);
                if (cinfo && Object.keys(cinfo).length > 0) {
                    segident.count_info = cinfo;
                    if (cinfo.subform_fields) {
                        segident.subform_fields = cinfo.subform_fields;
                    }
                }
            }
        }

        // Identify counter fields and associated fields
        const countersAndFields = {};
        if (
            segident.segment_class &&
            this.fieldschema[segident.segment_class.symbol]
        ) {
            const schema = this.fieldschema[segident.segment_class.symbol];
            // Find count fields and their related fields
            schema.forEach((field) => {
                if (field.type === "total_count" && field.subform_field) {
                    const countFieldId = field.field_code.split("-")[1];
                    const subformName = field.subform_field;

                    if (!countersAndFields[countFieldId]) {
                        countersAndFields[countFieldId] = [];
                    }

                    // Find all fields associated with this counter
                    schema.forEach((relatedField) => {
                        if (
                            relatedField.counter_field === field.field_code &&
                            relatedField.subform_field === subformName
                        ) {
                            const fieldId =
                                relatedField.field_code.split("-")[1];
                            countersAndFields[countFieldId].push(fieldId);
                        }
                    });
                }
            });
        }

        // Second pass to process elements
        for (const element of elements) {
            const key = element.substring(0, 2);
            const value = element.substring(2);

            // Check if this field has a continuity marker
            const hasContinuity =
                segident?.count_info &&
                key in segident.count_info &&
                segident.count_info[key].continuity;

            // Check if this field should be treated as an array (but not if it has continuity)
            const shouldBeArray =
                !hasContinuity &&
                ((segident?.count_info &&
                    key in segident.count_info &&
                    !segident.count_info[key].continuity) ||
                    Object.values(countersAndFields).some((fields) =>
                        fields.includes(key)
                    ));

            if (!Object.hasOwn(segmentData, key)) {
                // First occurrence of field
                if (shouldBeArray) {
                    segmentData[key] = [value];
                } else {
                    segmentData[key] = value;
                }
            } else if (hasContinuity) {
                // Concatenate continuity fields with a space
                segmentData[key] = segmentData[key].concat(" ", value);
            } else if (shouldBeArray) {
                // Add to array for non-continuity repeating fields
                if (Array.isArray(segmentData[key])) {
                    segmentData[key].push(value);
                } else {
                    // Convert to array if not already
                    segmentData[key] = [segmentData[key], value];
                }
            } else {
                // For non-repeating fields, just replace the value
                segmentData[key] = value;
            }
        }

        return this.build(segmentData);
    }
    toString() {
        const segments = [];

        // Add segment identifier
        if (this.data.segment_identification) {
            segments.push("AM" + this.data.segment_identification);
        } else if (this.constructor.segment_identifier) {
            segments.push("AM" + this.constructor.segment_identifier);
        }

        const schema = this.fieldschema[this.constructor.symbol];
        const countInfo = this.constructor.get_count_info(
            this.constructor.segment_identifier
        );

        // Get ordered fields, excluding AM
        const orderedFields = Object.entries(
            this.constructor.field_id_to_symbol
        )
            .map(([id]) => id)
            .filter((id) => id !== "AM");

        // First process non-subform fields
        orderedFields.forEach((fieldId) => {
            if (this.data[fieldId] !== undefined) {
                const fieldSchema = schema?.find(
                    (f) => f.field_code.split("-")[1] === fieldId
                );

                // Skip subform fields - we'll handle them separately
                if (fieldSchema?.subform_field) {
                    this.log.debug({
                        msg: "Skipping subform field for now",
                        fieldId,
                        subformField: fieldSchema.subform_field,
                    });
                    return;
                }

                if (Array.isArray(this.data[fieldId])) {
                    // Handle regular array fields (non-subform)
                    const countSchema = schema?.find(
                        (f) =>
                            (f.type === "total_count" ||
                                f.field?.includes("_count")) &&
                            f.qualifier_field === fieldSchema?.field
                    );

                    if (countSchema) {
                        const counterId = countSchema.field_code.split("-")[1];
                        if (!segments.some((s) => s.startsWith(counterId))) {
                            segments.push(
                                counterId + this.data[fieldId].length
                            );
                        }
                    }

                    this.data[fieldId].forEach((value) => {
                        const ks = this.get_field_schema(fieldId);
                        const needsFormatting =
                            ks?.length_code != null && fieldId !== "424-DO";
                        segments.push(
                            fieldId +
                                (needsFormatting
                                    ? formatField(
                                          value,
                                          ks?.length_code,
                                          ks?.length
                                      )
                                    : value)
                        );
                    });
                } else {
                    // Regular non-array field
                    const ks = this.get_field_schema(fieldId);
                    const needsFormatting =
                        ks?.length_code != null && fieldId !== "424-DO";
                    segments.push(
                        fieldId +
                            (needsFormatting
                                ? formatField(
                                      this.data[fieldId],
                                      ks?.length_code,
                                      ks?.length
                                  )
                                : this.data[fieldId])
                    );
                }
            }
        });

        // Now handle subform fields if this segment has any
        const subformFields = schema?.filter((f) => f.subform_field);

        if (subformFields?.length > 0) {
            // Group subform fields by their subform_field value
            const subformGroups = {};
            subformFields.forEach((field) => {
                const subformName = field.subform_field;
                if (!subformGroups[subformName]) {
                    subformGroups[subformName] = [];
                }
                subformGroups[subformName].push(field);
            });

            // Process each subform group
            Object.entries(subformGroups).forEach(([subformName, fields]) => {
                // First output any non-array fields in the subform
                const subformData = this.data[subformName];

                // Check if we have direct array data in the subform
                if (Array.isArray(subformData)) {
                    const itemCount = subformData.length;

                    // First add any count fields
                    const countFields = fields.filter(
                        (f) => f.type === "total_count"
                    );
                    countFields.forEach((fieldSchema) => {
                        const fieldId = fieldSchema.field_code.split("-")[1];
                        if (this.data[fieldId] !== undefined) {
                            const needsFormatting =
                                fieldSchema.length_code != null;
                            const formattedValue = needsFormatting
                                ? formatField(
                                      this.data[fieldId],
                                      fieldSchema.length_code,
                                      fieldSchema.length
                                  )
                                : this.data[fieldId];

                            segments.push(fieldId + formattedValue);
                        }
                    });

                    // Find fields that should appear before repeating items
                    const nonRepeatFields = fields.filter((f) => {
                        const fieldId = f.field_code.split("-")[1];
                        return (
                            !f.counter_field &&
                            !Array.isArray(this.data[fieldId])
                        );
                    });

                    // Output non-repeating fields first
                    nonRepeatFields.forEach((fieldSchema) => {
                        const fieldId = fieldSchema.field_code.split("-")[1];
                        if (this.data[fieldId] !== undefined) {
                            const needsFormatting =
                                fieldSchema.length_code != null;
                            const formattedValue = needsFormatting
                                ? formatField(
                                      this.data[fieldId],
                                      fieldSchema.length_code,
                                      fieldSchema.length
                                  )
                                : this.data[fieldId];

                            segments.push(fieldId + formattedValue);
                        }
                    });

                    // Now process each item in the subform array
                    for (let i = 0; i < itemCount; i++) {
                        const item = subformData[i];

                        // Process each field in this item
                        Object.entries(item).forEach(([fieldName, value]) => {
                            // Find the field schema for this field name
                            const fieldSchema = fields.find(
                                (f) => f.field === fieldName
                            );
                            if (!fieldSchema) return;

                            const fieldId =
                                fieldSchema.field_code.split("-")[1];
                            const needsFormatting =
                                fieldSchema.length_code != null;
                            const formattedValue = needsFormatting
                                ? formatField(
                                      value,
                                      fieldSchema.length_code,
                                      fieldSchema.length
                                  )
                                : value;

                            segments.push(fieldId + formattedValue);
                        });
                    }
                } else {
                    // Count field based approach (for arrays stored at the top level)
                    // Find the count field
                    const countField = fields.find(
                        (f) => f.type === "total_count"
                    );
                    if (!countField) return;

                    const countFieldId = countField.field_code.split("-")[1];
                    const itemCount = parseInt(
                        this.data[countFieldId] || "0",
                        10
                    );

                    // First output the count field
                    if (this.data[countFieldId]) {
                        const needsFormatting = countField.length_code != null;
                        segments.push(
                            countFieldId +
                                (needsFormatting
                                    ? formatField(
                                          this.data[countFieldId],
                                          countField.length_code,
                                          countField.length
                                      )
                                    : this.data[countFieldId])
                        );
                    }

                    // Find non-array fields that should be output first
                    fields
                        .filter((f) => {
                            const fieldId = f.field_code.split("-")[1];
                            return (
                                f !== countField &&
                                !Array.isArray(this.data[fieldId])
                            );
                        })
                        .forEach((fieldSchema) => {
                            const fieldId =
                                fieldSchema.field_code.split("-")[1];
                            if (this.data[fieldId] !== undefined) {
                                const needsFormatting =
                                    fieldSchema.length_code != null;
                                const formattedValue = needsFormatting
                                    ? formatField(
                                          this.data[fieldId],
                                          fieldSchema.length_code,
                                          fieldSchema.length
                                      )
                                    : this.data[fieldId];

                                segments.push(fieldId + formattedValue);
                            }
                        });

                    // Find the array fields
                    const arrayFields = fields.filter((f) => {
                        const fieldId = f.field_code.split("-")[1];
                        return Array.isArray(this.data[fieldId]);
                    });

                    // For each item in the count
                    for (let i = 0; i < itemCount; i++) {
                        // Process each array field for this item
                        arrayFields.forEach((fieldSchema) => {
                            const fieldId =
                                fieldSchema.field_code.split("-")[1];
                            const value = this.data[fieldId][i];

                            if (value !== undefined) {
                                const needsFormatting =
                                    fieldSchema.length_code != null;
                                const formattedValue = needsFormatting
                                    ? formatField(
                                          value,
                                          fieldSchema.length_code,
                                          fieldSchema.length
                                      )
                                    : value;

                                segments.push(fieldId + formattedValue);
                            }
                        });
                    }
                }
            });
        }

        // Add final segments log here
        this.log.debug({
            msg: "Final segments",
            segments,
        });

        return (
            FIELD_SEPARATOR + segments.join(FIELD_SEPARATOR) + SEGMENT_SEPARATOR
        );
    }

    formatFieldValue(value, schema) {
        if (!value) return "";

        if (schema?.type === "date" && value) {
            return moment(value).format("YYYYMMDD");
        }

        if (schema?.field?.includes("phone")) {
            return value.replace(/\D/g, "");
        }

        return formatField(value, schema?.length_code, schema?.length);
    }

    toJSON(options = {}) {
        const result = {};
        const subformData = {};
        let subfvalue = null;

        // First pass - identify subforms and their count fields
        for (const [key, value] of Object.entries(this.data)) {
            const ks = this.get_field_schema(key);
            if (ks && ks.subform_field) {
                const subformName = ks.subform_field;
                if (!subformData[subformName]) {
                    subformData[subformName] = {
                        fields: {},
                        countField: null,
                        count: 0,
                    };
                }

                if (ks.type === "total_count") {
                    subformData[subformName].countField = key;
                    subformData[subformName].count = parseInt(value, 10);
                }

                const mappedKey = options.readablekeys
                    ? this.constructor.get_symbol_by_field(key)
                    : key;
                const lcodes = ks?.length_code;
                let processedValue = value; // Start with original value

                // Helper function to check if reversal is needed
                const shouldReverse = (val, lc) =>
                    typeof val !== "object" && // Ensure it's not already an object/array element
                    lc &&
                    typeof lc === "string" &&
                    (lc.includes("v") ||
                        lc.startsWith("s9") ||
                        lc.startsWith("9") ||
                        (typeof val === "string" && // Fix: Use double quotes and correct parentheses
                            (val.includes("{") || val.includes("}")))); // Check for string format indicators

                if (Array.isArray(value)) {
                    // If the value is an array, process each item
                    processedValue = value.map((item) => { // Fix: Added parentheses
                        if (shouldReverse(item, lcodes)) {
                            console.debug({
                                msg: "Reversing subform array item format during first pass",
                                key: key,
                                itemValue: item,
                                lcode: lcodes,
                                mappedKey,
                            });
                            return reverseFormatField(
                                item.toString(),
                                lcodes,
                                mappedKey
                            );
                        }
                        return item; // Return original item if no reversal needed
                    });
                } else if (shouldReverse(value, lcodes)) {
                    // If it's not an array, process the single value
                    console.debug({
                        msg: "Reversing subform field format during first pass",
                        key: key,
                        value: value,
                        lcode: lcodes,
                        mappedKey,
                    });
                    processedValue = reverseFormatField(
                        value.toString(),
                        lcodes,
                        mappedKey
                    );
                }
                // Store the processed value (original or reversed)
                subformData[subformName].fields[mappedKey] = processedValue;
            }
        }

        // Process regular fields (non-subform)
        for (const [key, value] of Object.entries(this.data)) {
            const ks = this.get_field_schema(key);

            // Skip subform fields - we'll handle them separately
            if (ks && ks.subform_field) continue;

            const mappedKey = options.readablekeys
                ? this.constructor.get_symbol_by_field(key)
                : key;

            let processedValue = value;

            // Process code maps if needed
            if (options.mapkeys && ks && ks.code_map) {
                if (Array.isArray(value)) {
                    processedValue = value.map((v) =>
                        parseCodeMap(v, ks.code_map)
                    );
                } else {
                    processedValue = parseCodeMap(value, ks.code_map);
                }
            }

            // Process special numeric formats
            const lcode = ks?.length_code;
            if (
                typeof processedValue !== "object" &&
                lcode &&
                typeof lcode === "string" &&
                (lcode.includes("v") ||
                    lcode.startsWith("s9") ||
                    lcode.startsWith("9") ||
                    processedValue.includes("{") ||
                    processedValue.includes("}"))
            ) {
                console.debug({
                    msg: "Reversing format",
                    value: processedValue,
                    lcode,
                    mappedKey,
                });
                processedValue = reverseFormatField(
                    processedValue.toString(),
                    lcode,
                    mappedKey
                );
            }

            result[mappedKey] = processedValue;
        }

        // Specific post-processing for fields counted by a separate field
        // Example: approved_msg (6F) counted by approved_message_code_count (5F)
        const countKey_5F = "5F";
        const dataKey_6F = "6F";
        const mappedCountKey_5F = options.readablekeys
            ? this.constructor.get_symbol_by_field(countKey_5F) || countKey_5F // Fallback if symbol not found
            : countKey_5F;
        const mappedDataKey_6F = options.readablekeys
            ? this.constructor.get_symbol_by_field(dataKey_6F) || dataKey_6F // Fallback if symbol not found
            : dataKey_6F;

        // Ensure the count field is read as integer
        const approvedMsgCount = result[mappedCountKey_5F]
            ? parseInt(result[mappedCountKey_5F], 10)
            : 0;

        if (
            approvedMsgCount >= 1 &&
            result[mappedDataKey_6F] !== undefined && // Check for existence
            !Array.isArray(result[mappedDataKey_6F])
        ) {
            console.debug({
                msg: "Wrapping counted field into array",
                field: mappedDataKey_6F,
                count: approvedMsgCount,
                value: result[mappedDataKey_6F],
            });
            // Wrap the existing value in an array
            result[mappedDataKey_6F] = [result[mappedDataKey_6F]];
        }

        // Process subforms
        for (const [subformName, data] of Object.entries(subformData)) {
            const { fields, count, countField } = data;
            console.debug({
                msg: "Processing subform",
                subformName,
                fields,
                count,
                countField,
            });

            if (count > 0) {
                // Create an array of objects for the subform
                result[subformName] = [];

                // Find which fields are arrays that need distribution
                const arrayFields = {};
                const nonArrayFields = {};

                // Add the count field to the parent level
                if (countField) {
                    // Get the mapped key for the count field
                    const countFieldMapped = options.readablekeys
                        ? this.constructor.get_symbol_by_field(countField)
                        : countField;

                    // Add count to the parent object, not in each subform object
                    result[countFieldMapped] = fields[countFieldMapped];
                }

                for (const [fieldKey, fieldValue] of Object.entries(fields)) {
                    // Skip the count field - we already added it to the parent
                    if (
                        fieldKey ===
                        (options.readablekeys
                            ? this.constructor.get_symbol_by_field(countField)
                            : countField)
                    ) {
                        continue;
                    }

                    if (Array.isArray(fieldValue)) {
                        arrayFields[fieldKey] = fieldValue;
                    } else {
                        nonArrayFields[fieldKey] = fieldValue;
                    }
                }

                // Create an object for each count
                for (let i = 0; i < count; i++) {
                    const subformObject = { ...nonArrayFields };

                    // Add array field values for this position
                    for (const [fieldKey, fieldValues] of Object.entries(
                        arrayFields
                    )) {
                        if (i < fieldValues.length) {
                            subformObject[fieldKey] = fieldValues[i];
                        }
                    }

                    result[subformName].push(subformObject);
                }
            } else {
                // No count, just use the fields as a single object
                result[subformName] = { ...fields };

                // Remove the count field from the result if present
                if (countField) {
                    const countFieldMapped = options.readablekeys
                        ? this.constructor.get_symbol_by_field(countField)
                        : countField;

                    if (result[subformName][countFieldMapped]) {
                        delete result[subformName][countFieldMapped];
                    }
                }
            }
        }

        return result;
    }
}

class Patient extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "01";
    static symbol = "patient";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            CX: "patient_id_qualifier",
            CY: "patient_id",
            C4: "patient_date_of_birth",
            C5: "patient_gender_code",
            CA: "patient_first_name",
            CB: "patient_last_name",
            CM: "patient_street_address",
            CN: "patient_city_address",
            CO: "patient_state",
            CP: "patient_zip",
            CQ: "patient_phone",
            C7: "place_of_service",
            CZ: "employer_id",
            "1C": "smoker_code",
            "2C": "pregnancy_indicator",
            HN: "patient_email_address",
            "4X": "patient_residence",
        };
    }
}

class Pharmacy extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "02";
    static symbol = "pharmacy";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            EY: "provider_id_qualifier",
            E9: "provider_id",
        };
    }
}

class Prescriber extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "03";
    static symbol = "prescriber";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            EZ: "dr_id_qualifier",
            DB: "dr_id",
            DR: "dr_last_name",
            PM: "dr_phone",
            "2E": "pri_dr_id_qualifier",
            DL: "pri_dr_id",
            "4E": "pri_dr_last_name",
            "2J": "dr_first_name",
            "2K": "dr_street_address",
            "2M": "dr_city",
            "2N": "dr_state",
            "2P": "dr_zip",
        };
    }
}

class Insurance extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "04";
    static symbol = "insurance";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            C2: "card_holder_id",
            CC: "card_holder_first_name",
            CD: "card_holder_last_name",
            CE: "home_plan",
            FO: "plan_id",
            C9: "elig_clar_code",
            C1: "group_id",
            C3: "person_code",
            C6: "pt_rel_code",
            "2A": "medigap_id",
            "2B": "mcd_indicator",
            "2D": "dr_accept_indicator",
            G2: "partd_facility",
            N5: "mcd_id_no",
        };
    }
}

class CoordinationOfBenefitsOtherPayments extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "05";
    static symbol = "cob";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            "4C": "coordination_of_benefits_other_payments_count",
            "5C": "other_coverage_type",
            "6C": "other_id_qualifier",
            "7C": "other_id",
            E8: "other_date",
            A7: "internal_control_number",
            HB: "paid_amount_count",
            HC: "paid_qualifier",
            DV: "paid_amount",
            "5E": "other_payer_reject_count",
            "6E": "other_reject_code",
            NR: "pt_resp_amt_count",
            NP: "pt_resp_amt_qualifier",
            NQ: "pt_resp_amt",
            MU: "benefit_stage_count",
            MV: "benefit_stage_qualifier",
            MW: "benefit_stage_amount",
        };
    }
}

class WorkersCompensation extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "06";
    static symbol = "workers_comp";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            "4C": "cob_count",
            DY: "date_of_injury",
            CF: "employer_name",
            CG: "employer_address",
            CH: "employer_city",
            CI: "employer_state",
            CJ: "employer_zip",
            CK: "employer_phone",
            CL: "employer_contact_name",
            CR: "carrier_id",
            DZ: "claim_reference_id",
            TR: "bill_type_indicator",
            TS: "pay_to_qualifier",
            TT: "pay_to_id",
            TU: "pay_to_name",
            TV: "pay_to_address",
            TW: "pay_to_city",
            TX: "pay_to_state",
            TY: "pay_to_zip",
            TZ: "gen_prod_id_qualifier",
            UA: "gen_prod_id",
        };
    }
}

class Claim extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "07";
    static symbol = "claim";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            EM: "rx_svc_no_ref_qualifier",
            D2: "rx_svc_no",
            E1: "prod_svc_id_qualifier",
            D7: "prod_svc_id",
            EN: "associated_rx_svc_no",
            EP: "associated_rx_service_date",
            SE: "proc_mod_code_count",
            ER: "proc_mod_code",
            E7: "quantity_dispensed",
            D3: "fill_number",
            D5: "day_supply",
            D6: "compound_code",
            D8: "daw_code",
            DE: "date_rx_written",
            DF: "number_of_refills_authorized",
            DJ: "rx_origin_code",
            NX: "sub_clar_code_count",
            DK: "sub_clar_code",
            ET: "quantity_prescribed",
            C8: "other_coverage_code",
            DT: "sp_pk_indicator",
            EJ: "og_rx_id_qualifier",
            EA: "og_rx_id",
            EB: "og_rx_quantity",
            EK: "sched_rx_no",
            28: "unit_of_measure",
            DI: "level_of_service",
            EU: "pa_type_code",
            EV: "pa_no_submitted",
            EW: "inter_auth_type_id",
            EX: "inter_auth_id",
            HD: "dispensing_status",
            HF: "qty_to_disp",
            HG: "ds_to_disp",
            NV: "delay_reason_code",
            MT: "pt_assign_indicator",
            E2: "admin_route",
            G1: "compound_type",
            U7: "pharmacy_service_type",
        };
    }
}

class DurPps extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "08";
    static symbol = "dur";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            "7E": "dur_pps_code_counter",
            E4: "rsn_for_svc_code",
            E5: "prf_svc_code",
            E6: "rst_svc_code",
            "8E": "dur_pps_loe",
            J9: "co_agt_id_qualifier",
            H6: "co_agt_id",
        };
    }
}

class Coupon extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "09";
    static symbol = "coupon";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            KE: "coupon_type",
            ME: "coupon_number",
            NE: "coupon_value_amount",
        };
    }
}

class Compound extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "10";
    static symbol = "compound";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            EF: "comp_dsg_fm_code",
            EG: "comp_disp_unit",
            EC: "compound_ingredient_component_count",
            RE: "cmp_id_qualifier",
            TE: "cmp_id",
            ED: "cmp_ing_qty",
            EE: "cmp_ing_cost",
            UE: "cmp_ing_cost_basis",
            "2G": "cmp_ing_md_code_count",
            "2H": "cmp_ing_md_code",
        };
    }
}

class Pricing extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "11";
    static symbol = "pricing";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            D9: "ing_cst_sub",
            DC: "disp_fee_sub",
            BE: "pro_svc_fee_sub",
            DX: "pt_pd_amt_sub",
            E3: "incv_amt_sub",
            H7: "o_amt_sub_count",
            H8: "o_amt_sub_qualifier",
            H9: "o_amt_sub",
            HA: "flat_tax_amt",
            GE: "sales_tax",
            HE: "per_sales_tax_rate_used",
            JE: "sales_tax_basis",
            DQ: "u_and_c_charge",
            DU: "gross_amount_due",
            DN: "cost_basis",
            N3: "medicaid_paid_amount",
        };
    }
}

class PriorAuth extends Segments {
    constructor(data = {}) {
        super(data);
    }
    static segment_identifier = "12";
    static symbol = "prior_auth";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            PA: "request_type",
            PB: "request_period_date_begin",
            PC: "request_period_date_end",
            PD: "basis_of_request",
            PE: "authorized_representative_first_name",
            PF: "authorized_rep_last_name",
            PG: "authorized_rep_street_address",
            PH: "authorized_rep_city",
            PJ: "authorized_rep_state_province",
            PK: "authorized_rep_zip_postal_code",
            PY: "prior_authorization_number_assigned",
            F3: "authorization_number",
            PP: "prior_authorization_supporting_documentation",
        };
    }
}

class Clinical extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "13";
    static symbol = "clinical";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            VE: "dx_code_count",
            WE: "dx_code_qualifier",
            DO: "dx_code",
            XE: "clinical_information_counter",
            ZE: "measurement_date",
            H1: "measurement_time",
            H2: "measurement_dimension",
            H3: "measurement_unit",
            H4: "measurement_value",
        };
    }
}

class AdditionalDocumentationSegment extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "14";
    static symbol = "docs";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            "2Q": "doc_type_id",
            "2V": "request_period_begin_date",
            "2W": "request_period_recert_revised_date",
            "2U": "request_status",
            "2S": "length_of_need_qualifier",
            "2R": "length_of_need",
            "2T": "prescriber_supplier_date_signed",
            "2X": "supporting_documentation",
            "2Z": "question_number_letter_count",
            "4B": "question_number_letter",
            "4D": "question_percent_response",
            "4G": "question_date_response",
            "4H": "question_dollar_amount_response",
            "4J": "question_numeric_response",
            "4K": "question_alphanumeric_response",
        };
    }
}

class Facility extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "15";
    static symbol = "facility";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            "8C": "facility_id",
            "3Q": "facility_name",
            "3U": "facility_street_address",
            "5J": "facility_city_address",
            "3V": "facility_state_province_address",
            "6D": "facility_zip_postal_zone",
        };
    }
}

class Narrative extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "16";
    static symbol = "narrative";
    static segment_length = "9(2)";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            BM: "narrative_message",
        };
    }
}

// Response Segments

class ResponseMessage extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "20";
    static symbol = "response_msg";
    static segment_length = "x(2)";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            F4: "message",
        };
    }
}

class ResponseStatus extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "21";
    static symbol = "response_stat";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            AN: "transaction_response_status",
            F3: "authorization_number",
            FA: "reject_count",
            FB: "reject_code",
            "4F": "reject_field_occ_ind",
            "5F": "approved_message_code_count",
            "6F": "approved_msg",
            UF: "add_msg_count",
            UH: "add_msg_qualifier",
            FQ: "add_msg",
            UG: "add_msg_cont",
            "7F": "help_qual",
            "8F": "help_phone",
            K5: "transaction_ref_no",
            A7: "internal_control_no",
            MA: "url",
        };
    }
}

class ResponseClaim extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "22";
    static symbol = "response_claim";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            EM: "rx_svc_no_ref_qualifier",
            D2: "rx_svc_no",
            "9F": "preferred_product_count",
            AP: "preferred_product_id_qualifier",
            AR: "preferred_product_id",
            AS: "preferred_product_incentive",
            AT: "preferred_product_cost_share_incentive",
            AU: "preferred_product_description",
        };
    }
}

class ResponsePricing extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "23";
    static symbol = "response_pricing";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            F5: "pt_pay_amt",
            F6: "ing_cst_paid",
            F7: "disp_fee_paid",
            AV: "tax_exp_ind",
            AW: "flat_tax_paid",
            AX: "sales_tax_paid",
            AY: "per_sales_tax_paid",
            AZ: "sales_tax_basis_paid",
            FL: "incv_amt_paid",
            J1: "prof_svc_paid",
            J2: "other_amount_paid_count",
            J3: "oth_amt_qualifier",
            J4: "oth_amt_paid",
            J5: "opayer_amt_rec",
            F9: "total_paid",
            FM: "rem_basis",
            FN: "sales_tax_attr",
            FC: "acc_ded_amt",
            FD: "rem_ded_amt",
            FE: "rem_ben_amt",
            FH: "amt_apld_ded",
            FI: "amt_copay",
            FJ: "amt_attr_prod_sel",
            FK: "amt_exd_ben_max",
            HH: "disp_fee_basis",
            HJ: "copay_basis",
            HK: "flat_tax_basis",
            HM: "sales_tax_basis",
            NZ: "pro_fee_amt",
            EQ: "sales_tax_pt",
            "2Y": "sales_tax_plan",
            "4U": "coinsur_amt",
            "4V": "coinsur_basis",
            MU: "benefit_stage_count",
            MV: "benefit_stage_qualifier",
            MW: "benefit_stage_amount",
            G3: "est_gen_savings",
            UC: "spd_acct_remaining",
            UD: "hlth_pln_asst",
            UJ: "amt_attr_prov_sel",
            UK: "amt_attr_prod_sel",
            UM: "amt_attr_nonformulary",
            UN: "amt_attr_brd_nonformulary",
            UP: "amt_coverage_gap",
            U8: "amt_ing_cost_contracted",
            U9: "amt_disp_fee_contracted",
        };
    }
}

class ResponseDurPps extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "24";
    static symbol = "response_dur";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            J6: "dur_pps_response_code_counter",
            E4: "rsn_for_svc",
            FS: "clin_sig_code",
            FT: "opharmacy_indicator",
            FU: "ofill_date",
            FV: "prev_fill_qty",
            FW: "db_indicator",
            FX: "opresc_indicator",
            FY: "dur_free_text",
            NS: "dur_add_text",
        };
    }
}

class ResponseInsurance extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "25";
    static symbol = "response_insur";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            C1: "group_id",
            FO: "plan_id",
            "2F": "network_reimbursement_id",
            J7: "payer_id_qualifier",
            J8: "payer_id",
            N5: "mcd_id_no",
            N6: "mcd_agcy_no",
            C2: "cardholder_id",
        };
    }
}

class ResponsePriorAuthorization extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "26";
    static symbol = "response_prior_authorization";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            PR: "prior_authorization_processed_date",
            PS: "prior_authorization_effective_date",
            PT: "prior_authorization_expiration_date",
            RA: "prior_authorization_quantity",
            RB: "prior_authorization_dollars_authorized",
            PW: "prior_authorization_number_of_refills_authorized",
            PX: "prior_authorization_quantity_accumulated",
            PY: "prior_authorization_number_assigned",
        };
    }
}

class ResponseAdditionalDocumentation extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "27";
    static symbol = "response_docs";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            UR: "medi_part_d_cov_code",
            UQ: "cms_low_income_sharing",
            U1: "contract_number",
            FF: "formulary_id",
            U6: "benefit_id",
            US: "medi_part_d_eff_date",
            UT: "medi_part_d_trm_date",
        };
    }
}

class ResponseCoordOfBenefits extends Segments {
    constructor(data = {}) {
        super(data);
    }

    static segment_identifier = "28";
    static symbol = "response_cob";
    static segment_length = "undefined";
    static segment_required = "M";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            NT: "other_id_count",
            "5C": "other_coverage_type",
            "6C": "other_id_qualifier",
            "7C": "other_id",
            MH: "other_pcn",
            NU: "other_cardholder_id",
            MJ: "other_group_no",
            UV: "other_person_code",
            UB: "other_help_phone",
            UW: "other_rel_code",
            UX: "ben_eff_date",
            UY: "ben_trm_date",
        };
    }
}

class ResponsePatient extends Segments {
    constructor(data = {}) {
        super(data);
    }
    static segment_identifier = "29";
    static symbol = "response_pat";
    static segment_length = "undefined";
    static segment_required = "undefined";

    static get field_id_to_symbol() {
        return {
            ...super.field_id_to_symbol,
            CA: "patient_first_name",
            CB: "patient_last_name",
            C4: "patient_date_of_birth",
        };
    }
}

// register Main segments 01 - 16
Segments.register_segment(Patient); // 01
Segments.register_segment(Pharmacy); // 02
Segments.register_segment(Prescriber); // 03
Segments.register_segment(Insurance); // 04
Segments.register_segment(CoordinationOfBenefitsOtherPayments); // 05
Segments.register_segment(WorkersCompensation);
Segments.register_segment(Claim); // 06
Segments.register_segment(DurPps); // 07
Segments.register_segment(Coupon); // 08
Segments.register_segment(Compound); // 09
Segments.register_segment(Pricing); // 10
Segments.register_segment(PriorAuth); // 11
Segments.register_segment(Clinical); // 12
Segments.register_segment(AdditionalDocumentationSegment); // 13
Segments.register_segment(Facility); // 14
Segments.register_segment(Narrative); // 15

// register response segments 20 - 29
Segments.register_segment(ResponseMessage); // 20
Segments.register_segment(ResponseStatus); // 21
Segments.register_segment(ResponseClaim); // 22
Segments.register_segment(ResponsePricing); // 23
Segments.register_segment(ResponseDurPps); // 24
Segments.register_segment(ResponseInsurance); // 25
Segments.register_segment(ResponsePriorAuthorization); // 26
Segments.register_segment(ResponseAdditionalDocumentation); // 27
Segments.register_segment(ResponseCoordOfBenefits); // 28
Segments.register_segment(ResponsePatient); // 29
