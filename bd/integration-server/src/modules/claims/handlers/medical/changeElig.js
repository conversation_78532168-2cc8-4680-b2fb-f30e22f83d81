import Ajv from "ajv";
import addFormats from "ajv-formats";
import { readFileSync } from "fs";
const ajv = new Ajv.default({
    removeAdditional: true,
    useDefaults: true,
    allErrors: true,
    removeUndefined: true,
});
ajv.addKeyword("example");
addFormats(ajv);
const schema_url = new URL(
    "../../fixtures/schema/medical/MedicalEligibilitySchema.json",
    import.meta.url
);
const medicalEligibilitySchema = readFileSync(schema_url, "utf8");
const validateMedicalEligibility = ajv.compile(
    JSON.parse(medicalEligibilitySchema)
);

class BaseModel {
    constructor(requiredFields, data) {
        Object.defineProperty(this, "_requiredFields", {
            value: requiredFields,
            enumerable: false,
            writable: false,
            configurable: false,
        });
        this.validateRequiredFields(data);
        this.removeUndefinedProperties(this);
    }

    validateRequiredFields(data) {
        for (const field of this._requiredFields) {
            if (data[field] === undefined || data[field] === null) {
                throw new Error(`Required field ${field} is missing`);
            }
        }
    }

    removeUndefinedProperties(obj) {
        Object.keys(obj).forEach((key) => {
            if (obj[key] === undefined || obj[key] === null) {
                delete obj[key];
            } else if (typeof obj[key] === "object" && obj[key] !== null) {
                if (obj[key] instanceof BaseModel) {
                    obj[key] = obj[key].toJSON();
                } else {
                    this.removeUndefinedProperties(obj[key]);
                    if (Object.keys(obj[key]).length === 0) {
                        delete obj[key];
                    }
                }
            }
        });
    }

    toJSON() {
        const json = {};
        for (const [key, value] of Object.entries(this)) {
            if (value instanceof BaseModel || Array.isArray(value)) {
                json[key] = this.convertToJSON(value);
            } else if (value !== undefined) {
                json[key] = value;
            }
        }
        return json;
    }

    convertToJSON(value) {
        if (value instanceof BaseModel) {
            return value.toJSON();
        } else if (Array.isArray(value)) {
            return value.map((item) => this.convertToJSON(item));
        } else if (value && typeof value === "object") {
            return Object.fromEntries(
                Object.entries(value)
                    .filter(([_, v]) => v !== undefined)
                    .map(([k, v]) => [k, this.convertToJSON(v)])
            );
        }
        return value;
    }
}

export class MedicalEligibility extends BaseModel {
    constructor(data) {
        if (typeof data !== "object" || data === null) {
            throw new Error("MedicalEligibility data must be an object");
        }
        super(["controlnumber", "subscriber"], data);

        this.controlNumber = data.controlnumber;
        this.dependents = data.dependents?.map((dep) => new Dependent(dep));
        this.encounters = data.encounters?.map((enc) => new Encounter(enc));
        this.informationReceiverName = data.informationreceivername
            ? new InformationReceiverName(data.informationreceivername)
            : null;
        this.provider = new Provider(data.provider);
        this.submitterTransactionIdentifier =
            data.submittertransactionidentifier;
        this.subscriber = new Subscriber(data.subscriber);
        this.tradingPartnerServiceId = data.tradingpartnerserviceid;
        this.tradingPartnerName = data.tradingpartnername;
        this.portalUsername = data.portalusername;
        this.portalPassword = data.portalpassword;

        this.removeUndefinedProperties(this);
        this.validate(this);
    }

    validate(instance) {
        console.log("Validating instance:", JSON.stringify(instance, null, 2));
        const valid = validateMedicalEligibility(instance);
        if (!valid) {
            console.error(
                "Validation errors:",
                validateMedicalEligibility.errors
            );
            throw new Error(
                "Validation failed for MedicalEligibility data. " +
                    JSON.stringify(validateMedicalEligibility.errors)
            );
        }
        return validateMedicalEligibility.errors;
    }

    isValid() {
        try {
            this.validate(this);
            return true;
        } catch (error) {
            console.error("Validation errors:", error.message);
            return false;
        }
    }
}

class Provider extends BaseModel {
    constructor(data) {
        super([], data); // No required fields in the constructor, we'll validate against the schema

        this.organizationName = data.organizationname;
        this.firstName = data.firstname;
        this.lastName = data.lastname;
        this.npi = data.npi;
        this.serviceProviderNumber = data.serviceprovidernumber;
        this.payorId = data.payorid;
        this.taxId = data.taxid;
        this.ssn = data.ssn;
        this.pharmacyProcessorNumber = data.pharmacyprocessorNumber;
        this.servicesPlanID = data.servicesplanid;
        this.employersId = data.employersid;
        this.providerCode = data.providercode;
        this.referenceIdentification = data.referenceidentification;
        this.providerType = data.providertype;

        // Optional fields
        this.middleName = data.middleName;
        this.suffix = data.suffix;

        this.removeUndefinedProperties(this);
        this.validate(this);
    }

    validate(instance) {
        const providerSchema =
            validateMedicalEligibility.schema.properties.provider;
        const valid = ajv.validate(providerSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for Provider data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }

    static schemaPath = "schema.properties.provider";
}

class Subscriber extends BaseModel {
    constructor(data) {
        super(
            [
                "address",
                "memberid",
                "firstname",
                "lastname",
                "gender",
                "dateofbirth",
            ],
            data
        );
        this.memberId = data.memberid;
        this.firstName = data.firstname;
        this.middleName = data.middlename;
        this.lastName = data.lastname;
        this.suffix = data.suffix;
        this.gender = data.gender;
        this.dateOfBirth = data.dateofbirth;
        this.ssn = data.ssn;
        this.groupNumber = data.groupnumber;
        this.birthSequenceNumber = data.birthSequenceNumber;
        this.caseNumber = data.caseNumber;
        this.address = data.address ? new Address(data.address) : null;
        this.removeUndefinedProperties(this);
        this.validate(this);
    }
    validate(instance) {
        const subscriberSchema =
            validateMedicalEligibility.schema.properties.subscriber;
        const valid = ajv.validate(subscriberSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for Subscriber data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
    static schemaPath = "schema.properties.subscriber";
}

class Address extends BaseModel {
    constructor(data) {
        super(["address1", "city", "state", "postalcode"], data);
        this.address1 = data.address1;
        this.address2 = data.address2;
        this.city = data.city;
        this.state = data.state;
        this.postalCode = data.postalcode;
        this.countryCode = data.countrycode;
        this.locationIdentifier = data.locationidentifier;
        this.countrySubDivisionCode = data.countrysubdivisioncode;
        this.removeUndefinedProperties(this);
        this.validate(data);
    }
    static schemaPath = "schema.properties.subscriber.properties.address";
    static optionalFields = [
        "address2",
        "countryCode",
        "locationIdentifier",
        "countrySubDivisionCode",
    ];
    validate(instance) {
        const addressSchema =
            validateMedicalEligibility.schema.properties.subscriber.properties
                .address;
        const valid = ajv.validate(addressSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for Address data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
}

class Dependent extends BaseModel {
    constructor(data) {
        super(
            [
                "birthsequencenumber",
                "individualrelationshipcode",
                "memberid",
                "firstname",
                "lastname",
                "gender",
                "dateofbirth",
            ],
            data
        );
        this.birthSequenceNumber = data.birthsequencenumber;
        this.individualRelationshipCode = data.individualrelationshipcode;
        this.issueNumber = data.issuenumber;
        this.eligibilityCategory = data.eligibilitycategory;
        this.memberId = data.memberid;
        this.firstName = data.firstname;
        this.middleName = data.middlename;
        this.lastName = data.lastname;
        this.suffix = data.suffix;
        this.gender = data.gender;
        this.dateOfBirth = data.dateofbirth;
        this.ssn = data.ssn;
        this.groupNumber = data.groupnumber;
        this.address = data.address ? new Address(data.address) : null;
        this.additionalIdentification = data.additionalidentification
            ? new AdditionalIdentification(data.additionalidentification)
            : null;
        this.healthCareCodeInformation = data.healthcarecodeinformation?.map(
            (info) => new HealthCareCodeInformation(info)
        );
        this.removeUndefinedProperties(this);
        this.validate(data);
    }
    static schemaPath = "schema.properties.dependents";
    static optionalFields = [
        "address",
        "additionalidentification",
        "healthcarecodeinformation",
    ];
}

class HealthCareCodeInformation extends BaseModel {
    constructor(data) {
        super(["diagnosistypecode", "diagnosiscode"], data);
        this.diagnosisTypeCode = data.diagnosistypecode;
        this.diagnosisCode = data.diagnosiscode;
        this.removeUndefinedProperties(this);
        this.validate(data);
    }
    static schemaPath =
        "schema.properties.dependents.properties.healthCareCodeInformation";
    static optionalFields = [];
    validate(instance) {
        const healthCareCodeInformationSchema =
            validateMedicalEligibility.schema.properties.dependents.properties
                .healthCareCodeInformation;
        const valid = ajv.validate(healthCareCodeInformationSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for HealthCareCodeInformation data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
}

class Encounter extends BaseModel {
    constructor(data) {
        super(
            [
                "servicetypecodes",
                "beginningdateofservice",
                "enddateofservice",
                "dateofservice",
            ],
            data
        );
        this.beginningDateOfService = data.beginningdateofservice;
        this.endDateOfService = data.enddateofservice;
        this.dateOfService = data.dateofservice;
        this.serviceTypeCodes = data.servicetypecodes;
        this.priorAuthorizationOrReferralNumber =
            data.priorauthorizationorreferralnumber;
        this.referenceIdentificationQualifier =
            data.referenceidentificationqualifier;
        this.industryCode = data.industryCode;
        this.productOrServiceIDQualifier = data.productorserviceidqualifier;
        this.procedureCode = data.procedurecode;
        this.procedureModifiers = data.proceduremodifiers;
        this.diagnosisCodePointer = data.diagnosiscodepointer;
        this.medicalProcedures = data.medicalprocedures?.map(
            (proc) => new MedicalProcedure(proc)
        );
        this.removeUndefinedProperties(this);
        this.validate(data);
    }
    static schemaPath = "schema.properties.encounters";
    static optionalFields = ["medicalProcedures"];
    validate(instance) {
        const encountersSchema =
            validateMedicalEligibility.schema.properties.encounters;
        const valid = ajv.validate(encountersSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for Encounters data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
}

class MedicalProcedure extends BaseModel {
    constructor(data) {
        super(["productorserviceidqualifier", "procedurecode"], data);
        this.productOrServiceIDQualifier = data.productorserviceidqualifier;
        this.procedureCode = data.procedurecode;
        this.procedureModifiers = data.proceduremodifiers;
        this.diagnosisCodePointer = data.diagnosiscodepointer;
        this.removeUndefinedProperties(this);
        this.validate(data);
    }
    static schemaPath =
        "schema.properties.encounters.properties.medicalProcedures";
    static optionalFields = [];
    validate(instance) {
        const medicalProcedureSchema =
            validateMedicalEligibility.schema.properties.encounters.properties
                .medicalProcedures;
        const valid = ajv.validate(medicalProcedureSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for MedicalProcedure data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
}

class AdditionalIdentification extends BaseModel {
    constructor(data) {
        super([]);
        this.planNumber = data.plannumber;
        this.policyNumber = data.policynumber;
        this.memberIdentificationNumber = data.memberidentificationnumber;
        this.contractNumber = data.contractnumber;
        this.medicalRecordIdentificationNumber =
            data.medicalrecordidentificationnumber;
        this.patientAccountNumber = data.patientaccountnumber;
        this.healthInsuranceClaimNumber = data.healthinsuranceclaimnumber;
        this.identificationCardSerialNumber =
            data.identificationcardserialnumber;
        this.insurancePolicyNumber = data.insurancepolicynumber;
        this.planNetworkIdentificationNumber =
            data.plannetworkidentificationnumber;
        this.agencyClaimNumber = data.agencyclaimnumber;
        this.removeUndefinedProperties(this);
        this.validate(data);
    }
    static schemaPath =
        "schema.properties.dependents.properties.additionalIdentification";
    static optionalFields = [];
    validate(instance) {
        const additionalIdentificationSchema =
            validateMedicalEligibility.schema.properties.dependents.properties
                .additionalIdentification;
        const valid = ajv.validate(additionalIdentificationSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for AdditionalIdentification data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
}

class InformationReceiverName extends BaseModel {
    constructor(data) {
        super([], data);
        this.stateLicenceNumber = data.stateLicenceNumber;
        this.medicareProviderNumber = data.medicareprovidernumber;
        this.medicaidProviderNumber = data.medicaidprovidernumber;
        this.facilityIdNumber = data.facilityidnumber;
        this.contactNumber = data.contactnumber;
        this.devicePinNumber = data.devicepinnumber;
        this.submitterIdNumber = data.submitteridnumber;
        this.nationalProviderIdentifier = data.nationalprovideridentifier;
        this.providerPlanNetworkIdNumber = data.providerplannetworkidnumber;
        this.facilityNetworkIdNumber = data.facilitynetworkidnumber;
        this.priorIdentifierNumber = data.prioridentifiernumber;
        this.socialSecurityNumber = data.socialsecuritynumber;
        this.federalTaxpayerIdentificationNumber =
            data.federaltaxpayeridentificationnumber;
        this.informationReceiverAdditionalIdentifierState =
            data.informationreceiveradditionalidentifierstate;
        this.address = data.address ? new Address(data.address) : null;
        this.removeUndefinedProperties(this);
        this.validate(this);
    }
    static schemaPath = "schema.properties.informationReceiverName";
    static optionalFields = ["address"];
    validate(instance) {
        const informationReceiverNameSchema =
            validateMedicalEligibility.schema.properties
                .informationReceiverName;
        const valid = ajv.validate(informationReceiverNameSchema, instance);
        if (!valid) {
            console.error(ajv.errors);
            throw new Error(
                "Validation failed for InformationReceiverName data. " +
                    JSON.stringify(ajv.errors)
            );
        }
    }
}
