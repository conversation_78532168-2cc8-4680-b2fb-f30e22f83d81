import Ajv from "ajv";
import moment from "moment";
import addFormats from "ajv-formats";
import { readFileSync } from "fs";
const ajv = new Ajv.default({
    removeAdditional: true,
    useDefaults: true,
    //    coerceTypes: true,
    allErrors: true,
    removeUndefined: true, // This option will remove undefined properties
});
ajv.addKeyword("example");
addFormats(ajv);
const schema_url = new URL(
    "../../fixtures/schema/medical/MedicalClaimSchema.json",
    import.meta.url
);
const medicalClaimSchema = readFileSync(schema_url, "utf8");

const validateMedicalClaim = ajv.compile(JSON.parse(medicalClaimSchema));

class BaseModel {
    constructor(data) {
        this.formatDates(data);
        this.removeUndefinedProperties(data);
    }

    checkRequiredFields(requiredFields, data) {
        if (!data) {
            throw new Error(`No data provided for ${this.constructor.name}`);
        }
        for (const key of requiredFields) {
            if (!Object.prototype.hasOwnProperty.call(data, key)) {
                throw new Error(
                    `Required field ${key} is missing from the ${this.constructor.name}.`
                );
            }
        }
    }

    validate(schema, data) {
        const valid = ajv.validate(schema, data);
        if (!valid) {
            const errorMessages = ajv.errors.map((error) => {
                return {
                    type:
                        error.keyword === "required"
                            ? "required"
                            : "validation",
                    field:
                        error.keyword === "required" &&
                        error.params?.missingProperty
                            ? error.params?.missingProperty
                            : error.instancePath.replace(/^\//, ""), // Remove leading slash
                    message:
                        error.keyword == "enum" && error.params?.allowedValues
                            ? error.message +
                              ` [${error.params?.allowedValues}]`
                            : error.message,
                    parent: this.constructor.name,
                };
            });

            const eobj = {
                type: "validation",
                field: "root",
                description: errorMessages,
                message: "Claim validation failed",
                parent: this.constructor.name,
                name: "ChangeHealthError",
            };
            throw eobj;
        }
        //        console.log(`Validation passed for ${this.constructor.name} data`);
    }

    removeUndefinedProperties(obj) {
        Object.keys(obj).forEach((key) => {
            if (obj[key] === undefined || obj[key] === null) {
                delete obj[key];
            } else if (typeof obj[key] === "object" && obj[key] !== null) {
                this.removeUndefinedProperties(obj[key]);
            }
        });
    }
    formatDates(obj) {
        Object.keys(obj).forEach((key) => {
            if (
                obj[key] &&
                typeof obj[key] === "string" &&
                key.toLowerCase().includes("date")
            ) {
                console.log(
                    `Formatting date ${key}: ${obj[key]} to format yyyyMMdd`
                );
                const formattedDate = moment(obj[key]);
                if (!formattedDate.isValid()) {
                    throw new Error(
                        `Invalid date format for ${key}: ${obj[key]}`
                    );
                }
                obj[key] = formattedDate.format("yyyyMMDD");
                console.log(`Formatted date ${key} to ${obj[key]}`);
            } else if (obj[key] && typeof obj[key] === "object") {
                if (Array.isArray(obj[key])) {
                    obj[key].forEach((item, _index) => {
                        if (typeof item === "object") {
                            this.formatDates(item);
                        }
                    });
                } else {
                    this.formatDates(obj[key]);
                }
            }
        });
    }

    toJSON() {
        const json = {};
        for (const [key, value] of Object.entries(this)) {
            if (value instanceof BaseModel || Array.isArray(value)) {
                json[key] = this.convertToJSON(value);
            } else if (value !== undefined) {
                json[key] = value;
            }
        }
        return json;
    }

    convertToJSON(value) {
        if (value instanceof BaseModel) {
            return value.toJSON();
        } else if (Array.isArray(value)) {
            return value.map((item) => this.convertToJSON(item));
        } else if (value && typeof value === "object") {
            return Object.fromEntries(
                Object.entries(value)
                    .filter(([_, v]) => v !== undefined)
                    .map(([k, v]) => [k, this.convertToJSON(v)])
            );
        }
        return value;
    }
}

export class MedicalClaim extends BaseModel {
    constructor(data) {
        super(data);
        this.billing = new Provider(data.billing, "BillingProvider");
        this.claimInformation = new ClaimInformation(data.claimInformation);
        this.controlNumber = data.controlNumber;
        this.dependent = data.dependent ? new Dependent(data.dependent) : null;
        this.ordering = data.ordering
            ? new Provider(data.ordering, "OrderingProvider")
            : null;
        this.payToAddress = data.payToAddress
            ? new Address(data.payToAddress)
            : null;
        this.payToPlan = data.payToPlan ? new PayToPlan(data.payToPlan) : null;
        this.payerAddress = data.payerAddress
            ? new Address(data.payerAddress)
            : null;
        this.receiver = new Receiver(data.receiver);
        this.referring = data.referring
            ? new Provider(data.referring, "ReferringProvider")
            : null;
        this.rendering = data.rendering
            ? new Provider(data.rendering, "RenderingProvider")
            : null;
        this.submitter = new Submitter(data.submitter);
        this.subscriber = new Subscriber(data.subscriber);
        this.supervising = data.supervising
            ? new Provider(data.supervising, "SupervisingProvider")
            : null;
        this.tradingPartnerName = data.tradingPartnerName;
        this.tradingPartnerServiceId = data.tradingPartnerServiceId;
        this.usageIndicator = data.usageIndicator || "T";
        this.removeUndefinedProperties(this);
        this.validate(validateMedicalClaim.schema, this);
    }

    isValid() {
        try {
            this.validate(validateMedicalClaim.schema, this);
            return true;
        } catch (error) {
            console.error("Validation error:", error.message);
            return false;
        }
    }
}

class PayToPlan extends BaseModel {
    constructor(data) {
        super(
            [
                "address",
                "organizationName",
                "primaryIdentifier",
                "primaryIdentifierTypeCode",
                "taxIdentificationNumber",
            ],
            data
        );
        this.organizationName = data.organizationName;
        this.primaryIdentifier = data.primaryIdentifier;
        this.primaryIdentifierTypeCode = data.primaryIdentifierTypeCode;
        this.taxIdentificationNumber = data.taxIdentificationNumber;
        this.address = data.address ? new Address(data.address) : null;
        this.secondaryIdentifierTypeCode =
            data.secondaryIdentifierTypeCode || null;
        this.secondaryIdentifier = data.secondaryIdentifier || null;
        this.removeUndefinedProperties(this);
    }
}

class Receiver extends BaseModel {
    constructor(data) {
        super(["organizationName"], data);
        this.organizationName = data?.organizationName || null;

        this.removeUndefinedProperties(this);
    }
}

class Submitter extends BaseModel {
    constructor(data) {
        super(["contactInformation"], data);
        this.lastName = data.lastName;
        this.firstName = data.firstName;
        this.middleName = data.middleName;
        this.organizationName = data.organizationName;
        this.contactInformation = new ContactInformation(
            data.contactInformation
        );

        this.removeUndefinedProperties(this);
    }
    static schemaPath = "schema.properties.submitter";
    static optionalFields = [
        "organizationName",
        "lastName",
        "firstName",
        "middleName",
    ];
}

class ContactInformation extends BaseModel {
    constructor(data) {
        super(["name"], data);
        this.name = data.name;
        this.phoneNumber = data.phone_number;
        this.faxNumber = data.fax_number;
        this.email = data.email;
        this.phoneExtension = data.phone_extension;

        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.submitter.properties.contactInformation";
    static optionalFields = [
        "phoneNumber",
        "faxNumber",
        "email",
        "phoneExtension",
    ];
}

class Subscriber extends BaseModel {
    constructor(data) {
        super(["paymentResponsibilityLevelCode"], data);
        this.memberId = data.billing_claim_med?.ins_id || data.memberId;
        this.ssn = data.patient?.ssn || data.ssn;
        this.paymentResponsibilityLevelCode =
            data.paymentResponsibilityLevelCode;
        this.organizationName =
            data.patient_insurance?.organization_name || data.organizationName;
        this.insuranceTypeCode =
            data.patient_insurance?.type_id || data.insuranceTypeCode;
        this.subscriberGroupName =
            data.billing_claim_med?.insured_plan_name ||
            data.subscriberGroupName;
        this.firstName = data.firstName;
        this.lastName = data.lastName;
        this.middleName = data.middleName;
        this.suffix = data.suffix;
        this.gender = data.gender;
        this.dateOfBirth = data.dateOfBirth;
        this.policyNumber = data.policyNumber;
        this.groupNumber = data.groupNumber;
        this.contactInformation = data.subscriber_contact_info
            ? new ContactInformation(data.subscriber_contact_info)
            : null;
        this.address = data.address ? new Address(data.address) : null;

        this.removeUndefinedProperties(this);
    }
    static determineFromBillingClaim(value) {
        if (value === "P") {
            return "Primary";
        } else if (value === "S") {
            return "Secondary";
        } else if (value === "T") {
            return "Tertiary";
        } else {
            return null;
        }
    }

    static schemaPath = "schema.properties.subscriber";
    static optionalFields = [
        "memberId",
        "ssn",
        "organizationName",
        "insuranceTypeCode",
        "subscriberGroupName",
        "firstName",
        "lastName",
        "middleName",
        "suffix",
        "gender",
        "dateOfBirth",
        "policyNumber",
        "groupNumber",
        "contactInformation",
        "address",
    ];
}

class Dependent extends BaseModel {
    constructor(data) {
        super(
            [
                "firstName",
                "lastName",
                "dateOfBirth",
                "gender",
                "relationshipToSubscriberCode",
                "contactInformation",
                "address",
            ],
            data
        );
        this.firstName = data.firstName;
        this.lastName = data.lastName;
        this.middleName = data.middleName;
        this.suffix = data.suffix;
        this.gender = data.gender;
        this.dateOfBirth = data.dateOfBirth;
        this.ssn = data.ssn;
        this.memberId = data.memberId;
        this.relationshipToSubscriberCode = data.relationshipToSubscriberCode;
        this.contactInformation = data.dependent_contact_info
            ? new ContactInformation(data.dependent_contact_info)
            : null;
        this.address = data.dependent_address
            ? new Address(data.dependent_address)
            : null;

        this.removeUndefinedProperties(this);
    }
    static schemaPath = "schema.properties.dependent";
    static optionalFields = ["middleName", "suffix", "ssn", "memberId"];
}

class Provider extends BaseModel {
    constructor(data, providerType) {
        super(
            [
                "providerType",
                "npi",
                "firstName",
                "lastName",
                "contactInformation",
            ],
            data
        );
        console.log(`Building provider ${providerType} with npi ${data.npi}`);
        this.providerType = providerType || data.providerType; // Use the passed providerType or the one from data
        this.npi = data.npi;
        this.ssn = data.ssn;
        this.employerId = data.employerId;
        this.commercialNumber = data.commercialNumber;
        this.locationNumber = data.locationNumber;
        this.payerIdentificationNumber = data.payerIdentificationNumber;
        this.employerIdentificationNumber = data.employerIdentificationNumber;
        this.claimOfficeNumber = data.claimOfficeNumber;
        this.naic = data.naic;
        this.stateLicenseNumber = data.stateLicenseNumber;
        this.providerUpinNumber = data.providerUpinNumber;
        this.taxonomyCode = data.taxonomyCode;
        this.firstName = data.firstName;
        this.lastName = data.lastName;
        this.middleName = data.middleName;
        this.suffix = data.suffix;
        this.organizationName = data.organizationName;
        this.address = data.address ? new Address(data.address) : null;
        this.contactInformation = data.contactInformation
            ? new ContactInformation(data.contactInformation)
            : null;

        this.removeUndefinedProperties(this);
        this.validate(validateMedicalClaim.schema.properties.billing, this);
    }
}

class Address extends BaseModel {
    constructor(data) {
        super(["address1", "city", "state", "postalCode"], data);
        this.address1 = data.address1;
        this.address2 = data.address2;
        this.city = data.city;
        this.state = data.state;
        this.postalCode = data.postalCode;
        this.countryCode = data.countryCode;
        this.countrySubDivisionCode = data.countrySubDivisionCode;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.billing.properties.address,
            this
        );
    }

    static schemaPath = "schema.properties.billing.properties.address";
}

class ClaimInformation extends BaseModel {
    constructor(data) {
        super(
            [
                "benefitsAssignmentCertificationIndicator",
                "claimChargeAmount",
                "claimFilingCode",
                "claimFrequencyCode",
                "healthCareCodeInformation",
                "patientControlNumber",
                "placeOfServiceCode",
                "planParticipationCode",
                "releaseInformationCode",
                "serviceLines",
                "signatureIndicator",
            ],
            data
        );

        this.benefitsAssignmentCertificationIndicator =
            data.benefitsAssignmentCertificationIndicator;
        this.claimChargeAmount = data.claimChargeAmount;
        this.claimFilingCode = data.claimFilingCode;
        this.claimFrequencyCode = data.claimFrequencyCode;
        this.healthCareCodeInformation = data.healthCareCodeInformation;
        this.patientControlNumber = data.patientControlNumber;
        this.placeOfServiceCode = data.placeOfServiceCode;
        this.planParticipationCode = data.planParticipationCode;
        this.releaseInformationCode = data.releaseInformationCode;
        this.serviceLines = Array.isArray(data.serviceLines)
            ? data.serviceLines.map((line) => new ServiceLine(line))
            : null;
        this.signatureIndicator = data.signatureIndicator;
        this.propertyCasualtyClaimNumber = data.propertyCasualtyClaimNumber;
        this.deathDate = data.deathDate;
        this.patientWeight = data.patientWeight;
        this.pregnancyIndicator = data.pregnancyIndicator;
        this.specialProgramCode = data.specialProgramCode;
        this.delayReasonCode = data.delayReasonCode;
        this.claimDateInformation = data.claimDateInformation
            ? new ClaimDateInformation(data.claimDateInformation)
            : null;
        this.claimContractInformation = data.claimContractInformation
            ? new ClaimContractInformation(data.claimContractInformation)
            : null;
        this.claimSupplementalInformation = data.claimSupplementalInformation
            ? new ClaimSupplementalInformation(
                  data.claimSupplementalInformation
              )
            : null;
        this.relatedCausesCode = Array.isArray(data.relatedCausesCode)
            ? data.relatedCausesCode
            : typeof data.relatedCausesCode === "string"
              ? [data.relatedCausesCode]
              : [];
        this.autoAccidentStateCode = data.autoAccidentStateCode;
        this.autoAccidentCountryCode = data.autoAccidentCountryCode;
        this.otherSubscriberInformation = Array.isArray(
            data.otherSubscriberInformation
        )
            ? data.otherSubscriberInformation.map(
                  (item) => new OtherSubscriberInformationItem(item)
              )
            : [];
        this.claimPricingRepricingInformation =
            data.claimPricingRepricingInformation
                ? new ClaimPricingRepricingInformation(
                      data.claimPricingRepricingInformation
                  )
                : null;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation,
            this
        );
    }
    static schemaPath = "schema.properties.claimInformation";
    static optionalFields = [
        "serviceLines",
        "claimDateInformation",
        "claimContractInformation",
        "claimSupplementalInformation",
        "relatedCausesCode",
        "autoAccidentStateCode",
        "autoAccidentCountryCode",
        "otherSubscriberInformation",
    ];
}

class ClaimDateInformation extends BaseModel {
    constructor(data) {
        super(["dateTypeCode", "date"], data);
        this.dateTypeCode = data.dateTypeCode;
        this.date = data.date;

        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.claimDateInformation";
    static optionalFields = [];
}

// Loop: 2300, Segment: HCP
class ClaimPricingRepricingInformation extends BaseModel {
    constructor(data) {
        super(["pricingMethodologyCode", "repricedAllowedAmount"], data);
        this.pricingMethodologyCode = data.pricingMethodologyCode;
        this.repricedAllowedAmount = data.repricedAllowedAmount;
        this.repricedSavingAmount = data.repricedSavingAmount;
        this.repricingOrganizationIdentifier =
            data.repricingOrganizationIdentifier;
        this.repricingPerDiemOrFlatRateAmount =
            data.repricingPerDiemOrFlatRateAmount;
        this.repricedApprovedAmbulatoryPatientGroupCode =
            data.repricedApprovedAmbulatoryPatientGroupCode;
        this.repricedApprovedAmbulatoryPatientGroupAmount =
            data.repricedApprovedAmbulatoryPatientGroupAmount;
        this.rejectReasonCode = data.rejectReasonCode;

        this.removeUndefinedProperties(this);
    }

    static schemaPath =
        "schema.properties.claimInformation.properties.claimPricingRepricingInformation";
    static optionalFields = [
        "repricedSavingAmount",
        "repricingOrganizationIdentifier",
        "repricingPerDiemOrFlatRateAmount",
        "repricedApprovedAmbulatoryPatientGroupCode",
        "repricedApprovedAmbulatoryPatientGroupAmount",
        "rejectReasonCode",
    ];
}
// eslint-disable-next-line no-unused-vars
class SpinalManipulationServiceInformation extends BaseModel {
    constructor(data) {
        super(["patientConditionCode"], data);
        this.patientConditionCode = data.patientConditionCode;
        this.patientConditionDescription1 = data.patientConditionDescription1;
        this.patientConditionDescription2 = data.patientConditionDescription2;

        this.removeUndefinedProperties(this);
    }
}
// eslint-disable-next-line no-unused-vars
class ConditionInformation extends BaseModel {
    constructor(data) {
        super(["conditionCodes"], data);
        this.conditionCodes = data.conditionCodes;

        this.removeUndefinedProperties(this);
    }
}

class ConditionInformationVisionItem extends BaseModel {
    constructor(data) {
        super(
            [
                "certificationConditionIndicator",
                "codeCategory",
                "conditionCodes",
            ],
            data
        );
        this.codeCategory = data.codeCategory;
        this.certificationConditionIndicator =
            data.certificationConditionIndicator;
        // conditionCodes is an array of strings, directly assigned
        this.conditionCodes = data.conditionCodes;

        this.removeUndefinedProperties(this);
    }
}
// eslint-disable-next-line no-unused-vars
class PatientConditionInformationVision extends BaseModel {
    constructor(data) {
        super([], data);
        this.items = Array.isArray(data)
            ? data.map((item) => new ConditionInformationVisionItem(item))
            : typeof data === "object"
              ? [new ConditionInformationVisionItem(data)]
              : [];

        this.removeUndefinedProperties(this);
    }
}

class OtherSubscriberInformationItem extends BaseModel {
    constructor(data) {
        super(
            [
                "benefitsAssignmentCertificationIndicator",
                "claimFilingIndicatorCode",
                "individualRelationshipCode",
                "otherPayerName",
                "otherSubscriberName",
                "paymentResponsibilityLevelCode",
                "releaseOfInformationCode",
            ],
            data
        );

        this.benefitsAssignmentCertificationIndicator =
            data.benefitsAssignmentCertificationIndicator;
        this.claimFilingIndicatorCode = data.claimFilingIndicatorCode;
        this.individualRelationshipCode = data.individualRelationshipCode;
        this.otherPayerName = new OtherPayerName(data.otherPayerName);
        this.otherSubscriberName = new OtherSubscriberName(
            data.otherSubscriberName
        );
        this.paymentResponsibilityLevelCode =
            data.paymentResponsibilityLevelCode;
        this.releaseOfInformationCode = data.releaseOfInformationCode;
        this.insuranceGroupOrPolicyNumber = data.insuranceGroupOrPolicyNumber;
        this.otherInsuredGroupName = data.otherInsuredGroupName;
        this.insuranceTypeCode = data.insuranceTypeCode;
        this.claimLevelAdjustments = Array.isArray(data.claimLevelAdjustments)
            ? data.claimLevelAdjustments.map(
                  (adj) => new ClaimLevelAdjustment(adj)
              )
            : [];
        this.nonCoveredChargeAmount = data.nonCoveredChargeAmount;
        this.remainingPatientLiability = data.remainingPatientLiability;
        this.patientSignatureGeneratedForPatient =
            data.patientSignatureGeneratedForPatient;
        this.medicareOutpatientAdjudication =
            data.medicareOutpatientAdjudication
                ? new MedicareOutpatientAdjudication(
                      data.medicareOutpatientAdjudication
                  )
                : null;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.otherSubscriberInformation,
            this
        );
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.otherSubscriberInformation";
    static optionalFields = [
        "claimLevelAdjustments",
        "medicareOutpatientAdjudication",
    ];
}
class OtherPayerName extends BaseModel {
    constructor(data) {
        super(
            [
                "otherPayerIdentifier",
                "otherPayerIdentifierTypeCode",
                "otherPayerOrganizationName",
            ],
            data
        );
        this.otherPayerIdentifier = data.otherPayerIdentifier;
        this.otherPayerIdentifierTypeCode = data.otherPayerIdentifierTypeCode;
        this.otherPayerOrganizationName = data.otherPayerOrganizationName;
        this.otherInsuredAdditionalIdentifier =
            data.otherInsuredAdditionalIdentifier;
        this.otherPayerAddress = data.otherPayerAddress
            ? new Address(data.otherPayerAddress)
            : null;
        this.otherPayerAdjudicationOrPaymentDate =
            data.otherPayerAdjudicationOrPaymentDate;
        this.otherPayerSecondaryIdentifier = Array.isArray(
            data.otherPayerSecondaryIdentifier
        )
            ? data.otherPayerSecondaryIdentifier.map(
                  (item) => new ReferenceIdentification(item)
              )
            : [];
        this.otherPayerPriorAuthorizationNumber =
            data.otherPayerPriorAuthorizationNumber;
        this.otherPayerPriorAuthorizationOrReferralNumber =
            data.otherPayerPriorAuthorizationOrReferralNumber;
        this.otherPayerClaimAdjustmentIndicator =
            data.otherPayerClaimAdjustmentIndicator;
        this.otherPayerClaimControlNumber = data.otherPayerClaimControlNumber;

        this.removeUndefinedProperties(this);
    }

    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.otherSubscriberInformation.properties.otherPayerName";
    static optionalFields = [
        "otherInsuredAdditionalIdentifier",
        "otherPayerAddress",
        "otherPayerAdjudicationOrPaymentDate",
        "otherPayerSecondaryIdentifier",
        "otherPayerPriorAuthorizationNumber",
        "otherPayerPriorAuthorizationOrReferralNumber",
        "otherPayerClaimAdjustmentIndicator",
        "otherPayerClaimControlNumber",
    ];
}
class ReferenceIdentification extends BaseModel {
    constructor(data) {
        super(["identifier", "qualifier"], data);
        this.qualifier = data.qualifier;
        this.identifier = data.identifier;
        this.otherIdentifier = data.otherIdentifier;

        this.removeUndefinedProperties(this);
    }

    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.otherSubscriberInformation.properties.otherPayerName.properties.otherPayerSecondaryIdentifier.items";
    static optionalFields = ["otherIdentifier"];
}
class ClaimLevelAdjustment extends BaseModel {
    constructor(data) {
        super(["adjustmentDetails", "adjustmentGroupCode"], data);
        this.adjustmentDetails = data.adjustmentDetails.map(
            (detail) => new AdjustmentDetail(detail)
        );
        this.adjustmentGroupCode = data.adjustmentGroupCode;
        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.otherSubscriberInformation
                .properties.claimLevelAdjustments,
            this
        );
    }
}

class AdjustmentDetail extends BaseModel {
    constructor(data) {
        super(["adjustmentAmount", "adjustmentReasonCode"], data);
        this.adjustmentAmount = data.adjustmentAmount;
        this.adjustmentReasonCode = data.adjustmentReasonCode;
        this.adjustmentQuantity = data.adjustmentQuantity;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.otherSubscriberInformation
                .properties.claimLevelAdjustments.properties.adjustmentDetails,
            this
        );
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.otherSubscriberInformation.properties.claimLevelAdjustments.properties.adjustmentDetails";
    static optionalFields = ["adjustmentQuantity"];
}

class MedicareOutpatientAdjudication extends BaseModel {
    constructor(data) {
        super([], data);
        this.reimbursementRate = data.reimbursementRate;
        this.hcpcsPayableAmount = data.hcpcsPayableAmount;
        this.claimPaymentRemarkCode = Array.isArray(data.claimPaymentRemarkCode)
            ? data.claimPaymentRemarkCode
            : [];
        this.endStageRenalDiseasePaymentAmount =
            data.endStageRenalDiseasePaymentAmount;
        this.nonPayableProfessionalComponentBilledAmount =
            data.nonPayableProfessionalComponentBilledAmount;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.otherSubscriberInformation
                .properties.medicareOutpatientAdjudication,
            this
        );
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.otherSubscriberInformation.properties.medicareOutpatientAdjudication";
    static optionalFields = ["claimPaymentRemarkCode"];
}

class OtherSubscriberName extends BaseModel {
    constructor(data) {
        super(
            [
                "otherInsuredIdentifier",
                "otherInsuredIdentifierTypeCode",
                "otherInsuredLastName",
                "otherInsuredQualifier",
            ],
            data
        );
        this.otherInsuredQualifier = data.otherInsuredQualifier;
        this.otherInsuredLastName = data.otherInsuredLastName;
        this.otherInsuredFirstName = data.otherInsuredFirstName;
        this.otherInsuredMiddleName = data.otherInsuredMiddleName;
        this.otherInsuredNameSuffix = data.otherInsuredNameSuffix;
        this.otherInsuredIdentifierTypeCode =
            data.otherInsuredIdentifierTypeCode;
        this.otherInsuredIdentifier = data.otherInsuredIdentifier;
        this.otherInsuredAddress = new Address(data.otherInsuredAddress);
        this.otherInsuredAdditionalIdentifier =
            data.otherInsuredAdditionalIdentifier;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.otherSubscriberInformation
                .properties.otherSubscriberName,
            this
        );
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.otherSubscriberInformation.properties.otherSubscriberName";
    static optionalFields = [
        "otherInsuredAddress",
        "otherInsuredAdditionalIdentifier",
    ];
}

class ServiceLine extends BaseModel {
    constructor(data) {
        super(["professionalService", "serviceDate"], data);
        this.additionalNotes = data.additionalNotes;
        this.ambulanceCertification = data.ambulanceCertification
            ? data.ambulanceCertification.map(
                  (item) => new AmbulanceCertification(item)
              )
            : [];
        this.ambulanceDropOffLocation = data.ambulanceDropOffLocation
            ? new Location(data.ambulanceDropOffLocation)
            : null;
        this.ambulancePatientCount = data.ambulancePatientCount;
        this.ambulancePickUpLocation = data.ambulancePickUpLocation
            ? new Location(data.ambulancePickUpLocation)
            : null;
        this.ambulanceTransportInformation = data.ambulanceTransportInformation
            ? new AmbulanceTransportInformation(
                  data.ambulanceTransportInformation
              )
            : null;
        this.assignedNumber = data.assignedNumber;
        this.conditionIndicatorDurableMedicalEquipment =
            data.conditionIndicatorDurableMedicalEquipment;
        this.contractInformation = data.contractInformation
            ? new ContractInformation(data.contractInformation)
            : null;
        this.drugIdentification = data.drugIdentification
            ? new DrugIdentification(data.drugIdentification)
            : null;
        this.durableMedicalEquipmentCertificateOfMedicalNecessity =
            data.durableMedicalEquipmentCertificateOfMedicalNecessity
                ? new DurableMedicalEquipmentCertificateOfMedicalNecessity(
                      data.durableMedicalEquipmentCertificateOfMedicalNecessity
                  )
                : null;
        this.durableMedicalEquipmentCertification =
            data.durableMedicalEquipmentCertification
                ? new DurableMedicalEquipmentCertification(
                      data.durableMedicalEquipmentCertification
                  )
                : null;
        this.durableMedicalEquipmentService =
            data.durableMedicalEquipmentService
                ? new DurableMedicalEquipmentService(
                      data.durableMedicalEquipmentService
                  )
                : null;
        this.fileInformation = data.fileInformation
            ? new FileInformation(data.fileInformation)
            : null;
        this.formIdentification = data.formIdentification
            ? new FormIdentification(data.formIdentification)
            : null;
        this.goalRehabOrDischargePlans = data.goalRehabOrDischargePlans;
        this.hospiceEmployeeIndicator = data.hospiceEmployeeIndicator;
        this.lineAdjudicationInformation = data.lineAdjudicationInformation
            ? new LineAdjudicationInformation(data.lineAdjudicationInformation)
            : null;
        this.linePricingRepricingInformation =
            data.linePricingRepricingInformation
                ? new LinePricingRepricingInformation(
                      data.linePricingRepricingInformation
                  )
                : null;
        this.obstetricAnesthesiaAdditionalUnits =
            data.obstetricAnesthesiaAdditionalUnits;
        this.orderingProvider = data.orderingProvider
            ? new Provider(data.orderingProvider, "orderingProvider")
            : null;
        this.postageTaxAmount = data.postageTaxAmount;
        this.professionalService = data.professionalService
            ? new ProfessionalService(data.professionalService)
            : null;
        this.providerControlNumber = data.providerControlNumber;
        this.purchasedServiceInformation = data.purchasedServiceInformation
            ? new PurchasedServiceInformation(data.purchasedServiceInformation)
            : null;
        this.purchasedServiceProvider = data.purchasedServiceProvider
            ? new Provider(
                  data.purchasedServiceProvider,
                  "purchasedServiceProvider"
              )
            : null;
        this.referringProvider = data.referringProvider
            ? new Provider(data.referringProvider, "referringProvider")
            : null;
        this.renderingProvider = data.renderingProvider
            ? new Provider(data.renderingProvider, "renderingProvider")
            : null;
        this.salesTaxAmount = data.salesTaxAmount;
        this.serviceDate = data.serviceDate;
        this.serviceDateEnd = data.serviceDateEnd;
        this.serviceFacilityLocation = data.serviceFacilityLocation
            ? new ServiceFacilityLocation(data.serviceFacilityLocation)
            : null;
        this.serviceLineDateInformation = data.serviceLineDateInformation
            ? new ServiceLineDateInformation(data.serviceLineDateInformation)
            : null;
        this.serviceLineReferenceInformation =
            data.serviceLineReferenceInformation
                ? new ServiceLineReferenceInformation(
                      data.serviceLineReferenceInformation
                  )
                : null;
        this.serviceLineSupplementalInformation =
            data.serviceLineSupplementalInformation
                ? data.serviceLineSupplementalInformation.map(
                      (item) => new ServiceLineSupplementalInformation(item)
                  )
                : [];
        this.supervisingProvider = data.supervisingProvider
            ? new Provider(data.supervisingProvider, "supervisingProvider")
            : null;
        this.testResults = data.testResults
            ? data.testResults.map((item) => new TestResult(item))
            : [];
        this.thirdPartyOrganizationNotes = data.thirdPartyOrganizationNotes;
        this.removeUndefinedProperties(this);
    }

    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items";
    static optionalFields = [
        "additionalNotes",
        "ambulanceCertification",
        "ambulanceDropOffLocation",
        "ambulancePatientCount",
        "ambulancePickUpLocation",
        "ambulanceTransportInformation",
        "conditionIndicatorDurableMedicalEquipment",
        "contractInformation",
        "drugIdentification",
        "durableMedicalEquipmentCertificateOfMedicalNecessity",
        "durableMedicalEquipmentCertification",
        "durableMedicalEquipmentService",
        "fileInformation",
        "formIdentification",
        "goalRehabOrDischargePlans",
        "hospiceEmployeeIndicator",
        "lineAdjudicationInformation",
        "linePricingRepricingInformation",
        "obstetricAnesthesiaAdditionalUnits",
        "orderingProvider",
        "postageTaxAmount",
        "providerControlNumber",
        "purchasedServiceInformation",
        "purchasedServiceProvider",
        "referringProvider",
        "renderingProvider",
        "salesTaxAmount",
        "serviceDateEnd",
        "serviceFacilityLocation",
        "serviceLineDateInformation",
        "serviceLineReferenceInformation",
        "serviceLineSupplementalInformation",
        "supervisingProvider",
        "testResults",
        "thirdPartyOrganizationNotes",
    ];
}

class AmbulanceCertification extends BaseModel {
    constructor(data) {
        super(["certificationType"], data);
        this.certificationType = data.certificationType;
        this.certificationConditionIndicator =
            data.certificationConditionIndicator;
        this.certificationConditionIndicatorDate =
            data.certificationConditionIndicatorDate;
        this.certificationConditionIndicatorTime =
            data.certificationConditionIndicatorTime;
        this.certificationConditionIndicatorTimezone =
            data.certificationConditionIndicatorTimezone;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.ambulanceCertification";
    static optionalFields = [
        "certificationConditionIndicator",
        "certificationConditionIndicatorDate",
        "certificationConditionIndicatorTime",
        "certificationConditionIndicatorTimezone",
    ];
}

class Location extends BaseModel {
    constructor(data) {
        super(["address1", "city", "state", "postalCode"], data);
        this.address1 = data.address1;
        this.address2 = data.address2;
        this.city = data.city;
        this.state = data.state;
        this.postalCode = data.postalCode;
        this.countryCode = data.countryCode;
        this.countrySubDivisionCode = data.countrySubDivisionCode;
        this.removeUndefinedProperties(this);
    }
    static optionalFields = [
        "address2",
        "postalCode",
        "countrySubDivisionCode",
    ];
}
class SecondaryIdentifier extends BaseModel {
    constructor(data) {
        super(["identifier", "qualifier"], data);
        this.qualifier = data.qualifier;
        this.identifier = data.identifier;
        this.otherIdentifier = data.otherIdentifier;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.renderingProvider.properties.secondaryIdentifier";
    static optionalFields = ["otherIdentifier"];
}

class AmbulanceTransportInformation extends BaseModel {
    constructor(data) {
        super(
            ["ambulanceTransportReasonCode", "transportDistanceInMiles"],
            data
        );
        this.ambulanceTransportReasonCode = data.ambulanceTransportReasonCode;
        this.transportDistanceInMiles = data.transportDistanceInMiles;
        this.patientWeightInPounds = data.patientWeightInPounds;
        this.roundTripPurposeDescription = data.roundTripPurposeDescription;
        this.stretcherPurposeDescription = data.stretcherPurposeDescription;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.ambulanceTransportInformation";
    static optionalFields = [
        "patientWeightInPounds",
        "roundTripPurposeDescription",
        "stretcherPurposeDescription",
    ];
}
// CN1
class ContractInformation extends BaseModel {
    constructor(data) {
        super(["contractTypeCode"], data);
        this.contractTypeCode = data.contractTypeCode;
        this.contractAmount = data.contractAmount;
        this.contractPercentage = data.contractPercentage;
        this.contractCode = data.contractCode;
        this.termsDiscountPercentage = data.termsDiscountPercentage;
        this.contractVersionIdentifier = data.contractVersionIdentifier;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.contractInformation";
    static optionalFields = [
        "contractNotes",
        "contractAmount",
        "contractPercentage",
        "contractCode",
        "termsDiscountPercentage",
        "contractVersionIdentifier",
    ];
}
class DrugIdentification extends BaseModel {
    constructor(data) {
        super(
            [
                "serviceIdQualifier",
                "nationalDrugCode",
                "nationalDrugUnitCount",
                "measurementUnitCode",
            ],
            data
        );
        this.serviceIdQualifier = data.serviceIdQualifier;
        this.nationalDrugCode = data.nationalDrugCode;
        this.nationalDrugUnitCount = data.nationalDrugUnitCount;
        this.measurementUnitCode = data.measurementUnitCode;
        this.linkSequenceNumber = data.linkSequenceNumber;
        this.pharmacyPrescriptionNumber = data.pharmacyPrescriptionNumber;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.drugIdentification";
    static optionalFields = [
        "linkSequenceNumber",
        "pharmacyPrescriptionNumber",
    ];
}

class DurableMedicalEquipmentCertificateOfMedicalNecessity extends BaseModel {
    constructor(data) {
        super(["certificateNumber", "issueDate"], data);
        this.certificateNumber = data.certificateNumber;
        this.issueDate = data.issueDate;
        this.expiryDate = data.expiryDate;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.durableMedicalEquipmentCertificateOfMedicalNecessity";
    static optionalFields = ["expiryDate"];
}

class DurableMedicalEquipmentCertification extends BaseModel {
    constructor(data) {
        super(["certificationType"], data);
        this.certificationType = data.certificationType;
        this.certificationDate = data.certificationDate;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = ["certificationDate"];
}

class DurableMedicalEquipmentService extends BaseModel {
    constructor(data) {
        super(["serviceId", "serviceStartDate"], data);
        this.serviceId = data.serviceId;
        this.serviceStartDate = data.serviceStartDate;
        this.serviceEndDate = data.serviceEndDate;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = ["serviceEndDate"];
}

class FileInformation extends BaseModel {
    constructor(data) {
        super(["fileTypeCode", "fileIdentifier"], data);
        this.fileTypeCode = data.fileTypeCode;
        this.fileIdentifier = data.fileIdentifier;
        this.supportingDocumentation = data.supportingDocumentation
            ? data.supportingDocumentation.map(
                  (doc) => new SupportingDocumentation(doc)
              )
            : [];
        this.removeUndefinedProperties(this);
    }

    static optionalFields = ["supportingDocumentation"];
}

class FormIdentification extends BaseModel {
    constructor(data) {
        super(["formTypeCode", "formIdentifier"], data);
        this.formTypeCode = data.formTypeCode;
        this.formIdentifier = data.formIdentifier;
        this.supportingDocumentation = data.supportingDocumentation
            ? data.supportingDocumentation.map(
                  (doc) => new SupportingDocumentation(doc)
              )
            : [];
        this.removeUndefinedProperties(this);
    }

    static optionalFields = ["supportingDocumentation"];
}

class LineAdjudicationInformation extends BaseModel {
    constructor(data) {
        super(
            [
                "adjudicationOrPaymentDate",
                "otherPayerPrimaryIdentifier",
                "paidServiceUnitCount",
                "procedureCode",
                "serviceIdQualifier",
                "serviceLinePaidAmount",
            ],
            data
        );
        this.adjudicationOrPaymentDate = data.adjudicationOrPaymentDate;
        this.otherPayerPrimaryIdentifier = data.otherPayerPrimaryIdentifier;
        this.paidServiceUnitCount = data.paidServiceUnitCount;
        this.procedureCode = data.procedureCode;
        this.serviceIdQualifier = data.serviceIdQualifier;
        this.serviceLinePaidAmount = data.serviceLinePaidAmount;
        this.procedureModifier = data.procedureModifier
            ? data.procedureModifier.map(
                  (modifier) => new ProcedureModifier(modifier)
              )
            : [];
        this.procedureCodeDescription = data.procedureCodeDescription;
        this.bundledOrUnbundledLineNumber = data.bundledOrUnbundledLineNumber;
        this.claimAdjustmentInformation = data.claimAdjustmentInformation
            ? data.claimAdjustmentInformation.map(
                  (info) => new ClaimAdjustmentInformation(info)
              )
            : [];
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [
        "procedureModifier",
        "procedureCodeDescription",
        "bundledOrUnbundledLineNumber",
        "claimAdjustmentInformation",
    ];
}
class ProcedureModifier extends BaseModel {
    constructor(data) {
        super([], data);
        this.modifier = data;

        this.removeUndefinedProperties(this);
    }

    static schemaPath =
        "schema.properties.claimInformation.properties.serviceLines.items.properties.lineAdjudicationInformation.properties.procedureModifier.items";
    static optionalFields = [];
}
class ClaimAdjustmentInformation extends BaseModel {
    constructor(data) {
        super(["adjustmentGroupCode", "adjustmentDetails"], data);
        this.adjustmentGroupCode = data.adjustmentGroupCode;
        this.adjustmentDetails = data.adjustmentDetails.map(
            (detail) => new AdjustmentDetail(detail)
        );
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [];
}

class LinePricingRepricingInformation extends BaseModel {
    constructor(data) {
        super(["pricingMethodologyCode", "repricedAllowedAmount"], data);
        this.pricingMethodologyCode = data.pricingMethodologyCode;
        this.repricedAllowedAmount = data.repricedAllowedAmount;
        this.repricedSavingAmount = data.repricedSavingAmount;
        this.repricingOrganizationIdentifier =
            data.repricingOrganizationIdentifier;
        this.repricingPerDiemOrFlatRateAmount =
            data.repricingPerDiemOrFlatRateAmount;
        this.repricedApprovedAmbulatoryPatientGroupCode =
            data.repricedApprovedAmbulatoryPatientGroupCode;
        this.repricedApprovedAmbulatoryPatientGroupAmount =
            data.repricedApprovedAmbulatoryPatientGroupAmount;
        this.rejectReasonCode = data.rejectReasonCode;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [
        "repricedSavingAmount",
        "repricingOrganizationIdentifier",
        "repricingPerDiemOrFlatRateAmount",
        "repricedApprovedAmbulatoryPatientGroupCode",
        "repricedApprovedAmbulatoryPatientGroupAmount",
        "rejectReasonCode",
    ];
}

class PurchasedServiceInformation extends BaseModel {
    constructor(data) {
        super(
            [
                "purchasedServiceProviderIdentifier",
                "purchasedServiceChargeAmount",
            ],
            data
        );
        // Loop: 2400, Segment: PS1, Element: PS101, Example: 01
        this.purchasedServiceProviderIdentifier =
            data.purchasedServiceProviderIdentifier;
        // Loop: 2400, Segment: PS1, Element: PS102, Example: 10
        this.purchasedServiceChargeAmount = data.purchasedServiceChargeAmount;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [];
}

class ProfessionalService extends BaseModel {
    constructor(data) {
        super(
            [
                "compositeDiagnosisCodePointers",
                "lineItemChargeAmount",
                "measurementUnit",
                "procedureCode",
                "procedureIdentifier",
                "serviceUnitCount",
            ],
            data
        );
        if (!data) {
            throw new Error(`No data provided for ${this.constructor.name}`);
        }
        this.procedureIdentifier = data.procedureIdentifier;
        this.procedureCode = data.procedureCode;
        this.procedureModifiers = data.procedureModifiers || [];
        this.description = data.description;
        this.lineItemChargeAmount = data.lineItemChargeAmount;
        this.measurementUnit = data.measurementUnit;
        this.serviceUnitCount = data.serviceUnitCount;
        this.placeOfServiceCode = data.placeOfServiceCode;
        this.compositeDiagnosisCodePointers =
            new CompositeDiagnosisCodePointers(
                data.compositeDiagnosisCodePointers
            );
        this.emergencyIndicator = data.emergencyIndicator;
        this.epsdtIndicator = data.epsdtIndicator;
        this.familyPlanningIndicator = data.familyPlanningIndicator;
        this.copayStatusCode = data.copayStatusCode;

        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.professionalService,
            this
        );
    }
}

class CompositeDiagnosisCodePointers extends BaseModel {
    constructor(data) {
        super(["diagnosisCodePointers"], data);
        if (!data) {
            throw new Error(`No data provided for ${this.constructor.name}`);
        }
        this.diagnosisCodePointers = data.diagnosisCodePointers;
        this.removeUndefinedProperties(this);
        this.validate(
            validateMedicalClaim.schema.properties.claimInformation.properties
                .serviceLines.items.properties.professionalService.properties
                .compositeDiagnosisCodePointers,
            this
        );
    }
}

class ServiceFacilityLocation extends BaseModel {
    constructor(data) {
        super(["organizationName", "address"], data);
        this.organizationName = data.organizationName;
        this.address = new Address(data.address);
        this.secondaryIdentifier = data.secondaryIdentifier
            ? data.secondaryIdentifier.map((id) => new SecondaryIdentifier(id))
            : [];
        this.phoneName = data.phoneName;
        this.phoneNumber = data.phoneNumber;
        this.phoneExtension = data.phoneExtension;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [
        "secondaryIdentifier",
        "phoneName",
        "phoneNumber",
        "phoneExtension",
    ];
}

class ServiceLineDateInformation extends BaseModel {
    constructor(data) {
        super(
            [
                "prescriptionDate",
                "certificationRevisionOrRecertificationDate",
                "beginTherapyDate",
                "lastCertificationDate",
                "treatmentOrTherapyDate",
                "hemoglobinTestDate",
                "serumCreatineTestDate",
                "shippedDate",
                "lastXRayDate",
                "initialTreatmentDate",
            ],
            data
        );
        this.prescriptionDate = data.prescriptionDate;
        this.certificationRevisionOrRecertificationDate =
            data.certificationRevisionOrRecertificationDate;
        this.beginTherapyDate = data.beginTherapyDate;
        this.lastCertificationDate = data.lastCertificationDate;
        this.treatmentOrTherapyDate = data.treatmentOrTherapyDate;
        this.hemoglobinTestDate = data.hemoglobinTestDate;
        this.serumCreatineTestDate = data.serumCreatineTestDate;
        this.shippedDate = data.shippedDate;
        this.lastXRayDate = data.lastXRayDate;
        this.initialTreatmentDate = data.initialTreatmentDate;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [];
}

class ServiceLineReferenceInformation extends BaseModel {
    constructor(data) {
        super(
            ["adjustedRepricedLineItemReferenceNumber", "priorAuthorization"],
            data
        );
        this.adjustedRepricedLineItemReferenceNumber =
            data.adjustedRepricedLineItemReferenceNumber;
        this.priorAuthorization = data.priorAuthorization
            ? data.priorAuthorization.map(
                  (auth) => new PriorAuthorization(auth)
              )
            : [];
        this.mammographyCertificationNumber =
            data.mammographyCertificationNumber;
        this.clinicalLaboratoryImprovementAmendmentNumber =
            data.clinicalLaboratoryImprovementAmendmentNumber;
        this.referringCliaNumber = data.referringCliaNumber;
        this.immunizationBatchNumber = data.immunizationBatchNumber;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [
        "mammographyCertificationNumber",
        "clinicalLaboratoryImprovementAmendmentNumber",
        "referringCliaNumber",
        "immunizationBatchNumber",
    ];
}

class PriorAuthorization extends BaseModel {
    constructor(data) {
        super(["priorAuthorizationOrReferralNumber"], data);
        this.priorAuthorizationOrReferralNumber =
            data.priorAuthorizationOrReferralNumber;
        this.otherPayerPrimaryIdentifier = data.otherPayerPrimaryIdentifier;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = ["otherPayerPrimaryIdentifier"];
}

class ServiceLineSupplementalInformation extends BaseModel {
    constructor(data) {
        super(
            ["supplementalInformationCode", "supplementalInformationValue"],
            data
        );
        this.supplementalInformationCode = data.supplementalInformationCode;
        this.supplementalInformationValue = data.supplementalInformationValue;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [];
}
// eslint-disable-next-line no-unused-vars
class SupplementalInformation extends BaseModel {
    constructor(data) {
        super(["attachmentReportTypeCode", "attachmentTransmissionCode"], data);
        this.attachmentReportTypeCode = data.attachmentReportTypeCode;
        this.attachmentTransmissionCode = data.attachmentTransmissionCode;
        this.attachmentControlNumber = data.attachmentControlNumber;
        this.removeUndefinedProperties(this);
    }

    static schemaPath =
        "schema.properties.claimInformation.properties.supportingDocumentation";
    static optionalFields = ["attachmentControlNumber"];
}

class SupportingDocumentation extends BaseModel {
    constructor(data) {
        super(["questionNumber"], data);
        this.questionNumber = data.questionNumber;
        this.questionResponseCode = data.questionResponseCode;
        this.questionResponse = data.questionResponse;
        this.questionResponseAsDate = data.questionResponseAsDate;
        this.questionResponseAsPercent = data.questionResponseAsPercent;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [
        "questionResponseCode",
        "questionResponse",
        "questionResponseAsDate",
        "questionResponseAsPercent",
    ];
}

class TestResult extends BaseModel {
    constructor(data) {
        super(["testResultCode", "testResultValue"], data);
        this.testResultCode = data.testResultCode || null;
        this.testResultValue = data.testResultValue || null;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [];
}

// Loop 2300, Segment: CN1
class ClaimContractInformation extends BaseModel {
    constructor(data) {
        super([], data);
        this.contractTypeCode = data.contractTypeCode;
        this.contractAmount = data.contractAmount;
        this.contractPercentage = data.contractPercentage;
        this.contractCode = data.contractCode;
        this.contractTermsDiscountPercentage =
            data.contractTermsDiscountPercentage;
        this.contractTermsDiscountAmount = data.contractTermsDiscountAmount;
        this.contractTermsDiscountDays = data.contractTermsDiscountDays;
        this.contractTermsNetDays = data.contractTermsNetDays;
        this.contractTermsDescription = data.contractTermsDescription;
        this.removeUndefinedProperties(this);
    }
    static schemaPath =
        "schema.properties.claimInformation.properties.claimContractInformation";
    static optionalFields = [
        "termsDiscountPercentage",
        "contractVersionIdentifier",
        "contractVersionIdentifier",
        "contractCode",
        "contractAmount",
        "contractPercentage",
        "contractVersionIdentifier",
    ];
}

class ClaimSupplementalInformation extends BaseModel {
    constructor(data) {
        super(["reportInformation"], data);
        this.reportInformation = data.reportInformation
            ? data.reportInformation.map((info) => new ReportInformation(info))
            : null;
        this.priorAuthorizationNumber = data.priorAuthorizationNumber;
        this.referralNumber = data.referralNumber;
        this.claimControlNumber = data.claimControlNumber;
        this.cliaNumber = data.cliaNumber;
        this.repricedClaimNumber = data.repricedClaimNumber;
        this.adjustedRepricedClaimNumber = data.adjustedRepricedClaimNumber;
        this.investigationalDeviceExemptionNumber =
            data.investigationalDeviceExemptionNumber;
        this.claimNumber = data.claimNumber;
        this.mammographyCertificationNumber =
            data.mammographyCertificationNumber;
        this.medicalRecordNumber = data.medicalRecordNumber;
        this.demoProjectIdentifier = data.demoProjectIdentifier;
        this.carePlanOversightNumber = data.carePlanOversightNumber;
        this.medicareCrossoverReferenceId = data.medicareCrossoverReferenceId;
        this.serviceAuthorizationExceptionCode =
            data.serviceAuthorizationExceptionCode;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = [
        "priorAuthorizationNumber",
        "referralNumber",
        "claimControlNumber",
        "cliaNumber",
        "repricedClaimNumber",
        "adjustedRepricedClaimNumber",
        "investigationalDeviceExemptionNumber",
        "claimNumber",
        "mammographyCertificationNumber",
        "medicalRecordNumber",
        "demoProjectIdentifier",
        "carePlanOversightNumber",
        "medicareCrossoverReferenceId",
        "serviceAuthorizationExceptionCode",
    ];
}

class ReportInformation extends BaseModel {
    constructor(data) {
        super(["attachmentReportTypeCode", "attachmentTransmissionCode"], data);
        this.attachmentReportTypeCode = data.attachmentReportTypeCode;
        this.attachmentTransmissionCode = data.attachmentTransmissionCode;
        this.attachmentControlNumber = data.attachmentControlNumber;
        this.removeUndefinedProperties(this);
    }

    static optionalFields = ["attachmentControlNumber"];
}
