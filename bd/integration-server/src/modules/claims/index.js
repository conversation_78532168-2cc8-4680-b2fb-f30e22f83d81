import fp from "fastify-plugin";
import nunjucks from "nunjucks";
import autoload from "@fastify/autoload";
import { join } from "desm";
import { generate_control_number } from "../../database/pg_integrationsdb.js";
import { logger } from "../../utils/logger.js";
import path from "path";

function render_template(template, sdata) {
    const env = nunjucks.configure(this.change_template_path, {
        autoescape: false,
        trimBlocks: true,
        lstripBlocks: true,
    });
    if (!template) {
        throw new Error("No template specified");
    } else {
        if (!template.endsWith(".njk")) {
            template = template + ".njk";
        }
    }
    if (!sdata) {
        throw new Error("No data specified");
    }

    return env.render(template, sdata);
}

async function ChangeHealthErrorHandler(error, req, reply) {
    if (
        error.name != "ChangeHealthError" &&
        !req.context.config.url.includes("claims")
    ) {
        req.log.info(
            "did not call changehealth error handler " +
                req.raw.url +
                " " +
                JSON.stringify(error, Object.getOwnPropertyNames(error))
        );
        throw error;
    }

    req.log.error(
        `Change - ChangeHealthErrorHandler called: ${typeof error == "object" ? JSON.stringify(error) : error}`
    );
    if (error.stack) {
        req.log.error(`Error stack trace: ${error.stack}`);
    }

    let ed =
        error.error_description ||
        error.description ||
        error.message ||
        "Unknown error occurred";

    if (Array.isArray(ed)) {
        ed = ed.map((e) => {
            if (typeof e === "object") {
                return `${e.field}: ${e.description ? e.description : e.message}`;
            }
            return String(e);
        });
    } else if (
        typeof ed === "object" &&
        ed.errors &&
        Array.isArray(ed.errors)
    ) {
        ed = ed.errors.map((e) => {
            if (typeof e === "object") {
                return `${e.field}: ${e.message}`;
            }
            return String(e);
        });
    } else if (typeof ed === "object") {
        ed = JSON.stringify(ed, null, 2);
    } else {
        req.log.info(`ed is a ${typeof ed}`);
        ed = String(ed);
    }

    const responseError = {
        error_code: error.error_code || error.statusCode || 400,
        error_type: error.type || "validation",
        error_description: Array.isArray(ed) ? ed : [ed],
        error: error.message || "there was an exception processing the request",
    };

    // When sending the response, let Fastify handle the JSON stringification
    reply.code(400).send(responseError);
    return reply;
}

async function claimsprocessor(app, opts) {
    // Create base claims logger
    app.log = app.log.child({
        name: "Claims",
        component: "Claims",
    });

    const autoloadOpts = {
        dir: join(import.meta.url, "routes"),
        options: {
            prefix: opts.prefix,
            loggerFactory: () => app.log,
        },
    };

    // Make claims logger available to app
    app.decorate(
        "change_template_path",
        join(import.meta.url) + "/fixtures/templates/"
    );

    app.decorate("change_render_template", render_template);
    app.decorate("gen_control_number", () => generate_control_number(app));

    app.register(autoload, autoloadOpts);
    await app.setErrorHandler(ChangeHealthErrorHandler);
}

export default fp(claimsprocessor);
