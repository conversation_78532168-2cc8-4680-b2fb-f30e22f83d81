{"openapi": "3.0.2", "info": {"title": "Medical Network Claims Responses And Reports v2", "description": "Claims Responses And Reports", "version": "v2"}, "servers": [{"url": "https://sandbox.apigw.changehealthcare.com/medicalnetwork/reports/v2", "description": "Optum"}], "tags": [{"name": "Claims Responses And Reports", "description": "Claims Responses And Reports"}], "paths": {"/": {"get": {"tags": ["Reports"], "summary": "List Reports", "operationId": "list_reports_v2_reports_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Reports"}}}}}}}, "/healthcheck": {"get": {"tags": ["Health Check"], "summary": "Health Check", "operationId": "health_check_v2_reports_healthcheck_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheck"}}}}}}}, "/{filename}": {"get": {"tags": ["Report"], "summary": "Get Single Report", "operationId": "get_single_report_v2_reports__filename__get", "parameters": [{"required": true, "schema": {"title": "Filename"}, "name": "filename", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportContent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Report"], "summary": "Delete Single Report", "operationId": "delete_single_report_v2_reports__filename__delete", "parameters": [{"required": true, "schema": {"title": "Filename"}, "name": "filename", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportDeleted"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{filename}/277": {"get": {"tags": ["Report"], "summary": "Convert Report 277", "operationId": "convert_report_277_v2_reports__filename__277_get", "parameters": [{"required": true, "schema": {"title": "Filename"}, "name": "filename", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimStatusTransactionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{filename}/835": {"get": {"tags": ["Report"], "summary": "Convert Report 835", "operationId": "convert_report_835_v2_reports__filename__835_get", "parameters": [{"required": true, "schema": {"title": "Filename"}, "name": "filename", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimPaymentAdviceTransactionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Address": {"title": "Address", "type": "object", "properties": {"address1": {"type": "string", "description": "N301"}, "address2": {"type": "string", "description": "N302"}, "city": {"type": "string", "description": "N401"}, "state": {"type": "string", "description": "N402"}, "postalCode": {"type": "string", "description": "N403"}, "countryCode": {"type": "string", "description": "N404"}, "countrySubCode": {"type": "string", "description": "N407"}}, "description": "Model for Address segment N3 and N4"}, "Adjustments": {"title": "Adjustments", "type": "object", "properties": {"adjustmentReasonCode": {"type": "string", "description": "PLB03-1; PLB05-1; PLB07-1; PLB09-1; PLB11-1; PLB13-1"}, "adjustmentReasonCodeValue": {"type": "string", "description": "PLB03-1; PLB05-1; PLB07-1; PLB09-1; PLB11-1; PLB13-1 in English"}, "providerAdjustmentIdentifier": {"type": "string", "description": "PLB03-2; PLB05-2; PLB07-2; PLB09-2; PLB11-2; PLB13-2"}, "providerAdjustmentAmount": {"type": "string", "description": "PLB04; PLB06; PLB08; PLB10; PLB12; PLB14"}}}, "ClaimAdjustments": {"title": "ClaimAdjustments", "type": "object", "properties": {"claimAdjustmentGroupCode": {"type": "string", "description": "CAS01"}, "claimAdjustmentGroupCodeValue": {"type": "string", "description": "CAS01 Code Value"}, "adjustmentReasonCode1": {"type": "string", "description": "CAS02"}, "adjustmentAmount1": {"type": "string", "description": "CAS03"}, "adjustmentQuantity1": {"type": "string", "description": "CAS04"}, "adjustmentReasonCode2": {"type": "string", "description": "CAS05"}, "adjustmentAmount2": {"type": "string", "description": "CAS06"}, "adjustmentQuantity2": {"type": "string", "description": "CAS07"}, "adjustmentReasonCode3": {"type": "string", "description": "CAS08"}, "adjustmentAmount3": {"type": "string", "description": "CAS09"}, "adjustmentQuantity3": {"type": "string", "description": "CAS10"}, "adjustmentReasonCode4": {"type": "string", "description": "CAS11"}, "adjustmentAmount4": {"type": "string", "description": "CAS12"}, "adjustmentQuantity4": {"type": "string", "description": "CAS13"}, "adjustmentReasonCode5": {"type": "string", "description": "CAS14"}, "adjustmentAmount5": {"type": "string", "description": "CAS15"}, "adjustmentQuantity5": {"type": "string", "description": "CAS16"}, "adjustmentReasonCode6": {"type": "string", "description": "CAS17"}, "adjustmentAmount6": {"type": "string", "description": "CAS18"}, "adjustmentQuantity6": {"type": "string", "description": "CAS19"}}}, "ClaimPaymentAdviceResponse": {"title": "ClaimPaymentAdviceResponse", "type": "object", "properties": {"controlNumber": {"type": "string", "description": "ST02"}, "paymentAndRemitReassociationDetails": {"allOf": [{"$ref": "#/components/schemas/PaymentAndRemitReassociationDetails"}], "description": "TRN"}, "foreignCurrency": {"type": "string", "description": "CUR02 where CUR01=PR"}, "productionDate": {"type": "string", "description": "DTM02 where DTM01=405"}, "receiverIdentifier": {"type": "string", "description": "REF02 where REF01=EV"}, "versionIdentification": {"type": "string", "description": "REF02 where REF01=F2"}, "financialInformation": {"allOf": [{"$ref": "#/components/schemas/FinancialInformation"}], "description": "BPR"}, "payer": {"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_payment_advice__Payer"}, "payee": {"$ref": "#/components/schemas/Payee"}, "detailInfo": {"type": "array", "items": {"$ref": "#/components/schemas/DetailInfo"}, "default": []}, "providerAdjustments": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderAdjustments"}, "description": "PLB; segment REPEAT >1", "default": []}}}, "ClaimPaymentAdviceTransactionResponse": {"title": "ClaimPaymentAdviceTransactionResponse", "required": ["meta"], "type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimPaymentAdviceResponse"}, "description": "transactions", "default": []}, "meta": {"$ref": "#/components/schemas/Meta"}}}, "ClaimPaymentInfo": {"title": "ClaimPaymentInfo", "type": "object", "properties": {"patientControlNumber": {"type": "string", "description": "LOOP 2100; CLP01"}, "claimStatusCode": {"type": "string", "description": "LOOP 2100; CLP02"}, "totalClaimChargeAmount": {"type": "string", "description": "LOOP 2100; CLP03"}, "claimPaymentAmount": {"type": "string", "description": "LOOP 2100; CLP04"}, "patientResponsibilityAmount": {"type": "string", "description": "LOOP 2100; CLP05"}, "claimFilingIndicatorCode": {"type": "string", "description": "LOOP 2100; CLP06"}, "payerClaimControlNumber": {"type": "string", "description": "LOOP 2100; CLP07"}, "facilityTypeCode": {"type": "string", "description": "LOOP 2100; CLP08"}, "claimFrequencyCode": {"type": "string", "description": "LOOP 2100; CLP09"}, "diagnosisRelatedGroupDRGCode": {"type": "string", "description": "LOOP 2100; CLP11"}, "diagnosisRelatedGroupDRGWeight": {"type": "string", "description": "LOOP 2100; CLP12"}, "dischargeFraction": {"type": "string", "description": "LOOP 2100; CLP13"}}}, "ClaimStatus": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"referencedTransactionTraceNumber": {"type": "string", "description": "Loop 2200[D|E] TRN02"}, "informationClaimStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/InformationClaimStatus"}, "description": "Loop 2200[D|E]", "default": []}, "tradingPartnerClaimNumber": {"type": "string", "description": "Loop 2200[D|E] REF02 where REF01=1K"}, "billTypeIdentifier": {"type": "string", "description": "Loop 2200[D|E] REF02 where REF01=BLT"}, "patientAccountNumber": {"type": "string", "description": "Loop 2200[D|E] REF02 where REF01=EJ"}, "pharmacyPrescriptionNumber": {"type": "string", "description": "Loop 2200[D|E] REF02 where REF01=XZ"}, "voucherIdentifier": {"type": "string", "description": "Loop 2200[D|E] REF02 where REF01=VV"}, "clearinghouseTraceNumber": {"type": "string", "description": "Loop 2200[D|E] REF02 where REF01=D9"}, "claimServiceBeginDate": {"type": "string", "description": "Loop 2200[D|E] DTP03 where DTP02=RD8"}, "claimServiceEndDate": {"type": "string", "description": "Loop 2200[D|E] DTP03 where DTP02=RD8"}, "claimServiceDate": {"type": "string", "description": "Loop 2200[D|E] DTP03 where DTP02=D8"}}}, "ClaimStatusDetails": {"title": "ClaimStatusDetails", "type": "object", "properties": {"serviceProvider": {"allOf": [{"$ref": "#/components/schemas/ServiceProvider"}], "description": "Loop 2100C NM1", "default": {}}, "providerOFServiceInformationTraceIdentifier": {"type": "string", "description": "Loop 2200C TRN02"}, "serviceProviderClaimStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceProviderClaimStatus"}, "description": "Loop 2200C STC", "default": []}, "patientClaimStatusDetails": {"type": "array", "items": {"$ref": "#/components/schemas/PatientClaimStatusDetails"}, "description": "Loop 2000D HL103=22", "default": []}}}, "ClaimStatusResponse": {"title": "ClaimStatusResponse", "type": "object", "properties": {"controlNumber": {"type": "string", "description": "ST02"}, "referenceIdentification": {"type": "string", "description": "BHT03"}, "transactionSetCreationDate": {"type": "string", "description": "BHT04"}, "transactionSetCreationTime": {"type": "string", "description": "BHT05"}, "payers": {"type": "array", "items": {"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_status_cdpedi__Payer"}, "description": "Loop 2100A", "default": []}}}, "ClaimStatusTransactionResponse": {"title": "ClaimStatusTransactionResponse", "required": ["meta"], "type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimStatusResponse"}, "description": "transactions", "default": []}, "meta": {"$ref": "#/components/schemas/Meta"}}}, "ClaimStatusTransactions": {"title": "ClaimStatusTransactions", "type": "object", "properties": {"provider": {"allOf": [{"$ref": "#/components/schemas/Provider"}], "description": "Loop 2100B NM1", "default": {}}, "claimTransactionBatchNumber": {"type": "string", "description": "Loop 2200B TRN02"}, "providerClaimStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderClaimStatus"}, "description": "Loop 2200B STC", "default": []}, "claimStatusDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimStatusDetails"}, "description": "Loop 2000C HL103=19", "default": []}}}, "ClaimSupplementalInformation": {"title": "ClaimSupplementalInformation", "type": "object", "properties": {"coverageAmount": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = AU"}, "discountAmount": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = DB"}, "perDayLimit": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = DY"}, "patientAmountPaid": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = F5"}, "interest": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = I"}, "negativeLedgerBalance": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = NL"}, "tax": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = T"}, "totalClaimBeforeTaxes": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = T2"}, "federalMedicareOrMedicaidPaymentMandateCategory1": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = ZK"}, "federalMedicareOrMedicaidPaymentMandateCategory2": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = ZL"}, "federalMedicareOrMedicaidPaymentMandateCategory3": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = ZM"}, "federalMedicareOrMedicaidPaymentMandateCategory4": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = ZN"}, "federalMedicareOrMedicaidPaymentMandateCategory5": {"type": "string", "description": "LOOP 2100; AMT02 where AMT01 = ZO"}}, "description": "AMT"}, "ClaimSupplementalInformationQuantities": {"title": "ClaimSupplementalInformationQuantities", "type": "object", "properties": {"coveredActual": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = CA"}, "coInsuredActual": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = CD"}, "lifeTimeReserveActual": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = LA"}, "lifeTimeReserveEstimated": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = LE"}, "nonCoveredEstimated": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = NE"}, "notReplacedBloodUnits": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = NR"}, "outlierDays": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = OU"}, "prescription": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = PS"}, "visits": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = VS"}, "federalMedicareOrMedicaidPaymentMandateCategory1": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = ZK"}, "federalMedicareOrMedicaidPaymentMandateCategory2": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = ZL"}, "federalMedicareOrMedicaidPaymentMandateCategory3": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = ZM"}, "federalMedicareOrMedicaidPaymentMandateCategory4": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = ZN"}, "federalMedicareOrMedicaidPaymentMandateCategory5": {"type": "string", "description": "LOOP 2100; QTY02 where QTY01 = ZO"}}, "description": "QTY"}, "Claims": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {"claimStatus": {"allOf": [{"$ref": "#/components/schemas/ClaimStatus"}], "description": "claimStatus for Loop 2200[D|E]", "default": {}}, "serviceLines": {"type": "array", "items": {"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_status_cdpedi__ServiceLines"}, "description": "serviceDetails for Loop loop 2220[D|E]", "default": []}}}, "Contact": {"title": "Contact", "type": "object", "properties": {"contactName": {"type": "string", "description": "PER02"}, "contactMethods": {"type": "array", "items": {"$ref": "#/components/schemas/ContactMethods"}, "description": "each contact will have a single property set, except for phone with extension", "default": []}}, "description": "Model for Contact Information PER"}, "ContactInformation": {"title": "ContactInformation", "type": "object", "properties": {"contactName": {"type": "string", "description": "PER02"}, "contactMethods": {"type": "array", "items": {"$ref": "#/components/schemas/ContactMethod"}, "description": "each contact will have a single property set, except for phone with extention"}}, "description": "PER - <PERSON>Y<PERSON> BUSINESS CONTACT INFORMATION when PER01=CX"}, "ContactMethod": {"title": "ContactMethod", "type": "object", "properties": {"email": {"type": "string", "description": "PER04-06 where PER03-05 = EM"}, "fax": {"type": "string", "description": "PER04-06 where PER03-05 = FX"}, "phone": {"type": "string", "description": "PER04-06 where PER03-05 = TE"}, "phoneExtension": {"type": "string", "description": "PER06-08 where PER05-07 = EX"}}}, "ContactMethods": {"title": "ContactMethods", "type": "object", "properties": {"electronicDataInterChangeAccessNumber": {"type": "string", "description": "PER04-06-08 where PER03-05-7=ED"}, "email": {"type": "string", "description": "PER04-06-08 where PER03-05-7=EM"}, "fax": {"type": "string", "description": "PER04-06-08 where PER03-05-7=FX"}, "phone": {"type": "string", "description": "PER04-06-08 where PER03-05-7=TE"}, "phoneExtension": {"type": "string", "description": "PER04-06-08 where PER03-05-7=EX"}}}, "CorrectedPatientOrInsuredName": {"title": "CorrectedPatientOrInsuredName", "type": "object", "properties": {"organizationName": {"type": "string", "description": "LOOP 2100; NM103 where NM102=2"}, "lastName": {"type": "string", "description": "LOOP 2100; NM103 where NM102=1"}, "firstName": {"type": "string", "description": "LOOP 2100; NM104"}, "middleName": {"type": "string", "description": "LOOP 2100; NM105"}, "suffix": {"type": "string", "description": "LOOP 2100; NM107"}, "insuredsChangedUniqueIdentificationNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=C"}}}, "Dependent": {"title": "Dependent", "type": "object", "properties": {"lastName": {"type": "string", "description": "NM103 where NM102=1"}, "firstName": {"type": "string", "description": "NM104"}, "middleName": {"type": "string", "description": "NM105"}, "suffix": {"type": "string", "description": "NM107"}}}, "DetailInfo": {"title": "DetailInfo", "type": "object", "properties": {"assignedNumber": {"type": "string", "description": "LOOP 2000: LX01"}, "providerSummaryInformation": {"$ref": "#/components/schemas/ProviderSummaryInformation"}, "providerSupplementalSummaryInformation": {"$ref": "#/components/schemas/ProviderSupplementalSummaryInformation"}, "paymentInfo": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentInfo"}, "default": []}}, "description": "Detailed info for LOOP 2000 HEADER NUMBER"}, "ExtendedProviderStatus": {"title": "ExtendedProviderStatus", "type": "object", "properties": {"healthCareClaimStatusCategoryCode": {"type": "string", "description": "STC01-1, STC010-1, STC11-1"}, "healthCareClaimStatusCategoryCodeValue": {"type": "string", "description": "STC01-1, STC010-1, STC11-1 in English"}, "statusCode": {"type": "string", "description": "STC01-2, STC010-2, STC11-2"}, "statusCodeValue": {"type": "string", "description": "STC01-2, STC010-2, STC11-2 in English"}, "entityIdentifierCode": {"type": "string", "description": "STC01-3, STC010-3, STC11-3"}, "entityIdentifierCodeValue": {"type": "string", "description": "STC01-3, STC010-3, STC11-3 in English"}, "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": {"type": "string", "description": "STC01-4, STC010-4, STC11-4 in English"}}}, "FinancialInformation": {"title": "FinancialInformation", "type": "object", "properties": {"transactionHandlingCode": {"type": "string", "description": "BPR01"}, "totalActualProviderPaymentAmount": {"type": "string", "description": "BPR02"}, "creditOrDebitFlagCode": {"type": "string", "description": "BPR03"}, "paymentMethodCode": {"type": "string", "description": "BPR04"}, "paymentFormatCode": {"type": "string", "description": "BPR05"}, "payerIdentifier": {"type": "string", "description": "BPR10"}, "originatingCompanySupplementalCode": {"type": "string", "description": "BPR11"}, "checkIssueOrEFTEffectiveDate": {"type": "string", "description": "BPR16"}, "senderAccountDetails": {"$ref": "#/components/schemas/SenderAccountDetails"}, "receiverAccountDetails": {"$ref": "#/components/schemas/ReceiverAccountDetails"}}, "description": "Model for financial information in claim payment advice transactions BPR"}, "HTTPValidationError": {"title": "HTTPValidationError", "type": "object", "properties": {"detail": {"title": "Detail", "type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}}}, "HealthCareCheckRemarkCodes": {"title": "HealthCareCheckRemarkCodes", "type": "object", "properties": {"codeListQualifierCode": {"type": "string", "description": "LOOP 2110; LQ01"}, "codeListQualifierCodeValue": {"type": "string", "description": "LOOP 2110; LQ01"}, "remarkCode": {"type": "string", "description": "LOOP 2110; LQ02"}}, "description": "LQ - HEALTH CARE REMARK CODES"}, "HealthCarePolicyIdentification": {"title": "HealthCarePolicyIdentification", "type": "object", "properties": {"policyFormIdentifyingNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01=0k"}}}, "HealthCheck": {"title": "HealthCheck", "required": ["version"], "type": "object", "properties": {"version": {"type": "string"}, "status": {"type": "string", "description": "NM101 60 char, required"}}}, "IdentificationEntity": {"title": "IdentificationEntity", "type": "object", "properties": {"organizationName": {"type": "string", "description": "LOOP 2100; NM103 where NM102=2"}, "blueCrossBlueShieldAssociationPlanCode": {"type": "string", "description": "LOOP 2100; NM109 WHERE NM108=AD"}, "taxId": {"type": "string", "description": "LOOP 2100; NM109 WHERE NM108=FI"}, "nationalAssociationOfInsuranceCommissionersIdentification": {"type": "string", "description": "LOOP 2100; NM109 WHERE NM108=NI"}, "payorId": {"type": "string", "description": "LOOP 2100; NM109 WHERE NM108=PI"}, "pharmacyProcessorNumber": {"type": "string", "description": "LOOP 2100; NM109 WHERE NM108=PP"}, "centersForMedicareAndMedicaidServicesPlanId": {"type": "string", "description": "LOOP 2100; NM109 WHERE NM108=XV"}}}, "InformationClaimStatus": {"title": "InformationClaimStatus", "type": "object", "properties": {"statusInformationEffectiveDate": {"type": "string", "description": "Loop 2200[D|E] STC02"}, "totalClaimChargeAmount": {"type": "string", "description": "Loop 2200[D|E] STC04"}, "claimPaymentAmount": {"type": "string", "description": "Loop 2200[D|E] STC05"}, "adjudicatedFinalizedDate": {"type": "string", "description": "Loop 2200[D|E] STC06"}, "remittanceDate": {"type": "string", "description": "Loop 2200[D|E] STC08"}, "remittanceTraceNumber": {"type": "string", "description": "Loop 2200[D|E] STC09"}, "informationStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedProviderStatus"}, "description": "Loop 2200[D|E] STC01 ,STC10, STC11", "default": []}}}, "InpatientAdjudication": {"title": "InpatientAdjudication", "type": "object", "properties": {"coveredDaysOrVisitsCount": {"type": "string", "description": "LOOP 2100; MIA01"}, "ppsOperatingOutlierAmount": {"type": "string", "description": "LOOP 2100; MIA02"}, "lifetimePsychiatricDaysCount": {"type": "string", "description": "LOOP 2100; MIA03"}, "claimDRGAmount": {"type": "string", "description": "LOOP 2100; MIA04"}, "claimPaymentRemarkCode1": {"type": "string", "description": "LOOP 2100; MIA05"}, "claimDisproportionateShareAmount": {"type": "string", "description": "LOOP 2100; MIA06"}, "claimMSPPassThroughAmount": {"type": "string", "description": "LOOP 2100; MIA07"}, "claimPPSCapitalAmount": {"type": "string", "description": "LOOP 2100; MIA08"}, "ppsCapitalFSPDRGAmount": {"type": "string", "description": "LOOP 2100; MIA09"}, "ppsCapitalHSPDRGAmount": {"type": "string", "description": "LOOP 2100; MIA10"}, "ppsCapitalDSHDRGAmount": {"type": "string", "description": "LOOP 2100; MIA11"}, "oldCapitalAmount": {"type": "string", "description": "LOOP 2100; MIA12"}, "ppsCapitalIMEAmount": {"type": "string", "description": "LOOP 2100; MIA13"}, "ppsOperatingHospitalSpecificDRGAmount": {"type": "string", "description": "LOOP 2100; MIA14"}, "costReportDayCount": {"type": "string", "description": "LOOP 2100; MIA15"}, "ppsOperatingFederalSpecificDRGAmount": {"type": "string", "description": "LOOP 2100; MIA16"}, "claimPPSCapitalOutlierAmount": {"type": "string", "description": "LOOP 2100; MIA17"}, "claimIndirectTeachingAmount": {"type": "string", "description": "LOOP 2100; MIA18"}, "nonPayableProfessionalComponentAmount": {"type": "string", "description": "LOOP 2100; MIA19"}, "claimPaymentRemarkCode2": {"type": "string", "description": "LOOP 2100; MIA20"}, "claimPaymentRemarkCode3": {"type": "string", "description": "LOOP 2100; MIA21"}, "claimPaymentRemarkCode4": {"type": "string", "description": "LOOP 2100; MIA22"}, "claimPaymentRemarkCode5": {"type": "string", "description": "LOOP 2100; MIA23"}, "ppsCapitalExceptionAmount": {"type": "string", "description": "LOOP 2100; MIA24"}}}, "Loop": {"title": "Loop", "type": "object", "properties": {"name": {"type": "string"}, "index": {"type": "integer"}}}, "Meta": {"title": "Meta", "type": "object", "properties": {"applicationMode": {"type": "string"}, "senderId": {"type": "string"}, "traceId": {"type": "string"}}}, "OtherClaimRelatedIdentification": {"title": "OtherClaimRelatedIdentification", "type": "object", "properties": {"groupOrPolicyNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1L"}, "memberIdentificationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1W"}, "employeeIdentificationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 28"}, "groupNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 6P"}, "rePricedClaimReferenceNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 9A"}, "adjustedRePricedClaimReferenceNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 9C"}, "authorizationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = BB"}, "classOfContractCode": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = CE"}, "medicalRecordIdentificationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = EA"}, "originalReferenceNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = F8"}, "priorAuthorizationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = G1"}, "predeterminationOfBenefitsIdentificationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = G3"}, "insurancePolicyNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = IG"}, "ssn": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = SY"}}}, "OutpatientAdjudication": {"title": "OutpatientAdjudication", "type": "object", "properties": {"reimbursementRate": {"type": "string", "description": "LOOP 2100; MOA01"}, "claimHCPCSPayableAmount": {"type": "string", "description": "LOOP 2100; MOA02"}, "claimPaymentRemarkCode1": {"type": "string", "description": "LOOP 2100; MOA03"}, "claimPaymentRemarkCode2": {"type": "string", "description": "LOOP 2100; MOA04"}, "claimPaymentRemarkCode3": {"type": "string", "description": "LOOP 2100; MOA05"}, "claimPaymentRemarkCode4": {"type": "string", "description": "LOOP 2100; MOA06"}, "claimPaymentRemarkCode5": {"type": "string", "description": "LOOP 2100; MOA07"}, "claimESRDPaymentAmount": {"type": "string", "description": "LOOP 2100; MOA08"}, "nonPayableProfessionalComponentAmount": {"type": "string", "description": "LOOP 2100; MOA09"}}}, "PatientClaimStatusDetails": {"title": "PatientClaimStatusDetails", "type": "object", "properties": {"subscriber": {"allOf": [{"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_status_cdpedi__Subscriber"}], "description": "Loop 2100D NM1", "default": {}}, "dependent": {"allOf": [{"$ref": "#/components/schemas/Dependent"}], "description": "Loop 2100E NM1", "default": {}}, "claims": {"type": "array", "items": {"$ref": "#/components/schemas/Claims"}, "description": "Loop 2200[D|E]", "default": []}}}, "PatientName": {"title": "PatientName", "type": "object", "properties": {"lastName": {"type": "string", "description": "LOOP 2100; NM103"}, "firstName": {"type": "string", "description": "LOOP 2100; NM104"}, "middleName": {"type": "string", "description": "LOOP 2100; NM105"}, "suffix": {"type": "string", "description": "LOOP 2100; NM107"}, "ssn": {"type": "string", "description": "LOOP 2100 NM109 where NM108=34"}, "healthInsuranceClaimNumber": {"type": "string", "description": "LOOP 2100 NM109 where NM108=HN"}, "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": {"type": "string", "description": "LOOP 2100 NM109 where NM108=II"}, "memberId": {"type": "string", "description": "LOOP 2100 NM109 where NM108=MI"}, "medicaidRecipientIdentificationNumber": {"type": "string", "description": "LOOP 2100 NM109 where NM108=MR"}}}, "Payee": {"title": "Payee", "type": "object", "properties": {"name": {"type": "string", "description": "N102"}, "taxId": {"type": "string", "description": "N104 where N103 where N103=FI"}, "centersForMedicareAndMedicaidServicesPlanId": {"type": "string", "description": "N104 where N103 where N103=XV"}, "npi": {"type": "string", "description": "N104 where N103 where N103=XX"}, "address": {"allOf": [{"$ref": "#/components/schemas/Address"}], "description": "LOOP 1000B PAYEE Address N3 and N4", "default": {}}, "stateLicenseNumber": {"type": "string", "description": "LOOP 1000B REF02 where REF01=0B"}, "nationalCouncilForPrescriptionDrugProgramsPharmacyNumber": {"type": "string", "description": "LOOP 1000B REF02 where REF01=D3"}, "payeeIdentification": {"type": "string", "description": "LOOP 1000B REF02 where REF01=PQ"}, "federalTaxPayersIdentificationNumber": {"type": "string", "description": "LOOP 1000B REF02 where REF01=TJ"}, "remittanceDeliveryMethod": {"allOf": [{"$ref": "#/components/schemas/RemittanceDeliveryMethod"}], "description": "RDM - REMIT<PERSON><PERSON><PERSON> DELIVERY METHOD"}}, "description": "Model for the Payee Identification for LOOP 1000B"}, "PaymentAndRemitReassociationDetails": {"title": "PaymentAndRemitReassociationDetails", "type": "object", "properties": {"traceTypeCode": {"type": "string", "description": "TRN01"}, "checkOrEFTTraceNumber": {"type": "string", "description": "TRN02"}, "originatingCompanyIdentifier": {"type": "string", "description": "TRN03"}, "originatingCompanySupplementalCode": {"type": "string", "description": "TRN04"}}, "description": "Model for Payment And Remit Reassociation Details in claim payment advice transactions TRN"}, "PaymentInfo": {"title": "PaymentInfo", "type": "object", "properties": {"claimStatementPeriodStart": {"type": "string", "description": "LOOP 2100; DTM02 where DTM01=232"}, "claimStatementPeriodEnd": {"type": "string", "description": "LOOP 2100; DTM02 where DTM01=233"}, "coverageExpirationDate": {"type": "string", "description": "LOOP 2100; DTM02 where DTM01=036"}, "claimReceivedDate": {"type": "string", "description": "LOOP 2100; DTM02 where DTM01=050"}, "claimPaymentInfo": {"allOf": [{"$ref": "#/components/schemas/ClaimPaymentInfo"}], "description": "LOOP 2100; CLP - <PERSON><PERSON><PERSON> PAYMENT INFORMATION"}, "claimAdjustments": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimAdjustments"}, "description": "LOOP 2100; CAS - CLAIM ADJUSTMENT, Segment repeat = 99", "default": []}, "patientName": {"allOf": [{"$ref": "#/components/schemas/PatientName"}], "description": "LOOP 2100; NM1 - PATIENT NAME"}, "subscriber": {"allOf": [{"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_payment_advice__Subscriber"}], "description": "LOOP 2100; NM1 - INSURED NAME"}, "correctedPatientOrInsuredName": {"allOf": [{"$ref": "#/components/schemas/CorrectedPatientOrInsuredName"}], "description": "LOOP 2100; NM1 - CORRECTED PATIENT/INSURED NAME"}, "renderingProvider": {"allOf": [{"$ref": "#/components/schemas/RenderingProvider"}], "description": "LOOP 2100; NM1 - SERVICE PROVIDER NAME NM101 = 82"}, "crossoverCarrier": {"allOf": [{"$ref": "#/components/schemas/IdentificationEntity"}], "description": "LOOP 2100; NM1 - CROSSOVER CARRIER NAME NM101=TT"}, "correctedPriorityPayer": {"allOf": [{"$ref": "#/components/schemas/IdentificationEntity"}], "description": "LOOP 2100; NM1 - CORRECTED PRIORITY PAYER NAME NM101=PR"}, "otherSubscriber": {"allOf": [{"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_payment_advice__Subscriber"}], "description": "LOOP 2100; NM1 - <PERSON>THER SUBSCRIBER NAME NM101=GB"}, "inpatientAdjudication": {"allOf": [{"$ref": "#/components/schemas/InpatientAdjudication"}], "description": "LOOP 2100; MIA - INPATIENT ADJUDICATION INFORMATION"}, "outpatientAdjudication": {"allOf": [{"$ref": "#/components/schemas/OutpatientAdjudication"}], "description": "LOOP 2100; MOA - OUTPATIENT ADJUDICATION INFORMATION"}, "claimContactInformation": {"type": "array", "items": {"$ref": "#/components/schemas/ContactInformation"}, "description": "LOOP 2100; PER - <PERSON><PERSON><PERSON> CONTACT INFORMATION", "default": []}, "otherClaimRelatedIdentification": {"allOf": [{"$ref": "#/components/schemas/OtherClaimRelatedIdentification"}], "description": "LOOP 2100; REF - <PERSON><PERSON><PERSON> CLAIM RELATED IDENTIFICATION"}, "renderingProviderIdentification": {"allOf": [{"$ref": "#/components/schemas/RenderingProviderIdentification"}], "description": "LOOP 2100; REF - RENDERING PROVIDER IDENTIFICATION"}, "claimSupplementalInformation": {"allOf": [{"$ref": "#/components/schemas/ClaimSupplementalInformation"}], "description": "LOOP 2100; AMT - CLAIM SUPPLEMENTAL INFORMATION"}, "claimSupplementalInformationQuantities": {"allOf": [{"$ref": "#/components/schemas/ClaimSupplementalInformationQuantities"}], "description": "LOOP 2100; QTY - CLAIM SUPPLEMENTAL INFORMATION QUANTITY"}, "serviceLines": {"type": "array", "items": {"$ref": "#/components/schemas/cdpedi__x12__v5010__newmodels__claim_payment_advice__ServiceLines"}, "default": []}}}, "Provider": {"title": "Provider", "type": "object", "properties": {"organizationName": {"type": "string", "description": "NM103 where NM102=2"}, "lastName": {"type": "string", "description": "NM103 where NM102=1"}, "firstName": {"type": "string", "description": "NM104"}, "middleName": {"type": "string", "description": "NM105"}, "etin": {"type": "string", "description": "NM109"}}}, "ProviderAdjustments": {"title": "ProviderAdjustments", "type": "object", "properties": {"providerIdentifier": {"type": "string", "description": "PLB01"}, "fiscalPeriodDate": {"type": "string", "description": "PLB02"}, "adjustments": {"type": "array", "items": {"$ref": "#/components/schemas/Adjustments"}, "description": "PLB03-PLB14; array of size = 6", "default": []}}}, "ProviderClaimStatus": {"title": "ProviderClaimStatus", "type": "object", "properties": {"statusInformationEffectiveDate": {"type": "string", "description": "Loop 2200C STC02"}, "providerStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderStatus"}, "description": "Loop 2200C STC01 ,STC10, STC11", "default": []}}}, "ProviderStatus": {"title": "ProviderStatus", "type": "object", "properties": {"healthCareClaimStatusCategoryCode": {"type": "string", "description": "STC01-1, STC010-1, STC11-1"}, "healthCareClaimStatusCategoryCodeValue": {"type": "string", "description": "STC01-1, STC010-1, STC11-1 in English"}, "statusCode": {"type": "string", "description": "STC01-2, STC010-2, STC11-2"}, "statusCodeValue": {"type": "string", "description": "STC01-2, STC010-2, STC11-2 in English"}, "entityIdentifierCode": {"type": "string", "description": "STC01-3, STC010-3, STC11-3"}, "entityIdentifierCodeValue": {"type": "string", "description": "STC01-3, STC010-3, STC11-3 in English"}}}, "ProviderSummaryInformation": {"title": "ProviderSummaryInformation", "type": "object", "properties": {"providerIdentifier": {"type": "string", "description": "LOOP 2000; TS301"}, "facilityTypeCode": {"type": "string", "description": "LOOP 2000; TS302"}, "fiscalPeriodDate": {"type": "string", "description": "LOOP 2000; TS303"}, "totalClaimCount": {"type": "string", "description": "LOOP 2000; TS304"}, "totalClaimChargeAmount": {"type": "string", "description": "LOOP 2000; TS305"}, "totalMSPPayerAmount": {"type": "string", "description": "LOOP 2000; TS313"}, "totalNonLabChargeAmount": {"type": "string", "description": "LOOP 2000; TS315"}, "totalHCPCSReportedChargeAmount": {"type": "string", "description": "LOOP 2000; TS317"}, "totalHCPCSPayableAmount": {"type": "string", "description": "LOOP 2000; TS318"}, "totalProfessionalComponentAmount": {"type": "string", "description": "LOOP 2000; TS320"}, "totalMSPPatientLiabilityMetAmount": {"type": "string", "description": "LOOP 2000; TS321"}, "totalPatientReimbursementAmount": {"type": "string", "description": "LOOP 2000; TS322"}, "totalPIPClaimCount": {"type": "string", "description": "LOOP 2000; TS323"}, "totalPIPAdjustmentAmount": {"type": "string", "description": "LOOP 2000; TS324"}}, "description": "PROVIDER SUMMARY INFORMATION for LOOP 2000 TS3"}, "ProviderSupplementalSummaryInformation": {"title": "ProviderSupplementalSummaryInformation", "type": "object", "properties": {"totalDRGAmount": {"type": "string", "description": "LOOP 2000; TS201"}, "totalFederalSpecificAmount": {"type": "string", "description": "LOOP 2000; TS202"}, "totalHospitalSpecificAmount": {"type": "string", "description": "LOOP 2000; TS203"}, "totalDisproportionateShareAmount": {"type": "string", "description": "LOOP 2000; TS204"}, "totalCapitalAmount": {"type": "string", "description": "LOOP 2000; TS205"}, "totalIndirectMedicalEducationAmount": {"type": "string", "description": "LOOP 2000; TS206"}, "totalOutlierDayCount": {"type": "string", "description": "LOOP 2000; TS207"}, "totalDayOutlierAmount": {"type": "string", "description": "LOOP 2000; TS208"}, "totalCostOutlierAmount": {"type": "string", "description": "LOOP 2000; TS209"}, "averageDRGLengthOfStay": {"type": "string", "description": "LOOP 2000; TS210"}, "totalDischargeCount": {"type": "string", "description": "LOOP 2000; TS211"}, "totalCostReportDayCount": {"type": "string", "description": "LOOP 2000; TS212"}, "totalCoveredDayCount": {"type": "string", "description": "LOOP 2000; TS213"}, "totalNonCoveredDayCount": {"type": "string", "description": "LOOP 2000; TS214"}, "totalMSPPassThroughAmount": {"type": "string", "description": "LOOP 2000; TS215"}, "averageDRGWeight": {"type": "string", "description": "LOOP 2000; TS216"}, "totalPPSCapitalFSPDRGAmount": {"type": "string", "description": "LOOP 2000; TS217"}, "totalPPSCapitalHSPDRGAmount": {"type": "string", "description": "LOOP 2000; TS218"}, "totalPPSDSHDRGAmount": {"type": "string", "description": "LOOP 2000; TS219"}}, "description": "TS2 - PROVIDER SUPPLEMENTAL SUMMARY INFORMATION"}, "ReceiverAccountDetails": {"title": "ReceiverAccountDetails", "type": "object", "properties": {"receiverDfiIdNumberQualifier": {"type": "string", "description": "BPR12"}, "receiverDfiIdentificationNumber": {"type": "string", "description": "BPR13"}, "receiverAccountNumberQualifier": {"type": "string", "description": "BPR14"}, "receiverAccountNumber": {"type": "string", "description": "BPR15"}}, "description": "receiver account details related to a Financial Information"}, "RemittanceDeliveryMethod": {"title": "RemittanceDeliveryMethod", "type": "object", "properties": {"name": {"type": "string", "description": "RDM02"}, "email": {"type": "string", "description": "RDM03 where RDM01=EM"}, "ftp": {"type": "string", "description": "RDM03 where RDM01=FT"}, "onLine": {"type": "string", "description": "RDM03 where RDM01=OL"}}}, "RenderingProvider": {"title": "RenderingProvider", "type": "object", "properties": {"organizationName": {"type": "string", "description": "LOOP 2100; NM103 NM102=2"}, "lastName": {"type": "string", "description": "LOOP 2100; NM103 NM102=1"}, "firstName": {"type": "string", "description": "LOOP 2100; NM104"}, "middleName": {"type": "string", "description": "LOOP 2100; NM105"}, "suffix": {"type": "string", "description": "LOOP 2100; NM107"}, "blueCrossProviderNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=BD"}, "blueShieldProviderNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=BS"}, "taxId": {"type": "string", "description": "LOOP 2100; NM109 where NM108=FI"}, "medicaidProviderNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=MC"}, "providerCommercialNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=PC"}, "stateLicenseNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=SL"}, "uniquePhysicianIdentificationNumber": {"type": "string", "description": "LOOP 2100; NM109 where NM108=UP"}, "npi": {"type": "string", "description": "LOOP 2100; NM109 where NM108=XX"}}}, "RenderingProviderIdentification": {"title": "RenderingProviderIdentification", "type": "object", "properties": {"stateLicenseNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 0B"}, "blueCrossProviderNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1A"}, "blueShieldProviderNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1B"}, "medicareProviderNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1C"}, "medicaidProviderNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1D"}, "providerUPINNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1G"}, "champusIdentificationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1H"}, "facilityIdNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = 1J"}, "nationalCouncilForPrescriptionDrugProgramPharmacyNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = D3"}, "providerCommercialNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = G2"}, "locationNumber": {"type": "string", "description": "LOOP 2100; REF02 where REF01 = LU"}}}, "RenderingProviderInformation": {"title": "RenderingProviderInformation", "type": "object", "properties": {"stateLicenseNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 0B"}, "blueCrossProviderNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1A"}, "blueShieldProviderNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1B"}, "medicareProviderNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1C"}, "medicaidProviderNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1D"}, "providerUPINNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1G"}, "champusIdentificationNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1H"}, "facilityIdNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1J"}, "nationalCouncilForPrescriptionDrugProgramPharmacyNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = D3"}, "providerCommercialNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = G2"}, "npi": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = HPI"}, "ssn": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = SY"}, "federalTaxpayerIdentificationNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = TJ"}}}, "ReportContent": {"title": "ReportContent", "required": ["report_content", "meta"], "type": "object", "properties": {"report_content": {"title": "Report Content", "type": "string"}, "meta": {"$ref": "#/components/schemas/Meta"}}}, "ReportDeleted": {"title": "ReportDeleted", "required": ["status", "meta"], "type": "object", "properties": {"status": {"title": "Status", "type": "string"}, "meta": {"$ref": "#/components/schemas/Meta"}}}, "Reports": {"title": "Reports", "required": ["meta"], "type": "object", "properties": {"reports": {"title": "Reports", "type": "array", "items": {"type": "string"}, "default": []}, "meta": {"$ref": "#/components/schemas/Meta"}}}, "Segment": {"title": "Segment", "type": "object", "properties": {"name": {"type": "string"}, "index": {"type": "integer"}}}, "SenderAccountDetails": {"title": "SenderAccountDetails", "type": "object", "properties": {"senderDfiIdNumberQualifier": {"type": "string", "description": "BPR06"}, "senderDFIIdentifier": {"type": "string", "description": "BPR07"}, "senderAccountNumberQualifier": {"type": "string", "description": "BPR08"}, "senderAccountNumber": {"type": "string", "description": "BPR09"}}, "description": "sender account details related to a Financial Information"}, "Service": {"title": "Service", "type": "object", "properties": {"serviceIdQualifierCode": {"type": "string", "description": "Loop 2220[D|E] SVC01-01"}, "serviceIdQualifierCodeValue": {"type": "string", "description": "Loop 2220[D|E] SVC01-01 in English"}, "procedureCode": {"type": "string", "description": "Loop 2220[D|E] SVC01-02"}, "procedureModifiers": {"type": "array", "items": {"type": "string"}, "description": "Loop 2220[D|E] SVC01-03, SVC01-04, SVC01-05, SVC01-06", "default": []}, "chargeAmount": {"type": "string", "description": "Loop 2220[D|E] SVC02"}, "amountPaid": {"type": "string", "description": "Loop 2220[D|E] SVC03"}, "revenueCode": {"type": "string", "description": "Loop 2220[D|E] SVC04"}, "submittedUnits": {"type": "string", "description": "Loop 2220[D|E] SVC07"}}}, "ServiceClaimStatus": {"title": "ServiceClaimStatus", "type": "object", "properties": {"effectiveDate": {"type": "string", "description": "Loop 2220[D|E] STC02"}, "serviceStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedProviderStatus"}, "description": "Loop 2220[D|E] STC01 ,STC10, STC11", "default": []}}}, "ServiceIdentification": {"title": "ServiceIdentification", "type": "object", "properties": {"ambulatoryPatientGroupNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = 1S"}, "ambulatoryPaymentClassification": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = APC"}, "authorizationNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = BB"}, "attachmentCode": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = E9"}, "priorAuthorizationNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = G1"}, "preDeterminationOfBenefitsIdentificationNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = G3"}, "locationNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = LU"}, "rateCodeNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01 = RB"}}}, "ServicePaymentInformation": {"title": "ServicePaymentInformation", "type": "object", "properties": {"productOrServiceIDQualifier": {"type": "string", "description": "LOOP 2110; SVC01 - 1"}, "productOrServiceIDQualifierValue": {"type": "string", "description": "LOOP 2110; SVC01 - 1"}, "adjudicatedProcedureCode": {"type": "string", "description": "LOOP 2110; SVC01 - 2"}, "adjudicatedProcedureModifierCodes": {"type": "array", "items": {"type": "string"}, "description": "ARRAY SIZE = 4| SVC01 - 3; SVC01 - 4; SVC01 - 5; SVC01 - 6", "default": []}, "lineItemChargeAmount": {"type": "string", "description": "LOOP 2110; SVC02"}, "lineItemProviderPaymentAmount": {"type": "string", "description": "LOOP 2110; SVC03"}, "nationalUniformBillingCommitteeRevenueCode": {"type": "string", "description": "LOOP 2110; SVC04"}, "unitsOfServicePaidCount": {"type": "string", "description": "LOOP 2110; SVC05"}, "submittedProductOrServiceIDQualifier": {"type": "string", "description": "LOOP 2110; SVC06 -1"}, "submittedProductOrServiceIDQualifierValue": {"type": "string", "description": "LOOP 2110; SVC06 -1 in English"}, "submittedAdjudicatedProcedureCode": {"type": "string", "description": "LOOP 2110; SVC06 -2"}, "submittedAdjudicatedProcedureModifierCodes": {"type": "array", "items": {"type": "string"}, "description": "ARRAY SIZE = 4| SVC06 - 3; SVC06 - 4; SVC06 - 5; SVC06 - 6", "default": []}, "submittedProcedureCodeDescription": {"type": "string", "description": "LOOP 2110; SVC06 - 7"}, "originalUnitsOfServiceCount": {"type": "string", "description": "LOOP 2110; SVC07"}}}, "ServiceProvider": {"title": "ServiceProvider", "type": "object", "properties": {"organizationName": {"type": "string", "description": "NM103 where NM102=2"}, "lastName": {"type": "string", "description": "NM103 where NM102=1"}, "firstName": {"type": "string", "description": "NM104"}, "middleName": {"type": "string", "description": "NM105"}, "suffix": {"type": "string", "description": "NM107"}, "tin": {"type": "string", "description": "NM109 where NM108=FI"}, "spn": {"type": "string", "description": "NM109 where NM108=SV"}, "npi": {"type": "string", "description": "NM109 where NM108=XX"}}}, "ServiceProviderClaimStatus": {"title": "ServiceProviderClaimStatus", "type": "object", "properties": {"statusInformationEffectiveDate": {"type": "string", "description": "Loop 2200C STC02"}, "serviceProviderStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderStatus"}, "description": "Loop 2200C STC01 ,STC10, STC11", "default": []}}}, "ServiceSupplementalAmounts": {"title": "ServiceSupplementalAmounts", "type": "object", "properties": {"allowedActual": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = B6"}, "deductionAmount": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = KH"}, "tax": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = T"}, "totalClaimBeforeTaxes": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = T2"}, "federalMedicareOrMedicaidPaymentMandateCategory1": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = ZK"}, "federalMedicareOrMedicaidPaymentMandateCategory2": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = ZL"}, "federalMedicareOrMedicaidPaymentMandateCategory3": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = ZM"}, "federalMedicareOrMedicaidPaymentMandateCategory4": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = ZN"}, "federalMedicareOrMedicaidPaymentMandateCategory5": {"type": "string", "description": "LOOP 2110; AMT02 where AMT01 = ZO"}}}, "ServiceSupplementalQuantities": {"title": "ServiceSupplementalQuantities", "type": "object", "properties": {"federalMedicareOrMedicaidPaymentMandateCategory1": {"type": "string", "description": "LOOP 2110; QTY02 where QTY01 = ZK"}, "federalMedicareOrMedicaidPaymentMandateCategory2": {"type": "string", "description": "LOOP 2110; QTY02 where QTY01 = ZL"}, "federalMedicareOrMedicaidPaymentMandateCategory3": {"type": "string", "description": "LOOP 2110; QTY02 where QTY01 = ZM"}, "federalMedicareOrMedicaidPaymentMandateCategory4": {"type": "string", "description": "LOOP 2110; QTY02 where QTY01 = ZN"}, "federalMedicareOrMedicaidPaymentMandateCategory5": {"type": "string", "description": "LOOP 2110; QTY02 where QTY01 = ZO"}}, "description": "QTY SERVICE"}, "TechnicalContactInformation": {"title": "TechnicalContactInformation", "type": "object", "properties": {"contactName": {"type": "string", "description": "LOOP 1000A; PER02"}, "contactMethods": {"type": "array", "items": {"$ref": "#/components/schemas/TechnicalContactMethod"}, "description": "each contact will have a single property set, except for phone with extention"}}, "description": "PER - <PERSON>YER TECH<PERSON>CAL CONTACT INFORMATION when PER01=BL"}, "TechnicalContactMethod": {"title": "TechnicalContactMethod", "type": "object", "properties": {"url": {"type": "string", "description": "PER04-06-08 where PER03-05-07 = UR"}, "email": {"type": "string", "description": "PER04-06-08 where PER03-05 = EM"}, "fax": {"type": "string", "description": "PER06-08 where PER05-07 = FX"}, "phone": {"type": "string", "description": "PER04-06 where PER03-05 = TE"}, "phoneExtension": {"type": "string", "description": "PER06-08 where PER05-07 = EX"}}}, "ValidationError": {"title": "ValidationError", "required": ["loc", "msg", "type"], "type": "object", "properties": {"loc": {"title": "Location", "type": "array", "items": {"type": "string"}}, "msg": {"title": "Message", "type": "string"}, "type": {"title": "Error Type", "type": "string"}}}, "cdpedi__x12__v5010__newmodels__claim_payment_advice__Payer": {"title": "Payer", "type": "object", "properties": {"name": {"type": "string", "description": "N102"}, "centersForMedicareAndMedicaidServicesPlanId": {"type": "string", "description": "LOOP 1000A N104 and LOOP 1000A N103=XV"}, "payerIdentificationNumber": {"type": "string", "description": "REF02 where REF01=2U"}, "submitterIdentificationNumber": {"type": "string", "description": "REF02 where REF01=EO"}, "healthIndustryNumber": {"type": "string", "description": "REF02 where REF01=HI"}, "nationalAssociationOfInsuranceCommissioners": {"type": "string", "description": "REF02 where REF01=NF"}, "payerWebSiteUrl": {"type": "string", "description": "PER04 where PER01=IC and PER03=UR"}, "address": {"allOf": [{"$ref": "#/components/schemas/Address"}], "description": "LOOP 1000A PAYER Address N3 and N4", "default": {}}, "businessContactInformation": {"allOf": [{"$ref": "#/components/schemas/ContactInformation"}], "description": "LOOP 1000A; PER where PER01=CX"}, "technicalContactInformation": {"type": "array", "items": {"$ref": "#/components/schemas/TechnicalContactInformation"}, "description": "LOOP 1000A; PER where PER01=BL", "default": []}}, "description": "Model for the Payer Identification for LOOP 1000A"}, "cdpedi__x12__v5010__newmodels__claim_payment_advice__ServiceLines": {"title": "ServiceLines", "type": "object", "properties": {"serviceDate": {"type": "string", "description": "LOOP 2110; DTM02 where DTM01=472"}, "serviceStartDate": {"type": "string", "description": "LOOP 2110; DTM02 where DTM01=150"}, "serviceEndDate": {"type": "string", "description": "LOOP 2110; DTM02 where DTM01=151"}, "lineItemControlNumber": {"type": "string", "description": "LOOP 2110; REF02 where REF01=6R"}, "servicePaymentInformation": {"allOf": [{"$ref": "#/components/schemas/ServicePaymentInformation"}], "description": "LOOP 2110; SVC - SERVICE PAYMENT INFORMATION"}, "serviceAdjustments": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimAdjustments"}, "description": "LOOP 2110; CAS - SERVICE ADJUSTMENT, Segment repeat = 99", "default": []}, "serviceIdentification": {"allOf": [{"$ref": "#/components/schemas/ServiceIdentification"}], "description": "LOOP 2110; REF - SERVICE IDENTIFICATION"}, "renderingProviderInformation": {"allOf": [{"$ref": "#/components/schemas/RenderingProviderInformation"}], "description": "LOOP 2110; REF - RENDERING PROVIDER INFORMATION"}, "serviceSupplementalAmounts": {"allOf": [{"$ref": "#/components/schemas/ServiceSupplementalAmounts"}], "description": "LOOP 2110; AMT - SERVICE SUPPLEMENTAL AMOUNT"}, "serviceSupplementalQuantities": {"allOf": [{"$ref": "#/components/schemas/ServiceSupplementalQuantities"}], "description": "LOOP 2110; QTY - SERVICE SUPPLEMENTAL QUANTITY"}, "healthCareCheckRemarkCodes": {"type": "array", "items": {"$ref": "#/components/schemas/HealthCareCheckRemarkCodes"}, "description": "LOOP 2110; LQ - HEALTH CARE REMARK CODES, Segment repeat = 99", "default": []}, "healthCarePolicyIdentification": {"type": "array", "items": {"$ref": "#/components/schemas/HealthCarePolicyIdentification"}, "description": "LOOP 2120; REF where REF01 = 0K; segment repeat 5", "default": []}}, "description": "LOOP ID = 2110 ; Loop Repeat = 999"}, "cdpedi__x12__v5010__newmodels__claim_payment_advice__Subscriber": {"title": "Subscriber", "type": "object", "properties": {"organizationName": {"type": "string", "description": "LOOP 2100; NM103 where NM102=2"}, "lastName": {"type": "string", "description": "LOOP 2100; NM103 where NM102=1"}, "firstName": {"type": "string", "description": "LOOP 2100; NM104"}, "middleName": {"type": "string", "description": "LOOP 2100; NM105"}, "suffix": {"type": "string", "description": "LOOP 2100; NM107"}, "taxId": {"type": "string", "description": "LOOP 2100 NM109 where NM108=FI"}, "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": {"type": "string", "description": "LOOP 2100 NM109 where NM108=II"}, "memberId": {"type": "string", "description": "LOOP 2100 NM109 where NM108=MI"}}}, "cdpedi__x12__v5010__newmodels__claim_status_cdpedi__Payer": {"title": "Payer", "type": "object", "properties": {"organizationName": {"type": "string", "description": "NM103"}, "payerIdentification": {"type": "string", "description": "NM109 where NM108=PI"}, "centersForMedicareAndMedicaidServicePlanId": {"type": "string", "description": "NM109 where NM108=XV"}, "payerContactInformation": {"allOf": [{"$ref": "#/components/schemas/Contact"}], "description": "PER", "default": {}}, "claimStatusTransactions": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimStatusTransactions"}, "description": "Loop 2000C", "default": []}}, "description": "Model for the Payer Identification for LOOP 2100A"}, "cdpedi__x12__v5010__newmodels__claim_status_cdpedi__ServiceLines": {"title": "ServiceLines", "type": "object", "properties": {"service": {"allOf": [{"$ref": "#/components/schemas/Service"}], "description": "service for Loop 2220[D|E]", "default": {}}, "serviceClaimStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceClaimStatus"}, "description": "Loop 2220[D|E]", "default": []}, "lineItemControlNumber": {"type": "string", "description": "Loop 2220[D|E] REF02"}, "serviceLineDate": {"type": "string", "description": "Loop 2220[D|E] DTP03 where DTP02=D8"}, "beginServiceLineDate": {"type": "string", "description": "Loop 2220[D|E] DTP03 where DTP02=RD8"}, "endServiceLineDate": {"type": "string", "description": "Loop 2220[D|E] DTP03 where DTP02=RD8"}}}, "cdpedi__x12__v5010__newmodels__claim_status_cdpedi__Subscriber": {"title": "Subscriber", "type": "object", "properties": {"organizationName": {"type": "string", "description": "NM103 where NM102=2"}, "lastName": {"type": "string", "description": "NM103 where NM102=1"}, "firstName": {"type": "string", "description": "NM104"}, "middleName": {"type": "string", "description": "NM105"}, "suffix": {"type": "string", "description": "NM107"}, "memberId": {"type": "string", "description": "NM109 where NM108=MI"}, "employerIdentificationNumber": {"type": "string", "description": "NM109 where NM108=24"}, "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": {"type": "string", "description": "NM109 where NM108=II"}}}}}, "x-readme": {"explorer-enabled": true, "proxy-enabled": true}}