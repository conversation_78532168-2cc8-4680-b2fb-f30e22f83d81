const RefParser = require("@apidevtools/json-schema-ref-parser");
const fs = require('node:fs');
var { openapiSchemaToJsonSchema: toJsonSchema } = require("@openapi-contrib/openapi-schema-to-json-schema");
// openapi schema to convert to json schema
var Ajv = require('ajv');
var ajv = new Ajv();
ajv.addKeyword("example")
//console.log(data)
RefParser.dereference('openapi/claims_responses_and_reports_v2.json', (err, schema) => {
  if (err) {
    console.error(err);
  }
  else {
    // `schema` is just a normal JavaScript object that contains your entire JSON Schema,
    // including referenced files, combined into a single object
    console.log(schema);
    convertedSchema = toJsonSchema(schema)

    // Create an object containing both Request and Response schemas
    // let combinedSchemas = {
    //   Request: convertedSchema.components.schemas.Request,
    //   Response: convertedSchema.components.schemas.Response
    // };

    // Stringify the combined schemas
    let me = JSON.stringify(convertedSchema.components.schemas, null, 2);

    // Write the combined schemas to the file
    fs.writeFileSync('MedicalClaimsResponseReports.json', me);

    // Validate both schemas if needed
    //    let validateRequest = ajv.compile(convertedSchema.components.schemas.Request);
    //    let validateResponse = ajv.compile(convertedSchema.components.schemas.Response);

    //  console.log('Request validation:', validateRequest);
    //  console.log('Response validation:', validateResponse);
  }
})