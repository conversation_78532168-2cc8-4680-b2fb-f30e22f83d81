{"openapi": "3.0.1", "info": {"title": "Medical Network Eligibility v3", "description": "270 Eligibility Transaction", "version": "v3"}, "servers": [{"url": "https://sandbox.apigw.changehealthcare.com", "description": "Optum"}], "tags": [{"name": "Health Check", "description": "Health Check"}, {"name": "Eligibility", "description": "Medical Eligibility"}], "paths": {"/medicalnetwork/eligibility/v3/": {"post": {"tags": ["Eligibility"], "summary": "Check Eligibility", "description": "accepts 270 EDI json object model and produces a json object model of a 271 response", "operationId": "medicalEligibility", "parameters": [{"name": "x-chng-trace-id", "in": "header", "required": false, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer Your-Access-Token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MedicalEligibility"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/medicalnetwork/eligibility/v3/healthcheck": {"get": {"tags": ["Health Check"], "summary": "Health Check", "operationId": "healthCheck", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer Your-Access-Token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HealthCheck"}}}}}}}, "/medicalnetwork/eligibility/v3/raw-x12": {"post": {"tags": ["Eligibility"], "summary": "Check Eligibility x12", "description": "Takes json with a single string property 'x12' that should have a 270 edi string and produces a json response with a single 'x12' property that should have a 271 edi string", "operationId": "rawX12", "parameters": [{"name": "x-chng-trace-id", "in": "header", "required": false, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer Your-Access-Token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RawX12Request"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}}, "components": {"schemas": {"AdditionalIdentification": {"type": "object", "properties": {"planNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=18 Plan Number"}, "policyNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=1L Group or Policy Number"}, "memberIdentificationNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=1W Member Identification Number"}, "contractNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=CT Contract Number"}, "medicalRecordIdentificationNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=EA Medical Record Identification Number"}, "patientAccountNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=EJ Patient Account Number"}, "healthInsuranceClaimNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=F6 Health Insurance (HIC) Number"}, "identificationCardSerialNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=GH Identification Card Serial Number"}, "insurancePolicyNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=IG Insurance Policy Number"}, "planNetworkIdentificationNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=N6 Plan Network Identification Number"}, "agencyClaimNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=Y4 Agency Claim Number"}}, "description": "Loop: 2100C and 2100D, Segment: REF Notes: Additional Identification"}, "Address": {"type": "object", "properties": {"address1": {"maxLength": 55, "minLength": 0, "type": "string", "description": "Segment: N3, Element: N301, Notes: Required, Address Information", "example": "123 address1"}, "address2": {"maxLength": 55, "minLength": 0, "type": "string", "description": "Segment: N3, Element: N302, Notes: Address Information"}, "city": {"maxLength": 30, "minLength": 0, "type": "string", "description": "Segment: N4, Element: N401, Notes: Required, city", "example": "city1"}, "state": {"maxLength": 2, "minLength": 0, "type": "string", "description": "Segment: N4, Element: N402, Notes: state example: TN, WA", "example": "wa"}, "postalCode": {"maxLength": 15, "minLength": 0, "type": "string", "description": "Segment: N4, Element: N403", "example": "981010000"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "locationIdentifier": {"type": "string", "description": "Segment: N4, Element: N406"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407, Notes: Country SubDivision Code"}}, "description": "Address: N3 and N4"}, "Encounter": {"type": "object", "properties": {"beginningDateOfService": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: DTP, Element: DTP03, Notes: DTP01=291 and DTP02=RD8, Date Expressed in Format YYYYMMDD."}, "endDateOfService": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: DTP, Element: DTP03, Notes: DTP01=291 and DTP02=RD8, Date Expressed in Format YYYYMMDD."}, "dateOfService": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: DTP, Element: DTP03, Notes: DTP01=291 and DTP02=D8, Date Expressed in Format YYYYMMDD."}, "serviceTypeCodes": {"maxItems": 99, "minItems": 0, "type": "array", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ01, Notes: Service Type Codes, Allowed Values are: '1' Medical Care,'2' Surgical, '3' Consultation, '4' Diagnostic X-Ray, '5' Diagnostic Lab, '6' Radiation Therapy, '7' Anesthesia, '8' Surgical Assistance, '9' Other Medical, '10' Blood Charges, '11' Used Durable Medical Equipment, '12' Durable Medical Equipment Purchase, '13' Ambulatory Service Center Facility, '14' Renal Supplies in the Home, '15' Alternate Method Dialysis, '16' Chronic Renal Disease (CRD) Equipment, '17' Pre-Admission Testing, '18' Durable Medical Equipment Rental, '19' Pneumonia Vaccine, '20' Second Surgical Opinion, '21' Third Surgical Opinion, '22' Social Work, '23' Diagnostic Dental, '24' Periodontics, '25' Restorative, '26' Endodontics, '27' Maxillofacial Prosthetics, '28' Adjunctive Dental Services, '30' Health Benefit Plan Coverage, '32' Plan Waiting Period, '33' Chiropractic, '34' Chiropractic Office Visits, '35' Dental Care, '36' Dental Crowns, '37' Dental Accident, '38' Orthodontics, '39' Prosthodontics, '40' Oral Surgery, '41' Routine (Preventive) Dental, '42' Home Health Care, '43' Home Health Prescriptions, '44' Home Health Visits, '45' Hospice, '46' Respite Care, '47' Hospital, '48' Hospital - Inpatient, '49' Hospital - Room and Board, '50' Hospital - Outpatient, '51' Hospital - Emergency Accident, '52' Hospital - Emergency Medical, '53' Hospital - Ambulatory Surgical, '54' Long Term Care, '55' Major Medical, '56' Medically Related Transportation, '57' Air Transportation, '58' Cabulance, '59' Licensed Ambulance, '60' General Benefits, '61' In-vitro Fertilization, '62' MRI/CAT Scan, '63' Donor Procedures, '64' Acupuncture, '65' Newborn Care, '66' Pathology, '67' Smoking Cessation, '68' Well Baby Care, '69' Maternity, '70' Transplants, '71' Audiology Exam, '72' Inhalation Therapy, '73' Diagnostic Medical, '74' Private Duty Nursing, '75' Prosthetic Device, '76' Dialysis, '77' Otological Exam, '78' Chemotherapy, '79' Allergy Testing, '80' Immunizations, '81' Routine Physical, '82' Family Planning, '83' Infertility, '84' Abortion, '85' AIDS, '86' Emergency Services, '87' Cancer, '88' Pharmacy, '89' Free Standing Prescription Drug, '90' Mail Order Prescription Drug, '91' Brand Name Prescription Drug, '92' Generic Prescription Drug, '93' Podiatry, '94' Podiatry - Office Visits, '95' Podiatry - Nursing Home Visits, '96' Professional (Physician), '97' Anesthesiologist, '98' Professional (Physician) Visit - Office, '99' Professional (Physician) Visit - Inpatient, 'A0' Professional (Physician) Visit - Outpatient, 'A1' Professional (Physician) Visit - Nursing Home, 'A2' Professional (Physician) Visit - Skilled Nursing Facility, 'A3' Professional (Physician) Visit - Home, 'A4' Psychiatric, 'A5' Psychiatric - Room and Board, 'A6' Psychotherapy, 'A7' Psychiatric - Inpatient, 'A8' Psychiatric - Outpatient, 'A9' Rehabilitation, 'AA' Rehabilitation - Room and Board, 'AB' Rehabilitation - Inpatient, 'AC' Rehabilitation - Outpatient, 'AD' Occupational Therapy, 'AE' Physical Medicine, 'AF' Speech Therapy, 'AG' Skilled Nursing Care, 'AH' Skilled Nursing Care - Room and Board, 'AI' Substance Abuse, 'AJ' Alcoholism, 'AK' Drug Addiction, 'AL' Vision (Optometry), 'AM' Frames, 'AN' Routine Exam, 'AO' Lenses, 'AQ' Nonmedically Necessary Physical, 'AR' Experimental Drug Therapy, 'B1' Burn Care, 'B2' Brand Name Prescription Drug - Formulary, 'B3' Brand Name Prescription Drug - Non-Formulary, 'BA' Independent Medical Evaluation, 'BB' Partial Hospitalization (Psychiatric), 'BC' Day Care (Psychiatric), 'BD' Cognitive Therapy, 'BE' Massage Therapy, 'BF' Pulmonary Rehabilitation, 'BG' Cardiac Rehabilitation, 'BH' Pediatric, 'BI' Nursery, 'BJ' Skin, 'BK' Orthopedic, 'BL' Cardiac, 'BM' Lymphatic, 'BN' Gastrointestinal, 'BP' Endocrine, 'BQ' Neurology, 'BR' Eye, 'BS' Invasive Procedures, 'BT' Gynecological, 'BU' Obstetrical, 'BV' Obstetrical/Gynecological, 'BW' Mail Order Prescription Drug: Brand Name, 'BX' Mail Order Prescription Drug: Generic, 'BY' Physician Visit - Office: Sick, 'BZ' Physician Visit - Office: Well, 'C1' Coronary Care, 'CA' Private Duty Nursing - Inpatient, 'CB' Private Duty Nursing - Home, 'CC' Surgical Benefits - Professional (Physician), 'CD' Surgical Benefits - Facility, 'CE' Mental Health Provider - Inpatient, 'CF' Mental Health Provider - Outpatient, 'CG' Mental Health Facility - Inpatient, 'CH' Mental Health Facility - Outpatient, 'CI' Substance Abuse Facility - Inpatient, 'CJ' Substance Abuse Facility - Outpatient, 'CK' Screening X-ray, 'CL' Screening laboratory, 'CM' Mammogram, High Risk Patient, 'CN' Mammogram, Low Risk Patient, 'CO' Flu Vaccination, 'CP' Eyewear and Eyewear Accessories, 'CQ' Case Management, 'DG' Dermatology, 'DM' Durable Medical Equipment, 'DS' Diabetic Supplies, 'GF' Generic Prescription Drug - Formulary, 'GN' Generic Prescription Drug - Non-Formulary, 'GY' Allergy, 'IC' Intensive Care, 'MH' Mental Health, 'NI' Neonatal Intensive Care, 'ON' Oncology, 'PT' Physical Therapy, 'PU' Pulmonary, 'RN' Renal, 'RT' Residential Psychiatric Treatment, 'TC' Transitional Care, 'TN' Transitional Nursery Care, 'UC' Urgent Care", "items": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "30", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AQ", "AR", "B1", "B2", "B3", "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BK", "BL", "BM", "BN", "BP", "BQ", "BR", "BS", "BT", "BU", "BV", "BW", "BX", "BY", "BZ", "C1", "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "DG", "DM", "DS", "GF", "GN", "GY", "IC", "MH", "NI", "ON", "PT", "PU", "RN", "RT", "TC", "TN", "UC"]}}, "priorAuthorizationOrReferralNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: REF, Element: REF02, Notes: Prior Authorization or Referral Number"}, "referenceIdentificationQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: REF, Element: REF01, Notes: Prior Authorization or Referral Number, Allowed Values are: '9F' Referral Number, 'G1' Prior Authorization Number", "enum": ["9F", "G1"]}, "industryCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: III, Element: III02, Notes: III01=ZZ Industry Code, Allowed Values are: '01' Pharmacy, '02' Telehealth Provided Other than in Patient's Home, '03' School, '04' Homeless Shelter, '05' Indian Health Service Free-standing Facility, '06' Indian Health Service Provider-based Facility, '07' Tribal 638 Free-standing Facility, '08' Tribal 638 Provider-based Facility, '09' Prison/Correctional Facility, '10' Telehealth Provided in Patient's Home, '11' Office, '12' Home, '13' Assisted Living Facility, '14' Group Home, '15' Mobile Unit, '16' Temporary Lodging, '17' Walk-in Retail Health Clinic, '18' Place of Employment-Worksite, '19' Off Campus-Outpatient Hospital, '20' Urgent Care Facility, '21' Inpatient Hospital, '22' On Campus-Outpatient Hospital, '23' Emergency Room - Hospital, '24' Ambulatory Surgical Center, '25' Birthing Center, '26' Military Treatment Facility, '31' Skilled Nursing Facility, '32' Nursing Facility, '33' Custodial Care Facility, '34' Hospice, '41' Ambulance - Land, '42' Ambulance - Air or Water, '49' Independent Clinic, '50' Federally Qualified Health Center, '51' Inpatient Psychiatric Facility, '52' Psychiatric Facility - Partial Hospitalization, '53' Community Mental Health Center, '54' Intermediate Care Facility / Individuals with Intellectual Disabilities, '55' Residential Substance Abuse Treatment Facility, '56' Psychiatric Residential Treatment Center, '57' Non-residential Substance Abuse Treatment Facility, '58' Non-residential Opioid Treatment Facility, '60' Mass Immunization Center, '61' Comprehensive Inpatient Rehabilitation Facility, '62' Comprehensive Outpatient Rehabilitation Facility, '65' End-Stage Renal Disease Treatment Facility, '71' Public Health Clinic, '72' Rural Health Clinic, '81' Independent Laboratory, '99' Other Place of Service", "enum": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "31", "32", "33", "34", "41", "42", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "60", "61", "62", "65", "71", "72", "81", "99"]}, "productOrServiceIDQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-1, Notes: Composite Medical Procedure Identifier - Product or Service ID Qualifier, Allowed Values are: 'AD' American Dental Association Codes, 'CJ' Current Procedural Terminology (CPT) Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'ID' International Classification of Diseases, 9th Revision, Clinical Modification (ICD-9-CM) - Procedure, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code, 'N4' National Drug Code in 5-4-2 Format, 'ZZ' Mutually Defined", "enum": ["AD", "CJ", "HC", "ID", "IV", "N4", "ZZ"]}, "procedureCode": {"maxLength": 48, "minLength": 0, "type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-2, Notes: Composite Medical Procedure Identifier - Procedure Code"}, "procedureModifiers": {"maxItems": 4, "minItems": 0, "type": "array", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-3, Notes: Composite Medical Procedure Identifier - Procedure Modifier", "items": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-3, Notes: Composite Medical Procedure Identifier - Procedure Modifier"}}, "diagnosisCodePointer": {"maxItems": 4, "minItems": 0, "type": "array", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ05, Notes: Composite Diagnosis Code Pointer", "items": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ05, Notes: Composite Diagnosis Code Pointer"}}, "medicalProcedures": {"maxItems": 98, "minItems": 0, "type": "array", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-1-3, Notes: Composite Medical Procedure Identifier", "items": {"$ref": "#/components/schemas/MedicalProcedure"}}}, "description": "Loop: 2110C and 2110D, Notes: Eligibility or Benefit Information"}, "HealthCareInformation": {"type": "object", "properties": {"diagnosisTypeCode": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-1 , Notes: Diagnosis Type Code, Allowed descriptions are: 'BK' International Classification of Diseases Clinical Modification (ICD-9-CM) Principal Diagnosis, 'ABK' International Classification of Diseases Clinical Modification (ICD-10-CM) Principal Diagnosis, 'BF' International Classification of Diseases Clinical Modification (ICD-9-CM) Diagnosis, 'ABF' International Classification of Diseases Clinical Modification (ICD-10-CM) Diagnosis", "enum": ["BK", "ABK", "BF", "ABF"]}, "diagnosisCode": {"maxLength": 30, "minLength": 0, "pattern": "^[A-Za-z0-9]+$", "type": "string", "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-2, Notes: Diagnosis Code"}}, "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-1 HI01-2, Notes: Health Care Diagnosis"}, "InformationReceiverName": {"type": "object", "properties": {"stateLicenceNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=0B State License Number"}, "medicareProviderNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=1C Medicare Provider Number"}, "medicaidProviderNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=1D Medicaid Provider Number"}, "facilityIdNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=1J Facility ID Number"}, "contactNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=CT Contact Number"}, "devicePinNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=EL Electronic Device Pin Number"}, "submitterIdNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=EO Submitter Identification Number"}, "nationalProviderIdentifier": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=HPI Centers for Medicare and Medicaid Services National Provider Identifier"}, "providerPlanNetworkIdNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=N5 Provider Plan Network Identification Number"}, "facilityNetworkIdNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=N7 Facility Network Identification Number"}, "priorIdentifierNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=Q4 Prior Identifier Number"}, "socialSecurityNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=SY Social Security Number"}, "federalTaxpayerIdentificationNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: REF01=TJ Federal Taxpayer's Identification Number"}, "informationReceiverAdditionalIdentifierState": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF03, Notes: REF01=0B REF02=stateLicenceNumber Federal Taxpayer's Identification Number"}, "address": {"$ref": "#/components/schemas/Address"}}, "description": "Loop: 2100B, Notes: Information Receiver Name"}, "MedicalEligibility": {"required": ["controlNumber", "subscriber"], "type": "object", "properties": {"submitterTransactionIdentifier": {"type": "string", "description": "BHT03"}, "controlNumber": {"maxLength": 9, "minLength": 9, "type": "string", "description": "Segment: ISA, Element: ISA13, Notes: Required, Interchange Control Number - must be exactly 9 positive unsigned numeric characters.", "example": "*********"}, "tradingPartnerServiceId": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100A Segment: NM1, Element: NM109, Notes: we send this as MN108 as PI", "example": "serviceId"}, "tradingPartnerName": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100A, Segment: NM1, Element: NM103, Notes: organizational name"}, "provider": {"$ref": "#/components/schemas/Provider"}, "portalUsername": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: User Identification REF01=JD"}, "portalPassword": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: REF, Element: REF02, Notes: Personal Identification Number (PIN) REF01=4A"}, "informationReceiverName": {"$ref": "#/components/schemas/InformationReceiverName"}, "subscriber": {"$ref": "#/components/schemas/RequestSubscriber"}, "dependents": {"maxItems": **********, "minItems": 1, "type": "array", "description": "Loop: 2000D, Notes: Dependent Detail", "items": {"$ref": "#/components/schemas/RequestDependent"}}, "encounter": {"$ref": "#/components/schemas/Encounter"}}, "description": "MedicalEligibility"}, "MedicalProcedure": {"type": "object", "properties": {"productOrServiceIDQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-1, Notes: Composite Medical Procedure Identifier - Product or Service ID Qualifier, Allowed Values are: 'AD' American Dental Association Codes, 'CJ' Current Procedural Terminology (CPT) Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'ID' International Classification of Diseases, 9th Revision, Clinical Modification (ICD-9-CM) - Procedure, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code, 'N4' National Drug Code in 5-4-2 Format, 'ZZ' Mutually Defined", "enum": ["AD", "CJ", "HC", "ID", "IV", "N4", "ZZ"]}, "procedureCode": {"maxLength": 48, "minLength": 0, "type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-2, Notes: Composite Medical Procedure Identifier - Procedure Code"}, "procedureModifiers": {"maxItems": 4, "minItems": 0, "type": "array", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-3, Notes: Composite Medical Procedure Identifier - Procedure Modifier", "items": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ02-3, Notes: Composite Medical Procedure Identifier - Procedure Modifier"}}, "diagnosisCodePointer": {"maxItems": 4, "minItems": 0, "type": "array", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ05, Notes: Composite Diagnosis Code Pointer", "items": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: EQ, Element: EQ05, Notes: Composite Diagnosis Code Pointer"}}}, "description": "Loop: 2110C and 2110D, Notes: Eligibility or Benefit Information, EQ02 and beyond only"}, "Provider": {"type": "object", "properties": {"organizationName": {"maxLength": 60, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM103, Notes: NM101=PR when providerType='payer' && payerId is present otherwise 1P for Provider, NM102=2 Non-Person Entity, organizationName 1-60 alphanumeric characters", "example": "provider_name"}, "firstName": {"maxLength": 35, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM104, Notes: NM101=PR when providerType='payer' && payerId is present otherwise 1P for Provider, NM102=1 Person, firstName 1-35 alphanumeric characters"}, "lastName": {"maxLength": 60, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM103, Notes: NM101=PR when providerType='payer' && payerId is present otherwise 1P for Provider, NM102=1 Person, lastName 1-60 alphanumeric characters"}, "npi": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM109, Notes: NM108=XX Centers for Medicare and Medicaid Services National Provider Identifier 2-80 alphanumeric characters", "example": "0*********"}, "serviceProviderNumber": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM109, Notes: NM108=SV Service Provider Number 2-80 alphanumeric characters", "example": "54321"}, "payorId": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM109, Notes: NM101=PR when providerType='payer' && payerId is present otherwise 1P for Provider, NM108=PI Payor Identification 2-80 alphanumeric characters"}, "taxId": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B Segment: MN1, Element: NM109, Notes: NM108=FI Federal Taxpayer's Identification Number 2-80 alphanumeric characters"}, "ssn": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109 Notes: NM108=34 Social Security Number"}, "pharmacyProcessorNumber": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109 Notes: NM108=PP Pharmacy Processor Number"}, "servicesPlanID": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109 Notes: NM108=XV Centers for Medicare and Medicaid Services PlanID"}, "employersId": {"maxLength": 80, "minLength": 0, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109 Notes: NM108=24 Employer's Identification Number"}, "providerCode": {"type": "string", "description": "Loop: 2100B Segment: PRV, Element: PRV01, Notes: PRV02=PXC Allowed descriptions are: 'AD' Admitting, 'AT' Attending, 'BI' Billing, 'CO' Consulting, 'CV' Covering, 'H' Hospital, 'HH' Home Health Care, 'LA' Laboratory, 'OT' Other Physician, 'P1' Pharmacist, 'P2' Pharmacy, 'PC' Primary Care Physician, 'PE' Performing, 'R' Rural Health Clinic, 'RF' Referring, 'SB' Submitting, 'SK' Skilled Nursing Facility, 'SU' Supervising", "example": "AD", "enum": ["AD", "AT", "BI", "CO", "CV", "H", "HH", "LA", "OT", "P1", "P2", "PC", "PE", "R", "RF", "SB", "SK", "SU"]}, "referenceIdentification": {"type": "string", "description": "Loop: 2100B Segment: PRV, Element: PRV03, Notes: PRV02=PXC referenceIdentification and controlNumber into PRV03", "example": "54321g"}, "providerType": {"type": "string", "description": "Loop: 2100B Segment: MN1, Element: MN101, Notes: Allowed descriptions are: 'PR' when providerType='payer' && payerId is present, '2B' when providerType='third-party administrator', '36' when providerType='employer', '80' when providerType='hospital', 'FA' when providerType='facility', 'GP' when providerType='gateway provider', 'P5' when providerType='plan sponsor', '1P' when providerType='provider'", "enum": ["payer", "third-party administrator", "employer", "hospital", "facility", "gateway provider", "plan sponsor", "provider"]}}, "description": "Loop: 2100B, Segment: NM1, Notes: Information Receiver"}, "RequestDependent": {"type": "object", "properties": {"birthSequenceNumber": {"maxLength": 9, "minLength": 0, "pattern": "^[0-9]+$", "type": "string", "description": "Loop: 2100D Segment: INS, Element: INS17, Notes: Birth Sequence Number - must be exactly 9 positive unsigned numeric characters."}, "individualRelationshipCode": {"type": "string", "description": "Loop: 2100D Segment: INS, Element: INS02, Notes: Allowed Values are: '01' - Spouse, '19' - Child, '34' Other Adult", "enum": ["01", "19", "34"]}, "issueNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100D Segment: REF, Element: REF02, Notes: REF01=IF Issue Number"}, "eligibilityCategory": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100D Segment: REF, Element: REF02, Notes: REF01=MRC Eligibility Category"}, "memberId": {"maxLength": 80, "minLength": 2, "pattern": "^[A-Za-z0-9-]+$", "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM109, Notes: NM108=MI, memberId 2-80 alphanumeric characters", "example": "0000000000"}, "firstName": {"maxLength": 35, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM104, Notes: firstName 1-35 alphanumeric characters", "example": "jane<PERSON>ne"}, "middleName": {"maxLength": 25, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM105, Notes: middleName 1-25 alphanumeric characters"}, "lastName": {"maxLength": 60, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM103, Notes: lastName 1-60 alphanumeric characters", "example": "doeOne"}, "suffix": {"maxLength": 10, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM107, Notes: lastName 1-10 alphanumeric characters"}, "gender": {"maxLength": 1, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: DMG, Element: DMG03, Notes: gender 1 character 'M' or 'F'", "example": "F", "enum": ["M", "F"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: DMG, Element: DMG02, Notes: date of birth in YYYYMMDD", "example": "18160421"}, "ssn": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=SY Social Security Number"}, "groupNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=6P Group Number", "example": "**********"}, "idCard": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=HJ Identity Card Number"}, "providerCode": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: PRV, Element: PRV01, Notes: Provider Code, Notes: Allowed Values are: 'AD' Admitting, 'AT' Attending, 'BI' Billing, 'CO' Consulting, 'CV' Covering, 'H' Hospital, 'HH' Home Health Care, 'LA' Laboratory, 'OT' Other Physician, 'P1' Pharmacist, 'P2' Pharmacy, 'PC' Primary Care Physician, 'PE' Performing, 'R' Rural Health Clinic, 'RF' Referring, 'SK' Skilled Nursing Facility, 'SU' Supervising", "enum": ["AD", "AT", "BI", "CO", "CV", "H", "HH", "LA", "OT", "P1", "P2", "PC", "PE", "R", "RF", "SK", "SU"]}, "referenceIdentificationQualifier": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: PRV, Element: PRV02, Notes: Allowed Values are: '9K' Servicer, 'D3' National Council for Prescription Drug Programs Pharmacy Number, 'EI' Employer's Identification Number, 'HPI' Centers for Medicare and Medicaid Services National Provider Identifier, 'PXC' Health Care Provider Taxonomy Code, 'SY' Social Security Number, 'TJ' Federal Taxpayer's Identification Number", "enum": ["9K", "D3", "EI", "HPI", "PXC", "SY", "TJ"]}, "providerIdentifier": {"maxLength": 50, "minLength": 0, "pattern": "^[A-Za-z0-9]+$", "type": "string", "description": "Loop: 2100C and 2100D, Segment: PRV, Element: PRV03, Notes: Provider Identifier"}, "beginningCardIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=102, DTP02=RD8 Retired"}, "endCardIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=102, DTP02=RD8 Retired"}, "idCardIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=102, DTP02=D8 Retired"}, "planIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=291, DTP02=D8 Retired"}, "beginningPlanIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=291, DTP02=RD8 Retired"}, "endPlanIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=291, DTP02=RD8 Retired"}, "healthCareCodeInformation": {"maxItems": 8, "minItems": 0, "type": "array", "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-1 HI01-2, Notes: Health Care Diagnosis", "items": {"$ref": "#/components/schemas/HealthCareInformation"}}, "address": {"$ref": "#/components/schemas/Address"}, "additionalIdentification": {"$ref": "#/components/schemas/AdditionalIdentification"}}, "description": "Loop: 2000D, Notes: Dependent Detail"}, "RequestSubscriber": {"type": "object", "properties": {"birthSequenceNumber": {"maxLength": 9, "minLength": 0, "pattern": "^[0-9]+$", "type": "string", "description": "Loop: 2100C Segment: INS, Element: INS17, Notes: Birth Sequence Number - must be exactly 9 positive unsigned numeric characters."}, "caseNumber": {"maxLength": 50, "minLength": 0, "pattern": "^[A-Za-z0-9]+$", "type": "string", "description": "Loop: 2100C Segment: REF, Element: REF02, Notes: REF01=3H Case Number"}, "medicaidRecipientIdentificationNumber": {"maxLength": 80, "minLength": 0, "pattern": "^[A-Za-z0-9]+$", "type": "string", "description": "Loop: 2110C Segment: REF, Element: REF02, Notes: REF01=NQ Medicaid Recipient Identification Number"}, "spendDownAmount": {"maxLength": 18, "minLength": 0, "type": "string", "description": "Loop: 2110C Segment: ATM, Element: ATM02, Notes: ATM01=R Spend Down"}, "spendDownTotalBilledAmount": {"maxLength": 18, "minLength": 0, "type": "string", "description": "Loop: 2110C Segment: ATM, Element: ATM02, Notes: ATM01=PB Billed Amount"}, "coverageLevelCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Loop: 2110C Segment: EQ, Element: EQ03, Notes: Retired, Not Used"}, "memberId": {"maxLength": 80, "minLength": 0, "pattern": "^[A-Za-z0-9-]+$", "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM109, Notes: NM108=MI, memberId 2-80 alphanumeric characters", "example": "0000000000"}, "firstName": {"maxLength": 35, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM104, Notes: firstName 1-35 alphanumeric characters", "example": "john<PERSON><PERSON>"}, "middleName": {"maxLength": 25, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM105, Notes: middleName 1-25 alphanumeric characters"}, "lastName": {"maxLength": 60, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM103, Notes: lastName 1-60 alphanumeric characters", "example": "doeOne"}, "suffix": {"maxLength": 10, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: MN1, Element: NM107, Notes: lastName 1-10 alphanumeric characters"}, "gender": {"maxLength": 1, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: DMG, Element: DMG03, Notes: gender 1 character 'M' or 'F'", "example": "M", "enum": ["M", "F"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: DMG, Element: DMG02, Notes: date of birth in YYYYMMDD", "example": "18800102"}, "ssn": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=SY Social Security Number"}, "groupNumber": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=6P Group Number", "example": "**********"}, "idCard": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Loop: 2100C and 2100D, Segment: REF, Element: REF02, Notes: REF01=HJ Identity Card Number"}, "providerCode": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: PRV, Element: PRV01, Notes: Allowed Values are: 'AD' Admitting, 'AT' Attending, 'BI' Billing, 'CO' Consulting, 'CV' Covering, 'H' Hospital, 'HH' Home Health Care, 'LA' Laboratory, 'OT' Other Physician, 'P1' Pharmacist, 'P2' Pharmacy, 'PC' Primary Care Physician, 'PE' Performing, 'R' Rural Health Clinic, 'RF' Referring, 'SK' Skilled Nursing Facility, 'SU' Supervising", "enum": ["AD", "AT", "BI", "CO", "CV", "H", "HH", "LA", "OT", "P1", "P2", "PC", "PE", "R", "RF", "SK", "SU"]}, "referenceIdentificationQualifier": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: PRV, Element: PRV02, Notes: Allowed Values are: '9K' Servicer, 'D3' National Council for Prescription Drug Programs Pharmacy Number, 'EI' Employer's Identification Number, 'HPI' Centers for Medicare and Medicaid Services National Provider Identifier, 'PXC' Health Care Provider Taxonomy Code, 'SY' Social Security Number, 'TJ' Federal Taxpayer's Identification Number", "enum": ["9K", "D3", "EI", "HPI", "PXC", "SY", "TJ"]}, "providerIdentifier": {"maxLength": 50, "minLength": 0, "pattern": "^[A-Za-z0-9]+$", "type": "string", "description": "Loop: 2100C and 2100D, Segment: PRV, Element: PRV03, Notes: Provider Identifier"}, "beginningCardIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=102, DTP02=RD8 Retired"}, "endCardIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=102, DTP02=RD8 Retired"}, "idCardIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=102, DTP02=D8 Retired"}, "planIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=291, DTP02=D8 Retired"}, "beginningPlanIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=291, DTP02=RD8 Retired"}, "endPlanIssueDate": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: DTP, Element: DTP03, Notes: DTP01=291, DTP02=RD8 Retired"}, "healthCareCodeInformation": {"maxItems": 8, "minItems": 0, "type": "array", "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-1 HI01-2, Notes: Health Care Diagnosis", "items": {"$ref": "#/components/schemas/HealthCareInformation"}}, "address": {"$ref": "#/components/schemas/Address"}, "additionalIdentification": {"$ref": "#/components/schemas/AdditionalIdentification"}}, "description": "Loop: 2100C, Notes: Subscriber Detail"}, "AdditionalInformation": {"type": "object", "properties": {"description": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: MSG, Element: MSG01, Notes: Message Text Description"}}, "description": "AdditionalInformation"}, "BenefitsAdditionalInformation": {"type": "object", "properties": {"stateLicenseNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=0B stateLicenseNumber"}, "medicareProviderNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=1D medicareProviderNumber"}, "medicaidProviderNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=1C medicaidProviderNumber"}, "facilityIdNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=1J facilityIdNumber"}, "personalIdentificationNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=4A personalIdentificationNumber"}, "planNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=18 planNumber"}, "policyNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=1L policyNumber"}, "memberId": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=1W memberId"}, "caseNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=3H caseNumber"}, "familyUnitNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=46 familyUnitNumber"}, "groupNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=6P groupNumber"}, "referralNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=9F referralNumber"}, "alternativeListId": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=ALS alternativeListId"}, "classOfContractCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=CEE classOfContractCode"}, "coverageListId": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=CLI coverageListId"}, "contractNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=CT contractNumber"}, "medicalRecordIdentificationNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=EA medicalRecordIdentificationNumber"}, "electronicDevicePin": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=EL electronicDevicePin"}, "submitterIdentificationNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=EO submitterIdentificationNumber"}, "patientAccountNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=EJ patientAccountNumber"}, "hicNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=F6 hicNumber"}, "drugFormularyNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=FO drugFormularyNumber"}, "priorAuthorizationNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=G1 priorAuthorizationNumber"}, "idCardSerialNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=GH idCardSerialNumber"}, "idCardNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=HJ idCardNumber"}, "centersForMedicareAndMedicaidServicesNPI": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=HPI centersForMedicareAndMedicaidServicesNPI"}, "issueNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=IF issueNumber"}, "insurancePolicyNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=IG insurancePolicyNumber"}, "userIdentification": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=JD userIdentification"}, "medicalAssistanceCategory": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=M7 medicalAssistanceCategory"}, "eligibilityCategory": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=MRC medicalAssistanceCategory"}, "planNetworkIdNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=N6 planNetworkIdNumber"}, "facilityNetworkIdentificationNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=N7 facilityNetworkIdentificationNumber"}, "medicaidRecepientIdNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=NQ medicaidRecipientIdNumber"}, "priorIdNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=Q4 priorIdNumber"}, "socialSecurityNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=SY socialSecurityNumber"}, "federalTaxpayersIdentificationNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=TJ federalTaxpayersIdentificationNumber"}, "agencyClaimNumber": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: REF, Element: REF02, Notes: REF01=Y4 agencyClaimNumber"}}, "description": "BenefitsAdditionalInformation"}, "BenefitsDateInformation": {"type": "object", "properties": {"discharge": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=096 Discharge"}, "discharges": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=096 Discharge", "items": {"$ref": "#/components/schemas/DtpDate"}}, "issue": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=102 issue"}, "effectiveDateOfChange": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=152 effectiveDateOfChange"}, "periodStart": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=193 periodStart"}, "periodEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=193 periodEnd"}, "completion": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=198 completion"}, "coordinationOfBenefits": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=290 coordinationOfBenefits"}, "plan": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=291 plan"}, "benefit": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=292 benefit"}, "primaryCareProvider": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=295 primaryCareProvider"}, "latestVisitOrConsultation": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=304 latestVisitOrConsultation"}, "eligibility": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=307 eligibility"}, "added": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=318 added"}, "cobraBegin": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=340 cobraBegin"}, "cobraEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=341 cobraEnd"}, "premiumPaidtoDateBegin": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=342 premiumPaidToDateBegin"}, "premiumPaidToDateEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=343 premiumPaidToDateEnd"}, "planBegin": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=346 planBegin"}, "planEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=347 planEnd"}, "benefitBegin": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=348 benefitBegin"}, "benefitEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=349 benefitEnd"}, "eligibilityBegin": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=356 eligibilityBegin"}, "eligibilityEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=357 eligibilityEnd"}, "enrollment": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=382 enrollment"}, "admission": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=435 admission"}, "admissions": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=435 admission", "items": {"$ref": "#/components/schemas/DtpDate"}}, "dateOfDeath": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=442 dateOfDeath"}, "certification": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=458 certification"}, "service": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=472 service"}, "policyEffective": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=539 policyEffective"}, "policyExpiration": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=540 policyExpiration"}, "dateOfLastUpdate": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=636 dateOfLastUpdate"}, "status": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03 Notes: DTP01=771 status"}}, "description": "BenefitsDateInformation"}, "BenefitsInformation": {"type": "object", "properties": {"code": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB01, Notes: Eligibility or Benefit Information Code"}, "name": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB01, Notes: Eligibility or Benefit Information Code"}, "coverageLevelCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB02, Notes: Coverage Level Code"}, "coverageLevel": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB02, Notes: Coverage Level"}, "serviceTypeCodes": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB03, Notes: Service Type Codes", "items": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB03, Notes: Service Type Codes"}}, "serviceTypes": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB03, Notes: Service Types", "items": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB03, Notes: Service Types"}}, "insuranceTypeCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB04, Notes: Insurance Type Code"}, "insuranceType": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB04, Notes: Insurance Type"}, "planCoverage": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB05, Notes: Plan Coverage Description"}, "timeQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB06, Notes: Time Period Qualifier Code"}, "timeQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB06, Notes: Time Period Qualifier"}, "benefitAmount": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB07, Notes: Monetary Amount"}, "benefitPercent": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB08, Notes: Percentage as Decimal"}, "quantityQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB09, Notes: Quantity Qualifier Code"}, "quantityQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB09, Notes: Quantity Qualifier"}, "benefitQuantity": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB10, Notes: Quantity"}, "authOrCertIndicator": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB11, Notes: Yes/No Condition or Response Code"}, "inPlanNetworkIndicatorCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB12, Notes: Yes/No Condition or Response Code"}, "inPlanNetworkIndicator": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB12, Notes: Yes/No Condition or Response"}, "headerLoopIdentifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: LS, Element: LS01, Notes: Loop Identifier Code"}, "trailerLoopIdentifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: LE, Element: LE01, Notes: Loop Identifier Code"}, "compositeMedicalProcedureIdentifier": {"$ref": "#/components/schemas/CompositeMedicalProcedureIdentifier"}, "benefitsAdditionalInformation": {"$ref": "#/components/schemas/BenefitsAdditionalInformation"}, "benefitsDateInformation": {"$ref": "#/components/schemas/BenefitsDateInformation"}, "benefitsRelatedEntity": {"$ref": "#/components/schemas/BenefitsRelatedEntity"}, "benefitsRelatedEntities": {"type": "array", "description": "Loop: 2120C and 2120D, Segments: NM1, N3, N4, PER, PRV, Notes: All occurrence of SUBSCRIBER/DEPENDENT BENEFIT RELATED ENTITY", "items": {"$ref": "#/components/schemas/BenefitsRelatedEntity"}}, "benefitsServiceDelivery": {"type": "array", "items": {"$ref": "#/components/schemas/BenefitsServiceDelivery"}}, "additionalInformation": {"type": "array", "items": {"$ref": "#/components/schemas/AdditionalInformation"}}, "eligibilityAdditionalInformation": {"$ref": "#/components/schemas/EligibilityAdditionalInformation"}, "eligibilityAdditionalInformationList": {"type": "array", "description": "Loop: 2115C and 2115D, Segments: III Notes: all occurrences", "items": {"$ref": "#/components/schemas/EligibilityAdditionalInformation"}}}, "description": "BenefitsInformation"}, "BenefitsRelatedEntity": {"type": "object", "properties": {"entityIdentifier": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM101, Notes: Entity Identifier Code"}, "entityType": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM102, Notes: Entity Type Qualifier"}, "entityName": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM103, Notes: Benefit Related Entity Last or Organization Name"}, "entityFirstname": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM104, Notes: Name First"}, "entityMiddlename": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM105, Notes: Name Middle"}, "entitySuffix": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM107, Notes: Name Suffix"}, "entityIdentification": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM108, Notes: Identification Code Qualifier"}, "entityIdentificationValue": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM109, Notes: Benefit Related Entity Identifier"}, "entityRelationship": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: NM1, Element: NM110, Notes: Entity Relationship Code"}, "address": {"$ref": "#/components/schemas/Address"}, "contactInformation": {"$ref": "#/components/schemas/ContactInformation"}, "providerInformation": {"$ref": "#/components/schemas/ProviderInformation"}}, "description": "BenefitsRelatedEntity"}, "BenefitsServiceDelivery": {"type": "object", "properties": {"quantityQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD01"}, "quantityQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD01, Notes: Description of the quantityQualifier Code"}, "quantity": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD02"}, "unitForMeasurementCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD03, Notes: Description of the qualifier Code"}, "sampleSelectionModulus": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD04"}, "timePeriodQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD05"}, "timePeriodQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD05, Notes: Description of the timePeriodQualifier Code"}, "numOfPeriods": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD06"}, "deliveryOrCalendarPatternCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD07, Notes: Description of the deliveryOrCalendarPatternQualifier Code"}, "deliveryPatternTimeCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD08"}, "unitForMeasurementQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD03"}, "unitForMeasurementQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD03, Notes: Description of the unitForMeasurementQualifier Code"}, "deliveryOrCalendarPatternQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD07"}, "deliveryOrCalendarPatternQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD07, Notes: Description of the deliveryOrCalendarPatternQualifier Code"}, "deliveryPatternTimeQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD08"}, "deliveryPatternTimeQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: HSD, Element: HSD08, Notes: Description of the deliveryPatternTimeQualifier Code"}}, "description": "BenefitsServiceDelivery"}, "CompositeMedicalProcedureIdentifier": {"type": "object", "properties": {"productOrServiceIdQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB13-1, Notes: Product or Service ID Qualifier Code"}, "productOrServiceIdQualifier": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB13-1, Notes: Product or Service ID Qualifier"}, "procedureCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB13-2, Notes: Procedure Code"}, "procedureModifiers": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB13-3-6, Notes: ProcedureModifier", "items": {"type": "string"}}, "productOrServiceID": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB13-8, Notes: Product or Service ID"}, "diagnosisCodePointer": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB14-1-4, Notes: Diagnosis<PERSON>odePointer", "items": {"type": "string"}}}, "description": "CompositeMedicalProcedureIdentifier"}, "ContactInformation": {"type": "object", "properties": {"name": {"type": "string", "description": "Loop: 2100A, 2120C and 2120D, Segments: PER, Element: PER02, Notes: Name"}, "contacts": {"type": "array", "description": "Loop: 2100A, 2120C and 2120D, Segments: PER, Element: PER03-8, Notes: Communication", "items": {"$ref": "#/components/schemas/Contacts"}}}, "description": "ContactInformation"}, "Contacts": {"type": "object", "properties": {"communicationMode": {"type": "string", "description": "Loop: 2100A, Segments: PER, Element: PER03-8, Notes: Communication Number Qualifier"}, "communicationNumber": {"type": "string", "description": "Loop: 2100A, Segments: PER, Element: PER03-8, Notes: Communication Number"}}, "description": "Contacts"}, "DtpDate": {"type": "object", "properties": {"date": {"type": "string", "description": "DTP03 where DTP02=D8, single date"}, "startDate": {"type": "string", "description": "DTP03 where DTP02=RD8 left side of date range"}, "endDate": {"type": "string", "description": "DTP03 where DTP02=RD8 right side of date range"}}, "description": "DtpDate"}, "EligibilityAdditionalInformation": {"type": "object", "properties": {"codeListQualifierCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: III, Element: III01"}, "industryCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: III, Element: III02"}, "codeCategory": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: III, Element: III03"}, "injuredBodyPartName": {"type": "string", "description": "Loop: 2110C and 2110D, Segment: III, Element: III04"}}, "description": "EligibilityAdditionalInformation"}, "Error": {"type": "object", "properties": {"field": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "followupAction": {"type": "string"}, "location": {"type": "string"}, "possibleResolutions": {"type": "string"}}}, "HealthCareDiagnosisCode": {"type": "object", "properties": {"diagnosisTypeCode": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-1, HI02-1, HI03-1, HI04-1, HI05-1, HI06-1, HI01-7, HI08-1"}, "diagnosisCode": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: HI, Element: HI01-2, HI02-2, HI03-2, HI04-2, HI05-2, HI06-2, HI01-2, HI08-2"}}, "description": "HealthCareDiagnosisCode"}, "MetaData": {"type": "object", "properties": {"senderId": {"type": "string", "description": "Sender id assigned to this request"}, "submitterId": {"type": "string", "description": "Submitter id assigned to this request"}, "billerId": {"type": "string", "description": "Billing id assigned to this request"}, "applicationMode": {"type": "string", "description": "Used by Optum to identify where this request can be found for support"}, "traceId": {"type": "string", "description": "Unique Id assigned to each request by Optum"}, "outboundTraceId": {"type": "string", "description": "BHT03 Value for submitterTransactionIdentifier"}}, "description": "Meta data about the response"}, "Payer": {"type": "object", "properties": {"entityIdentifier": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM101 Notes: Entity Identifier Code"}, "entityType": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM102 Notes: Entity Type Qualifier"}, "firstName": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM104 Notes: Name First"}, "lastName": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM103 Notes: Name Last"}, "name": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM103 Notes: Organization Name"}, "middleName": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM105 Notes: Name Middle"}, "suffix": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM107 Notes: Name Suffix"}, "employersId": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM109 Notes: NM108=24 Employer's Identification Number"}, "federalTaxpayersIdNumber": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM109 Notes: NM108=FI Federal Taxpayer's Identification Number"}, "naic": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM109 Notes: NM108=NI National Association of Insurance Commissioners (NAIC) Identification"}, "npi": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM109 Notes: NM108=XX Centers for Medicare and Medicaid Services National Provider Identifier"}, "centersForMedicareAndMedicaidPlanId": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM109 Notes: NM108=XV Centers for Medicare and Medicaid Services PlanID"}, "payorIdentification": {"type": "string", "description": "Loop: 2100A, Segments: NM1, Element: NM109 Notes: NM108=PI Payor Identification"}, "contactInformation": {"$ref": "#/components/schemas/ContactInformation"}, "aaaErrors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}, "etin": {"type": "string"}}, "description": "Payer"}, "PlanDateInformation": {"type": "object", "properties": {"discharge": {"type": "string", "description": "Loop: 2100C and 2100D or 2110C and 2110D, Segments: DTP, Element: DTP03, Notes: DTP01=096 Discharge"}, "issue": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=102 issue"}, "effectiveDateOfChange": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=152 effectiveDateOfChange"}, "periodStart": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03, Notes: DTP01=193 periodStart"}, "periodEnd": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03, Notes: DTP01=194 periodEnd"}, "completion": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03, Notes: DTP01=198 completion"}, "coordinationOfBenefits": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: DTP, Element: DTP03, Notes: DTP01=290 coordinationOfBenefits"}, "plan": {"type": "string", "description": "Loop: 2100C and 2100D or 2110C and 2110D, Segments: DTP, Element: DTP03, Notes: DTP01=291 plan"}, "benefit": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=292 benefit"}, "primaryCareProvider": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=295 primaryCareProvider"}, "latestVisitOrConsultation": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=304 latestVisitOrConsultation"}, "eligibility": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=307 eligibility"}, "added": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=318 added"}, "cobraBegin": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=340 cobraBegin"}, "cobraEnd": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=341 cobraEnd"}, "premiumPaidToDateBegin": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=342 premiumPaidToDateBegin"}, "premiumPaidToDateEnd": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=343 premiumPaidToDateEnd"}, "planBegin": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=346 planBegin"}, "planEnd": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=347 planEnd"}, "benefitBegin": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=348 benefitBegin"}, "benefitEnd": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=349 benefitEnd"}, "eligibilityBegin": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=356 eligibilityBegin"}, "eligibilityEnd": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=357 eligibilityEnd"}, "enrollment": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=382 enrollment"}, "admission": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=435 admission"}, "dateOfDeath": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=442 dateOfDeath"}, "certification": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=458 certification"}, "service": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=472 service"}, "policyEffective": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=539 policyEffective"}, "policyExpiration": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=540 policyExpiration"}, "dateOfLastUpdate": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=636 dateOfLastUpdate"}, "status": {"type": "string", "description": "Loop: 2100C and 2100D, Segments: DTP, Element: DTP03, Notes: DTP01=771 status"}}, "description": "PlanDateInformation"}, "PlanInformation": {"type": "object", "properties": {"stateLicenseNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=0B State License Number"}, "medicareProviderNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=1C Medicare Provider Number"}, "medicaidProviderNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=1D Medicaid Provider Number"}, "facilityIdNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=1J Facility ID Number"}, "personalIdentificationNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=4A Personal Identification Number"}, "planNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=18 Plan Number"}, "planDescription": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF03; Notes: REF01=18 Plan Description"}, "policyNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=1L Group or Policy Number"}, "memberId": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=1W Member Identification Number"}, "caseNumber": {"type": "string", "description": "Loop: 2100C; Segments: REF; Element: REF02; Notes: REF01=3H Case Number"}, "familyUnitNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=49 Family Unit Number"}, "groupNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=6P Group Number"}, "groupDescription": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF03; Notes: REF01=6P Group Description"}, "referralNumber": {"type": "string", "description": "Loop: 2110C, 2110D; Segments: REF; Element: REF02; Notes: REF01=9F Referral Number"}, "alternativeListId": {"type": "string", "description": "Loop: 2110C, 2110D; Segments: REF; Element: REF02; Notes: REF01=ALS Alternative List ID"}, "classOfContractCode": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=CE Class of Contract Code"}, "coverageListId": {"type": "string", "description": "Loop: 2110C, 2110D; Segments: REF; Element: REF02; Notes: REF01=CLI Coverage List ID"}, "contractNumber": {"type": "string", "description": "Loop: 2100B, 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=CT Contract Number"}, "medicalRecordIdentificationNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=EA Medical Record Identification Number"}, "electronicDevicePin": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=EL Electronic device pin number"}, "submitterIdentificationNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=EO Submitter Identification Number"}, "patientAccountNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=EJ Patient Account Number"}, "hicNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=F6 Health Insurance Claim Number"}, "drugFormularyNumber": {"type": "string", "description": "Loop: 2110C, 2110D; Segments: REF; Element: REF02; Notes: REF01=FO Drug Formulary Number"}, "priorAuthorizationNumber": {"type": "string", "description": "Loop: 2110C, 2110D; Segments: REF; Element: REF02; Notes: REF01=G1 Prior Authorization Number"}, "idCardSerialNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=GH Identification Card Serial Number"}, "idCardNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=HJ Identity Card Number"}, "centersForMedicareAndMedicaidServicesNPI": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=HPI Centers for Medicare and Medicaid Services NPI"}, "issueNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=IF Issue Number"}, "insurancePolicyNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=IG Insurance Policy Number"}, "userIdentification": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=JD User Identification"}, "medicalAssistanceCategory": {"type": "string", "description": "Loop: 2110C; Segments: REF; Element: REF02; Notes: REF01=M7 Medical Assistance Category"}, "eligibilityCategory": {"type": "string", "description": "Loop: 2100D; Segments: REF; Element: REF02; Notes: REF01=MRC Eligibility Category"}, "planNetworkIdNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=N6 Plan Network Identification Number"}, "planNetworkIdDescription": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF03; Notes: REF01=N6 Plan Network Identification Description"}, "facilityNetworkIdentificationNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=N7 Facility Network Identification Number"}, "medicaidRecipientIdNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=NQ Medicaid Recipient Identification Number"}, "priorIdNumber": {"type": "string", "description": "Loop: 2100B, 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=Q4 Prior Identifier Number"}, "socialSecurityNumber": {"type": "string", "description": "Loop: 2100B, 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=SY Social Security Number"}, "federalTaxpayersIdentificationNumber": {"type": "string", "description": "Loop: 2100B; Segments: REF; Element: REF02; Notes: REF01=TJ Federal Taxpayer's Identification Number"}, "agencyClaimNumber": {"type": "string", "description": "Loop: 2100C, 2100D; Segments: REF; Element: REF02; Notes: REF01=Y4 Agency Claim Number"}}, "description": "PlanInformation"}, "PlanStatus": {"type": "object", "properties": {"statusCode": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB01, Notes: Eligibility or Benefit Information Code"}, "status": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB01, Notes: Eligibility or Benefit Information Code Description"}, "planDetails": {"type": "string", "description": "Loop: 2110C and 2110D, Segments: EB, Element: EB05, Notes: Plan Coverage Description"}, "serviceTypeCodes": {"type": "array", "items": {"type": "string"}}}, "description": "PlanStatus - Deprecated please use benefitsInformation", "deprecated": true}, "ProviderInformation": {"type": "object", "properties": {"providerCode": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: PRV, Element: PRV01, Notes: providerCode"}, "referenceIdentification": {"type": "string", "description": "Loop: 2120C and 2120D, Segments: PRV, Element: PRV03, Notes: Benefit Related Entity Provider Taxonomy Code"}}, "description": "ProviderInformation"}, "Response": {"type": "object", "properties": {"meta": {"$ref": "#/components/schemas/MetaData"}, "controlNumber": {"type": "string", "description": "Segment: ISA, Element: ISA13, Notes: Interchange Control Number original request"}, "reassociationKey": {"type": "string", "description": "Segment: ISA, Element: ISA13, Notes: Interchange Control Number"}, "tradingPartnerServiceId": {"type": "string", "description": "This is the payorId or Identification Code that was sent in the 270"}, "provider": {"$ref": "#/components/schemas/ResponseProvider"}, "subscriber": {"$ref": "#/components/schemas/ResponseMember"}, "subscriberTraceNumbers": {"type": "array", "description": "Loop: 2100C and 2100D, Segment: TRN, Notes: Subscriber Trace Numbers", "items": {"$ref": "#/components/schemas/SubscriberTraceNumber"}}, "dependents": {"type": "array", "description": "Loop: 2100D, Notes: Dependent Details", "items": {"$ref": "#/components/schemas/ResponseMember"}}, "payer": {"$ref": "#/components/schemas/Payer"}, "planInformation": {"$ref": "#/components/schemas/PlanInformation"}, "planDateInformation": {"$ref": "#/components/schemas/PlanDateInformation"}, "planStatus": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: EB, Notes: Subscriber/Dependent Eligibility Benefit Information - Deprecated please use benefitsInformation", "deprecated": true, "items": {"$ref": "#/components/schemas/PlanStatus"}}, "benefitsInformation": {"type": "array", "description": "Loop: 2110C and 2110D, Segments: NM1, PER, PRV, N3, N4, EB, H SD, MSG, LS, LE, REF, DTP, Notes: Subscriber/Dependent Eligibility Benefit Information", "items": {"$ref": "#/components/schemas/BenefitsInformation"}}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}, "status": {"type": "string"}, "transactionSetAcknowledgement": {"type": "string"}, "implementationTransactionSetSyntaxError": {"type": "string"}, "x12": {"type": "string"}}}, "ResponseMember": {"type": "object", "properties": {"healthCareDiagnosisCodes": {"type": "array", "items": {"$ref": "#/components/schemas/HealthCareDiagnosisCode"}}, "memberId": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM109, Notes: NM108=MI Member Identification Number"}, "firstName": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM104, Notes: First Name"}, "lastName": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM103, Notes: Last Name"}, "middleName": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1 Element: NM105, Notes: Middle Name"}, "suffix": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM107, Notes: suffix"}, "gender": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: DMG, Element: DMG03, Notes: gender", "enum": ["M", "F"]}, "entityIdentifier": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM101, Notes: entityIdentifier"}, "entityType": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM102, Notes: entityType"}, "uniqueHealthIdentifier": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: NM1, Element: NM109, Notes: NM108=II uniqueHealthIdentifier"}, "dateOfBirth": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: DMG, Element: DMG02, Notes: Date of Birth"}, "informationStatusCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI01, Notes: Information Status Code"}, "employmentStatusCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI02, Notes: Employment Status Code"}, "governmentServiceAffiliationCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI03, Notes: Government Service Affiliation Code"}, "description": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI04, Notes: Description"}, "militaryServiceRankCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI05, Notes: Military Service Rank Code"}, "dateTimePeriodFormatQualifier": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI06, Notes: Date Time Period Format Qualifier"}, "dateTimePeriod": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI07, Notes: MPI06=D8 Date Time Period"}, "endDateTimePeriod": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI08, Notes: MPI06=RD8 Date Time Period"}, "startDateTimePeriod": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: MPI, Element: MPI08, Notes: MPI06=RD8 Date Time Period"}, "ssn": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: REF, Element: REF02, Notes: REF01=SY Social Security Number"}, "groupNumber": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: REF, Element: REF02, Notes: REF01=6P Group Number"}, "planNumber": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: REF, Element: REF02, Notes: REF01=18 Plan Number"}, "planNetworkIdNumber": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: REF, Element: REF02, Notes: REF01=N6 Plan Network Identification Number"}, "relationToSubscriber": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: INS, Element: INS02, Notes: Individual Relationship Code"}, "relationToSubscriberCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: INS, Element: INS02, Notes: Individual Relationship Code"}, "insuredIndicator": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: INS, Element: INS01, Notes: Insured Indicator"}, "maintenanceTypeCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: INS, Element: INS03, Notes: Maintenance Type Code"}, "maintenanceReasonCode": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: INS, Element: INS04, Notes: Maintenance Reason Code"}, "birthSequenceNumber": {"type": "string", "description": "Loop: 2000C, 2000D, 2100C, 2100D, Segment: INS, Element: INS17, Notes: Birth Sequence Number Use to indicate the birth order in the event of multiple births in association with the birth date supplied in DMG02"}, "address": {"$ref": "#/components/schemas/Address"}, "responseProvider": {"$ref": "#/components/schemas/ResponseProvider"}, "aaaErrors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}}, "description": "ResponseMember"}, "ResponseProvider": {"type": "object", "properties": {"providerName": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM103, Notes: Name Last"}, "providerFirstName": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM104, Notes: Name First"}, "providerOrgName": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM103, Notes: Organization Name"}, "middleName": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM105, Notes: Name Middle"}, "suffix": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM107, Notes: suffix"}, "entityIdentifier": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM101, Notes: Entity Identifier Code"}, "entityType": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM102, Notes: Entity Type Qualifier"}, "npi": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=XX Centers for Medicare and Medicaid Services National Provider Identifier"}, "providerCode": {"type": "string", "description": "Loop: 2100B, 2100C and 2100D, Segment: PRV, Element: PRV01, Notes: Provider Code"}, "referenceIdentification": {"type": "string", "description": "Loop: 2100C, 2100C and 2100D, Segment: PRV, Element: PRV03, Notes: PRV02=PXC Reference Identification"}, "employersId": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=24 Employer's Identification Number"}, "ssn": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=34 Social Security Number"}, "federalTaxpayersIdNumber": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=FI Federal Taxpayer's Identification Number"}, "payorIdentification": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=PI Payor Identification"}, "pharmacyProcessorNumber": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=PP Pharmacy Processor Number"}, "serviceProviderNumber": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=SV Service Provider Number"}, "servicesPlanID": {"type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: NM108=XV Centers for Medicare and Medicaid Services PlanID"}, "address": {"$ref": "#/components/schemas/Address"}, "aaaErrors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}}, "description": "Provider"}, "SubscriberTraceNumber": {"type": "object", "properties": {"traceTypeCode": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: TRN, Element: TRN01, Notes: Trace Type Code"}, "traceType": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: TRN, Element: TRN01, Notes: Trace Type"}, "referenceIdentification": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: TRN, Element: TRN02, Notes: Reference Identification"}, "originatingCompanyIdentifier": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: TRN, Element: TRN03, Notes: Originating Company Identifier"}, "secondaryReferenceIdentification": {"type": "string", "description": "Loop: 2100C and 2100D, Segment: TRN, Element: TRN04, Notes: Originating Company Identifier"}}, "description": "SubscriberTraceNumber"}, "RawX12Request": {"type": "object", "properties": {"x12": {"type": "string"}}}, "HealthCheck": {"type": "object", "properties": {"status": {"type": "string", "description": "Health Status"}, "version": {"type": "string", "description": "API version"}}}}, "securitySchemes": {"bearer-key": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "x-readme": {"explorer-enabled": true, "proxy-enabled": true, "samples-enabled": true}}