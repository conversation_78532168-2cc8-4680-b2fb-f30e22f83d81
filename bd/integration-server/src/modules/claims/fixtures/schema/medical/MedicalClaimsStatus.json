{"Request": {"required": ["controlNumber", "providers", "subscriber"], "type": "object", "properties": {"controlNumber": {"maxLength": 9, "minLength": 9, "type": "string", "description": "Header, Segment: ST02 (no loop), Notes: Transaction Set Control Number", "example": "123456789"}, "tradingPartnerName": {"type": "string", "description": "Loop: 2100A, Segment: NM1, Element: NM103, Notes: organizational name"}, "tradingPartnerServiceId": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100A Segment: NM1, Element: NM109, Notes: we send this as MN108 as PI = Payer Identification", "example": "serviceId"}, "providers": {"maxItems": **********, "minItems": 1, "type": "array", "description": "Loop: 2100B, Loop: 2100C", "items": {"required": ["providerType"], "type": "object", "properties": {"organizationName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Segment: NM1, Element: NM103, Notes: Provider’s organization name. Can use organization or last name.", "example": "TestProvider"}, "firstName": {"maxLength": 35, "minLength": 1, "type": "string", "description": "Segment: NM1, Element: NM104, Notes: Maps to providerName"}, "lastName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Segment: NM1, Element: NM103, Notes: Provider last name. Can use organization or last name."}, "npi": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: National Provider Identification. Maps to provider NPI. NM108=XX"}, "spn": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: Service Provider Number. Maps to provider SPN. NM108=SV"}, "tin": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: Taxpayer’s Identification Number. Maps to provider TIN. NM108=FI"}, "taxId": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: Taxpayer’s Identification Number used when ProviderType = BillingProvider", "example": "**********"}, "etin": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: Electronic transmitter identification Number (ETIN) used when ProviderType = BillingProvider"}, "providerType": {"pattern": "BillingProvider|ServiceProvider", "type": "string", "description": "Segment: NM1, Notes: Code for entity (Billing or Service) Billing Provider: Loop 2100B, NM101=41 (required), Service Provider: Loop 2100C, NM101=1P if not present it is added and assigned tin = BillingProvider taxId", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "ServiceProvider"]}}, "description": "Loop: 2100B, Loop: 2100C"}}, "subscriber": {"required": ["firstName", "lastName", "memberId"], "type": "object", "properties": {"memberId": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100D, Segment: NM1, Element: NM109, Notes: The subscriber's insurance member ID. Maps to subscriberId.", "example": "**********"}, "firstName": {"maxLength": 35, "minLength": 1, "type": "string", "description": "Loop: 2100D, Segment: NM1, Element: NM104, Notes: The subscriber's first name as specified on their policy", "example": "johnone"}, "lastName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Loop: 2100D, Segment: NM1, Element: NM103, Notes: The subscriber's last name as specified on their policy.", "example": "doeone"}, "gender": {"pattern": "F|M|U", "type": "string", "description": "Loop: 2000D, 2000E, Segment: DMG, Element: DMG03, Notes: The subscriber's gender as specified on their policy.", "example": "M", "enum": ["M", "F", "U"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2000D, Segment: DMG, Element: DMG02, Notes: The birth date as specified on the policy for the subscriber. Maps to BirthDate", "example": "18800102"}, "groupNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D, Segment: REF, Element: REF01, Notes: The group number associated with the subscriber.", "example": "**********"}}, "description": "Loop: 2000D"}, "dependent": {"required": ["firstName", "lastName"], "type": "object", "properties": {"firstName": {"maxLength": 35, "minLength": 1, "type": "string", "description": "Loop: 2100E, Segment: NM1, Element: NM104, Notes: The dependent's first name as specified on their policy.", "example": "jane<PERSON>"}, "lastName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Loop: 2100E, Segment: NM1, Element: NM103, Notes: The dependent's last name as specified on their policy.", "example": "doeone"}, "gender": {"pattern": "F|M|U", "type": "string", "description": "Loop: 2000E, Segment: DMG, Element: DMG03, Notes: dependent's gender as specified on their policy.", "example": "F", "enum": ["M", "F", "U"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2000E, Segment: DMG, Element: DMG02, Notes: The birth date as specified on the policy for the dependent.", "example": "18800101"}, "groupNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200E, Segment: REF, Element: REF02 when REF01=6P, Notes: The group number associated with the subscriber and dependent. Maps to dependent groupNumber.", "example": "**********"}}, "description": "Loop: 2000E"}, "encounter": {"type": "object", "properties": {"beginningDateOfService": {"type": "string", "description": "Loop: 2200D or 2200E, Segment: DTP, Element: DTP03, Notes: Date Time Period - Start Date", "example": "20100101"}, "endDateOfService": {"type": "string", "description": "Loop: 2200D or 2200E, Segment: DTP, Element: DTP03, Notes: Date Time Period - End Date", "example": "20100102"}, "trackingNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: TRN, Element: TRN02, Notes: This is the claim status tracking number assigned to the status query for the claim.", "example": "ABCD"}, "tradingPartnerClaimNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: This is the payer assigned claim number. descriptions goes in REF02 where REF01=1K"}, "locationIdentifier": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Required if application or location system identifier is known. descriptions goes in REF02 where REF01=LU"}, "billingType": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Billing type reference ID. Example: billing type for inpatient services is 111. descriptions goes in REF02 where REF01=BLT"}, "patientAccountNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Patient account number provided by service provider. description goes in REF02 where REF01=EJ"}, "pharmacyPrescriptionNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Patient pharmacy prescription number. description goes in REF02 where REF01=XZ"}, "clearingHouseClaimNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Claim number provided by clearing house. description goes in REF02 where REF01=D9"}, "submittedAmount": {"maxLength": 18, "minLength": 1, "type": "string", "description": "Loop: 2200D or 2200E, Segment: AMT, Element: AMT02, Notes: Submitted total charges. description goes in AMT02 where AMT01=T3"}}, "description": "Loop: 2200D or 2200E"}, "serviceLineInformation": {"type": "object", "properties": {"productOrServiceIDQualifier": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-1, Notes: Allowed Values are 'AD' American Dental Association Codes, 'ER' Jurisdiction Specific Procedure and Supply Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'HP' Health Insurance Prospective Payment System (HIPPS) Skilled Nursing Facility Rate Code, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code, 'N4' National Drug Code in 5-4-2 Format, 'NU' National Uniform Billing Committee (NUBC) UB92 Codes, 'WK' Advanced Billing Concepts (ABC) Codes", "enum": ["AD", "ER", "HC", "HP", "IV", "N4", "NU", "WK"]}, "procedureCode": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-2, Notes: Procedure Code maps to Product/ServiceID"}, "procedureModifiers": {"maxItems": 4, "minItems": 0, "type": "array", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-3 to SVC01-6, Notes:  Procedure Modifier", "items": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-3 to SVC01-6, Notes:  Procedure Modifier"}}, "lineItemChargeAmount": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC02, Notes: Maps to Monetary Amount, This amount is the original submitted charge."}, "revenueCode": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC04, Notes: Identifying number for a product or service"}, "unitsOfServiceCount": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: SVC, Element: SVC07, Notes: Maps to Quantity, Numeric value of quantity accepted"}, "lineItemControlNumber": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: REF, Element: REF02 when REF01 = FJ, Notes: Maps to  Reference Identification"}, "serviceLineDate": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: DTP, Element: DTP03, Notes: When sent with serviceLineEndDate it will be used as the start date for Date Time period, if sent without serviceLineEndDate will use DTP02 = D8"}, "serviceLineEndDate": {"type": "string", "description": "Loop: 2210D, 2210E, Segment: DTP, Element: DTP03, Notes: DTP03 enddate and DTP02=RD8"}}, "description": "Loop: 2220D or 2220E"}}}, "Response": {"type": "object", "properties": {"controlNumber": {"type": "string", "description": "Provided by the submitter in the 270 Request. Transaction Set Control Number."}, "tradingPartnerServiceId": {"type": "string", "description": "ID used by Clearing House for the trading partner."}, "payer": {"type": "object", "properties": {"organizationName": {"type": "string", "description": "Segment: NM1, Element: NM103, Notes: Payers’s organization name.", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "payerIdentification": {"type": "string", "description": "Segment: NM1, Element: NM109, Notes: Payers’s identification number established through trading partner agreement. NM108=PI", "example": "12345"}, "centersForMedicareAndMedicaidServicePlanId": {"type": "string", "description": "Segment: NM1, Element: NM109, Notes: Use when reporting Health Plan ID (HPID) or Other Entity Identifier (OEID). NM108=XV", "example": "12345"}, "contactInformation": {"type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02, Notes: Payer’s name.", "example": "TEST PAYER DEPARTMENT"}, "electronicDataInterChangeAccessNumber": {"type": "string", "description": "Segment: PER, Element: PER04, PER06 or PER08, Notes: Payer's electronic data interchange access number. PER03=PER05=PER07=ED", "example": "111111"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04, PER06 or PER08, Notes: Payer's email. PER03=PER05=PER07=EM", "example": "<EMAIL>"}, "fax": {"type": "string", "description": "Segment: PER, Element: PER04, PER06 or PER08, Notes: Payer's facsimile number. PER03=PER05=PER07=FX", "example": "************"}, "phone": {"type": "string", "description": "Segment: PER, Element: PER04, PER06 or PER08, Notes: Payer's telephone number. PER03=PER05=PER07=TE", "example": "************"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER04, PER06 or PER08, Notes: Payer's telephone extension. PER05=PER07=EX", "example": "0000"}}, "description": "Segment: PER"}}, "description": "Loop: 2100A"}, "providers": {"type": "array", "description": "Loop: 2100B and 2100C", "items": {"required": ["providerType"], "type": "object", "properties": {"organizationName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Segment: NM1, Element: NM103, Notes: Provider’s organization name. Can use organization or last name.", "example": "TestProvider"}, "firstName": {"maxLength": 35, "minLength": 1, "type": "string", "description": "Segment: NM1, Element: NM104, Notes: Maps to providerName"}, "lastName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Segment: NM1, Element: NM103, Notes: Provider last name. Can use organization or last name."}, "npi": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: National Provider Identification. Maps to provider NPI. NM108=XX"}, "spn": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: Service Provider Number. Maps to provider SPN. NM108=SV"}, "tin": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: Taxpayer’s Identification Number. Maps to provider TIN. NM108=FI"}, "taxId": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: Taxpayer’s Identification Number used when ProviderType = BillingProvider", "example": "**********"}, "etin": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100B, Segment: NM1, Element: NM109, Notes: Electronic transmitter identification Number (ETIN) used when ProviderType = BillingProvider"}, "providerType": {"pattern": "BillingProvider|ServiceProvider", "type": "string", "description": "Segment: NM1, Notes: Code for entity (Billing or Service) Billing Provider: Loop 2100B, NM101=41 (required), Service Provider: Loop 2100C, NM101=1P if not present it is added and assigned tin = BillingProvider taxId", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "ServiceProvider"]}}, "description": "Loop: 2100B, Loop: 2100C"}}, "subscriber": {"required": ["firstName", "lastName", "memberId"], "type": "object", "properties": {"memberId": {"maxLength": 80, "minLength": 2, "type": "string", "description": "Loop: 2100D, Segment: NM1, Element: NM109, Notes: The subscriber's insurance member ID. Maps to subscriberId.", "example": "**********"}, "firstName": {"maxLength": 35, "minLength": 1, "type": "string", "description": "Loop: 2100D, Segment: NM1, Element: NM104, Notes: The subscriber's first name as specified on their policy", "example": "johnone"}, "lastName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Loop: 2100D, Segment: NM1, Element: NM103, Notes: The subscriber's last name as specified on their policy.", "example": "doeone"}, "gender": {"pattern": "F|M|U", "type": "string", "description": "Loop: 2000D, 2000E, Segment: DMG, Element: DMG03, Notes: The subscriber's gender as specified on their policy.", "example": "M", "enum": ["M", "F", "U"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2000D, Segment: DMG, Element: DMG02, Notes: The birth date as specified on the policy for the subscriber. Maps to BirthDate", "example": "18800102"}, "groupNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200D, Segment: REF, Element: REF01, Notes: The group number associated with the subscriber.", "example": "**********"}}, "description": "Loop: 2000D"}, "dependent": {"required": ["firstName", "lastName"], "type": "object", "properties": {"firstName": {"maxLength": 35, "minLength": 1, "type": "string", "description": "Loop: 2100E, Segment: NM1, Element: NM104, Notes: The dependent's first name as specified on their policy.", "example": "jane<PERSON>"}, "lastName": {"maxLength": 60, "minLength": 1, "type": "string", "description": "Loop: 2100E, Segment: NM1, Element: NM103, Notes: The dependent's last name as specified on their policy.", "example": "doeone"}, "gender": {"pattern": "F|M|U", "type": "string", "description": "Loop: 2000E, Segment: DMG, Element: DMG03, Notes: dependent's gender as specified on their policy.", "example": "F", "enum": ["M", "F", "U"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2000E, Segment: DMG, Element: DMG02, Notes: The birth date as specified on the policy for the dependent.", "example": "18800101"}, "groupNumber": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Loop: 2200E, Segment: REF, Element: REF02 when REF01=6P, Notes: The group number associated with the subscriber and dependent. Maps to dependent groupNumber.", "example": "**********"}}, "description": "Loop: 2000E"}, "claims": {"type": "array", "description": "Loop: 2200D and 2200E", "items": {"type": "object", "properties": {"claimStatus": {"type": "object", "properties": {"statusCategoryCode": {"type": "string", "description": "Segment: STC, Element: STC01-1, Notes: Status Category Code"}, "statusCategoryCodeValue": {"type": "string", "description": "Segment: STC, Element: STC01-1, Notes: Status Category Code value"}, "statusCode": {"type": "string", "description": "Segment: STC, Element: STC01-2, Notes: Status Code"}, "statusCodeValue": {"type": "string", "description": "Segment: STC, Element: STC01-2, Notes: Status Code value"}, "entityCode": {"type": "string", "description": "Segment: STC, Element: STC01-3, Notes: Entity Code"}, "entity": {"type": "string", "description": "Segment: STC, Element: STC01-3, Notes: Entity Code value"}, "effectiveDate": {"type": "string", "description": "Segment: STC, Element: STC02, Notes: Effective Date"}, "submittedAmount": {"type": "string", "description": "Segment: STC, Element: STC04, Notes: Total Claim Charge Amount"}, "amountPaid": {"type": "string", "description": "Segment: STC, Element: STC05, Notes: Claim Payment Amount"}, "paidDate": {"type": "string", "description": "Segment: STC, Element: STC06, Notes: Adjudication Finalized Date"}, "checkIssueDate": {"type": "string", "description": "Segment: STC, Element: STC08, Notes: Remittance Date"}, "checkNumber": {"type": "string", "description": "Segment: STC, Element: STC09, Notes: Remittance Trace Number"}, "trackingNumber": {"type": "string", "description": "Segment: TRN, Element: TRN02, Notes: Tracking Number"}, "claimServiceDate": {"type": "string", "description": "Segment: DTP, Element: DTP03, Notes: Claim Service Period"}, "tradingPartnerClaimNumber": {"type": "string", "description": "Segment: REF, Element: REF02, Notes: REF01 for this will be 1K"}, "patientAccountNumber": {"type": "string", "description": "Segment: REF, Element: REF02, Notes: REF01 for this will be EJ"}, "clearingHouseClaimNumber": {"type": "string", "description": "Segment: REF, Element: REF02, Notes: REF01 for this will be D9"}}, "description": "Loop: 2200D, 2200E"}, "serviceDetails": {"type": "array", "description": "Loop: 2220D, 2220E", "items": {"type": "object", "properties": {"service": {"type": "object", "properties": {"serviceIdQualifierCode": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC01-1, Notes: Code identifying the type/source of the descriptive number used in Product/Service ID. Code Example: AD, Allowed Values are 'AD' American Dental Association Codes, 'ER' Jurisdiction Specific Procedure and Supply Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'HP' Health Insurance Prospective Payment System (HIPPS) Skilled Nursing Facility Rate Code, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code, 'N4' National Drug Code in 5-4-2 Format, 'NU' National Uniform Billing Committee (NUBC) UB92 Codes, 'WK' Advanced Billing Concepts (ABC) Codes", "enum": ["AD", "ER", "HC", "HP", "IV", "N4", "NU", "WK"]}, "serviceIdQualifier": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC01-1, Notes: description associated with the service code. Example: American Dental Association Codes"}, "procedureId": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC01-2, Notes: Identifying number for a product or service."}, "submittedAmount": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC02, Notes: Amount submitted for the service. This is the line item total on the current claim service status."}, "amountPaid": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC03, Notes: Amount paid for the service."}, "revenueCode": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC04, Notes: National uniform billing committee revenue code."}, "submittedUnits": {"type": "string", "description": "Loop: 2220D, 2220E, Segment: SVC, Element: SVC07, Notes: Original submitted units of service."}}, "description": "Loop: 2220D, 2220E, Segment: SVC, Notes: Service Line Information"}, "status": {"type": "array", "description": "Loop: 2220D, 2220E, Segment: STC, Notes: Service Line Status Information", "items": {"type": "object", "properties": {"statusCategoryCode": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element: STC01-1, Notes: The health care claim status category code. Example: F3"}, "statusCategoryCodeValue": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element: STC01-1, Notes: Value of the category code. F3 = Finalized/Revised - Adjudication information has been changed"}, "statusCode": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element: STC01-2, Notes: Status code used to identify the status of an entire claim or a service line. Example: 3"}, "statusCodeValue": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element: STC01-2, Notes: Status Code value. 3 = Claim has been adjudicated and is awaiting payment cycle."}, "entityCode": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element STC01-3, Notes:Code for the entity. Example: 2P"}, "entity": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element STC01-3, Notes: Value of the entity code. Example: 2P=Public Health Service Facility"}, "effectiveDate": {"type": "string", "description": "Loop: 2220D and 2220E, Segment: STC, Element: STC02, Notes: Effective date of the status information. Date the service was placed in this status by the information source’s adjudication process. Date or Date range, format YYYYMMDD"}}, "description": "Loop: 2220D, 2220E, Segment: STC, Notes: Service Line Status Information"}}}, "description": "Loop: 2220D, 2220E"}}}, "description": "Loop: 2200D and 2200E"}}, "reassociationKey": {"type": "string", "description": "Segment: ISA, Element: ISA13, Notes: Interchange control number"}, "errorResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Code for the error.", "example": "INVALIDMISSINGINPUTDATA"}, "description": {"type": "string", "description": "Description of the error code. Value provided in payer id should be a valid Optum assigned ERA payer Id."}, "transactionIdentifier": {"type": "object", "properties": {"customerTransactionId": {"type": "string", "description": "The number assigned by the originator to identify the transaction within the originator's business application system"}, "transactionId": {"type": "string", "description": "The number assigned by the originator to identify the transaction within the originator's business application system"}}}, "errors": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string", "description": "Attribute that is bad"}, "value": {"type": "string", "description": "Value of bad attribute"}, "code": {"type": "string", "description": "Code for the error. Example: INVALID_VALUE"}, "description": {"type": "string", "description": "Description of the error code. Value provided in payer id should be a valid Optum assigned ERA payer ID"}, "location": {"type": "string", "description": "Segment/location where the error occurred. If this is a network/system error, there is no location attribute. Example: $.payerBenefits[0].payer"}, "followupAction": {"type": "string", "description": "Any follow action required for the error. For AAA errors, this would be value for AAA04"}}}}}}, "status": {"type": "string", "description": "Loop: 2200D, Segment: STC, Element: STC01, Health care claim status, Used to convey status of the entire claim or specific service line."}, "transactionSetAcknowledgement": {"type": "string", "description": "The 999 acknowledgment code"}, "implementationTransactionSetSyntaxError": {"type": "string", "description": "The 999 syntax error code"}, "x12": {"type": "string", "description": "Basic x12 edi response (for x12 endpoint)"}, "meta": {"type": "object", "properties": {"submitterId": {"type": "string", "description": "submitterId assigned to this request"}, "senderId": {"type": "string", "description": "senderId assigned to this request"}, "billerId": {"type": "string", "description": "billerId assigned to this request"}, "traceId": {"type": "string", "description": "Unique Id assigned to each request by Optum"}, "applicationMode": {"type": "string", "description": "Used by Optum to identify where this request can be found for support"}}, "description": "Meta data about the request"}}, "description": "Response"}}