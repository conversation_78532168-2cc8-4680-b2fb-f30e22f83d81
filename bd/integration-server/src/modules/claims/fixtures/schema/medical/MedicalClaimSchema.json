{"required": ["billing", "claimInformation", "controlNumber", "receiver", "submitter", "subscriber"], "type": "object", "properties": {"controlNumber": {"type": "string", "description": "Header, Segment: ST02 (no loop), Notes: Transaction Set Control Number", "example": "000000001"}, "tradingPartnerServiceId": {"type": "string", "description": "Loop: 2010BB Segment: NM1, Element: NM109, Notes: we send this as MN108 as PI = Payer Identification", "example": "9496"}, "submitter": {"required": ["contactInformation"], "type": "object", "properties": {"organizationName": {"type": "string", "description": "Loop: 1000A,  Segment: NM1, Element: NM103", "example": "REGIONAL PPO NETWORK"}, "lastName": {"type": "string", "description": "Loop: 1000A,  Segment: NM1, Element: NM103", "example": "doeOne"}, "firstName": {"type": "string", "description": "Loop: 1000A,  Segment: NM1, Element: NM104", "example": "jane<PERSON>"}, "middleName": {"type": "string", "description": "Loop: 1000A,  Segment: NM1, Element: NM105", "example": "middleone"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 1000A"}, "receiver": {"required": ["organizationName"], "type": "object", "properties": {"organizationName": {"type": "string", "description": "Loop: 1000B, Segment: NM1, Element: NM103", "example": "EXTRA HEALTHY INSURANCE"}}, "description": "Loop: 1000B"}, "subscriber": {"required": ["paymentResponsibilityLevelCode"], "type": "object", "properties": {"memberId": {"type": "string", "description": "Loop: 2010BA,  Segment: NM1, Element: NM109", "example": "**********"}, "ssn": {"type": "string", "description": "Loop: 2010BA,  Segment: REF, Element: REF02 when REF01=SY"}, "paymentResponsibilityLevelCode": {"type": "string", "description": "Loop: 2000B, Segment: SBR, Element: SBR01, Allowed Values:'A' Payer Responsibility Four 'B' Payer Responsibility Five 'C' Payer Responsibility Six 'D' Payer Responsibility Seven 'E' Payer Responsibility Eight 'F' Payer Responsibility Nine 'G' Payer Responsibility Ten 'H' Payer Responsibility Eleven 'P' Primary 'S' Secondary 'T' Tertiary 'U' Unknown", "example": "P", "enum": ["A", "B", "C", "D", "E", "F", "G", "H", "P", "S", "T", "U"]}, "organizationName": {"type": "string", "description": "Loop: 2010BA, Segment: NM1, Element: NM103 when NM102=2, Notes: when subscriber is organization pass patient as dependent"}, "insuranceTypeCode": {"type": "string", "description": "Loop: 2000B,  Segment: SBR, Element:SBR05 Notes: Allowed values: '12' Medicare Secondary Working Aged Beneficiary or Spouse with Employer Group Health Plan, '13' Medicare Secondary End-Stage Renal Disease Beneficiary in the Mandated Coordination Period with an Employer's Group Health Plan, '14' Medicare Secondary, No-fault Insurance including Auto is Primary, '15' Medicare Secondary Worker's Compensation, '16' Medicare Secondary Public Health Service (PHS)or Other Federal Agency, '41' Medicare Secondary Black Lung, '42' Medicare Secondary Veteran's Administration, '43' Medicare Secondary Disabled Beneficiary Under Age 65 with Large Group Health Plan (LGHP), '47' Medicare Secondary, Other Liability Insurance is Primary", "enum": ["12", "13", "14", "15", "16", "41", "42", "43", "47"]}, "subscriberGroupName": {"type": "string", "description": "Loop: 2000B,  Segment: SBR, Element: SBR04 Notes: Freeform text", "example": "Subscriber Group Name"}, "firstName": {"type": "string", "description": "Loop: 2010BA,  Segment: NM1, Element: NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "Loop: 2010BA,  Segment: NM1, Element: NM103", "example": "doeOne"}, "middleName": {"type": "string", "description": "Loop: 2010BA,  Segment: NM, Element: NM105"}, "suffix": {"type": "string", "description": "Loop: 2010BA,  Segment: NM, Element: NM107", "example": "<PERSON>"}, "gender": {"type": "string", "description": "Loop: 2010BA,  Segment: DMG, Element: DMG03 Subscriber Gender, Notes: 'M' Male, 'F' Female'U' Unknown", "example": "M", "enum": ["M", "F", "U"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2010BA,  Segment: DMG, Element: DMG02", "example": "19800102"}, "policyNumber": {"type": "string", "description": "Loop: 2000B,  Segment: SBR, Element: SBR03", "example": "00001"}, "groupNumber": {"type": "string", "description": "Loop: 2010BA,  Segment: SBR, Element: SBR04"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}}, "description": "Loop: 2000B"}, "dependent": {"required": ["dateOfBirth", "firstName", "gender", "lastName", "relationshipToSubscriberCode"], "type": "object", "properties": {"firstName": {"type": "string", "description": "Loop: 2010CA, Segment: NM1, Element: NM104", "example": "jane<PERSON>"}, "lastName": {"type": "string", "description": "Loop: 2010CA, Segment: NM1, Element: NM103", "example": "doeOne"}, "middleName": {"type": "string", "description": "Loop: 2010CA, Segment: NM1, Element: NM105"}, "suffix": {"type": "string", "description": "Loop: 2010CA,  Segment: NM, Element: NM107", "example": "<PERSON>"}, "gender": {"type": "string", "description": "Loop: 2010CA, Segment: DMG, Element: DMG03, Note: Allowed Values are: 'M' Male, 'F' Female, 'U' Unknown", "example": "F", "enum": ["M", "F", "U"]}, "dateOfBirth": {"type": "string", "description": "Loop: 2010CA, Segment: DMG, Element: DMG02 when DMG01=D8", "example": "19800102"}, "ssn": {"type": "string", "description": "Loop: 2010CA, Segment: REF, Element: REF02 when REF01=SY", "example": "19800102"}, "memberId": {"type": "string", "description": "Loop: 2010CA, Segment: REF, Element: REF02 when REF01=1W", "example": "19800102"}, "relationshipToSubscriberCode": {"type": "string", "description": "Loop: 2000C, Segment: PAT, Element: PAT01, Note: Allowed Values are: '01' Spouse, '19' Child, '20' Employee, '21'  Unknown, '39'  <PERSON> Donor, '40'  <PERSON><PERSON>ver Donor, '53'  Life Partner, 'G8' Other Relationship", "example": "01", "enum": ["01", "19", "20", "39", "40", "53", "G8"]}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}}, "description": "LOOP 2000C"}, "providers": {"maxItems": **********, "minItems": 1, "type": "array", "description": "setting providers deprecated, please set all providers individually by it's type.", "deprecated": true, "items": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 2420D"}}, "claimInformation": {"required": ["benefitsAssignmentCertificationIndicator", "claimChargeAmount", "claimFilingCode", "claimFrequencyCode", "healthCareCodeInformation", "patientControlNumber", "placeOfServiceCode", "planParticipationCode", "releaseInformationCode", "serviceLines", "signatureIndicator"], "type": "object", "properties": {"claimFilingCode": {"type": "string", "description": "Loop 2000B, Segment: SBR, Element: SBR09, Note: Allowed Values are: '11' Other Non-Federal Programs, '12' Preferred Provider Organization (PPO), '13' Point of Service (POS), '14' Exclusive Provider Organization (EPO), '15' Indemnity Insurance, '16' Health Maintenance Organization (HMO) Medicare Risk, '17' Dental Maintenance Organization, 'AM' Automobile Medical, 'BL' Blue Cross/Blue Shield, 'CH' Champus, 'CI' Commercial Insurance Co., 'DS' Disability, 'FI' Federal Employees Program, 'HM' Health Maintenance Organization, 'LM' Liability Medical, 'MA' Medicare Part A,  'MB' Medicare Part B,  'MC' Medicaid, 'OF' Other Federal Program, 'TV' Title V, 'VA' Veterans Affairs Plan, 'WC' Workers' Compensation Health Claim, 'ZZ' Mutually Defined", "example": "CI", "enum": ["11", "12", "13", "14", "15", "16", "17", "AM", "BL", "CH", "CI", "DS", "FI", "HM", "LM", "MA", "MB", "MC", "OF", "TV", "VA", "WC", "ZZ"]}, "propertyCasualtyClaimNumber": {"type": "string", "description": "Loop 2010BA, Segment: REF, Element: REF02"}, "deathDate": {"type": "string", "description": "Loop 2000B and 2000C, Segment: PAT, Element: PAT06 and PAT05=D8"}, "patientWeight": {"type": "string", "description": "Loop 2000B and 2000C, Segment: PAT, Element: PAT08 and PAT07=01"}, "pregnancyIndicator": {"type": "string", "description": "Loop 2000B and 2000C, Segment: PAT, Element: PAT09", "enum": ["Y"]}, "patientControlNumber": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM01", "example": "12345"}, "claimChargeAmount": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM02", "example": "28.75"}, "placeOfServiceCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM05-01", "example": "11"}, "claimFrequencyCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM05-03", "example": "1"}, "signatureIndicator": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM06, Note: Allowed Values are: 'N' NO, 'Y' Yes", "example": "Y", "enum": ["N", "Y"]}, "planParticipationCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM07, Note: Allowed Values are: 'A' Assigned, 'B' Assignment Accepted on Clinical Lab Services Only, 'C' Not Assigned", "example": "A", "enum": ["A", "B", "C"]}, "benefitsAssignmentCertificationIndicator": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM08, Note: Allowed Values are: 'N' No, 'W' Not Applicable - Use code 'W' when the patient refuses to assign benefits, 'Y' Yes", "example": "Y", "enum": ["N", "W", "Y"]}, "releaseInformationCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM09, Note: Allowed Values are: 'I' Informed Consent to Release Medical Information for Conditions or Diagnoses Regulated by Federal Statutes, 'Y' Yes", "example": "Y", "enum": ["I", "Y"]}, "patientSignatureSourceCode": {"type": "boolean", "description": "Loop 2300, Segment: CLM, Element: CLM10, Note: Allowed Values are: 'P' Signature generated by provider because the patient was not physically present for services", "enum": [false]}, "relatedCausesCode": {"maxItems": 2, "minItems": 0, "type": "array", "description": "Loop 2300, Segment: CLM, Element: CLM11-01, CLM11-02, Note: Allowed Values are: 'AA' Auto Accident, 'EM' Employment, 'OA' Other Accident", "items": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM11-01, CLM11-02, Note: Allowed Values are: 'AA' Auto Accident, 'EM' Employment, 'OA' Other Accident", "enum": ["AA", "EM", "OA"]}}, "autoAccidentStateCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM11-04, Note: When CLM11-1 or CLM11-2 has a value of 'AA' to identify the state, province or sub-country code in which the automobile accident occurred.", "enum": ["AA", "EM", "OA"]}, "autoAccidentCountryCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM11-05, Note: When CLM11-1 or CLM11-2 = AA and the accident occurred in a country other than US or Canada."}, "specialProgramCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM12, Note: Allowed Values are: '02' Physically Handicapped Children's Program, '03' Special Federal Funding, '05' Disabolity, '09' Second Opinion or Surgery", "enum": ["02", "03", "05", "09"]}, "delayReasonCode": {"type": "string", "description": "Loop 2300, Segment: CLM, Element: CLM20, Note: Allowed Values are: '1' Proof of Eligibility Unknown or Unavailable, '2' Litigation, '3' Authorization Delays, '4' Delay in Certifying Provider, '5' Delay in Supplying Billing Forms, '6' Delay in Delivery of Custom-made Appliances, '7' Third Party Processing Delay, '8' Delay in Eligibility Determination,  '9' Original Claim Rejected or Denied Due to a Reason Unrelated to the Billing Limitation Rules, '10' Administration Delay in the Prior Approval Process, '11' Other, '15' Natural Disaster", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "15"]}, "patientAmountPaid": {"type": "string", "description": "Loop 2300, Segment: AMT, Element: AMT02"}, "fileInformation": {"type": "string", "description": "Loop 2300, Segment: K3, Element: K301"}, "fileInformationList": {"maxItems": 10, "minItems": 0, "type": "array", "description": "Loop 2300, Segment: K3, Element: K301", "items": {"type": "string", "description": "Loop 2300, Segment: K3, Element: K301"}}, "claimDateInformation": {"type": "object", "properties": {"symptomDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "initialTreatmentDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "lastSeenDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "acuteManifestationDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "accidentDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "lastMenstrualPeriodDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "lastXRayDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "hearingAndVisionPrescriptionDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "disabilityBeginDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "disabilityEndDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "lastWorkedDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "authorizedReturnToWorkDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "admissionDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "dischargeDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "assumedAndRelinquishedCareBeginDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "assumedAndRelinquishedCareEndDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "repricerReceivedDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}, "firstContactDate": {"type": "string", "description": "Loop: 2300, Segment: DTP, Element: DTP03"}}, "description": "DTP"}, "claimContractInformation": {"required": ["contractTypeCode"], "type": "object", "properties": {"contractTypeCode": {"type": "string", "description": "Loop: 2300,Segment: CN1, Element: CN101, Note: Allowed Values are: '01' Diagnosis Related Group (DRG), '02' Per Diem, '03' Variable Per Diem, '04' Flat, '05' Capitated, '06' Percent, '09' Other", "enum": ["01", "02", "03", "04", "05", "06", "09"]}, "contractAmount": {"type": "string", "description": "Loop: 2300, Segment: CN1, Element: CN102"}, "contractPercentage": {"type": "string", "description": "Loop: 2300, Segment: CN1, Element: CN103"}, "contractCode": {"type": "string", "description": "Loop: 2300, Segment: CN1, Element: CN104"}, "termsDiscountPercentage": {"type": "string", "description": "Loop: 2300, Segment: CN1, Element: CN105"}, "contractVersionIdentifier": {"type": "string", "description": "Loop: 2300, Segment: CN1, Element: CN106"}}, "description": "Loop 2300, Segment: CN1"}, "claimSupplementalInformation": {"type": "object", "properties": {"reportInformation": {"type": "object", "properties": {"attachmentReportTypeCode": {"type": "string", "description": "Loop: 2400, Segment: PWK, Element: PWK01, Notes: Allowed Values are: '03' Report Justifying Treatment Beyond Utilization Guidelines, '04' Drugs Administered, '05' Treatment Diagnosis, '06' Initial Assessment, '07' Functional Goals, '08' Plan of Treatment, '09' Progress Report, '10' Continued Treatment, '11' Chemical Analysis, '13' Certified Test Report, '15' Justification for Admission, '21' Recovery Plan, 'A3' Allergies/Sensitivities Document, 'A4' Autopsy Report, 'AM' Ambulance Certification, 'AS' Admission Summary, 'B2' Prescription, 'B3' Physician Order, 'B4' Referral Form, 'BR' Benchmark Testing Results, 'BS' Baseline, 'BT' Blanket Test Results, 'CB' Chiropractic Justification, 'CK' Consent Form(s), 'CT' Certification, 'D2' Drug Profile Document, 'DA' Dental Models, 'DB' Durable Medical Equipment Prescription, 'DG' Diagnostic Report, 'DJ' Discharge Monitoring Report, 'DS' Discharge Summary, 'EB' Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payor), 'HC' Health Certificate, 'HR' Health Clinic Records, 'I5' Immunization Record,'IR' State School Immunization Records, 'LA' Laboratory Results, 'M1' Medical Record Attachment, 'MT' Models, 'NM Nursing Notes', 'OB' Operative Note, 'OC' Oxygen Content Averaging Report, 'OD' Orders and Treatments Document, 'OE' Objective Physical Examination (including vital signs) Document, 'OX' Oxygen Therapy Certification, 'OZ' Support Data for Claim, 'P4' Pathology Report, 'P5' Patient Medical History Document, 'PE' Parenteral or Enteral Certification, 'PN' Physical Therapy Notes, 'PO' Prosthetics or Orthotic Certification, 'PQ' Paramedical Results, 'PY' Physician's Report, 'PZ' Physical Therapy Certification, 'RB' Radiology Films, 'RR' Radiology Reports, 'RT' Report of Tests and Analysis Report, 'RX' Renewable Oxygen Content Averaging Report, 'SG' Symptoms Document, 'V5' Death Notification, 'XP' Photographs", "example": "93", "enum": ["03", "04", "05", "06", "07", "08", "09", "10", "11", "13", "15", "21", "A3", "A4", "AM", "AS", "B2", "B3", "B4", "BR", "BS", "BT", "CB", "CK", "CT", "D2", "DA", "DB", "DG", "DJ", "DS", "EB", "HC", "HR", "I5", "IR", "LA", "M1", "MT", "NM", "OB", "OC", "OD", "OE", "OX", "OZ", "P4", "P5", "PE", "PN", "PO", "PQ", "PY", "PZ", "RB", "RR", "RT", "RX", "SG", "V5", "XP"]}, "attachmentTransmissionCode": {"type": "string", "description": "Loop: 2400, Segment: PWK, Element: PWK02 Allowed Values are: 'AA' Available on Request at Provider Site, 'BM' By Mail,'EL' Electronically Only, 'EM' E-Mail, 'FT' File Transfer, 'FX' By Fax", "example": "AA", "enum": ["AA", "BM", "EL", "EM", "FT", "FX"]}, "attachmentControlNumber": {"type": "string", "description": "Loop 2400, Segment: PWK, Element: PWK05"}}, "required": ["attachmentReportTypeCode", "attachmentTransmissionCode"]}, "priorAuthorizationNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=G1"}, "referralNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=9F"}, "claimControlNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=F8"}, "cliaNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=X4"}, "repricedClaimNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=9A", "example": "00001"}, "adjustedRepricedClaimNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=9C"}, "investigationalDeviceExemptionNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=LX"}, "claimNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=D9", "example": "12345"}, "mammographyCertificationNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=EW"}, "medicalRecordNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=EA"}, "demoProjectIdentifier": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=P4"}, "carePlanOversightNumber": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=1J"}, "medicareCrossoverReferenceId": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=F5"}, "serviceAuthorizationExceptionCode": {"type": "string", "description": "Loop: 2300, Segment: REF, Element: REF02 and REF01=4N, Note: '1' Immediate/Urgent Care, '2' Services Rendered in a Retroactive Period, '3' Emergency Care, '4' Client has Temporary Medicaid, '5' Request from County for Second Opinion to Determine if Recipient Can Work, '6' Request for Override Pending, '7' Special Handling, Null", "enum": ["1", "2", "3", "4", "5", "6", "7"]}}, "description": "PWK and REF"}, "claimNote": {"type": "object", "properties": {"additionalInformation": {"type": "string", "description": "Loop 2300, Segment: NTE, Element: NTE02, Note: NTE01=ADD"}, "certificationNarrative": {"type": "string", "description": "Loop 2300, Segment: NTE, Element: NTE02, Note: NTE01=CER"}, "goalRehabOrDischargePlans": {"type": "string", "description": "Loop 2300, Segment: NTE, Element: NTE02, Note: NTE01=DCP"}, "diagnosisDescription": {"type": "string", "description": "Loop 2300, Segment: NTE, Element: NTE02, Note: NTE01=DGN"}, "thirdPartOrgNotes": {"type": "string", "description": "Loop 2300, Segment: NTE, Element: NTE02, Note: NTE01=TPO"}}, "description": "NTE"}, "ambulanceTransportInformation": {"required": ["ambulanceTransportReasonCode", "transportDistanceInMiles"], "type": "object", "properties": {"patientWeightInPounds": {"type": "string", "description": "Segment: CR1, Element: CR102"}, "ambulanceTransportReasonCode": {"type": "string", "description": "CR104, Note: Allowed Values are: 'A' Patient was transported to nearest facility for care of symptoms, complaints, or both, 'B' Patient was transported for the benefit of a preferred physician, 'C' Patient was transported for the nearness of family members, 'D' Patient was transported for the care of a specialist or for availability of specialized equipment, 'E' Patient Transferred to Rehabilitation Facility", "enum": ["A", "B", "C", "D", "E"]}, "transportDistanceInMiles": {"type": "string", "description": "Segment: CR1, Element: CR106"}, "roundTripPurposeDescription": {"type": "string", "description": "Segment: CR1, Element: CR109"}, "stretcherPurposeDescription": {"type": "string", "description": "Segment: CR1, Element: CR110"}}, "description": "CR1"}, "spinalManipulationServiceInformation": {"required": ["patientConditionCode"], "type": "object", "properties": {"patientConditionCode": {"type": "string", "description": "Loop: 2300, Segment: CR, Element: CR208"}, "patientConditionDescription1": {"type": "string", "description": "Loop: 2300, Segment: CR, Element: CR210 Note: Allowed Values are: 'A' Acute Condition, 'C' Chronic Condition, 'D' Chronic Condition, 'E' Non-Life Threatening, 'F' Routine, '<PERSON>' Symptomatic, 'M' Acute Manifestation of a Chronic Condition", "enum": ["A", "C", "D", "E", "F", "G", "M"]}, "patientConditionDescription2": {"type": "string", "description": "Loop: 2300, Segment: CR, Element: CR211"}}, "description": "Loop 2300, Segment: CR2"}, "ambulanceCertification": {"maxItems": 3, "minItems": 0, "type": "array", "description": "Loop 2300, Segment: CRC", "items": {"required": ["certificationConditionIndicator", "conditionCodes"], "type": "object", "properties": {"certificationConditionIndicator": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC02 when CRC01 = 07, Note: Allowed Values are: 'N' No, 'Y' Yes", "enum": ["N", "Y"]}, "conditionCodes": {"maxItems": 5, "minItems": 1, "type": "array", "description": "Loop: 2300, Segment: CRC, Element: CRC03, CRC04, CRC05, CRC06, CRC07, Note: Allowed Values are: '01' Patient was admitted to a hospital, '04' Patient was moved by stretcher, '05' Patient was unconscious or in shock, '06' Patient was transported in an emergency situation, '07' Patient had to be physically restrained, '08' Patient had visible hemorrhaging, '09' Ambulance service was medically necessary, '12' Patient is confined to a bed or chair", "items": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC03, CRC04, CRC05, CRC06, CRC07, Note: Allowed Values are: '01' Patient was admitted to a hospital, '04' Patient was moved by stretcher, '05' Patient was unconscious or in shock, '06' Patient was transported in an emergency situation, '07' Patient had to be physically restrained, '08' Patient had visible hemorrhaging, '09' Ambulance service was medically necessary, '12' Patient is confined to a bed or chair", "enum": ["01", "04", "05", "06", "07", "08", "09", "12"]}, "enum": ["01", "04", "05", "06", "07", "08", "09", "12"]}}, "description": "CRC"}}, "patientConditionInformationVision": {"maxItems": 3, "minItems": 0, "type": "array", "description": "Loop 2300, Segment: CRC", "items": {"required": ["certificationConditionIndicator", "codeCategory", "conditionCodes"], "type": "object", "properties": {"codeCategory": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC01, Notes: Allowed Values are: 'E1' Spectacle Lenses, 'E2' Contact Lenses, 'E3' Spectacle Frames", "enum": ["E1", "E2", "E3"]}, "certificationConditionIndicator": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC02, Notes: Allowed Values are: 'N' No, 'Y' Yes", "enum": ["N", "Y"]}, "conditionCodes": {"maxItems": 5, "minItems": 1, "type": "array", "description": "Loop: 2300, Segment: CRC, Element: CRC03 to CRC07, Notes: CRC03 is required, others are situational. Allowed Values are: 'L1' General Standard of 20 Degree or .5 Diopter Sphere or Cylinder Change Met, 'L2' Replacement Due to Loss or Theft, 'L3' Replacement Due to Breakage or Damage, L4' Replacement Due to Patient Preference, 'L5' Replacement Due to Medical Reason", "items": {"type": "string", "enum": ["L1", "L2", "L3", "L4", "L5"]}}}, "description": "Loop 2300, Segment: CRC"}}, "homeboundIndicator": {"type": "boolean", "description": "Loop 2300, Segment: CRC"}, "epsdtReferral": {"required": ["certificationConditionCodeAppliesIndicator", "conditionCodes"], "type": "object", "properties": {"certificationConditionCodeAppliesIndicator": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC02 When CRC01=ZZ, Note: 'N' No, 'Y' Yes", "enum": ["N", "Y"]}, "conditionCodes": {"maxItems": 3, "minItems": 1, "type": "array", "description": "Loop: 2300, Segment: CRC, Elements: CRC03, CRC04, CRC05 Note: Allowed Values are: 'AV' Available- Not Used, 'NU' Not Used, 'S2' Under Treatment, 'ST' New Services Requested", "items": {"type": "string", "enum": ["AV", "NU", "S2", "ST"]}}}, "description": "CRC"}, "healthCareCodeInformation": {"maxItems": 12, "minItems": 1, "type": "array", "description": "Loop 2300, Segment: HI", "items": {"required": ["diagnosisCode", "diagnosisTypeCode"], "type": "object", "properties": {"diagnosisTypeCode": {"type": "string", "description": "Loop: 2440, Segment: HI, Element: HI01-01 or HI02-01 or HI03-01 or HI04-01 or HI05-01 or HI06-01 or HI07-01 or HI08-01 or HI09-01 or HI10-01 or HI11-01 or HI12-01, Note: Allowed Values are: 'BK' International Classification of Diseases Clinical Modification (ICD-9-CM) Principal Diagnosis, 'ABK' International Classification of Diseases Clinical Modification (ICD-10-CM) Principal Diagnosis, 'BF' International Classification of Diseases Clinical Modification (ICD-9-CM) Diagnosis, 'ABF' International Classification of Diseases Clinical Modification (ICD-10-CM) Diagnosis", "enum": ["BK", "ABK", "BF", "ABF"]}, "diagnosisCode": {"type": "string", "description": "Loop: 2440, Segment: HI, Element: HI01-02 or HI02-02 or HI03-02 or HI04-02 or HI05-02 or HI06-02 or HI07-02 or HI08-02 or HI09-02 or HI10-02 or HI11-02 or HI12-02"}}, "description": "HI"}}, "anesthesiaRelatedSurgicalProcedure": {"maxItems": 2, "minItems": 1, "type": "array", "description": "Loop 2300, Segment: HI", "items": {"type": "string", "description": "Loop 2300, Segment: HI"}}, "conditionInformation": {"maxItems": 2, "minItems": 0, "type": "array", "description": "Loop 2300, Segment: HI", "items": {"required": ["conditionCodes"], "type": "object", "properties": {"conditionCodes": {"maxItems": 12, "minItems": 1, "type": "array", "items": {"type": "string", "description": "Loop: 2300, Segment: HI, Element: HI01-02, HI02-02, HI03-02, HI04-02, HI05-02, HI06-02, HI07-02, HI08-02, HI09-02, HI10-02, HI11-02, HI11-02, HI12-02"}}}, "description": "HI"}}, "claimPricingRepricingInformation": {"required": ["pricingMethodologyCode", "repricedAllowedAmount"], "type": "object", "properties": {"pricingMethodologyCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP01, Note: Allowed Values are: '00' Zero Pricing (Not Covered Under Contract), '01' Priced as Billed at 100%, '02' Priced at the Standard Fee Schedule, '03' Priced at a Contractual Percentage, '04' Bundled Pricing, '05' Peer Review Pricing, '06' Bundled Pricing, '07' Flat Rate Pricing, '08' Combination Pricing, '09' Maternity Pricing, '10' Other Pricing, '11' Lower of Cost, '12' <PERSON><PERSON> of Cost, '13' Cost Reimbursed, '14' Adjustment Pricing", "example": "01", "enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14"]}, "repricedAllowedAmount": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP02", "example": "1"}, "repricedSavingAmount": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP03"}, "repricingOrganizationIdentifier": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP04"}, "repricingPerDiemOrFlatRateAmoung": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP05"}, "repricedApprovedAmbulatoryPatientGroupCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP06"}, "repricedApprovedAmbulatoryPatientGroupAmount": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP07"}, "rejectReasonCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP13, Note: Allowed Values are: 'T1' Cannot Identify Provider as TPO (Third Party Organization) Participant, 'T2' Cannot Identify Payer as TPO (Third Party Organization) Participant, 'T3' Cannot Identify Insured as TPO (Third Party Organization) Participant, 'T4' Payer Name or Identifier Missing, 'T5' Certification Information Missing, '16' Claim does not contain enough information for repricing", "enum": ["T1", "T2", "T3", "T4", "T5", "T6"]}, "policyComplianceCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP14, Note: Allowed Values are: '1' Procedure Followed (Compliance), '2' Not Followed - Call Not Made (Non-Compliance Call Not Made), '3' Not Medically Necessary (Non-Compliance Non-Medically Necessary), '4' Not Followed Other (Non-Compliance Other), '5' Emergency Admit to Non-Network Hospital", "enum": ["1", "2", "3", "4", "5"]}, "exceptionCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP15, Note: Allowed Values are: '1' Non-Network Professional Provider in Network Hospital, '2' Emergency Care, '3' Services or Specialist not in Network, '4' Out-of-Service Area, '5' State Mandates, '6' Other", "enum": ["1", "2", "3", "4", "5", "6"]}}, "description": "HCP"}, "serviceFacilityLocation": {"required": ["address", "organizationName"], "type": "object", "properties": {"organizationName": {"type": "string", "description": "Loop: 2420C, Segment: NM1, Element: NM103", "example": "HAPPY DOCTORS GROUP"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "npi": {"type": "string", "description": "Loop: 2420C, Segment: NM1, Element: NM109, Note: National Provider Identifier"}, "secondaryIdentifier": {"maxItems": 3, "minItems": 0, "type": "array", "description": "Loop: 2420C: Segment: REF, Notes: A list containing qualifier (REF01), identifier (REF02), and otherIdentifier(REF04)", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}, "phoneName": {"type": "string", "description": "Loop: 2310C, Segment: PER, Element: PER02"}, "phoneNumber": {"type": "string", "description": "Loop: 2310C, Segment: PER, Element: PER04"}, "phoneExtension": {"type": "string", "description": "Loop: 2310C, Segment: PER, Element: PER06"}}}, "ambulancePickUpLocation": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "ambulanceDropOffLocation": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "otherSubscriberInformation": {"maxItems": 10, "minItems": 0, "type": "array", "description": "Loop 2320", "items": {"required": ["benefitsAssignmentCertificationIndicator", "claimFilingIndicatorCode", "individualRelationshipCode", "otherPayerName", "otherSubscriberName", "paymentResponsibilityLevelCode", "releaseOfInformationCode"], "type": "object", "properties": {"paymentResponsibilityLevelCode": {"type": "string", "description": "Loop: 2320, Segment: SBR, Element: SBR01, Notes: Allowable values are 'A' Payer Responsibility Four, 'B' Payer Responsibility Five, 'C' Payer Responsibility Six, 'D' Payer Responsibility Seven, 'E' Payer Responsibility Eight, 'F' Payer Responsibility Nine, 'G' Payer Responsibility Ten, 'H' Payer Responsibility Eleven, 'P' Primary, 'S' Secondary, 'T' Tertiary, and 'U' Unknown", "enum": ["A", "B", "C", "D", "E", "F", "G", "H", "P", "S", "T", "U"]}, "individualRelationshipCode": {"type": "string", "description": "Loop: 2320, Segment: SBR, Element: SBR02, Notes: Required when patient is the subscriber, Notes: Allowed Values are: '01' Spouse, '18' Self, '19' Child, '20' Employee, '21' Unknown, '39' <PERSON> Donor, '40' <PERSON><PERSON><PERSON> Donor, '53' Life Partner, 'G8' Other Relationship", "enum": ["01", "18", "19", "20", "21", "39", "40", "53", "G8"]}, "insuranceGroupOrPolicyNumber": {"type": "string", "description": "Loop: 2320, Segment: SBR, Element: SBR03"}, "otherInsuredGroupName": {"type": "string", "description": "Loop: 2000B, Segment: SBR, Element: SBR04"}, "insuranceTypeCode": {"type": "string", "description": "Loop: 2320, Segment: SBR, Element: SBR05, Notes: Allowable Values are:  '12' Medicare Secondary Working Aged Beneficiary or Spouse with Employer Group Health Plan, '13' Medicare Secondary End-Stage Renal Disease Beneficiary in the Mandated Coordination Period, '14' Medicare Secondary, No-fault Insurance including Auto is Primary, '15' Medicare Secondary Worker's Compensation, '16' Medicare Secondary Public Health Service (PHS)or Other Federal Agency, '41' Medicare Secondary Black Lung,  '42' Medicare Secondary Veteran's Administration, '43' Medicare Secondary Disabled Beneficiary Under Age 65 with Large Group Health Plan (LGHP), '47' Medicare Secondary, Other Liability Insurance is Primary", "enum": ["12", "13", "14", "15", "16", "41", "42", "43", "47"]}, "claimFilingIndicatorCode": {"type": "string", "description": "Loop: 2320, Segment: SBR, Element: SBR09, Notes: Allowed Values are: '11' Other Non-Federal Programs, '12' Preferred Provider Organization (PPO), '13' Point of Service (POS), '14' Exclusive Provider Organization (EPO), '15' Indemnity Insurance, '16' Health Maintenance Organization (HMO) Medicare Risk, '17' Dental Maintenance Organization, 'AM' Automobile Medical, 'BL' Blue Cross/Blue Shield, 'CH' Champus, 'CI' Commercial Insurance Co., 'DS' Disability, 'FI' Federal Employees Program, 'HM' Health Maintenance Organization, 'LM' Liability Medical, 'MA' Medicare Part A, 'MB' Medicare Part B,'MC' Medicare Part C, 'OF' Other Federal Program, 'TV' Title V, 'VA' Veterans Affairs Plan, 'WC' Worker's Compensation Health Claim, 'ZZ' Mutually Defined", "enum": ["11", "12", "13", "14", "15", "16", "17", "AM", "BL", "CH", "CI", "DS", "FI", "HM", "LM", "MA", "MB", "MC", "OF", "TV", "VA", "WC", "ZZ"]}, "claimLevelAdjustments": {"maxItems": 5, "minItems": 0, "type": "array", "description": "Loop: 2320, Segment: CAS", "items": {"required": ["adjustmentDetails", "adjustmentGroupCode"], "type": "object", "properties": {"adjustmentGroupCode": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS01, Notes: Code identifying the general category of payment adjustment", "enum": ["CO", "CR", "OA", "PI", "PR"]}, "adjustmentDetails": {"maxItems": 6, "minItems": 1, "type": "array", "description": "Loop: 2430, Segment: CAS", "items": {"required": ["adjustmentAmount", "adjustmentReasonCode"], "type": "object", "properties": {"adjustmentReasonCode": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS02, CAS05, CAS08, CAS11, CAS14, CAS17"}, "adjustmentAmount": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS03, CAS06, CAS09, CAS12, CAS15, CAS18"}, "adjustmentQuantity": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS04, CAS07, CAS10, CAS13, CAS16, CAS19"}}, "description": "CAS"}}}, "description": "CR1"}}, "payerPaidAmount": {"type": "string", "description": "Loop: 2320, Segment: AMT, Element: AMT02 when AMT01=D, Notes: It is acceptable to show '0' (Zero)"}, "nonCoveredChargeAmount": {"type": "string", "description": "Loop: 2320, Segment: AMT, Element: AMT02 when AMT01=A8"}, "remainingPatientLiability": {"type": "string", "description": "Loop: 2320, Segment: AMT, Element: AMT02 when AMT01=EAF"}, "benefitsAssignmentCertificationIndicator": {"type": "string", "description": "Loop: 2320, Segment: OI, Element: OI03, Notes: Allowable values are: 'N' No, 'W' Not Applicable, 'Y' Yes", "enum": ["N", "W", "Y"]}, "patientSignatureGeneratedForPatient": {"type": "boolean", "description": "Loop: 2320, Segment: OI, Element: OI04, Notes: Allowable value is 'P' Signature generated by provider because the patient was not physically present for services"}, "releaseOfInformationCode": {"type": "string", "description": "Loop: 2320, Segment: OI, Element: OI04, Notes: Allowable values are 'I' Informed Consent to Release Medical Information, 'Y' Yes", "enum": ["I", "Y"]}, "medicareOutpatientAdjudication": {"type": "object", "properties": {"reimbursementRate": {"type": "string", "description": "Loop 2320, Segment: MOA; Element: MOA01"}, "hcpcsPayableAmount": {"type": "string", "description": "Loop 2320, Segment: MOA; Element: MOA02"}, "claimPaymentRemarkCode": {"maxItems": 5, "minItems": 0, "type": "array", "description": "Loop: 2320: Segment: MOA, Element: MOA03 to MOA07", "items": {"type": "string"}}, "endStageRenalDiseasePaymentAmount": {"type": "string", "description": "Loop 2320, Segment: MOA; Element: MOA08"}, "nonPayableProfessionalComponentBilledAmount": {"type": "string", "description": "Loop 2320, Segment: MOA; Element: MOA09"}}, "description": "Loop: 2320, Segment: MOA"}, "otherSubscriberName": {"required": ["otherInsuredIdentifier", "otherInsuredIdentifierTypeCode", "otherInsuredLastName", "otherInsuredQualifier"], "type": "object", "properties": {"otherInsuredQualifier": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM102, Notes: Allowed Values are: '1' Person, '2' Non-Person Entity", "enum": ["1", "2"]}, "otherInsuredLastName": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM103"}, "otherInsuredFirstName": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM102, Notes: Required when NM102 = 1 (Person)"}, "otherInsuredMiddleName": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM105, Notes: Required when NM102 = 1 (Person)"}, "otherInsuredNameSuffix": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM107, Notes: Required when NM102 = 1 (Person)"}, "otherInsuredIdentifierTypeCode": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM108, Notes: Allowable values are: 'II' Standard Unique HealthIdentifier for each individual in the United States and 'MI' member identification number", "enum": ["II", "MI"]}, "otherInsuredIdentifier": {"type": "string", "description": "Loop: 2330A, Segment: NM1, Element: NM109"}, "otherInsuredAddress": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "otherInsuredAdditionalIdentifier": {"type": "string", "description": "Loop: 2330A, Segment: REF, Element: REF02 when REF01=SY"}}, "description": "Loop: 2330A"}, "otherPayerName": {"required": ["otherPayerIdentifier", "otherPayerIdentifierTypeCode", "otherPayerOrganizationName"], "type": "object", "properties": {"otherInsuredAdditionalIdentifier": {"type": "string", "description": "Loop: 2330B; Segment: NM1, Element: NM111"}, "otherPayerOrganizationName": {"type": "string", "description": "Loop: 2330B; Segment: NM1, Element: NM103"}, "otherPayerIdentifierTypeCode": {"type": "string", "description": "Loop: 2330B; Segment: NM1, Element: NM108, Notes: Allowable values: 'PI' Payor Identification and 'XV' Centers for Medicare/Medicaid Services PlanID", "enum": ["PI", "XV"]}, "otherPayerIdentifier": {"type": "string", "description": "Loop: 2330B; Segment: NM1, Element: NM109"}, "otherPayerAddress": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "otherPayerAdjudicationOrPaymentDate": {"type": "string", "description": "Loop: 2330B, Segment: DTP, Element: DTP03"}, "otherPayerSecondaryIdentifier": {"maxItems": 2, "minItems": 0, "type": "array", "description": "Loop: 2330B, Segment: REF", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}, "otherPayerPriorAuthorizationNumber": {"type": "string", "description": "Loop: 2330B, Segment: REF, Element: REF02 when REF01=G1"}, "otherPayerPriorAuthorizationOrReferralNumber": {"type": "string", "description": "Loop: 2330B; Segment: REF, Element: REF02 when REF01=9F"}, "otherPayerClaimAdjustmentIndicator": {"type": "boolean", "description": "Loop: 2330B, Segment: REF, Element: REF02 when REF01=T4"}, "otherPayerClaimControlNumber": {"type": "string", "description": "Loop: 2330B, Segment: REF, Element: REF02 when REF01=F8"}}, "description": "Loop: 2330B"}, "otherPayerReferringProvider": {"type": "array", "items": {"required": ["otherPayerReferringProviderIdentifier"], "type": "object", "properties": {"otherPayerReferringProviderIdentifier": {"maxItems": 3, "minItems": 1, "type": "array", "description": "Loop 2330E, Segment: NM1 and REF", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}, "description": "Loop 2330C"}}, "otherPayerRenderingProvider": {"type": "array", "items": {"required": ["entityTypeQualifier"], "type": "object", "properties": {"entityTypeQualifier": {"type": "string", "description": "Loop: 2330D, Segment NM1, Element: NM102, Notes: Allowable values are '1' Person and '2' Non-Person Entity", "enum": ["1", "2"]}, "otherPayerRenderingProviderSecondaryIdentifier": {"maxItems": 3, "minItems": 1, "type": "array", "description": "Loop 2330D, Segment: NM1 and REF", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}, "description": "Loop 2330D"}}, "otherPayerServiceFacilityLocation": {"type": "array", "items": {"required": ["otherPayerServiceFacilityLocationSecondaryIdentifier"], "type": "object", "properties": {"otherPayerServiceFacilityLocationSecondaryIdentifier": {"maxItems": 3, "minItems": 1, "type": "array", "description": "Loop 2330E, Segments: NM1 and REF", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}, "description": "Loop 2330E"}}, "otherPayerSupervisingProvider": {"type": "array", "items": {"required": ["otherPayerSupervisingProviderIdentifier"], "type": "object", "properties": {"otherPayerSupervisingProviderIdentifier": {"maxItems": 3, "minItems": 1, "type": "array", "description": "Loop 2330F, Segments: NM1 and REF", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}, "description": "Loop 2330F"}}, "otherPayerBillingProvider": {"type": "array", "items": {"required": ["entityTypeQualifier", "otherPayerBillingProviderIdentifier"], "type": "object", "properties": {"entityTypeQualifier": {"type": "string", "description": "Loop 2330G, Segment: NM1; Element: NM101, Notes: Code identifying an organizational entity, a physical location, property or an individual. Allowablevalues: '1' Person, '2' Non-Person Entity ", "enum": ["1", "2"]}, "otherPayerBillingProviderIdentifier": {"maxItems": 2, "minItems": 1, "type": "array", "description": "Loop 2330G, Segment: NM1 and REF", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}, "description": "Loop 2330G"}}}, "description": "Loop 2320"}}, "serviceLines": {"maxItems": 50, "minItems": 1, "type": "array", "description": "Loop 2400", "items": {"required": ["professionalService", "serviceDate"], "type": "object", "properties": {"assignedNumber": {"type": "string", "description": "Loop: 2400, Segment: LX, Element: LX01"}, "serviceDate": {"type": "string", "description": "Loop: 2400, Segment: DTP, Element: DTP03, Notes: When sent with serviceDateEnd it will be used as the start date for Date Time period, if sent without serviceDateEnd will use DTP02 = D8. Expressed in Format CCYYMMDD", "example": "20050514"}, "serviceDateEnd": {"type": "string", "description": "Loop: 2400, Segment: DTP, Element: DTP03, Notes: Range of Dates Expressed in Format CCYYMMDD"}, "providerControlNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF04-02 when REF01=6R"}, "professionalService": {"required": ["compositeDiagnosisCodePointers", "lineItemChargeAmount", "measurementUnit", "procedureCode", "procedureIdentifier", "serviceUnitCount"], "type": "object", "properties": {"procedureIdentifier": {"type": "string", "description": "Loop: 2400, Segment: SV1, Element: SV101-01, Notes: Allowed Values are: 'ER' Jurisdiction Specific Procedure and Supply Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code,'WK' Advanced Billing Concepts (ABC) Codes", "example": "HC", "enum": ["ER", "HC", "IV", "WK"]}, "procedureCode": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV101-02", "example": "E0570"}, "procedureModifiers": {"maxItems": 4, "minItems": 0, "type": "array", "description": "Loop 2400, Segment: SV1, Elements: SV101-03 to SV101-06, Notes: Required when modifier clarifies or improves the reporting accuracy of the associated procedure code. If not required then do not send", "items": {"type": "string"}}, "description": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV101-07, Notes: A free form description to clarify teh related data elements and their content"}, "lineItemChargeAmount": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV102, Notes: Required value for total charge amount, '0' (Zero) is acceptable for this value", "example": "25"}, "measurementUnit": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV103, Notes: Allowed values are 'MJ' Minutes, 'UN' Unit", "example": "UN", "enum": ["MJ", "UN"]}, "serviceUnitCount": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV104, Notes: When a decimal is needed to report units, include it in this element", "example": "1"}, "placeOfServiceCode": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV105"}, "compositeDiagnosisCodePointers": {"required": ["diagnosisCodePointers"], "type": "object", "properties": {"diagnosisCodePointers": {"maxItems": **********, "minItems": 1, "type": "array", "description": "Loop: 2400, Segment: SV1, Element: SV107-01, SV107-02, SV107-03, SV107-04", "example": 1, "items": {"type": "string"}}}, "description": "SVC107"}, "emergencyIndicator": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV109", "enum": ["Y"]}, "epsdtIndicator": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV111", "enum": ["Y"]}, "familyPlanningIndicator": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV112", "enum": ["Y"]}, "copayStatusCode": {"type": "string", "description": "Loop 2400, Segment: SV1, Element: SV115", "enum": ["0"]}}}, "durableMedicalEquipmentService": {"required": ["days", "frequencyCode", "purchasePrice", "rentalPrice"], "type": "object", "properties": {"days": {"type": "string", "description": "Loop: 2410, Segment: SV5, Element: SV503"}, "rentalPrice": {"type": "string", "description": "Loop: 2410, Segment: SV5, Element: SV504"}, "purchasePrice": {"type": "string", "description": "Loop: 2410, Segment: SV5, Element: SV505"}, "frequencyCode": {"type": "string", "description": "Loop: 2410, Segment: SV5, Element: SV506, Note: Allowed Values are: '1' weekly, '4' monthly, '6' daily", "enum": ["1", "4", "6"]}}, "description": "SV5"}, "serviceLineSupplementalInformation": {"maxItems": 10, "minItems": 0, "type": "array", "items": {"required": ["attachmentReportTypeCode", "attachmentTransmissionCode"], "type": "object", "properties": {"attachmentReportTypeCode": {"type": "string", "description": "Loop: 2400, Segment: PWK, Element: PWK01, Notes: Allowed Values are: '03' Report Justifying Treatment Beyond Utilization Guidelines, '04' Drugs Administered, '05' Treatment Diagnosis, '06' Initial Assessment, '07' Functional Goals, '08' Plan of Treatment, '09' Progress Report, '10' Continued Treatment, '11' Chemical Analysis, '13' Certified Test Report, '15' Justification for Admission, '21' Recovery Plan, 'A3' Allergies/Sensitivities Document, 'A4' Autopsy Report, 'AM' Ambulance Certification, 'AS' Admission Summary, 'B2' Prescription, 'B3' Physician Order, 'B4' Referral Form, 'BR' Benchmark Testing Results, 'BS' Baseline, 'BT' Blanket Test Results, 'CB' Chiropractic Justification, 'CK' Consent Form(s), 'CT' Certification, 'D2' Drug Profile Document, 'DA' Dental Models, 'DB' Durable Medical Equipment Prescription, 'DG' Diagnostic Report, 'DJ' Discharge Monitoring Report, 'DS' Discharge Summary, 'EB' Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payor), 'HC' Health Certificate, 'HR' Health Clinic Records, 'I5' Immunization Record,'IR' State School Immunization Records, 'LA' Laboratory Results, 'M1' Medical Record Attachment, 'MT' Models, 'NM Nursing Notes', 'OB' Operative Note, 'OC' Oxygen Content Averaging Report, 'OD' Orders and Treatments Document, 'OE' Objective Physical Examination (including vital signs) Document, 'OX' Oxygen Therapy Certification, 'OZ' Support Data for Claim, 'P4' Pathology Report, 'P5' Patient Medical History Document, 'PE' Parenteral or Enteral Certification, 'PN' Physical Therapy Notes, 'PO' Prosthetics or Orthotic Certification, 'PQ' Paramedical Results, 'PY' Physician's Report, 'PZ' Physical Therapy Certification, 'RB' Radiology Films, 'RR' Radiology Reports, 'RT' Report of Tests and Analysis Report, 'RX' Renewable Oxygen Content Averaging Report, 'SG' Symptoms Document, 'V5' Death Notification, 'XP' Photographs", "example": "93", "enum": ["03", "04", "05", "06", "07", "08", "09", "10", "11", "13", "15", "21", "A3", "A4", "AM", "AS", "B2", "B3", "B4", "BR", "BS", "BT", "CB", "CK", "CT", "D2", "DA", "DB", "DG", "DJ", "DS", "EB", "HC", "HR", "I5", "IR", "LA", "M1", "MT", "NM", "OB", "OC", "OD", "OE", "OX", "OZ", "P4", "P5", "PE", "PN", "PO", "PQ", "PY", "PZ", "RB", "RR", "RT", "RX", "SG", "V5", "XP"]}, "attachmentTransmissionCode": {"type": "string", "description": "Loop: 2400, Segment: PWK, Element: PWK02 Allowed Values are: 'AA' Available on Request at Provider Site, 'BM' By Mail,'EL' Electronically Only, 'EM' E-Mail, 'FT' File Transfer, 'FX' By Fax", "example": "AA", "enum": ["AA", "BM", "EL", "EM", "FT", "FX"]}, "attachmentControlNumber": {"type": "string", "description": "Loop 2400, Segment: PWK, Element: PWK05"}}}}, "durableMedicalEquipmentCertificateOfMedicalNecessity": {"required": ["attachmentTransmissionCode"], "type": "object", "properties": {"attachmentTransmissionCode": {"type": "string", "description": "Loop: 2400, Segment: PWK, Element: PWK02 when PWK01=CT, Note: Allowed Values are: 'AB' Previously Submitted to Payer, 'AD' Certification Included in this Claim, 'AF' Narrative Segment Included in this Claim, 'AG' No Documentation is Required, 'NS' Not Specified", "enum": ["AB", "AD", "AF", "AG", "NS"]}}, "description": "PWK"}, "ambulanceTransportInformation": {"required": ["ambulanceTransportReasonCode", "transportDistanceInMiles"], "type": "object", "properties": {"patientWeightInPounds": {"type": "string", "description": "Segment: CR1, Element: CR102"}, "ambulanceTransportReasonCode": {"type": "string", "description": "CR104, Note: Allowed Values are: 'A' Patient was transported to nearest facility for care of symptoms, complaints, or both, 'B' Patient was transported for the benefit of a preferred physician, 'C' Patient was transported for the nearness of family members, 'D' Patient was transported for the care of a specialist or for availability of specialized equipment, 'E' Patient Transferred to Rehabilitation Facility", "enum": ["A", "B", "C", "D", "E"]}, "transportDistanceInMiles": {"type": "string", "description": "Segment: CR1, Element: CR106"}, "roundTripPurposeDescription": {"type": "string", "description": "Segment: CR1, Element: CR109"}, "stretcherPurposeDescription": {"type": "string", "description": "Segment: CR1, Element: CR110"}}, "description": "CR1"}, "durableMedicalEquipmentCertification": {"required": ["certificationTypeCode", "durableMedicalEquipmentDurationInMonths"], "type": "object", "properties": {"certificationTypeCode": {"type": "string", "description": "Loop: 2400, Segment: CR3, Element: CR301, Note: Allowed Values are: 'I' Initial, 'R' Renewal, 'S' Revised", "enum": ["I", "R", "S"]}, "durableMedicalEquipmentDurationInMonths": {"type": "string", "description": "Loop: 2400, Segment: CR3, Element: CR303 when CR302=MO"}}, "description": "CR3"}, "ambulanceCertification": {"maxItems": 3, "minItems": 0, "type": "array", "items": {"required": ["certificationConditionIndicator", "conditionCodes"], "type": "object", "properties": {"certificationConditionIndicator": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC02 when CRC01 = 07, Note: Allowed Values are: 'N' No, 'Y' Yes", "enum": ["N", "Y"]}, "conditionCodes": {"maxItems": 5, "minItems": 1, "type": "array", "description": "Loop: 2300, Segment: CRC, Element: CRC03, CRC04, CRC05, CRC06, CRC07, Note: Allowed Values are: '01' Patient was admitted to a hospital, '04' Patient was moved by stretcher, '05' Patient was unconscious or in shock, '06' Patient was transported in an emergency situation, '07' Patient had to be physically restrained, '08' Patient had visible hemorrhaging, '09' Ambulance service was medically necessary, '12' Patient is confined to a bed or chair", "items": {"type": "string", "description": "Loop: 2300, Segment: CRC, Element: CRC03, CRC04, CRC05, CRC06, CRC07, Note: Allowed Values are: '01' Patient was admitted to a hospital, '04' Patient was moved by stretcher, '05' Patient was unconscious or in shock, '06' Patient was transported in an emergency situation, '07' Patient had to be physically restrained, '08' Patient had visible hemorrhaging, '09' Ambulance service was medically necessary, '12' Patient is confined to a bed or chair", "enum": ["01", "04", "05", "06", "07", "08", "09", "12"]}, "enum": ["01", "04", "05", "06", "07", "08", "09", "12"]}}, "description": "CRC"}}, "hospiceEmployeeIndicator": {"type": "boolean", "description": "Loop: 2400, Segment: CRC, Element: CRC02 Notes: True or False"}, "conditionIndicatorDurableMedicalEquipment": {"required": ["certificationConditionIndicator", "conditionIndicator"], "type": "object", "properties": {"certificationConditionIndicator": {"type": "string", "description": "Loop 2400, Segment: CRC, Element: CRC02 and CRC01=09, Note: Allowed Values are: 'N' No, 'Y' Yes", "example": "Y", "enum": ["Y", "N"]}, "conditionIndicator": {"type": "string", "description": "Loop 2400, Segment: CRC, Element: CRC03, Note: Allowed Values are: 01 Patient addmited to hospital, 12 Patient is confined to a bed or chair", "example": "01", "enum": ["01", "12"]}, "conditionIndicatorCode": {"type": "string", "description": "Loop 2400, Segment: CRC, Element: CRC04, Note: Allowed Values are: '38' Certification signed by the physician is on file at the supplier's office, 'ZV' Replacement Item", "example": "38", "enum": ["38", "ZV"]}}, "description": "CRC"}, "serviceLineDateInformation": {"type": "object", "properties": {"prescriptionDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=471, Notes: Date Expressed in Format CCYYMMDD"}, "certificationRevisionOrRecertificationDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=607, Notes: Date Expressed in Format CCYYMMDD"}, "beginTherapyDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=463, Notes: Date Expressed in Format CCYYMMDD"}, "lastCertificationDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=461, Notes: Date Expressed in Format CCYYMMDD"}, "treatmentOrTherapyDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=304, Notes: Date Expressed in Format CCYYMMDD"}, "hemoglobinTestDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=738, Notes: Date Expressed in Format CCYYMMDD"}, "serumCreatineTestDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=739, Notes: Date Expressed in Format CCYYMMDD"}, "shippedDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=011, Notes: Date Expressed in Format CCYYMMDD"}, "lastXRayDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=455, Notes: Date Expressed in Format CCYYMMDD"}, "initialTreatmentDate": {"type": "string", "description": "Loop: 2400, Segment: DPT, Element: DTP03 when DPT01=454, Notes: Date Expressed in Format CCYYMMDD"}}}, "ambulancePatientCount": {"type": "integer", "description": "Loop: 2400, Segment: QTY, Element: QTY02 when QTY01=PT", "format": "int32"}, "obstetricAnesthesiaAdditionalUnits": {"type": "integer", "description": "Loop: 2400, Segment: QTY, Element: QTY02 when QTY01=FL", "format": "int32"}, "testResults": {"maxItems": 5, "minItems": 0, "type": "array", "items": {"required": ["measurementQualifier", "measurementReferenceIdentificationCode", "testResults"], "type": "object", "properties": {"measurementReferenceIdentificationCode": {"type": "string", "description": "Loop 2400, Segment: MEA; Element: MEA01, Notes: Allowable values are 'OG' Original and 'TR' Test Results", "enum": ["OG", "TR"]}, "measurementQualifier": {"type": "string", "description": "Loop 2400, Segment: MEA; Element: MEA02, Notes: Allowable values are 'HT' Height, 'R1' Hemoglobin, 'R2' Hematocrit, 'R3' Epoetin Starting Dosage, 'R4' Crea<PERSON>ine", "enum": ["HT", "R1", "R2", "R3", "R4"]}, "testResults": {"type": "string", "description": "Loop 2400, Segment: MEA; Element: MEA03"}}}}, "contractInformation": {"required": ["contractTypeCode"], "type": "object", "properties": {"contractTypeCode": {"type": "string", "description": "Segment: CN1, Element: CN101, Allowed Values are: '01' Diagnosis Related Group (DRG), '02' Per Diem, '03' Variable Per Diem, '04' Flat, '05' Capitated, '06' Percent, '09' Other", "example": "01", "enum": ["01", "02", "03", "04", "05", "06", "09"]}, "contractAmount": {"type": "string", "description": "Segment: CN1, Element: CN102"}, "contractPercentage": {"type": "string", "description": "Segment: CN1, Element: CN103"}, "contractCode": {"type": "string", "description": "Segment: CN1, Element: CN104"}, "termsDiscountPercentage": {"type": "string", "description": "Segment: CN1, Element: CN105"}, "contractVersionIdentifier": {"type": "string", "description": "Segment: CN1, Element: CN106"}}, "description": "CN1"}, "serviceLineReferenceInformation": {"type": "object", "properties": {"repricedLineItemReferenceNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 Notes: When REF01=9B"}, "adjustedRepricedLineItemReferenceNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 Note: When REF01=9D"}, "priorAuthorization": {"maxItems": 5, "minItems": 0, "type": "array", "description": "Loop 2400 REF", "items": {"required": ["priorAuthorizationOrReferralNumber"], "type": "object", "properties": {"priorAuthorizationOrReferralNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 when REF01=G1"}, "otherPayerPrimaryIdentifier": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF04-2 when REF04-1=2U"}}, "description": "Loop 2400 REF"}}, "mammographyCertificationNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 Note: When REF01=EW"}, "clinicalLaboratoryImprovementAmendmentNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 Note: When REF01=X4"}, "referringCliaNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 Note: When REF01=F4"}, "immunizationBatchNumber": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF02 Note: When REF01=BT"}, "referralNumber": {"maxItems": 5, "minItems": 0, "type": "array", "description": "Loop: 2400, Segment: REF, Element: REF Note: When REF01=9F", "items": {"type": "string", "description": "Loop: 2400, Segment: REF, Element: REF Note: When REF01=9F"}}}}, "salesTaxAmount": {"type": "string", "description": "Loop: 2400, Segment: AMT, Element: AMT02 when AMT01=T"}, "postageTaxAmount": {"type": "string", "description": "Loop: 2400, Segment: AMT, Element: AMT02 when AMT01=F4"}, "fileInformation": {"maxItems": 10, "minItems": 0, "type": "array", "description": "Loop: 2400, Segment: K3, Element: K301", "items": {"type": "string", "description": "Loop: 2400, Segment: K3, Element: K301"}}, "additionalNotes": {"type": "string", "description": "Loop: 2400, Segment: NTE, Element: NTE02 when NTE01=ADD"}, "goalRehabOrDischargePlans": {"type": "string", "description": "Loop: 2400, Segment: NTE, Element: NTE02 when NTE01=DCP"}, "thirdPartyOrganizationNotes": {"type": "string", "description": "Loop: 2400, Segment: NTE, Element: NTE02 when NTE01=TPO"}, "purchasedServiceInformation": {"required": ["purchasedServiceChargeAmount", "purchasedServiceProviderIdentifier"], "type": "object", "properties": {"purchasedServiceProviderIdentifier": {"type": "string", "description": "Loop: 2400, Segment: PS1, Element: PS101", "example": "01"}, "purchasedServiceChargeAmount": {"type": "string", "description": "Loop: 2400, Segment: PS1, Element: PS102", "example": "10"}}}, "linePricingRepricingInformation": {"required": ["pricingMethodologyCode", "repricedAllowedAmount"], "type": "object", "properties": {"pricingMethodologyCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP01, Note: Allowed Values are: '00' Zero Pricing (Not Covered Under Contract), '01' Priced as Billed at 100%, '02' Priced at the Standard Fee Schedule, '03' Priced at a Contractual Percentage, '04' Bundled Pricing, '05' Peer Review Pricing, '06' Bundled Pricing, '07' Flat Rate Pricing, '08' Combination Pricing, '09' Maternity Pricing, '10' Other Pricing, '11' Lower of Cost, '12' <PERSON><PERSON> of Cost, '13' Cost Reimbursed, '14' Adjustment Pricing", "example": "01", "enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14"]}, "repricedAllowedAmount": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP02", "example": "1"}, "repricedSavingAmount": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP03"}, "repricingOrganizationIdentifier": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP04"}, "repricingPerDiemOrFlatRateAmoung": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP05"}, "repricedApprovedAmbulatoryPatientGroupCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP06"}, "repricedApprovedAmbulatoryPatientGroupAmount": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP07"}, "rejectReasonCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP13, Note: Allowed Values are: 'T1' Cannot Identify Provider as TPO (Third Party Organization) Participant, 'T2' Cannot Identify Payer as TPO (Third Party Organization) Participant, 'T3' Cannot Identify Insured as TPO (Third Party Organization) Participant, 'T4' Payer Name or Identifier Missing, 'T5' Certification Information Missing, '16' Claim does not contain enough information for repricing", "enum": ["T1", "T2", "T3", "T4", "T5", "T6"]}, "policyComplianceCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP14, Note: Allowed Values are: '1' Procedure Followed (Compliance), '2' Not Followed - Call Not Made (Non-Compliance Call Not Made), '3' Not Medically Necessary (Non-Compliance Non-Medically Necessary), '4' Not Followed Other (Non-Compliance Other), '5' Emergency Admit to Non-Network Hospital", "enum": ["1", "2", "3", "4", "5"]}, "exceptionCode": {"type": "string", "description": "Loop: 2300, Segment: HCP, Element: HCP15, Note: Allowed Values are: '1' Non-Network Professional Provider in Network Hospital, '2' Emergency Care, '3' Services or Specialist not in Network, '4' Out-of-Service Area, '5' State Mandates, '6' Other", "enum": ["1", "2", "3", "4", "5", "6"]}}, "description": "HCP"}, "drugIdentification": {"required": ["measurementUnitCode", "nationalDrugCode", "nationalDrugUnitCount", "serviceIdQualifier"], "type": "object", "properties": {"serviceIdQualifier": {"type": "string", "description": "Loop: 2410, Segment: LIN, Element: LIN02, Note: Allowed Values are: 'EN' EAN/UCC - 13, 'EO' EAN/UCC - 8, 'HI' HIBC (Health Care Industry Bar Code) Supplier Labeling Standard Primary Data Message, 'N4' National Drug Code in 5-4-2 Format, 'ON' Customer Order Number, 'UK' GTIN 14-digit Data Structure, 'UP' UCC - 12", "enum": ["EN", "EO", "HI", "N4", "ON", "UK", "UP"]}, "nationalDrugCode": {"type": "string", "description": "Loop: 2410, Segment: LIN, Element: LIN03"}, "nationalDrugUnitCount": {"type": "string", "description": "Loop: 2410, Segment: CTP, Element: CTP04"}, "measurementUnitCode": {"type": "string", "description": "Loop: 2410, Segment: CTP05, Element: CTP05-01, Allowed Values are: 'F2' International Unit, 'GR' Gram, 'ME' Milligram, 'ML' Milliliter, 'UN' Unit", "enum": ["F2", "GR", "ME", "ML", "UN"]}, "linkSequenceNumber": {"type": "string", "description": "Loop: 2410, Segment: REF, Element: REF02 when REF01=VY"}, "pharmacyPrescriptionNumber": {"type": "string", "description": "Loop: 2410, Segment: REF, Element: REF02 when REF01=XZ"}}, "description": "LOOP 2410"}, "renderingProvider": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}, "otherIdentifier": {"type": "string"}, "secondaryIdentifier": {"maxItems": 20, "minItems": 0, "type": "array", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}}, "purchasedServiceProvider": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}, "otherIdentifier": {"type": "string"}, "secondaryIdentifier": {"maxItems": 20, "minItems": 0, "type": "array", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}}, "serviceFacilityLocation": {"required": ["address", "organizationName"], "type": "object", "properties": {"organizationName": {"type": "string", "description": "Loop: 2420C, Segment: NM1, Element: NM103", "example": "HAPPY DOCTORS GROUP"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "npi": {"type": "string", "description": "Loop: 2420C, Segment: NM1, Element: NM109, Note: National Provider Identifier"}, "secondaryIdentifier": {"maxItems": 3, "minItems": 0, "type": "array", "description": "Loop: 2420C: Segment: REF, Notes: A list containing qualifier (REF01), identifier (REF02), and otherIdentifier(REF04)", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}, "phoneName": {"type": "string", "description": "Loop: 2310C, Segment: PER, Element: PER02"}, "phoneNumber": {"type": "string", "description": "Loop: 2310C, Segment: PER, Element: PER04"}, "phoneExtension": {"type": "string", "description": "Loop: 2310C, Segment: PER, Element: PER06"}}}, "supervisingProvider": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}, "otherIdentifier": {"type": "string"}, "secondaryIdentifier": {"maxItems": 20, "minItems": 0, "type": "array", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}}, "orderingProvider": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 or PER08, Note: PER05=EX or PER07=EX", "example": "1234"}}, "description": "PER"}, "otherIdentifier": {"type": "string"}, "secondaryIdentifier": {"maxItems": 20, "minItems": 0, "type": "array", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}}, "referringProvider": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}, "otherIdentifier": {"type": "string"}, "secondaryIdentifier": {"maxItems": 20, "minItems": 0, "type": "array", "items": {"required": ["identifier", "qualifier"], "type": "object", "properties": {"qualifier": {"type": "string", "description": "Segment: REF, Element: REF01"}, "identifier": {"type": "string", "description": "Segment: REF, Element: REF02"}, "otherIdentifier": {"type": "string", "description": "Segment: REF, Element: REF03"}}}}}}, "ambulancePickUpLocation": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "ambulanceDropOffLocation": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "lineAdjudicationInformation": {"maxItems": 15, "minItems": 0, "type": "array", "items": {"required": ["adjudicationOrPaymentDate", "otherPayerPrimaryIdentifier", "paidServiceUnitCount", "procedureCode", "serviceIdQualifier", "serviceLinePaidAmount"], "type": "object", "properties": {"otherPayerPrimaryIdentifier": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD01"}, "serviceLinePaidAmount": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD02"}, "serviceIdQualifier": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD03-01, Note: Allowed Values are: 'ER' Jurisdiction Specific Procedure and Supply Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'HP' Health Insurance Prospective Payment System (HIPPS) Skilled Nursing Facility Rate Code, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code, 'WK' Advanced Billing Concepts (ABC) Codes", "enum": ["ER", "HC", "HP", "IV", "WK"]}, "procedureCode": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD03-02"}, "procedureModifier": {"maxItems": 4, "minItems": 0, "type": "array", "items": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD03-03, SVD03-04, SVD03-05, SVD03-06"}}, "procedureCodeDescription": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD03-07"}, "paidServiceUnitCount": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD05"}, "bundledOrUnbundledLineNumber": {"type": "string", "description": "Loop: 2430, Segment: SVD, Element: SVD06"}, "claimAdjustmentInformation": {"maxItems": 5, "minItems": 0, "type": "array", "description": "Loop: 2430, Segment: CAS", "items": {"required": ["adjustmentDetails", "adjustmentGroupCode"], "type": "object", "properties": {"adjustmentGroupCode": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS01, Notes: Code identifying the general category of payment adjustment", "enum": ["CO", "CR", "OA", "PI", "PR"]}, "adjustmentDetails": {"maxItems": 6, "minItems": 1, "type": "array", "description": "Loop: 2430, Segment: CAS", "items": {"required": ["adjustmentAmount", "adjustmentReasonCode"], "type": "object", "properties": {"adjustmentReasonCode": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS02, CAS05, CAS08, CAS11, CAS14, CAS17"}, "adjustmentAmount": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS03, CAS06, CAS09, CAS12, CAS15, CAS18"}, "adjustmentQuantity": {"type": "string", "description": "Loop: 2430, Segment: CAS, Element: CAS04, CAS07, CAS10, CAS13, CAS16, CAS19"}}, "description": "CAS"}}}, "description": "CR1"}}, "adjudicationOrPaymentDate": {"type": "string", "description": "Loop: 2430, Segment: DTP, Element=DTP03 when DTP02=D8 and DTP01=573"}, "remainingPatientLiability": {"type": "string", "description": "Loop: 2430, Segment: AMT, Element=AMT02 when AMT01=EAF"}}, "description": "SVD, CAS, DTP and AMT"}}, "formIdentification": {"type": "array", "items": {"required": ["formIdentifier", "formTypeCode"], "type": "object", "properties": {"formTypeCode": {"type": "string", "description": "Loop: 2440, Segment: LQ, Element: LQ01, Note: Allowed Values are:'AS' Form Type Code, 'UT' Centers for Medicare and Medicaid Services (CMS) Durable Medical Equipment Regional Carrier (DMERC) Certificate of Medical Necessity (CMN) Forms", "enum": ["AS", "UT"]}, "formIdentifier": {"type": "string", "description": "Loop: 2440, Segment: LQ, Element: LQ02"}, "supportingDocumentation": {"maxItems": 99, "minItems": 0, "type": "array", "description": "Loop: 2440, Segment: FRM", "items": {"required": ["questionNumber"], "type": "object", "properties": {"questionNumber": {"type": "string", "description": "Loop: 2440,  Segment: FRM, Element: FRM01"}, "questionResponseCode": {"type": "string", "description": "Loop: 2440,  Segment: FRM, Element: FRM02, Notes: Allowed Values are: 'N' No, 'W' Not Applicable, 'Y' Yes", "enum": ["N", "W", "Y"]}, "questionResponse": {"type": "string", "description": "Loop: 2440,  Segment: FRM, Element: FRM03"}, "questionResponseAsDate": {"type": "string", "description": "Loop: 2440,  Segment: FRM, Element: FRM04"}, "questionResponseAsPercent": {"type": "string", "description": "Loop: 2440,  Segment: FRM, Element: FRM05"}}, "description": "Loop: 2440, Segment: FRM"}}}, "description": "LQ and FRM"}}}, "description": "Loop 2400"}}}, "description": "Loop2300"}, "payToAddress": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "payToPlan": {"required": ["address", "organizationName", "primaryIdentifier", "primaryIdentifierTypeCode", "taxIdentificationNumber"], "type": "object", "properties": {"organizationName": {"type": "string", "description": "Loop: 2010AC, Segment: NM1, Element: NM103", "example": "Example Org"}, "primaryIdentifierTypeCode": {"type": "string", "description": "Loop: 2010AC, Segment: NM1, Element: NM108, Notes: 'PI' Payor Identification and 'XV' Centers for Medicare/Medicaid Services PlanID", "example": "PI", "enum": ["PI", "XV"]}, "primaryIdentifier": {"type": "string", "description": "Loop: 2010AC, Segment: NM1, Element: NM109"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "secondaryIdentifierTypeCode": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF01, Notes: '2U Payer Identification Number, 'FY' Claim Office Number, 'NF National Association of Insurance Commissioners'", "example": "2U", "enum": ["2U", "FY", "NF"]}, "secondaryIdentifier": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02", "example": "11"}, "taxIdentificationNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}}, "description": "2010AC"}, "payerAddress": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "usageIndicator": {"type": "string", "description": "Interchange Usage Indicator ISA15; T-Test Data, P-Production Data", "example": "T"}, "billing": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 2000A"}, "referring": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 2420F"}, "rendering": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 2420A"}, "ordering": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 2420E, Setting ProviderType equal to OrderingProvider is deprecated, please use ClaimInformation.serviceLines.orderingProvider", "deprecated": true}, "supervising": {"required": ["providerType"], "type": "object", "properties": {"providerType": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "npi": {"type": "string", "description": "NM109, Notes: National Provider Identifier", "example": "**********"}, "ssn": {"type": "string", "description": "REF02 when REF01=SY, Notes: The Social Security Number must be a string of nine numbers with no separators", "example": "000000000"}, "employerId": {"type": "string", "description": "REF02 when REF01=EI, Notes: The Employer Identification Number must be a string of exactly nine numbers with no separators", "example": "123456789"}, "commercialNumber": {"type": "string", "description": "REF02 when REF01=G2"}, "locationNumber": {"type": "string", "description": "REF02 when REF01=LU"}, "payerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=2U"}, "employerIdentificationNumber": {"type": "string", "description": "LOOP: 2010AC, Segment: REF, Element: REF02 when REF01=EI"}, "claimOfficeNumber": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=FY"}, "naic": {"type": "string", "description": "Loop: 2010AC, Segment: REF, Element: REF02 when REF01=NF, Notes: National Association of Insurance Commissioners (NAIC) Code"}, "stateLicenseNumber": {"type": "string", "description": "REF02 when REF01=0B"}, "providerUpinNumber": {"type": "string", "description": "REF02 when REF01=1G, Notes: UPINs must be formatted as either X99999 or XXX999"}, "taxonomyCode": {"type": "string", "description": "PRV03"}, "firstName": {"type": "string", "description": "NM104", "example": "johnone"}, "lastName": {"type": "string", "description": "NM103", "example": "doeone"}, "middleName": {"type": "string", "description": "NM105", "example": "middleone"}, "suffix": {"type": "string", "description": "NM107", "example": "<PERSON>"}, "organizationName": {"type": "string", "description": "NM103", "example": "HAPPY DOCTORS GROUPPRACTICE"}, "address": {"required": ["address1", "city"], "type": "object", "properties": {"address1": {"type": "string", "description": "Segment: N3, Element: N301", "example": "123 address1"}, "address2": {"type": "string", "description": "Segment: N3, Element: N302", "example": "apt 000"}, "city": {"type": "string", "description": "Segment: N4, Element: N401", "example": "city1"}, "state": {"type": "string", "description": "Segment: N4, Element: N402", "example": "wa"}, "postalCode": {"type": "string", "description": "Segment: N4, Element: N403", "example": "*********"}, "countryCode": {"type": "string", "description": "Segment: N4, Element: N404"}, "countrySubDivisionCode": {"type": "string", "description": "Segment: N4, Element: N407"}}, "description": "N3 and N4"}, "contactInformation": {"required": ["name"], "type": "object", "properties": {"name": {"type": "string", "description": "Segment: PER, Element: PER02 and PER01=IC", "example": "SUBMITTER CONTACT INFO"}, "phoneNumber": {"type": "string", "description": "Segment: PER, Element: PER04 (Provider, Submitter, Subscriber, Dependent) or PER06 (Provider, Submitter) or PER08 (Provider, Submitter), Note: Used when PER03=TE (Provider, Submitter, Subscriber, Dependent) or PER05=TE (Provider, Submitter) or PER07=TE (Provider, Submitter)", "example": "**********"}, "faxNumber": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This is used in (Provider, Submitter) when PER03=FX or PER05=FX or PER07=FX", "example": "**********"}, "email": {"type": "string", "description": "Segment: PER, Element: PER04 or PER06 or PER08, Note: This used in (Provider, Submitter) when PER03=EM or PER05=EM or PER07=EM", "example": "<EMAIL>"}, "phoneExtension": {"type": "string", "description": "Segment: PER, Element: PER06 (Provider, Submitter, Subscriber, Dependent) or PER08 (Provider, Submitter),Note: Used when PER05=EX (Provider, Submitter, Subscriber, Dependent) or PER07=EX (Provider, Submitter)", "example": "1234"}}, "description": "PER"}}, "description": "Loop: 2420D"}, "tradingPartnerName": {"type": "string", "description": "Loop 2010BB NM103"}}}