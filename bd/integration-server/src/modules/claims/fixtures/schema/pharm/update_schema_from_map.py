import json

# Load the schema file
with open('NCPDPSchema.json') as f:
    schema = json.load(f)

# Load the other file
with open('ecl.json') as f:
    other_file = json.load(f)
    other_file = {k.strip(): v for k, v in other_file.items()}

# Function to recursively update the schema
def update_schema(obj):
    if isinstance(obj, dict):
        for k, v in list(obj.items()):  # Create a copy of items
            if isinstance(v, dict):
                update_schema(v)
            elif isinstance(v, list):
                for item in v:
                    update_schema(item)
            if k == 'field_code' and v != '111-AM' and v in other_file:
                obj['code_map'] = other_file[v]
            if k == 'segment_requirements':
                obj[k] = dict(sorted(v.items()))
    elif isinstance(obj, list):
        for item in obj:
            update_schema(item)

# Update the schema
update_schema(schema)

# Save the updated schema
with open('updated_NCPDPSchema.json', 'w') as f:
    json.dump(schema, f, indent=2)
