{"*95": {"explanation": "Time Out", "possible_field": ""}, "*96": {"explanation": "Scheduled Downtime", "possible_field": ""}, "*97": {"explanation": "Payer Unavailable", "possible_field": ""}, "*98": {"explanation": "Connection To Payer Is Down", "possible_field": ""}, "00": {"explanation": "MI Means Missing or Invalid)", "possible_field": ""}, "01": {"explanation": "Missing or Invalid Bin Number", "possible_field": "101"}, "02": {"explanation": "Missing or Invalid Version or Release Number", "possible_field": "102"}, "03": {"explanation": "Missing or Invalid Transaction Code", "possible_field": "103"}, "04": {"explanation": "Missing or Invalid Processor Control Number", "possible_field": "104"}, "05": {"explanation": "Missing or Invalid Service Provider Number", "possible_field": "201"}, "06": {"explanation": "Missing or Invalid Group ID", "possible_field": "301"}, "07": {"explanation": "Missing or Invalid Cardholder ID ", "possible_field": "302"}, "08": {"explanation": "Missing or Invalid Person Code", "possible_field": "303"}, "09": {"explanation": "Missing or Invalid Date Of Birth", "possible_field": "304"}, "10": {"explanation": "Missing or Invalid Patient Gender Code", "possible_field": "305"}, "11": {"explanation": "Missing or Invalid Patient Relationship Code", "possible_field": "306"}, "12": {"explanation": "Missing or Invalid Place of Service", "possible_field": "307"}, "13": {"explanation": "Missing or Invalid Other Coverage Code", "possible_field": "308"}, "14": {"explanation": "Missing or Invalid Eligibility Clarification Code", "possible_field": "309"}, "15": {"explanation": "Missing or Invalid Date of Service", "possible_field": "401"}, "16": {"explanation": "Missing or Invalid Prescription or Service Reference Number", "possible_field": "402"}, "17": {"explanation": "Missing or Invalid Fill Number", "possible_field": "403"}, "19": {"explanation": "Missing or Invalid Days Supply", "possible_field": "405"}, "1C": {"explanation": "Missing or Invalid Smoker or Non-Smoker Code", "possible_field": "334"}, "1E": {"explanation": "Missing or Invalid Prescriber Location Code", "possible_field": "467"}, "1R": {"explanation": "Version or Release Not Supported", "possible_field": "102-A2"}, "1S": {"explanation": "Transaction Code or Type Not Supported", "possible_field": "103-A3"}, "1T": {"explanation": "PCN Must Contain Processor or Payer Assigned Value", "possible_field": "104-A4"}, "1U": {"explanation": "Transaction Count Does Not Match Number of Transactions", "possible_field": "109-A9"}, "1V": {"explanation": "Multiple Transactions Not Supported", "possible_field": "109-A9"}, "1W": {"explanation": "Multi-Ingredient Compound Must Be A Single Transaction", "possible_field": "109-A9"}, "1X": {"explanation": "Vendor Not Certified For Processor or Payer", "possible_field": "110-AK"}, "1Y": {"explanation": "Claim Segment Required For Adjudication", "possible_field": "111-AM"}, "1Z": {"explanation": "Clinical Segment Required For Adjudication", "possible_field": "111-AM"}, "20": {"explanation": "Missing or Invalid Compound Code", "possible_field": "406"}, "21": {"explanation": "Missing or Invalid Product or Service ID", "possible_field": "407, 489-TE"}, "22": {"explanation": "Missing or Invalid Dispense As Written (DAW) or Product Selection Code ", "possible_field": "408"}, "23": {"explanation": "Missing or Invalid Ingredient Cost Submitted", "possible_field": "409"}, "25": {"explanation": "Missing or Invalid Prescriber ID", "possible_field": "411"}, "26": {"explanation": "Missing or Invalid Unit Of Measure", "possible_field": "600"}, "28": {"explanation": "Missing or Invalid Date Prescription Written", "possible_field": "414"}, "29": {"explanation": "Missing or Invalid Number Of Refills Authorized", "possible_field": "415"}, "2A": {"explanation": "Missing or Invalid Medigap ID", "possible_field": "239"}, "2B": {"explanation": "Missing or Invalid Medicaid Indicator", "possible_field": "360"}, "2C": {"explanation": "Missing or Invalid Pregnancy Indicator", "possible_field": "335"}, "2D": {"explanation": "Missing or Invalid Provider Accept Assignment Indicator", "possible_field": "361"}, "2E": {"explanation": "Missing or Invalid Primary Care Provider ID Qualifier", "possible_field": "468"}, "2G": {"explanation": "Missing or Invalid Compound Ingredient Modifier Code Count", "possible_field": "362"}, "2H": {"explanation": "Missing or Invalid Compound Ingredient Modifier Code", "possible_field": "363"}, "2J": {"explanation": "Missing or Invalid Prescriber First Name", "possible_field": "364"}, "2K": {"explanation": "Missing or Invalid Prescriber Street Address", "possible_field": "365"}, "2M": {"explanation": "Missing or Invalid Prescriber City Address", "possible_field": "366"}, "2N": {"explanation": "Missing or Invalid Prescriber State or Province Address", "possible_field": "367"}, "2P": {"explanation": "Missing or Invalid Prescriber Zip or Postal Zone", "possible_field": "368"}, "2Q": {"explanation": "Missing or Invalid Additional Documentation Type ID", "possible_field": "369"}, "2R": {"explanation": "Missing or Invalid Length of Need", "possible_field": "370"}, "2S": {"explanation": "Missing or Invalid Length of Need Qualifier", "possible_field": "371"}, "2T": {"explanation": "Missing or Invalid Prescriber or Supplier Date Signed", "possible_field": "372"}, "2U": {"explanation": "Missing or Invalid Request Status", "possible_field": "373"}, "2V": {"explanation": "Missing or Invalid Request Period Begin Date", "possible_field": "374"}, "2W": {"explanation": "Missing or Invalid Request Period Recert or Revised Date", "possible_field": "375"}, "2X": {"explanation": "Missing or Invalid Supporting Documentation", "possible_field": "376"}, "2Z": {"explanation": "Missing or Invalid Question Number or Letter Count ", "possible_field": "377"}, "32": {"explanation": "Missing or Invalid Level Of Service", "possible_field": "418"}, "33": {"explanation": "Missing or Invalid Prescription Origin Code", "possible_field": "419"}, "34": {"explanation": "Missing or Invalid Submission Clarification Code", "possible_field": "420"}, "35": {"explanation": "Missing or Invalid Primary Care Provider ID", "possible_field": "421"}, "38": {"explanation": "Missing or Invalid Basis Of Cost Determination\r\n", "possible_field": "423"}, "39": {"explanation": "Missing or Invalid Diagnosis Code", "possible_field": "424"}, "3A": {"explanation": "Missing or Invalid Request Type ", "possible_field": "498-PA"}, "3B": {"explanation": "Missing or Invalid Request Period Date-Begin", "possible_field": "498-PB"}, "3C": {"explanation": "Missing or Invalid Request Period Date-End", "possible_field": "498-PC"}, "3D": {"explanation": "Missing or Invalid Basis Of Request", "possible_field": "498-<PERSON>"}, "3E": {"explanation": "Missing or Invalid Authorized Representative First Name", "possible_field": "498-P<PERSON>"}, "3F": {"explanation": "Missing or Invalid Authorized Representative Last Name", "possible_field": "498-PF"}, "3G": {"explanation": "Missing or Invalid Authorized Representative Street Address", "possible_field": "498-P<PERSON>"}, "3H": {"explanation": "Missing or Invalid Authorized Representative City Address", "possible_field": "498-P<PERSON>"}, "3J": {"explanation": "Missing or Invalid Authorized Representative State or Province Address", "possible_field": "498-<PERSON><PERSON>"}, "3K": {"explanation": "Missing or Invalid Authorized Representative Zip or Postal Zone", "possible_field": "498-<PERSON><PERSON>"}, "3M": {"explanation": "Missing or Invalid Prescriber Phone Number", "possible_field": "498-PM"}, "3N": {"explanation": "Missing or Invalid Prior Authorized Number-Assigned", "possible_field": "498-PY"}, "3P": {"explanation": "Missing or Invalid Authorization Number", "possible_field": "503"}, "3Q": {"explanation": "Missing or Invalid Facility Name", "possible_field": "385"}, "3R": {"explanation": "Prior Authorization Not Required", "possible_field": "407"}, "3S": {"explanation": "Missing or Invalid Prior Authorization Supporting Documentation", "possible_field": "498-PP"}, "3T": {"explanation": "Active Prior Authorization Exists Resubmit At Expiration Of Prior Authorization", "possible_field": ""}, "3U": {"explanation": "Missing or Invalid Facility Street Address", "possible_field": "386"}, "3V": {"explanation": "Missing or Invalid Facility State or Province Address", "possible_field": "387"}, "3W": {"explanation": "Prior Authorization In Process", "possible_field": ""}, "3X": {"explanation": "Authorization Number Not Found", "possible_field": "503"}, "3Y": {"explanation": "Prior Authorization Denied", "possible_field": ""}, "40": {"explanation": "Pharmacy Not Contracted With Plan On Date Of Service", "possible_field": "None"}, "41": {"explanation": "Submit Bill To Other Processor Or Primary Payer", "possible_field": "None"}, "4B": {"explanation": "Missing or Invalid Question Number or Letter", "possible_field": "378"}, "4C": {"explanation": "Missing or Invalid Coordination Of Benefits or Other Payments Count", "possible_field": "337"}, "4D": {"explanation": "Missing or Invalid Question Percent Response", "possible_field": "379"}, "4E": {"explanation": "Missing or Invalid Primary Care Provider Last Name", "possible_field": "570"}, "4G": {"explanation": "Missing or Invalid Question Date Response", "possible_field": "380"}, "4H": {"explanation": "Missing or Invalid Question Dollar Amount\r\nResponse", "possible_field": "381"}, "4J": {"explanation": "Missing or Invalid Question Numeric Response", "possible_field": "382"}, "4K": {"explanation": "Missing or Invalid Question Alphanumeric Response", "possible_field": "383"}, "4M": {"explanation": "Compound Ingredient Modifier Code Count Does Not Match Number of Repetitions", "possible_field": "362"}, "4N": {"explanation": "Question Number or Letter Count Does Not Match Number of Repetitions", "possible_field": "377"}, "4P": {"explanation": "Question Number or Letter Not Valid for Identified Document ", "possible_field": "378"}, "4Q": {"explanation": "Question Response Not Appropriate for Question Number or Letter", "possible_field": "378"}, "4R": {"explanation": "Required Question\r\nNumber or Letter\r\nResponse for Indicated Document Missing", "possible_field": "378"}, "4S": {"explanation": "Compound Product ID Requires a Modifier Code", "possible_field": ""}, "4T": {"explanation": "Missing or Invalid Additional Documentation Segment", "possible_field": "111"}, "4W": {"explanation": "Must Fill Through Specialty Pharmacy", "possible_field": "407, 489"}, "4X": {"explanation": "Missing or Invalid Patient Residence", "possible_field": "384-4X"}, "4Y": {"explanation": "Patient Residence not supported by plan", "possible_field": "384-4X"}, "4Z": {"explanation": "Place of Service Not Support By Plan", "possible_field": "307-C7"}, "50": {"explanation": "Non-Matched Pharmacy Number", "possible_field": "201"}, "51": {"explanation": "Non-Matched Group ID", "possible_field": "301"}, "52": {"explanation": "Non-Matched Cardholder ID", "possible_field": "302"}, "53": {"explanation": "Non-Matched Person Code", "possible_field": "303"}, "54": {"explanation": "Non-Matched Product or Service ID Number", "possible_field": "407, 489-TE"}, "55": {"explanation": "Non-Matched Product Package Size", "possible_field": "407, 489-TE"}, "56": {"explanation": "Non-Matched Prescriber ID", "possible_field": "411"}, "58": {"explanation": "Non-Matched Primary Prescriber", "possible_field": "421"}, "5C": {"explanation": "Missing or Invalid Other Payer Coverage Type", "possible_field": "338"}, "5E": {"explanation": "Missing or Invalid Other Payer Reject Count", "possible_field": "471"}, "5J": {"explanation": "Missing or Invalid Facility City Address", "possible_field": "388"}, "60": {"explanation": "Product or Service Not Covered For Patient Age", "possible_field": "302, 304, 401, 407, 489-TE"}, "61": {"explanation": "Product or Service Not Covered For Patient Gender", "possible_field": "302, 305, 407, 489-TE"}, "62": {"explanation": "Patient or Card Holder ID Name Mismatch", "possible_field": "310, 311, 312, 313, 302"}, "63": {"explanation": "Institutionalized Patient Product or Service ID Not Covered", "possible_field": ""}, "64": {"explanation": "Claim Submitted Does Not Match Prior Authorization", "possible_field": "201, 404, 407, 442-E7, 461-EU, 462-EV, 489-TE"}, "65": {"explanation": "Patient Is Not Covered", "possible_field": "303, 306"}, "66": {"explanation": "Patient Age Exceeds Maximum Age", "possible_field": "303, 304, 306"}, "67": {"explanation": "Filled Before Coverage Effective", "possible_field": "401"}, "68": {"explanation": "Filled After Coverage Expired", "possible_field": "401"}, "69": {"explanation": "Filled After Coverage Terminated", "possible_field": "401"}, "6C": {"explanation": "Missing or Invalid Other Payer ID Qualifier", "possible_field": "339"}, "6D": {"explanation": "Missing or Invalid Facility Zip or Postal Zone", "possible_field": "389"}, "6E": {"explanation": "Missing or Invalid Other Payer Reject Code", "possible_field": "472"}, "6G": {"explanation": "Coordination Of Benefits or Other Payments Segment Required For Adjudication", "possible_field": "111-AM"}, "6H": {"explanation": "Coupon Segment Required For Adjudication", "possible_field": "111-AM"}, "6J": {"explanation": "Insurance Segment Required For Adjudication,", "possible_field": "111-AM"}, "6K": {"explanation": "Patient Segment Required For Adjudication", "possible_field": "111-AM"}, "6M": {"explanation": "Pharmacy Provider Segment Required For Adjudication", "possible_field": "111-AM"}, "6N": {"explanation": "Prescriber Segment Required For Adjudication", "possible_field": "111-AM"}, "6P": {"explanation": "Pricing Segment Required For Adjudication", "possible_field": "111-AM"}, "6Q": {"explanation": "Prior Authorization Segment Required For Adjudication", "possible_field": "111-AM"}, "6R": {"explanation": "Workers Compensation Segment Required For Adjudication", "possible_field": "111-AM"}, "6S": {"explanation": "Transaction Segment Required For Adjudication", "possible_field": "111-AM"}, "6T": {"explanation": "Compound Segment Required For Adjudication", "possible_field": "111-AM"}, "6U": {"explanation": "Compound Segment Incorrectly Formatted", "possible_field": "111-AM"}, "6V": {"explanation": "Multi-ingredient Compounds Not Supported,", "possible_field": "111-AM"}, "6W": {"explanation": "DUR or PPS Segment Required For Adjudication", "possible_field": "111-AM"}, "6X": {"explanation": "DUR or PPS Segment Incorrectly Formatted", "possible_field": "111-AM"}, "6Y": {"explanation": "Not Authorized To Submit Electronically", "possible_field": "201-B1"}, "6Z": {"explanation": "Provider Not Eligible To Perform Service or Dispense Product", "possible_field": "201-B1"}, "70": {"explanation": "Product or Service Not Covered", "possible_field": "407, 498"}, "71": {"explanation": "Prescriber Is Not Covered", "possible_field": "411"}, "72": {"explanation": "Primary Prescriber Is Not Covered", "possible_field": "421"}, "73": {"explanation": "Refills Are Not Covered", "possible_field": "402, 403"}, "74": {"explanation": "Other Carrier Payment Meets Or Exceeds Payable", "possible_field": "409, 442, 481-HA, 482-G3"}, "75": {"explanation": "Prior Authorization Required", "possible_field": "462, 489-TE"}, "76": {"explanation": "Plan Limitations Exceeded", "possible_field": "405, 442"}, "77": {"explanation": "Discontinued Product or Service ID Number", "possible_field": "407, 489-TE"}, "78": {"explanation": "Cost Exceeds Maximum", "possible_field": "407, 409, 442, 448-ED, 449-EE, 481-HA, 482-G3, 489-TE"}, "79": {"explanation": "Refill Too Soon", "possible_field": "401, 403, 405"}, "7A": {"explanation": "Provider Does Not Match \r\nAuthorization On File\r\n\u0007\r\n\u0007", "possible_field": "201-B1"}, "7B": {"explanation": "Service Provider ID Qualifier Value Not Supported For Processor or Payer", "possible_field": "202-B2"}, "7C": {"explanation": "Missing or Invalid Other Payer ID", "possible_field": "340"}, "7D": {"explanation": "Non-Matched DOB", "possible_field": "304-C4"}, "7E": {"explanation": "Missing or Invalid DUR or PPS Code Counter", "possible_field": "473"}, "7G": {"explanation": "Future Date Not Allowed For DOB", "possible_field": "304-C4"}, "7H": {"explanation": "Non-Matched Gender Code", "possible_field": "305-C5"}, "7J": {"explanation": "Patient Relationship Code Not Supported", "possible_field": "306-C6"}, "7K": {"explanation": "Discrepancy Between Other Coverage Code And Other Payer Amt., ", "possible_field": "308-C8"}, "7M": {"explanation": "Discrepancy Between Other Coverage Code And Other Coverage Information On File", "possible_field": "308-C8"}, "7N": {"explanation": "Patient ID Qualifier Submitted Not Supported", "possible_field": "331-CX"}, "7P": {"explanation": "Coordination Of Benefits or Other Payments Count Exceeds Number of Supported Payers ", "possible_field": "337-4C"}, "7Q": {"explanation": "Other Payer ID Qualifier Not Supported", "possible_field": "339-6C"}, "7R": {"explanation": "Other Payer Amount Paid Count Exceeds Number of Supported Groupings", "possible_field": "341-<PERSON><PERSON>"}, "7S": {"explanation": "Other Payer Amount Paid Qualifier Not Supported", "possible_field": "342-<PERSON>"}, "7T": {"explanation": "Quantity Intended To Be Dispensed Required For Partial Fill Transaction", "possible_field": "344-<PERSON><PERSON>"}, "7U": {"explanation": "Days Supply Intended To Be Dispensed Required For Partial Fill Transaction", "possible_field": "345-HG"}, "7V": {"explanation": "Duplicate Refills,", "possible_field": "403-D3"}, "7W": {"explanation": "Refills Exceed allowable Refills", "possible_field": "403-D3"}, "7X": {"explanation": "Days Supply Exceeds Plan Limitation", "possible_field": "405-D5"}, "7Y": {"explanation": "Compounds Not Covered,", "possible_field": "406-D6"}, "7Z": {"explanation": "Compound Requires Two Or More Ingredients,", "possible_field": "406-D6"}, "80": {"explanation": "Drug-Diagnosis Mismatch", "possible_field": "407, 424"}, "81": {"explanation": "<PERSON><PERSON><PERSON>", "possible_field": "401"}, "82": {"explanation": "Claim Is Post-Dated", "possible_field": "401"}, "83": {"explanation": "Duplicate Paid or Captured <PERSON><PERSON><PERSON>", "possible_field": "201, 401, 402, 403, 407"}, "84": {"explanation": "<PERSON>laim Has Not Been Paid or Captured", "possible_field": "201, 401, 402"}, "85": {"explanation": "Claim Not Processed", "possible_field": "None"}, "86": {"explanation": "Submit Manual Reversal", "possible_field": "None"}, "87": {"explanation": "Reversal Not Processed", "possible_field": "None"}, "88": {"explanation": "DUR Reject Error", "possible_field": ""}, "89": {"explanation": "Rejected C<PERSON><PERSON>", "possible_field": ""}, "8A": {"explanation": "Compound Requires At Least One Covered Ingredient", "possible_field": "406-D6"}, "8B": {"explanation": "Compound Segment Missing On A Compound Claim", "possible_field": "406-D6"}, "8C": {"explanation": "Missing or Invalid Facility ID", "possible_field": "336"}, "8D": {"explanation": "Compound Segment Present On A Non-Compound Claim", "possible_field": "406-D6"}, "8E": {"explanation": "Missing or Invalid DUR or PPS Level Of Effort", "possible_field": "474"}, "8G": {"explanation": "Primary Product In A Compound Claim Is Not Zero", "possible_field": "407-D7"}, "8H": {"explanation": "Product or Service Only Covered On Compound Claim", "possible_field": "407-D7"}, "8J": {"explanation": "Incorrect Product or Service ID For Processor or Payer", "possible_field": "407-D7, 489-TE "}, "8K": {"explanation": "DAW Code Not Supported", "possible_field": "408-D8"}, "8M": {"explanation": "Sum Of Compound Ingredient Costs Does Not Equal Ingredient Cost Submitted", "possible_field": "409-D9"}, "8N": {"explanation": "Future Date Prescription Written Not Allowed,", "possible_field": "414-<PERSON>"}, "8P": {"explanation": "Date Written Different On Previous Filling", "possible_field": "414-<PERSON>"}, "8Q": {"explanation": "Excessive Refills Authorized", "possible_field": "415-DF"}, "8R": {"explanation": "Submission Clarification Code Not Supported", "possible_field": "420-DK"}, "8S": {"explanation": "Basis Of Cost Not Supported ", "possible_field": "423-D<PERSON>"}, "8T": {"explanation": "U&C Must Be Greater Than Zero ", "possible_field": "426-<PERSON><PERSON>"}, "8U": {"explanation": "GAD Must Be Greater Than Zero", "possible_field": "430-DU"}, "8V": {"explanation": "Negative Dollar Amount Is Not Supported In The Other Payer Amount Paid Field,", "possible_field": "431-<PERSON><PERSON>"}, "8W": {"explanation": "Discrepancy Between Other Coverage Code and Other Payer Amount Paid", "possible_field": "431-<PERSON><PERSON>"}, "8X": {"explanation": "Collection From Cardholder Not Allowed,", "possible_field": "433-D<PERSON>"}, "8Y": {"explanation": "Excessive Amount Collected", "possible_field": "433-D<PERSON>"}, "8Z": {"explanation": "Product or Service ID Qualifier Value Not Supported", "possible_field": "436-E1"}, "90": {"explanation": "Host Hung Up", "possible_field": "Host Disconnected Before Session Completed"}, "91": {"explanation": "Host Response Error", "possible_field": "Response Not In Appropriate Format To Be Displayed"}, "92": {"explanation": "System Unavailable or Host Unavailable", "possible_field": "Processing Host Did Not Accept Transaction or Did Not Respond Within Time Out Period"}, "99": {"explanation": "Host Processing Error", "possible_field": "Do Not Retransmit <PERSON><PERSON><PERSON>(s)"}, "9B": {"explanation": "Reason For Service Code Value Not Supported", "possible_field": "439-E4"}, "9C": {"explanation": "Professional Service Code Value Not Supported", "possible_field": "440-E5"}, "9D": {"explanation": "Result Of Service Code Value Not Supported", "possible_field": "441-E6"}, "9E": {"explanation": "Quantity Does Not Match Dispensing Unit", "possible_field": "442-E7"}, "9G": {"explanation": "Quantity Dispensed Exceeds Maximum Allowed,", "possible_field": "442-E7"}, "9H": {"explanation": "Quantity Not Valid For Product or Service ID Submitted", "possible_field": "442-E7"}, "9J": {"explanation": "Future Other Payer Date Not Allowed", "possible_field": "443-E8"}, "9K": {"explanation": "Compound Ingredient Component Count Exceeds Number Of Ingredients Supported", "possible_field": "447-EC"}, "9M": {"explanation": "Minimum Of Two Ingredients Required", "possible_field": "447-EC"}, "9N": {"explanation": "Compound Ingredient Quantity Exceeds Maximum Allowed ", "possible_field": "448-<PERSON><PERSON>"}, "9P": {"explanation": "Compound Ingredient Drug Cost Must Be Greater Than Zero", "possible_field": "449-<PERSON><PERSON>"}, "9Q": {"explanation": "Route Of Administration Submitted Not Covered ", "possible_field": "995-E2"}, "9R": {"explanation": "Prescription or Service Reference Number Qualifier Submitted Not Covered", "possible_field": "455-<PERSON><PERSON>"}, "9S": {"explanation": "Future Associated Prescription or Service Date Not Allowed", "possible_field": "457-EP"}, "9T": {"explanation": "Prior Authorization Type Code Submitted Not Covered", "possible_field": "461-EU"}, "9U": {"explanation": "Provider ID Qualifier Submitted Not Covered", "possible_field": "465-<PERSON><PERSON>"}, "9V": {"explanation": "Prescriber ID Qualifier Submitted Not Covered", "possible_field": "466-<PERSON><PERSON>"}, "9W": {"explanation": "DUR or PPS Code Counter Exceeds Number Of Occurrences Supported", "possible_field": "473-7E"}, "9X": {"explanation": "Coupon Type Submitted Not Covered", "possible_field": "485-<PERSON><PERSON>"}, "9Y": {"explanation": "Compound Product ID Qualifier Submitted Not Covered", "possible_field": "488-<PERSON><PERSON>"}, "9Z": {"explanation": "Duplicate Product ID In Compound", "possible_field": "489-TE"}, "A5": {"explanation": "Not Covered Under Part D Law", "possible_field": ""}, "A6": {"explanation": "This Medication May Be Covered Under Part B", "possible_field": ""}, "A7": {"explanation": "Missing or Invalid Internal Control Number", "possible_field": "993-A7"}, "A9": {"explanation": "Missing or Invalid Transaction Count", "possible_field": "109"}, "AA": {"explanation": "Patient Spenddown Not Met", "possible_field": ""}, "AB": {"explanation": "Date Written Is After Date Filled", "possible_field": ""}, "AC": {"explanation": "Product Not Covered Non-Participating Manufacturer", "possible_field": "489-TE, 407-D7"}, "AD": {"explanation": "Billing Provider Not Eligible To Bill This Claim Type", "possible_field": ""}, "AE": {"explanation": "QMB (Qualified Medicare Beneficiary)-Bill <PERSON>", "possible_field": ""}, "AF": {"explanation": "Patient Enrolled Under Managed Care", "possible_field": ""}, "AG": {"explanation": "Days Supply Limitation For Product or Service", "possible_field": "489-TE, 407-D7"}, "AH": {"explanation": "Unit Dose Packaging Only Payable For Nursing Home Recipients", "possible_field": ""}, "AJ": {"explanation": "Generic Drug Required", "possible_field": "489-TE, 407-D7"}, "AK": {"explanation": "Missing or Invalid Software Vendor or Certification ID ", "possible_field": "110"}, "AM": {"explanation": "Missing or Invalid Segment Identification", "possible_field": "111"}, "AQ": {"explanation": "Missing or Invalid Facility Segment", "possible_field": "111"}, "B2": {"explanation": "Missing or Invalid Service Provider ID Qualifier", "possible_field": "202"}, "BA": {"explanation": "Compound Basis of Cost Determination Submitted Not Covered ", "possible_field": "490-UE"}, "BB": {"explanation": "Diagnosis Code Qualifier Submitted Not Covered", "possible_field": "492-WE"}, "BC": {"explanation": "Future Measurement Date Not Allowed", "possible_field": "494-<PERSON><PERSON>"}, "BD": {"explanation": "Sender Not Authorized To Submit File Type", "possible_field": "702"}, "BE": {"explanation": "Missing or Invalid Professional Service Fee Submitted", "possible_field": "477"}, "BF": {"explanation": "Missing or Invalid File Type", "possible_field": "702"}, "BG": {"explanation": "Sender ID Not Certified For Processor or Payer", "possible_field": "880-K1"}, "BH": {"explanation": "Missing or Invalid Sender ID", "possible_field": "880-K1"}, "BJ": {"explanation": "Transmission Type Submitted Not Supported,", "possible_field": "880-K6"}, "BK": {"explanation": "Missing or Invalid Transmission Type", "possible_field": "880-K6"}, "BM": {"explanation": "Missing or Invalid Narrative Message", "possible_field": "390"}, "CA": {"explanation": "Missing or Invalid Patient First Name", "possible_field": "310"}, "CB": {"explanation": "Missing or Invalid Patient Last Name", "possible_field": "311"}, "CC": {"explanation": "Missing or Invalid Cardholder First Name", "possible_field": "312"}, "CD": {"explanation": "Missing or Invalid Cardholder Last Name", "possible_field": "313"}, "CE": {"explanation": "Missing or Invalid Home Plan", "possible_field": "314"}, "CF": {"explanation": "Missing or Invalid Employer Name", "possible_field": "315"}, "CG": {"explanation": "Missing or Invalid Employer Street Address", "possible_field": "316"}, "CH": {"explanation": "Missing or Invalid Employer City Address", "possible_field": "317"}, "CI": {"explanation": "Missing or Invalid Employer State or Province Address", "possible_field": "318"}, "CJ": {"explanation": "Missing or Invalid Employer Zip Postal Zone", "possible_field": "319"}, "CK": {"explanation": "Missing or Invalid Employer Phone Number", "possible_field": "320"}, "CL": {"explanation": "Missing or Invalid Employer Contact Name", "possible_field": "321"}, "CM": {"explanation": "Missing or Invalid Patient Street Address", "possible_field": "322"}, "CN": {"explanation": "Missing or Invalid Patient City Address", "possible_field": "323"}, "CO": {"explanation": "Missing or Invalid Patient State or Province Address", "possible_field": "324"}, "CP": {"explanation": "Missing or Invalid Patient Zip or Postal Zone ", "possible_field": "325"}, "CQ": {"explanation": "Missing or Invalid Patient Phone Number", "possible_field": "326"}, "CR": {"explanation": "Missing or Invalid Carrier ID", "possible_field": "327"}, "CW": {"explanation": "Missing or Invalid Alternate ID", "possible_field": "330"}, "CX": {"explanation": "Missing or Invalid Patient ID Qualifier", "possible_field": "331"}, "CY": {"explanation": "Missing or Invalid Patient ID", "possible_field": "332"}, "CZ": {"explanation": "Missing or Invalid Employer ID", "possible_field": "333"}, "DC": {"explanation": "Missing or Invalid Dispensing Fee Submitted", "possible_field": "412"}, "DN": {"explanation": "Missing or Invalid Basis Of Cost Determination", "possible_field": "423, 490-U<PERSON>"}, "DQ": {"explanation": "Missing or Invalid Usual And Customary Charge", "possible_field": "426"}, "DR": {"explanation": "Missing or Invalid Prescriber Last Name", "possible_field": "427"}, "DT": {"explanation": "Missing or Invalid Special Packaging Indicator", "possible_field": "429"}, "DU": {"explanation": "Missing or Invalid Gross Amount Due", "possible_field": "430"}, "DV": {"explanation": "Missing or Invalid Other Payer Amount Paid", "possible_field": "431"}, "DX": {"explanation": "Missing or Invalid Patient Paid Amount Submitted", "possible_field": "433"}, "DY": {"explanation": "Missing or Invalid Date Of Injury", "possible_field": "434"}, "DZ": {"explanation": "Missing or Invalid Claim or Reference ID", "possible_field": "435"}, "E1": {"explanation": "Missing or Invalid Product or Service ID Qualifier", "possible_field": "436, 488-<PERSON><PERSON>"}, "E2": {"explanation": "Missing or Invalid Route of Administration", "possible_field": "995-E2"}, "E3": {"explanation": "Missing or Invalid Incentive Amount Submitted", "possible_field": "438"}, "E4": {"explanation": "Missing or Invalid Reason For Service Code", "possible_field": "439"}, "E5": {"explanation": "Missing or Invalid Professional Service Code", "possible_field": "440"}, "E6": {"explanation": "Missing or Invalid Result Of Service Code", "possible_field": "441"}, "E7": {"explanation": "Missing or Invalid Quantity Dispensed", "possible_field": "442"}, "E8": {"explanation": "Missing or Invalid Other Payer Date", "possible_field": "443"}, "E9": {"explanation": "Missing or Invalid Provider ID", "possible_field": "444"}, "EA": {"explanation": "Missing or Invalid Originally Prescribed Product or Service Code", "possible_field": "445"}, "EB": {"explanation": "Missing or Invalid Originally Prescribed Quantity", "possible_field": "446"}, "EC": {"explanation": "Missing or Invalid Compound Ingredient Component Count", "possible_field": "447"}, "ED": {"explanation": "Missing or Invalid Compound Ingredient Quantity", "possible_field": "448"}, "EE": {"explanation": "Missing or Invalid Compound Ingredient Drug Cost", "possible_field": "449"}, "EF": {"explanation": "Missing or Invalid Compound Dosage Form Description Code", "possible_field": "450"}, "EG": {"explanation": "Missing or Invalid Compound Dispensing Unit Form Indicator", "possible_field": "451"}, "EJ": {"explanation": "Missing or Invalid Originally Prescribed Product or Service ID Qualifier", "possible_field": "453"}, "EK": {"explanation": "Missing or Invalid Scheduled Prescription ID Number", "possible_field": "454"}, "EM": {"explanation": "Missing or Invalid Prescription or Service Reference Number Qualifier", "possible_field": "455"}, "EN": {"explanation": "Missing or Invalid Associated Prescription or Service Reference Number", "possible_field": "456"}, "EP": {"explanation": "Missing or Invalid Associated Prescription or Service Date", "possible_field": "457"}, "ER": {"explanation": "Missing or Invalid Procedure Modifier Code", "possible_field": "459"}, "ET": {"explanation": "Missing or Invalid Quantity Prescribed", "possible_field": "460"}, "EU": {"explanation": "Missing or Invalid Prior Authorization Type Code", "possible_field": "461"}, "EV": {"explanation": "Missing or Invalid Prior Authorization Number Submitted", "possible_field": "462"}, "EW": {"explanation": "Missing or Invalid Intermediary Authorization Type ID", "possible_field": "463"}, "EX": {"explanation": "Missing or Invalid Intermediary Authorization ID", "possible_field": "464"}, "EY": {"explanation": "Missing or Invalid Provider ID Qualifier", "possible_field": "465"}, "EZ": {"explanation": "Missing or Invalid Prescriber ID Qualifier", "possible_field": "466"}, "FO": {"explanation": "Missing or Invalid Plan ID", "possible_field": "524"}, "G1": {"explanation": "Missing or Invalid Compound Type", "possible_field": "996"}, "G2": {"explanation": "Missing or Invalid CMS Part D Defined Qualified Facility", "possible_field": "997"}, "G4": {"explanation": "Physician must contact plan", "possible_field": ""}, "G5": {"explanation": "Pharmacist must contact plan", "possible_field": ""}, "G6 ": {"explanation": "Pharmacy Not Contracted in Specialty Network ", "possible_field": ""}, "G7": {"explanation": "Pharmacy Not Contracted in Home Infusion Network", "possible_field": ""}, "G8": {"explanation": "Pharmacy Not Contracted in Long Term Care Network", "possible_field": ""}, "G9": {"explanation": "Pharmacy Not Contracted in 90 Day Retail Network (this message would be used when the pharmacy is not contracted to provide a 90 days supply of drugs)", "possible_field": ""}, "GE": {"explanation": "Missing or Invalid Percentage Sales Tax Amount Submitted", "possible_field": "482"}, "H1": {"explanation": "Missing or Invalid Measurement Time", "possible_field": "495"}, "H2": {"explanation": "Missing or Invalid Measurement Dimension", "possible_field": "496"}, "H3": {"explanation": "Missing or Invalid Measurement Unit", "possible_field": "497"}, "H4": {"explanation": "Missing or Invalid Measurement Value", "possible_field": "499"}, "H5": {"explanation": "Missing or Invalid Primary Care Provider Location Code", "possible_field": "469"}, "H6": {"explanation": "Missing or Invalid DUR Co-Agent ID", "possible_field": "476"}, "H7": {"explanation": "Missing or Invalid Other Amount Claimed Submitted Count", "possible_field": "478"}, "H8": {"explanation": "Missing or Invalid Other Amount Claimed Submitted Qualifier", "possible_field": "479"}, "H9": {"explanation": "Missing or Invalid Other Amount Claimed Submitted", "possible_field": "480"}, "HA": {"explanation": "Missing or Invalid Flat Sales Tax Amount Submitted", "possible_field": "481"}, "HB": {"explanation": "Missing or Invalid Other Payer Amount Paid <PERSON>", "possible_field": "341"}, "HC": {"explanation": "Missing or Invalid Other Payer Amount Pa<PERSON> Qualifier", "possible_field": "342"}, "HD": {"explanation": "Missing or Invalid Dispensing Status", "possible_field": "343"}, "HE": {"explanation": "Missing or Invalid Percentage Sales Tax Rate Submitted", "possible_field": "483"}, "HF": {"explanation": "Missing or Invalid Quantity Intended To Be Dispensed", "possible_field": "344"}, "HG": {"explanation": "Missing or Invalid Days Supply Intended To Be Dispensed", "possible_field": "345"}, "HN": {"explanation": "Missing or Invalid Patient E-Mail Address", "possible_field": "350"}, "J9": {"explanation": "Missing or Invalid DUR Co-Agent ID Qualifier", "possible_field": "475"}, "JE": {"explanation": "Missing or Invalid Percentage Sales Tax Basis Submitted", "possible_field": "484"}, "K5": {"explanation": "Missing or Invalid Transaction Reference Number", "possible_field": "880"}, "KE": {"explanation": "Missing or Invalid Coupon Type", "possible_field": "485"}, "M1": {"explanation": "Patient Not Covered In This Aid Category", "possible_field": ""}, "M2": {"explanation": "Recipient Locked In", "possible_field": ""}, "M3": {"explanation": "Host PA or MC Error", "possible_field": ""}, "M4": {"explanation": "Prescription or Service Reference Number or Time Limit Exceeded", "possible_field": ""}, "M5": {"explanation": "Requires Manual Claim", "possible_field": ""}, "M6": {"explanation": "Host Eligibility Error", "possible_field": ""}, "M7": {"explanation": "Host Drug File Error", "possible_field": ""}, "M8": {"explanation": "Host Provider File Error", "possible_field": ""}, "ME": {"explanation": "Missing or Invalid Coupon Number", "possible_field": "486"}, "MG  ": {"explanation": "Missing or Invalid Other Payer BIN Number ", "possible_field": "990"}, "MH": {"explanation": "Missing or Invalid Other Payer Processor Control Number", "possible_field": "991"}, "MJ": {"explanation": "Missing or Invalid Other Payer Group ID", "possible_field": "992"}, "MK": {"explanation": "Non-Matched Other Payer BIN Number ", "possible_field": "990"}, "MM": {"explanation": "Non-Matched Other Payer Processor Control Number", "possible_field": "991"}, "MN": {"explanation": "Non-Matched Other Payer Group ID", "possible_field": "992"}, "MP": {"explanation": "Non-Matched Other Payer Cardholder ID", "possible_field": "356"}, "MR": {"explanation": "Drug Not on Formulary", "possible_field": "407"}, "MS": {"explanation": "More than 1 Cardholder Found  Narrow Search Criteria", "possible_field": "302"}, "MT": {"explanation": "Missing or Invalid Patient Assignment Indicator (Direct Member Reimbursement Indicator)", "possible_field": "391"}, "MU": {"explanation": "Missing or Invalid Benefit Stage Count", "possible_field": "392"}, "MV": {"explanation": "Missing or Invalid Benefit Stage Qualifier", "possible_field": "393"}, "MW": {"explanation": "Missing or Invalid Benefit Stage Amount", "possible_field": "394"}, "MX": {"explanation": "Benefit Stage Count Does Not Match Number Of Repetitions", "possible_field": "392"}, "MZ": {"explanation": "Error Overflow", "possible_field": ""}, "N1": {"explanation": "No patient match found.", "possible_field": ""}, "N3": {"explanation": "Missing or Invalid Medicaid Paid Amount", "possible_field": "113"}, "N4": {"explanation": "Missing or Invalid Medicaid Subrogation Internal Control Number or Transaction Control Number (ICN or TCN)", "possible_field": "114"}, "N5": {"explanation": "Missing or Invalid Medicaid ID Number", "possible_field": "115"}, "N6": {"explanation": "Missing or Invalid Medicaid Agency Number", "possible_field": "116"}, "N7": {"explanation": "Use Prior Authorization Code Provided During Transition Period", "possible_field": ""}, "N8": {"explanation": "Use Prior Authorization Code Provided For Emergency Fill", "possible_field": ""}, "N9": {"explanation": "Use Prior Authorization Code Provided For Level of Care Change", "possible_field": ""}, "NE": {"explanation": "Missing or Invalid Coupon Value Amount", "possible_field": "487"}, "NN": {"explanation": "Transaction Rejected At Switch Or Intermediary", "possible_field": ""}, "NP": {"explanation": "Missing or Invalid Other Payer-Patient Responsibility Amount Qualifier", "possible_field": "351"}, "NQ": {"explanation": "Missing or Invalid Other Payer-Patient Responsibility Amount", "possible_field": "352"}, "NR": {"explanation": "Missing or Invalid Other Payer-Patient Responsibility Amount Count", "possible_field": "353"}, "NU": {"explanation": "Missing or Invalid Other Payer Cardholder ID", "possible_field": "356"}, "NV": {"explanation": "Missing or Invalid Delay Reason Code", "possible_field": "357"}, "NX": {"explanation": "Missing or Invalid Submission Clarification Code Count", "possible_field": "354"}, "P1": {"explanation": "Associated Prescription or Service Reference Number Not Found", "possible_field": "456"}, "P2": {"explanation": "Clinical Information Counter Out Of Sequence", "possible_field": "493"}, "P3": {"explanation": "Compound Ingredient Component Count Does Not Match Number Of Repetitions", "possible_field": "447"}, "P4": {"explanation": "Coordination Of Benefits or Other Payments Count Does Not Match Number Of Repetitions", "possible_field": "337"}, "P5": {"explanation": "Coupon Expired", "possible_field": "486"}, "P6": {"explanation": "Date Of Service Prior To Date Of Birth", "possible_field": "304, 401"}, "P7": {"explanation": "Diagnosis Code Count Does Not Match Number Of Repetitions", "possible_field": "491"}, "P8": {"explanation": "DUR or PPS Code Counter Out Of Sequence", "possible_field": "473"}, "P9": {"explanation": "Field Is Non-Repeatable", "possible_field": ""}, "PA": {"explanation": "PA Exhausted or Not Renewable", "possible_field": ""}, "PB": {"explanation": "Invalid Transaction Count For This Transaction Code", "possible_field": "103, 109"}, "PC": {"explanation": "Missing or Invalid Request Claim Segment", "possible_field": "111"}, "PD": {"explanation": "Missing or Invalid Request Clinical Segment", "possible_field": "111"}, "PE": {"explanation": "Missing or Invalid Request Coordination Of Benefits or Other Payments Segment", "possible_field": "111"}, "PF": {"explanation": "Missing or Invalid Request Compound Segment", "possible_field": "111"}, "PG": {"explanation": "Missing or Invalid Request Coupon Segment", "possible_field": "111"}, "PH": {"explanation": "Missing or Invalid Request DUR or PPS Segment", "possible_field": "111"}, "PJ": {"explanation": "Missing or Invalid Request Insurance Segment", "possible_field": "111"}, "PK": {"explanation": "Missing or Invalid Request Patient Segment", "possible_field": "111"}, "PM": {"explanation": "Missing or Invalid Request Pharmacy Provider Segment", "possible_field": "111"}, "PN": {"explanation": "Missing or Invalid Request Prescriber Segment", "possible_field": "111"}, "PP": {"explanation": "Missing or Invalid Request Pricing Segment", "possible_field": "111"}, "PQ": {"explanation": "Missing or Invalid Narrative Segment", "possible_field": "111"}, "PR": {"explanation": "Missing or Invalid Request Prior Authorization Segment", "possible_field": "111"}, "PS": {"explanation": "Missing or Invalid Transaction Header Segment", "possible_field": "111"}, "PT": {"explanation": "Missing or Invalid Request Workers Compensation Segment", "possible_field": "111"}, "PV": {"explanation": "Non-Matched Associated Prescription or Service Date", "possible_field": "457"}, "PW": {"explanation": "Non-Matched Employer ID", "possible_field": "333"}, "PX": {"explanation": "Non-Matched Other Payer ID", "possible_field": "340"}, "PY": {"explanation": "Non-Matched Unit Form or Route of Administration", "possible_field": "451, 995, 600"}, "PZ": {"explanation": "Non-Matched Unit Of Measure To Product or Service ID ", "possible_field": "407, 600"}, "R1": {"explanation": "Other Amount Claimed Submitted Count Does Not Match Number Of Repetitions", "possible_field": "478, 480"}, "R2": {"explanation": "Other Payer Reject Count Does Not Match Number Of Repetitions", "possible_field": "471, 472"}, "R3": {"explanation": "Procedure Modifier Code Count Does Not Match Number Of Repetitions", "possible_field": "458, 459"}, "R4": {"explanation": "Procedure Modifier Code Invalid For Product or Service ID", "possible_field": "407, 436, 459"}, "R5": {"explanation": "Product or Service ID Must Be Zero When Product or Service ID Qualifier Equals 06", "possible_field": "407, 436"}, "R6": {"explanation": "Product or Service Not Appropriate For This Location", "possible_field": "307, 407, 436, 489-TE"}, "R7": {"explanation": "Repeating Segment Not Allowed In Same Transaction", "possible_field": ""}, "R8": {"explanation": "Syntax Error", "possible_field": ""}, "R9": {"explanation": "Value In Gross Amount Due Does Not Follow Pricing Formulae", "possible_field": "430"}, "RA": {"explanation": "PA Reversal Out Of Order", "possible_field": ""}, "RB": {"explanation": "Multiple Partials Not Allowed", "possible_field": ""}, "RC": {"explanation": "Different Drug Entity Between Partial & Completion", "possible_field": ""}, "RD": {"explanation": "Mismatched Cardholder or Group ID-Partial To Completion", "possible_field": "301, 302"}, "RE": {"explanation": "Missing or Invalid Compound Product ID Qualifier", "possible_field": "488"}, "RF": {"explanation": "Improper Order Of Dispensing Status Code On Partial Fill Transaction", "possible_field": ""}, "RG": {"explanation": "Missing or Invalid Associated Prescription or service Reference Number On Completion Transaction ", "possible_field": "456"}, "RH": {"explanation": "Missing or Invalid Associated Prescription or Service Date On Completion Transaction", "possible_field": "457"}, "RJ": {"explanation": "Associated Partial Fill Transaction Not On File", "possible_field": ""}, "RK": {"explanation": "Partial Fill Transaction Not Supported", "possible_field": ""}, "RL ": {"explanation": "Transitional Benefit or Resubmit Claim", "possible_field": ""}, "RM": {"explanation": "Completion Transaction Not Permitted With Same Date Of Service As Partial Transaction", "possible_field": "401"}, "RN": {"explanation": "Plan Limits Exceeded On Intended Partial Fill Field Limitations", "possible_field": "344, 345"}, "RP": {"explanation": "Out Of Sequence Reversal On Partial Fill Transaction", "possible_field": ""}, "RS": {"explanation": "Missing or Invalid Associated Prescription or Service Date On Partial Transaction", "possible_field": "457"}, "RT": {"explanation": "Missing or Invalid Associated Prescription or Service Reference Number On Partial Transaction", "possible_field": "456"}, "RU": {"explanation": "Mandatory Data Elements Must Occur Before Optional Data Elements In A Segment", "possible_field": ""}, "RV": {"explanation": "Multiple Reversals Per Transmission Not Supported", "possible_field": "109"}, "SE": {"explanation": "Missing or Invalid Procedure Modifier Code Count", "possible_field": "458"}, "SF": {"explanation": "Other Payer Amount Paid Count Does Not Match Number Of Repetitions", "possible_field": "341"}, "SG": {"explanation": "Submission Clarification Code Count Does Not Match Number of Repetitions", "possible_field": "354"}, "SH": {"explanation": "Other Payer-Patient Responsibility Amount Count Does Not Match Number of Repetitions", "possible_field": "353"}, "TE": {"explanation": "Missing or Invalid Compound Product ID", "possible_field": "489"}, "TN ": {"explanation": "Emergency Fill or Resubmit C<PERSON>m", "possible_field": ""}, "TP": {"explanation": "Level of Care Change or Resubmit Claim", "possible_field": ""}, "TQ": {"explanation": "Dosage Exceeds Product Labeling Limit ", "possible_field": "442, 405"}, "TR": {"explanation": "Missing or Invalid Billing Entity Type Indicator", "possible_field": "117"}, "TS": {"explanation": "Missing or Invalid Pay To Qualifier", "possible_field": "118"}, "TT": {"explanation": "Missing or Invalid Pay To ID", "possible_field": "119"}, "TU": {"explanation": "Missing or Invalid Pay To Name", "possible_field": "120"}, "TV": {"explanation": "Missing or Invalid Pay To Street Address", "possible_field": "121"}, "TW": {"explanation": "Missing or Invalid Pay To City Address", "possible_field": "122"}, "TX": {"explanation": "Missing or Invalid Pay to State or  Province Address", "possible_field": "123"}, "TY": {"explanation": "Missing or Invalid Pay To Zip or Postal Zone", "possible_field": "124"}, "TZ": {"explanation": "Missing or Invalid Generic Equivalent Product ID Qualifier", "possible_field": "125"}, "U7": {"explanation": "Missing or Invalid Pharmacy Service Type", "possible_field": "147"}, "UA": {"explanation": "Missing or Invalid Generic Equivalent Product ID", "possible_field": "126"}, "UE": {"explanation": "Missing or Invalid Compound Ingredient Basis Of Cost Determination", "possible_field": "490"}, "UU": {"explanation": "DAW 0 cannot be submitted on a multi-source drug with available generics.", "possible_field": ""}, "UZ": {"explanation": "Other Payer Coverage Type (338-5C) required on reversals to downstream payers. Resubmit reversal with this field.", "possible_field": "338"}, "VA": {"explanation": "Pay To Qualifier Submitted Not Supported", "possible_field": "118"}, "VB": {"explanation": "Generic Equivalent Product ID Qualifier Submitted Not Supported", "possible_field": "125"}, "VC": {"explanation": "Pharmacy Service Type Submitted Not Supported", "possible_field": "147"}, "VD": {"explanation": "Eligibility Search Time Frame Exceeded", "possible_field": ""}, "VE": {"explanation": "Missing or Invalid Diagnosis Code Count", "possible_field": "491"}, "WE": {"explanation": "Missing or Invalid Diagnosis Code Qualifier", "possible_field": "492"}, "XE": {"explanation": "Missing or Invalid Clinical Information Counter", "possible_field": "493"}, "ZA": {"explanation": "The Coordination of Benefits or Other Payments Segment is mandatory to a downstream payer.", "possible_field": ""}, "ZE": {"explanation": "Missing or Invalid Measurement Date", "possible_field": "494"}}