-- ss-message-types-test.sql
-- This file tests the parsing of RxRenewalRequest and RxChangeRequest message types
-- It inserts test messages for each type and validates the parsing

-- Test RxRenewalRequest message type
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": 111111, 
          "From": 1260306616002, 
          "MessageID": "%s", 
          "SentTime": "2023-05-01T13:42:39.7Z", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Certification Testing", "SenderSoftwareVersionRelease": "20170715" }, 
          "PrescriberOrderNumber": "RENEWAL_TEST_ORDER",
          "RxReferenceNumber": "PHARM12345" 
        }, 
        "Body": { 
          "RxRenewalRequest": { 
            "Patient": { 
              "HumanPatient": { 
                "Name": { "LastName": "RenewalTest", "FirstName": "Patient", "MiddleName": "Middle" }, 
                "Gender": "F", 
                "DateOfBirth": { "Date": "1975-05-15" },
                "Address": { 
                  "AddressLine1": "123 Renewal St", 
                  "City": "Renewalville", 
                  "StateProvince": "CA", 
                  "PostalCode": "12345", 
                  "CountryCode": "US" 
                }
              } 
            }, 
            "Prescriber": { 
              "NonVeterinarian": { 
                "Identification": { 
                  "StateLicenseNumber": "SL54321", 
                  "DEANumber": "DE54321", 
                  "NPI": "**********"
                }, 
                "Specialty": "207Q00000X", 
                "Name": { "LastName": "RenewalDoctor", "FirstName": "Test" }, 
                "Address": { 
                  "AddressLine1": "456 Doctor Ave", 
                  "City": "Doctorville", 
                  "StateProvince": "CA", 
                  "PostalCode": "54321", 
                  "CountryCode": "US" 
                }, 
                "CommunicationNumbers": { 
                  "PrimaryTelephone": { "Number": "**********" }, 
                  "Fax": { "Number": "**********" }
                }
              } 
            }, 
            "MedicationPrescribed": { 
              "DrugDescription": "Renewal Test Med 10mg", 
              "DrugCoded": {
                "ProductCode": { "Code": "**********9", "Qualifier": "ND" }
              },
              "Quantity": { "Value": 30, "CodeListQualifier": "38", "QuantityUnitOfMeasure": { "Code": "C48480" } }, 
              "DaysSupply": 30,
              "WrittenDate": { "Date": "2022-11-01" }, 
              "Substitutions": "1", 
              "NumberOfRefills": 0,
              "Sig": { "SigText": "Take 1 tablet daily" } 
            },
            "MedicationDispensed": {
              "DrugDescription": "Renewal Test Med 10mg Generic",
              "DrugCoded": {
                "ProductCode": { "Code": "***********", "Qualifier": "ND" }
              },
              "Quantity": { "Value": 30, "CodeListQualifier": "38", "QuantityUnitOfMeasure": { "Code": "C48480" } },
              "DaysSupply": 30,
              "LastFillDate": { "Date": "2023-04-15" },
              "Substitutions": "1",
              "Sig": { "SigText": "Take 1 tablet daily" }
            },
            "Pharmacy": {
              "Identification": { "NCPDPID": "9876543", "NPI": "**********" },
              "BusinessName": "Renewal Test Pharmacy",
              "Address": { 
                "AddressLine1": "789 Pharmacy Blvd", 
                "City": "Pharmville", 
                "StateProvince": "CA", 
                "PostalCode": "98765", 
                "CountryCode": "US" 
              },
              "CommunicationNumbers": { 
                "PrimaryTelephone": { "Number": "**********" }, 
                "Fax": { "Number": "**********" } 
              }
            },
            "RxRenewalRequestType": "Refill",
            "Note": "Patient requests renewal of medication"
          } 
        } 
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
    ss_message_id INTEGER;
BEGIN
    message_id_value := 'RENEWAL_TEST_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);
    
    -- Insert the test message into form_ss_log
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'RxRenewalRequest', 'RENEWAL_TEST_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    
    RAISE NOTICE 'Inserted RxRenewalRequest test message into form_ss_log with ID: %', log_id;
    
    -- Process the message
    PERFORM parse_ss_inbound(log_id);
    
    -- Get the resulting ss_message_id
    SELECT id INTO ss_message_id FROM form_ss_message WHERE message_id = 'RENEWAL_TEST_' || message_id_unique_suffix;
    
    -- Validate the message was processed
    IF ss_message_id IS NULL THEN
        RAISE EXCEPTION 'Failed to process the RxRenewalRequest test message - no form_ss_message record created';
    ELSE
        RAISE NOTICE 'Successfully processed RxRenewalRequest test message - form_ss_message ID: %', ss_message_id;
        
        -- Validate key fields
        PERFORM assert_equals('RxRenewalRequest', (SELECT message_type FROM form_ss_message WHERE id = ss_message_id), 'Message Type');
        PERFORM assert_equals('RenewalTest', (SELECT patient_last_name FROM form_ss_message WHERE id = ss_message_id), 'Patient Last Name');
        PERFORM assert_equals('Patient', (SELECT patient_first_name FROM form_ss_message WHERE id = ss_message_id), 'Patient First Name');
        PERFORM assert_equals('RenewalDoctor', (SELECT prescriber_last_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Last Name');
        PERFORM assert_equals('**********', (SELECT prescriber_npi FROM form_ss_message WHERE id = ss_message_id), 'Prescriber NPI');
        PERFORM assert_equals('Renewal Test Med 10mg', (SELECT description FROM form_ss_message WHERE id = ss_message_id), 'Medication Description');
        PERFORM assert_equals('Renewal Test Med 10mg Generic', (SELECT disp_description FROM form_ss_message WHERE id = ss_message_id), 'Dispensed Medication Description');
        PERFORM assert_equals('PHARM12345', (SELECT pharmacy_rx_no FROM form_ss_message WHERE id = ss_message_id), 'Pharmacy RX Number');
        
        RAISE NOTICE 'All RxRenewalRequest validations passed!';
    END IF;
    
    -- Clean up the test data
    DELETE FROM form_ss_message WHERE id = ss_message_id;
    DELETE FROM form_ss_log WHERE id = log_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during RxRenewalRequest test: %', SQLERRM;
        -- Clean up any test data that might have been created
        DELETE FROM form_ss_message WHERE message_id = 'RENEWAL_TEST_' || message_id_unique_suffix;
        DELETE FROM form_ss_log WHERE id = log_id;
        RAISE;
END $$;

-- Test RxChangeRequest message type
DO $$
DECLARE
    json_template TEXT := '{ 
      "?xml": "", 
      "Message": { 
        "Header": { 
          "To": 111111, 
          "From": 1260306616002, 
          "MessageID": "%s", 
          "SentTime": "2023-05-01T13:42:39.7Z", 
          "SenderSoftware": { "SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Certification Testing", "SenderSoftwareVersionRelease": "20170715" }, 
          "PrescriberOrderNumber": "CHANGE_TEST_ORDER",
          "RxReferenceNumber": "PHARM54321" 
        }, 
        "Body": { 
          "RxChangeRequest": { 
            "Patient": { 
              "HumanPatient": { 
                "Name": { "LastName": "ChangeTest", "FirstName": "Patient" }, 
                "Gender": "M", 
                "DateOfBirth": { "Date": "1990-10-20" },
                "Address": { 
                  "AddressLine1": "123 Change St", 
                  "City": "Changeville", 
                  "StateProvince": "CA", 
                  "PostalCode": "12345", 
                  "CountryCode": "US" 
                }
              } 
            }, 
            "Prescriber": { 
              "NonVeterinarian": { 
                "Identification": { 
                  "StateLicenseNumber": "SL98765", 
                  "DEANumber": "DE98765", 
                  "NPI": "**********"
                }, 
                "Specialty": "207R00000X", 
                "Name": { "LastName": "ChangeDoctor", "FirstName": "Test" }, 
                "Address": { 
                  "AddressLine1": "456 Doctor Ln", 
                  "City": "Doctorville", 
                  "StateProvince": "CA", 
                  "PostalCode": "54321", 
                  "CountryCode": "US" 
                }, 
                "CommunicationNumbers": { 
                  "PrimaryTelephone": { "Number": "**********" }, 
                  "Fax": { "Number": "**********" }
                }
              } 
            }, 
            "MedicationPrescribed": { 
              "DrugDescription": "Change Test Med 20mg", 
              "DrugCoded": {
                "ProductCode": { "Code": "55566677788", "Qualifier": "ND" }
              },
              "Quantity": { "Value": 60, "CodeListQualifier": "38", "QuantityUnitOfMeasure": { "Code": "C48480" } }, 
              "DaysSupply": 30,
              "WrittenDate": { "Date": "2023-01-15" }, 
              "Substitutions": "0", 
              "NumberOfRefills": 2,
              "Sig": { "SigText": "Take 2 tablets daily" } 
            },
            "Pharmacy": {
              "Identification": { "NCPDPID": "5555555", "NPI": "**********" },
              "BusinessName": "Change Test Pharmacy",
              "Address": { 
                "AddressLine1": "789 Pharmacy Way", 
                "City": "Pharmville", 
                "StateProvince": "CA", 
                "PostalCode": "98765", 
                "CountryCode": "US" 
              },
              "CommunicationNumbers": { 
                "PrimaryTelephone": { "Number": "**********" }, 
                "Fax": { "Number": "**********" } 
              }
            },
            "MessageRequestCode": "G",
            "ChangeReasonText": "Request to change medication to generic alternative"
          } 
        } 
      }
    }';
    json_content_final TEXT;
    message_id_value TEXT;
    message_id_unique_suffix TEXT := gen_random_uuid()::text;
    log_id INTEGER;
    ss_message_id INTEGER;
BEGIN
    message_id_value := 'CHANGE_TEST_' || message_id_unique_suffix;
    json_content_final := format(json_template, message_id_value);
    
    -- Insert the test message into form_ss_log
    INSERT INTO form_ss_log (message_json, direction, message_type, message_id, created_by, updated_by)
    VALUES (json_content_final::jsonb, 'IN', 'RxChangeRequest', 'CHANGE_TEST_' || message_id_unique_suffix, 1, 1)
    RETURNING id INTO log_id;
    
    RAISE NOTICE 'Inserted RxChangeRequest test message into form_ss_log with ID: %', log_id;
    
    -- Process the message
    PERFORM parse_ss_inbound(log_id);
    
    -- Get the resulting ss_message_id
    SELECT id INTO ss_message_id FROM form_ss_message WHERE message_id = 'CHANGE_TEST_' || message_id_unique_suffix;
    
    -- Validate the message was processed
    IF ss_message_id IS NULL THEN
        RAISE EXCEPTION 'Failed to process the RxChangeRequest test message - no form_ss_message record created';
    ELSE
        RAISE NOTICE 'Successfully processed RxChangeRequest test message - form_ss_message ID: %', ss_message_id;
        
        -- Validate key fields
        PERFORM assert_equals('RxChangeRequest', (SELECT message_type FROM form_ss_message WHERE id = ss_message_id), 'Message Type');
        PERFORM assert_equals('ChangeTest', (SELECT patient_last_name FROM form_ss_message WHERE id = ss_message_id), 'Patient Last Name');
        PERFORM assert_equals('Patient', (SELECT patient_first_name FROM form_ss_message WHERE id = ss_message_id), 'Patient First Name');
        PERFORM assert_equals('ChangeDoctor', (SELECT prescriber_last_name FROM form_ss_message WHERE id = ss_message_id), 'Prescriber Last Name');
        PERFORM assert_equals('**********', (SELECT prescriber_npi FROM form_ss_message WHERE id = ss_message_id), 'Prescriber NPI');
        PERFORM assert_equals('Change Test Med 20mg', (SELECT description FROM form_ss_message WHERE id = ss_message_id), 'Medication Description');
        PERFORM assert_equals('G', (SELECT chg_type_id FROM form_ss_message WHERE id = ss_message_id), 'Change Request Type');
        PERFORM assert_equals('Request to change medication to generic alternative', (SELECT chg_reason FROM form_ss_message WHERE id = ss_message_id), 'Change Reason');
        
        RAISE NOTICE 'All RxChangeRequest validations passed!';
    END IF;
    
    -- Clean up the test data
    DELETE FROM form_ss_message WHERE id = ss_message_id;
    DELETE FROM form_ss_log WHERE id = log_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error during RxChangeRequest test: %', SQLERRM;
        -- Clean up any test data that might have been created
        DELETE FROM form_ss_message WHERE message_id = 'CHANGE_TEST_' || message_id_unique_suffix;
        DELETE FROM form_ss_log WHERE id = log_id;
        RAISE;
END $$;
