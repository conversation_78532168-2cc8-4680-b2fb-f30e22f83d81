// This is the inbound route from Clara NES Servers, which is a POST request to the /send endpoint. The request body is validated and then stored in an SQLite database.
// The message is then sent to the inbound queue for processing.If the message is successfully stored in the database, a return XML message is generated and sent back to the sender.
// The outbound message is also stored in the database for tracking purposes.
// The response from Surscripts.

"use strict";

import { validate_xsd } from "../../xml-valparse.js";
import { ss_urlmap } from "../../fixtures/maps.js";

/**
 * Handles the outbound route from Clara servers to SureScripts.
 * Sends the outbound message to SureScripts.
 *
 * @param {object} app - The Fastify app instance.
 * @param {object} opts - The options object.
 * @returns {Promise<void>}
 */
export default async function (app, opts) {
    const SS_TX_URL =
        app.config.ENVIRONMENT == "prod"
            ? ss_urlmap["tx_prod"]
            : ss_urlmap["tx_staging"];
    app.post(
        "/send",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description:
                    "This is the outbound Route from clara servers to SureScripts. It sends the outbound message to SureScripts.",
                tags: ["SureScripts"],
                summary: "Send an outbound message to SureScripts",
                body: {
                    $ref: "SSRequest#",
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        $ref: "ClaraTxResponse",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            app.log.debug(
                "Received Outbound message from Clara NES Servers" +
                    JSON.stringify(request.body, null, 2)
            );
            if (!request.body) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.body)
                );
                throw new Error("Invalid request");
            }

            // At this point we have validated the request and we can process and store it in an sqlite db
            const data = request.body.Message;
            app.log.info(
                "Outbound message received: " + JSON.stringify(data, null, 2)
            );

            const d = {};
            const cinfo = {};
            cinfo.customer_id = request.customer_id;
            cinfo.customer_name = request.customer_name;
            const headers = {};
            const message = data.Body;
            const dheader = data.Header;
            const new_mid = app.uuid();
            const message_type =
                Object.keys(message).length > 0
                    ? Object.keys(message)[0]
                    : null;
            if (!message_type) {
                app.log.error(
                    "Invalid message type: " + JSON.stringify(message)
                );
                throw new Error("Invalid message type");
            }
            Object.assign(d, message[message_type]);
            Object.assign(headers, dheader);
            headers.message_id = new_mid;
            d.message_type = message_type;
            d.header = headers;

            app.log.info(
                `Making a request to Surescripts with message type: ${message_type} and message id: ${new_mid}`
            );

            const reqxml = app.ss_rxml("ScriptRequest", d);
            app.log.info("Generated xml: " + reqxml);
            const valid = await validate_xsd(app, "transport", reqxml);
            app.log.info(
                "Generated xml is valid according to schema: " + valid
            );
            const url = SS_TX_URL.replace("{message_id}", new_mid);
            const res = await app.ss_request(url, valid, cinfo);
            app.log.info(
                "Transact.js Response received:" + JSON.stringify(res, null, 2)
            );
            reply.send(res);
            return reply;
        }
    );
}
