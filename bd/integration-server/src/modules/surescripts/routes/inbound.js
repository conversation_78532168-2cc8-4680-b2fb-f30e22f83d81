// Description: This is the inbound route for the SureScripts module. It receives the inbound XML from SureScripts and stores it in an sqlite db.
// There is also a validator that checks the inbound XML against the SureScripts schema. The validator is in the index.js in the sureScripts module.
"use strict";

import { SSErxDB } from "../../../database/surescriptsdb.js";
import fxp from "fast-xml-parser";
import {
    generate_signature,
    getCustomerDetails,
} from "../../../utils/tools.js";
import { validate_xsd } from "../xml-valparse.js";

/**
 * This function handles the inbound route for the SureScripts module.
 * It receives the inbound XML from SureScripts and stores it in an sqlite db.
 * There is also a validator that checks the inbound XML against the SureScripts schema.
 * The validator is in the index.js in the sureScripts module.
 *
 * @param {FastifyInstance} app - The Fastify app instance.
 * @param {Object} opts - The options object.
 * @returns {Promise<void>} - A promise that resolves when the route handling is complete.
 */
export default async function (app, opts) {
    app.post(
        "/",
        {
            config: {
                rawBody: true,
            },
            schema: {
                description: `
        This is the inbound route for the SureScripts module. It receives the inbound XML from SureScripts and stores it in an sqlite db.
        There is also a validator that checks the inbound XML against the SureScripts schema. The validator is in the index.js in the sureScripts module.
        `,
                tags: ["SureScripts"],
                summary: "Receive an inbound message from SureScripts",
                body: {
                    $ref: "SSRequest#",
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "string",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            if (!request.body || !request.body?.Message) {
                app.log.error(
                    "invalid request: " + JSON.stringify(request.body)
                );
                throw new Error("Invalid request");
            }

            // At this point we have validated the request and we can process and store it in an sqlite db
            const jmessage = request.body.Message;
            app.log.info(
                "Inbound message received: " + JSON.stringify(request.body)
            );
            const cinfo =
                (await getCustomerDetails(app, jmessage.Header.To)) || {};

            await request.body;

            const mh = await generate_signature(request.rawBody);
            if (!reply.sent) {
                const opts = {
                    rdata: request.body,
                    db_dir: app.db_root,
                    rawBody: request.rawBody,
                    direction: "inbound",
                    message_hash: mh,
                    customer_id: cinfo.customer_id,
                    customer_name: cinfo.customer_name,
                };

                const dbc = new SSErxDB(opts, app);
                const res = dbc.insert_new();
                if (res && res.changes) {
                    app.log.info(
                        "Inbound message stored in sqlitedb: " +
                            JSON.stringify(res)
                    );
                    // Send the message to the inbound queue
                    const options = { retryLimit: 5, retryBackoff: true };

                    const fd = {
                        message_id: jmessage.Header.MessageID,
                        message_type: Object.keys(jmessage.Body)[0],
                        direction: "inbound",
                        raw_json: JSON.stringify(request.body),
                        raw_xml: request.rawBody,
                        message_header: jmessage.Header,
                        message_hash: mh,
                        customer_id: cinfo.customer_id,
                        customer_name: cinfo.customer_name,
                    };
                    const jobid = await app.pgboss.send(
                        "ss_inbound",
                        fd,
                        options
                    );
                    app.log.info(
                        `Inbound message sent to queue with jobid: ${jobid}`
                    );

                    if (jobid) {
                        const d = {};
                        d.header = {};
                        d.header.ncpdp_from_type = "P";
                        d.header.ncpdp_to_type = "D";
                        d.header.From = jmessage.Header.To;
                        d.header.To = jmessage.Header.From;
                        d.ncpdp_version =
                            request.server.config.NCPDP_SCHEMA_VERSION;
                        d.sent_dt = new Date().toISOString();
                        d.header.message_id = app.uuid();
                        d.header.RelatesToMessageID = jmessage.Header.MessageID;
                        d.status_code = "000";
                        const r = app.ss_rxml("status", d);
                        app.log.info("Generated return XML for status: " + r);
                        const valid = await validate_xsd(app, "transport", r);
                        reply.code(200).type("text/xml").send(valid);
                        await reply;
                        try {
                            if (reply.sent) {
                                const xmlParser = new fxp.XMLParser({
                                    ignoreAttributes: true,
                                    ignoreDeclaration: true,
                                });
                                const body = xmlParser.parse(r);
                                const opts = {
                                    rdata: body,
                                    db_dir: app.sqlite_db_dir,
                                    rawBody: r,
                                    direction: "outbound",
                                    customer_id: cinfo.customer_id,
                                    customer_name: cinfo.customer_name,
                                };
                                const dbc = new SSErxDB(opts, app);
                                app.log.info(`opts: ${JSON.stringify(opts)}`);
                                const res = dbc.insert_new();
                                app.log.info(
                                    "Outbound message stored in sqlitedb: " +
                                        JSON.stringify(res)
                                );
                            }
                        } catch (err) {
                            app.log.error(err);
                        }
                    }
                }
            }
            if (!reply.sent) {
                reply.send("ok");
            }
        }
    );
}
