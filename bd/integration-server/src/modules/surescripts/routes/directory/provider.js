"use strict";
import { validate_xsd } from "../../xml-valparse.js";
import { ss_urlmap } from "../../fixtures/maps.js";
import { SSDirectoryDB } from "../../../../database/surescriptsdb.js";

export default async function (app, opts) {
    const SS_DIR_URL =
        app.config.ENVIRONMENT == "prod"
            ? ss_urlmap["directory_tx_prod"]
            : ss_urlmap["directory_tx_staging"];
    app.get(
        "/provider/live/:spi",
        {
            schema: {
                description:
                    "Instead of looking up a provider in the database, this endpoint will fetch the provider live from Surescripts.",
                tags: ["Surescripts Directory"],
                summary: "Get live provider details from Surescripts",
                params: {
                    type: "object",
                    properties: {
                        spi: {
                            type: "string",
                            description: "SPI",
                        },
                    },
                    required: ["spi"],
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        $ref: "ClaraTxResponse",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
            preHandler: app.verifyBearerAuth,
        },
        async (request, reply) => {
            const pspi = request.params?.spi;
            if (!pspi) {
                app.log.error("No Provider SPI provided");
                reply
                    .code(400)
                    .send(
                        "Invalid request: No Provider SPI provided in request params."
                    );
                return reply;
            }

            app.log.info(`Fetching provider details for: ${pspi}`);
            const d = {};
            const mid = app.uuid();
            d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
            d.provider_id = pspi;
            d.message_id = mid;

            const reqxml = app.ss_rxml("GetProviderLocation", d);
            const valid = await validate_xsd(app, "directory", reqxml);
            const url = SS_DIR_URL.replace("{message_id}", mid);
            const res = await app.ss_request(url, valid);
            reply.send(res);
            return reply;
        }
    ),
        app.get(
            "/provider",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description:
                        "Get provider details from the local database, at least one query parameter is required.",
                    tags: ["Surescripts Directory"],
                    querystring: {
                        spi: {
                            type: "string",
                        },
                        npi: {
                            type: "integer",
                        },
                        deanumber: {
                            type: "string",
                        },
                        firstname: {
                            type: "string",
                        },
                        lastname: {
                            type: "string",
                        },
                    },
                    response: {
                        200: {
                            type: "array",
                            items: { $ref: "directoryprovider#" },
                        },
                    },
                },
            },
            async (request, reply) => {
                const { spi, npi, deanumber, firstname, lastname } =
                    request.query;

                if (!spi && !npi && !deanumber && !firstname && !lastname) {
                    reply.code(400).send({
                        error: "At least one query parameter is required",
                    });
                    return;
                }
                const opts = {
                    dir_type: "provider",
                };
                const db = new SSDirectoryDB(opts, app);
                app.log.info(
                    `Fetching provider details for: ${JSON.stringify(request.query)}`
                );
                const res = await db.select_filtered(request.query);

                reply.send(res);
                return reply;
            }
        ),
        app.get(
            "/download/provider/:dltype?",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    tags: ["Surescripts Directory"],
                    description:
                        "Initiate a download of the provider directory from Surescripts, optionally specify the type of download.",
                    parameters: [
                        {
                            name: "dltype",
                            in: "query",
                            description: "Type of download",
                            required: false,
                            schema: {
                                type: "string",
                                enum: ["nightly", "full"],
                                default: "nightly",
                            },
                        },
                    ],
                    response: {
                        200: {
                            type: "object",
                            properties: {
                                message: {
                                    type: "string",
                                    example:
                                        "nightly Download/Extraction initiated for directory file 1100785_PRN_61_bfeba7c3-7f19-411e-900d-b3b9e4bd9447.zip with jobid: 117176fd-62b9-4086-9bc7-f181918cea36",
                                },
                            },
                        },
                        400: {
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "number" },
                                stack: { type: "string" },
                                error_description_code: { type: "number" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                if (!app.config.SURESCRIPTS_ACCOUNT_ID) {
                    app.log.error("SURESCRIPTS_ACCOUNT_ID not set.");
                    reply.code(400).send("Invalid request");
                }

                app.log.info("Initiating provider directory download request");
                const mid = app.uuid();
                const d = {};
                const cinfo = {};
                const dltype = request.params?.dltype || "nightly";
                d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
                d.message_id = mid;
                d.nightly = true;
                if (request.params?.dltype === "full") {
                    d.nightly = false;
                    app.log.info("FULL download requested");
                }
                const reqxml = app.ss_rxml("DirectoryDownloadProvider", d);
                const url = SS_DIR_URL.replace("{message_id}", mid);
                cinfo.customer_id = request.customer_id;
                cinfo.customer_name = request.customer_name;
                const valid = await validate_xsd(app, "directory", reqxml);
                app.log.info(
                    "Generated xml is valid according to schema: " + valid
                );

                const res = await app.ss_request(url, valid, cinfo);
                if (res && res.DirectoryMessage?.Body) {
                    // Send the message to the inbound queue
                    const download_url =
                        res.DirectoryMessage.Body?.DirectoryDownloadResponse
                            ?.URL;
                    app.log.info("Download URL found: " + download_url);
                    if (!download_url) {
                        app.log.error("No download URL found in response");
                        return reply;
                    }

                    const options = {
                        retryLimit: 5,
                        retryBackoff: true,
                    };
                    const fd = {
                        sqlite_db:
                            app.db_root +
                            "directory/" +
                            res.DirectoryMessage.Header.To +
                            ".db",
                        download_url: download_url,
                        type: "provider_download",
                        customer_id: request.customer_id,
                        customer_name: request.customer_name,
                    };
                    const jobid = await app.pgboss.send(
                        "ss_directory",
                        fd,
                        options
                    );
                    app.log.info(
                        `Inbound message sent to ss_directory queue with jobid: ${jobid}`
                    );
                    reply.send({
                        message: `${dltype} Download/Extraction initiated for directory file ${download_url} with jobid: ${jobid}`,
                    });
                }

                return reply;
            }
        );
}
