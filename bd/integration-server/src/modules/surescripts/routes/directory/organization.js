"use strict";
import { validate_xsd } from "../../xml-valparse.js";
import { ss_urlmap } from "../../fixtures/maps.js";
export default async function (app, opts) {
    // Add organization to surescripts directory
    const SS_DIR_URL =
        app.config.ENVIRONMENT == "prod"
            ? ss_urlmap["directory_tx_prod"]
            : ss_urlmap["directory_tx_staging"];
    app.post(
        "/organization",
        {
            preHandler: app.verifyBearerAuth,
            schema: {
                description:
                    "Create a new organization in the Surescripts directory",
                tags: ["Surescripts Directory"],
                body: {
                    type: "object",
                    $ref: "SSOrgReqBody",
                },
                response: {
                    200: {
                        description: "Successful response",
                        type: "object",
                        $ref: "ClaraTxResponse",
                    },
                    400: {
                        description: "Invalid request",
                        type: "object",
                        properties: {
                            error: { type: "string" },
                            error_code: { type: "number" },
                            stack: { type: "string" },
                            error_description_code: { type: "number" },
                        },
                    },
                },
            },
        },
        async (request, reply) => {
            const orgid = request.params.id;
            if (!request.body) {
                app.log.error("No request body provided");
                reply
                    .code(400)
                    .send("Invalid request: No request body provided");
                return reply;
            }

            app.log.info(
                `Adding new organization  with the following data: ${JSON.stringify(
                    request.body
                )}`
            );
            const d = request.body;
            const mid = app.uuid();
            d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
            d.organization_id = orgid;
            d.message_id = mid;

            const reqxml = app.ss_rxml("AddOrganization", d);

            const valid = await validate_xsd(app, "directory", reqxml);
            app.log.info(
                "Generated xml is valid according to schema: " + valid
            );
            const url = SS_DIR_URL.replace("{message_id}", mid);

            const res = await app.ss_request(url, valid);
            // res = res.response_json_data;
            console.log("Response received:", res);
            app.log.debug(`REPLY TO CLARA: ${JSON.stringify(res)}`);
            reply.send(res);
            return reply;
        }
    ),
        app.put(
            "/organization/:orgid",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description:
                        "Update an organization in the Surescripts directory",
                    tags: ["Surescripts Directory"],
                    body: {
                        type: "object",
                        $ref: "SSOrgReqBody",
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "ClaraTxResponse",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "number" },
                                stack: { type: "string" },
                                error_description_code: { type: "number" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                const orgid = request.params?.orgid;
                if (!orgid) {
                    app.log.error("No Organization ID provided");
                    reply
                        .code(400)
                        .send("Invalid request: No Organization ID provided");
                    return reply;
                }
                if (!request.body) {
                    app.log.error("No request body provided");
                    reply
                        .code(400)
                        .send("Invalid request: No request body provided");
                    return reply;
                }

                app.log.info(
                    `Updating organization details for: ${orgid} with the following data: ${JSON.stringify(
                        request.body
                    )}`
                );
                const d = request.body;
                const mid = app.uuid();
                d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
                d.organization_id = orgid;
                d.message_id = mid;

                const reqxml = app.ss_rxml("UpdateOrganization", d);
                app.log.info("Generated xml: " + reqxml);
                const valid = await validate_xsd(app, "directory", reqxml);
                app.log.info(
                    "Generated xml is valid according to schema: " + valid
                );
                const url = SS_DIR_URL.replace("{message_id}", mid);
                const res = await app.ss_request(url, valid);
                app.log.info("Response received:", res);
                app.log.debug(`REPLY TO CLARA: ${JSON.stringify(res)}`);

                reply.send(res);
                return reply;
            }
        ),
        app.get(
            "/organization/live/:orgid",
            {
                schema: {
                    description:
                        "Instead of looking up the organization in the database, this endpoint will fetch the organization from Surescripts.",
                    tags: ["Surescripts Directory"],
                    summary: "Get live organization",
                    params: {
                        type: "object",
                        properties: {
                            orgid: {
                                type: "string",
                                description: "Organization id",
                            },
                        },
                        required: ["orgid"],
                    },
                    response: {
                        200: {
                            description: "Successful response",
                            type: "object",
                            $ref: "ClaraTxResponse",
                        },
                        400: {
                            description: "Invalid request",
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "number" },
                                stack: { type: "string" },
                                error_description_code: { type: "number" },
                            },
                        },
                    },
                },
                preHandler: app.verifyBearerAuth,
            },
            async (request, reply) => {
                const orgid = request.params?.orgid;
                if (!orgid) {
                    app.log.error("No Organization ID provided");
                    reply
                        .code(400)
                        .send("Invalid request: No Organization ID provided");
                    return reply;
                }
                app.log.info("Fetching organization details for: " + orgid);
                const mid = app.uuid();
                const d = {};
                d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
                d.organization_id = orgid;
                d.message_id = mid;
                const reqxml = app.ss_rxml("GetOrganization", d);
                const valid = await validate_xsd(app, "directory", reqxml);
                app.log.info(
                    "Generated xml is valid according to schema: " + valid
                );
                const url = SS_DIR_URL.replace("{message_id}", mid);
                const res = await app.ss_request(url, valid);
                app.log.info("Response received:", res);
                app.log.debug(`REPLY TO CLARA: ${JSON.stringify(res)}`);
                reply.send(res);
                return reply;
            }
        ),
        app.put(
            "/organization/disable/:orgid",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    description:
                        "Disable an organization in the Surescripts directory",
                    tags: ["Surescripts Directory"],
                    params: {
                        type: "object",
                        properties: {
                            orgid: { type: "string" },
                        },
                        required: ["orgid"],
                    },
                },
            },
            async (request, reply) => {
                const orgid = request.params.orgid;
                if (!orgid) {
                    app.log.error("No Organization ID provided");
                    reply
                        .code(400)
                        .send("Invalid request: No Organization ID provided");
                    return reply;
                }
                app.log.info("Disabling organization: " + orgid);
                const d = {};
                d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
                d.organization_id = orgid;
                const mid = app.uuid();
                d.message_id = mid;
                const reqxml = app.ss_rxml("DisableOrganization", d);
                app.log("Generated xml: " + reqxml);
                const valid = await validate_xsd(app, "directory", reqxml);
                app.log.info(
                    "Generated xml is valid according to schema: " + valid
                );
                const url = SS_DIR_URL.replace("{message_id}", mid);
                const res = await app.ss_request(url, valid);
                app.log.info("Response received:", res);
                reply.send(res);
                return reply;
            }
        ),
        app.get(
            "/download/organization/:dltype?",
            {
                preHandler: app.verifyBearerAuth,
                schema: {
                    tags: ["Surescripts Directory"],
                    description:
                        "Download the organization directory from Surescripts, optionally specify full or nightly download. Default is nightly.",

                    parameters: [
                        {
                            name: "dltype",
                            in: "query",
                            description: "Type of download",
                            required: false,
                            schema: {
                                type: "string",
                                enum: ["nightly", "full"],
                                default: "nightly",
                            },
                        },
                    ],
                    response: {
                        200: {
                            type: "object",
                            properties: {
                                message: {
                                    type: "string",
                                    example:
                                        "nightly Download/Extraction initiated for directory file 1100785_PRN_61_bfeba7c3-7f19-411e-900d-b3b9e4bd9447.zip with jobid: 117176fd-62b9-4086-9bc7-f181918cea36",
                                },
                            },
                        },
                        400: {
                            type: "object",
                            properties: {
                                error: { type: "string" },
                                error_code: { type: "number" },
                                stack: { type: "string" },
                                error_description_code: { type: "number" },
                            },
                        },
                    },
                },
            },
            async (request, reply) => {
                if (!app.config.SURESCRIPTS_ACCOUNT_ID) {
                    app.log.error("SURESCRIPTS_ACCOUNT_ID not set.");
                    reply.code(400).send("Invalid request");
                }
                app.log.info(
                    "Initiating organization directory download request"
                );
                const mid = app.uuid();
                const d = {};
                const dltype = request.params?.dltype || "nightly";
                d.surescripts_account_id = app.config.SURESCRIPTS_ACCOUNT_ID;
                d.message_id = mid;
                d.nightly = true;
                if (request.params?.dltype === "full") {
                    d.nightly = false;
                    app.log.info("FULL download requested");
                }
                const reqxml = app.ss_rxml("DirectoryDownloadOrganization", d);
                const valid = await validate_xsd(app, "directory", reqxml);
                app.log.info(
                    "Generated xml is valid according to schema: " + valid
                );
                const url = SS_DIR_URL.replace("{message_id}", mid);
                const res = await app.ss_request(url, valid);
                if (res && res.DirectoryMessage?.Body) {
                    // Send the message to the inbound queue
                    const download_url =
                        res.DirectoryMessage.Body?.DirectoryDownloadResponse
                            ?.URL;
                    app.log.info("Download URL found: " + download_url);
                    if (!download_url) {
                        app.log.error("No download URL found in response");
                        return reply;
                    }

                    const options = {
                        retryLimit: 5,
                        retryBackoff: true,
                    };
                    const fd = {
                        sqlite_db:
                            app.db_root +
                            "directory/" +
                            res.DirectoryMessage.Header.To +
                            ".db",
                        download_url: download_url,
                        type: "organization_download",
                    };
                    const jobid = await app.pgboss.send(
                        "ss_directory",
                        fd,
                        options
                    );
                    app.log.info(
                        `Inbound message sent to ss_directory queue with jobid: ${jobid}`
                    );
                    reply.send({
                        message: `${dltype} Download/Extraction initiated for directory file ${download_url} with jobid: ${jobid}`,
                    });
                }
                if (!reply.sent) {
                    reply.send(res);
                }
                return reply;
            }
        );
}
