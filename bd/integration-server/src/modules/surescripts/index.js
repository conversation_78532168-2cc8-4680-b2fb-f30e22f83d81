import fp from "fastify-plugin";
import autoload from "@fastify/autoload";
import { join } from "desm";
import { ss_xml_validate } from "./xml-valparse.js";
import { ss_error_handler } from "./handlers/surescripts-error-handler.js";
import {
    make_surescripts_request,
    render_template,
} from "./handlers/surescripts-request-handler.js";

/**
 * Initializes the Surescripts module.
 *
 * @param {FastifyInstance} app - The Fastify instance.
 * @param {Object} opts - The options object.
 * @returns {Promise<void>} - A promise that resolves when the module is initialized.
 */
async function surescripts(app, opts) {
    app.decorate("surescripts_base_dir", join(import.meta.url) + "/");
    app.decorate(
        "template_path",
        join(import.meta.url) + "/fixtures/templates/"
    );
    app.decorate("schema_path"),
        join(import.meta.url) +
            "/fixtures/schemas/ncpdp/" +
            app.config.NCPDP_SCHEMA_VERSION +
            "/";
    app.decorate("ss_rxml", render_template);
    app.decorate("ss_request", make_surescripts_request);
    //  app.removeAllContentTypeParsers()
    app.addContentTypeParser(["text/xml", "application/xml"], ss_xml_validate);
    app.register(autoload, {
        dir: join(import.meta.url, "routes"),
        options: {
            prefix: opts.prefix,
        },
    });
    app.setErrorHandler(ss_error_handler);
}

export default fp(surescripts);
