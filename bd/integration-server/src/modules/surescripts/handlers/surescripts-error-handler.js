"use strict";
import fxp from "fast-xml-parser";
import {
    store_error_message,
    getCustomerDetails,
} from "../../../utils/tools.js";
import { SSErxDB } from "../../../database/surescriptsdb.js";

/**
 * Handles SureScripts errors.ncpdp
 *
 * @param {Error} err - The error object.
 * @param {Object} req - The request object.
 * @param {Object} reply - The reply object.
 * @returns {Promise} - A promise that resolves to the error or the reply object.
 */

async function ss_error_handler(err, req, reply) {
    await req.body;
    if (
        !err.domain &&
        !err.code &&
        !err.line &&
        !err.message.includes("UNIQUE constraint failed")
    ) {
        // This is not a surescripts error, just return the error
        throw err;
    }
    req.log.error(
        {
            err: err,
            errorMessage: err.message,
            errorStack: err.stack,
            errorCode: err.code,
            errorDomain: err.domain,
            errorLine: err.line,
        },
        "SURESCRIPTS - ss_error_handler called"
    );
    let body = req.body;
    const rawBody = req.rawBody;
    if (!body && rawBody) {
        const options = {
            ignoreAttributes: true,
            ignoreDeclaration: true,
            explicitArray: false,
        };
        const xmlParser = new fxp.XMLParser(options);
        try {
            body = xmlParser.parse(rawBody);
        } catch (err) {
            req.log.error("Error parsing XML: " + err);
            return reply;
        }
    }

    // Building response error message
    if (req.customer_id && req.raw.url && req.raw.url.split("/").length > 2) {
        // This is an internal request from a Clara server trying to send an outbound message
        // We need to return a clean error message JSON from the ss_request result
        const ed = err.error_description ? err.error_description : err.message;
        const resp = { error: ed, error_code: err.code, stack: err.stack };
        if (err.DescriptionCode) {
            resp.error_description_code = err.DescriptionCode;
        }
        if (err.line) {
            resp.error_line = err.line;
        }
        resp.req_body = req.body;
        reply.code(400).send(resp);
        return reply;
    }
    const d = {};
    d.header = {};
    d.header.ncpdp_from_type = "P";
    d.header.ncpdp_to_type = "D";
    d.ncpdp_version = req.server.config.NCPDP_SCHEMA_VERSION;
    d.sent_dt = new Date().toISOString();
    d.header.message_id = req.server.uuid();
    d.http_code = 400;
    d.error_description = `Syntax Error: ${err.message} at line ${err.line} and column ${err.column}`;

    if (
        (!body?.Message ||
            !["Body", "Header"].every((key) =>
                Object.keys(body.Message).includes(key)
            )) &&
        err.hasOwnProperty("line") &&
        err.level == 3
    ) {
        // This is a syntax error, map to NCPDP Error code 500
        if (body.Header?.MessageID) {
            d.header.message_id = req.server.uuid();
            d.header.RelatesToMessageID = body.Header.RelatedMessageID;
        }
        d.error_description = `Syntax Error: ${err.message} at line ${err.line} and column ${err.column}`;
        d.error_description_code = 500;
        d.http_code = 400;
        d.error_type = "syntax_error";
    }
    if (body && body.Message?.Header) {
        d.header.From = body.Message.Header.To;
        d.header.To = body.Message.Header.From;
        d.header.RelatesToMessageID = body.Message.Header.MessageID;
        if (err.message.includes("UNIQUE constraint failed")) {
            // This is a unique constraint, map to NCPDP Error code 220
            (d.error_description =
                "Duplicate Message: The message has already been received and processed"),
                (d.error_description_code = 220);
            d.http_error_code = 409;
            d.error_type = "duplicate_message";
        } else if (err.message && d.error_description && err.level == 2) {
            d.error_description = `Syntax Error: ${err.message} at line ${err.line} and column ${err.column}`;
            d.error_description_code = 500;
            d.error_type = "schema_error";
        }
    }
    if (
        d &&
        d.error_description_code &&
        d.header.RelatesToMessageID &&
        d.header?.To &&
        d.header?.From
    ) {
        // we can reliably-ish send back to this sender.
        const r = this.ss_rxml("error", d);
        this.log.info("Generated return XML for error: " + r);
        const options = {
            ignoreAttributes: true,
            ignoreDeclaration: true,
            explicitArray: false,
        };
        const xmlParser = new fxp.XMLParser(options);
        const body = xmlParser.parse(r);
        let cid, cname;
        if (req.customer_id) {
            cid = req.customer_id;
            cname = req.customer_name;
        } else if (this.customer_id) {
            cid = this.customer_id;
            cname = this.customer_name;
        } else {
            const cinfo = await getCustomerDetails(this, d.header.From);
            cid = cinfo?.customer_id;
            cname = cinfo?.customer_name;
        }
        d.customer_id = cid;
        d.customer_name = cname;
        const opts = {
            rdata: body,
            db_dir: this.db_root + "customer/",
            rawBody: r,
            direction: "outbound",
            customer_id: cid,
            customer_name: cname,
        };
        const dbc = new SSErxDB(opts, this);
        const res = dbc.insert_new();
        this.log.info(
            "Outbound message stored in sqlitedb: " + JSON.stringify(res)
        );
        store_error_message(this, body, err, d, r);
        reply.code(d.http_code).type("text/xml").send(r);
        // Need to store the outbound message in the db, it has already been returned async in the rsp.

        return reply;
    }

    return err;
}

export { ss_error_handler };
