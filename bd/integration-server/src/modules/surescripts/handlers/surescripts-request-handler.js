import axios from "axios";
import https from "https";
import fs from "fs";
import * as fxp from "fast-xml-parser";
import { SSErxDB } from "../../../database/surescriptsdb.js";
import * as uuid from "uuid";
import moment from "moment";
import nunjucks from "nunjucks";
import { escape_template_data } from "../../../utils/tools.js";
import { generate_signature } from "../../../utils/tools.js";

/**
 * Renders a template using Nunjucks and provided data.
 * @param {string} template - The name of the template to render.
 * @param {object} sdata - The data to be passed to the template.
 * @returns {string} - The rendered template as a string.
 * @throws {Error} - If no template or data is specified.
 */
export function render_template(template, sdata) {
    const env = nunjucks.configure(this.template_path, {
        autoescape: false,
        trimBlocks: true,
        lstripBlocks: true,
    });
    if (!template) {
        throw new Error("No template specified");
    } else {
        if (!template.endsWith(".njk")) {
            template = template + ".njk";
        }
    }
    if (!sdata) {
        throw new Error("No data specified");
    }
    sdata.ncpdp_version = this.config.NCPDP_SCHEMA_VERSION;
    env.addGlobal("utc_now", function (today = false) {
        if (today) {
            return moment().startOf("day").toISOString();
        } else {
            return moment().utc().toISOString();
        }
    });
    env.addGlobal("gen_mid", function () {
        return uuid.v4().replaceAll("-", "");
    });
    env.addGlobal("ss_fmt_dt", function (d = null, fmt = null) {
        if (!fmt) {
            fmt = "YYYY-MM-DDTHH:MM:SS.FZ";
        }
        if (!d) {
            return moment().format(fmt);
        }
        return moment(d).format(fmt);
    });
    const escapedData = escape_template_data(sdata);
    return env.render(template, escapedData);
}

/**
 * Makes a Surescripts request to the specified URL with the given XML data.
 * @param {string} url - The URL to send the request to.
 * @param {string} cdata - The XML data to send in the request body.
 * @returns {Promise<Object>} - A Promise that resolves to the Surescripts response object.
 * @throws {Error} - If there is an error in the response or if the request times out.
 */
export async function make_surescripts_request(url, cdata, cinfo) {
    const xmlParser = new fxp.XMLParser({
        ignoreAttributes: true,
        ignoreDeclaration: true,
    });
    const rjson = xmlParser.parse(cdata);
    const ssl_dir = "/etc/ssl/private/surescripts/";
    const httpsAgent = new https.Agent({
        cert: fs.readFileSync(ssl_dir + "mtls.pem"),
        key: fs.readFileSync(ssl_dir + "mtls.key"),
        ca: fs.readFileSync(ssl_dir + "mtls-bundle.pem"),
    });
    this.log.info("Sending request to: " + url);

    const resp = await axios
        .post(url, cdata, {
            headers: {
                "Content-Type": "text/xml",
            },
            httpsAgent: httpsAgent,
            timeout: 10000, // Wait for 5 seconds
        })
        .catch((err) => {
            this.log.error(
                "Error in response for Surescripts Request:" +
                    JSON.stringify(err)
            );
            throw new Error(err);
        });
    this.log.info("Response received: " + resp.data);
    if (cdata.DirectoryMessage) {
        cdata = cdata.DirectoryMessage;
    } else if (cdata.Message) {
        cdata = cdata.Message;
    }
    const mho = await generate_signature(cdata);
    const opts = {
        rdata: rjson,
        db_dir: this.sqlite_db_dir,
        rawBody: cdata,
        direction: "outbound",
        message_hash: mho,
        customer_id: cinfo?.customer_id,
        customer_name: cinfo?.customer_name,
    };
    this.log.info(`ss request handler opts: ${JSON.stringify(opts)}`);
    const dbc = new SSErxDB(opts, this);
    const dbres = dbc.insert_new();
    this.log.info(
        "Outbound message stored in sqlitedb: " + JSON.stringify(dbres)
    );

    if (resp && resp.data) {
        this.log.info("XML Response received: " + resp.data);
        let errs;
        const rdata = xmlParser.parse(resp.data);
        let rtype;

        if (rdata.DirectoryMessage) {
            rtype = "DirectoryMessage";
        } else if (rdata.Message) {
            rtype = "Message";
            this.log.debug(
                "Response received: " + JSON.stringify(rdata, null, 4)
            );
        } else {
            return resp;
        }
        if (rdata[rtype].Body?.Error) {
            const errobj = new Error();
            const ss_err = rdata[rtype].Body.Error;
            errobj.code = ss_err.Code;
            errobj.error_description = ss_err.Description;
            errobj.DescriptionCode = ss_err.DescriptionCode;
            errs = errobj;
        }
        const mh = await generate_signature(resp.data);
        const d = {
            rdata: rdata,
            db_dir: this.db_root + "directory/",
            rawBody: resp.data,
            direction: "inbound",
            message_hash: mh,
            customer_id: cinfo?.customer_id,
            customer_name: cinfo?.customer_name,
        };

        if (errs) {
            d.errors = errs;
        }
        const dbce = new SSErxDB(d, this);
        const res = dbce.insert_new();
        this.log.info(
            "Inbound Async message resp stored in sqlitedb: " +
                JSON.stringify(res)
        );
        if (errs && errs.code) {
            throw errs;
        }
        const ss_response = {
            request_message_id: rjson[rtype].Header.MessageID,
            request_hash: mho,
            request_xml_data: cdata,
            request_json_data: rjson,
            response_xml_data: resp.data,
            response_json_data: rdata,
            response_hash: mh,
            response_message_id: rdata[rtype].Header.MessageID,
        };
        return ss_response;
    }
    return resp;
}
