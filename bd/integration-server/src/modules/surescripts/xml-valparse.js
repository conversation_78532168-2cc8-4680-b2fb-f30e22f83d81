"use strict";

import fs from "fs";
import * as lxml from "libxmljs2";
import * as fxp from "fast-xml-parser";
import { join } from "desm";

/**
 * Validates an XML payload against an XSD schema.
 *
 * @param {Object} req - The request object.
 * @param {Object} payload - The XML payload to validate.
 * @param {Function} done - The callback function to invoke when validation is complete.
 */
function ss_xml_validate(req, payload, done) {
    const schemaPath = `${
        join(import.meta.url) +
        "/fixtures/schemas/ncpdp/" +
        req.server.config.NCPDP_SCHEMA_VERSION +
        "/"
    }`;
    console.log("schemaPath: " + schemaPath);
    const sfile = fs.readFileSync(schemaPath + "transport.xsd").toString();
    const schemaObj = lxml.parseXml(sfile, {
        baseUrl: schemaPath,
        big_lines: true,
    });
    //  const schemaObj = xsd.parseFile(schemaPath + 'transport.xsd', {baseUrl: schemaPath});
    const options = {
        ignoreAttributes: true,
        ignoreDeclaration: true,
    };
    const xmlParser = new fxp.XMLParser(options);
    const parsingOpts = {
        validate: true,
    };
    let rbody = "";
    payload.on("error", errorListener);
    payload.on("data", dataListener);
    payload.on("end", endListener);

    function errorListener(err) {
        console.log("errorListener: " + err.message);
        done(err);
    }
    function endListener() {
        if (parsingOpts.validate) {
            const result = validateXSD(rbody);
            if (result && result.stack) {
                result.statusCode = 400;
                payload.removeListener("error", errorListener);
                payload.removeListener("data", dataListener);
                payload.removeListener("end", endListener);
                done(result, handleParseXml(rbody));
            } else {
                handleParseXml(rbody);
            }
        } else {
            handleParseXml(rbody);
        }
    }
    function dataListener(data) {
        rbody += data;
    }
    function validateXSD(rbody) {
        try {
            const inxml = lxml.parseXmlString(rbody);
            console.log(inxml);
            const xsd_validate = inxml.validate(schemaObj);
            // xsd_validate uses libxmljs2 validate which returns an array of errors or null if
            // the xml is valid. If the array is not null, then there are errors. It also will return an
            // exception if the xml is not syntactically correct.
            if (!xsd_validate) {
                return inxml.validationErrors[0];
            } else {
                return;
            }
        } catch (err) {
            return err;
        }
    }
    function handleParseXml(rbody) {
        try {
            done(null, xmlParser.parse(rbody));
        } catch (err) {
            done(err);
        }
    }
}

/**
 * Validates XML against an XSD schema.
 * @param {Object} app - The application object.
 * @param {string} mainschema - The main schema file name.
 * @param {string} data - The XML data to validate.
 * @returns {Promise<string>} - The validated XML data.
 * @throws {Error} - If the schema file is not found or if there are validation errors.
 */
async function validate_xsd(app, mainschema, data) {
    const schemaPath = `${join(import.meta.url) + "/fixtures/schemas/ncpdp/" + app.config.NCPDP_SCHEMA_VERSION + "/"}`;
    console.log("schemaPath/Schemafile: " + schemaPath + mainschema + ".xsd");
    const sfile = fs.readFileSync(schemaPath + mainschema + ".xsd").toString();
    if (!sfile) {
        throw new Error("Schema not found: " + mainschema);
    }

    const schemaObj = lxml.parseXml(sfile, {
        baseUrl: schemaPath,
        big_lines: true,
    });
    const inxml = lxml.parseXmlString(data);
    //inxml = removeEmpty(inxml);
    const xsd_validate = inxml.validate(schemaObj);

    if (!xsd_validate) {
        if (inxml.validationErrors.length > 0) {
            for (let i = 0; i < inxml.validationErrors.length; i++) {
                app.log.error(
                    `XML validation error: ${inxml.validationErrors[i].message}` +
                        JSON.stringify(inxml.validationErrors[i], null, 2)
                );
            }
        } else {
            app.log.error(
                "XML validation error: " +
                    JSON.stringify(inxml.validationErrors, null, 2)
            );
        }
        throw inxml.validationErrors[0];
    } else {
        return data;
    }
}
export { ss_xml_validate, validate_xsd };
