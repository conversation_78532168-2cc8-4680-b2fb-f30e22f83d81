<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
        <Header>
		<To Qualifier="P">7447853</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>EPCS-NEWRX 4</PrescriberOrderNumber>
		<DigitalSignature Version="1.1">
			<DigitalSignatureIndicator>true</DigitalSignatureIndicator>
		</DigitalSignature>	</Header>
<message>
	<Body>
		<NewRx>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000021633</PayerID>
					<ProcessorIdentificationNumber>A4-AA1</ProcessorIdentificationNumber>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>61041fff5</IINNumber>
				</PayerIdentification>
				<PayerName>PBMF</PayerName>
				<!-- <CardholderID></CardholderID> -->
				<GroupID>TTC1</GroupID>
				<PBMMemberID>YX-5-VA-20011246</PBMMemberID>
			</BenefitsCoordination>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000001011</PayerID>
					<ProcessorIdentificationNumber>MT-R23-36</ProcessorIdentificationNumber>
					<IINNumber>612001</IINNumber>
				</PayerIdentification>
				<PayerName>PBMB</PayerName>
				<CardholderID>123456789</CardholderID>
				<GroupID>AA1V</GroupID>
				<PBMMemberID>HQR%K883883%ZZ88-002</PBMMemberID>
			</BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Whiteside</LastName>
						<FirstName>Kara</FirstName>
					</Name>
					<Gender>F</Gender>
					<DateOfBirth>
						<Date>1952-10-11aa</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>23230 Seaport</AddressLine1>
						<City>Akron</City>
						<StateProvince>OH</StateProvince>
						<PostalCode>44306</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<Extension>2345</Extension>
						</PrimaryTelephone>
						<Beeper>
							<Number>**********</Number>
						</Beeper>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>6445547</NCPDPID>
					<StateLicenseNumber>MASS PH897 9211G</StateLicenseNumber>
					<MedicareNumber>566977</MedicareNumber>
					<MedicaidNumber>566977</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Lawrence Academy Rx 10.6</BusinessName>
				<Address>
					<AddressLine1>235 Main St</AddressLine1>
					<City>Groton</City>
					<StateProvince>MA</StateProvince>
					<PostalCode>01450</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>CV5412421574</StateLicenseNumber>
					<MedicareNumber>335214</MedicareNumber>
					<MedicaidNumber>335214</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTPRX.21574</CertificateToPrescribe>
					<Data2000WaiverID>*********</Data2000WaiverID>
					<REMSHealthcareProviderEnrollmentID>8664416-UP-332</REMSHealthcareProviderEnrollmentID>
				</Identification>
				<Name>
					<LastName>Pinkerton</LastName>
					<FirstName>Umbriana</FirstName>
					<Suffix>MD</Suffix>
				</Name>
				<Address>
					<AddressLine1>6925 Bessemer Ave</AddressLine1>
					<City>Cleveland</City>
					<StateProvince>OH</StateProvince>
					<PostalCode>44127</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<MedicationPrescribed>
				<DrugDescription>Suboxone 8 mg-2 mg sublingual film</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>12496120803</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<DrugDBCode>
						<Code>1010606</Code>
						<Qualifier>SBD</Qualifier>
					</DrugDBCode>
					<DEASchedule>
						<Code>C48676</Code>
					</DEASchedule>
				</DrugCoded>
				<Quantity>
					<Value>30</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C53499</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>30</DaysSupply>
				<WrittenDate>
					<Date>2022-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>1</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>F1120</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Opioid dependence, uncomplicated</Description>
					</Primary>
				</Diagnosis>
				<Note>NADEAN: *********</Note>
				<Sig>
					<SigText>Place 1 flim under the tongue once daily until dissolved</SigText>
				</Sig>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>
