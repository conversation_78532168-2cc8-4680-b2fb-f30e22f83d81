<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">7447853</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<TestMessage>1</TestMessage>
		<TertiaryIdentifier>A1B</TertiaryIdentifier>
		<PrescriberOrderNumber>CORE NEWRX 2 MAX-POP TEST CASE #2a</PrescriberOrderNumber>
		<PrescriberOrderGroup>
			<OrderGroupNumber>CORE NEWRX 2 MAX-POP TESTCASE GRP</OrderGroupNumber>
			<ItemCountInOrderGroup>2</ItemCountInOrderGroup>
			<TotalCountForOrderGroup>2</TotalCountForOrderGroup>
			<OrderGroupReason>NewRx</OrderGroupReason>
		</PrescriberOrderGroup>
	</Header>
	<Body>
		<NewRx>
			<UrgencyIndicatorCode>X</UrgencyIndicatorCode>
			<AllergyOrAdverseEvent>
				<Allergies>
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Adversity to drug</Text>
						<Code>419511003</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>00904272561</Code>
						<Qualifier>ND</Qualifier>
						<Text>Sulfamethoxazole and trimethoprim</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>Hives</Text>
						<Code>247472004</Code>
					</ReactionCoded>
					<SeverityCoded>
						<Text>Moderate</Text>
						<Code>6736007</Code>
					</SeverityCoded>
				</Allergies>
			</AllergyOrAdverseEvent>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000022649</PayerID>
					<ProcessorIdentificationNumber>THISISALONGPCNNUMBERTOTESTMAXLENGTH</ProcessorIdentificationNumber>
					<MutuallyDefined>031430643</MutuallyDefined>
					<IINNumber>004655</IINNumber>
				</PayerIdentification>
				<PayerName>PHARMACEUTICAL LITE FOR MEDICARE AND MEDICAID SERVICES (PLS) PBM/PAYER</PayerName>
				<CardholderID>HEREISACARDHOLDERIDTESTINGMAXLENGTH</CardholderID>
				<CardHolderName>
					<LastName>Usumacintacoatzacoalcosniltepecvera</LastName>
					<FirstName>Juancarlosguadalupepaploapan</FirstName>
				</CardHolderName>
				<GroupID>THISGROUPIDISATTHEMAXIMUMLENGTHOF35</GroupID>
				<GroupName>HEREISAREALLYLONGANDOVERDONEGROUPNAMETOTESTLONG,BUTNOTMAX,SUPPORT</GroupName>
				<PBMMemberID>PLS$KKWS8826-JSHG82_NW91%92KS ZZQ8&amp;ZZQ9-TEST51CHARS</PBMMemberID>
			</BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>WENO</PayerID>
                    <ProcessorIdentificationNumber>HT</ProcessorIdentificationNumber>
                    <IINNumber>011867</IINNumber>
               </PayerIdentification>
                <PayerName>CASH CARD</PayerName>
                <GroupID>BSURE11</GroupID>
                <PayerType>L</PayerType>
            </BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>7417234</PayerID>
                    <ProcessorIdentificationNumber>IRX</ProcessorIdentificationNumber>
                    <IINNumber>610011</IINNumber>
               </PayerIdentification>
                <PayerName>Apply Patient Savings</PayerName>
                <GroupID>2388</GroupID>
                <PayerType>M</PayerType>
            </BenefitsCoordination>
        <Patient>
				<HumanPatient>
					<Name>
						<LastName>Usumacintacoatzacoalcosniltepecvera</LastName>
						<FirstName>Juancarlosguadalupepaploapan</FirstName>
						<MiddleName>Franciscolisandroculiacan</MiddleName>
						<Suffix>Junior</Suffix>
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>2004-06-21</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>27732 West Alameda Potholeladen Street</AddressLine1>
						<AddressLine2>Apt 425-B</AddressLine2>
						<City>Rancho Cucamonga</City>
						<StateProvince>CA</StateProvince>
						<PostalCode>917011515</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<SupportsSMS>Y</SupportsSMS>
						</PrimaryTelephone>
						<ElectronicMail><EMAIL></ElectronicMail>
						<HomeTelephone>
							<Number>**********</Number>
						</HomeTelephone>
						<WorkTelephone>
							<Number>**********</Number>
							<Extension>45422142</Extension>
							<SupportsSMS>N</SupportsSMS>
						</WorkTelephone>
						<OtherTelephone>
							<Number>**********</Number>
						</OtherTelephone>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>2455142</NCPDPID>
					<StateLicenseNumber>796597%PH12%82R</StateLicenseNumber>
					<MedicareNumber>886112</MedicareNumber>
					<MedicaidNumber>886112</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Medi-Blue Rapid Clinic (000)</BusinessName>
				<Address>
					<AddressLine1>2165-B1 Northpoint Parkway</AddressLine1>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95407</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<UPIN>0</UPIN>
					<DEANumber>*********</DEANumber>
					<HIN>0</HIN>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Macnair</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4221</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>7079442121</Number>
					</Fax>
					<HomeTelephone>
						<Number>7074775441</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- height -->
					<VitalSign>8302-2</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>59</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-11-20</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>120</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-11-23</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>IV Infusion Solution</DrugDescription>
				<Quantity>
					<Value>500</Value>
					<CodeListQualifier>CF</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C28254</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>15</DaysSupply>
				<WrittenDate>
					<Date>2019-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>1</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>K1233</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Oral mucositis (ulcerative) due to radiation</Description>
					</Primary>
					<Secondary>
						<Code>Z510</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Encounter for antineoplastic radiation therapy</Description>
					</Secondary>
				</Diagnosis>
				<PriorAuthorization>Q22759CB9475YBV2985BV2B2C43VV54</PriorAuthorization>
				<Note>Ship to patients home</Note>
				<DrugCoverageStatusCode>UN</DrugCoverageStatusCode>
				<Sig>
					<SigText>Inject 5ml every 12 hours for 2 weeks</SigText>
				</Sig>
				<RxFillIndicator>Dispensed And Partially Dispensed</RxFillIndicator>
				<DeliveryRequest>FIRST FILL DELIVERY</DeliveryRequest>
				<DeliveryLocation>CONTACT PATIENT FOR DELIVERY</DeliveryLocation>
				<FlavoringRequested>Y</FlavoringRequested>
				<CompoundInformation>
					<FinalCompoundPharmaceuticalDosageForm>C42945</FinalCompoundPharmaceuticalDosageForm>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Normal Saline 0.45% Solution</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>0.45</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- solution -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C25613</Code>
									<!-- percent -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>500</Value>
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Potassium phosphate 4.4 mEq/ML Soln</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>4.4</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- solution -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C48512</Code>
									<!-- milliequivalent -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>0.005</Value>
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>magnesium sulfate 50% solution</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>50</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- solution -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C25613</Code>
									<!-- percent -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>0.25</Value>
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C48155</Code>
								<!-- grams -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
				</CompoundInformation>
			</MedicationPrescribed>
			<Supervisor>
				<NonVeterinarian>
					<Identification>
						<NPI>**********</NPI>
					</Identification>
					<Name>
						<LastName>Martinson-McPherson</LastName>
						<FirstName>Julianne</FirstName>
						<MiddleName>Annabelle</MiddleName>
						<Suffix>PhD</Suffix>
					</Name>
					<Address>
						<AddressLine1>1425 Mendocino Ave</AddressLine1>
						<AddressLine2>Suite 12-A</AddressLine2>
						<City>Santa Rosa</City>
						<StateProvince>CA</StateProvince>
						<PostalCode>95401</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<Extension>455142</Extension>
							<SupportsSMS>N</SupportsSMS>
						</PrimaryTelephone>
					</CommunicationNumbers>
				</NonVeterinarian>
			</Supervisor>
			<ProhibitRenewalRequest>false</ProhibitRenewalRequest>
		</NewRx>
	</Body>
</Message>
