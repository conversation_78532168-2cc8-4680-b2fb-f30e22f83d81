<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018 NCPDP. All rights reserved. National Council for Prescription Drugs Programs, Inc. (NCPDP) publications are owned by NCPDP, 9240 East Raintree Drive Scottsdale, AZ 85260, and protected by the copyright laws of the United States. 17 U.S.C. §101, et. seq.   

Permission is given to Council members to copy and use the work or any part thereof in connection with the business purposes of the Council members. The work may not be changed or altered. The work may be shared within the member company but may not be distributed and/or copied for/by others outside of the member’s company. The work may not be sold, used or exploited for commercial purposes. This permission may be revoked by NCPDP at any time. NCPDP is not responsible for any errors or damage as a result of the use of the work.

All material is provided "as is", without warranty of any kind, expressed or implied, including but not limited to warranties of merchantability, fitness for a particular purpose, accuracy, completeness and non-infringement of third party rights. In no event shall NCPDP, its members or its contributors be liable for any claim, or any direct, special, indirect or consequential damages, or any damages whatsoever resulting from loss of use, data or profits, whether in an action of contract, negligence or other tortious action, arising out of or in connection with the use or performance of the material.

NCPDP recognizes the confidentiality of certain information exchanged electronically through the use of its standards. Users should be familiar with the federal, state, and local laws, regulations and codes requiring confidentiality of this information and should utilize the standards accordingly.

NOTICE: In addition, this NCPDP Standard contains certain data fields and elements that may be completed by users with the proprietary information of third parties. The use and distribution of third parties' proprietary information without such third parties' consent, or the execution of a license or other agreement with such third party, could subject the user to numerous legal claims. All users are encouraged to contact such third parties to determine whether such information is proprietary and if necessary, to consult with legal counsel to make arrangements for the use and distribution of such proprietary information.
-->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="20170715">
	<xsd:include schemaLocation="ecl.xsd"/>
	<xsd:include schemaLocation="datatypes.xsd"/>
	<xsd:include schemaLocation="structures.xsd"/>
	<!-- ================================================== -->
	<!-- =====  Complex Type Definitions  -->
	<!-- ================================================== -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ApprovedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ApprovedType">
		<xsd:sequence>
			<xsd:element name="ReasonCode" type="ReasonCode" minOccurs="0" maxOccurs="10">
				<xsd:annotation>
					<xsd:documentation>Approved may contain a ReasonCode.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Note" type="an1..70" minOccurs="0"/>
			<xsd:element name="AdditionalMessageIndicator" type="xsd:boolean" minOccurs="0"/>
			<xsd:element name="MessageToFollowMessageIdentifier" type="an1..35" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="NoKnown" type="NoKnown" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ApprovedWithChangesAbstractType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ApprovedWithChangesAbstractType">
		<xsd:sequence>
			<xsd:element name="ReasonCode" type="ReasonCode" minOccurs="0" maxOccurs="10">
				<xsd:annotation>
					<xsd:documentation>ApprovedWithChanges may be accompanied by a ReasonCode.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Note" type="an1..70" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ApprovedWithChangesType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ApprovedWithChangesType">
		<xsd:sequence>
			<xsd:element name="ReasonCode" type="ReasonCode" minOccurs="0" maxOccurs="10">
				<xsd:annotation>
					<xsd:documentation>ApprovedWithChanges may be accompanied by a ReasonCode.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Note" type="an1..70" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ApprovedWithoutReasonTypeForClinicalInfoResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ApprovedWithoutReasonTypeForClinicalInfoResponse">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedApprovedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode14" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Approved may contain a ReasonCode.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..70" minOccurs="0"/>
					<xsd:element name="AdditionalMessageIndicator" type="xsd:boolean"/>
					<xsd:element name="MessageToFollowMessageIdentifier" type="an1..35" minOccurs="0"/>
					<xsd:element name="NoKnown" type="NoKnown" minOccurs="0"/>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  BasicApprovedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="BasicApprovedType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedApprovedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode14" minOccurs="0" maxOccurs="10">
						<xsd:annotation>
							<xsd:documentation>Approved may contain a ReasonCode.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..70" minOccurs="0"/>
					<xsd:element name="AdditionalMessageIndicator" type="xsd:boolean" minOccurs="0" maxOccurs="0"/>
					<xsd:element name="MessageToFollowMessageIdentifier" type="an1..35" minOccurs="0" maxOccurs="0"/>
					<xsd:element name="NoKnown" type="NoKnown" minOccurs="0" maxOccurs="0"/>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFCounsel  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFCounsel">
		<xsd:sequence>
			<xsd:element name="Counsel" type="BooleanCode" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Indicator the patient has requested counseling for the medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CounselNotes" type="an">
				<xsd:annotation>
					<xsd:documentation>Counseling text to be printed on the documentation provided to the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFDrugImprint  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFDrugImprint">
		<xsd:sequence>
			<xsd:element name="Description" type="an"/>
			<xsd:element name="Markings" type="an"/>
			<xsd:element name="DrugShape" type="an"/>
			<xsd:element name="Flavor" type="an"/>
			<xsd:element name="Imprint1" type="an"/>
			<xsd:element name="Imprint2" type="an"/>
			<xsd:element name="Color" type="an"/>
			<xsd:element name="Coating" type="an"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFGenericSubstitutedForBrand  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFGenericSubstitutedForBrand">
		<xsd:sequence>
			<xsd:element name="SubstitutedBrandDrug" type="an"/>
			<xsd:element name="SubstitutionMessage" type="an"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFInventoryItem  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFInventoryItem">
		<xsd:sequence>
			<xsd:element name="DrugDescription" type="an1..105">
				<xsd:annotation>
					<xsd:documentation>See Medication for Definition and Usage</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DrugCoded" type="CFDrugCoded"/>
			<xsd:element name="UnitOfUse" type="BooleanCode">
				<xsd:annotation>
					<xsd:documentation>Indicator the product requires special quantity consideration.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Quantity" type="QuantityWithoutSecondQuantity" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFInventoryList  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFInventoryList">
		<xsd:annotation>
			<xsd:documentation>This transaction provides a list of product codes along with a unit of use flag and a billing unit for items that are currently available for fulfillment at the central fill facility.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="EffectiveDate" type="DateType">
				<xsd:annotation>
					<xsd:documentation>Date that the associated Drug Inventory List will become effective.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFInvList" type="CFInventoryItem" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFManifest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFManifest">
		<xsd:annotation>
			<xsd:documentation>This is initiated by the central fill facility to electronically inform the pharmacy of prescription orders being sent to them in their next delivery.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFTote" type="CFTote"/>
			<xsd:element name="CFManifestDetail" type="CFManifestDetail"/>
			<xsd:element name="MandatoryPharmacy" type="MandatoryPharmacy"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFManifestDetail  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFManifestDetail">
		<xsd:sequence>
			<xsd:element name="CFOrderID" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Unique Identifier assigned by the pharmacy for the prescription fulfillment order</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TotalNumberOfVials" type="n1..3">
				<xsd:annotation>
					<xsd:documentation>Total number of prescription vials included in the Central Fill Order.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TotalNumberOfPackages" type="n1..3">
				<xsd:annotation>
					<xsd:documentation>Total number of packages included in the Central Fill Order.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Patient" type="CFPatientType"/>
			<xsd:element name="RxReferenceNumber" type="an1..35">
				<xsd:annotation>
					<xsd:documentation>Prescription Number associated to medication record.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="FillNumber" type="n2">
				<xsd:annotation>
					<xsd:documentation>Defines the dispensing episode as an initial fill or an authorized refill. Values: 00 = initial fill, 01 = first refill, 02 = second refill, etc. Allowed values 00 thru 99. This element must always be two significant digits (e.g. 01, 02, 08, 14, 99).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RxBarCode" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Barcode representation of prescription as designated by pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DrugDescription" type="an1..105">
				<xsd:annotation>
					<xsd:documentation>See Medication for Definition and Usage</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DrugCoded" type="CFDrugCoded"/>
			<xsd:element name="ManufacturerName" type="an1..105">
				<xsd:annotation>
					<xsd:documentation>Name of the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Quantity" type="Quantity"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFMonograph  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFMonograph">
		<xsd:sequence>
			<xsd:element name="Binary" type="BinaryDataType"/>
			<xsd:element name="MonographText" type="an"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFOrder  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFOrder">
		<xsd:sequence>
			<xsd:element name="CFOrderID" type="an"/>
			<xsd:element name="Priority" type="an" minOccurs="0"/>
			<xsd:element name="MailOrder" type="BooleanCode" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFProductInquiry  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFProductInquiry">
		<xsd:annotation>
			<xsd:documentation>This transaction is initiated by the pharmacy prior to the completion of the prescription filling process to determine if the selected medication and quantity are available from the Central Fill Facility and can be delivered by the requested date.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MandatoryPharmacy" type="MandatoryPharmacy"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0"/>
			<xsd:element name="Medication" type="CFMedicationLotNotUsedInCompoundIngredient"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFProductInquiryResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFProductInquiryResponse">
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFResponse" type="CFProductInquiryResponseType"/>
			<xsd:element name="MandatoryPharmacy" type="MandatoryPharmacy"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0"/>
			<xsd:element name="Medication" type="CFMedicationLotNotUsedInCompoundIngredient"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFProductInquiryResponseDeniedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFProductInquiryResponseDeniedType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedDeniedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode15" maxOccurs="10"/>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DenialReason" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>A DenialReason response must be accompanied by the ReasonCode or DenialReason explanation, or both.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFProductInquiryResponseType  <<choice>>  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFProductInquiryResponseType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedResponseAbstractType">
				<xsd:choice>
					<xsd:element name="Denied" type="CFProductInquiryResponseDeniedType"/>
					<xsd:element name="Approved" type="BasicApprovedType"/>
					<xsd:element name="ApprovedWithChanges" type="ApprovedWithChangesType" minOccurs="0" maxOccurs="0"/>
				</xsd:choice>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxEntryTechnician  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxEntryTechnician">
		<xsd:annotation>
			<xsd:documentation>Used when information about a specific individual – the Rx entry Technician – is exchanged.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="Pharmacist">
				<xsd:sequence>
					<xsd:element name="TechnicianInitials" type="an1..3">
						<xsd:annotation>
							<xsd:documentation>Designated Technician for transaction Field 637-TF</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderCancel  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderCancel">
		<xsd:annotation>
			<xsd:documentation>This transaction can be initiated by either the pharmacy or the central fill facility. When initiated by the pharmacy it is to inform the central fill facility that a previously submitted CFRxOrderRequest is to be canceled and removed from the fulfillment queue.  When initiated by the central fill facility it is inform the pharmacy that the facility was unable to fulfill a previously accepted order request and the pharmacy is to complete the fulfillment of the medication locally.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MandatoryPharmacy" type="MandatoryPharmacy"/>
			<xsd:element name="CFResponse" type="CFRxOrderCancelResponseType" minOccurs="0"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0"/>
			<xsd:element name="Facilty" type="Facility" minOccurs="0"/>
			<xsd:element name="Patient" type="CFPatientType"/>
			<xsd:element name="CFOrder" type="CFOrder"/>
			<xsd:element name="ShipToEntity" type="CFShipToEntity" minOccurs="0"/>
			<xsd:element name="CFRxLabel" type="CFRxOrderType"/>
			<xsd:element name="Medication" type="CFMedicationLotNotUsedInCompoundIngredient"/>
			<xsd:element name="SupervisingPrescriber" type="SupervisorOptionalType" minOccurs="0"/>
			<xsd:element name="Prescriber" type="PrescriberGeneralChoice"/>
			<xsd:element name="ShippingInstructions" type="CFShipping" minOccurs="0"/>
			<xsd:element name="BenefitsCoordination" type="BaseBenefitsCoordination" minOccurs="0" maxOccurs="4">
				<xsd:annotation>
					<xsd:documentation>Indicates coverage’s known to the sending system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderCancelDeniedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderCancelDeniedType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedDeniedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode16" maxOccurs="10"/>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DenialReason" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>A DenialReason response must be accompanied by the ReasonCode or DenialReason explanation, or both.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderCancelResponseType  <<choice>>  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderCancelResponseType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedResponseAbstractType">
				<xsd:choice>
					<xsd:element name="Denied" type="CFRxOrderCancelDeniedType"/>
					<xsd:element name="Approved" type="SpecializedApprovedWithoutReasonType" minOccurs="0" maxOccurs="0"/>
					<xsd:element name="ApprovedWithChanges" type="ApprovedWithChangesType" minOccurs="0" maxOccurs="0"/>
				</xsd:choice>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderCompletion  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderCompletion">
		<xsd:annotation>
			<xsd:documentation>This transaction is sent from the central fill facility to the pharmacy with fulfillment information including delivery and product information.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MandatoryPharmacy" type="MandatoryPharmacy"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0"/>
			<xsd:element name="Facilty" type="Facility" minOccurs="0"/>
			<xsd:element name="RxOrder" type="CFRxOrderTypeMandatoryLot"/>
			<xsd:element name="CFOrder" type="CFOrder"/>
			<xsd:element name="Shipping" type="CFShipping"/>
			<xsd:element name="ShipToEntity" type="CFShipToEntity" minOccurs="0"/>
			<xsd:element name="Patient" type="Patient"/>
			<xsd:element name="Medication" type="CFMedicationLotOptional"/>
			<xsd:element name="SupervisingPrescriber" type="SupervisorOptionalType" minOccurs="0"/>
			<xsd:element name="Prescriber" type="PrescriberGeneralChoice"/>
			<xsd:element name="BenefitsCoordination" type="BaseBenefitsCoordination" minOccurs="0" maxOccurs="4">
				<xsd:annotation>
					<xsd:documentation>Indicates coverage’s known to the sending system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderRequest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderRequest">
		<xsd:annotation>
			<xsd:documentation>This transaction is sent from the pharmacy to the central fill facility requesting the facility accept and fulfill a prescription order.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MandatoryPharmacy" type="MandatoryPharmacy"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0"/>
			<xsd:element name="Facilty" type="Facility" minOccurs="0"/>
			<xsd:element name="Patient" type="CFPatientTypeWithSubstanceUse"/>
			<xsd:element name="Prescriber" type="PrescriberGeneralChoice"/>
			<xsd:element name="SupervisingPrescriber" type="SupervisorOptionalType" minOccurs="0"/>
			<xsd:element name="CFOrder" type="CFOrder"/>
			<xsd:element name="ShippingInstructions" type="CFShipping" minOccurs="0"/>
			<xsd:element name="ShipToEntity" type="CFShipToEntity" minOccurs="0"/>
			<xsd:element name="Medication" type="CFMedicationLotNotUsedInCompoundIngredient"/>
			<xsd:element name="CFRxLabel" type="CFRxOrderType"/>
			<xsd:element name="BenefitsCoordination" type="BaseBenefitsCoordination" minOccurs="0" maxOccurs="4">
				<xsd:annotation>
					<xsd:documentation>Indicates coverage’s known to the sending system.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderType">
		<xsd:sequence>
			<xsd:element name="FillNumber" type="n2">
				<xsd:annotation>
					<xsd:documentation>Defines the dispensing episode as an initial fill or an authorized refill. Values: 00 = initial fill, 01 = first refill, 02 = second refill, etc. Allowed values 00 thru 99. This element must always be two significant digits (e.g. 01, 02, 08, 14, 99).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RxBarCode" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Barcode representation of prescription as designated by pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ChildResistantPackage" type="BooleanCode">
				<xsd:annotation>
					<xsd:documentation>Indicator the prescription requires child resistant packaging.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFGenericSubstitutedForBrand" type="CFGenericSubstitutedForBrand" minOccurs="0"/>
			<xsd:element name="Technician" type="CFRxEntryTechnician" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Designated prescription entry tech for transaction</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RxRefillMessage" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Number of refills remaining and expiration date of prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFDrugImprint" type="CFDrugImprint" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Drug Imprint information for the medication requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFMonograph" type="CFMonograph" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Drug Monograph information for the medication requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFWarningLabel" type="CFWarningLabel" minOccurs="0" maxOccurs="10">
				<xsd:annotation>
					<xsd:documentation>Warning label information for the medication requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Counsel" type="CFCounsel" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Counseling Required indicator and text.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="IngredientCost" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Ingredient cost of the medication dispensed.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PrescriptionSellPrice" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Total amount paid by all plans involved or cash prescription selling price. Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TotalAmountBrandPenalty" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Total amount of penalty by all plans involved for the selection of the brand name drug.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PatientPayAmount" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Field 505-F5 - Amount that is calculated by the processor and returned to the pharmacy as the TOTAL amount to be paid by the patient to the pharmacy; the patient’s total cost share, including copayments, amounts applied to deductible, over maximum amounts, penalties, etc. This is the patient pay amount from the final payer. Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CalculatedPrescriptionSellingPrice" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Calculated selling price for the prescription.  Could be U and C or Gross Amount Due.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DiscountAmount" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Amount of discount that was applied to the prescription.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ShippingAmount" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Amount of shipping cost that was applied to the prescription.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AuthorizationNumber" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Field 503-F3 - Number assigned by the processor to identify an authorized 		transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderTypeMandatoryLot  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderTypeMandatoryLot">
		<xsd:complexContent>
			<xsd:restriction base="CFRxOrderType">
				<xsd:sequence>
					<xsd:element name="FillNumber" type="n2">
						<xsd:annotation>
							<xsd:documentation>Defines the dispensing episode as an initial fill or an authorized refill. Values: 00 = initial fill, 01 = first refill, 02 = second refill, etc. Allowed values 00 thru 99. This element must always be two significant digits (e.g. 01, 02, 08, 14, 99).</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RxBarCode" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Barcode representation of prescription as designated by pharmacy.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ChildResistantPackage" type="BooleanCode">
						<xsd:annotation>
							<xsd:documentation>Indicator the prescription requires child resistant packaging.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CFGenericSubstitutedForBrand" type="CFGenericSubstitutedForBrand" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Generic Substitution indicator and label text.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Technician" type="CFRxEntryTechnician" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Designated prescription entry tech for transaction</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RxRefillMessage" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Number of refills remaining and expiration date of prescription.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CFDrugImprint" type="CFDrugImprint" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Drug Imprint information for the medication requested.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CFMonograph" type="CFMonograph" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Drug Monograph information for the medication requested.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CFWarningLabel" type="CFWarningLabel" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Warning label information for the medication requested.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Counsel" type="CFCounsel" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Counseling Required indicator and text.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="IngredientCost" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Ingredient cost of the medication dispensed.  Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="PrescriptionSellPrice" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Total amount paid by all plans involved or cash prescription selling price. Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="TotalAmountBrandPenalty" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Total amount of penalty by all plans involved for the selection of the brand name drug.  Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="PatientPayAmount" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Field 505-F5 - Amount that is calculated by the processor and returned to the pharmacy as the TOTAL amount to be paid by the patient to the pharmacy; the patient’s total cost share, including copayments, amounts applied to deductible, over maximum amounts, penalties, etc. This is the patient pay amount from the final payer. Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CalculatedPrescriptionSellingPrice" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Calculated selling price for the prescription.  Could be U and C or Gross Amount Due.  Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DiscountAmount" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Amount of discount that was applied to the prescription.  Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ShippingAmount" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Amount of shipping cost that was applied to the prescription.  Include decimal and sign.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="AuthorizationNumber" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Field 503-F3 - Number assigned by the processor to identify an authorized 		transaction.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFRxOrderTypeResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFRxOrderTypeResponse">
		<xsd:sequence>
			<xsd:element name="FillNumber" type="n2">
				<xsd:annotation>
					<xsd:documentation>Defines the dispensing episode as an initial fill or an authorized refill. Values: 00 = initial fill, 01 = first refill, 02 = second refill, etc. Allowed values 00 thru 99. This element must always be two significant digits (e.g. 01, 02, 08, 14, 99).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RxBarCode" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Barcode representation of prescription as designated by pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ChildResistantPackage" type="BooleanCode">
				<xsd:annotation>
					<xsd:documentation>Indicator the prescription requires child resistant packaging.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Response" type="SpecializedResponseAbstractType"/>
			<xsd:element name="CFGenericSubstitutedForBrand" type="CFGenericSubstitutedForBrand" minOccurs="0"/>
			<xsd:element name="Technician" type="CFRxEntryTechnician" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Designated prescription entry tech for transaction</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RxRefillMessage" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Number of refills remaining and expiration date of prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFDrugImprint" type="CFDrugImprint" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Drug Imprint information for the medication requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFMonograph" type="CFMonograph" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Drug Monograph information for the medication requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CFWarningLabel" type="CFWarningLabel" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Warning label information for the medication requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Counsel" type="CFCounsel" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Counseling Required indicator and text.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="IngredientCost" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Ingredient cost of the medication dispensed.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PrescriptionSellPrice" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Total amount paid by all plans involved or cash prescription selling price. Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TotalAmountBrandPenalty" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Total amount of penalty by all plans involved for the selection of the brand name drug.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PatientPayAmount" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Field 505-F5 - Amount that is calculated by the processor and returned to the pharmacy as the TOTAL amount to be paid by the patient to the pharmacy; the patient’s total cost share, including copayments, amounts applied to deductible, over maximum amounts, penalties, etc. This is the patient pay amount from the final payer. Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CalculatedPrescriptionSellingPrice" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Calculated selling price for the prescription.  Could be U and C or Gross Amount Due.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DiscountAmount" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Amount of discount that was applied to the prescription.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ShippingAmount" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Amount of shipping cost that was applied to the prescription.  Include decimal and sign.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AuthorizationNumber" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Field 503-F3 - Number assigned by the processor to identify an authorized 		transaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFShipToEntity  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFShipToEntity">
		<xsd:sequence>
			<xsd:element name="Name" type="NameType"/>
			<xsd:element name="Address" type="AddressType">
				<xsd:annotation>
					<xsd:documentation>Required when the order is to be shipped to customer and not the pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CommunicationNumbers" type="CommunicationNumbersType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFShipping  <<choice>>  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFShipping">
		<xsd:choice>
			<xsd:element name="ShippingMethod" type="an"/>
			<xsd:element name="NeededNoLaterThanDate" type="UtcDateType">
				<xsd:annotation>
					<xsd:documentation>used to notify the CF facility when the requested order is needed by. only value supported - 203 = CCYYMMDDHHMM; C=Century; Y=Year; M=Month; D=Day; H=Hour; M=Minutes.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TimeZone" type="TimeZone" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Defines the time zone used by the sender.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:choice>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CFTote  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="CFTote">
		<xsd:sequence>
			<xsd:element name="ShipToteIdentification" type="an">
				<xsd:annotation>
					<xsd:documentation>Number assigned by Central Fill Facility for the tote</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ToteBarcode" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Barcode representation for the Shipping Tote Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  Census  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="Census">
		<xsd:annotation>
			<xsd:documentation>This transaction is sent from the resident source system to parties that need to be aware of these census changes. Designates type and description of census message – Admit, Discharge, Change. Used when a ReturnReceipt is requested. For a Change Transaction, the patient demographic and coverage information sent should be all information known, not just the changed information.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="SpecializedRequest">
				<xsd:sequence>
					<xsd:element name="MessageRequestCode" type="MessageFunctionCode">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="AllergyOrAdverseEvent" type="AllergyChoice" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>The AllergyOrAdverseEvent element enables the sender to inform the recipient of all known patient allergies. The element is intended to provide this patient Allergy or AdverseEvent profile information to the recipient. If the AllergyOrAdverseEvent element is sent, it must include all known patient allergies. Responder can send back an AllergyOrAdverseEvent element, but should not echo back the AllergyOrAdverseEvent element received on the request. Note: The AllergyOrAdverseEvent element is not to be used to replace or add to the transfer of drug use evaluation information, which is instead communicated using the existing DUE composite in the medication information. This is a comprehensive list of allergies that the sender is aware of at the time the transaction is sent. Trading partners must support the AllergyOrAdverseEvent Element but use is optional on a case by case basis.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="BenefitsCoordination" type="BenefitsCoordinationForCensus" minOccurs="0" maxOccurs="unbounded"/>
					<xsd:element name="DiagnosisGeneral" type="DiagnosisGeneral" minOccurs="0" maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Facility" type="Facility">
						<xsd:annotation>
							<xsd:documentation>The Facility composite may be included to describe the care facility or other location at which the patient resides.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Patient" type="PatientTypeWithSubstanceUseForCensus"/>
					<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Pharmacy" type="Pharmacy" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Prescriber" type="PrescriberGeneralChoice" minOccurs="0"/>
					<xsd:element name="Provider" type="Provider" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Service" type="SpecializedServiceType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="AnticipatedReturnDate" type="SimpleDateType" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CensusEffectiveDate" type="SimpleDateType" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="TimeZone" type="TimeZone" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DispensingRequestCode" type="DispensingRequestCode" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Consent" type="Consent" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..210" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="PriorPeriodCorrection" type="PriorPeriodCorrectionType" minOccurs="0"/>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalInfoRequest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ClinicalInfoRequest">
		<xsd:annotation>
			<xsd:documentation>Query transactions are used for the exchange of patient-centric clinical health information.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="SpecializedRequest">
				<xsd:sequence>
					<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="BenefitsCoordination" type="BaseBenefitsCoordination" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DiagnosisGeneral" type="DiagnosisGeneral" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Facility" type="Facility" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>The Facility composite may be included to describe the care facility or other location at which the patient resides.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Patient" type="PatientType"/>
					<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Pharmacy" type="MandatoryAddressPharmacy">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Prescriber" type="MandatoryPrescriberChoice"/>
					<xsd:element name="Provider" type="Provider" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Service" type="GlobalServiceType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="AnticipatedReturnDate" type="SimpleDateType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CensusEffectiveDate" type="SimpleDateType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="TimeZone" type="TimeZone" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DispensingRequestCode" type="DispensingRequestCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Consent" type="Consent">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..210" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalInfoResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ClinicalInfoResponse">
		<xsd:annotation>
			<xsd:documentation>The QueryResponse will indicate if the request is denied or approved and how the information will be sent.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="SpecializedResponse">
				<xsd:sequence>
					<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Facility" type="Facility" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>The Facility composite may be included to describe the care facility or other location at which the patient resides.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Patient" type="PatientType"/>
					<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Pharmacy" type="MandatoryAddressPharmacy">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Prescriber" type="MandatoryPrescriberChoice"/>
					<xsd:element name="Provider" type="Provider" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Service" type="SpecializedServiceType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Response" type="ClinicalInformationResponseType">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Consent" type="Consent">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..210" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalInfoResponseDeniedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ClinicalInfoResponseDeniedType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedDeniedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode8" maxOccurs="10"/>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DenialReason" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>A DenialReason response must be accompanied by the ReasonCode or DenialReason explanation, or both.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalInformationResponseType  <<choice>>  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ClinicalInformationResponseType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedResponseAbstractType">
				<xsd:choice>
					<xsd:element name="Denied" type="ClinicalInfoResponseDeniedType"/>
					<xsd:element name="Approved" type="ApprovedWithoutReasonTypeForClinicalInfoResponse"/>
					<xsd:element name="ApprovedWithChanges" type="ApprovedWithChangesType" minOccurs="0" maxOccurs="0"/>
				</xsd:choice>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GlobalServiceType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GlobalServiceType">
		<xsd:annotation>
			<xsd:documentation>Used in MTMServiceRequest, MTMServiceResponse</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="MTMPayerCaseIdentifier" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Unique identifier assigned by the payer to this case. MTMPayerCaseIdentifier is subservient to the patient identifier. (A patient may have multiple cases, distinguished by multiple case identifiers.)</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:choice>
				<xsd:element name="TypeOfServiceFreeText" type="an">
					<xsd:annotation>
						<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="TypeOfService" type="QualifiedCodeWithText">
					<xsd:annotation>
						<xsd:documentation>Note: The codification for MTM services is underway. At this point, to exchange the Service, the industry will exchange free text (TypeOfServiceFreeText) until services are codified. TypeOfServiceFreeText may be used or (TypeOfServiceCodeQualifier, TypeOfServiceCode and TypeOfServiceCodeText) – but not both. Once codified, TypeOfServiceCodeText, TypeOfServiceCode, TypeOfServiceCodeQualifier will be used and TypeOfServiceFreeText will be sunsetted.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:choice>
			<xsd:choice minOccurs="0">
				<xsd:element name="TargetedTypeOfServiceFreeText" type="an">
					<xsd:annotation>
						<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="TargetedTypeOfService" type="QualifiedCodeWithText">
					<xsd:annotation>
						<xsd:documentation>Note: The TargetedTypeOfServiceCodeQualifier contains code lists from which a codified TargetedTypeOfServiceCode and TargetedTypeOfServiceCodeText are to be populated. However, if a code does not exist for the target, free text may be used in the TargetedTypeOfServiceFreeText. TargetedTypeOfServiceFreeText may be used or (TargetedTypeOfServiceCodeQualifier, TargetedTypeOfServiceCode and TargetedTypeOfServiceCodeText) – but not both.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:choice>
			<xsd:element name="EffectiveDate" type="DateType" minOccurs="0"/>
			<xsd:element name="ExpirationDate" type="DateType" minOccurs="0"/>
			<xsd:element name="TotalNumberOfEncountersApproved" type="n1..4" minOccurs="0"/>
			<xsd:element name="FrequencyOfEncountersApproved" type="QualifiedCodeWithText" minOccurs="0"/>
			<xsd:element name="DateOfService" type="SimpleDateType" minOccurs="0"/>
			<xsd:choice minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Result, the industry will exchange free text (ResultOfActionFreeText) until actions are codified. ResultOfActionFreeText may be used or (ResultOfActionQualifier, ResultOfActionText and ResultOfActionCode); but not both. Once codified, ResultOfActionText, ResultOfActionCode, ResultOfActionQualifier will be used and ResultOfActionFreeText will be sunsetted.</xsd:documentation>
				</xsd:annotation>
				<xsd:element name="ResultOfActionFreeText" type="an"/>
				<xsd:element name="ResultOfAction" type="QualifiedCodeWithText"/>
			</xsd:choice>
			<xsd:choice minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Reason, the industry will exchange free text (ReasonForMTMServiceFreeText) until actions are codified. ReasonForMTMServiceFreeText may be used or (ReasonForMTMServiceQualifier, ReasonForMTMServiceText and ReasonForMTMServiceCode) but not both. Once codified, ReasonForMTMServiceText, ReasonForMTMServiceCode, ReasonForMTMServiceQualifier will be used and ReasonForMTMServiceFreeText will be sunsetted.</xsd:documentation>
				</xsd:annotation>
				<xsd:element name="ReasonForMTMServiceFreeText" type="an"/>
				<xsd:element name="ReasonForMTMService" type="QualifiedCodeWithText"/>
			</xsd:choice>
			<xsd:choice minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Action, the industry will exchange free text (ResultActionFreeText) until actions are codified. MTMActionFreeText may be used or (MTMActionQualifier, MTMActionText and MTMActionCode) but not both. Once codified, MTMActionText, MTMActionCode, MTMActionQualifier will be used and MTMActionFreeText will be sunsetted.</xsd:documentation>
				</xsd:annotation>
				<xsd:element name="MTMActionFreeText" type="an"/>
				<xsd:element name="MTMAction" type="QualifiedCodeWithText"/>
			</xsd:choice>
			<xsd:element name="TypeOfServiceGroupSetting" type="an" default="Y" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MTMResponseType  <<choice>>  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="MTMResponseType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedResponseAbstractType">
				<xsd:choice>
					<xsd:element name="Denied" type="MTMServiceResponseDeniedType"/>
					<xsd:element name="Approved" type="SpecializedApprovedWithoutReasonType"/>
					<xsd:element name="ApprovedWithChanges" type="ApprovedWithChangesType" minOccurs="0" maxOccurs="0"/>
				</xsd:choice>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MTMServiceDocumentation  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="MTMServiceDocumentation">
		<xsd:annotation>
			<xsd:documentation>The MedicationTherapy Management (MTM) Service Documentation transaction (MTMServiceDocumentation) is a description of services rendered, which may include clinical information.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="SpecializedRequest">
				<xsd:sequence>
					<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="BenefitsCoordination" type="BenefitsCoordination" maxOccurs="4">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DiagnosisGeneral" type="DiagnosisGeneral" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Facility" type="Facility" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>The Facility composite may be included to describe the care facility or other location at which the patient resides.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Patient" type="PatientFullTypeWithSubstanceUse"/>
					<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Pharmacy" type="Pharmacy" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Prescriber" type="PrescriberGeneralChoice" minOccurs="0"/>
					<xsd:element name="Provider" type="Provider" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Service" type="MTMServiceDocumentationType">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="AnticipatedReturnDate" type="SimpleDateType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CensusEffectiveDate" type="SimpleDateType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="TimeZone" type="TimeZone" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DispensingRequestCode" type="DispensingRequestCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Consent" type="Consent" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..210" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MTMServiceDocumentationType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="MTMServiceDocumentationType">
		<xsd:annotation>
			<xsd:documentation>Used in MTMServiceRequest, MTMServiceResponse</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="GlobalServiceType">
				<xsd:sequence>
					<xsd:element name="MTMPayerCaseIdentifier" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Unique identifier assigned by the payer to this case. MTMPayerCaseidentifier is subservient to the patient identifier. (A patient may have multiple cases, distinguished by multiple case identifiers.)</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:choice>
						<xsd:element name="TypeOfServiceFreeText" type="an">
							<xsd:annotation>
								<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="TypeOfService" type="QualifiedCodeWithText">
							<xsd:annotation>
								<xsd:documentation>Note: The codification for MTM services is underway. At this point, to exchange the Service, the industry will exchange free text (TypeOfServiceFreeText) until services are codified. TypeOfServiceFreeText may be used or (TypeOfServiceCodeQualifier, TypeOfServiceCode and TypeOfServiceCodeText) – but not both. Once codified, TypeOfServiceCodeText, TypeOfServiceCode, TypeOfServiceCodeQualifier will be used and TypeOfServiceFreeText will be sunsetted.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:choice>
					<xsd:choice minOccurs="0">
						<xsd:element name="TargetedTypeOfServiceFreeText" type="an">
							<xsd:annotation>
								<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="TargetedTypeOfService" type="QualifiedCodeWithText">
							<xsd:annotation>
								<xsd:documentation>Note: The TargetedTypeOfServiceCodeQualifier contains code lists from which a codified TargetedTypeOfServiceCode and TargetedTypeOfServiceCodeText are to be populated. However, if a code does not exist for the target, free text may be used in the TargetedTypeOfServiceFreeText. TargetedTypeOfServiceFreeText may be used or (TargetedTypeOfServiceCodeQualifier, TargetedTypeOfServiceCode and TargetedTypeOfServiceCodeText) – but not both.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:choice>
					<xsd:element name="EffectiveDate" type="DateType" minOccurs="0"/>
					<xsd:element name="ExpirationDate" type="DateType" minOccurs="0"/>
					<xsd:element name="TotalNumberOfEncountersApproved" type="n1..4" minOccurs="0"/>
					<xsd:element name="FrequencyOfEncountersApproved" type="QualifiedCodeWithText" minOccurs="0"/>
					<xsd:element name="DateOfService" type="SimpleDateType"/>
					<xsd:choice>
						<xsd:annotation>
							<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Result, the industry will exchange free text (ResultOfActionFreeText) until actions are codified. ResultOfActionFreeText may be used or (ResultOfActionQualifier, ResultOfActionText and ResultOfActionCode); but not both. Once codified, ResultOfActionText, ResultOfActionCode, ResultOfActionQualifier will be used and ResultOfActionFreeText will be sunsetted.</xsd:documentation>
						</xsd:annotation>
						<xsd:element name="ResultOfActionFreeText" type="an"/>
						<xsd:element name="ResultOfAction" type="QualifiedCodeWithText"/>
					</xsd:choice>
					<xsd:choice maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Reason, the industry will exchange free text (ReasonForMTMServiceFreeText) until actions are codified. ReasonForMTMServiceFreeText may be used or (ReasonForMTMServiceQualifier, ReasonForMTMServiceText and ReasonForMTMServiceCode) but not both. Once codified, ReasonForMTMServiceText, ReasonForMTMServiceCode, ReasonForMTMServiceQualifier will be used and ReasonForMTMServiceFreeText will be sunsetted.</xsd:documentation>
						</xsd:annotation>
						<xsd:element name="ReasonForMTMServiceFreeText" type="an"/>
						<xsd:element name="ReasonForMTMService" type="QualifiedCodeWithText"/>
					</xsd:choice>
					<xsd:choice maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Action, the industry will exchange free text (MTMActionFreeText) until actions are codified. MTMActionFreeText may be used or (MTMActionQualifier, MTMActionText and MTMActionCode); but not both. Once codified, MTMActionText, MTMActionCode, MTMActionQualifier will be used and MTMActionFreeText will be sunsetted.</xsd:documentation>
						</xsd:annotation>
						<xsd:element name="MTMActionFreeText" type="an"/>
						<xsd:element name="MTMAction" type="QualifiedCodeWithText"/>
					</xsd:choice>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MTMServiceRequest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="MTMServiceRequest">
		<xsd:annotation>
			<xsd:documentation>This message is sent by the Payer to the Pharmacy or the Prescriber. The Payer is requesting the Pharmacy or Prescriber accept the patient and perform the service.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="SpecializedRequest">
				<xsd:sequence>
					<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="unbounded">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="BenefitsCoordination" type="BenefitsCoordination" maxOccurs="4">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DiagnosisGeneral" type="DiagnosisGeneral" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Facility" type="Facility" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>The Facility composite may be included to describe the care facility or other location at which the patient resides.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Patient" type="PatientFullTypeWithSubstanceUse"/>
					<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Pharmacy" type="Pharmacy" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Prescriber" type="PrescriberGeneralChoice" minOccurs="0"/>
					<xsd:element name="Provider" type="Provider" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Service" type="SpecializedServiceType" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="AnticipatedReturnDate" type="SimpleDateType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="CensusEffectiveDate" type="SimpleDateType" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="TimeZone" type="TimeZone" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DispensingRequestCode" type="DispensingRequestCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Consent" type="Consent" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..210" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecializedRequest for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MTMServiceResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="MTMServiceResponse">
		<xsd:annotation>
			<xsd:documentation>This message is sent by the Pharmacy or Prescriber to the Payer. It is the response to an MTMServiceRequest. The Pharmacy or Prescriber either accepts the patient and service, or rejects the patient and service.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="SpecializedResponse">
				<xsd:sequence>
					<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Facility" type="Facility" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>The Facility composite may be included to describe the care facility or other location at which the patient resides.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Patient" type="PatientFullTypeWithSubstanceUse"/>
					<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Pharmacy" type="Pharmacy" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Prescriber" type="PrescriberGeneralChoice" minOccurs="0"/>
					<xsd:element name="Provider" type="Provider" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Service" type="SpecializedServiceType" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Response" type="MTMResponseType">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Consent" type="Consent" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..210" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>See SpecialzedResponse for Definition and Usage</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MTMServiceResponseDeniedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="MTMServiceResponseDeniedType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedDeniedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode4" maxOccurs="10"/>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DenialReason" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>A DenialReason response must be accompanied by the ReasonCode or DenialReason explanation, or both.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedApprovedAbstractType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedApprovedAbstractType">
		<xsd:sequence>
			<xsd:element name="ReasonCode" type="ReasonCode" minOccurs="0" maxOccurs="10">
				<xsd:annotation>
					<xsd:documentation>Approved may contain a ReasonCode.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Note" type="an1..70" minOccurs="0"/>
			<xsd:element name="AdditionalMessageIndicator" type="xsd:boolean" minOccurs="0"/>
			<xsd:element name="MessageToFollowMessageIdentifier" type="an1..35" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="NoKnown" type="NoKnown" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedApprovedWithoutReasonType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedApprovedWithoutReasonType">
		<xsd:complexContent>
			<xsd:restriction base="SpecializedApprovedAbstractType">
				<xsd:sequence>
					<xsd:element name="ReasonCode" type="ReasonCode14" minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Approved may contain a ReasonCode.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="Note" type="an1..70" minOccurs="0"/>
					<xsd:element name="AdditionalMessageIndicator" type="xsd:boolean" minOccurs="0" maxOccurs="0"/>
					<xsd:element name="MessageToFollowMessageIdentifier" type="an1..35" minOccurs="0" maxOccurs="0"/>
					<xsd:element name="NoKnown" type="NoKnown" minOccurs="0" maxOccurs="0"/>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedDeniedAbstractType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedDeniedAbstractType">
		<xsd:sequence>
			<xsd:element name="ReasonCode" type="ReasonCode" maxOccurs="10"/>
			<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DenialReason" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>A DenialReason response must be accompanied by the ReasonCode or DenialReason explanation, or both.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedDeniedType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedDeniedType">
		<xsd:sequence>
			<xsd:element name="ReasonCode" type="ReasonCode" maxOccurs="10"/>
			<xsd:element name="ReferenceNumber" type="an1..35" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Transaction Key. Used in conjunction with same field described above in the RequestReferenceNumber. The Mailbox may return the positional key of the specific piece of mail within their files.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DenialReason" type="an" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>A DenialReason response must be accompanied by the ReasonCode or DenialReason explanation, or both.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedRequest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedRequest" abstract="true">
		<xsd:sequence>
			<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>For Long Term Care (LTC) Medication Change Process (Prescriber initiated CHANGE Request,) the NEWRX and the CANCEL will use this field to indicate the level of the change.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Indicator that this message is a follow-up </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Provides patient summary information when applicable</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AllergyOrAdverseEvent" type="AllergyChoice" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The AllergyOrAdverseEvent element enables the sender to inform the recipient of all known patient allergies. The element is intended to provide this patient Allergy or AdverseEvent profile information to the recipient. If the AllergyOrAdverseEvent element is sent, it must include all known patient allergies. Responder can send back an AllergyOrAdverseEvent element, but should not echo back the AllergyOrAdverseEvent element received on the request. Note: The AllergyOrAdverseEvent element is not to be used to replace or add to the transfer of drug use evaluation information, which is instead communicated using the existing DUE composite in the medication information. This is a comprehensive list of allergies that the sender is aware of at the time the transaction is sent. Trading partners must support the AllergyOrAdverseEvent Element but use is optional on a case by case basis.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="BenefitsCoordination" type="BaseBenefitsCoordinationAbstract" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="DiagnosisGeneral" type="DiagnosisGeneral" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Element may be used only in the long-term care setting, where there are established processes supporting the sharing of diagnosis information between a resident’s facility and pharmacy. Note: The medication information enables communication of prescription-specific diagnosis information only—appropriate for use in new prescription messaging, but insufficient to support management of a resident’s full drug regimen. The intent is to convey the diagnosis that the prescribed medication is for.  There is also a need to convey diagnosis information in the Census event.  The difference here is that there is no related drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Facility" type="Facility" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>One loop mandatory for the skilled nursing facility.
Given that a new long term care resident may not yet have an assigned physician, it is more appropriate to require the facility information, and physician if that information is available.

 In long-term care settings, if the recipient is a facility, Facility is required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Patient" type="PatientFullTypeAbstract" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designated Pharmacist for transaction</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Pharmacy" type="Pharmacy" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designated Pharmacy for transaction

If recipient is a pharmacy, Pharmacy is required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Prescriber" type="HistoryPrescriberChoice" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="Provider" type="Provider" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designated Provider for transaction</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Service" type="GlobalServiceType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designates the service information to be exchanged.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AnticipatedReturnDate" type="SimpleDateType" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="CensusEffectiveDate" type="SimpleDateType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>For Census, ChangeRequestType values D3 (Discharge – Return Anticipated), D4 (Discharge Other), LT (Therapeutic Leave of Absence) and LG (Hospital Leave of Absence), - value AR (Anticipated Patient Return Date/Time) is used in the second Date loop. When populated, the second date loop indicates the date and time of the resident’s anticipated return to the facility, using value “AR”.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TimeZone" type="TimeZone" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Defines the time zone used by the sender.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DispensingRequestCode" type="DispensingRequestCode" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Code conveying a pharmacy dispensing action associated with a CENSUS event. Only allowed in the CENSUS message, when the Anticipated Patient Return date is sent and is Hospital Leave of Absence (LG) or Therapeutic Leave of Absence (LT).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Consent" type="Consent" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="Note" type="an1..210" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Used to provide extra information about the request as desired. Note should never be used as a simple email communication between parties. It must be relevant to the purpose of the initial request.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PriorPeriodCorrection" type="PriorPeriodCorrectionType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedResponse" abstract="true">
		<xsd:sequence>
			<xsd:element name="MessageRequestCode" type="MessageFunctionCode" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>For Long Term Care (LTC) Medication Change Process (Prescriber initiated CHANGE Request,) the NEWRX and the CANCEL will use this field to indicate the level of the change.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReturnReceipt" type="an1..3" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>If this field is submitted with 1 in the request, a Verify transaction is to be sent from the recipient at some time.
1 = Return Receipt Requested
all other values = no return receipt requested.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RequestReferenceNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="FollowUpRequest" type="n1..1" minOccurs="0"/>
			<xsd:element name="Facility" type="Facility" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>One loop mandatory for the skilled nursing facility.
Given that a new long term care resident may not yet have an assigned physician, it is more appropriate to require the facility information, and physician if that information is available.

 In long-term care settings, if the recipient is a facility, Facility is required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Patient" type="PatientFullTypeAbstract" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="Pharmacist" type="Pharmacist" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designated Pharmacist for transaction</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Pharmacy" type="Pharmacy" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designated Pharmacy for transaction

If recipient is a pharmacy, Pharmacy is required.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Prescriber" type="HistoryPrescriberChoice" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="Provider" type="Provider" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designated Provider for transaction</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Service" type="SpecializedServiceType" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Designates the service information to be exchanged.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Response" type="SpecializedResponseAbstractType" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="Consent" type="Consent" minOccurs="0"/>
			<xsd:element name="ClinicalInfoFormatsSupported" type="ClinicalInformationFormatSupported" minOccurs="0"/>
			<xsd:element name="ClinicalInfoTypesRequested" type="ClinicalInfoTypesRequested" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="ClinicalInfoAttachment" type="Attachment" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Provides patient summary information when applicable</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Note" type="an1..210" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Used to provide extra information about the request as desired. Note should never be used as a simple email communication between parties. It must be relevant to the purpose of the initial request.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedResponseAbstractType  <<choice>>  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedResponseAbstractType">
		<xsd:choice>
			<xsd:element name="Denied" type="SpecializedDeniedAbstractType" minOccurs="0"/>
			<xsd:element name="Approved" type="SpecializedApprovedAbstractType" minOccurs="0"/>
			<xsd:element name="ApprovedWithChanges" type="ApprovedWithChangesType" minOccurs="0"/>
		</xsd:choice>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SpecializedServiceType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="SpecializedServiceType">
		<xsd:annotation>
			<xsd:documentation>Used in MTMServiceRequest, MTMServiceResponse</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:restriction base="GlobalServiceType">
				<xsd:sequence>
					<xsd:element name="MTMPayerCaseIdentifier" type="an" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Unique identifier assigned by the payer to this case. MTMPayerCaseidentifier is subservient to the patient identifier. (A patient may have multiple cases, distinguished by multiple case identifiers.)</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:choice>
						<xsd:element name="TypeOfServiceFreeText" type="an">
							<xsd:annotation>
								<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="TypeOfService" type="QualifiedCodeWithText">
							<xsd:annotation>
								<xsd:documentation>Note: The codification for MTM services is underway. At this point, to exchange the Service, the industry will exchange free text (TypeOfServiceFreeText) until services are codified. TypeOfServiceFreeText may be used or (TypeOfServiceCodeQualifier, TypeOfServiceCode and TypeOfServiceCodeText) – but not both. Once codified, TypeOfServiceCodeText, TypeOfServiceCode, TypeOfServiceCodeQualifier will be used and TypeOfServiceFreeText will be sunsetted.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:choice>
					<xsd:choice minOccurs="0">
						<xsd:element name="TargetedTypeOfServiceFreeText" type="an">
							<xsd:annotation>
								<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="TargetedTypeOfService" type="QualifiedCodeWithText">
							<xsd:annotation>
								<xsd:documentation>Note: The TargetedTypeOfServiceCodeQualifier contains code lists from which a codified TargetedTypeOfServiceCode and TargetedTypeOfServiceCodeText are to be populated. However, if a code does not exist for the target, free text may be used in the TargetedTypeOfServiceFreeText. TargetedTypeOfServiceFreeText may be used or (TargetedTypeOfServiceCodeQualifier, TargetedTypeOfServiceCode and TargetedTypeOfServiceCodeText) – but not both.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:choice>
					<xsd:element name="EffectiveDate" type="DateType" minOccurs="0"/>
					<xsd:element name="ExpirationDate" type="DateType" minOccurs="0"/>
					<xsd:element name="TotalNumberOfEncountersApproved" type="n1..4" minOccurs="0"/>
					<xsd:element name="FrequencyOfEncountersApproved" type="QualifiedCodeWithText" minOccurs="0"/>
					<xsd:element name="DateOfService" type="SimpleDateType" minOccurs="0" maxOccurs="0"/>
					<xsd:choice minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Result, the industry will exchange free text (ResultOfActionFreeText) until actions are codified. ResultOfActionFreeText may be used or (ResultOfActionQualifier, ResultOfActionText and ResultOfActionCode); but not both. Once codified, ResultOfActionText, ResultOfActionCode, ResultOfActionQualifier will be used and ResultOfActionFreeText will be sunsetted.</xsd:documentation>
						</xsd:annotation>
						<xsd:element name="ResultOfActionFreeText" type="an"/>
						<xsd:element name="ResultOfAction" type="QualifiedCodeWithText"/>
					</xsd:choice>
					<xsd:choice minOccurs="0">
						<xsd:annotation>
							<xsd:documentation> Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Reason, the industry will exchange free text (ReasonForMTMServiceFreeText) until actions are codified. ReasonForMTMServiceFreeText may be used or (ReasonForMTMServiceQualifier, ReasonForMTMServiceText and ReasonForMTMServiceCode) but not both. Once codified, ReasonForMTMServiceText, ReasonForMTMServiceCode, ReasonForMTMServiceQualifier will be used and ReasonForMTMServiceFreeText will be sunsetted.</xsd:documentation>
						</xsd:annotation>
						<xsd:element name="ReasonForMTMServiceFreeText" type="an"/>
						<xsd:element name="ReasonForMTMService" type="QualifiedCodeWithText"/>
					</xsd:choice>
					<xsd:choice minOccurs="0" maxOccurs="0">
						<xsd:annotation>
							<xsd:documentation>Can send FreeText or Code/Qualifier/Text - but not both. If a code list exists, the Code/Qualifier/Text are used, where Text must be from the Code Source, not free text. Note: The codification for MTM services is underway. At this point, to exchange the Action, the industry will exchange free text (MTMActionFreeText) until actions are codified. MTMActionFreeText may be used or (MTMActionQualifier, MTMActionText and MTMActionCode); but not both. Once codified, MTMActionText, MTMActionCode, MTMActionQualifier will be used and MTMActionFreeText will be sunsetted.</xsd:documentation>
						</xsd:annotation>
						<xsd:element name="MTMActionFreeText" type="an"/>
						<xsd:element name="MTMAction" type="QualifiedCodeWithText"/>
					</xsd:choice>
					<xsd:element name="TypeOfServiceGroupSetting" type="an" default="Y" minOccurs="0"/>
				</xsd:sequence>
			</xsd:restriction>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:annotation>
		<xsd:documentation>The NCPDP specialized.xsd defines Business Domain Transactions.</xsd:documentation>
	</xsd:annotation>
</xsd:schema>
