<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018 NCPDP. All rights reserved. National Council for Prescription Drugs Programs, Inc. (NCPDP) publications are owned by NCPDP, 9240 East Raintree Drive Scottsdale, AZ 85260, and protected by the copyright laws of the United States. 17 U.S.C. §101, et. seq.   

Permission is given to Council members to copy and use the work or any part thereof in connection with the business purposes of the Council members. The work may not be changed or altered. The work may be shared within the member company but may not be distributed and/or copied for/by others outside of the member’s company. The work may not be sold, used or exploited for commercial purposes. This permission may be revoked by NCPDP at any time. NCPDP is not responsible for any errors or damage as a result of the use of the work.

All material is provided "as is", without warranty of any kind, expressed or implied, including but not limited to warranties of merchantability, fitness for a particular purpose, accuracy, completeness and non-infringement of third party rights. In no event shall NCPDP, its members or its contributors be liable for any claim, or any direct, special, indirect or consequential damages, or any damages whatsoever resulting from loss of use, data or profits, whether in an action of contract, negligence or other tortious action, arising out of or in connection with the use or performance of the material.

NCPDP recognizes the confidentiality of certain information exchanged electronically through the use of its standards. Users should be familiar with the federal, state, and local laws, regulations and codes requiring confidentiality of this information and should utilize the standards accordingly.

NOTICE: In addition, this NCPDP Standard contains certain data fields and elements that may be completed by users with the proprietary information of third parties. The use and distribution of third parties' proprietary information without such third parties' consent, or the execution of a license or other agreement with such third party, could subject the user to numerous legal claims. All users are encouraged to contact such third parties to determine whether such information is proprietary and if necessary, to consult with legal counsel to make arrangements for the use and distribution of such proprietary information.
-->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="20170715">
	<!-- ================================================== -->
	<!-- =====  Simple Type Definitions  -->
	<!-- ================================================== -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  AdditionalFreeTextIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="AdditionalFreeTextIndicator">
		<xsd:annotation>
			<xsd:documentation>Indicates if the prescriber is allowed to supply additional free text with their answer to the question.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="M">
				<xsd:annotation>
					<xsd:documentation>Mandatory, provider must supply additional free text</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="O">
				<xsd:annotation>
					<xsd:documentation>Optional, provider may supply additional free text</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NA">
				<xsd:annotation>
					<xsd:documentation>Not Allowed, provider may not supply additional free text</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  AddressTypeQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="AddressTypeQualifier">
		<xsd:annotation>
			<xsd:documentation>Qualifies the To or From.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Clinic</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="M">
				<xsd:annotation>
					<xsd:documentation>Mailbox</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Prescriber</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CF">
				<xsd:annotation>
					<xsd:documentation>Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZZZ">
				<xsd:annotation>
					<xsd:documentation>Mutually Defined</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PY">
				<xsd:annotation>
					<xsd:documentation>Payer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DIRECT">
				<xsd:annotation>
					<xsd:documentation>Direct ID (secure email address)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="REMS">
				<xsd:annotation>
					<xsd:documentation>REMS Administrator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  AdministrationIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="AdministrationIndicator">
		<xsd:annotation>
			<xsd:documentation>Indicates the action to be taken on the Administration fields.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="MANUFACTURERSINSTRUCTIONS">
				<xsd:annotation>
					<xsd:documentation>As directed per manufacturer’s packaging.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  AllergyDrugProductCodedQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="AllergyDrugProductCodedQualifier">
		<xsd:annotation>
			<xsd:documentation>The code list used to identify the product to which the patient is allergic.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>National Drug Code (NDC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UP">
				<xsd:annotation>
					<xsd:documentation>Universal Product Code (UPC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RT">
				<xsd:annotation>
					<xsd:documentation>National Drug File Reference (NDF-RT)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>Health Related Item (HRI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UN">
				<xsd:annotation>
					<xsd:documentation>Unique Ingredient Identifier (UNII)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SCD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Clinical Drug (SCD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SBD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Branded Drug (SBD) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Generic Package (GPCK) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Branded Package (BPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GMP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Marketed Product Identifier (MPid) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPI">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Product Identifier (ProdID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GSP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Specific Product Identifier (SPID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DI">
				<xsd:annotation>
					<xsd:documentation>Device Identifier</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  AlternateContactRelationship  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="AlternateContactRelationship">
		<xsd:annotation>
			<xsd:documentation>Alternate contact relationship to the patient. And Code indicating the relationship between the financial guarantor and the patient.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="0">
				<xsd:annotation>
					<xsd:documentation>Not Applicable</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Spouse</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Son or Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3">
				<xsd:annotation>
					<xsd:documentation>Father or Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4">
				<xsd:annotation>
					<xsd:documentation>Grandfather or Grandmother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="5">
				<xsd:annotation>
					<xsd:documentation>Grandson or Granddaughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="6">
				<xsd:annotation>
					<xsd:documentation>Uncle or Aunt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="7">
				<xsd:annotation>
					<xsd:documentation>Nephew or Niece</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="8">
				<xsd:annotation>
					<xsd:documentation>Cousin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="9">
				<xsd:annotation>
					<xsd:documentation>Adopted Child</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="10">
				<xsd:annotation>
					<xsd:documentation>Foster Child</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="11">
				<xsd:annotation>
					<xsd:documentation>Son-in-law or Daughter-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="12">
				<xsd:annotation>
					<xsd:documentation>Brother-in-law or Sister-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="13">
				<xsd:annotation>
					<xsd:documentation>Mother-in-law or Father-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="14">
				<xsd:annotation>
					<xsd:documentation>Brother or Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="15">
				<xsd:annotation>
					<xsd:documentation>Ward</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="16">
				<xsd:annotation>
					<xsd:documentation>Stepparent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="17">
				<xsd:annotation>
					<xsd:documentation>Stepson or Stepdaughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="18">
				<xsd:annotation>
					<xsd:documentation>Self</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="19">
				<xsd:annotation>
					<xsd:documentation>Child  - Dependent between the ages of � and 19; age qualifications may vary depending on policy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="20">
				<xsd:annotation>
					<xsd:documentation>Employee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="21">
				<xsd:annotation>
					<xsd:documentation>Unknown</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="22">
				<xsd:annotation>
					<xsd:documentation>Handicapped Dependent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="23">
				<xsd:annotation>
					<xsd:documentation>Sponsored Dependent - Dependents between the ages of 19 and 25 not attending school; age qualifications may vary depending on policy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="24">
				<xsd:annotation>
					<xsd:documentation>Dependent of a Minor Dependent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="25">
				<xsd:annotation>
					<xsd:documentation>Ex-spouse</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="26">
				<xsd:annotation>
					<xsd:documentation>Guardian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="27">
				<xsd:annotation>
					<xsd:documentation>Student - Dependent between the ages of 19 and 25 attending school; age qualifications may vary depending on policy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="28">
				<xsd:annotation>
					<xsd:documentation>Friend</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="29">
				<xsd:annotation>
					<xsd:documentation>Significant Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="30">
				<xsd:annotation>
					<xsd:documentation>Both Parents - The residence or legal custody of the student is with both parents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="31">
				<xsd:annotation>
					<xsd:documentation>Court Appointed Guardian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="32">
				<xsd:annotation>
					<xsd:documentation>Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="33">
				<xsd:annotation>
					<xsd:documentation>Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="34">
				<xsd:annotation>
					<xsd:documentation>Other Adult</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="36">
				<xsd:annotation>
					<xsd:documentation>Emancipated Minor - A person who has been judged by a court of competent jurisdiction to be allowed to act in his or her own interest; no adult is legally responsible for this minor; this may be declared as a result of marriage</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="37">
				<xsd:annotation>
					<xsd:documentation>Agency Representative</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="38">
				<xsd:annotation>
					<xsd:documentation>Collateral Dependent - Relative related by blood or marriage who resides in the home and is dependent on the insured for a major portion of their support</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="39">
				<xsd:annotation>
					<xsd:documentation>Organ Donor - Individual receiving medical service in order to donate organs for a transplant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="40">
				<xsd:annotation>
					<xsd:documentation>Cadaver Donor - Deceased individual donating body to be used for research or transplants</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="41">
				<xsd:annotation>
					<xsd:documentation>Injured Plaintiff</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="43">
				<xsd:annotation>
					<xsd:documentation>Child Where Insured Has No Financial Responsibility - Child is covered by the insured but the insured is not the legal guardian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="45">
				<xsd:annotation>
					<xsd:documentation>Widow</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="46">
				<xsd:annotation>
					<xsd:documentation>Widower</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="47">
				<xsd:annotation>
					<xsd:documentation>State Fund - The state affiliated insurance organization providing coverage and or benefits to the claimant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="48">
				<xsd:annotation>
					<xsd:documentation>Stepfather</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="49">
				<xsd:annotation>
					<xsd:documentation>Stepmother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="50">
				<xsd:annotation>
					<xsd:documentation>Foster Parent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="51">
				<xsd:annotation>
					<xsd:documentation>Emergency Contact</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="52">
				<xsd:annotation>
					<xsd:documentation>Employer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="53">
				<xsd:annotation>
					<xsd:documentation>Life Partner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="55">
				<xsd:annotation>
					<xsd:documentation>Adopted Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="56">
				<xsd:annotation>
					<xsd:documentation>Adopted Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="57">
				<xsd:annotation>
					<xsd:documentation>Adoptive Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="58">
				<xsd:annotation>
					<xsd:documentation>Adoptive Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="59">
				<xsd:annotation>
					<xsd:documentation>Adoptive Parents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="60">
				<xsd:annotation>
					<xsd:documentation>Annuitant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="61">
				<xsd:annotation>
					<xsd:documentation>Aunt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="62">
				<xsd:annotation>
					<xsd:documentation>Brother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="63">
				<xsd:annotation>
					<xsd:documentation>Brother-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="64">
				<xsd:annotation>
					<xsd:documentation>Business</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="65">
				<xsd:annotation>
					<xsd:documentation>Business Associate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="66">
				<xsd:annotation>
					<xsd:documentation>Business Insurance Trust</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="67">
				<xsd:annotation>
					<xsd:documentation>Business Partner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="68">
				<xsd:annotation>
					<xsd:documentation>Charity</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="70">
				<xsd:annotation>
					<xsd:documentation>Children of Marriage</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="71">
				<xsd:annotation>
					<xsd:documentation>Company</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="72">
				<xsd:annotation>
					<xsd:documentation>Corporation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="73">
				<xsd:annotation>
					<xsd:documentation>Creditor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="74">
				<xsd:annotation>
					<xsd:documentation>Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="75">
				<xsd:annotation>
					<xsd:documentation>Daughter-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="76">
				<xsd:annotation>
					<xsd:documentation>Dependent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="78">
				<xsd:annotation>
					<xsd:documentation>Estate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="79">
				<xsd:annotation>
					<xsd:documentation>Ex-wife</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="80">
				<xsd:annotation>
					<xsd:documentation>Family Member</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="81">
				<xsd:annotation>
					<xsd:documentation>Father-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="82">
				<xsd:annotation>
					<xsd:documentation>Fiancé (Male)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="83">
				<xsd:annotation>
					<xsd:documentation>Financée (Female)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="84">
				<xsd:annotation>
					<xsd:documentation>Fiduciary</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="86">
				<xsd:annotation>
					<xsd:documentation>Foster Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="87">
				<xsd:annotation>
					<xsd:documentation>Foster Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="88">
				<xsd:annotation>
					<xsd:documentation>Foster Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="90">
				<xsd:annotation>
					<xsd:documentation>Foster Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="91">
				<xsd:annotation>
					<xsd:documentation>God Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="92">
				<xsd:annotation>
					<xsd:documentation>God Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="93">
				<xsd:annotation>
					<xsd:documentation>God Parents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="94">
				<xsd:annotation>
					<xsd:documentation>God Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="95">
				<xsd:annotation>
					<xsd:documentation>Grandchildren</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="96">
				<xsd:annotation>
					<xsd:documentation>Granddaughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="97">
				<xsd:annotation>
					<xsd:documentation>Grandfather</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="98">
				<xsd:annotation>
					<xsd:documentation>Grandmother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="99">
				<xsd:annotation>
					<xsd:documentation>Grandparents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A1">
				<xsd:annotation>
					<xsd:documentation>Grandson</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A2">
				<xsd:annotation>
					<xsd:documentation>Great Aunt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A3">
				<xsd:annotation>
					<xsd:documentation>Ex-husband</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A4">
				<xsd:annotation>
					<xsd:documentation>Half Brother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A5">
				<xsd:annotation>
					<xsd:documentation>Half Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A6">
				<xsd:annotation>
					<xsd:documentation>Husband</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A7">
				<xsd:annotation>
					<xsd:documentation>Institution</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A8">
				<xsd:annotation>
					<xsd:documentation>Mortgage Holder</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A9">
				<xsd:annotation>
					<xsd:documentation>Mother-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B1">
				<xsd:annotation>
					<xsd:documentation>Nephew</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B2">
				<xsd:annotation>
					<xsd:documentation>Niece</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B3">
				<xsd:annotation>
					<xsd:documentation>Parents-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B4">
				<xsd:annotation>
					<xsd:documentation>Partnership</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B5">
				<xsd:annotation>
					<xsd:documentation>Partner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B6">
				<xsd:annotation>
					<xsd:documentation>Personal Insurance Trust</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B7">
				<xsd:annotation>
					<xsd:documentation>Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B8">
				<xsd:annotation>
					<xsd:documentation>Sister-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B9">
				<xsd:annotation>
					<xsd:documentation>Sole Proprietorship</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C1">
				<xsd:annotation>
					<xsd:documentation>Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C2">
				<xsd:annotation>
					<xsd:documentation>Son-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C3">
				<xsd:annotation>
					<xsd:documentation>Step Brother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C4">
				<xsd:annotation>
					<xsd:documentation>Step Children</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C5">
				<xsd:annotation>
					<xsd:documentation>Step Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C8">
				<xsd:annotation>
					<xsd:documentation>Step Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C9">
				<xsd:annotation>
					<xsd:documentation>Step Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D1">
				<xsd:annotation>
					<xsd:documentation>Trust</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D2">
				<xsd:annotation>
					<xsd:documentation>Trustee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D3">
				<xsd:annotation>
					<xsd:documentation>Uncle</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D4">
				<xsd:annotation>
					<xsd:documentation>Wife</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D5">
				<xsd:annotation>
					<xsd:documentation>Teacher</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D6">
				<xsd:annotation>
					<xsd:documentation>School Counselor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D7">
				<xsd:annotation>
					<xsd:documentation>School Principal</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D8">
				<xsd:annotation>
					<xsd:documentation>Other School Administrator</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D9">
				<xsd:annotation>
					<xsd:documentation>Coach</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E1">
				<xsd:annotation>
					<xsd:documentation>Activity Sponsor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E2">
				<xsd:annotation>
					<xsd:documentation>Supervisor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E3">
				<xsd:annotation>
					<xsd:documentation>Co-worker</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E4">
				<xsd:annotation>
					<xsd:documentation>Minister or Priest</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E5">
				<xsd:annotation>
					<xsd:documentation>Ecclesiastical or Religious Leader</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E6">
				<xsd:annotation>
					<xsd:documentation>God Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E7">
				<xsd:annotation>
					<xsd:documentation>Probation Officer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E8">
				<xsd:annotation>
					<xsd:documentation>Accountant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E9">
				<xsd:annotation>
					<xsd:documentation>Advisor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F1">
				<xsd:annotation>
					<xsd:documentation>Alma Mater</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F2">
				<xsd:annotation>
					<xsd:documentation>Applicant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F3">
				<xsd:annotation>
					<xsd:documentation>Banker</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F6">
				<xsd:annotation>
					<xsd:documentation>Clergyman</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F7">
				<xsd:annotation>
					<xsd:documentation>Client</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F8">
				<xsd:annotation>
					<xsd:documentation>Club or Organization Officer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F9">
				<xsd:annotation>
					<xsd:documentation>Doctor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G2">
				<xsd:annotation>
					<xsd:documentation>Educator/Teacher/Instructor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G3">
				<xsd:annotation>
					<xsd:documentation>Betrothed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G4">
				<xsd:annotation>
					<xsd:documentation>Insured</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G5">
				<xsd:annotation>
					<xsd:documentation>Lawyer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G6">
				<xsd:annotation>
					<xsd:documentation>Medical Care Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G7">
				<xsd:annotation>
					<xsd:documentation>Neighbor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G8">
				<xsd:annotation>
					<xsd:documentation>Other Relationship</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G9">
				<xsd:annotation>
					<xsd:documentation>Other Relative</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H1">
				<xsd:annotation>
					<xsd:documentation>Owner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H4">
				<xsd:annotation>
					<xsd:documentation>Payor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N1">
				<xsd:annotation>
					<xsd:documentation>None</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OT">
				<xsd:annotation>
					<xsd:documentation>Non-applicable Individual Relationship Category</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZZ">
				<xsd:annotation>
					<xsd:documentation>Mutually Defined</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  AttachmentRequired  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="AttachmentRequired">
		<xsd:annotation>
			<xsd:documentation>An indicator an attachment is required.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Y">
				<xsd:annotation>
					<xsd:documentation>Yes</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N">
				<xsd:annotation>
					<xsd:documentation>No</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  BooleanCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="BooleanCode">
		<xsd:restriction base="Code">
			<xsd:enumeration value="Y">
				<xsd:annotation>
					<xsd:documentation>Yes</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N">
				<xsd:annotation>
					<xsd:documentation>No</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ChangeOfPrescriptionStatusCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ChangeOfPrescriptionStatusCode">
		<xsd:annotation>
			<xsd:documentation>Used in the CancelRx Request message when the prescriber wishes to notify the pharmacy to no longer continue dispensing any open refills on an active prescription or to cancel a prescription that has not yet been dispensed.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Cancel - Prescriber wishes to notify the pharmacy to cancel a prescription that has not yet been dispensed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Discontinue - Prescriber wishes to notify the pharmacy to no longer continue dispensing any open refills on an active prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalInfoTypesRequested  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ClinicalInfoTypesRequested">
		<xsd:annotation>
			<xsd:documentation>Requested patient clinical information types. Situation: Used in the Clinical Info Request message to indicate what type of patient centric clinical information is being requested.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="All">
				<xsd:annotation>
					<xsd:documentation>All clinical information available to be shared.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Allergies">
				<xsd:annotation>
					<xsd:documentation>Allergy information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Conditions">
				<xsd:annotation>
					<xsd:documentation>Conditions information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Medical History">
				<xsd:annotation>
					<xsd:documentation>Medical history information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TransplantHealthCareFacilityDischargeDateMedicare">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the Medicare patient who received a transplant was discharged from the health care facility where the transplant was performed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TransplantHealthCareFacilityDischargeDateNonMedicare">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the patient who received a transplant was discharged from the health care facility where the transplant was performed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TransplantDate">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the patient received a transplant.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OtherHealthCareFacilityDischargeDate">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the patient was discharged from the health care facility.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalInformationQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ClinicalInformationQualifier">
		<xsd:annotation>
			<xsd:documentation>Qualifies how the PrimaryDiagnosis was obtained.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Prescriber/Prescriber Supplied - The diagnosis was given or supplied by the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Pharmacy Inferred - The pharmacy inferred the diagnosis using his/her professional judgment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ClinicalSignificanceCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ClinicalSignificanceCode">
		<xsd:annotation>
			<xsd:documentation>Code identifying the significance or severity level of a clinical event as contained in the originating database.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Major - Code indicating that an event, transaction, etc. is of the highest importance; action required to prevent adverse drug event.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Moderate – Code indicating that an event, transaction, etc. is of mid-level significance; requires thoughtful review before prescribing/dispensing the medication. Risk vs. benefit should be evaluated. </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3">
				<xsd:annotation>
					<xsd:documentation>Minor – Code indicating a non-life threatening, annoying, or now-well-documented effect which may or may not require a change in drug therapy. </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="9">
				<xsd:annotation>
					<xsd:documentation>Undetermined - value to describe a professional service with variable or unknown severity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CoAgentQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="CoAgentQualifier">
		<xsd:annotation>
			<xsd:documentation>Code qualifying the value in DUE Co-Agent ID.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="01">
				<xsd:annotation>
					<xsd:documentation>Universal Product Code (UPC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="02">
				<xsd:annotation>
					<xsd:documentation>Health Related Item (HRI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="03">
				<xsd:annotation>
					<xsd:documentation>National Drug Code (NDC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="04">
				<xsd:annotation>
					<xsd:documentation>Health Industry Business Communications Council (HIBCC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="07">
				<xsd:annotation>
					<xsd:documentation>Current Procedural Terminology (CPT4)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="08">
				<xsd:annotation>
					<xsd:documentation>Current Procedural Terminology (CPT5)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="09">
				<xsd:annotation>
					<xsd:documentation>Healthcare Common Procedure Coding System (HCPCS)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="11">
				<xsd:annotation>
					<xsd:documentation>National Pharmaceutical Product Interface Code (NAPPI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="12">
				<xsd:annotation>
					<xsd:documentation>Global Trade Identification Number (GTIN)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="14">
				<xsd:annotation>
					<xsd:documentation>Medi Span's Generic Product Identifier (GPI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="15">
				<xsd:annotation>
					<xsd:documentation>First DataBank Formulation ID (GCN)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="16">
				<xsd:annotation>
					<xsd:documentation>Truven/Micromedex Generic Formulation Code (GFC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="17">
				<xsd:annotation>
					<xsd:documentation>Medi Span's Drug Descriptor ID (DDID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="18">
				<xsd:annotation>
					<xsd:documentation>First DataBank SmartKey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="19">
				<xsd:annotation>
					<xsd:documentation>Truven/Micromedex Generic Master (GM)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="20">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases (ICD9)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="21">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-10-Clinical Modifications (ICD-10-CM)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="23">
				<xsd:annotation>
					<xsd:documentation>National Criteria Care Institute (NCCI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="24">
				<xsd:annotation>
					<xsd:documentation>The Systematized Nomenclature of Medicine Clinical Terms (SNOMED)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="25">
				<xsd:annotation>
					<xsd:documentation>Current Dental Terminology (CDT)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="26">
				<xsd:annotation>
					<xsd:documentation>American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM IV)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="27">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-10-Procedure Coding System (ICD-10-PCS)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="28">
				<xsd:annotation>
					<xsd:documentation>First DataBank Medication Name ID (FDB Med Name ID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="29">
				<xsd:annotation>
					<xsd:documentation>First DataBank Routed Medication ID (FDB Routed Med ID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="30">
				<xsd:annotation>
					<xsd:documentation>First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="31">
				<xsd:annotation>
					<xsd:documentation>First DataBank Medication ID (FDB MedID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="32">
				<xsd:annotation>
					<xsd:documentation>First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="33">
				<xsd:annotation>
					<xsd:documentation>A six-character numeric indicator that identifies a unique combination of active ingredients, irrespective of the manufacturer, package size, dosage form, route of administration, or strength.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="37">
				<xsd:annotation>
					<xsd:documentation>Suite of products providing peer-reviewed information on medicines and drug products, including off-label and labeled uses, drug interactions; adverse reactions; cautions and toxicity; therapeutic perspective; specific dosage and administration information; preparations, chemistry, and stability; pharmacology and pharmacokinetics; contraindications. The AHFS Classification System has been maintained by the American Society of Health-System Pharmacists since 1959.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="38">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Clinical Drug (SCD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="39">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Branded Drug (SBD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="40">
				<xsd:annotation>
					<xsd:documentation>RxNorm Generic Package (GPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="41">
				<xsd:annotation>
					<xsd:documentation>RxNorm Branded Package (BPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="99">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DI">
				<xsd:annotation>
					<xsd:documentation>The Device Identifier (DI) portion of the Unique Device Identifier (UDI) - A unique numeric or alphanumeric code on a device label, packaging or product.  The code is in plain text and machine readable.  Cannot be used until the Telecommunication Standard vF2 is mandated.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  Code  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="Code">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([!-~]|[ ])*[!-~]([!-~]|[ ])*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CodedReferenceQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="CodedReferenceQualifier">
		<xsd:annotation>
			<xsd:documentation>Qualifier to identify the code system being used.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>NCICode - NCI Values - NCPDP Drug StrengthForm Terminology - available at http://www.cancer.gov/cancertopics/terminologyresources/page7 For NCPDP Specific Terminology.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>NCICode - NCI Values - NCPDP Drug StrengthUnitOfMeasure Terminology - available at http://www.cancer.gov/cancertopics/terminologyresources/page7 For NCPDP Specific Terminology.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ABF">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-1Ø-Clinical Modifications (ICD-1Ø-CM) - Code indicating that the following information is a diagnosis as defined by ICD-1Ø-CM. As of January 1, 1999, the ICD-1Ø is used to code and classify mortality data from death certificates. The International Classification of Diseases, 1Øth Revision, Clinical Modification (ICD-9-CM) is a statistical classification system that arranges diseases and injuries into groups according to established criteria. The codes are 3 to 7 digits with the first digit alpha, the second and third numeric and the remainder A/N. The codes are maintained by the World Health Organization and published by the Centers for Medicare and Medicaid Services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>NCICode - NCI values – NCPDP Drug QuantityUnitOfMeasure Terminology - available at http://www.cancer.gov/cancertopics/terminologyresources/page7  For NCPDP Specific Terminology.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Branded Package (BPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CPT4">
				<xsd:annotation>
					<xsd:documentation>Common Procedure Terminology (CPT4) - Code indicating that the following data is a CPT® code. Current Procedural Terminology (CPT®) Fourth Edition is a listing of descriptive terms and identifying codes for reporting medical services and procedures. The code set is managed by the CPT Editorial panel and is maintained and published by the American Medical Association. Also known as Healthcare Common Procedure System (HCPCS) Level I.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CPT5">
				<xsd:annotation>
					<xsd:documentation>Common Procedure Terminology (CPT5) - Enhancements to CPT® 4 in development with emphasis on maintaining what works while correcting problems and extending the applicability of CPT into new areas.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CVX ">
				<xsd:annotation>
					<xsd:documentation>Coding system for vaccines – http://www2a.cdc.gov/vaccines/IIS/IISStandards/vaccines.asp?rpt=cvx.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-9- Clinical Modifications-Diagnosis (ICD-9-CM-Diagnosis) Code indicating the diagnosis is defined according to the International Classification of Diseases, 9th Revision, Clinical Modification (ICD-9-CM) is a statistical classification system that arranges diseases and injuries into groups according to established criteria. Most codes are numeric and consist of 3, 4, or 5 numbers and a description. The codes are maintained by the World Health Organization and published by the Centers for Medicare and Medicaid Services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Generic Package (GPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HCPCS">
				<xsd:annotation>
					<xsd:documentation>HCPCS - Healthcare Common Procedure Coding System (HCPCS)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HL7OID">
				<xsd:annotation>
					<xsd:documentation>HL7 Object Identifier – see www.hl7.org</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HL7TEMPLATEID">
				<xsd:annotation>
					<xsd:documentation>HL7 Template Identifier – see www.hl7.org</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LD">
				<xsd:annotation>
					<xsd:documentation>SNOMED - Systematized Nomenclature of Medicine Clinical Terms® (SNOMED CT) SNOMED CT® terminology which is available from the Health Terminology Standards Development Organisation (IHTSDO) http://www.ihtsdo.org/snomed-ct/ </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LOINC">
				<xsd:annotation>
					<xsd:documentation>A standard question code defined in the Logical Observation Identifier Names and Codes database. See http://www.LOINC.org</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NDC">
				<xsd:annotation>
					<xsd:documentation>National Drug Code is a unique 1Ø-digit, 3-segment number, assigned to each drug product by the FDA. For consistency in billing and reimbursement in the pharmacy services sector of healthcare, the NDC in a unique 11 digit formatted number, a zero is added. This number identifies the labeler/manufacturer, product, and package size of the drug. The first segment is the labeler code and assigned by the FDA. The second segment, the product code, identifies a specific strength, dosage form, and formulation. The third segment, the package code, identifies package sizes. Both the product and package codes are assigned by the labeler/manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RT">
				<xsd:annotation>
					<xsd:documentation>NDF-RT – National Drug File Reference Terminology  - Maintained by VA, distributed by NCI -  for classes of medications</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SBD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Branded Drug (SBD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SCD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Clinical Drug (SCD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UCUM">
				<xsd:annotation>
					<xsd:documentation>The Unified Code for Units of Measure is a code system intended to include all units of measures being contemporarily used in international science, engineering, and business. The purpose is to facilitate unambiguous electronic communication of quantities together with their units. http://unitsofmeasure.org/trac/</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UNII">
				<xsd:annotation>
					<xsd:documentation>The UNII is a part of the joint USP/FDA Substance Registration System (SRS), which has been designed to support health information technology initiatives by providing unique identifiers for substances in drugs, biologics, foods, and devices based on molecular structure and/or descriptive information. The SRS is used to generate permanent, unique, unambiguous identifiers for substances in regulated products, such as ingredients in drug products.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZZ">
				<xsd:annotation>
					<xsd:documentation>Mutually defined.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DI">
				<xsd:annotation>
					<xsd:documentation>Device Identifier</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ComparisonOperator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ComparisonOperator">
		<xsd:annotation>
			<xsd:documentation>Code that conveys the relationship between the answered value to a question and a defined boundary.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="EQ">
				<xsd:annotation>
					<xsd:documentation>Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GT">
				<xsd:annotation>
					<xsd:documentation>Greater Than</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LT">
				<xsd:annotation>
					<xsd:documentation>Less Than</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GE">
				<xsd:annotation>
					<xsd:documentation>Greater Than or Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LE">
				<xsd:annotation>
					<xsd:documentation>Less Than or Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NE">
				<xsd:annotation>
					<xsd:documentation>Not Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  CompoundIngredientProductCodeQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="CompoundIngredientProductCodeQualifier">
		<xsd:annotation>
			<xsd:documentation>The code list defining the CompoundIngredientProductCode.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>National Drug Code (NDC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UP">
				<xsd:annotation>
					<xsd:documentation>Universal Product Code (UPC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RT">
				<xsd:annotation>
					<xsd:documentation>National Drug File Reference (NDF-RT)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>Health Related Item (HRI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UN">
				<xsd:annotation>
					<xsd:documentation>Unique Ingredient Identifier (UNII)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SCD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Clinical Drug (SCD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SBD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Branded Drug (SBD) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Generic Package (GPCK) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Branded Package (BPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GMP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Marketed Product Identifier (MPid) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPI">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Product Identifier (ProdID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GSP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Specific Product Identifier (SPID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DI">
				<xsd:annotation>
					<xsd:documentation>Device Identifier</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  Consent  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="Consent">
		<xsd:annotation>
			<xsd:documentation>Patient Consent Indicator</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="Y">
				<xsd:annotation>
					<xsd:documentation>Patient gave consent for the information to be disclosed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N">
				<xsd:annotation>
					<xsd:documentation>Patient consent not given.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Patient gave consent for prescriber to only receive the medication history this prescriber prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="X">
				<xsd:annotation>
					<xsd:documentation>Parental/Guardian gave consent for the information to be disclosed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Z">
				<xsd:annotation>
					<xsd:documentation>Parental/Guardian consent on behalf of a minor for prescriber to only receive the medication history this prescriber prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TPO">
				<xsd:annotation>
					<xsd:documentation>Required by Health Plan for payment of associated services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DeliveryLocation  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DeliveryLocation">
		<xsd:annotation>
			<xsd:documentation>If patient specifies a delivery, this is the location for the delivery.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="HOME">
				<xsd:annotation>
					<xsd:documentation>Use patient address.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FACILITY">
				<xsd:annotation>
					<xsd:documentation>Use facility address.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CONTACT PATIENT FOR DELIVERY">
				<xsd:annotation>
					<xsd:documentation>Contact information is contained in patient elements.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AGENCY OF SERVICE">
				<xsd:annotation>
					<xsd:documentation>Use agency of service address.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PROVIDER">
				<xsd:annotation>
					<xsd:documentation>Use provider address.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DeliveryRequest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DeliveryRequest">
		<xsd:annotation>
			<xsd:documentation>Indicator of whether patient requests delivery of prescription.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="NO DELIVERY">
				<xsd:annotation>
					<xsd:documentation>No delivery required.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FIRST FILL DELIVERY">
				<xsd:annotation>
					<xsd:documentation>Deliver only first fill of prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ALL FILLS DELIVERY">
				<xsd:annotation>
					<xsd:documentation>Deliver all fills of prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DescriptionCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DescriptionCode">
		<xsd:annotation>
			<xsd:documentation>Reject Codes used by responder who takes responsibility for transaction</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="008">
				<xsd:annotation>
					<xsd:documentation>Request timed out before response could be received.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="103">
				<xsd:annotation>
					<xsd:documentation>COO cardholder last name is invalid.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="134">
				<xsd:annotation>
					<xsd:documentation>Sending a Quantity Sufficient with Quantity of 0 is invalid for this pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="144">
				<xsd:annotation>
					<xsd:documentation>Number of refills invalid</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="210">
				<xsd:annotation>
					<xsd:documentation>Unable to process transaction. Please resubmit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="220">
				<xsd:annotation>
					<xsd:documentation>Transaction is a duplicate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="500">
				<xsd:annotation>
					<xsd:documentation>XML syntax error – Parser error (error that would be caught by the XML parser. Xpath of the element must accompany).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4000">
				<xsd:annotation>
					<xsd:documentation>Intermediary is unable to deliver transaction to the recipient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4010">
				<xsd:annotation>
					<xsd:documentation>Intermediary is unable to process response from recipient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4020">
				<xsd:annotation>
					<xsd:documentation>Intermediary system error.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4030">
				<xsd:annotation>
					<xsd:documentation>Sender not allowed to send this transaction type.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4040">
				<xsd:annotation>
					<xsd:documentation>Receiver does not support receiving this transaction type.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1000">
				<xsd:annotation>
					<xsd:documentation>Unable to identify based on the information submitted (Xpath of the element must accompany).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2000">
				<xsd:annotation>
					<xsd:documentation>Data format is valid for the element, but content is invalid for the situation/context (Xpath of the element must accompany).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3000">
				<xsd:annotation>
					<xsd:documentation>Does not follow NCPDP standard or implementation guide rules (Xpath of the element must accompany).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DigestMethod  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DigestMethod">
		<xsd:annotation>
			<xsd:documentation>Defines the Hashing method to be used.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="SHA-1">
				<xsd:annotation>
					<xsd:documentation>Secure Hash Algorithm 1 is a cryptographic hash function designed by the United States National Security Agency and is a U.S. Federal Information Processing Standard published by the United States National Institute of Standards and Technology (NIST).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SHA-256">
				<xsd:annotation>
					<xsd:documentation>Secure Hash Algorithm 256 is a cryptographic hash function designed by the United States National Security Agency and is a U.S. Federal Information Processing Standard published by the United States National Institute of Standards and Technology (NIST).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DispensedPackageMethod  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DispensedPackageMethod">
		<xsd:annotation>
			<xsd:documentation>Indicate the methods by which treatment was dispensed.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="CYCLEFILL">
				<xsd:annotation>
					<xsd:documentation>Resupply sent automatically without facility request.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ONDEMAND">
				<xsd:annotation>
					<xsd:documentation>Resupply sent on request of facility.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="REMOTEDISPENSING">
				<xsd:annotation>
					<xsd:documentation>Doses are from a remote dispensing machine at the facility.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DispensingRequestCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DispensingRequestCode">
		<xsd:annotation>
			<xsd:documentation>Code conveying a pharmacy dispensing action associated with a CENSUS event.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="LOA">
				<xsd:annotation>
					<xsd:documentation>Request for the pharmacy to dispense a supply of medications in user packaging for use during a Leave of Absence</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DoNotFill  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DoNotFill">
		<xsd:annotation>
			<xsd:documentation>Used for medications ordered by a prescriber but not requiring dispensing at this time, but may be required for administration and may be available for drug-to-drug interactions.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="Y">
				<xsd:annotation>
					<xsd:documentation>Yes - Used for medications ordered by a prescriber not requiring dispensing, but available for reference and drug-to-drug interaction checking.  </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H">
				<xsd:annotation>
					<xsd:documentation>Hold - Indicates the prescriber’s authorization for the pharmacy to fill a prescription, but the prescriber recommends that the pharmacy wait for the patient to request it before filling it.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E">
				<xsd:annotation>
					<xsd:documentation>Do Not Fill – Used for a cover prescription for a previously called in emergency oral prescription as necessary for controlled substances.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DrugAdminReasonCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DrugAdminReasonCode">
		<xsd:annotation>
			<xsd:documentation>Code identifying the reason for the message</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="01">
				<xsd:annotation>
					<xsd:documentation>Awaiting lab action (procedure or result) – Medication administration suspended in preparation for a lab procedure or awaiting lab results.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="02">
				<xsd:annotation>
					<xsd:documentation>Awaiting clinical procedure (non-lab) – Medication administration suspended in preparation for a clinical procedure not lab related.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="03">
				<xsd:annotation>
					<xsd:documentation>Other clinical reason – Medication administration suspended for other clinical reason not procedure or lab related. Suggest further details be provided in text field.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="04">
				<xsd:annotation>
					<xsd:documentation>Non-clinical reason – Medication administration suspended for a non-clinical reason. Suggest further details be provided in text field.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="05">
				<xsd:annotation>
					<xsd:documentation>Patient request – Medication administration suspended at the request of the patient or patient’s representative.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DrugCoverageStatusCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DrugCoverageStatusCode">
		<xsd:annotation>
			<xsd:documentation>Code identifying the coverage status of the prescribed drug.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="PR">
				<xsd:annotation>
					<xsd:documentation>Preferred - Preferred means available on a pharmaceutical formulary in a manner such that the product is given preference in dispensing decisions over competing products in a therapeutic class or therapeutic use.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Approved - The product is included in the plan formulary.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization Required - A prior authorization is required before the prescription can be dispensed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NF">
				<xsd:annotation>
					<xsd:documentation>Non Formulary - The product is not included in the plan formulary.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NR">
				<xsd:annotation>
					<xsd:documentation>Not Reimbursed - The product is not reimbursable in the plan formulary.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>Differential Co-Pay - The product may be subject to potentially higher copay.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UN">
				<xsd:annotation>
					<xsd:documentation>Unknown - The coverage status code is not discernible.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ST">
				<xsd:annotation>
					<xsd:documentation>Step Therapy Required - The plan formulary requires that medication in a specific drug class be tried prior to the requested medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SI">
				<xsd:annotation>
					<xsd:documentation>Signed Prescription – This indicates the prescription has been signed according to the DEA requirements for electronic prescribing of controlled substances.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DrugDBCodeQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="DrugDBCodeQualifier">
		<xsd:annotation>
			<xsd:documentation>Qualifies DrugDBCode.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="E">
				<xsd:annotation>
					<xsd:documentation>Truven/Micromedex Generic Formulation Code (GFC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G">
				<xsd:annotation>
					<xsd:documentation>Truven/Micromedex Generic Master (GM)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>American Hospital Formulary Service (AHFS)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FG">
				<xsd:annotation>
					<xsd:documentation>First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FS">
				<xsd:annotation>
					<xsd:documentation>First Databank Smartkey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MC">
				<xsd:annotation>
					<xsd:documentation>Multum Drug ID</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Medi Span's Drug Descriptor ID (DDID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MG">
				<xsd:annotation>
					<xsd:documentation>Medi Span's Generic Product Identifier (GPI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MM">
				<xsd:annotation>
					<xsd:documentation>Multum MMDC</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>First DataBank Ingredient List ID (HICL_SEQNO)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>First DataBank Medication ID (FDB MedID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FN">
				<xsd:annotation>
					<xsd:documentation>First DataBank Medication Name ID (FDB Med Name ID) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FD">
				<xsd:annotation>
					<xsd:documentation>First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Generic Package (GPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SCD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Clinical Drug (SCD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BPK">
				<xsd:annotation>
					<xsd:documentation>RxNorm Branded Package (BPCK)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SBD">
				<xsd:annotation>
					<xsd:documentation>RxNorm Semantic Branded Drug (SBD)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FI">
				<xsd:annotation>
					<xsd:documentation>First DataBank Medication ID (FDB MedID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FR">
				<xsd:annotation>
					<xsd:documentation>First DataBank Routed Medication ID (FDB Routed Med ID) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GS">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Product Item Collection</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GMP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Marketed Product Identifier (MPid) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPI">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Product Identifier (ProdID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GSP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Specific Product Identifier (SPID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="US">
				<xsd:annotation>
					<xsd:documentation>U.S. Pharmacopoeia (USP)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ErrorCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ErrorCode">
		<xsd:annotation>
			<xsd:documentation>Codes used to relay successful or rejected communications.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="000">
				<xsd:annotation>
					<xsd:documentation>Transaction successful</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="001">
				<xsd:annotation>
					<xsd:documentation>Transaction successful, message(s) waiting to be retrieved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="002">
				<xsd:annotation>
					<xsd:documentation>No more messages</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="003">
				<xsd:annotation>
					<xsd:documentation>Transaction successful, no messages to be retrieved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="005">
				<xsd:annotation>
					<xsd:documentation>Transaction successful, password soon to expire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="010">
				<xsd:annotation>
					<xsd:documentation>Successful - accepted by ultimate receiver</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="600">
				<xsd:annotation>
					<xsd:documentation>Communication problem - try again later</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="601">
				<xsd:annotation>
					<xsd:documentation>Receiver unable to process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="602">
				<xsd:annotation>
					<xsd:documentation>Receiver System Error</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="900">
				<xsd:annotation>
					<xsd:documentation>Transaction rejected</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="700">
				<xsd:annotation>
					<xsd:documentation>Configuration Error</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  FinancialGuarantorRelationship  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="FinancialGuarantorRelationship">
		<xsd:annotation>
			<xsd:documentation>Alternate contact relationship to the patient. And Code indicating the relationship between the financial guarantor and the patient.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="0">
				<xsd:annotation>
					<xsd:documentation>Not Applicable</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Spouse</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Son or Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3">
				<xsd:annotation>
					<xsd:documentation>Father or Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4">
				<xsd:annotation>
					<xsd:documentation>Grandfather or Grandmother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="5">
				<xsd:annotation>
					<xsd:documentation>Grandson or Granddaughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="6">
				<xsd:annotation>
					<xsd:documentation>Uncle or Aunt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="7">
				<xsd:annotation>
					<xsd:documentation>Nephew or Niece</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="8">
				<xsd:annotation>
					<xsd:documentation>Cousin</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="9">
				<xsd:annotation>
					<xsd:documentation>Adopted Child</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="10">
				<xsd:annotation>
					<xsd:documentation>Foster Child</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="11">
				<xsd:annotation>
					<xsd:documentation>Son-in-law or Daughter-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="12">
				<xsd:annotation>
					<xsd:documentation>Brother-in-law or Sister-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="13">
				<xsd:annotation>
					<xsd:documentation>Mother-in-law or Father-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="14">
				<xsd:annotation>
					<xsd:documentation>Brother or Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="15">
				<xsd:annotation>
					<xsd:documentation>Ward</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="16">
				<xsd:annotation>
					<xsd:documentation>Stepparent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="17">
				<xsd:annotation>
					<xsd:documentation>Stepson or Stepdaughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="18">
				<xsd:annotation>
					<xsd:documentation>Self</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="19">
				<xsd:annotation>
					<xsd:documentation>Child  - Dependent between the ages of � and 19; age qualifications may vary depending on policy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="20">
				<xsd:annotation>
					<xsd:documentation>Employee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="21">
				<xsd:annotation>
					<xsd:documentation>Unknown</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="22">
				<xsd:annotation>
					<xsd:documentation>Handicapped Dependent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="23">
				<xsd:annotation>
					<xsd:documentation>Sponsored Dependent - Dependents between the ages of 19 and 25 not attending school; age qualifications may vary depending on policy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="24">
				<xsd:annotation>
					<xsd:documentation>Dependent of a Minor Dependent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="25">
				<xsd:annotation>
					<xsd:documentation>Ex-spouse</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="26">
				<xsd:annotation>
					<xsd:documentation>Guardian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="27">
				<xsd:annotation>
					<xsd:documentation>Student - Dependent between the ages of 19 and 25 attending school; age qualifications may vary depending on policy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="28">
				<xsd:annotation>
					<xsd:documentation>Friend</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="29">
				<xsd:annotation>
					<xsd:documentation>Significant Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="30">
				<xsd:annotation>
					<xsd:documentation>Both Parents - The residence or legal custody of the student is with both parents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="31">
				<xsd:annotation>
					<xsd:documentation>Court Appointed Guardian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="32">
				<xsd:annotation>
					<xsd:documentation>Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="33">
				<xsd:annotation>
					<xsd:documentation>Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="34">
				<xsd:annotation>
					<xsd:documentation>Other Adult</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="36">
				<xsd:annotation>
					<xsd:documentation>Emancipated Minor - A person who has been judged by a court of competent jurisdiction to be allowed to act in his or her own interest; no adult is legally responsible for this minor; this may be declared as a result of marriage</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="37">
				<xsd:annotation>
					<xsd:documentation>Agency Representative</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="38">
				<xsd:annotation>
					<xsd:documentation>Collateral Dependent - Relative related by blood or marriage who resides in the home and is dependent on the insured for a major portion of their support</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="39">
				<xsd:annotation>
					<xsd:documentation>Organ Donor - Individual receiving medical service in order to donate organs for a transplant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="40">
				<xsd:annotation>
					<xsd:documentation>Cadaver Donor - Deceased individual donating body to be used for research or transplants</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="41">
				<xsd:annotation>
					<xsd:documentation>Injured Plaintiff</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="43">
				<xsd:annotation>
					<xsd:documentation>Child Where Insured Has No Financial Responsibility - Child is covered by the insured but the insured is not the legal guardian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="45">
				<xsd:annotation>
					<xsd:documentation>Widow</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="46">
				<xsd:annotation>
					<xsd:documentation>Widower</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="47">
				<xsd:annotation>
					<xsd:documentation>State Fund - The state affiliated insurance organization providing coverage and or benefits to the claimant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="48">
				<xsd:annotation>
					<xsd:documentation>Stepfather</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="49">
				<xsd:annotation>
					<xsd:documentation>Stepmother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="50">
				<xsd:annotation>
					<xsd:documentation>Foster Parent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="51">
				<xsd:annotation>
					<xsd:documentation>Emergency Contact</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="52">
				<xsd:annotation>
					<xsd:documentation>Employer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="53">
				<xsd:annotation>
					<xsd:documentation>Life Partner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="55">
				<xsd:annotation>
					<xsd:documentation>Adopted Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="56">
				<xsd:annotation>
					<xsd:documentation>Adopted Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="57">
				<xsd:annotation>
					<xsd:documentation>Adoptive Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="58">
				<xsd:annotation>
					<xsd:documentation>Adoptive Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="59">
				<xsd:annotation>
					<xsd:documentation>Adoptive Parents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="60">
				<xsd:annotation>
					<xsd:documentation>Annuitant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="61">
				<xsd:annotation>
					<xsd:documentation>Aunt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="62">
				<xsd:annotation>
					<xsd:documentation>Brother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="63">
				<xsd:annotation>
					<xsd:documentation>Brother-in-law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="64">
				<xsd:annotation>
					<xsd:documentation>Business</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="65">
				<xsd:annotation>
					<xsd:documentation>Business Associate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="66">
				<xsd:annotation>
					<xsd:documentation>Business Insurance Trust</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="67">
				<xsd:annotation>
					<xsd:documentation>Business Partner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="68">
				<xsd:annotation>
					<xsd:documentation>Charity</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="70">
				<xsd:annotation>
					<xsd:documentation>Children of Marriage</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="71">
				<xsd:annotation>
					<xsd:documentation>Company</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="72">
				<xsd:annotation>
					<xsd:documentation>Corporation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="73">
				<xsd:annotation>
					<xsd:documentation>Creditor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="74">
				<xsd:annotation>
					<xsd:documentation>Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="75">
				<xsd:annotation>
					<xsd:documentation>Daughter-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="76">
				<xsd:annotation>
					<xsd:documentation>Dependent</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="78">
				<xsd:annotation>
					<xsd:documentation>Estate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="79">
				<xsd:annotation>
					<xsd:documentation>Ex-wife</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="80">
				<xsd:annotation>
					<xsd:documentation>Family Member</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="81">
				<xsd:annotation>
					<xsd:documentation>Father-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="82">
				<xsd:annotation>
					<xsd:documentation>Fiancé (Male)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="83">
				<xsd:annotation>
					<xsd:documentation>Financée (Female)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="84">
				<xsd:annotation>
					<xsd:documentation>Fiduciary</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="86">
				<xsd:annotation>
					<xsd:documentation>Foster Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="87">
				<xsd:annotation>
					<xsd:documentation>Foster Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="88">
				<xsd:annotation>
					<xsd:documentation>Foster Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="90">
				<xsd:annotation>
					<xsd:documentation>Foster Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="91">
				<xsd:annotation>
					<xsd:documentation>God Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="92">
				<xsd:annotation>
					<xsd:documentation>God Father</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="93">
				<xsd:annotation>
					<xsd:documentation>God Parents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="94">
				<xsd:annotation>
					<xsd:documentation>God Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="95">
				<xsd:annotation>
					<xsd:documentation>Grandchildren</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="96">
				<xsd:annotation>
					<xsd:documentation>Granddaughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="97">
				<xsd:annotation>
					<xsd:documentation>Grandfather</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="98">
				<xsd:annotation>
					<xsd:documentation>Grandmother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="99">
				<xsd:annotation>
					<xsd:documentation>Grandparents</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A1">
				<xsd:annotation>
					<xsd:documentation>Grandson</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A2">
				<xsd:annotation>
					<xsd:documentation>Great Aunt</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A3">
				<xsd:annotation>
					<xsd:documentation>Ex-husband</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A4">
				<xsd:annotation>
					<xsd:documentation>Half Brother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A5">
				<xsd:annotation>
					<xsd:documentation>Half Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A6">
				<xsd:annotation>
					<xsd:documentation>Husband</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A7">
				<xsd:annotation>
					<xsd:documentation>Institution</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A8">
				<xsd:annotation>
					<xsd:documentation>Mortgage Holder</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A9">
				<xsd:annotation>
					<xsd:documentation>Mother-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B1">
				<xsd:annotation>
					<xsd:documentation>Nephew</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B2">
				<xsd:annotation>
					<xsd:documentation>Niece</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B3">
				<xsd:annotation>
					<xsd:documentation>Parents-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B4">
				<xsd:annotation>
					<xsd:documentation>Partnership</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B5">
				<xsd:annotation>
					<xsd:documentation>Partner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B6">
				<xsd:annotation>
					<xsd:documentation>Personal Insurance Trust</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B7">
				<xsd:annotation>
					<xsd:documentation>Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B8">
				<xsd:annotation>
					<xsd:documentation>Sister-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B9">
				<xsd:annotation>
					<xsd:documentation>Sole Proprietorship</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C1">
				<xsd:annotation>
					<xsd:documentation>Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C2">
				<xsd:annotation>
					<xsd:documentation>Son-in-Law</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C3">
				<xsd:annotation>
					<xsd:documentation>Step Brother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C4">
				<xsd:annotation>
					<xsd:documentation>Step Children</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C5">
				<xsd:annotation>
					<xsd:documentation>Step Daughter</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C8">
				<xsd:annotation>
					<xsd:documentation>Step Sister</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C9">
				<xsd:annotation>
					<xsd:documentation>Step Son</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D1">
				<xsd:annotation>
					<xsd:documentation>Trust</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D2">
				<xsd:annotation>
					<xsd:documentation>Trustee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D3">
				<xsd:annotation>
					<xsd:documentation>Uncle</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D4">
				<xsd:annotation>
					<xsd:documentation>Wife</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D5">
				<xsd:annotation>
					<xsd:documentation>Teacher</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D6">
				<xsd:annotation>
					<xsd:documentation>School Counselor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D7">
				<xsd:annotation>
					<xsd:documentation>School Principal</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D8">
				<xsd:annotation>
					<xsd:documentation>Other School Administrator</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D9">
				<xsd:annotation>
					<xsd:documentation>Coach</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E1">
				<xsd:annotation>
					<xsd:documentation>Activity Sponsor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E2">
				<xsd:annotation>
					<xsd:documentation>Supervisor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E3">
				<xsd:annotation>
					<xsd:documentation>Co-worker</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E4">
				<xsd:annotation>
					<xsd:documentation>Minister or Priest</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E5">
				<xsd:annotation>
					<xsd:documentation>Ecclesiastical or Religious Leader</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E6">
				<xsd:annotation>
					<xsd:documentation>God Mother</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E7">
				<xsd:annotation>
					<xsd:documentation>Probation Officer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E8">
				<xsd:annotation>
					<xsd:documentation>Accountant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E9">
				<xsd:annotation>
					<xsd:documentation>Advisor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F1">
				<xsd:annotation>
					<xsd:documentation>Alma Mater</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F2">
				<xsd:annotation>
					<xsd:documentation>Applicant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F3">
				<xsd:annotation>
					<xsd:documentation>Banker</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F6">
				<xsd:annotation>
					<xsd:documentation>Clergyman</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F7">
				<xsd:annotation>
					<xsd:documentation>Client</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F8">
				<xsd:annotation>
					<xsd:documentation>Club or Organization Officer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F9">
				<xsd:annotation>
					<xsd:documentation>Doctor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G2">
				<xsd:annotation>
					<xsd:documentation>Educator/Teacher/Instructor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G3">
				<xsd:annotation>
					<xsd:documentation>Betrothed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G4">
				<xsd:annotation>
					<xsd:documentation>Insured</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G5">
				<xsd:annotation>
					<xsd:documentation>Lawyer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G6">
				<xsd:annotation>
					<xsd:documentation>Medical Care Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G7">
				<xsd:annotation>
					<xsd:documentation>Neighbor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G8">
				<xsd:annotation>
					<xsd:documentation>Other Relationship</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G9">
				<xsd:annotation>
					<xsd:documentation>Other Relative</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H1">
				<xsd:annotation>
					<xsd:documentation>Owner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H4">
				<xsd:annotation>
					<xsd:documentation>Payor</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N1">
				<xsd:annotation>
					<xsd:documentation>None</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OT">
				<xsd:annotation>
					<xsd:documentation>Non-applicable Individual Relationship Category</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZZ">
				<xsd:annotation>
					<xsd:documentation>Mutually Defined</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  FlavoringRequested  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="FlavoringRequested">
		<xsd:annotation>
			<xsd:documentation>Indicates the prescriber is requesting flavor be added to the prescribed product.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Y">
				<xsd:annotation>
					<xsd:documentation>Flavoring requested to be added to prescribed product.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  Gender  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="Gender">
		<xsd:annotation>
			<xsd:documentation>Code identifying the gender of the individual.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="M">
				<xsd:annotation>
					<xsd:documentation>Male</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F">
				<xsd:annotation>
					<xsd:documentation>Female</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="U">
				<xsd:annotation>
					<xsd:documentation>Not Specified or Unknown</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  HospiceIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="HospiceIndicator">
		<xsd:annotation>
			<xsd:documentation>Indicates patient's hospice status.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Enrolled in Hospice</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Not Enrolled in Hospice.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  InjuryRelated  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="InjuryRelated">
		<xsd:annotation>
			<xsd:documentation>Type of injury related to the prescription.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="WORK">
				<xsd:annotation>
					<xsd:documentation>The injury is work related.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AUTO">
				<xsd:annotation>
					<xsd:documentation>The injury is auto related.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OTHER">
				<xsd:annotation>
					<xsd:documentation>The injury is other property/casualty related.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  InstructionIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="InstructionIndicator">
		<xsd:annotation>
			<xsd:documentation>Indicates the action to be taken on the Instruction fields. Used for SIG.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="MEDICALENCOUNTER">
				<xsd:annotation>
					<xsd:documentation>As directed based on medical encounter.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  LTCLevelOfChangeCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="LTCLevelOfChangeCode">
		<xsd:annotation>
			<xsd:documentation>Used in Prescription Change Request transactions, to request a change to the original new prescription.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="MessageFunctionCode">
			<xsd:enumeration value="C1">
				<xsd:annotation>
					<xsd:documentation>Label change (Any changes to the Drug, form, strength, dosage, or route) – Change to an active order to the drug, form, strength, dosage, or route (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C2">
				<xsd:annotation>
					<xsd:documentation>Frequency Change (Any change to the frequency or hours of administration for the drug) - Change to the frequency or hours of administration for the medication (long term care settings).
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C3">
				<xsd:annotation>
					<xsd:documentation>Other Change (All other changes) – A change to the medication not covered by other values listed (long term care settings).
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OS">
				<xsd:annotation>
					<xsd:documentation>Pharmacy is out of stock of the medication prescribed and it cannot be obtained in a clinically appropriate timeframe.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  LowerBoundComparisonOperator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="LowerBoundComparisonOperator">
		<xsd:annotation>
			<xsd:documentation>Code that conveys the relationship between the answered value to a question and a defined lower boundary.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="GE">
				<xsd:annotation>
					<xsd:documentation>Greater Than or Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GT">
				<xsd:annotation>
					<xsd:documentation>Greater Than</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LE">
				<xsd:annotation>
					<xsd:documentation>Less Than or Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LT">
				<xsd:annotation>
					<xsd:documentation>Less Than</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MIMEType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="MIMEType">
		<xsd:annotation>
			<xsd:documentation>Defines the content nature of the AttachmentData.  It is an Internet standard defined in RFC 2045, RFC 2046, RFC 2047, RFC 4288, RFC 4289 and RFC 2049.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="application/hl7-sda+xml">
				<xsd:annotation>
					<xsd:documentation>HL7v3 SDA (xml) – SDA is a superset to CDA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="application/x-ccr">
				<xsd:annotation>
					<xsd:documentation>(not yet registered with IANA)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="application/pdf">
				<xsd:annotation>
					<xsd:documentation>PDF</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="application/jpeg">
				<xsd:annotation>
					<xsd:documentation>JPEG</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="application/tiff">
				<xsd:annotation>
					<xsd:documentation>TIFF</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="application/png">
				<xsd:annotation>
					<xsd:documentation>PNG</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MessageFunctionCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="MessageFunctionCode">
		<xsd:annotation>
			<xsd:documentation>Used in Prescription Change Request transactions, to request a change to the original new prescription.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="A">
				<xsd:annotation>
					<xsd:documentation>Admit -The patient is a new admission; demographic information included to populate basic patient information (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Admit Cancel - A previously communicated admission or outpatient registration is canceled, either because of an erroneous entry or because of a revised decision to not admit the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Change - The status of a patient has changed (long term care settings). Update Patient Information</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Cancel Suspension - Cancels the previously-communicated medication suspension—indicating that the suspend event did not actually occur as communicated.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Transfer a Patient - A patient moves from one location to another within the care setting. For example, a patient is transferred to another ward, room or bed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C1">
				<xsd:annotation>
					<xsd:documentation>Label change (Any changes to the Drug, form, strength, dosage, or route) - Change to an active order to the drug, form, strength, dosage, or route (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C2">
				<xsd:annotation>
					<xsd:documentation>Frequency Change (Any change to the frequency or hours of administration for the drug) - Change to the frequency or hours of administration for the medication (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C3">
				<xsd:annotation>
					<xsd:documentation>Other Change (All other changes) - A change to the medication not covered by other values listed (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>Discharge Cancel - A previously communicated discharge is canceled, either because of erroneous entry or because of a revised decision to not discharge, or end the visit of, the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D1">
				<xsd:annotation>
					<xsd:documentation>Discharge - Expired - The patient has been discharged due to death (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D2">
				<xsd:annotation>
					<xsd:documentation>Discharge - Return Not Anticipated - The patient has been discharged and not expected to return to site (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D3">
				<xsd:annotation>
					<xsd:documentation>Discharge - Return Anticipated - The patient has been discharged and is expected to return to site (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D4">
				<xsd:annotation>
					<xsd:documentation>Discharge Other - The patient has been discharged for an unknown reason (long term care settings).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FS">
				<xsd:annotation>
					<xsd:documentation>Fixed-Length Suspension - Administration of the specified medication has been temporarily suspended, for a pre-determined period of time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G">
				<xsd:annotation>
					<xsd:documentation>Generic Substitution - A modification of the product prescribed to a generic equivalent.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IO">
				<xsd:annotation>
					<xsd:documentation>Change inpatient to outpatient - An inpatient becomes an outpatient and is still receiving care/services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IS">
				<xsd:annotation>
					<xsd:documentation>Indefinite Suspension - Administration of the specified medication has been suspended for an undetermined time period.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LC">
				<xsd:annotation>
					<xsd:documentation>Leave of Absence Cancel - A previously communicated hospital leave of absence or therapeutic leave of absence is canceled, either because of an erroneous entry or because of a revised decision.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LG">
				<xsd:annotation>
					<xsd:documentation>Hospital Leave of Absence - An inpatient leaves the care setting for an overnight absence to a hospital.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LR">
				<xsd:annotation>
					<xsd:documentation>Return from Leave of Absence - An inpatient returns to the care setting from a hospital leave of absence or therapeutic leave of absence.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LT">
				<xsd:annotation>
					<xsd:documentation>Therapeutic Leave of Absence - An inpatient leaves the care setting for an overnight absence to visit friends or relatives or to participate in a therapeutic or rehabilitative plan of care.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OI">
				<xsd:annotation>
					<xsd:documentation>Change outpatient to inpatient - An outpatient or ER patient is being admitted as an inpatient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OS">
				<xsd:annotation>
					<xsd:documentation>Pharmacy is out of stock of the medication prescribed and it cannot be obtained in a clinically appropriate timeframe.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization Required - A request to obtain prior authorization before dispensing.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Pre-Admit - A prospective patient has been recorded prior to arrival at the care setting for an inpatient stay.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PC">
				<xsd:annotation>
					<xsd:documentation>PreAdmit Cancel - A previously communicated pre-admission is canceled, either because of an erroneous entry or because of a revised decision to not pre-admit the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RA">
				<xsd:annotation>
					<xsd:documentation>Resume Administration - Administration of the specified medication has been resumed or the resume date/time resumption has been set or changed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RC">
				<xsd:annotation>
					<xsd:documentation>Return from Leave Cancel - A previously communicated patient return from a leave of absence (hospital or therapeutic) is canceled, either because of an erroneous entry or because of a revised decision.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RO">
				<xsd:annotation>
					<xsd:documentation>Register outpatient - A patient has arrived or checked in as an outpatient, recurring outpatient, or emergency room patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="T">
				<xsd:annotation>
					<xsd:documentation>Therapeutic Interchange/Substitution - A modification of the product prescribed to a preferred product choice</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TC">
				<xsd:annotation>
					<xsd:documentation>Transfer Cancel - A previously communicated transfer is canceled, either because of an erroneous entry or because of a revised decision to not transfer the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="S">
				<xsd:annotation>
					<xsd:documentation>Script Clarification</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Drug Use Evaluation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="U">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – Resolution of the prescriber authorization conflict related to state/federal regulatory requirements is required before dispensing.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  MessageRequestSubCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="MessageRequestSubCode">
		<xsd:annotation>
			<xsd:documentation>Used in Prescription Change Request transactions, to further clarify the MessageRequestCode.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="A">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their State license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their DEA license status in prescribing state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their DEA registration by DEA class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their State Controlled Substance Registration license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their registration by State Controlled Substance Registration class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their NADEAN license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G">
				<xsd:annotation>
					<xsd:documentation>Prescriber must obtain/validate Type1 NPI.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H">
				<xsd:annotation>
					<xsd:documentation>Prescriber must enroll/re-enroll with prescription benefit plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="I">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm prescriptive authority criteria for prescribed medication is met.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="J">
				<xsd:annotation>
					<xsd:documentation>Prescriber must enroll/re-enroll in REMS. </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="K">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their assignment as patients’ lock-in prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="L">
				<xsd:annotation>
					<xsd:documentation>Prescriber must obtain/validate their supervising prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="M">
				<xsd:annotation>
					<xsd:documentation>Prescriber must confirm their Certificate to Prescribe Number status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  NoKnown  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="NoKnown">
		<xsd:annotation>
			<xsd:documentation>Indicates if the sender does not know of any specific categories of information for this patient.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Allergies">
				<xsd:annotation>
					<xsd:documentation>Allergy information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Conditions">
				<xsd:annotation>
					<xsd:documentation>Conditions information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Medical History">
				<xsd:annotation>
					<xsd:documentation>Medical history information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  OrderCaptureMethod  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="OrderCaptureMethod">
		<xsd:annotation>
			<xsd:documentation>Code conveying the method by which the order was defined by the prescriber and captured in the prescribing system.	
</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="EP">
				<xsd:annotation>
					<xsd:documentation>Entered by Prescriber - The prescribing practitioner entered the order directly into the electronic prescribing system. Includes entry from a remote location (e.g., via a web interface).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VT">
				<xsd:annotation>
					<xsd:documentation>Verbal Telephone Order - The order was received by telephone from the prescriber and entered into the prescribing system by the receiving party.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VI">
				<xsd:annotation>
					<xsd:documentation>Verbal In-Person Order - The order was received verbally from the prescriber and entered into the prescribing system by the receiving party. Order was received in-person. (Note: this value is also used for orders received by telephone if the prescribing system is unable to track telephone orders separately from other verbal orders entered by the receiving party).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WR">
				<xsd:annotation>
					<xsd:documentation>Written Order - The order was written by the prescriber and entered by another party into the electronic prescribing system. Includes faxed orders.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TV">
				<xsd:annotation>
					<xsd:documentation>Transcribed Verbal Order - The order was given verbally or by telephone to one party, but was entered into the electronic prescribing system by another party.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OM">
				<xsd:annotation>
					<xsd:documentation>Other Method - The order was captured by another method different from those implied or specified.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  OrderGroupReason  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="OrderGroupReason">
		<xsd:annotation>
			<xsd:documentation>Indicates the reason for the grouping of the product orders. The OrderGroupReason may be the same or may differ with each product within the order group.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AdditionalServices">
				<xsd:annotation>
					<xsd:documentation>Additional Services – Only used with OrderGroupReason “NewRx”. </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IVTherapy">
				<xsd:annotation>
					<xsd:documentation>IV Therapy – Used to link multiple electronic prescriptions necessary for the administration of an IV.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LoadingQuantity">
				<xsd:annotation>
					<xsd:documentation>Loading Quantity – Used when the initial quantity differs from that of the maintenance quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MaintenanceQuantity">
				<xsd:annotation>
					<xsd:documentation>Maintenance Quantity – Quantity to be dispensed for maintenance therapy after completion of a Trial Fill Quantity, Loading Quantity or Unbreakable Package Multiple Administration Locations Quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MultipleProductsPrescribed">
				<xsd:annotation>
					<xsd:documentation>Multiple Products Prescribed At Same Time – Multiple products to be dispensed as concurrent therapy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UnbreakablePkgMultipleLoc">
				<xsd:annotation>
					<xsd:documentation>Unbreakable Package Multiple Administration Locations Quantity – Quantity to be dispensed for an unbreakable package to support multiple administration locations. </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NewRx">
				<xsd:annotation>
					<xsd:documentation>NewRx – Only used with OrderGroupReason Additional Service.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SubsequentOrder">
				<xsd:annotation>
					<xsd:documentation>SubsequentOrder – Multiple CII for the same drug written at the same time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TrialFill">
				<xsd:annotation>
					<xsd:documentation>Trial Fill – Quantity to be dispensed as a trial fill.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Other">
				<xsd:annotation>
					<xsd:documentation>Other – Only to be used if no other OrderGroupReason applies. The reason for grouping must be included within the OrderGroupReason note field.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NonCommerciallyAvailableDose">
				<xsd:annotation>
					<xsd:documentation>Non-Commercially Available Dose – Non-commercially available dose requiring a prescription for each commercially available product.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  OtherMedicationDateQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="OtherMedicationDateQualifier">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="StartDate">
				<xsd:annotation>
					<xsd:documentation>The date or date/time after which this order/prescription being transmitted can be administered by a healthcare professional (i.e. do not administer before date) as authorized by the prescriber. To be sent on electronic prescriptions for patients receiving Long Term Care Pharmacy Services in leu of EffectiveDate to denote a future administration date or date/time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AnticipatedHealthCareFacilityDischargeDate">
				<xsd:annotation>
					<xsd:documentation>Indicates expected date of patient’s discharge from health care facility.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DateValidated">
				<xsd:annotation>
					<xsd:documentation>The date when material obligations were verified.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DeliveredOnDate">
				<xsd:annotation>
					<xsd:documentation>Date or date and time prescription was received at facility.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ExpirationDate">
				<xsd:annotation>
					<xsd:documentation>The final date or date and time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EffectiveDate">
				<xsd:annotation>
					<xsd:documentation>The date or date/time after which this prescription being transmitted can be dispensed (i.e. do not fill before date) as authorized by the prescriber. For receipt of prescriptions with transmission of the NewRx greater than 72 hours of the WrittenDate, the RxChange transaction can be used for clarification with the prescriber. EXCEPTION: Electronic prescriptions for patients receiving Long Term Care Pharmacy Services are exempt from the EffectiveDate usage stated above, instead use StartDate.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OtherHealthCareFacilityDischargeDate">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the patient was discharged from the health care facility.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PeriodEnd">
				<xsd:annotation>
					<xsd:documentation>The date or date and time that the referenced period expires.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SoldDate">
				<xsd:annotation>
					<xsd:documentation>The date or date and time the product was sold.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TransplantHealthCareFacilityDischargeDateMedicare">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the Medicare patient who received a transplant was discharged from the health care facility where the transplant was performed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TransplantHealthCareFacilityDischargeDateNonMedicare">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the patient who received a transplant was discharged from the health care facility where the transplant was performed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TransplantDate">
				<xsd:annotation>
					<xsd:documentation>The date or date/time the patient received a transplant.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PAPriorityIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PAPriorityIndicator">
		<xsd:annotation>
			<xsd:documentation>Element which indicates the priority of the requested prior authorization.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="S">
				<xsd:annotation>
					<xsd:documentation>Standard Priority - A request with standard time-frames is one that the physician/prescriber has deemed to not be life threatening or would otherwise not endanger the well-being of the patient if the request is not handled with immediacy. These requests are treated as secondary to urgent requests. (note: CMS guidance is 72 hours)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="X">
				<xsd:annotation>
					<xsd:documentation>Urgent / Expedited Priority- A request designation is when the treatment requested is required to prevent serious deterioration in the member’s health or could jeopardize the enrollee’s ability to regain maximum function. (note: CMS guidance is 24 hours)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PatientCodifiedNoteQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PatientCodifiedNoteQualifier">
		<xsd:annotation>
			<xsd:documentation>Codified note specific to patient and/or medication/product.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Dose(s) administered from Emergency box (kit) or automated dispensing machine.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Product has been contaminated during administration.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient requires oral solid medications to be crushed before administration.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AD">
				<xsd:annotation>
					<xsd:documentation>Patient requests/requires oral solid medications to be smallest tablet/capsule.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Medications administered via gastric tube. Used only if Route of Administration is not specified as gastric tube.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Patient requests product to be dispensed as written.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AG">
				<xsd:annotation>
					<xsd:documentation>Compound requested – allergy to inactive ingredient found in commercially available product. To be used when product is commercially available.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AH">
				<xsd:annotation>
					<xsd:documentation>Compound requested – dosage form not commercially available. To be used when product is commercially available. To be used when product is commercially available.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AJ">
				<xsd:annotation>
					<xsd:documentation>Compound requested – strength not commercially available. To be used when product is commercially available.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AK">
				<xsd:annotation>
					<xsd:documentation>Incremental Dispensing – prescriber allows pharmacy to dispense in quantities less than the prescribed quantity. To be used when the prescriber allows the pharmacy to dispense in quantities less than total prescribed quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Patient needs labs, appointment, or office visit. To be used when patient needs to schedule an office or laboratory appointment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Patient bringing drug coupon, discount, or other benefit card.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>This prescription is a mail-order, vacation, lost, stolen, or replacement supply. To be used when prescription is in response to a mail-order bridge supply; vacation supply, lost, or stolen supply; or miscellaneous replacement supply.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PatientRelationshipCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PatientRelationshipCode">
		<xsd:annotation>
			<xsd:documentation>Code indicating relationship of patient to cardholder.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Cardholder - The individual that is enrolled in and receives benefits from a health plan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Spouse - Patient is the husband/wife/partner of the cardholder</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3">
				<xsd:annotation>
					<xsd:documentation>Child - Patient is a child of the cardholder</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4">
				<xsd:annotation>
					<xsd:documentation>Other - Relationship to cardholder is not precise</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PayerResponsibilityCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PayerResponsibilityCode">
		<xsd:annotation>
			<xsd:documentation>Indicates the insurance type.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Primary</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="S">
				<xsd:annotation>
					<xsd:documentation>Secondary</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="T">
				<xsd:annotation>
					<xsd:documentation>Tertiary</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="U">
				<xsd:annotation>
					<xsd:documentation>Unknown</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PP">
				<xsd:annotation>
					<xsd:documentation>Private Pay</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PayerType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PayerType">
		<xsd:annotation>
			<xsd:documentation>Identifies the type of payer. </xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="A">
				<xsd:annotation>
					<xsd:documentation>Medicare Part A - Part of the Original Medicare Plan managed by the federal government. Covers some, but not all, of the expenses incurred for inpatient hospital care or medical care that a person may receive at a skilled nursing facility (not a custodial care facility). Some hospice care and some home health care are also covered. Limitations apply, and have deductibles, copays, or other costs to satisfy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B">
				<xsd:annotation>
					<xsd:documentation>Medicare Part B - Part of the Original Medicare Plan managed by the federal government. This covers medically necessary services from doctors or outpatient hospital care. It also helps with costs associated with some physical and occupational therapist services and some home health care services. A person typically must sign up for Part B and pay a monthly premium in order to benefit from coverage.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Medicare Part C - Part of Medicare includes medical and other benefits provided through private health benefits companies (approved by the federal government) known as Medicare Advantage Plans. Plans cover the same or better benefits as the Original Medicare Plan with easy-to-budget copay and coinsurance amounts when a person uses a network doctor and hospital.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Medicare Part D - The optional Medicare prescription drug coverage.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="E">
				<xsd:annotation>
					<xsd:documentation>Medicaid- A program, financed jointly by the federal government and the states, that provides health coverage for mostly low-income women and children as well as nursing-home care for low-income elderly.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F">
				<xsd:annotation>
					<xsd:documentation>Managed Care – Managed Medicaid or other managed plans</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="G">
				<xsd:annotation>
					<xsd:documentation>Hospice – A Hospice Organization is responsible for the patient’s treatment and prescription coverage.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="H">
				<xsd:annotation>
					<xsd:documentation>Commercial - A prescription health insurance program provided by a for-profit, private insurance agency or company.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="J">
				<xsd:annotation>
					<xsd:documentation>Private Pay – The patient (resident or financial guarantor) pays the full cost.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="K">
				<xsd:annotation>
					<xsd:documentation>Worker’s Compensation - plan providing workers compensation insurance (insurance required by law from employers for the protection of employees while engaged in the employer's business).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="L">
				<xsd:annotation>
					<xsd:documentation>Discount Program - Discount Program-a program that offer savings on prescription drugs to patients who are without health insurance, a traditional benefits plan, or have prescriptions that are not covered by insurance.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="M">
				<xsd:annotation>
					<xsd:documentation>Coupon-reimbursement based on the coupon amount determined by the processor.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N">
				<xsd:annotation>
					<xsd:documentation>Voucher- a form authorizing a disbursement of cash or a credit against a purchase or expense.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Military / VA- a government-run military veteran benefit system that administers programs of veterans’ benefits for veterans, their families, and survivors.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZY">
				<xsd:annotation>
					<xsd:documentation>Other - Any other payer types not covered by definitions above.  New codes, definitions and descriptions should be developed for anything classified as Other.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZZ">
				<xsd:annotation>
					<xsd:documentation>Unknown – payer type unknown.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PaymentType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PaymentType">
		<xsd:annotation>
			<xsd:documentation>Type of payment received for the prescription fill as recorded by a state Prescription Drug Monitoring Program (PDMP).</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Private Pay</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Medicaid</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3">
				<xsd:annotation>
					<xsd:documentation>Medicare</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4">
				<xsd:annotation>
					<xsd:documentation>Commercial Insurance</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="5">
				<xsd:annotation>
					<xsd:documentation>Military Installations and VA</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="6">
				<xsd:annotation>
					<xsd:documentation>Worker's Compensation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="7">
				<xsd:annotation>
					<xsd:documentation>Indian Nations</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="99">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PharmacyType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PharmacyType">
		<xsd:annotation>
			<xsd:documentation>Type of Pharmacy.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="R">
				<xsd:annotation>
					<xsd:documentation>Retail - A dully-licensed entity that delivers pharmaceutical goods or services for sale to or use by the final consumer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="M">
				<xsd:annotation>
					<xsd:documentation>Mail Order –A distribution center that provides medications directly to patients via U.S. Mail or other delivery services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="S">
				<xsd:annotation>
					<xsd:documentation>Specialty - A pharmacy which typically dispenses exclusively those medications which require special handling due to their storage or handling requirements.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="L">
				<xsd:annotation>
					<xsd:documentation>Long-Term Care-LTC is a community network of health and supportive services that help individuals and their caregivers manage health needs, personal needs and activities of daily living in a variety of settings on a long-term basis.  The various components in the LTC spectrum include nursing homes, skilled nursing facilities, housing with supportive services, assisted living, continuing care retirement communities, adult day care, intermediate care facilities for the mentally retarded and developmentally disabled, home health care, hospice care and respite care.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="A">
				<xsd:annotation>
					<xsd:documentation>Any - Code indicating a pharmacy without restriction or exception</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PrescriberCheckedREMS  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PrescriberCheckedREMS">
		<xsd:annotation>
			<xsd:documentation>Identifies if the prescribing system has performed an inquiry to the REMS Administrator in order to verify the REMS component of the prescription.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="A">
				<xsd:annotation>
					<xsd:documentation>Prescriber has checked REMS and the prescriber’s actions have been completed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="B">
				<xsd:annotation>
					<xsd:documentation>Prescriber has checked REMS and the prescriber’s actions are not yet completed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not checked REMS.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PrescriptionDeliveryMethod  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PrescriptionDeliveryMethod">
		<xsd:annotation>
			<xsd:documentation>The method through which the original electronically created transaction was delivered to its intended recipient. The presence of this value will confirm to the original sender the delivery method ultimately employed to successfully deliver the transaction to its intended recipient; clarity in ultimate delivery method will assist with any troubleshooting or transaction tracing that may take place.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Electronic Delivery - Prescription is delivered to its intended recipient via EDI/electronic communication methods (e.g. computer to computer - not via any faxing mechanism).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Facsimile Delivery - Prescription is delivered to its intended recipient via a FAX communication (e.g. used as a back-up method to an original electronic delivery attempt).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PrimaryDiagnosisCodeQualifierCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PrimaryDiagnosisCodeQualifierCode">
		<xsd:annotation>
			<xsd:documentation>Qualifies the code list used for the PrimaryDiagnosis.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="ABF">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-10- Clinical Modifications (ICD-10-CM) Code indicating that the following information is a diagnosis as defined by ICD-10-CM. As of January 1, 1999, the ICD-10 is used to code and classify mortality data from death certificates. The International Classification of Diseases, 10th Revision, Clinical Modification (ICD-10-CM)  is a statistical classification system that arranges diseases and injuries into groups according to established criteria. The codes are 3 to 7 digits with the first digit alpha, the second and third numeric and the remainder A/N. The codes are maintained by the World Health Organization and published by the Centers for Medicare and Medicaid Services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-9- Clinical Modifications-Diagnosis (ICD-9-CM-Diagnosis) Code indicating the diagnosis is defined according to the International Classification of Diseases, 9th Revision, Clinical Modification (ICD-9-CM) is a statistical classification system that arranges diseases and injuries into groups according to established criteria. Most codes are numeric and consist of 3, 4, or 5 numbers and a description. The codes are maintained by the World Health Organization and published by the Centers for Medicare and Medicaid Services</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LD">
				<xsd:annotation>
					<xsd:documentation>SNOMED - Systematized Nomenclature of Medicine Clinical Terms® (SNOMED CT). SNOMED CT® terminology which is available from the Health Terminology Standards Development Organisation (IHTSDO) http://www.ihtsdo.org/snomed-ct/</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  PriorAuthorizationStatus  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="PriorAuthorizationStatus">
		<xsd:annotation>
			<xsd:documentation>The status of the prescription’s prior authorization as known by the sender.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="A">
				<xsd:annotation>
					<xsd:documentation>Approved - The medication was approved by the payer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Denied - The medication was not approved by the payer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="F">
				<xsd:annotation>
					<xsd:documentation>Deferred - The medication request being reviewed by the payer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="N">
				<xsd:annotation>
					<xsd:documentation>Not Required - A prior authorization is not required for this medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="R">
				<xsd:annotation>
					<xsd:documentation>Requested - The action of obtaining a prior authorization approval is being sought.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ProblemNameCodeQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ProblemNameCodeQualifier">
		<xsd:annotation>
			<xsd:documentation>Text of problem.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="ABF">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-10- Clinical Modifications (ICD-10-CM)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-9- Clinical Modifications-Diagnosis (ICD-9-CM-Diagnosis)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LD">
				<xsd:annotation>
					<xsd:documentation>SNOMED - Systematized Nomenclature of Medicine Clinical Terms® (SNOMED CT) SNOMED CT® terminology which is available from the Health Terminology Standards Development Organisation (IHTSDO) http://www.ihtsdo.org/snomed-ct/</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ProductQualifierCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ProductQualifierCode">
		<xsd:annotation>
			<xsd:documentation>The code list defining the ProductCode.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="DI">
				<xsd:annotation>
					<xsd:documentation>Device Identifier</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>National Drug Code (NDC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RT">
				<xsd:annotation>
					<xsd:documentation>National Drug File Reference (NDF-RT)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UN">
				<xsd:annotation>
					<xsd:documentation>Unique Ingredient Identifier (UNII)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UP">
				<xsd:annotation>
					<xsd:documentation>Universal Product Code (UPC)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>Health Related Item (HRI)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GMP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Marketed Product Identifier (MPid) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GPI">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Product Identifier (ProdID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GSP">
				<xsd:annotation>
					<xsd:documentation>Gold Standard Specific Product Identifier (SPID)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ProfessionalServiceCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ProfessionalServiceCode">
		<xsd:annotation>
			<xsd:documentation>Code identifying intervention performed when a conflict has been detected.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>Self-care consultation - Code indicating activities performed by a pharmacist on behalf of a patient intended to allow the patient to function more effectively on his or her own behalf in health promotion and disease prevention, detection, or treatment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ZZ">
				<xsd:annotation>
					<xsd:documentation>Other Acknowledgment. When ZZ is used, the textual DUE Acknowledgment Reason must be used to provide additional detail.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RO">
				<xsd:annotation>
					<xsd:documentation>Pharmacist consulted other source -Code indicating communication related to collection of information or clarification of a specific limited problem.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TC">
				<xsd:annotation>
					<xsd:documentation>Payer/processor consulted - Code indicating communication by a pharmacist to a processor or payer related to the care of the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MR">
				<xsd:annotation>
					<xsd:documentation>Medication review-Code indicating comprehensive review and evaluation of a patient’s entire medication regimen.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FE">
				<xsd:annotation>
					<xsd:documentation>Formulary enforcement-Code indicating that activities including interventions with prescribers and patients related to the enforcement of a pharmacy benefit plan formulary have occurred. Comment: Use this code for cross-licensed brand products or generic to brand interchange.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CC">
				<xsd:annotation>
					<xsd:documentation>Coordination of care - Case management activities of a pharmacist related to the care being delivered by multiple providers.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PM">
				<xsd:annotation>
					<xsd:documentation>Patient monitoring - Code indicating the evaluation of established therapy for the purpose of determining whether an existing therapeutic plan should be altered.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PE">
				<xsd:annotation>
					<xsd:documentation>Patient education/instruction - Code indicating verbal and/or written communication by a pharmacist to enhance the patient’s knowledge about the condition under treatment or to develop skills and competencies related to its management.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MO">
				<xsd:annotation>
					<xsd:documentation>Prescriber consulted - Code indicating prescriber communication related to collection of information or clarification of a specific limited problem.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TH">
				<xsd:annotation>
					<xsd:documentation>Therapeutic product interchange - Code indicating that the selection of a therapeutically equivalent product to that specified by the prescriber for the purpose of achieving cost savings for the payer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Previous patient tolerance - Patient has taken medication previously without issue.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RT">
				<xsd:annotation>
					<xsd:documentation>Recommend laboratory test - Code indicating that the pharmacist recommends the performance of a clinical laboratory test on a patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>Patient assessment - Code indicating that an initial evaluation of a patient or complaint/symptom for the purpose of developing a therapeutic plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Dosing evaluation/determination - Cognitive service whereby the pharmacist reviews and evaluates the appropriateness of a prescribed medication’s dose, interval, frequency and/or formulation.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="P0">
				<xsd:annotation>
					<xsd:documentation>Patient consulted - Code indicating patient communication related to collection of information or clarification of a specific limited problem.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MA">
				<xsd:annotation>
					<xsd:documentation>Medication administration Code indicating an action of supplying a medication to a patient through any of several routes—oral, topical, intravenous, intramuscular, intranasal, etc.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SW">
				<xsd:annotation>
					<xsd:documentation>Literature search/review - Code indicating that the pharmacist searches or reviews the pharmaceutical and/or medical literature for information related to the care of a patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MB">
				<xsd:annotation>
					<xsd:documentation>Overriding benefit - Benefits of the prescribed medication outweigh the risks.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="00">
				<xsd:annotation>
					<xsd:documentation>No intervention</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PT">
				<xsd:annotation>
					<xsd:documentation>Perform laboratory test - Code indicating that the pharmacist performed a clinical laboratory test on a patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GP">
				<xsd:annotation>
					<xsd:documentation>Generic product selection-The selection of a chemically and therapeutically identical product to that specified by the prescriber for the purpose of achieving cost savings for the payer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PH">
				<xsd:annotation>
					<xsd:documentation>Patient medication history - Code indicating the establishment of a medication history database on a patient to serve as the foundation for the ongoing maintenance of a medication profile.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DP">
				<xsd:annotation>
					<xsd:documentation>Dosage evaluated - Code indicating that dosage has been evaluated with respect to risk for the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MP">
				<xsd:annotation>
					<xsd:documentation>Patient will be monitored - Prescriber is aware of the risk and will be monitoring the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ProphylacticOrEpisodic  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ProphylacticOrEpisodic">
		<xsd:annotation>
			<xsd:documentation>Indicates if treatment is for Prophylactic and/or episodic needs.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="PROPHYLACTIC">
				<xsd:annotation>
					<xsd:documentation>Indicates this is for prophylactic needs.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EPISODIC">
				<xsd:annotation>
					<xsd:documentation>Indicates this is for episodic needs.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BOTH">
				<xsd:annotation>
					<xsd:documentation>Indicates this is for both prophylactic and episodic.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  QuantityCodeListQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="QuantityCodeListQualifier">
		<xsd:annotation>
			<xsd:documentation>Qualifies QuantityValue.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="38">
				<xsd:annotation>
					<xsd:documentation>Original Quantity</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="40">
				<xsd:annotation>
					<xsd:documentation>Remaining Quantity</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="87">
				<xsd:annotation>
					<xsd:documentation>Quantity Received</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="QS">
				<xsd:annotation>
					<xsd:documentation>Quantity sufficient as determined by the dispensing pharmacy. Quantity to be based on established dispensing protocols between the prescriber and pharmacy/pharmacist.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CF">
				<xsd:annotation>
					<xsd:documentation>Compound Final Quantity</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UQ">
				<xsd:annotation>
					<xsd:documentation>Central Fill Unit of Use Quantity</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="QT">
				<xsd:annotation>
					<xsd:documentation>Quantity Transferred</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  REMSPatientRiskCategory  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="REMSPatientRiskCategory">
		<xsd:annotation>
			<xsd:documentation>Identifies the risk of adverse event based on variables of the patient.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AFRP">
				<xsd:annotation>
					<xsd:documentation>Adult female of reproductive potential</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AFNRP">
				<xsd:annotation>
					<xsd:documentation>Adult female not of reproductive potential</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Adult male</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MC">
				<xsd:annotation>
					<xsd:documentation>Male child</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CFRP">
				<xsd:annotation>
					<xsd:documentation>Child female of reproductive potential</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CFNRP">
				<xsd:annotation>
					<xsd:documentation>Child female not of reproductive potential.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AD">
				<xsd:annotation>
					<xsd:documentation>Patient has requested refill too soon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Medication never prescribed for the patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Patient should contact Provider first</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AG">
				<xsd:annotation>
					<xsd:documentation>Fill/Refill not appropriate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Change not appropriate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Patient needs appointment</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>Prescriber not associated with this practice or location.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AO">
				<xsd:annotation>
					<xsd:documentation>No attempt will be made to obtain Prior Authorization</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AQ">
				<xsd:annotation>
					<xsd:documentation>More Medication History Available</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Unable to cancel prescription; prescription was transferred to another pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>Qualified provider unavailable to provide this service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AT">
				<xsd:annotation>
					<xsd:documentation>Not accepting new patients</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AU">
				<xsd:annotation>
					<xsd:documentation>Unable to accommodate service based parameters</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AV">
				<xsd:annotation>
					<xsd:documentation>These parameters do not meet the patient's needs</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AW">
				<xsd:annotation>
					<xsd:documentation>Based on assessment, patient needs are outside of contractual agreement</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AX">
				<xsd:annotation>
					<xsd:documentation>Patient condition no longer applicable</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AY">
				<xsd:annotation>
					<xsd:documentation>Patient not available for service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZ">
				<xsd:annotation>
					<xsd:documentation>Patient declined service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BA">
				<xsd:annotation>
					<xsd:documentation>Qualified provider unavailable to provide this service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BB">
				<xsd:annotation>
					<xsd:documentation>No Information Available</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>Not Authorized to Release</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BD">
				<xsd:annotation>
					<xsd:documentation>Unable to Send Response in Format Requested</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BE">
				<xsd:annotation>
					<xsd:documentation>Medication denied at patient request</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BF">
				<xsd:annotation>
					<xsd:documentation>Conscientious objection</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BG">
				<xsd:annotation>
					<xsd:documentation>Not the patient-desired pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BH">
				<xsd:annotation>
					<xsd:documentation>Not In Central Fill inventory List</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BJ">
				<xsd:annotation>
					<xsd:documentation>Out of Stock – The dispensing entity does not have sufficient quantity of the requested product to fulfill the order/prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BK">
				<xsd:annotation>
					<xsd:documentation>Quantity to Dispense Incorrect for NDC Sent – NDC requested is for an item that must be distributed in the manufacturer’s packaging and the requested quantity is not an even multiple of the manufacturer’s packaging.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BL">
				<xsd:annotation>
					<xsd:documentation>Rx Received After Established Deadline – The order was not received in time for the requested shipment date.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BM">
				<xsd:annotation>
					<xsd:documentation>Duplicate Order – More than one message for an order was received.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BN">
				<xsd:annotation>
					<xsd:documentation>Fill Locally not filled by Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BO">
				<xsd:annotation>
					<xsd:documentation>NDC Discontinued from Formulary – Do not resend.  The NDC number requested in on the discontinued inventory list and there is not sufficient quantity of the medication requested to fulfill the order.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BP">
				<xsd:annotation>
					<xsd:documentation>Out of Stock/Manufacturer Back Order – NDC requested is currently on back order from the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BQ">
				<xsd:annotation>
					<xsd:documentation>Discontinued by Manufacturer – NDC requested has been discontinued by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BR">
				<xsd:annotation>
					<xsd:documentation>Rx Canceled by Central Fill Facility: Automation – Due to automation issues at the Central Fill Facility the Rx must be filled locally.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BS">
				<xsd:annotation>
					<xsd:documentation>Rx Canceled by Central Fill Facility: Site Closure – Due to site closure issues at Central Fill Facility the Rx must be filled locally.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BT">
				<xsd:annotation>
					<xsd:documentation>Drug Recalled by Manufacturer – NDC requested has been recalled by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BU">
				<xsd:annotation>
					<xsd:documentation>Eligible for Fulfillment from Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BV">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not enrolled/aligned with Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BW">
				<xsd:annotation>
					<xsd:documentation>Change to a different medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BX">
				<xsd:annotation>
					<xsd:documentation>Electronic Prior Authorization not supported. Submit via other methods.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BZ">
				<xsd:annotation>
					<xsd:documentation>Can’t find PACase ID</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>Unable to locate based on insufficient information - identifiers do not match</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CC">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization not required for patient/medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CB">
				<xsd:annotation>
					<xsd:documentation>Request already processed - final determination has been made</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Cannot find matching patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CE">
				<xsd:annotation>
					<xsd:documentation>Patient not eligible (does not have coverage with the payer) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CF">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization duplicate/approved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CG">
				<xsd:annotation>
					<xsd:documentation> Prior Authorization duplicate/in process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CH">
				<xsd:annotation>
					<xsd:documentation>Closed by health plan/payer/processor/PBM</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CJ">
				<xsd:annotation>
					<xsd:documentation>Closed by Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CK">
				<xsd:annotation>
					<xsd:documentation>Closed by Member</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CL">
				<xsd:annotation>
					<xsd:documentation>Attachment type (mimetype) not supported</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CM">
				<xsd:annotation>
					<xsd:documentation>Prescriber not allowed to submit PA request</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CN">
				<xsd:annotation>
					<xsd:documentation>Response content is inconsistent with the question</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the PA processor for this patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CP">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the PA processor for this patient and medication combination</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CQ">
				<xsd:annotation>
					<xsd:documentation>Transfer needs to be discussed - call with information provided</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CR">
				<xsd:annotation>
					<xsd:documentation>Prescription(s) cannot be electronically transferred; will be sent via manual transfer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Prescription not found</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out because it was previously transferred</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CU">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out because it is voided/cancelled/deactivated</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CV">
				<xsd:annotation>
					<xsd:documentation>Stop date has been exceeded</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CW">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out because it has expired</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CX">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out by law/regulation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CY">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization duplicate/denied</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CZ">
				<xsd:annotation>
					<xsd:documentation>Patient had allergy to requested medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DA">
				<xsd:annotation>
					<xsd:documentation>Medication has been discontinued</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DB">
				<xsd:annotation>
					<xsd:documentation>Drug Use Evaluation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>No refills remaining or medication order has expired</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DD">
				<xsd:annotation>
					<xsd:documentation>Pharmacy has no intention to stock the medication requested/prescribed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization has been denied by payer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DF">
				<xsd:annotation>
					<xsd:documentation>Generic Substitution – A modification of the product prescribed to a generic equivalent.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DG">
				<xsd:annotation>
					<xsd:documentation>Therapeutic Interchange/Substitution – A modification of the product prescribed to a preferred product choice.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DH">
				<xsd:annotation>
					<xsd:documentation>Profile Medication – medication appropriate for administration, not dispensed by pharmacy at this time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DJ">
				<xsd:annotation>
					<xsd:documentation>No Data – There is a response indicating that no data was found for the search criteria provided in the search request.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DK">
				<xsd:annotation>
					<xsd:documentation>Prescription Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DL">
				<xsd:annotation>
					<xsd:documentation>Disallowed – There was a permission problem of some type performing this request against this destination.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DM">
				<xsd:annotation>
					<xsd:documentation>Error- This response indicates a processing error in performing this request. (May only be used when a more specific ReasonCode does not apply).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DN">
				<xsd:annotation>
					<xsd:documentation>Excessive days supply according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DO">
				<xsd:annotation>
					<xsd:documentation>Insufficient days supply according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DP">
				<xsd:annotation>
					<xsd:documentation>Inappropriate days supply for the quantity prescribed according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DQ">
				<xsd:annotation>
					<xsd:documentation>Excessive dosage according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DR">
				<xsd:annotation>
					<xsd:documentation>Insufficient dosage according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DS">
				<xsd:annotation>
					<xsd:documentation>Refills not permitted according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DT">
				<xsd:annotation>
					<xsd:documentation>Quantity limit exceeds maximum according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DU">
				<xsd:annotation>
					<xsd:documentation>Inappropriate quantity prescribed according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DV">
				<xsd:annotation>
					<xsd:documentation>Laboratory test results not documented</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DW">
				<xsd:annotation>
					<xsd:documentation>Laboratory test not conducted within specified time period.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>Prescribing not authorized due to laboratory test results.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DY">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not documented safe use conditions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DZ">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not documented patient opioid tolerance.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EA">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not documented that patient has met contraceptive requirements.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EB">
				<xsd:annotation>
					<xsd:documentation>Invalid prescription may not be electronically submitted; hand written orders only.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EC">
				<xsd:annotation>
					<xsd:documentation>Non matched Diagnosis code submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ED">
				<xsd:annotation>
					<xsd:documentation>Patient First Name must be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EE">
				<xsd:annotation>
					<xsd:documentation>Patient Last Name must  be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EF">
				<xsd:annotation>
					<xsd:documentation>Patient Zip Code must be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EG">
				<xsd:annotation>
					<xsd:documentation>Multiple patient matches; call REMS Administrator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EH">
				<xsd:annotation>
					<xsd:documentation>Patient First Name must be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EI">
				<xsd:annotation>
					<xsd:documentation>Patient is younger than the minimum age required.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EJ">
				<xsd:annotation>
					<xsd:documentation>Patient is older than the maximum age allowed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EK">
				<xsd:annotation>
					<xsd:documentation>Patient is on a "Do Not Rechallenge List".</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EL">
				<xsd:annotation>
					<xsd:documentation>Patient has not documented safe use conditions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EM">
				<xsd:annotation>
					<xsd:documentation>Patient must enroll/certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EN">
				<xsd:annotation>
					<xsd:documentation>Patient must re-enroll/re-certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EO">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not enrolled/certified.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EP">
				<xsd:annotation>
					<xsd:documentation>Pharmacy must re-enroll/re-certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EQ">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not matched.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ER">
				<xsd:annotation>
					<xsd:documentation>Invalid Pharmacy type - contact program administrator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ES">
				<xsd:annotation>
					<xsd:documentation>Presciber must enroll/certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ET">
				<xsd:annotation>
					<xsd:documentation>Presciber must re-enroll/re-certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EU">
				<xsd:annotation>
					<xsd:documentation>Prescriber qualifications for REMS not met.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EV">
				<xsd:annotation>
					<xsd:documentation>Missing/Invalid Prescriber ID.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EW">
				<xsd:annotation>
					<xsd:documentation>Non-Matched Prescriber last name.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EX">
				<xsd:annotation>
					<xsd:documentation>Prescriber has been deactivated.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EY">
				<xsd:annotation>
					<xsd:documentation>Pharmacy is not participating.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EZ">
				<xsd:annotation>
					<xsd:documentation>Prescriber-Patient Agreement not found.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FA">
				<xsd:annotation>
					<xsd:documentation>No attempt will be made to obtain REMS approval.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FB">
				<xsd:annotation>
					<xsd:documentation>Electronic REMS not supported. Submit via other methods.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FC">
				<xsd:annotation>
					<xsd:documentation>Can not find REMSCaseID</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FD">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization not required for patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FE">
				<xsd:annotation>
					<xsd:documentation>Patient not eligible</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FF">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization duplicate/approved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FG">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization duplicate/in process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FH">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization duplicate/denied</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FI">
				<xsd:annotation>
					<xsd:documentation>Closed by REMS Administrator</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FJ">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the REMS Administrator for this patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FK">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the REMS Administrator for this patient and medication combination</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Provider responded after deadline to reply.  PA must be reinitiated</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Product not covered by this plan. Prior Authorization not available. (Used when the drug will not be covered even if the provider does a PA)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FN">
				<xsd:annotation>
					<xsd:documentation>Prescription within prescribing limits. Prior Authorization not required for PATIENT/MEDICATION. (Used when there are coverage rules in place, but the prescriber is not going outside of them)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FO">
				<xsd:annotation>
					<xsd:documentation>Coverage limits may exist for quantity and/or days supply. Plan will pay up to coverage limit. Prior Authorization not required for PATIENT/MEDICATION. (Used when the plan will only pay up to a certain amount, but not above.)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FP">
				<xsd:annotation>
					<xsd:documentation>Coverage limits have been exceeded exception not available. Prior Authorization not required for PATIENT/MEDICATION when within coverage limit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FQ">
				<xsd:annotation>
					<xsd:documentation>Active PA on file.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FR">
				<xsd:annotation>
					<xsd:documentation>Submitted Product Code is not valid.  Please resolve and resubmit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FS">
				<xsd:annotation>
					<xsd:documentation>The medication prescribed is out of stock and cannot be obtained in a clinically appropriate timeframe.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FT">
				<xsd:annotation>
					<xsd:documentation>The medication presscribed is available for administration from the Automated Dispensing System</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FV">
				<xsd:annotation>
					<xsd:documentation>The medication prescribed is available for cycle fill</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FW">
				<xsd:annotation>
					<xsd:documentation>Patient requested reduced quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FX">
				<xsd:annotation>
					<xsd:documentation>Prescriber requested/allowed reduced quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FY">
				<xsd:annotation>
					<xsd:documentation>Days’ Supply and/or Quantity are limited by Payer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FZ">
				<xsd:annotation>
					<xsd:documentation>Regulatory Days Supply Limitation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their state license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GB">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their DEA license status in prescribing state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GC">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their DEA registration by DEA class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GD">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their state Controlled Substance Registration license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GE">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their registration by state Controlled Substance Registration class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GF">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their NADEAN license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GG">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must obtain/validate Type1 NPI.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GH">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must enroll/re-enroll with prescription benefit plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GI">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm prescriptive authority criteria for prescribed medication is met.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GJ">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must enroll/re-enroll in REMS.  </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GK">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GL">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must obtain/validate their supervising prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GM">
				<xsd:annotation>
					<xsd:documentation>Active Registration Status</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GN">
				<xsd:annotation>
					<xsd:documentation>In-Active License with prescriptive authority based on state/federal regulations</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GP">
				<xsd:annotation>
					<xsd:documentation>Active with Prescriptive Authority – prescribed product class</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GQ">
				<xsd:annotation>
					<xsd:documentation>Active with Prescriptive Authority – Prescriber Type</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GR">
				<xsd:annotation>
					<xsd:documentation>Active with Prescriptive Authority – Supervising Prescriber Type</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GS">
				<xsd:annotation>
					<xsd:documentation>Registered</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GT">
				<xsd:annotation>
					<xsd:documentation>Enrolled/Re-Enrolled</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GU">
				<xsd:annotation>
					<xsd:documentation>Assigned</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GV">
				<xsd:annotation>
					<xsd:documentation>RequestorRole not authorized to receive PDMP data.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode1  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode1">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxRenewalResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AD">
				<xsd:annotation>
					<xsd:documentation>Patient has requested refill too soon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Medication never prescribed for the patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Patient should contact Provider first</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AG">
				<xsd:annotation>
					<xsd:documentation>Fill/Refill not appropriate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Patient needs appointment</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>Prescriber not associated with this practice or location</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BE">
				<xsd:annotation>
					<xsd:documentation>Medication denied at patient request</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CZ">
				<xsd:annotation>
					<xsd:documentation>Patient had allergy to requested medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DA">
				<xsd:annotation>
					<xsd:documentation>Medication has been discontinued</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode10  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode10">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: PAInitiationResponse-Closed</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BX">
				<xsd:annotation>
					<xsd:documentation>Electronic Prior Authorization not supported. Submit via other methods</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CC">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization not required for patient/medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Cannot find matching patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CE">
				<xsd:annotation>
					<xsd:documentation>Patient not eligible (does not have coverage with the payer) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CF">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization duplicate/approved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CG">
				<xsd:annotation>
					<xsd:documentation> Prior Authorization duplicate/in process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CP">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the PA processor for this patient and medication combination</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CY">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization duplicate/denied</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Provider responded after deadline to reply.  PA must be reinitiated</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Product not covered by this plan. Prior Authorization not available. (Used when the drug will not be covered even if the provider does a PA)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FN">
				<xsd:annotation>
					<xsd:documentation>Prescription within prescribing limits. Prior Authorization not required for PATIENT/MEDICATION. (Used when there are coverage rules in place, but the prescriber is not going outside of them)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FO">
				<xsd:annotation>
					<xsd:documentation>Coverage limits may exist for quantity and/or days supply. Plan will pay up to coverage limit. Prior Authorization not required for PATIENT/MEDICATION. (Used when the plan will only pay up to a certain amount, but not above.)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FP">
				<xsd:annotation>
					<xsd:documentation>Coverage limits have been exceeded exception not available. Prior Authorization not required for PATIENT/MEDICATION when within coverage limit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FQ">
				<xsd:annotation>
					<xsd:documentation>Active PA on file.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FR">
				<xsd:annotation>
					<xsd:documentation>Submitted Product Code is not valid.  Please resolve and resubmit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode11  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode11">
		<xsd:annotation>
			<xsd:documentation>Used by Transactions-Response Status: PAAppealResponse-Closed and PAResponse-Closed</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BX">
				<xsd:annotation>
					<xsd:documentation>Electronic Prior Authorization not supported. Submit via other methods</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CC">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization not required for patient/medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Cannot find matching patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CE">
				<xsd:annotation>
					<xsd:documentation>Patient not eligible (does not have coverage with the payer) </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CF">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization duplicate/approved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CG">
				<xsd:annotation>
					<xsd:documentation> Prior Authorization duplicate/in process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CP">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the PA processor for this patient and medication combination</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CH">
				<xsd:annotation>
					<xsd:documentation>Closed by health plan/payer/processor/PBM</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CJ">
				<xsd:annotation>
					<xsd:documentation>Closed by Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CK">
				<xsd:annotation>
					<xsd:documentation>Closed by Member</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CL">
				<xsd:annotation>
					<xsd:documentation>Attachment type (mimetype) not supported</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CM">
				<xsd:annotation>
					<xsd:documentation>Prescriber not allowed to submit PA request</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CN">
				<xsd:annotation>
					<xsd:documentation>Response content is inconsistent with the question</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the PA processor for this patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CY">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization duplicate/denied</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Provider responded after deadline to reply.  PA must be reinitiated</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Product not covered by this plan. Prior Authorization not available. (Used when the drug will not be covered even if the provider does a PA)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FN">
				<xsd:annotation>
					<xsd:documentation>Prescription within prescribing limits. Prior Authorization not required for PATIENT/MEDICATION. (Used when there are coverage rules in place, but the prescriber is not going outside of them)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FO">
				<xsd:annotation>
					<xsd:documentation>Coverage limits may exist for quantity and/or days supply. Plan will pay up to coverage limit. Prior Authorization not required for PATIENT/MEDICATION. (Used when the plan will only pay up to a certain amount, but not above.)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FP">
				<xsd:annotation>
					<xsd:documentation>Coverage limits have been exceeded exception not available. Prior Authorization not required for PATIENT/MEDICATION when within coverage limit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FQ">
				<xsd:annotation>
					<xsd:documentation>Active PA on file.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FR">
				<xsd:annotation>
					<xsd:documentation>Submitted Product Code is not valid.  Please resolve and resubmit.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode12  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode12">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: PACancelResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BZ">
				<xsd:annotation>
					<xsd:documentation>Can’t find PACase ID</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>Unable to locate based on insufficient information - identifiers do not match</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CB">
				<xsd:annotation>
					<xsd:documentation>Request already processed - final determination has been made</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode13  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode13">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions: PACancelRequest</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BW">
				<xsd:annotation>
					<xsd:documentation>Change to a different medication</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode14  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode14">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: CFProductInquiryResponse-Approved</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BU">
				<xsd:annotation>
					<xsd:documentation>Eligible for Fulfillment from Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode15  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode15">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: CFProductInquiryResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BH">
				<xsd:annotation>
					<xsd:documentation>Not In Central Fill inventory List</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BJ">
				<xsd:annotation>
					<xsd:documentation>Out of Stock – The dispensing entity does not have sufficient quantity of the requested product to fulfill the order/prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BK">
				<xsd:annotation>
					<xsd:documentation>Quantity to Dispense Incorrect for NDC Sent – NDC requested is for an item that must be distributed in the manufacturer’s packaging and the requested quantity is not an even multiple of the manufacturer’s packaging.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BM">
				<xsd:annotation>
					<xsd:documentation>Duplicate Order – More than one message for an order was received.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BO">
				<xsd:annotation>
					<xsd:documentation>NDC Discontinued from Formulary – Do not resend.  The NDC number requested in on the discontinued inventory list and there is not sufficient quantity of the medication requested to fulfill the order.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BP">
				<xsd:annotation>
					<xsd:documentation>Out of Stock/Manufacturer Back Order – NDC requested is currently on back order from the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BQ">
				<xsd:annotation>
					<xsd:documentation>Discontinued by Manufacturer – NDC requested has been discontinued by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BT">
				<xsd:annotation>
					<xsd:documentation>Drug Recalled by Manufacturer – NDC requested has been recalled by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BV">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not enrolled/aligned with Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode16  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode16">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: CFRxOrderCancel-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BJ">
				<xsd:annotation>
					<xsd:documentation>Out of Stock – The dispensing entity does not have sufficient quantity of the requested product to fulfill the order/prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BL">
				<xsd:annotation>
					<xsd:documentation>Rx Received After Established Deadline – The order was not received in time for the requested shipment date.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BM">
				<xsd:annotation>
					<xsd:documentation>Duplicate Order – More than one message for an order was received.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BN">
				<xsd:annotation>
					<xsd:documentation>Fill Locally not filled by Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BO">
				<xsd:annotation>
					<xsd:documentation>NDC Discontinued from Formulary – Do not resend.  The NDC number requested in on the discontinued inventory list and there is not sufficient quantity of the medication requested to fulfill the order.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BP">
				<xsd:annotation>
					<xsd:documentation>Out of Stock/Manufacturer Back Order – NDC requested is currently on back order from the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BQ">
				<xsd:annotation>
					<xsd:documentation>Discontinued by Manufacturer – NDC requested has been discontinued by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BR">
				<xsd:annotation>
					<xsd:documentation>Rx Canceled by Central Fill Facility: Automation – Due to automation issues at the Central Fill Facility the Rx must be filled locally.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BS">
				<xsd:annotation>
					<xsd:documentation>Rx Canceled by Central Fill Facility: Site Closure – Due to site closure issues at Central Fill Facility the Rx must be filled locally.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BT">
				<xsd:annotation>
					<xsd:documentation>Drug Recalled by Manufacturer – NDC requested has been recalled by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BV">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not enrolled/aligned with Central Fill Facility</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode17  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode17">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxTransferResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Patient should contact Provider first</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>Not Authorized to Release</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>Unable to locate based on insufficient information - identifiers do not match</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Cannot find matching patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CQ">
				<xsd:annotation>
					<xsd:documentation>Transfer needs to be discussed - call with information provided</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CR">
				<xsd:annotation>
					<xsd:documentation>Prescription(s) cannot be electronically transferred; will be sent via manual transfer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Prescription not found</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out because it was previously transferred</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CU">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out because it is voided/cancelled/deactivated</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CV">
				<xsd:annotation>
					<xsd:documentation>Stop date has been exceeded</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CW">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out because it has expired</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CX">
				<xsd:annotation>
					<xsd:documentation>Prescription cannot be transferred out by law/regulation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode18  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode18">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxFill-Dispensed</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="DF">
				<xsd:annotation>
					<xsd:documentation>Generic Substitution – A modification of the product prescribed to a generic equivalent.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DG">
				<xsd:annotation>
					<xsd:documentation>Therapeutic Interchange/Substitution – A modification of the product prescribed to a preferred product choice.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DH">
				<xsd:annotation>
					<xsd:documentation>Profile Medication – medication appropriate for administration, not dispensed by pharmacy at this time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FT">
				<xsd:annotation>
					<xsd:documentation>The medication presscribed is available for administration from the Automated Dispensing System </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FV">
				<xsd:annotation>
					<xsd:documentation>The medication prescribed is available for cycle fill</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their state license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GB">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their DEA license status in prescribing state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GC">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their DEA registration by DEA class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GD">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their state Controlled Substance Registration license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GE">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their registration by state Controlled Substance Registration class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GF">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their NADEAN license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GG">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must obtain/validate Type1 NPI.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GH">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must enroll/re-enroll with prescription benefit plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GI">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm prescriptive authority criteria for prescribed medication is met.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GJ">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must enroll/re-enroll in REMS.  </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GK">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GL">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must obtain/validate their supervising prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode19  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode19">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions: PDMPStatesRequested</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="DJ">
				<xsd:annotation>
					<xsd:documentation>No Data – There is a response indicating that no data was found for the search criteria provided in the search request.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DK">
				<xsd:annotation>
					<xsd:documentation>Prescription Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DL">
				<xsd:annotation>
					<xsd:documentation>Disallowed – There was a permission problem of some type performing this request against this destination.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DM">
				<xsd:annotation>
					<xsd:documentation>Error- This response indicates a processing error in performing this request. (May only be used when a more specific ReasonCode does not apply).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode2  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode2">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxChangeResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Medication never prescribed for the patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Patient should contact Provider first</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Change not appropriate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Patient needs appointment</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>Prescriber not associated with this practice or location</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AO">
				<xsd:annotation>
					<xsd:documentation>No attempt will be made to obtain Prior Authorization</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BE">
				<xsd:annotation>
					<xsd:documentation>Medication denied at patient request</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode20  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode20">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: REMSResponse/REMSInitiationResponse-Closed</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BX">
				<xsd:annotation>
					<xsd:documentation>Electronic Prior Authorization not supported. Submit via other methods.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Cannot find matching patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CJ">
				<xsd:annotation>
					<xsd:documentation>Closed by Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CK">
				<xsd:annotation>
					<xsd:documentation>Closed by Member</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CL">
				<xsd:annotation>
					<xsd:documentation>Attachment type (mimetype) not supported</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CN">
				<xsd:annotation>
					<xsd:documentation>Response content is inconsistent with the question</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DN">
				<xsd:annotation>
					<xsd:documentation>Excessive days supply according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DO">
				<xsd:annotation>
					<xsd:documentation>Insufficient days supply according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DP">
				<xsd:annotation>
					<xsd:documentation>Inappropriate days supply for the quantity prescribed according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DQ">
				<xsd:annotation>
					<xsd:documentation>Excessive dosage according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DR">
				<xsd:annotation>
					<xsd:documentation>Insufficient dosage according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DS">
				<xsd:annotation>
					<xsd:documentation>Refills not permitted according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DT">
				<xsd:annotation>
					<xsd:documentation>Quantity limit exceeds maximum according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DU">
				<xsd:annotation>
					<xsd:documentation>Inappropriate quantity prescribed according to REMS restrictions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DV">
				<xsd:annotation>
					<xsd:documentation>Laboratory test results not documented</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DW">
				<xsd:annotation>
					<xsd:documentation>Laboratory test not conducted within specified time period.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>Prescribing not authorized due to laboratory test results.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DY">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not documented safe use conditions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DZ">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not documented patient opioid tolerance.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EA">
				<xsd:annotation>
					<xsd:documentation>Prescriber has not documented that patient has met contraceptive requirements.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EB">
				<xsd:annotation>
					<xsd:documentation>Invalid prescription may not be electronically submitted; hand written orders only.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EC">
				<xsd:annotation>
					<xsd:documentation>Non matched Diagnosis code submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ED">
				<xsd:annotation>
					<xsd:documentation>Patient First Name must be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EE">
				<xsd:annotation>
					<xsd:documentation>Patient Last Name must  be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EF">
				<xsd:annotation>
					<xsd:documentation>Patient Zip Code must be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EG">
				<xsd:annotation>
					<xsd:documentation>Multiple patient matches; call REMS Administrator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EH">
				<xsd:annotation>
					<xsd:documentation>Patient First Name must be submitted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EI">
				<xsd:annotation>
					<xsd:documentation>Patient is younger than the minimum age required.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EJ">
				<xsd:annotation>
					<xsd:documentation>Patient is older than the maximum age allowed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EK">
				<xsd:annotation>
					<xsd:documentation>Patient is on a "Do Not Rechallenge List".</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EL">
				<xsd:annotation>
					<xsd:documentation>Patient has not documented safe use conditions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EM">
				<xsd:annotation>
					<xsd:documentation>Patient must enroll/certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EN">
				<xsd:annotation>
					<xsd:documentation>Patient must re-enroll/re-certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EO">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not enrolled/certified.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EP">
				<xsd:annotation>
					<xsd:documentation>Pharmacy must re-enroll/re-certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EQ">
				<xsd:annotation>
					<xsd:documentation>Pharmacy not matched.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ER">
				<xsd:annotation>
					<xsd:documentation>Invalid Pharmacy type - contact program administrator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ES">
				<xsd:annotation>
					<xsd:documentation>Presciber must enroll/certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ET">
				<xsd:annotation>
					<xsd:documentation>Presciber must re-enroll/re-certify.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EU">
				<xsd:annotation>
					<xsd:documentation>Prescriber qualifications for REMS not met.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EV">
				<xsd:annotation>
					<xsd:documentation>Missing/Invalid Prescriber ID.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EW">
				<xsd:annotation>
					<xsd:documentation>Non-Matched Prescriber last name.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EX">
				<xsd:annotation>
					<xsd:documentation>Prescriber has been deactivated.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EY">
				<xsd:annotation>
					<xsd:documentation>Pharmacy is not participating.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EZ">
				<xsd:annotation>
					<xsd:documentation>Prescriber-Patient Agreement not found.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FA">
				<xsd:annotation>
					<xsd:documentation>No attempt will be made to obtain REMS approval.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FB">
				<xsd:annotation>
					<xsd:documentation>Electronic REMS not supported. Submit via other methods.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FC">
				<xsd:annotation>
					<xsd:documentation>Can not find REMSCaseID</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FD">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization not required for patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FE">
				<xsd:annotation>
					<xsd:documentation>Patient not eligible</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FF">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization duplicate/approved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FG">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization duplicate/in process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FH">
				<xsd:annotation>
					<xsd:documentation>REMS Authorization duplicate/denied</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FI">
				<xsd:annotation>
					<xsd:documentation>Closed by REMS Administrator</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FJ">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the REMS Administrator for this patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FK">
				<xsd:annotation>
					<xsd:documentation>The receiver is not the REMS Administrator for this patient and medication combination</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode21  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode21">
		<xsd:annotation>
			<xsd:documentation>Fill Status Note Structure. Used for PartiallyDispensed.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="BJ">
				<xsd:annotation>
					<xsd:documentation>Out of Stock – The dispensing entity does not have sufficient quantity of the requested product to fulfill the order/prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DF">
				<xsd:annotation>
					<xsd:documentation>Generic Substitution – A modification of the product prescribed to a generic equivalent.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DG">
				<xsd:annotation>
					<xsd:documentation>Therapeutic Interchange/Substitution – A modification of the product prescribed to a preferred product choice.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DH">
				<xsd:annotation>
					<xsd:documentation>Profile Medication – medication appropriate for administration, not dispensed by pharmacy at this time.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FT">
				<xsd:annotation>
					<xsd:documentation>The medication presscribed is available for administration from the Automated Dispensing System </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FV">
				<xsd:annotation>
					<xsd:documentation>The medication prescribed is available for cycle fill</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FW">
				<xsd:annotation>
					<xsd:documentation>Patient requested reduced quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FX">
				<xsd:annotation>
					<xsd:documentation>Prescriber requested/allowed reduced quantity.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FY">
				<xsd:annotation>
					<xsd:documentation>Days’ Supply and/or Quantity are limited by Payer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FZ">
				<xsd:annotation>
					<xsd:documentation>Regulatory Days Supply Limitation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their state license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GB">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their DEA license status in prescribing state.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GC">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their DEA registration by DEA class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GD">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their state Controlled Substance Registration license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GE">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their registration by state Controlled Substance Registration class.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GF">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm their NADEAN license status.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GG">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must obtain/validate Type1 NPI.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GH">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must enroll/re-enroll with prescription benefit plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GI">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must confirm prescriptive authority criteria for prescribed medication is met.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GJ">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must enroll/re-enroll in REMS.  </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GK">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GL">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – prescriber must obtain/validate their supervising prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode22  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode22">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxChangeResponse-Validated</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="GM">
				<xsd:annotation>
					<xsd:documentation>Active Registration Status</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GN">
				<xsd:annotation>
					<xsd:documentation>In-Active License with prescriptive authority based on state/federal regulations</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GP">
				<xsd:annotation>
					<xsd:documentation>Active with Prescriptive Authority – prescribed product class</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GQ">
				<xsd:annotation>
					<xsd:documentation>Active with Prescriptive Authority – Prescriber Type</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GR">
				<xsd:annotation>
					<xsd:documentation>Active with Prescriptive Authority – Supervising Prescriber Type</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GS">
				<xsd:annotation>
					<xsd:documentation>Registered</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GT">
				<xsd:annotation>
					<xsd:documentation>Enrolled/Re-Enrolled</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GU">
				<xsd:annotation>
					<xsd:documentation>Assigned</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode3  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode3">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: CancelRxResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Unable to cancel prescription; prescription was transferred to another pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode4  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode4">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: MTMServiceResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>Qualified provider unavailable to provide this service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AT">
				<xsd:annotation>
					<xsd:documentation>Not accepting new patients</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AU">
				<xsd:annotation>
					<xsd:documentation>Unable to accommodate service based parameters</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AV">
				<xsd:annotation>
					<xsd:documentation>These parameters do not meet the patient's needs</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AW">
				<xsd:annotation>
					<xsd:documentation>Based on assessment, patient needs are outside of contractual agreement</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AX">
				<xsd:annotation>
					<xsd:documentation>Patient condition no longer applicable</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AY">
				<xsd:annotation>
					<xsd:documentation>Patient not available for service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZ">
				<xsd:annotation>
					<xsd:documentation>Patient declined service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BA">
				<xsd:annotation>
					<xsd:documentation>Qualified provider unavailable to provide this service</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BG">
				<xsd:annotation>
					<xsd:documentation>Not the patient-desired pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BY">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CL">
				<xsd:annotation>
					<xsd:documentation>Attachment type (mimetype) not supported</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode5  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode5">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxHistoryResponse-Approved</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AQ">
				<xsd:annotation>
					<xsd:documentation>More Medication History Available</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DJ">
				<xsd:annotation>
					<xsd:documentation>No Data – There is a response indicating that no data was found for the search criteria provided in the search request.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DK">
				<xsd:annotation>
					<xsd:documentation>Prescription Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DL">
				<xsd:annotation>
					<xsd:documentation>Disallowed – There was a permission problem of some type performing this request against this destination.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DM">
				<xsd:annotation>
					<xsd:documentation>Error- This response indicates a processing error in performing this request. (May only be used when a more specific ReasonCode does not apply).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode6  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode6">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status:RxHistoryResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BB">
				<xsd:annotation>
					<xsd:documentation>No Information Available</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>Not Authorized to Release</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GV">
				<xsd:annotation>
					<xsd:documentation>RequestorRole not authorized to receive PDMP data.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode7  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode7">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: RxFill-NotDispensed</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AD">
				<xsd:annotation>
					<xsd:documentation>Patient has requested refill too soon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AG">
				<xsd:annotation>
					<xsd:documentation>Fill/Refill not appropriate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AO">
				<xsd:annotation>
					<xsd:documentation>No attempt will be made to obtain Prior Authorization</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BF">
				<xsd:annotation>
					<xsd:documentation>Conscientious objection</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BG">
				<xsd:annotation>
					<xsd:documentation>Not the patient-desired pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BP">
				<xsd:annotation>
					<xsd:documentation>Out of Stock/Manufacturer Back Order – NDC requested is currently on back order from the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BQ">
				<xsd:annotation>
					<xsd:documentation>Discontinued by Manufacturer – NDC requested has been discontinued by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BT">
				<xsd:annotation>
					<xsd:documentation>Drug Recalled by Manufacturer – NDC requested has been recalled by the manufacturer.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Prescription not found</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DB">
				<xsd:annotation>
					<xsd:documentation>Drug Use Evaluation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>No refills remaining or medication order has expired</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DD">
				<xsd:annotation>
					<xsd:documentation>Pharmacy has no intention to stock the medication requested/prescribed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization has been denied by payer</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FS">
				<xsd:annotation>
					<xsd:documentation>The medication prescribed is out of stock and cannot be obtained in a clinically appropriate timeframe.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode8  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode8">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions-Response Status: ClinicalInfoResponse-Denied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BB">
				<xsd:annotation>
					<xsd:documentation>No Information Available</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>Not Authorized to Release</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BD">
				<xsd:annotation>
					<xsd:documentation>Unable to send Response in Format Requested</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonCode9  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonCode9">
		<xsd:annotation>
			<xsd:documentation>Codes used in response messages by the ultimate receiver. Used by Transactions: NewRxResponseDenied</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ReasonCode">
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Patient unknown to the Provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Patient never under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AC">
				<xsd:annotation>
					<xsd:documentation>Patient no longer under  Provider care</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AF">
				<xsd:annotation>
					<xsd:documentation>Patient should contact Provider first</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AM">
				<xsd:annotation>
					<xsd:documentation>Patient needs appointment</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>Prescriber not associated with this practice or location.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Request already responded to by other means (e.g. phone or fax)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ReasonForSubstitutionCodeUsed  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ReasonForSubstitutionCodeUsed">
		<xsd:annotation>
			<xsd:documentation>Restricted text for submitter to define their clarification basis for Substitution code applied. This field is not allowed to be used with Substitution value 0 or a non-specified value.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="BRAND MEDICALLY NECESSARY"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  RequestorRole  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="RequestorRole">
		<xsd:annotation>
			<xsd:documentation>Role of the PDMP query requestor. Required when mandated by state regulation.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="101">
				<xsd:annotation>
					<xsd:documentation>Dentist</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="102">
				<xsd:annotation>
					<xsd:documentation>Medical Intern under supervising DEA Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="103">
				<xsd:annotation>
					<xsd:documentation>Medical Intern with independent DEA Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="104">
				<xsd:annotation>
					<xsd:documentation>Medical Resident under supervising DEA Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="105">
				<xsd:annotation>
					<xsd:documentation>Medical Resident with independent DEA Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="106">
				<xsd:annotation>
					<xsd:documentation>Naturopathic Physician</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="107">
				<xsd:annotation>
					<xsd:documentation>Nurse Practitioner</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="108">
				<xsd:annotation>
					<xsd:documentation>Optometrist</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="109">
				<xsd:annotation>
					<xsd:documentation>Other Non-Prescriber</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="110">
				<xsd:annotation>
					<xsd:documentation>Other Prescriber</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="112">
				<xsd:annotation>
					<xsd:documentation>Physician</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="113">
				<xsd:annotation>
					<xsd:documentation>Physician's Assistant</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="114">
				<xsd:annotation>
					<xsd:documentation>Prescriber's Delegate – licensed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="115">
				<xsd:annotation>
					<xsd:documentation>Prescriber's Delegate – unlicensed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="116">
				<xsd:annotation>
					<xsd:documentation>Psychologist</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="117">
				<xsd:annotation>
					<xsd:documentation>Veterinarian</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="201">
				<xsd:annotation>
					<xsd:documentation>Pharmacist</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="202">
				<xsd:annotation>
					<xsd:documentation>Pharmacy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="203">
				<xsd:annotation>
					<xsd:documentation>Pharmacist Delegate – licensed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="204">
				<xsd:annotation>
					<xsd:documentation>Pharmacist Delegate – unlicensed</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ResidenceCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ResidenceCode">
		<xsd:annotation>
			<xsd:documentation>Code identifying the patient’s place of residence.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Home= Location, other than a hospital or other facility, where the patient receives drugs or services in a private residence.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2">
				<xsd:annotation>
					<xsd:documentation>Skilled Nursing Facility=A facility which primarily provides inpatient skilled nursing care and related services to patients who require medical, nursing, or rehabilitative service but does not provide the level of care or treatment available in a hospital.  For Medicare Part B use only.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3">
				<xsd:annotation>
					<xsd:documentation>Nursing Facility= A facility which primarily provides to residents skilled nursing care and related services for the rehabilitation of injured, disabled, or sick persons, or, on a regular basis,, health-related care services above the level of custodial care to other than mentally retarded individuals.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="4">
				<xsd:annotation>
					<xsd:documentation>Assisted Living Facility= Congregate residential facility with self-contained living units providing assessment of each resident’s needs and on-site support 24 hours a day, 7 days a week, with the capacity to deliver or arrange for services including some health care and other services.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="5">
				<xsd:annotation>
					<xsd:documentation>Custodial Care Facility=A facility which provides room, board and other personal assistance services, generally on a long-term basis, and which does not include a medical component. For Medicare Part B use only.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="6">
				<xsd:annotation>
					<xsd:documentation>Group Home=Congregate residential foster care setting for children and adolescents in state custody that provides some social, health care, and educational support services and that promotes rehabilitation and reintegration of residents into the community.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="7">
				<xsd:annotation>
					<xsd:documentation>Inpatient Psychiatric Facility=A facility that provides inpatient psychiatric services for the diagnosis and treatment of mental illness on a 24-hour basis, by or under the supervision of a physician. Not applicable to Pharmacy Benefits.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="8">
				<xsd:annotation>
					<xsd:documentation>Psychiatric Facility – Partial Hospitalization=A facility for the diagnosis and treatment of mental illness that provides a planned therapeutic program for patients who do not require full time hospitalization, but who need broader programs than are possible from outpatient visits to a hospital-based or hospital-affiliated facility. Not applicable to Pharmacy Benefits.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="9">
				<xsd:annotation>
					<xsd:documentation>Intermediate Care Facility/Mentally Retarded=A facility which primarily provides health-related care and services above the level of custodial care to mentally retarded individuals but does not provide the level of care or treatment available in a hospital or SNF.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="10">
				<xsd:annotation>
					<xsd:documentation>Residential Substance Abuse Treatment Facility=A facility which provides treatment for substance (alcohol and drug) abuse to live-in residents who do not require acute medical care. Services include individual and group therapy and counseling, family counseling, laboratory tests, drugs and supplies, psychological testing, and room and board. Not applicable to Pharmacy Benefits.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="11">
				<xsd:annotation>
					<xsd:documentation>Hospice= A facility, other than a patient's home, in which palliative and supportive care for terminally ill patients and their families are provided.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="12">
				<xsd:annotation>
					<xsd:documentation>Psychiatric Residential Treatment Facility=A facility or distinct part of a facility for psychiatric care which provides a total 24-hour therapeutically planned and professionally staffed group living and learning environment. Not applicable to Pharmacy Benefits.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="0">
				<xsd:annotation>
					<xsd:documentation>Not Specified=Other patient residence not identified below.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="13">
				<xsd:annotation>
					<xsd:documentation>Comprehensive Inpatient Rehabilitation Facility=A facility that provides comprehensive rehabilitation services under the supervision of a physician to inpatients with physical disabilities.  Services include physical therapy, occupational therapy, speech pathology, social or psychological services, and orthotics and prosthetics services. Not applicable to Pharmacy Benefits.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="14">
				<xsd:annotation>
					<xsd:documentation>Homeless Shelter=A facility or location whose primary purpose is to provide temporary housing to homeless individuals (e.g., emergency shelters, individual or family shelters).  Not applicable to Pharmacy Benefits.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="15">
				<xsd:annotation>
					<xsd:documentation>Correctional Institution=A facility that provides treatment and rehabilitation of offenders through a program of penal custody.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  RxChangeCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="RxChangeCode">
		<xsd:annotation>
			<xsd:documentation>Used in Prescription Change Request transactions, to request a change to the original new prescription.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="MessageFunctionCode">
			<xsd:enumeration value="G">
				<xsd:annotation>
					<xsd:documentation>Generic Substitution - A modification of the product prescribed to a generic equivalent.			
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Prior Authorization Required - A request to obtain prior authorization before dispensing.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="T">
				<xsd:annotation>
					<xsd:documentation>Therapeutic Interchange/Substitution - A modification of the product prescribed to a preferred product choice.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="D">
				<xsd:annotation>
					<xsd:documentation>Drug Use Evaluation</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="S">
				<xsd:annotation>
					<xsd:documentation>Script Clarification</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OS">
				<xsd:annotation>
					<xsd:documentation>Pharmacy is out of stock of the medication prescribed and it cannot be obtained in a clinically appropriate timeframe.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="U">
				<xsd:annotation>
					<xsd:documentation>Prescriber Authorization – Resolution of the prescriber authorization conflict related to state/federal regulatory requirements is required before dispensing.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  RxFillIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="RxFillIndicator">
		<xsd:annotation>
			<xsd:documentation>Indicates the type of fill status transactions the prescriber would like to receive for this patient/medication combination.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="All Fill Statuses">
				<xsd:annotation>
					<xsd:documentation>All Fill Status Transactions - Sent when the prescriber requests RxFill transactions for dispensed, partially dispensed, not dispensed and transferred prescriptions for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="All Fill Statuses Except Transferred">
				<xsd:annotation>
					<xsd:documentation>All Fill Status Transactions but Transferred – Sent when the prescriber requests RxFill transactions for dispensed, partially dispensed and not dispensed prescriptions only for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Dispensed And Partially Dispensed">
				<xsd:annotation>
					<xsd:documentation>Dispensed and Partially Dispensed – Sent when the prescriber requests RxFill transactions for dispensed and partially dispensed prescriptions only for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Partially Dispensed And Not Dispensed">
				<xsd:annotation>
					<xsd:documentation>Partially Dispensed and Not Dispensed – Sent when the prescriber requests RxFill transactions for partially dispensed and not dispensed prescriptions only for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Not Dispensed And Transferred">
				<xsd:annotation>
					<xsd:documentation>Not Dispensed or Transferred – Sent when the prescriber requests RxFill transactions for not dispensed and transferred prescriptions for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Partially Dispensed">
				<xsd:annotation>
					<xsd:documentation>Partially Dispensed – Sent when the prescriber requests RxFill transactions for partially dispensed prescriptions only for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Not Dispensed">
				<xsd:annotation>
					<xsd:documentation>Not Dispensed – Sent when the prescriber requests RxFill transaction for not dispensed prescriptions only for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Cancel All Fill Statuses">
				<xsd:annotation>
					<xsd:documentation>Cancel All Fill Statuses – Sent when the prescriber requests that no more RxFill transactions be sent for this patient/medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SecondaryDiagnosisCodeQualifierCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="SecondaryDiagnosisCodeQualifierCode">
		<xsd:annotation>
			<xsd:documentation>Qualifies the code list used for the secondarydiagnosisvalue.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="ABF">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-10- Clinical Modifications</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DX">
				<xsd:annotation>
					<xsd:documentation>International Classification of Diseases-9- Clinical Modifications-Diagnosis</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LD">
				<xsd:annotation>
					<xsd:documentation>SNOMED - Systematized Nomenclature of Medicine Clinical Terms® (SNOMED CT). SNOMED CT® terminology which is available from the Health Terminology Standards Development Organisation (IHTSDO) http://www.ihtsdo.org/snomed-ct/</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ServiceReasonCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ServiceReasonCode">
		<xsd:annotation>
			<xsd:documentation>Code identifying the type of conflict detected.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="PC">
				<xsd:annotation>
					<xsd:documentation>Patient Question/Concern –Code indicating that a request for information/concern was expressed by the patient, with respect to patient care.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PP">
				<xsd:annotation>
					<xsd:documentation>Plan Protocol – Code indicating that a cognitive service whereby a pharmacist, in consultation with the prescriber or using professional judgment, recommends a course of therapy as outlined in the patient’s plan and submits a claim for the professional service provided.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EX">
				<xsd:annotation>
					<xsd:documentation>Excessive Quantity-Code that documents the quantity is excessive for the single time period for which the drug is being prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OH">
				<xsd:annotation>
					<xsd:documentation>Alcohol Conflict  - Detects when a prescribed drug is contraindicated or might conflict with the use of alcoholic beverages</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DF">
				<xsd:annotation>
					<xsd:documentation>Drug-Food interaction-Indicates interactions between a drug and certain foods.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NS">
				<xsd:annotation>
					<xsd:documentation>Insufficient Quantity- Code indicating that the quantity of dosage units prescribed is insufficient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AN">
				<xsd:annotation>
					<xsd:documentation>Prescription Authentication –Code indicating that circumstances required the pharmacist to verify the validity and/or authenticity of the prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HD">
				<xsd:annotation>
					<xsd:documentation>High Dose-Detects drug doses that fall above the standard dosing range.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>Suboptimal Compliance – Code indicating that professional service was provided to counsel the patient regarding the importance of adherence to the provided instructions and of consistent use of the prescribed product including any ill effects anticipated as a result of non-compliance.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Drug-Age- Indicates age-dependent drug problems.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PG">
				<xsd:annotation>
					<xsd:documentation>Drug-Pregnancy-Indicates pregnancy related drug problems. This information is intended to assist the healthcare professional in weighing the therapeutic value of a drug against possible adverse effects on the fetus.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DA">
				<xsd:annotation>
					<xsd:documentation>Drug-Allergy – Indicates that an adverse immune event may occur due to the patient’s previously demonstrated heightened allergic response to the drug product in question.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IC">
				<xsd:annotation>
					<xsd:documentation>Iatrogenic Condition-Code indicating that a possible inappropriate use of drugs that are designed to amerliorate complications caused by another medication has been detected.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MN">
				<xsd:annotation>
					<xsd:documentation>Insufficient Duration – Code indicating that regimens shorter than the minimal limit of therapy for the drug product, based on the product’s common uses, has been detected.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DI">
				<xsd:annotation>
					<xsd:documentation>Drug Incompatibility-Indicates physical and chemical incompatibilities between two or more drugs.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DL">
				<xsd:annotation>
					<xsd:documentation>Drug-Lab Conflict –Indicates that laboratory values may be altered due to the use of the drug, or that the patient’s response to the drug may be altered due to a condition that is identified by a certain laboratory value.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Adverse Drug Reaction – Code indicating an adverse reaction by a patient to a drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SX">
				<xsd:annotation>
					<xsd:documentation>Drug-Gender- Indicates the therapy is inappropriate or contraindicated in either males or females.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AT">
				<xsd:annotation>
					<xsd:documentation>Additive Toxicity – Code indicating a detection of drugs with similar side effects when used in combination could exhibit a toxic potential greater than either agent by itself.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PR">
				<xsd:annotation>
					<xsd:documentation>Prior Adverse Reaction – Code identifying the patient has had a previous atypical reaction to drugs.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>Drug-Disease (Inferred)-Indicates that the use of the drug may be inappropriate in light of a specific medical condition that the patient has.  The existence of the specific medical condition is inferred from drugs in the patient’s medication history.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MS">
				<xsd:annotation>
					<xsd:documentation>Missing Information/Clarification-Code indicating that the prescription order is unclear, incomplete, or illegible with respect to essential information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TD">
				<xsd:annotation>
					<xsd:documentation>Therapeutic – Code indicating that a simultaneous use of different primary generic chemical entities that have the same therapeutic effect was detected.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ID">
				<xsd:annotation>
					<xsd:documentation>Ingredient Duplication- Code indicating that simultaneous use of drug products containing one or more identical generic chemical entities has been detected.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PS">
				<xsd:annotation>
					<xsd:documentation>Product Selection Opportunity – Code indicating that an acceptable generic substitute or a therapeutic equivalent exists for the drug. This code is intended to support discretionary drug product selection activities by pharmacists.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MX">
				<xsd:annotation>
					<xsd:documentation>Excessive Duration- Detects regimens that are longer than the maximal limit of therapy for a drug product based on the product’s common uses.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LD">
				<xsd:annotation>
					<xsd:documentation>Low Dose Code indicating that the submitted drug doses fall below the standard dosing range.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LK">
				<xsd:annotation>
					<xsd:documentation>Lock In Recipient – Code indicating that the professional service was related to a plan/payer constraint on the member whereby the member is required to obtain services from only one specified pharmacy or other provider type, hence the member is “locked in” to using only those providers or pharmacies.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ED">
				<xsd:annotation>
					<xsd:documentation>Patient Education/Instruction Code indicating that a cognitive service whereby the pharmacist performed a patient care activity by providing additional instructions or education to the patient beyond the simple task of explaining the prescriber’s instructions on the prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PH">
				<xsd:annotation>
					<xsd:documentation>Preventive Health Care – Code indicating that the provided professional service was to educate the patient regarding measures mitigating possible adverse effects or maximizing the benefits of the product(s) dispensed; or measures to optimize health status, prevent recurrence or exacerbation of problems.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CD">
				<xsd:annotation>
					<xsd:documentation>Chronic Disease Management – The patient is participating in a coordinated health care intervention program.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NF">
				<xsd:annotation>
					<xsd:documentation>Non-Formulary Drug-Code indicating that mandatory formulary enforcement activities have been performed by the pharmacist when the drug is not included on the formulary of the patient’s pharmacy benefit plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NC">
				<xsd:annotation>
					<xsd:documentation>Non-covered Drug Purchase-Code indicating a cognitive service whereby a patient is counseled, the pharmacist’s recommendation is accepted and a claim is submitted to the processor requesting payment for the professional pharmacy service only, not the drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NA">
				<xsd:annotation>
					<xsd:documentation>Drug Not Available-Indicates the drug is not currently available from any source.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DM">
				<xsd:annotation>
					<xsd:documentation>Apparent Drug Misuse Code indicating a pattern of drug use by a patient in a manner that is significantly different than that prescribed by the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CH">
				<xsd:annotation>
					<xsd:documentation>Call Help Desk – Processor message to recommend the receiver contact the processor/plan.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CS">
				<xsd:annotation>
					<xsd:documentation>Patient Complaint/Symptom- Code indicating that in the course of assessment or discussion with the patient, the pharmacist identified an actual or potential problem when the patient presented to the pharmacist complaints or symptoms suggestive of illness requesting evaluation and treatment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DD">
				<xsd:annotation>
					<xsd:documentation>Drug-Drug Interaction-Indicates that drug combinations in which the net pharmacologic response may be different from the result expected when each drug is given separately.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SD">
				<xsd:annotation>
					<xsd:documentation>Suboptimal Drug/Indication- Code indicating incorrect, inappropriate, or less than optimal drug prescribed for the patient’s condition.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RE">
				<xsd:annotation>
					<xsd:documentation>Suspected Environmental Risk- Code indicating that the professional service was provided to obtain information from the patient regarding suspected environmental factors.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TN">
				<xsd:annotation>
					<xsd:documentation>Laboratory Test Needed –Code indicating that an assessment of the patient suggests that a laboratory test is needed to optimally manage a therapy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DR">
				<xsd:annotation>
					<xsd:documentation>Dose Range Conflict - Code indicating that the prescription does not follow recommended medication dosage.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ER">
				<xsd:annotation>
					<xsd:documentation>Overuse – Code indicating that the current prescription refill is occurring before the days supply of the previous filling should have been exhausted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SE">
				<xsd:annotation>
					<xsd:documentation>Side Effect – Code reporting possible major side effects of the prescribed drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AD">
				<xsd:annotation>
					<xsd:documentation>Additional Drug Needed  - Code indicating optimal treatment of the patient’s condition requiring the addition of a new drug to the existing drug therapy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NP">
				<xsd:annotation>
					<xsd:documentation>New Patient Processing-Code indicating that a pharmacist has performed the initial interview and medication history of a new patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TP">
				<xsd:annotation>
					<xsd:documentation>Payer/Processor Question - Code indicating that a payer or processor requested information related to the care of a patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LR">
				<xsd:annotation>
					<xsd:documentation>Underuse Code indicating that a prescription refill that occurred after the days supply of the previous filling should have been exhausted.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>New Disease/Diagnosis-Code indicating that a professional pharmacy service has been performed for a patient who has a newly diagnosed condition or disease.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SF">
				<xsd:annotation>
					<xsd:documentation>Suboptimal Dosage Form – Code indicating incorrect, inappropriate, or less than optimal dosage form for the drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NN">
				<xsd:annotation>
					<xsd:documentation>Unnecessary Drug – Code indicating that the drug is no longer needed by the patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UD">
				<xsd:annotation>
					<xsd:documentation>Duplicate Drug – Code indicating that multiple prescriptions of the same drug formulation are present in the patient’s current medication profile.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PN">
				<xsd:annotation>
					<xsd:documentation>Prescriber Consultation –Code indicating that a prescriber has requested information or a recommendation related to the care of a patient.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DS">
				<xsd:annotation>
					<xsd:documentation>Tobacco Use – Code indicating that a conflict was detected when a prescribed drug is contraindicated or might conflict with the use of tobacco products.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MC">
				<xsd:annotation>
					<xsd:documentation>Drug-Disease (Reported)- Indicates that the use of the drug may be inappropriate in light of a specific medical condition that the patient has. Information about the specific medical condition was provided by the prescriber, patient or pharmacist.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RF">
				<xsd:annotation>
					<xsd:documentation>Health Provider Referral-Patient referred to the pharmacist by another health care provider for disease specific or general purposes.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SR">
				<xsd:annotation>
					<xsd:documentation>Suboptimal Regimen – Code indicating incorrect, inappropriate, or less than optimal dosage regimen specified for the drug in question.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NR">
				<xsd:annotation>
					<xsd:documentation>Lactation/Nursing Interaction-Code indicating that the drug is excreted in breast milk and may represent a danger to a nursing infant.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ServiceResultCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ServiceResultCode">
		<xsd:annotation>
			<xsd:documentation>Action taken in response to a conflict.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="4A">
				<xsd:annotation>
					<xsd:documentation>Prescribed With Acknowledgments - Physician is prescribing this medication with knowledge of the potential conflict.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3C">
				<xsd:annotation>
					<xsd:documentation>Discontinued Drug- Cognitive service involving the pharmacist’s review of drug therapy that results in the removal of a medication from the therapeutic regimen.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1C">
				<xsd:annotation>
					<xsd:documentation>Filled, With Different Dose- Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and fills the prescription with a different dose than was originally prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3E">
				<xsd:annotation>
					<xsd:documentation>Therapy Changed – Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert), recommends a more appropriate product or regimen then dispenses the alternative after consultation with the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3M">
				<xsd:annotation>
					<xsd:documentation>Compliance Aid Provided – Cognitive service whereby the pharmacist supplies a product that assists the patient in complying with instructions for taking medications.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1E">
				<xsd:annotation>
					<xsd:documentation>Filled, With Different Drug- Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and fills the prescription with a different drug than was originally prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2A">
				<xsd:annotation>
					<xsd:documentation>Prescription Not Filled - Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert) and determines that the prescription should not be filled as written.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1B">
				<xsd:annotation>
					<xsd:documentation>Filled Prescription As Is-Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and determines the alert is not relevant for that prescription for that patient and fills the prescription as originally written.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3A">
				<xsd:annotation>
					<xsd:documentation>Recommendation Accepted – Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert), recommends a more appropriate product or regimen then dispenses the alternative after consultation with the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3D">
				<xsd:annotation>
					<xsd:documentation>Regimen Changed - Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert), recommends a more appropriate regimen then dispenses the recommended medication(s) after consultation with the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1H">
				<xsd:annotation>
					<xsd:documentation>Brand-to-Generic Change – Action whereby a pharmacist dispenses the generic formulation of an originally prescribed branded product. Allowed, often mandated, unless the prescriber indicates “Do Not Substitute” on the prescription.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3B">
				<xsd:annotation>
					<xsd:documentation>Recommendation Not Accepted - Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert), recommends a more appropriate product or regimen but the prescriber does not concur.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3J">
				<xsd:annotation>
					<xsd:documentation>Patient Referral – Code indicating the referral of a patient to another health care provider following evaluation by the pharmacist.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1F">
				<xsd:annotation>
					<xsd:documentation>Filled, With Different Quantity – Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and fills the prescription with a different quantity than was originally prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1K">
				<xsd:annotation>
					<xsd:documentation>Filled with Different Dosage Form- Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and fills the prescription with a different dosage form than was originally prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3H">
				<xsd:annotation>
					<xsd:documentation>Follow-Up/Report – Code indicating that additional follow through by the pharmacist is required.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1D">
				<xsd:annotation>
					<xsd:documentation>Filled, With Different Directions – Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and fills the prescription with different directions than were originally prescribed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="2B">
				<xsd:annotation>
					<xsd:documentation>Not Filled, Directions Clarified-Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert), consults with the prescriber or using professional judgment, does not fill the prescription and counsels the patient as to the prescriber’s instructions.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3G">
				<xsd:annotation>
					<xsd:documentation>Drug Therapy Unchanged-Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert), consults with the prescriber or uses professional judgment and subsequently fills the prescription as originally written.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3N">
				<xsd:annotation>
					<xsd:documentation>Medication Administered-Cognitive service whereby the pharmacist performs a patient care activity by personally administering the medication.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1A">
				<xsd:annotation>
					<xsd:documentation>Filled As Is, False Positive-Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and determines the alert is incorrect for that prescription for that patient and fills the prescription as originally written.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="00">
				<xsd:annotation>
					<xsd:documentation>Not Specified</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3F">
				<xsd:annotation>
					<xsd:documentation>Therapy Changed-cost increased acknowledged - Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert), recommends a more appropriate product or regimen acknowledging that a cost increase will be incurred, then dispenses the alternative after consultation with the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="3K">
				<xsd:annotation>
					<xsd:documentation>Instructions Understood – Indicator used to convey that the patient affirmed understanding of the instructions provided by the pharmacist regarding the use and handling of the medication dispensed.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1J">
				<xsd:annotation>
					<xsd:documentation>Rx-to-OTC Change – Code indicating a cognitive service. The pharmacist reviews and evaluates a therapeutic issue (alert) fills the prescription with an over-the-counter product in lieu of the originally prescribed prescription-only product.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1G">
				<xsd:annotation>
					<xsd:documentation>Filled, With Prescriber Approval Cognitive service whereby the pharmacist reviews and evaluates a therapeutic issue (alert) and fills the prescription after consulting with or obtaining approval from the prescriber.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ServiceTypeCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="ServiceTypeCode">
		<xsd:annotation>
			<xsd:documentation>Medication list contains all current medication orders as of the current date and time of the response, for the patient indicated. Current status is determined by the point of care responder. 'Current' is medication orders which have not been discontinued.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Current Medication Orders - The contents are limited to medication orders that are current (within their Start and Stop dates, or on-or-after the Start date if open-ended).</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SigCodes  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="SigCodes">
		<xsd:annotation>
			<xsd:documentation>Used for Sig (previously was value 1 (SNOMED).  Refer Code Set Qualifier Values.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="SNOMED">
				<xsd:annotation>
					<xsd:documentation>SNOMED Systematized Nomenclature of Medicine--Clinical Terms (SNOMED) is available at http://www.ihtsdo.org/snomed-ct/.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SigUnitOfMeasure  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="SigUnitOfMeasure">
		<xsd:annotation>
			<xsd:documentation>Used for Sig (previously was value 2 (FMT).  Refer Code Set Qualifier Values.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="DoseUnitOfMeasure"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SourceOfInformation  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="SourceOfInformation">
		<xsd:annotation>
			<xsd:documentation>Sender indicates where the sender received the allergy information if known.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="P">
				<xsd:annotation>
					<xsd:documentation>Indicates information is provided by the patient or patient representative or personal health record (PHR)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="C">
				<xsd:annotation>
					<xsd:documentation>Indicates information is provided by a clinician or provider</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SplitScript  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="SplitScript">
		<xsd:annotation>
			<xsd:documentation>Indicator of split prescription</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="B">
				<xsd:annotation>
					<xsd:documentation>Prescription was written with a retail bridge supply and a mail prescription</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  StateProvince  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="StateProvince">
		<xsd:annotation>
			<xsd:documentation>State/Province Code of the address.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="WA">
				<xsd:annotation>
					<xsd:documentation>Washington</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OK">
				<xsd:annotation>
					<xsd:documentation>Oklahoma</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AZ">
				<xsd:annotation>
					<xsd:documentation>Arizona</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ON">
				<xsd:annotation>
					<xsd:documentation>Ontario</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="HI">
				<xsd:annotation>
					<xsd:documentation>Hawaii</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MO">
				<xsd:annotation>
					<xsd:documentation>Missouri</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NU">
				<xsd:annotation>
					<xsd:documentation>Nunavut			
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VI">
				<xsd:annotation>
					<xsd:documentation>Virgin Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AK">
				<xsd:annotation>
					<xsd:documentation>Alaska</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TX">
				<xsd:annotation>
					<xsd:documentation>Texas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OH">
				<xsd:annotation>
					<xsd:documentation>Ohio</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GU">
				<xsd:annotation>
					<xsd:documentation>Guam</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MS">
				<xsd:annotation>
					<xsd:documentation>Mississippi</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MP">
				<xsd:annotation>
					<xsd:documentation>Northern Mariana Islands</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AL">
				<xsd:annotation>
					<xsd:documentation>Alabama
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VA">
				<xsd:annotation>
					<xsd:documentation>Virginia
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NT">
				<xsd:annotation>
					<xsd:documentation>Northwest Territories</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GA">
				<xsd:annotation>
					<xsd:documentation>Georgia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TN">
				<xsd:annotation>
					<xsd:documentation>Tennessee</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ND">
				<xsd:annotation>
					<xsd:documentation>North Dakota
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MN">
				<xsd:annotation>
					<xsd:documentation>Minnesota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NS">
				<xsd:annotation>
					<xsd:documentation>Nova Scotia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FM">
				<xsd:annotation>
					<xsd:documentation>Federated States Of Micronesia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SD">
				<xsd:annotation>
					<xsd:documentation>South Dakota</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="FL">
				<xsd:annotation>
					<xsd:documentation>Florida</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NL">
				<xsd:annotation>
					<xsd:documentation>Newfoundland and Labrador</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DC">
				<xsd:annotation>
					<xsd:documentation>District Of Columbia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MI">
				<xsd:annotation>
					<xsd:documentation>Michigan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NB">
				<xsd:annotation>
					<xsd:documentation>New Brunswick</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NC">
				<xsd:annotation>
					<xsd:documentation>North Carolina
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SC">
				<xsd:annotation>
					<xsd:documentation>South Carolina
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MA">
				<xsd:annotation>
					<xsd:documentation>Massachusetts</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DE">
				<xsd:annotation>
					<xsd:documentation>Delaware			
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MB">
				<xsd:annotation>
					<xsd:documentation>Manitoba</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RI">
				<xsd:annotation>
					<xsd:documentation>Rhode Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NY">
				<xsd:annotation>
					<xsd:documentation>New York   </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BC">
				<xsd:annotation>
					<xsd:documentation>British Columbia			
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CT">
				<xsd:annotation>
					<xsd:documentation>Connecticut</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MD">
				<xsd:annotation>
					<xsd:documentation>Maryland</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PR">
				<xsd:annotation>
					<xsd:documentation>Puerto Rico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NM">
				<xsd:annotation>
					<xsd:documentation>New Mexico</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ME">
				<xsd:annotation>
					<xsd:documentation>Maine</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AB">
				<xsd:annotation>
					<xsd:documentation>Alberta</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LA">
				<xsd:annotation>
					<xsd:documentation>Louisiana</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MH">
				<xsd:annotation>
					<xsd:documentation>Marshall Islands
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KS">
				<xsd:annotation>
					<xsd:documentation>Kansas</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CO">
				<xsd:annotation>
					<xsd:documentation>Colorado</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NV">
				<xsd:annotation>
					<xsd:documentation>Nevada</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NJ">
				<xsd:annotation>
					<xsd:documentation>New Jersey</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PA">
				<xsd:annotation>
					<xsd:documentation>Pennsylvania</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="YT">
				<xsd:annotation>
					<xsd:documentation>Yukon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="KY">
				<xsd:annotation>
					<xsd:documentation>Kentucky</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CA">
				<xsd:annotation>
					<xsd:documentation>California   </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AS">
				<xsd:annotation>
					<xsd:documentation>American Samoa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WY">
				<xsd:annotation>
					<xsd:documentation>Wyoming</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IA">
				<xsd:annotation>
					<xsd:documentation>Iowa</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NH">
				<xsd:annotation>
					<xsd:documentation>New Hampshire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="NE">
				<xsd:annotation>
					<xsd:documentation>Nebraska
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PW">
				<xsd:annotation>
					<xsd:documentation>Palau</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SK">
				<xsd:annotation>
					<xsd:documentation>Saskatchewan</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IN">
				<xsd:annotation>
					<xsd:documentation>Indiana
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AR">
				<xsd:annotation>
					<xsd:documentation>Arkansas
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WI">
				<xsd:annotation>
					<xsd:documentation>Wisconsin
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IL">
				<xsd:annotation>
					<xsd:documentation>Illinois</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="QC">
				<xsd:annotation>
					<xsd:documentation>Quebec			
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="VT">
				<xsd:annotation>
					<xsd:documentation>Vermont
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OR">
				<xsd:annotation>
					<xsd:documentation>Oregon</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="WV">
				<xsd:annotation>
					<xsd:documentation>West Virginia</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PE">
				<xsd:annotation>
					<xsd:documentation>Prince Edward Island</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="MT">
				<xsd:annotation>
					<xsd:documentation>Montana
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="ID">
				<xsd:annotation>
					<xsd:documentation>Idaho
</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="UT">
				<xsd:annotation>
					<xsd:documentation>Utah</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AA">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Americas (except Canada)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AE">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Africa Armed Forces Canada Armed Forces Europe Armed Forces Middle East</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AP">
				<xsd:annotation>
					<xsd:documentation>Armed Forces Pacific</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  StatusCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="StatusCode">
		<xsd:annotation>
			<xsd:documentation>Codes used to relay successful or rejected communications.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="000">
				<xsd:annotation>
					<xsd:documentation>Transaction successful</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="001">
				<xsd:annotation>
					<xsd:documentation>Transaction successful, message(s) waiting to be retrieved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="002">
				<xsd:annotation>
					<xsd:documentation>No more messages</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="003">
				<xsd:annotation>
					<xsd:documentation>Transaction successful, no messages to be retrieved</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="005">
				<xsd:annotation>
					<xsd:documentation>Transaction successful, password soon to expire</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="010">
				<xsd:annotation>
					<xsd:documentation>Successful - accepted by ultimate receiver</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  SubstitutionCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="SubstitutionCode">
		<xsd:annotation>
			<xsd:documentation>Code indicating whether or not the prescriber’s instructions regarding generic substitution were followed.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="0">
				<xsd:annotation>
					<xsd:documentation>No Product Selection Indicated - This is the field default value that is appropriately used for prescriptions for single source brand, co-branded/co-licensed, or generic products. For a multi-source branded product with available generic(s), DAW 0 is not appropriate, and may result in a reject.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="1">
				<xsd:annotation>
					<xsd:documentation>Substitution Not Allowed by Prescriber - This value is used when the prescriber indicates, in a manner specified by prevailing law, that the product is Medically Necessary to be Dispensed As Written. DAW 1 is based on prescriber instruction and not product classification. </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  TimeZoneIdentifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="TimeZoneIdentifier">
		<xsd:annotation>
			<xsd:documentation>Defines the time zone used by the sender.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="UT">
				<xsd:annotation>
					<xsd:documentation>Universal Time Coordinate</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  TransactionDomain  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="TransactionDomain">
		<xsd:annotation>
			<xsd:documentation>Element defines which NCPDP business domain schema is being used.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="SCRIPT">
				<xsd:annotation>
					<xsd:documentation>NCPDP SCRIPT schema</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SPECIALIZED">
				<xsd:annotation>
					<xsd:documentation>NCPDP Specialized schema</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  TransactionErrorCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="TransactionErrorCode">
		<xsd:annotation>
			<xsd:documentation>Codes used to relay successful or rejected communications. Status Code list.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="ErrorCode">
			<xsd:enumeration value="600">
				<xsd:annotation>
					<xsd:documentation>Communication problem - try again later</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="601">
				<xsd:annotation>
					<xsd:documentation>Receiver unable to process</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="602">
				<xsd:annotation>
					<xsd:documentation>Receiver System Error</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="700">
				<xsd:annotation>
					<xsd:documentation>Configuration Error</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="900">
				<xsd:annotation>
					<xsd:documentation>Transaction rejected</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  TransferRequest  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="TransferRequest">
		<xsd:annotation>
			<xsd:documentation>Indicates the prescriptions requested to be transferred.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="ALL">
				<xsd:annotation>
					<xsd:documentation>Request all fillable prescriptions for patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="SPECIFIC">
				<xsd:annotation>
					<xsd:documentation>Request specific fillable prescription(s) for a patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  TransferType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="TransferType">
		<xsd:annotation>
			<xsd:documentation>Indicates whether the requesting or source pharmacy.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="TRANSFER TO PHARMACY">
				<xsd:annotation>
					<xsd:documentation>This is the requesting pharmacy has sent the transfer request and will be transferring in the prescriptions, i.e. the transfer to pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="TRANSFER FROM PHARMACY">
				<xsd:annotation>
					<xsd:documentation>This is the pharmacy has received the transfer request and will be transferring out the prescriptions, i.e. the transfer from pharmacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  TreatmentIndicator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="TreatmentIndicator">
		<xsd:annotation>
			<xsd:documentation>Indicates if the order is new, continuation of treatment or a restart of treatment.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="NEW">
				<xsd:annotation>
					<xsd:documentation>Indicates the therapy is new to the patient</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="CONTINUATION">
				<xsd:annotation>
					<xsd:documentation>Indicates a continuation of an existing therapy</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RESTART">
				<xsd:annotation>
					<xsd:documentation>Indicates a prior therapy has been restarted</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  UpperBoundComparisonOperator  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="UpperBoundComparisonOperator">
		<xsd:annotation>
			<xsd:documentation>Code that conveys the relationship between the answered value to a question and a defined lower boundary.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="GE">
				<xsd:annotation>
					<xsd:documentation>Greater Than or Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="GT">
				<xsd:annotation>
					<xsd:documentation>Greater Than</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LE">
				<xsd:annotation>
					<xsd:documentation>Less Than or Equal To</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="LT">
				<xsd:annotation>
					<xsd:documentation>Less Than</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  UrgencyIndicatorCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="UrgencyIndicatorCode">
		<xsd:annotation>
			<xsd:documentation>Element which indicates the sender’s urgency of the associated message. Indicates the requested PA processing priority </xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="S">
				<xsd:annotation>
					<xsd:documentation>Standard - A request with standard time-frames is one that the provider has deemed to not be life threatening or would otherwise not endanger the well-being of the patient if the request is not handled with immediacy.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="X">
				<xsd:annotation>
					<xsd:documentation>Urgent/Expedited - A request that requires immediate attention because the treatment is required to prevent serious deterioration in the patient’s health or could jeopardize the patient’s ability to regain maximum function.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  WoundLateralityCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="WoundLateralityCode">
		<xsd:annotation>
			<xsd:documentation>Code representing the laterality associated with the wound location.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="255549009">
				<xsd:annotation>
					<xsd:documentation>Anterior</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="7771000">
				<xsd:annotation>
					<xsd:documentation>Left</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="255561001">
				<xsd:annotation>
					<xsd:documentation>Medial</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="255551008">
				<xsd:annotation>
					<xsd:documentation>Posterior</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="24028007">
				<xsd:annotation>
					<xsd:documentation>Right</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  eclSNOMEDAdverseEventCode  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="eclSNOMEDAdverseEventCode">
		<xsd:annotation>
			<xsd:documentation>The code list defining the Item Number.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="420134006">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - propensity to adverse reactions - Used to record an adverse reaction.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="418038007">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - propensity to adverse reactions to substance - Used to record an adverse reaction to an environmental agent.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="419511003">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - propensity to adverse reactions to drug - Used to record an adverse reaction to a drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="418471000">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - propensity to adverse reactions to food - Used to record an adverse reaction to a food.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="419199007">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - allergy to substance - Used to record an allergy to an environmental agent.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="416098002">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - drug allergy - Used to record an allergy to a drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="414285001">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - food allergy - Used to record an allergy to a food.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="59037007">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - drug intolerance - Used to record intolerance to a drug.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="235719002">
				<xsd:annotation>
					<xsd:documentation>SNOMED CT® Preferred Terms for Adverse Event Type - food intolerance - Used to record intolerance to a food.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  eclSourceQualifier  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="eclSourceQualifier">
		<xsd:annotation>
			<xsd:documentation>Qualifies the Source Description.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="Code">
			<xsd:enumeration value="P2">
				<xsd:annotation>
					<xsd:documentation>Pharmacy - A facility or location where drugs and other medically related items and services are sold, dispensed, or otherwise provided directly to patients.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PC">
				<xsd:annotation>
					<xsd:documentation>Prescriber - A licensed entity that prescribes prescription drugs and provides professional medical services, such as clinical services respective to the prescribing function</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="PY">
				<xsd:annotation>
					<xsd:documentation>Payer - Entity that processes the data submitted by a provider of pharmacy services for the purpose of receiving eligibility and coverage determination and/or payment.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
