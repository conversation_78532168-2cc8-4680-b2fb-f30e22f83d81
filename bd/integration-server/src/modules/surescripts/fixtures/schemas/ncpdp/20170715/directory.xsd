<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2014 (http://www.altova.com) by <PERSON> (Surescripts LLC) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified" version="directory-6.1.1">
	<xsd:include schemaLocation="datatypes.xsd"/>
	<xsd:include schemaLocation="ecl.xsd"/>
	<xsd:include schemaLocation="structures.xsd"/>
	<xsd:include schemaLocation="script.xsd"/>
	<xsd:include schemaLocation="specialized.xsd"/>
	<xsd:include schemaLocation="transport.xsd"/>
	<!-- ================================================== -->
	<!-- =====  Element Declarations  -->
	<!-- ================================================== -->
	<xsd:element name="DirectoryMessage" type="DirectoryMessageType"/>
	<!-- ================================================== -->
	<!-- =====  Simple Type Definitions  -->
	<!-- ================================================== -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  an1..64  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="an1..64">
		<xsd:annotation>
			<xsd:documentation>Length Constrained Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="an">
			<xsd:maxLength value="64"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  flagType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="flagType">
		<xsd:annotation>
			<xsd:documentation>Type Constrained to yes (Y) or no (N)</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="y|Y|n|N"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  n13  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:simpleType name="n13">
		<xsd:annotation>
			<xsd:documentation>Length Constrained Type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="n">
			<xsd:length value="13"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- ================================================== -->
	<!-- =====  Complex Type Definitions  -->
	<!-- ================================================== -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DisableOrganization  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DisableOrganization">
		<xsd:sequence>
			<xsd:element name="Organization">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Identification">
							<xsd:complexType>
								<xsd:choice minOccurs="1" maxOccurs="1">
									<xsd:element name="NCPDPID" type="an1..35" minOccurs="0"/>
									<xsd:element name="OrganizationID" type="n1..10" minOccurs="0"/>
									<xsd:element name="PayerID" type="an1..35" minOccurs="0"/>
								</xsd:choice>
							</xsd:complexType>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DisableProviderLocation -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DisableProviderLocation">
		<xsd:sequence>
			<xsd:element name="ProviderLocation">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Identification">
							<xsd:complexType>
								<xsd:sequence>
									<xsd:element name="SPI" type="n13"/>
								</xsd:sequence>
							</xsd:complexType>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryBodyType -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryBodyType">
		<xsd:annotation>
			<xsd:documentation>Directory Transactions</xsd:documentation>
		</xsd:annotation>
		<xsd:choice maxOccurs="1">
			<xsd:element name="AddOrganization" type="DirectoryOrganizationItemType">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to add an Organization.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddOrganizationResponse" type="DirectoryOrganizationItemType">
				<xsd:annotation>
					<xsd:documentation>This response is used to relay acceptance of the AddOrganization transaction back to the sender. The response will include the object that was added.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddProviderLocation" type="DirectoryProviderLocationItemType">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to add a Provider Location.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddProviderLocationResponse" type="DirectoryProviderLocationItemType">
				<xsd:annotation>
					<xsd:documentation>This response is used to relay acceptance of the AddProviderLocation transaction back to the sender. The response will include the object that was added.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DirectoryDownload" type="DirectoryDownload">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to request a Directory Download based on the specified identification value(s).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DirectoryDownloadResponse" type="DirectoryDownloadResponse">
				<xsd:annotation>
					<xsd:documentation>This reponse is used to return a URL based on the specified identification value(s) sent in a DirectoryDownload message.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DisableOrganization" type="DisableOrganization">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to disable an Organization.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DisableProviderLocation" type="DisableProviderLocation">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to disable a Provider Location.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Error" type="DirectoryError">
				<xsd:annotation>
					<xsd:documentation>This transaction response indicates an error has occurred indicating the request was terminated. An Error can be generated when there is a communication problem or when the transaction actually had an error.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GetOrganization" type="GetOrganization">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to request an Organization based on the specified identification value(s).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GetOrganizationResponse" type="GetOrganizationResponse">
				<xsd:annotation>
					<xsd:documentation>This response is used to return Organizations based on the specified identification value in the GetOrganization request.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GetPortal" type="GetPortal">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to request a Portal based on the specified identification values.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GetPortalResponse" type="GetPortalResponse">
				<xsd:annotation>
					<xsd:documentation>This response is used to return a Portal based on the specified identification values sent in a GetePortal message.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GetProviderLocation" type="GetProviderLocation">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to request a Provider Location based on the specified identification value(s).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GetProviderLocationResponse" type="GetProviderLocationResponse">
				<xsd:annotation>
					<xsd:documentation>This response is used to return a Provider Location based on the specified identification value(s) sent in a GetProviderLocation message.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Status" type="Status">
				<xsd:annotation>
					<xsd:documentation>This response is used to relay acceptance of a transaction back to the sender. A Status in response indicates acceptance and responsibility.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="UpdateOrganization" type="DirectoryOrganizationItemType">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to update an Organization.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="UpdatePortal" type="UpdatePortal">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to update a Portal.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="UpdateProviderLocation" type="DirectoryProviderLocationItemType">
				<xsd:annotation>
					<xsd:documentation>This transaction is used to update a Provider Location.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:choice>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryCommunicationNumbersType -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryCommunicationNumbersType">
		<xsd:sequence>
			<xsd:element name="PrimaryTelephone" type="PhoneType" minOccurs="0"/>
			<xsd:element name="Beeper" type="PhoneType" minOccurs="0" maxOccurs="6"/>
			<xsd:element name="ElectronicMail" type="MailAddressType" minOccurs="0"/>
			<xsd:element name="Fax" type="PhoneType" minOccurs="0"/>
			<xsd:element name="HomeTelephone" type="PhoneType" minOccurs="0" maxOccurs="6"/>
			<xsd:element name="WorkTelephone" type="PhoneType" minOccurs="0" maxOccurs="6"/>
			<xsd:element name="OtherTelephone" type="PhoneType" minOccurs="0" maxOccurs="6"/>
			<xsd:element name="DirectAddress" type="an1..254" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>THE DIRECT PROJECT ADDRESS OF THE ENTITY.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryDownload  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryDownload">
		<xsd:sequence>
			<xsd:element name="AccountID" type="an1..35"/>
			<xsd:element name="DownloadType">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:pattern value="(ProviderLocation)|(Organization)"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="DirectoryDate" type="UtcDateType" minOccurs="0"/>
			<xsd:element name="VersionID" type="an1..10" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryDownloadResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryDownloadResponse">
		<xsd:sequence>
			<xsd:element name="URL" type="an1..210"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryError  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryError">
		<xsd:sequence>
			<xsd:element name="Code" type="TransactionErrorCode"/>
			<xsd:element name="DescriptionCode" type="an1..3" minOccurs="0"/>
			<xsd:element name="Description" type="an" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryInformationType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryInformationType">
		<xsd:sequence>
			<xsd:element name="AccountID" type="an1..35"/>
			<xsd:element name="PortalID" type="an1..35"/>
			<xsd:element name="ActiveStartTime" type="UtcDateType"/>
			<xsd:element name="ActiveEndTime" type="UtcDateType"/>
			<xsd:element name="DirectorySpecialties" type="DirectorySpecialtiesType" minOccurs="0"/>
			<xsd:element name="FaxBackup" type="flagType" minOccurs="0"/>
			<xsd:element name="ServiceLevels" type="ServiceLevelsType" minOccurs="0"/>
			<!-- Test flag-->
			<xsd:element name="Test" minOccurs="0">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:pattern value="0|1|-1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="UseCases" type="UseCasesType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryMessageType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryMessageType">
		<xsd:sequence>
			<xsd:element name="Header" type="HeaderType"/>
			<xsd:element name="Body" type="DirectoryBodyType"/>
		</xsd:sequence>
		<xsd:attribute name="DatatypesVersion" type="an" use="required" fixed="20170715"/>
		<xsd:attribute name="TransportVersion" type="an" use="required" fixed="20170715"/>
		<xsd:attribute name="TransactionDomain" use="required">
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:pattern value="DIRECTORY"/>
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:attribute>
		<xsd:attribute name="TransactionVersion" type="an" use="required" fixed="20170715"/>
		<xsd:attribute name="StructuresVersion" type="an" use="required" fixed="20170715"/>
		<xsd:attribute name="ECLVersion" type="an" use="required" fixed="20170715"/>
		<xsd:attribute name="Version" type="an" use="required" fixed="006"/>
		<xsd:attribute name="Release" type="an" use="required" fixed="001"/>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryOrganizationIDType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryOrganizationIDType">
		<xsd:sequence>
			<xsd:element name="NCPDPID" type="an1..35" minOccurs="0"/>
			<xsd:element name="StateLicenseNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="MedicareNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="MedicaidNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="UPIN" type="an1..35" minOccurs="0"/>
			<xsd:element name="FacilityID" type="an1..35" minOccurs="0"/>
			<xsd:element name="PayerID" type="an1..35" minOccurs="0"/>
			<xsd:element name="DEANumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="HIN" type="an1..35" minOccurs="0"/>
			<xsd:element name="NPI" type="an1..35" minOccurs="0"/>
			<xsd:element name="MutuallyDefined" type="an1..35" minOccurs="0"/>
			<xsd:element name="IHEID" type="an1..64" minOccurs="0"/>
			<xsd:element name="OrganizationID" type="n1..10" minOccurs="0"/>
			<xsd:element name="ParticipantUID" type="an1..35" minOccurs="0"/>
			<xsd:element name="StoreNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="SystemID" type="an1..35" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryOrganizationItemType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryOrganizationItemType">
		<xsd:sequence>
			<xsd:element name="DirectoryInformation" type="DirectoryInformationType"/>
			<xsd:element name="Organization" type="DirectoryOrganizationType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryOrganizationType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryOrganizationType">
		<xsd:sequence>
			<xsd:element name="Identification" type="DirectoryOrganizationIDType" minOccurs="0"/>
			<xsd:element name="OrganizationName" type="an1..100"/>
			<xsd:element name="OrganizationType">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:pattern value="(General)|(Pharmacy)|(Payer)|(System)"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="Address" type="MandatoryAddressType" minOccurs="0"/>
			<xsd:element name="CommunicationNumbers" type="DirectoryCommunicationNumbersType" minOccurs="0"/>
			<xsd:element name="CrossStreet" type="an1..35" minOccurs="0"/>
			<xsd:element name="Description" type="an1..255" minOccurs="0"/>
			<xsd:element name="OperatingStatus" type="OperatingStatusType" minOccurs="0"/>
			<xsd:element name="ParentOrganizationID" type="n1..10" minOccurs="0"/>
			<xsd:element name="PharmacyHoursOfOperation" type="an1..254" minOccurs="0"/>
			<xsd:element name="ReplaceNCPDPID" type="an1..35" minOccurs="0"/>
			<xsd:element name="SupportsNonVeterinarian" type="flagType" minOccurs="0"/>
			<xsd:element name="SupportsVeterinarian" type="flagType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryPortalConfigurationType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryPortalConfigurationType">
		<xsd:sequence>
			<xsd:element name="Name" type="an1..50"/>
			<xsd:element name="Active" type="flagType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryPortalConfigurationsType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryPortalConfigurationsType">
		<xsd:sequence>
			<xsd:element name="Configuration" type="DirectoryPortalConfigurationType" maxOccurs="250"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryPracticeLocationType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryPracticeLocationType">
		<xsd:sequence>
			<xsd:element name="Identification" type="DirectoryPracticeLocationIDType" minOccurs="0"/>
			<xsd:element name="BusinessName" type="an1..70" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryPracticeLocationIDType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryPracticeLocationIDType">
		<xsd:sequence>
			<xsd:element name="OrganizationID" type="n1..10" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryProviderLocationIDType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryProviderLocationIDType">
		<xsd:sequence>
			<xsd:element name="StateLicenseNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="MedicareNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="MedicaidNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="UPIN" type="an1..35" minOccurs="0"/>
			<xsd:element name="DEANumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="HIN" type="an1..35" minOccurs="0"/>
			<xsd:element name="SocialSecurity" type="an1..35" minOccurs="0"/>
			<xsd:element name="NPI" type="an1..35" minOccurs="0"/>
			<xsd:element name="CertificateToPrescribe" type="an1..35" minOccurs="0"/>
			<xsd:element name="Data2000WaiverID" type="an1..35" minOccurs="0"/>
			<xsd:element name="MutuallyDefined" type="an1..35" minOccurs="0"/>
			<xsd:element name="REMSHealthcareProviderEnrollmentID" type="an1..35" minOccurs="0"/>
			<xsd:element name="StateControlSubstanceNumber" type="an1..35" minOccurs="0"/>
			<xsd:element name="SPI" type="n13" minOccurs="0"/>
			<xsd:element name="Veterinarian" type="flagType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryProviderLocationItemType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryProviderLocationItemType">
		<xsd:sequence>
			<xsd:element name="DirectoryInformation" type="DirectoryInformationType"/>
			<xsd:element name="ProviderLocation" type="DirectoryProviderLocationType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectoryProviderLocationType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectoryProviderLocationType">
		<xsd:sequence>
			<xsd:element name="Identification" type="DirectoryProviderLocationIDType"/>
			<xsd:element name="Specialty" type="an1..10" minOccurs="0"/>
			<xsd:element name="PracticeLocation" type="DirectoryPracticeLocationType" minOccurs="0"/>
			<xsd:element name="Name" type="NameType"/>
			<xsd:element name="FormerName" type="NameType" minOccurs="0"/>
			<xsd:element name="Address" type="MandatoryAddressType"/>
			<xsd:element name="CommunicationNumbers" type="DirectoryCommunicationNumbersType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectorySpecialtiesType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectorySpecialtiesType">
		<xsd:sequence>
			<xsd:element name="DirectorySpecialty" type="DirectorySpecialtyType" maxOccurs="50">
				<xsd:annotation>
					<xsd:documentation>Directory Specialty may be expanded to include additional properties, like start/end dates</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  DirectorySpecialtyType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="DirectorySpecialtyType">
		<xsd:sequence>
			<xsd:element name="DirectorySpecialtyName" type="an1..35"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GetOrganization  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GetOrganization">
		<xsd:sequence>
			<xsd:element name="Organization">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Identification">
							<xsd:complexType>
								<xsd:choice>
									<xsd:element name="DEANumber" type="an1..35" minOccurs="0"/>
									<xsd:element name="NCPDPID" type="an1..35" minOccurs="0"/>
									<xsd:element name="NPI" type="an1..35" minOccurs="0"/>
									<xsd:element name="OrganizationID" type="n1..10" minOccurs="0"/>
									<xsd:element name="PayerID" type="an1..35" minOccurs="0"/>
								</xsd:choice>
							</xsd:complexType>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GetOrganizationResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GetOrganizationResponse">
		<xsd:sequence>
			<xsd:element name="GetOrganizationResponseItem" type="DirectoryOrganizationItemType" maxOccurs="250"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GetPortal  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GetPortal">
		<xsd:sequence>
			<xsd:element name="PortalID" type="an1..35"/>
			<xsd:element name="MessageCategory" type="an1..255"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GetPortalResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GetPortalResponse">
		<xsd:sequence>
			<xsd:element name="PortalID" type="an1..35"/>
			<xsd:element name="MessageCategory" type="an1..255"/>
			<xsd:element name="Configurations" type="DirectoryPortalConfigurationsType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GetProviderLocation  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GetProviderLocation">
		<xsd:sequence>
			<xsd:element name="ProviderLocation">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Identification">
							<xsd:complexType>
								<xsd:choice>
									<xsd:element name="DEANumber" type="an1..35" minOccurs="0"/>
									<xsd:element name="NPI" type="an1..35" minOccurs="0"/>
									<xsd:element name="SPI" type="n13" minOccurs="0"/>
								</xsd:choice>
							</xsd:complexType>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  GetProviderLocationResponse  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="GetProviderLocationResponse">
		<xsd:sequence>
			<xsd:element name="GetProviderLocationResponseItem" type="DirectoryProviderLocationItemType" maxOccurs="250"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  UpdatePortal  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="UpdatePortal">
		<xsd:sequence>
			<xsd:element name="PortalID" type="an1..35"/>
			<xsd:element name="MessageCategory" type="an1..255"/>
			<xsd:element name="Configuration" type="DirectoryPortalConfigurationType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  OperatingStatusType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="OperatingStatusType">
		<xsd:sequence>
			<xsd:element name="Status" minOccurs="0">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:pattern value="(Normal)|(Informational)|(Alert)"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="Notes" type="an1..70" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ServiceLevelType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ServiceLevelType">
		<xsd:sequence>
			<xsd:element name="ServiceLevelName" type="an1..50"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  ServiceLevelsType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="ServiceLevelsType">
		<xsd:sequence>
			<xsd:element name="ServiceLevel" type="ServiceLevelType" maxOccurs="50">
				<xsd:annotation>
					<xsd:documentation>ServiceLevel may be expanded to include additional properties, like start/end dates</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  UseCasesType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="UseCasesType">
		<xsd:sequence>
			<xsd:element name="UseCase" type="UseCaseType" maxOccurs="10">
				<xsd:annotation>
					<xsd:documentation>UseCase may be expanded to include additional properties, like start/end dates</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<!--  UseCaseType  -->
	<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
	<xsd:complexType name="UseCaseType">
		<xsd:sequence>
			<xsd:element name="UseCaseCode" type="an1..10"/>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
