const ss_urlmap = {
    directory_tx_staging:
        "https://dir-staging.surescripts.net/directory/Directory6dot1/v6_1?id={message_id}",
    directory_tx_prod:
        "https://dir.surescripts.net/directory/Directory6dot1/v6_1?id={message_id}",
    directory_download_url:
        "https://admin-staging.surescripts.net/Downloads/{directory_filename}",
    tx_staging:
        "https://smr-staging.surescripts.net/erx/EnvoyLabsTest/v6_1?id={message_id}",
    tx_prod: "https://smr.surescripts.net/erx/EnvoyLabs/v6_1?id={message_id}",
};

const ss_xml_template_map = {
    NewRx: "NewRx",
    NewRxRequest: "NewRxRequest",
    RxRenewal: "Prescription Renewal",
    RxChangeRequest: "RxChangeRequest",
    RxChangeResponse: "RxChangeResponse",
    CancelRx: "CancelRx",
    CancelRxResponse: "CancelRxResponse ",
    RxFill: "RxFill",
    RxFillIndicatorChange: "RxFillIndicatorChange",
    NewRxResponseDenied: "NewRxResponseDenied",
    Error: "error",
    Status: "status",
    Verify: "verify",
};
const directory_organization_headers = [
    "NCPDPID",
    "StoreNumber",
    "OrganizationName",
    "AddressLine1",
    "AddressLine2",
    "City",
    "StateProvince",
    "PostalCode",
    "CountryCode",
    "StandardizedAddressLine1",
    "StandardizedAddressLine2",
    "StandardizedCity",
    "StandardizedStateProvince",
    "StandardizedPostal",
    "PrimaryTelephone",
    "Fax",
    "ElectronicMail",
    "AlternatePhoneNumbers",
    "ActiveStartTime",
    "ActiveEndTime",
    "ServiceLevel",
    "PartnerAccount",
    "LastModifiedDate",
    "CrossStreet",
    "RecordChange",
    "OldServiceLevel",
    "Version",
    "NPI",
    "DirectorySpecialtyName",
    "ReplaceNCPDPID",
    "StateLicenseNumber",
    "UPIN",
    "FacilityID",
    "MedicareNumber",
    "MedicaidNumber",
    "PayerID",
    "DEANumber",
    "HIN",
    "MutuallyDefined",
    "DirectAddress",
    "OrganizationType",
    "OrganizationID",
    "ParentOrganizationID",
    "Latitude",
    "Longitude",
    "Precise",
    "UseCase",
];

const directory_provider_headers = [
    "SPI",
    "NPI",
    "DEANumber",
    "StateLicenseNumber",
    "Specialty",
    "Prefix",
    "LastName",
    "FirstName",
    "MiddleName",
    "Suffix",
    "BusinessName",
    "AddressLine1",
    "AddressLine2",
    "City",
    "StateProvince",
    "PostalCode",
    "CountryCode",
    "StandardizedAddressLine1",
    "StandardizedAddressLine2",
    "StandardizedCity",
    "StandardizedStateProvince",
    "StandardizedPostalCode",
    "PrimaryTelephone",
    "Fax",
    "ElectronicMail",
    "AlternatePhoneNumbers",
    "ActiveStartTime",
    "ActiveEndTime",
    "ServiceLevel",
    "PartnerAccount",
    "LastModifiedDate",
    "RecordChange",
    "OldServiceLevel",
    "Version",
    "DirectorySpecialtyName",
    "MedicareNumber",
    "MedicaidNumber",
    "UPIN",
    "CertificateToPrescribe",
    "Data2000WaiverID",
    "REMSHealthCareProviderEnrollmentID",
    "StateControlSubstanceNumber",
    "MutuallyDefined",
    "DirectAddress",
    "UseCases",
    "AvailableRoutes",
    "OrganizationID",
    "Latitude",
    "Longitude",
    "Precise",
];

export {
    ss_urlmap,
    ss_xml_template_map,
    directory_organization_headers,
    directory_provider_headers,
};
