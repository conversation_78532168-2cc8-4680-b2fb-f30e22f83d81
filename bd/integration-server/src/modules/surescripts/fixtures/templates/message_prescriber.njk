{%macro PrescriberMap(Prescriber) %}
{% if Prescriber.NonVeterinarian %}
<NonVeterinarian>
{% if Prescriber.NonVeterinarian.Identification %}
<Identification>
    {% if Prescriber.NonVeterinarian.Identification.StateLicenseNumber %}
    <StateLicenseNumber>{{ Prescriber.NonVeterinarian.Identification.StateLicenseNumber }}</StateLicenseNumber>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Identification.MedicaidNumber %}
    <MedicaidNumber>{{ Prescriber.NonVeterinarian.Identification.MedicaidNumber }}</MedicaidNumber>
    {% endif %}
    <UPIN>0</UPIN>
    {% if Prescriber.NonVeterinarian.Identification.DEANumber %}
    <DEANumber>{{ Prescriber.NonVeterinarian.Identification.DEANumber }}</DEANumber>
    {% endif %}
    <HIN>0</HIN>
    {% if Prescriber.NonVeterinarian.Identification.NPI %}
    <NPI>{{ Prescriber.NonVeterinarian.Identification.NPI }}</NPI>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Identification.CertificateToPrescribe %}
    <CertificateToPrescribe>{{ Prescriber.NonVeterinarian.Identification.CertificateToPrescribe }}</CertificateToPrescribe>
    {% endif %}
</Identification>
{% endif %}
{% if Prescriber.NonVeterinarian.Specialty %}
<Specialty>{{ Prescriber.NonVeterinarian.Specialty }}</Specialty>
{% endif %}
{% if Prescriber.NonVeterinarian.PracticeLocation %}
<PracticeLocation>
    {% if Prescriber.NonVeterinarian.PracticeLocation.BusinessName %}
    <BusinessName>{{ Prescriber.NonVeterinarian.PracticeLocation.BusinessName }}</BusinessName>
    {% endif %}
</PracticeLocation>
{% endif %}
{% if Prescriber.NonVeterinarian.Name %}
<Name>
    {% if Prescriber.NonVeterinarian.Name.LastName %}
    <LastName>{{ Prescriber.NonVeterinarian.Name.LastName }}</LastName>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Name.FirstName %}
    <FirstName>{{ Prescriber.NonVeterinarian.Name.FirstName }}</FirstName>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Name.MiddleName %}
    <MiddleName>{{ Prescriber.NonVeterinarian.Name.MiddleName }}</MiddleName>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Name.Suffix %}
    <Suffix>{{ Prescriber.NonVeterinarian.Name.Suffix }}</Suffix>
    {% endif %}
</Name>
{% endif %}
{% if Prescriber.NonVeterinarian.FormerName %}
<FormerName>
    {% if Prescriber.NonVeterinarian.FormerName.LastName %}
    <LastName>{{ Prescriber.NonVeterinarian.FormerName.LastName }}</LastName>
    {% endif %}
    {% if Prescriber.NonVeterinarian.FormerName.FirstName %}
    <FirstName>{{ Prescriber.NonVeterinarian.FormerName.FirstName }}</FirstName>
    {% endif %}
    {% if Prescriber.NonVeterinarian.FormerName.MiddleName %}
    <MiddleName>{{ Prescriber.NonVeterinarian.FormerName.MiddleName }}</MiddleName>
    {% endif %}
</FormerName>
{% endif %}
{% if Prescriber.NonVeterinarian.Address %}
<Address>
    {% if Prescriber.NonVeterinarian.Address.AddressLine1 %}
    <AddressLine1>{{ Prescriber.NonVeterinarian.Address.AddressLine1 }}</AddressLine1>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Address.AddressLine2 %}
    <AddressLine2>{{ Prescriber.NonVeterinarian.Address.AddressLine2 }}</AddressLine2>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Address.City %}
    <City>{{ Prescriber.NonVeterinarian.Address.City }}</City>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Address.StateProvince %}
    <StateProvince>{{ Prescriber.NonVeterinarian.Address.StateProvince }}</StateProvince>
    {% endif %}
    {% if Prescriber.NonVeterinarian.Address.PostalCode %}
    <PostalCode>{{ Prescriber.NonVeterinarian.Address.PostalCode }}</PostalCode>
    {% endif %}
    <CountryCode>{{ Prescriber.NonVeterinarian.Address.CountryCode | default('US')}}</CountryCode>
</Address>
{% endif %}
{% if Prescriber.NonVeterinarian.CommunicationNumbers %}
<CommunicationNumbers>
    {% if Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone %}
    <PrimaryTelephone>
            {% if Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number %}
        <Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
            {% endif %}
            {% if Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension %}
        <Extension>{{ Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension }}</Extension>
            {% endif %}
    </PrimaryTelephone>
    {% endif %}
    {% if Prescriber.NonVeterinarian.CommunicationNumbers.ElectronicMail %}
    <ElectronicMail>{{ Prescriber.NonVeterinarian.CommunicationNumbers.ElectronicMail }}</ElectronicMail>
    {% endif %}
    {% if Prescriber.NonVeterinarian.CommunicationNumbers.Fax %}
    <Fax>
            {% if Prescriber.NonVeterinarian.CommunicationNumbers.Fax.Number %}
        <Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.Fax.Number }}</Number>
            {% endif %}
    </Fax>
    {% endif %}
    {% if Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone %}
    <HomeTelephone>
            {% if Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.Number %}
        <Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.Number }}</Number>
            {% endif %}
            {% if Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.SupportsSMS %}
        <SupportsSMS>{{ Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.SupportsSMS }}</SupportsSMS>
            {% endif %}
    </HomeTelephone>
    {% endif %}
</CommunicationNumbers>
{% endif %}
</NonVeterinarian>
{% endif %}
{%endmacro%}