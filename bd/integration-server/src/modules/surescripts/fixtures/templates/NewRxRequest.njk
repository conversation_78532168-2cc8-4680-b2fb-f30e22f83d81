{% extends "base.njk" %}
{% block body %}
	<NewRxRequest>
		{% if UrgencyIndicatorCode %}
			<UrgencyIndicatorCode>{{ UrgencyIndicatorCode }}</UrgencyIndicatorCode>
		{% endif %}
		<Patient>
			<HumanPatient>
				<Name>
					<LastName>{{ Patient.HumanPatient.Name.LastName }}</LastName>
					<FirstName>{{ Patient.HumanPatient.Name.FirstName }}</FirstName>
					<MiddleName>{{ Patient.HumanPatient.Name.MiddleName }}</MiddleName>
				</Name>
				<Gender>{{ Patient.HumanPatient.Gender }}</Gender>
				<Address>
					<AddressLine1>{{ Patient.HumanPatient.Address.AddressLine1 }}</AddressLine1>
					<AddressLine2>{{ Patient.HumanPatient.Address.AddressLine2 }}</AddressLine2>
					<City>{{ Patient.HumanPatient.Address.City }}</City>
					<State>{{ Patient.HumanPatient.Address.State }}</State>
					<Zip>{{ Patient.HumanPatient.Address.Zip }}</Zip>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>{{ Patient.HumanPatient.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
					</PrimaryTelephone>
				</CommunicationNumbers>
				<Identification>
					<MedicalRecordIdentificationNumber>{{ Patient.HumanPatient.Identification.MedicalRecordIdentificationNumber }}</MedicalRecordIdentificationNumber>
					<MedicareNumber>{{ Patient.HumanPatient.Identification.MedicareNumber }}</MedicareNumber>
					<MedicaidNumber>{{ Patient.HumanPatient.Identification.MedicaidNumber }}</MedicaidNumber>
					<SocialSecurity>{{ Patient.HumanPatient.Identification.SocialSecurity }}</SocialSecurity>
					<REMSpatientID>{{ Patient.HumanPatient.Identification.REMSpatientID }}</REMSpatientID>
				</Identification>
				<DateOfBirth>
					<Date>{{ Patient.HumanPatient.DateOfBirth.Date }}</Date>
				</DateOfBirth>
			</HumanPatient>
		</Patient>
		<Pharmacy>
			<Identification>
				<NCPDPID>{{ Pharmacy.Identification.NCPDPID }}</NCPDPID>
				<StateLicenseNumber>{{ Pharmacy.Identification.StateLicenseNumber }}</StateLicenseNumber>
				<MedicareNumber>{{ Pharmacy.Identification.MedicareNumber }}</MedicareNumber>
				<MedicaidNumber>{{ Pharmacy.Identification.MedicaidNumber }}</MedicaidNumber>
				<DEANumber>{{ Pharmacy.Identification.DEANumber }}</DEANumber>
				<NPI>{{ Pharmacy.Identification.NPI }}</NPI>
			</Identification>
			<BusinessName>{{ Pharmacy.BusinessName }}</BusinessName>
			<Address>
				<AddressLine1>{{ Pharmacy.Address.AddressLine1 }}</AddressLine1>
				<City>{{ Pharmacy.Address.City }}</City>
				<StateProvince>{{ Pharmacy.Address.StateProvince }}</StateProvince>
				<PostalCode>{{ Pharmacy.Address.PostalCode }}</PostalCode>
				<CountryCode>{{ Pharmacy.Address.CountryCode }}</CountryCode>
			</Address>
			<CommunicationNumbers>
				<PrimaryTelephone>
					<Number>{{ Pharmacy.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
				</PrimaryTelephone>
				<Fax>
					<Number>{{ Pharmacy.CommunicationNumbers.Fax.Number }}</Number>
				</Fax>
			</CommunicationNumbers>
		</Pharmacy>
		<Prescriber>
			<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>{{ Prescriber.NonVeterinarian.Identification.StateLicenseNumber }}</StateLicenseNumber>
					<MedicaidNumber>{{ Prescriber.NonVeterinarian.Identification.MedicaidNumber }}</MedicaidNumber>
					<UPIN>0</UPIN>
					<DEANumber>{{ Prescriber.NonVeterinarian.Identification.DEANumber }}</DEANumber>
					<HIN>0</HIN>
					<NPI>{{ Prescriber.NonVeterinarian.Identification.NPI }}</NPI>
					<CertificateToPrescribe>{{ Prescriber.NonVeterinarian.Identification.CertificateToPrescribe }}</CertificateToPrescribe>
				</Identification>
				<Specialty>{{ Prescriber.NonVeterinarian.Specialty }}</Specialty>
				<PracticeLocation>
					<BusinessName>{{ Prescriber.NonVeterinarian.PracticeLocation.BusinessName }}</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>{{ Prescriber.NonVeterinarian.Name.LastName }}</LastName>
					<FirstName>{{ Prescriber.NonVeterinarian.Name.FirstName }}</FirstName>
					<MiddleName>{{ Prescriber.NonVeterinarian.Name.MiddleName }}</MiddleName>
					<Suffix>{{ Prescriber.NonVeterinarian.Name.Suffix }}</Suffix>
				</Name>
				<FormerName>
					<LastName>{{ Prescriber.NonVeterinarian.FormerName.LastName }}</LastName>
					<FirstName>{{ Prescriber.NonVeterinarian.FormerName.FirstName }}</FirstName>
					<MiddleName>{{ Prescriber.NonVeterinarian.FormerName.MiddleName }}</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>{{ Prescriber.NonVeterinarian.Address.AddressLine1 }}</AddressLine1>
					<AddressLine2>{{ Prescriber.NonVeterinarian.Address.AddressLine2 }}</AddressLine2>
					<City>{{ Prescriber.NonVeterinarian.Address.City }}</City>
					<StateProvince>{{ Prescriber.NonVeterinarian.Address.StateProvince }}</StateProvince>
					<PostalCode>{{ Prescriber.NonVeterinarian.Address.PostalCode }}</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
						<Extension>{{ Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension }}</Extension>
					</PrimaryTelephone>
					<ElectronicMail>{{ Prescriber.NonVeterinarian.CommunicationNumbers.ElectronicMail }}</ElectronicMail>
					<Fax>
						<Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.Fax.Number }}</Number>
					</Fax>
					<HomeTelephone>
						<Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.Number }}</Number>
						<SupportsSMS>{{ Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.SupportsSMS }}</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
			</NonVeterinarian>
		</Prescriber>
		<MedicationRequested>
			<DrugDescription>{{ MedicationRequested.DrugDescription }}</DrugDescription>
			<DrugCoded>
				<ProductCode>
					<Code>{{ MedicationRequested.DrugCoded.ProductCode.Code }}</Code>
					<Qualifier>{{ MedicationRequested.DrugCoded.ProductCode.Qualifier }}</Qualifier>
				</ProductCode>
				<Strength>
					<StrengthValue>{{ MedicationRequested.DrugCoded.Strength.StrengthValue }}</StrengthValue>
					<StrengthForm>
						<Code>{{ MedicationRequested.DrugCoded.Strength.StrengthForm.Code }}</Code>
					</StrengthForm>
					<StrengthUnitOfMeasure>
						<Code>{{ MedicationRequested.DrugCoded.Strength.StrengthUnitOfMeasure.Code }}</Code>
					</StrengthUnitOfMeasure>
				</Strength>
				<DrugDBCode>
					<Code>{{ MedicationRequested.DrugCoded.DrugDBCode.Code }}</Code>
					<Qualifier>{{ MedicationRequested.DrugCoded.DrugDBCode.Qualifier }}</Qualifier>
				</DrugDBCode>
				<DEASchedule>
					<Code>{{ MedicationRequested.DrugCoded.DEASchedule.Code }}</Code>
				</DEASchedule>
			</DrugCoded>
			<Quantity>
				<Value>{{ MedicationRequested.Quantity.Value }}</Value>
				<CodeListQualifier>{{ MedicationRequested.Quantity.CodeListQualifier }}</CodeListQualifier>
				<QuantityUnitOfMeasure>
					<Code>{{ MedicationRequested.Quantity.QuantityUnitOfMeasure.Code }}</Code>
				</QuantityUnitOfMeasure>
			</Quantity>
			<Sig>
				<SigText>{{ MedicationRequested.Sig.SigText }}</SigText>
			</Sig>
			<DaysSupply>{{ MedicationRequested.DaysSupply }}</DaysSupply>
			{% if MedicationRequested.CompoundInformation %}
				<CompoundInformation>
					<FinalCompoundPharmaceuticalDosageForm>{{ MedicationRequested.CompoundInformation.FinalCompoundPharmaceuticalDosageForm }}</FinalCompoundPharmaceuticalDosageForm>
					{% for compound in MedicationRequested.CompoundInformation.CompoundIngredientsLotNotUsed %}
						<CompoundIngredientsLotNotUsed>
							<CompoundIngredient>
								<CompoundIngredientItemDescription>{{ compound.CompoundIngredient.CompoundIngredientItemDescription }}</CompoundIngredientItemDescription>
								<Strength>
									<StrengthValue>{{ compound.CompoundIngredient.Strength.StrengthValue }}</StrengthValue>
									<StrengthForm>
										<Code>{{ compound.CompoundIngredient.Strength.StrengthForm.Code }}</Code>
									</StrengthForm>
									<StrengthUnitOfMeasure>
										<Code>{{ compound.CompoundIngredient.Strength.StrengthUnitOfMeasure.Code }}</Code>
									</StrengthUnitOfMeasure>
								</Strength>
								<Quantity>
									<Value>{{ compound.CompoundIngredient.Quantity.Value }}</Value>
									<CodeListQualifier>{{ compound.CompoundIngredient.Quantity.CodeListQualifier }}</CodeListQualifier>
									<QuantityUnitOfMeasure>
										<Code>{{ compound.CompoundIngredient.Quantity.QuantityUnitOfMeasure.Code }}</Code>
									</QuantityUnitOfMeasure>
								</Quantity>
							</CompoundIngredient>
						</CompoundIngredientsLotNotUsed>
					{% endfor %}
				</CompoundInformation>
			{% endif %}
			<WrittenDate>
				<Date>{{ MedicationRequested.WrittenDate.Date }}</Date>
			</WrittenDate>
			<Substitutions>{{ MedicationRequested.Substitutions }}</Substitutions>
			<ReasonForSubstitutionCodeUsed>{{ MedicationRequested.ReasonForSubstitutionCodeUsed }}</ReasonForSubstitutionCodeUsed>
			<NumberOfRefills>{{ MedicationRequested.NumberOfRefills }}</NumberOfRefills>
			<PriorAuthorization>{{ MedicationRequested.PriorAuthorization }}</PriorAuthorization>
			<PriorAuthorizationStatus>{{ MedicationRequested.PriorAuthorizationStatus }}</PriorAuthorizationStatus>
			<DrugCoverageStatusCode>{{ MedicationRequested.DrugCoverageStatusCode }}</DrugCoverageStatusCode>
			<DoNotFill>{{ MedicationRequested.DoNotFill }}</DoNotFill>
			<Note>{{ MedicationRequested.Note }}</Note>
			<DeliveryRequest>{{ MedicationRequested.DeliveryRequest }}</DeliveryRequest>
			<DeliveryLocation>{{ MedicationRequested.DeliveryLocation }}</DeliveryLocation>
			<FlavoringRequested>{{ MedicationRequested.FlavoringRequested }}</FlavoringRequested>
			<PrescriberCheckedREMS>{{ MedicationRequested.PrescriberCheckedREMS }}</PrescriberCheckedREMS>
			<REMSPatientRiskCategory>{{ MedicationRequested.REMSPatientRiskCategory }}</REMSPatientRiskCategory>
			<REMSAuthorizationNumber>{{ MedicationRequested.REMSAuthorizationNumber }}</REMSAuthorizationNumber>
		</MedicationRequested>
	</NewRxRequest>
{% endblock %}