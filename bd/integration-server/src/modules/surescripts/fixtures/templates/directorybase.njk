<?xml version="1.0" encoding="utf-8"?>
<DirectoryMessage DatatypesVersion="{{ ncpdp_version }}" TransportVersion="{{ ncpdp_version }}" TransactionDomain="DIRECTORY" TransactionVersion="{{ ncpdp_version }}" StructuresVersion="{{ ncpdp_version }}" ECLVersion="{{ ncpdp_version }}" Version="006" Release="001">
{% block message %}
  <Header>
    {% block header %}
    <To Qualifier="ZZZ">{{surescripts_provider_id|default('SSDR61')}}</To>
    <From Qualifier="ZZZ">{{clara_provider_id|default('ENVD61')}}</From>
    <MessageID>{{ message_id | default(gen_mid())}}</MessageID>
    <SentTime>{{ utc_now() }}</SentTime>
    <SenderSoftware>
      <SenderSoftwareDeveloper>{{ software_developer | default ('Envoy Labs')}}</SenderSoftwareDeveloper>
      <SenderSoftwareProduct>{{ software_product | default ('ClaraRX')}}</SenderSoftwareProduct>
      <SenderSoftwareVersionRelease>{{ sofware_version | default ('0.0.1')}}</SenderSoftwareVersionRelease>
    </SenderSoftware>
    {% endblock %}
  </Header>
  <Body>
    {% block body %}
    {% endblock %}
  </Body>
{% endblock -%}
</DirectoryMessage>