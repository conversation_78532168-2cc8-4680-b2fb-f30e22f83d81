{% extends "directorybase.njk" %}
{% block body %}
	<AddOrganization>
		<DirectoryInformation>
			<AccountID>{{surescripts_account_id|default('1100785')}}</AccountID>
			<PortalID>{{surescripts_partner_id|default('1315533')}}</PortalID>
			<ActiveStartTime>{{active_start_time|default(utc_now())}}</ActiveStartTime>
			<ActiveEndTime>{{active_end_time|default('2035-01-01T00:00:00.89Z')}}</ActiveEndTime>
			<DirectorySpecialties>
				{% if organization_specialties is defined %}
					{% for sp in organization_specialties %}
						<DirectorySpecialty>
							<DirectorySpecialtyName>{{sp}}</DirectorySpecialtyName>
						</DirectorySpecialty>
					{% endfor %}
				{% endif %}
			</DirectorySpecialties>
			<ServiceLevels>
				{% if organization_servicelevels is defined %}
					{% for sl in organization_servicelevels %}
						<ServiceLevel>
							<ServiceLevelName>{{sl}}</ServiceLevelName>
						</ServiceLevel>
					{% endfor %}
				{% endif %}
			</ServiceLevels>
		</DirectoryInformation>
		<Organization>
			<Identification>
				{% if organization_ncpdp_id %}
					<NCPDPID>{{organization_ncpdp_id}}</NCPDPID>
				{% endif %}
				{% if organization_dea_number %}
					<DEANumber>{{organization_dea_number}}</DEANumber>
				{% endif %}
				{% if organization_npi %}
					<NPI>{{organization_npi}}</NPI>
				{% endif %}
			</Identification>
			<OrganizationName>{{organization_name}}</OrganizationName>
			<OrganizationType>{{organization_type|default('Pharmacy')}}</OrganizationType>
			<Address>
				<AddressLine1>{{organization_address}}</AddressLine1>
				<City>{{organization_city}}</City>
				<StateProvince>{{organization_state}}</StateProvince>
				<PostalCode>{{organization_zipcode}}</PostalCode>
				<CountryCode>{{organization_country|default('US')}}</CountryCode>
			</Address>
			<CommunicationNumbers>
				<PrimaryTelephone>
					<Number>{{organization_phone}}</Number>
				</PrimaryTelephone>
				<Fax>
					<Number>{{organization_fax}}</Number>
				</Fax>
			</CommunicationNumbers>
			{% if organization_cross_street is defined %}
				<CrossStreet>{{organization_cross_street}}</CrossStreet>
			{% endif %}
			{% if organization_hoop is defined %}
				<PharmacyHoursOfOperation>{{organization_hoop}}</PharmacyHoursOfOperation>
			{% endif %}
			<SupportsNonVeterinarian>{{organization_nonveterinarian|default('Y')}}</SupportsNonVeterinarian>
			<SupportsVeterinarian>{{organization_veterinarian|default('N')}}</SupportsVeterinarian>
		</Organization>
	</AddOrganization>
{% endblock %}