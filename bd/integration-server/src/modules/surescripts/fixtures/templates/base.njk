<?xml version="1.0" encoding="utf-8"?>
<Message DatatypesVersion="{{ ncpdp_version }}" TransportVersion="{{ ncpdp_version }}" TransactionDomain="SCRIPT" TransactionVersion="{{ ncpdp_version }}" StructuresVersion="{{ ncpdp_version }}" ECLVersion="{{ ncpdp_version }}">
{% block message %}
  <Header>
    {% block header %}
      <To Qualifier="{{ header.ncpdp_to_type|default('D') }}">{%- if override_spi %}{{ override_spi }}{% else %}{{ header.To }}{% endif %}</To>
      <From Qualifier="{{ header.ncpdp_from_type|default('P') }}">{{ header.From }}</From>
      <MessageID>{{ header.message_id }}</MessageID>
      {% if header.RelatesToMessageID %}
        <RelatesToMessageID>{{ header.RelatesToMessageID }}</RelatesToMessageID>
      {% endif %}
      <SentTime>{{ sent_dt|default(utc_now()) }}</SentTime>
      <SenderSoftware>
        <SenderSoftwareDeveloper>{{ software_developer | default ('Envoy Labs')}}</SenderSoftwareDeveloper>
        <SenderSoftwareProduct>{{ software_product | default ('ClaraRX')}}</SenderSoftwareProduct>
        <SenderSoftwareVersionRelease>{{ sofware_version | default ('0.0.1')}}</SenderSoftwareVersionRelease>
      </SenderSoftware>
      {% if header.RxReferenceNumber is defined %}
        <RxReferenceNumber>{{header.RxReferenceNumber}}</RxReferenceNumber>
      {% endif %}
      {% if header.PrescriberOrderNumber is defined %}
        <PrescriberOrderNumber>{{header.PrescriberOrderNumber}}</PrescriberOrderNumber>
      {% endif %}
      {% if DigitalSignatureIndicator %}
      <DigitalSignature Version="{{digital_signature_version|default('1.1')}}">
        <DigitalSignatureIndicator>{{header.DigitalSignatureIndicator}}</DigitalSignatureIndicator>
      </DigitalSignature>
      {% endif %}
    {% endblock %}
  </Header>
  <Body>
    {% block body %}{% endblock %}
  </Body>
{% endblock -%}
</Message>