{% extends "base.njk" %}
{% from "message_prescriber.njk" import PrescriberMap %}
{% from "message_medication.njk" import MedicationMap %}

{% block body %}
<{{message_type}}>
{% if MessageRequestCode %}
	<MessageRequestCode>{{MessageRequestCode}}</MessageRequestCode>
{% endif %}
{% if MessageRequestSubCode %}
	{% for subcode in MessageRequestSubCode %}
		<MessageRequestSubCode>{{subcode}}</MessageRequestSubCode>
	{% endfor %}
{% endif %}
{% if ReturnReceipt %}
	<ReturnReceipt>{{ ReturnReceipt }}</ReturnReceipt>
{% endif %}
{% if UrgencyIndicatorCode %}
	<UrgencyIndicatorCode>{{ UrgencyIndicatorCode }}</UrgencyIndicatorCode>
{% endif %}
{%if Patient %}
	<Patient>
		{% include "message_patient.njk" %}
	</Patient>
{% endif %}
{% if Pharmacy %}
<Pharmacy>
	{% include "message_pharmacy.njk" %}
</Pharmacy>
{% endif %}
{% if Prescriber %}
<Prescriber>
	{{PrescriberMap(Prescriber)}}
</Prescriber>
{% endif %}
{% if observations %}
	<Observation>
		{% include "message_observations.njk" %}
	</Observation>
{% endif %}
{% if (MedicationPrescribed) and (message_type != 'RxRenewalRequest') %}
	<MedicationPrescribed>
		{{ MedicationMap(MedicationPrescribed) }}
	</MedicationPrescribed>
{% endif %}
{% if MedicationDispensed %}
	<MedicationDispensed>
		{{ MedicationMap(MedicationDispensed) }}
	</MedicationDispensed>
{% endif %}
{% if MedicationRequested %}
	<MedicationRequested>
		{{ MedicationMap(MedicationRequested) }}
	</MedicationRequested>
{% endif %}
{% if Supervisor %}
	<Supervisor>
		{{PrescriberMap(Supervisor)}}
	</Supervisor>
{% endif %}
{% if ProhibitRenewalRequest %}
	<ProhibitRenewalRequest>{{ ProhibitRenewalRequest }}</ProhibitRenewalRequest>
{% endif %}
{% if ChangeReasonText %}
	<ChangeReasonText>{{ ChangeReasonText }}</ChangeReasonText>
{% endif %}
{% if FollowUpPrescriber %}
	<FollowUpPrescriber>
		{{PrescriberMap(FollowUpPrescriber)}}
	</FollowUpPrescriber>
{% endif %}
{% if Response %}
<Response>
{% if Response.Approved %}
    <Approved>
    {% if Response.Approved.ReasonCode %}
        <ReasonCode>{{ Response.Approved.ReasonCode }}</ReasonCode>
    {% endif %}
    {% if Response.Approved.ReferenceNumber %}
        <ReferenceNumber>{{ Response.Approved.ReferenceNumber }}</ReferenceNumber>
    {% endif %}
    {% if Response.Approved.Note %}
        <Note>{{ Response.Approved.Note }}</Note>
    {% endif %}
    </Approved>
{% endif %}
{% if Response.Denied %}
    <Denied>
    {% if Response.Denied.ReasonCode %}
        <ReasonCode>{{ Response.Denied.ReasonCode }}</ReasonCode>
    {% endif %}
    {% if Response.Denied.ReferenceNumber %}
        <ReferenceNumber>{{ Response.Denied.ReferenceNumber }}</ReferenceNumber>
    {% endif %}
    {% if Response.Denied.DenialReason %}
        <DenialReason>{{ Response.Denied.DenialReason }}</DenialReason>
    {% endif %}
    {% if Response.Denied.Pharmacy %}
        <Pharmacy>{{ Response.Denied.Pharmacy }}</Pharmacy>
    {% endif %}
    </Denied>
{% endif %}
{% if Response.DeniedNewPrescriptionToFollow %}
    <DeniedNewPrescriptionToFollow>{{ Response.DeniedNewPrescriptionToFollow }}</DeniedNewPrescriptionToFollow>
{% endif %}
{% if Response.ApprovedWithChanges %}
    <ApprovedWithChanges>{{ Response.ApprovedWithChanges }}</ApprovedWithChanges>
{% endif %}
{% if Response.Replace %}
    <Replace>{{ Response.Replace }}</Replace>
{% endif %}
</Response>
{% endif %}
</{{message_type}}>
{% endblock %}