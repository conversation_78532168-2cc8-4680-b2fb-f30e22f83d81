{% extends "base.njk" %}
{% block body %}
    <{{message_type}}>
        {% if priority_flag %}
            <UrgencyIndicatorCode>{{ priority_flag }}</UrgencyIndicatorCode>
        {% endif %}
        {% if AllergyOrAdverseEvent %}
            <AllergyOrAdverseEvent>
                {% if AllergyOrAdverseEvent %}
                    <AllergyOrAdverseEvent>
                        {% for Allergy in AllergyOrAdverseEvent.Allergies %}
                            <Allergies>
                                <SourceOfInformation>{{ Allergy.SourceOfInformation }}</SourceOfInformation>
                                <EffectiveDate>{{ Allergy.EffectiveDate }}</EffectiveDate>
                                <ExpirationDate>{{ Allergy.ExpirationDate }}</ExpirationDate>
                                <AdverseEvent>
                                    {% if Allergy.AdverseEvent.Text %}
                                        <Text>{{ Allergy.AdverseEvent.Text }}</Text>
                                    {% endif %}
                                    {% if Allergy.AdverseEvent.Code %}
                                        <Code>{{ Allergy.AdverseEvent.Code }}</Code>
                                    {% endif %}
                                </AdverseEvent>
                                <DrugProductCoded>
                                    <Code>{{ Allergy.DrugProductCoded.Code }}</Code>
                                    <Qualifier>{{ Allergy.DrugProductCoded.Qualifier }}</Qualifier>
                                    <Text>{{ Allergy.DrugProductCoded.Text }}</Text>
                                </DrugProductCoded>
                                <ReactionCoded>
                                    <Text>{{ Allergy.ReactionCoded.Text }}</Text>
                                    <Code>{{ Allergy.ReactionCoded.Code }}</Code>
                                </ReactionCoded>
                                <SeverityCoded>
                                    <Text>{{ Allergy.SeverityCoded.Text }}</Text>
                                    <Code>{{ Allergy.SeverityCoded.Code }}</Code>
                                </SeverityCoded>
                            </Allergies>
                        {% endfor %}
                    </AllergyOrAdverseEvent>
                {% endif %}
                {% if BenefitsCoordination %}
                    {% for Benefit in BenefitsCoordination %}
                        <BenefitsCoordination>
                            <PayerIdentification>
                                <PayerID>{{ Benefit.PayerIdentification.PayerID }}</PayerID>
                                <IINNumber>{{ Benefit.PayerIdentification.IINNumber }}</IINNumber>
                                <ProcessorIdentificationNumber>{{ Benefit.PayerIdentification.ProcessorIdentificationNumber }}</ProcessorIdentificationNumber>
                                <NAICCode>{{ Benefit.PayerIdentification.NAICCode }}</NAICCode>
                                <StandardUniqueHealthPlanIdentifier>{{ Benefit.PayerIdentification.StandardUniqueHealthPlanIdentifier }}</StandardUniqueHealthPlanIdentifier>
                            </PayerIdentification>
                            <PayerName>{{ Benefit.PayerName }}</PayerName>
                            <CardholderID>{{ Benefit.CardholderID }}</CardholderID>
                            <CardHolderName>
                                <FirstName>{{ Benefit.CardHolderName.FirstName }}</FirstName>
                                <LastName>{{ Benefit.CardHolderName.LastName }}</LastName>
                            </CardHolderName>
                            <GroupID>{{ Benefit.GroupID }}</GroupID>
                            <GroupName>{{ Benefit.GroupName }}</GroupName>
                            <PBMMemberID>{{ Benefit.PBMMemberID }}</PBMMemberID>
                            <PayerType>{{ Benefit.PayerType }}</PayerType>
                            <PayerResponsibilityCode>{{ Benefit.PayerResponsibilityCode }}</PayerResponsibilityCode>
                            <PatientRelationship>{{ Benefit.PatientRelationship }}</PatientRelationship>
                            <PersonCode>{{ Benefit.PersonCode }}</PersonCode>
                            <Address>{{ Benefit.Address }}</Address>
                            <CommunicationNumbers>
                                <PrimaryTelephone>
                                    <Number>{{ Benefit.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
                                </PrimaryTelephone>
                                <Fax>
                                    <Number>{{ Benefit.CommunicationNumbers.Fax.Number }}</Number>
                                </Fax>
                            </CommunicationNumbers>
                            <Consent>{{ Benefit.Consent }}</Consent>
                        </BenefitsCoordination>
                    {% endfor %}
                {% endif %}
                <Patient>
                    <HumanPatient>
                        <Name>
                            <LastName>{{ Patient.HumanPatient.Name.LastName }}</LastName>
                            <FirstName>{{ Patient.HumanPatient.Name.FirstName }}</FirstName>
                            <MiddleName>{{ Patient.HumanPatient.Name.MiddleName }}</MiddleName>
                        </Name>
                        <Gender>{{ Patient.HumanPatient.Gender }}</Gender>
                        <Address>
                            <AddressLine1>{{ Patient.HumanPatient.Address.AddressLine1 }}</AddressLine1>
                            <AddressLine2>{{ Patient.HumanPatient.Address.AddressLine2 }}</AddressLine2>
                            <City>{{ Patient.HumanPatient.Address.City }}</City>
                            <State>{{ Patient.HumanPatient.Address.State }}</State>
                            <Zip>{{ Patient.HumanPatient.Address.Zip }}</Zip>
                        </Address>
                        <CommunicationNumbers>
                            <PrimaryTelephone>
                                <Number>{{ Patient.HumanPatient.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
                            </PrimaryTelephone>
                        </CommunicationNumbers>
                        <Identification>
                            <MedicalRecordIdentificationNumber>{{ Patient.HumanPatient.Identification.MedicalRecordIdentificationNumber }}</MedicalRecordIdentificationNumber>
                            <MedicareNumber>{{ Patient.HumanPatient.Identification.MedicareNumber }}</MedicareNumber>
                            <MedicaidNumber>{{ Patient.HumanPatient.Identification.MedicaidNumber }}</MedicaidNumber>
                            <SocialSecurity>{{ Patient.HumanPatient.Identification.SocialSecurity }}</SocialSecurity>
                            <REMSpatientID>{{ Patient.HumanPatient.Identification.REMSpatientID }}</REMSpatientID>
                        </Identification>
                        <DateOfBirth>
                            <Date>{{ Patient.HumanPatient.DateOfBirth.Date }}</Date>
                        </DateOfBirth>
                    </HumanPatient>
                </Patient>
                <Pharmacy>
                    <Identification>
                        <NCPDPID>{{ Pharmacy.Identification.NCPDPID }}</NCPDPID>
                        <StateLicenseNumber>{{ Pharmacy.Identification.StateLicenseNumber }}</StateLicenseNumber>
                        <MedicareNumber>{{ Pharmacy.Identification.MedicareNumber }}</MedicareNumber>
                        <MedicaidNumber>{{ Pharmacy.Identification.MedicaidNumber }}</MedicaidNumber>
                        <DEANumber>{{ Pharmacy.Identification.DEANumber }}</DEANumber>
                        <NPI>{{ Pharmacy.Identification.NPI }}</NPI>
                    </Identification>
                    <BusinessName>{{ Pharmacy.BusinessName }}</BusinessName>
                    <Address>
                        <AddressLine1>{{ Pharmacy.Address.AddressLine1 }}</AddressLine1>
                        <City>{{ Pharmacy.Address.City }}</City>
                        <StateProvince>{{ Pharmacy.Address.StateProvince }}</StateProvince>
                        <PostalCode>{{ Pharmacy.Address.PostalCode }}</PostalCode>
                        <CountryCode>{{ Pharmacy.Address.CountryCode }}</CountryCode>
                    </Address>
                    <CommunicationNumbers>
                        <PrimaryTelephone>
                            <Number>{{ Pharmacy.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
                        </PrimaryTelephone>
                        <Fax>
                            <Number>{{ Pharmacy.CommunicationNumbers.Fax.Number }}</Number>
                        </Fax>
                    </CommunicationNumbers>
                    
                </Pharmacy>
                <Prescriber>
                    <NonVeterinarian>
                        <Identification>
                            <StateLicenseNumber>{{ Prescriber.NonVeterinarian.Identification.StateLicenseNumber }}</StateLicenseNumber>
                            <MedicaidNumber>{{ Prescriber.NonVeterinarian.Identification.MedicaidNumber }}</MedicaidNumber>
                            <UPIN>0</UPIN>
                            <DEANumber>{{ Prescriber.NonVeterinarian.Identification.DEANumber }}</DEANumber>
                            <HIN>0</HIN>
                            <NPI>{{ Prescriber.NonVeterinarian.Identification.NPI }}</NPI>
                            <CertificateToPrescribe>{{ Prescriber.NonVeterinarian.Identification.CertificateToPrescribe }}</CertificateToPrescribe>
                        </Identification>
                        <Specialty>{{ Prescriber.NonVeterinarian.Specialty }}</Specialty>
                        <PracticeLocation>
                            <BusinessName>{{ Prescriber.NonVeterinarian.PracticeLocation.BusinessName }}</BusinessName>
                        </PracticeLocation>
                        <Name>
                            <LastName>{{ Prescriber.NonVeterinarian.Name.LastName }}</LastName>
                            <FirstName>{{ Prescriber.NonVeterinarian.Name.FirstName }}</FirstName>
                            <MiddleName>{{ Prescriber.NonVeterinarian.Name.MiddleName }}</MiddleName>
                            <Suffix>{{ Prescriber.NonVeterinarian.Name.Suffix }}</Suffix>
                        </Name>
                        <FormerName>
                            <LastName>{{ Prescriber.NonVeterinarian.FormerName.LastName }}</LastName>
                            <FirstName>{{ Prescriber.NonVeterinarian.FormerName.FirstName }}</FirstName>
                            <MiddleName>{{ Prescriber.NonVeterinarian.FormerName.MiddleName }}</MiddleName>
                        </FormerName>
                        <Address>
                            <AddressLine1>{{ Prescriber.NonVeterinarian.Address.AddressLine1 }}</AddressLine1>
                            <AddressLine2>{{ Prescriber.NonVeterinarian.Address.AddressLine2 }}</AddressLine2>
                            <City>{{ Prescriber.NonVeterinarian.Address.City }}</City>
                            <StateProvince>{{ Prescriber.NonVeterinarian.Address.StateProvince }}</StateProvince>
                            <PostalCode>{{ Prescriber.NonVeterinarian.Address.PostalCode }}</PostalCode>
                            <CountryCode>US</CountryCode>
                        </Address>
                        <CommunicationNumbers>
                            <PrimaryTelephone>
                                <Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
                                <Extension>{{ Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Extension }}</Extension>
                            </PrimaryTelephone>
                            <ElectronicMail>{{ Prescriber.NonVeterinarian.CommunicationNumbers.ElectronicMail }}</ElectronicMail>
                            <Fax>
                                <Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.Fax.Number }}</Number>
                            </Fax>
                            <HomeTelephone>
                                <Number>{{ Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.Number }}</Number>
                                <SupportsSMS>{{ Prescriber.NonVeterinarian.CommunicationNumbers.HomeTelephone.SupportsSMS }}</SupportsSMS>
                            </HomeTelephone>
                        </CommunicationNumbers>
                    </NonVeterinarian>
                </Prescriber>
                {% if observations %}
                    <Observation>
                        {% for observation in observations %}
                            <Measurement>
                                <VitalSign>{{ observation.type_id }}</VitalSign>
                                <LOINCVersion>{{ observation.loinc_version }}</LOINCVersion>
                                <Value>{{ observation.value }}</Value>
                                <UnitOfMeasure>{{ observation.unit_id }}</UnitOfMeasure>
                                <UCUMVersion>{{ observation.ucum_version }}</UCUMVersion>
                                <ObservationDate>
                                    <Date>{{ observation.observation_date }}</Date>
                                </ObservationDate>
                                {% if observation.notes %}
                                    <ObservationNotes>{{ observation.notes }}</ObservationNotes>
                                {% endif %}
                            </Measurement>
                        {% endfor %}
                    </Observation>
                {% endif %}
                <MedicationRequested>
                    <DrugDescription>{{ description }}</DrugDescription>
                    <DrugCoded>
                        <ProductCode>
                            <Code>{{ product_code }}</Code>
                            <Qualifier>{{ product_code_qualifier_id }}</Qualifier>
                        </ProductCode>
                        <Strength>
                            <StrengthValue>{{ strength }}</StrengthValue>
                            <StrengthForm>
                                <Code>{{ strength_form_id }}</Code>
                            </StrengthForm>
                            <StrengthUnitOfMeasure>
                                <Code>{{ strength_uom_id }}</Code>
                            </StrengthUnitOfMeasure>
                        </Strength>
                        <DrugDBCode>
                            <Code>{{ drug_db_code }}</Code>
                            <Qualifier>{{ drug_db_qualifier_id }}</Qualifier>
                        </DrugDBCode>
                    </DrugCoded>
                    <Quantity>
                        <Value>{{ quantity }}</Value>
                        <CodeListQualifier>{{ quantity_qualifier_id }}</CodeListQualifier>
                        <QuantityUnitOfMeasure>
                            <Code>{{ quantity_uom_id }}</Code>
                        </QuantityUnitOfMeasure>
                    </Quantity>
                    <Sig>
                        <SigText>{{ sig }}</SigText>
                    </Sig>
                </MedicationRequested>
            </{{message_type}}>
        {% endblock %}