<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Header>
		<To Qualifier="D">6301875277001</To>
		<From Qualifier="P">7447853</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<RelatesToMessageID>echo from NEWRX</RelatesToMessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<RxReferenceNumber>QA sample scrubbed from Prod OS1</RxReferenceNumber>
		<PrescriberOrderNumber>COPY FROM NEWRX</PrescriberOrderNumber>
	</Header>
	<Body>
		<RxChangeRequest>
			<MessageRequestCode>OS</MessageRequestCode>
			<Patient>            	
				<HumanPatient>
					<Name>
						<LastName>Acacianna</LastName>
						<FirstName>Rowena</FirstName>
						<MiddleName>Baylie</MiddleName>						
					</Name>
					<Gender>F</Gender>
					<DateOfBirth>
						<Date>1968-03-29</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>2798 Parsifal St NE</AddressLine1>
						<City>Albuquerque</City>
						<StateProvince>NM</StateProvince>
						<PostalCode>87112</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
						</PrimaryTelephone>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>2122548</NCPDPID>
					<StateLicenseNumber>MD-6218-96551</StateLicenseNumber>
					<MedicareNumber>365476</MedicareNumber>
					<MedicaidNumber>365476</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Bannockburn Pharmacy</BusinessName>
				<Address>
					<AddressLine1>6798 Pyle Rd</AddressLine1>
					<City>Bethesda</City>
					<StateProvince>MD</StateProvince>
					<PostalCode>20817</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>DC121_123_933</StateLicenseNumber>
					<MedicareNumber>154745</MedicareNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP-DC-123.933</CertificateToPrescribe>
					<Data2000WaiverID>*********</Data2000WaiverID>
					<REMSHealthcareProviderEnrollmentID>4521445-TM-457</REMSHealthcareProviderEnrollmentID>
				</Identification>
				<Name>
					<LastName>McTavish</LastName>
					<FirstName>Tarquin</FirstName>
					<Suffix>MD</Suffix>
					<Prefix>MR</Prefix>
				</Name>
				<Address>
					<AddressLine1>1818 Clydesdale Place NW</AddressLine1>
					<AddressLine2># 2210</AddressLine2>
					<City>Washington</City>
					<StateProvince>DC</StateProvince>
					<PostalCode>20009</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- height -->
					<VitalSign>8302-2</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>68</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-05-20</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>135</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-05-23</Date>
					</ObservationDate>
				</Measurement>
			</Observation>	
			<MedicationPrescribed>
                <DrugDescription>AZITHROMYCIN 1 GM PWD PACKET</DrugDescription>
                <DrugCoded>
                    <ProductCode>
                        <Code>59762305102</Code>
                        <Qualifier>ND</Qualifier>
                    </ProductCode>
                    <DrugDBCode>
                        <Code>861416</Code>
                        <Qualifier>SCD</Qualifier>
                    </DrugDBCode>
                </DrugCoded>
                <Quantity>
                    <Value>3</Value>
                    <CodeListQualifier>38</CodeListQualifier>
                    <QuantityUnitOfMeasure>
                        <Code>C48521</Code>
                    </QuantityUnitOfMeasure>
                </Quantity>
                <DaysSupply>1</DaysSupply>
                <WrittenDate>
                    <Date>2022-11-09</Date>
                </WrittenDate>
                <LastFillDate>
                    <Date>2022-11-09</Date>
                </LastFillDate>
                <Substitutions>0</Substitutions>
                <NumberOfRefills>0</NumberOfRefills>
                <Note>Product Backordered/Unavailable</Note>
                <Sig>
                    <SigText>TAKE 1 PACKET BY MOUTH ONCE A DAY</SigText>
                </Sig>
            </MedicationPrescribed>
            <MedicationRequested>
                <DrugDescription>AZITHROMYCIN 1 GM PWD PACKET</DrugDescription>
                <DrugCoded>
                    <ProductCode>
                        <Code>59762305102</Code>
                        <Qualifier>ND</Qualifier>
                    </ProductCode>
                    <DrugDBCode>
                        <Code>861416</Code>
                        <Qualifier>SCD</Qualifier>
                    </DrugDBCode>
                </DrugCoded>
                <Quantity>
                    <Value>3</Value>
                    <CodeListQualifier>38</CodeListQualifier>
                    <QuantityUnitOfMeasure>
                        <Code>C48521</Code>
                    </QuantityUnitOfMeasure>
                </Quantity>
                <DaysSupply>1</DaysSupply>
                <WrittenDate>
                    <Date>2022-11-09</Date>
                </WrittenDate>
                <LastFillDate>
                    <Date>2022-11-09</Date>
                </LastFillDate>
                <Substitutions>0</Substitutions>
                <NumberOfRefills>0</NumberOfRefills>
                <Note>Product Backordered/Unavailable</Note>
                <Sig>
                    <SigText>TAKE 1 PACKET BY MOUTH ONCE A DAY</SigText>
                </Sig>
            </MedicationRequested>
        </RxChangeRequest>
    </Body>
</Message>
