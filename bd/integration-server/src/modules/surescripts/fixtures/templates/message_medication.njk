{%macro MedicationMap(Medication) %}
    {% if Medication.DrugDescription %}
        <DrugDescription>{{ Medication.DrugDescription }}</DrugDescription>
    {% endif %}
    {% if Medication.DrugCoded %}
        <DrugCoded>
            {% if Medication.DrugCoded.ProductCode %}
                <ProductCode>
                    {% if Medication.DrugCoded.ProductCode.Code %}
                        <Code>{{ Medication.DrugCoded.ProductCode.Code }}</Code>
                    {% endif %}
                    {% if Medication.DrugCoded.ProductCode.Qualifier %}
                        <Qualifier>{{ Medication.DrugCoded.ProductCode.Qualifier }}</Qualifier>
                    {% endif %}
                </ProductCode>
            {% endif %}
            {% if Medication.DrugCoded.Strength is defined %}
                <Strength>
                    {% if Medication.DrugCoded.Strength.StrengthValue %}
                        <StrengthValue>{{ Medication.DrugCoded.Strength.StrengthValue }}</StrengthValue>
                    {% endif %}
                    {% if Medication.DrugCoded.Strength.StrengthForm %}
                        <StrengthForm>
                            {% if Medication.DrugCoded.Strength.StrengthForm.Code %}
                                <Code>{{ Medication.DrugCoded.Strength.StrengthForm.Code }}</Code>
                            {% endif %}
                        </StrengthForm>
                    {% endif %}
                    {% if Medication.DrugCoded.Strength.StrengthUnitOfMeasure %}
                        <StrengthUnitOfMeasure>
                            {% if Medication.DrugCoded.Strength.StrengthUnitOfMeasure.Code %}
                                <Code>{{ Medication.DrugCoded.Strength.StrengthUnitOfMeasure.Code }}</Code>
                            {% endif %}
                        </StrengthUnitOfMeasure>
                    {% endif %}
                </Strength>
            {% endif %}
            {% if Medication.DrugCoded.DrugDBCode %}
                <DrugDBCode>
                    {% if Medication.DrugCoded.DrugDBCode.Code %}
                        <Code>{{ Medication.DrugCoded.DrugDBCode.Code }}</Code>
                    {% endif %}
                    {% if Medication.DrugCoded.DrugDBCode.Qualifier %}
                        <Qualifier>{{ Medication.DrugCoded.DrugDBCode.Qualifier }}</Qualifier>
                    {% endif %}
                </DrugDBCode>
            {% endif %}
            {% if Medication.DrugCoded.DEASchedule %}
                <DEASchedule>
                    {% if Medication.DrugCoded.DEASchedule.Code %}
                        <Code>{{ Medication.DrugCoded.DEASchedule.Code }}</Code>
                    {% endif %}
                    {% if Medication.DrugCoded.DEASchedule.Qualifier %}
                        <Qualifier>{{ Medication.DrugCoded.DEASchedule.Qualifier }}</Qualifier>
                    {% endif %}
                </DEASchedule>
            {% endif %}
        </DrugCoded>
    {% endif %}
    {% if Medication.Quantity %}
        <Quantity>
            {% if Medication.Quantity.Value %}
                <Value>{{ Medication.Quantity.Value }}</Value>
            {% endif %}
            {% if Medication.Quantity.CodeListQualifier %}
                <CodeListQualifier>{{ Medication.Quantity.CodeListQualifier }}</CodeListQualifier>
            {% endif %}
            {% if Medication.Quantity.QuantityUnitOfMeasure %}
                <QuantityUnitOfMeasure>
                    {% if Medication.Quantity.QuantityUnitOfMeasure.Code %}
                        <Code>{{ Medication.Quantity.QuantityUnitOfMeasure.Code }}</Code>
                    {% endif %}
                </QuantityUnitOfMeasure>
            {% endif %}
        </Quantity>
    {% endif %}
    {% if Medication.DaysSupply is defined %}
        <DaysSupply>{{ Medication.DaysSupply }}</DaysSupply>
    {% endif %}
    {% if Medication.WrittenDate %}
        <WrittenDate>
            {% if Medication.WrittenDate.Date %}
                <Date>{{ ss_fmt_dt(Medication.WrittenDate.Date, 'YYYY-MM-DD') }}</Date>
            {% endif %}
        </WrittenDate>
    {% endif %}
    {% if Medication.LastFillDate %}
        <LastFillDate>
            {% if Medication.LastFillDate.Date %}
                <Date>{{ ss_fmt_dt(Medication.LastFillDate.Date , 'YYYY-MM-DD') }}</Date>
            {% endif %}
        </LastFillDate>
    {% endif %}
    {% if Medication.Substitutions %}
        <Substitutions>{{Medication.Substitutions}}</Substitutions>
    {% endif %}
    {% if Medication.NumberOfRefills is defined %}
        <NumberOfRefills>{{ Medication.NumberOfRefills }}</NumberOfRefills>
    {% endif %}
    {% if Medication.Diagnosis %}
        <Diagnosis>
            {% if Medication.Diagnosis.ClinicalInformationQualifier %}
                <ClinicalInformationQualifier>{{ Medication.Diagnosis.ClinicalInformationQualifier }}</ClinicalInformationQualifier>
            {% endif %}
            {% if Medication.Diagnosis.Primary %}
                <Primary>
                    {% if Medication.Diagnosis.Primary.Code %}
                        <Code>{{ Medication.Diagnosis.Primary.Code }}</Code>
                    {% endif %}
                    {% if Medication.Diagnosis.Primary.Qualifier %}
                        <Qualifier>{{ Medication.Diagnosis.Primary.Qualifier }}</Qualifier>
                    {% endif %}
                    {% if Medication.Diagnosis.Primary.Description %}
                        <Description>{{ Medication.Diagnosis.Primary.Description }}</Description>
                    {% endif %}
                </Primary>
            {% endif %}
            {% if Medication.Diagnosis.Secondary %}
                <Secondary>
                    {% if Medication.Diagnosis.Secondary.Code %}
                        <Code>{{ Medication.Diagnosis.Secondary.Code }}</Code>
                    {% endif %}
                    {% if Medication.Diagnosis.Secondary.Qualifier %}
                        <Qualifier>{{ Medication.Diagnosis.Secondary.Qualifier }}</Qualifier>
                    {% endif %}
                    {% if Medication.Diagnosis.Secondary.Description %}
                        <Description>{{ Medication.Diagnosis.Secondary.Description }}</Description>
                    {% endif %}
                </Secondary>
            {% endif %}
        </Diagnosis>
    {% endif %}
    {% if Medication.PriorAuthorization %}
        <PriorAuthorization>{{ Medication.PriorAuthorization }}</PriorAuthorization>
    {% endif %}
    {% if Medication.Sig %}
        <Sig>
            {% if Medication.Sig.SigText %}
                <SigText>{{ Medication.Sig.SigText }}</SigText>
            {% endif %}
            {% if Medication.Sig.CodeSystem %}
                <CodeSystem>
                    {% if Medication.Sig.CodeSystem.SNOMEDVersion %}
                        <SNOMEDVersion>{{ Medication.Sig.CodeSystem.SNOMEDVersion }}</SNOMEDVersion>
                    {% endif %}
                    {% if Medication.Sig.CodeSystem.FMTVersion %}
                        <FMTVersion>{{ Medication.Sig.CodeSystem.FMTVersion }}</FMTVersion>
                    {% endif %}
                </CodeSystem>
            {% endif %}
            {% if Medication.Sig.Instruction %}
                {% for instruction in Medication.Sig.Instruction %}
                    <Instruction>
                        {% if instruction.DoseAdministration %}
                            <DoseAdministration>
                                {% if instruction.DoseAdministration.DoseDeliveryMethod %}
                                    <DoseDeliveryMethod>
                                        {% if instruction.DoseAdministration.DoseDeliveryMethod.Text %}
                                            <Text>{{ instruction.DoseAdministration.DoseDeliveryMethod.Text }}</Text>
                                        {% endif %}
                                        {% if instruction.DoseAdministration.DoseDeliveryMethod.Qualifier %}
                                            <Qualifier>{{ instruction.DoseAdministration.DoseDeliveryMethod.Qualifier }}</Qualifier>
                                        {% endif %}
                                        {% if instruction.DoseAdministration.DoseDeliveryMethod.Code %}
                                            <Code>{{ instruction.DoseAdministration.DoseDeliveryMethod.Code }}</Code>
                                        {% endif %}
                                    </DoseDeliveryMethod>
                                {% endif %}
                                {% if instruction.DoseAdministration.Dosage %}
                                    <Dosage>
                                        {% if instruction.DoseAdministration.Dosage.DoseQuantity %}
                                            <DoseQuantity>{{ instruction.DoseAdministration.Dosage.DoseQuantity }}</DoseQuantity>
                                        {% endif %}
                                        {% if instruction.DoseAdministration.Dosage.DoseUnitOfMeasure %}
                                            <DoseUnitOfMeasure>
                                                {% if instruction.DoseAdministration.Dosage.DoseUnitOfMeasure.Text %}
                                                    <Text>{{ instruction.DoseAdministration.Dosage.DoseUnitOfMeasure.Text }}</Text>
                                                {% endif %}
                                                {% if instruction.DoseAdministration.Dosage.DoseUnitOfMeasure.Qualifier %}
                                                    <Qualifier>{{ instruction.DoseAdministration.Dosage.DoseUnitOfMeasure.Qualifier }}</Qualifier>
                                                {% endif %}
                                                {% if instruction.DoseAdministration.Dosage.DoseUnitOfMeasure.Code %}
                                                    <Code>{{ instruction.DoseAdministration.Dosage.DoseUnitOfMeasure.Code }}</Code>
                                                {% endif %}
                                            </DoseUnitOfMeasure>
                                        {% endif %}
                                    </Dosage>
                                {% endif %}
                                {% if instruction.DoseAdministration.RouteOfAdministration %}
                                    <RouteOfAdministration>
                                        {% if instruction.DoseAdministration.RouteOfAdministration.Text %}
                                            <Text>{{ instruction.DoseAdministration.RouteOfAdministration.Text }}</Text>
                                        {% endif %}
                                        {% if instruction.DoseAdministration.RouteOfAdministration.Qualifier %}
                                            <Qualifier>{{ instruction.DoseAdministration.RouteOfAdministration.Qualifier }}</Qualifier>
                                        {% endif %}
                                        {% if instruction.DoseAdministration.RouteOfAdministration.Code %}
                                            <Code>{{ instruction.DoseAdministration.RouteOfAdministration.Code }}</Code>
                                        {% endif %}
                                    </RouteOfAdministration>
                                {% endif %}
                            </DoseAdministration>
                        {% endif %}
                        {% if instruction.TimingAndDuration %}
                            <TimingAndDuration>
                                {% if instruction.TimingAndDuration.Frequency %}
                                    <Frequency>
                                        {% if instruction.TimingAndDuration.Frequency.FrequencyNumericValue %}
                                            <FrequencyNumericValue>{{ instruction.TimingAndDuration.Frequency.FrequencyNumericValue }}</FrequencyNumericValue>
                                        {% endif %}
                                        {% if instruction.TimingAndDuration.Frequency.FrequencyUnits %}
                                            <FrequencyUnits>
                                                {% if instruction.TimingAndDuration.Frequency.FrequencyUnits.Text %}
                                                    <Text>{{ instruction.TimingAndDuration.Frequency.FrequencyUnits.Text }}</Text>
                                                {% endif %}
                                                {% if instruction.TimingAndDuration.Frequency.FrequencyUnits.Qualifier %}
                                                    <Qualifier>{{ instruction.TimingAndDuration.Frequency.FrequencyUnits.Qualifier }}</Qualifier>
                                                {% endif %}
                                                {% if instruction.TimingAndDuration.Frequency.FrequencyUnits.Code %}
                                                    <Code>{{ instruction.TimingAndDuration.Frequency.FrequencyUnits.Code }}</Code>
                                                {% endif %}
                                            </FrequencyUnits>
                                        {% endif %}
                                    </Frequency>
                                {% endif %}
                                {% if instruction.TimingAndDuration.Duration %}
                                    <Duration>
                                        {% if instruction.TimingAndDuration.Duration.DurationNumericValue %}
                                            <DurationNumericValue>{{ instruction.TimingAndDuration.Duration.DurationNumericValue }}</DurationNumericValue>
                                        {% endif %}
                                        {% if instruction.TimingAndDuration.Duration.DurationText %}
                                            <DurationText>
                                                {% if instruction.TimingAndDuration.Duration.DurationText.Text %}
                                                    <Text>{{ instruction.TimingAndDuration.Duration.DurationText.Text }}</Text>
                                                {% endif %}
                                                {% if instruction.TimingAndDuration.Duration.DurationText.Qualifier %}
                                                    <Qualifier>{{ instruction.TimingAndDuration.Duration.DurationText.Qualifier }}</Qualifier>
                                                {% endif %}
                                                {% if instruction.TimingAndDuration.Duration.DurationText.Code %}
                                                    <Code>{{ instruction.TimingAndDuration.Duration.DurationText.Code }}</Code>
                                                {% endif %}
                                            </DurationText>
                                        {% endif %}
                                    </Duration>
                                {% endif %}
                            </TimingAndDuration>
                        {% endif %}
                    </Instruction>
                {% endfor %}
            {% endif %}
            {% if Medication.Sig.FreeText %}
                {% if Medication.Sig.MultipleInstructionModifier %}
                    <MultipleInstructionModifier>{{ Medication.Sig.MultipleInstructionModifier }}</MultipleInstructionModifier>
                {% endif %}
                {% if Medication.Sig.ClarifyingFreeText %}
                    <ClarifyingFreeText>{{ Medication.Sig.ClarifyingFreeText }}</ClarifyingFreeText>
                {% endif %}
                {% if Medication.Sig.FreeText %}
                    <FreeText>{{ Medication.Sig.FreeText }}</FreeText>
                {% endif %}
            {% endif %}
        </Sig>
    {% endif %}
    {% if Medication.PharmacyRequestedRefills %}
        <PharmacyRequestedRefills>{{ Medication.PharmacyRequestedRefills }}</PharmacyRequestedRefills>
    {% endif %}
    {% if Medication.DoNotFill %}
        <DoNotFill>{{ Medication.DoNotFill }}</DoNotFill>
    {% endif %}
    {% if Medication.TimeZone %}
        <TimeZone>{{ Medication.TimeZone }}</TimeZone>
    {% endif %}
    {% if Medication.RefillsRemaining %}
        <RefillsRemaining>{{ Medication.RefillsRemaining }}</RefillsRemaining>
    {% endif %}
    {% if Medication.HistoryPrescriberOrderNumber %}
        <HistoryPrescriberOrderNumber>{{ Medication.HistoryPrescriberOrderNumber }}</HistoryPrescriberOrderNumber>
    {% endif %}
    {% if Medication.OrderCaptureMethod %}
        <OrderCaptureMethod>{{ Medication.OrderCaptureMethod }}</OrderCaptureMethod>
    {% endif %}
    {% if Medication.Pharmacy %}
        <Pharmacy>
            {% if Medication.Pharmacy.Identification %}
                <Identification>
                    {% if Medication.Pharmacy.Identification.NCPDPID %}
                        <NCPDPID>{{ Medication.Pharmacy.Identification.NCPDPID }}</NCPDPID>
                    {% endif %}
                    {% if Medication.Pharmacy.Identification.StateLicenseNumber %}
                        <StateLicenseNumber>{{ Medication.Pharmacy.Identification.StateLicenseNumber }}</StateLicenseNumber>
                    {% endif %}
                </Identification>
            {% endif %}
            {% if Medication.Pharmacy.BusinessName %}
                <BusinessName>{{ Medication.Pharmacy.BusinessName }}</BusinessName>
            {% endif %}
            {% if Medication.Pharmacy.Address %}
                <Address>
                    {% if Medication.Pharmacy.Address.AddressLine1 %}
                        <AddressLine1>{{ Medication.Pharmacy.Address.AddressLine1 }}</AddressLine1>
                    {% endif %}
                    {% if Medication.Pharmacy.Address.City %}
                        <City>{{ Medication.Pharmacy.Address.City }}</City>
                    {% endif %}
                    {% if Medication.Pharmacy.Address.StateProvince %}
                        <StateProvince>{{ Medication.Pharmacy.Address.StateProvince }}</StateProvince>
                    {% endif %}
                    {% if Medication.Pharmacy.Address.PostalCode %}
                        <PostalCode>{{ Medication.Pharmacy.Address.PostalCode }}</PostalCode>
                    {% endif %}
                </Address>
            {% endif %}
            {% if Medication.Pharmacy.CommunicationNumbers %}
                <CommunicationNumbers>
                    {% if Medication.Pharmacy.CommunicationNumbers.PrimaryTelephone %}
                        <PrimaryTelephone>
                            {% if Medication.Pharmacy.CommunicationNumbers.PrimaryTelephone.Number %}
                                <Number>{{ Medication.Pharmacy.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
                            {% endif %}
                        </PrimaryTelephone>
                    {% endif %}
                </CommunicationNumbers>
            {% endif %}
        </Pharmacy>
    {% endif %}
    {% if Medication.Prescriber.NonVeterinarian %}
        <Prescriber>
            <NonVeterinarian>
                {% if Medication.Prescriber.NonVeterinarian.Identification %}
                    <Identification>
                        {% if Medication.Prescriber.NonVeterinarian.Identification.NPI %}
                            <NPI>{{ Medication.Prescriber.NonVeterinarian.Identification.NPI }}</NPI>
                        {% endif %}
                        {% if Medication.Prescriber.NonVeterinarian.Identification.DEANumber %}
                            <DEANumber>{{ Medication.Prescriber.NonVeterinarian.Identification.DEANumber }}</DEANumber>
                        {% endif %}
                    </Identification>
                {% endif %}
                {% if Medication.Prescriber.NonVeterinarian.Name %}
                    <Name>
                        {% if Medication.Prescriber.NonVeterinarian.Name.LastName %}
                            <LastName>{{ Medication.Prescriber.NonVeterinarian.Name.LastName }}</LastName>
                        {% endif %}
                        {% if Medication.Prescriber.NonVeterinarian.Name.FirstName %}
                            <FirstName>{{ Medication.Prescriber.NonVeterinarian.Name.FirstName }}</FirstName>
                        {% endif %}
                    </Name>
                {% endif %}
                {% if Medication.Prescriber.NonVeterinarian.Address %}
                    <Address>
                        {% if Medication.Prescriber.NonVeterinarian.Address.AddressLine1 %}
                            <AddressLine1>{{ Medication.Prescriber.NonVeterinarian.Address.AddressLine1 }}</AddressLine1>
                        {% endif %}
                        {% if Medication.Prescriber.NonVeterinarian.Address.City %}
                            <City>{{ Medication.Prescriber.NonVeterinarian.Address.City }}</City>
                        {% endif %}
                        {% if Medication.Prescriber.NonVeterinarian.Address.StateProvince %}
                            <StateProvince>{{ Medication.Prescriber.NonVeterinarian.Address.StateProvince }}</StateProvince>
                        {% endif %}
                        {% if Medication.Prescriber.NonVeterinarian.Address.PostalCode %}
                            <PostalCode>{{ Medication.Prescriber.NonVeterinarian.Address.PostalCode }}</PostalCode>
                        {% endif %}
                    </Address>
                {% endif %}
                {% if Medication.Prescriber.NonVeterinarian.CommunicationNumbers %}
                    <CommunicationNumbers>
                        {% if Medication.Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone %}
                            <PrimaryTelephone>
                                {% if Medication.Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number %}
                                    <Number>{{ Medication.Prescriber.NonVeterinarian.CommunicationNumbers.PrimaryTelephone.Number }}</Number>
                                {% endif %}
                            </PrimaryTelephone>
                        {% endif %}
                    </CommunicationNumbers>
                {% endif %}
            </NonVeterinarian>
        </Prescriber>
    {% endif %}

{% endmacro %}