EligibilityInquiry:
  $id: EligibilityInquiry
  properties:
    customerid:
      type: string
    dependent:
      properties:
        patient:
          properties:
            dob:
              type: string
            firstname:
              type: string
            gender:
              type: string
            lastname:
              type: string
            middlename:
              type: string
          type: object
        relationwithsubscriber:
          type: string
      type: object
    dos_enddate:
      type: string
    dos_startdate:
      type: string
    includetextresponse:
      type: boolean
    internalid:
      type: string
    issubscriberpatient:
      type: string
    location:
      type: string
    payercode:
      type: string
    payername:
      type: string
    provider:
      properties:
        firstname:
          type: string
        lastname:
          type: string
        middlename:
          type: string
        npi:
          type: string
        pin:
          type: string
        taxonomy:
          type: string
      required:
        - lastname
        - npi
      type: object
    referenceid:
      type: string
    servicecodes:
      items:
        type: string
      type: array
    subscriber:
      properties:
        dob:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        memberid:
          type: string
        ssn:
          type: string
      required:
        - memberid
      type: object
  required:
    - payercode
    - provider
    - subscriber
    - issubscriberpatient
    - dos_startdate
    - dos_enddate
    - servicecodes
    - referenceid
    - location
    - internalid
  type: object
EligibilityInquiryResponse:
  $id: EligibilityInquiryResponse
  type: object
  additionalProperties: true
  properties:
    elgrequestid:
      type: integer
    status:
      type: string
    payername:
      type: string
    payercode:
      type: string
    verificationtype:
      type: string
    ispayerbackoffice:
      type: boolean
    verificationstatus:
      type: string
    verificationMessage:
      type: string
    processedwitherror:
      type: boolean
    dos:
      type: string
    Plan:
      type: string
    exceptionnotes:
      type: string
    additionalinformation:
      type: string
    edierrormessage:
      type: string
    errorcode:
      type: string
    errordescription:
      type: string
    othermessage:
      type: string
    reporturl:
      type: string
    dob_r:
      type: string
    eligibilityperiod:
      type: object
      additionalProperties: true
      properties:
        effectivefromdate:
          type: string
        expiredondate:
          type: string
    demographicinfo:
      type: object
      additionalProperties: true
      properties:
        subscriber:
          type: object
          additionalProperties: true
          properties:
            address1:
              type: string
            address2:
              type: string
            city:
              type: string
            state:
              type: string
            zip:
              type: string
            dob_r:
              type: string
            firstname:
              type: string
            lastname_r:
              type: string
            gender_r:
              type: string
            identification:
              type: object
              additionalProperties: true
              properties:
                code:
                  type: string
                type:
                  type: string
                name:
                  type: string
        dependent:
          type: object
          additionalProperties: true
    relationship:
      type: string
    networksections:
      type: object
      additionalProperties: true
      properties:
        identifier:
          type: string
        label:
          type: string
        innetworkparameters:
          type: object
          additionalProperties: true
          properties:
            key:
              type: string
            value:
              type: string
    healthbenefitplancoverageservicetype:
      type: object
      additionalProperties: true
      properties:
        servicetypename:
          type: string
        servicetypesections:
          type: array
          items:
            type: object
            additionalProperties: true
            properties:
              label:
                type: string
              serviceparameters:
                type: object
                additionalProperties: true
                properties:
                  key:
                    type: string
                  value:
                    type: string
                  message:
                    type: string
                  otherinfo:
                    type: array
                    items:
                      type: string
    ishmoplan:
      type: boolean
    internalid:
      type: string
    customerid:
      type: string
    detailsurl:
      type: string
    isproviderinnetwork:
      type: boolean
    recursiverequestid:
      type: integer
    recursiveapiresponsecode:
      type: string
    recursiveapiresponsmessage:
      type: string
EligibilitySummaryRequest:
  $id: EligibilitySummaryRequest
  type: object
  properties:
    customerid:
      type: string
    dependent:
      type: object
      properties:
        patient:
          type: object
          properties:
            dob:
              type: string
            firstname:
              type: string
            gender:
              type: string
            lastname:
              type: string
            middlename:
              type: string
          required:
            - firstname
            - lastname
            - dob
        relationwithsubscriber:
          type: string
      required:
        - patient
    dos_enddate:
      type: string
    dos_startdate:
      type: string
    includetextresponse:
      type: boolean
    internalid:
      type: string
      description: Clara MRN 
    issubscriberpatient:
      type: string
    location:
      type: string
    payercode:
      type: string
    payername:
      type: string
    practicetypecode:
      type: string
    provider:
      type: object
      properties:
        firstname:
          type: string
        lastname:
          type: string
        middlename:
          type: string
        npi:
          type: string
        pin:
          type: string
        taxonomy:
          type: string
      required:
        - lastname
        - npi
    referenceid:
      type: string
    subscriber:
      type: object
      properties:
        dob:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        memberid:
          type: string
        ssn:
          type: string
      required:
        - memberid
  required:
    - payercode
    - provider
    - subscriber
    - issubscriberpatient
    - dos_startdate
    - dos_enddate
    - location
    - internalid
  allOf:
    - if:
        properties:
          issubscriberpatient:
            const: false
      then:
        required:
          - dependent
        properties:
          dependent:
            type: object
            required:
              - patient
            properties:
              patient:
                type: object
                required:
                  - firstname
                  - lastname
                  - dob
   
EligibilitySummaryResponse:
  $id: EligibilitySummaryResponse
  type: object
  properties:
    requestid:
      type: integer
    apiresponsecode:
      type: string
    apiresponsemessage:
      type: string
    processedwitherror:
      type: boolean
    edierrormessage:
      type: string
    pverifypayercode:
      type: string
    errorcode:
      type: string
    errordescription:
      type: string
    payername:
      type: string
    verificationtype:
      type: string
    ispayerbackoffice:
      type: string
    dos:
      type: string
    ishmoplan:
      type: boolean
    exceptionnotes:
      type: string
    addtionalinfo:
      type: string
    location:
      type: string
    referrenceid:
      type: string
    resultpracticetype:
      type: string
    areallstcsprocessed:
      type: boolean
    stcsstatusmessage:
      type: string
    isproviderinnetwork:
      type: boolean
    recursiverequestid:
      type: integer
    recursiveapiresponsecode:
      type: string
    recursiveapiresponsemessage:
      type: string
    recursiveprocessedwitherror:
      type: boolean
    internalid:
      type: string
    customerid:
      type: string
    notes:
      type: string
    demographicinfo:
      type: object
      additionalProperties: true
    servicedetails:
      type: array
      additionalProperties: true
    preventiveservices:
      type: object
      additionalProperties: true
    otherpayerinfo:
      type: object
      additionalProperties: true
    plancoveragesummary:
      type: object
      additionalProperties: true
    pcpauthinfosummary:
      type: object
      additionalProperties: true
    hbpc_deductible_oop_summary:
      type: object
      additionalProperties: true
    medicareinfosummary:
      type: object
      additionalProperties: true
    miscellaneousinfosummary:
      type: object
      additionalProperties: true
    specialistofficesummary:
      type: object
      additionalProperties: true
    diagnosticlabsummary:
      type: object
      additionalProperties: true
    surgicalsummary:
      type: object
      additionalProperties: true
    asc_facilitysummary:
      type: object
      additionalProperties: true
    oncologysummary:
      type: object
      additionalProperties: true
    dmesummary:
      type: object
      additionalProperties: true
    mentalhealthsummary:
      type: object
      additionalProperties: true
    primarycaresummary:
      type: object
      additionalProperties: true
    mri_ct_scansummary:
      type: object
      additionalProperties: true
    urgentcaresummary:
      type: object
      additionalProperties: true
    xraysummary:
      type: object
      additionalProperties: true
    visionoptometrysummary:
      type: object
      additionalProperties: true
    physicaltherapysummary:
      type: object
      additionalProperties: true
    chiropracticsummary:
      type: object
      additionalProperties: true
    speechthearysummary:
      type: object
      additionalProperties: true
    occupationaltherapysummary:
      type: object
      additionalProperties: true
    emergencymedicalsummary:
      type: object
      additionalProperties: true
    wellnessoroutinevisitsummary:
      type: object
      additionalProperties: true
    podiatryofficesummary:
      type: object
      additionalProperties: true
    anesthesiasummary:
      type: object
      additionalProperties: true
    substanceabuseprofessionalsummary:
      type: object
      additionalProperties: true
    substanceabuseinpatientfacilitysummary:
      type: object
      additionalProperties: true
    substanceabuseoutpatientfacilitysummary:
      type: object
      additionalProperties: true
    telemedicinesummary:
      type: object
      additionalProperties: true
    fluvaccinationsummary:
      type: object
      additionalProperties: true
    hospitalinpatientsummary:
      type: object
      additionalProperties: true
    hospitaloutpatientsummary:
      type: object
      additionalProperties: true
    pharmacysummary:
      type: object
      additionalProperties: true
    telemedicineprimarycaresummary:
      type: object
      additionalProperties: true
    telemedicinespecialistsummary:
      type: object
      additionalProperties: true
    telemedicineurgentcaresummary:
      type: object
      additionalProperties: true
    telemedicinementalhealthsummary:
      type: object
      additionalProperties: true
    telemedicinephysicaltherapysummary:
      type: object
      additionalProperties: true
    psychotherapysummary:
      type: object
      additionalProperties: true
    snfsummary:
      type: object
      additionalProperties: true
    snfroomboardsummary:
      type: object
      additionalProperties: true
    professionalphysicianvisitinpatientsummary:
      type: object
      additionalProperties: true
    hospicesummary:
      type: object
      additionalProperties: true
    homehealthcaresummary:
      type: object
      additionalProperties: true
    allergiessummary:
      type: object
      additionalProperties: true
    diagnosticmedicalsummary:
      type: object
      additionalProperties: true
    maternitysummary:
      type: object
      additionalProperties: true
    immunizationssummary:
      type: object
      additionalProperties: true
    telemedicineoccupationaltherapysummary:
      type: object
      additionalProperties: true
    telemedicinespeechtherapysummary:
      type: object
      additionalProperties: true
    eligibilityresult:
      type: string
    detailsurl:
      type: string
directoryprovider:
  $id: directoryprovider
  properties:
    ActiveEndTime:
      type: string
    ActiveStartTime:
      type: string
    AddressLine1:
      type: string
    AddressLine2:
      type: string
    AlternatePhoneNumbers:
      type: string
    AvailableRoutes:
      type: string
    BusinessName:
      type: string
    CertificateToPrescribe:
      type: string
    City:
      type: string
    CountryCode:
      type: string
    DEANumber:
      type: string
    Data2000WaiverID:
      type: string
    DirectAddress:
      type: string
    DirectorySpecialtyName:
      type: string
    ElectronicMail:
      type: string
    Fax:
      type: string
    FirstName:
      type: string
    LastModifiedDate:
      format: date-time
      type: string
    LastName:
      type: string
    Latitude:
      type: number
    Longitude:
      type: number
    MedicaidNumber:
      type: string
    MedicareNumber:
      type: string
    MiddleName:
      type: string
    MutuallyDefined:
      type: string
    NPI:
      type: number
    OldServiceLevel:
      type: string
    OrganizationID:
      type: string
    PartnerAccount:
      type: string
    PostalCode:
      type: string
    Precise:
      type: string
    Prefix:
      type: string
    PrimaryTelephone:
      type: string
    REMSHealthCareProviderEnrollmentID:
      type: string
    RecordChange:
      type: string
    SPI:
      type: number
    ServiceLevel:
      type: string
    Specialty:
      type: string
    StandardizedAddressLine1:
      type: string
    StandardizedAddressLine2:
      type: string
    StandardizedCity:
      type: string
    StandardizedPostalCode:
      type: string
    StandardizedStateProvince:
      type: string
    StateControlSubstanceNumber:
      type: string
    StateLicenseNumber:
      type: string
    StateProvince:
      type: string
    Suffix:
      type: string
    UPIN:
      type: string
    UseCases:
      type: string
    Version:
      type: string
    createdAt:
      format: date-time
      type: string
    id:
      type: number
    updatedAt:
      format: date-time
      type: string
  type: object
organizationprovider:
  $id: organizationprovider
  properties:
    ActiveEndTime:
      type: string
    ActiveStartTime:
      type: string
    AddressLine1:
      type: string
    AddressLine2:
      type: string
    AlternatePhoneNumbers:
      type: string
    City:
      type: string
    CountryCode:
      type: string
    CrossStreet:
      type: string
    DEANumber:
      type: string
    DirectAddress:
      type: string
    DirectorySpecialtyName:
      type: string
    ElectronicMail:
      type: string
    FacilityID:
      type: string
    Fax:
      type: string
    HIN:
      type: string
    LastModifiedDate:
      format: date-time
      type: string
    Latitude:
      type: number
    Longitude:
      type: number
    MedicaidNumber:
      type: string
    MedicareNumber:
      type: string
    MutuallyDefined:
      type: string
    NCPDPID:
      type: string
    NPI:
      type: string
    OldServiceLevel:
      type: string
    OrganizationID:
      type: string
    OrganizationName:
      type: string
    OrganizationType:
      type: string
    ParentOrganizationID:
      type: string
    PartnerAccount:
      type: string
    PayerID:
      type: string
    PostalCode:
      type: string
    Precise:
      type: string
    PrimaryTelephone:
      type: string
    RecordChange:
      type: string
    ReplaceNCPDPID:
      type: string
    ServiceLevel:
      type: string
    StandardizedAddressLine1:
      type: string
    StandardizedAddressLine2:
      type: string
    StandardizedCity:
      type: string
    StandardizedPostal:
      type: string
    StandardizedStateProvince:
      type: string
    StateLicenseNumber:
      type: string
    StateProvince:
      type: string
    StoreNumber:
      type: string
    UPIN:
      type: string
    UseCase:
      type: string
    Version:
      type: string
    createdAt:
      format: date-time
      type: string
    id:
      type: number
    updatedAt:
      format: date-time
      type: string
  type: object


organizationqresp:
  $id: organizationqresp
  properties:
    Body:
      properties:
        GetOrganizationResponse:
          properties:
            GetOrganizationResponseItem:
              properties:
                DirectoryInformation:
                  properties:
                    ActiveEndTime:
                      format: date-time
                      type: string
                    ActiveStartTime:
                      format: date-time
                      type: string
                    FaxBackup:
                      type: string
                    ServiceLevels:
                      properties:
                        ServiceLevel:
                          items:
                            properties:
                              ServiceLevelName:
                                type: string
                            type: object
                          type: array
                      type: object
                    Test:
                      type: number
                  type: object
                Organization:
                  properties:
                    Address:
                      properties:
                        AddressLine1:
                          type: string
                        City:
                          type: string
                        CountryCode:
                          type: string
                        PostalCode:
                          type: string
                        StateProvince:
                          type: string
                      type: object
                    CommunicationNumbers:
                      properties:
                        Fax:
                          properties:
                            Number:
                              type: number
                          type: object
                        PrimaryTelephone:
                          properties:
                            Number:
                              type: number
                          type: object
                      type: object
                    Identification:
                      properties:
                        NPI:
                          type: number
                        OrganizationID:
                          type: number
                      type: object
                    OperatingStatus:
                      properties:
                        Status:
                          type: string
                      type: object
                    OrganizationName:
                      type: string
                    OrganizationType:
                      type: string
                    ParentOrganizationID:
                      type: number
                  type: object
              type: object
          type: object
      type: object
pproviderqresp:
  $id: providerqresp
  properties:
    DirectoryMessage:
      properties:
        Body:
          additionalProperties: true
          properties:
            GetProviderLocationResponse:
              properties:
                GetProviderLocationResponseItem:
                  properties:
                    DirectoryInformation:
                      properties:
                        ActiveEndTime:
                          format: date-time
                          type: string
                        ActiveStartTime:
                          format: date-time
                          type: string
                        FaxBackup:
                          type: string
                        ServiceLevels:
                          properties:
                            ServiceLevel:
                              items:
                                properties:
                                  ServiceLevelName:
                                    type: string
                                type: object
                              type: array
                          type: object
                        Test:
                          type: number
                      type: object
                    ProviderLocation:
                      properties:
                        Address:
                          properties:
                            AddressLine1:
                              type: string
                            AddressLine2:
                              type: string
                            City:
                              type: string
                            CountryCode:
                              type: string
                            PostalCode:
                              type: string
                            StateProvince:
                              type: string
                          type: object
                        CommunicationNumbers:
                          properties:
                            Fax:
                              properties:
                                Number:
                                  type: string
                              type: object
                            PrimaryTelephone:
                              properties:
                                Number:
                                  type: string
                              type: object
                          type: object
                        Identification:
                          properties:
                            DEANumber:
                              type: string
                            NPI:
                              type: string
                            SPI:
                              type: string
                            Veterinarian:
                              type: string
                          type: object
                        Name:
                          properties:
                            FirstName:
                              type: string
                            LastName:
                              type: string
                          type: object
                        PracticeLocation:
                          properties:
                            BusinessName:
                              type: string
                          type: object
                      type: object
                  type: object
              type: object
          type: object
        Header:
          properties:
            From:
              type: string
            MessageID:
              type: string
            RelatesToMessageID:
              type: string
            SenderSoftware:
              properties:
                SenderSoftwareDeveloper:
                  type: string
                SenderSoftwareProduct:
                  type: string
                SenderSoftwareVersionRelease:
                  type: string
              type: object
            SentTime:
              format: date-time
              type: string
            To:
              type: string
          type: object
      type: object
  type: object
EasyEligibilityRequest:
  $id: EasyEligibilityRequest
  type: object
  properties:
    payerCode:
      type: string
      description: pVerify payer code
    provider_lastname:
      type: string
      description: Provider Last Name
    provider_npi:
      type: string
      description: NPI number of provider
    Provider_PIN:
      type: string
      description: Provider PIN
    Provider_TaxId:
      type: string
      description: Provider TaxId
    Patient_First:
      type: string
      description: Patient First Name
    Patient_Last:
      type: string
      description: Patient Last Name
    memberID:
      type: string
      description: Payer Member ID
    patient_DOB:
      type: string
      description: Patient date of birth (Expected format MM/dd/YYYY ie 01/01/2000)
    date_Of_Service:
      type: string
      description: Date of Service (Expected format MM/dd/YYYY ie 01/01/2000)
    Date_Of_Service_To:
      type: string
      description: Date of Service To (in format MM-DD-YYYY). Default date is Date of Service Start date
    serviceCodes:
      type: string
      description: If no STC passed then STC 30 will be used
    InternalId:
      type: string
      description: Field from customer with their InternalId
    CustomerId:
      type: string
      description: Field from customer with their CustomerId
  required:
    - payerCode
    - provider_lastname
    - provider_npi
    - date_Of_Service
EasyEligibilityResponse:
  $id: EasyEligibilityResponse
  type: object
  properties:
    transactionStatus:
      type: string
      description: Status of transaction
    transactionMessage:
      type: string
      description: Additional information regarding transaction
    ediErrorMessage:
      type: string
      description: Error message
    processedWithError:
      type: boolean
      description: true when processed with error by the payer.
    eligibilityStatus:
      type: string
      description: Status of eligibility
    eligibilityResult:
      type: string
      description: Final information regarding eligibility
    referenceId:
      type: string
      description: Patient MRN or account Number
    location:
      type: string
      description: Location is the practice location. Note by setting this, you will lock the patient to one location, so that users that login in via our portal who are not authorized to see that location will not see the patient.
    InternalId:
      type: string
      description: Field from customer with their InternalId
    CustomerId:
      type: string
      description: Field from customer with their CustomerId
    RecursiveRequestId:
      type: integer
      description: RequestID of secondary lookup for MCA
    RecursiveAPIResponseCode:
      type: string
      description: 0-Processed,1-Rejected, Status of Secondary Inquiry.
    RecursiveAPIResponseMessage:
      type: string
      description: Message about the Secondary transaction.
    DetailsURL:
      type: string
      description: URL for the full report
EasyEligibilitySummaryRequest:
  $id: EasyEligibilitySummaryRequest
  type: object
  properties:
    payerCode:
      type: string
      description: Payer Code
    payerName:
      type: string
      description: PayerName
    Provider_LastName:
      type: string
      description: Provider last name
    Provider_NPI:
      type: string
      description: Provider NPI
    Provider_PIN:
      type: string
      description: Provider PIN
    Provider_TaxId:
      type: string
      description: Provider TaxId
    Patient_First:
      type: string
      description: Patient First Name
    Patient_DOB:
      type: string
      description: Patient Date of Birth (in format MM-DD-YYYY)
    Patient_Last:
      type: string
      description: Patient Last Name
    memberID:
      type: string
      description: Member ID
    Date_Of_Service:
      type: string
      description: Date of Service (in format MM-DD-YYYY)
    Date_Of_Service_To:
      type: string
      description: Date of Service To (in format MM-DD-YYYY). Default date is Date of Service Start date
    referenceId:
      type: string
      description: Patient MRN or account Number
    Location:
      type: string
      description: Location is the practice location. Note by setting this, you will lock the patient to one location, so that users that login in via our portal who are not authorized to see that location will not see the patient.
    PracticeTypeCode:
      type: string
      description: Please consult Practice Type Code table - sets which practice type to use, i.e. 12 for physical therapy, which will return an object with PT benefits in the results. If you do not select a practice type, the default for your account will be used. Contact pVerify for more info.
    IncludeTextResponse:
      type: boolean
      description: Boolean value to determine if text response should be included
    InternalId:
      type: string
      description: No
    CustomerId:
      type: string
      description: No
  required:
    - payerCode
    - Provider_LastName
    - Provider_NPI
    - Date_Of_Service
EasyEligibilitySummaryResponse:
  $id: EasyEligibilitySummaryResponse
  type: object
  properties:
    RequestID:
      type: integer
      description: Eligibility Transaction ID
    APIResponseCode:
      type: string
      description: 0-Processed,1-Rejected,2-NoFunds,3-Pending,4-New
    APIResponseMessage:
      type: string
      description: Message about the transaction
    ProcessedWithError:
      type: boolean
      description: true when processed with error by the payer.
    EDIErrorMessage:
      type: string
      description: Message from Payer about errors
    PverifyPayerCode:
      type: string
      description: pVerify's Payer Code
    PayerName:
      type: string
      description: Payer Name as defined by pVerify
    VerificationType:
      type: string
      description: Verification Type –Subscriber or dependent
    IsPayerBackOffice:
      type: string
      description: If it is TRUE-NON EDI Payer, FALSE –EDI Payer
    VerificationStatus:
      type: string
      description: Eligibility Transaction Status- Values  Processed, Pending, Rejected
    DOS:
      type: string
      description: Date of service start date – DOS end date
    IsHMOPlan:
      type: boolean
      description: If true then Patient has HMO plan
    ExceptionNotes:
      type: string
      description: Exception Notes if any. If response demographic info is not matched with that of Request
    AddtionalInfo:
      type: string
      description: If there is any other info
    Location:
      type: string
      description: Location in which trans ran
    ReferrenceId:
      type: string
      description: Referencid passed in request
    ResultPracticeType:
      type: string
      description: Practice type Name
    AreAllSTCsProcessed:
      type: boolean
      description: True when all service codes are processed by payer else false, This is helpful to determine to whether transaction needs to be reverify or not.
    STCsStatusMessage:
      type: string
      description: Contains message when AreAllSTCsProcessed=false. Message contains list of not processed service codes
    IsProviderInNetwork:
      type: boolean
      description: If true then Provider is InNetwork. Based on Provider Payer Settings* Null, if NO Provider Payer Settings* (*Requires set up in Premium Portal)
    RecursiveRequestId:
      type: integer
      description: RequestID of secondary lookup for MCA
    RecursiveAPIResponseCode:
      type: string
      description: 0-Processed,1-Rejected, Status of Recursive Inquiry.
    RecursiveAPIResponseMessage:
      type: string
      description: Message about the Recursive transaction.
    InternalId:
      type: string
      description: Field from customer with their InternalId
    CustomerId:
      type: string
      description: Field from customer with their CustomerId
    Notes:
      type: string
    DemographicInfo:
      type: object
      description: It contains the subscriber info or dependent info .
    ServiceDetails:
      type: array
      description: It contains all Service type details info
    PreventiveServices:
      type: object
      description: Contains Preventive codes details
    OtherPayerInfo:
      type: object
      description: It contains Other Payer info. It is null then there is no payer change.
    PlanCoverageSummary:
      type: object
      description: It contains Plan related info.
    PCPAuthInfoSummary:
      type: object
      description: It contains PCP auth info.
    HBPC_Deductible_OOP_Summary:
      type: object
      description: It contains Deductible and OOP in from Health benefit Plan Coverage service type .It is null for Medicare Payer Inquiries.
    MedicareInfoSummary:
      type: object
      description: It contains Medicare info summary. It is null for other payers and other summary details are empty for Medicare payer transaction.
    MiscellaneousInfoSummary:
      type: object
    SpecialistOfficeSummary:
      type: object
      description: It contains Specialist Consultation specific Summary (Co-Pay,Co-ins,Ded,OOP) and exclusive values of practice type .
    DiagnosticLabSummary:
      type: object
      description: It contains Diagnostic Lab specific Summary
    SurgicalSummary:
      type: object
      description: It contains Surgical specific Summary
    ASC_FacilitySummary:
      type: object
      description: It contains Ambulatory Service Center Facility specific Summary
    OncologySummary:
      type: object
      description: It contains Oncology specific Summary
    DMESummary:
      type: object
      description: It contains Durable Medical Equipment specific Summary
    MentalHealthSummary:
      type: object
      description: It contains Mental Health specific Summary
    PrimaryCareSummary:
      type: object
      description: It contains Primary Care specific Summary
    MRI_CT_ScanSummary:
      type: object
      description: It contains MRI, CAT Scan specific Summary
    UrgentCareSummary:
      type: object
      description: It contains Urgent Care specific Summary
    XRaySummary:
      type: object
      description: It contains X Ray specific Summary
    VisionOptometrySummary:
      type: object
      description: It contains Opthalmology (Vision) specific Summary
    PhysicalTherapySummary:
      type: object
      description: It contains Physical Therapy specific Summary
    ChiropracticSummary:
      type: object
      description: It contains Chiropractice specific Summary
    SpeechThearySummary:
      type: object
      description: It contains Speech Therapy specific Summary
    OccupationalTherapySummary:
      type: object
      description: It contains Occupational Therapy specific Summary
    EmergencyMedicalSummary:
      type: object
      description: It contains Emergency Medical specific Summary
    WellnessOrRoutineVisitSummary:
      type: object
      description: It contains Wellness Or Routine Visit specific Summary
    PodiatryOfficeSummary:
      type: object
      description: It contains Podiatry Office specific Summary
    AnesthesiaSummary:
      type: object
      description: It contains Anesthesia specific Summary
    SubstanceAbuseProfessionalSummary:
      type: object
      description: It contains Substance Abuse Professional specific Summary
    SubstanceAbuseInPatientFacilitySummary:
      type: object
      description: It contains Substance Abuse InPatient Facility specific Summary
    SubstanceAbuseOutPatientFacilitySummary:
      type: object
      description: It contains Substance Abuse OutPatient Facility specific Summary
    TelemedicineSummary:
      type: object
      description: It contains Telemedicine specific Summary
    FluVaccinationSummary:
      type: object
      description: It contains FluVaccination specific Summary
    HospitalInpatientSummary:
      type: object
      description: It contains Hospital Inpatient specific Summary
    HospitalOutPatientSummary:
      type: object
      description: It contains Hospital Outpatient specific Summary
    PharmacySummary:
      type: object
      description: It contains Pharmacy specific Summary
    TelemedicinePrimaryCareSummary:
      type: object
      description: It contains Telemedicine Primary specific Summary
    TelemedicineSpecialistSummary:
      type: object
      description: It contains Telemedicine Specialist specific Summary
    TelemedicineUrgentCareSummary:
      type: object
      description: It contains Telemedicine Urgent Care specific Summary
    TelemedicineMentalHealthSummary:
      type: object
      description: It contains Telemedicine Mental Health specific Summary
    TelemedicinePhysicalTherapySummary:
      type: object
      description: It contains Telemedicine Physical Therapy specific Summary
    PsychotherapySummary:
      type: object
      description: It contains Psychotherapy specific Summary
    EligibilityResult:
      type: string
      description: It contains results in formatted text
    DetailsURL:
      type: string
      description: URL for the full report
PayerListResponse:
  $id: PayerListResponse
  type: object
  properties:
    payerCode:
      type: string
      description: Payer Code
    payerName:
      type: string
      description: Payer Name
    isActive:
      type: boolean
      description: Boolean determines if payer is active
    isSupportingEligibility:
      type: boolean
      description: Boolean determines if payer is supporting eligibility
    isSupportingClaims:
      type: boolean
      description: Boolean to see if payer is supporting claims
    isEDIPayer:
      type: boolean
      description: Boolean to see if payer is EDI
ClaraTxResponse:
  $id: ClaraTxResponse
  properties:
    request_hash:
      type: object
      properties:
        signature:
          type: string
        hash:
          type: string
    request_json_data:
      type: object
      additionalProperties: true
    request_message_id:
      type: string
    request_xml_data:
      type: string
    response_hash:
      type: object
      properties:
        signature:
          type: string
        hash:
          type: string
    response_json_data:
      type: object
      additionalProperties: true
    response_message_id:
      type: string
    response_xml_data:
      type: string
    s3_filehash:
      type: string
      minLength: 1
      nullable: true
ClaraClaimTxResponse:
  $id: ClaraClaimTxResponse
  properties:
    _meta:
      type: object
      additionalProperties: true
    request_json_data:
      type: object
      additionalProperties: true
    request_d0_raw:
      type: string
    request_d0_b64:
      type: string
    response_json_data:
      type: object
      additionalProperties: true
    validated_claim_object:
      type: object
      additionalProperties: true
    response_d0_raw:
      type: string
    response_d0_b64:
      type: string
    reversal_information:
      type: object
      additionalProperties: true
SSRequest:
  $id: SSRequest
  type: object
  properties:
    Message:
      type: object
      properties:
        Header:
          type: object
          properties:
            To:
              type: string
            From:
              type: string
            MessageID:
              type: string
          required:
            - To
            - From
        Body:
          type: object
      required:
        - Header
        - Body
  required:
    - Message
SSOrgReqBody:
  $id: SSOrgReqBody
  type: object
  properties:
    organization_ncpdp_id: { type: string }
    organization_id: { type: string }
    organization_npi: { type: string }
    organization_dea_number: { type: string }
    organization_name: { type: string }
    organization_address: { type: string }
    organization_city: { type: string }
    organization_state: { type: string }
    organization_zipcode: { type: string }
    organization_phone: { type: string }
    organization_fax: { type: string }
    organization_nonveterinarian: { type: string }
    organization_veterinarian: { type: string }
    organization_specialties: { type: array, items: { type: string } }
    organization_servicelevels: { type: array, items: { type: string } }
    organization_hoop: { type: string }
  required:
    - organization_ncpdp_id
    - organization_id
    - organization_name
    - organization_address
    - organization_city
    - organization_state
    - organization_zipcode
NCPDPClaimsRequest:
  $id: NCPDPClaimsRequest
  type: object
  properties:
    _meta:
      type: object
      additionalProperties: true
      properties:
        clara_mrn:
          type: string
          description: Clara MRN
        ncpdp_claims_processor:
          type: string
          description: NCPDP Claims Processor (change v powerline)
          enum:
            - change
            - powerline
          default: change
        is_test:
          type: boolean
          description: Is this a test claim, will be auto-reversed if True
          default: false
      required:
        - clara_mrn
    patient_segment:
      type: object
      additionalProperties: true
    pharmacy_segment:
      type: object
      additionalProperties: true
    prescriber_segment:
      type: object
      additionalProperties: true
    insurance_segment:
      type: object
      additionalProperties: true
    cob_segment:
      type: object
      additionalProperties: true
    workers_comp_segment:
      type: object
      additionalProperties: true
    claim_segment:
      type: object
      additionalProperties: true
    dur_segment:
      type: object
      additionalProperties: true
    coupon_segment:
      type: object
      additionalProperties: true
    compound_segment:
      type: object
      additionalProperties: true
    pricing_segment:
      type: object
      additionalProperties: true
    segment_clinical:
      type: object
      additionalProperties: true
    segment_additional_documentation:
      type: object
      additionalProperties: true
    segment_facility:
      type: object
      additionalProperties: true
    segment_narrative:
      type: object
      additionalProperties: true
ChangeMedicalEligRequest:
  $id: ChangeMedicalEligRequest
  type: object
  required:
    - controlnumber
    - subscriber
  properties:
    _meta:
      type: object
      additionalProperties: true
      properties:
        clara_mrn:
          type: string
          description: Clara MRN
        medical_claims_processor:
          type: string
          description: Medical Claims Processor (change)
          enum:
            - change
          default: change
        is_test:
          type: boolean
          description: Is this a test claim, will be auto-reversed if True
          default: false
        clara_claim_uuid:
          type: string
          description: Clara Claim UUID
      required:
        - clara_mrn
        - clara_claim_uuid
    controlnumber:
      type: string
      maxLength: 9
      minLength: 9
      pattern: "^[0-9]+$"
      description: "Segment: ISA, Element: ISA13, Notes: Required, Interchange Control Number - must be exactly 9 positive unsigned numeric characters."
    subscriber:
      type: object
      properties:
        memberid:
          type: string
          maxLength: 80
          minLength: 0
          pattern: "^[A-Za-z0-9-]+$"
          description: "Loop: 2100C and 2100D, Segment: MN1, Element: NM109, Notes: NM108=MI, memberId 2-80 alphanumeric characters"
        firstname:
          type: string
          maxLength: 35
          minLength: 0
          description: "Loop: 2100C and 2100D, Segment: MN1, Element: NM104, Notes: firstName 1-35 alphanumeric characters"
        lastname:
          type: string
          maxLength: 60
          minLength: 0
          description: "Loop: 2100C and 2100D, Segment: MN1, Element: NM103, Notes: lastName 1-60 alphanumeric characters"
        gender:
          type: string
          maxLength: 1
          minLength: 0
          enum: ["M", "F"]
          description: "Loop: 2100C and 2100D, Segment: DMG, Element: DMG03, Notes: gender 1 character 'M' or 'F'"
        dateofbirth:
          type: string
          description: "Loop: 2100C and 2100D, Segment: DMG, Element: DMG02, Notes: date of birth in YYYYMMDD"
        address:
          type: object
          properties:
            address1:
              type: string
              maxLength: 55
              minLength: 0
              description: "Segment: N3, Element: N301, Notes: Required, Address Information"
            city:
              type: string
              maxLength: 30
              minLength: 0
              description: "Segment: N4, Element: N401, Notes: Required, city"
            state:
              type: string
              maxLength: 2
              minLength: 0
              description: "Segment: N4, Element: N402, Notes: state example: TN, WA"
            postalcode:
              type: string
              maxLength: 15
              minLength: 0
              description: "Segment: N4, Element: N403"
          required:
            - address1
            - city
            - state
            - postalcode
      required:
        - memberid
        - firstname
        - lastname
        - gender
        - dateofbirth
    submittertransactionidentifier:
      type: string
      description: "BHT03"
    tradingpartnerserviceid:
      type: string
      maxLength: 80
      minLength: 0
      description: "Loop: 2100A Segment: NM1, Element: NM109, Notes: we send this as MN108 as PI"
    tradingpartnername:
      type: string
      maxLength: 80
      minLength: 0
      description: "Loop: 2100A, Segment: NM1, Element: NM103, Notes: organizational name"
    provider:
      type: object
      properties:
        organizationname:
          type: string
          maxLength: 60
          minLength: 0
          description: "Loop: 2100B Segment: MN1, Element: NM103, Notes: NM101=PR when providerType='payer' && payerId is present otherwise 1P for Provider, NM102=2 Non-Person Entity, organizationName 1-60 alphanumeric characters"
      required:
        - organizationname
  allOf:
    - if:
        properties:
          _meta:
            not:
              required: [clara_claim_uuid]
      then:
        required: [_meta]
    - if:
        required: [_meta]
      then:
        properties:
          _meta:
            required: [clara_claim_uuid]
ChangeMedicalClaimRequest:
  $id: ChangeMedicalClaimRequest
  type: object
  required:
    - billing
    - claimInformation
    - controlNumber
    - receiver
    - submitter
    - subscriber
  properties:
    _meta:
      type: object
      additionalProperties: true
      properties:
        clara_mrn:
          type: string
          description: Clara MRN
        medical_claims_processor:
          type: string
          description: Medical Claims Processor (change)
          enum:
            - change
          default: change
        is_test:
          type: boolean
          description: Is this a test claim, will be auto-reversed if True
          default: false
        clara_claim_uuid:
          type: string
          description: Clara Claim UUID
      required:
        - clara_mrn
        - clara_claim_uuid
    billing:
      type: object
      additionalProperties: true
    claimInformation:
      type: object
      additionalProperties: true
    controlNumber:
      type: string
      description: "Segment: ISA, Element: ISA13, Notes: Required, Interchange Control Number - must be exactly 9 positive unsigned numeric characters."
      maxLength: 9
      minLength: 9
      pattern: "^[0-9]+$"
    receiver:
      type: object
      additionalProperties: true
    submitter:
      type: object
      additionalProperties: true
    subscriber:
      type: object
      additionalProperties: true
  additionalProperties: true
MedicalClaimsStatusRequest:
  $id: MedicalClaimsStatusRequest
  type: object
  properties:
    _meta:
      type: object
      additionalProperties: true
      properties:
        clara_mrn:
          type: string
          description: Clara MRN
        clara_claim_uuid:
          type: string
          description: Clara Claim UUID
        medical_claims_processor:
          type: string
          description: Medical Claims Processor (change)
          enum:
            - change
          default: change
        is_test:
          type: boolean
          description: Is this a test claim, will be sent to sandbox
          default: false
      required:
        - clara_mrn
    controlNumber:
      type: string
      maxLength: 9
      minLength: 9
      pattern: "^[0-9]+$"
      description: "Header, Segment: ST02 (no loop), Notes: Transaction Set Control Number"
    tradingPartnerName:
      type: string
      description: "Loop: 2100A, Segment: NM1, Element: NM103, Notes: organizational name"
    tradingPartnerServiceId:
      type: string
      maxLength: 80
      minLength: 2
      description: "Loop: 2100A Segment: NM1, Element: NM109, Notes: we send this as MN108 as PI = Payer Identification"
    providers:
      type: array
      minItems: 1
      items:
        type: object
        required:
          - providerType
        properties:
          organizationName:
            type: string
            maxLength: 60
            minLength: 1
            description: "Segment: NM1, Element: NM103, Notes: Provider's organization name. Can use organization or last name."
          firstName:
            type: string
            maxLength: 35
            minLength: 1
            description: "Segment: NM1, Element: NM104, Notes: Maps to providerName"
          lastName:
            type: string
            maxLength: 60
            minLength: 1
            description: "Segment: NM1, Element: NM103, Notes: Provider last name. Can use organization or last name."
          npi:
            type: string
            maxLength: 80
            minLength: 2
            description: "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: National Provider Identification. Maps to provider NPI. NM108=XX"
          spn:
            type: string
            maxLength: 80
            minLength: 2
            description: "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: Service Provider Number. Maps to provider SPN. NM108=SV"
          tin:
            type: string
            maxLength: 80
            minLength: 2
            description: "Loop: 2100B, 2100C, Segment: NM1, Element: NM109, Notes: Taxpayer's Identification Number. Maps to provider TIN. NM108=FI"
          taxId:
            type: string
            maxLength: 80
            minLength: 2
            description: "Loop: 2100B, Segment: NM1, Element: NM109, Notes: Taxpayer's Identification Number used when ProviderType = BillingProvider"
          etin:
            type: string
            maxLength: 80
            minLength: 2
            description: "Loop: 2100B, Segment: NM1, Element: NM109, Notes: Electronic transmitter identification Number (ETIN) used when ProviderType = BillingProvider"
          providerType:
            type: string
            enum:
              - BillingProvider
              - ServiceProvider
            description: "Segment: NM1, Notes: Code for entity (Billing or Service) Billing Provider: Loop 2100B, NM101=41 (required), Service Provider: Loop 2100C, NM101=1P if not present it is added and assigned tin = BillingProvider taxId"
    subscriber:
      type: object
      required:
        - firstName
        - lastName
        - memberId
      properties:
        memberId:
          type: string
          maxLength: 80
          minLength: 2
          description: "Loop: 2100D, Segment: NM1, Element: NM109, Notes: The subscriber's insurance member ID. Maps to subscriberId."
        firstName:
          type: string
          maxLength: 35
          minLength: 1
          description: "Loop: 2100D, Segment: NM1, Element: NM104, Notes: The subscriber's first name as specified on their policy"
        lastName:
          type: string
          maxLength: 60
          minLength: 1
          description: "Loop: 2100D, Segment: NM1, Element: NM103, Notes: The subscriber's last name as specified on their policy."
        gender:
          type: string
          enum:
            - M
            - F
            - U
          description: "Loop: 2000D, 2000E, Segment: DMG, Element: DMG03, Notes: The subscriber's gender as specified on their policy."
        dateOfBirth:
          type: string
          description: "Loop: 2000D, Segment: DMG, Element: DMG02, Notes: The birth date as specified on the policy for the subscriber. Maps to BirthDate"
        groupNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D, Segment: REF, Element: REF01, Notes: The group number associated with the subscriber."
    dependent:
      type: object
      required:
        - firstName
        - lastName
      properties:
        firstName:
          type: string
          maxLength: 35
          minLength: 1
          description: "Loop: 2100E, Segment: NM1, Element: NM104, Notes: The dependent's first name as specified on their policy."
        lastName:
          type: string
          maxLength: 60
          minLength: 1
          description: "Loop: 2100E, Segment: NM1, Element: NM103, Notes: The dependent's last name as specified on their policy."
        gender:
          type: string
          enum:
            - M
            - F
            - U
          description: "Loop: 2000E, Segment: DMG, Element: DMG03, Notes: dependent's gender as specified on their policy."
        dateOfBirth:
          type: string
          description: "Loop: 2000E, Segment: DMG, Element: DMG02, Notes: The birth date as specified on the policy for the dependent."
        groupNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200E, Segment: REF, Element: REF02 when REF01=6P, Notes: The group number associated with the subscriber and dependent. Maps to dependent groupNumber."
    encounter:
      type: object
      properties:
        beginningDateOfService:
          type: string
          description: "Loop: 2200D or 2200E, Segment: DTP, Element: DTP03, Notes: Date Time Period - Start Date"
        endDateOfService:
          type: string
          description: "Loop: 2200D or 2200E, Segment: DTP, Element: DTP03, Notes: Date Time Period - End Date"
        trackingNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: TRN, Element: TRN02, Notes: This is the claim status tracking number assigned to the status query for the claim."
        tradingPartnerClaimNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: This is the payer assigned claim number. descriptions goes in REF02 where REF01=1K"
        locationIdentifier:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Required if application or location system identifier is known. descriptions goes in REF02 where REF01=LU"
        billingType:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Billing type reference ID. Example: billing type for inpatient services is 111. descriptions goes in REF02 where REF01=BLT"
        patientAccountNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Patient account number provided by service provider. description goes in REF02 where REF01=EJ"
        pharmacyPrescriptionNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Patient pharmacy prescription number. description goes in REF02 where REF01=XZ"
        clearingHouseClaimNumber:
          type: string
          maxLength: 50
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: REF, Element: REF02, Notes: Claim number provided by clearing house. description goes in REF02 where REF01=D9"
        submittedAmount:
          type: string
          maxLength: 18
          minLength: 1
          description: "Loop: 2200D or 2200E, Segment: AMT, Element: AMT02, Notes: Submitted total charges. description goes in AMT02 where AMT01=T3"
    serviceLineInformation:
      type: object
      properties:
        productOrServiceIDQualifier:
          type: string
          enum:
            - AD
            - ER
            - HC
            - HP
            - IV
            - N4
            - NU
            - WK
          description: "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-1, Notes: Allowed Values are 'AD' American Dental Association Codes, 'ER' Jurisdiction Specific Procedure and Supply Codes, 'HC' Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes, 'HP' Health Insurance Prospective Payment System (HIPPS) Skilled Nursing Facility Rate Code, 'IV' Home Infusion EDI Coalition (HIEC) Product/Service Code, 'N4' National Drug Code in 5-4-2 Format, 'NU' National Uniform Billing Committee (NUBC) UB92 Codes, 'WK' Advanced Billing Concepts (ABC) Codes"
        procedureCode:
          type: string
          description: "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-2, Notes: Procedure Code maps to Product/ServiceID"
        procedureModifiers:
          type: array
          maxItems: 4
          minItems: 0
          items:
            type: string
          description: "Loop: 2210D, 2210E, Segment: SVC, Element: SVC01-3 to SVC01-6, Notes:  Procedure Modifier"
        lineItemChargeAmount:
          type: string
          description: "Loop: 2210D, 2210E, Segment: SVC, Element: SVC02, Notes: Maps to Monetary Amount, This amount is the original submitted charge."
        revenueCode:
          type: string
          description: "Loop: 2210D, 2210E, Segment: SVC, Element: SVC04, Notes: Identifying number for a product or service"
        unitsOfServiceCount:
          type: string
          description: "Loop: 2210D, 2210E, Segment: SVC, Element: SVC07, Notes: Maps to Quantity, Numeric value of quantity accepted"
        lineItemControlNumber:
          type: string
          description: "Loop: 2210D, 2210E, Segment: REF, Element: REF02 when REF01 = FJ, Notes: Maps to  Reference Identification"
        serviceLineDate:
          type: string
          description: "Loop: 2210D, 2210E, Segment: DTP, Element: DTP03, Notes: When sent with serviceLineEndDate it will be used as the start date for Date Time period, if sent without serviceLineEndDate will use DTP02 = D8"
        serviceLineEndDate:
          type: string
          description: "Loop: 2210D, 2210E, Segment: DTP, Element: DTP03, Notes: DTP03 enddate and DTP02=RD8"
  required:
    - controlNumber
    - providers
    - subscriber
BaseClaraResponse:
  $id: BaseClaraResponse
  type: object
  properties:
    _meta:
      type: object
      additionalProperties: true
    request_json_data:
      type: object
      additionalProperties: true
    response_json_data:
      type: object
      additionalProperties: true
CHCPayerSearchResponse:
  $id: CHCPayerSearchResponse
  type: object
  properties:
    start:
      type: integer
    size:
      type: integer
    total:
      type: integer
    payers:
      type: array
      items:
        type: object
        properties:
          businessname:
            type: string
          alias:
            type: string
          plantype:
            type: string
          states:
            type: string
          servicename:
            type: string
          lineofbusiness:
            type: string
          servicedate:
            type: string
            format: date-time
          serviceid:
            type: string
          clearinghouse:
            type: string
          acceptssecondary:
            type: string
          reportlevel:
            type: string
          servicemodified:
            type: string
            format: date-time
          enrollmentstatus:
            type: string
          enrollmenttype:
            type: string
          enrollmentmethod:
            type: string
          servicenotes:
            type: string
          servicenotes1:
            type: string
          parstatus:
            type: string
          passthroughfee:
            type: string
          payersearchoptionsurl:
            type: string
          enrollmentauthorizationurl:
            type: string
          cpid:
            type: string
          papercpid:
            type: string
          naicpayerid:
            type: string
          okcpayerid:
            type: string
          recid:
            type: string
          timestamp:
            type: integer
      default: []
MedicalClaimsReportsResponse:
  $id: MedicalClaimsReportsResponse
  type: object
  properties:
    original_claim:
      type: object
      $ref: "ClaraClaimTxResponse"
    reports:
      type: object
      properties:
        "835":
          type: array
          items:
            type: object
            additionalProperties: true
        "277":
          type: array
          items:
            type: object
            additionalProperties: true
        "999":
          type: array
          items:
            type: object
            additionalProperties: true
    total:
      type: integer
    limit:
      type: integer
    offset:
      type: integer