import { Transform } from "stream";
import axios from "axios";
import CSV from "csv-parser";
import unzipper from "unzipper";
import {
    directory_organization_headers,
    directory_provider_headers,
    ss_urlmap,
} from "../modules/surescripts/fixtures/maps.js";
import { SSDirectoryDB } from "../database/surescriptsdb.js";
import {
    getCustomerDetails,
    getCustomerDetailsbySID,
    getCustomerDetailsbyCID,
} from "../utils/tools.js";
import { validate_xsd } from "../modules/surescripts/xml-valparse.js";
import { ChangeRequestMed } from "../modules/claims/handlers/changereq.js";
import { PgIntegrationsDB } from "../database/pg_integrationsdb.js";
import { logger } from "../utils/logger.js";

/**
 * Handles the inbound job for Surescripts.
 *
 * @param {Object} app - The application object.
 * @param {Object} job - The job object.
 * @param {Object} logEmitter - The log emitter object.
 * @returns {Promise<Object>} - A promise that resolves to the result of the job.
 */
async function SSInboundJobHandler(app, job, logEmitter) {
    const log = logger.child({
        name: "Surescripts/InboundRequest",
        component: "SSInboundJobHandler",
    });

    try {
        const data = job.data;
        const ncpdp_to = data.message_header.To;

        log.debug(`Processing job ${job.id} with data: ${Object.keys(data)}`);

        const _meta = {};
        _meta["request_message_id"] = data["message_id"];
        _meta["request_json_data"] = JSON.parse(data["raw_json"]);
        _meta["request_xml_data"] = data["raw_xml"];
        _meta["request_hash"] = data["message_hash"];
        _meta["customer_id"] = data["customer_id"];
        _meta["customer_name"] = data["customer_name"];
        _meta["customer_env_name"] = data["customer_env_name"];

        // Lookup which customer to send to based on ncpdp_to
        let customer_data;
        try {
            customer_data = await getCustomerDetails(app, ncpdp_to);
            log.info(
                `Customer data in SS handler: ${JSON.stringify(customer_data)}`
            );
            if (!customer_data) {
                throw new Error(
                    `NCPDP ${ncpdp_to} not found assigned to a customer`
                );
            }
        } catch (error) {
            log.error(`Error fetching customer details: ${error.message}`);
            throw error;
        }
        const authkey = Buffer.from(customer_data.secrets["FLY_NES"]).toString(
            "base64"
        );
        const authhost = customer_data.secrets["FLY_ID"];
        const dest_server = `https://${authhost}.fly.dev/api/surescripts/inbound`;

        const headers = {
            Authorization: `Bearer ${authkey}`,
            "Content-Type": "application/json",
        };

        log.debug(
            `Sending data \n ${JSON.stringify(_meta)} \nto destination server: ${dest_server}`
        );
        return axios
            .post(dest_server, _meta, {
                headers: headers,
                timeout: 15000, // Wait for 15 seconds
            })
            .then((response) => {
                log.debug("Response data: " + JSON.stringify(response.data));
                if (response && response.data) {
                    const rdata = {
                        status: "success",
                        message: `Job ${job.id} completed successfully`,
                        data: `Response from destination server: ${response.data}`,
                    };
                    const options = { retryLimit: 1, retryBackoff: true };
                    const fd = {
                        _meta: _meta,
                        response_data: response.data,
                    };
                    app.pgboss.send("clara_ss_response", fd, options);
                    return rdata;
                }
            })
            .catch((err) => {
                if (err.response) {
                    // The request was made and the server responded with a status code
                    // that falls out of the range of 2xx
                    log.error(
                        "Error in response for Surescripts Request:" +
                            JSON.stringify(err.response.data)
                    );
                }
                log(
                    "Error in response for Surescripts Request:" + err.response
                );
                const clara_err = err.response;
                if (clara_err && clara_err.err) {
                    log.info(
                        `Error from Clara Destination server ${clara_err.err}`
                    );
                }
                // Instead of throwing an error, return a rejected promise
                return Promise.reject(
                    new Error(
                        `Error in axios.post: ${JSON.stringify(err)} to destination server: ${dest_server} with data: ${Object.keys(
                            _meta
                        )}}`
                    )
                );
            });
    } catch (error) {
        log.error("Error in SSInboundJobHandler:", error);
        // Instead of throwing an error, return a rejected promise
        return Promise.reject(error);
    }
}

/**
 * Handles the SSDirectory job.
 *
 * @param {Object} app - The application object.
 * @param {Object} job - The job object.
 * @param {Object} logEmitter - The log emitter object.
 * @returns {Promise<Object>} - A promise that resolves to the result of the job processing.
 * @throws {Error} - If an invalid download type is encountered.
 */
async function SSDirectoryJobHandler(app, job, logEmitter) {
    const log = logger.child({
        name: "Surescripts/DirectoryDownloader",
        component: "SSDirectoryJobHandler",
    });
    log.debug(`Processing job ${job.id} with keys : ${Object.keys(job.data)}`);
    const dl_type = job.data.type;
    let headers, dir_type;

    if (dl_type === "organization_download") {
        headers = directory_organization_headers;
        dir_type = "organization";
    } else if (dl_type === "provider_download") {
        headers = directory_provider_headers;
        dir_type = "provider";
    } else {
        throw new Error("Invalid download type");
    }
    const opts = {
        headers: headers,
        dir_type: dir_type,
    };
    const db = new SSDirectoryDB(opts, app);

    try {
        const furl = ss_urlmap["directory_download_url"].replace(
            "{directory_filename}",
            job.data.download_url
        );
        const result = await new Promise((resolve, reject) => {
            try {
                const result_import = unzip_and_parse(log, furl, headers);
                const result_import_data = db.insert_or_update(result_import);
                const rdata = {
                    status: "success",
                    message: `Job ${job.id} completed successfully`,
                    data: `Processed ${result_import.length}  and imported ${result_import_data} records`,
                };
                resolve(rdata);
            } catch (error) {
                reject(error);
            }
        });

        return await result;
    } catch (error) {
        log.error(error);
        throw new Error(error);
    }
}

/**
 * Unzips and parses a file from a given URL.
 *
 * @param {Function} log - The logging function.
 * @param {string} url - The URL of the file to be unzipped and parsed.
 * @param {Object} headers - The headers to be included in the HTTP request.
 * @returns {Promise<Array>} - A promise that resolves to an array of parsed data.
 * @throws {Error} - If there is an error during the unzipping and parsing process.
 */
async function unzip_and_parse(log, url, headers) {
    log.debug("Unzipping and parsing file from url: " + url);

    const getTransformStream = () => {
        const transform = new Transform({
            transform: (chunk, encoding, next) => {
                next(null, chunk);
            },
        });
        return transform;
    };

    const getStream = async (url, headers = {}) => {
        try {
            const res = await axios.get(url, {
                headers,
                responseType: "stream",
                decompress: true,
            });
            return res.data;
        } catch (err) {
            log.error(err);
        }
    };

    const readCsv = async (url, headers) => {
        try {
            const httpStream = await getStream(url, {
                "accept-encoding": "gzip, deflate, zip",
            });

            const transform = getTransformStream();

            const csvReadStream = httpStream
                .pipe(unzipper.ParseOne())
                .pipe(transform)
                .pipe(CSV({ headers: headers, separator: "|", escape: "\\" }));

            const results = [];

            // return a promise
            return new Promise((resolve, reject) => {
                csvReadStream.on("data", (chunk) => {
                    results.push(chunk);
                });
                csvReadStream.on("end", () => {
                    resolve(results);
                });
                csvReadStream.on("error", (err) => {
                    reject(err);
                });
            });
        } catch (err) {
            throw new Error(err);
        }
    };
    try {
        const d = await readCsv(url, headers);
        return d;
    } catch (err) {
        throw new Error(err);
    }
}

/**
 * Handles the SSClara response.
 *
 * @param {Object} app - The application object.
 * @param {Object} job - The job object.
 * @param {Object} logEmitter - The log emitter object.
 * @returns {Promise<Object>} - A promise that resolves to the result of the SSClara response handling.
 * @throws {Error} - If an error occurs during the SSClara response handling.
 */
async function SSClaraResponseHandler(app, job, logEmitter) {
    const SS_TX_URL =
        app.config.ENVIRONMENT == "prod"
            ? ss_urlmap["tx_prod"]
            : ss_urlmap["tx_staging"];
    const log = logger.child({
        name: "Surescripts/ClaraRequestHandler",
        component: "SSClaraResponseHandler",
    });
    log.debug(
        `Processing job ${job.id} with data : ${JSON.stringify(job.data)}`
    );
    try {
        const result = await new Promise((resolve, reject) => {
            try {
                const _meta = job.data._meta;
                const d = {};
                d.header = {};
                d.header.ncpdp_from_type = "P";
                d.header.ncpdp_to_type = "D";
                d.header.From = _meta.request_json_data.Message?.Header?.To;
                d.header.To = _meta.request_json_data.Message?.Header?.From;
                d.ncpdp_version = app.config.NCPDP_SCHEMA_VERSION;
                d.sent_dt = new Date().toISOString();
                d.header.message_id = app.uuid();
                d.header.RelatesToMessageID = _meta.request_message_id;
                d.status_code = "010";
                const r = app.ss_rxml("verify", d);
                log.debug("Generated return XML for verify message : " + r);
                const valid = validate_xsd(app, "transport", r);
                if (!valid) {
                    throw new Error("Invalid return XML for verify message");
                }
                const url = SS_TX_URL.replace(
                    "{message_id}",
                    d.header.message_id
                );

                const cinfo = {
                    customer_id: _meta.customer_id,
                    customer_name: _meta.customer_name,
                    customer_env_name: _meta.customer_env_name,
                };
                const res = app.ss_request(url, r, cinfo);
                log.debug(
                    `ClaraSSResponseHandler: SS resp: ${JSON.stringify(res)}`
                );
                const rdata = {
                    status: "success",
                    message: `Job ${job.id} completed successfully`,
                    data: `SS Response: ${JSON.stringify(res)}`,
                };
                resolve(rdata);
            } catch (error) {
                reject(error);
            }
        });
        return await result;
    } catch (error) {
        log.error(error);
        throw new Error(error);
    }
}

/**
 * Handles the processing of medical claim reports.
 *
 * @param {Object} app - The application object.
 * @param {Object} job - The job object.
 * @param {Object} logEmitter - The log emitter object.
 * @returns {Promise<Object>} - A promise that resolves to the result of the processing.
 * @throws {Error} - If there is an error processing the reports.
 */
async function MedicalClaimReportsHandler(app, job, logEmitter) {
    const log = logger.child({
        name: "Claims/ReportsDownloader",
        component: "ReportsDownloader",
    });
    log.debug(
        `Processing job ${job.id} with keys : ${JSON.stringify(job.data)}`
    );

    const result = await new Promise(async (resolve, reject) => {
        try {
            const data = job.data;
            for (const r of data.reports) {
                let s3Stored = false;
                let dbStored = false;
                log.info("Downloading Report: " + JSON.stringify(r));
                const url = r.raw_report_url;

                // Create request with proper metadata
                const requestData = {
                    _meta: {
                        customer_id: r._meta?.customer_id,
                        customer_name: r._meta?.customer_name,
                        customer_env_name: r._meta?.customer_env_name,
                        environment: r._meta?.request_environment || "staging",
                    },
                };

                const change_req = new ChangeRequestMed(requestData._meta, app);
                const jsonData = await change_req.simple_req(requestData, url);
                log.info(`Report summary: {
                    url: ${url},
                    dataKeys: ${Object.keys(jsonData)},
                    dataLength: ${JSON.stringify(jsonData.report_content).length}
                }`);

                log.info(
                    `fetching converted report for ${r.report_convert_url}`
                );
                const converted_report_url = r.report_convert_url;
                const converted_report_data = await change_req.simple_req(
                    requestData,
                    converted_report_url
                );

                log.info(`Convert report summary:
                    url: ${converted_report_url},
                    dataKeys: ${Object.keys(converted_report_data)},
                    dataLength: ${converted_report_data.transactions ? converted_report_data.transactions.length : 0}
                `);

                const control_numbers = converted_report_data.transactions
                    ? converted_report_data.transactions.map((t) =>
                          t.controlNumber.toString()
                      )
                    : [];

                // Add debug logging
                log.debug(
                    `Extracted control numbers: ${JSON.stringify(control_numbers)}`
                );

                const reportData = {
                    submitter_id: r.submitter_id,
                    raw_json: converted_report_data,
                    raw_x12: jsonData.report_content,
                    converted_report_url: converted_report_url,
                    report_type: r.report_type,
                    type: r.report_type,
                    raw_report_url: url,
                    control_number: control_numbers[0],
                    payer_name:
                        r.report_type == "835" &&
                        converted_report_data.transactions
                            ? converted_report_data.transactions[0].payer.name
                            : r.report_type == "277" &&
                                converted_report_data.transactions &&
                                converted_report_data.transactions[0].payers
                              ? converted_report_data.transactions[0].payers[0]
                                    .organizationName
                              : null,
                    payer_info:
                        r.report_type == "835" &&
                        converted_report_data.transactions
                            ? converted_report_data.transactions[0].payer
                            : r.report_type == "277" &&
                                converted_report_data.transactions &&
                                converted_report_data.transactions[0].payers
                              ? converted_report_data.transactions[0].payers
                              : null,
                    customer_id: r._meta?.customer_id,
                    customer_name: r._meta?.customer_name,
                    customer_env_name: r._meta?.customer_env_name,
                };

                // Add debug logging
                log.debug(
                    `Report Data being stored: ${JSON.stringify({
                        control_number: reportData.control_number,
                        report_type: reportData.report_type,
                        hasTransactions: !!converted_report_data.transactions,
                        transactionCount:
                            converted_report_data.transactions?.length,
                    })}`
                );

                let reportDataList;
                const usePg = app.config.USE_POSTGRES === "true";

                if (!usePg) {
                    throw new Error(
                        "PostgreSQL is required - SQLite fallback has been removed"
                    );
                }

                try {
                    const pgDb = new PgIntegrationsDB(app, {
                        customer_id: r._meta?.customer_id,
                        customer_name: r._meta?.customer_name,
                        customer_env_name: r._meta?.customer_env_name,
                    });

                    await pgDb.ensureSchema();

                    // Add ON CONFLICT handling in the insert_medical_claim_report method
                    reportDataList = await pgDb.insert_medical_claim_report(
                        reportData,
                        control_numbers
                    );
                    dbStored = true;
                } catch (pgError) {
                    log.error(
                        `PostgreSQL storage failed: ${JSON.stringify(pgError)}`
                    );
                    throw new Error(
                        `Failed to store data in PostgreSQL: ${pgError.message}`
                    );
                }

                if (reportDataList && reportDataList.length > 0) {
                    log.info(
                        `Inserted ${reportDataList.length} medical claim reports into the database`
                    );
                    log.info(`Uploading reports to S3`);

                    const nonDefaultReports = reportDataList.filter(
                        (report) => report.customer_name !== "default"
                    );

                    for (const report of nonDefaultReports) {
                        const base_path = report.customer_name
                            ? `claims/medical/${report.customer_name}`
                            : `claims/medical/${report.control_number}`;
                        const fname = `${report.control_number}_${report.report_type}.x12`;
                        const full_path = `${base_path}/${fname}`;
                        const s3_exists = await app.s3.fileExists(full_path);
                        s3Stored = s3_exists.exists;
                        const fhash = Buffer.from(full_path).toString("base64");

                        if (!s3Stored) {
                            if (!report.raw_x12) {
                                log.warn(
                                    `No raw X12 data found for report ${report.control_number}, skipping S3 upload`
                                );
                                continue;
                            }

                            try {
                                const filebuf = Buffer.from(
                                    report.raw_x12,
                                    "utf-8"
                                );
                                const opts = {
                                    filename: fname,
                                    contentType: "application/edi-x12",
                                    base_path: base_path,
                                    customer_id: report.customer_id,
                                    filehash: fhash,
                                };
                                await app.s3.put(opts, filebuf);
                                s3Stored = true;
                                log.info(`Uploaded report to S3: ${full_path}`);
                            } catch (s3Error) {
                                log.error(
                                    `Failed to upload to S3: ${s3Error.message}`
                                );
                                continue;
                            }
                        }

                        // Update database with S3 information
                        const updateData = {
                            s3_filepath: full_path,
                            s3_filehash: fhash,
                            control_number: report.control_number.toString(),
                            report_type: report.report_type,
                            type: report.report_type,
                            customer_id: report.customer_id,
                            customer_name: report.customer_name,
                        };

                        try {
                            const pgDb = new PgIntegrationsDB(app, {
                                customer_id: report.customer_id,
                                customer_name: report.customer_name,
                                customer_env_name: report.customer_env_name,
                            });

                            const updateResult =
                                await pgDb.update_medical_claim_report(
                                    updateData
                                );
                            log.info(
                                `PostgreSQL update result: ${JSON.stringify(
                                    updateResult
                                )}`
                            );

                            if (!updateResult) {
                                throw new Error(
                                    `Failed to update PostgreSQL record for control number: ${report.control_number}`
                                );
                            }

                            log.info(
                                `Successfully updated PostgreSQL record for control number: ${report.control_number}`
                            );
                        } catch (pgError) {
                            log.error(
                                `PostgreSQL update failed: ${pgError.message}`
                            );
                            throw pgError;
                        }

                        // Verify the update in the active database
                        if (dbStored && s3Stored) {
                            log.info(
                                `Successfully stored report for control number: ${report.control_number.toString()} in database and S3. Deleting report from change mailbox...`
                            );

                            // Restore the report deletion functionality
                            const del_resp = await change_req.simple_req(
                                {},
                                report.raw_report_url,
                                "delete"
                            );
                            if (del_resp.status == "success") {
                                log.info(
                                    `Deleted report from change mailbox: ${report.raw_report_url}`
                                );
                            }

                            // Continue with report delivery if needed
                            if (
                                report.customer_name &&
                                report.customer_name !== "default"
                            ) {
                                const pgDb = new PgIntegrationsDB(app, {
                                    customer_id: report.customer_id,
                                    customer_name: report.customer_name,
                                    customer_env_name: report.customer_env_name,
                                });
                                // dont deliver report if it has clara_delivered set to true
                                const report_delivered =
                                    await pgDb.get_medical_claim_report_delivered(
                                        report.control_number,
                                        report.report_type
                                    );
                                if (
                                    report_delivered?.delivered &&
                                    report_delivered?.delivered_dt
                                ) {
                                    log.info(
                                        `Report ${report.control_number} already delivered at ${report_delivered?.delivered_dt}, skipping delivery`
                                    );
                                    continue;
                                }
                                const options = {
                                    retryLimit: 1,
                                    retryBackoff: true,
                                };
                                const deliveryData = {
                                    ...reportData,
                                    customer_id: report.customer_id,
                                    customer_name: report.customer_name,
                                    submitter_id: report.submitter_id,
                                    s3_filehash: fhash,
                                };

                                const jobid = await app.pgboss.send(
                                    "report_delivery_manager",
                                    deliveryData,
                                    options
                                );
                                log.info(
                                    `Created job to deliver report to Clara: ${jobid}`
                                );
                            }
                        } else {
                            log.error(
                                `Failed to store report in any database for control number: ${report.control_number}`
                            );
                        }
                    }
                }
            }
            resolve({
                status: "success",
                message: `Job ${job.id} completed successfully`,
                data: `Processed ${data.reports.length} reports`,
            });
        } catch (error) {
            log.error("Error processing reports:");
            log.error(error.stack || error);
            reject(
                new Error(
                    `Error processing reports: ${error.message}\n${error.stack}`
                )
            );
        }
    });
    return result;
}

async function ReportsDeliveryManager(app, job, logEmitter) {
    const log = logger.child({
        name: "Claims/ReportsDeliveryManager",
        component: "ReportsDeliveryManager",
    });
    log.info(`ReportsDeliveryManager: Processing job ${job.id}`);
    log.debug(
        `ReportsDeliveryManager: Processing job ${job.id} with data: ${JSON.stringify(
            job.data
        )}`
    );
    try {
        const data = job.data;
        log.debug(
            `ReportsDeliveryManager: Processing job ${job.id} with data: ${JSON.stringify(
                data
            )}`
        );

        let customer_data;
        if (!data.customer_id) {
            throw new Error("Customer ID is required");
        }

        try {
            if (data.customer_id) {
                customer_data = await getCustomerDetailsbyCID(
                    app,
                    data.customer_id
                );
            } else if (data.submitter_id) {
                customer_data = await getCustomerDetailsbySID(
                    app,
                    data.submitter_id
                );
            } else {
                throw new Error("Customer ID or Submitter ID is required");
            }

            if (!customer_data) {
                throw new Error(`Customer ID ${data.customer_id} not found`);
            }
        } catch (error) {
            app.log.error(`Error fetching customer details: ${error.message}`);
            throw error;
        }

        const authkey = Buffer.from(customer_data.secrets["FLY_NES"]).toString(
            "base64"
        );
        const authhost = customer_data.secrets["FLY_ID"];
        const dest_server = `https://${authhost}.fly.dev/api/billing/medical/reports`;

        const headers = {
            Authorization: `Bearer ${authkey}`,
            "Content-Type": "application/json",
        };

        // Extract patient information based on report type
        let patientId = null;
        let patientFirstName = null;
        let patientLastName = null;
        let dependentFirstName = null;
        let dependentLastName = null;
        let dependentId = null;

        if (data.report_type === "277") {
            // For 277 reports, check the patientClaimStatusDetails path
            const patientInfo =
                data.raw_json?.transactions?.[0]?.payers?.[0]
                    ?.claimStatusTransactions?.[0]?.claimStatusDetails?.[0]
                    ?.patientClaimStatusDetails?.[0];

            patientId =
                patientInfo?.subscriber?.memberId ||
                data.raw_json?.transactions?.[0]?.patientMemberId;
            patientFirstName = patientInfo?.subscriber?.firstName;
            patientLastName = patientInfo?.subscriber?.lastName;
            dependentFirstName = patientInfo?.dependent?.firstName;
            dependentLastName = patientInfo?.dependent?.lastName;
            dependentId = patientInfo?.dependent?.memberId;
        } else if (data.report_type === "835") {
            // For 835 reports, check the NM1 segments
            const claimInfo = data.raw_json?.transactions?.[0];
            const patientSegment =
                claimInfo?.claims?.[0]?.patientInfo || claimInfo?.beneficiary;

            patientId =
                patientSegment?.memberId ||
                data.raw_json?.transactions?.[0]?.patientMemberId;
            patientFirstName = patientSegment?.firstName;
            patientLastName = patientSegment?.lastName;
        }

        // Create payload with all fields
        const payload = {
            ...data,
            patient_id: patientId,
            patient_first_name: patientFirstName,
            patient_last_name: patientLastName,
            dependent_id: dependentId,
            dependent_first_name: dependentFirstName,
            dependent_last_name: dependentLastName,
        };

        // Remove any keys with null values
        Object.keys(payload).forEach((key) => {
            if (payload[key] === null) {
                delete payload[key];
            }
        });

        log.info(`Sending data to destination server: ${dest_server}`);
        log.debug(`Payload: object keys: ${Object.keys(payload)}`);
        const stopfetching_control_numbers = [];
        try {
            const response = await axios.post(dest_server, payload, {
                headers: headers,
                timeout: 15000,
            });
            log.debug(
                `ReportsDeliveryManager: Response from ${dest_server}: ${response?.status} : ${JSON.stringify(
                    response?.data
                )}`
            );
            if (response?.data) {
                if (
                    response?.status == 200 &&
                    (response?.data?.status == "success" ||
                        response?.data?.error ==
                            "Duplicate transaction found" ||
                        (response?.data?.stopFetching &&
                            response?.data?.stopFetching.length >= 0))
                ) {
                    if (
                        response?.data?.stopFetching &&
                        response?.data?.stopFetching.length >= 0
                    ) {
                        log.info(
                            `Stop fetching for control number: ${payload.control_number} received from Clara`
                        );
                        stopfetching_control_numbers.push(
                            payload.control_number
                        );
                        payload.stopFetching = true;
                        payload.stopFetching_control_numbers =
                            stopfetching_control_numbers;
                    }

                    try {
                        const pgDb = new PgIntegrationsDB(app, {
                            customer_id: payload.customer_id,
                            customer_name: payload.customer_name,
                            customer_env_name: payload.customer_env_name,
                        });

                        await pgDb.ensureSchema();
                        // Add ON CONFLICT handling in the
                        await pgDb.update_medical_claim_report_delivered(
                            payload
                        );
                    } catch (pgError) {
                        log.error(
                            `PostgreSQL update for report delivery failed: ${pgError.message}`
                        );
                        throw pgError;
                    }
                    return {
                        status: "success",
                        message: `Job ${job.id} completed successfully`,
                        data: response.data,
                    };
                } else {
                    throw new Error(
                        `ReportsDeliveryManager: Error from ${dest_server}: ${response?.status} : ${JSON.stringify(
                            response?.data
                        )}`
                    );
                }
            }
        } catch (err) {
            // Handle axios error without circular references
            const errorResponse = {
                status: err.response?.status,
                statusText: err.response?.statusText,
                data: err.response?.data,
                message: err.message,
            };

            log.error(
                `Error in ReportsDeliveryManager: ${JSON.stringify(errorResponse)}`
            );
            throw new Error(JSON.stringify(errorResponse));
        }
    } catch (error) {
        log.error(`Error in ReportsDeliveryManager: ${error.message}`);
        return Promise.reject(new Error(error.message));
    }
}
export {
    SSInboundJobHandler,
    SSDirectoryJobHandler,
    SSClaraResponseHandler,
    MedicalClaimReportsHandler,
    ReportsDeliveryManager,
};
