import { MedicalClaimReportsHandler } from "./inbound-pgboss-handler.js";
// Create a simple scheduler for the medical claim reports download to trigger a fetch.

async function scheduledReportsFetch(app, logEmitter) {
    const boss = app.pg.boss;
    const consumer_opts = { teamSize: 1, teamConcurrency: 1 };

    await boss.schedule("scheduled_reports_fetch", "every 1 hour", {
        teamSize: 1,
        teamConcurrency: 1,
    });

    await boss.on("scheduled_reports_fetch", async (job) => {
        try {
            logEmitter.info(
                `job ${job.id} for scheduled_reports_fetch received with data ${Object.keys(
                    job.data
                )} `
            );
            const jobResult = await MedicalClaimReportsHandler(
                app,
                job,
                logEmitter
            );
            logEmitter.info(
                `job ${job.id} completed with data: ${JSON.stringify(jobResult)}`
            );
        } catch (error) {
            logEmitter.error(`job ${job.id} failed with error: ${error}`);
            job.fail(error);
        }
    });
}
