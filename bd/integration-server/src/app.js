import fastify from "fastify";
import autoload from "@fastify/autoload";
import path from "path";
import Sensible from "@fastify/sensible";
import qsPlugin from "fastify-qs";
import fastifySwagger from "@fastify/swagger";
import fastifySwaggerUi from "@fastify/swagger-ui";
import fastifyPrintRoutes from "fastify-print-routes";
import fs from "fs";
import YAML from "yaml";
import { PgIntegrationsDB } from "./database/pg_integrationsdb.js";
import { PgMigrations } from "./database/pg_migrations.js";
import crypto from "node:crypto";
import { validateFipsMode } from "./utils/tools.js";
import { logger } from "./utils/logger.js";

export default async function build(opts) {
    const appOpts = { ...opts };

    // Ensure log directory exists
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }

    appOpts.logger = logger;

    const app = fastify(appOpts);
    const schemas = YAML.parse(
        fs.readFileSync("src/schemas/route_providers.yml", "utf8")
    );
    if (schemas) {
        for (const [key, schema] of Object.entries(schemas)) {
            app.log.info("loading schema: " + key);
            app.addSchema(schema);
            app.log.info("loaded schema: " + key);
        }
    }
    await registerDecorators(app);
    await registerPlugins(app);
    await registerModules(app);

    try {
        const fipsStatus = validateFipsMode();
        app.log.info(`FIPS mode status: ${fipsStatus.message}`);
        if (fipsStatus.error) {
            app.log.warn(`FIPS error details: ${fipsStatus.error}`);
        }
    } catch (error) {
        // Fatal error if FIPS was required but couldn't be enabled
        app.log.error(error.message);
    }
    app.addHook("preValidation", async (request, reply) => {
        let meta = request.body?._meta;
        if (!meta) {
            meta = {};
        }
        app.log.info("request.headers: " + JSON.stringify(request.headers));
        if (!meta.request_environment) {
            meta.request_environment = request?.headers?.host?.includes(
                "is-prod"
            )
                ? "prod"
                : "staging";
        }
        request.body = { ...request.body, _meta: meta };
    });

    app.addHook("onReady", async () => {
        if (app.config.USE_POSTGRES === "true") {
            try {
                await PgIntegrationsDB.initializeAllSchemas(app);
                app.log.info("PostgreSQL schemas initialized");
                const migrations = new PgMigrations(app);
                await migrations.runMigrations();
                app.log.info("PostgreSQL migrations completed");
            } catch (err) {
                app.log.error("Failed to initialize PostgreSQL schemas", err);
            }
        }
    });

    return app;
}

async function registerPlugins(app, opts) {
    await app.register(fastifyPrintRoutes);
    await app.register(Sensible);
    await app.register(qsPlugin);
    await app.register(autoload, {
        dir: app.plugin_root,
        options: { ...opts },
        encapsulate: false,
    });
}

async function registerDecorators(app) {
    // Register global utilities
    app.decorate("app_root", path.resolve() + "/src");
    app.decorate("app_logdir", path.resolve() + "/logs");
    app.decorate("module_root", path.resolve() + "/src" + "/modules");
    app.decorate("plugin_root", path.resolve() + "/src" + "/plugins");
    app.decorate("db_root", path.resolve() + "/src" + "/database/");
}

async function registerModules(app, opts) {
    const swaggerOptions = {
        openapi: {
            info: {
                title: "ClaraRX Integration Server",
                description: "ClaraRX Integration Server API Documentation",
                version: "1.0.0",
            },
            servers: [
                {
                    url: "http://localhost:3000",
                    description: "Development server",
                },
            ],
            components: {
                securitySchemes: {
                    apiKey: {
                        type: "Bearer",
                        name: "Authorization",
                        in: "header",
                    },
                },
            },
        },
        hideUntagged: false,
        exposeRoute: true,
    };

    const swaggerUiOptions = {
        routePrefix: "/docs",
        exposeRoute: true,
        deepLinking: true,
    };
    app.register(fastifySwagger, swaggerOptions);
    app.register(fastifySwaggerUi, swaggerUiOptions);
    await app.register(autoload, {
        dir: app.module_root,
        options: { ...opts },
        maxDepth: 1,
    });
    await app.register(fastifyPrintRoutes);
}
