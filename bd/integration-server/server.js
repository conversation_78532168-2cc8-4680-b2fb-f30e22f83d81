import build from "./src/app.js";
import { logger } from "./src/utils/logger.js";
import ajvKeywords from "ajv-keywords";

/**
 * Initializes the application server.
 * @returns {Promise<void>} A promise that resolves when the server is initialized.
 */
async function initAppServer() {
    try {
        const options = {
            logger,
            ajv: {
                customOptions: {
                    removeAdditional: true,
                    coerceTypes: true,
                    addUsedSchema: false,
                    allErrors: true,
                },
                plugins: [ajvKeywords],
            },
        };

        const app = await build(options);

        // Add error event handlers
        process.on("uncaughtException", (err) => {
            logger.fatal({ err }, "Uncaught Exception");
            process.exit(1);
        });

        process.on("unhandledRejection", (reason, promise) => {
            logger.fatal({ err: reason, promise }, "Unhandled Rejection");
            process.exit(1);
        });

        await app.listen({
            host: app.config.HTTP_HOST,
            port: app.config.HTTP_PORT,
        });

        logger.info(
            {
                event: "server_start",
                host: app.config.HTTP_HOST,
                port: app.config.HTTP_PORT,
            },
            "Server started successfully"
        );
    } catch (err) {
        // Log the full error details
        logger.error({
            msg: "Failed to initialize server",
            error: err.message,
            stack: err.stack,
            code: err.code,
            details: err
        });
        process.exit(1);
    }
}

// Start the server
initAppServer();
