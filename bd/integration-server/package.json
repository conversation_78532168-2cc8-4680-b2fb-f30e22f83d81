{"name": "integration-server", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "start": "nodemon server.js", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@apidevtools/json-schema-ref-parser": "^11.7.0", "@aws-sdk/client-s3": "^3.572.0", "@fastify/autoload": "^5.8.0", "@fastify/bearer-auth": "^9.4.0", "@fastify/env": "^4.3.0", "@fastify/postgres": "^5.2.2", "@fastify/sensible": "^5.5.0", "@fastify/soap-client": "^2.2.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^3.0.0", "@google-cloud/documentai": "^8.10.0", "@openapi-contrib/openapi-schema-to-json-schema": "^5.1.0", "@rgrove/parse-xml": "^4.1.0", "a-sync-waterfall": "^1.0.1", "abbrev": "^1.1.1", "abort-controller": "^3.0.0", "abstract-logging": "^2.0.1", "agent-base": "^6.0.2", "agentkeepalive": "^4.5.0", "aggregate-error": "^3.1.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "anymatch": "^3.1.3", "append-transform": "^2.0.0", "aproba": "^2.0.0", "archy": "^1.0.0", "are-we-there-yet": "^2.0.0", "argparse": "^1.0.10", "asap": "^2.0.6", "asn1": "^0.2.6", "assert-plus": "^1.0.0", "async-hook-domain": "^2.0.4", "asynckit": "^0.4.0", "atomic-sleep": "^1.0.0", "avvio": "^8.2.1", "aws-sign2": "^0.7.0", "aws4": "^1.12.0", "axios": "^1.6.8", "axios-debug-log": "^1.0.0", "axios-ntlm": "^1.4.2", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "bcrypt-pbkdf": "^1.0.2", "better-sqlite3": "^9.3.0", "binary-extensions": "^2.2.0", "bind-obj-methods": "^3.0.0", "bindings": "^1.5.0", "bl": "^4.1.0", "brace-expansion": "^2.0.1", "braces": "^3.0.2", "browserslist": "^4.22.2", "buffer": "^6.0.3", "buffer-from": "^1.1.2", "buffer-writer": "^2.0.0", "bytes": "^3.1.2", "cacache": "^15.3.0", "caching-transform": "^4.0.0", "call-bind": "^1.0.7", "camelcase": "^5.3.1", "caniuse-lite": "^1.0.30001579", "caseless": "^0.12.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "chownr": "^2.0.0", "clean-stack": "^2.2.0", "cliui": "^7.0.4", "close-with-grace": "^1.2.0", "color-convert": "^2.0.1", "color-name": "^1.1.4", "color-support": "^1.1.3", "colorette": "^2.0.20", "combined-stream": "^1.0.8", "commander": "^5.1.0", "commist": "^3.2.0", "commondir": "^1.0.1", "concat-map": "^0.0.1", "console-control-strings": "^1.1.0", "convert-source-map": "^1.9.0", "cookie": "^0.5.0", "core-util-is": "^1.0.2", "cron-parser": "^4.9.0", "cross-spawn": "^7.0.3", "csv-parser": "^3.0.0", "dashdash": "^1.14.1", "dateformat": "^4.6.3", "debug": "^4.3.4", "decamelize": "^1.2.0", "decompress-response": "^6.0.0", "deep-extend": "^0.6.0", "default-require-extensions": "^3.0.1", "define-data-property": "^1.1.4", "delay": "^5.0.0", "delayed-stream": "^1.0.0", "delegates": "^1.0.0", "depd": "^2.0.0", "des.js": "^1.1.0", "desm": "^1.3.1", "detect-libc": "^2.0.2", "dev-null": "^0.1.1", "dezalgo": "^1.0.4", "diff": "^4.0.2", "dotenv": "^16.3.2", "dotenv-expand": "^10.0.0", "ecc-jsbn": "^0.1.2", "electron-to-chromium": "^1.4.642", "emoji-regex": "^8.0.0", "encoding": "^0.1.13", "end-of-stream": "^1.4.4", "env-paths": "^2.2.1", "env-schema": "^5.2.1", "err-code": "^2.0.3", "es-define-property": "^1.0.0", "es-errors": "^1.3.0", "es6-error": "^4.1.1", "escalade": "^3.1.1", "escape-string-regexp": "^2.0.0", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsonc": "^2.16.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-yaml": "^1.0.3", "eslint-plugin-yml": "^1.14.0", "esprima": "^4.0.1", "event-target-shim": "^5.0.1", "events": "^3.3.0", "events-to-array": "^1.1.2", "expand-template": "^2.0.3", "extend": "^3.0.2", "extsprintf": "^1.3.0", "fast-content-type-parse": "^1.1.0", "fast-copy": "^3.0.1", "fast-decode-uri-component": "^1.0.1", "fast-deep-equal": "^3.1.3", "fast-json-stable-stringify": "^2.1.0", "fast-json-stringify": "^5.10.0", "fast-querystring": "^1.1.2", "fast-redact": "^3.3.0", "fast-safe-stringify": "^2.1.1", "fast-uri": "^2.3.0", "fast-xml-parser": "^4.3.3", "fastify": "^4.25.2", "fastify-cli": "^5.9.0", "fastify-plugin": "^4.5.1", "fastify-print-routes": "^3.2.0", "fastify-qs": "^4.0.2", "fastify-raw-body": "^4.3.0", "fastq": "^1.16.0", "file-uri-to-path": "^1.0.0", "fill-range": "^7.0.1", "find-cache-dir": "^3.3.2", "find-my-way": "^7.7.0", "find-up": "^3.0.0", "findit": "^2.0.0", "follow-redirects": "^1.15.5", "foreground-child": "^2.0.0", "forever-agent": "^0.6.1", "form-data": "^4.0.0", "formidable": "^2.1.2", "forwarded": "^0.2.0", "fromentries": "^1.3.2", "fs-constants": "^1.0.0", "fs-exists-cached": "^1.0.0", "fs-minipass": "^2.1.0", "fs.realpath": "^1.0.0", "function-bind": "^1.1.2", "function-loop": "^2.0.1", "gauge": "^3.0.2", "generify": "^4.2.0", "gensync": "^1.0.0-beta.2", "get-caller-file": "^2.0.5", "get-intrinsic": "^1.2.4", "get-package-type": "^0.1.0", "get-stream": "^6.0.1", "getpass": "^0.1.7", "github-from-package": "^0.0.0", "glob": "^8.1.0", "glob-parent": "^5.1.2", "globals": "^11.12.0", "globalyzer": "^0.1.0", "globrex": "^0.1.2", "gopd": "^1.0.1", "graceful-fs": "^4.2.11", "har-schema": "^2.0.0", "har-validator": "^5.1.5", "has-flag": "^4.0.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.0.3", "has-symbols": "^1.0.3", "has-unicode": "^2.0.1", "hasha": "^5.2.2", "hasown": "^2.0.1", "help-me": "^4.2.0", "hexoid": "^1.0.0", "html-escaper": "^2.0.2", "http-cache-semantics": "^4.1.1", "http-errors": "^2.0.0", "http-proxy-agent": "^4.0.1", "http-signature": "^1.2.0", "httpntlm": "^1.8.13", "httpreq": "^1.1.1", "https-proxy-agent": "^5.0.1", "humanize-ms": "^1.2.1", "iconv-lite": "^0.4.24", "ieee754": "^1.2.1", "ignore-by-default": "^1.0.1", "imurmurhash": "^0.1.4", "indent-string": "^4.0.0", "infer-owner": "^1.0.4", "inflight": "^1.0.6", "inherits": "^2.0.4", "ini": "^1.3.8", "ip": "^2.0.0", "ipaddr.js": "^1.9.1", "is-binary-path": "^2.1.0", "is-docker": "^2.2.1", "is-extglob": "^2.1.1", "is-fullwidth-code-point": "^3.0.0", "is-glob": "^4.0.3", "is-lambda": "^1.0.1", "is-number": "^7.0.0", "is-stream": "^2.0.1", "is-typedarray": "^1.0.0", "is-windows": "^1.0.2", "isbinaryfile": "^4.0.10", "isexe": "^2.0.0", "isstream": "^0.1.2", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-hook": "^3.0.0", "istanbul-lib-instrument": "^4.0.3", "istanbul-lib-processinfo": "^2.0.3", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.6", "jackspeak": "^1.4.2", "jest": "^29.7.0", "joycon": "^3.1.1", "js-md4": "^0.3.2", "js-tokens": "^4.0.0", "js-yaml": "^3.14.1", "jsbn": "^0.1.1", "jsesc": "^2.5.2", "json-schema": "^0.4.0", "json-schema-ref-resolver": "^1.0.1", "json-schema-traverse": "^1.0.0", "json-stringify-safe": "^5.0.1", "json5": "^2.2.3", "jsprim": "^1.4.2", "libtap": "^1.4.1", "libxmljs2": "^0.33.0", "light-my-request": "^5.11.0", "locate-path": "^3.0.0", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.flattendeep": "^4.4.0", "lru-cache": "^6.0.0", "luxon": "^3.4.4", "make-dir": "^3.1.0", "make-fetch-happen": "^9.1.0", "make-promises-safe": "^5.1.0", "makeerror": "^1.0.12", "media-typer": "^0.3.0", "mime": "^3.0.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mimic-response": "^3.1.0", "minimalistic-assert": "^1.0.1", "minimatch": "^5.1.6", "minimist": "^1.2.8", "minipass": "^3.3.6", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.4.1", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2", "mkdirp": "^1.0.4", "mkdirp-classic": "^0.5.3", "moment": "^2.30.1", "ms": "^2.1.2", "napi-build-utils": "^1.0.2", "negotiator": "^0.6.3", "node-abi": "^3.54.0", "node-addon-api": "^7.1.0", "node-fetch": "^2.7.0", "node-gyp": "^8.4.1", "node-preload": "^0.2.1", "node-releases": "^2.0.14", "node-soap": "^1.0.0", "node-stream-zip": "^1.15.0", "nodemon": "^3.0.3", "nopt": "^5.0.0", "normalize-path": "^3.0.0", "npmlog": "^5.0.1", "nunjucks": "^3.2.4", "nyc": "^15.1.0", "oauth-sign": "^0.9.0", "object-assign": "^4.1.1", "object-inspect": "^1.13.1", "on-exit-leak-free": "^2.1.2", "once": "^1.4.0", "openai": "^4.40.0", "openapi-typescript": "^5.4.1", "opener": "^1.5.2", "own-or": "^1.0.0", "own-or-env": "^1.0.2", "p-limit": "^2.3.0", "p-locate": "^3.0.0", "p-map": "^3.0.0", "p-try": "^2.2.0", "package-hash": "^4.0.0", "packet-reader": "^1.0.0", "papaparse": "^5.4.1", "path-exists": "^3.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "pdf-img-convert": "^1.2.1", "performance-now": "^2.1.0", "pg": "^8.11.3", "pg-boss": "^9.0.3", "pg-cloudflare": "^1.1.1", "pg-connection-string": "^2.6.2", "pg-int8": "^1.0.1", "pg-pool": "^3.6.1", "pg-protocol": "^1.6.0", "pg-types": "^2.2.0", "pgpass": "^1.0.5", "picocolors": "^1.0.0", "picomatch": "^2.3.1", "pino": "^8.17.2", "pino-abstract-transport": "^1.1.0", "pino-pretty": "^10.3.1", "pino-std-serializers": "^6.2.2", "pkg-dir": "^4.2.0", "pkg-up": "^3.1.0", "postgres-array": "^2.0.0", "postgres-bytea": "^1.0.0", "postgres-date": "^1.0.7", "postgres-interval": "^1.2.0", "prebuild-install": "^7.1.1", "process": "^0.11.10", "process-on-spawn": "^1.0.0", "process-warning": "^3.0.0", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "proxy-addr": "^2.0.7", "proxy-from-env": "^1.1.0", "psl": "^1.9.0", "pstree.remy": "^1.1.8", "pump": "^3.0.0", "punycode": "^2.3.1", "qs": "^6.11.2", "quick-format-unescaped": "^4.0.4", "raw-body": "^2.5.2", "rc": "^1.2.8", "readable-stream": "^3.6.2", "readdirp": "^3.6.0", "real-require": "^0.2.0", "release-zalgo": "^1.0.0", "request": "^2.88.2", "require-directory": "^2.1.1", "require-from-string": "^2.0.2", "require-main-filename": "^2.0.0", "resolve-from": "^5.0.0", "ret": "^0.2.2", "retry": "^0.12.0", "reusify": "^1.0.4", "rfdc": "^1.3.1", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "safe-regex2": "^2.0.0", "safe-stable-stringify": "^2.4.3", "safer-buffer": "^2.1.2", "sax": "^1.3.0", "secure-json-parse": "^2.7.0", "semver": "^7.5.4", "serialize-error": "^8.1.0", "set-blocking": "^2.0.0", "set-cookie-parser": "^2.6.0", "set-function-length": "^1.2.1", "setprototypeof": "^1.2.0", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "side-channel": "^1.0.5", "signal-exit": "^3.0.7", "simple-concat": "^1.0.1", "simple-get": "^4.0.1", "simple-update-notifier": "^2.0.0", "smart-buffer": "^4.2.0", "soap": "^0.45.0", "socks": "^2.7.1", "socks-proxy-agent": "^6.2.1", "sonic-boom": "^3.8.0", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "spawn-wrap": "^2.0.0", "split2": "^3.2.2", "sprintf-js": "^1.0.3", "sqlite3": "^5.1.7", "sshpk": "^1.18.0", "ssri": "^8.0.1", "stack-utils": "^2.0.6", "statuses": "^2.0.1", "string-width": "^4.2.3", "string_decoder": "^1.3.0", "strip-ansi": "^6.0.1", "strip-bom": "^4.0.0", "strip-json-comments": "^3.1.1", "strnum": "^1.0.5", "supports-color": "^7.2.0", "tap-mocha-reporter": "^5.0.4", "tap-parser": "^11.0.2", "tap-yaml": "^1.0.2", "tar": "^6.2.0", "tar-fs": "^2.1.1", "tar-stream": "^2.2.0", "tcompare": "^5.0.7", "tesseract.js": "^5.1.1", "test-exclude": "^6.0.0", "thread-stream": "^2.4.1", "tiny-glob": "^0.2.9", "tmpl": "^1.0.5", "to-fast-properties": "^2.0.0", "to-regex-range": "^5.0.1", "toad-cache": "^3.7.0", "toidentifier": "^1.0.1", "touch": "^3.1.0", "tough-cookie": "^2.5.0", "tr46": "^0.0.3", "trivial-deferred": "^1.1.2", "tunnel-agent": "^0.6.0", "tweetnacl": "^0.14.5", "type-fest": "^0.8.1", "type-is": "^1.6.18", "typedarray-to-buffer": "^3.1.5", "undefsafe": "^2.0.5", "underscore": "^1.12.1", "undici": "^5.28.3", "undici-types": "^5.26.5", "unicode-length": "^2.1.0", "unique-filename": "^1.1.1", "unique-slug": "^2.0.2", "unpipe": "^1.0.0", "unzipper": "^0.11.5", "update-browserslist-db": "^1.0.13", "uri-js": "^4.4.1", "util-deprecate": "^1.0.2", "uuid": "^9.0.1", "vary": "^1.1.2", "verror": "^1.10.0", "walker": "^1.0.8", "webidl-conversions": "^3.0.1", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^5.0.0", "which": "^2.0.2", "which-module": "^2.0.1", "wide-align": "^1.1.5", "wrap-ansi": "^7.0.0", "wrappy": "^1.0.2", "write-file-atomic": "^3.0.3", "xml-beautifier": "^0.5.0", "xml-crypto": "^2.1.5", "xml-formatter": "^3.6.2", "xml-parser-xo": "^4.1.1", "xml2js": "^0.6.2", "xpath": "^0.0.32", "xtend": "^4.0.2", "y18n": "^4.0.3", "yallist": "^4.0.0", "yaml": "^1.10.2", "yargs": "^15.4.1", "yargs-parser": "^21.1.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/preset-env": "^7.25.3", "babel-jest": "^29.7.0", "prettier": "3.2.5", "tap": "^16.3.10"}, "keywords": [], "author": "", "license": "ISC"}