#/bin/bash

set -e

if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then

    # NOTE: We want to run different scripts depending on container
    # Docker local dev: Run watchers
    # Fly.io Dev/Test : Run rsync daemon

    if [ -d "/root/bd/integration-server" ]; then
        # Rsyncs files from host (developer machine) to guest (docker)
        # NOTE: This will gracefully exit if not running under docker local dev mode
        /opt/clara/integration-server/container/exec/local.watcher &
    else
        # runs on port 873 by default
        rsync --daemon --config /opt/clara/integration-server/container/config/rsync/rsyncd.conf
    fi
fi

cron


export OPENSSL_CONF=/usr/local/ssl/openssl.cnf
export OPENSSL_MODULES=/usr/local/lib/ossl-modules
export OPENSSL_FIPS=1

pm2-runtime start /opt/clara/integration-server/container/config/pm2/ecosystem.config.cjs --env $NODE_ENV
