#!/bin/bash

# Manually maintain the sync folder list to be same as:
#   bd/Dockerfile
#   bd/cli/src/commands/fly/watch.ts

# Manually update the exclude list to handle same cases as:
#   bd/.dockerignore
#   bd/.gitignore
#   bd/**/.gitignore
#   bd/cli/src/commands/fly/watch.ts

# NOTE: rsync -R (relative path) is required here

if [ -d "/root/bd/integration-server" ]; then
    inotifywait -m -r -e modify,create,delete --format '%w%f' \
        /root/bd/{cli,integration-server} \
    | while read file; do
        echo "$file" >> /var/log/clara/watcher.log
        if [ -z "$sync_pid" ] || ! kill -0 "$sync_pid" 2>/dev/null; then
            (sleep 0.5 && \
            echo "Clara Dev sync..." && \
            cd /root/bd && \
            rsync -vaziR --delete --force \
                --exclude '.cache' \
                --exclude '.DS_Store' \
                --exclude '.git' \
                --exclude '.parcel-cache' \
                --exclude '.tmp' \
                --exclude 'dist' \
                --exclude 'node_modules' \
                --exclude '**.db' \
                --exclude '**/logs/**' \
                --exclude 'logs/**' \
                cli/ \
                integration-server/ \
                /opt/clara/ |& tee -a /var/log/clara/watcher.log
            ) &
            sync_pid=$!
        fi
    done &
fi

exit 0