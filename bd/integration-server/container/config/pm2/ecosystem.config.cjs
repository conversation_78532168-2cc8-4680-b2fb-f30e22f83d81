const process = require("node:process");
const console = require("node:console");
const node_mode = process.env.NODE_ENV || "development";

// ONLY specific mode
const dev_mode = node_mode == "development";
const test_mode = node_mode == "testing";
const stage_mode = node_mode == "staging";
const prod_mode = node_mode == "production";
let envs = {};
if (prod_mode) {
    envs = {
        OPENSSL_CONF: "/usr/local/ssl/openssl.cnf",
        OPENSSL_MODULES: "/usr/local/lib/ossl-modules",
        OPENSSL_FIPS: "1",
    };
}
console.log("Starting PM2 in " + node_mode + " mode.");

module.exports = [
    {
        name: "integration-server",
        cwd: "/opt/clara/integration-server",
        script: "server.js",
        args: "--enable-fips",
        exec_mode: "fork",
        error_file: "/var/log/clara/is-error.log",
        out_file: "/var/log/clara/is-out.log",
        instances: Math.max(
            1,
            stage_mode || prod_mode
                ? -1
                : "FLY_MACHINE_ID" in process.env
                  ? 2
                  : 1,
        ),
        env: envs,
        watch: dev_mode || test_mode,
        watch_delay: 500,
        max_memory_restart: prod_mode ? "1280M" : "640M",
        exp_backoff_restart_delay: 100,
        ignore_watch: [
            "__mocks__",
            "__tests__",
            "node_modules",
            "package.json",
            "pnpm-lock.yaml",
            "src/database/*.db*",
            "src/database/**/*.db*",
            "logs/*",
            "**/logs/*",
        ],
    },
];
