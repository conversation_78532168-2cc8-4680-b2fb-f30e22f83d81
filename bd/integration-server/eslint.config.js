// base lint rules for javascript
import eslint from "@eslint/js";
import globals from "globals";

// syntax check plugins for: json, yaml
import json from "eslint-plugin-jsonc";
import yml from "eslint-plugin-yml";

// prettier plugin for formatting
import prettier from "eslint-plugin-prettier";
import prettierRecommended from "eslint-plugin-prettier/recommended";

export default [
    {
        ignores: ["pnpm-lock.yaml", "**/customer/**", "**/fixtures/schema/**", "src/schemas/**"],
    },
    // follow recommended eslint rules
    eslint.configs.recommended,
    {
        files: ["**/*.js"],
        languageOptions: {
            sourceType: "module",
            globals: {
                ...globals.node,
                ...globals.jest,
            },
        },
        plugins: {
            prettier: prettier,
        },
        rules: {
            "no-unused-vars": [
                "warn",
                {
                    varsIgnorePattern: "^_",
                    argsIgnorePattern: "^_|app|opts",
                },
            ],
            "constructor-super": "warn",
            "no-console": "off",
            "no-const-assign": "error",
            "no-new-require": "error",
            "no-prototype-builtins": "off",
            "no-this-before-super": "warn",
            "no-undef": "error",
            "no-unreachable": "error",
            "no-var": "warn",
            "prefer-const": "warn",
            "prettier/prettier": "error",
            "valid-typeof": "warn",
            "no-control-regex": "off"
        },
    },
    {
        files: ["**/*.json"],
        languageOptions: {
            parser: json,
        },
        rules: {
            "prettier/prettier": "error",
        },
    },
    {
        files: ["**/*.yaml", "**/*.yml"],
        plugins: {
            yml: yml,
        },
        rules: {
            ...yml.configs.recommended.rules,
            "prettier/prettier": "error",
        },
    },
    // Prettier to handle conflicts with other configs
    prettierRecommended,
];
