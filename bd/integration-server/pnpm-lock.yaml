lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@apidevtools/json-schema-ref-parser':
        specifier: ^11.7.0
        version: 11.7.0
      '@aws-sdk/client-s3':
        specifier: ^3.572.0
        version: 3.600.0
      '@fastify/autoload':
        specifier: ^5.8.0
        version: 5.10.0
      '@fastify/bearer-auth':
        specifier: ^9.4.0
        version: 9.4.0
      '@fastify/env':
        specifier: ^4.3.0
        version: 4.3.0
      '@fastify/postgres':
        specifier: ^5.2.2
        version: 5.2.2(pg@8.12.0)
      '@fastify/sensible':
        specifier: ^5.5.0
        version: 5.6.0
      '@fastify/soap-client':
        specifier: ^2.2.0
        version: 2.2.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0)
      '@fastify/swagger':
        specifier: ^8.14.0
        version: 8.14.0(supports-color@7.2.0)
      '@fastify/swagger-ui':
        specifier: ^3.0.0
        version: 3.1.0
      '@google-cloud/documentai':
        specifier: ^8.10.0
        version: 8.10.0(encoding@0.1.13)(supports-color@7.2.0)
      '@openapi-contrib/openapi-schema-to-json-schema':
        specifier: ^5.1.0
        version: 5.1.0
      '@rgrove/parse-xml':
        specifier: ^4.1.0
        version: 4.1.0
      a-sync-waterfall:
        specifier: ^1.0.1
        version: 1.0.1
      abbrev:
        specifier: ^1.1.1
        version: 1.1.1
      abort-controller:
        specifier: ^3.0.0
        version: 3.0.0
      abstract-logging:
        specifier: ^2.0.1
        version: 2.0.1
      agent-base:
        specifier: ^6.0.2
        version: 6.0.2(supports-color@7.2.0)
      agentkeepalive:
        specifier: ^4.5.0
        version: 4.5.0
      aggregate-error:
        specifier: ^3.1.0
        version: 3.1.0
      ajv:
        specifier: ^8.12.0
        version: 8.16.0
      ajv-formats:
        specifier: ^2.1.1
        version: 2.1.1(ajv@8.16.0)
      ajv-keywords:
        specifier: ^5.1.0
        version: 5.1.0(ajv@8.16.0)
      ansi-regex:
        specifier: ^5.0.1
        version: 5.0.1
      ansi-styles:
        specifier: ^4.3.0
        version: 4.3.0
      anymatch:
        specifier: ^3.1.3
        version: 3.1.3
      append-transform:
        specifier: ^2.0.0
        version: 2.0.0
      aproba:
        specifier: ^2.0.0
        version: 2.0.0
      archy:
        specifier: ^1.0.0
        version: 1.0.0
      are-we-there-yet:
        specifier: ^2.0.0
        version: 2.0.0
      argparse:
        specifier: ^1.0.10
        version: 1.0.10
      asap:
        specifier: ^2.0.6
        version: 2.0.6
      asn1:
        specifier: ^0.2.6
        version: 0.2.6
      assert-plus:
        specifier: ^1.0.0
        version: 1.0.0
      async-hook-domain:
        specifier: ^2.0.4
        version: 2.0.4
      asynckit:
        specifier: ^0.4.0
        version: 0.4.0
      atomic-sleep:
        specifier: ^1.0.0
        version: 1.0.0
      avvio:
        specifier: ^8.2.1
        version: 8.3.2
      aws-sign2:
        specifier: ^0.7.0
        version: 0.7.0
      aws4:
        specifier: ^1.12.0
        version: 1.13.0
      axios:
        specifier: ^1.6.8
        version: 1.7.2(debug@4.3.5(supports-color@7.2.0))
      axios-debug-log:
        specifier: ^1.0.0
        version: 1.0.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0)
      axios-ntlm:
        specifier: ^1.4.2
        version: 1.4.2(debug@4.3.5(supports-color@7.2.0))
      balanced-match:
        specifier: ^1.0.2
        version: 1.0.2
      base64-js:
        specifier: ^1.5.1
        version: 1.5.1
      bcrypt-pbkdf:
        specifier: ^1.0.2
        version: 1.0.2
      better-sqlite3:
        specifier: ^9.3.0
        version: 9.6.0
      binary-extensions:
        specifier: ^2.2.0
        version: 2.3.0
      bind-obj-methods:
        specifier: ^3.0.0
        version: 3.0.0
      bindings:
        specifier: ^1.5.0
        version: 1.5.0
      bl:
        specifier: ^4.1.0
        version: 4.1.0
      brace-expansion:
        specifier: ^2.0.1
        version: 2.0.1
      braces:
        specifier: ^3.0.2
        version: 3.0.3
      browserslist:
        specifier: ^4.22.2
        version: 4.23.1
      buffer:
        specifier: ^6.0.3
        version: 6.0.3
      buffer-from:
        specifier: ^1.1.2
        version: 1.1.2
      buffer-writer:
        specifier: ^2.0.0
        version: 2.0.0
      bytes:
        specifier: ^3.1.2
        version: 3.1.2
      cacache:
        specifier: ^15.3.0
        version: 15.3.0
      caching-transform:
        specifier: ^4.0.0
        version: 4.0.0
      call-bind:
        specifier: ^1.0.7
        version: 1.0.7
      camelcase:
        specifier: ^5.3.1
        version: 5.3.1
      caniuse-lite:
        specifier: ^1.0.30001579
        version: 1.0.30001636
      caseless:
        specifier: ^0.12.0
        version: 0.12.0
      chalk:
        specifier: ^4.1.2
        version: 4.1.2
      chokidar:
        specifier: ^3.5.3
        version: 3.6.0
      chownr:
        specifier: ^2.0.0
        version: 2.0.0
      clean-stack:
        specifier: ^2.2.0
        version: 2.2.0
      cliui:
        specifier: ^7.0.4
        version: 7.0.4
      close-with-grace:
        specifier: ^1.2.0
        version: 1.3.0
      color-convert:
        specifier: ^2.0.1
        version: 2.0.1
      color-name:
        specifier: ^1.1.4
        version: 1.1.4
      color-support:
        specifier: ^1.1.3
        version: 1.1.3
      colorette:
        specifier: ^2.0.20
        version: 2.0.20
      combined-stream:
        specifier: ^1.0.8
        version: 1.0.8
      commander:
        specifier: ^5.1.0
        version: 5.1.0
      commist:
        specifier: ^3.2.0
        version: 3.2.0
      commondir:
        specifier: ^1.0.1
        version: 1.0.1
      concat-map:
        specifier: ^0.0.1
        version: 0.0.1
      console-control-strings:
        specifier: ^1.1.0
        version: 1.1.0
      convert-source-map:
        specifier: ^1.9.0
        version: 1.9.0
      cookie:
        specifier: ^0.5.0
        version: 0.5.0
      core-util-is:
        specifier: ^1.0.2
        version: 1.0.3
      cron-parser:
        specifier: ^4.9.0
        version: 4.9.0
      cross-spawn:
        specifier: ^7.0.3
        version: 7.0.3
      csv-parser:
        specifier: ^3.0.0
        version: 3.0.0
      dashdash:
        specifier: ^1.14.1
        version: 1.14.1
      dateformat:
        specifier: ^4.6.3
        version: 4.6.3
      debug:
        specifier: ^4.3.4
        version: 4.3.5(supports-color@7.2.0)
      decamelize:
        specifier: ^1.2.0
        version: 1.2.0
      decompress-response:
        specifier: ^6.0.0
        version: 6.0.0
      deep-extend:
        specifier: ^0.6.0
        version: 0.6.0
      default-require-extensions:
        specifier: ^3.0.1
        version: 3.0.1
      define-data-property:
        specifier: ^1.1.4
        version: 1.1.4
      delay:
        specifier: ^5.0.0
        version: 5.0.0
      delayed-stream:
        specifier: ^1.0.0
        version: 1.0.0
      delegates:
        specifier: ^1.0.0
        version: 1.0.0
      depd:
        specifier: ^2.0.0
        version: 2.0.0
      des.js:
        specifier: ^1.1.0
        version: 1.1.0
      desm:
        specifier: ^1.3.1
        version: 1.3.1
      detect-libc:
        specifier: ^2.0.2
        version: 2.0.3
      dev-null:
        specifier: ^0.1.1
        version: 0.1.1
      dezalgo:
        specifier: ^1.0.4
        version: 1.0.4
      diff:
        specifier: ^4.0.2
        version: 4.0.2
      dotenv:
        specifier: ^16.3.2
        version: 16.4.5
      dotenv-expand:
        specifier: ^10.0.0
        version: 10.0.0
      ecc-jsbn:
        specifier: ^0.1.2
        version: 0.1.2
      electron-to-chromium:
        specifier: ^1.4.642
        version: 1.4.808
      emoji-regex:
        specifier: ^8.0.0
        version: 8.0.0
      encoding:
        specifier: ^0.1.13
        version: 0.1.13
      end-of-stream:
        specifier: ^1.4.4
        version: 1.4.4
      env-paths:
        specifier: ^2.2.1
        version: 2.2.1
      env-schema:
        specifier: ^5.2.1
        version: 5.2.1
      err-code:
        specifier: ^2.0.3
        version: 2.0.3
      es-define-property:
        specifier: ^1.0.0
        version: 1.0.0
      es-errors:
        specifier: ^1.3.0
        version: 1.3.0
      es6-error:
        specifier: ^4.1.1
        version: 4.1.1
      escalade:
        specifier: ^3.1.1
        version: 3.1.2
      escape-string-regexp:
        specifier: ^2.0.0
        version: 2.0.0
      eslint:
        specifier: ^9.10.0
        version: 9.10.0(supports-color@7.2.0)
      eslint-config-prettier:
        specifier: ^9.1.0
        version: 9.1.0(eslint@9.10.0(supports-color@7.2.0))
      eslint-plugin-jsonc:
        specifier: ^2.16.0
        version: 2.16.0(eslint@9.10.0(supports-color@7.2.0))
      eslint-plugin-prettier:
        specifier: ^5.2.1
        version: 5.2.1(eslint-config-prettier@9.1.0(eslint@9.10.0(supports-color@7.2.0)))(eslint@9.10.0(supports-color@7.2.0))(prettier@3.2.5)
      eslint-plugin-yaml:
        specifier: ^1.0.3
        version: 1.0.3
      eslint-plugin-yml:
        specifier: ^1.14.0
        version: 1.14.0(eslint@9.10.0(supports-color@7.2.0))(supports-color@7.2.0)
      esprima:
        specifier: ^4.0.1
        version: 4.0.1
      event-target-shim:
        specifier: ^5.0.1
        version: 5.0.1
      events:
        specifier: ^3.3.0
        version: 3.3.0
      events-to-array:
        specifier: ^1.1.2
        version: 1.1.2
      expand-template:
        specifier: ^2.0.3
        version: 2.0.3
      extend:
        specifier: ^3.0.2
        version: 3.0.2
      extsprintf:
        specifier: ^1.3.0
        version: 1.4.1
      fast-content-type-parse:
        specifier: ^1.1.0
        version: 1.1.0
      fast-copy:
        specifier: ^3.0.1
        version: 3.0.2
      fast-decode-uri-component:
        specifier: ^1.0.1
        version: 1.0.1
      fast-deep-equal:
        specifier: ^3.1.3
        version: 3.1.3
      fast-json-stable-stringify:
        specifier: ^2.1.0
        version: 2.1.0
      fast-json-stringify:
        specifier: ^5.10.0
        version: 5.16.1
      fast-querystring:
        specifier: ^1.1.2
        version: 1.1.2
      fast-redact:
        specifier: ^3.3.0
        version: 3.5.0
      fast-safe-stringify:
        specifier: ^2.1.1
        version: 2.1.1
      fast-uri:
        specifier: ^2.3.0
        version: 2.4.0
      fast-xml-parser:
        specifier: ^4.3.3
        version: 4.4.0
      fastify:
        specifier: ^4.25.2
        version: 4.28.0
      fastify-cli:
        specifier: ^5.9.0
        version: 5.9.0
      fastify-plugin:
        specifier: ^4.5.1
        version: 4.5.1
      fastify-print-routes:
        specifier: ^3.2.0
        version: 3.2.0
      fastify-qs:
        specifier: ^4.0.2
        version: 4.0.2
      fastify-raw-body:
        specifier: ^4.3.0
        version: 4.3.0
      fastq:
        specifier: ^1.16.0
        version: 1.17.1
      file-uri-to-path:
        specifier: ^1.0.0
        version: 1.0.0
      fill-range:
        specifier: ^7.0.1
        version: 7.1.1
      find-cache-dir:
        specifier: ^3.3.2
        version: 3.3.2
      find-my-way:
        specifier: ^7.7.0
        version: 7.7.0
      find-up:
        specifier: ^3.0.0
        version: 3.0.0
      findit:
        specifier: ^2.0.0
        version: 2.0.0
      follow-redirects:
        specifier: ^1.15.5
        version: 1.15.6(debug@4.3.5(supports-color@7.2.0))
      foreground-child:
        specifier: ^2.0.0
        version: 2.0.0
      forever-agent:
        specifier: ^0.6.1
        version: 0.6.1
      form-data:
        specifier: ^4.0.0
        version: 4.0.0
      formidable:
        specifier: ^2.1.2
        version: 2.1.2
      forwarded:
        specifier: ^0.2.0
        version: 0.2.0
      fromentries:
        specifier: ^1.3.2
        version: 1.3.2
      fs-constants:
        specifier: ^1.0.0
        version: 1.0.0
      fs-exists-cached:
        specifier: ^1.0.0
        version: 1.0.0
      fs-minipass:
        specifier: ^2.1.0
        version: 2.1.0
      fs.realpath:
        specifier: ^1.0.0
        version: 1.0.0
      function-bind:
        specifier: ^1.1.2
        version: 1.1.2
      function-loop:
        specifier: ^2.0.1
        version: 2.0.1
      gauge:
        specifier: ^3.0.2
        version: 3.0.2
      generify:
        specifier: ^4.2.0
        version: 4.2.0
      gensync:
        specifier: ^1.0.0-beta.2
        version: 1.0.0-beta.2
      get-caller-file:
        specifier: ^2.0.5
        version: 2.0.5
      get-intrinsic:
        specifier: ^1.2.4
        version: 1.2.4
      get-package-type:
        specifier: ^0.1.0
        version: 0.1.0
      get-stream:
        specifier: ^6.0.1
        version: 6.0.1
      getpass:
        specifier: ^0.1.7
        version: 0.1.7
      github-from-package:
        specifier: ^0.0.0
        version: 0.0.0
      glob:
        specifier: ^8.1.0
        version: 8.1.0
      glob-parent:
        specifier: ^5.1.2
        version: 5.1.2
      globals:
        specifier: ^11.12.0
        version: 11.12.0
      globalyzer:
        specifier: ^0.1.0
        version: 0.1.4
      globrex:
        specifier: ^0.1.2
        version: 0.1.2
      gopd:
        specifier: ^1.0.1
        version: 1.0.1
      graceful-fs:
        specifier: ^4.2.11
        version: 4.2.11
      har-schema:
        specifier: ^2.0.0
        version: 2.0.0
      har-validator:
        specifier: ^5.1.5
        version: 5.1.5
      has-flag:
        specifier: ^4.0.0
        version: 4.0.0
      has-property-descriptors:
        specifier: ^1.0.2
        version: 1.0.2
      has-proto:
        specifier: ^1.0.3
        version: 1.0.3
      has-symbols:
        specifier: ^1.0.3
        version: 1.0.3
      has-unicode:
        specifier: ^2.0.1
        version: 2.0.1
      hasha:
        specifier: ^5.2.2
        version: 5.2.2
      hasown:
        specifier: ^2.0.1
        version: 2.0.2
      help-me:
        specifier: ^4.2.0
        version: 4.2.0
      hexoid:
        specifier: ^1.0.0
        version: 1.0.0
      html-escaper:
        specifier: ^2.0.2
        version: 2.0.2
      http-cache-semantics:
        specifier: ^4.1.1
        version: 4.1.1
      http-errors:
        specifier: ^2.0.0
        version: 2.0.0
      http-proxy-agent:
        specifier: ^4.0.1
        version: 4.0.1(supports-color@7.2.0)
      http-signature:
        specifier: ^1.2.0
        version: 1.4.0
      httpntlm:
        specifier: ^1.8.13
        version: 1.8.13
      httpreq:
        specifier: ^1.1.1
        version: 1.1.1
      https-proxy-agent:
        specifier: ^5.0.1
        version: 5.0.1(supports-color@7.2.0)
      humanize-ms:
        specifier: ^1.2.1
        version: 1.2.1
      iconv-lite:
        specifier: ^0.4.24
        version: 0.4.24
      ieee754:
        specifier: ^1.2.1
        version: 1.2.1
      ignore-by-default:
        specifier: ^1.0.1
        version: 1.0.1
      imurmurhash:
        specifier: ^0.1.4
        version: 0.1.4
      indent-string:
        specifier: ^4.0.0
        version: 4.0.0
      infer-owner:
        specifier: ^1.0.4
        version: 1.0.4
      inflight:
        specifier: ^1.0.6
        version: 1.0.6
      inherits:
        specifier: ^2.0.4
        version: 2.0.4
      ini:
        specifier: ^1.3.8
        version: 1.3.8
      ip:
        specifier: ^2.0.0
        version: 2.0.1
      ipaddr.js:
        specifier: ^1.9.1
        version: 1.9.1
      is-binary-path:
        specifier: ^2.1.0
        version: 2.1.0
      is-docker:
        specifier: ^2.2.1
        version: 2.2.1
      is-extglob:
        specifier: ^2.1.1
        version: 2.1.1
      is-fullwidth-code-point:
        specifier: ^3.0.0
        version: 3.0.0
      is-glob:
        specifier: ^4.0.3
        version: 4.0.3
      is-lambda:
        specifier: ^1.0.1
        version: 1.0.1
      is-number:
        specifier: ^7.0.0
        version: 7.0.0
      is-stream:
        specifier: ^2.0.1
        version: 2.0.1
      is-typedarray:
        specifier: ^1.0.0
        version: 1.0.0
      is-windows:
        specifier: ^1.0.2
        version: 1.0.2
      isbinaryfile:
        specifier: ^4.0.10
        version: 4.0.10
      isexe:
        specifier: ^2.0.0
        version: 2.0.0
      isstream:
        specifier: ^0.1.2
        version: 0.1.2
      istanbul-lib-coverage:
        specifier: ^3.2.2
        version: 3.2.2
      istanbul-lib-hook:
        specifier: ^3.0.0
        version: 3.0.0
      istanbul-lib-instrument:
        specifier: ^4.0.3
        version: 4.0.3(supports-color@7.2.0)
      istanbul-lib-processinfo:
        specifier: ^2.0.3
        version: 2.0.3
      istanbul-lib-report:
        specifier: ^3.0.1
        version: 3.0.1
      istanbul-lib-source-maps:
        specifier: ^4.0.1
        version: 4.0.1(supports-color@7.2.0)
      istanbul-reports:
        specifier: ^3.1.6
        version: 3.1.7
      jackspeak:
        specifier: ^1.4.2
        version: 1.4.2
      jest:
        specifier: ^29.7.0
        version: 29.7.0(@types/node@18.19.38)(supports-color@7.2.0)
      joycon:
        specifier: ^3.1.1
        version: 3.1.1
      js-md4:
        specifier: ^0.3.2
        version: 0.3.2
      js-tokens:
        specifier: ^4.0.0
        version: 4.0.0
      js-yaml:
        specifier: ^3.14.1
        version: 3.14.1
      jsbn:
        specifier: ^0.1.1
        version: 0.1.1
      jsesc:
        specifier: ^2.5.2
        version: 2.5.2
      json-schema:
        specifier: ^0.4.0
        version: 0.4.0
      json-schema-ref-resolver:
        specifier: ^1.0.1
        version: 1.0.1
      json-schema-traverse:
        specifier: ^1.0.0
        version: 1.0.0
      json-stringify-safe:
        specifier: ^5.0.1
        version: 5.0.1
      json5:
        specifier: ^2.2.3
        version: 2.2.3
      jsprim:
        specifier: ^1.4.2
        version: 1.4.2
      libtap:
        specifier: ^1.4.1
        version: 1.4.1
      libxmljs2:
        specifier: ^0.33.0
        version: 0.33.0(encoding@0.1.13)(supports-color@7.2.0)
      light-my-request:
        specifier: ^5.11.0
        version: 5.13.0
      locate-path:
        specifier: ^3.0.0
        version: 3.0.0
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      lodash.clonedeep:
        specifier: ^4.5.0
        version: 4.5.0
      lodash.debounce:
        specifier: ^4.0.8
        version: 4.0.8
      lodash.flattendeep:
        specifier: ^4.4.0
        version: 4.4.0
      lru-cache:
        specifier: ^6.0.0
        version: 6.0.0
      luxon:
        specifier: ^3.4.4
        version: 3.4.4
      make-dir:
        specifier: ^3.1.0
        version: 3.1.0
      make-fetch-happen:
        specifier: ^9.1.0
        version: 9.1.0(supports-color@7.2.0)
      make-promises-safe:
        specifier: ^5.1.0
        version: 5.1.0
      makeerror:
        specifier: ^1.0.12
        version: 1.0.12
      media-typer:
        specifier: ^0.3.0
        version: 0.3.0
      mime:
        specifier: ^3.0.0
        version: 3.0.0
      mime-db:
        specifier: ^1.52.0
        version: 1.52.0
      mime-types:
        specifier: ^2.1.35
        version: 2.1.35
      mimic-response:
        specifier: ^3.1.0
        version: 3.1.0
      minimalistic-assert:
        specifier: ^1.0.1
        version: 1.0.1
      minimatch:
        specifier: ^5.1.6
        version: 5.1.6
      minimist:
        specifier: ^1.2.8
        version: 1.2.8
      minipass:
        specifier: ^3.3.6
        version: 3.3.6
      minipass-collect:
        specifier: ^1.0.2
        version: 1.0.2
      minipass-fetch:
        specifier: ^1.4.1
        version: 1.4.1
      minipass-flush:
        specifier: ^1.0.5
        version: 1.0.5
      minipass-pipeline:
        specifier: ^1.2.4
        version: 1.2.4
      minipass-sized:
        specifier: ^1.0.3
        version: 1.0.3
      minizlib:
        specifier: ^2.1.2
        version: 2.1.2
      mkdirp:
        specifier: ^1.0.4
        version: 1.0.4
      mkdirp-classic:
        specifier: ^0.5.3
        version: 0.5.3
      moment:
        specifier: ^2.30.1
        version: 2.30.1
      ms:
        specifier: ^2.1.2
        version: 2.1.3
      napi-build-utils:
        specifier: ^1.0.2
        version: 1.0.2
      negotiator:
        specifier: ^0.6.3
        version: 0.6.3
      node-abi:
        specifier: ^3.54.0
        version: 3.65.0
      node-addon-api:
        specifier: ^7.1.0
        version: 7.1.0
      node-fetch:
        specifier: ^2.7.0
        version: 2.7.0(encoding@0.1.13)
      node-gyp:
        specifier: ^8.4.1
        version: 8.4.1(supports-color@7.2.0)
      node-preload:
        specifier: ^0.2.1
        version: 0.2.1
      node-releases:
        specifier: ^2.0.14
        version: 2.0.14
      node-soap:
        specifier: ^1.0.0
        version: 1.0.0
      node-stream-zip:
        specifier: ^1.15.0
        version: 1.15.0
      nodemon:
        specifier: ^3.0.3
        version: 3.1.4
      nopt:
        specifier: ^5.0.0
        version: 5.0.0
      normalize-path:
        specifier: ^3.0.0
        version: 3.0.0
      npmlog:
        specifier: ^5.0.1
        version: 5.0.1
      nunjucks:
        specifier: ^3.2.4
        version: 3.2.4(chokidar@3.6.0)
      nyc:
        specifier: ^15.1.0
        version: 15.1.0(supports-color@7.2.0)
      oauth-sign:
        specifier: ^0.9.0
        version: 0.9.0
      object-assign:
        specifier: ^4.1.1
        version: 4.1.1
      object-inspect:
        specifier: ^1.13.1
        version: 1.13.1
      on-exit-leak-free:
        specifier: ^2.1.2
        version: 2.1.2
      once:
        specifier: ^1.4.0
        version: 1.4.0
      openai:
        specifier: ^4.40.0
        version: 4.52.0(encoding@0.1.13)
      openapi-typescript:
        specifier: ^5.4.1
        version: 5.4.2
      opener:
        specifier: ^1.5.2
        version: 1.5.2
      own-or:
        specifier: ^1.0.0
        version: 1.0.0
      own-or-env:
        specifier: ^1.0.2
        version: 1.0.2
      p-limit:
        specifier: ^2.3.0
        version: 2.3.0
      p-locate:
        specifier: ^3.0.0
        version: 3.0.0
      p-map:
        specifier: ^3.0.0
        version: 3.0.0
      p-try:
        specifier: ^2.2.0
        version: 2.2.0
      package-hash:
        specifier: ^4.0.0
        version: 4.0.0
      packet-reader:
        specifier: ^1.0.0
        version: 1.0.0
      papaparse:
        specifier: ^5.4.1
        version: 5.4.1
      path-exists:
        specifier: ^3.0.0
        version: 3.0.0
      path-is-absolute:
        specifier: ^1.0.1
        version: 1.0.1
      path-key:
        specifier: ^3.1.1
        version: 3.1.1
      pdf-img-convert:
        specifier: ^1.2.1
        version: 1.2.1(encoding@0.1.13)(supports-color@7.2.0)
      performance-now:
        specifier: ^2.1.0
        version: 2.1.0
      pg:
        specifier: ^8.11.3
        version: 8.12.0
      pg-boss:
        specifier: ^9.0.3
        version: 9.0.3
      pg-cloudflare:
        specifier: ^1.1.1
        version: 1.1.1
      pg-connection-string:
        specifier: ^2.6.2
        version: 2.6.4
      pg-int8:
        specifier: ^1.0.1
        version: 1.0.1
      pg-pool:
        specifier: ^3.6.1
        version: 3.6.2(pg@8.12.0)
      pg-protocol:
        specifier: ^1.6.0
        version: 1.6.1
      pg-types:
        specifier: ^2.2.0
        version: 2.2.0
      pgpass:
        specifier: ^1.0.5
        version: 1.0.5
      picocolors:
        specifier: ^1.0.0
        version: 1.0.1
      picomatch:
        specifier: ^2.3.1
        version: 2.3.1
      pino:
        specifier: ^8.17.2
        version: 8.21.0
      pino-abstract-transport:
        specifier: ^1.1.0
        version: 1.2.0
      pino-pretty:
        specifier: ^10.3.1
        version: 10.3.1
      pino-std-serializers:
        specifier: ^6.2.2
        version: 6.2.2
      pkg-dir:
        specifier: ^4.2.0
        version: 4.2.0
      pkg-up:
        specifier: ^3.1.0
        version: 3.1.0
      postgres-array:
        specifier: ^2.0.0
        version: 2.0.0
      postgres-bytea:
        specifier: ^1.0.0
        version: 1.0.0
      postgres-date:
        specifier: ^1.0.7
        version: 1.0.7
      postgres-interval:
        specifier: ^1.2.0
        version: 1.2.0
      prebuild-install:
        specifier: ^7.1.1
        version: 7.1.2
      process:
        specifier: ^0.11.10
        version: 0.11.10
      process-on-spawn:
        specifier: ^1.0.0
        version: 1.0.0
      process-warning:
        specifier: ^3.0.0
        version: 3.0.0
      promise-inflight:
        specifier: ^1.0.1
        version: 1.0.1
      promise-retry:
        specifier: ^2.0.1
        version: 2.0.1
      proxy-addr:
        specifier: ^2.0.7
        version: 2.0.7
      proxy-from-env:
        specifier: ^1.1.0
        version: 1.1.0
      psl:
        specifier: ^1.9.0
        version: 1.9.0
      pstree.remy:
        specifier: ^1.1.8
        version: 1.1.8
      pump:
        specifier: ^3.0.0
        version: 3.0.0
      punycode:
        specifier: ^2.3.1
        version: 2.3.1
      qs:
        specifier: ^6.11.2
        version: 6.12.1
      quick-format-unescaped:
        specifier: ^4.0.4
        version: 4.0.4
      raw-body:
        specifier: ^2.5.2
        version: 2.5.2
      rc:
        specifier: ^1.2.8
        version: 1.2.8
      readable-stream:
        specifier: ^3.6.2
        version: 3.6.2
      readdirp:
        specifier: ^3.6.0
        version: 3.6.0
      real-require:
        specifier: ^0.2.0
        version: 0.2.0
      release-zalgo:
        specifier: ^1.0.0
        version: 1.0.0
      request:
        specifier: ^2.88.2
        version: 2.88.2
      require-directory:
        specifier: ^2.1.1
        version: 2.1.1
      require-from-string:
        specifier: ^2.0.2
        version: 2.0.2
      require-main-filename:
        specifier: ^2.0.0
        version: 2.0.0
      resolve-from:
        specifier: ^5.0.0
        version: 5.0.0
      ret:
        specifier: ^0.2.2
        version: 0.2.2
      retry:
        specifier: ^0.12.0
        version: 0.12.0
      reusify:
        specifier: ^1.0.4
        version: 1.0.4
      rfdc:
        specifier: ^1.3.1
        version: 1.4.1
      rimraf:
        specifier: ^3.0.2
        version: 3.0.2
      safe-buffer:
        specifier: ^5.2.1
        version: 5.2.1
      safe-regex2:
        specifier: ^2.0.0
        version: 2.0.0
      safe-stable-stringify:
        specifier: ^2.4.3
        version: 2.4.3
      safer-buffer:
        specifier: ^2.1.2
        version: 2.1.2
      sax:
        specifier: ^1.3.0
        version: 1.4.1
      secure-json-parse:
        specifier: ^2.7.0
        version: 2.7.0
      semver:
        specifier: ^7.5.4
        version: 7.6.2
      serialize-error:
        specifier: ^8.1.0
        version: 8.1.0
      set-blocking:
        specifier: ^2.0.0
        version: 2.0.0
      set-cookie-parser:
        specifier: ^2.6.0
        version: 2.6.0
      set-function-length:
        specifier: ^1.2.1
        version: 1.2.2
      setprototypeof:
        specifier: ^1.2.0
        version: 1.2.0
      shebang-command:
        specifier: ^2.0.0
        version: 2.0.0
      shebang-regex:
        specifier: ^3.0.0
        version: 3.0.0
      side-channel:
        specifier: ^1.0.5
        version: 1.0.6
      signal-exit:
        specifier: ^3.0.7
        version: 3.0.7
      simple-concat:
        specifier: ^1.0.1
        version: 1.0.1
      simple-get:
        specifier: ^4.0.1
        version: 4.0.1
      simple-update-notifier:
        specifier: ^2.0.0
        version: 2.0.0
      smart-buffer:
        specifier: ^4.2.0
        version: 4.2.0
      soap:
        specifier: ^0.45.0
        version: 0.45.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0)
      socks:
        specifier: ^2.7.1
        version: 2.8.3
      socks-proxy-agent:
        specifier: ^6.2.1
        version: 6.2.1(supports-color@7.2.0)
      sonic-boom:
        specifier: ^3.8.0
        version: 3.8.1
      source-map:
        specifier: ^0.6.1
        version: 0.6.1
      source-map-support:
        specifier: ^0.5.21
        version: 0.5.21
      spawn-wrap:
        specifier: ^2.0.0
        version: 2.0.0
      split2:
        specifier: ^3.2.2
        version: 3.2.2
      sprintf-js:
        specifier: ^1.0.3
        version: 1.1.3
      sqlite3:
        specifier: ^5.1.7
        version: 5.1.7(supports-color@7.2.0)
      sshpk:
        specifier: ^1.18.0
        version: 1.18.0
      ssri:
        specifier: ^8.0.1
        version: 8.0.1
      stack-utils:
        specifier: ^2.0.6
        version: 2.0.6
      statuses:
        specifier: ^2.0.1
        version: 2.0.1
      string-width:
        specifier: ^4.2.3
        version: 4.2.3
      string_decoder:
        specifier: ^1.3.0
        version: 1.3.0
      strip-ansi:
        specifier: ^6.0.1
        version: 6.0.1
      strip-bom:
        specifier: ^4.0.0
        version: 4.0.0
      strip-json-comments:
        specifier: ^3.1.1
        version: 3.1.1
      strnum:
        specifier: ^1.0.5
        version: 1.0.5
      supports-color:
        specifier: ^7.2.0
        version: 7.2.0
      tap-mocha-reporter:
        specifier: ^5.0.4
        version: 5.0.4(supports-color@7.2.0)
      tap-parser:
        specifier: ^11.0.2
        version: 11.0.2
      tap-yaml:
        specifier: ^1.0.2
        version: 1.0.2
      tar:
        specifier: ^6.2.0
        version: 6.2.1
      tar-fs:
        specifier: ^2.1.1
        version: 2.1.1
      tar-stream:
        specifier: ^2.2.0
        version: 2.2.0
      tcompare:
        specifier: ^5.0.7
        version: 5.0.7
      tesseract.js:
        specifier: ^5.1.1
        version: 5.1.1(encoding@0.1.13)
      test-exclude:
        specifier: ^6.0.0
        version: 6.0.0
      thread-stream:
        specifier: ^2.4.1
        version: 2.7.0
      tiny-glob:
        specifier: ^0.2.9
        version: 0.2.9
      tmpl:
        specifier: ^1.0.5
        version: 1.0.5
      to-fast-properties:
        specifier: ^2.0.0
        version: 2.0.0
      to-regex-range:
        specifier: ^5.0.1
        version: 5.0.1
      toad-cache:
        specifier: ^3.7.0
        version: 3.7.0
      toidentifier:
        specifier: ^1.0.1
        version: 1.0.1
      touch:
        specifier: ^3.1.0
        version: 3.1.1
      tough-cookie:
        specifier: ^2.5.0
        version: 2.5.0
      tr46:
        specifier: ^0.0.3
        version: 0.0.3
      trivial-deferred:
        specifier: ^1.1.2
        version: 1.1.2
      tunnel-agent:
        specifier: ^0.6.0
        version: 0.6.0
      tweetnacl:
        specifier: ^0.14.5
        version: 0.14.5
      type-fest:
        specifier: ^0.8.1
        version: 0.8.1
      type-is:
        specifier: ^1.6.18
        version: 1.6.18
      typedarray-to-buffer:
        specifier: ^3.1.5
        version: 3.1.5
      undefsafe:
        specifier: ^2.0.5
        version: 2.0.5
      underscore:
        specifier: ^1.12.1
        version: 1.13.6
      undici:
        specifier: ^5.28.3
        version: 5.28.4
      undici-types:
        specifier: ^5.26.5
        version: 5.28.4
      unicode-length:
        specifier: ^2.1.0
        version: 2.1.0
      unique-filename:
        specifier: ^1.1.1
        version: 1.1.1
      unique-slug:
        specifier: ^2.0.2
        version: 2.0.2
      unpipe:
        specifier: ^1.0.0
        version: 1.0.0
      unzipper:
        specifier: ^0.11.5
        version: 0.11.6
      update-browserslist-db:
        specifier: ^1.0.13
        version: 1.0.16(browserslist@4.23.1)
      uri-js:
        specifier: ^4.4.1
        version: 4.4.1
      util-deprecate:
        specifier: ^1.0.2
        version: 1.0.2
      uuid:
        specifier: ^9.0.1
        version: 9.0.1
      vary:
        specifier: ^1.1.2
        version: 1.1.2
      verror:
        specifier: ^1.10.0
        version: 1.10.1
      walker:
        specifier: ^1.0.8
        version: 1.0.8
      webidl-conversions:
        specifier: ^3.0.1
        version: 3.0.1
      whatwg-mimetype:
        specifier: ^3.0.0
        version: 3.0.0
      whatwg-url:
        specifier: ^5.0.0
        version: 5.0.0
      which:
        specifier: ^2.0.2
        version: 2.0.2
      which-module:
        specifier: ^2.0.1
        version: 2.0.1
      wide-align:
        specifier: ^1.1.5
        version: 1.1.5
      wrap-ansi:
        specifier: ^7.0.0
        version: 7.0.0
      wrappy:
        specifier: ^1.0.2
        version: 1.0.2
      write-file-atomic:
        specifier: ^3.0.3
        version: 3.0.3
      xml-beautifier:
        specifier: ^0.5.0
        version: 0.5.0
      xml-crypto:
        specifier: ^2.1.5
        version: 2.1.5
      xml-formatter:
        specifier: ^3.6.2
        version: 3.6.2
      xml-parser-xo:
        specifier: ^4.1.1
        version: 4.1.1
      xml2js:
        specifier: ^0.6.2
        version: 0.6.2
      xpath:
        specifier: ^0.0.32
        version: 0.0.32
      xtend:
        specifier: ^4.0.2
        version: 4.0.2
      y18n:
        specifier: ^4.0.3
        version: 4.0.3
      yallist:
        specifier: ^4.0.0
        version: 4.0.0
      yaml:
        specifier: ^1.10.2
        version: 1.10.2
      yargs:
        specifier: ^15.4.1
        version: 15.4.1
      yargs-parser:
        specifier: ^21.1.1
        version: 21.1.1
    devDependencies:
      '@babel/core':
        specifier: ^7.25.2
        version: 7.25.2(supports-color@7.2.0)
      '@babel/eslint-parser':
        specifier: ^7.25.1
        version: 7.25.1(@babel/core@7.25.2(supports-color@7.2.0))(eslint@9.10.0(supports-color@7.2.0))
      '@babel/plugin-syntax-import-assertions':
        specifier: ^7.24.7
        version: 7.25.6(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/preset-env':
        specifier: ^7.25.3
        version: 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      babel-jest:
        specifier: ^29.7.0
        version: 29.7.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      prettier:
        specifier: 3.2.5
        version: 3.2.5
      tap:
        specifier: ^16.3.10
        version: 16.3.10(supports-color@7.2.0)

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@apidevtools/json-schema-ref-parser@11.7.0':
    resolution: {integrity: sha512-pRrmXMCwnmrkS3MLgAIW5dXRzeTv6GLjkjb4HmxNnvAKXN1Nfzp4KmGADBQvlVUcqi+a5D+hfGDLLnd5NnYxog==}
    engines: {node: '>= 16'}

  '@aws-crypto/crc32@5.2.0':
    resolution: {integrity: sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/crc32c@5.2.0':
    resolution: {integrity: sha512-+iWb8qaHLYKrNvGRbiYRHSdKRWhto5XlZUEBwDjYNf+ly5SVYG6zEoYIdxvf5R3zyeP16w4PLBn3rH1xc74Rag==}

  '@aws-crypto/sha1-browser@5.2.0':
    resolution: {integrity: sha512-OH6lveCFfcDjX4dbAvCFSYUjJZjDr/3XJ3xHtjn3Oj5b9RjojQo8npoLeA/bNwkOkrSQ0wgrHzXk4tDRxGKJeg==}

  '@aws-crypto/sha256-browser@5.2.0':
    resolution: {integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==}

  '@aws-crypto/sha256-js@5.2.0':
    resolution: {integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution: {integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==}

  '@aws-crypto/util@5.2.0':
    resolution: {integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==}

  '@aws-sdk/client-s3@3.600.0':
    resolution: {integrity: sha512-iYoKbJTputbf+ubkX6gSK/y/4uJEBRaXZ18jykLdBQ8UJuGrk2gqvV8h7OlGAhToCeysmmMqM0vDWyLt6lP8nw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso-oidc@3.600.0':
    resolution: {integrity: sha512-7+I8RWURGfzvChyNQSyj5/tKrqRbzRl7H+BnTOf/4Vsw1nFOi5ROhlhD4X/Y0QCTacxnaoNcIrqnY7uGGvVRzw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso@3.598.0':
    resolution: {integrity: sha512-nOI5lqPYa+YZlrrzwAJywJSw3MKVjvu6Ge2fCqQUNYMfxFB0NAaDFnl0EPjXi+sEbtCuz/uWE77poHbqiZ+7Iw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sts@3.600.0':
    resolution: {integrity: sha512-KQG97B7LvTtTiGmjlrG1LRAY8wUvCQzrmZVV5bjrJ/1oXAU7DITYwVbSJeX9NWg6hDuSk0VE3MFwIXS2SvfLIA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/core@3.598.0':
    resolution: {integrity: sha512-HaSjt7puO5Cc7cOlrXFCW0rtA0BM9lvzjl56x0A20Pt+0wxXGeTOZZOkXQIepbrFkV2e/HYukuT9e99vXDm59g==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-env@3.598.0':
    resolution: {integrity: sha512-vi1khgn7yXzLCcgSIzQrrtd2ilUM0dWodxj3PQ6BLfP0O+q1imO3hG1nq7DVyJtq7rFHs6+9N8G4mYvTkxby2w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-http@3.598.0':
    resolution: {integrity: sha512-N7cIafi4HVlQvEgvZSo1G4T9qb/JMLGMdBsDCT5XkeJrF0aptQWzTFH0jIdZcLrMYvzPcuEyO3yCBe6cy/ba0g==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-ini@3.598.0':
    resolution: {integrity: sha512-/ppcIVUbRwDIwJDoYfp90X3+AuJo2mvE52Y1t2VSrvUovYn6N4v95/vXj6LS8CNDhz2jvEJYmu+0cTMHdhI6eA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.598.0

  '@aws-sdk/credential-provider-node@3.600.0':
    resolution: {integrity: sha512-1pC7MPMYD45J7yFjA90SxpR0yaSvy+yZiq23aXhAPZLYgJBAxHLu0s0mDCk/piWGPh8+UGur5K0bVdx4B1D5hw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-process@3.598.0':
    resolution: {integrity: sha512-rM707XbLW8huMk722AgjVyxu2tMZee++fNA8TJVNgs1Ma02Wx6bBrfIvlyK0rCcIRb0WdQYP6fe3Xhiu4e8IBA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-sso@3.598.0':
    resolution: {integrity: sha512-5InwUmrAuqQdOOgxTccRayMMkSmekdLk6s+az9tmikq0QFAHUCtofI+/fllMXSR9iL6JbGYi1940+EUmS4pHJA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.598.0':
    resolution: {integrity: sha512-GV5GdiMbz5Tz9JO4NJtRoFXjW0GPEujA0j+5J/B723rTN+REHthJu48HdBKouHGhdzkDWkkh1bu52V02Wprw8w==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.598.0

  '@aws-sdk/middleware-bucket-endpoint@3.598.0':
    resolution: {integrity: sha512-PM7BcFfGUSkmkT6+LU9TyJiB4S8yI7dfuKQDwK5ZR3P7MKaK4Uj4yyDiv0oe5xvkF6+O2+rShj+eh8YuWkOZ/Q==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-expect-continue@3.598.0':
    resolution: {integrity: sha512-ZuHW18kaeHR8TQyhEOYMr8VwiIh0bMvF7J1OTqXHxDteQIavJWA3CbfZ9sgS4XGtrBZDyHJhjZKeCfLhN2rq3w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-flexible-checksums@3.598.0':
    resolution: {integrity: sha512-xukAzds0GQXvMEY9G6qt+CzwVzTx8NyKKh04O2Q+nOch6QQ8Rs+2kTRy3Z4wQmXq2pK9hlOWb5nXA7HWpmz6Ng==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-host-header@3.598.0':
    resolution: {integrity: sha512-WiaG059YBQwQraNejLIi0gMNkX7dfPZ8hDIhvMr5aVPRbaHH8AYF3iNSsXYCHvA2Cfa1O9haYXsuMF9flXnCmA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-location-constraint@3.598.0':
    resolution: {integrity: sha512-8oybQxN3F1ISOMULk7JKJz5DuAm5hCUcxMW9noWShbxTJuStNvuHf/WLUzXrf8oSITyYzIHPtf8VPlKR7I3orQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-logger@3.598.0':
    resolution: {integrity: sha512-bxBjf/VYiu3zfu8SYM2S9dQQc3tz5uBAOcPz/Bt8DyyK3GgOpjhschH/2XuUErsoUO1gDJqZSdGOmuHGZQn00Q==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.598.0':
    resolution: {integrity: sha512-vjT9BeFY9FeN0f8hm2l6F53tI0N5bUq6RcDkQXKNabXBnQxKptJRad6oP2X5y3FoVfBLOuDkQgiC2940GIPxtQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-sdk-s3@3.598.0':
    resolution: {integrity: sha512-5AGtLAh9wyK6ANPYfaKTqJY1IFJyePIxsEbxa7zS6REheAqyVmgJFaGu3oQ5XlxfGr5Uq59tFTRkyx26G1HkHA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-signing@3.598.0':
    resolution: {integrity: sha512-XKb05DYx/aBPqz6iCapsCbIl8aD8EihTuPCs51p75QsVfbQoVr4TlFfIl5AooMSITzojdAQqxt021YtvxjtxIQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-ssec@3.598.0':
    resolution: {integrity: sha512-f0p2xP8IC1uJ5e/tND1l81QxRtRFywEdnbtKCE0H6RSn4UIt2W3Dohe1qQDbnh27okF0PkNW6BJGdSAz3p7qbA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-user-agent@3.598.0':
    resolution: {integrity: sha512-4tjESlHG5B5MdjUaLK7tQs/miUtHbb6deauQx8ryqSBYOhfHVgb1ZnzvQR0bTrhpqUg0WlybSkDaZAICf9xctg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/region-config-resolver@3.598.0':
    resolution: {integrity: sha512-oYXhmTokSav4ytmWleCr3rs/1nyvZW/S0tdi6X7u+dLNL5Jee+uMxWGzgOrWK6wrQOzucLVjS4E/wA11Kv2GTw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/signature-v4-multi-region@3.598.0':
    resolution: {integrity: sha512-1r/EyTrO1gSa1FirnR8V7mabr7gk+l+HkyTI0fcTSr8ucB7gmYyW6WjkY8JCz13VYHFK62usCEDS7yoJoJOzTA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/token-providers@3.598.0':
    resolution: {integrity: sha512-TKY1EVdHVBnZqpyxyTHdpZpa1tUpb6nxVeRNn1zWG8QB5MvH4ALLd/jR+gtmWDNQbIG4cVuBOZFVL8hIYicKTA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sso-oidc': ^3.598.0

  '@aws-sdk/types@3.598.0':
    resolution: {integrity: sha512-742uRl6z7u0LFmZwDrFP6r1wlZcgVPw+/TilluDJmCAR8BgRw3IR+743kUXKBGd8QZDRW2n6v/PYsi/AWCDDMQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-arn-parser@3.568.0':
    resolution: {integrity: sha512-XUKJWWo+KOB7fbnPP0+g/o5Ulku/X53t7i/h+sPHr5xxYTJJ9CYnbToo95mzxe7xWvkLrsNtJ8L+MnNn9INs2w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-endpoints@3.598.0':
    resolution: {integrity: sha512-Qo9UoiVVZxcOEdiOMZg3xb1mzkTxrhd4qSlg5QQrfWPJVx/QOg+Iy0NtGxPtHtVZNHZxohYwDwV/tfsnDSE2gQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-locate-window@3.568.0':
    resolution: {integrity: sha512-3nh4TINkXYr+H41QaPelCceEB2FXP3fxp93YZXB/kqJvX0U9j0N0Uk45gvsjmEPzG8XxkPEeLIfT2I1M7A6Lig==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-user-agent-browser@3.598.0':
    resolution: {integrity: sha512-36Sxo6F+ykElaL1mWzWjlg+1epMpSe8obwhCN1yGE7Js9ywy5U6k6l+A3q3YM9YRbm740sNxncbwLklMvuhTKw==}

  '@aws-sdk/util-user-agent-node@3.598.0':
    resolution: {integrity: sha512-oyWGcOlfTdzkC6SVplyr0AGh54IMrDxbhg5RxJ5P+V4BKfcDoDcZV9xenUk9NsOi9MuUjxMumb9UJGkDhM1m0A==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@aws-sdk/xml-builder@3.598.0':
    resolution: {integrity: sha512-ZIa2RK7CHFTZ4gwK77WRtsZ6vF7xwRXxJ8KQIxK2duhoTVcn0xYxpFLdW9WZZZvdP9GIF3Loqvf8DRdeU5Jc7Q==}
    engines: {node: '>=16.0.0'}

  '@babel/code-frame@7.24.7':
    resolution: {integrity: sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.25.4':
    resolution: {integrity: sha512-+LGRog6RAsCJrrrg/IO6LGmpphNe5DiK30dGjCoxxeGv49B10/3XYGxPsAwrDlMFcFEvdAUavDT8r9k/hSyQqQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.25.2':
    resolution: {integrity: sha512-BBt3opiCOxUr9euZ5/ro/Xv8/V7yJ5bjYMqG/C1YAo8MIKAnumZalCN+msbci3Pigy4lIQfPUpfMM27HMGaYEA==}
    engines: {node: '>=6.9.0'}

  '@babel/eslint-parser@7.25.1':
    resolution: {integrity: sha512-Y956ghgTT4j7rKesabkh5WeqgSFZVFwaPR0IWFm7KFHFmmJ4afbG49SmfW4S+GyRPx0Dy5jxEWA5t0rpxfElWg==}
    engines: {node: ^10.13.0 || ^12.13.0 || >=14.0.0}
    peerDependencies:
      '@babel/core': ^7.11.0
      eslint: ^7.5.0 || ^8.0.0 || ^9.0.0

  '@babel/generator@7.24.7':
    resolution: {integrity: sha512-oipXieGC3i45Y1A41t4tAqpnEZWgB/lC6Ehh6+rOviR5XWpTtMmLN+fGjz9vOiNRt0p6RtO6DtD0pdU3vpqdSA==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.25.6':
    resolution: {integrity: sha512-VPC82gr1seXOpkjAAKoLhP50vx4vGNlF4msF64dSFq1P8RfB+QAuJWGHPXXPc8QyfVWwwB/TNNU4+ayZmHNbZw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.24.7':
    resolution: {integrity: sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-builder-binary-assignment-operator-visitor@7.24.7':
    resolution: {integrity: sha512-xZeCVVdwb4MsDBkkyZ64tReWYrLRHlMN72vP7Bdm3OUOuyFZExhsHUUnuWnm2/XOlAJzR0LfPpB56WXZn0X/lA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.25.2':
    resolution: {integrity: sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.25.4':
    resolution: {integrity: sha512-ro/bFs3/84MDgDmMwbcHgDa8/E6J3QKNTk4xJJnVeFtGE+tL0K26E3pNxhYz2b67fJpt7Aphw5XcploKXuCvCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.25.2':
    resolution: {integrity: sha512-+wqVGP+DFmqwFD3EH6TMTfUNeqDehV3E/dl+Sd54eaXqm17tEUNbEIn4sVivVowbvUpOtIGxdo3GoXyDH9N/9g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.2':
    resolution: {integrity: sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-environment-visitor@7.24.7':
    resolution: {integrity: sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.24.7':
    resolution: {integrity: sha512-FyoJTsj/PEUWu1/TYRiXTIHc8lbw+TDYkZuoE43opPS5TrI7MyONBE1oNvfguEXAD9yhQRrVBnXdXzSLQl9XnA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.24.7':
    resolution: {integrity: sha512-MJJwhkoGy5c4ehfoRyrJ/owKeMl19U54h27YYftT0o2teQ3FJ3nQUf/I3LlJsX4l3qlw7WRXUmiyajvHXoTubQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.24.8':
    resolution: {integrity: sha512-LABppdt+Lp/RlBxqrh4qgf1oEH/WxdzQNDJIu5gC/W1GyvPVrOBiItmmM8wan2fm4oYqFuFfkXmlGpLQhPY8CA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.24.7':
    resolution: {integrity: sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.24.7':
    resolution: {integrity: sha512-1fuJEwIrp+97rM4RWdO+qrRsZlAeL1lQJoPqtCYWv0NL115XM93hIH4CSRln2w52SqvmY5hqdtauB6QFCDiZNQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-module-transforms@7.25.2':
    resolution: {integrity: sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.24.7':
    resolution: {integrity: sha512-jKiTsW2xmWwxT1ixIdfXUZp+P5yURx2suzLZr5Hi64rURpDYdMW0pv+Uf17EYk2Rd428Lx4tLsnjGJzYKDM/6A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.24.8':
    resolution: {integrity: sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.25.0':
    resolution: {integrity: sha512-NhavI2eWEIz/H9dbrG0TuOicDhNexze43i5z7lEqwYm0WEZVTwnPpA0EafUTP7+6/W79HWIP2cTe3Z5NiSTVpw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.25.0':
    resolution: {integrity: sha512-q688zIvQVYtZu+i2PsdIu/uWGRpfxzr5WESsfpShfZECkO+d2o+WROWezCi/Q6kJ0tfPa5+pUGUlfx2HhrA3Bg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-simple-access@7.24.7':
    resolution: {integrity: sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.24.7':
    resolution: {integrity: sha512-IO+DLT3LQUElMbpzlatRASEyQtfhSE0+m465v++3jyyXeBTBUjtVZg28/gHeV5mrTJqvEKhKroBGAvhW+qPHiQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.24.7':
    resolution: {integrity: sha512-oy5V7pD+UvfkEATUKvIjvIAH/xCzfsFVw7ygW2SI6NClZzquT+mwdTfgfdbUiceh6iQO0CHtCPsyze/MZ2YbAA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.24.7':
    resolution: {integrity: sha512-7MbVt6xrwFQbunH2DNQsAP5sTGxfqQtErvBIvIMi6EQnbgUOuVYanvREcmFrOPhoXBrTtjhhP+lW+o5UfK+tDg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.24.8':
    resolution: {integrity: sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.24.7':
    resolution: {integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.24.8':
    resolution: {integrity: sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.25.0':
    resolution: {integrity: sha512-s6Q1ebqutSiZnEjaofc/UKDyC4SbzV5n5SrA2Gq8UawLycr3i04f1dX4OzoQVnexm6aOCh37SQNYlJ/8Ku+PMQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.25.6':
    resolution: {integrity: sha512-Xg0tn4HcfTijTwfDwYlvVCl43V6h4KyVVX2aEm4qdO/PC6L2YvzLHFdmxhoeSA3eslcE6+ZVXHgWwopXYLNq4Q==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.24.7':
    resolution: {integrity: sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.24.7':
    resolution: {integrity: sha512-9uUYRm6OqQrCqQdG1iCBwBPZgN8ciDBro2nIOFaiRz1/BCxaI7CNvQbDHvsArAC7Tw9Hda/B3U+6ui9u4HWXPw==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/parser@7.25.6':
    resolution: {integrity: sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.3':
    resolution: {integrity: sha512-wUrcsxZg6rqBXG05HG1FPYgsP6EvwF4WpBbxIpWIIYnH8wG0gzx3yZY3dtEHas4sTAOGkbTsc9EGPxwff8lRoA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.0':
    resolution: {integrity: sha512-Bm4bH2qsX880b/3ziJ8KD711LT7z4u8CFudmjqle65AZj/HNUFhEf90dqYv6O86buWvSBmeQDjv0Tn2aF/bIBA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.0':
    resolution: {integrity: sha512-lXwdNZtTmeVOOFtwM/WDe7yg1PL8sYhRk/XH0FzbR2HDQ0xC+EnQ/JHeoMYSavtU115tnUk0q9CDyq8si+LMAA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.7':
    resolution: {integrity: sha512-+izXIbke1T33mY4MSNnrqhPXDz01WYhEf3yF5NbnUtkiNnm+XBZJl3kNfoK6NKmYlz/D07+l2GWVK/QfDkNCuQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.0':
    resolution: {integrity: sha512-tggFrk1AIShG/RUQbEwt2Tr/E+ObkfwrPjR6BjbRvsx24+PSjK8zrq0GWPNCjo8qpRx4DuJzlcvWJqlm+0h3kw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-bigint@7.8.3':
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-export-namespace-from@7.8.3':
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.25.6':
    resolution: {integrity: sha512-aABl0jHw9bZ2karQ/uUD6XP4u0SG22SJrOHFoL6XB1R7dTovOP4TzTlsxOYC5yQ1pdscVK2JTUnF6QL3ARoAiQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.25.6':
    resolution: {integrity: sha512-sXaDXaJN9SNLymBdlWFA+bjzBhFD617ZaFiY13dGt7TVslVvVgA6fkZOP7Ki3IGElC45lwHdOTrCtKZGVAWeLQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.24.7':
    resolution: {integrity: sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.25.4':
    resolution: {integrity: sha512-uMOCoHVU52BsSWxPOMVv5qKRdeSlPuImUCB2dlPuBSU+W2/ROE7/Zg8F2Kepbk+8yBa68LlRKxO+xgEVWorsDg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.24.7':
    resolution: {integrity: sha512-Dt9LQs6iEY++gXUwY03DNFat5C2NbO48jj+j/bSAz6b3HgPs39qcPiYt77fDObIcFwj3/C2ICX9YMwGflUoSHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.25.4':
    resolution: {integrity: sha512-jz8cV2XDDTqjKPwVPJBIjORVEmSGYhdRa8e5k5+vN+uwcjSrSxUaebBRa4ko1jqNF2uxyg8G6XYk30Jv285xzg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.24.7':
    resolution: {integrity: sha512-SQY01PcJfmQ+4Ash7NE+rpbLFbmqA2GPIgqzxfFTL4t1FKRq4zTms/7htKpoCUI9OcFYgzqfmCdH53s6/jn5fA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.24.7':
    resolution: {integrity: sha512-yO7RAz6EsVQDaBH18IDJcMB1HnrUn2FJ/Jslc/WtPPWcjhpUJXU/rjbwmluzp7v/ZzWcEhTMXELnnsz8djWDwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.25.0':
    resolution: {integrity: sha512-yBQjYoOjXlFv9nlXb3f1casSHOZkWr29NX+zChVanLg5Nc157CrbEX9D7hxxtTpuFy7Q0YzmmWfJxzvps4kXrQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.25.4':
    resolution: {integrity: sha512-nZeZHyCWPfjkdU5pA/uHiTaDAFUEqkpzf1YoQT2NeSynCGYq9rxfyI3XpQbfx/a0hSnFH6TGlEXvae5Vi7GD8g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.24.7':
    resolution: {integrity: sha512-HMXK3WbBPpZQufbMG4B46A90PkuuhN9vBCb5T8+VAHqvAqvcLi+2cKoukcpmUYkszLhScU3l1iudhrks3DggRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.25.4':
    resolution: {integrity: sha512-oexUfaQle2pF/b6E0dwsxQtAol9TLSO88kQvym6HHBWFliV2lGdrPieX+WgMRLSJDVzdYywk7jXbLPuO2KLTLg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.24.7':
    resolution: {integrity: sha512-25cS7v+707Gu6Ds2oY6tCkUwsJ9YIDbggd9+cu9jzzDgiNq7hR/8dkzxWfKWnTic26vsI3EsCXNd4iEB6e8esQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.24.8':
    resolution: {integrity: sha512-36e87mfY8TnRxc7yc6M9g9gOB7rKgSahqkIKwLpz4Ppk2+zC2Cy1is0uwtuSG6AE4zlTOUa+7JGz9jCJGLqQFQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.24.7':
    resolution: {integrity: sha512-ZOA3W+1RRTSWvyqcMJDLqbchh7U4NRGqwRfFSVbOLS/ePIP4vHB5e8T8eXcuqyN1QkgKyj5wuW0lcS85v4CrSw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.24.7':
    resolution: {integrity: sha512-JdYfXyCRihAe46jUIliuL2/s0x0wObgwwiGxw/UbgJBr20gQBThrokO4nYKgWkD7uBaqM7+9x5TU7NkExZJyzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.0':
    resolution: {integrity: sha512-YLpb4LlYSc3sCUa35un84poXoraOiQucUTTu8X1j18JV+gNa8E0nyUf/CjZ171IRGr4jEguF+vzJU66QZhn29g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.24.7':
    resolution: {integrity: sha512-sc3X26PhZQDb3JhORmakcbvkeInvxz+A8oda99lj7J60QRuPZvNAk9wQlTBS1ZynelDrDmTU4pw1tyc5d5ZMUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.24.7':
    resolution: {integrity: sha512-Rqe/vSc9OYgDajNIK35u7ot+KeCoetqQYFXM4Epf7M7ez3lWlOjrDjrwMei6caCVhfdw+mIKD4cgdGNy5JQotQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.24.7':
    resolution: {integrity: sha512-v0K9uNYsPL3oXZ/7F9NNIbAj2jv1whUEtyA6aujhekLs56R++JDQuzRcP2/z4WX5Vg/c5lE9uWZA0/iUoFhLTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.24.7':
    resolution: {integrity: sha512-wo9ogrDG1ITTTBsy46oGiN1dS9A7MROBTcYsfS8DtsImMkHk9JXJ3EWQM6X2SUw4x80uGPlwj0o00Uoc6nEE3g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.25.1':
    resolution: {integrity: sha512-TVVJVdW9RKMNgJJlLtHsKDTydjZAbwIsn6ySBPQaEAUU5+gVvlJt/9nRmqVbsV/IBanRjzWoaAQKLoamWVOUuA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.24.7':
    resolution: {integrity: sha512-2yFnBGDvRuxAaE/f0vfBKvtnvvqU8tGpMHqMNpTN2oWMKIR3NqFkjaAgGwawhqK/pIN2T3XdjGPdaG0vDhOBGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.25.2':
    resolution: {integrity: sha512-HQI+HcTbm9ur3Z2DkO+jgESMAMcYLuN/A7NRw9juzxAezN9AvqvUTnpKP/9kkYANz6u7dFlAyOu44ejuGySlfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.24.7':
    resolution: {integrity: sha512-4D2tpwlQ1odXmTEIFWy9ELJcZHqrStlzK/dAOWYyxX3zT0iXQB6banjgeOJQXzEc4S0E0a5A+hahxPaEFYftsw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.24.7':
    resolution: {integrity: sha512-T/hRC1uqrzXMKLQ6UCwMT85S3EvqaBXDGf0FaMf4446Qx9vKwlghvee0+uuZcDUCZU5RuNi4781UQ7R308zzBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.24.7':
    resolution: {integrity: sha512-9+pB1qxV3vs/8Hdmz/CulFB8w2tuu6EB94JZFsjdqxQokwGa9Unap7Bo2gGBGIvPmDIVvQrom7r5m/TCDMURhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.24.8':
    resolution: {integrity: sha512-WHsk9H8XxRs3JXKWFiqtQebdh9b/pTk4EgueygFzYlTKAg0Ud985mSevdNjdXdFBATSKVJGQXP1tv6aGbssLKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.25.0':
    resolution: {integrity: sha512-YPJfjQPDXxyQWg/0+jHKj1llnY5f/R6a0p/vP4lPymxLu7Lvl4k2WMitqi08yxwQcCVUUdG9LCUj4TNEgAp3Jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.24.7':
    resolution: {integrity: sha512-3aytQvqJ/h9z4g8AsKPLvD4Zqi2qT+L3j7XoFFu1XBlZWEl2/1kWnhmAbxpLgPrHSY0M6UA02jyTiwUVtiKR6A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.24.7':
    resolution: {integrity: sha512-/jr7h/EWeJtk1U/uz2jlsCioHkZk1JJZVcc8oQsJ1dUlaJD83f4/6Zeh2aHt9BIFokHIsSeDfhUmju0+1GPd6g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.24.7':
    resolution: {integrity: sha512-RNKwfRIXg4Ls/8mMTza5oPF5RkOW8Wy/WgMAp1/F1yZ8mMbtwXW+HDoJiOsagWrAhI5f57Vncrmr9XeT4CVapA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.24.7':
    resolution: {integrity: sha512-Ts7xQVk1OEocqzm8rHMXHlxvsfZ0cEF2yomUqpKENHWMF4zKk175Y4q8H5knJes6PgYad50uuRmt3UJuhBw8pQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.24.7':
    resolution: {integrity: sha512-e6q1TiVUzvH9KRvicuxdBTUj4AdKSRwzIyFFnfnezpCfP2/7Qmbb8qbU2j7GODbl4JMkblitCQjKYUaX/qkkwA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.24.7':
    resolution: {integrity: sha512-4QrHAr0aXQCEFni2q4DqKLD31n2DL+RxcwnNjDFkSG0eNQ/xCavnRkfCUjsyqGC2OviNJvZOF/mQqZBw7i2C5Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.24.7':
    resolution: {integrity: sha512-A/vVLwN6lBrMFmMDmPPz0jnE6ZGx7Jq7d6sT/Ev4H65RER6pZ+kczlf1DthF5N0qaPHBsI7UXiE8Zy66nmAovg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.24.7':
    resolution: {integrity: sha512-uLEndKqP5BfBbC/5jTwPxLh9kqPWWgzN/f8w6UwAIirAEqiIVJWWY312X72Eub09g5KF9+Zn7+hT7sDxmhRuKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.24.8':
    resolution: {integrity: sha512-5cTOLSMs9eypEy8JUVvIKOu6NgvbJMnpG62VpIHrTmROdQ+L5mDAaI40g25k5vXti55JWNX5jCkq3HZxXBQANw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.24.7':
    resolution: {integrity: sha512-yGWW5Rr+sQOhK0Ot8hjDJuxU3XLRQGflvT4lhlSY0DFvdb3TwKaY26CJzHtYllU0vT9j58hc37ndFPsqT1SrzA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.25.4':
    resolution: {integrity: sha512-ao8BG7E2b/URaUQGqN3Tlsg+M3KlHY6rJ1O1gXAEUnZoyNQnvKyH87Kfg+FoxSeyWUB8ISZZsC91C44ZuBFytw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.24.7':
    resolution: {integrity: sha512-9z76mxwnwFxMyxZWEgdgECQglF2Q7cFLm0kMf8pGwt+GSJsY0cONKj/UuO4bOH0w/uAel3ekS4ra5CEAyJRmDA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.24.7':
    resolution: {integrity: sha512-EMi4MLQSHfd2nrCqQEWxFdha2gBCqU4ZcCng4WBGZ5CJL4bBRW0ptdqqDdeirGZcpALazVVNJqRmsO8/+oNCBA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.24.7':
    resolution: {integrity: sha512-lq3fvXPdimDrlg6LWBoqj+r/DEWgONuwjuOuQCSYgRroXDH/IdM1C0IZf59fL5cHLpjEH/O6opIRBbqv7ELnuA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-reserved-words@7.24.7':
    resolution: {integrity: sha512-0DUq0pHcPKbjFZCfTss/pGkYMfy3vFWydkUBd9r0GHpIyfs2eCDENvqadMycRS9wZCXR41wucAfJHJmwA0UmoQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.24.7':
    resolution: {integrity: sha512-KsDsevZMDsigzbA09+vacnLpmPH4aWjcZjXdyFKGzpplxhbeB4wYtury3vglQkg6KM/xEPKt73eCjPPf1PgXBA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.24.7':
    resolution: {integrity: sha512-x96oO0I09dgMDxJaANcRyD4ellXFLLiWhuwDxKZX5g2rWP1bTPkBSwCYv96VDXVT1bD9aPj8tppr5ITIh8hBng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.24.7':
    resolution: {integrity: sha512-kHPSIJc9v24zEml5geKg9Mjx5ULpfncj0wRpYtxbvKyTtHCYDkVE3aHQ03FrpEo4gEe2vrJJS1Y9CJTaThA52g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.24.7':
    resolution: {integrity: sha512-AfDTQmClklHCOLxtGoP7HkeMw56k1/bTQjwsfhL6pppo/M4TOBSq+jjBUBLmV/4oeFg4GWMavIl44ZeCtmmZTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.24.8':
    resolution: {integrity: sha512-adNTUpDCVnmAE58VEqKlAA6ZBlNkMnWD0ZcW76lyNFN3MJniyGFZfNwERVk8Ap56MCnXztmDr19T4mPTztcuaw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.24.7':
    resolution: {integrity: sha512-U3ap1gm5+4edc2Q/P+9VrBNhGkfnf+8ZqppY71Bo/pzZmXhhLdqgaUl6cuB07O1+AQJtCLfaOmswiNbSQ9ivhw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.24.7':
    resolution: {integrity: sha512-uH2O4OV5M9FZYQrwc7NdVmMxQJOCCzFeYudlZSzUAHRFeOujQefa92E74TQDVskNHCzOXoigEuoyzHDhaEaK5w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.24.7':
    resolution: {integrity: sha512-hlQ96MBZSAXUq7ltkjtu3FJCCSMx/j629ns3hA3pXnBXjanNP0LHi+JpPeA81zaWgVK1VGH95Xuy7u0RyQ8kMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.25.4':
    resolution: {integrity: sha512-qesBxiWkgN1Q+31xUE9RcMk79eOXXDCv6tfyGMRSs4RGlioSg2WVyQAm07k726cSE56pa+Kb0y9epX2qaXzTvA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.25.4':
    resolution: {integrity: sha512-W9Gyo+KmcxjGahtt3t9fb14vFRWvPpu5pT6GBlovAK6BTBcxgjfVMSQCfJl4oi35ODrxP6xx2Wr8LNST57Mraw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/regjsgen@0.8.0':
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}

  '@babel/runtime@7.25.6':
    resolution: {integrity: sha512-VBj9MYyDb9tuLq7yzqjgzt6Q+IBQLrGZfdjOekyEirZPHxXWoTSGUTMrpsfi58Up73d13NfYLv8HT9vmznjzhQ==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.24.7':
    resolution: {integrity: sha512-jYqfPrU9JTF0PmPy1tLYHW4Mp4KlgxJD9l2nP9fD6yT/ICi554DmrWBAEYpIelzjHf1msDP3PxJIRt/nFNfBig==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.25.0':
    resolution: {integrity: sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.24.7':
    resolution: {integrity: sha512-yb65Ed5S/QAcewNPh0nZczy9JdYXkkAbIsEo+P7BE7yO3txAY30Y/oPa3QkQ5It3xVG2kpKMg9MsdxZaO31uKA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.25.6':
    resolution: {integrity: sha512-9Vrcx5ZW6UwK5tvqsj0nGpp/XzqthkT0dqIc9g1AdtygFToNtTF67XzYS//dm+SAK9cp3B9R4ZO/46p63SCjlQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.24.7':
    resolution: {integrity: sha512-XEFXSlxiG5td2EJRe8vOmRbaXVgfcBlszKujvVmWIK/UpywWljQCfzAv3RQCGujWQ1RD4YYWEAqDXfuJiy8f5Q==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.25.6':
    resolution: {integrity: sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@0.2.3':
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==}

  '@eslint-community/eslint-utils@4.4.0':
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.11.1':
    resolution: {integrity: sha512-m4DVN9ZqskZoLU5GlWZadwDnYo3vAEydiUayB9widCl9ffWx2IvPnp6n3on5rJmziJSw9Bv+Z3ChDVdMwXCY8Q==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.18.0':
    resolution: {integrity: sha512-fTxvnS1sRMu3+JjXwJG0j/i4RT9u4qJ+lqS/yCGap4lH4zZGzQ7tu+xZqQmcMZq5OBZDL4QRxQzRjkWcGt8IVw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.1.0':
    resolution: {integrity: sha512-4Bfj15dVJdoy3RfZmmo86RK1Fwzn6SstsvK9JS+BaVKqC6QQQQyXekNaC+g+LKNgkQ+2VhGAzm6hO40AhMR3zQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.10.0':
    resolution: {integrity: sha512-fuXtbiP5GWIn8Fz+LWoOMVf/Jxm+aajZYkhi6CuEm4SxymFM+eUWzbO9qXT+L0iCkL5+KGYMCSGxo686H19S1g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.4':
    resolution: {integrity: sha512-BsWiH1yFGjXXS2yvrf5LyuoSIIbPrGUWob917o+BTKuZ7qJdxX8aJLRxs1fS9n6r7vESrq1OUqb68dANcFXuQQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.1.0':
    resolution: {integrity: sha512-autAXT203ixhqei9xt+qkYOvY8l6LAFIdT2UXc/RPNeUVfqRF1BV94GTJyVPFKT8nFM6MyVJhjLj9E8JWvf5zQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@fastify/accept-negotiator@1.1.0':
    resolution: {integrity: sha512-OIHZrb2ImZ7XG85HXOONLcJWGosv7sIvM2ifAPQVhg9Lv7qdmMBNVaai4QTdyuaqbKM5eO6sLSQOYI7wEQeCJQ==}
    engines: {node: '>=14'}

  '@fastify/ajv-compiler@3.5.0':
    resolution: {integrity: sha512-ebbEtlI7dxXF5ziNdr05mOY8NnDiPB1XvAlLHctRt/Rc+C3LCOVW5imUVX+mhvUhnNzmPBHewUkOFgGlCxgdAA==}

  '@fastify/autoload@5.10.0':
    resolution: {integrity: sha512-4A6s86qMbjcpWHmJL7cErtjIxOPuW8c67DLiuO8HoJQxuK97vaptoUnK5BTOwRg1ntYqfc3tjwerTTo5NQ3fEQ==}

  '@fastify/bearer-auth@9.4.0':
    resolution: {integrity: sha512-+xn8UCKhJiBP3SsbCjzhfq+Dtf/pl1BDVrgb0/o6WcgNDUZ+bcEZnJ0P2e6FQEtE2nMYonLD/aVa9Ku8N9Cfbw==}

  '@fastify/busboy@2.1.1':
    resolution: {integrity: sha512-vBZP4NlzfOlerQTnba4aqZoMhE/a9HY7HRqoOPaETQcSQuWEIyZMHGfVu6w9wGtGK5fED5qRs2DteVCjOH60sA==}
    engines: {node: '>=14'}

  '@fastify/deepmerge@1.3.0':
    resolution: {integrity: sha512-J8TOSBq3SoZbDhM9+R/u77hP93gz/rajSA+K2kGyijPpORPWUXHUpTaleoj+92As0S9uPRP7Oi8IqMf0u+ro6A==}

  '@fastify/env@4.3.0':
    resolution: {integrity: sha512-WSredffWvaYjiwHGK5wvY33LFi39gAasBMWelglA6Jk+d7uj/ZWp7icaPoM0kSR5g9M8OOALEvk+8SXiNhK1YA==}

  '@fastify/error@3.4.1':
    resolution: {integrity: sha512-wWSvph+29GR783IhmvdwWnN4bUxTD01Vm5Xad4i7i1VuAOItLvbPAb69sb0IQ2N57yprvhNIwAP5B6xfKTmjmQ==}

  '@fastify/fast-json-stringify-compiler@4.3.0':
    resolution: {integrity: sha512-aZAXGYo6m22Fk1zZzEUKBvut/CIIQe/BapEORnxiD5Qr0kPHqqI69NtEMCme74h+at72sPhbkb4ZrLd1W3KRLA==}

  '@fastify/merge-json-schemas@0.1.1':
    resolution: {integrity: sha512-fERDVz7topgNjtXsJTTW1JKLy0rhuLRcquYqNR9rF7OcVpCa2OVW49ZPDIhaRRCaUuvVxI+N416xUoF76HNSXA==}

  '@fastify/postgres@5.2.2':
    resolution: {integrity: sha512-8TWRqDSiXJp0SZjbHrqwyhl0f55eV4fpYAd9m7G0hGUpyEZJFwcxIDQYjnlRAXcVTq5NloUjFH6DxgmxZ3apbQ==}
    peerDependencies:
      pg: '>=6.0.0'

  '@fastify/send@2.1.0':
    resolution: {integrity: sha512-yNYiY6sDkexoJR0D8IDy3aRP3+L4wdqCpvx5WP+VtEU58sn7USmKynBzDQex5X42Zzvw2gNzzYgP90UfWShLFA==}

  '@fastify/sensible@5.6.0':
    resolution: {integrity: sha512-Vq6Z2ZQy10GDqON+hvLF52K99s9et5gVVxTul5n3SIAf0Kq5QjPRUKkAMT3zPAiiGvoHtS3APa/3uaxfDgCODQ==}

  '@fastify/soap-client@2.2.0':
    resolution: {integrity: sha512-ETyBPo7dLJt14S3x8cIIdPpDCwWimgvhRUTLHEbDYNEYqjOWr68JP05XoWYCrddZKT16aQWuHZy6H7EJZnETrw==}
    deprecated: This module is deprecated, use the soap module directly

  '@fastify/static@7.0.4':
    resolution: {integrity: sha512-p2uKtaf8BMOZWLs6wu+Ihg7bWNBdjNgCwDza4MJtTqg+5ovKmcbgbR9Xs5/smZ1YISfzKOCNYmZV8LaCj+eJ1Q==}

  '@fastify/swagger-ui@3.1.0':
    resolution: {integrity: sha512-68jm6k8VzvHXkEBT4Dakm/kkzUlPO4POIi0agWJSWxsYichPBqzjo+IpfqPl4pSJR1zCToQhEOo+cv+yJL2qew==}

  '@fastify/swagger@8.14.0':
    resolution: {integrity: sha512-sGiznEb3rl6pKGGUZ+JmfI7ct5cwbTQGo+IjewaTvtzfrshnryu4dZwEsjw0YHABpBA+kCz3kpRaHB7qpa67jg==}

  '@gar/promisify@1.1.3':
    resolution: {integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==}

  '@google-cloud/documentai@8.10.0':
    resolution: {integrity: sha512-XCVdwFAPhzqT0M5F3q86CMwS9giD1xpaRo9FmCSrSssUWFnZiPqjgTojZjz0NeTbCDNlIHBRQwuMRvsHRdNsUg==}
    engines: {node: '>=14.0.0'}

  '@grpc/grpc-js@1.12.0':
    resolution: {integrity: sha512-eWdP97A6xKtZXVP/ze9y8zYRB2t6ugQAuLXFuZXAsyqmyltaAjl4yPkmIfc0wuTFJMOUF1AdvIFQCL7fMtaX6g==}
    engines: {node: '>=12.10.0'}

  '@grpc/proto-loader@0.7.13':
    resolution: {integrity: sha512-AiXO/bfe9bmxBjxxtYxFAXGZvMaN5s8kO+jBHAJCON8rJoB5YS/D6X7ZNc6XQkuHNmyl4CYaMI1fJ/Gn27RGGw==}
    engines: {node: '>=6'}
    hasBin: true

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.0':
    resolution: {integrity: sha512-d2CGZR2o7fS6sWB7DG/3a95bGKQyHMACZ5aW8qGkkqQpUoZV6C0X7Pc7l4ZNMZkfNBf4VWNe9E1jRsf0G146Ew==}
    engines: {node: '>=18.18'}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jest/console@29.7.0':
    resolution: {integrity: sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/core@29.7.0':
    resolution: {integrity: sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/environment@29.7.0':
    resolution: {integrity: sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect-utils@29.7.0':
    resolution: {integrity: sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect@29.7.0':
    resolution: {integrity: sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/fake-timers@29.7.0':
    resolution: {integrity: sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/globals@29.7.0':
    resolution: {integrity: sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/reporters@29.7.0':
    resolution: {integrity: sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/source-map@29.6.3':
    resolution: {integrity: sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-result@29.7.0':
    resolution: {integrity: sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-sequencer@29.7.0':
    resolution: {integrity: sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/transform@29.7.0':
    resolution: {integrity: sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/types@29.6.3':
    resolution: {integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@js-sdsl/ordered-map@4.4.2':
    resolution: {integrity: sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==}

  '@jsdevtools/ono@7.1.3':
    resolution: {integrity: sha512-4JQNk+3mVzK3xh2rqd6RB4J46qUR19azEHBneZyTZM+c456qOrbbM/5xcR8huNCCcbVt7+UmizG6GuUvPvKUYg==}

  '@lukeed/ms@2.0.2':
    resolution: {integrity: sha512-9I2Zn6+NJLfaGoz9jN3lpwDgAYvfGeNYdbAIjJOqzs4Tpc+VU3Jqq4IofSUBKajiDS8k9fZIg18/z13mpk1bsA==}
    engines: {node: '>=8'}

  '@mapbox/node-pre-gyp@1.0.11':
    resolution: {integrity: sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==}
    hasBin: true

  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    resolution: {integrity: sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@npmcli/fs@1.1.1':
    resolution: {integrity: sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==}

  '@npmcli/move-file@1.1.2':
    resolution: {integrity: sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==}
    engines: {node: '>=10'}
    deprecated: This functionality has been moved to @npmcli/fs

  '@openapi-contrib/openapi-schema-to-json-schema@5.1.0':
    resolution: {integrity: sha512-MJnq+CxD8JAufiJoa8RK6D/8P45MEBe0teUi30TNoHRrI6MZRNgetK2Y2IfDXWGLTHMopb1d9GHonqlV2Yvztg==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@pkgr/core@0.1.1':
    resolution: {integrity: sha512-cq8o4cWH0ibXh9VGi5P20Tu9XF/0fFXl9EUinr9QfTM7a7p0oTA4iJRCQWppXR1Pg8dSM0UCItCkPwsk9qWWYA==}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@protobufjs/aspromise@1.1.2':
    resolution: {integrity: sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==}

  '@protobufjs/base64@1.1.2':
    resolution: {integrity: sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==}

  '@protobufjs/codegen@2.0.4':
    resolution: {integrity: sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==}

  '@protobufjs/eventemitter@1.1.0':
    resolution: {integrity: sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==}

  '@protobufjs/fetch@1.1.0':
    resolution: {integrity: sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==}

  '@protobufjs/float@1.0.2':
    resolution: {integrity: sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==}

  '@protobufjs/inquire@1.1.0':
    resolution: {integrity: sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==}

  '@protobufjs/path@1.1.2':
    resolution: {integrity: sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==}

  '@protobufjs/pool@1.1.0':
    resolution: {integrity: sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==}

  '@protobufjs/utf8@1.1.0':
    resolution: {integrity: sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==}

  '@rgrove/parse-xml@4.1.0':
    resolution: {integrity: sha512-pBiltENdy8SfI0AeR1e5TRpS9/9Gl0eiOEt6ful2jQfzsgvZYWqsKiBWaOCLdocQuk0wS7KOHI37n0C1pnKqTw==}
    engines: {node: '>=14.0.0'}

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}

  '@sinonjs/commons@3.0.1':
    resolution: {integrity: sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==}

  '@sinonjs/fake-timers@10.3.0':
    resolution: {integrity: sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==}

  '@smithy/abort-controller@3.1.0':
    resolution: {integrity: sha512-XOm4LkuC0PsK1sf2bBJLIlskn5ghmVxiEBVlo/jg0R8hxASBKYYgOoJEhKWgOr4vWGkN+5rC+oyBAqHYtxjnwQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/chunked-blob-reader-native@3.0.0':
    resolution: {integrity: sha512-VDkpCYW+peSuM4zJip5WDfqvg2Mo/e8yxOv3VF1m11y7B8KKMKVFtmZWDe36Fvk8rGuWrPZHHXZ7rR7uM5yWyg==}

  '@smithy/chunked-blob-reader@3.0.0':
    resolution: {integrity: sha512-sbnURCwjF0gSToGlsBiAmd1lRCmSn72nu9axfJu5lIx6RUEgHu6GwTMbqCdhQSi0Pumcm5vFxsi9XWXb2mTaoA==}

  '@smithy/config-resolver@3.0.3':
    resolution: {integrity: sha512-4wHqCMkdfVDP4qmr4fVPYOFOH+vKhOv3X4e6KEU9wIC8xXUQ24tnF4CW+sddGDX1zU86GGyQ7A+rg2xmUD6jpQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/core@2.2.3':
    resolution: {integrity: sha512-SpyLOL2vgE6sUYM6nQfu82OirCPkCDKctyG3aMgjMlDPTJpUlmlNH0ttu9ZWwzEjrzzr8uABmPjJTRI7gk1HFQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/credential-provider-imds@3.1.2':
    resolution: {integrity: sha512-gqVmUaNoeqyrOAjgZg+rTmFLsphh/vS59LCMdFfVpthVS0jbfBzvBmEPktBd+y9ME4DYMGHFAMSYJDK8q0noOQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-codec@3.1.1':
    resolution: {integrity: sha512-s29NxV/ng1KXn6wPQ4qzJuQDjEtxLdS0+g5PQFirIeIZrp66FXVJ5IpZRowbt/42zB5dY8TqJ0G0L9KkgtsEZg==}

  '@smithy/eventstream-serde-browser@3.0.3':
    resolution: {integrity: sha512-ZXKmNAHl6SWKYuVmtoEc/hBQ7Nym/rbAx2SrqoJHn0i9QopIP7fG1AWmoFIeS5R3/VL6AwUIZMR0g8qnjjVRRA==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-config-resolver@3.0.2':
    resolution: {integrity: sha512-QbE3asvvBUZr7PwbOaxkSfKDjTAmWZkqh2G7pkYlD4jRkT1Y9nufeyu0OBPlLoF4+gl3YMpSVO7TESe8bVkD+g==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-node@3.0.3':
    resolution: {integrity: sha512-v61Ftn7x/ubWFqH7GHFAL/RaU7QZImTbuV95DYugYYItzpO7KaHYEuO8EskCaBpZEfzOxhUGKm4teS9YUSt69Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-universal@3.0.3':
    resolution: {integrity: sha512-YXYt3Cjhu9tRrahbTec2uOjwOSeCNfQurcWPGNEUspBhqHoA3KrDrVj+jGbCLWvwkwhzqDnnaeHAxm+IxAjOAQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/fetch-http-handler@3.1.0':
    resolution: {integrity: sha512-s7oQjEOUH9TYjctpITtWF4qxOdg7pBrP9eigEQ8SBsxF3dRFV0S28pGMllC83DUr7ECmErhO/BUwnULfoNhKgQ==}

  '@smithy/hash-blob-browser@3.1.1':
    resolution: {integrity: sha512-8RwdPG7arvL5pfMAFsH6jfBVcC7MDR1LYHjKevZPHREkVtORIQkRfm2K8px7giJt7x0zzQJnWamrsDM4ig8nTQ==}

  '@smithy/hash-node@3.0.2':
    resolution: {integrity: sha512-43uGA6o6QJQdXwAogybdTDHDd3SCdKyoiHIHb8PpdE2rKmVicjG9b1UgVwdgO8QPytmVqHFaUw27M3LZKwu8Yg==}
    engines: {node: '>=16.0.0'}

  '@smithy/hash-stream-node@3.1.1':
    resolution: {integrity: sha512-+uvJHPrFNE9crkh3INVS9FmDcx1DoywDgIzlRWlPy7gqoD8jG14os9ATIFY7wN/ARPz1EWlkCHUap70oXxMmjA==}
    engines: {node: '>=16.0.0'}

  '@smithy/invalid-dependency@3.0.2':
    resolution: {integrity: sha512-+BAY3fMhomtq470tswXyrdVBSUhiLuhBVT+rOmpbz5e04YX+s1dX4NxTLzZGwBjCpeWZNtTxP8zbIvvFk81gUg==}

  '@smithy/is-array-buffer@2.2.0':
    resolution: {integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==}
    engines: {node: '>=14.0.0'}

  '@smithy/is-array-buffer@3.0.0':
    resolution: {integrity: sha512-+Fsu6Q6C4RSJiy81Y8eApjEB5gVtM+oFKTffg+jSuwtvomJJrhUJBu2zS8wjXSgH/g1MKEWrzyChTBe6clb5FQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/md5-js@3.0.2':
    resolution: {integrity: sha512-WlSK9br7fkVucTkCXporwuOttCR3cJ1GV70J8ENYXGNc0nUTPzMdWCyHztgnbbKoekVMjGZOEu+8I52nOdzqwQ==}

  '@smithy/middleware-content-length@3.0.2':
    resolution: {integrity: sha512-/Havz3PkYIEmwpqkyRTR21yJsWnFbD1ec4H1pUL+TkDnE7RCQkAVUQepLL/UeCaZeCBXvfdoKbOjSbV01xIinQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-endpoint@3.0.3':
    resolution: {integrity: sha512-ARAXHodhj4tttKa9y75zvENdSoHq6VGsSi7XS3+yLutrnxttJs6N10UMInCC1yi3/bopT8xug3iOP/y9R6sKJQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-retry@3.0.6':
    resolution: {integrity: sha512-ICsFKp8eAyIMmxN5UT3IU37S6886L879TKtgxPsn/VD/laYNwqTLmJaCAn5//+2fRIrV0dnHp6LFlMwdXlWoUQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-serde@3.0.2':
    resolution: {integrity: sha512-oT2abV5zLhBucJe1LIIFEcRgIBDbZpziuMPswTMbBQNcaEUycLFvX63zsFmqfwG+/ZQKsNx+BSE8W51CMuK7Yw==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-stack@3.0.2':
    resolution: {integrity: sha512-6fRcxomlNKBPIy/YjcnC7YHpMAjRvGUYlYVJAfELqZjkW0vQegNcImjY7T1HgYA6u3pAcCxKVBLYnkTw8z/l0A==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-config-provider@3.1.2':
    resolution: {integrity: sha512-388fEAa7+6ORj/BDC70peg3fyFBTTXJyXfXJ0Bwd6FYsRltePr2oGzIcm5AuC1WUSLtZ/dF+hYOnfTMs04rLvA==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-http-handler@3.1.0':
    resolution: {integrity: sha512-pOpgB6B+VLXLwAyyvRz+ZAVXABlbAsJ2xvn3WZvrppAPImxwQOPFbeSUzWYMhpC8Tr7yQ3R8fG990QDhskkf1Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/property-provider@3.1.2':
    resolution: {integrity: sha512-Hzp32BpeFFexBpO1z+ts8okbq/VLzJBadxanJAo/Wf2CmvXMBp6Q/TLWr7Js6IbMEcr0pDZ02V3u1XZkuQUJaA==}
    engines: {node: '>=16.0.0'}

  '@smithy/protocol-http@4.0.2':
    resolution: {integrity: sha512-X/90xNWIOqSR2tLUyWxVIBdatpm35DrL44rI/xoeBWUuanE0iyCXJpTcnqlOpnEzgcu0xCKE06+g70TTu2j7RQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-builder@3.0.2':
    resolution: {integrity: sha512-xhv1+HacDYsOLdNt7zW+8Fe779KYAzmWvzs9bC5NlKM8QGYCwwuFwDBynhlU4D5twgi2pZ14Lm4h6RiAazCtmA==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-parser@3.0.2':
    resolution: {integrity: sha512-C5hyRKgrZGPNh5QqIWzXnW+LXVrPmVQO0iJKjHeb5v3C61ZkP9QhrKmbfchcTyg/VnaE0tMNf/nmLpQlWuiqpg==}
    engines: {node: '>=16.0.0'}

  '@smithy/service-error-classification@3.0.2':
    resolution: {integrity: sha512-cu0WV2XRttItsuXlcM0kq5MKdphbMMmSd2CXF122dJ75NrFE0o7rruXFGfxAp3BKzgF/DMxX+PllIA/cj4FHMw==}
    engines: {node: '>=16.0.0'}

  '@smithy/shared-ini-file-loader@3.1.2':
    resolution: {integrity: sha512-tgnXrXbLMO8vo6VeuqabMw/eTzQHlLmZx0TC0TjtjJghnD0Xl4pEnJtBjTJr6XF5fHMNrt5BcczDXHJT9yNQnA==}
    engines: {node: '>=16.0.0'}

  '@smithy/signature-v4@3.1.1':
    resolution: {integrity: sha512-2/vlG86Sr489XX8TA/F+VDA+P04ESef04pSz0wRtlQBExcSPjqO08rvrkcas2zLnJ51i+7ukOURCkgqixBYjSQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/smithy-client@3.1.4':
    resolution: {integrity: sha512-y6xJROGrIoitjpwXLY7P9luDHvuT9jWpAluliuSFdBymFxcl6iyQjo9U/JhYfRHFNTruqsvKOrOESVuPGEcRmQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/types@3.2.0':
    resolution: {integrity: sha512-cKyeKAPazZRVqm7QPvcPD2jEIt2wqDPAL1KJKb0f/5I7uhollvsWZuZKLclmyP6a+Jwmr3OV3t+X0pZUUHS9BA==}
    engines: {node: '>=16.0.0'}

  '@smithy/url-parser@3.0.2':
    resolution: {integrity: sha512-pRiPHrgibeAr4avtXDoBHmTLtthwA4l8jKYRfZjNgp+bBPyxDMPRg2TMJaYxqbKemvrOkHu9MIBTv2RkdNfD6w==}

  '@smithy/util-base64@3.0.0':
    resolution: {integrity: sha512-Kxvoh5Qtt0CDsfajiZOCpJxgtPHXOKwmM+Zy4waD43UoEMA+qPxxa98aE/7ZhdnBFZFXMOiBR5xbcaMhLtznQQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-body-length-browser@3.0.0':
    resolution: {integrity: sha512-cbjJs2A1mLYmqmyVl80uoLTJhAcfzMOyPgjwAYusWKMdLeNtzmMz9YxNl3/jRLoxSS3wkqkf0jwNdtXWtyEBaQ==}

  '@smithy/util-body-length-node@3.0.0':
    resolution: {integrity: sha512-Tj7pZ4bUloNUP6PzwhN7K386tmSmEET9QtQg0TgdNOnxhZvCssHji+oZTUIuzxECRfG8rdm2PMw2WCFs6eIYkA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-buffer-from@2.2.0':
    resolution: {integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-buffer-from@3.0.0':
    resolution: {integrity: sha512-aEOHCgq5RWFbP+UDPvPot26EJHjOC+bRgse5A8V3FSShqd5E5UN4qc7zkwsvJPPAVsf73QwYcHN1/gt/rtLwQA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-config-provider@3.0.0':
    resolution: {integrity: sha512-pbjk4s0fwq3Di/ANL+rCvJMKM5bzAQdE5S/6RL5NXgMExFAi6UgQMPOm5yPaIWPpr+EOXKXRonJ3FoxKf4mCJQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-defaults-mode-browser@3.0.6':
    resolution: {integrity: sha512-tAgoc++Eq+KL7g55+k108pn7nAob3GLWNEMbXhZIQyBcBNaE/o3+r4AEbae0A8bWvLRvArVsjeiuhMykGa04/A==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-defaults-mode-node@3.0.6':
    resolution: {integrity: sha512-UNerul6/E8aiCyFTBHk+RSIZCo7m96d/N5K3FeO/wFeZP6oy5HAicLzxqa85Wjv7MkXSxSySX29L/LwTV/QMag==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-endpoints@2.0.3':
    resolution: {integrity: sha512-Dyi+pfLglDHSGsKSYunuUUSFM5V0tz7UDgv1Ex97yg+Xkn0Eb0rH0rcvl1n0MaJ11fac3HKDOH0DkALyQYCQag==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-hex-encoding@3.0.0':
    resolution: {integrity: sha512-eFndh1WEK5YMUYvy3lPlVmYY/fZcQE1D8oSf41Id2vCeIkKJXPcYDCZD+4+xViI6b1XSd7tE+s5AmXzz5ilabQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-middleware@3.0.2':
    resolution: {integrity: sha512-7WW5SD0XVrpfqljBYzS5rLR+EiDzl7wCVJZ9Lo6ChNFV4VYDk37Z1QI5w/LnYtU/QKnSawYoHRd7VjSyC8QRQQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-retry@3.0.2':
    resolution: {integrity: sha512-HUVOb1k8p/IH6WFUjsLa+L9H1Zi/FAAB2CDOpWuffI1b2Txi6sknau8kNfC46Xrt39P1j2KDzCE1UlLa2eW5+A==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-stream@3.0.4':
    resolution: {integrity: sha512-CcMioiaOOsEVdb09pS7ux1ij7QcQ2jE/cE1+iin1DXMeRgAEQN/47m7Xztu7KFQuQsj0A5YwB2UN45q97CqKCg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-uri-escape@3.0.0':
    resolution: {integrity: sha512-LqR7qYLgZTD7nWLBecUi4aqolw8Mhza9ArpNEQ881MJJIU2sE5iHCK6TdyqqzcDLy0OPe10IY4T8ctVdtynubg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-utf8@2.3.0':
    resolution: {integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@3.0.0':
    resolution: {integrity: sha512-rUeT12bxFnplYDe815GXbq/oixEGHfRFFtcTF3YdDi/JaENIM6aSYYLJydG83UNzLXeRI5K8abYd/8Sp/QM0kA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-waiter@3.1.1':
    resolution: {integrity: sha512-xT+Bbpe5sSrC7cCWSElOreDdWzqovR1V+7xrp+fmwGAA+TPYBb78iasaXjO1pa+65sY6JjW5GtGeIoJwCK9B1g==}
    engines: {node: '>=16.0.0'}

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==}
    engines: {node: '>= 6'}

  '@tootallnate/once@2.0.0':
    resolution: {integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==}
    engines: {node: '>= 10'}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.6.8':
    resolution: {integrity: sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.20.6':
    resolution: {integrity: sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==}

  '@types/caseless@0.12.5':
    resolution: {integrity: sha512-hWtVTC2q7hc7xZ/RLbxapMvDMgUnDvKvMOpKal4DrMyfGBUfB1oKaZlIRr6mJL+If3bAP6sV/QneGzF6tJjZDg==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/graceful-fs@4.1.9':
    resolution: {integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/lodash@4.17.7':
    resolution: {integrity: sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA==}

  '@types/long@4.0.2':
    resolution: {integrity: sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==}

  '@types/ms@0.7.34':
    resolution: {integrity: sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g==}

  '@types/node-fetch@2.6.11':
    resolution: {integrity: sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==}

  '@types/node@18.19.38':
    resolution: {integrity: sha512-SApYXUF7si4JJ+lO2o6X60OPOnA6wPpbiB09GMCkQ+JAwpa9hxUVG8p7GzA08TKQn5OhzK57rj1wFj+185YsGg==}

  '@types/node@20.16.5':
    resolution: {integrity: sha512-VwYCweNo3ERajwy0IUlqqcyZ8/A7Zwa9ZP3MnENWcB11AejO+tLy3pu850goUW2FC/IJMdZUfKpX/yxL1gymCA==}

  '@types/request@2.48.12':
    resolution: {integrity: sha512-G3sY+NpsA9jnwm0ixhAFQSJ3Q9JkpLZpJbI3GMv0mIAT0y3mRabYeINzal5WOChIiaTEGQYlHOKgkaM9EisWHw==}

  '@types/stack-utils@2.0.3':
    resolution: {integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==}

  '@types/yargs@17.0.33':
    resolution: {integrity: sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==}

  '@xmldom/xmldom@0.7.13':
    resolution: {integrity: sha512-lm2GW5PkosIzccsaZIz7tp8cPADSIlIHWDFTR1N0SzfinhhYgeIQjFMz4rYzanCScr3DqQLeomUDArp6MWKm+g==}
    engines: {node: '>=10.0.0'}

  a-sync-waterfall@1.0.1:
    resolution: {integrity: sha512-RYTOHHdWipFUliRFMCS4X2Yn2X8M87V/OpSqWzKKOGhzqyUxzyVmhHDH9sAvG+ZuQf/TAOFsLCpMw09I1ufUnA==}

  abbrev@1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  abstract-logging@2.0.1:
    resolution: {integrity: sha512-2BjRTZxTPvheOvGbBslFSYOUkr+SjPtOnrLP33f+VIWLzezQpZcqVg7ja3L4dBXmzzgwT+a029jRx5PCi3JuiA==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.12.1:
    resolution: {integrity: sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acquerello@2.0.8:
    resolution: {integrity: sha512-+Xm1S7eutT6OzPVuW4jhpS0YlhdnEFVHOZOiVghLGDk7rLoQc1MQmT4jsVkuDCTbqIZy21gnsrTXY9Ya2sQvtw==}
    engines: {node: '>= 18.18.0'}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.1:
    resolution: {integrity: sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==}
    engines: {node: '>= 14'}

  agentkeepalive@4.5.0:
    resolution: {integrity: sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==}
    engines: {node: '>= 8.0.0'}

  aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-formats@3.0.1:
    resolution: {integrity: sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@5.1.0:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.16.0:
    resolution: {integrity: sha512-F0twR8U1ZU67JIEtekUcLkXkoO5mMMmgGD8sK/xUFzJ805jxHQl92hImFAqqXMyMYjSPOyUPAwHYhB72g5sTXw==}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  append-transform@2.0.0:
    resolution: {integrity: sha512-7yeyCEurROLQJFv5Xj4lEGTy0borxepjFv1g22oAdqFu//SrAlDl1O1Nxx15SH1RoliUml6p8dwJW9jvZughhg==}
    engines: {node: '>=8'}

  aproba@2.0.0:
    resolution: {integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==}

  archy@1.0.0:
    resolution: {integrity: sha512-Xg+9RwCg/0p32teKdGMPTPnVXKD0w3DfHnFTficozsAgsvq2XenPJq/MYpzzQ/v8zrOyJn6Ds39VA4JIDwFfqw==}

  are-we-there-yet@2.0.0:
    resolution: {integrity: sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==}
    engines: {node: '>=10'}
    deprecated: This package is no longer supported.

  are-we-there-yet@3.0.1:
    resolution: {integrity: sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}

  asn1@0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==}

  assert-plus@1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==}
    engines: {node: '>=0.8'}

  assert@1.5.1:
    resolution: {integrity: sha512-zzw1uCAgLbsKwBfFc8CX78DDg+xZeBksSO3vwVIDDN5i94eOrPsSSyiVhmsSABFDM/OcpE2aagCat9dnWQLG1A==}

  astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}

  async-hook-domain@2.0.4:
    resolution: {integrity: sha512-14LjCmlK1PK8eDtTezR6WX8TMaYNIzBIsd2D1sGoGjgx0BuNMMoSdk7i/drlbtamy0AWv9yv2tkB+ASdmeqFIw==}
    engines: {node: '>=10'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  atomic-sleep@1.0.0:
    resolution: {integrity: sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==}
    engines: {node: '>=8.0.0'}

  avvio@8.3.2:
    resolution: {integrity: sha512-st8e519GWHa/azv8S87mcJvZs4WsgTBjOw/Ih1CP6u+8SZvcOeAYNG6JbsIrAUUJJ7JfmrnOkR8ipDS+u9SIRQ==}

  aws-sign2@0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==}

  aws4@1.13.0:
    resolution: {integrity: sha512-3AungXC4I8kKsS9PuS4JH2nc+0bVY/mjgrephHTIi8fpEeGsTHBUJeosp0Wc1myYMElmD0B3Oc4XL/HVJ4PV2g==}

  axios-debug-log@1.0.0:
    resolution: {integrity: sha512-ZjMaEBEij9w+Vbk2Uc3XflchTT7j9rZdYD/snN+XQ5FRDq1QjZNhh0Izb3KSyarU5vTkiCvJyg1xDiQBHZZB9w==}
    peerDependencies:
      axios: '>=1.0.0'

  axios-ntlm@1.4.2:
    resolution: {integrity: sha512-8mS/uhmSWiRBiFKQvysPbX1eDBp6e+eXskmasuAXRHrn1Zjgji3O/oGXzXLw7tOhyD9nho1vGjZ2OYOD3cCvHg==}

  axios@1.7.2:
    resolution: {integrity: sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==}

  babel-jest@29.7.0:
    resolution: {integrity: sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}

  babel-plugin-jest-hoist@29.6.3:
    resolution: {integrity: sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  babel-plugin-polyfill-corejs2@0.4.11:
    resolution: {integrity: sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.10.6:
    resolution: {integrity: sha512-b37+KR2i/khY5sKmWNVQAnitvquQbNdWy6lJdsr0kmquCKEEUgMKK4SboVM3HtfnZilfjr4MMQ7vY58FVWDtIA==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.2:
    resolution: {integrity: sha512-2R25rQZWP63nGwaAswvDazbPXfrM3HwVoBXK6HcqeKrSrL/JqcC/rDcf95l4r7LXLyxDXc8uQDa064GubtCABg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-preset-current-node-syntax@1.1.0:
    resolution: {integrity: sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-jest@29.6.3:
    resolution: {integrity: sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==}

  better-sqlite3@9.6.0:
    resolution: {integrity: sha512-yR5HATnqeYNVnkaUTf4bOP2dJSnyhP4puJN/QPRyx4YkBEEUxib422n2XzPqDEHjQQqazoYoADdAm5vE15+dAQ==}

  big-integer@1.6.52:
    resolution: {integrity: sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==}
    engines: {node: '>=0.6'}

  bignumber.js@9.1.2:
    resolution: {integrity: sha512-2/mKyZH9K85bzOEfhXDBFZTGd1CTs+5IHpeFQo9luiBG7hghdC851Pj2WAhb6E3R6b9tZj/XKhbg4fum+Kepug==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  bind-obj-methods@3.0.0:
    resolution: {integrity: sha512-nLEaaz3/sEzNSyPWRsN9HNsqwk1AUyECtGj+XwGdIi3xABnEqecvXtIJ0wehQXuuER5uZ/5fTs2usONgYjG+iw==}
    engines: {node: '>=10'}

  bindings@1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  bluebird@3.4.7:
    resolution: {integrity: sha512-iD3898SR7sWVRHbiQv+sHUtHnMvC1o3nW5rAcqnq3uOn07DSAppZYUkIGslDz6gXC7HfunPe7YVBgoEJASPcHA==}

  bmp-js@0.1.0:
    resolution: {integrity: sha512-vHdS19CnY3hwiNdkaqk93DvjVLfbEcI8mys4UjuWrlX1haDmroo8o4xCzh4wD6DGV6HxRCyauwhHRqMTfERtjw==}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.23.1:
    resolution: {integrity: sha512-TUfofFo/KsK/bWZ9TWQ5O26tsWW4Uhmt8IYklbnUa70udB6P2wA7w7o4PY4muaEPBQaAX+CEnmmIA41NVHtPVw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  browserslist@4.23.3:
    resolution: {integrity: sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bser@2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}

  buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer-writer@2.0.0:
    resolution: {integrity: sha512-a7ZpuTZU1TRtnwyCNW3I5dc0wWNC3VR9S++Ewyk2HHZdrO3CQJqSpd+95Us590V6AL7JqUAH2IwZ/398PmNFgw==}
    engines: {node: '>=4'}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  cacache@15.3.0:
    resolution: {integrity: sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==}
    engines: {node: '>= 10'}

  caching-transform@4.0.0:
    resolution: {integrity: sha512-kpqOvwXnjjN44D89K5ccQC+RUrsy7jB/XLlRrx0D7/2HNcTPqzsb6XgYoErwko6QsV184CA2YgS1fxDiiDZMWA==}
    engines: {node: '>=8'}

  call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  caniuse-lite@1.0.30001636:
    resolution: {integrity: sha512-bMg2vmr8XBsbL6Lr0UHXy/21m84FTxDLWn2FSqMd5PrlbMxwJlQnC2YWYxVgp66PZE+BBNF2jYQUBKCo1FDeZg==}

  caniuse-lite@1.0.30001662:
    resolution: {integrity: sha512-sgMUVwLmGseH8ZIrm1d51UbrhqMCH3jvS7gF/M6byuHOnKyLOBL7W8yz5V02OHwgLGA36o/AFhWzzh4uc5aqTA==}

  canvas@2.11.2:
    resolution: {integrity: sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==}
    engines: {node: '>=6'}

  caseless@0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  char-regex@1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==}
    engines: {node: '>=10'}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chownr@1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}

  chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}

  ci-info@3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==}
    engines: {node: '>=8'}

  cjs-module-lexer@1.4.1:
    resolution: {integrity: sha512-cuSVIHi9/9E/+821Qjdvngor+xpnlwnuwIyZOaLmHBVdXL+gP+I6QQB9VkO7RI77YIcTV+S1W9AreJ5eN63JBA==}

  clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}

  cli@1.0.1:
    resolution: {integrity: sha512-41U72MB56TfUMGndAKK8vJ78eooOD4Z5NOL4xEfjc0c23s+6EYKXlXsmACBVclLP1yOfWCgEganVzddVrSNoTg==}
    engines: {node: '>=0.2.5'}

  cliui@6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  close-with-grace@1.3.0:
    resolution: {integrity: sha512-lvm0rmLIR5bNz4CRKW6YvCfn9Wg5Wb9A8PJ3Bb+hjyikgC1RO1W3J4z9rBXQYw97mAte7dNSQI8BmUsxdlXQyw==}

  co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  collect-v8-coverage@1.0.2:
    resolution: {integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-support@1.1.3:
    resolution: {integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==}
    hasBin: true

  colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@5.1.0:
    resolution: {integrity: sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==}
    engines: {node: '>= 6'}

  commist@3.2.0:
    resolution: {integrity: sha512-4PIMoPniho+LqXmpS5d3NuGYncG6XWlkBSVGiWycL22dd42OYdUGil2CWuzklaJoNxyxUSpO4MKIBU94viWNAw==}

  commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  console-browserify@1.1.0:
    resolution: {integrity: sha512-duS7VP5pvfsNLDvL1O4VOEbw37AI3A4ZUQYemvDlnpGrNu9tprR7BYWpDYwC0Xia0Zxz5ZupdiIrUp0GH1aXfg==}

  console-control-strings@1.1.0:
    resolution: {integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  cookie@0.6.0:
    resolution: {integrity: sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==}
    engines: {node: '>= 0.6'}

  core-js-compat@3.38.1:
    resolution: {integrity: sha512-JRH6gfXxGmrzF3tZ57lFx97YARxCXPaMzPo6jELZhv88pBH5VXpQ+y0znKGlFnzuaihqhLbefxSJxWJMPtfDzw==}

  core-util-is@1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  create-jest@29.7.0:
    resolution: {integrity: sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true

  cron-parser@4.9.0:
    resolution: {integrity: sha512-p0SaNjrHOnQeR8/VnfGbmg9te2kfyYSQ7Sc/j/6DtPL3JQvKxmjO9TSjNFpujqV3vEYYBvNNvXSxzyksBWAx1Q==}
    engines: {node: '>=12.0.0'}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  csv-parser@3.0.0:
    resolution: {integrity: sha512-s6OYSXAK3IdKqYO33y09jhypG/bSDHPuyCme/IdEHfWpLf/jKcpitVFyOC6UemgGk8v7Q5u2XE0vvwmanxhGlQ==}
    engines: {node: '>= 10'}
    hasBin: true

  dashdash@1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==}
    engines: {node: '>=0.10'}

  date-now@0.1.4:
    resolution: {integrity: sha512-AsElvov3LoNB7tf5k37H2jYSB+ZZPMT5sG2QjJCcdlV5chIv6htBUBUui2IKRjgtKAKtCBN7Zbwa+MtwLjSeNw==}

  dateformat@4.6.3:
    resolution: {integrity: sha512-2P0p0pFGzHS5EMnhdxQi7aJN+iMheud0UhG4dlE1DLAlvL8JHjJJTX/CSm4JXwV0Ka5nGk3zC5mcb5bUQUxxMA==}

  debug@4.3.5:
    resolution: {integrity: sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  decompress-response@4.2.1:
    resolution: {integrity: sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==}
    engines: {node: '>=8'}

  decompress-response@6.0.0:
    resolution: {integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==}
    engines: {node: '>=10'}

  dedent@1.5.3:
    resolution: {integrity: sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-extend@0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  default-require-extensions@3.0.1:
    resolution: {integrity: sha512-eXTJmRbm2TIt9MgWTsOH1wEuhew6XGZcMeGKCtLedIg/NCsg1iBePXkceTdK4Fii7pzmN9tGsZhKzZ4h7O/fxw==}
    engines: {node: '>=8'}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  delay@5.0.0:
    resolution: {integrity: sha512-ReEBKkIfe4ya47wlPYf/gu5ib6yUG0/Aez0JQZQz94kiWtRQvZIQbTiehsnwHvLSWJnQdhVeqYue7Id1dKr0qw==}
    engines: {node: '>=10'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  des.js@1.1.0:
    resolution: {integrity: sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==}

  desm@1.3.1:
    resolution: {integrity: sha512-vgTAOosB1aHrmzjGnzFCbjvXbk8QAOC/36JxJhcBkeAuUy8QwRFxAWBHemiDpUB3cbrBruFUdzpUS21aocvaWg==}

  detect-libc@2.0.3:
    resolution: {integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==}
    engines: {node: '>=8'}

  detect-newline@3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==}
    engines: {node: '>=8'}

  dev-null@0.1.1:
    resolution: {integrity: sha512-nMNZG0zfMgmdv8S5O0TM5cpwNbGKRGPCxVsr0SmA3NZZy9CYBbuNLL0PD3Acx9e5LIUgwONXtM9kM6RlawPxEQ==}

  dezalgo@1.0.4:
    resolution: {integrity: sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==}

  diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}

  dom-serializer@0.2.2:
    resolution: {integrity: sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==}

  domelementtype@1.3.1:
    resolution: {integrity: sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@2.3.0:
    resolution: {integrity: sha512-q9bUwjfp7Eif8jWxxxPSykdRZAb6GkguBGSgvvCrhI9wB71W2K/Kvv4E61CF/mcCfnVJDeDWx/Vb/uAqbDj6UQ==}

  dommatrix@1.0.3:
    resolution: {integrity: sha512-l32Xp/TLgWb8ReqbVJAFIvXmY7go4nTxxlWiAFyhoQw9RKEOHBZNnyGvJWqDVSPmq3Y9HlM4npqF/T6VMOXhww==}
    deprecated: dommatrix is no longer maintained. Please use @thednp/dommatrix.

  domutils@1.5.1:
    resolution: {integrity: sha512-gSu5Oi/I+3wDENBsOWBiRK1eoGxcywYSqg3rR960/+EfY0CF4EX1VPkgHOZ3WiS/Jg2DtliF6BhWcHlfpYUcGw==}

  dotenv-expand@10.0.0:
    resolution: {integrity: sha512-GopVGCpVS1UKH75VKHGuQFqS1Gusej0z4FyQkPdwjil2gNIv+LNsqBlboOzpJFZKVT95GkCyWJbBSdFEFUWI2A==}
    engines: {node: '>=12'}

  dotenv@16.4.5:
    resolution: {integrity: sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==}
    engines: {node: '>=12'}

  duplexer2@0.1.4:
    resolution: {integrity: sha512-asLFVfWWtJ90ZyOUHMqk7/S2w2guQKxUI2itj3d92ADHhxUSbCMGi1f1cBcJ7xM1To+pE/Khbwo1yuNbMEPKeA==}

  duplexify@4.1.3:
    resolution: {integrity: sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==}

  electron-to-chromium@1.4.808:
    resolution: {integrity: sha512-0ItWyhPYnww2VOuCGF4s1LTfbrdAV2ajy/TN+ZTuhR23AHI6rWHCrBXJ/uxoXOvRRqw8qjYVrG81HFI7x/2wdQ==}

  electron-to-chromium@1.5.25:
    resolution: {integrity: sha512-kMb204zvK3PsSlgvvwzI3wBIcAw15tRkYk+NQdsjdDtcQWTp2RABbMQ9rUBy8KNEOM+/E6ep+XC3AykiWZld4g==}

  emittery@0.13.1:
    resolution: {integrity: sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==}
    engines: {node: '>=12'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  encoding@0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}

  end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}

  entities@1.0.0:
    resolution: {integrity: sha512-LbLqfXgJMmy81t+7c14mnulFHJ170cM6E+0vMXR9k/ZiZwgX8i5pNgjTCX3SO4VeUsFLV+8InixoretwU+MjBQ==}

  entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}

  env-paths@2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==}
    engines: {node: '>=6'}

  env-schema@5.2.1:
    resolution: {integrity: sha512-gWMNrQ3dVHAZcCx7epiFwgXcyfBh4heD/6+OK3bEbke3uL+KqwYA9nUOwzJyRZh1cJOFcwdPuY1n0GKSFlSWAg==}

  err-code@2.0.3:
    resolution: {integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es6-error@4.1.1:
    resolution: {integrity: sha512-Um/+FxMr9CISWh0bi5Zv0iOD+4cFh5qLeks1qhAopKVAJw3drgKbKySikp7wGhDL0HPeaja0P5ULZrxLkniUVg==}

  escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-compat-utils@0.5.1:
    resolution: {integrity: sha512-3z3vFexKIEnjHE3zCMRo6fn/e44U7T1khUjg+Hp0ZQMCigh28rALD0nPFBcGZuiLC5rLZa2ubQHDRln09JfU2Q==}
    engines: {node: '>=12'}
    peerDependencies:
      eslint: '>=6.0.0'

  eslint-config-prettier@9.1.0:
    resolution: {integrity: sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-jsonc@2.16.0:
    resolution: {integrity: sha512-Af/ZL5mgfb8FFNleH6KlO4/VdmDuTqmM+SPnWcdoWywTetv7kq+vQe99UyQb9XO3b0OWLVuTH7H0d/PXYCMdSg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'

  eslint-plugin-prettier@5.2.1:
    resolution: {integrity: sha512-gH3iR3g4JfF+yYPaJYkN7jEl9QbweL/YfkoRlNnuIEHEz1vHVlCmWOS+eGGiRuzHQXdJFCOTxRgvju9b8VUmrw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '*'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-yaml@1.0.3:
    resolution: {integrity: sha512-PNA2P38xFIrIdb82W0k66UPtpn0UHbPs8LxwtmZqG03PUzV1RE6OYFUi0pxL4LsFndHjgOyNlLqK2N5lGdX8ew==}
    engines: {node: '>=12'}

  eslint-plugin-yml@1.14.0:
    resolution: {integrity: sha512-ESUpgYPOcAYQO9czugcX5OqRvn/ydDVwGCPXY4YjPqc09rHaUVUA6IE6HLQys4rXk/S+qx3EwTd1wHCwam/OWQ==}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-scope@8.0.2:
    resolution: {integrity: sha512-6E4xmrTw5wtxnLA5wYL3WDfhZ/1bUBGOXV0zQvVRDOtrR8D0p6W7fs3JweNYhwRYeGvd/1CKX2se0/2s7Q/nJA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.0.0:
    resolution: {integrity: sha512-OtIRv/2GyiF6o/d8K7MYKKbXrOUBIK6SfkIRM4Z0dY3w+LiQ0vy3F57m0Z71bjbyeiWFiHJ8brqnmE6H6/jEuw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.10.0:
    resolution: {integrity: sha512-Y4D0IgtBZfOcOUAIQTSXBKoNGfY0REGqHJG6+Q81vNippW5YlKjHFj4soMxamKK1NXHUWuBZTLdU3Km+L/pcHw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.1.0:
    resolution: {integrity: sha512-M1M6CpiE6ffoigIOWYO9UDP8TMUw9kqb21tf+08IgDYjCsOvCuDt4jQcZmoYxx+w7zlKw9/N0KXfto+I8/FrXA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  events-to-array@1.1.2:
    resolution: {integrity: sha512-inRWzRY7nG+aXZxBzEqYKB3HPgwflZRopAjDCHv0whhRx+MTUr1ei0ICZUypdyE0HRm4L2d5VEcIqLD6yl+BFA==}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}

  expand-template@2.0.3:
    resolution: {integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==}
    engines: {node: '>=6'}

  expect@29.7.0:
    resolution: {integrity: sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  extsprintf@1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==}
    engines: {'0': node >=0.6.0}

  extsprintf@1.4.1:
    resolution: {integrity: sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==}
    engines: {'0': node >=0.6.0}

  fast-content-type-parse@1.1.0:
    resolution: {integrity: sha512-fBHHqSTFLVnR61C+gltJuE5GkVQMV0S2nqUO8TJ+5Z3qAKG8vAx4FKai1s5jq/inV1+sREynIWSuQ6HgoSXpDQ==}

  fast-copy@3.0.2:
    resolution: {integrity: sha512-dl0O9Vhju8IrcLndv2eU4ldt1ftXMqqfgN4H1cpmGV7P6jeB9FwpN9a2c8DPGE1Ys88rNUJVYDHq73CGAGOPfQ==}

  fast-decode-uri-component@1.0.1:
    resolution: {integrity: sha512-WKgKWg5eUxvRZGwW8FvfbaH7AXSh2cL+3j5fMGzUMCxWBJ3dV3a7Wz8y2f/uQ0e3B6WmodD3oS54jTQ9HVTIIg==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-json-stringify@5.16.1:
    resolution: {integrity: sha512-KAdnLvy1yu/XrRtP+LJnxbBGrhN+xXu+gt3EUvZhYGKCr3lFHq/7UFJHHFgmJKoqlh6B40bZLEv7w46B0mqn1g==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-querystring@1.1.2:
    resolution: {integrity: sha512-g6KuKWmFXc0fID8WWH0jit4g0AGBoJhCkJMb1RmbsSEUNvQ+ZC8D6CUZ+GtF8nMzSPXnhiePyyqqipzNNEnHjg==}

  fast-redact@3.5.0:
    resolution: {integrity: sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==}
    engines: {node: '>=6'}

  fast-safe-stringify@2.1.1:
    resolution: {integrity: sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==}

  fast-uri@2.4.0:
    resolution: {integrity: sha512-ypuAmmMKInk5q7XcepxlnUWDLWv4GFtaJqAzWKqn62IpQ3pejtr5dTVbt3vwqVaMKmkNR55sTT+CqUKIaT21BA==}

  fast-xml-parser@4.2.5:
    resolution: {integrity: sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==}
    hasBin: true

  fast-xml-parser@4.4.0:
    resolution: {integrity: sha512-kLY3jFlwIYwBNDojclKsNAC12sfD6NwW74QB2CoNGPvtVxjliYehVunB3HYyNi+n4Tt1dAcgwYvmKF/Z18flqg==}
    hasBin: true

  fastify-cli@5.9.0:
    resolution: {integrity: sha512-CaIte5SwkLuvlzpdd1Al1VRVVwm2TQVV4bfVP4oz/Z54KVSo6pqNTgnxWOmyzdcNUbFnhJ3Z4vRjzvHoymP5cQ==}
    hasBin: true

  fastify-plugin@4.5.1:
    resolution: {integrity: sha512-stRHYGeuqpEZTL1Ef0Ovr2ltazUT9g844X5z/zEBFLG8RYlpDiOCIG+ATvYEp+/zmc7sN29mcIMp8gvYplYPIQ==}

  fastify-print-routes@3.2.0:
    resolution: {integrity: sha512-vbNliaT+SlZlH9sPYR1FYe3AJptbadf20/SjmramOxoeS6fdrAkMQu0H0/ZENaU4ruBlxCrUs0hUfV2J0dGyZw==}
    engines: {node: '>= 18.18.0'}

  fastify-qs@4.0.2:
    resolution: {integrity: sha512-grnKBvzxgMPWrGo+hx4luspmgz+vw5BYp7LNDavfS8lfwRDganFHrNX7N5cWN2LlkgHdtDnIoKjl6lAybKhApQ==}

  fastify-raw-body@4.3.0:
    resolution: {integrity: sha512-F4o8ZIMVx4YoxGfwrZys6wyjl40gF3Yv6AWWRy62ozFAyZBSS831/uyyCAqKYw3tR73g180ryG98yih6To1PUQ==}
    engines: {node: '>= 10'}

  fastify@4.28.0:
    resolution: {integrity: sha512-HhW7UHW07YlqH5qpS0af8d2Gl/o98DhJ8ZDQWHRNDnzeOhZvtreWsX8xanjGgXmkYerGbo8ax/n40Dpwqkot8Q==}

  fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}

  fb-watchman@2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  file-uri-to-path@1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}

  find-my-way@7.7.0:
    resolution: {integrity: sha512-+SrHpvQ52Q6W9f3wJoJBbAQULJuNEEQwBvlvYwACDhBTLOTMiQ0HYWh4+vC3OivGP2ENcTI1oKlFA2OepJNjhQ==}
    engines: {node: '>=14'}

  find-my-way@8.2.0:
    resolution: {integrity: sha512-HdWXgFYc6b1BJcOBDBwjqWuHJj1WYiqrxSh25qtU4DabpMFdj/gSunNBQb83t+8Zt67D7CXEzJWTkxaShMTMOA==}
    engines: {node: '>=14'}

  find-up@3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  findit@2.0.0:
    resolution: {integrity: sha512-ENZS237/Hr8bjczn5eKuBohLgaD0JyUd0arxretR1f9RO46vZHA1b2y0VorgGV3WaOT3c+78P8h7v4JGJ1i/rg==}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flatted@3.3.1:
    resolution: {integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==}

  follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@2.0.0:
    resolution: {integrity: sha512-dCIq9FpEcyQyXKCkyzmlPTFNgrCzPudOe+mhvJU5zAtlBnGVy2yKxtfsxK2tQBThwq225jcvBjpw1Gr40uzZCA==}
    engines: {node: '>=8.0.0'}

  foreground-child@3.2.1:
    resolution: {integrity: sha512-PXUUyLqrR2XCWICfv6ukppP96sdFwWbNEnfEMt7jNsISjMsvaLNinAHNDYyvkyU+SZG2BTSbT5NjG+vZslfGTA==}
    engines: {node: '>=14'}

  forever-agent@0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==}

  form-data-encoder@1.7.2:
    resolution: {integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==}

  form-data@2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}

  form-data@2.5.1:
    resolution: {integrity: sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA==}
    engines: {node: '>= 0.12'}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  formdata-node@4.4.1:
    resolution: {integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==}
    engines: {node: '>= 12.20'}

  formidable@2.1.2:
    resolution: {integrity: sha512-CM3GuJ57US06mlpQ47YcunuUZ9jpm8Vx+P2CGt2j7HpgkKZO/DJYQ0Bobim8G6PFQmK5lOqOOdUXboU+h73A4g==}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fromentries@1.3.2:
    resolution: {integrity: sha512-cHEpEQHUg0f8XdtZCc2ZAhrHzKzT0MrFUTcvx+hfxYu7rGMDc5SKoXFh+n4YigxsHXRzc6OrCshdR1bWH6HHyg==}

  fs-constants@1.0.0:
    resolution: {integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==}

  fs-exists-cached@1.0.0:
    resolution: {integrity: sha512-kSxoARUDn4F2RPXX48UXnaFKwVU7Ivd/6qpzZL29MCDmr9sTvybv4gFCp+qaI4fM9m0z9fgz/yJvi56GAz+BZg==}

  fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fstream@1.0.12:
    resolution: {integrity: sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg==}
    engines: {node: '>=0.6'}
    deprecated: This package is no longer supported.

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function-loop@2.0.1:
    resolution: {integrity: sha512-ktIR+O6i/4h+j/ZhZJNdzeI4i9lEPeEK6UPR2EVyTVBqOwcU3Za9xYKLH64ZR9HmcROyRrOkizNyjjtWJzDDkQ==}

  gauge@3.0.2:
    resolution: {integrity: sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==}
    engines: {node: '>=10'}
    deprecated: This package is no longer supported.

  gauge@4.0.4:
    resolution: {integrity: sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  gaxios@6.7.1:
    resolution: {integrity: sha512-LDODD4TMYx7XXdpwxAVRAIAuB0bzv0s+ywFonY46k126qzQHT9ygyoa9tncmOiQmmDrik65UYsEkv3lbfqQ3yQ==}
    engines: {node: '>=14'}

  gcp-metadata@6.1.0:
    resolution: {integrity: sha512-Jh/AIwwgaxan+7ZUUmRLCjtchyDiqh4KjBJ5tW3plBZb5iL/BPcso8A5DlzeD9qlw0duCamnNdpFjxwaT0KyKg==}
    engines: {node: '>=14'}

  generify@4.2.0:
    resolution: {integrity: sha512-b4cVhbPfbgbCZtK0dcUc1lASitXGEAIqukV5DDAyWm25fomWnV+C+a1yXvqikcRZXHN2j0pSDyj3cTfzq8pC7Q==}
    hasBin: true

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}

  get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  getpass@0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==}

  github-from-package@0.0.0:
    resolution: {integrity: sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob@10.4.2:
    resolution: {integrity: sha512-GwMlUF6PkPo3Gk21UxkCohOv0PLcIXVtKyLlpEI28R/cO/4eNOdmLk3CMW1wROV/WR/EsZOWAfBbBOqYvs88/w==}
    engines: {node: '>=16 || 14 >=14.18'}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@8.1.0:
    resolution: {integrity: sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==}
    engines: {node: '>=12'}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globalyzer@0.1.0:
    resolution: {integrity: sha512-40oNTM9UfG6aBmuKxk/giHn5nQ8RVz/SS4Ir6zgzOv9/qC3kKZ9v4etGTcJbEl/NyVQH7FGU7d+X1egr57Md2Q==}

  globalyzer@0.1.4:
    resolution: {integrity: sha512-LeguVWaxgHN0MNbWC6YljNMzHkrCny9fzjmEUdnF1kQ7wATFD1RHFRqA1qxaX2tgxGENlcxjOflopBwj3YZiXA==}

  globrex@0.1.2:
    resolution: {integrity: sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==}

  google-auth-library@9.14.1:
    resolution: {integrity: sha512-Rj+PMjoNFGFTmtItH7gHfbHpGVSb3vmnGK3nwNBqxQF9NoBpttSZI/rc0WiM63ma2uGDQtYEkMHkK9U6937NiA==}
    engines: {node: '>=14'}

  google-gax@4.4.1:
    resolution: {integrity: sha512-Phyp9fMfA00J3sZbJxbbB4jC55b7DBjE3F6poyL3wKMEBVKA79q6BGuHcTiM28yOzVql0NDbRL8MLLh8Iwk9Dg==}
    engines: {node: '>=14'}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  gtoken@7.1.0:
    resolution: {integrity: sha512-pCcEwRi+TKpMlxAQObHDQ56KawURgyAf6jtIY046fJ5tIv3zDe/LEIubckAO8fj6JnAxLdmWkUfNyulQ2iKdEw==}
    engines: {node: '>=14.0.0'}

  har-schema@2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-unicode@2.0.1:
    resolution: {integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==}

  hasha@5.2.2:
    resolution: {integrity: sha512-Hrp5vIK/xr5SkeN2onO32H0MgNZ0f17HRNH39WfL0SYUNOTZ5Lz1TJ8Pajo/87dYGEFlLMm7mIc/k/s6Bvz9HQ==}
    engines: {node: '>=8'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  help-me@4.2.0:
    resolution: {integrity: sha512-TAOnTB8Tz5Dw8penUuzHVrKNKlCIbwwbHnXraNJxPwf8LRtE2HlM84RYuezMFcwOJmoYOCWVDyJ8TQGxn9PgxA==}

  help-me@5.0.0:
    resolution: {integrity: sha512-7xgomUX6ADmcYzFik0HzAxh/73YlKR9bmFzf51CZwR+b6YtzU2m0u49hQCqV6SvlqIqsaxovfwdvbnsw3b/zpg==}

  hexoid@1.0.0:
    resolution: {integrity: sha512-QFLV0taWQOZtvIRIAdBChesmogZrtuXvVWsFHZTk2SU+anspqZ2vMnoLg7IE1+Uk16N19APic1BuF8bC8c2m5g==}
    engines: {node: '>=8'}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  htmlparser2@3.8.3:
    resolution: {integrity: sha512-hBxEg3CYXe+rPIua8ETe7tmG3XDn9B0edOE/e9wH2nLczxzgdu0m0aNHY+5wFZiviLWLdANPJTssa92dMcXQ5Q==}

  http-cache-semantics@4.1.1:
    resolution: {integrity: sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}

  http-proxy-agent@5.0.0:
    resolution: {integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==}
    engines: {node: '>= 6'}

  http-signature@1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  http-signature@1.4.0:
    resolution: {integrity: sha512-G5akfn7eKbpDN+8nPS/cb57YeA1jLTVxjpCj7tmm3QKPdyDy7T+qSC40e9ptydSWvkwjSXw1VbkpyEm39ukeAg==}
    engines: {node: '>=0.10'}

  httpntlm@1.8.13:
    resolution: {integrity: sha512-2F2FDPiWT4rewPzNMg3uPhNkP3NExENlUGADRUDPQvuftuUTGW98nLZtGemCIW3G40VhWZYgkIDcQFAwZ3mf2Q==}
    engines: {node: '>=10.4.0'}

  httpreq@1.1.1:
    resolution: {integrity: sha512-uhSZLPPD2VXXOSN8Cni3kIsoFHaU2pT/nySEU/fHr/ePbqHYr0jeiQRmUKLEirC09SFPsdMoA7LU7UXMd/w0Kw==}
    engines: {node: '>= 6.15.1'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  https-proxy-agent@7.0.5:
    resolution: {integrity: sha512-1e4Wqeblerz+tMKPIq2EMGiiWW1dIjZOksyHWSUm1rmuvw/how9hBHZ38lAGj5ID4Ik6EdkOw7NmWPy6LAwalw==}
    engines: {node: '>= 14'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  idb-keyval@6.2.1:
    resolution: {integrity: sha512-8Sb3veuYCyrZL+VBt9LJfZjLUPWVvqn8tG28VqYNFCo43KHcKuq+b4EiXGeuaLAQWL2YmyDgMp2aSpH9JHsEQg==}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  ignore-by-default@1.0.1:
    resolution: {integrity: sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  import-local@3.2.0:
    resolution: {integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  infer-owner@1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  ip-address@9.0.5:
    resolution: {integrity: sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==}
    engines: {node: '>= 12'}

  ip@2.0.1:
    resolution: {integrity: sha512-lJUL9imLTNi1ZfXT+DU6rBBdbiKGBuay9B6xGSPVjUeQwaH1RIGqef8RZkUtHioLmSNpPR5M4HVKJGm1j8FWVQ==}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-core-module@2.15.1:
    resolution: {integrity: sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==}
    engines: {node: '>= 0.4'}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-electron@2.2.2:
    resolution: {integrity: sha512-FO/Rhvz5tuw4MCWkpMzHFKWD2LsfHzIb7i6MdPYZ/KW7AlxawyLkqdy+jPZP1WubqEADE3O4FUENlJHDfQASRg==}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-fn@2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-lambda@1.0.1:
    resolution: {integrity: sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  is-url@1.2.4:
    resolution: {integrity: sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww==}

  is-windows@1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}

  isarray@0.0.1:
    resolution: {integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isbinaryfile@4.0.10:
    resolution: {integrity: sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==}
    engines: {node: '>= 8.0.0'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-hook@3.0.0:
    resolution: {integrity: sha512-Pt/uge1Q9s+5VAZ+pCo16TYMWPBIl+oaNIjgLQxcX0itS6ueeaA+pEfThZpH8WxhFgCiEb8sAJY6MdUKgiIWaQ==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@4.0.3:
    resolution: {integrity: sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@6.0.3:
    resolution: {integrity: sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==}
    engines: {node: '>=10'}

  istanbul-lib-processinfo@2.0.3:
    resolution: {integrity: sha512-NkwHbo3E00oybX6NGJi6ar0B29vxyvNwoC7eJ4G4Yq28UfY758Hgn/heV8VRFhevPED4LXfFz0DQ8z/0kw9zMg==}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==}
    engines: {node: '>=8'}

  jackspeak@1.4.2:
    resolution: {integrity: sha512-GHeGTmnuaHnvS+ZctRB01bfxARuu9wW83ENbuiweu07SFcVlZrJpcshSre/keGT7YGBhLHg/+rXCNSrsEHKU4Q==}
    engines: {node: '>=8'}

  jackspeak@3.4.0:
    resolution: {integrity: sha512-JVYhQnN59LVPFCEcVa2C3CrEKYacvjRfqIQl+h8oi91aLYQVWRYbxjPcv1bUiUy/kLmQaANrYfNMCO3kuEDHfw==}
    engines: {node: '>=14'}

  jest-changed-files@29.7.0:
    resolution: {integrity: sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-circus@29.7.0:
    resolution: {integrity: sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-cli@29.7.0:
    resolution: {integrity: sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@29.7.0:
    resolution: {integrity: sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true

  jest-diff@29.7.0:
    resolution: {integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-docblock@29.7.0:
    resolution: {integrity: sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-each@29.7.0:
    resolution: {integrity: sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-environment-node@29.7.0:
    resolution: {integrity: sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-get-type@29.6.3:
    resolution: {integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-haste-map@29.7.0:
    resolution: {integrity: sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-leak-detector@29.7.0:
    resolution: {integrity: sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-matcher-utils@29.7.0:
    resolution: {integrity: sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-message-util@29.7.0:
    resolution: {integrity: sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-mock@29.7.0:
    resolution: {integrity: sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-pnp-resolver@1.2.3:
    resolution: {integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@29.6.3:
    resolution: {integrity: sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve-dependencies@29.7.0:
    resolution: {integrity: sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve@29.7.0:
    resolution: {integrity: sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runner@29.7.0:
    resolution: {integrity: sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runtime@29.7.0:
    resolution: {integrity: sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-snapshot@29.7.0:
    resolution: {integrity: sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-util@29.7.0:
    resolution: {integrity: sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-validate@29.7.0:
    resolution: {integrity: sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-watcher@29.7.0:
    resolution: {integrity: sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-worker@29.7.0:
    resolution: {integrity: sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest@29.7.0:
    resolution: {integrity: sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  joycon@3.1.1:
    resolution: {integrity: sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==}
    engines: {node: '>=10'}

  js-md4@0.3.2:
    resolution: {integrity: sha512-/GDnfQYsltsjRswQhN9fhv3EMw2sCpUdrdxyWDOUK7eyD++r3gRhzgiQgc/x4MAv2i1iuQ4lxO5mvqM3vj4bwA==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsbn@0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==}

  jsbn@1.1.0:
    resolution: {integrity: sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==}

  jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  jshint@2.13.6:
    resolution: {integrity: sha512-IVdB4G0NTTeQZrBoM8C5JFVLjV2KtZ9APgybDA1MK73xb09qFs0jCXyQLnCOp1cSZZZbvhq/6mfXHUTaDkffuQ==}
    hasBin: true

  json-bigint@1.0.0:
    resolution: {integrity: sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==}

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-ref-resolver@1.0.1:
    resolution: {integrity: sha512-EJAj1pgHc1hxF6vo2Z3s69fMjO1INq6eGHXZ8Z6wCQeldCuwxGK9Sxf4/cScGn3FZubCVUehfWtcDM/PLteCQw==}

  json-schema-resolver@2.0.0:
    resolution: {integrity: sha512-pJ4XLQP4Q9HTxl6RVDLJ8Cyh1uitSs0CzDBAz1uoJ4sRD/Bk7cFSXL1FUXDW3zJ7YnfliJx6eu8Jn283bpZ4Yg==}
    engines: {node: '>=10'}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-schema@0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-eslint-parser@2.4.0:
    resolution: {integrity: sha512-WYDyuc/uFcGp6YtM2H0uKmUwieOuzeE/5YocFJLnLfclZ4inf3mRn8ZVy1s7Hxji7Jxm6Ss8gqpexD/GlKoGgg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  jsprim@1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}

  jsprim@2.0.2:
    resolution: {integrity: sha512-gqXddjPqQ6G40VdnI6T6yObEC+pDNvyP95wdQhkWkg7crHH3km5qP1FsOXEkzEQwnz6gz5qGTn1c2Y52wP3OyQ==}
    engines: {'0': node >=0.6.0}

  jwa@2.0.0:
    resolution: {integrity: sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==}

  jws@4.0.0:
    resolution: {integrity: sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}

  leven@3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  libtap@1.4.1:
    resolution: {integrity: sha512-S9v19shLTigoMn3c02V7LZ4t09zxmVP3r3RbEAwuHFYeKgF+ESFJxoQ0PMFKW4XdgQhcjVBEwDoopG6WROq/gw==}
    engines: {node: '>=10'}

  libxmljs2@0.33.0:
    resolution: {integrity: sha512-Hw74f2/3rbpxc6tkTqe3yrs4v2Tx0rEukrYxaNkXSVKK540i2eqlQxzf1jjG+RlwMuv66WxkkuZHM/OQq6km4w==}
    engines: {node: '>=18'}

  light-my-request@5.13.0:
    resolution: {integrity: sha512-9IjUN9ZyCS9pTG+KqTDEQo68Sui2lHsYBrfMyVUTTZ3XhH8PMZq7xO94Kr+eP9dhi/kcKsx4N41p2IXEBil1pQ==}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  locate-path@3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}

  lodash.clonedeep@4.5.0:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash.flattendeep@4.4.0:
    resolution: {integrity: sha512-uHaJFihxmJcEX3kT4I23ABqKKalJ/zDrDg0lsFtc1h+3uw49SIJ5beyhx5ExVRti3AvKoOJngIj7xz3oylPdWQ==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.truncate@4.4.2:
    resolution: {integrity: sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  long@5.2.3:
    resolution: {integrity: sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q==}

  lru-cache@10.2.2:
    resolution: {integrity: sha512-9hp3Vp2/hFQUiIwKo8XCeFVnrg8Pk3TYNPIR7tJADKi5YfcF7vEaK7avFHTlSy3kOKYaJQaalfEo6YuXdceBOQ==}
    engines: {node: 14 || >=16.14}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  luxon@3.4.4:
    resolution: {integrity: sha512-zobTr7akeGHnv7eBOXcRgMeCP6+uyYsczwmeRCauvpvaAltgNyTbLH/+VaEAPUeWBT+1GuNmz4wC/6jtQzbbVA==}
    engines: {node: '>=12'}

  make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  make-fetch-happen@9.1.0:
    resolution: {integrity: sha512-+zopwDy7DNknmwPQplem5lAZX/eCOzSvSNNcSKm5eVwTkOBzoktEfXsa9L23J/GIRhxRsaxzkPEhrJEpE2F4Gg==}
    engines: {node: '>= 10'}

  make-promises-safe@5.1.0:
    resolution: {integrity: sha512-AfdZ49rtyhQR/6cqVKGoH7y4ql7XkS5HJI1lZm0/5N6CQosy1eYbBJ/qbhkKHzo17UH7M918Bysf6XB9f3kS1g==}

  makeerror@1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@3.0.0:
    resolution: {integrity: sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mimic-response@2.1.0:
    resolution: {integrity: sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==}
    engines: {node: '>=8'}

  mimic-response@3.1.0:
    resolution: {integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==}
    engines: {node: '>=10'}

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  minimatch@3.0.8:
    resolution: {integrity: sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}

  minimatch@9.0.4:
    resolution: {integrity: sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass-collect@1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}

  minipass-fetch@1.4.1:
    resolution: {integrity: sha512-CGH1eblLq26Y15+Azk7ey4xh0J/XfJfrCox5LDJiKqI2Q2iwOLOKrlmIaODiSQS8d18jalF6y2K2ePUm0CmShw==}
    engines: {node: '>=8'}

  minipass-flush@1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}

  minipass-pipeline@1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}

  minipass-sized@1.0.3:
    resolution: {integrity: sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==}
    engines: {node: '>=8'}

  minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}

  mkdirp-classic@0.5.3:
    resolution: {integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nan@2.18.0:
    resolution: {integrity: sha512-W7tfG7vMOGtD30sHoZSSc/JVYiyDPEyQVso/Zz+/uQd0B0L46gtC+pHha5FFMRpil6fm/AoEcRWyOVi4+E/f8w==}

  napi-build-utils@1.0.2:
    resolution: {integrity: sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==}

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  node-abi@3.65.0:
    resolution: {integrity: sha512-ThjYBfoDNr08AWx6hGaRbfPwxKV9kVzAzOzlLKbk2CuqXE2xnCh+cbAGnwM3t8Lq4v9rUB7VfondlkBckcJrVA==}
    engines: {node: '>=10'}

  node-addon-api@7.1.0:
    resolution: {integrity: sha512-mNcltoe1R8o7STTegSOHdnJNN7s5EUvhoS7ShnTHDyOSd+8H+UdWODq6qSv67PjC8Zc5JRT8+oLAMCr0SIXw7g==}
    engines: {node: ^16 || ^18 || >= 20}

  node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-gyp@8.4.1:
    resolution: {integrity: sha512-olTJRgUtAb/hOXG0E93wZDs5YiJlgbXxTwQAFHyNlRsXQnYzUaF2aGgujZbw+hR8aF4ZG/rST57bWMWD16jr9w==}
    engines: {node: '>= 10.12.0'}
    hasBin: true

  node-int64@0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==}

  node-preload@0.2.1:
    resolution: {integrity: sha512-RM5oyBy45cLEoHqCeh+MNuFAxO0vTFBLskvQbOKnEE7YTTSN4tbN8QWDIPQ6L+WvKsB/qLEGpYe2ZZ9d4W9OIQ==}
    engines: {node: '>=8'}

  node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==}

  node-releases@2.0.18:
    resolution: {integrity: sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==}

  node-soap@1.0.0:
    resolution: {integrity: sha512-HNAQ6aQc4gdIRp7nh5g2fdSvghzmfsIrOK9SUp8xajwPcuYDZkXZRyZlVdu1PnKrJYA2zRbEUiaQNONj/1OghQ==}

  node-stream-zip@1.15.0:
    resolution: {integrity: sha512-LN4fydt9TqhZhThkZIVQnF9cwjU3qmUH9h78Mx/K7d3VvfRqqwthLwJEUOEL0QPZ0XQmNN7be5Ggit5+4dq3Bw==}
    engines: {node: '>=0.12.0'}

  nodemon@3.1.4:
    resolution: {integrity: sha512-wjPBbFhtpJwmIeY2yP7QF+UKzPfltVGtfce1g/bB15/8vCGZj8uxD62b/b9M9/WVgme0NZudpownKN+c0plXlQ==}
    engines: {node: '>=10'}
    hasBin: true

  nopt@5.0.0:
    resolution: {integrity: sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==}
    engines: {node: '>=6'}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  npmlog@5.0.1:
    resolution: {integrity: sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==}
    deprecated: This package is no longer supported.

  npmlog@6.0.2:
    resolution: {integrity: sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  nunjucks@3.2.4:
    resolution: {integrity: sha512-26XRV6BhkgK0VOxfbU5cQI+ICFUtMLixv1noZn1tGU38kQH5A5nmmbk/O45xdyBhD1esk47nKrY0mvQpZIhRjQ==}
    engines: {node: '>= 6.9.0'}
    hasBin: true
    peerDependencies:
      chokidar: ^3.3.0
    peerDependenciesMeta:
      chokidar:
        optional: true

  nyc@15.1.0:
    resolution: {integrity: sha512-jMW04n9SxKdKi1ZMGhvUTHBN0EICCRkHemEoE5jm6mTYcqcdas0ATzgUgejlQUHMvpnOZqGB5Xxsv9KxJW1j8A==}
    engines: {node: '>=8.9'}
    hasBin: true

  oauth-sign@0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  object-inspect@1.13.1:
    resolution: {integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.5:
    resolution: {integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==}
    engines: {node: '>= 0.4'}

  on-exit-leak-free@2.1.2:
    resolution: {integrity: sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==}
    engines: {node: '>=14.0.0'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  openai@4.52.0:
    resolution: {integrity: sha512-xmiNcdA9QJ5wffHpZDpIsge6AsPTETJ6h5iqDNuFQ7qGSNtonHn8Qe0VHy4UwLE8rBWiSqh4j+iSvuYZSeKkPg==}
    hasBin: true

  openapi-types@12.1.3:
    resolution: {integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==}

  openapi-typescript@5.4.2:
    resolution: {integrity: sha512-tHeRv39Yh7brqJpbUntdjtUaXrTHmC4saoyTLU/0J2I8LEFQYDXRLgnmWTMiMOB2GXugJiqHa5n9sAyd6BRqiA==}
    engines: {node: '>= 14.0.0'}
    hasBin: true

  opencollective-postinstall@2.0.3:
    resolution: {integrity: sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==}
    hasBin: true

  opener@1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==}
    hasBin: true

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  own-or-env@1.0.2:
    resolution: {integrity: sha512-NQ7v0fliWtK7Lkb+WdFqe6ky9XAzYmlkXthQrBbzlYbmFKoAYbDDcwmOm6q8kOuwSRXW8bdL5ORksploUJmWgw==}

  own-or@1.0.0:
    resolution: {integrity: sha512-NfZr5+Tdf6MB8UI9GLvKRs4cXY8/yB0w3xtt84xFdWy8hkGjn+JFc60VhzS/hFRfbyxFcGYMTjnF4Me+RbbqrA==}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  p-map@3.0.0:
    resolution: {integrity: sha512-d3qXVTF/s+W+CdJ5A29wywV2n8CQQYahlgz2bFiA+4eVNJbHJodPZ+/gXwPGh0bOqA+j8S+6+ckmvLGPk1QpxQ==}
    engines: {node: '>=8'}

  p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  package-hash@4.0.0:
    resolution: {integrity: sha512-whdkPIooSu/bASggZ96BWVvZTRMOFxnyUG5PnTSGKoJE2gd5mbVNmR2Nj20QFzxYYgAXpoqC+AiXzl+UMRh7zQ==}
    engines: {node: '>=8'}

  package-json-from-dist@1.0.0:
    resolution: {integrity: sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==}

  packet-reader@1.0.0:
    resolution: {integrity: sha512-HAKu/fG3HpHFO0AA8WE8q2g+gBJaZ9MG7fcKk+IJPLTGAD6Psw4443l+9DGRbOIh3/aXr7Phy0TjilYivJo5XQ==}

  papaparse@5.4.1:
    resolution: {integrity: sha512-HipMsgJkZu8br23pW15uvo6sib6wne/4woLZPlFf3rpDyMe9ywEXUsuD7+6K9PRkJlVT51j/sCOYDKGGS3ZJrw==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  path-exists@3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  pdf-img-convert@1.2.1:
    resolution: {integrity: sha512-eqJ1umG8lcazibkfZVkNfS7LXiuzgEtJjHM+Kqd2IxoggPWpLfrcV1yb7TWUMNIFr5IDlsM/oYdCwLW/vg4VBQ==}

  pdfjs-dist@2.16.105:
    resolution: {integrity: sha512-J4dn41spsAwUxCpEoVf6GVoz908IAA3mYiLmNxg8J9kfRXc2jxpbUepcP0ocp0alVNLFthTAM8DZ1RaHh8sU0A==}
    peerDependencies:
      worker-loader: ^3.0.8
    peerDependenciesMeta:
      worker-loader:
        optional: true

  performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}

  pg-boss@9.0.3:
    resolution: {integrity: sha512-cUWUiv3sr563yNy0nCZ25Tv5U0m59Y9MhX/flm0vTR012yeVCrqpfboaZP4xFOQPdWipMJpuu4g94HR0SncTgw==}
    engines: {node: '>=16'}

  pg-cloudflare@1.1.1:
    resolution: {integrity: sha512-xWPagP/4B6BgFO+EKz3JONXv3YDgvkbVrGw2mTo3D6tVDQRh1e7cqVGvyR3BE+eQgAvx1XhW/iEASj4/jCWl3Q==}

  pg-connection-string@2.6.4:
    resolution: {integrity: sha512-v+Z7W/0EO707aNMaAEfiGnGL9sxxumwLl2fJvCQtMn9Fxsg+lPpPkdcyBSv/KFgpGdYkMfn+EI1Or2EHjpgLCA==}

  pg-int8@1.0.1:
    resolution: {integrity: sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==}
    engines: {node: '>=4.0.0'}

  pg-pool@3.6.2:
    resolution: {integrity: sha512-Htjbg8BlwXqSBQ9V8Vjtc+vzf/6fVUuak/3/XXKA9oxZprwW3IMDQTGHP+KDmVL7rtd+R1QjbnCFPuTHm3G4hg==}
    peerDependencies:
      pg: '>=8.0'

  pg-protocol@1.6.1:
    resolution: {integrity: sha512-jPIlvgoD63hrEuihvIg+tJhoGjUsLPn6poJY9N5CnlPd91c2T18T/9zBtLxZSb1EhYxBRoZJtzScCaWlYLtktg==}

  pg-types@2.2.0:
    resolution: {integrity: sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==}
    engines: {node: '>=4'}

  pg@8.12.0:
    resolution: {integrity: sha512-A+LHUSnwnxrnL/tZ+OLfqR1SxLN3c/pgDztZ47Rpbsd4jUytsTtwQo/TLPRzPJMp/1pbhYVhH9cuSZLAajNfjQ==}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      pg-native: '>=3.0.1'
    peerDependenciesMeta:
      pg-native:
        optional: true

  pgpass@1.0.5:
    resolution: {integrity: sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug==}

  picocolors@1.0.1:
    resolution: {integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pino-abstract-transport@1.2.0:
    resolution: {integrity: sha512-Guhh8EZfPCfH+PMXAb6rKOjGQEoy0xlAIn+irODG5kgfYV+BQ0rGYYWTIel3P5mmyXqkYkPmdIkywsn6QKUR1Q==}

  pino-pretty@10.3.1:
    resolution: {integrity: sha512-az8JbIYeN/1iLj2t0jR9DV48/LQ3RC6hZPpapKPkb84Q+yTidMCpgWxIT3N0flnBDilyBQ1luWNpOeJptjdp/g==}
    hasBin: true

  pino-std-serializers@6.2.2:
    resolution: {integrity: sha512-cHjPPsE+vhj/tnhCy/wiMh3M3z3h/j15zHQX+S9GkTBgqJuTuJzYJ4gUyACLhDaJ7kk9ba9iRDmbH2tJU03OiA==}

  pino-std-serializers@7.0.0:
    resolution: {integrity: sha512-e906FRY0+tV27iq4juKzSYPbUj2do2X2JX4EzSca1631EB2QJQUqGbDuERal7LCtOpxl6x3+nvo9NPZcmjkiFA==}

  pino@8.21.0:
    resolution: {integrity: sha512-ip4qdzjkAyDDZklUaZkcRFb2iA118H9SgRh8yzTkSQK8HilsOJF7rSY8HoW5+I0M46AZgX/pxbprf2vvzQCE0Q==}
    hasBin: true

  pino@9.2.0:
    resolution: {integrity: sha512-g3/hpwfujK5a4oVbaefoJxezLzsDgLcNJeITvC6yrfwYeT9la+edCK42j5QpEQSQCZgTKapXvnQIdgZwvRaZug==}
    hasBin: true

  pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}

  pkg-up@3.1.0:
    resolution: {integrity: sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==}
    engines: {node: '>=8'}

  postgres-array@2.0.0:
    resolution: {integrity: sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==}
    engines: {node: '>=4'}

  postgres-bytea@1.0.0:
    resolution: {integrity: sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==}
    engines: {node: '>=0.10.0'}

  postgres-date@1.0.7:
    resolution: {integrity: sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==}
    engines: {node: '>=0.10.0'}

  postgres-interval@1.2.0:
    resolution: {integrity: sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==}
    engines: {node: '>=0.10.0'}

  prebuild-install@7.1.2:
    resolution: {integrity: sha512-UnNke3IQb6sgarcZIDU3gbMeTp/9SSU1DAIkil7PrqG1vZlBtY5msYccSKSHDqa3hNg436IXK+SNImReuA1wEQ==}
    engines: {node: '>=10'}
    hasBin: true

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    engines: {node: '>=6.0.0'}

  prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  prettier@3.2.5:
    resolution: {integrity: sha512-3/GWa9aOC0YeD7LUfvOG2NiDyhOWRvt1k+rcKhOuYnMY24iiCphgneUfJDyFXd6rZCAnuLBv6UeAULtrhT/F4A==}
    engines: {node: '>=14'}
    hasBin: true

  pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  process-on-spawn@1.0.0:
    resolution: {integrity: sha512-1WsPDsUSMmZH5LeMLegqkPDrsGgsWwk1Exipy2hvB0o/F0ASzbpIctSCcZIK1ykJvtTJULEH+20WOFjMvGnCTg==}
    engines: {node: '>=8'}

  process-warning@3.0.0:
    resolution: {integrity: sha512-mqn0kFRl0EoqhnL0GQ0veqFHyIN1yig9RHh/InzORTUiZHFRAur+aMtRkELNwGs9aNwKS6tg/An4NYBPGwvtzQ==}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  promise-inflight@1.0.1:
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true

  promise-retry@2.0.1:
    resolution: {integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==}
    engines: {node: '>=10'}

  prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}

  proto3-json-serializer@2.0.2:
    resolution: {integrity: sha512-SAzp/O4Yh02jGdRc+uIrGoe87dkN/XtwxfZ4ZyafJHymd79ozp5VG5nyZ7ygqPM5+cpLDjjGnYFUkngonyDPOQ==}
    engines: {node: '>=14.0.0'}

  protobufjs@7.4.0:
    resolution: {integrity: sha512-mRUWCc3KUU4w1jU8sGxICXH/gNS94DvI1gxqDvBzhj1JpcsimQkYiOJfwsPUykUI5ZaspFbSgmBLER8IrQ3tqw==}
    engines: {node: '>=12.0.0'}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  pstree.remy@1.1.8:
    resolution: {integrity: sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==}

  pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  pure-rand@6.1.0:
    resolution: {integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==}

  q@1.5.1:
    resolution: {integrity: sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw==}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    deprecated: |-
      You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.

      (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)

  qs@6.12.1:
    resolution: {integrity: sha512-zWmv4RSuB9r2mYQw3zxQuHWeU+42aKi1wWig/j4ele4ygELZ7PEO6MM7rim9oAQH2A5MWfsAVf/jPvTPgCbvUQ==}
    engines: {node: '>=0.6'}

  qs@6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quick-format-unescaped@4.0.4:
    resolution: {integrity: sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  rc@1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  readable-stream@1.1.14:
    resolution: {integrity: sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readable-stream@4.5.2:
    resolution: {integrity: sha512-yjavECdqeZ3GLXNgRXgeQEdz9fvDDkNKyHnbHRFtOr7/LcfgBcmct7t/ET+HaCTqfh06OzoAxrkN/IfjJBVe+g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  real-require@0.2.0:
    resolution: {integrity: sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==}
    engines: {node: '>= 12.13.0'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-runtime@0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==}

  regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}

  regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true

  release-zalgo@1.0.0:
    resolution: {integrity: sha512-gUAyHVHPPC5wdqX/LG4LWtRYtgjxyX78oanFNTMMyFEfOqdC54s3eE82imuWKbOeqYht2CrNf64Qb8vgmmtZGA==}
    engines: {node: '>=4'}

  repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}

  request@2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  require-main-filename@2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve.exports@2.0.2:
    resolution: {integrity: sha512-X2UW6Nw3n/aMgDVy+0rSqgHlv39WZAlZrXCdnbyEiKm17DSqHX4MmQMaST3FbeWR5FTuRcUwYAziZajji0Y7mg==}
    engines: {node: '>=10'}

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  ret@0.2.2:
    resolution: {integrity: sha512-M0b3YWQs7R3Z917WRQy1HHA7Ba7D8hvZg6UE5mLykJxQVE2ju0IXbGlaHPPlkY+WN7wFP+wUMXmBFA0aV6vYGQ==}
    engines: {node: '>=4'}

  ret@0.4.3:
    resolution: {integrity: sha512-0f4Memo5QP7WQyUEAYUO3esD/XjOc3Zjjg5CPsAq1p8sIu0XPeMbHJemKA0BO7tV0X7+A0FoEpbmHXWxPyD3wQ==}
    engines: {node: '>=10'}

  retry-request@7.0.2:
    resolution: {integrity: sha512-dUOvLMJ0/JJYEn8NrpOaGNE7X3vpI5XlZS/u0ANjqtcZVKnIxP7IgCFwrKTxENw29emmwug53awKtaMm4i9g5w==}
    engines: {node: '>=14'}

  retry@0.12.0:
    resolution: {integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==}
    engines: {node: '>= 4'}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}

  rimraf@2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-regex2@2.0.0:
    resolution: {integrity: sha512-PaUSFsUaNNuKwkBijoAPHAK6/eM6VirvyPWlZ7BAQy4D+hCvh4B6lIG+nPdhbFfIbP+gTGBcrdsOaUs0F+ZBOQ==}

  safe-regex2@3.1.0:
    resolution: {integrity: sha512-RAAZAGbap2kBfbVhvmnTFv73NWLMvDGOITFYTZBAaY8eR+Ir4ef7Up/e7amo+y1+AH+3PtLkrt9mvcTsG9LXug==}

  safe-stable-stringify@2.4.3:
    resolution: {integrity: sha512-e2bDA2WJT0wxseVd4lsDP4+3ONX6HpMXQa1ZhFQ7SU+GjvORCmShbCMltrtIDfkYhVHrOcPtj+KhmDBdPdZD1g==}
    engines: {node: '>=10'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  secure-json-parse@2.7.0:
    resolution: {integrity: sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.6.2:
    resolution: {integrity: sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==}
    engines: {node: '>=10'}
    hasBin: true

  serialize-error@8.1.0:
    resolution: {integrity: sha512-3NnuWfM6vBYoy5gZFvHiYsVbafvI9vZv/+jlIigFn4oP4zjNPK3LhcY0xSCgeb1a5L8jO71Mit9LlNoi2UfDDQ==}
    engines: {node: '>=10'}

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  set-cookie-parser@2.6.0:
    resolution: {integrity: sha512-RVnVQxTXuerk653XfuliOxBP81Sf0+qfQE73LIYKcyMYHG94AuH0kgrQpRDuTZnSmjpysHmzxJXKNfa6PjFhyQ==}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-concat@1.0.1:
    resolution: {integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==}

  simple-get@3.1.1:
    resolution: {integrity: sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==}

  simple-get@4.0.1:
    resolution: {integrity: sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==}

  simple-update-notifier@2.0.0:
    resolution: {integrity: sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==}
    engines: {node: '>=10'}

  sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}

  smart-buffer@4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  soap@0.45.0:
    resolution: {integrity: sha512-t2wgZaWQCL8htPxpHzwW1Sw/Qy22Ls6my1X9ajMMXcmNYLlO4ogk10T0A+J8p4/6xHUQthfe2J3Ys1e9/sqS5w==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      axios: '>=0.21.1'

  socks-proxy-agent@6.2.1:
    resolution: {integrity: sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==}
    engines: {node: '>= 10'}

  socks@2.8.3:
    resolution: {integrity: sha512-l5x7VUUWbjVFbafGLxPWkYsHIhEvmF85tbIeFZWc8ZPtoMyybuEhL7Jye/ooC4/d48FgOjSJXgsF/AJPYCW8Zw==}
    engines: {node: '>= 10.0.0', npm: '>= 3.0.0'}

  sonic-boom@3.8.1:
    resolution: {integrity: sha512-y4Z8LCDBuum+PBP3lSV7RHrXscqksve/bi0as7mhwVnBW+/wUqKT/2Kb7um8yqcFy0duYbbPxzt89Zy2nOCaxg==}

  sonic-boom@4.0.1:
    resolution: {integrity: sha512-hTSD/6JMLyT4r9zeof6UtuBDpjJ9sO08/nmS5djaA9eozT9oOlNdpXSnzcgj4FTqpk3nkLrs61l4gip9r1HCrQ==}

  source-map-support@0.5.13:
    resolution: {integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  spawn-wrap@2.0.0:
    resolution: {integrity: sha512-EeajNjfN9zMnULLwhZZQU3GWBoFNkbngTUPfaawT4RkMiviTxcX0qfhVbGey39mfctfDHkWtuecgQ8NJcyQWHg==}
    engines: {node: '>=8'}

  split2@3.2.2:
    resolution: {integrity: sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==}

  split2@4.2.0:
    resolution: {integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==}
    engines: {node: '>= 10.x'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  sprintf-js@1.1.3:
    resolution: {integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==}

  sqlite3@5.1.7:
    resolution: {integrity: sha512-GGIyOiFaG+TUra3JIfkI/zGP8yZYLPQ0pl1bH+ODjiX57sPhrLU5sQJn1y9bDKZUFYkX1crlrPfSYt0BKKdkog==}

  sshpk@1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  ssri@8.0.1:
    resolution: {integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==}
    engines: {node: '>= 8'}

  stack-utils@2.0.6:
    resolution: {integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==}
    engines: {node: '>=10'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  stream-events@1.0.5:
    resolution: {integrity: sha512-E1GUzBSgvct8Jsb3v2X15pjzN1tYebtbLaMg+eBOUOAxgbLoSbT2NS91ckc5lJD1KfLjId+jXJRgo0qnV5Nerg==}

  stream-shift@1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}

  string-length@4.0.2:
    resolution: {integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==}
    engines: {node: '>=10'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string_decoder@0.10.31:
    resolution: {integrity: sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-bom@4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-json-comments@1.0.4:
    resolution: {integrity: sha512-AOPG8EBc5wAikaG1/7uFCNFJwnKOuQwFTpYBdTW6OvWHeZBQBrAA/amefHGrEiOnCPcLFZK6FUPtWVKpQVIRgg==}
    engines: {node: '>=0.8.0'}
    hasBin: true

  strip-json-comments@2.0.1:
    resolution: {integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==}
    engines: {node: '>=0.10.0'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}

  stubs@3.0.0:
    resolution: {integrity: sha512-PdHt7hHUJKxvTCgbKX9C1V/ftOcjJQgz8BZwNfV5c4B6dcGqlpelTbJ999jBGZ2jYiPAwcX5dP6oBwVlBlUbxw==}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  synckit@0.6.2:
    resolution: {integrity: sha512-Vhf+bUa//YSTYKseDiiEuQmhGCoIF3CVBhunm3r/DQnYiGT4JssmnKQc44BIyOZRK2pKjXXAgbhfmbeoC9CJpA==}
    engines: {node: '>=12.20'}

  synckit@0.9.1:
    resolution: {integrity: sha512-7gr8p9TQP6RAHusBOSLs46F4564ZrjV8xFmw5zCmgmhGUcw2hxsShhJ6CEiHQMgPDwAQ1fWHPM0ypc4RMAig4A==}
    engines: {node: ^14.18.0 || >=16.0.0}

  table@6.8.2:
    resolution: {integrity: sha512-w2sfv80nrAh2VCbqR5AK27wswXhqcck2AhfnNW76beQXskGZ1V12GwS//yYVa3d3fcvAip2OUnbDAjW2k3v9fA==}
    engines: {node: '>=10.0.0'}

  tap-mocha-reporter@5.0.4:
    resolution: {integrity: sha512-J+YMO8B7lq1O6Zxd/jeuG27vJ+Y4tLiRMKPSb7KR6FVh86k3Rq1TwYc2GKPyIjCbzzdMdReh3Vfz9L5cg1Z2Bw==}
    engines: {node: '>= 8'}
    hasBin: true

  tap-parser@11.0.2:
    resolution: {integrity: sha512-6qGlC956rcORw+fg7Fv1iCRAY8/bU9UabUAhs3mXRH6eRmVZcNPLheSXCYaVaYeSwx5xa/1HXZb1537YSvwDZg==}
    engines: {node: '>= 8'}
    hasBin: true

  tap-yaml@1.0.2:
    resolution: {integrity: sha512-GegASpuqBnRNdT1U+yuUPZ8rEU64pL35WPBpCISWwff4dErS2/438barz7WFJl4Nzh3Y05tfPidZnH+GaV1wMg==}

  tap@16.3.10:
    resolution: {integrity: sha512-q5Am+PpGHS6JSjk/Zn4bCRBihmZVM15v/MYXUy60wenw5HDe7pVrevLCEoMEz7tuw6jaPOJJqni1y8apN23IGw==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      coveralls: ^3.1.1
      flow-remove-types: '>=2.112.0'
      ts-node: '>=8.5.2'
      typescript: '>=3.7.2'
    peerDependenciesMeta:
      coveralls:
        optional: true
      flow-remove-types:
        optional: true
      ts-node:
        optional: true
      typescript:
        optional: true
    bundledDependencies:
      - ink
      - treport
      - '@types/react'
      - '@isaacs/import-jsx'
      - react

  tar-fs@2.1.1:
    resolution: {integrity: sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==}

  tar-stream@2.2.0:
    resolution: {integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==}
    engines: {node: '>=6'}

  tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}

  tcompare@5.0.7:
    resolution: {integrity: sha512-d9iddt6YYGgyxJw5bjsN7UJUO1kGOtjSlNy/4PoGYAjQS5pAT/hzIoLf1bZCw+uUxRmZJh7Yy1aA7xKVRT9B4w==}
    engines: {node: '>=10'}

  teeny-request@9.0.0:
    resolution: {integrity: sha512-resvxdc6Mgb7YEThw6G6bExlXKkv6+YbuzGg9xuXxSgxJF7Ozs+o8Y9+2R3sArdWdW8nOokoQb1yrpFB0pQK2g==}
    engines: {node: '>=14'}

  tesseract.js-core@5.1.1:
    resolution: {integrity: sha512-KX3bYSU5iGcO1XJa+QGPbi+Zjo2qq6eBhNjSGR5E5q0JtzkoipJKOUQD7ph8kFyteCEfEQ0maWLu8MCXtvX5uQ==}

  tesseract.js@5.1.1:
    resolution: {integrity: sha512-lzVl/Ar3P3zhpUT31NjqeCo1f+D5+YfpZ5J62eo2S14QNVOmHBTtbchHm/YAbOOOzCegFnKf4B3Qih9LuldcYQ==}

  test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  thread-stream@2.7.0:
    resolution: {integrity: sha512-qQiRWsU/wvNolI6tbbCKd9iKaTnCXsTwVxhhKM6nctPdujTyztjlbUkUTUymidWcMnZ5pWR0ej4a0tjsW021vw==}

  thread-stream@3.1.0:
    resolution: {integrity: sha512-OqyPZ9u96VohAyMfJykzmivOrY2wfMSf3C5TtFJVgN+Hm6aj+voFhlK+kZEIv2FBh1X6Xp3DlnCOfEQ3B2J86A==}

  tiny-glob@0.2.9:
    resolution: {integrity: sha512-g/55ssRPUjShh+xkfx9UPDXqhckHEsHr4Vd9zX55oSdGZc/MD0m3sferOkwWtp98bv+kcVfEHtRJgBVJzelrzg==}

  tmpl@1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toad-cache@3.7.0:
    resolution: {integrity: sha512-/m8M+2BJUpoJdgAHoG+baCwBT+tf2VraSfkBgl0Y00qIWt41DJ8R5B8nsEw0I58YwF5IZH6z24/2TobDKnqSWw==}
    engines: {node: '>=12'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  touch@3.1.1:
    resolution: {integrity: sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==}
    hasBin: true

  tough-cookie@2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  trivial-deferred@1.1.2:
    resolution: {integrity: sha512-vDPiDBC3hyP6O4JrJYMImW3nl3c03Tsj9fEXc7Qc/XKa1O7gf5ZtFfIR/E0dun9SnDHdwjna1Z2rSzYgqpxh/g==}
    engines: {node: '>= 8'}

  tslib@2.6.3:
    resolution: {integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==}

  tunnel-agent@0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}

  tweetnacl@0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typedarray-to-buffer@3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}

  undefsafe@2.0.5:
    resolution: {integrity: sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==}

  underscore@1.12.1:
    resolution: {integrity: sha512-hEQt0+ZLDVUMhebKxL4x1BTtDY7bavVofhZ9KZ4aI26X9SRaE+Y3m83XUL1UP2jn8ynjndwCCpEHdUG+9pP1Tw==}

  underscore@1.13.6:
    resolution: {integrity: sha512-+A5Sja4HP1M08MaXya7p5LvjuM7K6q/2EaC0+iovj/wOcMsTzMvDFbasi/oSapiwOlt252IqsKqPjCl7huKS0A==}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  undici-types@5.28.4:
    resolution: {integrity: sha512-3OeMF5Lyowe8VW0skf5qaIE7Or3yS9LS7fvMUI0gg4YxpIBVg0L8BxCmROw2CcYhSkpR68Epz7CGc8MPj94Uww==}

  undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}

  undici@5.28.4:
    resolution: {integrity: sha512-72RFADWFqKmUb2hmmvNODKL3p9hcB6Gt2DOQMis1SEBaV6a4MH8soBvzg+95CYhCKPFedut2JY9bMfrDl9D23g==}
    engines: {node: '>=14.0'}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==}
    engines: {node: '>=4'}

  unicode-length@2.1.0:
    resolution: {integrity: sha512-4bV582zTV9Q02RXBxSUMiuN/KHo5w4aTojuKTNT96DIKps/SIawFp7cS5Mu25VuY1AioGXrmYyzKZUzh8OqoUw==}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  unique-filename@1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}

  unique-slug@2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  unzipper@0.11.6:
    resolution: {integrity: sha512-anERl79akvqLbAxfjIFe4hK0wsi0fH4uGLwNEl4QEnG+KKs3QQeApYgOS/f6vH2EdACUlZg35psmd/3xL2duFQ==}

  update-browserslist-db@1.0.16:
    resolution: {integrity: sha512-KVbTxlBYlckhF5wgfyZXTWnMn7MMZjMu9XG8bPlliUOP9ThaF4QnhP8qrjrH7DRzHfSk0oQv1wToW+iA5GajEQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  update-browserslist-db@1.1.0:
    resolution: {integrity: sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  util@0.10.4:
    resolution: {integrity: sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==}

  uuid@3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  v8-to-istanbul@9.3.0:
    resolution: {integrity: sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==}
    engines: {node: '>=10.12.0'}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  verror@1.10.0:
    resolution: {integrity: sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==}
    engines: {'0': node >=0.6.0}

  verror@1.10.1:
    resolution: {integrity: sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==}
    engines: {node: '>=0.6.0'}

  walker@1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==}

  wasm-feature-detect@1.8.0:
    resolution: {integrity: sha512-zksaLKM2fVlnB5jQQDqKXXwYHLQUVH9es+5TOOHwGOVJOCeRBCiPjwSg+3tN2AdTCzjgli4jijCH290kXb/zWQ==}

  web-streams-polyfill@3.3.3:
    resolution: {integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==}
    engines: {node: '>= 8'}

  web-streams-polyfill@4.0.0-beta.3:
    resolution: {integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==}
    engines: {node: '>= 14'}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  whatwg-mimetype@3.0.0:
    resolution: {integrity: sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==}
    engines: {node: '>=12'}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which-module@2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wide-align@1.1.5:
    resolution: {integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==}

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  write-file-atomic@3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}

  write-file-atomic@4.0.2:
    resolution: {integrity: sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  xml-beautifier@0.5.0:
    resolution: {integrity: sha512-QG/qiHeolHUd1tAtM+5zHxTzDprb8qvhmIYUYV1E9QK/jTFlrAa1Mz7QQqJPeqc3uuFAGzTOhjvbdx2hOP6bHw==}

  xml-crypto@2.1.5:
    resolution: {integrity: sha512-xOSJmGFm+BTXmaPYk8pPV3duKo6hJuZ5niN4uMzoNcTlwYs0jAu/N3qY+ud9MhE4N7eMRuC1ayC7Yhmb7MmAWg==}
    engines: {node: '>=0.4.0'}

  xml-formatter@3.6.2:
    resolution: {integrity: sha512-enWhevZNOwffZFUhzl1WMcha8lFLZUgJ7NzFs5Ug4ZOFCoNheGYXz1J9Iz/e+cTn9rCkuT1GwTacz+YlmFHOGw==}
    engines: {node: '>= 14'}

  xml-parser-xo@4.1.1:
    resolution: {integrity: sha512-Ggf2y90+Y6e9IK5hoPuembVHJ03PhDSdhldEmgzbihzu9k0XBo0sfcFxaSi4W1PlUSSI1ok+MJ0JCXUn+U4Ilw==}
    engines: {node: '>= 14'}

  xml2js@0.6.2:
    resolution: {integrity: sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xpath@0.0.32:
    resolution: {integrity: sha512-rxMJhSIoiO8vXcWvSifKqhvV96GjiD5wYb8/QHdoRyQvraTpp4IEv944nhGausZZ3u7dhQXteZuZbaqfpB7uYw==}
    engines: {node: '>=0.6.0'}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yaml-eslint-parser@1.2.3:
    resolution: {integrity: sha512-4wZWvE398hCP7O8n3nXKu/vdq1HcH01ixYlCREaJL5NUMwQ0g3MaGFUBNSlmBtKmhbtVG/Cm6lyYmSVTEVil8A==}
    engines: {node: ^14.17.0 || >=16.0.0}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yaml@2.4.5:
    resolution: {integrity: sha512-aBx2bnqDzVOyNKfsysjA2ms5ZlnjSAW2eG3/L5G/CSujfjLJTJsEw1bGw8kCf04KodQWk1pxlGnZ56CRxiawmg==}
    engines: {node: '>= 14'}
    hasBin: true

  yargs-parser@18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  zlibjs@0.3.1:
    resolution: {integrity: sha512-+J9RrgTKOmlxFSDHo0pI1xM6BLVUv+o0ZT9ANtCxGkjIVCCUdx9alUF8Gm+dGLKbkkkidWIHFDZHDMpfITt4+w==}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@apidevtools/json-schema-ref-parser@11.7.0':
    dependencies:
      '@jsdevtools/ono': 7.1.3
      '@types/json-schema': 7.0.15
      js-yaml: 4.1.0

  '@aws-crypto/crc32@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.598.0
      tslib: 2.6.3

  '@aws-crypto/crc32c@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.598.0
      tslib: 2.6.3

  '@aws-crypto/sha1-browser@5.2.0':
    dependencies:
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.6.3

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.6.3

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.598.0
      tslib: 2.6.3

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.6.3

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.6.3

  '@aws-sdk/client-s3@3.600.0':
    dependencies:
      '@aws-crypto/sha1-browser': 5.2.0
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.600.0(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/client-sts': 3.600.0
      '@aws-sdk/core': 3.598.0
      '@aws-sdk/credential-provider-node': 3.600.0(@aws-sdk/client-sso-oidc@3.600.0)(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/middleware-bucket-endpoint': 3.598.0
      '@aws-sdk/middleware-expect-continue': 3.598.0
      '@aws-sdk/middleware-flexible-checksums': 3.598.0
      '@aws-sdk/middleware-host-header': 3.598.0
      '@aws-sdk/middleware-location-constraint': 3.598.0
      '@aws-sdk/middleware-logger': 3.598.0
      '@aws-sdk/middleware-recursion-detection': 3.598.0
      '@aws-sdk/middleware-sdk-s3': 3.598.0
      '@aws-sdk/middleware-signing': 3.598.0
      '@aws-sdk/middleware-ssec': 3.598.0
      '@aws-sdk/middleware-user-agent': 3.598.0
      '@aws-sdk/region-config-resolver': 3.598.0
      '@aws-sdk/signature-v4-multi-region': 3.598.0
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-endpoints': 3.598.0
      '@aws-sdk/util-user-agent-browser': 3.598.0
      '@aws-sdk/util-user-agent-node': 3.598.0
      '@aws-sdk/xml-builder': 3.598.0
      '@smithy/config-resolver': 3.0.3
      '@smithy/core': 2.2.3
      '@smithy/eventstream-serde-browser': 3.0.3
      '@smithy/eventstream-serde-config-resolver': 3.0.2
      '@smithy/eventstream-serde-node': 3.0.3
      '@smithy/fetch-http-handler': 3.1.0
      '@smithy/hash-blob-browser': 3.1.1
      '@smithy/hash-node': 3.0.2
      '@smithy/hash-stream-node': 3.1.1
      '@smithy/invalid-dependency': 3.0.2
      '@smithy/md5-js': 3.0.2
      '@smithy/middleware-content-length': 3.0.2
      '@smithy/middleware-endpoint': 3.0.3
      '@smithy/middleware-retry': 3.0.6
      '@smithy/middleware-serde': 3.0.2
      '@smithy/middleware-stack': 3.0.2
      '@smithy/node-config-provider': 3.1.2
      '@smithy/node-http-handler': 3.1.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/url-parser': 3.0.2
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.6
      '@smithy/util-defaults-mode-node': 3.0.6
      '@smithy/util-endpoints': 2.0.3
      '@smithy/util-retry': 3.0.2
      '@smithy/util-stream': 3.0.4
      '@smithy/util-utf8': 3.0.0
      '@smithy/util-waiter': 3.1.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso-oidc@3.600.0(@aws-sdk/client-sts@3.600.0)':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sts': 3.600.0
      '@aws-sdk/core': 3.598.0
      '@aws-sdk/credential-provider-node': 3.600.0(@aws-sdk/client-sso-oidc@3.600.0)(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/middleware-host-header': 3.598.0
      '@aws-sdk/middleware-logger': 3.598.0
      '@aws-sdk/middleware-recursion-detection': 3.598.0
      '@aws-sdk/middleware-user-agent': 3.598.0
      '@aws-sdk/region-config-resolver': 3.598.0
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-endpoints': 3.598.0
      '@aws-sdk/util-user-agent-browser': 3.598.0
      '@aws-sdk/util-user-agent-node': 3.598.0
      '@smithy/config-resolver': 3.0.3
      '@smithy/core': 2.2.3
      '@smithy/fetch-http-handler': 3.1.0
      '@smithy/hash-node': 3.0.2
      '@smithy/invalid-dependency': 3.0.2
      '@smithy/middleware-content-length': 3.0.2
      '@smithy/middleware-endpoint': 3.0.3
      '@smithy/middleware-retry': 3.0.6
      '@smithy/middleware-serde': 3.0.2
      '@smithy/middleware-stack': 3.0.2
      '@smithy/node-config-provider': 3.1.2
      '@smithy/node-http-handler': 3.1.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/url-parser': 3.0.2
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.6
      '@smithy/util-defaults-mode-node': 3.0.6
      '@smithy/util-endpoints': 2.0.3
      '@smithy/util-middleware': 3.0.2
      '@smithy/util-retry': 3.0.2
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sts'
      - aws-crt

  '@aws-sdk/client-sso@3.598.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.598.0
      '@aws-sdk/middleware-host-header': 3.598.0
      '@aws-sdk/middleware-logger': 3.598.0
      '@aws-sdk/middleware-recursion-detection': 3.598.0
      '@aws-sdk/middleware-user-agent': 3.598.0
      '@aws-sdk/region-config-resolver': 3.598.0
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-endpoints': 3.598.0
      '@aws-sdk/util-user-agent-browser': 3.598.0
      '@aws-sdk/util-user-agent-node': 3.598.0
      '@smithy/config-resolver': 3.0.3
      '@smithy/core': 2.2.3
      '@smithy/fetch-http-handler': 3.1.0
      '@smithy/hash-node': 3.0.2
      '@smithy/invalid-dependency': 3.0.2
      '@smithy/middleware-content-length': 3.0.2
      '@smithy/middleware-endpoint': 3.0.3
      '@smithy/middleware-retry': 3.0.6
      '@smithy/middleware-serde': 3.0.2
      '@smithy/middleware-stack': 3.0.2
      '@smithy/node-config-provider': 3.1.2
      '@smithy/node-http-handler': 3.1.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/url-parser': 3.0.2
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.6
      '@smithy/util-defaults-mode-node': 3.0.6
      '@smithy/util-endpoints': 2.0.3
      '@smithy/util-middleware': 3.0.2
      '@smithy/util-retry': 3.0.2
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sts@3.600.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.600.0(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/core': 3.598.0
      '@aws-sdk/credential-provider-node': 3.600.0(@aws-sdk/client-sso-oidc@3.600.0)(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/middleware-host-header': 3.598.0
      '@aws-sdk/middleware-logger': 3.598.0
      '@aws-sdk/middleware-recursion-detection': 3.598.0
      '@aws-sdk/middleware-user-agent': 3.598.0
      '@aws-sdk/region-config-resolver': 3.598.0
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-endpoints': 3.598.0
      '@aws-sdk/util-user-agent-browser': 3.598.0
      '@aws-sdk/util-user-agent-node': 3.598.0
      '@smithy/config-resolver': 3.0.3
      '@smithy/core': 2.2.3
      '@smithy/fetch-http-handler': 3.1.0
      '@smithy/hash-node': 3.0.2
      '@smithy/invalid-dependency': 3.0.2
      '@smithy/middleware-content-length': 3.0.2
      '@smithy/middleware-endpoint': 3.0.3
      '@smithy/middleware-retry': 3.0.6
      '@smithy/middleware-serde': 3.0.2
      '@smithy/middleware-stack': 3.0.2
      '@smithy/node-config-provider': 3.1.2
      '@smithy/node-http-handler': 3.1.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/url-parser': 3.0.2
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.6
      '@smithy/util-defaults-mode-node': 3.0.6
      '@smithy/util-endpoints': 2.0.3
      '@smithy/util-middleware': 3.0.2
      '@smithy/util-retry': 3.0.2
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.598.0':
    dependencies:
      '@smithy/core': 2.2.3
      '@smithy/protocol-http': 4.0.2
      '@smithy/signature-v4': 3.1.1
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      fast-xml-parser: 4.2.5
      tslib: 2.6.3

  '@aws-sdk/credential-provider-env@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/property-provider': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/credential-provider-http@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/fetch-http-handler': 3.1.0
      '@smithy/node-http-handler': 3.1.0
      '@smithy/property-provider': 3.1.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/util-stream': 3.0.4
      tslib: 2.6.3

  '@aws-sdk/credential-provider-ini@3.598.0(@aws-sdk/client-sso-oidc@3.600.0)(@aws-sdk/client-sts@3.600.0)':
    dependencies:
      '@aws-sdk/client-sts': 3.600.0
      '@aws-sdk/credential-provider-env': 3.598.0
      '@aws-sdk/credential-provider-http': 3.598.0
      '@aws-sdk/credential-provider-process': 3.598.0
      '@aws-sdk/credential-provider-sso': 3.598.0(@aws-sdk/client-sso-oidc@3.600.0)
      '@aws-sdk/credential-provider-web-identity': 3.598.0(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/types': 3.598.0
      '@smithy/credential-provider-imds': 3.1.2
      '@smithy/property-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-node@3.600.0(@aws-sdk/client-sso-oidc@3.600.0)(@aws-sdk/client-sts@3.600.0)':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.598.0
      '@aws-sdk/credential-provider-http': 3.598.0
      '@aws-sdk/credential-provider-ini': 3.598.0(@aws-sdk/client-sso-oidc@3.600.0)(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/credential-provider-process': 3.598.0
      '@aws-sdk/credential-provider-sso': 3.598.0(@aws-sdk/client-sso-oidc@3.600.0)
      '@aws-sdk/credential-provider-web-identity': 3.598.0(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/types': 3.598.0
      '@smithy/credential-provider-imds': 3.1.2
      '@smithy/property-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - '@aws-sdk/client-sts'
      - aws-crt

  '@aws-sdk/credential-provider-process@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/property-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/credential-provider-sso@3.598.0(@aws-sdk/client-sso-oidc@3.600.0)':
    dependencies:
      '@aws-sdk/client-sso': 3.598.0
      '@aws-sdk/token-providers': 3.598.0(@aws-sdk/client-sso-oidc@3.600.0)
      '@aws-sdk/types': 3.598.0
      '@smithy/property-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.598.0(@aws-sdk/client-sts@3.600.0)':
    dependencies:
      '@aws-sdk/client-sts': 3.600.0
      '@aws-sdk/types': 3.598.0
      '@smithy/property-provider': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-bucket-endpoint@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-arn-parser': 3.568.0
      '@smithy/node-config-provider': 3.1.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      '@smithy/util-config-provider': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-expect-continue@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-flexible-checksums@3.598.0':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@aws-crypto/crc32c': 5.2.0
      '@aws-sdk/types': 3.598.0
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-host-header@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-location-constraint@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-logger@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-recursion-detection@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-sdk-s3@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-arn-parser': 3.568.0
      '@smithy/node-config-provider': 3.1.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/signature-v4': 3.1.1
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/util-config-provider': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-signing@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/property-provider': 3.1.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/signature-v4': 3.1.1
      '@smithy/types': 3.2.0
      '@smithy/util-middleware': 3.0.2
      tslib: 2.6.3

  '@aws-sdk/middleware-ssec@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/middleware-user-agent@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@aws-sdk/util-endpoints': 3.598.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/region-config-resolver@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/node-config-provider': 3.1.2
      '@smithy/types': 3.2.0
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.2
      tslib: 2.6.3

  '@aws-sdk/signature-v4-multi-region@3.598.0':
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.598.0
      '@aws-sdk/types': 3.598.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/signature-v4': 3.1.1
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/token-providers@3.598.0(@aws-sdk/client-sso-oidc@3.600.0)':
    dependencies:
      '@aws-sdk/client-sso-oidc': 3.600.0(@aws-sdk/client-sts@3.600.0)
      '@aws-sdk/types': 3.598.0
      '@smithy/property-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/types@3.598.0':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/util-arn-parser@3.568.0':
    dependencies:
      tslib: 2.6.3

  '@aws-sdk/util-endpoints@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/types': 3.2.0
      '@smithy/util-endpoints': 2.0.3
      tslib: 2.6.3

  '@aws-sdk/util-locate-window@3.568.0':
    dependencies:
      tslib: 2.6.3

  '@aws-sdk/util-user-agent-browser@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/types': 3.2.0
      bowser: 2.11.0
      tslib: 2.6.3

  '@aws-sdk/util-user-agent-node@3.598.0':
    dependencies:
      '@aws-sdk/types': 3.598.0
      '@smithy/node-config-provider': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@aws-sdk/xml-builder@3.598.0':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@babel/code-frame@7.24.7':
    dependencies:
      '@babel/highlight': 7.24.7
      picocolors: 1.0.1

  '@babel/compat-data@7.25.4': {}

  '@babel/core@7.25.2(supports-color@7.2.0)':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helpers': 7.25.6
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      '@babel/types': 7.25.6
      convert-source-map: 2.0.0
      debug: 4.3.5(supports-color@7.2.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/eslint-parser@7.25.1(@babel/core@7.25.2(supports-color@7.2.0))(eslint@9.10.0(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@nicolo-ribaudo/eslint-scope-5-internals': 5.1.1-v1
      eslint: 9.10.0(supports-color@7.2.0)
      eslint-visitor-keys: 2.1.0
      semver: 6.3.1

  '@babel/generator@7.24.7':
    dependencies:
      '@babel/types': 7.24.7
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/generator@7.25.6':
    dependencies:
      '@babel/types': 7.25.6
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.24.7':
    dependencies:
      '@babel/types': 7.24.7

  '@babel/helper-builder-binary-assignment-operator-visitor@7.24.7(supports-color@7.2.0)':
    dependencies:
      '@babel/traverse': 7.24.7(supports-color@7.2.0)
      '@babel/types': 7.24.7
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-compilation-targets@7.25.2':
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/helper-validator-option': 7.24.8
      browserslist: 4.23.1
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-member-expression-to-functions': 7.24.8(supports-color@7.2.0)
      '@babel/helper-optimise-call-expression': 7.24.7
      '@babel/helper-replace-supers': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7(supports-color@7.2.0)
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.25.2(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-annotate-as-pure': 7.24.7
      regexpu-core: 5.3.2
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      debug: 4.3.5(supports-color@7.2.0)
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.24.7':
    dependencies:
      '@babel/types': 7.24.7

  '@babel/helper-function-name@7.24.7':
    dependencies:
      '@babel/template': 7.24.7
      '@babel/types': 7.24.7

  '@babel/helper-hoist-variables@7.24.7':
    dependencies:
      '@babel/types': 7.24.7

  '@babel/helper-member-expression-to-functions@7.24.8(supports-color@7.2.0)':
    dependencies:
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.24.7(supports-color@7.2.0)':
    dependencies:
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-environment-visitor': 7.24.7
      '@babel/helper-module-imports': 7.24.7(supports-color@7.2.0)
      '@babel/helper-simple-access': 7.24.7(supports-color@7.2.0)
      '@babel/helper-split-export-declaration': 7.24.7
      '@babel/helper-validator-identifier': 7.24.7
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.25.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-module-imports': 7.24.7(supports-color@7.2.0)
      '@babel/helper-simple-access': 7.24.7(supports-color@7.2.0)
      '@babel/helper-validator-identifier': 7.24.7
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.24.7':
    dependencies:
      '@babel/types': 7.24.7

  '@babel/helper-plugin-utils@7.24.8': {}

  '@babel/helper-remap-async-to-generator@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-wrap-function': 7.25.0(supports-color@7.2.0)
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-member-expression-to-functions': 7.24.8(supports-color@7.2.0)
      '@babel/helper-optimise-call-expression': 7.24.7
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-simple-access@7.24.7(supports-color@7.2.0)':
    dependencies:
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.24.7(supports-color@7.2.0)':
    dependencies:
      '@babel/traverse': 7.24.7(supports-color@7.2.0)
      '@babel/types': 7.24.7
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-split-export-declaration@7.24.7':
    dependencies:
      '@babel/types': 7.24.7

  '@babel/helper-string-parser@7.24.7': {}

  '@babel/helper-string-parser@7.24.8': {}

  '@babel/helper-validator-identifier@7.24.7': {}

  '@babel/helper-validator-option@7.24.8': {}

  '@babel/helper-wrap-function@7.25.0(supports-color@7.2.0)':
    dependencies:
      '@babel/template': 7.25.0
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.25.6':
    dependencies:
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6

  '@babel/highlight@7.24.7':
    dependencies:
      '@babel/helper-validator-identifier': 7.24.7
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.1

  '@babel/parser@7.24.7':
    dependencies:
      '@babel/types': 7.24.7

  '@babel/parser@7.25.6':
    dependencies:
      '@babel/types': 7.25.6

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.3(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7(supports-color@7.2.0)
      '@babel/plugin-transform-optional-chaining': 7.24.8(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-import-assertions@7.25.6(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-import-attributes@7.25.6(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-jsx@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-typescript@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-arrow-functions@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-async-generator-functions@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-remap-async-to-generator': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-async-to-generator@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-module-imports': 7.24.7(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-remap-async-to-generator': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-block-scoping@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-class-properties@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-class-static-block@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-classes@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-replace-supers': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/template': 7.24.7

  '@babel/plugin-transform-destructuring@7.24.8(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-dotall-regex@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-duplicate-keys@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-dynamic-import@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-exponentiation-operator@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.24.7(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-export-namespace-from@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-for-of@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.25.1(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-json-strings@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-literals@7.25.2(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-logical-assignment-operators@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-member-expression-literals@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-modules-amd@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-module-transforms': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.24.8(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-simple-access': 7.24.7(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      '@babel/traverse': 7.25.6(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-module-transforms': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-new-target@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-nullish-coalescing-operator@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-numeric-separator@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-object-rest-spread@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-parameters': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-object-super@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-replace-supers': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-optional-catch-binding@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))

  '@babel/plugin-transform-optional-chaining@7.24.8(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7(supports-color@7.2.0)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-private-methods@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-private-property-in-object@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-property-literals@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-regenerator@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-reserved-words@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-shorthand-properties@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-spread@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-template-literals@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-typeof-symbol@7.24.8(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-unicode-escapes@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-unicode-property-regex@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-unicode-regex@7.24.7(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/plugin-transform-unicode-sets-regex@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/helper-plugin-utils': 7.24.8

  '@babel/preset-env@7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)':
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-validator-option': 7.24.8
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.25.3(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-import-assertions': 7.25.6(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-import-attributes': 7.25.6(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-arrow-functions': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-async-generator-functions': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-async-to-generator': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-block-scoped-functions': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-block-scoping': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-class-properties': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-class-static-block': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-classes': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-computed-properties': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-destructuring': 7.24.8(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-dotall-regex': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-duplicate-keys': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-dynamic-import': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-exponentiation-operator': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-export-namespace-from': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-for-of': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-function-name': 7.25.1(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-json-strings': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-literals': 7.25.2(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-logical-assignment-operators': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-member-expression-literals': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-modules-amd': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-modules-commonjs': 7.24.8(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-modules-systemjs': 7.25.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-modules-umd': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-new-target': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-nullish-coalescing-operator': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-numeric-separator': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-object-rest-spread': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-object-super': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-optional-catch-binding': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-optional-chaining': 7.24.8(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-parameters': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-private-methods': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-private-property-in-object': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-property-literals': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-regenerator': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-reserved-words': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-shorthand-properties': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-spread': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      '@babel/plugin-transform-sticky-regex': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-template-literals': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-typeof-symbol': 7.24.8(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-unicode-escapes': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-unicode-property-regex': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-unicode-regex': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-transform-unicode-sets-regex': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.25.2(supports-color@7.2.0))
      babel-plugin-polyfill-corejs2: 0.4.11(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      babel-plugin-polyfill-corejs3: 0.10.6(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      babel-plugin-polyfill-regenerator: 0.6.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      core-js-compat: 3.38.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.25.2(supports-color@7.2.0))':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/types': 7.24.7
      esutils: 2.0.3

  '@babel/regjsgen@0.8.0': {}

  '@babel/runtime@7.25.6':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.24.7':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/parser': 7.24.7
      '@babel/types': 7.24.7

  '@babel/template@7.25.0':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6

  '@babel/traverse@7.24.7(supports-color@7.2.0)':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.24.7
      '@babel/helper-environment-visitor': 7.24.7
      '@babel/helper-function-name': 7.24.7
      '@babel/helper-hoist-variables': 7.24.7
      '@babel/helper-split-export-declaration': 7.24.7
      '@babel/parser': 7.24.7
      '@babel/types': 7.24.7
      debug: 4.3.5(supports-color@7.2.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/traverse@7.25.6(supports-color@7.2.0)':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6
      debug: 4.3.5(supports-color@7.2.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.24.7':
    dependencies:
      '@babel/helper-string-parser': 7.24.7
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0

  '@babel/types@7.25.6':
    dependencies:
      '@babel/helper-string-parser': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0

  '@bcoe/v8-coverage@0.2.3': {}

  '@eslint-community/eslint-utils@4.4.0(eslint@9.10.0(supports-color@7.2.0))':
    dependencies:
      eslint: 9.10.0(supports-color@7.2.0)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.11.1': {}

  '@eslint/config-array@0.18.0(supports-color@7.2.0)':
    dependencies:
      '@eslint/object-schema': 2.1.4
      debug: 4.3.5(supports-color@7.2.0)
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/eslintrc@3.1.0(supports-color@7.2.0)':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.5(supports-color@7.2.0)
      espree: 10.1.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.10.0': {}

  '@eslint/object-schema@2.1.4': {}

  '@eslint/plugin-kit@0.1.0':
    dependencies:
      levn: 0.4.1

  '@fastify/accept-negotiator@1.1.0': {}

  '@fastify/ajv-compiler@3.5.0':
    dependencies:
      ajv: 8.16.0
      ajv-formats: 2.1.1(ajv@8.16.0)
      fast-uri: 2.4.0

  '@fastify/autoload@5.10.0': {}

  '@fastify/bearer-auth@9.4.0':
    dependencies:
      '@fastify/error': 3.4.1
      fastify-plugin: 4.5.1

  '@fastify/busboy@2.1.1': {}

  '@fastify/deepmerge@1.3.0': {}

  '@fastify/env@4.3.0':
    dependencies:
      env-schema: 5.2.1
      fastify-plugin: 4.5.1

  '@fastify/error@3.4.1': {}

  '@fastify/fast-json-stringify-compiler@4.3.0':
    dependencies:
      fast-json-stringify: 5.16.1

  '@fastify/merge-json-schemas@0.1.1':
    dependencies:
      fast-deep-equal: 3.1.3

  '@fastify/postgres@5.2.2(pg@8.12.0)':
    dependencies:
      fastify-plugin: 4.5.1
      pg: 8.12.0

  '@fastify/send@2.1.0':
    dependencies:
      '@lukeed/ms': 2.0.2
      escape-html: 1.0.3
      fast-decode-uri-component: 1.0.1
      http-errors: 2.0.0
      mime: 3.0.0

  '@fastify/sensible@5.6.0':
    dependencies:
      '@lukeed/ms': 2.0.2
      fast-deep-equal: 3.1.3
      fastify-plugin: 4.5.1
      forwarded: 0.2.0
      http-errors: 2.0.0
      type-is: 1.6.18
      vary: 1.1.2

  '@fastify/soap-client@2.2.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0)':
    dependencies:
      fastify-plugin: 4.5.1
      soap: 0.45.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0)
    transitivePeerDependencies:
      - axios
      - supports-color

  '@fastify/static@7.0.4':
    dependencies:
      '@fastify/accept-negotiator': 1.1.0
      '@fastify/send': 2.1.0
      content-disposition: 0.5.4
      fastify-plugin: 4.5.1
      fastq: 1.17.1
      glob: 10.4.2

  '@fastify/swagger-ui@3.1.0':
    dependencies:
      '@fastify/static': 7.0.4
      fastify-plugin: 4.5.1
      openapi-types: 12.1.3
      rfdc: 1.4.1
      yaml: 2.4.5

  '@fastify/swagger@8.14.0(supports-color@7.2.0)':
    dependencies:
      fastify-plugin: 4.5.1
      json-schema-resolver: 2.0.0(supports-color@7.2.0)
      openapi-types: 12.1.3
      rfdc: 1.4.1
      yaml: 2.4.5
    transitivePeerDependencies:
      - supports-color

  '@gar/promisify@1.1.3': {}

  '@google-cloud/documentai@8.10.0(encoding@0.1.13)(supports-color@7.2.0)':
    dependencies:
      google-gax: 4.4.1(encoding@0.1.13)(supports-color@7.2.0)
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@grpc/grpc-js@1.12.0':
    dependencies:
      '@grpc/proto-loader': 0.7.13
      '@js-sdsl/ordered-map': 4.4.2

  '@grpc/proto-loader@0.7.13':
    dependencies:
      lodash.camelcase: 4.3.0
      long: 5.2.3
      protobufjs: 7.4.0
      yargs: 17.7.2

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.0': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/console@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      chalk: 4.1.2
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0

  '@jest/core@29.7.0(supports-color@7.2.0)':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/reporters': 29.7.0(supports-color@7.2.0)
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 29.7.0
      jest-config: 29.7.0(@types/node@18.19.38)(supports-color@7.2.0)
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-resolve-dependencies: 29.7.0(supports-color@7.2.0)
      jest-runner: 29.7.0(supports-color@7.2.0)
      jest-runtime: 29.7.0(supports-color@7.2.0)
      jest-snapshot: 29.7.0(supports-color@7.2.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      jest-watcher: 29.7.0
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node

  '@jest/environment@29.7.0':
    dependencies:
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      jest-mock: 29.7.0

  '@jest/expect-utils@29.7.0':
    dependencies:
      jest-get-type: 29.6.3

  '@jest/expect@29.7.0(supports-color@7.2.0)':
    dependencies:
      expect: 29.7.0
      jest-snapshot: 29.7.0(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  '@jest/fake-timers@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@sinonjs/fake-timers': 10.3.0
      '@types/node': 18.19.38
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  '@jest/globals@29.7.0(supports-color@7.2.0)':
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      jest-mock: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/reporters@29.7.0(supports-color@7.2.0)':
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      '@types/node': 18.19.38
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 6.0.3(supports-color@7.2.0)
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1(supports-color@7.2.0)
      istanbul-reports: 3.1.7
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      jest-worker: 29.7.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      v8-to-istanbul: 9.3.0
    transitivePeerDependencies:
      - supports-color

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/source-map@29.6.3':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      callsites: 3.1.0
      graceful-fs: 4.2.11

  '@jest/test-result@29.7.0':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/types': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-sequencer@29.7.0':
    dependencies:
      '@jest/test-result': 29.7.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      slash: 3.0.0

  '@jest/transform@29.7.0(supports-color@7.2.0)':
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      babel-plugin-istanbul: 6.1.1(supports-color@7.2.0)
      chalk: 4.1.2
      convert-source-map: 2.0.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      micromatch: 4.0.8
      pirates: 4.0.6
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color

  '@jest/types@29.6.3':
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 18.19.38
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15

  '@js-sdsl/ordered-map@4.4.2': {}

  '@jsdevtools/ono@7.1.3': {}

  '@lukeed/ms@2.0.2': {}

  '@mapbox/node-pre-gyp@1.0.11(encoding@0.1.13)(supports-color@7.2.0)':
    dependencies:
      detect-libc: 2.0.3
      https-proxy-agent: 5.0.1(supports-color@7.2.0)
      make-dir: 3.1.0
      node-fetch: 2.7.0(encoding@0.1.13)
      nopt: 5.0.0
      npmlog: 5.0.1
      rimraf: 3.0.2
      semver: 7.6.2
      tar: 6.2.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    dependencies:
      eslint-scope: 5.1.1

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1

  '@npmcli/fs@1.1.1':
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.6.2

  '@npmcli/move-file@1.1.2':
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2

  '@openapi-contrib/openapi-schema-to-json-schema@5.1.0':
    dependencies:
      '@types/json-schema': 7.0.15
      '@types/lodash': 4.17.7
      '@types/node': 20.16.5
      fast-deep-equal: 3.1.3
      lodash: 4.17.21
      openapi-typescript: 5.4.2
      yargs: 17.7.2

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@pkgr/core@0.1.1': {}

  '@protobufjs/aspromise@1.1.2': {}

  '@protobufjs/base64@1.1.2': {}

  '@protobufjs/codegen@2.0.4': {}

  '@protobufjs/eventemitter@1.1.0': {}

  '@protobufjs/fetch@1.1.0':
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0

  '@protobufjs/float@1.0.2': {}

  '@protobufjs/inquire@1.1.0': {}

  '@protobufjs/path@1.1.2': {}

  '@protobufjs/pool@1.1.0': {}

  '@protobufjs/utf8@1.1.0': {}

  '@rgrove/parse-xml@4.1.0': {}

  '@sinclair/typebox@0.27.8': {}

  '@sinonjs/commons@3.0.1':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@10.3.0':
    dependencies:
      '@sinonjs/commons': 3.0.1

  '@smithy/abort-controller@3.1.0':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/chunked-blob-reader-native@3.0.0':
    dependencies:
      '@smithy/util-base64': 3.0.0
      tslib: 2.6.3

  '@smithy/chunked-blob-reader@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/config-resolver@3.0.3':
    dependencies:
      '@smithy/node-config-provider': 3.1.2
      '@smithy/types': 3.2.0
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.2
      tslib: 2.6.3

  '@smithy/core@2.2.3':
    dependencies:
      '@smithy/middleware-endpoint': 3.0.3
      '@smithy/middleware-retry': 3.0.6
      '@smithy/middleware-serde': 3.0.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/util-middleware': 3.0.2
      tslib: 2.6.3

  '@smithy/credential-provider-imds@3.1.2':
    dependencies:
      '@smithy/node-config-provider': 3.1.2
      '@smithy/property-provider': 3.1.2
      '@smithy/types': 3.2.0
      '@smithy/url-parser': 3.0.2
      tslib: 2.6.3

  '@smithy/eventstream-codec@3.1.1':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@smithy/types': 3.2.0
      '@smithy/util-hex-encoding': 3.0.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-browser@3.0.3':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.3
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-config-resolver@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-node@3.0.3':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.3
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-universal@3.0.3':
    dependencies:
      '@smithy/eventstream-codec': 3.1.1
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/fetch-http-handler@3.1.0':
    dependencies:
      '@smithy/protocol-http': 4.0.2
      '@smithy/querystring-builder': 3.0.2
      '@smithy/types': 3.2.0
      '@smithy/util-base64': 3.0.0
      tslib: 2.6.3

  '@smithy/hash-blob-browser@3.1.1':
    dependencies:
      '@smithy/chunked-blob-reader': 3.0.0
      '@smithy/chunked-blob-reader-native': 3.0.0
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/hash-node@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/hash-stream-node@3.1.1':
    dependencies:
      '@smithy/types': 3.2.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/invalid-dependency@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/is-array-buffer@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/md5-js@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/middleware-content-length@3.0.2':
    dependencies:
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/middleware-endpoint@3.0.3':
    dependencies:
      '@smithy/middleware-serde': 3.0.2
      '@smithy/node-config-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      '@smithy/url-parser': 3.0.2
      '@smithy/util-middleware': 3.0.2
      tslib: 2.6.3

  '@smithy/middleware-retry@3.0.6':
    dependencies:
      '@smithy/node-config-provider': 3.1.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/service-error-classification': 3.0.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      '@smithy/util-middleware': 3.0.2
      '@smithy/util-retry': 3.0.2
      tslib: 2.6.3
      uuid: 9.0.1

  '@smithy/middleware-serde@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/middleware-stack@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/node-config-provider@3.1.2':
    dependencies:
      '@smithy/property-provider': 3.1.2
      '@smithy/shared-ini-file-loader': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/node-http-handler@3.1.0':
    dependencies:
      '@smithy/abort-controller': 3.1.0
      '@smithy/protocol-http': 4.0.2
      '@smithy/querystring-builder': 3.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/property-provider@3.1.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/protocol-http@4.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/querystring-builder@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      '@smithy/util-uri-escape': 3.0.0
      tslib: 2.6.3

  '@smithy/querystring-parser@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/service-error-classification@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0

  '@smithy/shared-ini-file-loader@3.1.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/signature-v4@3.1.1':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/types': 3.2.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-middleware': 3.0.2
      '@smithy/util-uri-escape': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/smithy-client@3.1.4':
    dependencies:
      '@smithy/middleware-endpoint': 3.0.3
      '@smithy/middleware-stack': 3.0.2
      '@smithy/protocol-http': 4.0.2
      '@smithy/types': 3.2.0
      '@smithy/util-stream': 3.0.4
      tslib: 2.6.3

  '@smithy/types@3.2.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/url-parser@3.0.2':
    dependencies:
      '@smithy/querystring-parser': 3.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/util-base64@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/util-body-length-browser@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-body-length-node@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.6.3

  '@smithy/util-buffer-from@3.0.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      tslib: 2.6.3

  '@smithy/util-config-provider@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-defaults-mode-browser@3.0.6':
    dependencies:
      '@smithy/property-provider': 3.1.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      bowser: 2.11.0
      tslib: 2.6.3

  '@smithy/util-defaults-mode-node@3.0.6':
    dependencies:
      '@smithy/config-resolver': 3.0.3
      '@smithy/credential-provider-imds': 3.1.2
      '@smithy/node-config-provider': 3.1.2
      '@smithy/property-provider': 3.1.2
      '@smithy/smithy-client': 3.1.4
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/util-endpoints@2.0.3':
    dependencies:
      '@smithy/node-config-provider': 3.1.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/util-hex-encoding@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-middleware@3.0.2':
    dependencies:
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/util-retry@3.0.2':
    dependencies:
      '@smithy/service-error-classification': 3.0.2
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@smithy/util-stream@3.0.4':
    dependencies:
      '@smithy/fetch-http-handler': 3.1.0
      '@smithy/node-http-handler': 3.1.0
      '@smithy/types': 3.2.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/util-uri-escape@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.6.3

  '@smithy/util-utf8@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      tslib: 2.6.3

  '@smithy/util-waiter@3.1.1':
    dependencies:
      '@smithy/abort-controller': 3.1.0
      '@smithy/types': 3.2.0
      tslib: 2.6.3

  '@tootallnate/once@1.1.2': {}

  '@tootallnate/once@2.0.0': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.24.7
      '@babel/types': 7.24.7
      '@types/babel__generator': 7.6.8
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.6

  '@types/babel__generator@7.6.8':
    dependencies:
      '@babel/types': 7.24.7

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.24.7
      '@babel/types': 7.24.7

  '@types/babel__traverse@7.20.6':
    dependencies:
      '@babel/types': 7.24.7

  '@types/caseless@0.12.5': {}

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 0.7.34

  '@types/graceful-fs@4.1.9':
    dependencies:
      '@types/node': 18.19.38

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/json-schema@7.0.15': {}

  '@types/lodash@4.17.7': {}

  '@types/long@4.0.2': {}

  '@types/ms@0.7.34': {}

  '@types/node-fetch@2.6.11':
    dependencies:
      '@types/node': 18.19.38
      form-data: 4.0.0

  '@types/node@18.19.38':
    dependencies:
      undici-types: 5.26.5

  '@types/node@20.16.5':
    dependencies:
      undici-types: 6.19.8

  '@types/request@2.48.12':
    dependencies:
      '@types/caseless': 0.12.5
      '@types/node': 20.16.5
      '@types/tough-cookie': 4.0.5
      form-data: 2.5.1

  '@types/stack-utils@2.0.3': {}

  '@types/tough-cookie@4.0.5': {}

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@xmldom/xmldom@0.7.13': {}

  a-sync-waterfall@1.0.1: {}

  abbrev@1.1.1: {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  abstract-logging@2.0.1: {}

  acorn-jsx@5.3.2(acorn@8.12.1):
    dependencies:
      acorn: 8.12.1

  acorn@8.12.1: {}

  acquerello@2.0.8: {}

  agent-base@6.0.2(supports-color@7.2.0):
    dependencies:
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.1(supports-color@7.2.0):
    dependencies:
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  agentkeepalive@4.5.0:
    dependencies:
      humanize-ms: 1.2.1

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ajv-formats@2.1.1(ajv@8.16.0):
    optionalDependencies:
      ajv: 8.16.0

  ajv-formats@3.0.1(ajv@8.16.0):
    optionalDependencies:
      ajv: 8.16.0

  ajv-keywords@5.1.0(ajv@8.16.0):
    dependencies:
      ajv: 8.16.0
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.16.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  append-transform@2.0.0:
    dependencies:
      default-require-extensions: 3.0.1

  aproba@2.0.0: {}

  archy@1.0.0: {}

  are-we-there-yet@2.0.0:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2

  are-we-there-yet@3.0.1:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  asap@2.0.6: {}

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  assert@1.5.1:
    dependencies:
      object.assign: 4.1.5
      util: 0.10.4

  astral-regex@2.0.0: {}

  async-hook-domain@2.0.4: {}

  asynckit@0.4.0: {}

  atomic-sleep@1.0.0: {}

  avvio@8.3.2:
    dependencies:
      '@fastify/error': 3.4.1
      fastq: 1.17.1

  aws-sign2@0.7.0: {}

  aws4@1.13.0: {}

  axios-debug-log@1.0.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0):
    dependencies:
      '@types/debug': 4.1.12
      axios: 1.7.2(debug@4.3.5(supports-color@7.2.0))
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  axios-ntlm@1.4.2(debug@4.3.5(supports-color@7.2.0)):
    dependencies:
      axios: 1.7.2(debug@4.3.5(supports-color@7.2.0))
      des.js: 1.1.0
      dev-null: 0.1.1
      js-md4: 0.3.2
    transitivePeerDependencies:
      - debug

  axios@1.7.2(debug@4.3.5(supports-color@7.2.0)):
    dependencies:
      follow-redirects: 1.15.6(debug@4.3.5(supports-color@7.2.0))
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-jest@29.7.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@jest/transform': 29.7.0(supports-color@7.2.0)
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1(supports-color@7.2.0)
      babel-preset-jest: 29.6.3(@babel/core@7.25.2(supports-color@7.2.0))
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1(supports-color@7.2.0):
    dependencies:
      '@babel/helper-plugin-utils': 7.24.8
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1(supports-color@7.2.0)
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@29.6.3:
    dependencies:
      '@babel/template': 7.24.7
      '@babel/types': 7.24.7
      '@types/babel__core': 7.20.5
      '@types/babel__traverse': 7.20.6

  babel-plugin-polyfill-corejs2@0.4.11(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0):
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.10.6(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      core-js-compat: 3.38.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  babel-preset-current-node-syntax@1.1.0(@babel/core@7.25.2(supports-color@7.2.0)):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-bigint': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-import-attributes': 7.25.6(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.25.2(supports-color@7.2.0))

  babel-preset-jest@29.6.3(@babel/core@7.25.2(supports-color@7.2.0)):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      babel-plugin-jest-hoist: 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.25.2(supports-color@7.2.0))

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  better-sqlite3@9.6.0:
    dependencies:
      bindings: 1.5.0
      prebuild-install: 7.1.2

  big-integer@1.6.52: {}

  bignumber.js@9.1.2: {}

  binary-extensions@2.3.0: {}

  bind-obj-methods@3.0.0: {}

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  bluebird@3.4.7: {}

  bmp-js@0.1.0: {}

  bowser@2.11.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.23.1:
    dependencies:
      caniuse-lite: 1.0.30001636
      electron-to-chromium: 1.4.808
      node-releases: 2.0.14
      update-browserslist-db: 1.0.16(browserslist@4.23.1)

  browserslist@4.23.3:
    dependencies:
      caniuse-lite: 1.0.30001662
      electron-to-chromium: 1.5.25
      node-releases: 2.0.18
      update-browserslist-db: 1.1.0(browserslist@4.23.3)

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-equal-constant-time@1.0.1: {}

  buffer-from@1.1.2: {}

  buffer-writer@2.0.0: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  bytes@3.1.2: {}

  cacache@15.3.0:
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird

  caching-transform@4.0.0:
    dependencies:
      hasha: 5.2.2
      make-dir: 3.1.0
      package-hash: 4.0.0
      write-file-atomic: 3.0.3

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  callsites@3.1.0: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001636: {}

  caniuse-lite@1.0.30001662: {}

  canvas@2.11.2(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      '@mapbox/node-pre-gyp': 1.0.11(encoding@0.1.13)(supports-color@7.2.0)
      nan: 2.18.0
      simple-get: 3.1.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  caseless@0.12.0: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  char-regex@1.0.2: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chownr@1.1.4: {}

  chownr@2.0.0: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.4.1: {}

  clean-stack@2.2.0: {}

  cli@1.0.1:
    dependencies:
      exit: 0.1.2
      glob: 7.2.3

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  close-with-grace@1.3.0: {}

  co@4.6.0: {}

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-support@1.1.3: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@5.1.0: {}

  commist@3.2.0: {}

  commondir@1.0.1: {}

  concat-map@0.0.1: {}

  console-browserify@1.1.0:
    dependencies:
      date-now: 0.1.4

  console-control-strings@1.1.0: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie@0.5.0: {}

  cookie@0.6.0: {}

  core-js-compat@3.38.1:
    dependencies:
      browserslist: 4.23.3

  core-util-is@1.0.2: {}

  core-util-is@1.0.3: {}

  create-jest@29.7.0(@types/node@18.19.38)(supports-color@7.2.0):
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 29.7.0(@types/node@18.19.38)(supports-color@7.2.0)
      jest-util: 29.7.0
      prompts: 2.4.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  cron-parser@4.9.0:
    dependencies:
      luxon: 3.4.4

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  csv-parser@3.0.0:
    dependencies:
      minimist: 1.2.8

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  date-now@0.1.4: {}

  dateformat@4.6.3: {}

  debug@4.3.5(supports-color@5.5.0):
    dependencies:
      ms: 2.1.2
    optionalDependencies:
      supports-color: 5.5.0

  debug@4.3.5(supports-color@7.2.0):
    dependencies:
      ms: 2.1.2
    optionalDependencies:
      supports-color: 7.2.0

  decamelize@1.2.0: {}

  decompress-response@4.2.1:
    dependencies:
      mimic-response: 2.1.0

  decompress-response@6.0.0:
    dependencies:
      mimic-response: 3.1.0

  dedent@1.5.3: {}

  deep-extend@0.6.0: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  default-require-extensions@3.0.1:
    dependencies:
      strip-bom: 4.0.0

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delay@5.0.0: {}

  delayed-stream@1.0.0: {}

  delegates@1.0.0: {}

  depd@2.0.0: {}

  des.js@1.1.0:
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  desm@1.3.1: {}

  detect-libc@2.0.3: {}

  detect-newline@3.1.0: {}

  dev-null@0.1.1: {}

  dezalgo@1.0.4:
    dependencies:
      asap: 2.0.6
      wrappy: 1.0.2

  diff-sequences@29.6.3: {}

  diff@4.0.2: {}

  dom-serializer@0.2.2:
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0

  domelementtype@1.3.1: {}

  domelementtype@2.3.0: {}

  domhandler@2.3.0:
    dependencies:
      domelementtype: 1.3.1

  dommatrix@1.0.3: {}

  domutils@1.5.1:
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1

  dotenv-expand@10.0.0: {}

  dotenv@16.4.5: {}

  duplexer2@0.1.4:
    dependencies:
      readable-stream: 2.3.8

  duplexify@4.1.3:
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 3.6.2
      stream-shift: 1.0.3

  eastasianwidth@0.2.0: {}

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  electron-to-chromium@1.4.808: {}

  electron-to-chromium@1.5.25: {}

  emittery@0.13.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encoding@0.1.13:
    dependencies:
      iconv-lite: 0.6.3

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  entities@1.0.0: {}

  entities@2.2.0: {}

  env-paths@2.2.1: {}

  env-schema@5.2.1:
    dependencies:
      ajv: 8.16.0
      dotenv: 16.4.5
      dotenv-expand: 10.0.0

  err-code@2.0.3: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es6-error@4.1.1: {}

  escalade@3.1.2: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-compat-utils@0.5.1(eslint@9.10.0(supports-color@7.2.0)):
    dependencies:
      eslint: 9.10.0(supports-color@7.2.0)
      semver: 7.6.2

  eslint-config-prettier@9.1.0(eslint@9.10.0(supports-color@7.2.0)):
    dependencies:
      eslint: 9.10.0(supports-color@7.2.0)

  eslint-plugin-jsonc@2.16.0(eslint@9.10.0(supports-color@7.2.0)):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@9.10.0(supports-color@7.2.0))
      eslint: 9.10.0(supports-color@7.2.0)
      eslint-compat-utils: 0.5.1(eslint@9.10.0(supports-color@7.2.0))
      espree: 9.6.1
      graphemer: 1.4.0
      jsonc-eslint-parser: 2.4.0
      natural-compare: 1.4.0
      synckit: 0.6.2

  eslint-plugin-prettier@5.2.1(eslint-config-prettier@9.1.0(eslint@9.10.0(supports-color@7.2.0)))(eslint@9.10.0(supports-color@7.2.0))(prettier@3.2.5):
    dependencies:
      eslint: 9.10.0(supports-color@7.2.0)
      prettier: 3.2.5
      prettier-linter-helpers: 1.0.0
      synckit: 0.9.1
    optionalDependencies:
      eslint-config-prettier: 9.1.0(eslint@9.10.0(supports-color@7.2.0))

  eslint-plugin-yaml@1.0.3:
    dependencies:
      js-yaml: 4.1.0
      jshint: 2.13.6

  eslint-plugin-yml@1.14.0(eslint@9.10.0(supports-color@7.2.0))(supports-color@7.2.0):
    dependencies:
      debug: 4.3.5(supports-color@7.2.0)
      eslint: 9.10.0(supports-color@7.2.0)
      eslint-compat-utils: 0.5.1(eslint@9.10.0(supports-color@7.2.0))
      lodash: 4.17.21
      natural-compare: 1.4.0
      yaml-eslint-parser: 1.2.3
    transitivePeerDependencies:
      - supports-color

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@8.0.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@2.1.0: {}

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.0.0: {}

  eslint@9.10.0(supports-color@7.2.0):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@9.10.0(supports-color@7.2.0))
      '@eslint-community/regexpp': 4.11.1
      '@eslint/config-array': 0.18.0(supports-color@7.2.0)
      '@eslint/eslintrc': 3.1.0(supports-color@7.2.0)
      '@eslint/js': 9.10.0
      '@eslint/plugin-kit': 0.1.0
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.3.0
      '@nodelib/fs.walk': 1.2.8
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.5(supports-color@7.2.0)
      escape-string-regexp: 4.0.0
      eslint-scope: 8.0.2
      eslint-visitor-keys: 4.0.0
      espree: 10.1.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@10.1.0:
    dependencies:
      acorn: 8.12.1
      acorn-jsx: 5.3.2(acorn@8.12.1)
      eslint-visitor-keys: 4.0.0

  espree@9.6.1:
    dependencies:
      acorn: 8.12.1
      acorn-jsx: 5.3.2(acorn@8.12.1)
      eslint-visitor-keys: 3.4.3

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  event-target-shim@5.0.1: {}

  events-to-array@1.1.2: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exit@0.1.2: {}

  expand-template@2.0.3: {}

  expect@29.7.0:
    dependencies:
      '@jest/expect-utils': 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  extend@3.0.2: {}

  extsprintf@1.3.0: {}

  extsprintf@1.4.1: {}

  fast-content-type-parse@1.1.0: {}

  fast-copy@3.0.2: {}

  fast-decode-uri-component@1.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-json-stable-stringify@2.1.0: {}

  fast-json-stringify@5.16.1:
    dependencies:
      '@fastify/merge-json-schemas': 0.1.1
      ajv: 8.16.0
      ajv-formats: 3.0.1(ajv@8.16.0)
      fast-deep-equal: 3.1.3
      fast-uri: 2.4.0
      json-schema-ref-resolver: 1.0.1
      rfdc: 1.4.1

  fast-levenshtein@2.0.6: {}

  fast-querystring@1.1.2:
    dependencies:
      fast-decode-uri-component: 1.0.1

  fast-redact@3.5.0: {}

  fast-safe-stringify@2.1.1: {}

  fast-uri@2.4.0: {}

  fast-xml-parser@4.2.5:
    dependencies:
      strnum: 1.0.5

  fast-xml-parser@4.4.0:
    dependencies:
      strnum: 1.0.5

  fastify-cli@5.9.0:
    dependencies:
      '@fastify/deepmerge': 1.3.0
      chalk: 4.1.2
      chokidar: 3.6.0
      close-with-grace: 1.3.0
      commist: 3.2.0
      dotenv: 16.4.5
      fastify: 4.28.0
      fastify-plugin: 4.5.1
      generify: 4.2.0
      help-me: 4.2.0
      is-docker: 2.2.1
      make-promises-safe: 5.1.0
      pino-pretty: 10.3.1
      pkg-up: 3.1.0
      resolve-from: 5.0.0
      semver: 7.6.2
      yargs-parser: 21.1.1

  fastify-plugin@4.5.1: {}

  fastify-print-routes@3.2.0:
    dependencies:
      acquerello: 2.0.8
      fastify-plugin: 4.5.1
      table: 6.8.2

  fastify-qs@4.0.2:
    dependencies:
      fastify-plugin: 4.5.1
      qs: 6.12.1

  fastify-raw-body@4.3.0:
    dependencies:
      fastify-plugin: 4.5.1
      raw-body: 2.5.2
      secure-json-parse: 2.7.0

  fastify@4.28.0:
    dependencies:
      '@fastify/ajv-compiler': 3.5.0
      '@fastify/error': 3.4.1
      '@fastify/fast-json-stringify-compiler': 4.3.0
      abstract-logging: 2.0.1
      avvio: 8.3.2
      fast-content-type-parse: 1.1.0
      fast-json-stringify: 5.16.1
      find-my-way: 8.2.0
      light-my-request: 5.13.0
      pino: 9.2.0
      process-warning: 3.0.0
      proxy-addr: 2.0.7
      rfdc: 1.4.1
      secure-json-parse: 2.7.0
      semver: 7.6.2
      toad-cache: 3.7.0

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  file-uri-to-path@1.0.0: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-cache-dir@3.3.2:
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  find-my-way@7.7.0:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-querystring: 1.1.2
      safe-regex2: 2.0.0

  find-my-way@8.2.0:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-querystring: 1.1.2
      safe-regex2: 3.1.0

  find-up@3.0.0:
    dependencies:
      locate-path: 3.0.0

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  findit@2.0.0: {}

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.1
      keyv: 4.5.4

  flatted@3.3.1: {}

  follow-redirects@1.15.6(debug@4.3.5(supports-color@7.2.0)):
    optionalDependencies:
      debug: 4.3.5(supports-color@7.2.0)

  foreground-child@2.0.0:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 3.0.7

  foreground-child@3.2.1:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  forever-agent@0.6.1: {}

  form-data-encoder@1.7.2: {}

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@2.5.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formdata-node@4.4.1:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3

  formidable@2.1.2:
    dependencies:
      dezalgo: 1.0.4
      hexoid: 1.0.0
      once: 1.4.0
      qs: 6.12.1

  forwarded@0.2.0: {}

  fromentries@1.3.2: {}

  fs-constants@1.0.0: {}

  fs-exists-cached@1.0.0: {}

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  fstream@1.0.12:
    dependencies:
      graceful-fs: 4.2.11
      inherits: 2.0.4
      mkdirp: 0.5.6
      rimraf: 2.7.1

  function-bind@1.1.2: {}

  function-loop@2.0.1: {}

  gauge@3.0.2:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5

  gauge@4.0.4:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5

  gaxios@6.7.1(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      extend: 3.0.2
      https-proxy-agent: 7.0.5(supports-color@7.2.0)
      is-stream: 2.0.1
      node-fetch: 2.7.0(encoding@0.1.13)
      uuid: 9.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  gcp-metadata@6.1.0(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      gaxios: 6.7.1(encoding@0.1.13)(supports-color@7.2.0)
      json-bigint: 1.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  generify@4.2.0:
    dependencies:
      isbinaryfile: 4.0.10
      pump: 3.0.0
      split2: 3.2.2
      walker: 1.0.8

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-package-type@0.1.0: {}

  get-stream@6.0.1: {}

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  github-from-package@0.0.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.2:
    dependencies:
      foreground-child: 3.2.1
      jackspeak: 3.4.0
      minimatch: 9.0.4
      minipass: 7.1.2
      package-json-from-dist: 1.0.0
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@8.1.0:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0

  globals@11.12.0: {}

  globals@14.0.0: {}

  globalyzer@0.1.0: {}

  globalyzer@0.1.4: {}

  globrex@0.1.2: {}

  google-auth-library@9.14.1(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      base64-js: 1.5.1
      ecdsa-sig-formatter: 1.0.11
      gaxios: 6.7.1(encoding@0.1.13)(supports-color@7.2.0)
      gcp-metadata: 6.1.0(encoding@0.1.13)(supports-color@7.2.0)
      gtoken: 7.1.0(encoding@0.1.13)(supports-color@7.2.0)
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  google-gax@4.4.1(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      '@grpc/grpc-js': 1.12.0
      '@grpc/proto-loader': 0.7.13
      '@types/long': 4.0.2
      abort-controller: 3.0.0
      duplexify: 4.1.3
      google-auth-library: 9.14.1(encoding@0.1.13)(supports-color@7.2.0)
      node-fetch: 2.7.0(encoding@0.1.13)
      object-hash: 3.0.0
      proto3-json-serializer: 2.0.2
      protobufjs: 7.4.0
      retry-request: 7.0.2(encoding@0.1.13)(supports-color@7.2.0)
      uuid: 9.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  gtoken@7.1.0(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      gaxios: 6.7.1(encoding@0.1.13)(supports-color@7.2.0)
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-unicode@2.0.1: {}

  hasha@5.2.2:
    dependencies:
      is-stream: 2.0.1
      type-fest: 0.8.1

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  help-me@4.2.0:
    dependencies:
      glob: 8.1.0
      readable-stream: 3.6.2

  help-me@5.0.0: {}

  hexoid@1.0.0: {}

  html-escaper@2.0.2: {}

  htmlparser2@3.8.3:
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.3.0
      domutils: 1.5.1
      entities: 1.0.0
      readable-stream: 1.1.14

  http-cache-semantics@4.1.1: {}

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@4.0.1(supports-color@7.2.0):
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2(supports-color@7.2.0)
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@5.0.0(supports-color@7.2.0):
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2(supports-color@7.2.0)
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  http-signature@1.4.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 2.0.2
      sshpk: 1.18.0

  httpntlm@1.8.13:
    dependencies:
      des.js: 1.1.0
      httpreq: 1.1.1
      js-md4: 0.3.2
      underscore: 1.12.1

  httpreq@1.1.1: {}

  https-proxy-agent@5.0.1(supports-color@7.2.0):
    dependencies:
      agent-base: 6.0.2(supports-color@7.2.0)
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.5(supports-color@7.2.0):
    dependencies:
      agent-base: 7.1.1(supports-color@7.2.0)
      debug: 4.3.5(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  idb-keyval@6.2.1: {}

  ieee754@1.2.1: {}

  ignore-by-default@1.0.1: {}

  ignore@5.3.2: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  infer-owner@1.0.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  ini@1.3.8: {}

  ip-address@9.0.5:
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3

  ip@2.0.1: {}

  ipaddr.js@1.9.1: {}

  is-arrayish@0.2.1: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-core-module@2.15.1:
    dependencies:
      hasown: 2.0.2

  is-docker@2.2.1: {}

  is-electron@2.2.2: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-lambda@1.0.1: {}

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-stream@2.0.1: {}

  is-typedarray@1.0.0: {}

  is-url@1.2.4: {}

  is-windows@1.0.2: {}

  isarray@0.0.1: {}

  isarray@1.0.0: {}

  isbinaryfile@4.0.10: {}

  isexe@2.0.0: {}

  isstream@0.1.2: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-hook@3.0.0:
    dependencies:
      append-transform: 2.0.0

  istanbul-lib-instrument@4.0.3(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@5.2.1(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/parser': 7.24.7
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@6.0.3(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/parser': 7.24.7
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.6.2
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-processinfo@2.0.3:
    dependencies:
      archy: 1.0.0
      cross-spawn: 7.0.3
      istanbul-lib-coverage: 3.2.2
      p-map: 3.0.0
      rimraf: 3.0.2
      uuid: 8.3.2

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1(supports-color@7.2.0):
    dependencies:
      debug: 4.3.5(supports-color@7.2.0)
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@1.4.2:
    dependencies:
      cliui: 7.0.4

  jackspeak@3.4.0:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jest-changed-files@29.7.0:
    dependencies:
      execa: 5.1.1
      jest-util: 29.7.0
      p-limit: 3.1.0

  jest-circus@29.7.0(supports-color@7.2.0):
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0(supports-color@7.2.0)
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      chalk: 4.1.2
      co: 4.6.0
      dedent: 1.5.3
      is-generator-fn: 2.1.0
      jest-each: 29.7.0
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-runtime: 29.7.0(supports-color@7.2.0)
      jest-snapshot: 29.7.0(supports-color@7.2.0)
      jest-util: 29.7.0
      p-limit: 3.1.0
      pretty-format: 29.7.0
      pure-rand: 6.1.0
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-cli@29.7.0(@types/node@18.19.38)(supports-color@7.2.0):
    dependencies:
      '@jest/core': 29.7.0(supports-color@7.2.0)
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      chalk: 4.1.2
      create-jest: 29.7.0(@types/node@18.19.38)(supports-color@7.2.0)
      exit: 0.1.2
      import-local: 3.2.0
      jest-config: 29.7.0(@types/node@18.19.38)(supports-color@7.2.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  jest-config@29.7.0(@types/node@18.19.38)(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@jest/test-sequencer': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.25.2(supports-color@7.2.0))(supports-color@7.2.0)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 29.7.0(supports-color@7.2.0)
      jest-environment-node: 29.7.0
      jest-get-type: 29.6.3
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-runner: 29.7.0(supports-color@7.2.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      micromatch: 4.0.8
      parse-json: 5.2.0
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      '@types/node': 18.19.38
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-docblock@29.7.0:
    dependencies:
      detect-newline: 3.1.0

  jest-each@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      jest-get-type: 29.6.3
      jest-util: 29.7.0
      pretty-format: 29.7.0

  jest-environment-node@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      jest-mock: 29.7.0
      jest-util: 29.7.0

  jest-get-type@29.6.3: {}

  jest-haste-map@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/graceful-fs': 4.1.9
      '@types/node': 18.19.38
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      jest-worker: 29.7.0
      micromatch: 4.0.8
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-leak-detector@29.7.0:
    dependencies:
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      '@babel/code-frame': 7.24.7
      '@jest/types': 29.6.3
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      jest-util: 29.7.0

  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    optionalDependencies:
      jest-resolve: 29.7.0

  jest-regex-util@29.6.3: {}

  jest-resolve-dependencies@29.7.0(supports-color@7.2.0):
    dependencies:
      jest-regex-util: 29.6.3
      jest-snapshot: 29.7.0(supports-color@7.2.0)
    transitivePeerDependencies:
      - supports-color

  jest-resolve@29.7.0:
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-pnp-resolver: 1.2.3(jest-resolve@29.7.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      resolve: 1.22.8
      resolve.exports: 2.0.2
      slash: 3.0.0

  jest-runner@29.7.0(supports-color@7.2.0):
    dependencies:
      '@jest/console': 29.7.0
      '@jest/environment': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      chalk: 4.1.2
      emittery: 0.13.1
      graceful-fs: 4.2.11
      jest-docblock: 29.7.0
      jest-environment-node: 29.7.0
      jest-haste-map: 29.7.0
      jest-leak-detector: 29.7.0
      jest-message-util: 29.7.0
      jest-resolve: 29.7.0
      jest-runtime: 29.7.0(supports-color@7.2.0)
      jest-util: 29.7.0
      jest-watcher: 29.7.0
      jest-worker: 29.7.0
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color

  jest-runtime@29.7.0(supports-color@7.2.0):
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/globals': 29.7.0(supports-color@7.2.0)
      '@jest/source-map': 29.6.3
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      chalk: 4.1.2
      cjs-module-lexer: 1.4.1
      collect-v8-coverage: 1.0.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-snapshot: 29.7.0(supports-color@7.2.0)
      jest-util: 29.7.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-snapshot@29.7.0(supports-color@7.2.0):
    dependencies:
      '@babel/core': 7.25.2(supports-color@7.2.0)
      '@babel/generator': 7.24.7
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/plugin-syntax-typescript': 7.25.4(@babel/core@7.25.2(supports-color@7.2.0))
      '@babel/types': 7.24.7
      '@jest/expect-utils': 29.7.0
      '@jest/transform': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.25.2(supports-color@7.2.0))
      chalk: 4.1.2
      expect: 29.7.0
      graceful-fs: 4.2.11
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      natural-compare: 1.4.0
      pretty-format: 29.7.0
      semver: 7.6.2
    transitivePeerDependencies:
      - supports-color

  jest-util@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 29.6.3
      leven: 3.1.0
      pretty-format: 29.7.0

  jest-watcher@29.7.0:
    dependencies:
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 18.19.38
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.13.1
      jest-util: 29.7.0
      string-length: 4.0.2

  jest-worker@29.7.0:
    dependencies:
      '@types/node': 18.19.38
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@29.7.0(@types/node@18.19.38)(supports-color@7.2.0):
    dependencies:
      '@jest/core': 29.7.0(supports-color@7.2.0)
      '@jest/types': 29.6.3
      import-local: 3.2.0
      jest-cli: 29.7.0(@types/node@18.19.38)(supports-color@7.2.0)
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  joycon@3.1.1: {}

  js-md4@0.3.2: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsbn@0.1.1: {}

  jsbn@1.1.0: {}

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  jshint@2.13.6:
    dependencies:
      cli: 1.0.1
      console-browserify: 1.1.0
      exit: 0.1.2
      htmlparser2: 3.8.3
      lodash: 4.17.21
      minimatch: 3.0.8
      strip-json-comments: 1.0.4

  json-bigint@1.0.0:
    dependencies:
      bignumber.js: 9.1.2

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-ref-resolver@1.0.1:
    dependencies:
      fast-deep-equal: 3.1.3

  json-schema-resolver@2.0.0(supports-color@7.2.0):
    dependencies:
      debug: 4.3.5(supports-color@7.2.0)
      rfdc: 1.4.1
      uri-js: 4.4.1
    transitivePeerDependencies:
      - supports-color

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-safe@5.0.1: {}

  json5@2.2.3: {}

  jsonc-eslint-parser@2.4.0:
    dependencies:
      acorn: 8.12.1
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      semver: 7.6.2

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  jsprim@2.0.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  jwa@2.0.0:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jws@4.0.0:
    dependencies:
      jwa: 2.0.0
      safe-buffer: 5.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kleur@3.0.3: {}

  leven@3.1.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  libtap@1.4.1:
    dependencies:
      async-hook-domain: 2.0.4
      bind-obj-methods: 3.0.0
      diff: 4.0.2
      function-loop: 2.0.1
      minipass: 3.3.6
      own-or: 1.0.0
      own-or-env: 1.0.2
      signal-exit: 3.0.7
      stack-utils: 2.0.6
      tap-parser: 11.0.2
      tap-yaml: 1.0.2
      tcompare: 5.0.7
      trivial-deferred: 1.1.2

  libxmljs2@0.33.0(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      '@mapbox/node-pre-gyp': 1.0.11(encoding@0.1.13)(supports-color@7.2.0)
      bindings: 1.5.0
      nan: 2.18.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  light-my-request@5.13.0:
    dependencies:
      cookie: 0.6.0
      process-warning: 3.0.0
      set-cookie-parser: 2.6.0

  lines-and-columns@1.2.4: {}

  locate-path@3.0.0:
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.camelcase@4.3.0: {}

  lodash.clonedeep@4.5.0: {}

  lodash.debounce@4.0.8: {}

  lodash.flattendeep@4.4.0: {}

  lodash.merge@4.6.2: {}

  lodash.truncate@4.4.2: {}

  lodash@4.17.21: {}

  long@5.2.3: {}

  lru-cache@10.2.2: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  luxon@3.4.4: {}

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.6.2

  make-fetch-happen@9.1.0(supports-color@7.2.0):
    dependencies:
      agentkeepalive: 4.5.0
      cacache: 15.3.0
      http-cache-semantics: 4.1.1
      http-proxy-agent: 4.0.1(supports-color@7.2.0)
      https-proxy-agent: 5.0.1(supports-color@7.2.0)
      is-lambda: 1.0.1
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 1.4.1
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.3
      promise-retry: 2.0.1
      socks-proxy-agent: 6.2.1(supports-color@7.2.0)
      ssri: 8.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  make-promises-safe@5.1.0: {}

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  media-typer@0.3.0: {}

  merge-stream@2.0.0: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@3.0.0: {}

  mimic-fn@2.1.0: {}

  mimic-response@2.1.0: {}

  mimic-response@3.1.0: {}

  minimalistic-assert@1.0.1: {}

  minimatch@3.0.8:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.4:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass-collect@1.0.2:
    dependencies:
      minipass: 3.3.6

  minipass-fetch@1.4.1:
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13

  minipass-flush@1.0.5:
    dependencies:
      minipass: 3.3.6

  minipass-pipeline@1.2.4:
    dependencies:
      minipass: 3.3.6

  minipass-sized@1.0.3:
    dependencies:
      minipass: 3.3.6

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@5.0.0: {}

  minipass@7.1.2: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mkdirp-classic@0.5.3: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  moment@2.30.1: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  nan@2.18.0: {}

  napi-build-utils@1.0.2: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  node-abi@3.65.0:
    dependencies:
      semver: 7.6.2

  node-addon-api@7.1.0: {}

  node-domexception@1.0.0: {}

  node-fetch@2.7.0(encoding@0.1.13):
    dependencies:
      whatwg-url: 5.0.0
    optionalDependencies:
      encoding: 0.1.13

  node-gyp@8.4.1(supports-color@7.2.0):
    dependencies:
      env-paths: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      make-fetch-happen: 9.1.0(supports-color@7.2.0)
      nopt: 5.0.0
      npmlog: 6.0.2
      rimraf: 3.0.2
      semver: 7.6.2
      tar: 6.2.1
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color

  node-int64@0.4.0: {}

  node-preload@0.2.1:
    dependencies:
      process-on-spawn: 1.0.0

  node-releases@2.0.14: {}

  node-releases@2.0.18: {}

  node-soap@1.0.0:
    dependencies:
      assert: 1.5.1
      q: 1.5.1
      underscore: 1.13.6

  node-stream-zip@1.15.0: {}

  nodemon@3.1.4:
    dependencies:
      chokidar: 3.6.0
      debug: 4.3.5(supports-color@5.5.0)
      ignore-by-default: 1.0.1
      minimatch: 3.1.2
      pstree.remy: 1.1.8
      semver: 7.6.2
      simple-update-notifier: 2.0.0
      supports-color: 5.5.0
      touch: 3.1.1
      undefsafe: 2.0.5

  nopt@5.0.0:
    dependencies:
      abbrev: 1.1.1

  normalize-path@3.0.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npmlog@5.0.1:
    dependencies:
      are-we-there-yet: 2.0.0
      console-control-strings: 1.1.0
      gauge: 3.0.2
      set-blocking: 2.0.0

  npmlog@6.0.2:
    dependencies:
      are-we-there-yet: 3.0.1
      console-control-strings: 1.1.0
      gauge: 4.0.4
      set-blocking: 2.0.0

  nunjucks@3.2.4(chokidar@3.6.0):
    dependencies:
      a-sync-waterfall: 1.0.1
      asap: 2.0.6
      commander: 5.1.0
    optionalDependencies:
      chokidar: 3.6.0

  nyc@15.1.0(supports-color@7.2.0):
    dependencies:
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      caching-transform: 4.0.0
      convert-source-map: 1.9.0
      decamelize: 1.2.0
      find-cache-dir: 3.3.2
      find-up: 4.1.0
      foreground-child: 2.0.0
      get-package-type: 0.1.0
      glob: 7.2.3
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-hook: 3.0.0
      istanbul-lib-instrument: 4.0.3(supports-color@7.2.0)
      istanbul-lib-processinfo: 2.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1(supports-color@7.2.0)
      istanbul-reports: 3.1.7
      make-dir: 3.1.0
      node-preload: 0.2.1
      p-map: 3.0.0
      process-on-spawn: 1.0.0
      resolve-from: 5.0.0
      rimraf: 3.0.2
      signal-exit: 3.0.7
      spawn-wrap: 2.0.0
      test-exclude: 6.0.0
      yargs: 15.4.1
    transitivePeerDependencies:
      - supports-color

  oauth-sign@0.9.0: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.1: {}

  object-keys@1.1.1: {}

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  on-exit-leak-free@2.1.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  openai@4.52.0(encoding@0.1.13):
    dependencies:
      '@types/node': 18.19.38
      '@types/node-fetch': 2.6.11
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0(encoding@0.1.13)
      web-streams-polyfill: 3.3.3
    transitivePeerDependencies:
      - encoding

  openapi-types@12.1.3: {}

  openapi-typescript@5.4.2:
    dependencies:
      js-yaml: 4.1.0
      mime: 3.0.0
      prettier: 2.8.8
      tiny-glob: 0.2.9
      undici: 5.28.4
      yargs-parser: 21.1.1

  opencollective-postinstall@2.0.3: {}

  opener@1.5.2: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-or-env@1.0.2:
    dependencies:
      own-or: 1.0.0

  own-or@1.0.0: {}

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@3.0.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-map@3.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-try@2.2.0: {}

  package-hash@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      hasha: 5.2.2
      lodash.flattendeep: 4.4.0
      release-zalgo: 1.0.0

  package-json-from-dist@1.0.0: {}

  packet-reader@1.0.0: {}

  papaparse@5.4.1: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.24.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  path-exists@3.0.0: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.2.2
      minipass: 7.1.2

  pdf-img-convert@1.2.1(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      canvas: 2.11.2(encoding@0.1.13)(supports-color@7.2.0)
      is-url: 1.2.4
      node-fetch: 2.7.0(encoding@0.1.13)
      pdfjs-dist: 2.16.105
    transitivePeerDependencies:
      - encoding
      - supports-color
      - worker-loader

  pdfjs-dist@2.16.105:
    dependencies:
      dommatrix: 1.0.3
      web-streams-polyfill: 3.3.3

  performance-now@2.1.0: {}

  pg-boss@9.0.3:
    dependencies:
      cron-parser: 4.9.0
      delay: 5.0.0
      lodash.debounce: 4.0.8
      p-map: 4.0.0
      pg: 8.12.0
      serialize-error: 8.1.0
      uuid: 9.0.1
    transitivePeerDependencies:
      - pg-native

  pg-cloudflare@1.1.1: {}

  pg-connection-string@2.6.4: {}

  pg-int8@1.0.1: {}

  pg-pool@3.6.2(pg@8.12.0):
    dependencies:
      pg: 8.12.0

  pg-protocol@1.6.1: {}

  pg-types@2.2.0:
    dependencies:
      pg-int8: 1.0.1
      postgres-array: 2.0.0
      postgres-bytea: 1.0.0
      postgres-date: 1.0.7
      postgres-interval: 1.2.0

  pg@8.12.0:
    dependencies:
      pg-connection-string: 2.6.4
      pg-pool: 3.6.2(pg@8.12.0)
      pg-protocol: 1.6.1
      pg-types: 2.2.0
      pgpass: 1.0.5
    optionalDependencies:
      pg-cloudflare: 1.1.1

  pgpass@1.0.5:
    dependencies:
      split2: 4.2.0

  picocolors@1.0.1: {}

  picomatch@2.3.1: {}

  pino-abstract-transport@1.2.0:
    dependencies:
      readable-stream: 4.5.2
      split2: 4.2.0

  pino-pretty@10.3.1:
    dependencies:
      colorette: 2.0.20
      dateformat: 4.6.3
      fast-copy: 3.0.2
      fast-safe-stringify: 2.1.1
      help-me: 5.0.0
      joycon: 3.1.1
      minimist: 1.2.8
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 1.2.0
      pump: 3.0.0
      readable-stream: 4.5.2
      secure-json-parse: 2.7.0
      sonic-boom: 3.8.1
      strip-json-comments: 3.1.1

  pino-std-serializers@6.2.2: {}

  pino-std-serializers@7.0.0: {}

  pino@8.21.0:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 1.2.0
      pino-std-serializers: 6.2.2
      process-warning: 3.0.0
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.4.3
      sonic-boom: 3.8.1
      thread-stream: 2.7.0

  pino@9.2.0:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 1.2.0
      pino-std-serializers: 7.0.0
      process-warning: 3.0.0
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.4.3
      sonic-boom: 4.0.1
      thread-stream: 3.1.0

  pirates@4.0.6: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  pkg-up@3.1.0:
    dependencies:
      find-up: 3.0.0

  postgres-array@2.0.0: {}

  postgres-bytea@1.0.0: {}

  postgres-date@1.0.7: {}

  postgres-interval@1.2.0:
    dependencies:
      xtend: 4.0.2

  prebuild-install@7.1.2:
    dependencies:
      detect-libc: 2.0.3
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp-classic: 0.5.3
      napi-build-utils: 1.0.2
      node-abi: 3.65.0
      pump: 3.0.0
      rc: 1.2.8
      simple-get: 4.0.1
      tar-fs: 2.1.1
      tunnel-agent: 0.6.0

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@2.8.8: {}

  prettier@3.2.5: {}

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  process-nextick-args@2.0.1: {}

  process-on-spawn@1.0.0:
    dependencies:
      fromentries: 1.3.2

  process-warning@3.0.0: {}

  process@0.11.10: {}

  promise-inflight@1.0.1: {}

  promise-retry@2.0.1:
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  proto3-json-serializer@2.0.2:
    dependencies:
      protobufjs: 7.4.0

  protobufjs@7.4.0:
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 20.16.5
      long: 5.2.3

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-from-env@1.1.0: {}

  psl@1.9.0: {}

  pstree.remy@1.1.8: {}

  pump@3.0.0:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@2.3.1: {}

  pure-rand@6.1.0: {}

  q@1.5.1: {}

  qs@6.12.1:
    dependencies:
      side-channel: 1.0.6

  qs@6.5.3: {}

  queue-microtask@1.2.3: {}

  quick-format-unescaped@4.0.4: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1

  react-is@18.3.1: {}

  readable-stream@1.1.14:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readable-stream@4.5.2:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  real-require@0.2.0: {}

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.13.11: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.25.6

  regexpu-core@5.3.2:
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsparser@0.9.1:
    dependencies:
      jsesc: 0.5.0

  release-zalgo@1.0.0:
    dependencies:
      es6-error: 4.1.1

  repeat-string@1.6.1: {}

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-main-filename@2.0.0: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve.exports@2.0.2: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  ret@0.2.2: {}

  ret@0.4.3: {}

  retry-request@7.0.2(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      '@types/request': 2.48.12
      extend: 3.0.2
      teeny-request: 9.0.0(encoding@0.1.13)(supports-color@7.2.0)
    transitivePeerDependencies:
      - encoding
      - supports-color

  retry@0.12.0: {}

  reusify@1.0.4: {}

  rfdc@1.4.1: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-regex2@2.0.0:
    dependencies:
      ret: 0.2.2

  safe-regex2@3.1.0:
    dependencies:
      ret: 0.4.3

  safe-stable-stringify@2.4.3: {}

  safer-buffer@2.1.2: {}

  sax@1.4.1: {}

  secure-json-parse@2.7.0: {}

  semver@6.3.1: {}

  semver@7.6.2: {}

  serialize-error@8.1.0:
    dependencies:
      type-fest: 0.20.2

  set-blocking@2.0.0: {}

  set-cookie-parser@2.6.0: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.1

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  simple-concat@1.0.1: {}

  simple-get@3.1.1:
    dependencies:
      decompress-response: 4.2.1
      once: 1.4.0
      simple-concat: 1.0.1

  simple-get@4.0.1:
    dependencies:
      decompress-response: 6.0.0
      once: 1.4.0
      simple-concat: 1.0.1

  simple-update-notifier@2.0.0:
    dependencies:
      semver: 7.6.2

  sisteransi@1.0.5: {}

  slash@3.0.0: {}

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  smart-buffer@4.2.0: {}

  soap@0.45.0(axios@1.7.2(debug@4.3.5(supports-color@7.2.0)))(supports-color@7.2.0):
    dependencies:
      axios: 1.7.2(debug@4.3.5(supports-color@7.2.0))
      axios-ntlm: 1.4.2(debug@4.3.5(supports-color@7.2.0))
      debug: 4.3.5(supports-color@7.2.0)
      formidable: 2.1.2
      get-stream: 6.0.1
      lodash: 4.17.21
      sax: 1.4.1
      strip-bom: 3.0.0
      uuid: 8.3.2
      whatwg-mimetype: 3.0.0
      xml-crypto: 2.1.5
    transitivePeerDependencies:
      - supports-color

  socks-proxy-agent@6.2.1(supports-color@7.2.0):
    dependencies:
      agent-base: 6.0.2(supports-color@7.2.0)
      debug: 4.3.5(supports-color@7.2.0)
      socks: 2.8.3
    transitivePeerDependencies:
      - supports-color

  socks@2.8.3:
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0

  sonic-boom@3.8.1:
    dependencies:
      atomic-sleep: 1.0.0

  sonic-boom@4.0.1:
    dependencies:
      atomic-sleep: 1.0.0

  source-map-support@0.5.13:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  spawn-wrap@2.0.0:
    dependencies:
      foreground-child: 2.0.0
      is-windows: 1.0.2
      make-dir: 3.1.0
      rimraf: 3.0.2
      signal-exit: 3.0.7
      which: 2.0.2

  split2@3.2.2:
    dependencies:
      readable-stream: 3.6.2

  split2@4.2.0: {}

  sprintf-js@1.0.3: {}

  sprintf-js@1.1.3: {}

  sqlite3@5.1.7(supports-color@7.2.0):
    dependencies:
      bindings: 1.5.0
      node-addon-api: 7.1.0
      prebuild-install: 7.1.2
      tar: 6.2.1
    optionalDependencies:
      node-gyp: 8.4.1(supports-color@7.2.0)
    transitivePeerDependencies:
      - bluebird
      - supports-color

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  ssri@8.0.1:
    dependencies:
      minipass: 3.3.6

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  statuses@2.0.1: {}

  stream-events@1.0.5:
    dependencies:
      stubs: 3.0.0

  stream-shift@1.0.3: {}

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string_decoder@0.10.31: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.0.1

  strip-bom@3.0.0: {}

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-json-comments@1.0.4: {}

  strip-json-comments@2.0.1: {}

  strip-json-comments@3.1.1: {}

  strnum@1.0.5: {}

  stubs@3.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  synckit@0.6.2:
    dependencies:
      tslib: 2.6.3

  synckit@0.9.1:
    dependencies:
      '@pkgr/core': 0.1.1
      tslib: 2.6.3

  table@6.8.2:
    dependencies:
      ajv: 8.16.0
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  tap-mocha-reporter@5.0.4(supports-color@7.2.0):
    dependencies:
      color-support: 1.1.3
      debug: 4.3.5(supports-color@7.2.0)
      diff: 4.0.2
      escape-string-regexp: 2.0.0
      glob: 7.2.3
      tap-parser: 11.0.2
      tap-yaml: 1.0.2
      unicode-length: 2.1.0
    transitivePeerDependencies:
      - supports-color

  tap-parser@11.0.2:
    dependencies:
      events-to-array: 1.1.2
      minipass: 3.3.6
      tap-yaml: 1.0.2

  tap-yaml@1.0.2:
    dependencies:
      yaml: 1.10.2

  tap@16.3.10(supports-color@7.2.0):
    dependencies:
      chokidar: 3.6.0
      findit: 2.0.0
      foreground-child: 2.0.0
      fs-exists-cached: 1.0.0
      glob: 7.2.3
      isexe: 2.0.0
      istanbul-lib-processinfo: 2.0.3
      jackspeak: 1.4.2
      libtap: 1.4.1
      minipass: 3.3.6
      mkdirp: 1.0.4
      nyc: 15.1.0(supports-color@7.2.0)
      opener: 1.5.2
      rimraf: 3.0.2
      signal-exit: 3.0.7
      source-map-support: 0.5.21
      tap-mocha-reporter: 5.0.4(supports-color@7.2.0)
      tap-parser: 11.0.2
      tap-yaml: 1.0.2
      tcompare: 5.0.7
      which: 2.0.2
    transitivePeerDependencies:
      - supports-color

  tar-fs@2.1.1:
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.0
      tar-stream: 2.2.0

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.4
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  tcompare@5.0.7:
    dependencies:
      diff: 4.0.2

  teeny-request@9.0.0(encoding@0.1.13)(supports-color@7.2.0):
    dependencies:
      http-proxy-agent: 5.0.0(supports-color@7.2.0)
      https-proxy-agent: 5.0.1(supports-color@7.2.0)
      node-fetch: 2.7.0(encoding@0.1.13)
      stream-events: 1.0.5
      uuid: 9.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  tesseract.js-core@5.1.1: {}

  tesseract.js@5.1.1(encoding@0.1.13):
    dependencies:
      bmp-js: 0.1.0
      idb-keyval: 6.2.1
      is-electron: 2.2.2
      is-url: 1.2.4
      node-fetch: 2.7.0(encoding@0.1.13)
      opencollective-postinstall: 2.0.3
      regenerator-runtime: 0.13.11
      tesseract.js-core: 5.1.1
      wasm-feature-detect: 1.8.0
      zlibjs: 0.3.1
    transitivePeerDependencies:
      - encoding

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-table@0.2.0: {}

  thread-stream@2.7.0:
    dependencies:
      real-require: 0.2.0

  thread-stream@3.1.0:
    dependencies:
      real-require: 0.2.0

  tiny-glob@0.2.9:
    dependencies:
      globalyzer: 0.1.0
      globrex: 0.1.2

  tmpl@1.0.5: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toad-cache@3.7.0: {}

  toidentifier@1.0.1: {}

  touch@3.1.1: {}

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1

  tr46@0.0.3: {}

  trivial-deferred@1.1.2: {}

  tslib@2.6.3: {}

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  type-fest@0.8.1: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typedarray-to-buffer@3.1.5:
    dependencies:
      is-typedarray: 1.0.0

  undefsafe@2.0.5: {}

  underscore@1.12.1: {}

  underscore@1.13.6: {}

  undici-types@5.26.5: {}

  undici-types@5.28.4: {}

  undici-types@6.19.8: {}

  undici@5.28.4:
    dependencies:
      '@fastify/busboy': 2.1.1

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-length@2.1.0:
    dependencies:
      punycode: 2.3.1

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  unique-filename@1.1.1:
    dependencies:
      unique-slug: 2.0.2

  unique-slug@2.0.2:
    dependencies:
      imurmurhash: 0.1.4

  unpipe@1.0.0: {}

  unzipper@0.11.6:
    dependencies:
      big-integer: 1.6.52
      bluebird: 3.4.7
      duplexer2: 0.1.4
      fstream: 1.0.12
      graceful-fs: 4.2.11

  update-browserslist-db@1.0.16(browserslist@4.23.1):
    dependencies:
      browserslist: 4.23.1
      escalade: 3.1.2
      picocolors: 1.0.1

  update-browserslist-db@1.1.0(browserslist@4.23.3):
    dependencies:
      browserslist: 4.23.3
      escalade: 3.1.2
      picocolors: 1.0.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  util-deprecate@1.0.2: {}

  util@0.10.4:
    dependencies:
      inherits: 2.0.3

  uuid@3.4.0: {}

  uuid@8.3.2: {}

  uuid@9.0.1: {}

  v8-to-istanbul@9.3.0:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 2.0.0

  vary@1.1.2: {}

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.4.1

  verror@1.10.1:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.4.1

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  wasm-feature-detect@1.8.0: {}

  web-streams-polyfill@3.3.3: {}

  web-streams-polyfill@4.0.0-beta.3: {}

  webidl-conversions@3.0.1: {}

  whatwg-mimetype@3.0.0: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-module@2.0.1: {}

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3

  word-wrap@1.2.5: {}

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@3.0.3:
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5

  write-file-atomic@4.0.2:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  xml-beautifier@0.5.0:
    dependencies:
      repeat-string: 1.6.1

  xml-crypto@2.1.5:
    dependencies:
      '@xmldom/xmldom': 0.7.13
      xpath: 0.0.32

  xml-formatter@3.6.2:
    dependencies:
      xml-parser-xo: 4.1.1

  xml-parser-xo@4.1.1: {}

  xml2js@0.6.2:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xpath@0.0.32: {}

  xtend@4.0.2: {}

  y18n@4.0.3: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml-eslint-parser@1.2.3:
    dependencies:
      eslint-visitor-keys: 3.4.3
      lodash: 4.17.21
      yaml: 2.4.5

  yaml@1.10.2: {}

  yaml@2.4.5: {}

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@21.1.1: {}

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.2
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  zlibjs@0.3.1: {}
