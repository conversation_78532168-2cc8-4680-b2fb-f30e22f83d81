# STAGE: BASE
# Use <PERSON> instead of <PERSON> for better compatibility
FROM node:20-slim AS base

# Set environment (DO NOT combine into a single ENV command)
ARG NODE_ENV
ARG OPENSSL_VERSION=3.0.8
ENV NODE_ENV=$NODE_ENV
ENV SHELL=/bin/sh
ENV CLARA_HOME="/opt/clara"
ENV CLARA_BIN="node $CLARA_HOME/cli/bin/run.js"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$CLARA_HOME/cli/bin:$PATH"

# We need to compile openssl with FIPS enabled for compliance purposes.

RUN apt-get update \
    && apt-get install -y \
    wget \
    perl \
    gcc \
    musl-dev \
    make \
    curl \
    ca-certificates \
    inotify-tools

RUN wget https://www.openssl.org/source/openssl-$OPENSSL_VERSION.tar.gz
RUN tar xf openssl-$OPENSSL_VERSION.tar.gz

WORKDIR openssl-$OPENSSL_VERSION/

RUN ./Configure --libdir=lib enable-fips
RUN make
RUN make install
RUN make install_fips
RUN cp -a /etc/ssl/certs/* /usr/local/ssl/certs/
RUN openssl fipsinstall -out /usr/local/ssl/fipsmodule.cnf -module /usr/local/lib/ossl-modules/fips.so
RUN openssl version

COPY ./integration-server/container/config/openssl/openssl.cnf /usr/local/ssl/openssl.cnf
#ENV OPENSSL_CONF=/etc/ssl/openssl.cnf
#ENV OPENSSL_MODULES=/usr/local/lib/ossl-modules
#ENV OPENSSL_FIPS=1
# Install utils, deps, and configs we want in the final image
RUN apt-get -y update \
    && apt-get install -y \
    caddy \
    htop \
    imagemagick \
    graphicsmagick \
    logrotate \
    lsof \
    nano \
    openssh-client \
    procps \
    && if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then \
    apt-get install -y \
    inotify-tools \
    iproute2 \
    rsync \
    ; \
    echo "/var/log/clara/*.log {\n    size 20M\n    compress\n    rotate 14\n    missingok\n    notifempty\n    create 0640 root root\n    sharedscripts\n}" > /etc/logrotate.d/clara; \

    else \
    echo "/var/log/clara/*.log {\n    size 50M\n    compress\n    rotate 28\n    missingok\n    notifempty\n    create 0640 root root\n    sharedscripts\n}" > /etc/logrotate.d/clara; \
    fi \
    && echo "#!/bin/sh\n/usr/sbin/logrotate /etc/logrotate.d/clara" > /etc/cron.hourly/logrotate \
    && mkdir -p $PNPM_HOME \
    && corepack enable \
    && pnpm i --no-optional --cache .pnpm-cache -g --prod \
    oclif \
    pm2 \
    typescript \
    && rm -rf .pnpm-cache \
    && chmod +x /etc/cron.hourly/logrotate \
    && mkdir -p /var/log/clara/ \
    && echo "PATH=\"$PNPM_HOME:$CLARA_HOME/cli/bin:\$PATH\"" >> /etc/profile \
    && echo "export PS1='\$(whoami)@\$(hostname):\$(pwd)# '" >> /etc/profile \
    && echo "alias clara='$CLARA_BIN';" >> /etc/profile



# STAGE: BUILD
# Install deps only for building
FROM base AS build

RUN apt-get install -y \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    pkg-config \
    python3 \
    && pnpm i --no-optional --cache .pnpm-cache -g --prod \
    node-gyp \
    && rm -rf .pnpm-cache

# Copy bd/ repo
WORKDIR $CLARA_HOME

COPY --link . .

# Install deps for CLI and IS
RUN chmod +x $CLARA_HOME/cli/bin/* \
    && chmod +x $CLARA_HOME/integration-server/container/exec/* \
    && pnpm i --prefix $CLARA_HOME/cli --cache .pnpm-cache --prod=false \
    && if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then \
    pnpm i --prefix $CLARA_HOME/integration-server --cache .pnpm-cache --unsafe-perm; \
    else \
    pnpm i --prefix $CLARA_HOME/integration-server --cache .pnpm-cache --prod --no-optional; \
    fi \
    && rm -rf .pnpm-cache

# STAGE: DEVELOPMENT RUNTIME
# Copy from build stage and start services
FROM base AS development

COPY --link --from=build $CLARA_HOME $CLARA_HOME

# Start caddy, NES, frontend etc.
WORKDIR $CLARA_HOME/integration-server

EXPOSE 3000

CMD ["/opt/clara/integration-server/container/exec/entry.sh.start"]



# STAGE: TESTING RUNTIME
# Identical to development stage
FROM development AS testing



# STAGE: PRODUCTION RUNTIME
# Copy from build stage and start services
FROM base AS production

RUN apt-get clean \
    && rm -f /var/lib/apt/lists/*_*

# Manually maintain the sync folder list to be similar to:
#   bd/cli/src/commands/fly/watch.ts
#   bd/nes/container/exec/nes.watcher

# NOTE: In staging/production, js* are built into tshomebase/dist/public*
#       so there is no need to copy js* folders here


COPY --link --from=build $CLARA_HOME/cli $CLARA_HOME/cli
COPY --link --from=build $CLARA_HOME/integration-server $CLARA_HOME/integration-server

# Start caddy, NES etc.
WORKDIR $CLARA_HOME/integration-server

EXPOSE 3000

CMD ["/opt/clara/integration-server/container/exec/entry.sh.start"]



# STAGE: STAGING RUNTIME
# Identical to production stage
FROM production AS staging