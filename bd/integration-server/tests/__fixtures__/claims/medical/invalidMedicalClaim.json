{"billing": {"providerType": "Invalid<PERSON><PERSON>ider", "npi": "123", "firstName": "Billing Provider", "lastName": "<PERSON><PERSON>", "contactInformation": {"name": "Billing Contact", "phoneNumber": "invalid-phone"}}, "claimInformation": {"benefitsAssignmentCertificationIndicator": "Invalid", "claimChargeAmount": "1000.00", "claimFilingCode": "99", "claimFrequencyCode": 1, "healthCareCodeInformation": [{"diagnosisTypeCode": "Invalid", "diagnosisCode": 123}], "patientControlNumber": null, "placeOfServiceCode": "999", "planParticipationCode": "Invalid", "providerAcceptAssignmentCode": "Invalid", "providerSignatureOnFile": "Maybe", "relatedCausesInformation": {"relatedCausesCode": 123}, "serviceLines": [{"serviceId": 99213, "modifiers": "25", "lineItemChargeAmount": "100.00", "unitOrBasisForMeasurementCode": 123, "serviceUnitCount": "1", "placeOfServiceCode": 11, "diagnosisCodePointers": "1"}]}, "controlNumber": *********, "dependent": {"firstName": "", "lastName": null, "dateOfBirth": "19800505", "gender": "Invalid", "relationshipToSubscriberCode": 1, "contactInformation": {"name": 12345, "phoneNumber": null}, "address": {"address1": "", "city": null, "state": "TooLong", "postalCode": 54321}}, "ordering": null, "payToAddress": "Invalid Address", "payToPlan": {"organizationName": 12345, "primaryIdentifier": null, "primaryIdentifierTypeCode": 123, "taxIdentificationNumber": "Invalid", "address": null}, "payerAddress": null, "providers": "Not an array", "receiver": {"organizationName": null}, "submitter": {"contactInformation": null, "lastName": 12345, "firstName": null, "organizationName": ""}, "subscriber": {"memberId": null, "paymentResponsibilityLevelCode": "Invalid", "firstName": "Invalid", "lastName": null, "dateOfBirth": "19850607", "gender": "Invalid", "address": null, "contactInformation": "Invalid"}, "tradingPartnerName": null, "tradingPartnerServiceId": 12345, "usageIndicator": "Invalid"}