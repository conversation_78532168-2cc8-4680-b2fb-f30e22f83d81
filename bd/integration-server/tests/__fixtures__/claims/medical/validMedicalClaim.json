{"_meta": {"clara_mrn": "132532", "clara_claim_uuid": "o4ofidfgd87sd"}, "billing": {"providerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "npi": "**********", "firstName": "johnt<PERSON>", "lastName": "do<PERSON><PERSON>", "employerId": "123456", "address": {"address1": "123 address1", "city": "city1", "state": "wa", "postalCode": "*********"}, "contactInformation": {"name": "submitter contact info", "phoneNumber": "**********"}}, "claimInformation": {"benefitsAssignmentCertificationIndicator": "Y", "claimChargeAmount": "1000.00", "claimFilingCode": "12", "claimFrequencyCode": "1", "releaseInformationCode": "Y", "signatureIndicator": "Y", "healthCareCodeInformation": [{"diagnosisTypeCode": "ABK", "diagnosisCode": "R69"}], "patientControlNumber": "00002", "placeOfServiceCode": "11", "planParticipationCode": "A", "providerAcceptAssignmentCode": "A", "providerSignatureOnFile": "Y", "relatedCausesCode": "OA", "serviceLines": [{"serviceId": "99213", "modifiers": ["25"], "lineItemChargeAmount": 100.0, "unitOrBasisForMeasurementCode": "UN", "serviceUnitCount": "1", "placeOfServiceCode": "11", "professionalService": {"serviceId": "99213", "modifiers": ["25"], "quantity": 1, "unitOrBasisForMeasurementCode": "UN", "serviceOrProcedureDate": "2023-05-01", "description": "Office visit", "compositeDiagnosisCodePointers": {"diagnosisCodePointers": ["1"]}, "lineItemChargeAmount": "100.00", "measurementUnit": "UN", "procedureCode": "99213", "procedureIdentifier": "HC", "serviceUnitCount": "1"}, "serviceDate": "2023-05-01"}]}, "controlNumber": "*********", "dependent": {"firstName": "johnone", "lastName": "doeone", "dateOfBirth": "2000-01-01", "gender": "F", "relationshipToSubscriberCode": "01", "contactInformation": {"name": "janetwo doetwo", "phoneNumber": "**********"}, "address": {"address1": "456 Dependent St", "city": "Dependentville", "state": "DV", "postalCode": "54321"}}, "ordering": {"providerType": "OrderingProvider", "npi": "**********", "firstName": "johnt<PERSON>", "lastName": "do<PERSON><PERSON>", "address": {"address1": "123 address1", "city": "city1", "state": "wa", "postalCode": "*********"}, "contactInformation": {"name": "submitter contact info", "phoneNumber": "**********"}}, "payToAddress": {"address1": "123 address1", "city": "city1", "state": "wa", "postalCode": "*********"}, "payToPlan": {"organizationName": "extra healthy insurance", "primaryIdentifier": "PAYTO001", "primaryIdentifierTypeCode": "PI", "taxIdentificationNumber": "*********", "address": {"address1": "123 address1", "city": "city1", "state": "wa", "postalCode": "*********"}}, "payerAddress": {"address1": "123 address1", "city": "city1", "state": "wa", "postalCode": "*********"}, "receiver": {"organizationName": "HAPPY RECEIVER GROUP"}, "submitter": {"contactInformation": {"name": "janetwo doetwo", "phoneNumber": "**********"}, "firstName": "johnone", "lastName": "doeone"}, "subscriber": {"memberId": "**********", "paymentResponsibilityLevelCode": "P", "firstName": "johnone", "lastName": "doeone", "dateOfBirth": "1980-01-01", "gender": "M", "address": {"address1": "123 address1", "city": "city1", "state": "wa", "postalCode": "*********"}, "contactInformation": {"name": "johnone doeone", "phoneNumber": "**********"}}, "tradingPartnerName": "Trading Partner Corp", "tradingPartnerServiceId": "TP001", "usageIndicator": "P"}