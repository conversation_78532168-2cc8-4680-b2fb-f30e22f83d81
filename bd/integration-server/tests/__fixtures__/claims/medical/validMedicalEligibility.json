{"_meta": {"clara_mrn": "132532", "clara_claim_uuid": "afsadgfsdafg34534"}, "controlNumber": "123456789", "provider": {"organizationName": "provider_name", "npi": "**********", "serviceProviderNumber": "54321", "providerCode": "AD", "referenceIdentification": "54321g"}, "subscriber": {"memberId": "**********", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "DoeOne", "gender": "M", "dateOfBirth": "19800101", "address": {"address1": "123 Main St", "city": "Anytown", "state": "CA", "postalCode": "12345"}}, "submitterTransactionIdentifier": "BHT03", "tradingPartnerServiceId": "serviceId", "tradingPartnerName": "PartnerName", "portalUsername": "username", "portalPassword": "password", "informationReceiverName": {"stateLicenceNumber": "12345", "address": {"address1": "123 Main St", "city": "Anytown", "state": "CA", "postalCode": "12345"}}, "encounter": {"beginningDateOfService": "20100101", "endDateOfService": "20100102", "serviceTypeCodes": ["98"]}}