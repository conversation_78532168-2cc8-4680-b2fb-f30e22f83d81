import { MedicalClaim } from "../../../src/modules/claims/handlers/medical/changeClaims.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import fetch from "node-fetch";
import { config } from "../../config.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

describe("MedicalClaimsValidation", () => {
    const validDataPath = path.join(
        __dirname,
        "../../__fixtures__/claims/medical",
        "validMedicalClaim.json"
    );
    const invalidDataPath = path.join(
        __dirname,
        "../../__fixtures__/claims/medical",
        "invalidMedicalClaim.json"
    );
    const validData = JSON.parse(fs.readFileSync(validDataPath, "utf8"));
    const invalidData = JSON.parse(fs.readFileSync(invalidDataPath, "utf8"));

    test("should create a valid MedicalClaim object", () => {
        console.log("Valid data:", validData);
        const medicalClaim = new MedicalClaim(validData);
        const isValid = medicalClaim.isValid();
        expect(isValid).toBe(true);
    });

    test("should fail validation for invalid MedicalClaim object", () => {
        const error = captureError(() => new MedicalClaim(invalidData));
        expect(error).toBeDefined();
        console.log("Validation error:", JSON.stringify(error, null, 2));

        expect(error.type).toBe("validation");
        expect(error.field).toBe("root");
        expect(error.message).toBe("Claim validation failed");
        expect(error.parent).toBe("ClaimInformation");
        expect(error.name).toBe("ChangeHealthError");

        const errorDescription = JSON.parse(error.description);
        expect(Array.isArray(errorDescription)).toBe(true);

        // Check for specific validation errors
        expect(errorDescription).toEqual(
            expect.arrayContaining([
                expect.objectContaining({
                    type: "required",
                    field: "patientControlNumber",
                    message:
                        "must have required property 'patientControlNumber'",
                }),
                expect.objectContaining({
                    type: "validation",
                    field: "/claimFilingCode",
                    message: expect.stringContaining(
                        "must be equal to one of the allowed values"
                    ),
                }),
                expect.objectContaining({
                    type: "validation",
                    field: "/claimFrequencyCode",
                    message: "must be string",
                }),
                expect.objectContaining({
                    type: "validation",
                    field: "/planParticipationCode",
                    message: expect.stringContaining(
                        "must be equal to one of the allowed values"
                    ),
                }),
                expect.objectContaining({
                    type: "validation",
                    field: "/benefitsAssignmentCertificationIndicator",
                    message: expect.stringContaining(
                        "must be equal to one of the allowed values"
                    ),
                }),
                expect.objectContaining({
                    type: "validation",
                    field: "/healthCareCodeInformation/0/diagnosisTypeCode",
                    message: expect.stringContaining(
                        "must be equal to one of the allowed values"
                    ),
                }),
                expect.objectContaining({
                    type: "validation",
                    field: "/healthCareCodeInformation/0/diagnosisCode",
                    message: "must be string",
                }),
                expect.objectContaining({
                    type: "required",
                    field: "professionalService",
                    message:
                        "must have required property 'professionalService'",
                }),
                expect.objectContaining({
                    type: "required",
                    field: "serviceDate",
                    message: "must have required property 'serviceDate'",
                }),
            ])
        );
    });
    test("should successfully submit valid data to the claims processor", async () => {
        const validClaim = {
            ...validData,
            _meta: { ...validData._meta, clara_claim_uuid: "**********" },
        };
        const auth_header = `Bearer ${config.BASIC_AUTH_PASSWORD}`;
        console.log("auth_header", auth_header);
        const response = await fetch(
            "http://localhost:3005/claims/med/submit",
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "is-test": "true",
                    Authorization: auth_header,
                },
                body: JSON.stringify(validClaim),
            }
        );
        const d = await response.json();
        expect(response.ok).toBe(true);
        expect(response.status).toBe(200);
        expect(d.response_json_data).toBeDefined();
        console.log("response", d.response_json_data);
    });
});

function captureError(fn) {
    try {
        fn();
        return null;
    } catch (error) {
        return error;
    }
}
