import { MedicalEligibility } from "../../../src/modules/claims/handlers/medical/changeElig.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import fetch from "node-fetch";
import { config } from "../../config.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

describe("MedicalEligibility Validation", () => {
    const validDataPath = path.join(
        __dirname,
        "../../__fixtures__/claims/medical",
        "validMedicalEligibility.json"
    );
    const invalidDataPath = path.join(
        __dirname,
        "../../__fixtures__/claims/medical",
        "invalidMedicalEligibility.json"
    );
    const invalidDataMRFPath = path.join(
        __dirname,
        "../../__fixtures__/claims/medical",
        "invalidMedicalEligibilityRequiredFieldMissing.json"
    );
    const validData = JSON.parse(fs.readFileSync(validDataPath, "utf8"));
    const invalidData = JSON.parse(fs.readFileSync(invalidDataPath, "utf8"));
    const invalidDataMRF = JSON.parse(
        fs.readFileSync(invalidDataMRFPath, "utf8")
    );

    test("should create a valid MedicalEligibility object", () => {
        const medicalEligibility = new MedicalEligibility(validData);
        console.log(
            "MedicalEligibility:",
            JSON.stringify(medicalEligibility.toJSON(), null, 2)
        );
        const isValid = medicalEligibility.isValid();
        expect(isValid).toBe(true);
    });

    test("should fail validation when control number is too short", () => {
        const invalidDataWithShortControlNumber = {
            ...validData,
            controlNumber: "12345",
        };
        let error;
        try {
            new MedicalEligibility(invalidDataWithShortControlNumber);
        } catch (e) {
            error = e;
        }
        console.log("Error message:", error?.message);
        expect(error).toBeDefined();
        expect(error.message).toMatch(
            /Validation failed for MedicalEligibility data/
        );
    });

    test("should fail validation for missing address for subscriber", () => {
        const invalidDataWithoutAddress = {
            ...validData,
            subscriber: { ...validData.subscriber, address: undefined },
        };
        let error;
        try {
            new MedicalEligibility(invalidDataWithoutAddress);
        } catch (e) {
            error = e;
        }
        console.log("Error message:", error?.message);
        expect(error).toBeDefined();
        expect(error.message).toMatch(/Required field .+ is missing/);
    });

    test("should fail validation for invalid MedicalEligibility object with missing required fields", () => {
        const error = captureError(
            () => new MedicalEligibility(invalidDataMRF)
        );
        console.log("Error message:", error?.message);
        expect(error).toBeDefined();
        expect(error.message).toMatch(/Required field .+ is missing/);
    });

    test("should fail validation for invalid MedicalEligibility object with invalid data types", () => {
        const error = captureError(() => new MedicalEligibility(invalidData));
        expect(error).toBeDefined();
        expect(error.message).toMatch(/Validation failed for .+ data/);
        expect(error.message).toMatch(
            /must be (string|number|boolean|object|array)/
        );
        console.log("Full error message:", error.message);
        try {
            const validationErrors = JSON.parse(error.message.split(". ")[1]);
            console.log(
                "Validation errors:",
                JSON.stringify(validationErrors, null, 2)
            );
        } catch (e) {
            console.log(
                `Could not parse validation errors from the message: ${e}`
            );
        }
    });

    test("should successfully submit valid data to /med/eligibility endpoint and get response from Change Healthcare Sandbox", async () => {
        const auth_header = `Bearer ${config.BASIC_AUTH_PASSWORD}`;
        console.log("auth_header", auth_header);
        const response = await fetch(
            "http://localhost:3005/claims/med/eligibility",
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "is-test": "true",
                    Authorization: auth_header,
                },
                body: JSON.stringify(validData),
            }
        );

        expect(response.status).toBe(200);
        const responseBody = await response.json();
        expect(responseBody.response_json_data).toBeDefined();
        expect(
            responseBody.response_json_data.benefitsInformation
        ).toBeDefined();
        expect(
            responseBody.response_json_data.benefitsInformation[0].code
        ).toBe("1");

        console.log("Response body:", JSON.stringify(responseBody, null, 2));
    });
});

function captureError(fn) {
    try {
        fn();
        return null;
    } catch (error) {
        return error;
    }
}
