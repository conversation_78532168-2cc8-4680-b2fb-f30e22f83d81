{"printWidth": 80, "quoteProps": "as-needed", "tabWidth": 4, "useTabs": false, "overrides": [{"files": ["*.js", "*.jsx", "*.ts", "*.tsx"], "options": {"bracketSpacing": true, "semi": true, "singleQuote": false, "trailingComma": "es5"}}, {"files": ["*.json"], "options": {"bracketSpacing": true, "parser": "jsonc", "semi": true, "singleQuote": false, "trailingComma": "none"}}, {"files": ["*.yaml", "*.yml"], "options": {"bracketSpacing": false, "parser": "yaml", "printWidth": 160, "singleQuote": true, "tabWidth": 2}}]}