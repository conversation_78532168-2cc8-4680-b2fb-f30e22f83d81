{"name": "@clara/nes", "version": "1.0.0", "description": "NES Clara", "license": "UNLICENSED", "private": true, "main": "src/index.js", "packageManager": "pnpm@10.11.0", "scripts": {"test": "jest --runInBand --detectOpenHandles --forceExit", "debug": "node --inspect=[::]:9229 index.js", "start": "node index.js"}, "_moduleAliases": {"@root": ".", "@api": "src/api", "@core": "src/core", "@db": "src/core/modules/db", "@form": "src/api/form", "@actions": "src/api/form/actions", "@billing": "src/api/billing", "@surescripts": "src/api/surescripts", "@inventory": "src/api/inventory", "@dispense": "src/api/dispense", "@patient": "src/api/patient", "@operations": "src/api/operations", "@schemas": "src/core/schemas", "@server": "src/core/modules/server", "@service": "src/core/modules/service", "@tests": "__tests__", "@utils": "src/core/utils"}, "dependencies": {"@aws-sdk/client-s3": "catalog:", "@joi/date": "catalog:", "@koa/cors": "catalog:", "@ladjs/koa-views": "catalog:", "@ringcentral/sdk": "catalog:", "@sentry/node": "catalog:", "@sentry/profiling-node": "catalog:", "ajv": "catalog:", "ajv-errors": "catalog:", "ajv-formats": "catalog:", "ajv-keywords": "catalog:", "async-busboy": "catalog:", "async-lock": "catalog:", "axios": "catalog:", "bcrypt": "catalog:", "btoa": "catalog:", "camelcase-keys": "catalog:", "cookie": "catalog:", "cron-time-generator": "catalog:", "currency.js": "catalog:", "dotenv-cli": "catalog:", "flat": "catalog:", "gm": "catalog:", "joi": "catalog:", "jsonwebtoken": "catalog:", "koa": "catalog:", "koa-body": "catalog:", "koa-jwt": "catalog:", "koa-router": "catalog:", "koa-session": "catalog:", "koa-websocket": "catalog:", "koa2-winston": "catalog:", "lodash": "catalog:", "mathjs": "catalog:", "mime-detect": "catalog:", "module-alias-jest": "catalog:", "moment": "catalog:", "moment-timezone": "catalog:", "mustache": "catalog:", "node-fetch": "catalog:", "node-forge": "catalog:", "node-gyp": "catalog:", "numeral": "catalog:", "nunjucks": "catalog:", "openid-client": "catalog:", "path-match": "catalog:", "pdf-lib": "catalog:", "pdfmake": "catalog:", "pg": "catalog:", "pg-boss": "catalog:", "pg-escape": "catalog:", "pg-pool": "catalog:", "pg-promise": "catalog:", "pg-query-stream": "catalog:", "rrule": "catalog:", "sequelize": "catalog:", "snakecase-keys": "catalog:", "uuid": "catalog:", "winston": "catalog:", "winston-console-format": "catalog:", "winston-daily-rotate-file": "catalog:", "winston-slack-webhook-transport": "catalog:", "winston-transport": "catalog:", "zxcvbn": "catalog:"}, "devDependencies": {"@apidevtools/json-schema-ref-parser": "catalog:", "@eslint/js": "catalog:", "@faker-js/faker": "catalog:", "@jest/globals": "catalog:", "@types/eslint__js": "catalog:", "babel-jest": "catalog:", "cli-table3": "catalog:", "coffeescript": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-yml": "catalog:", "fast-xml-parser": "catalog:", "fs-extra": "catalog:", "globals": "catalog:", "jest": "catalog:", "node-notifier": "catalog:", "prettier": "catalog:", "pretty-format": "catalog:", "supertest": "catalog:"}}