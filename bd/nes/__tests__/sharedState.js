"use strict";
const fs = require("fs");
const path = require("path");

const stateFilePath = path.resolve(__dirname, "sharedState.json");

module.exports = {
    get: () => {
        if (fs.existsSync(stateFilePath)) {
            const data = fs.readFileSync(stateFilePath, "utf8");
            return JSON.parse(data);
        }
        return {};
    },
    set: (key, value) => {
        const state = module.exports.get();
        state[key] = value;
        fs.writeFileSync(stateFilePath, JSON.stringify(state, null, 2));
    },
    clear: () => {
        console.log("Clearing State...");
        fs.writeFileSync(stateFilePath, "{}");
    },
};
