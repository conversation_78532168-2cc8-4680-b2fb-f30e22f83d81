"use strict";
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

describe("Tests calculating refills remaining", () => {
    beforeEach(() => {
        delete global.ctx.sharedCache;
    });

    afterEach(() => {
        delete global.ctx.sharedCache;
    });

    it("should calculate refills remaining", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockOrderItem = {
            rx_no: "RX123456123122",
            next_fill_number: 1,
            billing_method: "Pharmacy",
            template_type: "Default",
            start_date: "01/01/2024",
            stop_date: "12/31/2024",
            written_date: "01/01/2024",
            expiration_date: "12/31/2024",
            inventory_id: 123,
            is_340b: null,
            dose: 2,
            dose_unit_id: "each",
            dispense_unit_id: "each",
            unit_comp_cnt: 1,
            refills: 3,
            frequency_id: "3CD/28D",
            route_id: "PO",
            daw_code: "0",
        };

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calcRefillsRemaining(mockOrderItem);

        expect(result).toEqual(3);
    });

    it("should calculate refills allowed with refills order", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockOrderItem = {
            rx_no: "RX123456123122",
            next_fill_number: 1,
            billing_method: "Pharmacy",
            template_type: "Default",
            start_date: "01/01/2024",
            stop_date: "12/31/2024",
            written_date: "01/01/2024",
            expiration_date: "12/31/2024",
            inventory_id: 123,
            is_340b: null,
            dose: 2,
            dose_unit_id: "each",
            dispense_unit_id: "each",
            unit_comp_cnt: 1,
            refills: 3,
            frequency_id: "3CD/28D",
            route_id: "PO",
            daw_code: "0",
        };

        const mockOrder = {
            site_id: 1,
            status_id: "active",
            prescriber_id: 123,
            written_date: "01/01/2024",
            expiration_date: "12/31/2024",
            validated: "Yes",
            dx_ids: [{ id: 1, rank: 1 }],
            day_supply: 28,
            start_date: "01/15/2024",
            void: null,
            subform_items: [mockOrderItem],
            subform_supply: null,
            medical_payer_ids: null,
            pharmacy_payer_ids: [{ id: 1, rank: 1 }],
        };

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calcRefillsAllowed(mockOrderItem);

        expect(result).toEqual(3);
    });

    it("should calculate refills allowed with doses allowed order", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockOrderItem = {
            rx_no: "RX123456123122",
            overrides: ["Dates", ""],
            next_fill_number: 1,
            billing_method: "Pharmacy",
            template_type: "Default",
            start_date: "01/01/2024",
            stop_date: "12/31/2024",
            written_date: "01/01/2024",
            expiration_date: "12/31/2024",
            inventory_id: 123,
            is_340b: null,
            dose: 2,
            dose_unit_id: "each",
            dispense_unit_id: "each",
            unit_comp_cnt: 1,
            doses_allowed: 22,
            frequency_id: "3CD/28D",
            route_id: "PO",
            daw_code: "0",
        };

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calcRefillsAllowed(mockOrderItem);

        expect(result).toEqual(4);
    });
});
