"use strict";
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");
const currency = require("currency.js");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});
const roundToTwoDecimals = (num) => currency(num).value;
const priceMatrixMultiplier = 3.5;
const taxRate = 5.0 + 6.25 + 2.25;
const flatTaxRate = 5.25;

describe("Tests converting delivery ticket item to billable item", () => {
    beforeEach(() => {
        delete global.ctx.sharedCache;
    });

    afterEach(() => {
        delete global.ctx.sharedCache;
    });
    it("should convert a compound item to a billable item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockDeliveryTicket = {
            patient_id: 1234,
            fill_number: 5678,
            site_id: 109,
            service_from: "01/01/2023",
            service_to: "01/31/2023",
            subform_drug: [],
            subform_compound: [],
            subform_supply: [],
            ship_state_id: "TX",
            confirmed: "Yes",
            verified: "Yes",
        };

        const deliveryTicketItem = {
            subform_bill: null,
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: 77,
            billing_unit_id: "gram",
            delay_reason_code: null,
            reviewed_on: null,
            deleted: null,
            archived: null,
            updated_on: null,
            auto_name: null,
            change_type: null,
            change_data: null,
            created_on: "07/08/2024 00:21:34",
            change_on: null,
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            partial_fill: null,
            is_340b: null,
            bill_notes: null,
            work_ticket_notes: null,
            is_reconstituted: null,
            partial_fill_comp: null,
            og_quantity: null,
            og_day_supply: null,
            compounding_instructions: null,
            dispense_unit_id: "gram",
            dispense_no: null,
            template_type: "Compound",
            bill_quantity: 20,
            dispense_quantity: 662,
            charge_quantity: 10,
            print_index: 0,
            bill_index: 0,
            partial_fill_index: null,
            partial_fill_comp_index: null,
            is_340b_index: 0,
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
            parent_archived: null,
            subform_ingred: [
                {
                    inventory_id: 85,
                    dispense_quantity: 12,
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 86,
                    dispense_quantity: 50,
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 87,
                    dispense_quantity: 600,
                    dispense_unit_id: "mL",
                },
            ],
        };

        const item1ExpectedEa = 10.66 * priceMatrixMultiplier;
        const item2ExpectedEa = 0.07 * priceMatrixMultiplier;
        const item3ExpectedEa = 0.21 * priceMatrixMultiplier;

        const mockResults = {
            bill_quantity: 670,
            billing_unit_id: "gram",
            charge_quantity: 67,
            subform_bill: [
                {
                    bill_type: "Dispense",
                    is_compound: "Compound",
                    patient_id: 1430,
                    order_id: 155,
                    order_item_id: 157,
                    insurance_id: 148,
                    pa_id: null,
                    date_of_service: "01/01/2023",
                    charge_quantity: 67,
                    charge_unit: "10 gram",
                    day_supply: 30,
                    subform_ingred: [
                        {
                            //AWP 10.66/ea
                            inventory_id: 85,
                            charge_quantity: 12,
                            charge_unit: "1 gram",
                            expected: roundToTwoDecimals(12 * item1ExpectedEa),
                            expected_ea: roundToTwoDecimals(item1ExpectedEa),
                            calc_expected_ea:
                                roundToTwoDecimals(item1ExpectedEa),
                            list: roundToTwoDecimals(37.31 * 12),
                            list_ea: 37.31,
                            cost: roundToTwoDecimals(0.25 * 12),
                            cost_ea: 0.25,
                            taxable: "Yes",
                            tax_codes_id: "AZTAX",
                            flat_tax_amt: flatTaxRate,
                            sales_tax: 60.44,
                            per_sales_tax_rate_used: taxRate,
                            sales_tax_basis: "03",
                        },
                        {
                            //AWP 0.07/ea
                            inventory_id: 86,
                            charge_quantity: 50,
                            charge_unit: "1 gram",
                            expected: roundToTwoDecimals(50 * item2ExpectedEa),
                            expected_ea: roundToTwoDecimals(item2ExpectedEa),
                            calc_expected_ea:
                                roundToTwoDecimals(item2ExpectedEa),
                            list: roundToTwoDecimals(0.25 * 50),
                            list_ea: 0.25,
                            cost: roundToTwoDecimals(0.33 * 50),
                            cost_ea: 0.33,
                            taxable: "Yes",
                            tax_codes_id: "AZTAX",
                            flat_tax_amt: 5.25,
                            sales_tax: 1.65,
                            per_sales_tax_rate_used: taxRate,
                            sales_tax_basis: "03",
                        },
                        {
                            // AWP 0.21/ea
                            inventory_id: 87,
                            charge_quantity: 60,
                            charge_unit: "10 mL",
                            expected: roundToTwoDecimals(60 * item3ExpectedEa),
                            expected_ea: roundToTwoDecimals(item3ExpectedEa),
                            calc_expected_ea:
                                roundToTwoDecimals(item3ExpectedEa),
                            list: roundToTwoDecimals(0.74 * 60),
                            list_ea: 0.74,
                            cost: roundToTwoDecimals(0.12 * 60),
                            cost_ea: 0.12,
                            taxable: "Yes",
                            tax_codes_id: "AZTAX",
                            flat_tax_amt: flatTaxRate,
                            sales_tax: 5.95,
                            per_sales_tax_rate_used: taxRate,
                            sales_tax_basis: "03",
                        },
                    ],
                    expected: roundToTwoDecimals(
                        12 * item1ExpectedEa +
                            50 * item2ExpectedEa +
                            60 * item3ExpectedEa
                    ),
                    expected_ea: roundToTwoDecimals(
                        (12 * item1ExpectedEa +
                            50 * item2ExpectedEa +
                            60 * item3ExpectedEa) /
                            67
                    ),
                    calc_expected_ea: roundToTwoDecimals(
                        (12 * item1ExpectedEa +
                            50 * item2ExpectedEa +
                            60 * item3ExpectedEa) /
                            67
                    ),
                    cost: roundToTwoDecimals(0.25 * 12 + 0.33 * 50 + 0.12 * 60),
                    cost_ea: roundToTwoDecimals(
                        (0.25 * 12 + 0.33 * 50 + 0.12 * 60) / 67
                    ),
                    list: roundToTwoDecimals(
                        37.31 * 12 + 0.25 * 50 + 0.74 * 60
                    ),
                    list_ea: roundToTwoDecimals(
                        (37.31 * 12 + 0.25 * 50 + 0.74 * 60) / 67
                    ),
                    taxable: "Yes",
                    tax_codes_id: "AZTAX",
                    flat_tax_amt: flatTaxRate,
                    sales_tax: 68.05,
                    per_sales_tax_rate_used: taxRate,
                    sales_tax_basis: "03",
                },
            ],
        };

        addDSLMocks(ctx, "site", true, null, "site");
        addDSLMocks(ctx, "patient", false, null, "kara");
        addDSLMocks(
            ctx,
            "careplan_order",
            false,
            null,
            "order-compound-active"
        );
        addDSLMocks(
            ctx,
            "careplan_order_item",
            false,
            null,
            "compound-order-item"
        );
        addDSLMocks(ctx, "payer", false, null, "payer-pharmacy");
        addDSLMocks(ctx, "list_tax_code", false, null, "AZ-tax-code");

        const BillingConverterClass = require("@api/billing/converter");
        const converter = new BillingConverterClass(nes, ctx);
        const result = await converter.convertDtItemToBillItem(
            mockDeliveryTicket,
            deliveryTicketItem
        );

        expect(result).toEqual(mockResults);
    });

    it("should convert a reconstituted item to a billable item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockDeliveryTicket = {
            patient_id: 1234,
            fill_number: 5678,
            site_id: 109,
            service_from: "01/01/2023",
            service_to: "01/31/2023",
            subform_drug: [],
            subform_compound: [],
            subform_supply: [],
            ship_state_id: "TX",
            confirmed: "Yes",
            verified: "Yes",
        };

        const deliveryTicketItem = {
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: null,
            brand_group_id: 70135,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            dispense_quantity: 500,
            dispense_unit_id: "mL",
            template_type: "Reconstituted",
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
        };

        const itemExpectedEa = 20.34 * priceMatrixMultiplier;
        const itemListEa = 71.19;
        const item1CostEa = 0.22;
        const item2CostEa = 1.23;
        const totalExpected = 5 * itemExpectedEa;
        const totalExpectedEa = totalExpected / 500;
        const totalCost = item1CostEa + 4 * item2CostEa;
        const totalCostEa = totalCost / 500;

        const mockResults = {
            bill_quantity: 500,
            billing_unit_id: "mL",
            charge_quantity: 500,
            subform_bill: [
                {
                    bill_type: "Dispense",
                    is_compound: "Reconstituted",
                    patient_id: 1667,
                    order_id: 155,
                    order_item_id: 157,
                    insurance_id: 182,
                    pa_id: null,
                    date_of_service: "01/01/2023",
                    charge_quantity: 500,
                    charge_unit: "1 mL",
                    day_supply: 30,
                    expected: roundToTwoDecimals(totalExpected),
                    calc_expected_ea: roundToTwoDecimals(totalExpectedEa),
                    expected_ea: roundToTwoDecimals(totalExpectedEa),
                    list: roundToTwoDecimals(totalExpected),
                    list_ea: roundToTwoDecimals(totalExpectedEa),
                    cost: roundToTwoDecimals(totalCost),
                    cost_ea: roundToTwoDecimals(totalCostEa),
                    subform_ingred: [
                        {
                            // AWP: 20.34/ea, cost 0.22/ea, list 71.19/ea
                            inventory_id: 89,
                            charge_quantity: 1,
                            expected: roundToTwoDecimals(itemExpectedEa),
                            calc_expected_ea:
                                roundToTwoDecimals(itemExpectedEa),
                            expected_ea: roundToTwoDecimals(itemExpectedEa),
                            list: roundToTwoDecimals(itemListEa),
                            list_ea: roundToTwoDecimals(itemListEa),
                            cost: roundToTwoDecimals(item1CostEa),
                            cost_ea: roundToTwoDecimals(item1CostEa),
                            charge_unit: "300 mL",
                            taxable: null,
                        },
                        {
                            // AWP: 20.34/ea, cost 1.23/ea, list 71.19/ea
                            inventory_id: 90,
                            charge_quantity: 4,
                            expected: roundToTwoDecimals(itemExpectedEa * 4),
                            calc_expected_ea:
                                roundToTwoDecimals(itemExpectedEa),
                            expected_ea: roundToTwoDecimals(itemExpectedEa),
                            list: roundToTwoDecimals(itemListEa * 4),
                            list_ea: roundToTwoDecimals(itemListEa),
                            cost: roundToTwoDecimals(item2CostEa * 4),
                            cost_ea: roundToTwoDecimals(item2CostEa),
                            charge_unit: "50 mL",
                            taxable: null,
                        },
                    ],
                },
            ],
        };

        addDSLMocks(ctx, "site", true, null, "site");
        addDSLMocks(ctx, "patient", false, null, "kara");
        addDSLMocks(ctx, "list_brd_grp", false, null, "gammagard");
        addDSLMocks(
            ctx,
            "inventory",
            false,
            null,
            "gammagard-5g",
            "gammagard-30g"
        );
        addDSLMocks(
            ctx,
            "careplan_order",
            false,
            null,
            "order-ig-active-reconstituted"
        );
        addDSLMocks(
            ctx,
            "careplan_order_item",
            false,
            null,
            "reconstituted-order-item"
        );
        addDSLMocks(
            ctx,
            "patient_insurance",
            false,
            null,
            "insurance-pharmacy"
        );
        addDSLMocks(ctx, "payer", false, null, "payer-pharmacy");
        addDSLMocks(ctx, "list_tax_code", false, null, "AZ-tax-code");

        const BillingConverterClass = require("@api/billing/converter");
        const converter = new BillingConverterClass(nes, ctx);
        const result = await converter.convertDtItemToBillItem(
            mockDeliveryTicket,
            deliveryTicketItem
        );

        expect(result).toEqual(mockResults);
    });

    it("should convert a non-compound/non-reconstituted item to a billable item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockDeliveryTicket = {
            patient_id: 1234,
            fill_number: 5678,
            site_id: 109,
            service_from: "01/01/2023",
            service_to: "01/31/2023",
            subform_drug: [],
            subform_compound: [],
            subform_supply: [],
            ship_state_id: "TX",
            confirmed: "Yes",
            verified: "Yes",
        };

        const deliveryTicketItem = {
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: 80,
            billing_unit_id: "each",
            created_on: "07/08/2024 00:21:34",
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            dispense_quantity: 50,
            dispense_unit_id: "each",
            template_type: "Default",
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
        };

        // List: 8.75, AWP: 2.50 Cost: 0.15
        const itemExpectedEa = 2.5 * priceMatrixMultiplier;
        const totalExpected = 50 * itemExpectedEa;
        const itemCostEa = 0.15;
        const totalCost = itemCostEa * 50;

        const mockResults = {
            bill_quantity: 50,
            billing_unit_id: "each",
            charge_quantity: 50,
            subform_bill: [
                {
                    bill_type: "Dispense",
                    is_compound: null,
                    patient_id: 1667,
                    order_id: 155,
                    order_item_id: 157,
                    insurance_id: 182,
                    pa_id: null,
                    date_of_service: "01/01/2023",
                    charge_quantity: 50,
                    charge_unit: "1 each",
                    day_supply: 30,
                    expected: roundToTwoDecimals(totalExpected),
                    calc_expected_ea: roundToTwoDecimals(itemExpectedEa),
                    expected_ea: roundToTwoDecimals(itemExpectedEa),
                    list: roundToTwoDecimals(totalExpected),
                    list_ea: roundToTwoDecimals(itemExpectedEa),
                    cost: roundToTwoDecimals(totalCost),
                    cost_ea: roundToTwoDecimals(itemCostEa),
                    subform_ingred: null,
                },
            ],
        };

        addDSLMocks(ctx, "site", true, null, "site");
        addDSLMocks(ctx, "patient", false, null, "kara");
        addDSLMocks(ctx, "careplan_order", false, null, "order-benadryl");
        addDSLMocks(ctx, "careplan_order_item", false, null, "benadryl");
        addDSLMocks(
            ctx,
            "patient_insurance",
            false,
            null,
            "insurance-pharmacy"
        );
        addDSLMocks(ctx, "payer", false, null, "payer-pharmacy");
        addDSLMocks(ctx, "list_tax_code", false, null, "AZ-tax-code");

        const BillingConverterClass = require("@api/billing/converter");
        const converter = new BillingConverterClass(nes, ctx);
        const result = await converter.convertDtItemToBillItem(
            mockDeliveryTicket,
            deliveryTicketItem
        );

        expect(result).toEqual(mockResults);
    });
});
