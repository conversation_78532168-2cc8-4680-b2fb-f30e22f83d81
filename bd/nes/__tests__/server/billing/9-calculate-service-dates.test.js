"use strict";
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

describe("Tests calculating service dates", () => {
    beforeEach(() => {
        delete global.ctx.sharedCache;
    });

    afterEach(() => {
        delete global.ctx.sharedCache;
    });

    it("should calculate service date from pre-existing delivery ticket", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        addDSLMocks(ctx, "careplan_order", true, null, "order-benadryl");
        addDSLMocks(
            ctx,
            "careplan_delivery_tick",
            false,
            null,
            "careplan_delivery_tick"
        );

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calculateServiceDates(1234);

        const expectedResult = {
            serviceFrom: "07/08/2024",
            serviceTo: "08/07/2024",
            promiseDate: "07/07/2024",
        };
        expect(result).toEqual(expectedResult);
    });

    it("should calculate service date from fresh order", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        addDSLMocks(ctx, "careplan_order", true, null, "order-benadryl");

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calculateServiceDates(343433);

        const expectedResult = {
            serviceFrom: "07/21/2024",
            serviceTo: "08/20/2024",
            promiseDate: "07/20/2024",
        };
        expect(result).toEqual(expectedResult);
    });
});
