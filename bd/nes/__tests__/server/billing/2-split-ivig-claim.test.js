"use strict";
const SECONDS = 1000;
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

it(
    "Checks splitting a reconstituted claim",
    async () => {
        const billables = [
            {
                patient_id: 1430,
                order_id: 155,
                order_item_id: 157,
                insurance_id: 1234,
                delivery_tick_id: null,
                date_of_service: "07/06/2024",
                charge_quantity: 500,
                billing_unit_id: "mL",
                day_supply: 30,
                is_compound: "Reconstituted",
                ready_to_bill: "Yes",
                bill_type: "Dispense",
                expected: 200.12,
                calc_expected_ea: 6.67,
                expected_ea: 6.67,
                list: 200.12,
                route_id: "IV",
                written_date: "07/06/2024",
                fill_number: 1,
                list_ea: 6.67,
                refills: 1,
                subform_ingred: [
                    {
                        inventory_id: 89,
                        charge_quantity: 10,
                        expected: 12313.12,
                        calc_expected_ea: 1231.31,
                        expected_ea: 1231.31,
                        list: 12313.12,
                        list_ea: 1231.31,
                        charge_unit: "mL",
                    },
                    {
                        inventory_id: 90,
                        charge_quantity: 1,
                        expected: 23244.34,
                        calc_expected_ea: 23244.67,
                        expected_ea: 23244.67,
                        list: 23244.12,
                        list_ea: 23244.67,
                        charge_unit: "mL",
                    },
                ],
            },
        ];
        const nes = global.nes;
        const ctx = global.ctx;

        addDSLMocks(ctx, "list_brd_grp", true, null, "gammagard");
        addDSLMocks(
            ctx,
            "inventory",
            false,
            null,
            "gammagard-5g",
            "gammagard-30g"
        );
        addDSLMocks(
            ctx,
            "careplan_order",
            false,
            null,
            "order-ig-active-reconstituted"
        );
        addDSLMocks(
            ctx,
            "careplan_order_item",
            false,
            null,
            "reconstituted-order-item"
        );
        addDSLMocks(
            ctx,
            "patient_insurance",
            false,
            null,
            "insurance-pharmacy"
        );
        addDSLMocks(
            ctx,
            "payer",
            false,
            null,
            "payer-pharmacy-split-reconstituted"
        );

        const NCPDPBuilderClass = require("@api/billing/pharmacy/builder");
        const builder = new NCPDPBuilderClass(nes, ctx);
        const claims = await builder.buildInvoices(billables, null, true);
        expect(claims.length).toEqual(2);
    },
    99999 * SECONDS
);
