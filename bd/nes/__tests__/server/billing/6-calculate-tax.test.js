"use strict";
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

describe("Tests calculating tax", () => {
    beforeEach(() => {
        delete global.ctx.sharedCache;
    });

    afterEach(() => {
        delete global.ctx.sharedCache;
    });
    it("should test calculating normal sales tax based on delivery ticket state tax", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockSite = {
            auto_name: "<PERSON><PERSON><PERSON> – HB Branson – HB",
            id: 13,
            name: "<PERSON><PERSON><PERSON> – HB",
            code: "<PERSON><PERSON>son – HB",
            state_id: "MO",
            npi: "**********",
            tax_code_id: "KSTAX",
            address: "255 NW Victoria Dr, Suite B",
            city: "Lee's Summit",
            zip: "64086",
            phone: "(*************",
            fax: "(*************",
            tax_id: "23-3432321",
            tso: "12312446",
            site_id: "12312446",
            timezone_id: "America/Chicago",
            submit_medicare_claim: "Yes",
            ncpdp_id: "1232221",
            pic_id: 5,
            tax_codes_id: ["KSTAX", "TXTAX", "AZTAX", "NMTAX"],
            tax_codes_id_auto_name: [
                "KSTAX - KS Sales Tax",
                "TXTAX - TX Sales Tax",
                "AZTAX - AZ Sales Tax",
                "NMTAX - NM Sales Tax",
            ],
        };

        const mockPayer = {
            id: 123,
            organization: "BCBS",
            type_id: "CMPBM",
            billing_method_id: "ncpdp",
            bin: "334323",
            pcn: "**********",
            never_comp_seg: null,
            auto_split_noncompound: null,
            ncpdp_sec_claims_ingred_cost: null,
            prescriber_no_field_id: "01",
            base_ing_cost_on_id: "00",
            compound_qualifier_id: "03",
            calc_perc_sales_tax: "Yes",
            noncompound_drug_qualifier_id: "03",
            default_tax_base_id: "03", //Ingredients + dispense fee
            ncpdp_send_primary_payer_amt_paid: "Yes",
            ncpdp_sec_claims_gross_amt: "Send Co-pay Amount",
            site_id: [109, 13],
        };

        const mockPatient = {
            id: 1430,
            home_zip: "78628",
            home_street: "105 Rosadi Cv",
            home_city: "Georgetown",
            firstname: "Kara",
            lastname: "Whiteside",
            dob: "10/11/1952",
            phone_cell: "(*************",
            gender: "Female",
            referral_date: "04/18/2024",
            language: "English",
            ship_city: "Houston",
            ship_zip: "77079",
            ship_street: "376 East Liberty Ave.",
            site_id: 109,
            home_state_id: "KS",
            ship_state_id: "TX",
            pmp_benefits_optout: "Opt-In",
            mrn: "11111",
            identify_gender: "Same as birth",
            search: "'11111':3 'kara':1A,4 'whiteside':2B,5",
            status_id: "1",
            status_id_auto_name: "1 - Pending",
            site_id_auto_name: "Branson - TX BTX",
            home_state_id_auto_name: "TX - Texas",
            ship_state_id_auto_name: "TX - Texas",
        };

        const mockDeliveryTicket = {
            patient_id: 1234,
            fill_number: 5678,
            site_id: 109,
            service_from: "01/01/2023",
            service_to: "01/31/2023",
            subform_drug: [],
            subform_compound: [],
            subform_supply: [],
            ship_state_id: "TX",
            confirmed: "Yes",
            verified: "Yes",
        };

        const dispenseFee = 5.0;
        const expectedAmt = 1232.43;
        const taxPerc = 15.9;
        const taxAmount = (expectedAmt + dispenseFee) * (taxPerc / 100.0);
        const mockResult = {
            tax_codes_id: "TXTAX",
            per_sales_tax_rate_used: taxPerc,
            flat_tax_amt: 0.0,
            sales_tax: Number(taxAmount.toFixed(2)),
            sales_tax_basis: "03",
            taxable: "Yes",
        };

        addDSLMocks(ctx, "list_tax_code", true, null, "TX-tax-code");

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result = await calculator.calcSalesTaxTotal(
            mockSite,
            mockPayer,
            mockPatient,
            expectedAmt,
            dispenseFee,
            mockDeliveryTicket
        );

        expect(result).toEqual(mockResult);
    });

    it("should test calculating normal sales tax based on patient state tax", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const mockSite = {
            auto_name: "Branson – HB Branson – HB",
            id: 13,
            name: "Branson – HB",
            code: "Branson – HB",
            state_id: "MO",
            npi: "**********",
            tax_code_id: "KSTAX",
            address: "255 NW Victoria Dr, Suite B",
            city: "Lee's Summit",
            zip: "64086",
            phone: "(*************",
            fax: "(*************",
            tax_id: "23-3432321",
            tso: "12312446",
            site_id: "12312446",
            timezone_id: "America/Chicago",
            submit_medicare_claim: "Yes",
            ncpdp_id: "1232221",
            pic_id: 5,
            tax_codes_id: ["KSTAX", "TXTAX", "AZTAX", "NMTAX"],
            tax_codes_id_auto_name: [
                "KSTAX - KS Sales Tax",
                "TXTAX - TX Sales Tax",
                "AZTAX - AZ Sales Tax",
                "NMTAX - NM Sales Tax",
            ],
        };

        const mockPayer = {
            id: 123,
            organization: "BCBS",
            type_id: "CMPBM",
            billing_method_id: "ncpdp",
            bin: "334323",
            pcn: "**********",
            never_comp_seg: null,
            auto_split_noncompound: null,
            ncpdp_sec_claims_ingred_cost: null,
            prescriber_no_field_id: "01",
            base_ing_cost_on_id: "00",
            compound_qualifier_id: "03",
            calc_perc_sales_tax: "Yes",
            noncompound_drug_qualifier_id: "03",
            default_tax_base_id: "02", //Ingredients
            ncpdp_send_primary_payer_amt_paid: "Yes",
            ncpdp_sec_claims_gross_amt: "Send Co-pay Amount",
            site_id: [109, 13],
        };

        const mockPatient = {
            id: 1430,
            home_zip: "78628",
            home_street: "105 Rosadi Cv",
            home_city: "Georgetown",
            firstname: "Kara",
            lastname: "Whiteside",
            dob: "10/11/1952",
            phone_cell: "(*************",
            gender: "Female",
            referral_date: "04/18/2024",
            language: "English",
            ship_city: "Houston",
            ship_zip: "77079",
            ship_street: "376 East Liberty Ave.",
            site_id: 109,
            home_state_id: "AZ",
            ship_state_id: null,
            pmp_benefits_optout: "Opt-In",
            mrn: "11111",
            identify_gender: "Same as birth",
            search: "'11111':3 'kara':1A,4 'whiteside':2B,5",
            status_id: "1",
            status_id_auto_name: "1 - Pending",
            site_id_auto_name: "Branson - TX BTX",
            home_state_id_auto_name: "TX - Texas",
            ship_state_id_auto_name: "TX - Texas",
        };

        const dispenseFee = 5.0;
        const expectedAmt = 1232.43;
        const taxPerc = 13.5;
        const taxAmount = expectedAmt * (taxPerc / 100.0);
        const mockResult = {
            tax_codes_id: "AZTAX",
            per_sales_tax_rate_used: taxPerc,
            flat_tax_amt: 5.25,
            sales_tax: Number(taxAmount.toFixed(2)),
            sales_tax_basis: "02",
            taxable: "Yes",
        };

        addDSLMocks(ctx, "list_tax_code", true, null, "AZ-tax-code");

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result = await calculator.calcSalesTaxTotal(
            mockSite,
            mockPayer,
            mockPatient,
            expectedAmt,
            dispenseFee
        );

        expect(result).toEqual(mockResult);
    });
});
