"use strict";
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

describe("Tests calculating charge quantity", () => {
    beforeEach(() => {
        delete global.ctx.sharedCache;
    });

    afterEach(() => {
        delete global.ctx.sharedCache;
    });
    it("should test calculating charge quantity for from billing quantity through inventory item and bill quantity", async () => {
        const nes = global.nes;
        const ctx = global.ctx;
        // Dispense 100g of 50ml vials
        // 10% solution is 1000ml
        // 100g is (1000/50) = 20 vials
        const mockInventoryItem = {
            id: 1,
            taxable: "Yes",
            billing_unit_id: "mL",
            dosing_unit_id: ["mL", "gram"],
            default_dosing_unit_id: "mL",
            dispense_unit_id: "gram",
            dispense_cnv_id: 1,
            dosing_cnv_ids: [1, 2],
            dea_schedule_id: null,
            sp_pk_indicator: null,
            storage_id: "refrigerated",
            quantity_each: 50,
        };

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calcChargeQuantityFromBillQuantity(
            mockInventoryItem,
            100
        );

        expect(result).toEqual(2);
    });

    it("should test converting billing quantity to charge quantity through billing item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const billingItem = {
            subform_bill: [],
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: 40,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            change_on: null,
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            is_340b: "Yes",
            dispense_unit_id: "mL",
            template_type: "Default",
            dispense_quantity: 400,
            charge_quantity: 10,
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
            bill_quantity: 600,
        };

        addDSLMocks(ctx, "inventory", true, null, "gammagard-30g");
        const mockResult = {
            quantityToCharge: 2,
            billingUnitId: "mL",
            inventoryItems: null,
        };

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result = await calculator.calculateChargeQuantity(billingItem);

        expect(result).toEqual(mockResult);
    });

    it("should test converting billing quantity to charge quantity through billing item for brand group item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const billingItem = {
            _meta: {
                source: "careplan_dt_drug",
                subform: {
                    subform_bill: "ledger_billable",
                },
            },
            subform_bill: null,
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: null,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            change_on: null,
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            is_340b: "Yes",
            brand_group_id: 454,
            dispense_unit_id: "mL",
            template_type: "Reconstituted",
            bill_quantity: 400,
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
        };

        addDSLMocks(ctx, "list_brd_grp", true, null, "gammagard");
        const mockResult = {
            quantityToCharge: 400,
            billingUnitId: "mL",
            inventoryItems: null,
        };

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result = await calculator.calculateChargeQuantity(billingItem);

        expect(result).toEqual(mockResult);
    });

    it("should test converting bill quantity to charge quantity through billing item for compounded item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const billingItem = {
            subform_bill: null,
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: 77,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            dispense_unit_id: "gram",
            dispense_no: null,
            template_type: "Compound",
            bill_quantity: 670,
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
            subform_ingred: [
                {
                    inventory_id: 85,
                    bill_quantity: 12,
                    dispense_quantity: 12,
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 86,
                    bill_quantity: 50,
                    dispense_quantity: 50,
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 87,
                    bill_quantity: 600,
                    dispense_quantity: 600,
                    dispense_unit_id: "mL",
                },
            ],
        };
        const mockResult = {
            quantityToCharge: 67,
            billingUnitId: "gram",
            inventoryItems: [
                {
                    inventory_id: 85,
                    bill_quantity: 12,
                    dispense_quantity: 12,
                    charge_quantity: 12,
                    billing_unit_id: "gram",
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 86,
                    bill_quantity: 50,
                    dispense_quantity: 50,
                    charge_quantity: 50,
                    billing_unit_id: "gram",
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 87,
                    bill_quantity: 600,
                    dispense_quantity: 600,
                    charge_quantity: 60,
                    billing_unit_id: "mL",
                    dispense_unit_id: "mL",
                },
            ],
        };

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result = await calculator.calculateChargeQuantity(billingItem);

        expect(result).toEqual(mockResult);
    });
});
