"use strict";
const SECONDS = 1000;
const { redirectLogs, restoreLogs, addDSLMocks } = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

it(
    "Checks splitting a compound claim",
    async () => {
        const billables = [
            {
                patient_id: 1430,
                order_id: 155,
                order_item_id: 157,
                insurance_id: 1234,
                delivery_tick_id: null,
                date_of_service: "07/06/2024",
                charge_quantity: 30,
                billing_unit_id: "mL",
                day_supply: 30,
                is_compound: "Compound",
                ready_to_bill: "Yes",
                bill_type: "Dispense",
                expected: 200.12,
                calc_expected_ea: 6.67,
                expected_ea: 6.67,
                list: 200.12,
                route_id: "TOP",
                written_date: "07/06/2024",
                fill_number: 1,
                list_ea: 6.67,
                refills: 1,
                subform_ingred: [
                    {
                        inventory_id: 85,
                        charge_quantity: 10,
                        expected: 122.12,
                        calc_expected_ea: 6.67,
                        expected_ea: 6.67,
                        list: 200.12,
                        list_ea: 6.67,
                        charge_unit: "mL",
                    },
                    {
                        inventory_id: 86,
                        charge_quantity: 23,
                        expected: 21.34,
                        calc_expected_ea: 6.67,
                        expected_ea: 6.67,
                        list: 200.12,
                        list_ea: 6.67,
                        charge_unit: "mL",
                    },
                    {
                        inventory_id: 87,
                        charge_quantity: 9,
                        expected: 65.32,
                        calc_expected_ea: 6.67,
                        expected_ea: 6.67,
                        list: 200.12,
                        list_ea: 6.67,
                        charge_unit: "mL",
                    },
                ],
            },
        ];
        const nes = global.nes;
        const ctx = global.ctx;

        addDSLMocks(ctx, "inventory", true, null, "pain-compound");
        addDSLMocks(
            ctx,
            "careplan_order",
            false,
            null,
            "order-compound-active"
        );
        addDSLMocks(
            ctx,
            "careplan_order_item",
            false,
            null,
            "compound-order-item"
        );
        addDSLMocks(
            ctx,
            "patient_insurance",
            false,
            null,
            "insurance-pharmacy"
        );
        addDSLMocks(ctx, "payer", false, null, "payer-pharmacy-split-compound");

        const NCPDPBuilderClass = require("@api/billing/pharmacy/builder");
        const builder = new NCPDPBuilderClass(nes, ctx);
        const claims = await builder.buildInvoices(billables, null, true);
        expect(claims.length).toEqual(3);
    },
    99999 * SECONDS
);
