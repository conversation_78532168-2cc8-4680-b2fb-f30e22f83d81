"use strict";
const {
    redirectLogs,
    restoreLogs,
    addDSLMocks,
    clearDSLMocks,
} = require("../setup");

let arrayTransport;
beforeAll(() => {
    ({ arrayTransport } = redirectLogs());
});

afterAll(() => {
    restoreLogs(arrayTransport);
});

describe("Tests calculating billing quantity", () => {
    beforeEach(() => {
        delete global.ctx.sharedCache;
    });

    afterEach(() => {
        delete global.ctx.sharedCache;
    });
    it("should test calculating billing quantity for from dispense quantity through prescription", async () => {
        const nes = global.nes;
        const ctx = global.ctx;
        // Dispense 100g of 50ml vials
        // 10% solution is 1000ml
        // 100g is (1000/50) = 20 vials
        const mockInventoryItem = {
            id: 1,
            taxable: "Yes",
            billing_unit_id: "mL",
            dosing_unit_id: ["mL", "gram"],
            default_dosing_unit_id: "mL",
            dispense_unit_id: "gram",
            dispense_cnv_id: 1,
            dosing_cnv_ids: [1, 2],
            dea_schedule_id: null,
            sp_pk_indicator: null,
            storage_id: "refrigerated",
            quantity_each: 50,
        };

        addDSLMocks(ctx, "list_unit_conversion", false, null, "ivig-10-perc");

        const DispenseCalculatorClass = require("@api/dispense/calculator");
        const calculator = new DispenseCalculatorClass(nes, ctx);
        const result = await calculator.calcBillQuantityFromDispenseQuantity(
            mockInventoryItem,
            100
        );

        expect(result).toEqual(1000);
    });

    it("should test converting billing unit to dispense unit through delivery ticket item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const deliveryTicketItem = {
            subform_bill: [],
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: 40,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            is_340b: "Yes",
            dispense_unit_id: "mL",
            dispense_no: null,
            template_type: "Default",
            bill_quantity: 20,
            dispense_quantity: 400,
            charge_quantity: 10,
            order_id_auto_name:
                "07/04/2024 - 5 - Active-30 - Pending Billing undefined GAMMAGARD LIQUID 1X - 5 - Active",
            order_item_id_auto_name:
                "GAMMAGARD LIQUID 10% VIAL Drug 30 1X One Time Only R:5 RR:5",
            brand_group_id_auto_name: null,
            inventory_id_auto_name: "GAMMAGARD LIQUID 10% VIAL Drug",
            dispense_unit_id_auto_name: "mL",
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
            parent_archived: null,
        };

        addDSLMocks(ctx, "inventory", true, null, "gammagard-30g");
        addDSLMocks(ctx, "list_unit_conversion", false, null, "ivig-10-perc");
        const mockResult = {
            quantityToBill: 600,
            billingUnitId: "mL",
            inventoryItems: null,
        };

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result =
            await calculator.calculateBillQuantity(deliveryTicketItem);

        expect(result).toEqual(mockResult);
    });

    it("should test converting billing unit to dispense unit through delivery ticket item for brand group item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const deliveryTicketItem = {
            subform_bill: null,
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: null,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            change_on: null,
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            is_340b: "Yes",
            brand_group_id: 454,
            dispense_unit_id: "mL",
            template_type: "Reconstituted",
            bill_quantity: 20,
            dispense_quantity: 400,
            charge_quantity: 10,
            order_id_auto_name:
                "07/04/2024 - 5 - Active-30 - Pending Billing undefined GAMMAGARD LIQUID 1X - 5 - Active",
            order_item_id_auto_name:
                "GAMMAGARD LIQUID 10% VIAL Drug 30 1X One Time Only R:5 RR:5",
            brand_group_id_auto_name: null,
            inventory_id_auto_name: "GAMMAGARD LIQUID 10% VIAL Drug",
            dispense_unit_id_auto_name: "mL",
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
            parent_archived: null,
        };

        addDSLMocks(ctx, "list_brd_grp", true, null, "gammagard");
        const mockResult = {
            quantityToBill: 400,
            billingUnitId: "mL",
            inventoryItems: null,
        };

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result =
            await calculator.calculateBillQuantity(deliveryTicketItem);

        expect(result).toEqual(mockResult);
    });

    it("should test converting billing unit to dispense unit through delivery ticket item for compounded item", async () => {
        const nes = global.nes;
        const ctx = global.ctx;

        const deliveryTicketItem = {
            _meta: {
                source: "careplan_dt_drug",
                subform: {
                    subform_bill: "ledger_billable",
                },
            },
            subform_bill: null,
            id: 2,
            created_by: 22,
            order_id: 155,
            order_item_id: 157,
            inventory_id: 77,
            billing_unit_id: "mL",
            created_on: "07/08/2024 00:21:34",
            print: "Yes",
            bill: "Yes",
            rx_no: "10000000000000015",
            fill_number: 1,
            day_supply: 30,
            dispense_unit_id: "gram",
            dispense_no: null,
            template_type: "Compound",
            bill_quantity: 20,
            dispense_quantity: 662,
            charge_quantity: 10,
            order_id_auto_name:
                "07/04/2024 - 5 - Active-30 - Pending Billing undefined GAMMAGARD LIQUID 1X - 5 - Active",
            order_item_id_auto_name:
                "GAMMAGARD LIQUID 10% VIAL Drug 30 1X One Time Only R:5 RR:5",
            inventory_id_auto_name: "GAMMAGARD LIQUID 10% VIAL Drug",
            dispense_unit_id_auto_name: "gram",
            parent_id: 27,
            parent_form: "careplan_delivery_tick",
            parent_archived: null,
            subform_ingred: [
                {
                    inventory_id: 85,
                    dispense_quantity: 12,
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 86,
                    dispense_quantity: 50,
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 87,
                    dispense_quantity: 600,
                    dispense_unit_id: "mL",
                },
            ],
        };

        const mockResult = {
            quantityToBill: 670,
            billingUnitId: "gram",
            inventoryItems: [
                {
                    inventory_id: 85,
                    dispense_quantity: 12,
                    bill_quantity: 12,
                    billing_unit_id: "gram",
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 86,
                    dispense_quantity: 50,
                    bill_quantity: 50,
                    billing_unit_id: "gram",
                    dispense_unit_id: "gram",
                },
                {
                    inventory_id: 87,
                    dispense_quantity: 600,
                    bill_quantity: 600,
                    billing_unit_id: "mL",
                    dispense_unit_id: "mL",
                },
            ],
        };

        const BillingCalculatorClass = require("@api/billing/calculator");
        const calculator = new BillingCalculatorClass(nes, ctx);
        const result =
            await calculator.calculateBillQuantity(deliveryTicketItem);

        expect(result).toEqual(mockResult);
    });
});
