"use strict";
const sharedState = require("@tests/sharedState");
const { startArrayTransport, removeArrayTransport } = require("@core/logger");

/**
 * @function redirectLogs
 * @description Redirects logs to an array transport for capturing during tests
 * @returns {Object} An object containing the arrayTransport
 */
function redirectLogs() {
    console.log("Redirect logs...");
    const arrayTransport = startArrayTransport();
    console.log("Redirected logs...");
    return { arrayTransport };
}

/**
 * @function restoreLogs
 * @description Restores the original logging configuration and captures the logs
 * @param {Object} arrayTransport - The array transport object used for capturing logs
 */
function restoreLogs(arrayTransport) {
    console.log("Restoring logs...");
    const capturedLogs = arrayTransport.getLogs();

    removeArrayTransport(arrayTransport);
    console.log("Restored logs...");

    sharedState.set("logs", capturedLogs);
    console.log(`Restored logs... ${capturedLogs.length} logs captured`);
}

/**
 * @function addDSLMocks
 * @description Adds DSL mocks to the request
 * @param {object} ctx - The context object
 * @param {string} form_name - The name of the DSL form
 * @param {boolean} clear_mocks - Whether to clear the mocks before adding them
 * @param {array} respect_filters - Filters to respect in the request as an array of field names
 * @param {string} filenames - The names of the files to return in the path
 * @returns {object} - The request object so you can chain it
 */
function addDSLMocks(
    ctx,
    form_name,
    clear_mocks,
    respect_filters,
    ...filenames
) {
    if (!form_name) throw new Error("Form name is required");
    if (!filenames.length)
        throw new Error("At least one mock filename is required");
    if (clear_mocks) ctx.dsl_mocks = [];
    if (!ctx.dsl_mocks) ctx.dsl_mocks = [];

    if (ctx.dsl_mocks.some((mock) => mock.form_name === form_name)) {
        console.log(`DSL Mock with the form name ${form_name} already exists`);
        return;
    }

    let dsl_mock = { form_name, filenames };
    if (respect_filters) {
        dsl_mock = { form_name, filenames, respect_filters };
    }

    ctx.dsl_mocks.push(dsl_mock);
    return ctx;
}

function clearDSLMocks(ctx) {
    ctx.dsl_mocks = [];
    return ctx;
}

module.exports = { redirectLogs, restoreLogs, addDSLMocks, clearDSLMocks };
