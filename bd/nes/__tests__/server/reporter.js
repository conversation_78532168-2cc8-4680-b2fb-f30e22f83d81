"use strict";
const DefaultReporter = require("@jest/reporters").DefaultReporter;
const sharedState = require("@tests/sharedState");
const Table = require("cli-table3");
const _ = require("lodash");

class CustomReporter extends DefaultReporter {
    constructor(globalConfig, options) {
        super(globalConfig);
        this.globalConfig = globalConfig;
        this.options = options;
    }

    onTestResult(test, testResult, aggregatedResults) {
        const resultsObj = sharedState.get().results;
        if (!resultsObj) {
            super.onTestResult(test, testResult, aggregatedResults);
        }
    }

    onRunComplete(contexts, results) {
        // Check if we have global results from the server
        const resultsObj = sharedState.get().results;
        if (resultsObj) {
            const { results, capturedLogs } = resultsObj;
            global.testsSummary = _.pick(results, [
                "numFailedTestSuites",
                "numFailedTests",
                "numPassedTestSuites",
                "numPassedTests",
                "numPendingTestSuites",
                "numPendingTests",
                "numRuntimeErrorTestSuites",
                "numTodoTests",
                "numTotalTestSuites",
                "numTotalTests",
            ]);
            global.success = results.success;
            global.coverageData = results.coverageMap;
            global.testResults = results.testResults;
            global.capturedLogs = capturedLogs;
            if (global.coverageData && global.success !== undefined) {
                this.printServerResults();
            }
        } else {
            super.onRunComplete(contexts, results);
        }
    }

    printServerResults() {
        console.log(this.color("\nServer-Side Test Results:\n", "bold"));

        if (global.success) {
            console.log(
                this.color(
                    `✓ All ${global.testsSummary.numTotalTests} tests passed\n`,
                    "green"
                )
            );
        } else {
            console.log(
                this.color(
                    `✗ ${global.testsSummary.numFailedTests} failed out of ${global.testsSummary.numTotalTests} tests\n`,
                    "red"
                )
            );
        }

        if (global.coverageData) {
            this.printCoverage(global.coverageData);
        }

        if (global.capturedLogs && !global.success) {
            console.log(this.color("\nCaptured Logs:\n", "bold"));
            global.capturedLogs.forEach((log) => {
                if (!log?.level) {
                    console.log(log);
                    return;
                }
                if (log?.level === "error") {
                    log.message = this.color(log.message, "red");
                    log.level = this.color(log.level, "red");
                } else if (log?.level === "warn") {
                    log.message = this.color(log.message, "yellow");
                    log.level = this.color(log.level, "yellow");
                }
                console.log(`${log.level}: ${log.message}`);
            });
        }

        if (global.testResults) {
            this.printTestResults(global.testResults);
        }
    }

    printStandardResults(results) {
        console.log(this.color(`\nTest Results:\n${results}`, "bold"));
        return;
    }

    printTestResults(testResults) {
        for (const fileTestResult of testResults) {
            const fileNameWithPath = fileTestResult.testFilePath;
            const passedTest = fileTestResult.numPassingTests;
            const failedTest = fileTestResult.numFailingTests;
            const fileName = fileNameWithPath.split("/").pop();
            console.log(this.color(`${fileName}`, "bold"));
            console.log(
                this.color(`${passedTest} passed`, "green") +
                    " " +
                    this.color(`${failedTest} failed`, "red")
            );
            for (const testResult of fileTestResult.testResults) {
                console.log(this.color(`${testResult.title}`, "bold"));
                if (testResult.status === "failed") {
                    console.log(this.color(`${testResult.status}`, "red"));
                } else {
                    console.log(this.color(`${testResult.status}`, "green"));
                }
                for (const failure of testResult.failureMessages) {
                    console.log(this.color(`${failure}`, "red"));
                }
            }
        }
    }

    printCoverage(coverageData) {
        // Remove this section as it's redundant and not using the actual test results
        // The overall test results are already printed in the printServerResults method
        console.log(this.color("Coverage Summary:", "bold"));

        const table = new Table({
            head: [
                this.color("File", "blue"),
                this.color("Statements", "blue"),
                this.color("Functions", "blue"),
                this.color("Branches", "blue"),
            ],
            colWidths: [28, 18, 18, 18],
        });

        const formatCoverage = (coverage) => {
            const percentage = coverage.percentage.toFixed(2) + "%";
            return this.color(
                percentage.padEnd(7),
                this.getCoverageColor(coverage.percentage)
            );
        };
        // Data rows
        Object.entries(coverageData).forEach(([file, fileCoverage]) => {
            const parts = file.split("/");
            const fileName = `${parts[parts.length - 2]}/${parts[parts.length - 1]}`;
            const { s, f, b } = fileCoverage;
            const statementCoverage = this.calculateCoverage(s);
            const functionCoverage = this.calculateCoverage(f);
            const branchCoverage = this.calculateCoverage(b);
            table.push({
                [fileName]: [
                    formatCoverage(statementCoverage),
                    formatCoverage(functionCoverage),
                    formatCoverage(branchCoverage),
                ],
            });
        });

        // Reset font style
        console.log(table.toString());
    }

    getCoverageColor(percentage) {
        if (percentage >= 80) return "green";
        if (percentage >= 50) return "yellow";
        return "red";
    }

    calculateCoverage(coverageData) {
        const total = Object.keys(coverageData).length;
        const covered = Object.values(coverageData).filter((v) => v > 0).length;
        const percentage = total === 0 ? 0 : (covered / total) * 100;
        return { total, covered, percentage };
    }

    formatCoverage(percentage) {
        const roundedPercentage = percentage.toFixed(2);
        if (percentage >= 80) {
            return this.color(`${roundedPercentage}%`, "green");
        } else if (percentage >= 50) {
            return this.color(`${roundedPercentage}%`, "yellow");
        } else {
            return this.color(`${roundedPercentage}%`, "red");
        }
    }

    color(text, style) {
        const styles = {
            bold: [1, 22],
            red: [31, 39],
            green: [32, 39],
            yellow: [33, 39],
            blue: [34, 39],
            magenta: [35, 39],
            cyan: [36, 39],
        };

        const open = styles[style][0];
        const close = styles[style][1];

        return `\u001b[${open}m${text}\u001b[${close}m`;
    }
}

module.exports = CustomReporter;
