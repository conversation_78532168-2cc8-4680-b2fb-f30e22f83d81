"use strict";
const request = require("@tests/nes-request").request();
const sharedState = require("@tests/sharedState");

const SECONDS = 1000;
let results;

it(
    "Checks calculations",
    async () => {
        const filenames = [
            { filename: "3-calculate-dispense-quantity" },
            { filename: "4-calculate-billing-quantity" },
            { filename: "5-calculate-charge-quantity" },
            { filename: "6-calculate-tax" },
            { filename: "7-convert-dt-ledger-billable" },
            { filename: "8-calculate-refills" },
            { filename: "9-calculate-service-dates" },
        ];
        const res = await request
            .post(`/api/testing`)
            .send({
                filenames: filenames,
            })
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        results = res.body;
        sharedState.set("results", results);
        expect(results).toBeDefined();
    },
    99999 * SECONDS
);
