"use strict";
const request = require("@tests/nes-request").request();
const sharedState = require("@tests/sharedState");

const SECONDS = 1000;
let results;

it(
    "Checks splitting a compound claim",
    async () => {
        const res = await request
            .post(`/api/testing`)
            .send({
                filenames: [
                    { filename: "1-split-compound-claim" },
                    { filename: "2-split-ivig-claim" },
                ],
            })
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        results = res.body;
        sharedState.set("results", results);
        expect(results).toBeDefined();
    },
    99999 * SECONDS
);
