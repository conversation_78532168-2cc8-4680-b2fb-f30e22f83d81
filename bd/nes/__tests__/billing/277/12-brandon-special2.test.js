const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");

const SECONDS = 1000;

it(
    "Checks <PERSON>'s test case 2 throws an error",
    async () => {
        const statusReport = await loadJsonFile(
            "admin_server/277/",
            "277-brandon-special2.json"
        );

        const res = await request
            .post(`/api/billing/medical/reports`)
            .clearDSLMocks()
            .send(statusReport.body)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["error"]).toBeDefined();
    },
    10 * SECONDS
);
