const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");

const SECONDS = 1000;

it(
    "Checks an approved status report response is handled properly",
    async () => {
        const statusReport = await loadJsonFile(
            "admin_server/277/",
            "277-response-template.json"
        );
        const controlNumber = "434343434343";

        await Promise.all([
            helper.applyDates(statusReport),
            helper.applyAmounts(statusReport),
            helper.setControlNumber(statusReport, controlNumber),
            helper.setCounts(statusReport),
            helper.setQuantities(statusReport),
            helper.setStatusReportStatus(statusReport, "F0"),
        ]);

        const wrappedReport = await helper.wrapAdminServerResponse(
            statusReport.body,
            "277",
            controlNumber
        );
        const res = await request
            .post(`/api/billing/medical/reports`)
            .addDSLMocks("med_claim", true, null, "multi-service-lines")
            .setContextVar("default_dsl_vars", [
                {
                    form: "med_claim",
                    control_number: controlNumber,
                },
            ])
            .send(wrappedReport)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["success"]).toBeTruthy();
        expect(body["stopFetching"]).toHaveLength(0);
        expect(body["medClaimRecs"]).toHaveLength(1);
        expect(body["medClaimRecs"][0]["status"]).toBe("Paid");
    },
    10 * SECONDS
);
