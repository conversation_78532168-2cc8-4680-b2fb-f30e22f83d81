const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");

const SECONDS = 1000;

it(
    "Checks a claim not found status is handled properly",
    async () => {
        const statusReport = await loadJsonFile(
            "admin_server/277/",
            "277-response-template.json"
        );
        const controlNumber = "434343434343";

        await Promise.all([
            helper.applyDates(statusReport),
            helper.applyAmounts(statusReport),
            helper.setControlNumber(statusReport, controlNumber),
            helper.setCounts(statusReport),
            helper.setQuantities(statusReport),
            helper.setStatusReportStatus(statusReport, "A4"),
        ]);

        const wrappedReport = await helper.wrapAdminServerResponse(
            statusReport.body,
            "277",
            controlNumber
        );
        const res = await request
            .post(`/api/billing/medical/reports`)
            .addDSLMocks("med_claim", true, null, "multi-service-lines")
            .setContextVar("default_dsl_vars", [
                {
                    form: "med_claim",
                    control_number: controlNumber,
                },
            ])
            .send(wrappedReport)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["error"]).toBeTruthy();
    },
    10 * SECONDS
);
