const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");

const SECONDS = 1000;

it(
    "Checks a rejected claim status is handled properly",
    async () => {
        const payerReport = await loadJsonFile(
            "admin_server/835/",
            "835-response-template.json"
        );
        const controlNumber = "434343434343";

        await Promise.all([
            helper.applyDates(payerReport),
            helper.applyAmounts(payerReport),
            helper.setControlNumber(payerReport, controlNumber),
            helper.setCounts(payerReport),
            helper.setQuantities(payerReport),
            helper.setPayerReportStatus(payerReport, "24"),
            helper.applyPercentages(payerReport),
        ]);

        const wrappedReport = await helper.wrapAdminServerResponse(
            payerReport.body,
            "835",
            controlNumber
        );
        const res = await request
            .post(`/api/billing/medical/reports`)
            .addDSLMocks("med_claim", true, null, "multi-service-lines")
            .setContextVar("default_dsl_vars", [
                {
                    form: "med_claim",
                    control_number: controlNumber,
                },
            ])
            .send(wrappedReport)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["success"]).toBeTruthy();
        expect(body["stopFetching"]).toContain(controlNumber);
        expect(body["medClaimRecs"]).toHaveLength(1);
        expect(body["medClaimRecs"][0]["status"]).toBe("Rejected");
    },
    10 * SECONDS
);
