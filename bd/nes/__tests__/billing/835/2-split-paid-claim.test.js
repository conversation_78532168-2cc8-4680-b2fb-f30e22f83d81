const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");
const uuid = require("uuid").v4;

const SECONDS = 1000;

it(
    "Checks a split paid claim status is handled properly",
    async () => {
        const payerReport = await loadJsonFile(
            "admin_server/835/",
            "835-response-template.json"
        );
        const controlNumber = "************";

        await Promise.all([
            helper.applyDates(payerReport),
            helper.applyAmounts(payerReport, 100.0),
            helper.setControlNumber(payerReport, controlNumber),
            helper.setCounts(payerReport),
            helper.setQuantities(payerReport),
            helper.setPayerReportStatus(payerReport, "3"),
            helper.applyPercentages(payerReport),
        ]);

        payerReport.body.meta.traceId = uuid();
        const wrappedReport = await helper.wrapAdminServerResponse(
            payerReport.body,
            "835",
            controlNumber
        );
        const res = await request
            .post(`/api/billing/medical/reports`)
            .addDSLMocks("med_claim", true, null, "multi-service-lines")
            .addDSLMocks(
                "med_claim_resp_835",
                false,
                ["response_id"],
                "paid_835_response"
            )
            .setContextVar("default_dsl_vars", [
                {
                    form: "med_claim",
                    control_number: controlNumber,
                },
            ])
            .send(wrappedReport)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["success"]).toBeTruthy();
        expect(body["stopFetching"]).toContain(controlNumber);
        expect(body["medClaimRecs"]).toHaveLength(1);
        expect(body["medClaimRecs"][0]["status"]).toBe("Paid");
        expect(body["medClaimRecs"][0]["paid"]).toBe(200);
    },
    10 * SECONDS
);
