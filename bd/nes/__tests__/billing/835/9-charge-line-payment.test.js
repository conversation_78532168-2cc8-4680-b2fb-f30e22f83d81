const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");

const SECONDS = 1000;

it(
    "Checks a individual charge line payments are handled properly",
    async () => {
        const payerReport = await loadJsonFile(
            "admin_server/835/",
            "835-response-template.json"
        );
        const controlNumber = "34343122222";

        await Promise.all([
            helper.applyDates(payerReport),
            helper.applyAmounts(payerReport),
            helper.setControlNumber(payerReport, controlNumber),
            helper.setCounts(payerReport),
            helper.setQuantities(payerReport),
            helper.setPayerReportStatus(payerReport, "72"),
            helper.applyPercentages(payerReport),
        ]);

        const serviceLine1 =
            payerReport.body?.transactions[0]?.detailInfo[0]?.paymentInfo[0]
                ?.serviceLines[0];
        const serviceLine1PaymentInfo = serviceLine1?.servicePaymentInformation;
        serviceLine1.lineItemControlNumber = "123";
        serviceLine1PaymentInfo.originalUnitsOfServiceCount = "10.00";
        serviceLine1PaymentInfo.unitsOfServicePaidCount = "5.00";
        serviceLine1PaymentInfo.lineItemChargeAmount = "99.50";
        serviceLine1PaymentInfo.lineItemProviderPaymentAmount = "49.50";
        const serviceLine2 =
            payerReport.body?.transactions[0]?.detailInfo[0]?.paymentInfo[0]
                ?.serviceLines[1];
        serviceLine2.lineItemControlNumber = "456";
        const serviceLine2PaymentInfo = serviceLine2?.servicePaymentInformation;
        serviceLine2PaymentInfo.originalUnitsOfServiceCount = "12.00";
        serviceLine2PaymentInfo.unitsOfServicePaidCount = "7.50";
        serviceLine2PaymentInfo.lineItemChargeAmount = "199.50";
        serviceLine2PaymentInfo.lineItemProviderPaymentAmount = "99.25";

        const wrappedReport = await helper.wrapAdminServerResponse(
            payerReport.body,
            "835",
            controlNumber
        );
        const res = await request
            .post(`/api/billing/medical/reports`)
            .addDSLMocks("med_claim", true, null, "med_claim_not_paid")
            .addDSLMocks(
                "ledger_charge_line",
                false,
                null,
                "med_charge_line_1",
                "med_charge_line_2"
            )
            .addDSLMocks("billing_invoice", false, null, "medical-invoice")
            .setContextVar("default_dsl_vars", [
                {
                    form: "med_claim",
                    control_number: controlNumber,
                },
            ])
            .send(wrappedReport)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["success"]).toBeTruthy();
        expect(body["stopFetching"]).toHaveLength(0);
        expect(body["medClaimRecs"]).toHaveLength(1);
        expect(body["payerResponse"]).toBeDefined();
        expect(body["invoiceRec"]).toBeDefined();
        expect(body["chargeLines"]).toHaveLength(2);
        expect(body["medClaimRecs"][0]["status"]).toBe("Partially Paid");

        const chargeLine1 = body["chargeLines"][0];
        expect(chargeLine1["charge_quantity_paid"]).toBe(5.0);
        expect(chargeLine1["paid"]).toBe(49.5);

        const chargeLine2 = body["chargeLines"][1];
        expect(chargeLine2["charge_quantity_paid"]).toBe(7.5);
        expect(chargeLine2["paid"]).toBe(99.25);
    },
    10 * SECONDS
);
