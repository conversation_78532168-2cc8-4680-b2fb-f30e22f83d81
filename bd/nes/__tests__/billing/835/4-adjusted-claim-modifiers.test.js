const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const helper = require("@tests/billing/helper");

const SECONDS = 1000;

it(
    "Checks adjusted claim modifiers are handled properly",
    async () => {
        const payerReport = await loadJsonFile(
            "admin_server/835/",
            "835-response-template.json"
        );
        const controlNumber = "434343434343";

        await Promise.all([
            helper.applyDates(payerReport),
            helper.applyAmounts(payerReport),
            helper.setControlNumber(payerReport, controlNumber),
            helper.setCounts(payerReport),
            helper.setQuantities(payerReport),
            helper.setPayerReportStatus(payerReport, "24"),
            helper.applyPercentages(payerReport),
            helper.setSubmittedProcedureModifierCodes(payerReport, [
                "AB",
                "CD",
                "EF",
                "GH",
            ]),
            helper.setAdjustedProcedureModifierCodes(payerReport, [
                "IJ",
                "KL",
                "MN",
                "OP",
            ]),
        ]);

        const wrappedReport = await helper.wrapAdminServerResponse(
            payerReport.body,
            "835",
            controlNumber
        );
        const res = await request
            .post(`/api/billing/medical/reports`)
            .addDSLMocks("med_claim", true, null, "multi-service-lines")
            .setContextVar("default_dsl_vars", [
                {
                    form: "med_claim",
                    control_number: controlNumber,
                },
            ])
            .send(wrappedReport)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body["success"]).toBeTruthy();
        expect(body["stopFetching"]).toContain(controlNumber);
        expect(body["medClaimRecs"]).toHaveLength(1);
        expect(body["payerResponse"]).toBeDefined();

        const payerResponse = body["payerResponse"];
        const payerSLObj =
            payerResponse?.transactions[0]?.detail_info[0]?.payment_info[0]
                ?.service_lines[0]?.service_payment_information[0];
        expect(payerSLObj["proc_modifier_1"]).toBe("AB");
        expect(payerSLObj["proc_modifier_2"]).toBe("CD");
        expect(payerSLObj["proc_modifier_3"]).toBe("EF");
        expect(payerSLObj["proc_modifier_4"]).toBe("GH");
        expect(payerSLObj["adj_modifier_1"]).toBe("IJ");
        expect(payerSLObj["adj_modifier_2"]).toBe("KL");
        expect(payerSLObj["adj_modifier_3"]).toBe("MN");
        expect(payerSLObj["adj_modifier_4"]).toBe("OP");
        expect(body["medClaimRecs"][0]["status"]).toBe("Rejected");
    },
    10 * SECONDS
);
