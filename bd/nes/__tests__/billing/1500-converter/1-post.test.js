const moment = require("moment");
const currency = require("currency.js");

const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");

const SECONDS = 1000;

it(
    "Checks a post request with a med claim form",
    async () => {
        const message = await loadJsonFile(
            "dsl/med_claim",
            "kitchen_sink.json"
        );

        const res = await request
            .post(`/api/print/1500/med_claim`)
            .send(message.body)
            .addHeaders(false)
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        checkResponseFields(body);
    },
    10 * SECONDS
);

function checkResponseFields(body) {
    expect(body["1"]["1"]).toBe("CHAMPVA");
    expect(body["1"]["1a"]).toBe("ABC123456789");

    expect(body["2"]).toBe("<PERSON><PERSON>, <PERSON> A");

    expect(body["3"]["month"]).toBe("05");
    expect(body["3"]["day"]).toBe("20");
    expect(body["3"]["year"]).toBe("10");
    expect(body["3"]["gender"]).toBe("F");

    expect(body["4"]).toBe("Doe, John M");

    expect(body["5"]["address"]).toBe("456 Oak Street Apt 789");
    expect(body["5"]["city"]).toBe("Somewhere");
    expect(body["5"]["state"]).toBe("NY");
    expect(body["5"]["zip"]).toBe("54321");
    expect(body["5"]["phone"]).toBe("************");

    expect(body["6"]).toBe("Spouse");

    expect(body["7"]["address"]).toBe("456 Apple Lane Apt 122");
    expect(body["7"]["city"]).toBe("Austin");
    expect(body["7"]["state"]).toBe("TX");
    expect(body["7"]["zip"]).toBe("78628");
    expect(body["7"]["phone"]).toBe("************");

    expect(body["9"]["9"]).toBe("Ranger, Jack P");
    expect(body["9"]["9a"]).toBe("GRP123456");
    expect(body["9"]["9d"]).toBe("ABC Insurance Company");

    expect(body["10"]["10a"]).toBe("NO");
    expect(body["10"]["10b"]["check"]).toBe("YES");
    expect(body["10"]["10b"]["place"]).toBe("CA");
    expect(body["10"]["10c"]).toBe("NO");

    expect(body["11"]["11a"]["month"]).toBe("01");
    expect(body["11"]["11a"]["day"]).toBe("15");
    expect(body["11"]["11a"]["year"]).toBe("80");
    expect(body["11"]["11b"]["qualifier"]).toBe("F8");
    expect(body["11"]["11b"]["ccn"]).toBe("CCN987654321");
    expect(body["11"]["11c"]).toBe("Test Payer");
    expect(body["11"]["11d"]).toBe("YES");

    expect(body["12"]["signature"]).toBe("Signature on File");
    expect(body["12"]["date"]).toBe(moment().format("MM/DD/YYYY"));

    expect(body["13"]).toBe("Signature on File");

    expect(body["14"]["month"]).toBe("01");
    expect(body["14"]["day"]).toBe("22");
    expect(body["14"]["year"]).toBe("21");

    expect(body["15"]["month"]).toBe("05");
    expect(body["15"]["day"]).toBe("18");
    expect(body["15"]["year"]).toBe("00");
    expect(body["15"]["qualifier"]).toBe("453");

    expect(body["16"]["from"]["month"]).toBe("04");
    expect(body["16"]["from"]["day"]).toBe("12");
    expect(body["16"]["from"]["year"]).toBe("24");
    expect(body["16"]["to"]["month"]).toBe("12");
    expect(body["16"]["to"]["day"]).toBe("01");
    expect(body["16"]["to"]["year"]).toBe("24");

    expect(body["17"]["17"]["qualifier"]).toBe("DN");
    expect(body["17"]["17"]["name"]).toBe("Mark Adams");
    expect(body["17"]["17a"]["qualifier"]).toBe("0B");
    expect(body["17"]["17a"]["license_no"]).toBe("SL123456");
    expect(body["17"]["17b"]).toBe("**********");

    expect(body["18"]["from"]["month"]).toBe("06");
    expect(body["18"]["from"]["day"]).toBe("01");
    expect(body["18"]["from"]["year"]).toBe("23");
    expect(body["18"]["to"]["month"]).toBe("06");
    expect(body["18"]["to"]["day"]).toBe("05");
    expect(body["18"]["to"]["year"]).toBe("23");

    expect(body["21"]["A"]).toBe("J45.901");
    expect(body["21"]["B"]).toBe("E11.9");
    expect(body["21"]["C"]).toBe("I10");
    expect(body["21"]["D"]).toBe("M54.5");
    expect(body["21"]["E"]).toBe("F41.1");
    expect(body["21"]["F"]).toBe("K21.9");
    expect(body["21"]["G"]).toBe("H40.9");
    expect(body["21"]["H"]).toBe("N18.3");
    expect(body["21"]["I"]).toBe("G47.33");
    expect(body["21"]["J"]).toBe("R53.83");
    expect(body["21"]["K"]).toBe("L40.0");
    expect(body["21"]["L"]).toBe("D50.9");
    expect(body["21"]["indicator"]).toBe("ABF");

    expect(body["22"]["resubmission_code"]).toBe("7");
    expect(body["22"]["original_ref_no"]).toBe("CCN123456789");

    expect(body["23"]).toBe("AUTH987654");

    expect(body["24"]["0"]["from"]["month"]).toBe("07");
    expect(body["24"]["0"]["from"]["day"]).toBe("15");
    expect(body["24"]["0"]["from"]["year"]).toBe("23");

    expect(body["24"]["0"]["to"]["month"]).toBe("07");
    expect(body["24"]["0"]["to"]["day"]).toBe("22");
    expect(body["24"]["0"]["to"]["year"]).toBe("23");

    expect(body["24"]["0"]["place_of_service"]).toBe("12");
    expect(body["24"]["0"]["emg"]).toBe("X");
    expect(body["24"]["0"]["cpt_hcpcs"]).toBe("E0277");
    expect(body["24"]["0"]["diagnosis_pointer"]).toBe("A,G,L,F");
    expect(body["24"]["0"]["charges"]).toBe("750.00");
    expect(body["24"]["0"]["days_or_units"]).toBe("3.5");
    expect(body["24"]["0"]["EPSDT"]).toBe("X");
    expect(body["24"]["0"]["rendering_provider_npi"]).toBe("**********");

    expect(body["24"]["0"]["modifier_1"]).toBe("GP");
    expect(body["24"]["0"]["modifier_2"]).toBe("59");
    expect(body["24"]["0"]["modifier_3"]).toBe("XU");
    expect(body["24"]["0"]["modifier_4"]).toBe("KX");

    expect(body["24"]["10"]["from"]["month"]).toBe("08");
    expect(body["24"]["10"]["from"]["day"]).toBe("22");
    expect(body["24"]["10"]["from"]["year"]).toBe("23");

    expect(body["24"]["10"]["to"]["month"]).toBe("09");
    expect(body["24"]["10"]["to"]["day"]).toBe("15");
    expect(body["24"]["10"]["to"]["year"]).toBe("23");

    expect(body["24"]["10"]["place_of_service"]).toBe("21");
    expect(body["24"]["10"]["emg"]).toBe("");
    expect(body["24"]["10"]["cpt_hcpcs"]).toBe("E0260");
    expect(body["24"]["10"]["diagnosis_pointer"]).toBe("E,I,B,K");
    expect(body["24"]["10"]["charges"]).toBe("122.42");
    expect(body["24"]["10"]["days_or_units"]).toBe("2.75");
    expect(body["24"]["10"]["EPSDT"]).toBe("");
    expect(body["24"]["10"]["rendering_provider_npi"]).toBe("**********");

    expect(body["24"]["10"]["modifier_1"]).toBe("TC");
    expect(body["24"]["10"]["modifier_2"]).toBe("76");
    expect(body["24"]["10"]["modifier_3"]).toBe("GC");
    expect(body["24"]["10"]["modifier_4"]).toBe("QK");

    expect(body["25"]["federal_tax_id"]).toBe("12-3456789");
    expect(body["25"]["ein"]).toBe("X");

    expect(body["27"]).toBe("YES");

    const split1 = 750.0 + 1125.75 + 895.5 + 1250.75 + 892.5 + 1125.75;
    const split2 = 925.5 + 625.75 + 980.25 + 1250.75 + 122.42;

    expect(body["28"]["page_1"]).toBe(
        currency(split1, { separator: ",", precision: 2, symbol: "" }).format()
    );
    expect(body["28"]["page_2"]).toBe(
        currency(split2, { separator: ",", precision: 2, symbol: "" }).format()
    );

    const adjSplit1 = 75.0 + 135.0 + 55.21 + 100.0 + 135.0 + 200.0 + 43.22;
    const adjSplit2 = 150.22 + 33.33 + 120.0 + 95.12;

    expect(body["29"]["page_1"]).toBe(
        currency(adjSplit1, {
            separator: ",",
            precision: 2,
            symbol: "",
        }).format()
    );
    expect(body["29"]["page_2"]).toBe(
        currency(adjSplit2, {
            separator: ",",
            precision: 2,
            symbol: "",
        }).format()
    );

    expect(body["31"]["signature"]).toBe("Signature on File");
    expect(body["31"]["date"]).toBe(moment().format("MM/DD/YYYY"));

    expect(body["32"]["line_1"]).toBe("Smith");
    expect(body["32"]["line_2"]).toBe("123 Main St Apt 4B");
    expect(body["32"]["line_3"]).toBe("Springfield, IL 62701");

    expect(body["33"]["phone"]).toBe("************");
    expect(body["33"]["line_1"]).toBe("Sample Pharmacy");
    expect(body["33"]["line_2"]).toBe("443 1st 2nd St Suite 100");
    expect(body["33"]["line_3"]).toBe("Anytown, CA 12345");
    expect(body["33"]["33a"]).toBe("3431121124");
}
