"use strict";
const config = require("@tests/config");
const { loadJsonFile } = require("@tests/helper");
const _ = require("lodash");
const moment = require("moment-timezone");

const replacements = [
    { path: "patient_id", value: config.TEST_PATIENT_ID },
    { path: "date_of_service", value: moment().format("MM/DD/YYYY") },
    { path: "insurance_id", value: config.TEST_INSURANCE_ID },
    { path: "prescriber_id", value: config.TEST_PRESCRIBER_LINK_ID },
];

function applyReplacements(jsonObject, file) {
    replacements.forEach((mod) => {
        if (
            (!mod?.ignore_file || mod?.ignore_file !== file) &&
            _.has(jsonObject, mod.path)
        ) {
            _.set(jsonObject, mod.path, mod.value);
        }
    });
    return jsonObject;
}

async function loadThisJsonFile(directory, file) {
    return await loadJsonFile(directory, file, applyReplacements);
}

exports.loadJsonFile = loadThisJsonFile;
