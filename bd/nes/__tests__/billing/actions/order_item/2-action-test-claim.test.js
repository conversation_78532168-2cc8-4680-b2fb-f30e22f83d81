"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

describe(
    "Checking check careplan_order_item test claim view actions",
    () => {
        it(
            "Checks building a valid test claim",
            async () => {
                const res = await request
                    .get(
                        `/api/form/careplan_order_item/1?perform_action=gen_claim`
                    )
                    .addDSLMocks(
                        "careplan_order",
                        true,
                        null,
                        "order-ig-active-new"
                    )
                    .addDSLMocks(
                        "careplan_order_item",
                        false,
                        null,
                        "ig-order-item"
                    )
                    .addDSLMocks("payer", false, null, "payer-pharmacy")
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.edit).toBeDefined();
                expect(body.edit.ncpdp).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
