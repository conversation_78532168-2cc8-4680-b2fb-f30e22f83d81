"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const {
    OrderItemActionKeys,
    OrderItemActions,
} = require("@api/dispense/settings");
const SECONDS = 1000;

describe(
    "Checking order action buttons",
    () => {
        it(
            "Checks default prescription buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_order_item/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_order_item",
                        true,
                        null,
                        "ig-order-item"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    OrderItemActionKeys[OrderItemActions.CREATE_PA],
                    OrderItemActionKeys[OrderItemActions.TEST_CLAIM],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks default prescription buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_order_item/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_order_item",
                        true,
                        null,
                        "ig-order-item"
                    )
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addDSLMocks(
                        "patient_prior_auth",
                        false,
                        null,
                        "prior-auth-approved"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    OrderItemActionKeys[OrderItemActions.VIEW_PA],
                    OrderItemActionKeys[OrderItemActions.VIEW_CLAIM],
                    OrderItemActionKeys[OrderItemActions.TEST_CLAIM],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);

function checkActionButtons(record, actions) {
    const actionKeys = record._meta.actions.map((button) => button.action);
    expect(actionKeys).toHaveLength(actions.length);
    for (const action of actions) {
        expect(actionKeys).toContain(action);
    }
}
