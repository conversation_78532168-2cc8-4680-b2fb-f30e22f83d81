"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks careplan_order_item view claim action",
    async () => {
        const res = await request
            .get(`/api/form/careplan_order_item/1?perform_action=view_claim`)
            .addDSLMocks("careplan_order_item", true, null, "ig-order-item")
            .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.view).toBeDefined();
        expect(body.view.ncpdp).toBeDefined();
    },
    10 * SECONDS
);
