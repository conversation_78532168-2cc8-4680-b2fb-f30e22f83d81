"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks careplan_order_item view PAs action",
    async () => {
        const res = await request
            .get(`/api/form/careplan_order_item/1?perform_action=view_pa`)
            .addDSLMocks("careplan_order_item", true, null, "ig-order-item")
            .addDSLMocks(
                "patient_prior_auth",
                false,
                null,
                "prior-auth-approved"
            )
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.view).toBeDefined();
        expect(body.view.patient_prior_auth).toBeDefined();
    },
    10 * SECONDS
);
