"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");

const SECONDS = 1000;

describe(
    "Checking callback COB view actions",
    () => {
        it(
            "Checks sending a valid COB view",
            async () => {
                const message = await loadJsonFile(
                    "dsl/view_billing_cob",
                    "cob-view.json"
                );
                const post = message.body;
                const res = await request
                    .post(
                        `/api/form/billing_invoice/1/action/cob_view?ncpdp_id=1`
                    )
                    .send(post)
                    .addDSLMocks(
                        "billing_invoice",
                        true,
                        null,
                        "pharmacy-invoice"
                    )
                    .addDSLMocks(
                        "careplan_order",
                        false,
                        null,
                        "order-ig-active-new"
                    )
                    .addDSLMocks(
                        "careplan_order_item",
                        false,
                        null,
                        "ig-order-item"
                    )
                    .addDSLMocks("payer", false, null, "payer-pharmacy")
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addDSLMocks(
                        "patient_insurance",
                        false,
                        null,
                        "insurance-pharmacy"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.edit).toBeDefined();
                expect(body.edit.ncpdp).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
