"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");
const SECONDS = 1000;

it(
    "Checks invoice callback accept action",
    async () => {
        const message = await loadJsonFile(
            "dsl/billing_invoice",
            "invoice-accept.json"
        );
        const post = message.body;

        const res = await request
            .post(`/api/form/billing_invoice/1/action/accept_revenue`)
            .send(post)
            .addDSLMocks("billing_invoice", true, null, "pharmacy-invoice")
            .addDSLMocks("billing_account", false, null, "payer-account")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.success).toBeDefined();
    },
    10 * SECONDS
);
