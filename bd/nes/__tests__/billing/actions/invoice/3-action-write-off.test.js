"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

describe(
    "Checking check add write-off invoice actions",
    () => {
        it(
            "Checks adding a valid write-off",
            async () => {
                const res = await request
                    .get(`/api/form/billing_invoice/1?perform_action=write_off`)
                    .addDSLMocks(
                        "billing_invoice",
                        true,
                        ["id"],
                        "pharmacy-invoice"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.create).toBeDefined();
                expect(body.create.billing_adjustment).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
