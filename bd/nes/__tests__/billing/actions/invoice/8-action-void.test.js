"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

describe(
    "Checking void invoice actions",
    () => {
        it(
            "Checks voiding a valid invoice",
            async () => {
                const res = await request
                    .get(`/api/form/billing_invoice/1?perform_action=void`)
                    .addDSLMocks(
                        "billing_invoice",
                        true,
                        null,
                        "pharmacy-invoice"
                    )
                    .addDSLMocks(
                        "patient_insurance",
                        false,
                        null,
                        "insurance-self-pay"
                    )
                    .addDSLMocks("payer", false, null, "payer-self-pay")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.popup).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
