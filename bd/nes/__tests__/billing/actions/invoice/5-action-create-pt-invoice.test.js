"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

describe(
    "Checking create pt invoice actions",
    () => {
        it(
            "Checks creating a valid pt invoice",
            async () => {
                const res = await request
                    .get(
                        `/api/form/billing_invoice/1?perform_action=create_pt_invoice`
                    )
                    .addDSLMocks(
                        "billing_invoice",
                        true,
                        null,
                        "pharmacy-invoice-accepted"
                    )
                    .addDSLMocks(
                        "patient_insurance",
                        false,
                        null,
                        "insurance-self-pay"
                    )
                    .addDSLMocks("payer", false, null, "payer-self-pay")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.edit).toBeDefined();
                expect(body.edit.billing_invoice).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
