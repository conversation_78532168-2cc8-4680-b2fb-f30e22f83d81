"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

describe(
    "Checking check reverse invoice actions",
    () => {
        it(
            "Checks reversing a valid invoice",
            async () => {
                const res = await request
                    .get(`/api/form/billing_invoice/1?perform_action=reverse`)
                    .addDSLMocks(
                        "billing_invoice",
                        true,
                        null,
                        "pharmacy-invoice-accepted"
                    )
                    .addDSLMocks(
                        "ledger_account",
                        false,
                        null,
                        "payer-ledger-entry"
                    )
                    .addDSLMocks(
                        "ledger_account_apply",
                        false,
                        null,
                        "payer-ledger-payment"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.success).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
