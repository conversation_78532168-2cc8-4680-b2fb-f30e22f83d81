"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

describe(
    "Checking create COB view actions",
    () => {
        it(
            "Checks creating a valid COB view",
            async () => {
                const res = await request
                    .get(
                        `/api/form/billing_invoice/1?perform_action=generate_cob`
                    )
                    .addDSLMocks(
                        "billing_invoice",
                        true,
                        null,
                        "pharmacy-invoice-accepted"
                    )
                    .addDSLMocks(
                        "patient_insurance",
                        false,
                        null,
                        "insurance-self-pay"
                    )
                    .addDSLMocks("payer", false, null, "payer-self-pay")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.create).toBeDefined();
                expect(body.create.view_billing_cob).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
