"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");
const SECONDS = 1000;

it(
    "Checks invoice callback void action",
    async () => {
        const message = await loadJsonFile(
            "dsl/billing_invoice",
            "invoice-void.json"
        );
        const post = message.body;

        const res = await request
            .post(`/api/form/billing_invoice/1/action/void`)
            .send(post)
            .addDSLMocks("billing_invoice", true, null, "pharmacy-invoice")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.success).toBeDefined();
    },
    10 * SECONDS
);
