"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks delivery ticket confirm delivery action",
    async () => {
        const res = await request
            .get(
                `/api/form/careplan_delivery_tick/1?perform_action=conf_delivery`
            )
            .addDSLMocks(
                "careplan_delivery_tick",
                true,
                null,
                "careplan_delivery_tick"
            )
            .addDSLMocks(
                "careplan_delivery_log",
                false,
                null,
                "careplan_delivery_log"
            )
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.edit).toBeDefined();
        expect(body.edit.careplan_delivery_log).toBeDefined();
    },
    10 * SECONDS
);
