"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const {
    DeliveryTicketActionKeys,
    DeliveryTicketActions,
} = require("@api/dispense/settings");
const SECONDS = 1000;

describe(
    "Checking careplan_delivery_ticket action buttons",
    () => {
        it(
            "Checks default delivery ticket buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_delivery_tick/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_delivery_tick",
                        true,
                        null,
                        "careplan_delivery_tick"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    DeliveryTicketActionKeys[DeliveryTicketActions.VOID],
                    DeliveryTicketActionKeys[
                        DeliveryTicketActions.CONFIRM_DELIVERY_TICKET
                    ],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks ready to bill delivery ticket buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_delivery_tick/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_delivery_tick",
                        true,
                        null,
                        "careplan_delivery_tick-ready-to-bill"
                    )
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addDSLMocks(
                        "careplan_delivery_log",
                        false,
                        null,
                        "careplan_delivery_log"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    DeliveryTicketActionKeys[DeliveryTicketActions.VIEW_CLAIM],
                    DeliveryTicketActionKeys[
                        DeliveryTicketActions.CONFIRM_DELIVERY
                    ],
                    DeliveryTicketActionKeys[DeliveryTicketActions.VOID],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);

function checkActionButtons(record, actions) {
    const actionKeys = record._meta.actions.map((button) => button.action);
    expect(actionKeys).toHaveLength(actions.length);
    for (const action of actions) {
        expect(actionKeys).toContain(action);
    }
}
