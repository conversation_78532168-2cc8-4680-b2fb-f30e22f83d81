"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");
const SECONDS = 1000;

it(
    "Checks delivery ticket callback void action",
    async () => {
        const message = await loadJsonFile(
            "dsl/careplan_order",
            "order-void.json"
        );
        const post = message.body;

        const res = await request
            .post(`/api/form/careplan_order/1/action/void`)
            .send(post)
            .addDSLMocks("careplan_order", true, null, "order-ig-active-new")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.success).toBeDefined();
    },
    10 * SECONDS
);
