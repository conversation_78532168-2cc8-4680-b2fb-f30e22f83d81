"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");

const SECONDS = 1000;

describe(
    "Checking check careplan_order generate delivery ticket actions",
    () => {
        it(
            "Checks generating a delivery ticket",
            async () => {
                const message = await loadJsonFile(
                    "dsl/view_create_dt",
                    "view_create_dt.json"
                );
                const post = message.body;

                const res = await request
                    .post(`/api/form/careplan_order/1/action/gen_dt`)
                    .send(post)
                    .addDSLMocks(
                        "careplan_order",
                        true,
                        null,
                        "order-ig-active-new"
                    )
                    .addDSLMocks(
                        "careplan_order_item",
                        false,
                        null,
                        "ig-order-item"
                    )
                    .addDSLMocks("payer", false, null, "payer-pharmacy")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.create).toBeDefined();
                expect(body.create.careplan_delivery_tick).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
