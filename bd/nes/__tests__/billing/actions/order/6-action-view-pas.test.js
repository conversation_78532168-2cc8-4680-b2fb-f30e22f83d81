"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks careplan_order view PAs action",
    async () => {
        const res = await request
            .get(`/api/form/careplan_order/1?perform_action=view_pas`)
            .addDSLMocks("careplan_order", true, null, "order-ig-active-new")
            .addDSLMocks(
                "patient_prior_auth",
                false,
                null,
                "prior-auth-approved"
            )
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.view).toBeDefined();
        expect(body.view.patient_prior_auth).toBeDefined();
    },
    10 * SECONDS
);
