"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const { OrderActionKeys, OrderActions } = require("@api/dispense/settings");
const SECONDS = 1000;

describe(
    "Checking order action buttons",
    () => {
        it(
            "Checks tabula rasa order buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_order/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_order",
                        true,
                        null,
                        "order-ig-pending"
                    )
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    OrderActionKeys[OrderActions.VOID],
                    OrderActionKeys[OrderActions.CREATE_PA],
                    OrderActionKeys[OrderActions.VIEW_CLAIM],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks new order kitchen sink buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_order/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_order",
                        true,
                        null,
                        "order-ig-active-new"
                    )
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addDSLMocks(
                        "patient_prior_auth",
                        false,
                        null,
                        "prior-auth-approved"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    OrderActionKeys[OrderActions.VOID],
                    OrderActionKeys[OrderActions.CREATE_PA],
                    OrderActionKeys[OrderActions.TEST_CLAIM],
                    OrderActionKeys[OrderActions.VIEW_CLAIM],
                    OrderActionKeys[OrderActions.DELIVERY_TICKET],
                    OrderActionKeys[OrderActions.VIEW_PAS],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks refill order kitchen sink buttons",
            async () => {
                const res = await request
                    .get(`/api/form/careplan_order/1?get_actions=true`)
                    .addDSLMocks(
                        "careplan_order",
                        true,
                        null,
                        "order-ig-active-refill"
                    )
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addDSLMocks(
                        "patient_prior_auth",
                        false,
                        null,
                        "prior-auth-approved"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    OrderActionKeys[OrderActions.CREATE_PA],
                    OrderActionKeys[OrderActions.TEST_CLAIM],
                    OrderActionKeys[OrderActions.VIEW_CLAIM],
                    OrderActionKeys[OrderActions.REFILL],
                    OrderActionKeys[OrderActions.VIEW_PAS],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);

function checkActionButtons(record, actions) {
    const actionKeys = record._meta.actions.map((button) => button.action);
    expect(actionKeys).toHaveLength(actions.length);
    for (const action of actions) {
        expect(actionKeys).toContain(action);
    }
}
