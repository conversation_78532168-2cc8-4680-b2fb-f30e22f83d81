"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks careplan_order refill action",
    async () => {
        const res = await request
            .get(`/api/form/careplan_order/1?perform_action=gen_dt`)
            .addDSLMocks("careplan_order", true, null, "order-ig-active-new")
            .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.create).toBeDefined();
        expect(body.create.view_create_dt).toBeDefined();
    },
    10 * SECONDS
);
