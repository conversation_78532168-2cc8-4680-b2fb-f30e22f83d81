"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const {
    AdjustmentActionKeys,
    AdjustmentActions,
} = require("@api/billing/settings");
const SECONDS = 1000;

describe(
    "Checking adjustment action buttons",
    () => {
        it(
            "Checks tabula rasa adjustment buttons",
            async () => {
                const res = await request
                    .get(`/api/form/billing_adjustment/1?get_actions=true`)
                    .addDSLMocks(
                        "billing_adjustment",
                        true,
                        null,
                        "billing-adjustment"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [AdjustmentActionKeys[AdjustmentActions.VOID]];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);

function checkActionButtons(record, actions) {
    const actionKeys = record._meta.actions.map((button) => button.action);
    expect(actionKeys).toHaveLength(actions.length);
    for (const action of actions) {
        expect(actionKeys).toContain(action);
    }
}
