"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks billing adjustment void action",
    async () => {
        const res = await request
            .get(`/api/form/billing_adjustment/1?perform_action=void`)
            .addDSLMocks("billing_adjustment", true, null, "billing-adjustment")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.popup).toBeDefined();
        expect(body.callback_url).toBeDefined();
    },
    10 * SECONDS
);
