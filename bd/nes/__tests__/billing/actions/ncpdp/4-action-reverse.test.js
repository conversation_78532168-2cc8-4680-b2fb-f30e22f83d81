"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks claims submit action",
    async () => {
        const res = await request
            .get(`/api/form/ncpdp/1?perform_action=reverse`)
            .addDSLMocks("ncpdp", true, false, "ncpdp-approved")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.edit).toBeDefined();
        expect(body.edit.ncpdp).toBeDefined();
    },
    10 * SECONDS
);
