"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const { ClaimActionKeys, ClaimActions } = require("@api/billing/settings");
const SECONDS = 1000;

describe(
    "Checking claim action buttons",
    () => {
        it(
            "Checks paid claim buttons",
            async () => {
                const res = await request
                    .get(`/api/form/ncpdp/1?get_actions=true`)
                    .addDSLMocks("ncpdp", true, null, "ncpdp-approved")
                    .addDSLMocks(
                        "billing_invoice",
                        false,
                        null,
                        "pharmacy-invoice"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    ClaimActionKeys[ClaimActions.REVERSE],
                    ClaimActionKeys[ClaimActions.REBILL],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks unpaid claim buttons",
            async () => {
                const res = await request
                    .get(`/api/form/ncpdp/1?get_actions=true`)
                    .addDSLMocks("ncpdp", true, null, "ncpdp-reversed")
                    .addDSLMocks(
                        "billing_invoice",
                        false,
                        null,
                        "pharmacy-invoice"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    ClaimActionKeys[ClaimActions.SUBMIT],
                    ClaimActionKeys[ClaimActions.SAVE_SUBMIT],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);

function checkActionButtons(record, actions) {
    const actionKeys = record._meta.actions.map((button) => button.action);
    expect(actionKeys).toHaveLength(actions.length);
    for (const action of actions) {
        expect(actionKeys).toContain(action);
    }
}
