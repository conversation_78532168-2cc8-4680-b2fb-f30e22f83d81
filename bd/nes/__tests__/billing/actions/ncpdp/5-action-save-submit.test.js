"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");
const SECONDS = 1000;

it(
    "Checks ncpdp save and submit action",
    async () => {
        const message = await loadJsonFile("dsl/ncpdp", "ncpdp-reversed.json");
        const post = message.body;

        const res = await request
            .post(`/api/form/ncpdp/1/action/save_submit`)
            .send(post)
            .addDSLMocks("ncpdp", true, null, "ncpdp-approved")
            .addDSLMocks("patient_insurance", false, null, "insurance-pharmacy")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body).toBeDefined();
        expect(body.edit).toBeDefined();
        expect(body.edit.ncpdp).toBeDefined();
    },
    10 * SECONDS
);
