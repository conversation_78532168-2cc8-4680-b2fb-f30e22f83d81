"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks pharmacy eligibility action",
    async () => {
        const res = await request
            .get(
                `/api/form/patient_insurance/1?perform_action=check_eligibility`
            )
            .addDSLMocks("patient_insurance", true, null, "insurance-pharmacy")
            .addDSLMocks("payer", false, null, "payer-pharmacy")
            .addDSLMocks("ncpdp", false, null, "ncpdp-eligibility-pending")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.view).toBeDefined();
        expect(body.view.ncpdp).toBeDefined();
    },
    10 * SECONDS
);
