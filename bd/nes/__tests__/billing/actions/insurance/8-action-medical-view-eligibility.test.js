"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks pharmacy eligibility action",
    async () => {
        const res = await request
            .get(
                `/api/form/patient_insurance/1?perform_action=view_eligibility`
            )
            .addDSLMocks(
                "patient_insurance",
                true,
                null,
                "insurance-medical-eligibility"
            )
            .addDSLMocks("payer", false, null, "payer-medical")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.print).toBeDefined();
    },
    10 * SECONDS
);
