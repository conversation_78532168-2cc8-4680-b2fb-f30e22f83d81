"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");
const SECONDS = 1000;

it(
    "Checks pharmacy eligibility view post action",
    async () => {
        const message = await loadJsonFile(
            "dsl/pverify_pat_elig",
            "pverify_submit.json"
        );
        const post = message.body;

        const res = await request
            .post(`/api/form/patient_insurance/1/action/medical_eligibility`)
            .send(post)
            .addDSLMocks(
                "patient_insurance",
                true,
                null,
                "insurance-medical-noeligibility"
            )
            .addDSLMocks("payer", false, null, "payer-medical")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.print).toBeDefined();
    },
    10 * SECONDS
);
