"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");
const { loadJsonFile } = require("../../setup");

const SECONDS = 1000;

describe(
    "Checking check patient_insurance test claim view actions",
    () => {
        it(
            "Checks sending a valid test claim",
            async () => {
                const message = await loadJsonFile(
                    "dsl/view_billing_claim_test",
                    "ig-test-claim.json"
                );
                const post = message.body;
                const res = await request
                    .post(`/api/form/patient_insurance/1/action/test_claim`)
                    .send(post)
                    .addDSLMocks(
                        "patient_insurance",
                        true,
                        null,
                        "insurance-pharmacy"
                    )
                    .addDSLMocks("payer", false, null, "payer-pharmacy")
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.edit).toBeDefined();
                expect(body.edit.ncpdp).toBeDefined();
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
