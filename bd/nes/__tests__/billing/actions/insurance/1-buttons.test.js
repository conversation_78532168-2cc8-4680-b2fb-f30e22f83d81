"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const {
    InsuranceActionKeys,
    InsuranceActions,
} = require("@api/dispense/settings");
const SECONDS = 1000;

describe(
    "Checking patient_insurance action buttons",
    () => {
        it(
            "Checks tabula rasa pharmacy insurance buttons",
            async () => {
                const res = await request
                    .get(`/api/form/patient_insurance/1?get_actions=true`)
                    .addDSLMocks(
                        "patient_insurance",
                        true,
                        null,
                        "insurance-pharmacy"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    InsuranceActionKeys[InsuranceActions.TEST_CLAIM],
                    InsuranceActionKeys[InsuranceActions.ELIGIBILITY],
                    InsuranceActionKeys[InsuranceActions.CREATE_PA],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks pharmacy insurance buttons with records",
            async () => {
                const res = await request
                    .get(`/api/form/patient_insurance/1?get_actions=true`)
                    .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
                    .addDSLMocks(
                        "patient_prior_auth",
                        false,
                        null,
                        "prior-auth-approved"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    InsuranceActionKeys[InsuranceActions.VIEW_PAS],
                    InsuranceActionKeys[InsuranceActions.VIEW_CLAIM],
                    InsuranceActionKeys[InsuranceActions.TEST_CLAIM],
                    InsuranceActionKeys[InsuranceActions.ELIGIBILITY],
                    InsuranceActionKeys[InsuranceActions.CREATE_PA],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks tabula rasa medical insurance buttons",
            async () => {
                const res = await request
                    .get(`/api/form/patient_insurance/1?get_actions=true`)
                    .addDSLMocks(
                        "patient_insurance",
                        true,
                        null,
                        "insurance-medical-noeligibility"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    InsuranceActionKeys[InsuranceActions.ELIGIBILITY],
                    InsuranceActionKeys[InsuranceActions.CREATE_PA],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );

        it(
            "Checks medical insurance buttons with records",
            async () => {
                const res = await request
                    .get(`/api/form/patient_insurance/1?get_actions=true`)
                    .addDSLMocks(
                        "patient_insurance",
                        true,
                        null,
                        "insurance-medical-eligibility"
                    )
                    .addDSLMocks(
                        "patient_prior_auth",
                        false,
                        null,
                        "prior-auth-approved"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                const record = body[0];
                expect(record._meta.actions).toBeDefined();
                const actions = [
                    InsuranceActionKeys[InsuranceActions.VIEW_PAS],
                    InsuranceActionKeys[InsuranceActions.VIEW_ELIGIBILITY],
                    InsuranceActionKeys[InsuranceActions.ELIGIBILITY],
                    InsuranceActionKeys[InsuranceActions.CREATE_PA],
                ];
                checkActionButtons(record, actions);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);

function checkActionButtons(record, actions) {
    const actionKeys = record._meta.actions.map((button) => button.action);
    expect(actionKeys).toHaveLength(actions.length);
    for (const action of actions) {
        expect(actionKeys).toContain(action);
    }
}
