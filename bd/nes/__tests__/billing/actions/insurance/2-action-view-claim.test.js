"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks pharmacy view claim action",
    async () => {
        const res = await request
            .get(`/api/form/patient_insurance/1?perform_action=view_claim`)
            .addDSLMocks("patient_insurance", true, null, "insurance-pharmacy")
            .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.view).toBeDefined();
        expect(body.view.ncpdp).toBeDefined();
        expect(body.view.ncpdp.length).toBe(1);
    },
    10 * SECONDS
);
