"use strict";
const request = require("@tests/nes-request").request();
const _ = require("lodash");

const SECONDS = 1000;

it(
    "Checks pharmacy test claim button",
    async () => {
        const res = await request
            .get(`/api/form/patient_insurance/1?perform_action=gen_claim`)
            .addDSLMocks("patient_insurance", true, null, "insurance-pharmacy")
            .addDSLMocks("ncpdp", false, null, "ncpdp-approved")
            .addHeaders()
            .expect("Content-Type", "application/json; charset=utf-8")
            .expect(200);

        const body = res.body;
        expect(body.create).toBeDefined();
        expect(body.create.view_billing_claim_test).toBeDefined();
        expect(body.create.view_billing_claim_test.preset).toBeDefined();
        expect(
            body.create.view_billing_claim_test.preset.insurance_id
        ).toBeDefined();
        expect(
            body.create.view_billing_claim_test.preset.patient_id
        ).toBeDefined();
    },
    10 * SECONDS
);
