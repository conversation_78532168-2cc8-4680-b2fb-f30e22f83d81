const _ = require("lodash");
const moment = require("moment-timezone");
const currency = require("currency.js");

exports.applyDates = async function (jsonObject, date = null) {
    const dateToUse = date || moment().format("MM/DD/YYYY");

    const setDateValue = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setDateValue(value);
            }

            if (
                typeof key === "string" &&
                (key.toLowerCase().includes("date") ||
                    key.toLowerCase().includes("periodstart") ||
                    key.toLowerCase().includes("periodend"))
            ) {
                obj[key] = dateToUse;
            }
        });
    };

    setDateValue(jsonObject);
    return jsonObject;
};

exports.applyAmounts = async function (jsonObject, amount = null) {
    const amountToUse =
        amount ||
        currency(100, {
            symbol: "$",
            precision: 2,
        }).value;

    const setAmountValue = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setAmountValue(value);
            }

            if (
                typeof key === "string" &&
                (key.endsWith("Amount") ||
                    /Amount\d+$/.test(key) ||
                    key.endsWith("Taxes") ||
                    key === "tax" ||
                    key.endsWith("Actual") ||
                    key.endsWith("Paid") ||
                    key.endsWith("Balance") ||
                    key.endsWith("Limit"))
            ) {
                obj[key] = amountToUse;
            }
        });
    };

    setAmountValue(jsonObject);
    return jsonObject;
};

exports.applyPercentages = async function (jsonObject, percentage = null) {
    const percentageToUse = percentage || "0.20";

    const setPercentageValue = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setPercentageValue(value);
            }

            if (typeof key === "string" && key.endsWith("Rate")) {
                obj[key] = percentageToUse;
            }
        });
    };

    setPercentageValue(jsonObject);
    return jsonObject;
};

exports.setControlNumber = async function (jsonObject, controlNumber) {
    const setControlNumber = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setControlNumber(value);
            }

            if (typeof key === "string" && key === "controlNumber") {
                obj[key] = controlNumber;
            }
        });
    };

    setControlNumber(jsonObject);
    return jsonObject;
};

exports.setLineControlNumber = async function (jsonObject, controlNumber) {
    const setLineControlNumber = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setLineControlNumber(value);
            }

            if (typeof key === "string" && key === "lineItemControlNumber") {
                obj[key] = controlNumber;
            }
        });
    };

    setLineControlNumber(jsonObject);
    return jsonObject;
};

exports.setCounts = async function (jsonObject, count = null) {
    const countToUse = count || Math.floor(Math.random() * 20) + 1;
    const setCounts = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setCounts(value);
            }

            if (typeof key === "string" && key === "count") {
                obj[key] = countToUse;
            }
        });
    };

    setCounts(jsonObject);
    return jsonObject;
};

exports.setQuantities = async function (jsonObject, quantity = null) {
    const quantityToUse = quantity || Math.floor(Math.random() * 20) + 1;
    const setQuantities = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setQuantities(value);
            }

            if (typeof key === "string" && key === "quantity") {
                obj[key] = quantityToUse;
            }
        });
    };

    setQuantities(jsonObject);
    return jsonObject;
};

exports.setStatusReportStatus = async function (jsonObject, statusCode) {
    const setStatus = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setStatus(value);
            }

            if (
                typeof key === "string" &&
                key === "healthCareClaimStatusCategoryCode"
            ) {
                obj[key] = statusCode;
            }
        });
    };

    setStatus(jsonObject);
    return jsonObject;
};

exports.setPayerReportStatus = async function (jsonObject, statusCode) {
    const setStatus = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setStatus(value);
            }

            if (typeof key === "string" && key === "claimStatusCode") {
                obj[key] = statusCode;
            }
        });
    };

    setStatus(jsonObject);
    return jsonObject;
};

exports.setAdjustedProcedureModifierCodes = async function (
    jsonObject,
    modifierCodes
) {
    const setModifierCodes = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setModifierCodes(value);
            }

            if (
                typeof key === "string" &&
                key === "adjudicatedProcedureModifierCodes"
            ) {
                obj[key] = modifierCodes;
            }
        });
    };

    setModifierCodes(jsonObject);
    return jsonObject;
};

exports.setSubmittedProcedureModifierCodes = async function (
    jsonObject,
    modifierCodes
) {
    const setModifierCodes = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setModifierCodes(value);
            }

            if (
                typeof key === "string" &&
                key === "submittedAdjudicatedProcedureModifierCodes"
            ) {
                obj[key] = modifierCodes;
            }
        });
    };

    setModifierCodes(jsonObject);
    return jsonObject;
};

exports.setDistinctFieldValue = async function (jsonObject, field, value) {
    const setFieldValue = (obj) => {
        _.forEach(obj, (value, key) => {
            if (typeof value === "object" && value !== null) {
                setFieldValue(value);
            }

            if (typeof key === "string" && key === field) {
                obj[key] = value;
                return;
            }
        });
    };

    setFieldValue(jsonObject);
    return jsonObject;
};

exports.wrapAdminServerResponse = function (
    jsonObject,
    reportType,
    controlNumber,
    payerName = "Test Payer"
) {
    return {
        raw_x12: "",
        raw_json: jsonObject,
        report_type: reportType,
        control_number: controlNumber,
        payer_name: payerName,
    };
};
