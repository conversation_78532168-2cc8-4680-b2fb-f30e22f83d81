"use strict";
const supertest = require("supertest");
const fs = require("fs");
const path = require("path");
const mocks_path = path.join(path.dirname(__filename), "../__mocks__");

const config = require("./config");

class NESRequest {
    constructor(url) {
        this._baseURL = url;
        this.admin_mocks = [];
        this.dsl_mocks = [];
        this.context_vars = {};
    }

    request() {
        const super_request = supertest(this._baseURL);
        const proxyHandler = {
            get: (target, prop, receiver) => {
                if (this[prop]) {
                    return (...args) => {
                        // Ensure we pass the target along with arguments
                        const result = this[prop](target, ...args);
                        // If result is undefined, it means we want to continue chaining from the original supertest instance
                        return result === undefined
                            ? new Proxy(target, proxyHandler)
                            : result;
                    };
                }

                if (typeof target[prop] === "function") {
                    return (...args) => {
                        const result = target[prop](...args);
                        // Return a new proxy wrapped around the result to maintain chaining
                        return new Proxy(result, proxyHandler);
                    };
                }
                return Reflect.get(target, prop);
            },
        };

        return new Proxy(super_request, proxyHandler);
    }

    check_file_exists(filename, directory_path = mocks_path) {
        try {
            const files = fs.readdirSync(directory_path);
            for (const file of files) {
                const file_path = path.join(directory_path, file);
                const stats = fs.statSync(file_path);
                if (stats.isDirectory()) {
                    // If it's a directory, call load_file recursively
                    const data = this.check_file_exists(filename, file_path);
                    if (data) {
                        return data;
                    }
                } else if (path.extname(file) === ".json") {
                    const file_name_without_extension = path.basename(
                        file,
                        ".json"
                    );
                    if (file_name_without_extension === filename) {
                        const data = fs.readFileSync(file_path, "utf8");
                        const json_data = JSON.parse(data);
                        if (Object.keys(json_data).length > 0) {
                            return true;
                        } else {
                            console.log(
                                `Mock File: ${file_path} is not properly formatted`
                            );
                        }
                        return false;
                    }
                }
            }
        } catch (err) {
            console.log("Unable to scan directory: " + err);
        }
    }

    /**
     * @function addDSLMocks
     * @description Adds DSL mocks to the request
     * @param {string} name - The name of the DSL form
     * @param {boolean} clear_mocks - Whether to clear the mocks before adding them
     * @param {array} respect_filters - Filters to respect in the request as an array of field names
     * @param {string} filenames - The names of the files to return in the path
     * @returns {object} - The request object so you can chain it
     */
    addDSLMocks(target, form_name, clear_mocks, respect_filters, ...filenames) {
        if (!form_name) throw new Error("Form name is required");
        if (!filenames.length)
            throw new Error("At least one mock filename is required");
        if (clear_mocks) this.dsl_mocks = [];

        if (this.dsl_mocks.some((mock) => mock.form_name === form_name)) {
            console.log(
                `DSL Mock with the form name ${form_name} already exists`
            );
            return;
        }

        filenames.forEach((filename) => {
            if (!this.check_file_exists(filename)) {
                throw new Error(`Mock File: ${filename} does not exist`);
            }
        });

        let dsl_mock = { form_name, filenames };
        if (respect_filters) {
            dsl_mock = { form_name, filenames, respect_filters };
        }

        this.dsl_mocks.push(dsl_mock);
    }

    /**
     * @function addDSLMocks
     * @description Adds DSL mocks to the request
     * @param {array} dslMocks - Optional DSL mocks, pass an array of { form_name:, filename:}
     * @returns {object} - The request object so you can chain it
     */
    addBulkDSLMocks(target, mocks) {
        if (!mocks) return;
        if (mocks?.length <= 0)
            throw new Error("At least one mock filename is required");

        mocks.forEach(({ form_name, filename }) => {
            if (!this.check_file_exists(filename)) {
                throw new Error(`Mock File: ${filename} does not exist`);
            }

            const dsl_mock = { form_name, filenames: [filename] };
            this.dsl_mocks.push(dsl_mock);
        });
    }

    /**
     * @function clearDSLMocks
     * @description Clears all DSL mocks from the request
     * @param {object} target - The target object
     * @returns {void}
     */
    clearDSLMocks(target) {
        this.dsl_mocks = [];
    }

    /**
     * @function addAdminMocks
     * @description Adds admin mocks to the request
     * @param {string} path - The path to the admin form
     * @param {boolean} clear_mocks - Whether to clear the mocks before adding them
     * @param {string} filename - The name of the file to return when the path is called
     * @returns {object} - The request object so you can chain it
     */
    addAdminMocks(target, path, clear_mocks, filename) {
        if (
            typeof __LIVE_ADMIN_SERVER__ !== "undefined" &&
            // eslint-disable-next-line no-undef
            __LIVE_ADMIN_SERVER__
        ) {
            console.warn(
                "Live Admin Server testing is enabled, skipping admin mocks"
            );
            return;
        }

        if (!path) throw new Error("Path is required");
        if (!filename) throw new Error("Filename is required");
        if (clear_mocks) this.admin_mocks = [];

        if (this.admin_mocks.some((mock) => mock.url === path)) {
            console.log(`Admin Mock with the path ${path} already exists`);
            return;
        }

        if (!this.check_file_exists(filename)) {
            throw new Error(`Mock File: ${filename} does not exist`);
        }

        const admin_mock = { url: path, filename };
        this.admin_mocks.push(admin_mock);
    }

    /**
     * @function setContextVar
     * @description Adds a context variable that will be available under
     * this.ctx.test_vars in nes
     * @param {string} key - The name of the function being traced
     * @param {Any} value - The value for the key
     * @param {Boolean} clear - clears existing context vars before setting if true
     */
    setContextVar(target, key, value, clear = true) {
        if (clear) this.context_vars = {};
        this.context_vars[key] = value;
    }

    /**
     * @function clearContextVars
     * @description Clears any context variables
     */
    clearContextVars() {
        this.context_vars = {};
    }

    /**
     * @function addHeaders
     * @description Adds the nes headers to the request
     * @param {boolean} test_user - Whether to add the test user (CSR role), uses admin by defaults
     * @returns {object} - The request object so you can chain it
     */
    addHeaders(target, test_user) {
        const username = test_user
            ? config.TEST_USERNAME
            : config.BASIC_AUTH_USERNAME;
        const password = test_user
            ? config.TEST_PASSWORD
            : config.BASIC_AUTH_PASSWORD;
        const auth_header = `Basic ${Buffer.from(`${username}:${password}`).toString("base64")}`;

        target
            .set("Authorization", auth_header)
            .set("Accept", "application/json; charset=utf-8")
            .set("x-unit-test", true);

        if (this.dsl_mocks.length > 0) {
            target.set(
                "x-dsl-mocks",
                Buffer.from(JSON.stringify(this.dsl_mocks)).toString("base64")
            );
        }

        if (this.admin_mocks.length > 0) {
            target.set(
                "x-admin-mocks",
                Buffer.from(JSON.stringify(this.admin_mocks)).toString("base64")
            );
        }

        if (Object.keys(this.context_vars).length > 0) {
            target.set(
                "x-test-vars",
                Buffer.from(JSON.stringify(this.context_vars)).toString(
                    "base64"
                )
            );
        }
    }
}

module.exports = new NESRequest(config.BASE_URL);
