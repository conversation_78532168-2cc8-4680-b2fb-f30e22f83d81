"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");
const _ = require("lodash");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Test that is handles an existing order",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [
                        { pharmacy_order_id: config.TEST_ORDER_ID },
                    ])
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(
                    res,
                    { pharmacy_order_id: config.TEST_ORDER_ID },
                    ["order"],
                    ["order_item"]
                );
            },
            TIMEOUT
        );

        it(
            "Test that is handles an existing physician",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [
                        { physician_id: config.TEST_PHYSICIAN_ID },
                    ])
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(
                    res,
                    { physician_id: config.TEST_PHYSICIAN_ID },
                    ["physician"]
                );
            },
            TIMEOUT
        );

        it(
            "Test that is handles an existing patient",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [
                        { patient_id: config.TEST_PATIENT_ID },
                    ])
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(
                    res,
                    { patient_id: config.TEST_PATIENT_ID },
                    ["patient"]
                );
            },
            TIMEOUT
        );

        it(
            "Test that is handles a new patient",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [{ patient_id: null }])
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(res, { patient_id: null }, null, [
                    "patient",
                ]);
            },
            TIMEOUT
        );

        it(
            "Test that is handles a new order",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("pharmacy_order_id", null)
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(
                    res,
                    { pharmacy_order_id: null },
                    null,
                    ["order"]
                );
            },
            TIMEOUT
        );

        it(
            "Test that is handles a new physician",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [{ physician_id: null }])
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(res, { physician_id: null }, null, [
                    "physician",
                    "prescriber",
                ]);
            },
            TIMEOUT
        );

        it(
            "Test that is handles a new payer",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=create_order&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        null,
                        "ss_message_missing_payer"
                    )
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .clearContextVars()
                    .addHeaders()
                    .expect(200);

                check_rec_val_and_filter(res, { payer_id: null }, null, [
                    "payer",
                    "insurance",
                ]);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_rec_val_and_filter(
    response,
    values,
    not_filters = null,
    have_filters = null
) {
    const body = response.body;
    expect(body).toHaveProperty("view_ss_order_generator");
    const view = body.view_ss_order_generator;

    _.forEach(view, (value, key) => {
        expect(view).toHaveProperty(key, value);
    });

    if (not_filters) {
        expect.not.arrayContaining(not_filters);
    }
    if (have_filters) {
        expect.arrayContaining(have_filters);
    }
}
