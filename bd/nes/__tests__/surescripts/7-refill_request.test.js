"use strict";
const request = require("@tests/nes-request.js").request();
const setup = require("./setup/setup-outbound.js");
const TIMEOUT = 10 * 1000;

const keys = [
    { filename: "error_renew_cs_service_level.json" },
    { filename: "error_renew_expired_rx.json" },
    { filename: "error_renew_invalid_service_level.json" },
    { filename: "error_renew_missing_written_date.json" },
    { filename: "error_renew_request_dea2.json" },
    { filename: "error_renew_request_dea4.json" },
    { filename: "error_renew_request_no_disp.json" },
    { filename: "renew_request.json" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, ["message_type"], "ss_message");

        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadJsonFile(
                    "surescripts/outbound/renew",
                    obj.filename
                );
                let exp_res_code = 202;
                if (obj.filename.includes("error")) {
                    exp_res_code = 500;
                }

                if (obj.filename.includes("cs_service_level")) {
                    request.addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider_refill_only"
                    );
                } else if (obj.filename.includes("invalid_service_level")) {
                    request.addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider_no_service_levels"
                    );
                } else {
                    request.addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    );
                }

                const body = message.body;
                const res = await request
                    .post("/api/surescripts/outbound")
                    .send(body)
                    .addAdminMocks(
                        "/internal/surescripts/tx",
                        false,
                        "ss_response_verify"
                    )
                    .setContextVar("had_dispense", true)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(exp_res_code);

                if (exp_res_code === 202) {
                    check_response_elements(res.body);
                    check_response_statuses(res.body);
                } else {
                    expect(res.body).toHaveProperty("Error");
                    expect(res.body.Error.length).toBeGreaterThan(0);
                }
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response) {
    expect(response).toHaveProperty("xml_json");
    expect(response).toHaveProperty("ssrec");

    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.HEADER,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PHARMACY,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PATIENT,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PRESCRIBER,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.MEDICATION,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.DX,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.MEDICATION_DISPENSED,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
}

function check_response_statuses(response) {
    const ssrec = response.ssrec;
    expect(ssrec.message_status).toBe("Verified");
    expect(ssrec.status_icons).toContain("sent");
}
