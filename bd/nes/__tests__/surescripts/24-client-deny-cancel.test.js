"use strict";
const request = require("@tests/nes-request").request();

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Doesn't allow deny cancel response if there isn't one pending",
            async () => {
                await request
                    .get("/api/surescripts/?func=deny_cancel&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That deny cancel response is allowed",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=deny_cancel&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message_cancel")
                    .setContextVar("had_dispense", false, false)
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("ss_message");
                const ssrec = body.ss_message;
                expect(Object.keys(ssrec).length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
