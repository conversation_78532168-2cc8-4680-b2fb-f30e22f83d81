"use strict";
const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");
const config = require("@tests/config");
const {
    FORM_TYPES_ORDER,
    FORM_TYPES_ORDER_ITEM,
    applyDefaultsToPrefill,
} = require("./setup/setup-generator");
const _ = require("lodash");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Does generate all record types and successfully saves results",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    message_type: "NewRx",
                    pa_number: "123123",
                    drug_cvg_status_id: "PA",
                    patient_id: null,
                    physician_id: null,
                    pharmacy_order_id: null,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "<PERSON><PERSON>",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };
                const view_ss_order_generator = {
                    events: '{"patient", "diagnosis", "physician", "prescriber", "insurance", "careplan", "intake", "pa", "order"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);
                const view_res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .addHeaders()
                    .expect(200);

                const generated_recs = check_rec_values(
                    view_res,
                    FORM_TYPES_ORDER
                );
                const recs_results = await request
                    .post("/api/surescripts/?func=save_workflow")
                    .send(generated_recs)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .addHeaders()
                    .expect(200);

                const res_body = recs_results.body;
                expect(res_body).toHaveProperty("success");
            },
            TIMEOUT
        );

        it(
            "Does handle generating prescription and saving over old order",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    message_type: "NewRx",
                    pa_number: "123123",
                    drug_cvg_status_id: "PA",
                    patient_id: null,
                    physician_id: null,
                    pharmacy_order_id: config.TEST_ORDER_ID,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "Karly",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };
                const view_ss_order_generator = {
                    events: '{"patient", "diagnosis", "physician", "prescriber", "insurance", "intake", "pa", "order_item"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);
                const view_res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message], true)
                    .addHeaders()
                    .expect(200);

                const generated_recs = check_rec_values(
                    view_res,
                    FORM_TYPES_ORDER_ITEM
                );
                const recs_results = await request
                    .post("/api/surescripts/?func=save_workflow")
                    .send(generated_recs)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .addHeaders()
                    .expect(200);

                const res_body = recs_results.body;
                expect(res_body).toHaveProperty("success");
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_rec_values(response, types) {
    const response_body = response.body;
    expect(response_body).toHaveProperty("wf_recs");
    const wf_recs = response_body.wf_recs;
    expect(Object.keys(wf_recs).length).toBeGreaterThan(0);
    _.forEach(types, (form_name, _) => {
        const prefill_data = wf_recs[form_name].prefill;
        expect(Object.keys(prefill_data).length).toBeGreaterThan(0);
    });
    return applyDefaultsToPrefill(response_body);
}
