"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");
const moment = require("moment-timezone");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "That refill isn't allowed isn't allowed if prohibited flag is set on message",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_refill&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("default_dsl_vars", [
                        {
                            message_type: "RxRenewalRequest",
                            prohibit_renewal_request: "true",
                        },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That refill isn't allowed isn't allowed if patient isn't linked on message",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_refill&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("default_dsl_vars", [
                        { message_type: "RxRenewalRequest", patient_id: null },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That refill isn't allowed isn't allowed if patient isn't active",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_refill&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("default_dsl_vars", [
                        {
                            form: "ss_message",
                            message_type: "RxRenewalRequest",
                            patient_id: config.TEST_PATIENT_ID,
                        },
                        {
                            form: "patient",
                            status_id_auto_name: "4 - Inactive",
                        },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That refill isn't allowed isn't allowed if order was canceled",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_refill&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message",
                        "ss_message_cancel"
                    )
                    .clearContextVars()
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That refill isn't allowed isn't allowed if provider doesn't have refill service level",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_refill&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider_no_service_levels"
                    )
                    .clearContextVars()
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That refill is allowed",
            async () => {
                const exp_date = moment().add(1, "days").format("MM/DD/YYYY");
                const refill_request = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: config.TEST_PATIENT_ID,
                    expiration_date: exp_date,
                    refills_remaining: 1,
                    pharmacy_rx_no: config.TEST_ORDER_ID,
                    dea_schedule_id: null,
                };
                const res = await request
                    .get("/api/surescripts/?func=request_refill&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [refill_request])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("ss_message");
                const ssrec = body.ss_message;
                expect(Object.keys(ssrec).length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
