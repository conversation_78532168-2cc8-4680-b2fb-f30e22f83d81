"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "That followup isn't allowed for new messages",
            async () => {
                await request
                    .get("/api/surescripts/?func=send_followup&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message_change_req"
                    )
                    .setContextVar("mock_change_is_new", true)
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That follow-up is allowed for change request message and sends follow-up",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=send_followup&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        null,
                        "ss_message_change_req"
                    )
                    .addAdminMocks(
                        "/internal/surescripts/tx",
                        true,
                        "ss_response_verify"
                    )
                    .setContextVar("mock_change_is_new", false)
                    .addHeaders()
                    .expect(202);

                const body = res.body;
                expect(body).toHaveProperty("ssrec");
                const ssrec = body.ssrec;
                expect(ssrec.followup_count).toBe(1);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
