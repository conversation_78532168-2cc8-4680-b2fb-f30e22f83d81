"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");
const moment = require("moment-timezone");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Doesn't allow clarification request if there is already one pending",
            async () => {
                await request
                    .get(
                        "/api/surescripts/?func=request_clarification&form_id=1"
                    )
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message",
                        "ss_message_clarification_req"
                    )
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "Doesn't allow change request if order is canceled",
            async () => {
                await request
                    .get(
                        "/api/surescripts/?func=request_clarification&form_id=1"
                    )
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message",
                        "ss_message_cancel"
                    )
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "Doesn't allow change request if there is an active order attached",
            async () => {
                await request
                    .get(
                        "/api/surescripts/?func=request_clarification&form_id=1"
                    )
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("default_dsl_vars", [
                        {
                            form: "ss_message",
                            pharmacy_order_id: config.TEST_ORDER_ID,
                        },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "Doesn't allow change request on an expired order",
            async () => {
                const exp_date = moment()
                    .subtract(1, "days")
                    .format("MM/DD/YYYY");
                await request
                    .get(
                        "/api/surescripts/?func=request_clarification&form_id=1"
                    )
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("default_dsl_vars", [
                        { form: "ss_message", expiration_date: exp_date },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "That clarification request is allowed",
            async () => {
                const exp_date = moment().add(1, "days").format("MM/DD/YYYY");
                const change_request = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: config.TEST_PATIENT_ID,
                    expiration_date: exp_date,
                    pharmacy_rx_no: config.TEST_ORDER_ID,
                    dea_schedule_id: null,
                };
                const res = await request
                    .get(
                        "/api/surescripts/?func=request_clarification&form_id=1"
                    )
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("default_dsl_vars", [change_request])
                    .setContextVar("had_dispense", false, false)
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("ss_message");
                const ssrec = body.ss_message;
                expect(Object.keys(ssrec).length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
