"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-inbound.js");
const TIMEOUT = 10 * 1000;

const keys = [
    { filename: "Cert_RenewalRES-2.xml" },
    { filename: "Cert_RenewalRES-2-BACKUP.xml" },
    { filename: "Cert_RenewalRES-6_2021v1.xml" },
    { filename: "Cert_RenewalRES-6_2023v1.xml" },
    { filename: "Cert_RenewalResponse_TransferDenial_2023v1.xml" },
    { filename: "ss-rxrenewalresponseTC5.xml" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");
        request.addAdminMocks(
            "/internal/surescripts/directory/provider/live",
            true,
            "ss_dir_provider"
        );

        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadXMLJsonFile(
                    "surescripts/inbound/renew",
                    obj.filename,
                    "RxRenewalResponse"
                );

                const body = message.body;
                const careplan_order = {
                    form: "careplan_order",
                    status_id: "H",
                };

                const res = await request
                    .post("/api/surescripts/inbound")
                    .send(body)
                    .setContextVar("default_dsl_vars", [careplan_order])
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(202);

                check_response_elements(res.body, body.request_meta_data);
                check_response_mapping(res.body, body.request_meta_data);
                check_response_statuses(res.body, body.request_meta_data);
                check_actions_and_icons(res.body, body.request_meta_data);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response, meta_data) {
    expect(response).toHaveProperty("patient_name_display");
    expect(response).toHaveProperty("prescriber_name_display");
    expect(response).toHaveProperty("prescriber_npi");
    expect(response).toHaveProperty("prescriber_address_1");
    expect(response).toHaveProperty("prescriber_phone");

    if (meta_data.response != "Denied") {
        expect(response).toHaveProperty("sig");
    }

    if (meta_data.has_supervisor) {
        expect(response).toHaveProperty("supervisor_npi");
    }

    if (meta_data.has_dx) {
        expect(response.subform_diagnosis.length).toBeGreaterThan(0);
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_code");
    }

    if (meta_data.has_due) {
        expect(response.subform_due.length).toBeGreaterThan(0);
        const subform_due = response.subform_due[0];
        expect(subform_due).toHaveProperty("service_reason_id");
    }

    if (meta_data.has_compound) {
        expect(response.subform_compound.length).toBeGreaterThan(0);
        const compound = response.subform_compound[0];
        expect(compound).toHaveProperty("description");
    }

    if (meta_data.has_allergies) {
        expect(response.subform_allergy.length).toBeGreaterThan(0);
        const allergy = response.subform_allergy[0];
        expect(allergy).toHaveProperty("source");
    }

    if (meta_data.has_observation) {
        expect(response.subform_observation.length).toBeGreaterThan(0);
        const observation = response.subform_observation[0];
        expect(observation).toHaveProperty("type_id");
    }

    if (meta_data.has_followup_prov) {
        expect(response).toHaveProperty("fu_prescriber_npi");
    }
}

function check_response_mapping(response, meta_data) {
    expect(response).toHaveProperty("patient_id");
    expect(response).toHaveProperty("site_id");
    expect(response).toHaveProperty("physician_id");

    if (meta_data.has_dx) {
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_id");
    }
}
function check_response_statuses(response, meta_data) {
    expect(response).toHaveProperty("prescriber_has_license");
    expect(response.prescriber_has_license.length).toBeGreaterThan(0);

    if (Object.values(meta_data).some((value) => value === true)) {
        expect(response).toHaveProperty("show_options");
        expect(response.show_options.length).toBeGreaterThan(0);
    }

    if (meta_data.has_supervisor) {
        expect(response).toHaveProperty("supervisor_has_license");
        expect(response.supervisor_has_license.length).toBeGreaterThan(0);
    }

    if (meta_data.has_dx) {
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_id");
    }

    if (meta_data.has_urgent) {
        expect(response).toHaveProperty("priority_flag", "X");
    }
}
function check_actions_and_icons(response, meta_data) {
    expect(response).toHaveProperty("renewal_status", meta_data.response);

    if (meta_data.response === "Denied") {
        expect(response.available_actions.length).toBe(0);
    } else {
        expect(response.available_actions).toContain("create");
        if (response.pa_number?.length == 0) {
            expect(response.available_actions).toContain("pa");
        }
        expect(response.available_actions).toContain("clarification");
        expect(response.available_actions).toContain("change");
        expect(response.available_actions).toContain("provider");
    }

    expect(response).toHaveProperty("status_icons");
    expect(response.status_icons.length).toBeGreaterThan(0);
    expect(response.status_icons).toContain("new");

    if (meta_data.has_urgent) {
        expect(response.status_icons).toContain("high_priority");
    }

    switch (meta_data.response) {
        case "ApprovedWithChanges":
        case "Approved":
            expect(response.status_icons).toContain("approved");
            break;
        case "Denied":
            expect(response.status_icons).toContain("denied");
            break;
    }
}
