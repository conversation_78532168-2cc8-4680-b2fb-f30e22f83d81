"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-outbound.js");
const TIMEOUT = 10 * 1000;

const keys = [
    { filename: "error_change_cs_invalid_type.json" },
    { filename: "change_request_clarification.json" },
    { filename: "change_request_info.json" },
    { filename: "change_request_pa.json" },
    { filename: "change_request_substitution.json" },
    { filename: "error_change_cs_invalid_service_level.json" },
    { filename: "error_change_expired_script.json" },
    { filename: "error_change_invalid_service_level.json" },
    { filename: "error_change_request_info_H_and_J.json" },
    { filename: "error_change_request_pa_existing.json" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadJsonFile(
                    "surescripts/outbound/change",
                    obj.filename
                );
                let exp_res_code = 202;
                if (obj.filename.includes("error")) {
                    exp_res_code = 500;
                }

                if (obj.filename.includes("cs_invalid_service_level")) {
                    request.addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider_refill_only"
                    );
                } else if (obj.filename.includes("invalid_service_level")) {
                    request.addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider_no_service_levels"
                    );
                } else {
                    request.addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    );
                }

                const body = message.body;
                const res = await request
                    .post("/api/surescripts/outbound")
                    .send(body)
                    .addAdminMocks(
                        "/internal/surescripts/tx",
                        false,
                        "ss_response_verify"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(exp_res_code);

                if (exp_res_code === 202) {
                    check_response_elements(res.body, obj.filename);
                    check_response_statuses(res.body);
                } else {
                    expect(res.body).toHaveProperty("Error");
                    expect(res.body.Error.length).toBeGreaterThan(0);
                }
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response, filename) {
    expect(response).toHaveProperty("xml_json");
    expect(response).toHaveProperty("ssrec");

    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.HEADER,
            "RxChangeRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PHARMACY,
            "RxChangeRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PATIENT,
            "RxChangeRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PRESCRIBER,
            "RxChangeRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.MEDICATION,
            "RxChangeRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.DX,
            "RxChangeRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.CHANGE_CODE,
            "RxChangeRequest"
        )
    ).toBeTruthy();

    const change_code = setup.fetchElementValue(
        response.xml_json,
        setup.elms.CHANGE_CODE,
        "RxChangeRequest"
    );

    if (filename.includes("change_request_clarification")) {
        expect(
            setup.checkForElement(
                response.xml_json,
                setup.elms.CHANGE_REASON,
                "RxChangeRequest"
            )
        ).toBeTruthy();
        expect(change_code).toBe("S");
    }

    if (filename.includes("change_request_info")) {
        expect(
            setup.checkForElement(
                response.xml_json,
                setup.elms.CHANGE_SUBCODE,
                "RxChangeRequest"
            )
        ).toBeTruthy();
        expect(change_code).toBe("U");
    }

    if (filename.includes("change_request_pa")) {
        expect(change_code).toBe("P");
    }
}

function check_response_statuses(response) {
    const ssrec = response.ssrec;
    expect(ssrec.message_status).toBe("Verified");
    expect(ssrec.status_icons).toContain("sent");
}
