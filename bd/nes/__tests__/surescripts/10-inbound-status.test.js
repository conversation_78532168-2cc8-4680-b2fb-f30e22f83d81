"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-inbound.js");
const TIMEOUT = 10 * 1000;

const keys = [
    { filename: "ss-error.xml" },
    { filename: "ss-status-000.xml" },
    { filename: "ss-status-010.xml" },
    { filename: "ss-verify.xml" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");
        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadXMLJsonFile(
                    "surescripts/inbound/status",
                    obj.filename,
                    "Status"
                );

                const body = message.body;
                await request
                    .post("/api/surescripts/inbound")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "text/plain; charset=utf-8")
                    .expect(201);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
