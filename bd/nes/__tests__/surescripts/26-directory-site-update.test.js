"use strict";
const request = require("@tests/nes-request").request();
const { faker } = require("@faker-js/faker");

const { loadJsonFile } = require("@tests/helper");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Update site information in the directory",
            async () => {
                const site_file = await loadJsonFile("dsl/site", "site.json");
                const siterec = site_file.body;
                siterec.name = faker.company.name();
                siterec.code = siterec.name;
                siterec.ss_organization_id = 3330553;

                const res = await request
                    .put("/api/form/overrides/site")
                    .send(siterec)
                    .addAdminMocks(
                        "/internal/surescripts/directory/organization",
                        true,
                        "ss_dir_site_update"
                    )
                    .addHeaders()
                    .expect(200);

                const res_body = res.body;
                expect(res_body).toHaveProperty("id");
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
