"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Checks that you can't request a PA on a canceled RX",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_pa&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message",
                        "ss_message_cancel"
                    )
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "Checks that you can't request a PA if a PA is already on the order",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_pa&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("default_dsl_vars", [
                        { pa_number: "21412422" },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "Checks that you can't request a PA if there is an existing PA request",
            async () => {
                await request
                    .get("/api/surescripts/?func=request_pa&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        null,
                        "ss_message",
                        "ss_message_pa_request"
                    )
                    .setContextVar("default_dsl_vars", [
                        { pa_number: "21412422" },
                    ])
                    .addHeaders()
                    .expect(500);
            },
            TIMEOUT
        );

        it(
            "Checks that you can send a PA request on a new message",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=request_pa&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .clearContextVars()
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("ss_message");
                const ssrec = body.ss_message;
                expect(Object.keys(ssrec).length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
