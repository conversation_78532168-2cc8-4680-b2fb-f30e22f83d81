"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-inbound.js");
const TIMEOUT = 10 * 1000;

// Note: Jest is fucking stupid and evals the test before the beforeAll
// so no way to set the data you want to loop on before the test.each
// ┌∩┐(◣_◢)┌∩┐ <--- Jest developers
const keys = [
    { filename: "QA-NEWRX-Max-Pop.xml" },
    { filename: "Cert_NEWRX-2_2019v2-BACKUP.xml" },
    { filename: "Cert_NEWRX-2_2019v2.xml" },
    { filename: "Cert_NEWRX-3_2019v2.xml" },
    { filename: "Cert_NEWRX-4.xml" },
    { filename: "Cert_NEWRX-5_2021v1.xml" },
    { filename: "Cert_NEWRX-6_2021v1.xml" },
    { filename: "Cert_NEWRX-Chg-1.xml" },
    { filename: "Cert_NEWRX-Chg-3.xml" },
    { filename: "Cert_NEWRX-Chg-6.xml" },
    { filename: "Cert_NEWRX-Chg-7_2019v2.xml" },
    { filename: "Cert_NEWRX-Transfer_2021v1.1.xml" },
    { filename: "Cert_NewRx-1_2023v1.xml" },
    { filename: "ss-newrxTC2.xml" },
    { filename: "ss-newrxTC3.xml" },
    { filename: "ss-newrxTC5.xml" },
    { filename: "ss-newrx-brandon-special.xml" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadXMLJsonFile(
                    "surescripts/inbound/new",
                    obj.filename,
                    "NewRx"
                );
                const body = message.body;

                const res = await request
                    .post("/api/surescripts/inbound")
                    .send(body)
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(202);

                check_response_elements(res.body, body.request_meta_data);
                check_response_mapping(res.body, body.request_meta_data);
                check_response_statuses(res.body, body.request_meta_data);
                check_actions_and_icons(res.body, body.request_meta_data);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response, meta_data) {
    expect(response).toHaveProperty("patient_name_display");
    expect(response).toHaveProperty("prescriber_name_display");
    expect(response).toHaveProperty("prescriber_npi");
    expect(response).toHaveProperty("prescriber_address_1");
    expect(response).toHaveProperty("prescriber_phone");
    expect(response).toHaveProperty("sig");

    if (meta_data.has_supervisor) {
        expect(response).toHaveProperty("supervisor_npi");
    }

    if (meta_data.has_dx) {
        expect(response.subform_diagnosis.length).toBeGreaterThan(0);
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_code");
    }

    if (meta_data.has_due) {
        expect(response.subform_due.length).toBeGreaterThan(0);
        const subform_due = response.subform_due[0];
        expect(subform_due).toHaveProperty("service_reason_id");
    }

    if (meta_data.has_benefits) {
        expect(response.subform_benefit.length).toBeGreaterThan(0);
    }

    if (meta_data.has_observation) {
        expect(response.subform_observation.length).toBeGreaterThan(0);
        const observation = response.subform_observation[0];
        expect(observation).toHaveProperty("type_id");
    }

    if (meta_data.has_followup_prov) {
        expect(response).toHaveProperty("fu_prescriber_npi");
    }

    if (meta_data.has_compound) {
        expect(response.subform_compound.length).toBeGreaterThan(0);
        const compound = response.subform_compound[0];
        expect(compound).toHaveProperty("description");
    }

    if (meta_data.has_allergies) {
        expect(response.subform_allergy.length).toBeGreaterThan(0);
        const allergy = response.subform_allergy[0];
        expect(allergy).toHaveProperty("source");
    }

    if (meta_data.has_coded_note) {
        expect(response.subform_cnote.length).toBeGreaterThan(0);
        const cnote = response.subform_cnote[0];
        expect(cnote).toHaveProperty("qualifier_id");
    }

    if (meta_data.has_odate) {
        expect(response).toHaveProperty("effective_date"); // Not required but all of our tests have it
    }
}

function check_response_mapping(response, meta_data) {
    expect(response).toHaveProperty("patient_id");
    expect(response).toHaveProperty("site_id");
    expect(response).toHaveProperty("physician_id");
    if (meta_data.has_ndc) {
        expect(response).toHaveProperty("fdb_id");
    }

    if (meta_data.has_dx) {
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_id");
    }

    if (meta_data.has_benefits) {
        const benefit = response.subform_benefit[0];
        expect(benefit).toHaveProperty("payer_id");
    }
}

function check_response_statuses(response, meta_data) {
    expect(response).toHaveProperty("prescriber_has_license");
    expect(response.prescriber_has_license.length).toBeGreaterThan(0);

    if (Object.values(meta_data).some((value) => value === true)) {
        expect(response).toHaveProperty("show_options");
        expect(response.show_options.length).toBeGreaterThan(0);
    }

    if (meta_data.has_supervisor) {
        expect(response).toHaveProperty("supervisor_has_license");
        expect(response.supervisor_has_license.length).toBeGreaterThan(0);
    }

    if (meta_data.has_followup_prov) {
        expect(response).toHaveProperty("fu_prescriber_has_license");
        expect(response.fu_prescriber_has_license.length).toBeGreaterThan(0);
    }

    if (meta_data.has_dx) {
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_id");
    }

    if (meta_data.has_benefits) {
        const benefit = response.subform_benefit[0];
        expect(benefit).toHaveProperty("payer_id");
    }

    if (meta_data.has_urgent) {
        expect(response).toHaveProperty("priority_flag", "X");
    }
}

function check_actions_and_icons(response, meta_data) {
    expect(response).toHaveProperty("available_actions");
    expect(response.available_actions.length).toBeGreaterThan(0);
    expect(response.available_actions).toContain("create");
    if (response.pa_number?.length == 0) {
        expect(response.available_actions).toContain("pa");
    }
    expect(response.available_actions).toContain("clarification");
    expect(response.available_actions).toContain("change");
    expect(response.available_actions).toContain("provider");

    expect(response).toHaveProperty("status_icons");
    expect(response.status_icons.length).toBeGreaterThan(0);
    expect(response.status_icons).toContain("new");

    if (meta_data.has_urgent) {
        expect(response.status_icons).toContain("high_priority");
    }
}
