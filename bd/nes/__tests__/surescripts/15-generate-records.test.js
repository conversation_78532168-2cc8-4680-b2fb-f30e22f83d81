"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");
const { loadJsonFile } = require("@tests/helper");
const _ = require("lodash");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Does generate patient prefill if patient_id is missing and patient is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: null,
                };
                const view_ss_order_generator = {
                    events: '{"patient"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "patient");
            },
            TIMEOUT
        );

        it(
            "Does generate patient diagnosis if diagnosis is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: null,
                };
                const view_ss_order_generator = {
                    events: '{"diagnosis"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "patient_diagnosis");
            },
            TIMEOUT
        );

        it(
            "Does generate patient insurance if patient insurance is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: null,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "Karly",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };
                const view_ss_order_generator = {
                    events: '{"insurance"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "patient_insurance");
            },
            TIMEOUT
        );

        it(
            "Does generate physician if physician is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: null,
                    physician_id: null,
                };
                const view_ss_order_generator = {
                    events: '{"physician"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "physician");
            },
            TIMEOUT
        );

        it(
            "Does generate patient prescriber if patient prescriber is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    form: "ss_message",
                    message_type: "NewRx",
                    patient_id: null,
                    physician_id: null,
                };
                const view_ss_order_generator = {
                    events: '{"prescriber"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "patient_prescriber");
            },
            TIMEOUT
        );

        it(
            "Does generate prior auth if prior auth is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const ss_message = {
                    form: "ss_message",
                    message_type: "NewRx",
                    pa_number: "123123",
                    drug_cvg_status_id: "PA",
                };
                const view_ss_order_generator = {
                    events: '{"pa"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "patient_prior_auth");
            },
            TIMEOUT
        );

        it(
            "Does generate careplan if careplan is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const view_ss_order_generator = {
                    events: '{"careplan"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "careplan");
            },
            TIMEOUT
        );

        it(
            "Does generate intake if intake is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const view_ss_order_generator = {
                    events: '{"intake"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "patient_intake");
            },
            TIMEOUT
        );

        it(
            "Does generate careplan order if careplan order is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const view_ss_order_generator = {
                    events: '{"order"}',
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "careplan_order");
            },
            TIMEOUT
        );

        it(
            "Does generate careplan prescription if careplan prescription is selected",
            async () => {
                const view = await loadJsonFile(
                    "surescripts/dsl",
                    "view_ss_order_generator.json"
                );
                const view_ss_order_generator = {
                    events: '{"order_item"}',
                    pharmacy_order_id: config.TEST_ORDER_ID,
                };
                const body = view.body;
                _.assignIn(body, view_ss_order_generator);

                const res = await request
                    .post("/api/surescripts/?func=generate_records")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addHeaders()
                    .expect(200);

                check_rec_values(res, "careplan_order_item");
                expect(
                    res.body.wf_recs["careplan_order_item"].prefill._meta
                        .careplan_order_id
                ).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_rec_values(response, form) {
    const response_body = response.body;
    expect(response_body).toHaveProperty("wf_recs");
    const wf_recs = response_body.wf_recs;
    expect(Object.keys(wf_recs).length).toBeGreaterThan(0);
    const prefill_data = wf_recs[form].prefill;
    expect(Object.keys(prefill_data).length).toBeGreaterThan(0);
}
