"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-inbound.js");
const TIMEOUT = 10 * 1000;

const keys = [
    { filename: "Cert_CANCEL-2.xml" },
    { filename: "Cert_CANCEL-3_2019v2.xml" },
    { filename: "Cert_CANCEL-4.xml" },
    { filename: "Cert_CANCEL-5_2019v2.xml" },
    { filename: "ss-cancelrxTC3.xml" },
    { filename: "ss-cancelrxTC4.xml" },
    { filename: "ss-cancelrxTC5.xml" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadXMLJsonFile(
                    "surescripts/inbound/cancel",
                    obj.filename,
                    "CancelRx"
                );

                const body = message.body;
                const res = await request
                    .post("/api/surescripts/inbound")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(202);

                check_response_elements(res.body, body.request_meta_data);
                check_response_mapping(res.body, body.request_meta_data);
                check_response_statuses(res.body, body.request_meta_data);
                check_actions_and_icons(res.body);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response, meta_data) {
    expect(response).toHaveProperty("patient_name_display");
    expect(response).toHaveProperty("prescriber_name_display");
    expect(response).toHaveProperty("prescriber_npi");
    expect(response).toHaveProperty("prescriber_address_1");
    expect(response).toHaveProperty("prescriber_phone");

    if (meta_data.has_supervisor) {
        expect(response).toHaveProperty("supervisor_npi");
    }

    if (meta_data.has_dx) {
        expect(response.subform_diagnosis.length).toBeGreaterThan(0);
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_code");
    }

    if (meta_data.has_due) {
        expect(response.subform_due.length).toBeGreaterThan(0);
        const subform_due = response.subform_due[0];
        expect(subform_due).toHaveProperty("service_reason_id");
    }

    if (meta_data.has_compound) {
        expect(response.subform_compound.length).toBeGreaterThan(0);
        const compound = response.subform_compound[0];
        expect(compound).toHaveProperty("description");
    }

    if (meta_data.has_allergies) {
        expect(response.subform_allergy.length).toBeGreaterThan(0);
        const allergy = response.subform_allergy[0];
        expect(allergy).toHaveProperty("source");
    }
}

function check_response_mapping(response, meta_data) {
    expect(response).toHaveProperty("patient_id");
    expect(response).toHaveProperty("site_id");
    expect(response).toHaveProperty("physician_id");
    if (meta_data.has_ndc) {
        expect(response).toHaveProperty("fdb_id");
    }

    if (meta_data.has_dx) {
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_id");
    }
}
function check_response_statuses(response, meta_data) {
    expect(response).toHaveProperty("prescriber_has_license");
    expect(response.prescriber_has_license.length).toBeGreaterThan(0);

    if (Object.values(meta_data).some((value) => value === true)) {
        expect(response).toHaveProperty("show_options");
        expect(response.show_options.length).toBeGreaterThan(0);
    }

    if (meta_data.has_supervisor) {
        expect(response).toHaveProperty("supervisor_has_license");
        expect(response.supervisor_has_license.length).toBeGreaterThan(0);
    }

    if (meta_data.has_dx) {
        const dx = response.subform_diagnosis[0];
        expect(dx).toHaveProperty("dx_id");
    }
    expect(response).toHaveProperty("priority_flag", "X");
}

function check_actions_and_icons(response) {
    expect(response).toHaveProperty("available_actions");
    expect(response.available_actions.length).toBeGreaterThan(0);
    expect(response.available_actions).toContain("approve_cancel");
    expect(response.available_actions).toContain("deny_cancel");

    expect(response).toHaveProperty("status_icons");
    expect(response.status_icons.length).toBeGreaterThan(0);
    expect(response.status_icons).toContain("new");
    expect(response.status_icons).toContain("canceled");

    // Cancel messages should always be high priority
    expect(response.status_icons).toContain("high_priority");
}
