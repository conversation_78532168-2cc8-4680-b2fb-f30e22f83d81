"use strict";
const request = require("@tests/nes-request").request();
const { faker } = require("@faker-js/faker");

const { loadJsonFile } = require("@tests/helper");

const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Does save new site information to the directory",
            async () => {
                const site_file = await loadJsonFile("dsl/site", "site.json");
                const siterec = site_file.body;
                siterec.name = faker.company.name();
                siterec.code = siterec.name;

                const res = await request
                    .post("/api/form/overrides/site")
                    .send(siterec)
                    .addAdminMocks(
                        "/internal/surescripts/directory",
                        true,
                        "ss_dir_site_create"
                    )
                    .addHeaders()
                    .expect(200);

                const res_body = res.body;
                expect(res_body).toHaveProperty("id");
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
