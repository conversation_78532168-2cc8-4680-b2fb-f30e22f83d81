"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "That actions for a refill request are are same as newRX",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=fetch_actions&form_id=1")
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        true,
                        "ss_dir_provider"
                    )
                    .setContextVar("had_dispense", false)
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("available_actions");
                const available_actions = body.available_actions;
                expect(available_actions.length).toBe(6);
            },
            TIMEOUT
        );

        it(
            "That actions for a refill request that was canceled are empty",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=fetch_actions&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message",
                        "ss_message_cancel"
                    )
                    .setContextVar("had_dispense", false)
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("available_actions");
                const available_actions = body.available_actions;
                expect(available_actions.length).toBe(0);
            },
            TIMEOUT
        );

        it(
            "That actions for a change request that is new doesn't have a follow-up option",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=fetch_actions&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message_change_req"
                    )
                    .setContextVar("mock_change_is_new", true)
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("available_actions");
                const available_actions = body.available_actions;
                expect(available_actions.length).toBe(0);
            },
            TIMEOUT
        );

        it(
            "That actions for a change request that is older does have a follow-up option",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=fetch_actions&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message_change_req"
                    )
                    .clearContextVars()
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("available_actions");
                const available_actions = body.available_actions;
                expect(available_actions).toContain("followup");
            },
            TIMEOUT
        );

        it(
            "A cancel request without an existing response has both approve/deny actions",
            async () => {
                const res = await request
                    .get("/api/surescripts/?func=fetch_actions&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        null,
                        "ss_message_cancel_processed"
                    )
                    .clearContextVars()
                    .addHeaders()
                    .expect(200);

                const body = res.body;
                expect(body).toHaveProperty("available_actions");
                const available_actions = body.available_actions;
                expect(available_actions.length).toBe(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
