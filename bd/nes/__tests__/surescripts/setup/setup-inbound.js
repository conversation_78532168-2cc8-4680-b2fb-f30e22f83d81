"use strict";
const config = require("@tests/config");
const { loadXMLJsonFile } = require("@tests/helper");
const _ = require("lodash");
const uuid = require("uuid").v4;

const replacements = [
    { path: "Message.Header.To", value: config.TEST_PHARM_NCPDP },
    {
        path: "Message.Body.<MessageType>.Patient.HumanPatient.Name.FirstName",
        value: config.TEST_PATIENT_FIRSTNAME,
    },
    {
        path: "Message.Body.<MessageType>.Patient.HumanPatient.Name.LastName",
        value: config.TEST_PATIENT_LASTNAME,
    },
    {
        path: "Message.Body.<MessageType>.Patient.HumanPatient.DateOfBirth.Date",
        value: config.TEST_PATIENT_DOB,
    },
    {
        path: "Message.Body.<MessageType>.Pharmacy.Identification.NCPDPID",
        value: config.TEST_PHARM_NCPDP,
    },
    {
        path: "Message.Body.<MessageType>.Prescriber.NonVeterinarian.Identification.NPI",
        value: config.TEST_PHYSICIAN_NPI,
    },
    {
        path: "Message.Body.<MessageType>.Supervisor.NonVeterinarian.Identification.NPI",
        value: config.TEST_SUPERVISOR_NPI,
    },
    {
        path: "Message.Body.<MessageType>.BenefitsCoordination.NonVeterinarian.PayerIdentification.ProcessorIdentificationNumber",
        value: config.TEST_PAYER_PCN,
    },
    {
        path: "Message.Body.<MessageType>.BenefitsCoordination.NonVeterinarian.PayerIdentification.IINNumber",
        value: config.TEST_PAYER_BIN,
    },
];

const optional_elms = {
    SUPERVISOR: "Message.Body.<MessageType>.Supervisor",
    DUE: "Message.Body.<MessageType>.MedicationPrescribed.DrugUseEvaluation",
    DX: "Message.Body.<MessageType>.MedicationPrescribed.Diagnosis",
    BENEFITS: "Message.Body.<MessageType>.BenefitsCoordination",
    OBSERVATION: "Message.Body.<MessageType>.Measurement.VitalSign",
    FOLLOWUP_PROV: "Message.Body.<MessageType>.FollowUpPrescriber",
    COMPOUND:
        "Message.Body.<MessageType>.MedicationPrescribed.CompoundInformation",
    ALLERGIES: "Message.Body.<MessageType>.AllergyOrAdverseEvent",
    NO_ALLERGIES:
        "Message.Body.<MessageType>.AllergyOrAdverseEvent.NoKnownAllergies",
    CODED_NOTE:
        "Message.Body.<MessageType>.MedicationPrescribed.PatientCodifiedNote",
    ODATE: "Message.Body.<MessageType>.MedicationPrescribed.OtherMedicationDate",
    URGENT: "Message.Body.<MessageType>.UrgencyIndicatorCode",
    NDC: "Message.Body.<MessageType>.MedicationPrescribed.DrugCoded.ProductCode.Code",
    PRODUCT_CODE_QUALIFIER_ID:
        "Message.Body.<MessageType>.MedicationPrescribed.DrugCoded.ProductCode.Qualifier",
};

const response_elms = {
    "Message.Body.RxChangeResponse.Response.Approved": "Approved",
    "Message.Body.RxChangeResponse.Response.Denied": "Denied",
    "Message.Body.RxChangeResponse.Response.ApprovedWithChanges":
        "ApprovedWithChanges",
    "Message.Body.RxChangeResponse.Response.Validated": "Validated",
    "Message.Body.RxRenewalResponse.Response.Approved": "Approved",
    "Message.Body.RxRenewalResponse.Response.Denied": "Denied",
    "Message.Body.RxRenewalResponse.Response.ApprovedWithChanges":
        "ApprovedWithChanges",
    "Message.Body.RxRenewalResponse.Response.Replace": "Replace",
};

function safeSet(obj, path, value) {
    // Check if the path exists without modifying the object
    const current = _.get(obj, path, undefined);

    // If the path currently holds a value (not undefined), set the new value
    if (current !== undefined) {
        _.set(obj, path, value);
    }
}

function checkForElement(obj, path, message_type) {
    path = path.replace("<MessageType>", message_type);
    const current = _.get(obj, path, undefined);
    return current !== undefined;
}

function fetchElementValue(obj, path, message_type) {
    path = path.replace("<MessageType>", message_type);
    return _.get(obj, path, undefined);
}

function checkForResponse(obj, message_type) {
    for (const [key, value] of Object.entries(response_elms)) {
        if (checkForElement(obj, key, message_type)) {
            return value;
        }
    }
    return null;
}

function processJsonContent(jsonContent, message_type, xmlContent) {
    // Apply modifications with our own reference data
    replacements.forEach((mod) => {
        let path = mod.path;
        path = path.replace("<MessageType>", message_type);
        safeSet(jsonContent, path, mod.value);
    });

    // Randonize message ID
    const messageId = uuid();
    safeSet(jsonContent, "Message.Header.MessageID", messageId);

    const urgent = _.get(jsonContent, optional_elms.URGENT, undefined) || false;
    let has_allergies = checkForElement(
        jsonContent,
        optional_elms.ALLERGIES,
        message_type
    );
    if (
        checkForElement(jsonContent, optional_elms.NO_ALLERGIES, message_type)
    ) {
        has_allergies = false;
    }

    const has_ndc = checkForElement(
        jsonContent,
        optional_elms.NDC,
        message_type
    );
    const qualifier = fetchElementValue(
        jsonContent,
        optional_elms.PRODUCT_CODE_QUALIFIER_ID,
        message_type
    );
    const meta_data = {
        has_supervisor: checkForElement(
            jsonContent,
            optional_elms.SUPERVISOR,
            message_type
        ),
        has_due: checkForElement(jsonContent, optional_elms.DUE, message_type),
        has_benefits: checkForElement(
            jsonContent,
            optional_elms.BENEFITS,
            message_type
        ),
        has_observation: checkForElement(
            jsonContent,
            optional_elms.OBSERVATION,
            message_type
        ),
        has_followup_prov: checkForElement(
            jsonContent,
            optional_elms.FOLLOWUP_PROV,
            message_type
        ),
        has_compound: checkForElement(
            jsonContent,
            optional_elms.COMPOUND,
            message_type
        ),
        has_allergies: has_allergies,
        has_coded_note: checkForElement(
            jsonContent,
            optional_elms.CODED_NOTE,
            message_type
        ),
        has_odate: checkForElement(
            jsonContent,
            optional_elms.ODATE,
            message_type
        ),
        has_dx: checkForElement(jsonContent, optional_elms.DX, message_type),
        has_urgent: urgent && urgent == "X" ? true : false,
        has_ndc: has_ndc && qualifier == "ND" ? true : false,
        message_type: message_type,
        response: checkForResponse(jsonContent, message_type),
    };

    // Enhanced output structure
    return {
        request_json_data: jsonContent,
        request_hash: uuid(), // Generate a random UUID
        request_xml_data: xmlContent,
        request_meta_data: meta_data, // Include the original XML data as a string
    };
}

async function loadThisXMLJsonFile(directory, file, message_type) {
    return await loadXMLJsonFile(
        directory,
        file,
        message_type,
        processJsonContent
    );
}

exports.loadXMLJsonFile = loadThisXMLJsonFile;
