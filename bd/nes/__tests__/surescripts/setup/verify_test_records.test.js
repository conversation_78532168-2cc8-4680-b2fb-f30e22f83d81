"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");
const SECONDS = 1000;

describe(
    "Verify Test Records in Database",
    () => {
        it(
            "checks for patient",
            async () => {
                const res = await request
                    .get(`/api/form/patient/${config.TEST_PATIENT_ID}`)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res.body.length).toBeGreaterThan(0);

                const record = res.body[0];
                expect(record.firstname).toEqual(config.TEST_PATIENT_FIRSTNAME);
                expect(record.lastname).toEqual(config.TEST_PATIENT_LASTNAME);
                expect(record.dob).toEqual(config.TEST_PATIENT_DOB);
            },
            10 * SECONDS
        );

        it(
            "checks for payer",
            async () => {
                const res = await request
                    .get(
                        `/api/form/payer?filter=bin:${config.TEST_PAYER_BIN}&filter=pcn:${config.TEST_PAYER_PCN}`
                    )
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res.body.length).toBeGreaterThan(0);

                const record = res.body[0];
                expect(record.bin.toString()).toEqual(
                    config.TEST_PAYER_BIN.toString()
                );
                expect(record.pcn.toString()).toEqual(
                    config.TEST_PAYER_PCN.toString()
                );
            },
            10 * SECONDS
        );

        it(
            "checks for physician",
            async () => {
                const res = await request
                    .get(
                        `/api/form/physician?filter=npi:${config.TEST_PHYSICIAN_NPI}`
                    )
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res.body.length).toBeGreaterThan(0);

                const record = res.body[0];
                expect(record.npi.toString()).toEqual(
                    config.TEST_PHYSICIAN_NPI.toString()
                );
            },
            10 * SECONDS
        );

        it(
            "checks for supervisor",
            async () => {
                const res = await request
                    .get(
                        `/api/form/physician?filter=npi:${config.TEST_SUPERVISOR_NPI}`
                    )
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res.body.length).toBeGreaterThan(0);

                const record = res.body[0];
                expect(record.npi.toString()).toEqual(
                    config.TEST_SUPERVISOR_NPI.toString()
                );
            },
            10 * SECONDS
        );

        it(
            "checks for po",
            async () => {
                const res = await request
                    .get(`/api/form/po/${config.TEST_PO_ID}`)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res.body.length).toBeGreaterThan(0);
            },
            10 * SECONDS
        );

        it(
            "checks for site",
            async () => {
                const res = await request
                    .get(`/api/form/site/${config.TEST_SITE_ID}`)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res.body.length).toBeGreaterThan(0);

                const record = res.body[0];
                expect(record.ncpdp_id.toString()).toEqual(
                    config.TEST_PHARM_NCPDP.toString()
                );
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
