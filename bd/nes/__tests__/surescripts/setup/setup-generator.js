"use strict";
const config = require("@tests/config");
const _ = require("lodash");
const moment = require("moment-timezone");

const FORM_TYPES_ORDER = {
    PATIENT: "patient",
    DIAGNOSIS: "patient_diagnosis",
    PRESCRIBER: "physician",
    PATIENT_PRESCRIBER: "patient_prescriber",
    INSURANCE: "patient_insurance",
    PRIOR_AUTH: "patient_prior_auth",
    INTAKE: "patient_intake",
    CAREPLAN: "careplan",
    ORDER: "careplan_order",
};

const FORM_TYPES_ORDER_ITEM = {
    PATIENT: "patient",
    DIAGNOSIS: "patient_diagnosis",
    PRESCRIBER: "physician",
    PATIENT_PRESCRIBER: "patient_prescriber",
    INSURANCE: "patient_insurance",
    PRIOR_AUTH: "patient_prior_auth",
    INTAKE: "patient_intake",
    ORDER_ITEM: "careplan_order_item",
};

const REQUIRED_FIELDS = {
    [FORM_TYPES_ORDER.PATIENT]: {
        identify_gender: "Same as birth",
        referral_date: moment().format("MM/DD/YYYY"),
        status_id: "1",
    },
    [FORM_TYPES_ORDER.PRESCRIBER]: {
        title: "MD",
        territory_id: config.TEST_TERRITORY_ID,
    },
    [FORM_TYPES_ORDER.PAYER]: {
        short_code: "BCBS",
        type_id: "CMPBM",
        billing_method_id: "ncpdp",
        inn_oon: "INN",
        mm_sec_pharmacy_qualifier_id: "EI",
        mm_taxonomy_code: "34343",
    },
    [FORM_TYPES_ORDER.INSURANCE]: {
        billing_method_id: "ncpdp",
        type_id: "CMPBM",
        policy_number: "12312312",
        effective_date: moment().format("MM/DD/YYYY"),
    },
    [FORM_TYPES_ORDER.PRIOR_AUTH]: {
        status_id: "5",
    },
    [FORM_TYPES_ORDER.INTAKE]: {
        territory_id: config.TEST_TERRITORY_ID,
        intake_therapy_id: "IVIG",
        branded_unbranded: "Branded",
    },
    [FORM_TYPES_ORDER.CAREPLAN]: {
        careplan_optout: "Opt-In",
    },
};

function applyDefaultsToPrefill(jsonObject) {
    _.forEach(REQUIRED_FIELDS, (value, form) => {
        for (const key in jsonObject) {
            if (jsonObject[key]?.prefill && form === key) {
                const prefillData = jsonObject[key].prefill;
                if (Array.isArray(prefillData)) {
                    prefillData.forEach((item) =>
                        __copy_defaults_over(item, value)
                    );
                } else {
                    __copy_defaults_over(prefillData, value);
                }
            }
        }
    });
    return jsonObject;
}

function __copy_defaults_over(item, values) {
    for (const key in values) {
        item[key] = values[key];
    }
}

module.exports = {
    applyDefaultsToPrefill,
    FORM_TYPES_ORDER,
    FORM_TYPES_ORDER_ITEM,
};
