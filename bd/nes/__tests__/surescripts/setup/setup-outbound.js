"use strict";
const config = require("@tests/config");
const { loadJsonFile } = require("@tests/helper");
const _ = require("lodash");
const moment = require("moment-timezone");

const replacements = [
    { path: "site_id", value: config.TEST_SITE_ID },
    { path: "patient_id", value: config.TEST_PATIENT_ID },
    { path: "patient_first_name", value: config.TEST_PATIENT_FIRSTNAME },
    { path: "patient_last_name", value: config.TEST_PATIENT_LASTNAME },
    { path: "patient_dob", value: config.TEST_PATIENT_DOB },
    { path: "prescriber_npi", value: config.TEST_PHYSICIAN_NPI },
    { path: "to", value: config.TEST_PHYSICIAN_SPI },
    { path: "from", value: config.TEST_PHARMACY_SPI },
    { path: "supervisor_npi", value: config.TEST_SUPERVISOR_NPI },
    { path: "supervisor_phone", value: "**********" },
    { path: "supervisor_first_name", value: "<PERSON>y" },
    { path: "supervisor_last_name", value: "Mark" },
    { path: "physician_order_id", value: "110088" },
    {
        path: "written_date",
        value: moment().subtract(310, "days"),
        ignore_file: "error_renew_missing_written_date.json",
    },
];

const elms = {
    SUPERVISOR: "Message.Body.<MessageType>.Supervisor",
    DX: "Message.Body.<MessageType>.MedicationPrescribed.Diagnosis",
    BENEFITS: "Message.Body.<MessageType>.BenefitsCoordination",
    OBSERVATION: "Message.Body.<MessageType>.Measurement.VitalSign",
    COMPOUND:
        "Message.Body.<MessageType>.MedicationPrescribed.CompoundInformation",
    ALLERGIES: "Message.Body.<MessageType>.AllergyOrAdverseEvent",
    URGENT: "Message.Body.<MessageType>.UrgencyIndicatorCode",
    MEDICATION: "Message.Body.<MessageType>.MedicationPrescribed",
    HEADER: "Message.Header",
    PHARMACY: "Message.Body.<MessageType>.Pharmacy",
    PATIENT: "Message.Body.<MessageType>.Patient",
    PRESCRIBER: "Message.Body.<MessageType>.Prescriber",
    RESPONSE: "Message.Body.<MessageType>.Response",
    MEDICATION_REQUESTED: "Message.Body.<MessageType>.MedicationRequested",
    MEDICATION_DISPENSED: "Message.Body.RxRenewalRequest.MedicationDispensed",
    DUE: "Message.Body.<MessageType>.MedicationRequested.DrugUseEvaluation",
    CHANGE_REASON: "Message.Body.RxChangeRequest.ChangeReasonText",
    CHANGE_SUBCODE: "Message.Body.RxChangeRequest.MessageRequestSubCode",
    CHANGE_CODE: "Message.Body.RxChangeRequest.MessageRequestCode",
};

function checkForElement(obj, path, message_type) {
    path = path.replace("<MessageType>", message_type);
    const current = _.get(obj, path, undefined);
    return current !== undefined;
}

function fetchElementValue(obj, path, message_type) {
    path = path.replace("<MessageType>", message_type);
    return _.get(obj, path, undefined);
}

function applyReplacements(jsonObject, file) {
    replacements.forEach((mod) => {
        if (!mod?.ignore_file || mod?.ignore_file !== file) {
            _.set(jsonObject, mod.path, mod.value);
        }
    });
    return jsonObject;
}

async function loadThisJsonFile(directory, file) {
    return await loadJsonFile(directory, file, applyReplacements);
}

exports.elms = elms;
exports.loadJsonFile = loadThisJsonFile;
exports.checkForElement = checkForElement;
exports.fetchElementValue = fetchElementValue;
