"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-outbound.js");
const TIMEOUT = 10 * 1000;

const filename = "cancel_response_denied.json";

request.addDSLMocks("ss_message", true, null, "ss_message");

describe(
    "Check Responses",
    () => {
        it("Handles status 000", async () => {
            const message = await setup.loadJsonFile(
                "surescripts/outbound/status",
                filename
            );

            const body = message.body;
            const res = await request
                .post("/api/surescripts/outbound")
                .send(body)
                .addAdminMocks(
                    "/internal/surescripts/tx",
                    true,
                    "ss_response_000"
                )
                .addHeaders()
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(202);
            expect(res.body.ssrec.message_status).toEqual("Sent");
        });

        it("Handles status 010", async () => {
            const message = await setup.loadJsonFile(
                "surescripts/outbound/status",
                filename
            );

            const body = message.body;
            const res = await request
                .post("/api/surescripts/outbound")
                .send(body)
                .addAdminMocks(
                    "/internal/surescripts/tx",
                    true,
                    "ss_response_010"
                )
                .addHeaders()
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(202);
            expect(res.body.ssrec.message_status).toEqual("Verified");
        });

        it("Handles verify response 010", async () => {
            const message = await setup.loadJsonFile(
                "surescripts/outbound/status",
                filename
            );

            const body = message.body;
            const res = await request
                .post("/api/surescripts/outbound")
                .send(body)
                .addAdminMocks(
                    "/internal/surescripts/tx",
                    true,
                    "ss_response_010"
                )
                .addHeaders()
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(202);
            expect(res.body.ssrec.message_status).toEqual("Verified");
        });

        it("Handles errors", async () => {
            const message = await setup.loadJsonFile(
                "surescripts/outbound/status",
                filename
            );

            const body = message.body;
            const res = await request
                .post("/api/surescripts/outbound")
                .send(body)
                .addAdminMocks(
                    "/internal/surescripts/tx",
                    true,
                    "ss_response_error"
                )
                .addHeaders()
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(202);
            expect(res.body.ssrec.message_status).toEqual("Error");
        });
    },
    TIMEOUT
);
