"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-outbound.js");
const TIMEOUT = 10 * 1000;

const keys = [
    { filename: "cancel_response_approved.json" },
    { filename: "cancel_response_denied.json" },
    { filename: "error_cancel_has_disp_no_note.json" },
    { filename: "error_cancel_response_denied_missing_note.json" },
];

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");
        request.addAdminMocks(
            "/internal/surescripts/tx",
            true,
            "ss_response_verify"
        );
        request.addAdminMocks(
            "/internal/surescripts/directory/provider/live",
            false,
            "ss_dir_provider"
        );

        it.each(keys)(
            "Converts $filename correctly",
            async (obj) => {
                const message = await setup.loadJsonFile(
                    "surescripts/outbound/cancel",
                    obj.filename
                );
                let exp_res_code = 202;
                let had_dispense = false;
                if (obj.filename.includes("error")) {
                    had_dispense = true;
                    exp_res_code = 500;
                }

                const body = message.body;
                const res = await request
                    .post("/api/surescripts/outbound")
                    .send(body)
                    .setContextVar("had_dispense", had_dispense)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(exp_res_code);

                if (exp_res_code === 202) {
                    check_response_elements(res.body);
                    check_response_statuses(res.body);
                } else {
                    expect(res.body).toHaveProperty("Error");
                    expect(res.body.Error.length).toBeGreaterThan(0);
                }
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response) {
    expect(response).toHaveProperty("xml_json");
    expect(response).toHaveProperty("ssrec");

    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.HEADER,
            "CancelRxResponse"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.RESPONSE,
            "CancelRxResponse"
        )
    ).toBeTruthy();
}

function check_response_statuses(response) {
    const ssrec = response.ssrec;
    expect(ssrec.message_status).toBe("Verified");
    expect(ssrec.status_icons).toContain("sent");
}
