"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        it(
            "Checks that you can edit a message with no dispenses",
            async () => {
                await request
                    .get("/api/surescripts/?func=check_edit&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .setContextVar("had_dispense", false)
                    .addHeaders()
                    .expect(200);
            },
            TIMEOUT
        );

        it(
            "Checks that you can't edit an outbound message that has been verified",
            async () => {
                await request
                    .get("/api/surescripts/?func=check_edit&form_id=1")
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message_refill_verified"
                    )
                    .setContextVar("had_dispense", false)
                    .addHeaders()
                    .expect(503);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
