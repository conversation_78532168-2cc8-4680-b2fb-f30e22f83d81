"use strict";
const request = require("@tests/nes-request").request();
const setup = require("./setup/setup-outbound.js");
const TIMEOUT = 10 * 1000;

const filename = "follow_up.json";

describe(
    "Check Responses",
    () => {
        it(
            "Checks for follow-up status and counts",
            async () => {
                const message = await setup.loadJsonFile(
                    "surescripts/outbound/followup",
                    filename
                );
                let exp_res_code = 202;
                if (filename.includes("error")) {
                    exp_res_code = 500;
                }

                const body = message.body;

                const res = await request
                    .post("/api/surescripts/outbound?followup=true")
                    .send(body)
                    .addDSLMocks(
                        "ss_message",
                        true,
                        ["message_type"],
                        "ss_message"
                    )
                    .addAdminMocks(
                        "/internal/surescripts/tx",
                        true,
                        "ss_response_verify"
                    )
                    .addAdminMocks(
                        "/internal/surescripts/directory/provider/live",
                        false,
                        "ss_dir_provider"
                    )
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(exp_res_code);

                check_response_elements(res.body);
                check_followup_count(res.body);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);

function check_response_elements(response) {
    expect(response).toHaveProperty("xml_json");
    expect(response).toHaveProperty("ssrec");

    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.HEADER,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PHARMACY,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PATIENT,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.PRESCRIBER,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
    expect(
        setup.checkForElement(
            response.xml_json,
            setup.elms.MEDICATION_DISPENSED,
            "RxRenewalRequest"
        )
    ).toBeTruthy();
}

function check_followup_count(response) {
    const ssrec = response.ssrec;
    expect(ssrec.followup_count).toBe(1);
    expect(ssrec.message_status).toEqual("Verified");
    expect(ssrec.subform_followup.length).toBeGreaterThan(0);
}
