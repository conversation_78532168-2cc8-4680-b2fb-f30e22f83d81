"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "cob_segment.json");
    nes = file.body;
});
describe("Testing COB Segment", () => {
    it("handles COB segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPCOBSegment(
            nes.transaction_code,
            nes.insurance,
            nes.payer,
            nes.opayer,
            nes.parent_claim,
            nes.list_reject_codes
        );
        console.log("<<<<<<<<<<<<result>>>>>>>>>>>>", result);
        expect(result).toBeTruthy();
    });
    it("handles COB segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPCOBSegment(
            nes.transaction_code,
            nes.insurance,
            nes.payer,
            nes.opayer,
            nes.parent_claim,
            nes.list_reject_codes
        );
        expect(result).toBeTruthy();
    });
    it("handles COB segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPCOBSegment(
            nes.transaction_code,
            nes.insurance,
            nes.payer,
            nes.opayer
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
