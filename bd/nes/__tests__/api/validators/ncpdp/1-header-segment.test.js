"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "header_segment.json");
    nes = file.body;
});
describe("Testing Header Segment", () => {
    it("handles header segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildHeaderSegment(
            nes.site,
            nes.insurance,
            nes.payer,
            nes.careplan_order,
            nes.careplan_order_item,
            nes.inventory
        );
        console.log("=====resel===", result);
        expect(result).toBeTruthy();
    });
    it("handles header segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildHeaderSegment(
            nes.site,
            nes.insurance,
            nes.payer
        );
        expect(result).toBeTruthy();
    });
    it("handles header segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildHeaderSegment(
            (nes.site = null),
            nes.insurance,
            nes.payer
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
