"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "pharmacy_segment.json");
    nes = file.body;
});
describe("Testing Pharmacy Segment", () => {
    it("handles Pharmacy segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPharmacySegment(
            nes.payer,
            nes.site,
            nes.pharmacist
        );
        console.log("<<<<<<<<<<<<result>>>>>>>>>>>>", result);
        expect(result).toBeTruthy();
    });
    it("handles Pharmacy segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPharmacySegment(
            nes.payer,
            nes.site,
            nes.pharmacist
        );
        expect(result).toBeTruthy();
    });
    it("handles Pharmacy segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPharmacySegment(
            nes.payer,
            nes.site
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
