"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "clinical_segment.json");
    nes = file.body;
});
describe("Testing Clinical Segment", () => {
    it("handles Clinical segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPClinicalSegment(
            nes.payer,
            nes.diagnosis
        );
        console.log("<<<<<<<<<<<<result>>>>>>>>>>>>", result);
        expect(result).toBeTruthy();
    });
    it("handles Clinical segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPClinicalSegment(
            nes.payer,
            nes.diagnosis
        );
        expect(result).toBeTruthy();
    });
    it("handles Clinical segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPClinicalSegment(nes.payer);
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
