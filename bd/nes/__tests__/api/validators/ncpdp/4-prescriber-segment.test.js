"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "prescriber_segment.json");
    nes = file.body;
});
describe("Testing Prescriber Segment", () => {
    it("handles Prescriber segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPrescriberSegment(
            nes.transaction_code,
            nes.payer,
            nes.insurance,
            nes.prescriber,
            nes.primary_physician
        );
        console.log("=====resel===", result);
        expect(result).toBeTruthy();
    });
    it("handles Prescriber segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPrescriberSegment(
            nes.transaction_code,
            nes.payer,
            nes.insurance,
            nes.prescriber,
            nes.primary_physician
        );
        expect(result).toBeTruthy();
    });
    it("handles Prescriber segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPrescriberSegment(
            nes.transaction_code,
            nes.payer,
            nes.insurance,
            nes.prescriber
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
