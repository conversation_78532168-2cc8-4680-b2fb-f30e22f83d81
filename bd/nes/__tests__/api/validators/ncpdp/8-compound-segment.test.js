"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "compound_segment.json");
    nes = file.body;
});
describe("Testing Compound Segment", () => {
    it("handles Compound segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPCompoundSegment(
            nes.payer,
            nes.inventory_items
        );
        console.log("<<<<<<<<<<<<result>>>>>>>>>>>>", result);
        expect(result).toBeTruthy();
    });
    it("handles Compound segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPCompoundSegment(
            nes.payer,
            nes.inventory_items
        );
        expect(result).toBeTruthy();
    });
    it("handles Compound segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPCompoundSegment(nes.payer);
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
