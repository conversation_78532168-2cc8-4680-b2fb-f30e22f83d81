"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "pricing_segment.json");
    nes = file.body;
});
describe("Testing Pricing Segment", () => {
    it("handles Pricing segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPricingSegment(
            nes.transaction_code,
            nes.site,
            nes.payer,
            nes.tax,
            nes.inventory_items,
            nes.parent_claim
        );
        console.log("<<<<<<<<<<<<result>>>>>>>>>>>>", result);
        expect(result).toBeTruthy();
    });
    it("handles Pricing segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPricingSegment(
            nes.transaction_code,
            nes.site,
            nes.payer,
            nes.tax,
            nes.inventory_items
        );
        expect(result).toBeTruthy();
    });
    it("handles Pricing segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPPricingSegment(
            nes.transaction_code,
            nes.site,
            nes.payer,
            nes.tax
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
