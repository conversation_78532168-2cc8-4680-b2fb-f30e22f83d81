"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "claim_segment.json");
    nes = file.body;
});
describe("Testing Claim Segment", () => {
    it("handles Claim segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPClaimSegment(
            nes.transaction_code,
            nes.dispense_obj,
            nes.parent_claim,
            nes.partial_fill_claim,
            nes.compound_id
        );
        console.log("<<<<<<<<<<<<result>>>>>>>>>>>>", result);
        expect(result).toBeTruthy();
    });
    it("handles Claim segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPClaimSegment(
            nes.transaction_code,
            nes.dispense_obj
        );
        expect(result).toBeTruthy();
    });
    it("handles Claim segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPClaimSegment(nes.transaction_code);
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
