"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "insurance_segment.json");
    nes = file.body;
});
describe("Testing Inusurance Segment", () => {
    it("handles Inusurance segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPInsuranceSegment(
            nes.transaction_code,
            nes.patient,
            nes.insurance,
            nes.payer,
            nes.parent_claim
        );
        console.log("=====resel===", result);
        expect(result).toBeTruthy();
    });
    it("handles Inusurance segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPInsuranceSegment(
            nes.transaction_code,
            nes.patient,
            nes.insurance,
            nes.payer
        );
        expect(result).toBeTruthy();
    });
    it("handles Inusurance segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildNCPDPInsuranceSegment(
            nes.transaction_code,
            nes.patient,
            nes.insurance,
            (nes.payer = "")
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
