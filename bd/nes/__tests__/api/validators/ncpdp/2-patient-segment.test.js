"use strict";
const NCPDP = require("@api/form/validators/ncpdp");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("/dsl/ncpdp/", "patient_segment.json");
    nes = file.body;
});
describe("Testing Patient Segment", () => {
    it("handles patient segment all params are given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildPatientSegment(
            nes.transaction_code,
            nes.patient,
            nes.insurance,
            nes.payer,
            nes.pregnancy_indicator
        );
        console.log("=====resel===", result);
        expect(result).toBeTruthy();
    });
    it("handles header segment optional params are not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildPatientSegment(
            nes.transaction_code,
            nes.patient,
            nes.insurance,
            nes.payer
        );
        expect(result).toBeTruthy();
    });
    it("handles header segment if any of required paramter is empty or not given", async () => {
        const ncpdp = new NCPDP();
        const result = await ncpdp.BuildPatientSegment(
            nes.transaction_code,
            nes.patient,
            (nes.insurance = {}),
            nes.payer
        );
        console.log("=====resel===", result);
        expect(result.error).toBeTruthy();
    });
});
