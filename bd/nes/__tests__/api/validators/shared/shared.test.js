"use strict";
const SharedValidators = require("@form/validators/shared");
const { loadJsonFile } = require("@tests/helper");

let nes = null;
beforeAll(async () => {
    const file = await loadJsonFile("nes", "nes_pass_medium.json");
    nes = file.body;
});

describe("Testing Password Checker", () => {
    it("handles null passwords", async () => {
        const sharedValidators = new SharedValidators();
        const result = await sharedValidators.PasswordStrengthValidator(
            nes,
            null,
            "test",
            { value: null }
        );
        expect(result.error).toBeTruthy();
    });

    it("handles weak passwords", async () => {
        const sharedValidators = new SharedValidators();
        const result = await sharedValidators.PasswordStrengthValidator(
            nes,
            null,
            "test",
            { value: "a" }
        );
        expect(result.error).toBeTruthy();
    });

    it("handles strong passwords", async () => {
        const sharedValidators = new SharedValidators();
        const result = await sharedValidators.PasswordStrengthValidator(
            nes,
            null,
            "test",
            { value: "v4V,L2M6,$3C" }
        );
        expect(result).toBeNull();
    });
});
