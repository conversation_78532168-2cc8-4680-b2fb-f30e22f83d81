"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it(
            "Returns no errors for a provider with a valid CS service level",
            async () => {
                const body = {
                    name: "CSServiceLevel",
                    form: "ss_chg_med",
                    field_vals: {
                        dea_schedule_id: "C48675",
                        to: "12345",
                    },
                };

                request.addAdminMocks(
                    "/internal/surescripts/directory/provider/live",
                    true,
                    "ss_dir_provider"
                );

                const res = await request
                    .post("/api/form/ss_message/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );

        it(
            "Returns an error for a provider with a no CS service level",
            async () => {
                const body = {
                    name: "CSServiceLevel",
                    form: "ss_chg_med",
                    field_vals: {
                        dea_schedule_id: "C48675",
                        to: "12345",
                    },
                };

                request.addAdminMocks(
                    "/internal/surescripts/directory/provider/live",
                    true,
                    "ss_dir_provider_no_service_levels"
                );

                const res = await request
                    .post("/api/form/ss_message/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
