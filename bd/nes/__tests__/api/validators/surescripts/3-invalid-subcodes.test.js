"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it(
            "Returns no errors for a change request of benefit plan",
            async () => {
                const body = {
                    name: "CheckSubCodeCombinations",
                    form: "ss_message",
                    field_vals: {
                        chg_type_sc_id: ["H"],
                        chg_type_id: "U",
                    },
                };

                const res = await request
                    .post("/api/form/ss_message/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );

        it(
            "Returns an error for for H & J change subtype combinations",
            async () => {
                const body = {
                    name: "CheckSubCodeCombinations",
                    form: "ss_message",
                    field_vals: {
                        chg_type_sc_id: ["H", "J"],
                        chg_type_id: "U",
                    },
                };

                const res = await request
                    .post("/api/form/ss_message/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
