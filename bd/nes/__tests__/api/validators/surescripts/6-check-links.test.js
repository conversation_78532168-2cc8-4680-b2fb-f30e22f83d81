"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;
const config = require("@tests/config");

describe(
    "Check Responses",
    () => {
        it(
            "Checks if order change and had a dispense that you get an error",
            async () => {
                const body = {
                    name: "SSCheckLinks",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        patient_id: 2,
                        physician_id: 2,
                        pharmacy_order_id: 2,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa"}',
                    },
                };

                const ss_message = {
                    form: "ss_message",
                    patient_id: 2,
                    physician_id: 2,
                    pharmacy_order_id: 3,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "<PERSON><PERSON>",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [ss_message])
                    .setContextVar("had_dispense", true, false)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Checks if order change and and old order not voided, that an error is generated",
            async () => {
                const body = {
                    name: "SSCheckLinks",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        patient_id: 2,
                        physician_id: 2,
                        pharmacy_order_id: 3,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa"}',
                    },
                };

                const ss_message = {
                    void: null,
                    form: "ss_message",
                    patient_id: 2,
                    physician_id: 2,
                    physician_order_id: 2,
                    pharmacy_order_id: config.TEST_ORDER_ID,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "Karly",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };

                const careplan_order = {
                    form: "careplan_order",
                    void: null,
                    physician_order_id: 2,
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [
                        ss_message,
                        careplan_order,
                    ])
                    .setContextVar("had_dispense", false, false)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Checks if order change and and old order has SS prescription and order not voided, that it throws an error",
            async () => {
                const body = {
                    name: "SSCheckLinks",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        patient_id: 2,
                        physician_id: 2,
                        pharmacy_order_id: 3,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa"}',
                    },
                };

                const ss_message = {
                    form: "ss_message",
                    patient_id: 2,
                    physician_id: 2,
                    physician_order_id: 2,
                    pharmacy_order_id: config.TEST_ORDER_ID,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "Karly",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };

                const careplan_order = {
                    void: null,
                    form: "careplan_order",
                    physician_order_id: null,
                    subform_items: [{ physician_order_id: 2 }],
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [
                        ss_message,
                        careplan_order,
                    ])
                    .setContextVar("had_dispense", false, false)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Checks if order has changed but order isn't linked through prescriptions that no error is throw",
            async () => {
                const body = {
                    name: "SSCheckLinks",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        patient_id: 2,
                        physician_id: 2,
                        pharmacy_order_id: 2,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa"}',
                    },
                };

                const ss_message = {
                    form: "ss_message",
                    patient_id: 2,
                    physician_id: 2,
                    physician_order_id: 2,
                    pharmacy_order_id: config.TEST_ORDER_ID,
                    subform_benefit: [
                        {
                            direction: "IN",
                            payer_type_id: "H",
                            payer_level: "P",
                            payer_name: "BCBS Test",
                            bin: config.TEST_PAYER_BIN,
                            pcn: config.TEST_PAYER_PCN,
                            cardholder_first_name: "Karly",
                            cardholder_last_name: "Whiteside",
                        },
                    ],
                };

                const careplan_order = {
                    form: "careplan_order",
                    void: null,
                    physician_order_id: null,
                    subform_items: [{ physician_order_id: null }],
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addDSLMocks("ss_message", true, null, "ss_message")
                    .setContextVar("default_dsl_vars", [
                        ss_message,
                        careplan_order,
                    ])
                    .setContextVar("had_dispense", false, false)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
