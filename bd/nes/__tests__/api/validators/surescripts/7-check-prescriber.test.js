"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;
const config = require("@tests/config");

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it(
            "Returns an error if the provider isn't listed as a prescriber for the patient",
            async () => {
                const body = {
                    name: "SSGeneratorCheckPrescriber",
                    form: "view_ss_order_generator",
                    field_vals: {
                        physician_id: config.TEST_PHYSICIAN_ID,
                        patient_prescriber_id: 1,
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns no error if the physician is linked to the patient",
            async () => {
                const body = {
                    name: "SSGeneratorCheckPrescriber",
                    form: "view_ss_order_generator",
                    field_vals: {
                        physician_id: config.TEST_PHYSICIAN_ID,
                        patient_prescriber_id: config.TEST_PRESCRIBER_LINK_ID,
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
