"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it(
            "Returns no errors for a refill request with no DEA schedule",
            async () => {
                const body = {
                    name: "DEAMAXRefills",
                    form: "ss_chg_med",
                    field_vals: {
                        refills: 12,
                        dea_schedule_id: null,
                    },
                };

                const res = await request
                    .post("/api/form/ss_chg_med/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );

        it(
            "Returns an error for a scheduled 2 drug with refills",
            async () => {
                const body = {
                    name: "DEAMAXRefills",
                    form: "ss_chg_med",
                    field_vals: {
                        dea_schedule_id: "C48675",
                        refills: 2,
                    },
                };

                const res = await request
                    .post("/api/form/ss_chg_med/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns an error for a scheduled 3 drug with 6 refills",
            async () => {
                const body = {
                    name: "DEAMAXRefills",
                    form: "ss_message",
                    field_vals: {
                        dea_schedule_id: "C48676",
                        request_refills: 6,
                    },
                };

                const res = await request
                    .post("/api/form/ss_message/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
