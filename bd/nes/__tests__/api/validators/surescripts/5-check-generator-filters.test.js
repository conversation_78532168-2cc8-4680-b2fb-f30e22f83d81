"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it(
            "Returns an error if ss_message ID is missing",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: null,
                        events: '{"patient", "diagnosis", "physician", "prescriber", "insurance", "careplan", "intake", "pa", "order"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns an error if site_id ID is missing",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        site_id: null,
                        events: '{"patient", "diagnosis", "physician", "prescriber", "insurance", "careplan", "intake", "pa", "order"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns an error if patient_id is missing and not generating patient record",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        site_id: 1,
                        patient_id: null,
                        events: '{"diagnosis", "physician", "prescriber", "insurance", "careplan", "intake", "pa", "order"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns an error if physician ID is missing and not generating a physician record",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        site_id: 1,
                        patient_id: null,
                        physician_id: null,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa", "order"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns an error if patient prescriber ID is missing and not generating prescriber record",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        site_id: 1,
                        patient_id: 123,
                        physician_id: 123,
                        patient_prescriber_id: null,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa", "order"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns an error if not generating an order and order ID is missing",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        site_id: 1,
                        patient_id: 123,
                        physician_id: 123,
                        patient_prescriber_id: 123,
                        pharmacy_order_id: null,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns no error if all checks pass",
            async () => {
                const body = {
                    name: "SSGeneratorCheckValidateFilters",
                    form: "view_ss_order_generator",
                    field_vals: {
                        ss_message_id: 123,
                        site_id: 1,
                        patient_id: 123,
                        physician_id: 123,
                        patient_prescriber_id: 123,
                        pharmacy_order_id: 343,
                        events: '{"patient", "diagnosis", "insurance", "careplan", "intake", "pa", "order_item"}',
                    },
                };

                const res = await request
                    .post("/api/form/view_ss_order_generator/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
