"use strict";
const request = require("@tests/nes-request").request();
const TIMEOUT = 10 * 1000;

describe(
    "Check Responses",
    () => {
        request.addDSLMocks("ss_message", true, null, "ss_message");

        it(
            "Returns no errors for an unscheduled drug",
            async () => {
                const body = {
                    name: "DEADaySupply",
                    form: "ss_chg_med",
                    field_vals: {
                        dea_schedule_id: null,
                        day_supply: 100,
                    },
                };

                const res = await request
                    .post("/api/form/ss_chg_med/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );

        it(
            "Returns an error for a scheduled 2 drug with 91 day supply",
            async () => {
                const body = {
                    name: "DEADaySupply",
                    form: "ss_chg_med",
                    field_vals: {
                        dea_schedule_id: "C48675",
                        day_supply: 91,
                    },
                };

                const res = await request
                    .post("/api/form/ss_chg_med/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                expect(res?.body?.error?.length).toBeGreaterThan(0);
            },
            TIMEOUT
        );

        it(
            "Returns no errors for a scheduled 3 drug with 30 day supply",
            async () => {
                const body = {
                    name: "DEADaySupply",
                    form: "ss_chg_med",
                    field_vals: {
                        dea_schedule_id: "C48676",
                        day_supply: 30,
                    },
                };

                const res = await request
                    .post("/api/form/ss_chg_med/validate")
                    .send(body)
                    .addHeaders()
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const error = res?.body?.error || null;
                expect(error).toBeNull();
            },
            TIMEOUT
        );
    },
    TIMEOUT
);
