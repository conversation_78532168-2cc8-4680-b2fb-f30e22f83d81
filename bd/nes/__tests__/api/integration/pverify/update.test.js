"use strict";
const request = require("@tests/nes-request").request();
const SECONDS = 1000;
describe(
    "Update PVerify Filehash",
    () => {
        it(
            "updates records with filehash",
            async () => {
                const testData = {
                    RequestId: "122620313",
                    fileHash:
                        "cHZlcmlmeS9lc2hvYWliLzEyMzQ1L2VsaWdpYmlsaXR5XzEyMjYwMTU1OF9ZWEYxMzI5Nzc5MTUwMDEucGRm",
                };

                const res = await request
                    .post(`/api/pverify/eligibility/update`)
                    .send(testData)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.success).toBe(true);
            },
            3 * SECONDS
        );
    },
    1 * SECONDS
);
