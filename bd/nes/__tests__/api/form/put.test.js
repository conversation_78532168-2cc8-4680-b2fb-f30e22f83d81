"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");
const { loadJsonFile } = require("@tests/helper");

const SECONDS = 1000;

describe(
    "Check form put responses",
    () => {
        it(
            "put on an existing patient",
            async () => {
                const message = await loadJsonFile(
                    "dsl/patient",
                    "test_patient_put.json"
                );

                const res = await request
                    .put(`/api/form/patient/${config.TEST_PATIENT_ID}`)
                    .send(message.body)
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.id).toBeDefined();
                expect(body.firstname).toBeDefined();
            },
            10 * SECONDS
        );

        it(
            "puts with an invalid user",
            async () => {
                const message = await loadJsonFile(
                    "dsl/po",
                    "new_po_with_subforms.json"
                );
                const res = await request
                    .put(`/api/form/sales_manager/1`)
                    .send(message.body)
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);

                const body = res.body;
                expect(body.error).toBe("Access denied.");
            },
            10 * SECONDS
        );

        it(
            "archives a form",
            async () => {
                const res = await request
                    .put(`/api/form/patient/${config.TEST_PATIENT_ID}/archive/`)
                    .send({ archived: true })
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.archived).toBe(true);
            },
            10 * SECONDS
        );

        it(
            "unarchives a form",
            async () => {
                const res = await request
                    .put(`/api/form/patient/${config.TEST_PATIENT_ID}/archive/`)
                    .send({ archived: false })
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.archived).toBe(false);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
