"use strict";
const request = require("@tests/nes-request").request();
const config = require("@tests/config");

const SECONDS = 1000;

describe(
    "Check form get responses",
    () => {
        it(
            "fetches multiple records",
            async () => {
                const res = await request
                    .get("/api/form/patient?limit=10")
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.length).toBeGreaterThan(0);
                expect(body[0].id).toBeDefined();
                expect(body[0].firstname).toBeDefined();
            },
            30 * SECONDS
        );

        it(
            "fetches a single record",
            async () => {
                const res = await request
                    .get(`/api/form/patient/${config.TEST_PATIENT_ID}`)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.length).toBeGreaterThan(0);
                expect(body[0].id).toBeDefined();
                expect(body[0].firstname).toBeDefined();
            },
            10 * SECONDS
        );

        it(
            "performs filtering correctly",
            async () => {
                const res = await request
                    .get(
                        `/api/form/patient?filter=id:${config.TEST_PATIENT_ID}`
                    )
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.length).toBeGreaterThan(0);
                expect(body[0].id).toBeDefined();
                expect(body[0].firstname).toBeDefined();
            },
            10 * SECONDS
        );

        it(
            "fetches mulitple records",
            async () => {
                const res = await request
                    .get("/api/form/po?limit=10")
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.length).toBeGreaterThan(0);
                expect(body[0].id).toBeDefined();
                expect(body[0].subform_items).toBeDefined();
            },
            10 * SECONDS
        );

        it(
            "fetches a single record and checks subforms",
            async () => {
                const res = await request
                    .get(`/api/form/po/${config.TEST_PO_ID}`)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.length).toBeGreaterThan(0);
                expect(body[0].id).toBeDefined();
                expect(body[0].subform_items.length).toBeGreaterThan(0);
            },
            10 * SECONDS
        );

        it(
            "fetches a non-existent patient",
            async () => {
                await request
                    .get("/api/form/patient/999999")
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);
            },
            10 * SECONDS
        );

        it(
            "fetches a non-existent record with filtering",
            async () => {
                const res = await request
                    .get(`/api/form/patient?filter=id:9999999`)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.length).toBe(0);
            },
            10 * SECONDS
        );
        it(
            "fetches a list of users",
            async () => {
                await request
                    .get("/api/form/sales_manager")
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);
            },
            10 * SECONDS
        );

        it(
            "fetches a unauthorized record by id",
            async () => {
                const res = await request
                    .get(`/api/form/sales_manager/1`)
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);

                const body = res.body;
                expect(body.error.length).toBeGreaterThan(0);
            },
            10 * SECONDS
        );

        it(
            "fetches a unauthorized record by filtering",
            async () => {
                const res = await request
                    .get(`/api/form/sales_manager?filter=id:1`)
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);

                const body = res.body;
                expect(body.error.length).toBeGreaterThan(0);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
