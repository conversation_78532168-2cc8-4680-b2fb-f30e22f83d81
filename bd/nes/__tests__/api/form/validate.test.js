"use strict";
const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");

const SECONDS = 1000;

describe(
    "Check form validate responses",
    () => {
        it(
            "test whole form validation failure",
            async () => {
                const message = await loadJsonFile(
                    "dsl/patient",
                    "test_patient_post.json"
                );
                message.body.dob = "abc";
                const res = await request
                    .post(`/api/form/patient`)
                    .send(message.body)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);

                const body = res.body;
                expect(body.error.length).toBeGreaterThan(0);
            },
            10 * SECONDS
        );

        it(
            "test whole form validation success",
            async () => {
                const message = await loadJsonFile(
                    "dsl/patient",
                    "test_patient_post.json"
                );
                message.body._meta.validate_only = true;
                await request
                    .post(`/api/form/patient`)
                    .send(message.body)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
