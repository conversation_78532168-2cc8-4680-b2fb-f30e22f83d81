"use strict";
const request = require("@tests/nes-request").request();
const { loadJsonFile } = require("@tests/helper");

const SECONDS = 1000;

describe(
    "Check form post responses",
    () => {
        it(
            "post a new patient",
            async () => {
                const message = await loadJsonFile(
                    "dsl/patient",
                    "test_patient_post.json"
                );

                const res = await request
                    .post(`/api/form/patient`)
                    .send(message.body)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.id).toBeDefined();
                expect(body.firstname).toBeDefined();
            },
            10 * SECONDS
        );
        it(
            "post a new po with subforms",
            async () => {
                const message = await loadJsonFile(
                    "dsl/po",
                    "new_po_with_subforms.json"
                );

                const res = await request
                    .post(`/api/form/po`)
                    .send(message.body)
                    .addHeaders(false)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(200);

                const body = res.body;
                expect(body.id).toBeDefined();
                expect(body.subform_items.length).toBeGreaterThan(0);
            },
            10 * SECONDS
        );
        it(
            "post an invalid form",
            async () => {
                const message = await loadJsonFile(
                    "dsl/po",
                    "new_po_with_subforms.json"
                );

                const _res = await request
                    .post(`/api/form/nonexistantform`)
                    .send(message.body)
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);
            },
            10 * SECONDS
        );

        it(
            "posts to an unauthorized form",
            async () => {
                const message = await loadJsonFile(
                    "dsl/po",
                    "new_po_with_subforms.json"
                ); // Actual content doesn't matter here
                const res = await request
                    .post(`/api/form/sales_manager`)
                    .send(message.body)
                    .addHeaders(true)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .expect(400);

                const body = res.body;
                expect(body.error).toBe("Access denied.");
            },
            10 * SECONDS
        );
    },
    10 * SECONDS
);
