"use strict";
const fs = require("fs-extra");
const path = require("path");
const fxp = require("fast-xml-parser");
/**
 * This function loads a JSON file from a directory, parses it, and optionally post-processes it.
 * @param {string} directory - The directory where the JSON file is located.
 * @param {string} file - The name of the JSON file.
 * @param {function} post_process_fn - An optional function to post-process the parsed JSON.
 * @returns {Object} An object containing the filename and the parsed JSON body.
 */

exports.loadJsonFile = async function (
    directory,
    file,
    post_process_fn = null
) {
    directory = path.join(__dirname, "../__mocks__", directory);
    if (path.extname(file) === ".json") {
        const filePath = path.join(directory, file);
        let jsonObject = await fs.readFile(filePath, "utf8");
        jsonObject = JSON.parse(jsonObject);
        if (post_process_fn) {
            jsonObject = post_process_fn(jsonObject, file);
        }
        const fileNameKey = path.basename(file, ".json");
        return { filename: fileNameKey, body: jsonObject };
    }
};

/**
 * This function converts an XML file to JSON, optionally post-processing it.
 * @param {string} filePath - The path to the XML file.
 * @param {object} params - Optional parameters for post-processing.
 * @param {function} post_process_fn - An optional function to post-process the parsed JSON.
 * @returns {Object} The parsed JSON body.
 */

async function convertXmlToJson(filePath, params, post_process_fn) {
    const xmlContent = await fs.readFile(filePath, "utf8");

    const xmlParser = new fxp.XMLParser({ ignoreAttributes: true });
    try {
        let jsonContent = xmlParser.parse(xmlContent);
        if (post_process_fn) {
            jsonContent = post_process_fn(jsonContent, params, xmlContent);
        }
        return jsonContent;
    } catch (err) {
        console.log(err);
        console.error(
            `Failed to parse ${filePath}, it might be malformed XML.`
        );
        return {};
    }
}

/**
 * This function loads an XML file from a directory, converts it to JSON, and optionally post-processes it.
 * @param {string} directory - The directory where the XML file is located.
 * @param {string} file - The name of the XML file.
 * @param {object} params - Optional parameters for post-processing.
 * @param {function} post_process_fn - An optional function to post-process the parsed JSON.
 * @returns {Object} An object containing the filename and the parsed XML JSON body.
 */

exports.loadXMLJsonFile = async function (
    directory,
    file,
    params = null,
    post_process_fn = null
) {
    directory = path.join(__dirname, "../__mocks__", directory);
    const filePath = path.join(directory, file);
    const jsonObject = await convertXmlToJson(
        filePath,
        params,
        post_process_fn
    );
    const fileNameKey = path.basename(file, ".xml");
    return { filename: fileNameKey, body: jsonObject };
};
