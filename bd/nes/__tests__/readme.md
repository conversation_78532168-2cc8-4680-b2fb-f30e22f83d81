# Unit Testing / System Testing

Summary of how to add new unit tests into the unit tests framework

## Table of Contents

- [Requirements](#installation)
- [Usage](#usage)
- [Settings](#settings)
- [Gotchas](#gotchas)

## Requirements

Please install the following dependencies before attempting to run any unit tests
*npm install --save-dev @jest/globals*
*npm install --save-dev jest*
*npm install --save-dev supertest*
*npm install --save-dev node-notifier*

## Usage

All test files should have the .test.js extension as that is what <PERSON><PERSON> looks for before
running the test.

It is recommended to install the Jest Runner extension for VS Code which adds a right-click
option where you can select the entire folder or individual file and select "Run Jest on Path"

Mocks / stub data should be stored in __mocks__ and can be set for nes to use in the following
manner:

To test NES API calls, use nes-request by including it at the top of your test file.
*const request = require('@tests/nes-request').request();*

NESRequest is a wrapper for supertest that includes helper functions for adding headers and
sending the mocks.

You can use the following options when making an nes call:
**result = await request.post(url).body(json_body)**
**result = await request.put(url).body(json_body)**
**result = await request.get(url)**

Make sure to call *.addHeaders(use_csr_user)* after your request method by chaining:
*user_csr_user* = true/false, true means use test CSR user, otherwise uses Admin user
i.e. *result = await request.post(url).body(json_body).addHeaders(false)*

Then add your mocks, either for the DSL or Admin server using the following:
*clear_mocks* will clear the associated mock types before adding the new one
*respect_filters* is an array of field names to respect the filters on so you can
add multiple mocks for the same DSL form and only return the ones that match the filters.

*request.addDSLMocks(form_name, clear_mocks, respect_filters, ...filenames)*
returns all files in the form_name result on api/form/get requests

You can also set context specific variables during run time using
*request.setContextVar(key, value, clear = true)*
These will be available under ctx.test_vars as properties

You can use this to define default variables on mock or live forms using the default_dsl_vars context variables array
like this:
*request.setContextVar('default_dsl_vars', [{form: 'ss_message', message_type: 'RxRenewalRequest', prohibit_renewal_request: 'true'}])*
This will assign message_type, and prohibit_renewal_request to all ss_message forms that are fetch. If you don't define form:,
it will assign the properties to all **MOCK DSL forms only**. form must be defined for it to load against live forms from the db.

*request.addAdminMocks(url, clear_mocks, filename)*

NOTE: If your globals.__LIVE_ADMIN_SERVER__ is set to true in your jest.config.js,
admin mocks will be skipped to test against the live admin server with your tests directly.

Finally you can chain expected calls at the end of your test. (see supertest for examples: https://www.npmjs.com/package/supertest)
*result.expect(status_code)*
*result.expect(header, value)*

Documentation for jest and how to define tests can be found here (https://jestjs.io)

Finally, *helper.js* includes helper functions to load XML or JSON files from __mocks__ directory if you need to send something to the server in a PUT/POST request.

## Settings

The server that you run the tests against is located in *__tests__/config.js*. Make sure to also set the matching FLY secrets or /.env file for BASIC_AUTH_USERNAME, BASIC_AUTH_PASSWORD, TEST_USERNAME, and TEST_PASSWORD.

## Gotchas

Jest has a beforeAll function that should run before tests are run. However, do not use
this to load data asyncronously or any data that will be included in a it.each function.

Jest loads the tests along with beforeAll so if you attempt to loop on the data you setup
in beforeAll, it will not be available to you and will throw an error.

If your requests are timing out, you may need to increase the timeout on the test.
You can do that by passing in a milliseconds argument as the 2nd parameter in the describe or
test function.