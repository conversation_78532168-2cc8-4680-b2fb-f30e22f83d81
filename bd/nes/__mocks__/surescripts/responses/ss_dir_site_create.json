{"request_hash": {"key": "MhoJGsXZ/LE3a95ED1eoE4KYUF24EwErqrXECvCWOGg6jjcNVsTSICo+QkE0CpHbIz4YWkPtzP85CKiw7we4+wifqCPLX7UmX18jEOcZCGaK47XCsWdImhyfrvRg65qHUgazzCC4elOlZXTT1Dnx1yF8uepH84OU8ynUDpL9l8dtfyQ2GbtUFtgoyZlXFswGGeckXvHqee6FuzvGOAHdjB8EGavz/CvuTetlzL+ULVG1VpU91FxmeKE4Hm/VseJXCJi3mcFzJ27k1IZGTq6LDeYgf+N/OLZE05Twa3YuyViXs9CLubdlhqWjbNSGaszdiXCOEc7GiDuj3FqZ7DuR+vIBWUPDZZibxL9LrgyZFRvRMLzE+R6jHaY77VN+P3GvAvKNJd/pdVgnFQgs6P4Dupr6oVRUe9SKWVHVCVK8+TGQfKFiT8nlj9KAQpX738CBRcJt3Xn38UpBVFIXe1V4bAHBzapYJC+DiG7J2sKqj3y2tLeGCuMxs4QGQnRvxqEchHVIf5e2RNED/sZejinJMYPez4epsyogX3LdN1sQwtaNpEmeTYaFmUbFTYff2sWF5yaUIavwr7/KMushtBRdciorKs5pVBpVE1Qhxkz3G5r7PYtOZbrNKrhvevE91KLShUfroCrqd6mo3lnYDa8azNwGbAQe1oEeeYqvAHfzin8=", "data": "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"}, "request_json_data": {"DirectoryMessage": {"Header": {"To": "SSDR61", "From": "ENVD61", "MessageID": "1e01bcdfa50a48d3be89892a454ae7db", "SentTime": "2024-05-08T09:55:55.948Z", "SenderSoftware": {"SenderSoftwareDeveloper": "Envoy Labs", "SenderSoftwareProduct": "ClaraRX", "SenderSoftwareVersionRelease": "0.0.1"}}, "Body": {"AddOrganization": {"DirectoryInformation": {"AccountID": 1100785, "PortalID": 1315533, "ActiveStartTime": "2024-05-08T09:55:55.948Z", "ActiveEndTime": "2035-01-01T00:00:00.89Z", "DirectorySpecialties": {"DirectorySpecialty": [{"DirectorySpecialtyName": "Retail"}, {"DirectorySpecialtyName": "Specialty"}, {"DirectorySpecialtyName": "Compounding"}]}, "ServiceLevels": {"ServiceLevel": [{"ServiceLevelName": "ControlledSubstance"}, {"ServiceLevelName": "New"}, {"ServiceLevelName": "Change"}, {"ServiceLevelName": "Refill"}, {"ServiceLevelName": "Cancel"}]}}, "Organization": {"Identification": {"NCPDPID": 1122987, "NPI": **********}, "OrganizationName": "Rempel, Berge and DuBuque", "OrganizationType": "Pharmacy", "Address": {"AddressLine1": "255 NW Victoria Dr, Suite B", "City": "Lee's Summit", "StateProvince": "MO", "PostalCode": 64086, "CountryCode": "US"}, "CommunicationNumbers": {"PrimaryTelephone": {"Number": **********}, "Fax": {"Number": **********}}, "PharmacyHoursOfOperation": "Monday 09:00 am-05:00 pm Tuesday 09:00 am-05:00 pm Wednesday 09:00 am-05:00 pm Thursday 09:00 am-05:00 pm Friday 09:00 am-05:00 pm", "SupportsNonVeterinarian": "Y", "SupportsVeterinarian": "N"}}}}}, "request_message_id": "1e01bcdfa50a48d3be89892a454ae7db", "request_xml_data": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<DirectoryMessage DatatypesVersion=\"20170715\" TransportVersion=\"20170715\" TransactionDomain=\"DIRECTORY\" TransactionVersion=\"20170715\" StructuresVersion=\"20170715\" ECLVersion=\"20170715\" Version=\"006\" Release=\"001\">\n  <Header>\n    <To Qualifier=\"ZZZ\">SSDR61</To>\n    <From Qualifier=\"ZZZ\">ENVD61</From>\n    <MessageID>1e01bcdfa50a48d3be89892a454ae7db</MessageID>\n    <SentTime>2024-05-08T09:55:55.948Z</SentTime>\n    <SenderSoftware>\n      <SenderSoftwareDeveloper>Envoy Labs</SenderSoftwareDeveloper>\n      <SenderSoftwareProduct>ClaraRX</SenderSoftwareProduct>\n      <SenderSoftwareVersionRelease>0.0.1</SenderSoftwareVersionRelease>\n    </SenderSoftware>\n  </Header>\n  <Body>\n\t<AddOrganization>\r\n\t\t<DirectoryInformation>\r\n\t\t\t<AccountID>1100785</AccountID>\r\n\t\t\t<PortalID>1315533</PortalID>\r\n\t\t\t<ActiveStartTime>2024-05-08T09:55:55.948Z</ActiveStartTime>\r\n\t\t\t<ActiveEndTime>2035-01-01T00:00:00.89Z</ActiveEndTime>\r\n\t\t\t<DirectorySpecialties>\r\n\t\t\t\t\t\t<DirectorySpecialty>\r\n\t\t\t\t\t\t\t<DirectorySpecialtyName>Retail</DirectorySpecialtyName>\r\n\t\t\t\t\t\t</DirectorySpecialty>\r\n\t\t\t\t\t\t<DirectorySpecialty>\r\n\t\t\t\t\t\t\t<DirectorySpecialtyName>Specialty</DirectorySpecialtyName>\r\n\t\t\t\t\t\t</DirectorySpecialty>\r\n\t\t\t\t\t\t<DirectorySpecialty>\r\n\t\t\t\t\t\t\t<DirectorySpecialtyName>Compounding</DirectorySpecialtyName>\r\n\t\t\t\t\t\t</DirectorySpecialty>\r\n\t\t\t</DirectorySpecialties>\r\n\t\t\t<ServiceLevels>\r\n\t\t\t\t\t\t<ServiceLevel>\r\n\t\t\t\t\t\t\t<ServiceLevelName>ControlledSubstance</ServiceLevelName>\r\n\t\t\t\t\t\t</ServiceLevel>\r\n\t\t\t\t\t\t<ServiceLevel>\r\n\t\t\t\t\t\t\t<ServiceLevelName>New</ServiceLevelName>\r\n\t\t\t\t\t\t</ServiceLevel>\r\n\t\t\t\t\t\t<ServiceLevel>\r\n\t\t\t\t\t\t\t<ServiceLevelName>Change</ServiceLevelName>\r\n\t\t\t\t\t\t</ServiceLevel>\r\n\t\t\t\t\t\t<ServiceLevel>\r\n\t\t\t\t\t\t\t<ServiceLevelName>Refill</ServiceLevelName>\r\n\t\t\t\t\t\t</ServiceLevel>\r\n\t\t\t\t\t\t<ServiceLevel>\r\n\t\t\t\t\t\t\t<ServiceLevelName>Cancel</ServiceLevelName>\r\n\t\t\t\t\t\t</ServiceLevel>\r\n\t\t\t</ServiceLevels>\r\n\t\t</DirectoryInformation>\r\n\t\t<Organization>\r\n\t\t\t<Identification>\r\n\t\t\t\t\t<NCPDPID>1122987</NCPDPID>\r\n\t\t\t\t\t<NPI>**********</NPI>\r\n\t\t\t</Identification>\r\n\t\t\t<OrganizationName>Rempel, Berge and DuBuque</OrganizationName>\r\n\t\t\t<OrganizationType>Pharmacy</OrganizationType>\r\n\t\t\t<Address>\r\n\t\t\t\t<AddressLine1>255 NW Victoria Dr, Suite B</AddressLine1>\r\n\t\t\t\t<City>Lee&apos;s Summit</City>\r\n\t\t\t\t<StateProvince>MO</StateProvince>\r\n\t\t\t\t<PostalCode>64086</PostalCode>\r\n\t\t\t\t<CountryCode>US</CountryCode>\r\n\t\t\t</Address>\r\n\t\t\t<CommunicationNumbers>\r\n\t\t\t\t<PrimaryTelephone>\r\n\t\t\t\t\t<Number>**********</Number>\r\n\t\t\t\t</PrimaryTelephone>\r\n\t\t\t\t<Fax>\r\n\t\t\t\t\t<Number>**********</Number>\r\n\t\t\t\t</Fax>\r\n\t\t\t</CommunicationNumbers>\r\n\t\t\t\t<PharmacyHoursOfOperation>Monday 09:00 am-05:00 pm Tuesday 09:00 am-05:00 pm Wednesday 09:00 am-05:00 pm Thursday 09:00 am-05:00 pm Friday 09:00 am-05:00 pm</PharmacyHoursOfOperation>\r\n\t\t\t<SupportsNonVeterinarian>Y</SupportsNonVeterinarian>\r\n\t\t\t<SupportsVeterinarian>N</SupportsVeterinarian>\r\n\t\t</Organization>\r\n\t</AddOrganization>\r\n  </Body>\n</DirectoryMessage>", "response_hash": {"key": "mS+CmyFKE8QHPDMdeFjpQSj7eCvSf1rQU3Sw7Ol4zQF+OVVS/7GThIdLWSP3nxiZn8Wz3vtfSLvfLwj4kIRuP2eo8KEEqvPrVyh1XlUmk3tm5vmw6FrUODHgm1My7Na9lFHkSpaZG4xgBBnusPaPadO1QTJRJHaZGiDT0npYb4KaNpIaIyEogMkznLtpbPUbDvdiYoJ4a0g25Zfzb9Yb8VXAQZvXRop8Wz+hScvXfFHFrK0pxje0hQDaz7dPUHXtTVosCcGCc9Jq6nsejR0Z69v5qf3ZwIRrNx91S9dqPoDN0zKGSpUmCeo7eLxBzIGpwkB26EBXg+DXQLEKwlqjwV6prGxkULpHxvqOOHYWpCv5UZcpzHZD2N7dUEPW92UQ3SqfVpRgfYZyBKmbeMBVmG7UCHSANWTWv6FPKSE6F/a3QlPiMqd+skNbzc3mHh9PeFLOOBXGLBCsAJo6IqnZEWaZtGWGEy3Rg9v+tT0UByWsVDzq/PPHTmz9EO7Ux16tP7VNcCBbmfk74kzdtXNKqYRUN4flYzPhHaL8RsEWdPO9SATwaN35JBXHQbsBeTLBLIK/gLELPE3Pqvbd9K6/mqBuHZFT4gvhEjge7hD466QfuuO1m5DlJ03+8Paqp1xOIeEU2ZASCUT9cb2VbvtijfFUWi9fy0ja0dWPnttUrAA=", "data": "60bd0d36d2a5f64ac5cae570c41a63ccd929dbe05cef45cfa8924eaee331e71e4e4f656e6cb92bd2e52f0290c9704d92a749e3b9043e840f87e8c77b3fbda0a4192786f20b251411c0d4266c75bd40c34ff19d2db712890082f18037660a7c26a811c1336fa1fcb5e98a1f97021c032ba8b0099b229d557c00236a92473ee6aa654f4895c5bcb3f8ce898b94c7035410b8a456c951c4cb22716ad638c0be2936df4ddb3726536998ed729ebab5dfa20d79f5da64a925eadb2ead9ee45e174919b212a31dd071b0981fd1b2d7b3b3743f91ad8aaaa2eb0d03b3cdee10e143b69e86196012474b73c6642ddfb3130fd2b58433748db746e4adc50402663b663c9c1f23dec3706bab80eab4946cf0e942786c0cacf1becef5394f7c3babf4dda1d1e4d1934a94cda57d59f9e54ae9c728c542669859323fd40409cd3dcfd4f4d87821df4ef76842d244377a90b1d08b9c1bb46d8a807abc1df0f18b88486aaceeccc76b1d5ab4c2e1b53394e4f2a84a95d94a7883a09298423d8126fa585659ef4748756f01be7156aad49f5b9f6e56c7858179ba9a3679c3bc085ece14f6264b6bf396bf43e896489299ed3a70b827d213e9ab79a45d4c7d5b808b853f10a4c4c871f2fec534075dca1cad09c9d03c9261a39cf649fd7f49a2c7770424db41c09cb101b28bdc83bc6f3f5ed536b15f12bdd34703eb5bbf490d0a78557960ec21f8f6cafbf8b1bf23aa99c0d4d6625a8ea4e2dac58918053c4fc3943f88a4c2eb37454ebb1eaf6b55b8b013ed85ab71e1f97f3d540b3404da67367973938fdc7d617d8fbf25df9391ed061775c07c41222c74c06fedfc995ea8004238c4b5fd8b0148364f4d494275d96a1074f02be35c799c40a10404a6176a5db1fc499ce8b00d14fe51a8472d94e8d23d30771797cf631941e13a9ded0fa30a6a0995ea2ca2ead712b3af21dba844251e307c73fd18c7a297ad101b0daebcee1803318745a283ad7de81ddcc79b56ef945af203ab63bba4c9d3cc84478bd8d956859fa802a2461b4fed4ef16ac2a84fdcb31ffd67779d739b582fd88df8cac5b5b6b09542a6de1217f35aaf8840b57f20d6d4570a2e729dc2edffd41d7cca86953f79c19d02d1803de8b1f27e2d352ac7812497ea25b86d4ba333fa753e399f870d1aa8e40e69e719aed39d9e289fb06de0468806a36b44d21b81474477fef2c1aefc531b7373d458a402df5b7691ef0061cefa40d958d1e7259999334e9e11ea4873e80d76580d0573fe574aa8a624e090dcd538ff74b0b4fb32d88cd11934eb5a8d89a36dac185ddd46a36a7a0f7f62bed70e89f014fe6378bc251a475468f3bf30413abdd7d01f2dd5a2698ec567f09e1b085d80b3ef72b330a77747a32d720347ad949fb8860349560a7df02e4c5ea2951d7b259460fe4e6e8102bd164800684b77e15b000cbcaf3b7b896ab15ba9822a98edcb99331712711f3f24d487e4db41259cdb4cef3e755c3bc2db3d85d8a95da32a85b14fa55f7a27a716af0d1f5b8214ef9809570d888d77ba24cad14cf685b1ee47c7365f700595fdcfb14450ebfc44b37ddb3ba2ea1d438c79b581369922cde19d7081778e30c9f9b7d14c902f2ae68238c4e28bef8474152796ce18b70d6df25266f808aba544d7c6ccd334a5a100a491871ef09629199e9e9b2ae6c5258590353df4ac65dc81d5ceeb683e3c13fee81f2335597dc0a8a6bf75c5e286b5a400b443c1bd3b694f72df683b5e08cd2869f65771d7c53decce39a19bf3f8676a0e28221a0d244c6e205d569308821ed7da39cacd1011cc550bca08973fbccecfbaa832b76aba496a575f1743ccd90618a90422d0a907ae1a562240ff4fb3e1250cd39a58c617be3cea7de6b80b71e021bda73dec6a48306188627a5f48910ce79e80c6dec8fe6c516623cfbfd37f6c8dc8f53c020949efe98e93a132d58e64878b23faa36d33052952d25d07365d6b481a9ad30d524b7f1dd5006ebdf0e3fd1b144bd6f9d4e3a86bb32a2f8ffeacb40fd28f8745f09d09d2453d2ac6a956433452dfde8b091ea66c47b57ce409a0282582a232c9ed6fd029ff6e5c64d63245d2738b735c0f3fa13e1a9d0d516c1eb08052c11935f06de3566a414d404f4e7b533872b645aaafb4ad97015d8ffe1feb4646ba61ac01ae708e5aa5e85f7df359c7479f91cefc17cb1cbee52269f489f55020a6b78cb13a230296bdbab08715e901dfc6f420eed502c05a6b7a0cb24823354e3e382360b840fa6ad013aa32118183505143eaa9f49c73f0dc92bdac9c61dd50f82354793cad74ce63e7822b10f618ae58ff9516889e62a3184955e1691715c2e6a24e393a208be6653f02ba24ea587507062cac497b8cf18fad2a0f1a7a0a9e64ca2f3379d96256917d0ed18914fe748da6cd59b23a7af310335861a8620d69ec0e8a3386379a558c39e8e31a590a52fa36d6b85242d6a4c9ade24aac1dbac5d1a513e0e0bef1cb89c6791deec654ded80170ffb437a391017234dcd2ec0092873c7fd8f6884f6a3ad04336208d76b9774b6c074af9c5f8fc45b54a25954b4261ffdca87064c497886311c20edc8d4a90cf22326e5506618038878115a7cc646b33bd95725013bfd2c8e7b868e0fd8c6aebca8e8ae2dbcf4e7257e641b104c7bf02f78c3f9de725eb2496109ef00b962cd764d41142c86a6bc7f0a7025f069adfae01308467ea8b0da625340eecc95e8361034eb695d33ca7634b02124f395f896b8550feb6bf15f3cebda89651e5aa7db1b19e2bcd37850b0c343fba47cd553f26f44cbaf5e68499280ed662b6ad88354029ff9521c40ae96168e34d0ed20b2436175dd5043765a1b6d6c20f9619ecb1cce83c53fa39ab11ca63cf7c6b6fb56048a536ecb6a7fa4c8c45e4324ccce6c5a5b9e538862a9ad8cc71ec90202db51b08996035602206455aea2953d0236272c53d4234ba4c6d32cf58f841614f744ce066e3118fcd89f41b522edf4d34d06b2542a2a1963bbba43791649c900d5fe0d8cc1de709cbf5e0d967ca4e1527ffe69b3258eab136bcb567dc1f1df9b33b0d877a08b76a371c62a6bbdd3881d245f5d38c960234c171b7a966a60c141a1f7118b447d1e1371cfe1b5e17668d9620a3eb693c2eafc9f961a07aba329cbd3ab1a97b57f92849cb02e90203616638538024877fe85d5a6889c20b2f636696b46fcf4712ef2b8b44ed607d5eb1b6a6fc19feaba321dcb96c5e12f319de7eedd3b4abbc9bb25f0e948f04784e202b89dc11849e9b132cd5c94e0afa95e24ad8d9f12bdfee295c4ffc15cd6442d0307b3d3525fbf82a30a20dd74aa0022bb827912f4f8d5b095944591c884fde2b012eb231cb08dae1e10915ee4418291abe2403cd236c09f2d745800744471450ddd335f5063db9da8013aba515491cc05c9c90323441500ff169fefd9a2f41d1cd27d1bd8ccc45bb514de541c6faf84677d9574b888c251c32ec7cb27dd80dedd97ffe4515dd502fdaadf55313dcb2697992ce0d00adc739d3af0a21ff7186e849b7e7c82f34c807e22d4aaa0986a1df7ee9597a97196b26e5a882d088952b8134f849bdcb7518d15db87adbc1c3bc52a2952f47ef439a9694c3b18d7d304ff140c7a18b131580bf995b250e0187ccaaf585a5c74f56952b410990b2411103fb433b346232f77f24e196a4f703aa7a3bd63b94b689fcb69362ea5bc316df724457f85c0960e5ea03cdee1460e10b21f6608b2c4893b53cba24c0629afac09cb41fa50b0aef28cea14a61a7865958d8fd71ec80dedad69afeca2de215967c6a256546514a343ec65cc30e3417836c4411b5ebb5dc4c5e3b738ba05e55afa02dfd810755cfa907606a142e78b2f0a4eaf2302ceb1dde0f270aa3304ec037f8cad1ff00e4eae24037f589ff9b82ae3c9e276a817e5fc8995d8f5d02165912efb28816b313d2bfcb51a8bb4ee5110559f855c4227b7e88b28be91713b109669b78632a98b421c1664b9be50f7645910950f4d6ac25291d5c75b879419b0d05ac0e1b918f72c64a"}, "response_json_data": {"DirectoryMessage": {"Header": {"To": "ENVD61", "From": "SSDR61", "MessageID": "1a4322e9dad241afb53541bfd4800702", "RelatesToMessageID": "1e01bcdfa50a48d3be89892a454ae7db", "SentTime": "2024-05-08T09:55:56.2717443Z", "SenderSoftware": {"SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Directory Messaging", "SenderSoftwareVersionRelease": "6.2, *******"}}, "Body": {"AddOrganizationResponse": {"DirectoryInformation": {"AccountID": 1100785, "PortalID": 1315533, "ActiveStartTime": "2024-05-08T09:55:56.214Z", "ActiveEndTime": "2035-01-01T00:00:00.89Z", "DirectorySpecialties": {"DirectorySpecialty": [{"DirectorySpecialtyName": "Retail"}, {"DirectorySpecialtyName": "Specialty"}, {"DirectorySpecialtyName": "Compounding"}]}, "FaxBackup": "Y", "ServiceLevels": {"ServiceLevel": [{"ServiceLevelName": "Cancel"}, {"ServiceLevelName": "Change"}, {"ServiceLevelName": "ControlledSubstance"}, {"ServiceLevelName": "New"}, {"ServiceLevelName": "Refill"}]}, "Test": 0}, "Organization": {"Identification": {"NCPDPID": 1122987, "NPI": **********, "OrganizationID": 3330553}, "OrganizationName": "Rempel, Berge and DuBuque", "OrganizationType": "Pharmacy", "Address": {"AddressLine1": "255 NW Victoria Dr, Suite B", "City": "Lee's Summit", "StateProvince": "MO", "PostalCode": 64086, "CountryCode": "US"}, "CommunicationNumbers": {"PrimaryTelephone": {"Number": **********}, "Fax": {"Number": **********}}, "OperatingStatus": {"Status": "Normal"}, "PharmacyHoursOfOperation": "Monday 09:00 am-05:00 pm Tuesday 09:00 am-05:00 pm Wednesday 09:00 am-05:00 pm Thursday 09:00 am-05:00 pm Friday 09:00 am-05:00 pm", "SupportsNonVeterinarian": "Y", "SupportsVeterinarian": "N"}}}}}, "response_message_id": "1a4322e9dad241afb53541bfd4800702", "response_xml_data": "<?xml version=\"1.0\" encoding=\"utf-8\"?><DirectoryMessage DatatypesVersion=\"20170715\" TransportVersion=\"20170715\" TransactionDomain=\"DIRECTORY\" TransactionVersion=\"20170715\" StructuresVersion=\"20170715\" ECLVersion=\"20170715\" Version=\"006\" Release=\"001\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"><Header><To Qualifier=\"ZZZ\">ENVD61</To><From Qualifier=\"ZZZ\">SSDR61</From><MessageID>1a4322e9dad241afb53541bfd4800702</MessageID><RelatesToMessageID>1e01bcdfa50a48d3be89892a454ae7db</RelatesToMessageID><SentTime>2024-05-08T09:55:56.2717443Z</SentTime><SenderSoftware><SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper><SenderSoftwareProduct>Directory Messaging</SenderSoftwareProduct><SenderSoftwareVersionRelease>6.2, *******</SenderSoftwareVersionRelease></SenderSoftware></Header><Body><AddOrganizationResponse><DirectoryInformation><AccountID>1100785</AccountID><PortalID>1315533</PortalID><ActiveStartTime>2024-05-08T09:55:56.214Z</ActiveStartTime><ActiveEndTime>2035-01-01T00:00:00.89Z</ActiveEndTime><DirectorySpecialties><DirectorySpecialty><DirectorySpecialtyName>Retail</DirectorySpecialtyName></DirectorySpecialty><DirectorySpecialty><DirectorySpecialtyName>Specialty</DirectorySpecialtyName></DirectorySpecialty><DirectorySpecialty><DirectorySpecialtyName>Compounding</DirectorySpecialtyName></DirectorySpecialty></DirectorySpecialties><FaxBackup>Y</FaxBackup><ServiceLevels><ServiceLevel><ServiceLevelName>Cancel</ServiceLevelName></ServiceLevel><ServiceLevel><ServiceLevelName>Change</ServiceLevelName></ServiceLevel><ServiceLevel><ServiceLevelName>ControlledSubstance</ServiceLevelName></ServiceLevel><ServiceLevel><ServiceLevelName>New</ServiceLevelName></ServiceLevel><ServiceLevel><ServiceLevelName>Refill</ServiceLevelName></ServiceLevel></ServiceLevels><Test>0</Test></DirectoryInformation><Organization><Identification><NCPDPID>1122987</NCPDPID><NPI>**********</NPI><OrganizationID>3330553</OrganizationID></Identification><OrganizationName>Rempel, Berge and DuBuque</OrganizationName><OrganizationType>Pharmacy</OrganizationType><Address><AddressLine1>255 NW Victoria Dr, Suite B</AddressLine1><City>Lee's Summit</City><StateProvince>MO</StateProvince><PostalCode>64086</PostalCode><CountryCode>US</CountryCode></Address><CommunicationNumbers><PrimaryTelephone><Number>**********</Number></PrimaryTelephone><Fax><Number>**********</Number></Fax></CommunicationNumbers><OperatingStatus><Status>Normal</Status></OperatingStatus><PharmacyHoursOfOperation>Monday 09:00 am-05:00 pm Tuesday 09:00 am-05:00 pm Wednesday 09:00 am-05:00 pm Thursday 09:00 am-05:00 pm Friday 09:00 am-05:00 pm</PharmacyHoursOfOperation><SupportsNonVeterinarian>Y</SupportsNonVeterinarian><SupportsVeterinarian>N</SupportsVeterinarian></Organization></AddOrganizationResponse></Body></DirectoryMessage>"}