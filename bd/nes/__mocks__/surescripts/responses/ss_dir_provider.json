{"response_json_data": {"DirectoryMessage": {"Header": {"To": {"_Qualifier": "ZZZ", "__text": "EHR123"}, "From": {"_Qualifier": "ZZZ", "__text": "SSDR61"}, "MessageID": "3f0e74da758444d29b48c4b4ef2854a8", "RelatesToMessageID": "c34b4ee123f741368c6b8ad673b5b49b", "SentTime": "2018-02-22T21:18:05.4538184Z", "SenderSoftware": {"SenderSoftwareDeveloper": "Surescripts", "SenderSoftwareProduct": "Directory Messaging", "SenderSoftwareVersionRelease": "6.1"}}, "Body": {"GetProviderLocationResponse": {"GetProviderLocationResponseItem": {"DirectoryInformation": {"AccountID": "1930438", "PortalID": "1871990", "ActiveStartTime": "2018-02-22T21:18:04.194Z", "ActiveEndTime": "2018-03-24T21:18:03.456Z", "ServiceLevels": {"ServiceLevel": [{"ServiceLevelName": "New"}, {"ServiceLevelName": "Refill"}, {"ServiceLevelName": "ControlledSubstance"}, {"ServiceLevelName": "Change"}]}, "Test": "0"}, "ProviderLocation": {"Identification": {"NPI": "**********", "SPI": "*************"}, "Name": {"LastName": "<PERSON>", "FirstName": "Nicola"}, "Address": {"AddressLine1": "3527 Powlowski Plains", "City": "West Albashire", "StateProvince": "MI", "PostalCode": "*********", "CountryCode": "US"}, "CommunicationNumbers": {"PrimaryTelephone": {"Number": "**********"}, "Fax": {"Number": "**********"}}}}}}}}}