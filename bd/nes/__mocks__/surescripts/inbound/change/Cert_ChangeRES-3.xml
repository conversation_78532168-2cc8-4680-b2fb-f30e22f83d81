<?xml version="1.0" encoding="utf-8"?>
<Message DatatypesVersion="20170715" TransportVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715">
  <Header>
    <To Qualifier="P">1122999</To>
    <From Qualifier="D">6301875277001</From>
    <MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
    <RelatesToMessageID>COPY FROM RXCHG</RelatesToMessageID>
    <SentTime>2019-01-01T13:42:39.7Z</SentTime>
    <SenderSoftware>
      <SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
      <SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
      <SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
    </SenderSoftware>
    <RxReferenceNumber>!</RxReferenceNumber>
    <PrescriberOrderNumber>ECHO FROM NEWRX</PrescriberOrderNumber>
  </Header>
  <Body>
    <RxChangeResponse>
      <MessageRequestCode>T</MessageRequestCode>
      <Response>
        <Approved />
      </Response>
      <Patient>
        <HumanPatient>
          <Name>
            <LastName>Acacianna</LastName>
            <FirstName>Rowena</FirstName>
            <MiddleName>Baylie</MiddleName>
          </Name>
          <Gender>F</Gender>
          <DateOfBirth>
            <Date>1968-03-29</Date>
          </DateOfBirth>
          <Address>
            <AddressLine1>2798 Parsifal St NE</AddressLine1>
            <City>Albuquerque</City>
            <StateProvince>NM</StateProvince>
            <PostalCode>87112</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
            </PrimaryTelephone>
          </CommunicationNumbers>
        </HumanPatient>
      </Patient>
      <Pharmacy>
        <Identification>
          <NCPDPID>1122999</NCPDPID>
          <StateLicenseNumber>MD-6218-96551</StateLicenseNumber>
          <MedicareNumber>365476</MedicareNumber>
          <MedicaidNumber>365476</MedicaidNumber>
          <DEANumber>*********</DEANumber>
          <NPI>**********</NPI>
        </Identification>
        <BusinessName>Bannockburn Pharmacy</BusinessName>
        <Address>
          <AddressLine1>6798 Pyle Rd</AddressLine1>
          <City>Bethesda</City>
          <StateProvince>MD</StateProvince>
          <PostalCode>20817</PostalCode>
          <CountryCode>US</CountryCode>
        </Address>
        <CommunicationNumbers>
          <PrimaryTelephone>
            <Number>**********</Number>
          </PrimaryTelephone>
          <Fax>
            <Number>**********</Number>
          </Fax>
        </CommunicationNumbers>
      </Pharmacy>
      <Prescriber>
        <NonVeterinarian>
          <Identification>
            <StateLicenseNumber>DC121_123_933</StateLicenseNumber>
            <MedicareNumber>154745</MedicareNumber>
            <DEANumber>*********</DEANumber>
            <NPI>**********</NPI>
            <CertificateToPrescribe>CTP-DC-123.933</CertificateToPrescribe>
            <Data2000WaiverID>*********</Data2000WaiverID>
            <REMSHealthcareProviderEnrollmentID>4521445-TM-457</REMSHealthcareProviderEnrollmentID>
          </Identification>
          <Name>
            <LastName>McTavish</LastName>
            <FirstName>Tarquin</FirstName>
            <Suffix>MD</Suffix>
            <Prefix>MR</Prefix>
          </Name>
          <Address>
            <AddressLine1>1818 Clydesdale Place NW</AddressLine1>
            <AddressLine2># 2210</AddressLine2>
            <City>Washington</City>
            <StateProvince>DC</StateProvince>
            <PostalCode>20009</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
            </PrimaryTelephone>
            <Fax>
              <Number>**********</Number>
            </Fax>
          </CommunicationNumbers>
        </NonVeterinarian>
      </Prescriber>
      <MedicationPrescribed>
        <DrugDescription>lisinopril 10 mg tablet</DrugDescription>
        <DrugCoded>
          <ProductCode>
            <Code>00591040710</Code>
            <Qualifier>ND</Qualifier>
          </ProductCode>
          <DrugDBCode>
            <Code>314076</Code>
            <Qualifier>SCD</Qualifier>
          </DrugDBCode>
        </DrugCoded>
        <Quantity>
          <Value>60</Value>
          <CodeListQualifier>38</CodeListQualifier>
          <QuantityUnitOfMeasure>
            <Code>C48542</Code>
          </QuantityUnitOfMeasure>
        </Quantity>
        <DaysSupply>30</DaysSupply>
        <WrittenDate>
          <Date>2019-01-01</Date>
        </WrittenDate>
        <Substitutions>0</Substitutions>
        <NumberOfRefills>1</NumberOfRefills>
        <Diagnosis>
          <ClinicalInformationQualifier>1</ClinicalInformationQualifier>
          <Primary>
            <Code>I10</Code>
            <Qualifier>ABF</Qualifier>
            <Description>Essential (primary) hypertension</Description>
          </Primary>
        </Diagnosis>
        <Note>Approved substitution request</Note>
        <Sig>
          <SigText>Take 2 tablets daily for 30 days.</SigText>
          <CodeSystem>
            <SNOMEDVersion>20170131</SNOMEDVersion>
            <FMTVersion>16.03d</FMTVersion>
          </CodeSystem>
          <Instruction>
            <DoseAdministration>
              <DoseDeliveryMethod>
                <Text>Take</Text>
                <Qualifier>SNOMED</Qualifier>
                <Code>419652001</Code>
              </DoseDeliveryMethod>
              <Dosage>
                <DoseQuantity>2</DoseQuantity>
                <DoseUnitOfMeasure>
                  <Text>tablet</Text>
                  <Qualifier>DoseUnitOfMeasure</Qualifier>
                  <Code>C48542</Code>
                </DoseUnitOfMeasure>
              </Dosage>
              <RouteOfAdministration>
                <Text>oral route</Text>
                <Qualifier>SNOMED</Qualifier>
                <Code>26643006</Code>
              </RouteOfAdministration>
            </DoseAdministration>
            <TimingAndDuration>
              <Frequency>
                <FrequencyNumericValue>1</FrequencyNumericValue>
                <FrequencyUnits>
                  <Text>day</Text>
                  <Qualifier>SNOMED</Qualifier>
                  <Code>258703001</Code>
                </FrequencyUnits>
              </Frequency>
            </TimingAndDuration>
            <TimingAndDuration>
              <Duration>
                <DurationNumericValue>30</DurationNumericValue>
                <DurationText>
                  <Text>day</Text>
                  <Qualifier>SNOMED</Qualifier>
                  <Code>258703001</Code>
                </DurationText>
              </Duration>
            </TimingAndDuration>
          </Instruction>
        </Sig>
        <RxFillIndicator>All Fill Statuses</RxFillIndicator>
      </MedicationPrescribed>
    </RxChangeResponse>
  </Body>
</Message>