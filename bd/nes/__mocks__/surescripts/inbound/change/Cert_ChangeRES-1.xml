<?xml version="1.0" encoding="utf-8"?>
<Message DatatypesVersion="20170715" TransportVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715">
  <Header>
    <To Qualifier="P">1122999</To>
    <From Qualifier="D">6301875277001</From>
    <MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
    <RelatesToMessageID>COPY FROM RXCHG</RelatesToMessageID>
    <SentTime>2019-01-01T13:42:39.7Z</SentTime>
    <SenderSoftware>
      <SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
      <SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
      <SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
    </SenderSoftware>
    <RxReferenceNumber>!</RxReferenceNumber>
    <PrescriberOrderNumber>ECHO FROM NEWRX</PrescriberOrderNumber>
  </Header>
  <Body>
    <RxChangeResponse>
      <MessageRequestCode>G</MessageRequestCode>
      <Response>
        <ApprovedWithChanges>
          <Note>Generic substitution approved</Note>
        </ApprovedWithChanges>
      </Response>
      <Patient>
        <HumanPatient>
          <Name>
            <LastName>Montevideo</LastName>
            <FirstName>Ricardo</FirstName>
            <MiddleName>P.</MiddleName>
          </Name>
          <Gender>M</Gender>
          <DateOfBirth>
            <Date>1998-01-29</Date>
          </DateOfBirth>
          <Address>
            <AddressLine1>125 Blue Jay Ln</AddressLine1>
            <City>Pittsville</City>
            <StateProvince>VA</StateProvince>
            <PostalCode>241390126</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
        </HumanPatient>
      </Patient>
      <Pharmacy>
        <Identification>
          <NCPDPID>1122999</NCPDPID>
          <StateLicenseNumber>MASS PH897 9211G</StateLicenseNumber>
          <MedicareNumber>566977</MedicareNumber>
          <MedicaidNumber>566977</MedicaidNumber>
          <DEANumber>*********</DEANumber>
          <NPI>**********</NPI>
        </Identification>
        <BusinessName>Lawrence Academy Rx 10.6</BusinessName>
        <Address>
          <AddressLine1>235 Main St</AddressLine1>
          <City>Groton</City>
          <StateProvince>MA</StateProvince>
          <PostalCode>01450</PostalCode>
          <CountryCode>US</CountryCode>
        </Address>
        <CommunicationNumbers>
          <PrimaryTelephone>
            <Number>**********</Number>
          </PrimaryTelephone>
          <Fax>
            <Number>**********</Number>
          </Fax>
        </CommunicationNumbers>
      </Pharmacy>
      <Prescriber>
        <NonVeterinarian>
          <Identification>
            <StateLicenseNumber>DC121_123_933</StateLicenseNumber>
            <MedicareNumber>154745</MedicareNumber>
            <DEANumber>*********</DEANumber>
            <NPI>**********</NPI>
            <CertificateToPrescribe>CTP-DC-123.933</CertificateToPrescribe>
            <Data2000WaiverID>*********</Data2000WaiverID>
            <REMSHealthcareProviderEnrollmentID>4521445-TM-457</REMSHealthcareProviderEnrollmentID>
          </Identification>
          <Name>
            <LastName>McTavish</LastName>
            <FirstName>Tarquin</FirstName>
            <Suffix>MD</Suffix>
            <Prefix>MR</Prefix>
          </Name>
          <Address>
            <AddressLine1>1818 Clydesdale Place NW</AddressLine1>
            <AddressLine2># 2210</AddressLine2>
            <City>Washington</City>
            <StateProvince>DC</StateProvince>
            <PostalCode>20009</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
            </PrimaryTelephone>
            <Fax>
              <Number>**********</Number>
            </Fax>
          </CommunicationNumbers>
        </NonVeterinarian>
      </Prescriber>
      <MedicationPrescribed>
        <DrugDescription>doxycycline hyclate 100 mg capsule</DrugDescription>
        <DrugCoded>
          <ProductCode>
            <Code>00143314250</Code>
            <Qualifier>ND</Qualifier>
          </ProductCode>
          <DrugDBCode>
            <Code>1649988</Code>
            <Qualifier>SCD</Qualifier>
          </DrugDBCode>
        </DrugCoded>
        <Quantity>
          <Value>20</Value>
          <CodeListQualifier>38</CodeListQualifier>
          <QuantityUnitOfMeasure>
            <Code>C48480</Code>
          </QuantityUnitOfMeasure>
        </Quantity>
        <WrittenDate>
          <Date>2019-01-01</Date>
        </WrittenDate>
        <Substitutions>0</Substitutions>
        <NumberOfRefills>3</NumberOfRefills>
        <Diagnosis>
          <ClinicalInformationQualifier>1</ClinicalInformationQualifier>
          <Primary>
            <Code>N390</Code>
            <Qualifier>ABF</Qualifier>
            <Description>Chronic urinary tract infection</Description>
          </Primary>
        </Diagnosis>
        <Note>Please note the modified sig from the request</Note>
        <Sig>
          <SigText>Take 1 capsule orally every 12 hours for 2 weeks</SigText>
          <CodeSystem>
            <SNOMEDVersion>20170131</SNOMEDVersion>
            <FMTVersion>19.12e</FMTVersion>
          </CodeSystem>
          <Instruction>
            <DoseAdministration>
              <DoseDeliveryMethod>
                <Text>Take</Text>
                <Qualifier>SNOMED</Qualifier>
                <Code>419652001</Code>
              </DoseDeliveryMethod>
              <Dosage>
                <DoseQuantity>1</DoseQuantity>
                <DoseUnitOfMeasure>
                  <Text>capsule</Text>
                  <Qualifier>DoseUnitOfMeasure</Qualifier>
                  <Code>C48480</Code>
                </DoseUnitOfMeasure>
              </Dosage>
              <RouteOfAdministration>
                <Text>oral route</Text>
                <Qualifier>SNOMED</Qualifier>
                <Code>26643006</Code>
              </RouteOfAdministration>
            </DoseAdministration>
            <TimingAndDuration>
              <Frequency>
                <FrequencyNumericValue>12</FrequencyNumericValue>
                <FrequencyUnits>
                  <Text>hours</Text>
                  <Qualifier>SNOMED</Qualifier>
                  <Code>258702006</Code>
                </FrequencyUnits>
              </Frequency>
            </TimingAndDuration>
            <TimingAndDuration>
              <Duration>
                <DurationNumericValue>2</DurationNumericValue>
                <DurationText>
                  <Text>week</Text>
                  <Qualifier>SNOMED</Qualifier>
                  <Code>258705008</Code>
                </DurationText>
              </Duration>
            </TimingAndDuration>
          </Instruction>
        </Sig>
      </MedicationPrescribed>
    </RxChangeResponse>
  </Body>
</Message>