<?xml version="1.0" encoding="utf-8"?>
<Message DatatypesVersion="20170715" TransportVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715">
  <Header>
    <To Qualifier="P">1122999</To>
    <From Qualifier="D">6301875277001</From>
    <MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
    <RelatesToMessageID>echo from ChgReq</RelatesToMessageID>
    <SentTime>2019-01-01T13:42:39.7Z</SentTime>
    <SenderSoftware>
      <SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
      <SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
      <SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
    </SenderSoftware>
    <PrescriberOrderNumber>CORE NEWRX 5</PrescriberOrderNumber>
    <DigitalSignature Version="1.1">
      <DigitalSignatureIndicator>true</DigitalSignatureIndicator>
    </DigitalSignature>
  </Header>
  <Body>
    <RxChangeResponse>
      <MessageRequestCode>U</MessageRequestCode>
      <MessageRequestSubCode>B</MessageRequestSubCode>
      <Response>
        <Validated>
          <ReasonCode>GM</ReasonCode>
          <Identification>
            <DEANumber>*********</DEANumber>
            <NPI>**********</NPI>
          </Identification>
        </Validated>
      </Response>
      <Patient>
        <HumanPatient>
          <Name>
            <LastName>Delaplaine</LastName>
            <FirstName>Angelyne</FirstName>
          </Name>
          <Gender>F</Gender>
          <DateOfBirth>
            <Date>2012-09-01</Date>
          </DateOfBirth>
          <Address>
            <AddressLine1>901 Sauvblanc Blvd</AddressLine1>
            <City>Petaluma</City>
            <StateProvince>VA</StateProvince>
            <PostalCode>94952</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
            </PrimaryTelephone>
            <Beeper>
              <Number>**********</Number>
            </Beeper>
          </CommunicationNumbers>
        </HumanPatient>
      </Patient>
      <Pharmacy>
        <Identification>
          <NCPDPID>1655458</NCPDPID>
          <StateLicenseNumber>784577%PH47%24R</StateLicenseNumber>
          <MedicareNumber>755449</MedicareNumber>
          <MedicaidNumber>755449</MedicaidNumber>
          <DEANumber>*********</DEANumber>
          <NPI>**********</NPI>
        </Identification>
        <BusinessName>Shollenberger Pharmacy</BusinessName>
        <Address>
          <AddressLine1>2002 S. McDowell Blvd Ext</AddressLine1>
          <City>Petaluma</City>
          <StateProvince>CA</StateProvince>
          <PostalCode>94954</PostalCode>
          <CountryCode>US</CountryCode>
        </Address>
        <CommunicationNumbers>
          <PrimaryTelephone>
            <Number>**********</Number>
          </PrimaryTelephone>
          <Fax>
            <Number>**********</Number>
          </Fax>
        </CommunicationNumbers>
      </Pharmacy>
      <Prescriber>
        <NonVeterinarian>
          <Identification>
            <StateLicenseNumber>784577%1147%24</StateLicenseNumber>
            <MedicaidNumber>658345</MedicaidNumber>
            <DEANumber>*********</DEANumber>
            <NPI>**********</NPI>
            <CertificateToPrescribe>CTP.CA.1147%24</CertificateToPrescribe>
          </Identification>
          <Specialty>363L00000X</Specialty>
          <PracticeLocation>
            <BusinessName>MediStar of California</BusinessName>
          </PracticeLocation>
          <Name>
            <LastName>Pimpernel</LastName>
            <FirstName>Marguerite</FirstName>
            <MiddleName>Anne</MiddleName>
            <Suffix>MD</Suffix>
            <Prefix>DR</Prefix>
          </Name>
          <Address>
            <AddressLine1>1425 Mendocino Ave</AddressLine1>
            <AddressLine2>Suite 12-A</AddressLine2>
            <City>Santa Rosa</City>
            <StateProvince>CA</StateProvince>
            <PostalCode>95401</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
              <Extension>4221</Extension>
            </PrimaryTelephone>
            <ElectronicMail><EMAIL></ElectronicMail>
            <Fax>
              <Number>7079442121</Number>
            </Fax>
          </CommunicationNumbers>
        </NonVeterinarian>
      </Prescriber>
      <Observation>
        <Measurement>
          <VitalSign>8302-2</VitalSign>
          <LOINCVersion>2.42</LOINCVersion>
          <Value>46</Value>
          <UnitOfMeasure>[in_i]</UnitOfMeasure>
          <UCUMVersion>2.1</UCUMVersion>
          <ObservationDate>
            <Date>2019-01-01</Date>
          </ObservationDate>
        </Measurement>
        <Measurement>
          <VitalSign>29463-7</VitalSign>
          <LOINCVersion>2.42</LOINCVersion>
          <Value>48</Value>
          <UnitOfMeasure>[lb_av]</UnitOfMeasure>
          <UCUMVersion>2.1</UCUMVersion>
          <ObservationDate>
            <Date>2019-01-01</Date>
          </ObservationDate>
        </Measurement>
      </Observation>
      <MedicationPrescribed>
        <DrugDescription>LORazepam 2 mg tablet</DrugDescription>
        <DrugCoded>
          <ProductCode>
            <Code>00093482201</Code>
            <Qualifier>ND</Qualifier>
          </ProductCode>
          <Strength>
            <StrengthValue>2</StrengthValue>
            <StrengthUnitOfMeasure>
              <Code>C28253</Code>
            </StrengthUnitOfMeasure>
          </Strength>
          <DrugDBCode>
            <Code>197902</Code>
            <Qualifier>SCD</Qualifier>
          </DrugDBCode>
          <DEASchedule>
            <Code>C48677</Code>
          </DEASchedule>
        </DrugCoded>
        <Quantity>
          <Value>60</Value>
          <CodeListQualifier>38</CodeListQualifier>
          <QuantityUnitOfMeasure>
            <Code>C48542</Code>
          </QuantityUnitOfMeasure>
        </Quantity>
        <WrittenDate>
          <Date>2019-01-01</Date>
        </WrittenDate>
        <Substitutions>0</Substitutions>
        <NumberOfRefills>2</NumberOfRefills>
        <Diagnosis>
          <ClinicalInformationQualifier>1</ClinicalInformationQualifier>
          <Primary>
            <Code>F411</Code>
            <Qualifier>ABF</Qualifier>
            <Description>Generalized anxiety disorder</Description>
          </Primary>
        </Diagnosis>
        <Sig>
          <SigText>Take 1 tablet orally twice a day</SigText>
          <CodeSystem>
            <SNOMEDVersion>20170131</SNOMEDVersion>
            <FMTVersion>16.03d</FMTVersion>
          </CodeSystem>
          <Instruction>
            <DoseAdministration>
              <DoseDeliveryMethod>
                <Text>Take</Text>
                <Qualifier>SNOMED</Qualifier>
                <Code>419652001</Code>
              </DoseDeliveryMethod>
              <Dosage>
                <DoseQuantity>1</DoseQuantity>
                <DoseUnitOfMeasure>
                  <Text>tablet</Text>
                  <Qualifier>DoseUnitOfMeasure</Qualifier>
                  <Code>C48542</Code>
                </DoseUnitOfMeasure>
              </Dosage>
              <RouteOfAdministration>
                <Text>oral route</Text>
                <Qualifier>SNOMED</Qualifier>
                <Code>26643006</Code>
              </RouteOfAdministration>
            </DoseAdministration>
            <TimingAndDuration>
              <Frequency>
                <FrequencyNumericValue>2</FrequencyNumericValue>
                <FrequencyUnits>
                  <Text>day</Text>
                  <Qualifier>SNOMED</Qualifier>
                  <Code>258703001</Code>
                </FrequencyUnits>
              </Frequency>
            </TimingAndDuration>
          </Instruction>
        </Sig>
        <RxFillIndicator>Not Dispensed</RxFillIndicator>
      </MedicationPrescribed>
    </RxChangeResponse>
  </Body>
</Message>