<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6927347909001</From>
		<MessageID>fb886f071fb84c4ab9a4ce615e8fee72</MessageID>
		<SentTime>2021-01-01T09:37:53.0Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<TestMessage>1</TestMessage>
		<PrescriberOrderNumber>CORE NEWRX-6 PON</PrescriberOrderNumber>
	</Header>
	<Body>
		<RxRenewalResponse>
			<Response>
				<ApprovedWithChanges></ApprovedWithChanges>
			</Response>
			<AllergyOrAdverseEvent>
				<Allergies> <!-- 1 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Allergy to drug</Text>
						<Code>416098002</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Penicillin</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>Drug-induced anaphylactoid reaction</Text>
						<Code>241947002</Code>
					</ReactionCoded>
				</Allergies>
				<Allergies> <!-- 2 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Allergy to food</Text>
						<Code>414285001</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Almond</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>Anaphylaxis caused by tree nut</Text>
						<Code>441495001</Code>
					</ReactionCoded>
				</Allergies>
				<Allergies> <!-- 3 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Adversity to drug</Text>
						<Code>419511003</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Aspirin</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>Aspirin-induced asthma</Text>
						<Code>407674008</Code>
					</ReactionCoded>
					<SeverityCoded>
						<Text>Mild to moderate</Text>
						<Code>371923003</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 4 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Allergy to drug</Text>
						<Code>416098002</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Hydrocortisone</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>Itching of skin</Text>
						<Code>418363000</Code>
					</ReactionCoded>
				</Allergies>
				<Allergies> <!-- 5 -->
					<SourceOfInformation>C</SourceOfInformation>
					<AdverseEvent>
						<Text>Propensity to adverse reactions</Text>
						<Code>420134006</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Imidazole antifungals</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Mild</Text>
						<Code>255604002</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 6 -->
					<SourceOfInformation>C</SourceOfInformation>
					<AdverseEvent>
						<Text>Propensity to adverse reactions</Text>
						<Code>420134006</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>4JVD4X01MJ</Code>
						<Qualifier>UN</Qualifier>
						<Text>Imidazole salicylate</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Mild</Text>
						<Code>255604002</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 7 -->
					<SourceOfInformation>C</SourceOfInformation>
					<AdverseEvent>
						<Text>Propensity to adverse reactions</Text>
						<Code>420134006</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Phenothiazines</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Mild</Text>
						<Code>255604002</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 8 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Adversity to drug</Text>
						<Code>419511003</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>L35JN3I7SJ</Code>
						<Qualifier>UN</Qualifier>
						<Text>Ramipril</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Mild to moderate</Text>
						<Code>371923003</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 9 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Propensity to adverse reactions</Text>
						<Code>420134006</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>5101OP7P2I</Code>
						<Qualifier>UN</Qualifier>
						<Text>Saccharin</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Mild</Text>
						<Code>255604002</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 10 -->
					<SourceOfInformation>C</SourceOfInformation>
					<AdverseEvent>
						<Text>Propensity to adverse reactions</Text>
						<Code>420134006</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Text>Thiazide-type diuretics</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Mild</Text>
						<Code>255604002</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 11 -->
					<SourceOfInformation>C</SourceOfInformation>
					<AdverseEvent>
						<Text>Adversity to drug</Text>
						<Code>419511003</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>057Y626693</Code>
						<Qualifier>UN</Qualifier>
						<Text>Neomycin</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Severe</Text>
						<Code>24484000</Code>
					</SeverityCoded>
				</Allergies>
				<Allergies> <!-- 12 -->
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Allergy to drug</Text>
						<Code>416098002</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>5P4DHS6ENR</Code>
						<Qualifier>UN</Qualifier>
						<Text>Benzonatate</Text>
					</DrugProductCoded>
					<SeverityCoded>
						<Text>Severe</Text>
						<Code>24484000</Code>
					</SeverityCoded>
				</Allergies>
			</AllergyOrAdverseEvent>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Witbrace</LastName>
						<FirstName>Rodger</FirstName>
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>1982-06-29</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>1410 Hanaford Ave</AddressLine1>
						<City>Bismarck</City>
						<StateProvince>ND</StateProvince>
						<PostalCode>58501</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>1655458</NCPDPID>
					<StateLicenseNumber>784577%PH47%24R</StateLicenseNumber>
					<MedicareNumber>755449</MedicareNumber>
					<MedicaidNumber>755449</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Shollenberger Pharmacy</BusinessName>
				<Address>
					<AddressLine1>2002 S. McDowell Blvd Ext</AddressLine1>
					<City>Petaluma</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>94954</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>CV5412421574</StateLicenseNumber>
					<MedicareNumber>335214</MedicareNumber>
					<MedicaidNumber>335214</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTPRX.21574</CertificateToPrescribe>
					<Data2000WaiverID>*********</Data2000WaiverID>
					<REMSHealthcareProviderEnrollmentID>8664416-UP-332</REMSHealthcareProviderEnrollmentID>
				</Identification>
				<Name>
					<LastName>Pinkerton</LastName>
					<FirstName>Umbriana</FirstName>
					<Suffix>MD</Suffix>
				</Name>
				<Address>
					<AddressLine1>6925 Bessemer Ave</AddressLine1>
					<City>Cleveland</City>
					<StateProvince>OH</StateProvince>
					<PostalCode>44127</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<MedicationResponse>
				<DrugDescription>Epinephrine 0.3 mg auto-injector</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>49502010202</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<Strength>
						<StrengthValue>0.3</StrengthValue>
						<StrengthForm>
							<Code>C91227</Code>
						</StrengthForm>
						<StrengthUnitOfMeasure>
							<Code>C28253</Code>
						</StrengthUnitOfMeasure>
					</Strength>
					<DrugDBCode>
						<Code>1870207</Code>
						<Qualifier>SCD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>2</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C64933</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<WrittenDate>
					<Date>2021-04-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>3</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>T7805XA</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Anaphylactic reaction due to tree nuts and seeds, initial encounter</Description>
					</Primary>
				</Diagnosis>
				<Sig>
					<SigText>Inject in thigh if needed for severe allergic reaction. Call 911.</SigText>
				</Sig>
				<RxFillIndicator>Cancel All Fill Statuses</RxFillIndicator>
			</MedicationResponse>
		</RxRenewalResponse>
	</Body>
</Message>
