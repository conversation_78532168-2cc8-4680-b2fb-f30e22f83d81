<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">8741932433002</From>
		<MessageID>46b1a19319fd445d8e7a6fbdfa3efab6</MessageID>
		<RelatesToMessageID>!</RelatesToMessageID>
		<SentTime>2019-01-01T16:28:56.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<RxReferenceNumber>!</RxReferenceNumber>
		<PrescriberOrderNumber>TRANSFER NEWRX A and B</PrescriberOrderNumber>
	</Header>
	<Body>
		<RxRenewalResponse>
			<Response>
				<Denied>
					<ReasonCode>AG</ReasonCode>
				</Denied>
			</Response>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Abingdon</LastName>
						<FirstName>Perseus</FirstName>
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>2012-09-01</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>98 Bayview Ave</AddressLine1>
						<City>New Rochelle</City>
						<StateProvince>NY</StateProvince>
						<PostalCode>10805</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
						</PrimaryTelephone>
						<ElectronicMail><EMAIL></ElectronicMail>
						<HomeTelephone>
							<Number>**********</Number>
						</HomeTelephone>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>8455475</NCPDPID>
					<StateLicenseNumber>MO-**********</StateLicenseNumber>
					<MedicareNumber>335476</MedicareNumber>
					<MedicaidNumber>335476</MedicaidNumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Rapid-Rx Online Pharmacy</BusinessName>
				<Address>
					<AddressLine1>52 Lukens Dr</AddressLine1>
					<City>New Castle</City>
					<StateProvince>DE</StateProvince>
					<PostalCode>19720</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>DC121_123_933</StateLicenseNumber>
					<MedicareNumber>154745</MedicareNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP-DC-123.933</CertificateToPrescribe>
					<Data2000WaiverID>*********</Data2000WaiverID>
					<REMSHealthcareProviderEnrollmentID>4521445-TM-457</REMSHealthcareProviderEnrollmentID>
				</Identification>
				<Name>
					<LastName>McTavish</LastName>
					<FirstName>Tarquin</FirstName>
					<Suffix>MD</Suffix>
					<Prefix>MR</Prefix>
				</Name>
				<Address>
					<AddressLine1>1818 Clydesdale Place NW</AddressLine1>
					<AddressLine2># 2210</AddressLine2>
					<City>Washington</City>
					<StateProvince>DC</StateProvince>
					<PostalCode>20009</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- height -->
					<VitalSign>8302-2</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>58</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-12-20</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>75</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-12-23</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationResponse>
				<DrugDescription>entecavir 1 mg tablet</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>65862084230</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<DrugDBCode>
						<Code>485436</Code>
						<Qualifier>SCD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>30</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C48542</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<WrittenDate>
					<Date>2019-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>1</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>B189</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Chronic viral hepatitis, unspecified</Description>
					</Primary>
				</Diagnosis>
				<Note>Instruct caregiver to ensure medication is taken at a defined schedule</Note>				
				<Sig>
					<SigText>Take 1 tablet orally once per day</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>19.12e</FMTVersion>
					</CodeSystem>
					<Instruction>
							<DoseAdministration>
								<DoseDeliveryMethod>
									<Text>Take</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>419652001</Code>
								</DoseDeliveryMethod>
								<Dosage>
									<DoseQuantity>1</DoseQuantity>
									<DoseUnitOfMeasure>
										<Text>tablet</Text>
										<Qualifier>DoseUnitOfMeasure</Qualifier>
										<Code>C48542</Code>
									</DoseUnitOfMeasure>
								</Dosage>
								<RouteOfAdministration>
									<Text>orally</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>26643006</Code>
								</RouteOfAdministration>
							</DoseAdministration>
							<TimingAndDuration>
								<Frequency>
									<FrequencyNumericValue>1</FrequencyNumericValue>
									<FrequencyUnits>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</FrequencyUnits>
								</Frequency>
							</TimingAndDuration>
					</Instruction>					
				</Sig>
				<RxFillIndicator>All Fill Statuses</RxFillIndicator>
			</MedicationResponse>
		</RxRenewalResponse>
	</Body>
</Message>
