<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>CORE NEWRX 3</PrescriberOrderNumber>
	</Header>
	<Body>
		<NewRx>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000001011</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>031430643</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>PBMB</PayerName>
				<CardholderID>123456789</CardholderID>
				<GroupID>AA1V</GroupID>
				<PBMMemberID>HQR%K883883%ZZ88-002</PBMMemberID>
			</BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>7417234</PayerID>
                    <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
                    <IINNumber>444444</IINNumber>
               </PayerIdentification>
                <PayerName>Apply Patient Savings</PayerName>
                <GroupID>2388</GroupID>
                <PayerType>M</PayerType>
            </BenefitsCoordination>			
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>RightRx</PayerID>
                    <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
                    <IINNumber>444444</IINNumber>
               </PayerIdentification>
                <PayerName>CASH CARD</PayerName>
                <GroupID>RXSURE-XT</GroupID>
                <PayerType>L</PayerType>
            </BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Whiteside</LastName>
						<FirstName>Kara</FirstName>
					</Name>
					<Gender>F</Gender>
					<DateOfBirth>
						<Date>1952-10-11</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>23230 Seaport</AddressLine1>
						<City>Akron</City>
						<StateProvince>OH</StateProvince>
						<PostalCode>44306</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
						</PrimaryTelephone>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>6445547</NCPDPID>
					<StateLicenseNumber>MASS PH897 9211G</StateLicenseNumber>
					<MedicareNumber>566977</MedicareNumber>
					<MedicaidNumber>566977</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Lawrence Academy Rx 10.6</BusinessName>
				<Address>
					<AddressLine1>235 Main St</AddressLine1>
					<City>Groton</City>
					<StateProvince>MA</StateProvince>
					<PostalCode>01450</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Macnair</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4211</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>7079442121</Number>
					</Fax>
					<HomeTelephone>
						<Number>7074775441</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>175</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2019-01-01</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>Prednisone 5 mg tablet</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>59746017210</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<Strength>
						<StrengthValue>5</StrengthValue>
						<StrengthForm>
							<Code>C42998</Code>
						</StrengthForm>
						<StrengthUnitOfMeasure>
							<Code>C28253</Code>
						</StrengthUnitOfMeasure>
					</Strength>
					<DrugDBCode>
						<Code>312617</Code>
						<Qualifier>SCD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>24</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C48542</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>6</DaysSupply>
				<WrittenDate>
					<Date>2019-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>0</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>D6959</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Secondary thrombocytopenia</Description>
					</Primary>
				</Diagnosis>
				<Note>This is a tapering sig</Note>
				<DrugCoverageStatusCode>AP</DrugCoverageStatusCode>				
				<Sig>
					<SigText>Take 6 tablets by mouth once daily for 2 days, then take 4 tablets by mouth once daily for 2 days, then take 2 tablets by mouth once daily for 2 days</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>19.12e</FMTVersion>
					</CodeSystem>
					<Instruction>
							<DoseAdministration>
								<DoseDeliveryMethod>
									<Text>Take</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>419652001</Code>
								</DoseDeliveryMethod>
								<Dosage>
									<DoseQuantity>6</DoseQuantity>
									<DoseUnitOfMeasure>
										<Text>tablet</Text>
										<Qualifier>DoseUnitOfMeasure</Qualifier>
										<Code>C48542</Code>
									</DoseUnitOfMeasure>
								</Dosage>
								<RouteOfAdministration>
									<Text>oral route</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>26643006</Code>
								</RouteOfAdministration>
							</DoseAdministration>
							<TimingAndDuration>
								<Frequency>
									<FrequencyNumericValue>1</FrequencyNumericValue>
									<FrequencyUnits>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</FrequencyUnits>
								</Frequency>
							</TimingAndDuration>
							<TimingAndDuration>
								<Duration>
									<DurationNumericValue>2</DurationNumericValue>
									<DurationText>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</DurationText>
								</Duration>
							</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>4</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>tablet</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C48542</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>1</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>2</DurationNumericValue>
								<DurationText>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>2</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>tablet</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C48542</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>1</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>2</DurationNumericValue>
								<DurationText>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
				</Sig>
				<RxFillIndicator>All Fill Statuses Except Transferred</RxFillIndicator>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>
