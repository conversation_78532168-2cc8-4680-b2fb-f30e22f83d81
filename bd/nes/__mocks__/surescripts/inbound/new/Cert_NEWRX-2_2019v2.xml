<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<TestMessage>1</TestMessage>
		<TertiaryIdentifier>A1B</TertiaryIdentifier>
		<PrescriberOrderNumber>CORE NEWRX 2 MAX-POP TEST CASE #2a</PrescriberOrderNumber>
		<PrescriberOrderGroup>
			<OrderGroupNumber>CORE NEWRX 2 MAX-POP TESTCASE GRP</OrderGroupNumber>
			<ItemCountInOrderGroup>2</ItemCountInOrderGroup>
			<TotalCountForOrderGroup>2</TotalCountForOrderGroup>
			<OrderGroupReason>NewRx</OrderGroupReason>
		</PrescriberOrderGroup>
	</Header>
	<Body>
		<NewRx>
			<UrgencyIndicatorCode>X</UrgencyIndicatorCode>
			<AllergyOrAdverseEvent>
				<Allergies>
					<SourceOfInformation>P</SourceOfInformation>
					<AdverseEvent>
						<Text>Adversity to drug</Text>
						<Code>419511003</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>00904272561</Code>
						<Qualifier>ND</Qualifier>
						<Text>Sulfamethoxazole and trimethoprim</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>Hives</Text>
						<Code>247472004</Code>
					</ReactionCoded>
					<SeverityCoded>
						<Text>Moderate</Text>
						<Code>6736007</Code>
					</SeverityCoded>
				</Allergies>
			</AllergyOrAdverseEvent>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000022649</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>031430643</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>PHARMACEUTICAL LITE FOR MEDICARE AND MEDICAID SERVICES (PLS) PBM/PAYER</PayerName>
				<CardholderID>HEREISACARDHOLDERIDTESTINGMAXLENGTH</CardholderID>
				<CardHolderName>
					<LastName>Usumacintacoatzacoalcosniltepecvera</LastName>
					<FirstName>Juancarlosguadalupepaploapan</FirstName>
				</CardHolderName>
				<GroupID>THISGROUPIDISATTHEMAXIMUMLENGTHOF35</GroupID>
				<GroupName>HEREISAREALLYLONGANDOVERDONEGROUPNAMETOTESTLONG,BUTNOTMAX,SUPPORT</GroupName>
				<PBMMemberID>PLS$KKWS8826-JSHG82_NW91%92KS ZZQ8&amp;ZZQ9-TEST51CHARS</PBMMemberID>
			</BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>WENO</PayerID>
                    <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
                    <IINNumber>444444</IINNumber>
               </PayerIdentification>
                <PayerName>CASH CARD</PayerName>
                <GroupID>BSURE11</GroupID>
                <PayerType>L</PayerType>
            </BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>7417234</PayerID>
                    <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
                    <IINNumber>444444</IINNumber>
               </PayerIdentification>
                <PayerName>Apply Patient Savings</PayerName>
                <GroupID>2388</GroupID>
                <PayerType>M</PayerType>
            </BenefitsCoordination>
        <Patient>
				<HumanPatient>
					<Name>
						<LastName>Usumacintacoatzacoalcosniltepecvera</LastName>
						<FirstName>Juancarlosguadalupepaploapan</FirstName>
						<MiddleName>Franciscolisandroculiacan</MiddleName>
						<Suffix>Junior</Suffix>
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>2004-06-21</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>27732 West Alameda Potholeladen Street</AddressLine1>
						<AddressLine2>Apt 425-B</AddressLine2>
						<City>Rancho Cucamonga</City>
						<StateProvince>CA</StateProvince>
						<PostalCode>917011515</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<SupportsSMS>Y</SupportsSMS>
						</PrimaryTelephone>
						<ElectronicMail><EMAIL></ElectronicMail>
						<HomeTelephone>
							<Number>**********</Number>
						</HomeTelephone>
						<WorkTelephone>
							<Number>**********</Number>
							<Extension>45422142</Extension>
							<SupportsSMS>N</SupportsSMS>
						</WorkTelephone>
						<OtherTelephone>
							<Number>**********</Number>
						</OtherTelephone>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>2455142</NCPDPID>
					<StateLicenseNumber>796597%PH12%82R</StateLicenseNumber>
					<MedicareNumber>886112</MedicareNumber>
					<MedicaidNumber>886112</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Medi-Blue Rapid Clinic (000)</BusinessName>
				<Address>
					<AddressLine1>2165-B1 Northpoint Parkway</AddressLine1>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95407</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<UPIN>0</UPIN>
					<DEANumber>*********</DEANumber>
					<HIN>0</HIN>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Macnair</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4221</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>7079442121</Number>
					</Fax>
					<HomeTelephone>
						<Number>7074775441</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- height -->
					<VitalSign>8302-2</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>59</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-11-20</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>120</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-11-23</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part</DrugDescription>
				<Quantity>
					<Value>900</Value>
					<CodeListQualifier>CF</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C28254</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>30</DaysSupply>
				<WrittenDate>
					<Date>2019-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>1</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>K1233</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Oral mucositis (ulcerative) due to radiation</Description>
					</Primary>
					<Secondary>
						<Code>Z510</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Encounter for antineoplastic radiation therapy</Description>
					</Secondary>
				</Diagnosis>
				<PriorAuthorization>Q22759CB9475YBV2985BV2B2C43VV54</PriorAuthorization>
				<Note>Patient requested peppermint flavoring if possible. Please provide appropriate documentation to patient for how to use this product, including not swallowing solution. A child-resistant package also requested.</Note>
				<DrugCoverageStatusCode>UN</DrugCoverageStatusCode>
				<Sig>
					<SigText>Swish and spit 15 mL orally for 1 minute every 12 hours</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>19.12e</FMTVersion>
					</CodeSystem>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Swish</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>421805007</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>15</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>mL</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C28254</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Interval>
								<IntervalNumericValue>12</IntervalNumericValue>
								<IntervalUnits>
									<Text>hours</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>307467005</Code>
								</IntervalUnits>
							</Interval>
							<TimingClarifyingFreeText>for 1 minute every 12 hours</TimingClarifyingFreeText>
						</TimingAndDuration>
					</Instruction>
				</Sig>
				<RxFillIndicator>Dispensed And Partially Dispensed</RxFillIndicator>
				<DeliveryRequest>FIRST FILL DELIVERY</DeliveryRequest>
				<DeliveryLocation>CONTACT PATIENT FOR DELIVERY</DeliveryLocation>
				<FlavoringRequested>Y</FlavoringRequested>
				<CompoundInformation>
					<FinalCompoundPharmaceuticalDosageForm>C68996</FinalCompoundPharmaceuticalDosageForm>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Diphenhydramine 12.5 mg/5 mL</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>12.5</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- solution -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C91131</Code>
									<!-- mg per 5 mL -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>300</Value>
							<!-- 300 mL as 1 of 3 equal parts to a 900 mL bottle -->
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Viscous lidocaine 2%</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>2</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- Solution (viscous lidocaine) -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C25613</Code>
									<!-- percentage -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>300</Value>
							<!-- 300 mL as 1 of 3 equal parts to a 900 mL bottle -->
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Maalox oral suspension</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>200/200/20</StrengthValue>
								<StrengthForm>
									<Code>C68992</Code>
									<!-- oral suspension -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C91131</Code>
									<!-- mg / 5 mL -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>300</Value>
							<!-- 300 mL as 1 of 3 equal parts to a 900 mL bottle -->
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
				</CompoundInformation>
			</MedicationPrescribed>
			<Supervisor>
				<NonVeterinarian>
					<Identification>
						<NPI>**********</NPI>
					</Identification>
					<Name>
						<LastName>Martinson-McPherson</LastName>
						<FirstName>Julianne</FirstName>
						<MiddleName>Annabelle</MiddleName>
						<Suffix>PhD</Suffix>
					</Name>
					<Address>
						<AddressLine1>1425 Mendocino Ave</AddressLine1>
						<AddressLine2>Suite 12-A</AddressLine2>
						<City>Santa Rosa</City>
						<StateProvince>CA</StateProvince>
						<PostalCode>95401</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<Extension>455142</Extension>
							<SupportsSMS>N</SupportsSMS>
						</PrimaryTelephone>
					</CommunicationNumbers>
				</NonVeterinarian>
			</Supervisor>
			<ProhibitRenewalRequest>false</ProhibitRenewalRequest>
		</NewRx>
	</Body>
</Message>
