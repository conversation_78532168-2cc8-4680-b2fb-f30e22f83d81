<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<TestMessage>1</TestMessage>
		<TertiaryIdentifier>A1B</TertiaryIdentifier>
		<PrescriberOrderNumber>CANCELRX TEST CASE #4a</PrescriberOrderNumber>
		<PrescriberOrderGroup>
			<OrderGroupNumber>CANCELRX TEST CASE #4a</OrderGroupNumber>
			<ItemCountInOrderGroup>2</ItemCountInOrderGroup>
			<TotalCountForOrderGroup>2</TotalCountForOrderGroup>
			<OrderGroupReason>Other</OrderGroupReason>
		</PrescriberOrderGroup>
	</Header>
	<Body>
		<NewRx>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000021633</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>PBMF</PayerName>
				<CardholderID>8351ZD</CardholderID>
				<GroupID>JW92983</GroupID>
				<GroupName>JW MID-CA#7</GroupName>
				<PBMMemberID>PBM-ZZ-T92831 8351%ZD</PBMMemberID>
			</BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Identification>
						<MedicareNumber>541221</MedicareNumber>
					</Identification>
					<Name>
						<LastName>DELAPLAINE</LastName>
						<FirstName>ZACHARY</FirstName>
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>2010-12-01</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>901 Sauvblanc Blvd</AddressLine1>
						<City>Petaluma</City>
						<StateProvince>CA</StateProvince>
						<PostalCode>94952</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>1655458</NCPDPID>
					<StateLicenseNumber>784577%PH47%24R</StateLicenseNumber>
					<MedicareNumber>755449</MedicareNumber>
					<MedicaidNumber>755449</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Shollenberger Pharmacy</BusinessName>
				<Address>
					<AddressLine1>2002 S. McDowell Blvd Ext</AddressLine1>
					<City>Petaluma</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>94954</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
					<Identification>
						<StateLicenseNumber>F-HH214422#221454%25LONG</StateLicenseNumber>
						<MedicareNumber>112422</MedicareNumber>
						<MedicaidNumber>214421</MedicaidNumber>
						<DEANumber>*********</DEANumber>
						<SocialSecurity>44518299</SocialSecurity>
						<NPI>**********</NPI>
						<CertificateToPrescribe>MP%884928976</CertificateToPrescribe>
						<Data2000WaiverID>*********</Data2000WaiverID>
						<REMSHealthcareProviderEnrollmentID>122931441-1992XX-THISIS30#LONG</REMSHealthcareProviderEnrollmentID>
					</Identification>
					<Specialty>208D00000X</Specialty>
					<PracticeLocation>
						<BusinessName>Caplan and Franklin Care Center</BusinessName>
					</PracticeLocation>
					<Name>
						<LastName>Popularilimaximalli</LastName>
						<FirstName>Maximallianna</FirstName>
						<MiddleName>Largelyfilledup</MiddleName>
						<Suffix>MD</Suffix>
						<Prefix>MS</Prefix>
					</Name>
					<Address>
						<AddressLine1>29919-A Hugelly Populated Street</AddressLine1>
						<AddressLine2>Suite 22001</AddressLine2>
						<City>Howey In Hills</City>
						<StateProvince>FL</StateProvince>
						<PostalCode>334112200</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<Extension>44776</Extension>
						</PrimaryTelephone>
						<ElectronicMail><EMAIL></ElectronicMail>
						<Fax>
							<Number>**********</Number>
						</Fax>
						<HomeTelephone>
							<Number>3521574487</Number>
							<SupportsSMS>N</SupportsSMS>
						</HomeTelephone>
					</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>28</Value>
					<UnitOfMeasure>[kg]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2021-01-26</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>BD 3mL syringe 25G x 1</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>Supply</Code>
						<Qualifier>UP</Qualifier>
					</ProductCode>
				</DrugCoded>
				<Quantity>
					<Value>100</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C64933</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>30</DaysSupply>
				<WrittenDate>
					<DateTime>2018-01-26T12:22:00.000</DateTime>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>2</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>E109</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Type 1 diabetes mellitus without complications</Description>
					</Primary>
				</Diagnosis>
				<Note>Supply - 100 syringes please</Note>
				<DrugUseEvaluation>
					<ServiceReasonCode>PS</ServiceReasonCode>
					<ProfessionalServiceCode>SC</ProfessionalServiceCode>
					<ClinicalSignificanceCode>3</ClinicalSignificanceCode>
					<AcknowledgementReason>Substitute as necessary, please provide documentation to patient for assistance</AcknowledgementReason>
				</DrugUseEvaluation>
				<Sig>
					<SigText>Use as directed every 6 hours</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>19.12e</FMTVersion>
					</CodeSystem>
					<Instruction>
						<InstructionIndicator>MEDICALENCOUNTER</InstructionIndicator>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>6</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>hour</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258702006</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
					</Instruction>
				</Sig>
				<RxFillIndicator>Cancel All Fill Statuses</RxFillIndicator>
				<DiabeticSupply>
					<TestingFrequency>4</TestingFrequency>   <!-- 4 times a day is every 6 hrs -->
					<SupplyIndicator>Y</SupplyIndicator>
					<InsulinDependent>Y</InsulinDependent>
					<HasAutomatedInsulinDevice>N</HasAutomatedInsulinDevice>
				</DiabeticSupply>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>