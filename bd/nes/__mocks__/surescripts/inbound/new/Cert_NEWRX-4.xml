<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>CORE NEWRX 4</PrescriberOrderNumber>
	</Header>
	<Body>
		<NewRx>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000022648</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<NAICCode>004327</NAICCode>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<CardholderID>E8-54221</CardholderID>
				<GroupID>GWE8</GroupID>
				<PBMMemberID>JJQQ77%PBM-UID-9654SDSFDW351CZ%SXD8IGI2X</PBMMemberID>
			</BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Addington</LastName>
						<FirstName>Winston</FirstName>
						<MiddleName>Barnaby</MiddleName>
						<Suffix>3rd</Suffix>						
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>1940-03-14</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>178 Paradise Crescent</AddressLine1>
						<City>Royal Palm Beach</City>
						<StateProvince>FL</StateProvince>
						<PostalCode>33411</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
						</PrimaryTelephone>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>8455475</NCPDPID>
					<StateLicenseNumber>MO-**********</StateLicenseNumber>
					<MedicareNumber>335476</MedicareNumber>
					<MedicaidNumber>335476</MedicaidNumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Rapid-Rx Online Pharmacy</BusinessName>
				<Address>
					<AddressLine1>52 Lukens Dr</AddressLine1>
					<City>New Castle</City>
					<StateProvince>DE</StateProvince>
					<PostalCode>19720</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
					<Identification>
						<StateLicenseNumber>F-HH214422#221454%25LONG</StateLicenseNumber>
						<MedicareNumber>112422</MedicareNumber>
						<MedicaidNumber>214421</MedicaidNumber>
						<DEANumber>*********</DEANumber>
						<SocialSecurity>44518299</SocialSecurity>
						<NPI>**********</NPI>
						<CertificateToPrescribe>MP%884928976</CertificateToPrescribe>
						<Data2000WaiverID>*********</Data2000WaiverID>
						<REMSHealthcareProviderEnrollmentID>122931441-1992XX-THISIS30#LONG</REMSHealthcareProviderEnrollmentID>
					</Identification>
					<Specialty>208D00000X</Specialty>
					<PracticeLocation>
						<BusinessName>Caplan &amp; Franklin Care Center</BusinessName>
					</PracticeLocation>
					<Name>
						<LastName>Popularilimaximalli</LastName>
						<FirstName>Maximallianna</FirstName>
						<MiddleName>Largelyfilledup</MiddleName>
						<Suffix>MD</Suffix>
						<Prefix>MS</Prefix>
					</Name>
					<Address>
						<AddressLine1>29919-A Hugelly Populated Street</AddressLine1>
						<AddressLine2>Suite 22001</AddressLine2>
						<City>Howey In The Hills</City>
						<StateProvince>FL</StateProvince>
						<PostalCode>334112200</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<Extension>44776</Extension>
						</PrimaryTelephone>
						<ElectronicMail><EMAIL></ElectronicMail>
						<Fax>
							<Number>**********</Number>
						</Fax>
						<HomeTelephone>
							<Number>3521574487</Number>
							<SupportsSMS>N</SupportsSMS>
						</HomeTelephone>
					</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<MedicationPrescribed>
				<DrugDescription>Fosamax Plus D 70 mg-5600 IU tablet</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>78206013601</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<DrugDBCode>
						<Code>904465</Code>
						<Qualifier>SBD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>4</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C48542</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>90</DaysSupply>
				<WrittenDate>
					<Date>2019-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>2</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>M810</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Age-related osteoporosis without current pathological fracture</Description>
					</Primary>
				</Diagnosis>
				<Sig>
					<SigText>Take 1 tablet by mouth once weekly in the morning; take with 12+ ounces of only water at least one half-hour before any meal, beverage or medication and remain standing upright.  Do not lie down or recline for at least 30 minutes after administering</SigText>
				</Sig>
				<RxFillIndicator>Dispensed And Partially Dispensed</RxFillIndicator>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>
