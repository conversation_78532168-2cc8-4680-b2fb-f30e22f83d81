<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="file:///C:/Users/<USER>/cjs/Work_info/Guides/Schemas/2017071/transport.xsd">
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">1260306616002</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2022-11-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>CORE NEWRX 1 for-S</PrescriberOrderNumber>
	</Header>
	<Body>
		<NewRx>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000021633</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>PBMF</PayerName>
				<CardholderID>8351ZD</CardholderID>
				<GroupID>JW92983</GroupID>
				<GroupName>JW MID-CA#7</GroupName>
				<PBMMemberID>PBM-ZZ-T92831 8351%ZD</PBMMemberID>
			</BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Delaplaine</LastName>
						<FirstName>Zachary</FirstName>
					</Name>
					<Gender>M</Gender>
					<DateOfBirth>
						<Date>2010-12-01</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>901 Sauvblanc Blvd</AddressLine1>
						<City>Petaluma</City>
						<StateProvince>CA</StateProvince>
						<PostalCode>94952</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>1655458</NCPDPID>
					<StateLicenseNumber>784577%PH47%24R</StateLicenseNumber>
					<MedicareNumber>755449</MedicareNumber>
					<MedicaidNumber>755449</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Shollenberger Pharmacy</BusinessName>
				<Address>
					<AddressLine1>2002 S. McDowell Blvd Ext</AddressLine1>
					<City>Petaluma</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>94954</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicareNumber>0</MedicareNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>Medi-Blue Rapid Clinic</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Macnair</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>2165-B1 Northpoint Parkway</AddressLine1>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95407</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
					<HomeTelephone>
						<Number>7074775441</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- height -->
					<VitalSign>8302-2</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>51</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2022-10-20</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>62</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2023-01-12</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>amoxicillin 200 mg/5mL oral suspension</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>00093416076</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<DrugDBCode>
						<Code>313850</Code>
						<Qualifier>SCD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>52.555</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C28254</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<WrittenDate>
					<Date>2022-10-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>0</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>H6690</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Acute otitis media, unspecified, unspecified ear</Description>
					</Primary>
				</Diagnosis>
				<Sig>
					<SigText>Take 2.5 mL by mouth three times a day for 7 days. Discard remainder.</SigText>
				</Sig>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>
