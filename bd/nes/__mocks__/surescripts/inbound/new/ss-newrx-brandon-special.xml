<?xml version="1.0" encoding="utf-8"?>
<Message DatatypesVersion="20170715" TransportVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715">
  <Header>
    <To Qualifier="P">1122999</To>
    <From Qualifier="D">1888663552001</From>
    <MessageID>c2e9b92d8d094d23afece57ec63de30bc83</MessageID>
    <SentTime>2024-05-07T23:24:16.6602282Z</SentTime>
    <Security>
      <Sender>
        <TertiaryIdentification>226613</TertiaryIdentification>
      </Sender>
      <Receiver>
        <TertiaryIdentification>1100785</TertiaryIdentification>
      </Receiver>
    </Security>
    <SenderSoftware>
      <SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
      <SenderSoftwareProduct>Surescripts.ErxMessageManager</SenderSoftwareProduct>
      <SenderSoftwareVersionRelease>**********</SenderSoftwareVersionRelease>
    </SenderSoftware>
    <PrescriberOrderNumber>6d3b4554b8c244e38b0a3e3fafedb3f6</PrescriberOrderNumber>
    <DigitalSignature Version="1.1">
      <DigitalSignatureIndicator>true</DigitalSignatureIndicator>
    </DigitalSignature>
  </Header>
  <Body>
    <NewRx>
      <BenefitsCoordination>
        <PayerIdentification>
          <PayerID>T00000000021633</PayerID>
          <MutuallyDefined>000021456</MutuallyDefined>
          <IINNumber>444444</IINNumber>
          <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
        </PayerIdentification>
        <PayerName>PBMF</PayerName>
        <CardholderID>8351ZD</CardholderID>
        <GroupID>JW92983</GroupID>
        <GroupName>JW MID-CA#7</GroupName>
        <PBMMemberID>PBM-ZZ-T92831 8351%ZD</PBMMemberID>
      </BenefitsCoordination>
      <Patient>
        <HumanPatient>
          <Name>
            <LastName>Delaplaine</LastName>
            <FirstName>Zachary</FirstName>
          </Name>
          <Gender>M</Gender>
          <DateOfBirth>
            <Date>2010-12-01</Date>
          </DateOfBirth>
          <Address>
            <AddressLine1>901 Sauvblanc Blvd</AddressLine1>
            <City>Petaluma</City>
            <StateProvince>CA</StateProvince>
            <PostalCode>94952</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
            </PrimaryTelephone>
          </CommunicationNumbers>
        </HumanPatient>
      </Patient>
      <Pharmacy>
        <Identification>
          <NCPDPID>1122999</NCPDPID>
          <DEANumber>F91234569</DEANumber>
          <NPI>**********</NPI>
        </Identification>
        <BusinessName>Heritage Biologics - Testing</BusinessName>
        <Address>
          <AddressLine1>255 NW Victoria Dr, Suite B</AddressLine1>
          <City>Lees Summit</City>
          <StateProvince>MO</StateProvince>
          <PostalCode>64086</PostalCode>
          <CountryCode>US</CountryCode>
        </Address>
        <CommunicationNumbers>
          <PrimaryTelephone>
            <Number>**********</Number>
          </PrimaryTelephone>
          <Fax>
            <Number>**********</Number>
          </Fax>
        </CommunicationNumbers>
      </Pharmacy>
      <Prescriber>
        <NonVeterinarian>
          <Identification>
            <DEANumber>*********</DEANumber>
            <NPI>**********</NPI>
          </Identification>
          <PracticeLocation>
            <BusinessName>SS Test Practice</BusinessName>
          </PracticeLocation>
          <Name>
            <LastName>SSTestprovider</LastName>
            <FirstName>Territest</FirstName>
            <MiddleName>A</MiddleName>
          </Name>
          <Address>
            <AddressLine1>902 S 2nd Ave</AddressLine1>
            <City>MINNEAPOLIS</City>
            <StateProvince>MN</StateProvince>
            <PostalCode>55410</PostalCode>
            <CountryCode>US</CountryCode>
          </Address>
          <CommunicationNumbers>
            <PrimaryTelephone>
              <Number>**********</Number>
            </PrimaryTelephone>
            <Fax>
              <Number>**********</Number>
            </Fax>
          </CommunicationNumbers>
        </NonVeterinarian>
      </Prescriber>
      <Observation>
        <Measurement>
          <VitalSign>8302-2</VitalSign>
          <LOINCVersion>2.42</LOINCVersion>
          <Value>51</Value>
          <UnitOfMeasure>[in_i]</UnitOfMeasure>
          <UCUMVersion>2.1</UCUMVersion>
          <ObservationDate>
            <Date>2018-01-26</Date>
          </ObservationDate>
        </Measurement>
        <Measurement>
          <VitalSign>29463-7</VitalSign>
          <LOINCVersion>2.42</LOINCVersion>
          <Value>62</Value>
          <UnitOfMeasure>[lb_av]</UnitOfMeasure>
          <UCUMVersion>2.1</UCUMVersion>
          <ObservationDate>
            <Date>2018-01-26</Date>
          </ObservationDate>
        </Measurement>
      </Observation>
      <MedicationPrescribed>
        <DrugDescription>Cotempla XR-ODT 17.3 mg tablet</DrugDescription>
        <DrugCoded>
          <ProductCode>
            <Code>70165020030</Code>
            <Qualifier>ND</Qualifier>
          </ProductCode>
          <Strength>
            <StrengthValue>17.3</StrengthValue>
            <StrengthForm>
              <Code>C42927</Code>
            </StrengthForm>
            <StrengthUnitOfMeasure>
              <Code>C28253</Code>
            </StrengthUnitOfMeasure>
          </Strength>
          <DrugDBCode>
            <Code>1926847</Code>
            <Qualifier>SBD</Qualifier>
          </DrugDBCode>
          <DEASchedule>
            <Code>C48675</Code>
          </DEASchedule>
        </DrugCoded>
        <Quantity>
          <Value>30</Value>
          <CodeListQualifier>38</CodeListQualifier>
          <QuantityUnitOfMeasure>
            <Code>C48542</Code>
          </QuantityUnitOfMeasure>
        </Quantity>
        <WrittenDate>
          <DateTime>2024-05-07T23:24:14.1121722+00:00</DateTime>
        </WrittenDate>
        <Substitutions>1</Substitutions>
        <NumberOfRefills>0</NumberOfRefills>
        <Diagnosis>
          <ClinicalInformationQualifier>1</ClinicalInformationQualifier>
          <Primary>
            <Code>F900</Code>
            <Qualifier>ABF</Qualifier>
            <Description>Attention-deficit hyperactivity disorder, predominantly inattentive type</Description>
          </Primary>
        </Diagnosis>
        <Note>Instruct the patient or caregiver on the appropriate administration instructions</Note>
        <Sig>
          <SigText>Place one whole tablet on the tongue and allow it to disintegrate without chewing or crushing, one time daily for 30 days</SigText>
        </Sig>
        <ReasonForSubstitutionCodeUsed>BRAND MEDICALLY NECESSARY</ReasonForSubstitutionCodeUsed>
        <RxFillIndicator>Cancel All Fill Statuses</RxFillIndicator>
        <OtherMedicationDate>
          <OtherMedicationDate>
            <Date>2019-10-01</Date>
          </OtherMedicationDate>
          <OtherMedicationDateQualifier>EffectiveDate</OtherMedicationDateQualifier>
        </OtherMedicationDate>
      </MedicationPrescribed>
    </NewRx>
  </Body>
</Message>