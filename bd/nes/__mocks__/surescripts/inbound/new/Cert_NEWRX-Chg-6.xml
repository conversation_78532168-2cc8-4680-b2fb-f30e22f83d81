<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>NEWRX for RxChange-6</PrescriberOrderNumber>
		<DigitalSignature Version="1.1">
			<DigitalSignatureIndicator>true</DigitalSignatureIndicator>
		</DigitalSignature>
	</Header>
	<Body>
		<NewRx>
			<AllergyOrAdverseEvent>
				<NoKnownAllergies>Y</NoKnownAllergies>
			</AllergyOrAdverseEvent>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000001010</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>PBMA</PayerName>
				<GroupID>Y8831_9</GroupID>
				<GroupName>SPC-9288</GroupName>
				<PBMMemberID>PBM-ZZ-T92293 8791%AD</PBMMemberID>
			</BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Delaplaine</LastName>
						<FirstName>Angelyne</FirstName>
					</Name>
					<Gender>F</Gender>
					<DateOfBirth>
						<Date>2012-09-01</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>901 Sauvblanc Blvd</AddressLine1>
						<City>Petaluma</City>
						<StateProvince>VA</StateProvince>
						<PostalCode>94952</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>1655458</NCPDPID>
					<StateLicenseNumber>784577%PH47%24R</StateLicenseNumber>
					<MedicareNumber>755449</MedicareNumber>
					<MedicaidNumber>755449</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Shollenberger Pharmacy</BusinessName>
				<Address>
					<AddressLine1>2002 S. McDowell Blvd Ext</AddressLine1>
					<City>Petaluma</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>94954</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1147%24</StateLicenseNumber>
					<MedicaidNumber>658345</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1147%24</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Pimpernel</LastName>
					<FirstName>Marguerite</FirstName>
					<MiddleName>Anne</MiddleName>
					<Suffix>MD</Suffix>
					<Prefix>DR</Prefix>
				</Name>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4221</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<VitalSign>8302-2</VitalSign>
					<!--HEIGHT-->
					<LOINCVersion>2.42</LOINCVersion>
					<Value>46</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2019-01-01</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<VitalSign>29463-7</VitalSign>
					<!--WEIGHT-->
					<LOINCVersion>2.42</LOINCVersion>
					<Value>48</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2019-01-01</Date>
					</ObservationDate>
				</Measurement>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>Vimpat 10 mg/mL oral solution</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>00131541070</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<Strength>
						<StrengthValue>10</StrengthValue>
						<StrengthUnitOfMeasure>
							<Code>C42576</Code>
						</StrengthUnitOfMeasure>
					</Strength>
					<DrugDBCode>
						<Code>99516</Code>
						<Qualifier>SBD</Qualifier>
					</DrugDBCode>
					<DEASchedule>
						<Code>C48679</Code>
					</DEASchedule>
				</DrugCoded>
				<Quantity>
					<Value>400</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C28254</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<WrittenDate>
					<Date>2019-01-01</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>0</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>G40001</Code>
						<Qualifier>ABF</Qualifier>
						<Description>Localization-related (focal) (partial) idiopathic epilepsy and epileptic syndromes with seizures of localized onset, not intractable</Description>
					</Primary>
				</Diagnosis>
				<Note>TBD</Note>
				<Sig>
					<SigText>Take 2 ml by mouth twice daily for 1 week, then take 4 ml by mouth twice daily for 1 week, then take 6 ml by mouth twice daily for 1 week, then take 8 ml by mouth twice daily for 2 weeks</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>16.03d</FMTVersion>
					</CodeSystem>
					<Instruction>
							<DoseAdministration>
								<DoseDeliveryMethod>
									<Text>Take</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>419652001</Code>
								</DoseDeliveryMethod>
								<Dosage>
									<DoseQuantity>2</DoseQuantity>
									<DoseUnitOfMeasure>
										<Text>mL</Text>
										<Qualifier>DoseUnitOfMeasure</Qualifier>
										<Code>C28254</Code>
									</DoseUnitOfMeasure>
								</Dosage>
								<RouteOfAdministration>
									<Text>oral route</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>26643006</Code>
								</RouteOfAdministration>
							</DoseAdministration>
							<TimingAndDuration>
								<Frequency>
									<FrequencyNumericValue>2</FrequencyNumericValue>
									<FrequencyUnits>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</FrequencyUnits>
								</Frequency>
							</TimingAndDuration>
							<TimingAndDuration>
								<Duration>
									<DurationNumericValue>1</DurationNumericValue>
									<DurationText>
										<Text>week</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258705008</Code>
									</DurationText>
								</Duration>
							</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>4</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>mL</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C28254</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>2</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>1</DurationNumericValue>
								<DurationText>
									<Text>week</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258705008</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>6</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>mL</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C28254</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>2</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>1</DurationNumericValue>
								<DurationText>
									<Text>week</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258705008</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>8</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>mL</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C28254</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>2</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>2</DurationNumericValue>
								<DurationText>
									<Text>week</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258705008</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
				</Sig>
				<RxFillIndicator>Cancel All Fill Statuses</RxFillIndicator>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>
