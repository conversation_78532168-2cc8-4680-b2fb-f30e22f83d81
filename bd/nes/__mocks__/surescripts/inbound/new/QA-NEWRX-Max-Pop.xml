<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="file:///C:/Users/<USER>/cjs/Work_info/Guides/Schemas/2017071/transport.xsd">
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2018-05-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts and Maximally-Populated</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Surescripts and Maximally-Populated</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>This field is set to be fifty (50) characters long</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>4987T6H43RKS8746RGSB3O86RS93S9879rq</PrescriberOrderNumber>
		<PrescriberOrderGroup>
			<OrderGroupNumber>DNSDOIUEWY937Y49879274H3SHQ98Y33XX2</OrderGroupNumber>
			<ItemCountInOrderGroup>11</ItemCountInOrderGroup>
			<TotalCountForOrderGroup>12</TotalCountForOrderGroup>
			<OrderGroupReason>MultipleProductsPrescribed</OrderGroupReason>
		</PrescriberOrderGroup>
	</Header>
	<Body>
		<NewRx>
			<UrgencyIndicatorCode>X</UrgencyIndicatorCode>
			<AllergyOrAdverseEvent>
				<Allergies>
					<SourceOfInformation>P</SourceOfInformation>
					<EffectiveDate>
						<Date>1985-12-31</Date>
					</EffectiveDate>
					<ExpirationDate>
						<Date>2018-01-01</Date>
					</ExpirationDate>
					<AdverseEvent>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>419511003</Code>
					</AdverseEvent>
					<DrugProductCoded>
						<Code>12345678911</Code>
						<Qualifier>ND</Qualifier>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
					</DrugProductCoded>
					<ReactionCoded>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>247472004</Code>
					</ReactionCoded>
					<SeverityCoded>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>6736007</Code>
					</SeverityCoded>
				</Allergies>
			</AllergyOrAdverseEvent>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000023152</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>This field is edited and the length is setup to be 70 characters long!</PayerName>
				<CardholderID>This field is ~35~ characters long!</CardholderID>
				<CardHolderName>
					<LastName>This field is ~35~ characters long!</LastName>
					<FirstName>This field is ~35~ characters long!</FirstName>
					<MiddleName>This field is ~35~ characters long!</MiddleName>
					<Suffix>THIS IS 10</Suffix>
					<Prefix>THIS IS 10</Prefix>
				</CardHolderName>
				<GroupID>This field is ~35~ characters long!</GroupID>
				<PayerResponsibilityCode>PP</PayerResponsibilityCode>
				<PatientRelationship>4</PatientRelationship>
				<PersonCode>1</PersonCode>
				<GroupName>This field is edited and the length is setup to be 70 characters long!</GroupName>
				<Address>
					<AddressLine1>This field is ~40~ characters long here!</AddressLine1>
					<AddressLine2>This field is ~40~ characters long here!</AddressLine2>
					<City>This field is ~35~ characters long!</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>*********</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>45554745</Extension>
						<SupportsSMS>Y</SupportsSMS>
					</PrimaryTelephone>
					<Beeper>
						<Number>**********</Number>
						<Extension>32321242</Extension>
						<SupportsSMS>N</SupportsSMS>
					</Beeper>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>*********7</Number>
						<Extension>64646454</Extension>
						<SupportsSMS>N</SupportsSMS>
					</Fax>
					<HomeTelephone>
						<Number>2124451423</Number>
						<Extension>64457457</Extension>
						<SupportsSMS>N</SupportsSMS>
					</HomeTelephone>
					<WorkTelephone>
						<Number>2123324774</Number>
						<Extension>32114514</Extension>
						<SupportsSMS>N</SupportsSMS>
					</WorkTelephone>
					<WorkTelephone>
						<Number>2123324734</Number>
						<Extension>32114521</Extension>
						<SupportsSMS>N</SupportsSMS>
					</WorkTelephone>
					<OtherTelephone>
						<Number>4574547995</Number>
						<Extension>32566226</Extension>
						<SupportsSMS>N</SupportsSMS>
					</OtherTelephone>
					<OtherTelephone>
						<Number>**********</Number>
						<Extension>32566986</Extension>
						<SupportsSMS>N</SupportsSMS>
					</OtherTelephone>
					<DirectAddress>This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!</DirectAddress>
				</CommunicationNumbers>
				<PBMMemberID>This field is edited and the length is setup to now be a full 80 characters long</PBMMemberID>
				<ResponsibleParty>
					<LastName>This field is ~35~ characters long!</LastName>
					<FirstName>This field is ~35~ characters long!</FirstName>
					<MiddleName>This field is ~35~ characters long!</MiddleName>
					<Suffix>THIS IS 10</Suffix>
					<Prefix>THIS IS 10</Prefix>
				</ResponsibleParty>
			</BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>WENO</PayerID>
                    <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
                    <IINNumber>444444</IINNumber>
               </PayerIdentification>
                <PayerName>CASH CARD</PayerName>
                <GroupID>BSURE11</GroupID>
                <PayerType>L</PayerType>
            </BenefitsCoordination>
            <BenefitsCoordination>
                <PayerIdentification>
                    <PayerID>7417234</PayerID>
                    <ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
                    <IINNumber>444444</IINNumber>
               </PayerIdentification>
                <PayerName>Apply Patient Savings</PayerName>
                <GroupID>2388</GroupID>
                <PayerType>M</PayerType>
            </BenefitsCoordination>			
            <Patient>
				<HumanPatient>
					<Name>
					<LastName>This field is ~35~ characters long!</LastName>
					<FirstName>This field is ~35~ characters long!</FirstName>
					<MiddleName>This field is ~35~ characters long!</MiddleName>
					<Suffix>THIS IS 10</Suffix>
					<Prefix>THIS IS 10</Prefix>
					</Name>
					<Gender>U</Gender>
					<DateOfBirth>
						<Date>2002-04-01</Date>
					</DateOfBirth>
					<Address>
					<AddressLine1>This field is ~40~ characters long here!</AddressLine1>
					<AddressLine2>This field is ~40~ characters long here!</AddressLine2>
					<City>This field is ~35~ characters long!</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>*********</PostalCode>
					<CountryCode>US</CountryCode>
					</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>45554745</Extension>
						<SupportsSMS>Y</SupportsSMS>
					</PrimaryTelephone>
					<Beeper>
						<Number>**********</Number>
						<Extension>32321242</Extension>
						<SupportsSMS>N</SupportsSMS>
					</Beeper>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>*********7</Number>
						<Extension>64646454</Extension>
						<SupportsSMS>N</SupportsSMS>
					</Fax>
					<HomeTelephone>
						<Number>2124451423</Number>
						<Extension>64457457</Extension>
						<SupportsSMS>N</SupportsSMS>
					</HomeTelephone>
					<WorkTelephone>
						<Number>2123324774</Number>
						<Extension>32114514</Extension>
						<SupportsSMS>N</SupportsSMS>
					</WorkTelephone>
					<WorkTelephone>
						<Number>2123324734</Number>
						<Extension>32114521</Extension>
						<SupportsSMS>N</SupportsSMS>
					</WorkTelephone>
					<OtherTelephone>
						<Number>4574547995</Number>
						<Extension>32566226</Extension>
						<SupportsSMS>N</SupportsSMS>
					</OtherTelephone>
					<OtherTelephone>
						<Number>**********</Number>
						<Extension>32566986</Extension>
						<SupportsSMS>N</SupportsSMS>
					</OtherTelephone>
					<DirectAddress>This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!</DirectAddress>
				</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>2455142</NCPDPID>
					<StateLicenseNumber>796597%PH12%82R</StateLicenseNumber>
					<MedicareNumber>886112</MedicareNumber>
					<MedicaidNumber>886112</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Medi-Blue Rapid Clinic (000)</BusinessName>
				<Address>
					<AddressLine1>2165-B1 Northpoint Parkway</AddressLine1>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95407</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<UPIN>0</UPIN>
					<DEANumber>*********</DEANumber>
					<HIN>0</HIN>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Thomas</LastName>
					<FirstName>Macnair</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4221</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
					<HomeTelephone>
						<Number>**********</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<Observation>
				<Measurement>
					<!-- height -->
					<VitalSign>8302-2</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>62</Value>
					<UnitOfMeasure>[in_i]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-01-20</Date>
					</ObservationDate>
				</Measurement>
				<Measurement>
					<!-- weight -->
					<VitalSign>29463-7</VitalSign>
					<LOINCVersion>2.42</LOINCVersion>
					<Value>140</Value>
					<UnitOfMeasure>[lb_av]</UnitOfMeasure>
					<UCUMVersion>2.1</UCUMVersion>
					<ObservationDate>
						<Date>2018-01-23</Date>
					</ObservationDate>
				</Measurement>
				<ObservationNotes>This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!</ObservationNotes>
			</Observation>
			<MedicationPrescribed>
				<DrugDescription>This field is established to test the length ability for a hundred-five (105) character long string test!</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>65862084230</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<Strength>
						<StrengthValue>This field is edited and the length is setup to be 70 characters long!</StrengthValue>
						<StrengthForm>
							<Code>C78747</Code>
						</StrengthForm>
						<StrengthUnitOfMeasure>
							<Code>C42576</Code>
						</StrengthUnitOfMeasure>
					</Strength>
					<DrugDBCode>
						<Code>1649988</Code>
						<Qualifier>SCD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>900</Value>
					<CodeListQualifier>CF</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C28254</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<DaysSupply>101</DaysSupply>
				<WrittenDate>
					<DateTime>2018-05-01T13:42:39.7Z</DateTime>
				</WrittenDate>
				<Substitutions>1</Substitutions>
				<NumberOfRefills>1</NumberOfRefills>
				<Diagnosis>
					<ClinicalInformationQualifier>1</ClinicalInformationQualifier>
					<Primary>
						<Code>E876</Code>
						<Qualifier>ABF</Qualifier>
						<Description>This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!</Description>
						<DateOfLastOfficeVisit>
							<DateTime>2018-01-01T13:42:39.7Z</DateTime>
						</DateOfLastOfficeVisit>
					</Primary>
					<Secondary>
						<Code>43339004</Code>
						<Qualifier>LD</Qualifier>   <!-- SNOMED code -->
						<Description>This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!</Description>
						<DateOfLastOfficeVisit>
							<DateTime>2018-01-01T13:42:39.7Z</DateTime>
						</DateOfLastOfficeVisit>
					</Secondary>
				</Diagnosis>
				<PriorAuthorization>This field is ~35~ characters long!</PriorAuthorization>
				<Note>This field is established to test the length ability for two hundred ten (210) character long string test in order to test notes and other fields that are allowed to be that long per the schema and syntax check</Note>
				<DrugUseEvaluation>
					<ServiceReasonCode>PC</ServiceReasonCode>
					<ProfessionalServiceCode>00</ProfessionalServiceCode>
					<ServiceResultCode>00</ServiceResultCode>
					<CoAgent>
						<CoAgentCode>
							<Code>12345678911</Code>
							<Qualifier>99</Qualifier>
							<Description>This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!</Description>
						</CoAgentCode>
					</CoAgent>
					<ClinicalSignificanceCode>2</ClinicalSignificanceCode>
					<AcknowledgementReason>This field is established to test the length ability for a hundred (100) character long string test!</AcknowledgementReason>
				</DrugUseEvaluation>
				<DrugCoverageStatusCode>PA</DrugCoverageStatusCode>
				<PriorAuthorizationStatus>A</PriorAuthorizationStatus>
				<Sig>
					<SigText>Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers 0123456789 and decimals 123456798.123456798 and just a bunch of placeholder junk. That all repeats again and again. Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers 0123456789 and decimals 123456798.123456798 and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers 0123456789 and decimals 123456798.123456798 and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field that tests characters (/?;:~`+-=[]{}|) as well as numbers 0123456789 and decimals 123456798.123456798 and just a bunch of placeholder junk. That all repeats again and again.Here is a full one thousand character sig field test... All to get to 1000 characters in final length, ending here.</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>16.03d</FMTVersion>
					</CodeSystem>
					<Instruction>
							<DoseAdministration>
								<DoseDeliveryMethod>
									<Text>Take</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>419652001</Code>
								</DoseDeliveryMethod>
								<Dosage>
									<DoseQuantity>0.5</DoseQuantity>
									<DoseUnitOfMeasure>
										<Text>tablet</Text>
										<Qualifier>DoseUnitOfMeasure</Qualifier>
										<Code>C48542</Code>
									</DoseUnitOfMeasure>
								</Dosage>
								<RouteOfAdministration>
									<Text>oral route</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>26643006</Code>
								</RouteOfAdministration>
							</DoseAdministration>
							<TimingAndDuration>
								<Frequency>
									<FrequencyNumericValue>2</FrequencyNumericValue>
									<FrequencyUnits>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</FrequencyUnits>
								</Frequency>
							</TimingAndDuration>
							<TimingAndDuration>
								<Duration>
									<DurationNumericValue>1</DurationNumericValue>
									<DurationText>
										<Text>week</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>*********</Code>
									</DurationText>
								</Duration>
							</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>1</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>tablet</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C48542</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>2</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>1</DurationNumericValue>
								<DurationText>
									<Text>week</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>*********</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>1.5</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>tablet</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C48542</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>2</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>1</DurationNumericValue>
								<DurationText>
									<Text>week</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>*********</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
					<MultipleInstructionModifier>THEN</MultipleInstructionModifier>
					<Instruction>
						<DoseAdministration>
							<DoseDeliveryMethod>
								<Text>Take</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>419652001</Code>
							</DoseDeliveryMethod>
							<Dosage>
								<DoseQuantity>2</DoseQuantity>
								<DoseUnitOfMeasure>
									<Text>tablet</Text>
									<Qualifier>DoseUnitOfMeasure</Qualifier>
									<Code>C48542</Code>
								</DoseUnitOfMeasure>
							</Dosage>
							<RouteOfAdministration>
								<Text>oral route</Text>
								<Qualifier>SNOMED</Qualifier>
								<Code>26643006</Code>
							</RouteOfAdministration>
						</DoseAdministration>
						<TimingAndDuration>
							<Frequency>
								<FrequencyNumericValue>2</FrequencyNumericValue>
								<FrequencyUnits>
									<Text>day</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>258703001</Code>
								</FrequencyUnits>
							</Frequency>
						</TimingAndDuration>
						<TimingAndDuration>
							<Duration>
								<DurationNumericValue>2</DurationNumericValue>
								<DurationText>
									<Text>week</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>*********</Code>
								</DurationText>
							</Duration>
						</TimingAndDuration>
					</Instruction>
					<ClarifyingFreeText>This field is established to test the length ability for two hundred fifty five (255) character long string test in order to test free text and misc random other fields...................... that are allowed to be that long per the schema and syntax check</ClarifyingFreeText>
				</Sig>
				<DoNotFill>Y</DoNotFill>
				<TimeZone>
					<TimeZoneIdentifier>UT</TimeZoneIdentifier>
					<TimeZoneDifferenceQuantity>-5</TimeZoneDifferenceQuantity>
				</TimeZone>
				<OrderCaptureMethod>EP</OrderCaptureMethod>
				<ReasonForSubstitutionCodeUsed>BRAND MEDICALLY NECESSARY</ReasonForSubstitutionCodeUsed>
				<SplitScript>B</SplitScript>
				<RxFillIndicator>All Fill Statuses</RxFillIndicator>
				<OfficeOfPharmacyAffairsID>JJSHEYWH992-012</OfficeOfPharmacyAffairsID>
				<DeliveryRequest>ALL FILLS DELIVERY</DeliveryRequest>
				<DeliveryLocation>CONTACT PATIENT FOR DELIVERY</DeliveryLocation>
				<DiabeticSupply>
					<TestingFrequency>12</TestingFrequency>
					<TestingFrequencyNotes>This field is established to test the length ability for two hundred ten (210) character long string test in order to test notes and other fields that are allowed to be that long per the schema and syntax check</TestingFrequencyNotes>
					<SupplyIndicator>N</SupplyIndicator>
					<InsulinDependent>N</InsulinDependent>
					<HasAutomatedInsulinDevice>N</HasAutomatedInsulinDevice>
				</DiabeticSupply>
				<InjuryRelated>OTHER</InjuryRelated>
				<Service>
					<AgencyOfService>
						<BusinessName>This field is edited and the length is setup to be 70 characters long!</BusinessName>
					<Address>
					<AddressLine1>This field is ~40~ characters long here!</AddressLine1>
					<AddressLine2>This field is ~40~ characters long here!</AddressLine2>
					<City>This field is ~35~ characters long!</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>*********</PostalCode>
					<CountryCode>US</CountryCode>
					</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>45554745</Extension>
						<SupportsSMS>Y</SupportsSMS>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>*********7</Number>
						<Extension>64646454</Extension>
						<SupportsSMS>N</SupportsSMS>
					</Fax>
					<OtherTelephone>
						<Number>4574547995</Number>
						<Extension>32566226</Extension>
						<SupportsSMS>N</SupportsSMS>
					</OtherTelephone>
					<OtherTelephone>
						<Number>**********</Number>
						<Extension>32566986</Extension>
						<SupportsSMS>N</SupportsSMS>
					</OtherTelephone>
					<DirectAddress>This field is established to test the length ability for a hundred (100) character long string test!----This field is designed to test a long length of one-hundred-fifty (150!!) characters in order to better test the ability to support them all properly!</DirectAddress>
				</CommunicationNumbers>
				<AgencyContactName>
					<LastName>This field is ~35~ characters long!</LastName>
					<FirstName>This field is ~35~ characters long!</FirstName>
					<MiddleName>This field is ~35~ characters long!</MiddleName>
					<Suffix>THIS IS 10</Suffix>
					<Prefix>THIS IS 10</Prefix>
				</AgencyContactName>
					</AgencyOfService>
					<ServiceType>
						<TypeOfServiceFreeText>This field is edited and the length is setup to now be a full 80 characters long</TypeOfServiceFreeText>
						<TargetedTypeOfServiceFreeText>This field is edited and the length is setup to now be a full 80 characters long</TargetedTypeOfServiceFreeText>
						<EffectiveDate>
							<Date>2018-01-01</Date>
						</EffectiveDate>
						<ExpirationDate>
							<Date>2018-12-31</Date>
						</ExpirationDate>
						<ReasonForMTMServiceFreeText>This field is edited and the length is setup to now be a full 80 characters long</ReasonForMTMServiceFreeText>
						<TypeOfServiceGroupSetting>Y</TypeOfServiceGroupSetting>
					</ServiceType>
				</Service>
				<IVAdministration>
					<NumberOfLumens>12</NumberOfLumens>
					<DiluentAmount>
						<Value>123456.1234</Value>
						<CodeListQualifier>38</CodeListQualifier>
						<QuantityUnitOfMeasure>
							<Code>C28254</Code>
						</QuantityUnitOfMeasure>
					</DiluentAmount>
					<SpecificAdministrationGauge>18</SpecificAdministrationGauge>
					<SpecificAdministrationBrand>SUPER PICC LINE INC</SpecificAdministrationBrand>
					<SpecificAdministrationLength>12</SpecificAdministrationLength>
					<SpecificAdministrationPump>N</SpecificAdministrationPump>
					<IVAccessType>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>C99999</Code>
					</IVAccessType>
					<IVAccessDeviceType>
						<IVAccessDeviceTypeDescription>This field is edited and the length is setup to now be a full 80 characters long</IVAccessDeviceTypeDescription>
						<IVAccessDeviceType>
							<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
							<Code>C99999</Code>
						</IVAccessDeviceType>
					</IVAccessDeviceType>
					<IVAccessCatheterTip>
						<IVAccessCatheterTipDescription>This field is edited and the length is setup to now be a full 80 characters long</IVAccessCatheterTipDescription>
						<IVAccessCatheterTipType>
							<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
							<Code>C99999</Code>
						</IVAccessCatheterTipType>
					</IVAccessCatheterTip>
					<IVInfusion>
						<IVInfusionDescription>This field is edited and the length is setup to now be a full 80 characters long</IVInfusionDescription>
						<IVInfusionType>
							<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
							<Code>C99999</Code>
						</IVInfusionType>
					</IVInfusion>
				</IVAdministration>
				<TreatmentIndicator>CONTINUATION</TreatmentIndicator>
				<ProphylacticOrEpisodic>EPISODIC</ProphylacticOrEpisodic>
				<CurrentTreatmentCycle>3</CurrentTreatmentCycle>
				<NumberOfCyclesPlanned>35</NumberOfCyclesPlanned>
				<Wound>
					<Location>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>310845006</Code>
					</Location>
					<Laterality>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>*********</Code>
					</Laterality>
					<Length>12</Length>
					<Width>1</Width>
					<Depth>2</Depth>
				</Wound>
				<Wound>
					<Location>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>728205001</Code>
					</Location>
					<Laterality>
						<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
						<Code>*********</Code>
					</Laterality>
					<Length>12</Length>
					<Width>3</Width>
					<Depth>2</Depth>
				</Wound>
				<PrescriberCheckedREMS>N</PrescriberCheckedREMS>
				<REMSPatientRiskCategory>This field is ~35~ characters long!</REMSPatientRiskCategory>
				<REMSAuthorizationNumber>This field is designed to test a long length of one-hundred-forty (140!) characters in order to better test the ability to support them all!</REMSAuthorizationNumber>
				<Titration>
					<PharmacyToTitrateDose>Y</PharmacyToTitrateDose>
					<TitrationDose>
						<Measurement>
						<VitalSign>29463-7</VitalSign>
						<LOINCVersion>2.42</LOINCVersion>
						<Value>140</Value>
						<UnitOfMeasure>[lb_av]</UnitOfMeasure>
						<UCUMVersion>2.1</UCUMVersion>
						<MinimumMeasurementValue>100</MinimumMeasurementValue>
						<MaximumMeasurementValue>350</MaximumMeasurementValue>
						<MeasurementNotes>This field is established to test the length ability for two hundred fifty five (255) character long string test in order to test free text and misc random other fields...................... that are allowed to be that long per the schema and syntax check</MeasurementNotes>
						</Measurement>
						<MeasurementTimingAndDuration>
							<MeasurementAdministrationTiming>
								<MeasurementTimingNumericValue>123</MeasurementTimingNumericValue>
								<VariableMeasurementTimingModifier>AND</VariableMeasurementTimingModifier>
								<AdministrationTimingNumericValue>567</AdministrationTimingNumericValue>
								<MeasurementTimingUnits>
									<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>*********</Code>
								</MeasurementTimingUnits>
								<MeasurementTimingModifier>
									<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>*********</Code>
								</MeasurementTimingModifier>
								<MeasurementTimingEvent>
									<Text>This field is edited and the length is setup to now be a full 80 characters long</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>*********</Code>
								</MeasurementTimingEvent>
								<MeasurementTimingClarifyingFreeText>This field is established to test the length ability for two hundred fifty five (255) character long string test in order to test free text and misc random other fields...................... that are allowed to be that long per the schema and syntax check</MeasurementTimingClarifyingFreeText>
							</MeasurementAdministrationTiming>
						</MeasurementTimingAndDuration>
					</TitrationDose>
				</Titration>
				<FlavoringRequested>Y</FlavoringRequested>
				<CompoundInformation>
					<FinalCompoundPharmaceuticalDosageForm>C28254</FinalCompoundPharmaceuticalDosageForm>
					<!-- mL -->
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Diphenhydramine 12.5 mg/5 mL</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>12.5</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- solution -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C91131</Code>
									<!-- mg per 5 mL -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>300</Value>
							<!-- 300 mL as 1 of 3 equal parts to a 900 mL bottle -->
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Viscous lidocaine 2%</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>2</StrengthValue>
								<StrengthForm>
									<Code>C42986</Code>
									<!-- Solution (viscous lidocaine) -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C25613</Code>
									<!-- percentage -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>300</Value>
							<!-- 300 mL as 1 of 3 equal parts to a 900 mL bottle -->
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
					<CompoundIngredientsLotNotUsed>
						<CompoundIngredient>
							<CompoundIngredientItemDescription>Maalox oral suspension</CompoundIngredientItemDescription>
							<Strength>
								<StrengthValue>200/200/20</StrengthValue>
								<StrengthForm>
									<Code>C68992</Code>
									<!-- oral suspension -->
								</StrengthForm>
								<StrengthUnitOfMeasure>
									<Code>C91131</Code>
									<!-- mg / 5 mL -->
								</StrengthUnitOfMeasure>
							</Strength>
						</CompoundIngredient>
						<Quantity>
							<Value>300</Value>
							<!-- 300 mL as 1 of 3 equal parts to a 900 mL bottle -->
							<CodeListQualifier>38</CodeListQualifier>
							<QuantityUnitOfMeasure>
								<Code>C28254</Code>
								<!-- mL -->
							</QuantityUnitOfMeasure>
						</Quantity>
					</CompoundIngredientsLotNotUsed>
				</CompoundInformation>
				<NumberOfPackagesToBeDispensed>1</NumberOfPackagesToBeDispensed>
				<PatientCodifiedNote>
					<Qualifier>AG</Qualifier>
					<Value>10</Value>
				</PatientCodifiedNote>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2099-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>AnticipatedHealthCareFacilityDischargeDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>DateValidated</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>DeliveredOnDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>EffectiveDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2099-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>ExpirationDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>OtherHealthCareFacilityDischargeDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>PeriodEnd</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>SoldDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>StartDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<OtherMedicationDate>
					<OtherMedicationDate>
						<Date>2018-05-08</Date>
					</OtherMedicationDate>
					<OtherMedicationDateQualifier>TransplantDate</OtherMedicationDateQualifier>
				</OtherMedicationDate>
				<NeedNoLaterThan>
					<NeededNoLaterThanDate>2018-12-01T13:42:39.7Z</NeededNoLaterThanDate>
					<NeededNoLaterThanReason>This field is edited and the length is setup to be 70 characters long!</NeededNoLaterThanReason>
				</NeedNoLaterThan>
				<PlaceOfServiceNonSelfAdministeredProduct>01</PlaceOfServiceNonSelfAdministeredProduct>
				<ProviderExplicitAuthorizationToAdminister>Y</ProviderExplicitAuthorizationToAdminister>
			</MedicationPrescribed>
			<Supervisor>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<UPIN>0</UPIN>
					<DEANumber>*********</DEANumber>
					<HIN>0</HIN>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Thomas</LastName>
					<FirstName>Macnair</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4221</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
					<HomeTelephone>
						<Number>**********</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Supervisor>
			<ProhibitRenewalRequest>false</ProhibitRenewalRequest>
			<FollowUpPrescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>784577%1142%14</StateLicenseNumber>
					<MedicaidNumber>654745</MedicaidNumber>
					<UPIN>0</UPIN>
					<DEANumber>*********</DEANumber>
					<HIN>0</HIN>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP.CA.1142%14</CertificateToPrescribe>
				</Identification>
				<Specialty>363L00000X</Specialty>
				<PracticeLocation>
					<BusinessName>MediStar of California</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Thomas</LastName>
					<FirstName>Walden</FirstName>
					<MiddleName>Macnair</MiddleName>
					<Suffix>NP</Suffix>
				</Name>
				<FormerName>
					<LastName>Thomas</LastName>
					<FirstName>Macnair</FirstName>
					<MiddleName>Robert</MiddleName>
				</FormerName>
				<Address>
					<AddressLine1>1425 Mendocino Ave</AddressLine1>
					<AddressLine2>Suite 12-A</AddressLine2>
					<City>Santa Rosa</City>
					<StateProvince>CA</StateProvince>
					<PostalCode>95401</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4221</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
					<HomeTelephone>
						<Number>**********</Number>
						<SupportsSMS>Y</SupportsSMS>
					</HomeTelephone>
				</CommunicationNumbers>
				</NonVeterinarian>
			</FollowUpPrescriber>
		</NewRx>
	</Body>
</Message>
