<?xml version="1.0" encoding="UTF-8"?>
<Message TransportVersion="20170715" DatatypesVersion="20170715" TransactionDomain="SCRIPT" TransactionVersion="20170715" StructuresVersion="20170715" ECLVersion="20170715" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<Header>
		<To Qualifier="P">1122999</To>
		<From Qualifier="D">6301875277001</From>
		<MessageID>0c9c3d8e48e74196b69c69320be2fb37</MessageID>
		<SentTime>2019-01-01T13:42:39.7Z</SentTime>
		<SenderSoftware>
			<SenderSoftwareDeveloper>Surescripts</SenderSoftwareDeveloper>
			<SenderSoftwareProduct>Certification Testing</SenderSoftwareProduct>
			<SenderSoftwareVersionRelease>20170715</SenderSoftwareVersionRelease>
		</SenderSoftware>
		<PrescriberOrderNumber>NEWRX for RxChange-7</PrescriberOrderNumber>
	</Header>
	<Body>
		<NewRx>
			<BenefitsCoordination>
				<PayerIdentification>
					<PayerID>T00000000022652</PayerID>
					<ProcessorIdentificationNumber>555555</ProcessorIdentificationNumber>
					<MutuallyDefined>000021456</MutuallyDefined>
					<IINNumber>444444</IINNumber>
				</PayerIdentification>
				<PayerName>ProInforma PBM</PayerName>
				<CardholderID>DM-K884-09</CardholderID>
				<GroupID>MM88409</GroupID>
				<GroupName>K884-MA-09</GroupName>
				<PBMMemberID>PRO-PBM^MA^65447</PBMMemberID>
			</BenefitsCoordination>
			<Patient>
				<HumanPatient>
					<Name>
						<LastName>Devereaux</LastName>
						<FirstName>Margaret Adelia</FirstName>
						<MiddleName>Beryl</MiddleName>
					</Name>
					<Gender>F</Gender>
					<DateOfBirth>
						<Date>1997-11-01</Date>
					</DateOfBirth>
					<Address>
						<AddressLine1>27-B Heald St</AddressLine1>
						<City>Pepperell</City>
						<StateProvince>MA</StateProvince>
						<PostalCode>01463</PostalCode>
						<CountryCode>US</CountryCode>
					</Address>
					<CommunicationNumbers>
						<PrimaryTelephone>
							<Number>**********</Number>
							<SupportsSMS>Y</SupportsSMS>
						</PrimaryTelephone>
						<ElectronicMail><EMAIL></ElectronicMail>
					</CommunicationNumbers>
				</HumanPatient>
			</Patient>
			<Pharmacy>
				<Identification>
					<NCPDPID>4222154</NCPDPID>
					<StateLicenseNumber>NY-99283%119-RX</StateLicenseNumber>
					<MedicareNumber>665475</MedicareNumber>
					<MedicaidNumber>665475</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
				</Identification>
				<BusinessName>Brooklyn @ Gates Pharmacy</BusinessName>
				<Address>
					<AddressLine1>92 Gates Ave</AddressLine1>
					<City>Brooklyn</City>
					<StateProvince>NY</StateProvince>
					<PostalCode>11238</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<SupportsSMS>N</SupportsSMS>
					</PrimaryTelephone>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
			</Pharmacy>
			<Prescriber>
				<NonVeterinarian>
				<Identification>
					<StateLicenseNumber>MASS 9918 2121W</StateLicenseNumber>
					<MedicareNumber>027547</MedicareNumber>
					<MedicaidNumber>027547</MedicaidNumber>
					<DEANumber>*********</DEANumber>
					<NPI>**********</NPI>
					<CertificateToPrescribe>CTP-MA.2121W</CertificateToPrescribe>
				</Identification>
				<Specialty>207Q00000X</Specialty>
				<PracticeLocation>
					<BusinessName>Northern MA Medical Center</BusinessName>
				</PracticeLocation>
				<Name>
					<LastName>Waterford-Fielding</LastName>
					<FirstName>Dolores</FirstName>
					<MiddleName>Rosemarie</MiddleName>
					<Suffix>FNP</Suffix>
					<Prefix>MS</Prefix>
				</Name>
				<FormerName>
					<LastName>Fielding</LastName>
					<FirstName>Dolores</FirstName>
				</FormerName>
				<Address>
					<AddressLine1>35 Captain Brown's Lane</AddressLine1>
					<City>Acton</City>
					<StateProvince>MA</StateProvince>
					<PostalCode>01720</PostalCode>
					<CountryCode>US</CountryCode>
				</Address>
				<CommunicationNumbers>
					<PrimaryTelephone>
						<Number>**********</Number>
						<Extension>4557</Extension>
					</PrimaryTelephone>
					<ElectronicMail><EMAIL></ElectronicMail>
					<Fax>
						<Number>**********</Number>
					</Fax>
				</CommunicationNumbers>
				</NonVeterinarian>
			</Prescriber>
			<MedicationPrescribed>
				<DrugDescription>Atenolol 50 mg tablet</DrugDescription>
				<DrugCoded>
					<ProductCode>
						<Code>00781150601</Code>
						<Qualifier>ND</Qualifier>
					</ProductCode>
					<Strength>
						<StrengthValue>50</StrengthValue>
						<StrengthForm>
							<Code>C42998</Code>
						</StrengthForm>
						<StrengthUnitOfMeasure>
							<Code>C28253</Code>
						</StrengthUnitOfMeasure>
					</Strength>
					<DrugDBCode>
						<Code>197381</Code>
						<Qualifier>SCD</Qualifier>
					</DrugDBCode>
				</DrugCoded>
				<Quantity>
					<Value>30</Value>
					<CodeListQualifier>38</CodeListQualifier>
					<QuantityUnitOfMeasure>
						<Code>C48542</Code>
					</QuantityUnitOfMeasure>
				</Quantity>
				<WrittenDate>
					<Date>2019-02-13</Date>
				</WrittenDate>
				<Substitutions>0</Substitutions>
				<NumberOfRefills>1</NumberOfRefills>
				<Sig>
					<SigText>Take 1 tablet orally once per day for 30 days</SigText>
					<CodeSystem>
						<SNOMEDVersion>20170131</SNOMEDVersion>
						<FMTVersion>19.12e</FMTVersion>
					</CodeSystem>
					<Instruction>
							<DoseAdministration>
								<DoseDeliveryMethod>
									<Text>Take</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>419652001</Code>
								</DoseDeliveryMethod>
								<Dosage>
									<DoseQuantity>1</DoseQuantity>
									<DoseUnitOfMeasure>
										<Text>tablet</Text>
										<Qualifier>DoseUnitOfMeasure</Qualifier>
										<Code>C48542</Code>
									</DoseUnitOfMeasure>
								</Dosage>
								<RouteOfAdministration>
									<Text>oral route</Text>
									<Qualifier>SNOMED</Qualifier>
									<Code>26643006</Code>
								</RouteOfAdministration>
							</DoseAdministration>
							<TimingAndDuration>
								<Frequency>
									<FrequencyNumericValue>1</FrequencyNumericValue>
									<FrequencyUnits>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</FrequencyUnits>
								</Frequency>
							</TimingAndDuration>
							<TimingAndDuration>
								<Duration>
									<DurationNumericValue>30</DurationNumericValue>
									<DurationText>
										<Text>day</Text>
										<Qualifier>SNOMED</Qualifier>
										<Code>258703001</Code>
									</DurationText>
								</Duration>
							</TimingAndDuration>
					</Instruction>
				</Sig>
				<RxFillIndicator>All Fill Statuses Except Transferred</RxFillIndicator>
			</MedicationPrescribed>
		</NewRx>
	</Body>
</Message>
