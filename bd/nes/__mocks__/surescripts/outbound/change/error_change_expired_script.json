{"_meta": {"source": "ss_message", "subform": {"subform_observation": "ss_observation", "subform_allergy": "ss_allergy", "subform_benefit": "ss_benefit", "subform_due": "ss_due", "subform_compound": "ss_compound", "subform_diagnosis": "ss_diagnosis", "subform_cnote": "ss_codified_note", "subform_chg_med": "ss_chg_med", "subform_chg_response": "ss_chg_resp", "subform_followup": "ss_followup"}}, "subform_observation": [], "subform_allergy": [], "subform_benefit": [], "subform_due": [], "subform_compound": [], "subform_diagnosis": [{"type": "Primary", "dx_code": "K1233", "dx_code_qualifier_id": "ABF", "dx_desc": "Oral mucositis (ulcerative) due to radiation"}], "subform_cnote": [], "subform_chg_med": [{"change_type": "Quantity", "quantity": 100, "qty_qualifier_id": "QS", "quantity_uom_id": "C64933", "DrugDescription": "Drug Description", "daw": "0"}], "subform_chg_response": [], "subform_followup": [], "id": 37, "clinical_info_qualifier": "1", "created_by": null, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": null, "physician_id": null, "pharmacy_order_id": null, "site_id": 107, "error_code_id": null, "error_desc_code_id": null, "rx_group_reason": null, "chg_type_id": "D", "patient_home_state": null, "prescriber_state": null, "fu_prescriber_state": null, "disp_product_code_qualifier_id": null, "disp_dea_schedule_id": null, "disp_quantity_qualifier_id": null, "disp_quantity_uom_id": null, "disp_pa_status_id": null, "disp_drug_coverage_status_id": null, "chg_allowed_chg_id": null, "product_code_qualifier_id": null, "drug_db_qualifier_id": null, "dea_schedule_id": null, "fdb_id": null, "strength_form_id": null, "strength_uom_id": null, "quantity_uom_id": "C64933", "quantity_qualifier_id": "CF", "compound_dosage_form_id": null, "pa_status_id": null, "drug_coverage_status_id": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "", "change_type": null, "change_data": null, "created_on": "04/18/2024 10:06:10", "change_on": null, "digital_signature_indicator": null, "direction": "IN", "status_icons": "{new,canceled}", "processed": "No", "followup_count": null, "last_followup_dt": null, "has_renewal": null, "renewal_warning": null, "rx_canceled": null, "cancel_warning": null, "show_pending_warning": null, "pending_warning": null, "show_last_action": null, "last_action": null, "available_actions": {}, "show_options": "{\"Practice Location\",\"Order Group\"}", "to": "7447853", "from": "6301875277001", "message_status": "Verified", "error_description": null, "sender_software_developer": "Surescripts", "sender_software_product": "Certification Testing", "sender_software_version": "20170715", "order_group_no": "CANCELRX TEST CASE #5a", "rx_group_no": null, "order_group_icnt": 2, "rx_group_icnt": null, "order_group_tcnt": 2, "rx_group_tcnt": null, "message_id": "0c9c3d8e48e74196b69c69320be2fb37", "related_message_id": "0c9c3d8e48e74196b69c69320be2fb37", "response_message_id": null, "physician_order_id": "CANCELRX TEST CASE #5a", "pharmacy_rx_no": null, "sent_dt": "01/01/2019 13:42:39", "message_type": "RxChangeRequest", "chg_reason": null, "allergy_warning": null, "pa_warning": null, "priority_flag": "X", "processed_dt": null, "patient_name_display": "ZACHARY DELAPLAINE", "patient_first_name": "ZACHARY", "patient_last_name": "DELAPLAINE", "patient_middle_name": null, "patient_gender": "M", "patient_dob": "12/01/2010", "patient_ssn": null, "patient_home_street_1": "901 Sauvblanc Blvd", "patient_home_street_2": null, "patient_home_city": "PETALUMA", "patient_home_zip": null, "patient_phone": null, "patient_mrn": null, "patient_medicare": null, "patient_medicaid": null, "prescriber_has_license": "{NPI}", "prescriber_npi": "**********", "prescriber_dea": null, "prescriber_rems": null, "prescriber_state_cs_lic": null, "prescriber_medicare": null, "prescriber_medicaid": null, "prescriber_state_lic": null, "prescriber_certificate_to_prescribe": null, "prescriber_2000waiver_id": null, "prescriber_specialty": null, "prescriber_name_display": "POPULARILIMAXIMALLI, MAXIMALLIANNA", "prescriber_last_name": "POPULARILIMAXIMALLI", "prescriber_first_name": "MAXIMALLIANNA", "prescriber_address_1": "29919-A <PERSON><PERSON><PERSON> Populated Street", "prescriber_address_2": null, "prescriber_city": "HOWEY IN THE HILLS", "prescriber_zip": null, "prescriber_phone": "**********", "prescriber_extension": "44776", "prescriber_fax": "**********", "prescriber_loc_name": null, "prescriber_loc_has_license": null, "prescriber_loc_ncpdp_id": null, "prescriber_loc_dea": null, "prescriber_loc_rems": null, "prescriber_loc_state_cs_lic": null, "prescriber_loc_medicare": null, "prescriber_loc_medicaid": null, "prescriber_loc_state_lic": null, "prescriber_agent_first_name": null, "prescriber_agent_last_name": null, "fu_prescriber_last_name": null, "fu_prescriber_first_name": null, "fu_prescriber_address_1": null, "fu_prescriber_address_2": null, "fu_prescriber_city": null, "fu_prescriber_zip": null, "fu_prescriber_phone": null, "fu_prescriber_extension": null, "fu_prescriber_fax": null, "fu_prescriber_has_license": null, "fu_prescriber_npi": null, "fu_prescriber_dea": null, "fu_prescriber_rems": null, "fu_prescriber_state_cs_lic": null, "fu_prescriber_medicare": null, "fu_prescriber_medicaid": null, "fu_prescriber_state_lic": null, "fu_prescriber_certificate_to_prescribe": null, "fu_prescriber_2000waiver_id": null, "fu_prescriber_specialty": null, "supervisor_first_name": null, "supervisor_last_name": null, "supervisor_has_license": null, "supervisor_npi": null, "supervisor_dea": null, "supervisor_rems": null, "supervisor_state_cs_lic": null, "supervisor_medicare": null, "supervisor_medicaid": null, "supervisor_state_lic": null, "supervisor_certificate_to_prescribe": null, "supervisor_2000waiver_id": null, "supervisor_specialty": null, "nka": null, "disp_description": null, "disp_product_code": null, "disp_quantity": null, "disp_sig": null, "disp_daw": null, "disp_last_disp_date": null, "disp_note": null, "request_refills": null, "refills_remaining": null, "disp_pa_number": null, "renewal_status": null, "renewal_denial_reason_code": null, "renewal_denied_reason": null, "renewal_note": null, "cancel_status": null, "cancel_denied_reason_code": null, "cancel_denied_reason": null, "cancel_note": null, "description": "Magic Mouthwash Diphenhydramine 12.5 mg/5 mL, Viscous lidocaine 2%, Maalox 1 part", "product_code": null, "drug_db_code": null, "strength": null, "quantity": 900, "day_supply": null, "written_date": "10/10/2023", "start_date": null, "expiration_date": "10/10/2023", "effective_date": null, "daw": "1", "daw_code_reason": null, "refills": 5, "pa_number": null, "do_not_fill": null, "note": null, "sig": "Swish and spit 15 mL orally for 1 minute every 12 hours", "delivery_request": null, "delivery_location": null, "flavoring_requested": null, "patient_rems": null, "prescriber_checked_rems": null, "rems_risk_category": null, "rems_authorization_number": null, "prohibit_renewal_request": null, "sys_period": "[\"2024-04-18 10:06:10.335411+00\",)", "warning": "", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": null, "physician_id_auto_name": null, "pharmacy_order_id_auto_name": null, "site_id_auto_name": "Dallas - TX DTX", "error_code_id_auto_name": null, "error_desc_code_id_auto_name": null, "order_group_reason_auto_name": null, "rx_group_reason_auto_name": null, "chg_type_id_auto_name": null, "patient_home_state_auto_name": null, "prescriber_state_auto_name": null, "fu_prescriber_state_auto_name": null, "disp_product_code_qualifier_id_auto_name": null, "disp_dea_schedule_id_auto_name": null, "disp_quantity_qualifier_id_auto_name": null, "disp_quantity_uom_id_auto_name": null, "disp_pa_status_id_auto_name": null, "disp_drug_coverage_status_id_auto_name": null, "chg_allowed_chg_id_auto_name": null, "product_code_qualifier_id_auto_name": null, "drug_db_qualifier_id_auto_name": null, "dea_schedule_id_auto_name": null, "fdb_id_auto_name": null, "strength_form_id_auto_name": null, "strength_uom_id_auto_name": null, "quantity_qualifier_id_auto_name": null, "quantity_uom_id_auto_name": null, "compound_dosage_form_id_auto_name": null, "pa_status_id_auto_name": null, "drug_coverage_status_id_auto_name": null, "external_id": null, "chg_type_sc_id": [], "chg_type_sc_id_auto_name": []}