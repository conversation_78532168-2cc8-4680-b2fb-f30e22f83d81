{"_meta": {"total_reports": 3, "request_timestamp": "2024-12-02T17:50:59.832Z"}, "request_json_data": {"report_type": "277", "start_date": "2024-01-01"}, "response_json_data": {"reports": {"277": [{"report_id": 27, "report_type": "277", "control_number": "1001", "submitter_id": "000000", "customer_id": 25, "customer_name": "e<PERSON>", "payer_name": "extra healthy insurance", "payer_info": [{"organizationName": "extra healthy insurance", "claimStatusTransactions": [{"provider": {"etin": "A222222221", "organizationName": "happy doctors group"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "happy doctors group"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceDate": "12/04/2020", "billTypeIdentifier": "111", "patientAccountNumber": "TEST00004", "informationClaimStatuses": [{"remittanceDate": "12/04/2020", "claimPaymentAmount": "80", "informationStatuses": [{"statusCode": "34", "statusCodeValue": "Subscriber and policyholder name not found.", "healthCareClaimStatusCategoryCode": "A7", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Rejected for Invalid Information - The claim/encounter has invalid information as specified in the Status details and has been rejected."}], "remittanceTraceNumber": "1111111", "totalClaimChargeAmount": "100", "adjudicatedFinalizedDate": "12/04/2020", "statusInformationEffectiveDate": "12/04/2020"}], "referencedTransactionTraceNumber": "**********"}}], "dependent": {"lastName": "doeone", "firstName": "johnone"}}]}]}], "payerContactInformation": {"contactName": "MEDICAL REVIEW DEPARTMENT", "contactMethods": [{"fax": "**********"}, {"phone": "**********"}]}, "centersForMedicareAndMedicaidServicePlanId": "9496"}], "s3_filepath": "claims/medical/epatrick/1001_277.x12", "created_at": "2024-12-02T17:25:52.784Z", "patient_id": null, "patient_first_name": null, "patient_last_name": null, "dependent_first_name": "johnone", "dependent_last_name": "doeone"}, {"report_id": 25, "report_type": "277", "control_number": "0001", "submitter_id": "000000", "customer_id": 25, "customer_name": "e<PERSON>", "payer_name": "UNITED HEALTH CARE", "payer_info": [{"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12345", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12345", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "600", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000001"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JANEONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "08/20/2020", "patientAccountNumber": "12346", "claimServiceBeginDate": "08/20/2020", "clearinghouseTraceNumber": "12346", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "400", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000002"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JOHNONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12347", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12347", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000003"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JANEONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/14/2020", "patientAccountNumber": "12348", "claimServiceBeginDate": "11/14/2020", "clearinghouseTraceNumber": "12348", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "180", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000004"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JOHNONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12349", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12349", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000005"}}], "subscriber": {"lastName": "SMITH", "memberId": "**********", "firstName": "JAY"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12340", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12340", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "450", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000006"}}], "subscriber": {"lastName": "MILLER", "memberId": "**********", "firstName": "MAGGIE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/15/2020", "patientAccountNumber": "12341", "claimServiceBeginDate": "11/15/2020", "clearinghouseTraceNumber": "12341", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000007"}}], "subscriber": {"lastName": "MARTIN", "memberId": "**********", "firstName": "MIKE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12342", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12342", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000008"}}], "subscriber": {"lastName": "SMITH", "memberId": "**********", "firstName": "JOHNATHAN"}}]}]}]}], "s3_filepath": "claims/medical/epatrick/0001_277.x12", "created_at": "2024-12-02T17:25:48.648Z", "patient_id": "**********", "patient_first_name": "JANEONE", "patient_last_name": "DOEONE", "dependent_first_name": null, "dependent_last_name": null}, {"report_id": 11, "report_type": "277", "control_number": "000000001", "submitter_id": "000000", "customer_id": 25, "customer_name": "e<PERSON>", "payer_name": "EXTRA HEALTHY INSURANCE", "payer_info": [{"organizationName": "EXTRA HEALTHY INSURANCE", "payerIdentification": "9496", "claimStatusTransactions": [{"provider": {"etin": "**********", "organizationName": "TestProvider"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "happy doctors group"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"informationClaimStatuses": [{"informationStatuses": [{"statusCode": "689", "statusCodeValue": "Entity was unable to respond within the expected time frame. Usage: This code requires use of an Entity Code.", "entityIdentifierCode": "ZZ", "entityIdentifierCodeValue": "Mutually Defined", "healthCareClaimStatusCategoryCode": "E1", "healthCareClaimStatusCategoryCodeValue": "Response not possible - System Status"}], "statusInformationEffectiveDate": "01/07/2020"}], "referencedTransactionTraceNumber": "ABCD"}}], "dependent": {"lastName": "doeone", "firstName": "jane<PERSON>"}, "subscriber": {"lastName": "doeone", "memberId": "*********", "firstName": "johnone"}}]}]}]}], "s3_filepath": "claims/medical/epatrick/000000001_277.x12", "created_at": "2024-12-02T17:19:15.844Z", "patient_id": "*********", "patient_first_name": "johnone", "patient_last_name": "doeone", "dependent_first_name": "jane<PERSON>", "dependent_last_name": "doeone"}]}}}