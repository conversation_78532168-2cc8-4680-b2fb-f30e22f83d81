{"transactions": [{"controlNumber": "aliquip sed reprehenderit cillum", "referenceIdentification": "culpa irure eu qui exercitation", "transactionSetCreationDate": "Ut consectetur eu deserunt culpa", "transactionSetCreationTime": "sint labore occaecat", "payers": [{"organizationName": "non", "payerIdentification": "laboris aliqua veniam", "centersForMedicareAndMedicaidServicePlanId": "enim", "payerContactInformation": {"contactName": "dolor veniam cillum exercitation", "contactMethods": [{"electronicDataInterChangeAccessNumber": "irure elit magna pariatur", "email": "<PERSON><PERSON><PERSON><PERSON>", "fax": "irure", "phone": "Ut occaecat amet dolor", "phoneExtension": "proident ut tempor"}]}, "claimStatusTransactions": [{"provider": {"organizationName": "id cillum voluptate", "lastName": "eu enim deserunt", "firstName": "aute dolore ut exercitation", "middleName": "minim dolor velit", "etin": "consequat id ea in"}, "claimTransactionBatchNumber": "culpa ea sed esse irure", "providerClaimStatuses": [{"statusInformationEffectiveDate": "dolor", "providerStatuses": [{"healthCareClaimStatusCategoryCode": "dolor laborum cupidatat ad", "healthCareClaimStatusCategoryCodeValue": "elit deserunt amet", "statusCode": "adipisicing cupidatat", "statusCodeValue": "et dolore proident", "entityIdentifierCode": "occaecat irure adipisicing", "entityIdentifierCodeValue": "commodo tempor"}]}, {"statusInformationEffectiveDate": "exercitation velit", "providerStatuses": [{"healthCareClaimStatusCategoryCode": "velit aliquip consequat Ut laboris", "healthCareClaimStatusCategoryCodeValue": "elit sint pariatur Duis", "statusCode": "proident ut est culpa", "statusCodeValue": "ullamco irure adipisicing", "entityIdentifierCode": "<PERSON><PERSON> dolor", "entityIdentifierCodeValue": "ullamco cupidatat eiusmod"}]}], "claimStatusDetails": [{"serviceProvider": {"organizationName": "fugiat cillum", "lastName": "in nisi occaecat", "firstName": "in Lorem ea", "middleName": "ea officia minim aliqua sit", "suffix": "ut", "tin": "quis", "spn": "nulla", "npi": "pariatur qui ad ipsum incididunt"}, "providerOFServiceInformationTraceIdentifier": "laborum deserunt officia fugiat", "serviceProviderClaimStatuses": [{"statusInformationEffectiveDate": "Ut eu reprehenderit", "serviceProviderStatuses": [{"healthCareClaimStatusCategoryCode": "laborum proident", "healthCareClaimStatusCategoryCodeValue": "consectetur cillum fugiat", "statusCode": "officia deserunt", "statusCodeValue": "labore nostrud sunt in consequat", "entityIdentifierCode": "cillum", "entityIdentifierCodeValue": "quis ipsum exercitation reprehenderit"}]}], "patientClaimStatusDetails": [{"subscriber": {"organizationName": "mollit magna exercitation", "lastName": "eu Excepteur", "firstName": "exercitation dolor laborum nisi magna", "middleName": "sed", "suffix": "Ut dolore", "memberId": "in ipsum sit", "employerIdentificationNumber": "cupidatat", "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": "amet"}, "dependent": {"lastName": "officia ea dolor veniam", "firstName": "officia non", "middleName": "occaecat proident", "suffix": "officia qui in fugiat"}, "claims": [{"claimStatus": {"referencedTransactionTraceNumber": "deserunt et veniam ut", "informationClaimStatuses": [{"statusInformationEffectiveDate": "anim velit reprehenderit elit", "totalClaimChargeAmount": "ex sit veniam cupidatat laboris", "claimPaymentAmount": "velit consectetur magna", "adjudicatedFinalizedDate": "dolor minim ea nisi", "remittanceDate": "pariatur officia reprehenderit dolor occaecat", "remittanceTraceNumber": "eu aute amet adipisicing culpa", "informationStatuses": [{"healthCareClaimStatusCategoryCode": "exercitation deserunt nulla", "healthCareClaimStatusCategoryCodeValue": "nulla aliquip minim", "statusCode": "voluptate ad consequat", "statusCodeValue": "irure est", "entityIdentifierCode": "ut", "entityIdentifierCodeValue": "occaecat dolore", "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": "elit"}, {"healthCareClaimStatusCategoryCode": "ipsum", "healthCareClaimStatusCategoryCodeValue": "ut", "statusCode": "culpa", "statusCodeValue": "aliqua voluptate", "entityIdentifierCode": "<PERSON><PERSON>", "entityIdentifierCodeValue": "enim tempor in pariatur", "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": "culpa adipisicing sit exercitation"}, {"healthCareClaimStatusCategoryCode": "proident quis", "healthCareClaimStatusCategoryCodeValue": "nulla velit ullamco", "statusCode": "dolor magna consequat", "statusCodeValue": "ut dolor ut", "entityIdentifierCode": "consectetur in nisi est dolore", "entityIdentifierCodeValue": "consequat incididunt magna", "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": "id"}, {"healthCareClaimStatusCategoryCode": "sit in velit cupidatat aliquip", "healthCareClaimStatusCategoryCodeValue": "culpa fugiat mollit", "statusCode": "in", "statusCodeValue": "Excepteur culpa ea Ut consequat", "entityIdentifierCode": "amet velit aliqua consequat", "entityIdentifierCodeValue": "veniam sunt", "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": "magna eu"}]}], "tradingPartnerClaimNumber": "magna pariatur", "billTypeIdentifier": "non ut ipsum", "patientAccountNumber": "Duis quis in non et", "pharmacyPrescriptionNumber": "nulla aliqua et", "voucherIdentifier": "voluptate irure in", "clearinghouseTraceNumber": "cillum", "claimServiceBeginDate": "dolore <PERSON>", "claimServiceEndDate": "eu dolor velit adipisicing", "claimServiceDate": "id irure ullamco sint est"}, "serviceLines": [{"service": {"serviceIdQualifierCode": "commodo", "serviceIdQualifierCodeValue": "ea", "procedureCode": "eu in", "procedureModifiers": ["adipisicing consequat", "irure exercitation ipsum veniam labore"], "chargeAmount": "qui adipisicing ad anim", "amountPaid": "ut cillum aliquip ipsum ad", "revenueCode": "dolore laboris aute ut proident", "submittedUnits": "occaecat incididunt"}, "serviceClaimStatuses": [{"effectiveDate": "enim velit cillum minim Excepteur", "serviceStatuses": [{"healthCareClaimStatusCategoryCode": "ex ea veniam dolor", "healthCareClaimStatusCategoryCodeValue": "tempor culpa", "statusCode": "ut", "statusCodeValue": "id cupidatat sint tempor", "entityIdentifierCode": "veniam minim nostrud sunt", "entityIdentifierCodeValue": "<PERSON>rem sit in esse velit", "nationalCouncilForPrescriptionDrugProgramsRejectPaymentCodes": "aliquip tempor"}]}], "lineItemControlNumber": "esse ad", "serviceLineDate": "occaecat labore elit Lorem", "beginServiceLineDate": "dolore proident", "endServiceLineDate": "non mollit cillum"}]}]}]}]}]}]}], "meta": {"applicationMode": "officia aliqua ex", "senderId": "esse labore minim", "traceId": "eu amet eiusmod deserunt ipsum"}}