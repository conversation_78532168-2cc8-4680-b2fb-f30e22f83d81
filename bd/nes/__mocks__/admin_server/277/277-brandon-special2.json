{"type": "277", "raw_x12": "ISA*00*          *00*          *ZZ*MCKESSON_TSH   *ZZ*208459         *200107*2319*^*00501***********1*P*:~GS*HN*CHC*123456*20201117*0427*832838996*X*005010X212~ST*277*0001*005010X212~BHT*0010*08***********20201117*042749*DG~HL*1**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*2*1*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*3*2*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*4*3*22*0~NM1*IL*1*DOEONE*JANEONE****MI*0*********~TRN*2*0000001~STC*A1:704*20201116*WQ*600********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12345~REF*D9*12345~DTP*472*RD8*20201113-20201113~HL*5**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*6*5*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*7*6*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*8*7*22*0~NM1*IL*1*DOEONE*JOHNONE****MI***********~TRN*2*0000002~STC*A1:704*20201116*WQ*400********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12346~REF*D9*12346~DTP*472*RD8*20200820-20200820~HL*9**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*10*9*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*11*10*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*12*11*22*0~NM1*IL*1*DOEONE*JANEONE****MI*0*********~TRN*2*0000003~STC*A1:704*20201116*WQ*100********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12347~REF*D9*12347~DTP*472*RD8*20201113-20201113~HL*13**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*14*13*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*15*14*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*16*15*22*0~NM1*IL*1*DOEONE*JOHNONE****MI***********~TRN*2*0000004~STC*A1:704*20201116*WQ*180********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12348~REF*D9*12348~DTP*472*RD8*20201114-20201114~HL*17**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*18*17*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*19*18*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*20*19*22*0~NM1*IL*1*SMITH*JAY****MI***********~TRN*2*0000005~STC*A1:704*20201116*WQ*100********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12349~REF*D9*12349~DTP*472*RD8*20201113-20201113~HL*21**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*22*21*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*23*22*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*24*23*22*0~NM1*IL*1*MILLER*MAGGIE****MI***********~TRN*2*0000006~STC*A1:704*20201116*WQ*450********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12340~REF*D9*12340~DTP*472*RD8*20201113-20201113~HL*25**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*26*25*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*27*26*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*28*27*22*0~NM1*IL*1*MARTIN*MIKE****MI***********~TRN*2*0000007~STC*A1:704*20201116*WQ*100********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12341~REF*D9*12341~DTP*472*RD8*20201115-20201115~HL*29**20*1~NM1*PR*2*UNITED HEALTH CARE*****PI**********~HL*30*29*21*1~NM1*41*2*CHANGE HEALTHCARE*****46**********~HL*31*30*19*1~NM1*1P*2*HAPPY DOCTORS GROUP*****XX***********~HL*32*31*22*0~NM1*IL*1*SMITH*JOHNATHAN****MI***********~TRN*2*0000008~STC*A1:704*20201116*WQ*100********PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM~REF*EJ*12342~REF*D9*12342~DTP*472*RD8*20201113-20201113~SE*107*0001~GE*1*832838996~IEA*1*842928883~", "raw_json": {"meta": {"traceId": "72bfd39e-0fa5-fbec-1ee6-89e4d78f2600", "senderId": "APIM_Marketplace_Native_N3000006", "applicationMode": "sandbox"}, "transactions": [{"payers": [{"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12345", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12345", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "600", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000001"}}], "subscriber": {"lastName": "DOEONE", "memberId": "0*********", "firstName": "JANEONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "08/20/2020", "patientAccountNumber": "12346", "claimServiceBeginDate": "08/20/2020", "clearinghouseTraceNumber": "12346", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "400", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000002"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JOHNONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12347", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12347", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000003"}}], "subscriber": {"lastName": "DOEONE", "memberId": "0*********", "firstName": "JANEONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/14/2020", "patientAccountNumber": "12348", "claimServiceBeginDate": "11/14/2020", "clearinghouseTraceNumber": "12348", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "180", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000004"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JOHNONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12349", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12349", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000005"}}], "subscriber": {"lastName": "SMITH", "memberId": "**********", "firstName": "JAY"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12340", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12340", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "450", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000006"}}], "subscriber": {"lastName": "MILLER", "memberId": "**********", "firstName": "MAGGIE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/15/2020", "patientAccountNumber": "12341", "claimServiceBeginDate": "11/15/2020", "clearinghouseTraceNumber": "12341", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000007"}}], "subscriber": {"lastName": "MARTIN", "memberId": "**********", "firstName": "MIKE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12342", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12342", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000008"}}], "subscriber": {"lastName": "SMITH", "memberId": "**********", "firstName": "JOHNATHAN"}}]}]}]}], "controlNumber": "0001", "referenceIdentification": "*********", "transactionSetCreationDate": "11/17/2020", "transactionSetCreationTime": "04:27:49 am"}]}, "payer_info": [{"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12345", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12345", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "600", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000001"}}], "subscriber": {"lastName": "DOEONE", "memberId": "0*********", "firstName": "JANEONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "08/20/2020", "patientAccountNumber": "12346", "claimServiceBeginDate": "08/20/2020", "clearinghouseTraceNumber": "12346", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "400", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000002"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JOHNONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12347", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12347", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000003"}}], "subscriber": {"lastName": "DOEONE", "memberId": "0*********", "firstName": "JANEONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/14/2020", "patientAccountNumber": "12348", "claimServiceBeginDate": "11/14/2020", "clearinghouseTraceNumber": "12348", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "180", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000004"}}], "subscriber": {"lastName": "DOEONE", "memberId": "**********", "firstName": "JOHNONE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12349", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12349", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000005"}}], "subscriber": {"lastName": "SMITH", "memberId": "**********", "firstName": "JAY"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12340", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12340", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "450", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000006"}}], "subscriber": {"lastName": "MILLER", "memberId": "**********", "firstName": "MAGGIE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/15/2020", "patientAccountNumber": "12341", "claimServiceBeginDate": "11/15/2020", "clearinghouseTraceNumber": "12341", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000007"}}], "subscriber": {"lastName": "MARTIN", "memberId": "**********", "firstName": "MIKE"}}]}]}]}, {"organizationName": "UNITED HEALTH CARE", "payerIdentification": "*********", "claimStatusTransactions": [{"provider": {"etin": "*********", "organizationName": "CHANGE HEALTHCARE"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "HAPPY DOCTORS GROUP"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceEndDate": "11/13/2020", "patientAccountNumber": "12342", "claimServiceBeginDate": "11/13/2020", "clearinghouseTraceNumber": "12342", "informationClaimStatuses": [{"message": "PAYER ACKNOWLEDGED RECEIPT OF THE CLAIM", "informationStatuses": [{"statusCode": "704", "statusCodeValue": "Claim Note Text", "healthCareClaimStatusCategoryCode": "A1", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication."}], "totalClaimChargeAmount": "100", "statusInformationEffectiveDate": "11/16/2020"}], "referencedTransactionTraceNumber": "0000008"}}], "subscriber": {"lastName": "SMITH", "memberId": "**********", "firstName": "JOHNATHAN"}}]}]}]}], "payer_name": "UNITED HEALTH CARE", "customer_id": 25, "report_type": "277", "s3_filehash": "Y2xhaW1zL21lZGljYWwvZXBhdHJpY2svMDAwMV8yNzcueDEy", "submitter_id": "000000", "customer_name": "e<PERSON>", "control_number": "0001", "raw_report_url": "https://sandbox-apigw.optum.com/medicalnetwork/reports/v2//X3000000.AC", "customer_env_name": "<PERSON><PERSON>", "converted_report_url": "https://sandbox-apigw.optum.com/medicalnetwork/reports/v2/X3000000.AC/277"}