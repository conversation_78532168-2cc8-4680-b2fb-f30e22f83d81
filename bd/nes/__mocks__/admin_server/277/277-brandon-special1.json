{"type": "277", "raw_x12": "ISA*00*          *00*          *ZZ*MCKESSON_TSH   *ZZ*208459         *200107*2319*^*00501*000000001*1*P*:~GS*HN*MTEXE*LCX1210000*20201130*1654*858072350*X*005010X212~ST*277*1001*005010X212~BHT*0085*48*277X2*********20060824*1211*RQ~HL*1**20*1~NM1*PR*2*extra healthy insurance*****XV*9496~PER*IC*MEDICAL REVIEW DEPARTMENT*FX************TE***********~HL*2*1*21*1~NM1*41*2*happy doctors group*****46*A222222221~HL*3*2*19*1~NM1*1P*2*happy doctors group*****XX***********~HL*4*3*22*0~NM1*QC*1*doeone*johnone****MI***********~TRN*2***********~STC*A7:34*20201204**100*80*20201204**20201204*1111111~REF*BLT*111~REF*EJ*TEST00004~REF*EA*STHHL12345~DTP*472*D8*20201204~SE*17*1001~GE*1*858072350~IEA*1*858072350~", "raw_json": {"meta": {"traceId": "f3751323-c98e-bb8d-ef0d-3489bf544708", "senderId": "APIM_Marketplace_Native_N3000006", "applicationMode": "sandbox"}, "transactions": [{"payers": [{"organizationName": "extra healthy insurance", "claimStatusTransactions": [{"provider": {"etin": "A222222221", "organizationName": "happy doctors group"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "happy doctors group"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceDate": "12/04/2020", "billTypeIdentifier": "111", "patientAccountNumber": "TEST00004", "informationClaimStatuses": [{"remittanceDate": "12/04/2020", "claimPaymentAmount": "80", "informationStatuses": [{"statusCode": "34", "statusCodeValue": "Subscriber and policyholder name not found.", "healthCareClaimStatusCategoryCode": "A7", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Rejected for Invalid Information - The claim/encounter has invalid information as specified in the Status details and has been rejected."}], "remittanceTraceNumber": "1111111", "totalClaimChargeAmount": "100", "adjudicatedFinalizedDate": "12/04/2020", "statusInformationEffectiveDate": "12/04/2020"}], "referencedTransactionTraceNumber": "**********"}}], "dependent": {"lastName": "doeone", "firstName": "johnone"}}]}]}], "payerContactInformation": {"contactName": "MEDICAL REVIEW DEPARTMENT", "contactMethods": [{"fax": "**********"}, {"phone": "**********"}]}, "centersForMedicareAndMedicaidServicePlanId": "9496"}], "controlNumber": "1001", "referenceIdentification": "277X2********", "transactionSetCreationDate": "08/24/2006", "transactionSetCreationTime": "01/01/1211"}]}, "payer_info": [{"organizationName": "extra healthy insurance", "claimStatusTransactions": [{"provider": {"etin": "A222222221", "organizationName": "happy doctors group"}, "claimStatusDetails": [{"serviceProvider": {"npi": "**********", "organizationName": "happy doctors group"}, "patientClaimStatusDetails": [{"claims": [{"claimStatus": {"claimServiceDate": "12/04/2020", "billTypeIdentifier": "111", "patientAccountNumber": "TEST00004", "informationClaimStatuses": [{"remittanceDate": "12/04/2020", "claimPaymentAmount": "80", "informationStatuses": [{"statusCode": "34", "statusCodeValue": "Subscriber and policyholder name not found.", "healthCareClaimStatusCategoryCode": "A7", "healthCareClaimStatusCategoryCodeValue": "Acknowledgement/Rejected for Invalid Information - The claim/encounter has invalid information as specified in the Status details and has been rejected."}], "remittanceTraceNumber": "1111111", "totalClaimChargeAmount": "100", "adjudicatedFinalizedDate": "12/04/2020", "statusInformationEffectiveDate": "12/04/2020"}], "referencedTransactionTraceNumber": "**********"}}], "dependent": {"lastName": "doeone", "firstName": "johnone"}}]}]}], "payerContactInformation": {"contactName": "MEDICAL REVIEW DEPARTMENT", "contactMethods": [{"fax": "**********"}, {"phone": "**********"}]}, "centersForMedicareAndMedicaidServicePlanId": "9496"}], "payer_name": "extra healthy insurance", "customer_id": 25, "report_type": "277", "s3_filehash": "Y2xhaW1zL21lZGljYWwvZXBhdHJpY2svMTAwMV8yNzcueDEy", "submitter_id": "000000", "customer_name": "e<PERSON>", "control_number": "1001", "raw_report_url": "https://sandbox-apigw.optum.com/medicalnetwork/reports/v2//X3000000.ZZ", "customer_env_name": "<PERSON><PERSON>", "converted_report_url": "https://sandbox-apigw.optum.com/medicalnetwork/reports/v2/X3000000.ZZ/277"}