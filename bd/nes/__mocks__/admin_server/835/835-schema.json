{"_meta": {"total_reports": 3, "request_timestamp": "2024-12-02T17:51:19.316Z"}, "request_json_data": {"report_type": "835", "start_date": "2024-01-01"}, "response_json_data": {"reports": {"835": [{"report_id": 31, "report_type": "835", "control_number": "35681", "submitter_id": "000000", "customer_id": 25, "customer_name": "e<PERSON>", "payer_name": "DENTAL OF ABC", "payer_info": {"name": "DENTAL OF ABC", "address": {"city": "CENTERVILLE", "state": "PA", "address1": "225 MAIN STREET", "postalCode": "17111"}, "technicalContactInformation": [{"contactName": "JANE DOE", "contactMethods": [{"phone": "**********"}]}]}, "s3_filepath": "claims/medical/epatrick/35681_835.x12", "created_at": "2024-12-02T17:26:06.436Z", "patient_id": null, "patient_first_name": null, "patient_last_name": null, "dependent_first_name": null, "dependent_last_name": null}, {"report_id": 30, "report_type": "835", "control_number": "112233", "submitter_id": "000000", "customer_id": 25, "customer_name": "e<PERSON>", "payer_name": "TWO OAKS INSURANCE", "payer_info": {"name": "TWO OAKS INSURANCE", "address": {"city": "NASHVILLE", "state": "TN", "address1": "1900 BELMONT BLVD", "postalCode": "37212"}, "technicalContactInformation": [{"contactName": "TAYLOR SMITH", "contactMethods": [{"phone": "**********", "phoneExtension": "123"}]}]}, "s3_filepath": "claims/medical/epatrick/112233_835.x12", "created_at": "2024-12-02T17:26:02.468Z", "patient_id": null, "patient_first_name": null, "patient_last_name": null, "dependent_first_name": null, "dependent_last_name": null}, {"report_id": 23, "report_type": "835", "control_number": "000000001", "submitter_id": "000000", "customer_id": 25, "customer_name": "e<PERSON>", "payer_name": "extra healthy insurance", "payer_info": {"name": "extra healthy insurance", "address": {"city": "city1", "state": "wa", "address1": "123 address1", "postalCode": "981010000"}, "payerIdentificationNumber": "06102", "businessContactInformation": {"contactName": "extra healthy insurance", "contactMethods": [{"phone": "**********"}]}, "technicalContactInformation": [{"contactName": "extra healthy insurance", "contactMethods": [{"phone": "**********"}, {"url": "www.extrahealthyinsurance.com"}]}]}, "s3_filepath": "claims/medical/epatrick/000000001_835.x12", "created_at": "2024-12-02T17:25:42.205Z", "patient_id": null, "patient_first_name": null, "patient_last_name": null, "dependent_first_name": null, "dependent_last_name": null}]}}}