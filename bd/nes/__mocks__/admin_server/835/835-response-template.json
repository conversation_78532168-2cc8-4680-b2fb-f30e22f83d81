{"transactions": [{"controlNumber": "elit anim non", "paymentAndRemitReassociationDetails": {"traceTypeCode": "enim", "checkOrEFTTraceNumber": "exercitation nostrud consectetur esse adipisicing", "originatingCompanyIdentifier": "cillum quis ex Lorem nulla", "originatingCompanySupplementalCode": "esse nostrud proident"}, "foreignCurrency": "cillum dolore ex officia", "productionDate": "et fugiat quis", "receiverIdentifier": "exercitation", "versionIdentification": "ipsum occaecat", "financialInformation": {"transactionHandlingCode": "sit", "totalActualProviderPaymentAmount": "aliquip consequat ea", "creditOrDebitFlagCode": "et in in pariatur sint", "paymentMethodCode": "nisi occaecat fugiat", "paymentFormatCode": "magna sit Lorem amet officia", "payerIdentifier": "sint ex officia", "originatingCompanySupplementalCode": "minim elit irure sunt", "checkIssueOrEFTEffectiveDate": "Duis laboris nostrud reprehenderit consequat", "senderAccountDetails": {"senderDfiIdNumberQualifier": "et esse Duis id", "senderDFIIdentifier": "in Ut", "senderAccountNumberQualifier": "id aliqua mollit ut", "senderAccountNumber": "consequat sunt"}, "receiverAccountDetails": {"receiverDfiIdNumberQualifier": "anim non est", "receiverDfiIdentificationNumber": "consectetur ex eu dolore nisi", "receiverAccountNumberQualifier": "pariatur sunt id", "receiverAccountNumber": "do velit exercitation"}}, "payer": {"name": "pariatur dolore dolore Ut", "centersForMedicareAndMedicaidServicesPlanId": "ut consectetur deserunt amet commodo", "payerIdentificationNumber": "aliqua ut commodo exercitation elit", "submitterIdentificationNumber": "quis", "healthIndustryNumber": "ut consequat qui tempor incididunt", "nationalAssociationOfInsuranceCommissioners": "ad irure officia", "payerWebSiteUrl": "occaecat dolore nisi", "address": {"address1": "consequat voluptate", "address2": "commodo mollit sed in in", "city": "Excepteur", "state": "in", "postalCode": "tempor laboris <PERSON><PERSON> dolore", "countryCode": "qui", "countrySubCode": "aliquip aliqua"}, "businessContactInformation": {"contactName": "dolore", "contactMethods": [{"email": "ipsum", "fax": "velit labore anim", "phone": "qui ex consequat ipsum", "phoneExtension": "sunt aliqua cupidatat ut laborum"}, {"email": "occaecat irure ullamco aliquip incididunt", "fax": "culpa irure sint ut", "phone": "qui Ut consequat", "phoneExtension": "dolore"}, {"email": "dolor consequat consectetur nulla voluptate", "fax": "reprehend<PERSON><PERSON>", "phone": "ad ut mollit", "phoneExtension": "fugiat nulla Ut"}, {"email": "deserunt Excepteur sed ut", "fax": "nostrud laborum sint Duis", "phone": "qui velit ipsum aute fugiat", "phoneExtension": "ex cupidatat"}]}, "technicalContactInformation": [{"contactName": "quis", "contactMethods": [{"url": "amet aute do Excepteur sit", "email": "in amet minim deserunt", "fax": "exercitation", "phone": "dolor", "phoneExtension": "amet cillum Duis dolor"}, {"url": "in consequat mollit", "email": "exercitation in id pariatur", "fax": "magna aliquip dolor in", "phone": "sed occaecat commodo", "phoneExtension": "veniam aute nulla occaecat mollit"}, {"url": "non consequat", "email": "sed Excepteur ut", "fax": "non deserunt aute", "phone": "dolore magna in velit", "phoneExtension": "sint"}, {"url": "qui cillum ad", "email": "Lorem adipisicing occaecat elit", "fax": "ea sint fugiat in non", "phone": "cillum non in", "phoneExtension": "minim aute"}]}]}, "payee": {"name": "ullamco", "taxId": "qui", "centersForMedicareAndMedicaidServicesPlanId": "cupidatat exercitation est et officia", "npi": "dolore commodo dolor", "address": {"address1": "nisi elit dolore", "address2": "quis", "city": "non cillum", "state": "aliqua", "postalCode": "est irure ea incididunt culpa", "countryCode": "nisi sint ut sunt et", "countrySubCode": "officia"}, "stateLicenseNumber": "in ipsum anim in in", "nationalCouncilForPrescriptionDrugProgramsPharmacyNumber": "pariatur officia aute eu", "payeeIdentification": "mollit Excepteur nisi voluptate", "federalTaxPayersIdentificationNumber": "labore ipsum", "remittanceDeliveryMethod": {"name": "ad do", "email": "ut", "ftp": "aute culpa do <PERSON>eur", "onLine": "qui commodo fugiat"}}, "detailInfo": [{"assignedNumber": "aliqua", "providerSummaryInformation": {"providerIdentifier": "incididunt", "facilityTypeCode": "eiusmod non", "fiscalPeriodDate": "mollit deserunt amet", "totalClaimCount": "elit aute non", "totalClaimChargeAmount": "velit in incididunt nisi sed", "totalMSPPayerAmount": "exercitation adipisicing cupidatat", "totalNonLabChargeAmount": "est esse eu", "totalHCPCSReportedChargeAmount": "ex ullamco esse eiusmod dolore", "totalHCPCSPayableAmount": "esse est aliquip ad", "totalProfessionalComponentAmount": "dolor anim officia", "totalMSPPatientLiabilityMetAmount": "nostrud incididunt in do", "totalPatientReimbursementAmount": "aliquip ipsum voluptate in", "totalPIPClaimCount": "incididunt dolore culpa tempor", "totalPIPAdjustmentAmount": "sunt est sed"}, "providerSupplementalSummaryInformation": {"totalDRGAmount": "laboris occaecat et", "totalFederalSpecificAmount": "proident e<PERSON><PERSON><PERSON>", "totalHospitalSpecificAmount": "voluptate consequat esse", "totalDisproportionateShareAmount": "nostrud nisi irure eiusmod non", "totalCapitalAmount": "aliquip culpa quis", "totalIndirectMedicalEducationAmount": "Duis adipisicing", "totalOutlierDayCount": "sunt", "totalDayOutlierAmount": "labore id", "totalCostOutlierAmount": "sint dolor et pariatur", "averageDRGLengthOfStay": "ea mollit", "totalDischargeCount": "culpa voluptate officia adipisicing", "totalCostReportDayCount": "in occaecat in", "totalCoveredDayCount": "in Ut mollit Duis", "totalNonCoveredDayCount": "magna id", "totalMSPPassThroughAmount": "dolore nisi reprehenderit ad", "averageDRGWeight": "Ut", "totalPPSCapitalFSPDRGAmount": "cillum Ut ipsum", "totalPPSCapitalHSPDRGAmount": "officia", "totalPPSDSHDRGAmount": "ea aute sunt"}, "paymentInfo": [{"claimStatementPeriodStart": "labore", "claimStatementPeriodEnd": "consectetur mollit ipsum sed labore", "coverageExpirationDate": "laboris tempor deserunt ut", "claimReceivedDate": "Excepteur nulla esse", "claimPaymentInfo": {"patientControlNumber": "<PERSON>is sunt elit", "claimStatusCode": "aliquip", "totalClaimChargeAmount": "culpa amet anim ullamco", "claimPaymentAmount": "mollit fugiat magna", "patientResponsibilityAmount": "ad culpa", "claimFilingIndicatorCode": "consequat", "payerClaimControlNumber": "pariatur ut do elit esse", "facilityTypeCode": "sunt", "claimFrequencyCode": "sit est", "diagnosisRelatedGroupDRGCode": "voluptate sed aute", "diagnosisRelatedGroupDRGWeight": "id reprehenderit", "dischargeFraction": "tempor et sit commodo deserunt"}, "claimAdjustments": [{"claimAdjustmentGroupCode": "officia ea cupidatat mollit ullamco", "claimAdjustmentGroupCodeValue": "dolore cillum ad sit et", "adjustmentReasonCode1": "velit ea proident esse quis", "adjustmentAmount1": "in", "adjustmentQuantity1": "Ut minim culpa qui laboris", "adjustmentReasonCode2": "Excepteur", "adjustmentAmount2": "ea irure aliquip dolor", "adjustmentQuantity2": "in velit exercitation <PERSON><PERSON>", "adjustmentReasonCode3": "dolore ea voluptate nisi esse", "adjustmentAmount3": "incididunt aliquip laboris", "adjustmentQuantity3": "cupidatat esse ea exercitation", "adjustmentReasonCode4": "ad", "adjustmentAmount4": "officia velit aliqua laboris dolore", "adjustmentQuantity4": "irure", "adjustmentReasonCode5": "Excepteur officia", "adjustmentAmount5": "incididunt dolore ad ipsum elit", "adjustmentQuantity5": "consectetur adipisicing ut", "adjustmentReasonCode6": "est eu", "adjustmentAmount6": "et", "adjustmentQuantity6": "exercitation incididunt ipsum officia mollit"}, {"claimAdjustmentGroupCode": "id occaecat", "claimAdjustmentGroupCodeValue": "magna et aliquip", "adjustmentReasonCode1": "labore", "adjustmentAmount1": "nulla fugiat", "adjustmentQuantity1": "elit aliqua ea Duis sunt", "adjustmentReasonCode2": "dolor", "adjustmentAmount2": "<PERSON>rem cup<PERSON>", "adjustmentQuantity2": "nulla incididunt ea", "adjustmentReasonCode3": "consequat cupidatat enim", "adjustmentAmount3": "reprehenderit tempor", "adjustmentQuantity3": "nisi cillum aute qui enim", "adjustmentReasonCode4": "enim dolor nulla", "adjustmentAmount4": "Ut consequat", "adjustmentQuantity4": "Lorem tempor", "adjustmentReasonCode5": "id", "adjustmentAmount5": "elit incididunt ut", "adjustmentQuantity5": "ad in velit", "adjustmentReasonCode6": "sed dolore sint est ipsum", "adjustmentAmount6": "deserunt ad", "adjustmentQuantity6": "Duis dolor tempor"}], "patientName": {"lastName": "non officia culpa tempor", "firstName": "magna", "middleName": "ipsum", "suffix": "id enim ut amet", "ssn": "<PERSON>rem ullam<PERSON>is voluptate", "healthInsuranceClaimNumber": "officia incididunt velit", "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": "in sed", "memberId": "voluptate officia tempor velit minim", "medicaidRecipientIdentificationNumber": "laborum esse Ut"}, "subscriber": {"organizationName": "sed et", "lastName": "magna ut", "firstName": "in fugiat mollit", "middleName": "ex deserunt incididunt", "suffix": "proident aute id anim exercitation", "taxId": "ut aute", "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": "aliquip nostrud dolore", "memberId": "tempor aute consectetur cupidatat ex"}, "correctedPatientOrInsuredName": {"organizationName": "nisi ad veniam", "lastName": "no<PERSON><PERSON> sunt", "firstName": "laborum dolor Excepteur tempor", "middleName": "esse ad anim", "suffix": "sunt labore laborum in", "insuredsChangedUniqueIdentificationNumber": "aliquip commodo est"}, "renderingProvider": {"organizationName": "mollit ad", "lastName": "in", "firstName": "dolor nisi aliquip", "middleName": "consequat laborum Lorem exercitation", "suffix": "laborum eiusmod anim ad", "blueCrossProviderNumber": "magna", "blueShieldProviderNumber": "in sit aliquip", "taxId": "qui occaecat dolor", "medicaidProviderNumber": "ea", "providerCommercialNumber": "deserunt esse in", "stateLicenseNumber": "reprehend<PERSON><PERSON>", "uniquePhysicianIdentificationNumber": "fugiat dolor voluptate", "npi": "ex ut Duis consequat"}, "crossoverCarrier": {"organizationName": "sunt e<PERSON><PERSON>d nost<PERSON>", "blueCrossBlueShieldAssociationPlanCode": "laboris mollit eu", "taxId": "voluptate minim tempor deserunt pariatur", "nationalAssociationOfInsuranceCommissionersIdentification": "ut elit ad", "payorId": "esse ipsum ea occaecat", "pharmacyProcessorNumber": "Duis cupidatat nostrud in", "centersForMedicareAndMedicaidServicesPlanId": "laboris"}, "correctedPriorityPayer": {"organizationName": "aliqua dolor commodo", "blueCrossBlueShieldAssociationPlanCode": "do id", "taxId": "adipisicing est enim", "nationalAssociationOfInsuranceCommissionersIdentification": "ad", "payorId": "reprehenderit dolor sunt dolor", "pharmacyProcessorNumber": "sunt", "centersForMedicareAndMedicaidServicesPlanId": "esse <PERSON> commodo"}, "otherSubscriber": {"organizationName": "incididunt ea irure dolore consectetur", "lastName": "dolor <PERSON> in", "firstName": "dolore sit quis mollit consectetur", "middleName": "ad", "suffix": "amet aliquip <PERSON>rem magna aute", "taxId": "sint Duis", "standardUniqueHealthIdentifierForEachIndividualInTheUnitedStates": "commodo", "memberId": "ipsum est reprehenderit"}, "inpatientAdjudication": {"coveredDaysOrVisitsCount": "cupidatat dolor consectetur sint aliquip", "ppsOperatingOutlierAmount": "elit cillum dolore", "lifetimePsychiatricDaysCount": "in aliquip occaecat officia", "claimDRGAmount": "officia", "claimPaymentRemarkCode1": "mollit ut magna", "claimDisproportionateShareAmount": "pariatur consectetur nostrud", "claimMSPPassThroughAmount": "in cupidatat commodo", "claimPPSCapitalAmount": "eu", "ppsCapitalFSPDRGAmount": "dolor aute tempor", "ppsCapitalHSPDRGAmount": "ex Excepteur esse exercitation", "ppsCapitalDSHDRGAmount": "culpa ut in qui cupidatat", "oldCapitalAmount": "incididunt aliqua voluptate", "ppsCapitalIMEAmount": "tempor <PERSON><PERSON> sunt", "ppsOperatingHospitalSpecificDRGAmount": "anim", "costReportDayCount": "reprehend<PERSON><PERSON>", "ppsOperatingFederalSpecificDRGAmount": "id ut minim adipisicing qui", "claimPPSCapitalOutlierAmount": "magna labore cillum minim", "claimIndirectTeachingAmount": "incididunt irure ullamco qui nisi", "nonPayableProfessionalComponentAmount": "incididunt esse labore cupidatat <PERSON>is", "claimPaymentRemarkCode2": "voluptate occaecat quis magna", "claimPaymentRemarkCode3": "id dolore consectetur proident eu", "claimPaymentRemarkCode4": "occaecat incididunt", "claimPaymentRemarkCode5": "amet minim do", "ppsCapitalExceptionAmount": "reprehend<PERSON><PERSON>"}, "outpatientAdjudication": {"reimbursementRate": "minim do", "claimHCPCSPayableAmount": "sed elit", "claimPaymentRemarkCode1": "irure tempor pariatur laborum ex", "claimPaymentRemarkCode2": "id pariatur aliquip anim", "claimPaymentRemarkCode3": "quis", "claimPaymentRemarkCode4": "commodo", "claimPaymentRemarkCode5": "ut Ut", "claimESRDPaymentAmount": "sit", "nonPayableProfessionalComponentAmount": "nisi voluptate nostrud in reprehenderit"}, "claimContactInformation": [{"contactName": "sit", "contactMethods": [{"email": "sint ut aliqua aliquip Ut", "fax": "anim irure cillum adipisicing", "phone": "commodo est cillum", "phoneExtension": "qui veniam ad consequat"}, {"email": "adipisicing", "fax": "Ut consectetur sit qui", "phone": "in", "phoneExtension": "id"}]}], "otherClaimRelatedIdentification": {"groupOrPolicyNumber": "ipsum quis", "memberIdentificationNumber": "Ut nulla minim reprehenderit quis", "employeeIdentificationNumber": "non", "groupNumber": "aliqua anim", "rePricedClaimReferenceNumber": "ex sit esse", "adjustedRePricedClaimReferenceNumber": "ipsum ex", "authorizationNumber": "occaecat consequat", "classOfContractCode": "proident eu est enim", "medicalRecordIdentificationNumber": "non occaecat commodo ullamco <PERSON>is", "originalReferenceNumber": "adipisicing consectetur", "priorAuthorizationNumber": "mollit Duis non", "predeterminationOfBenefitsIdentificationNumber": "dolor magna", "insurancePolicyNumber": "ullamco ut", "ssn": "Excepteur est esse"}, "renderingProviderIdentification": {"stateLicenseNumber": "in", "blueCrossProviderNumber": "aliquip sit", "blueShieldProviderNumber": "in", "medicareProviderNumber": "voluptate aute ex amet cupidatat", "medicaidProviderNumber": "sed ul<PERSON>co", "providerUPINNumber": "sint aliquip amet tempor", "champusIdentificationNumber": "dolor elit adipisicing", "facilityIdNumber": "anim fugiat est enim do", "nationalCouncilForPrescriptionDrugProgramPharmacyNumber": "commodo sint", "providerCommercialNumber": "in ipsum aute labore anim", "locationNumber": "labore nulla Excepteur dolore"}, "claimSupplementalInformation": {"coverageAmount": "<PERSON><PERSON><PERSON><PERSON>", "discountAmount": "deserunt ex labore in", "perDayLimit": "10.00", "patientAmountPaid": "10.00", "interest": "5.00", "negativeLedgerBalance": "0.00", "tax": "50.00", "totalClaimBeforeTaxes": "100.00", "federalMedicareOrMedicaidPaymentMandateCategory1": "quis", "federalMedicareOrMedicaidPaymentMandateCategory2": "qui voluptate aute", "federalMedicareOrMedicaidPaymentMandateCategory3": "ullamco", "federalMedicareOrMedicaidPaymentMandateCategory4": "mollit do", "federalMedicareOrMedicaidPaymentMandateCategory5": "consectetur do anim sed incididunt"}, "claimSupplementalInformationQuantities": {"coveredActual": "tempor sint officia", "coInsuredActual": "dolor occaecat laboris in Ut", "lifeTimeReserveActual": "dolor enim pariatur ullamco", "lifeTimeReserveEstimated": "in", "nonCoveredEstimated": "quis deserunt sit", "notReplacedBloodUnits": "Duis do nisi", "outlierDays": "amet magna deserunt", "prescription": "sit Excepteur elit minim", "visits": "nostrud ad incididunt nisi consequat", "federalMedicareOrMedicaidPaymentMandateCategory1": "enim sit id <PERSON>", "federalMedicareOrMedicaidPaymentMandateCategory2": "dolore sed amet dolore", "federalMedicareOrMedicaidPaymentMandateCategory3": "deserunt officia labore", "federalMedicareOrMedicaidPaymentMandateCategory4": "dolore qui minim quis magna", "federalMedicareOrMedicaidPaymentMandateCategory5": "minim nostrud qui Ut sit"}, "serviceLines": [{"serviceDate": "<PERSON><PERSON> aute", "serviceStartDate": "et laborum", "serviceEndDate": "ad ea incididunt nulla irure", "lineItemControlNumber": "ullamco", "servicePaymentInformation": {"productOrServiceIDQualifier": "ad", "productOrServiceIDQualifierValue": "mollit Excepteur nulla quis do", "adjudicatedProcedureCode": "dolore in pariatur", "adjudicatedProcedureModifierCodes": ["laborum sit id", "dolor", "aute exercitation do consectetur", "dolor ipsum magna laborum commodo"], "lineItemChargeAmount": "i<PERSON>re fugiat", "lineItemProviderPaymentAmount": "sit amet", "nationalUniformBillingCommitteeRevenueCode": "incididunt", "unitsOfServicePaidCount": "nulla labore", "submittedProductOrServiceIDQualifier": "id Duis culpa sunt", "submittedProductOrServiceIDQualifierValue": "irure", "submittedAdjudicatedProcedureCode": "exercitation", "submittedAdjudicatedProcedureModifierCodes": ["in nulla officia", "mollit fugiat cillum eiusmod consequat", "consequat exercitation", "ut magna dolor veniam id", "cupidatat minim Duis amet"], "submittedProcedureCodeDescription": "mollit", "originalUnitsOfServiceCount": "mollit"}, "serviceAdjustments": [{"claimAdjustmentGroupCode": "reprehenderit enim non", "claimAdjustmentGroupCodeValue": "velit in cupidatat", "adjustmentReasonCode1": "laborum id commodo esse", "adjustmentAmount1": "dolore <PERSON>", "adjustmentQuantity1": "ut veniam aliqua deserunt", "adjustmentReasonCode2": "aliqua eu", "adjustmentAmount2": "esse quis Duis et cupidatat", "adjustmentQuantity2": "voluptate labore", "adjustmentReasonCode3": "sit cillum commodo ipsum aliqua", "adjustmentAmount3": "amet eu", "adjustmentQuantity3": "id laboris qui nisi", "adjustmentReasonCode4": "laborum", "adjustmentAmount4": "Duis enim minim", "adjustmentQuantity4": "Duis ea amet", "adjustmentReasonCode5": "Ut anim dolor", "adjustmentAmount5": "ea amet dolore qui et", "adjustmentQuantity5": "eu minim anim enim <PERSON>eur", "adjustmentReasonCode6": "sed", "adjustmentAmount6": "nisi laboris Lorem dolore ipsum", "adjustmentQuantity6": "aute"}, {"claimAdjustmentGroupCode": "et dolor sit", "claimAdjustmentGroupCodeValue": "sit voluptate cillum veniam", "adjustmentReasonCode1": "enim", "adjustmentAmount1": "in sed pariatur amet", "adjustmentQuantity1": "sint aliquip incididunt", "adjustmentReasonCode2": "dolore <PERSON>", "adjustmentAmount2": "cillum Ut tempor ullamco", "adjustmentQuantity2": "ea Excepteur consequat laboris elit", "adjustmentReasonCode3": "laboris", "adjustmentAmount3": "non", "adjustmentQuantity3": "amet ad Excepteur deserunt dolore", "adjustmentReasonCode4": "elit", "adjustmentAmount4": "pariatur laborum ea Lorem adipisicing", "adjustmentQuantity4": "ut proident", "adjustmentReasonCode5": "officia ut quis sunt", "adjustmentAmount5": "fugiat tempor eu quis nisi", "adjustmentQuantity5": "consequat <PERSON>", "adjustmentReasonCode6": "eius<PERSON>d incididunt cupidatat", "adjustmentAmount6": "quis voluptate amet ullamco", "adjustmentQuantity6": "deserunt cillum do incididunt"}, {"claimAdjustmentGroupCode": "incididunt cupidatat sed sint", "claimAdjustmentGroupCodeValue": "consequat nostrud in anim", "adjustmentReasonCode1": "aute dolore", "adjustmentAmount1": "in pariatur ullamco voluptate veniam", "adjustmentQuantity1": "adipisicing dolor proident minim consectetur", "adjustmentReasonCode2": "tempor quis", "adjustmentAmount2": "in consequat", "adjustmentQuantity2": "voluptate veniam", "adjustmentReasonCode3": "ut eiusmod irure anim", "adjustmentAmount3": "id eu mollit in", "adjustmentQuantity3": "ea aliquip id ut", "adjustmentReasonCode4": "voluptate nisi dolore nostrud", "adjustmentAmount4": "labore aliqua <PERSON>", "adjustmentQuantity4": "in", "adjustmentReasonCode5": "labore laborum esse", "adjustmentAmount5": "aute Ut reprehenderit sed", "adjustmentQuantity5": "commodo", "adjustmentReasonCode6": "voluptate tempor do dolor", "adjustmentAmount6": "occaecat amet", "adjustmentQuantity6": "<PERSON>eur sint"}, {"claimAdjustmentGroupCode": "amet", "claimAdjustmentGroupCodeValue": "enim occaecat Duis aute tempor", "adjustmentReasonCode1": "ea voluptate nostrud cupidatat Lorem", "adjustmentAmount1": "nulla eu et aliqua", "adjustmentQuantity1": "exercitation", "adjustmentReasonCode2": "esse dolor ullamco exercitation", "adjustmentAmount2": "exercitation magna", "adjustmentQuantity2": "qui in", "adjustmentReasonCode3": "quis tempor dolore ut", "adjustmentAmount3": "occaecat est nisi cillum", "adjustmentQuantity3": "sit dolor esse qui", "adjustmentReasonCode4": "sit enim", "adjustmentAmount4": "elit", "adjustmentQuantity4": "<PERSON><PERSON><PERSON><PERSON>", "adjustmentReasonCode5": "magna incididunt", "adjustmentAmount5": "consectetur adipisicing", "adjustmentQuantity5": "sit voluptate", "adjustmentReasonCode6": "ut", "adjustmentAmount6": "no<PERSON><PERSON> dolor in consequat", "adjustmentQuantity6": "nisi"}], "serviceIdentification": {"ambulatoryPatientGroupNumber": "e<PERSON><PERSON><PERSON> aute", "ambulatoryPaymentClassification": "culpa", "authorizationNumber": "labore aute", "attachmentCode": "ut non labore", "priorAuthorizationNumber": "in enim et", "preDeterminationOfBenefitsIdentificationNumber": "fugiat adipisicing exercitation sint", "locationNumber": "est qui enim officia", "rateCodeNumber": "qui adipisicing"}, "renderingProviderInformation": {"stateLicenseNumber": "ullamco ut ea", "blueCrossProviderNumber": "id", "blueShieldProviderNumber": "commodo", "medicareProviderNumber": "consectetur", "medicaidProviderNumber": "dolore velit in dolor Duis", "providerUPINNumber": "qui proident tempor in consequat", "champusIdentificationNumber": "labore aliquip irure quis sit", "facilityIdNumber": "culpa do dolore", "nationalCouncilForPrescriptionDrugProgramPharmacyNumber": "sit", "providerCommercialNumber": "do eu voluptate", "npi": "exercitation", "ssn": "velit", "federalTaxpayerIdentificationNumber": "magna anim non voluptate ullamco"}, "serviceSupplementalAmounts": {"allowedActual": "10.00", "deductionAmount": "10.00", "tax": "10.00", "totalClaimBeforeTaxes": "10.00", "federalMedicareOrMedicaidPaymentMandateCategory1": "ea cupidatat laboris amet", "federalMedicareOrMedicaidPaymentMandateCategory2": "dolore <PERSON>", "federalMedicareOrMedicaidPaymentMandateCategory3": "in incididunt", "federalMedicareOrMedicaidPaymentMandateCategory4": "minim qui cillum consectetur in", "federalMedicareOrMedicaidPaymentMandateCategory5": "fugiat dolore ad velit adipisicing"}, "serviceSupplementalQuantities": {"federalMedicareOrMedicaidPaymentMandateCategory1": "labore adipisicing", "federalMedicareOrMedicaidPaymentMandateCategory2": "et ex ad", "federalMedicareOrMedicaidPaymentMandateCategory3": "occa<PERSON>t", "federalMedicareOrMedicaidPaymentMandateCategory4": "est et anim aliquip", "federalMedicareOrMedicaidPaymentMandateCategory5": "ipsum ut commodo irure officia"}, "healthCareCheckRemarkCodes": [{"codeListQualifierCode": "dolor consequat Excepteur ullam<PERSON> pariatur", "codeListQualifierCodeValue": "minim", "remarkCode": "sit elit"}, {"codeListQualifierCode": "veniam mollit voluptate", "codeListQualifierCodeValue": "in laboris exercitation minim aliqua", "remarkCode": "sunt"}, {"codeListQualifierCode": "sint aute et", "codeListQualifierCodeValue": "ex", "remarkCode": "reprehenderit ea veniam"}, {"codeListQualifierCode": "dolor ullamco commodo reprehenderit", "codeListQualifierCodeValue": "nisi nulla in velit", "remarkCode": "nostrud anim et in"}, {"codeListQualifierCode": "nisi in aute sed", "codeListQualifierCodeValue": "dolor deserunt", "remarkCode": "ea nostrud labore sit voluptate"}], "healthCarePolicyIdentification": [{"pariatur_9de": false, "do_d": -74910417.27541825, "policyFormIdentifyingNumber": "ad"}, {"ut2": "e<PERSON><PERSON><PERSON> sunt Excepteur elit", "policyFormIdentifyingNumber": "ut ex proident"}, {"policyFormIdentifyingNumber": "cillum officia incididunt culpa"}]}, {"serviceDate": "officia cillum elit", "serviceStartDate": "reprehenderit minim occaecat", "serviceEndDate": "ullamco ex", "lineItemControlNumber": "sunt sed et", "servicePaymentInformation": {"productOrServiceIDQualifier": "ad eiusmod", "productOrServiceIDQualifierValue": "quis mollit in in elit", "adjudicatedProcedureCode": "proident Ut elit cupidatat do", "adjudicatedProcedureModifierCodes": ["et sint", "mollit eiusmod minim", "aliqua o<PERSON>", "incididunt dolor amet commodo", "mollit ad enim dolore in"], "lineItemChargeAmount": "adipisicing", "lineItemProviderPaymentAmount": "in consequat qui nostrud", "nationalUniformBillingCommitteeRevenueCode": "dolor occaecat ut et dolor", "unitsOfServicePaidCount": "ea", "submittedProductOrServiceIDQualifier": "ea sed", "submittedProductOrServiceIDQualifierValue": "ea", "submittedAdjudicatedProcedureCode": "qui tempor <PERSON><PERSON> sit", "submittedAdjudicatedProcedureModifierCodes": ["mollit nisi consectetur culpa reprehenderit"], "submittedProcedureCodeDescription": "sint", "originalUnitsOfServiceCount": "nisi"}, "serviceAdjustments": [{"claimAdjustmentGroupCode": "in aute", "claimAdjustmentGroupCodeValue": "elit sunt", "adjustmentReasonCode1": "magna nisi", "adjustmentAmount1": "in aute sit fugiat do", "adjustmentQuantity1": "in", "adjustmentReasonCode2": "Excepteur", "adjustmentAmount2": "proident voluptate aute adipisicing ut", "adjustmentQuantity2": "enim elit Duis consequat", "adjustmentReasonCode3": "nisi", "adjustmentAmount3": "consequat sit culpa", "adjustmentQuantity3": "minim nisi Excepteur occaecat exercitation", "adjustmentReasonCode4": "<PERSON><PERSON> o<PERSON>", "adjustmentAmount4": "sed dolor", "adjustmentQuantity4": "officia veniam aliqua", "adjustmentReasonCode5": "in et cupidatat", "adjustmentAmount5": "laborum ut ut adipisicing", "adjustmentQuantity5": "mollit incididunt in ullamco", "adjustmentReasonCode6": "laborum commodo laboris pariatur et", "adjustmentAmount6": "qui enim ut", "adjustmentQuantity6": "dolor laboris"}, {"claimAdjustmentGroupCode": "mollit", "claimAdjustmentGroupCodeValue": "sit fugiat nisi exercitation cupidatat", "adjustmentReasonCode1": "cupidatat veniam ipsum voluptate nulla", "adjustmentAmount1": "est nisi", "adjustmentQuantity1": "tempor", "adjustmentReasonCode2": "sint deserunt Lorem", "adjustmentAmount2": "velit", "adjustmentQuantity2": "adipisicing", "adjustmentReasonCode3": "Lorem in dolor", "adjustmentAmount3": "velit enim", "adjustmentQuantity3": "sunt dolor qui dolore", "adjustmentReasonCode4": "minim laboris aliqua elit", "adjustmentAmount4": "in aute", "adjustmentQuantity4": "dolor Duis laboris labore", "adjustmentReasonCode5": "<PERSON><PERSON>", "adjustmentAmount5": "<PERSON><PERSON> Excepteur laboris Ut est", "adjustmentQuantity5": "<PERSON><PERSON>eur cillum proident", "adjustmentReasonCode6": "laboris do sed", "adjustmentAmount6": "dolore voluptate magna Ut", "adjustmentQuantity6": "Ut nostrud ut"}, {"claimAdjustmentGroupCode": "ul<PERSON><PERSON> sunt", "claimAdjustmentGroupCodeValue": "dolor exercitation quis", "adjustmentReasonCode1": "aute in sint sit aliqua", "adjustmentAmount1": "velit ea laborum", "adjustmentQuantity1": "laborum occaecat", "adjustmentReasonCode2": "ex non adipisicing esse", "adjustmentAmount2": "tempor anim dolor", "adjustmentQuantity2": "velit", "adjustmentReasonCode3": "Lorem aliqua do <PERSON>", "adjustmentAmount3": "sint exercitation dolor irure", "adjustmentQuantity3": "sint labore culpa id", "adjustmentReasonCode4": "dolore in sed", "adjustmentAmount4": "ut proident", "adjustmentQuantity4": "esse nisi sunt", "adjustmentReasonCode5": "et in ut sunt", "adjustmentAmount5": "id velit deserunt laboris", "adjustmentQuantity5": "sit est sed", "adjustmentReasonCode6": "in sunt cillum", "adjustmentAmount6": "consectetur", "adjustmentQuantity6": "ea culpa proident dolore"}, {"claimAdjustmentGroupCode": "elit amet proident in nisi", "claimAdjustmentGroupCodeValue": "dolor esse consequat", "adjustmentReasonCode1": "occaecat sit adipisicing", "adjustmentAmount1": "ipsum non pariatur laborum sint", "adjustmentQuantity1": "ut incididunt occaecat", "adjustmentReasonCode2": "sint proident commodo ut", "adjustmentAmount2": "cupidatat in velit tempor", "adjustmentQuantity2": "incididunt aliquip officia", "adjustmentReasonCode3": "mollit officia in", "adjustmentAmount3": "sed Lorem do amet", "adjustmentQuantity3": "et velit minim", "adjustmentReasonCode4": "aute eu ullamco ex", "adjustmentAmount4": "in in", "adjustmentQuantity4": "<PERSON><PERSON><PERSON><PERSON>", "adjustmentReasonCode5": "qui in", "adjustmentAmount5": "in voluptate magna adipisicing", "adjustmentQuantity5": "magna sed esse voluptate ipsum", "adjustmentReasonCode6": "quis laborum incididunt nostrud nisi", "adjustmentAmount6": "Excepteur sed adipisicing", "adjustmentQuantity6": "enim labore irure ex laborum"}], "serviceIdentification": {"ambulatoryPatientGroupNumber": "in proident incididunt eiusmod elit", "ambulatoryPaymentClassification": "aute", "authorizationNumber": "officia dolor", "attachmentCode": "pariatur eu enim sit", "priorAuthorizationNumber": "qui magna exercitation quis", "preDeterminationOfBenefitsIdentificationNumber": "veniam", "locationNumber": "consectetur in dolore", "rateCodeNumber": "cupidatat"}, "renderingProviderInformation": {"stateLicenseNumber": "et", "blueCrossProviderNumber": "deserunt", "blueShieldProviderNumber": "ipsum dolor ullamco in Lorem", "medicareProviderNumber": "velit in Lorem quis in", "medicaidProviderNumber": "ea", "providerUPINNumber": "mollit velit elit magna minim", "champusIdentificationNumber": "ullamco est ut aute ut", "facilityIdNumber": "in ad ut Ut cupidatat", "nationalCouncilForPrescriptionDrugProgramPharmacyNumber": "no<PERSON><PERSON> laboris", "providerCommercialNumber": "et reprehenderit sunt laboris ea", "npi": "occa<PERSON>t", "ssn": "consectetur occaecat", "federalTaxpayerIdentificationNumber": "non labore sint aute"}, "serviceSupplementalAmounts": {"allowedActual": "pariatur labore", "deductionAmount": "culpa officia reprehenderit ut aliqua", "tax": "<PERSON>is elit", "totalClaimBeforeTaxes": "aliqua laborum", "federalMedicareOrMedicaidPaymentMandateCategory1": "Duis magna non sunt", "federalMedicareOrMedicaidPaymentMandateCategory2": "veniam tempor ad", "federalMedicareOrMedicaidPaymentMandateCategory3": "culpa est voluptate nisi ullamco", "federalMedicareOrMedicaidPaymentMandateCategory4": "consectetur sit velit", "federalMedicareOrMedicaidPaymentMandateCategory5": "fugiat sint"}, "serviceSupplementalQuantities": {"federalMedicareOrMedicaidPaymentMandateCategory1": "laborum culpa id eiusmod magna", "federalMedicareOrMedicaidPaymentMandateCategory2": "ullamco eu officia amet <PERSON>", "federalMedicareOrMedicaidPaymentMandateCategory3": "reprehenderit adipisicing incididunt fugiat", "federalMedicareOrMedicaidPaymentMandateCategory4": "proident ea dolore qui", "federalMedicareOrMedicaidPaymentMandateCategory5": "dolore Lorem minim"}, "healthCareCheckRemarkCodes": [{"codeListQualifierCode": "sunt veniam ullamco velit", "codeListQualifierCodeValue": "dolore", "remarkCode": "anim"}, {"codeListQualifierCode": "tempor", "codeListQualifierCodeValue": "ex sit enim ipsum", "remarkCode": "consequat"}, {"codeListQualifierCode": "e<PERSON><PERSON><PERSON> sint", "codeListQualifierCodeValue": "non mollit laborum nisi", "remarkCode": "<PERSON><PERSON> consequat consectetur"}, {"codeListQualifierCode": "eu consequat nisi nulla commodo", "codeListQualifierCodeValue": "veniam nulla et", "remarkCode": "sint consequat aute incididunt"}], "healthCarePolicyIdentification": [{"temporb": -70990362, "policyFormIdentifyingNumber": "sit dolor dolor velit"}, {"ut_1_0": false, "policyFormIdentifyingNumber": "ut labore dolore amet cillum"}]}]}]}], "providerAdjustments": [{"providerIdentifier": "in officia adipisicing anim", "fiscalPeriodDate": "proident eiusmod veniam nulla", "adjustments": [{"adjustmentReasonCode": "quis commodo adipisicing cupidatat sint", "adjustmentReasonCodeValue": "enim", "providerAdjustmentIdentifier": "officia eu laboris ex", "providerAdjustmentAmount": "voluptate nisi culpa sunt tempor"}]}]}], "meta": {"applicationMode": "reprehenderit nulla", "senderId": "eu adipisicing sint", "traceId": "eu minim enim id"}}