{"_meta": {"source": "payer", "subform": {}}, "id": 53, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "type_id": "CMMED", "state_id": null, "bstate_id": null, "qualifier_id": null, "invoice_type_id": null, "cms_13_id": null, "cms_18_id": null, "biller_id": null, "collector_id": null, "auto_transfer_adj_id": null, "adjust_claim_feecode_qualifier_id": null, "adj_code_id": null, "o270_qualifier_id": null, "o270_entity_ident_code_id": null, "o270_entity_type_qualifier_id": null, "o270_service_code_id": null, "o270_secid_qualifier_id": null, "o270_dep_qualifier_id": null, "o270_facility_code_id": null, "o270_code_qualifier_id": null, "nabp_pharmacy_qualifier_id": null, "default_service_place_id": null, "software_cert_id": null, "presc_id_qualifier_id": null, "primary_prov_qualifier_id": null, "ps_id_qualifier_compound_id": null, "ps_id_qualifier_po_id": null, "insur_prov_qualifier_id": null, "default_los_code_id": null, "default_rx_origin_id": null, "default_pa_type_code_id": null, "default_service_type_id": null, "default_residence_id": null, "default_other_coverage_code_id": null, "other_payer_pt_resp_np_id": null, "assigned_contract_id": null, "assigned_matrix_id": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "05/15/2024 09:28:49", "auto_name": "UNC - United Healthcare 110,107,3,7 - INN", "change_type": null, "change_data": null, "created_on": "05/15/2024 13:28:50", "change_on": null, "organization": "United Healthcare", "short_code": "UNC", "active": "Yes", "inn_oon": "INN", "phone": "(*************", "fax": "(*************", "address": null, "city": null, "zip": null, "borganization": null, "attn": null, "baddress": null, "bcity": null, "bzip": null, "provider_no": null, "box_24j": "Yes", "box_24j_grt": "Yes", "naic_payer_id": null, "hpid": null, "ocna": null, "cms_1": [], "cms_2": null, "cms_3": null, "cms_4": null, "cms_5": null, "cms_6": null, "cms_7": null, "cms_8": null, "cms_9": null, "cms_10": null, "cms_11": null, "cms_12": null, "cms_14": null, "cms_15": null, "cms_16": null, "cms_17": null, "notes": null, "hcpc_pricing": null, "automatic_method": "Line Item", "rx_billing_method": "Ingredient", "updated_special_pricing": null, "updated_expected_pricing": null, "req_auth": null, "collapse_items": null, "req_signed_documents": null, "send_payer_address": null, "days_timely_filing": null, "default_coverage": null, "supplementary_payer": null, "combine_orders_batch": null, "payer_340B": null, "span_dates_rentals": null, "claims_calendar_month": null, "medi_cal_modifier_litem": null, "medi_cal_format_supp": null, "medi_cal_itemized": null, "min_accept_margin": null, "hold_sub_rental_claim": null, "bill_recur_arrears": null, "auto_transfer_ern": null, "box32_address": null, "auto_split_perdiem": null, "nonbillable_support_docs": null, "missing_dx_status": null, "discarded_meds": null, "auth_dose_freq": null, "transfer_deductibles": null, "o270_payer_id": null, "o270_receiver_id": null, "o270_taxid_recid": null, "o270_taxid_secid": null, "o270_allcaps": null, "o270_autopop_subscriber": null, "o270_autopop_dependent": null, "nabp_pharmacy_pull_id": null, "nabp_pharmacy_id": null, "processor_control_no": null, "bin": null, "prescriber_no_field": null, "pa_mc_code": null, "default_dispense_fee": null, "calc_gross_amt_basis": null, "sum_ing_cost_pricing_seg": null, "sum_ing_gross_pricing_seg": null, "base_ing_cost_on": null, "calc_perc_sales_tax": null, "send_zeros_ndc": null, "never_comp_seg": null, "use_employee_pcn": null, "import_dx_code": null, "exclude_ssn_claim": null, "tranmit_grp_no_reversal": null, "tranmit_card_id_reversal": null, "autopop_pa_mc_code": null, "send_pharmacist_npi": null, "send_blank_person_code": null, "swap_du_dq": null, "send_pt_email": null, "send_claim_ncpdp_d0": null, "send_presc_name_address": null, "send_uppercase": null, "send_uom": null, "default_tax_basis": null, "payer_require_coupon": null, "sec_plan_type": null, "send_primary_payer_amt_paid": null, "create_add_charge_entry": null, "sec_claims_gross_amt": null, "sec_claims_ingred_cost": null, "pt_paid_amount_dx": null, "created_rejection_entry": null, "billing_method_id": "mm", "neic_payer_id": null, "default_tax_basis_id": null, "sec_claims_pr_qualifier_id": null, "pcn": null, "send_dx_code": null, "sec_claims_payer_cover_id": null, "mm_sec_pharmacy_qualifier_id": "EI", "mm_provider_sec_qualifier_id": "EI", "mm_default_report_type_id": null, "mm_default_pwk_id": null, "mm_default_claim_type_id": null, "mm_order_provider_qualifier_id": null, "mm_insurance_type_id": null, "mm_sec_pharmacy_pull_id": "Yes", "mm_sec_pharmacy_id": null, "mm_rendering_provider_id": "**********", "mm_rendprovider_qualifier_id": "XX", "mm_provider_sec_id": "1234", "mm_submit_ndc_rx_info": null, "mm_send_claim_status_inquiry": null, "mm_exclude_2010aa": null, "mm_exclude_2010bb": null, "mm_exclude_dx_dec": null, "mm_sos_only": null, "mm_taxonomy_code": null, "mm_payee_id": null, "mm_claim_office_no": null, "mm_send_contract_pricing": null, "mm_copy_ordering_md_to_ref": null, "mm_send2320amt": null, "mm_send_plan_identifier": null, "mm_send_upin_supplies": null, "mm_send_employer_name": null, "mm_send_jurisdiction_state_code": null, "mm_remove_dash": null, "mm_exclude_ordering_provider": null, "mm_send_hpid": null, "mm_payerid_ref": null, "mm_default_837i_eob": null, "mm_send_mrn": null, "neic_id": null, "naic_id": null, "hp_id": null, "coba_id": null, "mm_taxonomy_id": null, "pverify_payer_id": "00192", "ss_benefit_id": null, "mm_default_other_coverage_id": null, "mm_sec_claims_pr_qualifier_id": null, "prescriber_no_field_id": null, "base_ing_cost_on_id": null, "compound_qualifier_id": null, "noncompound_drug_qualifier_id": null, "service_qualifier_id": null, "default_origin_id": null, "default_pa_type_id": null, "default_service_id": null, "default_place_of_res_id": null, "default_tax_base_id": null, "ncpdp_sec_plan_type_id": null, "ncpdp_sec_plan_qualifier_id": null, "ncpdp_pri_payer_qualifier_id": null, "ncpdp_sec_claims_pr_qualifier_id": null, "ncpdp_sec_claims_benefit_qual_id": null, "mac_id": null, "medicaid_id": null, "hin_id": null, "mm_send_primary_payer_amt_paid": null, "mm_sec_claims_gross_amt": null, "mm_sec_claims_ingred_cost": null, "mm_pt_paid_amount_dx": null, "mm_sec_claims_payer_cover_id": null, "default_los_id": null, "transmit_grp_no_reversal": null, "transmit_card_id_reversal": null, "swap_uc_and_gross": null, "ncpdp_sec_plan_id": null, "ncpdp_coupon": null, "ncpdp_sec_payer_name": null, "ncpdp_send_primary_payer_amt_paid": null, "ncpdp_sec_claims_gross_amt": null, "ncpdp_sec_claims_ingred_cost": null, "ncpdp_pt_paid_amount_dx": null, "ncpdp_e1_prov_required": null, "ncpdp_pharmacy_qualifier_id": "01", "ncpdp_pharmacy_pull_id": null, "ncpdp_pharmacy_id": null, "auto_split_noncompound": null, "compound_sub_clarification_code": null, "ncpdp_sec_claims_pp_qualifier_id": null, "is_self_pay": null, "search": "'-1234':7C '107':12 '110':11 '123':6C '3':13 '7':14 '888':5C 'healthcare':2A,10 'inn':4B,15 'unc':3A,8 'united':1A,9", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "type_id_auto_name": "Commercial Medical", "billing_method_id_auto_name": "mm - Electronic Medical", "state_id_auto_name": null, "bstate_id_auto_name": null, "qualifier_id_auto_name": null, "cms_13_id_auto_name": null, "cms_18_id_auto_name": null, "biller_id_auto_name": null, "collector_id_auto_name": null, "auto_transfer_adj_id_auto_name": null, "adjust_claim_feecode_qualifier_id_auto_name": null, "adj_code_id_auto_name": null, "pverify_payer_id_auto_name": "United Healthcare", "mm_sec_pharmacy_qualifier_id_auto_name": "EI - Employer's Identification Number", "mm_provider_sec_qualifier_id_auto_name": "EI - Employer's Identification Number", "mm_default_report_type_id_auto_name": null, "mm_default_pwk_id_auto_name": null, "mm_default_claim_type_id_auto_name": null, "mm_taxonomy_id_auto_name": null, "mm_order_provider_qualifier_id_auto_name": null, "mm_insurance_type_id_auto_name": null, "mm_default_other_coverage_id_auto_name": null, "mm_sec_claims_pr_qualifier_id_auto_name": null, "ncpdp_pharmacy_qualifier_id_auto_name": null, "prescriber_no_field_id_auto_name": null, "default_service_place_id_auto_name": null, "base_ing_cost_on_id_auto_name": null, "compound_qualifier_id_auto_name": null, "noncompound_drug_qualifier_id_auto_name": null, "service_qualifier_id_auto_name": null, "default_origin_id_auto_name": null, "default_pa_type_id_auto_name": null, "default_service_id_auto_name": null, "default_place_of_res_id_auto_name": null, "default_tax_base_id_auto_name": null, "ncpdp_sec_plan_type_id_auto_name": null, "ncpdp_sec_plan_qualifier_id_auto_name": null, "ncpdp_pri_payer_qualifier_id_auto_name": null, "ncpdp_sec_claims_pp_qualifier_id_auto_name": null, "ncpdp_sec_claims_benefit_qual_id_auto_name": null, "assigned_contract_id_auto_name": null, "assigned_matrix_id_auto_name": null, "site_id": [110, 107, 3, 7], "site_id_auto_name": ["Heritage Biologics Heritage Biologics", "Dallas - TX DTX", "Lee's Summit, MO <PERSON>'s Summit, MO", "Overland Park - HB Overland Park - HB"], "ncpdp_sec_claims_pp_qlrs": [], "ncpdp_sec_claims_pp_qlrs_auto_name": [], "subform_contact": [], "subform_contact_auto_name": []}