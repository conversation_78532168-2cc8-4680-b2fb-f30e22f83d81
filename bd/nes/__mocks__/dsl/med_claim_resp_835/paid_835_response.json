{"_meta": {"source": "med_claim_resp_835", "subform": {"transactions": "med_claim_resp_tran", "meta": "med_claim_resp_meta"}}, "transactions": [{"_meta": {"source": "med_claim_resp_tran", "subform": {"financial_information": "med_claim_resp_fn_in", "payer": "med_claim_resp_pyr", "payee": "med_claim_resp_pye", "payment_and_remit_reassociation_details": "med_claim_resp_py_dtl", "detail_info": "med_claim_resp_dt_info", "provider_adjustments": "med_claim_resp_prov_adjs"}}, "financial_information": [{"_meta": {"source": "med_claim_resp_fn_in", "subform": {"sender_account_details": "med_claim_resp_sd_act", "receiver_account_details": "med_claim_resp_rc_act"}}, "sender_account_details": [{"_meta": {"source": "med_claim_resp_sd_act", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "et esse Duis id", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "sender_dfi_id_number_qualifier": "et esse Duis id", "sender_dfi_identifier": "in Ut", "sender_account_number_qualifier": "id aliqua mollit ut", "sender_account_number": "consequat sunt", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_fn_in", "parent_archived": null}], "receiver_account_details": [{"_meta": {"source": "med_claim_resp_rc_act", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "anim non est", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "receiver_dfi_id_number_qualifier": "anim non est", "receiver_dfi_identification_number": "consectetur ex eu dolore nisi", "receiver_account_number_qualifier": "pariatur sunt id", "receiver_account_number": "do velit exercitation", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_fn_in", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "sit", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "transaction_handling_code": "sit", "payer_identifier": "sint ex officia", "originating_company_supplemental_code": "minim elit irure sunt", "total_actual_provider_payment_amount": 100, "check_issue_or_eft_effective_date": "12/10/2024", "credit_or_debit_flag_code": "et in in pariatur sint", "payment_method_code": "nisi occaecat fugiat", "payment_format_code": "magna sit Lorem amet officia", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tran", "parent_archived": null}], "payer": [{"_meta": {"source": "med_claim_resp_pyr", "subform": {"address": "med_claim_resp_py_adr", "business_contact_information": "med_claim_resp_bl_cont", "technical_contact_information": "med_claim_resp_tcnt"}}, "address": [{"_meta": {"source": "med_claim_resp_py_adr", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "in", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "address1": "consequat voluptate", "address2": "commodo mollit sed in in", "city": "Excepteur", "state": "in", "postal_code": "tempor laboris <PERSON><PERSON> dolore", "country_code": "qui", "country_sub_code": "aliquip aliqua", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_pyr", "parent_archived": null}], "business_contact_information": [{"_meta": {"source": "med_claim_resp_bl_cont", "subform": {"contact_methods": "med_claim_resp_bl_cont_m"}}, "contact_methods": [{"_meta": {"source": "med_claim_resp_bl_cont_m", "subform": {}}, "id": 113, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ipsum", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "email": "ipsum", "fax": "velit labore anim", "phone": "qui ex consequat ipsum", "phone_extension": "sunt aliqua cupidatat ut laborum", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 59, "parent_form": "med_claim_resp_bl_cont", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_bl_cont_m", "subform": {}}, "id": 114, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "occaecat irure ullamco aliquip incididunt", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "email": "occaecat irure ullamco aliquip incididunt", "fax": "culpa irure sint ut", "phone": "qui Ut consequat", "phone_extension": "dolore", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 59, "parent_form": "med_claim_resp_bl_cont", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_bl_cont_m", "subform": {}}, "id": 115, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "dolor consequat consectetur nulla voluptate", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "email": "dolor consequat consectetur nulla voluptate", "fax": "reprehend<PERSON><PERSON>", "phone": "ad ut mollit", "phone_extension": "fugiat nulla Ut", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 59, "parent_form": "med_claim_resp_bl_cont", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_bl_cont_m", "subform": {}}, "id": 116, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "deserunt Excepteur sed ut", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "email": "deserunt Excepteur sed ut", "fax": "nostrud laborum sint Duis", "phone": "qui velit ipsum aute fugiat", "phone_extension": "ex cupidatat", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 59, "parent_form": "med_claim_resp_bl_cont", "parent_archived": null}], "id": 59, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "dolore", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "contact_name": "dolore", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_pyr", "parent_archived": null}], "technical_contact_information": [{"_meta": {"source": "med_claim_resp_tcnt", "subform": {"contact_methods": "med_claim_resp_tcont_m"}}, "contact_methods": [{"_meta": {"source": "med_claim_resp_tcont_m", "subform": {}}, "id": 112, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "in amet minim deserunt", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "url": "amet aute do Excepteur sit", "email": "in amet minim deserunt", "phone": "dolor", "phone_extension": "amet cillum Duis dolor", "fax": "exercitation", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tcnt", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_tcont_m", "subform": {}}, "id": 113, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "exercitation in id pariatur", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "url": "in consequat mollit", "email": "exercitation in id pariatur", "phone": "sed occaecat commodo", "phone_extension": "veniam aute nulla occaecat mollit", "fax": "magna aliquip dolor in", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tcnt", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_tcont_m", "subform": {}}, "id": 114, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "sed Excepteur ut", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "url": "non consequat", "email": "sed Excepteur ut", "phone": "dolore magna in velit", "phone_extension": "sint", "fax": "non deserunt aute", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tcnt", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_tcont_m", "subform": {}}, "id": 115, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Lorem adipisicing occaecat elit", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "url": "qui cillum ad", "email": "Lorem adipisicing occaecat elit", "phone": "cillum non in", "phone_extension": "minim aute", "fax": "ea sint fugiat in non", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tcnt", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "quis", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "contact_name": "quis", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_pyr", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "pariatur dolore dolore Ut", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "name": "pariatur dolore dolore Ut", "centers_for_medicare_and_medicaid_services_plan_id": "ut consectetur deserunt amet commodo", "payer_identification_number": "aliqua ut commodo exercitation elit", "submitter_identification_number": "quis", "health_industry_number": "ut consequat qui tempor incididunt", "national_association_of_insurance_commissioners": "ad irure officia", "payer_web_site_url": "occaecat dolore nisi", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tran", "parent_archived": null}], "payee": [{"_meta": {"source": "med_claim_resp_pye", "subform": {"address": "med_claim_resp_pe_adr", "remittance_delivery_method": "med_claim_resp_dlv_mthd"}}, "address": [{"_meta": {"source": "med_claim_resp_pe_adr", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "aliqua", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "address1": "nisi elit dolore", "address2": "quis", "city": "non cillum", "state": "aliqua", "postal_code": "est irure ea incididunt culpa", "country_code": "nisi sint ut sunt et", "country_sub_code": "officia", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_pye", "parent_archived": null}], "remittance_delivery_method": [{"_meta": {"source": "med_claim_resp_dlv_mthd", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ad do", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "name": "ad do", "email": "ut", "ftp": "aute culpa do <PERSON>eur", "on_line": "qui commodo fugiat", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_pye", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ullamco", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "name": "ullamco", "tax_id": "qui", "centers_for_medicare_and_medicaid_services_plan_id": "cupidatat exercitation est et officia", "npi": "dolore commodo dolor", "state_license_number": "in ipsum anim in in", "national_council_for_prescription_drug_programs_pharmacy_number": "pariatur officia aute eu", "payee_identification": "mollit Excepteur nisi voluptate", "federal_tax_payers_identification_number": "labore ipsum", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tran", "parent_archived": null}], "payment_and_remit_reassociation_details": [{"_meta": {"source": "med_claim_resp_py_dtl", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "enim", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "trace_type_code": "enim", "check_or_eft_trace_number": "exercitation nostrud consectetur esse adipisicing", "originating_company_identifier": "cillum quis ex Lorem nulla", "originating_company_supplemental_code": "esse nostrud proident", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tran", "parent_archived": null}], "detail_info": [{"_meta": {"source": "med_claim_resp_dt_info", "subform": {"provider_summary_information": "med_claim_resp_prov_sum", "provider_supplemental_summary_information": "med_claim_resp_prov_sup", "payment_info": "med_claim_resp_py_info"}}, "provider_summary_information": [{"_meta": {"source": "med_claim_resp_prov_sum", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "incididunt", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "provider_identifier": "incididunt", "facility_type_code": "eiusmod non", "fiscal_period_date": "12/10/2024", "total_claim_charge_amount": 100, "total_claim_count": "elit aute non", "total_msp_payer_amount": 100, "total_non_lab_charge_amount": 100, "total_hcpcs_reported_charge_amount": 100, "total_hcpcs_payable_amount": 100, "total_professional_component_amount": 100, "total_msp_patient_liability_met_amount": 100, "total_patient_reimbursement_amount": 100, "total_pip_adjustment_amount": 100, "total_pip_claim_count": "incididunt dolore culpa tempor", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_dt_info", "parent_archived": null}], "provider_supplemental_summary_information": [{"_meta": {"source": "med_claim_resp_prov_sup", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "100", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "total_drg_amount": 100, "total_federal_specific_amount": 100, "total_hospital_specific_amount": 100, "total_disproportionate_share_amount": 100, "total_capital_amount": 100, "total_indirect_medical_education_amount": 100, "total_outlier_day_count": "sunt", "total_day_outlier_amount": 100, "total_cost_outlier_amount": 100, "total_discharge_count": "culpa voluptate officia adipisicing", "average_drg_length_of_stay": "ea mollit", "total_cost_report_day_count": "in occaecat in", "total_covered_day_count": "in Ut mollit Duis", "total_non_covered_day_count": "magna id", "total_msp_pass_through_amount": 100, "average_drg_weight": "Ut", "total_pps_capital_fsp_drg_amount": null, "total_pps_capital_hsp_drg_amount": null, "total_pps_dsh_drg_amount": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_dt_info", "parent_archived": null}], "payment_info": [{"_meta": {"source": "med_claim_resp_py_info", "subform": {"claim_payment_info": "med_claim_resp_pmt_info", "claim_adjustments": "med_claim_resp_pyr_adj", "patient_name": "med_claim_resp_pt_nm", "corrected_patient_or_insured_name": "med_claim_resp_cpt_nm", "subscriber": "med_claim_resp_py_sub", "other_subscriber": "med_claim_resp_osub", "rendering_provider": "med_claim_resp_rend", "rendering_provider_identification": "med_claim_resp_rend_id", "crossover_carrier": "med_claim_resp_x_ovr", "corrected_priority_payer": "med_claim_resp_pri_pyr", "inpatient_adjudication": "med_claim_resp_inpt", "outpatient_adjudication": "med_claim_resp_outpt", "claim_contact_information": "med_claim_resp_py_cont", "other_claim_related_identification": "med_claim_resp_oid", "claim_supplemental_information": "med_claim_resp_sup", "claim_supplemental_information_quantities": "med_claim_resp_sup_qty", "service_lines": "med_claim_resp_sl"}}, "claim_payment_info": [{"_meta": {"source": "med_claim_resp_pmt_info", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON>is sunt elit", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "patient_control_number": "<PERSON>is sunt elit", "claim_status_code": "3", "facility_type_code": "sunt", "total_claim_charge_amount": 100, "claim_payment_amount": 100, "patient_responsibility_amount": 100, "claim_filing_indicator_code": "consequat", "payer_claim_control_number": "pariatur ut do elit esse", "claim_frequency_code": "sit est", "diagnosis_related_group_drg_code": "voluptate sed aute", "diagnosis_related_group_drg_weight": "id reprehenderit", "discharge_fraction": "tempor et sit commodo deserunt", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "claim_adjustments": [{"_meta": {"source": "med_claim_resp_pyr_adj", "subform": {}}, "id": 39, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "officia ea cupidatat mollit ullamco", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "officia ea cupidatat mollit ullamco", "claim_adjustment_group_code_value": "dolore cillum ad sit et", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_pyr_adj", "subform": {}}, "id": 40, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "id occaecat", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "id occaecat", "claim_adjustment_group_code_value": "magna et aliquip", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "patient_name": [{"_meta": {"source": "med_claim_resp_pt_nm", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "magna non officia culpa tempor", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "last_name": "non officia culpa tempor", "first_name": "magna", "middle_name": "ipsum", "suffix": "id enim ut amet", "ssn": "<PERSON>rem ullam<PERSON>is voluptate", "health_insurance_claim_number": "officia incididunt velit", "standard_unique_health_identifier_for_each_individual_in_the_un": "in sed", "member_id": "voluptate officia tempor velit minim", "medicaid_recipient_identification_number": "laborum esse Ut", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "corrected_patient_or_insured_name": [{"_meta": {"source": "med_claim_resp_cpt_nm", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "nostrud sunt laborum dolor Excepteur tempor", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "last_name": "no<PERSON><PERSON> sunt", "first_name": "laborum dolor Excepteur tempor", "middle_name": "esse ad anim", "suffix": "sunt labore laborum in", "organization_name": "nisi ad veniam", "insureds_changed_unique_identification_number": "aliquip commodo est", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "subscriber": [{"_meta": {"source": "med_claim_resp_py_sub", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "in fugiat mollit magna ut", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "organization_name": "sed et", "last_name": "magna ut", "first_name": "in fugiat mollit", "middle_name": "ex deserunt incididunt", "suffix": "proident aute id anim exercitation", "tax_id": "ut aute", "standard_unique_health_identifier_for_each_individual_in_the_un": "aliquip nostrud dolore", "member_id": "tempor aute consectetur cupidatat ex", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "other_subscriber": [{"_meta": {"source": "med_claim_resp_osub", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "incididunt ea irure dolore consectetur dolor Duis in dolore s...", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "organization_name": "incididunt ea irure dolore consectetur", "last_name": "dolor <PERSON> in", "first_name": "dolore sit quis mollit consectetur", "middle_name": "ad", "suffix": "amet aliquip <PERSON>rem magna aute", "tax_id": "sint Duis", "standard_unique_health_identifier_for_each_individual_in_the_un": "commodo", "member_id": "ipsum est reprehenderit", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "rendering_provider": [{"_meta": {"source": "med_claim_resp_rend", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "dolor nisi aliquip in", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "organization_name": "mollit ad", "last_name": "in", "first_name": "dolor nisi aliquip", "middle_name": "consequat laborum Lorem exercitation", "suffix": "laborum eiusmod anim ad", "blue_cross_provider_number": "magna", "blue_shield_provider_number": "in sit aliquip", "provider_commercial_number": "deserunt esse in", "tax_id": "qui occaecat dolor", "medicaid_provider_number": "ea", "state_license_number": "reprehend<PERSON><PERSON>", "npi": "ex ut Duis consequat", "unique_physician_identification_number": "fugiat dolor voluptate", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "rendering_provider_identification": [{"_meta": {"source": "med_claim_resp_rend_id", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "in", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "state_license_number": "in", "blue_cross_provider_number": "aliquip sit", "blue_shield_provider_number": "in", "medicare_provider_number": "voluptate aute ex amet cupidatat", "provider_upin_number": "sint aliquip amet tempor", "champus_identification_number": "dolor elit adipisicing", "national_council_for_prescription_drug_program_pharmacy_number": "commodo sint", "provider_commercial_number": "in ipsum aute labore anim", "facility_id_number": "anim fugiat est enim do", "location_number": "labore nulla Excepteur dolore", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "crossover_carrier": [{"_meta": {"source": "med_claim_resp_x_ovr", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "sunt e<PERSON><PERSON>d nost<PERSON>", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "organization_name": "sunt e<PERSON><PERSON>d nost<PERSON>", "tax_id": "voluptate minim tempor deserunt pariatur", "payor_id": "esse ipsum ea occaecat", "national_association_of_insurance_commissioners_identification": "ut elit ad", "blue_cross_blue_shield_association_plan_code": "laboris mollit eu", "pharmacy_processor_number": "Duis cupidatat nostrud in", "centers_for_medicare_and_medicaid_services_plan_id": "laboris", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "corrected_priority_payer": [{"_meta": {"source": "med_claim_resp_pri_pyr", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "reprehenderit dolor sunt dolor", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "organization_name": "aliqua dolor commodo", "tax_id": "adipisicing est enim", "payor_id": "reprehenderit dolor sunt dolor", "national_association_of_insurance_commissioners_identification": "ad", "blue_cross_blue_shield_association_plan_code": "do id", "pharmacy_processor_number": "sunt", "centers_for_medicare_and_medicaid_services_plan_id": "esse <PERSON> commodo", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "inpatient_adjudication": [{"_meta": {"source": "med_claim_resp_inpt", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "cupidatat dolor consectetur sint aliquip", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "covered_days_or_visits_count": "cupidatat dolor consectetur sint aliquip", "lifetime_psychiatric_days_count": "in aliquip occaecat officia", "cost_report_day_count": "reprehend<PERSON><PERSON>", "claim_drg_amount": 100, "claim_disproportionate_share_amount": 100, "claim_msp_pass_through_amount": 100, "pps_operating_outlier_amount": 100, "claim_pps_capital_amount": 100, "pps_capital_fsp_drg_amount": null, "pps_capital_hsp_drg_amount": null, "pps_capital_dsh_drg_amount": null, "pps_capital_ime_amount": 100, "pps_capital_exception_amount": 100, "pps_operating_hospital_specific_drg_amount": 100, "pps_operating_federal_specific_drg_amount": 100, "claim_pps_capital_outlier_amount": 100, "old_capital_amount": 100, "claim_indirect_teaching_amount": 100, "non_payable_professional_component_amount": 100, "claim_payment_remark_code_1": null, "claim_payment_remark_code_2": null, "claim_payment_remark_code_3": null, "claim_payment_remark_code_4": null, "claim_payment_remark_code_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "outpatient_adjudication": [{"_meta": {"source": "med_claim_resp_outpt", "subform": {}}, "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "0.20", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "reimbursement_rate": "0.20", "claim_hcpcs_payable_amount": 100, "claim_esrd_payment_amount": 100, "non_payable_professional_component_amount": 100, "claim_payment_remark_code_1": null, "claim_payment_remark_code_2": null, "claim_payment_remark_code_3": null, "claim_payment_remark_code_4": null, "claim_payment_remark_code_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "claim_contact_information": [{"_meta": {"source": "med_claim_resp_py_cont", "subform": {"contact_methods": "med_claim_resp_py_cont_m"}}, "contact_methods": [{"_meta": {"source": "med_claim_resp_py_cont_m", "subform": {}}, "id": 39, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "sint ut aliqua aliquip Ut", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "email": "sint ut aliqua aliquip Ut", "fax": "anim irure cillum adipisicing", "phone": "commodo est cillum", "phone_extension": "qui veniam ad consequat", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 20, "parent_form": "med_claim_resp_py_cont", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_py_cont_m", "subform": {}}, "id": 40, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "adipisicing", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "email": "adipisicing", "fax": "Ut consectetur sit qui", "phone": "in", "phone_extension": "id", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 20, "parent_form": "med_claim_resp_py_cont", "parent_archived": null}], "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "sit", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "contact_name": "sit", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "other_claim_related_identification": [{"_meta": {"source": "med_claim_resp_oid", "subform": {}}, "id": 20, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "non occaecat commodo ullamco Duis Excepteur est esse", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "medical_record_identification_number": "non occaecat commodo ullamco <PERSON>is", "ssn": "Excepteur est esse", "insurance_policy_number": "ullamco ut", "group_or_policy_number": "ipsum quis", "group_number": "aliqua anim", "member_identification_number": "Ut nulla minim reprehenderit quis", "employee_identification_number": "non", "re_priced_claim_reference_number": "ex sit esse", "adjusted_re_priced_claim_reference_number": "ipsum ex", "class_of_contract_code": "proident eu est enim", "original_reference_number": "adipisicing consectetur", "prior_authorization_number": "mollit Duis non", "authorization_number": "occaecat consequat", "predetermination_of_benefits_identification_number": "dolor magna", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "claim_supplemental_information": [{"_meta": {"source": "med_claim_resp_sup", "subform": {}}, "id": 12, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "100", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "coverage_amount": 100, "discount_amount": 100, "per_day_limit": 100, "patient_amount_paid": 100, "interest": 5, "negative_ledger_balance": 100, "tax": 100, "total_claim_before_taxes": 100, "federal_medicare_or_medicaid_payment_mandate_category_1": null, "federal_medicare_or_medicaid_payment_mandate_category_2": null, "federal_medicare_or_medicaid_payment_mandate_category_3": null, "federal_medicare_or_medicaid_payment_mandate_category_4": null, "federal_medicare_or_medicaid_payment_mandate_category_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "claim_supplemental_information_quantities": [{"_meta": {"source": "med_claim_resp_sup_qty", "subform": {}}, "id": 12, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "100", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "covered_actual": "100", "co_insured_actual": "100", "non_covered_estimated": "quis deserunt sit", "life_time_reserve_actual": "100", "life_time_reserve_estimated": "in", "not_replaced_blood_units": "Duis do nisi", "outlier_days": "amet magna deserunt", "prescription": "sit Excepteur elit minim", "visits": "nostrud ad incididunt nisi consequat", "federal_medicare_or_medicaid_payment_mandate_category_1": null, "federal_medicare_or_medicaid_payment_mandate_category_2": null, "federal_medicare_or_medicaid_payment_mandate_category_3": null, "federal_medicare_or_medicaid_payment_mandate_category_4": null, "federal_medicare_or_medicaid_payment_mandate_category_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "service_lines": [{"_meta": {"source": "med_claim_resp_sl", "subform": {"service_payment_information": "med_claim_resp_sl_pi", "service_adjustments": "med_claim_resp_sl_adj", "service_identification": "med_claim_resp_svc_id", "rendering_provider_information": "med_claim_resp_sl_rprov", "service_supplemental_amounts": "med_claim_resp_sl_sup", "service_supplemental_quantities": "med_claim_resp_sl_sqty", "health_care_check_remark_codes": "med_claim_resp_sl_hc", "health_care_policy_identification": "med_claim_resp_sl_pcy"}}, "service_payment_information": [{"_meta": {"source": "med_claim_resp_sl_pi", "subform": {}}, "id": 51, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ad dolore in pariatur", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "product_or_service_id_qualifier": "ad", "product_or_service_id_qualifier_value": "mollit Excepteur nulla quis do", "adjudicated_procedure_code": "dolore in pariatur", "submitted_product_or_service_id_qualifier": "id Duis culpa sunt", "submitted_product_or_service_id_qualifier_value": "irure", "submitted_adjudicated_procedure_code": "exercitation", "submitted_procedure_code_description": "mollit", "national_uniform_billing_committee_revenue_code": "incididunt", "original_units_of_service_count": "mollit", "units_of_service_paid_count": "nulla labore", "line_item_charge_amount": 100, "line_item_provider_payment_amount": 100, "proc_modifier_1": null, "proc_modifier_2": null, "proc_modifier_3": null, "proc_modifier_4": null, "adj_modifier_1": null, "adj_modifier_2": null, "adj_modifier_3": null, "adj_modifier_4": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_adjustments": [{"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 94, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "reprehenderit enim non", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "reprehenderit enim non", "claim_adjustment_group_code_value": "velit in cupidatat", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 95, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "et dolor sit", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "et dolor sit", "claim_adjustment_group_code_value": "sit voluptate cillum veniam", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 96, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "incididunt cupidatat sed sint", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "incididunt cupidatat sed sint", "claim_adjustment_group_code_value": "consequat nostrud in anim", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 97, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "amet", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "amet", "claim_adjustment_group_code_value": "enim occaecat Duis aute tempor", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_identification": [{"_meta": {"source": "med_claim_resp_svc_id", "subform": {}}, "id": 51, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "ambulatory_patient_group_number": "e<PERSON><PERSON><PERSON> aute", "ambulatory_payment_classification": "culpa", "attachment_code": "ut non labore", "prior_authorization_number": "in enim et", "authorization_number": "labore aute", "pre_determination_of_benefits_identification_number": "fugiat adipisicing exercitation sint", "location_number": "est qui enim officia", "rate_code_number": "qui adipisicing", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "rendering_provider_information": [{"_meta": {"source": "med_claim_resp_sl_rprov", "subform": {}}, "id": 14, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ullamco ut ea", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "state_license_number": "ullamco ut ea", "blue_cross_provider_number": "id", "blue_shield_provider_number": "commodo", "medicare_provider_number": "consectetur", "provider_upin_number": "qui proident tempor in consequat", "champus_identification_number": "labore aliquip irure quis sit", "national_council_for_prescription_drug_program_pharmacy_number": "sit", "provider_commercial_number": "do eu voluptate", "facility_id_number": "culpa do dolore", "location_number": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_supplemental_amounts": [{"_meta": {"source": "med_claim_resp_sl_sup", "subform": {}}, "id": 50, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "100 100", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "allowed_actual": 100, "deduction_amount": 100, "tax": 100, "total_claim_before_taxes": 100, "federal_medicare_or_medicaid_payment_mandate_category_1": null, "federal_medicare_or_medicaid_payment_mandate_category_2": null, "federal_medicare_or_medicaid_payment_mandate_category_3": null, "federal_medicare_or_medicaid_payment_mandate_category_4": null, "federal_medicare_or_medicaid_payment_mandate_category_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_supplemental_quantities": [{"_meta": {"source": "med_claim_resp_sl_sqty", "subform": {}}, "id": 13, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "federal_medicare_or_medicaid_payment_mandate_category_1": null, "federal_medicare_or_medicaid_payment_mandate_category_2": null, "federal_medicare_or_medicaid_payment_mandate_category_3": null, "federal_medicare_or_medicaid_payment_mandate_category_4": null, "federal_medicare_or_medicaid_payment_mandate_category_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "health_care_check_remark_codes": [{"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 55, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "dolor consequat Excepteur ullam<PERSON> pariatur", "code_list_qualifier_code_value": "minim", "remark_code": "sit elit", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 56, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "veniam mollit voluptate", "code_list_qualifier_code_value": "in laboris exercitation minim aliqua", "remark_code": "sunt", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 57, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "sint aute et", "code_list_qualifier_code_value": "ex", "remark_code": "reprehenderit ea veniam", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 58, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "dolor ullamco commodo reprehenderit", "code_list_qualifier_code_value": "nisi nulla in velit", "remark_code": "nostrud anim et in", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 59, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "nisi in aute sed", "code_list_qualifier_code_value": "dolor deserunt", "remark_code": "ea nostrud labore sit voluptate", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "health_care_policy_identification": [{"_meta": {"source": "med_claim_resp_sl_pcy", "subform": {}}, "id": 31, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "policy_form_identifying_number": "ad", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_pcy", "subform": {}}, "id": 32, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "policy_form_identifying_number": "ut ex proident", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_pcy", "subform": {}}, "id": 33, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "policy_form_identifying_number": "cillum officia incididunt culpa", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 55, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "id": 55, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ullamco", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "line_item_control_number": "ullamco", "service_line_date": null, "begin_service_line_date": null, "end_service_line_date": null, "service_id_qualifier_code": null, "service_id_qualifier_code_value": null, "procedure_code": null, "procedure_modifiers": null, "charge_amount": null, "amount_paid": null, "submitted_units": null, "revenue_code": null, "service_date": "12/10/2024", "service_start_date": "12/10/2024", "service_end_date": "12/10/2024", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl", "subform": {"service_payment_information": "med_claim_resp_sl_pi", "service_adjustments": "med_claim_resp_sl_adj", "service_identification": "med_claim_resp_svc_id", "rendering_provider_information": "med_claim_resp_sl_rprov", "service_supplemental_amounts": "med_claim_resp_sl_sup", "service_supplemental_quantities": "med_claim_resp_sl_sqty", "health_care_check_remark_codes": "med_claim_resp_sl_hc", "health_care_policy_identification": "med_claim_resp_sl_pcy"}}, "service_payment_information": [{"_meta": {"source": "med_claim_resp_sl_pi", "subform": {}}, "id": 52, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ad eiusmod proident Ut elit cupidatat do", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "product_or_service_id_qualifier": "ad eiusmod", "product_or_service_id_qualifier_value": "quis mollit in in elit", "adjudicated_procedure_code": "proident Ut elit cupidatat do", "submitted_product_or_service_id_qualifier": "ea sed", "submitted_product_or_service_id_qualifier_value": "ea", "submitted_adjudicated_procedure_code": "qui tempor <PERSON><PERSON> sit", "submitted_procedure_code_description": "sint", "national_uniform_billing_committee_revenue_code": "dolor occaecat ut et dolor", "original_units_of_service_count": "nisi", "units_of_service_paid_count": "ea", "line_item_charge_amount": 100, "line_item_provider_payment_amount": 100, "proc_modifier_1": null, "proc_modifier_2": null, "proc_modifier_3": null, "proc_modifier_4": null, "adj_modifier_1": null, "adj_modifier_2": null, "adj_modifier_3": null, "adj_modifier_4": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_adjustments": [{"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 98, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "in aute", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "in aute", "claim_adjustment_group_code_value": "elit sunt", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 99, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "mollit", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "mollit", "claim_adjustment_group_code_value": "sit fugiat nisi exercitation cupidatat", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 100, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ul<PERSON><PERSON> sunt", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "ul<PERSON><PERSON> sunt", "claim_adjustment_group_code_value": "dolor exercitation quis", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_adj", "subform": {}}, "id": 101, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "elit amet proident in nisi", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_adjustment_group_code": "elit amet proident in nisi", "claim_adjustment_group_code_value": "dolor esse consequat", "adjustment_reason_code_1": null, "adjustment_amount_1": null, "adjustment_quantity_1": null, "adjustment_reason_code_2": null, "adjustment_amount_2": null, "adjustment_quantity_2": null, "adjustment_reason_code_3": null, "adjustment_amount_3": null, "adjustment_quantity_3": null, "adjustment_reason_code_4": null, "adjustment_amount_4": null, "adjustment_quantity_4": null, "adjustment_reason_code_5": null, "adjustment_amount_5": null, "adjustment_quantity_5": null, "adjustment_reason_code_6": null, "adjustment_amount_6": null, "adjustment_quantity_6": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_identification": [{"_meta": {"source": "med_claim_resp_svc_id", "subform": {}}, "id": 52, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "ambulatory_patient_group_number": "in proident incididunt eiusmod elit", "ambulatory_payment_classification": "aute", "attachment_code": "pariatur eu enim sit", "prior_authorization_number": "qui magna exercitation quis", "authorization_number": "officia dolor", "pre_determination_of_benefits_identification_number": "veniam", "location_number": "consectetur in dolore", "rate_code_number": "cupidatat", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "rendering_provider_information": [{"_meta": {"source": "med_claim_resp_sl_rprov", "subform": {}}, "id": 15, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "et", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "state_license_number": "et", "blue_cross_provider_number": "deserunt", "blue_shield_provider_number": "ipsum dolor ullamco in Lorem", "medicare_provider_number": "velit in Lorem quis in", "provider_upin_number": "mollit velit elit magna minim", "champus_identification_number": "ullamco est ut aute ut", "national_council_for_prescription_drug_program_pharmacy_number": "no<PERSON><PERSON> laboris", "provider_commercial_number": "et reprehenderit sunt laboris ea", "facility_id_number": "in ad ut Ut cupidatat", "location_number": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_supplemental_amounts": [{"_meta": {"source": "med_claim_resp_sl_sup", "subform": {}}, "id": 51, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "100 100", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "allowed_actual": 100, "deduction_amount": 100, "tax": 100, "total_claim_before_taxes": 100, "federal_medicare_or_medicaid_payment_mandate_category_1": null, "federal_medicare_or_medicaid_payment_mandate_category_2": null, "federal_medicare_or_medicaid_payment_mandate_category_3": null, "federal_medicare_or_medicaid_payment_mandate_category_4": null, "federal_medicare_or_medicaid_payment_mandate_category_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "service_supplemental_quantities": [{"_meta": {"source": "med_claim_resp_sl_sqty", "subform": {}}, "id": 14, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "federal_medicare_or_medicaid_payment_mandate_category_1": null, "federal_medicare_or_medicaid_payment_mandate_category_2": null, "federal_medicare_or_medicaid_payment_mandate_category_3": null, "federal_medicare_or_medicaid_payment_mandate_category_4": null, "federal_medicare_or_medicaid_payment_mandate_category_5": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "health_care_check_remark_codes": [{"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 60, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "sunt veniam ullamco velit", "code_list_qualifier_code_value": "dolore", "remark_code": "anim", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 61, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "tempor", "code_list_qualifier_code_value": "ex sit enim ipsum", "remark_code": "consequat", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 62, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "e<PERSON><PERSON><PERSON> sint", "code_list_qualifier_code_value": "non mollit laborum nisi", "remark_code": "<PERSON><PERSON> consequat consectetur", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_hc", "subform": {}}, "id": 63, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "code_list_qualifier_code": "eu consequat nisi nulla commodo", "code_list_qualifier_code_value": "veniam nulla et", "remark_code": "sint consequat aute incididunt", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "health_care_policy_identification": [{"_meta": {"source": "med_claim_resp_sl_pcy", "subform": {}}, "id": 34, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "policy_form_identifying_number": "sit dolor dolor velit", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}, {"_meta": {"source": "med_claim_resp_sl_pcy", "subform": {}}, "id": 35, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON> (Clara Team)", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "policy_form_identifying_number": "ut labore dolore amet cillum", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 56, "parent_form": "med_claim_resp_sl", "parent_archived": null}], "id": 56, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "sunt sed et", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "line_item_control_number": "sunt sed et", "service_line_date": null, "begin_service_line_date": null, "end_service_line_date": null, "service_id_qualifier_code": null, "service_id_qualifier_code_value": null, "procedure_code": null, "procedure_modifiers": null, "charge_amount": null, "amount_paid": null, "submitted_units": null, "revenue_code": null, "service_date": "12/10/2024", "service_start_date": "12/10/2024", "service_end_date": "12/10/2024", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_py_info", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "2024-12-10", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "claim_statement_period_start": "12/10/2024", "claim_statement_period_end": "12/10/2024", "coverage_expiration_date": "12/10/2024", "claim_received_date": "12/10/2024", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_dt_info", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "aliqua", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "assigned_number": "aliqua", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tran", "parent_archived": null}], "provider_adjustments": [{"_meta": {"source": "med_claim_resp_prov_adjs", "subform": {"adjustment": "med_claim_resp_prov_adj"}}, "adjustment": [], "id": 7, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "in officia adipisicing anim", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "provider_identifier": "in officia adipisicing anim", "fiscal_period_date": "12/10/2024", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_tran", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "434343434343", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "control_number": "434343434343", "receiver_identifier": "exercitation", "version_identifier": null, "production_date": "12/10/2024", "foreign_currency": "cillum dolore ex officia", "total_claim_charge_amount": null, "claim_payment_amount": null, "patient_responsibility_amount": null, "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_835", "parent_archived": null}], "meta": [{"_meta": {"source": "med_claim_resp_meta", "subform": {}}, "id": 73, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "eu adipisicing sint eu minim enim id reprehenderit nulla", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "sender_id": "eu adipisicing sint", "trace_id": "eu minim enim id", "application_mode": "reprehenderit nulla", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 54, "parent_form": "med_claim_resp_835", "parent_archived": null}], "id": 54, "created_by": 4, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "100", "change_type": null, "change_data": null, "created_on": "12/10/2024 18:14:56", "change_on": null, "reviewed": null, "notes": null, "total_claim_charge_amount": null, "claim_payment_amount": null, "patient_responsibility_amount": null, "raw": null, "request_raw_d0": null, "request_raw_json": null, "response_raw_d0": null, "response_raw_json": {"transactions": [{"control_number": "434343434343", "payment_and_remit_reassociation_details": [{"trace_type_code": "enim", "check_or_eft_trace_number": "exercitation nostrud consectetur esse adipisicing", "originating_company_identifier": "cillum quis ex Lorem nulla", "originating_company_supplemental_code": "esse nostrud proident"}], "foreign_currency": "cillum dolore ex officia", "production_date": "12/10/2024", "receiver_identifier": "exercitation", "version_identification": "ipsum occaecat", "financial_information": [{"transaction_handling_code": "sit", "total_actual_provider_payment_amount": 100, "credit_or_debit_flag_code": "et in in pariatur sint", "payment_method_code": "nisi occaecat fugiat", "payment_format_code": "magna sit Lorem amet officia", "payer_identifier": "sint ex officia", "originating_company_supplemental_code": "minim elit irure sunt", "check_issue_or_eft_effective_date": "12/10/2024", "sender_account_details": [{"sender_dfi_id_number_qualifier": "et esse Duis id", "sender_dfi_identifier": "in Ut", "sender_account_number_qualifier": "id aliqua mollit ut", "sender_account_number": "consequat sunt"}], "receiver_account_details": [{"receiver_dfi_id_number_qualifier": "anim non est", "receiver_dfi_identification_number": "consectetur ex eu dolore nisi", "receiver_account_number_qualifier": "pariatur sunt id", "receiver_account_number": "do velit exercitation"}]}], "payer": [{"name": "pariatur dolore dolore Ut", "centers_for_medicare_and_medicaid_services_plan_id": "ut consectetur deserunt amet commodo", "payer_identification_number": "aliqua ut commodo exercitation elit", "submitter_identification_number": "quis", "health_industry_number": "ut consequat qui tempor incididunt", "national_association_of_insurance_commissioners": "ad irure officia", "payer_web_site_url": "occaecat dolore nisi", "address": [{"address1": "consequat voluptate", "address2": "commodo mollit sed in in", "city": "Excepteur", "state": "in", "postal_code": "tempor laboris <PERSON><PERSON> dolore", "country_code": "qui", "country_sub_code": "aliquip aliqua"}], "business_contact_information": [{"contact_name": "dolore", "contact_methods": [{"email": "ipsum", "fax": "velit labore anim", "phone": "qui ex consequat ipsum", "phone_extension": "sunt aliqua cupidatat ut laborum"}, {"email": "occaecat irure ullamco aliquip incididunt", "fax": "culpa irure sint ut", "phone": "qui Ut consequat", "phone_extension": "dolore"}, {"email": "dolor consequat consectetur nulla voluptate", "fax": "reprehend<PERSON><PERSON>", "phone": "ad ut mollit", "phone_extension": "fugiat nulla Ut"}, {"email": "deserunt Excepteur sed ut", "fax": "nostrud laborum sint Duis", "phone": "qui velit ipsum aute fugiat", "phone_extension": "ex cupidatat"}]}], "technical_contact_information": [{"contact_name": "quis", "contact_methods": [{"url": "amet aute do Excepteur sit", "email": "in amet minim deserunt", "fax": "exercitation", "phone": "dolor", "phone_extension": "amet cillum Duis dolor"}, {"url": "in consequat mollit", "email": "exercitation in id pariatur", "fax": "magna aliquip dolor in", "phone": "sed occaecat commodo", "phone_extension": "veniam aute nulla occaecat mollit"}, {"url": "non consequat", "email": "sed Excepteur ut", "fax": "non deserunt aute", "phone": "dolore magna in velit", "phone_extension": "sint"}, {"url": "qui cillum ad", "email": "Lorem adipisicing occaecat elit", "fax": "ea sint fugiat in non", "phone": "cillum non in", "phone_extension": "minim aute"}]}]}], "payee": [{"name": "ullamco", "tax_id": "qui", "centers_for_medicare_and_medicaid_services_plan_id": "cupidatat exercitation est et officia", "npi": "dolore commodo dolor", "address": [{"address1": "nisi elit dolore", "address2": "quis", "city": "non cillum", "state": "aliqua", "postal_code": "est irure ea incididunt culpa", "country_code": "nisi sint ut sunt et", "country_sub_code": "officia"}], "state_license_number": "in ipsum anim in in", "national_council_for_prescription_drug_programs_pharmacy_number": "pariatur officia aute eu", "payee_identification": "mollit Excepteur nisi voluptate", "federal_tax_payers_identification_number": "labore ipsum", "remittance_delivery_method": [{"name": "ad do", "email": "ut", "ftp": "aute culpa do <PERSON>eur", "on_line": "qui commodo fugiat"}]}], "detail_info": [{"assigned_number": "aliqua", "provider_summary_information": [{"provider_identifier": "incididunt", "facility_type_code": "eiusmod non", "fiscal_period_date": "12/10/2024", "total_claim_count": "elit aute non", "total_claim_charge_amount": 100, "total_msp_payer_amount": 100, "total_non_lab_charge_amount": 100, "total_hcpcs_reported_charge_amount": 100, "total_hcpcs_payable_amount": 100, "total_professional_component_amount": 100, "total_msp_patient_liability_met_amount": 100, "total_patient_reimbursement_amount": 100, "total_pip_claim_count": "incididunt dolore culpa tempor", "total_pip_adjustment_amount": 100}], "provider_supplemental_summary_information": [{"total_drg_amount": 100, "total_federal_specific_amount": 100, "total_hospital_specific_amount": 100, "total_disproportionate_share_amount": 100, "total_capital_amount": 100, "total_indirect_medical_education_amount": 100, "total_outlier_day_count": "sunt", "total_day_outlier_amount": 100, "total_cost_outlier_amount": 100, "average_drg_length_of_stay": "ea mollit", "total_discharge_count": "culpa voluptate officia adipisicing", "total_cost_report_day_count": "in occaecat in", "total_covered_day_count": "in Ut mollit Duis", "total_non_covered_day_count": "magna id", "total_msp_pass_through_amount": 100, "average_drg_weight": "Ut", "total_pps_capital_fspdrg_amount": 100, "total_pps_capital_hspdrg_amount": 100, "total_ppsdshdrg_amount": 100}], "payment_info": [{"claim_statement_period_start": "12/10/2024", "claim_statement_period_end": "12/10/2024", "coverage_expiration_date": "12/10/2024", "claim_received_date": "12/10/2024", "claim_payment_info": [{"patient_control_number": "<PERSON>is sunt elit", "claim_status_code": "3", "total_claim_charge_amount": 100, "claim_payment_amount": 100, "patient_responsibility_amount": 100, "claim_filing_indicator_code": "consequat", "payer_claim_control_number": "pariatur ut do elit esse", "facility_type_code": "sunt", "claim_frequency_code": "sit est", "diagnosis_related_group_drg_code": "voluptate sed aute", "diagnosis_related_group_drg_weight": "id reprehenderit", "discharge_fraction": "tempor et sit commodo deserunt"}], "claim_adjustments": [{"claim_adjustment_group_code": "officia ea cupidatat mollit ullamco", "claim_adjustment_group_code_value": "dolore cillum ad sit et", "adjustment_reason_code1": "velit ea proident esse quis", "adjustment_amount1": 100, "adjustment_quantity1": "Ut minim culpa qui laboris", "adjustment_reason_code2": "Excepteur", "adjustment_amount2": 100, "adjustment_quantity2": "in velit exercitation <PERSON><PERSON>", "adjustment_reason_code3": "dolore ea voluptate nisi esse", "adjustment_amount3": 100, "adjustment_quantity3": "cupidatat esse ea exercitation", "adjustment_reason_code4": "ad", "adjustment_amount4": 100, "adjustment_quantity4": "irure", "adjustment_reason_code5": "Excepteur officia", "adjustment_amount5": 100, "adjustment_quantity5": "consectetur adipisicing ut", "adjustment_reason_code6": "est eu", "adjustment_amount6": 100, "adjustment_quantity6": "exercitation incididunt ipsum officia mollit"}, {"claim_adjustment_group_code": "id occaecat", "claim_adjustment_group_code_value": "magna et aliquip", "adjustment_reason_code1": "labore", "adjustment_amount1": 100, "adjustment_quantity1": "elit aliqua ea Duis sunt", "adjustment_reason_code2": "dolor", "adjustment_amount2": 100, "adjustment_quantity2": "nulla incididunt ea", "adjustment_reason_code3": "consequat cupidatat enim", "adjustment_amount3": 100, "adjustment_quantity3": "nisi cillum aute qui enim", "adjustment_reason_code4": "enim dolor nulla", "adjustment_amount4": 100, "adjustment_quantity4": "Lorem tempor", "adjustment_reason_code5": "id", "adjustment_amount5": 100, "adjustment_quantity5": "ad in velit", "adjustment_reason_code6": "sed dolore sint est ipsum", "adjustment_amount6": 100, "adjustment_quantity6": "Duis dolor tempor"}], "patient_name": [{"last_name": "non officia culpa tempor", "first_name": "magna", "middle_name": "ipsum", "suffix": "id enim ut amet", "ssn": "<PERSON>rem ullam<PERSON>is voluptate", "health_insurance_claim_number": "officia incididunt velit", "standard_unique_health_identifier_for_each_individual_in_the_united_states": "in sed", "member_id": "voluptate officia tempor velit minim", "medicaid_recipient_identification_number": "laborum esse Ut"}], "subscriber": [{"organization_name": "sed et", "last_name": "magna ut", "first_name": "in fugiat mollit", "middle_name": "ex deserunt incididunt", "suffix": "proident aute id anim exercitation", "tax_id": "ut aute", "standard_unique_health_identifier_for_each_individual_in_the_united_states": "aliquip nostrud dolore", "member_id": "tempor aute consectetur cupidatat ex"}], "corrected_patient_or_insured_name": [{"organization_name": "nisi ad veniam", "last_name": "no<PERSON><PERSON> sunt", "first_name": "laborum dolor Excepteur tempor", "middle_name": "esse ad anim", "suffix": "sunt labore laborum in", "insureds_changed_unique_identification_number": "aliquip commodo est"}], "rendering_provider": [{"organization_name": "mollit ad", "last_name": "in", "first_name": "dolor nisi aliquip", "middle_name": "consequat laborum Lorem exercitation", "suffix": "laborum eiusmod anim ad", "blue_cross_provider_number": "magna", "blue_shield_provider_number": "in sit aliquip", "tax_id": "qui occaecat dolor", "medicaid_provider_number": "ea", "provider_commercial_number": "deserunt esse in", "state_license_number": "reprehend<PERSON><PERSON>", "unique_physician_identification_number": "fugiat dolor voluptate", "npi": "ex ut Duis consequat"}], "crossover_carrier": [{"organization_name": "sunt e<PERSON><PERSON>d nost<PERSON>", "blue_cross_blue_shield_association_plan_code": "laboris mollit eu", "tax_id": "voluptate minim tempor deserunt pariatur", "national_association_of_insurance_commissioners_identification": "ut elit ad", "payor_id": "esse ipsum ea occaecat", "pharmacy_processor_number": "Duis cupidatat nostrud in", "centers_for_medicare_and_medicaid_services_plan_id": "laboris"}], "corrected_priority_payer": [{"organization_name": "aliqua dolor commodo", "blue_cross_blue_shield_association_plan_code": "do id", "tax_id": "adipisicing est enim", "national_association_of_insurance_commissioners_identification": "ad", "payor_id": "reprehenderit dolor sunt dolor", "pharmacy_processor_number": "sunt", "centers_for_medicare_and_medicaid_services_plan_id": "esse <PERSON> commodo"}], "other_subscriber": [{"organization_name": "incididunt ea irure dolore consectetur", "last_name": "dolor <PERSON> in", "first_name": "dolore sit quis mollit consectetur", "middle_name": "ad", "suffix": "amet aliquip <PERSON>rem magna aute", "tax_id": "sint Duis", "standard_unique_health_identifier_for_each_individual_in_the_united_states": "commodo", "member_id": "ipsum est reprehenderit"}], "inpatient_adjudication": [{"covered_days_or_visits_count": "cupidatat dolor consectetur sint aliquip", "pps_operating_outlier_amount": 100, "lifetime_psychiatric_days_count": "in aliquip occaecat officia", "claim_drg_amount": 100, "claim_payment_remark_code1": "mollit ut magna", "claim_disproportionate_share_amount": 100, "claim_msp_pass_through_amount": 100, "claim_pps_capital_amount": 100, "pps_capital_fspdrg_amount": 100, "pps_capital_hspdrg_amount": 100, "pps_capital_dshdrg_amount": 100, "old_capital_amount": 100, "pps_capital_ime_amount": 100, "pps_operating_hospital_specific_drg_amount": 100, "cost_report_day_count": "reprehend<PERSON><PERSON>", "pps_operating_federal_specific_drg_amount": 100, "claim_pps_capital_outlier_amount": 100, "claim_indirect_teaching_amount": 100, "non_payable_professional_component_amount": 100, "claim_payment_remark_code2": "voluptate occaecat quis magna", "claim_payment_remark_code3": "id dolore consectetur proident eu", "claim_payment_remark_code4": "occaecat incididunt", "claim_payment_remark_code5": "amet minim do", "pps_capital_exception_amount": 100}], "outpatient_adjudication": [{"reimbursement_rate": "0.20", "claim_hcpcs_payable_amount": 100, "claim_payment_remark_code1": "irure tempor pariatur laborum ex", "claim_payment_remark_code2": "id pariatur aliquip anim", "claim_payment_remark_code3": "quis", "claim_payment_remark_code4": "commodo", "claim_payment_remark_code5": "ut Ut", "claim_esrd_payment_amount": 100, "non_payable_professional_component_amount": 100}], "claim_contact_information": [{"contact_name": "sit", "contact_methods": [{"email": "sint ut aliqua aliquip Ut", "fax": "anim irure cillum adipisicing", "phone": "commodo est cillum", "phone_extension": "qui veniam ad consequat"}, {"email": "adipisicing", "fax": "Ut consectetur sit qui", "phone": "in", "phone_extension": "id"}]}], "other_claim_related_identification": [{"group_or_policy_number": "ipsum quis", "member_identification_number": "Ut nulla minim reprehenderit quis", "employee_identification_number": "non", "group_number": "aliqua anim", "re_priced_claim_reference_number": "ex sit esse", "adjusted_re_priced_claim_reference_number": "ipsum ex", "authorization_number": "occaecat consequat", "class_of_contract_code": "proident eu est enim", "medical_record_identification_number": "non occaecat commodo ullamco <PERSON>is", "original_reference_number": "adipisicing consectetur", "prior_authorization_number": "mollit Duis non", "predetermination_of_benefits_identification_number": "dolor magna", "insurance_policy_number": "ullamco ut", "ssn": "Excepteur est esse"}], "rendering_provider_identification": [{"state_license_number": "in", "blue_cross_provider_number": "aliquip sit", "blue_shield_provider_number": "in", "medicare_provider_number": "voluptate aute ex amet cupidatat", "medicaid_provider_number": "sed ul<PERSON>co", "provider_upin_number": "sint aliquip amet tempor", "champus_identification_number": "dolor elit adipisicing", "facility_id_number": "anim fugiat est enim do", "national_council_for_prescription_drug_program_pharmacy_number": "commodo sint", "provider_commercial_number": "in ipsum aute labore anim", "location_number": "labore nulla Excepteur dolore"}], "claim_supplemental_information": [{"coverage_amount": 100, "discount_amount": 100, "per_day_limit": 100, "patient_amount_paid": 100, "interest": "5.00", "negative_ledger_balance": 100, "tax": 100, "total_claim_before_taxes": 100, "federal_medicare_or_medicaid_payment_mandate_category1": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category2": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category3": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category4": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category5": "12/10/2024"}], "claim_supplemental_information_quantities": [{"covered_actual": 100, "co_insured_actual": 100, "life_time_reserve_actual": 100, "life_time_reserve_estimated": "in", "non_covered_estimated": "quis deserunt sit", "not_replaced_blood_units": "Duis do nisi", "outlier_days": "amet magna deserunt", "prescription": "sit Excepteur elit minim", "visits": "nostrud ad incididunt nisi consequat", "federal_medicare_or_medicaid_payment_mandate_category1": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category2": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category3": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category4": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category5": "12/10/2024"}], "service_lines": [{"service_date": "12/10/2024", "service_start_date": "12/10/2024", "service_end_date": "12/10/2024", "line_item_control_number": "ullamco", "service_payment_information": [{"product_or_service_id_qualifier": "ad", "product_or_service_id_qualifier_value": "mollit Excepteur nulla quis do", "adjudicated_procedure_code": "dolore in pariatur", "adjudicated_procedure_modifier_codes": ["laborum sit id", "dolor", "aute exercitation do consectetur", "dolor ipsum magna laborum commodo"], "line_item_charge_amount": 100, "line_item_provider_payment_amount": 100, "national_uniform_billing_committee_revenue_code": "incididunt", "units_of_service_paid_count": "nulla labore", "submitted_product_or_service_id_qualifier": "id Duis culpa sunt", "submitted_product_or_service_id_qualifier_value": "irure", "submitted_adjudicated_procedure_code": "exercitation", "submitted_adjudicated_procedure_modifier_codes": ["in nulla officia", "mollit fugiat cillum eiusmod consequat", "consequat exercitation", "ut magna dolor veniam id", "cupidatat minim Duis amet"], "submitted_procedure_code_description": "mollit", "original_units_of_service_count": "mollit"}], "service_adjustments": [{"claim_adjustment_group_code": "reprehenderit enim non", "claim_adjustment_group_code_value": "velit in cupidatat", "adjustment_reason_code1": "laborum id commodo esse", "adjustment_amount1": 100, "adjustment_quantity1": "ut veniam aliqua deserunt", "adjustment_reason_code2": "aliqua eu", "adjustment_amount2": 100, "adjustment_quantity2": "voluptate labore", "adjustment_reason_code3": "sit cillum commodo ipsum aliqua", "adjustment_amount3": 100, "adjustment_quantity3": "id laboris qui nisi", "adjustment_reason_code4": "laborum", "adjustment_amount4": 100, "adjustment_quantity4": "Duis ea amet", "adjustment_reason_code5": "Ut anim dolor", "adjustment_amount5": 100, "adjustment_quantity5": "eu minim anim enim <PERSON>eur", "adjustment_reason_code6": "sed", "adjustment_amount6": 100, "adjustment_quantity6": "aute"}, {"claim_adjustment_group_code": "et dolor sit", "claim_adjustment_group_code_value": "sit voluptate cillum veniam", "adjustment_reason_code1": "enim", "adjustment_amount1": 100, "adjustment_quantity1": "sint aliquip incididunt", "adjustment_reason_code2": "dolore <PERSON>", "adjustment_amount2": 100, "adjustment_quantity2": "ea Excepteur consequat laboris elit", "adjustment_reason_code3": "laboris", "adjustment_amount3": 100, "adjustment_quantity3": "amet ad Excepteur deserunt dolore", "adjustment_reason_code4": "elit", "adjustment_amount4": 100, "adjustment_quantity4": "ut proident", "adjustment_reason_code5": "officia ut quis sunt", "adjustment_amount5": 100, "adjustment_quantity5": "consequat <PERSON>", "adjustment_reason_code6": "eius<PERSON>d incididunt cupidatat", "adjustment_amount6": 100, "adjustment_quantity6": "deserunt cillum do incididunt"}, {"claim_adjustment_group_code": "incididunt cupidatat sed sint", "claim_adjustment_group_code_value": "consequat nostrud in anim", "adjustment_reason_code1": "aute dolore", "adjustment_amount1": 100, "adjustment_quantity1": "adipisicing dolor proident minim consectetur", "adjustment_reason_code2": "tempor quis", "adjustment_amount2": 100, "adjustment_quantity2": "voluptate veniam", "adjustment_reason_code3": "ut eiusmod irure anim", "adjustment_amount3": 100, "adjustment_quantity3": "ea aliquip id ut", "adjustment_reason_code4": "voluptate nisi dolore nostrud", "adjustment_amount4": 100, "adjustment_quantity4": "in", "adjustment_reason_code5": "labore laborum esse", "adjustment_amount5": 100, "adjustment_quantity5": "commodo", "adjustment_reason_code6": "voluptate tempor do dolor", "adjustment_amount6": 100, "adjustment_quantity6": "<PERSON>eur sint"}, {"claim_adjustment_group_code": "amet", "claim_adjustment_group_code_value": "enim occaecat Duis aute tempor", "adjustment_reason_code1": "ea voluptate nostrud cupidatat Lorem", "adjustment_amount1": 100, "adjustment_quantity1": "exercitation", "adjustment_reason_code2": "esse dolor ullamco exercitation", "adjustment_amount2": 100, "adjustment_quantity2": "qui in", "adjustment_reason_code3": "quis tempor dolore ut", "adjustment_amount3": 100, "adjustment_quantity3": "sit dolor esse qui", "adjustment_reason_code4": "sit enim", "adjustment_amount4": 100, "adjustment_quantity4": "<PERSON><PERSON><PERSON><PERSON>", "adjustment_reason_code5": "magna incididunt", "adjustment_amount5": 100, "adjustment_quantity5": "sit voluptate", "adjustment_reason_code6": "ut", "adjustment_amount6": 100, "adjustment_quantity6": "nisi"}], "service_identification": [{"ambulatory_patient_group_number": "e<PERSON><PERSON><PERSON> aute", "ambulatory_payment_classification": "culpa", "authorization_number": "labore aute", "attachment_code": "ut non labore", "prior_authorization_number": "in enim et", "pre_determination_of_benefits_identification_number": "fugiat adipisicing exercitation sint", "location_number": "est qui enim officia", "rate_code_number": "qui adipisicing"}], "rendering_provider_information": [{"state_license_number": "ullamco ut ea", "blue_cross_provider_number": "id", "blue_shield_provider_number": "commodo", "medicare_provider_number": "consectetur", "medicaid_provider_number": "dolore velit in dolor Duis", "provider_upin_number": "qui proident tempor in consequat", "champus_identification_number": "labore aliquip irure quis sit", "facility_id_number": "culpa do dolore", "national_council_for_prescription_drug_program_pharmacy_number": "sit", "provider_commercial_number": "do eu voluptate", "npi": "exercitation", "ssn": "velit", "federal_taxpayer_identification_number": "magna anim non voluptate ullamco"}], "service_supplemental_amounts": [{"allowed_actual": 100, "deduction_amount": 100, "tax": 100, "total_claim_before_taxes": 100, "federal_medicare_or_medicaid_payment_mandate_category1": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category2": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category3": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category4": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category5": "12/10/2024"}], "service_supplemental_quantities": [{"federal_medicare_or_medicaid_payment_mandate_category1": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category2": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category3": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category4": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category5": "12/10/2024"}], "health_care_check_remark_codes": [{"code_list_qualifier_code": "dolor consequat Excepteur ullam<PERSON> pariatur", "code_list_qualifier_code_value": "minim", "remark_code": "sit elit"}, {"code_list_qualifier_code": "veniam mollit voluptate", "code_list_qualifier_code_value": "in laboris exercitation minim aliqua", "remark_code": "sunt"}, {"code_list_qualifier_code": "sint aute et", "code_list_qualifier_code_value": "ex", "remark_code": "reprehenderit ea veniam"}, {"code_list_qualifier_code": "dolor ullamco commodo reprehenderit", "code_list_qualifier_code_value": "nisi nulla in velit", "remark_code": "nostrud anim et in"}, {"code_list_qualifier_code": "nisi in aute sed", "code_list_qualifier_code_value": "dolor deserunt", "remark_code": "ea nostrud labore sit voluptate"}], "health_care_policy_identification": [{"pariatur_9de": false, "do_d": -74910417.27541825, "policy_form_identifying_number": "ad"}, {"ut2": "e<PERSON><PERSON><PERSON> sunt Excepteur elit", "policy_form_identifying_number": "ut ex proident"}, {"policy_form_identifying_number": "cillum officia incididunt culpa"}]}, {"service_date": "12/10/2024", "service_start_date": "12/10/2024", "service_end_date": "12/10/2024", "line_item_control_number": "sunt sed et", "service_payment_information": [{"product_or_service_id_qualifier": "ad eiusmod", "product_or_service_id_qualifier_value": "quis mollit in in elit", "adjudicated_procedure_code": "proident Ut elit cupidatat do", "adjudicated_procedure_modifier_codes": ["et sint", "mollit eiusmod minim", "aliqua o<PERSON>", "incididunt dolor amet commodo", "mollit ad enim dolore in"], "line_item_charge_amount": 100, "line_item_provider_payment_amount": 100, "national_uniform_billing_committee_revenue_code": "dolor occaecat ut et dolor", "units_of_service_paid_count": "ea", "submitted_product_or_service_id_qualifier": "ea sed", "submitted_product_or_service_id_qualifier_value": "ea", "submitted_adjudicated_procedure_code": "qui tempor <PERSON><PERSON> sit", "submitted_adjudicated_procedure_modifier_codes": ["mollit nisi consectetur culpa reprehenderit"], "submitted_procedure_code_description": "sint", "original_units_of_service_count": "nisi"}], "service_adjustments": [{"claim_adjustment_group_code": "in aute", "claim_adjustment_group_code_value": "elit sunt", "adjustment_reason_code1": "magna nisi", "adjustment_amount1": 100, "adjustment_quantity1": "in", "adjustment_reason_code2": "Excepteur", "adjustment_amount2": 100, "adjustment_quantity2": "enim elit Duis consequat", "adjustment_reason_code3": "nisi", "adjustment_amount3": 100, "adjustment_quantity3": "minim nisi Excepteur occaecat exercitation", "adjustment_reason_code4": "<PERSON><PERSON> o<PERSON>", "adjustment_amount4": 100, "adjustment_quantity4": "officia veniam aliqua", "adjustment_reason_code5": "in et cupidatat", "adjustment_amount5": 100, "adjustment_quantity5": "mollit incididunt in ullamco", "adjustment_reason_code6": "laborum commodo laboris pariatur et", "adjustment_amount6": 100, "adjustment_quantity6": "dolor laboris"}, {"claim_adjustment_group_code": "mollit", "claim_adjustment_group_code_value": "sit fugiat nisi exercitation cupidatat", "adjustment_reason_code1": "cupidatat veniam ipsum voluptate nulla", "adjustment_amount1": 100, "adjustment_quantity1": "tempor", "adjustment_reason_code2": "sint deserunt Lorem", "adjustment_amount2": 100, "adjustment_quantity2": "adipisicing", "adjustment_reason_code3": "Lorem in dolor", "adjustment_amount3": 100, "adjustment_quantity3": "sunt dolor qui dolore", "adjustment_reason_code4": "minim laboris aliqua elit", "adjustment_amount4": 100, "adjustment_quantity4": "dolor Duis laboris labore", "adjustment_reason_code5": "<PERSON><PERSON>", "adjustment_amount5": 100, "adjustment_quantity5": "<PERSON><PERSON>eur cillum proident", "adjustment_reason_code6": "laboris do sed", "adjustment_amount6": 100, "adjustment_quantity6": "Ut nostrud ut"}, {"claim_adjustment_group_code": "ul<PERSON><PERSON> sunt", "claim_adjustment_group_code_value": "dolor exercitation quis", "adjustment_reason_code1": "aute in sint sit aliqua", "adjustment_amount1": 100, "adjustment_quantity1": "laborum occaecat", "adjustment_reason_code2": "ex non adipisicing esse", "adjustment_amount2": 100, "adjustment_quantity2": "velit", "adjustment_reason_code3": "Lorem aliqua do <PERSON>", "adjustment_amount3": 100, "adjustment_quantity3": "sint labore culpa id", "adjustment_reason_code4": "dolore in sed", "adjustment_amount4": 100, "adjustment_quantity4": "esse nisi sunt", "adjustment_reason_code5": "et in ut sunt", "adjustment_amount5": 100, "adjustment_quantity5": "sit est sed", "adjustment_reason_code6": "in sunt cillum", "adjustment_amount6": 100, "adjustment_quantity6": "ea culpa proident dolore"}, {"claim_adjustment_group_code": "elit amet proident in nisi", "claim_adjustment_group_code_value": "dolor esse consequat", "adjustment_reason_code1": "occaecat sit adipisicing", "adjustment_amount1": 100, "adjustment_quantity1": "ut incididunt occaecat", "adjustment_reason_code2": "sint proident commodo ut", "adjustment_amount2": 100, "adjustment_quantity2": "incididunt aliquip officia", "adjustment_reason_code3": "mollit officia in", "adjustment_amount3": 100, "adjustment_quantity3": "et velit minim", "adjustment_reason_code4": "aute eu ullamco ex", "adjustment_amount4": 100, "adjustment_quantity4": "<PERSON><PERSON><PERSON><PERSON>", "adjustment_reason_code5": "qui in", "adjustment_amount5": 100, "adjustment_quantity5": "magna sed esse voluptate ipsum", "adjustment_reason_code6": "quis laborum incididunt nostrud nisi", "adjustment_amount6": 100, "adjustment_quantity6": "enim labore irure ex laborum"}], "service_identification": [{"ambulatory_patient_group_number": "in proident incididunt eiusmod elit", "ambulatory_payment_classification": "aute", "authorization_number": "officia dolor", "attachment_code": "pariatur eu enim sit", "prior_authorization_number": "qui magna exercitation quis", "pre_determination_of_benefits_identification_number": "veniam", "location_number": "consectetur in dolore", "rate_code_number": "cupidatat"}], "rendering_provider_information": [{"state_license_number": "et", "blue_cross_provider_number": "deserunt", "blue_shield_provider_number": "ipsum dolor ullamco in Lorem", "medicare_provider_number": "velit in Lorem quis in", "medicaid_provider_number": "ea", "provider_upin_number": "mollit velit elit magna minim", "champus_identification_number": "ullamco est ut aute ut", "facility_id_number": "in ad ut Ut cupidatat", "national_council_for_prescription_drug_program_pharmacy_number": "no<PERSON><PERSON> laboris", "provider_commercial_number": "et reprehenderit sunt laboris ea", "npi": "occa<PERSON>t", "ssn": "consectetur occaecat", "federal_taxpayer_identification_number": "non labore sint aute"}], "service_supplemental_amounts": [{"allowed_actual": 100, "deduction_amount": 100, "tax": 100, "total_claim_before_taxes": 100, "federal_medicare_or_medicaid_payment_mandate_category1": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category2": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category3": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category4": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category5": "12/10/2024"}], "service_supplemental_quantities": [{"federal_medicare_or_medicaid_payment_mandate_category1": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category2": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category3": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category4": "12/10/2024", "federal_medicare_or_medicaid_payment_mandate_category5": "12/10/2024"}], "health_care_check_remark_codes": [{"code_list_qualifier_code": "sunt veniam ullamco velit", "code_list_qualifier_code_value": "dolore", "remark_code": "anim"}, {"code_list_qualifier_code": "tempor", "code_list_qualifier_code_value": "ex sit enim ipsum", "remark_code": "consequat"}, {"code_list_qualifier_code": "e<PERSON><PERSON><PERSON> sint", "code_list_qualifier_code_value": "non mollit laborum nisi", "remark_code": "<PERSON><PERSON> consequat consectetur"}, {"code_list_qualifier_code": "eu consequat nisi nulla commodo", "code_list_qualifier_code_value": "veniam nulla et", "remark_code": "sint consequat aute incididunt"}], "health_care_policy_identification": [{"temporb": -70990362, "policy_form_identifying_number": "sit dolor dolor velit"}, {"ut_1_0": false, "policy_form_identifying_number": "ut labore dolore amet cillum"}]}]}]}], "provider_adjustments": [{"provider_identifier": "in officia adipisicing anim", "fiscal_period_date": "12/10/2024", "adjustments": [{"adjustment_reason_code": "quis commodo adipisicing cupidatat sint", "adjustment_reason_code_value": "enim", "provider_adjustment_identifier": "officia eu laboris ex", "provider_adjustment_amount": 100}]}]}], "meta": [{"application_mode": "reprehenderit nulla", "sender_id": "eu adipisicing sint", "trace_id": "eu minim enim id"}], "raw_report_url": null, "response_id": "response_id:434343434343-eu minim enim id", "total_charge_amount": 100, "total_paid_amount": 100, "total_patient_responsibility_amount": 100, "claim_status_code": "3", "patient_id": 1430, "claim_no": "001632"}, "response_meta": null, "claim_status_code": "3", "total_charge_amount": 100, "total_paid_amount": 100, "total_patient_responsibility_amount": 100, "response_id": "response_id:434343434343-eu minim enim id", "raw_report_url": null, "s3_filepath": null, "s3_filehash": null, "patient_id": 1430, "claim_no": "001632", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "claim_status_code_auto_name": "3 - <PERSON>laim has been adjudicated and is awaiting payment cycle.", "external_id": null}