{"id": 109, "name": "<PERSON><PERSON><PERSON> – HB", "logo": "", "code": "<PERSON><PERSON><PERSON> – HB", "address": "255 NW Victoria Dr, Suite B", "city": "Lee's Summit", "state_id": "MO", "state_id_auto_name": "MO - Missouri", "zip": "64086", "phone": "(*************", "fax": "(*************", "npi": "**********", "asap_id": "", "timezone_id": "America/Chicago", "bill_name": "", "bill_address": "", "bill_city": "", "bill_state_id": "", "bill_zip": "", "part_b_provider": "", "part_a_provider": "", "nsc_id": "", "submitter_id": "", "pic_id": 2, "ncpdp_id": "1122987", "tax_id": "1122998", "tax_code_id": "KSTAX", "tax_code_id_auto_name": "KSTAX - KS Sales Tax", "bcbs_id": "", "dea_id": "", "nabp_id": "", "box_31_sig": "", "champus_id": "", "mcd_provider_id": "", "hcid": "", "company_id": "", "qualifier_id": "", "entity_identifier_code": "", "entity_type_qualifier": "", "receiver_sec_id": "", "receiver_sec_qualifier_id": "", "transmitter_id": "", "provider_ref_id": "", "provider_type_id": "", "clearinghouse": "Change Healthcare", "clearinghouse_username": "hb", "clearinghouse_password": "", "tso": "12312446", "site_id": "12312446", "submit_medicare_claim": "Yes", "dme_mac_region": "", "dme_mac_submitter_id": "12312446", "ss_organization_type": "Retail", "ss_organization_specialty": ["Specialty", "Compounding"], "ss_organization_specialty_auto_name": ["Compounding", "Specialty"], "ss_organization_id": "", "ss_service_level": ["ControlledSubstance", "New", "Change", "Refill", "Cancel"], "ss_service_level_auto_name": ["Electronic Prescribing of Controlled Substance.", "New Prescription", "Prescription Change", "Prescription Renewal", "Prescription Cancel"], "pic_label": "", "_meta": {"subform_hours": {}, "text": "Site Information:\n  Site Name: <PERSON><PERSON><PERSON> – HB\n  Company Code: <PERSON><PERSON><PERSON> – HB\n  Address: 255 NW Victoria Dr, Suite B\n  City: Lee's Summit\n  State: MO - Missouri\n  Zip: 64086\n  Phone Number: (*************\n  Fax Number: (*************\n  NPI: 1234345\n  Timezone: America/Chicago\n\nProvider Information:\n  NCPDP ID: 12312446\n  Tax ID: 12312446\n  Tax Code ID: KSTAX - KS Sales Tax\n\nClearinghouse Setup:\n  Clearinghouse: Change Healthcare\n  Clearinghouse Username: hb\n  TSO #: 12312446\n  Site ID: 12312446\n  Does site transmit Medicare claims?: Yes\n\nMedicare Claims Setup:\n  DME MAC Submitter: 12312446\n\nSurescripts Setup:\n  Surescripts Organization Organization Type: Retail\n  Surescripts Organization Specialty: Compounding, Specialty\n  Supported Surescripts Service Level: Electronic Prescribing of Controlled Substance., New Prescription, Prescription Change, Prescription Renewal, Prescription Cancel\n\nSite Business Hours (Form #1 of 5):\n  Site Business Hours:\n    Day: Monday\n    Open Time: 09:00 am\n    Close Time: 05:00 pm\n\nSite Business Hours (Form #2 of 5):\n  Site Business Hours:\n    Day: Tuesday\n    Open Time: 09:00 am\n    Close Time: 05:00 pm\n\nSite Business Hours (Form #3 of 5):\n  Site Business Hours:\n    Day: Wednesday\n    Open Time: 09:00 am\n    Close Time: 05:00 pm\n\nSite Business Hours (Form #4 of 5):\n  Site Business Hours:\n    Day: Thursday\n    Open Time: 09:00 am\n    Close Time: 05:00 pm\n\nSite Business Hours (Form #5 of 5):\n  Site Business Hours:\n    Day: Friday\n    Open Time: 09:00 am\n    Close Time: 05:00 pm", "tzoffset": 300, "client": {"updated_by": 4, "updated_by_auto_name": "", "updated_on": "05/07/2024 13:02:29"}, "clear_fields": {"site": [], "site_hours": []}, "server": {"null_missing_fields": true}}, "subform_hours": [{"day": "Monday", "start_time": "09:00 am", "close_time": "05:00 pm", "_meta": {"text": "Site Business Hours:\n  Day: Monday\n  Open Time: 09:00 am\n  Close Time: 05:00 pm"}}, {"day": "Tuesday", "start_time": "09:00 am", "close_time": "05:00 pm", "_meta": {"text": "Site Business Hours:\n  Day: Tuesday\n  Open Time: 09:00 am\n  Close Time: 05:00 pm"}}, {"day": "Wednesday", "start_time": "09:00 am", "close_time": "05:00 pm", "_meta": {"text": "Site Business Hours:\n  Day: Wednesday\n  Open Time: 09:00 am\n  Close Time: 05:00 pm"}}, {"day": "Thursday", "start_time": "09:00 am", "close_time": "05:00 pm", "_meta": {"text": "Site Business Hours:\n  Day: Thursday\n  Open Time: 09:00 am\n  Close Time: 05:00 pm"}}, {"day": "Friday", "start_time": "09:00 am", "close_time": "05:00 pm", "_meta": {"text": "Site Business Hours:\n  Day: Friday\n  Open Time: 09:00 am\n  Close Time: 05:00 pm"}}]}