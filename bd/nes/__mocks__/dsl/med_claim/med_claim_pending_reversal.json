{"_meta": {"source": "med_claim", "subform": {"receiver": "med_claim_receiver", "submitter": "med_claim_submitter", "pay_to_address": "med_claim_address_pay", "subscriber": "med_claim_subscriber", "dependent": "med_claim_dep", "claim_information": "med_claim_info", "providers": "med_claim_provs"}}, "receiver": [{"_meta": {"source": "med_claim_receiver", "subform": {}}, "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "payer_id": 54, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "BCBS-Ind - Independence BCBS PPO - INN Independence BCBS PPO", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "organization_name": "Independence BCBS PPO", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "payer_id_auto_name": "BCBS-Ind - Independence BCBS PPO - INN", "parent_id": 638, "parent_form": "med_claim", "parent_archived": null}], "submitter": [{"_meta": {"source": "med_claim_submitter", "subform": {"contact_information": "med_claim_smt_cont"}}, "contact_information": [{"_meta": {"source": "med_claim_smt_cont", "subform": {}}, "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "B<PERSON>son - MO", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "name": "B<PERSON>son - MO", "email": null, "phone_number": "(*************", "fax_number": "(*************", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 629, "parent_form": "med_claim_submitter", "parent_archived": null}], "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "site_id": 109, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Branson - MO BTX Heritage Biologics Branson", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "organization_name": "Heritage Biologics Branson", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "site_id_auto_name": "Branson - MO BTX", "parent_id": 638, "parent_form": "med_claim", "parent_archived": null}], "pay_to_address": [{"_meta": {"source": "med_claim_address_pay", "subform": {}}, "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "state": "KS", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Georgetown KS 78629", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "address1": "100 Test Road", "address2": null, "city": "Georgetown", "postal_code": "78629", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "state_auto_name": "KS", "parent_id": 638, "parent_form": "med_claim", "parent_archived": null}], "subscriber": [{"_meta": {"source": "med_claim_subscriber", "subform": {"address": "med_claim_address_sub", "contact_information": "med_claim_sub_cont"}}, "address": [{"_meta": {"source": "med_claim_address_sub", "subform": {}}, "id": 207, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "state": "TX", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Georgetown TX 78628", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "address1": "105 Rosadi Cv", "address2": null, "city": "Georgetown", "postal_code": "78628", "patient_id": null, "embed_patient_address": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": null, "state_auto_name": "TX", "external_id": null, "parent_id": 629, "parent_form": "med_claim_subscriber", "parent_archived": null}], "contact_information": [{"_meta": {"source": "med_claim_sub_cont", "subform": {}}, "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON>", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "name": "<PERSON>", "email": null, "phone_number": "(*************", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 629, "parent_form": "med_claim_subscriber", "parent_archived": null}], "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "payment_responsibility_level_code": "P", "insurance_type_code": null, "gender": "F", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON>", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "member_id": "123456", "policy_number": null, "ssn": null, "subscriber_group_name": null, "group_number": null, "first_name": "<PERSON>", "last_name": "Whiteside", "middle_name": null, "suffix": null, "date_of_birth": "10/11/1952", "order_id": null, "insurance_id": 262, "medical_relationship_id": "18", "patient_id": 1430, "insurance_dep_id": null, "patient_id_check": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "insurance_id_auto_name": ": BCBS-Ind - Independence BCBS PPO - INN : CMMED", "medical_relationship_id_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "insurance_dep_id_auto_name": null, "gender_auto_name": "F - Female", "payment_responsibility_level_code_auto_name": "P - Primary", "insurance_type_code_auto_name": null, "external_id": null, "parent_id": 638, "parent_form": "med_claim", "parent_archived": null}], "dependent": [], "claim_information": [{"_meta": {"source": "med_claim_info", "subform": {"health_care_code_information": "med_claim_dx", "claim_pricing_repricing_information": "med_claim_reprice", "service_facility_location": "med_claim_facility", "other_subscriber_information": "med_claim_osub", "service_lines": "med_claim_sl", "claim_note": "med_claim_note", "claim_supplemental_information": "med_claim_supplemental", "claim_info_other": "med_claim_info_other"}}, "health_care_code_information": [{"_meta": {"source": "med_claim_dx", "subform": {}}, "id": 696, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "dx_id": 266, "diagnosis_code_type": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "1 - O.0489 - (Induced) termination of pregnancy with other co...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "diagnosis_code": "O0489", "diagnosis_type_code": "ABK", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "dx_id_auto_name": "1 - O.0489 - (Induced) termination of pregnancy with other co...", "diagnosis_type_code_auto_name": "ABK - International Classification of Diseases Clinical Modification (ICD-10-CM) Principal Diagnosis", "external_id": null, "parent_id": 629, "parent_form": "med_claim_info", "parent_archived": null}], "claim_pricing_repricing_information": [], "service_facility_location": [], "other_subscriber_information": [], "service_lines": [{"_meta": {"source": "med_claim_sl", "subform": {"service_line_reference_information": "med_claim_sl_ref", "professional_service": "med_claim_sv", "drug_identification": "med_claim_sl_di", "durable_medical_equipment_service": "med_claim_dme", "durable_medical_equipment_certificate_of_medical_necessity": "med_claim_dme_cmn", "durable_medical_equipment_certification": "med_claim_dme_cert", "condition_indicator_durable_medical_equipment": "med_claim_dme_cond", "service_line_date_information": "med_claim_sl_dt", "line_pricing_repricing_information": "med_claim_reprice_sl", "line_adjudication_information": "med_claim_sl_adj", "form_identification": "med_claim_sl_fi", "service_line_supplemental_information": "med_claim_sl_sup", "file_information": "med_claim_sl_file"}}, "service_line_reference_information": [{"_meta": {"source": "med_claim_sl_ref", "subform": {"referral_number": "med_claim_sl_ref_rn", "prior_authorization": "med_claim_sl_ref_pa"}}, "referral_number": [], "prior_authorization": [], "id": 710, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "11111 <PERSON>side", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "repriced_line_item_reference_number": null, "adjusted_repriced_line_item_reference_number": null, "patient_id": 1430, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "external_id": null, "parent_id": 710, "parent_form": "med_claim_sl", "parent_archived": null}], "professional_service": [{"_meta": {"source": "med_claim_sv", "subform": {}}, "id": 710, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "inventory_id": 379, "hcpc_id": null, "procedure_identifier": "HC", "measurement_unit": "UN", "dx_id_1": 266, "dx_id_2": null, "dx_id_3": null, "dx_id_4": null, "place_of_service_code": "12", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON><PERSON><PERSON> (E0781) Equipment Rental", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "procedure_code": "J3490", "description": "<PERSON><PERSON><PERSON> (E0781)", "modifier_1": null, "modifier_2": null, "modifier_3": null, "modifier_4": null, "line_item_charge_amount": 600, "service_unit_count": 30, "emergency_indicator": null, "epsdt_indicator": null, "copay_status_code": null, "payer_id": null, "billable_id": null, "bill_hcpc": null, "type_filter": null, "order_item_id": null, "billable_code_id": null, "type": null, "supplemental_info": null, "patient_id": null, "charge_no": "CHG00001732", "type_index": null, "emergency_indicator_index": null, "epsdt_indicator_index": null, "copay_status_code_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": null, "inventory_id_auto_name": "<PERSON><PERSON><PERSON> (E0781) Equipment Rental", "hcpc_id_auto_name": null, "billable_code_id_auto_name": null, "procedure_identifier_auto_name": "HC - Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes", "measurement_unit_auto_name": "UN - Unit", "place_of_service_code_auto_name": "12 - Home", "dx_id_1_auto_name": "1 - O.0489 - (Induced) termination of pregnancy with other co...", "dx_id_2_auto_name": null, "dx_id_3_auto_name": null, "dx_id_4_auto_name": null, "external_id": null, "dx_filter": [266], "dx_filter_auto_name": ["1 - O.0489 - (Induced) termination of pregnancy with other co..."], "parent_id": 710, "parent_form": "med_claim_sl", "parent_archived": null}], "drug_identification": [], "durable_medical_equipment_service": [{"_meta": {"source": "med_claim_dme", "subform": {}}, "id": 15, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "inventory_id": 379, "frequency_code": "6", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON><PERSON><PERSON> (E0781) Equipment Rental 30 6 - The procedure/rev...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "days": 30, "rental_price": 600, "purchase_price": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "inventory_id_auto_name": "<PERSON><PERSON><PERSON> (E0781) Equipment Rental", "frequency_code_auto_name": "6 - Daily", "parent_id": 710, "parent_form": "med_claim_sl", "parent_archived": null}], "durable_medical_equipment_certificate_of_medical_necessity": [], "durable_medical_equipment_certification": [], "condition_indicator_durable_medical_equipment": [], "service_line_date_information": [{"_meta": {"source": "med_claim_sl_dt", "subform": {}}, "id": 710, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "2024-12-06", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "prescription_date": "12/06/2024", "certification_revision_or_recertification_date": null, "begin_therapy_date": null, "last_certification_date": null, "treatment_or_therapy_date": null, "hemoglobin_test_date": null, "serum_creatine_test_date": null, "shipped_date": "12/09/2024", "initial_treatment_date": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 710, "parent_form": "med_claim_sl", "parent_archived": null}], "line_pricing_repricing_information": [], "line_adjudication_information": [], "form_identification": [], "service_line_supplemental_information": [], "file_information": [], "id": 710, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "site_id": 109, "inventory_id": 379, "measurement_unit": "UN", "dx_id_1": 266, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "1", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "service_unit_count": 30, "modifier_1": null, "line_item_charge_amount": 600, "assigned_number": 1, "provider_control_number": "79330f06-f438-4dd6-9209-e58279099dcc", "service_date": "12/06/2024", "service_date_end": "01/05/2025", "sales_tax_amount": null, "dme": null, "additional_notes": null, "goal_rehab_or_discharge_plans": null, "third_party_organization_notes": null, "payer_id": 54, "mm_calc_perc_sales_tax": null, "tabif_dates": null, "tabif_notes": null, "tabif_dme": "Yes", "tabif_dme cmn": null, "tabif_dme certification": null, "tabif_dme condition": null, "tabif_repricing": null, "tabif_adjuication information": null, "tabif_dme_cmn": null, "tabif_dme_certification": null, "tabif_dme_condition": null, "tabif_adjuicationinformation": null, "tabif_dmecmn": null, "tabif_dmecertification": null, "tabif_dmecondition": null, "rx_no": null, "rental_item_id": null, "tabif_drugidentification": null, "rental_id": 75, "order_item_id": null, "lock_sv": null, "tabif_adjudicationinformation": null, "mm_calc_perc_sales_tax_index": null, "tabif_drugidentification_index": null, "tabif_dates_index": null, "tabif_notes_index": null, "tabif_dme_index": 0, "tabif_dmecmn_index": null, "tabif_dmecertification_index": null, "tabif_dmecondition_index": null, "tabif_repricing_index": null, "tabif_adjudicationinformation_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "order_item_id_auto_name": null, "rental_id_auto_name": "<PERSON><PERSON><PERSON> (E0781) Equipment Rental Rental E0781 (E-Code) Cu...", "patient_id_auto_name": "11111 <PERSON>side", "site_id_auto_name": "Branson - MO BTX", "payer_id_auto_name": "BCBS-Ind - Independence BCBS PPO - INN", "inventory_id_auto_name": "<PERSON><PERSON><PERSON> (E0781) Equipment Rental", "measurement_unit_auto_name": "UN - Unit", "dx_id_1_auto_name": "1 - O.0489 - (Induced) termination of pregnancy with other co...", "external_id": null, "parent_id": 629, "parent_form": "med_claim_info", "parent_archived": null}, {"_meta": {"source": "med_claim_sl", "subform": {"service_line_reference_information": "med_claim_sl_ref", "professional_service": "med_claim_sv", "drug_identification": "med_claim_sl_di", "durable_medical_equipment_service": "med_claim_dme", "durable_medical_equipment_certificate_of_medical_necessity": "med_claim_dme_cmn", "durable_medical_equipment_certification": "med_claim_dme_cert", "condition_indicator_durable_medical_equipment": "med_claim_dme_cond", "service_line_date_information": "med_claim_sl_dt", "line_pricing_repricing_information": "med_claim_reprice_sl", "line_adjudication_information": "med_claim_sl_adj", "form_identification": "med_claim_sl_fi", "service_line_supplemental_information": "med_claim_sl_sup", "file_information": "med_claim_sl_file"}}, "service_line_reference_information": [{"_meta": {"source": "med_claim_sl_ref", "subform": {"referral_number": "med_claim_sl_ref_rn", "prior_authorization": "med_claim_sl_ref_pa"}}, "referral_number": [], "prior_authorization": [], "id": 711, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "11111 <PERSON>side", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "repriced_line_item_reference_number": null, "adjusted_repriced_line_item_reference_number": null, "patient_id": 1430, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "external_id": null, "parent_id": 711, "parent_form": "med_claim_sl", "parent_archived": null}], "professional_service": [{"_meta": {"source": "med_claim_sv", "subform": {}}, "id": 711, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "inventory_id": 127, "hcpc_id": null, "procedure_identifier": "HC", "measurement_unit": "UN", "dx_id_1": 266, "dx_id_2": null, "dx_id_3": null, "dx_id_4": null, "place_of_service_code": "12", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Home infusion/specialty drug administration, per visit (up to...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "procedure_code": "J3490", "description": "Home infusion/specialty drug administration, per visit (up to 2 hours) (99601)", "modifier_1": null, "modifier_2": null, "modifier_3": null, "modifier_4": null, "line_item_charge_amount": 1800, "service_unit_count": 30, "emergency_indicator": null, "epsdt_indicator": null, "copay_status_code": null, "payer_id": null, "billable_id": null, "bill_hcpc": null, "type_filter": null, "order_item_id": null, "billable_code_id": null, "type": null, "supplemental_info": null, "patient_id": null, "charge_no": "CHG00001733", "type_index": null, "emergency_indicator_index": null, "epsdt_indicator_index": null, "copay_status_code_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": null, "inventory_id_auto_name": "Home infusion/specialty drug administration, per visit (up to...", "hcpc_id_auto_name": null, "billable_code_id_auto_name": null, "procedure_identifier_auto_name": "HC - Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes", "measurement_unit_auto_name": "UN - Unit", "place_of_service_code_auto_name": "12 - Home", "dx_id_1_auto_name": "1 - O.0489 - (Induced) termination of pregnancy with other co...", "dx_id_2_auto_name": null, "dx_id_3_auto_name": null, "dx_id_4_auto_name": null, "external_id": null, "dx_filter": [266], "dx_filter_auto_name": ["1 - O.0489 - (Induced) termination of pregnancy with other co..."], "parent_id": 711, "parent_form": "med_claim_sl", "parent_archived": null}], "drug_identification": [], "durable_medical_equipment_service": [{"_meta": {"source": "med_claim_dme", "subform": {}}, "id": 16, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "inventory_id": 127, "frequency_code": "6", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Home infusion/specialty drug administration, per visit (up to...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "days": 30, "rental_price": 1800, "purchase_price": 1400, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "inventory_id_auto_name": "Home infusion/specialty drug administration, per visit (up to...", "frequency_code_auto_name": "6 - Daily", "parent_id": 711, "parent_form": "med_claim_sl", "parent_archived": null}], "durable_medical_equipment_certificate_of_medical_necessity": [], "durable_medical_equipment_certification": [], "condition_indicator_durable_medical_equipment": [], "service_line_date_information": [{"_meta": {"source": "med_claim_sl_dt", "subform": {}}, "id": 711, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "2024-12-06", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "prescription_date": "12/06/2024", "certification_revision_or_recertification_date": null, "begin_therapy_date": null, "last_certification_date": null, "treatment_or_therapy_date": null, "hemoglobin_test_date": null, "serum_creatine_test_date": null, "shipped_date": "12/09/2024", "initial_treatment_date": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 711, "parent_form": "med_claim_sl", "parent_archived": null}], "line_pricing_repricing_information": [], "line_adjudication_information": [], "form_identification": [], "service_line_supplemental_information": [], "file_information": [], "id": 711, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "site_id": 109, "inventory_id": 127, "measurement_unit": "UN", "dx_id_1": 266, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "2", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "service_unit_count": 30, "modifier_1": null, "line_item_charge_amount": 1800, "assigned_number": 2, "provider_control_number": "baf4eae1-6a70-4d56-96c8-6ddfc152bc75", "service_date": "12/06/2024", "service_date_end": "01/05/2025", "sales_tax_amount": null, "dme": null, "additional_notes": null, "goal_rehab_or_discharge_plans": null, "third_party_organization_notes": null, "payer_id": 54, "mm_calc_perc_sales_tax": null, "tabif_dates": null, "tabif_notes": null, "tabif_dme": "Yes", "tabif_dme cmn": null, "tabif_dme certification": null, "tabif_dme condition": null, "tabif_repricing": null, "tabif_adjuication information": null, "tabif_dme_cmn": null, "tabif_dme_certification": null, "tabif_dme_condition": null, "tabif_adjuicationinformation": null, "tabif_dmecmn": null, "tabif_dmecertification": null, "tabif_dmecondition": null, "rx_no": null, "rental_item_id": null, "tabif_drugidentification": null, "rental_id": 76, "order_item_id": null, "lock_sv": null, "tabif_adjudicationinformation": null, "mm_calc_perc_sales_tax_index": null, "tabif_drugidentification_index": null, "tabif_dates_index": null, "tabif_notes_index": null, "tabif_dme_index": 0, "tabif_dmecmn_index": null, "tabif_dmecertification_index": null, "tabif_dmecondition_index": null, "tabif_repricing_index": null, "tabif_adjudicationinformation_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "order_item_id_auto_name": null, "rental_id_auto_name": "Home infusion/specialty drug administration, per visit (up to...", "patient_id_auto_name": "11111 <PERSON>side", "site_id_auto_name": "Branson - MO BTX", "payer_id_auto_name": "BCBS-Ind - Independence BCBS PPO - INN", "inventory_id_auto_name": "Home infusion/specialty drug administration, per visit (up to...", "measurement_unit_auto_name": "UN - Unit", "dx_id_1_auto_name": "1 - O.0489 - (Induced) termination of pregnancy with other co...", "external_id": null, "parent_id": 629, "parent_form": "med_claim_info", "parent_archived": null}], "claim_note": [], "claim_supplemental_information": [{"_meta": {"source": "med_claim_supplemental", "subform": {"report_information": "med_claim_report"}}, "report_information": [], "id": 573, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "insurance_id": 262, "pa_id": null, "service_authorization_exception_code": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "11111 Kara <PERSON>side : BCBS-Ind - Independence BCBS PPO - INN...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "claim_number": "001632", "claim_control_number": null, "repriced_claim_number": null, "adjusted_repriced_claim_number": null, "prior_authorization_number": null, "referral_number": null, "medical_record_number": "11111", "medicare_crossover_reference_id": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "insurance_id_auto_name": ": BCBS-Ind - Independence BCBS PPO - INN : CMMED", "pa_id_auto_name": null, "service_authorization_exception_code_auto_name": null, "external_id": null, "parent_id": 629, "parent_form": "med_claim_info", "parent_archived": null}], "claim_info_other": [{"_meta": {"source": "med_claim_info_other", "subform": {"claim_date_information": "med_claim_dates", "claim_contract_information": "med_claim_contract", "condition_information": "med_claim_cond", "file_information_list": "med_claim_file"}}, "claim_date_information": [], "claim_contract_information": [], "condition_information": [], "file_information_list": [], "id": 572, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "11111 <PERSON>side", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "site_id": 109, "patient_id": 1430, "payer_id": 54, "mm_send_contract_pricing": null, "tabif_links": null, "tabif_dates": null, "tabif_contract": null, "tabif_conditional": null, "tabif_files": null, "mm_send_contract_pricing_index": null, "tabif_links_index": null, "tabif_dates_index": null, "tabif_contract_index": null, "tabif_conditional_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "site_id_auto_name": "Branson - MO BTX", "patient_id_auto_name": "11111 <PERSON>side", "payer_id_auto_name": "BCBS-Ind - Independence BCBS PPO - INN", "external_id": null, "parent_id": 629, "parent_form": "med_claim_info", "parent_archived": null}], "id": 629, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "insurance_id": 262, "claim_filing_code": "12", "claim_frequency_code": "8", "plan_participate_code": null, "place_of_service_code": "12", "benefits_assignment_certification_indicator": "Y", "release_information_code": "Y", "related_cause_code": null, "auto_accident_state_code": null, "special_program_code": null, "delay_reason_code": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "11111 12 - Medicare Secondary Working Aged Beneficiary or Spo...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "patient_control_number": "11111", "death_date": null, "patient_weight": null, "claim_charge_amount": 2400, "patient_amount_paid": null, "pregnancy_indicator": null, "signature_indicator": "Yes", "patient_signature_source_code": "Yes", "homebound_indicator": null, "gender": "Female", "site_id": 109, "order_id": null, "payer_id": 54, "tabif_supplemental": "Yes", "tabif_repricing": null, "tabif_notes": null, "tabif_other": "Yes", "plan_participation_code": "A", "show_reprice_loop": null, "tabif_servicelocation": null, "show_reprice_loop_index": null, "gender_index": 0, "signature_indicator_index": 0, "patient_signature_source_code_index": 0, "homebound_indicator_index": null, "pregnancy_indicator_index": null, "tabif_servicelocation_index": null, "tabif_supplemental_index": 0, "tabif_repricing_index": null, "tabif_notes_index": null, "tabif_other_index": 0, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "site_id_auto_name": "Branson - MO BTX", "patient_id_auto_name": "11111 <PERSON>side", "insurance_id_auto_name": ": BCBS-Ind - Independence BCBS PPO - INN : CMMED", "payer_id_auto_name": "BCBS-Ind - Independence BCBS PPO - INN", "claim_filing_code_auto_name": "12 - Preferred Provider Organization (PPO)", "place_of_service_code_auto_name": "12 - Home", "plan_participation_code_auto_name": "A - Assigned", "special_program_code_auto_name": null, "claim_frequency_code_auto_name": "1 - Original Claim", "release_information_code_auto_name": "Y - Yes", "benefits_assignment_certification_indicator_auto_name": "Y - Yes", "delay_reason_code_auto_name": null, "related_cause_code_auto_name": null, "auto_accident_state_code_auto_name": null, "external_id": null, "parent_id": 638, "parent_form": "med_claim", "parent_archived": null}], "providers": [{"_meta": {"source": "med_claim_provs", "subform": {"billing": "med_claim_prov_bill", "referring": "med_claim_prov_ref", "ordering": "med_claim_prov_ord", "rendering": "med_claim_prov_rend", "supervising": "med_claim_prov_sup"}}, "billing": [{"_meta": {"source": "med_claim_prov_bill", "subform": {"contact_information": "med_claim_contact_bprov", "address": "med_claim_address_bprov"}}, "contact_information": [{"_meta": {"source": "med_claim_contact_bprov", "subform": {}}, "id": 575, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> (*************", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "name": "B<PERSON>son - MO", "email": null, "phone_number": "(*************", "fax_number": "(*************", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 575, "parent_form": "med_claim_prov_bill", "parent_archived": null}], "address": [{"_meta": {"source": "med_claim_address_bprov", "subform": {}}, "id": 575, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "state": "KS", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Georgetown KS 78629", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "address1": "100 Test Road", "address2": null, "city": "Georgetown", "postal_code": "78629", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "state_auto_name": "KS", "parent_id": 575, "parent_form": "med_claim_prov_bill", "parent_archived": null}], "id": 575, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "site_id": 109, "provider_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON><PERSON><PERSON> - MO BTX BillingProvider - Billing Provider Heritage ...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "organization_name": "Heritage Biologics Branson", "npi": "**********", "commercial_number": null, "state_license_number": null, "employer_identification_number": "12-4232322", "is_test": null, "last_name": null, "first_name": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "site_id_auto_name": "Branson - MO BTX", "provider_type_auto_name": "BillingProvider - Billing Provider", "parent_id": 575, "parent_form": "med_claim_provs", "parent_archived": null}], "referring": [{"_meta": {"source": "med_claim_prov_ref", "subform": {"contact_information": "med_claim_contact_rprov", "address": "med_claim_address_rprov"}}, "contact_information": [{"_meta": {"source": "med_claim_contact_rprov", "subform": {}}, "id": 567, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ANTONIO ALVAREZ (*************", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "name": "ANTONIO ALVAREZ", "email": null, "phone_number": "(*************", "fax_number": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 572, "parent_form": "med_claim_prov_ref", "parent_archived": null}], "address": [{"_meta": {"source": "med_claim_address_rprov", "subform": {}}, "id": 58, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "state": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Rock Hill 12775", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "address1": "10 First Rd", "address2": null, "city": "Rock Hill", "postal_code": "12775", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "state_auto_name": null, "parent_id": 572, "parent_form": "med_claim_prov_ref", "parent_archived": null}], "id": 572, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "prescriber_id": 193, "physician_id": 11994, "provider_type": "ReferringProvider", "taxonomy_code": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "********** <PERSON><PERSON><PERSON><PERSON>, ANTONIO M.D. ********** ALVAREZ, ANTONIO ...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "last_name": "ALVAREZ", "first_name": "ANTONIO", "npi": "**********", "commercial_number": null, "state_license_number": null, "payer_identification_number": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "prescriber_id_auto_name": "********** AL<PERSON><PERSON><PERSON>, ANTONIO M.D.", "physician_id_auto_name": "********** AL<PERSON><PERSON><PERSON>, ANTONIO M.D.", "provider_type_auto_name": "ReferringProvider - Referring Provider", "taxonomy_code_auto_name": null, "external_id": null, "parent_id": 575, "parent_form": "med_claim_provs", "parent_archived": null}], "ordering": [{"_meta": {"source": "med_claim_prov_ord", "subform": {"contact_information": "med_claim_contact_oprov", "address": "med_claim_address_oprov"}}, "contact_information": [{"_meta": {"source": "med_claim_contact_oprov", "subform": {}}, "id": 567, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "ANTONIO ALVAREZ (*************", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "name": "ANTONIO ALVAREZ", "email": null, "phone_number": "(*************", "fax_number": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 567, "parent_form": "med_claim_prov_ord", "parent_archived": null}], "address": [{"_meta": {"source": "med_claim_address_oprov", "subform": {}}, "id": 58, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "state": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Rock Hill 12775", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "address1": "10 First Rd", "address2": null, "city": "Rock Hill", "postal_code": "12775", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "state_auto_name": null, "parent_id": 567, "parent_form": "med_claim_prov_ord", "parent_archived": null}], "id": 567, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "patient_id": 1430, "prescriber_id": 193, "physician_id": 11994, "provider_type": "OrderingProvider", "taxonomy_code": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "********** <PERSON><PERSON><PERSON><PERSON>, ANTONIO M.D. ********** ALVAREZ, ANTONIO ...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "last_name": "ALVAREZ", "first_name": "ANTONIO", "npi": "**********", "commercial_number": null, "state_license_number": null, "payer_identification_number": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "prescriber_id_auto_name": "********** AL<PERSON><PERSON><PERSON>, ANTONIO M.D.", "physician_id_auto_name": "********** AL<PERSON><PERSON><PERSON>, ANTONIO M.D.", "provider_type_auto_name": "OrderingProvider - Ordering Provider", "taxonomy_code_auto_name": null, "external_id": null, "parent_id": 575, "parent_form": "med_claim_provs", "parent_archived": null}], "rendering": [{"_meta": {"source": "med_claim_prov_rend", "subform": {"contact_information": "med_claim_contact_rend", "address": "med_claim_address_rend"}}, "contact_information": [{"_meta": {"source": "med_claim_contact_rend", "subform": {}}, "id": 567, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> (*************", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "name": "B<PERSON>son - MO", "email": null, "phone_number": "(*************", "fax_number": "(*************", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 567, "parent_form": "med_claim_prov_rend", "parent_archived": null}], "address": [{"_meta": {"source": "med_claim_address_rend", "subform": {}}, "id": 567, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "state": "KS", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Georgetown KS 78629", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "address1": "100 Test Road", "address2": null, "city": "Georgetown", "postal_code": "78629", "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "state_auto_name": "KS", "parent_id": 567, "parent_form": "med_claim_prov_rend", "parent_archived": null}], "id": 567, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "site_id": 109, "provider_type": "RenderingProvider", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "<PERSON><PERSON>son - MO BTX Heritage Biologics Branson Ren<PERSON>rovider...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "organization_name": "Heritage Biologics Branson", "npi": "**********", "commercial_number": null, "state_license_number": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "site_id_auto_name": "Branson - MO BTX", "provider_type_auto_name": "RenderingProvider - Rendering Provider", "parent_id": 575, "parent_form": "med_claim_provs", "parent_archived": null}], "supervising": [], "id": 575, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "575 Clara Test (Clara Test) 2024-12-05 20:50:41", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "tabif_referring provider": null, "tabif_ordering provider": null, "tabif_rendering provider": null, "tabif_supervising provider": null, "tabif_referringprovider": "Yes", "tabif_orderingprovider": "Yes", "tabif_renderingprovider": "Yes", "tabif_supervisingprovider": null, "patient_id": 1430, "site_id": 109, "tabif_referringprovider_index": 0, "tabif_orderingprovider_index": 0, "tabif_renderingprovider_index": 0, "tabif_supervisingprovider_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "site_id_auto_name": "Branson - MO BTX", "external_id": null, "parent_id": 638, "parent_form": "med_claim", "parent_archived": null}], "id": 638, "created_by": 2, "change_by": null, "updated_by": null, "reviewed_by": null, "site_id": 109, "patient_id": 1430, "insurance_id": 262, "payer_id": 54, "void_reason_id": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": "Pending PT:11111 Kara Whiteside PY:BCBS-Ind - Independence BC...", "change_type": null, "change_data": null, "created_on": "12/05/2024 20:50:41", "change_on": null, "ready_to_bill": "Yes", "is_payable": null, "child_claim_no": null, "service_date": "12/06/2024", "status": "Pending", "control_number": "*********", "usage_indicator": "P", "trading_partner_service_id": "**********", "trading_partner_name": "Heritage Biologics Branson", "dependent_required": "No", "expected": 2400, "paid": 0, "copay": null, "tax": null, "cost": null, "show_providers": null, "void": null, "voided_datetime": null, "order_id": 498, "drug_cost": null, "nursing_cost": null, "is_test": null, "uuid": "7b8547c6-b560-4e4c-b932-25d9390ca726", "parent_claim_no": null, "claim_no": "001632", "rental_cost": 2400, "supplies_cost": null, "close_id": null, "invoice_no": "10000000162-2", "ch_responses": null, "payer_status_responses": null, "payer_final_responses": null, "is_test_index": null, "ready_to_bill_index": 0, "is_payable_index": null, "status_index": 0, "dependent_required_index": 0, "void_index": null, "created_by_auto_name": "Clara Test (Clara Test)", "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "site_id_auto_name": "Branson - MO BTX", "patient_id_auto_name": "11111 <PERSON>side", "insurance_id_auto_name": ": BCBS-Ind - Independence BCBS PPO - INN : CMMED", "payer_id_auto_name": "BCBS-Ind - Independence BCBS PPO - INN", "void_reason_id_auto_name": null, "external_id": null, "rental_id": [75, 76], "rental_id_auto_name": ["<PERSON><PERSON><PERSON> (E0781) Equipment Rental Rental E0781 (E-Code) Cu...", "Home infusion/specialty drug administration, per visit (up to..."], "order_item_id": [], "order_item_id_auto_name": [], "parent_id": 501, "parent_form": "billing_invoice", "parent_archived": null}