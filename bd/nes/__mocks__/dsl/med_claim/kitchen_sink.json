{"ready_to_bill": "Yes", "is_payable": "Yes", "parent_claim_no": 123, "usage_indicator": "P", "service_date": "07/14/2024", "site_id": 1, "patient_id": 1001, "insurance_id": 2001, "payer_id": 3001, "control_number": "123456789", "status": "Pending", "trading_partner_service_id": "TP123", "trading_partner_name": "Sample Trading Partner", "receiver": [{"payer_id": 3001, "organization_name": "Test Payer"}], "submitter": [{"site_id": 1, "organization_name": "Test Pharmacy Site", "contact_information": [{"name": "<PERSON>", "email": "<EMAIL>", "phone_number": "************", "fax_number": "************"}]}], "pay_to_address": [{"address1": "123 Main St", "address2": "Suite 456", "city": "Anytown", "state": "CA", "postal_code": "12345"}], "subscriber": [{"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "middle_name": "<PERSON>", "suffix": "Jr.", "date_of_birth": "01/15/1980", "gender": "M", "member_id": "ABC123456789", "policy_number": "POL*********", "ssn": "***********", "subscriber_group_name": "Employee Health Plan", "group_number": "GRP123456", "payment_responsibility_level_code": "P", "insurance_type_code": "12", "address": [{"address1": "456 Apple Lane", "address2": "Apt 122", "city": "Austin", "state": "TX", "postal_code": "78628"}], "contact_information": [{"name": "<PERSON>", "phone_number": "************", "email": "<EMAIL>"}]}], "dependent_required": "Yes", "dependent": [{"relationship_to_subscriber_code": "01", "member_id": "DEP*********", "ssn": "***********", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "middle_name": "<PERSON>", "suffix": "", "date_of_birth": "05/20/2010", "gender": "F", "address": [{"address1": "456 Oak Street", "address2": "Apt 789", "city": "Somewhere", "state": "NY", "postal_code": "54321"}], "contact_information": [{"name": "<PERSON>", "phone_number": "************", "email": "<EMAIL>"}]}], "claim_information": [{"patient_id": 1234, "insurance_id": 5678, "patient_control_number": "MRN123456", "gender": "Female", "claim_filing_code": "CH", "place_of_service_code": "11", "plan_participation_code": "A", "special_program_code": "02", "claim_charge_amount": 1500.0, "patient_amount_paid": 100.0, "claim_frequency_code": "7", "signature_indicator": "Yes", "patient_signature_source_code": "Yes", "homebound_indicator": "Yes", "release_information_code": "Y", "benefits_assignment_certification_indicator": "Y", "pregnancy_indicator": "Yes", "delay_reason_code": "1", "related_cause_code": "AA", "auto_accident_state_code": "CA", "death_date": "05/15/2023", "patient_weight": 75.5, "health_care_code_information": [{"patient_id": 1234, "dx_id": 1, "diagnosis_type_code": "ABF", "diagnosis_code": "J45.901"}, {"patient_id": 1234, "dx_id": 2, "diagnosis_type_code": "ABF", "diagnosis_code": "E11.9"}, {"patient_id": 1234, "dx_id": 3, "diagnosis_type_code": "ABF", "diagnosis_code": "I10"}, {"patient_id": 1234, "dx_id": 4, "diagnosis_type_code": "ABF", "diagnosis_code": "M54.5"}, {"patient_id": 1234, "dx_id": 5, "diagnosis_type_code": "ABF", "diagnosis_code": "F41.1"}, {"patient_id": 1234, "dx_id": 6, "diagnosis_type_code": "ABF", "diagnosis_code": "K21.9"}, {"patient_id": 1234, "dx_id": 7, "diagnosis_type_code": "ABF", "diagnosis_code": "H40.9"}, {"patient_id": 1234, "dx_id": 8, "diagnosis_type_code": "ABF", "diagnosis_code": "N18.3"}, {"patient_id": 1234, "dx_id": 9, "diagnosis_type_code": "ABF", "diagnosis_code": "G47.33"}, {"patient_id": 1234, "dx_id": 10, "diagnosis_type_code": "ABF", "diagnosis_code": "R53.83"}, {"patient_id": 1234, "dx_id": 11, "diagnosis_type_code": "ABF", "diagnosis_code": "L40.0"}, {"patient_id": 1234, "dx_id": 12, "diagnosis_type_code": "ABF", "diagnosis_code": "D50.9"}], "claim_pricing_repricing_information": [{"repricing_organization_identifier": "REPORG123", "pricing_methodology_code": "01", "exception_code": "1", "repriced_allowed_amount": 500.0, "repriced_saving_amount": 100.0, "repricing_per_diem_or_flat_rate_amount": 250.0, "repriced_approved_ambulatory_patient_group_code": "APG001", "repriced_approved_ambulatory_patient_group_amount": 300.0, "reject_reason_code": "T5", "policy_compliance_code": "5"}], "service_facility_location": [{"patient_id": 1234, "organization_name": "<PERSON>", "phone_name": "Home", "phone_number": "(*************", "address": [{"address1": "123 Main St", "address2": "Apt 4B", "city": "Springfield", "state": "IL", "postal_code": "62701"}]}], "other_subscriber_information": [{"payment_responsibility_level_code": "P", "individual_relationship_code": "01", "insurance_type_code": "CH", "claim_filing_indicator_code": "12", "benefits_assignment_certification_indicator": "Y", "patient_signature_generate_for_patient": "Yes", "insurance_group_or_policy_number": "GRP123456", "other_insured_group_name": "Other Insurance Group", "release_of_information_code": "Y", "other_subscriber_name": [{"other_insured_qualifier": "1", "other_insured_identifier_type_code": "MI", "other_insured_identifier": "ABC123456789", "other_insured_additional_identifier": "***********", "other_insured_first_name": "<PERSON>", "other_insured_last_name": "<PERSON>", "other_insured_middle_name": "<PERSON>", "other_insured_name_suffix": "<PERSON>", "other_insured_address": [{"address1": "456 Oak Street", "address2": "Apt 7C", "city": "Anytown", "state": "CA", "postal_code": "90210"}]}], "other_payer_name": [{"other_payer_organization_name": "ABC Insurance Company", "other_payer_identifier_type_code": "PI", "other_payer_identifier": "*********", "other_payer_adjudication_or_payment_date": "05/15/2023", "other_payer_claim_adjustment_indicator": "23", "other_payer_prior_authorization_number": "AUTH123456", "other_payer_claim_control_number": "CCN*********", "other_payer_secondary_identifier": [{"qualifier": "2U", "identifier": "SEC123456789"}], "other_payer_address": [{"address1": "789 Insurance Blvd", "address2": "Suite 500", "city": "Insuranceville", "state": "NY", "postal_code": "12345"}]}], "payer_paid_amount": 500.0, "non_covered_charge_amount": 100.0, "remaining_patient_liability": 50.0, "claim_level_adjustments": [{"adjustment_group_code": "CO", "adjustment_details": [{"adjustment_reason_code": "45", "adjustment_amount": 100.0, "adjustment_quantity": 1}, {"adjustment_reason_code": "23", "adjustment_amount": 50.0, "adjustment_quantity": 2}, {"adjustment_reason_code": "94", "adjustment_amount": 75.5, "adjustment_quantity": 1}, {"adjustment_reason_code": "16", "adjustment_amount": 200.0, "adjustment_quantity": 3}, {"adjustment_reason_code": "59", "adjustment_amount": 30.25, "adjustment_quantity": 1}, {"adjustment_reason_code": "B6", "adjustment_amount": 150.75, "adjustment_quantity": 2}]}, {"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "1", "adjustment_amount": 75.0, "adjustment_quantity": 1}, {"adjustment_reason_code": "2", "adjustment_amount": 25.5, "adjustment_quantity": 2}]}, {"adjustment_group_code": "OA", "adjustment_details": [{"adjustment_reason_code": "23", "adjustment_amount": 120.75, "adjustment_quantity": 3}]}, {"adjustment_group_code": "PI", "adjustment_details": [{"adjustment_reason_code": "97", "adjustment_amount": 50.25, "adjustment_quantity": 1}, {"adjustment_reason_code": "96", "adjustment_amount": 80.0, "adjustment_quantity": 2}]}, {"adjustment_group_code": "CR", "adjustment_details": [{"adjustment_reason_code": "132", "adjustment_amount": 95.5, "adjustment_quantity": 1}]}], "providers": [{"billing_provider": [{"entity_identifier_code": "85", "entity_type_qualifier": "1", "last_or_organization_name": "Smith Medical Group", "identification_code_qualifier": "XX", "identification_code": "**********"}], "rendering_provider": [{"entity_identifier_code": "82", "entity_type_qualifier": "1", "last_name": "<PERSON>", "first_name": "<PERSON>", "identification_code_qualifier": "XX", "identification_code": "**********"}]}], "other_payer_service_facility_location": [{"entity_identifier_code": "77", "entity_type_qualifier": "2", "name": "City Hospital", "identification_code_qualifier": "XX", "identification_code": "**********"}]}], "service_lines": [{"patient_id": 1234, "site_id": 5678, "inventory_id": 9012, "measurement_unit": "UN", "service_unit_count": 3.5, "dx_id_1": 8, "modifier_1": "GP", "line_item_charge_amount": 750.0, "assigned_number": 1, "provider_control_number": "PCN123456", "service_date": "07/15/2023", "service_date_end": "07/22/2023", "sales_tax_amount": 37.5, "service_line_reference_information": [{"patient_id": 1234, "repriced_line_item_reference_number": "REF9876543", "adjusted_repriced_line_item_reference_number": "AREF1234567", "referral_number": "RN5678901", "prior_authorization": [{"pa_id": "PA123", "prior_authorization_or_referral_number": "AUTH987654"}]}], "professional_service": [{"inventory_id": 9012, "hcpc_id": 4, "procedure_identifier": "HC", "procedure_code": "E0277", "description": "Powered pressure-reducing air mattress", "line_item_charge_amount": 750.0, "measurement_unit": "UN", "service_unit_count": 3.5, "emergency_indicator": "Yes", "epsdt_indicator": "Yes", "copay_status_code": "Yes", "place_of_service_code": "12", "modifier_1": "GP", "modifier_2": "59", "modifier_3": "XU", "modifier_4": "KX", "dx_id_1": 1, "dx_id_2": 7, "dx_id_3": 12, "dx_id_4": 6}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0250"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "Yes", "certification_type_code": "I", "durable_medical_equipment_duration_in_months": 12}], "durable_medical_equipment_certification": [{"dme_certification_type": "R", "certification_date": "07/01/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "Yes", "condition_indicator": "12", "condition_indicator_code": "38"}], "service_line_date_information": [{"prescription_date": "07/01/2023", "certification_revision_or_recertification_date": "07/05/2023", "begin_therapy_date": "07/10/2023", "last_certification_date": "06/30/2023", "treatment_or_therapy_date": "07/15/2023", "hemoglobin_test_date": "07/03/2023", "serum_creatine_test_date": "07/04/2023", "shipped_date": "07/12/2023", "initial_treatment_date": "07/08/2023"}], "additional_notes": "Patient reported mild discomfort during procedure.", "goal_rehab_or_discharge_plans": "Continue physical therapy for 4 weeks.", "third_party_organization_notes": "Claim reviewed by third-party auditor.", "line_pricing_repricing_information": [{"pricing_methodology_code": "01", "repriced_allowed_amount": 500.0, "repriced_saving_amount": 100.0, "repricing_organization_identifier": "REPORG123", "repricing_per_diem_or_flat_rate_amount": 250.0, "repriced_approved_ambulatory_patient_group_code": "APG001", "repriced_approved_ambulatory_patient_group_amount": 350.0, "reject_reason_code": "A1", "policy_compliance_code": "Y", "exception_code": "1"}], "line_adjudication_information": [{"other_payer_primary_identifier": "AB123456789", "service_id_qualifier": "HC", "procedure_code": "99213", "procedure_code_description": "Office visit, established patient", "paid_service_unit_count": 1.0, "adjudication_or_payment_date": "07/15/2023", "service_line_paid_amount": 75.0, "remaining_patient_liability": 25.0, "modifier_1": "GP", "modifier_2": "59", "modifier_3": "XU", "modifier_4": "KX", "claim_adjustment_information": [{"adjustment_group_code": "CO", "adjustment_details": [{"adjustment_reason_code": "45", "adjustment_amount": 100.5, "adjustment_quantity": 3.0}, {"adjustment_reason_code": "253", "adjustment_amount": 50.0, "adjustment_quantity": 2}]}]}]}, {"patient_id": 7823, "site_id": 3941, "inventory_id": 6205, "measurement_unit": "GM", "service_unit_count": 4.75, "dx_id_1": 3, "modifier_1": "RT", "line_item_charge_amount": 1125.75, "assigned_number": 9, "provider_control_number": "PCN789012", "service_date": "09/03/2023", "service_date_end": "09/07/2023", "sales_tax_amount": 56.29, "service_line_reference_information": [{"patient_id": 7823, "repriced_line_item_reference_number": "REF5678901", "adjusted_repriced_line_item_reference_number": "AREF2345678", "referral_number": "RN9012345", "prior_authorization": [{"pa_id": "PA456", "prior_authorization_or_referral_number": "AUTH654321"}]}], "professional_service": [{"inventory_id": 6205, "hcpc_id": 15, "procedure_identifier": "HC", "procedure_code": "E0430", "description": "Portable gaseous oxygen system", "line_item_charge_amount": 1125.75, "measurement_unit": "GM", "service_unit_count": 4.75, "emergency_indicator": "No", "epsdt_indicator": "Yes", "copay_status_code": "No", "place_of_service_code": "33", "modifier_1": "RT", "modifier_2": "KX", "modifier_3": "NU", "modifier_4": "LT", "dx_id_1": 7, "dx_id_2": 11, "dx_id_3": 2, "dx_id_4": 9}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0465"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 18}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "24", "condition_indicator_code": "52"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/18/2023", "begin_therapy_date": "08/22/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "08/25/2023", "hemoglobin_test_date": "08/12/2023", "serum_creatine_test_date": "08/13/2023", "shipped_date": "08/20/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient reported improvement in mobility after treatment.", "goal_rehab_or_discharge_plans": "Continue therapy for 3 weeks, then reassess.", "third_party_organization_notes": "Claim approved by third-party reviewer with minor adjustments.", "line_pricing_repricing_information": [{"pricing_methodology_code": "02", "repriced_allowed_amount": 675.0, "repriced_saving_amount": 217.5, "repricing_organization_identifier": "REPORG789", "repricing_per_diem_or_flat_rate_amount": 337.5, "repriced_approved_ambulatory_patient_group_code": "APG004", "repriced_approved_ambulatory_patient_group_amount": 450.0, "reject_reason_code": "C3", "policy_compliance_code": "Y", "exception_code": "2"}], "line_adjudication_information": [{"other_payer_primary_identifier": "EF*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "08/30/2023", "service_line_paid_amount": 135.0, "remaining_patient_liability": 40.0, "modifier_1": "25", "modifier_2": "GC", "modifier_3": "QW", "modifier_4": "TC", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 82.5, "adjustment_quantity": 2.0}, {"adjustment_reason_code": "158", "adjustment_amount": 32.5, "adjustment_quantity": 1.5}]}]}]}, {"patient_id": 9876, "site_id": 2468, "inventory_id": 1357, "measurement_unit": "ML", "service_unit_count": 2.75, "dx_id_1": 4, "modifier_1": "LT", "line_item_charge_amount": 895.5, "assigned_number": 7, "provider_control_number": "PCN654321", "service_date": "08/22/2023", "service_date_end": "08/25/2023", "sales_tax_amount": 44.78, "service_line_reference_information": [{"patient_id": 9876, "repriced_line_item_reference_number": "REF2468135", "adjusted_repriced_line_item_reference_number": "AREF7531902", "referral_number": "RN3698521", "prior_authorization": [{"pa_id": "PA789", "prior_authorization_or_referral_number": "AUTH456123"}]}], "professional_service": [{"inventory_id": 1357, "hcpc_id": 9, "procedure_identifier": "HC", "procedure_code": "E0601", "description": "Continuous positive airway pressure device", "line_item_charge_amount": 895.5, "measurement_unit": "ML", "service_unit_count": 2.75, "emergency_indicator": "Yes", "epsdt_indicator": "No", "copay_status_code": "Yes", "place_of_service_code": "21", "modifier_1": "LT", "modifier_2": "RR", "modifier_3": "GA", "modifier_4": "GY", "dx_id_1": 8, "dx_id_2": 3, "dx_id_3": 11, "dx_id_4": 6}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0470"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 9}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "15", "condition_indicator_code": "42"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/18/2023", "begin_therapy_date": "08/22/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "08/25/2023", "hemoglobin_test_date": "08/12/2023", "serum_creatine_test_date": "08/13/2023", "shipped_date": "08/20/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient reported improvement in sleep quality.", "goal_rehab_or_discharge_plans": "Continue CPAP therapy for 8 weeks, then reassess.", "third_party_organization_notes": "Claim approved by third-party reviewer with minor adjustments.", "line_pricing_repricing_information": [{"pricing_methodology_code": "03", "repriced_allowed_amount": 675.0, "repriced_saving_amount": 220.5, "repricing_organization_identifier": "REPORG789", "repricing_per_diem_or_flat_rate_amount": 337.5, "repriced_approved_ambulatory_patient_group_code": "APG004", "repriced_approved_ambulatory_patient_group_amount": 450.0, "reject_reason_code": "C3", "policy_compliance_code": "Y", "exception_code": "2"}], "line_adjudication_information": [{"other_payer_primary_identifier": "EF*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "08/30/2023", "service_line_paid_amount": 55.21, "remaining_patient_liability": 40.0, "modifier_1": "25", "modifier_2": "GC", "modifier_3": "QW", "modifier_4": "TC", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 82.5, "adjustment_quantity": 2.0}, {"adjustment_reason_code": "158", "adjustment_amount": 33.0, "adjustment_quantity": 1}]}]}]}, {"patient_id": 8765, "site_id": 3210, "inventory_id": 5678, "measurement_unit": "CM", "service_unit_count": 4.25, "dx_id_1": 9, "modifier_1": "RT", "line_item_charge_amount": 1250.75, "assigned_number": 3, "provider_control_number": "PCN987654", "service_date": "09/03/2023", "service_date_end": "09/05/2023", "sales_tax_amount": 62.54, "service_line_reference_information": [{"patient_id": 8765, "repriced_line_item_reference_number": "REF7654321", "adjusted_repriced_line_item_reference_number": "AREF3456789", "referral_number": "RN2345678", "prior_authorization": [{"pa_id": "PA789", "prior_authorization_or_referral_number": "AUTH987654"}]}], "professional_service": [{"inventory_id": 5678, "hcpc_id": 15, "procedure_identifier": "HC", "procedure_code": "E0430", "description": "Portable gaseous oxygen system", "line_item_charge_amount": 1250.75, "measurement_unit": "CM", "service_unit_count": 4.25, "emergency_indicator": "No", "epsdt_indicator": "Yes", "copay_status_code": "No", "place_of_service_code": "33", "modifier_1": "RT", "modifier_2": "KX", "modifier_3": "NU", "modifier_4": "LT", "dx_id_1": 3, "dx_id_2": 11, "dx_id_3": 7, "dx_id_4": 2}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0465"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 18}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/25/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "15", "condition_indicator_code": "42"}], "service_line_date_information": [{"prescription_date": "08/20/2023", "certification_revision_or_recertification_date": "08/30/2023", "begin_therapy_date": "09/02/2023", "last_certification_date": "08/15/2023", "treatment_or_therapy_date": "09/03/2023", "hemoglobin_test_date": "08/27/2023", "serum_creatine_test_date": "08/28/2023", "shipped_date": "09/01/2023", "initial_treatment_date": "08/31/2023"}], "additional_notes": "Patient showed improvement after initial treatment.", "goal_rehab_or_discharge_plans": "Follow-up appointment scheduled in 2 weeks.", "third_party_organization_notes": "Claim approved by third-party reviewer.", "line_pricing_repricing_information": [{"pricing_methodology_code": "02", "repriced_allowed_amount": 450.0, "repriced_saving_amount": 175.75, "repricing_organization_identifier": "REPORG456", "repricing_per_diem_or_flat_rate_amount": 225.0, "repriced_approved_ambulatory_patient_group_code": "APG002", "repriced_approved_ambulatory_patient_group_amount": 300.0, "reject_reason_code": "B2", "policy_compliance_code": "N", "exception_code": "2"}], "line_adjudication_information": [{"other_payer_primary_identifier": "CD*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "09/10/2023", "service_line_paid_amount": 100.0, "remaining_patient_liability": 35.0, "modifier_1": "25", "modifier_2": "95", "modifier_3": "GC", "modifier_4": "QP", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 75.25, "adjustment_quantity": 1.5}, {"adjustment_reason_code": "158", "adjustment_amount": 25.75, "adjustment_quantity": 1}]}]}]}, {"patient_id": 7532, "site_id": 9014, "inventory_id": 2468, "measurement_unit": "ML", "service_unit_count": 2.75, "dx_id_1": 3, "modifier_1": "LT", "line_item_charge_amount": 892.5, "assigned_number": 5, "provider_control_number": "PCN789012", "service_date": "08/22/2023", "service_date_end": "08/25/2023", "sales_tax_amount": 44.63, "service_line_reference_information": [{"patient_id": 7532, "repriced_line_item_reference_number": "REF3456789", "adjusted_repriced_line_item_reference_number": "AREF9876543", "referral_number": "RN1234567", "prior_authorization": [{"pa_id": "PA456", "prior_authorization_or_referral_number": "AUTH654321"}]}], "professional_service": [{"inventory_id": 2468, "hcpc_id": 9, "procedure_identifier": "HC", "procedure_code": "E0185", "description": "Gel or gel-like pressure pad for mattress", "line_item_charge_amount": 892.5, "measurement_unit": "ML", "service_unit_count": 2.75, "emergency_indicator": "Yes", "epsdt_indicator": "No", "copay_status_code": "Yes", "place_of_service_code": "21", "modifier_1": "LT", "modifier_2": "RR", "modifier_3": "GC", "modifier_4": "QK", "dx_id_1": 7, "dx_id_2": 11, "dx_id_3": 2, "dx_id_4": 9}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0275"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 9}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "07", "condition_indicator_code": "42"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/18/2023", "begin_therapy_date": "08/22/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "08/25/2023", "hemoglobin_test_date": "08/12/2023", "serum_creatine_test_date": "08/13/2023", "shipped_date": "08/20/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient reported improvement in mobility after treatment.", "goal_rehab_or_discharge_plans": "Continue therapy for 3 weeks, then reassess.", "third_party_organization_notes": "Claim approved by third-party reviewer with minor adjustments.", "line_pricing_repricing_information": [{"pricing_methodology_code": "02", "repriced_allowed_amount": 675.0, "repriced_saving_amount": 217.5, "repricing_organization_identifier": "REPORG789", "repricing_per_diem_or_flat_rate_amount": 337.5, "repriced_approved_ambulatory_patient_group_code": "APG004", "repriced_approved_ambulatory_patient_group_amount": 450.0, "reject_reason_code": "C3", "policy_compliance_code": "Y", "exception_code": "2"}], "line_adjudication_information": [{"other_payer_primary_identifier": "EF*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "08/30/2023", "service_line_paid_amount": 135.0, "remaining_patient_liability": 40.0, "modifier_1": "25", "modifier_2": "GC", "modifier_3": "QW", "modifier_4": "TC", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 82.5, "adjustment_quantity": 2.0}, {"adjustment_reason_code": "158", "adjustment_amount": 35.0, "adjustment_quantity": 1}]}]}]}, {"patient_id": 9876, "site_id": 3456, "inventory_id": 7890, "measurement_unit": "GM", "service_unit_count": 4.25, "dx_id_1": 10, "modifier_1": "RR", "line_item_charge_amount": 1125.75, "assigned_number": 3, "provider_control_number": "PCN654321", "service_date": "09/03/2023", "service_date_end": "09/05/2023", "sales_tax_amount": 56.29, "service_line_reference_information": [{"patient_id": 9876, "repriced_line_item_reference_number": "REF7654321", "adjusted_repriced_line_item_reference_number": "AREF3456789", "referral_number": "RN2345678", "prior_authorization": [{"pa_id": "PA789", "prior_authorization_or_referral_number": "AUTH987654"}]}], "professional_service": [{"inventory_id": 7890, "hcpc_id": 15, "procedure_identifier": "HC", "procedure_code": "E0430", "description": "Portable gaseous oxygen system", "line_item_charge_amount": 1125.75, "measurement_unit": "GM", "service_unit_count": 4.25, "emergency_indicator": "No", "epsdt_indicator": "Yes", "copay_status_code": "No", "place_of_service_code": "33", "modifier_1": "RR", "modifier_2": "KX", "modifier_3": "NU", "modifier_4": "LT", "dx_id_1": 4, "dx_id_2": 8, "dx_id_3": 2, "dx_id_4": 11}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0465"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 18}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "24", "condition_indicator_code": "52"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/20/2023", "begin_therapy_date": "08/25/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "09/01/2023", "hemoglobin_test_date": "08/18/2023", "serum_creatine_test_date": "08/19/2023", "shipped_date": "08/28/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient showed significant improvement after treatment.", "goal_rehab_or_discharge_plans": "Continue oxygen therapy for 6 weeks.", "third_party_organization_notes": "Claim approved by third-party reviewer.", "line_pricing_repricing_information": [{"pricing_methodology_code": "03", "repriced_allowed_amount": 750.0, "repriced_saving_amount": 375.75, "repricing_organization_identifier": "REPORG789", "repricing_per_diem_or_flat_rate_amount": 375.0, "repriced_approved_ambulatory_patient_group_code": "APG005", "repriced_approved_ambulatory_patient_group_amount": 525.0, "reject_reason_code": "B4", "policy_compliance_code": "N", "exception_code": "5"}], "line_adjudication_information": [{"other_payer_primary_identifier": "EF*********", "service_id_qualifier": "HC", "procedure_code": "99215", "procedure_code_description": "Office visit, established patient, high complexity", "paid_service_unit_count": 2.5, "adjudication_or_payment_date": "09/10/2023", "service_line_paid_amount": 200.0, "remaining_patient_liability": 75.0, "modifier_1": "25", "modifier_2": "95", "modifier_3": "GC", "modifier_4": "QP", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 125.75, "adjustment_quantity": 2.5}, {"adjustment_reason_code": "158", "adjustment_amount": 50.0, "adjustment_quantity": 1}]}]}, {"other_payer_primary_identifier": "EF*********", "service_id_qualifier": "HC", "procedure_code": "99215", "procedure_code_description": "Office visit, established patient, high complexity", "paid_service_unit_count": 2.5, "adjudication_or_payment_date": "09/10/2023", "service_line_paid_amount": 43.22, "remaining_patient_liability": 75.0, "modifier_1": "25", "modifier_2": "95", "modifier_3": "GC", "modifier_4": "QP", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 125.75, "adjustment_quantity": 2.5}, {"adjustment_reason_code": "158", "adjustment_amount": 50.0, "adjustment_quantity": 1}]}]}]}, {"patient_id": 7890, "site_id": 2345, "inventory_id": 6789, "measurement_unit": "ML", "service_unit_count": 2.75, "dx_id_1": 3, "modifier_1": "TC", "line_item_charge_amount": 925.5, "assigned_number": 4, "provider_control_number": "PCN789012", "service_date": "08/22/2023", "service_date_end": "08/22/2023", "sales_tax_amount": 46.28, "service_line_reference_information": [{"patient_id": 7890, "repriced_line_item_reference_number": "REF1234567", "adjusted_repriced_line_item_reference_number": "AREF7890123", "referral_number": "RN9012345", "prior_authorization": [{"pa_id": "PA456", "prior_authorization_or_referral_number": "AUTH123789"}]}], "professional_service": [{"inventory_id": 6789, "hcpc_id": 8, "procedure_identifier": "HC", "procedure_code": "E0260", "description": "Semi-electric hospital bed", "line_item_charge_amount": 925.5, "measurement_unit": "ML", "service_unit_count": 2.75, "emergency_indicator": "No", "epsdt_indicator": "No", "copay_status_code": "No", "place_of_service_code": "21", "modifier_1": "TC", "modifier_2": "76", "modifier_3": "GC", "modifier_4": "QK", "dx_id_1": 5, "dx_id_2": 9, "dx_id_3": 2, "dx_id_4": 11}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0275"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 9}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "24", "condition_indicator_code": "52"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/20/2023", "begin_therapy_date": "08/25/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "09/01/2023", "hemoglobin_test_date": "08/18/2023", "serum_creatine_test_date": "08/19/2023", "shipped_date": "08/28/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient showed significant improvement after treatment.", "goal_rehab_or_discharge_plans": "Continue oxygen therapy for 6 weeks.", "third_party_organization_notes": "Claim approved by third-party reviewer.", "line_pricing_repricing_information": [{"pricing_methodology_code": "03", "repriced_allowed_amount": 750.0, "repriced_saving_amount": 150.0, "repricing_organization_identifier": "REPORG456", "repricing_per_diem_or_flat_rate_amount": 375.0, "repriced_approved_ambulatory_patient_group_code": "APG003", "repriced_approved_ambulatory_patient_group_amount": 525.0, "reject_reason_code": "B2", "policy_compliance_code": "N", "exception_code": "3"}], "line_adjudication_information": [{"other_payer_primary_identifier": "CD*********", "service_id_qualifier": "HC", "procedure_code": "99215", "procedure_code_description": "Office visit, established patient, high complexity", "paid_service_unit_count": 2.0, "adjudication_or_payment_date": "09/10/2023", "service_line_paid_amount": 150.22, "remaining_patient_liability": 50.0, "modifier_1": "25", "modifier_2": "95", "modifier_3": "GC", "modifier_4": "QP", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 75.25, "adjustment_quantity": 1.5}, {"adjustment_reason_code": "158", "adjustment_amount": 25.75, "adjustment_quantity": 1}]}]}]}, {"patient_id": 8765, "site_id": 3210, "inventory_id": 4567, "measurement_unit": "CM", "service_unit_count": 4.25, "dx_id_1": 5, "modifier_1": "RT", "line_item_charge_amount": 625.75, "assigned_number": 3, "provider_control_number": "PCN789012", "service_date": "09/03/2023", "service_date_end": "09/05/2023", "sales_tax_amount": 31.29, "service_line_reference_information": [{"patient_id": 8765, "repriced_line_item_reference_number": "REF5678901", "adjusted_repriced_line_item_reference_number": "AREF2345678", "referral_number": "RN3456789", "prior_authorization": [{"pa_id": "PA789", "prior_authorization_or_referral_number": "AUTH567890"}]}], "professional_service": [{"inventory_id": 4567, "hcpc_id": 12, "procedure_identifier": "HC", "procedure_code": "E0430", "description": "Portable gaseous oxygen system", "line_item_charge_amount": 625.75, "measurement_unit": "CM", "service_unit_count": 4.25, "emergency_indicator": "No", "epsdt_indicator": "Yes", "copay_status_code": "No", "place_of_service_code": "11", "modifier_1": "RT", "modifier_2": "NU", "modifier_3": "KC", "modifier_4": "QE", "dx_id_1": 9, "dx_id_2": 3, "dx_id_3": 11, "dx_id_4": 7}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0465"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 9}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/28/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "15", "condition_indicator_code": "42"}], "service_line_date_information": [{"prescription_date": "08/25/2023", "certification_revision_or_recertification_date": "08/30/2023", "begin_therapy_date": "09/02/2023", "last_certification_date": "08/20/2023", "treatment_or_therapy_date": "09/03/2023", "hemoglobin_test_date": "08/27/2023", "serum_creatine_test_date": "08/28/2023", "shipped_date": "09/01/2023", "initial_treatment_date": "08/31/2023"}], "additional_notes": "Patient showed improvement after initial treatment.", "goal_rehab_or_discharge_plans": "Follow-up appointment scheduled in 2 weeks.", "third_party_organization_notes": "Claim approved by third-party reviewer.", "line_pricing_repricing_information": [{"pricing_methodology_code": "02", "repriced_allowed_amount": 450.0, "repriced_saving_amount": 175.75, "repricing_organization_identifier": "REPORG456", "repricing_per_diem_or_flat_rate_amount": 225.0, "repriced_approved_ambulatory_patient_group_code": "APG002", "repriced_approved_ambulatory_patient_group_amount": 300.0, "reject_reason_code": "B2", "policy_compliance_code": "N", "exception_code": "2"}], "line_adjudication_information": [{"other_payer_primary_identifier": "CD*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "09/10/2023", "service_line_paid_amount": 33.33, "remaining_patient_liability": 35.0, "modifier_1": "25", "modifier_2": "95", "modifier_3": "GC", "modifier_4": "QP", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 75.25, "adjustment_quantity": 1.5}, {"adjustment_reason_code": "158", "adjustment_amount": 25.75, "adjustment_quantity": 1}]}]}]}, {"patient_id": 7890, "site_id": 2345, "inventory_id": 6789, "measurement_unit": "ML", "service_unit_count": 2.75, "dx_id_1": 3, "modifier_1": "LT", "line_item_charge_amount": 980.25, "assigned_number": 5, "provider_control_number": "PCN987654", "service_date": "08/22/2023", "service_date_end": "08/25/2023", "sales_tax_amount": 49.01, "service_line_reference_information": [{"patient_id": 7890, "repriced_line_item_reference_number": "REF1234567", "adjusted_repriced_line_item_reference_number": "AREF7890123", "referral_number": "RN9876543", "prior_authorization": [{"pa_id": "PA456", "prior_authorization_or_referral_number": "AUTH123456"}]}], "professional_service": [{"inventory_id": 6789, "hcpc_id": 8, "procedure_identifier": "HC", "procedure_code": "E0601", "description": "Continuous positive airway pressure device", "line_item_charge_amount": 980.25, "measurement_unit": "ML", "service_unit_count": 2.75, "emergency_indicator": "No", "epsdt_indicator": "No", "copay_status_code": "Yes", "place_of_service_code": "21", "modifier_1": "LT", "modifier_2": "RR", "modifier_3": "GA", "modifier_4": "GY", "dx_id_1": 5, "dx_id_2": 11, "dx_id_3": 2, "dx_id_4": 9}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0470"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 9}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "15", "condition_indicator_code": "42"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/18/2023", "begin_therapy_date": "08/22/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "08/25/2023", "hemoglobin_test_date": "08/12/2023", "serum_creatine_test_date": "08/13/2023", "shipped_date": "08/20/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient showed improvement after initial treatment.", "goal_rehab_or_discharge_plans": "Continue treatment for 6 weeks, then reassess.", "third_party_organization_notes": "Claim approved by third-party reviewer.", "line_pricing_repricing_information": [{"pricing_methodology_code": "02", "repriced_allowed_amount": 750.0, "repriced_saving_amount": 230.25, "repricing_organization_identifier": "REPORG456", "repricing_per_diem_or_flat_rate_amount": 375.0, "repriced_approved_ambulatory_patient_group_code": "APG002", "repriced_approved_ambulatory_patient_group_amount": 525.0, "reject_reason_code": "B2", "policy_compliance_code": "N", "exception_code": "2"}], "line_adjudication_information": [{"other_payer_primary_identifier": "CD*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "08/30/2023", "service_line_paid_amount": 120.0, "remaining_patient_liability": 35.0, "modifier_1": "25", "modifier_2": "GC", "modifier_3": "QW", "modifier_4": "TC", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "60", "adjustment_amount": 75.25, "adjustment_quantity": 2.0}, {"adjustment_reason_code": "158", "adjustment_amount": 30.0, "adjustment_quantity": 1}]}]}]}, {"patient_id": 8765, "site_id": 3210, "inventory_id": 4567, "measurement_unit": "CM", "service_unit_count": 4.25, "dx_id_1": 5, "modifier_1": "RR", "line_item_charge_amount": 1250.75, "assigned_number": 3, "provider_control_number": "PCN654321", "service_date": "09/03/2023", "service_date_end": "09/05/2023", "sales_tax_amount": 62.54, "service_line_reference_information": [{"patient_id": 8765, "repriced_line_item_reference_number": "REF7654321", "adjusted_repriced_line_item_reference_number": "AREF3456789", "referral_number": "RN2345678", "prior_authorization": [{"pa_id": "PA789", "prior_authorization_or_referral_number": "AUTH987654"}]}], "professional_service": [{"inventory_id": 4567, "hcpc_id": 12, "procedure_identifier": "HC", "procedure_code": "E0430", "description": "Portable gaseous oxygen system", "line_item_charge_amount": 1250.75, "measurement_unit": "CM", "service_unit_count": 4.25, "emergency_indicator": "No", "epsdt_indicator": "Yes", "copay_status_code": "No", "place_of_service_code": "33", "modifier_1": "RR", "modifier_2": "KX", "modifier_3": "NU", "modifier_4": "LT", "dx_id_1": 9, "dx_id_2": 3, "dx_id_3": 11, "dx_id_4": 7}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0465"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 18}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "24", "condition_indicator_code": "52"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/20/2023", "begin_therapy_date": "08/25/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "09/01/2023", "hemoglobin_test_date": "08/18/2023", "serum_creatine_test_date": "08/19/2023", "shipped_date": "08/28/2023", "initial_treatment_date": "08/22/2023"}], "additional_notes": "Patient showed significant improvement after treatment.", "goal_rehab_or_discharge_plans": "Continue oxygen therapy for 6 weeks.", "third_party_organization_notes": "Claim approved by third-party reviewer.", "line_pricing_repricing_information": [{"pricing_methodology_code": "03", "repriced_allowed_amount": 750.0, "repriced_saving_amount": 150.0, "repricing_organization_identifier": "REPORG456", "repricing_per_diem_or_flat_rate_amount": 375.0, "repriced_approved_ambulatory_patient_group_code": "APG003", "repriced_approved_ambulatory_patient_group_amount": 525.0, "reject_reason_code": "B2", "policy_compliance_code": "N", "exception_code": "3"}], "line_adjudication_information": null}, {"patient_id": 7890, "site_id": 2345, "inventory_id": 6789, "measurement_unit": "ML", "service_unit_count": 2.75, "dx_id_1": 3, "modifier_1": "TC", "line_item_charge_amount": 122.42, "assigned_number": 4, "provider_control_number": "PCN789012", "service_date": "08/22/2023", "service_date_end": "09/15/2023", "sales_tax_amount": 46.28, "service_line_reference_information": [{"patient_id": 7890, "repriced_line_item_reference_number": "REF1234567", "adjusted_repriced_line_item_reference_number": "AREF7890123", "referral_number": "RN9012345", "prior_authorization": [{"pa_id": "PA456", "prior_authorization_or_referral_number": "AUTH123789"}]}], "professional_service": [{"inventory_id": 6789, "hcpc_id": 8, "procedure_identifier": "HC", "procedure_code": "E0260", "description": "Semi-electric hospital bed", "line_item_charge_amount": 122.42, "measurement_unit": "ML", "service_unit_count": 2.75, "emergency_indicator": "No", "epsdt_indicator": "No", "copay_status_code": "No", "place_of_service_code": "21", "modifier_1": "TC", "modifier_2": "76", "modifier_3": "GC", "modifier_4": "QK", "dx_id_1": 5, "dx_id_2": 9, "dx_id_3": 2, "dx_id_4": 11}], "durable_medical_equipment_service": [{"product_or_service_id_qualifier": "HC", "procedure_code": "E0275"}], "durable_medical_equipment_certificate_of_medical_necessity": [{"send_cert": "No", "certification_type_code": "R", "durable_medical_equipment_duration_in_months": 9}], "durable_medical_equipment_certification": [{"dme_certification_type": "I", "certification_date": "08/15/2023"}], "condition_indicator_durable_medical_equipment": [{"certification_condition_indicator": "No", "condition_indicator": "15", "condition_indicator_code": "42"}], "service_line_date_information": [{"prescription_date": "08/10/2023", "certification_revision_or_recertification_date": "08/18/2023", "begin_therapy_date": "08/25/2023", "last_certification_date": "08/05/2023", "treatment_or_therapy_date": "08/28/2023", "hemoglobin_test_date": "08/12/2023", "serum_creatine_test_date": "08/13/2023", "shipped_date": "08/20/2023", "initial_treatment_date": "08/23/2023"}], "additional_notes": "Patient experienced no complications during procedure.", "goal_rehab_or_discharge_plans": "Follow up with occupational therapy for 3 weeks.", "third_party_organization_notes": "Claim verified by external review board.", "line_pricing_repricing_information": [{"pricing_methodology_code": "03", "repriced_allowed_amount": 650.0, "repriced_saving_amount": 275.5, "repricing_organization_identifier": "REPORG456", "repricing_per_diem_or_flat_rate_amount": 300.0, "repriced_approved_ambulatory_patient_group_code": "APG003", "repriced_approved_ambulatory_patient_group_amount": 400.0, "reject_reason_code": "B2", "policy_compliance_code": "N", "exception_code": "3"}], "line_adjudication_information": [{"other_payer_primary_identifier": "CD*********", "service_id_qualifier": "HC", "procedure_code": "99214", "procedure_code_description": "Office visit, established patient, moderate complexity", "paid_service_unit_count": 1.5, "adjudication_or_payment_date": "08/30/2023", "service_line_paid_amount": 95.12, "remaining_patient_liability": 30.5, "modifier_1": "TC", "modifier_2": "76", "modifier_3": "GC", "modifier_4": "QK", "claim_adjustment_information": [{"adjustment_group_code": "PR", "adjustment_details": [{"adjustment_reason_code": "50", "adjustment_amount": 75.25, "adjustment_quantity": 2.5}, {"adjustment_reason_code": "158", "adjustment_amount": 40.0, "adjustment_quantity": 1}]}]}]}], "claim_note": [{"additional_information": "Patient reported improvement in symptoms after initial treatment. Additional notes here to reach 80 characters.", "certification_narrative": "Certify that all services provided were medically necessary and appropriate for the patient's condition.", "goal_rehab_or_discharge_plans": "Patient to continue physical therapy 3 times per week for 4 weeks.", "diagnosis_description": "Acute lower back pain, likely due to muscle strain.", "third_part_org_notes": "Claim reviewed by third-party auditor on 07/15/2023."}], "claim_supplemental_information": [{"patient_id": 1234, "insurance_id": 5678, "claim_number": "CLM9876543", "claim_control_number": "CCN123456789", "medicare_crossover_reference_id": "MCR*********", "pa_id": 123, "prior_authorization_number": "AUTH987654", "service_authorization_exception_code": "3", "repriced_claim_number": "RCN456789", "adjusted_repriced_claim_number": "ARCN123456", "medical_record_number": "MRN789012", "referral_number": "REF654321", "report_information": [{"attachment_report_type_code": "07", "attachment_transmission_code": "BM", "attachment_control_number": "ACN123456"}]}], "claim_info_other": [{"claim_date_information": [{"admission_date": "06/01/2023", "discharge_date": "06/05/2023", "statement_from_date": "06/01/2023", "statement_to_date": "06/05/2023", "symptom_date": "01/22/2021", "acute_manifestation_date": "05/18/2000", "last_worked_date": "04/12/2024", "authorized_return_to_work_date": "12/01/2024"}], "claim_contract_information": [{"contract_type_code": "01", "contract_amount": 5000.0, "contract_percentage": 80, "terms_discount_percentage": 5.24}], "condition_information": [{"condition_codes": ["04"]}, {"condition_codes": ["04", "05"]}], "file_information_list": [{"file": "patient_records.pdf", "comments": "Patient medical history"}, {"file": "lab_results.pdf", "comments": "Recent lab test results"}, {"file": "xray_images.jpg", "comments": "X-ray images from recent examination"}]}]}], "providers": [{"billing": [{"site_id": 1, "provider_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "organization_name": "Sample Pharmacy", "npi": "**********", "employer_identification_number": "12-3456789", "commercial_number": "COM123456", "state_license_number": "SL987654", "contact_information": [{"name": "<PERSON>", "email": "<EMAIL>", "phone_number": "(*************", "fax_number": "(*************"}], "address": [{"address1": "443 1st 2nd St", "address2": "Suite 100", "city": "Anytown", "state": "CA", "postal_code": "12345"}]}], "referring": [{"patient_id": 1001, "prescriber_id": 2001, "physician_id": 3001, "provider_type": "ReferringProvider", "last_name": "<PERSON>", "first_name": "<PERSON>", "npi": "**********", "commercial_number": "COM987654", "state_license_number": "SL123456", "payer_identification_number": "PIN789012", "taxonomy_code": "207Q00000X", "contact_information": [{"name": "<PERSON>", "phone_number": "************", "email": "<EMAIL>", "fax_number": "(*************"}], "address": [{"address1": "789 Elm Street", "address2": "Unit 456", "city": "Metropolis", "state": "NY", "postal_code": "10001"}]}], "ordering": [{"patient_id": 1001, "prescriber_id": 2001, "physician_id": 3001, "provider_type": "OrderingProvider", "last_name": "<PERSON>", "first_name": "<PERSON>", "npi": "**********", "commercial_number": "COM654321", "state_license_number": "SL789012", "payer_identification_number": "PIN345678", "taxonomy_code": "207RC0000X", "contact_information": [{"name": "<PERSON>", "phone_number": "************", "email": "<EMAIL>", "fax_number": "(*************"}], "address": [{"address1": "456 Oak Avenue", "address2": "Suite 789", "city": "Springfield", "state": "IL", "postal_code": "62701"}]}], "rendering": [{"organization_name": "Sample Medical Center", "npi": "**********", "commercial_number": "COM123456", "state_license_number": "SL987654", "contact_information": [{"name": "<PERSON>", "phone_number": "************", "email": "<EMAIL>", "fax_number": "************"}], "address": [{"address1": "123 Main St", "address2": "Suite 456", "city": "Anytown", "state": "CA", "postal_code": "12345"}]}], "supervising": [{"patient_id": 1001, "prescriber_id": 2001, "physician_id": 3001, "provider_type": "SupervisingProvider", "last_name": "<PERSON>", "first_name": "<PERSON>", "npi": "**********", "commercial_number": "COM123456", "state_license_number": "SL987654", "payer_identification_number": "PIN654321", "taxonomy_code": "207Q00000X", "contact_information": [{"name": "<PERSON>", "email": "<EMAIL>", "phone_number": "************", "fax_number": "************"}], "address": [{"address1": "789 Elm Street", "address2": "Suite 101", "city": "Metropolis", "state": "NY", "postal_code": "10001"}]}]}], "expected": 1000.0, "paid": 950.0, "copay": 50.0, "tax": 10.0, "cost": 800.0, "void": null, "void_reason_id": null, "voided_datetime": null}