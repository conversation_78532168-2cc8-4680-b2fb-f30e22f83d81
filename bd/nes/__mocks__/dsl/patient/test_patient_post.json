{"status_id": "1", "status_id_auto_name": "1 - Pending", "patient_tag_id": [], "patient_tag_id_auto_name": [], "pmp_benefits_optout": "Opt-In", "firstname": "Test", "lastname": "UserRandom", "middlename": "", "ssn": "", "external_ids": [], "site_id": 107, "site_id_auto_name": "Dallas - TX DTX", "dob": "10/10/1921", "gender": "Female", "identify_gender": "Same as birth", "height": "", "weight": "", "ibw": "", "lbw": "", "bsa": "", "language": "English", "diabetic": "", "diet_id": [], "diet_id_auto_name": [], "activity_id": [], "activity_id_auto_name": [], "advanced_directive": "", "code_status": "N/A", "power_of_attorney": "", "living_will": "", "home_street": "", "home_street2": "", "home_zip": "", "home_city": "", "home_state_id": "", "ship_location": "", "ship_sameas": "", "contact": [], "phone_cell": "", "phone_home": "", "phone_work": "", "phone_primary": "", "team_id": "", "facility_id": "", "user_id": "", "email": "", "is_test": "", "is_prn": "", "category_id": "", "referral_date": "04/24/2024", "referral_source_id": "", "primary_physician_id": "", "referrer_id": "", "territory_id": "", "clinical_alert": "", "billing_alert": "", "_meta": {"source": "patient", "text": "Chart State:\n  Patient Chart Status: 1 - Pending\n  Patient understands the benefits and wish to Opt-In/Opt-Out of Patient Management Program?: Opt-In\n\nName:\n  First Name: Test\n  Last Name: User\n\nIdentity:\n  Site: Dallas - TX DTX\n  Date of Birth: 10/10/1921\n  Sex at birth: Female\n  Sex identify as: Same as birth\n  Language: English\n  Code Status: N/A\n\nService:\n  Referral Date: 04/24/2024", "tzoffset": 300, "client": {"created_by": 22, "created_by_auto_name": "", "created_on": "04/24/2024 13:11:22"}, "clear_fields": {"patient": ["cancellation_reason", "death_date", "death_cause", "death_confirm", "code_status_other", "power_of_attorney_details", "ship_type", "ship_to", "ship_street", "ship_street2", "ship_city", "ship_state_id", "ship_zip", "is_prn_set_date"]}, "server": {"null_missing_fields": true}}}