{"transaction_code": "B1", "patient": {"id": 1, "firstname": "<PERSON>", "lastname": "<PERSON><PERSON>", "dob": "01/01/1990", "gender": "Male", "home_street": "123 Main St", "home_street2": "Apt 4B", "home_city": "Anytown", "home_state": "CA", "home_zip": "12345", "phone_cell": "555-1234", "phone_home": "555-5678", "phone_work": "555-9012", "email": "<EMAIL>", "employer_id": "ABC123"}, "insurance": {"id": 2, "payer_id": 3, "payer_type_id": 4, "patient_id_qualifier": "01", "patient_claim_id": "987654321", "medicaid_number": "MCD123456", "medicare_number": "MCR987654", "employer_id": "EMP12345", "pharmacy_relationship_id": "01"}, "payer": {"default_service_place_id": "01", "default_place_of_res_id": "01", "send_pt_email": "Yes"}, "pregnancy_indicator": null}