{"transaction_code": "B1", "insurance": {"id": 1}, "payer": {"id": 2, "ncpdp_sec_plan_type_id": "01", "ncpdp_sec_plan_qualifier_id": "03", "ncpdp_send_primary_payer_amt_paid": "Yes", "ncpdp_pri_payer_qualifier_id": "07", "ncpdp_pt_paid_amount_dx": "Yes", "ncpdp_sec_claims_pr_qualifier_id": "06", "ncpdp_sec_claims_benefit_qual_id": "01"}, "opayer": {"hp_id": "123456", "mac_id": "ABCDEF", "medicaid_id": "MCD123", "hin_id": "456789", "bin": "012345", "naic_id": "67890", "ncpdp_sec_plan_id": "PLAN123", "ncpdp_sec_payer_name": "Secondary Payer"}, "parent_claim": {"subform_response": [{"response_stat": {"transaction_response_status": "R", "subform_reject": [{"other_reject_code": ["05", "06", "07", "08"]}]}, "created_on": "2023-05-01T12:00:00Z", "segment_pricing": {"total_paid": 100.0, "pt_pay_amt": 20.0}}], "segment_cob": [{"other_reject_code": ["01", "02", "03", "04"]}]}, "list_reject_codes": [{"code": "01", "description": "Reject Code 1"}, {"code": "03", "description": "Reject Code 3"}, {"code": "05", "description": "Reject Code 5"}, {"code": "07", "description": "Reject Code 7"}]}