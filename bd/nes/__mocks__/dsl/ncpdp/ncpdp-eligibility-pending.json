{"claim_no": "21310230", "id": 185, "site_id": 109, "patient_id": 1667, "insurance_id": 182, "payer_id": 87, "status": "Pending", "pt_rel_code": "1", "version_number": "D0", "transaction_code": "E1", "bin_number": "015814", "process_control_number": "06430000", "svc_prov_id_qualifier": "01", "svc_prov_id": "**********", "date_of_service": "07/25/2024", "segment_patient": [{"patient_id": 1667, "insurance_id": 182, "transaction_code": "E1", "pt_rel_code": "1", "payer_type_id": "CMPBM", "patient_id_qualifier": "EA", "patient_date_of_birth": "10/30/1981", "patient_first_name": "<PERSON>", "patient_last_name": "<PERSON><PERSON>", "patient_street_address": "105 Rosadi Cv null", "patient_city_address": "Georgetown", "patient_zip": "78628", "place_of_service": "12", "patient_claim_id": "0008", "patient_gender_code": "1", "created_on": "2024-07-25 12:15:34", "created_by": 1, "auto_name": ""}], "segment_pharmacy": [{"provider_id": "**********", "provider_id_qualifier": "05", "created_on": "2024-07-25 12:15:34", "created_by": 1, "auto_name": ""}], "segment_prescriber": [{"physician_id": 11960, "primary_physician_id": 11960, "transaction_code": "E1", "pt_rel_code": "1", "payer_type_id": "CMPBM", "dr_last_name": "NCPDP-Test", "dr_first_name": "Physician", "pri_dr_last_name": "NCPDP-Test", "pri_dr_id_qualifier": "01", "pri_dr_id": "**********", "dr_id_qualifier": "01", "dr_id": "**********", "created_on": "2024-07-25 12:15:34", "created_by": 1, "auto_name": ""}], "segment_insurance": [{"insurance_id": 182, "transaction_code": "E1", "pt_rel_code": "1", "payer_type_id": "CMPBM", "card_holder_id": "YXF128085604001", "card_holder_first_name": "<PERSON>", "created_on": "2024-07-25 12:15:34", "created_by": 1, "auto_name": ""}], "created_on": "2024-07-25 12:15:34", "created_by": 1, "auto_name": "Pending DT:07/25/2024 PT: PY: ITM: PD:$", "is_test": "Yes"}