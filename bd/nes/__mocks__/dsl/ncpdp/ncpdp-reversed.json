{"_meta": {"source": "ncpdp", "subform": {"segment_patient": "ncpdp_patient", "segment_pharmacy": "ncpdp_pharmacy", "segment_prescriber": "ncpdp_prescriber", "segment_insurance": "ncpdp_insurance", "segment_cob": "ncpdp_cob", "segment_workers_comp": "ncpdp_worker_comp", "segment_claim": "ncpdp_claim", "segment_dur": "ncpdp_dur", "segment_coupon": "ncpdp_coupon", "segment_compound": "ncpdp_compound", "segment_pricing": "ncpdp_pricing", "segment_clinical": "ncpdp_clinical", "segment_docs": "ncpdp_doc", "segment_facility": "ncpdp_facility", "segment_narrative": "ncpdp_narrative", "subform_response": "ncpdp_response"}}, "segment_patient": [{"_meta": {"source": "ncpdp_patient", "subform": {}}, "id": 185, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "transaction_code": "B1", "patient_id": 1430, "insurance_id": 148, "payer_type_id": "CMPBM", "patient_id_qualifier": "EA", "patient_gender_code": "2", "patient_state": null, "place_of_service": null, "patient_residence": null, "smoker_code": null, "pregnancy_indicator": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "07/06/2024 20:56:27", "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:26", "change_on": null, "pt_rel_code": "1", "patient_claim_id": "11111", "patient_date_of_birth": "10/11/1952", "patient_first_name": "<PERSON>", "patient_last_name": "Whiteside", "employer_id": null, "patient_phone": null, "patient_email_address": null, "patient_street_address": "105 Rosadi Cv null", "patient_city_address": "Georgetown", "patient_zip": "78628", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_code_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "insurance_id_auto_name": "3 : ILDR - Prime-011552-ILDR  - OON : Commercial PBM", "payer_type_id_auto_name": "Commercial PBM", "patient_id_qualifier_auto_name": null, "patient_gender_code_auto_name": null, "patient_state_auto_name": null, "place_of_service_auto_name": null, "patient_residence_auto_name": null, "pregnancy_indicator_auto_name": null, "external_id": null, "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}], "segment_pharmacy": [], "segment_prescriber": [{"_meta": {"source": "ncpdp_prescriber", "subform": {}}, "id": 185, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "pt_dr_id": null, "physician_id": 11860, "primary_dr_id": null, "primary_physician_id": 11860, "transaction_code": "B1", "dr_id_qualifier": "01", "pri_dr_id_qualifier": "01", "dr_state": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "07/06/2024 20:56:27", "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:26", "change_on": null, "dr_id": "**********", "dr_last_name": "<PERSON>", "dr_first_name": "Walden", "dr_phone": null, "pri_dr_id": "**********", "pri_dr_last_name": "<PERSON>", "dr_street_address": null, "dr_city": null, "dr_zip": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_code_auto_name": null, "physician_id_auto_name": "********** <PERSON>, Walden NP", "primary_physician_id_auto_name": "********** <PERSON>, Walden NP", "dr_id_qualifier_auto_name": null, "pri_dr_id_qualifier_auto_name": null, "dr_state_auto_name": null, "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}], "segment_insurance": [{"_meta": {"source": "ncpdp_insurance", "subform": {}}, "id": 185, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "transaction_code": "B1", "insurance_id": 148, "payer_type_id": "CMPBM", "pt_rel_code": "1", "elig_clar_code": null, "mcd_indicator": null, "dr_accept_indicator": null, "partd_facility": "Y", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "07/06/2024 20:56:27", "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:26", "change_on": null, "card_holder_id": "1111111", "card_holder_first_name": "<PERSON>", "card_holder_last_name": null, "plan_id": null, "group_id": null, "person_code": null, "medigap_id": null, "mcd_id_no": null, "mcd_agcy_no": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_code_auto_name": null, "insurance_id_auto_name": "3 : ILDR - Prime-011552-ILDR  - OON : Commercial PBM", "payer_type_id_auto_name": "Commercial PBM", "pt_rel_code_auto_name": null, "elig_clar_code_auto_name": null, "mcd_indicator_auto_name": null, "dr_accept_indicator_auto_name": null, "partd_facility_auto_name": null, "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}], "segment_cob": [], "segment_workers_comp": [], "segment_claim": [{"_meta": {"source": "ncpdp_claim", "subform": {}}, "id": 185, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "rx_svc_no_ref_qualifier": "1", "patient_id": 1430, "careplan_order_id": null, "insurance_id": 148, "careplan_order_item_id": null, "transaction_code": "B1", "product_id": 40, "service_id": null, "prod_svc_id_qualifier": "03", "daw_code": "0", "unit_of_measure": null, "admin_route": "445755006", "og_product_id": null, "og_service_id": null, "og_rx_id_qualifier": null, "pa_id": null, "pa_type_code": null, "compound_code": "1", "compound_type": null, "rx_origin_code": null, "other_coverage_code": null, "sp_pk_indicator": null, "level_of_service": null, "dispensing_status": null, "delay_reason_code": null, "pt_assign_indicator": "Y", "pharmacy_service_type": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "07/06/2024 20:56:27", "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:26", "change_on": null, "sched_rx_no": null, "rx_svc_no": 63362605, "fill_number": 1, "product_service_id": null, "quantity_dispensed": 300, "days_supply": null, "number_of_refills_authorized": 0, "date_rx_written": "07/06/2024", "quantity_prescribed": null, "qty_to_disp": null, "ds_to_disp": null, "og_rx_id": null, "og_rx_quantity": null, "pa_no_submitted": null, "associated_rx_svc_no": null, "associated_rx_service_date": null, "day_supply": 30, "prod_svc_id": "00944270002", "order_id": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_code_auto_name": null, "rx_svc_no_ref_qualifier_auto_name": null, "patient_id_auto_name": "11111 <PERSON>side", "order_id_auto_name": null, "insurance_id_auto_name": "3 : ILDR - Prime-011552-ILDR  - OON : Commercial PBM", "product_id_auto_name": "GAMMAGARD LIQUID 10% VIAL Drug", "service_id_auto_name": null, "prod_svc_id_qualifier_auto_name": null, "daw_code_auto_name": null, "unit_of_measure_auto_name": null, "admin_route_auto_name": null, "og_product_id_auto_name": null, "og_service_id_auto_name": null, "og_rx_id_qualifier_auto_name": null, "pa_id_auto_name": null, "pa_type_code_auto_name": null, "compound_code_auto_name": null, "compound_type_auto_name": null, "rx_origin_code_auto_name": null, "other_coverage_code_auto_name": null, "sp_pk_indicator_auto_name": null, "level_of_service_auto_name": null, "dispensing_status_auto_name": null, "delay_reason_code_auto_name": null, "pt_assign_indicator_auto_name": null, "pharmacy_service_type_auto_name": null, "external_id": null, "proc_mod_code": [], "proc_mod_code_auto_name": [], "sub_clar_code": [], "sub_clar_code_auto_name": [], "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}], "segment_dur": [], "segment_coupon": [], "segment_compound": [], "segment_pricing": [{"_meta": {"source": "ncpdp_pricing", "subform": {"subform_oclaim": "ncpdp_pricing_cob"}}, "subform_oclaim": [], "id": 185, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "transaction_code": "B1", "payer_id": 8, "sales_tax_basis": null, "cost_basis": "00", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "07/06/2024 20:56:27", "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:26", "change_on": null, "ing_cst_sub": 2135.7, "disp_fee_sub": null, "pro_svc_fee_sub": null, "incv_amt_sub": null, "pt_pd_amt_sub": null, "medicaid_paid_amount": null, "u_and_c_charge": 2135.7, "flat_tax_amt": null, "per_sales_tax": null, "per_sales_tax_rate_used": null, "gross_amount_due": 2135.7, "tax_codes_id": null, "parent_claim_id": null, "tax_code_id": null, "sales_tax": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_code_auto_name": null, "payer_id_auto_name": "ILDR - Prime-011552-ILDR  - OON", "tax_code_id_auto_name": null, "sales_tax_basis_auto_name": null, "cost_basis_auto_name": null, "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}], "segment_clinical": [], "segment_docs": [], "segment_facility": [], "segment_narrative": [], "subform_response": [{"_meta": {"source": "ncpdp_response", "subform": {"response_msg": "ncpdp_response_msg", "response_insur": "ncpdp_response_insur", "response_docs": "ncpdp_response_insur_add", "response_pat": "ncpdp_response_pt", "response_stat": "ncpdp_response_stat", "response_claim": "ncpdp_response_clm", "response_pricing": "ncpdp_response_prc", "response_dur": "ncpdp_response_dur", "response_cob": "ncpdp_response_cob"}}, "response_msg": [{"_meta": {"source": "ncpdp_response_msg", "subform": {}}, "id": 30, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "message": "ERX88448: M/I Patient Cardholder ID", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 32, "parent_form": "ncpdp_response", "parent_archived": null}], "response_insur": [], "response_docs": [], "response_pat": [], "response_stat": [{"_meta": {"source": "ncpdp_response_stat", "subform": {"subform_reject": "ncpdp_response_stat_rej", "subform_msg": "ncpdp_response_stat_msg"}}, "subform_reject": [{"_meta": {"source": "ncpdp_response_stat_rej", "subform": {}}, "id": 27, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "other_reject_code": ["594"], "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "reject_field_occ_ind": null, "reject_code": "{NN}", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "reject_code_auto_name": null, "parent_id": 27, "parent_form": "ncpdp_response_stat", "parent_archived": null}], "subform_msg": [{"_meta": {"source": "ncpdp_response_stat_msg", "subform": {}}, "id": 27, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "add_msg_qualifier": "02", "add_msg_cont": "+", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "add_msg": "ERX88448: CHID must begin with a '1' and be 9 digits long.", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "add_msg_qualifier_auto_name": null, "add_msg_cont_auto_name": null, "parent_id": 27, "parent_form": "ncpdp_response_stat", "parent_archived": null}], "id": 27, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "transaction_response_status": "R", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "help_phone": null, "authorization_number": null, "transaction_ref_no": null, "internal_control_no": null, "url": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_response_status_auto_name": null, "approved_msg": [], "approved_msg_auto_name": [], "help_qual": [], "help_qual_auto_name": [], "parent_id": 32, "parent_form": "ncpdp_response", "parent_archived": null}], "response_claim": [{"_meta": {"source": "ncpdp_response_clm", "subform": {"subform_pref_prod": "ncpdp_response_clm_pref"}}, "subform_pref_prod": [], "id": 27, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "rx_svc_no_ref_qualifier": "1", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "rx_svc_no": 63362605, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "rx_svc_no_ref_qualifier_auto_name": null, "parent_id": 32, "parent_form": "ncpdp_response", "parent_archived": null}], "response_pricing": [{"pt_copay_amt": 25.0}], "response_dur": [], "response_cob": [], "id": 32, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "response_status": "A", "version_number": "D0", "service_provider_qualifier": "01", "transaction_code": "B1", "service_provider_id": "**********", "date_of_service": "07/25/2024", "show_segments": [], "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "response_status_auto_name": null, "version_number_auto_name": null, "service_provider_qualifier_auto_name": null, "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}, {"_meta": {"source": "ncpdp_response", "subform": {"response_msg": "ncpdp_response_msg", "response_insur": "ncpdp_response_insur", "response_docs": "ncpdp_response_insur_add", "response_pat": "ncpdp_response_pt", "response_stat": "ncpdp_response_stat", "response_claim": "ncpdp_response_clm", "response_pricing": "ncpdp_response_prc", "response_dur": "ncpdp_response_dur", "response_cob": "ncpdp_response_cob"}}, "response_msg": [{"_meta": {"source": "ncpdp_response_msg", "subform": {}}, "id": 31, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "message": "ERX88448: M/I Patient Cardholder ID", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "parent_id": 33, "parent_form": "ncpdp_response", "parent_archived": null}], "response_insur": [], "response_docs": [], "response_pat": [], "response_stat": [{"_meta": {"source": "ncpdp_response_stat", "subform": {"subform_reject": "ncpdp_response_stat_rej", "subform_msg": "ncpdp_response_stat_msg"}}, "subform_reject": [{"_meta": {"source": "ncpdp_response_stat_rej", "subform": {}}, "id": 28, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "other_reject_code": ["594"], "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "reject_field_occ_ind": null, "reject_code": "{NN}", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "reject_code_auto_name": null, "parent_id": 28, "parent_form": "ncpdp_response_stat", "parent_archived": null}], "subform_msg": [{"_meta": {"source": "ncpdp_response_stat_msg", "subform": {}}, "id": 28, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "add_msg_qualifier": "02", "add_msg_cont": "+", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "add_msg": "ERX88448: CHID must begin with a '1' and be 9 digits long.", "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "add_msg_qualifier_auto_name": null, "add_msg_cont_auto_name": null, "parent_id": 28, "parent_form": "ncpdp_response_stat", "parent_archived": null}], "id": 28, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "transaction_response_status": "R", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "help_phone": null, "authorization_number": null, "transaction_ref_no": null, "internal_control_no": null, "url": null, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "transaction_response_status_auto_name": null, "approved_msg": [], "approved_msg_auto_name": [], "help_qual": [], "help_qual_auto_name": [], "parent_id": 33, "parent_form": "ncpdp_response", "parent_archived": null}], "response_claim": [{"_meta": {"source": "ncpdp_response_clm", "subform": {"subform_pref_prod": "ncpdp_response_clm_pref"}}, "subform_pref_prod": [], "id": 28, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "rx_svc_no_ref_qualifier": "1", "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "rx_svc_no": 63362605, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "rx_svc_no_ref_qualifier_auto_name": null, "parent_id": 33, "parent_form": "ncpdp_response", "parent_archived": null}], "response_pricing": [{"pt_copay_amt": 25.0}], "response_dur": [], "response_cob": [], "id": 33, "created_by": 22, "change_by": null, "updated_by": null, "reviewed_by": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": null, "auto_name": null, "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:27", "change_on": null, "response_status": "A", "version_number": "D0", "service_provider_qualifier": "01", "transaction_code": "B1", "service_provider_id": "**********", "date_of_service": "07/25/2024", "show_segments": [], "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "response_status_auto_name": null, "version_number_auto_name": null, "service_provider_qualifier_auto_name": null, "parent_id": 185, "parent_form": "ncpdp", "parent_archived": null}], "id": 185, "created_by": 22, "change_by": null, "updated_by": 22, "reviewed_by": null, "site_id": 109, "patient_id": 1430, "insurance_id": 148, "payer_id": 8, "pt_rel_code": "1", "version_number": "D0", "transaction_code": "B1", "svc_prov_id_qualifier": "07", "comp_dsg_fm_code": null, "comp_disp_unit": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "07/06/2024 20:56:27", "auto_name": "Reversed -  DT:07/06/2024 PT:11111 Kara Whiteside PY:ILDR - Prime-011552-ILDR  - OON ITM:GAMMAGARD LIQUID 10% VIAL Drug PD:$", "change_type": null, "change_data": null, "created_on": "07/06/2024 20:56:26", "change_on": null, "description": null, "partial_fill_status": null, "status": "Reversed", "software_vendor_id": null, "bin_number": "444444", "process_control_number": "555555", "svc_prov_id": "1122999", "date_of_service": "07/25/2024", "careplan_order_id": null, "careplan_order_item_id": null, "never_comp_seg": null, "inventory_id": 40, "substatus_id": null, "claim_no": "000185", "paid": null, "cost": null, "brand_group_id": null, "is_test": "Yes", "void_reason_id": null, "trans_show_comp": null, "lineitem_no": null, "parent_claim_no": null, "void": null, "voided_datetime": null, "expected": null, "tax": null, "ready_to_bill": null, "order_id": null, "order_item_id": null, "prescriber_id": null, "quantity": null, "original_prescriber_id": null, "original_inventory_id": null, "original_quantity": null, "optional_segments": null, "addtl_segments": [], "rx_no": null, "copay": null, "patch_applied": null, "test_bill_line": null, "is_payable": "Yes", "close_id": null, "test_charge_line": {"id": 2, "ready_to_bill": "Yes", "bill_type": "Dispense", "is_compound": null, "patient_id": 1430, "order_id": 1, "site_id": 2, "order_item_id": 157, "insurance_id": 182, "pa_id": null, "date_of_service": "07/22/2024", "charge_quantity": 20, "charge_unit": "100 ML", "billing_unit_id": "mL", "fill_number": 1, "day_supply": 30, "dispense_fee": null, "expected": 2000.0, "calc_expected_ea": 15.0, "expected_ea": 15.0, "list": 2000.0, "list_ea": 15.0, "cost": 2000.0, "cost_ea": 15.0, "taxable": null, "tax_codes_id": null, "flat_tax_amt": null, "sales_tax": null, "per_sales_tax_rate_used": null, "sales_tax_basis": null, "subform_ingred": []}, "created_by_auto_name": null, "change_by_auto_name": null, "updated_by_auto_name": null, "reviewed_by_auto_name": null, "close_id_auto_name": null, "order_id_auto_name": null, "order_item_id_auto_name": null, "inventory_id_auto_name": "GAMMAGARD LIQUID 10% VIAL Drug", "brand_group_id_auto_name": null, "void_reason_id_auto_name": null, "site_id_auto_name": "Branson - TX BTX", "patient_id_auto_name": "11111 <PERSON>side", "insurance_id_auto_name": "3 : ILDR - Prime-011552-ILDR  - OON : Commercial PBM", "payer_id_auto_name": "ILDR - Prime-011552-ILDR  - OON", "pt_rel_code_auto_name": null, "version_number_auto_name": null, "transaction_code_auto_name": null, "svc_prov_id_qualifier_auto_name": null, "comp_dsg_fm_code_auto_name": null, "comp_disp_unit_auto_name": null, "external_id": null, "addtl_segments_auto_name": [], "parent_id": 1, "parent_form": null, "parent_archived": null}