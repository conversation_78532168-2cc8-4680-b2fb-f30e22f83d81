// Import service configuration from shared config
import { createServiceConfig } from "@clara/config/eslint/service.js";
import { applySecurityRules } from "@clara/config/eslint/security.js";
import globals from "globals";

// Since this is a JavaScript-only project, we use an empty array
const projects = [];

// Get the service configuration with security enhancements
const baseConfig = applySecurityRules(createServiceConfig(projects));

// Add module configuration to the base config
const configWithModuleSettings = baseConfig.map((config) => {
    if (config.languageOptions) {
        return {
            ...config,
            languageOptions: {
                ...config.languageOptions,
                sourceType: "module", // Set sourceType to module for ESM
            },
        };
    }
    return config;
});

// Customize the configuration for this project
const customConfig = configWithModuleSettings.map((config) => {
    // Add node and jest globals to JavaScript files
    if (config.files && config.files.includes("**/*.js")) {
        return {
            ...config,
            languageOptions: {
                ...config.languageOptions,
                globals: {
                    ...config.languageOptions?.globals,
                    ...globals.node,
                    ...globals.jest,
                },
            },
        };
    }

    // Add specific ignores
    if (config.ignores) {
        return {
            ...config,
            ignores: [
                ...config.ignores,
                "**/__mocks__/**/*.json",
                "**/schemas/**/*.json",
                "**/*map.json",
                "**/sharedState.json",
            ],
        };
    }

    return config;
});

// Add custom rules specific to this project
customConfig.push({
    files: ["**/*.js"],
    rules: {
        "constructor-super": "warn",
        "no-console": "off",
        "no-const-assign": "error",
        "no-new-require": "error",
        "no-process-exit": "off",
        "no-prototype-builtins": "off",
        "no-sync": "off",
        "no-this-before-super": "warn",
        "no-undef": "error",
        "no-unreachable": "error",
        "no-unused-vars": "off",
        "no-var": "warn",
        "prefer-const": "warn",
        "valid-typeof": "warn",
        // Allow redeclare for CommonJS requires
        "no-redeclare": "off",
    },
});

export default customConfig;
