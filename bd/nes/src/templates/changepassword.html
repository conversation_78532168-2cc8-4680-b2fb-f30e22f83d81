{% extends "layout.html" %}
{% block body %}
  <div id="container">
    <a id="logo_link" href="/login"><div id="logo"></div></a>
    <form id="pass_form" action="javascript:set_password();" method="POST">
      <h3>{{ user }}</h3>
      {% if expired %}
        <input id="passwordreset" type="button" value="Click to resend new link" onclick="window.location='/login/passwordreset/';"/>
      {% else %}
        <div id="password-checks">
          <div id='check-match' class='password-check'>Passwords must match</div>
        </div>
        <input id="password" type="password" name="password" placeholder="New Password"/>
        <input id="confirmpassword" type="password" name="confirmpassword" placeholder="Confirm Password"/>
        <input id="submit" type="submit" value="Change Password"/>
      {% endif %}
    </form>
  </div>
  <script>
    function set_password() {
      formData = new FormData(document.forms[0]);
      password = formData.get('password')
      confirmpassword = formData.get('confirmpassword')
      token = "{{ token | escape('js') }}";
      jQuery.ajax({
        type: "POST",
        url: "/login/setpassword/" + token,
        data: JSON.stringify({ password: password, password2: confirmpassword }),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (res) {
          console.log("res of set/pass: ", res)
          if(res.hasOwnProperty('success')){
            window.location = window.location.protocol + "//" + window.location.host + '/login'
          }
        },
        error: function (jqXHR, status, error) {
          if (jqXHR.status == 501) {
            set_message("Internal Server Error", 'danger')
          } else if (jqXHR.state == 404) {
            set_message("Internal server configuration error, please contact IT support", 'error')
          }
        }
      });
    }
  </script>
{% endblock %}
