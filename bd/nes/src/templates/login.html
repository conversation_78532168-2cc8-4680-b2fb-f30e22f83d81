{% extends "layout.html" %}

{% block body %}
<div id="container">
  <a id="logo_link" href="/login"><div id="logo"></div></a>
  <form id="login_form" action="javascript:verify_credentials();" method="POST">
    <div id="login_oauth">
        {% if oauth_providers.includes("AZ") or
            (oauth_providers.includes("CR") and override_sso_cr) or
            oauth_providers.includes("GC") %}
        <div id="login_email">Login with your company email:</div>
        {% endif %}
        {% if oauth_providers.includes("AZ") %}
        <input id="oauth_az" class="oauth_btn" type="button" value="Login with O365" onclick="window.location='/login/oauth/az';" />
        {% endif %}
        {% if oauth_providers.includes("CR") and override_sso_cr %}
        <input id="oauth_cr" class="oauth_btn" type="button" value="Login with Google" onclick="window.location='/login/oauth/cr';" />
        {% elseif oauth_providers.includes("GC") %}
        <input id="oauth_gc" class="oauth_btn" type="button" value="Login with Google" onclick="window.location='/login/oauth/gc';" />
        {% endif %}
    </div>
    <div id="login_local">
        <div id="login_account">Login with Clara account:</div>
        <div id="alerts">{% if error %}
            <div class="alert alert-danger">{{ error }}</div>
        {% endif %}</div>
        <div id="user">
            <input id="username" type="text" name="username" placeholder="Username" />
        </div>
        <div id="pass">
            <input id="password" type="password" name="password" placeholder="Password" />
        </div>
        <input id="submit" type="submit" value="Login with username" />
    </div>
  </form>
  <div id="#footer_link">
    <a id="passwordreset" href="/login/passwordreset">Forgot Password?</a>
</div>
</div>
<script>
    $(() => {
        $('#submit').on('click', (e) => {
            if(e.shiftKey) {
                e.preventDefault();
                window.location.href = '/login/oauth/cr';
            }
        });
    });

    function verify_credentials() {
        formData = new FormData(document.forms[0]);
        username = formData.get('username').toLowerCase()
        passwd = formData.get('password')
        $.ajax({
            type: "POST",
            url: "/api/auth/",
            data: JSON.stringify({ username: username, password: passwd }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (res) {
                if (res.hasOwnProperty('portal')) {
                    window.location = window.location.protocol + "//" + window.location.host + res.portal
                }
            },
            error: function (jqXHR, status, error) {
                if (jqXHR.status == 401) {
                    set_message("Invalid username or password", 'danger')
                } else if (jqXHR.state == 404) {
                    set_message("Internal server configuration error, please contact IT support", 'error')
                }
            }
        });
    }
</script>
{% endblock %}