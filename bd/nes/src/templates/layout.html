<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Clara: Login</title>
  <meta name="description" content="Clara Homebase">
  <meta name="author" content="Clara">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <!-- I was sleep deprived and hungry, i wasn't able to get favicon.ico to update, even after changing the original file. so i made this base64 encoded png. Sorry!-->
  <link rel="shortcut icon" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAjCAMAAAAkGTMsAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAB41BMVEUAAAD7q2j6qGb7qWn6qWj7qmf7qWn6qmr/r3D7q2j8qWj6qmj/p2j6qmf3p2j8qWn7qWn8qmj7qWj7qGj7p2j7qWfwiULwiUP7qmjvh0Hvh0L8qWbvj0DvikDviEL7p2jwiELviUP6qmXvh0DvhkL6qGnviULwiUL0kU74oF77qWj6p2byomzVm47dn4jloYDZln+cg8GAgemBgemCg+qDg+qSidrOm5nEiId2d+hzdOd1ded6e+h+f+mBguqDhOq3lLHzp3Dhi15rbOZlZuVjZORkZeVqa+Zyc+d7fOjspXnyjkqYcqhfYORcXeNbXONeX+NtbuZ4eOh/gOnlooDwiEJnYdlaW+NhYuRqa+V1dufNnqLdg1ZfYONnaOV9fumNj+zb2/fxyLHxikR3eOjR0vXm5/nn6Pnr3N75sXrxjEeCb8RdXuO5uvHj4/jl5fjp4OfQjX2lpu7f4Pfh4ffi4/jo5PD1vZX1llOlcpKdnu3g4ffh4/jk5fjm5vn3tYOudIjf4ffh4vfj5Pjs2NXUgGBtYc+Vluvh4vjdglaMjerl5vn2uYykd53otJz5rXHro3rg2+zuz8Luk1nh1eHn3ub4sXrpy8Hp1dTzkEztonTmuafjysnrxrb1t4z1mVX////MjItcAAAAKnRSTlMAQHCv3+/PMBB/32AgbyBQ75+AkEDP78+/oGBQEDCQgN9QMCBwcHCvz+9A+u8ZAAAAAWJLR0SgXtO+oAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+kDGxMmNbYqEIoAAAHsSURBVDjLldT5V9NAEAfwoTS1plioaDmkNgIepKIItWIRq3J7NPVY44UKaCKggiyoWOoBiieieIIo/6ppks0mS/qefn/+vJnZfbML8N8p8hR7OVHkfMWeDQWIf6NXtIUPlLigTZzIhA+wJugTXcI7yxVxomt4+3ilYqGUURbkxMKMNA2aZ4vtbdy3f31Tv6FChmk60NwSP5g4xLLNRikDtR5OJtuOtB9NHTvOsBKrVOxER2dXV3dPb19zKnHSqcpJqdip02fSkpTOdJ89dz6euOA8gDZZQEfooiTnk750+Up7PHXVwbboDTV0zUCyLPVfT964OTDItPTlERqSSaRM563binpnmKqtAFweoZFRi8l3790fU9RxyngAHSH0gKoJPDmlsYePrPEBHusIjUwT9ARjnJ3JPVXVZ4QBhJGZ5xRh/GJ2TlFf0o4VRKFX8xp6jc28ySmqOVolQJWl0Nt5+R1BODv1Xv1g3UQ1smUB08zmFFNtA6gJ29lHqrI5ZZEc0dFSy6fPVC0ZpUJ5FXEo9OWrqWa+fbdtDmxn2A8dTf40UdTY1RrByZZXfq2u/v7DbDREwojNGrl2jj7JHYVRre1BRoR/QAB1ghuqXPeh1Ass4na6fDp11YId+aJ+cE9k1+49DVoRb8jjHOgv6A0CDIvnZ/QAAAAldEVYdGRhdGU6Y3JlYXRlADIwMjUtMDMtMjdUMTk6Mzg6MjkrMDA6MDCKlPDsAAAAJXRFWHRkYXRlOm1vZGlmeQAyMDI1LTAzLTI3VDE5OjM4OjI5KzAwOjAw+8lIUAAAAABJRU5ErkJggg==" type="image/x-icon"> 
  <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://code.jquery.com/jquery-1.12.4.min.js"
  integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ=" crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>

  <style>
    @font-face {
        font-family: 'Inter';
        src: url("/homebase/public/fonts/Inter-Medium.ttf") format('truetype');
        /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-weight: bold;
    }

    @font-face {
        font-family: 'Inter';
        src: url("/homebase/public/fonts/Inter-Regular.ttf") format('truetype');
        /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-weight: normal;
    }

    @font-face {
        font-family: 'Inter';
        src: url("/homebase/public/fonts/Inter-Light.ttf") format('truetype');
        /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
        font-weight: lighter;
    }

    html, body {
        height:100%;
        margin:0;
    }

    body {
        background: #f1e8e3;
        font-family: "Inter", "Helvetica Neue", Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        display:flex;
        justify-content: center;
        align-items: center;
    }

    #container {
        background:#f9fcfd;
        width: 360px;
        height: fit-content;
        margin: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 0px;
        padding:24px 40px;
        border-radius: 24px;
        box-shadow: 0px 4px 8px 0px #35201714;
        text-align: center;
    }

    #logo_link {
        margin-top: 12px;
        display:block;
        flex-shrink: 0;
    }

    #logo {
        width: 270px;
        height: 100px;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
        background-size: contain;
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAl0AAACMCAYAAABGfxyIAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAACuBSURBVHgB7Z0NkFzVdefPva97viShEcgYbMdqJ97YeNelkbfWKScOamFn/VnFyHhjE6dKI9vYECNrVKkCBMhqaYCRqFSNhKSgWrasUe3aitnaICqbjZPNQrPx1ma9CRqMd9eJsWlBZJARaBCgmZ7ud+/ec997PT093TM9M/3uu++984On/v6Y7tfv/u//nHsOAEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBEEQBGELDFLG1Klbc1nOBqrSzTnM2cCY7JcAOZCsn4Hsx/voy01QH1ZJ3868U7zsunBWMjHpSD4hhTvZ9aVHJoAgCIIgCKKBxIoueWK4v9ozPeAyMZDhfKOUbEBdOwChIyeBsQmlzJQIk8+AcCdIiBEEQRAEkSjRVTl1ax4ysFlZVXmQKLBYP1iBJ8RcIR7nriySCCMIgiCI9BFr0RW4WcyBbVLKQXtE1sLoMCWDonThZPbm40UgCIIgCCLxxFJ0oaMVN6HVikCAiYp7mBwwgiAIgkgusRFd2tVaPb0ThByOu9BqBQowIWBfdrrnNNt+aBIIgiAIgkgM1ouuJLla7RK4XxUX9vXefLwEBEEQBEHEHmtFl06Kd2CvTopPMYzBOIkvgiAIgog/1okuElvNIfFFEARBEPHGGtGli5YqsSUlDAGxAPJQVbDDJL4IgiAIIl5ELrrSkCDfaYKE+66bj48DQRAEQRCxIFLRpZPkOZxo1XaHWBgUXxUBW8j1IgiCIAj7iUR0aXerr7xXnRsGYuVwKGT/zfF9QBAEQRCEtRgXXeRuhQO5XkQa2Dd6ZJwztg1CREi5b+/uHQUgCILoMBwMMvPo13eqV3ySBFfnwc80w9mZmVO3DgFBEARBENZhRHRhOLHy6K1PMskOAREish9dxMr3vj4GBEEQBEFYReiiC0tBuH3TZ6julknYcOV7t53Bzx4IgiAIgrCCUEVX9Xu3DWa4PEPhxCiQA1kVyiXhRRAEQRB2kIGQwPwtKeWhGPXUThxBnlfl1K1bszcfLwKROgqFsX7o7vHq3wnRD1z45/kkcK6bqhd231YCgiAIInRCEV2V/3jrXhBQAMICZD8uXlCu41DmCw+fBCJRaFGV7cpx7g4A4xsZYzkQMqfmOiiuct69XO/EwX/8SZAja9ePHDgaPF1Jb4xNCiGeYSBLAlgJytWJQmHXJBAEQRArouOiiwSXnUiQ49N/ctu6ni8+TIsZYkxh9OEch0qeM2ez+k7zoIUVCig/U0DKlZjLOb2p5+CMDeITaZ3Wk4WR0aMT6sqSBPcpIdgECTGCIIil01HRRYLLbhwmx9R3tJYKqcaLwugRJbLYjUpLKSHk5lBgSS20DMJgQImxAQZ80EFRh0LswNGiehcTQsrHC7t3FIEgCIJYkI6JLhJcMUF9RyrUWKJQo91g2NDp7dqpvrBhkNb2JM0r/ZV3GBseOXhEuV68uOfOb2wFgiAIoikdEV0kuOIFhhqV8AISXvaBrpYSMXvV2bwOFcZlIYoWhnIQCIIgiJasWHTpKvMkuGKHGs4PzXznlme6vvTIBBCRM0dsEQRBEIlkRaJr5tFbBqjKfFyR/TzjPDZ16lbq1xghJLYIgiDSw7JFFxbd5BIeM5zOS3QQrOOFBVTlieFNbPshWolmkMLoWM5h2RNAYosgCCI1LKsiPfZSzFLj6kSA32G1b+oEEMYYefDYTodnzgAJLoIgiFSxLKer2lfGcEgOiITABqf/5LZhquEVLjV3S2B9LerUQBAEkTaW7HTNnLp1SPkjw0AkCl3D69SteSBCQedukbtFEASRapYkujCPi3E5BhYghQBZqYA7dRkuTzE4f/lqeOFybs72esXW8kZ2wjicwNAxEB1l5OCxvQ5jT1pcb4sgCIIwwJLCixnOHlMnkQwcKLCql9+EqWkO58vXwM9mBuAV2AAXZA7K0LfgY6/ufgne3v0yvLu3pLbnYW2Wcsab4ed3Yeh4FxAdYf/Bo2MgyRkmCIIgliC6dD0uKQfAIOhmuZffgOobl+Ds1Hvg/4lPws/lv4KybC2yWO2fWX5ZvlZvz17apC+j8PrgFRNqOwNEI2xYhRkfz958vAjEirjvwNETUsIQEARBEAS0Kbp0WFHKAhik+vpFmJl8FV6svh/+1v06nJMfaO+BKLjk7Nlm+covKAGH2w9e2wIfvfJJEl8N+GFGKiOxTHQLn+4sru41OkkhCIIg7KYt0ZV1YK80lI8iZqah/Mp5mCxfAX9VuQf+SXzA11DNK4KxOWfY/BsXEGCY8/Xn57eS+GoAw4wzPdMYEisAsWQyPdkxElwEQRBEI4sm0le/97VBUyESdLemzr0Af3f5Bvj35QPwQvUD4AolxILN9Tcxe1p/uxRSnUp9KiWegte+TnraKzjfSCC+/vqVT0FZ9AChdgzOdqLDCcSSwBwutYsNAUEQBEE00IbTxY2sViy/8jK89YYLT8zcCv+nutkTSABaPHln5uqleueK4Rn839/0dfq8BIaPY/oenvGF17foI/x3kx+Bn751HfzeO79NyfYg+zPeStWtQLQFrlKkpHmCIAiiFQs6XViTK+yq85gsP/3Si1pwnZq6F56duR5cV+qtWsUN/NO5W6Vad7t/f72p64QrZx0xCb7rJf3zi7te3z33ZXhhKgcEG6TaXe2xf/ShITCc90gQBEHEiwVFF+ewF0JmRjlcWGfr228+AC/NbGgqsioVUCIL9G14vjrnfIMgc0FvNRFWF5IMxJesE18thdc/fRl+8OoWSD1O+PtA3MFK84wzK+rXEQRBEPbSUnSZcLkwpIi1t/7DG3vgtcr6uSJLb1Jv3nULn/c2JbgqgfjyXLCa+BJ1zleD8JItunZjgj3meaUaCXlyuxbGYVkqfEoQBEEsSsucLnS5JISHFlxvXoL/fvkmOFd+txY+QifCw6wgCkKDdW8kOD8ndwv/434OF9PvXZ0ydSr1ec7Vef95lCPh539JX3Iy/Rh9m3dxDpjnhaHGm649ld48L8/tKgIxDz+PKwcEQRAEsQhNna6wXa5AcE1MXw9PvPG5mmM1J7RYmc3ZwhBhEFJ03dnLs9f7j6/MOmXV6tycMFdvoPO9dMhR1q94XNj1wsKqmOeV2rZC5HY1BcOKlMdFEARBtEtT0cUc2AYhEQiuSXc9PHnpc02S5aGWHB+IJX3qzt9qtweX60TY/M1PuEexJrz710pNyNlwIyJb5Hl9+4U/gP998SOQRiSDQSDmoMOKBEEQBNEm88KLujaTcjYgBALBhTx9+Xq4UF6vBVDgNIn6XKtFRFBAEBKslYjwzwvB/NCi1OFFiaf4nA7oU8fxYopS3ebo52gSbmwINWINr/92wavl9dGr0jXeqnDtNnliuEBV6j30asWQcx47REnt0UVXirPqV1Bikk2qXVx/h9zhk8IVNftW/Rpy6heU45xvVD+AfvWbGKBctfhTGH1YHdOrA9xRYXDJN3DwvlP8vvUdGJtUh7/JYB8RwEpQrk4UCrvot24R2OkCsl3q9+nqwsv4W62/XX936vdduHvHaYgZhfuPDeDfhX+TGmvW4j6qJvr9+jgEelRW+6WcVAbJ62psnlD7calwzzcmIIbME11e9XnoOPWC62J1PfzXi1trOVxeQVOYLekg6+pzwSKiq77qPGM10YUCTD8n93K7JAow9S3i89YElzp19Iup+6AYU/fXisyr/AXeOZiX54UJ9th0+7PXPAbdfBrSgewv95aH1JlDkHL0akXG7VzVyeSk+k2dVLv7abcDA+f9Dx4bcF13QP0i8upHtRniITRTDYosziuD6uimvi+RB+n2ezNJ70AmG5dsS+8azrxqhnoS2pOFkQNHi0LKp6Tgp+M6wMWZ+d8jimX9Tenb55eaVPdkLW6yjMLokbza325k2LlDT+5QXPHaG5cNhTnxst47vSRufc3IwSPq2MaLUriPC3CLhd27ShAD5ifSh+By1Qsu5O/fvN7LqxLNkudlU4erMYE+cKJq99FFT2UtsR7/F1poBe5ZkEzvCS8UXDwQXsFhiHtii7M6m6tFMVUsovrtF65NVSFVh8sbgUSX+hy6tlmXPM/YafVDOrznrh1F6CD33KEHW9zG8TKKsKorhvCACSTArEH3++zt2qn2yzyAm9ezTGhy4FoaODDm1cx0rxJgJSnFvoUGt/sOHDsBIVOdntkVpgOHDjZnKHLCQwrx+J4WblTd9zikvsdch75HK6gJLab+tnoHXS7zb/NE6KCaAA86SrDhJEHtoye/tfub483uPqJeX913G4SIYPKZb915+4Jj5BzRFUYCfaPgQn74xm835FTNz6tCmjlcLW/390tPjHmqGFyG/9c9P5vzOo4z+1jpu188qG7Ppe93QUvhFRRS/djb/gv8+qqfQOJRglz+p29sYDcdOwspxbrkeSn3KUfrkKlQkC/CsOr+MB7EAFsecbiRwpDRgAOZwzkO0oMQRohiFnR3T+Dgdt+Bo+NVWdnXKL7UkXUIQiazek1BnYS2r6ujfl79HSEPzIDHzzmiS3+PjKF7ng/5ezRO4YEjgw5nOwF8Q0eGJiBRVOWV+NqLE4RG8eWF08PdR5VOKMIixsQc0cU4zl4794U3E1w/m7oOLkxfpfcrdLvaSWJvl0ZBFogvzO/yVib6LlpNfM26Xg5If2fwL9dCjQirNc9mTYTXn/7i93TD7DTkec3MuNshxY2wMyy715JDYtGVle1RWup7dmtXrYhClIOTtzbkmkCiHKTVqw05LDu0/+DRQ0JUDsclrGMjc77HhIGuoX9MyIFZ9AShlfiKmobwouzYCrVmggt59s0PLWnV4HKoDz0G4gvzuUQtNCmVyPIFlgzuqASY40WOtQDDXDAAv+6Xn+fVIsEewTyvadEDH3/bX0CS4V6IsQApBK1/GfXBkclJtSfuu3cRC9sk/qA77m9EiKDAdXhmTB2vIl9NrA6rw0p8DarB1bqBzXbwWJLpyY6hgIWEoYUksDE1ZA5AtATiaxtOUMESaqKr+r2vDXZqvtRKcCEvTr27JrjwFAljolafA6ZXRoIXLpSuVzw1EFyO4wsu8ISYrBdg/ipHfJ86z4svnGCfjobZbCCtIUbe7eBAl4PoKHFW3XrPHbsoqTmFjDx4TIURRcGyUK43sB08tiFpYbGw4LggpTezU31ciQrJo5Dkvdm9KMbBLvK6vA9jEzbso7NOF+M3diKyuJDgQlCUhC246pmbcO+9WBBu9K70Q4wwN8/LAS/5Hh+DJSfQ9mJC+it8YNE8r5uu/S5c3f0yJJFyRWyFFCbURxw+K6nZ2pY9d1AoJ23o5Oqe7GMgMEne0oRqKhK8FPIh5jVFgh8mPaGGyxzYSc6WxU+zxVE7sGpxMcGF/RVbrU4Mk/rXq9UDE6D7MQZNsfUWNMyu1vdt9KvY6+r1WNaivYbZP+jZCVPv3AxJhOHy5ZRR8JLGcxANWnBR7kz6uP/BsQEluM5AAnN+iGSADqwSXE8CrWZuC+10YUHUla5aXExwIa9q0QXzVimaoPH1hO9aeYab9O/j53M5rOaCQX2ivZ/nxZsl2PsXkWuvzcD11/fBW/A5kJk+6DubrDwvzngy1eQCZLA4LEQCCa6Uct/BP94mpIuOMq0MJaxk/8GjY8qJsC2caDXa6cpysaKEt3YElyYCsTXvLdS5XkKHOdUWOF2+s1Wtd7zcuY6XqNUXA9230VsNOet6XXmlA7/z8b7a613OfRIu/fOvgttzJSQH2f/md26JOknSKJEl0Au5iwRX+sBG6lKKcSrFQdjKfQeOnrAwf8t6tOgSkudhmbQtuOqwSXjNii9PSNULL92nsb43pDvbt9F7nCe8gkr6q1dz+Ne/0wdd3XPj9TPrPwivb9yRKOGVcTJ5SAmRhRal3Lcnhi09iJWBgotypAibQcGVxJWXJvC6DXLYCMsgjoIroFF4Bcn9mLsV5HDpptv1zbVrzbY9MVZf+mL1agaf+ewqWL2maQ9xEEpwTf7LO7QASwIZkMvaZ+IIi6bZd2nP7h0FIFKFt0KRBBdhLyS4VoanEJaRRL8cwdXjXAabqM8vq/WAdOuElzvrcrlNEuz1qXrsqlUcPvvZ1drpWmgFqMz06lDj5Q2fgrgjsV9WSuDAzAtMFVYEIlVoR1XI1LfZIuxFh71JcK2IzMyjfzCgl+UtgeUILmR99gLYSH1ZCV3PC0tK1DlguqwEzFawD+p4YV2v7i6AT35iFaxZE+hXv5QEQMvV3ZjnhQIME+xZdQriCc9BCsDl+mA+n6tIYcV0oYue4pJ7grAU7cIKcmFXCueymlvKA5YruJBe5XT1WuZ2BdS7Xl5Zi4Y8r+rsabUu72vLDX1w1XpeS6gHqDO7FnC9pt61GS6qcGN887xkPxZJhaTTnTHv6Em5D4hUoYs30pJ7wlK8nrOiAMSK4dUlFDNbieBinIPT0xeLxtAt87xcP8+r6m3XX98Lv/arWV9wyTl1u2oabAHhhXlemGBfXf1OiCMz5comSDjMvMtV8nsaEilBJ86T4CIsRk8KaCVtR1BSqL0w0UoEV0D3298B/+Ldr4LNzCkp0ZDnVV9I9SMf6YUPfainVsk+qHAvYe5igXaEFybYx7GQKmM88T9CzsBoPhc2aAUiNXgOAoVsCHuhSUFn4YyxRUNEnRBcUikYMTMNH/2VH4HtNDbh1lXoxawI++hHe/UGDffzBJfncS1FeCFvvfdzsUuwl2n4IUqWA4MIcItApAY/rEgQVkKTgs7DgckF3YpOCK6A6usXoS87De9fH49eyfMLqQL8xod7dFjR69c42xKoUYDVhFd9nlfd5WZggn2c6nk5DJKf08XAXE4XgwkqhJoe9o8+NATkIBAWk2HZvUB0FM4WyOnqpOBC3Okp7Xhtfd/fQFyoF1IDA13wyU+uarhN1rlcjQJs6Xlelf73Jq6Qaly5//5jRpPo1W9xAojUEHEDdYJYECxhQuUhOg9vdUOnBReCgqui3K73X3UW1vdNQlxAAYX9FD/96dXzc7bqXS2AJgJs+XlethdSTXp4scKF0Zw1IcVTQKQCcrkI23EYo0lBCGQaB04URuXz57QrFQbVSxchu3Yd3LLpP8Po//h9iAPr1jnwla+shZ6e2cJbQW2v2fNeHa+g3her65mNZ7zLbPYxsHA9r6CQal/p+4lrmB0XOMhcy2JrISCEQ05XSrDC5WJSzXx5UQiB+R71s+B+zthGwALItGItlfitz/IQPSU1ahZdKV6Hun3UYXyD1MdnK97jksg0XhGm4EK023XxVeV2cZ3b9ZMLdqcFoeD66leumCO4ApoLLxymmSeqGgRYvTDTjwF/SK+dmU9QSHXVz/4UrGORfMC4o6RyzpzkUlRmSkAknkhdLhRaAg67kBkv3HVbabG7j3iD75A6aG0GcuZSQ4axbRIioyiZeFxMueOFwq4FQ2JYvNrpyuTB4dvU4BpFu7YlM0d0zbz6SqiCK6Ci3C5n1Sq4ZeDPYM9Tt8DlSjfYSCC4+tVpKxqFl4Z5VevrmSe89JXtCS8spIrCa/U/fAdsQv0VNAvuFAwmFzvAEMlAuVzbwDRKbKnf675779yxpDZDfs24oq6YD5mCevPm3ztRT6nJdTnoIPhdR5LLpXNa5a6l1Cn0j5nYveO0t49mHzO6+GkZ1EQXrixEMWSK8ivn4ap39sDtHzwJDz79NbCNnh6+qOAK8NwtqImmZuHG4GZPW6nb1A3MF1r1Wq2V8Jq+5sO6ZZCVjldyyYE5SkAkHr0E33RIRA1mrqxuXcnKWP+xQ8r5GgevXVEOiPBAkSz5acHcZxzmFCuXZ0oLTcrQ8cn2deWEu/I8VA5OHkwj5WEltoZhBfj76Ca1j+LkwNp8NC26ZLUC5ddeAZPga06dOwu56nPwmbe/Df78/Fawia9+tT3BFRAkxy+U51WnutRvyhNecx4DCwsvdLz49KvQe86OfGv1J5SA6AwSyOVKAZxnBxer2ddRpDzplqvDnXJR0YVQwnELtS0KjSK2Adtz19K6Uvjfb0dyQhnwnWAS/Ht37yhAh8DnUiH8knKUT4CF6NWL0+d/AVGAwgv5zNWnlfB6DGzhppvW6NWKy6HlykbZmUKql3Ofim3bIIJIO2pSdSOYQjlcagAa6nTYGh0FV1a2YEgciM6gc+3k1j133b4lyjZg2ok1GZ7rsOAK+Nbub44zIXaBhfDq65MlMVOGqLFFeH3sY33woQ+tLMdsnvBqt5Bq8JjaP02eO9MLb/3a54AIH95Gt4ZOofzQEhCJBkNAYC60WHKhElr4QIdylEgAYuVg+FdUN+25e8dpiBiToUUV0BkPQ3AF3Hv3Nw+pD/cwWAafmbSnFyIKr69vOAy9zmWIAhRcN9zQB52gUUS1LKQKSxdeWEDVhl6NAuTrQBBEW+hVVobAHp5hdzfwHBn7BrWYUXLLlS22dKJgjJlyYktVWdkHIaPCtMNgWdFpLoUogUVsvOJpuPu9e+Cq7AUwSScFV0B9G6Da5cZCqssUXhhmRNcrShxg5lZeRIKk8AnRMQQ35nIVMbwCBnCnqwUKMy4bHaa1a9UyMxJaNDEpqHs1q8KMXP3xZ8Eyruq6AMO/Ogq/vuonYAIMJ3ZacAXMulz+5Q4JLxRc5auirVjvSrBu3+kkQoIxJ88v9EckGA5sI5hAytAdhAAUDEKQ27UczAqPxfHD3zkIn5KpSQESlD0BS1DHAWblLCUQXmHneV13XbdOnA+blQgv74b5z1m+5jcgShiVOSCI9jGToFwynYgty9VD5HYtGaPCoy26M0ZcLhaFADI4EVkMJbrsCi82gnlen782nKKguELx859fDaZYmvACqG+U3QzM7RLd0TXGJtHVSVgOiMSiV4VJCL2YsJDycTCMdrukPAlE26DLBZbBuZnQYtVlxp1RPRGxZGLAwXVKYDk3rP8ruPufdTbPC6vN//6X1jRt7xMm7QqvoFF27b61f+ZSjrApdlm4SZ/dlsAUCW+pRGRyYADuVec2DpfRvG5cEeAWwTKkNJLiUCrc841IEttVGNz4hKQZyulyrcrsb8W7el7Q4cZOCK922vuESX2CfdM8Lhnc3CTM2AC6XVGx+kuPxGLfWS6MGwy9KxfEz6kgEgg3lLMXVY0nt1ylZu3tU7IplytACfYNEDJqOHsGIoKBLIIF8HW7i6W4xOMxz2vk/X+onK+/hOUSteAKmONi1ZeTqAstNg0zNoiw6up3QTTI5B9khTD6u3AM5VQQ5sHm6RA+RYgIfwVeCYh2KIGVhN9LV7lpkY0btriLuiI9iHgVZvz8td9dVoI99lP8kgopRi24ApqFGoPzszfJBbuGiJ4rIykdwSRLvOhyhGP0b5SGciqICGDh53Op40SkdfOElHb0J7Mc9TlF5vYsQg5CRvLoambZ4i5q0SVZ/H4syymkiknzy23vExaN4UPZkMjVjtslHfOiqwrM1gNHx6jwGaNOF5eGSgoQ5jGQRB+li6ChFYztYufnZGBi4IjI//YSRIzndFlWsbVdllJI9TOfWQXXXdcFNtJYLiI4367b5faaX8FYdatFSDh6ZmRwIFGTn0EgiGViNAexGdS0Pd4YmBiwbO/zECUW7KOe6AJRhJiCeV53v2+vEmB/3/I+WG3+N38z2urtS2FB98sK2GTSk+hrCIMzI3XQGxk9kgcicXAGayFsDOcgNsJAloAgbMYCN1aLLp1MH+MkyF72pgo1PtQ0zyuM9j5h0MztmhNeBAmL1e0yhZAiNbkbAszmXwgAcrsSSfhJygRB2A8PzqiB3IoaFiuhsZDqb/1WTywEVzPmhhf961pccKZeA5MwyVJTk0eFbIw6epyzbVQ6IolQH0+CIOpEF0iRiIEUC6mOvO8P4bc3XoJPf9pctfmOItu8zoeXzYou16I+VmHjVFkRTKJCjLzbIbcrYRjp48l5pGLdUFkMgog1s6KrDBNJWX2CeV5f6L0PnIs/hzghW4UPZWvNlXnzHJhFTvTefLwEKeEerJ5s+HfBGN8LBLFEpKCuBsQKMHCcq5bL6yBKDCwWWIya6FpXKE5Cgmov8bfOwxV/uQO6/zGuBl5dXtcC9+LTr4JJpGTpq8VjfnVvbv/Bo8NAJAcDA5rD+AaIEM6ASp7EGRMr+zIiB1HCw69FtvhbqENK17omnCul7+lHoPfH4TTMNoFsvNSgwLouPAsmcSUcgpQhmfl8RwawVzdJJpKBgQFNGmo11BpaLBBrDOyjHFgOIkLnytrkdGkSFGKsp+fH34U1T9yl3K9fQqxoY5li9vXnwBzpCi0GCFExb5eqg4PDsieASARGyikwGIBoyQMRX1j4+2ikBaAtabM2R3RhiFFKOAkJJPPLZ2MnvBbTXNnJn4IzbS6JXgp2GFKI3z6iBObJjxw8RvldCYBJA4VLlVC/74/+XSQhxgLVl4s9JhZ7yAiFOWN2lOPh865JyCrGZnh5XrdD9tzfQhxgi9ze/fIPwSRpWrXYiJAymsmIlIX7Dv7xNiBijcuqRvIChTu9FSLAlgGNWD5myuPIXFQlcZTLthksYJ7oWre7WFQffxESCpt5C1b/zUg88rxY6yu4crh6zpsTXUzCeBpDiwESquMQEVKKcRJe8cZUSyn1O70RIoADi+R1ic7BXDMdBXhvdggMo/Njow+/a3izK5OYUN8I5nn1Pf1vgVXeAjthtX/Z/Kuh7+xfgEkqEhK/TyyEH2IsQkSg8KIVjTHHTEupvOkFGH5oMQdErDHlxkYxMciwrDVpGk1Fl+d2Jb+PVvc/Pg5XfH+HNXlerFk80Vdd9Td1XfgRuVxRIGWkwlN9D2OU4xVfBJNGyq0wyAyBQRzGaJ9MAKbcWMBcVYM5gDgJkQBDYAm81Q1SslQ4G5jnhQn2NhVSbSm+wAsrrv7ZY2CSpC6uWCp7du8oQtQ9SqUsjBw4+qSN5SQomXphmJRGnATO2U5T+8f+0YeGgFYtJgYhpJnyOAaFuk0uF9JSdK3b/cR4GtwuxMZCqq101yoVVnRMtv2RUMzefLwIhEaF+WyYjOQdnj1jg+uFSbH7Dz40jEJQOR5PAtESUXbNHGAMlRtBYUfdE5KFwV6zeRPpEiMPHBm0yeVC+EI3psXtCoiykCprTNxi3nWs7joUXCbDikhVwnYganxr9zfHIWq3C8Eif57r9Ty6DVHk8ew/cHTM6c08zyQfA3I7FqVQ2DVpsLtB6IOawzP4veeASAwmaxKGXQBaPzdnY2AZC4ou7XYZssRtIepCqvNCi2zW4ep74ftgEsrlao4lblcAug0n1AB45r4DR0+EFeJDRysQWiMHj1xEV0vtl8MgqQr5UhAgjXU3wBzAsFa9apdVMioTkTCMLhjyHNlQUiXwOfG5wcJJQWaxO0iQuxikK2wQFFJ944YDIFZdDWETCK1gvSJrSJxfpcTWKsOCC0n7isVWoNulHCYczPJgC0r8oI2uxNCQem8ltRNNSOE+LoQzUcCm3UukMPpwjkMlr+ING9W+OKBG8IGawJKLVZAjWsG9Ac1YSM5f9bruW3fe3rH2Xer5xtQT00rahCKkfIozlgcz5HzhtcUXfCvGZsGFLCq6cCXj5IGPFdXPNw8pIiikennT12DmPR+HsJjjbNWLLXXGqU7Bqhe/D32/MLLoaS4C9pHLtQC4ktHcgWmp5NT7Qwds0HEkKBHmNe3GlUmMTSoHZNKV4izekTG2lvs98/zeff7mwhwjnIRWR8DFGOr7KIJBwa4drwNHN1ZlZd9KBrb7HxwbEG72hNpRrKh3RISDLFcPQY/R5PMc5qgqMb9vpZMDzOFSh60TNvRYbMWioguR0t3OOD9j8x8SBlhI9cqfn4Jp50249I5PgMj0Qlh44ms2lyv71jlY+9PvQkadmka9fClz8/ECEC3xBs8jh9WntRPiQFAYUErdXorXqX3ZTpNPomNgA3UlhPJgEM8FzWJbqXFXzJxcivgqPHhsICPkTiFgCEh7Jx7MPTQ9MUBtoXatMfW6OzF9w8+dbRtMffBLl+RtP5y1JbqU21W6+MAN+xgH65LSwiSz+gq9rX7pKeh57Vl4452fgLfe9mHoFM3CihzdrZefgtURhBMDpKDk+XZwp6sFpze7LW2TEWJliKnKuNpv9kaw36ADWlDiCxdgFDGMJDHcKfgkcD63PpOsDnDONuvQspB5kuUpIzonX+eojhw8orQGL0pwnxICV1RmSnPuJUQ/5+6ATn9gcihOuaW83Tuuu/uJQ0luD9SM6puXoPzKy+r4U4FM+TVYp1yvq3/8R9B3fuW9GxvDiii21qhQ4tVP749UcKmB4DCViGgPvRpNyEh63RHxBfcbIWTUzePzyu3ciwsiVAj6jMPc5+dsnD2mF0rQqtRU4tckLEJUoIiSchBXRut9tHH/VPssirM4LuZpW3QhGGY0VLHWGlB4XX7xeZh+6UWovnEJsm+8qMXX2j/7MnT/w+klr3LUBeaDQqdKaHVfeg76nzsFbz+jxJYSXUxdFxU6rDjVUwCibbyDU+QDKBEzdN5Myo6lRMyIuANHUlmS6MIwo3TTuaLNnZ6C8oWX4fLZ56B8/hw4U69A35lHlPjargXYqv81pkVY5pc/0kn4jWCPR0ddnz33P6Hvxb+G9f/3GFwzMaJP+175YaRiK6AiYAvbfogGgiWCYUaD9ZeIBGCJ20UQLYnc7UoobeV01YNhxosHbsgpVyQeCcQhgAKsHhRZXc+rrY3HZtZcAd3rrwHroNWKywYH0MLo2FYH7F2mTNiHv0oMS4/kgCBsxO5V2rFkSU5XjWlRSFvR1E5gq+DCIqhZWq24InA1GOdsK4WMiHbxKtRLWrRCWAulT3SeZYmudYXipARJA8wSsFZwAZScqZ5dQKyYe+74xoQUgj5Lom30oMaYPU1fCaIBnT5BY33HWJ7TBX5+lxC0cqsNbBZclMfVWbC+DCPhRSwBd2oG3a4SEISFaEfWJUe2UyxbdCFYrV5KQV/GAlibwwVyUjB3K+VxdZ577/7mIVr5Q7QLhRkJ29lz947TFGbsDCsSXYgSXuOSevQ1xV7BhQVQ2a6u332E8vJCQoWNCjQhIdpFhxlJqBMWQ6u0O8OKRReybvcTBRJec7FbcMH2rpuPjwMRKrqVhaDcR6I9UKgr4XUSCMJC0JF1oYIpRSUglk1HRBdCwmsWmwUXloYgwWUOtOVdUdkEdKAi2sAtV4fJTSBsBVdpu7KyhSaSy6djogsh4WW5wyXZLioNYR59oJpWwotcDGIRtJtQVoMaCS/CUnR5HMZIeC2TjoouJM3Cy/qQ4hcfPgREJOBgqsJHQ3plIx2siAWoCS+qBk5YCpbHIQd/eXRcdCFpFF42r1JUIcUtFFK0A1zZqA9W5HoRC6BF+l23b6EVY4St1EKNJLyWRCiiC9HCC+t4pWBWb3MdLsnEFhVSLAJhDXiwQtfLX91YAoJowZ67dgxT3TfCVih1YumEJrqQdXcXTyvhpSxIWYKEYq/DxSaw8CmVhbAXXN2o3Iz3kPgiFkK7o7LyHrB5H6EctNQSm9QJS/bRUEUXoivXS4lJd4lrdWGt4JJwOPuFhzdR4dN4QOKLWAztjqp9xMpaXlIe9nPQiBRTS50AaddYz+QkY7BLgrAiVB+66EJQePXf+cTWJOV52Sm45CQmzGe/eHwYiNjRIL6KQBANYC0v7XrZEc4pqfexRb2nYV1Vn0g93uRgx1aLJpBFV1Q33Xvn7dYsIjMiugJ0nhePf7jRSsEloVgVbBMlzMcfX3xtwcFVSJ1IXQKbwZkksHGqwG+GICcQBQ9EIc7V942OG+by6Er6BNGABe59UU8I1HEUfy9gERkwzLo7ihMXC/lN0MMKDGAnxAwrBZeAfVR/K3n4Bwt0LYfvf/DYgCvdPJP8RnU5D1GCIkvy04K5zwiRPV2467YSEMbxBU8R9w3himE1hb4RJOuHsGB6JfRhd7p6iJwtoh10Vw6A8f2jDw0xxrdB+Mcu3U7L5skAgwi5OJrPM8ZOqLeRgxhgneBCd0vCdsrdSheFwli/050ZEOoAxjnfCELm1C95AMKhBIxNCCHOcrW/uSwzUdhNIstWRh44Mqj2BbWxzepiDlaKElpCwEkVEjlNrhaxUgqjYzkOTr7DAgzD3CddyIzH4dgUqegKuDh6Q4Ex2AsWY5fgkpNS8n1U7JQIQCGW7evKiarISUfmpGA5h/G1EpQgmyXX8LAS/qNCg/rUleIsU6F/JtkkiisoT0+SoxFftDvqugMS2ABnbKOapPUvKM512JBhqLiILiYTbIKEFhEmI6NH8pLLAQbOZiWc0KXNwcKTBSWwYJLhRFC6TwnIFuM2CbRCdCHK9cqpt6PEF9sGlmGP4EJ7nx3OTPccYtsP0WBIEMSyKIw+nJtzBQlswiJwEgndPXNC5Ulx2K0RXQEovpT1OKbODoIFZPpWQ/fb3wFRwySMVyTso1AiQRAEQcQT60RXgM734nynshIjE1+8qxt6rv0VUO8DooLEFkEQBEEkA2tFV0BUYUcMKXZdeXVEgovCiARBEASRNKwXXQGe+OJ5xuTesFc7ZtddBV39V4FxcHUYiMe7p/rGSWwRBEEQRLKIjeiqB0OP6q0PMc5u1CtyOgTLZKH7bdeA09ML5sCViOykCiOepsbUBEEQBJFcYim66rn4QB5r0gwyBpuX64BhCDGzdp1Bd4uEFkEQBEGkjdiLrno8BwxDkFgYUOYXu7/T0wdO3yrIrFkbcu6Wrn8zgaFDzmWx63cfsaLbOUEQBEEQ5kiU6GpEizAOAwz4ZqzazRxnAIUWV+HDzKo1KpwYRhckiblYWGByogriGRJZBEEQBEEgiRZdzZh59JYB5jr9LhMDTPJ+h8EGiRVwmexXQqlfMqyK27x/mQoHlvBUspqwmnQlnFUfYolzMVkRfIJKOxAEQRAE0Yz/D6yJ8Rwj646yAAAAAElFTkSuQmCC);
    }

    #login_form {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        flex-grow: 0;
    }

    #pass_form {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex-grow: 0;
    }

    input:focus {
        outline: none;
    }

    #login_email, #login_account {
        text-align:left;
        margin-bottom:4px;
        font-size:12px;
        color:#777;
    }

    #user, #pass {
        position:relative;
    }

    #user::before, #pass:before {
        position: absolute;
        left: 2px; /* adjust position */
        top: 45%; /* center vertically */
        transform: translateY(-50%) scale(0.6);
        pointer-events: none; /* allows input interaction */
    }

    #user::before {
        content: url(/public/img/login-user.png);
    }

    #pass:before {
        content: url(/public/img/login-pass.png);
    }

    #resetusername,
    #username,
    #password,
    #confirmpassword,
    #zipcode,
    #dob {
        background:#f9fcfd;
        font-size: 16px;
        margin-bottom: 10px;
        padding: 10px 10px 10px 60px;
        line-height: 30px;
        height: 48px;
        width: 100%;
        border: 0;
        border: 2px solid #B6BAC2;
        border-radius: 8px;
        border-width: 2px;
        box-shadow: 0px 1px 2px 1px #38383814 inset;
        padding-left: 10px;
    }

    #submit,
    #reset,
    .oauth_btn {
        border: 0;
        color: #FFF;
        font-size: 16px;
        font-weight: 600;
        font-family: "Inter", "Helvetica Neue", Helvetica;
        background: #20608A;
        width: 100%;
        height: 50px;
        margin-bottom: 8px;
        background: #837BB2;
        box-shadow: 0px -1px 2px 0px #00000038 inset;
        border-radius: 10px;
    }

    #footer_link {
        margin-top: auto;
        align-self: flex-end;
    }

    #passwordreset {
        font-size:12px;
        color:#777;
    }

    #error {
        background: #C20606;
        color: #FFF;
        border: 2px solid #FFF;
    }

    #password-checks {
        background: #E13D3D;
        color: #FFF;
        border: 2px solid #FFF;
        border-radius: 6px;
        padding: 6px;
        margin-bottom: 8px;
        font-size: 15px;
    }

    .password-check {
        text-align: left;
    }

    .alert {
        padding: 8px;
        margin-bottom:10px;
    }

    .alert-danger {
        background-color: rgba(255, 76, 76, 0.1);
        color: #d63031;
        border: 1px solid rgba(255, 76, 76, 0.3);
        border-radius: 10px;
        padding: 14px 18px;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(214, 48, 49, 0.08);
        display: flex;
        align-items: center;
        animation: fadeIn 0.3s ease-in-out;
    }

    .alert-danger::before {
        content: "";
        display: inline-block;
        width: 14px;
        height: 14px;
        background-color: #ff4d4d;
        border-radius: 50%;
        margin-right: 12px;
    }

    .alert-success {
        background-color: rgba(39, 174, 96, 0.1);
        color: #27ae60;
        border: 1px solid rgba(39, 174, 96, 0.3);
        border-radius: 10px;
        padding: 14px 18px;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(39, 174, 96, 0.08);
        display: flex;
        align-items: center;
        animation: fadeIn 0.3s ease-in-out;
    }

    .alert-success::before {
        content: "";
        display: inline-block;
        width: 14px;
        height: 14px;
        background-color: #2ecc71;
        flex-shrink: 0;
        border-radius: 50%;
        margin-right: 12px;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    input:-webkit-autofill,
    input:-webkit-autofill:hover, 
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px #eaeaea inset !important;
        -webkit-text-fill-color: #333 !important;
        transition: background-color 5000s ease-in-out 0s;
        background-clip: content-box !important;
    }
    
    input:-webkit-autofill::first-line {
        font-family: "Inter", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 16px;
    }

    .loader-container {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(249, 252, 253, 0.7);
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }

    .loader {
        width: 48px;
        height: 48px;
        border: 5px solid #837BB2;
        border-bottom-color: transparent;
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
  </style>
  {{ custom_style | safe }}
</head>

<body id="login">
  {% block body %}{% endblock %}
</body>
<script>
  function set_message(msg, category) {
    jQuery('#alerts').html(`
        <div class="alert alert-${category}">${msg}</div>
      `)
  }
</script>
<script>
  $(document).ready(function() {
    $('body').append('<div class="loader-container"><span class="loader"></span></div>');
    
    if ($('.alert-danger').length > 0) {
      hideLoader();
    }
  });

  function showLoader() {
    $('.loader-container').css('display', 'flex');
  }

  function hideLoader() {
    $('.loader-container').css('display', 'none');
  }

  var rgxs = [];
  var chks = [];
  {% for check in password_checks %}
  var chk = {{ check| tojson | safe}};
  rgxs.push(chk.regex);
  chks.push(chk.help_text);
  {% endfor %}

  function pw_chk() {

    var pw1 = $("#password").val();
    var pw2 = $("#confirmpassword").val();

    if (pw1 == undefined) pw1 = "";
    if (pw2 == undefined) pw2 = "";
    var errs = 0;
    $.each(rgxs, function (idx, val) {
      var re = new RegExp(val);
      if (pw1.match(re)) {
        $("#check-" + idx).hide();
      } else {
        $("#check-" + idx).show();
        errs++;
      }
    });

    if (pw1.length > 0 && (pw1 != pw2)) {
      $("#check-match").show();
      errs++;
    } else {
      $("#check-match").hide();
    }

    if (errs > 0) {
      $("#password-checks").show();
    } else {
      $("#password-checks").hide();
      errs++;
    }

    return;
  }

  function sanitizeString(str) {
    if (!str) {
      return str;
    }

    str = str.replace(/[^a-z0-9@áéíóúñü \.,_+-]/gim, "");
    return str.trim();
  }

  function chk_reset() {

    var user = sanitizeString($("#resetusername").val());
    if (!user) {
      return;
    }

    if (user.length > 0) {
      $("#reset").show();
    } else {
      $("#reset").hide();
    }
  }

  function reset_pw() {
    var user = sanitizeString($("#resetusername").val());

    if (user.length == 0) {
      $("#alerts").html($("<div class='alert alert-danger'>Please enter a username</div>"));
      return;
    }    
    showLoader();

    $.post("/login/reset/" + user + "/", function (data) {
      hideLoader();
      var suc = data.success;
      $("#alerts").html($("<div class='alert alert-success'>" + suc + "</div>"));
    }).fail(function (response) {
      hideLoader();
      var obj = jQuery.parseJSON(response.responseText);
      var err = obj.error;
      $("#alerts").html($("<div class='alert alert-danger'>" + err + "</div>"));
    });
  }

  $(document).ready(function () {
    $("form").on("submit", function() {
      showLoader();
      
      setTimeout(function() {
          hideLoader();
      }, 2000);
      
      return true;
    });
    
    $("#password").on("change paste keyup", function () {
      pw_chk();
    });

    $("#confirmpassword").on("change paste keyup", function () {
      pw_chk();
    });

    $("#confirmpassword").on("change paste keyup", function () {
      pw_chk();
    });

    $("#resetusername").on("change paste keyup", function (e) {
      chk_reset();
    });

    $("#resetusername").on("keypress", function (e) {
      if (e.which == 13) {
        reset_pw();
        return false;
      }
    });

    $("#reset").click(function () {
      reset_pw();
    });

    pw_chk();
    chk_reset();

    $.each(rgxs, function (idx, val) {
      $("#password-checks").append($("<div id='check-" + idx + "' class='password-check'>" + chks[idx] + "</div>"));
    });
  });
</script>

</html>