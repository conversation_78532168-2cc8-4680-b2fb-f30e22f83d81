"use strict";

module.exports = class DispenseActionHandler {
    constructor(nes) {
        this.nes = nes;
        this.actionHandler = null;
    }

    /**
     * Initializes the action handler for a specific form.
     * @async
     * @param {string} form - The form identifier.
     * @returns {Promise<Object|void>} An object with an error message if initialization fails, otherwise void.
     */
    async init(form) {
        console.log(`Fetching action handler for ${form}`);
        try {
            const ActionHandlerClass = require(`./actions/${form}`);
            const actionHandler = new ActionHandlerClass(this.nes);
            return actionHandler;
        } catch (error) {
            console.error(`Unable to fetch action handler for ${form}`, error);
            return { error: error.message };
        }
    }
};
