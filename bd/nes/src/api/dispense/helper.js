/*jshint : 6 */
"use strict";

const _ = require("lodash");
const moment = require("moment-timezone");
const Joi = require("joi");
const { ActionResponseWrappers } = require("@actions");

const {
    DEFAULT_REFILLS_DAYS_OUT,
    DEFAULT_SHIPPING_DAYS_OUT,
    PrescriptionStatus,
    OrderTemplateLabelFormMap,
    OrderTemplateLabelFormatMap,
    RxReadyToFill,
    OrderRxActionStages,
    RxStatus,
    OrderTemplateType,
} = require("./settings");
const { RecordSchemas } = require("./schemas");
const { OrderStatus } = require("@dispense/settings");

/**
 * @class
 * @classdesc Class responsible for various dispense helper functions.
 */
module.exports = class DispenseHelperClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const DispenseFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const InventoryPricingClass = require("@inventory/pricing");
        this.pricing = this.fx.getInstance(
            ctx,
            InventoryPricingClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Determines the current order stage for a prescription record
     * @async
     * @param {Object} rxRec - The prescription record to check
     * @returns {Promise<?string>} The order stage if one is found, null otherwise
     * @throws {Error} If there's an error checking the prescription order stage
     */
    async findRxOrderStage(rxRec) {
        try {
            await this.fx.validateSchema(
                "rxRec",
                RecordSchemas.prescription.required(),
                rxRec,
                "Missing prescription record to check if Rx is ready to fill."
            );

            const [orderRec, { orderItemRec }] = await Promise.all([
                this.form.get
                    .get_form(this.ctx, this.ctx.user, "careplan_order", {
                        filter: [`order_no:${rxRec.order_no}`],
                    })
                    .then((result) => _.head(result)),
                this.fetcher.fetchOrderItemByRxNo(rxRec.rx_no),
            ]);
            if (!orderRec || !orderItemRec) {
                console.error(
                    `Unable to find order or order item rec for rx ${rxRec.rx_no}`
                );
                return null;
            }

            const fetchPrimaryInsuranceId =
                await this.fetcher.fetchPrimaryInsuranceId(
                    orderRec,
                    orderItemRec
                );
            if (!fetchPrimaryInsuranceId) {
                console.error(
                    `Unable to find primary insurance id for rx ${rxRec.rx_no}. Unable to fill Rx.`
                );
                return null;
            }
            const isComplete = rxRec.rx_complete === "Yes";
            if (!isComplete) return null;
            const workingPrescriptionRec =
                await this.fetcher.fetchWorkingPrescriptionRecord(rxRec.id);
            const deliveryTicketId =
                workingPrescriptionRec?.delivery_ticket_id || null;
            const isNcpdp = workingPrescriptionRec?.is_ncpdp === "Yes";
            const invoiceIds = workingPrescriptionRec?.inv_id || [];
            const hasInvoice = invoiceIds.length > 0;
            switch (workingPrescriptionRec?.status || null) {
                case RxStatus.ORDER_ENTRY_SETUP: {
                    if (isNcpdp) {
                        if (hasInvoice) {
                            return OrderRxActionStages.VIEW_INVOICE;
                        }
                        return OrderRxActionStages.GENERATE_INVOICE;
                    }
                    return OrderRxActionStages.CREATE_TICKET;
                }
                case RxStatus.CLAIMS_TO_ADJUDICATE: {
                    return OrderRxActionStages.VIEW_INVOICE;
                }
                case RxStatus.TEST_CLAIM:
                    return OrderRxActionStages.TEST_CLAIM;
                case RxStatus.READY_TO_CONTACT:
                case RxStatus.PRINT_LABELS_FILL_RX:
                case RxStatus.ORDER_VERIFICATION:
                case RxStatus.DELIVERY_TICKET_CONFIRMATION:
                    return deliveryTicketId
                        ? OrderRxActionStages.OPEN_TICKET
                        : OrderRxActionStages.CREATE_TICKET;
                case RxStatus.READY_TO_REFILL: {
                    const refillDaysOutAllowed = parseInt(
                        this.shared.config.company.refill_days_out ??
                            DEFAULT_REFILLS_DAYS_OUT
                    );
                    const nextFillDate = moment(
                        workingPrescriptionRec.next_fill_date
                    );
                    console.log("nextFillDate", nextFillDate);
                    const nextFillDateWithRefillDaysOut = nextFillDate.subtract(
                        refillDaysOutAllowed,
                        "days"
                    );
                    console.log("nextFillDate", nextFillDate);
                    console.log(
                        "nextFillDateWithRefillDaysOut",
                        nextFillDateWithRefillDaysOut
                    );
                    if (nextFillDateWithRefillDaysOut.isBefore(moment())) {
                        if (isNcpdp) {
                            if (hasInvoice) {
                                return OrderRxActionStages.VIEW_INVOICE;
                            }
                            return OrderRxActionStages.GENERATE_INVOICE;
                        }
                        return OrderRxActionStages.CREATE_TICKET;
                    }
                    return null;
                }

                default:
                    return null;
            }
        } catch (error) {
            const errorMessage = `Error checking if Rx is ready to fill for Rx ID ${rxRec.id}`;
            console.error(`${errorMessage}:`, error);
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Checks if a prescription is ready to be filled based on its status and next fill date
     * @param {number} rxRecId - The prescription record ID to check
     * @returns {Promise<boolean>} True if prescription is ready to fill, false otherwise
     * @throws {Error} If prescription record ID is invalid or check fails
     */
    async checkRxIsReadyToFill(rxRecId) {
        try {
            await this.fx.validateSchema(
                "rxRecId",
                Joi.number().required(),
                rxRecId,
                "Missing prescription ID to check if Rx is ready to fill."
            );
            const workingPrescriptionRec =
                await this.fetcher.fetchWorkingPrescriptionRecord(rxRecId);
            if (!RxReadyToFill.includes(workingPrescriptionRec.status)) {
                return false;
            }

            const rxRec = await this.fetcher.fetchCachedRecord(
                "careplan_order_rx",
                rxRecId
            );

            const isComplete = await this.__checkRxComplete(rxRec);
            const isReadyToFill = await this.__checkRxReadyToFillByDate(rxRec);
            const canFillToday = isComplete && isReadyToFill;
            return canFillToday;
        } catch (error) {
            const errorMessage = `Error checking if Rx is ready to fill for Rx ID ${rxRecId}`;
            console.error(`${errorMessage}:`, error);
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Checks if a prescription is ready to be filled based on its status and returns the proper form / action
     * @param {Object} rxRecId - The prescription record to check
     * @returns {Promise<boolean>} True if prescription is ready to fill, false otherwise
     * @throws {Error} If prescription record is invalid or check fails
     */
    async fillRx(rxRecId) {
        try {
            await this.fx.validateSchema(
                "rxRecId",
                Joi.number().required(),
                rxRecId,
                "Missing prescription ID to check if Rx is ready to fill."
            );
            // Attempt to open an existing delivery ticket
            const isReadyToFill = await this.checkRxIsReadyToFill(rxRecId);
            if (!isReadyToFill) {
                return ActionResponseWrappers.error(
                    `Prescription is not ready to fill. Please verify prescription is ready to fill.`
                );
            }

            const openTicketResponse = await this.__openTicket(rxRecId);
            if (!openTicketResponse) {
                const callbackUrl = `/api/form/careplan_order_rx/${rxRecId}/action/fill_rx`;
                const dtView =
                    await this.generator.generateDeliveryTicketView(rxRecId);
                return ActionResponseWrappers.create(
                    dtView,
                    "view_create_dt",
                    callbackUrl
                );
            }

            return openTicketResponse;
        } catch (error) {
            const errorMessage = `Error checking if Rx is ready to fill for Rx ID ${rxRecId}`;
            console.error(`${errorMessage}:`, error);
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Checks if an order needs a prescription and assigns prescription numbers if needed.
     * @param {Object} transaction - The database transaction object
     * @param {Object} orderRec - The order record to check
     * @returns {Promise<void>}
     */
    async checkOrderNeedsPrescription(transaction, orderRec) {
        try {
            if (!orderRec) {
                throw this.fx.getClaraError(
                    `Missing Order Record to check if prescription is needed`
                );
            }
            if (!transaction) {
                throw this.fx.getClaraError(
                    `Missing Transaction to check if prescription is needed`
                );
            }

            if (
                orderRec.archived === true ||
                orderRec.deleted === true ||
                orderRec.void === "Yes"
            ) {
                return;
            }

            const orderItems = [
                ...(orderRec.subform_order || []),
                ...(orderRec.subform_single_order || []),
            ];
            const readyItems = _.filter(orderItems, (item) => {
                return (
                    item.order_complete === "Yes" &&
                    [OrderStatus.ACTIVE, OrderStatus.PENDING].includes(
                        item.status_id
                    ) &&
                    item.order_complete === "Yes" &&
                    !item?._meta?.delete &&
                    !item.rx_no
                );
            });
            if (readyItems.length === 0) {
                return;
            }

            for (const item of readyItems) {
                const labelFormat =
                    OrderTemplateLabelFormatMap[item.template_type];
                item.rx_no = transaction.series_next_number("RX");
                const frequencyId = item.frequency_id || null;

                const [
                    patientRec,
                    inventoryRec,
                    rxTemplateRec,
                    labelFormRec,
                    frequencyRec,
                    prescriberRec,
                ] = await Promise.all([
                    this.fetcher.fetchCachedRecord("patient", item.patient_id),
                    this.fetcher.fetchCachedRecord(
                        "inventory",
                        item.inventory_id
                    ),
                    this.fetcher.fetchCachedRecord(
                        "list_rx_template",
                        item.rx_template_id,
                        false,
                        true
                    ),
                    _.head(
                        await this.form.get.get_form(
                            this.ctx,
                            this.ctx.user,
                            "list_label",
                            {
                                filter: [
                                    `site_id:${orderRec.site_id}`,
                                    `active:Yes`,
                                    `label_format:${labelFormat}`,
                                ],
                            }
                        )
                    ),
                    frequencyId
                        ? this.fetcher.fetchCachedRecord(
                              "list_frequency",
                              frequencyId,
                              true,
                              true
                          )
                        : Promise.resolve(null),
                    this.fetcher.fetchCachedRecord(
                        "patient_prescriber",
                        item.prescriber_id || orderRec.prescriber_id
                    ),
                ]);

                if (!inventoryRec) {
                    throw this.fx.getClaraError(
                        `Missing Inventory Record for order item ID ${item.id}. Please verify the inventory item hasn't been archived or deleted.`
                    );
                }
                if (inventoryRec.active !== "Yes") {
                    throw this.fx.getClaraError(
                        `Inventory Record ${inventoryRec.auto_name} is not active. Cannot generate prescription.`
                    );
                }
                if (!labelFormRec) {
                    throw this.fx.getClaraError(
                        `Missing Label Record for template type ${item.template_type}. Cannot generate prescription.`
                    );
                }

                const physicianId = prescriberRec.physician_id;
                const physicianRec = await this.fetcher.fetchCachedRecord(
                    "physician",
                    physicianId
                );
                const refillDaysOutAllowed = parseInt(
                    this.shared.config.company.refill_days_out ??
                        DEFAULT_REFILLS_DAYS_OUT
                );
                const shippingDaysOutAllowed = parseInt(
                    this.shared.config.company.next_delivery_day ??
                        DEFAULT_SHIPPING_DAYS_OUT
                );
                let nextFillMoment = null;
                if (
                    item.start_date &&
                    (moment(item.start_date, "MM/DD/YYYY").isAfter(moment()) ||
                        moment(item.start_date, "MM/DD/YYYY").isSame(moment()))
                ) {
                    nextFillMoment = moment(item.start_date);
                } else if (
                    item.start_date &&
                    moment(item.start_date, "MM/DD/YYYY").isBefore(moment())
                ) {
                    nextFillMoment = moment();
                } else if (item.start_date) {
                    nextFillMoment = moment(
                        item.start_date,
                        "MM/DD/YYYY"
                    ).subtract(refillDaysOutAllowed, "days");
                }

                const expirationDate = item.expiration_date
                    ? moment(item.expiration_date)
                    : moment(item.written_date).add(365, "days");
                const nextDeliveryMoment =
                    [
                        OrderTemplateType.IV,
                        OrderTemplateType.TPN,
                        OrderTemplateType.COMPOUND,
                    ].includes(item.template_type) && nextFillMoment
                        ? nextFillMoment.add(shippingDaysOutAllowed, "days")
                        : null;

                const frequencyLabel =
                    frequencyRec?.frequency_label || frequencyRec?.name || null;
                const labelDesc =
                    `${inventoryRec.label_name || inventoryRec.name || null} ${frequencyLabel ? frequencyLabel : ""}`.trim();

                //TODO: Patrick Told to comment out for now.
                // const [labelSubform, _compoundItems] = await Promise.all([
                //     this.__buildSubformLabel(
                //         item,
                //         patientRec,
                //         inventoryRec,
                //         physicianRec
                //     ),
                //     // this.__buildCompoundItems(item, transaction),
                // ]);

                const labelSubform = await this.__buildSubformLabel(
                    item,
                    patientRec,
                    inventoryRec,
                    physicianRec
                );

                // TODO: Calculate dispense quantity from order record.
                const rxRecord = {
                    rx_no: item.rx_no,
                    rx_form_presc_order: labelDesc,
                    is_specialty: item.is_specialty,
                    site_id: orderRec.site_id,
                    patient_id: orderRec.patient_id,
                    careplan_id: orderRec.id,
                    order_no: orderRec.order_no,
                    therapy_id: item.therapy_id,
                    inventory_id: item.inventory_id,
                    status_id: item.status_id,
                    medid: inventoryRec.medid,
                    gcn_seqno: inventoryRec.gcn_seqno,
                    template_type: rxTemplateRec.template_type,
                    rx_template_id: rxTemplateRec.code,
                    refill_tracking: rxTemplateRec.refill_tracking,
                    status: PrescriptionStatus.SETUP,
                    refills: item?.refills || null,
                    daw_code: item?.ss_daw || null,
                    day_supply: item?.ss_day_supply || null,
                    start_date: item.start_date,
                    written_date: item?.written_date || orderRec.written_date,
                    expiration_date: expirationDate.format("MM/DD/YYYY"),
                    next_fill_date: nextFillMoment
                        ? nextFillMoment.format("MM/DD/YYYY")
                        : null,
                    next_delivery_date: nextDeliveryMoment
                        ? nextDeliveryMoment.format("MM/DD/YYYY")
                        : null,
                    label_id: labelFormRec.id,
                    label_form:
                        OrderTemplateLabelFormMap[rxTemplateRec.template_type],
                    subform_label: [labelSubform],
                    rxform_dr: orderRec.prescriber_id,
                    physician_id: orderRec.physician_id,
                    number_of_labels: rxTemplateRec.default_no_copies,
                    tpn_additives_label: rxTemplateRec.tpn_additives_label,
                    add_syringe_label: rxTemplateRec.add_syringe_label,
                    subform_syringe_label:
                        rxTemplateRec.add_syringe_label === "Yes"
                            ? await this.__syringeLabel(
                                  item,
                                  patientRec,
                                  inventoryRec,
                                  physicianRec
                              )
                            : null,
                    requires_nursing:
                        orderRec.requires_nursing === "Yes" ||
                        inventoryRec.requires_nursing === "Yes"
                            ? "Yes"
                            : null,
                    bill_notes: item.bill_notes,
                    auto_dc: inventoryRec.auto_dc,
                    is_refill: item.is_refill,
                    refill_rx_id: item.refill_rx_id,
                    next_fill_number: 1,
                    dea_schedule_id: inventoryRec.dea_schedule_id,
                    comp_dsg_fm_code: inventoryRec.comp_dsg_fm_code,
                    compound_type: inventoryRec.compound_type,
                };
                await transaction.insert("careplan_order_rx", rxRecord);
            }
        } catch (e) {
            const errorMessage = `Error checking if item needs prescription for order ID ${orderRec?.id}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Converts the template type of a prescription record
     * @param {Object} rxRec - The prescription record to convert
     * @param {number} rxTemplateId - The ID of the new template type
     * @returns {Promise<boolean>} True if the template type was converted, false otherwise
     * @throws {Error} If Rx template record is missing or template type is already correct
     */
    async convertRxRecTemplateType(rxRec, rxTemplateId) {
        try {
            const rxTemplateRec = await this.fetcher.fetchCachedRecord(
                "list_rx_template",
                rxTemplateId,
                false,
                true
            );

            if (!rxTemplateRec) {
                throw this.fx.getClaraError(
                    `Missing Rx Template Record for Rx template ID ${rxTemplateId}`
                );
            }
            if (rxRec.template_type === rxTemplateRec.template_type) {
                return true;
            }
            const labelFormat =
                OrderTemplateLabelFormatMap[rxTemplateRec.template_type];
            const [patientRec, inventoryRec, physicianRec, labelFormRec] =
                await Promise.all([
                    this.fetcher.fetchCachedRecord("patient", rxRec.patient_id),
                    this.fetcher.fetchCachedRecord(
                        "inventory",
                        rxRec.inventory_id
                    ),
                    this.fetcher.fetchCachedRecord(
                        "physician",
                        rxRec.physician_id
                    ),
                    _.head(
                        await this.form.get.get_form(
                            this.ctx,
                            this.ctx.user,
                            "list_label",
                            {
                                filter: [
                                    `site_id:${rxRec.site_id}`,
                                    `active:Yes`,
                                    `label_format:${labelFormat}`,
                                ],
                            }
                        )
                    ),
                ]);

            rxRec.template_type = rxTemplateRec.template_type;
            rxRec.rx_template_id = rxTemplateRec.code;
            rxRec.label_id = labelFormRec.id;
            rxRec.label_form =
                OrderTemplateLabelFormMap[rxTemplateRec.template_type];
            rxRec.number_of_labels = rxTemplateRec.default_no_copies;
            rxRec.tpn_additives_label = rxTemplateRec.tpn_additives_label;
            rxRec.add_syringe_label = rxTemplateRec.add_syringe_label;
            rxRec.subform_syringe_label =
                rxTemplateRec.add_syringe_label === "Yes"
                    ? await this.__syringeLabel(
                          rxRec,
                          patientRec,
                          inventoryRec,
                          physicianRec
                      )
                    : null;
            const newLabelSubform = await this.__buildSubformLabel(
                rxRec,
                patientRec,
                inventoryRec,
                physicianRec
            );
            const filteredLabelSubform = _.omitBy(newLabelSubform, _.isNull);
            const oldLabelSubform = rxRec.subform_label[0];
            const filteredOldLabelSubform = _.omit(
                _.omitBy(oldLabelSubform, _.isNull),
                [
                    "_meta",
                    "id",
                    "archived",
                    "deleted",
                    "created_by",
                    "created_on",
                    "updated_by",
                    "updated_on",
                ]
            );
            console.log("filteredOldLabelSubform", filteredOldLabelSubform);
            const updatedRxRec = _.omitBy(
                {
                    ..._.omit(rxRec, [
                        "_meta",
                        "archived",
                        "deleted",
                        "created_by",
                        "created_on",
                        "updated_by",
                        "updated_on",
                    ]),
                    template_type: rxTemplateRec.template_type,
                    subform_label: [
                        {
                            ...filteredOldLabelSubform,
                            ...filteredLabelSubform,
                        },
                    ],
                },
                _.isNull
            );
            console.log("updatedRxRec", updatedRxRec);
            return updatedRxRec;
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error converting Rx rec template type for Rx template ID ${rxTemplateId}`
            );
        }
    }

    /**
     * Checks if there are available prescriptions that can be added to a delivery ticket
     * @async
     * @param {Object} deliveryTicketRec - The delivery ticket record to check against
     * @returns {Promise<boolean>} True if there are available prescriptions, false otherwise
     * @throws {Error} If delivery ticket record is invalid or database query fails
     */
    async checkAvailableRxsAgainstDT(deliveryTicketRec) {
        try {
            await this.fx.validateSchema(
                "deliveryTicketRec",
                RecordSchemas.deliveryTicket.required(),
                deliveryTicketRec,
                "Missing delivery ticket record to check available Rxs against DT."
            );
            const rxIds = await this.fetcher.fetchDispenseReadyRxIds(
                deliveryTicketRec.patient_id
            );
            const currentRxIds = deliveryTicketRec.rx_id || [];
            const newRxIds = rxIds.filter((id) => !currentRxIds.includes(id));
            return newRxIds.length > 0;
        } catch (error) {
            const errorMessage = `Error checking available Rxs against DT for delivery ticket ID ${deliveryTicketRec.id}`;
            console.error(`${errorMessage}:`, error);
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Retrieves payer settings for an order.
     * @async
     * @param {Object} ticketNo - The delivery ticket number.
     * @returns {Promise<Object>} A promise that resolves to the payer settings object.
     * @throws {Error} If there's an error getting the dispense payer settings.
     */
    async getDispensePayerSettings(ticketNo) {
        console.log(`Getting dispense payer settings for delivery ticket...`);
        try {
            await this.fx.validateSchema(
                "ticketNo",
                Joi.string().required(),
                ticketNo,
                "Missing delivery ticket number to get dispense payer settings."
            );

            const sql = `
                SELECT check_payer_warnings(%L::text) AS result
            `;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [ticketNo]);
            const returnVal = rows[0].result;

            return returnVal;
        } catch (e) {
            const errorMessage = `Error getting dispense payer settings`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Gets the assessment data for a delivery ticket.
     * @async
     * @param {number} deliveryTicketId - The ID of the delivery ticket to get assessment for.
     * @returns {Promise<Object>} A promise that resolves to the delivery ticket assessment data.
     * @throws {Error} If there's an error getting the delivery ticket assessment.
     */
    async getDeliveryTicketAssessmentPresets(deliveryTicketId) {
        console.log(
            `Getting delivery ticket assessment presets for delivery ticket...`
        );
        try {
            await this.fx.validateSchema(
                "deliveryTicketId",
                Joi.number().required(),
                deliveryTicketId,
                "Missing delivery ticket ID to get delivery ticket assessment presets."
            );
            const sql = `
                SELECT get_delivery_ticket_assessment_presets(%s::integer) AS result
            `;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                deliveryTicketId,
            ]);
            const returnVal = rows[0].result;
            return returnVal;
        } catch (e) {
            const errorMessage = `Error getting delivery ticket assessment`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks if a prescription has associated inventory items
     *
     * @async
     * @param {Object} rxRec - The prescription record to check
     * @returns {Promise<boolean>} True if the prescription has inventory items, false otherwise
     * @throws {Error} If validation fails or there's an error checking the prescription items
     */
    async checkIfRxHasItems(rxRec) {
        try {
            await this.fx.validateSchema(
                "rxRec",
                RecordSchemas.prescription.required(),
                rxRec,
                "Missing prescription record to check if Rx has items."
            );
            const inventoryIds = await this.fetcher.fetchRxInventoryIds(
                rxRec.rx_no
            );
            return inventoryIds.length > 0;
        } catch (e) {
            const errorMessage = `Error checking if Rx has items for prescription ID ${rxRec.id}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Builds compound items for a given order item
     * @async
     * @param {Object} item - The order item to build compound items for
     * @param {Object} transaction - The transaction object
     * @returns {Promise<?Array>} Array of compound inventory items if found, null otherwise
     * @private
     */
    async __buildCompoundItems(item, transaction) {
        const sql = `
        SELECT inventory_id, COALESCE(quantity::numeric), 0::numeric)::numeric as dispense_quantity FROM vw_ss_patient_order_rx_cmp_prefill WHERE orderp_item_id = ${item.id}
        `;
        const rows = await this.db.env.rw.query(sql);
        for (const row of rows) {
            await transaction.insert("careplan_order_rx_cp", {
                inventory_id: row.inventory_id,
                rx_no: item.rx_no,
                patient_id: item.patient_id,
                dispense_quantity: row.dispense_quantity
            });
        }
        return true;
    }

    /**
     * Builds a subform label based on the template type
     * @async
     * @param {Object} item - The order item
     * @param {Object} patientRec - The patient record
     * @param {Object} inventoryRec - The inventory record
     * @param {Object} physicianRec - The physician record
     * @returns {Promise<Array>} Array of label objects
     * @private
     */
    async __buildSubformLabel(item, patientRec, inventoryRec, physicianRec) {
        switch (item.template_type) {
            case OrderTemplateType.IV:
            case OrderTemplateType.TPN:
            case OrderTemplateType.COMPOUND:
                return {
                    patient_id: item.patient_id,
                    rx_no: item.rx_no,
                    line3_rx: item.rx_no,
                    line9: inventoryRec.label_name || inventoryRec.name || null,
                    line1: inventoryRec.warning_label || null,
                    line2_pt: `${patientRec.firstname || ""} ${
                        patientRec.lastname || ""
                    }`.trim(),
                    line2_doc: `${physicianRec.first || ""} ${
                        physicianRec.last || ""
                    } ${physicianRec.title || ""}`.trim(),
                    storage_id: inventoryRec.storage_id || null,
                    line3_date: item.start_date
                        ? moment(item.start_date).format("MM/DD/YYYY")
                        : moment().format("MM/DD/YYYY"),
                };
            default:
                return {};
        }
    }

    /**
     * Builds a syringe label for a prescription
     * @async
     * @param {Object} item - The order item
     * @param {Object} patientRec - The patient record
     * @param {Object} inventoryRec - The inventory record
     * @param {Object} physicianRec - The physician record
     * @returns {Promise<Array>} Array of syringe label objects
     * @private
     */
    async __syringeLabel(item, patientRec, inventoryRec, physicianRec) {
        return [
            {
                patient_id: item.patient_id,
                line3_rx: item.rx_no,
                line1: inventoryRec.warning_label || null,
                line9: inventoryRec.label_name || inventoryRec.name || null,
                line2_pt:
                    `${patientRec.firstname} ${patientRec.lastname}`.trim(),
                line2_doc:
                    `${physicianRec.first} ${physicianRec.last} ${physicianRec.title}`.trim(),
                storage_id: inventoryRec.storage_id || null,
                line3_date: moment(item.start_date || null).format(
                    "MM/DD/YYYY"
                ),
            },
        ];
    }
    /**
     * Checks if a prescription is complete based on verification status and specialty flag
     * @async
     * @param {Object} rxRec - The prescription record to check
     * @returns {Promise<boolean>} True if prescription is complete, false otherwise
     * @private
     */
    async __checkRxComplete(rxRec) {
        const isComplete =
            rxRec.rx_verified === "Yes" && rxRec.rx_complete === "Yes";
        return isComplete;
    }

    /**
     * Checks if a prescription is ready to fill based on its next fill date
     * @async
     * @param {Object} rxRec - The prescription record to check
     * @returns {Promise<boolean>} True if prescription can be filled today based on next fill date, false otherwise
     * @private
     */
    async __checkRxReadyToFillByDate(rxRec) {
        const nextFillDate = rxRec.next_fill_date;
        const canFillToday = nextFillDate
            ? moment(nextFillDate).isSameOrBefore(moment())
            : false;
        return canFillToday;
    }
};
