/*jshint : 6 */
"use strict";

const _ = require("lodash");
const Joi = require("joi");

const { PLACEHOLDER_RECORD_ID } = require("./settings");

const { RecordSchemas } = require("./schemas");

/**
 * @class
 * @classdesc Class responsible for fetching billing-related data.
 */
module.exports = class DispenseFetcherClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    get sharedCache() {
        return this.fx.getContextCache(this.ctx);
    }

    /**
     * Fetches cached records for the specified form and form IDs.
     * @param {string} form - The form identifier.
     * @param {Array<string>} formIds - Array of form IDs to fetch.
     * @param {boolean} codeBased - Fetches record by code instead of id
     * @throws {Error} Throws an error if form IDs or form identifier is not provided.
     */
    async fetchCachedRecords(form, formIds, codeBased = false) {
        try {
            if (!this.shared.DSL[form])
                throw this.fx.getClaraError(`Invalid form (${form})`);
            await this.fx.validateSchema(
                "formIds",
                Joi.array().required(),
                formIds,
                "Missing/Invalid form IDs to fetch records form: " + form
            );

            if (formIds.length === 0) {
                return [];
            }
            const cachedRecords = [];
            const missingIds = [];
            const cachePromises = [];

            // Batch cache retrieval
            for (const formId of formIds) {
                const key = `${form}-${formId}`;
                cachePromises.push(this.sharedCache.get(key));
            }

            const cacheResults = await Promise.all(cachePromises);

            cacheResults.forEach((cachedRecord, index) => {
                if (cachedRecord) {
                    cachedRecords.push(cachedRecord);
                } else {
                    missingIds.push(formIds[index]);
                }
            });

            if (missingIds.length === 0) {
                return cachedRecords;
            }

            const formsToFetch = missingIds.map((id) =>
                codeBased ? `code:${id}` : `id:${id}`
            );
            const formRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                form,
                { filter: formsToFetch }
            );

            const setCachePromises = formRecs.map((formRec) => {
                const key = `${form}-${formRec.id}`;
                return this.sharedCache.set(key, formRec);
            });

            await Promise.all(setCachePromises);

            return [...cachedRecords, ...formRecs];
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching cached records for form ${form}: ${e.message}`
            );
        }
    }

    /**
     * Fetches a cached record for the specified form and form ID.
     * @param {string} form - The form identifier.
     * @param {number} formId - The form ID to fetch.
     * @param {boolean} suppressErrors - Suppresses throwing an error if record not found
     * @param {boolean} codeBased - Fetches record by code instead of id
     * @throws {Error} Throws an error if form ID or form identifier is not provided.
     */
    async fetchCachedRecord(
        form,
        formId,
        suppressErrors = false,
        codeBased = false
    ) {
        try {
            if (!this.shared.DSL[form])
                throw this.fx.getClaraError(`Invalid form (${form})`);
            await this.fx.validateSchema(
                "formId",
                Joi.alternatives().try(Joi.string(), Joi.number()).required(),
                formId,
                "Missing/Invalid form ID to fetch record."
            );

            if (formId === PLACEHOLDER_RECORD_ID) return null;

            const key = `${form}-${formId}`;
            const existingRec = await this.sharedCache.get(key);
            if (existingRec) {
                console.debug(`Fetching cached record for ${form} ${formId}`);
                return existingRec;
            }

            const filters =
                this.fx.isNumber(formId) && !codeBased
                    ? [`id:${formId}`]
                    : [`code:${formId}`];
            const result = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                form,
                {
                    limit: 1,
                    filter: filters,
                }
            );
            const formRec = _.head(result);

            if (!formRec) {
                if (!suppressErrors) {
                    throw this.fx.getClaraError(
                        `Invalid ${form} record with id ${formId}, check that record is not archived.`
                    );
                }
                return null;
            }

            await this.sharedCache.set(key, formRec);
            return formRec;
        } catch (e) {
            console.error(
                `Error fetching cached record for form ${form}: ${formId} : ${e.message || e.error_text}`
            );
            throw this.fx.wrapError(
                e,
                `Error fetching cached record for form ${form}: ${formId} : ${e.message || e.error_text}`
            );
        }
    }

    /**
     * Fetches an prescription by its RX number.
     * @async
     * @param {string} rxNo - The RX number to search for.
     * @returns {Promise<Object>} The prescription record.
     * @throws {Error} If no prescription is found or if there's an error during the fetch.
     */
    async fetchOrderItemByRxNo(rxNo) {
        try {
            await this.fx.validateSchema(
                "rxNo",
                Joi.string().required(),
                rxNo,
                "Missing/Invalid RX number to fetch prescription."
            );
            let itemFrom = "careplan_order_item";
            const filters = [`rx_no:${rxNo}`];
            const orderItemRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "careplan_order_item",
                { filter: filters }
            );

            const singleRxOrderRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "careplan_orderp_item",
                { filter: filters }
            );
            if (singleRxOrderRecs.length > 0) {
                itemFrom = "careplan_orderp_item";
            }
            const allOrderRecs = [...orderItemRecs, ...singleRxOrderRecs];
            if (allOrderRecs.length === 0) {
                throw this.fx.getClaraError(
                    `No prescription found for rx no ${rxNo}.`
                );
            }
            if (allOrderRecs.length > 1) {
                throw this.fx.getClaraError(
                    `Multiple prescriptions found for rx no ${rxNo}.`
                );
            }
            const orderItemRec = _.head(allOrderRecs);
            return { orderItemRec, itemFrom };
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching prescription by rx no: ${e.message}`
            );
        }
    }

    /**
     * Fetches the last claim for a given prescription.
     * @param {string} orderItemId - The ID of the prescription to fetch the test claim for.
     * @returns {Promise<Object|null>} The last successful test claim object if found, or null if not found.
     */
    async fetchLastOrderItemClaims(orderItemId) {
        try {
            await this.fx.validateSchema(
                "orderItemId",
                Joi.number().required(),
                orderItemId,
                "Missing/Invalid prescription ID to fetch claim."
            );
            // TODO handle with new SQL query
            return { formRec: null, formName: null };
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching last prescription claim: ${e.message}`
            );
        }
    }

    /**
     * Fetches a delivery ticket record using the ticket number
     * @async
     * @param {string} ticketNo - The ticket number to fetch the delivery ticket for
     * @returns {Promise<Object|null>} The delivery ticket record if found, or null if not found
     * @throws {Error} If there is an error fetching the delivery ticket record
     */
    async fetchDeliveryTicketRecWithTicketNo(ticketNo) {
        try {
            await this.fx.validateSchema(
                "ticketNo",
                Joi.string().required(),
                ticketNo,
                "Missing/Invalid ticket no to fetch delivery ticket record."
            );
            const dtRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "careplan_delivery_tick",
                    {
                        filter: [`ticket_no:${ticketNo}`],
                    }
                )
            );
            return dtRec;
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching delivery ticket record with ticket no: ${e.message}`
            );
        }
    }

    /**
     * Fetches active prescription records for a given patient.
     * @param {number} patientId - The ID of the patient to fetch active prescriptions for.
     * @returns {Promise<Array>} Array of active prescription record IDs for the patient.
     */
    async fetchActivePrescriptionRecordIdsMissingDeliveryTicket(patientId) {
        try {
            await this.fx.validateSchema(
                "patientId",
                Joi.number().required(),
                patientId,
                "Missing/Invalid patient ID to fetch active prescription records."
            );
            const sql = `
                SELECT 
                    rx_id
                FROM vw_working_prescriptions wrx
                WHERE patient_id = %s AND wrx.delivery_ticket_id IS NULL`;
            const rows = await this.db.env.rw.query(sql, [patientId]);
            if (rows.length > 0) {
                const rxIds = rows.map((row) => row.rx_id);
                return rxIds;
            }
            return [];
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching last prescription claim: ${e.message}`
            );
        }
    }

    /**
     * Fetches active dispense record IDs associated with a delivery ticket record
     * @param {Object} deliveryTicketRec - The delivery ticket record to fetch active dispense record IDs for
     * @returns {Promise<Array>} Array of active dispense record IDs associated with the delivery ticket
     * @throws {Error} If delivery ticket record is invalid or database query fails
     */
    async fetchDeliveryTicketRecActiveDispenseRecordIds(deliveryTicketRec) {
        try {
            await this.fx.validateSchema(
                "deliveryTicketRec",
                RecordSchemas.deliveryTicket.required(),
                deliveryTicketRec,
                "Missing/Invalid delivery ticket record to fetch active dispense records."
            );
            const rxwIdsArray = this.fx.pgArrayFormat(deliveryTicketRec.rx_id);
            const sql = `
                SELECT 
                    working_dispense_id
                FROM vw_working_prescriptions wrx
                WHERE wrx.rx_id = ANY('${rxwIdsArray}'::integer[])`;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql);
            console.log("Working ticket results", rows);
            if (rows.length > 0) {
                const wdtIds = rows.map((row) => row.working_dispense_id);
                return wdtIds;
            }
            return [];
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching delivery ticket active rx dispense records: ${e.message}`
            );
        }
    }

    /**
     * Fetches the working prescription record for a given prescription ID
     * @param {number} rxId - The ID of the prescription to fetch the working record for
     * @returns {Promise<Object|null>} The working prescription record if found, null otherwise
     * @throws {Error} If prescription ID is invalid or database query fails
     */
    async fetchWorkingPrescriptionRecord(rxId) {
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing/Invalid prescription ID to fetch working prescription record."
            );
            const workingPrescriptionId =
                await this.fetchWorkingPrescriptionRecordId(rxId);
            if (workingPrescriptionId) {
                const workingPrescriptionRec = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "careplan_order_rx_disp",
                    { filter: [`id:${workingPrescriptionId}`] }
                );
                return workingPrescriptionRec[0];
            }
            return null;
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching working prescription record: ${e.message}`
            );
        }
    }

    /**
     * Fetches inventory IDs associated with a prescription number
     * @async
     * @param {string} rxNo - The prescription number to fetch inventory IDs for
     * @returns {Promise<Array<number>>} Array of inventory IDs associated with the prescription
     * @throws {Error} If prescription number is invalid or database query fails
     */
    async fetchRxInventoryIds(rxNo) {
        try {
            await this.fx.validateSchema(
                "rxNo",
                Joi.string().required(),
                rxNo,
                "Missing/Invalid prescription No to fetch inventory IDs."
            );
            const sql = `
                SELECT 
                    inventory_id
                FROM vw_rx_fill_items
                WHERE rx_no = %s::text`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [rxNo]);
            if (rows.length > 0) {
                const inventoryIds = rows.map((row) => row.inventory_id);
                return inventoryIds;
            }
            return [];
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching inventory IDs for prescription No ${rxNo}: ${e.message}`
            );
        }
    }

    /**
     * Fetches the working prescription record for a given prescription ID.
     * @param {number} rxId - The ID of the prescription to fetch the working record for.
     * @returns {Promise<number|null>} The working dispense ID if found, null otherwise.
     */
    async fetchWorkingPrescriptionRecordId(rxId) {
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing/Invalid prescription ID to fetch working prescription record."
            );
            const sql = `
                SELECT 
                    working_dispense_id
                FROM vw_working_prescriptions wrx
                WHERE rx_id = %s::integer`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [rxId]);
            if (rows.length > 0) {
                const workingDispenseId = rows[0].working_dispense_id;
                return workingDispenseId;
            }
            return null;
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching working prescription record: ${e.message}`
            );
        }
    }

    /**
     * Fetches prescription IDs for working prescriptions without delivery tickets for a given patient.
     * @param {number} patientId - The ID of the patient to fetch working prescriptions without tickets for.
     * @returns {Promise<Array>} Array of prescription IDs for working prescriptions without delivery tickets.
     */
    async fetchWorkingPrescriptionsWOTickets(patientId) {
        try {
            await this.fx.validateSchema(
                "patientId",
                Joi.number().required(),
                patientId,
                "Missing/Invalid patient ID to fetch working prescription records without tickets."
            );
            const sql = `
                SELECT 
                    rx_id
                FROM vw_working_prescriptions wrx
                WHERE patient_id = %s::integer AND wrx.delivery_ticket_id IS NULL`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                patientId,
            ]);
            if (rows.length > 0) {
                const rxIds = rows.map((row) => row.rx_id);
                return rxIds;
            }
            return [];
        } catch (e) {
            throw this.fx.wrapError(
                e,
                `Error fetching working prescription records without tickets: ${e.message}`
            );
        }
    }

    /**
     * Fetches the open delivery ticket ID for a given prescription ID.
     * @async
     * @param {number} rxId - The ID of the prescription to fetch the open delivery ticket for.
     * @returns {Promise<number|null>} The delivery ticket ID if found, null otherwise.
     * @throws {Error} If there is an error fetching the delivery ticket ID.
     */
    async fetchRxOpenDeliveryTicketId(rxId) {
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing/Invalid prescription ID to fetch open delivery ticket ID."
            );
            const sql = `
                SELECT 
                    delivery_ticket_id
                FROM vw_working_prescriptions wrx
                WHERE rx_id = %s::integer`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [rxId]);
            if (rows.length > 0) {
                const workingDispenseId = rows[0].delivery_ticket_id;
                return workingDispenseId;
            }
            return null;
        } catch (e) {
            const errorMessage = `Error fetching open delivery ticket ID for prescription ID ${rxId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches all delivery ticket items associated with a delivery ticket ID.
     * @async
     * @param {Object} deliveryTicketRec - The delivery ticket record to fetch items for.
     * @returns {Promise<Array>} Array of delivery ticket item records associated with the delivery ticket.
     * @throws {Error} If there is an error fetching the delivery ticket items.
     */
    async fetchDeliveryTicketItems(deliveryTicketRec) {
        try {
            await this.fx.validateSchema(
                "deliveryTicketRec",
                RecordSchemas.deliveryTicket.required(),
                deliveryTicketRec,
                "Missing/Invalid delivery ticket record to fetch delivery ticket items."
            );
            const filters = [`ticket_no:${deliveryTicketRec.ticket_no}`];
            const deliveryTicketItems = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "careplan_dt_item",
                { filter: filters, sort: "-id" }
            );
            return deliveryTicketItems;
        } catch (e) {
            const errorMessage = `Error fetching delivery ticket items for ticket no ${deliveryTicketRec.ticket_no}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches all delivery tickets associated with a prescription ID.
     * @async
     * @param {number} rxId - The ID of the prescription to fetch delivery tickets for.
     * @returns {Promise<Array>} Array of delivery ticket records associated with the prescription.
     * @throws {Error} If there is an error fetching the delivery ticket records.
     */
    async fetchRxDeliveryTickets(rxId) {
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing/Invalid prescription ID to fetch delivery tickets."
            );
            const filters = [`rx_id:${rxId}`, `void:!Yes`];
            const deliveryTicketRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "careplan_delivery_tick",
                { filter: filters, sort: "-id" }
            );
            return deliveryTicketRecs;
        } catch (e) {
            const errorMessage = `Error fetching delivery tickets for prescription ID ${rxId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the last successful test claim for a given prescription.
     * @param {string} orderItemId - The ID of the prescription to fetch the test claim for.
     * @returns {Promise<Object|null>} The last successful test claim object if found, or null if not found.
     */
    async fetchLastOrderItemClaim(orderItemId) {
        try {
            await this.fx.validateSchema(
                "orderItemId",
                Joi.number().required(),
                orderItemId,
                "Missing/Invalid prescription ID to fetch claim."
            );

            const formName = "ncpdp";
            const claimFilters = [
                `rx_no:${orderItemId}`,
                `void:!Yes`,
                `is_test:!Yes`,
                `is_payable:Yes`,
            ];
            const ncpdpRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                formName,
                { filter: claimFilters, limit: 1, sort: "-id" }
            );
            if (ncpdpRecs.length > 0) {
                const invoiceGroupRec = _.head(ncpdpRecs);
                return { formRec: invoiceGroupRec, formName };
            }

            return { formRec: null, formName: null };
        } catch (e) {
            const errorMessage = `Error fetching last successful prescription claim for order item ID ${orderItemId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the patient's home address record.
     * @param {number} patientId - The ID of the patient to fetch the address for.
     * @returns {Promise<Object|null>} The patient's home address record if found, or null if not found.
     * @throws {Error} If there's an error fetching the patient's home address.
     */
    async fetchPatientHomeAddress(patientId) {
        try {
            await this.fx.validateSchema(
                "patientId",
                Joi.number().required(),
                patientId,
                "Missing/Invalid patient ID to fetch home address."
            );

            const filters = [`patient_id:${patientId}`];
            const patientAddressRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "patient_address",
                { filter: filters, limit: 1, sort: "-id" }
            );
            if (patientAddressRecs.length > 0) {
                const homeAddress = patientAddressRecs.find(
                    (rec) => rec.address_type === "Home"
                );
                if (homeAddress) return homeAddress;
                const shippingAddress = patientAddressRecs.find(
                    (rec) => rec.address_type === "Shipping"
                );
                if (shippingAddress) return shippingAddress;
                return _.head(patientAddressRecs);
            }
            return null;
        } catch (e) {
            const errorMessage = `Error fetching patient home address for patient ID ${patientId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches invoices associated with a delivery ticket.
     * @param {number} deliveryTicketId - The ID of the delivery ticket to fetch invoices for.
     * @returns {Promise<Array>} Array of invoice records associated with the delivery ticket.
     * @throws {Error} If there's an error fetching the delivery ticket invoices.
     */
    async fetchDeliveryTicketInvoices(deliveryTicketId) {
        try {
            if (!deliveryTicketId) return [];
            const formName = "billing_invoice";
            const filters = [
                `delivery_ticket_id:${deliveryTicketId}`,
                `void:!Yes`,
            ];
            const invoiceRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                formName,
                { filter: filters, sort: "-id" }
            );
            return invoiceRecs;
        } catch (e) {
            const errorMessage = `Error fetching delivery ticket invoices for delivery ticket ID ${deliveryTicketId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks if an order item has a pharmacy payer.
     * @param {Object} orderRec - The order record.
     * @param {Object} orderItemRec - The order item record.
     * @returns {boolean} True if the order item has a pharmacy payer, false otherwise.
     */
    async fetchOrderItemFirstPharmacyPayer(orderRec, orderItemRec) {
        try {
            const payerIdsWithRank = orderRec?.payer_ids || [];
            const orderItemPayerIds = orderItemRec?.payer_ids || [];

            // Use orderItemPayerIds if available, otherwise fall back to payerIdsWithRank
            const payersToUse =
                orderItemPayerIds.length > 0
                    ? orderItemPayerIds
                    : payerIdsWithRank;

            // Get array of all payer IDs
            const allPayerIds = payersToUse.map((payer) => payer.id);
            const allInsuranceRecs = await this.fetcher.fetchCachedRecords(
                "patient_insurance",
                allPayerIds
            );
            const pharmacyPayer = _.sortBy(allInsuranceRecs, (insurance) => {
                const payerInfo = payersToUse.find(
                    (payer) => payer.id === insurance.id
                );
                return payerInfo ? parseInt(payerInfo.rank) : Number.MAX_VALUE;
            }).find((insurance) => insurance.billing_method_id === "ncpdp");
            return pharmacyPayer;
        } catch (e) {
            const errorMessage = `Error checking if order item has pharmacy payer`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Gets the primary insurance ID from either the order or order item record
     * @param {Object} orderRec - The order record containing payer IDs with rank
     * @param {Object} orderItemRec - The order item record containing payer IDs
     * @returns {string|null} The primary insurance ID if found, null otherwise
     */
    async fetchPrimaryInsuranceId(orderRec, orderItemRec) {
        try {
            const payerIdsWithRank = orderRec?.payer_ids || [];
            const orderItemPayerIds = orderItemRec?.payer_ids || [];
            const getPrimaryPayerId = () => {
                try {
                    if (!orderItemPayerIds && !payerIdsWithRank) {
                        return null;
                    }
                    const primaryOrderItemPayer = orderItemPayerIds.find(
                        (payer) => payer.rank === 1 || payer.rank === "1"
                    );
                    if (primaryOrderItemPayer) {
                        return primaryOrderItemPayer.id;
                    }
                    const primaryOrderPayer = payerIdsWithRank.find(
                        (payer) => payer.rank === 1 || payer.rank === "1"
                    );
                    if (primaryOrderPayer) {
                        return primaryOrderPayer.id;
                    }

                    return null;
                } catch (e) {
                    console.error(
                        `Error getting primary payer ID: ${e.message}`
                    );
                    return null;
                }
            };

            const primaryPayerId = getPrimaryPayerId();
            return primaryPayerId;
        } catch (e) {
            const errorMessage = `Error getting primary insurance ID`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the active supply order delivery ticket for a given order item.
     * @param {Object} ordersItemRec - The order item record containing inventory and prescription IDs.
     * @returns {Promise<number|null>} The delivery ticket ID if found, null otherwise.
     * @throws {Error} If there is an error fetching the delivery ticket.
     */
    async fetchActiveSupplyOrderDeliveryTicket(ordersItemRec) {
        console.debug(
            `Fetching supply order delivery ticket for order item ID ${ordersItemRec.id}`
        );
        try {
            const sql = `
                SELECT 
                    delivery_ticket_id
                FROM vw_open_delivery_ticket_items odti
                WHERE odti.inventory_id = %s::integer AND odti.rx_id = %s::integer`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                ordersItemRec.inventory_id,
                ordersItemRec.associated_rx_id,
            ]);
            if (rows.length > 0) {
                const deliveryTicketId = rows[0].delivery_ticket_id;
                return deliveryTicketId;
            }
            return null;
        } catch (e) {
            const errorMessage = `Error fetching active supply order delivery ticket for order item ID ${ordersItemRec.id}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches prescription IDs that are ready to be dispensed for a given patient
     * @param {number} patientId - The ID of the patient to fetch prescriptions for
     * @returns {Promise<number[]>} Array of prescription IDs that are ready to be dispensed
     * @throws {Error} If patient ID is invalid or database query fails
     */
    async fetchDispenseReadyRxIds(patientId) {
        try {
            await this.fx.validateSchema(
                "patientId",
                Joi.number().required(),
                patientId,
                "Missing/Invalid patient ID to fetch dispense ready prescription IDs."
            );
            const sql = `
                SELECT 
                    rx_id
                FROM vw_working_prescriptions wrx
                WHERE wrx.patient_id = %s::integer AND wrx.delivery_ticket_id IS NULL`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                patientId,
            ]);
            if (rows.length > 0) {
                const rxIds = rows.map((row) => row.rx_id);
                return rxIds;
            }
            return [];
        } catch (e) {
            const errorMessage = `Error fetching dispense ready prescriptions for patient ID ${patientId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the payer and insurance record based on the given insurance ID.
     * @param {string} insuranceId - The ID of the insurance record.
     * @throws {Error} Throws an error if the insurance ID is invalid.
     */
    async fetchPayerAndInsuranceRec(insuranceId) {
        try {
            await this.fx.validateSchema(
                "insuranceId",
                Joi.number().required(),
                insuranceId,
                "Missing/Invalid insurance ID to fetch payer and insurance record."
            );
            const insuranceRec = await this.fetchCachedRecord(
                "patient_insurance",
                insuranceId
            );
            if (insuranceRec) {
                const payerId = insuranceRec.payer_id;
                const payerRec = await await this.fetchCachedRecord(
                    "payer",
                    payerId
                );
                return { insuranceRec, payerRec };
            } else {
                throw this.fx.getClaraError(
                    `Invalid Patient Insurance record (${insuranceId}), check that record is not archived.`
                );
            }
        } catch (e) {
            const errorMessage = `Error fetching payer and insurance record for insurance ID ${insuranceId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
