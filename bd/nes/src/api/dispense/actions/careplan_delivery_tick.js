"use strict";

const { ActionHandler } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const DispenseDeliveryTicketGetButtonsHandler = require("./delivery_ticket/buttons");
            const getActionsHandler =
                new DispenseDeliveryTicketGetButtonsHandler(this.nes, ctx);
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error encountered while checking delivery ticket actions.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const DispenseDeliveryTicketPerformActionHandler = require("./delivery_ticket/actions");
            const actionsHandler =
                new DispenseDeliveryTicketPerformActionHandler(this.nes, ctx);
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing delivery ticket action ${action} for delivery ticket ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );

        try {
            const DispenseDeliveryTicketPostActionsHandler = require("./delivery_ticket/posts");
            const postActionsHandler =
                new DispenseDeliveryTicketPostActionsHandler(this.nes, ctx);
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing delivery ticket action ${action} for delivery ticket ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
