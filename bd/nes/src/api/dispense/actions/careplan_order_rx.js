"use strict";

const { ActionHandler } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const DispenseOrderRxGetButtonsHandler = require("./order_rx/buttons");
            const getActionsHandler = new DispenseOrderRxGetButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error processing prescription actions for prescription ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const DispenseOrderRxPerformActionHandler = require("./order_rx/actions");
            const actionsHandler = new DispenseOrderRxPerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing prescription action ${action} for prescription ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );

        try {
            const DispenseOrderRxPostActionsHandler = require("./order_rx/posts");
            const postActionsHandler = new DispenseOrderRxPostActionsHandler(
                this.nes,
                ctx
            );
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing prescription action ${action} for prescription ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
