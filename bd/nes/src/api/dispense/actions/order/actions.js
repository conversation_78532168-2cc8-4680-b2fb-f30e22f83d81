"use strict";
const _ = require("lodash");
const moment = require("moment");

const {
    OrderActionKeys,
    OrderActions,
    OrderFormat,
    OrderStatus,
    DisplayType,
} = require("@dispense/settings");

const { ActionResponseWrappers, NextAction } = require("@actions");
const { ButtonsActionsHandler } = require("../index");

module.exports = class DispenseOrderPerformActionHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.ctx = ctx;
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Executes a specific action on an order.
     * @async
     * @param {string} id - The ID of the order.
     * @param {string} action - The action to be performed on the order.
     * @returns {Promise<Object>} The result of the action execution.
     * @throws {Error} If there's an error during the action execution.
     */
    async runAction(id, action) {
        console.log(`Processing order action ${action} for order ID ${id}`);

        try {
            const orderAction = _.invert(OrderActionKeys)[action];
            if (!orderAction) {
                throw this.fx.getClaraError(`Invalid action: ${action}`);
            }

            if (orderAction === OrderActions.FACTOR_ORDER) {
                if (
                    this.auth.can_access_path(
                        this.ctx,
                        this.ctx.user,
                        "/order/can/factor_order"
                    )
                )
                    return ActionResponseWrappers.runFunction(
                        "showFactorOrderView"
                    );
                else
                    return ActionResponseWrappers.error(
                        "You do not have permission to factor orders."
                    );
            }
            if (!id) {
                return ActionResponseWrappers.error(
                    `Invalid order record ID. Cannot run action.`
                );
            }
            switch (orderAction) {
                case OrderActions.VOID:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/order/can/void"
                        )
                    )
                        return await this.__voidOrder(id);
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to void orders."
                        );
                case OrderActions.DISCONTINUE_ORDER:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/order/can/discontinue"
                        )
                    )
                        return await this.__discontinueOrder(id);
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to discontinue orders."
                        );
                case OrderActions.FILL_SUPPLY:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/order/can/fill_supply"
                        )
                    )
                        return await this.__fillSupply(id);
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to fill supply orders."
                        );
            }
        } catch (e) {
            const errorMessage = `Error processing order action. Action: ${action}, Order ID: ${id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Voids an order.
     * @async
     * @param {string|number} orderId - The ID of the order to void.
     * @returns {Promise<Object>} A promise that resolves to an ActionResponseWrapper containing the popup configuration for voiding the order.
     * @throws {Error} If an error occurs during the process of voiding the order.
     */
    async __voidOrder(orderId) {
        console.debug(`Voiding order ID ${orderId}`);
        try {
            const callbackUrl = `/api/form/careplan_order/${orderId}/action/void`;
            return ActionResponseWrappers.popup(
                {
                    label: "Void Order",
                    form: "careplan_order",
                    sections: ["Void"],
                    preset: {
                        void: "Yes",
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Save",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while voiding order ID ${orderId}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Discontinues an order.
     * @async
     * @param {string|number} orderId - The ID of the order to discontinue.
     * @returns {Promise<Object>} A promise that resolves to an ActionResponseWrapper indicating the result of discontinuing the order.
     * @throws {Error} If an error occurs during the process of discontinuing the order.
     */
    async __discontinueOrder(orderId) {
        console.debug(`Discontinuing order ID ${orderId}`);
        try {
            const orderRec = await this.fetcher.fetchCachedRecord(
                "careplan_order",
                orderId
            );
            if (!orderRec) {
                return ActionResponseWrappers.error(
                    `Unable to discontinue order. Order not found.`
                );
            }

            if (orderRec.order_format === OrderFormat.SUPPLY_ORDER) {
                const orderItemRec = orderRec.subform_single_supply[0];
                if (!orderItemRec) {
                    return ActionResponseWrappers.error(
                        `Unable to discontinue order. Order item not found.`
                    );
                }
                const transaction = this.db.env.rw.transaction(this.ctx);
                await transaction.update(
                    "careplan_order_item",
                    {
                        status_id: "2",
                        discontinued_date: moment().format("MM/DD/YYYY"),
                    },
                    orderItemRec.id
                );
                const res = await transaction.commit();
                if (res.error) {
                    console.error(
                        `Error encountered discontinuing order: ${res.error}`
                    );
                    return ActionResponseWrappers.error(
                        `Unable to discontinue order. Please try contact support if issue persists.`
                    );
                }
                transaction.init();

                return ActionResponseWrappers.success(
                    `Order discontinued successfully.`,
                    NextAction.CLOSE
                );
            } else if (orderRec.order_format === OrderFormat.SINGLE) {
                const orderItemRec = orderRec.subform_single_order[0];
                if (!orderItemRec) {
                    return ActionResponseWrappers.error(
                        `Unable to discontinue order. Order item not found.`
                    );
                }
                const transaction = this.db.env.rw.transaction(this.ctx);
                await transaction.update(
                    "careplan_order_item",
                    {
                        status_id: "2",
                        discontinued_date: moment().format("MM/DD/YYYY"),
                    },
                    orderItemRec.id
                );
                const res = await transaction.commit();
                if (res.error) {
                    console.error(
                        `Error encountered discontinuing order: ${res.error}`
                    );
                    return ActionResponseWrappers.error(
                        `Unable to discontinue order. Please try contact support if issue persists.`
                    );
                }
                transaction.init();

                return ActionResponseWrappers.success(
                    `Order discontinued successfully.`,
                    NextAction.CLOSE
                );
            } else if (orderRec.order_format === OrderFormat.THERAPY_SET) {
                const orderItems = orderRec.subform_order;
                const transaction = this.db.env.rw.transaction(this.ctx);

                for (const orderItem of orderItems) {
                    if (
                        ![OrderStatus.ACTIVE, OrderStatus.PENDING].includes(
                            orderItem.status_id
                        )
                    ) {
                        continue;
                    }
                    await transaction.update(
                        "careplan_order_item",
                        {
                            status_id: "2",
                            discontinued_date: moment().format("MM/DD/YYYY"),
                        },
                        orderItem.id
                    );
                }
                if (!transaction.can_commit()) {
                    return ActionResponseWrappers.error(
                        `Unable to discontinue therapy set. Associated orders not active or pending.`
                    );
                }
                await transaction.update(
                    "careplan_order",
                    {
                        therapy_set_dc: "Yes",
                    },
                    orderRec.id
                );
                const res = await transaction.commit();
                if (res.error) {
                    console.error(
                        `Error encountered discontinuing order: ${res.error}`
                    );
                    return ActionResponseWrappers.error(
                        `Unable to discontinue order. Please try contact support if issue persists.`
                    );
                }
                transaction.init();

                return ActionResponseWrappers.success(
                    `Order discontinued successfully.`,
                    NextAction.CLOSE
                );
            }
        } catch (e) {
            const errorMessage = `Error encountered while discontinuing order ID ${orderId}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fills a supply order.
     * @async
     * @param {string} orderId - The ID of the order to fill supply for.
     * @returns {Promise<Object>} The result of the fill supply action.
     * @throws {Error} If there's an error during the fill supply process.
     */
    async __fillSupply(orderId) {
        console.debug(`Filling supply for order ID ${orderId}`);
        try {
            const orderRec = await this.fetcher.fetchCachedRecord(
                "careplan_order",
                orderId
            );
            if (!orderRec) {
                return ActionResponseWrappers.error(
                    `Unable to fill supply order. Order not found.`
                );
            }
            const orderItemRec = orderRec.subform_single_supply?.[0];
            if (!orderItemRec) {
                return ActionResponseWrappers.error(
                    `Unable to fill supply order. Supply order not found.`
                );
            }
            const associatedRxId = orderItemRec.associated_rx_id;
            if (!associatedRxId) {
                return ActionResponseWrappers.error(
                    `Unable to fill supply order. Associated prescription not found.`
                );
            }
            const rxRec = await this.fetcher.fetchCachedRecord(
                "careplan_order_rx",
                orderRec.associated_rx_id
            );
            if (!rxRec) {
                return ActionResponseWrappers.error(
                    `Unable to fill supply order. Prescription not found.`
                );
            }
            if (rxRec.discontinued === "Yes") {
                return ActionResponseWrappers.error(
                    `Unable to fill supply order. Associated prescription has been discontinued.`
                );
            }
            const deliveryTicketId =
                await this.fetcher.fetchActiveSupplyOrderDeliveryTicket(
                    orderItemRec
                );
            if (deliveryTicketId) {
                return ActionResponseWrappers.edit(
                    deliveryTicketId,
                    "careplan_delivery_tick",
                    {},
                    null,
                    null,
                    null,
                    null,
                    null,
                    DisplayType.TAB,
                    NextAction.REFRESH
                );
            }

            const callbackUrl = `/api/form/careplan_order/${orderId}/action/${OrderActionKeys[OrderActions.FILL_SUPPLY]}`;
            const serviceFrom = moment().format("MM/DD/YYYY");
            const daySupply = orderItemRec.day_supply || rxRec.day_supply || 28;
            const serviceTo = moment()
                .add(daySupply, "days")
                .format("MM/DD/YYYY");
            return ActionResponseWrappers.create(
                {
                    patient_id: rxRec.patient_id,
                    site_id: rxRec.site_id,
                    rx_id: associatedRxId,
                    service_from: serviceFrom,
                    service_to: serviceTo,
                    dispense_sorder: [{ id: orderItemRec.id }],
                },
                "view_create_dt",
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while filling supply for order ID ${orderId}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
