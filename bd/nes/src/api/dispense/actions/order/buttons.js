"use strict";

const {
    OrderActionKeys,
    OrderActions,
    OrderStatus,
    OrderFormat,
    OrderItemBillingMethod,
} = require("@dispense/settings");

const { ButtonStyle, actionButtonWrapper, CallbackType } = require("@actions");

const { ButtonsActionsHandler } = require("../index");
const {
    editActionButtonWrapper,
    listActionButtonWrapper,
} = require("../../../form/actions");

module.exports = class DispenseOrderGetButtonsHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Retrieves available actions for a given order.
     * @param {string} id - The ID of the order.
     * @returns {Promise<Object>} An object containing available actions and any warnings.
     */
    async getActions(id) {
        console.log(`Checking order actions for order ID ${id}`);

        try {
            if (!id) return { actions: [], warning: null };
            const orderRec = await this.fetcher.fetchCachedRecord(
                "careplan_order",
                id
            );
            if (!orderRec)
                throw this.fx.getClaraError(
                    `Unable to locate order with ID ${id}. Please verify order isn't voided or archived.`
                );
            const orderItems = [
                ...(orderRec.subform_order || []),
                ...(orderRec.subform_single_order || []),
                ...(orderRec.subform_single_supply || []),
            ].filter((item) =>
                [OrderStatus.ACTIVE, OrderStatus.PENDING].includes(
                    item.status_id
                )
            );

            const availableActions = [];

            if (
                (orderRec.therapy_id || []).includes("factor") &&
                orderRec.order_format === OrderFormat.THERAPY_SET
            ) {
                const factorOrderActionLabel = OrderActions.FACTOR_ORDER;
                const factorOrderActionKey =
                    OrderActionKeys[factorOrderActionLabel];
                const factorOrderCallbackURL = `/api/form/careplan_order/${id}?perform_action=${factorOrderActionKey}`;

                availableActions.push(
                    editActionButtonWrapper({
                        label: factorOrderActionLabel,
                        action: factorOrderActionKey,
                        callback_url: factorOrderCallbackURL,
                        path: "/order/can/factor_order",
                    })
                );
            }

            let warning = null;
            if (orderItems.length > 0 && orderRec.therapy_set_dc !== "Yes") {
                const callbackUrl = `/api/form/careplan_order/${id}?perform_action=${OrderActionKeys[OrderActions.DISCONTINUE_ORDER]}`;

                //TODO @Patrick changed these to edit action from add actions please verify
                availableActions.push(
                    editActionButtonWrapper({
                        label: OrderActions.DISCONTINUE_ORDER,
                        action: OrderActionKeys[OrderActions.DISCONTINUE_ORDER],
                        callback_url: callbackUrl,
                        callback_type: CallbackType.GET,
                        style: ButtonStyle.CANCEL,
                        path: "/order/can/discontinue",
                    })
                );
            }

            if (
                orderRec.order_format === OrderFormat.SUPPLY_ORDER &&
                orderItems.length > 0 &&
                orderRec.subform_single_supply?.[0]?.associated_rx_id !== null
            ) {
                const supplyOrderItemRec = orderRec.subform_single_supply?.[0];
                const supplyOrderItemPayerMissing =
                    supplyOrderItemRec.billing_method !==
                        OrderItemBillingMethod.DO_NOT_BILL &&
                    !supplyOrderItemRec.insurance_id;
                if (
                    supplyOrderItemRec.associated_rx_id &&
                    !supplyOrderItemPayerMissing
                ) {
                    const callbackUrl = `/api/form/careplan_order/${id}?perform_action=${OrderActionKeys[OrderActions.FILL_SUPPLY]}`;
                    availableActions.push(
                        editActionButtonWrapper({
                            label: OrderActions.FILL_SUPPLY,
                            action: OrderActionKeys[OrderActions.FILL_SUPPLY],
                            callback_url: callbackUrl,
                            callback_type: CallbackType.GET,
                            style: ButtonStyle.ACCEPT,
                            path: "/order/can/fill_supply",
                        })
                    );
                }
            }
            const isNotDiscontinued =
                orderItems.length > 0 && orderRec.therapy_set_dc !== "Yes";
            if (isNotDiscontinued) {
                const activeDispensedRxs = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "careplan_order_rx",
                    {
                        filter: [
                            `order_no:${orderRec.order_no}`,
                            `discontinued:!Yes`,
                            `last_dispense_delivery_tick_id:!null`,
                        ],
                    }
                );
                const hasSomeDeliveries = activeDispensedRxs.length > 0;
                if (!hasSomeDeliveries) {
                    const label = OrderActions.VOID;
                    const action = OrderActionKeys[label];
                    availableActions.push(
                        actionButtonWrapper({
                            label,
                            action,
                            style: ButtonStyle.CANCEL,
                            path: "/order/can/void",
                        })
                    );
                }
            } else {
                warning = "Referral is discontinued";
                return { actions: availableActions, warning: warning };
            }

            return { actions: availableActions, warning: warning };
        } catch (e) {
            const errorMessage = `Error encountered while checking available referral actions for referral ID ${id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
