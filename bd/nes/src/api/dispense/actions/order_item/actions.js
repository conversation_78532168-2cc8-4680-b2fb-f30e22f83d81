"use strict";
const _ = require("lodash");

const { OrderItemActionKeys, OrderItemActions } = require("@dispense/settings");

const { ActionResponseWrappers, DisplayType } = require("@actions");
const { ButtonsActionsHandler } = require("../index");

module.exports = class DispenseOrderItemPerformActionHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.ctx = ctx;
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingGeneratorClass = require("@billing/generator");
        this.generator = new BillingGeneratorClass(this.nes, ctx);
        const NCPDPClaimRunnerClass = require("@billing/pharmacy/runner");
        this.runner = this.fx.getInstance(
            ctx,
            NCPDPClaimRunnerClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Executes a specific action on an delivery ticket.
     * @async
     * @param {string} id - The ID of the delivery ticket.
     * @param {string} action - The action to be performed on the delivery ticket.
     * @param {string} form - The form name.
     * @returns {Promise<Object>} The result of the action execution.
     * @throws {Error} If there's an error during the action execution.
     */
    async runAction(id, action, form) {
        console.log(
            `Processing prescription action ${action} for prescription ID ${id}`
        );

        try {
            const orderItemRec = await this.fetcher.fetchCachedRecord(form, id);
            const orderRec = await this.fetcher.fetchCachedRecord(
                "careplan_order",
                orderItemRec.parent_id
            );
            const orderItemAction = _.invert(OrderItemActionKeys)[action];
            switch (orderItemAction) {
                case OrderItemActions.RUN_TEST_CLAIM:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/order_item/can/run_test_claim"
                        )
                    )
                        return await this.__runTestClaim(
                            form,
                            orderRec,
                            orderItemRec
                        );
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to run test claims."
                        );
                default:
                    return ActionResponseWrappers.error(
                        `Unable to handle prescription action ${action}`
                    );
            }
        } catch (e) {
            const errorMessage = `Error encountered while processing prescription action. Action ${action}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Runs a test claim for an prescription.
     * @async
     * @param {Object} orderItemId - The prescription record ID to run the test claim for.
     * @returns {Promise<Object>} A promise that resolves to the result of handling the test claim group responses.
     * @throws {Error} If an error occurs during the process of running the test claim.
     */
    async __runTestClaim(form, orderRec, orderItemRec) {
        console.debug(
            `Generating test claim for prescription ID ${orderItemRec.id}`
        );
        try {
            const primaryInsuranceId =
                await this.fetcher.fetchPrimaryInsuranceId(
                    orderRec,
                    orderItemRec
                );
            const insuranceRec = primaryInsuranceId
                ? await this.fetcher.fetchCachedRecord(
                      "patient_insurance",
                      primaryInsuranceId
                  )
                : null;
            if (insuranceRec.billing_method_id !== "ncpdp") {
                return ActionResponseWrappers.error(
                    `Insurance ${insuranceRec.payer_id_auto_name} is not a NCPDP insurance. Cannot run test claim.`
                );
            }

            const testClaimDefaults =
                await this.generator.generateOrderItemTestClaimView(
                    orderRec,
                    orderItemRec,
                    insuranceRec
                );
            const callbackUrl = `/api/form/${form}/${orderItemRec.id}/action/gen_test_claim`;
            return ActionResponseWrappers.create(
                {
                    ...testClaimDefaults,
                    order_item_id:
                        form === "careplan_order_item" ? orderItemRec.id : null,
                    orderp_item_id:
                        form === "careplan_orderp_item"
                            ? orderItemRec.id
                            : null,
                },
                "view_billing_claim_test",
                callbackUrl,
                null,
                null,
                null,
                null,
                null,
                DisplayType.MODAL
            );
        } catch (e) {
            const errorMessage = `Error encountered while generating test claim for order ID ${orderItemRec.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
