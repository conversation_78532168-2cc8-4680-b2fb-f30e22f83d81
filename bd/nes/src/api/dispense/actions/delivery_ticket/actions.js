"use strict";
const _ = require("lodash");
const QueryClass = require("../../../query/index");

const {
    DeliveryTicketActions,
    DeliveryTicketActionKeys,
} = require("@dispense/settings");

const {
    ActionResponseWrappers,
    CallbackType,
    NextAction,
} = require("@actions");
const { ButtonsActionsHandler } = require("../index");

module.exports = class DispenseDeliveryTicketPerformActionHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.billingRules = nes.securityRules.billing;
        const BillingFetcherClass = require("@billing/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingHelperClass = require("@billing/helper");
        this.helper = this.fx.getInstance(
            ctx,
            BillingHelperClass,
            true,
            this.nes,
            this.ctx
        );
        this.query = new QueryClass(this.nes);
    }

    /**
     * Executes a specific action on an delivery ticket.
     * @async
     * @param {string} id - The ID of the delivery ticket.
     * @param {string} action - The action to be performed on the delivery ticket.
     * @returns {Promise<Object>} The result of the action execution.
     * @throws {Error} If there's an error during the action execution.
     */
    async runAction(id, action) {
        console.log(
            `Processing delivery ticket action ${action} for delivery ticket ID ${id}`
        );

        try {
            const dtAction = _.invert(DeliveryTicketActionKeys)[action];
            if (!dtAction) {
                throw this.fx.getClaraError(`Invalid action: ${action}`);
            }

            if (!id) {
                return ActionResponseWrappers.error(
                    `Invalid action on new delivery ticket record: ${action}`
                );
            }

            const deliveryTicketRec = await this.fetcher.fetchCachedRecord(
                "careplan_delivery_tick",
                id
            );

            switch (dtAction) {
                case DeliveryTicketActions.ADD_ITEM:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/delivery_ticket/can/add_item"
                        )
                    )
                        return ActionResponseWrappers.runFunction("addDTItem");
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to add items to delivery tickets."
                        );
                case DeliveryTicketActions.VOID:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/delivery_ticket/can/void"
                        )
                    )
                        return await this.__voidDeliveryTicket(
                            deliveryTicketRec
                        );
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to void delivery tickets."
                        );
                case DeliveryTicketActions.CONF_REJECT: ///delivery_ticket/can/reject/confirmed
                case DeliveryTicketActions.VER_REJECT: {
                    const callbackUrl = `/api/form/careplan_delivery_tick/${deliveryTicketRec.id}/action/${DeliveryTicketActionKeys[dtAction]}`;
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/delivery_ticket/can/reject/verified"
                        )
                    )
                        return ActionResponseWrappers.popup(
                            {
                                label: "Reject Ticket",
                                form: "dt_reject",
                                sections: ["Rejection"],
                                preset: {
                                    ticket_no: deliveryTicketRec.ticket_no,
                                    status: deliveryTicketRec.status,
                                },
                                btnLabels: {
                                    cancel: "Close",
                                    save: "Save",
                                },
                            },
                            callbackUrl
                        );
                    else
                        return ActionResponseWrappers.error(
                            "You do not have permission to reject delivery tickets."
                        );
                }
                default:
                    return ActionResponseWrappers.error(
                        `Invalid action: ${action}`
                    );
            }
        } catch (e) {
            const errorMessage = `Error processing DT order action. Action: ${action}, DT ID: ${id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Initiates the process of voiding an invoice.
     * @async
     * @param {Object} deliveryTicketRec - The delivery ticket record to be voided.
     * @returns {Promise<Object>} A promise that resolves to an ActionResponseWrapper containing a popup configuration.
     * @throws {Error} If an error occurs during the voiding process.
     */
    async __voidDeliveryTicket(deliveryTicketRec) {
        console.debug(`Voiding delivery ticket ID ${deliveryTicketRec.id}`);
        try {
            const callbackUrl = `/api/form/careplan_delivery_tick/${deliveryTicketRec.id}/action/void`;
            return ActionResponseWrappers.popup(
                {
                    label: "Void Delivery Ticket",
                    form: "careplan_delivery_tick",
                    sections: ["Void"],
                    preset: {
                        void: "Yes",
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Save",
                    },
                },
                callbackUrl,
                false,
                CallbackType.POST,
                false,
                true,
                NextAction.CLOSE
            );
        } catch (e) {
            const errorMessage = `Error encountered while voiding delivery ticket ID ${deliveryTicketRec.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
