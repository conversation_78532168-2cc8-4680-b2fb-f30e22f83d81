"use strict";
const _ = require("lodash");
const {
    DeliveryTicketActionKeys,
    DeliveryTicketActions,
    DeliveryTicketStatus,
} = require("@billing/settings");

const { ActionResponseWrappers, NextAction } = require("@actions");

const { PostActionsHandler } = require("../index");
const { ErrorMessages } = require("@dispense/errors");

module.exports = class DispenseDeliveryTicketPostActionsHandler extends (
    PostActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.billingRules = nes.securityRules.billing;
        const BillingFetcherClass = require("@billing/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingHelperClass = require("@billing/helper");
        this.helper = this.fx.getInstance(
            ctx,
            BillingHelperClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Performs a post action on an delivery ticket.
     * @async
     * @param {string} id - The ID of the delivery ticket.
     * @param {string} action - The action to be performed on the delivery ticket.
     * @param {Object} params - Additional parameters for the action.
     * @returns {Promise<void>}
     */
    async postAction(id, action, _) {
        console.log(
            `Handling delivery ticket post action ${action} for delivery ticket id ${id}`
        );
        try {
            switch (action) {
                case DeliveryTicketActionKeys[DeliveryTicketActions.VOID]:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/delivery_ticket/can/void"
                        )
                    ) {
                        return await this.__handleVoidView(id);
                    } else {
                        return ActionResponseWrappers.error(
                            "You do not have permission to void delivery tickets."
                        );
                    }
                case DeliveryTicketActionKeys[
                    DeliveryTicketActions.CONF_REJECT
                ]: // path: /delivery_ticket/can/reject/confirmed
                case DeliveryTicketActionKeys[DeliveryTicketActions.VER_REJECT]:
                    if (
                        this.auth.can_access_path(
                            this.ctx,
                            this.ctx.user,
                            "/delivery_ticket/can/reject/verified"
                        )
                    ) {
                        return await this.__handleRejectView(id);
                    } else {
                        return ActionResponseWrappers.error(
                            "You do not have permission to reject delivery tickets."
                        );
                    }
            }

            return ActionResponseWrappers.error(
                `Invalid action name: ${action}`
            );
        } catch (e) {
            const errorMessage = `Error encountered while checking parameters for delivery ticket action.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Handles the void view for an invoice.
     * @async
     * @param {string} deliveryTicketId - The ID of the delivery ticket to handle.
     * @returns {Promise<void>}
     */
    async __handleVoidView(deliveryTicketId) {
        console.debug(
            `Handling void view for delivery ticket id ${deliveryTicketId}`
        );

        try {
            const transaction = this.db.env.rw.transaction(this.ctx);
            const voidData = this.ctx.request.body;
            if (!voidData) {
                return ActionResponseWrappers.error(
                    ErrorMessages.MISSING_VIEW_DATA
                );
            }
            if (voidData.void !== "Yes") {
                return ActionResponseWrappers.success(
                    `Delivery Ticket not voided.`,
                    NextAction.CLOSE
                );
            }
            const deliveryTicketRec = await this.fetcher.fetchCachedRecord(
                "careplan_delivery_tick",
                deliveryTicketId
            );
            if (deliveryTicketRec.void === "Yes") {
                return ActionResponseWrappers.error(
                    `Delivery ticket id ${deliveryTicketId} is already voided, cannot void.`
                );
            }

            const voidRes = await this.helper.voidDeliveryTicket(
                transaction,
                deliveryTicketRec,
                voidData
            );
            if (voidRes?.error)
                return ActionResponseWrappers.error(voidRes.error);
            const res = await transaction.commit();
            if (res.error) return ActionResponseWrappers.error(res.message);
            transaction.init();

            return ActionResponseWrappers.success(
                `Delivery Ticket has been successfully voided.`,
                NextAction.CLOSE
            );
        } catch (e) {
            const errorMessage = `Error encountered while voiding delivery ticket.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Handles the rejection view for a delivery ticket.
     * @async
     * @param {number} deliveryTicketId - The ID of the delivery ticket to be rejected.
     * @returns {Promise<Object>} The result of the rejection operation.
     * @throws {Error} If there is an error during the rejection process.
     */
    async __handleRejectView(deliveryTicketId) {
        console.debug(
            `Handling reject view for delivery ticket id ${deliveryTicketId}`
        );
        try {
            const transaction = this.db.env.rw.transaction(this.ctx);
            const rejectData = this.ctx.request.body;
            if (!rejectData) {
                return ActionResponseWrappers.error(
                    ErrorMessages.MISSING_VIEW_DATA
                );
            }
            await transaction.update(
                "careplan_delivery_tick",
                {
                    status: DeliveryTicketStatus.READY_TO_FILL,
                    verified: null,
                    tech_verified: null,
                    tech_verified_by: null,
                    tech_verified_datetime: null,
                    tech_signature: null,
                    tech_supplies_verified: null,
                    verified_by: null,
                    verified_datetime: null,
                    pharm_signature: null,
                },
                deliveryTicketId
            );
            await transaction.insert("dt_reject", rejectData);
            const res = await transaction.commit();
            if (res.error)
                return ActionResponseWrappers.error(
                    `Unable to save rejection. Please contact support if issues persist.`
                );
            transaction.init();

            return ActionResponseWrappers.success(
                `Delivery Ticket has been rejected.`,
                NextAction.CLOSE
            );
        } catch (e) {
            const errorMessage = `Error encountered while rejecting delivery ticket.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
