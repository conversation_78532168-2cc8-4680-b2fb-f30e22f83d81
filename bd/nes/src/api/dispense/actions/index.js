"use strict";
const { ActionResponseWrappers } = require("@actions");

class ButtonsActionsHandler {
    constructor(nes, ctx) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
    }

    // Return an array of action buttons and possbile warning message (see settings/ButtonSchema)
    async getActions(_id) {
        return { actions: [], warning: null };
    }
}

class RunActionsHandler {
    constructor(nes, ctx) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
    }

    // Perform the action, see ActionResponseWrappers for return types from this action
    async runAction(_id, _action) {
        this.ctx.status = 500;
        this.ctx.body = { error: "Not implemented" };
    }
}

class PostActionsHandler {
    constructor(nes, ctx) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
    }

    // Perform the action, see ActionResponseWrappers for return types from this action
    async postAction(_id, _action, _params) {
        return ActionResponseWrappers.error("Not implemented");
    }
}

module.exports = {
    ButtonsActionsHandler,
    RunActionsHandler,
    PostActionsHandler,
};
