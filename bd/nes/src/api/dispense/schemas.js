"use strict";
const Joi = require("joi").extend(require("@joi/date"));

const patientSchema = Joi.object({
    id: Joi.number().required(),
    status_id: Joi.string().required(),
    firstname: Joi.string().required(),
    lastname: Joi.string().required(),
    dob: Joi.date().format("MM/DD/YYYY").required(),
    site_id: Joi.number().required(),
    gender: Joi.string().valid("Male", "Female").required(),
    email: Joi.string().allow(null).optional(),
});

const deliveryTickItemSchema = Joi.object({
    print: Joi.string().valid("Yes", null).optional(),
    bill: Joi.string().valid("Yes", null).optional(),
    dispense_quantity: Joi.number().required(),
    dispense_unit: Joi.string().required(),
    day_supply: Joi.number().allow(null).optional(),
    is_340b: Joi.string().valid("Yes", null).optional(),
    rental_id: Joi.number().allow(null).optional(),
    ticket_no: Joi.string().allow(null).optional(),
    ticket_item_no: Joi.number().allow(null).optional(),
    part_of_kit: Joi.string().valid("Yes", null).optional(),
    supply_billable: Joi.string().valid("Yes", null).optional(),
    rental_type: Joi.string().valid("Rental", "Purchase").optional(),
    frequency_code: Joi.string().allow(null).optional(),
    last_through_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    report_quantity: Joi.number().allow(null).optional(),
    report_quantity_ea: Joi.number().allow(null).optional(),
    report_unit_id: Joi.string().allow(null).optional(),
    billing_method: Joi.string().allow(null).optional(),
    insurance_id: Joi.number().allow(null).optional(),
    doses_prepared: Joi.number().allow(null).optional(),
    containers_prepared: Joi.number().allow(null).optional(),
    copy_changes_to_rx: Joi.string().valid("Yes", null).optional(),
    update_payer_order: Joi.string().valid("Yes", null).optional(),
    accept_assignment: Joi.string().valid("Yes", null).optional(),
    allow_dispense_quantity_update: Joi.string().valid("Yes", null).optional(),
    is_primary_drug: Joi.string().valid("Yes", null).allow(null).optional(),
    rx_id: Joi.number().required(),
    refill_tracking: Joi.string().valid("Refills", "Doses").required(),
    auto_dc: Joi.string().valid("Yes", null).allow(null).optional(),
    template_type: Joi.string()
        .valid("PO", "IV", "Injection", "Factor", "Compound", "TPN")
        .allow(null)
        .optional(),
    inventory_id: Joi.number().required(),
});

const deliveryTicketSchema = Joi.object({
    status: Joi.string()
        .valid(
            "delivery_ticket",
            "ready_to_fill",
            "order_ver",
            "pending_conf",
            "ready_to_bill",
            "confirmed",
            "billed",
            "voided"
        )
        .required(),
    patient_id: Joi.number().required(),
    careplan_id: Joi.number().required(),
    site_id: Joi.number().required(),
    ticket_no: Joi.string().allow(null).optional(),
    service_from: Joi.date().format("MM/DD/YYYY").required(),
    service_to: Joi.date().format("MM/DD/YYYY").required(),
    confirmed: Joi.string().valid("Yes", null).allow(null).optional(),
    verified: Joi.string().valid("Yes", null).allow(null).optional(),
    tech_verified: Joi.string().valid("Yes", null).allow(null).optional(),
});

const deliveryTicketViewSchema = Joi.object({
    patient_id: Joi.number().required(),
    rx_id: Joi.number().required(),
    site_id: Joi.number().required(),
    service_from: Joi.date().format("MM/DD/YYYY").optional().allow(null),
    service_to: Joi.date().format("MM/DD/YYYY").optional().allow(null),
    wt_tracking_types: Joi.array().optional().allow(null),
    order_item_filter: Joi.array().optional().allow(null),
    dispense_prescriptions: Joi.array().optional().allow(null),
    dispense_sorder: Joi.array().optional().allow(null),
    working_ticket_id: Joi.array().optional().allow(null),
});

const insuranceSchema = Joi.object({
    id: Joi.number().required(),
    patient_id: Joi.number().required(),
    billing_method_id: Joi.string()
        .valid("ncpdp", "mm", "cms1500", "generic")
        .required(),
    pharmacy_relationship_id: Joi.string().when("billing_method_id", {
        is: "ncpdp",
        then: Joi.when("type_id", {
            is: "CMPBM",
            then: Joi.required(),
            otherwise: Joi.allow(null).optional(),
        }),
        otherwise: Joi.allow(null).optional(),
    }),
    type_id: Joi.string().required(),
    cardholder_id: Joi.string().when("billing_method_id", {
        is: Joi.valid("ncpdp", "mm", "cms1500"),
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    patient_id_qualifier: Joi.string().when("billing_method_id", {
        is: "ncpdp",
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
});

const payerSchema = Joi.object({
    id: Joi.number().required(),
    organization: Joi.string().required(),
    type_id: Joi.string().required(),
    billing_method_id: Joi.string()
        .valid("ncpdp", "mm", "cms1500", "generic")
        .required(),
    bin: Joi.string().when("billing_method_id", {
        is: "ncpdp",
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    pcn: Joi.string().when("billing_method_id", {
        is: "ncpdp",
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    never_comp_seg: Joi.string().valid("Yes", null),
    auto_split_noncompound: Joi.string().valid("Yes", null),
    ncpdp_sec_claims_ingred_cost: Joi.string()
        .valid(
            "Send Zeros",
            "Send Primary Claim Amount",
            "Send Co-pay Amount",
            null
        )
        .optional(),
});

const siteSchema = Joi.object({
    id: Joi.number().required(),
    name: Joi.string().required(),
    pic_id: Joi.number().required(),
    ncpdp_id: Joi.string().required(),
    npi: Joi.string().required(),
});

const embedFieldRanked = Joi.alternatives().try(
    Joi.array().items(
        Joi.object({
            id: Joi.number().required(),
            rank: Joi.number().required(),
        })
    ),
    Joi.string().custom((value, helpers) => {
        try {
            const parsed = JSON.parse(value);
            if (!Array.isArray(parsed)) {
                return helpers.error("string.jsonArray");
            }
            return value;
        } catch (e) {
            console.error(e);
            return helpers.error("string.jsonParse");
        }
    }, "must be a valid array")
);

const orderItemSchema = Joi.object({
    rx_no: Joi.string().optional().allow(null),
    billing_method: Joi.string()
        .valid("Insurance", "Self Pay", "Do Not Bill")
        .optional()
        .allow(null),
    payer_ids: embedFieldRanked.allow(null).optional(),
    dx_ids: embedFieldRanked.allow(null).optional(),
    start_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    stop_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    written_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    expiration_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    rx_template_id: Joi.string().required(),
    template_type: Joi.string().required(),
    refill_tracking: Joi.string().valid("Refills", "Doses").required(),
    inventory_id: Joi.number().required(),
    dose: Joi.number().allow(null),
    dose_range_1: Joi.number().allow(null),
    dose_range_2: Joi.number().allow(null),
    dose_unit_id: Joi.string().required(),
    refills: Joi.number().allow(null).optional(),
    frequency_id: Joi.string().allow(null),
    route_id: Joi.string().allow(null),
    daw_code: Joi.string().allow(null).optional(),
    doses_allowed: Joi.allow(null).optional(),
})
    .custom((value, helpers) => {
        if (
            value.dose !== null ||
            (value.dose_range_1 !== null && value.dose_range_2 !== null)
        ) {
            return value;
        }
        return helpers.error("custom.doseValidation");
    }, "Dose validation")
    .required();

const orderSchema = Joi.object({
    site_id: Joi.number().required(),
    order_format: Joi.string()
        .valid("Single Prescription", "Therapy Set")
        .required(),
    prescriber_id: Joi.number().allow(null).optional(),
    written_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    expiration_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    dx_ids: embedFieldRanked.allow(null).optional(),
    start_date: Joi.date().format("MM/DD/YYYY").allow(null).optional(),
    void: Joi.string().valid("Yes", null).optional(),
    subform_order: Joi.array().allow(null).optional(),
    payer_ids: embedFieldRanked.allow(null).optional(),
    billing_method: Joi.string()
        .valid("Insurance", "Self Pay", "Do Not Bill")
        .optional()
        .allow(null),
});

const prescriptionSchema = Joi.object({
    patient_id: Joi.number().required(),
    site_id: Joi.number().required(),
    rx_no: Joi.string().required(),
    inventory_id: Joi.number().required(),
    template_type: Joi.string().required(),
});

const inventoryItemSchema = Joi.object({
    id: Joi.number().required(),
    taxable: Joi.string().valid("Yes", null).optional(),
    type: Joi.string()
        .valid("Drug", "Compound", "Supply", "Equipment Rental", "Billable")
        .required(),
    ndc: Joi.string().when("type", {
        is: Joi.valid("Drug"),
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    billing_unit_id: Joi.string()
        .valid("each", "mL", "gram")
        .when("type", {
            is: Joi.valid("Drug", "Compound"),
            then: Joi.required(),
            otherwise: Joi.allow(null).optional(),
        }),
    default_dosing_unit_id: Joi.string().allow(null).optional(),
    dosing_unit_per_each: Joi.number().when("type", {
        is: Joi.valid("Drug"),
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    metric_unit_each: Joi.number().when("type", {
        is: Joi.valid("Drug"),
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    billable_code_id: Joi.string().when("type", {
        is: Joi.valid("Billable", "Equipment Rental"),
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    quantity_per_package: Joi.number().when("type", {
        is: "Supply",
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    supply_billable: Joi.string().valid("Yes", null).optional(),
    dea_schedule_id: Joi.string().allow(null).optional(),
    sp_pk_indicator: Joi.string().allow(null).optional(),
    storage_id: Joi.string().allow(null).optional(),
    quantity_each: Joi.number().allow(null).optional(),
});

const voidSchema = Joi.object({
    void: Joi.string().valid("Yes", null).optional(),
    void_reason_id: Joi.string().when("void", {
        is: "Yes",
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    voided_datetime: Joi.date()
        .format("MM/DD/YYYY HH:mm:ss")
        .when("void", {
            is: "Yes",
            then: Joi.required(),
            otherwise: Joi.allow(null).optional(),
        }),
}).required();

const diagnosisSchema = Joi.object({
    patient_id: Joi.number().required(),
    dx_id: Joi.string().required(),
    rank: Joi.number().required(),
    active: Joi.string().valid("Yes", null).optional(),
}).required();

const encounterSchema = Joi.object({
    contact_date: Joi.date().required(),
    time_total: Joi.number().required(),
});

const rentalSchema = Joi.object({
    inventory_id: Joi.number().required(),
    rental_type: Joi.string().valid("Rental", "Purchase").required(),
    frequency_code: Joi.string().required(),
});

const prescriberSchema = Joi.object({
    physician_id: Joi.number().required(),
    primary: Joi.string().valid("Yes", null).optional(),
    supervising: Joi.string().valid("Yes", null).optional(),
});

const physicianSchema = Joi.object({
    first: Joi.string().required(),
    last: Joi.string().required(),
    npi: Joi.number().required(),
});

const RecordSchemas = {
    rankedIds: embedFieldRanked,
    patient: patientSchema,
    payer: payerSchema,
    insurance: insuranceSchema,
    site: siteSchema,
    inventory: inventoryItemSchema,
    order: orderSchema,
    orderItem: orderItemSchema,
    rental: rentalSchema,
    deliveryTicket: deliveryTicketSchema,
    deliveryTickItem: deliveryTickItemSchema,
    deliveryTicketView: deliveryTicketViewSchema,
    diagnosis: diagnosisSchema,
    encounter: encounterSchema,
    void: voidSchema,
    prescriber: prescriberSchema,
    physician: physicianSchema,
    prescription: prescriptionSchema,
};

module.exports = {
    RecordSchemas,
};
