"use strict";

const DispenseGeneratorClass = require("./generator");
const DispenseHelperClass = require("./helper");
const DispenseWorkflowClass = require("./workflow");
const DrugAllergyModule = require("@api/interactions");

const { ErrorMessages } = require("./errors");
const { ActionResponseWrappers } = require("@actions");
const _ = require("lodash");
const QueryClass = require("../query/index");
module.exports = class ApiView {
    constructor(nes) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.query = new QueryClass(nes);
    }

    /**
     * Creates delivery ticket view presets for a prescription
     * @async
     * @param {Object} ctx - The context object containing request and response information
     * @param {number} rxId - The prescription ID to create view presets for
     * @returns {Promise<void>} A promise that resolves when the view presets are created
     */
    async __createDTViewPresets(ctx, rxId) {
        console.log(`Creating Delivery Ticket View Presets`);
        try {
            const generator = new DispenseGeneratorClass(this.nes, ctx);
            const resp = await generator.generateDeliveryTicketView(rxId);
            ctx.status = 200;
            ctx.body = resp;
        } catch (e) {
            const errorMessage = `Error encounter while creating delivery ticket view presets`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            this.fx.setReturnErrorMessage(ctx, e, errorMessage);
        }
    }

    /**
     * Generates a delivery ticket supply kit item and associated billable item.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {number} orderId - The ID of the order.
     * @param {number} supplyKitId - The ID of the supply kit.
     * @returns {Promise<void>} - A promise that resolves when the supply kit item is generated.
     */
    async __generateDTItemSupplyKit(ctx, orderId, supplyKitId) {
        console.log(`Generating Delivery Ticket Supply Kit Item`);
        try {
            const generator = new DispenseGeneratorClass(this.nes, ctx);
            const resp = await generator.generateSupplyKitDTRecords(
                orderId,
                supplyKitId
            );
            ctx.status = 200;
            ctx.body = resp;
        } catch (e) {
            const errorMessage = `Error encounter while generating delivery ticket supply kit item`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            this.fx.setReturnErrorMessage(ctx, e, errorMessage);
        }
    }

    async __addNewDTItem(ctx) {
        const data = ctx.request.body;
        try {
            if (!data) {
                throw new Error("Missing request data");
            }

            const generator = new DispenseGeneratorClass(this.nes, ctx);
            const itemsData = await generator.addDeliveryTicketItem(data);

            if (itemsData && itemsData.careplan_dt_item) {
                return await generator.createDeliveryTicketItems(itemsData);
            } else {
                throw new Error("Failed to generate delivery ticket item data");
            }
        } catch (e) {
            console.error("Error in __addNewDTItem:", e);
            this.__returnError(
                ctx,
                e,
                "Error generating delivery ticket items"
            );
        }
    }
    /**
     * Generates a delivery ticket from a delivery ticket view.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {Object} deliveryTicketView - The delivery ticket view to generate the delivery ticket from.
     * @returns {Promise<void>} - A promise that resolves when the delivery ticket is generated.
     */
    async __generateDeliveryTicket(ctx, deliveryTicketView) {
        console.log(`Generating Delivery Ticket`);
        try {
            const generator = new DispenseGeneratorClass(this.nes, ctx);
            const resp =
                await generator.generateDeliveryTicket(deliveryTicketView);
            ctx.status = 200;
            ctx.body = resp;
        } catch (e) {
            const errorMessage = `Error encounter while generating delivery ticket`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            this.fx.setReturnErrorMessage(ctx, e, errorMessage);
        }
    }

    /**
     * Checks the dispense payer settings for a delivery ticket.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {Object} deliveryTicketRec - The delivery ticket record.
     * @returns {Promise<void>} A promise that resolves when the payer settings check is complete.
     */
    async __checkDispensePayerSettings(ctx, ticketNo) {
        console.log(`Checking Payer Settings`);

        try {
            if (!ticketNo) {
                ctx.status = 500;
                ctx.body = { error: ErrorMessages.MISSING_TICKET_NO };
                return;
            }
            const helper = new DispenseHelperClass(this.nes, ctx);
            const settings = await helper.getDispensePayerSettings(ticketNo);

            ctx.status = 200;
            ctx.body = settings;
        } catch (e) {
            this.__returnError(ctx, e, "Error building payer settings");
        }
    }

    /**
     * Retrieves form presets for a given order and form.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {string} orderId - The ID of the order.
     * @param {string} deliveryTicketId - The ID of the delivery ticket.
     * @param {string} rxId - The ID of the rx.
     * @param {string} formName - The name of the form.
     * @returns {Promise<void>} - A promise that resolves when the form presets are retrieved and set in the context.
     */
    async __getFormPresets(ctx, orderId, deliveryTicketId, rxId, formName) {
        console.log(`Generating form presets for order ID: ${orderId}`);
        try {
            if (!(orderId || deliveryTicketId || rxId) || !formName) {
                ctx.status = 500;
                ctx.body = {
                    error: ErrorMessages.MISSING_WF_ID_AND_FORM_NAME,
                };
                return;
            }

            if (!this.shared.DSL[formName]) {
                ctx.status = 500;
                ctx.body = {
                    error: ErrorMessages.INVALID_FORM,
                };
                return;
            }
            const workflow = new DispenseWorkflowClass(this.nes, ctx);
            const presets = await workflow.generateFormPresetsForForm(
                orderId,
                deliveryTicketId,
                rxId,
                formName
            );
            console.log(
                `Form presets for ${formName}: ${JSON.stringify(presets)} orderId: ${orderId} deliveryTicketId: ${deliveryTicketId} rxId: ${rxId}`
            );
            ctx.status = 200;
            ctx.body = presets;
        } catch (e) {
            this.__returnError(
                ctx,
                e,
                "Error building form therapy specific assessment data"
            );
        }
    }

    /**
     * Checks drug interactions for a patient and optional Rx
     * @async
     * @param {Object} ctx - The context object containing request and response information
     * @param {string} patientId - The ID of the patient to check interactions for
     * @param {string|null} [rxId=null] - Optional Rx ID to check interactions against
     * @returns {Promise<void>} - A promise that resolves when interactions are checked and set in context
     * @throws {Error} If there is an error checking interactions
     */
    async __checkInteractions(ctx, patientId, rxId = null) {
        console.log(`Checking Interactions`);
        try {
            const interactionsClass = new DrugAllergyModule(this.nes, ctx);
            const interactions = await interactionsClass.getInteraction(
                patientId,
                rxId
            );
            ctx.status = 200;
            ctx.body = ActionResponseWrappers.create(
                interactions,
                "interactions"
            );
        } catch (e) {
            this.__returnError(ctx, e, "Error checking interactions");
        }
    }
    /**
     * Fetches the assessment data for a delivery ticket.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {string} deliveryTicketId - The ID of the delivery ticket to fetch assessment for.
     * @returns {Promise<void>} - A promise that resolves when the assessment data is fetched and set in the context.
     * @throws {Error} If there is an error fetching the delivery ticket assessment.
     */
    async __fetchDeliveryTicketAssessment(ctx, deliveryTicketId) {
        console.log(`Fetching Delivery Ticket Assessment`);
        try {
            const helper = new DispenseHelperClass(this.nes, ctx);
            const assessment =
                await helper.getDeliveryTicketAssessmentPresets(
                    deliveryTicketId
                );
            ctx.status = 200;
            ctx.body = assessment;
        } catch (e) {
            this.__returnError(
                ctx,
                e,
                "Error fetching delivery ticket assessment"
            );
        }
    }

    /**
     * Adds a dispensed item to the delivery ticket.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {Object} pulledRec - The dispensed item record.
     * @returns {Promise<void>} - A promise that resolves when the dispensed item is added to the delivery ticket.
     */
    async __addDispensedItem(ctx, pulledRec) {
        console.log(`Adding Dispensed Item`);
        try {
            const transaction = this.db.env.rw.transaction(ctx);
            const filters = [
                `inventory_id:${pulledRec.inventory_id}`,
                `ticket_item_no:${pulledRec.ticket_item_no}`,
                `dispensed_unit_id:${pulledRec.dispensed_unit_id}`,
                `void:!Yes`,
                `lot_no:${pulledRec.lot_no}`,
            ];
            const careplanDTItem = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_dt_item",
                    {
                        filter: [
                            `ticket_item_no:${pulledRec.ticket_item_no}`,
                            `inventory_id:${pulledRec.inventory_id}`,
                        ],
                    }
                )
            );
            if (!careplanDTItem) {
                throw new Error("Careplan DT Item not found");
            }
            const rxId = careplanDTItem.rx_id;
            const rxRecord = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_order_rx",
                    {
                        filter: [`id:${rxId}`],
                    }
                )
            );
            if (!rxRecord) {
                ctx.status = 200;
                ctx.body = {
                    error: true,
                    message: "Rx not found",
                };
                return;
            }
            const subform_label = rxRecord.subform_label || null;
            if (subform_label && subform_label.length > 0) {
                const labelForm = rxRecord.label_form;
                await transaction.update(
                    labelForm,
                    {
                        exp_date: pulledRec.expiration_date,
                    },
                    subform_label[0].id
                );
            }
            const pulledRecLot = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_dt_wt_pulled",
                    { filter: filters }
                )
            );
            if (pulledRecLot) {
                await transaction.update(
                    "careplan_dt_wt_pulled",
                    {
                        dispensed_quantity:
                            pulledRecLot.dispensed_quantity +
                            pulledRec.dispensed_quantity,
                    },
                    pulledRecLot.id
                );
            } else {
                await transaction.insert("careplan_dt_wt_pulled", pulledRec);
            }
            transaction.commit();
            ctx.status = 200;
            ctx.body = pulledRec;
        } catch (error) {
            this.__returnError(ctx, error, "Error adding dispensed item");
        }
    }

    /**
     * Scans a dispensed item from the delivery ticket.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {Object} scanDispensedItemData - The scan dispensed item data.
     * @returns {Promise<void>} - A promise that resolves when the dispensed item is scanned.
     */
    async __scanDispensedItem(
        ctx,
        inventory_id,
        ticket_no,
        lot_no,
        serial_no,
        expiration_date
    ) {
        console.log(`Scanning Dispensed Item`);
        try {
            const transaction = this.db.env.rw.transaction(ctx);
            const inventory = _.head(
                await this.form.get.get_form(ctx, ctx.user, "inventory", {
                    filter: [`id:${inventory_id}`],
                })
            );
            if (!inventory) {
                ctx.status = 200;
                ctx.body = {
                    error: true,
                    message: "Inventory not found",
                };
                return;
            }
            const careplanDtItem = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_dt_item",
                    {
                        filter: [
                            `inventory_id:${inventory_id}`,
                            `ticket_no:${ticket_no}`,
                        ],
                    }
                )
            );
            if (!careplanDtItem) {
                ctx.status = 200;
                ctx.body = {
                    error: true,
                    message:
                        "Ticket Item not found associated with scanned product",
                };
                return;
            }

            const rxId = careplanDtItem.rx_id;
            const rxRecord = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_order_rx",
                    {
                        filter: [`id:${rxId}`],
                    }
                )
            );
            if (!rxRecord) {
                ctx.status = 200;
                ctx.body = {
                    error: true,
                    message: "Rx not found",
                };
                return;
            }
            const subform_label = rxRecord.subform_label || null;
            if (subform_label && subform_label.length > 0) {
                const labelForm = rxRecord.label_form;
                await transaction.update(
                    labelForm,
                    {
                        exp_date: expiration_date,
                    },
                    subform_label[0].id
                );
            }
            const pulledObj = {
                inventory_id,
                patient_id: careplanDtItem.patient_id,
                lot_no: lot_no || null,
                serial_no: serial_no || null,
                ticket_no: careplanDtItem.ticket_no,
                ticket_item_no: careplanDtItem.ticket_item_no,
                site_id: careplanDtItem.site_id,
                status: careplanDtItem.status,
                raw_barcode: inventory.raw_barcode,
                lot_tracking: inventory.lot_tracking,
                serial_tracking: inventory.serial_tracking,
                quantity_needed: careplanDtItem.quantity_needed,
                flt_lot: null,
                lot_id: null,
                flt_ser: null,
                serial_id: null,
                dispensed_unit_id: careplanDtItem.dispense_unit,
                expiration_date: expiration_date || null,
                quantity_on_hand: 0,
                dispensed_quantity: 0,
            };

            const careplanDtWtPulled = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_dt_wt_pulled",
                {
                    filter: [
                        `ticket_item_no:${careplanDtItem.ticket_item_no || "-1"}`,
                        `dispensed_unit_id:${careplanDtItem.dispense_unit || "-1"}`,
                        "void:!Yes",
                    ],
                }
            );

            if (lot_no) {
                const lot = _.head(
                    await this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "inventory_lot",
                        {
                            filter: [
                                `inventory_id:${inventory_id}`,
                                `lot_no:${lot_no}`,
                            ],
                        }
                    )
                );
                if (!lot) {
                    ctx.status = 200;
                    ctx.body = {
                        error: true,
                        message: "Scanned lot does not exist",
                    };
                    return;
                }
                pulledObj.lot_id = lot.id;
                const quantityOnHand = _.head(
                    await this.query.run_query(
                        ctx,
                        ctx.user,
                        "work_ticket_inventory_on_hand",
                        {
                            x1: `${inventory_id}`,
                            x2: `${careplanDtItem.site_id}`,
                            x3: `${lot.id}`,
                            x4: "0",
                        }
                    )
                );
                if (
                    quantityOnHand.quantity_on_hand <
                    careplanDtItem.quantity_needed
                ) {
                    ctx.status = 200;
                    ctx.body = {
                        error: true,
                        message:
                            "Scanned lot does not have enough quantity on hand",
                    };
                    return;
                }
                if (careplanDtWtPulled.length > 0) {
                    const totalPulled = careplanDtWtPulled.reduce(
                        (acc, item) => acc + item.dispensed_quantity,
                        0
                    );
                    if (
                        parseFloat(totalPulled) ==
                        parseFloat(careplanDtItem.dispense_quantity)
                    ) {
                        ctx.status = 200;
                        ctx.body = {
                            error: true,
                            message: "The scanned item is already pulled",
                        };
                        return;
                    }
                    const totalNeeded = careplanDtItem.dispense_quantity;
                    const quantityToPull = Math.max(
                        totalNeeded - totalPulled,
                        0
                    );
                    pulledObj.quantity_needed = totalNeeded;
                    pulledObj.dispensed_quantity = quantityToPull;
                    pulledObj.id = careplanDtWtPulled[0].id;
                } else {
                    pulledObj.quantity_needed = careplanDtItem.quantity_needed;
                    pulledObj.dispensed_quantity =
                        careplanDtItem.quantity_needed;
                }
            }

            if (serial_no) {
                const serial = _.head(
                    await this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "inventory_serial",
                        {
                            filter: [
                                `inventory_id:${inventory_id}`,
                                `serial_no:${serial_no}`,
                            ],
                        }
                    )
                );
                if (!serial) {
                    ctx.status = 200;
                    ctx.body = {
                        error: true,
                        message: "Scanned serial does not exist",
                    };
                    return;
                }
                pulledObj.serial_id = serial.id;
                pulledObj.dispensed_quantity = 1;
                pulledObj.quantity_needed = careplanDtItem.quantity_needed;
            }
            await transaction.commit();
            ctx.body = pulledObj;
            ctx.status = 200;
        } catch (error) {
            this.__returnError(ctx, error, "Error scanning dispensed item");
        }
    }

    /**
     * Checks the pulled items verified data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the careplanDTRecord, or false if validation fails.
     */
    async __checkPulledItemsVerified(ctx, careplanDTRecord) {
        console.log(`Checking Pulled Items Verified`);

        try {
            const ticket_no = careplanDTRecord.ticket_no;
            if (!ticket_no) {
                ctx.status = 200;
                ctx.body = {
                    error: "Missing ticket_no",
                };
                return false;
            }
            const careplanDtItem = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_dt_item",
                { filter: [`ticket_no:${ticket_no}`] }
            );
            if (!careplanDtItem || careplanDtItem.length === 0) {
                ctx.status = 200;
                ctx.body = {
                    error: "Careplan DT Item not found",
                };
                return false;
            }
            const promises = [];
            for (const item of careplanDtItem) {
                promises.push(
                    this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "careplan_dt_wt_pulled",
                        {
                            filter: [
                                `ticket_item_no:${item.ticket_item_no}`,
                                `dispensed_unit_id:${item.dispense_unit}`,
                                `void:!Yes`,
                            ],
                        }
                    )
                );
            }
            const results = await Promise.all(promises);
            const careplanDtWtPulled = results.flat();
            if (careplanDtWtPulled.length === 0) {
                ctx.status = 200;
                ctx.body = {
                    error: "Careplan DT WT Pulled not found",
                };
                return false;
            }
            const careplanDtWtPulledItem = careplanDtWtPulled.find(
                (item) => item.item_verified !== "Yes"
            );
            if (careplanDtWtPulledItem) {
                ctx.status = 200;
                ctx.body = {
                    error: "Pulled item(s) not verified please verify them and then try again",
                    popup: true,
                };
                return false;
            }
            ctx.status = 200;
            ctx.body = {
                message: "All items verified",
                success: true,
            };
        } catch (error) {
            this.__returnError(
                ctx,
                error,
                "Error checking pulled items verified"
            );
        }
    }

    /**
     * Checks the scanner verification data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the scannerVerificationData, or false if validation fails.
     */
    async __scannerVerification(
        ctx,
        inventory_id,
        ticket_no,
        __lot_no,
        __serial_no
    ) {
        console.log(`Scanner Verification`);
        try {
            const inventory = _.head(
                await this.form.get.get_form(ctx, ctx.user, "inventory", {
                    filter: [`id:${inventory_id}`],
                })
            );
            if (!inventory) {
                ctx.status = 200;
                ctx.body = {
                    error: "Inventory not found",
                };
                return;
            }
            const careplanDtItem = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_dt_item",
                {
                    filter: [
                        `ticket_no:${ticket_no}`,
                        `inventory_id:${inventory.id}`,
                    ],
                }
            );
            if (!careplanDtItem || careplanDtItem.length === 0) {
                ctx.status = 200;
                ctx.body = {
                    error: "Careplan DT Item not found",
                };
                return;
            }
            const careplanDtWtPulled = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_dt_wt_pulled",
                {
                    filter: [
                        `ticket_item_no:${careplanDtItem[0].ticket_item_no}`,
                        `dispensed_unit_id:${careplanDtItem[0].dispense_unit}`,
                        `void:!Yes`,
                    ],
                }
            );
            if (careplanDtWtPulled.length === 0) {
                ctx.status = 200;
                ctx.body = {
                    error: "Careplan DT WT Pulled not found",
                };
                return;
            }
            const pulledItem = careplanDtWtPulled.find(
                (item) => item.item_verified !== "Yes"
            );
            if (pulledItem) {
                ctx.status = 200;
                ctx.body = { pulled_items: pulledItem, success: true };
                return;
            }
            ctx.status = 200;
            ctx.body = {
                message: "Scanner verification successful all items verified",
                items_verified: true,
            };
        } catch (error) {
            this.__returnError(ctx, error, "Error scanner verification");
        }
    }

    /**
     * Checks the delivery ticket data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the deliveryTicketRec, or false if validation fails.
     */
    async __checkDeliveryTicketCreateViewData(ctx) {
        console.log(`Checking Delivery Ticket Create View Data`);

        const deliveryTicketView = ctx.request.body;
        if (!deliveryTicketView) {
            ctx.status = 500;
            ctx.body = {
                error: ErrorMessages.MISSING_DELIVERY_TICKET_VIEW_DATA,
            };
            return false;
        }

        return { deliveryTicketView };
    }

    /**
     * Checks the delivery ticket data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the deliveryTicketRec, or false if validation fails.
     */
    async __checkAddDispensedItemData(ctx) {
        console.log(`Checking Add Dispensed Item Data`);

        const addDispensedItemData = ctx.request.body;
        if (!addDispensedItemData) {
            ctx.status = 500;
            ctx.body = {
                error: "Missing add dispensed item data",
            };
            return false;
        }

        return { addDispensedItemData };
    }

    /**
     * Checks the scan dispensed item data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the scanDispensedItemData, or false if validation fails.
     */
    async __checkScanDispensedItemData(ctx) {
        console.log(`Checking Scan Dispensed Item Data`);

        const scanDispensedItemData = ctx.request.body;
        const { inventory_id, ticket_no, lot_no, serial_no, expiration_date } =
            scanDispensedItemData;
        if (!inventory_id) {
            ctx.status = 500;
            ctx.body = {
                error: "Missing inventory_id",
            };
            return false;
        }

        return { inventory_id, ticket_no, lot_no, serial_no, expiration_date };
    }

    /**
     * Checks the pulled items verified data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the careplanDTRecord, or false if validation fails.
     */
    async __checkPulledItemsVerifiedData(ctx) {
        console.log(`Checking Pulled Items Verified Data`);

        const careplanDTRecord = ctx.request.body;
        if (!careplanDTRecord) {
            ctx.status = 500;
            ctx.body = {
                error: "Missing careplanDTRecord",
            };
            return false;
        }

        return { careplanDTRecord };
    }
    async __scannerVerificationData(ctx) {
        console.log(`Checking Scanner Verification Data`);

        const scannerVerificationData = ctx.request.body;
        const { inventory_id, ticket_no, lot_no, serial_no } =
            scannerVerificationData;
        if (!inventory_id || !ticket_no) {
            ctx.status = 500;
            ctx.body = {
                error: "Missing inventory_id or ticket_no",
            };
            return false;
        }

        return { inventory_id, ticket_no, lot_no, serial_no };
    }

    /**
     * Handles and logs errors, then sets the error message in the context.
     * @param {Object} ctx - The context object containing request and response information.
     * @param {Error} e - The error object.
     * @param {string} humanReadableMessage - A human-readable error message.
     * @private
     */
    __returnError(ctx, e, humanReadableMessage) {
        const errorMessage = humanReadableMessage;
        console.error(
            `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
        );
        const humanMessage = e?.humanMessage || errorMessage;
        this.fx.setReturnErrorMessage(ctx, e, humanMessage);
    }

    async process(ctx, urlpath) {
        if (urlpath.path.length < 2 || urlpath.path[1] == "") {
            ctx.status = 500;
            ctx.body = {
                error: "Invalid form path specified: " + urlpath.url.path,
            };
            return;
        }

        if (this.fx.checkParameters({ func: "Function ID" }, ctx) == false)
            return;

        const params = Object.assign({}, ctx.query);
        const func = params.func;
        switch (func) {
            case "check_dispense_payer_settings":
                return await this.__checkDispensePayerSettings(
                    ctx,
                    params.ticket_no
                );
            case "get_form_presets":
                return await this.__getFormPresets(
                    ctx,
                    params?.order_id || null,
                    params?.delivery_ticket_id || null,
                    params?.rx_id || null,
                    params?.form_name
                );
            case "add_new_dt_item":
                return await this.__addNewDTItem(ctx);
            case "create_dt_view_presets":
                return await this.__createDTViewPresets(ctx, params?.rx_id);
            case "fetch_delivery_ticket_assessment":
                return await this.__fetchDeliveryTicketAssessment(
                    ctx,
                    params?.delivery_ticket_id
                );
            case "check_interactions":
                return await this.__checkInteractions(
                    ctx,
                    params?.patient_id,
                    params?.rx_id
                );
            case "generate_delivery_ticket":
                await this.__checkDeliveryTicketCreateViewData(ctx)
                    .then(async ({ deliveryTicketView }) => {
                        await this.__generateDeliveryTicket(
                            ctx,
                            deliveryTicketView
                        );
                    })
                    .catch((e) => {
                        this.__returnError(
                            ctx,
                            e,
                            "Error encounter checking delivery ticket create view data"
                        );
                    });
                break;
            case "add_dispensed_item":
                await this.__checkAddDispensedItemData(ctx)
                    .then(async ({ addDispensedItemData }) => {
                        await this.__addDispensedItem(
                            ctx,
                            addDispensedItemData
                        );
                    })
                    .catch((e) => {
                        this.__returnError(
                            ctx,
                            e,
                            "Error encounter checking add dispensed item data"
                        );
                    });
                break;
            case "scan_dispensed_item":
                await this.__checkScanDispensedItemData(ctx)
                    .then(
                        async ({
                            inventory_id,
                            ticket_no,
                            lot_no,
                            serial_no,
                            expiration_date,
                        }) => {
                            await this.__scanDispensedItem(
                                ctx,
                                inventory_id,
                                ticket_no,
                                lot_no,
                                serial_no,
                                expiration_date
                            );
                        }
                    )
                    .catch((e) => {
                        this.__returnError(
                            ctx,
                            e,
                            "Error encounter checking scan dispensed item data"
                        );
                    });
                break;
            case "check_pulled_items_verified":
                await this.__checkPulledItemsVerifiedData(ctx)
                    .then(async ({ careplanDTRecord }) => {
                        await this.__checkPulledItemsVerified(
                            ctx,
                            careplanDTRecord
                        );
                    })
                    .catch((e) => {
                        this.__returnError(
                            ctx,
                            e,
                            "Error encounter checking pulled items verified"
                        );
                    });
                break;
            case "scanner_verification":
                await this.__scannerVerificationData(ctx)
                    .then(
                        async ({
                            inventory_id,
                            ticket_no,
                            lot_no,
                            serial_no,
                        }) => {
                            await this.__scannerVerification(
                                ctx,
                                inventory_id,
                                ticket_no,
                                lot_no,
                                serial_no
                            );
                        }
                    )
                    .catch((e) => {
                        this.__returnError(
                            ctx,
                            e,
                            "Error encounter checking scanner verification"
                        );
                    });
                break;
            default:
                ctx.status = 500;
                ctx.body = { error: "Invalid function name: " + func };
                return;
        }
    }
};
