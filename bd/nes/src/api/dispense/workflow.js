/*jshint : 6 */
"use strict";

/**
 * @class
 * @classdesc Class responsible for various dispense workflow functions.
 */
module.exports = class DispenseWorkflowClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
        const DispenseFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Generates form presets for a specific form based on the order record.
     * @async
     * @param {string} orderId - The ID of the order.
     * @param {string} deliveryTicketId - The ID of the delivery ticket.
     * @param {string} rxId - The ID of the rx.
     * @param {string} formName - The name of the form to generate presets for.
     * @returns {Promise<Object>} A promise that resolves to an object containing the generated form presets.
     * @throws {Error} If there's an error while generating the form presets.
     */
    async generateFormPresetsForForm(
        orderId,
        deliveryTicketId,
        rxId,
        formName
    ) {
        console.log(`Generating form presets for form: ${formName}`);
        try {
            const hasDisease =
                this.shared.DSL[formName].fields["disease_1"] !== null;
            const hasBrand =
                this.shared.DSL[formName].fields["brand_1"] !== null;
            const hasTherapy =
                this.shared.DSL[formName].fields["therapy_1"] !== null;
            const hasRoute =
                this.shared.DSL[formName].fields["route_id"] !== null;
            const hasClinicalAssessment =
                this.shared.DSL[formName].fields["clinical_1"] !== null;

            const sourceId = orderId || deliveryTicketId || rxId;
            const sourceForm = orderId
                ? "careplan_order"
                : deliveryTicketId
                  ? "careplan_delivery_tick"
                  : "careplan_order_rx";
            const sql = `
            SELECT generate_assessment_presets(
                '${sourceId}'::integer, 
                '${sourceForm}', 
                ${hasBrand ? "TRUE" : "FALSE"}, 
                ${hasRoute ? "TRUE" : "FALSE"}, 
                ${hasDisease ? "TRUE" : "FALSE"}, 
                ${hasTherapy ? "TRUE" : "FALSE"}, 
                ${hasClinicalAssessment ? "TRUE" : "FALSE"}
            ) AS result
            `;

            console.log(
                `Source ID: ${sourceId}, Source Form: ${sourceForm}, hasBrand: ${hasBrand}, hasRoute: ${hasRoute}, hasDisease: ${hasDisease}, hasTherapy: ${hasTherapy}, hasClinicalAssessment: ${hasClinicalAssessment}`
            );
            const rows = await this.db.env.rw.query(sql);
            const returnVal = rows[0].result;

            return returnVal;
        } catch (e) {
            const errorMessage = `Error generating form presets for form: ${formName}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
