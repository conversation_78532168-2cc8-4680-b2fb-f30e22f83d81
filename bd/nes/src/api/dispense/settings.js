"use strict";
const {
    InventoryTypes,
    PricingSource,
    InsuranceTypes,
    RentalBillableType,
} = require("@inventory/settings");

const { PatientStatus } = require("@patient/settings");

const RefillTrackingType = {
    DOSES: "Doses",
    REFILLS: "Refills",
};

const FactorDosingPrescriptionType = {
    RANGE: "Range",
    VARIANCE: "Variance",
};

const DeliveryTicketStatus = {
    CREATE: "delivery_ticket",
    READY_TO_FILL: "ready_to_fill",
    WT_VERIFICATION: "order_ver",
    DT_CONFIRMATION: "pending_conf",
    READY_TO_BILL: "ready_to_bill",
    CONFIRMED: "confirmed",
    BILLED: "billed",
    VOIDED: "voided",
};

const DeliveryTicketActions = {
    CONF_REJECT: "Reject",
    VER_REJECT: "Reject",
    INVOICES: "Assoc. Invoices",
    VOID: "Void",
    ADD_SUPPLY_KIT: "Add Kit",
    ADD_PRESCRIPTION: "Add Rx",
    ADD_ITEM: "Add Item",
    ADD_REPLACEMENT: "Add Replacement",
    READY_TO_FILL: "Ready To Fill",
};

const DeliveryTicketActionKeys = {
    [DeliveryTicketActions.CONF_REJECT]: "confirm_reject",
    [DeliveryTicketActions.VER_REJECT]: "verify_reject",
    [DeliveryTicketActions.INVOICES]: "assoc_invoices",
    [DeliveryTicketActions.VOID]: "void",
    [DeliveryTicketActions.ADD_SUPPLY_KIT]: "add_kit",
    [DeliveryTicketActions.ADD_PRESCRIPTION]: "add_rx",
    [DeliveryTicketActions.ADD_ITEM]: "add_item",
    [DeliveryTicketActions.ADD_REPLACEMENT]: "add_replacement",
    [DeliveryTicketActions.READY_TO_FILL]: "ready_to_fill",
};

const DeliveryTicketAddPrescriptionButtonStatus = [
    DeliveryTicketStatus.CREATE,
    DeliveryTicketStatus.READY_TO_FILL,
    DeliveryTicketStatus.WT_VERIFICATION,
];

const DeliveryTicketAddSuppliesButtonStatus = [
    DeliveryTicketStatus.CREATE,
    DeliveryTicketStatus.DT_CONFIRMATION,
    DeliveryTicketStatus.READY_TO_FILL,
    DeliveryTicketStatus.WT_VERIFICATION,
];

const DeliveryTicketCanVoidStatus = [
    DeliveryTicketStatus.CREATE,
    DeliveryTicketStatus.READY_TO_FILL,
    DeliveryTicketStatus.WT_VERIFICATION,
    DeliveryTicketStatus.DT_CONFIRMATION,
    DeliveryTicketStatus.READY_TO_BILL,
    DeliveryTicketStatus.CONFIRMED,
];

const DeliveryTicketBeforeConfirm = [
    DeliveryTicketStatus.CREATE,
    DeliveryTicketStatus.READY_TO_FILL,
    DeliveryTicketStatus.WT_VERIFICATION,
];

const OrderBVActions = {
    ADD_PAYER: "Add Payer",
    ADD_INSURANCE: "Add Insurance",
    INTERVENTIONS: "Interventions",
};

const OrderBVActionKeys = {
    [OrderBVActions.ADD_PAYER]: "add_payer",
    [OrderBVActions.ADD_INSURANCE]: "add_insurance",
    [OrderBVActions.INTERVENTIONS]: "interventions",
};

const PatientAssistanceActions = {
    ADD_PAYER: "Add Payer",
    ADD_INSURANCE: "Add Insurance",
    INTERVENTIONS: "Interventions",
};

const PatientAssistanceActionKeys = {
    [PatientAssistanceActions.ADD_PAYER]: "add_payer",
    [PatientAssistanceActions.ADD_INSURANCE]: "add_insurance",
    [PatientAssistanceActions.INTERVENTIONS]: "interventions",
};

const PrescriptionStatus = {
    SETUP: "Setup",
    COMPLETE: "Complete",
    VERIFIED: "Verified",
    DENIED: "Denied",
};

const OrderActions = {
    DISCONTINUE_ORDER: "D/C",
    FILL_SUPPLY: "Fill Supply",
    RUN_TEST_CLAIM: "Run Test Claim",
    FACTOR_ORDER: "Factor Order",
    CLAIM: "View Claims",
    VOID: "Void",
};

const OrderActionKeys = {
    [OrderActions.DISCONTINUE_ORDER]: "discontinue_order",
    [OrderActions.FILL_SUPPLY]: "fill_supply",
    [OrderActions.RUN_TEST_CLAIM]: "run_test_claim",
    [OrderActions.CLAIM]: "claim",
    [OrderActions.VOID]: "void",
    [OrderActions.FACTOR_ORDER]: "factor_order",
};

const OrderItemActions = {
    TEST_CLAIM: "Test Claims",
    RUN_TEST_CLAIM: "Run Test Claim",
    ATTACHMENT: "View Attachments",
};

const OrderRxActions = {
    VOID: "Void",
    RUN_TEST_CLAIM: "Run Test Claim",
    VIEW_TICKETS: "View Tickets",
    VIEW_INVOICES: "View Claims",
    FILL: "Fill Rx",
    DISCONTINUE_RX: "D/C",
    VIEW_PRICING: "View Pricing",
    CHANGE_TEMPLATE: "Change Template",
};

const OrderRxActionStages = {
    CREATE_TICKET: "Fill Rx",
    OPEN_TICKET: "Open Ticket",
    TEST_CLAIM: "Run Test Claim",
    GENERATE_INVOICE: "Start Fill",
    VIEW_INVOICE: "View Claim",
};

const OrderRxActionProgressLabels = {
    [OrderRxActionStages.CREATE_TICKET]: "Running interaction checks...",
    [OrderRxActionStages.OPEN_TICKET]: "Opening Delivery Ticket...",
    [OrderRxActionStages.TEST_CLAIM]: "Running Test Claim...",
    [OrderRxActionStages.GENERATE_INVOICE]: "Generating Claim...",
    [OrderRxActionStages.VIEW_INVOICE]: "Opening Claim...",
    [OrderRxActionStages.DISCONTINUE_RX]: "Discontinuing Prescription...",
};

const OrderRxActionKeys = {
    [OrderRxActions.RUN_TEST_CLAIM]: "gen_test_claim",
    [OrderRxActions.VIEW_TICKETS]: "view_tickets",
    [OrderRxActions.VIEW_INVOICES]: "view_invoices",
    [OrderRxActions.FILL]: "fill_rx",
    [OrderRxActions.DISCONTINUE_RX]: "discontinue_rx",
    [OrderRxActions.VOID]: "void",
    [OrderRxActions.VIEW_PRICING]: "view_pricing",
    [OrderRxActions.CHANGE_TEMPLATE]: "change_template",
};

const OrderItemActionKeys = {
    [OrderRxActions.TEST_CLAIM]: "test_claims",
    [OrderItemActions.RUN_TEST_CLAIM]: "gen_test_claim",
    [OrderItemActions.ATTACHMENT]: "attachment",
};

const DtExceptionActions = {
    VIEW_INVOICE: "View Related Invoice",
};

const DtExceptionActionKeys = {
    [DtExceptionActions.VIEW_INVOICE]: "view_invoice",
};

const EncounterStatus = {
    PENDING: "Pending",
    READY_FOR_REVIEW: "Ready for Review",
    APPROVED: "Approved",
    DENIED: "Denied",
};

const OrderStatus = {
    ACTIVE: "5",
    ON_HOLD: "4",
    NO_GO: "3",
    DISCONTINUED: "2",
    PENDING: "1",
};

const OrderFormat = {
    SINGLE: "Single Prescription",
    THERAPY_SET: "Therapy Set",
    SUPPLY_ORDER: "Supply Order",
};

const IntakeStatus = {
    REFERRAL_RECEIVED: "A",
    MISSING_PRESCRIPTION: "B",
    NEEDS_AUTH_CORRECTION: "C",
    MISSING_CLINICAL: "D",
    REAUTH_REQUIRED: "E",
    COPAY_REQUIRED: "F",
    INTAKE_COMPLETED: "G",
    THERAPY_STARTED: "H",
    PATIENT_DISCHARGED: "I",
    PATIENT_DECEASED: "J",
    DISCONTINUED_DR: "K",
    FORCED_TO_COMPETITOR: "L",
    MED_NEC_DENIED: "M",
    PT_REFUSED: "N",
    COPAY_TOO_HIGH: "O",
    LIMITED_DIST_DRUG: "P",
    USE_OTHER_SPECIALTY: "Q",
    NO_INSURANCE: "R",
    NO_OON: "S",
    OUTPATIENT_FACILITY: "T",
    PT_WANTS_INN: "U",
};

const PatientMedicationStatus = {
    PENDING: "Pending",
    ACTIVE: "Active",
    DISCONTINUED: "Discontinued",
};

const PriorAuthStatus = {
    PENDING: "1",
    SUBMITTED_FAX: "2",
    SUBMITTED_ELECTRONIC: "3",
    RECEIVED: "4",
    APPROVED: "5",
    DENIED: "6",
    DISCONTINUED: "7",
};

const DMERentalTypeCode = {
    RENTAL: "Rental",
    PURCHASE: "Purchase",
};

const OrderTemplateType = {
    PO: "PO",
    IV: "IV",
    TPN: "TPN",
    INJECTION: "Injection",
    COMPOUND: "Compound",
    FACTOR: "Factor",
};

const LabelFormat = {
    PO: "PO",
    IV: "IV",
    TPN: "TPN",
    INJECTION: "Injection",
};

const LabelForm = {
    [LabelFormat.PO]: "careplan_order_lbl_po",
    [LabelFormat.IV]: "careplan_order_lbl_iv",
    [LabelFormat.TPN]: "careplan_order_lbl_tpn",
    [LabelFormat.INJECTION]: "careplan_order_lbl_inj",
};

const OrderTemplateLabelFormMap = {
    [OrderTemplateType.PO]: LabelForm.PO,
    [OrderTemplateType.IV]: LabelForm.IV,
    [OrderTemplateType.TPN]: LabelForm.TPN,
    [OrderTemplateType.INJECTION]: LabelForm.INJECTION,
    [OrderTemplateType.COMPOUND]: LabelForm.IV,
    [OrderTemplateType.FACTOR]: LabelForm.PO,
};

const OrderTemplateLabelFormatMap = {
    [OrderTemplateType.PO]: LabelFormat.PO,
    [OrderTemplateType.IV]: LabelFormat.IV,
    [OrderTemplateType.TPN]: LabelFormat.TPN,
    [OrderTemplateType.INJECTION]: LabelFormat.INJECTION,
    [OrderTemplateType.COMPOUND]: LabelFormat.IV,
    [OrderTemplateType.FACTOR]: LabelFormat.PO,
};

const RentalFrequency = {
    WEEKLY: "1",
    MONTHLY: "4",
    DAILY: "6",
};

const LabOrderFrequency = {
    ONCE: "Once",
    MONTHLY: "Monthly",
    QUARTERLY: "Quarterly",
    ANNUALLY: "Annually",
    EVERY_VISIT: "Every Visit",
    OTHER: "Other",
};

const PricingExpRentalFrequencyMap = {
    [RentalFrequency.WEEKLY]: "weekly_rental_price_expected",
    [RentalFrequency.MONTHLY]: "monthly_rental_price_expected",
    [RentalFrequency.DAILY]: "daily_rental_price_expected",
};

const PricingListRentalFrequencyMap = {
    [RentalFrequency.WEEKLY]: "weekly_rental_list_price",
    [RentalFrequency.MONTHLY]: "monthly_rental_list_price",
    [RentalFrequency.DAILY]: "daily_rental_list_price",
};

const PricingCostRentalFrequencyMap = {
    [RentalFrequency.WEEKLY]: "weekly_rental_cost",
    [RentalFrequency.MONTHLY]: "monthly_rental_cost",
    [RentalFrequency.DAILY]: "daily_rental_cost",
};

const IVRoutes = ["IV", "IVP", "SQ"];
const LabelSize = {
    LARGE: "Large",
    STANDARD: "Standard",
};

const OrderItemTypes = {
    PRIMARY: "Primary",
    FLUSH: "Flush",
    HYDRATION: "Hydration",
    ANAPHYLAXIS: "Anaphylaxis",
    PREMEDICATION: "Premedication",
    POSTMEDICATION: "Postmedication",
};

const MultiIngredientTypes = [
    OrderTemplateType.COMPOUND,
    OrderTemplateType.RECONSTITUTED,
];

const DEFAULT_REFILLS_DAYS_OUT = 7;
const DEFAULT_SHIPPING_DAYS_OUT = 2;
const DEFAULT_WASTAGE_THRESHOLD = 0.5;
const DEFAULT_MAX_INV_CNT = 999999;
const LABEL_MAX_LENGTH = 38;
const MAX_LOT_STRING = 156;
const PLACEHOLDER_RECORD_ID = -99;
const RENTAL_QUANTITY_EACH = 1;
const DELIVERY_TICKET_PLACEHOLDER_NO = "CREATE";

const PriorAuthType = {
    DRUG: "Drug",
    NURSING: "Nursing",
    DME: "DME",
    SUPPLIES: "Supplies",
};

const OrderItemBillingMethod = {
    INSURANCE: "Insurance",
    SELF_PAY: "Self Pay",
    DO_NOT_BILL: "Do Not Bill",
};

const RxStatus = {
    ORDER_ENTRY_SETUP: "Order Entry/Setup",
    CLAIMS_TO_ADJUDICATE: "Claims to Adjudicate",
    RX_VERIFICATION: "Rx Verification",
    READY_TO_CONTACT: "Ready to Contact",
    READY_TO_REFILL: "Ready to Refill",
    TEST_CLAIM: "Test Claim",
    PRINT_LABELS_FILL_RX: "Print Labels / Fill Rx",
    ORDER_VERIFICATION: "Order Verification",
    DELIVERY_TICKET_CONFIRMATION: "Delivery Ticket Confirmation",
    CONFIRMED: "Confirmed",
    VOID: "Void",
};

const RxReadyToFill = [
    RxStatus.READY_TO_CONTACT,
    RxStatus.PRINT_LABELS_FILL_RX,
    RxStatus.ORDER_VERIFICATION,
    RxStatus.DELIVERY_TICKET_CONFIRMATION,
];

module.exports = {
    OrderTemplateType,
    MultiIngredientTypes,
    DeliveryTicketStatus,
    OrderActions,
    OrderItemActions,
    OrderItemActionKeys,
    OrderBVActions,
    OrderBVActionKeys,
    DeliveryTicketActionKeys,
    DeliveryTicketActions,
    DeliveryTicketAddPrescriptionButtonStatus,
    DeliveryTicketAddSuppliesButtonStatus,
    DeliveryTicketCanVoidStatus,
    PatientAssistanceActions,
    PatientAssistanceActionKeys,
    DeliveryTicketBeforeConfirm,
    DELIVERY_TICKET_PLACEHOLDER_NO,
    OrderStatus,
    IntakeStatus,
    PatientStatus,
    InventoryTypes,
    OrderActionKeys,
    OrderItemTypes,
    OrderRxActions,
    OrderRxActionKeys,
    OrderRxActionStages,
    OrderRxActionProgressLabels,
    OrderFormat,
    DEFAULT_REFILLS_DAYS_OUT,
    DEFAULT_SHIPPING_DAYS_OUT,
    DEFAULT_WASTAGE_THRESHOLD,
    DEFAULT_MAX_INV_CNT,
    PLACEHOLDER_RECORD_ID,
    LABEL_MAX_LENGTH,
    RENTAL_QUANTITY_EACH,
    MAX_LOT_STRING,
    DMERentalTypeCode,
    RentalFrequency,
    RentalBillableType,
    PriorAuthType,
    PricingExpRentalFrequencyMap,
    PricingCostRentalFrequencyMap,
    PricingListRentalFrequencyMap,
    RefillTrackingType,
    IVRoutes,
    LabelSize,
    FactorDosingPrescriptionType,
    OrderItemBillingMethod,
    PricingSource,
    InsuranceTypes,
    PriorAuthStatus,
    PatientMedicationStatus,
    LabOrderFrequency,
    EncounterStatus,
    PrescriptionStatus,
    LabelFormat,
    LabelForm,
    OrderTemplateLabelFormMap,
    OrderTemplateLabelFormatMap,
    RxStatus,
    RxReadyToFill,
    DtExceptionActions,
    DtExceptionActionKeys,
};
