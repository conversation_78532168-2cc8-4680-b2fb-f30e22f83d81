"use strict";
const _ = require("lodash");
const Joi = require("joi");

const { RecordSchemas } = require("./schemas");
const { InventoryTypes } = require("@inventory/settings");
/**
 * @class
 * @classdesc Class responsible for generating and managing dispense records.
 */
module.exports = class DispenseGeneratorClass {
    constructor(nes, ctx) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        const DispenseFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const DispenseHelperClass = require("./helper");
        this.helper = this.fx.getInstance(
            ctx,
            DispenseHelperClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Generates a delivery ticket from the creation view data.
     * @async
     * @param {Object} view - The creation view data containing information for the delivery ticket.
     * @returns {Promise<Object>} A promise that resolves to the generated delivery ticket object.
     */
    async generateDeliveryTicket(view) {
        console.debug(`Generating delivery ticket from creation view data`);

        try {
            await this.fx.validateSchema(
                "view",
                RecordSchemas.deliveryTicketView.required(),
                view,
                "Missing/Invalid delivery ticket view data to generate delivery ticket."
            );
            const rxId = view.rx_id;
            const rxwIds =
                view.dispense_prescription?.map(
                    (prescription) => prescription.id
                ) || [];
            const supplyOrderIds =
                view.dispense_sorder?.map((rental) => rental.id) || [];
            const supplyKitItemIds =
                view.preview_skt?.map((rental) => rental.id) || [];
            if (
                rxwIds.length === 0 &&
                supplyOrderIds.length === 0 &&
                supplyKitItemIds.length === 0
            ) {
                throw new Error(
                    "Missing/Invalid dispense items to generate delivery ticket."
                );
            }

            const viewWorkingTickets = view.working_ticket_id;
            let workingTicketId = null;
            if (viewWorkingTickets) {
                if (
                    Array.isArray(viewWorkingTickets) &&
                    viewWorkingTickets.length > 0
                ) {
                    try {
                        if (
                            !viewWorkingTickets[0] ||
                            !viewWorkingTickets[0].id
                        ) {
                            throw new Error(
                                "Invalid working ticket object - missing id property"
                            );
                        }
                        workingTicketId = viewWorkingTickets[0].id;
                    } catch (e) {
                        throw new Error(
                            `Error getting working ticket ID: ${e.message}`
                        );
                    }
                }
            }
            const rxwIdsArray = this.fx.pgArrayFormat(rxwIds);
            const supplyOrderIdsArray = this.fx.pgArrayFormat(supplyOrderIds);
            const supplyKitItemsIdArray =
                this.fx.pgArrayFormat(supplyKitItemIds);
            // Build the SQL query without pg-escape formatting
            const sql = `
            SELECT create_delivery_ticket_presets(
                '${rxwIdsArray}'::integer[], 
                '${rxId}', 
                '${view.service_from}', 
                '${view.service_to}', 
                ${view.supply_kit_id || "NULL"},
                '${supplyOrderIdsArray}'::integer[],
                '${supplyKitItemsIdArray}'::integer[],
                ${workingTicketId ? `'${workingTicketId}'` : "NULL"}
            ) AS result
            `;
            const rows = await this.db.env.rw.query(sql);
            const results = rows[0].result;
            const careplanDeliveryTicketRec =
                results?.careplan_delivery_tick || null;
            if (!careplanDeliveryTicketRec) {
                throw new Error(
                    "Missing careplan delivery ticket preset to generate delivery ticket."
                );
            }
            const careplanItemRecs = results?.careplan_dt_item || [];
            if (careplanItemRecs.length === 0) {
                if (!workingTicketId) {
                    throw new Error(
                        "Missing careplan delivery ticket items to generate delivery ticket."
                    );
                }
            }

            const workingPrescriptionId =
                await this.fetcher.fetchWorkingPrescriptionRecordId(rxId);
            console.log(`Working prescription ID: ${workingPrescriptionId}`);
            // Update existing delivery ticket record with new items if any
            if (workingTicketId) {
                return await this.__updateWorkingTicket({
                    workingTicketId,
                    careplanDeliveryTicketRec,
                    careplanItemRecs,
                });
            }
            const transaction = this.db.env.rw.transaction(this.ctx);

            careplanDeliveryTicketRec.ticket_no =
                transaction.series_next_number("TICKET");
            for (const itemRec of careplanItemRecs) {
                itemRec.ticket_no = careplanDeliveryTicketRec.ticket_no;
                itemRec.ticket_item_no =
                    transaction.series_next_number("TICKET_ITEM");
                itemRec.status = careplanDeliveryTicketRec.status;
                await transaction.insert("careplan_dt_item", itemRec);
            }
            careplanDeliveryTicketRec.id = await transaction.insert(
                "careplan_delivery_tick",
                careplanDeliveryTicketRec
            );
            const res = await transaction.commit();
            if (res.error) throw this.fx.getClaraError(res.message);
            transaction.init();

            const formId = _.head(
                this.fx.fetchFormIdsFromTransactionResults(
                    "careplan_delivery_tick",
                    res
                )
            );
            const assessmentPresetData =
                await this.helper.getDeliveryTicketAssessmentPresets(formId);
            let needsPrefill = "N";
            const assessmentForm = assessmentPresetData?.form || null;
            if (!assessmentForm) {
                console.error(
                    `Missing assessment form for delivery ticket ID ${formId}`
                );
            } else {
                needsPrefill = "Y";
                const updatedDeliveryTicketRec =
                    await this.fetcher.fetchCachedRecord(
                        "careplan_delivery_tick",
                        formId
                    );
                const assessmentPresets = assessmentPresetData?.presets || {};
                const finalPresets = {
                    ...updatedDeliveryTicketRec.patient_id,
                    ...updatedDeliveryTicketRec.careplan_id,
                    ...assessmentPresets,
                };
                await transaction.update(
                    "careplan_delivery_tick",
                    {
                        ...updatedDeliveryTicketRec,
                        assessment_form: assessmentForm,
                        subform_assessment: [
                            {
                                ...finalPresets,
                            },
                        ],
                    },
                    updatedDeliveryTicketRec.id
                );
                const assessmentRes = await transaction.commit();
                if (assessmentRes.error)
                    throw this.fx.getClaraError(assessmentRes.message);
                transaction.init();
            }
            console.log(`New Delivery Ticket ID: ${formId}`);
            if (!formId) {
                console.error(
                    `Error encountered while fetching delivery ticket record ID from transaction results.`
                );
                throw this.fx.getClaraError(
                    "Error encountered while fetching delivery ticket record ID from transaction results."
                );
            }

            return {
                _meta: {
                    prefill: needsPrefill,
                },
                careplan_delivery_tick: formId,
            };
        } catch (e) {
            const errorMessage = `Error encountered while building delivery ticket record`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a delivery ticket creation view for a given order ID.
     * @async
     * @param {string} rxId - The ID of the order for which to generate the delivery ticket view.
     * @returns {Promise<Object>} A promise that resolves to an object containing the delivery ticket creation view data.
     */
    async generateDeliveryTicketView(rxId) {
        console.debug(
            `Generating delivery ticket creation view for prescription ID ${rxId}`
        );
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing/Invalid prescription ID to generate delivery ticket view."
            );

            const deliveryTicketId =
                await this.fetcher.fetchRxOpenDeliveryTicketId(rxId);
            if (deliveryTicketId) {
                return {
                    form: "careplan_delivery_tick",
                    record: deliveryTicketId,
                };
            }

            const sql = `
                SELECT create_delivery_ticket_create_view_presets(%s::integer) AS result
            `;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [rxId]);
            const returnVal = rows[0].result;
            return {
                form: "view_create_dt",
                presets: returnVal,
            };
        } catch (e) {
            const errorMessage = `Error building delivery ticket creation view for prescription ID ${rxId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Processes a confirmed delivery ticket and generates confirmation presets
     * @async
     * @param {number} dtId - The ID of the delivery ticket to process
     * @param {number} userId - The ID of the user processing the confirmation
     * @returns {Promise<Object>} Object containing confirmation presets or error message
     * @throws {Error} If there is an error processing the delivery ticket confirmation
     */
    async generateDTConfirmedPresets(dtId, userId) {
        console.log(`Generating DT Confirmed presets for DT ID ${dtId}`);
        try {
            await Promise.all([
                this.fx.validateSchema(
                    "dtId",
                    Joi.number().required(),
                    dtId,
                    "Missing delivery ticket ID to process."
                ),
                this.fx.validateSchema(
                    "userId",
                    Joi.number().required(),
                    userId,
                    "Missing user ID to process."
                ),
            ]);
            const dtConfirmedPresetsQuery = `
                        SELECT 
                            generate_confirmed_dt_presets(%s::integer, %s::integer) as result
                    `;
            const dtConfirmedPresetsParams = [dtId, userId];
            const dtConfirmedPresets = await this.db.env.rw.parseSQLUsingPGP(
                dtConfirmedPresetsQuery,
                dtConfirmedPresetsParams
            );

            if (dtConfirmedPresets.length === 0) {
                return {
                    error: "Unable to generate confirmation presets.",
                };
            }
            const results = dtConfirmedPresets[0].result;
            return [results];
        } catch (e) {
            const errorMessage = `Error encountered while processing DT Confirmed for DT ID ${dtId}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Creates delivery ticket items from generated data
     * @async
     * @param {Object} data - The data object containing delivery ticket items information
     * @param {number} data.careplan_delivery_tick_id - The ID of the delivery ticket
     * @param {Object|Array<Object>} data.careplan_dt_item - The delivery ticket item(s) to create
     * @returns {Promise<Object>} Object containing the created delivery ticket items and their IDs
     * @throws {Error} If there is an error creating the delivery ticket items
     */
    async createDeliveryTicketItems(data) {
        console.log("Creating delivery ticket items");
        try {
            const dtId = data.careplan_delivery_tick_id;
            const dtItems = Array.isArray(data.careplan_dt_item)
                ? data.careplan_dt_item
                : [data.careplan_dt_item];

            if (!dtItems || dtItems.length === 0) {
                throw new Error("No delivery ticket items to create");
            }

            const transaction = this.db.env.rw.transaction(this.ctx);
            const createdItemIds = [];

            for (const itemData of dtItems) {
                if (!itemData || !itemData.inventory_id) {
                    console.warn("Skipping invalid item");
                    continue;
                }

                // Make a copy to avoid modifying the original
                const cleanItemData = { ...itemData };

                // Ensure dispense_quantity is at least 1
                if (
                    !cleanItemData.dispense_quantity ||
                    cleanItemData.dispense_quantity < 1
                ) {
                    cleanItemData.dispense_quantity = 1;
                }

                if (!cleanItemData.ticket_item_no) {
                    cleanItemData.ticket_item_no =
                        transaction.series_next_number("TICKET_ITEM");
                }

                console.log(
                    `Inserting item ${cleanItemData.inventory_id} (${cleanItemData.type})`
                );

                try {
                    const itemId = await transaction.insert(
                        "careplan_dt_item",
                        cleanItemData
                    );
                    createdItemIds.push(itemId);
                } catch (insertError) {
                    console.error(
                        `Error inserting item ${cleanItemData.inventory_id}:`,
                        insertError
                    );
                    console.error(
                        "Item data:",
                        JSON.stringify(cleanItemData, null, 2)
                    );
                    throw insertError;
                }
            }

            const res = await transaction.commit();

            if (res.error) {
                throw this.fx.getClaraError(res.message);
            }
            transaction.init();

            const formIds = this.fx.fetchFormIdsFromTransactionResults(
                "careplan_dt_item",
                res
            );

            // Format the return value properly
            let careplan_dt_item = Array.isArray(formIds)
                ? formIds
                : Object.values(formIds);

            // If we have a single ID, don't return an array
            if (
                Array.isArray(careplan_dt_item) &&
                careplan_dt_item.length === 1
            ) {
                careplan_dt_item = careplan_dt_item[0];
            }

            this.ctx.status = 200;
            this.ctx.body = {
                success: true,
                careplan_delivery_tick: dtId,
                careplan_dt_item,
            };

            return this.ctx.body;
        } catch (e) {
            const errorMessage =
                "Error encountered while creating delivery ticket items";
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Add a delivery ticket item - gets item data and creates it in one operation
     * @async
     * @param {Object} data - The input data for the delivery ticket item
     * @returns {Promise<Object>} The created delivery ticket item IDs
     */
    async addDeliveryTicketItem(data) {
        const {
            ticket_no,
            patient_id,
            careplan_id,
            site_id,
            rx_id,
            add_skt,
            supply_kit_id,
            inventory_id,
            billable_id,
            type,
            preview_skt,
            rental_type,
            frequency_code,
            day_supply,
            dispense_quantity,
        } = data;
        const isSupplyKit = add_skt === "Yes";

        console.log(
            `Adding delivery ticket item: ${isSupplyKit ? "Supply Kit: " + supply_kit_id : "Inventory Item: " + inventory_id}`
        );

        const dtItems = [];
        const deliveryTicketRec =
            await this.fetcher.fetchDeliveryTicketRecWithTicketNo(ticket_no);
        try {
            if (isSupplyKit) {
                console.debug("Processing Supply Kit with items", preview_skt);

                // Process each item in the kit
                const sktItemIds = preview_skt.map((item) => item.id);
                console.debug("Supply kit ids:", sktItemIds);
                const sktItems =
                    sktItemIds.length > 0
                        ? await this.fetcher.fetchCachedRecords(
                              "inventory_sk_item",
                              sktItemIds
                          )
                        : [];
                console.debug("Supply kit items:", sktItems);
                for (const supplyItem of sktItems) {
                    const sktId = supplyItem.id;

                    const inventoryName = `SKT Item ${sktId}`;
                    const inventoryId = supplyItem.inventory_id;
                    if (!inventoryId) {
                        console.debug("Skipping item with no inventory ID", {
                            sktId,
                            dispenseQuantity: supplyItem.dispense_quantity,
                            name: inventoryName,
                        });
                        continue;
                    }

                    const sql = `
                        SELECT gen_delivery_ticket_item_by_type(
                            ${site_id}::integer,
                            ${patient_id}::integer,
                            ${careplan_id}::integer,
                            ${inventoryId}::integer,
                            ${rx_id}::integer,
                            'Supply'::text,
                            '${ticket_no}'::text,
                            'Yes'::text,
                            NULL::text,
                            NULL::integer
                        ) AS result;
                    `;

                    // Try to get data from SQL function
                    let sqlResult = null;
                    try {
                        const rows = await this.db.env.rw.query(sql);
                        if (rows && rows.length > 0 && rows[0]?.result) {
                            sqlResult = rows[0].result;
                        } else {
                            console.warn(
                                `No results returned from SQL function  gen_delivery_ticket_item_by_type for kit item ${inventoryId} - creating fallback data`
                            );
                        }
                    } catch (err) {
                        console.error(
                            `Error getting data for kit item ${inventoryId}:`,
                            err
                        );
                    }

                    // Create a fallback object if SQL didn't return results
                    if (!sqlResult) {
                        sqlResult = {
                            code:
                                this.shared?.uuid?.v4() ||
                                `gen-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
                            inventory_id: inventoryId,
                            patient_id: patient_id,
                            careplan_id: careplan_id,
                            site_id: site_id,
                            status: deliveryTicketRec.status,
                            type: "Supply",
                            print: "Yes",
                            allow_dispense_quantity_update: "Yes",
                            part_of_kit: "Yes",
                            ticket_no: ticket_no,
                            ticket_item_no:
                                this.shared?.uuid?.v4() ||
                                `gen-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
                        };
                    }

                    // Start with a clean base object
                    const cleanBase = {
                        ticket_no,
                        patient_id,
                        site_id,
                        careplan_id,
                        status: deliveryTicketRec.status,
                        type: "Supply",
                        dispense_quantity: supplyItem?.dispense_quantity || 1,
                        quantity_needed: supplyItem?.dispense_quantity || 1,
                        part_of_kit: "Yes",
                        inventory_id: inventoryId,
                        print: "Yes",
                        allow_dispense_quantity_update: "Yes",
                    };

                    // Merge with SQL results
                    const itemData = { ...sqlResult };

                    // Now add our base fields, overwriting any existing fields to ensure consistency
                    for (const key in cleanBase) {
                        if (
                            key === "dispense_quantity" &&
                            cleanBase[key] === 0
                        ) {
                            itemData[key] = 1;
                        } else {
                            itemData[key] = cleanBase[key];
                        }
                    }

                    dtItems.push(itemData);
                }
            }

            // For non-kit items, continue with existing logic
            // Determine inventory_id based on item type
            const inventoryId = isSupplyKit
                ? billable_id || null
                : inventory_id;
            if (!inventoryId && isSupplyKit) {
                console.debug(`No billable item for supply kit, skipping`);
                return {
                    careplan_delivery_tick: deliveryTicketRec.id,
                    careplan_dt_item: dtItems,
                };
            }

            // Only validate inventory_id for non-prescription items
            if (!inventoryId) {
                throw new Error(`Missing inventory_id for type ${type}`);
            }

            // Call the SQL function using direct values instead of parameters

            const sql = `
                SELECT gen_delivery_ticket_item_by_type(
                    ${site_id}::integer,
                    ${patient_id}::integer,
                    ${careplan_id}::integer,
                    ${inventoryId}::integer,
                    ${rx_id}::integer,
                    '${ticket_no}'::text,
                    NULL::text,
                    ${rental_type ? `'${rental_type}'` : "NULL"}::text,
                    ${frequency_code ? `'${frequency_code}'` : "NULL"}::text,
                    ${day_supply ? day_supply : "NULL"}::integer
                ) AS result;
            `;

            console.debug(`Executing SQL for item ${inventoryId}`);

            const rows = await this.db.env.rw.query(sql);

            if (!rows || rows.length === 0) {
                throw new Error("No results returned from SQL function");
            }

            const sqlResult = rows[0]?.result;
            if (!sqlResult) {
                throw new Error(
                    "SQL function returned null or undefined result"
                );
            }
            const cleanBase = {
                ticket_no,
                patient_id,
                site_id,
                careplan_id,
                status: deliveryTicketRec.status,
                type: type,
                dispense_quantity: dispense_quantity || 1,
                quantity_needed: dispense_quantity || 1,
                inventory_id: inventoryId,
                print: "Yes",
                allow_dispense_quantity_update: "Yes",
            };

            const newItemData = { ...sqlResult };

            for (const key in cleanBase) {
                newItemData[key] = cleanBase[key];
            }
            dtItems.push(newItemData);

            return {
                careplan_delivery_tick: deliveryTicketRec.id,
                careplan_dt_item: dtItems,
            };
        } catch (error) {
            console.error("Error in addDeliveryTicketItem:", error);
            throw error;
        }
    }

    /**
     * Updates a working delivery ticket with new items and records
     * @async
     * @param {Object} params - The parameters object
     * @param {number} params.workingTicketId - The ID of the working delivery ticket to update
     * @param {Object} params.careplanDeliveryTicketRec - The delivery ticket record to update with
     * @param {Array<Object>} params.careplanItemRecs - Array of careplan item records to add to the ticket
     * @returns {Promise<void>}
     * @throws {Error} If there is an error updating the working ticket
     */
    async __updateWorkingTicket({
        workingTicketId,
        careplanDeliveryTicketRec,
        careplanItemRecs,
    }) {
        console.debug(
            `Updating working delivery ticket with new items and records`
        );
        try {
            const transaction = this.db.env.rw.transaction(this.ctx);

            const existingDeliveryTicketRec =
                await this.fetcher.fetchCachedRecord(
                    "careplan_delivery_tick",
                    workingTicketId
                );

            const existingItems = await this.fetcher.fetchDeliveryTicketItems(
                existingDeliveryTicketRec
            );
            for (const itemRec of careplanItemRecs) {
                // Check for existing items with same inventory_id
                const matchingItem = existingItems.find(
                    (existing) => existing.inventory_id === itemRec.inventory_id
                );
                if (matchingItem) {
                    if (
                        [InventoryTypes.DRUG, InventoryTypes.COMPOUND].includes(
                            matchingItem.type
                        )
                    ) {
                        console.error(
                            `${itemRec.inventory_id_auto_name} already exists on this delivery ticket. You cannot add the same drug twice to an existing ticket.`
                        );
                    }
                    console.log(
                        `Duplicate item found ${itemRec.inventory_id_auto_name}, skipping`
                    );
                    continue;
                } else {
                    itemRec.ticket_no = existingDeliveryTicketRec.ticket_no;
                    itemRec.ticket_item_no =
                        transaction.series_next_number("TICKET_ITEM");
                    itemRec.status = existingDeliveryTicketRec.status;
                    await transaction.insert("careplan_dt_item", itemRec);
                }
            }

            await transaction.update(
                "careplan_delivery_tick",
                {
                    summary: careplanDeliveryTicketRec.summary,
                    total_pt_responsibility:
                        careplanDeliveryTicketRec.total_pt_responsibility,
                    rx_id: careplanDeliveryTicketRec.rx_id,
                },
                existingDeliveryTicketRec.id
            );

            const res = await transaction.commit();
            if (res.error) throw this.fx.getClaraError(res.message);
            transaction.init();
            const formId = _.head(
                this.fx.fetchFormIdsFromTransactionResults(
                    "careplan_delivery_tick",
                    res
                )
            );
            const updatedDeliveryTicketRec =
                await this.fetcher.fetchCachedRecord(
                    "careplan_delivery_tick",
                    formId
                );
            const assessmentForm = updatedDeliveryTicketRec?.assessment_form;
            const assessmentData =
                updatedDeliveryTicketRec?.subform_assessment?.[0] || null;
            let needsPrefill = "N";
            const assessmentPresetData =
                await this.helper.getDeliveryTicketAssessmentPresets(formId);
            const assessmentPresets = assessmentPresetData?.presets || {};
            if (!assessmentForm || !assessmentData) {
                console.error(
                    `Missing assessment for existing delivery ticket ID ${existingDeliveryTicketRec.id}`
                );
                needsPrefill = "Y";
                const finalPresets = {
                    ...updatedDeliveryTicketRec.patient_id,
                    ...updatedDeliveryTicketRec.careplan_id,
                    ...(assessmentData || {}),
                    ...assessmentPresets,
                };
                await transaction.update(
                    "careplan_delivery_tick",
                    {
                        summary: careplanDeliveryTicketRec.summary,
                        assessment_form:
                            assessmentForm || assessmentPresetData.form,
                        subform_assessment: [
                            {
                                ...finalPresets,
                            },
                        ],
                    },
                    updatedDeliveryTicketRec.id
                );
            } else {
                await transaction.update(
                    assessmentForm,
                    {
                        ...(assessmentData || {}),
                        ...assessmentPresets?.presets,
                    },
                    assessmentData?.id
                );
            }
            const assessmentRes = await transaction.commit();
            if (assessmentRes.error)
                throw this.fx.getClaraError(assessmentRes.message);
            transaction.init();
            return {
                _meta: {
                    prefill: needsPrefill,
                },
                careplan_delivery_tick: updatedDeliveryTicketRec.id,
            };
        } catch (e) {
            const errorMessage = `Error encountered while updating working ticket`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
