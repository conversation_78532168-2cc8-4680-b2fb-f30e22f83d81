"use strict";
const ErrorMessages = {
    MISSING_VIEW_DATA: "Missing data for view action.",
    MISSING_ORDER:
        "Cannot calculate dispense quantity, missing order data. Please try again or contact support if issues persist.",
    MISSING_ORDER_ITEM:
        "Cannot calculate refills or doses remaining, missing prescription data. Please try again or contact support if issues persist.",
    COB_PAYER_FETCH_ERROR:
        "Exception encountered while fetching COB payer. Please contact support if issue persists.",
    COB_PAYERS_FETCH_ERROR:
        "Exception encountered while fetching COB payers. Please contact support if issue persists.",
    AVAILABLE_PAYERS_FETCH_ERROR:
        "Exception encountered while fetching available payers. Please contact support if issue persists.",
    LEDGER_PAYMENT_ENTRIES_FETCH_ERROR:
        "Exception encountered while fetching ledger payment entries. Please contact support if issue persists.",
    LEDGER_POSTING_ENTRIES_FETCH_ERROR:
        "Exception encountered while fetching ledger posting entries. Please contact support if issue persists.",
    LEDGER_PAYMENT_APPLIED_ENTRIES_FETCH_ERROR:
        "Exception encountered while fetching ledger payment applied entries. Please contact support if issue persists.",
    MISSING_DELIVERY_TICKET_DATA:
        "Missing delivery ticket data. Please try again or contact support if issues persist.",
    MISSING_DELIVERY_TICKET_ID:
        "Missing delivery ticket ID. Please try again or contact support if issues persist.",
    SECURITY_APPROVAL_ERROR:
        "Error encountered while generating security approval action. Please contact support if issues persist.",
    VOID_CLOSING_SAVE_ERROR:
        "Error encountered while saving voided closing record. Please contact support if issues persist.",
    MISSING_PAYER_IDS:
        "Missing payer IDs. Payer IDs are required to get the max claims.",
    MISSING_INVENTORY_ID: "Missing Inventory ID.",
    MISSING_SITE_ID: "Missing Site ID.",
    MISSING_PRIMARY_RANKED_PAYER: "Missing Primary Ranked Payer.",
    MISSING_WF_ID_AND_FORM_NAME:
        "Error: An order ID, delivery ticket ID, or rx ID, and form name are required to generate form presets.",
    INVALID_FORM: "Missing or invalid form name",
    MISSING_TICKET_NO: "Missing delivery ticket number.",
};

module.exports = {
    ErrorMessages,
};
