"use strict";
const _ = require("lodash");
const SchemaUtils = require("./schema-utils");

module.exports = class ApiView {
    constructor(nes) {
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.schema = new SchemaUtils();
    }

    async process(ctx, urlpath) {
        try {
            if (!ctx.request.method == "GET") {
                ctx.status = 400;
                ctx.body = { error: "Only GET method is allowed" };
                return;
            }
            if (urlpath.path.lenght < 5) {
                ctx.status = 400;
                ctx.body = { error: "Invalid path" };
                return;
            }

            const user = ctx.user;
            const params = Object.assign({}, ctx.query);
            if (user) {
                const query_code = urlpath.path[4];
                const query_rec = _.head(
                    await this.form.get.get_form(ctx, user, "query", {
                        limit: 1,
                        filter: [`code:${query_code}`],
                    })
                );
                if (!query_rec) {
                    ctx.status = 400;
                    ctx.body = { error: "Invalid query code" };
                    return;
                }
                const query = query_rec.report_sql;
                if (!query) {
                    ctx.status = 400;
                    ctx.body = { error: "Invalid query" };
                    return;
                }
                //in case sent_to.user_id is send as filter ignore it
                const r = await this.schema.get_schema(query, this.db, params);
                if (!r || r.error) {
                    ctx.status = 500;
                    ctx.body = { error: r.error || "Failed Unexpectedly" };
                    return;
                }
                ctx.body = this.schema.convert_data_types_to_cson(r.result);
            } else {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            }
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
