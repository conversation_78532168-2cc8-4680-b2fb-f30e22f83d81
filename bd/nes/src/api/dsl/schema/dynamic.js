"use strict";
const _ = require("lodash");
const SchemaUtils = require("./schema-utils");

module.exports = class ApiView {
    constructor(nes) {
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.schema = new SchemaUtils();
    }

    async process(ctx, urlpath) {
        try {
            if (!ctx.request.method == "POST") {
                ctx.status = 400;
                ctx.body = { error: "Only POST method is allowed" };
                return;
            }
            const user = ctx.user;
            const params = Object.assign({}, ctx.query);
            if (user) {
                const body = ctx.request.body || {};
                if (!body.query) {
                    ctx.status = 400;
                    ctx.body = { error: "Invalid query" };
                    return;
                }
                //in case sent_to.user_id is send as filter ignore it
                const r = await this.schema.get_schema(
                    body.query,
                    this.db,
                    params
                );
                if (!r || r.error) {
                    ctx.status = 500;
                    ctx.body = { error: r.error || "Unexpected Error" };
                    return;
                }
                ctx.body = this.schema.convert_data_types_to_cson(r.result);
            } else {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            }
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
