"use strict";

const _ = require("lodash");
const { v4: uuid } = require("uuid");

module.exports = class SchemaUtils {
    constructor() {}
    async get_schema(query, db, params) {
        const table_name = `temp_table_${uuid().replace(/-/g, "")}`;
        query = `${query.replaceAll("  ", " ").trim().replace(/;$/, "")}`;
        try {
            // must always use read db
            await db.env.rw.query(
                `CREATE TEMPORARY TABLE ${table_name} AS ${query} LIMIT 0`
            );
            const result = await db.env.rw.query(
                `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '${table_name}'`
            );
            await db.env.rw.query(`DROP TABLE ${table_name}`);
            let has_tag = false;
            for (const row of result) {
                if (row.column_name == "tag") {
                    has_tag = true;
                    break;
                }
            }
            if (params?.ignoreWFChecks != "true") {
                const has_order_by = /ORDER\s+BY\s+(?:\([^)]+\)|[^;]+)$/i.test(
                    query
                );
                if (!has_order_by) {
                    return {
                        error: "Query is Missing ORDER BY Clause at the end",
                    };
                }
                if (!has_tag) {
                    return {
                        error: "Query is Missing Tag Column in Select Statement",
                    };
                }
            }
            return { result: result };
        } catch (err) {
            console.error(err);
            return {
                error: "Invalid Query",
            };
        }
    }

    to_presentable_label(str) {
        if (!str) {
            return "";
        }
        let result = str.replace(/_/g, " ");

        result = result.replace(/\b\w/g, (char) => char.toUpperCase());

        return result;
    }

    convert_data_types_to_cson(columns) {
        const dsl_type_map = {
            integer: "int",
            "timestamp without time zone": "datetime",
            "time without time zone": "time",
            "character varying": "text",
            boolean: "bool",
            numeric: "decimal",
            date: "date",
            time: "time",
        };
        const update_columns = [];
        for (const column of columns) {
            const dsl_type = dsl_type_map[column.data_type] || "text";
            update_columns.push({
                id:
                    column.column_name +
                    "_" +
                    dsl_type.replace(/ /g, "_").toLowerCase(),
                field: column.column_name,
                label: this.to_presentable_label(column.column_name),
                type: dsl_type,
                sql_column: column.column_name,
                sql_type: column.data_type,
                grid: false,
                filter: "N",
            });
        }
        return update_columns;
    }
};
