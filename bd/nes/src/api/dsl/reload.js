"use strict";

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
    }

    async process(ctx, urlpath) {
        try {
            if (!ctx.user || ctx.user.is_admin != "Yes") {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
                return;
            }
            await this.dsl.reload();
            ctx.body = "DSL reloaded successfully";
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
