"use strict";
const UnauthorizedException = {
    status: 403,
};

module.exports = class ApiTest {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.crud = nes.shared.crud;
        this.table = "dsl";
        this.AppDsl = nes.shared.AppDsl;
    }

    async process(ctx, urlpath) {
        const user = ctx.user;
        let force = false;
        ctx.request.socket.setTimeout(2 * 60 * 1000);
        try {
            if (
                !user ||
                !("role" in user) ||
                !("is_admin" in user) ||
                !user.is_admin == "Yes"
            ) {
                throw UnauthorizedException;
            }
            const json = ctx.request.body;
            const dsl_key = urlpath.path[3]
                ? urlpath.path[3]
                : Object.keys(json)[0];
            if (urlpath.path[4] === "full") force = true;
            if (ctx.request.method == "PUT" || ctx.request.method == "POST") {
                const valid_name = this._valid_name(dsl_key);
                if (valid_name.status) {
                    try {
                        const res = await this._validate_save(json, force);
                        if (res) {
                            ctx.status = 201;
                            ctx.body = {
                                [dsl_key]:
                                    this.shared.BASE_URL +
                                    "/api" +
                                    this.table +
                                    "/" +
                                    dsl_key,
                            };
                        } else {
                            ctx.status = 400;
                            ctx.body = new Error(
                                "Deployment failed, See NES logs for more information"
                            );
                        }
                    } catch (e) {
                        console.error(e);
                        ctx.status = 400;
                        ctx.body = {
                            error: e + " See NES logs for more information",
                        };
                    }
                } else {
                    ctx.status = 400;
                    ctx.body = { error: valid_name.message };
                }
            }
        } catch (e) {
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
                console.error(e);
            } else {
                ctx.status = 500;
                ctx.body = e;
                console.error(e);
            }
        }
    }

    async _validate_save(json, force = false) {
        // accept only one json key like:
        // {"name_label":{...dsl..}}
        // if > 1 key then it is an error.

        if (Object.keys(json).length < 1) {
            console.log("requires at least one dsl form to post");
            return false;
        }

        await this.AppDsl.create(this.nes, json, false, force);
        return true;
    }

    _valid_name(name) {
        if (name !== name.toLowerCase()) {
            return {
                status: false,
                message: name + ": form names can be lower case only",
            };
        }

        const reserved_name = this.shared.RESERVED_DSL_FORMS;
        if (reserved_name.includes(name)) {
            console.log(
                name +
                    " is a reserved name. Names in the following list is not allowed: " +
                    reserved_name
            );
            return {
                status: false,
                message:
                    name +
                    " is a reserved name. Names in the following list is not allowed: " +
                    reserved_name,
            };
        }
        return { status: true };
    }
};
