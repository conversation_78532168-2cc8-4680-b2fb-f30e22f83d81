"use strict";

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
    }

    async process(ctx, urlpath) {
        try {
            const dsl = await this.dsl.get_cached();
            if (dsl) {
                let et = "";
                for (const [_, v] of Object.entries(dsl))
                    if (
                        "view" in v &&
                        "timestamp" in v.view &&
                        v.view.timestamp > et
                    )
                        et = v.view.timestamp;
                if (
                    ctx.header &&
                    "if-none-match" in ctx.header &&
                    ctx.header["if-none-match"] == et
                ) {
                    ctx.status = 304;
                } else {
                    if (et && "set" in ctx) ctx.set("ETag", et);
                    if (urlpath.path.length > 2) {
                        const form_name = urlpath.path[2];
                        if (form_name == "autoinsert") {
                            ctx.body = {
                                [form_name]: this.shared.DSL_AutoInsert,
                            };
                        } else {
                            ctx.body = { [form_name]: dsl[form_name] };
                        }
                    } else {
                        ctx.body = dsl;
                    }
                }
            } else {
                ctx.status = 500;
                ctx.body = {
                    error: "Could not load DSL! Please reload DSL on NES.",
                };
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
