"use strict";

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
    }

    async process(ctx, urlpath) {
        try {
            const dsl = await this.dsl.get_cached();
            if (dsl) {
                const dt =
                    "if-none-match" in ctx.header
                        ? ctx.header["if-none-match"]
                        : "";
                let et = "";
                for (const [k, v] of Object.entries(dsl)) {
                    if ("view" in v && "timestamp" in v.view) {
                        if (v.view.timestamp > et) et = v.view.timestamp;
                        if (v.view.timestamp <= dt) dsl[k] = true;
                    }
                }
                if (et) ctx.set("ETag", et);
                ctx.body = dsl;
            } else {
                ctx.status = 500;
                ctx.body = {
                    error: "Could not load DSL! Please reload DSL on NES.",
                };
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
