"use strict";
const _ = require("lodash");
const moment = require("moment");

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nescache = nes.modules.nescache;
        this.db = nes.modules.db;
    }
    async process(ctx, urlpath) {
        try {
            const { key } = ctx.query;
            ctx.status = 200;
            if (ctx.request.method == "PUT") {
                const { value } = ctx.request.body;
                await this.nescache.set(key, value);
                ctx.body = { message: "OK!" };
            } else if (ctx.request.method == "GET") {
                const cached_row = await this.nescache.get(key);
                if (!_.isEmpty(cached_row)) {
                    let body = JSON.parse(cached_row.data);
                    if (ctx.query.form) {
                        body = await this.process_form_data(ctx, key, {
                            updated_on: cached_row.updated_on,
                            body,
                        });
                    }
                    ctx.body = body;
                } else {
                    ctx.body = {
                        error: `Unable to fetch data against key ${key} from cache.`,
                    };
                }
            } else if (ctx.request.method == "DELETE") {
                try {
                    await this.nescache.unset(key);
                    ctx.body = {
                        message: `OK!`,
                    };
                } catch (e) {
                    console.log(`NES error: ${e}`);
                }
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }

    async process_form_data(ctx, key, cached_data) {
        const form = ctx.query.form;
        const id = parseInt(ctx.query.id);
        // Make sure this form was not updated since last recovery was set
        let { updated_on, body } = cached_data;
        updated_on = moment(updated_on).format("YYYY-MM-DD HH:mm:ss");
        if (form && id && form in this.shared.DSL && !Number.isNaN(id)) {
            const q = `SELECT id FROM %I WHERE id = %s AND (updated_on IS NULL OR updated_on < %L);`;
            const db_rec = await this.db.env.rw.query(q, [
                `form_${form}`,
                id,
                updated_on,
            ]);
            if (db_rec.length == 0) {
                body = { error: `Data against key ${key} is no longer valid.` };
                await this.nescache.unset(key);
            }
        }
        return body;
    }
};
