"use strict";
const SQLGetClass = require("@form/helpers/sql-get");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.sql_get = new SQLGetClass(nes);
    }

    async get_uniques(ctx, form, field, params = {}) {
        if (!("sort" in params)) params.sort = field;
        const sf = await this.sql_get.sql_form(ctx, form, params);
        if ("error" in sf) {
            ctx.status = 500;
            return sf;
        } else {
            // eslint-disable-next-line prefer-const
            let { sql, par } = sf;
            let frows = [];
            if (sql && typeof sql === "string") {
                // remove all fields/joins because they are not needed for unique queries
                sql = sql.replace(
                    /\(SELECT(.*)WHERE/,
                    "(SELECT DISTINCT " +
                        field +
                        " AS f" +
                        " FROM form_" +
                        form +
                        " WHERE (" +
                        field +
                        " IS NOT NULL) AND"
                );

                const rows = await this.db.env.rw.query(sql, par);
                frows = rows ? rows.map((r) => r.f) : [];
            }
            return frows;
        }
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const form = urlpath.path[2];
            if (!user || !this.auth.can_access_form(ctx, form)) {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            } else if (urlpath.path.length > 3) {
                const field = urlpath.path[3];
                if (!this.auth.can_access_field(ctx, form, field)) {
                    ctx.status = 403;
                    ctx.body = { error: "Access denied." };
                } else {
                    const params = Object.assign({}, ctx.query);
                    if (!("limit" in params))
                        params.limit = this.shared.config.limits.default_rows;
                    ctx.body = await this.get_uniques(ctx, form, field, params);
                }
            }
        } catch (e) {
            ctx.status = 500;
            if ("error" in e && "stack" in e.error)
                e.error.trace = e.error.stack.split("\n");
            ctx.body = e;
            console.error(e);
        }
    }
};
