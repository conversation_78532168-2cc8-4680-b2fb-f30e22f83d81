"use strict";

const GetAccessClass = require("./get_access");
const GetDslAccess = require("./get_dsl_access");

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.get_access = new GetAccessClass(nes);
        this.get_dsl_access = new GetDslAccess(nes);
    }
    async process(ctx, urlpath) {
        try {
            const rule = await this.get_access.process(ctx);
            const dsl = await this.get_dsl_access.process(ctx, urlpath);
            ctx.body = { rule, dsl };
            return ctx.body;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
