"use strict";
module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.db = nes.modules.db;
    }
    async process(ctx) {
        try {
            if (ctx?.user?.is_admin == "Yes") {
                return {};
            }
            ctx.body = this.fx.getCachedAccess(ctx)["dsl"];
            return ctx.body;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
