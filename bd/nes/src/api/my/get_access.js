"use strict";
module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
    }

    async process(ctx) {
        if (ctx?.user?.is_admin == "Yes") {
            return {};
        }
        ctx.body = this.fx.getCachedAccess(ctx)["rulesRaw"];
        return ctx.body;
    }
};
