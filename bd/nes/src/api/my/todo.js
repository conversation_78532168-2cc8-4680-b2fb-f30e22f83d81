"use strict";
module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
    }
    check_parameters(user) {
        if (!(user.id && !isNaN(parseInt(user.id))))
            return { msg: "Invalid User Id", code: 400 };
        return false;
    }

    async todos(ctx, user) {
        const transaction = this.db.env.rw.transaction(ctx);
        const formName = "todo";

        if (ctx.request.method === "GET") {
            try {
                const todos = await this.form.get.get_form(ctx, user, "todo", {
                    filter: "user_id:" + user.id,
                });

                if (Array.isArray(todos) && todos.length > 0) {
                    return todos;
                } else {
                    return {};
                }
            } catch (e) {
                console.log("ERROR while getting todos ", e);
            }
        } else if (ctx.request.method === "PUT") {
            try {
                const data = ctx.request.body;
                await transaction.update(formName, data, data.id);
                const result = await transaction.commit();
                if (result.length > 0) {
                    data.id = result[0].id;
                }
                return data;
            } catch (e) {
                console.log("ERROR PUT ", e);
            }
        }
        return;
    }

    async process(ctx) {
        try {
            const user = ctx.user;
            const err = this.check_parameters(user);
            if (err) {
                ctx.status = err.code;
                ctx.body = { error: err.msg };
                return;
            }
            ctx.body = await this.todos(ctx, user);
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
