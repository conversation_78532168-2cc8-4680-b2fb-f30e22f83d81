"use strict";
const _ = require("lodash");
module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
    }
    check_parameters(user) {
        if (!(user.id && !isNaN(parseInt(user.id))))
            return { msg: "Invalid User Id", code: 400 };
        return false;
    }

    get_device_type(userAgent) {
        if (/mobile/i.test(userAgent)) {
            return "Mobile";
        } else if (/tablet/i.test(userAgent) || /iPad/i.test(userAgent)) {
            return "Tablet";
        } else {
            return "Desktop";
        }
    }

    update_printer(ctx, data, cp = {}) {
        const ip = ctx.request.header["fly-client-ip"];
        const dt = this.get_device_type(ctx.request.header["user-agent"]);
        const printers = cp.printers || [];
        const index = printers.findIndex(
            (p) => p.ip === ip && p.device_type === dt
        );
        if (index > -1) {
            if (data.document_printer) {
                printers[index].document_printer = data.document_printer;
            }
            if (data.label_printer) {
                printers[index].label_printer = data.label_printer;
            }
        } else {
            printers.push({
                ip: ip,
                device_type: dt,
                document_printer: data.document_printer || "",
                label_printer: data.label_printer || "",
            });
        }
        data.printers = printers;
        return data;
    }

    extractPrinterData(data) {
        const printerFields = [
            "document_printer",
            "cpr_report_printer",
            "cpr_form_printer",
            "pharmacy_po_label_printer",
            "pharmacy_work_order_printer",
            "delivery_ticket_printer",
            "labelsship_printer",
            "pharmacy_label_printer",
            "cms_1500_form_printer",
            "physician_cover_letter_printer",
            "syringe_label_printer",
            "pharmacy_tpn_label_printer",
            "verbal_order_printer",
        ];

        return Object.fromEntries(
            Object.entries(data).filter(([key]) => printerFields.includes(key))
        );
    }

    async preferences(ctx, user) {
        const transaction = this.db.env.rw.transaction(ctx);
        const formName = "user_preference";

        if (ctx.request.method === "GET") {
            try {
                const p = _.head(
                    await this.form.get.get_form(ctx, user, "user_preference", {
                        limit: 1,
                        filter: "user_id:" + user.id,
                    })
                );
                return p || {};
            } catch (e) {
                console.log("ERROR GET ", e);
            }
        } else if (ctx.request.method === "PUT") {
            try {
                let data = ctx.request.body;
                const userPreference = await this.form.get.get_form(
                    ctx,
                    user,
                    "user_preference",
                    { limit: 1, filter: "user_id:" + user.id }
                );

                const subformPrinters =
                    userPreference.length > 0
                        ? userPreference[0].subform_printers || []
                        : [];

                const newPrinterConfig = data?.subform_printers?.[0] || {};

                if (newPrinterConfig?.site_id) {
                    const siteIndex = subformPrinters.findIndex(
                        (p) => p.site_id === newPrinterConfig.site_id
                    );

                    if (siteIndex >= 0) {
                        subformPrinters[siteIndex] = {
                            ...subformPrinters[siteIndex],
                            ...this.extractPrinterData(newPrinterConfig),
                        };
                    } else {
                        subformPrinters.push({
                            site_id: newPrinterConfig.site_id,
                            ...this.extractPrinterData(newPrinterConfig),
                        });
                    }
                } else {
                    const defaultIndex = subformPrinters.findIndex(
                        (p) => p.site_id === null
                    );
                    const printerData =
                        this.extractPrinterData(newPrinterConfig);

                    if (defaultIndex >= 0) {
                        subformPrinters[defaultIndex] = {
                            ...subformPrinters[defaultIndex],
                            ...printerData,
                        };
                    } else {
                        if (Object.keys(printerData).length > 0) {
                            subformPrinters.push({
                                site_id: null,
                                ...printerData,
                            });
                        }
                    }
                }

                data = {
                    ...data,
                    subform_printers: subformPrinters,
                };

                if (userPreference.length > 0) {
                    await transaction.update(
                        formName,
                        data,
                        userPreference[0].id
                    );
                } else {
                    data["user_id"] = user.id;
                    await transaction.insert(formName, data);
                }
                const result = await transaction.commit();
                if (result.length > 0) {
                    data.id = result[0].id;
                }
                return data;
            } catch (e) {
                console.log("ERROR PUT ", e);
            }
        }
        return;
    }

    async process(ctx) {
        try {
            const user = ctx.user;
            const err = this.check_parameters(user);
            if (err) {
                ctx.status = err.code;
                ctx.body = { error: err.msg };
                return;
            }
            ctx.body = await this.preferences(ctx, user);
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
