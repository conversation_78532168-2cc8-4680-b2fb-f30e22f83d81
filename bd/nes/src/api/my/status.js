"use strict";

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.nescache = nes.modules.nescache;
    }

    async editing_forms(ctx) {
        const data = ctx.request.body;
        if (!(data && "key" in data && "forms" in data)) return null;

        // if not editing any longer, clear cache
        if (Object.keys(data.forms).length == 0) {
            // do not need to wait for this to finish
            this.nescache.unset(`editing_${data.key}`);
            return null;
        }

        // cache this browser tab's open forms
        const editing = {};
        editing[ctx.user.id] = data.forms;
        // do not need to wait for this to finish
        this.nescache.set(`editing_${data.key}`, editing, 600);

        // get all open forms for all users
        const all_editing = await this.nescache.get_wildcard("editing_", true);

        // create list of all conflicting forms and users
        const user_ids = {};
        const conflict = {};
        for (const edit of all_editing) {
            for (const user in edit.data) {
                if (user == ctx.user.id) continue; // cannot conflict with self

                const forms = edit.data[user];
                for (const form in forms) {
                    if (!(form in data.forms)) continue; // no conflict if user isn't editing the form at all

                    for (const id of forms[form]) {
                        if (!data.forms[form].includes(id)) continue; // no conflict if user isn't editing the same form record

                        if (!(form in conflict)) conflict[form] = {};
                        if (!(id in conflict[form])) conflict[form][id] = {};
                        if (!(user in conflict[form][id]))
                            conflict[form][id][user] = -1;
                        const updt = new Date(edit.updated_on).getTime();
                        conflict[form][id][user] = Math.max(
                            conflict[form][id][user],
                            updt
                        );
                        user_ids[user] = user;
                    }
                }
            }
        }

        let users = {};
        const user_id_list = Object.keys(user_ids).join(",");
        if (user_id_list) {
            const users_query = `SELECT id, firstname, lastname, auto_name AS name, image_url AS photo, external_authentication_id AS sso FROM form_user WHERE id in (${user_id_list}) AND (archived IS NULL OR archived = FALSE) AND (deleted IS NULL OR deleted = FALSE);`;
            users = await this.db.env.ro.query(users_query);
        }

        return { forms: conflict, users };
    }

    async process(ctx) {
        try {
            const my_status = ctx.my_status || {};
            my_status.editing = await this.editing_forms(ctx);
            ctx.body = my_status;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
