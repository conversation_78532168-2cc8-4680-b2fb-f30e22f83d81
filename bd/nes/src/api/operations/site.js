"use strict";
const moment = require("moment-timezone");

module.exports = class SiteClass {
    constructor(nes, ctx = null, siteId = null, siteData = null) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.taxRateData = null;
        this.timeZoneData = null;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.siteId = siteId;
        this.data = siteData;
        return this.fx.dynamicGet(this);
    }

    async init() {
        if (!this.siteId && !this.data) {
            return;
        }

        if (!this.data) {
            const siteRec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "site",
                { limit: 1, filter: "id:" + this.siteId }
            );
            if (!siteRec) {
                throw new Error("Site not found");
            }
            this.data = siteRec[0];
        }

        const timezoneRec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "list_timezone",
            { limit: 1, filter: "code:" + this.data.timezone_id }
        );
        if (!timezoneRec) {
            throw new Error("Timezone not found");
        }
        this.timeZoneData = timezoneRec[0];
        return this;
    }

    get rawData() {
        return this.data;
    }

    get timezone() {
        return this.timeZoneData.code;
    }

    get localdatestamp() {
        return moment.tz(this.timezone).format("MM/DD/YYYY");
    }

    get localtimestamp() {
        return moment.tz(this.timezone).format("MM/DD/YYYY hh:mm a");
    }
};
