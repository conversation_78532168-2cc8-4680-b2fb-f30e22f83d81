"use strict";
const _ = require("lodash");
module.exports = class ApiView {
    constructor(nes) {
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    async process(ctx, urlpath) {
        try {
            if (ctx.request.method == "GET") {
                const result = await this.fx.parallel({
                    Patient: this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "patient",
                        Object.assign({}, ctx.query)
                    ),
                    Inventory: this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "inventory",
                        Object.assign({}, ctx.query)
                    ),
                    Physician: this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "physician",
                        Object.assign({}, ctx.query)
                    ),
                    Sales: this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "sales_account",
                        Object.assign({}, ctx.query)
                    ),
                });
                ctx.status = 200;
                ctx.body = {
                    result,
                };
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }

    getQuery(tables, columns, where) {
        const filters = [`LIMIT 20`].join(" ");
        return (
            tables
                .map((table) => {
                    if (
                        this.shared.DSL[table].model[
                            this.shared.dslSearchColumn
                        ].length > 0 &&
                        tables.includes(table)
                    )
                        return `SELECT ${columns} FROM form_${table} WHERE ${where} ${filters};`;
                })
                .filter(Boolean)
                .join(",") + ";"
        );
    }
};
