"use strict";
const { PassThrough } = require("stream");
module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.pgboss = nes.modules.pgboss;
        this.dsl = nes.modules.dsl;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
    }

    async get_tables(ctx) {
        try {
            const syncModeMixedTables = [];
            for (const [tableName, cson] of Object.entries(
                this.dsl.shared.DSL
            )) {
                const syncMode = cson.model.sync_mode ?? "none";
                if (
                    syncMode === "mixed" &&
                    cson.fields.active &&
                    cson.fields.allow_sync
                ) {
                    syncModeMixedTables.push(`${tableName}`);
                }
            }
            return syncModeMixedTables;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }

    handle_query(ctx, formData) {
        formData = formData.filter((e) => e.create_view === "Yes");
        if (formData.length < 1) return;
        for (const view of formData) {
            const code = view.code;
            const viewName = "view_" + code;
            const queueName = "view_refresh_query";
            const jdata = {
                view: viewName,
            };
            const cronLine = this.pgboss.getCron(view.refresh || 1);

            ctx.body.write(`DROP MATERIALIZED VIEW IF EXISTS ${viewName};\n`);
            ctx.body.write(
                `CREATE MATERIALIZED VIEW IF NOT EXISTS ${viewName} AS ${view.report_sql};\n`
            );
            ctx.body.write(
                `SELECT pgboss.create_queue('${queueName}'::text, '{"policy":"standard"}'::json);\n`
            );
            ctx.body.write(
                `INSERT INTO pgboss.schedule (name,cron,data,options,timezone) VALUES ('${queueName}','${cronLine}', '${JSON.stringify(jdata)}','{}','UTC') ON CONFLICT (name) DO UPDATE SET "cron" = EXCLUDED."cron";\n`
            );
        }
    }
    async get_upsert_query(ctx, formName) {
        ctx.body.write("BEGIN;\n");
        try {
            // *_auto_name,_meta, created_by updated_by -> eadmin id for id,
            const keysToExclude = ["_meta", "search", "expose_api"];
            const skipColumnsOnUpdate = ["id", "allow_sync", "active"];
            const syncModeMixedstartId =
                this.shared.config.limits.sync_mode.mix_start_id;
            // Needs escaping
            const sql = `SELECT * FROM form_${formName} WHERE id < ${syncModeMixedstartId} AND allow_sync = 'Yes' AND (archived IS NULL OR archived = FALSE) ORDER BY id;`;
            let formdata = await this.db.env.ro.query(sql);
            if (formdata.length < 1) {
                ctx.body.write("COMMIT;");
                ctx.body.end();
                return;
            }
            formdata = formdata.map((record) => {
                for (const key of Object.keys(record)) {
                    if (
                        key.includes("_auto_name") ||
                        keysToExclude.includes(key) ||
                        !Object.keys(this.shared.DSL[formName].fields).includes(
                            key
                        )
                    )
                        delete record[key];
                    if (["created_by", "updated_by"].includes(key))
                        record[key] = this.auth.ADMIN_USER.id;
                }
                return record;
            });

            const columns = Object.keys(formdata[0]);
            let updateValues = [...columns];
            updateValues = updateValues.filter(
                (key) => !skipColumnsOnUpdate.includes(key)
            );
            updateValues = updateValues
                .map((column) => {
                    return `"${column}" = EXCLUDED."${column}"`;
                })
                .join(", ");
            formdata.map((record) => {
                const values =
                    "(" +
                    Object.values(record)
                        .map((value) => {
                            return this.fx.serialize(value);
                        })
                        .join(",") +
                    ")";
                ctx.body.write(
                    `INSERT INTO form_${formName} ("${columns.join('", "')}") VALUES ${values} ON CONFLICT (id) DO UPDATE SET ${updateValues} WHERE form_${formName}.allow_sync = 'Yes';\n`
                );
            });
            if (typeof this["handle_" + formName] === "function")
                this["handle_" + formName](ctx, formdata);
            ctx.body.write("COMMIT;\n");
            ctx.body.end();
        } catch (e) {
            throw new Error(e);
        }
    }
    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            if (
                !user ||
                !("role" in user) ||
                !this.auth.ADMIN_INTERNAL.includes(user.role)
            ) {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            } else {
                const formName = urlpath.path[3];
                let queries = "";
                if (formName) {
                    if (
                        this.shared.DSL[formName] &&
                        !!this.shared.DSL[formName].model.sync_mode &&
                        this.shared.DSL[formName].model.sync_mode === "mixed"
                    ) {
                        ctx.status = 200;
                        ctx.type = "text/plain";
                        const stream = new PassThrough();
                        ctx.body = stream;
                        await this.get_upsert_query(ctx, formName);
                    } else {
                        throw new Error(
                            `form ${formName} is not in DSL or is not sync mode mixed`
                        );
                    }
                } else {
                    queries = await this.get_tables(ctx);
                    ctx.body = queries;
                    ctx.status = 200;
                }
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
