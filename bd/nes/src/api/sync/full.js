"use strict";
const { PassThrough } = require("stream");

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.insertLength = 1000;
    }

    async get_upsert_query(ctx, formName) {
        ctx.body.write("BEGIN;\n");
        try {
            const keysToExclude = ["_meta", "sys_period", "search"];
            const sql = `SELECT * FROM form_${formName} ORDER BY id;`;
            let formdata = await this.db.env.ro.query(sql);
            if (formdata.length < 1) {
                ctx.body.write("COMMIT;");
                ctx.body.end();
                return;
            }
            formdata = formdata.map((record) => {
                for (const key of Object.keys(record)) {
                    if (
                        key.includes("_auto_name") ||
                        keysToExclude.includes(key)
                    )
                        delete record[key];
                    // TODO only updated by if updated by is not null
                    if (["created_by", "updated_by"].includes(key))
                        record[key] = this.auth.ADMIN_USER.id;
                }
                return record;
            });
            const columns = Object.keys(formdata[0]);
            ctx.body.write(
                `SET session_replication_role = replica;\nDELETE FROM form_${formName}`
            );
            let n = 0;
            formdata.map((record) => {
                const values =
                    "(" +
                    Object.values(record)
                        .map((value) => {
                            return this.fx.serialize(value);
                        })
                        .join(",") +
                    ")";
                if (n === 0) {
                    ctx.body.write(
                        `;\nINSERT INTO form_${formName} ("${columns.join('", "')}") VALUES\n${values}`
                    );
                    n++;
                } else {
                    ctx.body.write(",\n" + values);
                    if (n < this.insertLength) n++;
                    else n = 0;
                }
            });
            ctx.body.write(
                `;\nSET session_replication_role = DEFAULT;\nCOMMIT;\n`
            );
            ctx.body.end();
        } catch (e) {
            console.error(e);
        }
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            if (
                !user ||
                !("role" in user) ||
                !this.auth.ADMIN_INTERNAL.includes(user.role)
            ) {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            } else {
                if (!urlpath.path[3]) {
                    throw new Error("Form Name cannot be empty");
                }
                const formName = urlpath.path[3];
                if (this.shared.DSL[formName]) {
                    ctx.status = 200;
                    ctx.type = "text/plain";
                    const stream = new PassThrough();
                    ctx.body = stream;
                    stream.on("error", (ctx) => {
                        ctx.status = 500;
                    });
                    this.get_upsert_query(ctx, formName);
                } else {
                    throw new Error(`form ${formName} is not in DSL`);
                }
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
