/*jshint : 6 */
"use strict";
const _ = require("lodash");

const {
    <PERSON><PERSON><PERSON><PERSON>,
    editAction<PERSON>uttonWrapper,
    ButtonStyle,
    ActionResponseWrappers,
    DisplayType,
    CallbackType,
} = require("@actions");
const {
    InventoryActions,
    InventoryActionKeys,
} = require("@inventory/settings");

/**
 * @class
 * @classdesc Class responsible for running inventory related actions
 */
module.exports = class InventoryHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    async postAction(ctx, form, id, action, _params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );
        try {
            const selectedPo = ctx.request.body;
            if (!selectedPo) {
                return ActionResponseWrappers.error("Missing PO Selection");
            }
            const poId = selectedPo[0]?.po_id || null;
            if (!poId) {
                return ActionResponseWrappers.error(
                    "Missing PO ID from selection"
                );
            }
            const DispenseFetcherClass = require("@dispense/fetcher");
            const fetcher = new DispenseFetcherClass(this.nes, ctx);
            const poPresets = await this.__fetchPOPresets(fetcher, poId);
            return ActionResponseWrappers.create(
                poPresets,
                "po",
                null,
                null,
                null,
                null,
                null,
                DisplayType.TAB
            );
        } catch (e) {
            const errorMessage = `Error encountered while processing ${form} post action.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Handles an action for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string|number} id - The ID of the form entry.
     * @param {string} action - The action to be performed on the form.
     * @returns {Promise<Object>} The result of the action performed.
     * @throws {Error} If an error occurs during the action processing.
     */
    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            if (!id) {
                return ActionResponseWrappers.error(
                    "Cannot run inventory action, missing record ID"
                );
            }

            return await this.__handleReorderAction(ctx, id);
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run ${action} action for ${form} id ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Retrieves available actions for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The form name.
     * @param {string|number} id - The ID of the form.
     * @returns {Promise<Array>} An array of available actions for the form.
     */
    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            if (!id) {
                return { actions: [], warning: null };
            }

            const filters = [`inventory_id:${id}`, `transaction_type:Purchase`];
            const ledgerInventoryRec = _.head(
                await this.nes.shared.form.get.get_form(
                    ctx,
                    ctx.user,
                    "ledger_inventory",
                    { limit: 1, filter: filters }
                )
            );
            const availableActions = [];
            if (ledgerInventoryRec) {
                const label = InventoryActions.REORDER;
                const action = InventoryActionKeys[label];
                const callbackUrl = `/api/form/inventory/${id}?perform_action=${action}`;
                availableActions.push(
                    editActionButtonWrapper(
                        label,
                        action,
                        callbackUrl,
                        CallbackType.GET,
                        ButtonStyle.ACCEPT
                    )
                );
            }
            return { actions: availableActions, warning: null };
        } catch (e) {
            const errorMessage = `Error encountered while checking available ${form} actions.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Handles the reorder action for a specific inventory item.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string|number} inventoryId - The ID of the inventory item to reorder.
     * @returns {Promise<Object>} The result of the reorder action.
     * @throws {Error} If there's an error during the reorder process.
     */
    async __handleReorderAction(ctx, inventoryId) {
        console.log(`Handling reorder action for inventory ID ${inventoryId}`);
        try {
            const DispenseFetcherClass = require("@dispense/fetcher");
            const fetcher = new DispenseFetcherClass(this.nes, ctx);
            const queryCode = "inventory_suppliers";
            const queryRec = await fetcher.fetchCachedRecord(
                "query",
                queryCode,
                false,
                true
            );
            if (!queryRec) {
                throw this.fx.getClaraError(
                    `Unable to find suppliers query ${queryCode}`
                );
            }

            const rows = await this.db.env.rw.query(queryRec.report_sql);
            if (rows.length === 0) {
                throw this.fx.getClaraError(
                    `Unable to find any POs for associated inventory item.`
                );
            }
            if (rows.length === 1) {
                const poId = rows[0].po_id;
                const poPresets = await this.__fetchPOPresets(fetcher, poId);
                return ActionResponseWrappers.create(
                    poPresets,
                    "po",
                    null,
                    null,
                    null,
                    null,
                    null,
                    DisplayType.TAB
                );
            }

            const label = InventoryActions.REORDER;
            const action = InventoryActionKeys[label];
            const callbackUrl = `/api/form/inventory/${inventoryId}/action/${action}`;

            return ActionResponseWrappers.popup(
                {
                    label: "Reorder",
                    form: "inventory",
                    sections: ["Reorder"],
                    preset: {
                        form_id: inventoryId,
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Save",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run reorder action for inventory ID ${inventoryId}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches presets for a Purchase Order (PO) based on the given PO ID.
     * @param {Object} fetcher - The fetcher object used to retrieve cached records.
     * @param {string|number} poId - The ID of the Purchase Order to fetch presets for.
     * @returns {Promise<Object>} A promise that resolves to an object containing PO presets.
     * @throws {Error} If the PO record cannot be found or if an error occurs during the fetch process.
     */
    async __fetchPOPresets(fetcher, poId) {
        console.log(`Fetching PO presets for PO ID ${poId}`);
        try {
            const poRec = await fetcher.fetchCachedRecord("po", poId);
            if (!poRec) {
                throw this.fx.getClaraError(
                    `Unable to find PO record for PO ID ${poId}. Please verify record isn't archived or deleted.`
                );
            }
            const presets = _.pick(poRec, [
                "site_id",
                "supplier_id",
                "reorder_no",
            ]);
            const orderedItems = poRec.subform_items || [];
            const orderItemPresets = orderedItems.map((item) => {
                const orderItemPreset = _.pick(item, [
                    "inventory_id",
                    "dispense_unit",
                    "qty_pkg",
                    "ordered",
                ]);
                return orderItemPreset;
            });

            return {
                ...presets,
                subform_items: orderItemPresets,
            };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to fetch PO presets for PO ID ${poId}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
