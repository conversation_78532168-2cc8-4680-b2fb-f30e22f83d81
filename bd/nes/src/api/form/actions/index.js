"use strict";
const fs = require("fs").promises;
const path = require("path");

/**
 * @class ActionHandler
 * @description Just add your action handlers here in the same folder with the name of the form
 * as the filename. Extend the ActionHandler class and implement the getActions and runAction methods using
 * the provided settings below. You can view any of the already implemented handlers for reference.
 */
class ActionHandler {
    constructor(nes) {
        this.nes = nes;
        this.fx = nes.modules.fx;
    }

    async postAction(_ctx, _form, _id, _action, _params) {
        return { error: "Not implemented" };
    }

    // Return an array of action buttons and possbile warning message (see settings/ButtonSchema)
    async getActions(_ctx, _form, _id) {
        return { actions: [], warning: null };
    }

    // Perform the action, see ActionResponseWrappers for return types from this action
    async runAction(_ctx, _form, _id, _action) {
        return { error: "Not implemented" };
    }
}

class FormActionsHandlerClass {
    constructor(nes) {
        this.nes = nes;
    }

    static register(ActionHandlerClass, nes, form) {
        console.log(`      .${form}`);
        if (!(ActionHandlerClass.prototype instanceof ActionHandler)) {
            throw new Error(
                `${ActionHandlerClass.name} must be a subclass of ActionHandler`
            );
        }
        nes.actionHandlers[form] = new ActionHandlerClass(nes);
    }

    async getActions(ctx, form, id = null) {
        const handler = this.nes.actionHandlers[form];
        if (!handler) {
            return null;
        }
        try {
            return await handler.getActions(ctx, form, id);
        } catch (error) {
            const errorMessage = `Error getting actions for ${form}.`;
            console.error(
                `${errorMessage} Error: ${error} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(error, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        const handler = this.nes.actionHandlers[form];
        if (!handler) {
            return null;
        }
        try {
            return await handler.runAction(ctx, form, id, action);
        } catch (error) {
            const errorMessage = `Error running action ${action} for ${form}.`;
            console.error(
                `${errorMessage} Error: ${error} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(error, errorMessage);
        }
    }
}

async function registerActionHandlers(nes) {
    nes.actionHandlers = {};
    const actionHandlers = await fs.readdir(__dirname);

    for (const file of actionHandlers) {
        if (file !== "index.js" && file.endsWith(".js")) {
            const fileName = path.basename(file, ".js");
            console.log(`    actions/${file}`);
            const ActionHandlerClass = require(path.join(__dirname, file));
            FormActionsHandlerClass.register(ActionHandlerClass, nes, fileName);
        }
    }
}

const ButtonType = {
    EDIT: "edit",
    VIEW: "view",
    LIST: "list",
};

// Buttons should show disabled until all requirements are met
const ButtonActionRequirements = {
    NONE: "none",
    ALL_REQUIRED: "required",
};

const ButtonStyle = {
    DEFAULT: "default", // white with blackish text
    INFO: "info", // blue with white text
    WARNING: "warning", // orange with blackish text
    PRIMARY: "primary", // purple with white text
    ACCEPT: "accept", // green with white text
    CANCEL: "error", // red with white text
};

const ButtonIcons = {
    ARCHIVE: "archive",
    PRINT: "print",
    EDIT: "edit",
    SAVE: "save",
    MORE: "more",
    CANCEL: "cancel",
    ADD: "add",
    VERIFY: "verify",
    SECTIONS: "sections",
    EDIT_OUTLINE: "editOutline",
    PLUS_OUTLINE: "plusOutline",
    ARCHIVE_OUTLINE: "archiveOutline",
    REJECT: "fa-solid fa-thumbs-down",
    APPROVE: "fa-solid fa-thumbs-up",
    COMPOUND: "fa-solid fa-mortar-pestle",
    NOTES: "fa-solid fa-memo-pad",
    WARNING: "fa-solid fa-diamond-exclamation",
    SHIPMENT: "fa-solid fa-truck",
    INVOICE: "fa-solid fa-file-invoice-dollar",
    REJECT_CLAIM: "fa-solid fa-shield-exclamation",
    PAID_CLAIM: "fa-solid fa-shield-check",
    SCHEDULE: "fa-solid fa-calendar",
    REPORT: "fa-solid fa-chart-scatter",
    VOMIT: "fa-solid fa-face-vomit",
    DISEASE: "fa-solid fa-virus",
    VIAL: "fa-solid fa-vial",
    PILLS: "fa-solid fa-pills",
    NURSE: "fa-solid fa-user-nurse-hair-long",
    PRESCRIPTION_FILE: "fa-solid fa-clipboard-prescription",
    RECEIPT: "fa-solid fa-receipt",
    BOX: "fa-solid fa-box-open",
    DENY: "fa-solid fa-thumbs-down",
    CHECK: "fa-solid fa-circle-check",
    RESEND: "fa-solid fa-paper-plane-top",
    REFILL: "fa-solid fa-arrows-rotate",
    FOLLOWUP: "fa-solid fa-message-exclamation",
    PA: "fa-solid fa-shield-check",
    CLARIFICATION: "fa-solid fa-comments-question-check",
    CHANGE: "fa-solid fa-swap-arrows",
    PROVIDER: "fa-solid fa-user-doctor",
    APPROVE_CANCEL: "fa-solid fa-thumbs-up",
    DENY_CANCEL: "fa-solid fa-thumbs-down",
    MARK_REVIEWED: "fa-solid fa-badge-check",
};

const DisplayType = {
    MODAL: "modal",
    TAB: "tab",
};

const CallbackType = {
    PUT: "PUT",
    POST: "POST",
    GET: "GET",
    NONE: "NONE",
};

const ConfirmationDialogType = {
    YESNO: "yesno",
    OKCANCEL: "okcancel",
    CONFIRMCANCEL: "confirmcancel",
};

const OverrideButtons = {
    SPECIAL_EVENT: "special_event",
    SPECIAL_EVENT_1: "special_event_1",
    SPECIAL_EVENT_2: "special_event_2",
    SPECIAL_EVENT_3: "special_event_3",
    PRINT: "print",
    SAVE: "save",
    EDIT: "edit",
    VERIFY: "verify",
    ARCHIVE: "archive",
    REVIEW: "review",
    CANCEL: "cancel",
    UNARCHIVE: "unarchive",
};

const ButtonSchema = {
    requirements: ButtonActionRequirements,
    type: ButtonType,
    label: "Button Label",
    action: "action_name",
    icon: "icon_name", //Optional icon name
    class: "class-name", //Optional for style overrides
    style: ButtonStyle,
    callback_url: "callback_url", //Override for action callback url
    show_loading_spinner: false, //Override to show spinner on button click
    loading_spinner_text: "Loading...", //Override to show spinner text on button click
};

const overrideButtonWrapper = (
    label,
    override = OverrideButtons.SPECIAL_EVENT,
    style = ButtonStyle.DEFAULT,
    type = ButtonType.VIEW,
    path = ""
) => {
    return {
        type: type,
        label: label,
        action: override,
        style: style,
        path: path,
    };
};

const iconActionButtonWrapper = (
    label,
    action,
    icon,
    style = ButtonStyle.DEFAULT,
    type = ButtonType.VIEW,
    path = ""
) => {
    return {
        type: type,
        label: label,
        action: action,
        icon: icon,
        style: style,
        path: path,
    };
};

const actionButtonWrapper = ({
    label,
    action,
    style = ButtonStyle.DEFAULT,
    type = ButtonType.VIEW,
    showLoadingSpinner = false,
    loadingSpinnerText = null,
    confirmationDialog = false,
    confirmationDialogMessage = null,
    confirmationDialogTitle = null,
    confirmationDialogType = ConfirmationDialogType.YESNO,
    path = "",
} = {}) => {
    return {
        type: type,
        label: label,
        action: action,
        style: style,
        show_loading_spinner: showLoadingSpinner,
        loading_spinner_text: loadingSpinnerText,
        confirmation_dialog: confirmationDialog,
        confirmation_dialog_message: confirmationDialogMessage,
        confirmation_dialog_title: confirmationDialogTitle,
        confirmation_dialog_type: confirmationDialogType,
        path: path,
    };
};

const listActionButtonWrapper = ({
    label,
    form,
    link,
    linkid,
    preset = {},
    canAdd = true,
    style = ButtonStyle.DEFAULT,
    sort = "id",
    sortDirection = "desc",
    filters = {},
    type = ButtonType.LIST,
    path = "",
} = {}) => {
    return {
        label,
        preset,
        type,
        style,
        form,
        canAdd: canAdd,
        filtersPresetFixed: filters,
        action:
            "list_" +
            form +
            "_" +
            (label || "").toLowerCase().replaceAll(" ", "_"),
        sortProperty: sort,
        sortDirection: sortDirection,
        linkMap: {
            link,
            links: Object.keys(linkid),
            linkid,
        },
        path: path,
    };
};

const editIconActionButtonWrapper = ({
    label,
    action,
    icon,
    callback_url,
    callback_type = CallbackType.GET,
    style = ButtonStyle.DEFAULT,
    requirements = ButtonActionRequirements.ALL_REQUIRED,
    path = "",
} = {}) => {
    return {
        requirements: requirements,
        type: ButtonType.EDIT,
        label: label,
        action: action,
        icon: icon,
        style: style,
        callback_url: callback_url,
        callback_type: callback_type,
        path: path,
    };
};

const editActionButtonWrapper = ({
    label,
    action,
    callback_url,
    callback_type = CallbackType.GET,
    style = ButtonStyle.DEFAULT,
    requirements = ButtonActionRequirements.ALL_REQUIRED,
    showLoadingSpinner = false,
    loadingSpinnerText = null,
    confirmationDialog = false,
    confirmationDialogMessage = null,
    confirmationDialogTitle = null,
    confirmationDialogType = ConfirmationDialogType.YESNO,
    path = "",
} = {}) => {
    return {
        requirements: requirements,
        type: ButtonType.EDIT,
        label: label,
        action: action,
        style: style,
        callback_url: callback_url,
        callback_type: callback_type,
        show_loading_spinner: showLoadingSpinner,
        loading_spinner_text: loadingSpinnerText,
        confirmation_dialog: confirmationDialog,
        confirmation_dialog_message: confirmationDialogMessage,
        confirmation_dialog_title: confirmationDialogTitle,
        confirmation_dialog_type: confirmationDialogType,
        path: path,
    };
};

const PopupSchema = {
    label: "Popup Label",
    form: "form_name",
    preset: {},
    sections: ["section_name"],
    btnLabels: {
        cancel: "Close",
        save: "Done",
    },
};

const NextAction = {
    CLOSE: "close",
    REFRESH: "refresh",
    NONE: "none",
};

const ActionResponseWrappers = {
    // Toolbar action only, should preset the form with the given data
    preset: (data) => {
        return {
            preset: data,
        };
    },
    // Client should open the form in readonly mode with the given data
    view: (data, form, display = DisplayType.MODAL) => {
        return {
            view: { [form]: Array.isArray(data) ? data : [data] },
            display, // How to display the form, in a tab or a modal view
        };
    },
    // Client should open the form for editing with the given data
    edit: (
        formId,
        form,
        preset = {},
        callbackUrl = null,
        errorMessage = null,
        errorTitle = null,
        successMessage = null,
        errors = null,
        display = DisplayType.MODAL,
        nextAction = null
    ) => {
        if (typeof preset !== "object" || preset === null) {
            throw new Error("preset must be an object");
        }
        if (!formId) {
            throw new Error("Form id must be present for edit mode");
        }
        return {
            edit: {
                [form]: {
                    id: formId,
                    preset: preset,
                },
            },
            callback_url: callbackUrl, // Should be a PUT back to this URL if set
            error_message: errorMessage, // Optional, show error message in a popup
            error_title: errorTitle, // Optional, show error title in a popup
            success_message: successMessage, // Optional, show message in a popup
            validation_errors: errors, // Optional, show validation errors in a popup
            display, // How to display the form, in a tab or a modal view
            next_action: nextAction,
        };
    },
    edit_open_form: (preset, callbackUrl = null) => {
        return {
            run_function: {
                name: "editWithPreset",
                options: {
                    preset: preset,
                },
            },
            callback_url: callbackUrl,
        };
    },
    validation_errors: (errors) => {
        return {
            validation_errors: errors,
        };
    },
    // Client should open the form and prefill in with the prefill data
    create: (
        preset,
        form,
        callback_url = null,
        errorMessage = null,
        errorTitle = null,
        callback_function = null,
        function_options = {},
        display = DisplayType.MODAL,
        nextAction = NextAction.REFRESH,
        hideCardMenu = false
    ) => {
        if (typeof preset !== "object" || preset === null) {
            throw new Error("preset must be an object");
        }
        if (callback_url && callback_function) {
            throw new Error(
                "callback_url and callback_function cannot both be set"
            );
        }
        return {
            create: {
                [form]: {
                    preset: preset,
                },
            },
            callback_url: callback_url, // Should be a POST back to this URL if set
            type: callback_function ? "func" : "url",
            func: {
                name: callback_function,
                options: function_options,
            },
            error_message: errorMessage, // Optional, show error message in a popup
            error_title: errorTitle, // Optional, show error title in a popup
            display, // How to display the form, in a tab or a modal view
            next_action: nextAction,
            hideCardMenu: hideCardMenu,
        };
    },
    // Client should show the popup using let data = await openPartialFormFillPopup(popupSettings)
    // and then return the data in POST request to the callback_url
    popup: (
        PopupSchema,
        callback_url = null,
        return_parent = false,
        callback_type = CallbackType.POST,
        readonly = false,
        suppress_save = false,
        next_action = null
    ) => {
        return {
            popup: PopupSchema,
            callback_url: callback_url, // Should be a POST back to this URL
            return_parent: return_parent,
            callback_type: callback_type,
            readonly: readonly,
            suppress_save: suppress_save,
            next_action: next_action,
        };
    },
    // Client should show the error message in "error" response in a popup
    error: (error, nextAction = NextAction.REFRESH) => {
        return {
            next_action: nextAction,
            error: Array.isArray(error) ? error.join(", ") : error,
        };
    },
    // Client should show the success message in "success" response in a popup or if no message, just close the current form
    success: (message, nextAction = NextAction.REFRESH) => {
        return {
            next_action: nextAction,
            success: message,
        };
    },
    // Client should call CRPrint.print_backend to print the given URL
    print: (url) => {
        return {
            print: url,
        };
    },

    // Client should open the given component
    open: (func) => {
        return {
            load: func,
        };
    },
    runFunction: (
        callback_function = null,
        function_options = {},
        callback_url = null
    ) => {
        return {
            run_function: {
                name: callback_function,
                options: function_options,
                callback_url: callback_url,
            },
        };
    },
};

module.exports = {
    registerActionHandlers,
    actionButtonWrapper,
    overrideButtonWrapper,
    editActionButtonWrapper,
    iconActionButtonWrapper,
    editIconActionButtonWrapper,
    listActionButtonWrapper,
    FormActionsHandlerClass,
    ActionHandler,
    DisplayType,
    NextAction,
    ButtonType,
    CallbackType,
    ButtonStyle,
    ButtonIcons,
    ButtonSchema,
    PopupSchema,
    ButtonActionRequirements,
    ActionResponseWrappers,
    ConfirmationDialogType,
};
