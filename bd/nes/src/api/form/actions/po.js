/*jshint : 6 */
"use strict";
const _ = require("lodash");

const {
    <PERSON><PERSON><PERSON><PERSON>,
    action<PERSON>utton<PERSON>rapper,
    ButtonStyle,
    ActionResponseWrappers,
    DisplayType,
} = require("@actions");
const {
    PurchaseOrderActions,
    PurchaseOrderActionKeys,
} = require("@inventory/settings");

/**
 * @class
 * @classdesc Class responsible for running purchase order related actions
 */
module.exports = class PurchaseOrderHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    /**
     * Handles an action for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string|number} id - The ID of the form entry.
     * @param {string} action - The action to be performed on the form.
     * @returns {Promise<Object>} The result of the action performed.
     * @throws {Error} If an error occurs during the action processing.
     */
    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            if (!id) {
                return ActionResponseWrappers.error("No PO record found");
            }
            const poAction = _.invert(PurchaseOrderActionKeys)[action];
            const poRec = _.head(
                await this.nes.shared.form.get.get_form(ctx, ctx.user, "po", {
                    filter: "id:" + id,
                })
            );
            if (!poRec) {
                return ActionResponseWrappers.error(
                    "No PO record found. Please verify PO isn't archived or deleted."
                );
            }
            switch (poAction) {
                case PurchaseOrderActions.RECEIVE: {
                    const poReceipt = _.head(
                        await this.nes.shared.form.get.get_form(
                            ctx,
                            ctx.user,
                            "receipt_po",
                            { filter: "po_id:" + id }
                        )
                    );
                    if (poReceipt || poRec.status_id === "RVD") {
                        return ActionResponseWrappers.error(
                            `PO already received`
                        );
                    }
                    return ActionResponseWrappers.create(
                        {
                            po_id: parseInt(id),
                            site_id: parseInt(poRec.site_id),
                        },
                        "view_inventory_lots",
                        null,
                        null,
                        null,
                        null,
                        null,
                        DisplayType.TAB
                    );
                }
                case PurchaseOrderActions.REORDER: {
                    return ActionResponseWrappers.create(
                        {
                            reorder_po_id: parseInt(id),
                        },
                        "po",
                        null,
                        null,
                        null,
                        null,
                        null,
                        DisplayType.TAB
                    );
                }
                default:
                    throw new Error(`Unsupported action: ${action}`);
            }
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run ${action} action for ${form} id ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Retrieves available actions for a purchase order.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The form name.
     * @param {string|number} id - The ID of the form.
     * @returns {Promise<Array>} An array of available actions for the form.
     */
    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const availableActions = [];
            if (!id) {
                return { actions: availableActions, warning: null };
            }
            const [poReceipt, poRec] = await Promise.all([
                _.head(
                    await this.nes.shared.form.get.get_form(
                        ctx,
                        ctx.user,
                        "receipt_po",
                        { filter: "po_id:" + id }
                    )
                ),
                _.head(
                    await this.nes.shared.form.get.get_form(
                        ctx,
                        ctx.user,
                        "po",
                        {
                            filter: "id:" + id,
                        }
                    )
                ),
            ]);
            if (!poReceipt && poRec.status_id !== "RVD") {
                const label = PurchaseOrderActions.RECEIVE;
                const action = PurchaseOrderActionKeys[label];
                const params = {
                    label,
                    action,
                    style: ButtonStyle.ACCEPT,
                    path: "/po/receive",
                };
                availableActions.push(actionButtonWrapper(params));
            } else {
                const label = PurchaseOrderActions.REORDER;
                const action = PurchaseOrderActionKeys[label];
                const params = {
                    label,
                    action,
                    style: ButtonStyle.ACCEPT,
                    path: "/po/reorder",
                };
                availableActions.push(actionButtonWrapper(params));
            }

            return { actions: availableActions, warning: null };
        } catch (e) {
            const errorMessage = `Error encountered while checking available ${form} actions.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }
};
