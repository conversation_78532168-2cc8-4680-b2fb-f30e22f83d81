/*jshint : 6 */
"use strict";
const _ = require("lodash");

const {
    <PERSON><PERSON><PERSON><PERSON>,
    editAction<PERSON>uttonWrapper,
    listActionButtonWrapper,
    ButtonStyle,
    ActionResponseWrappers,
    ButtonActionRequirements,
    CallbackType,
} = require("@actions");
const {
    PatientAssistanceActions,
    PatientAssistanceActionKeys,
} = require("@dispense/settings");

/**
 * @class
 * @classdesc Class responsible for running order BV related actions
 */
module.exports = class PatientAssistanceHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    /**
     * Handles an action for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string|number} id - The ID of the form entry.
     * @param {string} action - The action to be performed on the form.
     * @returns {Promise<Object>} The result of the action performed.
     * @throws {Error} If an error occurs during the action processing.
     */
    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const bvAction = _.invert(PatientAssistanceActionKeys)[action];

            switch (bvAction) {
                case PatientAssistanceActions.ADD_PAYER: {
                    return ActionResponseWrappers.runFunction(
                        "patientAssistanceAddPayer"
                    );
                }
                case PatientAssistanceActions.ADD_INSURANCE: {
                    return ActionResponseWrappers.runFunction(
                        "patientAssistanceAddInsurance"
                    );
                }
                default:
                    throw new Error(`Unsupported action: ${bvAction}`);
            }
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run ${action} action for ${form} id ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Retrieves available actions for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The form name.
     * @param {string|number} id - The ID of the form.
     * @returns {Promise<Array>} An array of available actions for the form.
     */
    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const availableActions = [];
            let label = PatientAssistanceActions.ADD_PAYER;
            let action = PatientAssistanceActionKeys[label];
            let callbackUrl = `/api/form/patient_assistance/${id}?perform_action=${action}`;
            availableActions.push(
                editActionButtonWrapper(
                    label,
                    action,
                    callbackUrl,
                    CallbackType.GET,
                    ButtonStyle.ACCEPT
                )
            );
            label = PatientAssistanceActions.ADD_INSURANCE;
            action = PatientAssistanceActionKeys[label];
            callbackUrl = `/api/form/patient_assistance/${id}?perform_action=${action}`;
            availableActions.push(
                editActionButtonWrapper(
                    label,
                    action,
                    callbackUrl,
                    CallbackType.GET,
                    ButtonStyle.ACCEPT,
                    ButtonActionRequirements.NONE
                )
            );
            label = PatientAssistanceActions.INTERVENTIONS;
            action = PatientAssistanceActionKeys[label];
            const usePatientExperience =
                this.shared.config.company.use_pt_experience_team === "Yes";
            const primaryInterventionType = usePatientExperience
                ? this.shared.config.company.px_inter_primary_type
                : this.shared.config.company.intake_inter_primary_type;
            const interventionTypeFilter = usePatientExperience
                ? this.shared.config.company.px_inter_types
                : this.shared.config.company.intake_inter_types;
            availableActions.push(
                listActionButtonWrapper(
                    label,
                    "patient_event",
                    "patient",
                    {},
                    {
                        category: "Intervention",
                        intr_typ_ftr: interventionTypeFilter || [],
                        intervention_type: primaryInterventionType,
                    },
                    true,
                    ButtonStyle.WARNING,
                    "id",
                    "desc"
                )
            );
            return { actions: availableActions, warning: null };
        } catch (e) {
            const errorMessage = `Error encountered while checking available ${form} actions.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }
};
