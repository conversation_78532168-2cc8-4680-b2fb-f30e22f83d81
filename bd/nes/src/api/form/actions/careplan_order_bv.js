/*jshint : 6 */
"use strict";
const _ = require("lodash");

const {
    <PERSON><PERSON><PERSON><PERSON>,
    editActionButtonWrapper,
    listActionButtonWrapper,
    ButtonStyle,
    ActionResponseWrappers,
    DisplayType,
    ButtonActionRequirements,
    CallbackType,
} = require("@actions");
const { OrderBVActions, OrderBVActionKeys } = require("@dispense/settings");

/**
 * @class
 * @classdesc Class responsible for running order BV related actions
 */
module.exports = class OrderBVHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    async postAction(ctx, form, id, action, _params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );
        try {
            const bvAction = _.invert(OrderBVActionKeys)[action];
            const recordData = ctx.request.body;
            switch (bvAction) {
                case OrderBVActions.ADD_PAYER: {
                    let formId = recordData?.id || null;

                    if (!formId) {
                        const transaction = this.db.env.rw.transaction(
                            this.ctx
                        );
                        await transaction.insert("payer", recordData, ctx.user);
                        const res = await transaction.commit(this.ctx);
                        if (res.error) throw this.fx.getClaraError(res.message);
                        transaction.init();

                        const form = "payer";
                        const formIds =
                            this.fx.fetchFormIdsFromTransactionResults(
                                form,
                                res
                            );
                        formId = formIds[0];
                    }
                    const insuranceAction =
                        OrderBVActionKeys[OrderBVActions.ADD_INSURANCE];
                    const callbackUrl = `/api/form/careplan_order_bv/${id}/action/${insuranceAction}`;

                    return ActionResponseWrappers.create(
                        { payer_id: formId },
                        "patient_insurance",
                        callbackUrl,
                        null,
                        null,
                        null,
                        null,
                        null,
                        DisplayType.TAB
                    );
                }
                case OrderBVActions.ADD_INSURANCE: {
                    let formId = recordData?.id || null;

                    if (!formId) {
                        const transaction = this.db.env.rw.transaction(
                            this.ctx
                        );
                        await transaction.insert(
                            "patient_insurance",
                            recordData,
                            ctx.user
                        );
                        const res = await transaction.commit(this.ctx);
                        if (res.error) throw this.fx.getClaraError(res.message);
                        transaction.init();

                        const form = "patient_insurance";
                        const formIds =
                            this.fx.fetchFormIdsFromTransactionResults(
                                form,
                                res
                            );
                        formId = formIds[0];
                    }

                    return ActionResponseWrappers.preset({
                        insurance_id: formId,
                    });
                }
                default:
                    throw new Error(`Unsupported action: ${bvAction}`);
            }
        } catch (e) {
            const errorMessage = `Error encountered while processing ${form} post action.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Handles an action for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string|number} id - The ID of the form entry.
     * @param {string} action - The action to be performed on the form.
     * @returns {Promise<Object>} The result of the action performed.
     * @throws {Error} If an error occurs during the action processing.
     */
    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const bvAction = _.invert(OrderBVActionKeys)[action];
            const callbackUrl = `/api/form/careplan_order_bv/${id}/action/${action}`;

            switch (bvAction) {
                case OrderBVActions.ADD_PAYER: {
                    return ActionResponseWrappers.create(
                        {},
                        "payer",
                        callbackUrl,
                        null,
                        null,
                        null,
                        null,
                        null,
                        DisplayType.TAB
                    );
                }
                case OrderBVActions.ADD_INSURANCE: {
                    return ActionResponseWrappers.create(
                        {},
                        "patient_insurance",
                        callbackUrl,
                        null,
                        null,
                        null,
                        null,
                        null,
                        DisplayType.TAB
                    );
                }
                default:
                    throw new Error(`Unsupported action: ${bvAction}`);
            }
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run ${action} action for ${form} id ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Retrieves available actions for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The form name.
     * @param {string|number} id - The ID of the form.
     * @returns {Promise<Array>} An array of available actions for the form.
     */
    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const availableActions = [];
            let label = OrderBVActions.ADD_PAYER;
            let action = OrderBVActionKeys[label];
            let callbackUrl = `/api/form/careplan_order_bv/${id}?perform_action=${action}`;
            availableActions.push(
                editActionButtonWrapper(
                    label,
                    action,
                    callbackUrl,
                    CallbackType.GET,
                    ButtonStyle.ACCEPT
                )
            );
            label = OrderBVActions.ADD_INSURANCE;
            action = OrderBVActionKeys[label];
            callbackUrl = `/api/form/careplan_order_bv/${id}?perform_action=${action}`;
            availableActions.push(
                editActionButtonWrapper(
                    label,
                    action,
                    callbackUrl,
                    CallbackType.GET,
                    ButtonStyle.ACCEPT,
                    ButtonActionRequirements.NONE
                )
            );
            label = OrderBVActions.INTERVENTIONS;
            action = OrderBVActionKeys[label];
            const primaryInterventionType =
                this.shared.config.company.intake_inter_primary_type;
            const interventionTypeFilter =
                this.shared.config.company.intake_inter_types;
            availableActions.push(
                listActionButtonWrapper(
                    label,
                    "patient_event",
                    "patient",
                    {},
                    {
                        category: "Intervention",
                        intr_typ_ftr: interventionTypeFilter || [],
                        intervention_type: primaryInterventionType,
                    },
                    true,
                    ButtonStyle.WARNING,
                    "id",
                    "desc",
                    { patient_id: id }
                )
            );
            return { actions: availableActions, warning: null };
        } catch (e) {
            const errorMessage = `Error encountered while checking available ${form} actions.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }
};
