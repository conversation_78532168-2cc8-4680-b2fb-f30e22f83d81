/*jshint : 6 */
"use strict";

const { ActionHandler } = require("./index");

/**
 * @class
 * @classdesc Class responsible for running billing cash related actions
 */
module.exports = class BillingCashHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );
        try {
            const BillingActionHandler = require("@billing/actions");
            const actionHandler = await new BillingActionHandler(this.nes).init(
                form
            );
            return await actionHandler.postAction(
                ctx,
                form,
                id,
                action,
                params
            );
        } catch (e) {
            const errorMessage = `Error encountered while processing ${form} post action.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Handles an action for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string|number} id - The ID of the form entry.
     * @param {string} action - The action to be performed on the form.
     * @returns {Promise<Object>} The result of the action performed.
     * @throws {Error} If an error occurs during the action processing.
     */
    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const BillingActionHandler = require("@billing/actions");
            const actionHandler = await new BillingActionHandler(this.nes).init(
                form
            );
            return await actionHandler.runAction(ctx, form, id, action);
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run ${action} action for ${form} id ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Retrieves available actions for a given form.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} form - The form name.
     * @param {string|number} id - The ID of the form.
     * @returns {Promise<Array>} An array of available actions for the form.
     */
    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const BillingActionHandler = require("@billing/actions");
            const actionHandler = await new BillingActionHandler(this.nes).init(
                form
            );
            return await actionHandler.getActions(ctx, form, id);
        } catch (e) {
            const errorMessage = `Error encountered while checking available ${form} actions.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }
};
