"use strict";

module.exports = class FormTestsAPIClass {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
    }

    hasData(request) {
        return request.body != null && Object.keys(request.body).length > 0;
    }

    // Request handler
    async process(ctx, urlpath) {
        try {
            if (
                !(
                    this.shared.config.env["NODE_ENV"] == "development" ||
                    this.shared.config.env["NODE_ENV"] == "testing"
                ) ||
                !ctx.is_test
            ) {
                ctx.status = 500;
                ctx.body = {
                    error: "Testing API is only available in development or testing environments.",
                };
                return;
            }

            const request = ctx.request;
            if (!this.hasData(request)) {
                ctx.status = 400;
                ctx.body = {
                    error: "No data provided.",
                };
                return;
            }
            const { classPath, functionName, functionArguments } = request.body;
            if (!classPath || !functionName || !functionArguments) {
                ctx.status = 400;
                ctx.body = {
                    error: "Invalid request body. Must have classPath, functionName, and functionArguments defined",
                };
                return;
            }

            const TestClass = require(classPath);
            const testClassConstructor = TestClass.toString().match(
                /constructor\s*\((.*?)\)/
            );
            const constructorArgs = testClassConstructor
                ? testClassConstructor[1].split(",").map((arg) => arg.trim())
                : [];

            let testInstance;
            if (
                constructorArgs.length === 2 &&
                constructorArgs[0] === "nes" &&
                constructorArgs[1] === "ctx"
            ) {
                testInstance = new TestClass(this.nes, ctx);
            } else if (
                constructorArgs.length === 1 &&
                constructorArgs[0] === "nes"
            ) {
                testInstance = new TestClass(this.nes);
            } else {
                throw new Error(
                    "TestClass constructor does not match expected signature (either nes, or nes, ctx)"
                );
            }
            // Check if one of the arguments is 'transaction' and create it if necessary
            const transactionIndex = functionArguments.findIndex(
                (arg) => arg === "transaction"
            );
            if (transactionIndex !== -1) {
                // Create a new transaction
                const transaction = this.db.env.rw.transaction(ctx);
                // Replace 'transaction' with the actual transaction object
                functionArguments[transactionIndex] = transaction;
            }

            const result = await testInstance[functionName](
                ...functionArguments
            );
            ctx.body = {
                result: result,
            };
        } catch (e) {
            console.log(e);
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
            } else {
                ctx.status = 400;
                ctx.body = {
                    error: e,
                };
            }
        }
    }
};
