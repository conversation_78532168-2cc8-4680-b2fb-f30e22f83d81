"use strict";

const FormGetClass = require("./operations/get");
const FormPostClass = require("./operations/post");
const FormPutClass = require("./operations/put");
const FormValidateClass = require("./operations/validate");
const FormListClass = require("./operations/list");

module.exports = class FormAPIClass {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.get = new FormGetClass(nes);
        this.post = new FormPostClass(nes);
        this.put = new FormPutClass(nes);
        this.list = new FormListClass(nes);
        this.validate = new FormValidateClass(nes);
    }

    has_data(request) {
        return request.body != null && Object.keys(request.body).length > 0;
    }

    async get_form(ctx, user, form, params = {}) {
        return await this.get.get_form(ctx, user, form, params);
    }

    // Request handler
    async process(ctx, urlpath) {
        try {
            const request = ctx.request;
            const form = urlpath.path[2];
            if (!this.auth.can_access_form(ctx, form)) {
                console.warn(
                    `Form Access denied for user: ${ctx.user.id} on form: ${this.shared.DSL[form].view.label}`
                );
                ctx.status = 403;
                ctx.body = {
                    error: "Access denied.",
                };
                return;
            }

            if (request.method == "GET") {
                await this.get.process(ctx, urlpath);
                return;
            } else if (request.method == "POST") {
                if (urlpath.path[3] && urlpath.path[3] === "list") {
                    await this.list.process(ctx, urlpath);
                    return;
                }
                if (urlpath.path[3] && urlpath.path[3] === "validate") {
                    await this.validate.process(ctx, urlpath);
                    return;
                }
                if (
                    urlpath.path.length == 6 &&
                    urlpath.path[4] &&
                    urlpath.path[4] === "action"
                ) {
                    const params = Object.assign({}, ctx.query);
                    const formId = parseInt(urlpath.path[3]);
                    const action = urlpath.path[5];
                    await this.__handleFormAction(
                        ctx,
                        form,
                        formId,
                        action,
                        params
                    );
                    return;
                }
                await this.post.process(ctx, urlpath);
                return;
            } else if (request.method == "PUT") {
                await this.put.process(ctx, urlpath);
                return;
            }
            ctx.status = 400;
            ctx.body = {
                error: "Invalid request method.",
            };
            return;
        } catch (e) {
            console.log(e);
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
            } else {
                ctx.status = 400;
                ctx.body = {
                    error: e,
                };
            }
        }
    }

    /**
     * Sets actions for each row in the form data.
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string} formId - The ID of the form.
     * @param {string} action - The action to perform.
     * @param {Object} params - The parameters for the action.
     * @returns {Promise<void>}
     */
    async __handleFormAction(ctx, form, formId, action, params) {
        try {
            const actionHandler = this.nes.actionHandlers[form];
            if (!actionHandler) {
                ctx.status = 500;
                ctx.body = {
                    error: `Action handler not found for form (${form})`,
                };
                return;
            }
            const response = await actionHandler.postAction(
                ctx,
                form,
                formId,
                action,
                params
            );
            ctx.status = 200;
            ctx.body = response;
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = {
                error: `Error running action (${action}) on form (${form}) with ID (${formId}) Error: ${e.message}`,
            };
            throw e;
        }
    }
};
