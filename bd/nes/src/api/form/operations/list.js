"use strict";
const ListGetClass = require("../helpers/list-get");
const _ = require("lodash");

module.exports = class FormListClass extends ListGetClass {
    constructor(nes, exporting = false) {
        super(nes, exporting);
        this.nes = nes;
        this.shared = nes.shared;
        this.dsl = nes.modules.dsl;
    }

    async getQueryBase(ctx, urlpath, opts = {}) {
        const form = urlpath.path[2];

        if (!form || !this.shared.DSL[form]) {
            throw new Error("Form not found");
        }
        const params = Object.assign({}, ctx.query);
        const sf = await this.sql_form(ctx, form, params);
        return {
            sql: sf.sql,
            par: sf.par,
            dsl: this.shared.DSL[form],
        };
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const form = urlpath.path[2];
            if (!user || !this.auth.can_access_form(ctx, form)) {
                ctx.status = 403;
                ctx.body = {
                    error: "Access denied.",
                };
                return;
            }
            ctx.body = await this.getData(ctx, urlpath);
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = { error: e.message, stack: e.stack };
        }
    }
};
