"use strict";
const _ = require("lodash");
const path = require("path");
const SaveFormClass = require("../helpers/save");
const class_path = __dirname + "/../overrides/";
module.exports = class FormPostClass extends SaveFormClass {
    constructor(nes) {
        super(nes, "CREATE");
        this.nes = nes;
        this.fx = nes.modules.fx;
    }

    async process(ctx, urlpath) {
        console.time("create");
        this.transaction = this.db.env.rw.transaction(ctx);
        let OverrideClass = null;
        const user = ctx.user;
        let form = urlpath.path[2];
        let formid = null;
        let Overrides = null;
        Overrides = await this.fx.get_folder_contents(class_path);
        if (
            Overrides &&
            Overrides.length > 0 &&
            Overrides.includes(form + ".js")
        ) {
            OverrideClass = require(path.join(class_path, `${form}.js`));
            OverrideClass = new OverrideClass(this.nes, ctx, this.method);
        }

        const body = this.get_data(ctx.request);
        if (!body) {
            ctx.status = 500;
            ctx.body = {
                error: "There was a problem while saving this form.",
            };
            return false;
        }
        try {
            if (OverrideClass) {
                const res = await OverrideClass.before_checks(
                    this.transaction,
                    body,
                    form,
                    user
                );
                if (res?.error) throw new Error(res.message);
                if (res?.warning) {
                    ctx.status = 400;
                    ctx.body = {
                        warning: res.warning,
                    };
                    return;
                }
                if (res.break) {
                    ctx.status = 200;
                    ctx.body = {
                        data: res.data,
                    };
                    return;
                }
            }
            // eslint-disable-next-line prefer-const
            let [data, meta, cson] = await this.validate(ctx, form, body);
            if (!data) {
                return;
            }
            await this.fx.addUserActivity(ctx, form, "post");
            if (OverrideClass) {
                const {
                    data: newData,
                    error,
                    formOverride,
                } = await OverrideClass.before_op(
                    this.transaction,
                    data,
                    form,
                    ctx
                );
                if (formOverride) {
                    form = formOverride;
                }
                if (error) throw new Error(error);
                data = newData;
            }

            const tempID = await this.transaction.insert(form, data);
            body.id = tempID;
            // before save
            if (OverrideClass) {
                const { data: newData, res } = await OverrideClass.before_save(
                    this.transaction,
                    body,
                    form,
                    ctx
                );
                data = newData;
                if (res?.error) throw new Error(res.error);
                if (res?.warning) {
                    ctx.status = 400;
                    ctx.body = {
                        warning: res.warning,
                    };
                    return;
                }
            }
            let res = [];
            if (this.transaction.can_commit()) {
                res = await this.transaction.commit();
                if (res.error) throw new Error(res.message);
            }
            this.transaction.init();

            if (res.length > 0) {
                formid = res
                    .filter((e) => e.formname === form && e.op === "insert")
                    .sort((a, b) => a.seq - b.seq)[0].id;
                data.id = formid;
            }
            if (OverrideClass) {
                data = _.head(
                    await this.get.get_form(ctx, ctx.user, form, {
                        filter: [`id:${formid}`],
                    })
                );
                const { data: newData, error } = await OverrideClass.after_save(
                    this.transaction,
                    data,
                    form,
                    ctx
                );
                data = newData;
                if (error) throw new Error(error);
            }
            data["_meta"] = meta;
            if (!ctx?.is_test) {
                // Don't run post transform in the test environment, they could save or send out an SMS
                data = await this.run_post_transforms(
                    ctx,
                    form,
                    cson,
                    data,
                    meta,
                    user
                );
                if (OverrideClass) {
                    const { data: newData, error } =
                        await OverrideClass.after_transform(
                            this.transaction,
                            data,
                            form,
                            ctx
                        );
                    data = newData;
                    if (error) throw new Error(error);
                }

                if (this.transaction.can_commit()) {
                    res = await this.transaction.commit(ctx);
                    if (res.error) throw new Error(res.message);
                }
            }
            await this.get_return_data(ctx, formid, form);
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body =
                e.name && e.message
                    ? {
                          [e.name]: e.message,
                      }
                    : e;
        } finally {
            console.timeEnd("create");
        }
    }
};
