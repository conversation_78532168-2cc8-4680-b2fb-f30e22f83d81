"use strict";
const _ = require("lodash");
const SaveFormClass = require("../helpers/save");
const class_path = __dirname + "/../overrides/";
const path = require("path");
module.exports = class FormPutClass extends SaveFormClass {
    constructor(nes) {
        super(nes, "UPDATE");
        this.nes = nes;
        this.auth = nes.modules.auth;
    }

    async validate(ctx, form, body) {
        // Run field level checks
        let [data, meta, cson] =
            (await this.run_field_level_checks(ctx, form, body)) || {};
        if (!data) {
            return [null, null, null];
        }
        // Run form level checks
        [data, meta, cson] = await this.run_form_level_checks(
            ctx,
            form,
            cson,
            data,
            meta,
            ctx.user,
            this.method
        );
        if (!data) {
            return [null, null, null];
        }
        // Check if this is a validate only request and triggers form not to save
        this.check_validate_only(ctx, data);
        return [data, meta, cson];
    }

    async process(ctx, urlpath) {
        this.transaction = this.db.env.rw.transaction(ctx);
        let OverrideClass = null;
        const user = ctx.user;
        let form = urlpath.path[2];
        const formid = urlpath.path[3];
        const request = ctx.request;

        let override_classes = null;
        override_classes = await this.fx.get_folder_contents(class_path);
        if (
            override_classes &&
            override_classes.length > 0 &&
            override_classes.includes(form + ".js")
        ) {
            OverrideClass = require(path.join(class_path, `${form}.js`));
            OverrideClass = new OverrideClass(this.nes, ctx, this.method);
        }
        const body = this.get_data(request, formid);
        if (!body) {
            ctx.status = 500;
            ctx.body = {
                error: "There was a problem while saving this form.",
            };
            return;
        }
        console.time("archive_checks");
        try {
            const res = await this.perform_archive_checks(
                ctx,
                urlpath,
                form,
                body,
                formid
            );
            if (res) {
                if (this.transaction.can_commit()) {
                    await this.transaction.commit();
                    return;
                }
            }
        } catch (error) {
            console.warn(`Error performing archive checks on ${form} ${error}`);
            ctx.status = 500;
            ctx.body = {
                error: "There was a problem while saving this form.",
            };
            return;
        } finally {
            console.timeEnd("archive_checks");
        }
        // Run field level checks
        console.time("update" + form);
        try {
            if (OverrideClass) {
                const res = await OverrideClass.before_checks(
                    this.transaction,
                    body,
                    form,
                    ctx
                );
                if (res?.error) throw new Error(res.message);
                if (res?.warning) {
                    ctx.status = 400;
                    ctx.body = {
                        warning: res.warning,
                    };
                    return;
                }
                if (res?.break) {
                    ctx.status = 200;
                    ctx.body = {
                        data: res.data,
                    };
                    return;
                }
            }
            // eslint-disable-next-line prefer-const
            let [data, meta, cson] = await this.validate(ctx, form, body);
            if (!data) {
                return;
            }
            await this.fx.addUserActivity(ctx, form, "put");
            if (OverrideClass) {
                const ogData = _.head(
                    await this.get.get_form(ctx, ctx.user, form, {
                        filter: [`id:${formid}`],
                    })
                );
                data = {
                    ...ogData,
                    ...data,
                };
                const {
                    data: newData,
                    error,
                    formOverride,
                } = await OverrideClass.before_op(
                    this.transaction,
                    data,
                    form,
                    ctx
                );
                if (formOverride) {
                    form = formOverride;
                }
                if (error) throw new Error(error);
                data = newData;
            }
            await this.transaction.update(form, data, formid);
            console.time("update");
            if (OverrideClass) {
                const res = await OverrideClass.before_save(
                    this.transaction,
                    body,
                    form,
                    ctx
                );
                if (res?.error) throw new Error(res.error);
                if (res?.warning) {
                    ctx.status = 400;
                    ctx.body = {
                        warning: res.warning,
                    };
                    return;
                }
            }
            const res = await this.transaction.commit();
            if (res.error) throw new Error(res.message);
            this.transaction.init();
            if (OverrideClass) {
                data = _.head(
                    await this.get.get_form(ctx, ctx.user, form, {
                        filter: [`id:${formid}`],
                    })
                );
                const { data: newData, error } = await OverrideClass.after_save(
                    this.transaction,
                    data,
                    form,
                    ctx
                );
                if (error) throw new Error(error);
                data = newData;
            }

            if (!ctx?.is_test) {
                // Don't run post transform in the test environment, they could save or send out an SMS
                // handle subforms and gerund tables entries
                data = await this.run_post_transforms(
                    ctx,
                    form,
                    cson,
                    data,
                    meta,
                    user
                );
                if (OverrideClass)
                    data = await OverrideClass.after_transform(
                        this.transaction,
                        data,
                        form,
                        ctx
                    );
                if (this.transaction.can_commit())
                    await this.transaction.commit(ctx);
            }
            await this.get_return_data(ctx, formid, form);
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body =
                e.name && e.message
                    ? {
                          [e.name]: e.message,
                      }
                    : e;
        } finally {
            console.timeEnd("update" + form);
        }
    }
};
