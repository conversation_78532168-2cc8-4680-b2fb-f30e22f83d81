"use strict";
const { FormValidatorClass } = require("../validators");

module.exports = class FormValidateClass {
    constructor(nes) {
        this.nes = nes;
    }

    async process(ctx, urlpath) {
        const data = ctx.request.body;
        const form = urlpath.path[2];
        if (!form || !data?.name || !data?.field_vals) {
            console.warn(
                `Validate Invalid Body: ${JSON.stringify(data || "")}`
            );
            ctx.status = 500;
            ctx.body = {
                error: "Invalid validator request. Requires form, name and field_vals.",
            };
            return;
        }

        const validator = new FormValidatorClass(this.nes);
        try {
            const result = await validator.validate(
                ctx,
                form,
                data.name,
                data.field_vals
            );
            ctx.status = 200;
            ctx.set("Content-Type", "application/json; charset=utf-8");
            ctx.body = result || {};
        } catch (error) {
            console.log(error);
            ctx.status = 500;
            ctx.body = {
                error: "Internal server error.",
            };
        }
    }
};
