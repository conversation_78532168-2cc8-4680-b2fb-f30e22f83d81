"use strict";
const { MocksLoaderClass } = require("@core/tester");
const SubformGetClass = require("../helpers/subform");
const _ = require("lodash");
const class_path = __dirname + "/../overrides/";
const path = require("path");

module.exports = class FormGetClass extends SubformGetClass {
    constructor(nes, exporting = false) {
        super(nes, exporting);
        this.nes = nes;
        this.shared = nes.shared;
    }

    /**
     * Runs an action for a specific form and ID with given parameters.
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {string|number} id - The ID of the form entry.
     * @param {string} action - The name of the action to run.
     * @returns {Promise<*>} The result of the action.
     * @throws {Error} If there's an error during action execution.
     */
    async runAction(ctx, form, id, action) {
        try {
            const actionHandler = this.nes.actionHandlers[form];
            if (!actionHandler) {
                return;
            }
            return await actionHandler.runAction(ctx, form, id, action);
        } catch (e) {
            console.error(e);
            throw e;
        }
    }

    /**
     * Sets actions for each row in the form data.
     * @param {Object} ctx - The context object.
     * @param {string} form - The name of the form.
     * @param {Array} frows - An array of form rows to process.
     * @returns {Promise<void>}
     */
    async setActions(ctx, form, frows) {
        try {
            const actionHandler = this.nes.actionHandlers[form];
            if (!actionHandler) {
                return;
            }
            for (const row of frows) {
                const formId = row.id;
                const { actions, warning } = await actionHandler.getActions(
                    ctx,
                    form,
                    formId
                );
                let sortedActions = [];
                if (actions?.length > 0) {
                    sortedActions = _.sortBy(actions, "label");
                }
                row._meta = {
                    ...(row?._meta || {}),
                    actions: sortedActions,
                    warning: warning,
                };
            }
        } catch (e) {
            console.error(e);
            throw e;
        }
    }

    async get_form(ctx, user, form, params = {}, fetchActions = false) {
        if (ctx.is_test) {
            const mocks_loader = new MocksLoaderClass(ctx);
            const data = mocks_loader.fetchMockView(form, params);
            if (!data) {
                console.warn(
                    `Missing mock for form request ${form}, referring back to real call`
                );
            } else {
                if (fetchActions) {
                    await this.setActions(ctx, form, data);
                }
                ctx.status = 200;
                return data;
            }
        }
        if (!("limit" in params))
            params.limit = this.shared.config.limits.max_rows;
        const sf = await this.sql_form(ctx, form, params);
        if ("error" in sf) {
            ctx.status = 500;
            return sf;
        } else {
            const { sql, par } = sf;
            // remove CASE _index because they do not add value to js clients
            const rows = await this.db.env.rw.query(
                sql.replace(/,\(CASE(.*?)_index/g, ""),
                par
            );
            let frows = this.filters.filter_fields(ctx, form, rows, params);

            if (frows.length > 0) {
                // additional processing
                if (
                    !("fields" in params) ||
                    (params.fields != "list" &&
                        params.fields != "min" &&
                        params.fields != "count")
                ) {
                    frows = await this.get_nested_subforms(
                        { [form]: frows },
                        ctx,
                        params
                    );
                    try {
                        frows = frows[Object.keys(frows)[0]];
                    } catch (e) {
                        ctx.status = 500;
                        console.error(e);
                        return {
                            error: "Failed to resolve subforms.",
                        };
                    }
                }
                if (ctx.is_test) {
                    const default_dsl_vars_array =
                        ctx?.test_vars?.default_dsl_vars || [];
                    default_dsl_vars_array.forEach((default_dsl_vars) => {
                        if (
                            default_dsl_vars?.form &&
                            default_dsl_vars.form === form
                        ) {
                            frows = frows.map((fm) =>
                                _.assignIn(fm, default_dsl_vars)
                            );
                        }
                    });
                }
            }
            ctx.status = 200;
            if (fetchActions) {
                await this.setActions(ctx, form, frows);
            }
            let OverrideClass = null;
            let override_classes = null;
            override_classes = await this.fx.get_folder_contents(class_path);
            if (
                override_classes &&
                override_classes.length > 0 &&
                override_classes.includes(form + ".js")
            ) {
                OverrideClass = require(path.join(class_path, `${form}.js`));
                OverrideClass = new OverrideClass(this.nes, ctx, this.method);
            }

            try {
                if (OverrideClass) {
                    const { data, error } = await OverrideClass.before_get(
                        frows,
                        form,
                        ctx
                    );
                    if (error) throw new Error(error);
                    frows = data;
                }
            } catch (e) {
                console.error(e);
                ctx.status = 500;
                ctx.body =
                    e.name && e.message
                        ? {
                              [e.name]: e.message,
                          }
                        : e;
            }
            return frows;
        }
    }

    async get_log_time(id, form) {
        try {
            const sql = `SELECT
                        lf.created_on,
                        lf.created_by,
                        lf.updated_on,
                        lf.updated_by,
                        lf.auto_name,
                        lf.sys_period,
                        CASE
                            WHEN lf.updated_by IS NOT NULL THEN fu_updated.firstname
                            ELSE fu_created.firstname
                        END AS firstname,
                        CASE
                            WHEN lf.updated_by IS NOT NULL THEN fu_updated.lastname
                            ELSE fu_created.lastname
                        END AS lastname,
                        CASE
                            WHEN lf.updated_by IS NOT NULL THEN fu_updated.displayname
                            ELSE fu_created.displayname
                        END AS displayname
                    FROM log_${form} as lf
                    LEFT JOIN
                        form_user as fu_updated on lf.updated_by = fu_updated.id
                    LEFT JOIN
                        form_user as fu_created on lf.created_by = fu_created.id
                    WHERE
                        lf.id=${id}
                    ORDER BY lf.updated_on DESC`;

            const result = await this.db.env.rw.query(sql);
            return result;
        } catch (error) {
            console.log(error);
            throw error;
        }
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const form = urlpath.path[2];
            if (!user || !this.auth.can_access_form(ctx, form)) {
                ctx.status = 403;
                ctx.body = {
                    error: "Access denied.",
                };
            } else if (urlpath.path.length > 4) {
                const id = urlpath.path[3];
                if (id && urlpath.path[4] == "log") {
                    ctx.body = await this.get_log_time(id, form);
                }
            } else if (urlpath.path.length > 3) {
                console.log("Processing action");
                const params = Object.assign({}, ctx.query);
                const formId = urlpath.path[3];
                params.limit = 1;
                params.filter = "id:" + formId;
                params.archived = "all";
                const fetchActions =
                    params.get_actions === "true" ||
                    params.get_actions === true;
                delete params.get_actions;
                const performAction = params.perform_action;
                if (performAction) {
                    ctx.body = await this.runAction(
                        ctx,
                        form,
                        formId,
                        performAction
                    );
                } else {
                    ctx.body = await this.get_form(
                        ctx,
                        user,
                        form,
                        { ...params, skip_site_filter: false },
                        fetchActions,
                        performAction
                    );
                }
            } else if (urlpath.path.length > 2) {
                const params = Object.assign({}, ctx.query);
                if (!("limit" in params) && !(form === "coffeedsl"))
                    params.limit = this.shared.config.limits.default_rows;
                ctx.body = await this.get_form(ctx, user, form, {
                    ...params,
                    skip_site_filter: false,
                });
            }
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = { error: e.message, stack: e.stack };
        }
    }
};
