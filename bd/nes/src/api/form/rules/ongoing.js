"use strict";
const _ = require("lodash");

module.exports = class OngoingRuleClass {
    constructor(nes, ctx, method) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
        this.method = method;
    }

    async assessment_add_dur(transaction, formdata, form, ctx) {
        console.log("assessment_add_dur rule in Ongoing", formdata);
        try {
            let createEncounterDUR = false;
            if (this.method == "UPDATE") {
                const ongoingPrev = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "ongoing",
                    {
                        filter: [`id:${formdata.id}`],
                    }
                );
                if (
                    formdata?.cust_pharm_checkbox.length > 0 &&
                    !_.isEqual(
                        ongoingPrev[0]?.cust_pharm_checkbox,
                        formdata.cust_pharm_checkbox
                    ) &&
                    formdata.cust_pharm_checkbox.includes("DUR reviewed")
                ) {
                    createEncounterDUR = true;
                }
            }
            if (
                createEncounterDUR ||
                (this.method == "CREATE" &&
                    formdata.cust_pharm_checkbox.includes("DUR reviewed"))
            ) {
                await transaction.insert("encounter_dur", {
                    patient_id: formdata.patient_id,
                    careplan_id: formdata.careplan_id,
                    completed: "Yes",
                    intervention: "No",
                });
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run after save for Ongoing. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    async ongoing_reviewed_id(transaction, formdata, form, ctx) {
        console.log("Rule: ongoing_reviewed_id ");
        try {
            /** ongoing_reviewed_id.rule */
            console.log(
                " ongoing_reviewed_id formdata.reviewed_by ",
                formdata.reviewed_by,
                formdata.reviewed_id
            );
            if (formdata.reviewed_by && !formdata.reviewed_id) {
                await transaction.update(
                    "ongoing",
                    {
                        reviewed_id: formdata.reviewed_by,
                    },
                    formdata.id
                );
            }
            /** ongoing_reviewed_id.rule */
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run rule ongoing_reviewed_id for ongoing. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
