"use strict";

module.exports = class admissionDischargeRuleClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
    }

    async admission_discharge(transaction, formdata, _form, ctx, method) {
        try {
            if (!formdata.patient_id) {
                return { data: formdata, error: null };
            }
            if (formdata.status != "Active") {
                return { data: formdata, error: null };
            } else {
                const filters = [
                    `status:${formdata.status}`,
                    `patient_id:${formdata.patient_id}`,
                ];
                const admission_discharge_forms = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "admission_discharge",
                    {
                        filter: filters,
                    }
                );
                const updatePromises = [];
                admission_discharge_forms.forEach((admission) => {
                    if (admission.id != formdata.id) {
                        updatePromises.push(
                            transaction.update(
                                "admission_discharge",
                                {
                                    status: "Discharged",
                                },
                                admission.id
                            )
                        );
                    }
                });
                if (updatePromises.length > 0)
                    await Promise.all(updatePromises);
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while running rule for admission discharge. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    async admission_discharge_audit(transaction, formdata, _form, ctx, method) {
        console.log(
            " =========== Rule: admission_discharge_audit =========== "
        );
        try {
            let createAudit = false;
            if (
                method == "UPDATE" &&
                (await this.fx.hasChanged(ctx, formdata, _form, "status"))
            ) {
                console.log("method is update and status changed ");
                createAudit = true;
            }

            if (method == "CREATE" || createAudit) {
                await transaction.insert("admission_discharge_audit", {
                    status: formdata.status,
                    patient_id: formdata.patient_id,
                    therapy_1: formdata.therapy_1,
                });
            }

            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before op for Admission Discharge Audit. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: formdata,
                error: returnError.error,
            };
        }
    }
};
