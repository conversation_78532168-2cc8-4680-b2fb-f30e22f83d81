"use strict";
const _ = require("lodash");
const moment = require("moment-timezone");

module.exports = class CareplanDeliveryTickRuleClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
    }

    async patient_dispense_pronote(transaction, formdata, form, ctx) {
        try {
            if (
                (formdata.confirmed == "Yes" &&
                    (await this.fx.hasChanged(
                        ctx,
                        formdata,
                        form,
                        "confirmed"
                    ))) ||
                (formdata.confirmed == "Yes" &&
                    formdata.subform_delivery_log[0].shipment_status_id ==
                        "SHIPPED" &&
                    careplanDeliveryTickPrev[0].subform_delivery_log[0]
                        .shipment_status_id !=
                        formdata.subform_delivery_log[0].shipment_status_id)
            ) {
                console.log("Patient Confirmed Dispense");

                const temp_tag = "patient_dispense_pronote:" + formdata.id;

                const note = {
                    created_by: formdata.confirmed_by,
                    patient_id: formdata.patient_id,
                    user_id: formdata.confirmed_by,
                    user_role: ctx.user.role,
                    subject: "Delivery Ticket " + formdata.id + " Confirmed",
                    note:
                        "Shipment method:" +
                        (formdata?.subform_delivery_log[0]?.ship_method_id
                            ? formdata?.subform_delivery_log[0].ship_method_id
                            : "") +
                        " Tracking number: ",
                    template_tag: temp_tag,
                    order_id: formdata.order_id,
                    order_no: formdata.order_no,
                    note_type: "Pharmacy",
                };

                if (formdata.tracking_no) {
                    note["note"] =
                        note["note"] +
                        (formdata.subform_delivery_log[0].tracking_no
                            ? formdata.subform_delivery_log[0].tracking_no
                            : "");
                }

                const old_note = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "patient_note",
                    {
                        filter: [`template_tag:${temp_tag}`],
                    }
                );
                if (old_note.length < 1) {
                    console.log(
                        "Creating Confirmed Dispense Ticket Progress Note for: " +
                            formdata.id
                    );
                    await transaction.insert("patient_note", note);
                    /** patient_last_update_date **/
                    await transaction.update(
                        "patient",
                        {
                            last_updated: moment
                                .tz()
                                .format("MM/DD/YYYY hh:mm a"),
                        },
                        formdata.patient_id
                    );
                    /** patient_last_update_date **/
                } else {
                    console.log(
                        "Updating Confirmed Dispense Ticket Progress Note for: " +
                            formdata.id
                    );
                    await transaction.update(
                        "patient_note",
                        {
                            note: note["note"],
                        },
                        old_note[0].id
                    );
                    /** patient_last_update_date **/
                    await transaction.update(
                        "patient",
                        {
                            last_updated: moment
                                .tz()
                                .format("MM/DD/YYYY hh:mm a"),
                        },
                        formdata.patient_id
                    );
                    /** patient_last_update_date **/
                }
            }
            const careplanDeliveryTickPrev = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_delivery_tick",
                {
                    filter: [`id:${formdata.id}`],
                }
            );
        } catch (e) {
            const errorMessage = `Error encountered while running rule patient_dispense_pronote for careplan_delivery_tick. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
