"use strict";
const _ = require("lodash");

module.exports = class PatientMedicationRuleClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
    }

    async patient_medication_status_update(transaction, formdata, _form, _ctx) {
        try {
            if (formdata.status != "Active") {
                if (formdata.currently_taking == "Yes") {
                    await transaction.update(
                        "patient_medication",
                        {
                            status: "Active",
                        },
                        formdata.id
                    );
                }
            }
        } catch (e) {
            const errorMessage = `Error encountered while running rule patient_medication_status_update for patient medication. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
