"use strict";
const _ = require("lodash");
const moment = require("moment-timezone");

module.exports = class PatientNoteRuleClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
    }

    async patient_last_update_date(transaction, formdata, form, ctx, method) {
        try {
            if (method === "CREATE") {
                await transaction.update(
                    "patient",
                    {
                        last_updated: moment.tz().format("MM/DD/YYYY hh:mm a"),
                    },
                    formdata.patient_id
                );
            } else {
                await transaction.update(
                    "patient",
                    {
                        last_updated: moment.tz().format("MM/DD/YYYY hh:mm a"),
                    },
                    formdata.patient_id
                );
            }
        } catch (e) {
            const errorMessage = `Error encountered while running rule patient_last_update_date for patient note. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
