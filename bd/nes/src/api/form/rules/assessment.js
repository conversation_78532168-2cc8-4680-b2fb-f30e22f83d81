"use strict";
const _ = require("lodash");

module.exports = class AssessmentRuleClass {
    constructor(nes, ctx, method) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
        this.method = method;
    }
    async assessment_add_dur(transaction, formdata, form, ctx) {
        try {
            let createEncounterDUR = false;
            if (this.method == "UPDATE") {
                const assessmentPrev = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "assessment",
                    {
                        filter: [`id:${formdata.id}`],
                    }
                );
                if (
                    formdata?.cust_pharm_checkbox.length > 0 &&
                    !_.isEqual(
                        assessmentPrev[0]?.cust_pharm_checkbox,
                        formdata.cust_pharm_checkbox
                    ) &&
                    formdata.cust_pharm_checkbox.includes("DUR reviewed")
                ) {
                    createEncounterDUR = true;
                }
            }
            if (
                createEncounterDUR ||
                (this.method == "CREATE" &&
                    formdata.cust_pharm_checkbox.includes("DUR reviewed"))
            ) {
                await transaction.insert("encounter_dur", {
                    patient_id: formdata.patient_id,
                    careplan_id: formdata.careplan_id,
                    completed: "Yes",
                    intervention: "No",
                });
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run after save for Assessment. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    async opt_out_physician_office(transaction, formdata, form, ctx) {
        console.log("opt-out-physician-office rule in Assessment");
        try {
            const assessmentPrev = await this.form.get.get_form(
                ctx,
                ctx.user,
                "assessment",
                {
                    filter: [`id:${formdata.id}`],
                }
            );
            // envoy field shipping_location is changed to admin_location
            if (
                formdata.admin_location &&
                formdata.admin_location != assessmentPrev[0].admin_location &&
                formdata.admin_location == "Physician office"
            ) {
                await transaction.update(
                    "assessment",
                    {
                        cust_benefits_understand_optout: "Opt-Out",
                    },
                    formdata.id
                );
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run rule opt_out_physician_office for Assessment. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
