"use strict";
const _ = require("lodash");
const moment = require("moment");

module.exports = class PatientRuleClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
    }
    async patient_discharged(transaction, formdata, form, ctx) {
        try {
            if (await this.fx.hasChanged(ctx, formdata, form, "status_id")) {
                if (formdata.status_id != 7 && formdata.status_id != 5) {
                    return;
                }

                const override_stage = [
                    "Patient Discharged",
                    "Discontinued by Prescriber",
                    "Patient Deceased",
                    "Forced to Competitor",
                    "Denied-Medical Necessity",
                    "No Coverage-Closed",
                    "Patient Refused Therapy",
                    "Therapy Completed",
                    "Discontinued ADE",
                ];

                if (
                    formdata.cust_stage &&
                    override_stage.indexOf(formdata.cust_stage) > -1
                ) {
                    console.log(
                        "Found override discharge status on stage " +
                            formdata.cust_stage +
                            "  for patient: " +
                            formdata.id
                    );
                    return;
                }
                await transaction.update(
                    "patient",
                    {
                        cust_stage: "Patient Discharged",
                    },
                    formdata.id
                );
            }
        } catch (e) {
            const errorMessage = `Error encountered while running rule patient_discharged for patient. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
    async patient_status_pronote(transaction, formdata, form, ctx) {
        console.log(
            " ********************** patient_status_pronote rule in patient ********************** "
        );
        try {
            if (
                (await this.fx.hasChanged(
                    ctx,
                    formdata,
                    form,
                    "last_stage_change"
                )) &&
                this.method == "UPDATE"
            ) {
                return false;
            }
            if (await this.fx.hasChanged(ctx, formdata, form, "cust_stage")) {
                let currentStage = null;
                if (formdata.id) {
                    currentStage = await this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "patient",
                        {
                            filter: [`id:${formdata.id}`],
                        }
                    );
                    currentStage = currentStage[0].cust_stage;
                }

                // TODO: do this as per csp(specific user)
                const user = formdata.updated_by;
                // if (user == 3356) {
                //     user = null;
                // }

                console.log(
                    "Creating PRONOTE - Patient Status Change: " +
                        formdata.cust_stage +
                        " for patient: " +
                        formdata.id
                );
                if (!currentStage) {
                    await transaction.insert("patient_note", {
                        patient_id: formdata.id,
                        subject:
                            "Patient Status Updated: " + formdata.cust_stage,
                        note:
                            "Patient Status updated to: " + formdata.cust_stage,
                        user_id: user ? user : ctx.user.id,
                    });
                } else {
                    await transaction.insert("patient_note", {
                        patient_id: formdata.id,
                        subject:
                            "Patient Status Updated: " + formdata.cust_stage,
                        note:
                            "Patient Status changed from " +
                            currentStage +
                            " to " +
                            formdata.cust_stage,
                        user_id: user ? user : ctx.user.id,
                    });
                }
                await transaction.update(
                    "patient",
                    {
                        last_stage_change: moment().format("MM/DD/YYYY"),
                    },
                    formdata.id
                );
            }
        } catch (e) {
            const errorMessage = `Error encountered while running rule patient_status_pronote for patient. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
