"use strict";
const _ = require("lodash");

module.exports = class EncounterRuleClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.fx = nes.modules.fx;
    }
    async pat_sch_on_hold(_transaction, formdata, _form, _user) {
        console.log("pat_sch_on_hold rule in encounter");
        try {
            let recentEncounter = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "encounter",
                {
                    filter: [`patient_id:${formdata.patient_id}`],
                    sort: "-created_on",
                }
            );
            recentEncounter = recentEncounter[0];
            if (
                (typeof recentEncounter.cust_contact_reason == "string" &&
                    recentEncounter.cust_contact_reason == "On Hold") ||
                (typeof recentEncounter.cust_contact_reason == "object" &&
                    recentEncounter.cust_contact_reason?.join("") ==
                        "On Hold" &&
                    recentEncounter.archived != true)
            ) {
                const sql = `
                        UPDATE form_schedule_event
                        SET on_hold = 'Yes'
                        WHERE patient_id = ${recentEncounter.patient_id} RETURNING id
                    `;
                await this.db.env.rw.query(sql);
            } else {
                const sql = `
                        UPDATE form_schedule_event
                        SET on_hold = 'No'
                        WHERE patient_id = ${recentEncounter.patient_id} RETURNING id
                    `;
                await this.db.env.rw.query(sql);
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error in rule pat_sch_on_hold in encounter. Please contact support.`;
            console.log(e);
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: formdata,
                error: returnError.error,
            };
        }
    }

    async encounter_code_ad(transaction, formdata, _form, _user) {
        try {
            const update_fields = {
                code_status: formdata.code_status,
                // advanced_directive: formdata.advanced_directive, // old envoy field
            };

            if (formdata.advanced_directive == "Yes") {
                let patient = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "patient",
                    {
                        filter: [`id:${formdata.patient_id}`],
                    }
                );
                patient = patient[0];
                const ptHealthDeclaration = patient.health_declaration;
                if (
                    !patient.health_declaration.includes("Advanced Directives")
                ) {
                    ptHealthDeclaration.push("Advanced Directives");
                    update_fields["health_declaration"] = ptHealthDeclaration;
                }
            }
            await transaction.update(
                "patient",
                update_fields,
                formdata.patient_id
            );
        } catch (e) {
            const errorMessage = `Error in rule encounter_code_ad in encounter. Please contact support.`;
            console.log(e);
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: formdata,
                error: returnError.error,
            };
        }
    }
};
