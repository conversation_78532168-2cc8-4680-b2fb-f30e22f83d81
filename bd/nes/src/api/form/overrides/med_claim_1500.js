"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
    }
    async before_checks(_transaction, body, _form, user) {
        console.log("Med Claim 1500 save override");
        if (body?.void === "Yes") body.archived = true;

        if (!body?.parent_id) {
            if (body?.is_test === "Yes") return null;
            return {
                error: "Cannot add or edit Medical claim outside of an invoice",
            };
        }

        const parent = await this.form.get.get_form(
            this.ctx,
            user,
            "billing_invoice",
            body.parent_id
        );

        if (parent.close_no) {
            return {
                error: "Period is closed, cannot add or edit Medical claim",
            };
        }
    }
};
