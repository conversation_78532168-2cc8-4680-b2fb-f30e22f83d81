"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
    }

    async before_op(transaction, data, _form, _user) {
        data.price_code_formulas = (data.price_code_formulas || []).map(
            (formula) => {
                if (!formula.code) {
                    formula.code = data.name;
                }
                formula.contract_type = data.contract_type;
                return formula;
            }
        );
        if (data.id) {
            return { data };
        }
        if (!data.contract_type) {
            throw new Error("Contract Type is required");
        }
        if (!data.assigned_matrix_id) {
            const matrix_id = await transaction.insert("payer_price_matrix", {
                name: data.name,
            });
            data.assigned_matrix_id = matrix_id;
            if (data.copied_from_matrix_id) {
                const fields_to_copy = [
                    "inventory_id",
                    "price_code_id",
                    "special_price_formula_id",
                    "override_special_formula_id",
                    "expected_price_formula_id",
                    "override_expected_formula_id",
                    "auto_name",
                    "hcpc_code",
                    "special_code",
                    "type",
                    "billable",
                    "not_covered",
                    "auth_required",
                    "modifier_1",
                    "modifier_2",
                    "modifier_3",
                    "modifier_4",
                    "last_cost",
                    "awp_price",
                    "wac_price",
                    "list_price",
                    "limits",
                    "refills_limit",
                    "unit_limit",
                    "limit_freq",
                    "special_price_multiplier",
                    "override_special_price_multiplier",
                    "special_price",
                    "expected_price_multiplier",
                    "override_expected_price_multiplier",
                    "expected_price",
                    "device_sale_price_special",
                    "device_sale_price_expected",
                    "daily_rental_price_special",
                    "daily_rental_price_expected",
                    "monthly_rental_price_special",
                    "monthly_rental_price_expected",
                ];
                const paramCount = transaction.getParamCount();
                transaction.setParamCount(paramCount + 3);
                transaction.rawSql(
                    `
                    INSERT INTO form_payer_price_matrix_item (
                        ${fields_to_copy.map((field) => `"${field}"`).join(", ")}, created_on, created_by, payer_price_matrix_id, contract_type
                    
                    )
                    SELECT
                        ${fields_to_copy.map((field) => `"${field}"`).join(", ")}, now() as created_on, $${paramCount + 1} AS created_by, ${matrix_id} AS payer_price_matrix_id, $${paramCount + 2} AS contract_type
                    FROM form_payer_price_matrix_item
                    WHERE payer_price_matrix_id = $${paramCount + 3}; 
                    `,
                    [
                        this.ctx.user.id,
                        data.contract_type,
                        data.copied_from_matrix_id,
                    ]
                );
            }
        }

        return { data };
    }
};
