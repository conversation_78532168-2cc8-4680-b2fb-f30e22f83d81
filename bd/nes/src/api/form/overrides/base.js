"use strict";
const _ = require("lodash");

module.exports = class BaseOverride {
    constructor(nes, ctx, method) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.frequency_hash = {};
        this.method = method;
    }

    async before_checks(_transaction, data, _form, _ctx) {
        console.log("BEFORE CHECKS OF BASE OVERRIDE");
        return data;
    }
    async before_op(_transaction, data, _form, _ctx) {
        console.log("BEFORE SAVE OF BASE OVERRIDE");
        return { data, error: null, formOverride: null };
    }
    async before_save(_transaction, data, _form, _ctx) {
        console.log("BEFORE SAVE OF BASE OVERRIDE");
        return { data, error: null };
    }
    async after_save(_transaction, data, _form, _ctx) {
        console.log("AFTER SAVE OF BASE OVERRIDE");
        return { data, error: null };
    }
    async after_transform(_transaction, data, _form, _ctx) {
        console.log("AFTER TRANSFORM OF BASE OVERRIDE");
        return { data, error: null };
    }
    async before_get(data, _form, _ctx) {
        console.log("BEFORE GET OF BASE OVERRIDE");
        return { data, error: null };
    }
};
