"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
    }

    async before_checks(transaction, medClaimRec, form, user) {
        console.log("Medical Claim save override");

        try {
            if (!medClaimRec.id) {
                return;
            }
            if (medClaimRec?.void === "Yes") medClaimRec.archived = true;

            if (!medClaimRec?.parent_id) {
                if (medClaimRec?.is_test === "Yes") return null;
                return {
                    error: "Cannot add or edit Medical claim outside of an invoice",
                };
            }

            const parent = await this.form.get.get_form(
                this.ctx,
                user,
                "billing_invoice",
                medClaimRec.parent_id
            );

            if (parent.close_no) {
                return {
                    error: "Period is closed, cannot add or edit Medical claim",
                };
            }

            return null;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for medical claim record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
