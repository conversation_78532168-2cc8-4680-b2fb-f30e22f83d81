"use strict";

const BaseClass = require("./base");
const BillingCloseClass = require("../../billing/close");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
    }

    async before_checks(transaction, closingRec, _form, _user) {
        console.log("Billing Closing save override");

        try {
            if (closingRec.needs_refreshed === "Yes") {
                return {
                    warning: "Period needs refreshed before saving.",
                };
            }
            return null;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for billing closing record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    async before_op(transaction, closingRec, form, ctx) {
        console.log("Billing Closing save override");

        try {
            if (closingRec.locked_period === "Yes") {
                if (closingRec.locked_period === "Yes") {
                    const closer = new BillingCloseClass(this.nes, this.ctx);
                    await closer.preSaveCloseProcessing(
                        transaction,
                        closingRec
                    );
                }
            }
            if (closingRec?.void === "Yes") closingRec.archived = true;

            return {
                data: closingRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for billing closing record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
