"use strict";
const _ = require("lodash");
const numeral = require("numeral");
const moment = require("moment-timezone");
const BaseClass = require("./base");
const {
    OrderItemTypes,
    OrderStatus,
    OrderFormat,
    FactorDosingPrescriptionType,
} = require("@dispense/settings");
module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const DispenseHelperClass = require("@dispense/helper");
        this.helper = this.fx.getInstance(
            ctx,
            DispenseHelperClass,
            true,
            this.nes,
            this.ctx
        );
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    async before_op(transaction, orderRec, _form, _ctx) {
        try {
            if (!orderRec.order_no) {
                orderRec.order_no = transaction.series_next_number("ORDER");
            }

            if (orderRec?.void !== "Yes") {
                await Promise.all([
                    this.helper.checkOrderNeedsPrescription(
                        transaction,
                        orderRec
                    ),
                    this.__buildSummaries(this.ctx, orderRec),
                ]);
                return {
                    data: orderRec,
                    error: null,
                    formOverride: null,
                };
            }
            const dispensesFilters = [
                `order_no:${orderRec.order_no}`,
                `status:Confirmed`,
                `void:!Yes`,
            ];
            const rxDispRec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "careplan_order_rx_disp",
                {
                    filter: dispensesFilters,
                }
            );
            if (rxDispRec.length > 0) {
                return {
                    data: null,
                    error: "Cannot void order with confirmed dispenses.",
                };
            }
            const orderItems = [
                ...(orderRec.subform_order || []),
                ...(orderRec.subform_single_order || []),
            ];
            for (const orderItem of orderItems) {
                if (
                    ![
                        OrderStatus.ACTIVE,
                        OrderStatus.PENDING,
                        OrderStatus.ON_HOLD,
                    ].includes(orderItem.status_id)
                ) {
                    continue;
                }
                orderItem.status_id = OrderStatus.DISCONTINUED;
                orderItem.discontinued_date = moment().format("MM/DD/YYYY");
                if (!orderItem.rx_no) {
                    continue;
                }
                const rxFilters = [`rx_no:${orderItem.rx_no}`];
                const rxRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_order_rx",
                        {
                            filter: rxFilters,
                        }
                    )
                );
                if (rxRec) {
                    rxRec.discontinued = "Yes";
                    await transaction.update(
                        "careplan_order_rx",
                        rxRec,
                        rxRec.id
                    );
                }

                const workingDispenseFilters = [
                    `rx_no:${orderItem.rx_no}`,
                    `void:!Yes`,
                    `status:!Confirmed`,
                ];
                const workingDispenseRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_order_rx_disp",
                        {
                            limit: 1,
                            sort: "-id",
                            filter: workingDispenseFilters,
                        }
                    )
                );
                if (!workingDispenseRec) {
                    continue;
                }
                workingDispenseRec.status = "Void";
                await transaction.update(
                    "careplan_order_rx_disp",
                    workingDispenseRec,
                    workingDispenseRec.id
                );
                const invoiceId = workingDispenseRec.inv_id;
                if (invoiceId) {
                    const invoiceFilters = [`id:${invoiceId}`, `void:!Yes`];
                    const invoiceRec = _.head(
                        await this.form.get.get_form(
                            this.ctx,
                            this.ctx.user,
                            "billing_invoice",
                            {
                                filter: invoiceFilters,
                            }
                        )
                    );
                    if (invoiceRec?.status === "Open") {
                        return {
                            data: null,
                            error: "Cannot void therapy set with an open invoice.",
                        };
                    }
                }
                const workingDeliveryTickets =
                    workingDispenseRec.delivery_ticket_id;
                if (!workingDeliveryTickets) {
                    continue;
                }
                const deliveryTicketFilters = [
                    `id:${workingDeliveryTickets}`,
                    `void:!Yes`,
                ];
                const deliveryTicketRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_delivery_tick",
                        {
                            filter: deliveryTicketFilters,
                        }
                    )
                );
                if (!deliveryTicketRec) {
                    continue;
                }
                const pulledItemsFilters = [
                    `ticket_no:${deliveryTicketRec.ticket_no}`,
                    `void:!Yes`,
                ];
                const pulledItemsRecs = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "careplan_dt_wt_pulled",
                    {
                        filter: pulledItemsFilters,
                    }
                );
                if (pulledItemsRecs.length > 0) {
                    return {
                        data: null,
                        error: "Cannot void therapy set with a working delivery ticket that has pulled inventory. Please void the delivery ticket first.",
                    };
                }
                deliveryTicketRec.void = "Yes";
                await transaction.update(
                    "careplan_delivery_tick",
                    deliveryTicketRec,
                    deliveryTicketRec.id
                );
            }

            if (orderRec.order_format === OrderFormat.SINGLE) {
                orderRec.single_rx_dc = "Yes";
            } else {
                orderRec.therapy_set_dc = "Yes";
            }
            return { data: orderRec, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while saving referral record.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: orderRec,
                error: returnError.error,
            };
        }
    }

    /**
     * Builds summaries for prescriptions.
     * @async
     * @param {Object} orderRec - The order record containing prescriptions.
     * @returns {Promise<void>}
     */
    async __buildSummaries(ctx, orderRec) {
        console.log("Build prescription summaries");
        try {
            const orderItems = [
                ...(orderRec.subform_order || []),
                ...(orderRec.subform_single_order || []),
                ...(orderRec.subform_single_supply || []),
            ];
            orderRec.summary = orderRec.therapy_id_auto_name;
            if (orderRec.order_format === OrderFormat.SUPPLY_ORDER) {
                const supplyItem = _.head(orderRec.subform_single_supply) || [];
                if (supplyItem) {
                    let summary = "";
                    if (supplyItem.inventory_id_auto_name) {
                        summary += supplyItem.inventory_id_auto_name;
                    }
                    if (supplyItem.dispense_quantity) {
                        summary += ` (${supplyItem.dispense_quantity}x)`;
                    }
                    orderRec.summary = summary;
                }
            }
            if (orderItems.length === 0) {
                return;
            }
            for (const orderItem of orderItems) {
                const templateType = orderItem.template_type || null;
                const frequencyId = orderItem.frequency_id || null;
                const frequencyRec = frequencyId
                    ? _.head(
                          await this.form.get.get_form(
                              ctx,
                              ctx.user,
                              "list_frequency",
                              {
                                  limit: 1,
                                  filter: ["code:" + frequencyId],
                              }
                          )
                      )
                    : "";
                const frequency = frequencyRec?.label_string || "";
                const doseUnit = orderItem?.dose_unit_id || "";
                const drugName = orderItem.inventory_id_auto_name || "";
                switch (templateType) {
                    case "Factor":
                        {
                            if (
                                orderItem.prescription_provided_in ===
                                FactorDosingPrescriptionType.RANGE
                            ) {
                                const dose1 = orderItem.dose_range_1
                                    ? numeral(orderItem.dose_range_1).format(
                                          "0,0"
                                      )
                                    : "";
                                const dose2 = orderItem.dose_range_2
                                    ? numeral(orderItem.dose_range_2).format(
                                          "0,0"
                                      )
                                    : "";
                                const dose =
                                    dose1 && dose2 ? `${dose1}-${dose2}` : "";
                                orderItem.summary =
                                    `${drugName}${dose ? ` ${dose}` : ""}${doseUnit ? ` ${doseUnit}` : ""}${frequency ? ` ${frequency}` : ""}`.trim();
                            } else {
                                const dose = orderItem.dose
                                    ? numeral(orderItem.dose).format("0,0")
                                    : "";
                                const variance = orderItem.allowed_variance
                                    ? numeral(
                                          orderItem.allowed_variance
                                      ).format("0,0") + "%"
                                    : "";
                                orderItem.summary =
                                    `${drugName}${dose ? " " + dose : ""}${doseUnit ? " " + doseUnit : ""}${frequency ? " " + frequency : ""}${variance ? " " + variance : ""}`.trim();
                            }
                        }
                        break;
                    default: {
                        const dose = orderItem.dose
                            ? await this.fx.buildUnitsAndUnitCodeString(
                                  this.ctx,
                                  orderItem.dose,
                                  orderItem.dose_unit_id
                              )
                            : "";
                        orderItem.summary =
                            `${drugName}${dose ? " " + dose : ""}${frequency ? " " + frequency : ""}`.trim();
                        break;
                    }
                }
                if (orderItem.type_id === OrderItemTypes.PRIMARY) {
                    orderRec.summary = orderItem.summary;
                }
            }
        } catch (e) {
            const errorMessage = `Error encountered while building order summary. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.wrapError(e, errorMessage);
        }
    }
};
