"use strict";
const _ = require("lodash");
const BaseClass = require("./base");
const { OrderTemplateType } = require("@dispense/settings");
module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const DispenseHelperClass = require("@dispense/helper");
        this.helper = this.fx.getInstance(
            ctx,
            DispenseHelperClass,
            true,
            this.nes,
            this.ctx
        );
    }

    async before_op(transaction, replacementLogRec, _form, _user) {
        console.log("Careplan DT Items Replacement");
        const { subform_replaced_items, patient_id, subform_original_items } =
            replacementLogRec;
        try {
            if (!subform_original_items.length) {
                throw new Error("Original items are required");
            }
            if (!subform_replaced_items.length) {
                console.log("No replaced items");
                return { data: replacementLogRec, error: null };
            }

            for (const item of subform_replaced_items) {
                const { rx_id, inventory_id, dispense_quantity } = item;
                if (!inventory_id) {
                    console.log("No inventory ID");
                    continue;
                }
                const rxRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_order_rx",
                        { filter: [`id:${rx_id}`] }
                    )
                );
                if (!rxRec) {
                    throw new Error("Rx Record not found");
                }
                const [
                    patientRec,
                    physicianRec,
                    labelRec,
                    inventoryRec,
                    frequencyRec,
                ] = await Promise.all([
                    this.form.get
                        .get_form(this.ctx, this.ctx.user, "patient", {
                            filter: [`id:${patient_id}`],
                        })
                        .then(_.head),
                    this.form.get
                        .get_form(this.ctx, this.ctx.user, "physician", {
                            filter: [`id:${rxRec?.physician_id}`],
                        })
                        .then(_.head),
                    this.form.get
                        .get_form(this.ctx, this.ctx.user, "list_label", {
                            filter: [
                                `site_id:${rxRec?.site_id}`,
                                `active:Yes`,
                                `label_format:${rxRec?.label_format}`,
                            ],
                        })
                        .then(_.head),
                    this.form.get
                        .get_form(this.ctx, this.ctx.user, "inventory", {
                            filter: [`id:${inventory_id}`],
                        })
                        .then(_.head),

                    rxRec.frequency_id
                        ? _.head(
                              await this.form.get.get_form(
                                  this.ctx,
                                  this.ctx.user,
                                  "list_frequency",
                                  { filter: [`id:${rxRec.frequency_id}`] }
                              )
                          )
                        : null,
                ]);

                if (!rxRec) {
                    throw new Error("Rx Record not found");
                }
                if (!inventoryRec) {
                    throw new Error("Inventory Record not found");
                }

                const labelSubform = await this.helper.__buildSubformLabel(
                    rxRec,
                    patientRec,
                    inventoryRec,
                    physicianRec
                );

                switch (rxRec.template_type) {
                    case OrderTemplateType.PO:
                    case OrderTemplateType.INJECTION:
                    case OrderTemplateType.FACTOR:
                        await this.updateRxRecord(
                            transaction,
                            rxRec,
                            inventoryRec,
                            dispense_quantity,
                            labelSubform,
                            labelRec,
                            frequencyRec
                        );
                        break;
                    case OrderTemplateType.IV:
                        await this.updateRxRecordForIV(
                            transaction,
                            rxRec,
                            inventoryRec,
                            dispense_quantity,
                            labelSubform,
                            labelRec,
                            frequencyRec
                        );
                        break;
                }
            }

            return { data: replacementLogRec, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before op for Careplan DT Items Replacement. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: replacementLogRec, error: returnError.error };
        }
    }

    async updateRxRecord(
        transaction,
        rxRec,
        inventoryRec,
        dispense_quantity,
        labelSubform,
        labelRec,
        frequencyRec
    ) {
        try {
            const { id: inventoryId } = inventoryRec;
            const updatedRxRec = {
                ...rxRec,
                inventory_id: inventoryId,
                compound_type: inventoryRec.compound_type,
                comp_dsg_fm_code: inventoryRec.comp_dsg_fm_code,
                auto_dc: inventoryRec.auto_dc,
                medid: inventoryRec.medid,
                gcn_seqno: inventoryRec.gcn_seqno,
                comp_disp_unit: inventoryRec.comp_disp_unit,
                dispense_quantity: dispense_quantity,
            };
            const frequencyLabel =
                frequencyRec?.frequency_label || frequencyRec?.name || null;
            const labelDesc =
                `${inventoryRec.label_name || inventoryRec.name || null} ${frequencyLabel ? frequencyLabel : ""}`.trim();
            updatedRxRec.rx_form_presc_order = labelDesc;
            updatedRxRec.label_subform = [labelSubform];
            console.log("updatedRxRec", updatedRxRec);
            await transaction.update(
                "careplan_order_rx",
                updatedRxRec,
                rxRec.id
            );
            transaction.commit();
            if (labelRec) {
                updatedRxRec.label_id = labelRec.id;
            }
        } catch (error) {
            const errorMessage = `Error encountered while attempting to update Rx Record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    async updateRxRecordForIV(
        transaction,
        rxRec,
        inventoryRec,
        dispense_quantity,
        labelSubform,
        labelRec,
        frequencyRec
    ) {
        try {
            const { id: inventoryId } = inventoryRec;
            const updatedRxRec = {
                ...rxRec,
                inventory_id: inventoryId,
                compound_type: inventoryRec.compound_type,
                comp_dsg_fm_code: inventoryRec.comp_dsg_fm_code,
                auto_dc: inventoryRec.auto_dc,
                medid: inventoryRec.medid,
                gcn_seqno: inventoryRec.gcn_seqno,
                comp_disp_unit: inventoryRec.comp_disp_unit,
                dispense_quantity: dispense_quantity,
            };
            const frequencyLabel =
                frequencyRec?.frequency_label || frequencyRec?.name || null;
            const labelDesc =
                `${inventoryRec.label_name || inventoryRec.name || null} ${frequencyLabel ? frequencyLabel : ""}`.trim();
            updatedRxRec.rx_form_presc_order = labelDesc;
            updatedRxRec.label_subform = [labelSubform];
            const wtRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "careplan_order_rx_wt",
                { filter: [`rx_no:${rxRec.rx_no}`] }
            );
            if (wtRecs.length) {
                for (const wtRec of wtRecs) {
                    await transaction.update(
                        "careplan_order_rx_wt",
                        {
                            ...wtRec,
                            medid: inventoryRec.medid,
                            inventory_id: inventoryRec.id,
                        },
                        wtRec.id
                    );
                }
            }
            await transaction.update(
                "careplan_order_rx",
                updatedRxRec,
                rxRec.id
            );
            transaction.commit();
            if (labelRec) {
                updatedRxRec.label_id = labelRec.id;
            }
        } catch (error) {
            const errorMessage = `Error encountered while attempting to update Rx Record for IV. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }
};
