"use strict";
const BaseClass = require("./base");
const currency = require("currency.js");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
    }

    async before_op(transaction, poRec, form, ctx) {
        try {
            await this.__updateInventory(ctx, poRec, transaction);
            return {
                data: poRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error in save override for PO. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: poRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }

    async __updateInventory(ctx, data, transaction) {
        const updatePromises = [];
        if (data.subform_items && data.subform_items.length > 0) {
            for (const subform_item of data.subform_items) {
                if (
                    subform_item &&
                    typeof subform_item == "object" &&
                    subform_item !== null
                ) {
                    const last_cost = currency(
                        currency(subform_item?.package_cost || 0).value /
                            parseFloat(subform_item?.qty_pkg || 0)
                    ).value;
                    updatePromises.push(
                        transaction.update(
                            "inventory",
                            {
                                last_cost: last_cost,
                                last_supplier_id: data?.supplier_id,
                            },
                            subform_item?.inventory_id
                        )
                    );
                }
            }
        }
        if (updatePromises.length > 0) await Promise.all(updatePromises);
    }
};
