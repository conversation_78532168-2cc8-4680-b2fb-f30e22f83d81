"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx, method) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.method = method;
    }

    async before_op(transaction, formdata, form, ctx) {
        console.log("Admission Discharge Audit save override");
        try {
            let createAudit = true;
            if (this.method == "UPDATE") {
                createAudit = false;
                const admissionDischargePrev = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "admission_discharge",
                    {
                        filter: [`id:${formdata.id}`],
                    }
                );
                if (admissionDischargePrev.status != formdata.status) {
                    createAudit = true;
                }
            }

            if (createAudit) {
                await transaction.insert("admission_discharge_audit", {
                    status: formdata.status,
                    patient_id: formdata.patient_id,
                    therapy_1: formdata.therapy_1,
                });
            }

            return {
                data: formdata,
                error: null,
                formOverride: false,
            };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before op for Admission Discharge Audit. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: formdata,
                error: returnError.error,
            };
        }
    }
};
