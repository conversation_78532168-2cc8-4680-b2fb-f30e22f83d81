"use strict";
const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    async before_op(_transaction, documentRec, _form, _user) {
        console.log("Document save override");

        try {
            if (documentRec?.patient_id) {
                if (documentRec?.site_id) {
                    return {
                        data: documentRec,
                        error: null,
                        formOverride: null,
                    };
                }
            }

            const formName = documentRec?.form_name || null;
            const formCode = documentRec?.form_code || null;
            if (!formName && !formCode) {
                return { data: documentRec, error: null };
            }

            if (formName && formCode) {
                const formRec = await this.fetcher.fetchCachedRecord(
                    formName,
                    formCode,
                    true
                );
                if (formName === "site") {
                    documentRec.site_id = formRec.code;
                } else {
                    documentRec.patient_id =
                        documentRec?.patient_id || formRec?.patient_id || null;
                    documentRec.site_id =
                        documentRec?.site_id || formRec?.site_id || null;
                    if (!documentRec.site_id) {
                        const patientRec = await this.fetcher.fetchCachedRecord(
                            "patient",
                            documentRec.patient_id
                        );
                        documentRec.site_id = patientRec?.site_id || null;
                    }
                }
            }
            return { data: documentRec, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before operations for Document. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: documentRec,
                error: returnError.error,
            };
        }
    }
};
