"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
    }
    async before_op(transaction, refundRec, form, ctx) {
        console.log("Billing Refund save override");

        try {
            if (!refundRec.refund_no) {
                refundRec.refund_no = transaction.series_next_number("REFUND");
            }
            return refundRec;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for billing refund record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
