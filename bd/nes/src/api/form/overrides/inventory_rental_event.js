"use strict";
const _ = require("lodash");

const BaseClass = require("./base");
module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    async before_op(transaction, eventRec, _form, _ctx) {
        try {
            const { data, error } = await this.checkAndUpdateRentalLog(
                eventRec,
                transaction
            );
            if (error) throw new Error(error);
            else
                return {
                    data: data,
                    error: error,
                    formOverride: null,
                };
        } catch (e) {
            const errorMessage = `Error encountered while saving rental event record.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: eventRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }

    async checkAndUpdateRentalLog(eventRec, transaction) {
        try {
            if (!eventRec.rental_id) throw new Error("Rental ID Not found");
            switch (eventRec.type) {
                case "Check":
                    await transaction.update(
                        "inventory_rental_log",
                        {
                            next_check_date: eventRec.next_check_date,
                        },
                        eventRec.rental_id
                    );
                    break;

                case "Preventative Maintenance":
                    await transaction.update(
                        "inventory_rental_log",
                        {
                            next_pm_date: eventRec.next_pm_date,
                        },
                        eventRec.rental_id
                    );
                    break;
                default:
                    throw new Error("Invalid event type");
            }
            return { data: eventRec, error: null };
        } catch (error) {
            const errorMessage = `Error encountered while saving rental event record.`;
            console.error(
                `${errorMessage} Error:${error.message} Stack:${error.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(
                error,
                errorMessage
            );
            return {
                data: eventRec,
                error: returnError.error,
            };
        }
    }
};
