"use strict";

const BaseClass = require("./base");
const PatientMedicationRuleClass = require("../rules/patient_medication");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx, method) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.method = method;
        this.patientMedicationRules = new PatientMedicationRuleClass(nes, ctx);
    }

    async after_save(transaction, formdata, _form, ctx) {
        try {
            await this.patientMedicationRules.patient_medication_status_update(
                transaction,
                formdata,
                _form,
                ctx
            );
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run after save for patient_medication. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
};
