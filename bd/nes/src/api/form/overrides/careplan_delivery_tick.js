"use strict";
const _ = require("lodash");
const BaseClass = require("./base");
const CareplanDeliveryTickRuleClass = require("../rules/careplan_delivery_tick");
const moment = require("moment");
const {
    DeliveryTicketStatus,
    RefillTrackingType,
    OrderStatus,
    DeliveryTicketBeforeConfirm,
} = require("@dispense/settings");
module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.isPost = false;
        this.isVoided = false;
        const DispenseGeneratorClass = require("@dispense/generator");
        this.generator = this.fx.getInstance(
            ctx,
            DispenseGeneratorClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingHelperClass = require("../billing/helper");
        this.helper = this.fx.getInstance(
            ctx,
            BillingHelperClass,
            true,
            this.nes,
            this.ctx
        );
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        this.careplanDeliveryTickRules = new CareplanDeliveryTickRuleClass(
            nes,
            ctx
        );
    }

    async before_op(transaction, deliveryTicketRec, _form, _user) {
        console.log("Careplan Delivery Ticket save override");

        try {
            const formId = deliveryTicketRec?.id ?? null;
            this.isPost = !formId;
            this.isVoided =
                await this.__checkIfVoidedOrArchived(deliveryTicketRec);

            let ogDeliveryTicketRec = null;
            if (formId) {
                const filters = [`id:${formId}`];
                ogDeliveryTicketRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_delivery_tick",
                        {
                            filter: filters,
                        }
                    )
                );
            }
            if (this.isVoided) {
                deliveryTicketRec.archived = true;
                const ogVoidedOrArchived = ogDeliveryTicketRec
                    ? await this.__checkIfVoidedOrArchived(ogDeliveryTicketRec)
                    : false;
                if (ogVoidedOrArchived) {
                    return {
                        error: "Delivery ticket was already voided or archived, cannot update",
                    };
                }
                return null;
            }

            const invoices = await this.fetcher.fetchDeliveryTicketInvoices(
                deliveryTicketRec.id
            );
            if (this.isVoided && invoices.length > 0) {
                if (deliveryTicketRec.confirmed === "Yes") {
                    return {
                        error: "Cannot void delivery ticket that is associated with an open invoice.",
                    };
                }
            }
            const slaveData = deliveryTicketRec._meta?.slave;
            console.log("Slave data", slaveData);
            console.log("Form ID", formId);
            if (slaveData) {
                let assessmentFormId = slaveData.record;
                const assessmentForm = slaveData.form;
                const assessmentFormData = slaveData.data;
                console.log("Assessment form ID", assessmentFormId);
                console.log("Assessment form", assessmentForm);
                console.log("Assessment form data", assessmentFormData);

                //TODO: Remove once fixed on the client.
                if (assessmentFormId === formId) {
                    console.log(
                        "Assessment form ID is the same as the delivery ticket ID"
                    );
                    assessmentFormId = null;
                    if (assessmentFormData?.id) {
                        console.log("Deleting assessment form data ID");
                        delete assessmentFormData.id;
                    }
                }
                if (assessmentForm && assessmentFormData) {
                    console.log("Adding assessment form to delivery ticket");
                    deliveryTicketRec["assessment_form"] = assessmentForm;
                    deliveryTicketRec["subform_assessment"] = [
                        { ...assessmentFormData, id: assessmentFormId },
                    ];
                    console.log("Delivery ticket", deliveryTicketRec);
                }
            }

            const deleteSubformConfirmation = (deliveryTicketRec) => {
                if (
                    Array.isArray(deliveryTicketRec?.subform_confirmation) &&
                    deliveryTicketRec?.subform_confirmation.length > 0
                ) {
                    const id = deliveryTicketRec.subform_confirmation[0]?.id;
                    if (id) {
                        deliveryTicketRec.subform_confirmation[0]._meta = {
                            delete: id,
                        };
                    }
                }
            };
            if (
                !(deliveryTicketRec.confirmed === "Yes") ||
                deliveryTicketRec?.void === "Yes" ||
                deliveryTicketRec?.archived === true ||
                deliveryTicketRec?.deleted === true
            ) {
                deleteSubformConfirmation(deliveryTicketRec);
                return {
                    data: deliveryTicketRec,
                    error: null,
                };
            } else if (
                DeliveryTicketBeforeConfirm.includes(deliveryTicketRec.status)
            ) {
                deleteSubformConfirmation(deliveryTicketRec);
            }
            await this.__generateTicketNo(transaction, deliveryTicketRec);
            return {
                data: deliveryTicketRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for Careplan Delivery Ticket. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: deliveryTicketRec,
                error: returnError.error,
            };
        }
    }

    async before_save(transaction, deliveryTicketRec, form, ctx) {
        try {
            await this.careplanDeliveryTickRules.patient_dispense_pronote(
                transaction,
                deliveryTicketRec,
                form,
                ctx
            );

            const ogDeliveryTicketRec = await this.fetcher.fetchCachedRecord(
                "careplan_delivery_tick",
                deliveryTicketRec.id
            );

            if (ogDeliveryTicketRec.confirmed === "Yes") {
                return {
                    data: deliveryTicketRec,
                    error: "Cannot edit a delivery ticket once confirmed.",
                };
            }
            if (
                !deliveryTicketRec.confirmed ||
                deliveryTicketRec.void === "Yes" ||
                deliveryTicketRec.archived === true ||
                deliveryTicketRec.deleted === true
            ) {
                return {
                    data: deliveryTicketRec,
                    error: null,
                };
            }
            await this.__handleConfirmation(
                transaction,
                deliveryTicketRec,
                ctx
            );
            return { data: deliveryTicketRec, error: null };
        } catch (e) {
            const errorMessage = `Error in save override for careplan devliery tick. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: deliveryTicketRec, error: returnError.error };
        }
    }

    async before_get(data, _form, ctx) {
        console.log("Careplan Delivery Ticket get override");

        try {
            for (const rec of data) {
                if (DeliveryTicketBeforeConfirm.includes(rec.status)) {
                    rec.subform_confirmation = [];
                }
                if (
                    [DeliveryTicketStatus.DT_CONFIRMATION].includes(rec.status)
                ) {
                    const confirmationSubform = rec.subform_confirmation || [];
                    if (confirmationSubform.length > 0) {
                        console.error(
                            `The confirmation subform and delivery ticket status are out of sync for DT ID ${rec.id}.`
                        );
                        return { data, error: null };
                    }

                    //TODO Check if user is allowed to confirm the delivery ticket before generating
                    const confirmationSfPresets =
                        await this.generator.generateDTConfirmedPresets(
                            rec.id,
                            ctx.user.id
                        );

                    rec.subform_confirmation = confirmationSfPresets;
                }
            }
            return { data, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before get for Careplan Delivery Ticket. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data, error: returnError.error };
        }
    }
    /**
     * Generates a ticket number for the careplan delivery ticket.
     * @async
     * @param {Object} transaction - The database transaction object.
     * @param {Object} deliveryTicketRec - The request body containing the form data.
     * @throws {Error} If there's an error in generating the ticket number.
     */
    async __generateTicketNo(transaction, deliveryTicketRec) {
        console.log("Generating ticket number");

        try {
            if (this.isPost) {
                const nextTicketNo = transaction.series_next_number("TICKET");
                deliveryTicketRec.ticket_no = nextTicketNo;
            }
        } catch (e) {
            const errorMessage = `Error encountered while generating ticket number for delivery ticket ID ${deliveryTicketRec?.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Handles pending orders for a delivery ticket.
     * @async
     * @param {Object} transaction - The database transaction object.
     * @param {Object} formdata - The form data containing order information.
     * @returns {Object} Object containing the updated formdata and any error.
     * @throws {Error} If there's an error processing the pending orders.
     */
    async __handlePendingOrders(transaction, formdata) {
        try {
            const rxIds = formdata.rx_id;
            if (!Array.isArray(rxIds) || rxIds.length === 0) {
                console.debug("No rx_ids found on delivery ticket");
                return { data: formdata, error: null };
            }

            // Fetch all careplan order rx records
            const careplanOrderRxRecs = await this.fetcher.fetchCachedRecords(
                "careplan_order_rx",
                rxIds
            );

            for (const careplanOrderRxRec of careplanOrderRxRecs) {
                const sql = `
                        SELECT * FROM vw_rx_order rx
                        WHERE rx.rx_no = '${careplanOrderRxRec.rx_no}'`;

                const rxOrderResult = await this.db.env.rw.query(sql);

                if (!rxOrderResult || rxOrderResult.length === 0) {
                    console.debug(
                        `No rx order found for rx_no: ${careplanOrderRxRec.rx_no}`
                    );
                    continue;
                }

                const rxOrder = rxOrderResult[0];
                const autoDc = rxOrder.auto_dc === "Yes";
                const refillsRemaining =
                    (rxOrder.refill_tracking === RefillTrackingType.REFILLS &&
                        parseInt(rxOrder.refills_remaining || 99) <= 0) ||
                    (rxOrder.refill_tracking === RefillTrackingType.DOSES &&
                        parseInt(rxOrder.doses_remaining || 99) <= 0);
                const newStatus =
                    autoDc && refillsRemaining
                        ? OrderStatus.DISCONTINUED
                        : OrderStatus.ACTIVE;
                const discontinuedDate =
                    autoDc && refillsRemaining
                        ? moment().format("MM/DD/YYYY")
                        : null;
                // If this is a refill, discontinue the original order items
                if (rxOrder.is_refill === "Yes" && rxOrder.refill_rx_id) {
                    const originalOrderItems = await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_order_item",
                        {
                            filter: [`refill_rx_id:${rxOrder.refill_rx_id}`],
                        }
                    );

                    // Discontinue original order items
                    const item = originalOrderItems[0];
                    await transaction.update(
                        "careplan_order_item",
                        {
                            ...item,
                            status_id: OrderStatus.DISCONTINUED,
                            discontinued_date: moment().format("MM/DD/YYYY"),
                        },
                        item.id
                    );
                }
                if (rxOrder.order_item_id) {
                    await transaction.update(
                        "careplan_order_item",
                        {
                            status_id: newStatus,
                            discontinued_date: discontinuedDate,
                        },
                        rxOrder.order_item_id
                    );
                } else if (rxOrder.orderp_item_id) {
                    await transaction.update(
                        "careplan_orderp_item",
                        {
                            status_id: newStatus,
                            discontinued_date: discontinuedDate,
                        },
                        rxOrder.orderp_item_id
                    );
                }
            }
        } catch (e) {
            const errorMessage = `Error encountered while handling pending orders.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
        }
    }

    /**
     * Processes invoice updates for delivery ticket confirmation
     * @async
     * @function __processInvoiceUpdates
     * @param {Object} transaction - The database transaction object
     * @param {Object} formdata - The delivery ticket form data
     * @param {Array<string>} uniqueInvoiceNos - Array of unique invoice numbers
     * @param {Array<Object>} linkedChargeLines - Array of charge line records linked to the delivery ticket
     * @returns {Promise<void>}
     * @throws {Error} If unable to process invoice updates
     */
    async __processInvoiceUpdates(
        transaction,
        formdata,
        uniqueInvoiceNos,
        linkedChargeLines
    ) {
        try {
            const updatePromises = [];
            if (uniqueInvoiceNos.length === 0) {
                return;
            }
            const mappedInvoiceFilters = uniqueInvoiceNos.map(
                (invoiceNo) => `invoice_no:${invoiceNo}`
            );
            const invoiceRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "billing_invoice",
                {
                    filter: mappedInvoiceFilters,
                }
            );
            const ticketNo = formdata.ticket_no;

            for (const invoiceRec of invoiceRecs) {
                const invoiceChargeLines = linkedChargeLines.filter(
                    (charge) => charge.invoice_no === invoiceRec.invoice_no
                );

                const payerRec = await this.fetcher.fetchCachedRecord(
                    "payer",
                    invoiceRec.payer_id
                );

                // Check if we have to add a major medical claim to the invoice, if this is set
                // we were only billing NCPDP for information purposes and it didn't actually send the claim
                // to the payer. This workflow is CSP specific
                let invoiceUpdates = {};
                const convertNcpdpToMm = payerRec?.bill_mm_and_ncpdp === "Yes";
                if (convertNcpdpToMm) {
                    const sql = `
                    SELECT build_mm_claim_from_ncpdp(%s::integer) AS result
                    `;
                    const rows = await this.db.env.rw.query(sql, [
                        invoiceRec.id,
                    ]);
                    const results = rows[0]?.result || null;
                    if (!results) {
                        throw new this.fx.getClaraError(
                            "Error encountered while building Medical Claim from NCPDP Claim. Please contact support."
                        );
                    }
                    const claimNo = transaction.series_next_number("CLAIM");
                    results.claim_no = claimNo;
                    results.invoice_no = invoiceRec.invoice_no;
                    await this.helper.setMedicalClaimNo(results, claimNo);
                    invoiceUpdates = {
                        claim_no: claimNo,
                        billing_method_id: "mm",
                        subform_medical: results,
                        total_paid: 0.00, //TODO: Add patient copay paid
                        total_insurance_paid: 0.00,
                        total_balance_due: invoiceRec.total_billed,
                    };
                }
                for (const chargeRec of invoiceChargeLines) {
                    let chargeLineUpdates = {};
                    if (convertNcpdpToMm) {
                        chargeLineUpdates = {
                            billing_method_id: "mm",
                            paid: 0.00,
                            balance_due: chargeRec.billed,
                        };
                    }
                    const dtItemRec = await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_dt_item",
                        {
                            filter: [
                                `ticket_no:${ticketNo}`,
                                `inventory_id:${chargeRec.inventory_id}`,
                            ],
                        }
                    );

                    const ticketItemNo = dtItemRec[0]?.ticket_item_no || null;

                    updatePromises.push(
                        transaction.update(
                            "ledger_charge_line",
                            {
                                locked: null,
                                locked_by: null,
                                locked_datetime: null,
                                ...chargeRec,
                                invoice_status: "Confirmed",
                                ticket_item_no: ticketItemNo,
                                ticket_no: ticketNo,
                                ...chargeLineUpdates,
                            },
                            chargeRec.id
                        )
                    );
                }

                updatePromises.push(
                    transaction.update(
                        "billing_invoice",
                        {
                            ...invoiceRec,
                            status: "Confirmed",
                            delivery_ticket_id: formdata.id,
                            billed_datetime: formdata.confirmed_datetime,
                            ...invoiceUpdates,
                        },
                        invoiceRec.id
                    )
                );
            }

            await Promise.all(updatePromises);
        } catch (e) {
            const errorMessage = `Error encountered while processing invoice updates.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Handles the confirmation process for a delivery ticket.
     * @async
     * @param {Object} transaction - The database transaction object.
     * @param {Object} formdata - The form data containing delivery ticket information.
     * @param {Object} ctx - The context object containing user information.
     * @throws {Error} If there's an error in the confirmation process.
     */
    async __handleConfirmation(transaction, formdata, _ctx) {
        console.log("Handling confirmation");

        try {
            const confirmationSubform =
                formdata?.subform_confirmation[0] || null;
            if (!confirmationSubform) {
                throw new this.fx.getClaraError(
                    "Missing delivery ticket confirmation subform. Cannot confirm ticket. Please contact support."
                );
            }

            const exceptions = confirmationSubform?.subform_exceptions || [];
            const unresolvedExceptions = exceptions.filter(
                (exception) => exception.resolved !== "Yes"
            );
            if (unresolvedExceptions.length > 0) {
                throw new this.fx.getClaraError(
                    "Cannot confirm delivery ticket with unresolved exceptions"
                );
            }
            const linkedCharges = confirmationSubform?.embed_charge_lines || [];
            const chargeIds = linkedCharges.map((charge) => charge.id) || [];
            const linkedChargeLines =
                chargeIds.length > 0
                    ? await this.fetcher.fetchCachedRecords(
                          "ledger_charge_line",
                          chargeIds
                      )
                    : [];
            const uniqueInvoiceNos = [
                ...new Set(
                    linkedChargeLines.map((charge) => charge.invoice_no)
                ),
            ];
            if (
                chargeIds.length > 0 &&
                (!uniqueInvoiceNos || uniqueInvoiceNos.length === 0)
            ) {
                throw this.fx.getClaraError(
                    "No invoice numbers found in linked charge lines"
                );
            }
            await this.__processInvoiceUpdates(
                transaction,
                formdata,
                uniqueInvoiceNos,
                linkedChargeLines
            );
        } catch (e) {
            const errorMessage = `Error encountered while handling confirmation for delivery ticket ID ${formdata?.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks if a record is voided or archived.
     * @async
     * @param {Object} deliveryTicketRec - The record to check.
     * @returns {Promise<boolean>} True if the record is voided or archived, false otherwise.
     * @throws {Error} If there's an error during the check.
     */
    async __checkIfVoidedOrArchived(deliveryTicketRec) {
        console.log("Checking if voided or archived");

        try {
            return (
                (typeof deliveryTicketRec.archived === "boolean" &&
                    deliveryTicketRec.archived === true) ||
                (typeof deliveryTicketRec.archived === "string" &&
                    deliveryTicketRec.archived.toLowerCase() === "true") ||
                deliveryTicketRec.void === "Yes"
            );
        } catch (e) {
            const errorMessage = `Error encountered while checking if voided or archived for delivery ticket ID ${deliveryTicketRec?.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
