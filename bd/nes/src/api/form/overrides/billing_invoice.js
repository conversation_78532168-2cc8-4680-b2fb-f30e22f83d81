"use strict";
const _ = require("lodash");
const currency = require("currency.js");
const moment = require("moment-timezone");
const BillingGeneratorClass = require("@billing/generator");
const BillingCheckerClass = require("@billing/checker");

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
        this.generator = new BillingGeneratorClass(nes, ctx);
        this.checker = new BillingCheckerClass(nes, ctx);
    }

    async before_checks(_transaction, invoiceRec, _form, _user) {
        console.log("Billing Invoice save override");

        try {
            if (invoiceRec.locked === "Yes") {
                const timeZone = this.ctx.user.timezone;
                const lockedTime = moment(invoiceRec.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                return {
                    warning: `Record is locked by ${invoiceRec.locked_by_auto_name} on ${lockedTime}`,
                };
            }

            return null;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for invoice record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    async before_op(transaction, invoiceRec, form, ctx) {
        if (invoiceRec.void === "Yes") invoiceRec.archived = true;

        invoiceRec.total_balance_due = currency(
            parseFloat(invoiceRec.total_expected || 0) -
                (invoiceRec.total_paid || 0) +
                (invoiceRec.total_adjusted || 0)
        ).value;
        return {
            data: invoiceRec,
            error: null,
            formOverride: null,
        };
    }

    async after_save(_transaction, formdata, _form, _ctx) {
        try {
            const isCOBEligible =
                await this.checker.checkIfInvoiceCOBEligible(formdata);
            if (isCOBEligible) {
                await this.generator.autogenerateNextCOBInvoice(formdata);
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error in after save override for invoice. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            return { data: formdata, error: null };
        }
    }
};
