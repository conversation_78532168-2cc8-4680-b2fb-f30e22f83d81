"use strict";
const BaseClass = require("./base");
const { InsuranceTypes } = require("@dispense/settings");

module.exports = class PatientOverrides extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
    }

    async before_op(transaction, insuranceRec, _form, _user) {
        try {
            const res = await this.__handleRanks(transaction, insuranceRec);
            if (res.error) throw new Error(res.error);
            return {
                data: insuranceRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error in save override for patient insurance. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: insuranceRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }

    /**
     * Handles the ranking of patient insurances.
     * @param {Object} transaction - The database transaction object.
     * @param {Object} insuranceRec - The insurance record to be processed.
     * @returns {Promise<Object>} An object potentially containing an error message.
     */
    async __handleRanks(transaction, insuranceRec) {
        try {
            if (insuranceRec.active !== "Yes") {
                // Clear out the rank if not active
                insuranceRec.rank = null;
                return {};
            }
            if (insuranceRec.type_id === InsuranceTypes.SELF_PAY) {
                insuranceRec.rank = 0;
                return {};
            }
            const rank = insuranceRec.rank || null;
            const insuranceId = insuranceRec.id || null;
            if (!rank) {
                return {
                    error: "Missing insurance rank",
                };
            }
            const patientInsurances = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "patient_insurance",
                {
                    filter: [`patient_id:${insuranceRec.patient_id}`],
                }
            );
            const insuranceClashRec = patientInsurances.filter(
                (pi) => pi.rank === rank && pi.id !== insuranceId
            );
            if (insuranceClashRec.length === 0) {
                return {};
            }

            const recsToUpdate = patientInsurances.filter(
                (pi) => pi.id !== insuranceId && pi.rank >= rank
            );

            const updatePromises = [];
            recsToUpdate.forEach(async (pi) => {
                const newRank = parseInt(pi.rank) + 1;
                const { payerLevel, otherPayerLevel } =
                    this.__getInsuranceLevelFields(newRank);

                updatePromises.push(
                    transaction.update(
                        "patient_insurance",
                        {
                            ...pi,
                            payer_level: payerLevel,
                            other_payer_level: otherPayerLevel,
                            rank: newRank,
                        },
                        pi.id
                    )
                );
            });
            if (updatePromises.length > 0) await Promise.all(updatePromises);
            return {};
        } catch (e) {
            const errorMessage = `Error encountered while attempting to handle ranks for patient insurance. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return this.fx.getReturnErrorMessage(e, errorMessage);
        }
    }

    /**
     * Determines the payer level fields based on the insurance rank.
     * @param {number} rank - The rank of the insurance.
     * @returns {Object} An object containing payerLevel and otherPayerLevel.
     */
    __getInsuranceLevelFields(rank) {
        const payerLevel =
            rank === 2 ? "Secondary" : rank === 3 ? "Tertiary" : "Other";
        const otherPayerLevel = rank < 4 || rank > 8 ? null : rank.toString();
        return { payerLevel, otherPayerLevel };
    }
};
