"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
    }

    async before_checks(_transaction, data, _form, _user) {
        try {
            if (data.payer_id) {
                return { break: true, data };
            }
            return data;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for billing closing record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
    async before_op(transaction, data, _form, _user) {
        if (data.payer_id) {
            return {
                error: "Payer already exists",
            };
        }
        console.log("NO PAYER ATTACHED CREATING NEW PAYER");
        data.payer_id = await transaction.insert("payer", {
            pcn: data.pcn,
            bin: data.bin,
            phone: data.phone,
            billing_method_id: data.billing_method_id,
            type_id: data.type_id,
        });
        return { data };
    }
};
