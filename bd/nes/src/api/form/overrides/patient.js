"use strict";
const _ = require("lodash");
const BaseClass = require("./base");
const PatientRuleClass = require("../rules/patient");

module.exports = class PatientOverrides extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.ctx = ctx;
        this.patientRules = new PatientRuleClass(nes, ctx);
    }

    async before_op(transaction, patientRec, form, ctx) {
        try {
            if (ctx.request.method == "POST") {
                patientRec.mrn = transaction.series_next_number("MRN");
            }
            return {
                data: patientRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error in save override for patient. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: patientRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }

    async before_save(transaction, patientRec, form, ctx) {
        try {
            await this.add_patient_prescriber(ctx, patientRec, transaction);
            await this.patientRules.patient_discharged(
                transaction,
                patientRec,
                form,
                ctx
            );
            await this.patientRules.patient_status_pronote(
                transaction,
                patientRec,
                form,
                ctx
            );
            return { data: patientRec, error: null };
        } catch (e) {
            const errorMessage = `Error in save override for patient. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: patientRec, error: returnError.error };
        }
    }

    async add_patient_prescriber(ctx, data, transaction) {
        if (!data.primary_physician_id) {
            return;
        }
        const primary_phys_filters = [
            `physician_id:${data.primary_physician_id}`,
            `patient_id:${data.id}`,
        ];
        const presc_rec = _.head(
            await this.form.get.get_form(ctx, ctx.user, "patient_prior_auth", {
                limit: 1,
                sort: "-id",
                filter: primary_phys_filters,
            })
        );
        if (!presc_rec) {
            const new_prescriber = {
                patient_id: data.id,
                primary: "Yes",
                physician_id: data.primary_physician_id,
            };
            await transaction.insert("patient_prescriber", new_prescriber);
        }
    }
};
