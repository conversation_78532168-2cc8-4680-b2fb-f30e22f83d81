"use strict";

const BaseClass = require("./base");
const OngoingRuleClass = require("../rules/ongoing");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx, method) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.method = method;
        this.ongoingRules = new OngoingRuleClass(nes, ctx, this.method);
    }

    async after_save(transaction, formdata, _form, ctx) {
        try {
            if (this.method == "UPDATE") {
                await this.ongoingRules.ongoing_reviewed_id(
                    transaction,
                    formdata,
                    _form,
                    ctx
                );
            }
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run after save for ongoing. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    async before_save(transaction, formdata, form, ctx) {
        console.log("Before save for ongoing override");
        try {
            await this.ongoingRules.assessment_add_dur(
                transaction,
                formdata,
                form,
                ctx,
                this.method
            );
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error in before save override for ongoing. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: formdata, error: returnError.error };
        }
    }
};
