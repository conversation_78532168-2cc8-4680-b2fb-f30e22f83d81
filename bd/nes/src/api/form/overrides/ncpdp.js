"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
    }

    async before_checks(transaction, ncpdpRec, form, user) {
        console.log("NCPDP save override");

        try {
            if (!ncpdpRec.id) {
                return;
            }
            if (ncpdpRec?.void === "Yes") ncpdpRec.archived = true;

            if (ncpdpRec?.invoice_no) {
                return {
                    error: true,
                    message:
                        "Cannot add or edit NCPDP claim outside of an invoice",
                };
            }

            const parent = await this.form.get.get_form(
                this.ctx,
                user,
                "billing_invoice",
                ncpdpRec.parent_id
            );

            if (parent.close_no) {
                return {
                    error: "Period is closed, cannot add or edit NCPDP claim",
                };
            }

            return null;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for NCPDP record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: ncpdpRec,
                error: returnError.error,
            };
        }
    }
};
