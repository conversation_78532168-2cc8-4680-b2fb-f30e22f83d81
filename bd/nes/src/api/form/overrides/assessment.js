"use strict";

const BaseClass = require("./base");
const AssessmentRuleClass = require("../rules/assessment");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx, method) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.method = method;
        this.assessmentRules = new AssessmentRuleClass(nes, ctx, this.method);
        const dispenseFetcher = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            dispenseFetcher,
            true,
            this.nes,
            this.ctx
        );
    }

    async before_save(transaction, formdata, form, ctx) {
        console.log("Before save for assessment override");
        try {
            // get careplan delivery tick id

            const deliveryTick = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_delivery_tick",
                formdata.careplan_delivery_tick_id
            );
            await this.__updateCareplanDeliveryTick(transaction, deliveryTick);
            await this.assessmentRules.assessment_add_dur(
                transaction,
                formdata,
                form,
                ctx
            );
            await this.assessmentRules.opt_out_physician_office(
                transaction,
                formdata,
                form,
                ctx
            );
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error in before save override for assessment. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: formdata, error: returnError.error };
        }
    }

    async __updateCareplanDeliveryTick(transaction, deliveryTicketRec) {
        console.log("Updating careplan delivery tick");
        try {
            const rxIds = deliveryTicketRec.rx_id;
            if (!Array.isArray(rxIds) || rxIds.length === 0)
                throw this.fx.getClaraError(`Delivery ticket has no rx_id.`);

            const careplanOrderRxRecs = await this.fetcher.fetchCachedRecords(
                "careplan_order_rx",
                rxIds
            );
            const labels = [];
            careplanOrderRxRecs.forEach((rec) => {
                labels.push({
                    label_form: rec.label_form,
                    subform_label: rec.subform_label,
                    rx_no: rec.rx_no,
                    inventory_id: rec.inventory_id,
                    number_of_labels: rec.number_of_labels,
                });
            });
            deliveryTicketRec.labels = labels;
            await transaction.update(
                "careplan_delivery_tick",
                deliveryTicketRec,
                deliveryTicketRec.id
            );
        } catch (e) {
            const errorMessage = `Error encountered while updating careplan delivery tick for delivery ticket ID ${deliveryTicketRec?.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
        }
    }
};
