"use strict";
const _ = require("lodash");
const moment = require("moment-timezone");

const BaseClass = require("./base");
const { EncounterStatus } = require("@dispense/settings");
const EncounterRuleClass = require("../rules/encounter");
const BillingGeneratorClass = require("@billing/generator");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.needsBillable = false;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
        this.encounterRules = new EncounterRuleClass(nes, ctx);
    }
    async before_op(transaction, encounterRec, form, user) {
        console.log("Encounter record before op save override");

        try {
            if (
                encounterRec.ready_for_review === "Yes" &&
                ![EncounterStatus.APPROVED, EncounterStatus.DENIED].includes(
                    encounterRec.review_status
                )
            ) {
                encounterRec.review_status = EncounterStatus.READY_FOR_REVIEW;
            } else if (!encounterRec.ready_for_review) {
                encounterRec.review_status = EncounterStatus.PENDING;
            }

            if (encounterRec.locked === "Yes") {
                const timeZone = this.ctx.user.timezone;
                const lockedTime = moment(encounterRec.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                return {
                    data: encounterRec,
                    error: `Record is locked by ${encounterRec.locked_by_auto_name} on ${lockedTime}`,
                    formOverride: null,
                };
            }
            const formId = encounterRec?.id ?? null;
            const isApproved =
                encounterRec.review_status === EncounterStatus.APPROVED;

            if (formId) {
                const filters = [`id:${formId}`];
                const encounterRec = _.head(
                    await this.form.get.get_form(this.ctx, user, "encounter", {
                        filter: filters,
                    })
                );
                if (encounterRec.locked === "Yes") {
                    return {
                        error:
                            "Record is locked by " +
                            encounterRec.locked_by_auto_name +
                            " on " +
                            encounterRec.locked_datetime,
                        formOverride: null,
                        data: encounterRec,
                    };
                }

                const wasApproved =
                    encounterRec.review_status === EncounterStatus.APPROVED;

                if (!wasApproved && isApproved) {
                    this.needsBillable = true;

                }
            } else if (isApproved) {
                this.needsBillable = true;
            }
            return {
                data: encounterRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error in save override for encounter posting. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: encounterRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }

    async after_save(transaction, formdata, _form, ctx) {
        try {
            await this.encounterRules.encounter_code_ad(
                transaction,
                formdata,
                _form,
                ctx.user
            );

            await this.encounterRules.pat_sch_on_hold(
                transaction,
                formdata,
                _form,
                ctx.user
            );

            if (this.needsBillable) {
                return await this.__checkNeedsBillable(formdata, transaction);
            }

            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run after save for encounter. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }

    /**
     * Checks if an encounter needs a billable and generates one if necessary.
     * @async
     * @param {Object} encounterRec - The encounter record to check.
     * @param {Object} transaction - The transaction object.
     * @returns {Promise<Object|null>} An object with a warning or error message, or null if successful.
     * @throws {Error} If there's an error during the billable calculation process.
     */
    async __checkNeedsBillable(encounterRec, transaction) {
        console.log("Checking if encounter needs billable.");

        try {
            const agencyId = encounterRec.agency_id || null;
            if (!agencyId) {
                return {
                    data: encounterRec,
                    warning: "Nursing agency is missing from encounter.",
                    formOverride: null,
                };
            }

            if (encounterRec.agency_does_billing === "Yes") {
                console.log(
                    "Agency does their own billing. Not generating billable."
                );
                return {
                    data: encounterRec,
                    error: null,
                    formOverride: null,
                };
            }

            const deliveryTicketId = encounterRec.delivery_ticket_id || null;
            if (!deliveryTicketId) {
                return {
                    data: encounterRec,
                    warning: "Delivery ticket is missing from encounter.",
                    formOverride: null,
                };
            }

            const generator = new BillingGeneratorClass(this.nes, this.ctx);
            const chargeLineRec =
                await generator.generateNursingVisitChargeLine(encounterRec.id);
            if (!chargeLineRec) {
                return {
                    data: encounterRec,
                    error: null,
                };
            }

            await transaction.insert("ledger_charge_line", chargeLineRec);
            return { data: encounterRec, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for encounter record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: encounterRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }
};
