"use strict";
const _ = require("lodash");
const BaseClass = require("./base");

module.exports = class CreatePatientOverrides extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.ctx = ctx;
    }

    async before_op(transaction, patientCreateRec, form, ctx) {
        try {
            if (ctx.request.method == "POST") {
                patientCreateRec.mrn = transaction.series_next_number("MRN");
            }
            return {
                data: {
                    ...patientCreateRec,
                    status_id: "1",
                    language: "English",
                    pediatric: "No",
                    code_status: "N/A",
                },
                error: null,
                formOverride: "patient",
            };
        } catch (e) {
            const errorMessage = `Error in save override for patient. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: patientCreateRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }
};
