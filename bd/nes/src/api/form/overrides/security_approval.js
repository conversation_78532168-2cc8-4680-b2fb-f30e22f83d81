"use strict";
const _ = require("lodash");
const fetch = require("node-fetch");

const BaseClass = require("./base");

const {
    SecurityApprovalStatus,
    SecurityApprovalAction,
} = require("../../billing/settings");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
        this.billingRules = nes.securityRules.billing;
    }

    async before_op(transaction, securityApprovalRec, form, user) {
        console.log("Security Approval before save override");

        try {
            const status = securityApprovalRec?.status;
            const actionComplete =
                securityApprovalRec?.action_complete === "Yes";
            if (status === SecurityApprovalStatus.APPROVED && !actionComplete) {
                console.log(
                    `Applying action ${securityApprovalRec?.action_type} for ${securityApprovalRec?.action_form} ${securityApprovalRec?.action_form_id}`
                );
                await this.__applyAction(
                    transaction,
                    securityApprovalRec,
                    user
                );
                securityApprovalRec.action_complete = "Yes";
            }
            return {
                data: securityApprovalRec,
                error: null,
                formOverride: null,
            };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before save operations for Security Approval. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: securityApprovalRec,
                error: returnError.error,
                formOverride: null,
            };
        }
    }

    /**
     * Applies the action specified in the security approval data.
     * @async
     * @param {Object} transaction - The database transaction object.
     * @param {Object} data - The security approval data containing action details.
     * @param {Object} user - The user object performing the action.
     * @returns {Promise<void>}
     * @throws {Error} If an error occurs while applying the action.
     */
    async __applyAction(transaction, data, user) {
        console.log(
            `Applying action ${data.action_type} for Security Approval ID ${data.id}`
        );

        try {
            const action = data.action_type;
            const actionData = data?.action_data || null;
            if (action === SecurityApprovalAction.POST) {
                const actionPath = data.action_path;
                await this.__handlePost(actionData, actionPath);
                return;
            } else if (action === SecurityApprovalAction.FUNCTION) {
                await this.__handleFunction(actionData);
                return;
            }

            for (const formName of Object.keys(actionData)) {
                const records = actionData[formName];
                console.log(
                    `Applying action ${action} for ${formName} with ${records.length} records`
                );
                for (const record of records) {
                    if (!record.id) {
                        await this.__handleInsert(
                            null,
                            record,
                            formName,
                            transaction,
                            user
                        );
                    } else {
                        await this.__handleUpdate(
                            formName,
                            record,
                            record.id,
                            user
                        );
                    }
                }
            }
        } catch (e) {
            console.log(
                `Error in applying security approval action. Error: ${e.message} Stack: ${e.stack}`
            );
            throw e;
        }
    }

    /**
     * Handles the insertion of a new record.
     * @async
     * @param {Object} seriesNoMap - Map to store series numbers.
     * @param {Object} record - The record to be inserted.
     * @param {string} formName - The name of the form being processed.
     * @param {Object} transaction - The transaction object for database operations.
     * @param {Object} user - The user object performing the action.
     * @returns {Promise<void>}
     */
    async __handleInsert(seriesNoMap, record, formName, transaction, user) {
        await transaction.insert(formName, record);
    }

    /**
     * Handles the updating of an existing record.
     * @async
     * @param {Object} record - The record to be updated.
     * @param {string} formName - The name of the form being processed.
     * @param {Object} transaction - The transaction object for database operations.
     * @param {Object} user - The user object performing the action.
     * @returns {Promise<void>}
     */
    async __handleUpdate(record, formName, transaction, user) {
        await transaction.update(formName, record, record.id);
    }

    /**
     * Handles POST requests to a specified action path.
     * @async
     * @param {string} actionPath - The path to send the POST request to.
     * @param {Object} actionData - The data to be sent in the request body.
     * @returns {Promise<null>} Returns null if the request is successful.
     * @throws {Error} Throws an error if the request fails or if the response contains an error.
     */
    async __handlePost(actionPath, actionData) {
        try {
            const url = this.shared.BASE_URL + actionPath;
            console.log(
                `Sending post request to ${url} with data:`,
                actionData
            );
            const response = await fetch(new URL(url), {
                method: "POST",
                headers: this.headers,
                body: JSON.stringify(actionData),
            });
            const res = await response.json();
            if (res.error) {
                throw new Error(res.error);
            }
            return null;
        } catch (e) {
            console.log(
                `Error in applying security approval post action for path ${actionPath} with data: ${JSON.stringify(actionData)}. Error: ${e.message} Stack: ${e.stack}`
            );
            throw e;
        }
    }

    /**
     * Handles the execution of a function specified in the action data.
     * @async
     * @param {Object} actionData - The data containing information about the function to be executed.
     * @throws {Error} Throws an error indicating that the functionality is not yet implemented.
     */
    async __handleFunction(actionData) {
        throw this.fx.getClaraError(
            `Security Action for Functions not yet implemented`
        );
        //TODO Add support for security function actions
    }
};
