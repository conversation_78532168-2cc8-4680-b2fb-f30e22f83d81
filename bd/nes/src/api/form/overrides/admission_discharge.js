"use strict";

const BaseClass = require("./base");
const AdmissionDischargeRuleClass = require("../rules/admission_discharge");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx, method) {
        super(nes, ctx, method);
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.admissionDischargeRules = new AdmissionDischargeRuleClass(
            nes,
            ctx
        );
    }

    async before_save(transaction, formdata, form, ctx) {
        console.log("Before save for admission_discharge override");
        try {
            await this.admissionDischargeRules.admission_discharge_audit(
                transaction,
                formdata,
                form,
                ctx,
                this.method
            );
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error in before save override for admission_discharge. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: formdata, error: returnError.error };
        }
    }

    async after_save(transaction, formdata, _form, _ctx) {
        try {
            await this.admissionDischargeRules.admission_discharge(
                transaction,
                formdata,
                _form,
                _ctx,
                this.method
            );
            return { data: formdata, error: null };
        } catch (e) {
            const errorMessage = `Error in after save override for admission_discharge. Please contact support.`;
            console.log(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return { data: formdata, error: returnError.error };
        }
    }
};
