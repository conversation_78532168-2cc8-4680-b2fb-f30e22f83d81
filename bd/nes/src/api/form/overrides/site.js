"use strict";
const BaseClass = require("./base");
module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.db = nes.modules.db;
    }

    async after_save(transaction, siteRec, _form, _user) {
        try {
            const id = siteRec?.id ?? null;

            if (siteRec?.ss_organization_type?.length > 0) {
                const SSDirectoryClass = require("@surescripts/directory");
                const directory = new SSDirectoryClass(this.nes, this.ctx);
                await directory.updateSite(siteRec, id);
            }
            return { error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for site record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: siteRec,
                error: returnError.error,
            };
        }
    }
};
