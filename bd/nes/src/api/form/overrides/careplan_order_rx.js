"use strict";
const BaseClass = require("./base");
const _ = require("lodash");
module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.shared = nes.shared;
    }

    async before_op(transaction, formdata, form, ctx) {
        console.log("Careplan Order Rx save override");
        try {
            formdata.verification_complete = formdata.rx_verified;

            return {
                data: formdata,
                error: null,
                formOverride: false,
            };
        } catch (e) {
            const errorMessage = `<PERSON>rror encountered while attempting to run before op for Careplan Order Rx. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: formdata,
                error: returnError.error,
            };
        }
    }

    async before_save(transaction, data, _form, _ctx) {
        if (data._meta?.slave) {
            const form = data._meta?.slave.form;
            let formId = data._meta?.slave.record;
            const formData = data._meta?.slave.data;

            if (!formId) {
                if (form === "view_careplan_order_item") {
                    const filePath = formData.file_path || {};
                    const documentId = formData.document_id || null;
                    const orderId = formData.order_id || null;
                    const orderItemId = formData.item_id || null;
                    const orderItemName = formData.item_id_type || null;
                    if (!orderItemId) {
                        throw new Error(
                            "Order item ID is missing from prescription"
                        );
                    }
                    if (filePath?.filename && !documentId) {
                        console.debug("Attaching document to order/order item");
                        const documentSourceName =
                            orderItemName === "careplan_order_item"
                                ? orderItemName
                                : "careplan_order";
                        const documentSourceId =
                            orderItemName === "careplan_order_item"
                                ? orderItemId
                                : orderId;

                        const filters = [`id:${documentSourceId}`];
                        const orderFormRec = _.head(
                            await this.form.get.get_form(
                                this.ctx,
                                this.ctx.user,
                                documentSourceName,
                                { filter: filters }
                            )
                        );
                        console.debug("form_code", orderFormRec.code);

                        if (!orderFormRec) {
                            throw new Error(
                                "Order record not found for prescription"
                            );
                        }
                        const documentRec = {
                            direct_attachment: "Yes",
                            source: "Scanned Document",
                            assigned_to: "Patient",
                            form_code: orderFormRec.code,
                            form_name: documentSourceName,
                            form_filter: documentSourceName,
                            type_id: "Prescription",
                            file_path: filePath,
                            site_id: orderFormRec.site_id,
                            patient_id: orderFormRec.patient_id,
                            patient_form_id: orderItemId,
                            view_patient_form: documentSourceName,
                            name: "Prescription",
                            comments: "Attached through prescription view",
                        };
                        console.debug("DocumentRec", documentRec);
                        await transaction.insert("document", documentRec);
                    }
                    const propsToExclude = [
                        "file_path",
                        "document_id",
                        "item_id",
                        "order_id",
                        "item_id_type",
                        "dose_str",
                    ];
                    const cleanedObject = _.pickBy(formData, (value, key) => {
                        return value !== null && !propsToExclude.includes(key);
                    });
                    formId = await transaction.update(
                        orderItemName,
                        cleanedObject,
                        orderItemId
                    );
                } else {
                    formId = await transaction.insert(form, formData);
                }
            } else {
                await transaction.update(form, formData, formId);
            }
        }
    }

    async after_save(transaction, data, _form, _ctx) {
        try {
            const { rx_verified, rx_complete, rx_denied } = data;
            if (rx_verified == "Yes") {
                data.status = "Verified";
            }

            if (rx_complete == "Yes") {
                data.status = "Complete";
            }

            if (rx_denied == "Yes") {
                data.status = "Denied";
            }
            return { data: data, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run after save checks for Careplan Order Rx. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: data,
                error: returnError.error,
            };
        }
    }
};
