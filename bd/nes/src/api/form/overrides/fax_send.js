"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    // Use before_op for actions before the (skipped) save operation
    async before_op(transaction, record, form, ctx) {
        const user_id = record.user_id
            ? record.user_id
            : record.updated_by
              ? record.updated_by
              : ctx.user.id;

        try {
            // Archive check
            if (record.archived) {
                return {
                    data: { ...record }, // Keep data on error
                    error: "Cannot process archived Fax Send record",
                    formOverride: null,
                };
            }

            // Required fields validation
            if (!record.from_number) {
                return {
                    data: { ...record }, // Keep data on error
                    error: "From Number is required",
                    formOverride: null,
                };
            }
            if (!record.to_number) {
                return {
                    data: { ...record }, // Keep data on error
                    error: "To Number is required",
                    formOverride: null,
                };
            }

            // Ensure fax_id
            if (!record.fax_id) {
                record.fax_id = this.fx.uuid();
                console.log(`Generated fax_id: ${record.fax_id}`);
            }
            if (!record.user_id) {
                record.user_id = user_id;
            }

            // Extract and parse document IDs
            let embedItems = [];
            if (record.fax_item_embed) {
                if (typeof record.fax_item_embed === "string") {
                    try {
                        embedItems = JSON.parse(record.fax_item_embed);
                        if (!Array.isArray(embedItems)) {
                            console.warn(
                                "Parsed fax_item_embed is not an array, defaulting to empty."
                            );
                            embedItems = [];
                        }
                    } catch (parseError) {
                        console.error(
                            "Failed to parse fax_item_embed JSON string:",
                            parseError
                        );
                        embedItems = [];
                    }
                } else if (Array.isArray(record.fax_item_embed)) {
                    embedItems = record.fax_item_embed;
                } else {
                    console.warn(
                        "fax_item_embed is neither a string nor an array, defaulting to empty."
                    );
                }
            }
            const documentIds = embedItems
                .map((item) => item?.id)
                .filter((id) => id != null);

            // Validate documents if type is Document
            if (record.fax_type === "Document" && documentIds.length === 0) {
                return {
                    data: { ...record }, // Keep data on error
                    error: "At least one document must be attached when Fax Type is Document.",
                    formOverride: null,
                };
            }

            // Check if already processed
            if (record.job_id && record.status !== "failed") {
                console.log(
                    `Fax already processed with job ID: ${record.job_id}. Skipping send.`
                );
                // Success, but indicate no save/override needed
                return {
                    data: { ...record },
                    error: null,
                    formOverride: "faxes",
                    break: true,
                };
            }

            // Send via FaxService
            try {
                const fromNumber = record.from_number.replace(/\D/g, "");
                const toNumber = record.to_number.replace(/\D/g, "");
                const recipients = [
                    { faxNumber: toNumber, name: record.to_name || "" },
                ];

                const faxParams = {
                    callerId: fromNumber,
                    recipients: recipients,
                    from_name: record.from_name || "",
                    subject: record.subject || "Fax Document",
                    message: record.message || "",
                    patientMrn: record.patient_id
                        ? String(record.patient_id)
                        : "",
                    documentIds: documentIds,
                    fax_quality: record.fax_quality || "Standard",
                    fax_id: record.fax_id,
                    files: record.files,
                    user_id: record.user_id || user_id,
                };
                if (record.scheduled_dt) {
                    faxParams.send_at = record.scheduled_dt;
                }

                console.log(
                    `Calling FaxService.send with doc IDs: ${documentIds.join(", ")}`
                );
                const result = await this.shared.fax.send(faxParams);
                // Handle service result
                if (
                    result.status !== 1 ||
                    !result.data ||
                    !Array.isArray(result.data.jobIds) ||
                    result.data.jobIds.length === 0
                ) {
                    console.error(
                        "Fax send service call failed or returned unexpected data:",
                        result
                    );
                    record.status = "failed";
                    record.error_message =
                        result.message ||
                        "Fax service call failed or missing Job ID.";
                    return {
                        data: { ...record }, // Keep data on error
                        error: record.error_message,
                        formOverride: null,
                    };
                }

                record.job_id = result.data.jobIds[0]; // Assign first job ID
                console.log(
                    `Fax submitted successfully via service. Primary job ID: ${record.job_id}`
                );

                // Success, indicate no save/override needed
                return {
                    data: { ...record },
                    error: null,
                    formOverride: "faxes",
                    break: true,
                };
            } catch (faxServiceError) {
                console.error(
                    "Error calling FaxService.send:",
                    faxServiceError
                );
                record.status = "failed";
                record.error_message =
                    faxServiceError.message || "Error invoking fax service";
                return {
                    data: { ...record }, // Keep data on error
                    error: record.error_message,
                    formOverride: null,
                };
            }
        } catch (e) {
            const errorMessage = `Error in before_op for fax send.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            return {
                data: { ...record }, // Keep data on error
                error:
                    this.fx.getReturnErrorMessage(e, errorMessage).error ||
                    "Unknown error in fax send override",
                formOverride: null,
            };
        }
    }
};
