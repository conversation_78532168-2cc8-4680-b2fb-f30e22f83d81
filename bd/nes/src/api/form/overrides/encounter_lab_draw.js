"use strict";
const moment = require("moment");

const BaseClass = require("./base");
const { LabOrderFrequency } = require("@dispense/settings");
module.exports = class EncounterLabDrawOverrides extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.ctx = ctx;
        const DispenseFetcherClass = require("@dispense/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    async before_op(transaction, labDrawRec, _form, _ctx) {
        console.log("Running lab draw order before op checks");
        try {
            if (!labDrawRec?.lab_orders?.length) {
                return {
                    data: labDrawRec,
                    error: "No associated lab orders found for lab draw",
                    formOverride: null,
                };
            }

            const labOrders = labDrawRec.lab_orders;
            const labOrderIds = labOrders.map((labOrder) => labOrder.id);
            labDrawRec.lab_order_id = labOrderIds;

            const labOrderRecs = this.fetcher.fetchCachedRecords(
                "careplan_order_lab",
                labOrderIds
            );

            if (labOrderRecs.length === 0) {
                return {
                    data: labDrawRec,
                    error: "Associated lab order records not found. Please verify records aren't archived or deleted.",
                };
            }

            const updatePromises = [];
            for (const labOrder of labOrderRecs) {
                const labOrderFrequency = labOrder.draw_frequency || null;
                switch (labOrderFrequency) {
                    case LabOrderFrequency.ONCE:
                        {
                            updatePromises.push(
                                transaction.update(
                                    "careplan_order_lab",
                                    {
                                        ...labOrder,
                                        active: null,
                                    },
                                    labOrder.id
                                )
                            );
                        }
                        break;
                    case LabOrderFrequency.MONTHLY:
                        {
                            const nextDrawDate = moment(
                                labDrawRec.draw_datetime
                            ).add(1, "month");
                            updatePromises.push(
                                transaction.update(
                                    "careplan_order_lab",
                                    {
                                        ...labOrder,
                                        next_draw_date:
                                            nextDrawDate.format("MM/DD/YYYY"),
                                    },
                                    labOrder.id
                                )
                            );
                        }
                        break;
                    case LabOrderFrequency.QUARTERLY:
                        {
                            const nextDrawDate = moment(
                                labDrawRec.draw_datetime
                            ).add(3, "month");
                            updatePromises.push(
                                transaction.update(
                                    "careplan_order_lab",
                                    {
                                        ...labOrder,
                                        next_draw_date:
                                            nextDrawDate.format("MM/DD/YYYY"),
                                    },
                                    labOrder.id
                                )
                            );
                        }
                        break;
                    case LabOrderFrequency.ANNUALLY:
                        {
                            const nextDrawDate = moment(
                                labDrawRec.draw_datetime
                            ).add(1, "year");
                            updatePromises.push(
                                transaction.update(
                                    "careplan_order_lab",
                                    {
                                        ...labOrder,
                                        next_draw_date:
                                            nextDrawDate.format("MM/DD/YYYY"),
                                    },
                                    labOrder.id
                                )
                            );
                        }
                        break;
                    case LabOrderFrequency.EVERY_VISIT:
                    case LabOrderFrequency.OTHER:
                        break;
                    default:
                        return {
                            data: labDrawRec,
                            error: `Invalid lab frequency for lab order ${labOrder.auto_name}: ${labOrderFrequency}`,
                        };
                }
            }
            if (updatePromises.length > 0) await Promise.all(updatePromises);
            return { data: labDrawRec, error: null };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before operation checks for Encounter Lab Draw. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: labDrawRec,
                error: returnError.error,
            };
        }
    }
};
