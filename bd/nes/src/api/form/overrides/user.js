"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx, method) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.method = method;
    }

    async before_op(transaction, formdata, form, ctx) {
        try {
            if (this.method == "UPDATE") {
                if (!formdata.password || !formdata.password2) {
                    delete formdata.password;
                    delete formdata.password2;
                }
            }
            return {
                data: formdata,
                error: null,
                formOverride: false,
            };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before op for ${form}. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                data: formdata,
                error: returnError.error,
            };
        }
    }
};
