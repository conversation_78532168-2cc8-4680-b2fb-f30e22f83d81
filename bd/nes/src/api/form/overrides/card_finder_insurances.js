"use strict";

const BaseClass = require("./base");

module.exports = class APIClass extends BaseClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
    }

    async before_checks(_transaction, data, _form, _user) {
        try {
            if (data.patient_insurance_id) {
                return { break: true, data };
            }
            return data;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to run before checks for billing closing record. Please contact support.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            const returnError = this.fx.getReturnErrorMessage(e, errorMessage);
            return {
                error: returnError.error,
            };
        }
    }
    async before_op(transaction, data, _form, _user) {
        if (data.patient_insurance_id) {
            return {
                error: "Patient Insurance already exists",
            };
        }
        console.log(
            "NO PATIENT INSURANCE ATTACHED CREATING NEW PATIENT INSURANCE"
        );

        data.patient_insurance_id = await transaction.insert(
            "patient_insurance",
            {
                patient_id: data.patient_id,
                payer_id: data.payer_id,
                mrn: data.mrn,
                bin: data.bin,
                pcn: data.pcn,
                billing_method_id: data.billing_method_id,
                send_blank_person_code: data.send_blank_person_code,
                is_self_pay: data.is_self_pay,
                type_id: data.type_id,
            }
        );
        return { data };
    }
};
