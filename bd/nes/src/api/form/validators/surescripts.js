"use strict";
const SSCheckerClass = require("@surescripts/checker");
const {
    SSServiceLevel,
    SSChangeType,
    SSChangeSubType,
    SSDEASchedules,
} = require("@surescripts/settings/types");
const { 
    MAX_DEA_REFILLS,
    MAX_DS_OTHER,
    MAX_DS_3_5,
} = require("@surescripts/settings/settings");
const { SSErrorMessages } = require("@surescripts/settings/errors");
module.exports = class SureScriptsValidators {
    async DEADaySupply(nes, ctx, form, data) {
        try {
            if (
                !data?.dea_schedule_id ||
                data?.dea_schedule_id.length === 0 ||
                !data?.day_supply ||
                data?.day_supply <= MAX_DS_OTHER
            ) {
                return null;
            }
    
            if (
                [SSDEASchedules.SCHED_2, SSDEASchedules.SCHED_5].includes(
                    data?.dea_schedule_id
                ) &&
                data?.day_supply > MAX_DS_3_5
            ) {
                return SSErrorMessages.CS_MAX_3_5_DAYS;
            } else if (data?.day_supply > MAX_DS_OTHER) {
                return SSErrorMessages.CS_MAX_OTHER_DAYS;
            }
        } catch (err) {
            return {
                error: err.message,
            };
        }
        return null;
    }

    async DEAMAXRefills(nes, ctx, form, data) {
        try {
            if (
                !data?.dea_schedule_id ||
                data?.dea_schedule_id.length === 0 ||
                !data?.request_refills ||
                data?.request_refills <= 0
            ) {
                return;
            }
    
            if (data?.dea_schedule_id == SSDEASchedules.SCHED_2) {
                return { error: SSErrorMessages.RR_CS_REFILL_NOT_ALLOWED };
            }
    
            if (
                [SSDEASchedules.SCHED_3, SSDEASchedules.SCHED_4].includes(
                    data?.dea_schedule_id
                ) &&
                data?.request_refills > MAX_DEA_REFILLS
            ) {
                return { error: SSErrorMessages.RR_CS_SCHIII_IV_LIMIT };
            }
        } catch (err) {
            return {
                error: err.message,
            };
        }
        return null;
    }

    async CheckSubCodeCombinations(nes, ctx, form, data) {
        try {
            if (
                !data?.chg_type_id ||
                data?.chg_type_id.length === 0 ||
                !data?.chg_type_sc_id ||
                data?.chg_type_sc_id.length === 0
            ) {
                return null;
            }
            if (
                data?.chg_type_id == SSChangeType.PROV_INFO &&
                data?.chg_type_sc_id?.includes(SSChangeSubType.BENEFIT_PLAN) &&
                data?.chg_type_sc_id?.includes(SSChangeSubType.REMS)
            ) {
                return { error: SSErrorMessages.CR_H_AND_J_NOT_ALLOWED };
            }
        } catch (err) {
            return {
                error: err.message,
            };
        }
        return null;
    }

    async CSServiceLevel(nes, ctx, form, data) {
        const checker = new SSCheckerClass(nes, ctx);
        try {
            if (!data?.dea_schedule_id || data?.dea_schedule_id.length === 0) {
                return null;
            }
            const res = await checker.checkServiceLevel(
                SSServiceLevel.CS,
                data?.change_to
            );
            if (!res) {
                return { error: SSErrorMessages.RR_CS_NOT_ALLOWED };
            }
        } catch (err) {
            return {
                error: err.message,
            };
        }
        return null;
    }
};
