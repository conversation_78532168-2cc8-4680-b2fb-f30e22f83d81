"use strict";
const fs = require("fs").promises;
const path = require("path");

/**
 * @class FormValidatorClass
 * @description This class is used as a proxy for form validators.
 * To add a new validator, just add it in the shared.js class or make your
 * own class in the /validators folder. It will pick it automatically on nes start.
 * VALIDATOR FUNCTION NAMES MUST BE UNIQUE.
 *			validate: [
                {
                    name: "MyValidatorName" <- name of the validator
                    fields: [
                        "field1" <- name of the fields to pass to the validator
                        "field2"
                    ]
                    clear_fields: [
                        "field1" <- name of the fields to clear if the validator fails (optional)
                    ]
                }
            ]

    validator function should take in 4 parameters and can run sync or async
    async ValidateFunction(nes, ctx, form, data) {
        1. If fields is a single field, data.value = field value
        2. If fields in mulitple, data = {field1: value1, field2: value2, ...}
    }
 */

class FormValidatorClass {
    constructor(nes) {
        this.nes = nes;
    }

    static register(ValidatorClass, nes) {
        console.log(`      .${ValidatorClass.name}`);
        let function_names = Object.getOwnPropertyNames(
            ValidatorClass.prototype || {}
        ).filter(
            (name) => typeof ValidatorClass.prototype[name] === "function"
        );
        function_names = function_names.filter(
            (func) => func !== "constructor"
        );
        function_names.forEach((func) => {
            if (func in nes.validators) {
                throw new Error(
                    `Validator ${func} is already registered. Validator names must be unique!`
                );
            }
            nes.validators[func] = new ValidatorClass()[func].bind(
                nes.validators
            );
        });
    }

    async validate(ctx, form, name, data, method = null) {
        if (!(name in this.nes.validators)) {
            throw new Error(`No validator registered with name ${name}`);
        }

        if (!data) {
            throw new Error(`Invalid validator request. Requires field_vals.`);
        }

        if (
            !this.nes.validators[name].length == 3 ||
            !this.nes.validators[name].length == 4
        ) {
            throw new Error(
                `Validator ${name} has incorrect number of parameters. Should be 3(ctx, form, data) or 4 (ctx, form, data, method)`
            );
        }

        try {
            const is_async =
                typeof this.nes.validators[name] === "function" &&
                this.nes.validators[name].constructor.name === "AsyncFunction";
            if (is_async) {
                const result = await this.nes.validators[name](
                    this.nes,
                    ctx,
                    form,
                    data,
                    method
                );
                return result;
            } else {
                return this.nes.validators[name](
                    this.nes,
                    ctx,
                    form,
                    data,
                    method
                );
            }
        } catch (error) {
            console.error(
                `Error in validator ${name} Error:${error.message} Stack:${error.stack}`
            );
        }
    }
}

async function registerValidators(nes) {
    nes.validators = {};
    const validators = await fs.readdir(__dirname);

    for (const file of validators) {
        if (file !== "index.js" && file.endsWith(".js")) {
            console.log(`    validators/${file}`);
            const ValidatorClass = require(path.join(__dirname, file));
            FormValidatorClass.register(ValidatorClass, nes);
        }
    }
}

module.exports = {
    registerValidators,
    FormValidatorClass,
};
