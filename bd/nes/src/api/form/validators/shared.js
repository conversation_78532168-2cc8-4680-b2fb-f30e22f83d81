"use strict";
const zxcvbn = require("zxcvbn");
const _ = require("lodash");

module.exports = class SharedValidators {
    async PasswordStrengthValidator(nes, ctx, form, data, method) {
        if (!data.value && method === "UPDATE") {
            return null;
        }
        const score_map = {
            "Very Weak": 0,
            Weak: 1,
            Medium: 2,
            Strong: 3,
            "Very Strong": 4,
        };
        const required_strength =
            nes.shared.config.company.password_strength || "Medium";
        const result = zxcvbn(data.value || "");

        // TODO: Check recently used passwords as well if resetting
        if (result.score < score_map[required_strength]) {
            let error = null;
            if (result.feedback.warning?.length > 0) {
                error = result.feedback.warning;
            } else if (result.feedback.suggestions?.length > 0) {
                error = result.feedback.suggestions.join(" ");
            } else {
                error =
                    "This password isn't strong enough, try a combination of uppercase letters, lowercase letters, numbers, and symbols.";
            }
            return {
                error: error,
            };
        }
        return null;
    }

    async EmailUniqueValidator(nes, ctx, form, data) {
        const form_ops = nes.shared.form;
        if (form === "user") {
            const filters = [
                `email:${data.email}`,
                `username:!${data.username}`,
            ];
            const dup_user_rec = _.head(
                await form_ops.get.get_form(ctx, ctx.user, "user", {
                    filter: filters,
                })
            );
            if (dup_user_rec) {
                return {
                    error: "This email is not unique. The email must be unique per user record.",
                };
            }
        } else if (form === "patient_portal") {
            const pt_filters = [`id:${data.patient_id}`];
            const patient_rec = _.head(
                await form_ops.get.get_form(ctx, ctx.user, "patient", {
                    filter: pt_filters,
                })
            );

            const filters = [
                `email:${data.email}`,
                `id:!${patient_rec.user_id}`,
            ];
            const dup_portal_rec = _.head(
                await form_ops.get.get_form(ctx, ctx.user, "user", {
                    filter: filters,
                })
            );
            if (dup_portal_rec) {
                return {
                    error: "This email is not unique. The email must be unique per user record.",
                };
            }
        }

        return null;
    }
};
