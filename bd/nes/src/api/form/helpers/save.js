"use strict";
const _ = require("lodash");

const { FormValidatorClass } = require("../validators");
const FormGetClass = require("../operations/get");

module.exports = class SaveFormClass {
    constructor(nes, method) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.dsl = nes.modules.dsl;
        this.db = nes.modules.db;
        this.crud = nes.shared.crud;
        this.transforms = nes.shared.transforms;
        this.utils = nes.shared.utils;
        this.shared = nes.shared;
        this.method = method;
        this.validator = new FormValidatorClass(nes);
        this.get = new FormGetClass(nes);
        this.transaction;
    }

    has_data(request) {
        return request.body != null && Object.keys(request.body).length > 0;
    }

    get_data(request, formid = null) {
        if (!this.has_data(request)) {
            return false;
        }
        let body = request.body;
        if (this.method == "UPDATE") {
            let id = null;
            if (!formid) {
                return false;
            }
            try {
                id = parseInt(formid);
            } catch (error) {
                console.error(error);
                return false;
            }
            body = { ...request.body, id: id };
        }
        return body;
    }
    async get_return_data(ctx, id, form) {
        const filters = [`id:${id}`];
        const newData = await this.get.get_form(ctx, ctx.user, form, {
            filter: filters,
        });
        ctx.status = 200;
        ctx.body = newData;
        return;
    }
    async validate(ctx, form, body) {
        // Run field level checks
        let [data, meta, cson] =
            (await this.run_field_level_checks(ctx, form, body)) || {};
        if (!data) {
            return [null, null, null];
        }
        // Run form level checks
        [data, meta, cson] = await this.run_form_level_checks(
            ctx,
            form,
            cson,
            data,
            meta,
            ctx.user
        );
        if (!data) {
            return [null, null, null];
        }
        // Check if this is a validate only request and triggers form not to save
        this.check_validate_only(ctx, data);
        return [data, meta, cson];
    }

    async run_field_level_checks(ctx, form, data) {
        //get cson information
        const cson = this.shared.DSL[form];

        try {
            //Run field transforms
            console.debug(`Running field level transforms on ${form}`);
            data = await this.run_field_transforms(ctx, form, cson, data);
            //Check fields
            console.debug(`Running field level checks on ${form}`);
            data = await this.utils._handle_fields(form, cson, data, this.crud);
            //Run field validators
            console.debug(`Running field level validators on ${form}`);
            const field_errors = await this.run_field_validators(
                ctx,
                form,
                cson,
                data
            );
            if (field_errors.length > 0) {
                ctx.status = 400;
                ctx.body = {
                    error: field_errors.join("\n"),
                };
                return [null, null, null];
            }
        } catch (e) {
            ctx.status = 400;
            ctx.body = {
                error: e.message,
            };
            return [null, null, null];
        }

        //set meta information
        const meta = data["_meta"];
        if (meta == null || meta == undefined) {
            data["_meta"] = {
                client: {},
            };
        }
        data = {
            ...data,
            ...data["_meta"].client,
        };
        return [data, meta, cson];
    }

    async perform_archive_checks(ctx, urlpath, form, data, formid) {
        // Archived Check
        console.debug(`Performing archive checks on ${form}`);
        const archived = await this.archived_check(
            ctx,
            urlpath,
            form,
            data,
            formid
        );
        if (archived) {
            return true;
        }

        // Unarchived Check
        console.debug(`Performing unarchive checks on ${form}`);
        const unarchived = await this.unarchived_check(
            ctx,
            urlpath,
            form,
            data,
            formid
        );
        if (unarchived) {
            return true;
        }
        return false;
    }

    async run_form_level_checks(ctx, form, cson, data, meta, user) {
        try {
            //Run post transform
            console.debug(`Running form level transforms on ${form}`);
            data = await this.run_pre_transforms(
                ctx,
                form,
                cson,
                data,
                meta,
                user
            );

            //Run form validators
            console.debug(`Running form level validators on ${form}`);
            const form_errors = await this.run_form_validators(
                ctx,
                form,
                cson,
                data
            );
            if (form_errors.length > 0) {
                ctx.status = 400;
                ctx.body = {
                    error: form_errors.join("\n"),
                };
                return [null, null, null];
            }
        } catch (e) {
            ctx.status = 400;
            ctx.body = {
                error: e.message,
            };
            return [null, null, null];
        }
        return [data, meta, cson];
    }

    async run_pre_transforms(ctx, form, cson, data, meta, user) {
        let args = { db: this.db };
        let pre_transforms = [];
        //Run post transform
        if (!_.isEmpty(cson.model.transform)) {
            pre_transforms = cson.model.transform;
            for (let i = 0; i < pre_transforms.length; i++) {
                const name = pre_transforms[i]["name"];
                let subject = "";
                let custom_map = {};
                if (!_.isEmpty(pre_transforms[i]["arguments"])) {
                    if (!_.isEmpty(pre_transforms[i]["arguments"]["subject"])) {
                        subject = pre_transforms[i]["arguments"]["subject"];
                    }

                    if (
                        !_.isEmpty(pre_transforms[i]["arguments"]["custom_map"])
                    ) {
                        custom_map =
                            pre_transforms[i]["arguments"]["custom_map"];
                    }
                }
                args = {
                    subject,
                    custom_map,
                    user,
                    method: this.method,
                    transform: pre_transforms[i],
                    ...args,
                };
                if (this.transforms[name]) {
                    data = await this.transforms[name](
                        ctx,
                        form,
                        cson,
                        { _meta: { ...meta }, ...data },
                        "",
                        this.get,
                        args,
                        this.transaction
                    );
                }
            }
        }
        return data;
    }

    async run_post_transforms(ctx, form, cson, data, meta, user) {
        let args = { db: this.db };
        let post_transforms = [];
        //Run post transform
        if (
            !_.isEmpty(cson.model.transform_post) ||
            !_.isEmpty(cson.model.transform)
        ) {
            post_transforms = cson.model.transform_post;
            for (let i = 0; i < post_transforms.length; i++) {
                const name = post_transforms[i]["name"];
                let subject = "";
                let custom_map = {};
                if (!_.isEmpty(post_transforms[i]["arguments"])) {
                    if (
                        !_.isEmpty(post_transforms[i]["arguments"]["subject"])
                    ) {
                        subject = post_transforms[i]["arguments"]["subject"];
                    }

                    if (
                        !_.isEmpty(
                            post_transforms[i]["arguments"]["custom_map"]
                        )
                    ) {
                        custom_map =
                            post_transforms[i]["arguments"]["custom_map"];
                    }
                }
                args = {
                    subject,
                    custom_map,
                    user,
                    method: this.method,
                    transform: post_transforms[i],
                    ...args,
                };
                if (this.transforms[name]) {
                    data = await this.transforms[name](
                        ctx,
                        form,
                        cson,
                        { _meta: { ...meta }, ...data },
                        "",
                        this.get,
                        args,
                        this.transaction
                    );
                }
            }
        }
        return data;
    }

    async run_field_transforms(ctx, form, cson, data) {
        for (const [k, v] of Object.entries(cson.fields)) {
            const transforms_list = v.model.transform;
            if (!_.isEmpty(transforms_list)) {
                for (let i = 0; i < transforms_list.length; i++) {
                    const transform = transforms_list[i].name;
                    if (this.transforms[transform]) {
                        data = await this.transforms[transform](
                            ctx,
                            form,
                            cson,
                            data,
                            k,
                            this.get,
                            {},
                            this.transaction
                        );
                    }
                }
            }
        }
        return data;
    }

    async run_field_validators(ctx, form, cson, data) {
        const errors = [];
        for (const [k, v] of Object.entries(cson.fields)) {
            const validators_list = v.model.validate;
            if (_.isEmpty(validators_list)) continue;
            for (const inner_validator of validators_list) {
                if (inner_validator.name?.length < 1) continue;
                const { name, fields } = inner_validator;
                try {
                    const field_vals =
                        fields.length > 1
                            ? fields.reduce(
                                  (acc, field) => ({
                                      ...acc,
                                      [field]: data[field],
                                  }),
                                  {}
                              )
                            : { value: data[fields] };
                    const res = await this.validator.validate(
                        ctx,
                        form,
                        name,
                        field_vals,
                        this.method
                    );
                    if (res && res.error) {
                        errors.push(res.error);
                    }
                } catch (e) {
                    errors.push(
                        e.message ||
                            `Error running validator ${name} on form ${form}`
                    );
                }
            }
        }
        return errors;
    }

    async run_form_validators(ctx, form, cson, data) {
        const errors = [];
        if (!cson.model.validate || _.isEmpty(cson.model.validate)) {
            return errors;
        }
        for (const form_validator of cson.model.validate) {
            if (form_validator.name?.length > 0) {
                const { name, fields } = form_validator;
                try {
                    const field_vals =
                        fields.length > 1
                            ? fields.reduce(
                                  (acc, field) => ({
                                      ...acc,
                                      [field]: data[field],
                                  }),
                                  {}
                              )
                            : { value: data[fields] };
                    const res = await this.validator.validate(
                        ctx,
                        form,
                        name,
                        field_vals
                    );
                    if (res && res.error) {
                        errors.push(res.error);
                    }
                } catch (e) {
                    errors.push(
                        e.message ||
                            `Error running validator ${name} on form ${form}`
                    );
                }
            }
        }
        return errors;
    }

    async archived_check(ctx, urlpath, form, data, formid) {
        if (urlpath.path.length > 4 && urlpath.path[4] == "archive") {
            try {
                await this.transaction.update(form, data, formid);
                ctx.status = 200;
                ctx.body = data;
                return true;
            } catch (e) {
                console.error(e);
                throw e;
            }
        }
        return false;
    }

    async unarchived_check(ctx, urlpath, form, data, formid) {
        if (urlpath.path.length > 4 && urlpath.path[4] == "unarchive") {
            try {
                await this.transaction.update(
                    form,
                    { archived: false },
                    formid
                );
                ctx.status = 200;
                ctx.body = data;
                return true;
            } catch (e) {
                console.error("Error Unarchiving Form", e);
                throw "Error Unarchiving Form. See server logs for details";
            }
        }
        return false;
    }

    check_validate_only(ctx, data) {
        if (data["_meta"]?.validate_only) {
            ctx.is_test = true;
        } else {
            ctx.is_test = false;
        }
    }
};
