"use strict";
const FiltersClass = require("./filter");

// test this with/without/combination of
// keyword, asof

module.exports = class SQLGetClass {
    constructor(nes, exporting = false) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.dsl = nes.modules.dsl;
        this.exporting = exporting;
        this.filters = new FiltersClass(nes);
    }

    init() {
        if ("view" in this.shared) return;

        // common functions
        const field_type = (fv) => {
            if (get_type(fv.model.source) === "string") {
                return "int";
            } else if (fv.model.multi) {
                return "array";
            } else {
                return fv.model.type;
            }
        };
        const get_type = this.dsl.get_type;

        /**
         * Returns the SQL filter condition based on the provided field and filter values.
         * @param {string} alias - table name or alias for which these filters are being generated.
         * @param {Object} field - dsl field object object.
         * @returns {string} The SQL filter condition.
         */
        const get_source_filter_cond = (alias, field) => {
            let cond = "";
            const form = field.model.source;
            if (!this.shared.sourceFilterForms.includes(form)) {
                return cond;
            }
            const sourcefilter = field.model.sourcefilter;
            const form_dsl = this.shared.DSL[form];
            if (
                !form_dsl ||
                !form_dsl.model.save ||
                Object.keys(sourcefilter).length === 0
            ) {
                return cond;
            }
            const form_fields = Object.keys(form_dsl.fields);
            for (const [fk, fv] of Object.entries(sourcefilter)) {
                if (!form_fields.includes(fk)) {
                    continue;
                }
                if (!fv.static) {
                    continue;
                }
                const ot = get_type(fv.static);
                if (ot === "int") {
                    cond += ` AND ${alias}.${fk} = ${fv.static} `;
                } else if (ot === "string") {
                    cond += ` AND ${alias}.${fk} = '${fv.static}' `;
                } else if (ot === "array") {
                    if (fv.static.length === 0) {
                        continue;
                    }
                    const avt = get_type(fv.static[0]);
                    if (avt === "int") {
                        cond += ` AND ${alias}.${fk} IN (${fv.static.join(",")}) `;
                    } else if (avt === "string") {
                        cond += ` AND ${alias}.${fk} IN ('${fv.static.join("','")}') `;
                    }
                }
            }
            return cond;
        };

        const null_type = (ty) => {
            if (ty === "date") {
                return "::date";
            } else if (ty === "datetime") {
                return "::timestamp";
            } else if (ty === "time") {
                return "::time";
            } else if (ty === "int" || ty === "decimal") {
                return "::decimal";
            } else {
                return "";
            }
        };

        // find out which forms are used as subforms
        // use this information to created union queries later
        const usubs = {},
            subfk = {};
        for (const [k, v] of Object.entries(this.shared.DSL)) {
            if (!v.model.save) continue;
            // subform views
            const subs = {};
            for (const [fk, fv] of Object.entries(v.fields)) {
                if (
                    !fv.model.active ||
                    !fv.model.save ||
                    fv.model.type !== "subform"
                )
                    continue;
                if (Object.keys(fv.model.sourcefilter).length === 0) {
                    if (get_type(fv.model.source) !== "string") continue;
                    if (
                        !(fv.model.source in this.shared.DSL) ||
                        !this.shared.DSL[fv.model.source].model.save
                    )
                        continue;
                    subs[fv.model.source] = fk;
                    if (!(fv.model.source in subfk))
                        subfk[fv.model.source] = {};
                    subfk[fv.model.source][k] = 1;
                } else {
                    for (const sk in fv.model.sourcefilter) {
                        if (
                            !(sk in this.shared.DSL) ||
                            !this.shared.DSL[sk].model.save
                        )
                            continue;
                        if (!(sk in subs)) subs[sk] = fk;
                        if (!(sk in subfk)) subfk[sk] = {};
                        subfk[sk][k] = 1;
                    }
                }
            }

            // simplify field name (which will become part of view name) if part of collection
            for (const [sk, sv] of Object.entries(subs)) {
                const sff = sv.split("_");
                if (
                    sff.length > 1 &&
                    parseInt(sff[sff.length - 1]) > 0 &&
                    sff.indexOf("subform") > -1
                ) {
                    // handles subform field collections where DSL.model.collections is missing
                    subs[sk] = sff.slice(0, sff.length - 1).join("_");
                } else {
                    for (const c of v.model.collections) {
                        for (let r = 1; r <= this.dsl.MAX_COLLECTIONS; r++) {
                            if (sv === c + "_" + r) subs[sk] = c;
                        }
                    }
                }
            }

            const sb = {};
            for (const sk in subs) {
                const sv = subs[sk];
                if (sb[sv] == null) sb[sv] = [];
                sb[sv].push(sk);
            }
            if (Object.keys(sb).length > 0) usubs[k] = sb;
        }

        /*
            create easy-to-query view for all form_ tables
            unique index: id
            includes:
                array indices for all radio button selections (useful for calculation)
                array of collection values (dx_1, dx_2 etc.) in one field
                _auto_name for all single & multi-valued linked fields
                _external_id for all tables with patient_id columns
                parent_id/parent_form for all forms used as subforms in other tables
            excludes:
                deleted rows
                foreign keys / arrays for inactive fields
        */
        const fviews = {},
            lviews = {
                links: [],
            },
            logviews = {};
        for (const [k, v] of Object.entries(this.shared.DSL)) {
            if (!v.model.save) continue;

            const logsf = [];
            const loglj = ["FROM log_" + k];
            const sf = [];
            const lj = ["FROM form_" + k];
            const wh = [
                "WHERE (form_" +
                    k +
                    ".deleted=FALSE OR form_" +
                    k +
                    ".deleted IS NULL)",
            ];
            const logwh = [
                "WHERE (log_" +
                    k +
                    ".deleted=FALSE OR log_" +
                    k +
                    ".deleted IS NULL)",
            ];

            if (this.exporting && k in this.dsl.custom_field_export) {
                sf.push(
                    "SELECT form_" +
                        k +
                        "." +
                        this.dsl.custom_field_export[k].join(
                            ", form_" + k + "."
                        )
                );
                logsf.push(
                    "SELECT log_" +
                        k +
                        "." +
                        this.dsl.custom_field_export[k].join(", log_" + k + ".")
                );
            } else {
                sf.push("SELECT form_" + k + ".*");
                logsf.push("SELECT log_" + k + ".*");
            }

            // array indices for automatic calculation
            for (const [fk, fv] of Object.entries(v.fields)) {
                if (
                    !fv.model.active ||
                    !fv.model.save ||
                    fv.model.multi ||
                    fv.model.autoinsert ||
                    fv.model.type === "subform"
                )
                    continue;

                if (
                    this.exporting &&
                    k in this.dsl.custom_field_export &&
                    !this.dsl.custom_field_export[k].includes(fk)
                )
                    continue;

                const fty = get_type(fv.model.source);
                if (fty === "array") {
                    const cs = [];
                    const log = [];
                    for (const i in fv.model.source) {
                        const a = fv.model.source[i];
                        if (a === null) continue;
                        cs.push(
                            "WHEN form_" +
                                k +
                                "." +
                                fk +
                                "='" +
                                a
                                    .toString()
                                    .replace(/'/g, '"')
                                    .replace(/"/g, "''") +
                                "' THEN " +
                                i
                        );
                        log.push(
                            "WHEN log_" +
                                k +
                                "." +
                                fk +
                                "='" +
                                a
                                    .toString()
                                    .replace(/'/g, '"')
                                    .replace(/"/g, "''") +
                                "' THEN " +
                                i
                        );
                    }
                    sf.push(
                        ",(CASE " +
                            cs.join(" ") +
                            " ELSE NULL END) AS " +
                            fk +
                            "_index"
                    );
                    logsf.push(
                        ",(CASE " +
                            log.join(" ") +
                            " ELSE NULL END) AS " +
                            fk +
                            "_index"
                    );
                } else if (fty === "object") {
                    const dky = Object.keys(fv.model.source).sort().join(",");
                    if (!(dky in this.dsl.infer_index)) continue;
                    const cs = [];
                    const log = [];
                    const aky = dky.split(",");
                    for (const i in aky) {
                        const a = aky[i];
                        const ii = this.dsl.infer_index[dky][i];
                        cs.push(
                            "WHEN form_" +
                                k +
                                "." +
                                fk +
                                "='" +
                                a
                                    .toString()
                                    .replace(/'/g, '"')
                                    .replace(/"/g, "''") +
                                "' THEN " +
                                ii
                        );
                        log.push(
                            "WHEN log_" +
                                k +
                                "." +
                                fk +
                                "='" +
                                a
                                    .toString()
                                    .replace(/'/g, '"')
                                    .replace(/"/g, "''") +
                                "' THEN " +
                                ii
                        );
                    }
                    sf.push(
                        ",(CASE " +
                            cs.join(" ") +
                            " ELSE NULL END) AS " +
                            fk +
                            "_index"
                    );
                    logsf.push(
                        ",(CASE " +
                            log.join(" ") +
                            " ELSE NULL END) AS " +
                            fk +
                            "_index"
                    );
                }
            }

            // _auto_name fields for single-value source dropdowns
            for (const [fk, fv] of Object.entries(v.fields)) {
                if (
                    !fv.model.active ||
                    !fv.model.save ||
                    fv.model.multi ||
                    fv.model.type === "subform" ||
                    get_type(fv.model.source) !== "string" ||
                    !(fv.model.source in this.shared.DSL)
                )
                    continue;

                if (
                    this.exporting &&
                    k in this.dsl.custom_field_export &&
                    !this.dsl.custom_field_export[k].includes(fk)
                )
                    continue;
                const ajc = get_source_filter_cond(
                    fk + "_" + fv.model.source,
                    fv
                );
                sf.push(
                    "," +
                        fk +
                        "_" +
                        fv.model.source +
                        ".auto_name AS " +
                        fk +
                        "_auto_name"
                );
                logsf.push(
                    "," +
                        fk +
                        "_" +
                        fv.model.source +
                        ".auto_name AS " +
                        fk +
                        "_auto_name"
                );
                lj.push(
                    " LEFT JOIN form_" +
                        fv.model.source +
                        " AS " +
                        fk +
                        "_" +
                        fv.model.source
                );
                lj.push(
                    " ON form_" +
                        k +
                        "." +
                        fk +
                        "=" +
                        fk +
                        "_" +
                        fv.model.source +
                        "." +
                        fv.model.sourceid +
                        ajc
                );
                loglj.push(
                    " LEFT JOIN log_" +
                        fv.model.source +
                        " AS " +
                        fk +
                        "_" +
                        fv.model.source
                );
                loglj.push(
                    " ON (log_" +
                        k +
                        "." +
                        fk +
                        "=" +
                        fk +
                        "_" +
                        fv.model.source +
                        "." +
                        fv.model.sourceid +
                        ` AND (upper(${fk + "_" + fv.model.source}.sys_period) > log_time AND lower(${fk + "_" + fv.model.source}.sys_period) <= log_time))` +
                        ajc
                );
            }

            // _external_id for patient_id columns
            if ("patient_id" in v.fields && !("external_id" in v.fields)) {
                const fkext = "patient_external_id";
                sf.push("," + fkext + ".external_id AS " + "external_id");
                logsf.push("," + fkext + ".external_id AS " + "external_id");
                lj.push(" LEFT JOIN form_patient AS " + fkext);
                lj.push(" ON form_" + k + ".patient_id" + "=" + fkext + ".id");
                loglj.push(" LEFT JOIN log_patient AS " + fkext);
                loglj.push(
                    " ON (log_" +
                        k +
                        ".patient_id" +
                        "=" +
                        fkext +
                        ".id" +
                        ` AND (upper(${fkext}.sys_period) > log_time AND lower(${fkext}.sys_period) <= log_time))`
                );
            }

            // array for collections
            if ("collections" in v.model) {
                for (const c of v.model.collections) {
                    const cl = [],
                        cla = [],
                        lcl = [];
                    for (let r = 1; r <= this.dsl.MAX_COLLECTIONS; r++) {
                        const fk = c + "_" + r;
                        if (!(fk in v.fields)) continue;
                        const fv = v.fields[fk];
                        if (
                            !fv.model.active ||
                            !fv.model.save ||
                            fv.model.multi ||
                            fv.model.type === "subform"
                        )
                            continue;

                        cl.push("form_" + k + "." + fk);
                        lcl.push("log_" + k + "." + fk);
                        if (
                            get_type(fv.model.source) !== "string" ||
                            !(fv.model.source in this.shared.DSL)
                        )
                            continue;
                        cla.push(fk + "_" + fv.model.source + ".auto_name");
                    }
                    if (cl.length > 1) {
                        sf.push(
                            ",ARRAY[" + cl.join(",") + "] AS collection_" + c
                        );
                        logsf.push(
                            ",ARRAY[" + lcl.join(",") + "] AS collection_" + c
                        );
                    }
                    if (cla.length > 1) {
                        sf.push(
                            ",ARRAY[" +
                                cla.join(",") +
                                "] AS collection_" +
                                c +
                                "_auto_name"
                        );
                        logsf.push(
                            ",ARRAY[" +
                                cla.join(",") +
                                "] AS collection_" +
                                c +
                                "_auto_name"
                        );
                    }
                }
            }
            // correct parsing of string Arrays
            for (const [fk, fv] of Object.entries(v.fields)) {
                const fty = get_type(fv.model.source);
                if (
                    !fv.model.active ||
                    !fv.model.save ||
                    !fv.model.multi ||
                    (fv.view?.control === "checkbox" &&
                        fv.view?.class == "list") ||
                    fty === "string"
                )
                    continue;
                if (fty === "array" || fty === "object") {
                    sf.push(`,CASE
                        WHEN form_${k}.${fk} IS NULL THEN ARRAY_TO_JSON(ARRAY[]::varchar[])
                        WHEN form_${k}.${fk}::varchar ~ '^{' AND form_${k}.${fk}::varchar ~ '}$' THEN ARRAY_TO_JSON(form_${k}.${fk}::varchar[])
                        ELSE ARRAY_TO_JSON(ARRAY[form_${k}.${fk}])
                    END AS ${fk}_to_array`);
                    logsf.push(`,CASE
                        WHEN log_${k}.${fk} IS NULL THEN ARRAY_TO_JSON(ARRAY[]::varchar[])
                        WHEN log_${k}.${fk}::varchar ~ '^{' AND log_${k}.${fk}::varchar ~ '}$' THEN ARRAY_TO_JSON(log_${k}.${fk}::varchar[])
                        ELSE ARRAY_TO_JSON(ARRAY[log_${k}.${fk}])
                    END AS ${fk}_to_array`);
                }
            }
            // array + _auto_name for multi-value source dropdowns
            for (const [fk, fv] of Object.entries(v.fields)) {
                if (
                    !fv.model.active ||
                    !fv.model.save ||
                    !fv.model.multi ||
                    fv.model.type === "subform" ||
                    get_type(fv.model.source) !== "string" ||
                    !(fv.model.source in this.shared.DSL)
                )
                    continue;

                const ajc = get_source_filter_cond(
                    "form_" + fv.model.source,
                    fv
                );
                const ft = fv.model.source;
                const gr = "gr_form_" + k + "_" + fk + "_to_" + ft + "_id";
                const lgr = "l" + gr;
                sf.push(
                    ",ARRAY(SELECT form_" +
                        ft +
                        "_fk from " +
                        gr +
                        " WHERE form_" +
                        k +
                        "_fk=form_" +
                        k +
                        ".id ORDER BY " +
                        gr +
                        ".id) AS " +
                        fk
                );
                sf.push(
                    ",ARRAY(SELECT form_" +
                        ft +
                        ".auto_name FROM " +
                        gr +
                        " LEFT JOIN form_" +
                        ft +
                        " ON " +
                        `${fv.model.sourceid && fv.model.sourceid !== "id" ? "LOWER(" : "("}` +
                        gr +
                        ".form_" +
                        ft +
                        `${fv.model.sourceid && fv.model.sourceid !== "id" ? "_fk)=LOWER(form_" : "_fk)=(form_"}` +
                        ft +
                        `${fv.model.sourceid ? "." + fv.model.sourceid : ".id"})` +
                        ajc +
                        " WHERE form_" +
                        k +
                        "_fk = form_" +
                        k +
                        ".id ORDER BY " +
                        gr +
                        ".id) AS " +
                        fk +
                        "_auto_name"
                );
                logsf.push(
                    ",ARRAY(SELECT form_" +
                        ft +
                        "_fk from " +
                        lgr +
                        " WHERE form_" +
                        k +
                        "_fk=log_" +
                        k +
                        ".id " +
                        ` AND (upper(${lgr}.sys_period) > log_time AND lower(${lgr}.sys_period) <= log_time)` +
                        "ORDER BY " +
                        lgr +
                        ".id) AS " +
                        fk
                );
                logsf.push(
                    ",ARRAY(SELECT log_" +
                        ft +
                        ".auto_name FROM " +
                        lgr +
                        " LEFT JOIN log_" +
                        ft +
                        " ON (" +
                        `${fv.model.sourceid && fv.model.sourceid !== "id" ? "LOWER(" : "("}` +
                        lgr +
                        ".form_" +
                        ft +
                        `${fv.model.sourceid && fv.model.sourceid !== "id" ? "_fk)=LOWER(form_" : "_fk)=(form_"}` +
                        ft +
                        `${fv.model.sourceid ? "." + fv.model.sourceid : ".id"})` +
                        ` AND (upper(log_${ft}.sys_period) > log_time AND lower(log_${ft}.sys_period) <= log_time))` +
                        "WHERE form_" +
                        k +
                        "_fk = log_" +
                        k +
                        ".id ORDER BY " +
                        lgr +
                        ".id) AS " +
                        fk +
                        "_auto_name"
                );

                if (v.model.reportable)
                    lviews.links.push(
                        "SELECT id, '" +
                            fk +
                            "' AS primary_field, '" +
                            k +
                            "' AS primary_source, form_" +
                            k +
                            "_fk AS primary_id, '" +
                            ft +
                            "' AS foreign_source, form_" +
                            ft +
                            "_fk AS foreign_id FROM " +
                            gr
                    );
            }
            if (k in subfk) {
                // union query for all rows of the form linked to the parent via sf_
                //   if no parent (self), show null in parent_ columns
                const uq = [],
                    ljs = [],
                    whs = [];
                const luq = [],
                    lljs = [],
                    lwhs = [];
                const sfs =
                    ",NULL::int AS parent_id, NULL::text AS parent_form, NULL::boolean AS parent_archived";
                for (const fk in subfk[k]) {
                    // match the parent
                    const sff = "sf_form_" + fk + "_to_" + k;
                    const sfa =
                        "," +
                        sff +
                        ".form_" +
                        fk +
                        "_fk AS parent_id, '" +
                        fk +
                        "'::text AS parent_form";
                    const lja =
                        "INNER JOIN " +
                        sff +
                        " ON " +
                        sff +
                        ".form_" +
                        k +
                        "_fk=form_" +
                        k +
                        ".id";
                    const logsff = "l" + sff;
                    const logsfa =
                        "," +
                        logsff +
                        ".form_" +
                        fk +
                        "_fk AS parent_id, '" +
                        fk +
                        "'::text AS parent_form";
                    const loglja =
                        "INNER JOIN " +
                        logsff +
                        " ON (" +
                        logsff +
                        ".form_" +
                        k +
                        "_fk=log_" +
                        k +
                        ".id" +
                        ` AND (upper(${logsff}.sys_period) > log_time AND lower(${logsff}.sys_period) <= log_time))`;

                    // get parent fields
                    const spf = "form_" + fk;
                    const spa = "," + spf + ".archived AS parent_archived";
                    const ljp =
                        "INNER JOIN " +
                        spf +
                        " ON " +
                        spf +
                        ".id=" +
                        sff +
                        "." +
                        spf +
                        "_fk";
                    const logspf = "log_" + fk;
                    const logspa =
                        "," + logspf + ".archived AS parent_archived";
                    const logljp =
                        "INNER JOIN " +
                        logspf +
                        " ON (" +
                        logspf +
                        ".id=" +
                        logsff +
                        "." +
                        spf +
                        "_fk" +
                        ` AND (upper(${logspf}.sys_period) > log_time AND lower(${logspf}.sys_period) <= log_time))`;

                    uq.push(
                        [
                            sf.join(""),
                            sfa,
                            spa,
                            lj.join(""),
                            lja,
                            ljp,
                            wh.join(""),
                        ].join(" ")
                    );
                    luq.push(
                        [
                            logsf.join(""),
                            logsfa,
                            logspa,
                            loglj.join(""),
                            loglja,
                            logljp,
                            logwh.join(""),
                        ].join(" ")
                    );
                    // list parameters for no parent (self) query
                    ljs.push(
                        "LEFT JOIN " +
                            sff +
                            " ON " +
                            sff +
                            ".form_" +
                            k +
                            "_fk=form_" +
                            k +
                            ".id"
                    );
                    lljs.push(
                        "LEFT JOIN " +
                            logsff +
                            " ON (" +
                            logsff +
                            ".form_" +
                            k +
                            "_fk=log_" +
                            k +
                            ".id" +
                            ` AND (upper(${logsff}.sys_period) > log_time AND lower(${logsff}.sys_period) <= log_time))`
                    );
                    whs.push(sff + ".form_" + k + "_fk IS NULL");
                    lwhs.push(logsff + ".form_" + k + "_fk IS NULL");
                }
                // add no parent (self) query
                uq.push(
                    [
                        sf.join(""),
                        sfs,
                        lj.join(""),
                        ljs.join(" "),
                        wh.concat(whs).join(" AND "),
                    ].join(" ")
                );
                luq.push(
                    [
                        logsf.join(""),
                        sfs,
                        loglj.join(""),
                        lljs.join(" "),
                        logwh.concat(lwhs).join(" AND "),
                    ].join(" ")
                );

                fviews[k] = uq;
                logviews[k] = luq;
            } else {
                fviews[k] = [sf.join(""), lj.join(""), wh.join("")].join(" ");
                logviews[k] = [
                    logsf.join(""),
                    loglj.join(""),
                    logwh.join(""),
                ].join(" ");
            }
        }

        // flatten form views with no unions
        for (const [k, v] of Object.entries(fviews)) {
            if (typeof v !== "string" && v.length == 1) {
                fviews[k] = v.join("");
            }
        }

        for (const [k, v] of Object.entries(logviews)) {
            if (typeof v !== "string" && v.length == 1) {
                logviews[k] = v.join("");
            }
        }

        /*
            create merged union query for all subforms used in the same sourcefilter
            unique index: subform_name, id
            includes:
                _auto_name for all single & multi-valued linked fields
                parent_id/subform_name for all forms used as subforms in other tables
            excludes:
                deleted rows
                foreign keys / arrays for inactive fields
        */
        const sviews = {};
        const logsviews = {};
        for (const [k, v] of Object.entries(usubs)) {
            for (const [s, tbs] of Object.entries(v)) {
                if (tbs.length === 1) continue;

                // build list of all fields
                const sb = k + "_" + s;
                const sf = {};
                const lsf = {};
                const allfld = {},
                    allmlt = {},
                    allsrc = {};
                for (const t of tbs) {
                    if (!(t in this.shared.DSL)) continue;

                    for (const [fk, fv] of Object.entries(
                        this.shared.DSL[t].fields
                    )) {
                        if (
                            !fv.model.active ||
                            !fv.model.save ||
                            fv.model.type === "subform"
                        )
                            continue;
                        if (fv.model.multi) allmlt[fk] = 1;
                        if (get_type(fv.model.source) === "string")
                            allsrc[fk] = 1;
                        if (!(fk in allfld)) allfld[fk] = {};
                        const ty = field_type(fv);
                        if (!(fv.model.type in allfld[fk])) allfld[fk][ty] = 1;
                    }
                }

                // view with all fields
                for (const t of tbs) {
                    if (!(t in this.shared.DSL)) continue;
                    const sff = "sf_form_" + k + "_to_" + t;
                    const lsff = "l" + sff;
                    const vf = [
                        "SELECT " + sff + ".form_" + k + "_fk AS " + k + "_id",
                        ",'" + t + "'::text AS subform_name",
                    ];
                    const lvf = [
                        "SELECT " + lsff + ".form_" + k + "_fk AS " + k + "_id",
                        ",'" + t + "'::text AS subform_name",
                    ];
                    const fl = this.shared.DSL[t].fields;

                    for (const [fk, ft] of Object.entries(allfld)) {
                        // for all fields
                        if (fk in allmlt && fk in allsrc) {
                            // multi-value from other form_ tables
                            if (fk in fl) {
                                const ftv = fl[fk];
                                const ftt = ftv.model.source;
                                if (
                                    ftv.model.active &&
                                    ftv.model.save &&
                                    get_type(ftt) == "string" &&
                                    ftt in this.shared.DSL &&
                                    this.shared.DSL[ftt].model.save
                                ) {
                                    const gr =
                                        "gr_form_" +
                                        t +
                                        "_" +
                                        fk +
                                        "_to_" +
                                        ftt +
                                        "_id";
                                    const lgr = "l" + gr;
                                    vf.push(
                                        ",ARRAY(SELECT form_" +
                                            ftt +
                                            "_fk from " +
                                            gr +
                                            " WHERE form_" +
                                            t +
                                            "_fk=form_" +
                                            t +
                                            ".id) AS " +
                                            fk
                                    );
                                    vf.push(
                                        ",ARRAY(SELECT form_" +
                                            ftt +
                                            ".auto_name FROM " +
                                            gr +
                                            " LEFT JOIN form_" +
                                            ftt +
                                            " ON " +
                                            gr +
                                            ".form_" +
                                            ftt +
                                            "_fk=form_" +
                                            ftt +
                                            ".id WHERE form_" +
                                            t +
                                            "_fk = form_" +
                                            t +
                                            ".id) AS " +
                                            fk +
                                            "_auto_name"
                                    );
                                    lvf.push(
                                        ",ARRAY(SELECT form_" +
                                            ftt +
                                            "_fk from " +
                                            lgr +
                                            " WHERE (form_" +
                                            t +
                                            "_fk=log_" +
                                            t +
                                            `.id AND (upper(${lgr}.sys_period) > log_time AND lower(${lgr}.sys_period) <= log_time))) AS ` +
                                            fk
                                    );
                                    lvf.push(
                                        ",ARRAY(SELECT log_" +
                                            ftt +
                                            ".auto_name FROM " +
                                            lgr +
                                            " LEFT JOIN log_" +
                                            ftt +
                                            " ON (" +
                                            lgr +
                                            ".form_" +
                                            ftt +
                                            "_fk=log_" +
                                            ftt +
                                            `.id AND (upper(log_${ftt}.sys_period) > log_time AND lower(log_${ftt}.sys_period) <= log_time)) WHERE form_` +
                                            t +
                                            "_fk = log_" +
                                            t +
                                            `.id  AND (upper(log_${t}.sys_period) > log_time AND lower(log_${t}.sys_period) <= log_time)) AS ` +
                                            fk +
                                            "_auto_name"
                                    );
                                } else {
                                    vf.push(",NULL::integer[] AS " + fk);
                                    vf.push(
                                        ",NULL::text[] AS " + fk + "_auto_name"
                                    );
                                    lvf.push(",NULL::integer[] AS " + fk);
                                    lvf.push(
                                        ",NULL::text[] AS " + fk + "_auto_name"
                                    );
                                }
                            } else {
                                vf.push(",NULL::integer[] AS " + fk);
                                vf.push(
                                    ",NULL::text[] AS " + fk + "_auto_name"
                                );
                                lvf.push(",NULL::integer[] AS " + fk);
                                lvf.push(
                                    ",NULL::text[] AS " + fk + "_auto_name"
                                );
                            }
                        } else {
                            for (const ty in ft) {
                                // for all types
                                try {
                                    const tya =
                                        Object.keys(ft).length > 1
                                            ? "_" + ty
                                            : "";
                                    let gra = "";
                                    let lgra = "";

                                    // _auto_name for single-value
                                    if (fk in fl && fk in allsrc) {
                                        gra =
                                            ",(SELECT auto_name FROM form_" +
                                            fl[fk].model.source +
                                            " WHERE " +
                                            fl[fk].model.sourceid +
                                            "=form_" +
                                            t +
                                            "." +
                                            fk +
                                            ") AS " +
                                            fk;
                                        lgra =
                                            ",(SELECT auto_name FROM log_" +
                                            fl[fk].model.source +
                                            " WHERE " +
                                            fl[fk].model.sourceid +
                                            "=log_" +
                                            t +
                                            "." +
                                            fk +
                                            ` AND (upper(log_${fl[fk].model.source}.sys_period) > log_time AND lower(log_${fl[fk].model.source}.sys_period) <= log_time)) AS ` +
                                            fk;
                                    }

                                    // field (value or typed-NULL) and optionally _auto_name
                                    if (fk in fl && tya === "") {
                                        if (field_type(fl[fk]) == "array") {
                                            vf.push(
                                                ",array_to_string(form_" +
                                                    t +
                                                    "." +
                                                    fk +
                                                    ",'|','')"
                                            );
                                            lvf.push(
                                                ",array_to_string(log_" +
                                                    t +
                                                    "." +
                                                    fk +
                                                    ",'|','')"
                                            );
                                        } else {
                                            vf.push(",form_" + t + "." + fk);
                                            lvf.push(",log_" + t + "." + fk);
                                        }
                                        if (gra !== "")
                                            vf.push(gra + "_auto_name");
                                        if (lgra !== "")
                                            lvf.push(lgra + "_auto_name");
                                    } else if (
                                        fk in fl &&
                                        (field_type(fl[fk]) === ty ||
                                            (field_type(fl[fk]) == "array" &&
                                                ty == "text"))
                                    ) {
                                        if (field_type(fl[fk]) == "array") {
                                            vf.push(
                                                ",array_to_string(form_" +
                                                    t +
                                                    "." +
                                                    fk +
                                                    ",'|','') AS " +
                                                    fk +
                                                    tya
                                            );
                                            lvf.push(
                                                ",array_to_string(log_" +
                                                    t +
                                                    "." +
                                                    fk +
                                                    ",'|','') AS " +
                                                    fk +
                                                    tya
                                            );
                                        } else {
                                            vf.push(
                                                ",form_" +
                                                    t +
                                                    "." +
                                                    fk +
                                                    " AS " +
                                                    fk +
                                                    tya
                                            );
                                            lvf.push(
                                                ",log_" +
                                                    t +
                                                    "." +
                                                    fk +
                                                    " AS " +
                                                    fk +
                                                    tya
                                            );
                                        }
                                        if (gra !== "")
                                            vf.push(gra + tya + "_auto_name");
                                        if (lgra !== "")
                                            lvf.push(lgra + tya + "_auto_name");
                                    } else {
                                        vf.push(
                                            ",NULL" +
                                                null_type(ty) +
                                                " AS " +
                                                fk +
                                                tya
                                        );
                                        lvf.push(
                                            ",NULL" +
                                                null_type(ty) +
                                                " AS " +
                                                fk +
                                                tya
                                        );
                                        if (fk in allsrc) {
                                            vf.push(
                                                ",NULL AS " + fk + "_auto_name"
                                            );
                                            lvf.push(
                                                ",NULL AS " + fk + "_auto_name"
                                            );
                                        }
                                    }
                                } catch (e) {
                                    console.log(e);
                                }
                            }
                        }
                    }

                    // _external_id for patient_id columns
                    if ("patient_id" in fl && !("external_id" in fl)) {
                        vf.push(
                            ",(SELECT external_id FROM form_patient WHERE id=form_" +
                                t +
                                ".patient_id) AS external_id"
                        );
                        lvf.push(
                            ",(SELECT external_id FROM log_patient WHERE id=log_" +
                                t +
                                `.patient_id AND (upper(log_patient.sys_period) > log_time AND lower(log_patient.sys_period) <= log_time)) AS external_id`
                        );
                    }

                    vf.push(" FROM " + sff + " INNER JOIN form_" + t);
                    vf.push(
                        " ON " + sff + ".form_" + t + "_fk=form_" + t + ".id"
                    );
                    lvf.push(" FROM " + lsff + " INNER JOIN log_" + t);
                    lvf.push(
                        " ON (" +
                            lsff +
                            ".form_" +
                            t +
                            "_fk=log_" +
                            t +
                            `.id AND (upper(log_patient.sys_period) > log_time AND lower(log_patient.sys_period) <= log_time))`
                    );

                    // filter out any test patients from all subforms
                    // note: we do not want to filter out test patients from direct forms because
                    //       the sql could also be used by client, where as subform queries will
                    //       never be used by clients so it is ok to filter out test patients here
                    if ("patient_id" in fl) {
                        vf.push(" INNER JOIN form_patient");
                        vf.push(
                            " ON form_" +
                                t +
                                ".patient_id = form_patient.id AND (form_patient.is_test='No' OR form_patient.is_test IS NULL)"
                        );
                        lvf.push(" INNER JOIN log_patient");
                        lvf.push(
                            " ON log_" +
                                t +
                                `.patient_id = log_patient.id AND (log_patient.is_test='No' OR log_patient.is_test IS NULL) AND (upper(log_patient.sys_period) > log_time AND lower(log_patient.sys_period) <= log_time)`
                        );
                    }

                    vf.push(
                        " WHERE (form_" +
                            t +
                            ".deleted=FALSE OR form_" +
                            t +
                            ".deleted IS NULL)"
                    );
                    lvf.push(
                        " WHERE (log_" +
                            t +
                            ".deleted=FALSE OR log_" +
                            t +
                            ".deleted IS NULL)"
                    );
                    sf[t] = vf.join("");
                    lsf[t] = lvf.join("");
                }
                sviews[sb] = sf;
                logsviews[sb] = lsf;
            }
        }

        // cache globally for frequent usage
        this.shared.view = {
            form: fviews,
            subform: sviews,
            links: lviews,
            subparents: subfk,
            lform: logviews,
            lsubform: logsviews,
        };
    }

    // override this function in the customer folder if necessary
    dynamic_fields_dashboard_admin(user, form, params) {
        return user.is_admin == "Yes";
    }

    async sql_form(ctx, form, params = {}) {
        const user = ctx.user;
        this.init();
        const flt = [],
            flt_cus = [],
            flt_kw = [],
            components = {};
        let par = [],
            sql = "",
            rankParams = [],
            row_count = false,
            defview = "";

        // limit
        if (!("limit" in params))
            params.limit = this.shared.config.limits.max_rows;
        // filters
        params.filter = this.fx.getParamsFilter(
            "filter" in params ? params.filter : []
        );
        let lprefix = ["form_", ""];
        if (params.asof) {
            lprefix = ["log_", "l"];
        }
        if ("archived" in params) {
            // no archived filter if archived=all
            if (params.archived == "" || params.archived == "true")
                params.filter.archived = "true";
        } else {
            params.filter.archived = "false";
        }
        if (
            this.dsl.get_type(params.keywords) === "string" &&
            params.keywords !== "" &&
            !(
                this.dsl.get_type(
                    this.shared.DSL[form].model[this.shared.dslSearchColumn]
                ) === "array" &&
                this.shared.DSL[form].model[this.shared.dslSearchColumn]
                    .length > 0
            )
        ) {
            params.filter["auto_name"] = ["%" + params.keywords + "%"];
            delete params.keywords;
        }
        if ("deleted" in params) {
            // no archived filter if archived=all
            if (params.deleted == "" || params.deleted == "true")
                params.filter.deleted = "true";
        }

        if ("where" in params && params.where) flt.push(params.where);

        const rls_access = this.filters.get_rls_filter(ctx, form);
        if (rls_access != "") {
            flt.push(rls_access);
        }
        if ("parent_id" in params || "parent_form" in params) {
            if (!params.parent_id || !params.parent_form) {
                return {
                    error: "parent_id and parent_form are both required when one is provided",
                };
            }
            if (!(params.parent_form in this.shared.view.form)) {
                return {
                    error:
                        "Parent form: " +
                        params.parent_form +
                        " doesn't exists in DSL",
                };
            }
        }

        if (form in this.shared.view.form) {
            for (const [k, v] of Object.entries(params.filter)) {
                const forlt = [];
                let conj = "OR";

                if (!(k in this.shared.DSL[form].fields))
                    return {
                        error:
                            "Filter field: " +
                            k +
                            " not found in form: " +
                            form,
                    };

                // create WHERE clause & parameters
                for (let vv of typeof v == "object" && Array.isArray(v)
                    ? v
                    : [v]) {
                    // support negative filters
                    let neg = false;
                    let rec = "";
                    if (typeof vv == "string") {
                        const sides = vv.split(/\.\.|\.<|>\.|></);
                        if (sides.length === 1) {
                            if (~sides[0].indexOf("!")) {
                                neg = true;
                                conj = "AND";
                                vv = vv.substr(1).trim();
                            }
                            if (~sides[0].indexOf(">")) {
                                // '>12' or '12>'
                                vv = vv.replace(">", "");
                                rec = ">";
                            }
                            if (~sides[0].indexOf("<")) {
                                // '<14' or '14<'
                                vv = vv.replace("<", "");
                                rec = "<";
                            }
                        } else if (sides.length === 2) {
                            if (sides[0] !== "" && sides[1] !== "") {
                                // 'x??y'
                                if (~vv.indexOf("..")) {
                                    // '12..14'
                                    vv = [sides[0], sides[1]];
                                    rec = "..";
                                }
                                if (~vv.indexOf(">.")) {
                                    // '12>.14'
                                    vv = [sides[0], sides[1]];
                                    rec = ">";
                                }
                                if (~vv.indexOf(".<")) {
                                    // '12.<14'
                                    vv = [sides[0], sides[1]];
                                    rec = "<";
                                }
                                if (~vv.indexOf("><")) {
                                    // '12><14'
                                    vv = [sides[0], sides[1]];
                                    rec = "><";
                                }
                            } else {
                                if (~vv.indexOf("..")) {
                                    if (sides[1] === "") {
                                        rec = ">=";
                                    } else if (sides[0] === "") {
                                        rec = "<=";
                                    }
                                    vv = vv.replace("..", "");
                                } else if (~vv.indexOf(">.")) {
                                    rec = ">";
                                    vv = vv.replace(">.", "");
                                } else if (~vv.indexOf(".<")) {
                                    rec = "<";
                                    vv = vv.replace(".<", "");
                                }
                            }
                        }
                    }
                    if (
                        typeof this.shared.DSL[form].fields[k].model.source ==
                            "string" &&
                        this.shared.DSL[form].fields[k].model.multi
                    ) {
                        // gerund linked filter
                        // v can only be integer (null/single/range + NOT)
                        const gsrc =
                            this.shared.DSL[form].fields[k].model.source;
                        let gwhr, gwhs;
                        if (this.shared.DSL[form].fields[k].view.findmulti) {
                            gwhr =
                                lprefix[0] +
                                form +
                                ".id in (SELECT form_" +
                                form +
                                `_fk FROM ${lprefix[1]}gr_form_` +
                                form +
                                "_" +
                                k +
                                "_to_" +
                                gsrc +
                                "_id WHERE ";
                            gwhs = ")";
                        } else {
                            gwhr =
                                lprefix[0] +
                                form +
                                ".id IN (SELECT form_" +
                                form +
                                `_fk FROM ${lprefix[1]}gr_form_` +
                                form +
                                "_" +
                                k +
                                "_to_" +
                                gsrc +
                                "_id WHERE ";
                            gwhs = ")";
                        }

                        if (
                            vv === null ||
                            typeof vv == "undefined" ||
                            v == "" ||
                            vv.toLowerCase() === "null"
                        ) {
                            // NULL
                            forlt.push(
                                gwhr +
                                    "form_" +
                                    gsrc +
                                    "_fk IS" +
                                    (neg ? " NOT" : "") +
                                    " NULL" +
                                    gwhs
                            );
                        } else if (
                            typeof vv == "string" &&
                            vv.indexOf("..") > -1
                        ) {
                            // range
                            const [vv1, vv2] = vv.split("..");
                            if (
                                vv1 &&
                                vv2 &&
                                typeof vv1 != "undefined" &&
                                typeof vv2 != "undefined"
                            ) {
                                forlt.push(
                                    gwhr +
                                        (neg ? "NOT " : "") +
                                        "(form_" +
                                        gsrc +
                                        "_fk >= %L AND form_" +
                                        gsrc +
                                        "_fk <= %L)" +
                                        gwhs
                                );
                                par.push(vv1);
                                par.push(vv2);
                            } else if (vv1 && typeof vv1 != "undefined") {
                                forlt.push(
                                    gwhr +
                                        (neg ? "NOT " : "") +
                                        "(form_" +
                                        gsrc +
                                        "_fk >= %L)" +
                                        gwhs
                                );
                                par.push(vv1);
                            } else if (vv2 && typeof vv2 != "undefined") {
                                forlt.push(
                                    gwhr +
                                        (neg ? "NOT " : "") +
                                        "(form_" +
                                        gsrc +
                                        "_fk <= %L)" +
                                        gwhs
                                );
                                par.push(vv2);
                            }
                        } else {
                            // default
                            forlt.push(
                                gwhr +
                                    "(form_" +
                                    gsrc +
                                    "_fk" +
                                    (neg ? "<>" : "=") +
                                    "%L" +
                                    (neg
                                        ? " OR form_" + gsrc + "_fk IS NULL)"
                                        : ")") +
                                    gwhs
                            );
                            par.push(vv);
                        }
                    } else {
                        // regular field filter
                        if (rec !== "") {
                            if (Array.isArray(vv)) {
                                if (rec === "..") {
                                    forlt.push(
                                        `(${lprefix[0]}` +
                                            form +
                                            "." +
                                            k +
                                            " >= %L AND " +
                                            lprefix[0] +
                                            form +
                                            "." +
                                            k +
                                            " <= %L)"
                                    );
                                    par.push(vv[0]);
                                    par.push(vv[1]);
                                } else if (rec === ">") {
                                    forlt.push(
                                        `(${lprefix[0]}` +
                                            form +
                                            "." +
                                            k +
                                            " > %L AND " +
                                            lprefix[0] +
                                            form +
                                            "." +
                                            k +
                                            " <= %L)"
                                    );
                                    par.push(vv[0]);
                                    par.push(vv[1]);
                                } else if (rec === "<") {
                                    forlt.push(
                                        `(${lprefix[0]}` +
                                            form +
                                            "." +
                                            k +
                                            " >= %L AND " +
                                            lprefix[0] +
                                            form +
                                            "." +
                                            k +
                                            " < %L)"
                                    );
                                    par.push(vv[0]);
                                    par.push(vv[1]);
                                } else if (rec === "><") {
                                    forlt.push(
                                        `(${lprefix[0]}` +
                                            form +
                                            "." +
                                            k +
                                            " > %L AND " +
                                            lprefix[0] +
                                            form +
                                            "." +
                                            k +
                                            " < %L)"
                                    );
                                    par.push(vv[0]);
                                    par.push(vv[1]);
                                }
                            } else {
                                forlt.push(
                                    `(${lprefix[0]}` +
                                        form +
                                        "." +
                                        k +
                                        " " +
                                        rec +
                                        " %L)"
                                );
                                par.push(vv);
                            }
                        } else if (
                            vv === null ||
                            typeof vv == "undefined" ||
                            v == "" ||
                            vv.toLowerCase() === "null"
                        ) {
                            // NULL
                            forlt.push(
                                lprefix[0] +
                                    form +
                                    "." +
                                    k +
                                    " IS" +
                                    (neg ? " NOT" : "") +
                                    " NULL"
                            );
                        } else if (
                            typeof vv == "string" &&
                            vv.indexOf("%") > -1
                        ) {
                            // LIKE
                            forlt.push(
                                "(" +
                                    lprefix[0] +
                                    form +
                                    "." +
                                    k +
                                    (neg ? " NOT" : "") +
                                    " ILIKE %L" +
                                    (neg
                                        ? " OR " +
                                          lprefix[0] +
                                          form +
                                          "." +
                                          k +
                                          " IS NULL"
                                        : "") +
                                    ")"
                            );
                            // we do not want to run ILIKE %keyword% on large tables for perf reasons
                            if (
                                this.shared.DSL[form].model.large &&
                                vv.startsWith("%")
                            ) {
                                par.push(vv.substring(1));
                            } else {
                                par.push(vv);
                            }
                        } else if (
                            typeof vv == "string" &&
                            vv.indexOf("..") > -1
                        ) {
                            // range
                            const [vv1, vv2] = vv.split("..");
                            if (
                                vv1 &&
                                vv2 &&
                                typeof vv1 != "undefined" &&
                                typeof vv2 != "undefined"
                            ) {
                                forlt.push(
                                    (neg ? "NOT " : "") +
                                        `(${lprefix[0]}` +
                                        form +
                                        "." +
                                        k +
                                        " >= %L AND " +
                                        lprefix[0] +
                                        form +
                                        "." +
                                        k +
                                        " <= %L)"
                                );
                                par.push(vv1);
                                par.push(vv2);
                            } else if (vv1 && typeof vv1 != "undefined") {
                                forlt.push(
                                    (neg ? "NOT " : "") +
                                        `(${lprefix[0]}` +
                                        form +
                                        "." +
                                        k +
                                        " >= %L)"
                                );
                                par.push(vv1);
                            } else if (vv2 && typeof vv2 != "undefined") {
                                forlt.push(
                                    (neg ? "NOT " : "") +
                                        `(${lprefix[0]}` +
                                        form +
                                        "." +
                                        k +
                                        " <= %L)"
                                );
                                par.push(vv2);
                            } else if (
                                ["json", "text"].includes(
                                    this.shared.DSL[form].fields[k].model.type
                                )
                            ) {
                                if (neg) {
                                    forlt.push(
                                        lprefix[0] + form + "." + k + ">%L"
                                    );
                                    par.push("");
                                } else {
                                    forlt.push(
                                        lprefix[0] + form + "." + k + "=%L"
                                    );
                                    par.push("");
                                }
                            } else {
                                // blank: date, datetime, decimal, image, int, password, subform, time
                                forlt.push(
                                    lprefix[0] +
                                        form +
                                        "." +
                                        k +
                                        " IS" +
                                        (neg ? " NOT" : "") +
                                        " NULL"
                                );
                            }
                        } else if (["FALSE"].indexOf(vv.toUpperCase()) > -1) {
                            // boolean FALSE (also account for NULL)
                            forlt.push(
                                (neg ? "NOT " : "") +
                                    `(${lprefix[0]}` +
                                    form +
                                    "." +
                                    k +
                                    "=%s OR " +
                                    lprefix[0] +
                                    form +
                                    "." +
                                    k +
                                    " IS NULL)"
                            );
                            par.push(vv.toUpperCase());
                        } else if (["TRUE"].indexOf(vv.toUpperCase()) > -1) {
                            // boolean TRUE
                            forlt.push(
                                "(" +
                                    lprefix[0] +
                                    form +
                                    "." +
                                    k +
                                    (neg ? "<>" : "=") +
                                    "%s" +
                                    (neg
                                        ? " OR " +
                                          lprefix[0] +
                                          form +
                                          "." +
                                          k +
                                          " IS NULL"
                                        : "") +
                                    ")"
                            );
                            par.push(vv.toUpperCase());
                        } else if (
                            typeof this.shared.DSL[form].fields[k].model
                                .source != "string" &&
                            this.shared.DSL[form].fields[k].model.multi
                        ) {
                            // multi-value without gerund
                            const mvl = [];
                            for (const vvv of vv.split(",")) {
                                mvl.push(
                                    "(array_to_string(ARRAY[NULL::VARCHAR]||" +
                                        lprefix[0] +
                                        form +
                                        "." +
                                        k +
                                        "||ARRAY[NULL::VARCHAR], '|', '')" +
                                        (neg ? " NOT" : "") +
                                        " ILIKE %L)"
                                );
                                par.push("%|%" + vvv + "%|%");
                                //TODO:SOMEDAY - CM:2017-11-02 - after a few months
                                //               when all servers support varchar[]
                                //               use the following lines instead
                                //mvl.push("(%L = ANY(form_" + form + "." + k + "))");
                                //par.push(vvv);
                            }
                            forlt.push(mvl.join(" AND "));
                        } else if (
                            typeof vv == "string" &&
                            /[a-z]+/i.test(vv)
                        ) {
                            // case-insensitive string, LOWER to make use of lower indexes
                            forlt.push(
                                "(LOWER" +
                                    `(${lprefix[0]}` +
                                    form +
                                    "." +
                                    k +
                                    ")" +
                                    (neg ? "<>" : "=") +
                                    "LOWER(%L)" +
                                    (neg
                                        ? " OR " +
                                          `${lprefix[0]}` +
                                          form +
                                          "." +
                                          k +
                                          " IS NULL)"
                                        : ")")
                            );
                            par.push(vv);
                        } else {
                            // default
                            forlt.push(
                                "(" +
                                    lprefix[0] +
                                    form +
                                    "." +
                                    k +
                                    (neg ? "<>" : "=") +
                                    "%L" +
                                    (neg
                                        ? " OR " +
                                          lprefix[0] +
                                          form +
                                          "." +
                                          k +
                                          " IS NULL"
                                        : "") +
                                    ")"
                            );
                            par.push(vv);
                        }
                    }
                }
                flt.push(
                    (forlt.length > 1 ? "(" : "") +
                        forlt.join(" " + conj + " ") +
                        (forlt.length > 1 ? ")" : "")
                );
            }

            // form filters
            const fflt = this.filters.filter_form(user, form, params);
            if (fflt.length > 0) {
                for (const [ffk, ffv] of fflt) {
                    flt.push(ffk);
                    if (ffv && ffv.length > 0) par = par.concat(ffv);
                }
            }
            // site filters
            // we dont want to apply site filter if we are getting subform
            if (!params.skip_site_filter) {
                const ffst = this.filters.filter_site(ctx, form);
                if (ffst.length > 0) {
                    for (const [ffk, ffv] of ffst) {
                        flt.push(ffk);
                        if (ffv && ffv.length > 0) par = par.concat(ffv);
                    }
                }
            }

            // form keyword filters
            let fflt_kw = [],
                column = [];
            [fflt_kw, column, params] = this.filters.filter_keywords(
                user,
                form,
                params
            );
            if (fflt_kw.length > 0) {
                for (const [ffk, ffv] of fflt_kw) {
                    flt_kw.push(ffk);
                    if (ffv && ffv.length > 0) {
                        par = par.concat(ffv);
                        rankParams = rankParams.concat(ffv);
                    }
                }
            }

            // concat multiple queries, with OR conditions
            const fflt_cust = this.filters.filter_form_custom(
                user,
                form,
                params
            );
            if (fflt_cust.length > 0) {
                for (const [ffk, ffv] of fflt_cust) {
                    flt_cus.push(ffk);
                    if (ffv && ffv.length > 0) par = par.concat(ffv);
                }
            }

            // sort
            const psort = this.fx.getParamsSort(
                "sort" in params ? params.sort : ["id"]
            );
            const sarray = [];
            if (form in this.shared.view.form) {
                for (const s of psort) {
                    if (
                        s[0] === this.shared.vectorRankColumn &&
                        column !== ""
                    ) {
                        continue;
                    }
                    if (!(s[0] in this.shared.DSL[form].fields))
                        return {
                            error:
                                "Sort field: " +
                                s[0] +
                                " not found in form: " +
                                form,
                        };
                    if (
                        this.dsl.get_type(
                            this.shared.DSL[form].fields[s[0]].model.source
                        ) === "string" &&
                        !this.shared.DSL[form].fields[s[0]].model.multi &&
                        !(
                            this.shared.DSL[form].fields[s[0]].model.type ===
                            "subform"
                        )
                    )
                        sarray.push([s[0] + "_auto_name"]);
                    else sarray.push(s);
                }
            }
            params.sort = sarray.map((q) => q.join(" ")).join(",");
            defview = " ORDER BY " + params.sort + " LIMIT " + params.limit;
            if ("page_number" in params && parseInt(params.page_number) > 0)
                defview +=
                    " OFFSET " + parseInt(params.page_number) * params.limit;

            row_count = "fields" in params && params.fields == "count";
            if (row_count) defview = "";
            // concat multiple queries
            let fsql;
            const sflt = flt.length > 0 ? " AND " + flt.join(" AND ") : "";
            const sflt_kw =
                flt_kw.length > 0 ? " AND " + flt_kw.join(" AND ") : "";
            const sflt_cust =
                flt_cus.length > 0 ? " OR " + flt_cus.join(" OR ") : "";
            if (params.asof) fsql = this.shared.view.lform[form];
            else fsql = this.shared.view.form[form];
            if (typeof fsql === "string") fsql = [fsql];
            fsql = fsql.map((q) => {
                if (column && typeof column === "string" && column.length > 0) {
                    const inj = this.fx.injectStringAfterSubstring(
                        q,
                        "SELECT ",
                        column
                    );
                    return inj + sflt + sflt_kw + sflt_cust;
                }
                return q + sflt + sflt_kw + sflt_cust;
            });

            // filter parent row's archive by:
            //   if filter is archived=false, parent.archived must be false
            //     NOTE: this is the most common use-case
            //   if filter is archived=true, no need to ensure parent is also archived
            //     NOTE: this misses rows where parent is archived and subform is not archived
            if (
                "archived" in params.filter &&
                params.filter.archived == "false" &&
                form in this.shared.view.subparents
            ) {
                for (const [k, _] of Object.entries(
                    this.shared.view.subparents[form]
                )) {
                    for (const fk in fsql) {
                        // apply filter only to relevant parent table query
                        if (fsql[fk].includes(" JOIN form_" + k + " "))
                            fsql[fk] +=
                                " AND (form_" +
                                k +
                                ".archived=FALSE OR form_" +
                                k +
                                ".archived IS NULL)";
                    }
                }
            }
            // repeat params for each UNION

            // update this to make sure top one is of ts vector field
            if (params.asof) {
                let logParams = [];
                for (const q of fsql) {
                    const count = (q.match(/log_time/g) || []).length;
                    for (let i = 0; i < count; ++i) {
                        logParams.push(params.asof);
                    }
                    if (logParams.length > 0) logParams = logParams.concat(par);
                }
                par = logParams;
            }

            if (rankParams.length > 0) {
                par = rankParams.concat(par);
            }
            let npar = [];
            for (const _f of fsql) npar = npar.concat(par);
            par = npar;

            // finalize queries
            sql = "(" + fsql.join("\n  UNION ALL\n") + ")";
            // this is supposed to be a temp solution for parent_id and parent_form
            if ("parent_id" in params || "parent_form" in params) {
                if (!params.parent_id || !params.parent_form) {
                    return {
                        error: "parent_id and parent_form are both required when one is provided",
                    };
                }
                if (!(params.parent_form in this.shared.view.form)) {
                    return {
                        error:
                            "Parent form: " +
                            params.parent_form +
                            " doesn't exists in DSL",
                    };
                }
                sql =
                    "WITH q AS " +
                    sql +
                    ` SELECT * FROM q WHERE q.parent_form = %L AND q.parent_id = %L `;
                par.push(params.parent_form);
                par.push(params.parent_id);
            }
            sql = sql + defview;
            components[form] = fsql.map((q) => q + defview);
        } else if (form in this.shared.view.subform) {
            const scomp = [];
            for (const [k, v] of Object.entries(
                this.shared.view.subform[form]
            )) {
                scomp.push(v);
                components[k] = v + defview;
            }
            sql = "(" + scomp.join("\n  UNION ALL\n") + ")" + defview;
        } else if (form in this.shared.view.links) {
            const lcomp = [];
            for (const [k, v] of Object.entries(this.shared.view.links[form])) {
                lcomp.push(v);
                components[k] = v + defview;
            }
            sql = "(" + lcomp.join("\n  UNION ALL\n") + ")" + defview;
        } else {
            return {
                error: "Invalid form: " + form,
            };
        }

        if (params.asof) sql = sql.replace(/log_time/g, "%L");
        if (row_count) {
            sql = `WITH q AS ${sql} SELECT COUNT(*) as count  FROM q`;
        }

        return {
            sql: sql,
            components: components,
            par: par,
        };
    }
};
