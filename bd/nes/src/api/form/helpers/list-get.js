"use strict";
const SubformGetClass = require("./subform");
const _ = require("lodash");

module.exports = class FormListGetClass extends SubformGetClass {
    constructor(nes, exporting = false) {
        super(nes, exporting);
        this.nes = nes;
        this.shared = nes.shared;
        this.dsl = nes.modules.dsl;
    }

    async getQueryBase(ctx, urlpath, opts = {}) {
        throw new Error("Not implemented");
    }

    async getData(ctx, urlpath, opts = {}) {
        const form = urlpath.path[2];
        const base = await this.getQueryBase(ctx, urlpath, opts);
        base.sql = base.sql.replace(/,\(CASE(.*?)_index/g, "");
        const request = ctx.request.body;
        const results =
            request.pivotCols &&
            request.pivotMode &&
            request.pivotCols.length > 0
                ? await this.executePivotQuery(request, base)
                : await this.db.env.rw.query(
                      this.buildSql(base, request),
                      base.par
                  );

        const rowCount = this.getRowCount(request, results);
        let resultsForPage = this.cutResultsToPageSize(request, results);
        resultsForPage = this.filters.filter_fields(
            ctx,
            form,
            resultsForPage,
            {}
        );

        return {
            rows: resultsForPage,
            lastRow: rowCount,
            pivotFields: await this.getPivotFields(request, base),
        };
    }

    async executePivotQuery(request, base) {
        const { pivotCols, valueCols } = request;
        if (!pivotCols?.length || !valueCols?.length) {
            return [];
        }

        const pivotValues = await this.getPivotValues(request, base);
        const pivotQuery = this.buildPivotQuery(request, base, pivotValues);

        return this.db.env.rw.query(pivotQuery, base.par);
    }

    async getPivotValues(request, base) {
        const { pivotCols } = request;
        const pivotValues = {};

        for (const pivotCol of pivotCols) {
            const sql = `
                SELECT DISTINCT "${pivotCol.field}"
                FROM (${base.sql})
                WHERE "${pivotCol.field}" IS NOT NULL
                ORDER BY "${pivotCol.field}"
            `;
            const results = await this.db.env.rw.query(sql, base.par);
            pivotValues[pivotCol.field] = results.map((r) => r[pivotCol.field]);
        }

        return pivotValues;
    }

    buildPivotQuery(request, base, pivotValues) {
        const { rowGroupCols, groupKeys, valueCols, pivotCols } = request;

        const groupsToUse = rowGroupCols.slice(
            groupKeys.length,
            groupKeys.length + 1
        );
        const groupByCols = [];
        const selectCols = [];

        groupsToUse.forEach((col) => {
            groupByCols.push(`"${col.field}"`);
            selectCols.push(`"${col.field}"`);

            const source = base.dsl.fields[col.field]?.model?.source;
            if (
                source &&
                typeof source === "string" &&
                base.sql.includes(`${col.field}_auto_name`)
            ) {
                groupByCols.push(`"${col.field}_auto_name"`);
                selectCols.push(`"${col.field}_auto_name"`);
            }
        });

        const pivotSelects = [];
        valueCols.forEach((valueCol) => {
            pivotCols.forEach((pivotCol) => {
                const pivotSource =
                    base.dsl.fields[pivotCol.field]?.model?.source;
                const hasPivotAutoName =
                    pivotSource &&
                    typeof pivotSource === "string" &&
                    base.sql.includes(`${pivotCol.field}_auto_name`);

                pivotValues[pivotCol.field].forEach((pivotValue) => {
                    const safeValue =
                        typeof pivotValue === "string"
                            ? `'${pivotValue}'`
                            : pivotValue;

                    const columnName = `${pivotValue}__${valueCol.field}`;

                    pivotSelects.push(`
                        ${valueCol.aggFunc}(CASE 
                            WHEN "${pivotCol.field}" = ${safeValue} 
                            THEN ${valueCol.field} 
                            ELSE NULL 
                        END) as "${columnName}"
                    `);

                    if (hasPivotAutoName) {
                        pivotSelects.push(`
                            MAX(CASE 
                                WHEN "${pivotCol.field}" = ${safeValue} 
                                THEN "${pivotCol.field}_auto_name"
                                ELSE NULL 
                            END) as "${columnName}_auto_name"
                        `);
                    }
                });
            });
        });

        const SQL = `
            SELECT 
                ${selectCols.join(",")},
                ${pivotSelects.join(",")}
            FROM (${base.sql})
            ${this.createWhereSql(request)}
            ${groupByCols.length ? `GROUP BY ${groupByCols.join(", ")}` : ""}
            ${this.createOrderBySql(request, base)}
            ${this.createLimitSql(request)}
        `;

        return SQL;
    }

    async getPivotFields(request, base) {
        const { pivotCols, valueCols } = request;
        if (!pivotCols?.length || !valueCols?.length) {
            return [];
        }

        const pivotValues = await this.getPivotValues(request, base);
        const pivotFields = [];

        valueCols.forEach((valueCol) => {
            pivotCols.forEach((pivotCol) => {
                const pivotSource =
                    base.dsl.fields[pivotCol.field]?.model?.source;
                const hasPivotAutoName =
                    pivotSource &&
                    typeof pivotSource === "string" &&
                    base.sql.includes(`${pivotCol.field}_auto_name`);

                pivotValues[pivotCol.field].forEach((pivotValue) => {
                    const columnName = `${pivotValue}__${valueCol.field}`;
                    pivotFields.push(columnName);

                    if (hasPivotAutoName) {
                        pivotFields.push(`${columnName}_auto_name`);
                    }
                });
            });
        });

        return pivotFields;
    }

    flatten(arrayOfArrays) {
        return [].concat(...arrayOfArrays);
    }

    buildSql(base, request) {
        const { sql, par, dsl } = base;
        const selectSql = this.createSelectSql(request, base);
        const fromSql = ` FROM (${sql}) `;
        const whereSql = this.createWhereSql(request, base);
        const limitSql = this.createLimitSql(request, base);

        const orderBySql = this.createOrderBySql(request, base);
        const groupBySql = this.createGroupBySql(request, base);

        const SQL =
            selectSql + fromSql + whereSql + groupBySql + orderBySql + limitSql;

        return SQL;
    }

    cutResultsToPageSize(request, results) {
        const pageSize = request.endRow - request.startRow;
        if (results && results.length > pageSize) {
            return results.splice(0, pageSize);
        } else {
            return results;
        }
    }

    createSelectSql(request, base) {
        const rowGroupCols = request.rowGroupCols;
        const valueCols = request.valueCols;
        const groupKeys = request.groupKeys;
        const pivotMode = request.pivotMode;
        const pivotCols = request.pivotCols;

        if (this.isDoingGrouping(rowGroupCols, groupKeys)) {
            const colsToSelect = [];

            const rowGroupCol = rowGroupCols[groupKeys.length];
            colsToSelect.push(rowGroupCol.field);
            const source = base.dsl.fields[rowGroupCol.field].model.source;
            if (
                source &&
                typeof source == "string" &&
                base.sql.includes(`${rowGroupCol.field}_auto_name`)
            ) {
                colsToSelect.push(`"${rowGroupCol.field}_auto_name"`);
            }

            if (pivotMode && pivotCols && pivotCols.length > 0) {
                pivotCols.forEach(function (pivotCol) {
                    valueCols.forEach(function (valueCol) {
                        colsToSelect.push(
                            `${valueCol.aggFunc}(CASE WHEN "${pivotCol.field}" = '${pivotCol.value}' ` +
                                `THEN ${valueCol.field} ELSE NULL END) as "${pivotCol.field}_${valueCol.field}"`
                        );
                    });
                });
            } else {
                valueCols.forEach(function (valueCol) {
                    colsToSelect.push(
                        `${valueCol.aggFunc}(${valueCol.field}) as ${valueCol.field}`
                    );
                });
            }

            return " select " + colsToSelect.join(", ");
        }

        return " select *";
    }

    createFilterSql(key, item) {
        if (item.filterType === "set" || item.filterType === "multi") {
            return this.createSetFilterSql(key, item);
        }

        if (item.operator) {
            const conditions = item.conditions || [];
            const operator = item.operator.toUpperCase();
            const filterParts = conditions
                .map((condition) => this.createSingleFilterSql(key, condition))
                .filter((part) => part !== "true");

            return filterParts.length > 0
                ? `(${filterParts.join(` ${operator} `)})`
                : "true";
        }

        return this.createSingleFilterSql(key, item);
    }

    createSingleFilterSql(key, item) {
        switch (item.filterType) {
            case "text":
                return this.createTextFilterSql(key, item);
            case "number":
                return this.createNumberFilterSql(key, item);
            case "date":
                return this.createDateFilterSql(key, item);
            case "set":
                return this.createSetFilterSql(key, item);
            default:
                console.log("unknown filter type: " + item.filterType);
                return "true";
        }
    }

    createSetFilterSql(key, item) {
        const values = Array.isArray(item.values) ? item.values : [item.values];
        if (!values.length) return "true";

        const exclusionValues = values
            .filter((v) => typeof v === "string" && v.startsWith("!"))
            .map((v) => v.substring(1));
        const inclusionValues = values.filter(
            (v) => !(typeof v === "string" && v.startsWith("!"))
        );

        if (exclusionValues.length > 0) {
            const conditions = [];

            if (exclusionValues.length > 0) {
                conditions.push(
                    `("${key}" NOT IN ('${exclusionValues.join("','")}') OR "${key}" IS NULL)`
                );
            }
            return conditions[0] || "true";
        }

        const hasNull = inclusionValues.includes(null);
        const nonNullValues = inclusionValues.filter((v) => v !== null);

        const conditions = [];

        if (nonNullValues.length > 0) {
            conditions.push(`"${key}" in ('${nonNullValues.join("','")}')`);
        }

        if (hasNull) {
            conditions.push(`"${key}" IS NULL`);
        }

        return conditions.length > 1
            ? `(${conditions.join(" OR ")})`
            : conditions[0] || "true";
    }

    createNumberFilterSql(key, item) {
        switch (item.type) {
            case "equals":
                return `"${key}" = ${item.filter}`;
            case "notEqual":
                return `"${key}" != ${item.filter}`;
            case "greaterThan":
                return `"${key}" > ${item.filter}`;
            case "greaterThanOrEqual":
                return `"${key}" >= ${item.filter}`;
            case "lessThan":
                return `"${key}" < ${item.filter}`;
            case "lessThanOrEqual":
                return `"${key}" <= ${item.filter}`;
            case "inRange":
                return (
                    "(" +
                    `"${key}" >= ${item.filter}` +
                    " and " +
                    `"${key}" <= ${item.filterTo}` +
                    ")"
                );
            case "blank":
                return `"${key}" IS NULL`;
            case "notBlank":
                return `"${key}" IS NOT NULL`;
            default:
                console.log("unknown number filter type: " + item.type);
                return "true";
        }
    }

    createDateFilterSql(key, item) {
        switch (item.type) {
            case "equals":
                return `DATE("${key}") = DATE('${item.dateFrom}')`;
            case "notEqual":
                return `DATE("${key}") != DATE('${item.dateFrom}')`;
            case "greaterThan":
                return `"${key}" > '${item.dateFrom}'`;
            case "lessThan":
                return `"${key}" < '${item.dateFrom}'`;
            case "inRange":
                return `"${key}" BETWEEN '${item.dateFrom}' AND '${item.dateTo}'`;
            case "blank":
                return `"${key}" IS NULL`;
            case "notBlank":
                return `"${key}" IS NOT NULL`;
            default:
                console.log("unknown date filter type: " + item.type);
                return "true";
        }
    }

    createTextFilterSql(key, item) {
        switch (item.type) {
            case "equals":
                return `LOWER("${key}") = LOWER('${item.filter}')`;
            case "notEqual":
                return `LOWER("${key}") != LOWER('${item.filter}')`;
            case "contains":
                return `LOWER("${key}") like LOWER('%${item.filter}%')`;
            case "notContains":
                return `LOWER("${key}") not like LOWER('%${item.filter}%')`;
            case "startsWith":
                return `LOWER("${key}") like LOWER('${item.filter}%')`;
            case "endsWith":
                return `LOWER("${key}") like LOWER('%${item.filter}')`;
            case "blank":
                return `"${key}" IS NULL OR TRIM("${key}") = ''`;
            case "notBlank":
                return `"${key}" IS NOT NULL AND TRIM("${key}") != ''`;
            default:
                console.log("unknown text filter type: " + item.type);
                return "true";
        }
    }

    createWhereSql(request) {
        const rowGroupCols = request.rowGroupCols;
        const groupKeys = request.groupKeys;
        const filterModel = request.filterModel;

        const that = this;
        const whereParts = [];

        if (groupKeys.length > 0) {
            groupKeys.forEach(function (key, index) {
                const colName = rowGroupCols[index].field;
                whereParts.push(`"${colName}" = '${key}'`);
            });
        }

        if (filterModel) {
            const keySet = Object.keys(filterModel);
            keySet.forEach(function (key) {
                const item = filterModel[key];
                whereParts.push(that.createFilterSql(key, item));
            });
        }

        if (whereParts.length > 0) {
            return " where " + whereParts.join(" and ");
        } else {
            return "";
        }
    }

    createGroupBySql(request, base) {
        const rowGroupCols = request.rowGroupCols;
        const groupKeys = request.groupKeys;
        const pivotCols = request.pivotCols;
        const pivotMode = request.pivotMode;

        if (this.isDoingGrouping(rowGroupCols, groupKeys, base)) {
            const colsToGroupBy = [];

            const rowGroupCol = rowGroupCols[groupKeys.length];
            colsToGroupBy.push(`"${rowGroupCol.field}"`);
            const source = base.dsl.fields[rowGroupCol.field].model.source;
            if (
                source &&
                typeof source == "string" &&
                base.sql.includes(`${rowGroupCol.field}_auto_name`)
            ) {
                colsToGroupBy.push(`"${rowGroupCol.field}_auto_name"`);
            }

            if (pivotMode && pivotCols && pivotCols.length > 0) {
                pivotCols.forEach(function (pivotCol) {
                    colsToGroupBy.push(`"${pivotCol.field}"`);
                });
            }

            return " group by " + colsToGroupBy.join(", ");
        }

        return "";
    }

    createOrderBySql(request, base) {
        const rowGroupCols = request.rowGroupCols;
        const groupKeys = request.groupKeys;
        const sortModel = request.sortModel;

        const grouping = this.isDoingGrouping(rowGroupCols, groupKeys, base);

        const sortParts = [];
        if (sortModel) {
            const groupColIds = rowGroupCols
                .map((groupCol) => groupCol.id)
                .slice(0, groupKeys.length + 1);

            sortModel.forEach(function (item) {
                if (grouping && groupColIds.indexOf(item.colId) < 0) {
                    // ignore
                } else {
                    sortParts.push(`"${item.colId}" ${item.sort}`);
                }
            });
        }

        if (sortParts.length > 0) {
            return " order by " + sortParts.join(", ");
        } else {
            return "";
        }
    }

    isDoingGrouping(rowGroupCols, groupKeys) {
        return rowGroupCols.length > groupKeys.length;
    }

    createLimitSql(request) {
        const startRow = request.startRow;
        const endRow = request.endRow;
        const pageSize = endRow - startRow;
        return " limit " + (pageSize + 1) + " offset " + startRow;
    }

    getRowCount(request, results) {
        if (results === null || results === undefined || results.length === 0) {
            return null;
        }
        const currentLastRow = request.startRow + results.length;
        return currentLastRow <= request.endRow ? currentLastRow : -1;
    }
};
