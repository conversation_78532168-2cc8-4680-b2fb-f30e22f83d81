"use strict";
const _ = require("lodash");
const GetAccessClass = require("../../my/get_access");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.get_access = new GetAccessClass(nes);
    }

    filter_fields(ctx, form, rows, params) {
        const frows = [];
        for (const row of rows) {
            const frow = this.auth.access_filter_fields(ctx, form, row, params);
            if (frow) frows.push(frow);
        }
        return frows;
    }

    // This function can be used to insert 'OR' condition queries
    filter_form_custom(user, form, params) {
        const filter = [];
        if (
            form == "patient_schedule_encounter" &&
            user.role != "nurse" &&
            !params.filter.id
        ) {
            //handle filters explicitly to make sure correct data is sent back
            let q = ` (form_${form}.reoccur = %L`;
            const vals = ["Yes"];
            if (!_.isEmpty(params.filter.patient_id)) {
                q += ` AND form_${form}.patient_id IN %L`;
                vals.push(params.filter.patient_id);
            }
            if (!_.isEmpty(params.filter.calendar_id)) {
                q += ` AND form_${form}.calendar_id IN %L`;
                vals.push(params.filter.calendar_id);
            }
            if (!_.isEmpty(params.filter.user_id)) {
                q += ` AND form_${form}.user_id IN %L`;
                vals.push(params.filter.user_id);
            }
            if (!_.isEmpty(params.filter.active)) {
                q += `AND form_${form}.active IN %L`;
                vals.push(params.filter.active);
            }
            if (!_.isEmpty(params.filter.portal_display)) {
                if (params.filter.portal_display[0].indexOf("!") > -1)
                    q += ` AND form_${form}.portal_display <> '${params.filter.portal_display[0].replace("!", "")}'`;
                else
                    q += ` AND form_${form}.portal_display = '${params.filter.portal_display[0]}'`;
            }
            if (!_.isEmpty(params.filter.appointment_status)) {
                const index =
                    params.filter.appointment_status.indexOf("!Cancelled");
                if (index > -1) {
                    params.filter.appointment_status.splice(index, 1);
                }
                if (!_.isEmpty(params.filter.appointment_status)) {
                    q += ` AND form_${form}.appointment_status IN %L`;
                    vals.push(params.filter.appointment_status);
                }
            }
            q += ` AND form_${form}.archived is not true AND form_${form}.deleted is not true )`;
            filter.push([q, vals]);
        }

        return filter;
    }

    filter_form(user, form, params) {
        const flt = [];
        if (
            this.dsl.get_type(params.asof) === "string" &&
            this.fx.validateTimestamp(params.asof, "ISO_8601")
        ) {
            flt.push([
                `(upper(log_${form}.sys_period) > %L ) AND (lower(log_${form}.sys_period) <= %L)`,
                [params.asof, params.asof],
            ]);
        }
        if (
            form == "inventory_lot" &&
            params.filter.site_id &&
            params.filter.inventory_id &&
            !params.filter.id
        ) {
            // To to also verify that the log is in stock
            flt.push([
                "((SELECT SUM(quantity) FROM form_ledger_lot ll WHERE ll.inventory_id = %L AND ll.lot_no = form_inventory_lot.lot_no AND ll.site_id = %L ) > 0)",
                [params.filter.inventory_id, params.filter.site_id],
            ]);
        }
        if (
            form == "inventory_serial" &&
            params.filter.site_id &&
            params.filter.inventory_id &&
            !params.filter.id
        ) {
            // To to also verify that the log is in stock
            flt.push([
                "((SELECT SUM(quantity) FROM form_ledger_serial ls WHERE ls.inventory_id = %L AND ls.serial_no = form_inventory_serial.serial_no AND ls.site_id = %L ) > 0)",
                [params.filter.inventory_id, params.filter.site_id],
            ]);
        }
        return flt;
    }

    filter_site(ctx, form) {
        const flt = [];
        if (ctx.user.id === this.auth.ADMIN_USER.id) {
            return flt;
        }
        if (!("site_id" in this.shared.DSL[form].fields)) {
            console.debug("site_id is not in fields for form: " + form);
            return flt;
        }
        // TODO: Shoaib make this gr supported
        if (this.shared.DSL[form].fields.site_id.model.multi) {
            return flt;
        }

        if (!("site_id" in ctx.session)) {
            console.debug("site_id is not in session");
            return [["(form_" + form + ".site_id = %s)", [-99999]]];
        }
        if (!Array.isArray(ctx.session.site_id)) {
            console.debug("session site_id is not an array");
            return [["(form_" + form + ".site_id = %s)", [-99999]]];
        }
        if (ctx.session.site_id.length < 1) {
            console.debug("session site_id is an empty array");
            return [["(form_" + form + ".site_id = %s)", [-99999]]];
        }
        if (ctx.session.site_id.includes(0)) {
            console.debug("session site_id includes 0, allowing all sites");
            return flt;
        }
        if (
            "patient_id" in this.shared.DSL[form].fields &&
            !this.shared.DSL[form].fields.patient_id.model.multi
        ) {
            flt.push([
                `(
                    EXISTS
                    (
                        select 1 from form_patient fsp where fsp.id = form_${form}.patient_id AND fsp.site_id IN
                        (${Array(ctx.session.site_id.length).fill("%s").join(",")})
                    )
                )`,
                ctx.session.site_id,
            ]);
        } else
            flt.push([
                "(form_" +
                    form +
                    `.site_id IN (${Array(ctx.session.site_id.length).fill("%s").join(",")}) OR form_${form}.site_id IS NULL)`,
                ctx.session.site_id,
            ]);
        return flt;
    }

    filter_keywords(user, form, params) {
        const flt = [];
        let column = "";
        let lprefix = ["form_", ""];
        if (params && params.asof && params.asof !== "") {
            lprefix = ["log_", "l"];
        }
        if (
            this.dsl.get_type(params.keywords) === "string" &&
            params.keywords !== "" &&
            this.dsl.get_type(
                this.shared.DSL[form].model[this.shared.dslSearchColumn]
            ) === "array" &&
            this.shared.DSL[form].model[this.shared.dslSearchColumn].length > 0
        ) {
            let qstring = "",
                parr = [],
                harr = [];
            const keywords = params.keywords.trim().split(" ");
            const specialChars = /[.*+?^${}()|<>[\]\\ ]/g;
            parr = keywords.map((keyword) =>
                keyword.replace(specialChars, "\\$&")
            );
            harr = Array(keywords.length).fill("%s:*");
            const parameterizedQuery = `to_tsquery('simple', '${harr.join(" & ")}')`;
            qstring = `${lprefix[0] + form}."${this.shared.dslSearchColumn}" @@ ${parameterizedQuery}`;
            column = `ts_rank(${lprefix[0] + form}."${this.shared.dslSearchColumn}",${parameterizedQuery}) AS ${this.shared.vectorRankColumn},`;
            flt.push([qstring, parr]);
            if (params && "sort" in params)
                if (typeof params.sort === "string")
                    params.sort = [
                        "-" + this.shared.vectorRankColumn,
                        params.sort,
                    ];
                else params.sort.unshift("-" + this.shared.vectorRankColumn);
            else if (params)
                params.sort = ["-" + this.shared.vectorRankColumn, "id"];
            else params = { sort: ["-" + this.shared.vectorRankColumn, "id"] };
        }
        return [flt, column, params];
    }

    get_rls_filter(ctx, form) {
        const user = ctx.user;
        let f_sql = "";
        if (user?.is_admin === "Yes") {
            return "";
        }
        if (!(user?.id in this.shared.perm)) {
            console.log(
                " ============= something is wrong ===================== "
            );
            return "1 = 2";
        }
        const rlsg = this.shared?.rls.global[form];
        let rule = (this.fx.getCachedAccess(ctx)["rls"] || {})[form];
        rule = { ...rlsg, ...rule };
        if (rule) {
            for (const field in rule) {
                let v_sql = ""; // form.field in (1,3,3,4)
                let q_sql = ""; // form.field in (rule1 and rule2 and rule 3)
                let q_sql_sub = "";
                for (let i = 0; i < rule[field]?.rules?.length; i++) {
                    const r = rule[field]?.rules[i];
                    if (!r.rule_key && !r.rule_link) {
                        // console.log(" rule should need to look, RULE CANNOT HAVE NO rule_key and rule_link ")
                        continue;
                    }
                    let ff = r.rule_key;
                    if (r.table_name != form) ff = r.rule_link;

                    if (r.cache_result == "No") {
                        if (r.query_value) {
                            const q_v = r.query_value.replaceAll("$1", user.id);
                            if (q_sql_sub.length > 2) {
                                q_sql_sub = q_sql_sub + ` UNION ` + q_v;
                            } else {
                                q_sql_sub = q_v;
                            }
                        }
                    } else {
                        if (!rule[field].filters.includes("*")) {
                            if (rule[field].filters.length > 1) {
                                v_sql =
                                    `form_${form}.${ff} IN ` +
                                    "(" +
                                    rule[field].filters
                                        .map((k) => "'" + k + "'")
                                        .join() +
                                    ")";
                            } else if (rule[field].filters.length == 1) {
                                v_sql = `form_${form}.${ff} IN ('${rule[field].filters}')`;
                            }
                        }
                    }
                }
                q_sql =
                    q_sql_sub.length > 2
                        ? `form_${form}.${field} IN (${q_sql_sub})`
                        : "";

                let n_f_sql = "";
                if (v_sql.length > 2 && q_sql.length > 2) {
                    n_f_sql = "(" + v_sql + " OR " + q_sql + ")";
                } else if (v_sql.length > 2 && q_sql.length < 2) {
                    n_f_sql = "(" + v_sql + ")";
                } else if (v_sql.length < 2 && q_sql.length > 2) {
                    n_f_sql = "(" + q_sql + ")";
                }

                if (n_f_sql) {
                    f_sql = (f_sql.length > 2 ? f_sql + " AND " : "") + n_f_sql;
                }
            }
        }
        return f_sql;
    }
};
