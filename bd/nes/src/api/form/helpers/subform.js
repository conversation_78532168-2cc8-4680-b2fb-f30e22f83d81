"use strict";
const SQLGetClass = require("./sql-get");
module.exports = class SubformGetClass extends SQLGetClass {
    constructor(nes, exporting = false) {
        super(nes, exporting);
    }

    //
    async get_nested_subforms(frows, ctx, params, depth = 1) {
        if (depth > 9) {
            return frows;
        }
        const formMaps = {};
        for (const [form, fd] of Object.entries(frows)) {
            formMaps[form] = {};
            formMaps[form]["formids"] = fd.map((q) => parseInt(q.id));
        }
        // resolve params inside the fun and remove extra parameter to the fun
        let linked_subforms = await this.get_linked_subforms(
            ctx,
            formMaps,
            params.archived,
            params.asof
        );
        if (Object.keys(linked_subforms).length > 0) {
            linked_subforms = await this.get_nested_subforms(
                linked_subforms,
                ctx,
                params,
                depth + 1
            );
            frows = this.get_merged_subforms(frows, linked_subforms);
        }
        return frows;
    }
    async get_linked_subforms(ctx, formMaps, archived, asof = "") {
        let prefix = "";
        if (asof) prefix = "l";
        const notExist = Object.keys(formMaps).some(
            (form) =>
                !(form in this.shared.DSL) ||
                !("fields" in this.shared.DSL[form])
        );
        if (notExist) return {};

        // prepare list of subform names
        for (const form of Object.keys(formMaps)) {
            formMaps[form]["sf"] = {};
            for (const [_, v] of Object.entries(this.shared.DSL[form].fields)) {
                if (v.model.type != "subform") continue;
                const vsf = Object.keys(v.model.sourcefilter);
                if (vsf.length > 0)
                    vsf.map((q) => (formMaps[form]["sf"][q] = 1));
                else if (
                    typeof v.model.source == "string" &&
                    v.model.source.indexOf("{") < 0
                )
                    formMaps[form]["sf"][v.model.source] = 1;
            }
        }

        for (const [form, fd] of Object.entries(formMaps)) {
            formMaps[form]["sflist"] = Object.keys(formMaps[form].sf).sort();
            if (formMaps[form]["sflist"].length == 0) continue;
            formMaps[form]["ri"] = " IN (" + fd["formids"].join(",") + ")";
        }
        // get all linked subforms via giant sf_ UNION query
        let sfad = "";
        // note that archive and delete columns in the sf gerund will
        // NEVER be null. They default to false
        if (archived && archived.toUpperCase() == "TRUE") {
            sfad = " (delete is false OR delete is NULL) ";
        } else {
            sfad =
                " (delete is false OR delete is NULL) AND (archive is false OR archive is NULL) ";
        }
        let sf = [];
        for (const [form, fd] of Object.entries(formMaps)) {
            for (const s of fd.sflist) {
                const sff = prefix + "sf_form_" + form + "_to_" + s;
                sf.push(
                    "SELECT '" +
                        s +
                        "' AS subform, form_" +
                        s +
                        "_fk AS id FROM " +
                        sff +
                        " WHERE form_" +
                        form +
                        "_fk" +
                        fd.ri +
                        " AND " +
                        sfad
                );
            }
        }
        if (sf.length < 1) return {};
        const sfsql = sf.join("\nUNION\n");
        const sftorows = await this.db.env.rw.query(sfsql);

        // prepare for subform queries
        const sflink = {};
        for (const s of sftorows) {
            if (!(s.subform in sflink)) sflink[s.subform] = {};
            sflink[s.subform][s.id] = 1;
        }

        const sfrows = {};
        for (const k of Object.keys(sflink).sort()) {
            const subids = Object.keys(sflink[k]).map((q) => parseInt(q));
            const where =
                prefix === ""
                    ? "form_" + k + ".id IN (" + subids.join(",") + ")"
                    : "log_" + k + ".id IN (" + subids.join(",") + ")";
            const params = {
                where: where,
                asof: asof,
            };
            if (archived)
                // if filtering for archived/all, show all subforms else subforms must be unarchived
                params.archived =
                    archived.toUpperCase() == "TRUE" ? "all" : archived;
            // we dont want to apply site filter if we are getting subform
            sf = await this.sql_form(ctx, k, {
                ...params,
                skip_site_filter: true,
            });
            if ("error" in sf) continue;
            const { sql, par } = sf;
            let rows = await this.db.env.rw.query(sql, par);
            rows = this.filters.filter_fields(ctx, k, rows, {});
            if (rows.length > 0) sfrows[k] = rows;
        }
        return sfrows;
    }

    get_merged_subforms(frows, srows) {
        const rid = {};
        for (const [form, rows] of Object.entries(frows)) {
            rid[form] = {};
            for (const r in rows) {
                rid[form][rows[r].id] = r;
            }
        }
        for (const [sfk, sfv] of Object.entries(srows)) {
            for (const sr of sfv) {
                if (
                    !("parent_id" in sr) ||
                    !(sr.parent_id in rid[sr.parent_form])
                )
                    continue;
                const ri = rid[sr.parent_form][sr.parent_id];
                const rv = frows[sr.parent_form][ri];
                let lf = null;
                for (const [lk, lv] of Object.entries(rv._meta.subform)) {
                    if (lv == sfk) {
                        lf = lk;
                        break;
                    }
                }
                if (lf) {
                    frows[sr.parent_form][ri][lf].push(sr);
                }
            }
        }
        return frows;
    }
};
