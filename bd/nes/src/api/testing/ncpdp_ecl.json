{"102-A2": {"10": "Version 1.0", "20": "Version 2.0", "30": "Version 3.0", "31": "Version 3.1", "32": "Version 3.2", "33": "Version 3.3", "34": "Version 3.4", "35": "Version 3.5", "40": "Version 4.0", "41": "Version 4.1", "42": "Version 4.2", "50": "Version 5.0", "51": "Version 5.1", "52": "Version 5.2", "53": "Version 5.3", "54": "Version 5.4", "55": "Version 5.5", "56": "Version 5.6", "60": "Version 6.0", "70": "Version 7.0", "71": "Version 7.1", "80": "Version 8.0", "81": "Version 8.1", "82": "Version 8.2", "83": "Version 8.3", "90": "Version 9.0", "A0": "Version A.0", "A1": "Version A.1", "B0": "Version B.0", "C0": "Version C.0", "C1": "Version C.1", "C2": "Version C.2", "C3": "Version C.3", "C4": "Version C.4", "D0": "Version D.0", "D1": "Version D.1", "D2": "Version D.2", "D3": "Version D.3", "D4": "Version D.4", "D5": "Version D.5", "D6": "Version D.6", "D7": "Version D.7", "D8": "Version D.8", "D9": "Version D.9", "E0": "Version E.0", "E1": "Version E.1", "E2": "Version E.2", "E3": "Version E.3", "E4": "Version E.4", "E5": "Version E5", "E6": "Version E6", "E7": "Version E7", "E8": "Version E8", "E9": "Version E9", "EB": "Version EB", "F2": "Version F2"}, "103-A3": {"B1": "Billing", "B2": "Reversal", "B3": "Rebill", "C1": "Controlled Substance Reporting", "C2": "Controlled Substance Reporting Reversal", "C3": "Controlled Substance Reporting Rebill", "D1": "Predetermination of Benefits", "E1": "Eligibility Verification", "N1": "Information Reporting", "N2": "Information Reporting Reversal", "N3": "Information Reporting Rebill", "P1": "P.A. Request & Billing", "P2": "<PERSON><PERSON><PERSON><PERSON>", "P3": "P.A. Inquiry", "P4": "P.A. Request Only", "S1": "Service Billing", "S2": "Service Reversal", "S3": "Service Rebill"}, "109-A9": {"1": "One Occurrence", "Blank": "Not Specified", "2": "Two Occurrences", "3": "Three Occurrences", "4": "Four Occurrences"}, "111-AM": {"01": "Patient", "02": "Pharmacy Provider", "03": "Prescriber", "04": "Insurance", "05": "Coordination of Benefits/Other Payments", "06": "Workers' Compensation", "07": "<PERSON><PERSON><PERSON>", "08": "DUR/PPS", "10": "Compound", "11": "Pricing", "12": "Prior Authorization", "13": "Clinical", "14": "Additional Documentation", "15": "Facility", "16": "Narrative", "17": "Purchaser", "18": "Service Provider", "20": "Response Message", "21": "Response Status", "22": "Response Claim", "23": "Response Pricing", "24": "Response DUR/PPS", "25": "Response Insurance", "26": "Response Prior Authorization", "27": "Response Insurance Additional Information", "28": "Response Other Payers", "29": "Response Patient", "19": "Intermediary", "37": "Last Known 4Rx Segment", "36": "Response Intermediary", "38": "N Transaction Payer Identification", "39": "Response Other Related Benefit Detail", "09": "Coupon"}, "112-AN": {"A": "Approved", "B": "Benefit", "C": "Captured", "D": "Duplicate of <PERSON><PERSON>", "F": "PA Deferred", "P": "Paid", "Q": "Duplicate of Capture", "R": "Rejected", "S": "Duplicate of Approved"}, "117-TR": {"0": "Provider Submitted - Pay to Provider", "1": "Provider Submitted - Pay to Another Party", "2": "Agent Submitted - Pay to Agent", "3": "Agent Submitted - Pay to Another Party"}, "118-TS": {"00": "Not Specified", "01": "National Provider Identifier (NPI)", "11": "Federal Tax ID"}, "123-TX": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "125-TZ": {"01": "Universal Product Code (UPC)", "02": "Health Related Item (HRI)", "03": "National Drug Code (NDC)", "04": "Health Industry Business Communications Council (HIBCC)", "06": "Drug Use Review/ Professional Pharmacy Service (DUR/PPS)", "07": "Current Procedural Terminology (CPT4)", "08": "Current Procedural Terminology (CPT5)", "09": "Healthcare Common Procedure Coding System (HCPCS)", "10": "Pharmacy Practice Activity Classification (PPAC)", "11": "National Pharmaceutical Product Interface Code (NAPPI)", "12": "Global Trade Identification Number (GTIN)", "15": "First DataBank Formulation ID (GCN)", "28": "First DataBank Medication Name ID (FDB Med Name ID)", "29": "First DataBank Routed Medication ID (FDB Routed Med ID)", "30": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)", "31": "First DataBank Medication ID (FDB MedID)", "32": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)", "33": "First DataBank Ingredient List ID (HICL_SEQNO)", "34": "Universal Product Number (UPIN)", "99": "Other", "44": "Gold Standard Specific Product Identifier (SPID)", "45": "Device Identifier (DI)"}, "131-UG": {"+": "Current text continues"}, "132-UH": {"01": "Used for first line of free form text with no pre-defined structure.", "02": "Used for second line of free form text with no pre-defined structure.", "03": "Used for third line of free form text with no pre-defined structure.", "04": "Used for fourth line of free form text with no pre-defined structure.", "05": "Used for fifth line of free form text with no pre-defined structure.", "06": "Used for sixth line of free form text with no pre-defined structure.", "07": "Used for seventh line of free form text with no pre-defined structure.", "08": "Used for eighth line of free form text with no pre-defined structure.", "09": "Used for ninth line of free form text with no pre-defined structure.", "10": "Next Available Fill Date - Next Date of Service on which this claim can be submitted.", "11": "Date The Prior Authorization Ends - Required when prior authorization end date is known.", "12": "Maximum Quantity Allowed Over The Designated Time Period. e.g.: Maximum Qty. of 200 tablets per 90-day period would be returned as \"200,90\".", "13": "Maximum Days Supply Allowed Over The Designated Time Period. e.g.: 90 day supply allowed per year would be returned as \"90,365\".", "14": "Maximum Age", "15": "Maximum Quantity", "16": "Maximum Days Supply", "17": "Maximum Fills", "18": "Maximum Dollar Amount", "19": "Remaining Quantity", "20": "Remaining Days Supply", "21": "Remaining Fills", "22": "Minimum Age", "23": "Minimum Quantity", "24": "Minimum Day Supply", "25": "Minimum Dollar Amount"}, "139-UR": {"1": "Primary - First", "2": "Secondary - Second", "3": "Tertiary - Third", "4": "Quaternary - Fourth", "5": "Quinary - Fifth", "6": "Senary - Sixth", "7": "Septenary - Seventh", "8": "Octonary - Eighth", "9": "Nonary - Ninth"}, "143-UW": {"0": "Not Specified", "1": "Cardholder", "2": "Spouse", "3": "Child", "4": "Other"}, "147-U7": {"1": "Community/Retail Pharmacy Services", "2": "Compounding Pharmacy Services", "3": "Home Infusion Therapy Provider Services", "4": "Institutional Pharmacy Services", "5": "Long Term Care Pharmacy Services", "6": "Mail Order Pharmacy Services", "7": "Managed Care Organization Pharmacy Services", "8": "Specialty Care Pharmacy Services", "9": "Not used.", "10": "Not used.", "99": "Other"}, "202-B2": {"01": "National Provider Identifier (NPI)", "02": "Blue Cross", "03": "Blue Shield", "04": "Medicare", "05": "Medicaid", "06": "UPIN (Unique Physician/Practitioner Identification Number)", "07": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)", "08": "State License", "09": "TRICARE", "10": "Health Industry Number (HIN)", "11": "Federal Tax ID", "12": "Drug Enforcement Administration (DEA) Number", "13": "State Issued", "14": "Plan Specific", "15": "HCIdea", "16": "Combat Methamphetamine Epidemic Act (CMEA) Certificate ID", "99": "Other", "00": "Not Specified"}, "305-C5": {"0": "Not Specified", "1": "Male", "2": "Female"}, "306-C6": {"0": "Not Specified", "1": "Cardholder", "2": "Spouse", "3": "Child", "4": "Other"}, "308-C8": {"0": "Not Specified by patient", "1": "No other coverage", "2": "Other coverage exists - payment collected", "3": "Other Coverage Billed - claim not covered", "4": "Other coverage exists - payment not collected", "8": "Claim is billing for patient financial responsibility only", "5": "Managed care plan denial", "6": "Other coverage denied - not participating provider", "7": "Other coverage exists - not in effect on DOS"}, "309-C9": {"0": "Not Specified", "1": "No Override", "2": "Override", "3": "Full Time Student", "4": "Disabled De<PERSON><PERSON>", "5": "Dependent Parent", "6": "Significant Other"}, "318-CI": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "324-CO": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "331-CX": {"Blank": "Not Specified", "01": "Social Security Number", "1J": "Facility ID Number", "02": "Driver's License Number", "03": "U.S. Military ID", "04": "Non-SSN-based patient identifier assigned by health plan", "05": "SSN-based patient identifier assigned by health plan", "06": "Medicaid ID", "07": "State Issued ID", "08": "Passport ID", "09": "Medicare HIC#", "10": "Employer Assigned ID", "11": "Payer/PBM Assigned ID", "12": "Alien Number (Government Permanent Residence Number)", "13": "Government Student VISA Number", "14": "Indian Tribal ID", "99": "Other", "EA": "Medical Record Identification Number (EHR)"}, "334-1C": {"Blank": "Not Specified", "1": "Non-Smoker", "2": "Smoker"}, "335-2C": {"Blank": "Not Specified", "1": "Not pregnant", "2": "Pregnant"}, "338-5C": {"UK": "Other Payer Order Unknown", "01": "Primary - First", "02": "Secondary - Second", "03": "Tertiary - Third", "04": "Quaternary - Fourth", "05": "Quinary - Fifth", "06": "Senary - Sixth", "07": "Septenary - Seventh", "08": "Octonary - Eighth", "09": "Nonary - Ninth", "98": "Coupon", "99": "Other", "NA": "Not Applicable"}, "339-6C": {"01": "Standard Unique Health Plan Identifier", "1C": "Medicare Number", "1D": "Medicaid Number", "02": "Health Industry Number (HIN)", "03": "Issuer Identification Number (IIN)", "04": "National Association of Insurance Commissioners (NAIC)", "05": "Medicare Carrier Number", "99": "Other", "Blank": "Not Specified", "09": "Coupon", "10": "Payer Name"}, "342-HC": {"01": "Delivery Cost", "02": "Shipping Cost", "03": "Postage Cost", "04": "Administrative Cost", "05": "Incentive", "06": "Cognitive Service", "07": "Drug Benefit", "09": "Compound Preparation Cost Submitted", "10": "Percentage Tax", "11": "Medication Administration", "12": "Regulatory Fee"}, "343-HD": {"Blank": "Not Specified", "P": "Partial Fill", "C": "Completion of Partial Fill"}, "346-HH": {"01": "Quantity Dispensed", "02": "Quantity Intended To Be Dispensed", "03": "Usual and Customary/Prorated", "04": "Waived Due To Partial Fill", "99": "Other", "Blank": "Not Specified", "00": "Not Specified"}, "347-HJ": {"01": "Quantity Dispensed", "02": "Quantity Intended To Be Dispensed", "03": "Usual and Customary/Prorated", "04": "Waived Due To Partial Fill", "99": "Other", "Blank": "Not Specified", "00": "Not Specified"}, "348-HK": {"Blank": "Not Specified", "00": "Not Specified", "01": "Quantity Dispensed", "02": "Quantity Intended To Be Dispensed"}, "349-HM": {"Blank": "Not Specified", "00": "Not Specified", "01": "Quantity Dispensed", "02": "Quantity Intended To Be Dispensed"}, "351-NP": {"Blank": "Not Specified", "01": "Amount Applied to Periodic Deductible as reported by previous payer.", "02": "Amount Attributed to Product Selection/Brand Drug as reported by previous payer.", "03": "Amount Attributed to Percentage Tax as reported by previous payer.", "04": "Amount Exceeding Periodic Benefit Maximum as reported by previous payer.", "05": "Amount of Copay as reported by previous payer.", "06": "Patient Pay Amount (505-F5) as reported by previous payer.", "07": "Amount of Coinsurance as reported by previous payer.", "08": "Amount Attributed to Product Selection/Non-Preferred Formulary Selection as reported by previous payer.", "09": "Amount Attributed to Health Plan Assistance Amount as reported by previous payer.", "10": "Amount Attributed to Provider Network Selection as reported by previous payer.", "11": "Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection as reported by previous payer.", "12": "Amount Attributed to Coverage Gap that was to be collected from the patient due to a coverage gap as reported by previous payer.", "13": "Amount Attributed to Process<PERSON> as reported by previous payer.", "14": "Amount Attributed to Grace Period as reported by previous payer.", "15": "Amount Attributed to Catastrophic Benefit as reported by previous payer.", "16": "Amount Attributed to Unbalanced Patient Pay Response Received from Previous Payer. The dollar amount representing the difference between the Patient Pay Amount (505-F5) and the sum of the reported Patient Pay Component Amounts (C93-KN).", "17": "Amount attributed to Regulatory Fee as reported by previous payer."}, "357-NV": {"1": "Proof of eligibility unknown or unavailable", "2": "Litigation", "3": "Authorization delays", "4": "Delay in certifying provider", "5": "Delay in supplying billing forms", "6": "Delay in delivery of custom-made appliances", "7": "Third party processing delay", "8": "Delay in eligibility determination", "9": "Original claims rejected or denied due to a reason unrelated to the billing limitation rules", "10": "Administration delay in the prior approval process", "11": "Other", "12": "Received late with no exceptions", "13": "Substantial damage by fire, etc. to provider records", "14": "Theft, sabotage/other willful acts by employee"}, "360-2B": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "361-2D": {"Y": "Assigned", "N": "Not Assigned"}, "367-2N": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "369-2Q": {"006": "Medicare = 04.04B Lymphedema Pumps", "007": "Medicare = 04.05C Osteogenesis Stimulator", "008": "Medicare = 06.03B Transcutaneous Electrical NerveStimulator (TENS)", "009": "Medicare = 07.03A Seat Lift Mechanisms", "012": "Medicare = 09.02 Infusion Pump", "015": "Medicare = 484.03 Oxygen", "016": "Medicare = 10.03 Enteral and Parenteral Nutrition", "017": "Medicare = 11.02 Section C Continuation Form"}, "371-2S": {"0": "Not Specified", "1": "Hours", "2": "Days", "3": "Weeks", "4": "Months", "5": "Years", "6": "Lifetime"}, "373-2U": {"0": "Not Specified", "1": "Initial", "2": "Revision", "3": "Recertification"}, "384-4X": {"0": "Not Specified", "1": "Home", "2": "Skilled Nursing Facility", "3": "Nursing Facility", "4": "Assisted Living Facility", "5": "Custodial Care Facility", "6": "Group Home", "7": "Inpatient Psychiatric Facility", "8": "Psychiatric Facility - Partial Hospitalization", "9": "Intermediate Care Facility/Individuals with Intellectual Disabilities", "10": "Residential Substance Abuse Treatment Facility", "11": "Hospice", "12": "Psychiatric Residential Treatment Facility", "13": "Comprehensive Inpatient Rehabilitation Facility", "14": "Homeless Shelter", "15": "Correctional Institution"}, "387-3V": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "391-MT": {"Y": "Patient assigns benefits", "N": "Patient does not assign benefits"}, "403-D3": {"0": "Original prescription fill for the associated Prescription Service Reference Number.", "1-99": "Prescription fill subsequent to the original fill for the associated Prescription Service reference Number."}, "406-D6": {"0": "Not Specified", "1": "Not a Compound", "2": "Compound"}, "408-D8": {"0": "No Product Selection Indicated", "1": "Substitution Not Allowed by Prescriber", "2": "Substitution Allowed - Patient Requested Product Dispensed", "3": "Substitution Allowed - Pharmacist Selected Product Dispensed", "4": "Substitution Allowed - Generic Drug Not in Stock", "5": "Substitution Allowed - Brand Drug Dispensed as a Generic", "6": "Override", "7": "Substitution Not Allowed - Brand Drug Mandated by Law", "8": "Substitution Allowed - Generic Drug Not Available in Marketplace", "9": "Substitution Allowed By Prescriber but Plan Requests Brand - Patient's Plan Requested Brand Product To Be Dispensed"}, "415-DF": {"0": "No refills authorized", "1-99": "Authorized Refill number  - with 99 being as needed, refills unlimited"}, "418-DI": {"0": "Not Specified", "1": "Patient Consultation", "2": "Home Delivery", "3": "Emergency", "4": "24 Hour Service", "5": "Patient Consultation regarding generic product selection", "6": "In-Home Service", "7": "Medical at home with special pharmacy services identical to Long Term Care beneficiaries with the exception of emergency kits."}, "419-DJ": {"0": "Not Known", "1": "Written", "2": "Telephone", "3": "Electronic", "4": "Facsimile", "5": "Pharmacy"}, "420-DK": {"1": "No Override", "2": "Other Override", "3": "Vacation Supply", "4": "Lost/Damaged Prescription", "5": "Therapy Change", "6": "Starter <PERSON><PERSON>", "7": "Medically Necessary", "8": "Process Compound For Approved Ingredients", "9": "Encounters", "10": "Meets Plan Limitations", "11": "Certification on File", "12": "DME Replacement Indicator", "13": "Payer-Recognized Emergency/Disaster Assistance Request", "14": "Long Term Care Leave of Absence", "15": "Long Term Care Replacement Medication", "16": "Long Term Care Emergency box (kit) or automated dispensing machine", "17": "Long Term Care Emergency supply remainder", "18": "Long Term Care Patient Admit/Readmit Indicator", "19": "Split Billing", "20": "340B", "21": "LTC dispensing: 14 days or less not applicable", "22": "LTC dispensing: 7 days", "23": "LTC dispensing: 4 days", "24": "LTC dispensing: 3 days", "25": "LTC dispensing: 2 days", "26": "LTC dispensing: 1 day", "27": "LTC dispensing: 4-3 days", "28": "LTC dispensing: 2-2-3 days", "29": "LTC dispensing: daily and 3-day weekend", "30": "LTC dispensing: Per shift dispensing", "31": "LTC dispensing: Per med pass dispensing", "32": "LTC dispensing: PRN on demand", "33": "LTC dispensing: 7 day or less cycle not otherwise represented", "34": "LTC dispensing: 14 days dispensing", "35": "LTC dispensing: 8-14 day dispensing method not listed above", "36": "LTC dispensing: dispensed outside short cycle", "42": "Prescriber ID Submitted is valid and prescribing requirements have been validated.", "43": "Prescriber's DEA is active with DEA Authorized Prescriptive Right.", "44": "For prescriber ID submitted, associated prescriber DEA recently licensed or re-activated", "45": "Prescriber's DEA is a valid Hospital DEA with Suffix and has prescriptive authority for this drug DEA Schedule", "46": "Prescriber's DEA has prescriptive authority for this drug DEA Schedule", "47": "Shortened Days Supply Dispensed", "48": "Dispensed Subsequent to a Shortened Days Supply Dispensing", "49": "Prescriber does not currently have an active Type 1 NPI", "99": "Other", "50": "Prescriber's active Medicare Fee For Service enrollment status has been validated", "51": "Pharmacy's active Medicare Fee For Service enrollment status has been validated", "52": "Prescriber's state license with prescriptive authority has been validated", "0": "Not Specified", "53": "Prescriber NPI active and valid", "54": "CMS Other Authorized Prescriber (OAP)", "55": "Prescriber Enrollment in State Medicaid Program has been validated.", "56": "Pharmacy Enrollment in State Medicaid Program has been validated."}, "423-DN": {"00": "<PERSON><PERSON><PERSON>", "01": "AWP (Average Wholesale Price)", "02": "Local Wholesaler", "03": "Direct", "04": "EAC (Estimated Acquisition Cost)", "05": "Acquisition", "06": "MAC (Maximum Allowable Cost)", "07": "Usual & Customary", "08": "340B/Disproportionate Share Pricing/Public Health Service", "09": "Other", "10": "ASP (Average Sales Price)", "11": "AMP (Average Manufacturer Price)", "12": "WAC (Wholesale Acquisition Cost)", "13": "Special Patient Pricing", "14": "Cost basis on unreportable quantities.", "15": "Free product or no associated cost.", "Blank": "Not Specified"}, "429-DT": {"0": "Not Specified", "1": "Not Unit Dose", "2": "Manufacturer Unit Dose", "3": "Pharmacy Unit Dose", "4": "Pharmacy Unit Dose Patient Compliance Packaging", "5": "Pharmacy Multi-drug Patient Compliance Packaging", "6": "Remote Device Unit Dose", "7": "Remote Device Multi-drug Compliance", "8": "Manufacturer Unit of Use Package (not unit dose)"}, "436-E1": {"Blank": "Not Specified", "00": "Not Specified", "01": "Universal Product Code (UPC)", "02": "Health Related Item (HRI)", "03": "National Drug Code (NDC)", "04": "Health Industry Business Communications Council (HIBCC)", "06": "Drug Use Review/ Professional Pharmacy Service (DUR/PPS)", "07": "Current Procedural Terminology (CPT4)", "08": "Current Procedural Terminology (CPT5)", "09": "Healthcare Common Procedure Coding System (HCPCS)", "10": "Pharmacy Practice Activity Classification (PPAC)", "11": "National Pharmaceutical Product Interface Code (NAPPI)", "12": "Global Trade Identification Number (GTIN)", "15": "First DataBank Formulation ID (GCN)", "28": "First DataBank Medication Name ID (FDB Med Name ID)", "29": "First DataBank Routed Medication ID (FDB Routed Med ID)", "30": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)", "31": "First DataBank Medication ID (FDB MedID)", "32": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)", "33": "First DataBank Ingredient List ID (HICL_SEQNO)", "34": "Universal Product Number (UPIN)", "36": "Representative National Drug Code (NDC)", "99": "Other", "42": "Gold Standard Marketed Product Identifier (MPid)", "43": "Gold Standard Product Identifier (ProdID)", "44": "Gold Standard Specific Product Identifier (SPID)", "05": "Department of Defense (DOD)", "13": "Drug Identification Number (DIN)", "45": "Device Identifier (DI)"}, "439-E4": {"AD": "Additional Drug Needed", "AN": "Prescription Authentication", "AR": "Adverse Drug Reaction", "AT": "Additive Toxicity", "CD": "Chronic Disease Management", "CH": "Call Help Desk", "CS": "Patient Complaint/Symptom", "DA": "Drug-Allergy", "DC": "Drug-Disease (Inferred)", "DD": "Drug-Drug Interaction", "DF": "Drug-Food Interaction", "DI": "Drug Incompatibility", "DL": "Drug-Lab Conflict", "DM": "Apparent Drug Misuse", "DR": "Dose Range Conflict", "DS": "Tobacco Use", "ED": "Patient Education/Instruction", "ER": "Overuse", "EX": "Excessive Quantity", "HD": "High Dose", "IC": "Iatrogenic Condition", "ID": "Ingredient Duplication", "LD": "Low Dose", "LK": "Lock In Recipient", "LR": "Underuse", "MC": "Drug-Disease (Reported)", "MN": "Insufficient Duration", "MS": "Missing Information/Clarification", "MX": "Excessive Duration", "NA": "Drug Not Available", "NC": "Non-covered Drug Purchase", "ND": "New Disease/Diagnosis", "NF": "Non-Formulary Drug", "NN": "Unnecessary Drug", "NP": "New Patient Processing", "NR": "Lactation/Nursing Interaction", "NS": "Insufficient Quantity", "OH": "Alcohol Conflict", "PA": "Drug-Age", "PC": "Patient Question/Concern", "PG": "Drug-Pregnancy", "PH": "Preventive Health Care", "PN": "Prescriber Consultation", "PP": "Plan Protocol", "PR": "Prior Adverse Reaction", "PS": "Product Selection Opportunity", "RE": "Suspected Environmental Risk", "RF": "Health Provider Referral", "SC": "Suboptimal Compliance", "SD": "Suboptimal Drug/Indication", "SE": "Side Effect", "SF": "Suboptimal Dosage Form", "SR": "Suboptimal Regimen", "SX": "Drug-Gender", "TD": "Therapeutic", "TN": "Laboratory Test Needed", "TP": "Payer/Processor Question", "UD": "Duplicate Drug"}, "440-E5": {"00": "No intervention", "AS": "Patient assessment", "CC": "Coordination of care", "DE": "Dosing evaluation/determination", "DP": "Dosage evaluated", "FE": "Formulary enforcement", "GP": "Generic product selection", "M0": "Prescriber consulted", "MA": "Medication administration", "MB": "Overriding benefit", "MP": "Patient will be monitored", "MR": "Medication review", "PA": "Previous patient tolerance", "PE": "Patient education/instruction", "PH": "Patient medication history", "PM": "Patient monitoring", "P0": "Patient consulted", "PT": "Perform laboratory test", "R0": "Pharmacist consulted other source", "RT": "Recommend laboratory test", "SC": "Self-care consultation", "SW": "Literature search/review", "TC": "Payer/processor consulted", "TH": "Therapeutic product interchange"}, "441-E6": {"00": "Not Specified", "1A": "Dispensed As Is, False Positive", "1B": "Dispensed Prescription As Is", "1C": "Dispensed, With Different Dose", "1D": "Dispensed, With Different Directions", "1E": "Dispensed, With Different Drug", "1F": "Dispensed, With Different Quantity", "1G": "Dispensed, With Prescriber Approval", "1H": "Brand-to-Generic Change", "1J": "Rx-to-OTC Change", "1K": "Dispensed with Different Dosage Form", "2A": "Prescription Not Dispensed", "2B": "Not Dispensed, Directions Clarified", "3A": "Recommendation Accepted", "3B": "Recommendation Not Accepted", "3C": "Discontinued Drug", "3D": "Regimen Changed", "3E": "Therapy Changed", "3F": "Therapy Changed-cost increased acknowledged", "3G": "Drug Therapy Unchanged", "3H": "Follow-Up/Report", "3J": "Patient Referral", "3K": "Instructions Understood", "3M": "Compliance Aid Provided", "3N": "Medication Administered", "4A": "Prescribed With Acknowledgments"}, "450-EF": {"Blank": "Not Specified", "01": "Capsule", "02": "Ointment", "03": "Cream", "04": "Suppository", "05": "<PERSON><PERSON><PERSON>", "06": "Emulsion", "07": "Liquid", "10": "Tablet", "11": "Solution", "12": "Suspension", "13": "Lotion", "14": "Shampoo", "15": "<PERSON><PERSON><PERSON>", "16": "<PERSON><PERSON><PERSON>", "17": "Lozenge", "18": "<PERSON>ema"}, "451-EG": {"1": "Each", "2": "Grams", "3": "Milliliters"}, "453-EJ": {"Blank": "Not Specified", "00": "Not Specified", "01": "Universal Product Code (UPC)", "02": "Health Related Item (HRI)", "03": "National Drug Code (NDC)", "04": "Health Industry Business Communications Council (HIBCC)", "06": "Drug Use Review/ Professional Pharmacy Service (DUR/PPS)", "07": "Current Procedural Terminology (CPT4)", "08": "Current Procedural Terminology (CPT5)", "09": "Healthcare Common Procedure Coding System (HCPCS)", "10": "Pharmacy Practice Activity Classification (PPAC)", "11": "National Pharmaceutical Product Interface Code (NAPPI)", "12": "Global Trade Identification Number (GTIN)", "15": "First DataBank Formulation ID (GCN)", "28": "First DataBank Medication Name ID (FDB Med Name ID)", "29": "First DataBank Routed Medication ID (FDB Routed Med ID)", "30": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)", "31": "First DataBank Medication ID (FDB MedID)", "32": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)", "33": "First DataBank Ingredient List ID (HICL_SEQNO)", "38": "RxNorm Semantic Clinical Drug (SCD)", "39": "RxNorm Semantic Branded Drug (SBD)", "40": "RxNorm Generic Package (GPCK)", "41": "RxNorm Branded Package (BPCK)", "99": "Other", "42": "Gold Standard Marketed Product Identifier (MPid)", "43": "Gold Standard Product Identifier (ProdID)", "44": "Gold Standard Specific Product Identifier (SPID)", "05": "Department of Defense (DOD)", "13": "Drug Identification Number (DIN)", "45": "Device Identifier (DI)"}, "455-EM": {"1": "<PERSON><PERSON>", "2": "Service Billing", "Blank": "Not Specified", "3": "Non Prescription Product"}, "461-EU": {"0": "Not Specified", "1": "Prior Authorization", "2": "Medical Certification", "3": "EPSDT (Early Periodic Screening Diagnosis Treatment)", "4": "Exemption from Copay and/or Coinsurance", "5": "Exemption from RX", "6": "Family Planning Indicator", "7": "TANF (Temporary Assistance for Needy Families)", "8": "Payer Defined Exemption", "9": "Emergency Preparedness"}, "462-EV": {"91100000000": "Emergency Preparedness (EP) Refill Extension Override", "91100000001": "Emergency Preparedness (EP) Refill Too Soon Edit Override", "91100000002": "Emergency Preparedness (EP) Prior Authorization Requirement Override", "91100000003": "Emergency Preparedness (EP) Accumulated Quantity Override", "91100000004": "Emergency Preparedness (EP) Step Therapy Override", "91100000005": "Emergency Preparedness (EP) override all of the above"}, "465-EY": {"01": "Drug Enforcement Administration (DEA)", "02": "State License", "03": "Social Security Number (SSN)", "04": "Name", "05": "National Provider Identifier (NPI)", "06": "Health Industry Number (HIN)", "07": "State Issued", "99": "Other", "Blank": "Not Specified"}, "466-EZ": {"01": "National Provider Identifier (NPI)", "02": "Blue Cross", "03": "Blue Shield", "04": "Medicare", "05": "Medicaid", "06": "UPIN (Unique Physician/Practitioner Identification Number)", "08": "State License", "09": "TRICARE", "10": "Health Industry Number (HIN)", "11": "Federal Tax ID", "12": "Drug Enforcement Administration (DEA) Number", "13": "State Issued", "14": "Plan Specific", "15": "HCIdea", "17": "Foreign Prescriber Identifier", "99": "Other", "00": "Not Specified", "07": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)", "18": "No Prescriber Required"}, "468-2E": {"01": "National Provider Identifier (NPI)", "02": "Blue Cross", "03": "Blue Shield", "04": "Medicare", "05": "Medicaid", "06": "UPIN (Unique Physician/Practitioner Identification Number)", "08": "State License", "09": "TRICARE", "10": "Health Industry Number (HIN)", "11": "Federal Tax ID", "12": "Drug Enforcement Administration (DEA) Number", "13": "State Issued", "14": "Plan Specific", "15": "HCIdea", "99": "Other", "00": "Not Specified", "07": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)"}, "474-8E": {"0": "Not Specified", "11": "Level 1 (Lowest) = Straightforward", "12": "Level 2 (Low Complexity)", "13": "Level 3 (Moderate Complexity)", "14": "Level 4 (High Complexity)", "15": "Level 5 (Highest) = Comprehensive", "16": "Low Level Complexity", "17": "Mid-Level Complexity Non-Hazardous", "18": "Mid-Level Complexity Hazardous", "19": "High Level Non-Hazardous", "20": "High Level Hazardous", "21": "High Level Non-Hazardous Sterile", "22": "High level Hazardous Sterile"}, "475-J9": {"Blank": "Not Specified", "01": "Universal Product Code (UPC)", "02": "Health Related Item (HRI)", "03": "National Drug Code (NDC)", "04": "Health Industry Business Communications Council (HIBCC)", "07": "Current Procedural Terminology (CPT4)", "08": "Current Procedural Terminology (CPT5)", "09": "Healthcare Common Procedure Coding System (HCPCS)", "11": "National Pharmaceutical Product Interface Code (NAPPI)", "12": "Global Trade Identification Number (GTIN)", "14": "Medi Span's Generic Product Identifier (GPI)", "15": "First DataBank Formulation ID (GCN)", "16": "Truven/Micromedex Generic Formulation Code (GFC)", "17": "Me<PERSON> Span's Drug Descriptor ID (DDID)", "18": "First DataBank SmartKey", "19": "Truven/Micromedex Generic Master (GM)", "20": "International Classification of Diseases (ICD9)", "21": "International Classification of Diseases-10-Clinical Modifications (ICD-10-CM)", "23": "National Criteria Care Institute (NCCI)", "24": "The Systematized Nomenclature of Medicine Clinical Terms® (SNOMED)", "25": "Current Dental Terminology (CDT)", "26": "American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM IV)", "27": "International Classification of Diseases-10-Procedure Coding System (ICD-10-PCS)", "28": "First DataBank Medication Name ID (FDB Med Name ID)", "29": "First DataBank Routed Medication ID (FDB Routed Med ID)", "30": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)", "31": "First DataBank Medication ID (FDB MedID)", "32": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)", "33": "First DataBank Ingredient List ID (HICL_SEQNO)", "35": "Logical Observation Identifier Names and Codes (LOINC)", "37": "American Hospital Formulary Service (AHFS)", "38": "RxNorm Semantic Clinical Drug (SCD)", "39": "RxNorm Semantic Branded Drug (SBD)", "40": "RxNorm Generic Package (GPCK)", "41": "RxNorm Branded Package (BPCK)", "99": "Other", "05": "Department of Defense (DOD)", "13": "Drug Identification Number (DIN)", "46": "American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM 5)", "45": "Device Identifier (DI)"}, "479-H8": {"01": "Delivery Cost", "02": "Shipping Cost", "03": "Postage Cost", "04": "Administrative Cost", "09": "Compound Preparation Cost Submitted", "11": "Medication Administration", "99": "Other"}, "484-JE": {"Blank": "Not Specified", "02": "Ingredient Cost", "03": "Ingredient Cost + Dispensing Fee", "04": "Professional Service Fee - The dollar amount/value for the professional service."}, "488-RE": {"Blank": "Not Specified", "01": "Universal Product Code (UPC)", "02": "Health Related Item (HRI)", "03": "National Drug Code (NDC)", "04": "Health Industry Business Communications Council (HIBCC)", "11": "National Pharmaceutical Product Interface Code (NAPPI)", "12": "Global Trade Identification Number (GTIN)", "15": "First DataBank Formulation ID (GCN)", "28": "First DataBank Medication Name ID (FDB Med Name ID)", "29": "First DataBank Routed Medication ID (FDB Routed Med ID)", "30": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)", "31": "First DataBank Medication ID (FDB MedID)", "32": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)", "33": "First DataBank Ingredient List ID (HICL_SEQNO)", "99": "Other", "05": "Department of Defense (DOD)", "13": "Drug Identification Number (DIN)", "45": "Device Identifier (DI)"}, "490-UE": {"00": "<PERSON><PERSON><PERSON>", "01": "AWP (Average Wholesale Price)", "02": "Local Wholesaler", "03": "Direct", "04": "EAC (Estimated Acquisition Cost)", "05": "Acquisition", "06": "MAC (Maximum Allowable Cost)", "07": "Usual & Customary", "08": "340B/Disproportionate Share Pricing/Public Health Service", "09": "Other", "10": "ASP (Average Sales Price)", "11": "AMP (Average Manufacturer Price)", "12": "WAC (Wholesale Acquisition Cost)", "13": "Special Patient Pricing", "14": "Cost basis on unreportable quantities.", "15": "Free product or no associated cost.", "Blank": "Not Specified"}, "492-WE": {"00": "Not Specified", "01": "International Classification of Diseases (ICD-9)", "02": "International Classification of Diseases-10-Clinical Modifications(ICD-10-CM)", "03": "National Criteria Care Institute (NCCI)", "04": "The Systematized Nomenclature of Medicine Clinical Terms (SNOMED)", "05": "Current Dental Terminology (CDT)", "07": "American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-IV)", "Blank": "Not Specified", "08": "American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-5)"}, "496-H2": {"Blank": "Not Specified", "01": "Blood Pressure (BP)", "02": "Blood Glucose", "03": "Temperature", "04": "Serum Creatinine (SCr)", "05": "Glycosylated Hemoglobin (HbA1c)", "06": "Sodium (Na+)", "07": "Potassium (K+)", "08": "Calcium (Ca++)", "09": "Serum Glutamic-Oxaloacetic Transaminase (SGOT)", "10": "Serum Glutamic-Pyruvic Transaminase (SGPT)", "11": "Alkaline Phosphatase", "12": "<PERSON><PERSON><PERSON><PERSON>", "13": "Digoxin", "14": "Weight", "15": "Body Surface Area (BSA)", "16": "Height", "17": "Creatinine Clearance (CrCl)", "18": "Cholesterol", "19": "Low Density Lipoprotein (LDL)", "20": "High Density Lipoprotein (HDL)", "21": "Triglycerides (TG)", "22": "Bone Mineral Density (BMD T-Score)", "23": "Prothrombin Time (PT)", "24": "Hemoglobin (Hb; Hgb)", "25": "Hematocrit (Hct)", "26": "White Blood Cell Count (WBC)", "27": "Red Blood Cell Count (RBC)", "28": "Heart Rate", "29": "Absolute Neutrophil Count (ANC)", "30": "Activated Partial Thromboplastin Time (APTT)", "31": "CD4 Count (also T4 Count, T-helper cells)", "32": "Partial Thromboplastin Time (PTT)", "33": "T-Cell Count", "34": "INR-International Normalized Ratio", "99": "Other"}, "497-H3": {"Blank": "Not Specified", "01": "Inches (In)", "02": "Centimeters (cm)", "03": "Pounds (lb)", "04": "Kilograms (kg)", "05": "<PERSON><PERSON><PERSON> (C)", "06": "Fahrenheit (F)", "07": "Meters squared (m2)", "08": "Milligrams per deciliter (mg/dl)", "09": "Units per milliliter (U/ml)", "10": "Millimeters of mercury (mmHg)", "11": "Centimeters squared (cm2)", "12": "Milliliters per minute (ml/min)", "13": "Percent (%)", "14": "Milliequivalents per milliliter (mEq/ml)", "15": "International units per liter (IU/L)", "16": "Micrograms per milliliter (mcg/ml)", "17": "Nanograms per milliliter (ng/ml)", "18": "Milligrams per milliliter (mg/ml)", "19": "<PERSON><PERSON>", "20": "SI Units", "21": "Millimoles/liter (mmol/l)", "22": "Seconds", "23": "Grams per deciliter (g/dl)", "24": "Cells per cubic millimeter (cells/cu mm)", "25": "1,000,000 cells per cubic millimeter (million cells/cu mm)", "26": "Standard deviation", "27": "Beats per minute"}, "498-PA": {"1": "Initial", "2": "Reauthorization", "3": "Deferred"}, "498-PD": {"ME": "Medical Exception", "PR": "Plan Requirement", "PL": "Increase Plan Limitation"}, "498-PJ": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "501-F1": {"A": "Accepted", "R": "Rejected"}, "511-FB": {"01": "M/I IIN Number", "02": "M/I Version/Release Number", "03": "M/I Transaction Code", "04": "M/I Processor Control Number", "05": "M/I Service Provider Number", "06": "M/I Group ID", "07": "M/I Cardholder ID", "08": "M/I Person Code", "09": "M/I Date Of Birth", "1C": "M/I Smoker/Non-Smoker Code", "1E": "M/I Prescriber Location Code", "1K": "M/I Patient Country Code", "1R": "Version/Release Value Not Supported", "1S": "Transaction Code/Type Value Not Supported", "1T": "PCN Must Contain Processor/Payer Assigned Value", "1U": "Transaction Count Does Not Match Number of Transactions", "1V": "Multiple Transactions Not Supported", "1X": "Vendor Not Certified For Processor/Payer", "1Y": "Claim Segment Required For Adjudication", "1Z": "Clinical Segment Required For Adjudication", "10": "M/I Patient Gender Code", "11": "M/I Patient Relationship Code", "12": "M/I Place of Service", "13": "M/I Other Coverage Code", "14": "M/I Eligibility Clarification Code", "15": "M/I Date of Service", "16": "M/I Prescription/Service Reference Number", "17": "M/I Fill Number", "19": "M/I Days Supply", "2A": "M/I Medigap ID", "2B": "M/I Medicaid Indicator", "2C": "M/I Pregnancy Indicator", "2D": "M/I Provider Accept Assignment Indicator", "2E": "M/I Primary Care Provider ID Qualifier", "2G": "M/I Compound Ingredient Modifier Code Count", "2H": "M/I Compound Ingredient Modifier Code", "2J": "M/I Prescriber First Name", "2K": "M/I Prescriber Street Address", "2M": "M/I Prescriber City Address", "2N": "M/I Prescriber State/Province Address", "2P": "M/I Prescriber Zip/Postal Zone", "2Q": "M/I Additional Documentation Type ID", "2R": "M/I Length Of Need", "2S": "M/I Length Of Need Qualifier", "2T": "M/I Prescriber/Supplier Date Signed", "2U": "M/I Request Status", "2V": "M/I Request Period Begin Date", "2W": "M/I Request Period Recert/Revised Date", "2X": "M/I Supporting Documentation", "2Z": "M/I Question Number/Letter Count", "20": "M/I Compound Code", "21": "M/I Product/Service ID", "22": "M/I Dispense As Written (DAW)/Product Selection Code", "23": "M/I Ingredient Cost Submitted", "25": "M/I Prescriber ID", "26": "M/I Unit Of Measure", "28": "M/I Date Prescription Written", "29": "M/I Number Of Refills Authorized", "201": "Patient Segment Is Not Used For This Transaction Code", "202": "Insurance Segment Is Not Used For This Transaction Code", "203": "Claim Segment Is Not Used For This Transaction Code", "204": "Pharmacy Provider Segment Is Not Used For This Transaction Code", "205": "Prescriber Segment Is Not Used For This Transaction Code", "206": "Coordination Of Benefits/Other Payments Segment Is Not Used For This Transaction Code", "207": "Workers' Compensation Segment Is Not Used For This Transaction Code", "208": "DUR/PPS Segment Is Not Used For This Transaction Code", "209": "Pricing Segment Is Not Used For This Transaction Code", "210": "Coupon Segment Is Not Used For This Transaction Code", "211": "Compound Segment Is Not Used For This Transaction Code", "212": "Prior Authorization Segment Is Not Used For This Transaction Code", "213": "Clinical Segment Is Not Used For This Transaction Code", "214": "Additional Documentation Segment Is Not Used For This Transaction Code", "215": "Facility Segment Is Not Used For This Transaction Code", "216": "Narrative Segment Is Not Used For This Transaction Code", "217": "Purchaser Segment Is Not Used For This Transaction Code", "218": "Service Provider Segment Is Not Used For This Transaction Code", "219": "Patient ID Qualifier Is Not Used For This Transaction Code", "220": "Patient ID Is Not Used For This Transaction Code", "221": "Date Of Birth Is Not Used For This Transaction Code", "222": "Patient Gender Code Is Not Used For This Transaction Code", "223": "Patient First Name Is Not Used For This Transaction Code", "224": "Patient Last Name Is Not Used For This Transaction Code", "225": "Patient Street Address Is Not Used For This Transaction Code", "226": "Patient City Address Is Not Used For This Transaction Code", "227": "Patient State/Province Address Is Not Used For This Transaction Code", "228": "Patient ZIP/Postal Zone Is Not Used For This Transaction Code", "229": "Patient Phone Number Is Not Used For This Transaction Code", "230": "Place Of Service Is Not Used For This Transaction Code", "231": "Employer ID Is Not Used For This Transaction Code", "232": "Smoker/Non-Smoker Code Is Not Used For This Transaction Code", "233": "Pregnancy Indicator Is Not Used For This Transaction Code", "234": "Patient E-Mail Address Is Not Used For This Transaction Code", "235": "Patient Residence Is Not Used For This Transaction Code", "236": "Patient ID Associated State/Province Address Is Not Used For This Transaction Code", "237": "Cardholder First Name Is Not Used For This Transaction Code", "238": "Cardholder Last Name Is Not Used For This Transaction Code", "239": "Home Plan Is Not Used For This Transaction Code", "240": "Plan ID Is Not Used For This Transaction Code", "241": "Eligibility Clarification Code Is Not Used For This Transaction Code", "242": "Group ID Is Not Used For This Transaction Code", "243": "Person Code Is Not Used For This Transaction Code", "244": "Patient Relationship Code Is Not Used For This Transaction Code", "245": "Other Payer BIN Number Is Not Used For This Transaction Code", "246": "Other Payer Processor Control Number Is Not Used For This Transaction Code", "247": "Other Payer Cardholder ID Is Not Used For This Transaction Code", "248": "Other Payer Group ID Is Not Used For This Transaction Code", "249": "Medigap ID Is Not Used For This Transaction Code", "250": "Medicaid Indicator Is Not Used For This Transaction Code", "251": "Provider Accept Assignment Indicator Is Not Used For This Transaction Code", "252": "CMS Part D Defined Qualified Facility Is Not Used For This Transaction Code", "253": "Medicaid ID Number Is Not Used For This Transaction Code", "254": "Medicaid Agency Number Is Not Used For This Transaction Code", "255": "Associated Prescription/Service Reference Number Is Not Used For This Transaction Code", "256": "Associated Prescription/Service Date Is Not Used For This Transaction Code", "257": "Procedure Modifier Code Count Is Not Used For This Transaction Code", "258": "Procedure Modifier Code Is Not Used For This Transaction Code", "259": "Quantity Dispensed Is Not Used For This Transaction Code", "260": "Fill Number Is Not Used For This Transaction Code", "261": "Days Supply Is Not Used For This Transaction Code", "262": "Compound Code Is Not Used For This Transaction Code", "263": "Dispense As Written(DAW)/Product Selection Code Is Not Used For This Transaction Code", "264": "Date Prescription Written Is Not Used For This Transaction Code", "265": "Number Of Refills Authorized Is Not Used For This Transaction Code", "266": "Prescription Origin Code Is Not Used For This Transaction Code", "267": "Submission Clarification Code Count Is Not Used For This Transaction Code", "268": "Submission Clarification Code Is Not Used For This Transaction Code", "269": "Quantity Prescribed Is Not Used For This Transaction Code", "270": "Other Coverage Code Is Not Used For This Transaction Code", "271": "Special Packaging Indicator Is Not Used For This Transaction Code", "272": "Originally Prescribed Product/Service ID Qualifier Is Not Used For This Transaction Code", "273": "Originally Prescribed Product/Service Code Is Not Used For This Transaction Code", "274": "Originally Prescribed Quantity Is Not Used For This Transaction Code", "275": "Alternate ID Is Not Used For This Transaction Code", "276": "Scheduled Prescription ID Number Is Not Used For This Transaction Code", "277": "Unit Of Measure Is Not Used For This Transaction Code", "278": "Level Of Service Is Not Used For This Transaction Code", "279": "Prior Authorization Type Code Is Not Used For This Transaction Code", "280": "Prior Authorization ID Submitted Is Not Used For This Transaction Code", "283": "Dispensing Status Is Not Used For This Transaction Code", "284": "Quantity Intended To Be Dispensed Is Not Used For This Transaction Code", "285": "Days Supply Intended To Be Dispensed Is Not Used For This Transaction Code", "286": "Delay Reason Code Is Not Used For This Transaction Code", "287": "Transaction Reference Number Is Not Used For This Transaction Code", "288": "Patient Assignment Indicator (Direct Member Reimbursement Indicator) Is Not Used For This Transaction Code", "289": "Route of Administration Is Not Used For This Transaction Code", "290": "Compound Type Is Not Used For This Transaction Code", "291": "Medicaid Subrogation Internal Control Number/Transaction Control Number (ICN/TCN) Is Not Used For This Transaction Code", "292": "Pharmacy Service Type Is Not Used For This Transaction Code", "293": "Associated Prescription/Service Provider ID Qualifier Is Not Used For This Transaction Code", "294": "Associated Prescription/Service Provider ID Is Not Used For This Transaction Code", "295": "Associated Prescription/Service Reference Number Qualifier Is Not Used For This Transaction Code", "296": "Associated Prescription/Service Reference Fill Number Is Not Used For This Transaction Code", "297": "Time of Service Is Not Used For This Transaction Code", "298": "Sales Transaction ID Is Not Used For This Transaction Code", "299": "Reported Adjudicated Program Type Is Not Used For This Transaction Code", "3A": "M/I Request Type", "3B": "M/I Request Period Date-Begin", "3C": "M/I Request Period Date-End", "3D": "M/I Basis Of Request", "3E": "M/I Authorized Representative First Name", "3F": "M/I Authorized Representative Last Name", "3G": "M/I Authorized Representative Street Address", "3H": "M/I Authorized Representative City Address", "3J": "M/I Authorized Representative State/Province Address", "3K": "M/I Authorized Representative Zip/Postal Zone", "3M": "M/I Prescriber Phone Number", "3N": "M/I Prior Authorization ID Assigned", "3P": "M/I Authorization Number", "3Q": "M/I Facility Name", "3R": "Prior Authorization Not Required", "3S": "M/I Prior Authorization Supporting Documentation", "3T": "Active Prior Authorization Exists Resubmit At Expiration Of Prior Authorization", "3U": "M/I Facility Street Address", "3V": "M/I Facility State/Province Address", "3W": "Prior Authorization In Process", "3X": "Authorization Number Not Found", "3Y": "Prior Authorization Denied", "32": "M/I Level Of Service", "33": "M/I Prescription Origin Code", "34": "M/I Submission Clarification Code", "35": "M/I Primary Care Provider ID", "38": "M/I Basis Of Cost Determination", "39": "M/I Diagnosis Code", "300": "Provider ID Qualifier Is Not Used For This Transaction Code", "301": "Provider ID Is Not Used For This Transaction Code", "302": "Prescriber ID Qualifier Is Not Used For This Transaction Code", "303": "Prescriber ID Is Not Used For This Transaction Code", "304": "Prescriber ID Associated State/Province Address Is Not Used For This Transaction Code", "305": "Prescriber Last Name Is Not Used For This Transaction Code", "306": "Prescriber Phone Number Is Not Used For This Transaction Code", "307": "Primary Care Provider ID Qualifier Is Not Used For This Transaction Code", "308": "Primary Care Provider ID Is Not Used For This Transaction Code", "309": "Primary Care Provider Last Name Is Not Used For This Transaction Code", "310": "Prescriber First Name Is Not Used For This Transaction Code", "311": "Prescriber Street Address Is Not Used For This Transaction Code", "312": "Prescriber City Address Is Not Used For This Transaction Code", "313": "Prescriber State/Province Address Is Not Used For This Transaction Code", "314": "Prescriber ZIP/Postal Zone Is Not Used For This Transaction Code", "315": "Prescriber Alternate ID Qualifier Is Not Used For This Transaction Code", "316": "Prescriber Alternate ID Is Not Used For This Transaction Code", "317": "Prescriber Alternate ID Associated State/Province Address Is Not Used For This Transaction Code", "318": "Other Payer ID Qualifier Is Not Used For This Transaction Code", "319": "Other Payer ID Is Not Used For This Transaction Code", "320": "Other Payer Date Is Not Used For This Transaction Code", "321": "Internal Control Number Is Not Used For This Transaction Code", "322": "Other Payer Amount Paid Count Is Not Used For This Transaction Code", "323": "Other Payer Amount Paid Qualifier Is Not Used For This Transaction Code", "324": "Other Payer Amount Paid Is Not Used For This Transaction Code", "325": "Other Payer Reject Count Is Not Used For This Transaction Code", "326": "Other Payer Reject Code Is Not Used For This Transaction Code", "327": "Other Payer-Patient Responsibility Amount Count Is Not Used For This Transaction Code", "328": "Other Payer-Patient Responsibility Amount Qualifier Is Not Used For This Transaction Code", "329": "Other Payer-Patient Responsibility Amount Is Not Used For This Transaction Code", "330": "Benefit Stage Count Is Not Used For This Transaction Code", "331": "Benefit Stage Qualifier Is Not Used For This Transaction Code", "332": "Benefit Stage Amount Is Not Used For This Transaction Code", "333": "Employer Name Is Not Used For This Transaction Code", "334": "Employer Street Address Is Not Used For This Transaction Code", "335": "Employer City Address Is Not Used For This Transaction Code", "336": "Employer State/Province Address Is Not Used For This Transaction Code", "337": "Employer ZIP/Postal Code Is Not Used For This Transaction Code", "338": "Employer Phone Number Is Not Used For This Transaction Code", "339": "Employer Contact Name Is Not Used For This Transaction Code", "340": "Carrier ID Is Not Used For This Transaction Code", "341": "Claim/Reference ID Is Not Used For This Transaction Code", "342": "Billing Entity Type Indicator Is Not Used For This Transaction Code", "343": "Pay To Qualifier Is Not Used For This Transaction Code", "344": "Pay To ID Is Not Used For This Transaction Code", "345": "Pay To Name Is Not Used For This Transaction Code", "346": "Pay To Street Address Is Not Used For This Transaction Code", "347": "Pay To City Address Is Not Used For This Transaction Code", "348": "Pay To State/Province Address Is Not Used For This Transaction Code", "349": "Pay To ZIP/Postal Zone Is Not Used For This Transaction Code", "350": "Generic Equivalent Product ID Qualifier Is Not Used For This Transaction Code", "351": "Generic Equivalent Product ID Is Not Used For This Transaction Code", "352": "DUR/PPS Code Counter Is Not Used For This Transaction Code", "353": "Reason For Service Code Is Not Used For This Transaction Code", "354": "Professional Service Code Is Not Used For This Transaction Code", "355": "Result Of Service Code Is Not Used For This Transaction Code", "356": "DUR/PPS Level Of Effort Is Not Used For This Transaction Code", "357": "DUR Co-Agent ID Qualifier Is Not Used For This Transaction Code", "358": "DUR Co-Agent ID Is Not Used For This Transaction Code", "359": "Ingredient Cost Submitted Is Not Used For This Transaction Code", "360": "Dispensing Fee Submitted Is Not Used For This Transaction Code", "361": "Professional Service Fee Submitted Is Not Used For This Transaction Code", "362": "Patient Paid Amount Submitted Is Not Used For This Transaction Code", "363": "Incentive Amount Submitted Is Not Used For This Transaction Code", "364": "Other Amount Claimed Submitted Count Is Not Used For This Transaction Code", "365": "Other Amount Claimed Submitted Qualifier Is Not Used For This Transaction Code", "366": "Other Amount Claimed Submitted Is Not Used For This Transaction Code", "367": "Flat Sales Tax Amount Submitted Is Not Used For This Transaction Code", "368": "Percentage Sales Tax Amount Submitted Is Not Used For This Transaction Code", "369": "Percentage Sales Tax Rate Submitted Is Not Used For This Transaction Code", "370": "Percentage Sales Tax Basis Submitted Is Not Used For This Transaction Code", "371": "Usual And Customary Charge Is Not Used For This Transaction Code", "372": "Gross Amount Due Is Not Used For This Transaction Code", "373": "Basis Of Cost Determination Is Not Used For This Transaction Code", "374": "Medicaid Paid Amount Is Not Used For This Transaction Code", "375": "Coupon Value Amount Is Not Used For This Transaction Code", "376": "Compound Ingredient Drug Cost Is Not Used For This Transaction Code", "377": "Compound Ingredient Basis Of Cost Determination Is Not Used For This Transaction Code", "378": "Compound Ingredient Modifier Code Count Is Not Used For This Transaction Code", "379": "Compound Ingredient Modifier Code Is Not Used For This Transaction Code", "380": "Authorized Representative First Name Is Not Used For This Transaction Code", "381": "Authorized Rep. Last Name Is Not Used For This Transaction Code", "382": "Authorized Rep. Street Address Is Not Used For This Transaction Code", "383": "Authorized Rep. City Is Not Used For This Transaction Code", "384": "Authorized Rep. State/Province Is Not Used For This Transaction Code", "385": "Authorized Rep. ZIP/Postal Code Is Not Used For This Transaction Code", "386": "Prior Authorization ID Assigned Is Not Used For This Transaction Code", "387": "Authorization Number Is Not Used For This Transaction Code", "388": "Prior Authorization Supporting Documentation Is Not Used For This Transaction Code", "389": "Diagnosis Code Count Is Not Used For This Transaction Code", "390": "Diagnosis Code Qualifier Is Not Used For This Transaction Code", "391": "Diagnosis Code Is Not Used For This Transaction Code", "392": "Clinical Information Counter Is Not Used For This Transaction Code", "393": "Measurement Date Is Not Used For This Transaction Code", "394": "Measurement Time Is Not Used For This Transaction Code", "395": "Measurement Dimension Is Not Used For This Transaction Code", "396": "Measurement Unit Is Not Used For This Transaction Code", "397": "Measurement Value Is Not Used For This Transaction Code", "398": "Request Period Begin Date Is Not Used For This Transaction Code", "399": "Request Period Recert/Revised Date Is Not Used For This Transaction Code", "4B": "M/I Question Number/Letter", "4C": "M/I Coordination Of Benefits/Other Payments Count", "4D": "M/I Question Percent Response", "4E": "M/I Primary Care Provider Last Name", "4G": "M/I Question Date Response", "4H": "M/I Question Dollar Amount Response", "4J": "M/I Question Numeric Response", "4K": "M/I Question Alphanumeric Response", "4M": "Compound Ingredient Modifier Code Count Does Not Match Number Of Repetitions", "4N": "Question Number/Letter Count Does Not Match Number Of Repetitions", "4P": "Question Number/Letter Not Valid For Identified Document", "4Q": "Question Response Not Appropriate For Question Number/Letter", "4R": "Required Question Number/Letter Response For Indicated Document Missing", "4S": "Compound Product ID Requires A Modifier Code", "4T": "M/I Additional Documentation Segment", "4W": "Must Dispense Through Specialty Pharmacy", "4X": "M/I Patient Residence", "4Y": "Patient Residence Value Not Supported", "4Z": "Place of Service Not Supported By Plan", "40": "Pharmacy Not Contracted With Plan/Processor On Date Of Service", "41": "Submit Bill To Other Processor Or Primary Payer", "42": "Plan's Prescriber Data Base Indicates The Prescriber ID Submitted Is Inactive Or Expired", "43": "Plan's Prescriber Data Base Indicates The Submitted Prescriber DEA Number Is Inactive Or Expired", "44": "Plan's Prescriber Data Base Indicates The Submitted Prescriber DEA Number Is Not Found", "46": "Plan's Prescriber Data Base Indicates The Submitted Prescriber DEA Number Does Not Allow This Drug DEA Class", "400": "Request Status Is Not Used For This Transaction Code", "401": "Length Of Need Qualifier Is Not Used For This Transaction Code", "402": "Length Of Need Is Not Used For This Transaction Code", "403": "Prescriber/Supplier Date Signed Is Not Used For This Transaction Code", "404": "Supporting Documentation Is Not Used For This Transaction Code", "405": "Question Number/Letter Count Is Not Used For This Transaction Code", "406": "Question Number/Letter Is Not Used For This Transaction Code", "407": "Question Percent Response Is Not Used For This Transaction Code", "408": "Question Date Response Is Not Used For This Transaction Code", "409": "Question Dollar Amount Response Is Not Used For This Transaction Code", "410": "Question Numeric Response Is Not Used For This Transaction Code", "411": "Question Alphanumeric Response Is Not Used For This Transaction Code", "412": "Facility ID Is Not Used For This Transaction Code", "413": "Facility Name Is Not Used For This Transaction Code", "414": "Facility Street Address Is Not Used For This Transaction Code", "415": "Facility City Address Is Not Used For This Transaction Code", "416": "Facility State/Province Address Is Not Used For This Transaction Code", "417": "Facility ZIP/Postal Zone Is Not Used For This Transaction Code", "418": "Purchaser ID Qualifier Is Not Used For This Transaction Code", "419": "Purchaser ID Is Not Used For This Transaction Code", "420": "Purchaser ID Associated State Code Is Not Used For This Transaction Code", "421": "Purchaser Date Of Birth Is Not Used For This Transaction Code", "422": "Purchaser Gender Code Is Not Used For This Transaction Code", "423": "Purchaser First Name Is Not Used For This Transaction Code", "424": "Purchaser Last Name Is Not Used For This Transaction Code", "425": "Purchaser Street Address Is Not Used For This Transaction Code", "426": "Purchaser City Address Is Not Used For This Transaction Code", "427": "Purchaser State/Province Address Is Not Used For This Transaction Code", "428": "Purchaser ZIP/Postal Zone Is Not Used For This Transaction Code", "429": "Purchaser Country Code Is Not Used For This Transaction Code", "430": "Purchaser Relationship Code Is Not Used For This Transaction Code", "431": "Released Date Is Not Used For This Transaction Code", "432": "Released Time Is Not Used For This Transaction Code", "433": "Service Provider Name Is Not Used For This Transaction Code", "434": "Service Provider Street Address Is Not Used For This Transaction Code", "435": "Service Provider City Address Is Not Used For This Transaction Code", "436": "Service Provider State/Province Address Is Not Used For This Transaction Code", "437": "Service Provider ZIP/Postal Zone Is Not Used For This Transaction Code", "438": "Seller ID Qualifier Is Not Used For This Transaction Code", "439": "Seller ID Is Not Used For This Transaction Code", "440": "Seller Initials Is Not Used For This Transaction Code", "441": "Other Amount Claimed Submitted Grouping Incorrect", "442": "Other Payer Amount Paid Grouping Incorrect", "443": "Other Payer-Patient Responsibility Amount Grouping Incorrect", "444": "Benefit Stage Amount Grouping Incorrect", "445": "Diagnosis Code Grouping Incorrect", "446": "COB/Other Payments Segment Incorrectly Formatted", "447": "Additional Documentation Segment Incorrectly Formatted", "448": "Clinical Segment Incorrectly Formatted", "449": "Patient Segment Incorrectly Formatted", "450": "Insurance Segment Incorrectly Formatted", "451": "Transaction Header Segment Incorrectly Formatted", "452": "Claim Segment Incorrectly Formatted", "453": "Pharmacy Provider Segment Incorrectly Formatted", "454": "Prescriber Segment Incorrectly Formatted", "455": "Workers' Compensation Segment Incorrectly Formatted", "456": "Pricing Segment Incorrectly Formatted", "457": "Coupon Segment Incorrectly Formatted", "458": "Prior Authorization Segment Incorrectly Formatted", "459": "Facility Segment Incorrectly Formatted", "460": "Narrative Segment Incorrectly Formatted", "461": "Purchaser Segment Incorrectly Formatted", "462": "Service Provider Segment Incorrectly Formatted", "463": "Pharmacy Not Contracted In Assisted Living Network", "464": "Service Provider ID Qualifier Does Not Precede Service Provider ID", "465": "Patient ID Qualifier Does Not Precede Patient ID", "466": "Prescription/Service Reference Number Qualifier Does Not Precede Prescription/Service Reference Number", "467": "Product/Service ID Qualifier Does Not Precede Product/Service ID", "468": "Procedure Modifier Code Count Does Not Precede Procedure Modifier Code", "469": "Submission Clarification Code Count Does Not Precede Submission Clarification Code", "470": "Originally Prescribed Product/Service ID Qualifier Does Not Precede Originally Prescribed Product/Service Code", "471": "Other Amount Claimed Submitted Count Does Not Precede Other Amount Claimed Amount And/Or Qualifier", "472": "Other Amount Claimed Submitted Qualifier Does Not Precede Other Amount Claimed Submitted", "473": "Provider ID Qualifier Does Not Precede Provider ID", "474": "Prescriber ID Qualifier  Does Not Precede Prescriber ID", "475": "Primary Care Provider ID Qualifier Does Not Precede Primary Care Provider ID", "476": "Coordination Of Benefits/Other Payments Count Does Not Precede Other Payer Coverage Type", "478": "Other Payer ID Qualifier Does Not Precede Other Payer ID", "479": "Other Payer Amount Paid Count Does Not Precede Other Payer Amount Paid And/Or Qualifier", "480": "Other Payer Amount Paid Qualifier Does Not Precede Other Payer Amount Paid", "481": "Other Payer Reject Count Does Not Precede Other Payer Reject Code", "482": "Other Payer-Patient Responsibility Amount Count Does Not Precede Other Payer-Patient Responsibility Amount and/or Qualifier", "483": "Other Payer-Patient Responsibility Amount Qualifier Does Not Precede Other Payer-Patient Responsibility Amount", "484": "Benefit Stage Count Does Not Precede Benefit Stage Amount and/or Qualifier", "485": "Benefit Stage Qualifier Does Not Precede Benefit Stage Amount", "486": "Pay To Qualifier Does Not Precede Pay To ID", "487": "Generic Equivalent Product ID Qualifier Does Not Precede Generic Equivalent Product ID", "488": "DUR/PPS Code Counter Does Not Precede DUR Data Fields", "489": "DUR Co-Agent ID Qualifier Does Not Precede DUR Co-Agent ID", "490": "Compound Ingredient Component Count Does Not Precede Compound Product ID And/Or Qualifier", "491": "Compound Product ID Qualifier  Does Not Precede Compound Product ID", "492": "Compound Ingredient Modifier Code Count Does Not Precede Compound Ingredient Modifier Code", "493": "Diagnosis Code Count Does Not Precede Diagnosis Code And/Or Qualifier", "494": "Diagnosis Code Qualifier Does Not Precede Diagnosis Code", "495": "Clinical Information Counter Does Not Precede Clinical Measurement Data", "496": "Length Of Need Qualifier Does Not Precede Length Of Need", "497": "Question Number/Letter Count Does Not Precede Question Number/Letter", "498": "Accumulator Month Count Does Not Precede Accumulator Month", "5C": "M/I Other Payer Coverage Type", "5E": "M/I Other Payer Reject Count", "5J": "M/I Facility City Address", "50": "Non-Matched Pharmacy Number", "51": "Non-Matched Group ID", "52": "Non-Matched Cardholder ID", "53": "Non-Matched Person Code", "54": "Non-Matched Product/Service ID Number", "55": "Non-Matched Product Package Size", "56": "Non-Matched Prescriber ID", "58": "Non-Matched Primary Prescriber", "504": "Benefit Stage Qualifier Value Not Supported", "505": "Other Payer Coverage Type Value Not Supported", "506": "Prescription/Service Reference Number Qualifier Value Not Supported", "507": "Additional Documentation Type ID Value Not Supported", "508": "Authorized Representative State/Province Address Value Not Supported", "509": "Basis Of Request Value Not Supported", "510": "Billing Entity Type Indicator Value Not Supported", "511": "CMS Part D Defined Qualified Facility Value Not Supported", "512": "Compound Code Value Not Supported", "513": "Compound Dispensing Unit Form Indicator Value Not Supported", "514": "Compound Ingredient Basis Of Cost Determination Value Not Supported", "515": "Compound Product ID Qualifier Value Not Supported", "516": "Compound Type Value Not Supported", "517": "Coupon Type Value Not Supported", "518": "DUR Co-Agent ID Qualifier Value Not Supported", "519": "DUR/PPS Level Of Effort Value Not Supported", "520": "Delay Reason Code Value Not Supported", "521": "Diagnosis Code Qualifier Value Not Supported", "522": "Dispensing Status Value Not Supported", "523": "Eligibility Clarification Code Value Not Supported", "524": "Employer State/Province Address Value Not Supported", "525": "Facility State/Province Address Value Not Supported", "528": "Length of Need Qualifier Value Not Supported", "529": "Level Of Service Value Not Supported", "530": "Measurement Dimension Value Not Supported", "531": "Measurement Unit Value Not Supported", "532": "Medicaid Indicator Value Not Supported", "533": "Originally Prescribed Product/Service ID Qualifier Value Not Supported", "534": "Other Amount Claimed Submitted Qualifier Value Not Supported", "535": "Other Coverage Code Value Not Supported", "536": "Other Payer-Patient Responsibility Amount Qualifier Value Not Supported", "537": "Patient Assignment Indicator (Direct Member Reimbursement Indicator) Value Not Supported", "538": "Patient Gender Code Value Not Supported", "539": "Patient State/Province Address Value Not Supported", "540": "Pay to State/Province Address Value Not Supported", "541": "Percentage Sales Tax Basis Submitted Value Not Supported", "542": "Pregnancy Indicator Value Not Supported", "543": "Prescriber ID Qualifier Value Not Supported", "544": "Prescriber State/Province Address Value Not Supported", "545": "Prescription Origin Code Value Not Supported", "546": "Primary Care Provider ID Qualifier Value Not Supported", "547": "Prior Authorization Type Code Value Not Supported", "548": "Provider Accept Assignment Indicator Value Not Supported", "549": "Provider ID Qualifier Value Not Supported", "550": "Request Status Value Not Supported", "551": "Request Type Value Not Supported", "552": "Route of Administration Value Not Supported", "553": "Smoker/Non-Smoker Code Value Not Supported", "554": "Special Packaging Indicator Value Not Supported", "555": "Transaction Count Value Not Supported", "556": "Unit Of Measure Value Not Supported", "557": "COB Segment Present On A Non-COB Claim", "558": "Part D Plan Cannot Coordinate Benefits With Another Part D Plan", "559": "ID Submitted Is Associated With A Sanctioned Pharmacy", "560": "Pharmacy Not Contracted In Retail Network", "561": "Pharmacy Not Contracted In Mail Order Network", "562": "Pharmacy Not Contracted In Hospice Network", "563": "Pharmacy Not Contracted In Veterans Administration Network", "564": "Pharmacy Not Contracted In Military Network", "565": "Patient Country Code Value Not Supported", "566": "Patient Country Code Not Used For This Transaction", "567": "M/I Veterinary Use Indicator", "568": "Veterinary Use Indicator Value Not Supported", "569": "Provide Notice: Medicare Prescription Drug Coverage And Your Rights", "570": "Veterinary Use Indicator Not Used For This Transaction", "571": "Patient ID Associated State/Province Address Value Not Supported", "572": "Medigap ID Not Covered", "573": "Prescriber Alternate ID Associated State/Province Address Value Not Supported", "574": "Compound Ingredient Modifier Code Not Covered", "575": "Purchaser State/Province Address Value Not Supported", "576": "Service Provider State/Province Address Value Not Supported", "582": "M/I Fill Number", "583": "Provider ID Not Covered", "584": "Purchaser ID Associated State/Province Code Value Not Supported", "585": "Fill Number Value Not Supported", "586": "Facility ID Not Covered", "587": "Carrier ID Not Covered", "588": "Alternate ID Not Covered", "589": "Patient ID Not Covered", "590": "Compound Dosage Form Not Covered", "591": "Plan ID Not Covered", "592": "DUR Co-Agent ID Not Covered", "593": "M/I Date Of Service", "594": "Pay To ID Not Covered", "595": "Associated Prescription/Service Provider ID Not Covered", "596": "Compound Preparation Time Not Used For This Transaction Code", "597": "LTC Dispensing Type Does Not Support The Packaging Type", "598": "More Than One Patient Found", "599": "Cardholder ID Matched But Last Name Did Not", "6C": "M/I Other Payer ID Qualifier", "6D": "M/I Facility ZIP/Postal Zone", "6E": "M/I Other Payer Reject Code", "6G": "Coordination Of Benefits/Other Payments Segment Required For Adjudication", "6H": "Coupon Segment Required For Adjudication", "6J": "Insurance Segment Required For Adjudication", "6K": "Patient Segment Required For Adjudication", "6M": "Pharmacy Provider Segment Required For Adjudication", "6N": "Prescriber Segment Required For Adjudication", "6P": "Pricing Segment Required For Adjudication", "6Q": "Prior Authorization Segment Required For Adjudication", "6R": "Worker's Compensation Segment Required For Adjudication", "6S": "Transaction Segment Required For Adjudication", "6T": "Compound Segment Required For Adjudication", "6U": "Compound Segment Incorrectly Formatted", "6V": "Multi-ingredient Compounds Not Supported", "6W": "DUR/PPS Segment Required For Adjudication", "6X": "DUR/PPS Segment Incorrectly Formatted", "6Y": "Not Authorized To Submit Electronically", "6Z": "Provider Not Eligible To Perform Service/Dispense Product", "60": "Product/Service Not Covered For Patient Age", "61": "Product/Service Not Covered For Patient Gender", "62": "Patient/Card Holder ID Name Mismatch", "63": "Product/Service ID Not Covered For Institutionalized Patient", "64": "Claim Submitted Does Not Match Prior Authorization", "65": "Patient Is Not Covered", "66": "Patient Age Exceeds Maximum Age", "67": "Date Of Service Before Coverage Effective", "68": "Date Of Service After Coverage Expired", "69": "Date Of Service After Coverage Terminated", "600": "Coverage Outside Submitted Date Of Service", "602": "Associated Prescription/Service Provider ID Qualifier Does Not Precede Associated Prescription/Service Provider ID", "603": "Prescriber Alternate ID Qualifier Does Not Precede Prescriber Alternate ID", "604": "Purchaser ID Qualifier Does Not Precede Purchaser ID", "605": "Seller ID Qualifier Does Not Precede Seller ID", "606": "Brand Drug/Specific Labeler Code Required", "607": "Information Reporting (N1/N3) Transaction Cannot Be Matched To A Claim (B1/B3)", "608": "Step Therapy, Alternate Drug Therapy Required Prior To Use Of Submitted Product Service ID", "609": "COB <PERSON>laim Not Required, Patient Liability Amount Submitted Was Zero", "610": "Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Submitted Under Part D IIN PCN", "611": "Information Reporting Transaction (N1/N3) Was Matched To A Claim Submitted Under The Part D IIN/PCN Paid As Enhanced Or OTC Or By A Benefit Other Than Part D", "612": "LTC Appropriate Dispensing Invalid Submission Clarification Code (SCC) Combination", "613": "The Packaging Methodology  Or Dispensing Frequency Is Missing Or Inappropriate For LTC Short Cycle", "614": "Uppercase Character(s) Required", "615": "Compound Ingredient Basis Of Cost Determination Value 14 Required When Compound Ingredient Quantity Is 0 But Cost Is Greater Than $0", "616": "Submission Clarification Code 8 Required When Compound Ingredient Quantity Is 0", "617": "Compound Ingredient Drug Cost Cannot Be Negative Amount", "618": "Plan's Prescriber Data Base Indicates The Submitted Prescriber's DEA Does Not Allow This Drug DEA Schedule", "619": "Prescriber Type 1 NPI Required", "620": "This Product/Service May Be Covered Under Medicare Part D", "621": "This Medicaid Patient Is Medicare Eligible", "645": "Repackaged Product Is Not Covered By The Contract", "646": "Patient Not Eligible Due To Non Payment Of Premium. Patient To Contact Plan", "647": "Quantity Prescribed Required For CII Prescription", "648": "Quantity Prescribed Does Not Match Quantity Prescribed On Original CII Dispensing", "649": "Cumulative Quantity For This CII Rx Number Exceeds  Quantity Prescribed", "650": "Date Of Service Greater Than 60 Days From CII Date Prescription Written (414-DE)", "7A": "Provider Does Not Match Authorization On File", "7B": "Service Provider ID Qualifier Value Not Supported For Processor/Payer", "7C": "M/I Other Payer ID", "7D": "Non-Matched DOB", "7E": "M/I DUR/PPS Code Counter", "7G": "Future Date Not Allowed For DOB", "7H": "Non-Matched Gender Code", "7J": "Patient Relationship Code Value Not Supported", "7K": "Discrepancy Between Other Coverage Code And Other Payer Amount", "7M": "Discrepancy Between Other Coverage Code And Other Coverage Information On File", "7N": "Patient ID Qualifier Value Not Supported", "7P": "Coordination Of Benefits/Other Payments Count Exceeds Number of Supported Payers", "7Q": "Other Payer ID Qualifier Value Not Supported", "7R": "Other Payer Amount Paid Count Exceeds Number Of Supported Groupings", "7S": "Other Payer Amount Paid Qualifier Value Not Supported", "7T": "Quantity Intended To Be Dispensed Required For Partial Fill Transaction", "7U": "Days Supply Intended To Be Dispensed Required For Partial Fill Transaction", "7V": "Duplicate Fill Number", "7W": "Number Of Refills Authorized Exceed Allowable Refills", "7X": "Days Supply Exceeds Plan Limitation", "7Y": "Compounds Not Covered", "7Z": "Compound Requires Two Or More Ingredients", "70": "Product/Service Not Covered - Plan/Benefit Exclusion", "71": "Prescriber ID Is Not Covered", "72": "Primary Prescriber Is Not Covered", "73": "Additional Fills Are Not Covered", "74": "Other Carrier Payment Meets Or Exceeds Payable", "75": "Prior Authorization Required", "76": "Plan Limitations Exceeded", "77": "Discontinued Product/Service ID Number", "78": "Cost Exceeds Maximum", "79": "Fill Too Soon", "8A": "Compound Requires At Least One Covered Ingredient", "8B": "Compound Segment Missing On A Compound Claim", "8C": "M/I Facility ID", "8D": "Compound Segment Present On A Non-Compound Claim", "8E": "M/I DUR/PPS Level Of Effort", "8G": "Product/Service ID (407-D7) Must Be A Single Zero For Compounds", "8H": "Product/Service Only Covered On Compound Claim", "8J": "Incorrect Product/Service ID For Processor/Payer", "8K": "DAW Code Value Not Supported", "8M": "Sum Of Compound Ingredient Costs Does Not Equal Ingredient Cost Submitted", "8N": "Future Date Prescription Written Not Allowed", "8P": "Date Written Different On Previous Fill", "8Q": "Excessive Refills Authorized", "8R": "Submission Clarification Code Value Not Supported", "8S": "Basis Of Cost  Determination Value Not Supported", "8T": "U&C Must Be Greater Than Zero", "8U": "GAD Must Be Greater Than Zero", "8W": "Discrepancy Between Other Coverage Code And Other Payer Amount Paid", "8X": "Collection From Cardholder Not Allowed", "8Y": "Excessive Amount Collected", "8Z": "Product/Service ID Qualifier Value Not Supported", "80": "Diagnosis Code Submitted Does Not Meet Drug Coverage Criteria", "81": "<PERSON><PERSON><PERSON>", "82": "Claim Is Post-Dated", "83": "Duplicate Paid/Captured <PERSON><PERSON><PERSON>", "85": "Claim Not Processed", "86": "Submit Manual Reversal", "87": "Reversal Not Processed", "88": "DUR Reject Error", "89": "Rejected C<PERSON><PERSON>", "9B": "Reason For Service Code Value Not Supported", "9C": "Professional Service Code Value Not Supported", "9D": "Result Of Service Code Value Not Supported", "9E": "Quantity Does Not Match Dispensing Unit", "9G": "Quantity Dispensed Exceeds Maximum Allowed", "9H": "Quantity Not Valid For Product/Service ID Submitted", "9J": "Future Other Payer Date Not Allowed", "9K": "Compound Ingredient Component Count Exceeds Number Of Ingredients Supported", "9M": "Minimum Of Two Ingredients Required", "9N": "Compound Ingredient Quantity Exceeds Maximum Allowed", "9P": "Compound Ingredient Drug Cost Must Be Greater Than Zero", "9Q": "Route Of Administration Submitted Not Covered", "9R": "Prescription/Service Reference Number Qualifier Submitted Not Covered", "9S": "Future Associated Prescription/Service Date Not Allowed", "9T": "Prior Authorization Type Code Submitted Not Covered", "9U": "Provider ID Qualifier Submitted Not Covered", "9V": "Prescriber ID Qualifier Submitted Not Covered", "9W": "DUR/PPS Code Counter Exceeds Number Of Occurrences Supported", "9X": "Coupon Type Submitted Not Covered", "9Y": "Compound Product ID Qualifier Submitted Not Covered", "9Z": "Duplicate Product ID In Compound", "90": "Host Hung Up", "91": "Host Response Error", "92": "System Unavailable/Host Unavailable", "95": "Time Out", "96": "Scheduled Downtime", "97": "Payer Unavailable", "98": "Connection To Payer Is Down", "99": "Host Processing Error", "AA": "Patient Spenddown Not Met", "AB": "Date Written Is After Date Of Service", "AC": "Product Not Covered Non-Participating Manufacturer", "AD": "Billing Provider Not Eligible To Bill This Claim Type", "AE": "QMB (Qualified Medicare Beneficiary)-Bill <PERSON>", "AF": "Patient Enrolled Under Managed Care", "AG": "Days Supply Limitation For Product/Service", "AH": "Unit Dose Packaging Only Payable For Nursing Home Recipients", "AJ": "Generic Drug Required", "AK": "M/I Software Vendor/Certification ID", "AM": "M/I Segment Identification", "AQ": "M/I Facility Segment", "A1": "ID Submitted Is Associated With An Excluded Prescriber", "A2": "ID Submitted Is Associated To A Deceased Prescriber", "A5": "Not Covered Under Part D Law", "A6": "This Product/Service May Be Covered Under Medicare Part B", "A7": "M/I Internal Control Number", "A9": "M/I Transaction Count", "BA": "Compound Basis Of Cost Determination Submitted Not Covered", "BB": "Diagnosis Code Qualifier Submitted Not Covered", "BC": "Future Measurement Date Not Allowed", "BE": "M/I Professional Service Fee Submitted", "BM": "M/I Narrative Message", "B2": "M/I Service Provider ID Qualifier", "CA": "M/I Patient First Name", "CB": "M/I Patient Last Name", "CC": "M/I Cardholder First Name", "CD": "M/I Cardholder Last Name", "CE": "M/I Home Plan", "CF": "M/I Employer Name", "CG": "M/I Employer Street Address", "CH": "M/I Employer City Address", "CI": "M/I Employer State/Province Address", "CJ": "M/I Employer ZIP Postal Zone", "CK": "M/I Employer Phone Number", "CL": "M/I Employer Contact Name", "CM": "M/I Patient Street Address", "CN": "M/I Patient City Address", "CO": "M/I Patient State/Province Address", "CP": "M/I Patient ZIP/Postal Zone", "CQ": "M/I Patient Phone Number", "CR": "M/I Carrier ID", "CW": "M/I Alternate ID", "CX": "M/I Patient ID Qualifier", "CY": "M/I Patient ID", "CZ": "M/I Employer ID", "DC": "M/I Dispensing Fee Submitted", "DN": "M/I Basis Of Cost Determination", "DQ": "M/I Usual And Customary Charge", "DR": "M/I Prescriber Last Name", "DT": "M/I Special Packaging Indicator", "DU": "M/I Gross Amount Due", "DV": "M/I Other Payer Amount Paid", "DX": "M/I <PERSON><PERSON> Paid Amount Submitted", "DY": "M/I Date Of Injury", "DZ": "M/I Claim/Reference ID", "EA": "M/I Originally Prescribed Product/Service Code", "EB": "M/I Originally Prescribed Quantity", "EC": "M/I Compound Ingredient Component Count", "ED": "M/I Compound Ingredient Quantity", "EE": "M/I Compound Ingredient Drug Cost", "EF": "M/I Compound Dosage Form Description Code", "EG": "M/I Compound Dispensing Unit Form Indicator", "EJ": "M/I Originally Prescribed Product/Service ID Qualifier", "EK": "M/I Scheduled Prescription ID Number", "EM": "M/I Prescription/Service Reference Number Qualifier", "EN": "M/I Associated Prescription/Service Reference Number", "EP": "M/I Associated Prescription/Service Date", "ER": "M/I Procedure Modifier Code", "ET": "M/I Quantity Prescribed", "EU": "M/I Prior Authorization Type Code", "EV": "M/I Prior Authorization ID Submitted", "EY": "M/I Provider ID Qualifier", "EZ": "M/I Prescriber ID Qualifier", "E1": "M/I Product/Service ID Qualifier", "E2": "M/I Route Of Administration", "E3": "M/I Incentive Amount Submitted", "E4": "M/I Reason For Service Code", "E5": "M/I Professional Service Code", "E6": "M/I Result Of Service Code", "E7": "M/I Quantity Dispensed", "E8": "M/I Other Payer Date", "E9": "M/I Provider ID", "FO": "M/I Plan ID", "GE": "M/I Percentage Sales Tax Amount Submitted", "G1": "M/I Compound Type", "G2": "M/I CMS Part D Defined Qualified Facility", "G4": "Physician Must Contact Plan", "G5": "Pharmacist Must Contact Plan", "G6": "Pharmacy Not Contracted In Specialty Network", "G7": "Pharmacy Not Contracted In Home Infusion Network", "G8": "Pharmacy Not Contracted In Long Term Care Network", "G9": "Pharmacy Not Contracted In 90 Day Retail Network", "HA": "M/I Flat Sales Tax Amount Submitted", "HB": "M/I Other Payer Amount Paid Count", "HC": "<PERSON>/I Other Payer Amount Paid Qualifier", "HD": "M/I Dispensing Status", "HE": "M/I Percentage Sales Tax Rate Submitted", "HF": "M/I Quantity Intended To Be Dispensed", "HG": "M/I Days Supply Intended To Be Dispensed", "HN": "M/I Patient E-Mail Address", "H1": "M/I Measurement Time", "H2": "M/I Measurement Dimension", "H3": "M/I Measurement Unit", "H4": "M/I Measurement Value", "H5": "M/I Primary Care Provider Location Code", "H6": "M/I DUR Co-Agent ID", "H7": "M/I Other Amount Claimed Submitted Count", "H8": "M/I Other Amount Claimed Submitted Qualifier", "H9": "M/I Other Amount Claimed Submitted", "JE": "M/I Percentage Sales Tax Basis Submitted", "J9": "M/I DUR Co-Agent ID Qualifier", "KE": "M/I Coupon Type", "K5": "M/I Transaction Reference Number", "M1": "Patient Not Covered In This Aid Category", "M2": "Recipient Locked In", "M3": "Host PA/MC <PERSON><PERSON>", "M4": "Prescription/Service Reference Number/Time Limit Exceeded", "M5": "Requires Manual Claim", "M6": "Host Eligibility Error", "M7": "Host Drug File Error", "M8": "Host Provider File Error", "ME": "M/I Coupon Number", "MG ": "M/I Other Payer BIN Number", "MH": "M/I Other Payer Processor Control Number", "MJ": "M/I Other Payer Group ID", "MK": "Non-Matched Other Payer BIN Number", "MM": "Non-Matched Other Payer Processor Control Number", "MN": "Non-Matched Other Payer Group ID", "MP": "Other Payer Cardholder ID Not Covered", "MR": "Product Not On Formulary", "MS": "More than 1 Cardholder Found -- Narrow Search Criteria", "MT": "M/I Patient Assignment Indicator (Direct Member Reimbursement Indicator)", "MU": "M/I Benefit Stage Count", "MV": "M/I Benefit Stage Qualifier", "MW": "M/I Benefit Stage Amount", "MX": "Benefit Stage Count Does Not Match Number Of Repetitions", "MZ": "Error Overflow", "NE": "M/I Coupon Value Amount", "NN": "Transaction Rejected At Switch Or Intermediary", "NP": "M/I Other Payer-Patient Responsibility Amount Qualifier", "NQ": "M/I Other Payer-Patient Responsibility Amount", "NR": "M/I <PERSON> Payer-Patient Responsibility Amount Count", "NU": "M/I Other Payer Cardholder ID", "NV": "M/I Delay Reason Code", "NX": "M/I Submission Clarification Code Count", "N1": "No Patient Match Found", "N3": "M/I Medicaid Paid Amount", "N4": "M/I Medicaid Subrogation Internal Control Number/Transaction Control Number (ICN/TCN)", "N5": "M/I Medicaid ID Number", "N6": "M/I Medicaid Agency Number", "N7": "Use Prior Authorization ID Provided During Transition Period", "N8": "Use Prior Authorization ID Provided For Emergency Supply", "N9": "Use Prior Authorization ID Provided For Level of Care Change", "PA": "PA Exhausted/Not Renewable", "PB": "Invalid Transaction Count For This Transaction Code", "PC": "M/I Request Claim Segment", "PD": "M/I Request Clinical Segment", "PE": "M/I Request Coordination Of Benefits/Other Payments Segment", "PF": "M/I Request Compound Segment", "PG": "M/I Request Coupon Segment", "PH": "M/I Request DUR/PPS Segment", "PJ": "M/I Request Insurance Segment", "PK": "M/I Request Patient Segment", "PM": "M/I Request Pharmacy Provider Segment", "PN": "M/I Request Prescriber Segment", "PP": "M/I Request Pricing Segment", "PQ": "M/I Narrative Segment", "PR": "M/I Request Prior Authorization Segment", "PS": "M/I Transaction Header Segment", "PT": "M/I Request Workers Compensation Segment", "PV": "Non-Matched Associated Prescription/Service Date", "PW": "Employer ID Not Covered", "PX": "Other Payer ID Not Covered", "PY": "Non-Matched Unit Form/Route of Administration", "PZ": "Non-Matched Unit Of Measure To Product/Service ID", "P0": "Non-zero Value Required for Vaccine Administration", "P1": "Associated Prescription/Service Reference Number Not Found", "P2": "Clinical Information Counter Out Of Sequence", "P3": "Compound Ingredient Component Count Does Not Match Number Of Repetitions", "P4": "Coordination Of Benefits/Other Payments Count Does Not Match Number Of Repetitions", "P5": "Coupon Expired", "P6": "Date Of Service Prior To Date Of Birth", "P7": "Diagnosis Code Count Does Not Match Number Of Repetitions", "P8": "DUR/PPS Code Counter Out Of Sequence", "P9": "Field Is Non-Repeatable", "RA": "PA Reversal Out Of Order", "RB": "Multiple Partials Not Allowed", "RC": "Different Drug Entity Between Partial & Completion", "RD": "Mismatched Cardholder/Group ID-Partial To Completion", "RE": "M/I Compound Product ID Qualifier", "RF": "Improper Order Of Dispensing Status Code On Partial Fill Transaction", "RG": "M/I Associated Prescription/Service Reference Number On Completion Transaction", "RH": "M/I Associated Prescription/Service Date On Completion Transaction", "RJ": "Associated Partial Fill Transaction Not On File", "RK": "Partial Fill Transaction Not Supported", "RL": "Transitional Benefit/Resubmit Claim", "RM": "Completion Transaction Not Permitted With Same Date Of Service As Partial Transaction", "RN": "Plan Limits Exceeded On Intended Partial Fill Field Limitations", "RP": "Out Of Sequence Reversal On Partial Fill Transaction", "RS": "M/I Associated Prescription/Service Date On Partial Transaction", "RT": "M/I Associated Prescription/Service Reference Number On Partial Transaction", "RU": "Mandatory Data Elements Must Occur Before Optional Data Elements In A Segment", "R0": "Professional Service Code of MA required for Vaccine Incentive Fee Submitted", "R1": "Other Amount Claimed Submitted Count Does Not Match Number Of Repetitions", "R2": "Other Payer Reject Count Does Not Match Number Of Repetitions", "R3": "Procedure Modifier Code Count Does Not Match Number Of Repetitions", "R4": "Procedure Modifier Code Invalid For Product/Service ID", "R5": "Product/Service ID Must Be Zero When Product/Service ID Qualifier Equals 06", "R6": "Product/Service Not Appropriate For This Location", "R7": "Repeating Segment Not Allowed In Same Transaction", "R8": "Syntax Error", "R9": "Value In Gross Amount Due Does Not Follow Pricing Formulae", "S0": "Accumulator Month Count Does Not Match Number of Repetitions", "S1": "M/I Accumulator Year", "S2": "M/I Transaction Identifier", "S3": "M/I Accumulated Patient True Out Of Pocket Amount", "S4": "M/I Accumulated Gross Covered Drug Cost  Amount", "S5": "M/I DateTime", "S6": "M/I Accumulator Month", "S7": "M/I Accumulator Month Count", "S8": "Non-Matched Transaction Identifier", "S9": "M/I Financial Information Reporting Transaction Header Segment", "SE": "M/I Procedure Modifier Code Count", "SF": "Other Payer Amount Paid Count Does Not Match Number Of Repetitions", "SG": "Submission Clarification Code Count Does Not Match Number of Repetitions", "SH": "Other Payer-Patient Responsibility Amount Count Does Not Match Number of Repetitions", "SW": "Accumulated Patient True Out Of Pocket Must Be Equal To Or Greater Than Zero", "TE": "Missing/Invalid Compound Product ID", "TN": "Emergency Supply/Resubmit Claim", "TP": "Level Of Care Change/Resubmit Claim", "TQ": "Dosage Exceeds Product Labeling Limit", "TR": "M/I Billing Entity Type Indicator", "TS": "M/I Pay To Qualifier", "TT": "M/I Pay To ID", "TU": "M/I Pay To Name", "TV": "M/I Pay To Street Address", "TW": "M/I Pay To City Address", "TX": "M/I Pay To State/ Province Address", "TY": "M/I Pay To ZIP/Postal Zone", "TZ": "M/I Generic Equivalent Product ID Qualifier", "T0": "Accumulator Month Count Exceeds Number Of Occurrences Supported", "T1": "Request Financial Segment Required For Financial Information Reporting", "T2": "M/I Request Reference Segment", "T3": "Out Of Order DateTime", "T4": "Duplicate DateTime", "UA": "M/I Generic Equivalent Product ID", "UE": "M/I Compound Ingredient Basis Of Cost Determination", "UU": "DAW 0 Cannot Be Submitted On A Multi-source Drug With Available Generics", "UZ": "Other Payer Coverage Type (338-5C) Required On Reversals To Downstream Payers. Resubmit Reversal With This Field.", "U7": "M/I Pharmacy Service Type", "VA": "Pay To Qualifier Value Not Supported", "VB": "Generic Equivalent Product ID Qualifier Value Not Supported", "VC": "Pharmacy Service Type Value Not Supported", "VD": "Eligibility Search Time Frame Exceeded", "VE": "M/I Diagnosis Code Count", "WE": "M/I Diagnosis Code Qualifier", "W9": "Accumulated Gross Covered Drug Cost Amount Must Be Equal To Or Greater Than Zero", "XE": "M/I Clinical Information Counter", "XZ": "M/I Associated Prescription/Service Reference Number Qualifier", "X1": "Accumulated Patient True Out Of Pocket Exceeds Maximum", "X2": "Accumulated Gross Covered Drug Cost Exceeds Maximum", "X3": "Out Of Order Accumulator Months", "X4": "Accumulator Year Not Current Or Prior Year", "X5": "M/I Financial Information Reporting Request Insurance Segment", "X6": "M/I Request Financial Segment", "X7": "Financial Information Reporting Request Insurance Segment Required For Financial Reporting", "X8": "Procedure Modifier Code Count Exceeds Number Of Occurrences Supported", "X9": "Diagnosis Code Count Exceeds Number Of Occurrences Supported", "X0": "M/I Associated Prescription/Service Fill Number", "YA": "Compound Ingredient Modifier Code Count Exceeds Number Of Occurrences Supported", "YB": "Other Amount Claimed Submitted Count Exceeds Number Of Occurrences Supported", "YC": "Other Payer Reject Count Exceeds Number Of Occurrences Supported", "YD": "Other Payer-Patient Responsibility Amount Count Exceeds Number Of Occurrences Supported", "YE": "Submission Clarification Code Count Exceeds Number of Occurrences Supported", "YF": "Question Number/Letter Count Exceeds Number Of Occurrences Supported", "YG": "Benefit Stage Count Exceeds Number Of Occurrences Supported", "YH": "Clinical Information Counter Exceeds Number of Occurrences Supported", "YJ": "Medicaid Agency Number Not Supported", "YK": "M/I Service Provider Name", "YM": "M/I Service Provider Street Address", "YN": "M/I Service Provider City Address", "YP": "M/I Service Provider State/Province Code Address", "YQ": "M/I Service Provider ZIP/Postal Code", "YR": "M/I Patient ID Associated State/Province Address", "YS": "M/I Purchaser Relationship Code", "YT": "<PERSON>/I <PERSON>ller Initials", "YU": "M/I Purchaser ID Qualifier", "YV": "M/I Purchaser ID", "YW": "M/I Purchaser ID Associated State/Province Code", "YX": "M/I Purchaser Date of Birth", "YY": "M/I Purchaser Gender Code", "YZ": "M/I Purchaser First Name", "Y0": "M/I Purchaser Last Name", "Y1": "M/I Purchaser Street Address", "Y2": "M/I Purchaser City Address", "Y3": "M/I Purchaser State/Province Code", "Y4": "M/I Purchaser ZIP/Postal Code", "Y5": "M/I Purchaser Country Code", "Y6": "M/I Time Of Service", "Y7": "M/I Associated Prescription/Service Provider ID Qualifier", "Y8": "M/I Associated Prescription/Service Provider ID", "Y9": "<PERSON>/<PERSON><PERSON> ID", "Z0": "Purchaser Country Code Value Not Supported For Processor/Payer", "Z1": "Prescriber Alternate ID Qualifier Value Not Supported", "Z2": "M/I Purchaser Segment", "Z3": "Purchaser Segment Present On A Non-Controlled Substance Reporting Transaction", "Z4": "Purchaser Segment Required On A Controlled Substance Reporting Transaction", "Z5": "M/I Service Provider Segment", "Z6": "Service Provider Segment Present On A Non-Controlled Substance Reporting Transaction", "Z7": "Service Provider Segment Required On A Controlled Substance Reporting Transaction", "Z8": "Purchaser Relationship Code Value Not Supported", "Z9": "Prescriber Alternate ID Not Covered", "ZB": "<PERSON>/<PERSON> Seller ID Qualifier", "ZC": "Associated Prescription/Service Provider ID Qualifier Value Not Supported For Processor/Payer", "ZD": "Associated Prescription/Service Reference Number Qualifier Value Not Supported", "ZE": "M/I Measurement Date", "ZF": "M/I Sales Transaction ID", "ZK": "M/I Prescriber ID Associated State/Province Address", "ZM": "M/I Prescriber Alternate ID Qualifier", "ZN": "Purchaser ID Qualifier Value Not Supported For Processor/Payer", "ZP": "M/I Prescriber Alternate ID", "ZQ": "M/I Prescriber Alternate ID Associated State/Province Address", "ZS": "M/I Reported Adjudicated Program Type", "ZT": "M/I Released Date", "ZU": "M/I Released Time", "ZV": "Reported Adjudicated ProgramType Value Not Supported", "ZW": "M/I Compound Preparation Time", "ZX": "M/I CMS Part D Contract ID", "ZY": "M/I Medicare Part D Plan Benefit Package (PBP)", "ZZ": "Cardholder ID Submitted Is Inactive. New Cardholder ID On File.", "622": "COB <PERSON>laim Not Required, Patient Liability Amount Submitted Was Zero", "623": "M/I Authorized Representative Country Code", "624": "M/I Employer Country Code", "625": "M/I Entity Country Code", "627": "M/I Facility Country Code", "628": "M/I Patient ID Associated Country Code", "629": "M/I Pay To Country Code", "630": "M/I Prescriber Alternate ID Associated Country Code", "631": "M/I Prescriber ID Associated Country Code", "632": "M/I Prescriber Country Code", "633": "M/I Purchaser ID Associated Country Code", "634": "Authorized Representative Country Code Value Not Supported", "635": "Employer Country Code Value Not Supported", "637": "Entity Country Code Value Not Supported", "638": "Facility Country Code Value Not Supported", "639": "Patient ID Associated Country Code Value Not Supported", "640": "Pay To Country Code Value Not Supported", "641": "Prescriber Alternate ID Associated Country Code Value Not Supported", "642": "Prescriber ID Associated Country Code Value Not Supported", "643": "Prescriber Country Code Value Not Supported", "644": "Purchaser ID Associated Country Code Value Not Supported", "A3": "This Product May Be Covered Under Hospice - Medicare A", "A4": "This Product May Be Covered Under The Medicare- B Bundled Payment To An ESRD Dialysis Facility", "651": "REMS: Mandatory Data Element(s) Missing", "652": "REMS: Prescriber Not Matched Or May Not Be Enrolled", "653": "REMS: Patient Not Matched Or May Not Be Enrolled", "654": "REMS: Pharmacy Not Matched Or May Not Be Enrolled", "655": "REMS: Multiple Patient Matches", "656": "REMS: Patient Age Not Matched", "657": "REMS: Patient Gender Not Matched", "658": "REMS: Pharmacy Has Not Enrolled", "659": "REMS: Pharmacy Has Not Renewed Enrollment", "660": "REMS: Pharmacy Has Not Submitted Agreement Form", "661": "REMS: Pharmacy Has Been Suspended Due To Non-compliance", "662": "REMS: Prescriber Has Not Enrolled", "663": "REMS: Prescriber Has Not Completed A Knowledge Assessment", "664": "REMS: Prescriber <PERSON> Been Suspended Due To Non-compliance", "665": "REMS: Excessive Days Supply", "666": "REMS: Insufficient Days Supply", "667": "REMS: Excessive Dosage", "668": "REMS: Insufficient Dosage", "669": "REMS: Additional Fills Not Permitted", "670": "REMS: Laboratory Test Results Not Documented", "671": "REMS: Laboratory Test Not Conducted Within Specified Time Period", "672": "REMS: Dispensing Not Authorized Due To Laboratory Test Results", "673": "REMS: Prescriber Counseling Of Patient Not Documented", "674": "REMS: Prescriber Has Not Documented Safe Use Conditions", "675": "REMS: Prescriber Has Not Documented Patient Opioid Tolerance", "676": "REMS: Prescriber Has Not Documented Patient Contraceptive Use", "677": "REMS: Lack Of Contraindicated Therapy Not Documented", "678": "REMS: Step Therapy Not Documented", "679": "REMS: Prescriber Has Not Enrolled Patient", "680": "REMS: Prescriber Must Renew Patient Enrollment", "681": "REMS: Patient Enrollment Requirements Have Not Been Met", "682": "REMS: Prescriber Has Not Submitted Patient Agreement", "683": "REMS: Prescriber Has Not Verified <PERSON><PERSON>'s Reproductive Potential", "684": "REMS: Patient Has Not Documented Safe Use Conditions", "685": "REMS: Patient Has Not Documented Completed Education", "686": "REMS: Patient Has Not Documented Contraceptive Use", "687": "REMS: Administrator <PERSON><PERSON>", "688": "REMS: Service Billing Denied", "689": "PDMP: Administrator Den<PERSON>", "690": "PDMP: Pharmacy Not Contracted", "691": "PDMP: Pharmacy Contract Not Renewed", "692": "PDMP: M/I Patient First Name", "693": "PDMP: <PERSON>/I Patient Last Name", "694": "PDMP: M/I Patient Street Address", "695": "PDMP: M/I Patient City", "696": "PDMP: M/I Patient State Or Province", "697": "PDMP: M/I Patient ZIP/Postal Code", "698": "PDMP: M/I Prescriber ID", "699": "PDMP: M/I Prescriber Last Name", "700": "PDMP: M/I Patient ID", "701": "PDMP: M/I Patient Date Of Birth", "702": "PDMP: M/I Patient Gender", "703": "PDMP: M/I Prescription Origin Code", "704": "PDMP: M/I Scheduled Rx Serial Number", "705": "PDMP: M/I Product/Service ID", "706": "PDMP: M/I Compound Code", "707": "PDMP: M/I Patient Phone Number", "708": "PDMP: M/I Reported Adjudicated Program Type", "27": "Product Identifier Not FDA/NSDE Listed", "30": "Reversal Request Outside Processor Reversal Window", "31": "No Matching Pa<PERSON> Found For Reversal Request", "709": "M/I Record Type", "710": "Date Received After Requested Response Date", "711": "M/I Transmission Date", "712": "M/I Sending Entity Identifier", "713": "M/I Receiver ID", "714": "M/I Transmission File Type", "715": "M/I Transmission Type", "716": "Transmission File Type Not Supported", "717": "M/I Submission Number", "718": "M/I Audit Request Type", "719": "Audit Request Type Not Supported", "720": "M/I Service Provider Chain Code", "721": "M/I Entity Name", "722": "M/I Entity Contact First Name", "723": "M/I Entity Contact Last Name", "724": "M/I Entity Address Line 1", "725": "M/I Entity Address Line 2", "726": "M/I Entity City", "727": "M/I Entity State/Province Address", "728": "M/I Entity ZIP/Postal Code", "729": "M/I Entity Fax Number", "730": "<PERSON>/I En<PERSON>ty Email", "731": "Header Response Status Not Supported For This Transmission File Type", "732": "Reject Code Not Supported For This Transmission File Type", "733": "M/I Claim Sequence Number", "734": "M/I Audit Control Identification", "735": "M/I Audit Range Qualifier", "736": "Audit Range Qualifier Not Supported For This Audit Request Type", "737": "M/I Audit Range Start", "738": "Audit Range Start Not Supported For This Audit Request Type", "739": "M/I Audit Range End", "740": "Audit Range End Not Supported For This Audit Request Type", "741": "Exceeds Range Start Limitations", "742": "Exceeds Range End Limitations", "743": "M/I Requested Response Date", "744": "Response Date Requires Rescheduling", "745": "M/I Estimated Arrival Time Description", "746": "Estimated Arrival Time Requires Rescheduling", "747": "<PERSON>/I <PERSON><PERSON>ponsor", "748": "Non-Matched Processor Control Number", "749": "M/I Audit Element Type 1", "750": "M/I Audit Element Type 2", "751": "M/I Audit Element Type 3", "752": "M/I Audit Element Type 4", "753": "M/I Audit Element Type 5", "754": "Audit Element Type Not Allowable Per State Regulation", "755": "Audit Element Type Not Required For Dispensing", "756": "M/I Audit Element Response Type 1", "757": "M/I Audit Element Response Type 2", "758": "M/I Audit Element Response Type 3", "759": "M/I Audit Element Response Type 4", "760": "M/I Audit Element Response Type 5", "761": "M/I Discrepancy Code 1", "762": "M/I Discrepancy Code 2", "763": "M/I Discrepancy Code 3", "764": "M/I Discrepancy Message", "765": "M/I Discrepancy Amount", "766": "Discrepancy Amount In Excess Of Claimed Amount", "767": "M/I Record Count", "768": "Pharmacy Location Has Closed", "769": "Paid Billing Transaction (B1/B3) Submitted Under The Part D IIN PCN Found But Information Reporting Reversal (N2) Cannot Be Matched To An Information Reporting Transaction (N1/N3) In An Approved Status; Reversal (N2) Not Processed", "770": "Paid Billing Transaction (B1/B3) Submitted Under The Part D IIN PCN Not Found And Information Reporting Reversal (N2) Cannot Be Matched To An Information Reporting Transaction (N1/N3) In Approved Status; Reversal (N2) Not Processed", "773": "Prescriber Is Not Listed On Medicare Enrollment File", "774": "Prescriber Medicare Enrollment Period Is Outside Of Claim Date Of Service", "775": "Pharmacy Not Listed Within Medicare Fee For Service Active Enrollment File", "776": "Pharmacy Enrollment With Medicare Fee For Service Has Terminated", "771": "Compound Contains Unidentifiable Ingredient(s); Submission Clarification Code Override Not Allowed", "772": "Compound Not Payable Due To Non-covered Ingredient(s); Submission Clarification Code Override Not Allowed", "777": "Plan's Prescriber Data Base Not Able To Verify Active State License With Prescriptive Authority For Prescriber ID Submitted", "820": "Information Reporting Transaction (N1/N3) Matched To Reversed Or Rejected Claim Not Submitted Under Part D IIN PCN", "821": "Information Reporting (N1/N3) Transaction Matched To Paid Claim Not Submitted Under Part D IIN PCN", "822": "Drug Is Unrelated To The Terminal Illness And/Or Related Conditions. Not Covered Under Hospice.", "823": "Drug Is Beneficiary's Liability - Not Covered By Hospice Or Part D. Hospice Non-Formulary. Check Other Coverage.", "816": "Pharmacy Benefit Exclusion, May Be Covered Under Patient's Medical Benefit", "817": "Pharmacy Benefit Exclusion, Covered Under Patient's Medical Benefit", "818": "Medication Administration Not Covered, Plan Benefit Exclusion", "819": "Plan Enrollment File Indicates Medicare As Primary Coverage", "778": "Invalid Transmission File Type", "779": "Invalid Document Reference Number", "780": "M/I Transmission Time", "781": "Corrupted Transmission Control Number", "782": "M/I Sender ID", "783": "M/I Receiver ID", "784": "M/I File Type", "785": "M/I Submission Number", "786": "M/I Transmission Date", "787": "M/I Accumulator Balance Count", "788": "M/I Accumulator Network Indicator", "789": "M/I Accumulator Action Code", "790": "M/I Benefit Type", "791": "M/I In Network Status", "792": "Duplicate Record", "793": "Retry Limit Exceeded", "794": "Deductible Over Accumulated", "795": "Out Of Pocket Over Accumulated", "796": "Maximum Benefit Amount (CAP) Over Accumulated", "797": "Corrupted Transmission Control Number", "798": "SA Over Accumulated", "799": "LTC Over Accumulated", "800": "RXC Over Accumulated", "801": "M/I Total Amount Paid", "802": "M/I Amount Of Copay", "803": "M/I Patient Pay Amount", "804": "M/I Amount Attributed To Product Selection/Brand", "805": "M/I Amount Attributed To Sales Tax", "806": "M/I Amount Attributed To Process Fee", "807": "M/I Invoiced Amount", "808": "M/I Penalty Amount", "809": "Mismatched Original Authorization", "810": "M/I Partner Eligibility Data", "811": "Partner Eligibility Mismatch", "812": "M/I Record Length", "813": "M/I Action Code", "814": "Not Supported Accumulator Action Code", "815": "Balance Mismatch", "825": "Claim Date Of Service Is Outside Of Product's FDA/NSDE Marketing Dates", "828": "Plan/Beneficiary Case Management Restriction In Place", "824": "Multi-transaction Transmission Not Allowed In Current NCPDP Standard", "826": "Prescriber NPI Submitted Not Found Within Processor's NPI File", "827": "Pharmacy Service Provider Is Temporarily Suspended From Processing Claims By Payer/Processor", "829": "Pharmacy Must Notify Beneficiary: Claim Not Covered Due To Failure To Meet Medicare Part D Active, Valid Prescriber NPI Requirements", "830": "Workers' Comp Or P&C Adjuster Authorization Required -- Patient Must Directly Contact Their Adjuster", "1W": "Multi-Ingredient Compound Must Be A Single Transaction", "281": "Intermediary Authorization Type ID Is Not Used For This Transaction Code", "282": "Intermediary Authorization ID Is Not Used For This Transaction Code", "477": "Other Payer ID Count Does Not Precede Other Payer ID Data Fields", "526": "Header Response Status Value Not Supported", "527": "Intermediary Authorization Type ID Value Not Supported", "577": "M/I Other Payer ID", "578": "Other Payer ID Count Does Not Match Number of Repetitions", "579": "Other Payer ID Count Exceeds Number Of Occurrences Supported", "580": "Other Payer ID Count Grouping Incorrect", "581": "Other Payer ID Count Is Not Used For This Transaction Code", "601": "Intermediary Authorization Type ID Does Not Precede Intermediary Authorization ID", "7F": "Future Date Not Allowed For Date Of Birth", "8V": "Negative Dollar Amount Is Not Supported In The Other Payer Amount Paid Field", "84": "<PERSON>laim Has Not Been Paid/Captured", "EW": "M/I Intermediary Authorization Type ID", "EX": "M/I Intermediary Authorization ID", "RV": "Multiple Reversals Per Transmission Not Supported", "832": "Prescriber NPI Not Found, Therefore NPI Active Status, MEDICARE Enrollment, Prescriptive Authority Could Not Be Validated", "833": "Accumulator Year Is Not Within ATBT Timeframe", "831": "Product Service ID Carve-Out, Bill Medicaid Fee For Service", "ZA": "The Coordination Of Benefits/Other Payments Segment Is Mandatory To A Downstream Payer", "834": "M/I Provider First Name", "835": "M/I Provider Last Name", "836": "M/I Facility ID Qualifier", "837": "Facility ID Value Not Supported", "838": "M/I Original Manufacturer Product  ID", "839": "M/I Original Manufacturer Product  ID Qualifier", "840": "Original Manufacturer Product  ID Value Not Supported", "841": "Record Is Locked.", "842": "Record Is Not Locked.", "843": "M/I Transmission ID", "876": "Prescriptive Authority Restrictions Apply, Criteria Not Met", "877": "Service Provider ID Terminated  On NPPES File", "878": "Service Provider ID Not Found On NPPES File", "879": "Service Provider ID Excluded From Receiving CMS Enrollment Data", "844": "M/I Other Payer Adjudicated Program Type", "845": "Other Payer Reconciliation ID Is Not Used For This Transaction Code", "846": "Benefit Stage Indicator Count Is Not Used For This Transaction Code", "847": "Benefit Stage Indicator Count Does Not Precede Benefit Stage Indicator", "848": "M/I Benefit Stage Indicator Count", "849": "Benefit Stage Indicator Count Does Not Match Number of Repetitions", "850": "Benefit Stage Indicator Is Not Used For This Transaction Code", "851": "Benefit Stage Indicator Value Not Supported", "852": "M/I Benefit Stage Indicator", "853": "N Payer IIN Is Not Used For This Transaction Code", "854": "M/I N Payer IIN", "855": "Non-Matched N Payer IIN", "856": "N Payer Processor Control Number Is Not Used For This Transaction Code", "857": "M/I N Payer Processor Control Number", "858": "Non-Matched N Payer Processor Control Number", "859": "N Payer Group ID Is Not Used For This Transaction Code", "860": "M/I N Payer Group ID", "861": "Non-Matched N Payer Group ID", "862": "N Payer Cardholder ID Is Not Used For This Transaction Code", "863": "M/I N Payer Cardholder ID", "864": "N Payer Cardholder ID Is Not Covered", "865": "N Payer Adjudicated Program Type Is Not Used For This Transaction Code", "866": "M/I N Payer Adjudicated Program Type", "867": "N Payer Adjudicated Program Type Value Not Supported", "868": "M/I N Transaction Reconciliation ID", "869": "M/I N Transaction Source Type", "871": "M/I Compound Level Of Complexity", "872": "Mismatch Between Compound Level Of Complexity And Preparation Environment Type", "873": "M/I Preparation Environment Type", "874": "M/I Preparation Environment Event Code", "870": "M/I Prescriber DEA Number", "875": "M/I Total Prescribed Quantity Remaining", "887": "A Previous Payer(s) Is An Excluded Federal Health Care Program Copay Assistance Is Not Allowed", "888": "Beneficiary Is Enrolled In Excluded Federal Health Care Program", "889": "Prescriber Not Enrolled in State Medicaid Program", "890": "Pharmacy Not Enrolled in State Medicaid Program", "880": "M/I Submission Type Code", "881": "Missing Submission Type Code Count", "882": "M/I Do Not Dispense Before Date", "883": "Date of Service Prior To Do Not Dispense Before Date", "884": "M/I Multiple RX Order Group Reason Code", "885": "M/I Multiple RX Order Group ID", "886": "M/I Prescriber Place of Service", "891": "Days Supply Is Less Than Plan Minimum", "892": "Pharmacy Must Attest FDA REMS Requirements Have Been Met", "893": "Pharmacy Must Attest Required Patient Form Is On File", "894": "Pharmacy Must Attest Plan Medical Necessity Criteria Has Been Met", "895": "Allowed Number of Overrides Exhausted", "896": "Other Adjudicated Program Type Of Unknown Is Not Covered"}, "522-FM": {"0": "Not Specified", "1": "Ingredient <PERSON><PERSON> as Submitted", "2": "Ingredient Cost Reduced to AWP Pricing", "3": "Ingredient Cost Reduced to AWP Less X% Pricing", "4": "Usual & Customary Paid as Submitted", "5": "<PERSON><PERSON> of Ingredient Cost Plus Fees Versus Usual & Customary", "6": "MAC Pricing Ingredient Cost Paid", "7": "MAC Pricing Ingredient Cost Reduced to MAC", "8": "Contract Pricing", "9": "Acquisition Pricing", "10": "ASP (Average Sales Price)", "11": "AMP (Average Manufacturer Price)", "12": "340B/Disproportionate Share/Public Health Service Pricing", "13": "WAC (Wholesale Acquisition Cost)", "14": "Other Payer-Patient Responsibility Amount", "15": "Patient Pay Amount", "16": "Coupon Payment", "17": "Special Patient Reimbursement", "18": "Direct Price (DP)", "19": "State Fee Schedule (SFS) Reimbursement", "20": "National Average Drug Acquisition Cost (NADAC)", "21": "State Average Acquisition Cost (AAC)", "22": "Ingredient cost paid based on submitted Basis of Cost Free Product.", "23": "Indicates the reimbursement was based on the contracted or state fee schedule rate for the Original Manufacturer Product ID for the repackaged drug.", "24": "Federal Upper Limit (FUL)"}, "528-FS": {"BLANK": "Not Specified", "1": "Major", "2": "Moderate", "3": "Minor", "9": "Undetermined"}, "529-FT": {"0": "Not Specified", "1": "Your Pharmacy", "2": "Other Pharmacy in Same Chain", "3": "Other Pharmacy"}, "532-FW": {"1": "First DataBank", "2": "Medi-Span Product Line", "3": "Micromedex/Medical Economics", "4": "Processor <PERSON>", "5": "Other", "6": "Redbook", "7": "Multum", "Blank": "Not Specified"}, "533-FX": {"0": "Not Specified", "1": "Same Prescriber", "2": "Other Prescriber"}, "548-6F": {"Blank": "Not Specified", "001": "Generic Available", "002": "Non-Formulary Drug", "003": "Maintenance Drug", "004": "<PERSON>laim paid under plan's transition benefit period, otherwise claim would have rejected.", "005": "Claim paid under the plan's transition benefit period, otherwise claim would have rejected as prior authorization required.", "006": "Claim paid under the plan's transition benefit period, otherwise claim would have rejected as non-formulary drug.", "007": "Claim paid under the plan's transition benefit period, otherwise claim would have rejected based on plan benefit restrictions.", "008": "Claim paid under the plan's emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected.", "009": "Claim paid under the plan's emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected as prior authorization required.", "010": "Claim paid under the plan's emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected as non-formulary drug.", "011": "Claim paid under the plan's emergency supply benefit, transition benefit did not apply; otherwise claim would have rejected based on plan benefit restrictions.", "012": "<PERSON>laim paid under plan's level of care change benefit, otherwise claim would have rejected.", "013": "Claim paid under plan's level of care change benefit, otherwise claim would have rejected for prior authorization required.", "014": "<PERSON><PERSON><PERSON> paid under plan's level of care change benefit, otherwise claim would have rejected for non-formulary drug.", "015": "<PERSON>laim paid under plan's level of care change benefit, otherwise claim would have rejected due to plan benefit restrictions.", "016": "PMP Reportable Required", "017": "PMP Reporting Completed", "018": "Provide Notice: Medicare Prescription Drug Coverage and Your Rights", "019": "The Submitted Prescriber ID is inactive or expired -- Flagged for Retrospective Review", "020": "Prescriber DEA number is not found in processor's system. Paid claim flagged for retrospective.", "021": "Prescriber DEA number in the processor's system is inactive/expired. Paid claim flagged for retrospective review.", "022": "Prescriber DEA number in the processor's system does not allow for this drug DEA schedule. Paid claim flagged for retrospective review.", "023": "Prorated copayment applied based on days supply. Plan has prorated the copayment based on days supply.", "024": "The submitted Prescriber ID is Not Found - Flagged for Retrospective Review", "025": "The submitted Prescriber ID is associated to a Deceased Prescriber -- Flagged for Retrospective Review", "026": "Prescriber Type 1 NPI Required - Flagged for Retrospective Review", "027": "The submitted Prescriber DEA does not allow this drug DEA Schedule -- Flagged for Retrospective Review", "028": "Type 1 NPI Required, <PERSON><PERSON><PERSON>id Based on Plan's Prescriber NPI Data.", "029": "Grace period claim. Patient required to pay for the full cost of the prescription.", "030": "Prescriber active enrollment with Medicare Fee For Service required. Flagged for retrospective review", "031": "Pharmacy active enrollment with Medicare Fee For Service required. Flagged for retrospective review", "032": "Plan's Prescriber data base not able to verify active state license with prescriptive authority for Prescriber ID Submitted, flagged for retrospective review.", "033": "Hospice Compassionate First Initial Dispensing", "034": "Prior Authorization Approval On File", "035": "Quantity Limit Per Specific Time Period", "036": "Days Supply Limit Per Specific Time Period", "037": "Preferred Formulary Alternative Available", "038": "Preferred Network Pharmacy", "039": "Non-Preferred Network Pharmacy", "040": "Specialty Pharmacy Network Available For This Medication", "041": "Filled as a Medicare Part D Provisional Supply. Active prescriber Medicare enrollment or Other Authorized Prescriber status not found.", "042": "The submitted Prescriber NPI not found, therefore NPI active status, Medicare enrollment, prescriptive authority could not be validated. Flagged for retrospective review.", "043": "The submitted Prescriber ID could not be validated as an Other Authorized Prescriber (OAP) and is not found on the Medicare Enrollment file. Flagged for retrospective review.", "044": "Plan's Prescriber data base determined prescriptive authority criteria not met, flagged for retrospective review.", "045": "Prescriber active enrollment with Medicaid Fee For Service/MCO required. Flagged for retrospective review.", "046": "Pharmacy active enrollment with Medicaid Fee For Service/MCO required. Flagged for retrospective review."}, "552-AP": {"Blank": "Not Specified", "01": "Universal Product Code (UPC)", "02": "Health Related Item (HRI)", "03": "National Drug Code (NDC)", "04": "Health Industry Business Communications Council (HIBCC)", "11": "National Pharmaceutical Product Interface Code (NAPPI)", "12": "Global Trade Identification Number (GTIN)", "14": "Medi Span's Generic Product Identifier (GPI)", "15": "First DataBank Formulation ID (GCN)", "16": "Truven/Micromedex Generic Formulation Code (GFC)", "17": "Me<PERSON> Span's Drug Descriptor ID (DDID)", "18": "First DataBank SmartKey", "19": "Truven/Micromedex Generic Master (GM)", "28": "First DataBank Medication Name ID (FDB Med Name ID)", "29": "First DataBank Routed Medication ID (FDB Routed Med ID)", "30": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID)", "31": "First DataBank Medication ID (FDB MedID)", "32": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO)", "33": "First DataBank Ingredient List ID (HICL_SEQNO)", "37": "American Hospital Formulary Service (AHFS)", "99": "Other", "42": "Gold Standard Marketed Product Identifier (MPid)", "43": "Gold Standard Product Identifier (ProdID)", "44": "Gold Standard Specific Product Identifier (SPID)", "05": "Department of Defense (DOD)", "13": "Drug Identification Number (DIN)", "45": "Device Identifier (DI)"}, "557-AV": {"Blank": "Not Specified", "1": "Payer/Plan is Tax Exempt", "3": "Patient is Tax Exempt", "4": "Payer/Plan and Patient are Tax Exempt", "2": "Not Tax Exempt", "5": "Religious Organization", "6": "Tax Exempt Certificate"}, "561-AZ": {"Blank": "Not Specified", "02": "Ingredient Cost", "03": "Ingredient Cost + Dispensing Fee", "04": "Professional Service Fee", "01": "Gross Amount Due"}, "564-J3": {"01": "Delivery Cost", "02": "Shipping Cost", "03": "Postage Cost", "04": "Administrative Cost", "09": "Compound Preparation Cost Submitted", "11": "Medication Administration", "99": "Other"}, "568-J7": {"Blank": "Not Specified", "01": "Standard Unique Health Plan Identifier", "02": "Health Industry Number (HIN)", "03": "Issuer Identification Number (IIN)", "04": "National Association of Insurance Commissioners (NAIC)", "99": "Other", "05": "Medicare Part D Contract Number"}, "573-4V": {"01": "Quantity Dispensed", "02": "Quantity Intended To Be Dispensed", "03": "Usual and Customary/Prorated", "04": "Waived Due To Partial Fill", "99": "Other", "Blank": "Not Specified", "00": "Not Specified"}, "579-XX": {"01": "National Provider Identifier (NPI)", "02": "Blue Cross", "03": "Blue Shield", "04": "Medicare", "05": "Medicaid", "06": "UPIN (Unique Physician/Practitioner Identification Number)", "07": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)", "08": "State License", "09": "TRICARE", "10": "Health Industry Number (HIN)", "11": "Federal Tax ID", "12": "Drug Enforcement Administration (DEA) Number", "13": "State Issued", "14": "Plan Specific", "15": "HCIdea", "99": "Other"}, "581-XZ": {"01": "<PERSON><PERSON>", "02": "Service Billing"}, "586-YP": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "591-YU": {"1": "State Issued ID", "2": "Drivers License", "3": "U.S. Military ID", "4": "Passport", "5": "Alien Number (Government Permanent Residence Number)", "6": "Government Student VISA Number", "7": "Indian Tribal ID", "99": "Other"}, "593-YW": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "595-YY": {"0": "Unknown", "1": "Male", "2": "Female"}, "600-28": {"EA": "Each", "GM": "Grams", "ML": "Milliliters"}, "675-Y3": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "680-ZB": {"1": "Employee ID as determined by the employer."}, "729-TA": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "931-F8": {"D": "Days", "Y": "Years"}, "934-GC": {"DL": "Dollar Amount", "DS": "Days Supply", "FL": "Fills", "QY": "Quantity"}, "935-GF": {"CM": "Calendar Month", "CQ": "Calendar Quarter", "CY": "Calendar Year", "DY": "Days", "LT": "Lifetime", "PD": "Per Dispensing", "SP": "Specific Date Range"}, "943-GQ": {"D": "Days", "Y": "Years"}, "996-G1": {"Blank": "Not Specified", "01": "Anti-infective", "02": "Ionotropic", "03": "Chemotherapy", "04": "Pain Management", "05": "TPN/PPN (Hepatic, Renal, Pediatric) Total Parenteral Nutrition/Peripheral Parenteral Nutrition", "06": "Hydration", "07": "Ophthalmic", "99": "Other", "08": "Z0790", "09": "Z0791", "10": "Z0792", "11": "Z0793"}, "997-G2": {"Y": "Yes=CMS qualified facility", "N": "No=Not a CMS qualified facility"}, "A22-YR": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "A23-YS": {"01": "Patient", "02": "Parent", "03": "Spouse", "04": "Caregiver", "05": "Legal Guardian", "06": "Dependent", "99": "Other"}, "A24-ZK": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "A25-ZM": {"01": "National Provider Identifier (NPI)", "02": "Blue Cross", "03": "Blue Shield", "04": "Medicare", "05": "Medicaid", "06": "UPIN (Unique Physician/Practitioner Identification Number)", "07": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)", "08": "State License", "09": "TRICARE", "10": "Health Industry Number (HIN)", "11": "Federal Tax ID", "12": "Drug Enforcement Administration (DEA) Number", "13": "State Issued", "14": "Plan Specific", "15": "HCID (HCIdea)", "99": "Other"}, "A27-ZQ": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "A28-ZR": {"1": "Medicaid Title XIX", "2": "Medicare", "3": "Commercial", "4": "Workers Compensation", "5": "Self-Pay: Discount Program", "6": "Manufacturer Sponsored Patient Pay Reduction Program", "7": "Manufacturer Free Product", "8": "Veterans Health Administration (VA)", "99": "Other", "9": "Unknown", "10": "Hospice - Non Medicare", "11": "Medicaid Managed Care", "12": "Medicare Part A", "13": "Medicare Advantage", "14": "Medicare Part D PDP", "15": "Self-Pay: Cash", "16": "Medicare Part B", "17": "Indian Health Services", "18": "ADAP/<PERSON>", "19": "Black Lung", "20": "Casualty Insurance", "21": "CHIP Title XXI", "22": "Health Marketplace Exchange Qualified Health Plan", "23": "HRSA 340B Indigent Program", "24": "Independent Charity Patient Assistance Program", "25": "Manufacturer Patient Assistance Program", "26": "Medicare - Medicaid Plan (MMP)", "27": "SPAP", "28": "Tricare", "29": "Other Federal Payer", "30": "Programs of All-Inclusive Care for the Elderly (PACE)"}, "A29-ZS": {"0": "Cash", "1": "Medicaid Title XIX", "2": "Medicare", "3": "Commercial", "4": "Workers Compensation", "5": "Self-Pay: Discount Program", "6": "Manufacturer Sponsored Patient Pay Reduction Program", "7": "Manufacturer Free Product", "8": "Veterans Health Administration (VA)", "99": "Other", "9": "Unknown", "10": "Hospice - Non Medicare", "11": "Medicaid Managed Care", "12": "Medicare Part A", "13": "Medicare Advantage", "14": "Medicare Part D PDP", "15": "Self-Pay: Cash", "16": "Medicare Part B", "17": "Indian Health Services", "18": "ADAP/<PERSON>", "19": "Black Lung", "20": "Casualty Insurance", "21": "CHIP Title XXI", "22": "Health Marketplace Exchange Qualified Health Plan", "23": "HRSA 340B Indigent Program", "24": "Independent Charity Patient Assistance Program", "25": "Manufacturer Patient Assistance Program", "26": "Medicare - Medicaid Plan (MMP)", "27": "SPAP", "28": "Tricare", "30": "Programs of All-Inclusive Care for the Elderly (PACE)"}, "A45-1R": {"Y": "Yes - Prescription for non-human use.", "N": "No - Prescription for human use."}, "B45-8H": {"01": "Intermediary", "02": "Prescription Drug Monitoring Program (PDMP)", "03": "Risk Evaluation and Mitigation Strategy (REMS)", "99": "Other"}, "B46-8J": {"01": "Intermediary", "02": "Patient", "03": "Pharmacist", "04": "Prescriber", "05": "Pharmacy", "06": "Patient Representative", "99": "Other"}, "B47-8K": {"01": "Alien Number (Government Permanent Residence Number)", "02": "Driver's License Number", "03": "Drug Enforcement Administration (DEA) Number", "04": "Government Student VISA Number", "05": "Indian Tribal ID", "06": "Intermediary Authorization", "07": "Medical Record Identification Number (EHR)", "08": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number)", "09": "National Provider Identifier (NPI)", "10": "Passport ID", "11": "State Issued ID", "12": "State Issued", "13": "State License", "14": "Risk Evaluation and Mitigation Strategy (REMS) Entity ID", "15": "U.S. Military ID", "16": "Risk Evaluation and Mitigation Strategy (REMS) Authorization ID", "99": "Other"}, "B49-8N": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "B53-8S": {"1": "Intermediary Authorization", "2": "Prescription Drug Monitoring Program (PDMP)", "3": "Risk Evaluation and Mitigation Strategy (REMS) Authorization", "99": "Other Override"}, "B95-3Z": {"Blank": "Not Specified", "1": "Facility Type 2 NPI", "2": "Other"}, "C02-4P": {"3": "NDC-National Drug Code"}, "C47-9T": {"1": "Medicaid Title XIX", "2": "Medicare", "3": "Commercial", "4": "Workers Compensation", "5": "Self-Pay: Discount Program", "6": "Manufacturer Sponsored Patient Pay Reduction Program", "7": "Manufacturer Free Product", "8": "Veterans Health Administration (VA)", "9": "Unknown", "10": "Hospice - Non Medicare", "11": "Medicaid Managed Care", "12": "Medicare Part A", "13": "Medicare Advantage", "14": "Medicare Part D PDP", "15": "Self-Pay: Cash", "16": "Medicare Part B", "17": "Indian Health Services", "18": "ADAP/<PERSON>", "19": "Black Lung", "20": "Casualty Insurance", "21": "CHIP Title XXI", "22": "Health Marketplace Exchange Qualified Health Plan", "23": "HRSA 340B Indigent Program", "24": "Independent Charity Patient Assistance Program", "25": "Manufacturer Patient Assistance Program", "26": "Medicare - Medicaid Plan (MMP)", "27": "SPAP", "28": "Tricare", "29": "Other Federal Payer", "99": "Other", "30": "Programs of All-Inclusive Care for the Elderly (PACE)"}, "C48-9U": {"1": "Medicaid Title XIX", "2": "Medicare", "3": "Commercial", "4": "Workers Compensation", "5": "Self-Pay: Discount Program", "6": "Manufacturer Sponsored Patient Pay Reduction Program", "7": "Manufacturer Free Product", "8": "Veterans Health Administration (VA)", "9": "Unknown", "10": "Hospice - Non Medicare", "11": "Medicaid Managed Care", "12": "Medicare Part A", "13": "Medicare Advantage", "14": "Medicare Part D PDP", "15": "Self-Pay: Cash", "16": "Medicare Part B", "17": "Indian Health Services", "18": "ADAP/<PERSON>", "19": "Black Lung", "20": "Casualty Insurance", "21": "CHIP Title XXI", "22": "Health Marketplace Exchange Qualified Health Plan", "23": "HRSA 340B Indigent Program", "24": "Independent Charity Patient Assistance Program", "25": "Manufacturer Patient Assistance Program", "26": "Medicare - Medicaid Plan (MMP)", "27": "SPAP", "28": "Tricare", "99": "Other", "30": "Programs of All-Inclusive Care for the Elderly (PACE)"}, "C51-9X": {"1": "Medicare Part D Deductible", "2": "Medicare Part D Initial Benefit", "3": "Medicare Part D Coverage Gap (donut hole)", "4": "Medicare Part D Catastrophic Coverage", "50": "Not paid under Part D, paid under Part C benefit (for MA-PD plan)", "51": "Not paid under Part D, paid under Part C benefit (for MA-PD plan). Beneficiary is a Qualified Medicare Beneficiary - pharmacy should not attempt to collect cost-share, but instead should attempt to bill COB to Medicaid coverage", "61": "Part D drug not paid by Part D plan benefit, paid as or under a co-administered insured benefit only.", "62": "Non-Part D/non-qualified drug not paid by Part D plan benefit. Paid as or under a co-administered benefit only.", "63": "Non-Part D/non-qualified drug not paid by Part D plan benefit. Paid under Medicaid benefit only of the Medicare/Medicaid (MMP) plan.", "70": "Part D drug not paid by Part D plan benefit, paid by the beneficiary under plan-sponsored negotiated pricing.", "80": "Non-Part D/non-qualified drug not paid by Part D plan benefit, hospice benefit, or any other component of Medicare; paid by the beneficiary under plan-sponsored negotiated pricing.", "90": "Enhance or OTC drug (PDE value of E/O) not applicable to the Part D drug spend, but is covered by the Part D plan."}, "C56-AC": {"1": "Real-time", "2": "<PERSON><PERSON>"}, "C58-AE": {"1": "Flu Vaccine Benefit", "2": "90 Day At Retail"}, "C60-AG": {"1": "Low Level Complexity", "10": "Mid-Level Complexity Non-Hazardous", "20": "Mid-Level Complexity Hazardous", "30": "High Level Non-Hazardous", "40": "High Level Hazardous", "50": "High Level Non-Hazardous Sterile", "60": "High Level Hazardous Sterile"}, "C63-A5": {"NA": "Not Applicable", "0": "No End-Stage Renal Disease", "1": "End-Stage Renal Disease"}, "C66-BA": {"1": "Pharmacy Help Desk", "2": "Clinical/PA", "3": "Health Plan", "4": "Eligibility - Third Party Liability", "5": "Other"}, "C70-BF": {"1": "Telephone Number", "2": "Fax Number", "3": "URL", "4": "Other"}, "C71-BG": {"1": "Pharmacy", "2": "Prescriber", "3": "Member Services", "4": "Other Payer - N1 Reporting", "5": "Other"}, "C73-BJ": {"NA": "Not Applicable", "0": "No Institutional", "1": "Institutional", "2": "NHC - Nursing Home Certifiable", "3": "HCBS - Home and Community Based"}, "C80-G8": {"1": "Intermediary/Switch", "2": "REMS - Risk Evaluation Mitigation Strategy", "3": "PDMP - Prescription Drug Monitoring Program", "4": "Other"}, "C84-KA": {"1": "Telephone Number", "2": "Fax Number", "3": "URL", "4": "Other"}, "C85-KB": {"1": "Pharmacy", "2": "Prescriber", "3": "Member Services", "4": "Other"}, "C88-KF": {"0": "None- Not low income", "1": "High", "2": "Low", "3": "0 (zero)", "4": "15%", "5": "Unknown"}, "C90-KH": {"1": "Full quantity dispensed on date of service.", "2": "Post-consumption where date of service represents date of earliest dispensing. One or more dispensings make up the total quantity on the claim and the total quantity on the claim has been dispensed.", "3": "Pre-consumption where date of service represents date of earliest dispensing. One or more dispensings make up the total quantity on the claim, but all dispensings that make up the total quantity on the claim have not yet occurred."}, "C91-KK": {"1": "Medication dispensed in a day-supply increment equal to the billed days supply (for example: medication dispensed for a 30-day supply and billed for a 30-day supply).", "2": "7 days - dispenses medication in 7-day supplies.", "3": "4 days - dispenses medication in 4-day supplies.", "4": "3 days - dispenses medication in 3-day supplies.", "5": "2 days - dispenses medication in 2-day supplies.", "6": "1 day - dispenses medication in 1-day supplies.", "7": "4-3 days - dispenses medication in 4-day, then 3-day supplies.", "8": "2-2-3 days - dispenses medication in 2-day, then 2-day, then 3-day supplies.", "9": "Daily and 3-day weekend - dispensed daily during the week and combines multiple days dispensing for weekends.", "10": "Per shift dispensing (multiple med passes).", "11": "Per med pass dispensing.", "12": "PRN on demand.", "13": "7-day or less cycle not otherwise represented.", "14": "14 days dispensing - dispenses medication in 14-day supplies.", "15": "8-14-Day dispensing cycle not otherwise represented."}, "C95-KQ": {"1": "Amount Applied to Periodic Deductible", "2": "Amount Attributed to Product Selection/Brand Drug", "3": "Amount Attributed to Percentage Tax", "4": "Amount Exceeding Periodic Benefit Maximum", "5": "Amount of Copay", "7": "Amount of Coinsurance", "8": "Amount Attributed to Product Selection/Non-Preferred Formulary Selection", "9": "Amount Attributed to Health Plan Assistance Amount", "10": "Amount Attributed to Provider Network Selection", "11": "Amount Attributed to Product Selection/Brand Non-Preferred Formulary Selection", "12": "Amount Attributed to Coverage Gap", "13": "Amount Attributed to <PERSON><PERSON>", "14": "Amount Attributed to <PERSON>", "15": "Amount Attributed to Catastrophic Benefit", "17": "Amount Attributed to Regulatory Fee.", "16": "Amount Attributed to Unbalanced Patient Pay OPPRA"}, "C97-KS": {"COST": "1876 Cost", "EGWP": "Employer Group Waiver Plan", "LNET": "Point-of-Sale Contractor", "MAPD": "Medicare Advantage Prescription Drug Plan", "MMP": "Medicare-Medicaid Plan HMO/HMOPOS", "PACE": "National Programs of All-Inclusive Care for the Elderly", "PDP": "Medicare Prescription Drug Plan"}, "C98-KT": {"1": "<PERSON><PERSON>", "2": "Other"}, "C99-KU": {"1": "Specific Pressure Environment Not Required/Not Used", "2": "Positive Pressure Non-sterile/Non-hazardous", "3": "Negative Pressure Non-sterile/Hazardous", "4": "Positive Pressure Sterile/Non-hazardous", "5": "Negative Pressure Sterile/Hazardous"}, "D51-P7": {"1": "Other Payer/Plan Is Tax Exempt", "2": "Other Payer Religious Organization", "3": "Other Payer Tax Exempt Certificate"}, "D52-P8": {"1": "Other Payer/Plan Is Regulatory Fee Exempt", "2": "Other Payer Religious Organization", "3": "Other Payer Regulatory Fee Exempt Certificate"}, "D63-RN": {"AA": "LA RS 46:2625", "AB": "Other"}, "D62-RM": {"1": "Payer/Plan Is Regulatory Fee Exempt", "2": "Religious Organization", "3": "Regulatory Fee Exempt Certificate"}, "D61-RL": {"AA": "LA RS 46:2625", "AB": "Other"}, "D20-M2": {"MDL": "Minimum Dollar Amount", "MDS": "Minimum Day Supply", "MFL": "Minimum Fills", "MQY": "Minimum Quantity"}, "D25-M7": {"RDS": "Remaining Days Supply", "RFL": "Remaining Fills", "RQY": "Remaining Quantity"}, "D42-PV": {"1": "Tier 1", "2": "Tier 2", "3": "Tier 3", "4": "Tier 4", "5": "Tier 5", "9": "Other"}, "D43-PZ": {"1": "Step Therapy Required", "2": "Prescribed Drug Is Non-Formulary", "3": "Prescribed Drug Requires Prior Authorization", "4": "Preferred Product"}, "D45-P1": {"1": "Required Treatment, not otherwise specified", "2": "Required Treatment, minimum time period duration required", "3": "Required Treatment within specified Time Period Range"}, "D46-P2": {"1": "Days", "2": "Calendar Month", "3": "Calendar Quarters", "4": "Calendar Years", "5": "Specified Date Range"}, "D22-M4": {"1": "Injectable Therapy", "2": "Loading Quantity", "3": "Maintenance Quantity", "4": "Unbreakable Package Multiple Locations", "5": "Trial Fill", "6": "Non-Commercially Available Dose", "7": "Bundled Health Care Service"}, "D32-MS": {"1": "Dual Status Level"}, "D40-PN": {"1": "Dialysis", "2": "Medicaid"}, "D50-P6": {"BEHAVIORAL": "Behavioral Health Benefit", "DENTAL": "Dental Benefit", "DME": "Durable Medical Equipment Benefit", "MEDICAL": "Medical Benefit", "RX": "Prescription Benefit", "VISION": "Vision Benefit", "UNKNOWN": "Benefit Classification Unknown"}, "D41-PQ": {"CP": "COB Coverage Prior To Responding Payer", "CS": "COB Coverage Subsequent To Responding Payer", "MX": "Mutually Exclusive Benefits", "CC": "Change in Coverage", "RP": "Responding Payer", "CE": "Centralized Eligibility"}, "D17-K8": {"AA": "340B", "AB": "Split Billing", "AC": "Encounter"}}