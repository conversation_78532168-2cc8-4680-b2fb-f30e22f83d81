{"status": "Pending", "created_by": 7, "change_by": null, "updated_by": 7, "reviewed_by": null, "site_id": 109, "patient_id": 1833, "insurance_id": 476, "payer_id": 8, "inventory_id": 123, "paid": 1323.8, "brand_group_id": "474469", "is_test": "Yes", "expected": 1323.8, "order_id": 440, "order_item_id": 482, "copay": 1000, "claim_no": "000185", "created_by_auto_name": "<PERSON> (Clara Team)", "change_by_auto_name": null, "updated_by_auto_name": "<PERSON> (Clara Team)", "reviewed_by_auto_name": null, "order_id_auto_name": "5 - Active GAMMAGARD LIQUID 55 gram 2XW", "order_item_id_auto_name": "GAMMAGARD LIQUID 55gram 2XW", "inventory_id_auto_name": "GAMMAGARD LIQUID 10% VIAL (10 GM) Drug", "brand_group_id_auto_name": "Gammagard Liquid 10 % injection solution", "site_id_auto_name": "Branson - MO BTX", "patient_id_auto_name": "11111 <PERSON>side", "insurance_id_auto_name": "2 : ILDR - Prime-011552-ILDR-Test - OON : CMPBM", "payer_id_auto_name": "ILDR - Prime-011552-ILDR-Test - OON", "payer_type_id": "CMPBM", "patient_id_qualifier": "EA", "patient_gender_code": "2", "patient_state": null, "place_of_service": "12", "patient_residence": null, "smoker_code": null, "pregnancy_indicator": null, "reviewed_on": null, "deleted": null, "archived": null, "updated_on": "10/09/2024 13:52:12", "auto_name": "11111 Kara Whiteside B1 3 : ILDR - Prime-011552-ILDR-Test - OON : CMP<PERSON> CMPBM", "change_type": null, "change_data": null, "created_on": "10/09/2024 13:52:10", "change_on": null, "patient_claim_id": "11111", "patient_date_of_birth": "10/11/1952", "patient_first_name": "<PERSON>", "patient_last_name": "Whiteside", "employer_id": null, "patient_phone": "(*************", "patient_email_address": null, "patient_street_address": "105 Rosadi Cv", "patient_city_address": "Georgetown", "patient_zip": "78628", "payer_type_id_auto_name": "Commercial PBM", "patient_id_qualifier_auto_name": "EA - Medical Record Identification Number (EHR)", "patient_gender_code_auto_name": "2 - Female", "place_of_service_auto_name": "12 - Home", "provider_id": "**********", "physician_id": 11994, "primary_physician_id": 11994, "dr_id_qualifier": "01", "pri_dr_id_qualifier": "01", "dr_state": null, "dr_id": "**********", "dr_last_name": "ALVAREZ", "dr_first_name": "ANTONIO", "dr_phone": "**********", "pri_dr_id": "**********", "pri_dr_last_name": "ALVAREZ", "physician_id_auto_name": "********** AL<PERSON><PERSON><PERSON>, ANTONIO M.D.", "primary_physician_id_auto_name": "********** AL<PERSON><PERSON><PERSON>, ANTONIO M.D.", "dr_id_qualifier_auto_name": "01  - National Provider Identifier (NPI)", "pri_dr_id_qualifier_auto_name": "01  - National Provider Identifier (NPI)", "dr_state_auto_name": null, "elig_clar_code": null, "mcd_indicator": null, "dr_accept_indicator": null, "partd_facility": "Y", "card_holder_id": "111111111", "card_holder_first_name": "<PERSON>", "card_holder_last_name": null, "plan_id": null, "group_id": null, "person_code": null, "medigap_id": null, "mcd_id_no": null, "mcd_agcy_no": null, "partd_facility_auto_name": "Y - Yes=CMS qualified facility", "rx_svc_no_ref_qualifier": "1", "careplan_order_id": null, "careplan_order_item_id": null, "product_id": 123, "service_id": null, "prod_svc_id_qualifier": "03", "daw_code": "0", "unit_of_measure": null, "admin_route": "*********", "og_product_id": null, "og_service_id": null, "og_rx_id_qualifier": null, "pa_id": null, "pa_type_code": null, "compound_code": "1", "compound_type": null, "rx_origin_code": "4", "other_coverage_code": null, "sp_pk_indicator": null, "level_of_service": null, "dispensing_status": "0", "delay_reason_code": null, "pt_assign_indicator": "Y", "pharmacy_service_type": "1", "sched_rx_no": null, "rx_svc_no": 10000000000000100, "fill_number": 1, "quantity_dispensed": 2, "days_supply": null, "number_of_refills_authorized": 7, "date_rx_written": "10/03/2024", "quantity_prescribed": null, "day_supply": 30, "prod_svc_id": "00944270005", "billable_id": null, "rx_svc_no_ref_qualifier_auto_name": "1 - <PERSON><PERSON>", "product_id_auto_name": "GAMMAGARD LIQUID 10% VIAL (10 GM) Drug", "prod_svc_id_qualifier_auto_name": "03 - National Drug Code (NDC)", "daw_code_auto_name": "0 - No Product Selection Indicated", "admin_route_auto_name": "********* - Intravascular route", "compound_code_auto_name": "1 - Not a Compound", "rx_origin_code_auto_name": "4 - Facsimile", "pt_assign_indicator_auto_name": "Y - Patient assigns benefits", "pharmacy_service_type_auto_name": "1 - Community/Retail Pharmacy Services", "proc_mod_code": [], "proc_mod_code_auto_name": [], "sub_clar_code": [], "sub_clar_code_auto_name": [], "sales_tax_basis": "0", "cost_basis": "12", "ing_cst_sub": 142.38, "date_of_service": "11/14/2024", "bin_number": "444444", "process_control_number": "555555"}