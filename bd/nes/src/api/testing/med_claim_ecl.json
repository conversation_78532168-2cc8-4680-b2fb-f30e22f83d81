{"PAT09": {"Y": "Yes"}, "DMG03": {"M": "Male", "F": "Female", "U": "Unknown"}, "SBR01": {"P": "Primary", "S": "Secondary", "T": "Tertiary", "A": "Payer Responsibility Four", "B": "Payer Responsibility Five", "C": "Payer Responsibility Six", "D": "Payer Responsibility Seven", "E": "Payer Responsibility Eight", "F": "Payer Responsibility Nine", "G": "Payer Responsibility Ten", "H": "Payer Responsibility Eleven", "U": "Unknown"}, "SBR05": {"12": "Medicare Secondary Working Aged Beneficiary or Spouse with Employer Group Health Plan", "13": "Medicare Secondary End-Stage Renal Disease Beneficiary in the Mandated Coordination Period", "14": "Medicare Secondary, No-fault Insurance including Auto is Primary", "15": "Medicare Secondary Worker's Compensation", "16": "Medicare Secondary Public Health Service (PHS) or Other Federal Agency", "41": "Medicare Secondary Black Lung", "42": "Medicare Secondary Veteran's Administration", "43": "Medicare Secondary Disabled Beneficiary Under Age 65 with Large Group Health Plan (LGHP)", "47": "Medicare Secondary, Other Liability Insurance is Primary"}, "N402": {"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "AS": "American Samoa", "CA": "California", "CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District Of Columbia", "FM": "Federated States Of Micronesia", "FL": "Florida", "GA": "Georgia", "GU": "Guam", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa", "KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MH": "Marshall Islands", "MD": "Maryland", "MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri", "MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey", "NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "MP": "Northern Mariana Islands", "OH": "Ohio", "OK": "Oklahoma", "OR": "Oregon", "PW": "<PERSON><PERSON>", "PA": "Pennsylvania", "PR": "Puerto Rico", "RI": "Rhode Island", "SC": "South Carolina", "SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont", "VA": "Virginia", "VI": "Virgin Islands", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming", "01": "Alabama", "02": "Alaska", "03": "Arizona", "04": "Arkansas", "05": "California", "06": "Colorado", "07": "Connecticut", "08": "Delaware", "09": "District Of Columbia", "10": "Florida", "11": "Georgia", "54": "Guam", "12": "Hawaii", "13": "Idaho", "14": "Illinois", "15": "Indiana", "16": "Iowa", "17": "Kansas", "18": "Kentucky", "19": "Louisiana", "20": "Maine", "21": "Maryland", "22": "Massachusetts", "23": "Michigan", "24": "Minnesota", "25": "Mississippi", "26": "Missouri", "27": "Montana", "28": "Nebraska", "29": "Nevada", "30": "New Hampshire", "31": "New Jersey", "32": "New Mexico", "33": "New York", "34": "North Carolina", "35": "North Dakota", "36": "Ohio", "37": "Oklahoma", "38": "Oregon", "39": "Pennsylvania", "40": "Puerto Rico", "41": "Rhode Island", "42": "South Carolina", "43": "South Dakota", "44": "Tennessee", "45": "Texas", "46": "Utah", "47": "Vermont", "48": "Virginia", "53": "Virgin Islands", "49": "Washington", "50": "West Virginia", "51": "Wisconsin", "52": "Wyoming", "56": "California", "57": "Florida", "58": "New York", "59": "Texas", "AA": "Armed Forces Americas  (except Canada)", "AE": "Armed Forces Middle East", "AP": "Armed Forces Pacific", "AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick", "NL": "Newfoundland and Labrador", "NS": "Nova Scotia", "NT": "Northwest Territories", "NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec", "SK": "Saskatchewan", "YT": "Yukon", "60": "Pennsylvania"}, "SBR01-2320": {"P": "Primary", "S": "Secondary", "T": "Tertiary", "A": "Payer Responsibility Four", "B": "Payer Responsibility Five", "C": "Payer Responsibility Six", "D": "Payer Responsibility Seven", "E": "Payer Responsibility Eight", "F": "Payer Responsibility Nine"}, "SBR02": {"01": "Spouse", "18": "Self", "19": "Child", "20": "Employee", "21": "Unknown", "39": "Organ Donor", "40": "Cadaver Donor", "53": "Life Partner", "G8": "Other Relationship"}, "CARC": {"1": "Deductible Amount", "10": "The diagnosis is inconsistent with the patient's gender. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "100": "Payment made to patient/insured/responsible party/employer.", "101": "Predetermination: anticipated payment upon completion of services or claim adjudication.", "102": "Major Medical Adjustment.", "103": "Provider promotional discount (e.g., Senior citizen discount).", "104": "Managed care withholding.", "105": "Tax withholding.", "106": "Patient payment option/election not in effect.", "107": "The related or qualifying claim/service was not identified on this claim. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "108": "Rent/purchase guidelines were not met. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "109": "Claim not covered by this payer/contractor. You must send the claim to the correct payer/contractor.", "11": "The diagnosis is inconsistent with the procedure. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "110": "Billing date predates service date.", "111": "Not covered unless the provider accepts assignment.", "112": "Service not furnished directly to the patient and/or not documented.", "114": "Procedure/product not approved by the Food and Drug Administration.", "115": "Procedure postponed, canceled, or delayed.", "116": "The advance indemnification notice signed by the patient did not comply with requirements.", "117": "Transportation is only covered to the closest facility that can provide the necessary care.", "118": "ESRD network support adjustment.", "119": "Benefit maximum for this time period or occurrence has been reached.", "12": "The diagnosis is inconsistent with the provider type. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "121": "Indemnification adjustment - compensation for outstanding member responsibility.", "122": "Psychiatric reduction.", "128": "<PERSON><PERSON>'s services are covered in the mother's Allowance.", "129": "Prior processing information appears incorrect. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "13": "The date of death precedes the date of service.", "130": "Claim submission fee.", "131": "Claim specific negotiated discount.", "132": "Prearranged demonstration project adjustment.", "133": "The disposition of this claim/service is pending further review.", "134": "Technical fees removed from charges.", "135": "Interim bills cannot be processed.", "136": "Failure to follow prior payer's coverage rules. (Use Group Code OA).", "137": "Regulatory Surcharges, Assessments, Allowances or Health Related Taxes.", "139": "Contracted funding agreement - Subscriber is employed by the provider of services.", "14": "The date of birth follows the date of service.", "140": "Patient/Insured health identification number and name do not match.", "141": "Claim spans eligible and ineligible periods of coverage.", "142": "Monthly Medicaid patient liability amount.", "143": "Portion of payment deferred.", "144": "Incentive adjustment, e.g. preferred product/service.", "146": "Diagnosis was invalid for the date(s) of service reported.", "147": "Provider contracted/negotiated rate expired or not on file.", "148": "Information from another provider was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "149": "Lifetime benefit maximum has been reached for this service/benefit category.", "15": "The authorization number is missing, invalid, or does not apply to the billed services or provider.", "150": "Payer deems the information submitted does not support this level of service.", "151": "Payment adjusted because the payer deems the information submitted does not support this many/frequency of services.", "152": "Payer deems the information submitted does not support this length of service. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "153": "Payer deems the information submitted does not support this dosage.", "154": "Payer deems the information submitted does not support this day's supply.", "155": "<PERSON><PERSON> refused the service/procedure.", "157": "Service/procedure was provided as a result of an act of war.", "158": "Service/procedure was provided outside of the United States.", "159": "Service/procedure was provided as a result of terrorism.", "16": "Claim/service lacks information which is needed for adjudication. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "160": "Injury/illness was the result of an activity that is a benefit exclusion.", "161": "Provider performance bonus", "163": "Attachment referenced on the claim was not received.", "164": "Attachment referenced on the claim was not received in a timely fashion.", "166": "These services were submitted after this payers responsibility for processing claims under this plan ended.", "167": "This (these) diagnosis(es) is (are) not covered. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "168": "Service(s) have been considered under the patient's medical plan. Benefits are not available under this dental plan.", "169": "Alternate benefit has been provided.", "170": "Payment is denied when performed/billed by this type of provider. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "171": "Payment is denied when performed/billed by this type of provider in this type of facility. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "172": "Payment is adjusted when performed/billed by a provider of this specialty. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "173": "Service was not prescribed by a physician.", "174": "Service was not prescribed prior to delivery.", "175": "Prescription is incomplete.", "176": "Prescription is not current.", "177": "Patient has not met the required eligibility requirements.", "178": "Patient has not met the required spend down requirements.", "179": "Patient has not met the required waiting requirements. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "18": "Duplicate claim/service.", "180": "Patient has not met the required residency requirements.", "181": "Procedure code was invalid on the date of service.", "182": "Procedure modifier was invalid on the date of service.", "183": "The referring provider is not eligible to refer the service billed. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "184": "The prescribing/ordering provider is not eligible to prescribe/order the service billed. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "185": "The rendering provider is not eligible to perform the service billed. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "186": "Level of care change adjustment.", "187": "Consumer Spending Account payments (includes but is not limited to Flexible Spending Account, Health Savings Account, Health Reimbursement Account, etc.)", "188": "This product/procedure is only covered when used according to FDA recommendations.", "189": "'Not otherwise classified' or 'unlisted' procedure code (CPT/HCPCS) was billed when there is a specific procedure code for this procedure/service", "19": "This is a work-related injury/illness and thus the liability of the Worker's Compensation Carrier.", "190": "Payment is included in the allowance for a Skilled Nursing Facility (SNF) qualified stay.", "192": "Non standard adjustment code from paper remittance. Note: This code is to be used by providers/payers providing Coordination of Benefits information to another payer in the 837 transaction only. This code is only used when the non-standard code cannot be reasonably mapped to an existing Claims Adjustment Reason Code, specifically Deductible, Coinsurance and Co-payment.", "193": "Original payment decision is being maintained. Upon review, it was determined that this claim was processed properly.", "194": "Anesthesia performed by the operating physician, the assistant surgeon or the attending physician.", "195": "Refund issued to an erroneous priority payer for this claim/service.", "197": "Precertification/authorization/notification absent.", "198": "Precertification/authorization exceeded.", "199": "Revenue code and Procedure code do not match.", "2": "Coinsurance Amount", "20": "This injury/illness is covered by the liability carrier.", "200": "Expenses incurred during lapse in coverage", "201": "Workers Compensation case settled. Patient is responsible for amount of this claim/service through WC 'Medicare set aside arrangement' or other agreement. (Use group code PR).", "202": "Non-covered personal comfort or convenience services.", "203": "Discontinued or reduced service.", "204": "This service/equipment/drug is not covered under the patient’s current benefit plan", "205": "Pharmacy discount card processing fee", "206": "National Provider Identifier - missing.", "207": "National Provider identifier - Invalid format", "208": "National Provider Identifier - Not matched.", "209": "Per regulatory or other agreement. The provider cannot collect this amount from the patient. However, this amount may be billed to subsequent payer. Refund to patient if collected. (Use Group code OA)", "21": "This injury/illness is the liability of the no-fault carrier.", "210": "Payment adjusted because pre-certification/authorization not received in a timely fashion", "211": "National Drug Codes (NDC) not eligible for rebate, are not covered.", "212": "Administrative surcharges are not covered", "213": "Non-compliance with the physician self referral prohibition legislation or payer policy.", "215": "Based on subrogation of a third party settlement", "216": "Based on the findings of a review organization", "217": "Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. (Note: To be used for Workers' Compensation only)", "218": "Based on entitlement to benefits (Note: To be used for Workers' Compensation only) This change effective 7/1/2011: Based on entitlement to benefits. Note: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Workers' Compensation only", "219": "Based on extent of injury (Note: To be used for Workers' Compensation only) This change effective 7/1/2011: Based on extent of injury. Note: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF).", "22": "This care may be covered by another payer per coordination of benefits.", "220": "The applicable fee schedule does not contain the billed code. Please resubmit a bill with the appropriate fee schedule code(s) that best describe the service(s) provided and supporting documentation if required. (Note: To be used for Workers' Compensation only)", "222": "Exceeds the contracted maximum number of hours/days/units by this provider for this period. This is not patient specific. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "223": "Adjustment code for mandated federal, state or local law/regulation that is not already covered by another code and is mandated before a new code can be created.", "224": "Patient identification compromised by identity theft. Identity verification required for processing this and future claims.", "225": "Penalty or Interest Payment by Payer (Only used for plan to plan encounter reporting within the 837)", "226": "Information requested from the Billing/Rendering Provider was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "227": "Information requested from the patient/insured/responsible party was not provided or was insufficient/incomplete. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "228": "Denied for failure of this provider, another provider or the subscriber to supply requested information to a previous payer for their adjudication", "229": "Partial charge amount not considered by Medicare due to the initial claim Type of Bill being 12X. Note: This code can only be used in the 837 transaction to convey Coordination of Benefits information when the secondary payer’s cost avoidance policy allows providers to bypass claim submission to a prior payer. Use Group Code PR.", "23": "The impact of prior payer(s) adjudication including payments and/or adjustments.", "230": "No available or correlating CPT/HCPCS code to describe this service. Note: Used only by Property and Casualty.", "231": "Mutually exclusive procedures cannot be done in the same day/setting. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "232": "Institutional Transfer Amount. Note - Applies to institutional claims only and explains the DRG amount difference when the patient care crosses multiple institutions.", "233": "Services/charges related to the treatment of a hospital-acquired condition or preventable medical error.", "234": "This procedure is not paid separately. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "235": "Sales Tax", "236": "This procedure or procedure/modifier combination is not compatible with another procedure or procedure/modifier combination provided on the same day according to the National Correct Coding Initiative.", "237": "Notes: This code replaces deactivated code 214", "238": "Claim spans eligible and ineligible periods of coverage, this is the reduction for the ineligible period. (Use only with Group Code PR)", "239": "Claim spans eligible and ineligible periods of coverage. Rebill separate claims.", "24": "Charges are covered under a capitation agreement/managed care plan.", "240": "The diagnosis is inconsistent with the patient's birth weight. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "241": "Low Income Subsidy (LIS) Co-payment Amount", "242": "Services not provided by network/primary care providers. Notes: This code replaces deactivated code 38", "243": "Services not authorized by network/primary care providers. Notes: This code replaces deactivated code 38", "244": "Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property & Casualty only. Notes: Use code P10", "245": "Provider performance program withhold.", "246": "This non-payable code is for required reporting only.", "247": "Deductible for Professional service rendered in an Institutional setting and billed on an Institutional claim. Notes: For Medicare Bundled Payment use only, under the Patient Protection and Affordable Care Act (PPACA).", "248": "Coinsurance for Professional service rendered in an Institutional setting and billed on an Institutional claim. Notes: For Medicare Bundled Payment use only, under the Patient Protection and Affordable Care Act (PPACA).", "249": "This claim has been identified as a readmission. (Use only with Group Code CO)", "250": "The attachment/other documentation that was received was the incorrect attachment/document. The expected attachment/document is still missing. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT).", "251": "The attachment/other documentation that was received was incomplete or deficient. The necessary information is still needed to process the claim. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT).", "252": "An attachment/other documentation is required to adjudicate this claim/service. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT).", "253": "Sequestration – reduction in federal payment", "254": "Claim received by the dental plan, but benefits not available under this plan. Submit these services to the patient's medical plan for further consideration. Notes: Use CARC 290 if the claim was forwarded", "256": "Service not payable per managed care contract.", "257": "The disposition of the claim/service is undetermined during the premium payment grace period, per Health Insurance Exchange requirements. This claim/service will be reversed and corrected when the grace period ends (due to premium payment or lack of premium payment). (Use only with Group Code OA) Notes: To be used after the first month of the grace period.", "258": "Claim/service not covered when patient is in custody/incarcerated. Applicable federal, state or local authority may cover the claim/service.", "259": "Additional payment for Dental/Vision service utilization.", "26": "Expenses incurred prior to coverage.", "260": "Processed under Medicaid ACA Enhanced Fee Schedule", "261": "The procedure or service is inconsistent with the patient's history.", "262": "Adjustment for delivery cost. Usage: To be used for pharmaceuticals only.", "263": "Adjustment for shipping cost. Usage: To be used for pharmaceuticals only.", "264": "Adjustment for postage cost. Usage: To be used for pharmaceuticals only.", "265": "Adjustment for administrative cost. Usage: To be used for pharmaceuticals only.", "266": "Adjustment for compound preparation cost. Usage: To be used for pharmaceuticals only.", "267": "Claim/service spans multiple months. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "268": "The Claim spans two calendar years. Please resubmit one claim per calendar year.", "269": "Anesthesia not covered for this service/procedure. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "27": "Expenses incurred after coverage terminated.", "270": "Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patient’s dental plan for further consideration. Notes: Use CARC 291 if the claim was forwarded.", "271": "Prior contractual reductions related to a current periodic payment as part of a contractual payment schedule when deferred amounts have been previously reported. (Use only with Group Code OA)", "272": "Coverage/program guidelines were not met.", "273": "Coverage/program guidelines were exceeded.", "274": "Fee/Service not payable per patient Care Coordination arrangement.", "275": "Prior payer's (or payers') patient responsibility (deductible, coinsurance, co-payment) not covered. (Use only with Group Code PR)", "276": "Services denied by the prior payer(s) are not covered by this payer.", "277": "The disposition of the claim/service is undetermined during the premium payment grace period, per Health Insurance SHOP Exchange requirements. This claim/service will be reversed and corrected when the grace period ends (due to premium payment or lack of premium payment). (Use only with Group Code OA) Notes: To be used during 31 day SHOP grace period.", "278": "Performance program proficiency requirements not met. (Use only with Group Codes CO or PI) Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "279": "Services not provided by Preferred network providers. Usage: Use this code when there are member network limitations. For example, using contracted providers not in the member's 'narrow' network.", "280": "Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patient's Pharmacy plan for further consideration. Notes: Use CARC 292 if the claim was forwarded.", "281": "Deductible waived per contractual agreement. Use only with Group Code CO.", "282": "The procedure/revenue code is inconsistent with the type of bill. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "283": "Attending provider is not eligible to provide direction of care.", "284": "Precertification/authorization/notification/pre-treatment number may be valid but does not apply to the billed services.", "285": "Appeal procedures not followed", "286": "Appeal time limits not met", "287": "Referral exceeded", "288": "Referral absent", "289": "Services considered under the dental and medical plans, benefits not available. Notes: Also see CARCs 254, 270 and 280.", "29": "The time limit for filing has expired.", "290": "Claim received by the dental plan, but benefits not available under this plan. Claim has been forwarded to the patient's medical plan for further consideration. Notes: Use CARC 254 if the claim was not forwarded.", "291": "Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patient's dental plan for further consideration. Notes: Use CARC 270 if the claim was not forwarded.", "292": "Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patient's pharmacy plan for further consideration. Notes: Use CARC 280 if the claim was not forwarded.", "293": "Payment made to employer.", "294": "Payment made to attorney.", "295": "Pharmacy Direct/Indirect Remuneration (DIR)", "296": "Precertification/authorization/notification/pre-treatment number may be valid but does not apply to the provider.", "297": "Claim received by the medical plan, but benefits not available under this plan. Submit these services to the patient's vision plan for further consideration.", "298": "Claim received by the medical plan, but benefits not available under this plan. Claim has been forwarded to the patient's vision plan for further consideration.", "299": "The billing provider is not eligible to receive payment for the service billed.", "3": "Co-payment Amount", "300": "Claim received by the Medical Plan, but benefits not available under this plan. Claim has been forwarded to the patient's Behavioral Health Plan for further consideration.", "301": "Claim received by the Medical Plan, but benefits not available under this plan. Submit these services to the patient's Behavioral Health Plan for further consideration.", "302": "Precertification/notification/authorization/pre-treatment time limit has expired.", "31": "Patient cannot be identified as our insured.", "32": "Our records indicate that this dependent is not an eligible dependent as defined.", "33": "Insured has no dependent coverage.", "34": "Insured has no coverage for newborns.", "35": "Lifetime benefit maximum has been reached.", "39": "Services denied at the time authorization/pre-certification was requested.", "4": "The procedure code is inconsistent with the modifier used or a required modifier is missing. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "40": "Charges do not meet qualifications for emergent/urgent care. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "44": "Prompt-pay discount.", "45": "Charge exceeds fee schedule/maximum allowable or contracted/legislated fee arrangement. (Use Group Codes PR or CO depending upon liability).", "49": "These are non-covered services because this is a routine exam or screening procedure done in conjunction with a routine exam. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "5": "The procedure code/bill type is inconsistent with the place of service. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "50": "These are non-covered services because this is not deemed a 'medical necessity' by the payer. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "51": "These are non-covered services because this is a pre-existing condition. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "53": "Services by an immediate relative or a member of the same household are not covered.", "54": "Multiple physicians/assistants are not covered in this case. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "55": "Procedure/treatment is deemed experimental/investigational by the payer. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "56": "Procedure/treatment has not been deemed 'proven to be effective' by the payer. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "58": "Treatment was deemed by the payer to have been rendered in an inappropriate or invalid place of service. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "59": "Processed based on multiple or concurrent procedure rules. (For example multiple surgery or diagnostic imaging, concurrent anesthesia.) Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "6": "The procedure/revenue code is inconsistent with the patient's age. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "60": "Charges for outpatient services are not covered when performed within a period of time prior to or after inpatient services.", "61": "Penalty for failure to obtain second surgical opinion. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "66": "Blood Deductible.", "69": "Day outlier amount.", "7": "The procedure/revenue code is inconsistent with the patient's gender. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "70": "Cost outlier - Adjustment to compensate for additional costs.", "74": "Indirect Medical Education Adjustment.", "75": "Direct Medical Education Adjustment.", "76": "Disproportionate Share Adjustment.", "78": "Non-Covered days/Room charge adjustment.", "8": "The procedure code is inconsistent with the provider type/specialty (taxonomy). Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "85": "Patient Interest Adjustment (Use Only Group code PR)", "89": "Professional fees removed from charges.", "9": "The diagnosis is inconsistent with the patient's age. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present.", "90": "Ingredient cost adjustment. Note: To be used for pharmaceuticals only.", "91": "Dispensing fee adjustment.", "94": "Processed in Excess of charges.", "95": "Plan procedures not followed.", "96": "Non-covered charge(s). At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.) Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "97": "The benefit for this service is included in the payment/allowance for another service/procedure that has already been adjudicated. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "A0": "Patient refund amount.", "A1": "Claim/Service denied. At least one Remark Code must be provided (may be comprised of either the NCPDP Reject Reason Code, or Remittance Advice Remark Code that is not an ALERT.)", "A5": "Medicare Claim PPS Capital Cost Outlier Amount.", "A6": "Prior hospitalization or 30 day transfer requirement not met.", "A8": "Ungroupable DRG.", "B1": "Non-covered visits.", "B10": "Allowed amount has been reduced because a component of the basic procedure/test was paid. The beneficiary is not liable for more than the charge limit for the basic procedure/test.", "B11": "The claim/service has been transferred to the proper payer/processor for processing. Claim/service not covered by this payer/processor.", "B12": "Services not documented in patients' medical records.", "B13": "Previously paid. Payment for this claim/service may have been provided in a previous payment.", "B14": "Only one visit or consultation per physician per day is covered.", "B15": "This service/procedure requires that a qualifying service/procedure be received and covered. The qualifying other service/procedure has not been received/adjudicated. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "B16": "'New Patient' qualifications were not met.", "B20": "Procedure/service was partially or fully furnished by another provider.", "B22": "This payment is adjusted based on the diagnosis.", "B23": "Procedure billed is not authorized per your Clinical Laboratory Improvement Amendment (CLIA) proficiency test.", "B4": "Late filing penalty.", "B7": "This provider was not certified/eligible to be paid for this procedure/service on this date of service. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "B8": "Alternative services were available, and should have been utilized. Note: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information  REF), if present.", "B9": "<PERSON><PERSON> is enrolled in a Hospice.", "P1": "State-mandated Requirement for Property and Casualty, see Claim Payment Remarks Code for specific explanation. To be used for Property and Casualty only.Notes: This code replaces deactivated code 162", "P10": "Payment reduced to zero due to litigation. Additional information will be sent following the conclusion of litigation. To be used for Property and Casualty only. Notes: This code replaces deactivated code 244", "P11": "The disposition of the related Property & Casualty claim (injury or illness) is pending due to litigation. To be used for Property and Casualty only. (Use only with Group Code OA) Notes: This code replaces deactivated code 255", "P12": "Workers' compensation jurisdictional fee schedule adjustment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Workers' Compensation only. Notes: This code replaces deactivated code W1", "P13": "Payment reduced or denied based on workers' compensation jurisdictional regulations or payment policies, use only if no other code is applicable. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Workers' Compensation only.Notes: This code replaces deactivated code W2", "P14": "The Benefit for this Service is included in the payment/allowance for another service/procedure that has been performed on the same day. Usage: Refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information REF), if present. To be used for Property and Casualty only. Notes: This code replaces deactivated code W3", "P15": "Workers' Compensation Medical Treatment Guideline Adjustment. To be used for Workers' Compensation only. Notes: This code replaces deactivated code W4", "P16": "Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction. To be used for Workers' Compensation only. (Use with Group Code CO or OA) Notes: This code replaces deactivated code W4", "P17": "Referral not authorized by attending physician per regulatory requirement. To be used for Property and Casualty only.Notes: This code replaces deactivated code W6", "P18": "Procedure is not listed in the jurisdiction fee schedule. An allowance has been made for a comparable service. To be used for Property and Casualty only. Notes: This code replaces deactivated code W7", "P19": "Procedure has a relative value of zero in the jurisdiction fee schedule, therefore no payment is due. To be used for Property and Casualty only. Notes: This code replaces deactivated code W8", "P2": "Not a work related injury/illness and thus not the liability of the workers' compensation carrier Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Workers' Compensation only. Notes: This code replaces deactivated code 191", "P20": "Service not paid under jurisdiction allowed outpatient facility fee schedule. To be used for Property and Casualty only. Notes: This code replaces deactivated code W9", "P21": "Payment denied based on the Medical Payments Coverage (MPC) and/or Personal Injury Protection (PIP) Benefits jurisdictional regulations, or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only. Notes: This code replaces deactivated code Y1", "P22": "Payment adjusted based on the Medical Payments Coverage (MPC) and/or Personal Injury Protection (PIP) Benefits jurisdictional regulations, or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only. Notes: This code replaces deactivated code Y2", "P23": "Medical Payments Coverage (MPC) or Personal Injury Protection (PIP) Benefits jurisdictional fee schedule adjustment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only. Notes: This code replaces deactivated code Y3", "P24": "Payment adjusted based on Preferred Provider Organization (PPO). Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty only. Use only with Group Code CO.", "P25": "Payment adjusted based on Medical Provider Network (MPN). Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty only. (Use only with Group Code CO).", "P26": "Payment adjusted based on Voluntary Provider network (VPN). Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty only. (Use only with Group Code CO).", "P27": "Payment denied based on the Liability Coverage Benefits jurisdictional regulations and/or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only.", "P28": "Payment adjusted based on the Liability Coverage Benefits jurisdictional regulations and/or payment policies. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') if the jurisdictional regulation applies. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only.", "P29": "Liability Benefits jurisdictional fee schedule adjustment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Class of Contract Code Identification Segment (Loop 2100 Other Claim Related Information REF). If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF) if the regulations apply. To be used for Property and Casualty Auto only.", "P3": "Workers' Compensation case settled. Pat<PERSON> is responsible for amount of this claim/service through WC 'Medicare set aside arrangement' or other agreement. To be used for Workers' Compensation only. (Use only with Group Code PR) Notes: This code replaces deactivated code 201", "P30": "Payment denied for exacerbation when supporting documentation was not complete. To be used for Property and Casualty only.", "P31": "Payment denied for exacerbation when treatment exceeds time allowed. To be used for Property and Casualty only.", "P4": "Workers' Compensation claim adjudicated as non-compensable. This Payer not liable for claim or service/treatment. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Workers' Compensation only Notes: This code replaces deactivated code 214", "P5": "Based on payer reasonable and customary fees. No maximum allowable defined by legislated fee arrangement. To be used for Property and Casualty only.Notes: This code replaces deactivated code 217", "P6": "Based on entitlement to benefits. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Property and Casualty only.Notes: This code replaces deactivated code 218", "P7": "The applicable fee schedule/fee database does not contain the billed code. Please resubmit a bill with the appropriate fee schedule/fee database code(s) that best describe the service(s) provided and supporting documentation if required. To be used for Property and Casualty only. Notes: This code replaces deactivated code 220", "P8": "Claim is under investigation. Usage: If adjustment is at the Claim Level, the payer must send and the provider should refer to the 835 Insurance Policy Number Segment (Loop 2100 Other Claim Related Information REF qualifier 'IG') for the jurisdictional regulation. If adjustment is at the Line Level, the payer must send and the provider should refer to the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment information REF). To be used for Property and Casualty only.Notes: This code replaces deactivated code 221", "P9": "No available or correlating CPT/HCPCS code to describe this service. To be used for Property and Casualty only.Notes: This code replaces deactivated code 230"}, "RARC": {"M50": "Missing/incomplete/invalid revenue code(s).", "M51": "Missing/incomplete/invalid procedure code(s).", "M52": "Missing/incomplete/invalid 'from' date(s) of service.", "M53": "Missing/incomplete/invalid days or units of service.", "M54": "Missing/incomplete/invalid total charges.", "M55": "We do not pay for self-administered anti-emetic drugs that are not administered with a covered oral anticancer drug.", "M56": "Missing/incomplete/invalid payer identifier.", "M59": "Missing/incomplete/invalid 'to' date(s) of service.", "M60": "Missing Certificate of Medical Necessity.", "M61": "We cannot pay for this as the approval period for the FDA clinical trial has expired.", "M62": "Missing/incomplete/invalid treatment authorization code.", "M64": "Missing/incomplete/invalid other diagnosis.", "M65": "One interpreting physician charge can be submitted per claim when a purchased diagnostic test is indicated. Please submit a separate claim for each interpreting physician.", "M66": "Our records indicate that you billed diagnostic tests subject to price limitations and the procedure code submitted includes a professional component. Only the technical component is subject to price limitations. Please submit the technical and professional components of this service as separate line items.", "M67": "Missing/incomplete/invalid other procedure code(s).", "M69": "Paid at the regular rate as you did not submit documentation to justify the modified procedure code.", "M70": "Alert: The NDC code submitted for this service was translated to a HCPCS code for processing, but please continue to submit the NDC on future claims for this item.", "M71": "Total payment reduced due to overlap of tests billed.", "M73": "The HPSA/Physician Scarcity bonus can only be paid on the professional component of this service. Rebill as separate professional and technical components.", "M74": "This service does not qualify for a HPSA/Physician Scarcity bonus payment.", "M75": "Multiple automated multichannel tests performed on the same day combined for payment.", "M76": "Missing/incomplete/invalid diagnosis or condition.", "M77": "Missing/incomplete/invalid/inappropriate place of service.", "M79": "Missing/incomplete/invalid charge.", "M80": "Not covered when performed during the same session/date as a previously processed service for the patient.", "M81": "You are required to code to the highest level of specificity.", "M82": "Service is not covered when patient is under age 50.", "M83": "Service is not covered unless the patient is classified as at high risk.", "M84": "Medical code sets used must be the codes in effect at the time of service.", "M85": "Subjected to review of physician evaluation and management services.", "M86": "Service denied because payment already made for same/similar procedure within set time frame.", "M87": "Claim/service(s) subjected to CFO-CAP prepayment review.", "M89": "Not covered more than once under age 40.", "M90": "Not covered more than once in a 12 month period.", "M91": "Lab procedures with different CLIA certification numbers must be billed on separate claims.", "M93": "Information supplied supports a break in therapy. A new capped rental period began with delivery of this equipment.", "M94": "Information supplied does not support a break in therapy. A new capped rental period will not begin.", "M95": "Services subjected to Home Health Initiative medical review/cost report audit.", "M96": "The technical component of a service furnished to an inpatient may only be billed by that inpatient facility. You must contact the inpatient facility for technical component reimbursement. If not already billed, you should bill us for the professional component only.", "M97": "Not paid to practitioner when provided to patient in this place of service. Payment included in the reimbursement issued the facility.", "M99": "Missing/incomplete/invalid Universal Product Number/Serial Number.", "M100": "We do not pay for an oral anti-emetic drug that is not administered for use immediately before, at, or within 48 hours of administration of a covered chemotherapy drug.", "M102": "Service not performed on equipment approved by the FDA for this purpose.", "M103": "Information supplied supports a break in therapy. However, the medical information we have for this patient does not support the need for this item as billed. We have approved payment for this item at a reduced level, and a new capped rental period will begin with the delivery of this equipment.", "M104": "Information supplied supports a break in therapy. A new capped rental period will begin with delivery of the equipment. This is the maximum approved under the fee schedule for this item or service.", "M105": "Information supplied does not support a break in therapy. The medical information we have for this patient does not support the need for this item as billed. We have approved payment for this item at a reduced level, and a new capped rental period will not begin.", "M107": "Payment reduced as 90-day rolling average hematocrit for ESRD patient exceeded 36.5%.", "M109": "We have provided you with a bundled payment for a teleconsultation. You must send 25 percent of the teleconsultation payment to the referring practitioner.", "M111": "We do not pay for chiropractic manipulative treatment when the patient refuses to have an x-ray taken.", "M112": "Reimbursement for this item is based on the single payment amount required under the DMEPOS Competitive Bidding Program for the area where the patient resides.", "M113": "Our records indicate that this patient began using this item/service prior to the current contract period for the DMEPOS Competitive Bidding Program.", "M114": "This service was processed in accordance with rules and guidelines under the DMEPOS Competitive Bidding Program or a Demonstration Project. For more information regarding these projects, contact your local contractor.", "M115": "This item is denied when provided to this patient by a non-contract or non-demonstration supplier.", "M116": "Processed under a demonstration project or program. Project or program is ending and additional services may not be paid under this project or program.", "M117": "Not covered unless submitted via electronic claim.", "M119": "Missing/incomplete/invalid/ deactivated/withdrawn National Drug Code (NDC).", "M121": "We pay for this service only when performed with a covered cryosurgical ablation.", "M122": "Missing/incomplete/invalid level of subluxation.", "M123": "Missing/incomplete/invalid name, strength, or dosage of the drug furnished.", "M124": "Missing indication of whether the patient owns the equipment that requires the part or supply.", "M125": "Missing/incomplete/invalid information on the period of time for which the service/supply/equipment will be needed.", "M126": "Missing/incomplete/invalid individual lab codes included in the test.", "M127": "Missing patient medical record for this service.", "M129": "Missing/incomplete/invalid indicator of x-ray availability for review.", "M130": "Missing invoice or statement certifying the actual cost of the lens, less discounts, and/or the type of intraocular lens used.", "M131": "Missing physician financial relationship form.", "M132": "Missing pacemaker registration form.", "M133": "<PERSON><PERSON><PERSON> did not identify who performed the purchased diagnostic test or the amount you were charged for the test.", "M134": "Performed by a facility/supplier in which the provider has a financial interest.", "M135": "Missing/incomplete/invalid plan of treatment.", "M136": "Missing/incomplete/invalid indication that the service was supervised or evaluated by a physician.", "M137": "Part B coinsurance under a demonstration project or pilot program.", "M138": "Patient identified as a demonstration participant but the patient was not enrolled in the demonstration at the time services were rendered. Coverage is limited to demonstration participants.", "M139": "Denied services exceed the coverage limit for the demonstration.", "M141": "Missing physician certified plan of care.", "M142": "Missing American Diabetes Association Certificate of Recognition.", "M143": "The provider must update license information with the payer.", "M144": "Pre-/post-operative care payment is included in the allowance for the surgery/procedure. MA01 Alert: If you do not agree with what we approved for these services, you may appeal our decision. To make sure that we are fair to you, we require another individual that did not process your initial claim to conduct the appeal. However, in order to be eligible for an appeal, you must write to us within 120 days of the date you received this notice, unless you have a good reason for being late. MA02 Alert: If you do not agree with this determination, you have the right to appeal. You must file a written request for an appeal within 180 days of the date you receive this notice. MA04 Secondary payment cannot be considered without the identity of or payment information from the primary payer. The information was either not reported or was illegible. MA07 Alert: The claim information has also been forwarded to Medicaid for review. MA08 Alert: Claim information was not forwarded because the supplemental coverage is not with a Medigap plan, or you do not participate in Medicare. MA09 Alert: Claim submitted as unassigned but processed as assigned in accordance with our current assignment/participation agreement. MA10 Alert: The patient's payment was in excess of the amount owed. You must refund the overpayment to the patient. MA12 You have not established that you have the right under the law to bill for services furnished by the person(s) that furnished this (these) service(s). MA13 Alert: You may be subject to penalties if you bill the patient for amounts not reported with the PR (patient responsibility) group code. MA14 Alert: The patient is a member of an employer-sponsored prepaid health plan. Services from outside that health plan are not covered. However, as you were not previously notified of this, we are paying this time. In the future, we will not pay you for non-plan services. MA15 Alert: Your claim has been separated to expedite handling. You will receive a separate notice for the other services reported. MA16 The patient is covered by the Black Lung Program. Send this claim to the Department of Labor, Federal Black Lung Program, P.O. Box 828, Lanham-Seabrook MD 20703. MA17 We are the primary payer and have paid at the primary rate. You must contact the patient's other insurer to refund any excess it may have paid due to its erroneous primary payment. MA18 Alert: The claim information is also being forwarded to the patient's supplemental insurer. Send any questions regarding supplemental benefits to them. MA19 Alert: Information was not sent to the Medigap insurer due to incorrect/invalid information you submitted concerning that insurer. Please verify your information and submit your secondary claim directly to that insurer. MA20 Skilled Nursing Facility (SNF) stay not covered when care is primarily related to the use of an urethral catheter for convenience or the control of incontinence. MA21 SSA records indicate mismatch with name and sex. MA22 Payment of less than $1.00 suppressed. MA23 Demand bill approved as result of medical review. MA24 Christian Science Sanitarium/ Skilled Nursing Facility (SNF) bill in the same benefit period. MA25 A patient may not elect to change a hospice provider more than once in a benefit period. MA26 Alert: Our records indicate that you were previously informed of this rule. MA27 Missing/incomplete/invalid entitlement number or name shown on the claim. MA28 Alert: Receipt of this notice by a physician or supplier who did not accept assignment is for information only and does not make the physician or supplier a party to the determination. No additional rights to appeal this decision, above those rights already provided for by regulation/instruction, are conferred by receipt of this notice. MA30 Missing/incomplete/invalid type of bill. MA31 Missing/incomplete/invalid beginning and ending dates of the period billed. MA32 Missing/incomplete/invalid number of covered days during the billing period. MA33 Missing/incomplete/invalid non-covered days during the billing period. MA34 Missing/incomplete/invalid number of coinsurance days during the billing period. MA35 Missing/incomplete/invalid number of lifetime reserve days. MA36 Missing/incomplete/invalid patient name. MA37 Missing/incomplete/invalid patient's address. MA39 Missing/incomplete/invalid gender. MA40 Missing/incomplete/invalid admission date. MA41 Missing/incomplete/invalid admission type. MA42 Missing/incomplete/invalid admission source. MA43 Missing/incomplete/invalid patient status. MA44 Alert: No appeal rights. Adjudicative decision based on law. MA45 Alert: As previously advised, a portion or all of your payment is being held in a special account. MA46 Alert: The new information was considered but additional payment will not be issued. MA47 Our records show you have opted out of Medicare, agreeing with the patient not to bill Medicare for services/tests/supplies furnished. As result, we cannot pay this claim. The patient is responsible for payment. MA48 Missing/incomplete/invalid name or address of responsible party or primary payer. MA50 Missing/incomplete/invalid Investigational Device Exemption number or Clinical Trial number. MA53 Missing/incomplete/invalid Competitive Bidding Demonstration Project identification. MA54 Physician certification or election consent for hospice care not received timely. MA55 Not covered as patient received medical health care services, automatically revoking his/her election to receive religious non-medical health care services. MA56 Our records show you have opted out of Medicare, agreeing with the patient not to bill Medicare for services/tests/supplies furnished. As result, we cannot pay this claim. The patient is responsible for payment, but under Federal law, you cannot charge the patient more than the limiting charge amount. MA57 Patient submitted written request to revoke his/her election for religious non-medical health care services. MA58 Missing/incomplete/invalid release of information indicator. MA59 Alert: The patient overpaid you for these services. You must issue the patient a refund within 30 days for the difference between his/her payment and the total amount shown as patient responsibility on this notice. MA60 Missing/incomplete/invalid patient relationship to insured. MA61 Missing/incomplete/invalid social security number. MA62 Alert: This is a telephone review decision. MA63 Missing/incomplete/invalid principal diagnosis. MA64 Our records indicate that we should be the third payer for this claim. We cannot process this claim until we have received payment information from the primary and secondary payers. MA65 Missing/incomplete/invalid admitting diagnosis. MA66 Missing/incomplete/invalid principal procedure code. MA67 Alert: Correction to a prior claim. MA68 Alert: We did not crossover this claim because the secondary insurance information on the claim was incomplete. Please supply complete information or use the PLANID of the insurer to assure correct and timely routing of the claim. MA69 Missing/incomplete/invalid remarks. MA70 Missing/incomplete/invalid provider representative signature. MA71 Missing/incomplete/invalid provider representative signature date. MA72 Alert: The patient overpaid you for these assigned services. You must issue the patient a refund within 30 days for the difference between his/her payment to you and the total of the amount shown as patient responsibility and as paid to the patient on this notice. MA73 Informational remittance associated with a Medicare demonstration. No payment issued under fee-for-service Medicare as patient has elected managed care. MA74 Alert: This payment replaces an earlier payment for this claim that was either lost, damaged or returned. MA75 Missing/incomplete/invalid patient or authorized representative signature. MA76 Missing/incomplete/invalid provider identifier for home health agency or hospice when physician is performing care plan oversight services. MA77 Alert: The patient overpaid you. You must issue the patient a refund within 30 days for the difference between the patient's payment less the total of our and other payer payments and the amount shown as patient responsibility on this notice. MA79 Billed in excess of interim rate. MA80 Informational notice. No payment issued for this claim with this notice. Payment issued to the hospital by its intermediary for all services for this encounter under a demonstration project. MA81 Missing/incomplete/invalid provider/supplier signature. MA83 Did not indicate whether we are the primary or secondary payer. MA84 Patient identified as participating in the National Emphysema Treatment Trial but our records indicate that this patient is either not a participant, or has not yet been approved for this phase of the study. Contact Johns Hopkins University, the study coordinator, to resolve if there was a discrepancy. MA88 Missing/incomplete/invalid insured's address and/or telephone number for the primary payer. MA89 Missing/incomplete/invalid patient's relationship to the insured for the primary payer. MA90 Missing/incomplete/invalid employment status code for the primary insured. MA91 Alert: This determination is the result of the appeal you filed. MA92 Missing plan information for other insurance. MA93 Non-PIP (Periodic Interim Payment) claim. MA94 Did not enter the statement 'Attending physician not hospice employee' on the claim form to certify that the rendering physician is not an employee of the hospice. MA96 Claim rejected. Coded as a Medicare Managed Care Demonstration but patient is not enrolled in a Medicare managed care plan. MA97 Missing/incomplete/invalid Medicare Managed Care Demonstration contract number or clinical trial registry number. MA99 Missing/incomplete/invalid Medigap information. MA100 Missing/incomplete/invalid date of current illness or symptoms. MA103 Hemophilia Add On. MA106 PIP (Periodic Interim Payment) claim. MA107 Paper claim contains more than three separate data items in field 19. MA108 Paper claim contains more than one data item in field 23. MA109 Claim processed in accordance with ambulatory surgical guidelines. MA110 Missing/incomplete/invalid information on whether the diagnostic test(s) were performed by an outside entity or if no purchased tests are included on the claim. MA111 Missing/incomplete/invalid purchase price of the test(s) and/or the performing laboratory's name and address. MA112 Missing/incomplete/invalid group practice information. MA113 Incomplete/invalid taxpayer identification number (TIN) submitted by you per the Internal Revenue Service. Your claims cannot be processed without your correct TIN, and you may not bill the patient pending correction of your TIN. There are no appeal rights for unprocessable claims, but you may resubmit this claim after you have notified this office of your correct TIN. MA114 Missing/incomplete/invalid information on where the services were furnished. MA115 Missing/incomplete/invalid physical location (name and address, or PIN) where the service(s) were rendered in a Health Professional Shortage Area (HPSA). MA116 Did not complete the statement 'Homebound' on the claim to validate whether laboratory services were performed at home or in an institution. MA117 This claim has been assessed a $1.00 user fee. MA118 Alert: No Medicare payment issued for this claim for services or supplies furnished to a Medicare-eligible veteran through a facility of the Department of Veterans Affairs. Coinsurance and/or deductible are applicable. MA120 Missing/incomplete/invalid CLIA certification number. MA121 Missing/incomplete/invalid x-ray date. MA122 Missing/incomplete/invalid initial treatment date. MA123 Your center was not selected to participate in this study, therefore, we cannot pay for these services. MA125 Per legislation governing this program, payment constitutes payment in full. MA126 Pancreas transplant not covered unless kidney transplant performed. MA128 Missing/incomplete/invalid FDA approval number. MA130 Your claim contains incomplete and/or invalid information, and no appeal rights are afforded because the claim is unprocessable. Please submit a new claim with the complete/correct information. MA131 Physician already paid for services in conjunction with this demonstration claim. You must have the physician withdraw that claim and refund the payment before we can process your claim. MA132 Adjustment to the pre-demonstration rate. MA133 Claim overlaps inpatient stay. Rebill only those services rendered outside the inpatient stay. MA134 Missing/incomplete/invalid provider number of the facility where the patient resides.", "N1": "Alert: You may appeal this decision in writing within the required time limits following receipt of this notice by following the instructions included in your contract, plan benefit documents or jurisdiction statutes. Refer to the URL provided in the ERA for the payer website to access the appeals process guidelines.", "N2": "This allowance has been made in accordance with the most appropriate course of treatment provision of the plan.", "N3": "Missing consent form.", "N4": "Missing/Incomplete/Invalid prior Insurance Carrier(s) EOB.", "N5": "EOB received from previous payer. Claim not on file.", "N6": "Under FEHB law (U.S.C. 8904(b)), we cannot pay more for covered care than the amount Medicare would have allowed if the patient were enrolled in Medicare Part A and/or Medicare Part B.", "N7": "Alert: Processing of this claim/service has included consideration under Major Medical provisions.", "N8": "Crossover claim denied by previous payer and complete claim data not forwarded. Resubmit this claim to this payer to provide adequate data for adjudication.", "N9": "Adjustment represents the estimated amount a previous payer may pay.", "N10": "Adjustment based on the findings of a review organization/professional consult/manual adjudication/medical advisor/dental advisor/peer review.", "N11": "Denial reversed because of medical review.", "N12": "Policy provides coverage supplemental to Medicare. As the member does not appear to be enrolled in the applicable part of Medicare, the member is responsible for payment of the portion of the charge that would have been covered by Medicare.", "N13": "Payment based on professional/technical component modifier(s).", "N15": "Services for a newborn must be billed separately.", "N16": "Family/member Out-of-Pocket maximum has been met. Payment based on a higher percentage.", "N19": "Procedure code incidental to primary procedure.", "N20": "Service not payable with other service rendered on the same date.", "N21": "Alert: Your line item has been separated into multiple lines to expedite handling.", "N22": "Alert: This procedure code was added/changed because it more accurately describes the services rendered.", "N23": "Alert: Patient liability may be affected due to coordination of benefits with other carriers and/or maximum benefit provisions.", "N24": "Missing/incomplete/invalid Electronic Funds Transfer (EFT) banking information.", "N25": "This company has been contracted by your benefit plan to provide administrative claims payment services only. This company does not assume financial risk or obligation with respect to claims processed on behalf of your benefit plan.", "N26": "Missing itemized bill/statement.", "N27": "Missing/incomplete/invalid treatment number.", "N28": "Consent form requirements not fulfilled.", "N30": "Patient ineligible for this service.", "N31": "Missing/incomplete/invalid prescribing provider identifier.", "N32": "Claim must be submitted by the provider who rendered the service.", "N33": "No record of health check prior to initiation of treatment.", "N34": "Incorrect claim form/format for this service.", "N35": "Program integrity/utilization review decision.", "N36": "Claim must meet primary payer's processing requirements before we can consider payment.", "N37": "Missing/incomplete/invalid tooth number/letter.", "N39": "Procedure code is not compatible with tooth number/letter.", "N40": "Missing radiology film(s)/image(s).", "N42": "Missing mental health assessment.", "N43": "Bed hold or leave days exceeded.", "N45": "Payment based on authorized amount.", "N46": "Missing/incomplete/invalid admission hour.", "N47": "<PERSON><PERSON><PERSON> conflicts with another inpatient stay.", "N48": "Claim information does not agree with information received from other insurance carrier.", "N49": "Court ordered coverage information needs validation.", "N50": "Missing/incomplete/invalid discharge information.", "N51": "Electronic interchange agreement not on file for provider/submitter.", "N52": "Patient not enrolled in the billing provider's managed care plan on the date of service.", "N53": "Missing/incomplete/invalid point of pick-up address.", "N54": "Claim information is inconsistent with pre-certified/authorized services.", "N55": "Procedures for billing with group/referring/performing providers were not followed.", "N56": "Procedure code billed is not correct/valid for the services billed or the date of service billed.", "N57": "Missing/incomplete/invalid prescribing date.", "N58": "Missing/incomplete/invalid patient liability amount.", "N59": "Alert: Please refer to your provider manual for additional program and provider information.", "N61": "Rebill services on separate claims.", "N62": "Dates of service span multiple rate periods. Resubmit separate claims.", "N63": "Rebill services on separate claim lines.", "N64": "The 'from' and 'to' dates must be different.", "N65": "Procedure code or procedure rate count cannot be determined, or was not on file, for the date of service/provider.", "N67": "Professional provider services not paid separately. Included in facility payment under a demonstration project. Apply to that facility for payment, or resubmit your claim if: the facility notifies you the patient was excluded from this demonstration; or if you furnished these services in another location on the date of the patient's admission or discharge from a demonstration hospital. If services were furnished in a facility not involved in the demonstration on the same date the patient was discharged from or admitted to a demonstration facility, you must report the provider ID number for the non-demonstration facility on the new claim.", "N68": "Prior payment being cancelled as we were subsequently notified this patient was covered by a demonstration project in this site of service. Professional services were included in the payment made to the facility. You must contact the facility for your payment. Prior payment made to you by the patient or another insurer for this claim must be refunded to the payer within 30 days.", "N69": "Alert: PPS (Prospective Payment System) code changed by claims processing system.", "N70": "Consolidated billing and payment applies.", "N71": "Your unassigned claim for a drug or biological, clinical diagnostic laboratory services or ambulance service was processed as an assigned claim. You are required by law to accept assignment for these types of claims.", "N72": "PPS (Prospective Payment System) code changed by medical reviewers. Not supported by clinical records.", "N74": "Resubmit with multiple claims, each claim covering services provided in only one calendar month.", "N75": "Missing/incomplete/invalid tooth surface information.", "N76": "Missing/incomplete/invalid number of riders.", "N77": "Missing/incomplete/invalid designated provider number.", "N78": "The necessary components of the child and teen checkup (EPSDT) were not completed.", "N79": "Service billed is not compatible with patient location information.", "N80": "Missing/incomplete/invalid prenatal screening information.", "N81": "Procedure billed is not compatible with tooth surface code.", "N82": "Provider must accept insurance payment as payment in full when a third party payer contract specifies full reimbursement.", "N83": "No appeal rights. Adjudicative decision based on the provisions of a demonstration project.", "N84": "Alert: Further installment payments are forthcoming.", "N85": "Alert: This is the final installment payment.", "N86": "A failed trial of pelvic muscle exercise training is required in order for biofeedback training for the treatment of urinary incontinence to be covered.", "N87": "Home use of biofeedback therapy is not covered.", "N88": "Alert: This payment is being made conditionally. An HHA episode of care notice has been filed for this patient. When a patient is treated under a HHA episode of care, consolidated billing requires that certain therapy services and supplies, such as this, be included in the HHA's payment. This payment will need to be recouped from you if we establish that the patient is concurrently receiving treatment under a HHA episode of care.", "N89": "Alert: Payment information for this claim has been forwarded to more than one other payer, but format limitations permit only one of the secondary payers to be identified in this remittance advice.", "N90": "Covered only when performed by the attending physician.", "N91": "Services not included in the appeal review.", "N92": "This facility is not certified for digital mammography.", "N93": "A separate claim must be submitted for each place of service. Services furnished at multiple sites may not be billed in the same claim.", "N94": "Claim/Service denied because a more specific taxonomy code is required for adjudication.", "N95": "This provider type/provider specialty may not bill this service.", "N96": "Patient must be refractory to conventional therapy (documented behavioral, pharmacologic and/or surgical corrective therapy) and be an appropriate surgical candidate such that implantation with anesthesia can occur.", "N97": "Patients with stress incontinence, urinary obstruction, and specific neurologic diseases (e.g., diabetes with peripheral nerve involvement) which are associated with secondary manifestations of the above three indications are excluded.", "N98": "Patient must have had a successful test stimulation in order to support subsequent implantation. Before a patient is eligible for permanent implantation, he/she must demonstrate a 50 percent or greater improvement through test stimulation. Improvement is measured through voiding diaries.", "N99": "Patient must be able to demonstrate adequate ability to record voiding diary data such that clinical results of the implant procedure can be properly evaluated.", "N103": "Records indicate this patient was a prisoner or in custody of a Federal, State, or local authority when the service was rendered. This payer does not cover items and services furnished to an individual while he or she is in custody under a penal statute or rule, unless under State or local law, the individual is personally liable for the cost of his or her health care while in custody and the State or local government pursues the collection of such debt in the same way and with the same vigor as the collection of its other debts. The provider can collect from the Federal/State/ Local Authority as appropriate.", "N104": "This claim/service is not payable under our claims jurisdiction area. You can identify the correct Medicare contractor to process this claim/service through the CMS website at www.cms.gov.", "N105": "This is a misdirected claim/service for an RRB beneficiary. Submit paper claims to the RRB carrier: Palmetto GBA, P.O. Box 10066, Augusta, GA 30999. Call ************ for RRB EDI information for electronic claims processing.", "N106": "Payment for services furnished to Skilled Nursing Facility (SNF) inpatients (except for excluded services) can only be made to the SNF. You must request payment from the SNF rather than the patient for this service.", "N107": "Services furnished to Skilled Nursing Facility (SNF) inpatients must be billed on the inpatient claim. They cannot be billed separately as outpatient services.", "N108": "Missing/incomplete/invalid upgrade information.", "N109": "Alert: This claim/service was chosen for complex review.", "N110": "This facility is not certified for film mammography.", "N111": "No appeal right except duplicate claim/service issue. This service was included in a claim that has been previously billed and adjudicated.", "N112": "This claim is excluded from your electronic remittance advice.", "N113": "Only one initial visit is covered per physician, group practice or provider.", "N114": "During the transition to the Ambulance Fee Schedule, payment is based on the lesser of a blended amount calculated using a percentage of the reasonable charge/cost and fee schedule amounts, or the submitted charge for the service. You will be notified yearly what the percentages for the blended payment calculation will be.", "N115": "This decision was based on a Local Coverage Determination (LCD). An LCD provides a guide to assist in determining whether a particular item or service is covered. A copy of this policy is available at www.cms.gov/mcd, or if you do not have web access, you may contact the contractor to request a copy of the LCD.", "N116": "Alert: This payment is being made conditionally because the service was provided in the home, and it is possible that the patient is under a home health episode of care. When a patient is treated under a home health episode of care, consolidated billing requires that certain therapy services and supplies, such as this, be included in the home health agency's (HHA's) payment. This payment will need to be recouped from you if we establish that the patient is concurrently receiving treatment under an HHA episode of care.", "N117": "This service is paid only once in a patient's lifetime.", "N118": "This service is not paid if billed more than once every 28 days.", "N119": "This service is not paid if billed once every 28 days, and the patient has spent 5 or more consecutive days in any inpatient or Skilled /nursing Facility (SNF) within those 28 days.", "N120": "Payment is subject to home health prospective payment system partial episode payment adjustment. Patient was transferred/discharged/readmitted during payment episode.", "N121": "Medicare Part B does not pay for items or services provided by this type of practitioner for beneficiaries in a Medicare Part A covered Skilled Nursing Facility (SNF) stay.", "N122": "Add-on code cannot be billed by itself.", "N123": "Alert: This is a split service and represents a portion of the units from the originally submitted service.", "N124": "Payment has been denied for the/made only for a less extensive service/item because the information furnished does not substantiate the need for the (more extensive) service/item. The patient is liable for the charges for this service/item as you informed the patient in writing before the service/item was furnished that we would not pay for it, and the patient agreed to pay.", "N125": "Payment has been (denied for the/made only for a less extensive) service/item because the information furnished does not substantiate the need for the (more extensive) service/item. If you have collected any amount from the patient, you must refund that amount to the patient within 30 days of receiving this notice. The requirements for a refund are in ¤1834(a)(18) of the Social Security Act (and in ¤¤1834(j)(4) and 1879(h) by cross-reference to ¤1834(a)(18)). Section 1834(a)(18)(B) specifies that suppliers which knowingly and willfully fail to make appropriate refunds may be subject to civil money penalties and/or exclusion from the Medicare program. If you have any questions about this notice, please contact this office.", "N126": "Social Security Records indicate that this individual has been deported. This payer does not cover items and services furnished to individuals who have been deported.", "N127": "This is a misdirected claim/service for a United Mine Workers of America (UMWA) beneficiary. Please submit claims to them.", "N128": "This amount represents the prior to coverage portion of the allowance.", "N129": "Not eligible due to the patient's age.", "N130": "Consult plan benefit documents/guidelines for information about restrictions for this service.", "N131": "Total payments under multiple contracts cannot exceed the allowance for this service.", "N132": "Alert: Payments will cease for services rendered by this US Government debarred or excluded provider after the 30 day grace period as previously notified.", "N133": "Alert: Services for predetermination and services requesting payment are being processed separately.", "N134": "Alert: This represents your scheduled payment for this service. If treatment has been discontinued, please contact Customer Service.", "N135": "Record fees are the patient's responsibility and limited to the specified co-payment.", "N136": "Alert: To obtain information on the process to file an appeal in Arizona, call the Department's Consumer Assistance Office at (************* or (*************.", "N137": "Alert: The provider acting on the Member's behalf, may file an appeal with the Payer. The provider, acting on the Member's behalf, may file a complaint with the State Insurance Regulatory Authority without first filing an appeal, if the coverage decision involves an urgent condition for which care has not been rendered. The address may be obtained from the State Insurance Regulatory Authority.", "N138": "Alert: In the event you disagree with the Dental Advisor's opinion and have additional information relative to the case, you may submit radiographs to the Dental Advisor Unit at the subscriber's dental insurance carrier for a second Independent Dental Advisor Review.", "N139": "Alert: Under 32 CFR 199.13, a non-participating provider is not an appropriate appealing party. Therefore, if you disagree with the Dental Advisor's opinion, you may appeal the determination if appointed in writing, by the beneficiary, to act as his/her representative. Should you be appointed as a representative, submit a copy of this letter, a signed statement explaining the matter in which you disagree, and any radiographs and relevant information to the subscriber's Dental insurance carrier within 90 days from the date of this letter.", "N140": "Alert: You have not been designated as an authorized OCONUS provider therefore are not considered an appropriate appealing party. If the beneficiary has appointed you, in writing, to act as his/her representative and you disagree with the Dental Advisor's opinion, you may appeal by submitting a copy of this letter, a signed statement explaining the matter in which you disagree, and any relevant information to the subscriber's Dental insurance carrier within 90 days from the date of this letter.", "N141": "The patient was not residing in a long-term care facility during all or part of the service dates billed.", "N142": "The original claim was denied. Resubmit a new claim, not a replacement claim.", "N143": "The patient was not in a hospice program during all or part of the service dates billed.", "N144": "The rate changed during the dates of service billed.", "N146": "Missing screening document.", "N147": "Long term care case mix or per diem rate cannot be determined because the patient ID number is missing, incomplete, or invalid on the assignment request.", "N148": "Missing/incomplete/invalid date of last menstrual period.", "N149": "Rebill all applicable services on a single claim.", "N150": "Missing/incomplete/invalid model number.", "N151": "Telephone contact services will not be paid until the face-to-face contact requirement has been met.", "N152": "Missing/incomplete/invalid replacement claim information.", "N153": "Missing/incomplete/invalid room and board rate.", "N154": "Alert: This payment was delayed for correction of provider's mailing address.", "N155": "Alert: Our records do not indicate that other insurance is on file. Please submit other insurance information for our records.", "N156": "Alert: The patient is responsible for the difference between the approved treatment and the elective treatment.", "N157": "Transportation to/from this destination is not covered.", "N158": "Transportation in a vehicle other than an ambulance is not covered.", "N159": "Payment denied/reduced because mileage is not covered when the patient is not in the ambulance.", "N160": "The patient must choose an option before a payment can be made for this procedure/ equipment/ supply/ service.", "N161": "This drug/service/supply is covered only when the associated service is covered.", "N162": "Alert: Although your claim was paid, you have billed for a test/specialty not included in your Laboratory Certification. Your failure to correct the laboratory certification information will result in a denial of payment in the near future.", "N163": "Medical record does not support code billed per the code definition.", "N167": "Charges exceed the post-transplant coverage limit.", "N170": "A new/revised/renewed certificate of medical necessity is needed.", "N171": "Payment for repair or replacement is not covered or has exceeded the purchase price.", "N172": "The patient is not liable for the denied/adjusted charge(s) for receiving any updated service/item.", "N173": "No qualifying hospital stay dates were provided for this episode of care.", "N174": "This is not a covered service/procedure/ equipment/bed, however patient liability is limited to amounts shown in the adjustments under group 'PR'.", "N175": "Missing review organization approval.", "N176": "Services provided aboard a ship are covered only when the ship is of United States registry and is in United States waters. In addition, a doctor licensed to practice in the United States must provide the service.", "N177": "Alert: We did not send this claim to patient's other insurer. They have indicated no additional payment can be made.", "N178": "Missing pre-operative images/visual field results.", "N179": "Additional information has been requested from the member. The charges will be reconsidered upon receipt of that information.", "N180": "This item or service does not meet the criteria for the category under which it was billed.", "N181": "Additional information is required from another provider involved in this service.", "N182": "This claim/service must be billed according to the schedule for this plan.", "N183": "Alert: This is a predetermination advisory message, when this service is submitted for payment additional documentation as specified in plan documents will be required to process benefits.", "N184": "Rebill technical and professional components separately.", "N185": "Alert: Do not resubmit this claim/service.", "N186": "Non-Availability Statement (NAS) required for this service. Contact the nearest Military Treatment Facility (MTF) for assistance.", "N187": "Alert: You may request a review in writing within the required time limits following receipt of this notice by following the instructions included in your contract or plan benefit documents.", "N188": "The approved level of care does not match the procedure code submitted.", "N189": "Alert: This service has been paid as a one-time exception to the plan's benefit restrictions.", "N190": "Missing contract indicator.", "N191": "The provider must update insurance information directly with payer.", "N192": "Alert: <PERSON><PERSON> is a Medicaid/Qualified Medicare Beneficiary.", "N193": "Alert: Specific federal/state/local program may cover this service through another payer.", "N194": "Technical component not paid if provider does not own the equipment used.", "N195": "The technical component must be billed separately.", "N196": "Alert: Patient eligible to apply for other coverage which may be primary.", "N197": "The subscriber must update insurance information directly with payer.", "N198": "Rendering provider must be affiliated with the pay-to provider.", "N199": "Additional payment/recoupment approved based on payer-initiated review/audit.", "N200": "The professional component must be billed separately.", "N202": "Alert: Additional information/explanation will be sent separately.", "N203": "Missing/incomplete/invalid anesthesia time/units.", "N204": "Services under review for possible pre-existing condition. Send medical records for prior 12 months", "N205": "Information provided was illegible.", "N206": "The supporting documentation does not match the information sent on the claim.", "N207": "Missing/incomplete/invalid weight.", "N208": "Missing/incomplete/invalid DRG code.", "N209": "Missing/incomplete/invalid taxpayer identification number (TIN).", "N210": "Alert: You may appeal this decision.", "N211": "Alert: You may not appeal this decision.", "N212": "Charges processed under a Point of Service benefit.", "N213": "Missing/incomplete/invalid facility/discrete unit DRG/DRG exempt status information.", "N214": "Missing/incomplete/invalid history of the related initial surgical procedure(s).", "N215": "Alert: A payer providing supplemental or secondary coverage shall not require a claims determination for this service from a primary payer as a condition of making its own claims determination.", "N216": "We do not offer coverage for this type of service or the patient is not enrolled in this portion of our benefit package.", "N217": "We pay only one site of service per provider per claim.", "N218": "You must furnish and service this item for as long as the patient continues to need it. We can pay for maintenance and/or servicing for the time period specified in the contract or coverage manual.", "N219": "Payment based on previous payer's allowed amount.", "N220": "Alert: See the payer's web site or contact the payer's Customer Service department to obtain forms and instructions for filing a provider dispute.", "N221": "Missing Admitting History and Physical report.", "N222": "Incomplete/invalid Admitting History and Physical report.", "N223": "Missing documentation of benefit to the patient during initial treatment period.", "N224": "Incomplete/invalid documentation of benefit to the patient during initial treatment period.", "N226": "Incomplete/invalid American Diabetes Association Certificate of Recognition.", "N227": "Incomplete/invalid Certificate of Medical Necessity.", "N228": "Incomplete/invalid consent form.", "N229": "Incomplete/invalid contract indicator.", "N230": "Incomplete/invalid indication of whether the patient owns the equipment that requires the part or supply.", "N231": "Incomplete/invalid invoice or statement certifying the actual cost of the lens, less discounts, and/or the type of intraocular lens used.", "N232": "Incomplete/invalid itemized bill/statement.", "N233": "Incomplete/invalid operative note/report.", "N234": "Incomplete/invalid oxygen certification/re-certification.", "N235": "Incomplete/invalid pacemaker registration form.", "N236": "Incomplete/invalid pathology report.", "N237": "Incomplete/invalid patient medical record for this service.", "N238": "Incomplete/invalid physician certified plan of care.", "N239": "Incomplete/invalid physician financial relationship form.", "N240": "Incomplete/invalid radiology report.", "N241": "Incomplete/invalid review organization approval.", "N242": "Incomplete/invalid radiology film(s)/image(s).", "N243": "Incomplete/invalid/not approved screening document.", "N244": "Incomplete/Invalid pre-operative images/visual field results.", "N245": "Incomplete/invalid plan information for other insurance.", "N246": "State regulated patient payment limitations apply to this service.", "N247": "Missing/incomplete/invalid assistant surgeon taxonomy.", "N248": "Missing/incomplete/invalid assistant surgeon name.", "N249": "Missing/incomplete/invalid assistant surgeon primary identifier.", "N250": "Missing/incomplete/invalid assistant surgeon secondary identifier.", "N251": "Missing/incomplete/invalid attending provider taxonomy.", "N252": "Missing/incomplete/invalid attending provider name.", "N253": "Missing/incomplete/invalid attending provider primary identifier.", "N254": "Missing/incomplete/invalid attending provider secondary identifier.", "N255": "Missing/incomplete/invalid billing provider taxonomy.", "N256": "Missing/incomplete/invalid billing provider/supplier name.", "N257": "Missing/incomplete/invalid billing provider/supplier primary identifier.", "N258": "Missing/incomplete/invalid billing provider/supplier address.", "N259": "Missing/incomplete/invalid billing provider/supplier secondary identifier.", "N260": "Missing/incomplete/invalid billing provider/supplier contact information.", "N261": "Missing/incomplete/invalid operating provider name.", "N262": "Missing/incomplete/invalid operating provider primary identifier.", "N263": "Missing/incomplete/invalid operating provider secondary identifier.", "N264": "Missing/incomplete/invalid ordering provider name.", "N265": "Missing/incomplete/invalid ordering provider primary identifier.", "N266": "Missing/incomplete/invalid ordering provider address.", "N267": "Missing/incomplete/invalid ordering provider secondary identifier.", "N268": "Missing/incomplete/invalid ordering provider contact information.", "N269": "Missing/incomplete/invalid other provider name.", "N270": "Missing/incomplete/invalid other provider primary identifier.", "N271": "Missing/incomplete/invalid other provider secondary identifier.", "N272": "Missing/incomplete/invalid other payer attending provider identifier.", "N273": "Missing/incomplete/invalid other payer operating provider identifier.", "N274": "Missing/incomplete/invalid other payer other provider identifier.", "N275": "Missing/incomplete/invalid other payer purchased service provider identifier.", "N276": "Missing/incomplete/invalid other payer referring provider identifier.", "N277": "Missing/incomplete/invalid other payer rendering provider identifier.", "N278": "Missing/incomplete/invalid other payer service facility provider identifier.", "N279": "Missing/incomplete/invalid pay-to provider name.", "N280": "Missing/incomplete/invalid pay-to provider primary identifier.", "N281": "Missing/incomplete/invalid pay-to provider address.", "N282": "Missing/incomplete/invalid pay-to provider secondary identifier.", "N283": "Missing/incomplete/invalid purchased service provider identifier.", "N284": "Missing/incomplete/invalid referring provider taxonomy.", "N285": "Missing/incomplete/invalid referring provider name.", "N286": "Missing/incomplete/invalid referring provider primary identifier.", "N287": "Missing/incomplete/invalid referring provider secondary identifier.", "N288": "Missing/incomplete/invalid rendering provider taxonomy.", "N289": "Missing/incomplete/invalid rendering provider name.", "N290": "Missing/incomplete/invalid rendering provider primary identifier.", "N291": "Missing/incomplete/invalid rendering provider secondary identifier.", "N292": "Missing/incomplete/invalid service facility name.", "N293": "Missing/incomplete/invalid service facility primary identifier.", "N294": "Missing/incomplete/invalid service facility primary address.", "N295": "Missing/incomplete/invalid service facility secondary identifier.", "N296": "Missing/incomplete/invalid supervising provider name.", "N297": "Missing/incomplete/invalid supervising provider primary identifier.", "N298": "Missing/incomplete/invalid supervising provider secondary identifier.", "N299": "Missing/incomplete/invalid occurrence date(s).", "N300": "Missing/incomplete/invalid occurrence span date(s).", "N301": "Missing/incomplete/invalid procedure date(s).", "N302": "Missing/incomplete/invalid other procedure date(s).", "N303": "Missing/incomplete/invalid principal procedure date.", "N304": "Missing/incomplete/invalid dispensed date.", "N305": "Missing/incomplete/invalid injury/accident date.", "N306": "Missing/incomplete/invalid acute manifestation date.", "N307": "Missing/incomplete/invalid adjudication or payment date.", "N308": "Missing/incomplete/invalid appliance placement date.", "N309": "Missing/incomplete/invalid assessment date.", "N310": "Missing/incomplete/invalid assumed or relinquished care date.", "N311": "Missing/incomplete/invalid authorized to return to work date.", "N312": "Missing/incomplete/invalid begin therapy date.", "N313": "Missing/incomplete/invalid certification revision date.", "N314": "Missing/incomplete/invalid diagnosis date.", "N315": "Missing/incomplete/invalid disability from date.", "N316": "Missing/incomplete/invalid disability to date.", "N317": "Missing/incomplete/invalid discharge hour.", "N318": "Missing/incomplete/invalid discharge or end of care date.", "N319": "Missing/incomplete/invalid hearing or vision prescription date.", "N320": "Missing/incomplete/invalid Home Health Certification Period.", "N321": "Missing/incomplete/invalid last admission period.", "N322": "Missing/incomplete/invalid last certification date.", "N323": "Missing/incomplete/invalid last contact date.", "N324": "Missing/incomplete/invalid last seen/visit date.", "N325": "Missing/incomplete/invalid last worked date.", "N326": "Missing/incomplete/invalid last x-ray date.", "N327": "Missing/incomplete/invalid other insured birth date.", "N328": "Missing/incomplete/invalid Oxygen Saturation Test date.", "N329": "Missing/incomplete/invalid patient birth date.", "N330": "Missing/incomplete/invalid patient death date.", "N331": "Missing/incomplete/invalid physician order date.", "N332": "Missing/incomplete/invalid prior hospital discharge date.", "N333": "Missing/incomplete/invalid prior placement date.", "N334": "Missing/incomplete/invalid re-evaluation date.", "N335": "Missing/incomplete/invalid referral date.", "N336": "Missing/incomplete/invalid replacement date.", "N337": "Missing/incomplete/invalid secondary diagnosis date.", "N338": "Missing/incomplete/invalid shipped date.", "N339": "Missing/incomplete/invalid similar illness or symptom date.", "N340": "Missing/incomplete/invalid subscriber birth date.", "N341": "Missing/incomplete/invalid surgery date.", "N342": "Missing/incomplete/invalid test performed date.", "N343": "Missing/incomplete/invalid Transcutaneous Electrical Nerve Stimulator (TENS) trial start date.", "N344": "Missing/incomplete/invalid Transcutaneous Electrical Nerve Stimulator (TENS) trial end date.", "N345": "Date range not valid with units submitted.", "N346": "Missing/incomplete/invalid oral cavity designation code.", "N347": "Your claim for a referred or purchased service cannot be paid because payment has already been made for this same service to another provider by a payment contractor representing the payer.", "N348": "You chose that this service/supply/drug would be rendered/supplied and billed by a different practitioner/supplier.", "N349": "The administration method and drug must be reported to adjudicate this service.", "N350": "Missing/incomplete/invalid description of service for a Not Otherwise Classified (NOC) code or for an Unlisted/By Report procedure.", "N351": "Service date outside of the approved treatment plan service dates.", "N352": "Alert: There are no scheduled payments for this service. Submit a claim for each patient visit.", "N353": "Alert: Benefits have been estimated, when the actual services have been rendered, additional payment will be considered based on the submitted claim.", "N354": "Incomplete/invalid invoice.", "N355": "Alert: The law permits exceptions to the refund requirement in two cases: - If you did not know, and could not have reasonably been expected to know, that we would not pay for this service; or - If you notified the patient in writing before providing the service that you believed that we were likely to deny the service, and the patient signed a statement agreeing to pay for the service. If you come within either exception, or if you believe the carrier was wrong in its determination that we do not pay for this service, you should request appeal of this determination within 30 days of the date of this notice. Your request for review should include any additional information necessary to support your position. If you request an appeal within 30 days of receiving this notice, you may delay refunding the amount to the patient until you receive the results of the review. If the review decision is favorable to you, you do not need to make any refund. If, however, the review is unfavorable, the law specifies that you must make the refund within 15 days of receiving the unfavorable review decision. The law also permits you to request an appeal at any time within 120 days of the date you receive this notice. However, an appeal request that is received more than 30 days after the date of this notice, does not permit you to delay making the refund. Regardless of when a review is requested, the patient will be notified that you have requested one, and will receive a copy of the determination. The patient has received a separate notice of this denial decision. The notice advises that he/she may be entitled to a refund of any amounts paid, if you should have known that we would not pay and did not tell him/her. It also instructs the patient to contact our office if he/she does not hear anything about a refund within 30 days", "N356": "Not covered when performed with, or subsequent to, a non-covered service.", "N357": "Time frame requirements between this service/procedure/supply and a related service/procedure/supply have not been met.", "N358": "Alert: This decision may be reviewed if additional documentation as described in the contract or plan benefit documents is submitted.", "N359": "Missing/incomplete/invalid height.", "N360": "Alert: Coordination of benefits has not been calculated when estimating benefits for this pre-determination. Submit payment information from the primary payer with the secondary claim.", "N362": "The number of Days or Units of Service exceeds our acceptable maximum.", "N363": "Alert: in the near future we are implementing new policies/procedures that would affect this determination.", "N364": "Alert: According to our agreement, you must waive the deductible and/or coinsurance amounts.", "N366": "Requested information not provided. The claim will be reopened if the information previously requested is submitted within one year after the date of this denial notice.", "N367": "Alert: The claim information has been forwarded to a Consumer Spending Account processor for review; for example, flexible spending account or health savings account.", "N368": "You must appeal the determination of the previously adjudicated claim.", "N369": "Alert: Although this claim has been processed, it is deficient according to state legislation/regulation.", "N370": "Billing exceeds the rental months covered/approved by the payer.", "N371": "Alert: title of this equipment must be transferred to the patient.", "N372": "Only reasonable and necessary maintenance/service charges are covered.", "N373": "It has been determined that another payer paid the services as primary when they were not the primary payer. Therefore, we are refunding to the payer that paid as primary on your behalf.", "N374": "Primary Medicare Part A insurance has been exhausted and a Part B Remittance Advice is required.", "N375": "Missing/incomplete/invalid questionnaire/information required to determine dependent eligibility.", "N376": "Subscriber/patient is assigned to active military duty, therefore primary coverage may be TRICARE.", "N377": "Payment based on a processed replacement claim.", "N378": "Missing/incomplete/invalid prescription quantity.", "N379": "Claim level information does not match line level information.", "N380": "The original claim has been processed, submit a corrected claim.", "N381": "Alert: Consult our contractual agreement for restrictions/billing/payment information related to these charges.", "N382": "Missing/incomplete/invalid patient identifier.", "N383": "Not covered when deemed cosmetic.", "N384": "Records indicate that the referenced body part/tooth has been removed in a previous procedure.", "N385": "Notification of admission was not timely according to published plan procedures.", "N386": "This decision was based on a National Coverage Determination (NCD). An NCD provides a coverage determination as to whether a particular item or service is covered. A copy of this policy is available at www.cms.gov/mcd/search.asp. If you do not have web access, you may contact the contractor to request a copy of the NCD.", "N387": "Alert: Submit this claim to the patient's other insurer for potential payment of supplemental benefits. We did not forward the claim information.", "N388": "Missing/incomplete/invalid prescription number.", "N389": "Duplicate prescription number submitted.", "N390": "This service/report cannot be billed separately.", "N391": "Missing emergency department records.", "N392": "Incomplete/invalid emergency department records.", "N393": "Missing progress notes/report.", "N394": "Incomplete/invalid progress notes/report.", "N395": "Missing laboratory report.", "N396": "Incomplete/invalid laboratory report.", "N397": "Benefits are not available for incomplete service(s)/undelivered item(s).", "N398": "Missing elective consent form.", "N399": "Incomplete/invalid elective consent form.", "N400": "Alert: Electronically enabled providers should submit claims electronically.", "N401": "Missing periodontal charting.", "N402": "Incomplete/invalid periodontal charting.", "N403": "Missing facility certification.", "N404": "Incomplete/invalid facility certification.", "N405": "This service is only covered when the donor's insurer(s) do not provide coverage for the service.", "N406": "This service is only covered when the recipient's insurer(s) do not provide coverage for the service.", "N407": "You are not an approved submitter for this transmission format.", "N408": "This payer does not cover deductibles assessed by a previous payer.", "N409": "This service is related to an accidental injury and is not covered unless provided within a specific time frame from the date of the accident.", "N410": "Not covered unless the prescription changes.", "N411": "This service is allowed one time in a 6-month period.", "N412": "This service is allowed 2 times in a 12-month period.", "N413": "This service is allowed 2 times in a benefit year.", "N414": "This service is allowed 4 times in a 12-month period.", "N415": "This service is allowed 1 time in an 18-month period.", "N416": "This service is allowed 1 time in a 3-year period.", "N417": "This service is allowed 1 time in a 5-year period.", "N418": "Misrouted claim. See the payer's claim submission instructions.", "N419": "Claim payment was the result of a payer's retroactive adjustment due to a retroactive rate change.", "N420": "Claim payment was the result of a payer's retroactive adjustment due to a Coordination of Benefits or Third Party Liability Recovery.", "N421": "Claim payment was the result of a payer's retroactive adjustment due to a review organization decision.", "N422": "Claim payment was the result of a payer's retroactive adjustment due to a payer's contract incentive program.", "N423": "Claim payment was the result of a payer's retroactive adjustment due to a non standard program.", "N424": "Patient does not reside in the geographic area required for this type of payment.", "N425": "Statutorily excluded service(s).", "N426": "No coverage when self-administered.", "N427": "Payment for eyeglasses or contact lenses can be made only after cataract surgery.", "N428": "Not covered when performed in this place of service.", "N429": "Not covered when considered routine.", "N430": "Procedure code is inconsistent with the units billed.", "N431": "Not covered with this procedure.", "N432": "Alert: Adjustment based on a Recovery Audit.", "N433": "Resubmit this claim using only your National Provider Identifier (NPI).", "N434": "Missing/Incomplete/Invalid Present on Admission indicator.", "N435": "Exceeds number/frequency approved /allowed within time period without support documentation.", "N436": "The injury claim has not been accepted and a mandatory medical reimbursement has been made.", "N437": "Alert: If the injury claim is accepted, these charges will be reconsidered.", "N438": "This jurisdiction only accepts paper claims.", "N439": "Missing anesthesia physical status report/indicators.", "N440": "Incomplete/invalid anesthesia physical status report/indicators.", "N441": "This missed/cancelled appointment is not covered.", "N442": "Payment based on an alternate fee schedule.", "N443": "Missing/incomplete/invalid total time or begin/end time.", "N444": "Alert: This facility has not filed the Election for High Cost Outlier form with the Division of Workers' Compensation.", "N445": "Missing document for actual cost or paid amount.", "N446": "Incomplete/invalid document for actual cost or paid amount.", "N447": "Payment is based on a generic equivalent as required documentation was not provided.", "N448": "This drug/service/supply is not included in the fee schedule or contracted/legislated fee arrangement.", "N449": "Payment based on a comparable drug/service/supply.", "N450": "Covered only when performed by the primary treating physician or the designee.", "N451": "Missing Admission Summary Report.", "N452": "Incomplete/invalid Admission Summary Report.", "N453": "Missing Consultation Report.", "N454": "Incomplete/invalid Consultation Report.", "N455": "Missing Physician Order.", "N456": "Incomplete/invalid Physician Order.", "N457": "Missing Diagnostic Report.", "N458": "Incomplete/invalid Diagnostic Report.", "N459": "Missing <PERSON><PERSON><PERSON>.", "N460": "Incomplete/invalid Discharge Summary.", "N461": "Missing Nursing Notes.", "N462": "Incomplete/invalid Nursing Notes.", "N463": "Missing support data for claim.", "N464": "Incomplete/invalid support data for claim.", "N465": "Missing Physical Therapy Notes/Report.", "N466": "Incomplete/invalid Physical Therapy Notes/Report.", "N467": "Missing Tests and Analysis Report.", "N468": "Incomplete/invalid Report of Tests and Analysis Report.", "N469": "Alert: Claim/Service(s) subject to appeal process, see section 935 of Medicare Prescription Drug, Improvement, and Modernization Act of 2003 (MMA).", "N470": "This payment will complete the mandatory medical reimbursement limit.", "N471": "Missing/incomplete/invalid HIPPS Rate Code.", "N472": "Payment for this service has been issued to another provider.", "N473": "Missing certification.", "N474": "Incomplete/invalid certification.", "N475": "Missing completed referral form.", "N476": "Incomplete/invalid completed referral form.", "N477": "Missing Dental Models.", "N478": "Incomplete/invalid Dental Models.", "N479": "Missing Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payer).", "N480": "Incomplete/invalid Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payer).", "N481": "Missing Models.", "N482": "Incomplete/invalid Models.", "N485": "Missing Physical Therapy Certification.", "N486": "Incomplete/invalid Physical Therapy Certification.", "N487": "Missing Prosthetics or Orthotics Certification.", "N488": "Incomplete/invalid Prosthetics or Orthotics Certification.", "N489": "Missing referral form.", "N490": "Incomplete/invalid referral form.", "N491": "Missing/Incomplete/Invalid Exclusionary Rider Condition.", "N492": "Alert: A network provider may bill the member for this service if the member requested the service and agreed in writing, prior to receiving the service, to be financially responsible for the billed charge.", "N493": "Missing Doctor First Report of Injury.", "N494": "Incomplete/invalid Doctor First Report of Injury.", "N495": "Missing Supplemental Medical Report.", "N496": "Incomplete/invalid Supplemental Medical Report.", "N497": "Missing Medical Permanent Impairment or Disability Report.", "N498": "Incomplete/invalid Medical Permanent Impairment or Disability Report.", "N499": "Missing Medical Legal Report.", "N500": "Incomplete/invalid Medical Legal Report.", "N501": "Missing Vocational Report.", "N502": "Incomplete/invalid Vocational Report.", "N503": "Missing Work Status Report.", "N504": "Incomplete/invalid Work Status Report.", "N505": "Alert: This response includes only services that could be estimated in real-time. No estimate will be provided for the services that could not be estimated in real-time.", "N506": "Alert: This is an estimate of the member's liability based on the information available at the time the estimate was processed. Actual coverage and member liability amounts will be determined when the claim is processed. This is not a pre-authorization or a guarantee of payment.", "N507": "Plan distance requirements have not been met.", "N508": "Alert: This real-time claim adjudication response represents the member responsibility to the provider for services reported. The member will receive an Explanation of Benefits electronically or in the mail. Contact the insurer if there are any questions.", "N509": "Alert: A current inquiry shows the member's Consumer Spending Account contains sufficient funds to cover the member liability for this claim/service. Actual payment from the Consumer Spending Account will depend on the availability of funds and determination of eligible services at the time of payment processing.", "N510": "Alert: A current inquiry shows the member's Consumer Spending Account does not contain sufficient funds to cover the member's liability for this claim/service. Actual payment from the Consumer Spending Account will depend on the availability of funds and determination of eligible services at the time of payment processing.", "N511": "Alert: Information on the availability of Consumer Spending Account funds to cover the member liability on this claim/service is not available at this time.", "N512": "Alert: This is the initial remit of a non-NCPDP claim originally submitted real-time without change to the adjudication.", "N513": "Alert: This is the initial remit of a non-NCPDP claim originally submitted real-time with a change to the adjudication.", "N516": "Records indicate a mismatch between the submitted NPI and EIN.", "N517": "Resubmit a new claim with the requested information.", "N518": "No separate payment for accessories when furnished for use with oxygen equipment.", "N519": "Invalid combination of HCPCS modifiers.", "N520": "Alert: Payment made from a Consumer Spending Account.", "N521": "Mismatch between the submitted provider information and the provider information stored in our system.", "N522": "Duplicate of a claim processed, or to be processed, as a crossover claim.", "N523": "The limitation on outlier payments defined by this payer for this service period has been met. The outlier payment otherwise applicable to this claim has not been paid.", "N524": "Based on policy this payment constitutes payment in full.", "N525": "These services are not covered when performed within the global period of another service.", "N526": "Not qualified for recovery based on employer size.", "N527": "We processed this claim as the primary payer prior to receiving the recovery demand.", "N528": "Patient is entitled to benefits for Institutional Services only.", "N529": "Patient is entitled to benefits for Professional Services only.", "N530": "Not Qualified for Recovery based on enrollment information.", "N531": "Not qualified for recovery based on direct payment of premium.", "N532": "Not qualified for recovery based on disability and working status.", "N533": "Services performed in an Indian Health Services facility under a self-insured tribal Group Health Plan.", "N534": "This is an individual policy, the employer does not participate in plan sponsorship.", "N535": "Payment is adjusted when procedure is performed in this place of service based on the submitted procedure code and place of service.", "N536": "We are not changing the prior payer's determination of patient responsibility, which you may collect, as this service is not covered by us.", "N537": "We have examined claims history and no records of the services have been found.", "N538": "A facility is responsible for payment to outside providers who furnish these services/supplies/drugs to its patients/residents.", "N539": "Alert: We processed appeals/waiver requests on your behalf and that request has been denied.", "N540": "Payment adjusted based on the interrupted stay policy.", "N541": "Mismatch between the submitted insurance type code and the information stored in our system.", "N542": "Missing income verification.", "N543": "Incomplete/invalid income verification.", "N544": "Alert: Although this was paid, you have billed with a referring/ordering provider that does not match our system record. Unless corrected this will not be paid in the future.", "N545": "Payment reduced based on status as an unsuccessful eprescriber per the Electronic Prescribing (eRx) Incentive Program.", "N546": "Payment represents a previous reduction based on the Electronic Prescribing (eRx) Incentive Program.", "N547": "A refund request (Frequency Type Code 8) was processed previously.", "N548": "Alert: <PERSON><PERSON>'s calendar year deductible has been met.", "N549": "Alert: <PERSON><PERSON>'s calendar year out-of-pocket maximum has been met.", "N550": "Alert: You have not responded to requests to revalidate your provider/supplier enrollment information. Your failure to revalidate your enrollment information will result in a payment hold in the near future.", "N551": "Payment adjusted based on the Ambulatory Surgical Center (ASC) Quality Reporting Program.", "N552": "Payment adjusted to reverse a previous withhold/bonus amount.", "N554": "Missing/Incomplete/Invalid Family Planning Indicator.", "N555": "Missing medication list.", "N556": "Incomplete/invalid medication list.", "N557": "This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the specimen was collected.", "N558": "This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the equipment was received.", "N559": "This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the Ordering Physician is located.", "N560": "The pilot program requires an interim or final claim within 60 days of the Notice of Admission. A claim was not received.", "N561": "The bundled claim originally submitted for this episode of care includes related readmissions. You may resubmit the original claim to receive a corrected payment based on this readmission.", "N562": "The provider number of your incoming claim does not match the provider number on the processed Notice of Admission (NOA) for this bundled payment.", "N563": "Alert: Missing required provider/supplier issuance of advance patient notice of non-coverage. The patient is not liable for payment for this service.", "N564": "Patient did not meet the inclusion criteria for the demonstration project or pilot program.", "N565": "Alert: This non-payable reporting code requires a modifier. Future claims containing this non-payable reporting code must include an appropriate modifier for the claim to be processed.", "N566": "Alert: This procedure code requires functional reporting. Future claims containing this procedure code must include an applicable non-payable code and appropriate modifiers for the claim to be processed.", "N567": "Not covered when considered preventative.", "N568": "Alert: Initial payment based on the Notice of Admission (NOA) under the Bundled Payment Model IV initiative.", "N569": "Not covered when performed for the reported diagnosis.", "N570": "Missing/incomplete/invalid credentialing data.", "N571": "Alert: Payment will be issued quarterly by another payer/contractor.", "N572": "This procedure is not payable unless appropriate non-payable reporting codes and associated modifiers are submitted.", "N573": "Alert: You have been overpaid and must refund the overpayment. The refund will be requested separately by another payer/contractor.", "N574": "Our records indicate the ordering/referring provider is of a type/specialty that cannot order or refer. Please verify that the claim ordering/referring provider information is accurate or contact the ordering/referring provider.", "N575": "Mismatch between the submitted ordering/referring provider name and the ordering/referring provider name stored in our records.", "N576": "Services not related to the specific incident/claim/accident/loss being reported.", "N577": "Personal Injury Protection (PIP) Coverage.", "N578": "Coverages do not apply to this loss.", "N579": "Medical Payments Coverage (MPC).", "N580": "Determination based on the provisions of the insurance policy.", "N581": "Investigation of coverage eligibility is pending.", "N582": "Benefits suspended pending the patient's cooperation.", "N583": "Patient was not an occupant of our insured vehicle and therefore, is not an eligible injured person.", "N584": "Not covered based on the insured's noncompliance with policy or statutory conditions.", "N585": "Benefits are no longer available based on a final injury settlement.", "N586": "The injured party does not qualify for benefits.", "N587": "Policy benefits have been exhausted.", "N588": "The patient has instructed that medical claims/bills are not to be paid.", "N589": "Coverage is excluded to any person injured as a result of operating a motor vehicle while in an intoxicated condition or while the ability to operate such a vehicle is impaired by the use of a drug.", "N590": "Missing independent medical exam detailing the cause of injuries sustained and medical necessity of services rendered.", "N591": "Payment based on an Independent Medical Examination (IME) or Utilization Review (UR).", "N592": "Adjusted because this is not the initial prescription or exceeds the amount allowed for the initial prescription.", "N593": "Not covered based on failure to attend a scheduled Independent Medical Exam (IME).", "N594": "Records reflect the injured party did not complete an Application for Benefits for this loss.", "N595": "Records reflect the injured party did not complete an Assignment of Benefits for this loss.", "N596": "Records reflect the injured party did not complete a Medical Authorization for this loss.", "N597": "Adjusted based on a medical/dental provider's apportionment of care between related injuries and other unrelated medical/dental conditions/injuries.", "N598": "Health care policy coverage is primary.", "N599": "Our payment for this service is based upon a reasonable amount pursuant to both the terms and conditions of the policy of insurance under which the subject claim is being made as well as the Florida No-Fault Statute, which permits, when determining a reasonable charge for a service, an insurer to consider usual and customary charges and payments accepted by the provider, reimbursement levels in the community and various federal and state fee schedules applicable to automobile and other insurance coverages, and other information relevant to the reasonableness of the reimbursement for the service. The payment for this service is based upon 200% of the Participating Level of Medicare Part B fee schedule for the locale in which the services were rendered.", "N600": "Adjusted based on the applicable fee schedule for the region in which the service was rendered.", "N601": "In accordance with Hawaii Administrative Rules, Title 16, Chapter 23 Motor Vehicle Insurance Law payment is recommended based on Medicare Resource Based Relative Value Scale System applicable to Hawaii.", "N602": "Adjusted based on the Redbook maximum allowance.", "N603": "This fee is calculated according to the New Jersey medical fee schedules for Automobile Personal Injury Protection and Motor Bus Medical Expense Insurance Coverage.", "N604": "In accordance with New York No-Fault Law, Regulation 68, this base fee was calculated according to the New York Workers' Compensation Board Schedule of Medical Fees, pursuant to Regulation 83 and / or Appendix 17-C of 11 NYCRR.", "N605": "This fee was calculated based upon New York All Patients Refined Diagnosis Related Groups (APR-DRG), pursuant to Regulation 68.", "N606": "The Oregon allowed amount for this procedure is based upon the Workers Compensation Fee Schedule (OAR 436-009). The allowed amount has been calculated in accordance with Section 4 of ORS 742.524.", "N607": "Service provided for non-compensable condition(s).", "N608": "The fee schedule amount allowed is calculated at 110% of the Medicare Fee Schedule for this region, specialty and type of service. This fee is calculated in compliance with Act 6.", "N609": "80% of the provider's billed amount is being recommended for payment according to Act 6.", "N610": "Alert: Payment based on an appropriate level of care.", "N611": "Claim in litigation. Contact insurer for more information.", "N612": "Medical provider not authorized/certified to provide treatment to injured workers in this jurisdiction.", "N613": "Alert: Although this was paid, you have billed with an ordering provider that needs to update their enrollment record. Please verify that the ordering provider information you submitted on the claim is accurate and if it is, contact the ordering provider instructing them to update their enrollment record. Unless corrected, a claim with this ordering provider will not be paid in the future.", "N614": "Alert: Additional information is included in the 835 Healthcare Policy Identification Segment (loop 2110 Service Payment Information).", "N615": "Alert: This enrollee receiving advance payments of the premium tax credit is in the grace period of three consecutive months for non-payment of premium. Under 45 CFR 156.270, a Qualified Health Plan issuer must pay all appropriate claims for services rendered to the enrollee during the first month of the grace period and may pend claims for services rendered to the enrollee in the second and third months of the grace period.", "N616": "Alert: This enrollee is in the first month of the advance premium tax credit grace period.", "N617": "This enrollee is in the second or third month of the advance premium tax credit grace period.", "N618": "Alert: This claim will automatically be reprocessed if the enrollee pays their premiums.", "N619": "Coverage terminated for non-payment of premium.", "N620": "Alert: This procedure code is for quality reporting/informational purposes only", "N621": "Charges for Jurisdiction required forms, reports, or chart notes are not payable.", "N622": "Not covered based on the date of injury/accident.", "N623": "Not covered when deemed unscientific/unproven/outmoded/experimental/excessive/inappropriate.", "N624": "The associated Workers' Compensation claim has been withdrawn.", "N625": "Missing/Incomplete/Invalid Workers' Compensation Claim Number.", "N626": "New or established patient E/M codes are not payable with chiropractic care codes.", "N628": "Out-patient follow up visits on the same date of service as a scheduled test or treatment is disallowed.", "N629": "Reviews/documentation/notes/summaries/reports/charts not requested.", "N630": "Referral not authorized by attending physician.", "N631": "Medical Fee Schedule does not list this code. An allowance was made for a comparable service.", "N633": "Additional anesthesia time units are not allowed.", "N634": "The allowance is calculated based on anesthesia time units.", "N635": "The Allowance is calculated based on the anesthesia base units plus time.", "N636": "Adjusted because this is reimbursable only once per injury.", "N637": "Consultations are not allowed once treatment has been rendered by the same provider.", "N638": "Reimbursement has been made according to the home health fee schedule.", "N639": "Reimbursement has been made according to the inpatient rehabilitation facilities fee schedule.", "N640": "Exceeds number/frequency approved/allowed within time period.", "N641": "Reimbursement has been based on the number of body areas rated.", "N642": "Adjusted when billed as individual tests instead of as a panel.", "N643": "The services billed are considered Not Covered or Non-Covered (NC) in the applicable state fee schedule.", "N644": "Reimbursement has been made according to the bilateral procedure rule.", "N645": "Mark-up allowance.", "N646": "Reimbursement has been adjusted based on the guidelines for an assistant.", "N647": "Adjusted based on diagnosis-related group (DRG).", "N648": "Adjusted based on Stop Loss.", "N649": "Payment based on invoice.", "N650": "This policy was not in effect for this date of loss. No coverage is available.", "N651": "No Personal Injury Protection/Medical Payments Coverage on the policy at the time of the loss.", "N652": "The date of service is before the date of loss.", "N653": "The date of injury does not match the reported date of loss.", "N654": "Adjusted based on achievement of maximum medical improvement (MMI).", "N655": "Payment based on provider's geographic region.", "N656": "An interest payment is being made because benefits are being paid outside the statutory requirement.", "N657": "This should be billed with the appropriate code for these services.", "N658": "The billed service(s) are not considered medical expenses.", "N659": "This item is exempt from sales tax.", "N660": "Sales tax has been included in the reimbursement.", "N661": "Documentation does not support that the services rendered were medically necessary.", "N662": "Alert: Consideration of payment will be made upon receipt of a final bill.", "N663": "Adjusted based on an agreed amount.", "N664": "Adjusted based on a legal settlement.", "N665": "Services by an unlicensed provider are not reimbursable.", "N666": "Only one evaluation and management code at this service level is covered during the course of care.", "N667": "Missing prescription.", "N668": "Incomplete/invalid prescription.", "N669": "Adjusted based on the Medicare fee schedule.", "N670": "This service code has been identified as the primary procedure code subject to the Medicare Multiple Procedure Payment Reduction (MPPR) rule.", "N671": "Payment based on a jurisdiction cost-charge ratio.", "N672": "Alert: Amount applied to Health Insurance Offset.", "N673": "Reimbursement has been calculated based on an outpatient per diem or an outpatient factor and/or fee schedule amount.", "N674": "Not covered unless a pre-requisite procedure/service has been provided.", "N675": "Additional information is required from the injured party.", "N676": "Service does not qualify for payment under the Outpatient Facility Fee Schedule.", "N677": "Alert: Films/Images will not be returned.", "N678": "Missing post-operative images/visual field results.", "N679": "Incomplete/Invalid post-operative images/visual field results.", "N680": "Missing/Incomplete/Invalid date of previous dental extractions.", "N681": "Missing/Incomplete/Invalid full arch series.", "N682": "Missing/Incomplete/Invalid history of prior periodontal therapy/maintenance.", "N683": "Missing/Incomplete/Invalid prior treatment documentation.", "N684": "Payment denied as this is a specialty claim submitted as a general claim.", "N685": "Missing/Incomplete/Invalid Prosthesis, Crown or Inlay Code.", "N686": "Missing/incomplete/Invalid questionnaire needed to complete payment determination.", "N687": "Alert: This reversal is due to a retroactive disenrollment.", "N688": "Alert: This reversal is due to a medical or utilization review decision.", "N689": "Alert: This reversal is due to a retroactive rate change.", "N690": "Alert: This reversal is due to a provider submitted appeal.", "N691": "Alert: This reversal is due to a patient submitted appeal.", "N692": "Alert: This reversal is due to an incorrect rate on the initial adjudication.", "N693": "Alert: This reversal is due to a cancellation of the claim by the provider.", "N694": "Alert: This reversal is due to a resubmission/change to the claim by the provider.", "N695": "Alert: This reversal is due to incorrect patient financial responsibility information on the initial adjudication.", "N696": "Alert: This reversal is due to a Coordination of Benefits or Third Party Liability Recovery retroactive adjustment.", "N697": "Alert: This reversal is due to a payer's retroactive contract incentive program adjustment.", "N698": "Alert: This reversal is due to non-payment of the health insurance premiums (Health Insurance Exchange or other) by the end of the premium payment grace period, resulting in loss of coverage.", "N699": "Payment adjusted based on the Physician Quality Reporting System (PQRS) Incentive Program.", "N700": "Payment adjusted based on the Electronic Health Records (EHR) Incentive Program.", "N701": "Payment adjusted based on the Value-based Payment Modifier.", "N702": "Decision based on review of previously adjudicated claims or for claims in process for the same/similar type of services.", "N703": "This service is incompatible with previously adjudicated claims or claims in process.", "N704": "Alert: You may not appeal this decision but can resubmit this claim/service with corrected information if warranted.", "N705": "Incomplete/invalid documentation.", "N706": "Missing documentation.", "N707": "Incomplete/invalid orders.", "N708": "Missing orders.", "N709": "Incomplete/invalid notes.", "N710": "Missing notes.", "N711": "Incomplete/invalid summary.", "N712": "Missing summary.", "N713": "Incomplete/invalid report.", "N714": "Missing report.", "N715": "Incomplete/invalid chart.", "N716": "Missing chart.", "N717": "Incomplete/Invalid documentation of face-to-face examination.", "N718": "Missing documentation of face-to-face examination.", "N719": "Penalty applied based on plan requirements not being met.", "N720": "Alert: The patient overpaid you. You may need to issue the patient a refund for the difference between the patient's payment and the amount shown as patient responsibility on this notice.", "N721": "This service is only covered when performed as part of a clinical trial.", "N722": "Patient must use Workers' Compensation Set-Aside (WCSA) funds to pay for the medical service or item.", "N723": "Patient must use Liability set-aside (LSA) funds to pay for the medical service or item.", "N724": "Patient must use No-Fault set-aside (NFSA) funds to pay for the medical service or item.", "N725": "A liability insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.", "N726": "A conditional payment is not allowed.", "N727": "A no-fault insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.", "N728": "A workers' compensation insurer has reported having ongoing responsibility for medical services (ORM) for this diagnosis.", "N729": "Missing patient medical/dental record for this service.", "N730": "Incomplete/invalid patient medical/dental record for this service.", "N731": "Incomplete/Invalid mental health assessment.", "N732": "Services performed at an unlicensed facility are not reimbursable.", "N733": "Regulatory surcharges are paid directly to the state.", "N734": "The patient is eligible for these medical services only when unable to work or perform normal activities due to an illness or injury.", "N736": "Incomplete/invalid Sleep Study Report.", "N737": "Missing Sleep Study Report.", "N738": "Incomplete/invalid Vein Study Report.", "N739": "Missing Vein Study Report.", "N740": "The member's Consumer Spending Account does not contain sufficient funds to cover the member's liability for this claim/service.", "N741": "This is a site neutral payment.", "N743": "Adjusted because the services may be related to an employment accident.", "N744": "Adjusted because the services may be related to an auto/other accident.", "N745": "Missing Ambulance Report.", "N746": "Incomplete/invalid Ambulance Report.", "N747": "This is a misdirected claim/service. Submit the claim to the payer/plan where the patient resides.", "N748": "Adjusted because the related hospital charges have not been received.", "N749": "Missing Blood Gas Report.", "N750": "Incomplete/invalid Blood Gas Report.", "N751": "Adjusted because the patient is covered under a Medicare Part D plan.", "N752": "Missing/incomplete/invalid HIPPS Treatment Authorization Code (TAC).", "N753": "Missing/incomplete/invalid Attachment Control Number.", "N754": "Missing/incomplete/invalid Referring Provider or Other Source Qualifier on the 1500 Claim Form.", "N755": "Missing/incomplete/invalid ICD Indicator.", "N756": "Missing/incomplete/invalid point of drop-off address.", "N757": "Adjusted based on the Federal Indian Fees schedule (MLR).", "N758": "Adjusted based on the prior authorization decision.", "N759": "Payment adjusted based on the National Electrical Manufacturers Association (NEMA) Standard XR-29-2013.", "N760": "This facility is not authorized to receive payment for the service(s).", "N761": "This provider is not authorized to receive payment for the service(s).", "N762": "This facility is not certified for Tomosynthesis (3-D) mammography.", "N763": "The demonstration code is not appropriate for this claim; resubmit without a demonstration code.", "N764": "Missing/incomplete/invalid Hematocrit (HCT) value.", "N765": "This payer does not cover coinsurance assessed by a previous payer.", "N766": "This payer does not cover co-payment assessed by a previous payer.", "N767": "The Medicaid state requires provider to be enrolled in the member's Medicaid state program prior to any claim benefits being processed.", "N768": "Incomplete/invalid initial evaluation report.", "N769": "A lateral diagnosis is required.", "N770": "The adjustment request received from the provider has been processed. Your original claim has been adjusted based on the information received.", "N771": "Alert: Under Federal law you cannot charge more than the limiting charge amount.", "N772": "Alert: Rebill urgent/emergent and ancillary services separately.", "N773": "Drug supplied not obtained from specialty vendor.", "N774": "Alert: Refer to your Third Party Processor Agreement for specific information on fees associated with this payment type.", "N775": "Payment adjusted based on x-ray radiograph on film.", "N776": "This service is not a covered Telehealth service.", "N777": "Missing Assignment of Benefits Indicator.", "N778": "Missing Primary Care Physician Information.", "N779": "Replacement/Void claims cannot be submitted until the original claim has finalized. Please resubmit once payment or denial is received.", "N780": "Missing/incomplete/invalid end therapy date.", "N781": "Alert: Patient is a Medicaid/ Qualified Medicare Beneficiary. Review your records for any wrongfully collected deductible. This amount may be billed to a subsequent payer.", "N782": "Alert: Patient is a Medicaid/ Qualified Medicare Beneficiary. Review your records for any wrongfully collected coinsurance. This amount may be billed to a subsequent payer.", "N783": "Alert: Patient is a Medicaid/ Qualified Medicare Beneficiary. Review your records for any wrongfully collected copayment. This amount may be billed to a subsequent payer.", "N784": "Missing comprehensive procedure code.", "N785": "Missing current radiology film/images.", "N786": "Benefit limitation for the orthodontic active and/or retention phase of treatment.", "N787": "Alert: Under 42 CFR 410.43, an eligible Partial Hospitalization Program (PHP) patient/beneficiary requires a minimum of 20 hours of PHP services per week, as evidenced in the plan of care. PHP services must be furnished in accordance with the plan of care.", "N788": "Alert: The third-party administrator/review organization did not receive the required information.", "N789": "Clinical Trial is not a covered benefit.", "N790": "Provider/supplier not accredited for product/service.", "N791": "Missing history & physical report.", "N792": "Incomplete/invalid history & physical report.", "N794": "Payment adjusted based on type of technology used.", "N795": "Item must be resubmitted as a purchase.", "N796": "Missing/incomplete/invalid Hemoglobin (Hb or Hgb) value.", "N797": "Missing/incomplete/invalid date qualifier.", "N798": "Submit a void request for the original claim and resubmit a new claim.", "N799": "Submitted identifier must be an individual identifier, not group identifier.", "N800": "Only one service date is allowed per claim.", "N801": "Services performed in a Medicare participating or CAH facility under a self-insured tribal Group Health Plan, in accordance with Federal Regulation 42 CFR 136.", "N802": "This claim/service is not payable under our service area. The claim must be filed to the Payer/Plan in whose service area the Rendering Physician is located.", "N803": "Submission of the claim for the service rendered is the responsibility of the Contracted Medical Group or Hospital.", "N804": "Alert: The claim/service was processed through the Outpatient Code Editor (OCE).", "N805": "Alert: The claim/service was processed through the Correct Code Editor (CCE).", "N806": "Payment is included in the Global transplant allowance.", "N807": "Payment adjustment based on the Merit-based Incentive Payment System (MIPS).", "N808": "Not covered for this provider type / provider specialty.", "N809": "Alert: The fee schedule amount for this service was adjusted based on prior competitive bidding rates. For more information, contact your local contractor.", "N810": "Alert: Due to federal, state or local disaster declaration, this claim has been processed at the in-network level of benefit. At the conclusion or expiration of the disaster declaration, network payment rules will be reinstated.", "N811": "Missing Federal Sequestration Reduction from Prior Payer.", "N812": "The start service date through end service date cannot span greater than 18 months.", "N815": "Missing/Incomplete/Invalid NDC Unit Count", "N816": "Missing/Incomplete/Invalid NDC Unit of Measure", "N817": "Alert: Applicable laboratories are required to collect and report private payor data and report that data to CMS between January 1, 2020 - March 31, 2020.", "N818": "Claims Dates of Service do not match Electronic Visit Verification System.", "N819": "Patient not enrolled in Electronic Visit Verification System.", "N820": "Electronic Visit Verification System units do not meet requirements of visit.", "N821": "Electronic Visit Verification System visit not found.", "N822": "Missing procedure modifier(s).", "N823": "Incomplete/Invalid procedure modifier(s).", "N824": "Electronic Visit Verification (EVV) data must be submitted through EVV Vendor.", "N825": "Early intervention guidelines were not met.", "N826": "Pat<PERSON> did not meet the inclusion criteria for the Medicare Shared Savings Program.", "N827": "Missing/Incomplete/Invalid Federal Information Processing Standard (FIPS) Code.", "N828": "Alert: Payment is suppressed due to a contracted funding.", "N829": "Missing/incomplete/invalid Diagnostics Exchange Z-Code Identifier.", "N830": "Alert: The charge[s] for this service was processed in accordance with Federal/ State, Balance Billing/ No Surprise Billing regulations. As such, any amount identified with OA, CO, or PI cannot be collected from the member and may be considered provider liability or be billable to a subsequent payer. Any amount the provider collected over the identified PR amount must be refunded to the patient within applicable Federal/State timeframes. Payment amounts are eligible for dispute pursuant to any Federal/State documented appeal/grievance process(es).", "N831": "You have not responded to requests to revalidate your provider/supplier enrollment information.", "N832": "Duplicate occurrence code/occurrence span code.", "N833": "Patient share of cost waived.", "N834": "Jurisdiction exempt from sales and health tax charges.", "N835": "Unrelated Service/procedure/treatment is reduced. The balance of this charge is the patient's responsibility.", "N836": "Provider W9 or Payee Registration not on file.", "N837": "Alert: Missing modifier was added.", "N838": "Alert: Service/procedure postponed due to a federal, state, or local mandate/disaster declaration. Any amounts applied to deductible or member liability will be applied to the prior plan year from which the procedure was cancelled.", "N839": "The procedure code was added/changed because the level of service exceeds the compensable condition(s).", "N840": "Worker's compensation claim filed with a different state.", "N841": "Alert: North Dakota Administrative Rule 92-01-02-50.3.", "N842": "Alert: Patient cannot be billed for charges.", "N843": "Missing/incomplete/invalid Core-Based Statistical Area (CBSA) code.", "N844": "This claim, or a portion of this claim, was processed in accordance with the Nebraska Legislative LB997 July 24, 2020 - Out of Network Emergency Medical Care Act.", "N845": "Alert: Nebraska Legislative LB997 July 24, 2020 - Out of Network Emergency Medical Care Act.", "N846": "National Drug Code (NDC) supplied does not correspond to the HCPCs/CPT billed.", "N847": "National Drug Code (NDC) billed is obsolete.", "N848": "National Drug Code (NDC) billed cannot be associated with a product.", "N849": "Missing Tooth Clause: <PERSON><PERSON> missing prior to the member effective date.", "N850": "Missing/incomplete/invalid narrative explaining/describing this service/treatment.", "N851": "Payment reduced because services were furnished by a therapy assistant.", "N852": "The pay-to and rendering provider tax identification numbers (TINs) do not match", "N853": "The number of modalities performed per session exceeds our acceptable maximum.", "N854": "Alert: If you have primary other health insurance (OHI) coverage that has denied services, you must exhaust all appeal levels with your primary OHI before we can consider your claim for reimbursement.", "N855": "This coverage is subject to the exclusive jurisdiction of ERISA (1974), U.S.C. SEC 1001.", "N856": "This coverage is not subject to the exclusive jurisdiction of ERISA (1974), U.S.C. SEC 1001.", "N857": "This claim has been adjusted/reversed. Refund any collected copayment to the member.", "N858": "Alert: State regulations relating to an Out of Network Medical Emergency Care Act were applied to the processing of this claim. Payment amounts are eligible for dispute following the state's documented appeal/ grievance/ arbitration process.", "N859": "Alert: The Federal No Surprise Billing Act was applied to the processing of this claim. Payment amounts are eligible for dispute pursuant to any Federal documented appeal/ grievance/ dispute resolution process(es).", "N860": "Alert: The Federal No Surprise Billing Act Qualified Payment Amount (QPA) was used to calculate the member cost share(s).", "N861": "Alert: Mismatch between the submitted Patient Liability/Share of Cost and the amount on record for this recipient.", "N862": "Alert: Member cost share is in compliance with the No Surprises Act, and is calculated using the lesser of the QPA or billed charge.", "N863": "Alert: This claim is subject to the No Surprises Act (NSA). The amount paid is the final out-of-network rate and was calculated based on an All Payer Model Agreement, in accordance with the NSA.", "N864": "Alert: This claim is subject to the No Surprises Act provisions that apply to emergency services.", "N865": "Alert: This claim is subject to the No Surprises Act provisions that apply to nonemergency services furnished by nonparticipating providers during a patient visit to a participating facility.", "N866": "Alert: This claim is subject to the No Surprises Act provisions that apply to services furnished by nonparticipating providers of air ambulance services.", "N867": "Alert: Cost sharing was calculated based on a specified state law, in accordance with the No Surprises Act.", "N868": "Alert: Cost sharing was calculated based on an All-Payer Model Agreement, in accordance with the No Surprises Act.", "N869": "Alert: Cost sharing was calculated based on the qualifying payment amount, in accordance with the No Surprises Act.", "N870": "Alert: In accordance with the No Surprises Act, cost sharing was based on the billed amount because the billed amount was lower than the qualifying payment amount.", "N871": "Alert: This initial payment was calculated based on a specified state law, in accordance with the No Surprises Act.", "N872": "Alert: This final payment was calculated based on a specified state law, in accordance with the No Surprises Act.", "N873": "Alert: This final payment was calculated based on an All-Payer Model Agreement, in accordance with the No Surprises Act.", "N874": "Alert: This final payment was determined through open negotiation, in accordance with the No Surprises Act.", "N875": "Alert: This final payment equals the amount selected as the out-of-network rate by a Federal Independent Dispute Resolution Entity, in accordance with the No Surprises Act.", "N876": "Alert: This item or service is covered under the plan. This is a notice of denial of payment provided in accordance with the No Surprises Act. The provider or facility may initiate open negotiation if they desire to negotiate a higher out-of-network rate than the amount paid by the patient in cost sharing.", "N877": "Alert: This initial payment is provided in accordance with the No Surprises Act. The provider or facility may initiate open negotiation if they desire to negotiate a higher out-of-network rate.", "N878": "Alert: The provider or facility specified that notice was provided and consent to balance bill obtained, but notice and consent was not provided and obtained in a manner consistent with applicable Federal law. Thus, cost sharing and the total amount paid have been calculated based on the requirements under the No Surprises Act, and balance billing is prohibited.", "N879": "Alert: The notice and consent to balance bill, and to be charged out-of-network cost sharing, that was obtained from the patient with regard to the billed services, is not permitted for these services. Thus, cost sharing and the total amount paid have been calculated based on the requirements under the No Surprises Act, and balance billing is prohibited.", "N880": "Original claim closed due to changes in submitted data. Adjustment claim will be processed under a new claim number.", "N881": "Client Obligation, patient responsibility for Home & Community Based Services (HCBS)", "N882": "Alert: The out-of-network payment and cost sharing amounts were based on the plan's allowance because the provider or facility obtained the patient's consent to waive the balance billing protections under the No Surprises Act.", "N883": "Alert: Processed according to state law", "N884": "Alert: The No Surprises Act may apply to this claim. Please contact payer for instructions on how to submit information regarding whether or not the item or service was furnished during a patient visit to a participating facility.", "N885": "Alert: This claim was not processed in accordance with the No Surprises Act cost-sharing or out-of-network payment requirements. The payer disagrees with your determination that those requirements apply. You may contact the payer to find out why it disagrees. You may appeal this adverse determination on behalf of the patient through the payerÕs internal appeals and external review processes.", "N886": "Alert: A Health Care Claim Request for Additional Information (277 RFAI) has been sent.", "N887": "Providers not participating in the Medicare Advantage Plan have the right to appeal if the plan has partially or fully denied payment or if the provider believes the plan has not paid the services at the expected Medicare reimbursable rate or type of level/service. Providers may file their appeal in writing within 60 calendar days after the date of the remittance advice. For the plan to review the appeal, the plan will need a completed signed Waiver of Liability Statement. To obtain a Waiver of Liability form, please contact your Medicare Advantage Plan. Once we receive the completed forms, we will give you a decision on your appeal within 60 calendar days.", "N888": "Alert: An electronic request for additional information has been sent for this claim.", "N889": "Alert: This claim was originally processed in real-time, and we sent a real-time 835 response.", "N890": "Electronic Visit Verification Data Element Requirements were not met.", "N891": "The maximum allowable payment for this service/procedure was paid by the primary insurance. No further payment due.", "N892": "The claim does not meet the criteria for acceptable use of the Delay Reason Code.", "N893": "Missing/incomplete/invalid child medical evaluation form/checklist.", "N894": "Alert: These payments are made subject to a reservation of rights for the Payor to recoup or otherwise recover all or part of these payments based on any of the following: outcome of pending or future litigation/ new or updated state, federal or regulatory guidance/ any other actions that may affect the Payor's obligation to make these payments.", "N895": "Processed based on a negotiated fee schedule for a specialty drug program.", "N896": "Missing/incomplete/invalid trauma activation sheet.", "N897": "Missing/incomplete/invalid proof of member payment.", "N898": "Missing/incomplete/invalid Resource Utilization Group(s) (RUG) code(s).", "N899": "Missing Initial Evaluation Report.", "N900": "Missing Therapy Notes/Report.", "N901": "Incomplete/Invalid Therapy Notes/Report.", "N902": "Missing Health Risk Assessment (HRA).", "N903": "Incomplete/Invalid Health Risk Assessment (HRA).", "N904": "The transportation vendor is responsible for this claim."}, "NM102": {"1": "Person - The entity is an individual person.", "2": "Non-Person Entity - The entity is an organization or other non-person entity."}, "NM108-2320": {"II": "Standard Unique HealthIdentifier", "MI": "Member Identification Number"}, "NM108-2330B": {"PI": "Payer Identification", "XV": "Centers for Medicare/Medicaid Service Plan ID"}, "PAT01": {"01": "Spouse - The patient is the spouse of the subscriber.", "19": "Child - The patient is the child of the subscriber.", "20": "Employee - The patient is the employee of the subscriber.", "39": "Organ Donor - The patient is an organ donor related to the subscriber.", "40": "Cadaver Donor - The patient is a cadaver donor related to the subscriber.", "53": "Life Partner - The patient is the life partner of the subscriber.", "G8": "Other Relationship - The patient has some other relationship to the subscriber."}, "ISA15": {"T": "Test", "P": "Production"}, "NM101": {"BillingProvider": "Billing Provider", "ReferringProvider": "Referring Provider", "RenderingProvider": "Rendering Provider", "OrderingProvider": "Ordering Provider", "SupervisingProvider": "Supervising Provider"}, "OI03-2320": {"Y": "Yes - Benefits have been assigned to the provider.", "N": "No - Benefits have not been assigned to the provider.", "W": "Not Applicable - Assignment of benefits is not applicable."}, "OI04-2320": {"Y": "Yes - The provider has a signature on file from the patient.", "N": "No - The provider does not have a signature on file from the patient.", "P": "Signature Not Required - A patient signature is not required."}, "OI06-2320": {"Y": "Yes - The provider has a signed statement on file permitting the release of medical information.", "N": "No - The provider does not have a signed statement on file.", "I": "Informed Consent - The provider has informed consent to release medical information for conditions regulated by federal statutes."}, "PRV03": {"193200000X": "Multi-Specialty Group", "193400000X": "Single Specialty Group", "207K00000X": "Allergy & Immunology Physician", "207KA0200X": "Allergy Physician", "207KI0005X": "Clinical & Laboratory Immunology (Allergy & Immunology) Physician", "207L00000X": "Anesthesiology Physician", "207LA0401X": "Addiction Medicine (Anesthesiology) Physician", "207LC0200X": "Critical Care Medicine (Anesthesiology) Physician", "207LH0002X": "Hospice and Palliative Medicine (Anesthesiology) Physician", "207LP2900X": "Pain Medicine (Anesthesiology) Physician", "207LP3000X": "Pediatric Anesthesiology Physician", "208U00000X": "Clinical Pharmacology Physician", "208C00000X": "Colon & Rectal Surgery Physician", "207N00000X": "Dermatology Physician", "207NI0002X": "Clinical & Laboratory Dermatological Immunology Physician", "207ND0900X": "Dermatopathology Physician", "207ND0101X": "MOHS-Micrographic Surgery Physician", "207NP0225X": "Pediatric Dermatology Physician", "207NS0135X": "Procedural Dermatology Physician", "204R00000X": "Electrodiagnostic Medicine Physician", "207P00000X": "Emergency Medicine Physician", "207PE0004X": "Emergency Medical Services (Emergency Medicine) Physician", "207PH0002X": "Hospice and Palliative Medicine (Emergency Medicine) Physician", "207PT0002X": "Medical Toxicology (Emergency Medicine) Physician", "207PP0204X": "Pediatric Emergency Medicine (Emergency Medicine) Physician", "207PS0010X": "Sports Medicine (Emergency Medicine) Physician", "207PE0005X": "Undersea and Hyperbaric Medicine (Emergency Medicine) Physician", "207Q00000X": "Family Medicine Physician", "207QA0401X": "Addiction Medicine (Family Medicine) Physician", "207QA0000X": "Adolescent Medicine (Family Medicine) Physician", "207QA0505X": "Adult Medicine Physician", "207QG0300X": "Geriatric Medicine (Family Medicine) Physician", "207QH0002X": "Hospice and Palliative Medicine (Family Medicine) Physician", "207QB0002X": "Obesity Medicine (Family Medicine) Physician", "207QS1201X": "Sleep Medicine (Family Medicine) Physician", "207QS0010X": "Sports Medicine (Family Medicine) Physician", "208D00000X": "General Practice Physician", "208M00000X": "Hospitalist Physician", "202C00000X": "Independent Medical Examiner Physician", "202D00000X": "Integrative Medicine Physician", "207R00000X": "Internal Medicine Physician", "207RA0401X": "Addiction Medicine (Internal Medicine) Physician", "207RA0000X": "Adolescent Medicine (Internal Medicine) Physician", "207RA0002X": "Adult Congenital Heart Disease Physician", "207RA0001X": "Advanced Heart Failure and Transplant Cardiology Physician", "207RA0201X": "Allergy & Immunology (Internal Medicine) Physician", "207RC0000X": "Cardiovascular Disease Physician", "207RI0001X": "Clinical & Laboratory Immunology (Internal Medicine) Physician", "207RC0001X": "Clinical Cardiac Electrophysiology Physician", "207RC0200X": "Critical Care Medicine (Internal Medicine) Physician", "207RE0101X": "Endocrinology & Diabetes & Metabolism Physician", "207RG0100X": "Gastroenterology Physician", "207RG0300X": "Geriatric Medicine (Internal Medicine) Physician", "207RH0000X": "Hematology (Internal Medicine) Physician", "207RH0003X": "Hematology & Oncology Physician", "207RI0008X": "Hepatology Physician", "207RH0002X": "Hospice and Palliative Medicine (Internal Medicine) Physician", "207RH0005X": "Hypertension Specialist Physician", "207RI0200X": "Infectious Disease Physician", "207RI0011X": "Interventional Cardiology Physician", "207RM1200X": "Magnetic Resonance Imaging (MRI) Internal Medicine Physician", "207RX0202X": "Medical Oncology Physician", "207RN0300X": "Nephrology Physician", "207RB0002X": "Obesity Medicine (Internal Medicine) Physician", "207RP1001X": "Pulmonary Disease Physician", "207RR0500X": "Rheumatology Physician", "207RS0012X": "Sleep Medicine (Internal Medicine) Physician", "207RS0010X": "Sports Medicine (Internal Medicine) Physician", "207RT0003X": "Transplant Hepatology Physician", "209800000X": "Legal Medicine (M.D./D.O.) Physician", "207SG0202X": "Clinical Biochemical Genetics Physician", "207SC0300X": "Clinical Cytogenetics Physician", "207SG0201X": "Clinical Genetics (M.D.) Physician", "207SG0203X": "Clinical Molecular Genetics Physician", "207SG0207X": "Medical Biochemical Genetics", "207SM0001X": "Molecular Genetic Pathology (Medical Genetics) Physician", "207SG0205X": "Ph.D. Medical Genetics Physician", "207T00000X": "Neurological Surgery Physician", "204D00000X": "Neuromusculoskeletal Medicine & OMM Physician", "204C00000X": "Sports Medicine (Neuromusculoskeletal Medicine) Physician", "207U00000X": "Nuclear Medicine Physician", "207UN0903X": "In Vivo & In Vitro Nuclear Medicine Physician", "207UN0901X": "Nuclear Cardiology Physician", "207UN0902X": "Nuclear Imaging & Therapy Physician", "207V00000X": "Obstetrics & Gynecology Physician", "207VC0300X": "Complex Family Planning Physician", "207VC0200X": "Critical Care Medicine (Obstetrics & Gynecology) Physician", "207VF0040X": "Urogynecology and Reconstructive Pelvic Surgery (Obstetrics & Gynecology) Physician", "207VX0201X": "Gynecologic Oncology Physician", "207VG0400X": "Gynecology Physician", "207VH0002X": "Hospice and Palliative Medicine (Obstetrics & Gynecology) Physician", "207VM0101X": "Maternal & Fetal Medicine Physician", "207VB0002X": "Obesity Medicine (Obstetrics & Gynecology) Physician", "207VX0000X": "Obstetrics Physician", "207VE0102X": "Reproductive Endocrinology Physician", "207W00000X": "Ophthalmology Physician", "207WX0120X": "Cornea and External Diseases Specialist Physician", "207WX0009X": "Glaucoma Specialist (Ophthalmology) Physician", "207WX0109X": "Neuro-ophthalmology Physician", "207WX0200X": "Ophthalmic Plastic and Reconstructive Surgery Physician", "207WX0110X": "Pediatric Ophthalmology and Strabismus Specialist Physician Physician", "207WX0107X": "<PERSON><PERSON> <PERSON> (Ophthalmology) Physician", "207WX0108X": "Uveitis and Ocular Inflammatory Disease (Ophthalmology) Physician", "204E00000X": "Oral & Maxillofacial Surgery (D.M.D.)", "207X00000X": "Orthopaedic Surgery Physician", "207XS0114X": "Adult Reconstructive Orthopaedic Surgery Physician", "207XX0004X": "Orthopaedic Foot and Ankle Surgery Physician", "207XS0106X": "Orthopaedic Hand Surgery Physician", "207XS0117X": "Orthopaedic Surgery of the Spine Physician", "207XX0801X": "Orthopaedic Trauma Physician", "207XP3100X": "Pediatric Orthopaedic Surgery Physician", "207XX0005X": "Sports Medicine (Orthopaedic Surgery) Physician", "207Y00000X": "Otolaryngology Physician", "207YS0123X": "Facial Plastic Surgery Physician", "207YX0602X": "Otolaryngic Allergy Physician", "207YX0905X": "Otolaryngology/Facial Plastic Surgery Physician", "207YX0901X": "Otology & Neurotology Physician", "207YP0228X": "Pediatric Otolaryngology Physician", "207YX0007X": "Plastic Surgery within the Head & Neck (Otolaryngology) Physician", "207YS0012X": "Sleep Medicine (Otolaryngology) Physician", "208VP0014X": "Interventional Pain Medicine Physician", "208VP0000X": "Pain Medicine Physician", "207ZP0101X": "Anatomic Pathology Physician", "207ZP0102X": "Anatomic Pathology & Clinical Pathology Physician", "207ZB0001X": "Blood Banking & Transfusion Medicine Physician", "207ZP0104X": "Chemical Pathology Physician", "207ZC0008X": "Clinical Informatics (Pathology) Physician", "207ZC0006X": "Clinical Pathology Physician", "207ZP0105X": "Clinical Pathology/Laboratory Medicine Physician", "207ZC0500X": "Cytopathology Physician", "207ZD0900X": "Dermatopathology (Pathology) Physician", "207ZF0201X": "Forensic Pathology Physician", "207ZH0000X": "Hematology (Pathology) Physician", "207ZI0100X": "Immunopathology Physician", "207ZM0300X": "Medical Microbiology Physician", "207ZP0007X": "Molecular Genetic Pathology (Pathology) Physician", "207ZN0500X": "Neuropathology Physician", "207ZP0213X": "Pediatric Pathology Physician", "208000000X": "Pediatrics Physician", "2080A0000X": "Pediatric Adolescent Medicine Physician", "2080C0008X": "Child Abuse Pediatrics Physician", "2080I0007X": "Pediatric Clinical & Laboratory Immunology Physician", "2080P0006X": "Developmental - Behavioral Pediatrics Physician", "2080H0002X": "Pediatric Hospice and Palliative Medicine Physician", "2080T0002X": "Pediatric Medical Toxicology Physician", "2080N0001X": "Neonatal-Perinatal Medicine Physician", "2080P0008X": "Pediatric Neurodevelopmental Disabilities Physician", "2080B0002X": "Pediatric Obesity Medicine Physician", "2080P0201X": "Pediatric Allergy/Immunology Physician", "2080P0202X": "Pediatric Cardiology Physician", "2080P0203X": "Pediatric Critical Care Medicine Physician", "2080P0204X": "Pediatric Emergency Medicine (Pediatrics) Physician", "2080P0205X": "Pediatric Endocrinology Physician", "2080P0206X": "Pediatric Gastroenterology Physician", "2080P0207X": "Pediatric Hematology & Oncology Physician", "2080P0208X": "Pediatric Infectious Diseases Physician", "2080P0210X": "Pediatric Nephrology Physician", "2080P0214X": "Pediatric Pulmonology Physician", "2080P0216X": "Pediatric Rheumatology Physician", "2080T0004X": "Pediatric Transplant Hepatology Physician", "2080S0012X": "Pediatric Sleep Medicine Physician", "2080S0010X": "Pediatric Sports Medicine Physician", "202K00000X": "Phlebology Physician", "208100000X": "Physical Medicine & Rehabilitation Physician", "2081P0301X": "Brain Injury Medicine (Physical Medicine & Rehabilitation) Physician", "2081H0002X": "Hospice and Palliative Medicine (Physical Medicine & Rehabilitation) Physician", "2081N0008X": "Neuromuscular Medicine (Physical Medicine & Rehabilitation) Physician", "2081P2900X": "Pain Medicine (Physical Medicine & Rehabilitation) Physician", "2081P0010X": "Pediatric Rehabilitation Medicine Physician", "2081P0004X": "Spinal Cord Injury Medicine Physician", "2081S0010X": "Sports Medicine (Physical Medicine & Rehabilitation) Physician", "208200000X": "Plastic Surgery Physician", "2082S0099X": "Plastic Surgery Within the Head and Neck (Plastic Surgery) Physician", "2082S0105X": "Surgery of the Hand (Plastic Surgery) Physician", "2083A0300X": "Addiction Medicine (Preventive Medicine) Physician", "2083A0100X": "Aerospace Medicine Physician", "2083C0008X": "Clinical Informatics Physician", "2083T0002X": "Medical Toxicology (Preventive Medicine) Physician", "2083B0002X": "Obesity Medicine (Preventive Medicine) Physician", "2083X0100X": "Occupational Medicine Physician", "2083P0500X": "Preventive Medicine/Occupational Environmental Medicine Physician", "2083P0901X": "Public Health & General Preventive Medicine Physician", "2083S0010X": "Sports Medicine (Preventive Medicine) Physician", "2083P0011X": "Undersea and Hyperbaric Medicine (Preventive Medicine) Physician", "2084A0401X": "Addiction Medicine (Psychiatry & Neurology) Physician", "2084P0802X": "Addiction Psychiatry Physician", "2084B0040X": "Behavioral Neurology & Neuropsychiatry Physician", "2084P0301X": "Brain Injury Medicine (Psychiatry & Neurology) Physician", "2084P0804X": "Child & Adolescent Psychiatry Physician", "2084N0600X": "Clinical Neurophysiology Physician", "2084D0003X": "Diagnostic Neuroimaging (Psychiatry & Neurology) Physician", "2084E0001X": "Epilepsy Physician", "2084F0202X": "Forensic Psychiatry Physician", "2084P0805X": "Geriatric Psychiatry Physician", "2084H0002X": "Hospice and Palliative Medicine (Psychiatry & Neurology) Physician", "2084A2900X": "Neurocritical Care Physician", "2084P0005X": "Neurodevelopmental Disabilities Physician", "2084N0400X": "Neurology Physician", "2084N0402X": "Neurology with Special Qualifications in Child Neurology Physician", "2084N0008X": "Neuromuscular Medicine (Psychiatry & Neurology) Physician", "2084B0002X": "Obesity Medicine (Psychiatry & Neurology) Physician", "2084P2900X": "Pain Medicine (Psychiatry & Neurology) Physician", "2084P0800X": "Psychiatry Physician", "2084P0015X": "Psychosomatic Medicine Physician", "2084S0012X": "Sleep Medicine (Psychiatry & Neurology) Physician", "2084S0010X": "Sports Medicine (Psychiatry & Neurology) Physician", "2084V0102X": "Vascular Neurology Physician", "2085B0100X": "Body Imaging Physician", "2085D0003X": "Diagnostic Neuroimaging (Radiology) Physician", "2085R0202X": "Diagnostic Radiology Physician", "2085U0001X": "Diagnostic Ultrasound Physician", "2085H0002X": "Hospice and Palliative Medicine (Radiology) Physician", "2085N0700X": "Neuroradiology Physician", "2085N0904X": "Nuclear Radiology Physician", "2085P0229X": "Pediatric Radiology Physician", "2085R0001X": "Radiation Oncology Physician", "2085R0205X": "Radiological Physics Physician", "2085R0203X": "Therapeutic Radiology Physician", "2085R0204X": "Vascular & Interventional Radiology Physician", "208600000X": "Surgery Physician", "2086H0002X": "Hospice and Palliative Medicine (Surgery) Physician", "2086S0120X": "Pediatric Surgery Physician", "2086S0122X": "Plastic and Reconstructive Surgery Physician", "2086S0105X": "Surgery of the Hand (Surgery) Physician", "2086S0102X": "Surgical Critical Care Physician", "2086X0206X": "Surgical Oncology Physician", "2086S0127X": "Trauma Surgery Physician", "2086S0129X": "Vascular Surgery Physician", "208G00000X": "Thoracic Surgery (Cardiothoracic Vascular Surgery) Physician", "204F00000X": "Transplant Surgery Physician", "208800000X": "Urology Physician", "2088F0040X": "Urogynecology and Reconstructive Pelvic Surgery (Urology) Physician", "2088P0231X": "Pediatric Urology Physician", "106E00000X": "Assistant Behavior Analyst", "106S00000X": "Behavior Technician", "103K00000X": "Behavior Analyst", "103G00000X": "Clinical Neuropsychologist", "103GC0700X": "Deactivated - Clinical Neuropsychologist", "101Y00000X": "Counselor", "101YA0400X": "Addiction (Substance Use Disorder) Counselor", "101YM0800X": "Mental Health Counselor", "101YP1600X": "Pastoral Counselor", "101YP2500X": "Professional Counselor", "101YS0200X": "School Counselor", "101200000X": "Drama Therapist", "106H00000X": "Marriage & Family Therapist", "102X00000X": "Poetry Therapist", "102L00000X": "Psychoanalyst", "103T00000X": "Psychologist", "103TA0400X": "Addiction (Substance Use Disorder) Psychologist ", "103TA0700X": "Adult Development & Aging Psychologist", "103TC0700X": "Clinical Psychologist", "103TC2200X": "Clinical Child & Adolescent Psychologist", "103TB0200X": "Cognitive & Behavioral Psychologist", "103TC1900X": "Counseling Psychologist", "103TE1000X": "Deactivated - Psychologist", "103TE1100X": "Exercise & Sports Psychologist", "103TF0000X": "Family Psychologist", "103TF0200X": "Forensic Psychologist", "103TP2701X": "Group Psychotherapy Psychologist", "103TH0004X": "Health Psychologist", "103TH0100X": "Health Service Psychologist", "103TM1700X": "Deactivated - Psychologist Men & Masculinity", "103TM1800X": "Intellectual & Developmental Disabilities Psychologist", "103TP0016X": "Prescribing (Medical) Psychologist", "103TP0814X": "Psychoanalysis Psychologist", "103TP2700X": "Deactivated - Psychologist Psychotherap<PERSON>", "103TR0400X": "Rehabilitation Psychologist", "103TS0200X": "School Psychologist", "103TW0100X": "Deactivated - Psychotherapy Women", "104100000X": "Social Worker", "1041C0700X": "Clinical Social Worker", "1041S0200X": "School Social Worker", "111N00000X": "Chiropractor", "111NI0013X": "Independent Medical Examiner Chiropractor", "111NI0900X": "Internist Chiropractor", "111NN0400X": "Neurology Chiropractor", "111NN1001X": "Nutrition Chiropractor", "111NX0100X": "Occupational Health Chiropractor", "111NX0800X": "Orthopedic Chiropractor", "111NP0017X": "Pediatric Chiropractor", "111NR0200X": "Radiology Chiropractor", "111NR0400X": "Rehabilitation Chiropractor", "111NS0005X": "Sports Physician Chiropractor", "111NT0100X": "Thermography Chiropractor", "125K00000X": "Advanced Practice Dental Therapist", "126800000X": "Dental Assistant", "124Q00000X": "Dental Hygienist", "126900000X": "Dental Laboratory Technician", "125J00000X": "Dental Therapist", "122300000X": "Dentist", "1223D0001X": "Public Health Dentist", "1223D0004X": "Dentist Anesthesiologist", "1223E0200X": "Endodontist", "1223G0001X": "General Practice Dentistry", "1223P0106X": "Oral and Maxillofacial Pathology Dentist", "1223X0008X": "Oral and Maxillofacial Radiology Dentist", "1223S0112X": "Oral and Maxillofacial Surgery (Dentist)", "125Q00000X": "Oral Medicine", "1223X2210X": "Orofacial Pain Dentist", "1223X0400X": "Orthodontics and Dentofacial Orthopedic Dentist", "1223P0221X": "Pediatric Dentist", "1223P0300X": "Periodontist", "1223P0700X": "Prosthodontist", "122400000X": "Denturist", "132700000X": "Dietary Manager", "136A00000X": "Registered Dietetic Technician", "133V00000X": "Registered Dietitian", "133VN1101X": "Gerontological Nutrition Registered Dietitian", "133VN1006X": "Metabolic Nutrition Registered Dietitian", "133VN1201X": "Obesity and Weight Management Nutrition Registered Dietitian", "133VN1301X": "Oncology Nutrition Registered Dietitian", "133VN1004X": "Pediatric Nutrition Registered Dietitian", "133VN1401X": "Pediatric Critical Care Nutrition Registered Dietitian", "133VN1005X": "Renal Nutrition Registered Dietitian", "133VN1501X": "Sports Dietetics Nutrition Registered Dietitian", "133N00000X": "Nutritionist", "133NN1002X": "Nutrition Education Nutritionist", "146N00000X": "Basic Emergency Medical Technician", "146M00000X": "Intermediate Emergency Medical Technician", "146L00000X": "Paramedic", "146D00000X": "Personal Emergency Response Attendant", "152W00000X": "Optometrist", "152WC0802X": "Corneal and Contact Management Optometrist", "152WL0500X": "Low Vision Rehabilitation Optometrist", "152WX0102X": "Occupational Vision Optometrist", "152WP0200X": "Pediatric Optometrist", "152WS0006X": "Sports Vision Optometrist", "152WV0400X": "Vision Therapy Optometrist", "156F00000X": "Technician/Technologist", "156FC0800X": "Contact Lens Technician/Technologist", "156FC0801X": "Contact Lens Fitter", "156FX1700X": "Ocularist", "156FX1100X": "Ophthalmic Technician/Technologist", "156FX1101X": "Ophthalmic Assistant ", "156FX1800X": "Optician", "156FX1201X": "Optometric Assistant Technician", "156FX1202X": "Optometric Technician", "156FX1900X": "Orthoptist", "164W00000X": "Licensed Practical Nurse", "167G00000X": "Licensed Psychiatric Technician", "164X00000X": "Licensed Vocational Nurse", "163W00000X": "Registered Nurse", "163WA0400X": "Addiction (Substance Use Disorder) Registered Nurse", "163WA2000X": "Administrator Registered Nurse", "163WP2201X": "Ambulatory Care Registered Nurse", "163WC3500X": "Cardiac Rehabilitation Registered Nurse", "163WC0400X": "Case Management Registered Nurse", "163WC1400X": "College Health Registered Nurse", "163WC1500X": "Community Health Registered Nurse", "163WC2100X": "Continence Care Registered Nurse", "163WC1600X": "Continuing Education/Staff Development Registered Nurse", "163WC0200X": "Critical Care Medicine Registered Nurse", "163WD0400X": "Diabetes Educator Registered Nurse", "163WD1100X": "Peritoneal Dialysis Registered Nurse", "163WE0003X": "Emergency Registered Nurse", "163WE0900X": "Enterostomal Therapy Registered Nurse", "163WF0300X": "Flight Registered Nurse", "163WG0100X": "Gastroenterology Registered Nurse", "163WG0000X": "General Practice Registered Nurse", "163WG0600X": "Gerontology Registered Nurse", "163WH0500X": "Hemodialysis Registered Nurse", "163WH0200X": "Home Health Registered Nurse", "163WH1000X": "Hospice Registered Nurse", "163WI0600X": "Infection Control Registered Nurse", "163WI0500X": "Infusion Therapy Registered Nurse", "163WL0100X": "Lactation Consultant (Registered Nurse)", "163WM0102X": "Mat<PERSON><PERSON> Registered Nurse", "163WM0705X": "Medical-Surgical Registered Nurse", "163WN0002X": "Neonatal Intensive Care Registered Nurse", "163WN0003X": "Low-Risk Neonatal Registered Nurse", "163WN0300X": "Nephrology Registered Nurse", "163WN0800X": "Neuroscience Registered Nurse", "163WM1400X": "Nurse Massage Therapist (NMT)", "163WN1003X": "Nutrition Support Registered Nurse", "163WX0002X": "High-Risk Obstetric Registered Nurse", "163WX0003X": "Inpatient Obstetric Registered Nurse", "163WX0106X": "Occupational Health Registered Nurse", "163WX0200X": "Oncology Registered Nurse", "163WX1100X": "Ophthalmic Registered Nurse", "163WX0800X": "Orthopedic Registered Nurse", "163WX1500X": "Ostomy Care Registered Nurse", "163WX0601X": "Otorhinolaryngology & Head-Neck Registered Nurse", "163WP0000X": "Pain Management Registered Nurse", "163WP0218X": "Pediatric Oncology Registered Nurse", "163WP0200X": "Pediatric Registered Nurse", "163WP1700X": "Perinatal Registered Nurse", "163WS0121X": "Plastic Surgery Registered Nurse", "163WP0808X": "Psychiatric/Mental Health Registered Nurse", "163WP0809X": "Adult Psychiatric/Mental Health Registered Nurse", "163WP0807X": "Child & Adolescent Psychiatric/Mental Health Registered Nurse", "163WR0006X": "Registered Nurse First Assistant", "163WR0400X": "Rehabilitation Registered Nurse", "163WR1000X": "Reproductive Endocrinology/Infertility Registered Nurse", "163WS0200X": "School Registered Nurse", "163WU0100X": "Urology Registered Nurse", "163WW0101X": "Ambulatory Women's Health Care Registered Nurse", "163WW0000X": "Wound Care Registered Nurse", "372600000X": "Adult Companion", "372500000X": "Chore Provider", "373H00000X": "Day Training/Habilitation Specialist", "374J00000X": "<PERSON><PERSON>", "374U00000X": "Home Health Aide", "376J00000X": "Homemaker", "376K00000X": "Nurse's Aide", "376G00000X": "Nursing Home Administrator", "374T00000X": "Religious Nonmedical Nursing Personnel", "374K00000X": "Religious Nonmedical Practitioner", "374700000X": "Technician", "3747A0650X": "Attendant Care Provider", "3747P1801X": "Personal Care Attendant", "171100000X": "Acupuncturist", "171M00000X": "Case Manager/Care Coordinator", "174V00000X": "Clinical Ethicist", "172V00000X": "Community Health Worker", "171W00000X": "Contractor", "171WH0202X": "Home Modifications Contractor", "171WV0202X": "Vehicle Modifications Contractor", "172A00000X": "Driver", "176P00000X": "Funeral Director", "170300000X": "<PERSON><PERSON> Counselor (M.S.)", "171400000X": "Health & Wellness Coach", "174H00000X": "Health Educator", "175L00000X": "Homeopath", "171R00000X": "Interpreter", "174N00000X": "Lactation Consultant (Non-RN)", "175M00000X": "Lay Midwife", "173000000X": "Legal Medicine", "172M00000X": "Mechanotherapist", "176B00000X": "Midwife", "171000000X": "Military Health Care Provider", "1710I1002X": "Independent Duty Corpsman", "1710I1003X": "Independent Duty Medical Technicians", "172P00000X": "<PERSON><PERSON><PERSON>", "175F00000X": "<PERSON><PERSON><PERSON>", "175T00000X": "Peer Specialist", "170100000X": "Ph.D. Medical Genetics", "405300000X": "Prevention Professional", "173C00000X": "Reflexologist", "173F00000X": "Sleep Specialist (PhD)", "174400000X": "Specialist", "1744G0900X": "Graphics Designer", "1744P3200X": " Prosthetics Case Management", "1744R1103X": "Research Study Abstracter/Coder", "1744R1102X": "Research Study Specialist", "174M00000X": "Veterinarian", "174MM1900X": "Medical Research Veterinarian", "183500000X": "Pharmacist", "1835P2201X": "Ambulatory Care Pharmacist", "1835C0206X": "Cardiology Pharmacist", "1835C0207X": "Compounded Sterile Preparations Pharmacist", "1835C0205X": "Critical Care Pharmacist", "1835E0208X": "Emergency Medicine Pharmacist", "1835G0000X": "Deactivated - Pharmacist", "1835G0303X": "Geriatric Pharmacist", "1835I0206X": "Infectious Diseases Pharmacist", "1835N0905X": "Nuclear Pharmacist", "1835N1003X": "Nutrition Support Pharmacist", "1835X0200X": "Oncology Pharmacist", "1835P0200X": "Pediatric Pharmacist", "1835P0018X": "Pharmacist Clinician (PhC)/ Clinical Pharmacy Specialist", "1835P1200X": "Pharmacotherapy Pharmacist", "1835P1300X": "Psychiatric Pharmacist", "1835S0206X": "Solid Organ Transplant Pharmacist", "183700000X": "Pharmacy Technician", "367A00000X": "Advanced Practice Midwife", "367H00000X": "Anesthesiologist Assistant", "364S00000X": "Clinical Nurse Specialist", "364SA2100X": "Acute Care Clinical Nurse Specialist", "364SA2200X": "Adult Health Clinical Nurse Specialist", "364SC2300X": "Chronic Care Clinical Nurse Specialist", "364SC1501X": "Community Health/Public Health Clinical Nurse Specialist", "364SC0200X": "Critical Care Medicine Clinical Nurse Specialist", "364SE0003X": "Emergency Clinical Nurse Specialist", "364SE1400X": "Ethics Clinical Nurse Specialist", "364SF0001X": "Family Health Clinical Nurse Specialist", "364SG0600X": "Gerontology Clinical Nurse Specialist", "364SH1100X": "Holistic Clinical Nurse Specialist", "364SH0200X": "Home Health Clinical Nurse Specialist", "364SI0800X": "Informatics Clinical Nurse Specialist", "364SL0600X": "Long-Term Care Clinical Nurse Specialist", "364SM0705X": "Medical-Surgical Clinical Nurse Specialist", "364SN0000X": "Neonatal Clinical Nurse Specialist", "364SN0800X": "Neuroscience Clinical Nurse Specialist", "364SX0106X": "Occupational Health Clinical Nurse Specialist", "364SX0200X": "Oncology Clinical Nurse Specialist", "364SX0204X": "Pediatric Oncology Clinical Nurse Specialist", "364SP0200X": "Pediatric Clinical Nurse Specialist", "364SP1700X": "Perinatal Clinical Nurse Specialist", "364SP2800X": "Perioperative Clinical Nurse Specialist", "364SP0808X": "Psychiatric/Mental Health Clinical Nurse Specialist", "364SP0809X": "Adult Psychiatric/Mental Health Clinical Nurse Specialist", "364SP0807X": "Child & Adolescent Psychiatric/Mental Health Clinical Nurse Specialist", "364SP0810X": "Child & Family Psychiatric/Mental Health Clinical Nurse Specialist", "364SP0811X": "Chronically Ill Psychiatric/Mental Health Clinical Nurse Specialist", "364SP0812X": "Community Psychiatric/Mental Health Clinical Nurse Specialist", "364SP0813X": "Geropsychiatric Psychiatric/Mental Health Clinical Nurse Specialist", "364SR0400X": "Rehabilitation Clinical Nurse Specialist", "364SS0200X": "School Clinical Nurse Specialist", "364ST0500X": "Transplantation Clinical Nurse Specialist", "364SW0102X": "Women's Health Clinical Nurse Specialist", "367500000X": "Certified Registered Nurse Anesthetist", "363L00000X": "Nurse Practitioner", "363LA2100X": "Acute Care Nurse Practitioner", "363LA2200X": "Adult Health Nurse Practitioner", "363LC1500X": "Community Health Nurse Practitioner", "363LC0200X": "Critical Care Medicine Nurse Practitioner", "363LF0000X": "Family Nurse Practitioner", "363LG0600X": "Gerontology Nurse Practitioner", "363LN0000X": "Neonatal Nurse Practitioner", "363LN0005X": "Critical Care Neonatal Nurse Practitioner", "363LX0001X": "Obstetrics & Gynecology Nurse Practitioner", "363LX0106X": "Occupational Health Nurse Practitioner", "363LP0200X": "Pediatric Nurse Practitioner", "363LP0222X": "Critical Care Pediatric Nurse Practitioner", "363LP1700X": "Perinatal Nurse Practitioner", "363LP2300X": "Primary Care Nurse Practitioner", "363LP0808X": "Psychiatric/Mental Health Nurse Practitioner", "363LS0200X": "School Nurse Practitioner", "363LW0102X": "Women's Health Nurse Practitioner", "363A00000X": "Physician Assistant", "363AM0700X": "Medical Physician Assistant", "363AS0400X": "Surgical Physician Assistant", "211D00000X": "Podiatric Assistant", "213E00000X": "Podiatrist", "213ES0103X": "Foot & Ankle Surgery Podiatrist", "213ES0131X": "Foot Surgery Podiatrist", "213EG0000X": "Deactivated - Podiatrist", "213EP1101X": "Primary Podiatric Medicine Podiatrist", "213EP0504X": "Public Medicine Podiatrist", "213ER0200X": "Radiology Podiatrist", "213ES0000X": "Sports Medicine Podiatrist", "229N00000X": "Anaplastologist", "221700000X": "Art Therapist", "224Y00000X": "Clinical Exercise Physiologist", "225600000X": "Dance Therapist", "222Q00000X": "Developmental Therapist", "226300000X": "Kinesiotherapist", "225700000X": "Massage Therapist", "224900000X": "Ma<PERSON>ct<PERSON> Fitter", "225A00000X": "Music Therapist", "225X00000X": "Occupational Therapist", "225XR0403X": "Driving and Community Mobility Occupational Therapist", "225XE0001X": "Environmental Modification Occupational Therapist", "225XE1200X": "Ergonomics Occupational Therapist", "225XF0002X": "Feeding & Eating & Swallowing Occupational Therapist", "225XG0600X": "Gerontology Occupational Therapist", "225XH1200X": "Hand Occupational Therapist", "225XH1300X": "Human Factors Occupational Therapist", "225XL0004X": "Low Vision Occupational Therapist", "225XM0800X": "Mental Health Occupational Therapist", "225XN1300X": "Neurorehabilitation Occupational Therapist", "225XP0200X": "Pediatric Occupational Therapist", "225XP0019X": "Physical Rehabilitation Occupational Therapist", "224Z00000X": "Occupational Therapy Assistant", "224ZR0403X": "Driving and Community Mobility Occupational Therapy Assistant", "224ZE0001X": "Environmental Modification Occupational Therapy Assistant", "224ZF0002X": "Feeding & Eating & Swallowing Occupational Therapy Assistant", "224ZL0004X": "Low Vision Occupational Therapy Assistant", "225000000X": "Orthotic Fitter", "222Z00000X": "Orthotist", "224L00000X": "Pedorthist", "225100000X": "Physical Therapist", "2251C2600X": "Cardiopulmonary Physical Therapist", "2251E1300X": "Clinical Electrophysiology Physical Therapist", "2251E1200X": "Ergonomics Physical Therapist", "2251G0304X": "Geriatric Physical Therapist", "2251H1200X": "Hand Physical Therapist", "2251H1300X": "Human Factors Physical Therapist", "2251N0400X": "Neurology Physical Therapist", "2251X0800X": "Orthopedic Physical Therapist", "2251P0200X": "Pediatric Physical Therapist", "2251S0007X": "Sports Physical Therapist", "225200000X": "Physical Therapy Assistant", "224P00000X": "Prosthetist", "225B00000X": "Pulmonary Function Technologist", "225800000X": "Recreation Therapist", "226000000X": "Recreational Therapist Assistant", "225C00000X": "Rehabilitation Counselor", "225CA2400X": "Assistive Technology Practitioner Rehabilitation Counselor", "225CA2500X": "Assistive Technology Supplier Rehabilitation Counselor", "225CX0006X": "Orientation and Mobility Training Rehabilitation Counselor", "225400000X": "Rehabilitation Practitioner", "227800000X": "Certified Respiratory Therapist", "2278C0205X": "Critical Care Certified Respiratory Therapist", "2278E1000X": "Educational Certified Respiratory Therapist", "2278E0002X": "Emergency Care Certified Respiratory Therapist", "2278G1100X": "General Care Certified Respiratory Therapist", "2278G0305X": "Geriatric Care Certified Respiratory Therapist", "2278H0200X": "Home Health Certified Respiratory Therapist", "2278P3900X": "Neonatal/Pediatric Certified Respiratory Therapist", "2278P3800X": "Palliative/Hospice Certified Respiratory Therapist", "2278P4000X": "Patient Transport Certified Respiratory Therapist", "2278P1004X": "Pulmonary Diagnostics Certified Respiratory Therapist", "2278P1006X": "Pulmonary Function Technologist Certified Respiratory Therapist", "2278P1005X": "Pulmonary Rehabilitation Certified Respiratory Therapist", "2278S1500X": "SNF/Subacute Care Certified Respiratory Therapist", "227900000X": "Registered Respiratory Therapist", "2279C0205X": "Critical Care Registered Respiratory Therapist", "2279E1000X": "Educational Registered Respiratory Therapist", "2279E0002X": "Emergency Care Registered Respiratory Therapist", "2279G1100X": "General Care Registered Respiratory Therapist", "2279G0305X": "Geriatric Care Registered Respiratory Therapist", "2279H0200X": "Home Health Registered Respiratory Therapist", "2279P3900X": "Neonatal/Pediatric Registered Respiratory Therapist", "2279P3800X": "Palliative/Hospice Registered Respiratory Therapist", "2279P4000X": "Patient Transport Registered Respiratory Therapist", "2279P1004X": "Pulmonary Diagnostics Registered Respiratory Therapist", "2279P1006X": "Pulmonary Function Technologist Registered Respiratory Therapist", "2279P1005X": "Pulmonary Rehabilitation Registered Respiratory Therapist", "2279S1500X": "SNF/Subacute Care Registered Respiratory Therapist", "225500000X": "Respiratory/Developmental/Rehabilitative Specialist/Technologist", "2255A2300X": "Athletic Trainer", "2255R0406X": "Blind Rehabilitation Specialist/Technologist", "231H00000X": "Audiologist", "231HA2400X": "Assistive Technology Practitioner Audiologist", "231HA2500X": "Assistive Technology Supplier Audiologist", "237600000X": "Audiologist-Hearing Aid Fitter", "237700000X": "Hearing Instrument Specialist", "235500000X": "Speech/Language/Hearing Specialist/Technologist", "2355A2700X": "Audiology Assistant", "2355S0801X": "Speech-Language Assistant", "235Z00000X": "Speech-Language Pathologist", "390200000X": "Student in an Organized Health Care Education/Training Program", "242T00000X": "Perfusionist", "247100000X": "Radiologic Technologist", "2471B0102X": "Bone Densitometry Radiologic Technologist", "2471C1106X": "Cardiac-Interventional Technology Radiologic Technologist", "2471C1101X": "Cardiovascular-Interventional Technology Radiologic Technologist", "2471C3401X": "Computed Tomography Radiologic Technologist", "2471M1202X": "Magnetic Resonance Imaging Radiologic Technologist", "2471M2300X": "Mammography Radiologic Technologist", "2471N0900X": "Nuclear Medicine Technology Radiologic Technologist", "2471Q0001X": "Quality Management Radiologic Technologist", "2471R0002X": "Radiation Therapy Radiologic Technologist", "2471C3402X": "Radiography Radiologic Technologist", "2471S1302X": "Sonography Radiologic Technologist", "2471V0105X": "Vascular Sonography Radiologic Technologist", "2471V0106X": "Vascular-Interventional Technology Radiologic Technologist", "243U00000X": "Radiology Practitioner Assistant", "246X00000X": "Cardiovascular Specialist/Technologist", "246XC2901X": "Cardiovascular Invasive Specialist/Technologist", "246XS1301X": "Sonography Specialist/Technologist", "246XC2903X": "Vascular Specialist/Technologist", "246Y00000X": "Health Information Specialist/Technologist", "246YC3301X": "Hospital Based Coding Specialist", "246YC3302X": "Physician Office Based Coding Specialist", "246YR1600X": " Registered Record Administrator", "246Z00000X": "Other Specialist/Technologist", "246ZA2600X": "Medical Art Specialist/Technologist", "246ZB0500X": "Biochemist", "246ZB0301X": "Biomedical Engineer", "246ZB0302X": "Biomedical Photographer", "246ZB0600X": "Biostatiscian", "246ZE0500X": "EEG Specialist/Technologist", "246ZE0600X": "Electroneurodiagnostic Specialist/Technologist", "246ZG1000X": "Medical Geneticist (PhD) Specialist/Technologist", "246ZG0701X": "Graphics Methods Specialist/Technologist", "246ZI1000X": "Medical Illustrator", "246ZN0300X": "Nephrology Specialist/Technologist", "246ZX2200X": "Orthopedic Assistant", "246ZC0007X": "Surgical Assistant", "246ZS0410X": "Surgical Technologist", "246Q00000X": "Pathology Specialist/Technologist", "246QB0000X": "Blood Banking Specialist/Technologist", "246QC1000X": "Chemistry Pathology Specialist/Technologist", "246QC2700X": "Cytotechnology Specialist/Technologist", "246QH0401X": "Hemapheresis Practitioner", "246QH0000X": "Hematology Specialist/Technologist", "246QH0600X": "Histology Specialist/Technologist", "246QI0000X": "Immunology Pathology Specialist/Technologist", "246QL0900X": "Laboratory Management Specialist/Technologist", "246QL0901X": "Diplomate Laboratory Management Specialist/Technologist", "246QM0706X": "Medical Technologist", "246QM0900X": "Microbiology Specialist/Technologist", "246W00000X": "Cardiology Technician", "247000000X": "Health Information Technician", "2470A2800X": "Assistant Health Information Record Technician", "247200000X": "Other Technician", "2472B0301X": "Biomedical Engineering Technician", "2472D0500X": "Darkroom Technician", "2472E0500X": "EEG Technician", "2472R0900X": "Renal Dialysis Technician", "2472V0600X": "Veterinary Technician", "246R00000X": "Pathology Technician", "247ZC0005X": "Clinical Laboratory Director (Non-physician)", "246RH0600X": "Histology Technician", "246RM2200X": "Medical Laboratory Technician", "246RP1900X": "Phlebotomy Technician", "251B00000X": "Case Management Agency", "251S00000X": "Community/Behavioral Health Agency", "251C00000X": "Developmentally Disabled Services Day Training Agency", "252Y00000X": "Early Intervention Provider Agency", "253J00000X": "Foster Care Agency", "251E00000X": "Home Health Agency", "251F00000X": "Home Infusion Agency", "251G00000X": "Community Based Hospice Care Agency", "253Z00000X": "In Home Supportive Care Agency", "251300000X": "Local Education Agency (LEA)", "251J00000X": "Nursing Care Agency", "251T00000X": "PACE Provider Organization", "251K00000X": "Public Health or Welfare Agency", "251X00000X": "Supports Brokerage Agency", "251V00000X": "Voluntary or Charitable Agency", "261Q00000X": "Clinic/Center", "261QM0855X": "Adolescent and Children Mental Health Clinic/Center", "261QA0600X": "Adult Day Care Clinic/Center", "261QM0850X": "Adult Mental Health Clinic/Center", "261QA0005X": "Ambulatory Family Planning Facility", "261QA0006X": "Ambulatory Fertility Facility", "261QA1903X": "Ambulatory Surgical Clinic/Center", "261QA0900X": "Amputee Clinic/Center", "261QA3000X": "Augmentative Communication Clinic/Center", "261QB0400X": "Birthing Clinic/Center", "261QC1500X": "Community Health Clinic/Center", "261QC1800X": "Corporate Health Clinic/Center", "261QC0050X": "Critical Access Hospital Clinic/Center", "261QD0000X": "Dental Clinic/Center", "261QD1600X": "Developmental Disabilities Clinic/Center", "261QE0002X": "Emergency Care Clinic/Center", "261QE0700X": "End-Stage Renal Disease (ESRD) Treatment Clinic/Center", "261QE0800X": "Endoscopy Clinic/Center", "261QF0050X": "Non-Surgical Family Planning Clinic/Center", "261QF0400X": "Federally Qualified Health Center (FQHC)", "261QG0250X": "Genetics Clinic/Center", "261QH0100X": "Health Service Clinic/Center", "261QH0700X": "Hearing and Speech Clinic/Center", "261QI0500X": "Infusion Therapy Clinic/Center", "261QL0400X": "Lithotripsy Clinic/Center", "261QM1200X": "Magnetic Resonance Imaging (MRI) Clinic/Center", "261QM2500X": "Medical Specialty Clinic/Center", "261QM3000X": "Medically Fragile Infants and Children Day Care", "261QM0801X": "Mental Health Clinic/Center (Including Community Mental Health Center)", "261QM2800X": "Methadone Clinic", "261QM1000X": "Migrant Health Clinic/Center", "261QM1103X": "Military Ambulatory Procedure Visits Operational (Transportable) Clinic/Center", "261QM1101X": "Military and U.S. Coast Guard Ambulatory Procedure Clinic/Center", "261QM1102X": "Military Outpatient Operational (Transportable) Component Clinic/Center", "261QM1100X": "Military/U.S. Coast Guard Outpatient Clinic/Center", "261QM1300X": "Multi-Specialty Clinic/Center", "261QX0100X": "Occupational Medicine Clinic/Center", "261QX0200X": "Oncology Clinic/Center", "261QX0203X": "Radiation Oncology Clinic/Center", "261QS0132X": "Ophthalmologic Surgery Clinic/Center", "261QS0112X": "Oral and Maxillofacial Surgery Clinic/Center", "261QP3300X": "Pain Clinic/Center", "261QP2000X": "Physical Therapy Clinic/Center", "261QP1100X": "Podiatric Clinic/Center", "261QP2300X": "Primary Care Clinic/Center", "261QP2400X": "Prison Health Clinic/Center", "261QP0904X": "Federal Public Health Clinic/Center", "261QP0905X": "State or Local Public Health Clinic/Center", "261QR0200X": "Radiology Clinic/Center", "261QR0206X": "Mammography Clinic/Center", "261QR0208X": "Mobile Radiology Clinic/Center", "261QR0207X": "Mobile Mammography Clinic/Center", "261QR0800X": "Recovery Care Clinic/Center", "261QR0400X": "Rehabilitation Clinic/Center", "261QR0404X": "Cardiac Rehabilitation Clinic/Center", "261QR0401X": "Comprehensive Outpatient Rehabilitation Facility (CORF)", "261QR0405X": "Substance Use Disorder Rehabilitation Clinic/Center", "261QR1100X": "Research Clinic/Center", "261QR1300X": "Rural Health Clinic/Center", "261QS1200X": "Sleep Disorder Diagnostic Clinic/Center", "261QS1000X": "Student Health Clinic/Center", "261QU0200X": "Urgent Care Clinic/Center", "261QV0200X": "VA Clinic/Center", "273100000X": "Epilepsy Hospital Unit", "275N00000X": "Medicare Defined Swing Bed Hospital Unit", "273R00000X": "Psychiatric Hospital Unit", "273Y00000X": "Rehabilitation Hospital Unit", "276400000X": "Substance Use Disorder Rehabilitation Hospital Unit", "287300000X": "Deactivated - Christian Science Sanitorium", "281P00000X": "Chronic Disease Hospital", "281PC2000X": "Children's Chronic Disease Hospital", "282N00000X": "General Acute Care Hospital", "282NC2000X": "Children's Hospital", "282NC0060X": "Critical Access Hospital", "282NR1301X": "Rural Acute Care Hospital", "282NW0100X": "Women's Hospital", "282E00000X": "Long Term Care Hospital", "286500000X": "Military Hospital", "2865C1500X": "Deactivated - Military Hospital", "2865M2000X": "Military General Acute Care Hospital", "2865X1600X": "Operational (Transportable) Military General Acute Care Hospital", "283Q00000X": "Psychiatric Hospital", "283X00000X": "Rehabilitation Hospital", "283XC2000X": "Children's Rehabilitation Hospital", "282J00000X": "Religious Nonmedical Health Care Institution", "284300000X": "Special Hospital", "291U00000X": "Clinical Medical Laboratory", "292200000X": "Dental Laboratory", "291900000X": "Military Clinical Medical Laboratory", "293D00000X": "Physiological Laboratory", "302F00000X": "Exclusive Provider Organization", "302R00000X": "Health Maintenance Organization", "305S00000X": "Point of Service", "305R00000X": "Preferred Provider Organization", "311500000X": "Alzheimer Center (Dementia Center)", "310400000X": "Assisted Living Facility", "3104A0630X": "Assisted Living Facility (Behavioral Disturbances)", "3104A0625X": "Assisted Living Facility (Mental Illness)", "317400000X": "Deactivated - Christian Science Facility", "311Z00000X": "Custodial Care Facility", "311ZA0620X": "Adult Care Home Facility", "315D00000X": "Inpatient Hospice", "315P00000X": "Intellectual Disabilities Intermediate Care Facility", "310500000X": "Mental Illness Intermediate Care Facility", "313M00000X": "Nursing Facility/Intermediate Care Facility", "314000000X": "Skilled Nursing Facility", "3140N1450X": "Pediatric Skilled Nursing Facility", "177F00000X": "Lodging Provider", "174200000X": "Meals Provider", "320800000X": "Mental Illness Community Based Residential Treatment Facility", "320900000X": "Intellectual and/or Developmental Disabilities Community Based Residential Treatment Facility", "323P00000X": "Psychiatric Residential Treatment Facility", "322D00000X": "Emotionally Disturbed Childrens' Residential Treatment Facility", "320600000X": "Intellectual and/or Developmental Disabilities Residential Treatment Facility", "320700000X": "Physical Disabilities Residential Treatment Facility", "324500000X": "Substance Abuse Rehabilitation Facility", "3245S0500X": "Children's Substance Abuse Rehabilitation Facility", "385H00000X": "Respite Care", "385HR2050X": "Respite Care Camp", "385HR2055X": "Child Mental Illness Respite Care", "385HR2060X": "Child Intellectual and/or Developmental Disabilities Respite Care", "385HR2065X": "Child Physical Disabilities Respite Care", "331L00000X": "Blood Bank", "332100000X": "Department of Veterans Affairs (VA) Pharmacy", "332B00000X": "Durable Medical Equipment & Medical Supplies", "332BC3200X": "Customized Equipment (DME)", "332BD1200X": "Dialysis Equipment & Supplies (DME)", "332BN1400X": "Nursing Facility Supplies (DME)", "332BX2000X": "Oxygen Equipment & Supplies (DME)", "332BP3500X": "Parenteral & Enteral Nutrition Supplies (DME)", "333300000X": "Emergency Response System Companies", "332G00000X": "Eye Bank", "332H00000X": "Eyewear Supplier", "332S00000X": "Hearing  Aid Equipment", "332U00000X": "Home Delivered Meals", "332800000X": "Indian Health Service/Tribal/Urban Indian Health (I/T/U) Pharmacy", "335G00000X": "Medical Foods Supplier", "332000000X": "Military/U.S. Coast Guard Pharmacy", "332900000X": "Non-Pharmacy Dispensing Site", "335U00000X": "Organ Procurement Organization", "333600000X": "Pharmacy", "3336C0002X": "Clinic Pharmacy", "3336C0003X": "Community/Retail Pharmacy", "3336C0004X": "Compounding Pharmacy", "3336H0001X": "Home Infusion Therapy Pharmacy", "3336I0012X": "Institutional Pharmacy", "3336L0003X": "Long Term Care Pharmacy", "3336M0002X": "Mail Order Pharmacy", "3336M0003X": "Managed Care Organization Pharmacy", "3336N0007X": "Nuclear Pharmacy", "3336S0011X": "Specialty Pharmacy", "335V00000X": "Portable X-ray and/or Other Portable Diagnostic Imaging Supplier", "335E00000X": "Prosthetic/Orthotic Supplier", "344800000X": "Air Carrier", "341600000X": "Ambulance", "3416A0800X": "Air Ambulance", "3416L0300X": "Land Ambulance", "3416S0300X": "Water Ambulance", "347B00000X": "Bus", "341800000X": "Military/U.S. Coast Guard Transport & ", "3418M1120X": "Military or U.S. Coast Guard Air Transport Ambulance", "3418M1110X": "Military or U.S. Coast Guard Ground Transport Ambulance", "3418M1130X": "Military or U.S. Coast Guard Water Transport Ambulance", "343900000X": "Non-emergency Medical Transport (VAN)", "347C00000X": "Private Vehicle", "343800000X": "Secured Medical Transport (VAN)", "344600000X": "Taxi", "347D00000X": "Train", "347E00000X": "Transportation Broker", "342000000X": "Transportation Network Company"}, "SBR09": {"11": "Other Non-Federal Programs", "12": "Preferred Provider Organization (PPO)", "13": "Point of Service (POS)", "14": "Exclusive Provider Organization (EPO)", "15": "Indemnity Insurance", "16": "Health Maintenance Organization (HMO) Medicare Risk", "17": "Dental Maintenance Organization", "AM": "Automobile Medical", "BL": "Blue Cross/Blue Shield", "CH": "Champus", "CI": "Commercial Insurance Co.", "DS": "Disability", "FI": "Federal Employees Program", "HM": "Health Maintenance Organization", "LM": "Liability Medical", "MA": "Medicare Part A", "MB": "Medicare Part B", "MC": "Medicare Part C", "OF": "Other Federal Program", "TV": "Title V", "VA": "Veterans Affairs Plan", "WC": "Worker's Compensation Health Claim", "ZZ": "Mutually Defined"}, "CLM05-01": {"1": "Pharmacy", "2": "Telehealth Provided Other than in Patient’s Home", "3": "School", "4": "Homeless Shelter", "5": "Indian Health Service Free-standing Facility", "6": "Indian Health ServiceProvider-based Facility", "7": "Tribal 638 Free-standing Facility", "8": "Tribal 638 Provider-based Facility", "9": "Prison/ Correctional Facility", "10": "Telehealth Provided in <PERSON><PERSON>’s Home", "11": "Office", "12": "Home", "13": "Assisted Living Facility", "14": "Group Home", "15": "Mobile Unit", "16": "Temporary Lodging", "17": "Walk-in Retail Health Clinic", "18": "Place of Employment-Worksite", "19": "Off Campus-Outpatient Hospital", "20": "Urgent Care Facility", "21": "Inpatient Hospital", "22": "On Campus-Outpatient Hospital", "23": "Emergency Room – Hospital", "24": "Ambulatory Surgical Center", "25": "Birthing Center", "26": "Military Treatment Facility", "27": "Outreach Site/ Street", "31": "Skilled Nursing Facility", "32": "Nursing Facility", "33": "Custodial Care Facility", "34": "Hospice", "41": "Ambulance - Land", "42": "Ambulance – Air or Water", "49": "Independent Clinic", "50": "Federally Qualified Health Center", "51": "Inpatient Psychiatric Facility", "52": "Psychiatric Facility-Partial Hospitalization", "53": "Community Mental Health Center", "54": "Intermediate Care Facility/ Individuals with Intellectual Disabilities", "55": "Residential Substance Abuse Treatment Facility", "56": "Psychiatric Residential Treatment Center", "57": "Non-residential Substance Abuse Treatment Facility", "58": "Non-residential Opioid Treatment Facility", "59": "Unassigned", "60": "Mass Immunization Center", "61": "Comprehensive Inpatient Rehabilitation Facility", "62": "Comprehensive Outpatient Rehabilitation Facility", "65": "End-Stage Renal Disease Treatment Facility", "66": "Programs of All-Inclusive Care for the Elderly (PACE) Center", "71": "Public Health Clinic", "72": "Rural Health Clinic", "81": "Independent Laboratory", "99": "Other Place of Service"}, "CLM05-03": {"1": "Original Claim", "7": "Replacement of <PERSON> <PERSON><PERSON><PERSON>", "8": "Void/Cancel of Prior Claim", "9": "Final Claim for a period of continuous care"}, "CLM06": {"N": "No", "Y": "Yes"}, "CLM07": {"A": "Assigned", "B": "Assignment Accepted on Clinical Lab Services Only", "C": "Not Assigned"}, "CLM08": {"N": "No", "W": "Not Applicable - Used when patient refuses to assign benefits", "Y": "Yes"}, "CLM09": {"I": "Informed Consent to Release Medication Information for Conditions or Diagnosis Regulated by Federal Statutes", "Y": "Yes"}, "CLM10": {"P": "Signature generated by provider because the patient was not physically present for services"}, "CLM11": {"AA": "Auto accident", "EM": "Employment", "OA": "Other Accident"}, "CLM12": {"02": "Physically Handicapped Children's Program", "03": "Special Federal Funding", "05": "Diability", "09": "Second Opinion or Surgery"}, "CLM20": {"1": "Proof of Eligibility Unknown or Unavailable", "2": "Litigation", "3": "Authorization Delays", "4": "Delay in Certifying Provider", "5": "Delay in Supplying Billing Forms", "6": "Delay in Delivery of Custom-made Appliances", "7": "Third Party Processing Delay", "8": "Delay in Eligibility Determination", "9": "Original Claim Rejected or Denied Due to a Reason Unrelated to the Billing Limitation Rules", "10": "Administration Delay in the Prior Approval Process", "11": "Other", "15": "Natural Disaster"}, "CN101": {"01": "Diagnosis Related Group (DRG)", "02": "<PERSON>", "03": "Variable Per <PERSON>", "04": "Flat", "05": "Capitated", "06": "Percent", "09": "Other"}, "PWK01": {"03": "Report Justifying Treatment Beyond Utilization Guidelines", "04": "Drugs Administered", "05": "Treatment Diagnosis", "06": "Initial Assessment", "07": "Functional Goals", "08": "Plan of Treatment", "09": "Progress Report", "10": "Continued Treatment", "11": "Chemical Analysis", "13": "Certified Test Report", "15": "Justification for Admission", "21": "Recovery Plan", "A3": "Allergies/Sensitivities Document", "A4": "Autopsy Report", "AM": "Ambulance Certification", "AS": "Admission Summary", "B2": "Prescription", "B3": "Physician Order", "B4": "Referral Form", "BR": "Benchmark Testing Results", "BS": "Baseline", "BT": "Blanket Test Results", "CB": "Chiropractic Justification", "CK": "Consent Form(s)", "CT": "Certification", "D2": "Drug Profile Document", "DA": "Dental Models", "DB": "Durable Medical Equipment Prescription", "DG": "Diagnostic Report", "DJ": "Discharge Monitoring Report", "DS": "Discharge Summary", "EB": "Explanation of Benefits (Coordination of Benefits or Medicare Secondary Payor)", "HC": "Health Certificate", "HR": "Health Clinic Records", "I5": "Immunization Record", "IR": "State School Immunization Records", "LA": "Laboratory Results", "M1": "Medical Record Attachment", "MT": "Models", "NM": "Nursing Notes", "OB": "Operative Note", "OC": "Oxygen Content Averaging Report", "OD": "Orders and Treatments Document", "OE": "Objective Physical Examination (including vital signs) Document", "OX": "Oxygen Therapy Certification", "OZ": "Support Data for Claim", "P4": "Pathology Report", "P5": "Patient Medical History Document", "PE": "Parenteral or Enteral Certification", "PN": "Physical Therapy Notes", "PO": "Prosthetics or Orthotic Certification", "PQ": "Paramedical Results", "PY": "Physician's Report", "PZ": "Physical Therapy Certification", "RB": "Radiology Films", "RR": "Radiology Reports", "RT": "Report of Tests and Analysis Report", "RX": "Renewable Oxygen Content Averaging Report", "SG": "Symptoms Document", "V5": "Death Notification", "XP": "Photographs"}, "PWK02": {"AA": "Available on Request at Provider Site", "BM": "By Mail", "EL": "Electronic Only", "EM": "Email", "FT": "File Transfer", "FX": "By Fax"}, "REF02-4N": {"1": "Immediate/Urgent Care", "2": "Services Rendered in a Retroactive Period", "3": "Emergency Care", "4": "Client has Temporary Medicaid", "5": "Request from County for Second Opinion to Determine if Recipient Can Work", "6": "Request for Override Pending", "7": "Special Handling"}, "HI0*": {"BK": "International Classification of Diseases Clinical Modification (ICD-9-CM) Principal Diagnosis", "ABK": "International Classification of Diseases Clinical Modification (ICD-10-CM) Principal Diagnosis", "BF": "International Classification of Diseases Clinical Modification (ICD-9-CM) Diagnosis", "ABF": "International Classification of Diseases Clinical Modification (ICD-10-CM) Diagnosis"}, "HCP01": {"00": "Zero Pricing (Not Covered Under Contract)", "01": "Priced as Billed at 100%", "02": "Priced at the Standard Fee Schedule", "03": "Priced at a Contractual Percentage", "04": "Bundled Pricing", "05": "Peer Review Pricing", "06": "Bundled Pricing", "07": "Flat Rate Pricing", "08": "Combination Pricing", "09": "Maternity Pricing", "10": "Other Pricing", "11": "Lower of Cost", "12": "<PERSON><PERSON> of Cost", "13": "Cost Reimbursed", "14": "Adjustment Pricing"}, "HCP13": {"T1": "Cannot Identify Provider as TPO (Third Party Organization) Participant", "T2": "Cannot Identify Payer as TPO (Third Party Organization) Participant", "T3": "Cannot Identify Insured as TPO (Third Party Organization) Participant", "T4": "Payer Name or Identifier Missing", "T5": "Certification Information Missing", "16": "Claim does not contain enough information for repricing"}, "CAS01": {"CO": "Contractual Obligations", "CR": "Corrections and Reversals", "OA": "Other Adjustments", "PI": "Payer Initiated Reductions", "PR": "Patient Responsibility"}, "O103": {"N": "No", "W": "Not Applicable", "Y": "Yes"}, "O104": {"P": "Signature generated by provider because the patient was not physically present for services"}, "O106": {"I": "Informed Consent to Release Medical Information", "Y": "Yes"}, "2330B-REF01": {"2U": "Payer Identification Number", "PI": "Employer’s Identification Number'", "NI": "National Association of Insurance Commissioners (NAIC) Code", "XV": "Health Plan Identifier (HPID)", "ZZ": "Mutually Defined"}, "2330C-REF01": {"NF": "National Provider Identifier (NPI)", "G2": "Commercial Number", "0B": "State License Number", "ZZ": "Mutually Defined"}, "2330D-REF01": {"NF": "National Provider Identifier (NPI)", "G2": "Commercial Number", "0B": "State License Number", "ZZ": "Mutually Defined"}, "2330G-REF01": {"NF": "National Provider Identifier (NPI)", "G2": "Commercial Number", "0B": "State License Number", "ZZ": "Mutually Defined"}, "2330E-REF01": {"0B": "State License Number", "1D": "Medicaid Provider Number", "1G": "Provider UPIN Number", "1H": "CHAMPUS Identification Number", "EI": "Employer's Identification Number", "G2": "Provider Commercial Number", "LU": "Location Number", "SY": "Social Security Number (SSN)", "X5": "State Industrial Accident Provider Number", "ZZ": "Provider Taxonomy"}, "SV101": {"ER": "Jurisdiction Specific Procedure and Supply Codes", "HC": "Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes", "IV": "Home Infusion EDI Coalition (HIEC) Product/Service Code", "WK": "Advanced Billing Concepts (ABC) Codes"}, "SV103": {"MJ": "Minutes", "UN": "Unit"}, "SV105": {"01": "Pharmacy", "12": "Home", "15": "Mobile Unit", "19": "Off Campus-Outpatient Hospital", "20": "Urgent Care Facility", "21": "Inpatient Hospital", "22": "On Campus-Outpatient Hospital", "23": "Emergency Room - Hospital", "31": "Skilled Nursing Facility", "32": "Nursing Facility", "41": "Ambulance - Land", "50": "Federally Qualified Health Center", "61": "Comprehensive Inpatient Rehabilitation Facility", "99": "Other Place of Service"}, "SV109": {"N": "Service was not emergency related", "Y": "Service was emergency related"}, "SV111": {"N": "No EPSDT involvement", "Y": "EPSDT involvement related"}, "SV112": {"N": "No family planning involvement", "Y": "Family planning involvement"}, "SV115": {"0": "Copay exempt"}, "SV506": {"1": "Weekly", "4": "Monthly", "6": "Daily"}, "PWK02-DME-Code": {"AB": "Previously Submitted to Payer", "AD": "Certification Included in this Claim", "AF": "Narrative Segment Included in this Claim", "AG": "No Documentation is Required", "NS": "Not Specified"}, "CR301": {"I": "Initial", "R": "Renewal", "S": "Revised"}, "CRC02": {"N": "No", "Y": "Yes"}, "CRC03": {"01": "Patient admitted to hospital", "12": "Patient confined to bed/chair"}, "CRC04": {"38": "Certification signed by the physician is on file at the supplier's office", "ZV": "Replacement Item"}, "HCP14": {"1": "Procedure Followed (Compliance)", "2": "Not Followed - Call Not Made (Non-Compliance Call Not Made)", "3": "Not Medically Necessary (Non-Compliance Non-Medically Necessary)", "4": "Not Followed Other (Non-Compliance Other)", "5": "Emergency Admit to Non-Network Hospital"}, "HCP15": {"1": "Non-Network Professional Provider in Network Hospital", "2": "Emergency Care", "3": "Services or Specialist not in Network", "4": "Out-of-Service Area", "5": "State Mandates", "6": "Other"}, "SVD03-01": {"ER": "Jurisdiction Specific Procedure and Supply Codes", "HC": "Health Care Financing Administration Common Procedural Coding System (HCPCS) Codes", "HP": "Health Insurance Prospective Payment System (HIPPS) Skilled Nursing Facility Rate Code", "IV": "Home Infusion EDI Coalition (HIEC) Product/Service Code", "WK": "Advanced Billing Concepts (ABC) Codes"}, "LQ01": {"AS": "General At<PERSON>ch<PERSON>", "UT": "Centers for Medicare and Medicaid Services (CMS) Durable Medical Equipment Regional Carrier (DMERC) Certificate of Medical Necessity (CMN) Forms"}, "FRM02": {"N": "No", "W": "Not Applicable", "Y": "Yes"}, "2010AC-REF01": {"2U": "Payer Identification Number", "FY": "Claim Office Number", "NF": "National Association of Insurance Commissioners (NAIC) Code"}, "LIN02": {"EN": "EAN/UCC - 13", "EO": "EAN/UCC - 8", "HI": "HIBC (Health Care Industry Bar Code) Supplier Labeling Standard Primary Data Message", "N4": "National Drug Code in 5-4-2 Format", "ON": "Customer Order Number", "UK": "GTIN 14-digit Data Structure", "UP": "UCC - 12"}, "CTP05-01": {"F2": "International Unit", "GR": "Gram", "ME": "Milligram", "ML": "Milliliter", "UN": "Unit"}, "STC01-1": {"A0": "Acknowledgement/Forwarded-The claim/encounter has been forwarded to another entity.", "A1": "Acknowledgement/Receipt-The claim/encounter has been received. This does not mean that the claim has been accepted for adjudication.", "A2": "Acknowledgement/Acceptance into adjudication system-The claim/encounter has been accepted into the adjudication system.", "A3": "Acknowledgement/Returned as unprocessable claim-The claim/encounter has been rejected and has not been entered into the adjudication system.", "A4": "Acknowledgement/Not Found-The claim/encounter can not be found in the adjudication system.", "A5": "Acknowledgement/Split Claim-The claim/encounter has been split upon acceptance into the adjudication system.", "A6": "Acknowledgement/Rejected for Missing Information - The claim/encounter is missing the information specified in the Status details and has been rejected.", "A7": "Acknowledgement/Rejected for Invalid Information - The claim/encounter has invalid information as specified in the Status details and has been rejected.", "A8": "Acknowledgement/Rejected for relational field in error.", "DR01": "Acknowledgement/Receipt - The claim/encounter has been received. This does not mean the claim has been accepted into the data reporting/processing system. Usage: Can only be used in the Data Reporting Acknowledgement Transaction.", "DR02": "Acknowledgement/Acceptance into the data reporting/processing system - The claim/encounter has been accepted into the data reporting/processing system. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "DR03": "Acknowledgement/Returned as unprocessable claim - The claim/encounter has been rejected and has not been entered into the data reporting/processing system. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "DR04": "Acknowledgement/Not Found - The claim/encounter can not be found in the data reporting/processing system. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "DR05": "Acknowledgement/Rejected for Missing Information - The claim/encounter is missing the information specified in the Status details and has been rejected. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "DR06": "Acknowledgment/Rejected for invalid information - The claim/encounter has invalid information as specified in the Status details and has been rejected. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "DR07": "Acknowledgement/Rejected for relational field in error. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "DR08": "Acknowledgement/Warning - The claim/encounter has been accepted into the data reporting/processing system but has received a warning as specified in the Status details. Usage: Can only be used in the Data Reporting Acknowledgment Transaction.", "P0": "Pending: Adjudication/Details-This is a generic message about a pended claim. A pended claim is one for which no remittance advice has been issued, or only part of the claim has been paid.", "P1": "Pending/In Process-The claim or encounter is in the adjudication system.", "P2": "Pending/Payer Review-The claim/encounter is suspended and is pending review (e.g. medical review, repricing, Third Party Administrator processing).", "P3": "Pending/Provider Requested Information - The claim or encounter is waiting for information that has already been requested from the provider. (Usage: A Claim Status Code identifying the type of information requested, must be reported)", "P4": "Pending/Patient Requested Information - The claim or encounter is waiting for information that has already been requested from the patient. (Usage: A status code identifying the type of information requested must be sent)", "P5": "Pending/Payer Administrative/System hold", "F0": "Finalized-The claim/encounter has completed the adjudication cycle and no more action will be taken.", "F1": "Finalized/Payment-The claim/line has been paid.", "F2": "Finalized/Denial-The claim/line has been denied.", "F3": "Finalized/Revised - Adjudication information has been changed", "F3F": "Finalized/Forwarded-The claim/encounter processing has been completed. Any applicable payment has been made and the claim/encounter has been forwarded to a subsequent entity as identified on the original claim or in this payer's records.", "F3N": "Finalized/Not Forwarded-The claim/encounter processing has been completed. Any applicable payment has been made. The claim/encounter has NOT been forwarded to any subsequent entity identified on the original claim.", "F4": "Finalized/Adjudication Complete - No payment forthcoming-The claim/encounter has been adjudicated and no further payment is forthcoming.", "R0": "Requests for additional Information/General Requests-Requests that don't fall into other R-type categories.", "R1": "Requests for additional Information/Entity Requests-Requests for information about specific entities (subscribers, patients, various providers).", "R3": "Requests for additional Information/Claim/Line-Requests for information that could normally be submitted on a claim.", "R4": "Requests for additional Information/Documentation-Requests for additional supporting documentation. Examples: certification, x-ray, notes.", "R5": "Request for additional information/more specific detail-Additional information as a follow up to a previous request is needed. The original information was received but is inadequate. More specific/detailed information is requested.", "R6": "Requests for additional information – Regulatory requirements", "R7": "Requests for additional information – Confirm care is consistent with Health Plan policy coverage", "R8": "Requests for additional information – Confirm care is consistent with health plan coverage exceptions", "R9": "Requests for additional information – Determination of medical necessity", "R10": "Requests for additional information – Support a filed grievance or appeal", "R11": "Requests for additional information – Pre-payment review of claims", "R12": "Requests for additional information – Clarification or justification of use for specified procedure code", "R13": "Requests for additional information – Original documents submitted are not readable. Used only for subsequent request(s).", "R14": "Requests for additional information – Original documents received are not what was requested. Used only for subsequent request(s).", "R15": "Requests for additional information – Workers Compensation coverage determination.", "R16": "Requests for additional information – Eligibility determination", "R17": "Replacement of a Prior Request. Used to indicate that the current attachment request replaces a prior attachment request.", "E0": "Response not possible - error on submitted request data", "E1": "Response not possible - System Status", "E2": "Information Holder is not responding; resubmit at a later time.", "E3": "Correction required - relational fields in error.", "E4": "Trading partner agreement specific requirement not met: Data correction required. (Usage: A status code identifying the type of information requested must be sent)", "D0": "Data Search Unsuccessful - The payer is unable to return status on the requested claim(s) based on the submitted search criteria."}, "STC01-2": {"0": "Cannot provide further status electronically.", "1": "For more detailed information, see remittance advice.", "2": "More detailed information in letter.", "3": "Claim has been adjudicated and is awaiting payment cycle.", "6": "Balance due from the subscriber.", "12": "One or more originally submitted procedure codes have been combined.", "15": "One or more originally submitted procedure code have been modified.", "16": "Claim/encounter has been forwarded to entity. Usage: This code requires use of an Entity Code.", "17": "Claim/encounter has been forwarded by third party entity to entity. Usage: This code requires use of an Entity Code.", "18": "Entity received claim/encounter, but returned invalid status. Usage: This code requires use of an Entity Code.", "19": "Entity acknowledges receipt of claim/encounter. Usage: This code requires use of an Entity Code.", "20": "Accepted for processing.", "21": "Missing or invalid information. Usage: At least one other status code is required to identify the missing or invalid information.", "23": "Returned to Entity. Usage: This code requires use of an Entity Code.", "24": "Entity not approved as an electronic submitter. Usage: This code requires use of an Entity Code.", "25": "Entity not approved. Usage: This code requires use of an Entity Code.", "26": "Entity not found. Usage: This code requires use of an Entity Code.", "27": "Policy canceled.", "29": "Subscriber and policy number/contract number mismatched.", "30": "Subscriber and subscriber id mismatched.", "31": "Subscriber and policyholder name mismatched.", "32": "Subscriber and policy number/contract number not found.", "33": "Subscriber and subscriber id not found.", "34": "Subscriber and policyholder name not found.", "35": "Claim/encounter not found.", "37": "Predetermination is on file, awaiting completion of services.", "38": "Awaiting next periodic adjudication cycle.", "39": "Charges for pregnancy deferred until delivery.", "40": "Waiting for final approval.", "41": "Special handling required at payer site.", "42": "Awaiting related charges.", "44": "Charges pending provider audit.", "45": "Awaiting benefit determination.", "46": "Internal review/audit.", "47": "Internal review/audit - partial payment made.", "49": "Pending provider accreditation review.", "50": "Claim waiting for internal provider verification.", "51": "Investigating occupational illness/accident.", "52": "Investigating existence of other insurance coverage.", "53": "Claim being researched for Insured ID/Group Policy Number error.", "54": "Duplicate of a previously processed claim/line.", "55": "<PERSON><PERSON>m assigned to an approver/analyst.", "56": "Awaiting eligibility determination.", "57": "Pending COBRA information requested.", "59": "Information was requested by a non-electronic method. Usage: At least one other status code is required to identify the requested information.", "60": "Information was requested by an electronic method. Usage: At least one other status code is required to identify the requested information.", "61": "Eligibility for extended benefits.", "64": "Re-pricing information.", "65": "Claim/line has been paid.", "66": "Payment reflects usual and customary charges.", "72": "Claim contains split payment.", "73": "Payment made to entity, assignment of benefits not on file. Usage: This code requires use of an Entity Code.", "78": "Duplicate of an existing claim/line, awaiting processing.", "81": "Contract/plan does not cover pre-existing conditions.", "83": "No coverage for newborns.", "84": "Service not authorized.", "85": "Entity not primary. Usage: This code requires use of an Entity Code.", "86": "Diagnosis and patient gender mismatch.", "88": "Entity not eligible for benefits for submitted dates of service. Usage: This code requires use of an Entity Code.", "89": "Entity not eligible for dental benefits for submitted dates of service. Usage: This code requires use of an Entity Code.", "90": "Entity not eligible for medical benefits for submitted dates of service. Usage: This code requires use of an Entity Code.", "91": "Entity not eligible/not approved for dates of service. Usage: This code requires use of an Entity Code.", "92": "Entity does not meet dependent or student qualification. Usage: This code requires use of an Entity Code.", "93": "Entity is not selected primary care provider. Usage: This code requires use of an Entity Code.", "94": "Entity not referred by selected primary care provider. Usage: This code requires use of an Entity Code.", "95": "Requested additional information not received.", "96": "No agreement with entity. Usage: This code requires use of an Entity Code.", "97": "Patient eligibility not found with entity. Usage: This code requires use of an Entity Code.", "98": "Charges applied to deductible.", "99": "Pre-treatment review.", "100": "Pre-certification penalty taken.", "101": "<PERSON>laim was processed as adjustment to previous claim.", "102": "<PERSON><PERSON>'s charges processed on mother's claim.", "103": "Claim combined with other claim(s).", "104": "Processed according to plan provisions (Plan refers to provisions that exist between the Health Plan and the Consumer or Patient).", "105": "Claim/line is capitated.", "106": "This amount is not entity's responsibility. Usage: This code requires use of an Entity Code.", "107": "Processed according to contract provisions (Contract refers to provisions that exist between the Health Plan and a Provider of Health Care Services).", "109": "Entity not eligible. Usage: This code requires use of an Entity Code.", "110": "Claim requires pricing information.", "111": "At the policyholder's request these claims cannot be submitted electronically.", "114": "Claim/service should be processed by entity. Usage: This code requires use of an Entity Code.", "116": "<PERSON><PERSON><PERSON> submitted to incorrect payer.", "117": "Claim requires signature-on-file indicator.", "121": "Service line number greater than maximum allowable for payer.", "123": "Additional information requested from entity. Usage: This code requires use of an Entity Code.", "124": "Entity's name, address, phone and id number. Usage: This code requires use of an Entity Code.", "125": "Entity's name. Usage: This code requires use of an Entity Code.", "126": "Entity's address. Usage: This code requires use of an Entity Code.", "127": "Entity's Communication Number. Usage: This code requires use of an Entity Code.", "128": "Entity's tax id. Usage: This code requires use of an Entity Code.", "129": "Entity's Blue Cross provider id. Usage: This code requires use of an Entity Code.", "130": "Entity's Blue Shield provider id. Usage: This code requires use of an Entity Code.", "131": "Entity's Medicare provider id. Usage: This code requires use of an Entity Code.", "132": "Entity's Medicaid provider id. Usage: This code requires use of an Entity Code.", "133": "Entity's UPIN. Usage: This code requires use of an Entity Code.", "134": "Entity's TRICARE provider id. Usage: This code requires use of an Entity Code.", "135": "Entity's commercial provider id. Usage: This code requires use of an Entity Code.", "136": "Entity's health industry id number. Usage: This code requires use of an Entity Code.", "137": "Entity's plan network id. Usage: This code requires use of an Entity Code.", "138": "Entity's site id . Usage: This code requires use of an Entity Code.", "139": "Entity's health maintenance provider id (HMO). Usage: This code requires use of an Entity Code.", "140": "Entity's preferred provider organization id (PPO). Usage: This code requires use of an Entity Code.", "141": "Entity's administrative services organization id (ASO). Usage: This code requires use of an Entity Code.", "142": "Entity's license/certification number. Usage: This code requires use of an Entity Code.", "143": "Entity's state license number. Usage: This code requires use of an Entity Code.", "144": "Entity's specialty license number. Usage: This code requires use of an Entity Code.", "145": "Entity's specialty/taxonomy code. Usage: This code requires use of an Entity Code.", "146": "Entity's anesthesia license number. Usage: This code requires use of an Entity Code.", "147": "Entity's qualification degree/designation (e.g. <PERSON><PERSON>,PhD,MD). Usage: This code requires use of an Entity Code.", "148": "Entity's social security number. Usage: This code requires use of an Entity Code.", "149": "Entity's employer id. Usage: This code requires use of an Entity Code.", "150": "Entity's drug enforcement agency (DEA) number. Usage: This code requires use of an Entity Code.", "152": "Pharmacy processor number.", "153": "Entity's id number. Usage: This code requires use of an Entity Code.", "154": "Relationship of surgeon & assistant surgeon.", "155": "Entity's relationship to patient. Usage: This code requires use of an Entity Code.", "156": "Patient relationship to subscriber.", "157": "Entity's Gender. Usage: This code requires use of an Entity Code.", "158": "Enti<PERSON>'s date of birth. Usage: This code requires use of an Entity Code.", "159": "Enti<PERSON>'s date of death. Usage: This code requires use of an Entity Code.", "160": "Entity's marital status. Usage: This code requires use of an Entity Code.", "161": "Entity's employment status. Usage: This code requires use of an Entity Code.", "162": "Entity's health insurance claim number (HICN). Usage: This code requires use of an Entity Code.", "163": "Entity's policy/group number. Usage: This code requires use of an Entity Code.", "164": "Entity's contract/member number. Usage: This code requires use of an Entity Code.", "165": "Entity's employer name, address and phone. Usage: This code requires use of an Entity Code.", "166": "Entity's employer name. Usage: This code requires use of an Entity Code.", "167": "Entity's employer address. Usage: This code requires use of an Entity Code.", "168": "Entity's employer phone number. Usage: This code requires use of an Entity Code.", "170": "Entity's employee id. Usage: This code requires use of an Entity Code.", "171": "Other insurance coverage information (health, liability, auto, etc.).", "172": "Other employer name, address and telephone number.", "173": "Entity's name, address, phone, gender, DOB, marital status, employment status and relation to subscriber. Usage: This code requires use of an Entity Code.", "174": "Entity's student status. Usage: This code requires use of an Entity Code.", "175": "Entity's school name. Usage: This code requires use of an Entity Code.", "176": "Entity's school address. Usage: This code requires use of an Entity Code.", "177": "Transplant recipient's name, date of birth, gender, relationship to insured.", "178": "Submitted charges.", "179": "Outside lab charges.", "180": "Hospital's semi-private room rate.", "181": "Hospital's room rate.", "182": "Allowable/paid from other entities coverage Usage: This code requires the use of an entity code.", "183": "Amount entity has paid. Usage: This code requires use of an Entity Code.", "184": "Purchase price for the rented durable medical equipment.", "185": "Rental price for durable medical equipment.", "186": "Purchase and rental price of durable medical equipment.", "187": "Date(s) of service.", "188": "Statement from-through dates.", "189": "Facility admission date.", "190": "Facility discharge date.", "191": "Date of Last Menstrual Period (LMP).", "192": "Date of first service for current series/symptom/illness.", "193": "First consultation/evaluation date.", "194": "Confinement dates.", "195": "Unable to work dates/Disability Dates.", "196": "Return to work dates.", "197": "Effective coverage date(s).", "198": "Medicare effective date.", "199": "Date of conception and expected date of delivery.", "200": "Date of equipment return.", "201": "Date of dental appliance prior placement.", "202": "Date of dental prior replacement/reason for replacement.", "203": "Date of dental appliance placed.", "204": "Date dental canal(s) opened and date service completed.", "205": "Date(s) dental root canal therapy previously performed.", "206": "Most recent date of curettage, root planing, or periodontal surgery.", "207": "Dental impression and seating date.", "208": "Most recent date pacemaker was implanted.", "209": "Most recent pacemaker battery change date.", "210": "Date of the last x-ray.", "211": "Date(s) of dialysis training provided to patient.", "212": "Date of last routine dialysis.", "213": "Date of first routine dialysis.", "214": "Original date of prescription/orders/referral.", "215": "Date of tooth extraction/evolution.", "216": "Drug information.", "217": "Drug name, strength and dosage form.", "218": "NDC number.", "219": "Prescription number.", "222": "Drug dispensing units and average wholesale price (AWP).", "223": "Route of drug/myelogram administration.", "224": "Anatomical location for joint injection.", "225": "Anatomical location.", "226": "Joint injection site.", "227": "Hospital information.", "228": "Type of bill for UB claim.", "229": "Hospital admission source.", "230": "Hospital admission hour.", "231": "Hospital admission type.", "232": "Admitting diagnosis.", "233": "Hospital discharge hour.", "234": "Patient discharge status.", "235": "Units of blood furnished.", "236": "Units of blood replaced.", "237": "Units of deductible blood.", "238": "Separate claim for mother/baby charges.", "239": "Dental information.", "240": "Tooth surface(s) involved.", "241": "List of all missing teeth (upper and lower).", "242": "Tooth numbers, surfaces, and/or quadrants involved.", "243": "Months of dental treatment remaining.", "244": "Tooth number or letter.", "245": "Dental quadrant/arch.", "246": "Total orthodontic service fee, initial appliance fee, monthly fee, length of service.", "247": "Line information.", "249": "Place of service.", "250": "Type of service.", "251": "Total anesthesia minutes.", "252": "Entity's prior authorization/certification number. Usage: This code requires the use of an Entity Code.", "254": "Principal diagnosis code.", "255": "Diagnosis code.", "256": "DRG code(s).", "257": "ADSM-III-R code for services rendered.", "258": "Days/units for procedure/revenue code.", "259": "Frequency of service.", "260": "Length of medical necessity, including begin date.", "261": "Obesity measurements.", "262": "Type of surgery/service for which anesthesia was administered.", "263": "Length of time for services rendered.", "264": "Number of liters/minute & total hours/day for respiratory support.", "265": "Number of lesions excised.", "266": "Facility point of origin and destination - ambulance.", "267": "Number of miles patient was transported.", "268": "Location of durable medical equipment use.", "269": "Length/size of laceration/tumor.", "270": "Subluxation location.", "271": "Number of spine segments.", "272": "Oxygen contents for oxygen system rental.", "273": "Weight.", "274": "Height.", "275": "Claim.", "276": "UB04/HCFA-1450/1500 claim form.", "277": "Paper claim.", "279": "Claim/service must be itemized.", "281": "Related confinement claim.", "282": "Copy of prescription.", "283": "Medicare entitlement information is required to determine primary coverage.", "284": "Copy of Medicare ID card.", "286": "Other payer's Explanation of Benefits/payment information.", "287": "Medical necessity for service.", "288": "Hospital late charges.", "290": "Pre-existing information.", "291": "Reason for termination of pregnancy.", "292": "Purpose of family conference/therapy.", "293": "Reason for physical therapy.", "294": "Supporting documentation. Usage: At least one other status code is required to identify the supporting documentation.", "295": "Attending physician report.", "296": "Nurse's notes.", "297": "Medical notes/report.", "298": "Operative report.", "299": "Emergency room notes/report.", "300": "Lab/test report/notes/results.", "301": "MRI report.", "305": "Radiology/x-ray reports and/or interpretation.", "306": "Detailed description of service.", "307": "Narrative with pocket depth chart.", "308": "Discharge summary.", "310": "Progress notes for the six months prior to statement date.", "311": "Pathology notes/report.", "312": "Dental charting.", "313": "Bridgework information.", "314": "Dental records for this service.", "315": "Past perio treatment history.", "316": "Complete medical history.", "318": "X-rays/radiology films.", "319": "Pre/post-operative x-rays/photographs.", "320": "Study models.", "322": "Recent Full Mouth X-rays.", "323": "Study models, x-rays, and/or narrative.", "324": "Recent x-ray of treatment area and/or narrative.", "325": "Recent fm x-rays and/or narrative.", "326": "Copy of transplant acquisition invoice.", "327": "Periodontal case type diagnosis and recent pocket depth chart with narrative.", "329": "Exercise notes.", "330": "Occupational notes.", "331": "History and physical.", "333": "Patient release of information authorization.", "334": "Oxygen certification.", "335": "Durable medical equipment certification.", "336": "Chiropractic certification.", "337": "Ambulance certification/documentation.", "339": "Enteral/parenteral certification.", "340": "Pacemaker certification.", "341": "Private duty nursing certification.", "342": "Podiatric certification.", "343": "Documentation that facility is state licensed and Medicare approved as a surgical facility.", "344": "Documentation that provider of physical therapy is Medicare Part B approved.", "345": "Treatment plan for service/diagnosis.", "346": "Proposed treatment plan for next 6 months.", "352": "Duration of treatment plan.", "353": "Orthodontics treatment plan.", "354": "Treatment plan for replacement of remaining missing teeth.", "359": "Health care coverage end date.", "360": "Benefits Assignment Certification Indicator.", "363": "Possible Workers' Compensation.", "364": "Is accident/illness/condition employment related?", "365": "Is service the result of an accident?", "366": "Is injury due to auto accident?", "374": "Is prescribed lenses a result of cataract surgery?", "375": "Was refraction performed?", "380": "CRNA supervision/medical direction.", "382": "Did provider authorize generic or brand name dispensing?", "383": "Nerve block use (surgery vs. pain management).", "384": "Is prosthesis/crown/inlay placement an initial placement or a replacement?", "385": "Is appliance upper or lower arch & is appliance fixed or removable?", "386": "Orthodontic Treatment/Purpose Indicator.", "387": "Date patient last examined by entity. Usage: This code requires use of an Entity Code.", "388": "Date post-operative care assumed.", "389": "Date post-operative care relinquished.", "390": "Date of most recent medical event necessitating service(s).", "391": "Date(s) dialysis conducted.", "394": "Date(s) of most recent hospitalization related to service.", "395": "Date entity signed certification/recertification. Usage: This code requires use of an Entity Code.", "396": "Date home dialysis began.", "397": "Date of onset/exacerbation of illness/condition.", "398": "Visual field test results.", "400": "<PERSON><PERSON><PERSON> is out of balance.", "401": "Source of payment is not valid.", "402": "Amount must be greater than zero. Usage: At least one other status code is required to identify which amount element is in error.", "403": "Entity referral notes/orders/prescription. Effective 05/01/2018: Entity referral notes/orders/prescription. Usage: this code requires use of an entity code.", "406": "Brief medical history as related to service(s).", "407": "Complications/mitigating circumstances.", "408": "Initial certification.", "409": "Medication logs/records (including medication therapy).", "414": "Necessity for concurrent care (more than one physician treating the patient).", "417": "Prior testing, including result(s) and date(s) as related to service(s).", "419": "Individual test(s) comprising the panel and the charges for each test.", "420": "Name, dosage and medical justification of contrast material used for radiology procedure.", "428": "Reason for transport by ambulance.", "430": "Nearest appropriate facility.", "431": "Patient's condition/functional status at time of service.", "432": "Date benefits exhausted.", "433": "Copy of patient revocation of hospice benefits.", "434": "Reasons for more than one transfer per entitlement period.", "435": "Notice of Admission.", "441": "Entity professional qualification for service(s).", "442": "Modalities of service.", "443": "Initial evaluation report.", "449": "Projected date to discontinue service(s).", "450": "Awaiting spend down determination.", "451": "Preoperative and post-operative diagnosis.", "452": "Total visits in total number of hours/day and total number of hours/week.", "453": "Procedure Code Modifier(s) for Service(s) Rendered.", "454": "Procedure code for services rendered.", "455": "Revenue code for services rendered.", "456": "Covered Day(s).", "457": "Non-Covered Day(s).", "458": "Coinsurance Day(s).", "459": "Lifetime Reserve Day(s).", "460": "NUBC Condition Code(s).", "464": "Payer Assigned Claim Control Number.", "465": "Principal Procedure Code for Service(s) Rendered.", "466": "Entity's Original Signature. Usage: This code requires use of an Entity Code.", "467": "Entity Signature Date. Usage: This code requires use of an Entity Code.", "468": "Patient Signature Source.", "469": "Purchase Service Charge.", "470": "Was service purchased from another entity? Usage: This code requires use of an Entity Code.", "471": "Were services related to an emergency?", "472": "Ambulance Run Sheet.", "473": "Missing or invalid lab indicator.", "474": "Procedure code and patient gender mismatch.", "475": "Procedure code not valid for patient age.", "476": "Missing or invalid units of service.", "477": "Diagnosis code pointer is missing or invalid.", "478": "Claim submitter's identifier.", "479": "Other Carrier payer ID is missing or invalid.", "480": "Entity's claim filing indicator. Usage: This code requires use of an Entity Code.", "481": "Claim/submission format is invalid.", "483": "Maximum coverage amount met or exceeded for benefit period.", "484": "Business Application Currently Not Available.", "485": "More information available than can be returned in real time mode. Narrow your current search criteria. This change effective September 1, 2017: More information available than can be returned in real-time mode. Narrow your current search criteria.", "486": "Principal Procedure Date.", "487": "Claim not found, claim should have been submitted to/through 'entity'. Usage: This code requires use of an Entity Code.", "488": "Diagnosis code(s) for the services rendered.", "489": "Attachment Control Number.", "490": "Other Procedure Code for Service(s) Rendered.", "491": "Entity not eligible for encounter submission. Usage: This code requires use of an Entity Code.", "492": "Other Procedure Date.", "493": "Version/Release/Industry ID code not currently supported by information holder.", "494": "Real-Time requests not supported by the information holder, resubmit as batch request. This change effective September 1, 2017: Real-time requests not supported by the information holder, resubmit as batch request.", "495": "Requests for re-adjudication must reference the newly assigned payer claim control number for this previously adjusted claim. Correct the payer claim control number and re-submit.", "496": "Submitter not approved for electronic claim submissions on behalf of this entity. Usage: This code requires use of an Entity Code.", "497": "Sales tax not paid.", "498": "Maximum leave days exhausted.", "499": "No rate on file with the payer for this service for this entity. Usage: This code requires use of an Entity Code.", "500": "Entity's Postal/Zip Code. Usage: This code requires use of an Entity Code.", "501": "Entity's State/Province. Usage: This code requires use of an Entity Code.", "502": "Entity's City. Usage: This code requires use of an Entity Code.", "503": "Entity's Street Address. Usage: This code requires use of an Entity Code.", "504": "Entity's Last Name. Usage: This code requires use of an Entity Code.", "505": "Entity's First Name. Usage: This code requires use of an Entity Code.", "506": "Entity is changing processor/clearinghouse. This claim must be submitted to the new processor/clearinghouse. Usage: This code requires use of an Entity Code.", "507": "HCPCS.", "508": "ICD9. Usage: At least one other status code is required to identify the related procedure code or diagnosis code.", "509": "External Cause of Injury Code.", "510": "Future date. Usage: At least one other status code is required to identify the data element in error.", "511": "Invalid character. Usage: At least one other status code is required to identify the data element in error.", "512": "Length invalid for receiver's application system. Usage: At least one other status code is required to identify the data element in error.", "513": "HIPPS Rate Code for services Rendered.", "514": "Entity's Middle Name. Usage: This code requires use of an Entity Code.", "515": "Managed Care review.", "516": "Other Entity's Adjudication or Payment/Remittance Date. Usage: An Entity code is required to identify the Other Payer Entity, i.e. primary, secondary.", "517": "Adjusted Repriced Claim Reference Number.", "518": "Adjusted Repriced Line item Reference Number.", "519": "Adjustment Amount.", "520": "Adjustment Quantity.", "521": "Adjustment Reason Code.", "522": "Anesthesia Modifying Units.", "523": "Anesthesia Unit Count.", "524": "Arterial Blood Gas Quantity.", "525": "Begin Therapy Date.", "526": "Bundled or Unbundled Line Number.", "527": "Certification Condition Indicator.", "528": "Certification Period Projected Visit Count.", "529": "Certification Revision Date.", "530": "Claim Adjustment Indicator.", "531": "Claim Disproportionate Share Amount.", "532": "Claim DRG Amount.", "533": "Claim DRG Outlier Amount.", "534": "Claim ESRD Payment Amount.", "535": "Claim Frequency Code.", "536": "Claim Indirect Teaching Amount.", "537": "Claim MSP Pass-through Amount.", "538": "Claim or Encounter Identifier.", "539": "Claim PPS Capital Amount.", "540": "Claim PPS Capital Outlier Amount.", "541": "Claim Submission Reason Code.", "542": "Claim Total Denied Charge Amount.", "543": "Clearinghouse or Value Added Network Trace.", "544": "Clinical Laboratory Improvement Amendment (CLIA) Number.", "545": "Contract Amount.", "546": "Contract Code.", "547": "Contract Percentage.", "548": "Contract Type Code.", "549": "Contract Version Identifier.", "550": "Coordination of Benefits Code.", "551": "Coordination of Benefits Total Submitted Charge.", "552": "Cost Report Day Count.", "553": "Covered Amount.", "554": "Date Clai<PERSON>.", "555": "Delay Reason Code.", "556": "Demonstration Project Identifier.", "557": "Diagnosis Date.", "558": "Discount Amount.", "559": "Document Control Identifier.", "560": "Entity's Additional/Secondary Identifier. Usage: This code requires use of an Entity Code.", "561": "Entity's Contact Name. Usage: This code requires use of an Entity Code.", "562": "Entity's National Provider Identifier (NPI). Usage: This code requires use of an Entity Code.", "563": "Entity's Tax Amount. Usage: This code requires use of an Entity Code.", "564": "EPSDT Indicator.", "565": "Estimated Claim Due Amount.", "566": "Exception Code.", "567": "Facility Code Qualifier.", "568": "Family Planning Indicator.", "569": "Fixed Format Information.", "571": "Frequency Count.", "572": "Frequency Period.", "573": "Functional Limitation Code.", "574": "HCPCS Payable Amount Home Health.", "575": "Homebound Indicator.", "576": "Immunization Batch Number.", "577": "Industry Code.", "578": "Insurance Type Code.", "579": "Investigational Device Exemption Identifier.", "580": "Last Certification Date.", "581": "Last Worked Date.", "582": "Lifetime Psychiatric Days Count.", "583": "Line Item Charge Amount.", "584": "Line Item Control Number.", "585": "Denied Charge or Non-covered Charge.", "586": "Line Note Text.", "587": "Measurement Reference Identification Code.", "588": "Medical Record Number.", "589": "Provider Accept Assignment Code.", "590": "Medicare Coverage Indicator.", "591": "Medicare Paid at 100% Amount.", "592": "Medicare Paid at 80% Amount.", "593": "Medicare Section 4081 Indicator.", "594": "Mental Status Code.", "595": "Monthly Treatment Count.", "596": "Non-covered Charge Amount.", "597": "Non-payable Professional Component Amount.", "598": "Non-payable Professional Component Billed Amount.", "599": "Note Reference Code.", "600": "Oxygen Saturation Qty.", "601": "Oxygen Test Condition Code.", "602": "Oxygen Test Date.", "603": "Old Capital Amount.", "604": "Originator Application Transaction Identifier.", "605": "Orthodontic Treatment Months Count.", "606": "Paid From Part A Medicare Trust Fund Amount.", "607": "Paid From Part B Medicare Trust Fund Amount.", "608": "Paid Service Unit Count.", "609": "Participation Agreement.", "610": "Patient Discharge Facility Type Code.", "611": "Peer Review Authorization Number.", "612": "Per Day Limit Amount.", "613": "Physician Contact Date.", "614": "Physician Order Date.", "615": "Policy Compliance Code.", "616": "Policy Name.", "617": "Postage Claimed Amount.", "618": "PPS-Capital DSH DRG Amount.", "619": "PPS-Capital Exception Amount.", "620": "PPS-Capital FSP DRG Amount.", "621": "PPS-Capital HSP DRG Amount.", "622": "PPS-Capital IME Amount.", "623": "PPS-Operating Federal Specific DRG Amount.", "624": "PPS-Operating Hospital Specific DRG Amount.", "625": "Predetermination of Benefits Identifier.", "626": "Pregnancy Indicator.", "627": "Pre-Tax Claim Amount.", "628": "Pricing Methodology.", "629": "Property Casualty Claim Number.", "630": "Referring CLIA Number.", "631": "Reimbursement Rate.", "632": "Reject Reason Code.", "633": "Related Causes Code (Accident, auto accident, employment).", "634": "Remark Code.", "635": "Repriced Ambulatory Patient Group Code.", "636": "Repriced Line Item Reference Number.", "637": "Repriced Saving Amount.", "638": "Repricing Per Diem or Flat Rate Amount.", "639": "Responsibility Amount.", "640": "Sales Tax Amount.", "642": "Service Authorization Exception Code.", "643": "Service Line Paid Amount.", "644": "Service Line Rate.", "645": "Service Tax Amount.", "646": "Ship, Delivery or Calendar Pattern Code.", "647": "Shipped Date.", "648": "Similar Illness or Symptom Date.", "649": "Skilled Nursing Facility Indicator.", "650": "Special Program Indicator.", "651": "State Industrial Accident Provider Number.", "652": "Terms Discount Percentage.", "653": "Test Performed Date.", "654": "Total Denied Charge Amount.", "655": "Total Medicare Paid Amount.", "656": "Total Visits Projected This Certification Count.", "657": "Total Visits Rendered Count.", "658": "Treatment Code.", "659": "Unit or Basis for Measurement Code.", "660": "Universal Product Number.", "661": "Visits Prior to Recertification Date Count CR702.", "662": "X-ray Availability Indicator.", "663": "Entity's Group Name. Usage: This code requires use of an Entity Code.", "664": "Orthodontic Banding Date.", "665": "Surgery Date.", "666": "Surgical Procedure Code.", "667": "Real-Time requests not supported by the information holder, do not resubmit. This change effective September 1, 2017: Real-time requests not supported by the information holder, do not resubmit.", "668": "Missing Endodontics treatment history and prognosis.", "669": "Dental service narrative needed.", "670": "Funds applied from a consumer spending account such as consumer directed/driven health plan (CDHP), Health savings account (H S A) and or other similar accounts.", "671": "Funds may be available from a consumer spending account such as consumer directed/driven health plan (CDHP), Health savings account (H S A) and or other similar accounts.", "672": "Other Payer's payment information is out of balance.", "673": "Patient Reason for Visit.", "674": "Authorization exceeded.", "675": "Facility admission through discharge dates.", "676": "Entity possibly compensated by facility. Usage: This code requires use of an Entity Code.", "677": "Entity not affiliated. Usage: This code requires use of an Entity Code.", "678": "Revenue code and patient gender mismatch.", "679": "Submit newborn services on mother's claim.", "680": "Entity's Country. Usage: This code requires use of an Entity Code.", "681": "Claim currency not supported.", "682": "Cosmetic procedure.", "683": "Awaiting Associated Hospital Claims.", "684": "Rejected. Syntax error noted for this claim/service/inquiry. See Functional or Implementation Acknowledgement for details. (Usage: Only for use to reject claims or status requests in transactions that were 'accepted with errors' on a 997 or 999 Acknowledgement.)", "685": "<PERSON><PERSON><PERSON> could not complete adjudication in real time. <PERSON><PERSON><PERSON> will continue processing in a batch mode. Do not resubmit. This change effective September 1, 2017: <PERSON><PERSON><PERSON> could not complete adjudication in real-time. <PERSON><PERSON><PERSON> will continue processing in a batch mode. Do not resubmit.", "686": "The claim/encounter has completed the adjudication cycle and the entire claim has been voided.", "687": "Claim estimation cannot be completed in real time. Do not resubmit. This change effective September 1, 2017: Claim predetermination/estimation could not be completed in real-time. Do not resubmit.", "688": "Present on Admission Indicator for reported diagnosis code(s).", "689": "Entity was unable to respond within the expected time frame. Usage: This code requires use of an Entity Code.", "690": "Multiple claims or estimate requests cannot be processed in real time. This change effective September 1, 2017: Multiple claims or estimate requests cannot be processed in real-time.", "691": "Multiple claim status requests cannot be processed in real time. This change effective September 1, 2017: Multiple claim status requests cannot be processed in real-time.", "692": "Contracted funding agreement-Subscriber is employed by the provider of services.", "693": "Amount must be greater than or equal to zero. Usage: At least one other status code is required to identify which amount element is in error.", "694": "Amount must not be equal to zero. Usage: At least one other status code is required to identify which amount element is in error.", "695": "Entity's Country Subdivision Code. Usage: This code requires use of an Entity Code.", "696": "Claim Adjustment Group Code.", "697": "Invalid Decimal Precision. Usage: At least one other status code is required to identify the data element in error.", "698": "Form Type Identification.", "699": "Question/Response from Supporting Documentation Form.", "700": "ICD10. Usage: At least one other status code is required to identify the related procedure code or diagnosis code.", "701": "Initial Treatment Date.", "702": "Repriced Claim Reference Number.", "703": "Advanced Billing Concepts (ABC) code.", "704": "Claim Note Text.", "705": "Repriced Allowed Amount.", "706": "Repriced Approved Amount.", "707": "Repriced Approved Ambulatory Patient Group Amount.", "708": "Repriced Approved Revenue Code.", "709": "Repriced Approved Service Unit Count.", "710": "Line Adjudication Information. Usage: At least one other status code is required to identify the data element in error.", "711": "Stretcher purpose.", "712": "Obstetric Additional Units.", "713": "Patient Condition Description.", "714": "Care Plan Oversight Number.", "715": "Acute Manifestation Date.", "716": "Repriced Approved DRG Code.", "717": "This claim has been split for processing.", "718": "Claim/service not submitted within the required timeframe (timely filing).", "719": "NUBC Occurrence Code(s).", "720": "NUBC Occurrence Code Date(s).", "721": "NUBC Occurrence Span Code(s).", "722": "NUBC Occurrence Span Code Date(s).", "723": "Drug days supply.", "724": "Drug dosage.", "725": "NUBC Value Code(s).", "726": "NUBC Value Code Amount(s).", "727": "Accident date.", "728": "Accident state.", "729": "Accident description.", "730": "Accident cause.", "731": "Measurement value/test result.", "732": "Information submitted inconsistent with billing guidelines. Usage: At least one other status code is required to identify the inconsistent information.", "733": "Prefix for entity's contract/member number.", "734": "Verifying premium payment.", "735": "This service/claim is included in the allowance for another service or claim.", "736": "A related or qualifying service/claim has not been received/adjudicated.", "737": "Current Dental Terminology (CDT) Code.", "738": "Home Infusion EDI Coalition (HEIC) Product/Service Code.", "739": "Jurisdiction Specific Procedure or Supply Code.", "740": "Drop-Off Location.", "741": "Entity must be a person. Usage: This code requires use of an Entity Code.", "742": "Payer Responsibility Sequence Number Code.", "743": "Entity's credential/enrollment information. Usage: This code requires use of an Entity Code.", "744": "Services/charges related to the treatment of a hospital-acquired condition or preventable medical error.", "745": "Identifier Qualifier Usage: At least one other status code is required to identify the specific identifier qualifier in error.", "746": "Duplicate Submission Usage: use only at the information receiver level in the Health Care Claim Acknowledgement transaction.", "747": "Hospice Employee Indicator.", "748": "Corrected Data Usage: Requires a second status code to identify the corrected data.", "749": "Date of Injury/Illness.", "750": "Auto Accident State or Province Code.", "751": "Ambulance Pick-up State or Province Code.", "752": "Ambulance Drop-off State or Province Code.", "753": "Co-pay status code.", "754": "Entity Name Suffix. Usage: This code requires the use of an Entity Code.", "755": "Entity's primary identifier. Usage: This code requires the use of an Entity Code.", "756": "Entity's Received Date. Usage: This code requires the use of an Entity Code.", "757": "Last seen date.", "758": "Repriced approved HCPCS code.", "759": "Round trip purpose description.", "760": "Tooth status code.", "761": "Entity's referral number. Usage: This code requires the use of an Entity Code.", "762": "Locum Tenens Provider Identifier. Code must be used with Entity Code 82 - Rendering Provider.", "763": "Ambulance Pickup ZipCode.", "764": "Professional charges are non covered.", "765": "Institutional charges are non covered.", "766": "Services were performed during a Health Insurance Exchange (HIX) premium payment grace period.", "767": "Qualifications for emergent/urgent care.", "768": "Service date outside the accidental injury coverage period.", "769": "DME Repair or Maintenance.", "770": "Duplicate of a claim processed or in process as a crossover/coordination of benefits claim.", "771": "<PERSON><PERSON><PERSON> submitted prematurely. Please resubmit after crossover/payer to payer COB allotted waiting period.", "772": "The greatest level of diagnosis code specificity is required.", "773": "One calendar year per claim.", "774": "Experimental/Investigational.", "775": "Entity Type Qualifier (Person/Non-Person Entity). Usage: this code requires use of an entity code.", "776": "Pre/Post-operative care.", "777": "Processed based on multiple or concurrent procedure rules.", "778": "Non-Compensable incident/event. Usage: To be used for Property and Casualty only.", "779": "Service submitted for the same/similar service within a set timeframe.", "780": "Lifetime benefit maximum.", "781": "<PERSON><PERSON><PERSON> has been identified as a readmission.", "782": "Second surgical opinion.", "783": "Federal sequestration adjustment.", "784": "Electronic Visit Verification criteria do not match.", "785": "Missing/Invalid Sterilization/Abortion/Hospital Consent Form.", "786": "Submit claim to the third party property and casualty automobile insurer.", "787": "Resubmit a new claim, not a replacement claim.", "788": "Submit these services to the Pharmacy plan/processor for further consideration/adjudication.", "789": "Submit these services to the patient's Medical Plan for further consideration.", "790": "Submit these services to the patient's Dental Plan for further consideration.", "791": "Submit these services to the patient's Vision Plan for further consideration.", "792": "Submit these services to the patient's Behavioral Health Plan for further consideration.", "793": "Submit these services to the patient's Property and Casualty Plan for further consideration.", "794": "<PERSON><PERSON><PERSON> could not complete adjudication in real time. Resubmit as a batch request.", "795": "<PERSON><PERSON><PERSON> submitted prematurely. Please provide the prior payer's final adjudication.", "796": "Procedure code not valid for date of service.", "798": "Claim predetermination/estimation could not be completed in real time. Claim requires manual review upon submission. Do not resubmit.", "799": "Resubmit a replacement claim, not a new claim.", "800": "Entity's required reporting has been forwarded to the jurisdiction. Usage: This code requires use of an Entity Code. To be used for Property and Casualty only.", "801": "Entity's required reporting was accepted by the jurisdiction. Usage: This code requires use of an Entity Code. To be used for Property and Casualty only.", "802": "Entity's required reporting was rejected by the jurisdiction. Usage: This code requires use of an Entity Code. To be used for Property and Casualty only.", "803": "Provider reporting has been rejected due to non-compliance with the jurisdiction's mandated registration. To be used for Property and Casualty only.", "804": "Exceeds inquiry limit for batch.", "805": "Mammography Certification Number.", "806": "Residential county does not match the county of the service location.", "807": "Health Risk Assessment."}}