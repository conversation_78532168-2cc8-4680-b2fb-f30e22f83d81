"use strict";
const _ = require("lodash");
const jest = require("jest");
const testRootPath = global.nesroot;
const sharedState = require("@tests/sharedState");
const fs = require("fs");
const { startArrayTransport, removeArrayTransport } = require("@core/logger");
const GetBilling = require("../billing/index");
module.exports = class ApiView {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.billing = new GetBilling(nes);
    }

    async process(ctx, urlpath) {
        try {
            const form = urlpath.path[2];
            this.skippedSubforms = [
                "segment_cob",
                "segment_workers_comp",
                "segment_dur",
                "segment_coupon",
                "segment_compound",
                "subform_oclaim",
                "segment_clinical",
                "segment_docs",
                "segment_facility",
                "segment_narrative",
                "addtl_segments",
                "dependent",
                "claim_pricing_repricing_information",
                "service_facility_location",
                "other_subscriber_information",
                "drug_identification",
                "durable_medical_equipment_service",
                "durable_medical_equipment_certificate_of_medical_necessity",
                "durable_medical_equipment_certification",
                "condition_indicator_durable_medical_equipment",
                "line_pricing_repricing_information",
                "line_adjudication_information",
                "form_identification",
                "service_line_supplemental_information",
                "file_information",
                "referral_number",
                "prior_authorization",
                "claim_note",
                "claim_date_information",
                "claim_contract_information",
                "condition_information",
                "file_information_list",
            ];
            if (form == "ncpdp") {
                this.defaultJsonBlob = JSON.parse(
                    fs.readFileSync(__dirname + "/ncpdp_default.json", "utf8")
                );
                this.defaultNCPDPECL = JSON.parse(
                    fs.readFileSync(__dirname + "/ncpdp_ecl.json", "utf8")
                );
            } else if (form == "med_claim") {
                this.defaultJsonBlob = JSON.parse(
                    fs.readFileSync(
                        __dirname + "/med_claim_default.json",
                        "utf8"
                    )
                );
                this.defaultNCPDPECL = JSON.parse(
                    fs.readFileSync(__dirname + "/med_claim_ecl.json", "utf8")
                );
            }
            const arrayJsonBlobs = {};
            const combinations = this.generateCombinations(
                this.defaultNCPDPECL,
                10
            );

            for (let i = 0; i < combinations.length; i++) {
                const jsonBlob = this.parseFieldsToJsonBlob(
                    form,
                    combinations[i]
                );

                try {
                    const resp = await this.testClaims(ctx, jsonBlob, form);
                    arrayJsonBlobs[`OK ${i + 1}`] = resp;
                } catch (_error) {
                    arrayJsonBlobs[`Failed ${i + 1}`] = ctx.body;
                }
            }
            ctx.body = arrayJsonBlobs;
            ctx.status = 200;
        } catch (error) {
            console.error("Error running tests:", error);
            ctx.status = 500;
            ctx.body = {
                error: `An error occurred while running tests: ${error}. Stack trace: ${error.stack}`,
            };
        }
    }

    generateCombinations(eclObject, limit = Infinity) {
        const keys = Object.keys(eclObject);
        const combinations = [];
        let count = 0;

        const recurse = (currentCombo, depth) => {
            if (count >= limit || depth === keys.length) {
                if (depth === keys.length) {
                    combinations.push({ ...currentCombo });
                    count++;
                }
                return;
            }

            const key = keys[depth];
            for (const code in eclObject[key]) {
                if (count >= limit) break;
                recurse({ ...currentCombo, [key]: code }, depth + 1);
            }
        };

        recurse({}, 0);
        return combinations;
    }

    parseFieldsToJsonBlob(formName, eclCombination) {
        const jsonBlob = {};
        const fields = this.shared.DSL[formName].fields;
        for (const key in fields) {
            if (fields.hasOwnProperty(key)) {
                const field = fields[key];
                if (field && typeof field === "object" && field.model) {
                    const shouldAddField = this.shouldAddField(
                        key,
                        jsonBlob,
                        formName
                    );
                    if (shouldAddField) {
                        this.addFieldToJsonBlob(
                            key,
                            field,
                            jsonBlob,
                            eclCombination
                        );
                    }
                }
            }
        }

        return jsonBlob;
    }

    shouldAddField(key, jsonBlob, form) {
        const existInCondition = this.findConditionObjectByKey(
            this.shared.DSL[form].fields,
            key
        );
        if (existInCondition) {
            const { fieldName, conditionKey, _ } = existInCondition;
            const matched = this.inRange(
                this.shared.DSL[form].fields[fieldName],
                conditionKey,
                jsonBlob[fieldName]
            );
            if (matched) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    findConditionObjectByKey(dslFields, fieldKey) {
        for (const fieldName in dslFields) {
            const field = dslFields[fieldName];
            if (field.model && field.model.if) {
                const modelIf = field.model.if;
                for (const conditionKey in modelIf) {
                    const conditionObj = modelIf[conditionKey];
                    if (
                        Array.isArray(conditionObj.fields) &&
                        conditionObj.fields.includes(fieldKey)
                    ) {
                        return {
                            fieldName,
                            conditionKey,
                            conditionObj,
                        };
                    }
                }
            }
        }
        return false;
    }

    addFieldToJsonBlob(key, field, jsonBlob, eclCombination) {
        if (key == "id") {
            jsonBlob[key] =
                Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
        } else if (field.model.source && field.model.type === "subform") {
            const subformName = field.model.source;
            const subformData = this.parseFieldsToJsonBlob(
                subformName,
                eclCombination
            );
            jsonBlob[key] = this.skippedSubforms.includes(key)
                ? []
                : [subformData];
        } else if (
            field.model.source &&
            typeof field.model.source === "string" &&
            field.model.source.includes("ecl") &&
            !field.model.source.includes("ext")
        ) {
            const sf = field.model.sourcefilter.field.static;
            const eclCode = eclCombination[sf];
            const name = this.defaultNCPDPECL[sf][eclCode];
            jsonBlob[key] = field.model.multi
                ? [eclCode]
                : eclCode || this.defaultJsonBlob[key];
            jsonBlob[key + "_auto_name"] =
                eclCode && name
                    ? field.model.multi
                        ? [`${eclCode} - ${name}`]
                        : `${eclCode} - ${name}`
                    : this.defaultJsonBlob[key];
        } else if (field.model.source) {
            jsonBlob[key] = this.defaultJsonBlob[key] || null;
            jsonBlob[key + "_auto_name"] =
                this.defaultJsonBlob[key + "_auto_name"];
        } else {
            jsonBlob[key] = this.defaultJsonBlob[key] || null;
        }
    }

    inRange(f, r, v) {
        const parseType = (vl) => {
            if (f.model.type === "int") {
                return parseInt(vl);
            } else if (f.model.type === "decimal") {
                return parseFloat(vl);
            } else {
                return typeof vl === "string" ? vl.toLowerCase() : vl;
            }
        };
        if (f.model.multi) {
            if (Array.isArray(v)) {
                for (const dt of v) {
                    if (r === dt) {
                        return true;
                    }
                }
            }
            return false;
        } else if (f.view.control === "checkbox") {
            if (Array.isArray(v)) {
                for (const dt of v) {
                    if (r === dt) {
                        return true;
                    }
                }
            }
        }
        v = parseType(v);
        const sides = r.split(/\.\.|\.<|>\.|></);
        if (sides.length === 1) {
            if (sides[0].includes("!")) {
                return v !== parseType(sides[0].replace("!", "")); // '!12'
            }
            if (sides[0].includes(">")) {
                return v > parseType(sides[0].replace(">", "")); // '>12' or '12>'
            }
            if (sides[0].includes("<")) {
                return v < parseType(sides[0].replace("<", "")); // '<14' or '14<'
            }
            return v === parseType(r);
        } else if (sides.length === 2) {
            if (sides[1] === "" && r.includes("..")) {
                // '12..'
                return v >= parseType(sides[0]);
            }
            if (sides[0] === "" && r.includes("..")) {
                // '..14'
                return v <= parseType(sides[1]);
            }
            if (sides[0] !== "" && sides[1] !== "") {
                // 'x??y'
                if (r.includes("..")) {
                    // '12..14'
                    return v >= parseType(sides[0]) && v <= parseType(sides[1]);
                }
                if (r.includes(">.")) {
                    // '12>.14'
                    return v > parseType(sides[0]) && v <= parseType(sides[1]);
                }
                if (r.includes(".<")) {
                    // '12.<14'
                    return v >= parseType(sides[0]) && v < parseType(sides[1]);
                }
                if (r.includes("><")) {
                    // '12><14'
                    return v > parseType(sides[0]) && v < parseType(sides[1]);
                }
            }
        }
        return false;
    }

    async testClaims(ctx, json, form) {
        try {
            ctx.request.body = json;
            ctx.is_test = true;
            if (form == "ncpdp") {
                await this.billing.__ncpdpClaimTester(ctx);
                if (ctx.body.error) {
                    throw new Error(ctx.body.error);
                } else if (ctx.status == 200) {
                    return ctx.body;
                }
            } else if (form == "med_claim") {
                await this.billing.__medClaimTester(ctx);
                if (ctx.body.error) {
                    throw new Error(ctx.body.error);
                } else if (ctx.status == 200) {
                    return ctx.body;
                }
            }
        } catch (error) {
            console.log(error);
            throw new Error(error);
        }
    }
    /**
     * Mocks a test file and runs it using Jest.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} filename - The filename of the test class to be executed.
     * @returns {Promise<Object>} The test results including success status, test results, and coverage data.
     * @throws {Error} If an error occurs while running the tests.
     */
    async mockServerTest(ctx, filenames) {
        // Mock the test file
        // Set up global objects
        global.ctx = ctx;
        global.nes = this.nes;

        try {
            // Create a new array with the correct test file patterns
            const testPatterns = filenames.map(
                (file) => `**/${file.filename}.test.js`
            );
            console.log(testPatterns);
            const arrayTransport = startArrayTransport();
            // Run Jest programmatically
            const results = await jest.runCLI(
                {
                    rootDir: testRootPath,
                    testMatch: testPatterns,
                    collectCoverage: true,
                    coverageReporters: ["json-summary"],
                    silent: false,
                    verbose: false,
                    runInBand: true,
                    testEnvironment: "node",
                },
                [process.cwd()]
            );

            const allLogs = [];
            const capturedLogsServer = arrayTransport.getLogs() || [];
            allLogs.push(`Server Test Logs ${capturedLogsServer.length}`);

            const capturedLogsUnitTest = sharedState.get().logs || [];
            allLogs.push(`Unit Test Logs ${capturedLogsUnitTest.length}`);

            const finalLogs = allLogs
                .concat(capturedLogsServer)
                .concat(capturedLogsUnitTest);
            removeArrayTransport(arrayTransport);
            return {
                results: results.results,
                capturedLogs: finalLogs,
            };
        } catch (error) {
            console.error(error);
            throw error;
        } finally {
            // Clean up global objects
            delete global.ctx;
            delete global.nes;
        }
    }
};
