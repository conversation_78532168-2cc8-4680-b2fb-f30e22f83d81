"use strict";

module.exports = class DrugAllergyNdcAlrgnClass {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
    }

    async getInteractions(ndc, dam_concept_id, desc) {
        if (!ndc) throw new Error("NDC cannot be empty");
        if (!dam_concept_id) throw new Error("dam_concept_id cannot be empty");
        const [allergies, hicSeqnList] = await this.getNDCIngredients(
            ndc,
            dam_concept_id
        );
        if (Object.keys(hicSeqnList).length < 1) {
            throw new Error("No hic seqn found for ndc: " + ndc);
        }
        const intAllergies = await this.runInteractions(
            dam_concept_id,
            hicSeqnList
        );
        for (const [hic, info] of Object.entries(intAllergies)) {
            if (hic in allergies) {
                allergies[hic].desc = allergies[hic].desc + "," + info.desc;
                allergies[hic].type = allergies[hic].type + "," + info.type;
            }
        }
        const allAllergies = { ...intAllergies, ...allergies };
        for (const alrgn of Object.keys(allAllergies))
            allAllergies[alrgn]["alrgn"] = desc;
        return allAllergies;
    }
    async getNDCIngredients(ndc, dam_concept_id) {
        const par = [ndc];
        const hicSeqnList = {};
        const allergies = {};

        // step 1-3
        const sql = `
            SELECT
                fha.dam_alrgn_hic_seqn,
                fhd.hic_desc
            FROM
                form_list_fdb_ndc fn
                JOIN form_list_fdb_gcnseqno_mstr ff ON ff.gcn_seqno = fn.gcn_seqno
                JOIN form_list_fdb_hic_hicl_alrg_link fha ON ff.hicl_seqno = fha.hicl_seqno
                JOIN form_list_fdb_hic_desc fhd ON fhd.hic_seqn = fha.dam_alrgn_hic_seqn
            WHERE
                fn.ndc = %L`;
        const activeHicSeqn = await this.db.env.ro.query(sql, par);
        if (activeHicSeqn.length < 1) {
            throw new Error("No hic_seqn found for ndc: " + ndc);
        }
        activeHicSeqn.map((h) => {
            hicSeqnList[h.dam_alrgn_hic_seqn] = {
                hic_seqn: h.dam_alrgn_hic_seqn,
                hic_desc: h.hic_desc,
            };
        });

        const inactiveReviewed = await this.getInactiveReviewed(ndc);
        if (inactiveReviewed.length < 1) {
            const potentialInactive =
                await this.getAlrgnPotentialInactive(dam_concept_id);
            for (const [hic, info] of Object.entries(potentialInactive))
                allergies[hic] = info;
        }
        const iaHICS = await this.getInactiveLinkedIng(ndc);
        iaHICS.map((h) => {
            if (!(h.hic_seqn in hicSeqnList))
                hicSeqnList[h.hic_seqn] = {
                    hic_seqn: h.hic_seqn,
                    hic_desc: h.hic_desc,
                };
        });
        return [allergies, hicSeqnList];
    }

    async runInteractions(alrgn_grp, hicSeqnList) {
        const par = [alrgn_grp];
        const allergies = {};
        const alHicSQL = `
            SELECT
                'Active ingredient group allergies' as type,
                hd.hic_desc as desc,
                hag.hic_seqn
            FROM
                form_list_fdb_hic_alrgn_grp_link hag
                JOIN form_list_fdb_hic_desc hd ON hag.hic_seqn = hd.hic_seqn
            WHERE
                dam_alrgn_grp = %s AND dam_alrgn_grp NOT IN (900388,900590,001218,000143,000795,000815,000816)`;
        const alHICSeqn = await this.db.env.ro.query(alHicSQL, par);
        if (alHICSeqn.length < 1) {
            throw new Error("No hic seqn found for alrgn grp: " + alrgn_grp);
        }
        for (const potential of alHICSeqn) {
            if (potential.hic_seqn in hicSeqnList)
                allergies[potential.hic_seqn] = {
                    type: potential.type,
                    desc: potential.desc,
                };
        }
        const xsHICSQL = `
            SELECT
                xlk.dam_alrgn_xsense,
                xlk.dam_alrgn_grp,
                hxal.hic_seqn,
                hd.hic_desc
            FROM
                form_list_fdb_alrgn_grp_xsense_link xlk
                JOIN form_list_fdb_xsensit_allergy_desc xsnlnk ON xlk.dam_alrgn_xsense = xsnlnk.dam_alrgn_xsense
                JOIN form_list_fdb_hic_alrgn_xsense_link hxal ON xlk.dam_alrgn_xsense = hxal.dam_alrgn_xsense
                JOIN form_list_fdb_hic_desc hd ON hxal.hic_seqn = hd.hic_seqn
            WHERE
                xlk.dam_alrgn_grp = %s
                AND xsnlnk.dam_alrgn_xsense_status_cd = 0
            ORDER BY
                xlk.dam_alrgn_xsense DESC,
                hd.hic_desc ASC`;
        const xsHICS = await this.db.env.ro.query(xsHICSQL, par);
        for (const xs of xsHICS) {
            if (xs.hic_seqn in hicSeqnList)
                allergies[xs.hic_seqn] = {
                    type: "Allergen group cross allergy",
                    desc: xs.hic_desc,
                };
        }
        return allergies;
    }
    async getAlrgnPotentialInactive(alrgn_grp) {
        const res = [];
        const par = [alrgn_grp];
        const sql = `
        SELECT 
            dsc.dam_grp_potentially_inactv_ind,
            dsc.dam_alrgn_grp_desc,
            dsc.dam_alrgn_grp,
            hdsc.hic_seqn,
            hdsc.hic_desc
        FROM form_list_fdb_alrgn_grp_desc dsc
        LEFT JOIN form_list_fdb_hic_alrgn_grp_link hgl on hgl.dam_alrgn_grp = dsc.dam_alrgn_grp
        LEFT JOIN form_list_fdb_hic_desc hdsc on hdsc.hic_seqn = hgl.hic_seqn
        WHERE 
            dsc.dam_alrgn_grp = %s AND dam_alrgn_grp_status_cd = 0`;
        const potentialInactive = await this.db.env.ro.query(sql, par);
        const inactives = potentialInactive.filter(
            (pi) => pi.dam_grp_potentially_inactv_ind == 1
        );
        if (inactives.length > 0)
            res[inactives[0].dam_alrgn_grp] = {
                type: "Potential inactive ingredients/allergen group",
                desc:
                    "Manually review the following: " +
                    `${inactives[0].dam_alrgn_grp_desc},${inactives.map((i) => i.hic_desc).join(",")}`,
            };
        return res;
    }
    async getInactiveReviewed(ndc) {
        const par = [ndc];
        const sql = `SELECT id FROM form_list_fdb_inactv_reviewed WHERE ndc = %L`;
        return await this.db.env.ro.query(sql, par);
    }
    async getInactiveLinkedIng(ndc) {
        const par = [ndc];
        const sql = ` SELECT dsc.hic_seqn, dsc.hic_desc
            FROM
                form_list_fdb_ndc_inactv_link lnk
                JOIN form_list_fdb_hic_desc dsc ON dsc.hic_seqn = lnk.hic_seqn
            WHERE
                ndc = %L;
        `;
        return await this.db.env.ro.query(sql, par);
    }
};
