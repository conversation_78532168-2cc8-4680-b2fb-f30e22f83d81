"use strict";

module.exports = class DrugAllergyNdcIngClass {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
    }

    async getInteractions(ndc, allergenHic, desc) {
        if (!ndc) throw new Error("NDC cannot be empty");
        if (!allergenHic) throw new Error("allergenHic cannot be empty");
        const [allergies, hicSeqnList] = await this.getNDCIngredients(
            ndc,
            allergenHic
        );
        if (Object.keys(hicSeqnList).length < 1) {
            throw new Error("No hic seqn found for ndc: " + ndc);
        }
        const intAllergies = await this.runInteractions(
            allergenHic,
            hicSeqnList
        );
        for (const [hic, info] of Object.entries(intAllergies)) {
            if (hic in allergies) {
                allergies[hic].desc = allergies[hic].desc + "," + info.desc;
                allergies[hic].type = allergies[hic].type + "," + info.type;
            }
        }
        const allAllergies = { ...intAllergies, ...allergies };
        for (const alrgn of Object.keys(allAllergies))
            allAllergies[alrgn]["alrgn"] = desc;
        return allAllergies;
    }

    async getNDCIngredients(ndc, allergenHic) {
        let par = [ndc];
        const hicSeqnList = {};
        const allergies = {};

        // step 1-3
        const sql = `
            SELECT
                fha.dam_alrgn_hic_seqn,
                fhd.hic_desc,
                fhd.hic_potentially_inactv_ind
            FROM
                form_list_fdb_ndc fn
                JOIN form_list_fdb_gcnseqno_mstr ff ON ff.gcn_seqno = fn.gcn_seqno
                JOIN form_list_fdb_hic_hicl_alrg_link fha ON ff.hicl_seqno = fha.hicl_seqno
                JOIN form_list_fdb_hic_desc fhd ON fhd.hic_seqn = fha.dam_alrgn_hic_seqn
            WHERE
                fn.ndc = %L`;

        const activeHicSeqn = await this.db.env.ro.query(sql, par);
        if (activeHicSeqn.length < 1) {
            throw new Error("No hic seqn found for ndc: " + ndc);
        }

        activeHicSeqn.map((h) => {
            hicSeqnList[h.dam_alrgn_hic_seqn] = {
                hic_seqn: h.dam_alrgn_hic_seqn,
                hic_desc: h.hic_desc,
                hic_potentially_inactv_ind: h.hic_potentially_inactv_ind,
            };
        });
        if (allergenHic in hicSeqnList) {
            allergies[allergenHic] = {
                desc: hicSeqnList[allergenHic].hic_desc,
                type: "Active ingredient based allergy",
            };
            // return allergies;
        }

        // related hics
        par = Object.values(hicSeqnList).map((hic) => hic.hic_seqn);
        const relatedHICSQL = `
            SELECT
                fhhl.related_hic_seqn as hic_seqn,
                fhd.hic_desc,
                fhd.hic_potentially_inactv_ind
            FROM
                form_list_fdb_hic_hic_link fhhl
                JOIN form_list_fdb_hic_desc fhd ON fhd.hic_seqn = fhhl.hic_seqn
            WHERE
                fhhl.hic_seqn IN (${Array(par.length).fill("%s").join(",")})`;
        const relatedHICs = await this.db.env.ro.query(relatedHICSQL, par);
        relatedHICs.map((h) => {
            hicSeqnList[h.hic_seqn] = {
                related: true,
                hic_seqn: h.hic_seqn,
                hic_desc: h.hic_desc,
                hic_potentially_inactv_ind: h.hic_potentially_inactv_ind,
            };
        });

        const inactiveReviewed = await this.getInactiveReviewed(ndc);
        if (inactiveReviewed.length < 1) {
            const potentialInactive = await this.getAlrgnPotentialInactive([
                ...Object.keys(hicSeqnList),
                allergenHic,
            ]);
            for (const [hic, info] of Object.entries(potentialInactive)) {
                if (hic in allergies) {
                    allergies[hic].desc = allergies[hic].desc + "," + info.desc;
                    allergies[hic].type = allergies[hic].type + "," + info.type;
                } else allergies[hic] = info;
            }
        }
        const iaHICS = await this.getInactiveLinkedIng(ndc);

        iaHICS.map((h) => {
            if (!(h.hic_seqn in hicSeqnList))
                hicSeqnList[h.hic_seqn] = {
                    inactive: true,
                    hic_seqn: h.hic_seqn,
                    hic_desc: h.hic_desc,
                };
        });
        return [allergies, hicSeqnList];
    }
    async runInteractions(allergenHic, hicSeqnList) {
        const allergies = {};
        for (const [hic, info] of Object.entries(hicSeqnList))
            if (hic == allergenHic) {
                allergies[allergenHic] = {
                    desc: info.hic_desc,
                    type: "Ingredient based allergic reaction",
                };
            }
        return allergies;
    }
    async getAlrgnPotentialInactive(hicSeqnList) {
        const res = {};
        const sql = `SELECT 
            hic_seqn,
            hic_desc,
            hic_potentially_inactv_ind 
        FROM form_list_fdb_hic_desc 
        WHERE
            hic_seqn IN (${Array(hicSeqnList.length).fill("%s").join(",")})`;

        const potentialInactive = await this.db.env.ro.query(sql, hicSeqnList);
        for (const inactive of potentialInactive) {
            if (inactive.hic_potentially_inactv_ind == 1) {
                res[inactive.hic_seqn] = {
                    type: "Potential inactive ingrediant allergy",
                    desc: "Manual review needed",
                    hdesc: inactive.hic_desc,
                };
            }
        }
        return res;
    }
    async getInactiveReviewed(ndc) {
        const par = [ndc];
        const sql = `
        SELECT
            id
        FROM form_list_fdb_inactv_reviewed
        WHERE
            ndc = %L`;
        return await this.db.env.ro.query(sql, par);
    }
    async getInactiveLinkedIng(ndc) {
        const par = [ndc];
        const sql = `
        SELECT
            dsc.hic_seqn,
            dsc.hic_desc
        FROM form_list_fdb_ndc_inactv_link lnk
            JOIN form_list_fdb_hic_desc dsc ON dsc.hic_seqn = lnk.hic_seqn
        WHERE
            ndc = %L;
        `;
        return await this.db.env.ro.query(sql, par);
    }
};
