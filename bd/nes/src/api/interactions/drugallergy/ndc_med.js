"use strict";

module.exports = class DrugAllergyNdcMedClass {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
    }

    async getInteractions(ndc, dam_concept_id, desc) {
        if (!ndc) throw new Error("NDC cannot be empty");
        if (!dam_concept_id) throw new Error("dam_concept_id cannot be empty");
        const [allergies, hicSeqnList] = await this.getNDCIngredients(ndc);
        if (Object.keys(hicSeqnList).length < 1) {
            throw new Error("No hic seqn found for ndc: " + ndc);
        }
        const intAllergies = await this.runInteractions(
            dam_concept_id,
            hicSeqnList
        );
        for (const [hic, info] of Object.entries(intAllergies)) {
            if (hic in allergies) {
                allergies[hic].desc = allergies[hic].desc + "," + info.desc;
                allergies[hic].type = allergies[hic].type + "," + info.type;
            }
        }
        const allAllergies = { ...intAllergies, ...allergies };
        for (const alrgn of Object.keys(allAllergies))
            allAllergies[alrgn]["alrgn"] = desc;
        return allAllergies;
    }
    async getNDCIngredients(ndc) {
        const par = [ndc];
        const hicSeqnList = {};
        const allergies = {};

        // step 1-4 and 6
        const sql = `
            SELECT
                alnk.dam_alrgn_hic_seqn,
                rhhl.related_hic_seqn,
                dsc.hic_desc,
                rhdsc.hic_potentially_inactv_ind,
                rhdsc.hic_desc AS related_hic_desc
            FROM
                form_list_fdb_ndc fndc
                JOIN form_list_fdb_gcnseqno_mstr mstr ON mstr.gcn_seqno = fndc.gcn_seqno
                JOIN form_list_fdb_hic_hicl_alrg_link alnk ON alnk.hicl_seqno = mstr.hicl_seqno
                JOIN form_list_fdb_hic_desc dsc ON dsc.hic_seqn = alnk.dam_alrgn_hic_seqn
                JOIN form_list_fdb_hic_hic_link rhhl ON rhhl.hic_seqn = alnk.dam_alrgn_hic_seqn
                JOIN form_list_fdb_hic_desc rhdsc ON rhdsc.hic_seqn = rhhl.hic_seqn
            WHERE
                fndc.ndc = %L`;
        const activeHicSeqn = await this.db.env.ro.query(sql, par);
        if (activeHicSeqn.length < 1) {
            throw new Error("No hic seqn found for ndc: " + ndc);
        }
        activeHicSeqn.map((h) => {
            hicSeqnList[h.related_hic_seqn] = {
                hic_seqn: h.related_hic_seqn,
                hic_desc: h.related_hic_desc,
                inactive: h.hic_potentially_inactv_ind,
            };
            if (!(h.dam_alrgn_hic_seqn in hicSeqnList))
                hicSeqnList[h.dam_alrgn_hic_seqn] = {
                    hic_seqn: h.dam_alrgn_hic_seqn,
                    hic_desc: h.hic_desc,
                    inactive: h.hic_potentially_inactv_ind,
                };
        });

        // step 5
        const iaSQL = `SELECT ndc FROM form_list_fdb_inactv_reviewed WHERE ndc = %L`;
        const hasInactive = await this.db.env.ro.query(iaSQL, par);
        if (hasInactive.length < 1) {
            // step 7
            for (const hic of Object.values(hicSeqnList)) {
                if (hic.inactive == 1)
                    allergies[hic.hic_seqn] = {
                        desc: "Potential inactive ingredients",
                        type: "Check Drug for the presence of " + hic.hic_desc,
                    };
            }
        }
        // step 8
        const iaHICS = await this.getInactiveLinkedIng(ndc);
        iaHICS.map((h) => {
            if (!(h.hic_seqn in hicSeqnList))
                hicSeqnList[h.hic_seqn] = {
                    ndc: ndc,
                    inactive: true,
                    hic_seqn: h.hic_seqn,
                    hic_desc: h.hic_desc,
                };
        });
        return [allergies, hicSeqnList];
    }

    async getInactiveLinkedIng(ndc) {
        const par = [ndc];
        const sql = ` SELECT dsc.hic_seqn, dsc.hic_desc
            FROM
                form_list_fdb_ndc_inactv_link lnk
                JOIN form_list_fdb_hic_desc dsc ON dsc.hic_seqn = lnk.hic_seqn
            WHERE
                ndc = %L;
        `;
        return await this.db.env.ro.query(sql, par);
    }
    async runInteractions(med_concept_id, hicSeqnList) {
        let par = [med_concept_id];
        let medHicSeqn = [];
        const allergies = {};
        // step 1,2
        const medSQL = `
                SELECT
                    distinct alnk.dam_alrgn_hic_seqn as hic_seqn, dsc.hic_desc
                FROM
                    form_list_fdb_alrgn_mstr mstr
                    JOIN form_list_fdb_med_hiclseqno_link ml ON (
                        ml.med_concept_id = mstr.dam_concept_id
                        AND ml.med_concept_id_typ = 1
                    )
                    JOIN form_list_fdb_hic_hicl_alrg_link alnk ON alnk.hicl_seqno = ml.hicl_seqno
                    JOIN form_list_fdb_hic_desc dsc ON dsc.hic_seqn = alnk.dam_alrgn_hic_seqn
                WHERE
                    ml.med_concept_id = %s;
            `;
        medHicSeqn = await this.db.env.ro.query(medSQL, par);
        if (medHicSeqn.length < 1) {
            throw new Error(
                "No hic seqn found for med_concept_id: " + med_concept_id
            );
        }
        Object.values(hicSeqnList).map((relatedHIC) => {
            medHicSeqn.map((med) => {
                if (med.hic_seqn == relatedHIC.hic_seqn)
                    allergies[med.hic_seqn] = {
                        desc: relatedHIC.hic_desc,
                        type: "Med alrgn specific allergy",
                    };
            });
        });
        par = medHicSeqn.map((h) => h.hic_seqn);
        const sql = `
                SELECT
                    ahl.hic_seqn,
                    dsc.hic_desc
                FROM
                    form_list_fdb_hic_alrgn_grp_link hal
                    JOIN form_list_fdb_alrgn_grp_desc ad ON ad.dam_alrgn_grp = hal.dam_alrgn_grp
                    JOIN form_list_fdb_hic_alrgn_grp_link ahl ON ahl.dam_alrgn_grp = hal.dam_alrgn_grp
                    JOIN form_list_fdb_hic_desc dsc ON dsc.hic_seqn = ahl.hic_seqn
                WHERE
                    hal.hic_seqn IN (${Array(medHicSeqn.length).fill("%s").join(",")})
                    AND ad.dam_alrgn_grp_status_cd = 0;
                `;
        const alIngHics = await this.db.env.ro.query(sql, par);
        for (const alHIC of alIngHics) {
            Object.values(hicSeqnList)
                .filter((hic) => hic.hic_seqn == alHIC.hic_seqn)
                .map((al) => {
                    allergies[al.hic_seqn] = {
                        desc: al.hic_desc,
                        type: "Med alrgn specific allergy",
                    };
                });
        }
        return allergies;
    }
};
