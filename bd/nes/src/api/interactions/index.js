"use strict";

const NDCMedAllergyClass = require("./drugallergy/ndc_med");
const NDCAlrgnAllergyClass = require("./drugallergy/ndc_alrgngrp");
const NDCIngrAllergyClass = require("./drugallergy/ndc_ingredient");
const DrugDrugInteractionClass = require("./drugdrug/index");
const _ = require("lodash");

// COMMENTS: This module can be severely optimised but its kept straightforward with a performance hit
// to encourage readability, direct information flow and easier debugging
module.exports = class DrugAllergyModule {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.notify = nes.shared.notify;
        this.med = new NDCMedAllergyClass(nes);
        this.alrgn = new NDCAlrgnAllergyClass(nes);
        this.ingredient = new NDCIngrAllergyClass(nes);
        this.dd = new DrugDrugInteractionClass(nes);
        this.nes = nes;
    }

    async getInteraction(pid) {
        const medsObject = await this.getNDCMeds(pid);
        const da_interaction = await this.getDrugAllergyInteraction(
            pid,
            medsObject
        );
        const dd_interaction = await this.getDrugDrugInteraction(medsObject);
        const has_da = (da_interaction?.length > 0 && "Yes") || "No";
        const has_dd = (dd_interaction?.length > 0 && "Yes") || "No";
        return {
            da_interaction,
            dd_interaction,
            has_da,
            has_dd,
        };
    }

    async getDrugDrugInteraction(medsObject) {
        const ndcs = Object.keys(medsObject);
        if (ndcs.length < 1) return [];
        const res = await this.dd.getInteractions(ndcs, medsObject);
        return res;
    }
    async getDrugAllergyInteraction(pid, medsObject) {
        const finalInteractions = [];
        const allergies = {};
        const allergens = await this.getAllergies(pid);
        if (allergens.length < 1) return finalInteractions;
        const ndcs = Object.keys(medsObject);
        for (const ndc of ndcs) {
            allergies[ndc] = {};
            let alrgy = {};
            for (const allergen of allergens) {
                try {
                    switch (allergen.type) {
                        case 1:
                            alrgy = await this.alrgn.getInteractions(
                                ndc,
                                allergen.dam_concept_id,
                                allergen.desc
                            );
                            allergies[ndc][allergen.dam_concept_id] = alrgy;
                            break;
                        case 2:
                            alrgy = await this.med.getInteractions(
                                ndc,
                                allergen.dam_concept_id,
                                allergen.desc
                            );
                            allergies[ndc][allergen.dam_concept_id] = alrgy;
                            break;
                        case 6:
                            alrgy = await this.ingredient.getInteractions(
                                ndc,
                                allergen.dam_concept_id,
                                allergen.desc
                            );
                            allergies[ndc][allergen.dam_concept_id] = alrgy;
                            break;
                        default:
                    }
                } catch (error) {
                    console.warn(error.message);
                }
            }
        }
        for (const [ndc, obj] of Object.entries(allergies)) {
            for (const interaction of Object.values(obj)) {
                if (_.isEmpty(interaction)) continue;
                for (const info of Object.values(interaction))
                    finalInteractions.push({
                        ndc: medsObject[ndc],
                        type: info.type,
                        description: info.desc,
                        alrgn: info.alrgn,
                    });
            }
        }
        return finalInteractions;
    }

    async getNDCMeds(pid) {
        const par = [pid, pid];
        const sql = `
            SELECT
                fn.ndc,COALESCE(fn.bn,fn.gni) as name
            FROM form_patient_medication fpm
            INNER JOIN form_list_fdb_ndc fn ON fn.code = fpm.fdb_id
            WHERE fpm.patient_id = %s AND (fpm.archived IS NULL OR fpm.archived = False) AND fpm.status = 'Active'
            UNION 
            SELECT
            	fn.ndc,COALESCE(fn.bn,fn.gni) as name
            FROM form_careplan_order_rx rx
            INNER JOIN form_inventory inv ON inv.id = rx.inventory_id
            INNER JOIN form_list_fdb_ndc fn ON fn.code = inv.fdb_id
            WHERE rx.status_id IN ('1', '5') AND rx.patient_id = %s AND rx.deleted IS NOT TRUE AND rx.archived IS NOT TRUE `;
        const res = await this.db.env.ro.query(sql, par);
        const map = {};
        for (const element of res) map[element.ndc] = element.name;
        return map;
    }

    async getAllergies(pid) {
        const par = [pid];
        const sql = `
            SELECT
                mstr.dam_concept_id,
                mstr.dam_concept_id_typ as type,
                mstr.dam_concept_id_desc as desc
            FROM
                form_patient_allergy pa
                JOIN form_list_fdb_alrgn_mstr mstr ON mstr.code = pa.allergen_id
            WHERE
                pa.patient_id = %s;
        `;
        const allergens = await this.db.env.ro.query(sql, par);
        return allergens;
    }

    // based on type call the step that needs to be called
    async process(ctx) {
        try {
            const { pid } = ctx.query;
            if (!pid) throw new Error("Patient ID: pid cannot be empty");
            const result = await this.getInteraction(pid);
            ctx.status = 200;
            ctx.body = result;
        } catch (e) {
            ctx.status = 500;
            ctx.body = "Interal Server Error";
            console.error(e);
        }
    }
};
