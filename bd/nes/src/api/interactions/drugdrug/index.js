"use strict";

const _ = require("lodash");
module.exports = class DrugAllergyNdcMedClass {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
    }
    async getInteractions(ndcList, medsObject) {
        if (!ndcList || ndcList.length < 1)
            throw new Error("NDC cannot be empty");
        let par = [];
        const slMap = {
            1: "Contraindicated",
            2: "Severe Interaction",
            3: "Moderate Interaction",
            9: "Undetermined Severity",
        };
        const ndcQueries = ndcList
            .map((ndc) => {
                par = [...par, ndc, ndc, ndc];
                return `
            %L, (
                SELECT json_agg(
                    json_build_object(
                        'ddi_codex', subquery.ddi_codex,
                        'ddi_monox', subquery.ddi_monox,
                        'ddi_sl', subquery.ddi_sl,
                        'ddi_des', subquery.ddi_des,
                        'type', subquery.type
                    )
                )
                FROM (
                    SELECT gl.ddi_codex, fmst.ddi_monox, fmst.ddi_sl, fmst.ddi_des, 'active' AS type
                    FROM form_list_fdb_ndc fn
                    JOIN form_list_fdb_gcnseqno_link gl ON gl.gcn_seqno = fn.gcn_seqno
                    JOIN form_list_fdb_mstr fmst ON fmst.ddi_codex = gl.ddi_codex
                    WHERE fn.ndc = %L

                    UNION ALL

                    SELECT il.ddi_codex, fmst.ddi_monox, fmst.ddi_sl, fmst.ddi_des, 'inactive' AS type
                    FROM form_list_fdb_ndc_inactv_ddim_link il
                    JOIN form_list_fdb_mstr fmst ON fmst.ddi_codex = il.ddi_codex
                    WHERE il.ddi_ndc = %L
                ) subquery
            )
          `;
            })
            .join(",");
        const finalQuery = `SELECT json_build_object( ${ndcQueries} ) AS result`;
        const res = _.head(await this.db.env.ro.query(finalQuery, par));
        const ndcs = Object.keys(res.result);
        const interactions = [];
        for (let i = 0; i < ndcs.length; i++) {
            for (let j = i + 1; j < ndcs.length; j++) {
                if (j >= ndcs.length) break;
                const ndc = ndcs[i];
                const ndcNext = ndcs[j];
                const codexMap = res.result[ndc];
                const codexMapNext = res.result[ndcNext];
                if (!codexMap || !codexMapNext) continue;
                codexMap.map((codes) => {
                    codexMapNext.map((codesNext) => {
                        if (
                            codes.ddi_monox === codesNext.ddi_monox &&
                            codes.ddi_codex !== codesNext.ddi_codex
                        ) {
                            let sltxt = "";
                            let sl = 9;
                            const sl1 = parseInt(codes.ddi_sl);
                            if (sl1 && !isNaN(sl1)) {
                                sl = codes.ddi_sl;
                            }
                            const sl2 = parseInt(codesNext.ddi_sl);
                            if (sl2 && !isNaN(sl2)) {
                                if (isNaN(sl1) || sl2 > sl1) {
                                    sl = codesNext.ddi_sl;
                                }
                            }
                            sltxt = slMap[sl];
                            interactions.push({
                                ndc_1: medsObject[ndc],
                                ndc_2: medsObject[ndcNext],
                                description: codesNext.ddi_des,
                                sl: sltxt,
                            });
                        }
                    });
                });
            }
        }
        return interactions;
    }
};
