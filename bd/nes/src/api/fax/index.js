/**
 * Fax API module
 * Handles API endpoints for fax operations by using the shared fax service
 */
module.exports = class FaxApi {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }
    async normalizePhoneNumber(phoneNumber) {
        if (!phoneNumber) return phoneNumber;
        const cleaned = phoneNumber.replace(/\D/g, "");
        return cleaned.startsWith("1") ? cleaned : `1${cleaned}`;
    }
    async sendFax(ctx) {
        const body = ctx.request.body;

        // Validate required fields
        if (!body.faxNumber && !body.recipients) {
            throw new Error("Either faxNumber or recipients array is required");
        }

        if (!body.callerId) {
            throw new Error("callerId is required");
        }

        // Validate that we have some form of file data
        if (!body.faxData && !body.fileBuffer && !body.s3Key && !body.files) {
            throw new Error(
                "File data is required (faxData, fileBuffer, s3Key, or files)"
            );
        }

        // Now handle various ways a buffer might be sent

        // Case 1: fileBuffer is a Buffer-like object with type and data fields
        // This is how a Buffer serializes in JSON: { type: 'Buffer', data: [byte array] }
        if (
            body.fileBuffer &&
            typeof body.fileBuffer === "object" &&
            body.fileBuffer.type === "Buffer" &&
            Array.isArray(body.fileBuffer.data)
        ) {
            body.fileBuffer = Buffer.from(body.fileBuffer.data);
            console.log(
                "Converted serialized Buffer object to Buffer instance for fileBuffer"
            );
        }
        // Case 2: fileBuffer is an array of numbers (raw bytes)
        else if (body.fileBuffer && Array.isArray(body.fileBuffer)) {
            body.fileBuffer = Buffer.from(body.fileBuffer);
            console.log("Converted array to Buffer instance for fileBuffer");
        }
        // Case 3: fileBuffer is a base64 string (rare but possible)
        else if (body.fileBuffer && typeof body.fileBuffer === "string") {
            body.fileBuffer = Buffer.from(body.fileBuffer, "base64");
            console.log(
                "Converted base64 string to Buffer instance for fileBuffer"
            );
        }

        // Verify fileBuffer is now a proper Buffer instance
        if (body.fileBuffer && !(body.fileBuffer instanceof Buffer)) {
            console.error(
                "Invalid fileBuffer type:",
                typeof body.fileBuffer,
                body.fileBuffer
            );
            throw new Error("fileBuffer must be a valid binary buffer");
        }

        // Similarly handle files array
        if (body.files && Array.isArray(body.files)) {
            body.files = body.files.map((file, index) => {
                if (!file.buffer) return file;

                // Case 1: Buffer-like object with type and data
                if (
                    typeof file.buffer === "object" &&
                    file.buffer.type === "Buffer" &&
                    Array.isArray(file.buffer.data)
                ) {
                    console.log(
                        `Converted serialized Buffer object to Buffer instance for file ${index}`
                    );
                    return {
                        ...file,
                        buffer: Buffer.from(file.buffer.data),
                    };
                }
                // Case 2: Array of numbers
                else if (Array.isArray(file.buffer)) {
                    console.log(
                        `Converted array to Buffer instance for file ${index}`
                    );
                    return {
                        ...file,
                        buffer: Buffer.from(file.buffer),
                    };
                }
                // Case 3: Base64 string
                else if (typeof file.buffer === "string") {
                    console.log(
                        `Converted base64 string to Buffer instance for file ${index}`
                    );
                    return {
                        ...file,
                        buffer: Buffer.from(file.buffer, "base64"),
                    };
                }
                return file;
            });
        }

        const result = await this.shared.fax.send({
            faxNumber: body.faxNumber,
            callerId: body.callerId,
            faxData: body.faxData,
            fileBuffer: body.fileBuffer,
            s3Key: body.s3Key,
            files: body.files,
            recipients: body.recipients,
            subject: body.subject,
            from_name: body.from_name,
            to_name: body.to_name,
            message: body.message,
            send_at: body.send_at,
            patientMrn: body.patientMrn,
            fileName: body.fileName,
        });
        if (result.status !== 1) {
            console.error("Error sending fax:", result);
            throw new Error(result.message || "Failed to send fax");
        }
        return result;
    }

    async getFaxStatus(ctx) {
        let jobId = ctx.query?.jobId;
        if (!jobId) {
            // check body for jobId
            const body = ctx.request.body;
            if (body.jobId) {
                jobId = body.jobId;
            } else {
                ctx.status = 400;
                return { message: "Job ID is required" };
            }
        }
        return await this.shared.fax.getStatus(jobId);
    }

    /**
     * Resend a failed fax
     * @param {Object} ctx - Koa context
     * @returns {Promise<Object>} - API response
     */
    async resendFax(ctx) {
        const { jobId } = ctx.request.body;

        if (!jobId) {
            throw new Error("Job ID is required");
        }

        const result = await this.shared.fax.resend(jobId);
        return result;
    }

    async retrieveFaxes(ctx, direction) {
        try {
            // Get the query parameters
            const params = {
                ...ctx.query, // Include all query parameters (faxNumber, fromDate, etc.)
                direction, // Add the direction (inbound/outbound/all)
            };

            // Get the fax service instance
            const faxService = this.shared.fax;

            // Pass all parameters to the service
            const result = await faxService.retrieveFaxes(params);

            return {
                status: 1,
                message: "Success",
                ...result,
            };
        } catch (error) {
            console.error("Error retrieving faxes:", error);
            throw new Error("Failed to retrieve faxes");
        }
    }

    async cancelFax(ctx) {
        const { jobId } = ctx.query;
        if (!jobId) {
            return {
                status: 0,
                message: "Job ID is required",
            };
        }
        return await this.shared.fax.cancelFax(jobId);
    }

    async handleCallback(ctx) {
        const body = ctx.request.body;
        if (body?.jobId && body?.direction) {
            const direction =
                body.direction === "sent" ? "outbound" : "inbound";
            return await this.shared.fax.webhook_callback(body, direction);
        } else {
            return {
                status: 0,
                message: "Invalid direction",
            };
        }
    }

    async process(ctx, options) {
        console.log("FaxApi processing request:", options.url.pathname);

        try {
            const path = options.url.pathname;

            if (path.endsWith("/send")) {
                ctx.body = await this.sendFax(ctx);
            } else if (path.endsWith("/fax/status")) {
                ctx.body = await this.getFaxStatus(ctx);
            } else if (path.endsWith("/resend")) {
                ctx.body = await this.resendFax(ctx);
            } else if (path.endsWith("/fetch/inbound")) {
                ctx.body = await this.retrieveFaxes(ctx, "inbound");
            } else if (path.endsWith("/fetch/outbound")) {
                ctx.body = await this.retrieveFaxes(ctx, "outbound");
            } else if (path.endsWith("/fetch/all")) {
                ctx.body = await this.retrieveFaxes(ctx, "all");
            } else if (path.endsWith("/cancel")) {
                ctx.body = await this.cancelFax(ctx);
            } else if (path.endsWith("/wh/status")) {
                ctx.body = await this.handleCallback(ctx);
            } else {
                ctx.status = 404;
                ctx.body = {
                    status: 0,
                    message: `Endpoint not found: ${path}`,
                };
            }
        } catch (error) {
            ctx.status = 500;
            ctx.body = {
                message: error.message,
                code: error.code || 500,
            };
            console.error("Fax API error:", error);
        }
    }
};
