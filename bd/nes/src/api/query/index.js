"use strict";
const _ = require("lodash");
const FormListGetClass = require("../form/helpers/list-get");

module.exports = class ApiView extends FormListGetClass {
    constructor(nes) {
        super(nes);
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.RESERVED_WORDS = [
            "limit",
            "page_number",
            "filter",
            "sort",
            "fields",
            "code",
            "keywords",
            "refresh",
        ];
    }

    async getQueryBase(ctx, urlpath, opts = {}) {
        const user = ctx.user;
        const params = Object.assign({}, ctx.query);
        const parts = ctx.url.split("?");
        const queryPart = parts[0];
        const pathParts = queryPart.split("/");
        const code = pathParts[pathParts.indexOf("query") + 1];
        const err = this.check_parameters(user, params);
        if (err) {
            return err;
        }
        return await this.makeQuery(ctx, user, code, params);
    }

    async makeQuery(ctx, user, code, params) {
        const keys = Object.keys(params);
        const paramValues = this.fx.getPGParamValues(params);
        const formdata = _.head(
            await this.form.get.get_form(ctx, user, "query", {
                limit: 1,
                filter: "code:" + code,
            })
        );
        if (!formdata) {
            return { error: "Invaid query code" };
        }
        let sql = formdata.report_sql;
        if (!sql || !sql.trim()) {
            return { error: "Invaid Query" };
        }
        params.keywords_query = params.keywords || "";
        const qf = this.fx.buildQueryFilters(
            ctx,
            params,
            formdata.dsl_struct,
            this.shared.config.limits.max_rows
        );
        if (qf.error) {
            return qf;
        }
        sql = `
            WITH q AS (
                ${sql.trim().replace(/;$/, "")}
            ) SELECT * FROM q ${qf.filters}
        `;
        if (formdata.create_view && formdata.create_view === "Yes") {
            if (keys.includes("refresh") && params.refresh == "true") {
                const refreshSql = `REFRESH MATERIALIZED VIEW view_${formdata.code}`;
                await this.db.env.rw.query(refreshSql);
            }
            sql = `SELECT * FROM view_${formdata.code} ${qf.filters}`;
        }
        return {
            sql: this.fx.sanitizeReadQuery(sql),
            par: paramValues,
            dsl: formdata.dsl_struct,
        };
    }

    check_parameters(user) {
        if (!(user.id && !isNaN(parseInt(user.id))))
            return { msg: "Invalid User Id", code: 400 };
        return false;
    }

    async run_query(ctx, user, code, params) {
        const base = await this.makeQuery(ctx, user, code, params);
        const { sql, par } = base;
        if (base.error) {
            return { error: base.error };
        }
        if (!sql) {
            return { error: "Unknown error" };
        }
        return await this.db.env.rw.query(this.fx.sanitizeReadQuery(sql), par);
    }

    async run(ctx, urlpath) {
        const base = await this.getQueryBase(ctx, urlpath);
        const { sql, par } = base;
        if (base.error) {
            return { error: base.error };
        }
        if (!sql) {
            return { error: "Unknown error" };
        }
        if (ctx.request.method === "POST") {
            return await this.getData(ctx, urlpath);
        } else {
            return await this.db.env.rw.query(
                this.fx.sanitizeReadQuery(sql),
                par
            );
        }
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const params = Object.assign({}, ctx.query);
            const err = this.check_parameters(user, params);
            if (err) {
                ctx.status = err.code;
                ctx.body = { error: err.msg };
                return;
            }
            let qr = null;
            if (ctx.request.method === "POST" && params.legacy === "true") {
                const opts = ctx.request.body;
                qr = await this.run_query(ctx, user, opts.code, opts.params);
            } else {
                qr = await this.run(ctx, urlpath);
            }
            if (qr.error) {
                ctx.status = 400;
                ctx.body = qr;
                return;
            }
            ctx.body = qr;
            ctx.status = 200;
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
