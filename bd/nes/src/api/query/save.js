"use strict";
const _ = require("lodash");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.pgboss = nes.modules.pgboss;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.RESERVED_WORDS = [
            "limit",
            "page_number",
            "filter",
            "sort",
            "fields",
            "code",
            "keywords",
        ];
    }

    worker_view_refresh_query = async (ctx, job) => {
        try {
            await this.db.env.rw.query(
                "REFRESH MATERIALIZED VIEW " + job.data.view
            );
            console.log(
                "Refreshed View:" +
                    job.data.view +
                    " process id: " +
                    process.pid
            );
        } catch (error) {
            console.log(error);
            console.log("Error Refreshing view:", job.data.view);
        }
        return job;
    };

    async check_parameters(user, data) {
        data.refresh = parseInt(data.refresh);
        if (!(user.id && !isNaN(parseInt(user.id))))
            return { msg: "Invalid User Id", code: 400 };
        if (!(data && data.code))
            return { msg: "Missing Data|Code", code: 400 };
        if (!data.report_sql) return { msg: "Missing report_sql", code: 400 };
        if (!data.refresh || isNaN(data.refresh))
            return { msg: "Invalid referesh param", code: 400 };
        const err = await this.validate_query(data);
        if (err) return err;
        return false;
    }

    async validate_query(data) {
        const err = this.check_illegal(data);
        if (err) return err;
        try {
            await this.db.env.rw.pool.multi(data.report_sql);
        } catch (error) {
            return { msg: "Error running Query", code: 400, error: error };
        }
        return false;
    }

    // TODO on archive drop materialized view
    async create_view(ctx, user, data, id) {
        if (id) id = parseInt(id);
        const t = this.db.env.rw.transaction(ctx);
        const code = data.code;
        const view = "view_" + code;
        const viewSql = `CREATE MATERIALIZED VIEW IF NOT EXISTS ${view} AS ${data.report_sql}`;
        const dropSql = `DROP MATERIALIZED VIEW IF EXISTS ${view}`;
        try {
            if (id) await this.db.env.rw.query(dropSql);
            await this.db.env.rw.query(viewSql);
            if (!id) {
                t.insert("query", data);
                const res = await t.commit();
                data.id = res[0].id;
            } else {
                await t.update("query", data, id);
                await t.commit();
            }
            this.pgboss.jobSchedule(
                "view_refresh",
                "query",
                { view },
                data.refresh
            );
            return data;
        } catch (error) {
            console.log(error);
            throw Error({ msg: error, code: 400 });
        }
    }

    check_illegal(data) {
        const forbiddenOperations =
            /(?<!["])\b(?!SELECT\b)(UPDATE|DELETE|INSERT|TRUNCATE|DROP)\b(?!["])/gi;
        const message = [];
        if (forbiddenOperations.test(data.report_sql))
            message.push("Query contains one of the UPDATE|DELETE|INSERT");
        const placeHolders = /%([%sILQS])/g;
        if (placeHolders.test(data.report_sql) && data.create_view !== "Yes")
            message.push("Query contains one of the %L,%s");
        if (message.length > 0) return { msg: message.join("\n"), code: 400 };
        return false;
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const id = urlpath.path[3];
            const data = ctx.request.body;
            const err = await this.check_parameters(user, data);
            if (err) {
                ctx.status = err.code;
                ctx.body = { error: err.msg };
                return;
            }
            const qr = await this.create_view(ctx, user, data, id);
            if (qr.error) {
                ctx.status = 400;
                ctx.body = qr;
                return;
            }
            ctx.body = qr;
            ctx.status = 200;
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
