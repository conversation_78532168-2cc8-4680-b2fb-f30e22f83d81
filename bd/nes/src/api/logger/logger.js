"use strict";
const UnauthorizedException = {
    status: 403,
};

const _ = require("lodash");
const valid_levels = ["error", "warn", "info", "verbose", "debug"];
const { set_log_level } = require("@core/logger");

module.exports = class ApiTest {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.crud = nes.shared.crud;
        this.utils = nes.shared.utils;
        this.transforms = nes.shared.transforms;
    }

    async process(ctx) {
        try {
            if (!this.auth.can_access_form(ctx, "company")) {
                throw UnauthorizedException;
            }
            if (ctx.request.method == "POST") {
                const params = Object.assign({}, ctx.query);
                const func = params.func;
                if (func == "set-log-level") {
                    if (!valid_levels.includes(params.level)) {
                        throw {
                            status: 500,
                            body: { error: "Invalid log level" },
                        };
                    }
                    set_log_level(params.level);
                    ctx.status = 200;
                    ctx.body = {
                        message:
                            "Successfully reset log level to " + params.level,
                    };
                } else {
                    throw { status: 500, body: { error: "Invalid function" } };
                }
            }
        } catch (e) {
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
                console.error(e);
            } else {
                ctx.status = 500;
                ctx.body = e;
                console.error(e);
            }
        }
    }
};
