"use strict";
const { RRule } = require("rrule");
const UnauthorizedException = {
    status: 403,
};
module.exports = class ApiView {
    constructor(nes) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    get_rrule_field_obj() {
        return {
            dtstart: "effective_start_date",
            until: "effective_end_date",
            freq: "repeat_period",
            interval_daily: "repeat_daily",
            interval_weekly: "repeat_weekly",
            interval_monthly: "repeat_monthly",
            byweekday: "repeat_on",
            monthweekday: "repeat_by",
            event_start_time: "effective_start_time",
            event_end_time: "effective_end_time",
        };
    }

    update_single_event(data) {
        const fields = ["start_date", "start_time", "end_date", "end_time"];
        for (const field of fields) {
            data["override_" + field]
                ? (data["effective_" + field] = data["override_" + field])
                : null;
        }
        data["dirty"] = "Yes";
        return data;
    }
    async upsert_events(ctx, data, id = null) {
        const transaction = this.db.env.rw.transaction(ctx);
        if (!data.event_series_id) {
            // schedule_event operations create/update
            if (data.series === "No") {
                if (id) {
                    data = this.update_single_event(data);
                    if (data.id) delete data["id"];
                    await transaction.update("schedule_event", data, id);
                } else {
                    await transaction.insert("schedule_event", data);
                }
            } else if (data.series === "Yes") {
                // schedule_event -> schedule_event_series AND new Series Create
                const [eventsArray, ical] = this.get_events_array(data);
                data["ical"] = ical;
                const seriesId = await transaction.insert(
                    "schedule_event_series",
                    data
                );
                for (const dt of eventsArray.all()) {
                    if (!dt) {
                        throw new Error("Invalid date in events array");
                    }

                    const eventObject = {
                        event_start_date: dt.toLocaleDateString(),
                        event_start_time: dt.toLocaleTimeString(),
                        event_end_time: data.event_end_time,
                        effective_start_date: dt.toLocaleDateString(),
                        effective_start_time: dt.toLocaleTimeString(),
                        auto_name:
                            dt.toLocaleTimeString() +
                            " " +
                            dt.toLocaleDateString(),
                        event_series_id: seriesId,
                        series: "No",
                        patient_id: data.patient_id,
                        user_id: data.user_id,
                        user_id_auto_name: data.user_id_auto_name,
                        calendar_id: data.calendar_id,
                        therapy_1: data.therapy_1,
                        appointment_status: data.appointment_status,
                        notes: data.notes,
                        portal_display: data.portal_display,
                    };

                    await transaction.insert("schedule_event", eventObject);
                }
                if (id) {
                    if (data.id) delete data["id"];
                    await transaction.update(
                        "schedule_event",
                        { ...data, deleted: true },
                        id
                    );
                }
            }
        } else {
            if (data.series === "Yes") {
                const date = this.fx.greaterDate(data.effective_start_date);
                const oldEvents = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "schedule_event",
                    {
                        filter: [
                            `event_series_id:${data.event_series_id}`,
                            `deleted:false`,
                            `effective_start_date:${date}..`,
                        ],
                        sort: "effective_start_date",
                    }
                );
                // use the new and update the previous
                const [eventsArray, ical] = this.get_events_array(data);
                const newDates = eventsArray.between(
                    new Date(date),
                    new Date(data.event_end_date),
                    true
                );
                if (newDates.length > 0) {
                    const updatePromises = [];
                    for (const newDate of newDates) {
                        let oldEvent = oldEvents.shift();
                        if (oldEvent) {
                            const oldId = oldEvent["id"];
                            oldEvent = {
                                ...data,
                                effective_start_date:
                                    newDate.toLocaleDateString(),
                                effective_start_time:
                                    newDate.toLocaleTimeString(),
                                event_start_date: newDate.toLocaleDateString(),
                                event_start_time: newDate.toLocaleTimeString(),
                                series: "No",
                            };
                            delete oldEvent["id"];
                            updatePromises.push(
                                transaction.update(
                                    "schedule_event",
                                    oldEvent,
                                    oldId
                                )
                            );
                        } else if (!oldEvent) {
                            const eventObject = {
                                event_start_date: newDate.toLocaleDateString(),
                                event_start_time: newDate.toLocaleTimeString(),
                                event_end_time: data.event_end_time,
                                effective_start_date:
                                    newDate.toLocaleDateString(),
                                effective_start_time:
                                    newDate.toLocaleTimeString(),
                                auto_name:
                                    newDate.toLocaleTimeString() +
                                    " " +
                                    newDate.toLocaleDateString(),
                                event_series_id: data.event_series_id,
                                series: "No",
                                patient_id: data.patient_id,
                                user_id: data.user_id,
                                user_id_auto_name: data.user_id_auto_name,
                                calendar_id: data.calendar_id,
                                therapy_1: data.therapy_1,
                                appointment_status: data.appointment_status,
                                notes: data.notes,
                                portal_display: data.portal_display,
                            };
                            await transaction.insert(
                                "schedule_event",
                                eventObject
                            );
                        }
                    }
                    if (updatePromises.length > 0)
                        await Promise.all(updatePromises);
                }
                const deletePromises = [];
                for (const oldEvent of oldEvents)
                    deletePromises.push(
                        transaction.update(
                            "schedule_event",
                            { ...oldEvent, deleted: true },
                            oldEvent.id
                        )
                    );
                if (deletePromises.length > 0)
                    await Promise.all(deletePromises);
                data["ical"] = ical;
                if (data.id) delete data["id"];
                await transaction.update(
                    "schedule_event_series",
                    data,
                    data.event_series_id
                );
            } else if (
                !data.series ||
                data.series === "" ||
                data.series === "No"
            ) {
                data = this.update_single_event(data);
                if (data.id) delete data["id"];
                await transaction.update("schedule_event", data, id);
            }
        }
        const res = await transaction.commit();
        if (res.error) throw new Error(res.message);
        res[0] && res[0].id ? (data["id"] = res[0].id) : null;
        return data;
    }

    generate_rrule_strings(val) {
        const frequencyMap = {
            Yearly: RRule.YEARLY,
            Monthly: RRule.MONTHLY,
            Weekly: RRule.WEEKLY,
            Daily: RRule.DAILY,
            Hourly: RRule.HOURLY,
            Minutely: RRule.MINUTELY,
            Secondly: RRule.SECONDLY,
        };

        const options = {
            freq: frequencyMap[val.freq],
            dtstart: new Date(val.dtstart),
            until: new Date(val.until),
            interval: val.interval,
        };

        if (val.byweekday) {
            options.byweekday =
                RRule[val.byweekday.substring(0, 2).toUpperCase()];
        }

        if (val.monthweekday) {
            options.bymonthday = val.monthweekday;
        }
        const rule = new RRule(options);
        return {
            rruleString: rule.toString(),
            text: rule.toText(),
        };
    }

    get_events_array(data) {
        const vals = {};
        for (const [k, v] of Object.entries(this.get_rrule_field_obj())) {
            vals[k] = data[v];
        }
        vals.interval =
            vals["interval_" + (vals.freq ? vals.freq.toLowerCase() : "")];
        for (const [k, _] of Object.entries(vals)) {
            if (k.substr(0, 9) === "interval_") {
                delete vals[k];
            }
        }
        const outputRRule = this.generate_rrule_strings(vals);
        return [
            RRule.fromString(outputRRule.rruleString),
            outputRRule.rruleString,
        ];
    }

    async process(ctx, urlpath) {
        if (!this.auth.can_access_form(ctx, "schedule_event")) {
            throw UnauthorizedException;
        }
        try {
            const res = this.validate(ctx, urlpath.path[3]);
            if (res.error) {
                throw new Error(res.errorMessage);
            }
            ctx.body = await this.upsert_events(ctx, res, urlpath.path[3]);
            ctx.status = 200;
        } catch (e) {
            ctx.status = 400;
            ctx.body = e.message;
            console.error(e);
        }
    }

    validate(ctx, id = null) {
        const data = ctx.request.body;
        const error = { error: true };

        if (
            !(
                data &&
                data.patient_id &&
                data.event_start_date &&
                data.event_start_time
            )
        ) {
            error.errorMessage = "Missing Required Fields";
            return error;
        }
        if (data.event_series_id && id && isNaN(+id)) {
            error.errorMessage = "Malformed data: Missing id";
            return error;
        }
        if (
            !(
                this.fx.validateTimestamp(
                    data.event_start_date,
                    "MM/DD/YYYY"
                ) && this.fx.validateTimestamp(data.event_start_time, "hh:mm A")
            )
        ) {
            error.errorMessage =
                "Invalid Date Time Format for event_start_date field";
            return error;
        }
        if (
            (data.override_start_date &&
                !this.fx.validateTimestamp(
                    data.override_start_date,
                    "MM/DD/YYYY"
                )) ||
            (data.override_start_time &&
                !this.fx.validateTimestamp(data.override_start_time, "hh:mm A"))
        ) {
            error.errorMessage =
                "Invalid Date Time Format for fields: override_start_date, override_start_time";
            return error;
        }
        if (
            (data.override_end_date &&
                !this.fx.validateTimestamp(
                    data.override_end_date,
                    "MM/DD/YYYY"
                )) ||
            (data.override_end_time &&
                !this.fx.validateTimestamp(data.override_end_time, "hh:mm A"))
        ) {
            error.errorMessage =
                "Invalid Date Time Format for fields: override_end_date, override_end_time";
            return error;
        }
        if (!(ctx.request.method == "PUT" || ctx.request.method == "POST")) {
            error.errorMessage = "Invalid Request Method";
            return error;
        }
        return data;
    }
};
