"use strict";

const InventoryLedgerClass = require("./ledger");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.site = null;
        this.fx = nes.modules.fx;
    }

    async void_transfer(ctx, transaction, data) {
        if (data.lot_id && data.lot_id > 0) {
            const tinv_lot_rec = await this.form.get.get_form(
                ctx,
                ctx.user,
                "inventory_lot",
                { limit: 1, filter: "receipt_no:" + data.receipt_no }
            );
            if (!tinv_lot_rec) {
                console.warn(`Inventory Lot not found Lot ID:${data.lot_id}`);
                throw new Error("Inventory Lot not found");
            }
            const tinv_lot = tinv_lot_rec[0];

            await transaction.update(
                "inventory_lot",
                { ...data, archived: true },
                tinv_lot.id
            );
        }

        if (data.serial_id && data.serial_id > 0) {
            const tinv_serial_rec = await this.form.get.get_form(
                ctx,
                ctx.user,
                "inventory_serial",
                { limit: 1, filter: "receipt_no:" + data.receipt_no }
            );
            if (!tinv_serial_rec) {
                console.warn(
                    `Inventory Serial not found Serial ID:${data.serial_id}`
                );
                throw new Error("Inventory Serial not found");
            }
            const tinv_serial = tinv_serial_rec[0];

            await transaction.update(
                "inventory_serial",
                { ...data, archived: true },
                tinv_serial.id
            );
        }
    }

    async void_inv_items(ctx, transaction, data) {
        // Check for existing inventory and serial entries from the PO to archive
        const lots_rec = await this.form.get.get_form(
            ctx,
            ctx.user,
            "inventory_lot",
            { filter: "receipt_no:" + data.receipt_no }
        );
        if (lots_rec.length >= 1) {
            const updatePromises = [];
            for (const lot of lots_rec) {
                updatePromises.push(
                    transaction.update(
                        "inventory_lot",
                        { ...data, archived: true },
                        lot.id
                    )
                );
            }
            if (updatePromises.length > 0) await Promise.all(updatePromises);
        }

        const serials_rec = await this.form.get.get_form(
            ctx,
            ctx.user,
            "inventory_serial",
            { filter: "receipt_no:" + data.receipt_no }
        );
        if (serials_rec.length >= 1) {
            const updatePromises = [];
            for (const serial of serials_rec) {
                updatePromises.push(
                    transaction.update(
                        "inventory_serial",
                        { ...data, archived: true },
                        serial.id
                    )
                );
            }
            if (updatePromises.length > 0) await Promise.all(updatePromises);
        }
    }

    async process(ctx, urlpath) {
        if (ctx.request.method == "POST") {
            ctx.status = 500;
            ctx.body = {
                error: "Receipts should only be generated on the server.",
            };
            return;
        }

        if (this.fx.checkParameters({ form: "Form" }, ctx) == false) return;

        const params = Object.assign({}, ctx.query);
        const data = ctx.request.body;
        if (data.void != "Yes") {
            // Nothing to update, you can only void receipts
            ctx.status = 200;
            ctx.body = data;
            return;
        }

        // Make sure this is the first time voiding and they didn't edit and resave
        // Blocked on the client but just to make sure
        const id = urlpath.path[3];
        const rc_rec = await this.form.get.get_form(
            ctx,
            ctx.user,
            params.form,
            { limit: 1, filter: "id:" + id }
        );
        const og_rec = rc_rec[0];

        if (og_rec.void == "Yes") {
            console.warn(`Receipt already voided ID:${og_rec.id}`);
            ctx.status = 400;
            ctx.body = { error: "Receipt already voided." };
            return;
        }

        try {
            const transaction = this.db.env.rw.transaction(ctx);
            switch (params.form) {
                case "receipt_transfer":
                    await this.void_transfer(ctx, transaction, data);
                    break;
                case "receipt_po":
                    await this.void_inv_items(ctx, transaction, data);
                    break;
                case "receipt_adjustment":
                    // Nothing to do here
                    break;
                default:
                    ctx.status = 500;
                    console.error(
                        `Receipt not supported for voiding:${params.form}`
                    );
                    throw new Error("Form not supported for voiding");
            }

            // Add any necessary ledger entries
            const ledger = new InventoryLedgerClass(this.nes);
            await ledger.checkLedger(transaction, {
                data: data,
                form: params.form,
            });
            await transaction.update(params.form, data, id);

            const res = await transaction.commit();
            if (res.error) {
                console.error(`Error saving receipt:${res.message}`);
                ctx.status = 500;
                ctx.body = { error: res.message };
                return;
            }
            if (res.length > 0) {
                data.id = res[0].id;
            }
            ctx.body = data;
            ctx.status = 200;
        } catch (e) {
            console.error(`Exception saving receipt:${e.message}`);
            ctx.status = 400;
            ctx.body = e.message;
        }
    }
};
