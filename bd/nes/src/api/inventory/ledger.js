"use strict";
const _ = require("lodash");
const SiteClass = require("@operations/site");
const currency = require("currency.js");
const { ModuleLogger } = require("@core/logger");

module.exports = class InventoryLedgerClass extends ModuleLogger {
    constructor(nes, ctx) {
        super("ledger");
        this.db = nes.modules.db;
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.ctx = ctx;
        this.form = nes.shared.form;

        this.tran_types = {
            DISP: "Dispense",
            TRAN: "Transfer",
            PURC: "Purchase",
            RECL: "Recall",
            ADJT: "Adjustment",
            VOID: "Void",
        };
    }

    // blob = { "form": "formname", "data": { "field": "value" }... }
    async checkLedger(transaction, blob = false, receipt_id = null) {
        // Perform inspection on transaction object or blob
        if (blob) {
            await this.__performBlobInspection(transaction, blob, receipt_id);
            return;
        }
    }

    async __performBlobInspection(transaction, blob, receipt_id) {
        super.log("Performing Blob Inspection...");

        if (!("form" in blob) || !("data" in blob)) {
            super.error(`Invalid blob object. Missing form or data`);
            throw new Error("Invalid blob object. Missing form or data");
        }

        switch (blob.form) {
            case "view_inventory_lots":
                await this.__performLotsInspection(transaction, blob.data);
                break;
            case "view_inventory_adjustment":
                await this.__performAdjustmentInspection(
                    transaction,
                    blob.data,
                    receipt_id
                );
                break;
            case "view_inventory_transfer":
                await this.__performTransferInspection(transaction, blob.data);
                break;
            case "receipt_po":
                await this.__performVoidPoInspection(transaction, blob.data);
                break;
            case "receipt_adjustment":
                await this.__performVoidAdjInspection(transaction, blob.data);
                break;
            case "receipt_transfer":
                await this.__performVoidTransInspection(transaction, blob.data);
                break;
            default:
                super.error(
                    `Form not supported for ledger inspection: ${blob.form}`
                );
                throw new Error("Form not supported for ledger inspection");
        }
    }

    async __performVoidPoInspection(transaction, data) {
        super.log("Performing PO Void Inspection...");

        if (data.void !== "Yes") {
            // Nothing to do, not voided
            return;
        }

        const linv_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "ledger_inventory",
            { filter: "receipt_no:" + data.receipt_no }
        );
        if (linv_rec.length < 1) {
            throw new Error("No ledger entries found for PO");
        }

        let site = null;
        for (const linv of linv_rec) {
            if (!site) {
                site = await new SiteClass(
                    this.nes,
                    this.ctx,
                    linv.site_id
                ).init();
            }
            const nle = _.pick(linv, [
                "source_id",
                "po_id",
                "site_id",
                "supplier_id",
                "inventory_id",
                "acquisition_cost",
                "quantity",
                "description",
            ]);
            nle.quantity = Number(linv.quantity) * -1;
            nle.source_form = "receipt_po";
            nle.source_id = data.receipt_no;
            nle.transaction_type = this.tran_types.VOID;
            nle.transaction_date = site.localdatestamp;
            const linv_id = await transaction.insert("ledger_inventory", nle);

            const llot_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "ledger_lot",
                { filter: "ledger_id:" + linv.id }
            );
            for (const llot of llot_rec) {
                const nll = _.pick(llot, [
                    "source_id",
                    "po_id",
                    "site_id",
                    "supplier_id",
                    "lot_no",
                    "inventory_id",
                    "acquisition_cost",
                    "quantity",
                    "description",
                ]);
                nll.quantity = Number(llot.quantity) * -1;
                nll.source_form = "receipt_po";
                nll.source_id = data.receipt_no;
                nll.transaction_type = this.tran_types.VOID;
                nll.transaction_date = site.localdatestamp;
                nll.ledger_id = linv_id;
                await transaction.insert("ledger_lot", nll);
            }

            const lserial_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "ledger_serial",
                { filter: "ledger_id:" + linv.id }
            );
            for (const lserial of lserial_rec) {
                const nls = _.pick(lserial, [
                    "source_id",
                    "po_id",
                    "site_id",
                    "supplier_id",
                    "serial_no",
                    "inventory_id",
                    "acquisition_cost",
                    "quantity",
                    "description",
                ]);
                nls.quantity = Number(lserial.quantity) * -1;
                nls.source_form = "receipt_po";
                nls.source_id = data.receipt_no;
                nls.transaction_type = this.tran_types.VOID;
                nls.transaction_date = site.localdatestamp;
                nls.ledger_id = linv_id;
                await transaction.insert("ledger_serial", nls);
            }
        }
    }

    async __performVoidAdjInspection(transaction, data) {
        super.log("Performing Adjustment Void Inspection...");

        if (data.void !== "Yes") {
            // Nothing to do, not voided
            return;
        }

        const linv_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "ledger_inventory",
            { filter: "receipt_no:" + data.receipt_no }
        );
        if (linv_rec.length < 1) {
            super.error(
                `No ledger entries found for Adjustment Receipt ${data.receipt_no}`
            );
            throw new Error("No ledger entries found for Adjustment");
        }

        const inv_data = linv_rec[0];
        const site = await new SiteClass(
            this.nes,
            this.ctx,
            data.site_id
        ).init();
        const nle = _.pick(inv_data, [
            "source_id",
            "site_id",
            "supplier_id",
            "inventory_id",
            "quantity",
            "description",
        ]);
        nle.quantity = Number(nle.quantity) * -1;
        nle.source_form = "receipt_adjustment";
        nle.source_id = data.receipt_no;
        nle.transaction_type = this.tran_types.VOID;
        nle.transaction_date = site.localdatestamp;
        nle.acquisition_cost = 0.0;
        const linv_id = await transaction.insert("ledger_inventory", nle);

        const llot_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "ledger_lot",
            { filter: "ledger_id:" + inv_data.id }
        );
        if (llot_rec.length < 1) {
            const lot_data = llot_rec[0];
            const nlre = _.pick(lot_data, [
                "source_id",
                "site_id",
                "supplier_id",
                "lot_no",
                "inventory_id",
                "quantity",
                "description",
            ]);
            nlre.quantity = Number(nlre.quantity) * -1;
            nlre.ledger_id = linv_id;
            nlre.source_form = "receipt_adjustment";
            nlre.source_id = data.receipt_no;
            nlre.transaction_type = this.tran_types.VOID;
            nlre.transaction_date = site.localdatestamp;
            nlre.acquisition_cost = 0.0;
            await transaction.insert("ledger_lot", nlre);
        }

        const lsni_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "ledger_serial",
            { filter: "ledger_id:" + inv_data.id }
        );
        if (lsni_rec.length < 1) {
            const sni_data = lsni_rec[0];
            const nls = _.pick(sni_data, [
                "source_id",
                "site_id",
                "supplier_id",
                "serial_no",
                "lot_no",
                "inventory_id",
                "quantity",
                "description",
            ]);
            nls.quantity = Number(nls.quantity) * -1;
            nls.ledger_id = linv_id;
            nls.source_form = "receipt_adjustment";
            nls.source_id = data.receipt_no;
            nls.transaction_type = this.tran_types.VOID;
            nls.transaction_date = site.localdatestamp;
            nls.acquisition_cost = 0.0;
            await transaction.insert("ledger_serial", nls);
        }
    }

    async __performVoidTransInspection(transaction, data) {
        super.log("Performing Transfer Void Inspection...");

        if (data.void !== "Yes") {
            // Nothing to do, not voided
            return;
        }

        const linv_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "ledger_inventory",
            { filter: "receipt_no:" + data.receipt_no }
        );
        if (linv_rec.length < 1) {
            super.error(
                `No ledger entries found for Transfer Receipt ${data.receipt_no}`
            );
            throw new Error("No ledger entries found for Transfer");
        }

        const site = await new SiteClass(
            this.nes,
            this.ctx,
            data.site_id
        ).init();
        for (const inv_data of linv_rec) {
            const nle = _.pick(inv_data, [
                "source_id",
                "site_id",
                "supplier_id",
                "inventory_id",
                "quantity",
                "description",
            ]);
            if (nle.site_id === data.site_id) {
                nle.quantity = Number(nle.quantity);
            } else {
                nle.quantity = Number(nle.quantity) * -1;
            }
            nle.source_form = "receipt_transfer";
            nle.source_id = data.receipt_no;
            nle.transaction_type = this.tran_types.VOID;
            nle.transaction_date = site.localdatestamp;
            nle.acquisition_cost = 0.0;
            const linv_id = await transaction.insert("ledger_inventory", nle);

            const llot_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "ledger_lot",
                { filter: "ledger_id:" + inv_data.id }
            );
            for (const lot_data of llot_rec) {
                const nlre = _.pick(lot_data, [
                    "site_id",
                    "supplier_id",
                    "lot_no",
                    "inventory_id",
                    "quantity",
                    "description",
                ]);
                if (nlre.site_id === data.site_id) {
                    nlre.quantity = Number(nlre.quantity);
                } else {
                    nlre.quantity = Number(nlre.quantity) * -1;
                }
                nlre.ledger_id = linv_id;
                nlre.source_form = "receipt_transfer";
                nlre.source_id = data.receipt_no;
                nlre.transaction_type = this.tran_types.VOID;
                nlre.transaction_date = site.localdatestamp;
                nlre.acquisition_cost = 0.0;
                await transaction.insert("ledger_lot", nlre);
            }

            const lsni_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "ledger_serial",
                { filter: "ledger_id:" + inv_data.id }
            );
            for (const sni_data of lsni_rec) {
                const nls = _.pick(sni_data, [
                    "site_id",
                    "supplier_id",
                    "serial_no",
                    "lot_no",
                    "inventory_id",
                    "quantity",
                    "description",
                ]);
                if (nls.site_id === data.site_id) {
                    nls.quantity = Number(nls.quantity);
                } else {
                    nls.quantity = Number(nls.quantity) * -1;
                }
                nls.ledger_id = linv_id;
                nls.source_form = "receipt_transfer";
                nls.source_id = data.receipt_no;
                nls.transaction_type = this.tran_types.VOID;
                nls.transaction_date = site.localdatestamp;
                nls.acquisition_cost = 0.0;
                await transaction.insert("ledger_serial", nls);
            }
        }
    }

    async __performLotsInspection(transaction, lots) {
        super.log("Performing Lots Inspection...");

        const itms = _.groupBy(lots.subform_lots, "inventory_id");
        for (const invid in itms) {
            const gitms = itms[invid];
            const qty = _.sumBy(gitms, "quantity");
            const aqcost = _.sumBy(gitms, "acq_cost");
            const gitm = _.head(gitms);

            const inv_led = {
                receipt_no: lots.receipt_no,
                receipt_date: lots.receipt_date,
                source_form: "po",
                source_id: lots.po_id,
                transaction_type: this.tran_types.PURC,
                transaction_date: lots.receipt_date,
                po_id: lots.po_id,
                site_id: lots.site_id,
                inventory_id: invid,
                acquisition_cost: currency(aqcost).value,
                acquisition_cost_ea: currency(aqcost / (qty || 1)).value,
                supplier_id: lots.supplier_id,
                quantity: qty,
                description:
                    "PO-" +
                    lots.po_id +
                    " ST:" +
                    lots.site_id_auto_name +
                    " SP:" +
                    lots.supplier_id_auto_name +
                    " ITM:" +
                    gitm.inventory_id_auto_name +
                    " QTY:" +
                    qty.toString() +
                    " CST:" +
                    currency(aqcost),
            };
            const linv_id = await transaction.insert(
                "ledger_inventory",
                inv_led
            );

            // Log lot numbers and serial number seperate (unless serial is in a lot)
            const loitms = _.filter(gitms, "lot_no"); // Filter out items with no lot
            const litms = _.groupBy(loitms, "lot_no");
            for (const lt in litms) {
                const lqty = _.sumBy(litms[lt], "quantity");
                const laqcost = _.sumBy(litms[lt], "acq_cost");
                const lot_led = {
                    ledger_id: linv_id,
                    source_id: lots.po_id,
                    source_form: "po",
                    transaction_type: this.tran_types.PURC,
                    transaction_date: lots.receipt_date,
                    po_id: lots.po_id,
                    site_id: lots.site_id,
                    supplier_id: lots.supplier_id,
                    lot_no: lt,
                    inventory_id: invid,
                    acquisition_cost: currency(laqcost).value,
                    acquisition_cost_ea: currency(laqcost / (lqty || 1)).value,
                    quantity: lqty,
                    description:
                        "PO-" +
                        lots.po_id +
                        " ST:" +
                        lots.site_id_auto_name +
                        " SP:" +
                        lots.supplier_id_auto_name +
                        " ITM:" +
                        gitm.inventory_id_auto_name +
                        " LOT:" +
                        lt +
                        " QTY:" +
                        qty.toString() +
                        " CST:" +
                        currency(aqcost),
                };
                await transaction.insert("ledger_lot", lot_led);

                if (lt.serial_no && lt.serial_no.length > 0) {
                    const ser_led = {
                        ledger_id: linv_id,
                        source_id: lots.po_id,
                        source_form: "po",
                        transaction_type: this.tran_types.PURC,
                        transaction_date: lots.receipt_date,
                        site_id: lots.site_id,
                        supplier_id: lots.supplier_id,
                        lot_no: lt,
                        serial_no: lt.serial_no,
                        inventory_id: invid,
                        acquisition_cost: currency(laqcost).value,
                        acquisition_cost_ea: currency(laqcost / (lqty || 1))
                            .value,
                        quantity: lqty,
                        description:
                            "PO-" +
                            lots.po_id +
                            " ST:" +
                            lots.site_id_auto_name +
                            " SP:" +
                            lots.supplier_id_auto_name +
                            " ITM:" +
                            gitm.inventory_id_auto_name +
                            " LOT:" +
                            lt +
                            " QTY:" +
                            qty.toString() +
                            " CST:" +
                            currency(aqcost),
                    };
                    await transaction.insert("ledger_serial", ser_led);
                }
            }

            const soitms = _.filter(lots.subform_lots, function (lot) {
                return (
                    lot.serial_no &&
                    lot.serial_no.length > 0 &&
                    (!lot.lot_no || lot.lot_no.length == 0)
                );
            }); // Filter out items with no serial
            for (const sitm of soitms) {
                const sqty = sitm.quantity;
                const saqcost = sitm.acq_cost;
                const ser_led = {
                    ledger_id: linv_id,
                    source_id: lots.po_id,
                    source_form: "po",
                    transaction_type: this.tran_types.PURC,
                    transaction_date: lots.receipt_date,
                    site_id: lots.site_id,
                    supplier_id: lots.supplier_id,
                    serial_no: sitm.serial_no,
                    inventory_id: invid,
                    acquisition_cost: currency(saqcost / (sqty || 1)).value,
                    quantity: sqty,
                    description:
                        "PO-" +
                        lots.po_id +
                        " ST:" +
                        lots.site_id_auto_name +
                        " SP:" +
                        lots.supplier_id_auto_name +
                        " ITM:" +
                        gitm.inventory_id_auto_name +
                        " SNI:" +
                        sitm.serial_no +
                        " QTY:" +
                        qty.toString() +
                        " CST:" +
                        currency(aqcost),
                };
                transaction.insert("ledger_serial", ser_led);
            }
        }
    }

    async __performTransferInspection(transaction, data) {
        super.log("Performing Transfer Inspection...");

        if (
            !data.subform_items ||
            !Array.isArray(data.subform_items) ||
            data.subform_items.length === 0
        ) {
            console.warn(`Invalid receipt transfer items`);
            throw new Error("Invalid receipt transfer items");
        }

        for (const item of data.subform_items) {
            if (Number(item.quantity) <= 0) {
                super.error(`Invalid transfer quantity:${item.quantity}`);
                throw new Error("Ledger: Invalid transfer quantity");
            }

            const finv_led = {
                receipt_no: data.receipt_no,
                receipt_date: data.receipt_date,
                source_form: "receipt_transfer",
                source_id: data.receipt_no,
                transaction_type: this.tran_types.TRAN,
                transaction_date: data.receipt_date,
                site_id: data.site_id,
                inventory_id: item.inventory_id,
                quantity: Number(item.quantity) * -1,
                acquisition_cost: 0.0,
                description:
                    "FR:" +
                    data.site_id_auto_name +
                    " TO:" +
                    data.to_site_id_auto_name +
                    " ITM:" +
                    item.inventory_id_auto_name +
                    " QTY:" +
                    item.quantity.toString() +
                    " CMT: " +
                    (data.comments || ""),
            };

            const flid = await transaction.insert("ledger_inventory", finv_led);

            const tinv_led = {
                receipt_no: data.receipt_no,
                receipt_date: data.receipt_date,
                source_form: "receipt_transfer",
                source_id: data.receipt_no,
                transaction_type: this.tran_types.TRAN,
                transaction_date: data.receipt_date,
                site_id: data.to_site_id,
                inventory_id: item.inventory_id,
                quantity: Number(item.quantity),
                acquisition_cost: 0.0,
                description:
                    "FR:" +
                    data.site_id_auto_name +
                    " TO:" +
                    data.to_site_id_auto_name +
                    " ITM:" +
                    item.inventory_id_auto_name +
                    " QTY:" +
                    item.quantity.toString() +
                    " CMT: " +
                    (data.comments || ""),
            };
            const tlid = await transaction.insert("ledger_inventory", tinv_led);

            if (item.lot_id && item.lot_id > 0) {
                const inv_lot_rec = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "inventory_lot",
                    { limit: 1, filter: "id:" + item.lot_id }
                );
                if (!inv_lot_rec) {
                    super.error(
                        `Inventory Lot not found Lot ID:${item.lot_id}`
                    );
                    throw new Error("Ledger: Inventory Lot not found");
                }
                const inv_lot = inv_lot_rec[0];
                const fll_data = {
                    ledger_id: flid,
                    source_form: "receipt_transfer",
                    source_id: data.receipt_no,
                    transaction_type: this.tran_types.TRAN,
                    transaction_date: data.receipt_date,
                    site_id: data.site_id,
                    supplier_id: inv_lot.supplier_id,
                    lot_no: inv_lot.lot_no,
                    inventory_id: item.inventory_id,
                    quantity: Number(item.quantity) * -1,
                    acquisition_cost: 0.0,
                    description:
                        "FR:" +
                        data.site_id_auto_name +
                        " TO:" +
                        data.to_site_id_auto_name +
                        " ITM:" +
                        item.inventory_id_auto_name +
                        " LOT:" +
                        inv_lot.lot_no +
                        " QTY:" +
                        item.quantity.toString() +
                        " CMT: " +
                        (data.comments || ""),
                };
                await transaction.insert("ledger_lot", fll_data);

                const tll_data = {
                    ledger_id: tlid,
                    source_form: "receipt_transfer",
                    source_id: data.receipt_no,
                    transaction_type: this.tran_types.TRAN,
                    transaction_date: data.receipt_date,
                    site_id: data.to_site_id,
                    supplier_id: inv_lot.supplier_id,
                    lot_no: inv_lot.lot_no,
                    inventory_id: item.inventory_id,
                    quantity: Number(item.quantity),
                    acquisition_cost: 0.0,
                    description:
                        "FR:" +
                        data.site_id_auto_name +
                        " TO:" +
                        data.to_site_id_auto_name +
                        " ITM:" +
                        item.inventory_id_auto_name +
                        " LOT:" +
                        inv_lot.lot_no +
                        " QTY:" +
                        item.quantity.toString() +
                        " CMT: " +
                        (data.comments || ""),
                };
                await transaction.insert("ledger_lot", tll_data);
            }

            if (item.serial_id && item.serial_id > 0) {
                const inv_ser_rec = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "inventory_serial",
                    { limit: 1, filter: "id:" + item.serial_id }
                );
                if (!inv_ser_rec) {
                    super.error(
                        `Inventory Serial not found Serial ID:${item.serial_id}`
                    );
                    throw new Error("Ledger: Inventory Serial not found");
                }
                const inv_serial = inv_ser_rec[0];
                const fsl_data = {
                    ledger_id: flid,
                    source_form: "receipt_transfer",
                    source_id: data.receipt_no,
                    transaction_type: this.tran_types.TRAN,
                    transaction_date: data.receipt_date,
                    site_id: data.site_id,
                    supplier_id: inv_serial.supplier_id,
                    serial_no: inv_serial.serial_no,
                    lot_no: inv_serial.lot_no,
                    inventory_id: item.inventory_id,
                    quantity: Number(item.quantity) * -1,
                    acquisition_cost: 0.0,
                    description:
                        "FR:" +
                        data.site_id_auto_name +
                        " TO:" +
                        data.to_site_id_auto_name +
                        " ITM:" +
                        item.inventory_id_auto_name +
                        " SNI:" +
                        inv_serial.serial_no +
                        " QTY:" +
                        item.quantity.toString() +
                        " CMT: " +
                        (data.comments || ""),
                };
                await transaction.insert("ledger_serial", fsl_data);

                const tsl_data = {
                    ledger_id: tlid,
                    source_form: "receipt_transfer",
                    source_id: data.receipt_no,
                    transaction_type: this.tran_types.TRAN,
                    transaction_date: data.receipt_date,
                    site_id: data.to_site_id,
                    supplier_id: inv_serial.supplier_id,
                    serial_no: inv_serial.serial_no,
                    lot_no: inv_serial.lot_no,
                    inventory_id: item.inventory_id,
                    quantity: Number(item.quantity),
                    acquisition_cost: 0.0,
                    description:
                        "FR:" +
                        data.site_id_auto_name +
                        " TO:" +
                        data.to_site_id_auto_name +
                        " ITM:" +
                        item.inventory_id_auto_name +
                        " SNI:" +
                        inv_serial.serial_no +
                        " QTY:" +
                        item.quantity.toString() +
                        " CMT: " +
                        (data.comments || ""),
                };
                await transaction.insert("ledger_serial", tsl_data);
            }
        }
    }

    async __performAdjustmentInspection(transaction, data, receipt_id) {
        super.log("Performing Adjustment Inspection...");
        if (Number(data.quantity) == 0) {
            super.error(`Invalid adjustment quantity:${data.quantity}`);
            throw new Error("Ledger: Invalid adjustment quantity");
        }

        const inv_led = {
            receipt_no: data.receipt_no,
            receipt_date: data.receipt_date,
            source_form: "receipt_adjustment",
            source_id: receipt_id,
            transaction_type: this.tran_types.ADJT,
            transaction_date: data.receipt_date,
            site_id: data.site_id,
            inventory_id: data.inventory_id,
            quantity: Number(data.quantity),
            acquisition_cost: 0.0,
            description:
                "ST:" +
                data.site_id_auto_name +
                " ITM:" +
                data.inventory_id_auto_name +
                " QTY:" +
                data.quantity.toString() +
                " CMT: " +
                (data.comments || ""),
        };

        const flid = await transaction.insert("ledger_inventory", inv_led);

        if (data.lot_id && data.lot_id > 0) {
            const inv_lot_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "inventory_lot",
                { limit: 1, filter: "id:" + data.lot_id }
            );
            if (!inv_lot_rec) {
                super.error(`Inventory Lot not found Lot ID:${data.lot_id}`);
                throw new Error("Ledger: Inventory Lot not found");
            }
            const inv_lot = inv_lot_rec[0];
            const fll_data = {
                ledger_id: flid,
                source_form: "receipt_adjustment",
                source_id: receipt_id,
                transaction_type: this.tran_types.ADJT,
                transaction_date: data.receipt_date,
                site_id: data.site_id,
                supplier_id: inv_lot.supplier_id,
                lot_no: inv_lot.lot_no,
                inventory_id: data.inventory_id,
                quantity: Number(data.quantity),
                acquisition_cost: 0.0,
                description:
                    "ST:" +
                    data.site_id_auto_name +
                    " ITM:" +
                    data.inventory_id_auto_name +
                    " LOT:" +
                    inv_lot.lot_no +
                    " QTY:" +
                    data.quantity.toString() +
                    " CMT: " +
                    (data.comments || ""),
            };
            await transaction.insert("ledger_lot", fll_data);
        }

        if (data.serial_id && data.serial_id > 0) {
            const inv_ser_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "inventory_serial",
                { limit: 1, filter: "id:" + data.serial_id }
            );
            if (!inv_ser_rec) {
                super.error(
                    `Inventory Serial not found Serial ID:${data.serial_id}`
                );
                throw new Error("Ledger: Inventory Serial not found");
            }
            const inv_serial = inv_ser_rec[0];
            const fsl_data = {
                ledger_id: flid,
                source_form: "receipt_adjustment",
                source_id: receipt_id,
                transaction_type: this.tran_types.ADJT,
                transaction_date: data.receipt_date,
                site_id: data.site_id,
                supplier_id: inv_serial.supplier_id,
                lot_no: inv_serial.lot_no,
                serial_no: inv_serial.serial_no,
                inventory_id: data.inventory_id,
                quantity: Number(data.quantity),
                acquisition_cost: 0.0,
                description:
                    "FR:" +
                    data.site_id_auto_name +
                    " TO:" +
                    data.to_site_id_auto_name +
                    " ITM:" +
                    data.inventory_id_auto_name +
                    " SNI:" +
                    inv_serial.serial_no +
                    " QTY:" +
                    data.quantity.toString() +
                    " CMT: " +
                    (data.comments || ""),
            };
            await transaction.insert("ledger_serial", fsl_data);
        }
    }
};
