"use strict";
const _ = require("lodash");

module.exports = class InventoryCheckerClass {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.nes = nes;
    }

    /**
     * Checks the stock for a given inventory item at a specific site.
     * @async
     * @param {number} siteId - The ID of the site.
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {string|boolean} [lotNo=false] - The lot number, if applicable.
     * @param {string|boolean} [serialNo=false] - The serial number, if applicable.
     * @returns {Promise<number>} The quantity of stock available.
     */
    async checkStock(siteId, inventoryId, lotNo = false, serialNo = false) {
        if (lotNo && serialNo) {
            return await this.checkLotSerialStock(
                siteId,
                inventoryId,
                lotNo,
                serialNo
            );
        } else if (lotNo) {
            return await this.__checkLotStock(siteId, inventoryId, lotNo);
        } else if (serialNo) {
            return await this.__checkSerialStock(siteId, inventoryId, serialNo);
        }

        const sql = `SELECT COALESCE(SUM(il.quantity),0) as quantity
		FROM form_ledger_inventory il
        WHERE
        il.inventory_id = ${inventoryId}
        AND il.site_id = ${siteId}
        AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        const row = _.head(rows) || { quantity: 0 };
        const holdQuantity = await this.checkQuantityHold(inventoryId, siteId);
        return Number(row.quantity) - holdQuantity;
    }

    /**
     * Checks the stock quantity for a specific lot and serial number combination at a given site and inventory.
     * @async
     * @param {number} siteId - The ID of the site.
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {string} lotNo - The lot number to check.
     * @param {string} serialNo - The serial number to check.
     * @returns {Promise<number>} The quantity of stock available for the specified lot and serial number combination.
     */
    async checkLotSerialStock(siteId, inventoryId, lotNo, serialNo) {
        const sql = `SELECT COALESCE(SUM(il.quantity),0) as quantity
		FROM form_ledger_serial ils
		INNER JOIN form_ledger_inventory il ON il.inventory_id = ${inventoryId} AND il.id = ils.ledger_id AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE
        WHERE
        ils.site_id = ${siteId}
        AND ils.serial_no = '${serialNo}'
        AND ils.lot_no = '${lotNo}'
        AND ils.archived IS NOT TRUE AND ils.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        const row = _.head(rows) || { quantity: 0 };
        return Number(row.quantity);
    }

    /**
     * Checks the quantity of inventory on hold for a specific inventory item at a site.
     * @async
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {number} siteId - The ID of the site.
     * @returns {Promise<number>} The quantity of inventory on hold.
     */
    async checkQuantityHold(inventoryId, siteId) {
        const sql = `SELECT COALESCE(SUM(ih.quantity),0) as quantity
		FROM form_inventory_hold ih
        WHERE
        ih.inventory_id = ${inventoryId}
        AND ih.site_id = ${siteId}
        AND ih.archived IS NOT TRUE AND ih.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        const row = _.head(rows) || { quantity: 0 };
        return Number(row.quantity);
    }

    /**
     * Checks the stock quantity for a specific lot at a given site and inventory.
     * @async
     * @param {number} siteId - The ID of the site.
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {string} lotNo - The lot number to check.
     * @returns {Promise<number>} The quantity of stock available for the specified lot.
     */
    async __checkLotStock(siteId, inventoryId, lotNo) {
        const sql = `SELECT COALESCE(SUM(il.quantity),0) as quantity
		FROM form_ledger_lot ill
		INNER JOIN form_ledger_inventory il ON il.inventory_id = ${inventoryId} AND il.id = ill.ledger_id AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE
        WHERE
        ill.site_id = ${siteId}
        AND ill.lot_no = '${lotNo}'
        AND ill.archived IS NOT TRUE AND ill.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        const row = _.head(rows) || { quantity: 0 };
        return Number(row.quantity);
    }

    /**
     * Checks the stock quantity for a specific serial number at a given site and inventory.
     * @async
     * @param {number} siteId - The ID of the site.
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {string} serialNo - The serial number to check.
     * @returns {Promise<number>} The quantity of stock available for the specified serial number.
     */
    async __checkSerialStock(siteId, inventoryId, serialNo) {
        const sql = `SELECT COALESCE(SUM(il.quantity),0) as quantity
		FROM form_ledger_serial ils
		INNER JOIN form_ledger_inventory il ON il.inventory_id = ${inventoryId} AND il.id = ils.ledger_id AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE
        WHERE
        ils.site_id = ${siteId}
        AND ils.serial_no = '${serialNo}' 
        AND ils.archived IS NOT TRUE AND ils.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        const row = _.head(rows) || { quantity: 0 };
        return Number(row.quantity);
    }
};
