"use strict";

const moment = require("moment");
const { ActionResponseWrappers } = require("@actions");
const { ButtonsActionsHandler } = require("../index");

module.exports = class InventoryReceiptPerformActionHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.ctx = ctx;
    }

    async runAction(id, action) {
        console.log(
            `Processing receipt action ${action} for receipt_transfer ID ${id}`
        );

        try {
            if (action) return await this["_" + action + "ReceiptTransfer"](id);
            else throw this.fx.getClaraError(`Invalid action: ${action}`);
        } catch (e) {
            const errorMessage = `Error processing receipt action. Action: ${action}, receipt_transfer ID: ${id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async _lostReceiptTransfer(receiptTransferId) {
        console.debug(
            `Marking inventory as lost for receipt_transfer ID ${receiptTransferId}`
        );
        if (
            !this.auth.can_access_path(
                this.ctx,
                this.ctx.user,
                "/receipt/can/lost"
            )
        ) {
            return ActionResponseWrappers.error(
                "You do not have permission to mark inventory as lost."
            );
        }
        try {
            const callbackUrl = `/api/form/receipt_transfer/${receiptTransferId}/action/lost`;
            return ActionResponseWrappers.popup(
                {
                    label: "Mark Inventory as Lost",
                    form: "receipt_transfer",
                    sections: ["Transfer Status", "Status Comment"],
                    preset: {
                        status: "Lost",
                        status_by: this.ctx.user.id,
                        status_on: moment().format("MM/DD/YYYY hh:mm a"),
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Mark Lost",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while marking inventory as lost.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async _acceptReceiptTransfer(receiptTransferId) {
        console.debug(`Accepting receipt_transfer ID ${receiptTransferId}`);

        if (
            !this.auth.can_access_path(
                this.ctx,
                this.ctx.user,
                "/receipt/can/accept"
            )
        ) {
            return ActionResponseWrappers.error(
                "You do not have permission to accept transfers."
            );
        }
        try {
            const callbackUrl = `/api/form/receipt_transfer/${receiptTransferId}/action/accept`;
            return ActionResponseWrappers.popup(
                {
                    label: "Accept Transfer",
                    form: "receipt_transfer",
                    sections: ["Transfer Status", "Status Comment"],
                    preset: {
                        status: "Accepted",
                        status_by: this.ctx.user.id,
                        status_on: moment().format("MM/DD/YYYY hh:mm a"),
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Accept",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while accepting transfer.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async _voidReceiptTransfer(receiptTransferId) {
        console.debug(`Voiding receipt_transfer ID ${receiptTransferId}`);

        if (
            !this.auth.can_access_path(
                this.ctx,
                this.ctx.user,
                "/receipt/can/void"
            )
        ) {
            return ActionResponseWrappers.error(
                "You do not have permission to void transfers."
            );
        }
        try {
            const callbackUrl = `/api/form/receipt_transfer/${receiptTransferId}/action/void`;
            return ActionResponseWrappers.popup(
                {
                    label: "Void/Reverse Transfer",
                    form: "receipt_transfer",
                    sections: ["Transfer Status", "Status Comment"],
                    preset: {
                        status: "Voided",
                        void: "Yes",
                        status_by: this.ctx.user.id,
                        status_on: moment().format("MM/DD/YYYY hh:mm a"),
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Void",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while voiding transfer.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
