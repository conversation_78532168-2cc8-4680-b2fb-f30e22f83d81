"use strict";

const moment = require("moment");
const { ActionResponseWrappers } = require("@actions");
const { ButtonsActionsHandler } = require("../index");

module.exports = class InventoryAdjustmentPerformActionHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.ctx = ctx;
    }

    async runAction(id, action) {
        console.log(
            `Processing adjustment action ${action} for adjustment ID ${id}`
        );

        try {
            if (action) return await this["_" + action + "Adjustment"](id);
            else throw this.fx.getClaraError(`Invalid action: ${action}`);
        } catch (e) {
            const errorMessage = `Error processing receipt action. Action: ${action}, receipt_transfer ID: ${id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
    async _voidAdjustment(adjustmentId) {
        console.debug(`Voiding adjustment ID ${adjustmentId}`);
        if (
            !this.auth.can_access_path(
                this.ctx,
                this.ctx.user,
                "/adjustment/can/void"
            )
        ) {
            return ActionResponseWrappers.error(
                "You do not have permission to void adjustments."
            );
        }
        try {
            const callbackUrl = `/api/form/receipt_adjustment/${adjustmentId}/action/void`;
            return ActionResponseWrappers.popup(
                {
                    label: "Void/Reverse Adjustment",
                    form: "receipt_adjustment",
                    sections: ["Void"],
                    preset: {
                        void: "Yes",
                        voided_by: this.ctx.user.id,
                        voided_datetime: moment().format("MM/DD/YYYY hh:mm a"),
                    },
                    btnLabels: {
                        cancel: "Close",
                        save: "Void",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            const errorMessage = `Error encountered while voiding adjustment.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
