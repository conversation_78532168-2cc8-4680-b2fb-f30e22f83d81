"use strict";

class ButtonsActionsHandler {
    constructor(nes, ctx) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
    }

    // Return an array of action buttons and possbile warning message (see settings/ButtonSchema)
    async getActions(id) {
        return { actions: [], warning: null };
    }
}

class RunActionsHandler {
    constructor(nes, ctx) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
    }

    // Perform the action, see ActionResponseWrappers for return types from this action
    async runAction(id, action) {
        this.ctx.status = 500;
        this.ctx.body = { error: "Not implemented" };
    }
}

class PostActionsHandler {
    constructor(nes, ctx) {
        this.nes = nes;
        this.fx = nes.modules.fx;
        this.ctx = ctx;
    }

    // Perform the action, see ActionResponseWrappers for return types from this action
    async postAction(id, action, params) {
        return { error: "Not implemented" };
    }
}

module.exports = {
    Buttons<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    PostActions<PERSON>andler,
};
