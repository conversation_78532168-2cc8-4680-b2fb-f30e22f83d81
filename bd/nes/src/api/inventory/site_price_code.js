"use strict";
module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
    }

    async get_sites_with_site_price_code_matrix() {
        const sql = `SELECT
            site.auto_name AS site_name,
            site.id AS site_id,
            site_cm.id AS pcm_id,
            site_cm.updated_on AS pcm_updated_on,
            site_cm.created_on AS pcm_created_on
        FROM
            form_site AS site
            LEFT JOIN form_site_price_code_matrix AS site_cm ON site_cm.site_id = site.id
        WHERE
            site.archived IS NOT TRUE
            AND site.deleted IS NOT TRUE
            AND site_cm.archived IS NOT TRUE
            AND site_cm.deleted IS NOT TRUE
            `;
        const rows = await this.db.env.rw.query(sql);
        return rows;
    }

    async process(ctx) {
        try {
            const user = ctx.user;

            // Check to make sure user has access to the inventory form before continuing
            if (
                !user ||
                !this.auth.can_access_form(ctx, "site_price_code_matrix")
            ) {
                ctx.status = 403;
                ctx.body = {
                    error: "Access denied, access to inventory not allowed",
                };
                return;
            }

            ctx.status = 200;
            ctx.body = await this.get_sites_with_site_price_code_matrix();
            return;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
