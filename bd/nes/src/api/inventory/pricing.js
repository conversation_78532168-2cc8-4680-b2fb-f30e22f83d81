"use strict";
const _ = require("lodash");

module.exports = class InventoryPricingClass {
    constructor(nes, ctx) {
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
        this.form = nes.shared.form;
    }

    /**
     * Retrieves pricing information for a specific item, payer, and site.
     * @async
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {number} insuranceId - The ID of the insurance.
     * @param {number} siteId - The ID of the site.
     * @param {number} patientId - The ID of the patient.
     * @returns {Promise<Object>} A promise that resolves to an object containing pricing details.
     */
    async getPricing(inventoryId, insuranceId, siteId, patientId) {
        let rows = [];

        rows = await this.__getPricing(
            inventoryId,
            siteId,
            insuranceId,
            patientId
        );
        const row = _.head(rows) || {};

        return row;
    }

    /**
     * Retrieves pricing information for a specific item at a given site.
     * @async
     * @param {number} inventoryId - The ID of the inventory item.
     * @param {number} insuranceId - The ID of the insurance.
     * @param {number} siteId - The ID of the site.
     * @param {number} patientId - The ID of the patient.
     * @returns {Promise<Object[]>} A promise that resolves to an array of pricing information objects.
     */
    async __getPricing(inventoryId, siteId, insuranceId, patientId) {
        const sql = `
        SELECT 
            -- Pricing columns
            bill_ea,
            special_ea,
            expected_ea,
            cost_ea,
            
            -- Rental pricing
            daily_rental_price_expected,
            weekly_rental_price_expected,
            monthly_rental_price_expected,
            daily_rental_cost,
            weekly_rental_cost,
            monthly_rental_cost,
            
            -- List prices
            list_price,
            daily_rental_list_price,
            weekly_rental_list_price,
            monthly_rental_list_price,
            
            -- Price basis and source
            special_price_price_basis,
            expected_price_price_basis,
            pricing_source,
            
            -- Billing details
            billable,
            billing_unit_ea,
            charge_unit,
            
            -- Item identifiers
            hcpc_id,
            upc,
            upin,
            billable_code_id,
            
            -- Response type
            mm_response_type,
            error
        FROM get_inventory_pricing(
            p_inventory_id := ${inventoryId},
            p_insurance_id := ${insuranceId},
            p_site_id := ${siteId},
            p_patient_id := ${patientId}
        )`;
        const rows = await this.db.env.rw.query(sql);
        return rows;
    }
};
