"use strict";

const SiteClass = require("@operations/site");
const InventoryCheckerClass = require("./stock");
const InventoryLedgerClass = require("./ledger");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.inventory_checker = new InventoryCheckerClass(nes);
        this.site = null;
    }

    async build_receipts(ctx, transaction, data) {
        const receipt_no = transaction.series_next_number("RECEIPT");
        data.receipt_no = receipt_no;
        data.receipt_date = this.site.localdatestamp;

        const adj_rec = {
            receipt_no: data.receipt_no,
            receipt_date: data.receipt_date,
            site_id: data.site_id,
            inventory_id: data.inventory_id,
            lot_id: data?.lot_id || null,
            serial_id: data?.serial_id || null,
            quantity: data.quantity,
            reason_id: data.reason_id,
        };
        const receipt_id = await transaction.insert(
            "receipt_adjustment",
            adj_rec
        );
        return receipt_id;
    }

    async check_inventory(ctx, data) {
        if (Number(data.quantity) > 0) {
            return;
        }

        // Need to check base inventory to account for hold items
        let quantity = await this.inventory_checker.checkStock(
            data.site_id,
            data.inventory_id
        );
        if (quantity < Math.abs(data.quantity)) {
            console.warn(
                `Insufficient inventory at site:${data.site_id}, Inventory ID:${data.inventory_id}, Quantity:${quantity}, Adjustment Quantity:${data.quantity}`
            );
            throw new Error("Insufficient inventory at site");
        }

        let lot_no = false;
        if (data.lot_id && data.lot_id > 0) {
            const inv_lot_rec = await this.form.get.get_form(
                ctx,
                ctx.user,
                "inventory_lot",
                { limit: 1, filter: "id:" + data.lot_id }
            );
            if (!inv_lot_rec) {
                console.warn(`Inventory Lot not found Lot ID:${data.lot_id}`);
                throw new Error("Inventory Lot not found");
            }
            const inv_lot = inv_lot_rec[0];
            lot_no = inv_lot.lot_no;
            quantity = await this.inventory_checker.checkStock(
                data.site_id,
                data.inventory_id,
                inv_lot.lot_no
            );
            if (quantity < Math.abs(data.quantity)) {
                console.warn(
                    `Insufficient lot inventory at site:${data.site_id}, Inventory ID:${data.inventory_id}, Lot No:${inv_lot.lot_no}, Quantity:${quantity}, Adjustment Quantity:${data.quantity}`
                );
                throw new Error("Insufficient lot inventory at site");
            }
        }

        if (data.serial_id && data.serial_id > 0) {
            const inv_serial_rec = await this.form.get.get_form(
                ctx,
                ctx.user,
                "inventory_serial",
                { limit: 1, filter: "id:" + data.serial_id }
            );
            if (!inv_serial_rec) {
                throw new Error("Inventory Serial not found");
            }
            const inv_serial = inv_serial_rec[0];
            quantity = await this.inventory_checker.checkStock(
                data.site_id,
                data.inventory_id,
                lot_no,
                inv_serial.serial_no
            );
            if (quantity < Math.abs(data.quantity)) {
                console.warn(
                    `Insufficient serial inventory at site:${data.site_id}, Inventory ID:${data.inventory_id}, Serial No:${inv_serial.serial_no}, Quantity:${quantity}, Adjustment Quantity:${data.quantity}`
                );
                throw new Error("Insufficient serial inventory at site");
            }
        }
    }

    async process(ctx, urlpath) {
        try {
            const data = ctx.request.body;

            const transaction = this.db.env.rw.transaction(ctx);
            this.site = await new SiteClass(this.nes, ctx, data.site_id).init();

            const receipt_id = await this.build_receipts(
                ctx,
                transaction,
                data
            );
            await this.check_inventory(ctx, data);

            const ledger = new InventoryLedgerClass(this.nes, ctx);
            await ledger.checkLedger(transaction, {
                data: data,
                form: "view_inventory_adjustment",
                receipt_id: receipt_id,
            });
            const res = await transaction.commit();
            if (res.error) {
                ctx.status = 500;
                console.error(`Error saving adjustment:${res.error}`);
                ctx.body = { error: res.message };
                return;
            }
            ctx.body = {
                receipt_id: res[0].id,
            };
            ctx.status = 200;
        } catch (e) {
            console.error(`Exception saving adjustment:${e.message}`);
            ctx.body = { error: e.message };
            ctx.status = 200; // Return 200 here so you can customize the response pop-up
        }
    }
};
