"use strict";

const InventoryCheckerClass = require("./stock");
const InventoryLedgerClass = require("./ledger");
const _ = require("lodash");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.inventory_checker = new InventoryCheckerClass(nes);
        this.site = null;
        this.transfer_site = null;
    }

    async build_receipts(transaction, data, ctx) {
        const receipt_no = transaction.series_next_number("RECEIPT");
        data.receipt_no = receipt_no;
        data.receipt_date = this.site.localdatestamp;

        const trans_rec = {
            receipt_no: data.receipt_no,
            receipt_date: data.receipt_date,
            site_id: data.site_id,
            to_site_id: data.to_site_id,
            status: "Pending",
            subform_items: data.subform_items,
        };
        await transaction.insert("receipt_transfer", trans_rec);
    }

    async build_inventory_entries(ctx, transaction, data) {
        if (
            !data.subform_items ||
            !Array.isArray(data.subform_items) ||
            data.subform_items.length === 0
        ) {
            console.warn(`Invalid transfer items`);
            throw new Error("Invalid transfer items");
        }

        for (const item of data.subform_items) {
            if (Number(item.quantity) <= 0) {
                console.warn(`Invalid transfer quantity:${item.quantity}`);
                throw new Error("Invalid transfer quantity");
            }

            const quantity = await this.inventory_checker.checkStock(
                data.site_id,
                item.inventory_id
            );
            if (quantity < Number(item.quantity)) {
                console.warn(
                    `Insufficient inventory at site:${data.site_id}, Inventory ID:${item.inventory_id}, Quantity:${quantity}, Transfer Quantity:${item.quantity}`
                );
                throw new Error("Insufficient inventory at source site");
            }

            // Build inventory meta data tables for serial and lot tracking
            if (item.lot_id && item.lot_id > 0) {
                const inv_lot_rec = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "inventory_lot",
                    { limit: 1, filter: "id:" + item.lot_id }
                );
                if (!inv_lot_rec) {
                    console.warn(
                        `Inventory Lot not found Lot ID:${item.lot_id}`
                    );
                    throw new Error("Inventory Lot not found");
                }
                const inv_lot = inv_lot_rec[0];
                const quantity = this.inventory_checker.checkStock(
                    data.site_id,
                    item.inventory_id,
                    inv_lot.lot_no
                );
                if (quantity < Number(item.quantity)) {
                    console.warn(
                        `Insufficient lot inventory at site:${data.site_id}, Inventory ID:${item.inventory_id}, Lot No:${inv_lot.lot_no}, Quantity:${quantity}, Transfer Quantity:${item.quantity}`
                    );
                    throw new Error(
                        "Insufficient lot inventory at source site"
                    );
                }

                const dlot_itm = {
                    receipt_no: data.receipt_no,
                    receipt_form: "receipt_transfer",
                    receipt_date: data.receipt_date,
                    site_id: data.to_site_id,
                    inventory_id: item.inventory_id,
                    lot_no: inv_lot.lot_no,
                    expiration_date: inv_lot.expiration_date,
                };
                await transaction.insert("inventory_lot", dlot_itm);

                if (item.serial_id && item.serial_id > 0) {
                    const inv_serial_rec = await this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "inventory_serial",
                        { limit: 1, filter: "id:" + item.serial_id }
                    );
                    if (!inv_serial_rec) {
                        console.warn(
                            `Inventory Serial not found Serial ID:${item.serial_id}`
                        );
                        throw new Error("Inventory Serial not found");
                    }
                    const inv_serial = inv_serial_rec[0];
                    const quantity = this.inventory_checker.checkStock(
                        data.site_id,
                        item.inventory_id,
                        inv_serial.lot_no,
                        inv_serial.serial_no
                    );
                    if (quantity < Number(item.quantity)) {
                        throw new Error(
                            "Insufficient serial inventory at source site"
                        );
                    }

                    const dserial_itm = {
                        receipt_no: data.receipt_no,
                        receipt_form: "receipt_transfer",
                        receipt_date: data.receipt_date,
                        site_id: data.to_site_id,
                        inventory_id: item.inventory_id,
                        lot_no: inv_serial.lot_no,
                        serial_no: inv_serial.serial_no,
                        expiration_date: inv_serial.expiration_date,
                    };
                    await transaction.insert("inventory_serial", dserial_itm);
                }
            }

            if (
                item.serial_id &&
                item.serial_id > 0 &&
                (!item.lot_id || item.lot_id <= 0)
            ) {
                const inv_serial_rec = await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "inventory_serial",
                    { limit: 1, filter: "id:" + item.serial_id }
                );
                if (!inv_serial_rec) {
                    console.warn(
                        `Inventory Serial not found Serial ID:${item.serial_id}`
                    );
                    throw new Error("Inventory Serial not found");
                }
                const inv_serial = inv_serial_rec[0];
                const quantity = this.inventory_checker.checkStock(
                    data.site_id,
                    item.inventory_id,
                    false,
                    inv_serial.serial_no
                );
                if (quantity < Number(item.quantity)) {
                    console.warn(
                        `Insufficient serial inventory at source site:${data.site_id}, Inventory ID:${item.inventory_id}, Serial No:${inv_serial.serial_no}, Quantity:${quantity}, Transfer Quantity:${item.quantity}`
                    );
                    throw new Error(
                        "Insufficient serial inventory at source site"
                    );
                }

                const dserial_itm = {
                    receipt_no: data.receipt_no,
                    receipt_form: "receipt_transfer",
                    receipt_date: data.receipt_date,
                    site_id: data.to_site_id,
                    inventory_id: item.inventory_id,
                    serial_no: inv_serial.serial_no,
                    expiration_date: inv_serial.expiration_date,
                };
                await transaction.insert("inventory_serial", dserial_itm);
            }
        }
    }

    async process(ctx, urlpath) {
        try {
            const data = ctx.request.body;
            const transaction = this.db.env.rw.transaction(ctx);
            const SiteClass = require("@operations/site");
            this.site = await new SiteClass(this.nes, ctx, data.site_id).init();
            this.transfer_site = _.head(
                await this.form.get.get_form(ctx, ctx.user, "site", {
                    limit: 1,
                    filter: "id:" + this.shared.TRANSFER_SITE_ID,
                })
            );
            if (!this.transfer_site) {
                throw new Error("Transfer site not found.");
            }

            await this.build_receipts(transaction, data, ctx);
            const transfer_data = {
                ...data,
                to_site_id: this.transfer_site.id, // Moving to transfer site
            };
            await this.build_inventory_entries(ctx, transaction, transfer_data);

            const ledger = new InventoryLedgerClass(this.nes, ctx);
            await ledger.checkLedger(transaction, {
                data: transfer_data,
                form: "view_inventory_transfer",
            });

            const res = await transaction.commit();
            if (res.error) {
                ctx.status = 500;
                console.error(`Error saving transfer:${res.error}`);
                ctx.body = { error: res.error };
                return;
            }
            ctx.body = {
                receipt_id: res[0].id,
            };
            ctx.status = 200;
        } catch (e) {
            console.error(`Exception saving transfer:${e.message}`);
            ctx.body = { error: e.message };
            ctx.status = 200; // Return 200 here so you can customize the response pop-up
        }
    }
};
