"use strict";

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    async __checkStock(ctx) {
        if (
            this.fx.checkParameters(
                { site_id: "Site ID", inventory_id: "Inventory Item ID" },
                ctx
            ) == false
        )
            return;

        const params = Object.assign({}, ctx.query);
        const site_id = params.site_id;
        const inv_id = params.inventory_id;

        const results = {
            inv_quantity: 0,
            lot_quantity: 0,
            sni_quantity: 0,
        };

        const InventoryCheckerClass = require("./stock");
        const checker = new InventoryCheckerClass(this.nes);
        let lot_no = false;
        if (params.lot_id && params.lot_id > 0) {
            const inv_lot_rec = await this.form.get.get_form(
                ctx,
                ctx.user,
                "inventory_lot",
                { limit: 1, filter: "id:" + params.lot_id }
            );
            if (!inv_lot_rec) {
                throw new Error("Inventory Lot not found");
            }
            const inv_lot = inv_lot_rec[0];
            lot_no = inv_lot.lot_no;
            results.lot_quantity = await checker.checkStock(
                site_id,
                inv_id,
                lot_no
            );
        }

        if (params.serial_id && params.serial_id > 0) {
            const inv_serial_rec = await this.form.get.get_form(
                ctx,
                ctx.user,
                "inventory_serial",
                { limit: 1, filter: "id:" + params.serial_id }
            );
            if (!inv_serial_rec) {
                throw new Error("Inventory Serial not found");
            }
            const inv_serial = inv_serial_rec[0];
            results.sni_quantity = await checker.checkStock(
                site_id,
                inv_id,
                lot_no,
                inv_serial.serial_no
            );
        }

        results.inv_quantity = await checker.checkStock(site_id, inv_id);
        ctx.status = 200;
        ctx.body = { results: results };
        return;
    }

    async process(ctx, urlpath) {
        try {
            if (
                this.fx.checkParameters({ func: "Function Name" }, ctx) == false
            )
                return;

            const params = Object.assign({}, ctx.query);
            const func = params.func;
            switch (func) {
                case "check_stock":
                    await this.__checkStock(ctx);
                    return;
                default:
                    ctx.status = 500;
                    ctx.body = { error: "Invalid function name: " + func };
                    return;
            }
        } catch (e) {
            console.error(e);
            ctx.body = { error: e.message };
            ctx.status = 500;
        }
    }
};
