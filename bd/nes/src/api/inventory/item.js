"use strict";
const _ = require("lodash");

module.exports = class InventoryItemClass {
    constructor(nes, ctx, item_id = null) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.dispensing_unit = null;
        this.internal_hash = null;
        this.nes = nes;
        this.ctx = ctx;
        this.form = nes.shared.form;
        this.internal_item_id = item_id;
    }

    async init() {
        if (!this.internal_item_id) {
            return;
        }

        const item_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "inventory",
            { limit: 1, filter: "id:" + this.internal_item_id }
        );
        if (!item_rec) {
            throw new Error("Inventory Item not found");
        }
        this.data = item_rec[0];

        if (this.data.dispense_unit_id) {
            const unit_rec = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "list_unit",
                { limit: 1, filter: "code:" + this.data.dispense_unit_id }
            );
            if (!unit_rec) {
                throw new Error("Dispensing unit not found");
            }
            this.dispensing_unit = unit_rec[0];
        }
        return this;
    }

    // Public Functions
    async check_quantity(desired_quantity, site_id) {
        let rows = [];
        if (!["Education", "Supply"].includes(this.type)) {
            if (this.allow_fuzzy_match) {
                rows = await this.check_drug_instock_fuzzy(
                    desired_quantity,
                    site_id
                );
            } else {
                rows = await this.check_drug_instock(desired_quantity, site_id);
            }
        } else {
            rows = await this.check_supply_instock(desired_quantity, site_id);
        }

        if (rows && rows.length == 1) {
            const row = rows[0];

            return {
                total_quantity: row.total_quantity,
                required_quantity: row.required_quantity,
            };
        }
        return { total_quantity: 0, required_quantity: desired_quantity };
    }

    // Private Functions
    async check_drug_instock_fuzzy(required_quantity, site_id) {
        const sql = `SELECT COALESCE(SUM(lot.quantity*i.strength),0) as total_quantity,SUM(bi.strength * ${required_quantity}) as required_quantity
        FROM form_inventory_lots il
        INNER JOIN form_inventory i ON il.inventory_id = i.id AND i.archived IS NOT TRUE AND i.deleted IS NOT TRUE
        INNER JOIN form_inventory bi ON bi.id = ${this.id} AND bi.archived IS NOT TRUE AND bi.deleted IS NOT TRUE
        INNER JOIN lsf_form_inventory_lots_to_inventory_lot gril ON gril.form_inventory_lots_fk = il.id AND gril.archive IS NOT TRUE AND gril.delete IS NOT TRUE 
        INNER JOIN form_inventory_lot lot ON lot.id = gril.form_inventory_lot_fk AND lot.archived IS NOT TRUE AND lot.deleted IS NOT TRUE
        WHERE i.brand_name_id = bi.brand_name_id
        AND i.dispense_unit_id = bi.dispense_unit_id
        AND il.site_id = ${site_id}
        AND (lot.expiration_date >= (now() - interval '4 week') OR lot.expiration_date IS NULL)
        AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        return rows;
    }

    async check_drug_instock(required_quantity, site_id) {
        const sql = `SELECT COALESCE(SUM(lot.quantity*i.strength),0) as total_quantity,SUM(i.strength * ${required_quantity}) as required_quantity
        FROM form_inventory_lots il
        INNER JOIN form_inventory i ON il.inventory_id = i.id AND i.archived IS NOT TRUE AND i.deleted IS NOT TRUE AND i.id = ${this.id}
        INNER JOIN lsf_form_inventory_lots_to_inventory_lot gril ON gril.form_inventory_lots_fk = il.id AND gril.archive IS NOT TRUE AND gril.delete IS NOT TRUE
        INNER JOIN form_inventory_lot lot ON lot.id = gril.form_inventory_lot_fk AND lot.archived IS NOT TRUE AND lot.deleted IS NOT TRUE
        WHERE il.site_id = ${site_id}
        AND (lot.expiration_date >= (now() - interval '4 week') OR lot.expiration_date IS NULL)
        AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        return rows;
    }

    async check_supply_instock(quantity, site_id) {
        const sql = `SELECT COALESCE(SUM(lot.quantity),0) AS total_quantity, ${quantity} as required_quantity
        FROM form_inventory_lots il
        INNER JOIN form_inventory i ON il.inventory_id = i.id AND i.archived IS NOT TRUE AND i.deleted IS NOT TRUE AND i.id = ${this.id}
        INNER JOIN lsf_form_inventory_lots_to_inventory_lot gril ON gril.form_inventory_lots_fk = il.id AND gril.archive IS NOT TRUE AND gril.delete IS NOT TRUE
        INNER JOIN form_inventory_lot lot ON lot.id = gril.form_inventory_lot_fk AND lot.archived IS NOT TRUE AND lot.deleted IS NOT TRUE
        WHERE il.site_id = ${site_id}
        AND il.archived IS NOT TRUE AND il.deleted IS NOT TRUE`;
        const rows = await this.db.env.rw.query(sql);
        return rows;
    }

    // Properties
    set item_id(item_id) {
        this.internal_item_id = item_id;
    }

    get item_id() {
        return this.internal_item_id;
    }

    get id() {
        return this.data.id;
    }

    get ndc() {
        return this.data.ndc;
    }

    get generic_name() {
        return this.data.generic_name;
    }

    get revenue_code_id() {
        return this.data.revenue_code_id;
    }

    get hcpc_quantity() {
        return this.data.hcpc_quantity;
    }

    get quantity_each() {
        return this.data.quantity_each;
    }

    get name() {
        return this.data?.name;
    }

    get last_cost() {
        return this.data.last_cost;
    }

    get wac_price() {
        return this.data.wac_price;
    }

    get awp_price() {
        return this.data.awp_price;
    }

    get list_price() {
        return this.data.list_price;
    }

    get unit_qualifier_id() {
        return this.data.unit_qualifier_id;
    }

    get allow_fuzzy_match() {
        return this.data.allow_fuzzy_matching == "Yes";
    }

    get type() {
        return this.data.type_id;
    }

    get category() {
        return this.data.category_id;
    }

    get brand_name() {
        return this.data.brand_name_id_auto_name
            ? this.data.brand_name_id_auto_name
            : this.data.name;
    }

    get hash() {
        if (this.internal_hash) {
            return this.internal_hash;
        }

        //TODO Should be based off of gcn sequence number
        const supply_type = ["Education", "Supply"];
        if (this.brand_name && this.unit && !supply_type.includes(this.type)) {
            this.internal_hash = `DRG:# ${this.brand_name} - ${this.unit}`;
        } else {
            this.internal_hash = "SPLY:#" + this.name;
        }
        return this.internal_hash;
    }

    get manufacturer() {
        return this.data.manufacturer_id_auto_name;
    }

    get unit() {
        return this.dispensing_unit.name;
    }

    get strength() {
        return this.data.strength;
    }
};
