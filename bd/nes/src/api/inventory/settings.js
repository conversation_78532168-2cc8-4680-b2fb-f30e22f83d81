"use strict";

const InventoryTypes = {
    DRUG: "Drug",
    COMPOUND: "Compound",
    SUPPLY: "Supply",
    EQUIPMENT_RENTAL: "Equipment Rental",
    BILLABLE: "Billable",
};

const PartBDMEModifiers = {
    RENTAL: "RR",
    PURCHASE_NEW: "NU",
};

const InventoryActions = {
    REORDER: "Reorder",
    ACCEPT: "Accept",
    VOID: "Void",
    LOST: "Lost",
};

const InventoryActionKeys = {
    [InventoryActions.REORDER]: "reorder",
    [InventoryActions.ACCEPT]: "accept",
    [InventoryActions.VOID]: "void",
    [InventoryActions.LOST]: "lost",
};

const PurchaseOrderActions = {
    RECEIVE: "Receive",
    REORDER: "Reorder",
};

const PurchaseOrderActionKeys = {
    [PurchaseOrderActions.RECEIVE]: "receive",
    [PurchaseOrderActions.REORDER]: "reorder",
};

const PricingSource = {
    PART_B_ASP: "part_b_asp",
    PART_B_DME: "part_b_dme",
    PAYER_CONTRACT: "payer_contract",
    SHARED_CONTRACT: "shared_contract",
    LIST: "list",
    COB: "cob_override",
    COPAY: "copay",
};

const InsuranceTypes = {
    MEDICARE_PART_B: "MCRB",
    MEDICARE_PART_D: "MCRD",
    MEDICAID: "MEDI",
    MAJOR_MEDICAL: "CMMED",
    PBM: "CMPBM",
    SELF_PAY: "SELF",
    COPAY: "COPAY",
    PAP: "PAP",
    FOUNDATION: "FOUND",
    OTHER: "OTHER",
};

const RentalBillableType = {
    WEEKLY: "Weekly",
    MONTHLY: "Monthly",
    DAILY: "Daily",
    PURCHASE: "Purchase",
};

module.exports = {
    InventoryTypes,
    InventoryActions,
    InventoryActionKeys,
    PartBDMEModifiers,
    PricingSource,
    InsuranceTypes,
    RentalBillableType,
    PurchaseOrderActions,
    PurchaseOrderActionKeys,
};
