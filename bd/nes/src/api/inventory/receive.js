"use strict";

const _ = require("lodash");
const InventoryLedgerClass = require("./ledger");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
    }

    async check_po(ctx, data) {
        if (!data.po_id || data.po_id == "") {
            // Build mock PO
            const itms = _.groupBy(data.subform_lots, "inventory_id");

            const poitms = [];
            let totcost = 0.0;
            for (const invid in itms) {
                const pqty = _.sumBy(itms[invid], "quantity");
                const paqcost = _.sumBy(itms[invid], "acq_cost");
                totcost += paqcost;
                poitms.push({
                    inventory_id: invid,
                    qty_pkg: pqty,
                    ordered: 1,
                    package_cost: Number(paqcost).toFixed(2),
                });
            }
            const po_data = {
                site_id: data.site_id,
                supplier_id: data.supplier_id,
                subform_items: poitms,
                order_date: data.receipt_date,
                expected_date: data.receipt_date,
                status_id: "RVD",
                total_cost: Number(totcost.toFixed(2)),
                auto_generated: "Yes",
                receipt_file: data.file_attachment,
            };
            data.po_data = po_data;
            data.po_action = "insert";
        } else {
            const po_rec = await this.form.get.get_form(ctx, ctx.user, "po", {
                limit: 1,
                filter: "id:" + data.po_id,
            });
            data.po_data = po_rec[0];

            const update = {
                status_id: "RVD",
                receipt_file: data.file_attachment,
                expected_date: data.receipt_date,
            };
            data.po_update = update;
            data.po_action = "update";
        }
    }

    async update_po(transaction, data, ctx) {
        if (data.po_action == "insert") {
            const po_data = data.po_data;
            po_data.receipt_no = data.receipt_no;
            const po_id = await transaction.insert("po", po_data);
            data.po_id = po_id;
        } else {
            const po_id = data.po_id;
            const update = data.po_update;
            update.receipt_no = data.receipt_no;
            await transaction.update("po", update, po_id);
        }
    }

    async build_receipts(transaction, data, ctx) {
        const receipt_no = transaction.series_next_number("RECEIPT");
        data.receipt_no = receipt_no;

        // We might want to consider if the package cost was empty on PO entry on the receipt
        // but for now we will just use the PO data from the entry in the view_inventory_lots

        const po_receipt = _.pick(data.po_data, [
            "site_id",
            "supplier_id",
            "total_cost",
        ]);
        po_receipt.receipt_no = receipt_no;
        po_receipt.receipt_date = data.receipt_date;
        po_receipt.receipt_file = data.file_attachment;
        po_receipt.site_id = data.site_id;
        po_receipt.po_id = data.po_id;

        const receipt_items = [];
        const itms = _.groupBy(data.subform_lots, "inventory_id");
        for (const invid in itms) {
            const gitms = itms[invid];
            const qty = _.sumBy(gitms, "quantity");
            const cost_ea = _.meanBy(gitms, "cost_each");

            const inv_led = {
                inventory_id: invid,
                cost_each: Number((cost_ea ? cost_ea : 0.0).toFixed(2)),
                quantity: qty,
            };
            receipt_items.push(inv_led);
        }

        po_receipt.subform_items = receipt_items;
        console.log("po_receipt", po_receipt);
        await transaction.insert("receipt_po", po_receipt);
    }

    async build_inventory_entries(transaction, data, ctx) {
        // Build inventory meta data tables for serial and lot tracking
        const loitms = _.filter(data.subform_lots, "lot_no"); // Filter out items with no lot
        for (const itm of loitms) {
            if (itm.lot_no.length == 0) {
                continue;
            }

            const lot_itm = {
                receipt_no: data.receipt_no,
                receipt_form: "receipt_po",
                receipt_date: data.receipt_date,
                site_id: data.site_id,
                inventory_id: itm.inventory_id,
                lot_no: itm.lot_no,
                expiration_date: itm.expiration,
            };
            const filters = [
                `inventory_id:${itm.inventory_id}`,
                `lot_no:${itm.lot_no}`,
                `site_id:${data.site_id}`,
            ];
            const lot_rec = _.head(
                await this.form.get.get_form(ctx, ctx.user, "inventory_lot", {
                    limit: 1,
                    filter: filters,
                })
            );
            console.log("lot_rec1", lot_rec);
            if (lot_rec) {
                console.log("lot_itm1", lot_itm);
                await transaction.update("inventory_lot", lot_itm, lot_rec.id);
            } else {
                console.log("lot_itm2", lot_itm);
                await transaction.insert("inventory_lot", lot_itm);
            }
            if (itm.serial_no && itm.serial_no.length > 0) {
                const serial_itm = {
                    receipt_no: data.receipt_no,
                    receipt_form: "receipt_po",
                    receipt_date: data.receipt_date,
                    site_id: data.site_id,
                    inventory_id: itm.inventory_id,
                    lot_no: itm.lot_no,
                    serial_no: itm.serial_no,
                    expiration_date: itm.expiration,
                };
                const filters = [
                    `inventory_id:${itm.inventory_id}`,
                    `lot_no:${itm.lot_no}`,
                    `site_id:${data.site_id}`,
                ];
                const serial_rec = _.head(
                    await this.form.get.get_form(
                        ctx,
                        ctx.user,
                        "inventory_serial",
                        {
                            limit: 1,
                            filter: filters,
                        }
                    )
                );
                if (serial_rec) {
                    await transaction.update(
                        "inventory_serial",
                        serial_itm,
                        serial_rec.id
                    );
                } else {
                    console.log("serial_itm3", serial_itm);
                    await transaction.insert("inventory_serial", serial_itm);
                }
            }
        }

        const soitms = _.filter(data.subform_lots, "serial_no"); // Filter out items with no serial
        for (const itm of soitms) {
            if (itm.lot_no && itm.lot_no.length > 0) {
                continue; // Skip ones we already entered above
            }

            if (itm.serial_no.length == 0) {
                continue;
            }

            const serial_itm = {
                receipt_no: data.receipt_no,
                receipt_form: "receipt_po",
                receipt_date: data.receipt_date,
                site_id: data.site_id,
                inventory_id: itm.inventory_id,
                serial_no: itm.serial_no,
                expiration_date: itm.expiration,
            };
            const filters = [
                `inventory_id:${itm.inventory_id}`,
                `serial_no:${itm.serial_no}`,
                `site_id:${data.site_id}`,
            ];
            const serial_rec = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "inventory_serial",
                    {
                        limit: 1,
                        filter: filters,
                    }
                )
            );
            if (serial_rec) {
                console.log("serial_rec", serial_rec);
                await transaction.update(
                    "inventory_serial",
                    serial_itm,
                    serial_rec.id
                );
            } else {
                console.log("serial_itm", serial_itm);
                await transaction.insert("inventory_serial", serial_itm);
            }
        }
    }

    async process(ctx, _urlpath) {
        try {
            const data = ctx.request.body;

            if (!data.subform_lots || data.subform_lots.length == 0) {
                ctx.status = 200;
                console.warn(`No items received`);
                ctx.body = { error: "No items received" };
                return;
            }

            // Total aqusition cost to save in later sums
            _.forEach(data.subform_lots, (itm) => {
                itm.acq_cost = (itm.cost_each || 0.0) * Number(itm.quantity);
            });

            const transaction = this.db.env.rw.transaction(ctx);
            await this.check_po(ctx, data);
            await this.build_receipts(transaction, data, ctx);
            await this.update_po(transaction, data, ctx);
            await this.build_inventory_entries(transaction, data, ctx);

            const ledger = new InventoryLedgerClass(this.nes, ctx);
            await ledger.checkLedger(transaction, {
                data: data,
                form: "view_inventory_lots",
            });
            const res = await transaction.commit();
            if (res.error) {
                ctx.status = 500;
                console.error(`Error saving receipt:${res.error}`);
                ctx.body = { error: res.message };
                return;
            }
            ctx.body = {
                receipt_id: res[0].id,
            };
            ctx.status = 200;
        } catch (e) {
            console.error(`Exception saving receipt:${e.message}`);
            ctx.body = { error: e.message };
            ctx.status = 200;
        }
    }
};
