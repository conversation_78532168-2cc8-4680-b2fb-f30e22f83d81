"use strict";
module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
    }

    async getLabels(ctx) {
        const params = Object.assign({}, ctx.query);
        const sql = `
        SELECT DISTINCT 
            sn.gcn_seqno,
            sn.lbl_warn,
            sn.lbl_prty,
            lbl.lbl_textsn,
            lbl.lbl_desc 
        FROM 
            form_list_fdb_ndc AS ndc 
        INNER JOIN 
            form_list_fdb_gcnseq_label_link AS sn ON ndc.gcn_seqno = sn.gcn_seqno 
        INNER JOIN 
            form_list_fdb_warning_label_info AS lbl ON sn.lbl_warn = lbl.lbl_warn 
        WHERE 
            ndc.gcn_seqno = ${params.gcn_seqno} 
        ORDER BY 
            sn.lbl_prty, 
            lbl.lbl_textsn 
        LIMIT 6;        
        `;
        const data = await this.db.env.rw.query(sql);
        let filtered = [];
        if (data.length > 5) {
            for (let i = 0; i < data.length; i++) {
                const item = data[i];
                if (i == 5) {
                    filtered = data.filter(
                        (obj) => !(item.lbl_warn === obj.lbl_warn)
                    );
                }
            }
        } else {
            filtered = data;
        }
        return filtered;
    }

    async process(ctx) {
        try {
            ctx.status = 200;
            ctx.body = await this.getLabels(ctx);
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
