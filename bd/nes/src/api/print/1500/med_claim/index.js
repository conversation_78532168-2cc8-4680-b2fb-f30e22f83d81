"use strict";
const CMSClaimMapperClass = require("../../../billing/medical/1500/mapper");
const { MedClaimType } = require("../../../billing/medical/settings");

module.exports = class ApiView {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.auth = nes.modules.auth;
    }

    /**
     * Processes the 1500 mapping request
     * @param {Object} ctx - The context object
     * @param {Object} urlpath - The URL path object
     * @returns {Promise<void>}
     */
    async process(ctx, urlpath) {
        console.debug(`Handling 1500 mapping request`);

        try {
            const { request, user } = ctx;

            if (!this.auth.can_access_form(ctx, "med_claim")) {
                console.warn(
                    `Form Access denied for user: ${user.id} on form: med_claim`
                );
                this.fx.setReturnErrorMessage(ctx, null, "Access denied.", 403);
                return;
            }

            const mapper = new CMSClaimMapperClass(this.nes, ctx);

            switch (request.method) {
                case "GET":
                    await this.__handleGetRequest(ctx, urlpath, mapper);
                    break;
                case "POST":
                    await this.__handlePostRequest(ctx, request, mapper);
                    break;
                default:
                    this.fx.setReturnErrorMessage(
                        ctx,
                        null,
                        "Invalid request method.",
                        400
                    );
            }
        } catch (e) {
            this.fx.setReturnErrorMessage(ctx, e, "Unable to process request.");
        }
    }

    /**
     * Handles the GET request for 1500 form mapping
     * @param {Object} ctx - The context object
     * @param {Object} urlpath - The URL path object
     * @param {Object} mapper - The CMS claim mapper instance
     * @throws {Error} If the path is invalid or the medical claim ID is not found
     */
    async __handleGetRequest(ctx, urlpath, mapper) {
        if (urlpath.path.length < 4) {
            throw this.fx.getClaraError(
                `Invalid Path. Must include a med_claim ID`
            );
        }

        const id = urlpath.path[3];
        const medClaimRec = await this.__getMedicalClaimRecord(ctx, id);

        if (!medClaimRec) {
            throw this.fx.getClaraError(`Invalid medical claim ID ${id}`);
        }

        const results = await mapper.convertClaim(
            medClaimRec,
            MedClaimType.ELECTRONIC
        );
        ctx.body = results;
        ctx.status = 200;
    }

    /**
     * Handles the POST request for 1500 form mapping
     * @param {Object} ctx - The context object
     * @param {Object} request - The request
     * @param {Object} mapper - The CMS claim mapper instance
     * @returns {Promise<void>}
     */
    async __handlePostRequest(ctx, request, mapper) {
        const { body } = request;
        if (!body) {
            throw this.fx.getClaraError(`Invalid medical claim record`);
        }

        const results = await mapper.convertClaim(
            body,
            MedClaimType.ELECTRONIC
        );
        ctx.body = results;
        ctx.status = 200;
    }

    /**
     * Retrieves a medical claim record by its ID
     * @param {Object} ctx - The context object
     * @param {string} id - The ID of the medical claim record to retrieve
     * @returns {Promise<Object|null>} The medical claim record if found, null otherwise
     */
    async __getMedicalClaimRecord(ctx, id) {
        const filters = [`id:${id}`];
        const [medClaimRec] = await this.form.get.get_form(
            ctx,
            ctx.user,
            "med_claim",
            {
                limit: 1,
                filter: filters,
            }
        );
        return medClaimRec;
    }
};
