/* eslint-disable no-eval */
"use strict";

const PassThrough = require("stream").PassThrough;
const pdfmake = require("pdfmake/src/printer");

module.exports = class ApiView {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;

        this.basedir = __dirname;
        this.fonts = {
            Roboto: {
                normal: this.basedir + "/fonts/Roboto-Regular.ttf",
                bold: this.basedir + "/fonts/Roboto-Medium.ttf",
                italics: this.basedir + "/fonts/Roboto-Italic.ttf",
                bolditalics: this.basedir + "/fonts/Roboto-MediumItalic.ttf",
            },
        };
    }

    // server-side pdf
    async generate_pdf(ctx, user) {
        const req = ctx.request.body;
        if (!req || !("filename" in req) || !("document" in req))
            throw { error: "Invalid PDF parameters!" };
        const cdisp =
            "download" in req && req.download == "true"
                ? "attachment"
                : "inline";
        ctx.set(
            "Content-Disposition",
            cdisp + "; filename=" + req.filename + ".pdf"
        );
        ctx.set("Content-Type", "application/pdf");

        const dd = this.parse_dynamic(JSON.parse(req.document));
        const printer = new pdfmake(this.fonts);
        const pdf = printer.createPdfKitDocument(dd);
        ctx.body = pdf.on("error", ctx.onerror).pipe(PassThrough());
        pdf.end();
    }

    parse_dynamic(doc) {
        const that = this;
        if (typeof doc == "object") {
            let dd;
            if (Array.isArray(doc)) {
                dd = [];
                for (const d of doc) dd.push(this.parse_dynamic(d));
            } else {
                dd = {};
                for (const [k, v] of Object.entries(doc)) {
                    if (
                        [
                            "hLineWidth",
                            "vLineWidth",
                            "hLineColor",
                            "vLineColor",
                            "footer",
                            "pageBreakBefore",
                        ].includes(k) &&
                        "fx" in v &&
                        "src" in v
                    ) {
                        if (v.fx == "line") {
                            dd[k] = function (i, node) {
                                return that.parse_dynamic(eval(v.src));
                            };
                        } else if (v.fx == "footer") {
                            dd[k] = function (currentPage, pageCount) {
                                return that.parse_dynamic(eval(v.src));
                            };
                        } else if (v.fx == "pageBreakBefore") {
                            dd[k] = function (
                                currentNode,
                                followingNodesOnPage,
                                nodesOnNextPage,
                                previousNodesOnPage
                            ) {
                                return that.parse_dynamic(eval(v.src));
                            };
                        }
                    } else {
                        dd[k] = this.parse_dynamic(v);
                    }
                }
            }
            return dd;
        } else {
            return doc;
        }
    }

    // handle pdf delivery
    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            if (!user) {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            } else {
                await this.generate_pdf(ctx, user);
            }
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
