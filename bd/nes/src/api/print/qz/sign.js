"use strict";

const crypto = require("crypto");
const fs = require("fs");
const path = require("path");

module.exports = class ApiView {
    constructor(nes) {
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
    }

    async gen_signature(reqdata) {
        const data = fs.readFileSync(
            path.join(__dirname, "cert/qz.key"),
            "utf8"
        );
        const sign = crypto.createSign("SHA512").update(reqdata);
        const signature = sign.sign({ key: data }, "base64");
        return signature;
    }

    check_parameters(params) {
        if (params && !params.req)
            return { msg: "No req passed in query request.", code: 404 };
        return false;
    }

    async process(ctx, urlpath) {
        const params = Object.assign({}, ctx.query);
        const err = this.check_parameters(params);
        if (err) {
            console.error(err);
            ctx.status = err.code;
            ctx.body = { error: err.msg };
            return;
        }
        try {
            const req = params.req;
            const signature = await this.gen_signature(req);
            ctx.body = signature;
        } catch (e) {
            ctx.status = 500;
            console.error(e);
            ctx.body = { error: "An Error occured, please contact Support." };
        }
    }
};
