"use strict";
module.exports = class PatientDXClass {
    constructor(nes, ctx, dx_id = null) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.dx = null;
        this.form = nes.shared.form;
        this.dx_id = dx_id;
    }

    async init() {
        if (!this.dx_id) {
            return;
        }

        const dx_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "patient_diagnosis",
            { limit: 1, filter: "id:" + this.dx_id }
        );
        if (!dx_rec) {
            throw new Error("Diagnosis record not found");
        }
        this.data = dx_rec[0];
        const DXClass = require("./dx");
        this.dx = await new DXClass(this.nes, this.ctx, this.data.dx_id).init();
        return this;
    }

    get id() {
        return this.data.id;
    }

    get rank() {
        return this.data.rank;
    }

    get patient_id() {
        return this.data.patient_id;
    }

    get icd_code() {
        return this.dx.icd_code;
    }

    get name() {
        return this.dx.name;
    }

    get active() {
        return this.data.active;
    }
};
