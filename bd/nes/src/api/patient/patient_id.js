"use strict";

const _ = require("lodash");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
    }

    check_parameters(user, params) {
        console.log(params);
        if (!(user.id && !isNaN(parseInt(user.id))))
            return { error: "Invalid User Id", code: 400 };
        if (!(params.form_id && !isNaN(parseInt(params.form_id))))
            return { error: "Invalid Form Id", code: 400 };
        if (!params.form_name) return { error: "Invalid Form Name", code: 400 };
        return false;
    }

    async get_patient(ctx, user, params) {
        if (!this.shared.DSL[params.form_name]) {
            return { error: "Form not found", code: 404 };
        }
        if (!this.shared.DSL[params.form_name].fields.patient_id) {
            return {
                error: "Form does not have a patient id field",
                code: 400,
            };
        }
        if (!this.auth.can_access_form(ctx, params.form_name)) {
            return { error: "Access denied", code: 403 };
        }
        if (!this.auth.can_access_field(ctx, params.form_name, "patient_id")) {
            return { error: "Access denied", code: 403 };
        }
        try {
            const db_rec = await this.db.env.rw.query(
                `SELECT patient_id FROM form_%s WHERE id = %s`,
                [params.form_name, params.form_id]
            );

            if (db_rec.length === 0) {
                return { error: "Patient ID Not Found", code: 404 };
            }
            const patient_rec = await this.form.get.get_form(
                ctx,
                user,
                "patient",
                { limit: 1, filter: "id:" + db_rec[0].patient_id }
            );
            if (!patient_rec || patient_rec.length === 0) {
                return { error: "Patient not found", code: 404 };
            }
            return patient_rec[0];
        } catch (e) {
            return { error: "Error", code: 500, stack: e };
        }
    }

    async process(ctx) {
        try {
            const user = ctx.user;
            const params = Object.assign({}, ctx.query);
            const err = this.check_parameters(user, params);
            if (err) {
                ctx.status = err.code;
                ctx.body = { error: err.error };
                return;
            }
            const patient = await this.get_patient(ctx, user, params);
            if (patient.error) {
                ctx.status = patient.code;
                ctx.body = patient;
                return;
            }
            ctx.body = patient;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
