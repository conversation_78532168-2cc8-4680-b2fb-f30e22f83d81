"use strict";
const _ = require("lodash");

module.exports = class PatientClass {
    constructor(nes, ctx = null, patient_id = null) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.internal_primary_dx = null;
        this.internal_site = null;
        this.form = nes.shared.form;
        this.pt_id = patient_id;
        this.internal_patient_status = null;
        return this.fx.dynamicGet(this);
    }

    async init() {
        if (!this.patient_id) {
            return;
        }

        const patient_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "patient",
            { limit: 1, filter: "id:" + this.pt_id }
        );
        if (!patient_rec) {
            throw new Error("Patient not found");
        }
        this.data = patient_rec[0];

        return this;
    }

    async get_site() {
        if (!this.internal_site) {
            const SiteClass = require("@operations/site");
            this.internal_site = await new SiteClass(
                this.nes,
                this.ctx,
                this.data.site_id
            ).init();
        }
        return this.internal_site;
    }

    async get_primary_dx() {
        if (!this.internal_primary_dx) {
            const filters = ["patient_id:" + this.id, "active:Yes"];
            const dx_recs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "patient_diagnosis",
                { filter: filters }
            );
            const primary_dx_rec = _.find(dx_recs, (dx) => {
                return dx.rank == 1;
            });

            const PatientDXClass = require("./patient_dx");
            if (primary_dx_rec) {
                this.internal_primary_dx = await new PatientDXClass(
                    this.nes,
                    this.ctx,
                    primary_dx_rec.id
                ).init();
            }
        }
        return this.internal_primary_dx;
    }

    get patient_id() {
        return this.pt_id;
    }

    get status() {
        return this.data.status_id_auto_name.split(" - ")[1];
    }

    get name() {
        return this.data.firstname + " " + this.data.lastname;
    }

    get address() {
        return this.data.home_street;
    }

    get city() {
        return this.data.home_city;
    }

    get state() {
        return this.data.home_state;
    }

    get zip() {
        return this.data.home_zip;
    }

    get phone() {
        if (this.data.phone_home) {
            return this.data.phone_home;
        } else if (this.data.phone_cell) {
            return this.data.phone_cell;
        } else if (this.data.phone_work) {
            return this.data.phone_work;
        } else {
            return "";
        }
    }
};
