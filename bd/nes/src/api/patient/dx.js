"use strict";
module.exports = class DxClass {
    constructor(nes, ctx = null, dx_id = null) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.form = nes.shared.form;
        this.dx_id = dx_id;
    }

    async init() {
        if (!this.dx_id) {
            return;
        }

        const dx_rec = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "list_diagnosis",
            { limit: 1, filter: "code:" + this.dx_id }
        );
        if (!dx_rec) {
            throw new Error("Diagnosis record not found");
        }
        this.data = dx_rec[0];
        return this;
    }

    get icd_code() {
        return this.data.icd_code;
    }

    get name() {
        return this.data.name;
    }
};
