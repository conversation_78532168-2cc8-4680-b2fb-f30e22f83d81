"use strict";

const { ModuleLogger } = require("@core/logger");
const SSHelperClass = require("@surescripts/helper");

const { SSMapping } = require("@surescripts/settings/ss_mapping");
const { SSErrorMessages } = require("@surescripts/settings/errors");
const { flatten } = require("flat");

module.exports = class SSDirectoryClass extends ModuleLogger {
    constructor(nes, ctx) {
        super("surescripts", "directory");
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
        this.helper = this.fx.getInstance(
            ctx,
            SSHelperClass,
            true,
            this.nes,
            this.ctx
        );
        this.service_level_cache = {};
    }

    async updateSite(siterec, id) {
        super.debug(`Attempting to update site record for: ${siterec.name}`);

        let hours = "";
        if (siterec.id) {
            const hoursSql = `SELECT get_formatted_site_hours(%L::integer) as hours;`;
            const hoursResult = await this.db.env.rw.parseSQLUsingPGP(hoursSql, [
                siterec.id,
            ]);
            if (hoursResult && hoursResult[0] && hoursResult[0].hours) {
                hours = hoursResult[0].hours;
            }
        }

        let org_map = { organization_hoop: hours };
        if (siterec.id) {
            const siteViewSql = `SELECT * FROM vw_surescripts_directory_site_info WHERE original_site_id = %L::integer;`;
            const viewResult = await this.db.env.rw.parseSQLUsingPGP(
                siteViewSql,
                [siterec.id]
            );
            if (viewResult && viewResult[0]) {
                const siteDetailsFromView = viewResult[0];
                org_map = {
                    ...org_map,
                    organization_servicelevels: siteDetailsFromView.organization_servicelevels,
                    organization_ncpdp_id: siteDetailsFromView.organization_ncpdp_id,
                    organization_npi: siteDetailsFromView.organization_npi,
                    organization_dea_number: siteDetailsFromView.organization_dea_number,
                    organization_id: siteDetailsFromView.organization_id,
                    organization_name: siteDetailsFromView.organization_name,
                    organization_address: siteDetailsFromView.organization_address,
                    organization_city: siteDetailsFromView.organization_city,
                    organization_state: siteDetailsFromView.organization_state,
                    organization_zipcode: siteDetailsFromView.organization_zipcode,
                    organization_phone: siteDetailsFromView.organization_phone,
                    organization_fax: siteDetailsFromView.organization_fax,
                    organization_type: siteDetailsFromView.organization_type_raw,
                    organization_specialties: siteDetailsFromView.organization_specialties,
                };
            } else {
                super.error(
                    `Could not fetch site details from vw_surescripts_directory_site_info for site ID: ${siterec.id}`
                );
                this.ctx.body = { 
                    error: "Failed to prepare site data for Surescripts directory.",
                };
                this.ctx.status = 500;
                return;
            }
        } else {
            super.error(
                `Site ID is missing, cannot fetch details for Surescripts directory.`
            );
            this.ctx.body = { error: "Site ID missing." };
            this.ctx.status = 400;
            return;
        }

        let result = null;

        super.log(
            `Attempting a save to validate against invalid data before sending to SureScripts`
        );
        const org_id = siterec.ss_organization_id || null;
        if (org_id) {
            result = await this.updateOrganization(org_map, org_id);
        } else {
            result = await this.createOrganization(org_map);
        }

        if (result && result?.response_json_data) {
            const flat_response = flatten(result.response_json_data);
            const new_org_id =
                flat_response[SSMapping.DIRECTORY_ELM_MAP.NEW_ORG_ID] ||
                flat_response[SSMapping.DIRECTORY_ELM_MAP.UPDATE_ORG_ID] ||
                null;
            const org_id_updated = new_org_id && new_org_id != org_id;
            if (org_id_updated) {
                siterec.ss_organization_id = new_org_id;
            } else if (!org_id && !new_org_id) {
                this.ctx.body = {
                    error: SSErrorMessages.DIRECTORY_ORG_NOT_FOUND,
                };
                this.ctx.status = 500;
                return;
            }
        } else if (result && result.error) {
            super.error(
                `Error updating site information in directory: ${siterec.name} Error:${result.error}`
            );
            throw new Error(result.error);
        } else {
            super.error(
                `Invalid response after updating site information in directory: ${siterec.name}`
            );
            throw new Error(SSErrorMessages.DIRECTORY_INVALID_RESPONSE);
        }
    }

    async updateOrganization(org_map, org_id) {
        const res = await this.nes.modules.fx.adminRequest(
            this.ctx,
            `surescripts/directory/organization/${org_id}`,
            {
                method: "PUT",
                data: org_map,
            }
        );
        return res;
    }

    async createOrganization(org_map) {
        const res = await this.nes.modules.fx.adminRequest(
            this.ctx,
            "surescripts/directory/organization",
            {
                method: "POST",
                data: org_map,
            }
        );
        return res;
    }

    async fetchServiceLevels(spi) {
        const service_level_cache = this.service_level_cache[spi];
        if (service_level_cache !== undefined) {
            return service_level_cache;
        }
        try {
            const provider_loc = await this.nes.modules.fx.adminRequest(
                this.ctx,
                `surescripts/directory/provider/live/${spi}`
            );
            const service_levels_elm =
                provider_loc?.response_json_data?.DirectoryMessage?.Body
                    ?.GetProviderLocationResponse
                    ?.GetProviderLocationResponseItem?.DirectoryInformation
                    ?.ServiceLevels?.ServiceLevel;
            if (service_levels_elm) {
                const service_levels = [];
                if (Array.isArray(service_levels_elm)) {
                    service_levels_elm.forEach((elm) => {
                        service_levels.push(elm.ServiceLevelName);
                    });
                } else if (
                    typeof service_levels_elm === "object" &&
                    service_levels_elm.ServiceLevelName
                ) {
                    service_levels.push(service_levels_elm.ServiceLevelName);
                }
                this.service_level_cache[spi] = service_levels;
                return service_levels;
            } else {
                super.error(
                    `${SSErrorMessages.DIRECTORY_SERVICE_LEVEL_ERR} :${spi}`
                );
                throw new Error(
                    "Unable to fetch service levels from admin server. Contact support if issues persist."
                );
            }
        } catch (err) {
            super.error(
                `${SSErrorMessages.DIRECTORY_SERVICE_LEVEL_ERR} ::${spi}`
            );
            throw err;
        }
    }
};
