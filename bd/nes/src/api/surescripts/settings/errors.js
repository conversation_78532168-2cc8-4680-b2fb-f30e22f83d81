"use strict";
const {
    MAX_DEA_REFILLS,
    MA<PERSON>_DS_3_5,
    <PERSON><PERSON>_<PERSON>_OTHER,
    REFIL<PERSON>_LEFT_ALLOWED,
    REFILLS_MAX_DAYS_OUT,
} = require("./settings");

const SSTransEC = {
    COMM_PROBLEM: "600",
    RECEIVER_UNABLE: "601",
    RECEIVER_SYS_ERR: "602",
    CONFIG_ERROR: "700",
    TRANSACTION_REJECTED: "900",
};

const SSDescriptionEC = {
    TIMEOUT: "008",
    COO_CARDHOLDER_LAST_NAME_INVALID: "103",
    QUANTITY_SUFFICIENT_INVALID: "134",
    REFILLS_INVALID: "144",
    UNABLE_TO_PROCESS: "210",
    TRANSACTION_DUPLICATE: "220",
    XML_SYNTAX_ERROR: "500",
    UNABLE_TO_IDENTIFY: "1000",
    DATA_FORMAT_INVALID: "2000",
    NCPDP_STANDARD_RULES_INVALID: "3000",
    UNABLE_TO_DELIVER: "4000",
    UNABLE_TO_PROCESS_RESPONSE: "4010",
    INTERMEDIARY_SYS_ERR: "4020",
    SENDER_NOT_ALLOWED: "4030",
    RECEIVER_NOT_SUPPORT: "4040",
};

const SSErrorMessages = {
    FU_STANDARD:
        "Please wait 48 hours before sending a subsequent follow-up message",
    FU_NEW: "Please wait 48 hours before sending a follow-up message",
    RR_PROHIBITED:
        "Prescription is prohibited for renewal, cannot process renewal request",
    RR_NOT_ACTIVE:
        "Patient is not active or pending, cannot process renewal request",
    RR_CANCELED: "Prescription is canceled, cannot process renewal request",
    RR_NO_MED_DISP:
        "Prescription has not been dispensed, or is not linked to the message, cannot process renewal request",
    RR_CS_NOT_ALLOWED:
        "Physician does not have controlled substance service level",
    RR_CS_REFILL_NOT_ALLOWED:
        "Refill is not allowed for level of controlled substance",
    RR_CS_SCHIII_IV_LIMIT: `Refills for Schedule III-IV controlled substance is limited to ${MAX_DEA_REFILLS} refills`,
    RR_MISSING_DIAGNOSIS: "Message is missing diagnosis, cannot proceed",
    RR_MISSING_REFILLS_REMAINING:
        "Message is missing refills remaining, cannot proceed",
    RR_SERVICE_LEVEL_ERR:
        "Physician does not support the refill service level, cannot proceed",
    RR_MISSING_WRITTEN_DATE:
        "Written date is missing from either the dispensed or prescribed information, cannot proceed",
    RR_START_DATE:
        "Cannot request a refill before the original start date or effective date",
    RR_NO_PHARMACY_RX_NO:
        "Cannot request a refill that if there is no associated order",
    RR_NO_DISPENSE:
        "Prescription has not been dispensed, cannot process refill request",
    RR_MULT_NDCS:
        "Multiple inventory items have been dispensed for the same prescription, cannot process refill request",
    RR_MISSING_INV:
        "Unable to locate dispensed inventory item. Cannot build dispense element of refill message",
    RR_MISSING_FDB:
        "Unable to locate FDB record for inventory item. Cannot build dispense element of refill message",
    RR_MISSING_REFILLS:
        "Unable to fetch remaining refills for this prescription, cannot continue",
    RR_TOO_SOON_REFILLS: `Requested refill is too soon. You have > ${REFILLS_LEFT_ALLOWED} refills left on the prescription`,
    RR_TOO_SOON_EXP: `Requested refill is too soon. You have more than ${REFILLS_MAX_DAYS_OUT} days out from prescription expiration date`,
    RR_TOO_SOON_WRITTEN: `Requested refill is too soon. You have more than ${REFILLS_MAX_DAYS_OUT} days out from the expiration date based on the written date`,
    CR_ALREADY_SUBMITTED:
        "Pending change request of the same or similar type already submitted for this prescription, wait for a response before resubmitting",
    CR_CS_INVALID_TYPE:
        "Controlled substances can only have the following change request types: Prior Authorization Required, Prescriber Authorization",
    CR_ACTIVE_ORDER:
        "Cannot request a change while an active order is associated with the prescription",
    CR_H_AND_J_NOT_ALLOWED:
        'Cannot have both "Prescriber must enroll/re-enroll with prescription benefit plan" and "Prescriber must enroll/re-enroll in REMS" types on the same change request',
    CR_SERVICE_LEVEL_ERR:
        "Physician does not support the change prescription service level, cannot proceed",
    CR_CANCELED: "Prescription is canceled, cannot process change request",
    CR_PA_APPROVED: "Prior Authorization is already approved",
    CR_PA_DENIED: "Prior Authorization is already denied",
    CR_PA_NOT_REQUIRED:
        "Prior Authorization is not required for this prescription",
    CR_PA_ALREADY_REQUESTED: "Prior Authorization has already been requested",
    CR_PA_ALREADY_EXISTING:
        "Prior Authorization number already on the prescription",
    CR_CS_NOT_ALLOWED:
        "Physician is not allowed to prescribe controlled substances or does not have that service level set in their EMR",
    FU_CANCELED: "Prescription is canceled, cannot process follow-up message",
    CS_MAX_OTHER_DAYS: `Maximum ${MAX_DS_OTHER} days of supply for controlled substances`,
    CS_MAX_3_5_DAYS: `Maximum ${MAX_DS_3_5} days of supply for controlled substances with an approval`,
    CNCL_APPROVAL_NOTE_REQUIRED:
        "Approval note is required if patient has received any fills from a canceled RX",
    CNCL_DENIAL_NOTE_REQUIRED:
        "Denial note or reason code is required if patient has received any fills from a canceled RX",
    OUTBOUND_MISSING_TO_FROM:
        "Missing To or From on the message header, cannot continue. Please contact support is issues persist.",
    DISP_EXPIRED: "Prescription is expired, cannot proceed",
    XML_MISSING_ELEMENTS: "XML is missing required elements, cannot proceed: ",
    INVALID_RX_NUMBER:
        "Prescriber Order Number not found in system: Message.Header.PrescriberOrderNumber",
    INVALID_RESPONSE_TYPE:
        "Response type invalid or unsupported on receiving system: Message.Body.RxChangeResponse.<ResponseType>",
    INVALID_SITE: "Site ID not found in system: Header.To",
    INVALID_MESSAGE_ID:
        "Message ID not found in system: Message.Header.RelatesToMessageID",
    SITE_MISSING_NCPDP_ID: "Site record is missing NCPDP ID, cannot proceed",
    SITE_MISSING_NPI: "Site record is missing NPI, cannot proceed",
    SAVE_ERROR:
        "Error saving inbound message. Please try again later or contact support at pharmacy",
    DIRECTORY_ORG_NOT_FOUND:
        "Organization element not found in directory response",
    DIRECTORY_SAVE_ERROR: "Error saving site record after directory update",
    DIRECTORY_INVALID_RESPONSE:
        "Invalid response after updating site information in directory",
    DIRECTORY_ERROR_PRESC_UPDATING:
        "Error updating site information in directory",
    DIRECTORY_SERVICE_LEVEL_ERR: "Unable to find service level for provider",
    DIRECTORY_ERROR_PRESC:
        "Error fetching physician from directory, cannot continue. Please try again or contact support if issue continues",
};

class SSError extends Error {
    constructor(data) {
        super(data?.desc || "");
        this.TransactionErrorCode = data.trans_code;
        this.DescriptionCode = data.desc_code;
        this.Description = data.description;
    }
}

module.exports = {
    SSError,
    SSErrorMessages,
    SSTransEC,
    SSDescriptionEC,
};
