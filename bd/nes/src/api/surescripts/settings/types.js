"use strict";
const SSType = {
    NEW: "NewRx",
    CANCEL: "CancelRx",
    CANCEL_RES: "CancelRxResponse",
    RENEW: "RxRenewalRequest",
    RENEW_RES: "RxRenewalResponse",
    CHANGE: "RxChangeRequest",
    CHANGE_RES: "RxChangeResponse",
    STATUS: "Status",
    VERIFY: "Verify",
    ERROR: "Error",
};
const SSSupportedInboundTypes = [
    SSType.NEW,
    SSType.CANCEL_RES,
    SSType.RENEW_RES,
    SSType.CHANGE_RES,
    SSType.CANCEL,
];

const SSServiceLevel = {
    CHANGE: "Change",
    RENEW: "Renew",
    REFILL: "Refill",
    CS: "ControlledSubstance",
};

const SSStatus = {
    APPROVE: "Approved",
    DENIED: "Denied",
    APPROVE_CHG: "ApprovedWithChanges",
    REPLACE: "Replace",
    VALIDATED: "Validated",
};

const SSAction = {
    CREATE: "create",
    RESEND: "resend",
    REFILL: "refill",
    FOLLOWUP: "followup",
    PA: "request_pa",
    CLARIFY: "request_clarification",
    CHANGE: "request_change",
    REQ_INFO: "request_provider_info",
    APV_CANCEL: "approve_cancel",
    DENY_CANCEL: "deny_cancel",
    MARK_REVIEWED: "mark_reviewed",
    SAVE_AND_SEND: "save_and_send",
};

const SSActionLabels = {
    [SSAction.CREATE]: "Create Records",
    [SSAction.RESEND]: "Resend",
    [SSAction.REFILL]: "Request Refill",
    [SSAction.FOLLOWUP]: "Request Followup",
    [SSAction.PA]: "Request PA",
    [SSAction.CLARIFY]: "Request Clarification",
    [SSAction.CHANGE]: "Request Change",
    [SSAction.REQ_INFO]: "Request Provider Info",
    [SSAction.APV_CANCEL]: "Approve Cancel",
    [SSAction.DENY_CANCEL]: "Deny Cancel",
    [SSAction.MARK_REVIEWED]: "Reviewed",
};

const SSIcon = {
    PRIORITY: "high_priority",
    NEW: "new",
    INFO: "info",
    DENIED: "denied",
    APPROVED: "approved",
    PENDING: "pending",
    SENT: "sent",
    ERROR: "error",
    CANCELED: "canceled",
    REPLIED: "replied",
    RESEND: "resend",
    NON_EPCS: "non_epcs",
    EPCS: "epcs",
};

const SSChangeType = {
    PA: "P",
    GENERIC_SUB: "G",
    THERAPY_SUB: "T",
    DUE_SUB: "D",
    OOS_SUB: "OS",
    CLARIFY: "S",
    PROV_INFO: "U",
};

const SSChangeSubType = {
    STATE_LIC: "A",
    DEA_LIC: "B",
    DEA_REG: "C",
    STATE_CS: "D",
    STATE_CS_REG: "E",
    NADEAN: "F",
    NPI: "G",
    BENEFIT_PLAN: "H",
    AUTHORITY: "I",
    REMS: "J",
    LOCK_IN_PROV: "K",
    SUPERVISOR: "L",
    CERTIFICATE: "M",
};

const SSPAStatus = {
    APPROVED: "A",
    DENIED: "D",
    DEFERRED: "F",
    NOT_REQUIRED: "N",
    REQUESTED: "R",
};

const SSDEASchedules = {
    SCHED_1: "C48672",
    SCHED_2: "C48675",
    SCHED_3: "C48676",
    SCHED_4: "C48677",
    SCHED_5: "C48679",
    SCHED_6: "C48680",
};

module.exports = {
    SSType,
    SSServiceLevel,
    SSStatus,
    SSAction,
    SSActionLabels,
    SSIcon,
    SSChangeType,
    SSChangeSubType,
    SSPAStatus,
    SSDEASchedules,
    SSSupportedInboundTypes,
};
