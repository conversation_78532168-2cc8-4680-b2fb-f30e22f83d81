"use strict";

const SSReceiverClass = require("@surescripts/receiver");
const { ModuleLogger } = require("@core/logger");

module.exports = class ApiView extends ModuleLogger {
    constructor(nes) {
        super("surescripts");
        this.auth = nes.modules.auth;
        this.nes = nes;
        this.nes = nes;
    }

    async process(ctx, urlpath) {
        try {
            this.log("Processing inbound message");
            const data = ctx.request.body;
            if (
                data.request_hash == undefined ||
                data.request_xml_data == undefined ||
                data.request_json_data == undefined
            ) {
                ctx.status = 400;
                ctx.body = {
                    error: "Invalid data. Inbound message must be in the format {request_hash:Message Hash, request_xml_data:XML String, request_json_data:XML Json Object",
                };
                return;
            }

            const meta_data = {
                hash: data.request_hash,
                xml_data: data.request_xml_data,
            };
            const receiver = new SSReceiverClass(this.nes, ctx, meta_data);
            await receiver.receive(data.request_json_data);
        } catch (e) {
            ctx.status = 503;
            const err_message = `Error processing inbound message: ${e.message} Stack:${e.stack}`;
            super.error(err_message);
            ctx.body = { error: err_message };
        }
    }
};
