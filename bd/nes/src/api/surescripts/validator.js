/*jshint : 6 */
"use strict";

const SSHelperClass = require("@surescripts/helper");

const {
    SSError,
    SSErrorMessages,
    SSTransEC,
    SSDescriptionEC,
} = require("@surescripts/settings/errors");
const {
    SSChangeResponseTypes,
    SSStatus,
    SSSupportedInboundTypes,
} = require("@surescripts/settings/types");
const { ModuleLogger } = require("@core/logger");

const SSDirectoryClass = require("@surescripts/directory");

module.exports = class SSValidatorClass extends ModuleLogger {
    constructor(nes, ctx) {
        super("surescripts");
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.nes = nes;
        this.ctx = ctx;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.helper = this.fx.getInstance(
            ctx,
            SSHelperClass,
            true,
            this.nes,
            this.ctx
        );
        this.directory = this.fx.getInstance(
            ctx,
            SSDirectoryClass,
            false,
            this.nes,
            this.ctx
        );
    }
    /**
     * Validates the refill request
     * @param {Object} ssrec - The ss_message record
     * @public
     */
    async validateRefillRequest(ssrec) {
        await this._runSqlValidation(ssrec, "refill"); 

        // Some high-level data presence checks can remain in JS if they are simple and quick,
        // or if they are prerequisites for even attempting SQL validation logic.
        // However, the primary business rule validation is now delegated to SQL.

        super.debug(`Refill Request: checking dispensed description...`);
        if (!ssrec.disp_description || ssrec.disp_description?.length === 0) {
            throw new Error(SSErrorMessages.RR_NO_MED_DISP);
        }

        super.debug(`Refill Request: checking for diagnosis...`);
        if (!ssrec.subform_diagnosis || ssrec.subform_diagnosis?.length === 0) {
            throw new Error(SSErrorMessages.RR_MISSING_DIAGNOSIS);
        }

        super.debug(
            `Refill Request: checking for refills remaining on dispense element...`
        );
        if (ssrec.refills_remaining === null) {
            throw new Error(SSErrorMessages.RR_MISSING_REFILLS_REMAINING);
        }

        super.debug(
            `Refill Request: checking for written date on prescription...`
        );
        if (!ssrec.written_date) {
            throw new Error(SSErrorMessages.RR_MISSING_WRITTEN_DATE);
        }
    }

    /**
     * Validates the change request
     * @param {Object} ssrec - The ss_message record
     * @public
     */
    async validateChangeRequest(ssrec) {
        await this._runSqlValidation(ssrec, "change");
    }

    /**
     * Validates the cancel response
     * @param {Object} ssrec - The ss_message record
     * @public
     */
    async validateCanceledResponse(ssrec) {
        const hadDispenseSql = `SELECT EXISTS (SELECT 1 FROM form_careplan_order_rx_disp disp WHERE disp.rx_no = %L AND disp.site_id = %s AND disp.status = 'Confirmed' AND disp.deleted IS NOT TRUE AND disp.archived IS NOT TRUE) AS had_dispense;`;
        let had_dispense = false;
        if (ssrec.pharmacy_rx_no && ssrec.site_id) {
            const dispenseResult = await this.db.env.rw.parseSQLUsingPGP(
                hadDispenseSql,
                [ssrec.pharmacy_rx_no, ssrec.site_id]
            );
            if (dispenseResult && dispenseResult[0]) {
                had_dispense = dispenseResult[0].had_dispense;
            }
        }

        super.debug(`Cancel Response: checking against existing dispenses...`);
        if (ssrec.cancel_status == SSStatus.APPROVE && had_dispense) {
            if (!(ssrec.cancel_note || ssrec.cancel_note?.length == 0)) {
                throw new Error(SSErrorMessages.CNCL_APPROVAL_NOTE_REQUIRED);
            }
        } else if (ssrec.cancel_status == SSStatus.DENIED && had_dispense) {
            if (
                (!ssrec.cancel_denied_reason ||
                    ssrec.cancel_denied_reason.length == 0) &&
                (!ssrec.cancel_denied_reason_code ||
                    ssrec.cancel_denied_reason_code.length == 0)
            ) {
                throw new Error(SSErrorMessages.CNCL_DENIAL_NOTE_REQUIRED);
            }
        }
    }

    /**
     * Validates the change response
     * Reject any entries with an invalid change response (i.e. DeniedNewPrescriptionToFollow from older versions)
     * @param {Object} xml_json - The xml_json object
     * @public
     */
    async validateChangeResponse(xml_json) {
        super.debug(`Change Response: checking against supported statuses...`);
        const response = await this.helper.fetchResponseStatus(xml_json);
        if (!SSChangeResponseTypes.includes(response)) {
            const desc_code = await this.helper.getErrorDesc(
                SSDescriptionEC.XML_SYNTAX_ERROR
            );
            throw new SSError({
                trans_code: SSTransEC.TRANSACTION_REJECTED,
                desc_code: desc_code,
                description: SSErrorMessages.INVALID_RESPONSE_TYPE,
            });
        }
    }

    async _runSqlValidation(ssrec, actionType) {
        const spi = SSSupportedInboundTypes.includes(ssrec.message_type)
            ? ssrec.from // For inbound, recipient is 'from' (original sender of request)
            : ssrec.to;  // For outbound, recipient is 'to'

        let recipientServiceLevels = [];
        if (spi) {
            try {
                recipientServiceLevels = await this.directory.fetchServiceLevels(spi);
            } catch (e) {
                super.error(
                    `Failed to fetch service levels for SPI ${spi}: ${e.message}`
                );
                // Decide if this is a hard error or if SQL can proceed with empty/null service levels
                throw new Error(SSErrorMessages.DIRECTORY_ERROR_PRESC); // Or a more specific error
            }
        }

        const validationSql = `SELECT fn_validate_ss_outbound_message_rules(%s::integer, %L::text, %L::TEXT[]) AS errors;`;
        const validationParams = [
            ssrec.id,
            actionType,
            this.fx.pgArrayFormat(recipientServiceLevels),
        ];
        const validationResult = await this.db.env.rw.parseSQLUsingPGP(
            validationSql,
            validationParams
        );

        if (
            validationResult &&
            validationResult[0] &&
            validationResult[0].errors &&
            validationResult[0].errors.length > 0
        ) {
            throw new Error(validationResult[0].errors.join(", "));
        }
    }
};
