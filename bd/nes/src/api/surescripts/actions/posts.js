"use strict";
const { <PERSON><PERSON>ctions<PERSON>and<PERSON> } = require("./index");
const { ActionResponseWrappers, NextAction, DisplayType } = require("@actions");
const { SSAction } = require("@surescripts/settings/types");
const SSSenderClass = require("@surescripts/sender");
const SSHelperClass = require("@surescripts/helper");

module.exports = class SurescriptsMessagePostActionsHandler extends (
    PostActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.auth = nes.modules.auth; // Assuming auth module is available
        this.sender = this.fx.getInstance(
            ctx,
            SSSenderClass,
            false,
            this.nes,
            this.ctx
        );
        this.helper = this.fx.getInstance(
            ctx,
            SSHelperClass,
            false,
            this.nes,
            this.ctx
        );
    }

    /**
     * Handles the posting of an action for a Surescripts message.
     * This typically involves saving the new outbound message, generating the Surescripts payload,
     * sending it, and then processing the synchronous response from the gateway.
     * @async
     * @param {string} id - The ID of the original Surescripts message this action relates to.
     * @param {string} action - The action being performed (e.g., SSAction.REFILL, SSAction.APV_CANCEL).
     * @param {Object} params - The data submitted by the user for the new outbound message.
     * @returns {Promise<Object>} The result of the action execution.
     */
    async postAction(id, action, params) {
        console.log(
            `Handling Surescripts message post action ${action} for Surescripts message id ${id}`
        );
        try {
            const data = this.ctx.request.body;

            switch (action) {
                case SSAction.CREATE:
                    return await this.__handleCreate(data, id);
                case SSAction.SAVE_AND_SEND:
                    return await this.__handleSaveAndSend(data, id);
                default:
                    return ActionResponseWrappers.error(
                        `Invalid post action for Surescripts message: ${action}`
                    );
            }
        } catch (e) {
            const errorMessage = `Error encountered while processing Surescripts message post action.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Handles creating a new Surescripts message from a form submission
     * @async
     * @param {Object} data - The form data submitted by the user
     * @param {string} id - The ID of the original Surescripts message
     * @returns {Promise<Object>} Action response wrapper with the result
     * @private
     */
    async __handleCreate(data, id) {
        console.log(`Handling create for Surescripts message id ${id}`);

        try {
            const formNames = data.slct_frm || [];
            if (formNames.length === 0) {
                return ActionResponseWrappers.error(
                    `No forms selected to create!`
                );
            }
            const ssRec = await this.fetcher.fetchCachedRecord(
                "ss_message",
                id
            );

            if (!ssRec) {
                return ActionResponseWrappers.error(
                    `Surescripts message not found for id ${id}`
                );
            }
            const transaction = this.db.env.rw.transaction(this.ctx);

            const updateFields = {};
            for (const formName of formNames) {
                const prefillData = await this.helper.getFormPrefill(
                    ssRec.id,
                    formName
                );
                const formId = await transaction.insert(formName, prefillData);
                if (formName === "patient") {
                    updateFields.patient_id = formId;
                } else if (formName === "careplan_order") {
                    updateFields.pharmacy_order_id = formId;
                }
            }

            if (Object.keys(updateFields).length > 0) {
                await transaction.update("ss_message", updateFields, {
                    id: ssRec.id,
                });
            }

            const res = await transaction.commit();
            if (res.error) {
                console.error(
                    `Error while saving generated records for Surescripts message id ${id}. Forms: ${formNames}`,
                    res.error
                );
                return ActionResponseWrappers.error(
                    `Error while saving generated records for Surescripts message id ${id}.`
                );
            }
            if (updateFields.patient_id) {
                const formIds = this.fx.fetchFormIdsFromTransactionResults(
                    "patient",
                    res
                );
                const patientId = formIds[0];
                return ActionResponseWrappers.edit(
                    patientId,
                    "patient",
                    {},
                    null,
                    null,
                    null,
                    null,
                    null,
                    DisplayType.TAB,
                    NextAction.REFRESH
                );
            } else if (updateFields.pharmacy_order_id) {
                const formIds = this.fx.fetchFormIdsFromTransactionResults(
                    "careplan_order",
                    res
                );
                const pharmacyOrderId = formIds[0];
                return ActionResponseWrappers.edit(
                    pharmacyOrderId,
                    "careplan_order",
                    {},
                    null,
                    null,
                    null,
                    null,
                    null,
                    DisplayType.TAB,
                    NextAction.REFRESH
                );
            }
            return ActionResponseWrappers.success(
                `Records created successfully`
            );
        } catch (e) {
            return ActionResponseWrappers.error(
                `Error encountered while processing Surescripts message create.`
            );
        }
    }

    /**
     * Handles saving and sending a Surescripts message from form data
     * @async
     * @param {Object} formData - The form data to save and send
     * @param {string} id - The ID of the original Surescripts message
     * @returns {Promise<Object>} Action response wrapper with the result
     * @private
     */
    async __handleSaveAndSend(formData, id) {
        console.log(`Handling save and send for Surescripts message id ${id}`);
        try {
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("ss_message", formData);
            const res = await transaction.commit();

            if (res.error) {
                console.error(
                    `Error inserting Surescripts response message for id ${id}.`,
                    res.error
                );
                return ActionResponseWrappers.error(
                    `Error encountered while saving Surescripts response message for id ${id}.`
                );
            }

            const formIds = this.fx.fetchFormIdsFromTransactionResults(
                "ss_message",
                res
            );
            const formId = formIds[0];
            if (!formId) {
                console.error(
                    `No form ID found for Surescripts response message for id ${id} after saving response record`
                );
                return ActionResponseWrappers.error(
                    `No form ID found for Surescripts response message for id ${id} after saving response record`
                );
            }
            const ssRec = await this.fetcher.fetchCachedRecord(
                "ss_message",
                formId
            );
            const result = await this.sender.send(ssRec, false);
            return result;
        } catch (e) {
            console.error(
                `Error encountered while processing Surescripts response for message id ${id}.`,
                e
            );
            return ActionResponseWrappers.error(
                `Error encountered while processing Surescripts response for message id ${id}.`
            );
        }
    }
};