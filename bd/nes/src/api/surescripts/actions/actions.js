"use strict";
const moment = require("moment-timezone");

const { RunActionsHandler } = require("./index");
const { ActionResponseWrappers, NextAction } = require("@actions");
const { SSAction } = require("@surescripts/settings/types");
const DispenseFetcherClass = require("@dispense/fetcher");
const SSSenderClass = require("@surescripts/sender");

module.exports = class SurescriptsMessagePerformActionHandler extends (
    RunActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.fetcher = this.fx.getInstance(
            ctx,
            DispenseFetcherClass,
            false,
            this.nes,
            this.ctx
        );
        this.sender = this.fx.getInstance(
            ctx,
            SSSenderClass,
            false,
            this.nes,
            this.ctx
        );
    }

    /**
     * Executes a specific action on a Surescripts message.
     * @async
     * @param {string} id - The ID of the Surescripts message.
     * @param {string} action - The action to be performed.
     * @returns {Promise<Object>} The result of the action execution.
     * @throws {Error} If there's an error during the action execution.
     */
    async runAction(id, action) {
        console.log(
            `Processing Surescripts message action ${action} for message ID ${id}`
        );

        const ssRec = await this.fetcher.fetchCachedRecord("ss_message", id);
        if (!ssRec) {
            return ActionResponseWrappers.error(
                `Surescripts message record not found. Cannot perform action ${action}.`
            );
        }

        try {
            // Get available actions from the SQL function and validate
            const availableActionsSql = `SELECT get_ss_message_available_actions(%s) AS available_actions;`;
            const actionResults = await this.db.env.rw.parseSQLUsingPGP(
                availableActionsSql,
                [id]
            );
            const sqlAvailableActions = actionResults[0]?.available_actions || [];

            if (!sqlAvailableActions.includes(action)) {
                return ActionResponseWrappers.error(
                    `Action ${action} is not available for this message.`
                );
            }

            switch (action) {
                case SSAction.CREATE: {
                    await this.__create(ssRec);
                    break;
                }
                case SSAction.RESEND: {
                    await this.__resend(ssRec);
                    break;
                }
                case SSAction.FOLLOWUP: {
                    await this.__followup(ssRec);
                    break;
                }
                case SSAction.MARK_REVIEWED: {
                    await this.__markReviewed(ssRec);
                    break;
                }
                case SSAction.REFILL:
                case SSAction.CLARIFY:
                case SSAction.CHANGE:
                case SSAction.REQ_INFO:
                case SSAction.PA: {
                    await this.__prefillAction(ssRec, action);
                    break;
                }
                case SSAction.APV_CANCEL:
                case SSAction.DENY_CANCEL: {
                    await this.__prefillActionAndSend(ssRec, action);
                    break;
                }
                default:
                    return ActionResponseWrappers.error(
                        `Unknown action: ${action}`
                    );
            }
        } catch (e) {
            const errorMessage = `Error processing Surescripts message action ${action} for message ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            return ActionResponseWrappers.error(
                `Error processing Surescripts message action ${action} for message ID ${id}`
            );
        }
    }

    /**
     * Creates a new Surescripts message form
     * @async
     * @param {Object} ssRec - The Surescripts message record
     * @returns {Promise<Object>} Action response wrapper with the form configuration
     * @private
     */
    async __create(ssRec) {
        console.log(`Creating message ${ssRec.id}`);

        try {
            const sql = `SELECT DISTINCT form_name FROM vw_ss_available_prefills WHERE ss_message_id = %s;`;
            const prefillForms = await this.db.env.rw.parseSQLUsingPGP(sql, [
                ssRec.id,
            ]);
            const prefillFormNames = prefillForms.map((form) => form.form_name);

            const callbackUrl = `/api/form/ss_message/${ssRec.id}/action/${SSAction.CREATE}`;
            return ActionResponseWrappers.popup(
                {
                    label: "Surescripts Form Generator",
                    form: "view_ss_message_create",
                    sections: ["Select Form(s)"],
                    preset: {
                        ss_message_id: ssRec.id,
                        fltr: prefillFormNames,
                    },
                    btnLabels: {
                        cancel: "Cancel",
                        save: "Generate",
                    },
                },
                callbackUrl
            );
        } catch (e) {
            console.error(`Error creating Surescripts message ${ssRec.id}`, e);
            return ActionResponseWrappers.error(e.message);
        }
    }

    /**
     * Resends a Surescripts message
     * @async
     * @param {Object} ssRec - The Surescripts message record
     * @returns {Promise<Object>} Action response wrapper with the resend result
     * @private
     */
    async __resend(ssRec) {
        console.log(`Resending message ${ssRec.id}`);

        try {
            const result = await this.sender.send(ssRec, false);
            return result;
        } catch (e) {
            console.error(`Error resending Surescripts message ${ssRec.id}`, e);
            return ActionResponseWrappers.error(e.message);
        }
    }

    /**
     * Requests a followup for a Surescripts message
     * @async
     * @param {Object} ssRec - The Surescripts message record
     * @returns {Promise<Object>} Action response wrapper with the followup result
     * @private
     */
    async __followup(ssRec) {
        console.log(`Requesting followup for message ${ssRec.id}`);

        try {
            const result = await this.sender.send(ssRec, true);
            return result;
        } catch (e) {
            console.error(
                `Error requesting followup for Surescripts message ${ssRec.id}`,
                e
            );
            return ActionResponseWrappers.error(e.message);
        }
    }

    /**
     * Prefills a Surescripts action
     * @async
     * @param {Object} ssRec - The Surescripts message record
     * @param {string} action - The action to prefill
     * @returns {Promise<Object>} Action response wrapper with the prefilled data
     * @private
     */
    async __prefillAction(ssRec, action) {
        console.log(`Requesting ${action} for message ${ssRec.id}`);

        try {
            const prefillData = await this.helper.getActionPrefill(
                ssRec.id,
                action
            );
            const callbackUrl = `/api/form/ss_message/${ssRec.id}/action/${SSAction.SAVE_AND_SEND}`;
            return ActionResponseWrappers.create(
                prefillData,
                "ss_message",
                callbackUrl
            );
        } catch (e) {
            console.error(
                `Error requesting ${action} for Surescripts message ${ssRec.id}`,
                e
            );
            return ActionResponseWrappers.error(e.message);
        }
    }

    /**
     * Prefills a Surescripts action and sends the message
     * @async
     * @param {Object} ssRec - The Surescripts message record
     * @param {string} action - The action to prefill and send
     * @returns {Promise<Object>} Action response wrapper with the result
     * @private
     */
    async __prefillActionAndSend(ssRec, action) {
        console.log(`Requesting ${action} for message ${ssRec.id}`);

        try {
            const prefillData = await this.helper.getActionPrefill(
                ssRec.id,
                action
            );
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("ss_message", prefillData);
            const res = await transaction.commit();

            if (res.error) {
                console.error(
                    `Error inserting Surescripts message ${ssRec.id} for ${action} from prefill data`,
                    res.error
                );
                const callbackUrl = `/api/form/ss_message/${ssRec.id}/action/${SSAction.SAVE_AND_SEND}`;
                return ActionResponseWrappers.create(
                    prefillData,
                    "ss_message",
                    callbackUrl
                );
            }

            const formIds = this.fx.fetchFormIdsFromTransactionResults(
                "ss_message",
                res
            );
            const formId = formIds[0];
            if (!formId) {
                console.error(
                    `No form ID found for Surescripts message ${ssRec.id} for ${action} from prefill data`
                );
                return ActionResponseWrappers.error(
                    `No form ID found for Surescripts after saving new response record`
                );
            }
            const newSsRec = await this.fetcher.fetchCachedRecord(
                "ss_message",
                formId
            );
            const result = await this.sender.send(newSsRec, false);
            return result;
        } catch (e) {
            console.error(
                `Error requesting ${action} for Surescripts message ${ssRec.id}`,
                e
            );
            return ActionResponseWrappers.error(e.message);
        }
    }

    /**
     * Marks a Surescripts message as reviewed
     * @async
     * @param {Object} ssRec - The Surescripts message record to mark as reviewed
     * @returns {Promise<Object>} The action response wrapper indicating success or failure
     * @private
     */
    async __markReviewed(ssRec) {
        console.log(`Marking reviewed for message ${ssRec.id}`);

        try {
            const transaction = this.db.env.rw.transaction(this.ctx);
            const reviewedOn = moment().format("MM/DD/YYYY HH:mm:ss");

            await transaction.update(
                "ss_message",
                {
                    reviewed_by: this.ctx.user.id,
                    reviewed_on: reviewedOn,
                },
                ssRec.id
            );
            const res = await transaction.commit();
            if (res.error) {
                console.error(
                    `Error encountered marking message as reviewed for Surescripts message ${ssRec.id}: ${res.error}`
                );
                return ActionResponseWrappers.error(res.error);
            }
            return ActionResponseWrappers.success(null, NextAction.CLOSE);
        } catch (e) {
            console.error(
                `Error marking reviewed for Surescripts message ${ssRec.id}`,
                e
            );
            return ActionResponseWrappers.error(e.message);
        }
    }
}

