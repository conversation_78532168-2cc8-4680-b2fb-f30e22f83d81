"use strict";
const { <PERSON>tons<PERSON>ctionsHandler } = require("./index");
const { 
    ButtonIcons,
    ButtonType,
    ButtonStyle,
    iconActionButtonWrapper,
    editIconActionButtonWrapper,
    CallbackType,
    ButtonActionRequirements,
} = require("@actions");
const { SSAction, SSActionLabels } = require("@surescripts/settings/types");

module.exports = class SurescriptsMessageButtonsHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
    }

    /**
     * Gets available actions for a Surescripts message.
     * @async
     * @param {string} id - The ID of the Surescripts message.
     * @returns {Promise<Object>} An object containing actions and warning.
     */
    async getActions(id) {
        console.debug(`Fetching actions for Surescripts message ID ${id}`);
        try {
            // Fetch the message record to pass to __mapActionsToButtons if needed for context
            const ssRec = await this.__fetchMessageRecord(id);
            if (!ssRec) {
                return { actions: [], warning: "Message not found" };
            }

            // Get available actions from the SQL function
            const availableActionsSql = `SELECT get_ss_message_available_actions(%s) AS available_actions;`;
            const actionResults = await this.db.env.rw.parseSQLUsingPGP(
                availableActionsSql,
                [id]
            );
            const actions = [];
            const sqlAvailableActions = actionResults[0]?.available_actions || [];
            for (const action of sqlAvailableActions) {
                switch (action) {
                    case SSAction.CREATE: {
                        const label = SSActionLabels[SSAction.CREATE];
                        const action = SSAction.CREATE;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.CREATE,
                                ButtonStyle.PRIMARY,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.CREATE}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.CREATE,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.PRIMARY,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.RESEND: {
                        const label = SSActionLabels[SSAction.RESEND];
                        const action = SSAction.RESEND;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.RESEND,
                                ButtonStyle.PRIMARY,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.RESEND}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.CANCEL,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.PRIMARY,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.REFILL: {
                        const label = SSActionLabels[SSAction.REFILL];
                        const action = SSAction.REFILL;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.REFILL,
                                ButtonStyle.PRIMARY,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.RESEND}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.REFILL,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.PRIMARY,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.FOLLOWUP: {
                        const label = SSActionLabels[SSAction.FOLLOWUP];
                        const action = SSAction.FOLLOWUP;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.FOLLOWUP,
                                ButtonStyle.PRIMARY,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.FOLLOWUP}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.FOLLOWUP,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.PRIMARY,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.PA: {
                        const label = SSActionLabels[SSAction.PA];
                        const action = SSAction.PA;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.PA,
                                ButtonStyle.DEFAULT,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.FOLLOWUP}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.PA,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.DEFAULT,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.CLARIFY: {
                        const label = SSActionLabels[SSAction.CLARIFY];
                        const action = SSAction.CLARIFY;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.PA,
                                ButtonStyle.DEFAULT,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.CLARIFY}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.PA,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.DEFAULT,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.CHANGE: {
                        const label = SSActionLabels[SSAction.CHANGE];
                        const action = SSAction.CHANGE;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.CHANGE,
                                ButtonStyle.DEFAULT,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.CHANGE}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.CHANGE,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.DEFAULT,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.REQ_INFO: {
                        const label = SSActionLabels[SSAction.REQ_INFO];
                        const action = SSAction.REQ_INFO;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.REQ_INFO,
                                ButtonStyle.INFO,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.REQ_INFO}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.REQ_INFO,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.INFO,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.DENY_CANCEL: {
                        const label = SSActionLabels[SSAction.DENY_CANCEL];
                        const action = SSAction.DENY_CANCEL;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.DENY_CANCEL,
                                ButtonStyle.ERROR,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.DENY_CANCEL}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.DENY_CANCEL,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.ERROR,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.APV_CANCEL: {
                        const label = SSActionLabels[SSAction.APV_CANCEL];
                        const action = SSAction.APV_CANCEL;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.APV_CANCEL,
                                ButtonStyle.ACCEPT,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.APV_CANCEL}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.APV_CANCEL,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.ACCEPT,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    case SSAction.MARK_REVIEWED: {
                        const label = SSActionLabels[SSAction.MARK_REVIEWED];
                        const action = SSAction.MARK_REVIEWED;
                        actions.push(
                            iconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.MARK_REVIEWED,
                                ButtonStyle.PRIMARY,
                                ButtonType.VIEW
                            )
                        );
                        const callbackUrl = `/api/form/ss_message/${id}?perform_action=${SSAction.MARK_REVIEWED}`;
                        actions.push(
                            editIconActionButtonWrapper(
                                label,
                                action,
                                ButtonIcons.MARK_REVIEWED,
                                callbackUrl,
                                CallbackType.GET,
                                ButtonStyle.PRIMARY,
                                ButtonActionRequirements.ALL_REQUIRED
                            )
                        );
                        break;
                    }
                    default:
                        console.warn(`Unknown action: ${action}`);
                        break;
                }
            }

            // Map actions to button configurations
            return { actions, warning: ssRec.warning || null }; 
        } catch (e) {
            const errorMessage = `Error fetching actions for Surescripts message ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};