"use strict";

const SSSenderClass = require("@surescripts/sender");
const { ModuleLogger } = require("@core/logger");
module.exports = class ApiView extends ModuleLogger {
    constructor(nes) {
        super("surescripts");
        this.nes = nes;
    }

    async processOutbound(ctx, urlpath) {
        try {
            const data = ctx.request.body;
            const params = Object.assign({}, ctx.query);
            const is_followup = params?.followup == "true" ? true : false;

            if (data == undefined) {
                ctx.status = 400;
                ctx.body = {
                    error: "Invalid data. Outbound message must be in an instance of ss_message",
                };
                return;
            }

            const sender = new SSSenderClass(this.nes, ctx);
            await sender.send(data, is_followup);
        } catch (e) {
            const err_message = `Error processing outbound message: ${e.message} Stack:${e.stack}`;
            super.error(err_message);
            ctx.status = 503;
            ctx.body = {
                error: "We encounter an occur while processing your outbound message. Please contact support if issues persist.",
            };
        }
    }
};
