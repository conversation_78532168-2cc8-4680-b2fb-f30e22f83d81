"use strict";

const SSHelperClass = require("@surescripts/helper");
const { ModuleLogger } = require("@core/logger");

module.exports = class SSReceiverClass extends ModuleLogger {
    constructor(nes, ctx, meta_data = null) {
        super("surescripts");
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.meta_data = meta_data; // meta_data - additional details from the admin server like xml_data and hash
        this.helper = this.fx.getInstance(
            ctx,
            SSHelperClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Converts the given data based on the direction specified during instantiation.
     *
     * @param {Object} data - The ss_message record or the inbound XML data (non-flat JSON object).
     * @public
     */
    async receive(data) {
        await this.__processInbound(data);
    }

    /**
     * Processes an inbound message by calling the main SQL parsing and insertion function.
     * This function now handles all inbound message types, including responses like Status, Verify, Error.
     * @param {Object} xml_json - The XML JSON object.
     * @private
     */
    async __processInbound(xml_json) {
        const message_type = await this.helper.fetchMessageType(xml_json);
        if (!message_type) {
            super.error(
                `Unable to determine message type from inbound XML: ${JSON.stringify(xml_json)}`
            );
            await this.helper.sendInboundErrorMsg(
                xml_json,
                new Error("Unable to determine message type.")
            );
            return;
        }

        super.debug(
            `Processing inbound ${message_type} message via SQL parse_ss_inbound`
        );
        const ssLogId = await this.helper.saveSsLog(
            xml_json,
            this.meta_data,
            message_type
        );

        if (!ssLogId) {
            this.ctx.status = 500; // Internal Server Error

            // Ensure a body is set if not already
            if (!this.ctx.body) {
                this.ctx.body = {
                    error: "Unable to save inbound message log.",
                };
            }
            // No explicit error to send back to Surescripts here, as we failed to log internally.
            // The HTTP status code should signal a server-side issue.
            return;
        }

        try {
            super.debug(
                `Calling SQL parse_ss_inbound for ${message_type}, Log ID: ${ssLogId}`
            );
            const sql = `
            SELECT ss_message_id, error_message, error_json_response FROM parse_ss_inbound(
                %s::integer
            )
            `;

            const sqlParams = [ssLogId];
            const ssParsingResults = await this.db.env.rw.parseSQLUsingPGP(
                sql,
                sqlParams
            );
            const resultsRow = ssParsingResults[0];
            const ssMessageId = resultsRow?.ss_message_id;
            const errorMessage = resultsRow?.error_message;
            const errorJsonResponse = resultsRow?.error_json_response; // This is the JSON to send back to Surescripts

            if (errorJsonResponse) {
                super.error(
                    `SQL function parse_ss_inbound returned an error JSON payload for ssLogId ${ssLogId}: ${JSON.stringify(errorJsonResponse)}`
                );
                this.ctx.status = 503; // Service Unavailable - as we are sending a Surescripts error back
                this.ctx.body = errorJsonResponse; // Send the Surescripts-formatted error back
                return;
            }

            if (!ssMessageId && errorMessage) {
                // This indicates an internal processing error in the SQL function that couldn't be formed into a Surescripts error.
                super.error(
                    `Error from SQL parse_ss_inbound for ssLogId ${ssLogId}: ${errorMessage}`
                );
                // Send a generic internal server error message back to Surescripts.
                await this.helper.sendInboundErrorMsg(
                    xml_json, // Original message for context if needed by sendInboundErrorMsg
                    new Error(errorMessage) // The internal error message
                );
                return;
            }

            if (!ssMessageId && !errorMessage && !errorJsonResponse) {
                // Should not happen if SQL function is correct, but as a safeguard.
                super.error(
                    `Unknown outcome from SQL parse_ss_inbound for ssLogId ${ssLogId}. No message ID, error message, or error JSON.`
                );
                await this.helper.sendInboundErrorMsg(
                    xml_json,
                    new Error("Unknown error processing message.")
                );
                return;
            }

            if (ssMessageId) {
                super.log(
                    `Successfully processed Surescripts message. Log ID: ${ssLogId}, New/Updated form_ss_message ID: ${ssMessageId}`
                );
            } else {
                // This case would typically be for Status/Verify/Error messages that don't create a new form_ss_message themselves,
                // but update an existing one or log an error. The SQL function handles this.
                super.log(
                    `Surescripts message processed (e.g., Status/Verify/Error update). Log ID: ${ssLogId}. No new form_ss_message created by this specific call, or an update was made.`
                );
            }
        } catch (error) {
            super.error(
                `Critical error processing inbound message type: ${message_type} ID: ${xml_json.Message?.Header?.MessageID} Error:${error.message} Stack:${error.stack}`
            );
            await this.helper.sendInboundErrorMsg(xml_json, error);
            return;
        }
        // If we reach here, the message was processed successfully by the SQL function,
        // and if it was an error/status/verify, it was handled. If it was a content message, it was inserted/updated.
        // A 202 Accepted is appropriate as we've taken the message and processed it asynchronously (from Surescripts' perspective).
        this.ctx.status = 202;
    }

};
