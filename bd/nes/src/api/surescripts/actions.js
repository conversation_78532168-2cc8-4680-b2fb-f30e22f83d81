"use strict";
const { ActionHandler } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const SurescriptsMessageButtonsHandler = require("@surescripts/actions/buttons");
            const getActionsHandler = new SurescriptsMessageButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error checking Surescripts message actions for message ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const SurescriptsMessagePerformActionHandler = require("@surescripts/actions/actions");
            const actionsHandler = new SurescriptsMessagePerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing Surescripts message action ${action} for message ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );

        try {
            const SurescriptsMessagePostActionsHandler = require("@surescripts/actions/posts");
            const postActionsHandler = new SurescriptsMessagePostActionsHandler(
                this.nes,
                ctx
            );
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing Surescripts message post action ${action} for message ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};