/*jshint : 6 */
"use strict";

const _ = require("lodash");
const moment = require("moment-timezone");

const {
    SSError,
    SSErrorMessages,
    SSTransEC,
    SSDescriptionEC,
} = require("@surescripts/settings/errors");

const { ModuleLogger } = require("@core/logger");
const {
    SSSupportedInboundTypes,
    SSType,
    SSStatus,
} = require("@surescripts/settings/types");
const { ActionResponseWrappers } = require("@actions");

module.exports = class SSHelperClass extends ModuleLogger {
    constructor(nes, ctx, logger_module = "surescripts") {
        super(logger_module);
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.nes = nes;
        this.ctx = ctx;
        this.form = nes.shared.form;
    }

    /**
     * Fetches the message type based on the inbound XML data
     * @param {Object} xml_json - The inbound XML data (non-flat JSON object)
     * @returns {String} The message type
     * @public
     */
    async fetchMessageType(xml_json) {
        // Check each supported inbound message type
        for (const messageType of SSSupportedInboundTypes) {
            if (xml_json?.Message?.Body[messageType]) {
                return messageType;
            }
        }
        return null;
    }

    /**
     * Saves the message log to the database.
     *
     * @param {Object} xml_json - The XML JSON object.
     * @param {Object} meta_data - The meta data object.
     * @param {string} message_type - The message type.
     * @param {string} direction - The direction.
     * @param {Object} error_data - The error data.
     * @public
     */
    async saveSsLog(
        xml_json,
        meta_data,
        message_type,
        direction = "inbound",
        error_data = null
    ) {
        const message_log = {
            direction: direction == "inbound" ? "IN" : "OUT",
            message_id: xml_json.Message?.Header?.MessageID,
            message_hash: meta_data?.response_hash || meta_data?.request_hash,
            message_xml:
                meta_data?.response_xml_data || meta_data?.request_xml_data,
            message_json: xml_json,
            message_type: message_type,
        };

        if (error_data) {
            message_log.surescripts_error_code = error_data.code;
            message_log.surescripts_error_desc_code = error_data.desc_code;
            message_log.error_description = error_data.description;
        }

        try {
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("ss_log", message_log);
            const res = await transaction.commit();
            if (res.error) {
                super.error(
                    `Error saving to Log: ${res.error.message} Stack:${res.error.stack}`
                );
                console.error(
                    `Failed to save to Surescripts log during error handling: ${res.error.message}`
                );
                return false;
            }
            const formIds = this.fx.fetchFormIdsFromTransactionResults(
                "ss_log",
                res
            );
            return formIds[0];
        } catch (error) {
            super.error(
                `Exception saving to Log: ${error.message} Stack:${error.stack}`
            );
            console.error(
                `Exception during Surescripts log save: ${error.message}`
            );
            return false;
        }
    }

    /**
     * Sends a system error.
     *
     * @param {Object} xml_json - The XML JSON object.
     * @param {Object} error - The error object.
     * @param {String} direction - The direction.
     * @param {number} status_code - The status code.
     * @public
     */
    async sendInboundErrorMsg(
        xml_json,
        error,
        direction = "inbound",
        status_code = 503
    ) {
        this.ctx.status = status_code;
        if (direction == "inbound" && status_code == 503) {
            let sError = error;
            if (!(error instanceof SSError)) {
                sError = new SSError({
                    trans_code: SSTransEC.RECEIVER_UNABLE,
                    description: error.message || SSErrorMessages.SAVE_ERROR,
                    desc_code: SSDescriptionEC.UNABLE_TO_PROCESS,
                });
            }

            console.error(sError.message);
            const error_msg_payload = await this.buildErrorResponse(
                xml_json,
                sError
            );
            this.ctx.body = { error: error_msg_payload };
            this.ctx.status = status_code;
        } else {
            this.ctx.body = ActionResponseWrappers.error(error.message);
            this.ctx.status = status_code;
        }
    }

    /**
     * Fetches the associated error description code
     * @param {string} code - The error code
     * @returns {Object} The error code object
     * @public
     */
    async getErrorCode(code) {
        if (!this.error_codes) {
            this.error_codes = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "list_ss_error_code"
            );
        }
        return _.find(this.error_codes, { code: code });
    }

    /**
     * Fetches the associated description
     * @param {string} code - The description
     * @returns {Object} The description object
     * @public
     */
    async getErrorDesc(code) {
        if (!this.desc_codes) {
            this.desc_codes = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "list_ss_error_desc"
            );
        }
        return _.find(this.desc_codes, { code: code });
    }

    /**
     * Fetches the response type based on the inbound XML data
     * @param {Object} xml_json - The inbound XML data (non-flat JSON object)
     * @returns {String} The response type
     * @public
     */
    async fetchResponseType(xml_json) {
        const SSSupportedResponseTypes = [
            SSType.STATUS,
            SSType.VERIFY,
            SSType.ERROR,
            SSType.CANCEL_RES,
            SSType.RENEW_RES,
            SSType.CHANGE_RES,
        ];
        for (const key of SSSupportedResponseTypes) {
            if (xml_json.Message?.Body[key]) {
                return key;
            }
        }
        return null;
    }

    /**
     * Fetches the response status based on the inbound XML data
     * @param {Object} xml_json - The inbound XML data (non-flat JSON object)
     * @returns {String} The response status
     * @public
     */
    async fetchResponseStatus(xml_json) {
        const SSChangeResponseTypesInner = [
            SSStatus.APPROVE,
            SSStatus.DENIED,
            SSStatus.APPROVE_CHG,
            SSStatus.REPLACE,
            SSStatus.VALIDATED,
        ];
        for (const key of SSChangeResponseTypesInner) {
            if (
                key in
                (xml_json.Message?.Body?.RxChangeResponse?.Response || {})
            ) {
                return key;
            }
            if (
                key in
                (xml_json.Message?.Body?.RxRenewalResponse?.Response || {})
            ) {
                return key;
            }
        }
        return null;
    }

    /**
     *
     * Builds the error response XML object
     * @param {Object} xml_json - The XML object
     * @param {Object} sserror - The ss_error object (should have TransactionErrorCode, DescriptionCode, Description)
     * @returns {Object} The error response object
     * @public
     */
    async buildErrorResponse(xml_json, sserror) {
        return {
            Message: {
                Header: {
                    RelatesToMessageID: xml_json.Message?.Header?.MessageID,
                    To: xml_json.Message?.Header?.From,
                    From: xml_json.Message?.Header?.To,
                    SentTime: moment().utc().format("YYYYMMDDHHmmss"),
                    PrescriberOrderNumber:
                        xml_json.Message?.Header?.PrescriberOrderNumber,
                },
                Body: {
                    Error: {
                        Code: sserror.TransactionErrorCode,
                        DescriptionCode: sserror.DescriptionCode,
                        Description: sserror.Description,
                    },
                },
            },
        };
    }

    /**
     * Gets the prefill data for a Surescripts action
     * @param {number} ssRecId - The ID of the Surescripts message record
     * @param {string} action - The action type (e.g. 'refill', 'pa', 'change', etc)
     * @returns {Object} The prefill data for the action
     * @throws {Error} If there is an error fetching the prefill data
     * @public
     */
    async getActionPrefill(ssRecId, action) {
        try {
            const sql = `SELECT get_ss_response_prefill(%s::integer, %L::text) as result;`;
            const prefillForms = await this.db.env.rw.parseSQLUsingPGP(sql, [
                ssRecId,
                action,
            ]);
            const result = prefillForms[0].result;
            return result;
        } catch (error) {
            super.error(
                `Error fetching action prefill for ssRecId: ${ssRecId} and action: ${action} Error:${error.message} Stack:${error.stack}`
            );
            return ActionResponseWrappers.error(
                `Error fetching action prefill for message ID: ${ssRecId} and action: ${action}`
            );
        }
    }

    /**
     * Gets the prefill data for a specific form from a Surescripts message
     * @param {number} ssRecId - The ID of the Surescripts message record
     * @param {string} formName - The name of the form to prefill
     * @returns {Object} The prefill data for the form
     * @throws {Error} If there is an error fetching the prefill data
     * @public
     */
    async getFormPrefill(ssRecId, formName) {
        try {
            const prefillForms = await this.db.env.rw.parseSQLUsingPGP(
                `SELECT prefill_data FROM vw_ss_available_prefills WHERE ss_message_id::integer = %s AND form_name = %L::text;`,
                [ssRecId, formName]
            );
            const result = prefillForms[0].prefill_data;
            return result;
        } catch (error) {
            super.error(
                `Error fetching form prefill for ssrecId: ${ssRecId} and formName: ${formName} Error:${error.message} Stack:${error.stack}`
            );
            return ActionResponseWrappers.error(
                `Error fetching form prefill for message ID: ${ssRecId} and formName: ${formName}`
            );
        }
    }
};
