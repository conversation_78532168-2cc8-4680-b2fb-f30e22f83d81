"use strict";
const _ = require("lodash");

const SSHelperClass = require("@surescripts/helper");
const SSValidatorClass = require("@surescripts/validator");
const SSDirectoryClass = require("@surescripts/directory");
const SiteClass = require("@operations/site");

const { SSErrorMessages } = require("@surescripts/settings/errors");
const { SSType } = require("@surescripts/settings/types");
const { ModuleLogger } = require("@core/logger");
const { ActionResponseWrappers, NextAction } = require("@actions");

module.exports = class SSSenderClass extends ModuleLogger {
    constructor(nes, ctx) {
        super("surescripts");
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.directory = this.fx.getInstance(
            ctx,
            SSDirectoryClass,
            false,
            this.nes,
            this.ctx
        );
        this.helper = this.fx.getInstance(
            ctx,
            SSHelperClass,
            true,
            this.nes,
            this.ctx
        );
        this.validator = this.fx.getInstance(
            ctx,
            SSValidatorClass,
            false,
            this.nes,
            this.ctx
        );
    }

    /**
     * Converts the given data based on the direction specified during instantiation.
     *
     * @param {Object} ssRec - The ss_message record or the inbound XML data (non-flat JSON object).
     * @param {boolean} [followup=false] - Indicates if this is a followup message.
     * @public
     */
    async send(ssRec, followup = false) {
        super.debug(
            `Processing ${ssRec.message_type} message to ${ssRec.prescriber_name_display} for ${ssRec.patient_name_display}`
        );

        try {
            super.debug(`Running validation checks on ${ssRec.message_type}`);
            await this.__validateOutbound(ssRec, followup);
        } catch (error) {
            super.log(`Validation error for ${ssRec.message_type}: ${error}`);
            return ActionResponseWrappers.error(
                `Validation error: ${error.message}. Cannot send message until issue is addressed.`
            );
        }

        let xmlJson = null;
        try {
            super.debug(`Running conversion for ${ssRec.message_type}`);
            const sql = `
            SELECT generate_ss_outbound_payload(
                %s::integer,
                %L::boolean
            ) as result
            `;

            const sqlParams = [ssRec.id, followup];
            const ssParsingResults = await this.db.env.rw.parseSQLUsingPGP(
                sql,
                sqlParams
            );
            const resultsRow = ssParsingResults[0];
            xmlJson = resultsRow && resultsRow.result;
            const errorMessage = xmlJson && xmlJson.error;
            if (!xmlJson || errorMessage) {
                super.error(
                    errorMessage ||
                        `Error generating outbound message for ${ssRec.message_type} for message ID:${ssRec.id}`
                );
                return ActionResponseWrappers.error(
                    "Error generating outbound message. Please contact support if issues persist."
                );
            }
        } catch (error) {
            const errMessage = `Error converting record type: ${ssRec.message_type} Stack:${error.stack}`;
            super.error(errMessage);
            return ActionResponseWrappers.error(
                "Error generating outbound message. Please contact support if issues persist."
            );
        }

        const sendAttemptOutcomeSsRec = ssRec;
        const transaction = this.db.env.rw.transaction(this.ctx);

        try {
            super.debug(`Attempting to send message ${ssRec.message_type}`);
            const trySendErrorWrapper = await this.__trySendMessage(
                sendAttemptOutcomeSsRec,
                xmlJson,
                followup,
                transaction
            );

            if (trySendErrorWrapper) {
                return trySendErrorWrapper;
            }

            if (followup) {
                await this.__saveFollowup(
                    sendAttemptOutcomeSsRec,
                    xmlJson,
                    null,
                    transaction,
                    true
                );
            } else {
                await this.__postProcessOutbound(
                    sendAttemptOutcomeSsRec,
                    transaction
                );
            }

            const res = await transaction.commit();
            if (res.error) {
                const errMessage = `Error committing sent Surescripts message for ${ssRec.message_type} ID ${ssRec.id}: ${res.error}`;
                super.error(errMessage);
                return ActionResponseWrappers.error(
                    "Error saving message. Message may have been sent but not saved in DB, please contact the physician to verify receipt."
                );
            }

            if (sendAttemptOutcomeSsRec.message_status === "Error") {
                return ActionResponseWrappers.error(
                    sendAttemptOutcomeSsRec.error_description ||
                        "Message send resulted in an error."
                );
            }
            return ActionResponseWrappers.success(
                "Message has been sent!",
                NextAction.REFRESH_VIEW
            );
        } catch (error) {
            const errMessage = `Error during send/finalization for ${ssRec.message_type} ID ${ssRec.id}: ${error.message} Stack:${error.stack}`;
            super.error(errMessage);
            return ActionResponseWrappers.error(
                `Exception processing message: ${error.message}`
            );
        }
    }

    /**
     * Attempts to validate outbound messages before sending to the admin server.
     * @function validateOutbound
     * @param {Object} ssRec - The inbound ss_message.
     * @param {boolean} followup - True if a follow-up only request.
     * @private
     */
    async __validateOutbound(ssRec, followup) {
        if (followup) {
            const followupCheckSql =
                "SELECT fn_check_followup_eligibility(%s::integer) AS errors;";
            try {
                const validationResult = await this.db.env.rw.parseSQLUsingPGP(
                    followupCheckSql,
                    [ssRec.id]
                );
                if (
                    validationResult &&
                    validationResult[0] &&
                    validationResult[0].errors &&
                    validationResult[0].errors.length > 0
                ) {
                    throw new Error(validationResult[0].errors.join(", "));
                }
            } catch (e) {
                super.error(
                    `Followup validation failed for message ID ${ssRec.id}: ${e.message}`
                );
                throw new Error(
                    e.message && e.message.includes("followup")
                        ? e.message
                        : SSErrorMessages.FU_STANDARD
                );
            }
            return;
        }

        if (!ssRec.send_to || !ssRec.sent_from) {
            throw new Error(SSErrorMessages.OUTBOUND_MISSING_TO_FROM);
        }

        // For non-followup, call the main rule validator
        const generalValidationSql =
            "SELECT fn_validate_ss_outbound_message_rules(%s::integer, %L::text, %L::TEXT[]) AS errors;";
        let recipientServiceLevels = [];
        if (ssRec.send_to) {
            // Assuming ssRec.send_to is the recipient SPI for outbound
            try {
                recipientServiceLevels = await this.directory.fetchServiceLevels(ssRec.send_to);
            } catch (e) {
                const errMessage = `Failed to fetch service levels for recipient SPI ${ssRec.send_to}: ${e.message}`;
                super.error(errMessage);
                throw new Error(SSErrorMessages.DIRECTORY_ERROR_PRESC);
            }
        }
        const validationParams = [
            ssRec.id,
            ssRec.message_type, // This should be the type of the message *being sent*
            this.fx.pgArrayFormat(recipientServiceLevels),
        ];
        const validationResultRules = await this.db.env.rw.parseSQLUsingPGP(
            generalValidationSql,
            validationParams
        );
        if (
            validationResultRules &&
            validationResultRules[0] &&
            validationResultRules[0].errors &&
            validationResultRules[0].errors.length > 0
        ) {
            throw new Error(validationResultRules[0].errors.join(", "));
        }

        // Specific JS validator calls (if any remain for very specific structural things not in SQL)
        switch (ssRec.message_type) {
            case SSType.CANCEL_RES:
                await this.validator.validateCanceledResponse(ssRec);
                break;
            case SSType.RENEW:
                await this.validator.validateRefillRequest(ssRec);
                break;
            case SSType.CHANGE:
                await this.validator.validateChangeRequest(ssRec);
                break;
            default:
                break;
        }
    }

    /**
     * Attempts to send the message to the admin server and handle the response.
     *
     * @param {Object} ssRec - The ss_message record for the outbound message.
     * @param {Object} xmlJson - The ss message object in JSON format (the payload to send).
     * @param {boolean} followup - True if a follow-up only request.
     * @param {Object} transaction - The transaction object.
     * @private
     */
    async __trySendMessage(ssRec, xmlJson, followup, transaction) {
        const result = await this.fx.adminRequest(
            this.ctx,
            "surescripts/tx/send",
            {
                method: "POST",
                data: {
                    message_type: ssRec.message_type,
                    Message: xmlJson.Message,
                    override_spi:
                        ssRec.chg_type_id === "P" ? "0000000000000" : null,
                },
            }
        );

        if (
            this.ctx.is_test &&
            this.ctx &&
            this.ctx.admin_mocks &&
            this.ctx.admin_mocks.length > 0
        ) {
            result.request_json_data = xmlJson;
        }

        if (result.error) {
            super.error(
                `Error sending outbound message via adminRequest: ${result.error} For SSREC ID:${ssRec.id}`
            );
            await this.helper.saveSsLog(
                xmlJson,
                { request_hash: null, request_xml_data: null },
                ssRec.message_type,
                "outbound",
                { code: "NES_SEND_FAIL", description: result.error }
            );
            ssRec.message_status = "Error";
            ssRec.error_description = `Admin Send Error: ${result.error}`;
            ssRec.status_icons = _.uniq([
                "error",
                ...(ssRec.status_icons || []),
            ]);
            return null; // Indicates an error occurred, but it's handled by setting ssRec status
        }

        const requiredResponseFields = [
            "request_message_id",
            "response_json_data",
        ];
        if (requiredResponseFields.some((prop) => !result[prop])) {
            const errorDetail = `Invalid admin response structure. Response: ${JSON.stringify(
                result
            )}`;
            super.error(errorDetail + ` For SSREC ID:${ssRec.id}`);
            await this.helper.saveSsLog(
                xmlJson,
                { request_hash: null, request_xml_data: null },
                ssRec.message_type,
                "outbound",
                {
                    code: "NES_ADMIN_RESPONSE_INVALID",
                    description: errorDetail,
                }
            );
            ssRec.message_status = "Error";
            ssRec.error_description = "Invalid Admin Response";
            ssRec.status_icons = _.uniq([
                "error",
                ...(ssRec.status_icons || []),
            ]);
            return null; // Indicates an error occurred
        }

        const surescriptsAssignedMessageId = result.request_message_id;
        if (ssRec.message_id !== surescriptsAssignedMessageId) {
            super.log(
                `Updating Surescripts assigned MessageID from ${ssRec.message_id} to ${surescriptsAssignedMessageId} for record ${ssRec.id}`
            );
            ssRec.message_id = surescriptsAssignedMessageId; // Update in-memory record
        }

        super.debug(
            `Message successfully sent to Surescripts. Assigned MessageID ${surescriptsAssignedMessageId}. Processing synchronous Surescripts reply.`
        );

        const surescriptsSyncReplyJson = result.response_json_data;
        const syncReplyType = await this.helper.fetchMessageType(
            surescriptsSyncReplyJson
        );

        // Log the synchronous reply from Surescripts
        const syncReplyLogId = await this.helper.saveSsLog(
            surescriptsSyncReplyJson,
            result, // Contains response_xml_data, response_hash from admin server
            syncReplyType,
            "inbound" // Treat Surescripts' synchronous reply as an inbound event for logging
        );

        if (!syncReplyLogId) {
            super.error(
                `CRITICAL: Failed to log Surescripts synchronous reply for sent message ${surescriptsAssignedMessageId}. Reply was: ${JSON.stringify(
                    surescriptsSyncReplyJson
                )}`
            );
            ssRec.message_status = "Error";
            ssRec.error_description =
                "Failed to log synchronous Surescripts reply.";
            ssRec.status_icons = _.uniq([
                "error",
                ...(ssRec.status_icons || []),
            ]);
            // No transaction update here as we need to commit the original send attempt's log first, or handle this error state more gracefully.
            return null; // Error state
        }

        // Process the synchronous reply using the main inbound SQL parser
        // This will handle Status, Verify, or Error replies from Surescripts
        const parsingResults = await this.db.env.rw.parseSQLUsingPGP(
            "SELECT ss_message_id, error_message, error_json_response FROM parse_ss_inbound(%s::integer);",
            [syncReplyLogId]
        );
        const parsingRow = parsingResults[0];

        if (parsingRow && parsingRow.error_json_response) {
            // This means parse_ss_inbound itself had an issue processing Surescripts' Error reply (e.g., malformed Surescripts Error)
            // or decided to generate an error to send back *if* Surescripts sent an Error and our SQL handler for Error messages had a problem.
            // For sender context, we mostly care about our original message's status.
            super.error(
                `parse_ss_inbound generated an error response while processing a Surescripts synchronous reply (Log ID: ${syncReplyLogId}). This is unexpected for typical Status/Verify. Original sent message ID: ${ssRec.id}`
            );
            // The original ssRec status might already be set if the sync reply was an Error message.
            // If parse_ss_inbound had to generate an error (e.g. T500), it means our system had an issue with *their* error.
            // We don't send this error_json_response back from the sender flow.
            ssRec.message_status =
                ssRec.message_status === "Error"
                    ? ssRec.message_status
                    : "Error"; // Keep existing error or mark as error
            ssRec.error_description =
                ssRec.error_description ||
                "Error processing Surescripts synchronous reply. Details in ss_error_log.";
            ssRec.status_icons = _.uniq([
                "error",
                ...(ssRec.status_icons || []),
            ]);
        } else if (parsingRow && parsingRow.error_message) {
            // This implies an internal SQL error during processing of the sync reply.
            super.error(
                `Internal error processing Surescripts synchronous reply (Log ID: ${syncReplyLogId}) for original message ${ssRec.id}: ${parsingRow.error_message}`
            );
            ssRec.message_status = "Error";
            ssRec.error_description = `Internal error processing sync reply: ${parsingRow.error_message}`;
            ssRec.status_icons = _.uniq([
                "error",
                ...(ssRec.status_icons || []),
            ]);
        } else {
            // If parse_ss_inbound ran successfully (no error_message or error_json_response),
            // it means the Status, Verify, or Error from Surescripts was processed and updated the DB.
            // We need to reload ssRec to get its potentially updated status by parse_ss_inbound.
            const updatedSsRec = await this.db.get_form(
                this.ctx,
                "ss_message",
                ssRec.id
            );
            if (updatedSsRec) {
                Object.assign(ssRec, updatedSsRec); // Update in-memory ssRec with latest from DB
                super.log(
                    `Synchronous Surescripts reply (Type: ${syncReplyType}) processed. Message ID ${ssRec.id} status is now: ${ssRec.message_status}`
                );
            } else {
                super.error(
                    `Failed to reload ss_message ID ${ssRec.id} after processing synchronous Surescripts reply.`
                );
                ssRec.message_status = "Error";
                ssRec.error_description =
                    "Failed to reload message after sync reply.";
                ssRec.status_icons = _.uniq([
                    "error",
                    ...(ssRec.status_icons || []),
                ]);
            }
        }
        // Ensure the current transaction updates the original ssRec with its latest state based on the sync reply processing.
        // The actual values of message_status, error_description, status_icons in ssRec
        // should now reflect the outcome of processing the Surescripts synchronous reply.
        await transaction.update(
            "ss_message",
            {
                message_id: ssRec.message_id, // Ensure the Surescripts-assigned MessageID is saved
                message_status: ssRec.message_status,
                error_description: ssRec.error_description,
                error_code_id: ssRec.error_code_id, // these should be updated by parse_ss_inbound if applicable
                error_desc_code_id: ssRec.error_desc_code_id,
                surescripts_error_code: ssRec.surescripts_error_code,
                surescripts_error_desc_code: ssRec.surescripts_error_desc_code,
                status_icons: ssRec.status_icons,
            },
            ssRec.id
        );
        return null; // Success or error state of original message is now in ssRec
    }

    /**
     * Performs post-processing actions on an outbound message after it has been successfully processed.
     *
     * @param {Object} ssRec - The ss_message record (the outbound message that was just sent and updated by __handleSentResponse).
     * @param {Object} transaction - The transaction object.
     * @private
     */
    async __postProcessOutbound(ssRec, transaction) {
        const site = await new SiteClass(
            this.nes,
            this.ctx,
            ssRec.site_id
        ).init();

        const updateData = {
            message_id: ssRec.message_id,
            processed: "Yes",
            processed_dt: site.localtimestamp,
            message_status: ssRec.message_status,
            status_icons: ssRec.status_icons,
            error_code_id: ssRec.error_code_id,
            error_desc_code_id: ssRec.error_desc_code_id,
            error_description: ssRec.error_description,
        };

        super.log(
            `Finalizing outbound message ID ${ssRec.id} (Surescripts MessageID: ${updateData.message_id}) with status: ${updateData.message_status}`
        );
        if (ssRec.id) {
            try {
                await transaction.update("ss_message", updateData, ssRec.id);
            } catch (error) {
                const errMessage = `Error updating outbound message ID ${ssRec.id}: ${error.message}`;
                super.error(errMessage);
                return ActionResponseWrappers.error(errMessage);
            }
        } else {
            // This case should ideally not happen if ssRec is from DB or was just inserted by caller
            const errMessage = "Unable to update outbound message. Message was already sent!";
            super.error(errMessage);
            return ActionResponseWrappers.error(errMessage);
        }
    }

    /**
     * Saves follow-up information for an outbound message after it has been sent.
     *
     * @param {Object} originalSsRec - The original ss_message record for which followup was sent.
     * @param {Object} sentResultXmlJson - The result data from sending the followup message (contains new MessageID and sync response).
     * @param {string} followupResponseType - The type of the synchronous Surescripts reply to the followup.
     * @param {Object} transaction - The transaction object.
     * @param {boolean} [isFinalizingStep=false] - Indicates if this is the finalizing step.
     * @private
     */
    async __saveFollowup(
        originalSsRec,
        sentResultXmlJson,
        followupResponseType,
        transaction,
        isFinalizingStep = false
    ) {
        if (!transaction && isFinalizingStep) {
            super.error(
                "__saveFollowup in finalizing step called without a transaction. This is unexpected."
            );
            return;
        }

        let followupMessageStatus = "Sent";
        const followupErrorData = {
            code: null,
            desc_code: null,
            description: null,
        };

        const sentFollowupMessageId =
            (sentResultXmlJson &&
                sentResultXmlJson.Message &&
                sentResultXmlJson.Message.Header &&
                sentResultXmlJson.Message.Header.MessageID) ||
            originalSsRec.message_id +
                "-F" +
                ((originalSsRec.followup_count || 0) + 1);

        if (followupResponseType === SSType.ERROR) {
            const errorBody =
                sentResultXmlJson &&
                sentResultXmlJson.Message &&
                sentResultXmlJson.Message.Body &&
                sentResultXmlJson.Message.Body.Error;
            if (errorBody) {
                followupMessageStatus = "Error";
                followupErrorData.code = errorBody.Code;
                followupErrorData.desc_code = errorBody.DescriptionCode;
                followupErrorData.description = errorBody.Description;
            }
        } else if (
            followupResponseType === SSType.VERIFY ||
            (followupResponseType === SSType.STATUS &&
                sentResultXmlJson &&
                sentResultXmlJson.Message &&
                sentResultXmlJson.Message.Body &&
                sentResultXmlJson.Message.Body.Status &&
                sentResultXmlJson.Message.Body.Status.Code === "010")
        ) {
            followupMessageStatus = "Verified";
        }

        try {
            const followupSql =
                "SELECT prepare_surescripts_followup(%s::integer, %L::text, %L::text, %L::text, %L::text, %L::text);";
            await this.db.env.rw.parseSQLUsingPGP(followupSql, [
                originalSsRec.id,
                sentFollowupMessageId,
                followupMessageStatus,
                followupErrorData.code,
                followupErrorData.desc_code,
                followupErrorData.description,
            ]);
        } catch (error) {
            const errMessage = `Error updating followup details for original message ID ${originalSsRec.id}: ${error.message}`;
            super.error(errMessage);
            return ActionResponseWrappers.error(errMessage);
        }
    }
};
