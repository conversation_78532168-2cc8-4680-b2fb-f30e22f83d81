/*jshint : 6 */
"use strict";

const { ModuleLogger } = require("@core/logger");
const { SSErrorMessages } = require("@surescripts/settings/errors");

const SSDirectoryClass = require("@surescripts/directory");

module.exports = class SSCheckerClass extends ModuleLogger {
    constructor(nes, ctx, options = null) {
        super("surescripts");
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        this.options = options;
        this.form = this.nes.shared.form;
        this.shared = nes.shared;
        this.directory = this.fx.getInstance(
            ctx,
            SSDirectoryClass,
            false,
            this.nes,
            this.ctx
        );
    }

    /**
     * Validates the service level for a given SPI.
     * This might still be called directly from other parts of the JS codebase.
     * @param {String} serviceLevel - The service level to check.
     * @param {String} spi - The provider/pharmacy SPI.
     * @public
     */
    async checkServiceLevel(serviceLevel, spi) {
        if (!spi) {
            super.warn("checkServiceLevel called with no SPI.");
            return false; // Or throw error, depending on strictness required
        }
        try {
            const serviceLevels = await this.directory.fetchServiceLevels(spi);
            return serviceLevels.includes(serviceLevel);
        } catch (e) {
            super.error(
                `Error in checkServiceLevel for SPI ${spi}, Level ${serviceLevel}: ${e.message}`
            );
            throw new Error(SSErrorMessages.DIRECTORY_ERROR_PRESC); // Propagate a user-friendly error
        }
    }

};
