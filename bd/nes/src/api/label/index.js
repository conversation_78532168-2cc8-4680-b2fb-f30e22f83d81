"use strict";

// const { Shippo, DistanceUnitEnum, WeightUnitEnum } = require('shippo');
// const moment = require("moment");

const _ = require("lodash");
const moment = require("moment");

module.exports = class ShippoLabelService {
    constructor(nes) {
        // this.shippo = new Shippo({ apiKeyHeader: process.env.SHIPPO_API_TOKEN });
        this.shippo = {};
        this.carrierId = process.env.CARRIER_ID || null;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.label = nes.shared.label;
        this.exceptions = nes.shared.exceptions;
        this.fx = nes.modules.fx;
        this.nes = nes;
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const method = ctx.request.method.toLowerCase();
            if (!user || !this.auth.can_access_form(ctx, "patient")) {
                throw this.exceptions.UnauthorizedException;
            }
            if (!urlpath.path[2]) {
                throw this.exceptions.BadRequestException;
            }

            if (method === "post") {
                if (this.carrierId) {
                    // Create label directly with the carrier ID from the environment.
                    // const label = await this.createLabel(
                    //     shipment,
                    //     this.carrierId
                    // );
                    ctx.body = {
                        // label,
                        message: "Label created successfully.",
                    };
                } else {
                    // check if the we need to get rates or create label
                    const body = ctx.request.body;
                    if (!body.parcel) {
                        throw this.exceptions.BadRequestException;
                    }
                    if (body.rate && body.rate.trim() !== "") {
                        // Create label with the selected rate

                        const label = await this.label.purchaseShipmentLabel(
                            body.rate,
                            body.metadata
                        );
                        if (label.status === "SUCCESS") {
                            if (label.label_url) {
                                const hashObject =
                                    await this.downloadAndUploadLabel(
                                        label.label_url
                                    );
                                if (hashObject) {
                                    await this.updateCareplanDeliveryTick(
                                        ctx,
                                        urlpath.path[2],
                                        hashObject,
                                        label,
                                        body
                                    );
                                }
                            }
                            await this.saveShipmentData(
                                ctx,
                                urlpath.path[2],
                                label
                            );
                            ctx.body = {
                                label,
                                status: "success",
                                message: "Label created successfully.",
                            };
                        } else {
                            ctx.status = 400;
                            ctx.body = {
                                status: "error",
                                message: "Error creating label.",
                                error: label.messages[0].text,
                            };
                        }
                    } else {
                        const shipment = await this.label.getShipmentRates(
                            ctx,
                            urlpath.path[2]
                        );
                        ctx.body = {
                            shipment,
                            message: "Select a rate to create the label",
                        };
                    }
                }
            } else if (method === "delete") {
                // Void label
                const labelId = urlpath.path[2];
                if (!labelId) {
                    throw this.exceptions.BadRequestException;
                }
                const filters = [`id:${labelId}`];
                const labelData = await this.form.get.get_form(
                    ctx,
                    user,
                    "shipment",
                    { limit: 1, filter: filters }
                );
                if (!labelData[0].shipment_id) {
                    throw this.exceptions.BadRequestException;
                }
                const result = await this.label.voidLabel(
                    labelData[0].shipment_id
                );
                if (result.status === "QUEUED") {
                    await this.updateShipmentStatus(labelId, "voided");
                    ctx.body = { result, message: "Label voided successfully" };
                } else {
                    ctx.status = 400;
                    ctx.body = { result, message: "Error voiding label" };
                }
            } else {
                throw new this.exceptions.BadRequestException();
            }
        } catch (e) {
            ctx.status = e.status || 500;
            ctx.body = { error: e.message };
            console.error(e);
        }
    }

    downloadAndUploadLabel = async (labelUrl) => {
        try {
            // Download PDF
            const response = await fetch(labelUrl);
            const urlHash = this.fx.md5(labelUrl);
            if (!response.ok) {
                throw new Error(
                    `Failed to fetch: ${response.status} ${response.statusText}`
                );
            }

            // Get the filename from URL or Content-Disposition header
            const contentDisposition = response.headers.get(
                "content-disposition"
            );
            let filename = "shipping_label_" + urlHash + ".pdf";
            if (contentDisposition) {
                const filenameMatch =
                    contentDisposition.match(/filename="(.+)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Convert to Buffer
            const buffer = await response
                .arrayBuffer()
                .then((ab) => Buffer.from(ab));

            // Create temporary file
            const tempDir = require("os").tmpdir();
            const tempFilePath = require("path").join(tempDir, filename);
            require("fs").writeFileSync(tempFilePath, buffer);

            // Create file object for uploadToS3
            const file = {
                filepath: tempFilePath,
                originalFilename: filename,
                mimetype: "application/pdf",
                size: buffer.length,
            };

            // Upload to S3
            const SecureFile = require("../../api/file/secure");
            const secureFile = new SecureFile(this.nes);
            const result = await secureFile.uploadToS3(file);

            return result;
        } catch (error) {
            console.error("Download and upload failed:", error);
            throw error;
        }
    };

    updateCareplanDeliveryTick = async (ctx, dtId, hashObject, label, body) => {
        try {
            const transaction = this.db.env.rw.transaction(ctx);
            const careplanDeliveryTick = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_delivery_tick",
                    { limit: 1, filter: [`id:${dtId}`] }
                )
            );
            if (careplanDeliveryTick) {
                const rate = body.rateMeta;
                if (!rate) {
                    throw new Error("Rate not found");
                }
                let sipping_method = null;
                if (rate.provider && rate.provider === "UPS") {
                    if (rate.servicelevel.name.includes("Ground")) {
                        sipping_method = "UPS Ground";
                    } else if (rate.servicelevel.name.includes("2nd Day")) {
                        sipping_method = "UPS 2-day";
                    } else if (rate.servicelevel.name.includes("Overnight")) {
                        sipping_method = "UPS Overnight";
                    }
                } else if (rate.provider && rate.provider === "FedEx") {
                    if (rate.servicelevel.name.includes("Ground")) {
                        sipping_method = "FedEx Ground";
                    } else if (rate.servicelevel.name.includes("2Day")) {
                        sipping_method = "FedEx 2-day";
                    } else if (rate.servicelevel.name.includes("Overnight")) {
                        sipping_method = "FedEx Overnight";
                    }
                }
                await transaction.update(
                    "careplan_delivery_tick",
                    {
                        subform_delivery_log: [
                            {
                                ...careplanDeliveryTick.subform_delivery_log[0],
                                shipping_label: hashObject,
                                ship_method_id: sipping_method,
                                shipment_status_id: "SHIPPED",
                                ship_date: moment(body.shipment_date).format(
                                    "MM/DD/YYYY"
                                ),
                                tracking_no: label.tracking_number,
                                shipping_cost: parseFloat(rate.amount),
                            },
                        ],
                    },
                    careplanDeliveryTick.id
                );
            }
            if (transaction.can_commit()) {
                transaction.commit();
            }
        } catch (error) {
            console.error(
                "Error updating careplan delivery tick:",
                error.message
            );
            return { error: true, message: error.message };
        }
    };
    updateShipmentStatus = async (shipmentId, status) => {
        try {
            const sql = `UPDATE form_shipment SET shipment_status = %L WHERE id = %L`;
            this.db.env.rw.query(sql, [status, shipmentId]);
        } catch (error) {
            console.error("Error updating shipment status:", error.message);
            return { error: true, message: error.message };
        }
    };

    saveShipmentData = async (ctx, dtId, label) => {
        try {
            console.log("label", label);
            const body = ctx.request.body;
            if (!body) {
                throw this.exceptions.BadRequestException;
            }
            const filters = [`id:${dtId}`];
            const [dtData] = await this.form.get.get_form(
                ctx,
                ctx.user,
                "careplan_delivery_tick",
                { limit: 1, filter: filters }
            );
            const data = body.shipment_meta;

            const [rate] = data.rates.filter((r) => r.object_id === label.rate);
            console.log("rate", rate);
            const shipmentUrl = `INSERT INTO form_shipment (delivery_ticket_id, shipment_date, rate_id, shipment_id, amount, currency, provider, estimated_days, 
            service_level, shipment_status, tracking_number, tracking_status, label_url, tracking_url, patient_id, view_tracking, view_label, view_delivery_ticket)
            VALUES (%s, %L, %L, %L, %L, %L, %L, %L, %L, %L, %L, %L, %L, %L, %s, %L, %L, %L)`;
            await this.db.env.rw.query(shipmentUrl, [
                dtId,
                data.shipment_date,
                label.rate,
                label.object_id,
                rate.amount,
                rate.currency,
                rate.provider,
                label.eta,
                rate.servicelevel?.name || "",
                label.status,
                label.tracking_number,
                label.tracking_status,
                label.label_url,
                label.tracking_url_provider,
                dtData.patient_id,
                JSON.stringify({
                    url: label.tracking_url_provider,
                    title: "View Shipment Status",
                    icon: "fa fa-link",
                }),
                JSON.stringify({
                    url: label.label_url,
                    title: "View Label",
                    icon: "fa fa-link",
                }),
                JSON.stringify({
                    url: `#/patient/${dtData.patient_id}/careplan_delivery_tick/${dtId}/read`,
                    title: "View Delivery Ticket",
                    icon: "fa fa-link",
                }),
            ]);
            console.log(data.parcels, "parcels\n\n");
            data.parcels.map(async (parcel) => {
                const sql = `INSERT INTO form_shipment_parcel (delivery_ticket_id, height, width, length, distance_unit, mass_unit, weight, parcel_object_id) 
                VALUES (%L, %L, %L, %L, %L, %L, %L, %L)`;
                await this.db.env.rw.query(sql, [
                    dtId,
                    parcel.height,
                    parcel.width,
                    parcel.length,
                    parcel.distance_unit,
                    parcel.mass_unit,
                    parcel.weight,
                    label.parcel,
                ]);
                const sid = await this.db.env.rw.query(
                    `SELECT MAX(id) AS id FROM form_shipment`,
                    []
                );
                const pid = await this.db.env.rw.query(
                    `SELECT MAX(id) AS id FROM form_shipment_parcel`,
                    []
                );
                await this.db.env.rw.query(
                    `INSERT INTO sf_form_shipment_to_shipment_parcel (form_shipment_fk, form_shipment_parcel_fk) VALUES (%s, %s)`,
                    [sid[0].id, pid[0].id]
                );
            });
        } catch (error) {
            console.error("Error creating shipment:", error.message);
            return { error: true, message: error.message };
        }
    };
};
