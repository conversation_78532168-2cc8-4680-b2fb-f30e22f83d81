/*jshint : 6 */
"use strict";

const _ = require("lodash");

const { ModuleLogger } = require("@core/logger");
const { ActionResponseWrappers, DisplayType, NextAction } = require("@actions");

/**
 * @class
 * @classdesc Class for running claims.
 * @extends ModuleLogger
 */
module.exports = class ClaimRunnerClass extends ModuleLogger {
    constructor(nes, ctx, formName) {
        super("claim-runner");
        this.nes = nes;
        this.ctx = ctx;
        this.formName = formName;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    /**
     * Runs a claim.
     *
     * @param {Object} claimRec - The claim record object.
     * @returns {Promise<Array|Object>} An array containing the processed claim or an error object.
     */
    async runClaim(_claimRec) {
        throw this.fx.getClaraError(`Run not implemented`);
    }

    /**
     * Reverses a claim.
     * @async
     * @param {Object} claimRec - The claim record object.
     * @returns {Promise<boolean>} A promise that resolves to true if the claim was successfully reversed, false otherwise.
     * @throws {Error} If an error occurs during the reversal process.
     */
    async reverseClaim(_claimRec) {
        throw this.fx.getClaraError(`Reversal not implemented`);
    }

    /**
     * Rebill a claim.
     * @async
     * @param {Object} claimRec - The claim record object.
     * @returns {Promise<Object>} A promise that resolves to the result of the rebill operation.
     * @throws {Error} If an error occurs during the rebill process.
     */
    async rebillClaim(_claimRec) {
        throw this.fx.getClaraError(`Rebilling not implemented`);
    }

    /**
     * Validates a claim.
     * @async
     * @param {Object} claimRec - The claim record object.
     * @returns {Promise<Object>} A promise that resolves to the result of the validation operation.
     * @throws {Error} If an error occurs during the validation process.
     */
    async validateClaim(_claimRec) {
        throw this.fx.getClaraError(`Validation not implemented`);
    }

    /**
     * Handles the response for a claim.
     * @async
     * @param {Object} claimResponse - The response object for the claim.
     * @param {Object|null} invoiceRec - The invoice record, if applicable. Defaults to null.
     * @returns {Promise<Object>} A promise that resolves to the result of handling the claim response.
     */
    async handleClaimResponse(claimResponse, invoiceRec = null) {
        console.log("Transaction:", claimResponse);
        console.log(`Handling response for ${this.formName} claim`);
        try {
            console.log(claimResponse);
            const validationErrors = claimResponse?.validationErrors || null;
            const error = claimResponse?.error || null;
            const claimRec = claimResponse?.record || null;
            const errorBeforeSending = claimResponse?.beforeResponse || false;
            if (error && errorBeforeSending) {
                return await this.__handleResponseError(
                    error,
                    "Claim Validation Error",
                    claimRec,
                    validationErrors,
                    invoiceRec
                );
            }

            if (error) {
                console.log(
                    `Handling claim response error for ${this.formName} ID ${claimRec?.id}. Error: ${error}`
                );
                return await this.__handleResponseError(
                    error,
                    "Claim Submission Error",
                    claimRec,
                    validationErrors,
                    invoiceRec
                );
            }

            const rawClaimRec = claimRec;
            const successMessage = await this.fetchSuccessMessage(rawClaimRec);
            if (!invoiceRec) {
                const overrideResults =
                    await this.fetchResponseOverride(rawClaimRec);
                if (overrideResults) return overrideResults;
                return ActionResponseWrappers.edit(
                    claimRec.id,
                    this.formName,
                    {},
                    null,
                    null,
                    null,
                    successMessage,
                    null,
                    DisplayType.TAB,
                    NextAction.REFRESH
                );
            }
            return ActionResponseWrappers.success(
                successMessage,
                NextAction.REFRESH
            );
        } catch (e) {
            const errorMessage = `Error encountered while handling ${this.formName} claim response.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the success message for a claim submission
     * @async
     * @param {Object} _claimRec - The claim record (unused parameter)
     * @returns {Promise<string>} A promise that resolves to the success message
     */
    async fetchSuccessMessage(_claimRec) {
        return `Claim has been successfully submitted. Please check response for remittance information`;
    }

    /**
     * Fetches a response override for a claim. If set, will return the value from the claim runner instead of the default.
     * @async
     * @param {Object} _claimRec - The claim record (unused parameter)
     * @returns {Promise<Object|null>} A promise that resolves to the response override or null
     */
    async fetchResponseOverride(_claimRec) {
        return null;
    }

    /**
     * Handles response errors for claims.
     *
     * @async
     * @param {string} error - The error message.
     * @param {string|null} errorTitle - The error title, if any.
     * @param {Object|null} claimRec - The claim record.
     * @param {Object|null} validationErrors - Validation errors, if any.
     * @param {Object|null} invoiceRec - The invoice record, if any.
     * @param {Object|null} ncpdpRec - The test claim group record, if any.
     * @returns {Promise<Object>} An ActionResponseWrapper containing the error information.
     * @throws {Error} If an error occurs while handling the response error.
     */
    async __handleResponseError(
        error,
        errorTitle = "Claim Error",
        claimRec = null,
        validationErrors = null,
        invoiceRec = null
    ) {
        try {
            if (invoiceRec) {
                return ActionResponseWrappers.edit(
                    invoiceRec.id,
                    "billing_invoice",
                    {},
                    null,
                    error,
                    errorTitle,
                    null,
                    validationErrors,
                    DisplayType.TAB,
                    NextAction.REFRESH
                );
            }

            if (claimRec) {
                return ActionResponseWrappers.edit(
                    claimRec.id,
                    this.formName,
                    {},
                    null,
                    error,
                    errorTitle,
                    null,
                    validationErrors,
                    DisplayType.TAB,
                    NextAction.REFRESH
                );
            }
            return ActionResponseWrappers.error(error);
        } catch (e) {
            const errorMessage = `Error encountered while handling claim response.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
