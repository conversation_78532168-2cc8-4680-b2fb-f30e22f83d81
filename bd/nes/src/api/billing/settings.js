"use strict";
const dispenseSettings = require("@dispense/settings");

const ClaimType = {
    NCPDP: "NCPDP",
    MEDICAL: "Major Medical",
    CMS1500: "CMS1500",
    GENERIC: "Generic",
};

const TaxBasisCode = {
    INGREDIENTS_ONLY: "02",
    ING_PLUS_DISPENSING_FEE: "03",
    SERVICE_COST_ONLY: "04",
};

const InvoiceStatus = {
    ACCEPTED: "Accepted",
    CONFIRMED: "Confirmed",
    OPEN: "Open",
    VOIDED: "Voided",
    ZEROED: "$0",
};

const ClaimSubstatus = {
    BILL_FOR_DENIAL: "100",
    ON_HOLD_PA: "101",
    ON_HOLD_PAP: "102",
    ON_HOLD_COPAY: "103",
    ON_HOLD_CMN: "104",
    ON_HOLD_NURSING: "105",
    ON_HOLD_SERVICE_DATE: "106",
};

const InsuranceActions = {
    RUN_TEST_CLAIM: "Run Test Claim",
    TEST_CLAIM: "Test Claims",
    CLAIM: "Claims",
    ELIGIBILITY: "Run Extended Eligibility",
    PHARMELIGIBILITY: "Check Eligibility",
    CREATEBV: "Create BV",
    VIEWEBV: "View BV",
    ELIGIBILITYCH: "Run 270 Eligibility",
    VIEW_ELIGIBILITY: "View Elig. Results",
    PA: "Prior Auths",
    VIEW_PAYER: "View Payer",
};

const InsuranceActionKeys = {
    [InsuranceActions.RUN_TEST_CLAIM]: "gen_claim",
    [InsuranceActions.TEST_CLAIM]: "test_claim",
    [InsuranceActions.CLAIM]: "claim",
    [InsuranceActions.ELIGIBILITY]: "check_eligibility",
    [InsuranceActions.PHARMELIGIBILITY]: "check_eligibility_ph",
    [InsuranceActions.CREATEBV]: "create_bv",
    [InsuranceActions.VIEWEBV]: "view_bv",
    [InsuranceActions.ELIGIBILITYCH]: "check_eligibility_ch",
    [InsuranceActions.VIEW_ELIGIBILITY]: "view_eligibility",
    [InsuranceActions.PA]: "pas",
    [InsuranceActions.VIEW_PAYER]: "view_payer",
};

const InvoiceActions = {
    VOID: "Void Claim",
    REVERSE: "Reverse Claim",
    REBILL: "Rebill Claim",
    SUBMIT: "Adjudicate",
    SAVE_SUBMIT: "Adjudicate & Save",
    VALIDATE: "Validate",
    GENERATE_COB: "Generate COB",
    ACCEPT: "Accept",
    ACCEPT_REVENUE: "Confirm Revenue",
    REOPEN: "Unaccept",
    ZERO_INVOICE: "$0 Invoice",
    CMS_1500: "View CMS-1500",
    NCPDP_DX: "DX / Measures",
    NCPDP_NARRATIVE: "Narrative",
    NCPDP_DUR: "DUR/PPS",
    NCPDP_COUPON: "Coupon",
    NCPDP_DX_READ: "View DX / Measures",
    NCPDP_NARRATIVE_READ: "View Narrative",
    NCPDP_DUR_READ: "View DUR/PPS",
    NCPDP_COUPON_READ: "View Coupon",
};

const InvoiceActionKeys = {
    [InvoiceActions.VOID]: "invoice_void",
    [InvoiceActions.SUBMIT]: "invoice_submit",
    [InvoiceActions.VALIDATE]: "invoice_validate",
    [InvoiceActions.REVERSE]: "invoice_reverse",
    [InvoiceActions.REBILL]: "invoice_rebill",
    [InvoiceActions.GENERATE_COB]: "invoice_generate_cob",
    [InvoiceActions.ACCEPT]: "invoice_accept",
    [InvoiceActions.REOPEN]: "invoice_unaccept",
    [InvoiceActions.CMS_1500]: "invoice_cms_1500",
    [InvoiceActions.SAVE_SUBMIT]: "invoice_save_submit",
    [InvoiceActions.ZERO_INVOICE]: "invoice_zero",
    [InvoiceActions.ACCEPT_REVENUE]: "invoice_accept_revenue",
    [InvoiceActions.NCPDP_DX]: "ncpdp_dx",
    [InvoiceActions.NCPDP_NARRATIVE]: "ncpdp_narrative",
    [InvoiceActions.NCPDP_DUR]: "ncpdp_dur",
    [InvoiceActions.NCPDP_COUPON]: "ncpdp_coupon",
    [InvoiceActions.NCPDP_DX_READ]: "ncpdp_dx_read",
    [InvoiceActions.NCPDP_NARRATIVE_READ]: "ncpdp_narrative_read",
    [InvoiceActions.NCPDP_DUR_READ]: "ncpdp_dur_read",
    [InvoiceActions.NCPDP_COUPON_READ]: "ncpdp_coupon_read",
};

const ChargeLineActions = {
    VOID: "Void Charge Line",
    ZERO_CHARGE_LINE: "$0 Charge Line",
};

const ChargeLineActionKeys = {
    [ChargeLineActions.VOID]: "charge_line_void",
    [ChargeLineActions.ZERO_CHARGE_LINE]: "charge_line_zero",
};

const ClosingPeriodActions = {
    UNLOCK: "Unlock",
    UNLOCK_APPROVED: "Unlock Approved",
    REFRESH_AND_LOCK: "Refresh and Lock",
    REFRESH_AND_LOCK_APPROVED: "Refresh and Lock Approved",
    VOID: "Void",
};

const ClosingPeriodKeys = {
    [ClosingPeriodActions.UNLOCK]: "unlock",
    [ClosingPeriodActions.REFRESH_AND_LOCK]: "refresh_and_lock",
    [ClosingPeriodActions.VOID]: "void",
    [ClosingPeriodActions.UNLOCK_APPROVED]: "unlock_approved",
    [ClosingPeriodActions.REFRESH_AND_LOCK_APPROVED]:
        "refresh_and_lock_approved",
};

const ledgerFinanceActions = {
    REVERSE_LEDGER_FINANCE: "Reverse",
    ZERO_LEDGER_FINANCE: "$0 Entry",
};

const ledgerFinanceActionKeys = {
    [ledgerFinanceActions.REVERSE_LEDGER_FINANCE]: "reverse_ledger_finance",
    [ledgerFinanceActions.ZERO_LEDGER_FINANCE]: "zero_ledger_finance",
};

const BillingCashActions = {
    ZERO_CASH: "$0 Entry",
};

const BillingCashActionKeys = {
    [BillingCashActions.ZERO_CASH]: "zero_cash",
};

const ARTransactionActions = {
    ZERO_TRANSACTION: "$0 Entry",
};

const ARTransactionActionKeys = {
    [ARTransactionActions.ZERO_TRANSACTION]: "zero_transaction",
};

const AdjustmentReason = {
    CONTRACT: "Contract",
    CLAWBACK: "Clawback",
    EOB: "EOB",
    OVERPAYMENT: "Overpayment",
    UNDERPAYMENT: "Underpayment",
    MISTAKE: "Mistake",
    OTHER: "Other",
};

const WriteoffReason = {
    HARDSHIP: "Hardship",
    BANKRUPTCY: "Bankruptcy",
    DAMAGED_PRODUCT: "DamagedProduct",
    NONRESPONSIVE: "Nonresponsive",
    UNCOLLECTIBLE: "Uncollectible",
    DISPUTED_CLAIM: "DisputedClaim",
    FRAUD: "Fraud",
    ERROR: "Error",
};

const ARLedgerTransactionType = {
    POSTING: "Posting",
    CASH: "Cash",
    ADJUSTMENT: "Adjustment",
    REVERSAL: "Reversal",
    BAD_DEBT: "Bad Debt",
};

const RevenueLedgerTransactionType = {
    POSTING: "Posting",
    ADJUSTMENT: "Adjustment",
    REVERSAL: "Reversal",
    BAD_DEBT: "Bad Debt",
};

const CashLedgerTransactionType = {
    CASH: "Cash",
    REVERSAL: "Reversal",
};

const LedgerTransactionCategory = {
    DISPENSE: "Dispense",
    SUPPLY: "Supply",
    BILLABLE: "Billable",
    COPAY: "Copay",
    NURSING: "Nursing Visit",
    DME: "DME",
};

const AccountType = {
    PATIENT: "Patient",
    PAYER: "Payer",
    SITE: "Site",
};

const InvoiceType = {
    PATIENT: "Patient",
    PAYER: "Payer",
};

const InvoiceAdjustmentType = {
    CREDIT: "Credit",
    DEBIT: "Debit",
};

const InvoiceAdjustmentAppliedTo = {
    INVOICE: "Invoice",
    COPAY: "Copay",
};

const SecurityActions = {
    THRESHOLD: "Threshold",
    POST_CLOSE_ADJUSTMENT: "Post Adjustment",
    POST_CLOSE_WRITEOFF: "Post Close Writeoff",
    POST_CLOSE_POSTING: "Post Close Posting",
    VOID_INVOICE: "Void Claim",
    VOID_CLOSING: "Void Closing",
    REFRESH_AND_LOCK_CLOSING: "Refresh and Lock Closing",
    UNLOCK_CLOSING: "Unlock Closing",
};

const SecurityApprovalStatus = {
    PENDING: "Pending",
    APPROVED: "Approved",
    REJECTED: "Rejected",
};

const SecurityApprovalAction = {
    CREATE: "Create",
    UPDATE: "Update",
    MULTIPLE: "Create/Update",
    POST: "Post",
    FUNCTION: "Function",
};

const WorkflowTeams = {
    INTAKE: "Intake",
    BILLING: "Billing",
    SALES: "Sales",
    PHARMACY: "Pharmacy",
    BILLING_MANAGERS: "Billing Managers",
    PHARMACY_MANAGERS: "Pharmacy Managers",
};

const BillingMethodType = {
    MAJOR_MEDICAL: "mm",
    CMS1500: "cms1500",
    NCPDP: "ncpdp",
    GENERIC: "generic",
};

const ChargeLineBillType = {
    DISPENSE: "Dispense",
    NURSING_VISIT: "Nursing Visit",
    DME: "DME",
    COPAY: "Copay",
    CHARGE: "Charge",
    PER_DIEM: "Per Diem",
};

const BillableType = {
    PER_DIEM: "Per Diem",
    DME: "DME",
    CHARGE: "Charge",
};

const FormsToClosePeriod = [
    "billing_adjustment",
    "billing_invoice",
    "billing_posting",
];

const AttachmentFlagIds = {
    CLINICAL: 1,
    PRIOR_AUTH: 2,
    INTAKE: 3,
    ASSISTANCE: 4,
    INVOICE: 5,
    HARDSHIP_WAIVER: 6,
    RECEIPT: 7,
    SURVEY: 8,
    NURSING: 9,
    BILLING: 10,
    INCIDENT: 11,
    INTERVENTION: 12,
    VERBAL_ORDER: 13,
    WELCOME_PACKET: 14,
    REFERRAL: 15,
    PRESCRIPTION: 16,
    CMN: 17,
    EOB: 18,
};

const FormsToCloseFilters = {
    [FormsToClosePeriod.billing_adjustment]: [`close_id:null`, `void:!Yes`],
    [FormsToClosePeriod.billing_invoice]: [
        `close_id:null`,
        `void:!Yes`,
        `status:Closed`,
    ],
    [FormsToClosePeriod.billing_posting]: [`void:!Yes`],
};
const MAX_SERVICE_LINES = 50; // Max service lines for a medical claim
const MAX_DIAGNOSES = 12;
const SELF_PAY_PAYER_ID = 1;
const DefaultClaimFilingIndicator = "CI"; // Commercial Insurance

const DefaultPlaceOfServiceCode = "12"; // Home

module.exports = {
    BillingMethodType,
    MAX_SERVICE_LINES,
    MAX_DIAGNOSES,
    WorkflowTeams,
    SecurityApprovalAction,
    SecurityApprovalStatus,
    SecurityActions,
    ClaimType,
    InvoiceAdjustmentType,
    TaxBasisCode,
    AdjustmentReason,
    WriteoffReason,
    ledgerFinanceActions,
    ledgerFinanceActionKeys,
    BillingCashActions,
    BillingCashActionKeys,
    ARTransactionActions,
    ARTransactionActionKeys,
    InvoiceStatus,
    ClaimSubstatus,
    InvoiceActions,
    InvoiceActionKeys,
    InvoiceAdjustmentAppliedTo,
    LedgerTransactionCategory,
    ARLedgerTransactionType,
    CashLedgerTransactionType,
    RevenueLedgerTransactionType,
    AccountType,
    InvoiceType,
    ClosingPeriodActions,
    ClosingPeriodKeys,
    BillableType,
    ChargeLineBillType,
    SELF_PAY_PAYER_ID,
    FormsToClosePeriod,
    FormsToCloseFilters,
    DefaultClaimFilingIndicator,
    DefaultPlaceOfServiceCode,
    AttachmentFlagIds,
    InsuranceActions,
    InsuranceActionKeys,
    ChargeLineActions,
    ChargeLineActionKeys,
    ...dispenseSettings,
};
