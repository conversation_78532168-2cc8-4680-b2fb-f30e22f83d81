/*jshint : 6 */
"use strict";

const _ = require("lodash");
const Joi = require("joi");
const moment = require("moment");

const DispenseFetcherClass = require("@dispense/fetcher");

const { ErrorMessages } = require("./errors");
const { PLACEHOLDER_RECORD_ID, BillingMethodType } = require("./settings");
const { RecordSchemas } = require("./schemas");

/**
 * @class
 * @classdesc Class responsible for fetching billing-related data.
 */
module.exports = class BillingFetcherClass extends DispenseFetcherClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.billingRules = nes.securityRules.billing;
        const InventoryPricingClass = require("@inventory/pricing");
        this.pricing = this.fx.getInstance(
            ctx,
            InventoryPricingClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Fetches all invoices associated with a prescription ID.
     * @async
     * @param {number} rxId - The ID of the prescription to fetch invoices for.
     * @returns {Promise<Array>} Array of invoice records associated with the prescription.
     * @throws {Error} If there is an error fetching the invoice records.
     */
    async fetchRxInvoices(rxId) {
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing/Invalid prescription ID to fetch invoices."
            );
            const filters = [
                `rx_id:${rxId}`,
                `void:!Yes`,
                `zeroed:!Yes`,
                `delivery_ticket_id:null`,
            ];
            const invoiceRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "billing_invoice",
                { filter: filters, sort: "-id" }
            );
            return invoiceRecs;
        } catch (e) {
            const errorMessage = `Error fetching prescription invoices for prescription ID ${rxId}`;
            console.error(`${errorMessage}:`, e);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the claim record for a given invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record to fetch the claim ID from.
     * @returns {Promise<Object>} The claim record.
     * @throws {Error} If there's an error fetching the claim record.
     */
    async fetchInvoiceElectronicClaimRecord(invoiceRec) {
        try {
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to fetch electronic claim record."
            );

            if (invoiceRec.billing_method_id === BillingMethodType.NCPDP) {
                return invoiceRec?.subform_pharmacy?.length > 0
                    ? invoiceRec.subform_pharmacy[0]
                    : null;
            } else if (
                invoiceRec.billing_method_id === BillingMethodType.MAJOR_MEDICAL
            ) {
                return invoiceRec?.subform_medical?.length > 0
                    ? invoiceRec.subform_medical[0]
                    : null;
            } else {
                return null;
            }
        } catch (e) {
            const errorMessage = `Error fetching invoice claim record`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the amounts associated with an invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record to fetch amounts for.
     * @returns {Promise<Object|null>} The invoice amounts if available, or null if not found.
     * @throws {Error} If there's an error fetching the invoice amounts.
     */
    async fetchInvoiceAmounts(invoiceRec) {
        console.debug(
            `Fetching invoice amounts for invoice ID ${invoiceRec.id}`
        );
        try {
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to fetch amounts."
            );
            const sql = `
            SELECT
                *
            FROM 
                get_invoice_amounts(%L::text)`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                invoiceRec.invoice_no,
            ]);
            return rows?.[0] || null;
        } catch (e) {
            const errorMessage = `Error fetching invoice amounts`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the last NCPDP claim response for a given claim record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record to fetch the response for.
     * @param {boolean} [successOnly=false] - Whether to only fetch successful responses.
     * @returns {Promise<Object|null>} The last claim response if available, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchLastNCPDPClaimResponse(ncpdpRec, successOnly = false) {
        console.debug(
            `Fetching last response for claim no ${ncpdpRec.claim_no}`
        );
        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                Joi.object().required(),
                ncpdpRec,
                "Missing NCPDP claim record to fetch last response."
            );

            const claimNo = ncpdpRec.claim_no;
            if (!claimNo) {
                throw this.fx.getClaraError(
                    `Claim no is required to fetch last response.`
                );
            }
            const filters = [`claim_no:${claimNo}`];
            if (successOnly) {
                filters.push(
                    `transaction_code:B3`,
                    `transaction_code:B1`,
                    `response_status:A`,
                    `transaction_response_status:P`
                );
            }
            const lastResponseRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "ncpdp_response",
                    {
                        limit: 1,
                        sort: "-id",
                        filter: filters,
                    }
                )
            );
            return lastResponseRec;
        } catch (e) {
            const errorMessage = `Error encountered while fetching last response.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the Coordination of Benefits (COB) child invoice for a given invoice number.
     * @async
     * @param {string} invoiceNo - The parent invoice number to search for.
     * @returns {Promise<Object|null>} The COB child invoice record if found, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchCOBChildInvoice(invoiceNo) {
        try {
            await this.fx.validateSchema(
                "invoiceNo",
                Joi.string().required(),
                invoiceNo,
                "Missing invoice number to fetch COB child invoice."
            );
            const filters = [
                `parent_invoice_no:${invoiceNo}`,
                `void:!Yes`,
                `zeroed:!Yes`,
            ];
            const invoiceRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "billing_invoice",
                    { limit: 1, filter: filters }
                )
            );
            return invoiceRec || null;
        } catch (e) {
            const errorMessage = `Error fetching COB child invoice`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the Coordination of Benefits (COB) child charge line for a given charge line number.
     * @async
     * @param {string} chargeNo - The parent charge line number to search for.
     * @returns {Promise<Object|null>} The COB child charge line record if found, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchCOBChargeLine(chargeNo) {
        try {
            await this.fx.validateSchema(
                "chargeNo",
                Joi.string().required(),
                chargeNo,
                "Missing charge line number to fetch COB charge line."
            );
            const filters = [
                `parent_charge_no:${chargeNo}`,
                `void:!Yes`,
                `zeroed:!Yes`,
            ];
            const chargeLineRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "billing_charge_line",
                    { limit: 1, filter: filters }
                )
            );
            return chargeLineRec || null;
        } catch (e) {
            const errorMessage = `Error fetching COB charge line`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the last close period record.
     * @returns {Promise<Object|null>} The last close period record, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async getLastClosePeriod() {
        try {
            const closingRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "billing_closing",
                    { limit: 1, sort: "-end_date" }
                )
            );

            if (!closingRec) {
                return null;
            }

            const endDate = moment(closingRec.end_date);
            const nextOpenDate = endDate.add(1, "day");
            return nextOpenDate.format("MM/DD/YYYY");
        } catch (e) {
            const errorMessage = `Error fetching last close period`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the ledger payment applied entries for a given invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record object.
     * @returns {Promise<Array>} An array of ledger payment applied entries for the specified invoice.
     */
    async invoiceHasPostedPayments(invoiceRec) {
        try {
            console.log(
                `Checking invoice has posted payments for invoice ID ${invoiceRec?.id}`
            );
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to fetch posted payments."
            );

            const sql = `
            SELECT
                paid_raw AS total_payments
            FROM 
                vw_master_invoice_drill_down inv
            WHERE
                inv.invoice_no = %L::text`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                invoiceRec.invoice_no,
            ]);
            const totalPaymentAmount = rows?.[0]?.total_payments || 0.0;
            const hasPostedPayments = totalPaymentAmount > 0;
            return hasPostedPayments;
        } catch (e) {
            const errorMessage =
                ErrorMessages.LEDGER_PAYMENT_ENTRIES_FETCH_ERROR;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks if a charge line has any posted payments.
     * @async
     * @param {Object} chargeLineRec - The charge line record object to check for posted payments.
     * @returns {Promise<boolean>} A promise that resolves to true if the charge line has posted payments, false otherwise.
     * @throws {Error} If there is an error fetching the payment information or if the charge line record is invalid.
     */
    async chargeLineHasPostedPayments(chargeLineRec) {
        try {
            console.log(
                `Checking charge line has posted payments for charge line ID ${chargeLineRec?.id}`
            );
            await this.fx.validateSchema(
                "chargeLineRec",
                RecordSchemas.chargeLine.required(),
                chargeLineRec,
                "Missing charge line record to fetch posted payments."
            );

            const sql = `
            SELECT
                chg.paid_raw AS total_payments
            FROM 
                vw_master_charge_line_drill_down chg
            WHERE
                chg.charge_no = %L::text`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                chargeLineRec.charge_no,
            ]);
            const totalPaymentAmount = rows?.[0]?.total_payments || 0.0;
            const hasPostedPayments = totalPaymentAmount > 0;
            return hasPostedPayments;
        } catch (e) {
            const errorMessage =
                ErrorMessages.LEDGER_PAYMENT_ENTRIES_FETCH_ERROR;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the next payer based on the provided order record, previous insurance record, and prescription records.
     * @async
     * @param {number} rxRecId - The prescription record id.
     * @param {number} previousInsuranceId - The previous insurance record id.
     * @returns {Promise<Object|null>} The next payer record, or null if not found.
     * @throws {Error} If there's an error fetching the next payer.
     */
    async fetchNextInsuranceId(rxRecId, previousInsuranceId) {
        try {
            await Promise.all([
                this.fx.validateSchema(
                    "rxRecId",
                    Joi.number().required(),
                    rxRecId,
                    "Missing prescription record to fetch next insurance ID."
                ),
                this.fx.validateSchema(
                    "previousInsuranceId",
                    Joi.number().required(),
                    previousInsuranceId,
                    "Missing previous insurance ID to fetch next insurance ID."
                ),
            ]);

            const sql = `
                SELECT
                    ptins.id as insurance_id
                FROM form_patient pt
                INNER JOIN form_patient_insurance ptins ON ptins.id = get_next_insurance_id(%L::integer, %L::integer, FALSE::boolean);`;

            const nextInsuranceId = await this.db.env.rw.parseSQLUsingPGP(sql, [
                rxRecId,
                previousInsuranceId,
            ]);
            if (nextInsuranceId.length === 0) {
                return null;
            }

            const insuranceId = nextInsuranceId[0].result;
            return insuranceId;
        } catch (e) {
            const errorMessage = ErrorMessages.COB_PAYER_FETCH_ERROR;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches available Coordination of Benefits (COB) payers for a given invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record object to fetch available COB payers for.
     * @returns {Promise<Array>} An array of insurance IDs available for COB that are not yet associated with the master invoice.
     * @throws {Error} If there's an error fetching the available COB payers.
     */
    async fetchAvailableCOBPayers(invoiceRec) {
        try {
            console.log(
                `Checking invoice COB payers for invoice ID ${invoiceRec?.id}`
            );
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to fetch COB payers."
            );

            const sql = `
                SELECT
                    ARRAY_AGG(DISTINCT ptins.id) as insurance_ids
                FROM form_patient_insurance ptins
                WHERE ptins.id NOT IN (
                    SELECT
                        bi.insurance_id
                    FROM form_billing_invoice bi
                    WHERE bi.master_invoice_no = %L::text
                    AND COALESCE(bi.void, 'No') != 'Yes'
                    AND COALESCE(bi.zeroed, 'No') != 'Yes'
                )
                AND ptins.patient_id = %s::integer;`;
            const availablePayers = await this.db.env.rw.parseSQLUsingPGP(sql, [
                invoiceRec.master_invoice_no,
                invoiceRec.patient_id,
            ]);
            if (availablePayers.length === 0) {
                return [];
            }
            return availablePayers[0].insurance_ids;
        } catch (error) {
            const errorMessage = ErrorMessages.COB_PAYERS_FETCH_ERROR;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Fetches the claim runner for a given invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record.
     * @returns {Promise<Object>} The claim runner instance.
     * @throws {Error} If an error occurs during the fetching process.
     */
    async fetchClaimRunnerForInvoice(invoiceRec) {
        console.log(
            `Fetching claim runner for invoice no ${invoiceRec.invoice_no}`
        );
        try {
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to fetch claim runner."
            );

            const billingMethod = invoiceRec.billing_method_id;
            console.log("Billing Method", billingMethod);
            switch (billingMethod) {
                case BillingMethodType.MAJOR_MEDICAL: {
                    const MedicalElectronicClaimsRunnerClass = require("./medical/electronic/runner");
                    const runner = new MedicalElectronicClaimsRunnerClass(
                        this.nes,
                        this.ctx
                    );
                    return runner;
                }
                case BillingMethodType.NCPDP: {
                    const NCPDPClaimRunnerClass = require("./pharmacy/runner");
                    const runner = new NCPDPClaimRunnerClass(
                        this.nes,
                        this.ctx
                    );
                    return runner;
                }
                default:
                    throw this.fx.getClaraError(
                        `Invalid billing method: ${billingMethod}`
                    );
            }
        } catch (e) {
            const errorMessage = ErrorMessages.CLAIM_RUNNER_FETCH_EXCEPTION;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the claim form for a given invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record.
     * @returns {Promise<string>} The claim form type.
     * @throws {Error} If an error occurs during the fetching process.
     */
    async fetchInvoiceClaim(invoiceRec) {
        console.log(
            `Fetching claim form for invoice no ${invoiceRec.invoice_no}`
        );

        try {
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to fetch claim."
            );

            const billingMethod = invoiceRec.billing_method_id;
            switch (billingMethod) {
                case BillingMethodType.MAJOR_MEDICAL:
                    return invoiceRec.subform_medical[0];
                case BillingMethodType.NCPDP:
                    return invoiceRec.subform_pharmacy[0];
                case BillingMethodType.CMS1500:
                    return invoiceRec.subform_1500[0];
                case BillingMethodType.GENERIC:
                    return invoiceRec;
                default:
                    throw this.fx.getClaraError(
                        `Invalid billing method: ${billingMethod}`
                    );
            }
        } catch (e) {
            const errorMessage = ErrorMessages.INVOICE_CLAIM_FORM_ERROR;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

};
