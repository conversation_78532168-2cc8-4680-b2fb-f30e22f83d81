"use strict";
const Joi = require("joi");
const _ = require("lodash");
const moment = require("moment-timezone");

const DispenseGeneratorClass = require("@dispense/generator");
const { ActionResponseWrappers, DisplayType, NextAction } = require("@actions");
const { BillingMethodType } = require("./settings");
const { RecordSchemas } = require("./schemas");
const { ErrorMessages } = require("./errors");
/**
 * @class
 * @classdesc Class responsible for generating and managing claims.
 */
module.exports = class BillingGeneratorClass extends DispenseGeneratorClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.form = nes.shared.form;
        const BillingFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingHelperClass = require("./helper");
        this.helper = this.fx.getInstance(
            ctx,
            BillingHelperClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Generates fill records for a prescription
     * @param {Object} rxRec - The prescription record object
     * @returns {Promise<void>}
     * @throws {Error} If there is an error generating the fill records
     */
    async generateFillRxRecords(rxRec) {
        console.debug(
            `Generating fill Rx records for prescription ID ${rxRec.id}`
        );
        try {
            const rxSettingsSql = `
                SELECT
                    billing_method_id,
                    insurance_id,
                    payer_id,
                    site_id
                FROM vw_rx_order rx
                WHERE rx.rx_no = %L::text`;
            const settingsRows = await this.db.env.rw.parseSQLUsingPGP(
                rxSettingsSql,
                [rxRec.rx_no]
            );
            if (settingsRows.length === 0) {
                throw this.fx.getClaraError(
                    `No billing method found for prescription with Rx # ${rxRec.rx_no}.`
                );
            }
            const settings = settingsRows[0];
            const billingMethodId = settings.billing_method_id || null;
            const insuranceId = settings.insurance_id || null;
            const payerId = settings.payer_id || null;
            const siteId = settings.site_id || null;
            if (!insuranceId || !payerId || !siteId) {
                throw this.fx.getClaraError(
                    `Missing required settings (Insurance ID, Payer ID, or Site ID) for prescription with Rx # ${rxRec.rx_no}.`
                );
            }
            const processPharmacyClaim = billingMethodId === "ncpdp";

            if (!processPharmacyClaim) {
                return false;
            }

            const sql = `
            SELECT
                create_rx_fill_invoice(
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer
            ) as result
            `;

            const sqlParams = [
                insuranceId,
                payerId,
                rxRec.patient_id,
                siteId,
                rxRec.id,
            ];

            const rxChargesAndInvoicesRow =
                await this.db.env.rw.parseSQLUsingPGP(sql, sqlParams);

            if (rxChargesAndInvoicesRow.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating invoice record for the prescription. Rx ID ${rxRec.id}`
                );
            }
            const results = rxChargesAndInvoicesRow[0].result;
            const invoiceRec = results?.invoice_record || null;
            if (!invoiceRec) {
                return ActionResponseWrappers.error(
                    `Error generating invoice record for the prescription.`
                );
            }
            const chargeLines = results?.charge_lines || [];
            if (chargeLines.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating charge lines for the prescription. Rx ID ${rxRec.id}`
                );
            }
            const { savedInvoiceRec } = await this.__trySaveInvoiceRecords({
                invoiceRec,
                chargeLines,
            });
            if (!savedInvoiceRec) {
                return ActionResponseWrappers.error(
                    `Error saving invoice and charge lines after generation. Rx ID ${rxRec.id}`
                );
            }
            return ActionResponseWrappers.edit(
                savedInvoiceRec.id,
                "billing_invoice",
                {},
                null,
                null,
                null,
                null,
                null,
                DisplayType.TAB,
                NextAction.REFRESH
            );
        } catch (e) {
            const errorMessage = `Error generating fill Rx records for prescription no ${rxRec.rx_no}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Autogenerates the next COB invoice for a given invoice record
     * @async
     * @param {Object} transaction - The transaction object
     * @param {Object} invoiceRec - The invoice record
     * @returns {Promise<Object>} The saved COB invoice record
     * @throws {Error} If there's an error generating the COB invoice
     */
    async autogenerateNextCOBInvoice(invoiceRec) {
        console.debug(
            `Auto-generating next COB invoice for invoice no ${invoiceRec.invoice_no}`
        );
        try {
            await this.fx.validateSchema(
                "invoiceRec",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to auto-generate next COB invoice."
            );

            if (!invoiceRec.rx_id || !Array.isArray(invoiceRec.rx_id)) {
                throw this.fx.getClaraError(
                    "Missing or invalid prescription IDs on invoice record"
                );
            }

            for (const rxId of invoiceRec.rx_id) {
                const nextInsuranceId = await this.fetcher.fetchNextInsuranceId(
                    rxId,
                    invoiceRec.id
                );
                if (nextInsuranceId) {
                    await this.generateCOBInvoice(invoiceRec, nextInsuranceId);
                }
            }
        } catch (e) {
            const errorMessage = `Error auto-generating next COB invoice.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a Coordination of Benefits (COB) invoice.
     * @async
     * @param {Object} parentInvoiceRec - The parent invoice record.
     * @param {number} nextInsuranceId - The ID of the next insurance.
     * @returns {Promise<Object>} The saved COB invoice record.
     * @throws {Error} If there's an error generating the COB invoice.
     */
    async generateCOBInvoice(parentInvoiceRec, nextInsuranceId) {
        console.debug(
            `Generating COB invoice for invoice no ${parentInvoiceRec.invoice_no}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "parentInvoiceRec",
                    RecordSchemas.invoice.required(),
                    parentInvoiceRec,
                    "Missing parent invoice record to generate COB invoice."
                ),
                this.fx.validateSchema(
                    "nextInsuranceId",
                    Joi.number().required(),
                    nextInsuranceId,
                    "Missing next insurance ID to generate COB invoice."
                ),
            ]);

            const previousInvoiceNo = parentInvoiceRec.invoice_no;
            const insuranceRec = await this.fetcher.fetchCachedRecord(
                "patient_insurance",
                nextInsuranceId
            );
            if (!insuranceRec) {
                throw this.fx.getClaraError(
                    `Insurance with ID ${nextInsuranceId} not found.`
                );
            }
            const payerId = insuranceRec.payer_id;
            const siteId = parentInvoiceRec.site_id;

            const sql = `
            SELECT
                create_cob_invoice(
                %L::text,
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer,
                %s::integer
            ) as result
            `;

            const sqlParams = [
                previousInvoiceNo,
                parentInvoiceRec.insurance_id,
                parentInvoiceRec.payer_id,
                nextInsuranceId,
                payerId,
                insuranceRec.patient_id,
                siteId,
            ];

            const rxChargesAndInvoicesRow =
                await this.db.env.rw.parseSQLUsingPGP(sql, sqlParams);

            if (rxChargesAndInvoicesRow.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating COB invoice record for invoice no ${previousInvoiceNo}.`
                );
            }
            const results = rxChargesAndInvoicesRow[0].result;
            const invoiceRec = results?.invoice_record || null;
            if (!invoiceRec) {
                return ActionResponseWrappers.error(
                    `Error generating COB invoice record for invoice no ${previousInvoiceNo}.`
                );
            }
            console.log("invoiceRec", invoiceRec);
            const chargeLines = results?.charge_lines || [];
            if (chargeLines.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating charge lines for the invoice no ${previousInvoiceNo}.`
                );
            }
            console.log("chargeLines", chargeLines);
            const { savedInvoiceRec } = await this.__trySaveInvoiceRecords({
                invoiceRec,
                chargeLines,
            });
            console.log("savedInvoiceRec", savedInvoiceRec);
            if (!savedInvoiceRec) {
                return ActionResponseWrappers.error(
                    `Error saving invoice and charge lines after generation. Invoice ID ${invoiceRec.id}`
                );
            }

            return ActionResponseWrappers.edit(
                savedInvoiceRec.id,
                "billing_invoice",
                {},
                null,
                null,
                null,
                null,
                null,
                DisplayType.TAB,
                NextAction.REFRESH
            );
        } catch (e) {
            const errorMessage = `Error generating COB invoice for invoice ${parentInvoiceRec.id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a SPRX claim for a prescription
     * @async
     * @param {Object} rxRec - The prescription record to generate claim for
     * @returns {Promise<Object>} The generated claim response wrapper
     * @throws {Error} If there is an error generating the SPRX claim
     */
    async generateRxSprxClaim(rxRec) {
        console.debug(
            `Generating SPRX claim for prescription number ${rxRec.rx_no}`
        );
        try {
            await this.fx.validateSchema(
                "rxRec",
                RecordSchemas.prescription.required(),
                rxRec,
                "Missing prescription record to check if Rx is ready to fill."
            );
            const workingPrescriptionRec =
                await this.fetcher.fetchWorkingPrescriptionRecord(rxRec.id);
            if (workingPrescriptionRec.is_ncpdp !== "Yes") {
                return ActionResponseWrappers.error(ErrorMessages.RX_NOT_NCPDP);
            }
            const res = await this.generateFillRxRecords(rxRec);
            if (!res) {
                throw new Error("Error generating claim record");
            }
            return res;
        } catch (e) {
            const errorMessage = `Error encountered while generating claim for prescription number ${rxRec.rx_no}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a test claim view for a prescription
     * @async
     * @param {Object} prescriptionRec - The prescription record object
     * @returns {Promise<Object>} The test claim view object with default values from the prescription
     * @throws {Error} If there is an error generating the test claim view
     */
    async generatePrescriptionTestClaimView(prescriptionRec) {
        console.debug(`Generating test claim view`);

        try {
            const sql = `
                SELECT DISTINCT
                    insurance_id,
                    site_id,
                    patient_id,
                    prescriber_id,
                    inventory_id,
                    route_id,
                    day_supply,
                    dispense_quantity,
                    route_id,
                    comp_dsg_fm_code,
                    compound_type,
                    comp_disp_unit,
                    day_supply,
                    rx_no,
                    careplan_order_item_id as order_item_id,
                    careplan_orderp_item_id as orderp_item_id,
                    order_no
                FROM vw_rx_test_claim_settings tcs
                WHERE tcs.rx_id = %s::integer`;
            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                prescriptionRec.id,
            ]);
            const testClaimDefaults = _.head(rows);
            return {
                ...testClaimDefaults,
                rx_no: prescriptionRec.rx_no,
                date_of_service: moment().format("MM/DD/YYYY"),
            };
        } catch (e) {
            const errorMessage = `Error encountered while generating test claim view for prescription.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a test claim view for an order item
     * @async
     * @param {Object} orderRec - The order record object
     * @param {Object} orderItemRec - The order item record object
     * @param {Object} insuranceRec - The insurance record object
     * @returns {Promise<Object>} The test claim view object with default values
     * @throws {Error} If there is an error generating the test claim view
     */
    async generateOrderItemTestClaimView(orderRec, orderItemRec, insuranceRec) {
        console.debug(`Generating test claim view`);

        try {
            if (orderItemRec.rx_no) {
                const filters = [`rx_no:${orderItemRec.rx_no}`];
                const rxRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "careplan_order_rx",
                        {
                            sort: "-id",
                            filter: filters,
                            limit: 1,
                        }
                    )
                );
                return await this.generatePrescriptionTestClaimView(rxRec);
            }

            const testClaimDefaults = {
                site_id: orderRec?.site_id,
                inventory_id: orderItemRec?.inventory_id,
                date_of_service: moment().format("MM/DD/YYYY"),
                day_supply: 28,
                prescriber_id: orderRec?.prescriber_id,
                patient_id: orderRec.patient_id,
                insurance_id: insuranceRec.id,
                route_id: orderItemRec?.route_id,
            };

            return testClaimDefaults;
        } catch (e) {
            const errorMessage = `Error encountered while generating test claim view.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a test claim view for a prescription
     * @async
     * @param {Object} rxRec - The prescription record to generate test claim for
     * @returns {Promise<Object>} The test claim view response wrapper
     * @throws {Error} If there is an error generating the test claim view
     */
    async generateRxTestClaimView(rxRec) {
        console.debug(
            `Generating test claim for prescription number ${rxRec.rx_no}`
        );
        try {
            const sql = `
                SELECT 
                    billing_method_id,
                    insurance_id
                FROM vw_rx_order rx
                WHERE rx.rx_no = %L`;
            const rows = await this.db.env.rw.query(sql, [rxRec.rx_no]);
            if (rows.length === 0) {
                return ActionResponseWrappers.error(
                    `Unable to locate prescription with number ${rxRec.rx_no}.`
                );
            }
            const billingMethodId = rows[0].billing_method_id;
            const insuranceId = rows[0].insurance_id;
            if (billingMethodId !== "ncpdp") {
                return ActionResponseWrappers.error(
                    `Unable to generate test claim, primary insurance is not NCPDP.`
                );
            }
            if (!insuranceId) {
                return ActionResponseWrappers.error(
                    `Unable to generate test claim, cannot find primary insurance for prescription.`
                );
            }
            const testClaimDefaults =
                await this.generatePrescriptionTestClaimView(rxRec);
            const callbackUrl = `/api/form/careplan_order_rx/${rxRec.id}/action/gen_test_claim`;
            return ActionResponseWrappers.create(
                {
                    ...testClaimDefaults,
                },
                "view_billing_claim_test",
                callbackUrl,
                null,
                null,
                null,
                null,
                null,
                DisplayType.MODAL
            );
        } catch (e) {
            const errorMessage = `Error encountered while generating test claim for prescription number ${rxRec.rx_no}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates a view for creating an invoice from split charges
     * @async
     * @param {string} splitInvoiceNo - The split invoice number to generate the view for
     * @returns {Promise<Object>} The action response wrapper with the invoice builder presets
     * @throws {Error} If there's an error building the split charge invoice view
     */
    async generateSplitChargeCreateInvoiceView(splitInvoiceNo) {
        try {
            const filters = [
                `calc_invoice_split_no:${splitInvoiceNo}`,
                `void:!Yes`,
                `invoice_no:null`,
            ];
            const chargeLineRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "ledger_charge_line",
                    {
                        sort: "-id",
                        filter: filters,
                        limit: 1,
                    }
                )
            );
            if (!chargeLineRec) {
                throw this.fx.getClaraError(
                    `Unable to locate charge line with split to build invoice.`
                );
            }
            const invoiceBuilderPresets = {
                site_id: chargeLineRec.site_id,
                ticket_no: chargeLineRec.ticket_no,
                calc_invoice_split_no: splitInvoiceNo,
                insurance_id: chargeLineRec.insurance_id,
                payer_id: chargeLineRec.payer_id,
                patient_id: chargeLineRec.patient_id,
                billing_method_id: chargeLineRec.billing_method_id,
                add_new_type_filter: ["Billable"],
            };
            const callbackUrl = `/api/billing/?func=generate_charge_lines_invoice`;
            return ActionResponseWrappers.create(
                invoiceBuilderPresets,
                "view_create_invoice",
                callbackUrl,
                null,
                null,
                null,
                null,
                null,
                DisplayType.MODAL
            );
        } catch (e) {
            const errorMessage = `Error building split charge invoice view. ${splitInvoiceNo}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates an invoice from the invoice creation view
     * @async
     * @param {Object} invoiceCreationViewRec - The invoice creation view record
     * @returns {Promise<Object>} The generated invoice response
     * @throws {Error} If there is an error generating the invoice
     */
    async generateInvoiceFromInvoiceCreationView(invoiceCreationViewRec) {
        try {
            const billingMethodId = invoiceCreationViewRec.billing_method_id;
            const insuranceChargeLines =
                billingMethodId === BillingMethodType.NCPDP
                    ? invoiceCreationViewRec.charge_line
                    : [
                            BillingMethodType.MAJOR_MEDICAL,
                            BillingMethodType.CMS1500,
                        ].includes(billingMethodId)
                      ? invoiceCreationViewRec.mm_charge_line
                      : invoiceCreationViewRec.generic_charge_line;
            const insuranceChargeLineIds = insuranceChargeLines.map(
                (chargeLine) => {
                    return chargeLine.id;
                }
            );

            const otherChargeLines =
                invoiceCreationViewRec?.other_charge_line || [];
            const otherChargeLineIds = otherChargeLines.map((chargeLine) => {
                return chargeLine.id;
            });
            const allChargeLineIds = [
                ...insuranceChargeLineIds,
                ...otherChargeLineIds,
            ];
            const chargeLineArray = this.fx.pgArrayFormat(allChargeLineIds);
            const splitNo = invoiceCreationViewRec.calc_invoice_split_no;

            let rxChargesAndInvoicesRow = null;

            const sql = `
                    SELECT
                        create_ready_to_bill_invoice(
                        %s::integer,
                        %s::integer,
                        %s::integer,
                        '${chargeLineArray}'::integer[],
                        %s::integer
                    ) as result
                `;

            const insuranceId = invoiceCreationViewRec.insurance_id;
            const patientId = invoiceCreationViewRec.patient_id;
            const siteId = invoiceCreationViewRec.site_id;
            const sqlParams = [
                patientId,
                siteId,
                insuranceId,
                invoiceCreationViewRec?.created_by,
            ];

            rxChargesAndInvoicesRow = await this.db.env.rw.parseSQLUsingPGP(
                sql,
                sqlParams
            );

            if (rxChargesAndInvoicesRow.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating invoice from charge lines.`
                );
            }
            const results = rxChargesAndInvoicesRow[0].result;
            const invoiceRec = results?.invoice_record || null;
            if (!invoiceRec) {
                return ActionResponseWrappers.error(
                    `Error generating invoice record for the selected charge lines. Charge lines split no ${splitNo}`
                );
            }
            const chargeLines = results?.charge_lines || [];
            if (chargeLines.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating updated charge lines for the invoice. Charge lines split no ${splitNo}`
                );
            }
            const { savedInvoiceRec } = await this.__trySaveInvoiceRecords({
                invoiceRec,
                chargeLines,
            });
            if (!savedInvoiceRec) {
                return ActionResponseWrappers.error(
                    `Error saving invoice and charge lines after generation. Charge lines split no ${splitNo}`
                );
            }

            return ActionResponseWrappers.edit(
                savedInvoiceRec.id,
                "billing_invoice",
                {},
                null,
                null,
                null,
                null,
                null,
                DisplayType.TAB,
                NextAction.REFRESH
            );
        } catch (e) {
            const errorMessage = `Error building invoice from invoice creation view.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Converts an encounter record to a billing item.
     * @async
     * @param {Object} encounterId - The encounter record to convert.
     * @returns {Promise<Object>} The converted billing item.
     * @throws {Error} If there's an error during the conversion process.
     */
    async generateNursingVisitChargeLine(encounterId) {
        console.log("Generating Nursing Visit Charge Line");

        try {
            if (!encounterId) {
                throw this.fx.getClaraError(
                    `Missing encounter ID to generate nursing visit charge line.`
                );
            }

            const sql = `
                    SELECT
                        create_nursing_visit_charge_line_json(
                        %s::integer
                    ) as result
                `;
            const sqlParams = [encounterId];
            const result = await this.db.env.rw.parseSQLUsingPGP(
                sql,
                sqlParams
            );
            if (result.length === 0) {
                console.error(
                    `Error generating nursing visit charge line for encounter ID ${encounterId}.`
                );
                return null;
            }
            const chargeLineRec = result[0].result;
            if (!chargeLineRec) {
                console.error(
                    `Missing generated charge line for encounter ID ${encounterId}.`
                );
                return null;
            }
            return chargeLineRec;
        } catch (e) {
            const errorMessage = `Error validating encounter charge line`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Attempts to save invoice records and associated charge lines to the database
     * @async
     * @param {Object} params - The parameters object
     * @param {Object} params.invoiceRec - The invoice record to save
     * @param {Array<Object>} params.chargeLines - Array of charge line records to save
     * @returns {Promise<Object>} Object containing saved claim and invoice records
     * @returns {Object} returns.savedClaimRec - The saved electronic claim record
     * @returns {Object} returns.savedInvoiceRec - The saved invoice record
     * @throws {Error} If there's an error saving the records
     */
    async __trySaveInvoiceRecords({ invoiceRec, chargeLines }) {
        try {
            const transaction = this.db.env.rw.transaction(this.ctx);

            const isMasterInvoice =
                invoiceRec?.master_invoice_no &&
                invoiceRec.master_invoice_no.startsWith(
                    "INVOICE_NO_PLACEHOLDER"
                );
            if (isMasterInvoice) {
                const seriesNumber = transaction.series_next_number("INVOICE");
                invoiceRec.master_invoice_no = seriesNumber;
            }
            const claimRec =
                await this.fetcher.fetchInvoiceElectronicClaimRecord(
                    invoiceRec
                );
            let claimNo = null;
            if (claimRec) {
                claimNo = transaction.series_next_number("CLAIM");
                invoiceRec.claim_no = claimNo;
                claimRec.claim_no = claimNo;
                claimRec.invoice_no = invoiceRec.invoice_no;

                // Extract and set claim_no in medical claim supplemental and service lines
                if (
                    invoiceRec.subform_medical &&
                    Array.isArray(invoiceRec.subform_medical)
                ) {
                    const medicalClaimRec = invoiceRec.subform_medical[0];
                    await this.helper.setMedicalClaimNo(
                        medicalClaimRec,
                        claimNo
                    );
                }
            }

            for (const chargeLine of chargeLines) {
                chargeLine.claim_no = claimNo;
                chargeLine.invoice_no = invoiceRec.invoice_no;
                if (chargeLine.id) {
                    await transaction.update(
                        "ledger_charge_line",
                        chargeLine,
                        chargeLine.id
                    );
                } else {
                    const chargeNo = transaction.series_next_number("CHARGE");
                    chargeLine.master_charge_no =
                        chargeLine.master_charge_no || chargeNo;
                    chargeLine.charge_no = chargeNo;
                    await transaction.insert("ledger_charge_line", chargeLine);
                }
            }
            await transaction.insert("billing_invoice", invoiceRec);
            const res = await transaction.commit();
            if (res.error) throw this.fx.getClaraError(res.message);
            const formIds = this.fx.fetchFormIdsFromTransactionResults(
                "billing_invoice",
                res
            );
            transaction.init();

            const invoiceId = formIds[0];
            const savedInvoiceRec = await this.fetcher.fetchCachedRecord(
                "billing_invoice",
                invoiceId
            );
            const savedClaimRec =
                await this.fetcher.fetchInvoiceElectronicClaimRecord(
                    savedInvoiceRec
                );
            return { savedClaimRec, savedInvoiceRec };
        } catch (e) {
            const errorMessage = `Error saving invoice records.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
