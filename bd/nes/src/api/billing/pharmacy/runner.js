/*jshint : 6 */
"use strict";
const _ = require("lodash");

const ClaimRunnerClass = require("@billing/runner");
const { ErrorMessages } = require("./errors");
const {
    TransactionResponses,
    ClaimResponses,
    TransactionCodes,
    ClaimType,
    ResponseFieldToSegmentMap,
    NCPDPPaidClaimStatus,
} = require("./settings");
const { RecordSchemas } = require("@billing/schemas");
const { ActionResponseWrappers, DisplayType, NextAction } = require("@actions");
/**
 * @class
 * @classdesc Class for running NCPDP claims.
 * @extends ClaimRunnerClass
 */
module.exports = class NCPDPClaimRunnerClass extends ClaimRunnerClass {
    constructor(nes, ctx) {
        super(nes, ctx, "ncpdp");
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.ncpdpClaimErrorMapper = nes.shared.ncpdpClaimErrorMapper;
        const NCPDPFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            NCPDPFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const ClaimValidatorClass = require("@billing/validator");
        this.validator = this.fx.getInstance(
            ctx,
            ClaimValidatorClass,
            true,
            this.nes,
            this.ctx
        );
        const NCPDPPatcherClass = require("./patcher");
        this.patcher = this.fx.getInstance(
            ctx,
            NCPDPPatcherClass,
            true,
            this.nes,
            this.ctx
        );
        this.currentTransaction = TransactionCodes.BILL;
        this.claimResponse = null;
        this.transactionResponse = null;
    }

    /**
     * Runs an NCPDP claim for the given primary payer.
     *
     * @param {Object} ncpdpRec - The NCPDP record.
     * @returns {Promise<Array|Object>} An array containing the processed claim or an error object.
     */
    async runClaim(ncpdpRec) {
        try {
            // TODO Disable for prod
            ncpdpRec.is_test = "Yes";
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to run."
            );
            this.currentTransaction = TransactionCodes.BILL;
            return this.__runClaim(ncpdpRec);
        } catch (e) {
            const errorMessage = `Error encountered running claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Runs an eligibility check for the given NCPDP record.
     *
     * @async
     * @param {Object} ncpdpRec - The NCPDP record.
     * @returns {Promise<Array|Object>} An array containing the processed eligibility check or an error object.
     * @throws {Error} If an error occurs during the eligibility check process.
     */
    async runEligibility(ncpdpRec, bypassValidation = false) {
        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to run eligibility."
            );
            this.currentTransaction = TransactionCodes.ELIGIBILITY;
            const returnVal = await this.__runClaim(ncpdpRec, bypassValidation);
            return returnVal;
        } catch (e) {
            const errorMessage = `Error encountered running claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Reverses a claim.
     * @async
     * @param {Object} ncpdpRec - The NCPDP record object.
     * @returns {Promise<boolean>} A promise that resolves to true if the claim was successfully reversed, false otherwise.
     * @throws {Error} If an error occurs during the reversal process.
     */
    async reverseClaim(ncpdpRec) {
        console.debug(`Reversing claim no ${ncpdpRec.claim_no}`);

        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to reverse."
            );

            this.currentTransaction = TransactionCodes.REVERSE;
            const returnVal = await this.__runClaim(ncpdpRec);
            return returnVal;
        } catch (e) {
            const errorMessage = `Error encountered while reversing claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Rebill a claim.
     * @async
     * @param {Object} ncpdpRec - The NCPDP record object.
     * @returns {Promise<Object>} A promise that resolves to the result of the rebill operation.
     * @throws {Error} If an error occurs during the rebill process.
     */
    async rebillClaim(ncpdpRec) {
        console.debug(`Rebilling claim no ${ncpdpRec.claim_no}`);
        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to rebill."
            );

            this.currentTransaction = TransactionCodes.REBILL;
            const returnVal = await this.__runClaim(ncpdpRec);
            return returnVal;
        } catch (e) {
            const errorMessage = `Error encountered while rebilling claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the success message for a pharmacy claim submission
     * @async
     * @param {Object} _ncpdpRec - The NCPDP claim record
     * @returns {Promise<string>} A promise that resolves to the success message
     */
    async fetchSuccessMessage(_ncpdpRec) {
        if (this.currentTransaction === TransactionCodes.REVERSE) {
            return `Claim has been successfully reversed.`;
        } else if (this.currentTransaction === TransactionCodes.REBILL) {
            return `Claim has been successfully rebilled.`;
        }
        return `Claim has been successfully submitted. Please check response for remittance information`;
    }

    async fetchResponseOverride(ncpdpRec) {
        if (ncpdpRec?.transaction_code === "E1") {
            const e1ResponseSummaryRec =
                await this.fetcher.fetchLastE1ResponseSummary(ncpdpRec);
            if (e1ResponseSummaryRec) {
                return ActionResponseWrappers.edit(
                    e1ResponseSummaryRec.id,
                    "ncpdp_response_elig_summary",
                    {},
                    null,
                    null,
                    null,
                    null,
                    null,
                    DisplayType.MODAL,
                    NextAction.NONE
                );
            }
        }
        return null;
    }

    /**
     * Runs an NCPDP claim for the given primary payer.
     *
     * @param {Object} ncpdpRec - The NCPDP record.
     * @returns {Promise<Array|Object>} An array containing the processed claim or an error object.
     */
    async __runClaim(ncpdpRec, cardFinderOnly = false) {
        console.debug(`Running NCPDP claim for payer ID ${ncpdpRec.payer_id}`);

        try {
            await this.__changeTransactionType(
                ncpdpRec,
                this.currentTransaction
            );

            let payerRec, patientRec;
            if (this.currentTransaction !== TransactionCodes.ELIGIBILITY) {
                [payerRec, patientRec] = await Promise.all([
                    this.fetcher.fetchCachedRecord("payer", ncpdpRec.payer_id),
                    this.fetcher.fetchCachedRecord(
                        "patient",
                        ncpdpRec.patient_id
                    ),
                    this.__preprocessClaim(ncpdpRec),
                ]);
            } else {
                patientRec = await this.fetcher.fetchCachedRecord(
                    "patient",
                    ncpdpRec.patient_id
                );
                if (!cardFinderOnly) {
                    payerRec = await this.fetcher.fetchCachedRecord(
                        "payer",
                        ncpdpRec.payer_id
                    );
                }
            }
            const validatedClaimRec = await this.__validateAndPrepareClaimData(
                ncpdpRec,
                cardFinderOnly
            );
            if (validatedClaimRec.validationErrors)
                return {
                    beforeResponse: true,
                    validationErrors: validatedClaimRec.validationErrors,
                    rawErrors: validatedClaimRec.rawErrorMessages,
                    error: ErrorMessages.CLAIM_VALIDATION_ERROR,
                    record: ncpdpRec,
                };

            const _meta = this.__createMetaData(
                payerRec,
                patientRec,
                ncpdpRec,
                cardFinderOnly
            );

            super.debug(
                `Sending Claim for payer ${payerRec?.organization} and patient ${patientRec?.mrn}`
            );

            const response = await this.__sendClaimRequest(
                validatedClaimRec,
                _meta
            );

            if (response.error) {
                const errorMessage = `Error encountered running claim no ${ncpdpRec.claim_no}`;
                console.error(
                    `${errorMessage} Error: ${JSON.stringify(response.error)}`
                );
                return {
                    beforeResponse: true,
                    validationErrors: {
                        form: [response.error],
                    },
                    rawErrors: [response.error],
                    error: ErrorMessages.NCPDP_RUNNER_EXCEPTION,
                    record: ncpdpRec,
                };
            }

            const responseResult = await this.__processClaimResponse(
                ncpdpRec,
                response,
                cardFinderOnly
            );

            if (cardFinderOnly) {
                return responseResult;
            }
            const wasRejected =
                this.claimResponse === ClaimResponses.Rejected ||
                this.transactionResponse === TransactionResponses.Rejected;
            if (
                wasRejected &&
                this.currentTransaction === TransactionCodes.REVERSE
            ) {
                const errorMessage = `Claim reversal has been rejected.`;
                return {
                    beforeResponse: false,
                    validationErrors: null,
                    rawErrors: [errorMessage],
                    error: errorMessage,
                    record: ncpdpRec,
                };
            }

            if (wasRejected) {
                const [
                    { rejectSubform, rejectMessageSubform },
                    lastResponseMessageSegment,
                ] = await Promise.all([
                    this.fetcher.fetchLastResponseStatusRejectSegment(
                        responseResult
                    ),
                    this.fetcher.fetchLastResponseMessageSegment(
                        responseResult
                    ),
                ]);
                const messages = lastResponseMessageSegment.map(
                    (segment) => segment.message
                );
                const addMessages = rejectMessageSubform.map(
                    (segment) => segment.add_msg
                );
                const allErrorMessages = [...messages, ...addMessages];
                if (rejectSubform && rejectSubform.length > 0) {
                    const [validationErrors, rawErrorCodes] = await Promise.all(
                        [
                            this.ncpdpClaimErrorMapper.mapErrors(
                                this.ctx,
                                rejectSubform,
                                allErrorMessages,
                                ncpdpRec
                            ),
                            this.ncpdpClaimErrorMapper.mapErrorCodes(
                                this.ctx,
                                rejectSubform
                            ),
                        ]
                    );
                    // console.log("rejectSubform", rawErrorCodes);

                    let displayError =
                        allErrorMessages.length > 0
                            ? allErrorMessages.join("\n")
                            : ErrorMessages.CLAIM_REJECTED;

                    if (rejectSubform?.[0]?.reject_code_auto_name?.length > 0) {
                        displayError +=
                            "\n" +
                            rejectSubform[0].reject_code_auto_name.join("\n");
                    }
                    return {
                        beforeResponse: false,
                        validationErrors: validationErrors,
                        rawErrors: rawErrorCodes,
                        error: displayError,
                        record: responseResult,
                    };
                } else {
                    const errorMessages =
                        allErrorMessages.length > 0
                            ? messages
                            : [ErrorMessages.REJECTED_MISSING_ERRORS];
                    return {
                        beforeResponse: false,
                        validationErrors: null,
                        rawErrors: errorMessages,
                        error: errorMessages.join(", "),
                        record: responseResult,
                    };
                }
            }
            return {
                beforeResponse: false,
                validationErrors: null,
                rawErrors: [],
                error: null,
                record: responseResult,
            };
        } catch (e) {
            super.error(
                `Error encountered running claim for payer ID ${ncpdpRec.payer_id} Error: ${e.message} Stack: ${e.stack}`
            );
            return {
                error: ErrorMessages.NCPDP_RUNNER_EXCEPTION,
            };
        }
    }

    /**
     * Validates and prepares claim data.
     *
     * @param {Object} ncpdpRec - The NCPDP record.
     * @returns {Promise<Object>} Validated claim data or error object.
     * @private
     */
    async __validateAndPrepareClaimData(ncpdpRec, bypassValidation = false) {
        const ncpdpRecCopy = JSON.parse(JSON.stringify(ncpdpRec));
        const results = await this.validator.validateClaim(
            ncpdpRecCopy,
            ClaimType.NCPDP,
            bypassValidation
        );
        if (results?.validationErrors) return results;
        // Transform inline subforms
        for (const [key, value] of Object.entries(results)) {
            if (Array.isArray(value) && value.length === 1) {
                results[key] = value[0];
            }
        }

        // Swap patient_claim_id for patient_id (DSL error if patient_id is not linked to patient record)
        if (results.segment_patient?.patient_claim_id) {
            results.segment_patient.patient_id =
                results.segment_patient.patient_claim_id;
            delete results.segment_patient.patient_claim_id;
        }

        return results;
    }

    /**
     * Creates metadata for the claim.
     *
     * @param {Object} payerRec - The payer record.
     * @param {Object} patientRec - The patient record.
     * @param {Object} ncpdpRecCopy - The validated claim data.
     * @returns {Object} Metadata object.
     * @private
     */
    __createMetaData(
        payerRec,
        patientRec,
        ncpdpRecCopy,
        cardFinderOnly = false
    ) {
        const _meta = {
            clara_mrn: patientRec?.mrn,
            is_test:
                ncpdpRecCopy.is_test === "Yes" ||
                this.shared.config.env["NODE_ENV"] !== "production",
            send_group_in_reversal:
                payerRec?.transmit_grp_no_reversal === "Yes",
            send_card_id_in_reversal:
                payerRec?.transmit_card_id_reversal === "Yes",
        };
        // console.log(`Meta Data: ${JSON.stringify(_meta)}`);
        if (!cardFinderOnly) _meta["claim_no"] = ncpdpRecCopy.claim_no;
        return _meta;
    }

    /**
     * Sends the claim request.
     *
     * @param {Object} validatedClaim - The validated claim data.
     * @param {Object} _meta - The metadata object.
     * @returns {Promise<Object>} The response from the claim request.
     * @private
     */
    async __sendClaimRequest(validatedClaim, _meta) {
        console.debug(`Sending claim request for claim no ${_meta?.claim_no}`);
        return this.nes.modules.fx.adminRequest(this.ctx, "claims/pharm", {
            method: "POST",
            data: {
                ...validatedClaim,
                _meta,
            },
        });
    }

    /**
     * Pre-process a claim before submission.
     *
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record to preprocess.
     * @returns {Promise<Object|undefined>} Returns an error object if preprocessing fails, otherwise undefined.
     */
    async __preprocessClaim(ncpdpRec) {
        console.debug(`Preprocessing claim no ${ncpdpRec.claim_no}`);

        try {
            if (this.currentTransaction === TransactionCodes.REVERSE) {
                const childCOBClaim =
                    await this.__checkForChildCOBClaimToReverse(ncpdpRec);
                if (childCOBClaim) {
                    const returnVal = await this.reverseClaim(childCOBClaim);
                    if (returnVal.error) {
                        throw this.fx.getClaraError(
                            `Error encountered reversing child COB claim no ${childCOBClaim.claim_no} for claim no ${ncpdpRec.claim_no}`
                        );
                    }
                }
            }
        } catch (e) {
            const errorMessage = `Error encountered preprocessing claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Processes the claim response for a given NCPDP record.
     *
     * @async
     * @param {Object} ncpdpRec - The NCPDP record object.
     * @param {Object} results - The results object containing the claim response.
     * @returns {Promise<Object>} The updated NCPDP record or an error object.
     */
    async __processClaimResponse(ncpdpRec, results, cardFinderOnly = false) {
        console.debug(`Processing results for claim no ${ncpdpRec?.claim_no}`);

        try {
            const claimNo = ncpdpRec.claim_no;
            const patientId = ncpdpRec.patient_id;
            const { jsonResult, responseLog } =
                await this.__validateAndGetJsonResult(results, claimNo);

            await this.__prepareResponseData(jsonResult);
            this.claimResponse = jsonResult.response_status;
            this.transactionResponse = jsonResult.transaction_response_status;
            const res = await this.__insertResponseIntoDatabase(
                jsonResult,
                claimNo,
                patientId,
                responseLog,
                ncpdpRec.bin_number,
                cardFinderOnly
            );
            if (cardFinderOnly) {
                return res;
            }
            const filters = [`id:${ncpdpRec.id}`];
            const updatedNcpdpRec = _.head(
                await this.form.get.get_form(this.ctx, this.ctx.user, "ncpdp", {
                    filter: filters,
                })
            );
            return updatedNcpdpRec;
        } catch (e) {
            const errorMessage = `Error encountered processing claim response for payer ID ${ncpdpRec.payer_id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return {
                error: ErrorMessages.NCPDP_RESPONSE_ERROR,
            };
        }
    }

    /**
     * Validates and retrieves the JSON result from the response.
     *
     * @async
     * @param {Object} results - The results object containing the claim response.
     * @param {string} claimNo - The claim number.
     * @returns {Promise<Object>} The validated JSON result.
     * @throws {Error} If JSON data is missing.
     */
    async __validateAndGetJsonResult(results, claimNo) {
        try {
            const jsonResult = results?.response_json_data || null;
            if (!jsonResult) {
                throw this.fx.getClaraError(
                    ErrorMessages.NCPDP_RESPONSE_MISSING_JSON
                );
            }
            results.claim_no = claimNo;
            const responseLog = _.pick(results, [
                "request_d0_b64",
                "request_d0_raw",
                "request_json_data",
                "response_d0_b64",
                "response_d0_raw",
                "response_json_data",
            ]);
            const reversalInformation = results.reversal_information || null;
            responseLog.reversal_information = reversalInformation;
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("ncpdp_response_log", responseLog);
            const res = await transaction.commit();
            if (res.error) {
                const errorMessage = `Error encountered saving NCPDP response log for claim no ${claimNo}`;
                console.error(`${errorMessage} Error: ${res.error}`);
                throw this.fx.getClaraError(errorMessage);
            }
            transaction.init();

            return { jsonResult, reversalInformation, responseLog };
        } catch (e) {
            const errorMessage = `Error encountered while attempting to validate and get JSON result for claim no ${claimNo} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Prepares the response data by converting to DSL and building show/hide options.
     *
     * @async
     * @param {Object} jsonResult - The JSON result to prepare.
     */
    async __prepareResponseData(jsonResult) {
        try {
            await this.__convertResponseToDSL(jsonResult, "ncpdp_response");
            await Promise.all([
                this.__buildResponseShowHideOptions(jsonResult),
                this.__copyForwardResponseFields(jsonResult),
            ]);
        } catch (e) {
            const errorMessage = `Error encountered while attempting to prepare response data for claim no ${jsonResult.claim_no}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Copies forward specific fields from the JSON result to the top level.
     *
     * @async
     * @param {Object} jsonResult - The JSON result object containing the response data.
     * @throws {Error} If there's an error during the copy forward process.
     */
    async __copyForwardResponseFields(jsonResult) {
        try {
            const _ = require("lodash");

            const fieldsToCopy = [
                "response_pricing[0].total_paid",
                "response_pricing[0].pt_pay_amt",
                "response_msg[0].message",
                "response_stat[0].transaction_response_status",
            ];

            fieldsToCopy.forEach((field) => {
                const value = _.get(jsonResult, field);
                if (value !== undefined) {
                    const newKey = field.split(".").pop();
                    _.set(jsonResult, newKey, value);
                }
            });
        } catch (e) {
            const errorMessage = `Error encountered while attempting to copy forward response fields for claim no ${jsonResult.claim_no}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
    /**
     * Inserts the response into the database.
     *
     * @async
     * @param {Object} jsonResult - The JSON result to insert.
     * @param {string} claimNo - The claim number.
     * @param {number} patientId - The patient ID.
     * @param {Object} responseLog - The response log object.
     * @param {string} binNumber - The bin number.
     * @param {boolean} cardFinderOnly - Whether to return the response log.
     * @throws {Error} If there's an error during database insertion.
     */
    async __insertResponseIntoDatabase(
        jsonResult,
        claimNo,
        patientId,
        responseLog,
        binNumber,
        cardFinderOnly = false
    ) {
        try {
            const transaction = this.db.env.rw.transaction(this.ctx);
            jsonResult.claim_no = claimNo;
            jsonResult.patient_id = patientId;
            jsonResult.request_json_data = JSON.parse(
                JSON.stringify(responseLog.request_json_data)
            );
            jsonResult.response_json_data = JSON.parse(
                JSON.stringify(responseLog.response_json_data)
            );
            jsonResult.request_d0_raw = responseLog.request_d0_raw;
            jsonResult.response_d0_raw = responseLog.response_d0_raw;
            jsonResult.bin_number = binNumber;
            await transaction.insert("ncpdp_response", jsonResult);
            const res = await transaction.commit();
            if (res.error) {
                const errorMessage = `Error encountered processing NCPDP response for claim no ${claimNo}`;
                console.error(`${errorMessage} Error: ${res.error}`);
                throw this.fx.getClaraError(errorMessage);
            }
            transaction.init();

            if (cardFinderOnly) {
                const form = "ncpdp_response";
                const formIds = this.fx.fetchFormIdsFromTransactionResults(
                    form,
                    res
                );
                const formId = formIds[0];
                return formId;
            }
            transaction.init();
            return null;
        } catch (e) {
            const errorMessage = `Error encountered processing NCPDP response for claim no ${claimNo}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Converts the JSON response to DSL format.
     *
     * @async
     * @param {Object} jsonResult - The JSON result to be converted.
     * @returns {Promise<void>}
     */
    async __convertResponseToDSL(jsonResult, form) {
        console.debug(`Converting response to DSL for claim and form ${form}`);

        try {
            const formDSL = this.shared.DSL[form];

            for (const [fieldName, fieldConfig] of Object.entries(
                formDSL.fields
            )) {
                const fieldVal = jsonResult[fieldName];
                if (fieldConfig.model && fieldConfig.model.type === "subform") {
                    if (
                        fieldVal &&
                        typeof fieldVal === "object" &&
                        !Array.isArray(fieldVal)
                    ) {
                        const source = fieldConfig.model.source;
                        await this.__convertResponseToDSL(fieldVal, source);
                        jsonResult[fieldName] = [fieldVal];
                    }
                } else if (
                    fieldVal &&
                    Array.isArray(fieldVal) &&
                    !fieldConfig.model.multi &&
                    !fieldConfig.model.source
                )
                    jsonResult[fieldName] = fieldVal[0];
            }
        } catch (e) {
            const errorMessage = `Error encountered while attempting to convert response to DSL Form (${form})`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Builds response show/hide options based on the JSON result.
     *
     * @async
     * @param {Object} jsonResult - The JSON result object to analyze.
     * @returns {Promise<void>}
     */
    async __buildResponseShowHideOptions(jsonResult) {
        console.debug(`Building response show hide options`);

        try {
            const showHideOptions = [];
            for (const [field, segment] of Object.entries(
                ResponseFieldToSegmentMap
            )) {
                const result = jsonResult[field];
                if (
                    Array.isArray(result) &&
                    result.length > 0 &&
                    result.some((obj) =>
                        Object.values(obj).some((val) => val !== null)
                    )
                ) {
                    showHideOptions.push(segment);
                } else if (
                    result &&
                    typeof result === "object" &&
                    Object.values(result).some((val) => val !== null)
                ) {
                    showHideOptions.push(segment);
                }
            }
            jsonResult.show_segments = showHideOptions;
        } catch (e) {
            const errorMessage = `Error encountered while attempting to build response show hide options`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Changes the transaction type of a claim.
     * @param {Object} ncpdpRec - The claim record to be modified.
     * @param {string} transactionCode - The new transaction code to be set.
     */
    async __changeTransactionType(ncpdpRec, transactionCode) {
        console.debug(`Changing transaction type of claim`);

        try {
            function deepInspectAndChange(obj) {
                _.forOwn(obj, (value, key) => {
                    if (key === "transaction_code") {
                        obj[key] = transactionCode;
                    } else if (
                        _.isObject(value) &&
                        key !== "response_json_data" &&
                        key !== "request_json_data"
                    ) {
                        deepInspectAndChange(value);
                    }
                });
            }

            deepInspectAndChange(ncpdpRec);
        } catch (e) {
            const errorMessage = `Error encountered while changing transaction type of claim.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks for the existence of a child Coordination of Benefits (COB) claim.
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record to check for a child COB claim.
     * @returns {Promise<Object|null>} The child claim record if it exists and is paid, otherwise null.
     * @throws {Error} If an error occurs during the check process.
     */
    async __checkForChildCOBClaimToReverse(ncpdpRec) {
        console.debug(
            `Checking for child COB claim for claim no ${ncpdpRec.claim_no}`
        );

        try {
            const childClaimNo = ncpdpRec?.parent_claim_no;
            if (!childClaimNo) return;
            const childClaimRec = await this.fetcher.fetchClaimWithClaimNumber(
                "ncpdp",
                childClaimNo
            );
            if (!childClaimRec) return;
            const childClaimIsPaid = NCPDPPaidClaimStatus.includes(
                childClaimRec.status
            );
            if (childClaimIsPaid) return childClaimRec;
            return null;
        } catch (e) {
            const errorMessage = `Error encountered while checking for child COB claim for claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks for and applies a claim patch based on past payable test claims.
     * @async
     * @param {Object} ncpdpRec - The NCPDP record to check and potentially patch.
     * @returns {Promise<void>}
     */
    async __checkForClaimPatch(ncpdpRec) {
        console.debug(
            `Building patch from past payable test claim for prescription RX No:${ncpdpRec?.rx_no}`
        );

        try {
            const mostRecentClaim = await this.__fetchLastPaidClaim(ncpdpRec);
            if (!mostRecentClaim) return;

            await this.patcher.applyClaimPatch(ncpdpRec, mostRecentClaim);
        } catch (e) {
            const errorMessage = `Error encountered while building patch for claim.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches past claims based on the given NCPDP record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP record to base the fetch on.
     * @returns {Promise<Array>} An array of past claims.
     * @private
     */
    async __fetchLastPaidClaim(ncpdpRec) {
        const filters = [
            `rx_no:${ncpdpRec.rx_no}`,
            `payer_id:${ncpdpRec.payer_id}`,
            "status:Payable",
            "void:!Yes",
        ];
        const results = await this.form.get.get_form(
            this.ctx,
            this.ctx.user,
            "ncpdp",
            { limit: 1, filter: filters, sort: "-date_of_service" }
        );
        return results;
    }
};
