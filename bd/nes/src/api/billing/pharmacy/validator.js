"use strict";
const _ = require("lodash");

const { Mo<PERSON>leLogger } = require("@core/logger");
const { RecordSchemas } = require("@billing/schemas");
const { TransactionCodes } = require("./settings");

/**
 * @class NCPDPValidatorClass
 * @classdesc Class for validating NCPDP claims within the pharmacy billing module.
 * @extends ModuleLogger
 */
module.exports = class NCPDPValidatorClass extends ModuleLogger {
    constructor(nes, ctx) {
        super("ncpdp-validator");
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        const NCPDPCheckerClass = require("./checker");
        this.checker = this.fx.getInstance(
            ctx,
            NCPDPCheckerClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingFetcherClass = require("@billing/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Validates an NCPDP claim by checking various required fields and conditions.
     * @param {Object} ncpdpRec - The NCPDP claim object to validate.
     * @returns {Promise<Object>} The validation result containing transformed data and field errors.
     * @throws {Error} If validation fails or encounters errors.
     */
    async validateClaim(ncpdpRec, bypassValidation = false) {
        super.debug(`Validating claim ${ncpdpRec.claim_no} before running`);

        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing pharmacy claim record to validate."
            );
            const fetchedData = await this.__fetchRequiredData(
                ncpdpRec,
                bypassValidation
            );
            if (bypassValidation) {
                fetchedData["insuranceRec"] = {};
                fetchedData["insuranceRec"]["bin"] = ncpdpRec.bin_number;
                fetchedData["insuranceRec"]["pcn"] =
                    ncpdpRec.process_control_number;
                delete ncpdpRec["claim_no"];
            } else {
                const res = await this.__performValidationChecks(
                    fetchedData,
                    ncpdpRec,
                    bypassValidation
                );
                if (!bypassValidation && res?.error?.length > 0) {
                    return res;
                }
            }

            const claimNo = ncpdpRec?.claim_no;
            const results = await this.__validateSchema(ncpdpRec, claimNo);
            if (bypassValidation) {
                results.validationErrors = null;
                results.rawErrorMessages = null;
            }
            return results;
        } catch (e) {
            const errorMessage = `Exception while validating ncpdpclaim no ${ncpdpRec?.claim_no}`;
            super.error(
                `${errorMessage}. Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            return { error: errorMessage };
        }
    }

    /**
     * Fetches all required data for claim validation.
     * @param {Object} ncpdpRec - The NCPDP claim object.
     * @returns {Promise<Object>} Object containing all fetched data.
     * @private
     */
    async __fetchRequiredData(ncpdpRec, bypassValidation = false) {
        let insuranceRec = null,
            payerRec = null,
            siteRec = null;
        if (!bypassValidation)
            [{ insuranceRec, payerRec }, siteRec] = await Promise.all([
                this.fetcher.fetchPayerAndInsuranceRec(ncpdpRec.insurance_id),
                this.fetcher.fetchCachedRecord("site", ncpdpRec.site_id),
            ]);
        const rxNo = ncpdpRec?.rx_no || null;
        const careplanPrescriptionRec = rxNo
            ? _.head(
                  await this.form.get.get_form(
                      this.ctx,
                      this.ctx.user,
                      "careplan_order_rx",
                      { filter: [`rx_no:${rxNo}`] }
                  )
              )
            : null;
        const claimSegment = ncpdpRec.segment_claim
            ? ncpdpRec.segment_claim[0]
            : null;
        const priorAuthId = claimSegment?.pa_id || null;
        const priorAuthRec = priorAuthId
            ? await this.fetcher.fetchCachedRecord(
                  "patient_prior_auth",
                  priorAuthId
              )
            : null;

        return {
            insuranceRec,
            payerRec,
            siteRec,
            careplanPrescriptionRec,
            priorAuthRec,
        };
    }

    /**
     * Performs all validation checks for the claim.
     * @param {Object} fetchedData - Object containing all fetched data.
     * @param {Object} ncpdpRec - The NCPDP claim object.
     * @throws {Error} If any validation check fails.
     * @private
     */
    async __performValidationChecks(fetchedData, ncpdpRec) {
        const {
            insuranceRec,
            payerRec,
            siteRec,
            careplanPrescriptionRec,
            priorAuthRec,
        } = fetchedData;
        const dateOfService = ncpdpRec?.date_of_service;

        const checkTasks = [
            this.checker.checkBin(insuranceRec?.bin || payerRec?.bin),
            this.checker.checkNCPDPSupportedClaimType(
                ncpdpRec?.transaction_code
            ),
            this.checker.checkInsuranceStatus(insuranceRec, dateOfService),
            this.checker.checkBillingMethod(payerRec?.billing_method_id),
            this.checker.checkMedicare(insuranceRec, siteRec),
            priorAuthRec
                ? this.checker.checkPriorAuth(priorAuthRec)
                : Promise.resolve(),
        ];

        if (ncpdpRec.order_id && ncpdpRec.rx_no) {
            checkTasks.push(
                this.checker.checkOrderItemExpiration(
                    careplanPrescriptionRec,
                    dateOfService
                )
            );
        }

        const results = await Promise.allSettled(checkTasks);
        const error =
            results
                .filter((result) => result.status === "rejected")
                .map((result) => result.reason.message) || null;

        if (error.length > 0) {
            return {
                error,
            };
        }
    }

    /**
     * Validates the NCPDP claim against the schema.
     * @param {Object} ncpdpRec - The NCPDP claim object to validate.
     * @returns {Promise<Object>} The validation result containing transformed data and field errors.
     * @throws {Error} If schema validation fails.
     * @private
     */
    async __validateSchema(ncpdpRec) {
        try {
            ncpdpRec = await this.__restructureCompoundSegment(ncpdpRec);
            ncpdpRec = this.fx.removeNulls(ncpdpRec);
            const schema =
                ncpdpRec.transaction_code === TransactionCodes.REVERSE
                    ? "ncpdp-reverse"
                    : "ncpdp";
            const validate =
                await this.shared.schema.getSchemaValidator(schema);
            const success = validate(ncpdpRec);
            if (!success) {
                super.debug(
                    `Validation failed with the following errors. ${JSON.stringify(validate.errors)}`
                );
                const { validationErrors, rawErrorMessages } =
                    await this.shared.schema.processValidationErrors(
                        "ncpdp",
                        validate.errors,
                        validate
                    );
                return {
                    transformedData: ncpdpRec,
                    validationErrors,
                    rawErrorMessages,
                };
            }
            return {
                transformedData: ncpdpRec,
                validationErrors: null,
                rawErrorMessages: null,
            };
        } catch (e) {
            const errorMessage = `Error validating schema for claim no ${ncpdpRec?.claim_no}`;
            super.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Restructures the compound segment of an NCPDP claim.
     * @param {Object} ncpdpRec - The NCPDP claim object to restructure.
     * @returns {Object} The NCPDP claim with restructured compound segment.
     * @throws {Error} If restructuring fails.
     * @private
     */
    async __restructureCompoundSegment(ncpdpRec) {
        try {
            if (
                !ncpdpRec.segment_compound ||
                ncpdpRec.segment_compound?.length === 0
            )
                return ncpdpRec;
            const segmentCompound = [
                {
                    // Required from schema
                    comp_dsg_fm_code: ncpdpRec.comp_dsg_fm_code || null,
                    comp_disp_unit: ncpdpRec.comp_disp_unit || "1", // Each
                    compound_ingredient_component_count:
                        ncpdpRec.segment_compound?.length || null,
                    subform_compound: [...(ncpdpRec.segment_compound || [])],
                },
            ];
            return {
                ...ncpdpRec,
                segment_compound: segmentCompound,
            };
        } catch (e) {
            const errorMessage = `Error restructuring compound segment for claim no ${ncpdpRec?.claim_no}`;
            super.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
        }
    }
};
