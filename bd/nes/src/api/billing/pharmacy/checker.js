/*jshint : 6 */
"use strict";

const BillingCheckerClass = require("@billing/checker");
const Joi = require("joi");

const { NCPDP_TRANSACTIONS } = require("./settings");

/**
 * @class
 * @classdesc Class for checking NCPDP related claims
 * @extends BillingCheckerClass
 */
module.exports = class NCPDPCheckerClass extends BillingCheckerClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const NCPDPFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            NCPDPFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Checks the BIN (Bank Identification Number) from the insurance object.
     * @param {string} binNo - The BIN number.
     */
    async checkBin(binNo) {
        console.debug(`Checking BIN #`);
        try {
            const binSchema = Joi.string()
                .length(6)
                .pattern(/^[0-9]+$/)
                .required();
            await this.fx.validateSchema(
                "binNo",
                binSchema,
                binNo,
                "Missing BIN number to check."
            );
        } catch (e) {
            console.error(e);
            throw this.fx.getClaraError(
                `Insurance record has an invalid BIN. BIN must be a 6-digit number.`
            );
        }
    }

    /**
     * Checks the PCN (Processor Control Number) from the insurance object.
     * @param {string} pcnNo - The PCN number.
     */
    async checkPcn(pcnNo) {
        console.debug(`Checking PCN #`);

        try {
            const pcnSchema = Joi.string().min(1).max(10).required();
            await this.fx.validateSchema(
                "pcnNo",
                pcnSchema,
                pcnNo,
                "Missing PCN number to check."
            );
        } catch (e) {
            console.error(e);
            throw this.fx.getClaraError(
                `Insurance record has an invalid PCN. PCN is required.`
            );
        }
    }

    /**
     * Checks the billing method of the payer to ensure it is set up as a pharmacy payer.
     * @param {string} billingMethod - The billing method.
     */
    async checkBillingMethod(billingMethod) {
        console.debug(`Checking Payer Billing Method`);
        const billingMethodSchema = Joi.string().valid("ncpdp").required();

        try {
            await this.fx.validateSchema(
                "billingMethod",
                billingMethodSchema,
                billingMethod,
                "Missing billing method to check."
            );
        } catch (e) {
            console.error(e);
            throw this.fx.getClaraError(
                `Payer is not setup as a pharmacy payer. The billing method must be 'ncpdp'.`
            );
        }
    }

    /**
     * Checks if the claim type is supported.
     *
     * @param {string} transactionCode - The transaction code.
     * @returns {boolean} - Returns true if the claim type is supported, otherwise false.
     */
    async checkNCPDPSupportedClaimType(transactionCode) {
        console.debug(`Checking Claim Type`);

        try {
            await this.fx.validateSchema(
                "transactionCode",
                Joi.string().required(),
                transactionCode,
                "Missing transaction code to check."
            );
            return NCPDP_TRANSACTIONS.includes(transactionCode);
        } catch (e) {
            console.error(e);
            throw this.fx.getClaraError(
                `Claim type is not supported by pharmacy payer. The transaction code must be one of the following: ${NCPDP_TRANSACTIONS.join(
                    ", "
                )}.`
            );
        }
    }
};
