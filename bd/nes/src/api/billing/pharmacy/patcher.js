/*jshint : 6 */
"use strict";

const _ = require("lodash");

/**
 * @class
 * @classdesc Helper class for NCPDP billing processes.
 * @extends NCPDPPatcherClass
 */
module.exports = class NCPDPPatcherClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    /**
     * Applies the claim patch to the given NCPDP record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP record to apply the patch to.
     * @param {Object} mostRecentClaim - The most recent claim to build the patch from.
     * @returns {Promise<void>}
     * @private
     */
    async applyClaimPatch(ncpdpRec, mostRecentClaim) {
        console.log(
            `Applying claim patch from claim no ${mostRecentClaim.claim_no}`
        );
        const claimPatch =
            await this.__buildPatchObjectFromClaimRec(mostRecentClaim);
        await this.__applyPatch(ncpdpRec, claimPatch);
        ncpdpRec.patch_applied = claimPatch;
    }

    /**
     * Applies a patch object to a target object recursively.
     * @async
     * @param {Object} target - The target object to apply the patch to.
     * @param {Object} patch - The patch object containing the changes to apply.
     * @returns {Promise<Object>} The updated target object after applying the patch.
     */
    async __applyPatch(target, patch) {
        for (const [key, value] of Object.entries(patch)) {
            if (Array.isArray(value)) {
                if (!target[key]) {
                    target[key] = [];
                }
                await Promise.all(
                    value.map(async (patchItem, index) => {
                        const targetItem = target[key][index] || {};
                        if (
                            Object.keys(patchItem).length === 1 &&
                            Object.values(patchItem)[0] !== null
                        ) {
                            // This is a linked item
                            const linkKey = Object.keys(patchItem)[0];
                            const existingItem = target[key].find(
                                (item) => item[linkKey] === patchItem[linkKey]
                            );
                            if (existingItem) {
                                Object.assign(existingItem, patchItem);
                            } else {
                                target[key].push(patchItem);
                            }
                        } else {
                            await this.__applyPatch(targetItem, patchItem);
                            if (index >= target[key].length) {
                                target[key].push(targetItem);
                            } else {
                                target[key][index] = targetItem;
                            }
                        }
                    })
                );
            } else if (typeof value === "object" && value !== null) {
                if (!target[key]) {
                    target[key] = {};
                }
                await this.__applyPatch(target[key], value);
            } else {
                target[key] = value;
            }
        }
        return target;
    }

    /**
     * Fetches the patch schema for a given form.
     * @async
     * @param {string} form - The form to fetch the patch schema for.
     * @param {boolean} [copyAllFields=false] - Whether to copy all fields or only those marked for copying.
     * @returns {Promise<Object>} The patch schema object.
     */
    async __getPatchSchema(form, copyAllFields = false) {
        try {
            const patchObj = {};
            const jsonObj = this.shared.DSL[form];

            for (const [fieldName, fieldConfig] of Object.entries(
                jsonObj.fields
            )) {
                const copyForward =
                    fieldConfig.model._meta?.copy_forward || false;
                if (fieldConfig.model && fieldConfig.model.type === "subform") {
                    const sourceForm = fieldConfig.model.source;
                    const patchInfo = await this.__getPatchSchema(
                        sourceForm,
                        copyForward
                    );
                    if (patchInfo && Object.keys(patchInfo).length > 0) {
                        patchObj[fieldName] = patchInfo;
                    }
                } else if (copyAllFields) {
                    patchObj[fieldName] = null;
                } else if (copyForward) {
                    const linkBy = fieldConfig.model._meta?.link;
                    patchObj[fieldName] = { link: linkBy };
                }
            }

            return patchObj;
        } catch (e) {
            const errorMessage = `Error encountered while fetching ncpdp patch schema`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Builds a patch object from a given NCPDP claim record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record to build the patch from.
     * @returns {Promise<Object>} The constructed patch object.
     */
    async __buildPatchObjectFromClaimRec(ncpdpRec) {
        console.debug(
            `Building patch from previous claim data:${ncpdpRec?.claim_no}`
        );

        try {
            const patchSchema = await this.__getPatchSchema("ncpdp");
            const results = await this.__createPatchObject(
                patchSchema,
                ncpdpRec
            );
            return results;
        } catch (e) {
            const errorMessage = `Error encountered while building patch from previous claim data:${ncpdpRec?.claim_no}`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Creates a patch object based on the provided schema and record.
     * @param {Object} schema - The schema defining the structure of the patch.
     * @param {Object} record - The record containing the data to be patched.
     * @returns {Promise<Object>} The created patch object.
     */
    async __createPatchObject(schema, record) {
        const patch = {};

        for (const [key, value] of Object.entries(schema)) {
            if (value === null) {
                patch[key] = _.cloneDeep(record[key]);
            } else if (typeof value === "object") {
                if ("link" in value) {
                    if (Array.isArray(record[key])) {
                        patch[key] = record[key].map((item) => ({
                            [value.link]: item[value.link],
                        }));
                    } else if (record[key]) {
                        patch[key] = { [value.link]: record[key][value.link] };
                    }
                } else {
                    if (Array.isArray(record[key])) {
                        patch[key] = await Promise.all(
                            record[key].map((item) =>
                                this.__createPatchObject(value, item)
                            )
                        );
                    } else if (record[key]) {
                        patch[key] = await this.__createPatchObject(
                            value,
                            record[key]
                        );
                    }
                }
            }
        }

        return patch;
    }
};
