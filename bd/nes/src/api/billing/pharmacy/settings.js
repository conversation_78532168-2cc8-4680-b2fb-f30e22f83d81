"use strict";
const billingSettings = require("@billing/settings");

const TransactionCodes = {
    ELIGIBILITY: "E1",
    BILL: "B1",
    REVERSE: "B2",
    REBILL: "B3",
    SERVICE: "S1",
    SERVICE_REVERSE: "S2",
    SERVICE_REBILL: "S3",
};

const TransactionResponses = {
    Accepted: "A",
    Benefit: "B",
    Captured: "C",
    DuplicatePaid: "D",
    PADeferred: "F",
    Paid: "P",
    DuplicateCaptured: "Q",
    Rejected: "R",
    DuplicateApproved: "S",
};

const ClaimResponses = {
    Accepted: "A",
    Rejected: "R",
};

const ResponseFieldToSegmentMap = {
    response_insur: "Insurance",
    response_docs: "Insurance Additional Information",
    response_claim: "Claim",
    response_pricing: "Pricing",
    response_dur: "DUR/PPS",
    response_cob: "COB",
    response_pat: "Patient",
    response_msg: "Message",
};

const NCPDPClaimStatus = {
    CAPTURED: "Captured",
    BENEFIT: "Benefit",
    APPROVED: "Approved",
    PAID: "Payable",
    REJECTED: "Rejected",
    REVERSED: "Reversed",
    REVERSAL_REJECTED: "Reversal Rejected",
    REBILL_REJECTED: "Rebill Rejected",
    DEFERRED: "PA Deferred",
    DUPLICATE: "Duplicate",
    MARGIN: "Margin",
};

const NCPDPPaidClaimStatus = [
    NCPDPClaimStatus.PAID,
    NCPDPClaimStatus.CAPTURED,
    NCPDPClaimStatus.REVERSAL_REJECTED,
    NCPDPClaimStatus.REBILL_REJECTED,
    NCPDPClaimStatus.DUPLICATE,
    NCPDPClaimStatus.MARGIN,
    NCPDPClaimStatus.DEFERRED,
];

const NCPDPOpenClaimStatus = [
    NCPDPClaimStatus.REVERSAL_REJECTED,
    NCPDPClaimStatus.REBILL_REJECTED,
    NCPDPClaimStatus.DEFERRED,
    NCPDPClaimStatus.APPROVED,
    NCPDPClaimStatus.REJECTED,
    NCPDPClaimStatus.REVERSED,
    NCPDPClaimStatus.BENEFIT,
];

const NCPDPCOBEligibleClaimStatus = [
    NCPDPClaimStatus.PAID,
    NCPDPClaimStatus.CAPTURED,
    NCPDPClaimStatus.REVERSAL_REJECTED,
    NCPDPClaimStatus.REBILL_REJECTED,
    NCPDPClaimStatus.MARGIN,
    NCPDPClaimStatus.DEFERRED,
    NCPDPClaimStatus.APPROVED,
    NCPDPClaimStatus.REJECTED,
];

const NCPDP_TRANSACTIONS = [
    TransactionCodes.ELIGIBILITY,
    TransactionCodes.BILL,
    TransactionCodes.REBILL,
    TransactionCodes.REVERSE,
];

const TEST_DATA_PATCH = {
    svc_prov_id_qualifier: "01",
    segment_insurance: {
        card_holder_id: "123456789", //"1111111111",
    },
    bin_number: "610144",
    process_control_number: "D0TEST",
    svc_prov_id: "1199999990",
};

const ClaimActions = {
    SUBMIT: "Submit",
    REBILL: "Rebill",
    REVERSE: "Reverse",
    SAVE_SUBMIT: "Save & Submit",
    NCPDP_DX: "DX / Measures",
    NCPDP_DUR: "DUR/PPS",
    NCPDP_DX_READ: "View DX / Measures",
    NCPDP_DUR_READ: "View DUR/PPS",
};

const ClaimActionKeys = {
    [ClaimActions.SUBMIT]: "ncpdp_submit",
    [ClaimActions.REBILL]: "ncpdp_rebill",
    [ClaimActions.REVERSE]: "ncpdp_reverse",
    [ClaimActions.SAVE_SUBMIT]: "ncpdp_save_submit",
    [ClaimActions.NCPDP_DX]: "ncpdp_dx",
    [ClaimActions.NCPDP_DUR]: "ncpdp_dur",
    [ClaimActions.NCPDP_DX_READ]: "ncpdp_dx_read",
    [ClaimActions.NCPDP_DUR_READ]: "ncpdp_dur_read",
};

module.exports = {
    TEST_DATA_PATCH,
    TransactionCodes,
    NCPDP_TRANSACTIONS,
    TransactionResponses,
    NCPDPClaimStatus,
    NCPDPPaidClaimStatus,
    NCPDPOpenClaimStatus,
    NCPDPCOBEligibleClaimStatus,
    ClaimActions,
    ClaimActionKeys,
    ClaimResponses,
    ResponseFieldToSegmentMap,
    ...billingSettings,
};
