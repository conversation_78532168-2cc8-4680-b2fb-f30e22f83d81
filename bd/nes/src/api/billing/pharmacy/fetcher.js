/*jshint : 6 */
"use strict";

const _ = require("lodash");
const Joi = require("joi").extend(require("@joi/date"));

const BillingFetcherClass = require("@billing/fetcher");

const { RecordSchemas } = require("@billing/schemas");
const { TransactionResponses } = require("./settings");

/**
 * @class
 * @classdesc Fetcher class for NCPDP billing processes.
 * @extends BillingFetcherClass
 */
module.exports = class NCPDPFetcherClass extends BillingFetcherClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
    }

    /**
     * Fetches a claim record with the given claim number.
     * @param {string} claimNo - The claim number to search for.
     * @returns {Promise<Object|null>} The claim record if found, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchClaimWithClaimNumber(claimNo) {
        await this.fx.validateSchema(
            "claimNo",
            Joi.string().min(1).required(),
            claimNo,
            "Missing claim number to fetch claim."
        );
        const filters = [`claim_no:${claimNo}`];
        const claimRec = _.head(
            await this.form.get.get_form(this.ctx, this.ctx.user, "ncpdp", {
                limit: 1,
                filter: filters,
            })
        );
        return claimRec;
    }

    /**
     * Fetches the last E1 response summary for a given NCPDP claim record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record.
     * @returns {Promise<Object|null>} The last response status segment if available, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchLastE1ResponseSummary(ncpdpRec) {
        console.debug(
            `Fetching last E1 response summary for claim no ${ncpdpRec.claim_no}`
        );
        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to fetch last response message segment."
            );
            const claimNo = ncpdpRec.claim_no;
            const filters = [`claim_no:${claimNo}`];
            const e1ResponseSummaryRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "ncpdp_response_elig_summary",
                    {
                        limit: 1,
                        filter: filters,
                        sort: "-id",
                    }
                )
            );
            return e1ResponseSummaryRec;
        } catch (e) {
            const errorMessage = `Error encountered while fetching last response status reject segment.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the last response status reject segment for a given NCPDP claim record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record.
     * @returns {Promise<Object|null>} The last response status reject segment if available, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchLastResponseStatusRejectSegment(ncpdpRec) {
        console.debug(
            `Fetching last response status reject segment for claim no ${ncpdpRec.claim_no}`
        );

        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to fetch last response status reject segment."
            );

            const lastClaimResponse =
                await this.fetchLastNCPDPClaimResponse(ncpdpRec);
            if (!lastClaimResponse) return null;

            const responseStatusSegment =
                lastClaimResponse?.response_stat[0] || null;
            const responseStatus =
                responseStatusSegment?.transaction_response_status || null;
            const rejectSubform = responseStatusSegment?.subform_reject || [];
            const rejectMessageSubform =
                responseStatusSegment?.subform_msg || [];
            if (
                responseStatus !== TransactionResponses.Rejected ||
                rejectSubform.length === 0
            )
                return { rejectSubform: [], rejectMessageSubform: [] };

            return { rejectSubform, rejectMessageSubform };
        } catch (e) {
            const errorMessage = `Error encountered while fetching last response status reject segment.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches the last response message segment for a given NCPDP claim record.
     * @async
     * @param {Object} ncpdpRec - The NCPDP claim record.
     * @returns {Promise<Object|null>} The last response message segment if available, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async fetchLastResponseMessageSegment(ncpdpRec) {
        console.debug(
            `Fetching last response message segment for claim no ${ncpdpRec.claim_no}`
        );

        try {
            await this.fx.validateSchema(
                "ncpdpRec",
                RecordSchemas.pharmacyClaim.required(),
                ncpdpRec,
                "Missing NCPDP claim record to fetch last response message segment."
            );

            const lastClaimResponse =
                await this.fetchLastNCPDPClaimResponse(ncpdpRec);
            if (!lastClaimResponse) return [];

            const responseMessageSegment =
                lastClaimResponse?.response_msg || [];

            return responseMessageSegment;
        } catch (e) {
            const errorMessage = `Error encountered while fetching last response status reject segment.`;
            console.error(
                `${errorMessage} Error:${e.message} Stack:${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches a card finder response record with the given response ID.
     * @async
     * @param {number} responseId - The ID of the card finder response record.
     * @returns {Promise<Object|null>} The card finder response record if found, or null if not found.
     * @throws {Error} If there's an error during the fetch operation.
     */
    async getCardFinderResponse(responseId) {
        console.debug(
            `Fetching card finder response for response ID ${responseId}`
        );
        const filters = [`response_id:${responseId}`];
        const responseRec = _.head(
            await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "ncpdp_response_cf_summary",
                { limit: 1, filter: filters }
            )
        );
        return responseRec;
    }
};
