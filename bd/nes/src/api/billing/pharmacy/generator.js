/*jshint : 6 */
"use strict";
const _ = require("lodash");
const uuid = require("uuid").v4;

const BillingGeneratorClass = require("@billing/generator");
const Joi = require("joi").extend(require("@joi/date"));

/**
 * @class
 * @classdesc Class for generating NCPDP segments for billing processes.
 * @extends BillingGeneratorClass
 */
module.exports = class NCPDPGeneratorClass extends BillingGeneratorClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;

        const NCPDPFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            NCPDPFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const NCPDPClaimRunnerClass = require("./runner");
        this.runner = this.fx.getInstance(
            ctx,
            NCPDPClaimRunnerClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Generates a test claim record and inserts it into the database
     * @async
     * @param {Object} ncpdpTestClaimRec - The NCPDP test claim record containing claim details
     * @returns {Promise<void>}
     * @throws {Error} If there is an error generating or inserting the test claim
     */
    async generateTestClaim(ncpdpTestClaimRec) {
        console.debug(`Generating test claim`);

        try {
            const sql = `
                SELECT create_test_claim(%s::integer, %s::integer, %s::integer, %s::integer, %s::integer, %s::integer, %L::text, %L::date, %L::integer, %L::integer, %L::text, %L::text, %L::text, %L::text, %L::text, %s::integer, %s::integer, %L::text) AS result
            `;

            const queryParams = [
                ncpdpTestClaimRec.insurance_id,
                ncpdpTestClaimRec.payer_id,
                ncpdpTestClaimRec.patient_id,
                ncpdpTestClaimRec.site_id,
                ncpdpTestClaimRec.prescriber_id,
                ncpdpTestClaimRec.inventory_id,
                ncpdpTestClaimRec.route_id || null,
                ncpdpTestClaimRec.date_of_service,
                ncpdpTestClaimRec.day_supply,
                ncpdpTestClaimRec.dispense_quantity,
                ncpdpTestClaimRec.rx_no || null,
                ncpdpTestClaimRec.compound_code || null,
                ncpdpTestClaimRec.compound_type || null,
                ncpdpTestClaimRec.comp_dsg_fm_code || null,
                ncpdpTestClaimRec.comp_disp_unit || null,
                ncpdpTestClaimRec.order_item_id || null,
                ncpdpTestClaimRec.orderp_item_id || null,
                ncpdpTestClaimRec.order_no || null,
            ];

            const rows = await this.db.env.rw.parseSQLUsingPGP(
                sql,
                queryParams
            );
            if (rows.length === 0)
                throw this.fx.getClaraError(
                    "Error encountered while building test claim."
                );
            const records = rows[0].result;
            const chargeLine = records.charge_lines?.[0];
            if (!chargeLine)
                throw this.fx.getClaraError(
                    "Error encountered while generating test charge line."
                );
            chargeLine.claim_no = uuid();
            const ncpdpRec = records.ncpdp_claim;
            if (!ncpdpRec)
                throw this.fx.getClaraError(
                    "Error encountered while generating test claim record."
                );
            ncpdpRec.claim_no = chargeLine.claim_no;
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("ncpdp", ncpdpRec);
            await transaction.insert("test_charge_line", chargeLine);
            const res = await transaction.commit();
            transaction.init();

            if (res.error) {
                console.error(
                    `Error encountered while inserting test claim records: ${res.message}`
                );
                throw this.fx.getClaraError(res.message);
            }
            const form = "ncpdp";
            const formIds = this.fx.fetchFormIdsFromTransactionResults(
                form,
                res
            );
            const updatedNcpdpRec = _.head(
                await this.fetcher.fetchCachedRecords(form, formIds)
            );
            if (!updatedNcpdpRec)
                throw this.fx.getClaraError(
                    "Error encountered while fetching test claim form record after save."
                );
            const returnVal = await this.runner.runClaim(updatedNcpdpRec);
            return returnVal;
        } catch (e) {
            const errorMessage = `Error encountered while generating test claim.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates eligibility for a given insurance ID.
     *
     * @async
     * @params {Object} transaction - The transaction object.
     * @param {number} insuranceId - The ID of the insurance to generate eligibility for.
     * @returns {Promise<Object>} The NCPDP record with the eligibility response.
     * @throws {Error} If there's an error during the eligibility generation process.
     */
    async generateEligibility(insuranceId) {
        console.debug(
            `Generating eligibility for insurance ID: ${insuranceId}`
        );

        try {
            await this.fx.validateSchema(
                "insuranceId",
                Joi.number().required(),
                insuranceId,
                "Missing insurance ID to generate eligibility."
            );
            const { insuranceRec, payerRec, siteRec } =
                await this.__fetchEligibilityRecords(insuranceId);
            const ncpdpRec = await this.__buildEligibilityRecord(
                siteRec,
                insuranceRec,
                payerRec
            );
            const result = await this.runner.runEligibility(ncpdpRec);
            return result;
        } catch (e) {
            const errorMessage = `Error encountered while generating eligibility check.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Generates CardFinder for a given patient ID.
     *
     * @async
     * @params {Object} transaction - The transaction object.
     * @param {number} siteID - The ID of the site to generate eligibility for.
     * @param {number} patientID - The ID of the insurance to generate eligibility for.
     * @param {Object} data - The data object containing the type, subscriber_ssn, and subscriber_name.
     * @returns {Promise<Object>} The NCPDP record with the eligibility response.
     * @throws {Error} If there's an error during the eligibility generation process.
     */
    async generateCardFinder(siteID, patientID, data) {
        console.debug(
            `Generating Card Finder NCPDP Req for patient ID: ${patientID}`
        );

        try {
            const ncpdpRec = await this.__buildCardFinderRecord(
                siteID,
                patientID,
                data
            );
            const responseId = await this.runner.runEligibility(ncpdpRec, true);
            const responseRec =
                await this.fetcher.getCardFinderResponse(responseId);
            return responseRec;
        } catch (e) {
            const errorMessage = `Error encountered while generating eligibility check.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Fetches eligibility records based on the provided insurance ID.
     *
     * @param {string|number} insuranceId - The ID of the insurance to fetch records for.
     * @returns {Promise<Object>} An object containing insurance, payer, and site records.
     * @throws {Error} If any required record is not found or if there's an error during the fetch process.
     */
    async __fetchEligibilityRecords(insuranceId) {
        try {
            const insuranceRec = await this.fetcher.fetchCachedRecord(
                "patient_insurance",
                insuranceId
            );
            if (!insuranceRec)
                throw this.fx.getClaraError(
                    `Insurance ID ${insuranceId} not found`
                );

            const [payerRec, patientRec] = await Promise.all([
                this.fetcher.fetchCachedRecord("payer", insuranceRec.payer_id),
                this.fetcher.fetchCachedRecord(
                    "patient",
                    insuranceRec.patient_id
                ),
            ]);

            if (!payerRec)
                throw this.fx.getClaraError(
                    `Payer ID ${insuranceRec.payer_id} not found`
                );

            if (!patientRec)
                throw this.fx.getClaraError(
                    `Patient ID ${insuranceRec.patient_id} not found`
                );
            const siteRec = await this.fetcher.fetchCachedRecord(
                "site",
                patientRec.site_id
            );
            if (!siteRec)
                throw this.fx.getClaraError(
                    `Site ID ${patientRec.site_id} not found`
                );

            return { insuranceRec, payerRec, siteRec };
        } catch (e) {
            const errorMessage = `Error encountered while fetching eligibility records.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Builds an eligibility record for pharmacy billing.
     *
     * @async
     * @param {Object} siteRec - The site object.
     * @param {Object} insuranceRec - The insurance object.
     * @param {Object} payerRec - The payer object.
     * @returns {Promise<Object>} The generated and saved eligibility record.
     * @throws {Error} If there's an error during the eligibility record building process.
     */
    async __buildEligibilityRecord(siteRec, insuranceRec, payerRec) {
        try {
            const sql = `
                SELECT build_ncpdp_eligibility_claim(%s::integer, %s::integer, %s::integer, %s::integer) AS result
            `;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                insuranceRec.patient_id,
                siteRec.id,
                insuranceRec.id,
                payerRec.id,
            ]);
            const ncpdpRec = rows[0]?.result;
            if (!ncpdpRec)
                throw this.fx.getClaraError(
                    "Error encountered while building eligibility record."
                );
            const transaction = this.db.env.rw.transaction(this.ctx);
            const cleanNcpdpRec = this.fx.removeNulls(ncpdpRec);
            cleanNcpdpRec.claim_no = transaction.series_next_number("CLAIM");
            await transaction.insert("ncpdp", cleanNcpdpRec);
            const res = await transaction.commit();
            if (res.error) throw this.fx.getClaraError(res.message);
            transaction.init();

            const recId = res[0].id;
            const savedNcpdpRec = await this.fetcher.fetchCachedRecord(
                "ncpdp",
                recId
            );
            return savedNcpdpRec;
        } catch (e) {
            const errorMessage = `Error encountered while building eligibility record.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Builds a CardFinder record for NCPDP eligibility check.
     *
     * @async
     * @param {number} siteID - The ID of the site.
     * @param {number} patientID - The ID of the patient.
     * @param {object} cardFinderViewData - The cardfinder view data.
     * @returns {Promise<Object>} The generated and saved CardFinder record.
     * @throws {Error} If there's an error during the CardFinder record building process.
     */
    async __buildCardFinderRecord(siteID, patientID, cardFinderViewData) {
        try {
            const isCommercialOnly = cardFinderViewData.type === "COMMCF";
            const cardHolderId =
                cardFinderViewData?.subscriber_ssn?.slice(-4) || null;
            const patientFirstName =
                cardFinderViewData?.subscriber_first_name || null;
            const patientLastName =
                cardFinderViewData?.subscriber_last_name || null;
            const patientDob = cardFinderViewData?.subscriber_dob || null;
            const patientGender = cardFinderViewData?.subscriber_gender || null;
            const patientZipCode = cardFinderViewData?.subscriber_zip || null;
            const sql = `
                SELECT build_ncpdp_cardfinder_claim(%s::integer, %s::integer, %L::text, %L::text, %L::date, %L::text, %L::text, %L::text, ${isCommercialOnly ? "TRUE" : "FALSE"}) AS result
            `;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [
                patientID,
                siteID,
                patientFirstName,
                patientLastName,
                patientDob,
                patientGender,
                patientZipCode,
                cardHolderId,
            ]);
            const ncpdpRec = rows[0]?.result;
            if (!ncpdpRec)
                throw this.fx.getClaraError(
                    "Error encountered while building CardFinder record."
                );
            const transaction = this.db.env.rw.transaction(this.ctx);
            const cleanNcpdpRec = this.fx.removeNulls(ncpdpRec);
            cleanNcpdpRec.claim_no = transaction.series_next_number("CLAIM");
            await transaction.insert("ncpdp", cleanNcpdpRec);
            const res = await transaction.commit();
            if (res.error) throw this.fx.getClaraError(res.message);
            transaction.init();

            const recId = res[0].id;
            const savedNcpdpRec = await this.fetcher.fetchCachedRecord(
                "ncpdp",
                recId
            );
            return savedNcpdpRec;
        } catch (e) {
            const errorMessage = `Error encountered while building card finder record.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
