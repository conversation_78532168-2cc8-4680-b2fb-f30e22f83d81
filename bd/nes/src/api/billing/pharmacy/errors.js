"use strict";
const { ErrorMessages: billingErrors } = require("@billing/errors");

const ErrorMessages = {
    ...billingErrors,
    NCPDP_RESPONSE_ERROR: `Error encountered during NCPDP claim results processing. Please contact support if issue persist.`,
    NCPDP_RUNNER_EXCEPTION: `Exception encountered during NCPDP claim processing. Please contact support if issue persist.`,
    NCPDP_RESPONSE_MISSING_JSON: `NCPDP claim response missing JSON data.`,
    CLAIM_NOT_FOUND: `Claim not found.`,
    REJECTED_MISSING_ERRORS: `Rejected claim missing error codes. Please view response for more details.`,
};

module.exports = {
    ErrorMessages,
};
