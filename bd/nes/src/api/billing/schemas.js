"use strict";
const Joi = require("joi").extend(require("@joi/date"));

const { RecordSchemas: dispenseRecordSchemas } = require("@dispense/schemas");

const pricingSchema = {
    expected: Joi.number().required(),
    calc_expected_ea: Joi.number().required(),
    expected_ea: Joi.number().required(),
    list: Joi.number().required(),
    list_ea: Joi.number().required(),
};

const taxSchema = {
    sales_tax_basis: Joi.string().valid("02", "03").allow(null).optional(),
    per_sales_tax_rate_used: Joi.number().allow(null).optional(),
    flat_tax_amt: Joi.number().allow(null).optional(),
    sales_tax: Joi.number().allow(null).optional(),
    dispense_fee: Joi.number().allow(null).optional(),
    tax_codes_id: Joi.string().allow(null).optional(),
};

const priorAuthSchema = Joi.object({
    insurance_id: Joi.number().required(),
    status_id: Joi.string().valid("5").required(),
    number: Joi.string().required(),
});

const agencySchema = Joi.object({
    type: Joi.string().valid("Nurse", "Agency").required().default("Agency"),
    name: Joi.string().max(64).required(),
    active: Joi.string().valid("Yes").required(),
    site_id: Joi.array().items(Joi.number()).required(),
    agency_does_billing: Joi.string()
        .valid("No", "Yes")
        .required()
        .default("No"),
    rate: Joi.number().precision(2).required().default(0.0),
    mileage_rate: Joi.number().precision(2).required().default(0.0),
    overtime: Joi.string().valid("Yes").optional().allow(null),
    overtime_threshold: Joi.number()
        .integer()
        .min(1)
        .when("overtime", {
            is: "Yes",
            then: Joi.required(),
            otherwise: Joi.optional().allow(null),
        }),
    overtime_rate: Joi.number()
        .precision(2)
        .default(0.0)
        .when("overtime", {
            is: "Yes",
            then: Joi.required(),
            otherwise: Joi.optional().allow(null),
        }),
});

const postingSchema = Joi.object({
    patient_id: Joi.number().allow(null),
    payer_id: Joi.number().allow(null),
    check_no: Joi.number().required(),
    amount: Joi.number().precision(2).required().default(0.0),
    charge_line_postings: Joi.array()
        .items(
            Joi.object({
                charge_no: Joi.string().required(),
                amount: Joi.number().required().default(0.0),
                outstanding_balance: Joi.number().required().default(0.0),
            })
        )
        .required(),
}).or("patient_id", "payer_id");

const closingSchema = Joi.object({
    start_date: Joi.date().required(),
    end_date: Joi.date().required(),
    associatedRecords: Joi.string().required(),
    locked_period: Joi.string().valid("Yes", "No").required(),
    needs_refreshed: Joi.string().valid("Yes", "No").required(),
});

const closingUnlockSchema = Joi.object({
    unlock_reason_id: Joi.number().required(),
    signature: Joi.string().required(),
});

const closingRelockSchema = Joi.object({
    signature: Joi.string().required(),
});

const billLineSchema = Joi.object({
    patient_id: Joi.number().required(),
    site_id: Joi.number().required(),
    order_id: Joi.number().allow(null).optional(),
    order_item_id: Joi.number().allow(null).optional(),
    rental_id: Joi.number().allow(null).optional(),
    encounter_id: Joi.number().allow(null).optional(),
    frequency_code: Joi.string().allow(null).optional(),
    insurance_id: Joi.number().required(),
    delivery_tick_id: Joi.number().allow(null).optional(),
    date_of_service: Joi.date().format("MM/DD/YYYY").required(),
    charge_quantity: Joi.number().required(),
    day_supply: Joi.number().allow(null).optional(),
    bill_type: Joi.string()
        .valid(
            "Dispense",
            "Per Diem",
            "Nursing Visit",
            "Charge",
            "Copay",
            "DME"
        )
        .allow(null)
        .optional(),
    subform_ingred: Joi.array()
        .items(
            Joi.object({
                inventory_id: Joi.number().required(),
                charge_quantity: Joi.number().required(),
            })
        )
        .allow(null)
        .optional(),
});

const chargeLineSchema = billLineSchema.append({
    billing_unit_id: Joi.string().valid("mL", "each", "gram").optional(),
    billing_method_id: Joi.string()
        .valid("generic", "ncpdp", "mm", "cms1500")
        .required(),
    inventory_id: Joi.number().allow(null).optional(),
    bill_type: Joi.string().required(),
    route_id: Joi.string().optional(),
    prescriber_id: Joi.number().when("billing_method_id", {
        is: "generic",
        then: Joi.optional().allow(null),
        otherwise: Joi.required(),
    }),
    written_date: Joi.date()
        .format("MM/DD/YYYY")
        .when("billing_method_id", {
            is: "generic",
            then: Joi.optional().allow(null),
            otherwise: Joi.required(),
        }),
    fill_number: Joi.number().optional().allow(null),
    order_item_id: Joi.number().allow(null).optional(),
    rx_no: Joi.string().when("order_item_id", {
        is: Joi.exist().not(null),
        then: Joi.required(),
        otherwise: Joi.optional().allow(null),
    }),
    insurance_id: Joi.number().allow(null).optional(),
    parent_invoice_no: Joi.string().allow(null).optional(),
    is_compound: Joi.string()
        .valid("Compound", "Reconstituted")
        .allow(null)
        .optional(),
    subform_ingred: Joi.when("is_compound", {
        is: Joi.exist().not(null),
        then: Joi.array()
            .items(
                Joi.object({
                    inventory_id: Joi.number().required(),
                    expected: Joi.number().required(),
                    charge_quantity: Joi.number().required(),
                })
            )
            .required(),
        otherwise: Joi.allow(null).optional(),
    }),
});

const chargeLineRecords = Joi.object({
    chargeLine: chargeLineSchema.required(),
    siteRec: dispenseRecordSchemas.site.required(),
    payerRec: dispenseRecordSchemas.payer.required(),
    insuranceRec: dispenseRecordSchemas.insurance.required(),
    orderItemRec: dispenseRecordSchemas.orderItem.optional().allow(null),
    orderRec: dispenseRecordSchemas.order.required(),
    patientRec: dispenseRecordSchemas.patient.required(),
});

const invoiceSchema = Joi.object({
    site_id: Joi.number().required(),
    status: Joi.string()
        .valid("Accepted", "Open", "Confirmed", "Voided", "$0")
        .required(),
    invoice_type: Joi.string().valid("Payer", "Patient").required(),
    subform_pharmacy: Joi.array().items(Joi.object()).allow(null).optional(),
    patient_id: Joi.number().required(),
    payer_id: Joi.number().required(),
    insurance_id: Joi.number().required(),
    billing_method_id: Joi.string()
        .valid("ncpdp", "mm", "cms1500", "generic")
        .required(),
    invoice_no: Joi.string().required(),
    void: Joi.string().valid("Yes", null).allow(null).optional(),
    zeroed: Joi.string().valid("Yes", null).allow(null).optional(),
});

const medicalClaimSchema = Joi.object({
    usage_indicator: Joi.string().valid("P", "T").required(),
    service_date: Joi.date().required(),
    status: Joi.string()
        .valid(
            "Pending",
            "Sent",
            "Rejected",
            "Denied",
            "Payable",
            "Partially Paid",
            "Adjusted",
            "In Process",
            "Suspended",
            "Forwarded",
            "Canceled",
            "Duplicate"
        )
        .required(),
    id: Joi.number().required(),
    site_id: Joi.number().required(),
    patient_id: Joi.number().required(),
    insurance_id: Joi.number().required(),
    payer_id: Joi.number().required(),
    trading_partner_service_id: Joi.string().allow(null).optional(),
    trading_partner_name: Joi.string().required(),
    receiver: Joi.alternatives().try(Joi.array(), Joi.object()).required(),
    submitter: Joi.alternatives().try(Joi.array(), Joi.object()).required(),
    pay_to_address: Joi.alternatives()
        .try(Joi.array(), Joi.object())
        .optional(),
    subscriber: Joi.alternatives().try(Joi.array(), Joi.object()).required(),
    dependent_required: Joi.string().valid("No", "Yes").required(),
    dependent: Joi.alternatives()
        .try(Joi.array(), Joi.object())
        .allow(null)
        .optional(),
    claim_information: Joi.alternatives()
        .try(Joi.array(), Joi.object())
        .optional(),
    expected: Joi.number().precision(2).required(),
});

const pharmacyClaimSchema = Joi.object({
    transaction_code: Joi.string().required(),
    status: Joi.string()
        .valid(
            "Approved",
            "Benefit",
            "Payable",
            "Margin",
            "Captured",
            "Rejected",
            "Reversed",
            "Reversal Rejected",
            "Rebill Rejected",
            "PA Deferred",
            "Duplicate"
        )
        .optional()
        .allow(null),
    payer_id: Joi.number().optional().allow(null),
    segment_pricing: Joi.array()
        .items({
            ing_cst_sub: Joi.number().required(),
        })
        .allow(null)
        .optional(),
});

const zeroedSchema = Joi.object({
    zeroed: Joi.string().valid("Yes", null).optional(),
    zeroed_reason_id: Joi.string().when("zeroed", {
        is: "Yes",
        then: Joi.required(),
        otherwise: Joi.allow(null).optional(),
    }),
    zeroed_datetime: Joi.date()
        .format("MM/DD/YYYY HH:mm:ss")
        .when("zeroed", {
            is: "Yes",
            then: Joi.required(),
            otherwise: Joi.allow(null).optional(),
        }),
}).required();

const cashSchema = Joi.object({
    cash_no: Joi.string().required(),
    site_id: Joi.number().required(),
    close_no: Joi.string().optional().allow(null),
    applied_datetime: Joi.date().format("MM/DD/YYYY HH:mm:ss").required(),
    post_datetime: Joi.date().format("MM/DD/YYYY HH:mm:ss").required(),
    payment_method: Joi.string()
        .valid("Check", "Cash", "Credit Card", "Debit Card", "ERN", "Other")
        .required(),
    other_payment_method: Joi.string().optional().allow(null),
    check_no: Joi.string().optional().allow(null),
    amount: Joi.number().required(),
}).required();

const ledgerFinanceSchema = Joi.object({
    batch_no: Joi.string().optional().allow(null),
    invoice_id: Joi.number().optional().allow(null),
    inventory_id: Joi.number().optional().allow(null),
    ticket_no: Joi.string().optional().allow(null),
    ticket_item_no: Joi.number().optional().allow(null),
    invoice_no: Joi.string().optional().allow(null),
    charge_line_id: Joi.number().optional().allow(null),
    charge_no: Joi.string().optional().allow(null),
    account_id: Joi.number().required(),
    credit: Joi.number().required(),
    debit: Joi.number().required(),
    reversal_for_id: Joi.number().optional().allow(null),
    post_datetime: Joi.date().format("MM/DD/YYYY HH:mm:ss").required(),
    transaction_type: Joi.string().required(),
    account_type: Joi.string().required(),
}).required();

const RecordSchemas = {
    ...dispenseRecordSchemas,
    invoice: invoiceSchema,
    priorAuth: priorAuthSchema,
    billLineNoPrice: billLineSchema,
    billLine: billLineSchema.append(pricingSchema).append(taxSchema),
    chargeLine: chargeLineSchema.append(pricingSchema).append(taxSchema),
    chargeLineWithRecords: chargeLineRecords,
    posting: postingSchema.required(),
    closing: closingSchema,
    closingUnlock: closingUnlockSchema,
    closingRelock: closingRelockSchema,
    agency: agencySchema,
    zeroInvoice: zeroedSchema,
    zeroChargeLine: zeroedSchema,
    zeroCash: zeroedSchema,
    cash: cashSchema,
    medicalClaim: medicalClaimSchema,
    pharmacyClaim: pharmacyClaimSchema,
    ledgerFinance: ledgerFinanceSchema,
};

module.exports = {
    RecordSchemas,
};
