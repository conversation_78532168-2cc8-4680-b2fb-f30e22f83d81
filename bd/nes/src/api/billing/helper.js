/*jshint : 6 */
"use strict";

const _ = require("lodash");
const Joi = require("joi");
const moment = require("moment");

const {
    InvoiceStatus,
    BillingMethodType,
    InvoiceActions,
} = require("./settings");
const { NCPDPPaidClaimStatus } = require("@billing/pharmacy/settings");
const {
    MedClaimStatusOpenStatus,
} = require("@billing/medical/electronic/settings");
const { RecordSchemas } = require("./schemas");
const DispenseHelperClass = require("@dispense/helper");

/**
 * @class
 * @classdesc Class responsible for various billing helper functions.
 */
module.exports = class BillingHelperClass extends DispenseHelperClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const BillingFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const ClaimActionHandler = require("./actions/invoice/claim_action_handler");
        this.claimActionHandler = new ClaimActionHandler(this.nes, this.ctx);
    }

    /**
     * Voids an invoice.
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} invoiceRec - The invoice record to be voided.
     * @param {Object} voidData - The data for voiding the invoice.
     * @returns {Promise<Object>} An object indicating success or error.
     * @throws {Error} If an error occurs during the voiding process.
     */
    async voidInvoice(transaction, invoiceRec, voidData) {
        console.debug(
            `Handling void view for invoice no ${invoiceRec.invoice_no}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void invoice."
                ),
                this.fx.validateSchema(
                    "invoiceRec",
                    RecordSchemas.invoice.required(),
                    invoiceRec,
                    "Missing invoice record to void."
                ),
                this.fx.validateSchema(
                    "voidData",
                    RecordSchemas.void.required(),
                    voidData,
                    "Missing void data to void invoice."
                ),
            ]);

            if (voidData.void !== "Yes") {
                return { success: "No action taken as void is not requested." };
            }

            if (invoiceRec.void === "Yes") {
                return { success: "Invoice already voided" };
            }
            if (invoiceRec.zeroed === "Yes") {
                return {
                    error: `Invoice is already zeroed out, cannot void.`,
                };
            }
            if (invoiceRec.revenue_accepted_posted === "Yes") {
                return {
                    error: `Invoice #${invoiceRec.invoice_no} has already been accepted and posted, cannot void. Please zero out invoice to reverse charges.`,
                };
            }
            if (invoiceRec.close_no) {
                return {
                    error: `Invoice #${invoiceRec.invoice_no} is already closed for the period, cannot void.`,
                };
            }
            if (invoiceRec.locked === "Yes") {
                const timeZone = this.ctx.user.timezone;
                const lockedTime = moment(invoiceRec.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                return {
                    error: `Cannot void invoice #${invoiceRec.invoice_no} as it was locked by ${invoiceRec.locked_by_auto_name} on ${lockedTime}`,
                };
            }
            const res = await this.attemptReverseInvoiceClaim(invoiceRec);
            if (res?.error) {
                return res;
            }
            await this.__updateInvoiceToVoidStatus(
                transaction,
                invoiceRec,
                voidData
            );
            return {
                success: `Invoice no ${invoiceRec.invoice_no} has been successfully voided.`,
            };
        } catch (error) {
            const errorMessage = `Error encountered while voiding invoice id ${invoiceRec?.id}`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Voids a charge line.
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} chargeLineRec - The charge line record to be voided.
     * @param {Object} voidData - The data for voiding the charge line.
     * @returns {Promise<Object>} An object indicating success or error.
     * @throws {Error} If an error occurs during the voiding process.
     */
    async voidChargeLine(transaction, chargeLineRec, voidData) {
        console.debug(
            `Handling void view for charge line no ${chargeLineRec.charge_no}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void invoice."
                ),
                this.fx.validateSchema(
                    "invoiceRec",
                    RecordSchemas.chargeLine.required(),
                    chargeLineRec,
                    "Missing charge line record to void."
                ),
                this.fx.validateSchema(
                    "voidData",
                    RecordSchemas.void.required(),
                    voidData,
                    "Missing void data to void invoice."
                ),
            ]);

            if (voidData.void !== "Yes") {
                return { success: "No action taken as void is not requested." };
            }

            if (chargeLineRec.void === "Yes") {
                return { success: "Charge line already voided" };
            }
            if (chargeLineRec.zeroed === "Yes") {
                return {
                    error: `Charge line is already zeroed out, cannot void.`,
                };
            }
            if (chargeLineRec.revenue_accepted_posted === "Yes") {
                return {
                    error: `Charge line #${chargeLineRec.charge_no} has already been accepted and posted, cannot void. Please zero out charge line to reverse charges.`,
                };
            }
            if (chargeLineRec.close_no) {
                return {
                    error: `Charge line #${chargeLineRec.charge_no} is already closed for the period, cannot void.`,
                };
            }
            if (chargeLineRec.locked === "Yes") {
                const timeZone = this.ctx.user.timezone;
                const lockedTime = moment(chargeLineRec.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                return {
                    error: `Cannot void charge line #${chargeLineRec.charge_no} as it was locked by ${chargeLineRec.locked_by_auto_name} on ${lockedTime}`,
                };
            }

            await this.__updateChargeLineToVoidStatus(
                transaction,
                chargeLineRec,
                voidData
            );
            return {
                success: `Charge line no ${chargeLineRec.charge_no} has been successfully voided.`,
            };
        } catch (error) {
            const errorMessage = `Error encountered while voiding charge line id ${chargeLineRec?.id}`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Generates estimated pricing for a prescription
     *
     * @param {number} rxId - The ID of the prescription to generate pricing for
     * @returns {Promise<Object>} - The estimated pricing information for the prescription
     * @throws {Error} - If the prescription ID is invalid or pricing cannot be generated
     */
    async generateEstimatedRxPricing(rxId) {
        console.debug(
            `Generating estimated pricing for prescription ID ${rxId}`
        );
        try {
            await this.fx.validateSchema(
                "rxId",
                Joi.number().required(),
                rxId,
                "Missing prescription ID to generate estimated pricing."
            );

            const sql = `
                SELECT get_rx_pricing_json(%s::integer) AS result
            `;

            const rows = await this.db.env.rw.parseSQLUsingPGP(sql, [rxId]);
            const returnVal = rows[0].result;

            return returnVal;
        } catch (e) {
            const errorMessage = `Error encountered while generating estimated pricing for prescription ID ${rxId}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Zeros out an invoice
     *
     * @param {Object} transaction - The transaction object required for database operations
     * @param {Object} invoiceRec - The invoice record to be zeroed out
     * @param {Object} zeroData - Data containing zeroing information
     * @returns {Promise<Object>} - Result of the zero operation
     * @throws {Error} - If any validation fails or zeroing cannot be completed
     */
    async zeroInvoice(transaction, invoiceRec, zeroData) {
        console.debug(
            `Handling zero view for invoice no ${invoiceRec.invoice_no}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void invoice."
                ),
                this.fx.validateSchema(
                    "invoiceRec",
                    RecordSchemas.invoice.required(),
                    invoiceRec,
                    "Missing invoice record to void."
                ),
                this.fx.validateSchema(
                    "zeroData",
                    RecordSchemas.zeroInvoice.required(),
                    zeroData,
                    "Missing zero data to zero invoice."
                ),
            ]);

            if (zeroData.zeroed !== "Yes") {
                return { success: "No action taken as zero is not selected." };
            }

            if (invoiceRec.void === "Yes") {
                return { success: "Invoice already voided" };
            }
            if (invoiceRec.zeroed === "Yes") {
                return {
                    error: `Invoice is already zeroed out, cannot zero again.`,
                };
            }
            if (invoiceRec.revenue_accepted_posted !== "Yes") {
                return {
                    error: `Invoice #${invoiceRec.invoice_no} does not have posted revenue, cannot zero out. Please use void instead.`,
                };
            }
            if (invoiceRec.locked === "Yes") {
                const timeZone = this.ctx.user.timezone;
                const lockedTime = moment(invoiceRec.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                return {
                    error: `Cannot zero invoice #${invoiceRec.invoice_no} as it was locked by ${invoiceRec.locked_by_auto_name} on ${lockedTime}`,
                };
            }
            const res = await this.attemptReverseInvoiceClaim(invoiceRec);
            if (res?.error) {
                return res;
            }
            const invoiceUpdate = {
                status: InvoiceStatus.ZEROED,
                ...zeroData,
            };
            await transaction.update(
                "billing_invoice",
                invoiceUpdate,
                invoiceRec.id
            );

            return {
                success: `Invoice no ${invoiceRec.invoice_no} has been successfully zeroed out.`,
            };
        } catch (error) {
            const errorMessage = `Error encountered while zeroing out invoice id ${invoiceRec?.id}`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Zeros out an invoice
     *
     * @param {Object} transaction - The transaction object required for database operations
     * @param {Object} invoiceRec - The invoice record to be zeroed out
     * @param {Object} zeroData - Data containing zeroing information
     * @returns {Promise<Object>} - Result of the zero operation
     * @throws {Error} - If any validation fails or zeroing cannot be completed
     */
    async zeroChargeLine(transaction, chargeLineRec, zeroData) {
        console.debug(
            `Handling zero view for charge line no ${chargeLineRec.charge_no}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void invoice."
                ),
                this.fx.validateSchema(
                    "chargeLineRec",
                    RecordSchemas.chargeLine.required(),
                    chargeLineRec,
                    "Missing charge line record to zero."
                ),
                this.fx.validateSchema(
                    "zeroData",
                    RecordSchemas.zeroChargeLine.required(),
                    zeroData,
                    "Missing zero data to zero charge line."
                ),
            ]);

            if (zeroData.zeroed !== "Yes") {
                return { success: "No action taken as zero is not selected." };
            }

            if (chargeLineRec.void === "Yes") {
                return { success: "Charge line already voided" };
            }
            if (chargeLineRec.zeroed === "Yes") {
                return {
                    error: `Charge line is already zeroed out, cannot zero again.`,
                };
            }
            if (chargeLineRec.revenue_accepted_posted !== "Yes") {
                return {
                    error: `Charge line #${chargeLineRec.charge_no} does not have posted revenue, cannot zero out. Please use void instead.`,
                };
            }
            if (chargeLineRec.locked === "Yes") {
                const timeZone = this.ctx.user.timezone;
                const lockedTime = moment(chargeLineRec.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                return {
                    error: `Cannot zero charge line #${chargeLineRec.charge_no} as it was locked by ${chargeLineRec.locked_by_auto_name} on ${lockedTime}`,
                };
            }

            const chargeLineUpdate = {
                ...zeroData,
            };
            await transaction.update(
                "ledger_charge_line",
                chargeLineUpdate,
                chargeLineRec.id
            );

            return {
                success: `Charge line no ${chargeLineRec.charge_no} has been successfully zeroed out.`,
            };
        } catch (error) {
            const errorMessage = `Error encountered while zeroing out charge line id ${chargeLineRec?.id}`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Zeros out a cash record
     *
     * @param {Object} transaction - The transaction object required for database operations
     * @param {Object} cashRec - The cash record to be zeroed out
     * @param {Object} zeroData - Data containing zeroing information
     * @returns {Promise<Object>} - Result of the zero operation
     * @throws {Error} - If any validation fails or zeroing cannot be completed
     */
    async zeroCash(transaction, cashRec, zeroData) {
        console.debug(`Handling void view for cash no ${cashRec.cash_no}`);

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void invoice."
                ),
                this.fx.validateSchema(
                    "cashRec",
                    RecordSchemas.cash.required(),
                    cashRec,
                    "Missing cash record to zero."
                ),
                this.fx.validateSchema(
                    "zeroData",
                    RecordSchemas.zeroCash.required(),
                    zeroData,
                    "Missing zero data to zero cash."
                ),
            ]);

            if (zeroData.zeroed !== "Yes") {
                return { success: "No action taken as zero is not selected." };
            }

            if (cashRec.zeroed === "Yes") {
                return {
                    error: `Cash is already zeroed out, cannot zero again.`,
                };
            }

            const cashUpdate = {
                ...zeroData,
            };
            await transaction.update("billing_cash", cashUpdate, cashRec.id);

            return {
                success: `Cash no ${cashRec.cash_no} has been successfully zeroed out.`,
            };
        } catch (error) {
            const errorMessage = `Error encountered while zeroing out cash id ${cashRec?.id}`;
            console.error(
                `${errorMessage} Error: ${error.message} Stack: ${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(error, errorMessage);
        }
    }

    /**
     * Voids a delivery ticket and associated invoices.
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} deliveryTicketRec - The delivery ticket record to be voided.
     * @param {Object} voidData - The data for voiding.
     * @returns {Promise<Object>} A promise that resolves to an object with success or error message.
     * @throws {Error} If an error occurs during the voiding process.
     */
    async voidDeliveryTicket(transaction, deliveryTicketRec, voidData) {
        console.log(`Voiding delivery ticket no ${deliveryTicketRec.id}`);

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void delivery ticket."
                ),
                this.fx.validateSchema(
                    "deliveryTicketRec",
                    RecordSchemas.deliveryTicket.required(),
                    deliveryTicketRec,
                    "Missing delivery ticket record to void."
                ),
                this.fx.validateSchema(
                    "voidData",
                    RecordSchemas.void.required(),
                    voidData,
                    "Missing void data to void delivery ticket."
                ),
            ]);
            const updatedDeliveryTicketRec = {
                ...deliveryTicketRec,
                ...voidData,
                archived: true,
            };
            await transaction.update(
                "careplan_delivery_tick",
                updatedDeliveryTicketRec,
                deliveryTicketRec.id
            );

            return {
                success: `Delivery ticket no ${deliveryTicketRec.id} has been successfully voided.`,
            };
        } catch (e) {
            const errorMessage = `Error encountered while voiding delivery ticket no ${deliveryTicketRec.id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Voids an order.
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} orderRec - The order record to be voided.
     * @param {Object} voidData - The data for voiding the order.
     * @returns {Promise<Object>} A promise that resolves to an object with success or error message.
     * @throws {Error} If an error occurs during the voiding process.
     */
    async voidOrder(transaction, orderRec, voidData) {
        console.log(`Voiding order no ${orderRec.id}`);

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to void order."
                ),
                this.fx.validateSchema(
                    "orderRec",
                    RecordSchemas.order.required(),
                    orderRec,
                    "Missing order record to void."
                ),
                this.fx.validateSchema(
                    "voidData",
                    RecordSchemas.void.required(),
                    voidData,
                    "Missing void data to void order."
                ),
            ]);
            await transaction.update(
                "careplan_order",
                {
                    ...orderRec,
                    ..._.omit(voidData, "void_warnings"),
                    archived: true,
                },
                orderRec.id
            );
            return {
                success: `Order no ${orderRec.id} has been successfully voided.`,
            };
        } catch (e) {
            const errorMessage = `Error encountered while voiding order no ${orderRec.id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks if a claim is reversible based on the billing method.
     * @async
     * @param {Object} invoiceRec - The invoice record.
     * @returns {Promise<boolean>} True if the claim is reversible, false otherwise.
     */
    async claimIsReversible(invoiceRec) {
        console.debug("Checking if claim is reversible");
        try {
            if (invoiceRec.billing_method_id === BillingMethodType.NCPDP) {
                const claimRec =
                    await this.fetcher.fetchInvoiceElectronicClaimRecord(
                        invoiceRec
                    );

                const isPaidClaim = claimRec
                    ? NCPDPPaidClaimStatus.includes(claimRec?.status)
                    : false;
                return isPaidClaim;
            } else if (
                invoiceRec.billing_method_id === BillingMethodType.MAJOR_MEDICAL
            ) {
                const claimRec =
                    await this.fetcher.fetchInvoiceElectronicClaimRecord(
                        invoiceRec
                    );
                const claimStatusIsOpen = claimRec
                    ? MedClaimStatusOpenStatus.includes(claimRec?.status)
                    : false;
                return !claimStatusIsOpen;
            }
        } catch (e) {
            const errorMessage = `Error encountered while reversing invoice claim for invoice id ${invoiceRec.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Attempts to reverse an invoice claim if it's reversible.
     * @async
     * @param {Object} invoiceRec - The invoice record to reverse.
     * @returns {Promise<Object|null>} The result of the reverse action or null if claim is not reversible.
     * @throws {Error} If an error occurs during the reversal process.
     */
    async attemptReverseInvoiceClaim(invoiceRec) {
        console.debug(
            `Reversing invoice claim for invoice id ${invoiceRec.id}`
        );

        try {
            const claimIsReversible = await this.claimIsReversible(invoiceRec);
            if (!claimIsReversible) return null;
            const res = await this.claimActionHandler.runAction(
                invoiceRec,
                InvoiceActions.REVERSE
            );
            return res;
        } catch (e) {
            const errorMessage = `Error encountered while reversing invoice claim for invoice id ${invoiceRec.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Sets the claim number on a medical claim record and all its related records
     * @async
     * @param {Object} medClaimRec - The medical claim record to update
     * @param {string} claimNo - The claim number to set
     * @throws {Error} If there is an error setting the claim number
     */
    async setMedicalClaimNo(medClaimRec, claimNo) {
        try {
            // Set claim_number in supplemental information
            if (
                medClaimRec.claim_information &&
                Array.isArray(medClaimRec.claim_information)
            ) {
                for (const claimInfo of medClaimRec.claim_information) {
                    if (
                        claimInfo.claim_supplemental_information &&
                        Array.isArray(claimInfo.claim_supplemental_information)
                    ) {
                        for (const suppInfo of claimInfo.claim_supplemental_information) {
                            suppInfo.claim_number = claimNo;
                        }
                    }

                    // Set claim_no in service lines
                    if (
                        claimInfo.service_lines &&
                        Array.isArray(claimInfo.service_lines)
                    ) {
                        for (const serviceLine of claimInfo.service_lines) {
                            serviceLine.claim_no = claimNo;
                        }
                    }
                }
            }
        } catch (e) {
            const errorMessage = `Error encountered while setting medical claim no for med claim id ${medClaimRec.id}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
    /**
     * Updates the invoice status after voiding.
     * @private
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} invoiceRec - The invoice record to be updated.
     * @param {Object} voidData - The data for voiding the invoice.
     */
    async __updateInvoiceToVoidStatus(transaction, invoiceRec, voidData) {
        const invoiceUpdate = {
            ..._.omit(voidData, "void_warnings"),
            status: InvoiceStatus.VOIDED,
            archived: true,
        };
        await transaction.update(
            "billing_invoice",
            invoiceUpdate,
            invoiceRec.id
        );
    }

    /**
     * Updates the charge line status after voiding.
     * @private
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} chargeLineRec - The charge line record to be updated.
     * @param {Object} voidData - The data for voiding the charge line.
     */
    async __updateChargeLineToVoidStatus(transaction, chargeLineRec, voidData) {
        const chargeLineUpdate = {
            ...voidData,
            archived: true,
        };
        await transaction.update(
            "ledger_charge_line",
            chargeLineUpdate,
            chargeLineRec.id
        );
    }
};
