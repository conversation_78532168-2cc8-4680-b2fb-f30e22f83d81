{"1": {"type": "object", "fields": {"1": {"type": "map", "path": "insurance_type", "default_value": "OTHER", "map": {"Medicare": "MEDICARE", "Medicaid": "MEDICAID", "ChampVA": "CHAMPVA", "Group Health Plan": "GROUP HEALTH PLAN", "FECA Black Lung": "FECA BLK LUNG", "Other": "OTHER"}}, "1a": {"path": "subscriber_member_id"}}, "comment": "Insurance Type/Member ID"}, "2": {"type": "template", "format": "{% if last_name %}{{ last_name | capitalize }}{% endif %}{% if first_name and last_name %}, {% endif %}{% if first_name %}{{ first_name | capitalize }}{% endif %}{% if middle_name %} {{ middle_name | initial }}{% endif %}", "context": [{"condition": "patient_first_name", "fields": {"first_name": {"path": "patient_first_name"}, "last_name": {"path": "patient_last_name"}, "middle_name": {"path": "patient_middle_name"}}}, {"condition": "patient_first_name", "fields": {"first_name": {"path": "patient_first_name"}, "last_name": {"path": "patient_last_name"}, "middle_name": {"path": "patient_middle_name"}}}], "comment": "Patient Name"}, "3": {"type": "object", "fields": {"month": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatMonth }}{% endif %}", "context": {"fields": {"date_of_birth": {"path": "patient_dob"}}}}, "day": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatDay }}{% endif %}", "context": {"fields": {"date_of_birth": {"path": "patient_dob"}}}}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "patient_dob"}}}, "gender": {"path": "patient_gender"}}, "comment": "Patient DOB and Sex"}, "4": {"type": "template", "format": "{% if last_name %}{{ last_name | capitalize }}{% endif %}{% if first_name and last_name %}, {% endif %}{% if first_name %}{{ first_name | capitalize }}{% endif %}{% if middle_name %} {{ middle_name | initial }}{% endif %}", "context": {"fields": {"first_name": {"path": "subscriber_first_name"}, "last_name": {"path": "subscriber_last_name"}, "middle_name": {"path": "subscriber_middle_name"}}}, "comment": "Subscriber Name"}, "5": {"type": "object", "fields": {"address": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "patient_address1"}, "address2": {"path": "patient_address2"}}}}, "city": {"path": "patient_city"}, "state": {"path": "patient_state"}, "zip": {"path": "patient_zip"}, "phone": {"path": "patient_phone_number"}}, "comment": "Patient Address and phone number"}, "6": {"type": "map", "path": "subscriber_relationship_id", "default_value": "Self", "map": {"01": "Spouse", "18": "Self", "19": "Child", "20": "Other", "39": "Other", "40": "Other", "53": "Other", "G8": "Other"}, "comment": "Relationship to subscriber"}, "7": {"type": "object", "condition": "subscriber_address1", "fields": {"address": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "subscriber_address1"}, "address2": {"path": "subscriber_address2"}}}}, "city": {"path": "subscriber_city"}, "state": {"path": "subscriber_state_id"}, "zip": {"path": "subscriber_postal_code"}, "phone": {"path": "subscriber_phone_number"}}, "comment": "Insured Address"}, "9": {"type": "object", "fields": {"9": {"type": "template", "format": "{% if last_name %}{{ last_name | capitalize }}{% endif %}{% if first_name and last_name %}, {% endif %}{% if first_name %}{{ first_name | capitalize }}{% endif %}{% if middle_name %} {{ middle_name | initial }}{% endif %}", "context": {"condition": "cob_last_name", "fields": {"first_name": {"path": "cob_first_name"}, "last_name": {"path": "cob_last_name"}, "middle_name": {"path": "cob_middle_name"}}}}, "9a": {"path": "cob_insurance_group_or_policy_number"}, "9d": {"path": "cob_organization_name"}}, "comment": "Other Insured Info"}, "10": {"type": "object", "fields": {"10a": {"type": "template", "format": "{% if related_cause_code %}{{ related_cause_code | relatedCauseEmploymentCheck }}{% endif %}", "context": {"fields": {"related_cause_code": {"path": "related_cause_code"}}}}, "10b": {"type": "object", "fields": {"check": {"type": "template", "format": "{% if related_cause_code %}{{ related_cause_code | relatedCauseAutoCheck }}{% endif %}", "context": {"fields": {"related_cause_code": {"path": "related_cause_code"}}}}, "place": {"path": "auto_accident_state_code"}}}, "10c": {"type": "template", "format": "{% if related_cause_code %}{{ related_cause_code | relatedCauseAccidentCheck }}{% endif %}", "context": {"fields": {"related_cause_code": {"path": "related_cause_code"}}}}}, "comment": "Conditions"}, "11": {"type": "object", "fields": {"11": {"path": "subscriber_insurance_group_or_policy_number"}, "11a": {"type": "object", "context": {"fields": {"date_of_birth": {"path": "subscriber_dob"}}}, "fields": {"month": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "subscriber_dob"}}}, "gender": {"path": "subscriber_gender"}}}, "11b": {"type": "object", "condition": "other_payer_claim_control_number", "fields": {"qualifier": {"value": "F8"}, "ccn": {"path": "other_payer_claim_control_number"}}}, "11c": {"path": "subscriber_payer_organization_name"}, "11d": {"type": "template", "format": "{% if payment_responsibility_level_code %}{{ payment_responsibility_level_code | otherPayerCheck }}{% endif %}", "context": {"fields": {"payment_responsibility_level_code": {"path": "subscriber_payment_responsibility_level_code"}}}}}, "comment": "Insured Info"}, "12": {"type": "object", "fields": {"signature": {"value": "Signature on File"}, "date": {"path": "patient_signature_date"}}, "comment": "<PERSON><PERSON>'s signature"}, "13": {"value": "Signature on File"}, "14": {"type": "object", "condition": "symptom_date", "context": {"fields": {"symptom_date": {"path": "symptom_date"}}}, "fields": {"month": {"type": "template", "format": "{% if symptom_date %}{{ symptom_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if symptom_date %}{{ symptom_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "symptom_date"}}}, "qualifier": {"value": "431"}}, "comment": "Date of current illness, injury, or pregnancy"}, "15": {"type": "object", "context": {"fields": {"other_date": {"path": "other_date"}}}, "fields": {"month": {"type": "template", "format": "{% if other_date %}{{ other_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if other_date %}{{ other_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "other_date"}}}, "qualifier": {"type": "map", "path": "other_date_type", "map": {"Accident Date": "439", "Last Seen Date": "304", "Acute Manifestation Date": "453", "Initial Treatment Date": "454", "Prescription Date": "471"}}}, "comment": "Other Date"}, "16": {"type": "object", "fields": {"from": {"type": "object", "context": {"fields": {"work_date": {"path": "last_worked_date"}}}, "fields": {"month": {"type": "template", "format": "{% if work_date %}{{ work_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if work_date %}{{ work_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "work_date"}}}}}, "to": {"type": "object", "context": {"fields": {"authorized_return_to_work_date": {"path": "authorized_return_to_work_date"}}}, "fields": {"month": {"type": "template", "format": "{% if authorized_return_to_work_date %}{{ authorized_return_to_work_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if authorized_return_to_work_date %}{{ authorized_return_to_work_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "authorized_return_to_work_date"}}}}}}, "comment": "Dates patient unable to work in current occupation"}, "17": {"type": "object", "fields": {"17": {"type": "object", "fields": {"qualifier": {"type": "function", "function": "fetchProviderNameQualifier", "params": {"payer_id": {"path": "payer_id"}}}, "name": {"type": "template", "format": "{% if first_name %}{{ first_name | capitalize }} {% endif %}{% if last_name %}{{ last_name | capitalize }}{% endif %}", "context": {"fields": {"first_name": {"path": "referring_provider_first_name"}, "last_name": {"path": "referring_provider_last_name"}}}}}}, "17a": {"type": "object", "fields": {"qualifier": {"path": "referring_provider_qualifier"}, "license_no": {"path": "referring_provider_alt_id"}}}, "17b": {"path": "referring_provider_npi"}}, "comment": "Referring provider"}, "18": {"type": "object", "fields": {"from": {"type": "object", "context": {"fields": {"admission_date": {"path": "admission_date"}}}, "fields": {"month": {"type": "template", "format": "{% if admission_date %}{{ admission_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if admission_date %}{{ admission_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "admission_date"}}}}}, "to": {"type": "object", "context": {"fields": {"discharge_date": {"path": "discharge_date"}}}, "fields": {"month": {"type": "template", "format": "{% if discharge_date %}{{ discharge_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if discharge_date %}{{ discharge_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "discharge_date"}}}}}}, "comment": "Hospitalization Dates"}, "19": {"path": "additional_information", "comment": "Additional Claim Information"}, "21": {"type": "object", "fields": {"A": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.0.dx_id"}, "code": {"path": "subform_dx.0.diagnosis_code"}}}, "B": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.1.dx_id"}, "code": {"path": "subform_dx.1.diagnosis_code"}}}, "C": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.2.dx_id"}, "code": {"path": "subform_dx.2.diagnosis_code"}}}, "D": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.3.dx_id"}, "code": {"path": "subform_dx.3.diagnosis_code"}}}, "E": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.4.dx_id"}, "code": {"path": "subform_dx.4.diagnosis_code"}}}, "F": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.5.dx_id"}, "code": {"path": "subform_dx.5.diagnosis_code"}}}, "G": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.6.dx_id"}, "code": {"path": "subform_dx.6.diagnosis_code"}}}, "H": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.7.dx_id"}, "code": {"path": "subform_dx.7.diagnosis_code"}}}, "I": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.8.dx_id"}, "code": {"path": "subform_dx.8.diagnosis_code"}}}, "J": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.9.dx_id"}, "code": {"path": "subform_dx.9.diagnosis_code"}}}, "K": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.10.dx_id"}, "code": {"path": "subform_dx.10.diagnosis_code"}}}, "L": {"type": "function", "function": "formatDiagnosisCode", "params": {"payer_id": {"path": "payer_id"}, "dx_id": {"path": "subform_dx.11.dx_id"}, "code": {"path": "subform_dx.11.diagnosis_code"}}}, "indicator": {"value": "ABF"}}, "comment": "Diagnosis (A-L) and indicator"}, "22": {"type": "object", "fields": {"resubmission_code": {"type": "map", "path": "claim_resubmission_code", "default_value": "", "map": {"7": "7", "8": "8"}}, "original_ref_no": {"path": "claim_control_number"}}, "comment": "Resubmission Code / Original Control Number"}, "23": {"path": "prior_authorization_number", "comment": "Prior Authorization Number"}, "24": {"type": "object", "repeat": {"path_raw": "subform_sl"}, "fields": {"supplemental_info": {"path": "subform_sl.{{idx}}.supplemental_info"}, "from": {"type": "object", "context": {"fields": {"service_date": {"path": "subform_sl.{{idx}}.service_date"}}}, "fields": {"month": {"type": "template", "format": "{% if service_date %}{{ service_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if service_date %}{{ service_date | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "subform_sl.{{idx}}.service_date"}}}}}, "to": {"type": "object", "context": {"fields": {"service_date_end": {"path": "subform_sl.{{idx}}.service_date_end"}}}, "fields": {"month": {"type": "template", "format": "{% if service_date_end %}{{ service_date_end | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if service_date_end %}{{ service_date_end | formatDay }}{% endif %}"}, "year": {"type": "function", "function": "formatYear", "params": {"payer_id": {"path": "payer_id"}, "date": {"path": "subform_sl.{{idx}}.service_date_end"}}}}}, "place_of_service": {"condition": "subform_sl.{{idx}}.service_date", "path": "subform_sl.{{idx}}.place_of_service_code"}, "emg": {"type": "template", "format": "{% if emergency_indicator %}{{ emergency_indicator | convertToX }}{% endif %}", "context": {"fields": {"emergency_indicator": {"path": "subform_sl.{{idx}}.emergency_indicator"}}}}, "cpt_hcpcs": {"path": "subform_sl.{{idx}}.procedure_code"}, "modifier_1": {"path": "subform_sl.{{idx}}.modifier_1"}, "modifier_2": {"path": "subform_sl.{{idx}}.modifier_2"}, "modifier_3": {"path": "subform_sl.{{idx}}.modifier_3"}, "modifier_4": {"path": "subform_sl.{{idx}}.modifier_4"}, "diagnosis_pointer": {"type": "function", "function": "convertDiagnosisPointers", "params": {"reference": {"path_raw": "subform_dx"}, "dx_1": {"path": "subform_sl.{{idx}}.dx_id_1"}, "dx_2": {"path": "subform_sl.{{idx}}.dx_id_2"}, "dx_3": {"path": "subform_sl.{{idx}}.dx_id_3"}, "dx_4": {"path": "subform_sl.{{idx}}.dx_id_4"}}}, "charges": {"type": "template", "format": "{{ charge | formatCurrency }}", "context": {"fields": {"charge": {"path": "subform_sl.{{idx}}.line_item_charge_amount"}}}}, "days_or_units": {"type": "template", "format": "{{ units | formatUnits(3) }}", "context": {"fields": {"units": {"path": "subform_sl.{{idx}}.service_unit_count"}}}}, "EPSDT": {"type": "template", "format": "{% if epsdt_indicator %}{{ epsdt_indicator | convertToX }}{% endif %}", "context": {"fields": {"epsdt_indicator": {"path": "subform_sl.{{idx}}.epsdt_indicator"}}}}, "rendering_provider_npi": {"type": "function", "function": "loadRenderingProviderNPI", "params": {"payer_id": {"path": "payer_id"}, "npi": {"path": "referring_provider_npi"}, "taxonomy_id": {"path": "referring_provider_taxonomy_id"}}}}, "comment": "Service Line"}, "25": {"type": "object", "fields": {"federal_tax_id": {"path": "bill_tax_id"}, "ein": {"value": "X"}}, "comment": "Federal Tax ID, SSN, EIN"}, "26": {"path": "patient_account_number"}, "27": {"value": "YES", "comment": "Accept Assignment"}, "28": {"type": "function", "function": "calculate1500SplitCharges", "params": {"serviceLines": {"path_raw": "subform_sl"}, "payer_id": {"path": "payer_id"}}, "comment": "Total Charge"}, "29": {"type": "function", "function": "calculate1500SplitPaid", "params": {"serviceLines": {"path_raw": "subform_sl"}, "payer_id": {"path": "payer_id"}}, "comment": "Amount <PERSON>"}, "31": {"type": "object", "fields": {"signature": {"value": "Signature on File"}, "date": {"type": "function", "function": "getCurrentDate", "params": {"payer_id": {"path": "payer_id"}}}}, "comment": "Physician Signature"}, "32": {"type": "object", "fields": {"line_1": {"path": "subform_sf.0.organization_name"}, "line_2": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "subform_sf.0.address1"}, "address2": {"path": "subform_sf.0.address2"}}}}, "line_3": {"type": "template", "format": "{% if city %}{{ city | capitalize }}{% endif %}{% if city and state %}, {% endif %}{% if state %}{{ state }}{% endif %}{% if zip %} {{ zip }}{% endif %}", "context": {"fields": {"city": {"path": "subform_sf.0.city"}, "state": {"path": "subform_sf.0.state"}, "zip": {"path": "subform_sf.0.postal_code"}}}}}, "comment": "Service Facility Location"}, "33": {"type": "object", "fields": {"phone": {"type": "template", "format": "{% if phone %}{{ phone | formatPhoneNumber }}{% endif %}", "context": {"fields": {"phone": {"path": "bill_phone"}}}}, "line_1": {"path": "bill_organization_name"}, "line_2": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "bill_address1"}, "address2": {"path": "bill_address2"}}}}, "line_3": {"type": "template", "format": "{% if city %}{{ city | capitalize }}{% endif %}{% if city and state %}, {% endif %}{% if state %}{{ state }}{% endif %}{% if zip %} {{ zip }}{% endif %}", "context": {"fields": {"city": {"path": "bill_city"}, "state": {"path": "bill_state_id"}, "zip": {"path": "bill_zip"}}}}, "33a": {"path": "npi"}, "33b": {"path": "bill_alt_provider_id"}}, "comment": "Billing Provider"}}