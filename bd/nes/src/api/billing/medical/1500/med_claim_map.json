{"1": {"type": "object", "fields": {"1": {"type": "map", "path": "claim_information.0.claim_filing_code", "default_value": "OTHER", "map": {"16": "MEDICARE", "MA": "MEDICARE", "MB": "MEDICARE", "MC": "MEDICAID", "CH": "TRICARE", "FI": "FECA", "12": "GROUP HEALTH PLAN", "13": "GROUP HEALTH PLAN", "15": "GROUP HEALTH PLAN", "HM": "GROUP HEALTH PLAN"}}, "1a": {"path": "subscriber.0.member_id"}}, "comment": "Insurance Type/Member ID"}, "2": {"type": "template", "format": "{% if last_name %}{{ last_name | capitalize }}{% endif %}{% if first_name and last_name %}, {% endif %}{% if first_name %}{{ first_name | capitalize }}{% endif %}{% if middle_name %} {{ middle_name | initial }}{% endif %}", "context": [{"condition": "dependent.0.first_name", "fields": {"first_name": {"path": "dependent.0.first_name"}, "last_name": {"path": "dependent.0.last_name"}, "middle_name": {"path": "dependent.0.middle_name"}}}, {"condition": "subscriber.0.first_name", "fields": {"first_name": {"path": "subscriber.0.first_name"}, "last_name": {"path": "subscriber.0.last_name"}, "middle_name": {"path": "subscriber.0.middle_name"}}}], "comment": "Patient Name"}, "3": {"type": "object", "fields": {"month": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatMonth }}{% endif %}", "context": {"fields": {"date_of_birth": {"path": [{"condition": "dependent.0.first_name", "path": "dependent.0.date_of_birth"}, {"condition": "subscriber.0.first_name", "path": "subscriber.0.date_of_birth"}]}}}}, "day": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatDay }}{% endif %}", "context": {"fields": {"date_of_birth": {"path": [{"condition": "dependent.0.first_name", "path": "dependent.0.date_of_birth"}, {"condition": "subscriber.0.first_name", "path": "subscriber.0.date_of_birth"}]}}}}, "year": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatYear }}{% endif %}", "context": {"fields": {"date_of_birth": {"path": [{"condition": "dependent.0.first_name", "path": "dependent.0.date_of_birth"}, {"condition": "subscriber.0.first_name", "path": "subscriber.0.date_of_birth"}]}}}}, "gender": {"path": [{"condition": "dependent.0.first_name", "path": "dependent.0.gender"}, {"condition": "subscriber.0.first_name", "path": "subscriber.0.gender"}]}}, "comment": "Patient DOB and Sex"}, "4": {"type": "template", "format": "{% if last_name %}{{ last_name | capitalize }}{% endif %}{% if first_name and last_name %}, {% endif %}{% if first_name %}{{ first_name | capitalize }}{% endif %}{% if middle_name %} {{ middle_name | initial }}{% endif %}", "context": {"fields": {"first_name": {"path": "subscriber.0.first_name"}, "last_name": {"path": "subscriber.0.last_name"}, "middle_name": {"path": "subscriber.0.middle_name"}}}, "comment": "Subscriber Name"}, "5": {"type": "object", "fields": {"address": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": [{"condition": "dependent.0.first_name", "path": "dependent.0.address.0.address1"}, {"condition": "subscriber.0.first_name", "path": "subscriber.0.address.0.address1"}]}, "address2": {"path": [{"condition": "dependent.0.first_name", "path": "dependent.0.address.0.address2"}, {"condition": "subscriber.0.first_name", "path": "subscriber.0.address.0.address2"}]}}}}, "city": {"path": [{"condition": "dependent.0.address.0.address1", "path": "dependent.0.address.0.city"}, {"condition": "subscriber.0.address.0.address1", "path": "subscriber.0.address.0.city"}]}, "state": {"path": [{"condition": "dependent.0.address.0.address1", "path": "dependent.0.address.0.state"}, {"condition": "subscriber.0.address.0.address1", "path": "subscriber.0.address.0.state"}]}, "zip": {"path": [{"condition": "dependent.0.address.0.address1", "path": "dependent.0.address.0.postal_code"}, {"condition": "subscriber.0.address.0.address1", "path": "subscriber.0.address.0.postal_code"}]}, "phone": {"type": "template", "format": "{% if phone %}{{ phone | formatPhoneNumber }}{% endif %}", "context": {"fields": {"phone": {"path": [{"condition": "dependent.0.address.0.address1", "path": "dependent.0.contact_information.0.phone_number"}, "subscriber.0.contact_information.0.phone_number"]}}}}}, "comment": "Patient Address and phone number"}, "6": {"type": "map", "path": "dependent.0.relationship_to_subscriber_code", "default_value": "Self", "map": {"01": "Spouse", "18": "Self", "19": "Child", "20": "Other", "39": "Other", "40": "Other", "53": "Other", "G8": "Other"}, "comment": "Relationship to subscriber"}, "7": {"type": "object", "condition": "subscriber.0.address.0.address1", "fields": {"address": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "subscriber.0.address.0.address1"}, "address2": {"path": "subscriber.0.address.0.address2"}}}}, "city": {"path": "subscriber.0.address.0.city"}, "state": {"path": "subscriber.0.address.0.state"}, "zip": {"path": "subscriber.0.address.0.postal_code"}, "phone": {"type": "template", "format": "{% if phone %}{{ phone | formatPhoneNumber }}{% endif %}", "context": {"fields": {"phone": {"path": "subscriber.0.contact_information.0.phone_number"}}}}}, "comment": "Insured Address"}, "9": {"type": "object", "fields": {"9": {"type": "template", "format": "{% if last_name %}{{ last_name | capitalize }}{% endif %}{% if first_name and last_name %}, {% endif %}{% if first_name %}{{ first_name | capitalize }}{% endif %}{% if middle_name %} {{ middle_name | initial }}{% endif %}", "context": {"condition": "claim_information.0.other_subscriber_information.0.other_subscriber_name.0.other_insured_first_name", "fields": {"first_name": {"path": "claim_information.0.other_subscriber_information.0.other_subscriber_name.0.other_insured_first_name"}, "last_name": {"path": "claim_information.0.other_subscriber_information.0.other_subscriber_name.0.other_insured_last_name"}, "middle_name": {"path": "claim_information.0.other_subscriber_information.0.other_subscriber_name.0.other_insured_middle_name"}}}}, "9a": {"path": [{"condition": "claim_information.0.other_subscriber_information.1.other_subscriber_name.0.other_insured_first_name", "path": "claim_information.0.other_subscriber_information.1.insurance_group_or_policy_number"}, {"condition": "claim_information.0.other_subscriber_information.0.other_subscriber_name.0.other_insured_first_name", "path": "claim_information.0.other_subscriber_information.0.insurance_group_or_policy_number"}]}, "9d": {"path": [{"condition": "claim_information.0.other_subscriber_information.1.other_subscriber_name.0.other_insured_first_name", "path": "claim_information.0.other_subscriber_information.1.other_payer_name.0.other_payer_organization_name"}, {"condition": "claim_information.0.other_subscriber_information.0.other_subscriber_name.0.other_insured_first_name", "path": "claim_information.0.other_subscriber_information.0.other_payer_name.0.other_payer_organization_name"}]}}, "comment": "Other Insured Info"}, "10": {"type": "object", "fields": {"10a": {"type": "template", "format": "{% if related_cause_code %}{{ related_cause_code | relatedCauseEmploymentCheck }}{% endif %}", "context": {"fields": {"related_cause_code": {"path": "claim_information.0.related_cause_code"}}}}, "10b": {"type": "object", "fields": {"check": {"type": "template", "format": "{% if related_cause_code %}{{ related_cause_code | relatedCauseAutoCheck }}{% endif %}", "context": {"fields": {"related_cause_code": {"path": "claim_information.0.related_cause_code"}}}}, "place": {"path": "claim_information.0.auto_accident_state_code"}}}, "10c": {"type": "template", "format": "{% if related_cause_code %}{{ related_cause_code | relatedCauseAccidentCheck }}{% endif %}", "context": {"fields": {"related_cause_code": {"path": "claim_information.0.related_cause_code"}}}}}, "comment": "Conditions"}, "11": {"type": "object", "fields": {"11": {"path": "subscriber.0.policy_number"}, "11a": {"type": "object", "context": {"fields": {"date_of_birth": {"path": "subscriber.0.date_of_birth"}}}, "fields": {"month": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if date_of_birth %}{{ date_of_birth | formatYear }}{% endif %}"}, "gender": {"path": "subscriber.0.gender"}}}, "11b": {"type": "object", "condition": "claim_information.0.other_subscriber_information.0.other_payer_name.0.other_payer_claim_control_number", "fields": {"qualifier": {"value": "F8"}, "ccn": {"path": "claim_information.0.other_subscriber_information.0.other_payer_name.0.other_payer_claim_control_number"}}}, "11c": {"path": "receiver.0.organization_name"}, "11d": {"type": "template", "format": "{% if payment_responsibility_level_code %}{{ payment_responsibility_level_code | otherPayerCheck }}{% endif %}", "context": {"fields": {"payment_responsibility_level_code": {"path": "claim_information.0.other_subscriber_information.0.payment_responsibility_level_code"}}}}}, "comment": "Insured Info"}, "12": {"type": "object", "fields": {"signature": {"value": "Signature on File"}, "date": {"type": "function", "function": "getCurrentDate", "params": {"payer_id": {"path": "payer_id"}}}}, "comment": "<PERSON><PERSON>'s signature"}, "13": {"value": "Signature on File"}, "14": {"type": "object", "condition": "claim_information.0.claim_info_other.0.claim_date_information.0.symptom_date", "context": {"fields": {"symptom_date": {"path": "claim_information.0.claim_info_other.0.claim_date_information.0.symptom_date"}}}, "fields": {"month": {"type": "template", "format": "{% if symptom_date %}{{ symptom_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if symptom_date %}{{ symptom_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if symptom_date %}{{ symptom_date | formatYear }}{% endif %}"}, "qualifier": {"value": "431"}}, "comment": "Date of current illness, injury, or pregnancy"}, "15": {"type": "object", "context": {"fields": {"other_date": {"path": ["claim_information.0.claim_info_other.0.claim_date_information.0.accident_date", "claim_information.0.claim_info_other.0.claim_date_information.0.last_seen_date", "claim_information.0.claim_info_other.0.claim_date_information.0.acute_manifestation_date", "claim_information.0.claim_info_other.0.claim_date_information.0.initial_treatment_date", "claim_information.0.service_lines.0.service_line_date_information.0.prescription_date"]}}}, "fields": {"month": {"type": "template", "format": "{% if other_date %}{{ other_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if other_date %}{{ other_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if other_date %}{{ other_date | formatYear }}{% endif %}"}, "qualifier": {"path": [{"condition": "claim_information.0.claim_info_other.0.claim_date_information.0.accident_date", "value": "439"}, {"condition": "claim_information.0.claim_info_other.0.claim_date_information.0.last_seen_date", "value": "304"}, {"condition": "claim_information.0.claim_info_other.0.claim_date_information.0.acute_manifestation_date", "value": "453"}, {"condition": "claim_information.0.claim_info_other.0.claim_date_information.0.initial_treatment_date", "value": "454"}, {"condition": "claim_information.0.service_lines.0.service_line_date_information.0.prescription_date", "value": "471"}]}}, "comment": "Other Date"}, "16": {"type": "object", "fields": {"from": {"type": "object", "context": {"fields": {"work_date": {"path": "claim_information.0.claim_info_other.0.claim_date_information.0.last_worked_date"}}}, "fields": {"month": {"type": "template", "format": "{% if work_date %}{{ work_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if work_date %}{{ work_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if work_date %}{{ work_date | formatYear}}{% endif %}"}}}, "to": {"type": "object", "context": {"fields": {"work_date": {"path": "claim_information.0.claim_info_other.0.claim_date_information.0.authorized_return_to_work_date"}}}, "fields": {"month": {"type": "template", "format": "{% if work_date %}{{ work_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if work_date %}{{ work_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if work_date %}{{ work_date | formatYear }}{% endif %}"}}}}, "comment": "Dates patient unable to work in current occupation"}, "17": {"type": "object", "fields": {"17": {"type": "object", "fields": {"qualifier": {"path": [{"condition": "providers.0.referring.0.last_name", "value": "DN"}, {"condition": "providers.0.ordering.0.last_name", "value": "DK"}, {"condition": "providers.0.supervising.0.last_name", "value": "DQ"}]}, "name": {"type": "template", "format": "{% if first_name %}{{ first_name | capitalize }} {% endif %}{% if last_name %}{{ last_name | capitalize }}{% endif %}", "context": {"fields": {"first_name": [{"condition": "providers.0.referring.0.last_name", "path": "providers.0.referring.0.first_name"}, {"condition": "providers.0.ordering.0.last_name", "path": "providers.0.ordering.0.first_name"}, {"condition": "providers.0.supervising.0.last_name", "path": "providers.0.supervising.0.first_name"}], "last_name": {"path": ["providers.0.referring.0.last_name", "providers.0.ordering.0.last_name", "providers.0.supervising.0.last_name"]}}}}}}, "17a": {"type": "object", "fields": {"qualifier": {"path": [{"condition": "providers.0.referring.0.last_name", "value": {"condition": "providers.0.referring.0.state_license_number", "value": "0B"}}, {"condition": "providers.0.ordering.0.last_name", "value": {"condition": "providers.0.ordering.0.state_license_number", "value": "0B"}}, {"condition": "providers.0.supervising.0.last_name", "value": {"condition": "providers.0.supervising.0.state_license_number", "value": "0B"}}]}, "license_no": {"path": [{"condition": "providers.0.referring.0.last_name", "path": "providers.0.referring.0.state_license_number"}, {"condition": "providers.0.ordering.0.last_name", "path": "providers.0.ordering.0.state_license_number"}, {"condition": "providers.0.supervising.0.last_name", "path": "providers.0.supervising.0.state_license_number"}]}}}, "17b": {"path": [{"condition": "providers.0.referring.0.last_name", "path": "providers.0.referring.0.npi"}, {"condition": "providers.0.ordering.0.last_name", "path": "providers.0.ordering.0.npi"}, {"condition": "providers.0.supervising.0.last_name", "path": "providers.0.supervising.0.npi"}]}}, "comment": "Referring provider"}, "18": {"type": "object", "fields": {"from": {"type": "object", "context": {"fields": {"admission_date": {"path": "claim_information.0.claim_info_other.0.claim_date_information.0.admission_date"}}}, "fields": {"month": {"type": "template", "format": "{% if admission_date %}{{ admission_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if admission_date %}{{ admission_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if admission_date %}{{ admission_date | formatYear }}{% endif %}"}}}, "to": {"type": "object", "context": {"fields": {"discharge_date": {"path": "claim_information.0.claim_info_other.0.claim_date_information.0.discharge_date"}}}, "fields": {"month": {"type": "template", "format": "{% if discharge_date %}{{ discharge_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if discharge_date %}{{ discharge_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if discharge_date %}{{ discharge_date | formatYear }}{% endif %}"}}}}, "comment": "Hospitalization Dates"}, "19": {"path": "claim_information.0.claim_note.0.additional_information", "comment": "Additional Claim Information"}, "21": {"type": "object", "fields": {"A": {"path": "claim_information.0.health_care_code_information.0.diagnosis_code"}, "B": {"path": "claim_information.0.health_care_code_information.1.diagnosis_code"}, "C": {"path": "claim_information.0.health_care_code_information.2.diagnosis_code"}, "D": {"path": "claim_information.0.health_care_code_information.3.diagnosis_code"}, "E": {"path": "claim_information.0.health_care_code_information.4.diagnosis_code"}, "F": {"path": "claim_information.0.health_care_code_information.5.diagnosis_code"}, "G": {"path": "claim_information.0.health_care_code_information.6.diagnosis_code"}, "H": {"path": "claim_information.0.health_care_code_information.7.diagnosis_code"}, "I": {"path": "claim_information.0.health_care_code_information.8.diagnosis_code"}, "J": {"path": "claim_information.0.health_care_code_information.9.diagnosis_code"}, "K": {"path": "claim_information.0.health_care_code_information.10.diagnosis_code"}, "L": {"path": "claim_information.0.health_care_code_information.11.diagnosis_code"}, "indicator": {"value": "ABF"}}, "comment": "Diagnosis (A-L) and indicator"}, "22": {"type": "object", "fields": {"resubmission_code": {"type": "map", "path": "claim_information.0.claim_frequency_code", "default_value": "", "map": {"7": "7", "8": "8"}}, "original_ref_no": {"path": "claim_information.0.claim_supplemental_information.0.claim_control_number"}}, "comment": "Resubmission Code / Original Referral Number"}, "23": {"path": "claim_information.0.claim_supplemental_information.0.prior_authorization_number", "comment": "Prior Authorization Number"}, "24": {"type": "object", "repeat": {"path_raw": "claim_information[0].service_lines"}, "fields": {"from": {"type": "object", "context": {"fields": {"service_date": {"path": "claim_information.0.service_lines.{{idx}}.service_date"}}}, "fields": {"month": {"type": "template", "format": "{% if service_date %}{{ service_date | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if service_date %}{{ service_date | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if service_date %}{{ service_date | formatYear }}{% endif %}"}}}, "to": {"type": "object", "context": {"fields": {"service_date_end": {"path": "claim_information.0.service_lines.{{idx}}.service_date_end"}}}, "fields": {"month": {"type": "template", "format": "{% if service_date_end %}{{ service_date_end | formatMonth }}{% endif %}"}, "day": {"type": "template", "format": "{% if service_date_end %}{{ service_date_end | formatDay }}{% endif %}"}, "year": {"type": "template", "format": "{% if service_date_end %}{{ service_date_end | formatYear }}{% endif %}"}}}, "place_of_service": {"condition": "claim_information.0.service_lines.{{idx}}.service_date", "path": ["claim_information.0.service_lines.{{idx}}.professional_service.0.place_of_service_code", "claim_information.0.place_of_service_code"]}, "emg": {"type": "template", "format": "{% if emergency_indicator %}{{ emergency_indicator | convertToX }}{% endif %}", "context": {"fields": {"emergency_indicator": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.emergency_indicator"}}}}, "cpt_hcpcs": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.procedure_code"}, "modifier_1": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.modifier_1"}, "modifier_2": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.modifier_2"}, "modifier_3": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.modifier_3"}, "modifier_4": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.modifier_4"}, "diagnosis_pointer": {"type": "function", "function": "convertDiagnosisPointers", "params": {"reference": {"path_raw": "claim_information[0].health_care_code_information"}, "dx_1": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.dx_id_1"}, "dx_2": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.dx_id_2"}, "dx_3": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.dx_id_3"}, "dx_4": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.dx_id_4"}}}, "charges": {"type": "template", "format": "{{ charge | formatCurrency }}", "context": {"fields": {"charge": {"path": "claim_information.0.service_lines.{{idx}}.line_item_charge_amount"}}}}, "days_or_units": {"type": "template", "format": "{{ units | formatUnits(3) }}", "context": {"fields": {"units": {"path": "claim_information.0.service_lines.{{idx}}.service_unit_count"}}}}, "EPSDT": {"type": "template", "format": "{% if epsdt_indicator %}{{ epsdt_indicator | convertToX }}{% endif %}", "context": {"fields": {"epsdt_indicator": {"path": "claim_information.0.service_lines.{{idx}}.professional_service.0.epsdt_indicator"}}}}, "rendering_provider_npi": {"condition": "claim_information.0.service_lines.{{idx}}.service_date", "path": "providers.0.rendering.0.npi"}}, "comment": "Service Line"}, "25": {"type": "object", "fields": {"federal_tax_id": {"path": "providers.0.billing.0.employer_identification_number"}, "ein": {"value": "X"}}, "comment": "Federal Tax ID, SSN, EIN"}, "26": {"type": "function", "function": "loadPatientMRN", "params": {"patient_id": {"path": "patient_id"}}}, "27": {"value": "YES", "comment": "Accept Assignment"}, "28": {"type": "function", "function": "calculateSplitCharges", "params": {"serviceLines": {"path_raw": "claim_information[0].service_lines"}}, "comment": "Total Charge"}, "29": {"type": "function", "function": "calculateSplitAdjustments", "params": {"serviceLines": {"path_raw": "claim_information[0].service_lines"}}, "comment": "Amount <PERSON>"}, "31": {"type": "object", "fields": {"signature": {"value": "Signature on File"}, "date": {"type": "function", "function": "getCurrentDate", "params": {"payer_id": {"path": "payer_id"}}}}, "comment": "Physician Signature"}, "32": {"type": "object", "fields": {"line_1": {"path": "claim_information.0.service_facility_location.0.organization_name"}, "line_2": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "claim_information.0.service_facility_location.0.address.0.address1"}, "address2": {"path": "claim_information.0.service_facility_location.0.address.0.address2"}}}}, "line_3": {"type": "template", "format": "{% if city %}{{ city | capitalize }}{% endif %}{% if city and state %}, {% endif %}{% if state %}{{ state }}{% endif %}{% if zip %} {{ zip }}{% endif %}", "context": {"fields": {"city": {"path": "claim_information.0.service_facility_location.0.address.0.city"}, "state": {"path": "claim_information.0.service_facility_location.0.address.0.state"}, "zip": {"path": "claim_information.0.service_facility_location.0.address.0.postal_code"}}}}}, "comment": "Service Facility Location"}, "33": {"type": "object", "fields": {"phone": {"type": "template", "format": "{% if phone %}{{ phone | formatPhoneNumber }}{% endif %}", "context": {"fields": {"phone": {"path": "providers.0.billing.0.contact_information.0.phone_number"}}}}, "line_1": {"path": "providers.0.billing.0.organization_name"}, "line_2": {"type": "template", "format": "{% if address1 %}{{ address1 }}{% endif %}{% if address2 %} {{ address2 }}{% endif %}", "context": {"fields": {"address1": {"path": "providers.0.billing.0.address.0.address1"}, "address2": {"path": "providers.0.billing.0.address.0.address2"}}}}, "line_3": {"type": "template", "format": "{% if city %}{{ city | capitalize }}{% endif %}{% if city and state %}, {% endif %}{% if state %}{{ state }}{% endif %}{% if zip %} {{ zip }}{% endif %}", "context": {"fields": {"city": {"path": "providers.0.billing.0.address.0.city"}, "state": {"path": "providers.0.billing.0.address.0.state"}, "zip": {"path": "providers.0.billing.0.address.0.postal_code"}}}}, "33a": {"path": "providers.0.billing.0.npi"}}, "comment": "Billing Provider"}}