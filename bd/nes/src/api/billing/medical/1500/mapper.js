/*jshint : 6 */
"use strict";

const Joi = require("joi");
const currency = require("currency.js");
const moment = require("moment");
const _ = require("lodash");

const electronic_map = require("./med_claim_map.json");
const paper_map = require("./paper_map.json");
const MapperClass = require("@utils/object-mapper/mapper");
const { MedClaimType } = require("./settings");
const SERVICE_LINES_PER_PAGE = 6;

/**
 * @class CMSClaimMapperClass
 * @classdesc This class is responsible for mapping a 837 medical claim to a 1500 paper claim
 */
module.exports = class CMSClaimMapperClass extends MapperClass {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.fx = nes.modules.fx;
        const BillingFetcherClass = require("@billing/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Converts a medical claim record to a 1500 mapping object.
     * @async
     * @param {Object} medClaimRec - The medical claim record to be converted.
     * @param {string} medClaimType - The type of medical claim to be converted.
     * @returns {Promise<Object>} The converted 1500 mapping object.
     * @throws {Error} If an error occurs during the conversion process.
     */
    async convertClaim(medClaimRec, medClaimType) {
        super.debug("Converting medical claim to 1500 mapping object");

        try {
            // Validate input
            await this.fx.validateSchema(
                "medClaimRec",
                Joi.object().required(),
                medClaimRec,
                "Missing medical claim record to convert to 1500 mapping object."
            );

            // Prepare for conversion
            await this.__addHelpers();
            const outboundObj = await this.__fetchOutboundObj();

            // Perform conversion
            const map =
                medClaimType === MedClaimType.ELECTRONIC
                    ? electronic_map
                    : paper_map;
            const result = await super.convert(medClaimRec, outboundObj, map);

            super.debug(
                "Successfully converted medical claim to 1500 mapping object"
            );
            return result;
        } catch (error) {
            super.error(
                "Exception encountered while mapping medical claim to 1500",
                {
                    error: error.message,
                    stack: error.stack,
                }
            );
            throw this.fx.wrapError(
                "Failed to convert medical claim to 1500 mapping object",
                error
            );
        }
    }

    /**
     * Converts diagnosis pointers to their corresponding alphabetic representation.
     * @async
     * @param {Object} params - The parameters for converting diagnosis pointers.
     * @param {Array} params.reference - The reference array of diagnosis codes.
     * @param {number} [params.dx_1] - The first diagnosis code.
     * @param {number} [params.dx_2] - The second diagnosis code.
     * @param {number} [params.dx_3] - The third diagnosis code.
     * @param {number} [params.dx_4] - The fourth diagnosis code.
     * @returns {Promise<string>} A comma-separated string of alphabetic diagnosis pointers.
     * @throws {Error} If an error occurs during the conversion process.
     */
    async convertDiagnosisPointers({ reference, dx_1, dx_2, dx_3, dx_4 }) {
        super.debug("Converting diagnosis pointers");

        try {
            if (!reference) {
                return "";
            }
            await Promise.all([
                this.fx.validateSchema(
                    "reference",
                    Joi.array().required(),
                    reference,
                    "Missing reference data to convert diagnosis pointers."
                ),
                this.fx.validateSchema(
                    "dx_1",
                    Joi.number().allow(null).optional(),
                    dx_1,
                    "Invalid first diagnosis code to convert diagnosis pointers."
                ),
                this.fx.validateSchema(
                    "dx_2",
                    Joi.number().allow(null).optional(),
                    dx_2,
                    "Invalid second diagnosis code to convert diagnosis pointers."
                ),
                this.fx.validateSchema(
                    "dx_3",
                    Joi.number().allow(null).optional(),
                    dx_3,
                    "Invalid third diagnosis code to convert diagnosis pointers."
                ),
                this.fx.validateSchema(
                    "dx_4",
                    Joi.number().allow(null).optional(),
                    dx_4,
                    "Invalid fourth diagnosis code to convert diagnosis pointers."
                ),
            ]);

            const alphabet = "ABCDEFGHIJKL";

            const pointers = [dx_1, dx_2, dx_3, dx_4]
                .map((dx) => {
                    if (dx) {
                        const index = reference.findIndex(
                            (ref) => ref.dx_id === dx
                        );
                        return index !== -1 && index < alphabet.length
                            ? alphabet[index]
                            : null;
                    }
                    return null;
                })
                .filter(Boolean);

            return pointers.join(",");
        } catch (error) {
            super.error(
                `Exception encountered while converting diagnosis pointers. Error: ${error.message}`,
                { stack: error.stack }
            );
            throw this.fx.wrapError(
                "Error encountered while converting diagnosis pointers",
                error
            );
        }
    }

    /**
     * Gets the current date formatted based on payer preferences.
     * @async
     * @param {Object} params - The parameters for getting the current date.
     * @param {number} params.payer_id - The unique identifier of the payer.
     * @returns {Promise<string>} The formatted current date.
     * @throws {Error} If an error occurs during the date formatting process.
     */
    async getCurrentDate({ payer_id }) {
        super.debug("Getting current date");
        if (!payer_id) {
            return "";
        }
        await this.fx.validateSchema(
            "payer_id",
            Joi.number().required(),
            payer_id,
            "Invalid payer ID to get current date."
        );
        const payerRec = await this.fetcher.fetchCachedRecord(
            "payer",
            payer_id
        );
        const twoYearDigit = payerRec.cms_7 !== "Yes";
        return twoYearDigit ? moment().format("YY") : moment().format("YYYY");
    }

    /**
     * Formats the year based on payer preferences.
     * @async
     * @param {Object} params - The parameters for formatting the year.
     * @param {number} params.payer_id - The unique identifier of the payer.
     * @param {Date|string|null} params.date - The date to format.
     * @returns {Promise<string>} The formatted year.
     * @throws {Error} If an error occurs during the year formatting process.
     */
    async formatYear({ payer_id, date }) {
        super.debug("Formatting year");
        if (!payer_id) {
            return "";
        }
        await Promise.all([
            this.fx.validateSchema(
                "payer_id",
                Joi.number().required(),
                payer_id,
                "Invalid payer ID to format year."
            ),
            this.fx.validateSchema(
                "date",
                Joi.date().allow(null).optional(),
                date,
                "Invalid date to format year."
            ),
        ]);

        if (!date) {
            return "";
        }
        const payerRec = await this.fetcher.fetchCachedRecord(
            "payer",
            payer_id
        );
        const twoYearDigit = payerRec.cms_7 !== "Yes";
        return twoYearDigit
            ? moment(date).format("YY")
            : moment(date).format("YYYY");
    }

    /**
     * Loads the Medical Record Number (MRN) for a given patient.
     * @async
     * @param {Object} params - The parameters for loading the patient MRN.
     * @param {number|string} params.patient_id - The unique identifier of the patient.
     * @returns {Promise<string|null>} The patient's MRN if found, or null if not available.
     * @throws {Error} If an error occurs during the MRN loading process.
     */
    async loadPatientMRN({ patient_id }) {
        super.debug("Loading patient MRN");

        try {
            if (!patient_id) {
                return "";
            }
            await this.fx.validateSchema(
                "patient_id",
                Joi.number().required(),
                patient_id,
                "Invalid patient ID to load patient MRN."
            );

            const patientRec = await this.fetcher.fetchCachedRecord(
                "patient",
                patient_id
            );
            return patientRec?.mrn;
        } catch (error) {
            super.error(
                `Exception encountered while loading patient MRN. Error: ${error.message}`,
                { stack: error.stack }
            );
            throw this.fx.wrapError(
                "Error encountered while loading patient MRN",
                error
            );
        }
    }

    /**
     * Calculates split charges for service lines.
     * @async
     * @param {Object} params - The parameters for calculating split charges.
     * @param {Array} params.serviceLines - An array of service line objects.
     * @returns {Promise<Object>} An object containing total charges for each page.
     * @throws {Error} If an error occurs during the calculation process.
     */
    async calculateSplitCharges({ serviceLines }) {
        super.debug("Calculating split charges");

        try {
            if (!serviceLines) {
                return "";
            }
            await this.fx.validateSchema(
                "serviceLines",
                Joi.array().required(),
                serviceLines,
                "Invalid service lines to calculate split charges."
            );
            const totalCharges = {};

            for (
                let pageNum = 1;
                pageNum <=
                Math.ceil(serviceLines.length / SERVICE_LINES_PER_PAGE);
                pageNum++
            ) {
                const startIdx = (pageNum - 1) * SERVICE_LINES_PER_PAGE;
                const endIdx = startIdx + SERVICE_LINES_PER_PAGE;
                const group = serviceLines.slice(startIdx, endIdx);

                const pageTotal = group.reduce((sum, line) => {
                    const professionalService = line.professional_service?.[0];
                    const chargeAmount =
                        professionalService?.line_item_charge_amount;
                    return sum + (parseFloat(chargeAmount) || 0);
                }, 0);

                totalCharges[`page_${pageNum}`] = currency(pageTotal, {
                    separator: ",",
                    precision: 2,
                    symbol: "",
                }).format();
            }

            return totalCharges;
        } catch (error) {
            super.error(
                `Exception encountered while calculating split charges. Error: ${error.message}`,
                { stack: error.stack }
            );
            throw this.fx.wrapError(
                "Error encountered while calculating split charges",
                error
            );
        }
    }

    /**
     * Calculates split charges for a 1500 form
     * @async
     * @param {Object} params - The parameters for calculating split charges
     * @param {number} params.payer_id - The ID of the payer
     * @param {Array} params.serviceLines - The array of service lines
     * @returns {Promise<Object>} An object containing the calculated split charges
     * @throws {Error} If there's an error during the calculation process
     */
    async calculate1500SplitCharges({ payer_id, serviceLines }) {
        super.debug("Calculating split charges");

        try {
            if (!payer_id || !serviceLines) {
                return "";
            }
            await Promise.all([
                this.fx.validateSchema(
                    "payer_id",
                    Joi.number().required(),
                    payer_id,
                    "Invalid payer ID to calculate 1500 split charges."
                ),
                this.fx.validateSchema(
                    "serviceLines",
                    Joi.array().required(),
                    serviceLines,
                    "Invalid service lines to calculate 1500 split charges."
                ),
            ]);
            const totalCharges = {};

            const payerRec = await this.fetcher.fetchCachedRecord(
                "payer",
                payer_id
            );
            const pages = Math.ceil(
                serviceLines.length / SERVICE_LINES_PER_PAGE
            );
            if (payerRec.cms_8 !== "Yes") {
                const totalCharge = _.sumBy(
                    serviceLines,
                    (line) => parseFloat(line.line_item_charge_amount) || 0
                );

                totalCharges.total = currency(totalCharge, {
                    separator: ",",
                    precision: 2,
                    symbol: "",
                }).format();
                return totalCharges;
            }

            for (let pageNum = 1; pageNum <= pages; pageNum++) {
                const startIdx = (pageNum - 1) * SERVICE_LINES_PER_PAGE;
                const endIdx = startIdx + SERVICE_LINES_PER_PAGE;
                const group = serviceLines.slice(startIdx, endIdx);

                const pageTotal = group.reduce((sum, line) => {
                    const chargeAmount = line?.line_item_charge_amount;
                    return sum + (parseFloat(chargeAmount) || 0);
                }, 0);

                totalCharges[`page_${pageNum}`] = currency(pageTotal, {
                    separator: ",",
                    precision: 2,
                    symbol: "",
                }).format();
            }

            return totalCharges;
        } catch (error) {
            super.error(
                `Exception encountered while calculating 1500 split charges. Error: ${error.message}`,
                { stack: error.stack }
            );
            throw this.fx.wrapError(
                "Error encountered while calculating 1500 split charges",
                error
            );
        }
    }

    /**
     * Calculates the split paid amount for a 1500 form.
     * @async
     * @param {Object} params - The parameters for the calculation.
     * @param {string|null} params.parent_claim_no - The parent claim number.
     * @param {number} params.payer_id - The payer ID.
     * @param {Array} params.serviceLines - The array of service lines.
     * @returns {Promise<Object>} An object containing the calculated split paid amounts.
     */
    async calculate1500SplitPaid({ parent_claim_no, payer_id, serviceLines }) {
        super.debug("Calculating split paid");

        try {
            if (!payer_id || !serviceLines) {
                return "";
            }
            await Promise.all([
                this.fx.validateSchema(
                    "parent_claim_no",
                    Joi.string().allow(null).optional(),
                    parent_claim_no,
                    "Invalid parent claim number to calculate 1500 split paid."
                ),
                this.fx.validateSchema(
                    "payer_id",
                    Joi.number().required(),
                    payer_id,
                    "Invalid payer ID to calculate 1500 split paid."
                ),
                this.fx.validateSchema(
                    "serviceLines",
                    Joi.array().required(),
                    serviceLines,
                    "Invalid service lines to calculate 1500 split paid."
                ),
            ]);
            const totalCharges = {};
            const payerRec = await this.fetcher.fetchCachedRecord(
                "payer",
                payer_id
            );

            if (parent_claim_no && payerRec.cms_9 !== "Yes") {
                return "";
            }

            for (
                let pageNum = 1;
                pageNum <=
                Math.ceil(serviceLines.length / SERVICE_LINES_PER_PAGE);
                pageNum++
            ) {
                const startIdx = (pageNum - 1) * SERVICE_LINES_PER_PAGE;
                const endIdx = startIdx + SERVICE_LINES_PER_PAGE;
                const group = serviceLines.slice(startIdx, endIdx);

                const pageTotal = group.reduce((sum, line) => {
                    const paidAmount = line?.line_item_paid_amount;
                    return sum + (parseFloat(paidAmount) || 0);
                }, 0);

                totalCharges[`page_${pageNum}`] = currency(pageTotal, {
                    separator: ",",
                    precision: 2,
                    symbol: "",
                }).format();
            }

            return totalCharges;
        } catch (error) {
            super.error(
                `Exception encountered while calculating 1500 split paid. Error: ${error.message}`,
                { stack: error.stack }
            );
            throw this.fx.wrapError(
                "Error encountered while calculating 1500 split paid",
                error
            );
        }
    }

    /**
     * Fetches the provider ID qualifier based on payer and physician information.
     * @deprecated - Now calculated in SQL build_mm_1500_claim function
     * @async
     * @param {Object} params - The parameters for fetching the provider ID qualifier.
     * @param {number} params.payer_id - The unique identifier of the payer.
     * @param {number} params.physician_id - The unique identifier of the physician.
     * @returns {Promise<string>} The provider ID qualifier if found, or an empty string.
     * @throws {Error} If an error occurs during the fetching process.
     */
    async fetchProviderIDQualifier({ payer_id, physician_id }) {
        super.debug("Fetching provider ID qualifier - DEPRECATED");
        // This function is now handled in SQL
        return "";
    }

    /**
     * Fetches the provider ID based on payer and physician information.
     * @deprecated - Now calculated in SQL build_mm_1500_claim function
     * @async
     * @param {Object} params - The parameters for fetching the provider ID.
     * @param {number} params.payer_id - The unique identifier of the payer.
     * @param {number} params.physician_id - The unique identifier of the physician.
     * @returns {Promise<string>} The provider ID if found, or an empty string.
     * @throws {Error} If an error occurs during the fetching process.
     */
    async fetchProviderID({ payer_id, physician_id }) {
        super.debug("Fetching provider ID - DEPRECATED");
        // This function is now handled in SQL
        return "";
    }

    /**
     * Calculates split adjustments for service lines.
     * @async
     * @param {Object} params - The parameters for calculating split adjustments.
     * @param {Array} params.serviceLines - An array of service line objects.
     * @returns {Promise<Object>} An object containing total adjustments for each page.
     * @throws {Error} If an error occurs during the calculation process.
     */
    async calculateSplitAdjustments({ serviceLines }) {
        super.debug("Calculating split adjustments");

        try {
            if (!serviceLines) {
                return "";
            }
            await this.fx.validateSchema(
                "serviceLines",
                Joi.array().required(),
                serviceLines,
                "Invalid service lines to calculate split adjustments."
            );

            const totalAdjustments = {};

            for (
                let idx = 0;
                idx < serviceLines.length;
                idx += SERVICE_LINES_PER_PAGE
            ) {
                const pageNum = Math.floor(idx / SERVICE_LINES_PER_PAGE) + 1;
                const pageTotal = serviceLines
                    .slice(idx, idx + SERVICE_LINES_PER_PAGE)
                    .reduce((sum, line) => {
                        if (Array.isArray(line.line_adjudication_information)) {
                            return (
                                sum +
                                line.line_adjudication_information.reduce(
                                    (lineSum, adjudication) => {
                                        return (
                                            lineSum +
                                            (Number(
                                                adjudication.service_line_paid_amount
                                            ) || 0)
                                        );
                                    },
                                    0
                                )
                            );
                        }
                        return sum;
                    }, 0);

                totalAdjustments[`page_${pageNum}`] = currency(pageTotal, {
                    separator: ",",
                    precision: 2,
                    symbol: "",
                }).format();
            }

            return totalAdjustments;
        } catch (error) {
            super.error(
                `Exception encountered while calculating split adjustments. Error: ${error.message}`,
                { stack: error.stack }
            );
            throw this.fx.wrapError(
                "Error encountered while calculating split adjustments",
                error
            );
        }
    }

    /**
     * Fetches and returns the initial outbound object structure for the 1500 form.
     * @async
     * @private
     * @returns {Promise<Object>} A promise that resolves to an object representing the initial structure of the 1500 form.
     */
    async __fetchOutboundObj() {
        return {
            1: {},
            2: {},
            3: {},
            4: "",
            5: {},
            6: "",
            7: {},
            9: {},
            10: {},
            11: {},
            12: {},
            13: "",
            14: {},
            15: {},
            16: {},
            17: {},
            18: {},
            19: {},
            21: {},
            22: {},
            23: "",
            24: {},
            25: {},
            27: "",
            28: "",
            29: "",
            31: {},
            32: {},
            33: {},
        };
    }

    /**
     * Adds custom template helpers to the Nunjucks environment.
     * These helpers are used for formatting and transforming data in the claim mapping process.
     * @async
     * @private
     * @throws {Error} If an error occurs while adding the template parsing helpers.
     */
    async __addHelpers() {
        try {
            const filters = {
                initial: (str) => (str ? `${str.charAt(0).toUpperCase()}` : ""),
                formatMonth: (date) => moment(date).format("MM"),
                formatDay: (date) => moment(date).format("DD"),
                formatYear: (date) => moment(date).format("YY"),
                formatPhoneNumber: (phoneNumber) =>
                    phoneNumber
                        .replace(/\D/g, "")
                        .replace(/(\d{3})(\d{3})(\d{4})/, "$1 $2-$3"),
                relatedCauseAutoCheck: (value) =>
                    value === "AA" ? "YES" : "NO",
                relatedCauseEmploymentCheck: (value) =>
                    value === "EM" ? "YES" : "NO",
                relatedCauseAccidentCheck: (value) =>
                    value === "OA" ? "YES" : "NO",
                otherPayerCheck: (value) =>
                    value && value.length > 0 ? "YES" : "NO",
            };

            Object.entries(filters).forEach(([name, func]) => {
                this.nunjucks.addFilter(name, func);
            });
        } catch (e) {
            super.error(
                `Exception encountered while adding template parsing helpers. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(
                "An error occurred while adding template parsing helpers",
                e
            );
        }
    }

    /**
     * Loads the rendering provider NPI for field 24j based on payer cms_3 setting.
     * If cms_3 = 'Yes', includes taxonomy ID with NPI (npi^taxonomy_id).
     * Otherwise, returns just the NPI.
     * @async
     * @param {Object} params - The parameters for loading rendering provider NPI.
     * @param {number} params.payer_id - The ID of the payer.
     * @param {string} params.npi - The NPI of the rendering provider.
     * @param {string} params.taxonomy_id - The taxonomy ID of the rendering provider.
     * @returns {Promise<string>} The rendering provider NPI with or without taxonomy ID.
     * @throws {Error} If an error occurs during the loading process.
     */
    async loadRenderingProviderNPI({ payer_id, npi, taxonomy_id }) {
        super.debug("Loading rendering provider NPI for field 24j");

        try {
            if (!payer_id || !npi) {
                super.debug(
                    "Missing required parameters for rendering provider NPI"
                );
                return npi || "";
            }

            // Get payer cms_3 setting
            const payer = await this.nes.dsl.form.payer.read({
                id: payer_id,
                fields: ["cms_3"],
            });

            if (!payer) {
                super.debug(`Payer not found for ID: ${payer_id}`);
                return npi;
            }

            // Check cms_3 setting
            if (payer.cms_3 === "Yes" && taxonomy_id) {
                super.debug("cms_3 is Yes, including taxonomy ID with NPI");
                return `${npi}^${taxonomy_id}`;
            } else {
                super.debug(
                    "cms_3 is not Yes or no taxonomy ID, returning NPI only"
                );
                return npi;
            }
        } catch (error) {
            super.error(
                `Error loading rendering provider NPI: ${error.message}`
            );
            throw this.fx.wrapError(
                error,
                "Failed to load rendering provider NPI"
            );
        }
    }
};
