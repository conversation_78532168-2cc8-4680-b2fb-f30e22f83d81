/*jshint : 6 */
"use strict";

const {
    ClaimStatus277FinalCodes,
    MedClaimStatus,
    ClaimReportTypes,
    MedClaim835FinalStatuses,
} = require("./settings");
const SuccessStatus = "SUCCESS";
const { ModuleLogger } = require("@core/logger");
const { ErrorMessages } = require("./errors");

/**
 * @class
 * @classdesc Receiver class for MedicalClaims billing processes.
 * @extends MedicalElectronicClaimsReceiverClass
 */
module.exports = class MedicalElectronicClaimsReceiverClass extends (
    ModuleLogger
) {
    constructor(nes, ctx) {
        super("837p-receiver");
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
        this.medicalClaimErrorMapper = nes.shared.medicalClaimErrorMapper;
        const MedicalElectronicClaimsFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const MedicalElectronicClaimsHelperClass = require("./helper");
        this.helper = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsHelperClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Processes the clearing house response for a medical claim.
     *
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} medClaimRec - The medical claim record to be updated.
     * @param {Object} responseData - The response data from the clearing house.
     * @returns {Promise<Object>} The updated medical claim record.
     */
    async processClearingHouseResponse(transaction, medClaimRec, responseData) {
        super.log("Processing clearing house response");

        try {
            const { responseRec } =
                await this.helper.parseClearinghouseResponse(responseData);

            const errors = responseRec?.errors || [];
            const responseStatus = responseRec?.status || "EDITS";

            if (responseStatus === SuccessStatus) {
                medClaimRec.status = MedClaimStatus.SENT;
                medClaimRec.control_number = responseRec.control_number;
            } else {
                medClaimRec.status = MedClaimStatus.ERROR;
            }
            if (medClaimRec.id) {
                await transaction.update(
                    "med_claim",
                    medClaimRec,
                    medClaimRec.id
                );
            } else {
                await transaction.insert("med_claim", medClaimRec);
            }
            if (responseStatus !== SuccessStatus) {
                if (errors.length > 0) {
                    const returnVal =
                        await this.medicalClaimErrorMapper.mapErrors(
                            errors,
                            medClaimRec
                        );
                    return {
                        beforeResponse: false,
                        validationErrors: returnVal,
                        error: ErrorMessages.CLAIM_REJECTED,
                        record: medClaimRec,
                    };
                } else {
                    super.error(
                        `Unknown response status: ${responseStatus}. Claim unsuccessful but missing errors.`
                    );
                    return {
                        beforeResponse: false,
                        validationErrors: {
                            form: [
                                `${ErrorMessages.MED_CLAIM_RESP_MISSING_ERRORS} ${responseStatus}. Please contact support if issue persist.`,
                            ],
                        },
                        error: ErrorMessages.CLAIM_REJECTED,
                        record: medClaimRec,
                    };
                }
            }
            return medClaimRec;
        } catch (e) {
            const errorMessage = `Error processing status response: ${e}`;
            super.error(errorMessage);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Processes the reports response from the server.
     *
     * @async
     * @param {Object} data - The data containing the reports to process.
     * @param {string} [source="Admin Server"] - The source of the reports, defaults to "Admin Server".
     * @returns {Promise<Object>} The result of processing the reports.
     * @throws {Error} If there's an error processing the reports response.
     */
    async processReportsResponse(data, source = "Admin Server") {
        super.log("Processing status response");

        try {
            const { metaData, responseRecs } =
                await this.helper.parsePayerClaimResponse(
                    data,
                    data.type,
                    source
                );

            const stopFetching = [];
            const reportType = metaData.report_type;
            const payerName = metaData?.payer_name || "Missing Payer Name";
            console.log(
                `Processing ${reportType} report for payer: ${payerName}`
            );

            for (const responseRec of responseRecs) {
                const { error, fetchingComplete, controlNumber } =
                    await this.__processPayerReport(reportType, responseRec);
                if (error) {
                    return { error, stopFetching: false };
                } else if (fetchingComplete) {
                    stopFetching.push(controlNumber);
                }
            }

            return {
                success: true,
                stopFetching,
            };
        } catch (e) {
            const errorMessage = `Error processing status response: ${e}`;
            super.error(errorMessage);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Process an individual report from the response
     * @param {string} reportType - The type of report being processed
     * @param {Object} reportRec - The report data to process
     * @returns {Promise<Object>} Result object containing error status and fetching status
     * @private
     */
    async __processPayerReport(reportType, reportRec) {
        try {
            let fetchingComplete = false;
            if (reportType === ClaimReportTypes.STATUS_REPORT) {
                const { error, stopFetching, controlNumber } =
                    await this.__processStatusResponse(reportRec);
                fetchingComplete = stopFetching;
                return { error, fetchingComplete, controlNumber };
            } else if (reportType === ClaimReportTypes.PAYER_RESPONSE) {
                const { error, stopFetching, controlNumber } =
                    await this.__processPayerResponse(reportRec);
                fetchingComplete = stopFetching;
                return { error, fetchingComplete, controlNumber };
            } else {
                throw this.fx.getClaraError(
                    `Unsupported report type: ${reportType}`
                );
            }
        } catch (e) {
            const errorMessage = `Error processing ${reportType} report: ${e}`;
            super.error(errorMessage);
            throw this.fx.wrapError(e, errorMessage);
        }
    }
    /**
     * Processes the status response for a medical claim.
     *
     * @async
     * @param {Object} reportRec - The data object containing the status response.
     * @returns {Promise<void>}
     */
    async __processStatusResponse(reportRec) {
        super.log("Processing status response");

        try {
            const associatedClaimRec =
                await this.fetcher.fetchClaimWithClaimNumber(
                    reportRec.claim_no
                );
            if (!associatedClaimRec) {
                console.error(
                    `No associated claims found with claim no ${reportRec.claim_no}.`
                );
                const error = `No associated claims found with claim no ${reportRec.claim_no}.`;
                super.error(error);
                return {
                    error,
                    stopFetching: false,
                    controlNumber: null,
                };
            }

            if (
                ["Not Found", "Payer Not Responding, Try Again Later"].includes(
                    associatedClaimRec.status
                )
            ) {
                const error = `Claim ${reportRec.claim_no} is in status ${associatedClaimRec.status}.`;
                super.error(error);
                return {
                    error,
                    stopFetching: false,
                    controlNumber: associatedClaimRec.control_number,
                };
            }

            const stopFetching = ClaimStatus277FinalCodes.includes(
                reportRec.health_care_claim_status_category_code
            );
            return {
                error: null,
                stopFetching,
                controlNumber: associatedClaimRec.control_number,
            };
        } catch (e) {
            const errorMessage = `Error processing status response: ${e}`;
            super.error(errorMessage);
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Processes the payer response for a medical claim.
     *
     * @async
     * @param {Object} reportRec - The data containing the payer response.
     * @returns {Promise<Object>} The processed claim record or a success message.
     * @throws {Error} If there's an error processing the payer response.
     */
    async __processPayerResponse(reportRec) {
        super.log("Processing payer response");

        try {
            const associatedClaimRec =
                await this.fetcher.fetchClaimWithClaimNumber(
                    reportRec.claim_no
                );
            if (!associatedClaimRec) {
                console.error(
                    `No associated claims found with claim no ${reportRec.claim_no}.`
                );
                const error = `No associated claims found with claim no ${reportRec.claim_no}.`;
                super.error(error);
                return {
                    error,
                    stopFetching: false,
                    controlNumber: null,
                };
            }

            return {
                stopFetching: MedClaim835FinalStatuses.includes(
                    associatedClaimRec.status
                ),
                controlNumber: associatedClaimRec.control_number,
                error: null,
            };
        } catch (e) {
            const errorMessage = `Error processing payer response: ${e}`;
            super.error(errorMessage);
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
