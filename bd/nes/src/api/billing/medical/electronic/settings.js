const medicalSettings = require("@billing/medical/settings");

const TestValues = {
    controlNumber: [
        "000000001",
        "000000002",
        "000000003",
        "000000004",
        "123456789",
    ],
    memberId: [
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "123456789",
    ],
    firstName: ["johnone", "johntwo", "janeone", "janetwo"],
    lastName: ["doeone", "doetwo"],
    middleName: ["middleone", "middletwo"],
    gender: ["M", "F"],
    dateOfBirth: [
        "18800102",
        "18800101",
        "18160421",
        "19800101",
        "19800102",
        "20000101",
        "20000102",
    ],
    ssn: [
        "000000000",
        "555443333",
        "111111111",
        "000000001",
        "891234567",
        "123456789",
    ],
    groupNumber: [
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
        "**********",
    ],
    address1: ["123 address1", "000 address1"],
    address2: ["apt 123", "apt 000", "123", "000"],
    city: ["city1", "city2"],
    state: ["wa", "tn"],
    postalCode: ["981010000", "372030000"],
    employerId: [
        "00000",
        "12345",
        "00001",
        "00002",
        "000000000",
        "123456789",
        "123456",
    ],
    propertyCasualtyClaimNumber: ["00000", "12345", "00001", "00002"],
    patientControlNumber: ["00000", "12345", "00001", "00002"],
    priorAuthorizationNumber: ["00000", "12345", "00001", "00002"],
    referralNumber: ["00000", "12345", "00001", "00002"],
    repricedClaimNumber: ["00000", "12345", "00001", "00002"],
    investigationalDeviceExemptionNumber: ["00000", "12345", "00001", "00002"],
    claimNumber: ["00000", "12345", "00001", "00002"],
    name: [
        "johnone doeone",
        "johntwo doetwo",
        "janeone doeone",
        "janetwo doetwo",
        "submitter contact info",
    ],
    phoneNumber: ["**********", "123456789", "**********", "**********"],
    faxNumber: ["**********", "123456789", "**********", "**********"],
    email: ["<EMAIL>", "<EMAIL>"],
    stateLicenseNumber: ["0000000", "0000001", "123456"],
    contractVersionIdentifier: ["111111", "222222", "123456"],
    cliaNumber: ["12D4567890", "00D0000001"],
    mammographyCertificationNumber: ["00000", "12345", "00001", "00002"],
    medicalRecordNumber: ["00000", "12345", "00001", "00002"],
    demoProjectIdentifier: ["00000", "12345", "00001", "00002"],
    carePlanOversightNumber: ["00000", "12345", "00001", "00002"],
    policyNumber: ["00000", "12345", "00001", "00002"],
    npi: ["**********"],
    organizationName: [
        "happy doctors group",
        "happy doctors grouppractice",
        "extra healthy insurance",
        "regional ppo network",
    ],
};

const ClaimStatus277FinalCodes = ["A0", "F3F", "D0", "E0", "E1", "DR04", "A4"];
const MedClaimStatus = {
    PENDING: "Pending",
    VALIDATED: "Validated",
    SENT: "Sent",
    PROCESSING: "Processing",
    RECEIVED: "Received",
    ACCEPTED: "Accepted",
    FORWARDED: "Forwarded",
    REJECTED: "Rejected",
    REVISED: "Revised",
    REVERSED: "Reversed",
    WARNING: "Warning",
    DENIED: "Denied",
    ON_HOLD: "On-hold",
    ERROR: "Error",
    AWAITING_REQUESTED_INFORMATION: "Awaiting Requested Information",
    REQUEST_FOR_ADDITIONAL_INFORMATION: "Request for Additional Information",
    UNDER_REVIEW: "Under Review",
    PARTIALLY_PAID: "Partially Paid",
    PAYABLE: "Payable",
};
const MedClaim835FinalStatuses = [
    MedClaimStatus.PAYABLE,
    MedClaimStatus.FORWARDED,
    MedClaimStatus.REVERSED,
    MedClaimStatus.REJECTED,
    MedClaimStatus.DENIED,
    MedClaimStatus.ERROR,
];

const MedClaimStatusOpenStatus = [
    MedClaimStatus.PENDING,
    MedClaimStatus.REJECTED,
    MedClaimStatus.ACCEPTED,
    MedClaimStatus.ERROR,
    MedClaimStatus.VALIDATED,
    MedClaimStatus.REVERSED,
];

const MedClaimStatusBilledStatus = [
    MedClaimStatus.SENT,
    MedClaimStatus.PROCESSING,
    MedClaimStatus.RECEIVED,
    MedClaimStatus.ACCEPTED,
    MedClaimStatus.FORWARDED,
    MedClaimStatus.REVISED,
    MedClaimStatus.WARNING,
    MedClaimStatus.ON_HOLD,
    MedClaimStatus.AWAITING_REQUESTED_INFORMATION,
    MedClaimStatus.REQUEST_FOR_ADDITIONAL_INFORMATION,
    MedClaimStatus.UNDER_REVIEW,
    MedClaimStatus.PARTIALLY_PAID,
    MedClaimStatus.PAYABLE,
];

const MedClaimCOBEligibleStatus = [
    MedClaimStatus.FORWARDED,
    MedClaimStatus.REJECTED,
    MedClaimStatus.REVISED,
    MedClaimStatus.DENIED,
    MedClaimStatus.PARTIALLY_PAID,
    MedClaimStatus.PAYABLE,
];

const MedClaimPaidStatus = [
    MedClaimStatus.PAYABLE,
    MedClaimStatus.PARTIALLY_PAID,
];

const ClaimReportTypes = {
    STATUS_REPORT: "277",
    PAYER_RESPONSE: "835",
};

const MedClaimFrequencyCode = {
    ORIGINAL_CLAIM: "1",
    REPLACEMENT_OF_PRIOR_CLAIM: "7",
    VOID_CANCEL_OF_PRIOR_CLAIM: "8",
    FINAL_CLAIM_FOR_A_PERIOD_OF_CONTINUOUS_CARE: "9",
};

const Claim277Status = {
    PROCESSING: "Processing",
    RESPONSE_PROCESSING_ERROR: "Response Processing Error",
    RECEIVED: "Received",
    ACCEPTED: "Accepted",
    FORWARDED: "Forwarded",
    REJECTED: "Rejected",
    REVERSED: "Reversed",
    REVISED: "Revised",
    WARNING: "Warning",
    DENIED: "Denied",
    ON_HOLD: "On-hold",
    NOT_FOUND: "Not Found",
    PAYER_NOT_RESPONDING: "Payer Not Responding, Try Again Later",
    SUBMISSION_ERROR: "Submission Error",
    AWAITING_REQUESTED_INFORMATION: "Awaiting Requested Information",
    REQUEST_FOR_ADDITIONAL_INFORMATION: "Request for Additional Information",
    UNDER_REVIEW: "Under Review",
    PARTIALLY_PAID: "Partially Paid",
    PAID: "Paid",
};

module.exports = {
    TestValues,
    MedClaimFrequencyCode,
    MedClaimStatus,
    ClaimStatus277FinalCodes,
    Claim277Status,
    ClaimReportTypes,
    MedClaimStatusOpenStatus,
    MedClaimPaidStatus,
    MedClaimStatusBilledStatus,
    MedClaim835FinalStatuses,
    MedClaimCOBEligibleStatus,
    ...medicalSettings,
};
