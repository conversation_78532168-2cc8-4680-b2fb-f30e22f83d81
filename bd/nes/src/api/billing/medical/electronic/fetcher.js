/*jshint : 6 */
"use strict";

const _ = require("lodash");
const Joi = require("joi").extend(require("@joi/date"));

const BillingFetcherClass = require("@billing/fetcher");

/**
 * @class
 * @classdesc Fetcher class for MedicalClaims billing processes.
 * @extends BillingFetcherClass
 */
module.exports = class MedicalElectronicClaimsFetcherClass extends (
    BillingFetcherClass
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
    }

    /**
     * Fetches a claim record based on the claim no.
     *
     * @async
     * @param {string} claimNo - The claim no of the claim to fetch.
     * @returns {Promise<Object|null>} The claim record if found, or null if not found.
     * @throws {Error} If there's an error fetching the claim record.
     */
    async fetchClaimWithClaimNumber(claimNo) {
        console.debug(`Fetching claim records for claim no ${claimNo}`);
        try {
            await this.fx.validateSchema(
                "claimNo",
                Joi.string().required(),
                claimNo,
                "Missing claim no to fetch claim record."
            );

            const filters = [`claim_no:${claimNo}`];
            const medClaimRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "med_claim",
                    { sort: "-id", filter: filters, limit: 1 }
                )
            );

            return medClaimRec;
        } catch (e) {
            const errorMessage = `Error encountered while fetching claim records for 277 response`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
