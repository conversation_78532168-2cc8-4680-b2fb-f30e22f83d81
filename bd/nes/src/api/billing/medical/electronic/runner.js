/*jshint : 6 */
"use strict";
const _ = require("lodash");
const Joi = require("joi");

const ClaimRunnerClass = require("@billing/runner");
const { ActionResponseWrappers } = require("@actions");
const { ErrorMessages } = require("./errors");
const { RecordSchemas } = require("@billing/schemas");
const {
    ClaimType,
    TestValues,
    MedClaimStatusOpenStatus,
    MedClaimFrequencyCode,
} = require("./settings");

/**
 * @class
 * @classdesc Class for running Med Claims.
 * @extends ClaimRunnerClass
 */
module.exports = class MedicalElectronicClaimsRunnerClass extends (
    ClaimRunnerClass
) {
    constructor(nes, ctx) {
        super(nes, ctx, "med_claim");
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        const ClaimValidatorClass = require("@billing/validator");
        this.validator = this.fx.getInstance(
            ctx,
            ClaimValidatorClass,
            true,
            this.nes,
            this.ctx
        );
        const MedicalElectronicClaimsFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const MedicalElectronicClaimsHelperClass = require("./helper");
        this.helper = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsHelperClass,
            true,
            this.nes,
            this.ctx
        );
        const MedicalElectronicClaimsPreprocessorClass = require("./preprossor");
        this.preprocessor = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsPreprocessorClass,
            true,
            this.nes,
            this.ctx
        );
        const MedicalElectronicClaimsReceiverClass = require("./receiver");
        this.receiver = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsReceiverClass,
            true,
            this.nes,
            this.ctx
        );
        this.validateOnly = false;
    }

    /**
     * Runs a Medical claim for the given primary payer.
     *
     * @param {Object} transaction - The transaction object.
     * @param {Object} medClaimRec - The Medical Claim record.
     * @param {Object|null} transaction - The transaction object.
     * @param {Boolean} [isTest=false] - true if running a test claim
     * @returns {Promise<Array|Object>} An array containing the processed claim or an error object.
     */
    async runClaim(transaction, medClaimRec, isTest = false) {
        console.debug(
            `Running medical claim for payer ID ${medClaimRec.payer_id}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to run medical claim."
                ),
                this.fx.validateSchema(
                    "medClaimRec",
                    RecordSchemas.medicalClaim.required(),
                    medClaimRec,
                    "Missing medical claim record to run claim."
                ),
            ]);

            const claimNo = medClaimRec.claim_no;
            const ogClaimCopy = JSON.parse(JSON.stringify(medClaimRec));

            const validatedClaimRec =
                await this.__validateAndPrepareClaimData(medClaimRec);
            if (validatedClaimRec.validationErrors)
                return {
                    beforeResponse: true,
                    validationErrors: validatedClaimRec.validationErrors,
                    rawErrorMessages: validatedClaimRec.rawErrorMessages,
                    error: ErrorMessages.CLAIM_VALIDATION_ERROR,
                    record: medClaimRec,
                };

            const sql = `
                SELECT
                    mm_build_claim_json(
                    %s::integer
                ) as result
                `;

            const sqlParams = [medClaimRec.id];

            const resultRow = await this.db.env.rw.parseSQLUsingPGP(
                sql,
                sqlParams
            );

            if (resultRow.length === 0) {
                return ActionResponseWrappers.error(
                    `Error generating final claim record for claim no ${medClaimRec.claim_no}`
                );
            }
            const postValidationClaimRec = resultRow[0].result;

            const finalClaimRec = isTest
                ? await this.helper.replaceTestValues(postValidationClaimRec)
                : postValidationClaimRec;

            if (isTest) {
                finalClaimRec.billing.employerId = TestValues.employerId[0];
            }
            super.debug(
                `Sending Claim for payer ID ${medClaimRec.payer_id} and patient ID ${medClaimRec.patient_id}`
            );

            const patientRec = await this.fetcher.fetchCachedRecord(
                "patient",
                medClaimRec.patient_id
            );
            const _meta = this.__createMetaData(patientRec, medClaimRec);

            if (
                !this.validateOnly ||
                this.shared.config.env["NODE_ENV"] !== "production"
            ) {
                const errorMessage = `Medical claims can only be submitted in production.`;
                console.error(`${errorMessage}`);
                return {
                    beforeResponse: true,
                    validationErrors: {
                        form: [errorMessage],
                    },
                    error: errorMessage,
                    record: medClaimRec,
                };
            }
            const response = await this.__sendClaimRequest(
                finalClaimRec,
                _meta
            );
            if (response.error) {
                const errorMessage = `Error encountered running claim no ${claimNo}`;
                console.error(`${errorMessage} Error:`, response.error);
                return {
                    beforeResponse: true,
                    validationErrors: {
                        form: [response.error],
                    },
                    error: ErrorMessages.MED_CLAIM_RUNNER_ERROR,
                    record: medClaimRec,
                };
            }

            const returnVal = await this.receiver.processClearingHouseResponse(
                transaction,
                ogClaimCopy,
                response
            );

            return returnVal;
        } catch (e) {
            return this.__handleRunnerError(e, medClaimRec.payer_id);
        }
    }

    /**
     * Reverses a medical claim.
     *
     * @param {Object} transaction - The transaction object.
     * @param {Object} medClaimRec - The medical claim record to be reversed.
     * @returns {Promise<Object>} The result of the reversal process.
     */
    async reverseClaim(transaction, medClaimRec) {
        console.debug(
            `Reversing medical claim for payer ID ${medClaimRec.payer_id}`
        );

        try {
            if (MedClaimStatusOpenStatus.includes(medClaimRec.status)) {
                return {
                    error: `Medical claim is not in a reversible status. Claim status is ${medClaimRec.status}.`,
                };
            }

            medClaimRec.claim_information[0].claim_frequency_code =
                MedClaimFrequencyCode.VOID_CANCEL_OF_PRIOR_CLAIM;
            return await this.runClaim(transaction, medClaimRec);
        } catch (e) {
            return this.__handleRunnerError(e, medClaimRec.payer_id);
        }
    }

    /**
     * Resubmits a medical claim.
     *
     * @param {Object} transaction - The transaction object.
     * @param {Object} medClaimRec - The medical claim record to be resubmitted.
     * @returns {Promise<Object>} The result of the resubmission process.
     */
    async rebillClaim(transaction, medClaimRec) {
        console.debug(
            `Resubmitting medical claim for payer ID ${medClaimRec.payer_id}`
        );

        try {
            if (MedClaimStatusOpenStatus.includes(medClaimRec.status)) {
                console.warn(
                    `Medical claim is not in a resubmission status. Claim status is ${medClaimRec.status}. Submitting claim through normal process...`
                );
                return await this.runClaim(transaction, medClaimRec);
            }

            medClaimRec.claim_information[0].claim_frequency_code =
                MedClaimFrequencyCode.REPLACEMENT_OF_PRIOR_CLAIM;
            const returnVal = await this.runClaim(transaction, medClaimRec);
            return returnVal;
        } catch (e) {
            return this.__handleRunnerError(e, medClaimRec.payer_id);
        }
    }

    /**
     * Validates a medical claim.
     *
     * @async
     * @param {Object} transaction - The transaction object.
     * @param {Object} medClaimRec - The medical claim record to be validated.
     * @returns {Promise<Object>} The result of the validation process.
     */
    async validateClaim(transaction, medClaimRec) {
        console.debug(
            `Validating medical claim for payer ID ${medClaimRec.payer_id}`
        );

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to validate medical claim."
                ),
            ]);
            this.validateOnly = true;
            const returnVal = await this.runClaim(transaction, medClaimRec);
            return returnVal;
        } catch (e) {
            return this.__handleRunnerError(e, medClaimRec.payer_id);
        }
    }

    /**
     * Fetches the success message for a pharmacy claim submission
     * @async
     * @param {Object} medClaimRec - The NCPDP claim record
     * @returns {Promise<string>} A promise that resolves to the success message
     */
    async fetchSuccessMessage(medClaimRec) {
        const frequencyCode =
            medClaimRec.claim_information[0].claim_frequency_code;
        if (
            frequencyCode === MedClaimFrequencyCode.VOID_CANCEL_OF_PRIOR_CLAIM
        ) {
            return `Claim has been successfully reversed.`;
        }
        if (this.validateOnly) {
            return `Claim has been successfully validated.`;
        }
        return `Claim has been successfully submitted. Please check response for remittance information`;
    }

    /**
     * Validates and prepares claim data.
     *
     * @param {Object} medClaimRec - The Claim record.
     * @returns {Promise<Object>} Validated claim data or error object.
     * @private
     */
    async __validateAndPrepareClaimData(medClaimRec) {
        const medClaimRecCopy = JSON.parse(JSON.stringify(medClaimRec));
        const results = await this.validator.validateClaim(
            medClaimRecCopy,
            ClaimType.MEDICAL
        );
        if (results?.validationErrors) return results;
        return results;
    }

    /**
     * Sends the claim request.
     *
     * @param {Object} submissionClaimRec - The validated claim data.
     * @param {Object} _meta - The metadata object.
     * @returns {Promise<Object>} The response from the claim request.
     * @private
     */
    async __sendClaimRequest(submissionClaimRec, _meta) {
        const url = this.validateOnly
            ? "claims/med/validate"
            : "claims/med/submit";
        return this.nes.modules.fx.adminRequest(this.ctx, url, {
            method: "POST",
            data: {
                ...submissionClaimRec,
                _meta,
            },
        });
    }

    /**
     * Creates metadata for the claim.
     *
     * @param {Object} patientRec - The patient record.
     * @param {Object} medClaimRec - The validated claim data.
     * @returns {Object} Metadata object.
     * @private
     */
    __createMetaData(patientRec, medClaimRec) {
        return {
            clara_mrn: patientRec?.mrn,
            clara_claim_uuid: medClaimRec.uuid,
        };
    }

    /**
     * Handles errors in the runClaim function.
     *
     * @param {Error} e - The caught error.
     * @param {string} payerId - The payer ID.
     * @returns {Object} An error object.
     * @private
     */
    __handleRunnerError(e, payerId) {
        super.error(
            `Error encountered running claim for payer ID ${payerId} Error: ${e.message} Stack: ${e.stack}`
        );
        return {
            error: ErrorMessages.NCPDP_RUNNER_EXCEPTION,
        };
    }
};
