/*jshint : 6 */
"use strict";
const _ = require("lodash");

const BillingHelperClass = require("@billing/helper");
const { ErrorMessages } = require("./errors");

const { TestValues } = require("./settings");

/**
 * @class
 * @classdesc Helper class for Medical Claims billing processes.
 * @extends BillingHelperClass
 */
module.exports = class MedicalElectronicClaimsHelperClass extends (
    BillingHelperClass
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
        this.db = nes.modules.db;

        const MedicalElectronicClaimsFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Replaces field values with test values in the medical claim record.
     *
     * @async
     * @param {Object} medClaimRec - The medical claim record to modify.
     * @returns {Promise<Object>} The modified medical claim record with test values.
     * @throws {Error} If an error occurs during the replacement process.
     */
    async replaceTestValues(medClaimRec) {
        console.debug(
            `Replacing field values with test values before sending claim`
        );
        try {
            const replaceTestValuesRecursively = (obj) => {
                for (const [key, value] of Object.entries(obj)) {
                    if (typeof value === "object" && value !== null) {
                        replaceTestValuesRecursively(value);
                    } else if (TestValues.hasOwnProperty(key)) {
                        const testValues = TestValues[key];
                        obj[key] =
                            testValues[
                                Math.floor(Math.random() * testValues.length)
                            ];
                    }
                }
            };

            replaceTestValuesRecursively(medClaimRec);

            return medClaimRec;
        } catch (e) {
            const errorMessage = `Error encountered while replacing field names with test data`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Retrieves and processes the claim response.
     *
     * @async
     * @param {Object} results - The results object containing the claim response.
     * @param {String} type - The type of the response.
     * @param {String} responseSource - The source of the response.
     * @returns {Promise<Object>} The validated JSON result.
     * @throws {Error} If JSON data is missing.
     */
    async parsePayerClaimResponse(results, type, responseSource) {
        console.debug(`Processing claim response`);

        try {
            const metaData = _.omit(results, ["raw_json", "raw_x12"]);
            const responseRawJson = results.raw_json || {};
            const responseRawX12 = results.raw_x12 || "";
            if (!responseRawJson) {
                const errorMessage = `Response missing JSON data for claim`;
                console.error(`${errorMessage}`);
                throw this.fx.wrapError(
                    ErrorMessages.MED_CLAIM_RESPONSE_MISSING_JSON,
                    errorMessage
                );
            }

            const responseLog = {
                response_type: type,
                response_source: responseSource,
                response_meta: JSON.stringify(metaData),
                request_raw_x12: null,
                request_raw_json: null,
                response_raw_x12: responseRawX12,
                response_raw_json: JSON.stringify(responseRawJson),
                raw_report_url: results?.raw_report_url || null,
                s3_filepath: results?.s3_filepath || null,
            };
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("med_claim_resp_log", responseLog);
            const res = await transaction.commit();
            if (res.error)
                throw this.fx.getClaraError(
                    "Error saving claim response into response log"
                );
            transaction.init();
            const formId = _.head(
                this.fx.fetchFormIdsFromTransactionResults(
                    "med_claim_resp_log",
                    res
                )
            );

            if (!formId) {
                throw this.fx.getClaraError(
                    `Error encountered while saving payer response type ${type} into response log`
                );
            }
            const filters = [`response_id:${formId}`];
            const responseRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                type === "277" ? "med_claim_resp_277" : "med_claim_resp_835",
                { filter: filters }
            );

            if (!responseRecs || responseRecs.length === 0) {
                throw this.fx.getClaraError(
                    `Error encountered while saving payer report response type ${type} into response log`
                );
            }
            return {
                metaData,
                responseRawX12,
                responseRawJson,
                responseRecs,
            };
        } catch (e) {
            const errorMessage = `Error encountered while processing claim response`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Parses the clearing house response.
     *
     * @async
     * @param {Object} results - The results object from the clearing house.
     * @returns {Promise<Object>} The parsed response containing metadata, raw request and response data.
     * @throws {Error} If there's an error processing the clearing house response.
     */
    async parseClearinghouseResponse(results) {
        console.debug(`Processing clearing house response`);

        try {
            const metaData = results._meta || {};
            const requestRawX12 = results.request_x12_raw || "";
            const requestRawJson =
                results.request_json_data || results.original_claim || {};
            const responseRawJson =
                results.response_json_data || results.reports || {};
            const responseRawX12 = results.response_x12_raw || "";
            if (!responseRawJson) {
                const errorMessage = `Response missing JSON data for claim`;
                console.error(`${errorMessage}`);
                throw this.fx.wrapError(
                    ErrorMessages.MED_CLAIM_RESPONSE_MISSING_JSON,
                    errorMessage
                );
            }

            const responseLog = {
                response_type: "999",
                response_source: "Client",
                response_meta: JSON.stringify(metaData),
                request_raw_x12: requestRawX12,
                request_raw_json: JSON.stringify(requestRawJson),
                response_raw_x12: responseRawX12,
                response_raw_json: JSON.stringify(responseRawJson),
            };
            const transaction = this.db.env.rw.transaction(this.ctx);
            await transaction.insert("med_claim_resp_log", responseLog);
            const res = await transaction.commit();
            if (res.error)
                throw this.fx.getClaraError(
                    "Error saving clearing house response into response log"
                );
            transaction.init();
            const formId = _.head(
                this.fx.fetchFormIdsFromTransactionResults(
                    "med_claim_resp_log",
                    res
                )
            );

            if (!formId) {
                throw this.fx.getClaraError(
                    "Error encountered while saving clearing house response into response log"
                );
            }
            const filters = [`response_id:${formId}`];
            const responseRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "med_claim_resp",
                    { limit: 1, filter: filters }
                )
            );

            if (!responseRec) {
                throw this.fx.getClaraError(
                    "Error encountered while fetching clearing house response record"
                );
            }
            return {
                metaData,
                requestRawX12,
                requestRawJson,
                responseRawX12,
                responseRawJson,
                responseRec,
            };
        } catch (e) {
            const errorMessage = `Error encountered while processing claim response`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
