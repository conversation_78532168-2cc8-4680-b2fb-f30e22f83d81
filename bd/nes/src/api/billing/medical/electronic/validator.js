"use strict";
const { ModuleLogger } = require("@core/logger");
const { RecordSchemas } = require("@billing/schemas");

/**
 * @class
 * @classdesc Class for validating Medical claims within the pharmacy billing module.
 * @extends ModuleLogger
 */
module.exports = class MedicalElectronicClaimsValidatorClass extends (
    ModuleLogger
) {
    constructor(nes, ctx) {
        super("837p-validator");
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        const MedicalElectronicClaimsFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            MedicalElectronicClaimsFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Validates an Medical claim by checking various required fields and conditions.
     * @param {Object} medClaimRec - The Medical claim object to validate.
     * @returns {Promise<Object>} The validation result containing transformed data and field errors.
     * @throws {Error} If validation fails or encounters errors.
     */
    async validateClaim(medClaimRec) {
        super.debug(`Validating claim ID ${medClaimRec?.id} before running`);

        const claimNo = medClaimRec.claim_no;
        try {
            await this.fx.validateSchema(
                "medClaimRec",
                RecordSchemas.medicalClaim.required(),
                medClaimRec,
                "Missing medical claim to validate."
            );
            const results = await this.__validateSchema(medClaimRec, claimNo);
            return results;
        } catch (e) {
            this.__handleValidationError(e, medClaimRec, claimNo);
        }
    }

    /**
     * Validates the NCPDP claim against the schema.
     * @param {Object} medClaimRec - The Medical claim object to validate.
     * @param {String} claimNo - The Claim No.
     * @returns {Promise<Object>} The validation result containing transformed data and field errors.
     * @throws {Error} If schema validation fails.
     * @private
     */
    async __validateSchema(medClaimRec, claimNo) {
        try {
            medClaimRec = this.fx.removeNulls(medClaimRec);
            const validate =
                await this.shared.schema.getSchemaValidator("837p");
            const success = validate(medClaimRec);
            if (!success) {
                super.debug(
                    `Validation failed with the following errors. ${JSON.stringify(validate.errors)}`
                );
                const { validationErrors, rawErrorMessages } =
                    await this.shared.schema.processValidationErrors(
                        "med_claim",
                        validate.errors,
                        validate
                    );
                super.debug(
                    `Mapped field errors: ${JSON.stringify(validationErrors)}`
                );
                return {
                    transformedData: medClaimRec,
                    validationErrors,
                    rawErrorMessages,
                };
            }
            return {
                transformedData: medClaimRec,
                validationErrors: null,
                rawErrorMessages: null,
            };
        } catch (e) {
            const errorMessage = `Error validating schema for claim no ${claimNo}`;
            super.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Handles and logs validation errors.
     * @param {Error} error - The error that occurred during validation.
     * @param {String} claimNo - The Claim No
     * @throws {Error} The original error is re-thrown after logging.
     * @private
     */
    __handleValidationError(error, claimNo) {
        const errorMessage = `Exception while validating claim ID ${claimNo}`;
        super.error(
            `${errorMessage}. Error:${error.message} Stack:${error.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
        );
        return { error: errorMessage };
    }
};
