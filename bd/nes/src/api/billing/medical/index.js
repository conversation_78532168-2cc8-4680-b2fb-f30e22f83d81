"use strict";

const MedicalElectronicClaimsReceiverClass = require("./electronic/receiver");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    async __processReportsResponse(ctx, data) {
        console.log("Processing payer reports response");

        try {
            const reportsResponse = data;
            if (
                !reportsResponse ||
                !reportsResponse?.raw_json ||
                !reportsResponse?.report_type
            ) {
                ctx.status = 500;
                ctx.body = {
                    error: "Invalid reports response. Missing JSON data.",
                };
                return;
            }

            const receiver = new MedicalElectronicClaimsReceiverClass(
                this.nes,
                ctx
            );
            const result = await receiver.processReportsResponse(data);
            ctx.body = result;
            ctx.status = 200;
        } catch (e) {
            ctx.status = 500;
            ctx.body = {
                error: `Unable to process reports response. Error: ${e.message}`,
            };
            return;
        }
    }

    async process(ctx, urlpath) {
        if (urlpath.path.length < 4) {
            ctx.status = 500;
            ctx.body = {
                error: "Invalid path specified: " + urlpath.url.path,
            };
            return;
        }
        const action = urlpath.path[3];
        const data = ctx.request.body;

        if (!data) {
            ctx.status = 500;
            ctx.body = { error: "Missing data" };
            return;
        }

        switch (action) {
            case "reports":
                await this.__processReportsResponse(ctx, data);
                break;

            default:
                ctx.status = 500;
                ctx.body = { error: "Invalid action name: " + action };
                return;
        }
    }
};
