/*jshint : 6 */
"use strict";

const _ = require("lodash");
const moment = require("moment-timezone");
const currency = require("currency.js");
const Joi = require("joi");

const { ErrorMessages } = require("./errors");
const { FormsToClosePeriod, FormsToCloseFilters } = require("./settings");
const { RecordSchemas } = require("./schemas");

/**
 * @class
 * @classdesc Class responsible for closing billing periods.
 */
module.exports = class BillingCloseClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.db = nes.modules.db;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.pgboss = nes.modules.pgboss;
    }

    /**
     * Builds a close period record.
     * @async
     * @function buildClosePeriodRecord
     * @returns {Promise<Object>} The result of the close period operation, which may include an error message if the operation fails.
     * @throws {Error} If an unexpected error occurs during the process.
     */
    async buildClosePeriodRecord() {
        console.debug("Checking Insurance Status");

        let recordsLocked = false;
        let closingRecs = null;
        let startDate = null;
        let endDate = null;
        let lastPeriodRec = null;
        try {
            const datesRes = await this.__getPeriodDates();
            startDate = datesRes.startDate;
            endDate = datesRes.endDate;
            lastPeriodRec = datesRes.lastPeriodRec;
            const transaction = this.db.env.rw.transaction(this.ctx);
            closingRecs = await this.__getPeriodRecords(
                transaction,
                startDate,
                endDate
            );

            const res = await this.__lockClosingPeriodRecs(
                transaction,
                closingRecs,
                true
            );
            if (res.error) {
                console.error(
                    `Error encountered locking records: ${res.error}`
                );
                return res;
            }
        } catch (e) {
            console.error(
                `Unable to lock records for closing period: ${e.message}`
            );
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_LOCK_ERROR);
        }
        recordsLocked = true;

        try {
            const closingRec = await this.__buildClosePeriodRecord(
                startDate,
                endDate,
                closingRecs
            );
            await this.__addLastPeriodData(closingRec, lastPeriodRec);
            return closingRec;
        } catch (e) {
            console.error(`Unable to build next closing period: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        } finally {
            console.error("Error encountered unlocking records...");
            if (recordsLocked) {
                await this.worker_unlock_record_billing({}, closingRecs);
            }
        }
    }

    /**
     * Unlocks the records associated with a period closing.
     * @async
     * @function unlockPeriodClosingRecords
     * @param {Object} transaction - The transaction object for database operations.
     * @param {Object} associatedRecords - The records associated with the period closing to be unlocked.
     * @returns {Promise<Object>} An object indicating success or containing an error message.
     * @throws {Error} If unable to unlock the period closing records.
     */
    async unlockPeriodClosingRecords(transaction, associatedRecords) {
        console.log("Unlocking Period Closing Records");

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to unlock period closing records."
                ),
                this.fx.validateSchema(
                    "associatedRecords",
                    Joi.object().required(),
                    associatedRecords,
                    "Missing associated records to unlock period closing records."
                ),
            ]);
            const updatePromises = [];
            for (const [formName, formRecs] of Object.entries(
                associatedRecords
            )) {
                formRecs.forEach((rec) => {
                    rec.locked = null;
                    rec.locked_by = null;
                    rec.locked_datetime = null;
                    updatePromises.push(
                        transaction.update(formName, rec, rec.id)
                    );
                });
            }
            await Promise.all(updatePromises);
        } catch (e) {
            console.error(`Unable to unlock close period: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        }
    }

    /**
     * Performs pre-save processing for closing a billing period.
     * @async
     * @function preSaveCloseProcessing
     * @param {Object} transaction - The transaction object for database operations.
     * @param {Object} closingRec - The period record to be processed.
     * @returns {Promise<void>}
     * @throws {Error} If unable to process the period closing record.
     */
    async preSaveCloseProcessing(transaction, closingRec) {
        console.log("Saving Period Closing Record");

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "transaction",
                    Joi.object().required(),
                    transaction,
                    "Missing transaction to pre-save period closing record."
                ),
                this.fx.validateSchema(
                    "closingRec",
                    RecordSchemas.closing.required(),
                    closingRec,
                    "Missing closing record to pre-save period closing record."
                ),
            ]);

            let closeNo = closingRec?.close_no || null;
            if (!closeNo) {
                closeNo = transaction.series_next_number("CLOSE_PERIOD");
                closingRec.close_no = closeNo;
            }
            const associatedRecords = JSON.parse(closingRec.associated_records);
            const updatePromises = [];
            for (const [formName, formRecs] of Object.entries(
                associatedRecords
            )) {
                for (const rec of formRecs) {
                    rec.close_no = closeNo;
                    rec.locked = null;
                    rec.locked_by = null;
                    rec.locked_datetime = null;
                    updatePromises.push(
                        transaction.update(formName, rec, rec.id)
                    );
                }
            }
            await Promise.all(updatePromises);
        } catch (e) {
            console.error(`Unable to save period closing record: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        }
    }

    /**
     * Unlocks a closed billing period.
     * @async
     * @function unlockClosePeriod
     * @param {Object} closingRec - The period record to unlock.
     * @param {Object} unlockRec - The record to unlock.
     * @returns {Promise<Object>} An object indicating success or containing an error message.
     * @throws {Error} If unable to unlock the close period.
     */
    async unlockClosePeriod(closingRec, unlockRec) {
        console.log("Unlocking Close Period");

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "closingRec",
                    RecordSchemas.closing.required(),
                    closingRec,
                    "Missing closing record to unlock period closing record."
                ),
                this.fx.validateSchema(
                    "unlockRec",
                    RecordSchemas.closingUnlock.required(),
                    unlockRec,
                    "Missing unlock record to unlock period closing record."
                ),
            ]);
            const transaction = this.db.env.rw.transaction(this.ctx);
            const associatedRecords = JSON.parse(closingRec.associated_records);
            const updatePromises = [];
            for (const [formName, formRecs] of Object.entries(
                associatedRecords
            )) {
                for (const rec of formRecs) {
                    rec.locked = null;
                    rec.locked_by = null;
                    rec.locked_datetime = null;
                    updatePromises.push(
                        transaction.update(formName, rec, rec.id)
                    );
                }
            }
            await Promise.all(updatePromises);

            closingRec.locked_period = "No";
            closingRec.needs_refreshed = "Yes";
            if (Array.isArray(closingRec.subform_unlock)) {
                closingRec.subform_unlock.push(unlockRec);
            } else {
                closingRec.subform_unlock = [unlockRec];
            }

            await transaction.update(
                "billing_closing",
                closingRec,
                closingRec.id
            );
            const res = await transaction.commit();
            if (res.error) {
                console.error(
                    `Error encountered unlocking records: ${res.error}`
                );
                return {
                    error: ErrorMessages.CLOSE_PERIOD_UNLOCK_COMMIT_ERROR,
                };
            }
            transaction.init();

            return { success: "Period Successfully Unlocked" };
        } catch (e) {
            console.error(`Unable to unlock close period: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        }
    }

    /**
     * Refreshes an open billing period.
     * @async
     * @function refreshAndLockPeriod
     * @param {Object} closingRec - The period record to refresh.
     * @param {Object} refreshAndLockRec - The record to lock.
     * @returns {Promise<Object>} The refreshed period record.
     * @throws {Error} If unable to refresh the open period.
     */
    async refreshAndLockPeriod(closingRec, refreshAndLockRec) {
        console.log("Refreshing and Locking Open Period");
        try {
            await Promise.all([
                this.fx.validateSchema(
                    "closingRec",
                    RecordSchemas.closing.required(),
                    closingRec,
                    "Missing closing record to refresh and lock period closing record."
                ),
                this.fx.validateSchema(
                    "lockRec",
                    RecordSchemas.closingRelock.required(),
                    refreshAndLockRec,
                    "Missing refresh and lock record to refresh and lock period closing record."
                ),
            ]);
            const startDate = closingRec.start_date;
            const endDate = closingRec.end_date;
            const transaction = this.db.env.rw.transaction(this.ctx);
            const closingRecs = await this.__getPeriodRecords(
                transaction,
                startDate,
                endDate
            );

            const lockingRes = await this.__lockClosingPeriodRecs(
                transaction,
                closingRecs,
                false
            );
            if (lockingRes.error) {
                console.error(
                    `Error encountered locking records: ${lockingRes.error}`
                );
                return lockingRes;
            }

            const updatedPeriodRec = await this.__buildClosePeriodRecord(
                startDate,
                endDate,
                closingRecs
            );
            const timeZone = this.ctx.user.timezone;
            const lockedDateTime = moment()
                .tz(timeZone)
                .format("MM/DD/YYYY HH:mm:ss");
            updatedPeriodRec.needs_refreshed = "No";
            updatedPeriodRec.locked_period = "Yes";
            updatedPeriodRec.locked_datetime = lockedDateTime;
            if (Array.isArray(updatedPeriodRec.subform_lock)) {
                updatedPeriodRec.subform_lock.push(refreshAndLockRec);
            } else {
                updatedPeriodRec.subform_lock = [refreshAndLockRec];
            }

            const mergedPeriodRec = {
                ...closingRec,
                ...updatedPeriodRec,
            };
            await transaction.update(
                "billing_closing",
                mergedPeriodRec,
                closingRec.id
            );
            const res = await transaction.commit();
            if (res.error) {
                console.error(
                    `Error encountered locking records: ${res.error}`
                );
                return res;
            }
            transaction.init();

            return mergedPeriodRec;
        } catch (e) {
            console.error(
                `Unable to lock records for closing period: ${e.message}`
            );
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_LOCK_ERROR);
        }
    }

    /**
     * Locks the closing period records.
     * @param {Object} transaction - The transaction object for database operations.
     * @param {Object} closingRecs - The period records to be locked.
     * @returns {Promise<Object>} A promise that resolves to the locked period records or an error object.
     * @private
     */
    async __lockClosingPeriodRecs(transaction, closingRecs, commit = true) {
        console.log("Getting Closing Period Record");
        try {
            if (closingRecs.errors.length > 0) {
                return {
                    error:
                        ErrorMessages.CLOSE_PERIOD_LOCK_ERROR +
                        closingRecs.errors.join("\n"),
                };
            }

            delete closingRecs.errors;
            const allArraysEmpty = Object.values(closingRecs).every(
                (value) => Array.isArray(value) && value.length === 0
            );

            if (allArraysEmpty) {
                return {
                    error: ErrorMessages.CLOSE_PERIOD_NO_RECORDS,
                };
            }

            if (commit) {
                const res = await transaction.commit();
                if (res.error) {
                    console.error(
                        `Error encountered locking records: ${res.error}`
                    );
                    return {
                        error: ErrorMessages.CLOSE_PERIOD_LOCK_COMMIT_ERROR,
                    };
                }
                transaction.init();
            }

            await this.__scheduleUnlockRecords(closingRecs);
            return null;
        } catch (e) {
            console.error(`Unable to get closing period record: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        }
    }

    /**
     * Schedules the unlocking of records after a specified delay.
     * @async
     * @function __scheduleUnlockRecords
     * @param {Object} closingRecs - The period records to be unlocked.
     * @returns {Promise<void>}
     * @throws {Error} If unable to schedule the unlock operation.
     */
    async __scheduleUnlockRecords(closingRecs) {
        const job = "unlock_record";
        const eventName = "billing";
        const jobOptions = {
            startAfter: "30 minutes",
        };
        await this.pgboss.jobSend(job, eventName, closingRecs, jobOptions);
        console.log("Scheduling Unlock Records");
    }

    /**
     * Builds a close period record with the provided data.
     * @async
     * @param {string} startDate - The start date of the period.
     * @param {string} endDate - The end date of the period.
     * @param {Object} closingRecs - The period records.
     * @returns {Promise<Object>} The built close period record.
     * @throws {Error} If unable to build the closing period.
     */
    async __buildClosePeriodRecord(startDate, endDate, closingRecs) {
        try {
            const timeZone = this.ctx.user.timezone;
            const lockedDateTime = moment()
                .tz(timeZone)
                .format("MM/DD/YYYY HH:mm:ss");

            const [ar, ap, cost, credits, debits] = await Promise.all([
                this.__getPeriodAR(startDate, endDate),
                this.__getPeriodAP(startDate, endDate),
                this.__getPeriodCost(startDate, endDate),
                this.__getPeriodCredits(startDate, endDate),
                this.__getPeriodDebits(startDate, endDate),
            ]);
            const closingRec = {
                locked_datetime: lockedDateTime,
                start_date: endDate,
                end_date: currency(ar).value,
                tot_ar: currency(ap).value,
                tot_ap: currency(cost).value,
                tot_cost: currency(credits).value,
                tot_credits: currency(debits).value,
                tot_debits: currency(debits).value,

                locked_period: "Yes",
                associated_records: JSON.stringify(closingRecs),
            };
            return closingRec;
        } catch (e) {
            console.error(`Unable to build next closing period: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        }
    }

    /**
     * Adds data from the last period to the current period record.
     * @async
     * @param {Object} closingRec - The current period record to update.
     * @param {Object} lastPeriodRec - The last period record to get data from.
     * @returns {Promise<void>}
     * @throws {Error} If unable to add last period data.
     */
    async __addLastPeriodData(closingRec, lastPeriodRec) {
        console.log("Adding Last Period Data");
        try {
            closingRec.last_start = lastPeriodRec?.start_date || null;
            closingRec.last_end = lastPeriodRec?.end_date || null;
            closingRec.last_tot_ar = lastPeriodRec?.tot_ar || null;
            closingRec.last_tot_ap = lastPeriodRec?.tot_ap || null;
            closingRec.last_tot_cost = lastPeriodRec?.tot_cost || null;
            closingRec.last_tot_credits = lastPeriodRec?.tot_credits || null;
            closingRec.last_tot_debits = lastPeriodRec?.tot_debits || null;
        } catch (e) {
            console.error(`Unable to add last period data: ${e.message}`);
            throw this.fx.wrapError(
                e,
                ErrorMessages.CLOSE_PERIOD_LAST_PERIOD_DATA_ERROR
            );
        }
    }

    /**
     * Unlocks records for the given period.
     * @async
     * @function worker_unlock_record_billing
     * @param {Object} closingRecs - The period records to unlock.
     * @returns {Promise<void>}
     * @throws {Error} If unable to unlock records.
     */
    worker_unlock_record_billing = async (mockCtx, job) => {
        console.log("Unlocking Records...");
        const closingRecs = job.data;
        try {
            if (_.isEmpty(mockCtx)) mockCtx = await this.fx.adminNewContext();
            const transaction = this.db.env.rw.transaction(mockCtx);
            let formsUpdated = false;
            const updatePromises = [];
            for (const [formName, formRecs] of Object.entries(
                closingRecs.associated_records
            )) {
                for (const rec of formRecs) {
                    if (rec.close_no) continue;
                    rec.locked = null;
                    rec.locked_by = null;
                    rec.locked_datetime = null;
                    formsUpdated = true;
                    updatePromises.push(
                        transaction.update(formName, rec, rec.id)
                    );
                }
            }
            if (!formsUpdated) return;
            await Promise.all(updatePromises);
            const res = await transaction.commit();
            if (res.error) {
                console.error(
                    `Error encountered unlocking records: ${res.error}`
                );
                return {
                    error: ErrorMessages.CLOSE_PERIOD_UNLOCK_COMMIT_ERROR,
                };
            }
            transaction.init();
        } catch (e) {
            console.error(`Unable to unlock records: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_ERROR);
        }
    };

    /**
     * Retrieves period records within the specified date range.
     * @async
     * @function __getPeriodRecords
     * @param {Object} transaction - The database transaction object.
     * @param {string} startDate - The start date of the period (format: MM/DD/YYYY).
     * @param {string} endDate - The end date of the period (format: MM/DD/YYYY).
     * @returns {Promise<Object>} An object containing the period records and any errors encountered.
     * @throws {Error} If unable to retrieve period records.
     */
    async __getPeriodRecords(transaction, startDate, endDate) {
        console.log("Getting Period Records");

        try {
            const formRecords = { errors: [] };
            for (const formName of FormsToClosePeriod) {
                console.log(`Fetching records for ${formName}`);
                const filters = FormsToCloseFilters[formName];
                const formRecs = await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    formName,
                    { filter: filters }
                );
                const filteredRecs = await this.__filterPeriodRecords(
                    formRecs,
                    startDate,
                    endDate
                );
                await this.__processLockRecords(
                    filteredRecs,
                    formName,
                    formRecords,
                    transaction
                );
            }
            return formRecords;
        } catch (e) {
            console.error(`Unable to get period records: ${e.message}`);
            throw this.fx.wrapError(
                e,
                ErrorMessages.CLOSE_PERIOD_RECORDS_ERROR
            );
        }
    }

    /**
     * Processes and locks records for a specific form within the closing period.
     * @async
     * @function __processLockRecords
     * @param {Array} filteredRecs - The filtered records to process.
     * @param {string} formName - The name of the form being processed.
     * @param {Object} formRecords - An object to store processed records and errors.
     * @param {Object} transaction - The database transaction object.
     * @throws {Error} If unable to process or lock records.
     */
    async __processLockRecords(
        filteredRecs,
        formName,
        formRecords,
        transaction
    ) {
        console.log("Processing Locked Records");

        try {
            const lockedRecord = _.find(filteredRecs, {
                locked: "Yes",
            });
            if (lockedRecord) {
                const prettyFormName = this.shared.DSL[formName].view.label;
                const timeZone = this.ctx.user.timezone;
                const lockedDateTime = moment(lockedRecord.locked_datetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                formRecords.errors.push(
                    `Record Locked: ${prettyFormName} ID ${lockedRecord.id} by ${lockedRecord.locked_by} on ${lockedDateTime}`
                );
            } else {
                formRecords[formName] = filteredRecs;
                const updatePromises = [];
                for (const rec of filteredRecs) {
                    rec.locked = "Yes";
                    rec.locked_by = this.ctx.user.id;
                    rec.locked_datetime = moment()
                        .utc()
                        .format("MM/DD/YYYY HH:mm:ss");
                    updatePromises.push(
                        transaction.update(formName, rec, rec.id)
                    );
                }
                await Promise.all(updatePromises);
            }
        } catch (e) {
            console.error(`Unable to filter period records: ${e.message}`);
            throw this.fx.wrapError(
                e,
                ErrorMessages.CLOSE_PERIOD_RECORDS_ERROR
            );
        }
    }

    /**
     * Filters records based on the given start and end dates.
     * @async
     * @param {Array} formRecs - The array of form records to filter.
     * @param {string} startDate - The start date of the period (format: MM/DD/YYYY).
     * @param {string} endDate - The end date of the period (format: MM/DD/YYYY).
     * @returns {Promise<Array>} A filtered array of records within the specified date range.
     */
    async __filterPeriodRecords(formRecs, startDate, endDate) {
        console.log("Filtering Period Records");

        try {
            const filteredRecs = formRecs.filter((rec) => {
                const postDate = moment(
                    rec.post_datetime,
                    "mm/dd/yyyy HH:MM:ss"
                );
                return (
                    postDate.isSameOrAfter(
                        moment(startDate, "MM/DD/YYYY").startOf("day")
                    ) &&
                    postDate.isSameOrBefore(
                        moment(endDate, "MM/DD/YYYY").endOf("day")
                    )
                );
            });
            return filteredRecs;
        } catch (e) {
            console.error(`Unable to filter period records: ${e.message}`);
            throw this.fx.wrapError(
                e,
                ErrorMessages.CLOSE_PERIOD_RECORDS_ERROR
            );
        }
    }

    /**
     * Retrieves the dates for the next closing period.
     * @async
     * @returns {Promise<Object>} An object containing the start date, end date, and last period record.
     * @throws {Error} If unable to get the next closing period dates.
     */
    async __getPeriodDates() {
        console.log("Getting Next Closing Period Dates");

        try {
            const lastPeriodRec = _.head(
                await this.form.get.get_form(
                    this.ctx,
                    this.ctx.user,
                    "billing_closing",
                    { sort: "-end_date", limit: 1 }
                )
            );
            const lastDayOfPreviousMonth = moment()
                .subtract(1, "month")
                .endOf("month")
                .format("MM/DD/YYYY");
            if (!lastPeriodRec) {
                const companyRec = _.head(
                    await this.form.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        "company",
                        { sort: "-end_date", limit: 1 }
                    )
                );

                return {
                    startDate: moment(companyRec.created_on).format(
                        "MM/DD/YYYY"
                    ),
                    endDate: lastDayOfPreviousMonth,
                    lastPeriodRec,
                };
            }

            return {
                startDate: moment(lastPeriodRec.end_date)
                    .add(1, "day")
                    .format("MM/DD/YYYY"),
                endDate: lastDayOfPreviousMonth,
                lastPeriodRec,
            };
        } catch (e) {
            console.error(
                `Unable to get next closing period dates: ${e.message}`
            );
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_DATES_ERROR);
        }
    }

    /**
     * Retrieves the Accounts Receivable (AR) for a specific period.
     * @async
     * @param {string} startDate - The start date of the period in 'MM/DD/YYYY' format.
     * @param {string} endDate - The end date of the period in 'MM/DD/YYYY' format.
     * @returns {Promise<number>} The total AR amount for the specified period.
     * @throws {Error} If unable to retrieve the period AR.
     */
    async __getPeriodAR(startDate, endDate) {
        console.log("Getting Period AR");

        try {
            const sql = `
            SELECT COALESCE(SUM(flaa.amount),0.00) as amt from form_ledger_account_apply flaa
            INNER JOIN form_ledger_account fla ON fla.ledger_no = flaa.associated_ledger_no 
            INNER JOIN form_billing_account fba ON fba.id = fla.account_id AND (fba.archived = false OR fba.archived IS NULL) AND (fba.deleted = false OR fba.deleted IS NULL)
            WHERE applied_type = 'Payment' AND fba.type = 'Site' AND flaa.post_datetime >= '${startDate}' AND flaa.post_datetime <= '${endDate}'`;
            const rows = await this.db.env.rw.query(sql);
            return rows[0].amt;
        } catch (e) {
            console.error(`Unable to get period AR: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_AR_ERROR);
        }
    }

    /**
     * Retrieves the Accounts Payable (AP) for a specific period.
     * @async
     * @param {string} startDate - The start date of the period in 'MM/DD/YYYY' format.
     * @param {string} endDate - The end date of the period in 'MM/DD/YYYY' format.
     * @returns {Promise<number>} The total AP amount for the specified period.
     * @throws {Error} If unable to retrieve the period AP.
     */
    async __getPeriodAP(startDate, endDate) {
        console.log("Getting Period AP");

        try {
            const sql = `
            SELECT COALESCE(SUM(fla.amount),0.00) as amt from form_ledger_account fla
            INNER JOIN form_billing_account fba ON fba.id = fla.account_id AND (fba.archived = false OR fba.archived IS NULL) AND (fba.deleted = false OR fba.deleted IS NULL)
            WHERE account_type = 'Receivable' AND fba.type = 'Site' AND fla.post_datetime >= '${startDate}' AND fla.post_datetime <= '${endDate}'`;
            const rows = await this.db.env.rw.query(sql);
            return rows[0].amt;
        } catch (e) {
            console.error(`Unable to get period AP: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_AP_ERROR);
        }
    }

    /**
     * Retrieves the total cost for a specific period.
     * @async
     * @param {string} startDate - The start date of the period in 'MM/DD/YYYY' format.
     * @param {string} endDate - The end date of the period in 'MM/DD/YYYY' format.
     * @returns {Promise<number>} The total cost amount for the specified period.
     * @throws {Error} If unable to retrieve the period cost.
     */
    async __getPeriodCost(startDate, endDate) {
        console.log("Getting Period Cost");

        try {
            const sql = `
            SELECT COALESCE(SUM(fbi.total_cost),0.00) as amt from form_billing_invoice fbi
            WHERE (fbi.archived = false OR fbi.archived IS NULL) AND (fbi.deleted = false OR fbi.deleted IS NULL) AND fbi.post_datetime >= '${startDate}' AND fbi.post_datetime <= '${endDate}'`;
            const rows = await this.db.env.rw.query(sql);
            return rows[0].amt;
        } catch (e) {
            console.error(`Unable to get period Cost: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_COST_ERROR);
        }
    }

    /**
     * Retrieves the total credits for a specific period.
     * @async
     * @param {string} startDate - The start date of the period in 'MM/DD/YYYY' format.
     * @param {string} endDate - The end date of the period in 'MM/DD/YYYY' format.
     * @returns {Promise<number>} The total credit amount for the specified period.
     * @throws {Error} If unable to retrieve the period credits.
     */
    async __getPeriodCredits(startDate, endDate) {
        console.log("Getting Period Credits");

        try {
            const sql = `
            SELECT COALESCE(SUM(fba.amount),0.00) as amt from form_billing_adjustment fba
            INNER JOIN form_billing_invoice fbi ON fbi.invoice_no = fba.invoice_no AND (fbi.archived = false OR fbi.archived IS NULL) AND (fbi.deleted = false OR fbi.deleted IS NULL)
            WHERE COALESCE(fbi.void,'No') <> 'Yes' AND fba.type = 'Credit' AND (fba.archived = false OR fba.archived IS NULL) AND (fba.deleted = false OR fba.deleted IS NULL) AND fba.post_datetime >= '${startDate}' AND fba.post_datetime <= '${endDate}'`;
            const rows = await this.db.env.rw.query(sql);
            return rows[0].amt;
        } catch (e) {
            console.error(`Unable to get period Credits: ${e.message}`);
            throw this.fx.wrapError(
                e,
                ErrorMessages.CLOSE_PERIOD_CREDITS_ERROR
            );
        }
    }

    /**
     * Retrieves the total debits for a specific period.
     * @async
     * @param {string} startDate - The start date of the period in 'MM/DD/YYYY' format.
     * @param {string} endDate - The end date of the period in 'MM/DD/YYYY' format.
     * @returns {Promise<number>} The total debit amount for the specified period.
     * @throws {Error} If unable to retrieve the period debits.
     */
    async __getPeriodDebits(startDate, endDate) {
        console.log("Getting Period Debits");

        try {
            const sql = `
            SELECT COALESCE(SUM(fba.amount),0.00) as amt from form_billing_adjustment fba
            INNER JOIN form_billing_invoice fbi ON fbi.invoice_no = fba.invoice_no AND (fbi.archived = false OR fbi.archived IS NULL) AND (fbi.deleted = false OR fbi.deleted IS NULL)
            WHERE fbi.void IS NULL AND fba.type = 'Debit' AND (fba.archived = false OR fba.archived IS NULL) AND (fba.deleted = false OR fba.deleted IS NULL) AND fba.post_datetime >= '${startDate}' AND fba.post_datetime <= '${endDate}'`;
            const rows = await this.db.env.rw.query(sql);
            return rows[0].amt;
        } catch (e) {
            console.error(`Unable to get period Debits: ${e.message}`);
            throw this.fx.wrapError(e, ErrorMessages.CLOSE_PERIOD_DEBITS_ERROR);
        }
    }
};
