"use strict";
const moment = require("moment-timezone");

const { ActionHandler, ActionResponseWrappers } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const chargeLineRec = await this.__getChargeLineRec(ctx, id);

            if (chargeLineRec.close_no) {
                return {
                    actions: [],
                    warning: "Billing period is closed",
                };
            }
            if (chargeLineRec.void === "Yes") {
                return {
                    actions: [],
                    warning: "Charge line is voided",
                };
            }
            if (chargeLineRec.zeroed === "Yes") {
                return {
                    actions: [],
                    warning: "Charge line is zeroed out",
                };
            }

            if (chargeLineRec.locked === "Yes") {
                const timeZone = ctx.user.timezone;
                const lockedDatetime = chargeLineRec.locked_datetime || "";
                const lockedTime = moment(lockedDatetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                const warning = `Record is locked by ${chargeLineRec.locked_by_auto_name} on ${lockedTime}`;
                return {
                    actions: [],
                    warning: warning,
                };
            }

            const BillingChargeLineGetButtonsHandler = require("./charge_line/buttons");
            const getActionsHandler = new BillingChargeLineGetButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error checking charge line actions for charge line ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(
            `Processing charge line action ${action} for ${form} ID ${id}`
        );

        try {
            const chargeLineRec = await this.__getChargeLineRec(ctx, id);
            if (chargeLineRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this charge line"
                );
            }
            if (chargeLineRec.void === "Yes") {
                return ActionResponseWrappers.error("Charge line is voided");
            }
            if (chargeLineRec.zeroed === "Yes") {
                return ActionResponseWrappers.error(
                    "Charge line is zeroed out"
                );
            }
            if (chargeLineRec.locked === "Yes") {
                const timeZone = ctx.user.timezone;
                const lockedDatetime = chargeLineRec.locked_datetime || "";
                const lockedTime = moment(lockedDatetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                const warning = `Record is locked by ${chargeLineRec.locked_by_auto_name} on ${lockedTime}`;

                return ActionResponseWrappers.error(warning);
            }
            const BillingChargeLinePerformActionHandler = require("./charge_line/actions");
            const actionsHandler = new BillingChargeLinePerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing charge line action ${action} for charge line ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing charge line post action ${action} for charge line ID ${id}`
        );

        try {
            const chargeLineRec = await this.__getChargeLineRec(ctx, id);
            if (chargeLineRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this charge line"
                );
            }
            if (chargeLineRec.void === "Yes") {
                return ActionResponseWrappers.error("Charge line is voided");
            }
            if (chargeLineRec.locked === "Yes") {
                const timeZone = ctx.user.timezone;
                const lockedDatetime = chargeLineRec.locked_datetime || "";
                const lockedTime = moment(lockedDatetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                const warning = `Record is locked by ${chargeLineRec.locked_by_auto_name} on ${lockedTime}`;

                return ActionResponseWrappers.error(warning);
            }
            const BillingChargeLinePostActionsHandler = require("./charge_line/posts");
            const postActionsHandler = new BillingChargeLinePostActionsHandler(
                this.nes,
                ctx
            );
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing charge line action ${action} for charge line ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async __getChargeLineRec(ctx, chargeLineId) {
        const BillingFetcherClass = require("@billing/fetcher");
        const fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            ctx
        );
        const chargeLineRec = await fetcher.fetchCachedRecord(
            "ledger_charge_line",
            chargeLineId
        );
        return chargeLineRec;
    }
};
