"use strict";

const { <PERSON><PERSON><PERSON><PERSON>, ActionResponseWrappers } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            if (!id) {
                return {
                    actions: [],
                    warning: null,
                };
            }
            const cashRec = await this.__getCashRec(ctx, id);

            if (cashRec.close_no) {
                return {
                    actions: [],
                    warning: "Billing period is closed",
                };
            }
            if (cashRec.zeroed === "Yes") {
                return {
                    actions: [],
                    warning: "Invoice is zeroed out",
                };
            }

            const BillingCashGetButtonsHandler = require("./cash/buttons");
            const getActionsHandler = new BillingCashGetButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error checking ${form} actions for ${form} ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const cashRec = await this.__getCashRec(ctx, id);
            if (cashRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this invoice"
                );
            }
            if (cashRec.zeroed === "Yes") {
                return ActionResponseWrappers.error("Invoice is zeroed out");
            }

            const BillingCashPerformActionHandler = require("./cash/actions");
            const actionsHandler = new BillingCashPerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing ${form} action ${action} for ${form} ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(`Processing cash post action ${action} for cash ID ${id}`);

        try {
            const cashRec = await this.__getCashRec(ctx, id);
            if (cashRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this cash"
                );
            }
            if (cashRec.zeroed === "Yes") {
                return ActionResponseWrappers.error(
                    "Cash record is zeroed out"
                );
            }

            const BillingCashPostActionsHandler = require("./cash/posts");
            const postActionsHandler = new BillingCashPostActionsHandler(
                this.nes,
                ctx
            );
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing cash action ${action} for cash ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async __getCashRec(ctx, cashId) {
        const BillingFetcherClass = require("@billing/fetcher");
        const fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            ctx
        );
        const cashRec = await fetcher.fetchCachedRecord("billing_cash", cashId);
        return cashRec;
    }
};
