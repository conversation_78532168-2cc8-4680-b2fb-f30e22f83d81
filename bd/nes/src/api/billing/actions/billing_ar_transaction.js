"use strict";

const { <PERSON><PERSON><PERSON><PERSON>, ActionResponseWrappers } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            if (!id) {
                return {
                    actions: [],
                    warning: null,
                };
            }
            const arTransactionRec = await this.__getARTransactionRec(ctx, id);

            if (arTransactionRec.close_no) {
                return {
                    actions: [],
                    warning: "Billing period is closed",
                };
            }
            if (arTransactionRec.zeroed === "Yes") {
                return {
                    actions: [],
                    warning: "Invoice is zeroed out",
                };
            }

            const BillingARTransactionGetButtonsHandler = require("./ar_transaction/buttons");
            const getActionsHandler = new BillingARTransactionGetButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error checking ${form} actions for ${form} ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            if (!id) {
                return ActionResponseWrappers.error(
                    "AR Transaction ID is required"
                );
            }
            const arTransactionRec = await this.__getARTransactionRec(ctx, id);
            if (arTransactionRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this invoice"
                );
            }
            if (arTransactionRec.zeroed === "Yes") {
                return ActionResponseWrappers.error("Invoice is zeroed out");
            }

            const BillingARTransactionPerformActionHandler = require("./ar_transaction/actions");
            const actionsHandler = new BillingARTransactionPerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing ${form} action ${action} for ${form} ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing ar transaction post action ${action} for ar transaction ID ${id}`
        );

        try {
            const arTransactionRec = await this.__getARTransactionRec(ctx, id);
            if (arTransactionRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this ar transaction"
                );
            }
            if (arTransactionRec.zeroed === "Yes") {
                return ActionResponseWrappers.error(
                    "AR transaction is zeroed out"
                );
            }

            const BillingARTransactionPostActionsHandler = require("./ar_transaction/posts");
            const postActionsHandler =
                new BillingARTransactionPostActionsHandler(this.nes, ctx);
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing ar transaction action ${action} for ar transaction ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async __getARTransactionRec(ctx, arTransactionId) {
        const BillingFetcherClass = require("@billing/fetcher");
        const fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            ctx
        );
        const arTransactionRec = await fetcher.fetchCachedRecord(
            "billing_ar_transaction",
            arTransactionId
        );
        return arTransactionRec;
    }
};
