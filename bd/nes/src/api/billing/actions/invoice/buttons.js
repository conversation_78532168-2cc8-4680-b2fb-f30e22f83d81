"use strict";

const {
    InvoiceActionKeys,
    InvoiceActions,
    InvoiceStatus,
    BillingMethodType,
    NCPDPPaidClaimStatus,
    NCPDPOpenClaimStatus,
} = require("@billing/pharmacy/settings");
const {
    MedClaimStatusOpenStatus,
    MedClaimStatusBilledStatus,
} = require("@billing/medical/electronic/settings");
const {
    ButtonStyle,
    ButtonType,
    actionButtonWrapper,
    ButtonActionRequirements,
    editActionButtonWrapper,
    CallbackType,
} = require("@actions");

const { ButtonsActionsHandler } = require("../index");

module.exports = class BillingInvoiceGetButtonsHandler extends (
    ButtonsActionsHandler
) {
    constructor(nes, ctx) {
        super(nes, ctx);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.billingRules = nes.securityRules.billing;
        const BillingFetcherClass = require("@billing/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingCheckerClass = require("@billing/checker");
        this.checker = this.fx.getInstance(
            ctx,
            BillingCheckerClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Retrieves available actions for a given invoice.
     * @param {string} invoiceId - The ID of the invoice.
     * @returns {Promise<Object>} An object containing available actions and any warnings.
     */
    async getActions(id) {
        console.log(`Checking invoice actions for invoice ID ${id}`);

        try {
            if (!id) return { actions: [], warning: null };
            const invoiceRec = await this.fetcher.fetchCachedRecord(
                "billing_invoice",
                id
            );
            const invoiceNo = invoiceRec.invoice_no;

            if (!invoiceRec)
                throw this.fx.getClaraError(
                    `Unable to locate invoice with invoice ID ${id}. Please verify invoice isn't voided or archived.`
                );

            const availableActions = [];
            if (invoiceRec?.void === "Yes")
                return {
                    actions: availableActions,
                    warning: "Invoice is voided",
                };
            if (invoiceRec?.zeroed === "Yes")
                return {
                    actions: availableActions,
                    warning: "Invoice is zeroed",
                };
            const warning = null;
            if (
                [
                    BillingMethodType.CMS_1500,
                    BillingMethodType.MAJOR_MEDICAL,
                ].includes(invoiceRec.billing_method_id)
            ) {
                const cms1500Label = InvoiceActions.CMS_1500;
                const cms1500Action = InvoiceActionKeys[cms1500Label];
                const callbackCms1500Url = `/api/form/billing_invoice/${id}/action/${cms1500Action}`;
                const params = {
                    label: cms1500Label,
                    action: cms1500Action,
                    showLoadingSpinner: true,
                    loadingSpinnerText: "Generating CMS 1500...",
                    path: "/invoice/can/generate/cms1500",
                };
                availableActions.push(actionButtonWrapper(params));
                params["callback_url"] = callbackCms1500Url;
                availableActions.push(editActionButtonWrapper(params));
            }
            const isRevenueAccepted = invoiceRec.revenue_accepted === "Yes";
            const revenueIsBooked =
                invoiceRec.revenue_accepted_posted === "Yes";
            const periodIsClosed = invoiceRec.close_no !== null;

            if (invoiceRec?.close_no)
                return {
                    actions: availableActions,
                    warning: warning || "Invoice period is closed",
                };

            const hasCobInvoice =
                await this.fetcher.fetchCOBChildInvoice(invoiceNo);
            const cobInvoiceClosedOrAccepted =
                hasCobInvoice?.status === InvoiceStatus.CONFIRMED ||
                hasCobInvoice?.revenue_accepted_posted === "Yes";

            const hasPostedPayments =
                await this.fetcher.invoiceHasPostedPayments(invoiceRec);

            if (isRevenueAccepted) {
                if (
                    !revenueIsBooked &&
                    !cobInvoiceClosedOrAccepted &&
                    !hasPostedPayments
                ) {
                    const reopenLabel = InvoiceActions.REOPEN;
                    const reopenAction = InvoiceActionKeys[reopenLabel];
                    let params = {
                        label: reopenLabel,
                        action: reopenAction,
                        style: ButtonStyle.WARNING,
                        type: ButtonType.VIEW,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Reopening Invoice...",
                        path: "/invoice/can/reopen",
                    };
                    availableActions.push(actionButtonWrapper(params));
                    const callbackReopenUrl = `/api/form/billing_invoice/${id}?perform_action=${reopenAction}`;
                    params = {
                        ...params,
                        callback_url: callbackReopenUrl,
                        showLoadingSpinner: true,
                    };
                    availableActions.push(editActionButtonWrapper(params));
                }

                const cobEligible =
                    await this.checker.checkIfInvoiceCOBEligible(invoiceRec);
                if (cobEligible) {
                    const cobLabel = InvoiceActions.GENERATE_COB;
                    const cobAction = InvoiceActionKeys[cobLabel];
                    const callbackCobUrl = `/api/form/billing_invoice/${id}?perform_action=${cobAction}`;
                    const params = {
                        label: cobLabel,
                        action: cobAction,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Generating COB Claim Options...",
                        path: "/invoice/can/generate/cob",
                    };
                    availableActions.push(actionButtonWrapper(params));
                    params["callback_url"] = callbackCobUrl;
                    availableActions.push(editActionButtonWrapper(params));
                }
            }

            if (invoiceRec.billing_method_id === BillingMethodType.NCPDP) {
                const claimRec =
                    await this.fetcher.fetchInvoiceElectronicClaimRecord(
                        invoiceRec
                    );

                await this.__checkNCPDPActions(
                    invoiceRec,
                    claimRec,
                    availableActions,
                    revenueIsBooked
                );
            } else if (
                invoiceRec.billing_method_id === BillingMethodType.MAJOR_MEDICAL
            ) {
                const claimRec =
                    await this.fetcher.fetchInvoiceElectronicClaimRecord(
                        invoiceRec
                    );
                await this.__checkElectronicMedClaimActions(
                    invoiceRec,
                    claimRec,
                    availableActions,
                    revenueIsBooked
                );
            } else if (!periodIsClosed && !revenueIsBooked && !hasCobInvoice) {
                const voidLabel = InvoiceActions.VOID;
                const voidAction = InvoiceActionKeys[voidLabel];
                const params = {
                    label: voidLabel,
                    action: voidAction,
                    style: ButtonStyle.CANCEL,
                    path: "/invoice/can/void",
                };
                availableActions.push(actionButtonWrapper(params));
                params["callback_url"] = null;
                params["style"] = ButtonStyle.CANCEL;
                params["requirements"] = ButtonActionRequirements.NONE;
                availableActions.push(editActionButtonWrapper(params));
            } else if (!periodIsClosed && !hasCobInvoice) {
                const zeroInvoiceLabel = InvoiceActions.ZERO_INVOICE;
                const zeroInvoiceAction = InvoiceActionKeys[zeroInvoiceLabel];
                const params = {
                    label: zeroInvoiceLabel,
                    action: zeroInvoiceAction,
                    style: ButtonStyle.CANCEL,
                    path: "/invoice/can/generate/zero",
                };
                availableActions.push(actionButtonWrapper(params));
                params["callback_url"] = null;
                params["style"] = ButtonStyle.CANCEL;
                params["requirements"] = ButtonActionRequirements.NONE;
                availableActions.push(editActionButtonWrapper(params));
            }

            if (isRevenueAccepted) {
                return {
                    actions: availableActions,
                    warning: warning,
                };
            }

            const canAcceptInvoice = invoiceRec.status === InvoiceStatus.OPEN;
            if (canAcceptInvoice) {
                const acceptLabel = InvoiceActions.ACCEPT;
                const acceptAction = InvoiceActionKeys[acceptLabel];
                const callbackAcceptUrl = `/api/form/billing_invoice/${id}?perform_action=${acceptAction}`;
                const params = {
                    label: acceptLabel,
                    action: acceptAction,
                    style: ButtonStyle.ACCEPT,
                    showLoadingSpinner: true,
                    loadingSpinnerText: "Accepting Revenue...",
                    path: "/invoice/can/accept",
                };
                availableActions.push(actionButtonWrapper(params));
                params["callback_url"] = callbackAcceptUrl;
                availableActions.push(editActionButtonWrapper(params));
            }

            const canAcceptAndPost =
                invoiceRec.delivery_ticket_id && !invoiceRec.revenue_accepted;
            if (canAcceptAndPost) {
                const acceptAndPostLabel = InvoiceActions.ACCEPT_REVENUE;
                const acceptAndPostAction =
                    InvoiceActionKeys[acceptAndPostLabel];
                const callbackAcceptUrl = `/api/form/billing_invoice/${id}?perform_action=${acceptAndPostAction}`;
                const params = {
                    label: acceptAndPostLabel,
                    action: acceptAndPostAction,
                    style: ButtonStyle.ACCEPT,
                    showLoadingSpinner: true,
                    loadingSpinnerText: "Accepting Revenue...",
                    path: "/invoice/can/accept/revenue",
                };
                availableActions.push(actionButtonWrapper(params));
                params["callback_url"] = callbackAcceptUrl;
                availableActions.push(editActionButtonWrapper(params));
            }

            return {
                actions: availableActions,
                warning: warning,
            };
        } catch (e) {
            const errorMessage = `Error checking invoice actions for invoice ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks and adds available actions for NCPDP claims.
     * @param {Object} invoiceRec - The invoice record.
     * @param {Object} ncpdpRec - The NCPDP claim record.
     * @param {Array} availableActions - The list of available actions to be updated.
     * @param {boolean} revenueIsBooked - Whether the revenue is booked.
     * @returns {Promise<void>}
     */
    async __checkNCPDPActions(
        invoiceRec,
        ncpdpRec,
        availableActions,
        revenueIsBooked
    ) {
        console.log("Checking NCPDP actions");
        try {
            const isPaidClaim = ncpdpRec
                ? NCPDPPaidClaimStatus.includes(ncpdpRec?.status)
                : false;
            const reverseLabel = InvoiceActions.REVERSE;
            const reverseAction = InvoiceActionKeys[reverseLabel];
            const reverseCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${reverseAction}`;

            availableActions.push(
                editActionButtonWrapper({
                    label: InvoiceActions.NCPDP_NARRATIVE,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_NARRATIVE],
                    callback_url: `/api/form/billing_invoice/${invoiceRec.id}?perform_action=${InvoiceActionKeys[InvoiceActions.NCPDP_NARRATIVE]}`,
                    callback_type: CallbackType.GET,
                    style: ButtonStyle.INFO,
                    requirements: ButtonActionRequirements.NONE,
                    path: "/invoice/can/edit/ncpdp_narrative",
                })
            );

            availableActions.push(
                actionButtonWrapper({
                    label: InvoiceActions.NCPDP_NARRATIVE_READ,
                    action: InvoiceActionKeys[
                        InvoiceActions.NCPDP_NARRATIVE_READ
                    ],
                    style: ButtonStyle.INFO,
                    type: ButtonType.VIEW,
                    path: "/invoice/can/edit/ncpdp_narrative",
                })
            );

            availableActions.push(
                editActionButtonWrapper({
                    label: InvoiceActions.NCPDP_DX,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_DX],
                    callback_url: `/api/form/billing_invoice/${invoiceRec.id}?perform_action=${InvoiceActionKeys[InvoiceActions.NCPDP_DX]}`,
                    callback_type: CallbackType.GET,
                    style: ButtonStyle.INFO,
                    requirements: ButtonActionRequirements.NONE,
                    path: "/invoice/ncpdp/can/edit/ncpdp_dx",
                })
            );

            availableActions.push(
                actionButtonWrapper({
                    label: InvoiceActions.NCPDP_DX_READ,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_DX_READ],
                    style: ButtonStyle.INFO,
                    type: ButtonType.VIEW,
                    path: "/invoice/ncpdp/can/edit/ncpdp_dx",
                })
            );

            availableActions.push(
                editActionButtonWrapper({
                    label: InvoiceActions.NCPDP_DUR,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_DUR],
                    callback_url: `/api/form/billing_invoice/${invoiceRec.id}?perform_action=${InvoiceActionKeys[InvoiceActions.NCPDP_DUR]}`,
                    callback_type: CallbackType.GET,
                    style: ButtonStyle.INFO,
                    requirements: ButtonActionRequirements.NONE,
                    path: "/invoice/ncpdp/can/edit/ncpdp_dur",
                })
            );

            availableActions.push(
                actionButtonWrapper({
                    label: InvoiceActions.NCPDP_DUR_READ,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_DUR_READ],
                    style: ButtonStyle.INFO,
                    type: ButtonType.VIEW,
                    path: "/invoice/ncpdp/can/edit/ncpdp_dur",
                })
            );

            availableActions.push(
                editActionButtonWrapper({
                    label: InvoiceActions.NCPDP_COUPON,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_COUPON],
                    callback_url: `/api/form/billing_invoice/${invoiceRec.id}?perform_action=${InvoiceActionKeys[InvoiceActions.NCPDP_COUPON]}`,
                    callback_type: CallbackType.GET,
                    style: ButtonStyle.INFO,
                    requirements: ButtonActionRequirements.NONE,
                    path: "/invoice/ncpdp/can/edit/ncpdp_coupon",
                })
            );

            availableActions.push(
                actionButtonWrapper({
                    label: InvoiceActions.NCPDP_COUPON_READ,
                    action: InvoiceActionKeys[InvoiceActions.NCPDP_COUPON_READ],
                    style: ButtonStyle.INFO,
                    type: ButtonType.VIEW,
                    path: "/invoice/ncpdp/can/edit/ncpdp_coupon",
                })
            );
            availableActions.push(
                actionButtonWrapper({
                    label: InvoiceActions.REVERSE,
                    action: InvoiceActionKeys[InvoiceActions.REVERSE],
                    style: ButtonStyle.WARNING,
                    type: ButtonType.VIEW,
                    showLoadingSpinner: true,
                    loadingSpinnerText: "Reversing Claim...",
                    path: "/invoice/can/reverse",
                })
            );
            availableActions.push(
                editActionButtonWrapper({
                    label: InvoiceActions.REVERSE,
                    action: InvoiceActionKeys[InvoiceActions.REVERSE],
                    callback_url: reverseCallbackUrl,
                    callback_type: CallbackType.POST,
                    style: ButtonStyle.CANCEL,
                    requirements: ButtonActionRequirements.ALL_REQUIRED,
                    showLoadingSpinner: true,
                    loadingSpinnerText: "Reversing Claim...",
                    path: "/invoice/can/reverse",
                })
            );

            if (isPaidClaim) {
                const rebillLabel = InvoiceActions.REBILL;
                const rebillAction = InvoiceActionKeys[rebillLabel];
                availableActions.push(
                    actionButtonWrapper({
                        label: rebillLabel,
                        action: rebillAction,
                        style: ButtonStyle.ACCEPT,
                        type: ButtonType.VIEW,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/rebill",
                    })
                );
                const rebillCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${rebillAction}`;
                availableActions.push(
                    editActionButtonWrapper({
                        label: rebillLabel,
                        action: rebillAction,
                        callback_url: rebillCallbackUrl,
                        callback_type: CallbackType.POST,
                        style: ButtonStyle.ACCEPT,
                        requirements: ButtonActionRequirements.ALL_REQUIRED,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/rebill",
                    })
                );
            }

            const claimIsOpen =
                !ncpdpRec.status ||
                NCPDPOpenClaimStatus.includes(ncpdpRec.status);
            if (claimIsOpen) {
                if (revenueIsBooked) {
                    const zeroInvoiceLabel = InvoiceActions.ZERO_INVOICE;
                    const zeroInvoiceAction =
                        InvoiceActionKeys[zeroInvoiceLabel];
                    availableActions.push(
                        actionButtonWrapper({
                            label: zeroInvoiceLabel,
                            action: zeroInvoiceAction,
                            style: ButtonStyle.CANCEL,
                            path: "/invoice/can/generate/zero",
                        })
                    );
                    availableActions.push(
                        editActionButtonWrapper({
                            label: zeroInvoiceLabel,
                            action: zeroInvoiceAction,
                            callback_url: null,
                            callback_type: CallbackType.GET,
                            style: ButtonStyle.CANCEL,
                            requirements: ButtonActionRequirements.NONE,
                            path: "/invoice/can/generate/zero",
                        })
                    );
                } else {
                    const voidLabel = InvoiceActions.VOID;
                    const voidAction = InvoiceActionKeys[voidLabel];
                    availableActions.push(
                        actionButtonWrapper({
                            label: voidLabel,
                            action: voidAction,
                            style: ButtonStyle.CANCEL,
                            path: "/invoice/can/void",
                        })
                    );
                    availableActions.push(
                        editActionButtonWrapper({
                            label: voidLabel,
                            action: voidAction,
                            callback_url: null,
                            callback_type: CallbackType.GET,
                            style: ButtonStyle.CANCEL,
                            requirements: ButtonActionRequirements.NONE,
                            path: "/invoice/can/void",
                        })
                    );
                }

                const submitLabel = InvoiceActions.SUBMIT;
                const submitAction = InvoiceActionKeys[submitLabel];
                availableActions.push(
                    actionButtonWrapper({
                        label: submitLabel,
                        action: submitAction,
                        style: ButtonStyle.ACCEPT,
                        type: ButtonType.VIEW,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/submit",
                    })
                );
                const saveSubmitLabel = InvoiceActions.SAVE_SUBMIT;
                const saveSubmitAction = InvoiceActionKeys[saveSubmitLabel];
                const saveSubmitCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${saveSubmitAction}`;
                availableActions.push(
                    editActionButtonWrapper({
                        label: saveSubmitLabel,
                        action: saveSubmitAction,
                        callback_url: saveSubmitCallbackUrl,
                        callback_type: CallbackType.POST,
                        style: ButtonStyle.ACCEPT,
                        requirements: ButtonActionRequirements.ALL_REQUIRED,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/submit",
                    })
                );
            }
        } catch (e) {
            const errorMessage = `Error checking NCPDP claim actions for claim no ${ncpdpRec.claim_no}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Checks and adds available actions for electronic medical claims.
     * @param {Object} invoiceRec - The invoice record.
     * @param {Object} medClaimRec - The electronic medical claim record.
     * @param {Array} availableActions - The list of available actions to be updated.
     * @param {boolean} revenueIsBooked - Whether the revenue is booked.
     * @returns {Promise<void>}
     */
    async __checkElectronicMedClaimActions(
        invoiceRec,
        medClaimRec,
        availableActions,
        revenueIsBooked
    ) {
        console.log("Checking Electronic Medical Claim actions");
        try {
            const claimStatusIsOpen = medClaimRec
                ? MedClaimStatusOpenStatus.includes(medClaimRec?.status)
                : false;
            const claimStatusIsBilled = medClaimRec
                ? MedClaimStatusBilledStatus.includes(medClaimRec?.status)
                : false;

            const claimInformation = medClaimRec?.claim_information[0];
            const claimSupplementalInformation =
                claimInformation?.claim_supplemental_information[0];
            const claimControlNumber =
                claimSupplementalInformation?.claim_control_number || null;

            if (claimStatusIsOpen) {
                const submitLabel = InvoiceActions.SUBMIT;
                const submitAction = InvoiceActionKeys[submitLabel];
                availableActions.push(
                    actionButtonWrapper({
                        label: submitLabel,
                        action: submitAction,
                        path: "/invoice/can/submit",
                    })
                );
                const submitCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${submitAction}`;
                availableActions.push(
                    editActionButtonWrapper({
                        label: submitLabel,
                        action: submitAction,
                        callback_url: submitCallbackUrl,
                        callback_type: CallbackType.POST,
                        style: ButtonStyle.ACCEPT,
                        requirements: ButtonActionRequirements.ALL_REQUIRED,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/submit",
                    })
                );
                const validateLabel = InvoiceActions.VALIDATE;
                const validateAction = InvoiceActionKeys[validateLabel];
                availableActions.push(
                    actionButtonWrapper({
                        label: validateLabel,
                        action: validateAction,
                        style: ButtonStyle.ACCEPT,
                        type: ButtonType.VIEW,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Validating Claim...",
                        path: "/invoice/med/can/validate",
                    })
                );
                const validateCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${validateAction}`;
                availableActions.push(
                    editActionButtonWrapper({
                        label: validateLabel,
                        action: validateAction,
                        callback_url: validateCallbackUrl,
                        callback_type: CallbackType.POST,
                        style: ButtonStyle.ACCEPT,
                        requirements: ButtonActionRequirements.ALL_REQUIRED,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Validating Claim...",
                        path: "/invoice/med/can/validate",
                    })
                );

                if (revenueIsBooked) {
                    const zeroInvoiceLabel = InvoiceActions.ZERO_INVOICE;
                    const zeroInvoiceAction =
                        InvoiceActionKeys[zeroInvoiceLabel];
                    availableActions.push(
                        actionButtonWrapper({
                            label: zeroInvoiceLabel,
                            action: zeroInvoiceAction,
                            style: ButtonStyle.CANCEL,
                            path: "/invoice/can/generate/zero",
                        })
                    );
                    availableActions.push(
                        editActionButtonWrapper({
                            label: zeroInvoiceLabel,
                            action: zeroInvoiceAction,
                            callback_url: null,
                            callback_type: CallbackType.GET,
                            style: ButtonStyle.CANCEL,
                            requirements: ButtonActionRequirements.NONE,
                            path: "/invoice/can/generate/zero",
                        })
                    );
                } else {
                    const voidLabel = InvoiceActions.VOID;
                    const voidAction = InvoiceActionKeys[voidLabel];
                    availableActions.push(
                        actionButtonWrapper({
                            label: voidLabel,
                            action: voidAction,
                            style: ButtonStyle.CANCEL,
                            path: "/invoice/can/void",
                        })
                    );
                    availableActions.push(
                        editActionButtonWrapper({
                            label: voidLabel,
                            action: voidAction,
                            callback_url: null,
                            callback_type: CallbackType.GET,
                            style: ButtonStyle.CANCEL,
                            requirements: ButtonActionRequirements.NONE,
                            path: "/invoice/can/void",
                        })
                    );
                }
            } else if (claimStatusIsBilled && claimControlNumber) {
                const rebillLabel = InvoiceActions.REBILL;
                const rebillAction = InvoiceActionKeys[rebillLabel];
                availableActions.push(
                    actionButtonWrapper({
                        label: rebillLabel,
                        action: rebillAction,
                        style: ButtonStyle.ACCEPT,
                        type: ButtonType.VIEW,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/rebill",
                    })
                );
                const rebillCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${rebillAction}`;
                availableActions.push(
                    editActionButtonWrapper({
                        label: rebillLabel,
                        action: rebillAction,
                        callback_url: rebillCallbackUrl,
                        callback_type: CallbackType.POST,
                        style: ButtonStyle.ACCEPT,
                        requirements: ButtonActionRequirements.ALL_REQUIRED,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Submitting Claim...",
                        path: "/invoice/can/rebill",
                    })
                );
                const reverseLabel = InvoiceActions.REVERSE;
                const reverseAction = InvoiceActionKeys[reverseLabel];
                availableActions.push(
                    actionButtonWrapper({
                        label: reverseLabel,
                        action: reverseAction,
                        path: "/invoice/can/reverse",
                    })
                );
                const reverseCallbackUrl = `/api/form/billing_invoice/${invoiceRec.id}/action/${reverseAction}`;
                availableActions.push(
                    editActionButtonWrapper({
                        label: reverseLabel,
                        action: reverseAction,
                        callback_url: reverseCallbackUrl,
                        callback_type: CallbackType.POST,
                        style: ButtonStyle.CANCEL,
                        requirements: ButtonActionRequirements.ALL_REQUIRED,
                        showLoadingSpinner: true,
                        loadingSpinnerText: "Reversing Claim...",
                        path: "/invoice/med/can/reverse",
                    })
                );
            }
        } catch (e) {
            const errorMessage = `Error checking electronic medical claim actions for claim no ${medClaimRec.claim_no}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
