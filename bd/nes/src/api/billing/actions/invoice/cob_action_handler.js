/*jshint : 6 */
"use strict";
const currency = require("currency.js");
module.exports = class COBActionHandler {
    constructor(nes, ctx) {
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.nes = nes;
        this.ctx = ctx;
        const BillingFetcherClass = require("@billing/fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
        const BillingCheckerClass = require("@billing/checker");
        this.checker = this.fx.getInstance(
            ctx,
            BillingCheckerClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Gets the Coordination of Benefits (COB) invoice selection data for a given invoice.
     * @async
     * @param {Object} invoiceRec - The invoice record to get COB data for.
     * @returns {Promise<Object>} An object containing COB selection data including insurance IDs and error messages if applicable.
     * @throws {Error} If there's an error retrieving the COB selection data.
     */
    async getCOBInvoiceSelectionData(invoiceRec) {
        console.debug(
            `Generating COB data for invoice no ${invoiceRec.invoice_no}`
        );
        try {
            const masterInvoiceNo = invoiceRec.master_invoice_no || null;
            const filters = [
                `master_invoice_no:${masterInvoiceNo}`,
                `void:!Yes`,
                `zeroed:!Yes`,
            ];
            const allRelatedInvoiceRecs = masterInvoiceNo
                ? await this.form.get.get_form(
                      this.ctx,
                      this.ctx.user,
                      "billing_invoice",
                      { filter: filters }
                  )
                : [];

            const prevPayerId = invoiceRec.payer_id;
            const allBillingPayerIds = allRelatedInvoiceRecs.map(
                (invoice) => invoice.payer_id
            );
            const allBilledInsuranceIds = allRelatedInvoiceRecs.map(
                (invoice) => invoice.insurance_id
            );
            allBillingPayerIds.push(prevPayerId);

            const allInsuranceRecs = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "patient_insurance",
                {
                    filter: [
                        `patient_id:${invoiceRec.patient_id}`,
                        `active:Yes`,
                        `bill_for_denial:!Yes`,
                    ],
                }
            );
            const filteredInsuranceRecs = allInsuranceRecs.filter(
                (insuranceRec) =>
                    !allBillingPayerIds.includes(insuranceRec.payer_id)
            );
            if (filteredInsuranceRecs.length === 0) {
                return {
                    reason: "no_active_insurance",
                    error: `No active insurance records available for COB generation for patient. All active insurances have been billed.`,
                };
            }

            const filteredPayerIds = filteredInsuranceRecs.map(
                (insuranceRec) => insuranceRec.payer_id
            );
            const nextInsuranceId = await this.__findNextInsuranceId(
                invoiceRec,
                allBilledInsuranceIds
            );
            const invoiceAmounts =
                await this.fetcher.fetchInvoiceAmounts(invoiceRec);
            const totalBalanceDue = currency(
                invoiceAmounts?.total_balance_due || 0.0
            ).value;
            const totalPaid = currency(invoiceAmounts?.total_paid || 0.0).value;

            const patientCopay = currency(
                invoiceAmounts?.total_pt_pay || 0.0
            ).value;

            if (totalPaid >= totalBalanceDue && patientCopay <= 0) {
                return {
                    reason: "no_remaining_balance",
                    error: `No remaining balance exists for COB generation.`,
                };
            }
            const isBillForDenial =
                invoiceRec.bill_for_denial === "Yes" &&
                parseFloat(invoiceRec.total_paid || 0.0) <= 0;
            const prefillNextInsuranceId = isBillForDenial
                ? invoiceRec.next_insurance_id || nextInsuranceId
                : nextInsuranceId;
            return {
                patient_id: invoiceRec.patient_id,
                payer_filter: filteredPayerIds,
                current_insurance_id: invoiceRec.insurance_id,
                next_insurance_id: prefillNextInsuranceId,
                total_pt_pay: patientCopay,
                total_balance_due: totalBalanceDue,
            };
        } catch (e) {
            const errorMessage = `Error encountered while generating COB data for invoice no ${invoiceRec.invoice_no}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    /**
     * Finds the next insurance ID for a given patient and prescription IDs.
     * @param {Object} invoiceRec - The invoice record.
     * @param {Array} allBilledInsuranceIds - The list of all insurance records.
     * @returns {Promise<string>} The next insurance ID.
     */
    async __findNextInsuranceId(invoiceRec, allBilledInsuranceIds) {
        try {
            // Attempt to find next insurance id through the order ranking
            const rxIds = invoiceRec?.rx_id || [];
            const prevInsuranceId = invoiceRec.insurance_id;
            const nextInsuranceIds = [];
            let nextInsuranceId = null;
            for (const rxId of rxIds) {
                const nextInsuranceId = await this.fetcher.fetchNextInsuranceId(
                    rxId,
                    prevInsuranceId
                );
                if (nextInsuranceId) {
                    nextInsuranceIds.push(nextInsuranceId);
                }
            }

            const filteredNextInsuranceIds = nextInsuranceIds.filter(
                (insuranceRec) =>
                    insuranceRec.id !== prevInsuranceId &&
                    !allBilledInsuranceIds.includes(insuranceRec.id)
            );
            if (filteredNextInsuranceIds.length > 0) {
                nextInsuranceId = filteredNextInsuranceIds[0];
            }

            return nextInsuranceId;
        } catch (e) {
            const errorMessage = `Error encountered while finding next insurance id for invoice no ${invoiceRec.invoice_no}.`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }
};
