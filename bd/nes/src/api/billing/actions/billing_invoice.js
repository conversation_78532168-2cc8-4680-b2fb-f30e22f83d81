"use strict";
const moment = require("moment-timezone");

const { ActionHandler, ActionResponseWrappers } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const invoiceRec = await this.__getInvoiceRec(ctx, id);

            if (invoiceRec.close_no) {
                return {
                    actions: [],
                    warning: "Billing period is closed",
                };
            }
            if (invoiceRec.void === "Yes") {
                return {
                    actions: [],
                    warning: "Invoice is voided",
                };
            }
            if (invoiceRec.zeroed === "Yes") {
                return {
                    actions: [],
                    warning: "Invoice is zeroed out",
                };
            }

            if (invoiceRec.locked === "Yes") {
                const timeZone = ctx.user.timezone;
                const lockedDatetime = invoiceRec.locked_datetime || "";
                const lockedTime = moment(lockedDatetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                const warning = `Record is locked by ${invoiceRec.locked_by_auto_name} on ${lockedTime}`;
                return {
                    actions: [],
                    warning: warning,
                };
            }

            const BillingInvoiceGetButtonsHandler = require("./invoice/buttons");
            const getActionsHandler = new BillingInvoiceGetButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error checking invoice actions for invoice ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing invoice action ${action} for ${form} ID ${id}`);

        try {
            const invoiceRec = await this.__getInvoiceRec(ctx, id);
            if (invoiceRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this invoice"
                );
            }
            if (invoiceRec.void === "Yes") {
                return ActionResponseWrappers.error("Invoice is voided");
            }
            if (invoiceRec.zeroed === "Yes") {
                return ActionResponseWrappers.error("Invoice is zeroed out");
            }
            if (invoiceRec.locked === "Yes") {
                const timeZone = ctx.user.timezone;
                const lockedDatetime = invoiceRec.locked_datetime || "";
                const lockedTime = moment(lockedDatetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                const warning = `Record is locked by ${invoiceRec.locked_by_auto_name} on ${lockedTime}`;

                return ActionResponseWrappers.error(warning);
            }
            const BillingInvoicePerformActionHandler = require("./invoice/actions");
            const actionsHandler = new BillingInvoicePerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing invoice action ${action} for invoice ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing invoice post action ${action} for invoice ID ${id}`
        );

        try {
            const invoiceRec = await this.__getInvoiceRec(ctx, id);
            if (invoiceRec.close_no) {
                return ActionResponseWrappers.error(
                    "Billing period is closed for this invoice"
                );
            }
            if (invoiceRec.void === "Yes") {
                return ActionResponseWrappers.error("Invoice is voided");
            }
            if (invoiceRec.locked === "Yes") {
                const timeZone = ctx.user.timezone;
                const lockedDatetime = invoiceRec.locked_datetime || "";
                const lockedTime = moment(lockedDatetime)
                    .tz(timeZone)
                    .format("MM/DD/YYYY HH:mm:ss");
                const warning = `Record is locked by ${invoiceRec.locked_by_auto_name} on ${lockedTime}`;

                return ActionResponseWrappers.error(warning);
            }
            const BillingInvoicePostActionsHandler = require("./invoice/posts");
            const postActionsHandler = new BillingInvoicePostActionsHandler(
                this.nes,
                ctx
            );
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing invoice action ${action} for invoice ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async __getInvoiceRec(ctx, invoiceId) {
        const BillingFetcherClass = require("@billing/fetcher");
        const fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            ctx
        );
        const invoiceRec = await fetcher.fetchCachedRecord(
            "billing_invoice",
            invoiceId
        );
        return invoiceRec;
    }
};
