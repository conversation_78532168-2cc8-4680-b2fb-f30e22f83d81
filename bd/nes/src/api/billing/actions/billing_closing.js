"use strict";

const { <PERSON><PERSON><PERSON><PERSON>, ActionResponseWrappers } = require("@actions");

module.exports = class ActionHandlerClass extends ActionHandler {
    constructor(nes) {
        super(nes);
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
    }

    async getActions(ctx, form, id) {
        console.log(`Checking ${form} actions for ${form} ID ${id}`);

        try {
            const closingRec = await this.__getClosingRec(ctx, id);

            if (closingRec.void === "Yes") {
                return {
                    actions: [],
                    warning: "Closing record is voided",
                };
            }

            const BillingClosingGetButtonsHandler = require("./closing/buttons");
            const getActionsHandler = new BillingClosingGetButtonsHandler(
                this.nes,
                ctx
            );
            return await getActionsHandler.getActions(id);
        } catch (e) {
            const errorMessage = `Error checking closing actions for closing record ID ${id}`;
            console.error(
                `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async runAction(ctx, form, id, action) {
        console.log(`Processing ${form} action ${action} for ${form} ID ${id}`);

        try {
            const closingRec = await this.__getClosingRec(ctx, id);
            if (closingRec.void === "Yes") {
                return ActionResponseWrappers.error("Closing record is voided");
            }
            const BillingClosingPerformActionHandler = require("./closing/actions");
            const actionsHandler = new BillingClosingPerformActionHandler(
                this.nes,
                ctx
            );
            return await actionsHandler.runAction(id, action);
        } catch (e) {
            const errorMessage = `Error processing closing action ${action} for closing record ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async postAction(ctx, form, id, action, params) {
        console.log(
            `Processing ${form} post action ${action} for ${form} ID ${id}`
        );

        try {
            const closingRec = await this.__getClosingRec(ctx, id);
            if (closingRec.void === "Yes") {
                return ActionResponseWrappers.error("Closing record is voided");
            }
            const BillingClosingPostActionsHandler = require("./closing/posts");
            const postActionsHandler = new BillingClosingPostActionsHandler(
                this.nes,
                ctx
            );
            return await postActionsHandler.postAction(id, action, params);
        } catch (e) {
            const errorMessage = `Error processing closing action ${action} for closing ID ${id}`;
            console.error(
                errorMessage + `Error: ${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(e, errorMessage);
        }
    }

    async __getClosingRec(ctx, closingId) {
        const BillingFetcherClass = require("@billing/fetcher");
        const fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            ctx
        );
        const closingRec = await fetcher.fetchCachedRecord(
            "billing_closing",
            closingId
        );
        return closingRec;
    }
};
