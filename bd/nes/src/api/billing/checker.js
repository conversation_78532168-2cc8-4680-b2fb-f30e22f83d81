/*jshint : 6 */
"use strict";

const moment = require("moment-timezone");
const Joi = require("joi").extend(require("@joi/date"));

const { ErrorMessages } = require("./errors");
const { RecordSchemas } = require("./schemas");
const { PriorAuthStatus, BillingMethodType } = require("./settings");
const { NCPDPCOBEligibleClaimStatus } = require("./pharmacy/settings");
const { MedClaimCOBEligibleStatus } = require("./medical/electronic/settings");
const { MedClaimPaperCOBEligibleStatus } = require("./medical/1500/settings");

/**
 * @class
 * @classdesc Class responsible for checking various billing-related conditions and constraints.
 */
module.exports = class BillingCheckerClass {
    constructor(nes, ctx) {
        this.nes = nes;
        this.ctx = ctx;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        const BillingFetcherClass = require("./fetcher");
        this.fetcher = this.fx.getInstance(
            ctx,
            BillingFetcherClass,
            true,
            this.nes,
            this.ctx
        );
    }

    /**
     * Checks the status of the insurance to ensure it is active and the dates are valid.
     * @param {Object} insuranceRec - The insurance object containing effective and termination dates.
     * @param {String} serviceDate - The date when the service is provided.
     * @throws {Error} If insurance is not active or dates are invalid.
     */
    async checkInsuranceStatus(insuranceRec, serviceDate) {
        console.debug("Checking Insurance Status");

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "insurance",
                    RecordSchemas.insurance.required(),
                    insuranceRec,
                    "Missing insurance record to check status."
                ),
                this.fx.validateSchema(
                    "serviceDate",
                    Joi.date().format("MM/DD/YYYY").required(),
                    serviceDate,
                    "Missing service date to check insurance status."
                ),
            ]);

            const svcDate = moment(serviceDate);
            if (insuranceRec.active !== "Yes") {
                throw this.fx.getClaraError(
                    `Insurance record ${insuranceRec?.auto_name} is not active but is on the claim!`
                );
            }
            const effDate = insuranceRec.effective_date
                ? moment(insuranceRec.effective_date)
                : moment(svcDate).subtract(1, "day");
            if (effDate.isAfter(svcDate)) {
                throw this.fx.getClaraError(
                    `Insurance record ${insuranceRec?.auto_name} has an effective date (${effDate.format("MM/DD/YYYY")}) after the service date (${svcDate.format("MM/DD/YYYY")})!`
                );
            }
            const termDate = insuranceRec.termination_date
                ? moment(insuranceRec.termination_date)
                : moment(svcDate).add(1, "day");
            if (termDate.isBefore(svcDate)) {
                throw this.fx.getClaraError(
                    `Insurance record ${insuranceRec?.auto_name} has a termination date before the service date (${svcDate.format("MM/DD/YYYY")})!`
                );
            }
        } catch (e) {
            this.fx.wrapError(
                e,
                `Unable to validate insurance record ${insuranceRec?.auto_name}`
            );
        }
    }

    /**
     * Checks if the site supports Medicare claims based on the insurance type.
     * @param {Object} insuranceRec - The insurance object containing type information.
     * @param {Object} siteRec - The site object containing Medicare claim submission settings.
     */
    async checkMedicare(insuranceRec, siteRec) {
        console.debug(`Checking Site Medicare Support`);
        const type = insuranceRec?.type_id || null;
        if (
            ["MCRD", "MCRB"].includes(type) &&
            siteRec.submit_medicare_claim != "Yes"
        ) {
            throw this.fx.getClaraError(
                `Site ${siteRec.name} is not setup for Medicare claims but the claim is Medicare.`
            );
        }
    }

    /**
     * Checks the status of prior authorization.
     * @param {Object} priorAuthRec - The prior authorization object to check.
     */
    async checkPriorAuth(priorAuthRec) {
        console.debug(`Checking Prior Auth`);
        try {
            await this.fx.validateSchema(
                "priorAuth",
                Joi.object({
                    status: Joi.string().required(),
                }).required(),
                priorAuthRec,
                "Missing prior authorization record to check status."
            );

            if (priorAuthRec.status !== PriorAuthStatus.APPROVED) {
                throw this.fx.getClaraError(ErrorMessages.PA_NOT_ACTIVE);
            }
        } catch (e) {
            this.fx.wrapError(
                e,
                `Unable to validate prior authorization record ${priorAuthRec?.auto_name}`
            );
        }
    }

    /**
     * Checks if the invoice is eligible for Coordination of Benefits (COB).
     *
     * @param {Object} invoiceRec - The invoice record containing the invoice details.
     * @param {boolean} ignoreRevenueAccepted - Whether to ignore the revenue accepted status.
     * @returns {boolean} - Returns true if the invoice is COB eligible, otherwise false.
     * @throws {Error} - Throws an error if there is an issue checking the COB eligibility.
     */
    async checkIfInvoiceCOBEligible(invoiceRec) {
        console.debug(
            `Checking Invoice ${invoiceRec.invoice_no} for COB eligibility`
        );
        try {
            await this.fx.validateSchema(
                "invoice",
                RecordSchemas.invoice.required(),
                invoiceRec,
                "Missing invoice record to check for COB eligibility."
            );

            const revenueIsAccepted = invoiceRec.revenue_accepted === "Yes";
            if (!revenueIsAccepted) {
                console.debug(
                    `Revenue must be accepted from previous payer before COB can be generated`
                );
                return false;
            }

            const invoiceIsOnHold = invoiceRec.on_hold === "Yes";
            if (invoiceIsOnHold) {
                console.debug(
                    `Invoice is on hold, cannot generate COB invoice. Invoice must be taken off hold before generating COB.`
                );
                return false;
            }

            const invoiceIsClosed = invoiceRec.close_no !== null;
            if (invoiceIsClosed) {
                console.debug(
                    `Invoice is period closed, cannot generate COB invoice.`
                );
                return false;
            }
            const invoiceIsVoided = invoiceRec.void === "Yes";
            if (invoiceIsVoided) {
                console.debug(
                    `Invoice is voided, cannot generate COB invoice.`
                );
                return false;
            }

            const remainingBalance = invoiceRec.total_balance_due || 0.0;
            const patientCopay = invoiceRec.total_pt_pay || 0.0;
            if (remainingBalance <= 0 && patientCopay <= 0) {
                console.debug(
                    `Invoice ${invoiceRec.invoice_no} has no remaining balance, cannot be COB eligible`
                );
                return false;
            }

            const filters = [
                `parent_invoice_no:${invoiceRec.invoice_no}`,
                `void:!Yes`,
                `zeroed:!Yes`,
            ];
            const childInvoices = await this.form.get.get_form(
                this.ctx,
                this.ctx.user,
                "billing_invoice",
                { filter: filters }
            );

            if (
                [
                    BillingMethodType.NCPDP,
                    BillingMethodType.CMS1500,
                    BillingMethodType.MAJOR_MEDICAL,
                ].includes(invoiceRec.billing_method_id)
            ) {
                const claimRec =
                    await this.fetcher.fetchInvoiceClaim(invoiceRec);
                const cobEligibleStatuses = [
                    ...NCPDPCOBEligibleClaimStatus,
                    ...MedClaimCOBEligibleStatus,
                    ...MedClaimPaperCOBEligibleStatus,
                ];

                if (!cobEligibleStatuses.includes(claimRec.status)) {
                    return false;
                }
            }

            const hasChildInvoices = childInvoices.length > 0;
            if (hasChildInvoices) {
                console.debug(
                    `Invoice ${invoiceRec.invoice_no} has ${childInvoices.length} child invoices, cannot be COB eligible`
                );
                return false;
            }

            return true;
        } catch (e) {
            const error =
                "Exception encountered while checking COB eligibility.";
            console.error(`${error} Error:${e.message} Stack:${e.stack}`);
            throw this.fx.wrapError(e, error);
        }
    }

    /**
     * Checks if the prescription expiration date is valid against the provided service date.
     * @param {Object} careplanPrescriptionRec - The specific care plan prescription.
     * @param {String} serviceDate - The date when the service is provided.
     * @throws {Error} If the order is missing an expiration date or if the expiration date is before the service date.
     */
    async checkOrderItemExpiration(careplanPrescriptionRec, serviceDate) {
        if (!careplanPrescriptionRec) return;
        await Promise.all([
            this.fx.validateSchema(
                "careplanPrescriptionRec",
                RecordSchemas.prescription.required(),
                careplanPrescriptionRec,
                "Missing care plan prescription record to check expiration date."
            ),
            this.fx.validateSchema(
                "serviceDate",
                Joi.date().format("MM/DD/YYYY").required(),
                serviceDate,
                "Missing service date to check prescription expiration."
            ),
        ]);

        console.debug("Checking prescription Expiration against Service Date");
        const expDate = careplanPrescriptionRec.expiration_date
            ? moment(careplanPrescriptionRec.expiration_date)
            : moment(careplanPrescriptionRec.written_date).add(365, "days");
        const svcDate = moment(serviceDate);

        if (expDate.isBefore(svcDate)) {
            throw this.fx.getClaraError(
                `Order expiration date (${expDate.format("MM/DD/YYYY")}) is before service date (${svcDate.format("MM/DD/YYYY")})`
            );
        }
    }
};
