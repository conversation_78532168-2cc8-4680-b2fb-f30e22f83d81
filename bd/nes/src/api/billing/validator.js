"use strict";
const Joi = require("joi");

const { ClaimType } = require("./settings");

/**
 * @class
 * @classdesc Class responsible for validating different types of claims.
 */
module.exports = class ClaimValidatorClass {
    constructor(nes, ctx) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.fx = nes.modules.fx;
        this.nes = nes;
        this.ctx = ctx;
        const NCPDPValidatorClass = require("@billing/pharmacy/validator");
        this.ncpdpValidator = new NCPDPValidatorClass(this.nes, this.ctx);
        const MedicalElectronicClaimsValidatorClass = require("@billing/medical/electronic/validator");
        this.medicalClaimsValidator = new MedicalElectronicClaimsValidatorClass(
            this.nes,
            this.ctx
        );
    }

    /**
     * Validates a claim based on its type and presence.
     * @param {Object} claimRec - The claim object to validate.
     * @param {ClaimType} type - The type of the claim.
     * @returns {Object|boolean} The validation results or false if validation fails.
     * @throws {Error} If the claim object is missing or the claim type is invalid.
     */
    async validateClaim(claimRec, type, bypassValidation = false) {
        console.log(`Validating ${type} claim...`);

        try {
            await Promise.all([
                this.fx.validateSchema(
                    "claimRec",
                    Joi.object().required(),
                    claimRec,
                    "Missing claim record to validate."
                ),
                this.fx.validateSchema(
                    "type",
                    Joi.string()
                        .valid(...Object.values(ClaimType))
                        .required(),
                    type,
                    "Missing claim type to validate."
                ),
            ]);
            const results = await this.__processClaimByType(
                claimRec,
                type,
                bypassValidation
            );
            return results;
        } catch (e) {
            return this.__handleValidationError(e);
        }
    }

    /**
     * Processes the claim based on its type.
     * @param {Object} claimRec - The claim object to validate.
     * @param {ClaimType} type - The type of the claim.
     * @returns {Object|boolean} The validation results or false if validation fails.
     * @throws {Error} If the claim type is not supported.
     * @private
     */
    async __processClaimByType(claimRec, type, bypassValidation = false) {
        console.log(`Processing ${type} claim...`);
        switch (type) {
            case ClaimType.NCPDP:
                return this.__validateNCPDPClaim(claimRec, bypassValidation);
            case ClaimType.MEDICAL:
                return this.__validateMedicalClaim(claimRec);
            case ClaimType.CMS1500:
            case ClaimType.GENERIC:
                throw this.fx.getClaraError("Claim type not supported!");
            default:
                throw this.fx.getClaraError(`Unexpected claim type: ${type}`);
        }
    }

    /**
     * Validates an NCPDP claim.
     * @param {Object} claimRec - The claim object to validate.
     * @returns {Object} The transformed data if validation succeeds.
     * @private
     */
    async __validateNCPDPClaim(claimRec, bypassValidation = false) {
        console.log("Validating NCPDP claim...");
        const {
            transformedData = null,
            validationErrors = null,
            rawErrorMessages = null,
        } = await this.ncpdpValidator.validateClaim(claimRec, bypassValidation);
        if (validationErrors) return { validationErrors, rawErrorMessages };
        return transformedData;
    }

    /**
     * Validates an Medical claim.
     * @param {Object} claimRec - The claim object to validate.
     * @returns {Object} The transformed data if validation succeeds.
     * @private
     */
    async __validateMedicalClaim(claimRec) {
        console.log("Validating Medical claim...");
        const {
            transformedData = null,
            validationErrors = null,
            rawErrorMessages = null,
        } = await this.medicalClaimsValidator.validateClaim(claimRec);
        if (validationErrors) return { validationErrors, rawErrorMessages };
        return transformedData;
    }

    /**
     * Handles validation errors.
     * @param {Object} ctx - The context in which the claim is being validated.
     * @param {Error} error - The error that occurred during validation.
     * @returns {boolean} Always returns false to indicate validation failure.
     * @private
     */
    __handleValidationError(error) {
        const errorMessage = `Claim validation error`;
        console.error(
            `${errorMessage}: ${error.message} Stack: ${error.stack}`
        );
        return { error: this.fx.wrapError(error, errorMessage).message };
    }
};
