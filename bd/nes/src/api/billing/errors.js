"use strict";
const { ErrorMessages: dispenseErrorMessages } = require("@dispense/errors");

const ErrorMessages = {
    ...dispenseErrorMessages,
    PA_NOT_ACTIVE: "Associated Prior Auth is not active.",
    MISSING_SNOMED: "Missing SNOMED code linked to route:",
    INVALID_ROUTE: "prescription has invalid Route. Cannot continue.",
    SALES_TAX_ERROR: "Error encountered while calculating sales tax",
    ITEM_PRICING_ERROR: "Error calculating item level price",
    TOTAL_TAX_ERROR:
        "Error calculating total tax amount for top level prescription",
    INVENTORY_PRICE_ERROR: `Error fetching pricing for inventory item:`,
    INVOICE_FETCH_ERROR: `Error encountered while fetching invoices after save. Please contact support for further assistance.`,
    INVOICE_BILL_LINES_FETCH_ERROR: `Error encountered while fetching bill lines for invoice. Please contact support for further assistance.`,
    TEST_CLAIM_ERROR: `Error encountered while building test claim. Please contact support if issues persist.`,
    ELIGIBILITY_GENERATION_ERROR: `Error encountered while generating eligibility. Please contact support if issues persist.`,
    UNSUPPORTED_CLAIM_TYPE: `Unsupported claim type. Only Drug claims have available actions`,
    MISSING_DT_DATA: `Missing delivery ticket data`,
    MISSING_DT: `Missing delivery ticket`,
    MISSING_DT_ID: `Missing delivery ticket ID`,
    MISSING_DT_ITEM: `Missing delivery ticket item`,
    MISSING_RX_ID: `Missing Prescription Record ID`,
    RX_NOT_NCPDP: `Prescription is not NCPDP. Cannot generate claim.`,
    BILL_LINE_GENERATION_EXCEPTION: `Exception encountered while building bill lines for invoice. Please contact support if issue persist.`,
    MISSING_UNLOCK_DATA: "Missing data for unlock action.",
    MISSING_REFRESH_AND_LOCK_DATA: "Missing data for refresh and lock action.",
    MISSING_CLAIM_DATA: "Missing data for claim action.",
    MISSING_INVOICE_DATA: "Missing data for invoice action.",
    MISSING_POST_DATE: "Missing post date.",
    MISSING_SPLIT_INVOICE_NO: "Missing split invoice number.",
    MISSING_SPLIT_INVOICE_VIEW_DATA: "Missing invoice creation view data.",
    INVOICE_NOT_FOUND: "Invoice record not found.",
    INVOICE_CHARGE_ERROR:
        "Error encountered while fetching invoice charge lines. Please contact support if issues persist.",
    INVALID_POST_DATE:
        "User does not have permission to post on this date because period is already closed.",
    BILL_LINE_VALIDATION_EXCEPTION:
        "Exception encountered while validating bill line. Please contact support if issue persists.",
    INGREDIENT_PROCESSING_EXCEPTION:
        "Exception encountered while processing ingredient. Please contact support if issue persists.",
    INGREDIENT_MISSING_INVENTORY_ID: "Missing inventory ID for ingredient.",
    SPLIT_INGREDIENTS_PROCESSING_EXCEPTION:
        "Exception encountered while processing split ingredients. Please contact support if issue persists.",
    CLOSE_PERIOD_AR_ERROR:
        "Exception encountered while getting close period AR. Please contact support if issue persists.",
    CLOSE_PERIOD_AP_ERROR:
        "Exception encountered while getting close period AP. Please contact support if issue persists.",
    CLOSE_PERIOD_CREDITS_ERROR:
        "Exception encountered while getting close period credits. Please contact support if issue persists.",
    CLOSE_PERIOD_DEBITS_ERROR:
        "Exception encountered while getting close period debits. Please contact support if issue persists.",
    CLOSE_PERIOD_COST_ERROR:
        "Exception encountered while getting close period cost. Please contact support if issue persists.",
    CLOSE_PERIOD_DATES_ERROR:
        "Exception encountered while getting close period dates. Please contact support if issue persists.",
    CLOSE_PERIOD_RECORDS_ERROR:
        "Exception encountered while getting close period records. Please contact support if issue persists.",
    CLOSE_PERIOD_ERROR:
        "Exception encountered while closing period. Please contact support if issue persists.",
    CLOSE_PERIOD_LOCK_ERROR:
        "Cannot close period. The following records are locked:",
    CLOSE_PERIOD_LOCK_COMMIT_ERROR:
        "Cannot close period. Error encountered while locking records. Please contact support if issue persists.",
    CLOSE_PERIOD_NO_RECORDS:
        "Cannot close period. No records found for the period.",
    CLOSE_PERIOD_UNLOCK_COMMIT_ERROR:
        "Cannot unlock period. Error encountered while unlocking records. Please contact support if issue persists.",
    CLOSE_PERIOD_LAST_PERIOD_DATA_ERROR:
        "Cannot close period. Error encountered while adding last period data. Please contact support if issue persists.",
    CLOSE_PERIOD_MISSING_ASSOC_RECORDS:
        "Cannot unlock period. Missing associated records.",
    UNLOCK_CLOSING_SAVE_ERROR:
        "Error encountered while unlocking closed period. Please contact support if issues persist.",
    LOCK_REFRESH_CLOSING_SAVE_ERROR:
        "Error encountered while locking and refreshing period. Please contact support if issues persist.",
    MISSING_ENCOUNTER_IDS: "Missing encounter IDs.",
    MISSING_ORDER_ID: "Missing order ID.",
    CHARGE_LINE_BUILD_EXCEPTION:
        "Exception encountered while building charge lines. Please contact support if issue persists.",
    MISSING_DELIVERY_TICKET_ID: "Missing delivery ticket ID.",
    NURSING_BILLABLE_ERROR:
        "Exception encountered while fetching nursing billable.",
    CHARGE_LINE_ENC_BUILD_EXCEPTION:
        "Exception encountered while building charge lines for encounter. Please contact support if issue persists.",
    CLAIM_RUNNER_FETCH_EXCEPTION:
        "Exception encountered while fetching claim runner. Please contact support if issue persists.",
    CLAIM_REVERSAL_ERROR:
        "Unable to reverse claim. Please contact support if issue persists.",
    INVOICE_CLAIM_FORM_ERROR:
        "Unable to determine claim form for invoice. Please contact support if issue persists.",
    CLAIM_SUBMISSION_ERROR:
        "Unable to submit claim. Please contact support if issue persists.",
    CLAIM_REJECTED: `Claim Rejected, please view form validation errors for more details.`,
    CLAIM_VALIDATION_ERROR: `An error occurred while validating the claim. Please correct errors and try again.`,
    CLAIM_SAVE_ERROR: `An error occurred while saving the claim record. Please contact support if issues persist.`,
    CLAIM_SAVE_ID_ERROR: `Missing claim ID after saving. Please contact support if issues persist.`,
    DELIVERY_TICKET_CHARGE_ERROR: `Error encountered while fetching delivery ticket charges. Please contact support if issues persist.`,
    REPORT_QUANTITY_ERROR: `Unable to calculate report quantity.`,
    WRITEOFF_NOT_FOUND: `Writeoff record not found.`,
    NO_INSURANCE_OR_PAYER_COVERS_RENTAL: `No insurance found that covers rental.`,
    ERROR_GENERATING_INVOICE_GROUP: `Error encountered while generating invoice group.`,
    ERROR_GENERATING_DT_PHARM_CLAIMS: `Error encountered while generating pharmacy claims for delivery ticket.`,
};

module.exports = {
    ErrorMessages,
};
