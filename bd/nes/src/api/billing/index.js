"use strict";

const DispenseFetcherClass = require("@dispense/fetcher");
const BillingGeneratorClass = require("@billing/generator");
const { ErrorMessages } = require("./errors");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.fx = nes.modules.fx;
    }

    /**
     * Generates a Rx claim for a given Rx ID
     * @async
     * @param {Object} ctx - The context object containing request and response information
     * @param {string} rxId - The ID of the Rx to generate a claim for
     * @returns {Promise<void>} - A promise that resolves when the Rx claim is generated and set in the context
     * @throws {Error} If there is an error generating the Rx claim
     */
    async __generateRxClaim(ctx, rxId) {
        console.log(`Generating Rx Claim`);
        try {
            if (!rxId) {
                ctx.status = 500;
                ctx.body = {
                    error: ErrorMessages.MISSING_RX_ID,
                };
                return;
            }

            const fetcher = new DispenseFetcherClass(this.nes, ctx);
            const rxRec = await fetcher.fetchCachedRecord(
                "careplan_order_rx",
                rxId
            );
            const generator = new BillingGeneratorClass(this.nes, ctx);
            const res = await generator.generateRxSprxClaim(rxRec);
            ctx.status = 200;
            ctx.body = res;
        } catch (e) {
            this.__returnError(ctx, e, "Error generating claim");
        }
    }

    /**
     * Runs a test claim for a prescription
     * @async
     * @param {Object} ctx - The context object containing request and response information
     * @param {string} rxId - The ID of the prescription to run the test claim for
     * @returns {Promise<void>} A promise that resolves when the test claim is complete
     * @throws {Error} If there is an error running the test claim
     */
    async __runTestClaim(ctx, rxId) {
        console.log(`Running Test Claim for Rx ID ${rxId}`);
        try {
            if (!rxId) {
                ctx.status = 500;
                ctx.body = {
                    error: ErrorMessages.MISSING_RX_ID,
                };
                return;
            }
            const generator = new BillingGeneratorClass(this.nes, ctx);
            const fetcher = new DispenseFetcherClass(this.nes, ctx);
            const rxRec = await fetcher.fetchCachedRecord(
                "careplan_order_rx",
                rxId
            );
            const res = await generator.generateRxTestClaimView(rxRec);
            ctx.status = 200;
            ctx.body = res;
        } catch (e) {
            this.__returnError(
                ctx,
                e,
                `Error encountered while running test claim`
            );
        }
    }

    /**
     * Closes the current billing period.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<void>} A promise that resolves when the period closing is complete.
     */
    async __closePeriod(ctx) {
        console.log(`Closing Period`);

        try {
            const BillingCloseClass = require("./close");
            const closer = new BillingCloseClass(this.nes, ctx);
            const res = await closer.buildClosePeriodRecord();
            ctx.body = res;
            ctx.status = 200;
            return;
        } catch (e) {
            this.__returnError(ctx, e, `Error encounter closing period`);
        }
    }

    /**
     * Unlocks the records for a billing period.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<void>} A promise that resolves when the period records are unlocked.
     */
    async __unlockPeriodRecords(ctx) {
        console.log(`Unlocking Period`);

        try {
            const data = ctx.request.body;

            if (!data) {
                ctx.status = 500;
                ctx.body = {
                    error: ErrorMessages.CLOSE_PERIOD_MISSING_ASSOC_RECORDS,
                };
                return;
            }

            const BillingCloseClass = require("./close");
            const transaction = this.db.env.rw.transaction(this.ctx);

            const closer = new BillingCloseClass(this.nes, ctx);
            await closer.unlockPeriodClosingRecords(transaction, data);
            const res = await transaction.commit();
            if (res.error) {
                console.error(
                    `Error encountered unlocking records: ${res.error}`
                );
                ctx.body = {
                    error: ErrorMessages.CLOSE_PERIOD_UNLOCK_COMMIT_ERROR,
                };
                ctx.status = 500;
                return;
            }
            transaction.init();
            ctx.body = { success: "Period Successfully Unlocked" };
            ctx.status = 200;
            return;
        } catch (e) {
            this.__returnError(
                ctx,
                e,
                `Error encounter unlocking period records`
            );
        }
    }

    /**
     * Creates a view for generating an invoice from split charges that are ready to bill
     * @async
     * @param {Object} ctx - The context object containing request and response information
     * @param {string} splitInvoiceNo - The split invoice number to create the view for
     * @returns {Promise<void>} A promise that resolves when the view creation is complete
     * @throws {Error} If there is an error creating the ready to bill invoice view
     */
    async __createReadyToBillInvoiceView(ctx, splitInvoiceNo) {
        console.log("Creating split invoice creation view");
        try {
            if (!splitInvoiceNo) {
                ctx.status = 500;
                ctx.body = { error: ErrorMessages.MISSING_SPLIT_INVOICE_NO };
                return;
            }
            const generator = new BillingGeneratorClass(this.nes, ctx);
            const res =
                await generator.generateSplitChargeCreateInvoiceView(
                    splitInvoiceNo
                );
            ctx.status = 200;
            ctx.body = res;
        } catch (e) {
            this.__returnError(
                ctx,
                e,
                "Error encountered creating ready to bill invoice view"
            );
        }
    }

    /**
     * Creates a split invoice from a split invoice view.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @param {Object} splitInvoiceView - The split invoice view to create the invoice from.
     * @returns {Promise<void>} A promise that resolves when the split invoice is created.
     */
    async __createSplitInvoiceFromView(ctx, splitInvoiceView) {
        console.log(`Creating split invoice from view`);
        try {
            const generator = new BillingGeneratorClass(this.nes, ctx);
            const res =
                await generator.generateInvoiceFromInvoiceCreationView(
                    splitInvoiceView
                );
            ctx.status = 200;
            ctx.body = res;
        } catch (e) {
            this.__returnError(
                ctx,
                e,
                "Error encountered creating split invoice from view"
            );
        }
    }

    /**
     * Checks the split invoice view data from the request body.
     * @async
     * @param {Object} ctx - The context object containing request and response information.
     * @returns {Promise<Object|boolean>} - A promise that resolves to an object containing the splitInvoiceView, or false if validation fails.
     */
    async __checkSplitInvoiceViewData(ctx) {
        console.log(`Checking Split Invoice View Data`);

        const splitInvoiceView = ctx.request.body?.values || null;
        if (!splitInvoiceView) {
            ctx.status = 500;
            ctx.body = {
                error: ErrorMessages.MISSING_SPLIT_INVOICE_VIEW_DATA,
            };
            return false;
        }

        return { splitInvoiceView };
    }

    __returnError(ctx, e, errorMessage) {
        console.error(
            `${errorMessage} Error: ${e.message} Stack: ${e.stack} Meta Data: ${this.fx.funcParamsToString(arguments)}`
        );
        const humanMessage = e?.humanMessage || errorMessage;
        this.fx.setReturnErrorMessage(ctx, e, humanMessage);
    }

    async process(ctx, urlpath) {
        if (urlpath.path.length < 2 || urlpath.path[1] == "") {
            ctx.status = 500;
            ctx.body = {
                error: "Invalid form path specified: " + urlpath.url.path,
            };
            return;
        }

        if (this.fx.checkParameters({ func: "Function ID" }, ctx) == false)
            return;

        const params = Object.assign({}, ctx.query);
        const func = params.func;
        switch (func) {
            case "generate_create_invoice_view":
                return await this.__createReadyToBillInvoiceView(
                    ctx,
                    params?.calc_invoice_split_no
                );
            case "generate_charge_lines_invoice":
                await this.__checkSplitInvoiceViewData(ctx)
                    .then(async ({ splitInvoiceView }) => {
                        await this.__createSplitInvoiceFromView(
                            ctx,
                            splitInvoiceView
                        );
                    })
                    .catch((e) => {
                        this.__returnError(
                            ctx,
                            e,
                            "Error encounter checking split invoice view data"
                        );
                    });
                break;
            case "close_period":
                await this.__closePeriod(ctx);
                break;
            case "unlock_period_records":
                await this.__unlockPeriodRecords(ctx);
                break;
            case "run_test_claim":
                return await this.__runTestClaim(ctx, params?.rx_id);
            case "generate_rx_claim":
                return await this.__generateRxClaim(ctx, params?.rx_id);
            default:
                ctx.status = 500;
                ctx.body = { error: "Invalid function name: " + func };
                return;
        }
    }
};
