"use strict";

module.exports = class CanAccessPath {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
    }
    async process(ctx, urlpath) {
        try {
            const { url } = ctx.request.query;
            const { user } = ctx.session;
            if (user) {
                const resp = this.auth.can_access_path(ctx, user, url);
                ctx.body = resp;
            } else {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            }

            return;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
