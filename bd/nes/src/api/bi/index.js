"use strict";
const _ = require("lodash");

module.exports = class SupersetEmbed {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
    }
    async process(ctx) {
        try {
            ctx.body = await this.fetchBIDashboard(ctx);
            return ctx.body;
        } catch (e) {
            ctx.status = 500;
            ctx.body = {
                message: e.message || "Server error occurred",
            };
            console.error(e);
        }
    }

    async fetchBIDashboard(ctx) {
        const { slug } = ctx.query;
        if (!slug) {
            throw new Error("Slug is required");
        }

        const filters = [`slug:${slug}`];
        const dashboardData = _.head(
            await this.form.get.get_form(ctx, ctx.user, "bi_dashboard", {
                filter: filters,
            })
        );

        if (!dashboardData) {
            throw new Error("Dashboard not found");
        }

        const responseData = {
            supersetUrl: this.shared.SUPERSET_URL,
            dashboardId: dashboardData.dashboard_id,
        };
        return responseData;
    }
};
