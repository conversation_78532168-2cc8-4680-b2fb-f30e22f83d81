"use strict";
const jwt = require("jsonwebtoken");

module.exports = class SupersetEmbed {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
    }
    async process(ctx) {
        try {
            ctx.body = await this.getGuestToken(ctx);
            return ctx.body;
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }

    async getGuestToken(ctx) {
        const { dashboardId } = ctx.query;
        if (!dashboardId) {
            throw {
                message: "Dashboard ID not found to authenticate",
            };
        }
        const payload = {
            user: {
                username: this.shared.config.env["SS_EMBED_USER"] || "embed",
                first_name: "embed",
                last_name: "embed",
            },
            resources: [
                {
                    type: "dashboard",
                    id: dashboardId,
                },
            ],
            rls_rules: [],
            aud:
                this.shared.config.env["SS_GUEST_TOKEN_JWT_AUDIENCE"] || // should match the audience in the env vars for bi fly app
                "elabs",
            type: "guest",
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 60 * 60, // Token expires in 1 hour
        };

        // Generate the token using the secret
        const token = jwt.sign(
            payload,
            this.shared.config.env["SS_GUEST_TOKEN_JWT_SECRET"] // should match the secret in the env vars for bi fly app
        );
        return token;
    }
};
