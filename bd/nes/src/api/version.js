"use strict";

module.exports = class ApiView {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
    }

    activeFrontendFlags(obj) {
        return Object.entries(obj)
            .filter(
                ([_, value]) => value.status === true && value.frontend === true
            )
            .reduce((acc, [key, value]) => {
                acc[key] = value;
                return acc;
            }, {});
    }

    feature() {
        const feature = {
            wss: false,
            monitor: true,
        };

        if (
            "nes" in this.shared.config &&
            "feature" in this.shared.config.nes
        ) {
            for (const [k, v] of Object.entries(this.shared.config.nes.feature))
                feature[k] = v;
            if (!feature.wss && feature.monitor)
                // need wss for monitoring
                feature.monitor = false;
        }
        return feature;
    }

    parseArray(array, key, value) {
        const obj = {};
        for (let i = 0; i < array?.length; i++) {
            const e = array[i];
            if (value == "whole_obj") {
                obj[e[key]] = e;
            } else {
                obj[e[key]] = e[value];
            }
        }
        return obj;
    }

    paymentProcessors() {
        const pp = {};
        if (
            this.shared.config.env["AUTHNET_API_ID"] &&
            this.shared.config.env["AUTHNET_API_TN"]
        ) {
            pp.authnet_id = this.shared.config.env["AUTHNET_API_ID"];
            pp.authnet_tn = this.shared.config.env["AUTHNET_API_TN"];
        }
        if (this.shared.config.env["BTREE_API_TOKEN"]) {
            pp.btree = this.shared.config.env["BTREE_API_TOKEN"];
        }
        if (this.shared.config.env["STRIPE_API_ID"]) {
            pp.stripe = this.shared.config.env["STRIPE_API_ID"];
        }
        return pp;
    }

    async process(ctx, urlpath) {
        try {
            const pkg = require("@root/package.json");
            if ("version" in pkg) {
                const ft = this.feature();
                ctx.body = {
                    version: pkg.version,
                    wss: ft.wss,
                    monitor: ft.monitor,
                };

                ctx.body.is_prod = false;
                ctx.body = {
                    ...ctx.body,
                    environment: this.shared.config.env["NODE_ENV"],
                    appname: this.shared.config.admin_console.appname,
                    applink: parseInt(this.shared.config.db.env.port),
                    flags: this.activeFrontendFlags(
                        this.parseArray(
                            this.shared.config.admin_console.feature_flags?.filter(
                                (i) => i
                            ),
                            "name",
                            "whole_obj"
                        )
                    ),
                    payment: this.paymentProcessors(),
                };
                if (ctx.body.environment == "production") {
                    ctx.body.is_prod = true;
                }
            } else {
                ctx.status = 500;
                ctx.body = { error: "Unknown version" };
            }
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
