"use strict";
const _ = require("lodash");

module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.form = nes.shared.form;
    }

    check_parameters(user, path, params) {
        if (!(user.id && !isNaN(parseInt(user.id))))
            return { msg: "Invalid User Id", code: 400 };

        if (
            path.length < 6 ||
            !path.includes("report") ||
            !path.includes("query")
        ) {
            return { msg: "Invalid/Incomplete URL", code: 400 };
        }
        const reportIndex = path.indexOf("report") + 1;
        if (!path[reportIndex]) {
            return { msg: "Missing report id", code: 400 };
        }
        if (isNaN(parseInt(path[reportIndex]))) {
            return { msg: "Invalid report id, must integer.", code: 400 };
        }
        const queryIndex = path.indexOf("query") + 1;
        if (queryIndex > path.length || !path[queryIndex]) {
            return { msg: "Missing query code", code: 400 };
        }
        return false;
    }

    async run_query(ctx, user, reportId, code, params) {
        const paramValues = this.fx.getPGParamValues(params);
        const formdata = _.head(
            await this.form.get.get_form(ctx, user, "report", {
                limit: 1,
                filter: "id:" + reportId,
            })
        );

        if (!formdata) {
            return { error: "Invalid Report ID" };
        }
        const queries = formdata.query || {};
        if (!queries[code]) {
            return { error: "Invalid Query Code" };
        }
        let sql = queries[code].sql;
        if (!sql || !sql.trim()) {
            return { error: "Invalid Query" };
        }
        sql = `
            WITH q AS (
                ${sql.trim().replace(/;$/, "")}
            ) SELECT * FROM q
        `;

        const resp = await this.db.env.rw.query(
            this.fx.sanitizeReadQuery(sql),
            paramValues
        );
        if (resp) {
            return resp;
        }
    }

    async process(ctx, urlpath) {
        try {
            const user = ctx.user;
            const params = Object.assign({}, ctx.query);
            const parts = ctx.url.split("?");
            const queryPart = parts[0];
            const pathParts = queryPart.split("/");
            const err = this.check_parameters(user, pathParts, params);
            if (err) {
                ctx.status = err.code;
                ctx.body = { error: err.msg };
                return;
            }
            const reportId = pathParts[pathParts.indexOf("report") + 1];
            const code = pathParts[pathParts.indexOf("query") + 1];
            const qr = await this.run_query(ctx, user, reportId, code, params);
            if (qr.error) {
                ctx.status = 400;
                ctx.body = qr;
                return;
            }
            ctx.body = qr;
            ctx.status = 200;
        } catch (e) {
            console.error(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
