"use strict";
module.exports = class ApiView {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.shared = nes.shared;
    }
    async process(ctx) {
        const request = ctx.request;
        const fieldName = request.query.field_name;
        const formName = request.query.form_name;
        const formId = request.query.form_id;
        if (
            !fieldName ||
            typeof fieldName != "string" ||
            !formName ||
            typeof formName != "string" ||
            !formId ||
            typeof formId != "string"
        ) {
            const e = "Invalid parameter values";
            console.error(e);
            ctx.status = 500;
            ctx.body = {
                message: e,
            };
            return;
        }
        if (request.method == "GET") {
            try {
                function getLowestAfterDate(arr, key, dateStr) {
                    const targetDate = new Date(dateStr);

                    const filteredArr = arr.filter(
                        (item) => new Date(item[key]) > targetDate
                    );

                    if (filteredArr.length === 0) {
                        return null;
                    }

                    return filteredArr.reduce((lowest, item) => {
                        return new Date(item[key]) < new Date(lowest[key])
                            ? item
                            : lowest;
                    })[key];
                }
                function getBiggestUpperAfterDate(arr, key, dateStr) {
                    const targetDate = new Date(dateStr);
                    const filteredArr = arr.filter((item) => {
                        const itemDate = new Date(item[key]);
                        const pInfinity = new Date("3000-01-01T00:00:00.000Z");
                        if (
                            itemDate.valueOf() > targetDate &&
                            itemDate.valueOf() != pInfinity.valueOf()
                        ) {
                            return true;
                        } else return false;
                    });
                    if (filteredArr.length === 0) {
                        return [];
                    }
                    return filteredArr;
                }
                function getSmallestArr(arr, key, smallestValue) {
                    return arr.filter(
                        (item) =>
                            new Date(item[key]).getTime() ===
                            new Date(smallestValue).getTime()
                    );
                }
                let sql = "";
                let params = [];
                const source =
                    this.shared.DSL[formName].fields[fieldName]?.model?.source;
                const multi =
                    this.shared.DSL[formName].fields[fieldName]?.model?.multi;
                const sourceid =
                    this.shared.DSL[formName].fields[fieldName]?.model
                        ?.sourceid ?? "id";
                if (source && !multi && typeof source == "string") {
                    sql = `SELECT %s,
                                    lfupdated_on,
                                    lffirstname,
                                    lflastname,
                                    lfdisplayname
                            FROM   (SELECT
                                        (SELECT fc.auto_name from form_%s fc where fc.%s = lf.%s) as %s,
                                        lf.%s AS %s_int,
                                        Lower(lf.sys_period)                                     AS lfupdated_on,
                                        COALESCE(fu_updated.firstname, fu_created.firstname)     AS lffirstname,
                                        COALESCE(fu_updated.lastname, fu_created.lastname)       AS lflastname,
                                        CASE 
                                            WHEN COALESCE(fu_updated.lastname, fu_created.lastname) IS NULL AND COALESCE(fu_updated.firstname, fu_created.firstname) IS NULL THEN ''
                                            WHEN COALESCE(fu_updated.lastname, fu_created.lastname) IS NULL THEN COALESCE(fu_updated.firstname, fu_created.firstname)
                                            WHEN COALESCE(fu_updated.firstname, fu_created.firstname) IS NULL THEN COALESCE(fu_updated.lastname, fu_created.lastname)
                                            ELSE CONCAT_WS(', ', COALESCE(fu_updated.lastname, fu_created.lastname), COALESCE(fu_updated.firstname, fu_created.firstname))
                                        END AS lfdisplayname,
                                        Lag(lf.%s) OVER (ORDER BY lf.sys_period) AS prev_%s
                                    FROM log_%s AS lf
                                        LEFT JOIN form_user AS fu_updated ON lf.updated_by = fu_updated.id
                                        LEFT JOIN form_user AS fu_created ON lf.created_by = fu_created.id
                                    WHERE  lf.id = %s AND lf.%s IS NOT NULL) query_set
                            WHERE  %s_int != prev_%s
                              OR prev_%s IS NULL;`;
                    params = [
                        fieldName,
                        source,
                        sourceid,
                        fieldName,
                        fieldName,
                        fieldName,
                        fieldName,
                        fieldName,
                        fieldName,
                        formName,
                        formId,
                        fieldName,
                        fieldName,
                        fieldName,
                        fieldName,
                    ];
                } else if (source && multi && typeof source == "string") {
                    sql = `
                        SELECT
                            query_set.%s,
                            lfupdated_on,
                            lfupdated_on_upper,
                            lffirstname,
                            lflastname,
                            foreign_id,
                            lfdisplayname
                        FROM   (SELECT
                                flt.auto_name                                            AS %s,
                                lgrfptlt.id                                              AS category_id_int,
                                lgrfptlt.form_%s_fk                                      AS foreign_id,
                                Lower(lgrfptlt.sys_period)                               AS lfupdated_on,
                                Upper(lgrfptlt.sys_period)                               AS lfupdated_on_upper,
                                COALESCE(fu_updated.firstname, fu_created.firstname)     AS lffirstname,
                                COALESCE(fu_updated.lastname, fu_created.lastname)       AS lflastname,
                                CASE 
                                    WHEN COALESCE(fu_updated.lastname, fu_created.lastname) IS NULL AND COALESCE(fu_updated.firstname, fu_created.firstname) IS NULL THEN ''
                                    WHEN COALESCE(fu_updated.lastname, fu_created.lastname) IS NULL THEN COALESCE(fu_updated.firstname, fu_created.firstname)
                                    WHEN COALESCE(fu_updated.firstname, fu_created.firstname) IS NULL THEN COALESCE(fu_updated.lastname, fu_created.lastname)
                                    ELSE CONCAT_WS(', ', COALESCE(fu_updated.lastname, fu_created.lastname), COALESCE(fu_updated.firstname, fu_created.firstname))
                                END AS lfdisplayname,
                                Lag(lgrfptlt.id) OVER (ORDER BY lgrfptlt.sys_period) AS prev_category_id
                            FROM lgr_form_%s_%s_to_%s_id AS lgrfptlt
                            LEFT JOIN log_%s flt ON lgrfptlt.form_%s_fk = flt.%s
                            LEFT JOIN log_%s lp ON lp.id = lgrfptlt.form_%s_fk
                            LEFT JOIN form_user AS fu_updated ON lp.updated_by = fu_updated.id
                            LEFT JOIN form_user AS fu_created ON lp.created_by = fu_created.id
                            WHERE lgrfptlt.form_%s_fk = %s ORDER BY Lower(lgrfptlt.sys_period)) query_set
                        WHERE  category_id_int != prev_category_id
                        OR prev_category_id IS NULL ORDER BY lfupdated_on;
                    `;
                    params = [
                        fieldName,
                        fieldName,
                        source,
                        formName,
                        fieldName,
                        source,
                        source,
                        source,
                        sourceid,
                        formName,
                        formName,
                        formName,
                        formId,
                    ];
                    const values = await this.db.env.ro.query(sql, params);
                    let f_arr = []; //carryforward array
                    const f_t = []; //final to return array
                    function getStates(db_arr, inf) {
                        const smallest_lower = getLowestAfterDate(
                            values,
                            "lfupdated_on",
                            inf
                        );
                        const smallest_lower_arr = getSmallestArr(
                            values,
                            "lfupdated_on",
                            smallest_lower,
                            inf
                        );
                        const nstate = [];
                        const nstate2 = [];
                        db_arr.filter((item) => {
                            const itemDateUpper = new Date(
                                item.lfupdated_on_upper
                            );
                            const smallestLoweDate = new Date(smallest_lower);
                            if (
                                itemDateUpper.valueOf() <
                                smallestLoweDate.valueOf()
                            ) {
                                nstate.push({
                                    ...item,
                                    foreign_id: -item.foreign_id,
                                    updated_on: item.lfupdated_on_upper,
                                });
                            }
                            if (
                                itemDateUpper.valueOf() ==
                                smallestLoweDate.valueOf()
                            ) {
                                nstate2.push({
                                    ...item,
                                    foreign_id: -item.foreign_id,
                                    updated_on: item.lfupdated_on,
                                });
                            }
                        });
                        const pstate = smallest_lower_arr.map((item) => {
                            return { ...item, updated_on: item.lfupdated_on };
                        });
                        if (nstate.length > 0) {
                            const f_arr_before = [...f_arr];
                            let u_on = "";
                            const r_arr = getRemovedArray(
                                f_arr,
                                Math.abs(nstate[0].foreign_id)
                            ); //all these are removed in one step, so there update_on will be same
                            u_on = r_arr ? r_arr[0]?.lfupdated_on_upper : null;
                            nstate.map((ns) => {
                                f_arr = removeItem(
                                    f_arr,
                                    Math.abs(ns.foreign_id)
                                );
                            });
                            if (f_arr_before.length != f_arr.length) {
                                const a = f_arr.map((aa) => {
                                    return { ...aa, updated_on: u_on };
                                });
                                f_t.push([...a]);
                            }
                        }
                        if (pstate.length > 0) {
                            f_arr.push(...pstate);
                            if (nstate2.length > 0) {
                                const f_arr_before = [...f_arr];
                                nstate2.map((ns2) => {
                                    f_arr = removeItem(
                                        f_arr,
                                        Math.abs(ns2.foreign_id)
                                    );
                                });
                                if (f_arr_before.length != f_arr.length) {
                                    f_t.push([...f_arr]);
                                }
                            } else {
                                f_t.push([...f_arr]);
                            }
                        }
                        if (smallest_lower_arr.length == 0) {
                            const aa = getBiggestUpperAfterDate(
                                db_arr,
                                "lfupdated_on_upper",
                                inf
                            );
                            // Sorting the array based on lfupdated_on_upper
                            const sortedData = aa.sort(
                                (a, b) =>
                                    new Date(a.lfupdated_on_upper) -
                                    new Date(b.lfupdated_on_upper)
                            );
                            // Grouping by lfupdated_on_upper
                            const groupedData = sortedData.reduce(
                                (acc, curr) => {
                                    const key = curr.lfupdated_on_upper;
                                    if (!acc[key]) {
                                        acc[key] = [];
                                    }
                                    acc[key].push(curr);
                                    return acc;
                                },
                                {}
                            );

                            // Converting the grouped object into an array of arrays
                            const result = Object.values(groupedData);
                            result.map((a) => {
                                const f_arr_before = [...f_arr];
                                a.map((p) => {
                                    f_arr = removeItem(
                                        f_arr,
                                        Math.abs(p.foreign_id)
                                    );
                                });
                                const b = f_arr.map((fa) => {
                                    return {
                                        ...fa,
                                        updated_on: fa.lfupdated_on_upper,
                                    };
                                });
                                if (f_arr_before.length != b.length) {
                                    //its means some of the obj are removed
                                    f_t.push([...b]);
                                }
                                return;
                            });
                            return;
                        }
                        getStates(db_arr, smallest_lower);
                        function removeItem(arr, value) {
                            arr = arr.filter((obj) => {
                                if (obj.foreign_id != value) {
                                    return obj;
                                }
                            });
                            return arr;
                        }
                        function getRemovedArray(arr, value) {
                            arr = arr.filter((obj) => {
                                if (obj.foreign_id == value) {
                                    return obj;
                                }
                            });
                            return arr;
                        }
                    }
                    getStates(values, "1992-07-25T14:15:13.553Z");
                    const to_return = f_t.map((aa) => {
                        const arr = [];
                        let obj = {};
                        aa.filter((o) => {
                            obj = { ...o };
                            arr.push(o[`${fieldName}`]);
                        });
                        obj = { ...obj };
                        obj[`${fieldName}`] = arr;
                        return obj;
                    });
                    ctx.status = 200;
                    ctx.body = to_return;
                    return;
                } else {
                    sql = `SELECT query_set.%s,
                              lfupdated_on,
                              lffirstname,
                              lflastname,
                              lfdisplayname
                      FROM   (SELECT lf.%s,
                                  Lower(lf.sys_period)                                     AS lfupdated_on,
                                  COALESCE(fu_updated.firstname, fu_created.firstname)     AS lffirstname,
                                  COALESCE(fu_updated.lastname, fu_created.lastname)       AS lflastname,
                                  CASE 
                                        WHEN COALESCE(fu_updated.lastname, fu_created.lastname) IS NULL AND COALESCE(fu_updated.firstname, fu_created.firstname) IS NULL THEN ''
                                        WHEN COALESCE(fu_updated.lastname, fu_created.lastname) IS NULL THEN COALESCE(fu_updated.firstname, fu_created.firstname)
                                        WHEN COALESCE(fu_updated.firstname, fu_created.firstname) IS NULL THEN COALESCE(fu_updated.lastname, fu_created.lastname)
                                        ELSE CONCAT_WS(', ', COALESCE(fu_updated.lastname, fu_created.lastname), COALESCE(fu_updated.firstname, fu_created.firstname))
                                    END AS lfdisplayname,
                                  Lag(lf.%s) OVER (ORDER BY lf.sys_period) AS prev_%s
                              FROM log_%s AS lf
                                  LEFT JOIN form_user AS fu_updated ON lf.updated_by = fu_updated.id
                                  LEFT JOIN form_user AS fu_created ON lf.created_by = fu_created.id
                              WHERE  lf.id = %s AND lf.%s IS NOT NULL) query_set
                      WHERE  query_set.%s != prev_%s
                              OR prev_%s IS NULL; `;
                    params = [
                        fieldName,
                        fieldName,
                        fieldName,
                        fieldName,
                        formName,
                        formId,
                        fieldName,
                        fieldName,
                        fieldName,
                        fieldName,
                    ];
                }
                const values = await this.db.env.ro.query(sql, params);
                ctx.status = 200;
                ctx.body = values;
            } catch (e) {
                console.error(e);
                ctx.status = 500;
                ctx.body = e;
            }
        } else {
            const e = "Invalid Request Method";
            console.error(e);
            ctx.status = 500;
            ctx.body = {
                message: e,
            };
        }
    }
};
