"use strict";
const _ = require("lodash");
const moment = require("moment");

module.exports = class PVerifyClass {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.adminRequestURL = "pverify/eligibility";
        this.fx = nes.modules.fx;
    }

    verifyRequired(data) {
        const requiredFields = [
            "dos_start_date",
            "dos_end_date",
            "is_subscriber_patient",
            "location",
            "payer_code",
            "provider_last_name",
            "provider_npi",
            "subscriber_member_id",
            "internal_id",
        ];
        const missing = requiredFields.filter((e) => !data[e]);
        if (missing.length > 0) {
            return {
                error: true,
                message: `Required fields ${missing.join("|")} are missing.`,
            };
        }
        return data;
    }
    mapData(data) {
        const dependant = [
            "dependent_patient_dob",
            "dependent_patient_first_name",
            "dependent_patient_gender",
            "dependent_patient_last_name",
            "dependent_patient_middle_name",
        ];
        const found = dependant.filter((e) => data[e]);
        const apiData = {
            doS_EndDate: data.dos_end_date || "",
            doS_StartDate: data.dos_start_date || "",
            includeTextResponse:
                data?.include_text_response ||
                data?.include_text_response?.toLowerCase() === "false"
                    ? "false"
                    : "true",
            InternalId: data.internal_id || "12345",
            isSubscriberPatient: data.is_subscriber_patient || "",
            location: data.location || "any",
            payerCode: data.payer_code || "",
            payerName: data.payer_name || "",
            provider: {
                firstName: data.provider_first_name || "",
                lastName: data.provider_last_name || "",
                middleName: data.provider_middle_name || "",
                npi: data.provider_npi || "",
            },
            subscriber: {
                dob: data.subscriber_dob || "",
                firstName: data.subscriber_first_name || "",
                lastName: data.subscriber_last_name || "",
                memberID: data.subscriber_member_id || "",
            },
        };

        if (found.length > 0) {
            apiData["dependent"] = {
                patient: {
                    dob: data.dependent_patient_dob || "",
                    firstName: data.dependent_patient_first_name || "",
                    gender: data.dependent_patient_gender || "",
                    lastName: data.dependent_patient_last_name || "",
                    middleName: data.dependent_patient_middle_name || "",
                },
            };
            if (data.dependent_relation_with_subscriber)
                apiData["dependent"]["relationWithSubscriber"] =
                    data.dependent_relation_with_subscriber;
        }

        if (
            data.customer_id &&
            typeof data.customer_id === "string" &&
            data.customer_id !== ""
        ) {
            apiData["customerId"] = data.customer_id;
        }
        if (
            data.reference_id &&
            typeof data.reference_id === "string" &&
            data.reference_id !== ""
        ) {
            apiData["referenceId"] = data.reference_id;
        }
        if (
            data.practice_type_code &&
            typeof data.practice_type_code === "string" &&
            data.practice_type_code !== ""
        ) {
            apiData["practiceTypeCode"] = data.practice_type_code;
        }
        if (
            data.provider_taxonomy &&
            typeof data.provider_taxonomy === "string" &&
            data.provider_taxonomy !== ""
        ) {
            apiData.provider["taxonomy"] = data.provider_taxonomy;
        }
        if (
            data.subscriber_ssn &&
            typeof data.subscriber_ssn === "string" &&
            data.subscriber_ssn !== ""
        ) {
            apiData.subscriber["ssn"] = data.subscriber_ssn.replaceAll("-", "");
        }
        if (
            data.provider_pin &&
            typeof data.provider_pin === "string" &&
            data.provider_pin !== ""
        ) {
            apiData.provider["pin"] = data.provider_pin;
        }
        return apiData;
    }

    async processResponse(transaction, response, data) {
        if (
            !(
                response.response_json_data &&
                !_.isEmpty(response.response_json_data)
            )
        ) {
            return {
                error: true,
                message: `PVerify Request Failed. ${JSON.stringify(response)}`,
            };
        } else if (
            response.response_json_data &&
            !_.isEmpty(response.response_json_data) &&
            response.response_json_data.ErrorCode &&
            response.response_json_data.ErrorCode !== ""
        ) {
            return {
                error: true,
                message: `PVerify Request Failed. ${JSON.stringify(response.response_json_data.ErrorDescription)}`,
            };
        }
        if (
            response.response_json_data.requestid &&
            response.response_json_data.requestid !== ""
        ) {
            return this.updateInsuranceRecord(transaction, response, data);
        }
        return {
            error: true,
            message: "Verification Response Handling Failed",
        };
    }

    async prettyError(ctx, err, data) {
        const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
        await delay(1000);
        ctx.status = 500;
        ctx.body = {
            data: data,
            error: err,
        };
        return;
    }

    /**
     * Proxies the PVerify API call to verify patient eligibility.
     * Updates patient_insurance if success and file has recieved
     * if no filehash then return success string
     * @param {object} ctx - The Koa context object.
     * @param {object} data - The data to be sent to the PVerify API.
     * @returns {error Object} - in case of error
     */
    async proxyPverify(ctx, data) {
        const t = this.db.env.rw.transaction(ctx);
        t.insert("pverify_pat_elig", data);
        const res = await t.commit();
        if (res.error) {
            return this.prettyError(ctx, `Save Error: ${res.error}`, data);
        }
        t.init();
        const apiData = this.mapData(data);
        const adminResponse = await this.adminPVerify(ctx, apiData);
        const result = await this.processResponse(t, adminResponse, data);
        if (result.error) {
            return result;
        }
        const tresult = await t.commit();
        if (tresult.error) {
            return { error: true, message: `Save Error: ${tresult.message}` };
        }
        return result;
    }

    async updateInsuranceRecord(transaction, response, data) {
        // if file hash exists, update patient_insurance
        const resBody = {
            success: true,
            message:
                "Patient eligibility verification request submitted successfully",
        };
        const lastSentDate = moment().format("YYYY-MM-DD HH:mm:ss");
        const reqID = response.response_json_data.RequestID;
        const fileHash = response.s3_filehash;
        const patInsuranceData = {
            last_verification_req_id: reqID,
            last_verification_sent_date: lastSentDate,
        };
        data["last_verification_req_id"] = reqID;
        data["last_verification_sent_date"] = lastSentDate;
        data["response_json_data"] = response.response_json_data;
        if (fileHash) {
            patInsuranceData["last_verification_file"] = fileHash;
            data["last_verification_file"] = fileHash;
            resBody["patient_insurance_id"] = data.patient_insurance_id;
        }
        await transaction.insert("pverify_pat_elig", data);
        await transaction.update(
            "patient_insurance",
            patInsuranceData,
            data.patient_insurance_id
        );
        return resBody;
    }
    async adminPVerify(ctx, data) {
        const successReq = {
            payerCode: "00115",
            payerName: "IBX",
            provider: {
                firstName: "",
                middleName: "",
                lastName: " test name",
                npi: "**********",
                pin: "00000",
            },
            subscriber: {
                firstName: "Brandon",
                dob: "05/18/1989",
                lastName: "Young",
                memberID: "YXF132977915001",
            },
            dependent: {},
            isSubscriberPatient: "True",
            doS_StartDate: "05/06/2024",
            doS_EndDate: "05/06/2024",
            practiceTypeCode: "3",
            referenceId: "Pat MRN:12345",
            location: "any",
            IncludeTextResponse: "true",
            InternalId: "12345",
            CustomerId: "25",
        };
        const adminResponse = await this.fx.adminRequest(
            ctx,
            this.adminRequestURL + "/summary",
            {
                method: "POST",
                data: data,
            }
        );
        return adminResponse;
    }

    async process(ctx) {
        let data = ctx.request.body;
        if (_.isEmpty(data)) {
            throw new Error("No data provided");
        }
        try {
            data = this.verifyRequired(data);
            if (data.error) {
                throw new Error(data.message);
            }
            const result = await this.proxyPverify(ctx, data);
            if (result.error) {
                await this.prettyError(ctx, result.message, data);
                return;
            }
        } catch (e) {
            console.error(e);
            ctx.type = "application/json";
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
            } else {
                ctx.status = 400;
                ctx.body = {
                    error: e.message,
                };
            }
        }
    }
};
