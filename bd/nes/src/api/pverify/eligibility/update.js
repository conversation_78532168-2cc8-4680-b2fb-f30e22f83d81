"use strict";

module.exports = class PVerifyUpdateClass {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
    }

    /**
     * Updates the patient verification information in the database.
     *
     * @param {object} ctx - The context object containing the user information.
     * @param {object} data - An object containing the request ID and file hash.
     * @returns {object} - An object with a success flag and a message.
     * @throws {Error} - If the patient verification record or patient insurance record is not found.
     * @throws {Error} - If there is an error updating the records.
     */
    async updatePVerifyInfo(ctx, data) {
        let pverify = await this.form.get.get_form(
            ctx,
            ctx.user,
            "pverify_pat_elig",
            {
                limit: 1,
                filter: "last_verification_req_id:" + data.RequestId,
            }
        );
        if (!(pverify && pverify.length > 0)) {
            throw new Error(
                "Patient verification record not found against RequestId: " +
                    data.RequestId
            );
        }
        pverify = pverify[0];
        if (!pverify.patient_insurance_id) {
            throw new Error(
                "Patient insurance record not found against RequestId: " +
                    data.RequestId
            );
        }
        const updateData = {
            last_verification_file: data.fileHash,
        };
        const t = this.db.env.rw.transaction(ctx);
        const updatePromises = [];
        updatePromises.push(
            t.update("pverify_pat_elig", updateData, pverify.id)
        );
        updatePromises.push(
            t.update(
                "patient_insurance",
                updateData,
                pverify.patient_insurance_id
            )
        );
        await Promise.all(updatePromises);
        const res = await t.commit();
        if (res.error) {
            throw new Error(res.message);
        } else {
            return {
                success: true,
                message: "Update Records.",
            };
        }
    }

    dataValidation(data) {
        if (!data.RequestId) {
            throw new Error("RequestId is required.");
        }
        if (!data.fileHash) {
            throw new Error("fileHash is required.");
        }
    }

    // Request handler
    async process(ctx) {
        const data = ctx.request.body;
        try {
            this.dataValidation(data);
            const res = await this.updatePVerifyInfo(ctx, data);
            ctx.status = 200;
            ctx.body = res;
        } catch (e) {
            console.error(e);
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
            } else {
                ctx.status = 400;
                ctx.body = {
                    error: e.message,
                };
            }
        }
    }
};
