"use strict";

module.exports = class PVerifyUpdateClass {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.form = nes.shared.form;
        this.adminRequestURL = "pverify/eligibility";
        this.fx = nes.modules.fx;
    }

    /**
     * Retrieves a PVerify document from the S3 storage based on the provided insurance ID.
     *
     * @param {object} ctx - The Koa context object.
     * @param {string} ins_id - The ID of the insurance record.
     * @returns {Promise<void>} - Resolves with the PDF file content in the response body.
     * @throws {Error} - If no patient insurance record is found for the given insurance ID, or if no file hash is found.
     */
    async getPVerifyDocument(ctx, ins_id) {
        const pat_ins = await this.form.get.get_form(
            ctx,
            ctx.user,
            "patient_insurance",
            { limit: 1, filter: "id:" + ins_id }
        );
        if (!(pat_ins && pat_ins.length > 0)) {
            throw new Error(
                "No patient insurance record found for insurance id " + ins_id
            );
        }
        const fileHash = pat_ins[0]?.last_verification_file;
        if (!fileHash) {
            throw new Error("No file hash found for insurance id " + ins_id);
        }
        console.log("Getting PDF file from S3: " + fileHash);
        const adminResponse = await this.fx.adminRequest(
            ctx,
            this.adminRequestURL + "/report?filehash=" + fileHash,
            {
                method: "GET",
                raw: true,
            }
        );
        const pdf = await adminResponse.buffer();
        ctx.type = "application/pdf";
        ctx.body = pdf;
        ctx.status = 200;
    }

    async process(ctx) {
        // check access
        const params = Object.assign({}, ctx.query);
        const ins_id = params?.ins_id;
        if (!ins_id) {
            throw new Error("Insurance id is required");
        }
        try {
            await this.getPVerifyDocument(ctx, ins_id);
        } catch (e) {
            ctx.status = 400;
            ctx.body = {
                error: e.message,
            };
        }
    }
};
