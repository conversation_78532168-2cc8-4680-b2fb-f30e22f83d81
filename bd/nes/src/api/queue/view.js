"use strict";
const _ = require("lodash");

const QueueUtils = require("./queue-utils");

module.exports = class ApiView {
    constructor(nes) {
        this.shared = nes.shared;
        this.queue = new QueueUtils(nes);
    }
    async process(ctx, urlpath) {
        try {
            if (urlpath.path.lenght < 5) {
                ctx.status = 400;
                ctx.body = { error: "Invalid path" };
                return;
            }
            const wf_code = urlpath.path[3];
            const wf_node = urlpath.path[4];
            const user = ctx.user;
            const params = Object.assign({}, ctx.query);
            if (user) {
                let qd = {
                    error: "Unexpected error",
                };
                if (ctx.request.method === "POST") {
                    qd = await this.queue.getData(ctx, urlpath, {
                        wf_code,
                        wf_node,
                        params,
                    });
                } else {
                    qd = await this.queue.get_data(
                        ctx,
                        user,
                        wf_code,
                        wf_node,
                        params
                    );
                }
                if (qd.error) {
                    ctx.status = 400;
                    ctx.body = qd;
                    return;
                }
                ctx.body = qd;
                return;
            } else {
                ctx.status = 403;
                ctx.body = { error: "Access denied." };
            }
        } catch (e) {
            console.log(e);
            ctx.status = 500;
            ctx.body = e;
        }
    }
};
