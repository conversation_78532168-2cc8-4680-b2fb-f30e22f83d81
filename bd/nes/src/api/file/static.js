"use strict";
const { S3 } = require("@aws-sdk/client-s3");
const uuid = require("uuid");
const fs = require("fs");
const _ = require("lodash");
const path = require("path");

const BadRequestException = { status: 400 };
const FileNotSupportedException = { status: 415 };
const NoFileException = { status: 404 };
const FileSizeException = { status: 413 };
const UnauthorizedException = { status: 403 };

module.exports = class Static {
    constructor(nes, basedir = null) {
        this.nes = nes;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.shared = nes.shared;
        this.basedir = basedir ? basedir : __dirname;
        this.bucket = this.shared.config.env["AWS_S3_BUCKET"];
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.s3 = new S3({
            credentials: {
                accessKeyId: this.shared.config.env["AWS_ACCESS_KEY"],
                secretAccessKey: this.shared.config.env["AWS_SECRET_KEY"],
            },
            region: this.shared.config.env["AWS_REGION"],
        });
        this.allowed_extensions = [
            "pdf",
            "txt",
            "png",
            "jpg",
            "jpeg",
            "gif",
            "zip",
            "json",
            "doc",
            "docx",
            "xls",
            "xlsx",
            "csv",
            "pptx",
            "ppt",
            "odt",
            "bmp",
            "odp",
            "ods",
        ];
    }

    get_filesize(filename) {
        const stats = fs.statSync(filename);
        const fileSizeInBytes = stats.size;
        return fileSizeInBytes;
    }
    async init() {}
    //set credentials for AWS and returns a promise for file upload
    upload_to_S3(file, user) {
        const hash = Buffer.from(uuid.v4()).toString("hex");
        const { key, cachePath, cacheFile } = this.fx.getFileKeyPath(
            "static",
            hash
        );
        const filename = file.originalFilename.replace(/[^a-zA-Z0-9 .]/g, "");
        const jsonFile = `${cacheFile}.json`;
        console.log(user);
        const sql = `INSERT INTO %s(name, hash_key, mime, upload_type, created_by, created_on) VALUES(%L, %L, %L, %L, %s, NOW())`;
        this.db.env.rw.query(sql, [
            "form_upload",
            filename,
            hash,
            file.mimetype,
            "static",
            user.id,
        ]);
        if (!fs.existsSync(cachePath) && !fs.existsSync(jsonFile)) {
            fs.mkdirSync(cachePath, { recursive: true });
            fs.writeFileSync(
                jsonFile,
                JSON.stringify({
                    filename: filename,
                    mimeType: file.mimetype,
                    filesize: file.size,
                })
            ); // Save metadata
        }

        const params = {
            Bucket: this.bucket,
            Key: key,
            Body: fs.readFileSync(file.filepath),
            ServerSideEncryption: "AES256",
            StorageClass: "STANDARD_IA",
            Metadata: { filename: filename },
            ContentType: file.mimetype,
        };
        return new Promise((resolve, reject) => {
            try {
                this.s3.putObject(params, function (err, data) {
                    if (err) {
                        reject(err);
                    } else {
                        if (!fs.existsSync(cachePath)) {
                            fs.mkdirSync(cachePath, { recursive: true });
                        }
                        const res = JSON.stringify({
                            filehash: hash,
                            filename: file.filename,
                            filesize: file.size,
                            mimetype: file.mimetype,
                        });
                        console.info(
                            "File successfully uploaded to S3 for hash: " + hash
                        );
                        resolve(res);
                    }
                });
            } catch (e) {
                console.log(e);
            }
        });
    }

    async delete_from_cache(hash, file, user) {
        const sql =
            "UPDATE %s SET archived='t', updated_by=%s, updated_on=NOW() WHERE hash_key=%L;";
        await this.db.env.rw.query(sql, ["form_upload", user.id, hash]);
        fs.unlinkSync(file);
        return true;
    }
    delete_from_s3(key) {
        const params = {
            Bucket: this.bucket,
            Key: key,
        };
        try {
            return new Promise((resolve, reject) => {
                try {
                    this.s3.deleteObject(params, function (err, _) {
                        if (err) {
                            console.error(err);
                            reject(err);
                        } else {
                            resolve(true);
                        }
                    });
                } catch (e) {
                    console.log(e);
                }
            });
        } catch (e) {
            console.info(
                `Error occured while retrieving file from s3: ${key}\n${e}`
            );
        }
    }

    // Request handler
    async process(ctx, urlpath) {
        const that = this;
        const user = ctx.user;
        const callback = ctx.request.query.callback;
        const callbackid = ctx.request.query.callbackid;
        try {
            if (urlpath.path.length < 3 || urlpath.path[2] == "") {
                ctx.status = 500;
                ctx.body = {
                    error: "Invalid form path specified: " + urlpath.url.path,
                };
                return;
            }

            // User authentication
            if (_.isEmpty(user)) {
                throw UnauthorizedException;
            }
            // Delete Request to downloading file
            if (ctx.request.method == "DELETE") {
                const hash = urlpath.path[3];
                try {
                    if (!hash) {
                        throw BadRequestException;
                    }
                    const { key, cachePath, cacheFile } =
                        this.fx.getFileKeyPath("static", hash);
                    await this.delete_from_cache(hash, cacheFile, user);
                    this.delete_from_s3(key);
                    ctx.status = 200;
                    ctx.body = { success: true };
                } catch (e) {
                    if (e.status == 400) {
                        ctx.status = e.status;
                        ctx.body = { error: "No hash provided" };
                    } else {
                        ctx.status = 200;
                        ctx.body = { success: true };
                    }
                }
            } else if (ctx.request.method == "POST") {
                let files = ctx.request.files.file;
                if (!Array.isArray(files)) {
                    files = [files];
                } else {
                    files = files.slice(0, 5);
                }
                ctx.request.socket.setTimeout(files.length * 2 * 60 * 1000);
                try {
                    if (files.length == 0) {
                        throw NoFileException;
                    }

                    // this can be done in a better way, somehow its a hack to use first five files and discard others
                    files = files.slice(0, 5);
                    const promises = []; //handle all file uploads at once
                    files.forEach((file) => {
                        if (file.size > 104857600) {
                            //100MBs = 104857600 bytes
                            throw FileSizeException;
                        }

                        const fileType = path
                            .extname(file.originalFilename)
                            .slice(1)
                            .toLowerCase();
                        if (!this.allowed_extensions.includes(fileType)) {
                            throw FileNotSupportedException;
                        }
                        promises.push(this.upload_to_S3(file, user));
                    });
                    await Promise.all(promises).then(function (fileshashes) {
                        ctx.status = 200;
                        ctx.body = that.fx.write_resp(
                            callback,
                            callbackid,
                            fileshashes
                        );
                    });
                } catch (e) {
                    console.error(e);
                    if (e.status == 415) {
                        ctx.status = e.status;
                        ctx.body = this.fx.write_resp(callback, callbackid, [
                            JSON.stringify({ error: "File type not allowed." }),
                        ]);
                    } else if (e.status == 413) {
                        ctx.status = e.status;
                        ctx.body = this.fx.write_resp(callback, callbackid, [
                            JSON.stringify({
                                error: "The file that was selected is too large, the maximum upload size is 100MB",
                            }),
                        ]);
                    } else if (e.status == 404) {
                        ctx.status = e.status;
                        ctx.body = this.fx.write_resp(callback, callbackid, [
                            JSON.stringify({ error: "No file in request" }),
                        ]);
                    } else {
                        ctx.status = 500;
                        ctx.body = this.fx.write_resp(callback, callbackid, [
                            JSON.stringify({
                                error: "Server Error, please contact IT.",
                            }),
                        ]);
                    }
                }
            }
        } catch (e) {
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = this.fx.write_resp(callback, callbackid, [
                    JSON.stringify({ error: "Access denied." }),
                ]);
            }
        }
    }
};
