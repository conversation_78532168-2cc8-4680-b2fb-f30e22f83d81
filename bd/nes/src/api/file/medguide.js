"use strict";
const QueryClass = require("../query/index");
const { PassThrough } = require("stream");

module.exports = class MedGuide {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.query = new QueryClass(nes);
        this.downloadLocation = "/tmp/";
        this.daysLimit = 7;
    }
    async getDocument(ctx, invID) {
        // call to query module with params to get invID
        let fileOnServer = "";
        try {
            let url = await this.query.run_query(
                ctx,
                ctx.user,
                "medguide_link",
                { x1: invID }
            );
            if (url.length < 1) {
                throw new Error("Medguide not found");
            } else if (url.error) {
                throw new Error(url.error);
            }
            url = url[0].link;
            const urlHash = this.fx.md5(url);
            fileOnServer =
                this.downloadLocation + "medguide_" + urlHash + ".pdf";
            if (!(await this.fx.exists(fileOnServer))) {
                console.log(
                    `Medguide not found for ${invID}. Downloading from link...`
                );
                await this.fx.downloadAndStreamFile(ctx, url);
                return;
            } else if (
                await this.checkOlderThan(fileOnServer, this.daysLimit)
            ) {
                console.log(
                    `Cached file older then ${this.daysLimit} fetching latest`
                );
                this.fx.deleteFile(fileOnServer);
                await this.fx.downloadAndStreamFile(ctx, url);
                return;
            }
            console.log(`Serving Cached file for ${invID}`);
            const fileReadStream = this.fx.getReadStream(fileOnServer);
            for await (const chunk of fileReadStream) {
                ctx.body.write(chunk);
            }
            return;
        } catch (error) {
            if (fileOnServer !== "")
                await this.fx.deleteFileIfExists(fileOnServer);
            throw new Error(error);
        }
    }
    async process(ctx, urlpath) {
        try {
            if (urlpath.path.length < 4 || urlpath.path[3] == "")
                throw new Error("Inventory id not Provided");
            const inventoryId = urlpath.path[3];
            ctx.status = 200;
            ctx.type = "application/pdf";
            ctx.body = new PassThrough();
            ctx.body.on("error", (err) => {
                throw new Error(err);
            });
            await this.getDocument(ctx, inventoryId);
            ctx.body.end();
        } catch (error) {
            ctx.type = "application/json";
            ctx.body = error.message;
            ctx.status = 500;
        }
    }

    async checkOlderThan(file, days) {
        const stats = this.fx.getStat(file);
        const creationTime = stats.birthtime.getTime();
        const currentTime = Date.now();
        const durationMilliseconds = days * 24 * 60 * 60 * 1000;
        const timeDifference = currentTime - creationTime;
        return timeDifference > durationMilliseconds;
    }
};
