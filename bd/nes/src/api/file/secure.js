"use strict";
const { S3 } = require("@aws-sdk/client-s3");
const uuid = require("uuid");
const fs = require("fs");
const _ = require("lodash");
const path = require("path");

const FileNotSupportedException = { status: 415 };
const NoFileException = { status: 404 };
const FileSizeException = { status: 413 };
const UnauthorizedException = { status: 403 };

module.exports = class ApiView {
    constructor(nes, basedir = null) {
        this.nes = nes;
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.basedir = basedir || __dirname;
        this.fx = nes.modules.fx;
        this.bucket = this.shared.config.env["AWS_S3_BUCKET"];
        this.s3 = new S3({
            credentials: {
                accessKeyId: this.shared.config.env["AWS_ACCESS_KEY"],
                secretAccessKey: this.shared.config.env["AWS_SECRET_KEY"],
            },
            region: this.shared.config.env["AWS_REGION"],
        });
        this.allowedExtensions = [
            "pdf",
            "txt",
            "png",
            "jpg",
            "jpeg",
            "gif",
            "zip",
            "json",
            "doc",
            "docx",
            "xls",
            "xlsx",
            "csv",
            "pptx",
            "ppt",
            "odt",
            "bmp",
            "odp",
            "ods",
        ];
    }

    getFileSize(filename) {
        return fs.statSync(filename).size;
    }

    async downloadFromS3(hash) {
        console.info(`Fetching the following key from S3: ${hash}`);
        const params = { Bucket: this.bucket, Key: hash };

        try {
            return new Promise((resolve, reject) => {
                try {
                    this.s3.getObject(params, function (err, data) {
                        if (err) {
                            console.error(err);
                            reject(err);
                        } else {
                            resolve(data);
                        }
                    });
                } catch (e) {
                    console.log(e);
                    reject(e);
                }
            });
        } catch (e) {
            console.info(
                `Error occured while retrieving file from s3: ${hash}\n${e}`
            );
        }
    }

    async uploadToS3(file) {
        const hash = Buffer.from(uuid.v4()).toString("hex");
        const { key, cachePath, cacheFile } = this.fx.getFileKeyPath(
            "secure",
            hash
        );
        const filename = file.originalFilename.replace(/[^a-zA-Z0-9 .]/g, "");
        const ext = path.extname(filename).slice(1).toLowerCase();
        const jsonFile = `${cacheFile}.json`;
        const params = {
            Bucket: this.bucket,
            Key: key,
            Body: fs.readFileSync(file.filepath),
            ServerSideEncryption: "AES256",
            StorageClass: "STANDARD_IA",
            Metadata: { filename: filename },
            ContentType: file.mimetype,
        };
        if (ext === "pdf" || ext === "tif" || ext === "tiff") {
            fs.mkdirSync(`${cacheFile}-images`, { recursive: true });
        } else if (
            !fs.existsSync(cachePath) &&
            !fs.existsSync(jsonFile) &&
            !fs.existsSync(cacheFile)
        ) {
            fs.mkdirSync(cachePath, { recursive: true });
        }
        const meta = {
            filename: filename,
            mimeType: file.mimetype,
            filesize: file.size,
        };

        await this.fx.copyFile(file.filepath, cacheFile);
        fs.writeFileSync(jsonFile, JSON.stringify(meta)); // Save metadata
        return new Promise((resolve, reject) => {
            try {
                this.s3.putObject(params, async (err, data) => {
                    if (err) {
                        console.error(err);
                        this.fx.deleteFile(cacheFile);
                        this.fx.deleteFile(jsonFile);
                        reject(err);
                    } else {
                        let res = {
                            filehash: hash,
                            filename: file.originalFilename,
                            filesize: file.size,
                            mimetype: file.mimetype,
                        };
                        res = await this.makeResponse(
                            res,
                            ext,
                            cacheFile,
                            cachePath,
                            hash
                        );
                        console.info(
                            "File successfully uploaded to S3 for hash: " + hash
                        );
                        this.fx.deleteFile(file.filepath);
                        resolve(res);
                    }
                });
            } catch (e) {
                console.log(e);
            }
        });
    }

    async makeResponse(res, ext, cacheFile, cachePath, hash) {
        try {
            if (["pdf", "tif", "tiff"].includes(ext)) {
                const images = await this.fx.convertFileToImage(
                    cacheFile,
                    cachePath,
                    hash
                );
                if (images.thumbnail && images.images.length > 0) {
                    res = {
                        ...res,
                        thumbnail: images.thumbnail,
                        images: [...images.images],
                    };
                }
            }
            return JSON.stringify(res);
        } catch (err) {
            console.error(err);
            return JSON.stringify({ error: "An error occurred" });
        }
    }

    setHeaders(ctx, filename, mimeType) {
        ctx.set("Content-Disposition", `attachment; filename=${filename}`);
        ctx.set("Content-Type", mimeType);
    }

    writeResponse(callback, callbackId, data) {
        if (!callback || !callbackId) {
            return data;
        }
        return `<script>${callback}('${callbackId}',${data});</script>`;
    }

    async serveFile(ctx, cacheFile, jsonFile, cachePath, key) {
        if (fs.existsSync(cacheFile) && fs.existsSync(jsonFile)) {
            const jsonData = fs.readFileSync(jsonFile, "utf8");
            const metadata = JSON.parse(jsonData);
            this.setHeaders(ctx, metadata.filename, metadata.mimeType);
            ctx.status = 200;
            ctx.body = fs.readFileSync(cacheFile);
            return true;
        }
        return false;
    }

    checkPaths(cachePath, cacheFile, previewPath) {
        if (!fs.existsSync(cachePath)) {
            fs.mkdirSync(cachePath, { recursive: true });
        }
        if (previewPath && !fs.existsSync(previewPath)) {
            fs.mkdirSync(`${cacheFile}-images`, { recursive: true });
        }
    }

    async process(ctx, urlpath) {
        const user = ctx.user;
        const callback = ctx.request.query.callback;
        const callbackId = ctx.request.query.callbackid;

        try {
            if (urlpath.path.length < 3 || !urlpath.path[2]) {
                ctx.status = 500;
                ctx.body = {
                    error: `Invalid form path specified: ${urlpath.url.path}`,
                };
                return;
            }

            if (_.isEmpty(user)) {
                throw UnauthorizedException;
            }

            if (ctx.request.method === "GET") {
                const hash = urlpath.path[3];
                const { key, cachePath, cacheFile } = this.fx.getFileKeyPath(
                    "secure",
                    hash
                );
                const jsonFile = `${cacheFile}.json`;
                const previewPath = urlpath.path[4]
                    ? `${cacheFile}-images/${hash}-${urlpath.path[4]}`
                    : null;
                this.checkPaths(cachePath, cacheFile, previewPath);
                if (previewPath && fs.existsSync(previewPath)) {
                    this.setHeaders(ctx, urlpath.path[4], "image/png");
                    ctx.status = 200;
                    ctx.body = fs.readFileSync(previewPath);
                    return;
                }

                if (
                    !previewPath &&
                    (await this.serveFile(
                        ctx,
                        cacheFile,
                        jsonFile,
                        cachePath,
                        key
                    ))
                ) {
                    return;
                }
                if (!fs.existsSync(cacheFile)) {
                    const file = await this.downloadFromS3(key);
                    if (file) {
                        await this.fx.writeFileToFS(
                            file.Body,
                            {
                                filename: file.Metadata.filename,
                                mimeType: file.ContentType,
                            },
                            cacheFile
                        );
                        this.setHeaders(
                            ctx,
                            file.Metadata.filename,
                            file.ContentType
                        );
                    }
                }

                if (previewPath) {
                    if (!fs.existsSync(previewPath)) {
                        await this.fx.convertFileToImage(
                            cacheFile,
                            cachePath,
                            hash
                        );
                        ctx.status = 200;
                        ctx.body = fs.readFileSync(previewPath);
                        return true;
                    }
                }

                ctx.status = 200;
                ctx.body = fs.readFileSync(cacheFile);
            } else {
                let files = ctx.request.files.file;
                if (!Array.isArray(files)) {
                    files = [files];
                } else {
                    files = files.slice(0, 5);
                }

                ctx.request.socket.setTimeout(files.length * 2 * 60 * 1000);

                if (files.length === 0) {
                    throw NoFileException;
                }

                const promises = files.map((file) => {
                    if (file.size > 104857600) {
                        throw FileSizeException;
                    }
                    const fileType = path
                        .extname(file.originalFilename)
                        .slice(1)
                        .toLowerCase();
                    if (!this.allowedExtensions.includes(fileType)) {
                        throw FileNotSupportedException;
                    }
                    return this.uploadToS3(file);
                });
                try {
                    const fileHashes = await Promise.all(promises);
                    ctx.status = 200;
                    ctx.body = this.writeResponse(
                        callback,
                        callbackId,
                        fileHashes
                    );
                } catch (e) {
                    this.handleError(e, ctx, callback, callbackId);
                }
            }
        } catch (e) {
            this.handleError(e, ctx, callback, callbackId);
        }
    }

    handleError(e, ctx, callback, callbackId) {
        console.error(e);
        if (e.status === 415) {
            ctx.status = e.status;
            ctx.body = this.writeResponse(callback, callbackId, [
                JSON.stringify({ error: "File type not allowed." }),
            ]);
        } else if (e.status === 413) {
            ctx.status = e.status;
            ctx.body = this.writeResponse(callback, callbackId, [
                JSON.stringify({
                    error: "The file that was selected is too large, the maximum upload size is 100MB",
                }),
            ]);
        } else if (e.status === 404) {
            ctx.status = e.status;
            ctx.body = this.writeResponse(callback, callbackId, [
                JSON.stringify({ error: "No file in request" }),
            ]);
        } else if (e.status === 403) {
            ctx.status = e.status;
            ctx.body = this.writeResponse(callback, callbackId, [
                JSON.stringify({ error: "Access denied." }),
            ]);
        } else if (e.name === "NoSuchKey") {
            ctx.status = 404;
            ctx.body = this.writeResponse(callback, callbackId, [
                JSON.stringify({ error: "File not found." }),
            ]);
        } else {
            ctx.status = 500;
            ctx.body = this.writeResponse(callback, callbackId, [
                JSON.stringify({ error: "Server Error, please contact IT." }),
            ]);
        }
    }
};
