"use strict";

module.exports = class ApiTest {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.dsl = nes.modules.dsl;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.notify = nes.shared.notify;
    }
    async sendEmail(ctx) {
        // get template
        const body = ctx.request.body;
        const data = await this.notify.sendTemplateEmail(
            ctx,
            { to: body.to },
            body.subject,
            body.code,
            body.subsititutions
        );
        if (data.error) throw new Error(data.message);
    }
    async process(ctx, urlpath) {
        try {
            const body = await this.sendEmail(ctx);
            return (ctx.body = body);
        } catch (e) {
            ctx.status = 500;
            ctx.body = e;
            console.error(e);
        }
    }
};
