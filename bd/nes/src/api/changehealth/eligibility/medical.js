"use strict";
const _ = require("lodash");
const moment = require("moment");
const uuid = require("uuid").v4;

module.exports = class CHealthClass {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.adminRequestURL = "/claims/med";
        this.fx = nes.modules.fx;
    }

    verifyRequired(data) {
        const requiredFields = [
            "subscriber_first_name",
            "subscriber_member_id",
            "subscriber_last_name",
            "subscriber_dob",
            "subscriber_gender",
            "subscriber_street",
            "subscriber_city",
            "subscriber_state_id",
            "subscriber_zip",
            "organization_name",
            "trading_partner_service_id",
        ];
        const missing = requiredFields.filter((e) => !data[e]);
        if (missing.length > 0) {
            return {
                error: true,
                message: `Required fields ${missing.join("|")} are missing.`,
            };
        }
        return data;
    }
    mapData(data) {
        const apiData = {
            _meta: {
                clara_mrn: data.patient_mrn,
                medical_claims_processor: "change",
                is_test: process.env["NODE_ENV"] != "production",
                clara_claim_uuid: uuid(),
            },
            subscriber: {
                memberId: data.subscriber_member_id,
                firstName: data.subscriber_first_name,
                lastName: data.subscriber_last_name,
                dateOfBirth: moment(data.subscriber_dob, "MM/DD/YYYY").format(
                    "yyyyMMDD"
                ),
                gender:
                    data.subscriber_gender.toLowerCase() === "male" ? "M" : "F",
                address: {
                    address1:
                        data.subscriber_street +
                        (data.subscriber_street2 || ""),
                    city: data.subscriber_city,
                    state: data.subscriber_state_id,
                    postalCode: data.subscriber_zip,
                },
            },
            controlNumber: "002211334", // dummy value that admin server overwrites but is required for request
            submitterTransactionIdentifier:
                data.submitter_transaction_identifier,
            tradingPartnerServiceId: data.trading_partner_service_id,
            tradingPartnerName: "Change",
            provider: {
                organizationName: data.organization_name,
            },
        };
        return apiData;
    }

    async processResponse(transaction, response, data) {
        const responseJson = await response.json();

        if (response.status == 200) {
            if (
                responseJson.response_json_data.controlNumber &&
                responseJson.response_json_data.controlNumber !== ""
            ) {
                return this.updateInsuranceRecord(
                    transaction,
                    responseJson,
                    data
                );
            } else {
                return {
                    error: true,
                    message: "Verification Response Handling Failed",
                };
            }
        } else {
            if (responseJson.error) {
                if (
                    responseJson.error_description &&
                    Array.isArray(responseJson.error_description) &&
                    responseJson.error_description.length > 0
                ) {
                    return {
                        error: true,
                        message: `270 Eligibility Request Failed. ${responseJson.error_description.join("\n")}`,
                    };
                }
            } else {
                return {
                    error: true,
                    message: "Verification Response Handling Failed",
                };
            }
        }
    }

    async prettyError(ctx, err, data) {
        const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
        await delay(1000);
        ctx.status = 500;
        ctx.body = {
            data: data,
            error: err,
        };
        return;
    }

    /**
     * Proxies the PVerify API call to verify patient eligibility.
     * Updates patient_insurance if success and file has recieved
     * if no filehash then return success string
     * @param {object} ctx - The Koa context object.
     * @param {object} data - The data to be sent to the PVerify API.
     * @returns {error Object} - in case of error
     */
    async proxyCH(ctx, data) {
        const t = this.db.env.rw.transaction(ctx);
        t.insert("chealth_pat_elig", data);
        const res = await t.commit();
        if (res.error) {
            return this.prettyError(ctx, `Save Error: ${res.error}`, data);
        }
        t.init();
        const apiData = this.mapData(data);
        const adminResponse = await this.adminCH(ctx, apiData);
        const result = await this.processResponse(t, adminResponse, data);
        if (result.error) {
            return result;
        }
        const tresult = await t.commit();
        if (tresult.error) {
            return { error: true, message: `Save Error: ${tresult.message}` };
        }
        return result;
    }

    async updateInsuranceRecord(transaction, response, data) {
        // if file hash exists, update patient_insurance
        const resBody = {
            success: true,
            message:
                "270 eligibility verification request submitted successfully",
        };
        const lastSentDate = moment().format("YYYY-MM-DD HH:mm:ss");
        const reqID = response.request_json_data.clara_claim_uuid;
        const patInsuranceData = {
            last_verification_req_id: reqID,
            last_verification_sent_date: lastSentDate,
        };
        data["last_verification_req_id"] = reqID;
        data["last_verification_sent_date"] = lastSentDate;
        data["response_json_data"] = response.response_json_data;
        await transaction.insert("chealth_pat_elig", data);
        await transaction.update(
            "patient_insurance",
            patInsuranceData,
            data.patient_insurance_id
        );
        return resBody;
    }
    async adminCH(ctx, data) {
        const successReq = {
            _meta: {
                clara_mrn: "12345",
                medical_claims_processor: "change",
                is_test: true,
                clara_claim_uuid: uuid(),
            },
            controlNumber: "396602647",
            subscriber: {
                memberId: "**********",
                firstName: "johnone",
                lastName: "doeOne",
                dateOfBirth: "19800101",
                gender: "M",
                address: {
                    address1: "**********",
                    city: "Anytown",
                    state: "st",
                    postalCode: "12345",
                },
            },
            submitterTransactionIdentifier: "**********",
            tradingPartnerServiceId: "**********",
            tradingPartnerName: "Change",
            provider: {
                organizationName: "HAPPY DOCTORS GROUP",
            },
        };
        console.log(" successReq ==== ", successReq);
        const adminResponse = await this.fx.adminRequest(
            ctx,
            this.adminRequestURL + "/eligibility",
            {
                method: "POST",
                data: data,
                raw: true,
            }
        );
        return adminResponse;
    }

    async process(ctx) {
        let data = ctx.request.body;
        if (_.isEmpty(data)) {
            throw new Error("No data provided");
        }
        try {
            data = this.verifyRequired(data);
            if (data.error) {
                throw new Error(data.message);
            }
            const result = await this.proxyPverify(ctx, data);
            if (result.error) {
                await this.prettyError(ctx, result.message, data);
                return;
            }
        } catch (e) {
            console.error(e);
            ctx.type = "application/json";
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
            } else {
                ctx.status = 400;
                ctx.body = {
                    error: e.message,
                };
            }
        }
    }
};
