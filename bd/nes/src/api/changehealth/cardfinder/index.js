"use strict";
const _ = require("lodash");
const uuid = require("uuid").v4;
const DispenseFetcherClass = require("@dispense/fetcher");

module.exports = class CHealthClass {
    constructor(nes) {
        this.auth = nes.modules.auth;
        this.db = nes.modules.db;
        this.adminRequestURL = "/claims/med";
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.nes = nes;
    }

    checkParams(params) {
        if (!params.patient_id) {
            throw new Error("Patient ID is required");
        }
    }

    async getPatient(ctx, pid) {
        const filters = [`id:${pid}`];
        const patientRec = _.head(
            await this.form.get.get_form(ctx, ctx.user, "patient", {
                filter: filters,
            })
        );
        if (!patientRec) {
            throw new Error("Patient not found for ID: " + pid);
        }
        return patientRec;
    }
    async getPresets(ctx, pid) {
        const fetcher = new DispenseFetcherClass(this.nes, ctx);
        const patientRec = await this.getPatient(ctx, pid);
        const patientAddress = await fetcher.fetchPatientHomeAddress(pid);
        const internalID = uuid(); // this is generated here to store the uuid before we send request for tracking
        const preset = {
            patient_id: patientRec.id,
            subscriber_first_name: patientRec.firstname,
            subscriber_last_name: patientRec.lastname,
            subscriber_dob: patientRec.dob,
            subscriber_gender: patientRec.gender,
            internal_id: internalID,
            clara_mrn: patientRec.mrn,
        };
        if (patientAddress) preset.subscriber_zip = patientAddress.zip;
        if (patientRec.ssn) preset.subscriber_ssn = patientRec.ssn;
        return preset;
    }

    async requestCardFinder(ctx, data) {
        const NCPDPGeneratorClass = require("@billing/pharmacy/generator");
        const ncpdpGenerator = new NCPDPGeneratorClass(this.nes, ctx);
        const patientRec = await this.getPatient(ctx, data.patient_id);
        const responseData = await ncpdpGenerator.generateCardFinder(
            patientRec.id,
            patientRec.site_id,
            data
        );
        return responseData;
    }

    async checkCardFinderParams(data) {
        if (!data.patient_id) {
            throw new Error("Patient ID is required");
        }
    }

    async process(ctx) {
        try {
            if (ctx.request.method === "GET") {
                const params = ctx.request.query;
                this.checkParams(params);
                const pid = params.patient_id;
                ctx.body = await this.getPresets(ctx, pid);
            } else if (ctx.request.method === "POST") {
                const data = ctx.request.body;
                this.checkCardFinderParams(data);
                ctx.body = await this.requestCardFinder(ctx, data);
                ctx.status = 200;
            }
        } catch (e) {
            console.error(e);
            ctx.type = "application/json";
            if (e.status == 403) {
                ctx.status = e.status;
                ctx.body = {
                    error: "Access denied.",
                };
            } else {
                ctx.status = 400;
                ctx.body = {
                    error: e.message,
                };
            }
        }
    }
};
