const Joi = require("joi");

const FieldTypes = {
    DIRECT: "direct",
    OBJECT: "object",
    TEMPLATE: "template",
    MAP: "map",
    FUNCTION: "function",
};

const mappingSchema = Joi.object({
    type: Joi.string()
        .valid(
            FieldTypes.DIRECT,
            FieldTypes.OBJECT,
            FieldTypes.FUNCTION,
            FieldTypes.MAP,
            FieldTypes.TEMPLATE
        )
        .optional()
        .description("Type of field"),
    condition: Joi.string()
        .optional()
        .description(
            "Condition path, value is required in condition location for parsing to continue"
        ),
    fields: Joi.object()
        .optional()
        .description(
            "Fields object for an object field type that will get populated and rolled up to parent field"
        ),
    context: Joi.alternatives()
        .try(Joi.object(), Joi.array())
        .optional()
        .description("Context path object or array used in template strings"),
    no_context: Joi.boolean()
        .optional()
        .description("Context path object or array used in template strings"),
    path: Joi.alternatives()
        .try(Joi.object(), Joi.string(), Joi.array())
        .optional()
        .description("Path used to find the value"),
    path_raw: Joi.string()
        .optional()
        .description(
            "Raw path used to extract sub-objects from the base record. Used in functions and repeating loops."
        ),
    comment: Joi.string()
        .optional()
        .description(
            "Description of the field that prints out for debugging purposes."
        ),
    format: Joi.string()
        .optional()
        .description("Format string for the nunjucks template"),
    value: Joi.string().optional().description("Static string value"),
    default_value: Joi.string()
        .allow("")
        .optional()
        .description("Fall-through default value used in mapping"),
    map: Joi.object()
        .optional()
        .description("Key-value pair used to mapped field values"),
    function: Joi.string()
        .optional()
        .description(
            "Function name that is used in combination with the params. Calls the function to calculate the value in the mapper subclass"
        ),
    params: Joi.object()
        .optional()
        .description(
            "Parameters for the function. Key-value pairs of field name and path to fetch the values. Passed into function as an object."
        ),
    repeat: Joi.object()
        .optional()
        .description(
            "Path for repeating object. Path must return an array for repeating to occur. Only used in object field types."
        ),
}).required();

module.exports = {
    FieldTypes,
    mappingSchema,
};
