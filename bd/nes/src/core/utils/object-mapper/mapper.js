const moment = require("moment");
const _ = require("lodash");
const currency = require("currency.js");

const { flatten } = require("flat");
const Joi = require("joi");
const nunjucks = require("nunjucks");

const { ModuleLogger } = require("../../logger");
const { FieldTypes, mappingSchema } = require("./schema");

/**
 * @class MapperClass
 * @classdesc This class can be used to transform a record object into another object based on a mapping schema.
 * Please see schema.js in the included directory for the format or where it is used for medical claims to map to the 1500 form
 */
module.exports = class MapperClass extends ModuleLogger {
    constructor(nes, ctx) {
        super("object-mapper");
        this.shared = nes.shared;
        this.nes = nes;
        this.ctx = ctx;
        this.flatRecord = null;
        this.recordObj = null;
        this.fx = nes.modules.fx;

        this.nunjucks = new nunjucks.Environment({ autoescape: false });
    }

    /**
     * Converts a record object to an outbound object based on a given mapping.
     * @async
     * @param {Object} recordObj - The input record object to be converted.
     * @param {Object} outboundObj - The output object to store the converted data.
     * @param {Object} map - The mapping object that defines how to convert the record.
     * @returns {Promise<Object>} The converted outbound object.
     * @throws {Error} If an error occurs during the conversion process.
     */
    async convert(recordObj, outboundObj, map) {
        try {
            await Promise.all([
                this.fx.validateSchema(
                    "recordObj",
                    Joi.object().required(),
                    recordObj,
                    "Missing/Invalid record object to convert items."
                ),
                this.fx.validateSchema(
                    "outboundObj",
                    Joi.object().required(),
                    outboundObj,
                    "Missing/Invalid outbound object to convert items."
                ),
                this.fx.validateSchema(
                    "map",
                    Joi.object().required(),
                    map,
                    "Missing/Invalid map to convert items."
                ),
            ]);

            await this.addHelpers();
            this.recordObj = recordObj;
            this.flatRecord = flatten(recordObj);

            const mappedResults = await Promise.all(
                Object.entries(map).map(async ([key, value]) => {
                    const mappedValue = await this.__handleField(value);
                    return { key, mappedValue };
                })
            );

            mappedResults.forEach(({ key, mappedValue }) => {
                if (mappedValue != null) {
                    _.set(outboundObj, key, mappedValue);
                }
            });

            return outboundObj;
        } catch (e) {
            super.error(
                `Exception encountered during object conversion. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(e, "Failed to convert object");
        }
    }

    /**
     * Adds template helpers to the Nunjucks environment.
     * This method sets up custom global functions and filters for use in templates.
     * @async
     * @returns {Promise<void>}
     * @throws {Error} If an error occurs while adding template helpers.
     */
    async addHelpers() {
        try {
            this.nunjucks.addGlobal("getCurrentDate", () =>
                moment().format("MM/DD/YYYY")
            );
            this.nunjucks.addFilter("convertToX", (value) =>
                value === "Yes" ? "X" : ""
            );
            this.nunjucks.addFilter("formatCurrency", (value) =>
                currency(value, {
                    separator: ",",
                    precision: 2,
                    symbol: "",
                }).format()
            );
            this.nunjucks.addFilter("formatUnits", (value, precision) =>
                parseFloat(parseFloat(value).toFixed(precision)).toString()
            );
        } catch (error) {
            super.error(
                `Exception encountered while adding template parsing helpers. Error: ${error.message} Stack: ${error.stack}`
            );
            throw this.fx.wrapError(
                error,
                "An error occurred while adding template parsing helpers"
            );
        }
    }

    /**
     * Handles a field object for mapping.
     * @async
     * @param {Object} obj - The field object to handle.
     * @param {Object|null} context - Optional context for the field.
     * @returns {Promise<*>} The processed field value.
     * @throws {Error} If an error occurs while handling the field.
     */
    async __handleField(obj, context = null) {
        try {
            await this.fx.validateSchema(
                "field object",
                mappingSchema,
                obj,
                "Missing/Invalid field object to convert items."
            );

            const {
                comment,
                condition,
                context: objContext,
                default_value,
                type = FieldTypes.DIRECT,
            } = obj;

            if (comment) {
                super.debug(`Comment: ${comment}`);
            }

            if (condition && !(await this.__checkCondition(condition))) {
                super.debug(`Failed condition check ${condition}`);
                return null;
            }

            const finalContext = objContext
                ? await this.__fetchPath(objContext)
                : context || null;

            switch (type) {
                case FieldTypes.DIRECT:
                    if (obj.value !== undefined) {
                        return obj.value;
                    } else if (obj.path) {
                        return (
                            (await this.__fetchPath(obj.path)) ?? default_value
                        );
                    }
                    break;
                case FieldTypes.TEMPLATE:
                    return (
                        (await this.__handleTemplate(obj, finalContext)) ??
                        default_value
                    );
                case FieldTypes.MAP:
                    return (await this.__handleMapType(obj)) ?? default_value;
                case FieldTypes.FUNCTION:
                    return (
                        (await this.__handleFunctionType(obj)) ?? default_value
                    );
                case FieldTypes.OBJECT:
                    return (await this.__handleObject(obj)) ?? default_value;
                default:
                    throw this.fx.getClaraError(
                        `Invalid field type for object ${JSON.stringify(obj)}`
                    );
            }

            return default_value;
        } catch (e) {
            super.error(
                `Exception encountered while handling object field type during mapping. Error:${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while handling object field type for mapping record`
            );
        }
    }

    /**
     * Handles the map type field in the object mapper.
     * @param {Object} obj - The object containing map type configuration.
     * @returns {Promise<*>} The mapped value or null if condition fails.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async __handleMapType(obj) {
        try {
            const schema = Joi.object({
                type: Joi.string().valid("map").required(),
                map: Joi.object().required(),
                path: Joi.alternatives()
                    .try(Joi.object(), Joi.string(), Joi.array())
                    .required(),
                default_value: Joi.any().optional(),
                condition: Joi.string().optional(),
                comment: Joi.string().optional(),
            }).required();

            await this.fx.validateSchema(
                "mapped field object",
                schema,
                obj,
                "Missing/Invalid mapped field object to convert items."
            );

            const { comment, condition, map, path, default_value } = obj;

            if (comment) {
                super.debug(`Comment: ${comment}`);
            }

            if (condition && !(await this.__checkCondition(condition))) {
                super.debug(`Failed condition check ${condition}`);
                return null;
            }

            const value = await this.__fetchPath(path);
            return map[value] ?? default_value ?? null;
        } catch (e) {
            super.error(
                `Exception encountered while handling mapped field type during mapping. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while handling mapped field type for mapping record`,
                e
            );
        }
    }

    /**
     * Handles the function type field in the object mapper.
     * @async
     * @param {Object} obj - The object containing function type configuration.
     * @returns {Promise<*>} The result of the function execution or null if condition fails.
     * @throws {Error} If an error occurs during the function handling process.
     */
    async __handleFunctionType(obj) {
        try {
            const schema = Joi.object({
                type: Joi.string().valid("function").required(),
                function: Joi.string().required(),
                params: Joi.object().required(),
                comment: Joi.string().optional(),
                condition: Joi.string().optional(),
            }).required();

            await this.fx.validateSchema(
                "function field object",
                schema,
                obj,
                "Missing/Invalid function field object to convert items."
            );

            const { comment, condition, function: funcName, params } = obj;

            if (comment) {
                super.debug(`Comment: ${comment}`);
            }

            if (condition && !(await this.__checkCondition(condition))) {
                super.debug(`Failed condition check ${condition}`);
                return null;
            }

            if (typeof this[funcName] !== "function") {
                const error = `Method ${funcName} does not exist.`;
                super.error(error);
                throw this.fx.getClaraError(error);
            }

            const processedParams = await this.__getFunctionParams(params);
            const returnVal = await this[funcName](processedParams);

            if (returnVal === undefined) {
                super.warn(
                    `Warning: ${funcName} on function type field returned undefined.`
                );
            }

            return returnVal;
        } catch (e) {
            super.error(
                `Exception encountered while handling function field type during mapping. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while handling function field type for mapping record`
            );
        }
    }

    /**
     * Fetches and processes function parameters.
     * @async
     * @param {Object} params - The parameters object to process.
     * @returns {Promise<Object>} An object containing processed parameters.
     * @throws {Error} If an error occurs during parameter processing.
     */
    async __getFunctionParams(params) {
        try {
            const schema = Joi.object().required();
            await this.fx.validateSchema(
                "function params object",
                schema,
                params,
                "Missing/Invalid function params object to convert items."
            );

            const paramsObj = await Object.entries(params).reduce(
                async (accPromise, [key, value]) => {
                    const acc = await accPromise;
                    acc[key] = (await this.__fetchPath(value)) ?? null;
                    return acc;
                },
                Promise.resolve({})
            );

            return paramsObj;
        } catch (e) {
            super.error(
                `Exception encountered while handling function params during mapping. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while handling function params for mapping record`
            );
        }
    }

    /**
     * Handles an object type field in the mapping process.
     * @async
     * @param {Object} obj - The object field to handle.
     * @returns {Promise<Object|null>} The processed object or null if conditions are not met.
     * @throws {Error} If an error occurs during object handling.
     */
    async __handleObject(obj) {
        try {
            const schema = Joi.object({
                type: Joi.string().valid("object").required(),
                condition: Joi.string().optional(),
                fields: Joi.object().required(),
                context: Joi.alternatives()
                    .try(Joi.object(), Joi.array())
                    .optional(),
                comment: Joi.string().optional(),
                repeat: Joi.object().optional(),
            }).required();

            await this.fx.validateSchema(
                "object field",
                schema,
                obj,
                "Missing/Invalid object field to convert items."
            );

            if (obj.comment) {
                super.debug(`Comment: ${obj.comment}`);
            }

            if (
                obj.condition &&
                !(await this.__checkCondition(obj.condition))
            ) {
                super.debug(`Failed condition check ${obj.condition}`);
                return null;
            }

            const context = obj.context
                ? await this.__fetchPath(obj.context)
                : null;

            if (obj.repeat) {
                const repeatVal = await this.__fetchPath(obj.repeat);
                if (!Array.isArray(repeatVal)) {
                    throw new Error("Repeat value must be an array");
                }
                return Object.fromEntries(
                    await Promise.all(
                        repeatVal.map(async (_, idx) => [
                            String(idx),
                            await this.__fetchObjectFields(
                                obj.fields,
                                context,
                                idx
                            ),
                        ])
                    )
                );
            }

            return this.__fetchObjectFields(obj.fields, context);
        } catch (e) {
            super.error(
                `Exception encountered while handling object field type during mapping. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while handling object field type for mapping record`
            );
        }
    }

    /**
     * Fetches and processes object fields.
     * @async
     * @param {Object} fields - The fields to fetch and process.
     * @param {Object|null} [context=null] - Optional context for field processing.
     * @param {number|null} [index=null] - Optional index for repeated fields.
     * @returns {Promise<Object>} An object containing the processed field values.
     */
    async __fetchObjectFields(fields, context = null, index = null) {
        try {
            const finalFieldsObj =
                index !== null
                    ? await this.__mapFieldsObjWithIndex(fields, index)
                    : fields;
            const objectFields = {};

            await Promise.all(
                Object.entries(finalFieldsObj).map(async ([key, value]) => {
                    objectFields[key] = await this.__handleField(
                        value,
                        context
                    );
                })
            );

            return objectFields;
        } catch (e) {
            super.error(
                `Exception encountered while handling object fields. Error: ${e.message} Stack: ${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                "An error occurred while handling object fields for mapping record"
            );
        }
    }

    /**
     * Handles template type fields.
     * @async
     * @param {Object} obj - The template object to handle.
     * @param {Object|null} [context=null] - Optional context for the template.
     * @returns {Promise<string>} The rendered template string.
     * @throws {Error} If an error occurs while handling the template.
     */
    async __handleTemplate(obj, context = null) {
        try {
            const schema = Joi.object({
                type: Joi.string().valid("template").required(),
                format: Joi.string().required(),
                context: Joi.alternatives()
                    .try(Joi.object(), Joi.array())
                    .optional(),
                no_context: Joi.boolean().optional(),
                comment: Joi.string().optional(),
                passthrough_context: Joi.object().optional(),
            })
                .required()
                .or("context", "no_context", "passthrough_context")
                .without("no_context", ["context", "passthrough_context"])
                .without("context", ["no_context"])
                .without("passthrough_context", ["no_context"]);

            const objCopy = _.cloneDeep(obj);
            if (context) {
                objCopy.passthrough_context = context;
            }

            await this.fx.validateSchema(
                "template field",
                schema,
                objCopy,
                "Missing/Invalid template field to convert items."
            );

            if (obj.comment) {
                super.debug(`Comment: ${obj.comment}`);
            }

            const template = obj.format;
            let renderContext = context || {};

            if (!context && obj.context) {
                renderContext = (await this.__fetchPath(obj.context)) || {};
                if (
                    !renderContext ||
                    (typeof renderContext === "string" &&
                        renderContext.length === 0)
                ) {
                    return "";
                }
            }

            try {
                return this.nunjucks.renderString(template, renderContext);
            } catch (e) {
                super.error(
                    `Exception encountered mapping ${template} with context:${JSON.stringify(renderContext)} Error:${e.message} Stack:${e.stack}`
                );
                throw this.fx.wrapError(
                    e,
                    `An issue occurred while attempting to map a template field `
                );
            }
        } catch (e) {
            super.error(
                `Exception encountered while handling template field type during mapping. Error:${e.message} Stack:${e.stack}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while handling template field type for mapping record`
            );
        }
    }

    /**
     * Fetches the value at the specified path in the record object.
     * @async
     * @param {string|Array|Object} path - The path to fetch. Can be a string, array, or object with specific properties.
     * @returns {Promise<*>} The value at the specified path.
     * @throws {Error} If an error occurs while fetching the path.
     */
    async __fetchPath(path) {
        try {
            await this.__validatePath(path);

            if (typeof path === "string") {
                return await this.__handleStringPath(path);
            }

            if (Array.isArray(path)) {
                return await this.__handleArrayPath(path);
            }

            if (typeof path !== "object" || path === null) {
                throw new Error("Invalid path type");
            }

            const { condition, value, path_raw, path: subPath } = path;

            if (condition && !(await this.__checkCondition(condition))) {
                super.debug(`Failed condition check ${condition}`);
                return "";
            }

            if (value !== undefined) {
                return this.__handleValuePath(value);
            }

            if (path_raw) {
                return _.get(this.recordObj, path_raw, null);
            }

            if (subPath) {
                return this.__fetchPath(subPath);
            }

            return this.__handleFieldsPath(path);
        } catch (e) {
            return this.__handleFetchPathError(e, path);
        }
    }

    /**
     * Validates the given path using a Joi schema.
     * @async
     * @param {string|Array|Object} path - The path to validate.
     * @throws {Error} If the path fails validation.
     */
    async __validatePath(path) {
        const schema = Joi.alternatives()
            .try(
                Joi.object({
                    condition: Joi.string().optional(),
                    value: Joi.alternatives()
                        .try(Joi.object(), Joi.string())
                        .optional(),
                    path: Joi.alternatives()
                        .try(Joi.object(), Joi.string(), Joi.array())
                        .optional(),
                    path_raw: Joi.string().optional(),
                    fields: Joi.object().optional(),
                }).xor("fields", "path", "value", "path_raw"),
                Joi.array().items(
                    Joi.alternatives().try(Joi.string(), Joi.object())
                ),
                Joi.string()
            )
            .required();

        try {
            await this.fx.validateSchema(
                "path",
                schema,
                path,
                "Missing/Invalid path to convert items."
            );
        } catch (error) {
            console.error(`Path Validation failed on Path ${path}`);
            throw new Error(`Path validation failed: ${error.message}`);
        }
    }

    /**
     * Handles a string path by retrieving the corresponding value from the flat record.
     * @param {string} path - The string path to handle.
     * @returns {string} The value associated with the path in the flat record, or an empty string if not found.
     * @private
     */
    async __handleStringPath(path) {
        const val = this.flatRecord[path] || null;
        return val;
    }

    /**
     * Handles an array path by iterating through its items and fetching the first non-empty value.
     * @async
     * @param {Array} path - The array path to handle.
     * @returns {Promise<string>} The first non-empty value found in the path array, or an empty string if none found.
     * @private
     */
    async __handleArrayPath(path) {
        if (!Array.isArray(path)) {
            throw new TypeError("Path must be an array");
        }

        for (const item of path) {
            try {
                const value = await this.__fetchPath(item);
                if (value !== null && value !== undefined && value !== "") {
                    return value;
                }
            } catch (error) {
                super.warn(`Error fetching path item: ${error.message}`);
            }
        }

        super.debug(`No valid values found in path array`);
        return "";
    }

    /**
     * Handles a value path by processing string or object values.
     * @async
     * @param {string|object} value - The value to handle.
     * @returns {Promise<string|object>} The processed value or an empty string if invalid.
     * @private
     */
    async __handleValuePath(value) {
        if (typeof value === "string") {
            return value;
        } else if (value !== null && typeof value === "object") {
            return await this.__fetchPath(value);
        }
        super.debug(`Invalid value type, returning empty string`);
        return "";
    }

    /**
     * Handles a fields path by processing each field in the path object.
     * @async
     * @param {Object} path - The path object containing fields to process.
     * @returns {Promise<Object>} An object with processed field values.
     * @private
     */
    async __handleFieldsPath(path) {
        const { fields } = path;

        if (!fields || typeof fields !== "object") {
            const errorMessage = `Path type of object must have fields defined. Path: ${JSON.stringify(path)}`;
            super.error(errorMessage);
            throw this.fx.getClaraError(errorMessage);
        }

        const contextObj = {};
        const entries = Object.entries(fields);

        await Promise.all(
            entries.map(async ([key, value]) => {
                contextObj[key] = await this.__fetchPath(value);
            })
        );

        return contextObj;
    }

    /**
     * Handles errors that occur during fetch path operations.
     * @param {Error} e - The error object caught during the fetch operation.
     * @param {string|object} path - The path that was being fetched when the error occurred.
     * @private
     * @throws {Error} Wrapped error with additional context.
     */
    __handleFetchPathError(e, path) {
        super.error(
            `Exception encountered while fetching mapping path ${path}. Error:${e.message} Stack:${e.stack}`
        );
        throw this.fx.wrapError(
            e,
            `An error occurred while fetching mapping path for mapping record`
        );
    }

    /**
     * Checks a condition against the flatRecord.
     * @async
     * @param {string} condition - The condition to check.
     * @returns {Promise<boolean>} True if the condition is met, false otherwise.
     * @private
     */
    async __checkCondition(condition) {
        try {
            if (
                !Object.prototype.hasOwnProperty.call(
                    this.flatRecord,
                    condition
                )
            ) {
                return false;
            }
            const data = this.flatRecord[condition];
            return (
                data !== null &&
                !(typeof data === "string" && data.length === 0)
            );
        } catch (e) {
            super.error(
                `Exception encountered while checking condition: ${e.message}`
            );
            throw this.fx.wrapError(
                e,
                "An error occurred while checking a condition for mapping record"
            );
        }
    }

    /**
     * Maps an index into field paths of an object.
     * @async
     * @param {Object} fields - The object containing field paths.
     * @param {number} index - The index to be mapped into the field paths.
     * @returns {Promise<void>}
     * @private
     */
    async __mapFieldsObjWithIndex(fields, index) {
        try {
            await Promise.all([
                this.fx.validateSchema(
                    "fields",
                    Joi.object().required(),
                    fields,
                    "Missing/Invalid fields object to convert items."
                ),
                this.fx.validateSchema(
                    "index",
                    Joi.number().integer().required(),
                    index,
                    "Missing/Invalid index to convert items."
                ),
            ]);

            const replaceIndexInObject = (obj, index) => {
                for (const key in obj) {
                    if (typeof obj[key] === "string") {
                        obj[key] = obj[key].replace(/\{\{idx\}\}/g, index);
                    } else if (
                        typeof obj[key] === "object" &&
                        obj[key] !== null
                    ) {
                        replaceIndexInObject(obj[key], index);
                    }
                }
            };

            const deepFieldsCopy = _.cloneDeep(fields);
            replaceIndexInObject(deepFieldsCopy, index);

            return deepFieldsCopy;
        } catch (e) {
            super.error(
                `Exception encountered while mapping index into fields. Error: ${e.message}`
            );
            throw this.fx.wrapError(
                e,
                `An error occurred while mapping index into fields`
            );
        }
    }
};
