"use strict";

/**
 * This function is used to initialize a wrapper for a class.
 * It guarantees that init will be called before any other functions.
 * WARNING: If using make sure you are accessing through some function
 * first and not a property that depends on the init data. If you attempt
 * to access a getter before a function, init() would not have been called
 * @param {Object} target - The class to be wrapped
 * @returns {Proxy} - A proxy object with a construct trap
 * @public
 */
function initWrapper(Class) {
    return class extends Class {
        constructor(...args) {
            super(...args);
            this.initialized = false;
            return new Proxy(this, {
                get: (tgt, prop, receiver) => {
                    // Bypass proxy logic for Promise-specific properties
                    if (
                        prop === "then" ||
                        prop === "catch" ||
                        prop === "finally"
                    ) {
                        return Reflect.get(tgt, prop, receiver);
                    }

                    // Check if the property is a function and the instance is not initialized
                    if (!tgt.initialized && typeof tgt.init === "function") {
                        if (prop !== "init") {
                            return async (...methodArgs) => {
                                await tgt.init();
                                tgt.initialized = true;
                                const value = Reflect.get(tgt, prop, receiver);
                                if (typeof value === "function") {
                                    return value.apply(receiver, methodArgs);
                                }
                                return value;
                            };
                        }
                    }

                    const value = Reflect.get(tgt, prop, receiver);
                    if (typeof value === "function") {
                        return function (...methodArgs) {
                            return value.apply(receiver, methodArgs);
                        };
                    }
                    return value;
                },
            });
        }
    };
}

module.exports = {
    initWrapper,
};
