"use strict";
const _ = require("lodash");

const { ModuleLogger } = require("@core/logger");

/**
 * @class
 * @classdesc Class for mapping errors from AJV schema errors.
 * @extends ModuleLogger
 */
module.exports = class SchemaErrorMapperClass extends ModuleLogger {
    constructor(nes, loggerName) {
        super(loggerName || "SchemaErrorMapper");
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.schemaMapping = {};
    }

    /**
     * Maps schema errors to their corresponding fields in the DSL structure.
     *
     * @async
     * @param {Object} schemaErrors - The schema errors object to be mapped.
     * @returns {Promise<Object>} A promise that resolves to the mapped DSL error object.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async mapSchemaErrors(schemaErrors) {
        super.log(`Mapping ajv schema errors...`);
        try {
            const result = {};

            const mappedErrors = await Promise.all(
                Object.entries(schemaErrors).map(
                    async ([schemaPath, error]) => {
                        if (!error) {
                            super.error(
                                `Unknown schema error at path: ${JSON.stringify(schemaPath)}`
                            );
                            return;
                        }
                        const mappedField =
                            await this.__getErrorPathForSchemaError(schemaPath);
                        if (mappedField) {
                            return { path: mappedField, error: error };
                        } else {
                            super.warn(
                                `Unmapped ajv schema for path ${schemaPath} error: ${JSON.stringify(
                                    error
                                )}`
                            );
                            return { path: "form", error: error };
                        }
                    }
                )
            );
            for (const mappedError of mappedErrors) {
                const path = mappedError.path;
                const error = mappedError.error;
                if (path === "form") {
                    if (!result?.form) result.form = [];
                    result.form.push(error);
                } else {
                    const existingError = _.get(result, path);
                    _.set(
                        result,
                        path,
                        existingError ? `${existingError}, ${error}` : error
                    );
                }
            }
            return result;
        } catch (error) {
            super.error(
                `Error mapping ajv schema errors: ${error}, errors: ${JSON.stringify(
                    schemaErrors
                )}`
            );
            throw error;
        }
    }

    /**
     * Retrieves the error path for a given schema error path.
     *
     * @async
     * @param {string} schemaErrorPath - The schema error path to process.
     * @returns {Promise<string|null>} The corresponding error path or null if not found.
     * @throws {Error} If an error occurs while processing the schema error path.
     */
    async __getErrorPathForSchemaError(schemaErrorPath) {
        super.debug(`Getting error path for schema error: ${schemaErrorPath}`);
        try {
            const jsonPath = schemaErrorPath.replace(/\.(\d+)\./g, "[$1].");
            if (this.schemaMapping[jsonPath]) {
                return this.schemaMapping[jsonPath];
            }

            const wildcardPath = jsonPath.replace(/\[\d+\]/g, "[*]");
            const matchingPaths = Object.keys(this.schemaMapping).filter(
                (key) => key.replace(/\[\d+\]/g, "[*]") === wildcardPath
            );

            if (matchingPaths.length === 1) {
                let matchingPath = this.schemaMapping[matchingPaths[0]];
                const arrayIndexes = jsonPath.match(/\[(\d+)\]/g) || [];
                arrayIndexes.forEach((index, _) => {
                    matchingPath = matchingPath.replace(
                        "{idx}",
                        index.slice(1, -1)
                    );
                });
                return matchingPath;
            }

            return null;
        } catch (error) {
            super.error(
                `Error getting error path for schema error: ${error}, schema error path: ${schemaErrorPath}`
            );
            throw error;
        }
    }
};
