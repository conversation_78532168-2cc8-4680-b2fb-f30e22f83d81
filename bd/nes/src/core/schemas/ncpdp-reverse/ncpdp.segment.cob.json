{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp-reverse.segment.cob.json", "title": "NCPDP Claim COB Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_cob", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "05", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "paid_amount_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_paid"}, "$ref": "ncpdp.fields.json#/$defs/341-HB"}, "subform_opayer": {"type": "array", "maxItems": 9, "items": {"type": "object", "additionalProperties": false, "properties": {"other_coverage_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/338-5C"}, {"$ref": "ncpdp.ecl.json#/$defs/338-5C"}], "$comment": "payer.ncpdp_sec_plan_type_id"}}, "required": ["other_coverage_type"], "errorMessage": {"required": "Other payer coverage type is required for COB claims."}}}}}