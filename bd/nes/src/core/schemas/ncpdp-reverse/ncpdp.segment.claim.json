{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp-reverse.segment.claim.json", "title": "NCPDP Claim Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_claim", "properties": {"isInlineSubform": true, "transaction_code": {"type": "string", "default": "B2", "removeProperty": true}, "segment_identification": {"type": "string", "default": "07", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "rx_svc_no_ref_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/455-EM"}, {"$ref": "ncpdp.ecl.json#/$defs/455-EM"}]}, "rx_svc_no": {"$ref": "ncpdp.fields.json#/$defs/402-D2"}, "prod_svc_id_qualifier": {"type": "string", "default": "03", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/436-E1"}, {"$ref": "ncpdp.ecl.json#/$defs/436-E1"}]}, "prod_svc_id": {"$ref": "ncpdp.fields.json#/$defs/407-D7"}, "fill_number": {"$ref": "ncpdp.fields.json#/$defs/403-D3"}, "other_coverage_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/308-C8"}, {"$ref": "ncpdp.ecl.json#/$defs/308-C8"}]}, "pharmacy_service_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/147-U7"}, {"$ref": "ncpdp.ecl.json#/$defs/147-U7"}], "$comment": "payer.default_service_id"}}, "required": ["rx_svc_no_ref_qualifier", "rx_svc_no", "prod_svc_id_qualifier", "prod_svc_id"], "errorMessage": {"required": "For claim segment, the following fields are required: Segment Identifier (111-AM), Transaction Code (103-A3), Service Reference Number Qualifier (455-EM), Service Reference Number (402-D2), Product Service ID Qualifier (436-E1), Product Service ID (407-D7)"}}