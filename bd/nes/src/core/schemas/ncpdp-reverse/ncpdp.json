{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp-reverse.json", "title": "NCPDP <PERSON>", "description": "An NCPDP Claim Form Schema", "type": "object", "additionalProperties": false, "$comment": "ncpdp", "properties": {"bin_number": {"type": "string", "$ref": "ncpdp.fields.json#/$defs/101-A1", "$comment": "payer.bin"}, "version_number": {"type": "string", "default": "D0", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/102-A2"}, {"$ref": "ncpdp.ecl.json#/$defs/102-A2"}]}, "transaction_code": {"type": "string", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/103-A3"}, {"$ref": "ncpdp.ecl.json#/$defs/103-A3"}]}, "process_control_number": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/104-A4"}, {"$ref": "ncpdp.fields.json#/$defs/pcn"}], "$comment": "payer.pcn"}, "transaction_count": {"type": "number", "default": 1, "allOf": [{"$ref": "ncpdp.fields.json#/$defs/109-A9"}, {"$ref": "ncpdp.ecl.json#/$defs/109-A9"}]}, "svc_prov_id_qualifier": {"type": "string", "default": "11", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/202-B2"}, {"$ref": "ncpdp.ecl.json#/$defs/202-B2"}]}, "svc_prov_id": {"$ref": "ncpdp.fields.json#/$defs/201-B1"}, "date_of_service": {"$ref": "ncpdp.fields.json#/$defs/401-D1"}, "software_vendor_id": {"$ref": "ncpdp.fields.json#/$defs/110-AK"}, "segment_insurance": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp-reverse.segment.insurance.json"}}, "segment_cob": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp-reverse.segment.cob.json"}}, "segment_claim": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp-reverse.segment.claim.json"}}, "segment_pricing": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp-reverse.segment.pricing.json"}}}, "required": ["bin_number", "process_control_number", "svc_prov_id_qualifier", "svc_prov_id", "date_of_service", "segment_insurance"], "errorMessage": {"required": "The following are required for all NCPDP transactions: BIN Number (101-A1), PCN (104-A4), Service Provider ID Qualifier (202-B2), Service Provider ID (202-B2), Date of Service (401-D1), and Insurance Segment"}}