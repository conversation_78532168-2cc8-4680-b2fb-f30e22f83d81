{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp-reverse.segment.pricing.json", "title": "NCPDP Claim Pricing Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_pricing", "properties": {"isInlineSubform": true, "transaction_code": {"type": "string", "default": "B2", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/103-A3"}, {"$ref": "ncpdp.ecl.json#/$defs/103-A3"}, {"removeProperty": true}]}, "segment_identification": {"type": "string", "default": "11", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "incv_amt_sub": {"$ref": "ncpdp.fields.json#/$defs/438-E3"}, "gross_amount_due": {"$ref": "ncpdp.fields.json#/$defs/430-DU"}}, "required": ["segment_identification", "transaction_code"], "errorMessage": {"required": "Segment Identifier (111-AM) and Transaction Code (103-A3) are required for pricing segment."}}