"use strict";
const { Schema } = require("../index");

module.exports = class SchemaValidatorClass extends Schema {
    constructor() {
        super();
        this.keyword = "ncpdp-reverse";
    }

    async init() {
        return this;
    }

    async getCompiledSchemas(ajv) {
        // Primary NCPDP Schemas
        const claimsSchema = require("./ncpdp.segment.claim.json");
        const cobSchema = require("./ncpdp.segment.cob.json");
        const insuranceSchema = require("./ncpdp.segment.insurance.json");
        const pricingSchema = require("./ncpdp.segment.pricing.json");

        // Register primary Schemas
        ajv.addSchema(claimsSchema, "ncpdp-reverse.segment.claim.json");
        ajv.addSchema(cobSchema, "ncpdp-reverse.segment.cob.json");
        ajv.addSchema(insuranceSchema, "ncpdp-reverse.segment.insurance.json");
        ajv.addSchema(pricingSchema, "ncpdp-reverse.segment.pricing.json");

        const ncpdpSchema = require("./ncpdp.json");
        return ajv.compile(ncpdpSchema);
    }

    async addCustomKeywords(ajv) {
        return;
    }

    async addCustomFormats(ajv) {
        return;
    }
};
