{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp-reverse.segment.insurance.json", "title": "NCPDP Claim Insurance Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_insurance", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "04", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "card_holder_id": {"$ref": "ncpdp.fields.json#/$defs/302-C2"}, "group_id": {"$ref": "ncpdp.fields.json#/$defs/301-C1"}, "medigap_id": {"$ref": "ncpdp.fields.json#/$defs/359-2A"}, "mcd_id_no": {"$ref": "ncpdp.fields.json#/$defs/115-N5"}}, "required": ["segment_identification", "card_holder_id"], "errorMessage": {"required": "Cardholder ID is required"}}