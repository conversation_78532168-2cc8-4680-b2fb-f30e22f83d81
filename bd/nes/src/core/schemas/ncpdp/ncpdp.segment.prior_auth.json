{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.prior_auth.json", "title": "NCPDP Claim PA Segment", "type": "object", "additionalProperties": false, "properties": {"segment_identification": {"type": "string", "default": "12", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "request_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/498-PA"}, {"$ref": "ncpdp.ecl.json#/$defs/498-PA"}]}, "request_period_date_begin": {"$ref": "ncpdp.fields.json#/$defs/498-PB"}, "request_period_date_end": {"$ref": "ncpdp.fields.json#/$defs/498-PC"}, "basis_of_request": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/498-PD"}, {"$ref": "ncpdp.ecl.json#/$defs/498-PD"}]}, "authorized_representative_first_name": {"$ref": "ncpdp.fields.json#/$defs/498-PE"}, "authorized_rep_last_name": {"$ref": "ncpdp.fields.json#/$defs/498-PF"}, "authorized_rep_street_address": {"$ref": "ncpdp.fields.json#/$defs/498-PG"}, "authorized_rep_city": {"$ref": "ncpdp.fields.json#/$defs/498-PH"}, "authorized_rep_state_province": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/498-PJ"}, {"$ref": "ncpdp.ecl.json#/$defs/498-PJ"}]}, "authorized_rep_zip_postal_code": {"$ref": "ncpdp.fields.json#/$defs/498-PK"}, "prior_authorization_number_assigned": {"$ref": "ncpdp.fields.json#/$defs/498-PY"}, "authorization_number": {"$ref": "ncpdp.fields.json#/$defs/503-F3"}, "prior_authorization_supporting_documentation": {"$ref": "ncpdp.fields.json#/$defs/498-PP"}}}