{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.insurance.json", "title": "NCPDP Claim Insurance Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_insurance", "properties": {"isInlineSubform": true, "payer_type_id": {"type": "string", "allOf": [{"removeProperty": true}], "$comment": "payer.type_id"}, "segment_identification": {"type": "string", "default": "04", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "card_holder_id": {"$ref": "ncpdp.fields.json#/$defs/302-C2"}, "card_holder_first_name": {"$ref": "ncpdp.fields.json#/$defs/312-CC"}, "card_holder_last_name": {"$ref": "ncpdp.fields.json#/$defs/313-CD"}, "plan_id": {"$ref": "ncpdp.fields.json#/$defs/524-FO"}, "elig_clar_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/309-C9"}, {"$ref": "ncpdp.ecl.json#/$defs/309-C9"}]}, "group_id": {"$ref": "ncpdp.fields.json#/$defs/301-C1"}, "person_code": {"$ref": "ncpdp.fields.json#/$defs/303-C3"}, "pt_rel_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/306-C6"}, {"$ref": "ncpdp.ecl.json#/$defs/306-C6"}]}, "medigap_id": {"$ref": "ncpdp.fields.json#/$defs/359-2A"}, "home_plan": {"$ref": "ncpdp.fields.json#/$defs/314-CE"}, "mcd_indicator": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/360-2B"}, {"$ref": "ncpdp.ecl.json#/$defs/360-2B"}]}, "dr_accept_indicator": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/361-2D"}, {"$ref": "ncpdp.ecl.json#/$defs/361-2D"}]}, "partd_facility": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/997-G2"}, {"$ref": "ncpdp.ecl.json#/$defs/997-G2"}]}, "mcd_id_no": {"$ref": "ncpdp.fields.json#/$defs/115-N5"}}, "required": ["segment_identification", "card_holder_id"], "errorMessage": {"required": "Cardholder ID is required"}, "allOf": [{"if": {"properties": {"payer_type_id": {"const": "MCRD"}}}, "then": {"properties": {"card_holder_id": {"anyOf": [{"$ref": "ncpdp.insurance_ids.json#/$defs/hicn"}, {"$ref": "ncpdp.insurance_ids.json#/$defs/last4"}, {"$ref": "ncpdp.insurance_ids.json#/$defs/ssn"}, {"$ref": "ncpdp.insurance_ids.json#/$defs/rrb"}]}}}, "errorMessage": {"then": "The Cardholder ID for Medicare Part D must be one of the following: HICN, last 4 digits of SSN, full SSN, or RRB number."}}]}