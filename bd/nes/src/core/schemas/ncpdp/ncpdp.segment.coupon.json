{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.coupon.json", "title": "NCPDP Claim Coupon Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_coupon", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "09", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "coupon_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/485-KE"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/485-KE"}]}, "coupon_number": {"$ref": "ncpdp.fields.json#/$defs/486-ME"}, "coupon_value_amount": {"$ref": "ncpdp.fields.json#/$defs/487-NE"}}, "required": ["coupon_type", "coupon_number", "segment_identification"], "errorMessage": {"required": "The following fields are required: Coupon Type (485-KE), Coupon Number (486-ME)"}}