{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.insurance_ids.json", "title": "NCPDP Insurance ID Formats", "description": "An NCPDP Insurance ID Type Schema", "$defs": {"hicn": {"pattern": "^[A-Za-z0-9]{9,13}$", "description": "Medicare Number, 9-13 digit alphanumeric", "errorMessage": {"pattern": "Field must be a a valid Medicare Number, 9-13 digit alphanumeric"}}, "last4": {"pattern": "^\\d{4}$", "description": "Last 4 of SSN", "errorMessage": {"pattern": "Field must be the last 4 of the SSN"}}, "ssn": {"pattern": "^\\d{9}", "description": "Social Security Number, must be 9 digits", "errorMessage": {"pattern": "Field must be a valid Social Security Number, must be 9 digits"}}, "rrb": {"pattern": "^([A-Z]{1,2}\\d{9}|\\d{9}[A-Z]?)$", "description": "Railroad Retirement Board (RRB) Number, must be 1-2 alphanumeric prefix, 9 digits, and an optional suffix", "errorMessage": {"pattern": "Field must be a valid Railroad Retirement Board (RRB) Number, 1-2 alphanumeric prefix, 9 digits, and an optional suffix"}}}}