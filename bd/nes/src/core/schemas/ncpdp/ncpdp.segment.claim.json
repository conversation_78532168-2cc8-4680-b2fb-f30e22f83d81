{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.claim.json", "title": "NCPDP Claim Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_claim", "properties": {"isInlineSubform": true, "transaction_code": {"type": "string", "default": "B1", "removeProperty": true}, "segment_identification": {"type": "string", "default": "07", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "rx_svc_no_ref_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/455-EM"}, {"$ref": "ncpdp.ecl.json#/$defs/455-EM"}]}, "rx_svc_no": {"$ref": "ncpdp.fields.json#/$defs/402-D2"}, "prod_svc_id_qualifier": {"type": "string", "default": "03", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/436-E1"}, {"$ref": "ncpdp.ecl.json#/$defs/436-E1"}]}, "prod_svc_id": {"$ref": "ncpdp.fields.json#/$defs/407-D7"}, "associated_rx_svc_no": {"$ref": "ncpdp.fields.json#/$defs/456-EN"}, "associated_rx_service_date": {"$ref": "ncpdp.fields.json#/$defs/457-EP"}, "proc_mod_code_count": {"default": 0, "defaultCounterLength": {"arrayField": "proc_mod_code"}, "$ref": "ncpdp.fields.json#/$defs/458-SE"}, "proc_mod_code": {"type": "array", "maxItems": 4, "items": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/459-ER"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/459-ER"}]}}, "quantity_dispensed": {"$ref": "ncpdp.fields.json#/$defs/442-E7"}, "fill_number": {"$ref": "ncpdp.fields.json#/$defs/403-D3"}, "day_supply": {"$ref": "ncpdp.fields.json#/$defs/405-D5"}, "compound_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/406-D6"}, {"$ref": "ncpdp.ecl.json#/$defs/406-D6"}]}, "daw_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/408-D8"}, {"$ref": "ncpdp.ecl.json#/$defs/408-D8"}]}, "date_rx_written": {"$ref": "ncpdp.fields.json#/$defs/414-DE"}, "number_of_refills_authorized": {"$ref": "ncpdp.fields.json#/$defs/415-DF"}, "rx_origin_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/419-DJ"}, {"$ref": "ncpdp.ecl.json#/$defs/419-DJ"}], "$comment": "payer.default_origin_id"}, "sub_clar_code_count": {"default": 0, "defaultCounterLength": {"arrayField": "sub_clar_code"}, "$ref": "ncpdp.fields.json#/$defs/354-NX"}, "sub_clar_code": {"type": "array", "maxItems": 3, "items": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/420-DK"}, {"$ref": "ncpdp.ecl.json#/$defs/420-DK"}]}}, "quantity_prescribed": {"$ref": "ncpdp.fields.json#/$defs/460-ET"}, "other_coverage_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/308-C8"}, {"$ref": "ncpdp.ecl.json#/$defs/308-C8"}]}, "sp_pk_indicator": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/429-DT"}, {"$ref": "ncpdp.ecl.json#/$defs/429-DT"}]}, "og_rx_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/453-EJ"}, {"$ref": "ncpdp.ecl.json#/$defs/453-EJ"}]}, "og_rx_id": {"$ref": "ncpdp.fields.json#/$defs/445-EA"}, "og_rx_quantity": {"$ref": "ncpdp.fields.json#/$defs/446-EB"}, "sched_rx_no": {"$ref": "ncpdp.fields.json#/$defs/454-EK"}, "unit_of_measure": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/600-28"}, {"$ref": "ncpdp.ecl.json#/$defs/600-28"}]}, "level_of_service": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/418-DI"}, {"$ref": "ncpdp.ecl.json#/$defs/418-DI"}], "$comment": "payer.default_los_id"}, "pa_type_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/461-EU"}, {"$ref": "ncpdp.ecl.json#/$defs/461-EU"}], "$comment": "payer.default_pa_type_id"}, "pa_no_submitted": {"$ref": "ncpdp.fields.json#/$defs/462-EV"}, "inter_auth_type_id": {"$ref": "ncpdp.fields.json#/$defs/463-EW"}, "inter_auth_id": {"$ref": "ncpdp.fields.json#/$defs/464-EX"}, "dispensing_status": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/343-HD"}, {"$ref": "ncpdp.ecl.json#/$defs/343-HD"}]}, "qty_to_disp": {"$ref": "ncpdp.fields.json#/$defs/344-HF"}, "ds_to_disp": {"$ref": "ncpdp.fields.json#/$defs/345-HG"}, "delay_reason_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/357-NV"}, {"$ref": "ncpdp.ecl.json#/$defs/357-NV"}]}, "pt_assign_indicator": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/391-MT"}, {"$ref": "ncpdp.ecl.json#/$defs/391-MT"}]}, "admin_route": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/995-E2"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/995-E2"}]}, "compound_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/996-G1"}, {"$ref": "ncpdp.ecl.json#/$defs/996-G1"}]}, "pharmacy_service_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/147-U7"}, {"$ref": "ncpdp.ecl.json#/$defs/147-U7"}], "$comment": "payer.default_service_id"}}, "required": ["rx_svc_no_ref_qualifier", "rx_svc_no", "prod_svc_id_qualifier", "prod_svc_id"], "errorMessage": {"required": "For claim segment, the following fields are required: Segment Identifier (111-AM), Transaction Code (103-A3), Service Reference Number Qualifier (455-EM), Service Reference Number (402-D2), Product Service ID Qualifier (436-E1), Product Service ID (407-D7)"}, "allOf": [{"if": {"properties": {"og_rx_id_qualifier": {"const": "01"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/upc"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "02"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hri"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"anyOf": [{"const": "03"}, {"const": "36"}]}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ndc"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "04"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hibcc"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "06"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dur_pps"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"anyOf": [{"const": "07"}, {"const": "08"}]}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/cpt4"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "09"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hcpc"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "10"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ppac"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "11"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/nappi"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "12"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/gtin"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "15"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "28"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_name_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "29"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_route_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "30"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_dose_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "31"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "32"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn_seq"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "33"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_ingred_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "42"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/mp_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "43"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/prod_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "44"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/sp_id"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "05"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dod"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "13"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/din"}}}}, {"if": {"properties": {"og_rx_id_qualifier": {"const": "45"}}}, "then": {"properties": {"og_rx_id": {"$ref": "ncpdp.product_id_types.json#/$defs/di"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "01"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/upc"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "02"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hri"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"anyOf": [{"const": "03"}, {"const": "36"}]}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ndc"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "04"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hibcc"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "06"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dur_pps"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"anyOf": [{"const": "07"}, {"const": "08"}]}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/cpt4"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "09"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hcpc"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "10"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ppac"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "11"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/nappi"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "12"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/gtin"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "15"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "28"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_name_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "29"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_route_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "30"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_dose_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "31"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "32"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn_seq"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "33"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_ingred_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "34"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/upin"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "42"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/mp_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "43"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/prod_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "44"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/sp_id"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "05"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dod"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "13"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/din"}}}}, {"if": {"properties": {"prod_svc_id_qualifier": {"const": "45"}}}, "then": {"properties": {"prod_svc_id": {"$ref": "ncpdp.product_id_types.json#/$defs/di"}}}}, {"if": {"properties": {"transaction_code": {"const": "B1"}}}, "then": {"properties": {"quantity_dispensed": {"$ref": "ncpdp.fields.json#/$defs/442-E7"}, "fill_number": {"$ref": "ncpdp.fields.json#/$defs/403-D3"}, "day_supply": {"$ref": "ncpdp.fields.json#/$defs/405-D5"}, "compound_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/406-D6"}, {"$ref": "ncpdp.ecl.json#/$defs/406-D6"}]}, "daw_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/408-D8"}, {"$ref": "ncpdp.ecl.json#/$defs/408-D8"}]}, "date_rx_written": {"$ref": "ncpdp.fields.json#/$defs/414-DE"}}, "required": ["quantity_dispensed", "fill_number", "day_supply", "compound_code", "daw_code", "date_rx_written"], "errorMessage": {"required": "For claim segment, the following fields are required: Quantity Dispensed (442-E7), Fill Number (403-D3), Day Supply (405-D5), Compound Code (406-D6), DAW Flag (408-D8), Date Prescription Written (414-DE)"}}}, {"if": {"properties": {"compound_code": {"const": "2"}}}, "then": {"properties": {"prod_svc_id_qualifier": {"const": "00", "errorMessage": {"const": "Product Service Qualifier must be 00 when billing for a compound."}}, "prod_svc_id": {"const": "0", "errorMessage": {"const": "Product Service ID must be 0 when billing for a compound."}}}}}, {"if": {"required": ["dispensing_status"], "properties": {"dispensing_status": {"const": "C"}}}, "then": {"required": ["associated_rx_svc_no", "associated_rx_service_date"], "properties": {"associated_rx_svc_no": {"minLength": 1, "errorMessage": {"minLength": "Associated Prescription Reference Number is required on the competition of additional fills of a partial fill."}}, "associated_rx_service_date": {"minLength": 1, "errorMessage": {"minLength": "Associated Prescription Service Date is required on the competition of additional fills of a partial fill."}}}}}, {"if": {"required": ["dispensing_status", "partial_fill_claim_id"], "properties": {"dispensing_status": {"const": "P"}, "partial_fill_claim_id": {"not": {"type": "null"}}}}, "then": {"required": ["associated_rx_svc_no", "associated_rx_service_date"], "properties": {"associated_rx_svc_no": {"minLength": 1, "errorMessage": {"minLength": "Associated Prescription Reference Number is required on the additional fills of a partial fill."}}, "associated_rx_service_date": {"minLength": 1, "errorMessage": {"minLength": "Associated Prescription Service Date is required on the additional fills of a partial fill."}}}}}, {"if": {"required": ["og_rx_id"], "properties": {"og_rx_id": {"not": {"type": "string", "minLength": 1}}}}, "then": {"required": ["og_rx_id_qualifier"], "properties": {"og_rx_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/453-EJ"}, {"$ref": "ncpdp.ecl.json#/$defs/453-EJ"}]}}, "errorMessage": {"required": "Originally prescriber product qualifier is required if Originally prescriber product code."}}}, {"if": {"properties": {"transaction_code": {"enum": ["B1", "B2", "B3"]}}}, "then": {"properties": {"rx_svc_no_ref_qualifier": {"const": "1", "errorMessage": {"const": "Prescription Service Reference Number Qualifier must be '1' for transaction codes B1, B2, B3."}}}}}, {"if": {"properties": {"transaction_code": {"enum": ["S1", "S2", "S3"]}}}, "then": {"properties": {"rx_svc_no_ref_qualifier": {"const": "2", "errorMessage": {"const": "Prescription Service Reference Number Qualifier must be '2' for transaction codes S1, S2, S3."}}}}}, {"if": {"required": ["qty_to_disp"], "properties": {"qty_to_disp": {"type": "string", "minLength": 1}}}, "then": {"required": ["ds_to_disp"], "properties": {"ds_to_disp": {"$ref": "ncpdp.fields.json#/$defs/345-HG"}}, "errorMessage": {"required": "Day Supply Intended to be Dispensed is required if Quantity Intended to be Dispensed is set."}}}, {"if": {"required": ["ds_to_disp"], "properties": {"ds_to_disp": {"type": "string", "minLength": 1}}}, "then": {"required": ["qty_to_disp"], "properties": {"qty_to_disp": {"$ref": "ncpdp.fields.json#/$defs/344-HF"}}, "errorMessage": {"required": "Quantity Intended to be Dispensed is required if Day Supply Intended to be Dispensed is set."}}}, {"if": {"required": ["transaction_code", "prod_svc_id_qualifier"], "properties": {"transaction_code": {"enum": ["S1", "S2", "S3"]}, "prod_svc_id_qualifier": {"const": "06"}}}, "then": {"properties": {"prod_svc_id": {"const": "0", "errorMessage": {"const": "Product Service ID must be '0' for transaction codes S1, S2, S3 when Service Provider ID Qualifier is '06'."}}}}}]}