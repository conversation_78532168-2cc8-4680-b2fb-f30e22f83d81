{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.workers_comp.json", "title": "NCPDP Claim Worker's Comp Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_worker_comp", "properties": {"segment_identification": {"type": "string", "default": "06", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "date_of_injury": {"$ref": "ncpdp.fields.json#/$defs/434-DY"}, "employer_name": {"$ref": "ncpdp.fields.json#/$defs/315-CF"}, "employer_address": {"$ref": "ncpdp.fields.json#/$defs/316-CG"}, "employer_city": {"$ref": "ncpdp.fields.json#/$defs/317-CH"}, "employer_state": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/318-CI"}, {"$ref": "ncpdp.ecl.json#/$defs/318-CI"}]}, "employer_zip": {"$ref": "ncpdp.fields.json#/$defs/319-CJ"}, "employer_phone": {"$ref": "ncpdp.fields.json#/$defs/320-CK"}, "employer_contact_name": {"$ref": "ncpdp.fields.json#/$defs/321-CL"}, "carrier_id": {"$ref": "ncpdp.fields.json#/$defs/327-CR"}, "claim_reference_id": {"$ref": "ncpdp.fields.json#/$defs/435-DZ"}, "bill_type_indicator": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/117-TR"}, {"$ref": "ncpdp.ecl.json#/$defs/117-TR"}]}, "pay_to_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/118-TS"}, {"$ref": "ncpdp.ecl.json#/$defs/118-TS"}]}, "pay_to_id": {"$ref": "ncpdp.fields.json#/$defs/119-TT"}, "pay_to_name": {"$ref": "ncpdp.fields.json#/$defs/120-TU"}, "pay_to_address": {"$ref": "ncpdp.fields.json#/$defs/121-TV"}, "pay_to_city": {"$ref": "ncpdp.fields.json#/$defs/122-TW"}, "pay_to_state": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/123-TX"}, {"$ref": "ncpdp.ecl.json#/$defs/123-TX"}]}, "pay_to_zip": {"$ref": "ncpdp.fields.json#/$defs/124-TY"}, "gen_prod_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/125-TZ"}, {"$ref": "ncpdp.ecl.json#/$defs/125-TZ"}]}, "gen_prod_id": {"$ref": "ncpdp.fields.json#/$defs/126-UA"}}, "required": ["segment_identification", "date_of_injury", "bill_type_indicator"], "errorMessage": {"required": "Date of injury (434-DY) is required for worker's compensation claim."}, "allOf": [{"if": {"properties": {"gen_prod_id_qualifier": {"const": "01"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/upc"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "02"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hri"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"anyOf": [{"const": "03"}, {"const": "36"}]}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ndc"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "04"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hibcc"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "06"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dur_pps"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"anyOf": [{"const": "07"}, {"const": "08"}]}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/cpt4"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "09"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hcpc"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "10"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ppac"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "11"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/nappi"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "12"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/gtin"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "15"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "28"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_name_id"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "29"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_route_id"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "30"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_dose_id"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "31"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_id"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "32"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn_seq"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "33"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_ingred_id"}}}}, {"if": {"properties": {"gen_prod_id_qualifier": {"const": "45"}}}, "then": {"properties": {"gen_prod_id": {"$ref": "ncpdp.product_id_types.json#/$defs/di"}}}}, {"if": {"properties": {"gen_prod_id": {"type": "string", "minLength": 1}}}, "then": {"required": ["gen_prod_id_qualifier"], "properties": {"gen_prod_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/125-TZ"}, {"$ref": "ncpdp.ecl.json#/$defs/125-TZ"}]}}, "errorMessage": {"required": "Generic Equivalent Product ID Qualifier is required when Generic Equivalent Product ID is used."}}}, {"if": {"properties": {"pay_to_id": {"type": "string", "minLength": 1}}}, "then": {"required": ["pay_to_qualifier"], "properties": {"pay_to_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/118-TS"}, {"$ref": "ncpdp.ecl.json#/$defs/118-TS"}]}}, "errorMessage": {"required": "Pay To Qualifier is required when Pay To ID is used."}}}]}