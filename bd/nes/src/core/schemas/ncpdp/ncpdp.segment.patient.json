{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.patient.json", "title": "NCPDP Claim Patient Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_pt", "properties": {"isInlineSubform": true, "transaction_code": {"type": "string", "default": "B1", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/103-A3"}, {"$ref": "ncpdp.ecl.json#/$defs/103-A3"}, {"removeProperty": true}]}, "pt_rel_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/306-C6"}, {"$ref": "ncpdp.ecl.json#/$defs/306-C6"}, {"removeProperty": true}]}, "payer_type_id": {"type": "string", "allOf": [{"removeProperty": true}], "$comment": "payer.type_id"}, "segment_identification": {"type": "string", "default": "01", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "patient_id_qualifier": {"type": "string", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/331-CX"}, {"$ref": "ncpdp.ecl.json#/$defs/331-CX"}]}, "patient_claim_id": {"$ref": "ncpdp.fields.json#/$defs/332-CY"}, "patient_date_of_birth": {"$ref": "ncpdp.fields.json#/$defs/304-C4"}, "patient_gender_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/305-C5"}, {"$ref": "ncpdp.ecl.json#/$defs/305-C5"}]}, "patient_first_name": {"$ref": "ncpdp.fields.json#/$defs/310-CA"}, "patient_last_name": {"$ref": "ncpdp.fields.json#/$defs/311-CB"}, "patient_street_address": {"$ref": "ncpdp.fields.json#/$defs/322-CM"}, "patient_city_address": {"$ref": "ncpdp.fields.json#/$defs/323-CN"}, "patient_state": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/324-CO"}, {"$ref": "ncpdp.ecl.json#/$defs/324-CO"}]}, "patient_zip": {"$ref": "ncpdp.fields.json#/$defs/325-CP"}, "patient_phone": {"$ref": "ncpdp.fields.json#/$defs/326-CQ"}, "place_of_service": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/307-C7"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/307-C7"}], "$comment": "payer.default_service_place_id"}, "employer_id": {"$ref": "ncpdp.fields.json#/$defs/333-CZ"}, "pregnancy_indicator": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/335-2C"}, {"$ref": "ncpdp.ecl.json#/$defs/335-2C"}]}, "patient_email_address": {"$ref": "ncpdp.fields.json#/$defs/350-HN"}, "patient_residence": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/384-4X"}, {"$ref": "ncpdp.ecl.json#/$defs/384-4X"}], "$comment": "payer.default_place_of_res_id"}}, "required": ["segment_identification", "transaction_code"], "allOf": [{"if": {"properties": {"transaction_code": {"enum": ["B1", "B2", "B3"]}}}, "then": {"properties": {"patient_id_qualifier": {"default": "EA", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/331-CX"}, {"$ref": "ncpdp.ecl.json#/$defs/331-CX"}]}}}}, {"if": {"properties": {"payer_type_id": {"const": "MCRD"}, "transaction_code": {"const": "E1"}}}, "then": {"required": ["patient_zip", "patient_first_name", "patient_last_name", "patient_gender_code", "patient_date_of_birth"], "properties": {"patient_zip": {"$ref": "ncpdp.fields.json#/$defs/325-CP"}, "patient_first_name": {"$ref": "ncpdp.fields.json#/$defs/310-CA"}, "patient_last_name": {"$ref": "ncpdp.fields.json#/$defs/311-CB"}, "patient_gender_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/305-C5"}, {"$ref": "ncpdp.ecl.json#/$defs/305-C5"}]}, "patient_date_of_birth": {"$ref": "ncpdp.fields.json#/$defs/304-C4"}}, "errorMessage": {"required": "To check eligibility on Medicare Part D, the following are required: Zip Code (325-CP), First Name (310-CA), Last Name (311-CB), Gender (305-C5), and DOB (304-C4)"}}}, {"if": {"properties": {"transaction_code": {"const": "B1"}}}, "then": {"required": ["patient_first_name", "patient_last_name", "patient_gender_code", "patient_date_of_birth"], "properties": {"patient_zip": {"$ref": "ncpdp.fields.json#/$defs/325-CP"}, "patient_first_name": {"$ref": "ncpdp.fields.json#/$defs/310-CA"}, "patient_last_name": {"$ref": "ncpdp.fields.json#/$defs/311-CB"}, "patient_gender_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/305-C5"}, {"$ref": "ncpdp.ecl.json#/$defs/305-C5"}]}, "patient_date_of_birth": {"$ref": "ncpdp.fields.json#/$defs/304-C4"}}, "errorMessage": {"required": "Patient First Name, Last Name, Gender, and DOB are required"}}}, {"if": {"properties": {"pt_rel_code": {"not": {"const": "1"}}}, "anyOf": [{"properties": {"patient_date_of_birth": {"type": "null"}}}, {"properties": {"patient_date_of_birth": {"const": ""}}}]}, "then": {"required": ["patient_first_name", "patient_last_name"], "properties": {"patient_first_name": {"$ref": "ncpdp.fields.json#/$defs/310-CA"}, "patient_last_name": {"$ref": "ncpdp.fields.json#/$defs/311-CB"}}, "errorMessage": {"required": "Patient First Name, and Last Name is required if patient is not the cardholder and DOB is not available"}}}]}