{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.product_id_types.json", "title": "NCPDP Product ID Formats", "description": "An NCPDP Product ID Formats Schema", "$defs": {"upc": {"pattern": "^\\d{12}$", "description": "Universal Product Code (UPC), must be 12 digits", "errorMessage": {"pattern": "Field must be a valid Universal Product Code (UPC), 12 digits."}}, "hri": {"pattern": "^\\d{11}$", "description": "Health Related Item (HRI), must be 11 digits", "errorMessage": {"pattern": "Field must be a valid Health Related Item (HRI), 11 digits."}}, "ndc": {"pattern": "^\\d{11}$", "description": "National Drug Code (NDC), must be 11 digits", "errorMessage": {"pattern": "Field must be a valid National Drug Code (NDC), 11 digits."}}, "hibcc": {"pattern": "^[A-Za-z][A-Za-z0-9]{8}$", "description": "Health Industry Business Communications Council (HIBCC), must be 8 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Health Industry Business Communications Council (HIBCC) code, 8 alphanumeric characters."}}, "dur_pps": {"pattern": "^[A-Z0-9]{6}$", "description": "Drug Use Review/ Professional Pharmacy Service (DUR/PPS) Code, must be 6 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Drug Use Review/Professional Pharmacy Service (DUR/PPS) Code, 6 alphanumeric characters."}}, "cpt4": {"pattern": "^(?:\\d{5}|\\d{4}[FT])$", "description": "Current Procedural Terminology (CPT4), must be 5 digits or 4 digits followed by an F (Category II) or T (Category III)", "errorMessage": {"pattern": "Field must be a valid Current Procedural Terminology (CPT4) code, 5 digits or 4 digits followed by an F or T."}}, "cpt5": {"pattern": "^(?:\\d{5}|\\d{4}[FT])$", "description": "Current Procedural Terminology (CPT5), must be 5 digits or 4 digits followed by an F (Category II) or T (Category III)", "errorMessage": {"pattern": "Field must be a valid Current Procedural Terminology (CPT5) code, 5 digits or 4 digits followed by an F or T."}}, "hcpc": {"pattern": "^(?:\\d{5}|[A-V][0-9]{4})$", "description": "Healthcare Common Procedure Coding System (HCPCS), must be 5 digits (Level I), or alphanumeric character followed by 4 digits (Level II)", "errorMessage": {"pattern": "Field must be a valid Healthcare Common Procedure Coding System (HCPCS) code, 5 digits (Level I) or alphanumeric character followed by 4 digits (Level II)."}}, "ppac": {"pattern": "^[A-Z]{2}\\d{4}$", "description": "Pharmacy Practice Activity Classification (PPAC), must be 2 capital letters followed by 4 digits", "errorMessage": {"pattern": "Field must be a valid Pharmacy Practice Activity Classification (PPAC) code, 2 capital letters followed by 4 digits."}}, "nappi": {"pattern": "^\\d{9}$", "description": "National Pharmaceutical Product Interface Code (NAPPI), must be 9 digits", "errorMessage": {"pattern": "Field must be a valid National Pharmaceutical Product Interface Code (NAPPI), 9 digits."}}, "gtin": {"pattern": "^\\d{8}$|^\\d{12}$|^\\d{13}$|^\\d{14}$", "description": "Global Trade Identification Number (GTIN), must be 8, 12, 13, or 14 digits", "errorMessage": {"pattern": "Field must be a valid Global Trade Identification Number (GTIN), 8, 12, 13, or 14 digits."}}, "fdb_gcn": {"pattern": "^\\d{6}$", "description": "First DataBank Formulation ID (GCN), must be 6 digits", "errorMessage": {"pattern": "Field must be a valid First DataBank Formulation ID (GCN), 6 digits."}}, "fdb_med_name_id": {"pattern": "^\\d{6,8}$", "description": "First DataBank Medication Name ID (FDB Med Name ID), must be 6-8 digits", "errorMessage": {"pattern": "Field must be a valid First DataBank Medication Name ID (FDB Med Name ID), 6-8 digits."}}, "fdb_med_route_id": {"pattern": "^\\d{6,7}$", "description": "First DataBank Routed Medication ID (FDB Routed Med ID), must be 6-7 digits", "errorMessage": {"pattern": "Field must be a valid First DataBank Routed Medication ID (FDB Routed Med ID), 6-7 digits."}}, "fdb_med_dose_id": {"pattern": "^\\d{7}$", "description": "First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID), must be 7 digits", "errorMessage": {"pattern": "Field must be a valid First DataBank Routed Dosage Form ID (FDB Routed Dosage Form Med ID), 7 digits."}}, "fdb_med_id": {"pattern": "^\\d{6,7}$", "description": "First DataBank Medication ID (FDB MedID), must be 6-7 digits", "errorMessage": {"pattern": "Field must be a valid First DataBank Medication ID (FDB MedID), 6-7 digits."}}, "fdb_gcn_seq": {"pattern": "^\\d{6}$", "description": "First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO), must be 6 digits long", "errorMessage": {"pattern": "Field must be a valid First DataBank Clinical Formulation ID Sequence Number (GCN_SEQNO), 6 digits."}}, "fdb_ingred_id": {"pattern": "^\\d{4,6}$", "description": "First DataBank Ingredient List ID (HICL_SEQNO), must be 4-6 digits", "errorMessage": {"pattern": "Field must be a valid First DataBank Ingredient List ID (HICL_SEQNO), 4-6 digits."}}, "upin": {"pattern": "^(?:\\+[A-Z0-9]{1,4}[A-Z0-9]{1,18}[0-9][A-Z0-9]|\\d{12,14})$", "description": "Universal Product Number (UPIN), must be either a valid HIBC or UCC/EAN format", "errorMessage": {"pattern": "Field must be a valid Universal Product Number (UPIN), either a valid HIBC or UCC/EAN format."}}, "mp_id": {"pattern": "^[A-Z0-9]{8,12}$", "description": "Gold Standard Marketed Product Identifier (MPid), must be 8-12 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Gold Standard Marketed Product Identifier (MPid), 8-12 alphanumeric characters."}}, "prod_id": {"pattern": "^[A-Z0-9]{6,10}$", "description": "Gold Standard Product Identifier (ProdID), must be 6-10 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Gold Standard Product Identifier (ProdID), 6-10 alphanumeric characters."}}, "sp_id": {"pattern": "^[A-Z0-9]{7,11}$", "description": "Gold Standard Specific Product Identifier (SPID), must be 7-11 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Gold Standard Specific Product Identifier (SPID), 7-11 alphanumeric characters."}}, "dod": {"pattern": "^[A-Z0-9]{13}$", "description": "Department of Defense (DOD), must be 13 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Department of Defense (DOD) identifier, 13 alphanumeric characters."}}, "din": {"pattern": "^\\d{8}$", "description": "Drug Identification Number (DIN), must be 8 digits", "errorMessage": {"pattern": "Field must be a valid Drug Identification Number (DIN), 8 digits."}}, "di": {"pattern": "^[A-Z0-9]{8,16}$", "description": "Device Identifier (DI), must be 8-16 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Device Identifier (DI), 8-16 alphanumeric characters."}}}}