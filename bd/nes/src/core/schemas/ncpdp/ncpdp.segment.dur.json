{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.dur.json", "title": "NCPDP Claim DUR Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_dur", "properties": {"rsn_for_svc_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/439-E4"}, {"$ref": "ncpdp.ecl.json#/$defs/439-E4"}]}, "prf_svc_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/440-E5"}, {"$ref": "ncpdp.ecl.json#/$defs/440-E5"}]}, "rst_svc_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/441-E6"}, {"$ref": "ncpdp.ecl.json#/$defs/441-E6"}]}, "dur_pps_loe": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/474-8E"}, {"$ref": "ncpdp.ecl.json#/$defs/474-8E"}]}, "co_agt_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/475-J9"}, {"$ref": "ncpdp.ecl.json#/$defs/475-J9"}]}, "co_agt_id": {"$ref": "ncpdp.fields.json#/$defs/476-H6"}}, "allOf": [{"if": {"properties": {"co_agt_id_qualifier": {"const": "01"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/upc"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "02"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hri"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"anyOf": [{"const": "03"}, {"const": "36"}]}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ndc"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "04"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hibcc"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"anyOf": [{"const": "07"}, {"const": "08"}]}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/cpt4"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "09"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hcpc"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "11"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/nappi"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "15"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "20"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/icd9"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "21"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/icd10"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "23"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/ncci"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "24"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/snomed"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "25"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/cdt"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "26"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/dsm_iv"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "28"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_name_id"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "29"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_route_id"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "30"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_dose_id"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "31"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_id"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "32"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn_seq"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "33"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_ingred_id"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "35"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.dx_code_sets.json#/$defs/loinc"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "05"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dod"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "13"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/din"}}}}, {"if": {"properties": {"co_agt_id_qualifier": {"const": "45"}}}, "then": {"properties": {"co_agt_id": {"$ref": "ncpdp.product_id_types.json#/$defs/di"}}}}, {"if": {"properties": {"co_agt_id": {"not": {"type": "null"}}}}, "then": {"required": ["co_agt_id_qualifier"], "errorMessage": {"required": "Co-Agent qualifier is required if Co-Agent ID is used."}}}]}