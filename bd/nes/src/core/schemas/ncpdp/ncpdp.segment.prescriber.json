{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.prescriber.json", "title": "NCPDP Claim Prescriber Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_prescriber", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "03", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "dr_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/466-EZ"}, {"$ref": "ncpdp.ecl.json#/$defs/466-EZ"}], "$comment": "payer.prescriber_no_field_id"}, "dr_id": {"$ref": "ncpdp.fields.json#/$defs/411-DB"}, "dr_last_name": {"$ref": "ncpdp.fields.json#/$defs/427-DR"}, "dr_phone": {"$ref": "ncpdp.fields.json#/$defs/498-PM"}, "pri_dr_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/468-2E"}, {"$ref": "ncpdp.ecl.json#/$defs/468-2E"}]}, "pri_dr_id": {"$ref": "ncpdp.fields.json#/$defs/421-DL"}, "pri_dr_last_name": {"$ref": "ncpdp.fields.json#/$defs/470-4E"}, "dr_first_name": {"$ref": "ncpdp.fields.json#/$defs/364-2J"}, "dr_street_address": {"$ref": "ncpdp.fields.json#/$defs/365-2K"}, "dr_city": {"$ref": "ncpdp.fields.json#/$defs/366-2M"}, "dr_state": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/367-2N"}, {"$ref": "ncpdp.ecl.json#/$defs/367-2N"}]}, "dr_zip": {"$ref": "ncpdp.fields.json#/$defs/368-2P"}}, "required": ["segment_identification"], "allOf": [{"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"anyOf": [{"const": "09"}, {"const": "01"}]}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/npi"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "02"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/blue_cross"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "03"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/blue_shield"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "04"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/mcr_ptan_id"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "05"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/medicaid_id"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "06"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/upin"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "08"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/state_license"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "10"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/hin_id"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "11"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/tax_id"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "15"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/hci_dea_id"}}}}, {"if": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"const": "07"}}}, "then": {"properties": {"pri_dr_id": {"$ref": "ncpdp.licenses.json#/$defs/ncpdp"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"anyOf": [{"const": "09"}, {"const": "01"}]}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/npi"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "02"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/blue_cross"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "03"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/blue_shield"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "04"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/mcr_ptan_id"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "05"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/medicaid_id"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "06"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/upin"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "08"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/state_license"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "10"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/hin_id"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "11"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/tax_id"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "15"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/hci_dea_id"}}}}, {"if": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"const": "07"}}}, "then": {"properties": {"dr_id": {"$ref": "ncpdp.licenses.json#/$defs/ncpdp"}}}}, {"if": {"required": ["dr_id"], "properties": {"dr_id": {"type": "string", "minLength": 1}}}, "then": {"required": ["dr_id_qualifier"], "properties": {"dr_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/466-EZ"}, {"$ref": "ncpdp.ecl.json#/$defs/466-EZ"}], "$comment": "payer.prescriber_no_field_id"}}, "errorMessage": {"minLength": "Prescriber ID Qualifier is required when Prescriber ID field is used."}}}, {"if": {"properties": {"dr_id": {"type": "null"}}}, "then": {"required": ["dr_last_name"], "properties": {"dr_last_name": {"$ref": "ncpdp.fields.json#/$defs/427-DR"}}, "errorMessage": {"minLength": "Prescriber Last Name is required if Prescriber ID is not known."}}}, {"if": {"required": ["pri_dr_id"], "properties": {"pri_dr_id": {"type": "string", "minLength": 1}}}, "then": {"required": ["pri_dr_id_qualifier"], "properties": {"pri_dr_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/468-2E"}, {"$ref": "ncpdp.ecl.json#/$defs/468-2E"}]}}, "errorMessage": {"minLength": "Primary Care Provider ID Qualifier is required if Primary Care Provider ID is used."}}}, {"if": {"properties": {"dr_id_qualifier": {"const": "00"}}}, "then": {"required": ["dr_last_name"], "properties": {"dr_last_name": {"$ref": "ncpdp.fields.json#/$defs/427-DR"}}, "errorMessage": {"minLength": "Prescriber Last Name is needed is Prescriber ID Qualifier is Not Specified."}}}, {"if": {"properties": {"pri_dr_id_qualifier": {"const": "00"}}}, "then": {"required": ["pri_dr_last_name"], "properties": {"pri_dr_last_name": {"$ref": "ncpdp.fields.json#/$defs/470-4E"}}, "errorMessage": {"minLength": "Primary Care Provider Last Name is needed is Primary Care Provider ID Qualifier is Not Specified."}}}]}