{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.licenses.json", "title": "NCPDP License Formats", "description": "An NCPDP License Formats Schema", "$defs": {"hp_id": {"pattern": "^\\d{10}$", "description": "Health Plan Identifier (HPID), must be 10 digits", "errorMessage": {"pattern": "Field must be a valid Health Plan Identifier (HPID), 10 digits."}}, "naic_id": {"pattern": "^\\d{5}$", "description": "National Association of Insurance Commissioners (NAIC) ID, must be 5 digits", "errorMessage": {"pattern": "Field must be a valid National Association of Insurance Commissioners (NAIC) ID, 5 digits."}}, "hin_id": {"pattern": "^[A-Za-z][A-Za-z0-9]{8}$", "description": "Health Industry Number (HIN), must be 8 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Health Industry Number (HIN), 8 alphanumeric characters."}}, "coba_id": {"pattern": "^\\d{5}$", "description": "Coordination of Benefits Agreement (COBA) ID, must be 5 digits", "errorMessage": {"pattern": "Field must be a valid Coordination of Benefits Agreement (COBA) ID, 5 digits."}}, "medicaid_id": {"anyOf": [{"pattern": "^\\d{10}$", "errorMessage": {"pattern": "Field must be a valid Medicaid ID, 10 digits."}}, {"pattern": "^\\d{9}$", "errorMessage": {"pattern": "Field must be a valid Medicaid ID, 9 digits."}}, {"pattern": "^[A-Za-z0-9]{8,12}$", "errorMessage": {"pattern": "Field must be a valid Medicaid ID, 8-12 alphanumeric characters."}}, {"pattern": "^[A-Z]{2}\\d{6}[A-Z]?$", "errorMessage": {"pattern": "Field must be a valid Medicaid ID, 2 letters followed by 6 digits and an optional letter."}}], "description": "Medicaid ID"}, "mac_id": {"pattern": "^[A-Z]{3}\\d{4}$", "description": "Medicare Administrative Contractors (MACs) ID, must be 3 capital letters and 4 digits", "errorMessage": {"pattern": "Field must be a valid Medicare Administrative Contractors (MACs) ID, 3 capital letters and 4 digits."}}, "ncpdp_id": {"pattern": "^\\d{7}$", "description": "NCPDP ID, must be 7 digits long", "errorMessage": {"pattern": "Field must be a valid NCPDP ID, 7 digits."}}, "npi": {"pattern": "^\\d{10}$", "description": "NPI, must be 10 digits", "errorMessage": {"pattern": "Field must be a valid NPI, 10 digits."}}, "asap_id": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{6,20}$", "description": "ASAP ID, must be 6-20 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid ASAP ID, 6-20 alphanumeric characters."}}]}, "ccn_id": {"pattern": "^[0-5][0-9][0-9]{4}$", "description": "CCN Number (Part A), must be 6 digits starting with 0-5", "errorMessage": {"pattern": "Field must be a valid CCN Number (Part A), 6 digits starting with 0-5."}}, "nsc_id": {"pattern": "^[0-5][0-9][0-9]{4}$", "description": "NSC ID, NSC IDs are typically 10-digits long", "errorMessage": {"pattern": "Field must be a valid NSC ID, 10 digits starting with 0-5."}}, "medicare_submitter_id": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{5,10}$", "description": "Medicare Submitter ID, must be 5-10 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Medicare Submitter ID, 5-10 alphanumeric characters."}}]}, "dmac_submitter_id": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{6,15}$", "description": "DME Medicare Submitter ID, must be 6-15 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid DME Medicare Submitter ID, 6-15 alphanumeric characters."}}]}, "tax_id": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{6,15}$", "description": "Tax ID (TIN), must be nine digits, formatted as XX-XXXXXXX", "errorMessage": {"pattern": "Field must be a valid Tax ID (TIN), nine digits formatted as XX-XXXXXXX."}}]}, "bcbs_id": {"pattern": "^[A-Za-z0-9]{1,10}$", "description": "BCBS ID, must be 1-10 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid BCBS ID, 1-10 alphanumeric characters."}}, "dea_id": {"pattern": "^[A-Za-z]{2}\\d{7}$", "description": "DEA number, DEA numbers must be 2 letters followed by 7 numbers", "errorMessage": {"pattern": "Field must be a valid DEA number, 2 letters followed by 7 numbers."}}, "nabp_id": {"pattern": "^\\d{6,8}$", "description": "NABP number, NABP numbers must be 6 or 8 digits long", "errorMessage": {"pattern": "Field must be a valid NABP number, 6 or 8 digits."}}, "champus_id": {"anyOf": [{"pattern": "^\\d{9}[0-9A-Za-z]{2}$", "errorMessage": {"pattern": "Field must be a valid CHAMPUS ID, 9 digits followed by 2 alphanumeric characters."}}, {"pattern": "^\\d{11}$", "errorMessage": {"pattern": "Field must be a valid CHAMPUS ID, 11 digits."}}], "description": "CHAMPUS ID, CHAMPUS ID numbers must be either 11 digits or an older 9-digit SSN and 2 alphanumeric characters"}, "medi_cal_pin": {"pattern": "^\\d{6,9}$", "description": "Medi-<PERSON>, must be 6-9 digits", "errorMessage": {"pattern": "Field must be a valid Medi-Cal Pin, 6-9 digits."}}, "mcr_ptan_id": {"pattern": "^[A-Za-z0-9]{5,12}$", "description": "Medicare Provider Number (PTAN), must be 5-12 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Medicare Provider Number (PTAN), 5-12 alphanumeric characters."}}, "state_license": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{5,20}$", "description": "State License Number, must be 5-20 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid State License Number, 5-20 alphanumeric characters."}}]}, "state_cs_license": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{5,20}$", "description": "State Controlled Substance License Number, must be 5-20 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid State Controlled Substance License Number, 5-20 alphanumeric characters."}}]}, "certificate_to_presc": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{5,20}$", "description": "Certificate to Prescriber Number, must be 5-20 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Certificate to Prescriber Number, 5-20 alphanumeric characters."}}]}, "rems": {"allOf": [{"removeHyphen": true}, {"pattern": "^[A-Za-z0-9]{5,20}$", "description": "REMs ID, must be 5-20 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid REMs ID, 5-20 alphanumeric characters."}}]}, "blue_cross": {"pattern": "^[A-Z]{3}[A-Z0-9]{6,15}$", "description": "Blue Cross ID, must be 3 alphanumeric prefix followed by 6-15 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Blue Cross ID, 3 alphanumeric prefix followed by 6-15 alphanumeric characters."}}, "blue_shield": {"pattern": "^([A-Z0-9]{1,3})?\\d{8,14}$", "description": "Blue Shield ID, must be 8-14 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Blue Shield ID, 8-14 alphanumeric characters."}}, "upin": {"pattern": "^[A-Za-z][0-9]{5}$", "description": "UPIN (Unique Physician/Practitioner Identification Number), must be 6 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid UPIN (Unique Physician/Practitioner Identification Number), 6 alphanumeric characters."}}, "ncpdp": {"pattern": "^\\d{7}$", "description": "NCPDP Provider Identification Number (National Council for Prescription Drug Programs Provider Identification Number), must be 7 digits", "errorMessage": {"pattern": "Field must be a valid NCPDP Provider Identification Number, 7 digits."}}, "hci_dea_id": {"pattern": "^\\d{7}$", "description": "HCIdea ID (Healthcare Identification Directory ID), must be 7 digits", "errorMessage": {"pattern": "Field must be a valid HCIdea ID (Healthcare Identification Directory ID), 7 digits."}}, "cmea_id": {"pattern": "^[A-Za-z]{3,5}\\d{5,8}$", "description": "Combat Methamphetamine Epidemic Act (CMEA) Certificate ID, must be 3-5 alpha numeric characters followed by 5-8 digits", "errorMessage": {"pattern": "Field must be a valid Combat Methamphetamine Epidemic Act (CMEA) Certificate ID, 3-5 alphanumeric characters followed by 5-8 digits."}}, "ssn": {"allOf": [{"pattern": "^\\d{9}$", "description": "Social Security Number, must be 9 digits", "errorMessage": {"pattern": "Field must be a valid Social Security Number, 9 digits."}}]}}}