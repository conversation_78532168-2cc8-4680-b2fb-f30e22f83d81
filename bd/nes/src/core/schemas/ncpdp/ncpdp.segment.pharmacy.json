{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.pharmacy.json", "title": "NCPDP Claim Pharmacy Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_pharmacy", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "02", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "provider_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/465-EY"}, {"$ref": "ncpdp.ecl.json#/$defs/465-EY"}]}, "provider_id": {"$comment": "payer.nabp_pharmacy_id", "$ref": "ncpdp.fields.json#/$defs/444-E9"}}, "required": ["segment_identification", "provider_id_qualifier", "provider_id"], "errorMessage": {"required": "The following fields are required: Pharmacy ID Qualifier (465-EY), and Pharmacy ID (444-E9)"}, "allOf": [{"if": {"properties": {"provider_id_qualifier": {"const": "01"}}}, "then": {"properties": {"provider_id": {"$ref": "ncpdp.licenses.json#/$defs/dea_id"}}}}, {"if": {"properties": {"provider_id_qualifier": {"const": "02"}}}, "then": {"properties": {"provider_id": {"$ref": "ncpdp.licenses.json#/$defs/state_license"}}}}, {"if": {"properties": {"provider_id_qualifier": {"const": "03"}}}, "then": {"properties": {"provider_id": {"$ref": "ncpdp.licenses.json#/$defs/ssn"}}}}, {"if": {"properties": {"provider_id_qualifier": {"const": "05"}}}, "then": {"properties": {"provider_id": {"$ref": "ncpdp.licenses.json#/$defs/npi"}}}}, {"if": {"properties": {"provider_id_qualifier": {"const": "06"}}}, "then": {"properties": {"provider_id": {"$ref": "ncpdp.licenses.json#/$defs/hin_id"}}}}, {"if": {"properties": {"provider_id_qualifier": {"const": "07"}}}, "then": {"properties": {"provider_id": {"$ref": "ncpdp.licenses.json#/$defs/state_license"}}}}]}