{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.clinical.json", "title": "NCPDP Claim Clinical Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_clinical", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "13", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "dx_code_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_diagnosis"}, "$ref": "ncpdp.fields.json#/$defs/491-VE"}, "subform_diagnosis": {"type": "array", "items": {"maxItems": 5, "additionalProperties": false, "properties": {"dx_code_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/492-WE"}, {"$ref": "ncpdp.ecl.json#/$defs/492-WE"}]}, "dx_code": {"$ref": "ncpdp.fields.json#/$defs/411-DB"}}, "required": ["dx_code_qualifier", "dx_code"]}}, "clinical_information_counter": {"default": 0, "defaultCounterLength": {"arrayField": "subform_measurement"}, "$ref": "ncpdp.fields.json#/$defs/493-XE"}, "subform_measurement": {"type": "array", "maxItems": 5, "items": {"type": "object", "additionalProperties": false, "properties": {"measurement_date": {"$ref": "ncpdp.fields.json#/$defs/494-ZE"}, "measurement_time": {"$ref": "ncpdp.fields.json#/$defs/495-H1"}, "measurement_dimension": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/496-H2"}, {"$ref": "ncpdp.ecl.json#/$defs/496-H2"}]}, "measurement_unit": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/497-H3"}, {"$ref": "ncpdp.ecl.json#/$defs/497-H3"}]}, "measurement_value": {"$ref": "ncpdp.fields.json#/$defs/499-H4"}}, "required": ["measurement_dimension", "measurement_unit", "measurement_value"], "errorMessage": {"required": "Measurement dimension, unit, and value are required."}}}}, "required": ["segment_identification"], "errorMessage": {"required": "For claim segment, the following fields are required: Segment Identifier (111-AM)"}, "allOf": [{"if": {"properties": {"dx_code_qualifier": {"const": "01"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/icd9"}}}}, {"if": {"properties": {"dx_code_qualifier": {"const": "02"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/icd10"}}}}, {"if": {"properties": {"dx_code_qualifier": {"const": "03"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/ncci"}}}}, {"if": {"properties": {"dx_code_qualifier": {"const": "04"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/snomed"}}}}, {"if": {"properties": {"dx_code_qualifier": {"const": "05"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/cdt"}}}}, {"if": {"properties": {"dx_code_qualifier": {"const": "07"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/dsm_iv"}}}}, {"if": {"properties": {"dx_code_qualifier": {"const": "08"}}}, "then": {"properties": {"dx_code": {"$ref": "ncpdp.dx_code_sets.json#/$defs/dsm_5"}}}}]}