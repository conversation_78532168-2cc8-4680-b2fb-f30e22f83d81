{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.cob.json", "title": "NCPDP Claim COB Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_cob", "required": ["segment_identification", "subform_opayer"], "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "05", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "subform_opayer": {"type": "array", "maxItems": 9, "minItems": 1, "items": {"type": "object", "additionalProperties": false, "properties": {"other_date": {"$ref": "ncpdp.fields.json#/$defs/443-E8"}, "internal_control_number": {"$ref": "ncpdp.fields.json#/$defs/993-A7"}, "other_coverage_type": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/338-5C"}, {"$ref": "ncpdp.ecl.json#/$defs/338-5C"}], "$comment": "payer.ncpdp_sec_plan_type_id", "type": "string"}, "other_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/339-6C"}, {"$ref": "ncpdp.ecl.json#/$defs/339-6C"}]}, "other_id": {"$ref": "ncpdp.fields.json#/$defs/340-7C"}}, "required": ["other_coverage_type"], "errorMessage": {"required": "Other payer coverage type is required for COB claims."}}}, "coordination_of_benefits_other_payments_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_opayer"}, "$ref": "ncpdp.fields.json#/$defs/337-4C"}, "subform_paid": {"type": "array", "maxItems": 9, "items": {"type": "object", "additionalProperties": false, "properties": {"paid_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/342-HC"}, {"$ref": "ncpdp.ecl.json#/$defs/342-HC"}], "$comment": "payer.ncpdp_pri_payer_qualifier_id"}, "paid_amount": {"$ref": "ncpdp.fields.json#/$defs/431-DV"}}}}, "paid_amount_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_paid"}, "$ref": "ncpdp.fields.json#/$defs/341-HB"}, "other_reject_code": {"type": "array", "maxItems": 5, "items": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/472-6E"}, {"$ref": "ncpdp.ecl.json#/$defs/511-FB"}]}}, "other_payer_reject_count": {"default": 0, "defaultCounterLength": {"arrayField": "other_reject_code"}, "$ref": "ncpdp.fields.json#/$defs/471-5E"}, "subform_resp": {"type": "array", "maxItems": 25, "items": {"type": "object", "additionalProperties": false, "properties": {"pt_resp_amt_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/351-NP"}, {"$ref": "ncpdp.ecl.json#/$defs/351-NP"}], "$comment": "payer.ncpdp_sec_claims_pr_qualifier_id"}, "pt_resp_amt": {"$ref": "ncpdp.fields.json#/$defs/352-NQ"}}}}, "pt_resp_amt_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_resp"}, "$ref": "ncpdp.fields.json#/$defs/353-NR"}, "subform_benefit": {"type": "array", "maxItems": 4, "items": {"type": "object", "additionalProperties": false, "properties": {"benefit_stage_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/393-MV"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/393-MV"}], "$comment": "payer.ncpdp_sec_claims_benefit_qual_id"}, "benefit_stage_amount": {"$ref": "ncpdp.fields.json#/$defs/394-MW"}}}}, "benefit_stage_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_benefit"}, "$ref": "ncpdp.fields.json#/$defs/392-MU"}}, "allOf": [{"if": {"required": ["other_id"], "properties": {"other_id": {"type": "string", "minLength": 1}, "other_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/339-6C"}, {"$ref": "ncpdp.ecl.json#/$defs/339-6C"}]}}}, "then": {"required": ["other_id_qualifier"], "errorMessage": {"required": "Payer ID Qualifier is required when Payer ID is used."}}}, {"if": {"properties": {"other_id_qualifier": {"const": "01"}}}, "then": {"properties": {"other_id": {"$ref": "ncpdp.licenses.json#/$defs/hp_id"}}}}, {"if": {"properties": {"other_id_qualifier": {"const": "1C"}}}, "then": {"properties": {"other_id": {"$ref": "ncpdp.licenses.json#/$defs/mac_id"}}}}, {"if": {"properties": {"other_id_qualifier": {"const": "1D"}}}, "then": {"properties": {"other_id": {"$ref": "ncpdp.licenses.json#/$defs/medicaid_id"}}}}, {"if": {"properties": {"other_id_qualifier": {"const": "02"}}}, "then": {"properties": {"other_id": {"$ref": "ncpdp.licenses.json#/$defs/hin_id"}}}}, {"if": {"properties": {"other_id_qualifier": {"const": "03"}}}, "then": {"properties": {"other_id": {"$ref": "ncpdp.fields.json#/$defs/bin"}}}}, {"if": {"properties": {"other_id_qualifier": {"const": "04"}}}, "then": {"properties": {"other_id": {"$ref": "ncpdp.licenses.json#/$defs/naic_id"}}}}, {"if": {"properties": {"subform_resp": {"items": {"properties": {"pt_resp_amt_qualifier": {"const": "06"}}}}}}, "then": {"properties": {"subform_resp": {"maxItems": 1}}, "errorMessage": {"properties": {"subform_resp": "When Patient Responsibility Qualifier is 06, only one entry is allowed"}}}}, {"if": {"properties": {"subform_paid": {"items": {"required": ["paid_amount"], "properties": {"paid_amount": {"type": "string", "minLength": 1}}}}}}, "then": {"properties": {"subform_paid": {"items": {"required": ["paid_qualifier"], "properties": {"paid_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/342-HC"}, {"$ref": "ncpdp.ecl.json#/$defs/342-HC"}], "$comment": "payer.ncpdp_pri_payer_qualifier_id"}}, "errorMessage": {"required": "Payer Paid Amount Qualifier is required when Payer Paid Amount is used."}}}}}}, {"if": {"properties": {"subform_resp": {"items": {"required": ["pt_resp_amt"], "properties": {"pt_resp_amt": {"type": "string", "minLength": 1}}}}}}, "then": {"properties": {"subform_resp": {"items": {"required": ["pt_resp_amt_qualifier"], "properties": {"pt_resp_amt_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/351-NP"}, {"$ref": "ncpdp.ecl.json#/$defs/351-NP"}], "$comment": "payer.ncpdp_sec_claims_pr_qualifier_id"}}, "errorMessage": {"required": "Patient Responsibility Amount Qualifier is required when Patient Responsibility Amount is used."}}}}}}, {"if": {"properties": {"subform_benefit": {"items": {"required": ["benefit_stage_amount"], "properties": {"benefit_stage_amount": {"type": "string", "minLength": 1}}}}}}, "then": {"properties": {"subform_benefit": {"items": {"required": ["benefit_stage_qualifier"], "properties": {"benefit_stage_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/393-MV"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/393-MV"}], "$comment": "payer.ncpdp_sec_claims_benefit_qual_id"}}, "errorMessage": {"required": "Patient Benefit Stage Qualifier is required when Patient Benefit Stage Amount is used."}}}}}}]}