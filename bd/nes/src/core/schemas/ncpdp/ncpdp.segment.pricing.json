{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.pricing.json", "title": "NCPDP Claim Pricing Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_pricing", "properties": {"isInlineSubform": true, "transaction_code": {"type": "string", "default": "B1", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/103-A3"}, {"$ref": "ncpdp.ecl.json#/$defs/103-A3"}, {"removeProperty": true}]}, "segment_identification": {"type": "string", "default": "11", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "ing_cst_sub": {"$ref": "ncpdp.fields.json#/$defs/409-D9"}, "disp_fee_sub": {"$ref": "ncpdp.fields.json#/$defs/412-DC", "$comment": "payer.default_dispense_fee"}, "pro_svc_fee_sub": {"$ref": "ncpdp.fields.json#/$defs/477-BE"}, "pt_pd_amt_sub": {"$ref": "ncpdp.fields.json#/$defs/433-DX", "$comment": "payer.ncpdp_pt_paid_amount_dx"}, "incv_amt_sub": {"$ref": "ncpdp.fields.json#/$defs/438-E3"}, "o_amt_sub_count": {"$ref": "ncpdp.fields.json#/$defs/478-H7"}, "subform_oclaim": {"maxItems": 3, "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"o_amt_sub_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/479-H8"}, {"$ref": "ncpdp.ecl.json#/$defs/479-H8"}]}, "o_amt_sub": {"$ref": "ncpdp.fields.json#/$defs/480-H9"}}, "required": ["o_amt_sub_qualifier", "o_amt_sub"]}}, "flat_tax_amt": {"$ref": "ncpdp.fields.json#/$defs/481-HA"}, "sales_tax": {"$ref": "ncpdp.fields.json#/$defs/482-GE"}, "per_sales_tax_rate_used": {"$ref": "ncpdp.fields.json#/$defs/483-HE"}, "sales_tax_basis": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/484-JE"}, {"$ref": "ncpdp.ecl.json#/$defs/484-JE"}], "$comment": "payer.default_tax_base_id"}, "u_and_c_charge": {"$ref": "ncpdp.fields.json#/$defs/426-DQ"}, "gross_amount_due": {"$ref": "ncpdp.fields.json#/$defs/430-DU"}, "cost_basis": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/423-DN"}, {"$ref": "ncpdp.ecl.json#/$defs/423-DN"}], "$comment": "payer.base_ing_cost_on_id"}}, "required": ["segment_identification", "transaction_code"], "errorMessage": {"required": "Segment Identifier (111-AM) and Transaction Code (103-A3) are required for pricing segment."}, "allOf": [{"if": {"properties": {"transaction_code": {"const": "B1"}}}, "then": {"properties": {"ing_cst_sub": {"$ref": "ncpdp.fields.json#/$defs/409-D9"}, "gross_amount_due": {"$ref": "ncpdp.fields.json#/$defs/430-DU"}}, "required": ["ing_cst_sub", "gross_amount_due"], "errorMessage": {"required": "Ingredient Cost (409-D9) and Gross amount due (430-DU) are required on drug related claims."}}}, {"if": {"required": ["sales_tax_basis", "sales_tax"], "properties": {"sales_tax_basis": {"type": "string", "minLength": 1}, "sales_tax": {"type": "string", "minLength": 1}, "transaction_code": {"not": {"enum": ["S1", "S3"]}}}}, "then": {"required": ["per_sales_tax_rate_used"], "properties": {"per_sales_tax_rate_used": {"$ref": "ncpdp.fields.json#/$defs/483-HE"}}, "errorMessage": {"required": "Sales tax rate (483-HE) is required when tax basis or amount is used."}}}, {"if": {"required": ["per_sales_tax_rate_used", "sales_tax", "transaction_code"], "properties": {"per_sales_tax_rate_used": {"type": "string", "minLength": 1}, "sales_tax": {"type": "string", "minLength": 1}, "transaction_code": {"not": {"enum": ["S1", "S3"]}}}}, "then": {"required": ["sales_tax_basis"], "properties": {"sales_tax_basis": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/484-JE"}, {"$ref": "ncpdp.ecl.json#/$defs/484-JE"}], "$comment": "payer.default_tax_base_id"}}, "errorMessage": {"required": "Sales tax basis (484-JE) is required when tax rate or amount is used."}}}, {"if": {"properties": {"transaction_code": {"enum": ["S1", "S3"]}}}, "then": {"required": ["pro_svc_fee_sub", "gross_amount_due"], "errorMessage": {"required": "Professional service fee (477-BE) and Gross amount due (430-DU) are required on service claims."}, "properties": {"pro_svc_fee_sub": {"$ref": "ncpdp.fields.json#/$defs/477-BE"}, "gross_amount_due": {"$ref": "ncpdp.fields.json#/$defs/430-DU"}}}}]}