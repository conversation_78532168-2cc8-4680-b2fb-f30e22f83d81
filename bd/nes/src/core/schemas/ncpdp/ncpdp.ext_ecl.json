{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.ext_ecl.json", "title": "NCPDP External Codes List", "description": "Valid code entries from the External Codes List", "type": "object", "$defs": {"307-C7": {"type": "string", "enum": ["11", "12", "21", "22", "23", "24", "25", "26", "31", "32", "33", "34", "41", "42", "50", "51", "52", "53", "54", "55", "56", "60", "61", "62", "65", "71", "01", "03", "04", "05", "06", "07", "08"]}, "393-MV": {"type": "string", "enum": ["50", "60", "61", "62", "63", "70", "80", "90", "01", "02", "03", "04"]}, "363-2H": {"type": "string", "enum": ["A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "AA", "AB", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AM", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", "BA", "BL", "BO", "BP", "BR", "BU", "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "CR", "CS", "CT", "DA", "E1", "E2", "E3", "E4", "EA", "EB", "EC", "ED", "EE", "EJ", "EM", "EP", "ER", "ET", "EX", "EY", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "FA", "FB", "FC", "FP", "FQ", "FR", "FS", "FT", "FX", "FY", "G0", "G1", "G2", "G3", "G4", "G5", "G6", "G7", "G8", "G9", "GA", "GB", "GC", "GD", "GE", "GF", "GG", "GH", "GJ", "GK", "GL", "GM", "GN", "GO", "GP", "GQ", "GR", "GS", "GT", "GU", "GV", "GW", "GX", "GY", "GZ", "H9", "HA", "HB", "HC", "HD", "HE", "HF", "HG", "HH", "HI", "HJ", "HK", "HL", "HM", "HN", "HO", "HP", "HQ", "HR", "HS", "HT", "HU", "HV", "HW", "HX", "HY", "HZ", "J1", "J2", "J3", "J4", "J5", "JA", "JB", "JC", "JD", "JE", "JF", "JG", "JK", "JL", "JW", "JZ", "K0", "K1", "K2", "K3", "K4", "KA", "KB", "KC", "KD", "KE", "KF", "KG", "KH", "KI", "KJ", "KK", "KL", "KM", "KN", "KO", "KP", "KQ", "KR", "KS", "KT", "KU", "KV", "KW", "KX", "KY", "KZ", "L1", "LC", "LD", "LL", "LM", "LR", "LS", "LT", "LU", "M2", "MA", "MB", "MC", "MD", "ME", "MF", "MG", "MH", "MS", "N1", "N2", "N3", "NB", "NR", "NU", "P1", "P2", "P3", "P4", "P5", "P6", "PA", "PB", "PC", "PD", "PI", "PL", "PM", "PN", "PO", "PS", "PT", "Q0", "Q1", "Q2", "Q3", "Q4", "Q5", "Q6", "Q7", "Q8", "Q9", "QA", "QB", "QC", "QD", "QE", "QF", "QG", "QH", "QJ", "QK", "QL", "QM", "QN", "QP", "QQ", "QR", "QS", "QT", "QW", "QX", "QY", "QZ", "RA", "RB", "RC", "RD", "RE", "RI", "RR", "RT", "SA", "SB", "SC", "SD", "SE", "SF", "SG", "SH", "SJ", "SK", "SL", "SM", "SN", "SQ", "SS", "ST", "SU", "SV", "SW", "SY", "SZ", "T1", "T2", "T3", "T4", "T5", "T6", "T7", "T8", "T9", "TA", "TB", "TC", "TD", "TE", "TF", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TP", "TQ", "TR", "TS", "TT", "TU", "TV", "TW", "U1", "U2", "U3", "U4", "U5", "U6", "U7", "U8", "U9", "UA", "UB", "UC", "UD", "UE", "UF", "UG", "UH", "UJ", "UK", "UN", "UP", "UQ", "UR", "US", "V1", "V2", "V3", "V4", "V5", "V6", "V7", "V8", "V9", "VM", "VP", "X1", "X2", "X3", "X4", "X5", "XE", "XP", "XS", "XU", "ZA", "ZB", "ZC"]}, "459-ER": {"type": "string", "enum": ["01", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "AA", "AB", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AM", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", "BA", "BL", "BO", "BP", "BR", "BU", "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "CR", "CS", "CT", "DA", "E1", "E2", "E3", "E4", "EA", "EB", "EC", "ED", "EE", "EJ", "EM", "EP", "ER", "ET", "EX", "EY", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "FA", "FB", "FC", "FP", "FQ", "FR", "FS", "FT", "FX", "FY", "G0", "G1", "G2", "G3", "G4", "G5", "G6", "G7", "G8", "G9", "GA", "GB", "GC", "GD", "GE", "GF", "GG", "GH", "GJ", "GK", "GL", "GM", "GN", "GO", "GP", "GQ", "GR", "GS", "GT", "GU", "GV", "GW", "GX", "GY", "GZ", "H9", "HA", "HB", "HC", "HD", "HE", "HF", "HG", "HH", "HI", "HJ", "HK", "HL", "HM", "HN", "HO", "HP", "HQ", "HR", "HS", "HT", "HU", "HV", "HW", "HX", "HY", "HZ", "J1", "J2", "J3", "J4", "J5", "JA", "JB", "JC", "JD", "JE", "JF", "JG", "JK", "JL", "JW", "JZ", "K0", "K1", "K2", "K3", "K4", "KA", "KB", "KC", "KD", "KE", "KF", "KG", "KH", "KI", "KJ", "KK", "KL", "KM", "KN", "KO", "KP", "KQ", "KR", "KS", "KT", "KU", "KV", "KW", "KX", "KY", "KZ", "L1", "LC", "LD", "LL", "LM", "LR", "LS", "LT", "LU", "M2", "MA", "MB", "MC", "MD", "ME", "MF", "MG", "MH", "MS", "N1", "N2", "N3", "NB", "NR", "NU", "P1", "P2", "P3", "P4", "P5", "P6", "PA", "PB", "PC", "PD", "PI", "PL", "PM", "PN", "PO", "PS", "PT", "Q0", "Q1", "Q2", "Q3", "Q4", "Q5", "Q6", "Q7", "Q8", "Q9", "QA", "QB", "QC", "QD", "QE", "QF", "QG", "QH", "QJ", "QK", "QL", "QM", "QN", "QP", "QQ", "QR", "QS", "QT", "QW", "QX", "QY", "QZ", "RA", "RB", "RC", "RD", "RE", "RI", "RR", "RT", "SA", "SB", "SC", "SD", "SE", "SF", "SG", "SH", "SJ", "SK", "SL", "SM", "SN", "SQ", "SS", "ST", "SU", "SV", "SW", "SY", "SZ", "T1", "T2", "T3", "T4", "T5", "T6", "T7", "T8", "T9", "TA", "TB", "TC", "TD", "TE", "TF", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TP", "TQ", "TR", "TS", "TT", "TU", "TV", "TW", "U1", "U2", "U3", "U4", "U5", "U6", "U7", "U8", "U9", "UA", "UB", "UC", "UD", "UE", "UF", "UG", "UH", "UJ", "UK", "UN", "UP", "UQ", "UR", "US", "V1", "V2", "V3", "V4", "V5", "V6", "V7", "V8", "V9", "VM", "VP", "X1", "X2", "X3", "X4", "X5", "XE", "XP", "XS", "XU", "ZA", "ZB", "ZC"]}, "995-E2": {"type": "string", "enum": ["6064005", "10547007", "12130007", "34206005", "37737002", "54485002", "78421000", "372449004", "372459003", "372461007", "372464004", "372466002", "372467006", "372473007", "372474001", "372475000", "404815008", "417255000", "417950001", "417989007", "418418000", "418586008", "418743005", "418813001", "418851001", "418947002", "418987007", "419165009", "419243002", "419320008", "419396008", "419762003", "419874009", "419894000", "420047004", "420254004", "429817007", "445754005", "445755006", "445767008", "445913005", "447121004", "447122006", "447694001", "447964005", "448077001", "711360002", "711378007", "714743009", "718329006", "764723001", "876824003", "1259221004", "58751000052109", "58811000052103", "1611000175109"]}, "485-KE": {"type": "string", "enum": ["99", "01", "02"]}, "334-1C": {"type": "string", "enum": ["1", "2"]}}, "errorMessage": {"enum": "Value is not a valid code for the field."}}