{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.json", "title": "NCPDP <PERSON>", "description": "An NCPDP Claim Form Schema", "type": "object", "additionalProperties": false, "$comment": "ncpdp", "properties": {"bin_number": {"type": "string", "$ref": "ncpdp.fields.json#/$defs/101-A1", "$comment": "payer.bin"}, "version_number": {"type": "string", "default": "D0", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/102-A2"}, {"$ref": "ncpdp.ecl.json#/$defs/102-A2"}]}, "transaction_code": {"type": "string", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/103-A3"}, {"$ref": "ncpdp.ecl.json#/$defs/103-A3"}]}, "process_control_number": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/104-A4"}, {"$ref": "ncpdp.fields.json#/$defs/pcn"}], "$comment": "payer.pcn"}, "transaction_count": {"type": "number", "default": 1, "allOf": [{"$ref": "ncpdp.fields.json#/$defs/109-A9"}, {"$ref": "ncpdp.ecl.json#/$defs/109-A9"}]}, "svc_prov_id_qualifier": {"type": "string", "default": "11", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/202-B2"}, {"$ref": "ncpdp.ecl.json#/$defs/202-B2"}]}, "svc_prov_id": {"$ref": "ncpdp.fields.json#/$defs/201-B1"}, "date_of_service": {"$ref": "ncpdp.fields.json#/$defs/401-D1"}, "software_vendor_id": {"type": "string", "$ref": "ncpdp.fields.json#/$defs/110-AK"}, "segment_patient": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.patient.json"}}, "segment_pharmacy": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.pharmacy.json"}}, "segment_prescriber": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.prescriber.json"}}, "segment_insurance": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.insurance.json"}}, "segment_cob": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.cob.json"}}, "segment_workers_comp": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.workers_comp.json"}}, "segment_claim": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.claim.json"}}, "segment_dur": {"type": "array", "additionalProperties": false, "maxItems": 1, "items": {"isInlineSubform": true, "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "08", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "dur_pps_code_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_dur"}, "$ref": "ncpdp.fields.json#/$defs/473-7E"}, "subform_dur": {"type": "array", "maxItems": 9, "items": {"$ref": "ncpdp.segment.dur.json"}}}}}, "segment_coupon": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.coupon.json"}}, "segment_compound": {"type": "array", "additionalProperties": false, "maxItems": 1, "items": {"isInlineSubform": true, "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "10", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "comp_dsg_fm_code": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/450-EF"}, {"$ref": "ncpdp.ecl.json#/$defs/450-EF"}]}, "comp_disp_unit": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/451-EG"}, {"$ref": "ncpdp.ecl.json#/$defs/451-EG"}]}, "compound_ingredient_component_count": {"default": 0, "$ref": "ncpdp.fields.json#/$defs/447-EC"}, "subform_compound": {"type": "array", "maxItems": 25, "items": {"$ref": "ncpdp.segment.compound.json"}}}}, "required": ["segment_identification", "comp_disp_unit", "compound_ingredient_component_count"], "errorMessage": {"required": "Compound dosage form and dispensing unit indicator are required for compound claims."}}, "segment_pricing": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.pricing.json"}}, "segment_clinical": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.clinical.json"}}, "segment_docs": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.docs.json"}}, "segment_facility": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.facility.json"}}, "segment_narrative": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "ncpdp.segment.narrative.json"}}}, "required": ["bin_number", "process_control_number", "svc_prov_id_qualifier", "svc_prov_id", "date_of_service", "segment_insurance"], "errorMessage": {"required": "The following are required for all NCPDP transactions: BIN Number (101-A1), PCN (104-A4), Service Provider ID Qualifier (202-B2), Service Provider ID (202-B2), Date of Service (401-D1), and Insurance Segment"}, "allOf": [{"if": {"properties": {"svc_prov_id_qualifier": {"anyOf": [{"const": "01"}, {"const": "09"}]}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/npi"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "02"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/blue_cross"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "03"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/blue_shield"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "04"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/mcr_ptan_id"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "05"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/medicaid_id"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "06"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/upin"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "07"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/ncpdp"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "08"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/state_license"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "10"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/hin_id"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "11"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/tax_id"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "12"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/dea_id"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "15"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/hci_dea_id"}}}}, {"if": {"properties": {"svc_prov_id_qualifier": {"const": "16"}}}, "then": {"properties": {"svc_prov_id": {"$ref": "ncpdp.licenses.json#/$defs/cmea_id"}}}}, {"if": {"properties": {"transaction_code": {"enum": ["B1", "B2", "B3"]}}}, "then": {"properties": {"segment_claim": {"items": {"properties": {"rx_svc_no_ref_qualifier": {"const": "1"}}}}}}, "errorMessage": {"then": "Service reference number must be 1 for all Non-service related claims"}}, {"if": {"required": ["transaction_code"], "properties": {"transaction_code": {"enum": ["S1", "S2", "S3"]}}}, "then": {"required": ["segment_pricing"], "properties": {"segment_pricing": {"type": "array", "maxItems": 1, "items": {"$ref": "ncpdp.segment.pricing.json"}}, "segment_claim": {"items": {"properties": {"rx_svc_no_ref_qualifier": {"const": "2"}}}}}}, "errorMessage": {"then": "Service reference number must be 2 for all service related claims"}}, {"if": {"required": ["segment_claim"], "properties": {"segment_claim": {"items": {"required": ["other_coverage_code"], "properties": {"other_coverage_code": {"const": "3"}}}}}}, "then": {"required": ["segment_cob"], "properties": {"segment_cob": {"type": "array", "maxItems": 1, "items": {"properties": {"other_reject_code": {"type": "array", "minItems": 1, "items": {"type": "string"}, "errorMessage": {"minItems": "If Other Payer Insurance Code = Other Coverage Billed – claim not covered, then the reject code from the previous claim is required in the COB segment."}}}}}}, "errorMessage": {"required": "COB Segment is required if Other Coverage Code is Other Coverage Billed - claim not covered"}}}, {"if": {"required": ["segment_claim"], "properties": {"segment_claim": {"items": {"required": ["rx_svc_no_ref_qualifier", "prod_svc_id_qualifier"], "properties": {"rx_svc_no_ref_qualifier": {"const": "2"}, "prod_svc_id_qualifier": {"const": "06"}}}}}}, "then": {"required": ["segment_dur"], "properties": {"segment_claim": {"items": {"properties": {"prod_svc_id": {"type": "string", "default": "0"}}}}, "segment_dur": {"type": "object", "additionalProperties": false, "maxItems": 1, "properties": {"segment_identification": {"type": "string", "default": "08", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "dur_pps_code_count": {"$ref": "ncpdp.fields.json#/$defs/473-7E"}, "subform_dur": {"type": "array", "maxItems": 9, "items": {"$ref": "ncpdp.segment.dur.json"}}}}}, "errorMessage": {"minItems": "DUR segment is required when service billing and claim product qualifier (436-E1) is 06 (Drug Use Review/ Professional Pharmacy Service (DUR/PPS))"}}}, {"if": {"properties": {"segment_claim": {"items": {"properties": {"other_coverage_code": {"enum": ["0", "1"]}}}}}}, "then": {"properties": {"not": {"properties": {"segment_cob": {"maxItems": 1}}, "errorMessage": {"not": "COB Segment is not allowed when Other Coverage Code (308-C8) is '0' - Not Specified by patient or '1' - No other coverage"}}}}}, {"if": {"properties": {"segment_claim": {"items": {"properties": {"other_coverage_code": {"enum": ["2", "3", "4"]}}}}}}, "then": {"properties": {"segment_cob": {"minItems": 1, "errorMessage": {"minItems": "COB Segment is required when Other Coverage Code (308-C8) is set to (2 or 4) Other coverage exists or 3- Other coverage Billed"}}}}}]}