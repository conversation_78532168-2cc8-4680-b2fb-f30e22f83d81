{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.compound.json", "title": "NCPDP Claim Compound Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_compound", "properties": {"cmp_id_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/488-RE"}, {"$ref": "ncpdp.ecl.json#/$defs/488-RE"}]}, "cmp_id": {"$ref": "ncpdp.fields.json#/$defs/489-TE"}, "cmp_ing_qty": {"$ref": "ncpdp.fields.json#/$defs/448-ED"}, "cmp_ing_cost": {"$ref": "ncpdp.fields.json#/$defs/449-EE"}, "cmp_ing_cost_basis": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/490-UE"}, {"$ref": "ncpdp.ecl.json#/$defs/490-UE"}]}, "cmp_ing_md_code_count": {"default": 0, "defaultCounterLength": {"arrayField": "cmp_ing_md_code"}, "$ref": "ncpdp.fields.json#/$defs/362-2G"}, "cmp_ing_md_code": {"type": "array", "maxItems": 10, "items": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/363-2H"}, {"$ref": "ncpdp.ext_ecl.json#/$defs/363-2H"}]}}}, "required": ["cmp_id_qualifier", "cmp_id", "cmp_ing_qty"], "errorMessage": {"required": "Compound Item Qualifier (488-RE), Product ID (489-TE), and Ingredient Quantity (448-ED) are required."}, "allOf": [{"if": {"properties": {"cmp_id_qualifier": {"const": "01"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/upc"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "02"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hri"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"anyOf": [{"const": "03"}, {"const": "36"}]}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/ndc"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "04"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/hibcc"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "11"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/nappi"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "12"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/gtin"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "15"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "28"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_name_id"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "29"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_route_id"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "30"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_dose_id"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "31"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_med_id"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "32"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_gcn_seq"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "33"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/fdb_ingred_id"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "05"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/dod"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "13"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/din"}}}}, {"if": {"properties": {"cmp_id_qualifier": {"const": "45"}}}, "then": {"properties": {"cmp_id": {"$ref": "ncpdp.product_id_types.json#/$defs/di"}}}}]}