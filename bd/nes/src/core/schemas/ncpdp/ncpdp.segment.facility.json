{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.facility.json", "title": "NCPDP Claim Facility Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_facility", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "15", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "facility_id": {"$ref": "ncpdp.fields.json#/$defs/336-8C"}, "facility_name": {"$ref": "ncpdp.fields.json#/$defs/385-3Q"}, "facility_street_address": {"$ref": "ncpdp.fields.json#/$defs/386-3U"}, "facility_city_address": {"$ref": "ncpdp.fields.json#/$defs/388-5J"}, "facility_state_province_address": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/387-3V"}, {"$ref": "ncpdp.ecl.json#/$defs/387-3V"}]}, "facility_zip_postal_zone": {"$ref": "ncpdp.fields.json#/$defs/389-6D"}}, "required": ["segment_identification"]}