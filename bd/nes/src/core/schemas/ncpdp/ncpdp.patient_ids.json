{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.patient_ids.json", "title": "NCPDP Patient IDs Formats", "description": "An NCPDP Patient IDs Schema", "$defs": {"ssn": {"pattern": "^\\d{9}", "description": "Social Security Number, must be 9 digits", "errorMessage": {"pattern": "Field must be a valid Social Security Number, must be 9 digits"}}, "dod": {"pattern": "^\\d{10}$", "description": "U.S. Military ID, must be 10 digits", "errorMessage": {"pattern": "Field must be a valid U.S. Military ID, must be 10 digits"}}, "medicaid_id": {"pattern": "^[A-Z0-9]{9,12}$", "description": "Medicaid ID, must be 9-12 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid Medicaid ID, must be 9-12 alphanumeric characters"}}, "medicare_hic": {"pattern": "^[A-Z]\\d[A-Z]\\d[A-Z]\\d[A-Z]\\d[A-Z]\\d[A-Z]$", "description": "Medicare Beneficiary Identifier (MBI), must be 11 characters long", "errorMessage": {"pattern": "Field must be a valid Medicare Beneficiary Identifier (MBI), must be 11 characters long"}}}}