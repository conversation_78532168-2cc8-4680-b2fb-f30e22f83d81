{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.ecl.json", "title": "NCPDP External Codes List", "description": "Valid code entries from the External Codes List", "type": "object", "$defs": {"102-A2": {"type": "string", "enum": ["10", "20", "30", "31", "32", "33", "34", "35", "40", "41", "42", "50", "51", "52", "53", "54", "55", "56", "60", "70", "71", "80", "81", "82", "83", "90", "A0", "A1", "B0", "C0", "C1", "C2", "C3", "C4", "D0", "D1", "D2", "D3", "D4", "D5", "D6", "D7", "D8", "D9", "E0", "E1", "E2", "E3", "E4", "E5", "E6", "E7", "E8", "E9", "EB", "F2"]}, "103-A3": {"type": "string", "enum": ["B1", "B2", "B3", "C1", "C2", "C3", "D1", "E1", "N1", "N2", "N3", "P1", "P2", "P3", "P4", "S1", "S2", "S3"]}, "109-A9": {"type": "string", "enum": ["1", "2", "3", "4"]}, "111-AM": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "36", "37", "38", "39", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "112-AN": {"type": "string", "enum": ["A", "B", "C", "D", "F", "P", "Q", "R", "S"]}, "117-TR": {"type": "string", "enum": ["0", "1", "2", "3"]}, "118-TS": {"type": "string", "enum": ["11", "00", "01"]}, "123-TX": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "125-TZ": {"type": "string", "enum": ["10", "11", "12", "15", "28", "29", "30", "31", "32", "33", "34", "44", "45", "99", "01", "02", "03", "04", "06", "07", "08", "09"]}, "131-UG": {"type": "string", "enum": ["+"]}, "132-UH": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "139-UR": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9"]}, "143-UW": {"type": "string", "enum": ["0", "1", "2", "3", "4"]}, "147-U7": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "99"]}, "202-B2": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "99", "01", "02", "03", "04", "05", "06", "07", "08", "09", "00"]}, "305-C5": {"type": "string", "enum": ["0", "1", "2"]}, "306-C6": {"type": "string", "enum": ["0", "1", "2", "3", "4"]}, "308-C8": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8"]}, "309-C9": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6"]}, "318-CI": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "324-CO": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "331-CX": {"type": "string", "enum": ["10", "11", "12", "13", "14", "99", "01", "1J", "02", "03", "04", "05", "06", "07", "08", "09", "EA"]}, "334-1C": {"type": "string", "enum": ["1", "2"]}, "335-2C": {"type": "string", "enum": ["1", "2"]}, "338-5C": {"type": "string", "enum": ["98", "99", "UK", "01", "02", "03", "04", "05", "06", "07", "08", "09", "NA"]}, "339-6C": {"type": "string", "enum": ["10", "99", "01", "1C", "1D", "02", "03", "04", "05", "09"]}, "342-HC": {"type": "string", "enum": ["10", "11", "12", "01", "02", "03", "04", "05", "06", "07", "09"]}, "343-HD": {"type": "string", "enum": ["P", "C"]}, "346-HH": {"type": "string", "enum": ["99", "01", "02", "03", "04", "00"]}, "347-HJ": {"type": "string", "enum": ["99", "01", "02", "03", "04", "00"]}, "348-HK": {"type": "string", "enum": ["00", "01", "02"]}, "349-HM": {"type": "string", "enum": ["00", "01", "02"]}, "351-NP": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "357-NV": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14"]}, "360-2B": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "361-2D": {"type": "string", "enum": ["Y", "N"]}, "367-2N": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "369-2Q": {"type": "string", "enum": ["006", "007", "008", "009", "012", "015", "016", "017"]}, "371-2S": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6"]}, "373-2U": {"type": "string", "enum": ["0", "1", "2", "3"]}, "384-4X": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"]}, "387-3V": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "391-MT": {"type": "string", "enum": ["Y", "N"]}, "403-D3": {"type": "string", "enum": ["0", "1-99"]}, "406-D6": {"type": "string", "enum": ["0", "1", "2"]}, "408-D8": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]}, "418-DI": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7"]}, "419-DJ": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5"]}, "420-DK": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "99"]}, "423-DN": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "00", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "429-DT": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8"]}, "436-E1": {"type": "string", "enum": ["10", "11", "12", "13", "15", "28", "29", "30", "31", "32", "33", "34", "36", "42", "43", "44", "45", "99", "00", "01", "02", "03", "04", "06", "07", "08", "09", "05"]}, "439-E4": {"type": "string", "enum": ["AD", "AN", "AR", "AT", "CD", "CH", "CS", "DA", "DC", "DD", "DF", "DI", "DL", "DM", "DR", "DS", "ED", "ER", "EX", "HD", "IC", "ID", "LD", "LK", "LR", "MC", "MN", "MS", "MX", "NA", "NC", "ND", "NF", "NN", "NP", "NR", "NS", "OH", "PA", "PC", "PG", "PH", "PN", "PP", "PR", "PS", "RE", "RF", "SC", "SD", "SE", "SF", "SR", "SX", "TD", "TN", "TP", "UD"]}, "440-E5": {"type": "string", "enum": ["00", "AS", "CC", "DE", "DP", "FE", "GP", "M0", "MA", "MB", "MP", "MR", "PA", "PE", "PH", "PM", "P0", "PT", "R0", "RT", "SC", "SW", "TC", "TH"]}, "441-E6": {"type": "string", "enum": ["00", "1A", "1B", "1C", "1D", "1E", "1F", "1G", "1H", "1J", "1K", "2A", "2B", "3A", "3B", "3C", "3D", "3E", "3F", "3G", "3H", "3J", "3K", "3M", "3N", "4A"]}, "450-EF": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "01", "02", "03", "04", "05", "06", "07"]}, "451-EG": {"type": "string", "enum": ["1", "2", "3"]}, "453-EJ": {"type": "string", "enum": ["10", "11", "12", "13", "15", "28", "29", "30", "31", "32", "33", "38", "39", "40", "41", "42", "43", "44", "45", "99", "00", "01", "02", "03", "04", "06", "07", "08", "09", "05"]}, "455-EM": {"type": "string", "enum": ["1", "2", "3"]}, "461-EU": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]}, "462-EV": {"type": "string", "enum": ["91100000000", "91100000001", "91100000002", "91100000003", "91100000004", "91100000005"]}, "465-EY": {"type": "string", "enum": ["99", "01", "02", "03", "04", "05", "06", "07"]}, "466-EZ": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "17", "18", "99", "01", "02", "03", "04", "05", "06", "08", "09", "00", "07"]}, "468-2E": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "99", "01", "02", "03", "04", "05", "06", "08", "09", "00", "07"]}, "474-8E": {"type": "string", "enum": ["0", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22"]}, "475-J9": {"type": "string", "enum": ["11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "35", "37", "38", "39", "40", "41", "45", "46", "99", "01", "02", "03", "04", "07", "08", "09", "05"]}, "479-H8": {"type": "string", "enum": ["11", "99", "01", "02", "03", "04", "09"]}, "484-JE": {"type": "string", "enum": ["02", "03", "04"]}, "488-RE": {"type": "string", "enum": ["11", "12", "13", "15", "28", "29", "30", "31", "32", "33", "45", "99", "01", "02", "03", "04", "05"]}, "490-UE": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "00", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "492-WE": {"type": "string", "enum": ["00", "01", "02", "03", "04", "05", "07", "08"]}, "496-H2": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "99", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "497-H3": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "498-PA": {"type": "string", "enum": ["1", "2", "3"]}, "498-PD": {"type": "string", "enum": ["ME", "PR", "PL"]}, "498-PJ": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "501-F1": {"type": "string", "enum": ["A", "R"]}, "511-FB": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "19", "20", "21", "22", "23", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "38", "39", "40", "41", "42", "43", "44", "46", "50", "51", "52", "53", "54", "55", "56", "58", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "95", "96", "97", "98", "99", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "370", "371", "372", "373", "374", "375", "376", "377", "378", "379", "380", "381", "382", "383", "384", "385", "386", "387", "388", "389", "390", "391", "392", "393", "394", "395", "396", "397", "398", "399", "400", "401", "402", "403", "404", "405", "406", "407", "408", "409", "410", "411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439", "440", "441", "442", "443", "444", "445", "446", "447", "448", "449", "450", "451", "452", "453", "454", "455", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "627", "628", "629", "630", "631", "632", "633", "634", "635", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "01", "02", "03", "04", "05", "06", "07", "08", "09", "1C", "1E", "1K", "1R", "1S", "1T", "1U", "1V", "1X", "1Y", "1Z", "2A", "2B", "2C", "2D", "2E", "2G", "2H", "2J", "2K", "2M", "2N", "2P", "2Q", "2R", "2S", "2T", "2U", "2V", "2W", "2X", "2Z", "3A", "3B", "3C", "3D", "3E", "3F", "3G", "3H", "3J", "3K", "3M", "3N", "3P", "3Q", "3R", "3S", "3T", "3U", "3V", "3W", "3X", "3Y", "4B", "4C", "4D", "4E", "4G", "4H", "4J", "4K", "4M", "4N", "4P", "4Q", "4R", "4S", "4T", "4W", "4X", "4Y", "4Z", "5C", "5E", "5J", "6C", "6D", "6E", "6G", "6H", "6J", "6K", "6M", "6N", "6P", "6Q", "6R", "6S", "6T", "6U", "6V", "6W", "6X", "6Y", "6Z", "7A", "7B", "7C", "7D", "7E", "7G", "7H", "7J", "7K", "7M", "7N", "7P", "7Q", "7R", "7S", "7T", "7U", "7V", "7W", "7X", "7Y", "7Z", "8A", "8B", "8C", "8D", "8E", "8G", "8H", "8J", "8K", "8M", "8N", "8P", "8Q", "8R", "8S", "8T", "8U", "8W", "8X", "8Y", "8Z", "9B", "9C", "9D", "9E", "9G", "9H", "9J", "9K", "9M", "9N", "9P", "9Q", "9R", "9S", "9T", "9U", "9V", "9W", "9X", "9Y", "9Z", "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AJ", "AK", "AM", "AQ", "A1", "A2", "A5", "A6", "A7", "A9", "BA", "BB", "BC", "BE", "BM", "B2", "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "CR", "CW", "CX", "CY", "CZ", "DC", "DN", "DQ", "DR", "DT", "DU", "DV", "DX", "DY", "DZ", "EA", "EB", "EC", "ED", "EE", "EF", "EG", "EJ", "EK", "EM", "EN", "EP", "ER", "ET", "EU", "EV", "EY", "EZ", "E1", "E2", "E3", "E4", "E5", "E6", "E7", "E8", "E9", "FO", "GE", "G1", "G2", "G4", "G5", "G6", "G7", "G8", "G9", "HA", "HB", "HC", "HD", "HE", "HF", "HG", "HN", "H1", "H2", "H3", "H4", "H5", "H6", "H7", "H8", "H9", "JE", "J9", "KE", "K5", "M1", "M2", "M3", "M4", "M5", "M6", "M7", "M8", "ME", "MG ", "MH", "MJ", "MK", "MM", "MN", "MP", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MZ", "NE", "NN", "NP", "NQ", "NR", "NU", "NV", "NX", "N1", "N3", "N4", "N5", "N6", "N7", "N8", "N9", "PA", "PB", "PC", "PD", "PE", "PF", "PG", "PH", "PJ", "PK", "PM", "PN", "PP", "PQ", "PR", "PS", "PT", "PV", "PW", "PX", "PY", "PZ", "P0", "P1", "P2", "P3", "P4", "P5", "P6", "P7", "P8", "P9", "RA", "RB", "RC", "RD", "RE", "RF", "RG", "RH", "RJ", "RK", "RL", "RM", "RN", "RP", "RS", "RT", "RU", "R0", "R1", "R2", "R3", "R4", "R5", "R6", "R7", "R8", "R9", "S0", "S1", "S2", "S3", "S4", "S5", "S6", "S7", "S8", "S9", "SE", "SF", "SG", "SH", "SW", "TE", "TN", "TP", "TQ", "TR", "TS", "TT", "TU", "TV", "TW", "TX", "TY", "TZ", "T0", "T1", "T2", "T3", "T4", "UA", "UE", "UU", "UZ", "U7", "VA", "VB", "VC", "VD", "VE", "WE", "W9", "XE", "XZ", "X1", "X2", "X3", "X4", "X5", "X6", "X7", "X8", "X9", "X0", "YA", "YB", "YC", "YD", "YE", "YF", "YG", "YH", "YJ", "YK", "YM", "YN", "YP", "YQ", "YR", "YS", "YT", "YU", "YV", "YW", "YX", "YY", "YZ", "Y0", "Y1", "Y2", "Y3", "Y4", "Y5", "Y6", "Y7", "Y8", "Y9", "Z0", "Z1", "Z2", "Z3", "Z4", "Z5", "Z6", "Z7", "Z8", "Z9", "ZB", "ZC", "ZD", "ZE", "ZF", "ZK", "ZM", "ZN", "ZP", "ZQ", "ZS", "ZT", "ZU", "ZV", "ZW", "ZX", "ZY", "ZZ", "A3", "A4", "1W", "7F", "8V", "EW", "EX", "RV", "ZA"]}, "522-FM": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24"]}, "528-FS": {"type": "string", "enum": ["1", "2", "3", "9"]}, "529-FT": {"type": "string", "enum": ["0", "1", "2", "3"]}, "532-FW": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7"]}, "533-FX": {"type": "string", "enum": ["0", "1", "2"]}, "548-6F": {"type": "string", "enum": ["001", "002", "003", "004", "005", "006", "007", "008", "009", "010", "011", "012", "013", "014", "015", "016", "017", "018", "019", "020", "021", "022", "023", "024", "025", "026", "027", "028", "029", "030", "031", "032", "033", "034", "035", "036", "037", "038", "039", "040", "041", "042", "043", "044", "045", "046"]}, "552-AP": {"type": "string", "enum": ["11", "12", "13", "14", "15", "16", "17", "18", "19", "28", "29", "30", "31", "32", "33", "37", "42", "43", "44", "45", "99", "01", "02", "03", "04", "05"]}, "557-AV": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6"]}, "561-AZ": {"type": "string", "enum": ["02", "03", "04", "01"]}, "564-J3": {"type": "string", "enum": ["11", "99", "01", "02", "03", "04", "09"]}, "568-J7": {"type": "string", "enum": ["99", "01", "02", "03", "04", "05"]}, "573-4V": {"type": "string", "enum": ["99", "01", "02", "03", "04", "00"]}, "579-XX": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "99", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "581-XZ": {"type": "string", "enum": ["01", "02"]}, "586-YP": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "591-YU": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "99"]}, "593-YW": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "595-YY": {"type": "string", "enum": ["0", "1", "2"]}, "600-28": {"type": "string", "enum": ["EA", "GM", "ML"]}, "675-Y3": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "680-ZB": {"type": "string", "enum": ["1"]}, "729-TA": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "931-F8": {"type": "string", "enum": ["D", "Y"]}, "934-GC": {"type": "string", "enum": ["DL", "DS", "FL", "QY"]}, "935-GF": {"type": "string", "enum": ["CM", "CQ", "CY", "DY", "LT", "PD", "SP"]}, "943-GQ": {"type": "string", "enum": ["D", "Y"]}, "996-G1": {"type": "string", "enum": ["10", "11", "99", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "997-G2": {"type": "string", "enum": ["Y", "N"]}, "A22-YR": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "A23-YS": {"type": "string", "enum": ["99", "01", "02", "03", "04", "05", "06"]}, "A24-ZK": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "A25-ZM": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "99", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "A27-ZQ": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "A28-ZR": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "99"]}, "A29-ZS": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "30", "99"]}, "A45-1R": {"type": "string", "enum": ["Y", "N"]}, "B45-8H": {"type": "string", "enum": ["99", "01", "02", "03"]}, "B46-8J": {"type": "string", "enum": ["99", "01", "02", "03", "04", "05", "06"]}, "B47-8K": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "99", "01", "02", "03", "04", "05", "06", "07", "08", "09"]}, "B49-8N": {"type": "string", "enum": ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "56", "57", "58", "59", "60", "AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]}, "B53-8S": {"type": "string", "enum": ["1", "2", "3", "99"]}, "B95-3Z": {"type": "string", "enum": ["1", "2"]}, "C02-4P": {"type": "string", "enum": ["3"]}, "C47-9T": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "99"]}, "C48-9U": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "30", "99"]}, "C51-9X": {"type": "string", "enum": ["1", "2", "3", "4", "50", "51", "61", "62", "63", "70", "80", "90"]}, "C56-AC": {"type": "string", "enum": ["1", "2"]}, "C58-AE": {"type": "string", "enum": ["1", "2"]}, "C60-AG": {"type": "string", "enum": ["1", "10", "20", "30", "40", "50", "60"]}, "C63-A5": {"type": "string", "enum": ["0", "1", "NA"]}, "C66-BA": {"type": "string", "enum": ["1", "2", "3", "4", "5"]}, "C70-BF": {"type": "string", "enum": ["1", "2", "3", "4"]}, "C71-BG": {"type": "string", "enum": ["1", "2", "3", "4", "5"]}, "C73-BJ": {"type": "string", "enum": ["0", "1", "2", "3", "NA"]}, "C80-G8": {"type": "string", "enum": ["1", "2", "3", "4"]}, "C84-KA": {"type": "string", "enum": ["1", "2", "3", "4"]}, "C85-KB": {"type": "string", "enum": ["1", "2", "3", "4"]}, "C88-KF": {"type": "string", "enum": ["0", "1", "2", "3", "4", "5"]}, "C90-KH": {"type": "string", "enum": ["1", "2", "3"]}, "C91-KK": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"]}, "C95-KQ": {"type": "string", "enum": ["1", "2", "3", "4", "5", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17"]}, "C97-KS": {"type": "string", "enum": ["COST", "EGWP", "LNET", "MAPD", "MMP", "PACE", "PDP"]}, "C98-KT": {"type": "string", "enum": ["1", "2"]}, "C99-KU": {"type": "string", "enum": ["1", "2", "3", "4", "5"]}, "D51-P7": {"type": "string", "enum": ["1", "2", "3"]}, "D52-P8": {"type": "string", "enum": ["1", "2", "3"]}, "D63-RN": {"type": "string", "enum": ["AA", "AB"]}, "D62-RM": {"type": "string", "enum": ["1", "2", "3"]}, "D61-RL": {"type": "string", "enum": ["AA", "AB"]}, "D20-M2": {"type": "string", "enum": ["MDL", "MDS", "MFL", "MQY"]}, "D25-M7": {"type": "string", "enum": ["RDS", "RFL", "RQY"]}, "D42-PV": {"type": "string", "enum": ["1", "2", "3", "4", "5", "9"]}, "D43-PZ": {"type": "string", "enum": ["1", "2", "3", "4"]}, "D45-P1": {"type": "string", "enum": ["1", "2", "3"]}, "D46-P2": {"type": "string", "enum": ["1", "2", "3", "4", "5"]}, "D22-M4": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7"]}, "D32-MS": {"type": "string", "enum": ["1"]}, "D40-PN": {"type": "string", "enum": ["1", "2"]}, "D50-P6": {"type": "string", "enum": ["BEHAVIORAL", "DENTAL", "DME", "MEDICAL", "RX", "VISION", "UNKNOWN"]}, "D41-PQ": {"type": "string", "enum": ["CP", "CS", "MX", "CC", "RP", "CE"]}, "D17-K8": {"type": "string", "enum": ["AA", "AB", "AC"]}}, "errorMessage": {"enum": "Value is not a valid code for the field."}}