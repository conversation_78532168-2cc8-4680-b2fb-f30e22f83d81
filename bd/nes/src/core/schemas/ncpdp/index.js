"use strict";
const { Schema } = require("../index");
const CustomNCPDPKeywords = {
    // Converts MM/DD/YYYY into YYYYMMDD
    TRANS_DATE: "transformNCPDPDate",
    // Converts HH:mm a into HHMM
    TRANS_TIME: "transformNCPDPTime",
};

module.exports = class SchemaValidatorClass extends Schema {
    constructor() {
        super();
        this.keyword = "ncpdp";
    }

    async init() {
        return this;
    }

    async getCompiledSchemas(ajv) {
        // Primary NCPDP Schemas
        const claimsSchema = require("./ncpdp.segment.claim.json");
        const cobSchema = require("./ncpdp.segment.cob.json");
        const clinicalSchema = require("./ncpdp.segment.clinical.json");
        const compoundSchema = require("./ncpdp.segment.compound.json");
        const couponSchema = require("./ncpdp.segment.coupon.json");
        const docsSchema = require("./ncpdp.segment.docs.json");
        const durSchema = require("./ncpdp.segment.dur.json");
        const facilitySchema = require("./ncpdp.segment.facility.json");
        const insuranceSchema = require("./ncpdp.segment.insurance.json");
        const narrativeSchema = require("./ncpdp.segment.narrative.json");
        const patientSchema = require("./ncpdp.segment.patient.json");
        const pharmacySchema = require("./ncpdp.segment.pharmacy.json");
        const prescriberSchema = require("./ncpdp.segment.prescriber.json");
        const pricingSchema = require("./ncpdp.segment.pricing.json");
        const workersCompSchema = require("./ncpdp.segment.workers_comp.json");

        // Support Schemas
        const eclSchema = require("./ncpdp.ecl.json");
        const extEclSchema = require("./ncpdp.ext_ecl.json");
        const fieldsSchema = require("./ncpdp.fields.json");
        const dxCodesSchema = require("./ncpdp.dx_code_sets.json");
        const insuranceIdsSchema = require("./ncpdp.insurance_ids.json");
        const licensesSchema = require("./ncpdp.licenses.json");
        const patientIdsSchema = require("./ncpdp.patient_ids.json");
        const productTypesSchema = require("./ncpdp.product_id_types.json");

        // Register primary Schemas
        ajv.addSchema(claimsSchema, "ncpdp.segment.claim.json");
        ajv.addSchema(cobSchema, "ncpdp.segment.cob.json");
        ajv.addSchema(clinicalSchema, "ncpdp.segment.clinical.json");
        ajv.addSchema(compoundSchema, "ncpdp.segment.compound.json");
        ajv.addSchema(couponSchema, "ncpdp.segment.coupon.json");
        ajv.addSchema(docsSchema, "ncpdp.segment.docs.json");
        ajv.addSchema(durSchema, "ncpdp.segment.dur.json");
        ajv.addSchema(facilitySchema, "ncpdp.segment.facility.json");
        ajv.addSchema(insuranceSchema, "ncpdp.segment.insurance.json");
        ajv.addSchema(narrativeSchema, "ncpdp.segment.narrative.json");
        ajv.addSchema(patientSchema, "ncpdp.segment.patient.json");
        ajv.addSchema(pharmacySchema, "ncpdp.segment.pharmacy.json");
        ajv.addSchema(prescriberSchema, "ncpdp.segment.prescriber.json");
        ajv.addSchema(pricingSchema, "ncpdp.segment.pricing.json");
        ajv.addSchema(workersCompSchema, "ncpdp.segment.workers_comp.json");

        // Register Support Schemas
        ajv.addSchema(eclSchema, "ncpdp.ecl.json");
        ajv.addSchema(extEclSchema, "ncpdp.ext_ecl.json");
        ajv.addSchema(fieldsSchema, "ncpdp.fields.json");
        ajv.addSchema(dxCodesSchema, "ncpdp.dx_code_sets.json");
        ajv.addSchema(insuranceIdsSchema, "ncpdp.insurance_ids.json");
        ajv.addSchema(licensesSchema, "ncpdp.licenses.json");
        ajv.addSchema(patientIdsSchema, "ncpdp.patient_ids.json");
        ajv.addSchema(productTypesSchema, "ncpdp.product_id_types.json");

        const ncpdpSchema = require("./ncpdp.json");
        return ajv.compile(ncpdpSchema);
    }

    async addCustomKeywords(ajv) {
        ajv.addKeyword({
            keyword: CustomNCPDPKeywords.TRANS_DATE,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema, parentSchema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    if (typeof kwVal === "string" && schema === true) {
                        // Check if the date is in MM/DD/YYYY format
                        const regex =
                            /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/(\d{4})$/;
                        if (regex.test(kwVal)) {
                            // Transform MM/DD/YYYY to YYYYMMDD
                            const [month, day, year] = kwVal.split("/");
                            parentData[parentDataProperty] =
                                `${year}${month}${day}`;
                        }
                    }
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });

        ajv.addKeyword({
            keyword: CustomNCPDPKeywords.TRANS_TIME,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema, parentSchema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const regex = /^(0[1-9]|1[0-2]):([0-5][0-9]) ([AP][M])$/i;
                    if (regex.test(kwVal)) {
                        // Transform hh:mm a to HHMM
                        const [time, period] = kwVal.split(" ");
                        // eslint-disable-next-line prefer-const
                        let [hours, minutes] = time.split(":");
                        hours = parseInt(hours, 10);
                        if (period.toUpperCase() === "PM" && hours !== 12) {
                            hours += 12;
                        } else if (
                            period.toUpperCase() === "AM" &&
                            hours === 12
                        ) {
                            hours = 0;
                        }
                        const formattedTime = `${hours.toString().padStart(2, "0")}${minutes}`;
                        parentData[parentDataProperty] = formattedTime;
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });
    }

    async addCustomFormats(ajv) {
        return;
    }
};
