{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.fields.json", "title": "NCPDP Field Formats", "description": "An NCPDP Field Formats Schema", "type": "object", "$defs": {"93": {"allOf": [{"padZero": 3}, {"maxLength": 3, "pattern": "^\\d{1,3}$", "errorMessage": {"pattern": "Field must be a number with up to 3 digits"}}]}, "96": {"allOf": [{"padZero": 6}, {"maxLength": 6, "pattern": "^\\d{1,6}$", "errorMessage": {"pattern": "Field must be a number with up to 6 digits"}}]}, "911": {"allOf": [{"padZero": 11}, {"maxLength": 11, "pattern": "^\\d{1,11}$", "errorMessage": {"pattern": "Field must be a number with up to 11 digits"}}]}, "912": {"allOf": [{"padZero": 12}, {"maxLength": 12, "pattern": "^\\d{1,12}$", "errorMessage": {"pattern": "Field must be a number with up to 12 digits"}}]}, "93v99": {"allOf": [{"toFixed": 2}, {"padZeroDecimal": {"integerPart": 3, "decimalPart": 2}}, {"maxLength": 6, "pattern": "^\\d{1,3}\\.\\d{2}$", "errorMessage": {"pattern": "Field must be in the format of up to 3 digits followed by 2 digits after the decimal"}}]}, "93v4": {"allOf": [{"toFixed": 4}, {"padZeroDecimal": {"integerPart": 3, "decimalPart": 4}}, {"maxLength": 8, "pattern": "^\\d{1,3}\\.\\d{4}$", "errorMessage": {"pattern": "Field must be in the format of up to 3 digits followed by 4 digits after the decimal"}}]}, "96v99": {"allOf": [{"toFixed": 2}, {"padZeroDecimal": {"integerPart": 6, "decimalPart": 2}}, {"maxLength": 9, "pattern": "^\\d{1,6}\\.\\d{2}$", "errorMessage": {"pattern": "Field must be in the format of up to 6 digits followed by 2 digits after the decimal"}}]}, "97v999": {"allOf": [{"toFixed": 3}, {"padZeroDecimal": {"integerPart": 7, "decimalPart": 3}}, {"maxLength": 11, "pattern": "^\\d{1,7}\\.\\d{3}$", "errorMessage": {"pattern": "Field must be in the format of up to 7 digits followed by 3 digits after the decimal"}}]}, "98v99": {"allOf": [{"toFixed": 2}, {"padZeroDecimal": {"integerPart": 8, "decimalPart": 2}}, {"maxLength": 11, "pattern": "^\\d{1,8}\\.\\d{2}$", "errorMessage": {"pattern": "Field must be in the format of up to 8 digits followed by 2 digits after the decimal"}}]}, "99v99": {"allOf": [{"toFixed": 2}, {"padZeroDecimal": {"integerPart": 9, "decimalPart": 2}}, {"maxLength": 12, "pattern": "^\\d{1,9}\\.\\d{2}$", "errorMessage": {"pattern": "Field must be in the format of up to 9 digits followed by 2 digits after the decimal"}}]}, "x1": {"allOf": [{"trimMaxLength": 1}, {"toUpperCase": true}, {"maxLength": 1, "pattern": "^.{1}$", "errorMessage": {"pattern": "Field must be exactly 1 character."}}]}, "x2": {"allOf": [{"trimMaxLength": 2}, {"toUpperCase": true}, {"maxLength": 2, "pattern": "^.{2}$", "errorMessage": {"pattern": "Field must be exactly 2 characters."}}]}, "x3": {"allOf": [{"trimMaxLength": 3}, {"toUpperCase": true}, {"maxLength": 3, "pattern": "^.{1,3}$", "errorMessage": {"pattern": "Field must be up to 3 characters."}}]}, "x8": {"allOf": [{"trimMaxLength": 8}, {"toUpperCase": true}, {"maxLength": 8, "pattern": "^.{1,8}$", "errorMessage": {"pattern": "Field can be up to 8 characters."}}]}, "x10": {"allOf": [{"trimMaxLength": 10}, {"toUpperCase": true}, {"maxLength": 10, "pattern": "^.{1,10}$", "errorMessage": {"pattern": "Field can be up to 10 characters."}}]}, "x12": {"allOf": [{"trimMaxLength": 12}, {"toUpperCase": true}, {"maxLength": 12, "pattern": "^.{1,12}$", "errorMessage": {"pattern": "Field can be up to 12 characters."}}]}, "x15": {"allOf": [{"trimMaxLength": 15}, {"toUpperCase": true}, {"maxLength": 15, "pattern": "^.{1,15}$", "errorMessage": {"pattern": "Field can be up to 15 characters."}}]}, "x19": {"allOf": [{"trimMaxLength": 19}, {"toUpperCase": true}, {"maxLength": 19, "pattern": "^.{1,19}$", "errorMessage": {"pattern": "Field can be up to 19 characters."}}]}, "x20": {"allOf": [{"trimMaxLength": 20}, {"toUpperCase": true}, {"maxLength": 20, "pattern": "^.{1,20}$", "errorMessage": {"pattern": "Field can be up to 20 characters."}}]}, "x30": {"allOf": [{"trimMaxLength": 30}, {"toUpperCase": true}, {"maxLength": 30, "pattern": "^.{1,30}$", "errorMessage": {"pattern": "Field can be up to 30 characters."}}]}, "x40": {"allOf": [{"trimMaxLength": 40}, {"toUpperCase": true}, {"maxLength": 40, "pattern": "^.{1,40}$", "errorMessage": {"pattern": "Field can be up to 40 characters."}}]}, "x65": {"allOf": [{"trimMaxLength": 65}, {"toUpperCase": true}, {"maxLength": 65, "pattern": "^.{1,65}$", "errorMessage": {"pattern": "Field can be up to 65 characters."}}]}, "x100": {"allOf": [{"trimMaxLength": 100}, {"toUpperCase": true}, {"maxLength": 100, "pattern": "^.{1,100}$", "errorMessage": {"pattern": "Field can be up to 100 characters."}}]}, "x200": {"allOf": [{"trimMaxLength": 200}, {"toUpperCase": true}, {"maxLength": 200, "pattern": "^.{1,200}$", "errorMessage": {"pattern": "Field can be up to 200 characters."}}]}, "x500": {"allOf": [{"trimMaxLength": 500}, {"toUpperCase": true}, {"maxLength": 500, "pattern": "^.{1,500}$", "errorMessage": {"pattern": "Field can be up to 500 characters."}}]}, "max3": {"maxLength": 1, "pattern": "^[1-3]$", "errorMessage": {"pattern": "Field must be a number between 1 and 3."}}, "max4": {"maxLength": 1, "pattern": "^[1-4]$", "errorMessage": {"pattern": "Field must be a number between 1 and 4."}}, "max5": {"maxLength": 1, "pattern": "^[1-5]$", "errorMessage": {"pattern": "Field must be a number between 1 and 5."}}, "max6": {"maxLength": 1, "pattern": "^[1-6]$", "errorMessage": {"pattern": "Field must be a number between 1 and 6."}}, "max9": {"maxLength": 1, "pattern": "^[1-9]$", "errorMessage": {"pattern": "Field must be a number between 1 and 9."}}, "max10": {"maxLength": 2, "pattern": "^([1-9]|10)$", "errorMessage": {"pattern": "Field must be a number between 1 and 10."}}, "max25": {"maxLength": 2, "pattern": "^(?:[1-9]|1[0-9]|2[0-5])$", "errorMessage": {"pattern": "Field must be a number between 1 and 25."}}, "max50": {"maxLength": 2, "pattern": "^([1-9]|[1-4][0-9]|50)$", "errorMessage": {"pattern": "Field must be a number between 1 and 50."}}, "max99": {"maxLength": 2, "pattern": "^([1-9]|[1-9][0-9])$", "errorMessage": {"pattern": "Field must be a number between 1 and 99."}}, "maxDays": {"maxLength": 3, "pattern": "^(?:36[0-5]|[1-2]?[0-9]{1,2}|3[0-5][0-9])$", "errorMessage": {"pattern": "Field must be a number between 1 and 365."}}, "date": {"allOf": [{"transformNCPDPDate": true}, {"maxLength": 8, "pattern": "^\\d{4}\\d{2}\\d{2}$", "errorMessage": {"pattern": "Field must be in the format YYYYMMDD."}}]}, "time": {"allOf": [{"transformNCPDPTime": true}, {"maxLength": 4, "pattern": "^([0-1]\\d|2[0-3])([0-5]\\d)$", "errorMessage": {"pattern": "Field must be in the format HHMM."}}]}, "email": {"maxLength": 80, "pattern": "^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$", "errorMessage": {"pattern": "Field must be a valid email address."}}, "url": {"maxLength": 255, "pattern": "^(?=.{1,255}$)(https?://)?([\\da-z\\.-]+)\\.([a-z\\.]{2,6})([\\/\\w \\.-]*)*\\/?$", "errorMessage": {"pattern": "Field must be a valid URL."}}, "phone10": {"allOf": [{"justNumbers": true}, {"maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Phone number must be a 10-digit phone number."}}]}, "phone18": {"allOf": [{"justNumbers": true}, {"maxLength": 18, "pattern": "^\\d{10}(\\d{1,8})?$", "errorMessage": {"pattern": "Phone number must be a 10-digit phone number optionally followed by up to 8 additional digits for an extension."}}]}, "bin": {"allOf": [{"justNumbers": true}, {"maxLength": 6, "minLength": 6, "pattern": "^\\d{6}$", "errorMessage": {"pattern": "BIN number must be a 6-digit."}}]}, "pcn": {"maxLength": 15, "minLength": 2, "pattern": "^[a-zA-Z0-9]{2,15}$", "errorMessage": {"pattern": "Pharmacy Claim Number (PCN) number must be a 2-15 characters long."}}, "512-FC": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount in dollars met by the patient/family in a deductible plan.", "metadata": {"field": "512-FC", "format": "9(6)v99"}}]}, "369-2Q": {"type": "string", "description": "Unique identifier for the data being submitted.", "metadata": {"field": "369-2Q", "format": "x(3)", "reference": "ECL"}}, "526-FQ": {"type": "string", "description": "Free text message.", "maxLength": 40, "pattern": "^.{40}$", "metadata": {"field": "526-FQ", "format": "x(40)"}}, "131-UG": {"type": "string", "description": "Indicates continuity of the text found in the current repetition of Additional Message Information (526-FQ) with the text found in the next repetition that follows.", "metadata": {"field": "131-U<PERSON>", "format": "x(1)", "reference": "ECL"}}, "130-UF": {"allOf": [{"$ref": "#/$defs/max25"}, {"type": "string", "description": "Count of the Additional Message Information (526-FQ) occurrences that follow.", "metadata": {"field": "130-U<PERSON>", "format": "9(2)"}}]}, "132-UH": {"allOf": [{"$ref": "#/$defs/x2"}, {"type": "string", "description": "Format qualifier of the Additional Message Information (526-FQ) that follows. Each value may occur only once per transaction and values must be ordered sequentially (numeric characters precede alpha characters, i.e., 0-9, A-Z).", "metadata": {"field": "132-U<PERSON>", "format": "x(2)", "reference": "ECL"}}]}, "330-CW": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Person identifier to be used for controlled product reporting. Identifier may be that of the patient or the person picking up the prescription as required by the governing body.", "metadata": {"field": "330-CW", "format": "x(20)"}}]}, "517-FH": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from a patient that is included in Patient Pay Amount (505-F5) that is applied to a periodic deductible.", "metadata": {"field": "517-FH", "format": "9(6)v99"}}]}, "137-UP": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the patient being in the coverage gap (for example Medicare Part D Coverage Gap (donut hole)). A coverage gap is defined as the period or amount during which the previous coverage ends and before an additional coverage begins.", "metadata": {"field": "137-UP", "format": "9(6)v99"}}]}, "571-NZ": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the processing fee imposed by the processor.", "metadata": {"field": "571-NZ", "format": "9(6)v99"}}]}, "134-UK": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the patients selection of a Brand product.", "metadata": {"field": "134-UK", "format": "9(6)v99"}}]}, "136-UN": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the patients selection of a Brand Non- Preferred Formulary product.", "metadata": {"field": "136-UN", "format": "9(6)v99"}}]}, "135-UM": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the patients selection of a Non-Preferred Formulary product.", "metadata": {"field": "135-U<PERSON>", "format": "9(6)v99"}}]}, "133-UJ": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the patients provider network selection.", "metadata": {"field": "133-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "523-FN": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to sales tax paid.", "metadata": {"field": "523-F<PERSON>", "format": "9(6)v99"}}]}, "520-FK": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to the patient exceeding a periodic benefit maximum.", "metadata": {"field": "520-FK", "format": "9(6)v99"}}]}, "572-4U": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (5∅5-F5) that is due to a per prescription coinsurance.", "metadata": {"field": "572-4U", "format": "9(6)v99"}}]}, "518-FI": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount to be collected from the patient that is included in Patient Pay Amount (505-F5) that is due to a per prescription copay.", "metadata": {"field": "518-FI", "format": "9(6)v99"}}]}, "548-6F": {"type": "string", "description": "Message code, on an approved claim/service, communicating the need for an additional follow-up.", "metadata": {"field": "548-6F", "format": "x(3)", "reference": "ECL"}}, "547-5F": {"allOf": [{"$ref": "#/$defs/max5"}, {"type": "string", "description": "Count of the Approved Message Code (548-6F) occurrences.", "metadata": {"field": "547-5F", "format": "9(1)"}}]}, "457-EP": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date of the Associated Prescription/Service Reference Number (456-EN).", "metadata": {"field": "457-EP", "format": "9(8)"}}]}, "456-EN": {"allOf": [{"$ref": "#/$defs/912"}, {"type": "string", "description": "Related Prescription/Service Reference Number (402-D2) to which the service is associated.", "metadata": {"field": "456-E<PERSON>", "format": "9(12)"}}]}, "503-F3": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Number assigned by the processor to identify an authorized transaction.", "metadata": {"field": "503-F3", "format": "x(20)"}}]}, "498-PH": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Free-form text for city name.", "metadata": {"field": "498-P<PERSON>", "format": "x(20)"}}]}, "498-PE": {"allOf": [{"$ref": "#/$defs/x12"}, {"type": "string", "description": "First name of the patients authorized representative.", "metadata": {"field": "498-P<PERSON>", "format": "x(12)"}}]}, "498-PF": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Last name of the patients authorized representative.", "metadata": {"field": "498-PF", "format": "x(15)"}}]}, "498-PJ": {"type": "string", "description": "Standard State/Province code as defined by appropriate government agency.", "metadata": {"field": "498-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "498-PG": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Free-form text for address information.", "metadata": {"field": "498-P<PERSON>", "format": "x(30)"}}]}, "498-PK": {"allOf": [{"justNumbers": true}, {"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code defining international postal zone excluding punctuation and blanks (zip code for US).", "metadata": {"field": "498-<PERSON><PERSON>", "format": "x(15)"}}]}, "573-4V": {"type": "string", "description": "Code indicating how the Coinsurance reimbursement amount was calculated for Patient Pay Amount (505-F5).", "metadata": {"field": "573-4V", "format": "x(2)", "reference": "ECL"}}, "347-HJ": {"type": "string", "description": "Code indicating how the Copay reimbursement amount was calculated for Patient Pay Amount (505-F5).", "metadata": {"field": "347-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "346-HH": {"type": "string", "description": "Code indicating how the reimbursement amount was calculated for Dispensing <PERSON><PERSON> (507-F7).", "metadata": {"field": "346-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "348-HK": {"type": "string", "description": "Code indicating how the reimbursement amount was calculated for Flat Sales Tax Amount Paid (558-AW).", "metadata": {"field": "348-HK", "format": "x(2)", "reference": "ECL"}}, "349-HM": {"type": "string", "description": "Code indicating how the reimbursement amount was calculated for Percentage Sales Tax Amount Paid (559- AX).", "metadata": {"field": "349-HM", "format": "x(2)", "reference": "ECL"}}, "423-DN": {"type": "string", "description": "Code indicating the method by which 'Ingredient Cost Submitted' (Field 409-D9) was calculated.", "metadata": {"field": "423-D<PERSON>", "format": "x(2)", "reference": "ECL"}}, "522-FM": {"type": "string", "description": "Code identifying how the reimbursement amount was calculated for Ingredient Cost Paid (506-F6).", "metadata": {"field": "522-FM", "format": "x(2)", "reference": "ECL"}}, "498-PD": {"type": "string", "description": "Code describing the reason for prior authorization request.", "metadata": {"field": "498-<PERSON>", "format": "x(2)", "reference": "ECL"}}, "757-U6": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Assigned by processor to identify a set of parameters, benefits, or coverage criteria used to adjudicate a claim.", "metadata": {"field": "757-U6", "format": "x(15)"}}]}, "394-MW": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "The amount of claim allocated to the Medicare stage identified by the Benefit Stage Qualifier (393-MV).", "metadata": {"field": "394-MW", "format": "9(6)v99"}}]}, "392-MU": {"allOf": [{"$ref": "#/$defs/max4"}, {"type": "string", "description": "Count of Benefit Stage Amount (394-MW) occurrences.", "metadata": {"field": "392-<PERSON><PERSON>", "format": "9(1)"}}]}, "393-MV": {"type": "string", "description": "Code qualifying the Benefit Stage Amount (394-MW).", "maxLength": 2, "metadata": {"field": "393-MV", "format": "x(2)", "reference": "ECL"}}, "117-TR": {"type": "string", "description": "A code that identifies the entity submitting the billing transaction.", "metadata": {"field": "117-TR", "format": "9(2)", "reference": "ECL"}}, "101-A1": {"allOf": [{"$ref": "#/$defs/bin"}, {"type": "string", "description": "Card Issuer ID or Bank ID Number used for network routing.", "metadata": {"field": "101-A1", "format": "9(6)"}}]}, "312-CC": {"allOf": [{"$ref": "#/$defs/x12"}, {"type": "string", "description": "Individual first name.", "metadata": {"field": "312-CC", "format": "x(12)"}}]}, "302-C2": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Insurance ID assigned to the cardholder or identification number used by the plan.", "metadata": {"field": "302-C2", "format": "x(20)"}}]}, "313-CD": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Individual last name.", "metadata": {"field": "313-CD", "format": "x(15)"}}]}, "327-CR": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "Carrier code assigned in Workers Compensation Program.", "metadata": {"field": "327-CR", "format": "x(10)"}}]}, "435-DZ": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Identifies the claim number assigned by Workers Compensation Program.", "metadata": {"field": "435-DZ", "format": "x(30)"}}]}, "493-XE": {"allOf": [{"$ref": "#/$defs/max5"}, {"type": "string", "description": "Counter number of clinical information measurement set/logical grouping.", "metadata": {"field": "493-X<PERSON>", "format": "9(1)"}}]}, "528-FS": {"type": "string", "description": "Code identifying the significance or severity level of a clinical event as contained in the originating database.", "metadata": {"field": "528-FS", "format": "x(1)", "reference": "ECL"}}, "997-G2": {"type": "string", "description": "Indicates that the patient resides in a facility that qualifies for the CMS Part D benefit.", "metadata": {"field": "997-G2", "format": "x(1)", "reference": "ECL"}}, "138-UQ": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Free form text that provides the low-income subsidy copay level for a Part D patient.", "metadata": {"field": "138-<PERSON><PERSON>", "format": "x(20)"}}]}, "406-D6": {"type": "string", "description": "Code indicating whether or not the prescription is a compound.", "metadata": {"field": "406-D6", "format": "9(1)", "reference": "ECL"}}, "451-EG": {"type": "string", "description": "Compound dispensing unit form indicator.", "metadata": {"field": "451-<PERSON><PERSON>", "format": "9(1)", "reference": "ECL"}}, "450-EF": {"type": "string", "description": "Dosage form of the complete compound mixture.", "metadata": {"field": "450-EF", "format": "x(2)", "reference": "ECL"}}, "490-UE": {"type": "string", "description": "Code indicating the method by which the drug cost of an ingredient used in a compound was calculated.", "metadata": {"field": "490-UE", "format": "x(2)", "reference": "ECL"}}, "447-EC": {"allOf": [{"$ref": "#/$defs/max25"}, {"type": "string", "description": "Count of compound product IDs (both active and inactive) in the compound mixture submitted.", "metadata": {"field": "447-EC", "format": "9(2)"}}]}, "449-EE": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Ingredient cost for the metric decimal quantity of the product included in the compound mixture indicated in Compound Ingredient Quantity (Field 448-ED).", "metadata": {"field": "449-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "363-2H": {"type": "string", "description": "Identifies special circumstances related to the dispensing/payment of the product as identified in the Compound Product ID (498-TE).", "metadata": {"field": "363-2H", "format": "x(2)", "reference": "ECL"}}, "362-2G": {"allOf": [{"$ref": "#/$defs/max10"}, {"type": "string", "description": "Code indicating the number of Compound Ingredient Modifier Code (363-2H)", "metadata": {"field": "362-2G", "format": "9(2)"}}]}, "448-ED": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Amount expressed in metric decimal units of the product included in the compound mixture.", "metadata": {"field": "448-<PERSON><PERSON>", "format": "9(7)v999"}}]}, "489-TE": {"allOf": [{"$ref": "#/$defs/x19"}, {"type": "string", "description": "Product identification of an ingredient used in a compound.", "metadata": {"field": "489-TE", "format": "x(19)"}}]}, "488-RE": {"type": "string", "description": "Code qualifying the type of product dispensed.", "metadata": {"field": "488-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "996-G1": {"type": "string", "description": "Clarifies the type of compound.", "metadata": {"field": "996-G1", "format": "x(2)", "reference": "ECL"}}, "240-U1": {"allOf": [{"$ref": "#/$defs/x8"}, {"type": "string", "description": "Account Number assigned during installation for segments of business", "metadata": {"field": "240-U1", "format": "x(8)"}}]}, "337-4C": {"allOf": [{"$ref": "#/$defs/max9"}, {"type": "string", "description": "Count of other payment occurrences.", "metadata": {"field": "337-4C", "format": "9(1)"}}]}, "486-ME": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Unique serial number assigned to the prescription coupons.", "metadata": {"field": "486-ME", "format": "x(15)"}}]}, "485-KE": {"type": "string", "description": "Code indicating the type of coupon being used.", "metadata": {"field": "485-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "487-NE": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Value of the coupon.", "metadata": {"field": "487-NE", "format": "9(6)v99"}}]}, "532-FW": {"type": "string", "description": "Code identifying the source of drug information used for DUR processing or to define the database used for identifying the product.", "metadata": {"field": "532-FW", "format": "x(1)", "reference": "ECL"}}, "304-C4": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date of birth of patient.", "metadata": {"field": "304-C4", "format": "9(8)"}}]}, "434-DY": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date on which the injury occurred.", "metadata": {"field": "434-DY", "format": "9(8)"}}]}, "401-D1": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Identifies date the prescription was filled or professional service rendered or subsequent payer began coverage following Part A expiration in a long-term care setting only.", "metadata": {"field": "401-D1", "format": "9(8)"}}]}, "414-DE": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date prescription was written.", "metadata": {"field": "414-<PERSON>", "format": "9(8)"}}]}, "405-D5": {"allOf": [{"$ref": "#/$defs/maxDays"}, {"type": "string", "description": "Estimated number of days the prescription will last.", "metadata": {"field": "405-D5", "format": "9(3)"}}]}, "345-HG": {"allOf": [{"$ref": "#/$defs/maxDays"}, {"type": "string", "description": "Days supply for metric decimal quantity of medication that would be dispensed on original dispensing if inventory were available. Used in association with a P or C in Dispensing Status (343-HD).", "metadata": {"field": "345-HG", "format": "9(3)"}}]}, "357-NV": {"type": "string", "description": "Code to specify the reason that submission of the transactions has been delayed.", "metadata": {"field": "357-NV", "format": "9(2)", "reference": "ECL"}}, "424-DO": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code identifying the diagnosis of the patient.", "metadata": {"field": "424-<PERSON><PERSON>", "format": "x(15)"}}]}, "491-VE": {"allOf": [{"$ref": "#/$defs/max5"}, {"type": "string", "description": "Count of diagnosis occurrences.", "metadata": {"field": "491-VE", "format": "9(1)"}}]}, "492-WE": {"type": "string", "description": "Code qualifying the Diagnosis Code (424-DO).", "metadata": {"field": "492-WE", "format": "x(2)", "reference": "ECL"}}, "408-D8": {"type": "string", "description": "Code indicating whether or not the prescriber's instructions regarding generic substitution were followed.", "metadata": {"field": "408-D8", "format": "x(1)", "reference": "ECL"}}, "149-U9": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Informational field used when Other Payer-Patient Responsibility Amount (352-NQ) or Patient Pay Amount (505-F5) is used for reimbursement. Amount is equal to contracted or reimbursable dispensing fee for product being dispensed.", "metadata": {"field": "149-U9", "format": "9(6)99"}}]}, "507-F7": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Dispensing fee paid included in the Total Amount Paid (509- F9).", "metadata": {"field": "507-F7", "format": "9(6)v99"}}]}, "412-DC": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Dispensing fee submitted by the pharmacy. This amount is included in the 'Gross Amount Due' (430-DU).", "metadata": {"field": "412-DC", "format": "9(6)v99"}}]}, "343-HD": {"type": "string", "description": "Code indicating the quantity dispensed is a partial fill or the completion of a partial fill. Used only in situations where inventory shortages do not allow the full quantity to be dispensed.", "metadata": {"field": "343-HD", "format": "x(1)", "reference": "ECL"}}, "570-NS": {"allOf": [{"$ref": "#/$defs/x100"}, {"type": "string", "description": "Descriptive information that further defines the referenced DUR alert.", "metadata": {"field": "570-NS", "format": "x(100)"}}]}, "476-H6": {"allOf": [{"$ref": "#/$defs/x19"}, {"type": "string", "description": "Identifies the co-existing agent contributing to the DUR event (drug or disease conflicting with the prescribed drug or prompting pharmacist professional service).", "metadata": {"field": "476-H6", "format": "x(19)"}}]}, "475-J9": {"type": "string", "description": "Code qualifying the value in DUR Co-Agent ID (476-H6).", "metadata": {"field": "475-J9", "format": "x(2)", "reference": "ECL"}}, "544-FY": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Text that provides additional detail regarding a DUR conflict.", "metadata": {"field": "544-FY", "format": "x(30)"}}]}, "473-7E": {"allOf": [{"$ref": "#/$defs/max9"}, {"type": "string", "description": "Counter number for each DUR/PPS set/logical grouping.", "metadata": {"field": "473-7E", "format": "9(1)"}}]}, "474-8E": {"type": "string", "description": "Code indicating the level of effort as determined by the complexity of decision-making or resources utilized by a pharmacist to perform a professional service.", "metadata": {"field": "474-8E", "format": "9(2)", "reference": "ECL"}}, "567-J6": {"allOf": [{"$ref": "#/$defs/max9"}, {"type": "string", "description": "Counter number for each DUR/PPS response set/logical grouping.", "metadata": {"field": "567-J6", "format": "9(1)"}}]}, "309-C9": {"type": "string", "description": "Code indicating that the pharmacy is clarifying eligibility for a patient.", "maxLength": 1, "metadata": {"field": "309-C9", "format": "9(1)", "reference": "ECL"}}, "317-CH": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Free-form text for city name.", "metadata": {"field": "317-CH", "format": "x(20)"}}]}, "321-CL": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Employer primary contact.", "metadata": {"field": "321-C<PERSON>", "format": "x(30)"}}]}, "333-CZ": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "ID assigned to employer.", "metadata": {"field": "333-<PERSON><PERSON>", "format": "x(15)"}}]}, "315-CF": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Complete name of employer.", "metadata": {"field": "315-CF", "format": "x(30)"}}]}, "320-CK": {"allOf": [{"$ref": "#/$defs/phone10"}, {"type": "string", "description": "Ten-digit phone number of employer.", "metadata": {"field": "320-CK", "format": "9(10)"}}]}, "318-CI": {"type": "string", "description": "Standard State/Province Code as defined by appropriate government agency.", "metadata": {"field": "318-C<PERSON>", "format": "x(2)", "reference": "ECL"}}, "316-CG": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Free-form text for address information.", "metadata": {"field": "316-CG", "format": "x(30)"}}]}, "319-CJ": {"allOf": [{"justNumbers": true}, {"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code defining international postal zone excluding punctuation and blanks (zip code for US).", "metadata": {"field": "319-<PERSON><PERSON>", "format": "x(15)"}}]}, "577-G3": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "The amount, not included in the Total Amount Paid (509- F9), that the patient would have saved if they had chosen the generic drug instead of the brand drug.", "metadata": {"field": "577-G3", "format": "9(6)v99"}}]}, "388-5J": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Free form text for facility city name", "metadata": {"field": "388-5J", "format": "x(20)"}}]}, "336-8C": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "ID assigned to the patients clinic/host party.", "metadata": {"field": "336-8C", "format": "x(10)"}}]}, "385-3Q": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Name identifying the location of the service rendered.", "metadata": {"field": "385-3Q", "format": "x(30)"}}]}, "387-3V": {"type": "string", "description": "Standard state /province code as defined by appropriate government agency.", "maxLength": 2, "metadata": {"field": "387-3V", "format": "x(2)", "reference": "ECL"}}, "386-3U": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Free form text for Facility address information.", "metadata": {"field": "386-3U", "format": "x(30)"}}]}, "389-6D": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code defining international postal zone excluding punctuation and blanks", "metadata": {"field": "389-6D", "format": "x(15)"}}]}, "403-D3": {"type": "string", "description": "The code indicating whether the prescription is an original or a refill.", "metadata": {"field": "403-D3", "format": "9(2)", "reference": "ECL"}}, "558-AW": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Flat sales tax paid which is included in the Total Amount Paid (509-F9).", "metadata": {"field": "558-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "481-HA": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Flat sales tax submitted for prescription. This amount is included in the Gross Amount Due (430-DU).", "metadata": {"field": "481-H<PERSON>", "format": "9(6)v99"}}]}, "926-FF": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "ID for the formulary list", "metadata": {"field": "926-FF", "format": "x(10)"}}]}, "126-UA": {"allOf": [{"$ref": "#/$defs/x19"}, {"type": "string", "description": "Identifies the generic equivalent of the brand product dispensed.", "metadata": {"field": "126-UA", "format": "x(19)"}}]}, "125-TZ": {"type": "string", "description": "Code qualifying the Generic Equivalent Product ID (126- UA).", "metadata": {"field": "125-TZ", "format": "x(2)", "reference": "ECL"}}, "430-DU": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Total price claimed from all sources.  For prescription claim request, field represents a sum of Ingredient Cost Submitted  (409-D9), Dispensing Fee Submitted (412-DC), Flat Sales Tax Amount Submitted (481-HA), Percentage Sales Tax Amount Submitted (482-GE), Incentive Amount Submitted (438-E3), Other Amount Claimed (480-H9). For service claim request, field represents a sum of Professional Services Fee Submitted (477- BE), Flat Sales Tax Amount Submitted (481-HA), Percentage Sales Tax Amount Submitted (482-GE), Other Amount Claimed (480-H9).", "metadata": {"field": "430-DU", "format": "9(6)v99"}}]}, "301-C1": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "ID assigned to the cardholder group or employer group.", "metadata": {"field": "301-C1", "format": "x(15)"}}]}, "501-F1": {"type": "string", "description": "Code indicating the status of the transmission.", "metadata": {"field": "501-F1", "format": "x(1)", "reference": "ECL"}}, "129-UD": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "The amount from the health plan-funded assistance account for the patient that was applied to reduce Patient Pay Amount (505-F5). This amount is used in Healthcare Reimbursement Account (HRA) benefits only. This field is always a negative amount or zero.", "metadata": {"field": "129-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "550-8F": {"allOf": [{"justNumbers": true}, {"$ref": "#/$defs/phone18"}, {"type": "string", "description": "Ten-digit phone number of the help desk.", "metadata": {"field": "550-8F", "format": "x(18)"}}]}, "549-7F": {"type": "string", "description": "Code qualifying the phone number in the Help Desk Phone Number (550-8F).", "metadata": {"field": "549-7F", "format": "x(2)", "reference": "ECL"}}, "314-CE": {"allOf": [{"$ref": "#/$defs/x3"}, {"type": "string", "description": "Code identifying the Blue Cross or Blue Shield plan ID which indicates where the members coverage has been designated. Usually where the member lives or purchased their coverage.", "metadata": {"field": "314-CE", "format": "x(3)"}}]}, "521-FL": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount represents the contractually agreed upon incentive fee paid for specific services rendered. Amount is included in the 'Total Amount Paid' (509-F9).", "metadata": {"field": "521-FL", "format": "9(6)v99"}}]}, "438-E3": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount represents a fee that is submitted by the pharmacy for contractually agreed upon services. This amount is included in the 'Gross Amount Due' (430-DU).", "metadata": {"field": "438-E3", "format": "9(6)v99"}}]}, "148-U8": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Informational field used when Other Payer-Patient Responsibility Amount (352- NQ) or Patient Pay Amount (505-F5) is used for reimbursement. Amount is equal to contracted or reimbursable amount for product being dispensed.", "metadata": {"field": "148-U8", "format": "9(6)99"}}]}, "506-F6": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Drug ingredient cost paid included in the Total Amount Paid (509-F9).", "metadata": {"field": "506-F6", "format": "9(6)v99"}}]}, "409-D9": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Submitted product component cost of the dispensed prescription. This amount is included in the 'Gross Amount Due' (430-DU).", "metadata": {"field": "409-D9", "format": "9(6)v99"}}]}, "463-EW": {"allOf": [{"$ref": "#/$defs/x2"}, {"type": "string", "description": "Value indicating that authorization occurred for intermediary processing.", "metadata": {"field": "463-<PERSON><PERSON>", "format": "x(2)"}}]}, "464-EX": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "INTERMEDIARY AUTHORIZATION ID Q Claim Billing/Encounter: Required for overriding an authorized intermediary system edit when the pharmacy participates with an intermediary. Not used for payer-to-payer transaction.", "metadata": {"field": "464-EX", "format": "x(20)"}}]}, "993-A7": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Number assigned by the processor to identify an adjudicated claim when supplied in payer-to-payer coordination of benefits only.", "metadata": {"field": "993-A7", "format": "x(30)"}}]}, "370-2R": {"allOf": [{"$ref": "#/$defs/93"}, {"type": "string", "description": "Length of time the physician expects the patient to require use of the ordered item.", "metadata": {"field": "370-2R", "format": "9(3)"}}]}, "371-2S": {"type": "string", "description": "Code qualifying the length of need.", "metadata": {"field": "371-2S", "format": "9(2)", "reference": "ECL"}}, "418-DI": {"type": "string", "description": "Coding indicating the type of service the provider rendered.", "metadata": {"field": "418-D<PERSON>", "format": "9(2)", "reference": "ECL"}}, "494-ZE": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date clinical information was collected or measured.", "metadata": {"field": "494-<PERSON><PERSON>", "format": "9(8)"}}]}, "496-H2": {"type": "string", "description": "Code indicating the clinical domain of the observed value in Measurement Value (499- H4).", "metadata": {"field": "496-H2", "format": "x(2)", "reference": "ECL"}}, "495-H1": {"allOf": [{"$ref": "#/$defs/time"}, {"type": "string", "description": "Time clinical information was collected or measured.", "metadata": {"field": "495-H1", "format": "9(4)"}}]}, "497-H3": {"type": "string", "description": "Code indicating the metric or English units used with the clinical information.", "metadata": {"field": "497-H3", "format": "x(2)", "reference": "ECL"}}, "499-H4": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Actual value of clinical information.", "metadata": {"field": "499-H4", "format": "x(15)"}}]}, "116-N6": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Number assigned by processor to identify the individual Medicaid Agency or representative.", "metadata": {"field": "116-N6", "format": "x(15)"}}]}, "115-N5": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "A unique member identification number assigned by the Medicaid Agency.", "metadata": {"field": "115-N5", "format": "x(20)"}}]}, "360-2B": {"type": "string", "description": "Two character State Postal Code indicating the state where Medicaid coverage exists.", "metadata": {"field": "360-2B", "format": "x(2)", "reference": "ECL"}}, "113-N3": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount paid by the Medicaid Agency.", "metadata": {"field": "113-N3", "format": "9(6)V99"}}]}, "114-N4": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Claim number assigned by the Medicaid Agency.", "metadata": {"field": "114-N4", "format": "x(20)"}}]}, "139-UR": {"type": "string", "description": "Code indicating the position of Medicare Part D in the billing order.", "metadata": {"field": "139-U<PERSON>", "format": "9(2)", "reference": "ECL"}}, "359-2A": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Patients ID assigned by the Medigap Insurer", "metadata": {"field": "359-2A", "format": "x(20)"}}]}, "504-F4": {"allOf": [{"$ref": "#/$defs/x200"}, {"type": "string", "description": "Free form message.", "metadata": {"field": "504-F4", "format": "x(200)"}}]}, "390-BM": {"allOf": [{"$ref": "#/$defs/x200"}, {"type": "string", "description": "Free form text", "metadata": {"field": "390-BM", "format": "x(200)"}}]}, "545-2F": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "Field defined by the processor. It identifies the network, for the covered member, used to calculate the reimbursement to the pharmacy.", "metadata": {"field": "545-2F", "format": "x(10)"}}]}, "140-US": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Future date Part D coverage begins for the patient.", "metadata": {"field": "140-US", "format": "9(8)"}}]}, "141-UT": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Future date Part D coverage ends for the patient", "metadata": {"field": "141-UT", "format": "9(8)"}}]}, "415-DF": {"allOf": [{"$ref": "#/$defs/max99"}, {"type": "string", "description": "Number of refills authorized by the prescriber.", "metadata": {"field": "415-DF", "format": "9(2)"}}]}, "445-EA": {"allOf": [{"$ref": "#/$defs/x19"}, {"type": "string", "description": "Code of the initially prescribed product or service.", "metadata": {"field": "445-<PERSON>", "format": "x(19)"}}]}, "453-EJ": {"type": "string", "description": "Code qualifying the value in 'Originally Prescribed Product/Service Code (Field 445-EA).", "metadata": {"field": "453-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "446-EB": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Product initially prescribed amount expressed in metric decimal units.", "metadata": {"field": "446-<PERSON><PERSON>", "format": "9(7)v999"}}]}, "480-H9": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount representing the additional incurred costs for a dispensed prescription or service.", "metadata": {"field": "480-H9", "format": "9(6)v99"}}]}, "478-H7": {"allOf": [{"$ref": "#/$defs/max3"}, {"type": "string", "description": "Count of other amount claimed submitted occurrences.", "metadata": {"field": "478-H7", "format": "9(1)"}}]}, "479-H8": {"type": "string", "description": "Code identifying the additional incurred cost claimed in Other Amount Claimed Submitted (480-H9).", "metadata": {"field": "479-H8", "format": "x(2)", "reference": "ECL"}}, "565-J4": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount paid for additional costs claimed in Other Amount Claimed Submitted (480-H9).", "metadata": {"field": "565-J4", "format": "9(6)v99"}}]}, "563-J2": {"allOf": [{"$ref": "#/$defs/max3"}, {"type": "string", "description": "Count of the other amount paid occurrences.", "metadata": {"field": "563-J2", "format": "9(1)"}}]}, "564-J3": {"type": "string", "description": "Code clarifying the value in the Other Amount Paid (565-J4).", "metadata": {"field": "564-J3", "format": "x(2)", "reference": "ECL"}}, "308-C8": {"type": "string", "description": "Code indicating whether or not the patient has other insurance coverage.", "metadata": {"field": "308-C8", "format": "9(2)", "reference": "ECL"}}, "431-DV": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount of any payment known by the pharmacy from other sources.", "metadata": {"field": "431-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "341-HB": {"allOf": [{"$ref": "#/$defs/max9"}, {"type": "string", "description": "Count of the payer amount paid occurrences.", "metadata": {"field": "341-<PERSON><PERSON>", "format": "9(1)"}}]}, "342-HC": {"type": "string", "description": "Code qualifying the Other Payer Amount Paid (431-DV).", "metadata": {"field": "342-<PERSON>", "format": "x(2)", "reference": "ECL"}}, "566-J5": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Total amount recognized by the processor of any payment from another source.", "metadata": {"field": "566-J5", "format": "9(6)v99"}}]}, "144-UX": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Other Payers effective date of the patients benefit.", "metadata": {"field": "144-U<PERSON>", "format": "9(8)"}}]}, "145-UY": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Other Payers termination date of the patients benefit.", "metadata": {"field": "145-U<PERSON>", "format": "9(8)"}}]}, "990-MG": {"allOf": [{"$ref": "#/$defs/96"}, {"type": "string", "description": "The secondary, tertiary, etc. card issuer or bank ID number used for network routing.", "metadata": {"field": "990-MG", "format": "9(6)"}}]}, "356-NU": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Cardholder ID for this member that is associated with the Payer noted.", "metadata": {"field": "356-N<PERSON>", "format": "x(20)"}}]}, "338-5C": {"type": "string", "description": "Code identifying the type of Other Payer ID (340-7C).", "metadata": {"field": "338-5C", "format": "x(2)", "reference": "ECL"}}, "443-E8": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Payment or denial date of the claim submitted to the other payer. Used for coordination of benefits.", "metadata": {"field": "443-E8", "format": "9(8)"}}]}, "992-MJ": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "ID assigned to the cardholder group or employer group by the secondary, tertiary, etc. payer.", "metadata": {"field": "992-<PERSON><PERSON>", "format": "x(15)"}}]}, "127-UB": {"allOf": [{"$ref": "#/$defs/phone18"}, {"type": "string", "description": "Phone number of the other payers help desk.", "metadata": {"field": "127-UB", "format": "x(18)"}}]}, "340-7C": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "ID assigned to the payer.", "metadata": {"field": "340-7C", "format": "x(10)"}}]}, "355-NT": {"allOf": [{"$ref": "#/$defs/max3"}, {"type": "string", "description": "Count of other payers with payment responsibility.", "metadata": {"field": "355-NT", "format": "9(1)"}}]}, "339-6C": {"type": "string", "description": "Code qualifying the Other Payer ID (340-7C).", "metadata": {"field": "339-6C", "format": "x(2)", "reference": "ECL"}}, "143-UW": {"type": "string", "description": "Code assigned by the other payer to indicate the relationship of patient to cardholder.", "metadata": {"field": "143-<PERSON><PERSON>", "format": "9(1)", "reference": "ECL"}}, "352-NQ": {"allOf": [{"$ref": "#/$defs/98v99"}, {"type": "string", "description": "The patients cost share from a previous payer.", "metadata": {"field": "352-NQ", "format": "9(8)v99"}}]}, "353-NR": {"allOf": [{"$ref": "#/$defs/max25"}, {"type": "string", "description": "Count of “Other Payer-Patient Responsibility Amount” (352- NQ) and “Other Payer-Patient Responsibility Amount Qualifier” (351-NP) occurrences.", "metadata": {"field": "353-NR", "format": "9(2)"}}]}, "351-NP": {"type": "string", "description": "Code qualifying the “Other Payer-Patient Responsibility Amount (352-NQ)”.", "metadata": {"field": "351-NP", "format": "x(2)", "reference": "ECL"}}, "142-UV": {"allOf": [{"$ref": "#/$defs/x3"}, {"type": "string", "description": "Code assigned by the other payer to a specific person within a family.", "metadata": {"field": "142-UV", "format": "x(3)"}}]}, "991-MH": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "A number that uniquely identifies the secondary, tertiary, etc. payer to the processor.", "metadata": {"field": "991-MH", "format": "x(10)"}}]}, "472-6E": {"allOf": [{"$ref": "#/$defs/x3"}, {"type": "string", "description": "The error encountered by the previous “Other Payer” in Reject Code (511-FB).", "metadata": {"field": "472-6E", "format": "x(3)"}}]}, "471-5E": {"allOf": [{"$ref": "#/$defs/max5"}, {"type": "string", "description": "Count of Other Payer Reject Code (472-6E) occurrences.", "metadata": {"field": "471-5E", "format": "9(2)"}}]}, "529-FT": {"type": "string", "description": "Code indicating the pharmacy responsible for the previous event involved in the DUR conflict.", "maxLength": 1, "metadata": {"field": "529-FT", "format": "9(1)", "reference": "ECL"}}, "533-FX": {"type": "string", "description": "Code comparing the prescriber of the current prescription to the prescriber of the previously filled conflicting prescription.", "metadata": {"field": "533-FX", "format": "9(1)", "reference": "ECL"}}, "391-MT": {"type": "string", "description": "Code to indicate a patients choice on assignment of benefits.", "metadata": {"field": "391-MT", "format": "x(1)", "reference": "ECL"}}, "323-CN": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Free-form text for city name.", "metadata": {"field": "323-C<PERSON>", "format": "x(20)"}}]}, "350-HN": {"allOf": [{"$ref": "#/$defs/email"}, {"type": "string", "description": "The E-Mail address of the patient (member).", "metadata": {"field": "350-HN", "format": "x(80)"}}]}, "310-CA": {"allOf": [{"$ref": "#/$defs/x12"}, {"type": "string", "description": "Individual first name.", "metadata": {"field": "310-CA", "format": "x(12)"}}]}, "305-C5": {"type": "string", "description": "Code indicating the gender of the individual.", "metadata": {"field": "305-C5", "format": "9(1)", "reference": "ECL"}}, "332-CY": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "ID assigned to the patient.", "metadata": {"field": "332-<PERSON><PERSON>", "format": "x(20)"}}]}, "331-CX": {"type": "string", "description": "Code qualifying the Patient ID (332-CY).", "metadata": {"field": "331-CX", "format": "x(2)", "reference": "ECL"}}, "311-CB": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Individual last name.", "metadata": {"field": "311-<PERSON>", "format": "x(15)"}}]}, "433-DX": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount the pharmacy received from the patient for the prescription dispensed.", "metadata": {"field": "433-D<PERSON>", "format": "9(6)v99"}}]}, "505-F5": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount that is calculated by the processor and returned to the pharmacy as the TOTAL amount to be paid by the patient to the pharmacy; the patients total cost share, including copayments, amounts applied to deductible, over maximum amounts, penalties, etc.", "metadata": {"field": "505-F5", "format": "9(6)v99"}}]}, "326-CQ": {"allOf": [{"$ref": "#/$defs/phone10"}, {"type": "string", "description": "Ten-digit phone number of patient.", "metadata": {"field": "326-<PERSON><PERSON>", "format": "9(10)"}}]}, "306-C6": {"type": "string", "description": "Code indicating relationship of patient to cardholder.", "metadata": {"field": "306-C6", "format": "9(1)", "reference": "ECL"}}, "384-4X": {"type": "string", "description": "Code identifying the patients place of residence.", "metadata": {"field": "384-4X", "format": "9(2)", "reference": "ECL"}}, "575-EQ": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Patient sales tax responsibility. This field is not a component of the Patient Pay Amount (505- F5) formula.", "metadata": {"field": "575-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "324-CO": {"type": "string", "description": "Standard State/Province Code as defined by appropriate government agency.", "metadata": {"field": "324-CO", "format": "x(2)", "reference": "ECL"}}, "322-CM": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Free-form text for address information.", "metadata": {"field": "322-CM", "format": "x(30)"}}]}, "325-CP": {"allOf": [{"justNumbers": true}, {"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code defining international postal zone excluding punctuation and blanks (zip code for US).", "metadata": {"field": "325-CP", "format": "x(15)"}}]}, "569-J8": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "ID of the payer.", "metadata": {"field": "569-J8", "format": "x(10)"}}]}, "568-J7": {"type": "string", "description": "Code indicating the type of payer ID.", "metadata": {"field": "568-J7", "format": "x(2)", "reference": "ECL"}}, "122-TW": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "City of the entity to receive payment for claim.", "metadata": {"field": "122-TW", "format": "x(20)"}}]}, "119-TT": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Identifying number of the entity to receive payment for claim.", "metadata": {"field": "119-TT", "format": "x(15)"}}]}, "120-TU": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Name of the entity to receive payment for claim.", "metadata": {"field": "120-TU", "format": "x(20)"}}]}, "118-TS": {"type": "string", "description": "Code qualifying the Pay To ID (119-TT).", "metadata": {"field": "118-TS", "format": "x(2)", "reference": "ECL"}}, "123-TX": {"type": "string", "description": "Standard state /province code as defined by appropriate government agency.", "metadata": {"field": "123-TX", "format": "x(2)", "reference": "ECL"}}, "121-TV": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Street address of the entity to receive payment for claim.", "metadata": {"field": "121-TV", "format": "x(30)"}}]}, "124-TY": {"allOf": [{"justNumbers": true}, {"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code defining international postal zone excluding punctuation and blanks (zip code for US).", "metadata": {"field": "124-TY", "format": "x(15)"}}]}, "559-AX": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount of percentage sales tax paid which is included in the Total Amount Paid (509- F9).", "metadata": {"field": "559-AX", "format": "9(6)v99"}}]}, "482-GE": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Percentage sales tax submitted.", "metadata": {"field": "482-GE", "format": "9(6)v99"}}]}, "561-AZ": {"type": "string", "description": "Code indicating the percentage sales tax paid basis.", "metadata": {"field": "561-AZ", "format": "x(2)", "reference": "ECL"}}, "484-JE": {"type": "string", "description": "Code indicating the basis for percentage sales tax.", "metadata": {"field": "484-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "560-AY": {"allOf": [{"$ref": "#/$defs/93v4"}, {"type": "string", "description": "Percentage Sales Tax Amount Submitted (482-GE).", "metadata": {"field": "560-AY", "format": "9(3)v4"}}]}, "483-HE": {"allOf": [{"$ref": "#/$defs/93v4"}, {"type": "string", "description": "Percentage sales tax rate used to calculate Percentage Sales Tax Amount Submitted (482-GE).", "metadata": {"field": "483-<PERSON><PERSON>", "format": "9(3)v4"}}]}, "303-C3": {"allOf": [{"$ref": "#/$defs/x3"}, {"type": "string", "description": "Code assigned to a specific person within a family.", "metadata": {"field": "303-C3", "format": "x(3)"}}]}, "147-U7": {"type": "string", "description": "The type of service being performed by a pharmacy when different contractual terms exist between a payer and the pharmacy, or when benefits are based upon the type of service performed.", "metadata": {"field": "147-U7", "format": "9(2)", "reference": "ECL"}}, "307-C7": {"type": "string", "description": "Code identifying the place where a drug or service is dispensed or administered.", "metadata": {"field": "307-C7", "format": "9(2)", "reference": "ECL"}}, "524-FO": {"allOf": [{"$ref": "#/$defs/x8"}, {"type": "string", "description": "Assigned by the processor to identify a set of parameters, benefit, or coverage criteria used to adjudicate a claim.", "metadata": {"field": "524-FO", "format": "x(8)"}}]}, "574-2Y": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Plan sales tax responsibility. This field is not a component of the Patient Pay Amount (505- F5) formula.", "metadata": {"field": "574-2Y", "format": "9(6)v99"}}]}, "555-AT": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount of patients copay/cost-share incentive for preferred product.", "metadata": {"field": "555-AT", "format": "9(6)v99"}}]}, "551-9F": {"allOf": [{"$ref": "#/$defs/max6"}, {"type": "string", "description": "Count of preferred product occurrences.", "metadata": {"field": "551-9F", "format": "9(1)"}}]}, "556-AU": {"allOf": [{"$ref": "#/$defs/x40"}, {"type": "string", "description": "Free text message.", "metadata": {"field": "556-AU", "format": "x(40)"}}]}, "553-AR": {"allOf": [{"$ref": "#/$defs/x19"}, {"type": "string", "description": "Alternate product recommended by the plan.", "metadata": {"field": "553-AR", "format": "x(19)"}}]}, "552-AP": {"type": "string", "description": "Code qualifying the type of product ID submitted in Preferred Product ID (553- AR).", "metadata": {"field": "552-AP", "format": "x(2)", "reference": "ECL"}}, "554-AS": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount of pharmacy incentive available for substitution of preferred product.", "metadata": {"field": "554-<PERSON>", "format": "9(6)v99"}}]}, "335-2C": {"type": "string", "description": "Code indicating the patient as pregnant or non-pregnant.", "metadata": {"field": "335-2C", "format": "x(1)", "reference": "ECL"}}, "364-2J": {"allOf": [{"$ref": "#/$defs/x12"}, {"type": "string", "description": "Individual first name.", "metadata": {"field": "364-2J", "format": "x(12)"}}]}, "366-2M": {"allOf": [{"$ref": "#/$defs/x20"}, {"type": "string", "description": "Free form text for prescriber city name.", "metadata": {"field": "366-2M", "format": "x(20)"}}]}, "411-DB": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "ID assigned to the prescriber.", "metadata": {"field": "411-<PERSON>", "format": "x(15)"}}]}, "466-EZ": {"type": "string", "description": "Code qualifying the Prescriber ID (411-DB).", "metadata": {"field": "466-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "427-DR": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Individual last name.", "metadata": {"field": "427-DR", "format": "x(15)"}}]}, "498-PM": {"allOf": [{"$ref": "#/$defs/phone10"}, {"type": "string", "description": "Ten-digit phone number of the prescriber.", "metadata": {"field": "498-PM", "format": "9(10)"}}]}, "367-2N": {"type": "string", "description": "Standard state /province code as defined by appropriate government agency.", "metadata": {"field": "367-2N", "format": "x(2)", "reference": "ECL"}}, "365-2K": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Free form text for prescriber address information.", "metadata": {"field": "365-2K", "format": "x(30)"}}]}, "372-2T": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "The date the form was completed and signed by the ordering physician.", "metadata": {"field": "372-2T", "format": "9(8)"}}]}, "368-2P": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Code defining international postal zone excluding punctuation and blanks.", "metadata": {"field": "368-2P", "format": "x(15)"}}]}, "419-DJ": {"type": "string", "description": "Code indicating the origin of the prescription.", "metadata": {"field": "419-DJ", "format": "9(1)", "reference": "ECL"}}, "402-D2": {"allOf": [{"$ref": "#/$defs/912"}, {"type": "string", "description": "Reference number assigned by the provider for the dispensed drug/product and/or service provided.", "metadata": {"field": "402-D2", "format": "9(12)"}}]}, "455-EM": {"type": "string", "description": "Indicates the type of billing submitted.", "metadata": {"field": "455-<PERSON><PERSON>", "format": "x(1)", "reference": "ECL"}}, "530-FU": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date prescription was previously filled.", "metadata": {"field": "530-FU", "format": "9(8)"}}]}, "421-DL": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "ID assigned to the primary care provider. Used when the patient is referred to a secondary care provider.", "metadata": {"field": "421-DL", "format": "x(15)"}}]}, "468-2E": {"type": "string", "description": "Code qualifying the Primary Care Provider ID (421-DL).", "metadata": {"field": "468-2E", "format": "x(2)", "reference": "ECL"}}, "470-4E": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Individual last name.", "metadata": {"field": "470-4E", "format": "x(15)"}}]}, "498-RB": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount authorized in the prior authorization.", "metadata": {"field": "498-RB", "format": "9(6)v99"}}]}, "498-PS": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date the prior authorization became effective.", "metadata": {"field": "498-PS", "format": "9(8)"}}]}, "498-PT": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date the prior authorization ends.", "metadata": {"field": "498-<PERSON>", "format": "9(8)"}}]}, "498-PY": {"allOf": [{"$ref": "#/$defs/911"}, {"type": "string", "description": "Unique number identifying the prior authorization assigned by the processor.", "metadata": {"field": "498-PY", "format": "9(11)"}}]}, "498-PW": {"allOf": [{"$ref": "#/$defs/max99"}, {"type": "string", "description": "Number of refills authorized by the prior authorization.", "metadata": {"field": "498-<PERSON><PERSON>", "format": "9(2)"}}]}, "462-EV": {"allOf": [{"$ref": "#/$defs/911"}, {"type": "string", "description": "Number submitted by the provider to identify the prior authorization.", "metadata": {"field": "462-<PERSON><PERSON>", "format": "9(11)"}}]}, "498-PR": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date the prior authorization request was processed.", "metadata": {"field": "498-<PERSON>", "format": "9(8)"}}]}, "498-RA": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Amount authorized expressed in metric decimal units.", "metadata": {"field": "498-RA", "format": "9(7)v999"}}]}, "498-PX": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Accumulated authorized amount expressed in metric decimal units.", "metadata": {"field": "498-PX", "format": "9(7)v999"}}]}, "498-PP": {"allOf": [{"$ref": "#/$defs/x500"}, {"type": "string", "description": "Free text message.", "metadata": {"field": "498-PP", "format": "x(500)"}}]}, "461-EU": {"type": "string", "description": "Code clarifying the Prior Authorization Number Submitted (462-EV) or benefit/plan exemption.", "metadata": {"field": "461-EU", "format": "9(2)", "reference": "ECL"}}, "459-ER": {"type": "string", "description": "Identifies special circumstances related to the performance of the service.", "metadata": {"field": "459-<PERSON>", "format": "x(2)", "reference": "ECL"}}, "458-SE": {"allOf": [{"$ref": "#/$defs/max10"}, {"type": "string", "description": "Count of the Procedure Modifier Code (459-ER) occurrences.", "metadata": {"field": "458-SE", "format": "9(2)"}}]}, "104-A4": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "Number assigned by the processor.", "metadata": {"field": "104-A4", "format": "x(10)"}}]}, "407-D7": {"allOf": [{"$ref": "#/$defs/x19"}, {"type": "string", "description": "ID of the product dispensed or service provided.", "metadata": {"field": "407-D7", "format": "x(19)"}}]}, "436-E1": {"type": "string", "description": "Code qualifying the value in 'Product/Service ID' (407-D7).", "metadata": {"field": "436-E1", "format": "x(2)", "reference": "ECL"}}, "440-E5": {"type": "string", "description": "Code identifying pharmacist intervention when a conflict code has been identified or service has been rendered.", "metadata": {"field": "440-E5", "format": "x(2)", "reference": "ECL"}}, "562-J1": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount representing the contractually agreed upon fee for professional services rendered. This amount is included in the Total Amount Paid (509-F9).", "metadata": {"field": "562-J1", "format": "9(6)v99"}}]}, "477-BE": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount submitted by the provider for professional services rendered.", "metadata": {"field": "477-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "361-2D": {"type": "string", "description": "Code indicating whether the provider accepts assignment.", "metadata": {"field": "361-2D", "format": "x(1)", "reference": "ECL"}}, "444-E9": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "Unique ID assigned to the person responsible for the dispensing of the prescription or provision of the service.", "metadata": {"field": "444-E9", "format": "x(15)"}}]}, "465-EY": {"type": "string", "description": "Code qualifying the Provider ID (444-E9).", "metadata": {"field": "465-<PERSON><PERSON>", "format": "x(2)", "reference": "ECL"}}, "442-E7": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Quantity dispensed expressed in metric decimal units.", "metadata": {"field": "442-E7", "format": "9(7)v999"}}]}, "344-HF": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Metric decimal quantity of medication that would be dispensed on original filling if inventory were available. Used in association with a P or C in  Dispensing Status (343- HD).", "metadata": {"field": "344-<PERSON><PERSON>", "format": "9(7)v999"}}]}, "531-FV": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Amount expressed in metric decimal units of the conflicting agent that was previously filled.", "metadata": {"field": "531-FV", "format": "9(7)v999"}}]}, "460-ET": {"allOf": [{"$ref": "#/$defs/97v999"}, {"type": "string", "description": "Amount expressed in metric decimal units.", "metadata": {"field": "460-ET", "format": "9(7)v999"}}]}, "383-4K": {"allOf": [{"$ref": "#/$defs/x30"}, {"type": "string", "description": "Alphanumeric response to a question (part of the question information).", "metadata": {"field": "383-4K", "format": "x(30)"}}]}, "380-4G": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Date response to a question (part of the question information)", "metadata": {"field": "380-4G", "format": "9(8)"}}]}, "381-4H": {"allOf": [{"$ref": "#/$defs/99v99"}, {"type": "string", "description": "Dollar Amount response to a question (part of the question information).", "metadata": {"field": "381-4H", "format": "9(9)v99"}}]}, "382-4J": {"allOf": [{"$ref": "#/$defs/911"}, {"type": "string", "description": "Numeric response to a question (part of the question information).", "metadata": {"field": "382-4J", "format": "9(11)"}}]}, "378-4B": {"allOf": [{"$ref": "#/$defs/x3"}, {"type": "string", "description": "Identifies the question number/letter that the question response applies to (part of the question information).", "metadata": {"field": "378-4B", "format": "x(3)"}}]}, "377-2Z": {"allOf": [{"$ref": "#/$defs/max50"}, {"type": "string", "description": "Count of Question Number/Letter occurrences.", "metadata": {"field": "377-2Z", "format": "9(2)"}}]}, "379-4D": {"allOf": [{"toFixed": 2}, {"$ref": "#/$defs/93v99"}, {"type": "string", "description": "Percent response to a question (part of the question information).", "metadata": {"field": "379-4D", "format": "9(3)v99"}}]}, "439-E4": {"type": "string", "description": "Code identifying the type of utilization conflict detected or the reason for the pharmacists professional service.", "metadata": {"field": "439-E4", "format": "x(2)", "reference": "ECL"}}, "511-FB": {"type": "string", "description": "Code indicating the error encountered.", "metadata": {"field": "511-FB", "format": "x(3)", "reference": "ECL"}}, "510-FA": {"allOf": [{"$ref": "#/$defs/max5"}, {"type": "string", "description": "Count of Reject Code (511-FB) occurrences.", "metadata": {"field": "510-FA", "format": "9(2)"}}]}, "546-4F": {"allOf": [{"$ref": "#/$defs/max99"}, {"type": "string", "description": "Identifies the counter number or occurrence of the field that is being rejected. Used to indicate rejects for repeating fields.", "metadata": {"field": "546-4F", "format": "9(2)"}}]}, "514-FE": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount remaining in a patient/family plan with a periodic maximum benefit.", "metadata": {"field": "514-FE", "format": "9(6)v99"}}]}, "513-FD": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount not met by the patient/family in the deductible plan.", "metadata": {"field": "513-FD", "format": "9(6)v99"}}]}, "374-2V": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "The beginning date of need.", "metadata": {"field": "374-2V", "format": "9(8)"}}]}, "498-PB": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Beginning date for a prior authorization request.", "metadata": {"field": "498-PB", "format": "9(8)"}}]}, "498-PC": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "Ending date for a prior authorization request.", "metadata": {"field": "498-PC", "format": "9(8)"}}]}, "375-2W": {"allOf": [{"$ref": "#/$defs/date"}, {"type": "string", "description": "The effective date of the revision or re-certification provided by the certifying physician.", "metadata": {"field": "375-2W", "format": "9(8)"}}]}, "373-2U": {"type": "string", "description": "Code identifying type of request.", "metadata": {"field": "373-2U", "format": "x(1)", "reference": "ECL"}}, "498-PA": {"type": "string", "description": "Code identifying type of prior authorization request.", "metadata": {"field": "498-PA", "format": "x(1)", "reference": "ECL"}}, "441-E6": {"type": "string", "description": "Action taken by a pharmacist in response to a conflict or the result of a pharmacists professional service.", "maxLength": 2, "metadata": {"field": "441-E6", "format": "x(2)", "reference": "ECL"}}, "995-E2": {"type": "string", "description": "This is an override to the “default” route referenced for the product. For a multi-ingredient compound, it is the route of the complete compound mixture.", "metadata": {"field": "995-E2", "format": "x(11)", "reference": "ECL"}}, "454-EK": {"allOf": [{"$ref": "#/$defs/x12"}, {"type": "string", "description": "The serial number of the prescription blank/form.", "metadata": {"field": "454-<PERSON><PERSON>", "format": "x(12)"}}]}, "111-AM": {"type": "string", "description": "Identifies the segment in the request and/or response.", "metadata": {"field": "111-AM", "format": "x(2)", "reference": "ECL"}}, "201-B1": {"allOf": [{"$ref": "#/$defs/x15"}, {"type": "string", "description": "ID assigned to a pharmacy or provider.", "metadata": {"field": "201-B1", "format": "x(15)"}}]}, "202-B2": {"type": "string", "description": "Code qualifying the Service Provider ID (201-B1).", "metadata": {"field": "202-B2", "format": "x(2)", "reference": "ECL"}}, "334-1C": {"type": "string", "description": "Code indicating the patient as a smoker or non-smoker.", "metadata": {"field": "334-1C", "format": "x(1)", "reference": "ECL"}}, "110-AK": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "ID assigned by the switch or processor to identify the software source.", "metadata": {"field": "110-AK", "format": "x(10)"}}]}, "429-DT": {"type": "string", "description": "Code indicating the type of dispensing dose.", "metadata": {"field": "429-DT", "format": "9(1)", "reference": "ECL"}}, "128-UC": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "The balance from the patients spending account after this transaction was applied.", "metadata": {"field": "128-UC", "format": "9(6)v99"}}]}, "420-DK": {"type": "string", "description": "Code indicating that the pharmacist is clarifying the submission.", "metadata": {"field": "420-DK", "format": "9(2)", "reference": "ECL"}}, "354-NX": {"allOf": [{"$ref": "#/$defs/max3"}, {"type": "string", "description": "Count of the Submission Clarification Code (420-DK) occurrences.", "metadata": {"field": "354-NX", "format": "9(1)"}}]}, "376-2X": {"allOf": [{"$ref": "#/$defs/x65"}, {"type": "string", "description": "Free text message", "metadata": {"field": "376-2X", "format": "x(65)"}}]}, "557-AV": {"type": "string", "description": "Code indicating the payer and/or the patient is exempt from taxes.", "metadata": {"field": "557-AV", "format": "x(1)", "reference": "ECL"}}, "509-F9": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Total amount to be paid by the claims processor (i.e. pharmacy receivable). Represents a sum of Ingredient Cost Paid (506- F6), Dispensing Fee Paid (507-F7), Flat Sales Tax Amount Paid (558-AW), Percentage Sales Tax Amount Paid (559-AX), Incentive Amount Paid (521-FL), Professional Service Fee Paid (562-J1), Other Amount Paid (565-J4), less Patient Pay Amount (505-F5) and Other Payer Amount Recognized (566-J5).", "metadata": {"field": "509-F9", "format": "9(6)v99"}}]}, "103-A3": {"type": "string", "description": "Code identifying the type of transaction.", "metadata": {"field": "103-A3", "format": "x(2)", "reference": "ECL"}}, "109-A9": {"allOf": [{"$ref": "#/$defs/max3"}, {"type": "string", "description": "Count of transactions in the transmission.", "metadata": {"field": "109-A9", "format": "x(1)", "reference": "ECL"}}]}, "880-K5": {"allOf": [{"$ref": "#/$defs/x10"}, {"type": "string", "description": "A reference number assigned by the provider to each of the data records in the batch or real-time transactions. The purpose of this number is to facilitate the process of matching the transaction response to the transaction. The transaction reference number assigned should be returned in the response.", "metadata": {"field": "880-K5", "format": "x(10)"}}]}, "112-AN": {"type": "string", "description": "Code indicating the status of the transaction.", "metadata": {"field": "112-AN", "format": "x(1)", "reference": "ECL"}}, "600-28": {"type": "string", "description": "NCPDP standard product billing codes.", "metadata": {"field": "600-28", "format": "x(2)", "reference": "ECL"}}, "987-MA": {"allOf": [{"$ref": "#/$defs/url"}, {"type": "string", "description": "The web page address.", "metadata": {"field": "987-MA", "format": "x(255)"}}]}, "426-DQ": {"allOf": [{"$ref": "#/$defs/96v99"}, {"type": "string", "description": "Amount charged cash customers for the prescription exclusive of sales tax or other amounts claimed.", "metadata": {"field": "426-<PERSON><PERSON>", "format": "9(6)v99"}}]}, "102-A2": {"type": "string", "description": "Code uniquely identifying the transmission syntax and corresponding Data Dictionary.", "metadata": {"field": "102-A2", "format": "x(2)", "reference": "ECL"}}}}