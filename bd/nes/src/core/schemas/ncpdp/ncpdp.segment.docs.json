{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "ncpdp.segment.docs.json", "title": "NCPDP Claim Additional Docs Segment", "type": "object", "additionalProperties": false, "$comment": "ncpdp_doc", "properties": {"isInlineSubform": true, "segment_identification": {"type": "string", "default": "14", "allOf": [{"$ref": "ncpdp.fields.json#/$defs/111-AM"}, {"$ref": "ncpdp.ecl.json#/$defs/111-AM"}]}, "doc_type_id": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/369-2Q"}, {"$ref": "ncpdp.ecl.json#/$defs/369-2Q"}]}, "request_period_begin_date": {"$ref": "ncpdp.fields.json#/$defs/374-2V"}, "request_period_recert_revised_date": {"$ref": "ncpdp.fields.json#/$defs/375-2W"}, "request_status": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/373-2U"}, {"$ref": "ncpdp.ecl.json#/$defs/373-2U"}]}, "length_of_need_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/371-2S"}, {"$ref": "ncpdp.ecl.json#/$defs/371-2S"}]}, "length_of_need": {"$ref": "ncpdp.fields.json#/$defs/370-2R"}, "prescriber_supplier_date_signed": {"$ref": "ncpdp.fields.json#/$defs/372-2T"}, "supporting_documentation": {"$ref": "ncpdp.fields.json#/$defs/376-2X"}, "question_number_letter_count": {"default": 0, "defaultCounterLength": {"arrayField": "subform_answer"}, "$ref": "ncpdp.fields.json#/$defs/377-2Z"}, "subform_answer": {"type": "array", "maxItems": 50, "items": {"type": "object", "additionalProperties": false, "properties": {"question_number_letter": {"$ref": "ncpdp.fields.json#/$defs/378-4B"}, "question_percent_response": {"$ref": "ncpdp.fields.json#/$defs/379-4D"}, "question_date_response": {"$ref": "ncpdp.fields.json#/$defs/380-4G"}, "question_dollar_amount_response": {"$ref": "ncpdp.fields.json#/$defs/381-4H"}, "question_numeric_response": {"$ref": "ncpdp.fields.json#/$defs/382-4J"}, "question_alphanumeric_response": {"$ref": "ncpdp.fields.json#/$defs/383-4K"}}, "required": ["question_number_letter"], "anyOf": [{"required": ["question_percent_response"], "properties": {"question_percent_response": {"$ref": "ncpdp.fields.json#/$defs/379-4D"}}}, {"required": ["question_date_response"], "properties": {"question_date_response": {"$ref": "ncpdp.fields.json#/$defs/380-4G"}}}, {"required": ["question_dollar_amount_response"], "properties": {"question_dollar_amount_response": {"$ref": "ncpdp.fields.json#/$defs/381-4H"}}}, {"required": ["question_numeric_response"], "properties": {"question_numeric_response": {"$ref": "ncpdp.fields.json#/$defs/382-4J"}}}, {"required": ["question_alphanumeric_response"], "properties": {"question_alphanumeric_response": {"$ref": "ncpdp.fields.json#/$defs/383-4K"}}}], "errorMessage": {"required": "At least one response is needed."}}}}, "required": ["segment_identification", "doc_type_id"], "errorMessage": {"required": "The following fields are required: Additional Document Type ID (369-2Q)"}, "allOf": [{"if": {"properties": {"request_status": {"anyOf": [{"const": "2"}, {"const": "3"}]}}}, "then": {"required": ["request_period_recert_revised_date"], "properties": {"request_period_recert_revised_date": {"$ref": "ncpdp.fields.json#/$defs/375-2W"}}, "errorMessage": {"required": "Request Period Recert/Revised Date is required if Request Status = Revision or Recertification."}}}, {"if": {"properties": {"length_of_need": {"not": {"type": "null"}}}}, "then": {"required": ["length_of_need_qualifier"], "properties": {"length_of_need_qualifier": {"allOf": [{"$ref": "ncpdp.fields.json#/$defs/371-2S"}, {"$ref": "ncpdp.ecl.json#/$defs/371-2S"}]}}, "errorMessage": {"required": "Length of need qualifier must be selected if length of need is populated."}}}]}