{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.dme.json", "title": "837 Professional Claim DME", "type": "object", "$comment": "med_claim_dme", "additionalProperties": false, "properties": {"isInlineSubform": true, "days": {"type": "string", "$ref": "837p.fields.json#/$defs/number3", "$comment": "SV503"}, "rental_price": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "SV504"}, "purchase_price": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "SV505"}, "frequency_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SV506"}], "$comment": "SV506"}}, "required": ["days", "rental_price", "purchase_price", "frequency_code"], "errorMessage": {"required": "The following fields are required: Days (SV503), Rental Price (SV504), Purchase Price (SV505), and Frequency Code (SV506)"}}