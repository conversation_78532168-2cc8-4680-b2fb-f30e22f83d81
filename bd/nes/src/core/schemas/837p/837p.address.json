{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.address.json", "title": "837 Professional Claim Address", "type": "object", "$comment": "med_claim_address", "additionalProperties": false, "properties": {"isInlineSubform": true, "address1": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "N301"}, "address2": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "N302"}, "city": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "N401"}, "state": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-35"}, {"$ref": "837p.ecl.json#/$defs/N402"}], "$comment": "N402", "errorMessage": {"enum": "Invalid State"}}, "postal_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/3-15"}, {"$ref": "837p.fields.json#/$defs/postal_code"}], "$comment": "N403"}}, "required": ["address1", "city"], "errorMessage": {"required": "The following fields are required: Address (N301) and City (N401)"}}