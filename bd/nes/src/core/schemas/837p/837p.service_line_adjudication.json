{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.service_line_adjudication.json", "title": "837 Professional Claim Line Adjudication Information", "type": "object", "$comment": "med_claim_sl_adj", "additionalProperties": false, "properties": {"other_payer_primary_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "SVD01"}, "service_line_paid_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "SVD02"}, "service_id_qualifier": {"default": "HC", "type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SVD03-01"}], "$comment": "SVD03-01"}, "procedure_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-48"}, {"$ref": "837p.code_sets.json#/$defs/hcpc"}], "$comment": "SVD03-02"}, "procedure_code_description": {"type": "string", "$ref": "837p.fields.json#/$defs/1-48", "$comment": "SVD03-07"}, "modifier_1": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$ref": "837p.fields.json#/$defs/2-2", "$comment": "SVD03-03"}, "modifier_2": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$ref": "837p.fields.json#/$defs/2-2", "$comment": "SVD03-04"}, "modifier_3": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$ref": "837p.fields.json#/$defs/2-2", "$comment": "SVD03-05"}, "modifier_4": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$ref": "837p.fields.json#/$defs/2-2", "$comment": "SVD03-06"}, "paid_service_unit_count": {"type": "string", "$ref": "837p.fields.json#/$defs/quantity13-2", "$comment": "SVD05"}, "bundle_or_unbundled_line_number": {"type": "string", "$ref": "837p.fields.json#/$defs/number6", "$comment": "SVD06"}, "claim_adjustment_information": {"type": "array", "maxItems": 5, "items": {"type": "object", "$ref": "837p.adjustment.json"}}, "adjudication_or_payment_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 (DTP02=D8 and DTP01=573)"}, "remaining_patient_liability": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "AMT02 AMT01=EAF"}}, "required": ["adjudication_or_payment_date", "other_payer_primary_identifier", "paid_service_unit_count", "procedure_code", "service_id_qualifier", "service_line_paid_amount"], "errorMessage": {"required": "The following fields are required: Adjudication/Payment Date, Other Payer Primary Identifier, Paid Service Unit Count, Service ID Qualifier, Procedure Code, and Service Line Paid Amount"}}