{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.dates.json", "title": "837 Professional Claim Dates", "type": "object", "$comment": "med_claim_dates", "additionalProperties": false, "properties": {"isInlineSubform": true, "symptom_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "initial_treatment_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "last_seen_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "acute_manifestation_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "accident_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "last_menstrual_period_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "last_x_ray_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "hearing_and_vision_prescription_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "disability_begin_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "disability_end_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "last_worked_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "authorized_return_to_work_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "admission_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "discharge_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "assumed_and_relinquished_care_begin_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "assumed_and_relinquished_care_end_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "repricer_received_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "first_contact_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}}}