{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.service_line_supplemental.json", "title": "837 Professional Claim Service Line Supplemental Information", "type": "object", "$comment": "med_claim_sl_sup", "additionalProperties": false, "properties": {"attachment_report_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/PWK01"}], "$comment": "PWK01", "errorMessage": {"enum": "Invalid Attachment Report Code"}}, "attachment_transmission_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/PWK02"}], "$comment": "PWK02", "errorMessage": {"enum": "Invalid Attachment Transmission Code"}}, "attachment_control_number": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "PWK05"}}, "required": ["attachment_report_type_code", "attachment_transmission_code"], "errorMessage": {"required": "The following fields are required: Attachment Report Type Code (PWK01) and Attachment Transmission Code (PWK02)"}}