{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.oreferring.json", "title": "837 Professional Claim Other Referring Provider", "type": "object", "$comment": "med_claim_oprov_ref", "additionalProperties": false, "properties": {"isInlineSubform": true, "other_payer_referring_provider_identifier": {"type": "array", "maxItems": 3, "minItems": 1, "items": {"type": "object", "additionalProperties": false, "properties": {"qualifier": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-3"}, {"$ref": "837p.ecl.json#/$defs/2330E-REF01"}], "$comment": "REF01"}, "identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02"}}, "required": ["qualifier", "identifier"], "errorMessage": {"required": "The following fields are required: Identifier Qualifier (REF01) and Identifier (REF02)"}}}}, "required": ["other_payer_referring_provider_identifier"], "errorMessage": {"required": "The following fields are required: Other Referring Provider Identifier"}}