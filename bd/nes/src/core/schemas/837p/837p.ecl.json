{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.ecl.json", "title": "837 Professional Claim  External Codes List", "description": "Valid code entries from the External Codes List", "type": "object", "$defs": {"PAT09": {"type": "string", "enum": ["Y"]}, "DMG03": {"type": "string", "enum": ["M", "F", "U"]}, "SBR01": {"type": "string", "enum": ["P", "S", "T", "A", "B", "C", "D", "E", "F", "G", "H", "U"]}, "SBR05": {"type": "string", "enum": ["12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "BL", "CH", "CI", "DS", "FI", "HM", "HN", "HS", "IN", "IP", "LC", "LD", "LI", "LT", "MA", "MB", "MC", "MH", "MI", "MP", "OF", "TV", "VA", "WC", "ZZ"]}, "N402": {"type": "string", "enum": ["AL", "AK", "AZ", "AR", "AS", "CA", "CO", "CT", "DE", "DC", "FM", "FL", "GA", "GU", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MH", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "MP", "OH", "OK", "OR", "PW", "PA", "PR", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "VI", "WA", "WV", "WI", "WY", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "54", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "53", "49", "50", "51", "52", "56", "57", "58", "59", "AA", "AE", "AP", "AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT", "60"]}, "SBR01-2320": {"type": "string", "enum": ["P", "S", "T", "A", "B", "C", "D", "E", "F"]}, "SBR02": {"type": "string", "enum": ["01", "18", "19", "20", "21", "39", "40", "53", "G8"]}, "CARC": {"type": "string", "enum": ["1", "10", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "112", "114", "115", "116", "117", "118", "119", "12", "121", "122", "128", "129", "13", "130", "131", "132", "133", "134", "135", "136", "137", "139", "14", "140", "141", "142", "143", "144", "146", "147", "148", "149", "15", "150", "151", "152", "153", "154", "155", "157", "158", "159", "16", "160", "161", "163", "164", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "18", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "19", "190", "192", "193", "194", "195", "197", "198", "199", "2", "20", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "21", "210", "211", "212", "213", "215", "216", "217", "218", "219", "22", "220", "222", "223", "224", "225", "226", "227", "228", "229", "23", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "24", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "256", "257", "258", "259", "26", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "27", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "29", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "3", "300", "301", "302", "31", "32", "33", "34", "35", "39", "4", "40", "44", "45", "49", "5", "50", "51", "53", "54", "55", "56", "58", "59", "6", "60", "61", "66", "69", "7", "70", "74", "75", "76", "78", "8", "85", "89", "9", "90", "91", "94", "95", "96", "97", "A0", "A1", "A5", "A6", "A8", "B1", "B10", "B11", "B12", "B13", "B14", "B15", "B16", "B20", "B22", "B23", "B4", "B7", "B8", "B9", "P1", "P10", "P11", "P12", "P13", "P14", "P15", "P16", "P17", "P18", "P19", "P2", "P20", "P21", "P22", "P23", "P24", "P25", "P26", "P27", "P28", "P29", "P3", "P30", "P31", "P4", "P5", "P6", "P7", "P8", "P9"]}, "RARC": {"type": "string", "enum": ["M50", "M51", "M52", "M53", "M54", "M55", "M56", "M59", "M60", "M61", "M62", "M64", "M65", "M66", "M67", "M69", "M70", "M71", "M73", "M74", "M75", "M76", "M77", "M79", "M80", "M81", "M82", "M83", "M84", "M85", "M86", "M87", "M89", "M90", "M91", "M93", "M94", "M95", "M96", "M97", "M99", "M100", "M102", "M103", "M104", "M105", "M107", "M109", "M111", "M112", "M113", "M114", "M115", "M116", "M117", "M119", "M121", "M122", "M123", "M124", "M125", "M126", "M127", "M129", "M130", "M131", "M132", "M133", "M134", "M135", "M136", "M137", "M138", "M139", "M141", "M142", "M143", "M144", "N1", "N2", "N3", "N4", "N5", "N6", "N7", "N8", "N9", "N10", "N11", "N12", "N13", "N15", "N16", "N19", "N20", "N21", "N22", "N23", "N24", "N25", "N26", "N27", "N28", "N30", "N31", "N32", "N33", "N34", "N35", "N36", "N37", "N39", "N40", "N42", "N43", "N45", "N46", "N47", "N48", "N49", "N50", "N51", "N52", "N53", "N54", "N55", "N56", "N57", "N58", "N59", "N61", "N62", "N63", "N64", "N65", "N67", "N68", "N69", "N70", "N71", "N72", "N74", "N75", "N76", "N77", "N78", "N79", "N80", "N81", "N82", "N83", "N84", "N85", "N86", "N87", "N88", "N89", "N90", "N91", "N92", "N130", "N131", "N132", "N133", "N134", "N135", "N136", "N137", "N138", "N139", "N140", "N141", "N142", "N143", "N144", "N146", "N147", "N148", "N149", "N150", "N151", "N152", "N153", "N154", "N155", "N156", "N157", "N158", "N159", "N160", "N161", "N162", "N163", "N167", "N170", "N171", "N172", "N173", "N174", "N175", "N176", "N177", "N178", "N179", "N180", "N181", "N182", "N183", "N184", "N185", "N186", "N187", "N188", "N189", "N190", "N191", "N192", "N193", "N194", "N195", "N196", "N197", "N198", "N199", "N200", "N202", "N203", "N204", "N205", "N206", "N207", "N208", "N209", "N210", "N211", "N212", "N213", "N214", "N215", "N216", "N217", "N218", "N219", "N220", "N221", "N222", "N223", "N224", "N226", "N227", "N228", "N229", "N230", "N231", "N232", "N233", "N234", "N235", "N236", "N237", "N238", "N239", "N240", "N241", "N242", "N243", "N244", "N245", "N246", "N247", "N248", "N249", "N250", "N251", "N252", "N253", "N254", "N255", "N256", "N257", "N258", "N259", "N260", "N261", "N262", "N263", "N264", "N265", "N266", "N267", "N268", "N269", "N270", "N271", "N272", "N273", "N274", "N275", "N276", "N277", "N278", "N279", "N280", "N281", "N282", "N283", "N284", "N285", "N286", "N287", "N288", "N289", "N290", "N291", "N292", "N293", "N294", "N295", "N296", "N297", "N298", "N299", "N300", "N301", "N302", "N303", "N304", "N305", "N306", "N307", "N308", "N309", "N310", "N311", "N312", "N313", "N314", "N315", "N316", "N317", "N318", "N319", "N320", "N321", "N322", "N323", "N324", "N325", "N326", "N327", "N328", "N329", "N330", "N331", "N332", "N333", "N334", "N335", "N336", "N337", "N338", "N339", "N340", "N341", "N342", "N343", "N344", "N345", "N346", "N347", "N348", "N349", "N350", "N351", "N352", "N353", "N354", "N355", "N356", "N357", "N358", "N359", "N360", "N362", "N363", "N364", "N366", "N367", "N368", "N369", "N370", "N371", "N372", "N373", "N374", "N375", "N376", "N377", "N378", "N379", "N380", "N381", "N382", "N383", "N384", "N385", "N386", "N387", "N388", "N389", "N390", "N391", "N392", "N393", "N394", "N395", "N396", "N397", "N398", "N399", "N400", "N401", "N402", "N403", "N404", "N405", "N406", "N407", "N408", "N409", "N410", "N411", "N412", "N413", "N414", "N415", "N416", "N417", "N418", "N419", "N420", "N421", "N422", "N423", "N424", "N425", "N426", "N427", "N428", "N429", "N430", "N431", "N432", "N433", "N434", "N435", "N436", "N437", "N438", "N439", "N440", "N441", "N442", "N443", "N444", "N445", "N446", "N447", "N448", "N449", "N450", "N451", "N452", "N453", "N454", "N455", "N456", "N457", "N458", "N459", "N460", "N461", "N462", "N463", "N464", "N465", "N466", "N467", "N468", "N469", "N470", "N471", "N472", "N473", "N474", "N475", "N476", "N477", "N478", "N479", "N480", "N481", "N482", "N485", "N486", "N487", "N488", "N489", "N490", "N491", "N492", "N493", "N494", "N495", "N496", "N497", "N498", "N499", "N500", "N501", "N502", "N503", "N504", "N505", "N506", "N507", "N508", "N509", "N510", "N511", "N512", "N513", "N516", "N517", "N518", "N519", "N520", "N521", "N522", "N523", "N524", "N525", "N526", "N527", "N528", "N529", "N530", "N531", "N532", "N533", "N534", "N535", "N536", "N537", "N538", "N539", "N540", "N541", "N542", "N543", "N544", "N545", "N546", "N547", "N548", "N549", "N550", "N551", "N552", "N554", "N555", "N556", "N557", "N558", "N559", "N560", "N561", "N562", "N563", "N564", "N565", "N566", "N567", "N568", "N569", "N570", "N571", "N572", "N573", "N574", "N575", "N576", "N577", "N578", "N579", "N580", "N581", "N582", "N583", "N584", "N585", "N586", "N587", "N588", "N589", "N590", "N591", "N592", "N593", "N594", "N595", "N596", "N597", "N598", "N599", "N600", "N601", "N602", "N603", "N604", "N605", "N606", "N607", "N608", "N609", "N610", "N611", "N612", "N613", "N614", "N615", "N616", "N617", "N618", "N619", "N620", "N621", "N622", "N623", "N624", "N625", "N626", "N628", "N629", "N630", "N631", "N633", "N634", "N635", "N636", "N637", "N638", "N639", "N640", "N641", "N642", "N643", "N644", "N645", "N646", "N647", "N648", "N649", "N650", "N651", "N652", "N653", "N654", "N655", "N656", "N657", "N658", "N659", "N660", "N661", "N662", "N663", "N664", "N665", "N666", "N667", "N668", "N669", "N670", "N671", "N672", "N673", "N674", "N675", "N676", "N677", "N678", "N679", "N680", "N681", "N682", "N683", "N684", "N685", "N686", "N687", "N688", "N689", "N690", "N691", "N692", "N693", "N694", "N695", "N696", "N697", "N698", "N699", "N700", "N701", "N702", "N703", "N704", "N705", "N706", "N707", "N708", "N709", "N710", "N711", "N712", "N713", "N714", "N715", "N716", "N717", "N718", "N719", "N720", "N721", "N722", "N723", "N724", "N725", "N726", "N727", "N728", "N729", "N730", "N731", "N732", "N733", "N734", "N736", "N737", "N738", "N739", "N740", "N741", "N743", "N744", "N745", "N746", "N747", "N748", "N749", "N750", "N751", "N752", "N753", "N754", "N755", "N756", "N757", "N758", "N759", "N760", "N761", "N762", "N763", "N764", "N765", "N766", "N767", "N768", "N769", "N770", "N771", "N772", "N773", "N774", "N775", "N776", "N777", "N778", "N779", "N780", "N781", "N782", "N783", "N784", "N785", "N786", "N787", "N788", "N789", "N790", "N791", "N792", "N794", "N795", "N796", "N797", "N798", "N799", "N800", "N801", "N802", "N803", "N804", "N805", "N806", "N807", "N808", "N809", "N810", "N811", "N812", "N815", "N816", "N817", "N818", "N819", "N820", "N821", "N822", "N823", "N824", "N825", "N826", "N827", "N828", "N829", "N830", "N831", "N832", "N833", "N834", "N835", "N836", "N837", "N838", "N839", "N840", "N841", "N842", "N843", "N844", "N845", "N846", "N847", "N848", "N849", "N850", "N851", "N852", "N853", "N854", "N855", "N856", "N857", "N858", "N859", "N860", "N861", "N862", "N863", "N864", "N865", "N866", "N867", "N868", "N869", "N870", "N871", "N872", "N873", "N874", "N875", "N876", "N877", "N878", "N879", "N880", "N881", "N882", "N883", "N884", "N885", "N886", "N887", "N888", "N889", "N890", "N891", "N892", "N893", "N894", "N895", "N896", "N897", "N898", "N899", "N900", "N901", "N902", "N903", "N904"]}, "NM102": {"type": "string", "enum": ["1", "2"]}, "NM108-2320": {"type": "string", "enum": ["II", "MI"]}, "PAT08": {"type": "string", "enum": ["LB", "KG"]}, "NM108-2330B": {"type": "string", "enum": ["PI", "XV"]}, "PAT01": {"type": "string", "enum": ["01", "19", "20", "39", "40", "53", "G8"]}, "ISA15": {"type": "string", "enum": ["T", "P"]}, "NM101": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReferringProvider", "RenderingProvider", "OrderingProvider", "SupervisingProvider"]}, "OI03-2320": {"type": "string", "enum": ["Y", "N", "W"]}, "OI04-2320": {"type": "string", "enum": ["Y", "N", "P"]}, "OI06-2320": {"type": "string", "enum": ["Y", "N", "I"]}, "PRV03": {"type": "string", "enum": ["193200000X", "193400000X", "207K00000X", "207KA0200X", "207KI0005X", "207L00000X", "207LA0401X", "207LC0200X", "207LH0002X", "207LP2900X", "207LP3000X", "208U00000X", "208C00000X", "207N00000X", "207NI0002X", "207ND0900X", "207ND0101X", "207NP0225X", "207NS0135X", "204R00000X", "207P00000X", "207PE0004X", "207PH0002X", "207PT0002X", "207PP0204X", "207PS0010X", "207PE0005X", "207Q00000X", "207QA0401X", "207QA0000X", "207QA0505X", "207QG0300X", "207QH0002X", "207QB0002X", "207QS1201X", "207QS0010X", "208D00000X", "208M00000X", "202C00000X", "202D00000X", "207R00000X", "207RA0401X", "207RA0000X", "207RA0002X", "207RA0001X", "207RA0201X", "207RC0000X", "207RI0001X", "207RC0001X", "207RC0200X", "207RE0101X", "207RG0100X", "207RG0300X", "207RH0000X", "207RH0003X", "207RI0008X", "207RH0002X", "207RH0005X", "207RI0200X", "207RI0011X", "207RM1200X", "207RX0202X", "207RN0300X", "207RB0002X", "207RP1001X", "207RR0500X", "207RS0012X", "207RS0010X", "207RT0003X", "209800000X", "207SG0202X", "207SC0300X", "207SG0201X", "207SG0203X", "207SG0207X", "207SM0001X", "207SG0205X", "207T00000X", "204D00000X", "204C00000X", "207U00000X", "207UN0903X", "207UN0901X", "207UN0902X", "207V00000X", "207VC0300X", "207VC0200X", "207VF0040X", "207VX0201X", "207VG0400X", "207VH0002X", "207VM0101X", "207VB0002X", "207VX0000X", "207VE0102X", "207W00000X", "207WX0120X", "207WX0009X", "207WX0109X", "207WX0200X", "207WX0110X", "207WX0107X", "207WX0108X", "204E00000X", "207X00000X", "207XS0114X", "207XX0004X", "207XS0106X", "207XS0117X", "207XX0801X", "207XP3100X", "207XX0005X", "207Y00000X", "207YS0123X", "207YX0602X", "207YX0905X", "207YX0901X", "207YP0228X", "207YX0007X", "207YS0012X", "208VP0014X", "208VP0000X", "207ZP0101X", "207ZP0102X", "207ZB0001X", "207ZP0104X", "207ZC0008X", "207ZC0006X", "207ZP0105X", "207ZC0500X", "207ZD0900X", "207ZF0201X", "207ZH0000X", "207ZI0100X", "207ZM0300X", "207ZP0007X", "207ZN0500X", "207ZP0213X", "208000000X", "2080A0000X", "2080C0008X", "2080I0007X", "2080P0006X", "2080H0002X", "2080T0002X", "2080N0001X", "2080P0008X", "2080B0002X", "2080P0201X", "2080P0202X", "2080P0203X", "2080P0204X", "2080P0205X", "2080P0206X", "2080P0207X", "2080P0208X", "2080P0210X", "2080P0214X", "2080P0216X", "2080T0004X", "2080S0012X", "2080S0010X", "202K00000X", "208100000X", "2081P0301X", "2081H0002X", "2081N0008X", "2081P2900X", "2081P0010X", "2081P0004X", "2081S0010X", "208200000X", "2082S0099X", "2082S0105X", "2083A0300X", "2083A0100X", "2083C0008X", "2083T0002X", "2083B0002X", "2083X0100X", "2083P0500X", "2083P0901X", "2083S0010X", "2083P0011X", "2084A0401X", "2084P0802X", "2084B0040X", "2084P0301X", "2084P0804X", "2084N0600X", "2084D0003X", "2084E0001X", "2084F0202X", "2084P0805X", "2084H0002X", "2084A2900X", "2084P0005X", "2084N0400X", "2084N0402X", "2084N0008X", "2084B0002X", "2084P2900X", "2084P0800X", "2084P0015X", "2084S0012X", "2084S0010X", "2084V0102X", "2085B0100X", "2085D0003X", "2085R0202X", "2085U0001X", "2085H0002X", "2085N0700X", "2085N0904X", "2085P0229X", "2085R0001X", "2085R0205X", "2085R0203X", "2085R0204X", "2085R0203X", "208600000X", "2086H0002X", "2086S0120X", "2086S0122X", "2086S0105X", "2086S0102X", "2086X0206X", "2086S0127X", "2086S0129X", "208G00000X", "204F00000X", "208800000X", "2088F0040X", "2088P0231X", "106E00000X", "106S00000X", "103K00000X", "103G00000X", "103GC0700X", "101Y00000X", "101YA0400X", "101YM0800X", "101YP1600X", "101YP2500X", "101YS0200X", "101200000X", "106H00000X", "102X00000X", "102L00000X", "103T00000X", "103TA0400X", "103TA0700X", "103TC0700X", "103TC2200X", "103TB0200X", "103TC1900X", "103TE1000X", "103TE1100X", "103TF0000X", "103TF0200X", "103TP2701X", "111NI0900X", "111NN0400X", "111NN1001X", "111NX0100X", "111NX0800X", "111NP0017X", "111NR0200X", "111NR0400X", "111NS0005X", "111NT0100X", "125K00000X", "126800000X", "124Q00000X", "126900000X", "125J00000X", "122300000X", "1223D0001X", "1223D0004X", "1223E0200X", "1223G0001X", "1223P0106X", "1223X0008X", "1223S0112X", "125Q00000X", "1223X2210X", "1223X0400X", "1223P0221X", "1223P0300X", "1223P0700X", "122400000X", "132700000X", "136A00000X", "133V00000X", "133VN1101X", "133VN1006X", "133VN1201X", "133VN1301X", "133VN1004X", "133VN1401X", "133VN1005X", "133VN1501X", "133N00000X", "133NN1002X", "146N00000X", "146M00000X", "146L00000X", "146D00000X", "152W00000X", "152WC0802X", "152WL0500X", "152WX0102X", "152WP0200X", "152WS0006X", "152WV0400X", "156F00000X", "156FC0800X", "156FC0801X", "156FX1700X", "156FX1100X", "156FX1101X", "156FX1800X", "156FX1201X", "156FX1202X", "156FX1900X", "164W00000X", "167G00000X", "164X00000X", "163W00000X", "163WA0400X", "163WA2000X", "163WP2201X", "163WC3500X", "163WC0400X", "163WC1400X", "163WC1500X", "163WC2100X", "163WC1600X", "163WC0200X", "163WD0400X", "163WD1100X", "163WE0003X", "163WE0900X", "163WF0300X", "163WG0100X", "163WG0000X", "163WG0600X", "163WH0500X", "163WH0200X", "163WH1000X", "163WI0600X", "163WI0500X", "163WL0100X", "163WM0102X", "163WM0705X", "163WN0002X", "163WN0003X", "163WN0300X", "163WN0800X", "163WM1400X", "163WN1003X", "163WX0002X", "163WX0003X", "163WX0106X", "163WX0200X", "163WX1100X", "163WX0800X", "163WX1500X", "163WX0601X", "163WP0000X", "163WP0218X", "163WP0200X", "163WP1700X", "163WS0121X", "163WP0808X", "163WP0809X", "163WP0807X", "163WR0006X", "163WR0400X", "163WR1000X", "163WS0200X", "163WU0100X", "163WW0101X", "163WW0000X", "372600000X", "372500000X", "373H00000X", "374J00000X", "374U00000X", "376J00000X", "376K00000X", "376G00000X", "374T00000X", "374K00000X", "374700000X", "3747A0650X", "3747P1801X", "171100000X", "171M00000X", "174V00000X", "172V00000X", "171W00000X", "171WH0202X", "171WV0202X", "172A00000X", "176P00000X", "170300000X", "171400000X", "174H00000X", "175L00000X", "171R00000X", "174N00000X", "175M00000X", "173000000X", "172M00000X", "176B00000X", "171000000X", "1710I1002X", "1710I1003X", "172P00000X", "175F00000X", "175T00000X", "170100000X", "405300000X", "173C00000X", "173F00000X", "174400000X", "1744G0900X", "1744P3200X", "1744R1103X", "1744R1102X", "174M00000X", "174MM1900X", "183500000X", "1835P2201X", "1835C0206X", "1835C0207X", "1835C0205X", "1835E0208X", "1835G0000X", "1835G0303X", "1835I0206X", "1835N0905X", "1835N1003X", "1835X0200X", "1835P0200X", "1835P0018X", "1835P1200X", "1835P1300X", "1835S0206X", "183700000X", "367A00000X", "367H00000X", "364S00000X", "364SA2100X", "364SA2200X", "364SC2300X", "364SC1501X", "364SC0200X", "364SE0003X", "364SE1400X", "364SF0001X", "364SG0600X", "364SH1100X", "364SH0200X", "364SI0800X", "364SL0600X", "364SM0705X", "364SN0000X", "364SN0800X", "364SX0106X", "364SX0200X", "364SX0204X", "364SP0200X", "364SP1700X", "364SP2800X", "364SP0808X", "364SP0809X", "364SP0807X", "364SP0810X", "364SP0811X", "364SP0812X", "364SP0813X", "364SR0400X", "364SS0200X", "364ST0500X", "364SW0102X", "367500000X", "363L00000X", "363LA2100X", "363LA2200X", "363LC1500X", "363LC0200X", "363LF0000X", "363LG0600X", "363LN0000X", "363LN0005X", "363LX0001X", "363LP0200X", "363LP0222X", "363LP1700X", "363LP2300X", "363LP0808X", "363LS0200X", "363LW0102X", "363A00000X", "363AM0700X", "363AS0400X", "211D00000X", "213E00000X", "213ES0103X", "213ES0131X", "213EG0000X", "213EP1101X", "213EP0504X", "213ER0200X", "213ES0000X", "229N00000X", "221700000X", "224Y00000X", "225600000X", "222Q00000X", "226300000X", "225700000X", "224900000X", "225A00000X", "225X00000X", "225XR0403X", "225XE0001X", "225XE1200X", "225XF0002X", "225XG0600X", "225XH1200X", "225XH1300X", "225XL0004X", "225XM0800X", "225XN1300X", "225XP0200X", "225XP0019X", "224Z00000X", "224ZR0403X", "224ZE0001X", "224ZF0002X", "224ZL0004X", "225000000X", "222Z00000X", "224L00000X", "225100000X", "2251C2600X", "2251E1300X", "2251E1200X", "2251G0304X", "2251H1200X", "2251H1300X", "2251N0400X", "2251X0800X", "2251P0200X", "2251S0007X", "225200000X", "224P00000X", "225B00000X", "225800000X", "226000000X", "225C00000X", "225CA2400X", "225CA2500X", "225CX0006X", "225400000X", "227800000X", "2278C0205X", "2278E1000X", "2278E0002X", "2278G1100X", "2278G0305X", "2278H0200X", "2278P3900X", "2278P3800X", "2278P4000X", "2278P1004X", "2278P1006X", "2278P1005X", "2278S1500X", "227900000X", "2279C0205X", "2279E1000X", "2279E0002X", "2279G1100X", "2279G0305X", "2279H0200X", "2279P3900X", "2279P3800X", "2279P4000X", "2279P1004X", "2279P1006X", "2279P1005X", "2279S1500X", "225500000X", "2255A2300X", "2255R0406X", "231H00000X", "231HA2400X", "231HA2500X", "237600000X", "237700000X", "235500000X", "2355A2700X", "2355S0801X", "235Z00000X", "390200000X", "242T00000X", "247100000X", "2471B0102X", "2471C1106X", "2471C1101X", "2471C3401X", "2471M1202X", "2471M2300X", "2471N0900X", "2471Q0001X", "2471R0002X", "2471C3402X", "2471S1302X", "2471V0105X", "2471V0106X", "243U00000X", "246X00000X", "246XC2901X", "246XS1301X", "246XC2903X", "246Y00000X", "246YC3301X", "246YC3302X", "246YR1600X", "246Z00000X", "246ZA2600X", "246ZB0500X", "246ZB0301X", "246ZB0302X", "246ZB0600X", "246ZE0500X", "246ZE0600X", "246ZG1000X", "246ZG0701X", "246ZI1000X", "246ZN0300X", "246ZX2200X", "246ZC0007X", "246ZS0410X", "246Q00000X", "246QB0000X", "246QC1000X", "246QC2700X", "246QH0401X", "246QH0000X", "246QH0600X", "246QI0000X", "246QL0900X", "246QL0901X", "246QM0706X", "246QM0900X", "246W00000X", "247000000X", "2470A2800X", "247200000X", "2472B0301X", "2472D0500X", "2472E0500X", "2472R0900X", "2472V0600X", "246R00000X", "247ZC0005X", "246RH0600X", "246RM2200X", "246RP1900X", "251B00000X", "251S00000X", "251C00000X", "252Y00000X", "253J00000X", "251E00000X", "251F00000X", "251G00000X", "253Z00000X", "251300000X", "251J00000X", "251T00000X", "251K00000X", "251X00000X", "251V00000X", "261Q00000X", "261QM0855X", "261QA0600X", "261QM0850X", "261QA0005X", "261QA0006X", "261QA1903X", "261QA0900X", "261QA3000X", "261QB0400X", "261QC1500X", "261QC1800X", "261QC0050X", "261QD0000X", "261QD1600X", "261QE0002X", "261QE0700X", "261QE0800X", "261QF0050X", "261QF0400X", "261QG0250X", "261QH0100X", "261QH0700X", "261QI0500X", "261QL0400X", "261QM1200X", "261QM2500X", "261QM3000X", "261QM0801X", "261QM2800X", "261QM1000X", "261QM1103X", "261QM1101X", "261QM1102X", "261QM1100X", "261QM1300X", "261QX0100X", "261QX0200X", "261QX0203X", "261QS0132X", "261QS0112X", "261QP3300X", "261QP2000X", "261QP1100X", "261QP2300X", "261QP2400X", "261QP0904X", "261QP0905X", "261QR0200X", "261QR0206X", "261QR0208X", "261QR0207X", "261QR0800X", "261QR0400X", "261QR0404X", "261QR0401X", "261QR0405X", "261QR1100X", "261QR1300X", "261QS1200X", "261QS1000X", "261QU0200X", "261QV0200X", "273100000X", "275N00000X", "273R00000X", "273Y00000X", "276400000X", "287300000X", "281P00000X", "281PC2000X", "282N00000X", "282NC2000X", "282NC0060X", "282NR1301X", "282NW0100X", "282E00000X", "286500000X", "2865C1500X", "2865M2000X", "2865X1600X", "283Q00000X", "283X00000X", "283XC2000X", "282J00000X", "284300000X", "291U00000X", "292200000X", "291900000X", "293D00000X", "302F00000X", "302R00000X", "305S00000X", "305R00000X", "311500000X", "310400000X", "3104A0630X", "3104A0625X", "317400000X", "311Z00000X", "311ZA0620X", "315D00000X", "315P00000X", "310500000X", "313M00000X", "314000000X", "3140N1450X", "177F00000X", "174200000X", "320800000X", "320900000X", "323P00000X", "322D00000X", "320600000X", "320700000X", "324500000X", "3245S0500X", "385H00000X", "385HR2050X", "385HR2055X", "385HR2060X", "385HR2065X", "331L00000X", "332100000X", "332B00000X", "332BC3200X", "332BD1200X", "332BN1400X", "332BX2000X", "332BP3500X", "333300000X", "332G00000X", "332H00000X", "332S00000X", "332U00000X", "332800000X", "335G00000X", "332000000X", "332900000X", "335U00000X", "333600000X", "3336C0002X", "3336C0003X", "3336C0004X", "3336H0001X", "3336I0012X", "3336L0003X", "3336M0002X", "3336M0003X", "3336N0007X", "3336S0011X", "335V00000X", "335E00000X", "344800000X", "341600000X", "3416A0800X", "3416L0300X", "3416S0300X", "347B00000X", "341800000X", "3418M1120X", "3418M1110X", "3418M1130X", "343900000X", "347C00000X", "343800000X", "344600000X", "347D00000X", "347E00000X", "342000000X"]}, "SBR09": {"type": "string", "enum": ["11", "12", "13", "14", "15", "16", "17", "AM", "BL", "CH", "CI", "DS", "FI", "HM", "LM", "MA", "MB", "MC", "OF", "TV", "VA", "WC", "ZZ"]}, "CLM05-01": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "31", "32", "33", "34", "41", "42", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "65", "66", "71", "72", "81", "99"]}, "CLM05-03": {"type": "string", "enum": ["1", "7", "8", "9"]}, "CLM06": {"type": "string", "enum": ["N", "Y"]}, "CLM07": {"type": "string", "enum": ["A", "B", "C"]}, "CLM08": {"type": "string", "enum": ["N", "W", "Y"]}, "CLM09": {"type": "string", "enum": ["I", "Y"]}, "CLM10": {"type": "string", "enum": ["P"]}, "CLM11": {"type": "string", "enum": ["AA", "EM", "OA"]}, "CLM12": {"type": "string", "enum": ["02", "03", "05", "09"]}, "CLM20": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "15"]}, "CN101": {"type": "string", "enum": ["01", "02", "03", "04", "05", "06", "09"]}, "PWK01": {"type": "string", "enum": ["03", "04", "05", "06", "07", "08", "09", "10", "11", "13", "15", "21", "A3", "A4", "AM", "AS", "B2", "B3", "B4", "BR", "BS", "BT", "CB", "CK", "CT", "D2", "DA", "DB", "DG", "DJ", "DS", "EB", "HC", "HR", "I5", "IR", "LA", "M1", "MT", "NM", "OB", "OC", "OD", "OE", "OX", "OZ", "P4", "P5", "PE", "PN", "PO", "PQ", "PY", "PZ", "RB", "RR", "RT", "RX", "SG", "V5", "XP"]}, "PWK02": {"type": "string", "enum": ["AA", "BM", "EL", "EM", "FT", "FX"]}, "REF02-4N": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6", "7"]}, "HI0*": {"type": "string", "enum": ["BK", "ABK", "BF", "ABF"]}, "HCP01": {"type": "string", "enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14"]}, "HCP13": {"type": "string", "enum": ["T1", "T2", "T3", "T4", "T5", "16"]}, "CAS01": {"type": "string", "enum": ["CO", "CR", "OA", "PI", "PR"]}, "O103": {"type": "string", "enum": ["N", "W", "Y"]}, "O104": {"type": "string", "enum": ["P"]}, "O106": {"type": "string", "enum": ["I", "Y"]}, "2330B-REF01": {"type": "string", "enum": ["2U", "PI", "NI", "XV", "ZZ"]}, "2330C-REF01": {"type": "string", "enum": ["NF", "G2", "0B", "ZZ"]}, "2330D-REF01": {"type": "string", "enum": ["NF", "G2", "0B", "ZZ"]}, "2330G-REF01": {"type": "string", "enum": ["NF", "G2", "0B", "ZZ"]}, "2330E-REF01": {"type": "string", "enum": ["0B", "1D", "1G", "1H", "EI", "G2", "LU", "SY", "X5", "ZZ"]}, "SV101": {"type": "string", "enum": ["ER", "HC", "IV", "WK"]}, "SV103": {"type": "string", "enum": ["MJ", "UN"]}, "SV105": {"type": "string", "enum": ["01", "12", "15", "19", "20", "21", "22", "23", "31", "32", "41", "50", "61", "99"]}, "SV109": {"type": "string", "enum": ["N", "Y"]}, "SV111": {"type": "string", "enum": ["N", "Y"]}, "SV112": {"type": "string", "enum": ["N", "Y"]}, "SV115": {"type": "string", "enum": ["0"]}, "SV506": {"type": "string", "enum": ["1", "4", "6"]}, "PWK02-DME-Code": {"type": "string", "enum": ["AB", "AD", "AF", "AG", "NS"]}, "CR301": {"type": "string", "enum": ["I", "R", "S"]}, "CRC02": {"type": "string", "enum": ["N", "Y"]}, "CRC03": {"type": "string", "enum": ["01", "12"]}, "CRC04": {"type": "string", "enum": ["38", "ZV"]}, "HCP14": {"type": "string", "enum": ["1", "2", "3", "4", "5"]}, "HCP15": {"type": "string", "enum": ["1", "2", "3", "4", "5", "6"]}, "SVD03-01": {"type": "string", "enum": ["ER", "HC", "HP", "IV", "WK"]}, "LQ01": {"type": "string", "enum": ["AS", "UT"]}, "FRM02": {"type": "string", "enum": ["N", "W", "Y"]}, "2010AC-REF01": {"type": "string", "enum": ["2U", "FY", "NF"]}, "LIN02": {"type": "string", "enum": ["EN", "EO", "HI", "N4", "ON", "UK", "UP"]}, "CTP05-01": {"type": "string", "enum": ["F2", "GR", "ME", "ML", "UN"]}}}