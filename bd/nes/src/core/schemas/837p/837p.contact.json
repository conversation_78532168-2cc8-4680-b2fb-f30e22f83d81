{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.contact.json", "title": "837 Professional Claim Contact Info", "type": "object", "$comment": "med_claim_contact", "additionalProperties": false, "properties": {"isInlineSubform": true, "name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "PER02 and PER01 !=C"}, "phone_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/phone10"}, {"$ref": "837p.fields.json#/$defs/1-10"}], "$comment": "PER04"}, "phone_extension": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/ext8"}, {"$ref": "837p.fields.json#/$defs/1-8"}], "$comment": "PER04"}, "fax_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/phone10"}, {"$ref": "837p.fields.json#/$defs/1-10"}], "$comment": "PER04/PER06/PER08"}, "email": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-256"}, {"$ref": "837p.fields.json#/$defs/email"}], "$comment": "PER04/PER06/PER08"}}}