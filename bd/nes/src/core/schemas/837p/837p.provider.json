{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.provider.json", "title": "837 Professional Claim Provider", "type": "object", "$comment": "med_claim_provider", "additionalProperties": false, "properties": {"isInlineSubform": true, "provider_type": {"type": "string", "$ref": "837p.ecl.json#/$defs/NM101", "$comment": "NM101"}, "npi": {"type": "string", "$ref": "837p.licenses.json#/$defs/npi", "$comment": "NM109"}, "ssn": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/ssn"}], "$comment": "REF02 REF01=SY"}, "employer_id": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=EI"}, "commercial_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=G2"}, "location_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=LU"}, "payer_identification_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=2U"}, "employer_identification_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=EI"}, "claim_office_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=FY"}, "naic": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/naic"}], "$comment": "REF02 REF01=NF"}, "state_license_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/state_license"}], "$comment": "REF02 REF01=0B"}, "provider_upin_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/upin"}], "$comment": "REF02 REF01=1G"}, "taxonomy_code": {"type": "string", "$ref": "837p.code_sets.json#/$defs/taxonomy_code", "$comment": "PRV03"}, "last_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "first_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "NM104"}, "middle_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-25", "$comment": "NM105"}, "suffix": {"type": "string", "$ref": "837p.fields.json#/$defs/1-10", "$comment": "NM107"}, "organization_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "contact_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.contact.json"}}, "address": {"isInlineSubform": true, "type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.address.json"}}}, "required": ["provider_type"], "anyOf": [{"required": ["last_name"], "properties": {"last_name": {"$ref": "837p.fields.json#/$defs/1-60"}}}, {"required": ["organization_name"], "properties": {"organization_name": {"$ref": "837p.fields.json#/$defs/1-60"}}}], "errorMessage": {"required": "The following fields are required: Provider Type Code (NM101), Organization Name, Provider Last Name, Provider Address", "anyOf": "Either SSN or Employer ID is required"}}