const { Schema } = require("../index");
const Custom837pKeywords = {
    // Converts MM/DD/YYYY into YYYYMMDD
    TRANS_DATE: "transformX12Date",
    // Convert whole number percentage to fractional (112% -> 1.12)
    TRANS_PERC: "transformX12Percent",
    // Collects procedure modifiers into proper API expected array
    TRANS_MOD: "transformX12Modifiers",
};

module.exports = class SchemaValidatorClass extends Schema {
    constructor() {
        super();
        this.keyword = "837p";
    }

    async init() {
        return this;
    }

    async getCompiledSchemas(ajv) {
        // Primary 847p Schemas
        const submitterSchema = require("./837p.submitter.json");
        const receiverSchema = require("./837p.receiver.json");
        const subscriberSchema = require("./837p.subscriber.json");
        const dependentSchema = require("./837p.dependent.json");
        const claimInfoSchema = require("./837p.claim_info.json");
        const addressSchema = require("./837p.address.json");
        const providerSchema = require("./837p.provider.json");

        // Support Schemas
        const eclSchema = require("./837p.ecl.json");
        const adjustmentSchema = require("./837p.adjustment.json");
        const fieldsSchema = require("./837p.fields.json");
        const codesSchema = require("./837p.code_sets.json");
        const contactSchema = require("./837p.contact.json");
        const contractSchema = require("./837p.contract.json");
        const datesSchema = require("./837p.dates.json");
        const dmeSchema = require("./837p.dme.json");
        const claimNoteSchema = require("./837p.claim_note.json");
        const claimInfoOtherSchema = require("./837p.claim_info_other.json");
        const drugSchema = require("./837p.drug.json");
        const facilitySchema = require("./837p.facility.json");
        const formIdentificationSchema = require("./837p.form_identification.json");
        const licensesSchema = require("./837p.licenses.json");
        const lineAdjudicationSchema = require("./837p.service_line_adjudication.json");
        const otherBillingProviderSchema = require("./837p.obilling_provider.json");
        const otherFacilitySchema = require("./837p.ofacility.json");
        const otherPayerSchema = require("./837p.opayer.json");
        const otherReferringProviderSchema = require("./837p.oreferring.json");
        const otherRenderingProviderSchema = require("./837p.orendering.json");
        const otherSubscriberSchema = require("./837p.osubscriber.json");
        const otherSubscriberNameSchema = require("./837p.osubscriber_name.json");
        const otherSupervisingProviderSchema = require("./837p.osupervisor.json");
        const repricingSchema = require("./837p.repricing.json");
        const serviceLineDateSchema = require("./837p.service_line_date.json");
        const serviceLineReferenceSchema = require("./837p.service_line_reference.json");
        const serviceLineSupplementalSchema = require("./837p.service_line_supplemental.json");
        const serviceLineSchema = require("./837p.service_line.json");
        const serviceSchema = require("./837p.service.json");
        const supplementalSchema = require("./837p.supplemental.json");
        const supportingDocSchema = require("./837p.supporting_documentation.json");

        // Register primary Schemas
        ajv.addSchema(submitterSchema, "837p.submitter.json");
        ajv.addSchema(receiverSchema, "837p.receiver.json");
        ajv.addSchema(subscriberSchema, "837p.subscriber.json");
        ajv.addSchema(dependentSchema, "837p.dependent.json");
        ajv.addSchema(claimInfoSchema, "837p.claim_info.json");
        ajv.addSchema(providerSchema, "837p.provider.json");
        ajv.addSchema(addressSchema, "837p.address.json");

        // Register Support Schemas
        ajv.addSchema(eclSchema, "837p.ecl.json");
        ajv.addSchema(adjustmentSchema, "837p.adjustment.json");
        ajv.addSchema(fieldsSchema, "837p.fields.json");
        ajv.addSchema(codesSchema, "837p.code_sets.json");
        ajv.addSchema(contactSchema, "837p.contact.json");
        ajv.addSchema(contractSchema, "837p.contract.json");
        ajv.addSchema(datesSchema, "837p.dates.json");
        ajv.addSchema(dmeSchema, "837p.dme.json");
        ajv.addSchema(claimNoteSchema, "837p.claim_note.json");
        ajv.addSchema(drugSchema, "837p.drug.json");
        ajv.addSchema(facilitySchema, "837p.facility.json");
        ajv.addSchema(claimInfoOtherSchema, "837p.claim_info_other.json");
        ajv.addSchema(
            formIdentificationSchema,
            "837p.form_identification.json"
        );
        ajv.addSchema(licensesSchema, "837p.licenses.json");
        ajv.addSchema(
            lineAdjudicationSchema,
            "837p.service_line_adjudication.json"
        );
        ajv.addSchema(
            otherBillingProviderSchema,
            "837p.obilling_provider.json"
        );
        ajv.addSchema(otherFacilitySchema, "837p.ofacility.json");
        ajv.addSchema(otherPayerSchema, "837p.opayer.json");
        ajv.addSchema(otherReferringProviderSchema, "837p.oreferring.json");
        ajv.addSchema(otherRenderingProviderSchema, "837p.orendering.json");
        ajv.addSchema(otherSubscriberSchema, "837p.osubscriber.json");
        ajv.addSchema(otherSubscriberNameSchema, "837p.osubscriber_name.json");
        ajv.addSchema(otherSupervisingProviderSchema, "837p.osupervisor.json");
        ajv.addSchema(repricingSchema, "837p.repricing.json");
        ajv.addSchema(serviceLineDateSchema, "837p.service_line_date.json");
        ajv.addSchema(
            serviceLineReferenceSchema,
            "837p.service_line_reference.json"
        );
        ajv.addSchema(
            serviceLineSupplementalSchema,
            "837p.service_line_supplemental.json"
        );
        ajv.addSchema(serviceLineSchema, "837p.service_line.json");
        ajv.addSchema(serviceSchema, "837p.service.json");
        ajv.addSchema(supplementalSchema, "837p.supplemental.json");
        ajv.addSchema(
            supportingDocSchema,
            "837p.supporting_documentation.json"
        );

        const ncpdpSchema = require("./837p.claim.json");
        return ajv.compile(ncpdpSchema);
    }

    async addCustomKeywords(ajv) {
        ajv.addKeyword({
            keyword: Custom837pKeywords.TRANS_PERC,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema, parentSchema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    if (schema !== true) {
                        return true; // Skip if schema is not true
                    }
                    const currentValue = parentData[parentDataProperty];
                    if (typeof currentValue === "number") {
                        parentData[parentDataProperty] = currentValue / 100;
                    }
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });

        ajv.addKeyword({
            keyword: Custom837pKeywords.TRANS_MOD,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema, parentSchema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    if (schema !== true) {
                        return true; // Skip if schema is not true
                    }
                    const currentValue = parentData[parentDataProperty];
                    const modifiers = [
                        "modifier_1",
                        "modifier_2",
                        "modifier_3",
                        "modifier_4",
                    ];
                    const validModifiers = modifiers
                        .map((mod) => currentValue[mod])
                        .filter(
                            (val) =>
                                val !== undefined &&
                                typeof val === "string" &&
                                val.length === 2 &&
                                /^[a-zA-Z0-9]{2}$/.test(val)
                        );
                    // Add the new property 'procedure_modifiers' with the valid modifiers
                    parentData["procedure_modifiers"] = validModifiers;

                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });

        ajv.addKeyword({
            keyword: Custom837pKeywords.TRANS_DATE,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema, parentSchema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    if (schema === true) {
                        // Check if the date is in MM/DD/YYYY format
                        const regex =
                            /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/(\d{4})$/;
                        const currentValue = parentData[parentDataProperty];
                        if (regex.test(currentValue)) {
                            // Transform MM/DD/YYYY to YYYYMMDD
                            const [month, day, year] = kwVal.split("/");
                            parentData[parentDataProperty] =
                                `${year}${month}${day}`;
                        }
                    }
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });
    }

    async addCustomFormats(ajv) {
        return;
    }
};
