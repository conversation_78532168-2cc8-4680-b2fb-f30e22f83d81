{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.dependent.json", "title": "837 Professional <PERSON><PERSON><PERSON> De<PERSON>dent", "type": "object", "$comment": "med_claim_dep", "additionalProperties": false, "properties": {"isInlineSubform": true, "first_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "NM104"}, "last_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "middle_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-25", "$comment": "NM105"}, "suffix": {"type": "string", "$ref": "837p.fields.json#/$defs/1-10", "$comment": "NM107"}, "date_of_birth": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DMG02"}, "gender": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/DMG03"}], "$comment": "DMG03", "errorMessage": {"enum": "Invalid Gender"}}, "ssn": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/ssn"}], "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=1W"}, "member_id": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "NM109"}, "relationship_to_subscriber_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/PAT01"}], "$comment": "PAT01", "errorMessage": {"enum": "Invalid Relationship Code"}}, "contact_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.contact.json"}}, "address": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.address.json"}}}, "required": ["first_name", "gender", "date_of_birth", "relationship_to_subscriber_code"], "errorMessage": {"required": "The following fields are required: First Name (NM104), Date Of Birth (DMG02), Gender (DMG03), and Relationship To Subscriber Code (PAT01)"}}