{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.repricing.json", "title": "837 Professional Claim Repricing", "type": "object", "$comment": "med_claim_reprice", "additionalProperties": false, "properties": {"isInlineSubform": true, "pricing_methodology_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/HCP01"}], "$comment": "HCP01", "errorMessage": {"enum": "Invalid Pricing Methodology Code"}}, "repriced_allowed_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "HCP02"}, "repriced_saving_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "HCP03"}, "repricing_organization_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "HCP04"}, "repricing_per_diem_or_flat_rate_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-9"}, {"$ref": "837p.fields.json#/$defs/currency9"}], "$comment": "HCP05"}, "repriced_approved_ambulatory_patient_group_code": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "HCP06"}, "repriced_approved_ambulatory_patient_group_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "HCP07"}, "reject_reason_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/HCP13"}], "$comment": "HCP13", "errorMessage": {"enum": "Invalid Reject Reason Code"}}, "policy_compliance_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/HCP14"}], "$comment": "HCP14", "errorMessage": {"enum": "Invalid Policy Compliance Code"}}, "exception_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/HCP15"}], "$comment": "HCP15", "errorMessage": {"enum": "Invalid Policy Compliance Code"}}}, "required": ["pricing_methodology_code", "repriced_allowed_amount"], "errorMessage": {"required": "The following fields are required: Price Methodology Code (HCP01) and Repriced Allowed Amount (HCP02)"}}