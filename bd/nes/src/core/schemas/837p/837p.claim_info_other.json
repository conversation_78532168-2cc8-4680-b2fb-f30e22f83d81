{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.claim_info_other.json", "title": "837 Professional Claim - Claim Information Other", "type": "object", "$comment": "med_claim_info_other", "additionalProperties": false, "properties": {"isInlineSubform": true, "file_information_list": {"type": "array", "maxItems": 10, "items": {"type": "object", "additionalProperties": false, "properties": {"file": {"type": "object", "additionalProperties": false, "properties": {"filehash": {"type": "string"}, "filename": {"type": "string"}}}}}, "$comment": "K301"}, "claim_date_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.dates.json"}}, "claim_contract_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.contract.json"}}, "condition_information": {"type": "array", "maxItems": 2, "items": {"additionalProperties": false, "properties": {"condition_codes": {"type": "array", "maxItems": 12, "items": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-3"}, {"$ref": "837p.code_sets.json#/$defs/nubc_condition"}], "$comment": "HI(01-12)-02"}}}, "required": ["condition_codes"], "errorMessage": {"required": "The following fields are required: NUBC Condition Code"}}}}}