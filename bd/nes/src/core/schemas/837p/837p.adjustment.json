{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.adjustment.json", "title": "837 Professional Claim Adjustment", "type": "object", "$comment": "med_claim_adj", "additionalProperties": false, "properties": {"adjustment_group_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/CAS01"}], "$comment": "CAS01", "errorMessage": {"enum": "Invalid Adjustment Group Code"}}, "adjustment_details": {"type": "array", "maxItems": 6, "minItems": 1, "items": {"type": "object", "additionalProperties": false, "properties": {"adjustment_reason_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-5"}, {"$ref": "837p.ecl.json#/$defs/CARC"}], "$comment": "CAS02/CAS05/CAS08/CAS11/CAS14/CAS17", "errorMessage": {"enum": "Invalid Adjustment Reason Code"}}, "adjustment_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "CAS03/CAS06/CAS09/CAS12/CAS15/CAS18"}, "adjustment_quantity": {"type": "string", "items": {"$ref": "837p.fields.json#/$defs/quantity13-2"}, "$comment": "CAS04/CAS07/CAS10/CAS13/CAS16/CAS19"}}, "required": ["adjustment_reason_code", "adjustment_amount"], "errorMessage": {"required": "The following fields are required: Adjustment Reason Code and Adjustment Amount"}}}}, "required": ["adjustment_group_code", "adjustment_details"], "errorMessage": {"required": "The following fields are required: Adjustment Group Code (CAS01) and Adjustment Details"}}