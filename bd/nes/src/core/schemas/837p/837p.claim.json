{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.claim.json", "title": "837 Professional Claim", "description": "837 Professional Claim Form Schema", "type": "object", "$comment": "med_claim", "additionalProperties": false, "properties": {"control_number": {"type": "string", "$comment": "ST02"}, "trading_partner_service_id": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "NM109"}, "trading_partner_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "usage_indicator": {"type": "string", "default": "P", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/ISA15"}], "$comment": "ISA15", "errorMessage": {"enum": "Invalid Usage Indicator Code"}}, "submitter": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.submitter.json"}, "$comment": "1000A"}, "receiver": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.receiver.json"}, "$comment": "1000B"}, "subscriber": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.subscriber.json"}, "$comment": "2010BA"}, "dependent": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.dependent.json"}, "$comment": "2010CA"}, "claim_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.claim_info.json"}, "$comment": "2300"}, "pay_to_address": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.address.json"}, "$comment": "2010AB"}, "providers": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"properties": {"isInlineSubform": true, "ordering": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}, "$comment": "2000A"}, "billing": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}, "$comment": "2000A"}, "referring": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}, "$comment": "2420F"}, "rendering": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}, "$comment": "2420A"}, "supervising": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}, "$comment": "2420D"}}}, "required": ["billing"], "errorMessage": {"required": "Billing provider is required."}}}, "required": ["claim_information", "receiver", "submitter", "subscriber", "trading_partner_name"], "errorMessage": {"required": "The following fields are required: Trading Partner Name (NM103), Control Number (ST02), Claim, Receiver, Submitter, and Subscriber"}}