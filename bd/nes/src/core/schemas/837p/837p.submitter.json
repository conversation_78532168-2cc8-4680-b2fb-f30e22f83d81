{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.submitter.json", "title": "837 Professional Claim Submitter", "type": "object", "$comment": "med_claim_submitter", "additionalProperties": false, "properties": {"isInlineSubform": true, "organization_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103 NM102 = 02"}, "first_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "NM104 NM102 = 01"}, "last_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "middle_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-25", "$comment": "NM105"}, "contact_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.contact.json"}}}, "required": ["contact_information"], "errorMessage": {"required": "The following fields are required: Organization Name and Contact Information"}}