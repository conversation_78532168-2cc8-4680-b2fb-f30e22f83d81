{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.drug.json", "title": "837 Professional Claim Drug Identification", "type": "object", "$comment": "med_claim_drg", "additionalProperties": false, "properties": {"isInlineSubform": true, "service_id_qualifier": {"default": "N4", "type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/LIN02"}], "$comment": "LIN02"}, "national_drug_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-48"}, {"$ref": "837p.code_sets.json#/$defs/ndc"}], "$comment": "LIN03"}, "national_drug_unit_count": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-15"}, {"$ref": "837p.fields.json#/$defs/quantity13-2"}], "$comment": "CTP04"}, "measurement_unit_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/CTP05-01"}], "$comment": "CTP05-01"}, "link_sequence_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF01 REF01=VY"}, "pharmacy_prescription_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF01 REF01=XZ"}}, "required": ["service_id_qualifier", "national_drug_code", "national_drug_unit_count", "measurement_unit_code"], "errorMessage": {"required": "The following fields are required: Service ID Qualifier (LIN02), NDC (LIN03), NDC Unit Count (CTP04), and Measurement Unit Code (CTP05-01)"}}