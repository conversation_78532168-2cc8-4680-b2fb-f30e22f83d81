{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.fields.json", "title": "837 Professional Claim Field Formats", "description": "An Professional Claim Field Formats Schema", "type": "object", "$defs": {"1-1": {"maxLength": 1, "minLength": 1, "pattern": "^.{1,1}$", "errorMessage": {"pattern": "Field must be 1 alphanumeric character."}}, "1-2": {"maxLength": 2, "minLength": 1, "pattern": "^.{1,2}$", "errorMessage": {"pattern": "Field must be between 1-2 alphanumeric characters."}}, "1-3": {"maxLength": 3, "minLength": 1, "pattern": "^.{1,3}$", "errorMessage": {"pattern": "Field must be between 1-3 alphanumeric characters."}}, "1-5": {"maxLength": 5, "minLength": 1, "pattern": "^.{1,5}$", "errorMessage": {"pattern": "Field must be between 1-5 alphanumeric characters."}}, "1-6": {"maxLength": 6, "minLength": 1, "pattern": "^.{1,6}$", "errorMessage": {"pattern": "Field must be between 1-6 alphanumeric characters."}}, "1-8": {"maxLength": 8, "minLength": 1, "pattern": "^.{1,8}$", "errorMessage": {"pattern": "Field must be between 1-8 alphanumeric characters."}}, "1-9": {"maxLength": 9, "minLength": 1, "pattern": "^.{1,9}$", "errorMessage": {"pattern": "Field must be between 1-9 alphanumeric characters."}}, "1-10": {"maxLength": 10, "minLength": 1, "pattern": "^.{1,10}$", "errorMessage": {"pattern": "Field must be between 1-10 alphanumeric characters."}}, "1-15": {"maxLength": 15, "minLength": 1, "pattern": "^.{1,15}$", "errorMessage": {"pattern": "Field must be between 1-15 alphanumeric characters."}}, "1-18": {"maxLength": 18, "minLength": 1, "pattern": "^.{1,18}$", "errorMessage": {"pattern": "Field must be between 1-18 alphanumeric characters."}}, "1-20": {"maxLength": 20, "minLength": 1, "pattern": "^.{1,20}$", "errorMessage": {"pattern": "Field must be between 1-20 alphanumeric characters."}}, "1-25": {"maxLength": 25, "minLength": 1, "pattern": "^.{1,25}$", "errorMessage": {"pattern": "Field must be between 1-25 alphanumeric characters."}}, "1-30": {"maxLength": 30, "minLength": 1, "pattern": "^.{1,30}$", "errorMessage": {"pattern": "Field must be between 1-30 alphanumeric characters."}}, "1-35": {"maxLength": 35, "minLength": 1, "pattern": "^.{1,35}$", "errorMessage": {"pattern": "Field must be between 1-35 alphanumeric characters."}}, "1-38": {"maxLength": 38, "minLength": 1, "pattern": "^.{1,38}$", "errorMessage": {"pattern": "Field must be between 1-38 alphanumeric characters."}}, "1-48": {"maxLength": 48, "minLength": 1, "pattern": "^.{1,48}$", "errorMessage": {"pattern": "Field must be between 1-48 alphanumeric characters."}}, "1-50": {"maxLength": 50, "minLength": 1, "pattern": "^.{1,50}$", "errorMessage": {"pattern": "Field must be between 1-50 alphanumeric characters."}}, "1-55": {"maxLength": 55, "minLength": 1, "pattern": "^.{1,55}$", "errorMessage": {"pattern": "Field must be between 1-55 alphanumeric characters."}}, "1-60": {"maxLength": 60, "minLength": 1, "pattern": "^.{1,60}$", "errorMessage": {"pattern": "Field must be between 1-60 alphanumeric characters."}}, "1-80": {"maxLength": 80, "minLength": 1, "pattern": "^.{1,80}$", "errorMessage": {"pattern": "Field must be between 1-80 alphanumeric characters."}}, "1-256": {"maxLength": 256, "minLength": 1, "pattern": "^.{1,256}$", "errorMessage": {"pattern": "Field must be between 1-256 alphanumeric characters."}}, "2-2": {"maxLength": 2, "minLength": 2, "pattern": "^.{2,2}$", "errorMessage": {"pattern": "Field must be between 2-2 alphanumeric characters."}}, "2-3": {"maxLength": 3, "minLength": 2, "pattern": "^.{2,3}$", "errorMessage": {"pattern": "Field must be between 2-3 alphanumeric characters."}}, "2-30": {"maxLength": 30, "minLength": 2, "pattern": "^.{2,30}$", "errorMessage": {"pattern": "Field must be between 2-30 alphanumeric characters."}}, "2-50": {"maxLength": 50, "minLength": 2, "pattern": "^.{2,50}$", "errorMessage": {"pattern": "Field must be between 2-50 alphanumeric characters."}}, "2-80": {"maxLength": 80, "minLength": 2, "pattern": "^.{2,80}$", "errorMessage": {"pattern": "Field must be between 2-80 alphanumeric characters."}}, "3-15": {"maxLength": 15, "minLength": 3, "pattern": "^.{3,15}$", "errorMessage": {"pattern": "Field must be between 3-15 alphanumeric characters."}}, "8-8": {"maxLength": 8, "minLength": 8, "pattern": "^.{8}$", "errorMessage": {"pattern": "Field must be 8 alphanumeric characters."}}, "9-9": {"maxLength": 9, "minLength": 9, "pattern": "^.{9}$", "errorMessage": {"pattern": "Field must be 9 alphanumeric characters."}}, "loop": {"maxLength": 6, "minLength": 4, "pattern": "^.{4,6}$", "errorMessage": {"pattern": "Loop name must be 4-6 alphanumeric characters."}}, "phone10": {"allOf": [{"maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Phone number must be a 10-digit phone number."}}]}, "ext8": {"allOf": [{"maxLength": 8, "minLength": 1, "pattern": "^\\d{1,8}$", "errorMessage": {"pattern": "Phone extension must be a 1-8 digit number."}}]}, "email": {"maxLength": 256, "pattern": "^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$", "errorMessage": {"pattern": "Field must be a valid email address."}}, "date": {"maxLength": 8, "pattern": "^\\d{4}\\d{2}\\d{2}$", "errorMessage": {"pattern": "Field must be in the format YYYYMMDD."}}, "postal_code": {"maxLength": 35, "minLength": 3, "pattern": "^\\d{5}(-\\d{4})?$", "errorMessage": {"pattern": "Field must be a valid Zip Code"}}, "currency": {"pattern": "^-?\\d{1,10}(\\.\\d{1,2})?$", "errorMessage": {"pattern": "Field must be a currency amount"}}, "currency9": {"pattern": "^-?\\d{1,7}(\\.\\d{1,2})?$", "errorMessage": {"pattern": "Field must be a currency amount"}}, "weight": {"pattern": "^-?\\d{1,8}(\\.\\d{1,2})?$", "errorMessage": {"pattern": "Field must be a valid weight"}}, "quantity13-2": {"pattern": "^\\d{1,13}(\\.\\d{1,2})?$", "errorMessage": {"pattern": "Field must be a quantity with 1-13 digits followed by an optional 1-2 decimal places"}}, "quantity5-3": {"pattern": "^\\d{1,5}(\\.\\d{1,3})?$", "errorMessage": {"pattern": "Field must be a quantity with 1-5 digits followed by an optional 1-3 decimal places"}}, "percent8-2": {"pattern": "^\\d{1,8}(\\.\\d{1,2})?$", "transformX12Percent": true, "errorMessage": {"pattern": "Field must be a percentage with 1-8 digits followed by an optional 1-2 decimal places"}}, "percent4-2": {"pattern": "^\\d{1,4}(\\.\\d{1,2})?$", "transformX12Percent": true, "errorMessage": {"pattern": "Field must be a percentage with 1-4 digits followed by an optional 1-2 decimal places"}}, "pcn": {"maxLength": 38, "minLength": 1, "pattern": "^[a-zA-Z0-9]{1,38}$", "errorMessage": {"pattern": "Patient Claim Number (PCN) number must be a 1-38 alphanumeric characters long."}}, "number15": {"maxLength": 15, "minLength": 1, "pattern": "^\\d{1,15}$", "errorMessage": {"pattern": "Field must be between 1-15 digits."}}, "number6": {"maxLength": 6, "minLength": 1, "pattern": "^\\d{1,6}$", "errorMessage": {"pattern": "Field must be between 1-6 digits."}}, "number3": {"pattern": "^\\d{1,3}$", "errorMessage": {"pattern": "Field must be between 1-3 digits."}}}}