{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.osubscriber.json", "title": "837 Professional Claim Other Payer Subscriber", "type": "object", "$comment": "med_claim_osub", "additionalProperties": false, "properties": {"isInlineSubform": true, "payment_responsibility_level_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SBR01"}], "$comment": "SBR01", "errorMessage": {"enum": "Invalid Patient Responsibility Code"}}, "individual_relationship_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SBR02"}], "$comment": "SBR02", "errorMessage": {"enum": "Invalid Relationship Code"}}, "insurance_group_or_policy_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "SBR03"}, "other_insured_group_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "SBR04"}, "insurance_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SBR05"}], "$comment": "SBR05", "errorMessage": {"enum": "Invalid Insurance Type Code"}}, "claim_filing_indicator_code": {"type": "string", "$ref": "837p.ecl.json#/$defs/SBR09", "$comment": "SBR09", "errorMessage": {"enum": "Invalid Filling Indicator Code"}}, "claim_level_adjustments": {"type": "array", "maxItems": 5, "items": {"type": "object", "$ref": "837p.adjustment.json"}}, "payer_paid_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "AMT02 AMT01=D"}, "non_covered_charge_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "AMT02 AMT01=A8"}, "remaining_patient_liability": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "AMT02 AMT01=EAF"}, "benefits_assignment_certification_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/O103"}], "$comment": "O103", "errorMessage": {"enum": "Invalid Benefits Assignment Indicator"}}, "patient_signature_generate_for_patient": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/O104"}], "$comment": "O104", "errorMessage": {"enum": "Invalid Patient Signature Generated Flag"}}, "release_of_information_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/O106"}], "$comment": "O106", "errorMessage": {"enum": "Invalid Release of Information Code"}}, "other_subscriber_name": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.osubscriber_name.json"}}, "other_payer_name": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.opayer.json"}}, "other_payer_referring_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.oreferring.json"}}, "other_payer_rendering_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.orendering.json"}}, "other_payer_service_facility_location": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.ofacility.json"}}, "other_payer_supervising_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.osupervisor.json"}}, "other_payer_billing_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.obilling_provider.json"}}}, "required": ["benefits_assignment_certification_indicator", "claim_filing_indicator_code", "individual_relationship_code", "other_payer_name", "other_subscriber_name", "payment_responsibility_level_code", "release_of_information_code"], "errorMessage": {"required": "The following fields are required: Benefits Assignment Certification Indicator, Claim Filling Indicator Code, Relationship Code, Payer Name, Subscriber Name, Payment Responsibility Level Code, and Release of Information Code"}}