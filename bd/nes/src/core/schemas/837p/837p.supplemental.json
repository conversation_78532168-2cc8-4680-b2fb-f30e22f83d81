{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.supplemental.json", "title": "837 Professional Claim Supplemental Information", "type": "object", "$comment": "med_claim_supplemental", "additionalProperties": false, "properties": {"isInlineSubform": true, "report_information": {"type": "object", "additionalProperties": false, "properties": {"attachment_report_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/PWK01"}], "$comment": "PWK01", "errorMessage": {"enum": "Invalid Attachment Report Code"}}, "attachment_transmission_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/PWK02"}], "$comment": "PWK02", "errorMessage": {"enum": "Invalid Attachment Transmission Code"}}, "attachment_control_number": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "PWK05"}}, "required": ["attachment_report_type_code", "attachment_transmission_code"], "errorMessage": {"required": "The following fields are required: Attachment Report Type Code (PWK01) and Attachment Transmission Code (PWK02)"}}, "prior_authorization_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=G1"}, "referral_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=9F"}, "claim_control_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=F8"}, "clia_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=X4"}, "repriced_claim_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=9A"}, "adjusted_repriced_claim_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=9C"}, "investigation_device_exemption_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=LX"}, "claim_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=D9"}, "medical_record_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=EA"}, "demo_project_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=P4"}, "care_plan_oversight_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=1J"}, "medicare_crossover_reference_id": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=F5"}, "service_authorization_exception_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/REF02-4N"}], "$comment": "REF02 REF01=4N"}}, "required": ["claim_number"], "errorMessage": {"required": "The following field is required: Claim Number (REF02 REF01=D9)"}}