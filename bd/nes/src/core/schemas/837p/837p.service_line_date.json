{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.service_line_date.json", "title": "837 Professional Claim Service Line Date Information", "type": "object", "$comment": "med_claim_sl_dt", "additionalProperties": false, "properties": {"isInlineSubform": true, "prescription_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=471"}, "certification_revision_or_recertification_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=607"}, "begin_therapy_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=463"}, "last_certification_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=461"}, "treatment_or_therapy_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=304"}, "hemoglobin_test_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=738"}, "serum_creatine_test_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=739"}, "shipped_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=011"}, "last_x_ray_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=455"}, "initial_treatment_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DPT01=454"}}}