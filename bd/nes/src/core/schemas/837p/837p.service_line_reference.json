{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.service_line_reference.json", "title": "837 Professional Claim Service Line Reference Information", "type": "object", "$comment": "med_claim_sl_ref", "additionalProperties": false, "properties": {"isInlineSubform": true, "repriced_line_item_reference_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=9B"}, "adjusted_repriced_line_item_reference_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=9D"}, "prior_authorization": {"type": "array", "maxItems": 5, "items": {"type": "object", "additionalProperties": false, "properties": {"prior_authorization_or_referral_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=G1"}}, "required": ["prior_authorization_or_referral_number"], "errorMessage": {"required": "The following fields are required: Prior Authorization Number"}}, "$comment": "REF02 REF01=G1"}, "immunization_batch_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=BT"}, "referral_number": {"type": "array", "maxItems": 5, "items": {"type": "string", "additionalProperties": false, "$ref": "837p.fields.json#/$defs/1-50"}, "$comment": "REF02 REF01=9F"}}}