{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.code_sets.json", "title": "837 Professional Claim Diagnosis Code Sets", "description": "An 837 Professional Claim Diagnosis Code Type Schema", "$defs": {"icd9": {"pattern": "^\\d{3,5}(\\.\\d{1,2})?$", "description": "International Classification of Diseases (ICD-9) code, 3 digits followed by 1-2 digits", "errorMessage": {"pattern": "Field must be a valid International Classification of Diseases (ICD-9) code, 3 digits followed by 1-2 digits"}}, "icd10": {"pattern": "^[A-Z]\\d{1,4}(\\.\\d{1,4})?$", "description": "International Classification of Diseases-10-Clinical Modifications(ICD-10-CM) code, one capital letter, followed by 1-4 digits", "errorMessage": {"pattern": "Field must be a valid International Classification of Diseases-10-Clinical Modifications(ICD-10-CM) code, one capital letter, followed by 1-4 digits"}}, "ncci": {"pattern": "^[A-Z]\\d{2}(\\.\\d{1,4})?$", "description": "National Criteria Care Institute (NCCI), one capital letter, followed by 2 digits, and 1-4 digits after the period", "errorMessage": {"pattern": "Field must be a valid National Criteria Care Institute (NCCI) code, one capital letter, followed by 2 digits, and 1-4 digits after the period"}}, "snomed": {"pattern": "^\\d{1,18}$", "description": "The Systematized Nomenclature of Medicine Clinical Terms (SNOMED), must be 1-18 digits", "errorMessage": {"pattern": "Field must be a valid The Systematized Nomenclature of Medicine Clinical Terms (SNOMED) code, must be 1-18 digits"}}, "cdt": {"pattern": "^D\\d{4}$", "description": "Current Dental Terminology (CDT), start with a capital D and followed by 4 digits", "errorMessage": {"pattern": "Field must be a valid Current Dental Terminology (CDT) code, start with a capital D and followed by 4 digits"}}, "dsm_iv": {"pattern": "^\\d{1,4}(\\.\\d{1,2})?$", "description": "American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-IV), 1-4 digits then 1-2 digits following a period", "errorMessage": {"pattern": "Field must be a valid American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-IV) code, 1-4 digits then 1-2 digits following a period"}}, "dsm_5": {"pattern": "^[A-Z]\\d{2}(\\.\\d{1,4})?$", "description": "American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-5), a capital letter, followed by 2 digits and then 1-4 digits after the period", "errorMessage": {"pattern": "Field must be a valid American Psychiatric Association Diagnostic Statistical Manual of Mental Disorders (DSM-5) code, a capital letter, followed by 2 digits and then 1-4 digits after the period"}}, "loinc": {"pattern": "^\\d{1,5}-\\d{1,2}$", "description": "Logical Observation Identifier Names and Codes (LOINC), 1-5 digits, a hyphen, and then 1-2 digits", "errorMessage": {"pattern": "Field must be a valid Logical Observation Identifier Names and Codes (LOINC), 1-5 digits, a hyphen, and then 1-2 digits"}}, "nubc_condition": {"pattern": "^[A-Za-z0-9]{2,3}$", "description": "NUBC Condition Codes", "errorMessage": {"pattern": "Field must be a valid NUBC Condition Code (2-3 alphanumeric characters)"}}, "hcpc": {"pattern": "^([A-V][0-9]{4}|[0-9]{5})$", "description": "HCPC Level I and II Codes", "errorMessage": {"pattern": "Field must be a valid HCPC Code"}}, "ndc": {"pattern": "^\\d{5}-\\d{4}-\\d{2}$", "description": "National Drug Code (NDC) in 5-4-2 format", "errorMessage": {"pattern": "Field must be a valid NDC Code in (5-4-2 format)"}}, "taxonomy_code": {"pattern": "^[A-Za-z0-9]{10}$", "description": "Taxonomy Code", "errorMessage": {"pattern": "Field must be a valid Taxonomy Code"}}}}