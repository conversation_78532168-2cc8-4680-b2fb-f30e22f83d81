{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.orendering.json", "title": "837 Professional Claim Other Rendering Provider", "type": "object", "$comment": "med_claim_oprov_rend", "additionalProperties": false, "properties": {"isInlineSubform": true, "entity_type_qualifier": {"type": "string", "default": "1", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/NM102"}], "$comment": "NM102"}, "other_payer_rendering_provider_secondary_identifier": {"type": "array", "maxItems": 3, "minItems": 1, "items": {"type": "object", "additionalProperties": false, "properties": {"qualifier": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-3"}, {"$ref": "837p.ecl.json#/$defs/2330E-REF01"}], "$comment": "REF01"}, "identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02"}}, "required": ["qualifier", "identifier"], "errorMessage": {"required": "The following fields are required: Identifier Qualifier (REF01) and Identifier (REF02)"}}, "$comment": "2330D"}}, "required": ["entity_type_qualifier", "other_payer_rendering_provider_secondary_identifier"], "errorMessage": {"required": "The following fields are required: Entity Type Qualifier and Other Rendering Provider Identifier"}}