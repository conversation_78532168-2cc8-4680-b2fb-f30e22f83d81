{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.osubscriber_name.json", "title": "837 Professional Claim Other Subscriber Name", "type": "object", "$comment": "med_claim_osub_nm", "additionalProperties": false, "properties": {"isInlineSubform": true, "other_insured_qualifier": {"type": "string", "default": "1", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/NM102"}], "$comment": "NM102", "errorMessage": {"enum": "Invalid Other Insured Qualifier"}}, "other_insured_first_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "NM104"}, "other_insured_last_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "other_insured_middle_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-25", "$comment": "NM105"}, "other_insured_name_suffix": {"type": "string", "$ref": "837p.fields.json#/$defs/1-10", "$comment": "NM107"}, "other_insured_identifier_type_code": {"type": "string", "default": "MI", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/NM108-2320"}], "$comment": "NM108", "errorMessage": {"enum": "Invalid Other Insured Identifier Type Code"}}, "other_insured_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "NM109"}, "other_insured_address": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.address.json"}}, "other_insured_additional_identifier": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/ssn"}], "$comment": "REF02 REF01=SY (SSN)"}}, "required": ["other_insured_qualifier", "other_insured_identifier_type_code", "other_insured_identifier", "other_insured_last_name"], "errorMessage": {"required": "The following fields are required: Other Insured Qualifier, Identifier Type Code, Identifier, and Last Name"}}