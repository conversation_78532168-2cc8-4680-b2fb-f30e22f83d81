{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.form_identification.json", "title": "837 Professional Claim Form Identification", "type": "object", "$comment": "med_claim_fm_id", "additionalProperties": false, "properties": {"form_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/LQ01"}], "$comment": "LQ01", "errorMessage": {"enum": "Invalid Form Type Code"}}, "form_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-30", "$comment": "LQ02"}, "supporting_documentation": {"type": "array", "maxItems": 99, "minItems": 0, "items": {"type": "object", "$ref": "837p.supporting_documentation.json"}}}, "required": ["form_type_code", "form_identifier"], "errorMessage": {"required": "The following fields are required: Form Type Code (LQ01) and Form Identifier (LQ02)"}}