{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.opayer.json", "title": "837 Professional Claim Other Payer", "type": "object", "$comment": "med_claim_opayer", "additionalProperties": false, "properties": {"other_payer_organization_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "other_payer_identifier_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/NM108-2330B"}], "$comment": "NM108", "errorMessage": {"enum": "Invalid Payer Identification Type Code"}}, "other_payer_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "N109"}, "other_payer_address": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.address.json"}}, "other_payer_adjudication_or_payment_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-35"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DTP01=573 (Date Claim Paid)"}, "other_payer_secondary_identifier": {"type": "array", "maxItems": 2, "items": {"type": "object", "additionalProperties": false, "properties": {"qualifier": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-3"}, {"$ref": "837p.ecl.json#/$defs/2330B-REF01"}], "$comment": "REF01"}, "identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02"}}, "required": ["qualifier", "identifier"], "errorMessage": {"required": "The following fields are required: Identifier Qualifier (REF01) and Identifier (REF02)"}}}, "other_payer_prior_authorization_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 where REF01=G1"}, "other_payer_claim_adjustment_indicator": {"type": "string", "$ref": "837p.fields.json#/$defs/1-1", "$comment": "REF02 REF01=T4"}, "other_payer_claim_control_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=F8"}}, "required": ["other_payer_identifier", "other_payer_identifier_type_code", "other_payer_organization_name"], "errorMessage": {"required": "The following fields are required: Other Payer Identifier Type Code, Identifier, and Organization Name"}}