{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.subscriber.json", "title": "837 Professional Claim Subscriber", "type": "object", "$comment": "med_claim_subscriber", "additionalProperties": false, "properties": {"isInlineSubform": true, "member_id": {"type": "string", "$ref": "837p.fields.json#/$defs/2-80", "$comment": "NM109"}, "ssn": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-50"}, {"$ref": "837p.licenses.json#/$defs/ssn"}], "$comment": "REF02 REF01=SY"}, "payment_responsibility_level_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SBR01"}], "$comment": "SBR01", "errorMessage": {"enum": "Invalid Patient Responsibility Code"}}, "organization_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "insurance_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SBR05"}], "$comment": "SBR05", "errorMessage": {"enum": "Invalid Insurance Type Code"}}, "policy_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "SBR03"}, "subscriber_group_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "SBR04"}, "group_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "SBR04"}, "first_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-35", "$comment": "NM104"}, "last_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "middle_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-25", "$comment": "NM105"}, "suffix": {"type": "string", "$ref": "837p.fields.json#/$defs/1-10", "$comment": "NM107"}, "date_of_birth": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DMG02"}, "gender": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/DMG03"}], "$comment": "DMG03", "errorMessage": {"enum": "Invalid Gender"}}, "contact_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.contact.json"}}, "address": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.address.json"}}}, "required": ["payment_responsibility_level_code", "member_id"], "errorMessage": {"required": "The following fields are required: Payment Responsibility Level Code (SBR01) and Member ID (NM109)"}}