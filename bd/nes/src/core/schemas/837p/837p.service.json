{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.service.json", "title": "837 Professional Claim Service", "type": "object", "$comment": "med_claim_sv", "additionalProperties": false, "properties": {"isInlineSubform": true, "procedure_identifier": {"type": "string", "default": "HC", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SV101"}], "$comment": "SV101"}, "procedure_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-48"}, {"$ref": "837p.code_sets.json#/$defs/hcpc"}], "$comment": "SV101-01"}, "modifier_1": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$comment": "SV101-03"}, "modifier_2": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$comment": "SV101-04"}, "modifier_3": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$comment": "SV101-05"}, "modifier_4": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}], "$comment": "SV101-06"}, "description": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "SV101-07"}, "line_item_charge_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "SV102"}, "measurement_unit": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SV103"}], "$comment": "SV103"}, "service_unit_count": {"type": "string", "$ref": "837p.fields.json#/$defs/quantity5-3", "$comment": "SV104"}, "place_of_service_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/SV105"}], "$comment": "SV105"}, "emergency_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SV109"}], "$comment": "SV109"}, "epsdt_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SV111"}], "$comment": "SV111"}, "family_planning_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SV112"}], "$comment": "SV112"}, "copay_status_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/SV115"}], "$comment": "SV115"}}, "required": ["line_item_charge_amount", "measurement_unit", "procedure_identifier", "procedure_code", "service_unit_count"], "errorMessage": {"required": "The following fields are required: Procedure Identifier (SV101), Procedure Code (SV101-01), Charge Amount (SV102), Measurement Unit (SV103), and Diagnosis Code Pointer (SV107)"}}