{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.licenses.json", "title": "837 Professional License Formats", "description": "An 837 Professional License Formats Schema", "$defs": {"npi": {"pattern": "^\\d{10}$", "description": "NPI, must be 10 digits", "errorMessage": {"pattern": "Field must be a valid NPI, 10 digits."}}, "ssn": {"pattern": "^\\d{9}$", "description": "Social Security Number, must be 9 digits", "errorMessage": {"pattern": "Field must be a valid Social Security Number, 9 digits."}}, "naic": {"pattern": "^\\d{5}$", "description": "National Association of Insurance Commissioners (NAIC) ID, must be 5 digits", "errorMessage": {"pattern": "Field must be a valid National Association of Insurance Commissioners (NAIC) ID, 5 digits."}}, "state_license": {"pattern": "^[A-Za-z0-9\\-]{5,20}$", "description": "State License Number, must be 5-20 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid State License Number, 5-20 alphanumeric characters."}}, "upin": {"pattern": "^[A-Za-z][0-9]{5}$", "description": "UPIN (Unique Physician/Practitioner Identification Number), must be 6 alphanumeric characters", "errorMessage": {"pattern": "Field must be a valid UPIN (Unique Physician/Practitioner Identification Number), 6 alphanumeric characters."}}}}