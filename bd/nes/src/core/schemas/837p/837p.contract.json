{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.contract.json", "title": "837 Professional Claim Contract", "type": "object", "$comment": "med_claim_contract", "additionalProperties": false, "properties": {"isInlineSubform": true, "contract_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/CN101"}], "$comment": "CN101"}, "contract_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "CN102"}, "contract_percentage": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-6"}, {"$ref": "837p.fields.json#/$defs/percent4-2"}], "$comment": "CN103"}, "contract_code": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "CN104"}, "terms_discount_percentage": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-6"}, {"$ref": "837p.fields.json#/$defs/percent4-2"}], "$comment": "CN105"}, "contract_version_identifier": {"type": "string", "$ref": "837p.fields.json#/$defs/1-30", "$comment": "CN106"}}, "required": ["contract_type_code"], "errorMessage": {"required": "The following fields are required: Contract Type Code (CN101)"}}