{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.facility.json", "title": "837 Professional Claim Facility Location", "type": "object", "$comment": "med_claim_facility", "additionalProperties": false, "properties": {"isInlineSubform": true, "organization_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}, "npi": {"type": "string", "$ref": "837p.licenses.json#/$defs/npi", "$comment": "NM109"}, "address": {"type": "object", "$ref": "837p.address.json"}, "phone_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "PER02"}, "phone_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/phone10"}, {"$ref": "837p.fields.json#/$defs/1-10"}], "$comment": "PER04"}, "phone_extension": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-8"}, {"$ref": "837p.fields.json#/$defs/ext8"}], "$comment": "PER04"}}, "required": ["organization_name", "address"], "errorMessage": {"required": "The following fields are required: Organization Name (NM103) and Address"}}