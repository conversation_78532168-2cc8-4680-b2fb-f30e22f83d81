{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.service_line.json", "title": "837 Professional Claim Service Line", "type": "object", "$comment": "med_claim_sl", "additionalProperties": false, "properties": {"assigned_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-6"}, {"$ref": "837p.fields.json#/$defs/number6"}], "$comment": "LX01"}, "service_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03"}, "service_date_end": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "DTP03 DTP02=D8"}, "provider_control_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02 REF01=6R"}, "professional_service": {"type": "array", "maxItems": 1, "isInlineSubform": true, "items": {"isInlineSubform": true, "$ref": "837p.service.json"}}, "durable_medical_equipment_service": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.dme.json"}}, "service_line_supplemental_information": {"type": "array", "maxItems": 10, "items": {"type": "object", "$ref": "837p.service_line_supplemental.json"}}, "durable_medical_equipment_certificate_of_medical_necessity": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "properties": {"attachment_transmission_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/PWK02-DME-Code"}], "$comment": "PWK02"}}}}, "durable_medical_equipment_certification": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "properties": {"certification_type_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CR301"}], "$comment": "CR301"}, "durable_medical_equipment_duration_in_months": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-15"}, {"$ref": "837p.fields.json#/$defs/number15"}], "$comment": "CR303 CR302=MO"}}}}, "condition_indicator_durable_medical_equipment": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "properties": {"certification_condition_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CRC02"}], "$comment": "CRC02"}, "condition_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/CRC03"}], "$comment": "CRC03", "errorMessage": {"enum": "Invalid Condition Indicator"}}, "condition_indicator_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/CRC04"}], "$comment": "CRC04", "errorMessage": {"enum": "Invalid Condition Indicator Code"}}}}}, "service_line_date_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.service_line_date.json"}}, "service_line_reference_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.service_line_reference.json"}}, "sales_tax_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "AMT02 AMT01=T"}, "file_information": {"type": "array", "maxItems": 10, "items": {"$ref": "837p.fields.json#/$defs/1-50"}, "$comment": "K301"}, "additional_notes": {"type": "string", "$ref": "837p.fields.json#/$defs/1-80", "$comment": "NTE02 NTE01=ADD"}, "goal_rehab_or_discharge_plans": {"type": "string", "$ref": "837p.fields.json#/$defs/1-80", "$comment": "NTE02 NTE01=DCP"}, "third_party_organization_notes": {"type": "string", "$ref": "837p.fields.json#/$defs/1-80", "$comment": "NTE02 NTE01=TPO"}, "line_pricing_repricing_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.repricing.json"}}, "drug_identification": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.drug.json"}}, "rendering_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}}, "supervising_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}}, "ordering_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}}, "referring_provider": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.provider.json"}}, "line_adjudication_information": {"type": "array", "maxItems": 15, "items": {"type": "object", "$ref": "837p.service_line_adjudication.json"}}, "form_identification": {"type": "array", "maxItems": 99, "items": {"type": "object", "$ref": "837p.form_identification.json"}}}, "required": ["professional_service", "service_date", "provider_control_number"], "errorMessage": {"required": "The following fields are required: Processional Service, Service Date (DTP03), Provider Control Number (REF02)"}}