{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.supporting_documentation.json", "title": "837 Professional Claim Supporting Documentation", "type": "object", "$comment": "med_claim_sup_doc", "additionalProperties": false, "properties": {"question_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-20", "$comment": "FRM01"}, "question_response_code": {"type": "string", "default": "1", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/FRM02"}], "$comment": "FRM02", "errorMessage": {"enum": "Invalid Question Response Code"}}, "question_response": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "FRM03"}, "question_response_as_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "FRM04"}, "question_response_as_percent": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-6"}, {"$ref": "837p.fields.json#/$defs/percent4-2"}], "$comment": "FRM05"}}, "required": ["question_number"], "anyOf": [{"required": ["question_response"], "properties": {"question_response": {"$ref": "837p.fields.json#/$defs/1-50"}}}, {"required": ["question_response_code"], "properties": {"question_response_code": {"$ref": "837p.ecl.json#/$defs/FRM02"}}}, {"required": ["question_response_as_date"], "properties": {"question_response_as_date": {"$ref": "837p.fields.json#/$defs/date"}}}, {"required": ["question_response_as_percent"], "properties": {"question_response_as_percent": {"$ref": "837p.fields.json#/$defs/percent4-2"}}}], "errorMessage": {"required": "The following fields are required: Question Number (FRM01)", "anyOf": "At least one of Question Response, Question Response Code, Question Response Date, Question Response Percent is required"}}