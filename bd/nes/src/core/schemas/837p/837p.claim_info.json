{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.claim_info.json", "title": "837 Professional Claim - Claim Information", "type": "object", "$comment": "med_claim_info", "additionalProperties": false, "properties": {"isInlineSubform": true, "claim_filing_code": {"type": "string", "$ref": "837p.ecl.json#/$defs/SBR09", "$comment": "SBR09", "errorMessage": {"enum": "Invalid Filling Indicator Code"}}, "property_casualty_claim_number": {"type": "string", "$ref": "837p.fields.json#/$defs/1-50", "$comment": "REF02"}, "death_date": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/date"}], "$comment": "PAT06"}, "patient_weight": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-10"}, {"$ref": "837p.fields.json#/$defs/weight"}], "$comment": "PAT08"}, "pregnancy_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/PAT09"}], "$comment": "PAT09"}, "patient_control_number": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-38"}, {"$ref": "837p.fields.json#/$defs/pcn"}], "$comment": "CLM01"}, "claim_charge_amount": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "CLM02"}, "place_of_service_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-2"}, {"$ref": "837p.ecl.json#/$defs/CLM05-01"}], "$comment": "CLM05-01", "errorMessage": {"enum": "Invalid Place of Service Code"}}, "claim_frequency_code": {"type": "string", "default": "1", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CLM05-03"}], "$comment": "CLM05-03", "errorMessage": {"enum": "Invalid Claim Frequency Code"}}, "signature_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CLM06"}], "$comment": "CLM06", "errorMessage": {"enum": "Invalid Signature Indicator"}}, "plan_participation_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CLM07"}], "$comment": "CLM07", "errorMessage": {"enum": "Invalid Plan Participate Code"}}, "benefits_assignment_certification_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CLM08"}], "$comment": "CLM08", "errorMessage": {"enum": "Invalid Benefits Assignment Certification Indicator"}}, "release_information_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CLM09"}], "$comment": "CLM09", "errorMessage": {"enum": "Invalid Release of Information Code"}}, "patient_signature_source_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CLM10"}], "$comment": "CLM10", "errorMessage": {"enum": "Invalid Patient Signature Source Code"}}, "related_cause_code": {"type": "string", "$ref": "837p.ecl.json#/$defs/CLM11", "$comment": "CLM11", "errorMessage": {"enum": "Invalid Related Cause Code"}}, "auto_accident_state_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/N402"}], "$comment": "CLM11", "errorMessage": {"enum": "Invalid State"}}, "special_program_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/2-2"}, {"$ref": "837p.ecl.json#/$defs/CLM12"}], "$comment": "CLM12", "errorMessage": {"enum": "Invalid Special Program Code"}}, "delay_reason_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-2"}, {"$ref": "837p.ecl.json#/$defs/CLM20"}], "$comment": "CLM20", "errorMessage": {"enum": "Invalid Delay Reason Code"}}, "patient_amount_paid": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-18"}, {"$ref": "837p.fields.json#/$defs/currency"}], "$comment": "ATM02"}, "claim_supplemental_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.supplemental.json"}}, "claim_note": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.claim_note.json"}}, "homebound_indicator": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-1"}, {"$ref": "837p.ecl.json#/$defs/CRC02"}], "$comment": "CRC02", "errorMessage": {"enum": "Homebound Indicator"}}, "health_care_code_information": {"type": "array", "maxItems": 12, "items": {"type": "object", "additionalProperties": false, "properties": {"diagnosis_type_code": {"type": "string", "default": "ABF", "allOf": [{"$ref": "837p.fields.json#/$defs/1-3"}, {"$ref": "837p.ecl.json#/$defs/HI0*"}], "$comment": "HI0(1-12)-01", "errorMessage": {"enum": "Invalid Diagnosis Code Type"}}, "diagnosis_code": {"type": "string", "allOf": [{"$ref": "837p.fields.json#/$defs/1-30"}, {"$ref": "837p.code_sets.json#/$defs/icd10"}], "$comment": "HI0(1-12)-02", "errorMessage": {"enum": "Invalid Diagnosis Code"}}}, "required": ["diagnosis_type_code", "diagnosis_code"], "errorMessage": {"required": "The following fields are required: Diagnosis Code Type and Diagnosis Code"}}}, "claim_pricing_repricing_information": {"type": "array", "maxItems": 1, "items": {"isInlineSubform": true, "$ref": "837p.repricing.json"}}, "other_subscriber_information": {"type": "array", "maxItems": 10, "items": {"type": "object", "$ref": "837p.osubscriber.json"}}, "service_lines": {"type": "array", "maxItems": 50, "minItems": 1, "items": {"type": "object", "$ref": "837p.service_line.json"}}}, "required": ["benefits_assignment_certification_indicator", "claim_charge_amount", "claim_filing_code", "claim_frequency_code", "health_care_code_information", "patient_control_number", "place_of_service_code", "plan_participation_code", "release_information_code", "service_lines", "signature_indicator"], "errorMessage": {"required": "The following fields are required: Benefits Assignment Certification Indicator, Charge Amount, Filing Code, Frequency Code, Diagnosis, Patient Control Number, Place of Service Code, Plan Participate Code, Release of Information Code, Signature Indicator"}}