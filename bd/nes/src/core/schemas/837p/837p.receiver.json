{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "837p.receiver.json", "title": "837 Professional Claim Receiver", "type": "object", "$comment": "med_claim_receiver", "additionalProperties": false, "properties": {"isInlineSubform": true, "organization_name": {"type": "string", "$ref": "837p.fields.json#/$defs/1-60", "$comment": "NM103"}}, "required": ["organization_name"], "errorMessage": {"required": "The following fields are required: Organization Name (NM103)"}}