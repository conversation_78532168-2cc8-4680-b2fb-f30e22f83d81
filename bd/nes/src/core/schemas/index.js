"use strict";
/**
 * Base class for schema handling. Extend in your schema sub-directory
 * This class provides the structure for initializing schemas,
 * adding schemas to AJV instance, and adding custom keywords.
 */

class Schema {
    constructor() {
        this.keyword = "default"; // Keyboard used to access the validator
    }

    async init() {
        // Perform any necessary init functionality prior to compiling
    }

    async addCustomKeywords(_ajv) {
        // Override in subclass to add custom keywords used in the schema
    }

    async addCustomFormats(_ajv) {
        // Override in subclass to add custom formats used in the schema
    }

    async getCompiledSchemas(_ajv) {
        // Add your schema and compile it here. Should return the compiled schema.
    }
}

const fs = require("fs").promises; // Ensure to use promises API for fs
const path = require("path");

const schemasDir = __dirname;
const currentFilePath = __filename;

const { ModuleLogger } = require("@core/logger");

const CustomKeywords = {
    // When used on a property, sets the default value to an array length
    // defined as defaultCounterLength: { arrayField: "field_name" }
    DEF_COUNTER: "defaultCounterLength",
    // Trims string field to max length before validation
    TRIM_MAX_LEN: "trimMaxLength",
    // Removes property in the final validation results
    REMOVE_PROPERTY: "removeProperty",
    // Converts number to fixed precision
    TO_FIXED: "toFixed",
    META_DATA: "metadata",
    // If set, removes all non-numeric characters from the string
    JUST_NUMBERS: "justNumbers",
    // Sets string to uppercase
    TO_UPPERCASE: "toUpperCase",
    // Removes the dot
    REMOVE_DOT: "removeDot",
    // Pads numbers to the left with zeros
    PAD_ZERO: "padZero",
    // Pads numbers to the left and right with zeros
    PAD_ZERO_DECIMAL: "padZeroDecimal",
    // Removes the hyphen
    REMOVE_HYPHEN: "removeHyphen",
    // Tags the inline subform in the schemas so we can modify the instance path of the errors post validation
    IS_INLINE_SUBFORM: "isInlineSubform",
};

class SchemaValidatorClass extends ModuleLogger {
    constructor(nes) {
        super("schema-validator");
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.DSL = nes.shared.DSL;
        this.dsl = nes.modules.dsl;
        this.crud = nes.shared.crud;
        this.shared = nes.shared;
        this.nes = nes;
        this.validator = null;
        this.schemas = new Map();

        const Ajv2020 = require("ajv/dist/2020");
        const ajvOptions = {
            allErrors: true,
            strictNumbers: false,
            strictTuples: true,
            strictTypes: false,
            strictRequired: true,
            removeAdditional: true,
            allowMatchingProperties: true,
            useDefaults: "empty",
            coerceTypes: true,
            logger: {
                log: super.info.bind(this),
                warn: super.warn.bind(this),
                error: super.error.bind(this),
            },
        };

        this.ajv = new Ajv2020(ajvOptions);
        require("ajv-errors")(this.ajv);
        require("ajv-keywords")(this.ajv, "transform");
    }

    /**
     * Initializes the SchemaValidator by setting up AJV with custom keywords and registering schemas.
     */
    async init() {
        super.debug("Initializing AJV...");

        await this.__addCustomKeywords(this.ajv);
        await this.__addCustomFormats(this.ajv);
        await this.__registerSchemas(schemasDir);
        return this;
    }

    /**
     * Retrieves a schema validator based on the provided keyword.
     * @param {string} keyword - The keyword associated with the schema to retrieve.
     * @returns {<Object>} The schema validator if found.
     * @throws {Error} If the keyword is not provided or the schema is not found.
     */
    async getSchemaValidator(keyword) {
        if (!keyword) throw new Error(`Invalid schema keyword ${keyword}`);

        if (this.schemas.has(keyword)) {
            return this.schemas.get(keyword);
        }

        throw new Error(`Invalid schema: ${keyword}`);
    }

    /**
     * Modifies the instance path of validation errors to handle inline subforms.
     * @param {string} instancePath - The original instance path from the validation error.
     * @param {Object} compiledSchema - The compiled schema object containing schema and refs.
     * @param {Object} compiledSchema.schema - The main schema object.
     * @param {Object} compiledSchema.refs - Object containing referenced schemas.
     * @returns {string} The modified instance path with inline subform array indices removed.
     */
    modifyErrorInstancePath(instancePath, compiledSchema) {
        const schema = compiledSchema.schema;
        const ajv = this.ajv;
        function findSchemaForPath(pathParts) {
            let currentSchema = schema;

            for (const part of pathParts) {
                if (!isNaN(part)) {
                    currentSchema = currentSchema.items || {};
                    // Handle $ref
                    if (currentSchema.$ref) {
                        currentSchema =
                            ajv.getSchema([currentSchema.$ref])?.schema || {};
                    }
                } else {
                    currentSchema = currentSchema.properties?.[part] || {};
                    // Handle $ref
                    if (currentSchema.$ref) {
                        currentSchema =
                            ajv.getSchema([currentSchema.$ref])?.schema || {};
                    }
                }
            }
            return currentSchema;
        }

        const parts = instancePath.split("/").filter(Boolean);
        const newParts = [];

        for (let idx = 0; idx < parts.length; idx++) {
            const part = parts[idx];

            if (!isNaN(part)) {
                const parentSchema = findSchemaForPath(parts.slice(0, idx));
                const itemsSchema = parentSchema.items || {};

                if (
                    !itemsSchema.properties?.isInlineSubform &&
                    !itemsSchema?.isInlineSubform
                ) {
                    newParts.push(part);
                }
            } else {
                newParts.push(part);
            }
        }

        return newParts.join("/");
    }

    /**
     * Processes validation errors by mapping them to form fields.
     * @param {Object} form - The form object to validate against.
     * @param {Array} errors - The list of errors returned from validation.
     * @param {Object} compiledSchema - The compiled schema object.
     * @throws {Error} If the form object is not provided.
     */
    async processValidationErrors(form, errors, compiledSchema) {
        if (!form) throw new Error(`Form is required to reference DSL fields.`);

        if (!errors || errors.length == 0) {
            throw new Error(`No errors to process`);
        }

        try {
            const updatedErrors =
                this.__splitMissingRequiredFieldsErrors(errors);
            const modifiedErrors = updatedErrors?.map((error) => ({
                ...error,
                instancePath: this.modifyErrorInstancePath(
                    error.instancePath,
                    compiledSchema
                ),
            }));
            const errorsWithMessage = modifiedErrors.filter(
                (error) => error.keyword == "errorMessage"
            );
            let normalErrors = modifiedErrors.filter(
                (error) => error.keyword != "errorMessage"
            );
            if (errorsWithMessage.length > 0) {
                // Don't show raw error messages if we have custom error messages already defined
                normalErrors = [];
            }
            const [
                mappedFieldsWithCustomErrorMessage,
                mappedFieldsWithNormalErrorMessage,
            ] = await Promise.all([
                this.__processErrorMessages(errorsWithMessage),
                this.__processErrorMessages(normalErrors),
            ]);

            const mappedErrors = {
                ...mappedFieldsWithNormalErrorMessage,
                ...mappedFieldsWithCustomErrorMessage,
            };

            if (form === "ncpdp") {
                const validationErrors =
                    await this.nes.shared.ncpdpClaimErrorMapper.mapSchemaErrors(
                        mappedErrors
                    );

                const rawErrorMessage = this.ajv.errorsText(errors, {
                    separator: "\n",
                    dataVar: "Pharmacy Claim",
                });
                const rawErrorMessages = rawErrorMessage.split("\n");

                return { validationErrors, rawErrorMessages };
            } else if (form === "med_claim") {
                const rawErrorMessage = this.ajv.errorsText(errors, {
                    separator: "\n",
                    dataVar: "Medical Claim",
                });
                const rawErrorMessages = rawErrorMessage.split("\n");
                const validationErrors =
                    await this.nes.shared.medicalClaimErrorMapper.mapSchemaErrors(
                        mappedErrors
                    );
                return { validationErrors, rawErrorMessages };
            } else {
                return {
                    validationErrors: mappedErrors,
                    rawErrorMessages: null,
                };
            }
        } catch (e) {
            console.error(
                `Exception while parsing schema validation errors. Error:${e.message} Stack:${e.stack}`
            );
            throw e;
        }
    }

    async __mapErrorsToField(mappedField, medClaimRec, error) {
        try {
            let errorResultPath = `${mappedField}`;
            if (errorResultPath.includes("{idx}")) {
                console.debug(
                    `Medical Claim Error is in multi-field, attempting to find index(es).`
                );

                const { errorDslPath, dslPath } = mappedField;
                if (!dslPath) {
                    throw new Error(
                        `No dslPath object found for result ${JSON.stringify(
                            errorDslPath
                        )} with error: ${JSON.stringify(error)}`
                    );
                }
                errorResultPath = await this.__handleErrorWithMissingIndexes(
                    medClaimRec,
                    errorDslPath,
                    dslPath,
                    error
                );
                if (!errorResultPath) {
                    return false;
                }
            }

            return {
                path: errorResultPath,
                error: error.description,
            };
        } catch (error) {
            super.error(
                `Error setting error in result: ${error}, mapped field: ${mappedField}, error message: ${error.description}`
            );
            throw error;
        }
    }

    /**
     * Splits errors related to missing required fields by appending the missing property to the instancePath
     * @param {Array} errors - Array of validation errors to process
     * @returns {Array} Modified array of errors with updated paths and messages for missing required fields
     */
    __splitMissingRequiredFieldsErrors(errors) {
        const formattedErrors = [];

        errors.forEach((error) => {
            const hasRequiredError = error.params?.errors?.find(
                (param) => param.keyword === "required"
            );
            if (hasRequiredError) {
                const ogErrors = error.params.errors;
                const missingProperties = ogErrors.reduce((acc, curr) => {
                    acc.push(curr?.params?.missingProperty);
                    return acc;
                }, []);

                missingProperties.forEach((property) => {
                    formattedErrors.push({
                        ...error,
                        instancePath:
                            `${error.instancePath}/${property}`.replace(
                                /\/\//g,
                                "/"
                            ),
                        message: `Missing required field: ${property}`,
                        params: { missingProperty: property }, // Set only the current missing property
                    });
                });
            } else {
                // Push other errors as-is
                formattedErrors.push(error);
            }
        });

        return formattedErrors;
    }

    /**
     * Private method to register schemas from a specified directory.
     * @param {string} dir - The directory to search for schema files.
     */
    async __registerSchemas(dir) {
        const files = await fs.readdir(dir); // Now properly using async/await with fs.promises
        for (const file of files) {
            const filePath = path.join(dir, file);
            const stats = await fs.stat(filePath);

            if (stats.isDirectory()) {
                // Skip the current directory to avoid infinite recursion
                if (filePath === schemasDir) {
                    continue;
                }
                await this.__registerSchemas(filePath);
            } else if (file === "index.js" && filePath !== currentFilePath) {
                let relativeFilePath = path.relative(__dirname, filePath);
                if (
                    !relativeFilePath.startsWith(".") &&
                    !relativeFilePath.startsWith("..")
                ) {
                    relativeFilePath = "./" + relativeFilePath;
                }
                const SchemaValidatorClass = require(relativeFilePath);

                try {
                    const schemaValidator = await new SchemaValidatorClass(
                        this.nes
                    ).init();
                    await schemaValidator.addCustomKeywords(this.ajv);
                    await schemaValidator.addCustomFormats(this.ajv);

                    super.debug(
                        `Compiling Schema ${schemaValidator.keyword}...`
                    );
                    const compiledSchema =
                        await schemaValidator.getCompiledSchemas(this.ajv);
                    this.schemas.set(schemaValidator.keyword, compiledSchema);
                } catch (e) {
                    super.error(
                        `Error compiling schema: ${e.message} Stack:${e.stack}`
                    );
                    throw e;
                }
            }
        }
    }

    async __addCustomFormats(ajv) {
        return;
    }

    /**
     * Adds custom AJV keywords for schema validation.
     */
    async __addCustomKeywords(ajv) {
        ajv.addKeyword({
            keyword: CustomKeywords.META_DATA,
            compile(schema, parentSchema) {
                return function (kwVal, data, metadata, dataCxt) {
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "object",
            },
        });

        ajv.addKeyword({
            schema: false,
            keyword: CustomKeywords.TRIM_MAX_LEN,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    if (typeof kwVal === "string" && kwVal.length > schema) {
                        parentData[parentDataProperty] = kwVal.substring(
                            0,
                            schema
                        );
                    }
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "integer",
                minimum: 0,
            },
        });

        ajv.addKeyword({
            schema: false,
            keyword: CustomKeywords.REMOVE_PROPERTY,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    delete parentData[parentDataProperty];
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.DEF_COUNTER,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema, _) {
                return function (
                    kwVal,
                    { parentData, parentDataProperty, rootData }
                ) {
                    if (Array.isArray(parentData[schema?.arrayField])) {
                        parentData[parentDataProperty] =
                            `${parentData[schema?.arrayField].length}`;
                    } else {
                        delete parentData[parentDataProperty];
                    }
                    return true; // Always returns true to indicate the data is valid
                };
            },
            metaSchema: {
                type: "object",
                properties: {
                    arrayField: { type: "string" },
                },
                required: ["arrayField"],
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.IS_INLINE_SUBFORM,
            modifying: false,
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.TO_FIXED,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value !== "number" && isNaN(parseFloat(value))) {
                        return false; // Validation error if not a number
                    }
                    const fixedValue = parseFloat(value).toFixed(schema);
                    parentData[parentDataProperty] = fixedValue;
                    return true;
                };
            },
            metaSchema: {
                type: "integer",
                minimum: 0,
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.JUST_NUMBERS,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string" && /\D/.test(value)) {
                        const justNumbers = value.replace(/\D/g, "");
                        parentData[parentDataProperty] = justNumbers;
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.TO_UPPERCASE,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string") {
                        parentData[parentDataProperty] = value.toUpperCase();
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
                minimum: 0,
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.MODIFY_PATH,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string") {
                        parentData[parentDataProperty] = value.toUpperCase();
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
                minimum: 0,
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.REMOVE_DOT,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string") {
                        parentData[parentDataProperty] = value.replace(
                            /\./g,
                            ""
                        );
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
                minimum: 0,
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.PAD_ZERO,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string") {
                        const paddedValue = value.padStart(schema, "0");
                        parentData[parentDataProperty] = paddedValue;
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "integer",
                minimum: 0,
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.PAD_ZERO_DECIMAL,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string" && value.includes(".")) {
                        const parts = value.split(".");
                        const intPart = (parts[0] || "0").padStart(
                            schema.integerPart,
                            "0"
                        );
                        const decimalPart = (parts[1] || "0").padEnd(
                            schema.decimalPart,
                            "0"
                        );
                        parentData[parentDataProperty] =
                            `${intPart}.${decimalPart}`;
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "object",
                properties: {
                    integerPart: { type: "integer" },
                    decimalPart: { type: "integer" },
                },
                required: ["integerPart", "decimalPart"],
            },
        });

        ajv.addKeyword({
            keyword: CustomKeywords.REMOVE_HYPHEN,
            modifying: true, // Indicates that this keyword modifies the data
            compile(schema) {
                return function (kwVal, { parentData, parentDataProperty }) {
                    const value = parentData[parentDataProperty];
                    if (typeof value === "string") {
                        parentData[parentDataProperty] = value.replace(
                            /-/g,
                            ""
                        );
                    }
                    return true;
                };
            },
            metaSchema: {
                type: "boolean",
            },
        });
    }

    /**
     * Processes error messages from validation errors.
     * @param {Array} errors - An array of validation error objects.
     * @returns {Object} An object with mapped fields and their corresponding error messages.
     * @private
     */
    async __processErrorMessages(errors) {
        const mappedFields = {};
        for (const error of errors) {
            const message = error.message;
            const instancePath = error.instancePath;
            const noLeadingSlash = instancePath.replace(/^\/+/, "");
            const convertedPath = noLeadingSlash.replace(/\//g, ".") || "*";

            if (mappedFields[convertedPath]) {
                mappedFields[convertedPath] += `, ${message}`;
            } else {
                mappedFields[convertedPath] = message;
            }
        }
        return mappedFields;
    }
}

module.exports = {
    Schema,
    SchemaValidatorClass,
};
