/*jshint : 6 */
"use strict";
const _ = require("lodash");

const { SecurityRulesHandler, RuleType, ReturnType } = require("../index");

/**
 * @class
 * @classdesc Class responsible for running order related actions
 */
module.exports = class BillingRulesHandlerClass extends SecurityRulesHandler {
    constructor(nes) {
        super(nes);
        this.nes = nes;
        this.shared = nes.shared;
        this.form = nes.shared.form;
    }

    get rules() {
        return {
            // This rule checks if the user need approval to void an invoice
            voidInvoice: {
                url: "/security/rule?rule=voidInvoice&invoice_id=123",
                args: ["invoice_id"],
                description:
                    "This rule checks if the user need approval to void an invoice",
                ruleType: RuleType.NEEDS_APPROVAL,
                returnType: ReturnType.BOOLEAN,
                func: (user, ...args) => this.#voidInvoice(user, ...args),
            },
            adjustmentPostClose: {
                url: "/security/rule?rule=adjustmentPostClose&invoice_id=123",
                args: ["invoice_id"],
                description:
                    "This rule checks if the user need approval to post an adjustment for a transaction post close",
                ruleType: RuleType.NEEDS_APPROVAL,
                returnType: ReturnType.BOOLEAN,
                func: (user, ...args) =>
                    this.#adjustmentPostClose(user, ...args),
            },
            adjustmentThreshold: {
                url: "/security/rule?rule=adjustmentThreshold&amount=123",
                args: ["amount"],
                description:
                    "This rule checks if the user need approval to post an adjustment based on a set threshold amount",
                ruleType: RuleType.NEEDS_APPROVAL,
                returnType: ReturnType.BOOLEAN,
                func: (user, ...args) =>
                    this.#adjustmentThreshold(user, ...args),
            },
            userCanPostRevenueOnDate: {
                url: "/security/rule?rule=userCanPostRevenueOnDate&date=2021-01-01",
                args: ["date"],
                description:
                    "This rule checks if the user can post revenue on a given date",
                ruleType: RuleType.CAN_PERFORM,
                returnType: ReturnType.BOOLEAN,
                func: (user, ...args) =>
                    this.#userCanPostRevenueOnDate(user, ...args),
            },
        };
    }

    #voidInvoice = async (user, ...args) => {
        return false;
    };

    // eslint-disable-next-line no-unused-private-class-members
    #voidDeliveryTicket = async (user, ...args) => {
        return true;
    };

    #adjustmentPostClose = async (user, ...args) => {
        return false;
    };

    #adjustmentThreshold = async (user, ...args) => {
        // TODO check if user is a manager
        return false;
        // const amount = args[0];
        // const approvalThreshold = parseFloat(
        //     this.shared.config.company.adjustment_threshold || Number.MAX_VALUE
        // );
        // const needsApproval = amount > approvalThreshold;
        // return needsApproval;
    };

    #userCanPostRevenueOnDate = async (user, ...args) => {
        return true;
    };
};
