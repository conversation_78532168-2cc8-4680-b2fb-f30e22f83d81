"use strict";
const fs = require("fs").promises;
const path = require("path");

const RuleType = Object.freeze({
    NEEDS_APPROVAL: Symbol("NEEDS_APPROVAL"),
    CAN_PERFORM: Symbol("CAN_PERFORM"),
});

const ReturnType = Object.freeze({
    BOOLEAN: Symbol("BOOLEAN"),
    STRING: Symbol("STRING"),
    NUMBER: Symbol("NUMBER"),
    ARRAY: Symbol("ARRAY"),
    OBJECT: Symbol("OBJECT"),
});

/**
 * @class SecurityRulesHandler
 * @classdesc To use, under ruleset extend the SecurityRulesHandler class and define your rules.
 * You should not need to overwrite the needsApproval or canPerform methods. Let the parent class
 * handle the rule evaluation and return the result.
 */

class SecurityRulesHandler {
    constructor(nes) {
        this.nes = nes;
    }

    async process(ctx, urlpath) {
        if (urlpath.path.length < 2 || urlpath.path[1] == "") {
            ctx.status = 500;
            ctx.body = {
                error: "Invalid form path specified: " + urlpath.url.path,
            };
            return;
        }

        try {
            if (this.fx.checkParameters({ rule: "Rule Name" }, ctx) == false)
                return;
            const params = Object.assign({}, ctx.query);
            const ruleKey = params.rule;
            const rule = this.__getRule(ruleKey);
            const args = this.__matchParamsToArgs(rule, params);
            this.__checkRuleArgs(ruleKey, rule, args);
            const user = ctx.user;
            let res = null;
            if (rule.ruleType === RuleType.NEEDS_APPROVAL) {
                res = await this.needsApproval(ruleKey, user, ...args);
            } else if (rule.ruleType === RuleType.CAN_PERFORM) {
                res = await this.canPerform(ruleKey, user, ...args);
            }

            ctx.status = 200;
            ctx.body = { result: res };
        } catch (e) {
            console.error(
                `Error encountered while processing security rule. Error: ${e.message} Stack: ${e.stack}`
            );
            ctx.status = 500;
            ctx.body = { error: e.message };
        }
    }

    #exampleRuleFunc = async (user, ...args) => {
        return false;
    };

    // Define your rules in your override class
    get rules() {
        return {
            // Description of the rule
            EXAMPLE_RULE: {
                args: ["invoice_id", "patient_id"],
                ruleType: RuleType.NEEDS_APPROVAL,
                returnType: ReturnType.BOOLEAN,
                func: (user, ...args) => this.#exampleRuleFunc(user, ...args),
            },
        };
    }

    // You do NOT need to overwrite this in the child class.
    async needsApproval(ruleKey, user, ...args) {
        try {
            const rule = this.__getRule(ruleKey);
            if (rule.ruleType !== RuleType.NEEDS_APPROVAL) {
                throw new Error(`Rule ${ruleKey} is not a needs approval rule`);
            }
            this.__checkRuleArgs(ruleKey, rule, args);
            return await rule.func(user, ...args);
        } catch (error) {
            console.error(`Error running rule ${ruleKey}: ${error}`);
            return false;
        }
    }

    // You do NOT need to overwrite this in the child class.
    async canPerform(ruleKey, user, ...args) {
        try {
            const rule = this.__getRule(ruleKey);
            if (rule.ruleType !== RuleType.CAN_PERFORM) {
                throw new Error(`Rule ${ruleKey} is not a can perform rule`);
            }
            this.__checkRuleArgs(ruleKey, rule, args);
            return await rule.func(user, ...args);
        } catch (error) {
            console.error(`Error running rule ${ruleKey}: ${error}`);
            return false;
        }
    }

    __getRule(ruleKey) {
        const rule = this.rules[ruleKey];
        if (!rule) {
            throw new Error(`Rule ${ruleKey} not found`);
        }
        return rule;
    }

    __checkRuleArgs(ruleKey, rule, args) {
        if (rule.args) {
            const requiredArgs = rule.args.length;
            const providedArgs = args?.length || 0;
            if (providedArgs < requiredArgs) {
                throw new Error(
                    `Missing required argument(s) for rule ${ruleKey} Required: ${rule.args.join(", ")} Provided: ${args?.join(", ")}`
                );
            }
        }
    }

    __matchParamsToArgs(rule, params) {
        const args = [];
        for (const arg of rule.args) {
            const matchingParam = params[arg];
            if (matchingParam) {
                args.push(matchingParam);
            }
        }
        return args;
    }
}

async function registerSecurityRules(nes) {
    nes.securityRules = {};
    const rulesetDir = path.join(__dirname, "ruleset");
    const securityHandlers = await fs.readdir(rulesetDir);
    for (const file of securityHandlers) {
        if (file.endsWith(".js")) {
            const fileName = path.basename(file, ".js");
            console.log(`    security/${file}`);
            const SecurityRulesHandlerClass = require(
                path.join(rulesetDir, file)
            );
            if (
                !(
                    SecurityRulesHandlerClass.prototype instanceof
                    SecurityRulesHandler
                )
            ) {
                throw new Error(
                    `${SecurityRulesHandlerClass.name} must be a subclass of SecurityRulesHandler`
                );
            }
            nes.securityRules[fileName] = new SecurityRulesHandlerClass(nes);
            console.log(`      .${fileName}`);
        }
    }
}

module.exports = {
    RuleType,
    ReturnType,
    registerSecurityRules,
    SecurityRulesHandler,
};
