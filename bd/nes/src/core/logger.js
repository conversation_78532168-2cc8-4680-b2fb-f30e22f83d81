"use strict";

// HOW TO USE:
// Either use console.log/warn/error/info/debug as normal
// OR extend your class using ModuleLogger, see class definition
// below for how to use.
const { consoleFormat } = require("winston-console-format");
const moment = require("moment-timezone");
const os = require("os");

const DEFAULT_MODULE = "nes";
const DEFAULT_LOG_DIR =
    os.type() == "Darwin" ? "/tmp/clara/" : "/var/log/clara";
const LOG_LEVEL = "debug";

const winston = require("winston");
const {
    json,
    splat,
    combine,
    timestamp,
    printf,
    colorize,
    align,
    label,
    padLevels,
    errors,
    prettyPrint,
} = winston.format;
const SlackHook = require("winston-slack-webhook-transport");
const koa_winston = require("koa2-winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const Transport = require("winston-transport");

const { existsSync, mkdirSync, createWriteStream } = require("fs");
const createDirIfNotExists = (dir) =>
    !existsSync(dir) ? mkdirSync(dir) : undefined;

createDirIfNotExists(DEFAULT_LOG_DIR);

const get_filename = (info, error_logger = false) => {
    const module_name = info.module_name || DEFAULT_MODULE;
    return `${DEFAULT_LOG_DIR}/${module_name}${error_logger ? "-error" : ""}.log`;
};

const ignore_log_path = (path) => {
    return path.includes("/api/my/status") || path.includes("/login");
};

class DynamicFileTransport extends Transport {
    constructor(opts, error_logger = false) {
        super(opts);
        this.files = {};
        this.error_logger = error_logger;
    }

    log(info, callback) {
        setImmediate(() => {
            this.emit("logged", info);
        });

        const module_name = info.module_name || DEFAULT_MODULE;
        if (!this.files[module_name]) {
            this.files[module_name] = createWriteStream(
                get_filename(info, this.error_logger),
                { flags: "a" }
            );
        }

        this.files[module_name].write(
            `[${info.timestamp}] ${info.level}: ${info.message}\n`
        );

        callback();
    }

    // Ensure to close all file streams when you're done logging
    close() {
        Object.values(this.files).forEach((stream) => stream.end());
    }
}

class DynamicRotateFile extends Transport {
    constructor(opts) {
        super(opts);
        this.rotateOptions = opts.rotateOptions;
        this.transport = null;
    }

    log(info, callback) {
        setImmediate(() => {
            this.emit("logged", info);
        });

        // Determine the filename dynamically from the log info
        const currentFilename = get_filename(info);

        // Check if transport needs to be recreated
        if (!this.transport || this.transport.filename !== currentFilename) {
            if (this.transport) {
                this.transport.close(); // Close the old transport
            }
            // Create a new transport with the new filename
            this.transport = new DailyRotateFile(
                Object.assign({}, this.rotateOptions, {
                    filename: currentFilename,
                })
            );
        }

        // Delegate the logging to the daily rotate file transport
        this.transport.log(info, callback);
    }

    // Ensure to close the file stream when you're done logging
    close() {
        if (this.transport) {
            this.transport.close();
        }
    }
}

class ArrayTransport extends Transport {
    constructor(opts) {
        super(opts);
        this.logArray = [];
        this.maxLogs = 50;
    }

    log(info, callback) {
        if (this.logArray.length >= this.maxLogs) {
            this.logArray.shift(); // Remove the oldest log
        }
        this.logArray.push(info);
        callback();
    }

    clearLogs() {
        this.logArray = [];
    }

    getLogs() {
        return this.logArray;
    }
}

// Define the different transports we are going to use through the app
// Console, daily rotation logs, and error logs
const console_transport = new winston.transports.Console({
    json: true,
    stringify: true,
    format: combine(
        colorize(),
        errors({ stack: true }),
        padLevels(),
        splat(),
        json(),
        prettyPrint(),
        consoleFormat({
            showMeta: true,
            metaStrip: ["service"],
            inspectOptions: {
                depth: Infinity,
                colors: true,
                maxArrayLength: Infinity,
                breakLength: 120,
                compact: Infinity,
            },
        })
    ),
});

const daily_transport = new DynamicRotateFile({
    rotateOptions: {
        frequency: "24h",
        level: LOG_LEVEL,
        datePattern: "YYYY-MM-DD",
        maxFiles: "14",
        maxsize: "20m",
        createSymlink: true,
        format: combine(
            colorize(),
            errors({ stack: true }),
            padLevels(),
            timestamp({
                format: "YYYY-MM-DD hh:mm:ss.SSS A",
            }),
            align(),
            printf(
                (info) => `[${info.timestamp}] ${info.level}: ${info.message}`
            )
        ),
    },
});

const error_transport = new DynamicFileTransport(
    {
        level: "warn",
        format: combine(
            colorize(),
            errors({ stack: true }),
            padLevels(),
            label({ label: `NES ${process.env["FLY_ID"]}` }),
            timestamp({
                format: "YYYY-MM-DD hh:mm:ss.SSS A",
            }),
            align(),
            printf(
                (info) => `[${info.timestamp}] ${info.level}: ${info.message}`
            )
        ),
        maxsize: 10000000,
        maxFiles: 10,
        tailable: true,
    },
    true
);

const slack_transport = new SlackHook({
    webhookUrl:
        "*********************************************************************************",
    level: "error",
    formatter: (info) => ({
        text: `${process.env.FLY_ID.split("-")[0]}_\\n*${info.level.toUpperCase()}:*\\n>${info.message}`,
    }),
});

// Create our primary logger now that we have the transports defined
const logger = winston.createLogger({
    level: LOG_LEVEL,
    transports: [console_transport, daily_transport, error_transport],
});

/**
 * Extend out to use a module level logger
 *
 * @param {string} module_name - The name of the module (will log under a {module_name}.log file instead of nes.log)
 * @param {string} child_module - The name of the child module
 */
class ModuleLogger {
    constructor(module_name, child_module = null) {
        this.logger = logger;
        this.module_name = module_name;
        if (child_module) {
            this.logger = this.logger.child({ module: child_module });
        }
    }

    log(message, meta = {}) {
        meta.module_name = this.module_name;
        this.logger.info(message, { meta });
    }

    info(message, meta = {}) {
        meta.module_name = this.module_name;
        this.logger.info(message, { meta });
    }

    warn(message, meta = {}) {
        meta.module_name = this.module_name;
        this.logger.warn(message, { meta });
    }

    error(message, meta = {}) {
        meta.module_name = this.module_name;
        this.logger.error(message, { meta });
    }

    debug(message, meta = {}) {
        meta.module_name = this.module_name;
        this.logger.debug(message, { meta });
    }
}

module.exports = {
    initLogger: function () {
        console.log("Initializing Logger...");

        const logToLogger = (loggerFunction, args) => {
            const message = args
                .map((arg) => {
                    if (arg instanceof Error) {
                        return arg.stack || arg.message || arg.toString();
                    } else if (typeof arg === "object") {
                        try {
                            return JSON.stringify(arg, null, 2);
                        } catch (error) {
                            return `Circular structure in logging: ${error}`;
                        }
                    }
                    return String(arg);
                })
                .join(" ");
            loggerFunction(message);
        };
        const overrideConsoleMethod = (method, loggerFunction) => {
            global.console[method] = (...args) => {
                logToLogger(loggerFunction, args);
            };
        };

        overrideConsoleMethod("error", logger.error);
        overrideConsoleMethod("warn", logger.warn);
        overrideConsoleMethod("info", logger.info);
        overrideConsoleMethod("log", logger.info);
        overrideConsoleMethod("debug", logger.debug);

        global.console.log_ctx = function (ctx, status, start, end) {
            try {
                const ur = new URL(
                    ctx.request.url,
                    `https://${ctx.request.headers.host}`
                );

                //NOTE: this.shared.config is not available here
                //      so we need to check environment manually
                if (process.env["NODE_ENV"] != "production") {
                    // do not log health check calls in non-prod mode
                    if (ignore_log_path(ur.pathname)) return;
                }

                let ms = end - start;
                if (isNaN(ms)) {
                    ms = 0;
                }
                const reqId = ctx.state ? ctx.state.reqId : "-";
                const ip =
                    "headers" in ctx && ctx.headers
                        ? ctx.request.header["cf-connecting-ip"] ||
                          ctx.request.header["x-real-ip"] ||
                          ctx.request.header["x-forwarded-for"] ||
                          "-"
                        : "-";
                const us =
                    "user" in ctx && ctx.user && "username" in ctx.user
                        ? ctx.user.username
                        : "-";
                const dt = moment(start).format("DD/MMM/YYYY:HH:mm:ss.SSS Z");
                const rf =
                    "headers" in ctx && ctx.headers && "origin" in ctx.headers
                        ? ctx.headers["origin"]
                        : "-";
                const ua =
                    "headers" in ctx &&
                    ctx.headers &&
                    "user-agent" in ctx.headers
                        ? ctx.headers["user-agent"]
                        : "-";
                logger.info(
                    [
                        ip,
                        "-",
                        us,
                        "[" + dt + "]",
                        "reqId - ",
                        reqId ? reqId : "-",
                        " ",
                        ctx.protocol.toUpperCase(),
                        '"' + ctx.request.method + " " + ur.pathname + '"',
                        status,
                        ms + "ms",
                        '"' + rf + '"',
                        '"' + ua + '"',
                    ].join(" ")
                );
            } catch (e) {
                logger.error(e);
            }
        };
    },

    get_module_logger: function (module_name, child_module = null) {
        return new ModuleLogger(module_name, child_module);
    },

    set_log_level: function (log_level = "warn") {
        logger.level = log_level;
    },

    attach_app_logger: function (options = {}) {
        const transports = [
            console_transport,
            daily_transport,
            error_transport,
        ];

        if (options?.config?.env["NODE_ENV"] == "production") {
            // Only use slack alerts for prod environments
            transports.push(slack_transport);
            logger.add(slack_transport);
        }

        const koa_win_log = koa_winston.logger({
            transports: transports,
            level: LOG_LEVEL,
            reqKeys: ["url", "method", "href", "query", "header.authorization"],
            reqUnselect: ["header.cookie"],
            resKeys: ["header", "header.content-type", "status"],
        });

        return async (ctx, next) => {
            // do not log health check calls in non-prod mode
            if (
                options?.config?.env["NODE_ENV"] != "production" &&
                ignore_log_path(ctx.url)
            ) {
                await next();
            } else {
                await koa_win_log(ctx, next);
            }
        };
    },

    startArrayTransport: function () {
        const arrayTransport = new ArrayTransport();
        logger.add(arrayTransport);
        return arrayTransport;
    },

    removeArrayTransport: function (arrayTransport) {
        logger.remove(arrayTransport);
    },

    ModuleLogger: ModuleLogger,
};
