"use strict";

/**
 * Shared services class that don't require direct access to the server
 * but still have to be initialized during startup
 */
const FormGetClass = require("@form/operations/get");
const FormPostClass = require("@form/operations/post");
const FormPutClass = require("@form/operations/put");
const FormValidateClass = require("@form/operations/validate");
const { SchemaValidatorClass } = require("@schemas/index");
const EmailNotificationClass = require("@service/email-notify");
const ShippoLabelService = require("@service/shippo");
const MedicalClaimErrorMapperClass = require("@service/medical-claim-error-mapper");
const NcpdpClaimErrorMapperClass = require("@service/ncpdp-claim-error-mapper");
const ShippoWebhookService = require("@service/shippo-webhook");
const FaxService = require("@service/fax");
module.exports = class Service {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.customer = nes.modules.customer;
    }

    async init() {
        this.shared.form = {
            get: new FormGetClass(this.nes),
            post: new FormPostClass(this.nes),
            put: new FormPutClass(this.nes),
            validate: new FormValidateClass(this.nes),
        };
        await this.customer.load_company().catch((error) => {
            console.error("Error loading company config...", error);
        });
        this.shared.notify = {
            sendTemplateEmail: new EmailNotificationClass(this.nes)
                ?.sendTemplateEmail,
            sendRawEmail: new EmailNotificationClass(this.nes)?.sendRawEmail,
        };

        const [schema, medicalClaimErrorMapper, ncpdpClaimErrorMapper] =
            await Promise.all([
                new SchemaValidatorClass(this.nes).init(),
                new MedicalClaimErrorMapperClass(this.nes).init(),
                new NcpdpClaimErrorMapperClass(this.nes).init(),
            ]);
        this.shared.schema = schema;
        this.shared.medicalClaimErrorMapper = medicalClaimErrorMapper;
        this.shared.ncpdpClaimErrorMapper = ncpdpClaimErrorMapper;
        const env = this.shared.config.env?.NODE_ENV;
        const shippo_token = this.shared.config.env?.SHIPPO_API_TOKEN || "";

        // enforce proper shippo token usage
        if (env !== "production" && !shippo_token.startsWith("shippo_test")) {
            console.error(
                "You should use Shippo sandbox token with non-production environments."
            );
        } else if (
            env === "production" &&
            shippo_token.startsWith("shippo_test")
        ) {
            console.error(
                "You cannot use sandbox shippo token with production environments!"
            );
        }

        this.shared.label = new ShippoLabelService(this.nes);
        this.shared.shipment_webhook = new ShippoWebhookService(
            this.nes
        ).process;

        const faxService = new FaxService(this.nes);
        await faxService.init();
        this.shared.fax = faxService;
    }
};
