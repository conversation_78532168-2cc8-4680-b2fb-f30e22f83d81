"use strict";
const _ = require("lodash");
module.exports = class NESCACHE {
    constructor(nes) {
        this.shared = nes.shared;
        this.db = nes.modules.db;
    }

    async init() {
        await this.db.env.rw.query(`
            CREATE TABLE IF NOT EXISTS "nescache" (
                "id" SERIAL PRIMARY KEY,
                "key" VARCHAR(255) UNIQUE,
                "expiration" TIMESTAMP without TIME ZONE,
                "updated_on" TIMESTAMP without TIME ZONE DEFAULT NOW(),
                "data" TEXT
            );
            SELECT pg_advisory_lock(1);
            ALTER TABLE nescache ADD COLUMN IF NOT EXISTS updated_on TIMESTAMP without TIME ZONE DEFAULT NOW();
            SELECT pg_advisory_unlock(1);
        `);
        setInterval(this.gc.bind(this), 1000000);
        this.gc();
    }

    async flush() {
        try {
            console.log("FLUSH");
            await this.db.env.rw.query(`TRUNCATE nescache;`);
        } catch {
            return;
        }
    }

    async gc() {
        try {
            await this.db.env.rw.query(
                `DELETE from nescache WHERE expiration < NOW();`
            );
        } catch (error) {
            console.log(error);
        }
    }

    async get(key) {
        try {
            const get_query = `SELECT updated_on, data FROM nescache WHERE key = %L AND (expiration > NOW() OR expiration IS NULL);`;
            const res = await this.db.env.ro.query(get_query, [key], true);
            return res?.rows?.length > 0 ? res.rows[0] : null;
        } catch (error) {
            console.log(error);
        }
    }

    async get_wildcard(key, json = false) {
        try {
            const get_query = `SELECT updated_on, data${json ? "::jsonb" : ""} FROM nescache WHERE key LIKE %L AND (expiration > NOW() OR expiration IS NULL);`;
            const res = await this.db.env.ro.query(get_query, [`${key}%`]);
            return res;
        } catch (error) {
            console.log(error);
        }
    }

    async set(key, value, expiration = 0) {
        const expirationValue =
            expiration === 0
                ? "NULL"
                : `NOW() + INTERVAL '${parseInt(expiration)} SECONDS'`;
        const set_query = `
            INSERT INTO nescache (key, expiration, data) VALUES(%L, ${expirationValue}, %L)
            ON CONFLICT(key) DO UPDATE SET updated_on = NOW(), data = %L, expiration = ${expirationValue};`;
        const val_json = JSON.stringify(value);

        return this.db.env.rw.query(set_query, [key, val_json, val_json]);
    }

    async unset(key) {
        const delete_query = `DELETE FROM nescache WHERE key = %L;`;
        return this.db.env.rw.query(delete_query, [key]);
    }
};
