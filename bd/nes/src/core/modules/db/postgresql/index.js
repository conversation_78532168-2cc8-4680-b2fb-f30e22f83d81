"use strict";
const pgp = require("pg-promise")({
    noWarnings: true,
});
const pgesc = require("pg-escape");
const pgqs = require("pg-query-stream");
const { v4: uuid } = require("uuid");
const Writable = require("stream").Writable;
const TransactionClass = require("@db/postgresql/transaction");

module.exports = class PostgreSQL {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.index = 3;
    }

    get_transaction_id(type, date = null) {
        if (!type && !date) return uuid();
        else {
            this.index++;
            return `${type[0].toUpperCase()}${type[1].toUpperCase()}23-${this.index.toString().padStart(4, 0)}`;
        }
    }
    column_type(type_id) {
        switch (type_id) {
            case 16:
                return "bool";
            case 23:
                return "int";
            case 1082:
                return "date";
            case 1083:
                return "time";
            case 1114:
                return "datetime";
            case 1700:
                return "decimal";
            default:
                // 1043(text), 25(json), 1007(text-array), 1015(text), 1042(text), 1182(date-array), 1009(text)
                return "text";
        }
    }

    /**
     * Converts pg-escape style SQL placeholders to pg-promise style and formats the SQL.
     * @param {string} sql - The SQL query string which may contain pg-escape or pg-promise style placeholders
     * @param {Array} params - Array of parameter values
     * @returns {string} - The formatted SQL string with proper pg-promise placeholders
     */
    async parseSQLUsingPGP(sql, params) {
        if (!sql) return sql;

        let modifiedSql = sql;
        let paramCount = 1;

        // Keep replacing first occurrence until none left
        while (modifiedSql.match(/%[Ls]/)) {
            modifiedSql = modifiedSql.replace(/%[Ls]/, `$${paramCount}`);
            paramCount++;
        }

        try {
            modifiedSql = pgp.as.format(modifiedSql, params);
            return await this.query(modifiedSql);
        } catch (err) {
            console.error("Error formatting SQL:", err);
            throw err;
        }
    }
    async init(config) {
        this.config = config;
        pgp.pg.types.setTypeParser(1700, parseFloat);
        pgp.pg.defaults.query_timeout = this.shared.config.limits.timeout; // timeout every query after X ms seconds
        this.pool = pgp(this.config);
        const c1 = await this.query(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_type = 'BASE TABLE' AND table_schema = 'public'"
        );
        if (
            !(c1.length == 1 && "count" in c1[0] && parseInt(c1[0].count) > 0)
        ) {
            console.error("DB ERR");
            return;
        }
        if (config.source == "env") {
            const c2 = await this.query("SELECT COUNT(*) FROM form_user");
            if (
                !(
                    c2.length == 1 &&
                    "count" in c2[0] &&
                    parseInt(c2[0].count) > 0
                )
            ) {
                console.error("DB DATA ERR");
                return;
            }
        }
    }

    /*
        try {
            const res = await this.db.env.rw.query("SELECT id, code, auto_name FROM form_therapy ORDER BY auto_name DESC LIMIT 3;");
            return res;
        } catch (e) {
            console.error(e);
        }
    */
    async query(sql, params = [], raw = false) {
        try {
            const escsql = pgesc(sql, ...params);
            // Uncomment line below to log all sql queries to stdout.
            // console.log(escsql + "\n");
            const result = await this.pool.result(escsql).catch((e) => {
                throw e;
            });
            return raw ? result : result.rows;
        } catch (e) {
            console.error(e);
            let position = null;
            const err = {
                error: e,
                sql: sql,
            };
            position = parseInt(e.position);
            if (!isNaN(position)) {
                const { query } = e;
                const errorquery =
                    query.substring(0, position) +
                    "^" +
                    query.substring(position);
                err["error_text"] = errorquery.substring(
                    position - 40 > 0 ? position - 40 : 0,
                    position + 40 < errorquery.length
                        ? position + 40
                        : errorquery.length
                );
            }
            throw err;
        }
    }

    /*
      Given a SELECT query that returns just 1 ARRAY_AGG column in 1 row,
        parse the unique values and return them as object keys:

        SELECT
            ARRAY_AGG(user_id)
        FROM (
            SELECT
                user_id
            FROM
                form_patient
            WHERE (user_id IS NOT NULL)
            AND(archived IS NULL
                OR archived = FALSE)
            AND(deleted IS NULL
                OR deleted = FALSE)
        ORDER BY
            id DESC
        LIMIT 10);

        => {
          "27": true,
          "45": true,
          "48": true
          ...
        }
    */
    async query_agg(sql, params = []) {
        const rows = await this.query(sql, params);
        if (Array.isArray(rows)) {
            const row = rows[0];
            if (row && "array_agg" in row && Array.isArray(row.array_agg)) {
                return row.array_agg.reduce((acc, cur) => {
                    acc[cur] = true;
                    return acc;
                }, {});
            }
        }
        return [];
    }

    async reconnect() {
        pgp.end();
        await new Promise((tmo, _) => {
            setTimeout(tmo, 3000);
        }); // wait a few seconds
        await this.init(this.config);
    }

    /*
        try {
            let processor = (row) => {
                console.log(row);
            }
            await this.db.env.rw.stream(processor, "SELECT * FROM form_patient ORDER BY auto_name;");
        } catch (e) {
            console.error(e);
        }
    */
    async stream(processor, sql, params = [], batch = 100) {
        try {
            const qs = new pgqs(pgesc(sql, ...params), [], {
                highWaterMark: batch * 2,
                batchSize: batch,
            });
            const stream = this.pool.stream;
            const stream_read = this.stream_read;

            return await new Promise((resolve, reject) => {
                stream(qs, async (data_stream) => {
                    resolve(
                        (await stream_read(processor, data_stream, batch)) ||
                            "SUCCESS"
                    );
                }).catch((e) => {
                    if (
                        "detail" in e &&
                        e.detail.includes("row versions that must be removed")
                    ) {
                        // do nothing;
                    } else {
                        console.log(e);
                        reject(e);
                    }
                });
            });
        } catch (e) {
            throw {
                error: e,
                sql: sql,
            };
        }
    }

    // used by this.stream
    async stream_read(processor, stream, batch) {
        const buf = {};
        let writing = false;
        const batch_write = async (data, cb) => {
            while (writing) {
                // no need to check this more frequently
                await new Promise((tmo, _) => {
                    setTimeout(tmo, 100);
                });
            }
            if (!data || Object.keys(data).length === 0) {
                for (const k of Object.keys(buf)) {
                    const write_buf = buf[k];
                    delete buf[k];
                    writing = true;
                    await processor.write(write_buf);
                    writing = false;
                }
            } else {
                const row = processor.parse(data);
                if (row) {
                    if (row.type in buf) {
                        buf[row.type].push(row);
                        if (buf[row.type].length >= batch) {
                            const write_buf = buf[row.type];
                            delete buf[row.type];
                            writing = true;
                            await processor.write(write_buf);
                            writing = false;
                        }
                    } else {
                        buf[row.type] = [row];
                    }
                }
            }
            cb();
        };

        const writer = new Writable({
            objectMode: true,
            write: (obj, _, cb) => {
                batch_write(obj, cb);
            },
        });
        return await new Promise((resolve, _) => {
            stream.on("end", () => {
                batch_write(null, resolve);
            });
            stream.on("error", (e) => {
                // we want to finish the async/await no matter what
                if (
                    "detail" in e &&
                    e.detail.includes("row versions that must be removed")
                )
                    console.log("!!! PSQL DISCONNECTED !!!");
                batch_write(null, () => {
                    if (
                        "detail" in e &&
                        e.detail.includes("row versions that must be removed")
                    ) {
                        resolve("RESTART");
                    }
                });
            });
            stream.pipe(writer);
        });
    }

    transaction(ctx) {
        return new TransactionClass(this.nes, ctx);
    }

    /**
     * Sets up a trigger for a specified table and channel.
     * @param {string} tableName - The name of the table on which the trigger is to be set up.
     * @param {string} channel - The channel name used for notifications.
     */
    async setupTrigger(tableName, channel) {
        const triggerFunction = `
            CREATE OR REPLACE FUNCTION notify_${channel}()
            RETURNS trigger AS $$
            BEGIN
            PERFORM pg_notify('${channel}', NEW.id::text);
            RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `;

        const dropTrigger = `
            DROP TRIGGER IF EXISTS trigger_${channel} ON ${tableName};
        `;

        const createTrigger = `
            CREATE TRIGGER trigger_${channel}
            AFTER INSERT OR UPDATE ON ${tableName}
            FOR EACH ROW
            EXECUTE FUNCTION notify_${channel}();
        `;

        try {
            await this.db.env.rw.query(triggerFunction);
            await this.db.env.rw.query(dropTrigger);
            await this.db.env.rw.query(createTrigger);
        } catch (e) {
            console.error(
                `Error setting up triggers table listener on ${tableName}. Error: ${e.message} Stack: ${e.stack}`
            );
            throw e;
        }
    }

    subscribe_trigger_function = async (triggerChannel, triggerFunction) => {
        this.pool
            .connect()
            .then((obj) => {
                obj.client.on("notification", (_data) => {
                    triggerFunction();
                });
                return obj.none("LISTEN $1:name", triggerChannel);
            })
            .catch((error) => {
                console.error("Error setting up trigger listener:", error);
            });
    };
};
