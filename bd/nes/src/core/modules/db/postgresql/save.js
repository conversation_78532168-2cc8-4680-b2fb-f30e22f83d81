"use strict";
const _ = require("lodash");
const pgp = require("pg-promise")();
const uuid = require("uuid").v4;
const moment = require("moment");
const SecurityPermsClass = require("@core/modules/security/perms");

module.exports = class SQLSaveClass {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.dsl = nes.modules.dsl;
        this.db = nes.modules.db;
        this.crud = nes.shared.crud;
        this.transforms = nes.shared.transforms;
        this.utils = nes.shared.utils;
        this.tablePrefix = "tmpTable_";
        this.shared = nes.shared;
        this.securityPerms = new SecurityPermsClass(nes);
        this.defaulFields = [
            "created_by",
            "updated_by",
            "created_on",
            "updated_on",
            "archived",
        ];
    }

    get_field(formName, field) {
        if (
            this.shared.DSL[formName] &&
            this.shared.DSL[formName].fields[field]
        )
            return _.cloneDeep(this.shared.DSL[formName].fields[field]);
        else return false;
    }

    should_save_form(formName) {
        if (formName.startsWith("gr_") || formName.startsWith("sf_"))
            return true;
        return (
            this.shared.DSL[formName] && this.shared.DSL[formName].model.save
        );
    }

    should_save_field(fieldObj) {
        if (typeof fieldObj?.model?.save === "boolean")
            return fieldObj.model.save;
        return true;
    }

    exists_in_dsl(formName, field) {
        return (
            this.shared.DSL[formName] && this.shared.DSL[formName].fields[field]
        );
    }

    check_form_cond(formName, data, ctx, op, newId = "") {
        const user = ctx.user;
        let sql = "";
        if (formName == "sec_assign") {
            data = this.check_sec_assign_allow_sync(formName, data);
        }
        if (this.dsl.get_type(newId) === "string" && newId !== "") {
            // should only be true for INSERTS
            [data, sql] = this.insert_id_by_syncmode(
                formName,
                user,
                data,
                newId
            );
        }
        // security rules and custom locks
        data = this.check_perms(ctx, formName, data, op);
        // DSL fields perms
        data = this.can_upsert(ctx, formName, data, op);
        return [data, sql];
    }

    check_perms(ctx, formName, data, op) {
        return this.securityPerms.runFormPerms(ctx, formName, data, op);
    }

    can_upsert(ctx, formName, data, op) {
        if (this.auth.can_upsert_form(ctx, formName, op)) return data;
        return this.auth.can_upsert_fields(ctx, formName, data, op);
    }

    check_sec_assign_allow_sync(formName, data) {
        if (formName == "sec_assign") {
            const { user_id, sec_group, role, rule } = data;
            const syncModeMixedstartId =
                this.shared.config.limits.sync_mode.mix_start_id;
            if (
                user_id == null &&
                (sec_group < syncModeMixedstartId || sec_group == null) &&
                (rule < syncModeMixedstartId || rule == null) &&
                (role < syncModeMixedstartId || role == null)
            ) {
                data["allow_sync"] = "Yes";
                data["active"] = "Yes";
            }
        }
        return data;
    }

    /**
     * Description.
     ** generate auto_name and adds to data
     update form set auto_name concat([])
     update form set auto_name =
     '{start_time} : {dose_given} over {dose_time} hours'
     ['name']
     */
    get_pg_id(formName = "") {
        if (typeof formName !== "string") {
            throw new TypeError(`Expected string got: ${typeof formName}`);
        }
        const id = formName !== "" ? formName : this.get_var_name();
        return id;
    }

    get_next_number(series, date = null) {
        const seqId = this.get_var_name();
        const qstr = `select crx_next_number('${series}'${date ? `, '${date}')` : ")"} INTO ${seqId};`;
        return [qstr, [seqId]];
    }

    get_var_name() {
        return `psqlVar_${uuid().replaceAll("-", "_")}_eVar`;
    }

    get_insert_sql(formName, insertData, parentObj, ctx) {
        /**
         * DO NOT REMOVE THE FOLLOWING LINE, UNLESS YOU ARE
         * WILLING TO WALK THE DEPTHS OF 7 HELLS TO FIGURE
         * OUT ISSUES ARISING OF JS REFERENCES
         */
        let data = JSON.parse(JSON.stringify(insertData));
        const user = ctx.user;
        const columnArray = [];
        let idx = [];
        const grTables = [];
        const sfTables = [];
        let insertDataArray = [];
        const placeHolders = [];
        let paramterizedSql = "";
        let tableName = "";
        let paramCount =
            parentObj && parentObj.paramCount ? parentObj.paramCount || 0 : 0;
        if (!this.should_save_form(formName))
            return [
                paramterizedSql,
                insertDataArray,
                paramCount,
                tableName,
                idx,
            ];
        const newId = this.get_pg_id();
        [data, paramterizedSql] = this.check_form_cond(
            formName,
            data,
            ctx,
            "create",
            newId
        );
        if (
            !(
                parentObj &&
                parentObj.formName &&
                parentObj.formName.startsWith("gr_")
            )
        ) {
            data["created_on"] = moment().format("YYYY-MM-DD HH:mm:ss");
            if (typeof user === "object" && Object.keys(user).length > 0) {
                data["created_by"] = user.id;
            }
            if (!(parentObj && parentObj.gr)) {
                tableName = `${this.tablePrefix}${this.db.env.rw.get_transaction_id().replaceAll("-", "_")}`;
                // for transaction class maybe we add two more columns so we can pass same table to rest of the queries and use columns to get different tables id
                paramterizedSql = `${paramterizedSql}CREATE TEMP TABLE IF NOT EXISTS ${tableName} (id int);`;
            }
            idx.push({
                tempTble: tableName || "gr_",
                id: newId,
                formName: formName,
                op: "insert",
                data: data,
            });
            paramterizedSql = `${paramterizedSql}INSERT INTO form_${formName}`;
        }

        for (const [field, origValue] of Object.entries(data)) {
            let value = origValue;
            const fieldObj = this.get_field(formName, field);
            // check if field exists in the dsl fields
            if (
                !this.defaulFields.includes(field) &&
                !(
                    this.exists_in_dsl(formName, field) &&
                    this.should_save_field(fieldObj)
                )
            ) {
                continue;
            }
            if (
                typeof fieldObj.model.source === "string" &&
                fieldObj.model.type === "subform"
            ) {
                if (value == null) {
                    console.log(
                        `falsy value for form ${formName} field ${field} ---------- skipping`
                    );
                    // TODO: ask shoaib if we should skip cause throwing error is no go
                    continue;
                }
                // subform
                if (fieldObj.model.source.includes("{")) {
                    let tempField = "";
                    tempField = fieldObj.model.source
                        .split("{")
                        .pop()
                        .split("}")[0]; // get the temp field name from within the {}

                    if (
                        !(
                            data[tempField] &&
                            this.dsl.get_type(data[tempField]) === "string"
                        )
                    ) {
                        console.error(
                            `Error making INSERT Query Incorrect Value for dynamic key ${tempField} for form ${formName} on field ${field}`
                        );
                        throw new Error(
                            `Incorrect Value for dynamic key expected string got ${typeof value}`
                        );
                    }
                    // remake the source form name which exists in the dsl
                    fieldObj.model.source = fieldObj.model.source
                        .replace(tempField, data[tempField])
                        .replace(/[^A-Za-z0-9_]/gi, "")
                        .toLowerCase();
                }
                if (this.dsl.get_type(value) !== "array") {
                    console.error(
                        `Error making INSERT Query for form ${formName} on field ${field}`
                    );
                    throw new Error(
                        `Incorrect Value expected array got ${typeof value}`
                    );
                }
                sfTables.push({
                    formName: `${fieldObj.model.source}`,
                    data: value,
                    linkTable: {
                        formName: `sf_form_${formName}_to_${fieldObj.model.source}`,
                        parentKey: `form_${formName}_fk`,
                        parentValue: newId,
                        childKey: `form_${fieldObj.model.source}_fk`,
                        sf: true,
                    },
                });
            } else if (
                typeof fieldObj.model.source === "string" &&
                fieldObj.model.multi
            ) {
                // gerund
                if (!value) {
                    console.log(
                        `falsy value for form ${formName} field ${field} ---------- skipping`
                    );
                    continue;
                }
                if (
                    this.dsl.get_type(value) !== "array" &&
                    this.dsl.get_type(value) !== "number"
                ) {
                    console.error(
                        `Error making INSERT Query for form ${formName} on field ${field}`
                    );
                    throw new Error(
                        `Incorrect Value expected array|number got ${typeof value}`
                    );
                }
                if (this.dsl.get_type(value) === "number") value = [value];
                grTables.push({
                    formName: `gr_form_${formName}_${field}_to_${fieldObj.model.source}_id`,
                    parentKey: `form_${formName}_fk`,
                    parentValue: newId,
                    childKey: `form_${fieldObj.model.source}_fk`,
                    childIdx: value,
                    gr: true,
                });
            } else {
                if (
                    ["decimal", "int"].includes(fieldObj.model.type) &&
                    !value
                ) {
                    if (this.dsl.get_type(fieldObj.model.source) === "string")
                        value = null;
                    else if (value !== 0) value = null;
                    else value = 0;
                }
                value = this.fix_falsy(value, fieldObj, field);
                value = this.check_source(value, fieldObj);
                // if single value in radio and mode.multi true then we need to cast it to array
                if (fieldObj.model.type == "json") {
                    if (value && typeof value === "object")
                        value = JSON.stringify(value);
                } else if (
                    this.dsl.get_type(value) === "string" &&
                    fieldObj.model.multi
                ) {
                    value = [value];
                }
                if (
                    value &&
                    typeof value === "object" &&
                    value._meta &&
                    value._meta === "psqlFun" &&
                    value.sqlString &&
                    value.sqlString !== ""
                ) {
                    columnArray.push(field);
                    placeHolders.push(value.sqlString);
                } else if (
                    (typeof fieldObj.model?.source === "string" &&
                        this.dsl.get_type(value) === "string" &&
                        fieldObj.model?.type === "int") ||
                    (typeof value === "string" && value.startsWith("psqlVar_"))
                ) {
                    columnArray.push(field);
                    placeHolders.push(value);
                } else {
                    paramCount++;
                    insertDataArray.push(value);
                    columnArray.push(field);
                    placeHolders.push(`$${paramCount}`);
                }
            }
        }
        if (insertDataArray.length > 0) {
            paramterizedSql = `${paramterizedSql}("${columnArray.join('","')}") VALUES (${placeHolders.join(",")}) RETURNING id INTO ${newId};`;
        }
        if (parentObj && parentObj.sf) {
            if (
                !(
                    parentObj.formName &&
                    parentObj.parentKey &&
                    parentObj.childKey &&
                    parentObj.parentValue
                )
            ) {
                console.error(`Error making INSERT Query for sf ${formName}`);
                throw new Error(
                    `Type Error: got falsy parameter ${typeof value}`
                );
            }
            paramterizedSql = `${paramterizedSql} INSERT INTO ${parentObj.formName} (${parentObj.parentKey},${parentObj.childKey}) VALUES (${parentObj.parentValue},${newId});`;
        } else if (parentObj && parentObj.gr) {
            // gerund
            if (
                this.dsl.get_type(parentObj.childIdx) !== "null" &&
                this.dsl.get_type(parentObj.childIdx) !== "array"
            ) {
                console.error(`Error making INSERT Query for gr ${formName}`);
                throw new Error(
                    `Incorrect Value expected array got ${typeof parentObj.childIdx}`
                );
            }
            for (const id of parentObj.childIdx) {
                if (this.dsl.get_type(parentObj.parentValue) !== "string") {
                    paramterizedSql = `${paramterizedSql} INSERT INTO ${formName} (${parentObj.parentKey},${parentObj.childKey}) SELECT $${++paramCount},$${++paramCount} WHERE NOT EXISTS (SELECT * from ${formName} WHERE ${parentObj.parentKey} = $${--paramCount} AND ${parentObj.childKey} = $${++paramCount});`;
                    insertDataArray.push(parentObj.parentValue);
                } else {
                    paramterizedSql = `${paramterizedSql} INSERT INTO ${formName} (${parentObj.parentKey},${parentObj.childKey}) SELECT ${parentObj.parentValue},$${++paramCount} WHERE NOT EXISTS (SELECT * from ${formName} WHERE ${parentObj.parentKey} = ${parentObj.parentValue} AND ${parentObj.childKey} = $${paramCount});`;
                }
                insertDataArray.push(id);
            }
        }
        for (const childObj of grTables) {
            let childSqlArray = "";
            let childParamArray = [];
            let t = "";
            let id = [];
            [childSqlArray, childParamArray, paramCount, t, id] =
                this.get_insert_sql(
                    childObj.formName,
                    {},
                    { ...childObj, paramCount },
                    ctx
                );
            paramterizedSql = `${paramterizedSql} ${childSqlArray}`;
            if (id.length > 0) idx = idx.concat(id);
            insertDataArray = insertDataArray.concat(childParamArray);
        }
        for (const childObj of sfTables) {
            for (const entry of childObj.data) {
                let childSqlArray = "";
                let childParamArray = [];
                let t = "";
                let id = [];
                [childSqlArray, childParamArray, paramCount, t, id] =
                    this.get_insert_sql(
                        childObj.formName,
                        entry,
                        { ...childObj.linkTable, paramCount },
                        ctx
                    );
                paramterizedSql = `${paramterizedSql} ${childSqlArray}`;
                if (id.length > 0) idx = idx.concat(id);
                insertDataArray = insertDataArray.concat(childParamArray);
            }
        }
        if (tableName !== "") {
            paramterizedSql = `${paramterizedSql} INSERT INTO ${tableName} (id) VALUES (${newId});`;
        } else {
            paramterizedSql = `${paramterizedSql} `;
        }
        // TODO see if we can remove returning the tableName
        return [paramterizedSql, insertDataArray, paramCount, tableName, idx];
    }

    get_update_sql(formName, updateData, id, parentObj, ctx) {
        /**
         * DO NOT REMOVE THE FOLLOWING LINE, UNLESS YOU ARE
         * WILLING TO WALK THE DEPTHS OF 7 HELLS TO FIGURE
         * OUT ISSUES ARISING OUT OF JS REFERENCES
         *
        return [sqlString, updateDataArray, paramCount, idx];
         */
        let data = JSON.parse(JSON.stringify(updateData));
        const user = ctx.user;
        let sqlString = "";
        let updateDataArray = [];
        let idx = [];
        let paramCount =
            parentObj && parentObj.paramCount ? parentObj.paramCount || 0 : 0;
        if (!this.should_save_form(formName))
            return [sqlString, updateDataArray, paramCount, idx];
        if (typeof id === "string") {
            if (id === "") throw new TypeError("Invalid ID empty string");
        } else if (isNaN(parseInt(id))) {
            throw new TypeError(
                `Invalid type of id expected number|string got ${typeof id}`
            );
        } else {
            id = parseInt(id);
        }
        data = this.check_form_cond(formName, data, ctx, "update")[0];
        const grTables = [];
        const sfTables = [];
        sqlString = `UPDATE form_${formName} SET `;
        idx.push({
            id: id,
            formName: formName,
            op: "update",
            data: data,
        });
        if (
            !(
                parentObj &&
                parentObj.formName &&
                parentObj.formName.startsWith("gr_")
            )
        ) {
            // do the updated on here
            data["updated_on"] = moment().format("YYYY-MM-DD HH:mm:ss");
            if (typeof user === "object" && Object.keys(user).length > 0)
                data["updated_by"] = user.id;
        } else {
            sqlString = `DELETE FROM ${formName} `;
        }
        if ("auto_name" in data) delete data["auto_name"];
        for (const [field, origValue] of Object.entries(data)) {
            let value = origValue;
            const fieldObj = this.get_field(formName, field);
            if (
                !this.defaulFields.includes(field) &&
                !(
                    this.exists_in_dsl(formName, field) &&
                    this.should_save_field(fieldObj)
                )
            ) {
                continue;
            }

            if (
                typeof fieldObj.model.source === "string" &&
                fieldObj.model.type === "subform"
            ) {
                // subform
                // data sanitization check for MULTI
                if (fieldObj.model.source.includes("{")) {
                    let tempField = "";
                    tempField = fieldObj.model.source
                        .split("{")
                        .pop()
                        .split("}")[0]; // get the temp field name from within the {}

                    if (
                        !(
                            data[tempField] &&
                            this.dsl.get_type(data[tempField]) === "string"
                        )
                    ) {
                        console.error(
                            `Error making INSERT Query Incorrect Value for dynamic key ${tempField} for form ${formName} on field ${field}`
                        );
                        throw new Error(
                            `Incorrect Value for dynamic key expected string got ${typeof value}`
                        );
                    }
                    // remake the source form name which exists in the dsl
                    fieldObj.model.source = fieldObj.model.source
                        .replace(tempField, data[tempField])
                        .replace(/[^A-Za-z0-9_]/gi, "")
                        .toLowerCase();
                }
                if (this.dsl.get_type(value) !== "array") {
                    console.error(
                        `Error making UPDATE Query for form ${formName} on field ${field}`
                    );
                    throw new Error(
                        `Incorrect Value expected array got ${typeof value}`
                    );
                }
                sfTables.push({
                    formName: `${fieldObj.model.source}`,
                    data: value,
                    field: field,
                    delete: data._meta?.[field]?.delete || [],
                    linkTable: {
                        formName: `sf_form_${formName}_to_${fieldObj.model.source}`,
                        parentKey: `form_${formName}_fk`,
                        parentValue: id,
                        childKey: `form_${fieldObj.model.source}_fk`,
                        sf: true,
                    },
                });
            } else if (
                typeof fieldObj.model.source === "string" &&
                fieldObj.model.multi
            ) {
                // gerund
                if (!value && !this.dsl.get_type(value) === "array") {
                    console.log(
                        `falsy value for form ${formName} field ${field} ---------- skipping gr loop`
                    );
                    continue;
                }
                if (
                    this.dsl.get_type(value) !== "array" &&
                    this.dsl.get_type(value) !== "number"
                ) {
                    throw new Error(
                        `Incorrect Value expected array|number got ${typeof value}`
                    );
                }
                if (this.dsl.get_type(value) === "number") value = [value];
                value = value.filter(Boolean);
                grTables.push({
                    formName: `gr_form_${formName}_${field}_to_${fieldObj.model.source}_id`,
                    parentKey: `form_${formName}_fk`,
                    parentValue: id,
                    childKey: `form_${fieldObj.model.source}_fk`,
                    childIdx: value,
                    gr: true,
                });
                // add in validation for min, max and empty json and string
            } else {
                if (
                    ["decimal", "int"].includes(fieldObj.model.type) &&
                    !value
                ) {
                    if (this.dsl.get_type(fieldObj.model.source) === "string")
                        value = null;
                    else if (value !== 0) value = null;
                    else value = 0;
                }
                value = this.fix_falsy(value, fieldObj, field);
                value = this.check_source(value, fieldObj);
                if (fieldObj.model.type == "json") {
                    if (value && typeof value === "object")
                        value = JSON.stringify(value);
                } else if (
                    this.dsl.get_type(value) === "string" &&
                    fieldObj.model.multi
                ) {
                    value = [value];
                }
                if (
                    value &&
                    typeof value === "object" &&
                    value._meta &&
                    value._meta === "psqlFun" &&
                    value.sqlString &&
                    value.sqlString !== ""
                ) {
                    sqlString = `${sqlString}"${field}" = ${value.sqlString},`;
                } else if (
                    typeof value === "string" &&
                    value.startsWith("psqlVar_")
                ) {
                    sqlString = `${sqlString}"${field}" = ${value},`;
                } else {
                    paramCount++;
                    sqlString = `${sqlString}"${field}" = $${paramCount},`;
                    updateDataArray.push(value);
                }
            }
        }
        if (updateDataArray.length > 0) {
            sqlString = `${sqlString.slice(0, -1) + ""} WHERE id = ${typeof id === "number" ? `$${++paramCount}` : id};`;
            if (typeof id === "number") updateDataArray.push(id);
        }
        if (parentObj && parentObj.sf && data.archived) {
            if (
                !(
                    parentObj.formName &&
                    parentObj.parentKey &&
                    parentObj.childKey &&
                    parentObj.parentValue
                )
            ) {
                console.error(`Error making Update Query for sf ${formName}`);
                throw new Error(`Type Error: got falsy parameter in parentObj`);
            }
            sqlString = `${sqlString} UPDATE ${parentObj.formName} SET archive = true, delete = true WHERE ${parentObj.parentKey} = $${++paramCount} AND ${parentObj.childKey} = $${++paramCount};`;
            updateDataArray.push(parentObj.parentValue);
            updateDataArray.push(id);
        } else if (parentObj && parentObj.gr) {
            // gerund
            let childSqlArray = "";
            let childDataArray = [];
            let t = "";
            let id = [];
            if (
                this.dsl.get_type(parentObj.childIdx) !== "null" &&
                this.dsl.get_type(parentObj.childIdx) !== "array"
            ) {
                console.error(`Error making Update Query for gr ${formName}`);
                throw new Error(
                    `Incorrect Value expected array got ${typeof parentObj.childIdx}`
                );
            }
            // Delete the ones in the table but not in the gr array
            updateDataArray.push(parentObj.parentValue);
            sqlString = `${sqlString} WHERE ${parentObj.parentKey} = $${++paramCount}`;
            if (parentObj.childIdx?.length > 0)
                sqlString = `${sqlString} AND ${parentObj.childKey} NOT IN (${parentObj.childIdx
                    .map((o) => {
                        updateDataArray.push(o);
                        return `$${++paramCount}`;
                    })
                    .join(",")});`;
            if (!sqlString.endsWith(";")) sqlString = sqlString + ";";
            // Insert the remaining
            [childSqlArray, childDataArray, paramCount, t, id] =
                this.get_insert_sql(
                    formName,
                    {},
                    { ...parentObj, paramCount },
                    ctx
                );
            sqlString = `${sqlString} ${childSqlArray}`;
            if (id.length > 0) idx = idx.concat(id);
            updateDataArray = updateDataArray.concat(childDataArray);
        }
        for (const childObj of grTables) {
            let childSqlArray = "";
            let childDataArray = [];
            [childSqlArray, childDataArray, paramCount] = this.get_update_sql(
                childObj.formName,
                {},
                id,
                { ...childObj, paramCount },
                ctx
            );
            sqlString = `${sqlString} ${childSqlArray}`;
            updateDataArray = updateDataArray.concat(childDataArray);
        }
        for (const childObj of sfTables) {
            for (const entry of childObj.data) {
                let childSqlArray = "";
                let id = [];
                let t = "";
                let childDataArray = [];
                if (entry.id) {
                    // if the subform is updated then we need to get the old data
                    // of the subform from parent's __oldData for perms check later on
                    if (this.shared.perms.includes(childObj.formName)) {
                        const oldData = _.head(
                            data.__oldData[childObj.field]?.filter(
                                (o) => parseInt(o.id) === parseInt(entry.id)
                            )
                        );
                        entry.__oldData = oldData;
                    }
                    if (
                        childObj.delete.includes(entry.id) ||
                        entry?._meta?.delete === entry.id
                    ) {
                        entry["archived"] = true;
                        entry["deleted"] = true;
                    }
                    [childSqlArray, childDataArray, paramCount, id] =
                        this.get_update_sql(
                            childObj.formName,
                            entry,
                            entry.id,
                            { ...childObj.linkTable, paramCount },
                            ctx
                        );
                } else
                    [childSqlArray, childDataArray, paramCount, t, id] =
                        this.get_insert_sql(
                            childObj.formName,
                            entry,
                            { ...childObj.linkTable, paramCount },
                            ctx
                        );
                sqlString = `${sqlString} ${childSqlArray}`;
                if (id.length > 0) idx = idx.concat(id);
                updateDataArray = updateDataArray.concat(childDataArray);
            }
        }
        sqlString = `${sqlString} `;
        return [sqlString, updateDataArray, paramCount, idx];
    }

    check_source(value, fieldObj) {
        if (
            this.dsl.get_type(fieldObj.model?.source) === "array" &&
            this.dsl.get_type(value) === "array"
        ) {
            if (value.length === 0) {
                return null;
            }
            for (const val of value) {
                if (!fieldObj.model?.source.includes(val))
                    throw TypeError(
                        `Expected one of the following ${fieldObj.model?.source.join(" | ")} got ${val}`
                    );
            }
        }
        if (
            this.dsl.get_type(fieldObj.model?.source) === "array" &&
            this.dsl.get_type(value) === "string" &&
            !!value &&
            !fieldObj.model?.source.includes(value)
        )
            throw TypeError(
                `Expected one of the following ${fieldObj.model?.source.join(" | ")} got ${value}`
            );
        return value;
    }

    fix_falsy(value, fieldObj, field) {
        //source:['date', 'datetime', 'decimal', 'image', 'int', 'json', 'password', 'subform', 'text', 'time']
        if (!value) {
            if (
                (fieldObj.model?.dynamic?.source &&
                    typeof fieldObj.model?.dynamic?.source === "string" &&
                    fieldObj.model?.dynamic?.source !== "") ||
                field == "deleted" ||
                field == "archived" ||
                ["date", "datetime"].includes(fieldObj.model?.type) ||
                this.dsl.get_type(fieldObj.model?.source) === "string" ||
                ["image", "json", "xml", "password", "text", "time"].includes(
                    fieldObj.model?.type
                )
            ) {
                value = null;
            }
        }
        return value;
    }

    async update_form(formName, data, id, ctx, is_test = false) {
        let sqlUpdateFun = "";
        let sql = "";
        let idx = [];
        let count = 0;
        let params = [];
        // get field level access
        if (!data) {
            throw new Error("Unauthorized");
        }
        // check the feild access of every field
        [sqlUpdateFun, params, count, idx] = this.get_update_sql(
            formName,
            data,
            id,
            null,
            ctx
        );
        sql = idx
            .map((id) => {
                if (id !== "") return `DECLARE ${id} int;`;
            })
            .join("");
        sqlUpdateFun = `DO $tag$ ${sql !== "" ? `BEGIN ${sql}` : ""} BEGIN ${sqlUpdateFun} ${sql !== "" ? `END;` : ""} END; $tag$;`;
        try {
            if (is_test) {
                console.warn("Performing test, rolling back transaction");
                sql = `${sql}; ROLLBACK;`;
            }
            console.time("updateTime");
            await this.db.env.rw.pool.multi(sqlUpdateFun, params);
            data.id = id;
            return data;
        } catch (error) {
            // let full = pgp.as.format(sqlUpdateFun, params);
            // console.log(params);
            // console.log(full);
            // console.log("<<<<<<<<<<<<<<<<full>>>>>>>>>>>>>>>>");
            throw new Error(error);
        } finally {
            console.timeEnd("updateTime");
        }
    }

    async create_form(formName, data, ctx, cson = {}, is_test = false) {
        let sql = "";
        let params = [];
        let sqlInsertFun = "";
        let result = {};
        let idx = [];
        let paramCount = 0;
        let tempTableName = "";
        // get field level access
        if (_.isEmpty(data)) throw new Error("Data cannot be empty");
        [sqlInsertFun, params, paramCount, tempTableName, idx] =
            this.get_insert_sql(formName, data, null, ctx);
        sql = idx
            .map((id) => {
                if (id !== "") return `DECLARE ${id} int;`;
            })
            .join("");
        sqlInsertFun = `DO $tag$ ${sql !== "" ? `BEGIN ${sql}` : ""} BEGIN ${sqlInsertFun} ${sql !== "" ? `END;` : ""} END; $tag$;`;
        try {
            console.time("insertTime");
            sql = pgp.helpers.concat([
                sqlInsertFun,
                `SELECT id from ${tempTableName};`,
                `DROP TABLE IF EXISTS ${tempTableName};`,
            ]);
            // let full = pgp.as.format(sql, params);
            // console.log(params);
            // console.log(full);
            // console.log("<<<<<<<<<<<<<<<<full>>>>>>>>>>>>>>>>");
            if (is_test) {
                console.warn("Performing test, rolling back transaction");
                sql = `${sql}; ROLLBACK;`;
            }
            result = await this.db.env.rw.pool.multi(sql, params);
            result = result[1][0];
        } catch (error) {
            console.error(error);
            if (error.code == 23505) {
                console.error(
                    `Unique key violation Method: INSERT Form: ${formName} Detail: ${error.detail}`
                );
                throw new Error(
                    `Duplicate Value on Unique Constraint: Check error logs for detail`
                );
            } else if (error.code == 22007) {
                console.error(
                    `Invalid time METHOD: INSERT Form: ${formName} Routine: ${error.routine}`
                );
                console.error(error);
                throw new Error(
                    `Invalid input syntax for type time:  Check error logs for detail`
                );
            } else {
                throw new Error(error);
            }
        } finally {
            console.timeEnd("insertTime");
        }
        return result;
    }

    insert_id_by_syncmode = (form, user, data, pgId) => {
        let sql = "";
        if (!user || !form) return data;
        try {
            const syncMode = this.shared.DSL[form]?.model.sync_mode ?? "none";
            const isAdmin = user?.is_admin;
            const allowSync = data?.allow_sync ?? false;
            const syncModeMixedstartId =
                this.shared.config.limits.sync_mode.mix_start_id;

            if (
                syncMode === "mixed" &&
                isAdmin === "Yes" &&
                allowSync === "Yes"
            ) {
                sql = `SELECT MAX(id) INTO ${pgId} FROM form_${form} WHERE id < ${syncModeMixedstartId}; IF ${pgId} IS NOT NULL THEN ${pgId} := ${pgId} + 1;ELSE ${pgId} := 1;END IF;`;
                data.id = pgId;
            }
        } catch (e) {
            console.error(e);
            throw new Error("Error Creating SQL see logs for details");
        }
        return [data, sql];
    };
};
