"use strict";
const PgBoss = require("pg-boss");
const crontime = require("cron-time-generator");

module.exports = class PGBOSS {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.pgb_queues = {};
        this.listeners = {};
    }

    async init() {
        if (this.shared.config.env?.DATABASE_URL) {
            this.pgboss = new PgBoss({
                application_name: "pgboss @ " + process.env["HOSTNAME"],
                connectionString: this.shared.config.env["DATABASE_URL"],
                deleteAfterDays: 90,
            });
        } else {
            console.error("No DATABASE_URL found in Environment. ");
            process.exit(-1);
        }

        this.pgboss.on("error", (err) => {
            console.log(
                "=============================PGBOSS ERROR===================================="
            );
            console.log(err); // TODO Replace it with NES Specific log
        });

        await this.pgboss.start();
        await this.startWorkers();
    }

    async startWorkers() {
        const enqueue = [];
        if (this.shared.config.queues.length < 1) {
            console.log("No Queues found");
            return;
        }
        try {
            for (const src of this.shared.config.queues) {
                const MC = require(src);
                const mo = new MC(this.nes);

                let funcNames = Object.getOwnPropertyNames(MC.prototype).filter(
                    (name) => typeof MC.prototype[name] === "function"
                );
                funcNames = [...funcNames, ...Object.keys(mo)];
                for (const worker of funcNames) {
                    if (!worker.toLowerCase().startsWith("worker_")) continue;
                    const workerFun = mo[worker];
                    if (!this.fx.isAsyncFunction(workerFun)) {
                        console.log(
                            worker + " must be async function: Skipping"
                        );
                        continue;
                    }
                    const queueName = worker.replace(/^worker_/, "");
                    enqueue.push(
                        this.pgboss.createQueue(queueName).then(() => {
                            console.log(
                                `       Attaching worker ${worker} to Queue ${queueName}`
                            );
                            return this.pgboss.work(
                                queueName,
                                async ([job]) => {
                                    console.log(
                                        `Calling from process id: ${process.pid} job id: ${job.id}`
                                    );
                                    return this.fx
                                        .adminNewContext()
                                        .then((ctx) => workerFun(ctx, job));
                                }
                            );
                        })
                    );
                }
            }
            if (enqueue.length > 0) {
                await Promise.all(enqueue);
                console.log("    Workers Initiated process id: " + process.pid);
            } else console.log("No workers found process id: " + process.pid);
        } catch (err) {
            console.error(err.message);
            console.error(err.stack);
        }
    }

    async jobSend(job, eventName, data, options = {}) {
        const queue = job + "_" + eventName;
        const id = await this.pgboss.send(queue, data, options);
        console.log(`creating job ${id} in queue ${queue}`);
    }

    async jobSchedule(job, eventName, job_data, cadence, job_opts = {}) {
        const queue = job + "_" + eventName;
        const cronline = this.getCron(cadence);
        await this.pgboss.schedule(queue, cronline, job_data, job_opts);
        console.log(`Added PG-Boss Scheduler for CRON ${cronline}`);
    }

    getCron(cadence) {
        if (typeof cadence === "number") {
            cadence = { minutes: cadence };
        }
        const intervals = ["minutes", "hours", "days", "months", "years"];
        const unit = intervals.find((e) => e in cadence);
        if (!unit) {
            throw new Error(
                "Wrong cadence unit expected one of:",
                intervals.join("|")
            );
        }
        return crontime.every(cadence[unit])[unit]();
    }
};
