"use strict";
const _ = require("lodash");
const { registerValidators } = require("@api/form/validators");
const { registerActionHandlers } = require("@api/form/actions");
const { registerSecurityRules } = require("@core/rules");

module.exports = class DSL {
    constructor(nes) {
        this.nes = nes;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.updatedAt = "";
        this.searchColumn = "search";
        this.rankColumn = "tsVectorRank";
        this.sourceFilterForms = nes.shared.sourceFilterForms;
        this.sourceIds = nes.shared.sourceIds;
        this.shared = nes.shared;
        this.nescache = nes.modules.nescache;
        this.shared.DSL_AutoInsert = {
            view: {
                transform: [],
                open: "edit",
                find: {
                    advanced: [],
                    basic: [],
                },
                manage: false,
                grid: {
                    fields: [],
                    filter: {},
                    sort: [],
                },
                actions: {},
                label: "",
                validate: [],
            },
            fields: {
                reviewed_on: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "",
                        format: "",
                        label: "Reviewed On",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "datetime",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [{ name: "NullReview" }],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [{ name: "NeedsReview" }],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                deleted: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "Deleted",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "boolean",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: false,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                archived: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "select",
                        format: "",
                        label: "Archived",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "boolean",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: false,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                created_by: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "Created By",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "int",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [
                            {
                                arguments: { fun: "current_user" },
                                name: "OnCreateSetField",
                            },
                        ],
                        if: {},
                        template: null,
                        source: "user",
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                id: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "ID",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "int",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                updated_on: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "Updated On",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "datetime",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [
                            {
                                arguments: { fun: "datetime_now" },
                                name: "OnUpdateSetField",
                            },
                        ],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                auto_name: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "Auto Name",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "text",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                change_type: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "",
                        format: "",
                        label: "Change Type",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "string",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: [null, "create", "read", "update", "delete"],
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                change_data: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "",
                        format: "",
                        label: "Change Request",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "json",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                created_on: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "Created On",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "datetime",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [
                            {
                                arguments: { fun: "datetime_now" },
                                name: "OnCreateSetField",
                            },
                        ],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                change_by: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "",
                        format: "",
                        label: "Change By",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "int",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: "user",
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                updated_by: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: true,
                        control: "",
                        format: "",
                        label: "Updated By",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "int",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [
                            {
                                arguments: { fun: "current_user" },
                                name: "OnUpdateSetField",
                            },
                        ],
                        if: {},
                        template: null,
                        source: "user",
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                change_on: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "",
                        format: "",
                        label: "Change On",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "datetime",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
                reviewed_by: {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "",
                        format: "",
                        label: "Reviewed By",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "int",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: "user",
                        default: null,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                },
            },
            model: {
                sections_group: [],
                name: [],
                indexes: { fulltext: [], unique: [], many: [] },
                save: true,
                prefill: {},
                transform: [{ name: "ChangeRequestDashboard" }],
                sections: {},
                bundle: [],
                transform_post: [],
                access: { write: [], read: [] },
                validate: [],
                transform_filter: [],
            },
        };

        this.MAX_COLLECTIONS = 5;

        // auto-reloading parameters
        this.reloading_dsl = false; // flag to prevent race conditions during slow this.reload()
        this.reloading_timer = 60; // seconds

        // only export specific columns from these tables
        //   specify using 'lowercase' only
        this.custom_field_export = {
            user: [
                "id",
                "external_id",
                "username",
                "role",
                "firstname",
                "lastname",
                "displayname",
                "job_title",
                "sales_code",
            ],
        };

        // treat these tables as obsolete:
        //   export and /api/view will not work for these
        this.force_non_save = [
            "additive_tpn",
            "assement_enteral",
            "assessment_assigned",
            "cpr_icd_master_list",
            "cpr_icd_patient",
            "ebridged_errors",
            "extract_job",
            "osmolarity",
            "pao_aat",
            "pao_antibiotic_drug",
            "pao_antibiotic",
            "pao_biologics",
            "pao_blood_thinners",
            "pao_catheter_care",
            "pao_chelation",
            "pao_chemotherapy",
            "pao_enteral",
            "pao_enzyme",
            "pao_factor_drug",
            "pao_factor",
            "pao_hbig",
            "pao_hepc_drug",
            "pao_hepc",
            "pao_hydration",
            "pao_immunotherapy",
            "pao_inotrope",
            "pao_ivig",
            "pao_methyl",
            "pao_nursing",
            "pao_pain_management",
            "pao_steroid",
            "pao_tnf",
            "pao_tpn",
            "pao_tysabri",
            "pao",
            "pot_aat",
            "pot_antibiotic",
            "pot_biologics",
            "pot_blood_thinners",
            "pot_catheter_care",
            "pot_chelation",
            "pot_chemotherapy",
            "pot_enteral",
            "pot_enzyme",
            "pot_factor",
            "pot_hbig",
            "pot_hepc",
            "pot_hydration",
            "pot_immunotherapy",
            "pot_inotrope",
            "pot_ivig",
            "pot_methyl",
            "pot_nursing",
            "pot_pain_management",
            "pot_steroid",
            "pot_tnf",
            "pot_tpn",
            "pot_tysabri",
            "pot",
        ];

        // no need to report on these tables:
        //   export will not work for these but /api/view will
        this.force_non_reportable = [
            "changeset",
            "coffeedsl",
            "communication_to",
            "communication",
            "cprexport",
            "ebridged_error",
            "letter_template",
            "letter",
            "password_policy",
            "permission_group",
            "user_role",
        ];

        // used to automatically create _index for fields with dictionary sources
        //   only use this for gradients/ranges, not unrelated values
        this.infer_index = {
            "0,1": [0, 1], // patient
            "0,1,2": [0, 1, 2], // no known usage
            "0,1,2,3": [0, 1, 2, 3], // assessment_tpn
            "0,1,2,3,4": [0, 1, 2, 3, 4], // no known usage
            "0,1,2,3,4,5": [0, 1, 2, 3, 4, 5], // no known usage
            "adequate,excelled,na,underperform": [1, 2, 3, 0], // ongoing
            "bad,best,better,no_change,worse": [1, 4, 3, 2, 0], // encounter_factor
            "bad,best,better": [0, 2, 1], // encounter_factor
            "better,same,unsure,worse": [2, 1, 3, 0], // selfreport_ivig
            "better,same,worse": [2, 1, 0], // encounter_factor
            "easy,impossible,moderate,some": [0, 3, 2, 1], // encounter_mhaq
            "impaired,normal,weak": [2, 0, 1], // checklist_fall
            "little,moderate,none,very": [2, 1, 3, 0], // ongoing_tnf
            "little,much,okay": [0, 2, 1], // selfreport_ivig
            "mild,moderate,none,severe": [1, 2, 0, 3], // encounter_hbi
            "mild,moderate,severe": [0, 1, 2], // intervention
            "no,sig_improve,small_improve,worsen": [1, 3, 2, 0], // encounter
            "no,yes_decline,yes_sig_improve,yes_small_improve": [1, 0, 3, 2], // encounter
            "poor,slighty_below,terrible,very_poor,well": [2, 1, 4, 3, 0], // encounter_hbi
        };
    }

    async auto_reload() {
        const remoteUpdatedAt = await this.nescache.get("dsl_updated_at");
        if (this.updatedAt != "" && remoteUpdatedAt > this.updatedAt) {
            if (this.reloading_dsl) return;
            this.reloading_dsl = true;

            const sql =
                "SELECT MAX(COALESCE(updated_on, created_on)) AS latest FROM dsl";
            let latest = await this.db.env.ro.query(sql);

            if (latest && latest.length == 1) {
                latest = latest[0].latest.toISOString();
                let found = false;
                for (const k in this.shared.DSL) {
                    if (this.shared.DSL[k].view.timestamp == latest) {
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    await this.reload();
                    console.log("DSL auto-reloaded at", latest);
                }
            }

            this.reloading_dsl = false;
        }
    }

    collections() {
        return Array(this.MAX_COLLECTIONS)
            .fill()
            .map((_, r) => r + 1);
    }

    field_exists(form, field) {
        return form in this.shared.DSL && field in this.shared.DSL[form].fields;
    }

    async get_cached() {
        if (!_.isEmpty(this.shared.DSL)) {
            return this.shared.DSL;
        } else {
            console.error("DSL Cache Empty");
            return false;
        }
    }

    get_type(val) {
        if (val === null) return "null";
        else if (typeof val === "boolean") return "boolean";
        else if (typeof val === "string") return "string";
        else if (typeof val === "number") return "number";
        else if (typeof val === "object")
            return Array.isArray(val) ? "array" : "object";
        else return "undefined";
    }

    async init() {
        if ("env" in this.db && "ro" in this.db.env) {
            this.set_auto_reload();
            await this.load();
        } else {
            console.log("SKIP");
        }
    }

    async load() {
        await registerValidators(this.nes);
        await registerSecurityRules(this.nes);
        await registerActionHandlers(this.nes);
        await this.reload();
    }

    async reload() {
        try {
            // need to attach autoinsert here so it can be cached
            //   because the cached data is send to client on demand
            const dsl = { autoinsert: this.shared.DSL_AutoInsert };

            // load dsl directly from sql
            const sql = `
              SELECT dsl.key, dsl.value, dsl.created_on, dsl.updated_on
              FROM dsl
              LEFT JOIN form_coffeedsl cof ON dsl.key = cof.code
              WHERE (cof.archived IS NULL) OR (cof.archived IS FALSE)
              ORDER BY dsl.key
            `;
            const rows = await this.db.env.ro.query(sql);
            if (rows) {
                for (const r of rows) {
                    const dv = JSON.parse(r.value);
                    // dynamically set last created/updated timestamp for each DSL form
                    try {
                        dv.view.timestamp = (
                            r.updated_on ||
                            r.created_on ||
                            new Date()
                        ).toISOString();
                    } catch {
                        console.log(dv);
                    }
                    dsl[r.key] = dv;
                }
            }

            /*
            // load list of tables that are too large to do auto_name=%keyword% search on
            const large_tables = await this.db.env.ro.query(
                `SELECT relname AS form FROM pg_class WHERE relname LIKE 'form_%' AND relkind = 'r' AND pg_total_relation_size(quote_ident(relname)::regclass) > 100000000;`
            );
            if (large_tables) {
                for (const s of large_tables) {
                    const form = s.form.replace("form_", "");
                    if (form in dsl) {
                        dsl[form].model.large = true;
                    } else {
                        console.error(
                            `Large table not found in DSL: ${s.form}!`
                        );
                    }
                }
            }
            */

            // set shared DSL object
            await this.set_shared(dsl);

            // clear cached views
            if ("view" in this.shared) delete this.shared.view;
        } catch (e) {
            console.error("Cannot load remote DSL:");
            console.log(e);
            process.exit(1);
        }
    }

    set_auto_reload() {
        this.auto_reload_timer = setInterval(
            this.auto_reload.bind(this),
            this.reloading_timer * 1000
        );
        console.log(
            "    auto-reload: look for changes every " +
                this.reloading_timer +
                "s"
        );
    }

    async set_shared(dsl) {
        // autoinsert already exists globally as this.shared.DSL_AutoInsert
        //   so we do not need it in the this.shared.DSL object
        const formEvents = {};
        const parentForms = {};
        if ("autoinsert" in dsl) delete dsl.autoinsert;
        // set dsl params
        for (const k in dsl) {
            formEvents[k] = {};
            const autoname = dsl[k].model?.name;
            if (this.get_type(autoname) === "array")
                dsl[k].model.autoNameFields = autoname;
            else if (this.get_type(autoname) === "string") {
                const regex = /\{([^}]+)\}/g;
                const fields = [];
                let match;
                while ((match = regex.exec(autoname)) !== null) {
                    fields.push(match[1]);
                }
                dsl[k].model.autoNameFields = fields;
            } else dsl[k].model.autoNameFields = [];
            if (dsl[k].model?.ledger) {
                if (typeof dsl[k].model?.ledger === "object") {
                    const types = ["create", "update"];
                    for (const type of types) {
                        if (dsl[k].model?.ledger[type]?.event) {
                            formEvents[k][dsl[k].model.ledger[type].event] =
                                "table_event_" + type;
                        }
                    }
                } else if (typeof dsl[k].model?.ledger === "string")
                    formEvents[k][dsl[k].model.ledger] = "table_event";
            }
            dsl[k].model[this.searchColumn] = [];
            if (this.force_non_save.indexOf(k) > -1) dsl[k].model.save = false;
            if (this.force_non_reportable.indexOf(k) > -1)
                dsl[k].model.reportable = false;
            if (k in this.custom_field_export)
                //CM:2017-06-10 - we want to custom export tables like user
                dsl[k].model.reportable = true;

            for (const [field, value] of Object.entries(dsl[k].fields)) {
                if (
                    this.get_type(value.model.search) === "string" &&
                    value.model.search !== ""
                ) {
                    dsl[k].model[this.searchColumn].push(field);
                }
                if (value.model?.ledger) {
                    formEvents[k][value.model.ledger] = field;
                }
                if (value.model.type === "subform") {
                    if (value.model.source.includes("{")) {
                        for (const sf of Object.keys(
                            value.model.sourcefilter
                        )) {
                            if (!parentForms[sf]) {
                                parentForms[sf] = {};
                            }
                            parentForms[sf][k] = 1;
                        }
                    } else {
                        if (!parentForms[value.model.source]) {
                            parentForms[value.model.source] = {};
                        }
                        parentForms[value.model.source][k] = 1;
                    }
                }
            }
        }
        // keeping the this.shared.DSL as is since it is being used in 129 places
        // updating the this.shared.DSL in reload
        // clinical_cidp diff_write,clinical_dlqi itchy, clinical_edss edss_score
        for (const sf in parentForms) {
            for (const pf in parentForms[sf]) {
                if (!_.isEmpty(parentForms[pf])) {
                    parentForms[sf] = {
                        ...parentForms[pf],
                        ...parentForms[sf],
                    };
                }
            }
        }
        this.shared.DSL = dsl;
        this.shared.parentForms = parentForms;
        this.shared.formEvents = formEvents;
        this.shared.dslSearchColumn = this.searchColumn;
        this.shared.vectorRankColumn = this.rankColumn;
        // let set = await this.nescache.set("dsl", dsl);
        const time = JSON.stringify(Date.now());
        await this.nescache.set("dsl_updated_at", time);
        this.updatedAt = time;
    }
};
