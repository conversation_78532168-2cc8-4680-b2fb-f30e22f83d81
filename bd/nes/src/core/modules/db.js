"use strict";
const { URL } = require("url");

const libdsljs = require("@core/libdsljs");

module.exports = class DB {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;

        //TODO - eventually obsolete libdsljs
        this.shared.utils = libdsljs.utils;
        this.shared.transforms = libdsljs.transforms.transforms;
    }

    async init() {
        if (!this.shared.config.env["DATABASE_URL"]) {
            console.error("No DATABASE_URL found in Environment.");
            process.exit(-1);
        } else if (this.shared.config.env["NODE_ENV"] == "development") {
            console.log(`    ${this.shared.config.env["DATABASE_URL"]}\n`);
        }

        await this.load_db();

        this.shared.AppDsl = new libdsljs.dsl(this.nes);
        this.shared.crud = new libdsljs.crud(this.nes);
    }

    async load_db() {
        const [
            db_type,
            db_hostname,
            db_port,
            db_database,
            db_username,
            db_password,
        ] = this.parse_db_url(this.shared.config.env["DATABASE_URL"]);
        const identifier =
            process.env["HOSTNAME"] || process.env["FLY_ID"] || "NES";
        // also possible to set other dbs here, including non-postgresql types
        this.shared.config.db.env = {
            application_name: this.shared.config.package.description,
            type: db_type,
            port: db_port,
            database: db_database,
            user: db_username,
            password: db_password,

            rw: {
                application_name:
                    this.shared.config.package.description +
                    " RW @ " +
                    identifier,
                host: db_hostname,
                port: db_port,
            },
            ro: {
                application_name:
                    this.shared.config.package.description +
                    " RO @ " +
                    identifier,
                host: db_hostname,
                port: (parseInt(db_port) + 1).toString(),
            },

            pool: {
                max: 64,
                min: 0,
                idleTimeoutMillis: 5000,
            },
            poolSize: 32,
        };

        // Only add SSL config if hostname is not internal or localhost
        if (
            !db_hostname.includes(".internal") &&
            !db_hostname.includes("localhost")
        ) {
            this.shared.config.db.env.ssl = {
                rejectUnauthorized: false,
            };
        }

        for (const [k, v] of Object.entries(this.shared.config.db)) {
            try {
                console.log(`    ${k} (${v.type}):`);
                const DBClass = require("@db/" + v.type);
                if (v.type == "postgresql" && "rw" in v && "ro" in v) {
                    this[k] = {};
                    for (const d of ["rw", "ro"]) {
                        const dd = d.toUpperCase();
                        this[k][d] = new DBClass(this.nes);
                        const dbconfig = Object.assign({}, v);
                        dbconfig.source = k;
                        for (const cf of ["application_name", "host", "port"])
                            dbconfig[cf] = v[d][cf];
                        try {
                            await this[k][d].init(dbconfig);
                        } catch (e) {
                            console.error(`DB ${k} ${d} ERR`);
                            console.error(e);
                            if (
                                d == "ro" &&
                                this.shared.config.env["NODE_ENV"] !=
                                    "production"
                            ) {
                                console.error(
                                    `Unable to connect to Read Only DB ${k}. Reverting to Read Write DB.`
                                );
                                dbconfig.port = v.rw.port;
                                await this[k][d].init(dbconfig);
                            }
                        }
                        console.log(`      ${dd}:${dbconfig["port"]}`);
                    }
                } else {
                    this[k] = new DBClass(this.nes);
                    await this[k].init(v);
                    console.log("      DB OK");
                }
            } catch (e) {
                console.error("    ERR");
                console.error(e);
            }
        }
    }

    parse_db_url(url = false) {
        let type, hostname, port, username, password;

        if (!url) {
            console.error(`Invalid database URL: ${url}!`);
            process.exit(-1);
        }

        const db_conn = new URL(url);

        if (db_conn.protocol) {
            type = db_conn.protocol.replace(":", "");
        } else {
            console.error(`Protocol is missing in ${url}!`);
            process.exit(-1);
        }

        if (db_conn.hostname) {
            hostname = db_conn.hostname;
        } else {
            console.error(`Hostname is missing in ${url}!`);
            process.exit(-1);
        }

        if (db_conn.port) {
            port = db_conn.port;
        } else {
            console.error(`Port is missing in ${url}!`);
            process.exit(-1);
        }

        if (db_conn.username) {
            username = db_conn.username;
        } else {
            console.error(`Username is missing in ${url}!`);
            process.exit(-1);
        }

        if (db_conn.password) {
            password = db_conn.password;
        } else {
            console.error(`Password is missing in ${url}!`);
            process.exit(-1);
        }

        // Extract the database name from the pathname
        const database = db_conn.pathname.split("/").filter(Boolean).join();
        if (!database) {
            console.error(`Database name is missing in ${url}!`);
            process.exit(-1);
        }

        return [type, hostname, port, database, username, password];
    }
};
