"use strict";
const { Issuer, generators } = require("openid-client");

module.exports = class NESServerOAuthAZ {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.auth = this.nes.modules.auth;
        this.cred = this.auth.get_oauth_providers()["AZ"];
        this.oauth_base = this.shared.BASE_URL + "/login/oauth/az";
    }

    async init() {
        const issuer = await Issuer.discover(
            `https://login.microsoftonline.com/${this.cred.tenant || "common"}/.well-known/openid-configuration`
        );

        this.client = new issuer.Client({
            client_id: this.cred.id,
            redirect_uris: [this.oauth_base],
            response_types: ["id_token"],
        });
    }

    async authorize(ctx) {
        const nonce = generators.nonce();
        const auth_url = this.client.authorizationUrl({
            scope: "openid email profile",
            response_mode: "form_post",
            nonce,
        });
        ctx.session.oauth_nonce = nonce; // session has already started as per server.js
        ctx.redirect(auth_url);
    }

    async get_user(ctx) {
        try {
            const nonce = ctx.session.oauth_nonce;
            const params = this.client.callbackParams(ctx.request);
            const tokenSet = await this.client.callback(
                this.oauth_base,
                params,
                {
                    nonce,
                }
            );
            const user = tokenSet.claims();
            user.sso_id = user.email || user.unique_name || user.upn;
            return user;
        } catch (e) {
            console.error(e);
            return false;
        }
    }
};
