"use strict";

const _ = require("lodash");
const nunjucks = require("nunjucks");
const views = require("@ladjs/koa-views");

module.exports = class NESServerLogin {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.utils = nes.shared.utils;
    }

    async init(_server, _router) {
        const template_path = global.nesroot + "src/templates";
        nunjucks.configure(template_path);
        this.render = views(template_path, { map: { html: "nunjucks" } });
    }

    async run_api_auth(server, router) {
        const auth = this.nes.modules.auth;
        const db = this.nes.modules.db;
        const fx = this.nes.modules.fx;

        router.get("/api/auth/mock/admin", async (ctx, next) => {
            const ctxHash = ctx.query.hash || false;
            if (!ctxHash) return false;
            const ctxSig = fx.md5(ctxHash + "-" + this.shared.TOKEN_SECRET);
            if (ctxSig != ctx.query.sig) return false;

            const user = auth.admin_user_for_session();
            const token = fx.generateToken(
                86400,
                this.shared.TOKEN_SECRET,
                user
            );

            ctx.user = ctx.session.user = user;
            ctx.session.is_proxy = false;
            ctx.cookies.set(this.shared.TOKEN_NAME, token);
            ctx.status = 200;

            ctx.body = {
                id: auth.ADMIN_USER.id,
            };
            this.nes.modules.server.mock_ctx[ctxHash] = ctx;
            return ctx;
        });

        router.post("/api/auth/", async (ctx, next) => {
            const start = new Date();
            const res = await auth.authenticate(ctx, {
                username: ctx.request.body.username,
                password: ctx.request.body.password,
            });

            if (!res) {
                await fx.addUserActivity(
                    { ...ctx, user: {} },
                    "user",
                    "login-pass-fail",
                    { username: ctx.request.body.username }
                );

                ctx.status = 401;
                delete ctx.user;
                return ctx;
            }

            const update_fields = {
                last_login: start,
                last_login_ip: fx.clientIP(ctx),
            };
            return await auth.login_user(
                ctx,
                update_fields,
                res.user.id,
                res.user,
                "login-pass-ok"
            );
        });
    }

    async run_api_login(server, router) {
        const fx = this.nes.modules.fx;

        router.get("/api/login/changepassword/:splat*", async (ctx, next) => {
            if (ctx.session.user) {
                ctx = fx.invalidateToken(ctx);
            }
            ctx.redirect("/login");
        });
    }

    async run_login_logout(server, router) {
        const db = this.nes.modules.db;
        const fx = this.nes.modules.fx;
        const auth = this.nes.modules.auth;
        const shared = this.shared;
        router.use(this.render);

        router.all(["/", "/login/", "/logout", "/logout/"], async (ctx) => {
            const adminUser = auth.admin_user_for_session();
            if ("session" in ctx && "user" in ctx.session && ctx.session.user) {
                await fx.addUserActivity(
                    { ...ctx, user: adminUser },
                    "user",
                    "logout"
                );
                ctx = fx.invalidateToken(ctx);
            }
            ctx.redirect("/login");
        });

        router.get("/login", async (ctx, next) => {
            if ("session" in ctx && "user" in ctx.session && ctx.session.user) {
                ctx = fx.invalidateToken(ctx);
            }
            await ctx.render("login.html", {
                oauth_providers: Object.keys(auth.get_oauth_providers()),
                override_sso_cr: this.shared.BASE_URL.includes(
                    this.shared.DEV_DOMAIN
                ),
                error: ctx.query.error || false,
                custom_style:
                    shared.config.admin_console?.env_config?.login_html,
            });
            return;
        });
    }

    async run_login_password(server, router) {
        const shared = this.shared;
        const fx = this.nes.modules.fx;
        const auth = this.nes.modules.auth;
        const db = this.nes.modules.db;
        const mustache = require("mustache");

        router.use(this.render);

        router.get("/login/changepassword/:token/", async (ctx, next) => {
            const token = ctx.params.token;
            const t = db.env.rw.transaction(ctx);
            let db_token;
            try {
                db_token = fx.extractTokenData(token);
            } catch (error) {
                ctx.body = { error: error.message };
                return;
            }
            const sql = `SELECT * FROM form_user WHERE change_password_token = %L`;
            const user = _.head(await db.env.ro.query(sql, [db_token]));
            if (!user) {
                ctx.body = { error: "User not found" };
            } else {
                const token = fx.generateRandomString();
                const ttl = Math.floor(Date.now() / 1000); // Current time in seconds
                const token_fm = fx.createToken(token, ttl);
                await t.update(
                    "user",
                    { change_password_token: token },
                    user.id
                );
                await t.commit();
                await ctx.render("changepassword.html", {
                    custom_style:
                        shared.config.admin_console?.env_config?.login_html,
                    token: token_fm,
                });
            }
        });

        router.get("/login/passwordreset", async (ctx, next) => {
            await ctx.render("passwordreset.html", {
                custom_style:
                    shared.config.admin_console?.env_config?.login_html,
            });
        });

        router.post("/login/reset/:username/", async (ctx, next) => {
            const username = ctx.params.username;
            const reset_message = `Please check the email associated with user: ${username}`;
            const sql = `SELECT * FROM form_user WHERE username = %L`;
            const user = _.head(
                await db.env.ro.query(sql, [ctx.params.username])
            );

            if (!user) {
                // Just a positive message to avoid brute forcing of usernames
                ctx.status = 200;
                ctx.body = { success: reset_message };
                return;
            }

            // Is External Auth User?
            if (user.authentication_type != "password") {
                ctx.status = 200;
                ctx.body = {
                    success:
                        "You're currently configured to use external authentication. If you need help resetting your password, please contact IT support.",
                };
                return;
            }

            const token = fx.generateRandomString();
            const ttl = Math.floor(Date.now() / 1000); // Current time in seconds
            const token_fm = fx.createToken(token, ttl);
            const pass_fields = {
                password_reset: "1",
                change_password_token: token,
            };
            const reset_url =
                this.shared.BASE_URL +
                "/login/changepassword/" +
                token_fm +
                "/";

            const t = db.env.rw.transaction(ctx);
            await t.update("user", pass_fields, user.id);
            const ures = await t.commit();
            if (ures.error) {
                ctx.status = 500;
                ctx.body = {
                    error: "Server configuration error, please contact IT support",
                };
                console.error(ures.message);
                return ctx;
            } else {
                const sql = `SELECT template_html FROM form_template_email WHERE code = %L`;
                const html_template = _.head(
                    await db.env.ro.query(sql, ["reset_pass_email"])
                );
                const html_body = mustache.render(html_template.template_html, {
                    reset_link: reset_url,
                });
                await shared.notify.sendRawEmail(
                    ctx,
                    { to: [user.email] },
                    "Password Reset",
                    `${html_body}`
                );
                ctx.status = 200;
                ctx.body = { success: reset_message };
                return;
            }
        });

        router.post("/login/setpassword/:token", async (ctx, next) => {
            const token = ctx.params.token;
            const { password, password2 } = ctx.request.body;
            let db_token;
            try {
                db_token = fx.extractTokenData(token);
            } catch (error) {
                ctx.body = { error: error.message };
                return;
            }
            if (password != password2) {
                ctx.body = {
                    message: "Password not matched",
                };
                return;
            }
            const sql = `SELECT * FROM form_user WHERE change_password_token = %L`;
            const user = _.head(await db.env.ro.query(sql, [db_token]));
            if (!user) {
                ctx.body = { error: "User not found" };
                ctx.status = 404;
                return;
            }
            const hashedPassword = this.utils.generate_password(password);
            const t = db.env.rw.transaction(ctx);
            await t.update(
                "user",
                {
                    password: hashedPassword,
                    password2: hashedPassword,
                    change_password_token: null,
                },
                user.id
            );
            await auth.validate_pass(password, hashedPassword);
            if (t.can_commit()) await t.commit();
            ctx.body = {
                success: true,
                message: "Password Reset Successfully",
            };
            ctx.status = 200;
        });
    }
};
