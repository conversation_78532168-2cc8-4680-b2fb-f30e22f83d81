"use strict";
const _ = require("lodash");

module.exports = class NESServerRoutes {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
    }

    async init(server, router) {}

    async run_api_file_static(server, router) {
        const fx = this.nes.modules.fx;

        /**
         * Route for handling static/public file downloads.
         *
         * This route listens for GET requests on the path "/api/file/static/:hash" where ":hash" is a
         * dynamic segment that represents the hash of the file to be downloaded. The hash can include
         * any character, as denoted by the regular expression "(.*)".
         *
         * @param {string} hash - The hash part of the URL, used to identify the specific file to download.
         * @param {Object} ctx - The Koa context object for the current request. It provides access to the
         * request and response, among other things.
         * @param {Function} next - A function to pass control to the next middleware function in the stack.
         *
         * @returns {Promise} A promise that resolves when the file download is initiated.
         */
        router.get("/api/file/static/:hash(.*)", async (ctx, next) => {
            const { hash } = ctx.params;
            return fx.download(hash, ctx);
        });
    }
    async run_webhook_shippo(server, router) {
        router.post("/api/shipment/all", async (ctx, next) => {
            this.shared.shipment_webhook(ctx);
            ctx.status = 200;
            return true;
        });
    }
};
