"use strict";
const { koaBody } = require("koa-body");

module.exports = class NESServerServe {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;

        this.use_body = koaBody({
            formLimit: "100mb",
            jsonLimit: "100mb",
            jsonStrict: false,
            multipart: true,
            keepExtensions: true,
        });
    }

    async init(server, router) {}

    async use_request_envelop(ctx, next) {
        const start = Date.now();
        ctx.set("X-Accel-Buffering", "no"); // no reason to slow this any more than necessary
        ctx.set("X-NES-Start", `${Date.now()}`);
        ctx.set("X-NES-PID", `${process.pid}`);
        ctx.set("X-NES-PMID", `${process.env.pm_id}`);
        await next();
        const end = Date.now();
        const ms = end - start;
        ctx.set("X-Response-Time", ms + "ms");
        console.log_ctx(ctx, ctx.status, start, end);
    }

    // cause a client browser redirect
    async use_redirect_invalid(ctx, next) {
        if (
            !ctx.path.startsWith("/api/") &&
            !ctx.path.startsWith("/login/") &&
            ctx.path != "/login" &&
            ctx.path != "/logout" &&
            ctx.path != "/logout/"
        ) {
            await this.fx.addUserActivity(ctx, "user", "logout");
            ctx.redirect("/login");
            return;
        }
        return next();
    }

    // use updated path without client browser redirect
    async use_path_overwrite(ctx, next) {
        if (ctx.path.startsWith("/api/view")) {
            ctx.path = ctx.path.replace("/api/view", "/api/form");
            ctx.set("X-NES-Path", ctx.path);
        }
        return next();
    }
};
