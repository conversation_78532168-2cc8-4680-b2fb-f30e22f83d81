"use strict";
const cors = require("@koa/cors");
const { attach_app_logger } = require("@core/logger");
const {
    registerTestEnv,
    detectCircularReferencesInProject,
} = require("@core/tester");

module.exports = class NESServerTelemetry {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
        this.monitor = nes.modules.monitor;
    }

    async init(server, router) {
        const env_prod =
            this.shared.config.env["NODE_ENV"] == "production" ||
            this.shared.config.env["NODE_ENV"] == "staging";

        this.use_app_logger = attach_app_logger({
            config: this.shared.config,
        });

        if (env_prod) {
            // for prod or staging: cors, perf monitoring

            this.use_cors = cors();

            this.run_performance_monitoring = async (server, router) => {
                // global.sentry.setupKoaErrorHandler(server);
                this.monitor.attach_performance_monitoring(server);
            };
        } else {
            // for dev or testing: attach test, detect circular ref.

            this.use_attach_test = registerTestEnv({
                config: this.shared.config,
            });

            this.run_detect_circular_reference = async (server, router) => {
                if (
                    this.shared.config.env["NODE_ENV"] == "development" ||
                    this.shared.config.env["NODE_ENV"] == "testing"
                ) {
                    setTimeout(() => {
                        detectCircularReferencesInProject(
                            global.nesroot + "src/"
                        );
                    }, 1000);
                }
            };
        }
    }
};
