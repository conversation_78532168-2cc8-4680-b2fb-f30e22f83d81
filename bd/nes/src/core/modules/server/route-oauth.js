"use strict";
const _ = require("lodash");

module.exports = class NESServerOAuth {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
    }

    async init(_server, _router) {}

    async get_provider(type) {
        const provider = require(__dirname + "/oauth/" + type.toLowerCase());
        const oauth = new provider(this.nes);
        await oauth.init();
        return oauth;
    }

    async run_oauth_providers(server, router) {
        const auth = this.nes.modules.auth;
        const db = this.nes.modules.db;
        const fx = this.nes.modules.fx;

        const auth_providers = auth.get_oauth_providers();

        router.get("/login/oauth", async (ctx, next) => {
            ctx.redirect("/login");
        });

        router.get("/login/oauth/:provider", async (ctx, next) => {
            const { provider } = ctx.params;
            if (!provider || !(provider.toUpperCase() in auth_providers)) {
                return ctx.redirect("/login");
            }

            const oauth = await this.get_provider(provider);
            await oauth.authorize(ctx);
        });

        router.post("/login/oauth/:provider", async (ctx, next) => {
            const { provider } = ctx.params;
            if (!(provider && provider.toUpperCase() in auth_providers)) {
                return ctx.redirect("/login");
            }

            const oauth = await this.get_provider(provider);
            const user = await oauth.get_user(ctx);

            if (!user) {
                return ctx.redirect("/login?error=Error logging in.");
            }

            const start = new Date();
            const res = await auth.authenticate(ctx, user);
            if (!res) {
                await fx.addUserActivity(
                    { ...ctx, user: {} },
                    "user",
                    `login-${provider}-fail`,
                    {
                        username: user.sso_id,
                        user,
                        ctx_request: ctx.request,
                        ctx_params: ctx.params,
                        session: ctx.session,
                    }
                );

                ctx.status = 401;
                delete ctx.user;
                return ctx.redirect("/login?error=User not setup for SSO.");
            }

            const update_fields = {
                last_login: start,
                last_login_ip: fx.clientIP(ctx),
            };
            const logctx = await auth.login_user(
                ctx,
                update_fields,
                res.user.id,
                res.user,
                `login-${provider}-ok`
            );
            if (logctx.body.portal) {
                ctx.redirect(logctx.body.portal);
            }
            return ctx;
        });
    }
};
