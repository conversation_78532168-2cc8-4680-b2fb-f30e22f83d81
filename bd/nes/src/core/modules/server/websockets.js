"use strict";
const http = require("http");
const url = require("url");
const _ = require("lodash");

module.exports = class NESWebsockets {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
    }

    async init(server, router) {
        // setInterval(() => { this.monitor_next(); }, 2000);
    }

    /*
    is_origin_local(ctx) {
        return (
            "host" in ctx.header &&
            "origin" in ctx.header &&
            "x-scheme" in ctx.header &&
            ctx.header.origin ==
                ctx.header["x-scheme"] + "://" + ctx.header.host
        );
    }

    monitor(ctx, form) {
        if (!(form in this.server.monitors))
            this.server.monitors[form] = {
                ts: null,
                clients: [],
            };
        ctx.websocket.monitor_failures = 0;
        this.server.monitors[form].clients.push(ctx.websocket);
    }

    async add_client(ctx) {
        // get all clients then update the object and set to save again
        let conn_clients = await this.nescache.get("conn_clients");
        const conn_user = await this.auth.get_user(ctx);
        //we should be getting an object array here or bool false
        //check for the presense of the user if already there don't add blindly
        let update = true;
        this.server.wsclients[conn_user.id] = ctx.websocket;
        if (!_.isEmpty(conn_clients))
            for (const [_, val] of Object.entries(conn_clients)) {
                if (parseInt(val.id) == parseInt(conn_user.id)) update = false;
            }
        if (update) {
            if (!_.isEmpty(conn_clients))
                conn_clients.push({
                    user_id: conn_user.id,
                    clients: ctx.websocket,
                });
            //       should only get in the else when there is no user connected
            else
                conn_clients = [
                    {
                        user_id: conn_user.id,
                        clients: ctx.websocket,
                    },
                ];
            const set = await this.nescache.set("conn_clients", conn_clients);
            if (set)
                console.log(
                    `user id: ${conn_user.id} successfully stored in cache`
                );
            else console.log(`user id: ${conn_user.id} cache store failed`);
        }
    }

    async remove_client(ctx, session) {
        if (!session) session = ctx.session;
        //update sessions array to make sure we don't get logged in user_id issue
        const conn_clients = await this.nescache.get("conn_clients");
        const conn_close = await this.auth.get_user(ctx);

        _.remove(conn_clients, function (e) {
            return e.user_id == conn_close.id;
        });
        delete this.server.wsclients[conn_close.id];
        const set = await this.nescache.set("conn_clients", conn_clients);
        if (set)
            console.log(
                `user id:${session.user.id} successfully removed in cache`
            );
        else console.log(`user id:${session.user.id} cache remove failed`);
    }

    async monitor_next() {
        for (const [form, data] of Object.entries(this.server.monitors)) {
            const sql =
                "SELECT GREATEST(MAX(updated_on), MAX(created_on), '2001-01-01') m FROM form_" +
                form;
            let ts = await this.db.env.ro.query(sql, []);
            if (ts && ts.length == 1) ts = new Date(ts[0].m).getTime();
            else continue;
            if (ts == data.ts) continue;
            data.ts = ts;

            for (const [i, ws] of data.clients.entries()) {
                try {
                    if (ws)
                        ws.send(
                            JSON.stringify({
                                monitor: form,
                                timestamp: ts,
                            })
                        );
                } catch {
                    if (!ws || ++ws.monitor_failures > 3)
                        delete data.clients[i];
                }
            }
        }
    }

    // Send message using websockets shared over IPC
    ws_send(params) {
        if (this.server.wsclients[params.clients.user_id]) {
            this.server.wsclients[params.clients.user_id].send(
                JSON.stringify(params.data)
            );
        }
    }

    ws_use() {
        // WSS: response
        // not using sockets currently
        this.server.ws.use(async (ctx, next) => {
            ctx.session = this.server.createContext(
                ctx.req,
                new http.OutgoingMessage()
            ).session;
            // await this.add_client(ctx);
            await next();
            if (!this.is_origin_local(ctx)) {
                this.shared.utils.error({
                    error: "Unauthorized connection attempt!",
                    header: ctx.header,
                });
                ctx.websocket.terminate();
            } else {
                console.log_ctx(ctx, 101, Date.now(), Date.now());
            }

            ctx.websocket.send(
                JSON.stringify({
                    id: 0,
                    status: 200,
                    body: {
                        message: "NES connected over WSS successfully.",
                    },
                })
            );

            ctx.websocket.on("close", async () => {
                console.log("connection closed");
                // removing client on connection closed
                // this.remove_client(ctx);
            });
            // no jsonP or pdf/print support
            ctx.websocket.on("message", async (call) => {
                ctx.session = this.server.createContext(
                    ctx.req,
                    new http.OutgoingMessage()
                ).session;
                const start = Date.now();
                let end = 0;
                let respms = 0;
                try {
                    call = JSON.parse(call);
                } catch (e) {
                    end = Date.now();
                    console.log_ctx(ctx, 500, start, end);
                    this.shared.utils.error(e);
                    return;
                }

                if ("monitor" in call) return this.monitor(ctx, call.monitor);

                const crx = {
                    id: "id" in call ? call.id : 0,
                    body: "",
                    cookies: ctx.cookies,
                    headers: ctx.headers,
                    protocol: "WSS",
                    query: {},
                    request: {
                        method: "method" in call ? call.method : "GET",
                        url: "url" in call ? call.url : "",
                        body: "body" in call ? call.body : "",
                    },
                    status: 200,
                    user: {},
                };

                if (
                    (call.url
                        ? call.url.replace(this.shared.BASE_URL, "")
                        : "") === "/api/status/"
                ) {
                    const status = await this.auth.status(ctx);
                    if (status) {
                        crx.body = status;
                        crx.user = ctx.session.user;
                    } else {
                        await this.fx.invalidateToken(ctx);
                        crx.status = 401;
                        crx.body = {
                            error: "forbidden: " + crx.request.url,
                        };
                        ctx.session = null;
                    }
                    end = Date.now();
                    respms = end - start;
                    console.log_ctx(crx, crx.status, start, end);
                } else {
                    try {
                        crx.user =
                            "user" in ctx
                                ? ctx.user
                                : await this.auth.get_user(ctx);
                        if (crx.user) {
                            crx.query = url.parse(crx.request.url, true).query;
                            const callurl = url.parse(crx.request.url);
                            callurl.pathname = callurl.pathname.replace(
                                /\/+$/,
                                ""
                            );
                            const callpath =
                                (callurl.pathname || "")
                                    .substring(1)
                                    .split("/") || [];
                            const [CallSource, mod_src, mod_base] =
                                await this.fx.getModuleSource(callpath);
                            if (CallSource) {
                                const callapi = new CallSource(this.nes);
                                await callapi.process(crx, {
                                    url: callurl,
                                    path: callpath,
                                });
                            } else {
                                crx.status = 404;
                                crx.body = {
                                    error: "Not Found: " + crx.request.url,
                                };
                            }
                            end = Date.now();
                            respms = end - start;
                            console.log_ctx(crx, crx.status, start, end);
                        } else {
                            await this.fx.invalidateToken(ctx);
                            crx.status = 401;
                            ctx.session = null;
                            crx.body = {
                                error: "forbidden: " + crx.request.url,
                            };
                        }
                    } catch (e) {
                        crx.status = 500;
                        end = Date.now();
                        respms = end - start;
                        console.log_ctx(crx, crx.status, start, end);
                        this.shared.utils.error(e);
                        console.error(e);
                        crx.body = {
                            error:
                                "Internet error accessing URL: " +
                                crx.request.url,
                        };
                    }
                }

                ctx.websocket.send(
                    JSON.stringify({
                        id: crx.id,
                        ms: respms,
                        status: crx.status,
                        body: crx.body,
                    })
                );
            });
        });
    }
    */
};
