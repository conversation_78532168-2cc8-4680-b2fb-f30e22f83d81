"use strict";

module.exports = class BasicAuthMiddleware {
    constructor(nes) {
        this.nes = nes;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
    }

    init(server, router) {
        // Init method required by the middleware system
    }

    async use_basic_auth(ctx, next) {
        // Only process requests with Basic Auth header
        if (
            ctx.header &&
            ctx.header.authorization &&
            ctx.header.authorization.startsWith("Basic ")
        ) {
            const basic_auth = ctx.header.authorization.substr(6).trim();
            const credentials = Buffer.from(basic_auth, "base64")
                .toString()
                .split(":");

            if (credentials.length === 2) {
                const username = credentials[0];
                const password = credentials[1];

                // First check admin/test users (synchronous)
                const user_admin = this.shared.config.nes.username;
                const pass_admin2 = this.shared.config.env["NES_API_KEY"];

                const allow_test =
                    this.shared.config.env["NODE_ENV"] == "development" ||
                    this.shared.config.env["NODE_ENV"] == "testing";
                const user_test = allow_test
                    ? this.shared.config.env["TEST_USERNAME"]
                    : "INVALID!!!";
                const pass_test = allow_test
                    ? this.shared.config.env["TEST_PASSWORD"]
                    : "INVALID!!!";

                if (username === user_admin && password === pass_admin2) {
                    // Admin user
                    ctx._authToken = "admin_authenticated";
                    ctx._authUser = this.auth.admin_user_for_session();
                    console.log("Basic auth: pre-authenticated admin user");
                } else if (
                    allow_test &&
                    username === user_test &&
                    password === pass_test
                ) {
                    // Test user
                    ctx._authToken = "test_authenticated";
                    ctx._authUser = this.auth.TEST_USER;
                    console.log("Basic auth: pre-authenticated test user");
                } else {
                    // For regular users, perform async DB authentication
                    try {
                        const authResult = await this.auth.authenticate(ctx, {
                            username,
                            password,
                        });

                        if (authResult && authResult.user) {
                            // Set synchronous token and user
                            ctx._authToken = "db_authenticated";
                            ctx._authUser = authResult.user;
                            console.log(
                                "Basic auth: pre-authenticated DB user:",
                                username
                            );
                        } else {
                            console.log("Basic auth failed for", username);
                        }
                    } catch (error) {
                        console.error("Basic auth error:", error);
                    }
                }
            }
        }

        await next();
    }
};
