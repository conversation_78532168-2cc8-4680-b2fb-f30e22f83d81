"use strict";
const _ = require("lodash");
module.exports = class Security {
    constructor(nes) {
        this.db = nes.modules.db;
        this.shared = nes.shared;
    }

    async init() {
        this.make_rls_global();
        this.get_security_forms();
        await this.db.env.rw.subscribe_trigger_function(
            "sec_rule_trigger_channel",
            this.make_rls_global
        );
    }

    make_rls_global = async () => {
        try {
            if (!this.shared.DSL.sec_rule) return;

            const obj = {};
            const sql =
                "SELECT * FROM form_sec_rule WHERE type='RLS Global' AND (archived IS NULL OR archived = false) AND (deleted IS NULL OR deleted = false) AND (active IS NULL OR active = 'Yes')";
            const rules = await this.db.env.rw.query(sql);
            for (let i = 0; i < rules.length; i++) {
                const rule = rules[i];
                if (!rule.table_name || !rule.rule_key) continue;

                if (!(rule?.default_value && rule.default_value != "*"))
                    continue;

                if (!(rule.table_name in obj)) obj[rule.table_name] = {};
                if (!(rule.rule_key in obj[rule.table_name]))
                    obj[rule.table_name][rule.rule_key] = {
                        rules: [],
                        filters: [],
                    };

                obj[rule.table_name][rule.rule_key].rules.push(rule);
                obj[rule.table_name][rule.rule_key].filters.push(
                    rule.default_value
                );
                if (!(rule.rule_link?.length > 0)) continue;

                for (const [table_name, cson] of Object.entries(
                    this.shared.DSL
                )) {
                    if (
                        cson &&
                        "fields" in cson &&
                        rule.rule_link in cson.fields
                    ) {
                        if (!(table_name in obj)) {
                            obj[table_name] = {
                                [rule.rule_link]: { rules: [], filters: [] },
                            };
                        }
                        obj[table_name][rule.rule_link].rules.push(rule);
                        if (obj[table_name][rule.rule_link].filters) {
                            obj[table_name][rule.rule_link].filters.push(
                                rule.default_value
                            );
                        } else {
                            obj[table_name][rule.rule_link].filters = [
                                rule.default_value,
                            ];
                        }
                    }
                }
            }
            if ((!"rls") in this.shared) this.shared.rls = { global: {} };
            this.shared.rls.global = obj;
        } catch (error) {
            console.error(error);
            this.shared.rls.global = {};
        }
    };

    // also all the parents forms of the forms in the perms array
    get_security_forms() {
        const forms = require("./security/perms");
        const formNames = {};
        Object.getOwnPropertyNames(forms.prototype)
            .filter((prop) => prop.startsWith("check_"))
            .map((prop) => prop.replace("check_", ""))
            .map((form) => {
                formNames[form] = 1;
                if (!_.isEmpty(this.shared.parentForms[form])) {
                    Object.keys(this.shared.parentForms[form]).map((parent) => {
                        formNames[parent] = 1;
                    });
                }
            });
        this.shared.perms = Object.keys(formNames);
    }
};
