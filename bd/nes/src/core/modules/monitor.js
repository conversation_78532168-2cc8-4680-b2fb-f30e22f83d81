"use strict";
const Sentry = global.sentry;

module.exports = class NESMonitor {
    constructor(nes) {
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.sentry = Sentry;
    }

    async init() {}

    attach_performance_monitoring(server) {
        Sentry.setupKoaErrorHandler(server);
        // server.use(this.requestHandler);
        // server.on("error", (err, ctx) => {
        //     Sentry.withScope((scope) => {
        //         scope.addEventProcessor((event) => {
        //             return Sentry.addRequestDataToEvent(event, ctx.request);
        //         });
        //         Sentry.captureException(err);
        //     });
        // });
    }

    async monitor_async(fn, name, ...args) {
        Sentry.startSpan(
            {
                op: "rootSpan",
                name: name,
            },
            async () => {
                try {
                    await fn(...args);
                } catch (error) {
                    console.error("Error within span execution:", error);
                    Sentry.captureException(error);
                    throw error;
                }
            }
        );
    }

    monitor_sync(fn, name, ...args) {
        Sentry.startSpan(
            {
                op: "rootSpan",
                name: name,
            },
            () => {
                try {
                    fn(...args);
                } catch (error) {
                    console.error("Error within span execution:", error);
                    Sentry.captureException(error);
                    throw error;
                }
            }
        );
    }

    // async requestHandler(ctx, next) {
    //     await Sentry.withIsolationScope(async () => {
    //         let traceparentData;
    //         if (ctx.request.get("sentry-trace")) {
    //             traceparentData = extractTraceParentData(
    //                 ctx.request.get("sentry-trace")
    //             );
    //         }

    //         await Sentry.startSpan(
    //             {
    //                 name: ctx.path, // Will be refined when _matchedRoute is set
    //                 op: "http.server",
    //                 startTime: ctx.startTime / 1000, // Convert milliseconds to seconds
    //                 ...traceparentData,
    //             },
    //             async (span) => {
    //                 if (span) {
    //                     Object.defineProperty(ctx, "_matchedRoute", {
    //                         set(value) {
    //                             span.updateName(`${ctx.method} ${value}`);
    //                             this.__matchedRoute = value;
    //                         },
    //                         get() {
    //                             return this.__matchedRoute;
    //                         },
    //                     });
    //                 }

    //                 Sentry.getIsolationScope().setSDKProcessingMetadata({
    //                     request: ctx.request,
    //                 });

    //                 try {
    //                     await next();
    //                 } catch (err) {
    //                     // If an error occurs, emit it using the Koa app's error event
    //                     ctx.app.emit("error", err, ctx);
    //                 } finally {
    //                     // Optional: Increment metrics and log distribution, adjust according to your Sentry plan and setup
    //                     Sentry.metrics.increment("http.server.request", 1);
    //                     Sentry.metrics.distribution(
    //                         "http.server.response_time",
    //                         ctx.deltaTime,
    //                         {
    //                             unit: "ms",
    //                         }
    //                     );
    //                 }
    //             }
    //         );
    //     });
    // }
};
