const SQLGetClass = require("@api/form/helpers/sql-get");
const _ = require("lodash");

module.exports = class Customer {
    constructor(nes) {
        this.nes = nes;
        this.modules = nes.modules;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.sql_get = null;

        // Bind the method to preserve 'this' context
        this.load_company = this.load_company.bind(this);
    }

    get_customer_query() {
        return `
        SELECT
            form_company.*,
            created_by_user.auto_name AS created_by_auto_name,
            change_by_user.auto_name AS change_by_auto_name,
            updated_by_user.auto_name AS updated_by_auto_name,
            reviewed_by_user.auto_name AS reviewed_by_auto_name,
            state_id_list_us_state.auto_name AS state_id_auto_name,
            px_inter_primary_type_list_intervention_type.auto_name AS px_inter_primary_type_auto_name,
            px_note_type_list_note_type.auto_name AS px_note_type_auto_name,
            intake_inter_primary_type_list_intervention_type.auto_name AS intake_inter_primary_type_auto_name,
            CASE
                WHEN form_company.hidden_days IS NULL THEN ARRAY_TO_JSON(ARRAY [] :: varchar [])
                WHEN form_company.hidden_days :: varchar ~ '^{'
                AND form_company.hidden_days :: varchar ~ '}$' THEN ARRAY_TO_JSON(form_company.hidden_days :: varchar [])
                ELSE ARRAY_TO_JSON(ARRAY [form_company.hidden_days])
            END AS hidden_days_to_array,
            CASE
                WHEN form_company.px_survey_type IS NULL THEN ARRAY_TO_JSON(ARRAY [] :: varchar [])
                WHEN form_company.px_survey_type :: varchar ~ '^{'
                AND form_company.px_survey_type :: varchar ~ '}$' THEN ARRAY_TO_JSON(form_company.px_survey_type :: varchar [])
                ELSE ARRAY_TO_JSON(ARRAY [form_company.px_survey_type])
            END AS px_survey_type_to_array,
            ARRAY(
                SELECT
                    form_list_clinical_asmt_fk
                from
                    gr_form_company_clinical_assmts_to_list_clinical_asmt_id
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_clinical_assmts_to_list_clinical_asmt_id.id
            ) AS clinical_assmts,
            ARRAY(
                SELECT
                    form_list_clinical_asmt.auto_name
                FROM
                    gr_form_company_clinical_assmts_to_list_clinical_asmt_id
                    LEFT JOIN form_list_clinical_asmt ON LOWER(
                        gr_form_company_clinical_assmts_to_list_clinical_asmt_id.form_list_clinical_asmt_fk
                    ) = LOWER(form_list_clinical_asmt.code)
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_clinical_assmts_to_list_clinical_asmt_id.id
            ) AS clinical_assmts_auto_name,
            ARRAY(
                SELECT
                    form_list_intervention_type_fk
                from
                    gr_form_company_px_inter_types_to_list_intervention_type_id
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_px_inter_types_to_list_intervention_type_id.id
            ) AS px_inter_types,
            ARRAY(
                SELECT
                    form_list_intervention_type.auto_name
                FROM
                    gr_form_company_px_inter_types_to_list_intervention_type_id
                    LEFT JOIN form_list_intervention_type ON LOWER(
                        gr_form_company_px_inter_types_to_list_intervention_type_id.form_list_intervention_type_fk
                    ) = LOWER(form_list_intervention_type.code)
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_px_inter_types_to_list_intervention_type_id.id
            ) AS px_inter_types_auto_name,
            ARRAY(
                SELECT
                    form_list_intervention_type_fk
                from
                    gr_form_company_intake_inter_types_to_list_intervention_type_id
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_intake_inter_types_to_list_intervention_type_id.id
            ) AS intake_inter_types,
            ARRAY(
                SELECT
                    form_list_intervention_type.auto_name
                FROM
                    gr_form_company_intake_inter_types_to_list_intervention_type_id
                    LEFT JOIN form_list_intervention_type ON LOWER(
                        gr_form_company_intake_inter_types_to_list_intervention_type_id.form_list_intervention_type_fk
                    ) = LOWER(form_list_intervention_type.code)
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_intake_inter_types_to_list_intervention_type_id.id
            ) AS intake_inter_types_auto_name,
            ARRAY(
                SELECT
                    form_list_intervention_type_fk
                from
                    gr_form_company_nurse_inter_types_to_list_intervention_type_id
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_nurse_inter_types_to_list_intervention_type_id.id
            ) AS nurse_inter_types,
            ARRAY(
                SELECT
                    form_list_intervention_type.auto_name
                FROM
                    gr_form_company_nurse_inter_types_to_list_intervention_type_id
                    LEFT JOIN form_list_intervention_type ON LOWER(
                        gr_form_company_nurse_inter_types_to_list_intervention_type_id.form_list_intervention_type_fk
                    ) = LOWER(form_list_intervention_type.code)
                WHERE
                    form_company_fk = form_company.id
                ORDER BY
                    gr_form_company_nurse_inter_types_to_list_intervention_type_id.id
            ) AS nurse_inter_types_auto_name
        FROM
            form_company
            LEFT JOIN form_user AS created_by_user ON form_company.created_by = created_by_user.id
            LEFT JOIN form_user AS change_by_user ON form_company.change_by = change_by_user.id
            LEFT JOIN form_user AS updated_by_user ON form_company.updated_by = updated_by_user.id
            LEFT JOIN form_user AS reviewed_by_user ON form_company.reviewed_by = reviewed_by_user.id
            LEFT JOIN form_list_us_state AS state_id_list_us_state ON form_company.state_id = state_id_list_us_state.code
            LEFT JOIN form_list_intervention_type AS px_inter_primary_type_list_intervention_type ON form_company.px_inter_primary_type = px_inter_primary_type_list_intervention_type.code
            LEFT JOIN form_list_note_type AS px_note_type_list_note_type ON form_company.px_note_type = px_note_type_list_note_type.code
            LEFT JOIN form_list_intervention_type AS intake_inter_primary_type_list_intervention_type ON form_company.intake_inter_primary_type = intake_inter_primary_type_list_intervention_type.code
        WHERE
            (
                form_company.deleted = FALSE
                OR form_company.deleted IS NULL
            )
            AND (form_company.id = '1')
            AND (
                form_company.archived = FALSE
                OR form_company.archived IS NULL
            )
        `;
    }
    async init() {
        await this.load_company();
        await this.db.env.rw.subscribe_trigger_function(
            "company_config_trigger_channel",
            this.load_company
        );
    }

    async get_customer_from_sql() {
        const company = _.head(
            await this.db.env.rw.query(this.get_customer_query())
        );
        return company;
    }

    async load_company() {
        console.log("    Loading Company Config...");
        let company = false;
        try {
            if (this.shared.form?.get) {
                company = _.head(
                    await this.shared.form.get.get_form(
                        { user: this.modules.auth.ADMIN_USER },
                        this.modules.auth.ADMIN_USER,
                        "company",
                        {
                            filter: "id:1",
                            fields: "all",
                            limit: 1,
                        }
                    )
                );
                console.log("Company configuration loaded from form...");
            } else {
                company = await this.get_customer_from_sql();
                console.log("Company configuration loaded from sql...");
            }
        } catch (error) {
            console.error("Error reading company config...", error);
        }
        if (company) {
            this.shared.config.company = company;
            console.log("Company configuration loaded...");
        } else {
            console.error("Company configuration not found..");
        }
    }
};
