"use strict";

module.exports = class FxClass {
    getPGParamValues(params) {
        const RESERVED_WORDS = [
            "limit",
            "page_number",
            "filter",
            "sort",
            "fields",
            "code",
            "keywords",
            "refresh",
        ];
        const keys = Object.keys(params);
        const paramValues = keys
            .map((key) => {
                if (RESERVED_WORDS.includes(key)) {
                    return null;
                }
                return params[key];
            })
            .filter(Boolean);
        return paramValues;
    }

    sanitizeReadQuery(query) {
        const forbiddenOperations =
            /(?<![.\w])(UPDATE|DELETE|INSERT)(?![.\w])/gi;
        return query.replace(forbiddenOperations, '"$1"');
    }

    buildQueryFilters(ctx, params, dsl, default_row_limit = 1000) {
        const flt = this.getParamsFilter(
            "filter" in params ? params.filter : []
        );
        if (Object.keys(flt).length > 0 && !dsl) {
            return { error: "DSL definition not found." };
        }
        let sql = "";
        const where = [];

        for (const [k, index] of Object.entries(flt)) {
            if (k.endsWith("_auto_name")) {
                continue;
            }
            if (!dsl.fields?.[k]) {
                continue;
            }
            if (dsl.fields?.[k]?._meta?.sql_type == "ARRAY") {
                if (this.get_type(index) == "string") {
                    where.push(`'${index}' = ANY(${k})`);
                } else if (this.get_type(index) == "array") {
                    const ao = [];
                    for (const i of index) {
                        ao.push(
                            `'${i}' = ANY(ARRAY(SELECT jsonb_array_elements_text(${k}::jsonb)))`
                        );
                    }
                    where.push(`(${ao.join(" OR ")})`);
                }
            } else if (index.length > 1) {
                where.push(`"${k}" in ('${index.join("', '")}')`);
            } else {
                if (index[0].includes("%")) {
                    where.push(`"${k}" ilike '${index[0]}'`);
                } else {
                    if (index[0] == "null") {
                        where.push(`"${k}" is null`);
                    } else if (
                        typeof index[0] == "string" &&
                        index[0].startsWith("!")
                    ) {
                        where.push(`"${k}" != '${index[0].slice(1)}'`);
                    } else {
                        where.push(`"${k}" = '${index[0]}'`);
                    }
                }
            }
        }
        if ("site_id" in dsl.fields) {
            if (
                Array.isArray(ctx.session.site_id) &&
                !ctx.session.site_id.includes(0)
            ) {
                where.push(
                    `("site_id"::TEXT in (${ctx.session.site_id
                        .map((id) => `'${id}'`)
                        .join(",")}) OR "site_id" IS NULL)`
                );
            }
        }
        if (
            params.keywords_query &&
            params.keywords_query.length > 0 &&
            dsl &&
            dsl?.view?.grid?.fields?.length
        ) {
            const kw = params.keywords_query;
            const hide_columns = dsl?.view?.grid?.hide_columns || [];
            const fields = dsl?.view?.grid?.fields
                .filter((f) => !hide_columns.includes(f))
                .map((f) => `"${f}"`)
                .join(",");
            where.push(`concat(${fields}) ilike '%${kw}%'`);
        }

        if (where.length > 0) sql = sql + " WHERE " + where.join(" AND ");

        if (!("limit" in params)) params.limit = default_row_limit;

        const psort = this.getParamsSort("sort" in params ? params.sort : []);
        if (dsl && dsl.fields) {
            for (const s of psort) {
                if (!(s[0] in dsl.fields))
                    return {
                        error:
                            "Sort field: " +
                            s[0] +
                            " not found in form: " +
                            dsl.view.label,
                    };
            }
        }
        params.sort = psort
            .map((q) => {
                if (q.length == 1) return `"${q[0]}"`;
                return `"${q[0]}" ${q[1]}`;
            })
            .join(",");
        let defview = " ";
        if (params?.sort?.trim?.()) {
            defview = " ORDER BY " + params.sort;
        }
        defview += " LIMIT " + params.limit;
        if ("page_number" in params && parseInt(params.page_number) > 0)
            defview += " OFFSET " + parseInt(params.page_number) * params.limit;

        return { filters: sql + defview };
    }

    getParamsFilter(filter) {
        const fp = {};
        const ff =
            typeof filter == "object" && Array.isArray(filter)
                ? filter
                : [filter];
        for (const f of ff) {
            const [fk, fv] = f.split(/:(.+)/);
            if (typeof fk != "undefined" && typeof fv != "undefined") {
                if (!(fk in fp)) fp[fk] = [];
                fp[fk].push(fv.replace(/\*/g, "%"));
            }
        }
        return fp;
    }

    getParamsSort(fields) {
        const sorted = [];
        if (typeof fields == "string") fields = [fields];
        for (const f of fields) {
            if (f.substr(0, 1) == "-") {
                sorted.push([f.substr(1), "DESC"]);
            } else {
                sorted.push([f]);
            }
        }
        return sorted;
    }

    get_type(val) {
        if (val === null) return "null";
        else if (typeof val === "boolean") return "boolean";
        else if (typeof val === "string") return "string";
        else if (typeof val === "number") return "number";
        else if (typeof val === "object")
            return Array.isArray(val) ? "array" : "object";
        else return "undefined";
    }
};
