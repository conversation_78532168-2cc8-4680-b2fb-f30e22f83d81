/*jshint : 6 */
"use strict";

const { v4: uuid } = require("uuid");
const AsyncLock = require("async-lock");

const _ = require("lodash");
const { Worker } = require("worker_threads");
const DEFAULT_TIMEOUT_MS = 5000;
/**
 * @class SafeAssigner
 * @description A class to safely assign data into a shared object
 * during async operations. Pass in the shared_object in the constructor
 * and use await assign_in() or await set_val(). A lock is acquired
 * before assigning a value so concurrent assignments can't occur.
 */
class SafeAssigner {
    constructor(shared_obj) {
        this.uuid = uuid();
        this.lock = new AsyncLock({ timeout: DEFAULT_TIMEOUT_MS });
        this.shared_obj = shared_obj;
    }

    /**
     * Assigns the data into to the shared object.
     *
     * @param {!Object} data - The data to assign in
     * @param {String} path - The subpath of the object to set in dot notation
     * @param {Function} done_func - closure to run when operation is complete
     *                          (error = null) => { }
     * @pubic
     */
    assign_in = async (data, path = null, done_func = null) => {
        if (done_func && done_func.length !== 1)
            throw new Error(
                "Done_func has incorrect number of parameters. Should be (error)."
            );

        await this.lock
            .acquire(this.uuid, async () => {
                if (path) {
                    // Set a sub-path on the object
                    const sub_obj = _.get(this.shared_obj, path);
                    if (!sub_obj)
                        throw new Error(
                            `${path} is an invalid path for shared object`
                        );
                    _.assignIn(sub_obj, data);
                } else {
                    _.assignIn(this.shared_obj, data);
                }
            })
            .catch((err) => {
                if (done_func) done_func(err);
                console.debug(`Safe Assigner: Assign in error: ${err.message}`);
                throw err;
            })
            .then(() => {
                if (done_func) done_func(null);
            });
    };

    // accepts an object with {key: promise} and returns fulfilled object
    async parallel(queue) {
        const keys = Object.keys(queue);
        const vals = Object.values(queue);
        return await new Promise((resolve, _) => {
            Promise.all(vals).then((values) => {
                const result = {};
                for (const i in values) result[keys[i]] = values[i];
                resolve(result);
            });
        });
    }
    /**
     * Sets the value of a key in the shared object.
     *
     * @param {!String} key - The key to set, can be a path in dot notation in the object.
     * @param {!Object} value - The value to set.
     * @param {Function} func - closure that runs to fetch the value. Will be passed the key and value
     *                          (key, value) => { return new_value }
     * @param {Function} done_func - closure to run when operation is complete
     *                          (error = null) => { }
     * @public
     */
    set_val = async (key, value, func = null, done_func = null) => {
        if (func && func.length !== 2)
            throw new Error(
                "Func has incorrect number of parameters. Should be (key, value)."
            );
        if (done_func && done_func.length !== 1)
            throw new Error(
                "Done_func has incorrect number of parameters. Should be (error)."
            );

        await this.lock
            .acquire(this.uuid, async () => {
                if (func) {
                    value = func(key, value);
                }
                _.set(this.shared_obj, key, value);
            })
            .catch((err) => {
                if (done_func) done_func(err);
                console.debug(`Safe Assigner: Set value error: ${err.message}`);
                throw err;
            })
            .then(() => {
                if (done_func) done_func(null);
            });
    };
}

module.exports = class FxClass {
    getSafeAssigner(obj) {
        const safe_assigner = new SafeAssigner(obj);
        return safe_assigner;
    }

    async thread(path, data) {
        return new Promise((resolve, reject) => {
            const worker = new Worker(path, {
                workerData: data,
            });
            worker.on("message", resolve);
            worker.on("error", reject);
            worker.on("exit", (code) => {
                if (code !== 0)
                    reject(new Error(`Worker stopped with exit code ${code}`));
            });
        });
    }

    /**
     * Executes a batch of asynchronous operations in parallel.
     *
     * @param {Object.<string, Promise>} queue - An object where keys represent the operation names and values are Promises representing the asynchronous operations.
     * @returns {Promise<Object.<string, any>>} - A Promise that resolves to an object containing the results of all the asynchronous operations, where keys correspond to the original operation names.
     * @public
     *
     * @example
     * // Example usage:
     * const asyncFunction1 = () => new Promise(resolve => setTimeout(() => resolve(1), 1000));
     * const asyncFunction2 = () => new Promise(resolve => setTimeout(() => resolve(2), 2000));
     * const asyncFunction3 = () => new Promise(resolve => setTimeout(() => resolve(3), 3000));
     *
     * const queue = {
     *     operation1: asyncFunction1(),
     *     operation2: asyncFunction2(),
     *     operation3: asyncFunction3()
     * };
     *
     * async function example() {
     *     try {
     *         const results = await parallel(queue);
     *         console.log(results); // { operation1: 1, operation2: 2, operation3: 3 }
     *     } catch (error) {
     *         console.error(error);
     *     }
     * }
     *
     * example();
     */
    async parallel(queue) {
        const keys = Object.keys(queue);
        const vals = Object.values(queue);
        return await new Promise((resolve, _) => {
            Promise.all(vals).then((values) => {
                const result = {};
                for (const i in values) result[keys[i]] = values[i];
                resolve(result);
            });
        });
    }
};
