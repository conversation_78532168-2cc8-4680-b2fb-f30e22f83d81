"use strict";
const crypto = require("crypto");
const moment = require("moment");
const currency = require("currency.js");
const numeral = require("numeral");
const _ = require("lodash");

const { OrderTemplateType } = require("@dispense/settings");
class SharedCache {
    constructor() {
        this.cache = {};
    }

    async get(key) {
        return this.cache[key];
    }

    async set(key, value) {
        this.cache[key] = value;
    }
}

class ClaraError extends Error {
    constructor(humanMessage, originalError = null) {
        super(humanMessage);
        this.humanMessage = humanMessage;
        this.originalError = originalError;
    }

    getErrorInfo() {
        return {
            humanMessage: this.humanMessage,
            message: this.message,
            stack: this.stack,
            originalError: this.originalError
                ? {
                      message: this.originalError.message,
                      stack: this.originalError.stack,
                  }
                : null,
        };
    }

    get message() {
        return this.humanMessage;
    }
}

module.exports = class FxClass {
    getContextCache(ctx) {
        if (!ctx.sharedCache) {
            ctx.sharedCache = new SharedCache();
        }
        return ctx.sharedCache;
    }

    getClaraError(msg) {
        return new ClaraError(msg);
    }

    deepFind(obj, key) {
        if (_.has(obj, key)) {
            return _.get(obj, key);
        }
        for (const k in obj) {
            if (_.isObject(obj[k])) {
                const result = this.deepFind(obj[k], key);
                if (result !== undefined) return result;
            }
        }
        return undefined;
    }

    /**
     * Wraps an error in a ClaraError instance.
     * @param {Error} error - The original error to wrap.
     * @param {string} [humanMessage="An error occurred. Please contact support if issues persist."] - A human-readable error message.
     * @returns {ClaraError} A new ClaraError instance or the original error if it's already a ClaraError.
     */
    wrapError(
        error,
        humanMessage = "An error occurred. Please contact support if issues persist."
    ) {
        if (error instanceof ClaraError) {
            return error;
        } else {
            console.error(`Error: ${error.message} Stack: ${error.stack}`);
            return new ClaraError(humanMessage, error);
        }
    }

    /**
     * Sets the error message in the response context.
     * @param {Object} ctx - The context object.
     * @param {Error} error - The error object.
     * @param {string} [humanMessage="An error occurred. Please contact support if issues persist."] - A human-readable error message.
     */
    setReturnErrorMessage(
        ctx,
        error,
        humanMessage = "An error occurred. Please contact support if issues persist."
    ) {
        ctx.body = this.getReturnErrorMessage(error, humanMessage);
        ctx.status = 500;
    }

    /**
     * Generates an error message object based on the provided error and human-readable message.
     * @param {Error} error - The error object to process.
     * @param {string} [humanMessage="An error occurred. Please contact support if issues persist."] - A human-readable error message.
     * @returns {Object} An object containing the error message and stack trace.
     */
    getReturnErrorMessage(
        error,
        humanMessage = "An error occurred. Please contact support if issues persist."
    ) {
        if (error instanceof ClaraError) {
            return {
                error: error.message?.replace(/null/g, "(New Record)"),
                stack: error.stack,
            };
        } else {
            return { error: humanMessage, stack: error.stack };
        }
    }
    funcParamsToString(args) {
        try {
            const argsArray = Array.prototype.slice.call(args);
            const errorMetaData = JSON.stringify(argsArray);
            return errorMetaData;
        } catch (e) {
            return `Unable to parse function args. Error:${e.message}`;
        }
    }
    md5(input) {
        return crypto.createHash("md5").update(input).digest("hex");
    }

    /**
     * Recursively removes a specified key from an object or array.
     * @param {Object|Array} obj - The object or array to remove the key from.
     * @param {string} keyToRemove - The key to remove from the object or array.
     * @returns {Object|Array} - A new object or array with the specified key removed.
     */
    removeKey(obj, keyToRemove, formName) {
        if (typeof keyToRemove === "string") keyToRemove = [keyToRemove];
        if (!Array.isArray(keyToRemove)) return obj;
        if (!obj || typeof obj !== "object") {
            return obj;
        }
        if (Array.isArray(obj)) {
            return obj.map((item) =>
                this.removeKey(item, keyToRemove, formName)
            );
        }
        const newObj = { ...obj };
        for (const ktr of keyToRemove) {
            delete newObj[ktr];
        }
        for (const key in newObj) {
            if (
                !(
                    this.shared.DSL[formName]?.fields[key]?.model.type ===
                    "subform"
                )
            )
                continue;
            newObj[key] = this.removeKey(
                newObj[key],
                keyToRemove,
                this.shared.DSL[formName]?.fields[key].model.source
            );
        }
        return newObj;
    }

    // Function to generate a random string
    generateRandomString(length = 32) {
        return crypto.randomBytes(length).toString("hex");
    }

    // Function to create a token with a random string and TTL
    createToken(randomString, ttl) {
        const data = `${randomString}:${ttl}`;
        const hash = crypto.createHash("sha256").update(data).digest("hex");
        const token = Buffer.from(`${data}:${hash}`).toString("base64");
        return token;
    }

    parseJSONString(value, defaultValue = []) {
        let parsedValue = defaultValue;
        if (value && typeof value === "string") {
            try {
                parsedValue = JSON.parse(value);
            } catch (e) {
                console.log(
                    `Error parsing JSON string: ${e.message} Value: ${value}`
                );
                // Do nothing
            }
        } else if (Array.isArray(value) && Array.isArray(defaultValue)) {
            parsedValue = value;
        } else if (
            typeof value === "object" &&
            typeof defaultValue === "object"
        ) {
            parsedValue = value;
        }
        return parsedValue;
    }

    // Function to extract the random string and TTL from the token
    extractTokenData(token) {
        const decoded = Buffer.from(token, "base64").toString("utf-8");
        const [randomString, ttl, hash] = decoded.split(":");
        // Verify the hash
        const data = `${randomString}:${ttl}`;
        const expectedHash = crypto
            .createHash("sha256")
            .update(data)
            .digest("hex");
        if (hash !== expectedHash) {
            throw new Error("Invalid token");
        }
        // Check if the token was created within the last 5 minutes
        const currentTime = Math.floor(Date.now() / 1000);
        const tokenAge = currentTime - ttl;
        const fiveMinutesInSeconds = 5 * 60;
        if (tokenAge > fiveMinutesInSeconds) {
            throw new Error("Reset Token Expired");
        }
        return randomString;
    }

    /**
     * Checks if the provided value is a number or a string representation of a number.
     * @param {any} value - The value to check.
     * @returns {boolean} - True if the value is a number, false otherwise.
     */
    isNumber(value) {
        if (typeof value === "number") {
            return true;
        }
        if (typeof value === "string") {
            return !isNaN(value) && !isNaN(parseFloat(value));
        }
        return false;
    }

    isAsyncFunction(obj) {
        return (
            obj && obj.constructor && obj.constructor.name === "AsyncFunction"
        );
    }

    uuidv4() {
        return "1000000010004000800010000001048004000800010000000".replace(
            /[018]/g,
            (c) =>
                (
                    c ^
                    (crypto.getRandomValues(new Uint8Array(1))[0] &
                        (15 >> (c / 4)))
                ).toString(16)
        );
    }

    injectStringAfterSubstring(originalString, substring, stringToInject) {
        const index = originalString.indexOf(substring);
        if (index === -1) {
            return originalString;
        }
        const indexAfterSubstring = index + substring.length;
        const result =
            originalString.slice(0, indexAfterSubstring) +
            stringToInject +
            originalString.slice(indexAfterSubstring);
        return result;
    }

    arraysEqual(a, b) {
        a.sort();
        b.sort();
        if (a === b) return true;
        if (a == null || b == null) return false;
        if (a.length !== b.length) return false;
        for (let i = 0; i < a.length; ++i) {
            if (a[i] !== b[i]) return false;
        }
        return true;
    }

    validateTimestamp(dt, format) {
        if (!format) return false;
        if (["ISO_8601", "RFC_2822"].includes(format))
            return moment(dt, moment[format], true).isValid();
        return moment(dt, format, true).isValid();
    }

    // First > 2nd/now == true
    firstGtSecond(date, now = null) {
        const format = "MM/DD/YYYY";
        const err = {
            error: true,
            msg: "Invalid Date, Required format is MM/DD/YYYY",
        };
        if (!this.validateTimestamp(date, format)) return err;
        if (!now) now = moment().format(format);
        else if (!this.validateTimestamp(now, format)) return err;
        return moment(date, format).diff(moment(now, format)) > 0;
    }

    isToday(date) {
        const now = moment();
        const format = "MM/DD/YYYY";
        const err = {
            error: true,
            msg: "Invalid Date, Required format is MM/DD/YYYY",
        };
        if (!this.validateTimestamp(date, format)) return err;
        return moment(date, format).diff(now) > 0;
    }

    greaterDate(date, now = null) {
        const format = "MM/DD/YYYY";
        const err = {
            error: true,
            msg: "Invalid Date, Required format is MM/DD/YYYY",
        };
        if (!this.validateTimestamp(date, format)) return err;
        if (!now) now = moment().format(format);
        else if (!this.validateTimestamp(now, format)) return err;
        if (this.firstGtSecond(date, now)) return date;
        else return now;
    }

    formatDateTime(dt) {
        return moment(dt, moment.defaultFormat).format("MM/DD/YYYY hh:mm:ss A");
    }

    formatDate(dt) {
        return moment(dt, moment.defaultFormat).format("MM/DD/YYYY");
    }

    formatDecimal(d, z) {
        return (Math.round(d * Math.pow(10, z)) / Math.pow(10, z)).toFixed(z);
    }

    formatTime(dt) {
        return moment("2001-01-01 " + dt, "YYYY-MM-DD HH:mm:ss").format(
            "hh:mm A"
        );
    }

    formatCurrency(num) {
        const str = currency(num);
        return str;
    }

    formatDecimalComas(d, z) {
        const x = this.formatDecimal(d, z);
        const parts = x.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
    }
    roundTo(val, rounding) {
        if (val == null) {
            return "";
        }
        const v = parseFloat(val);
        if (isNaN(v)) {
            return 0;
        }
        const r = Math.round(v / rounding) * rounding;
        return Math.round(r * 100000) / 100000;
    }

    timediff(d1, d2) {
        let de, ds, te, ts, tt;
        tt = 0;
        ts = d1.trim().toLowerCase();
        te = d2.trim().toLowerCase();

        if (ts.indexOf("pm") > -1 && te.indexOf("am") > -1) {
            ts = "1/1/2001 " + ts;
            te = "1/2/2001 " + te;
        } else {
            ts = "1/1/2001 " + ts;
            te = "1/1/2001 " + te;
            ds = new Date(ts);
            de = new Date(te);
            tt += (de.getTime() - ds.getTime()) / 3600 / 1000;
        }

        tt = this.roundTo((tt + 24) % 24, 0.01);
        if (tt === 0) {
            tt = "";
        }
        return tt;
    }

    logJson(j) {
        try {
            console.log(JSON.stringify(j, null, 4));
        } catch {
            console.log(j);
        }
    }

    /**
     * Checks if all required parameters are in the request
     * @param {Object} keys - A key value pair of keys to check and value are the proper name for the param in the error response if missing
     * @param {Object} ctx - The context object, sets the status to 500 and puts the error in the body
     * @param {Boolean} [check_body] - By default checks the query params but if true, checks the body of the request
     * @returns {Boolean}
     * @public
     */
    checkParameters(keys, ctx, check_body = false) {
        if (!keys || !ctx) throw new Error("Missing keys or context object");
        const params =
            (check_body ? ctx.request.body : Object.assign({}, ctx.query)) ||
            null;
        for (const [key, value] of Object.entries(keys)) {
            if (!(key in params)) {
                ctx.status = 500;
                ctx.body = { error: "Missing " + value };
                return false;
            }
        }
        return true;
    }

    /**
     * Checks if the user has access to the specified forms based on the provided authorization module.
     * @param {Object} user - The user object to check access for.
     * @param {Object} forms - The forms to check access against.
     * @param {Object} auth - The authorization module to verify access.
     * @param {Object} ctx - The session context
     * @throws {Error} If any of the parameters are missing.
     */
    checkFormAccess(user, forms, auth, ctx) {
        if (!user || !forms || !auth) throw new Error("Missing parameter");
        for (const key of Object.keys(forms)) {
            if (!user || !auth.can_access_form(ctx, key)) {
                ctx.status = 403;
                ctx.body = { error: "Unauthorized access" };
                return false;
            }
        }
        return true;
    }

    /**
     * This function takes in a json string array (i.e. "{'A','B','C'}") and converts it to a real array.
     * @param {String} string - The keys to check
     * @returns {Array} - Returns array
     * @public
     */
    jsonStrArrayToArray(string) {
        if (Array.isArray(string)) return string;
        if (
            string &&
            _.isString(string) &&
            string.includes("{") &&
            string.includes("}")
        ) {
            string = string.replace(/^\{\s*|\s*\}$/g, ""); // Remove curly braces and trim whitespace
            return string
                .split(/\s*,\s*/)
                .map((s) => s.replace(/^"|"$/g, "").trim());
        }
        return string;
    }

    /**
     * Escapes an XML string
     * @param {String} unsafe - The unsafe string
     * @returns {String} The escaped XML string
     * @public
     */
    escapeXml(unsafe) {
        if (!_.isString(unsafe)) return unsafe;
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&apos;");
    }

    /**
     * Removes null fields/properties from the objects
     * handles nested properties recursively
     * @param {Object} object - The object to process
     * @public
     */
    removeNulls(object) {
        if (!object) return;
        return _.transform(
            object,
            (result, value, key) => {
                if (Array.isArray(value)) {
                    const cleanedArray = value
                        .map((item) => {
                            if (_.isObject(item)) {
                                return this.removeNulls(item);
                            }
                            return item;
                        })
                        .filter((item) => !this.isEmpty(item));
                    if (cleanedArray.length > 0) {
                        result[key] = cleanedArray;
                    }
                } else if (_.isObject(value)) {
                    const cleaned = this.removeNulls(value);
                    if (!_.isEmpty(cleaned)) {
                        result[key] = cleaned;
                    }
                } else if (
                    value !== null &&
                    value !== "" &&
                    !(Array.isArray(value) && value.length === 0)
                ) {
                    result[key] = value;
                }
            },
            {}
        );
    }

    /**
     * Checks if the provided value is considered empty.
     * A value is considered empty if it is null.
     *
     * @param {*} value - The value to check.
     * @param {Boolean} [include_empty_strings] - Whether to treat empty strings as nulls
     * @returns {boolean} - Returns true if the value is considered empty, otherwise false.
     */
    isEmpty(value, include_empty_strings = true) {
        if (value === null) return true;
        if (include_empty_strings && value === "") return true;
        if (Array.isArray(value) && value.length === 0) return true;
        if (typeof value === "object" && Object.keys(value).length === 0)
            return true;
        return false;
    }

    /**
     * Removes falsy fields/properties from the objects
     * handles nested properties recursively
     * @param {Object} object
     * @public
     */
    removeFalsy(object) {
        if (!object) return;
        Object.keys(object).forEach((key) => {
            if (object[key] && typeof object[key] === "object") {
                this.removeFalsy(object[key]);
            } else if (!object[key]) {
                delete object[key];
            }
        });
    }

    /**
     * This function is used to make the string a sentence type case (first letter of each word capitalized)
     * @param {string} str - The string to convert
     * @returns {string} - The new string
     * @public
     */

    toSentenceCase(str) {
        if (!str || str.length < 1) return null;
        return str.replace(/(^\w{1})|(\s+\w{1})/g, (letter) =>
            letter.toUpperCase()
        );
    }

    /**
     * Fetches form IDs from transaction results for a specific form name.
     * @param {string} formName - The name of the form to filter results for.
     * @param {Array<Array<Object>>} results - The transaction results to process.
     * @returns {Array<number>} An array of form IDs matching the specified form name.
     */
    fetchFormIdsFromTransactionResults(formName, results) {
        if (!formName || !results)
            throw new Error(
                `Missing formName ${formName} or results ${results}`
            );
        return _.flatten(results)
            .filter((rec) => rec.formname.replace(/_\d+$/, "") === formName)
            .reduce((acc, rec) => {
                if (rec.id) acc.push(rec.id);
                return acc;
            }, []);
    }

    /**
     * Builds a string representation of units and unit code.
     * @async
     * @param {Object} ctx - The context object.
     * @param {number|string} units - The number of units.
     * @param {string} unitCode - The code representing the unit of measurement.
     * @returns {string} A formatted string combining units and unit code.
     */
    async buildUnitsAndUnitCodeString(ctx, units, unitCode) {
        if (!units || !unitCode) return "(Missing units or unit code)";
        const unitCodeMapRec = _.head(
            await this.nes.shared.form.get.get_form(
                ctx,
                ctx.user,
                "list_unit_display_map",
                {
                    filter: [`code:${unitCode}`],
                }
            )
        );

        const unitsVal = parseFloat(units);
        const unitsCodeFormatted =
            unitsVal > 1 ? unitCodeMapRec?.plural_name || unitCode : unitCode;
        return this.formatUnitAndCode(unitsVal, unitsCodeFormatted);
    }

    async hasChanged(ctx, formData, form, field) {
        /*** ONLY USE FOR BEFORE SAVE ***/
        /*** Use for simple fields only. Not for subform,gerund etc. ***/
        try {
            if (
                !formData.id ||
                (typeof formData.id == "string" &&
                    formData.id.startsWith("psqlVar"))
            ) {
                // POST case
                return true;
            }
            let prev = await this.nes.shared.form.get.get_form(
                ctx,
                ctx.user,
                form,
                {
                    filter: [`id:${formData.id}`],
                }
            );
            prev = prev[0];
            if (prev[field] != formData[field]) {
                return true;
            }
            return false;
        } catch (error) {
            console.error(error);
            throw new Error("Error at hasChanged");
        }
    }

    /**
     * Gets the plural form of a unit string based on the unit code.
     * @async
     * @param {Object} ctx - The context object.
     * @param {string} unitCode - The code representing the unit of measurement.
     * @returns {Promise<string>} The plural form of the unit string, or the original unit code if no plural form exists.
     */
    async getPluralUnitString(ctx, unitCode) {
        const unitDisplayMap = await this.nes.shared.form.get.get_form(
            ctx,
            ctx.user,
            "list_unit_display_map",
            { filter: [`code:${unitCode}`] }
        );
        return unitDisplayMap[0]?.plural_name || unitCode;
    }

    /**
     * Formats a quantity and unit into a standardized string representation.
     * @param {number} quantity - The numeric value to format.
     * @param {string} unit - The unit of measurement.
     * @returns {string} A formatted string combining the quantity and unit.
     */
    formatUnitAndCode(quantity, unit) {
        return `${numeral(quantity).format("0,0.[000000]")} ${unit}`;
    }

    /**
     * Gets the display name for an prescription based on its template type.
     * For reconstituted items, returns brand group or brand name.
     * For other items, returns inventory name or brand name.
     * @param {Object} orderItem - The prescription object
     * @param {string} [orderItem.template_type] - The template type of the prescription
     * @param {string} [orderItem.brand_group_id_auto_name] - The brand group auto name
     * @param {string} [orderItem.brand_name_id_auto_name] - The brand name auto name
     * @param {string} [orderItem.inventory_id_auto_name] - The inventory auto name
     * @returns {string} The display name for the prescription
     */
    getOrderItemName(orderItem) {
        if (orderItem?.template_type === OrderTemplateType.RECONSTITUTED) {
            return (
                orderItem?.brand_group_id_auto_name ||
                orderItem?.brand_name_id_auto_name
            );
        }
        return (
            orderItem?.inventory_id_auto_name ||
            orderItem?.brand_name_id_auto_name
        );
    }

    /**
     * Picks auto-name fields from a form record based on the form CSON and additional specified fields.
     * @async
     * @param {Object} formCSON - The CSON (Coffee Script Object Notation) representation of the form.
     * @param {Object} formRec - The form record object.
     * @param {string[]} [otherFields=[]] - Additional fields to pick from the form record.
     * @returns {Promise<Object>} An object containing the picked fields.
     * @throws {Error} If formCSON or formRec is missing.
     */
    async pickAutoNameFields(formCSON, formRec, otherFields = []) {
        if (!formCSON || !formRec)
            throw new Error(
                `Missing formCSON ${formCSON} or formRec ${formRec}`
            );
        const nameFields = formCSON.model?.name || [];
        return _.pick(formRec, "id", ...nameFields, ...otherFields);
    }

    /**
     * Groups an array of objects by specified properties and sums a numeric property for each group.
     * @param {Array} array - The array of objects to group and sum.
     * @param {string} sumProperty - The property name whose values will be summed for each group.
     * @param {...string} groupByProperties - One or more property names to group by.
     * @returns {Object} An object with nested properties representing the groups and their sums.
     */
    async groupByAndSum(array, sumProperty, ...groupByProperties) {
        const result = array.reduce((acc, item) => {
            let current = acc;
            for (let idx = 0; idx < groupByProperties.length; idx++) {
                const prop = groupByProperties[idx];
                const value = item[prop];
                if (!current[value]) {
                    current[value] =
                        idx === groupByProperties.length - 1 ? 0 : {};
                }
                if (idx === groupByProperties.length - 1) {
                    current[value] += parseFloat(item[sumProperty]);
                } else {
                    current = parseFloat(current[value]);
                }
            }
            return acc;
        }, {});

        return result;
    }

    /**
     * Filters out zero quantities from a nested object structure.
     * @param {Object} obj - The object to filter.
     * @param {string} sumProperty - The property name whose values will be checked for zero.
     * @returns {Array} An array of objects with non-zero quantities and their paths.
     */
    async filterZeroQuantities(obj, sumProperty) {
        const recurse = (current) => {
            if (typeof current !== "object") {
                return current[sumProperty] !== 0 ? current : undefined;
            }

            const result = {};
            for (const [key, value] of Object.entries(current)) {
                const filtered = recurse(value);
                if (filtered !== undefined) {
                    result[key] = filtered;
                }
            }
            return Object.keys(result).length > 0 ? result : undefined;
        };

        const filteredResult = recurse(obj);

        return filteredResult;
    }

    /**
     * Adds days out to expire information to the given object.
     * @async
     * @param {Object} nes - The NES object containing shared libraries.
     * @param {Object} obj - The object to process.
     * @param {string} form - The form name to query.
     * @param {number} keyLevel - The level in the object hierarchy to process.
     * @param {string} filterProp - The property to use for filtering the form.
     * @returns {Promise<Object>} A promise that resolves to an object with the processed result.
     */
    async addDaysOutToExpire(nes, obj, form, keyLevel, filterProp) {
        const formLib = nes.shared.form;

        const processObject = async (current, path = []) => {
            if (typeof current !== "object" || current === null) {
                if (path.length === keyLevel) {
                    const id = path[path.length - 1];
                    const formRec = await formLib.get.get_form(
                        this.ctx,
                        this.ctx.user,
                        form,
                        { filter: `${filterProp}:${id}` }
                    );

                    if (
                        formRec &&
                        formRec.length > 0 &&
                        formRec[0].expiration_date
                    ) {
                        const expirationDate = moment(
                            formRec[0].expiration_date,
                            "MM/DD/YYYY"
                        );
                        const daysOut = expirationDate.diff(moment(), "days");
                        return {
                            quantity: current,
                            expireDaysOut: daysOut,
                        };
                    }
                }
                return { quantity: current };
            }

            const result = {};
            for (const [key, value] of Object.entries(current)) {
                result[key] = await processObject(value, [...path, key]);
            }
            return result;
        };
        return await processObject(obj);
    }

    /**
     * Reduces the object by grouping items into buckets based on their days out to expire.
     * @async
     * @param {Object} obj - The object to reduce, containing items with quantity and expireDaysOut properties.
     * @param {number} bucketSize - The size of each bucket in days.
     * @returns {Promise<Object>} A promise that resolves to an object with items grouped by buckets.
     */
    async reduceByDaysOutSize(obj, bucketSize) {
        const reduced = {};
        const processObject = (current, path = []) => {
            if (typeof current === "object" && current !== null) {
                if ("quantity" in current && "expireDaysOut" in current) {
                    const bucket =
                        Math.ceil(current.expireDaysOut / bucketSize) *
                        bucketSize;
                    const id = path[0];

                    if (!reduced[id]) reduced[id] = {};
                    if (!reduced[id][bucket]) reduced[id][bucket] = 0;
                    reduced[id][bucket] += current.quantity;
                } else {
                    for (const [key, value] of Object.entries(current)) {
                        processObject(value, [...path, key]);
                    }
                }
            }
        };

        processObject(obj);

        return reduced;
    }

    /**
     * Resolves auto_name for nested form objects with various source configurations
     * @param {Object} shared - The shared object containing shared libraries.
     * @param {Object} ctx - Context object containing user and other necessary information
     * @param {Object} formObject - The deeply nested object to resolve
     * @param {string} form - The form name to resolve
     * @returns {Promise<Object>} Resolved object with added auto_name properties
     */
    async resolveFormAutoNames(shared, ctx, formObject, form) {
        // Helper function to recursively resolve auto names
        async function processObject(obj, formDefinition) {
            // Create a copy of the object to avoid mutation
            const resolvedObject = _.cloneDeep(obj);

            // Ensure we have a fields definition
            if (!formDefinition || !formDefinition.fields) {
                return resolvedObject;
            }

            // Parallel promises for async resolution
            const autoNamePromises = Object.entries(formDefinition.fields)
                .filter(
                    ([_fieldName, fieldDef]) =>
                        fieldDef.model &&
                        fieldDef.model.source &&
                        fieldDef.model.type !== "subform"
                )
                .map(async ([fieldName, fieldDef]) => {
                    const { source, sourceid } = fieldDef.model;
                    const objectValue = resolvedObject[fieldName];

                    // If no value, skip
                    if (objectValue === undefined || objectValue === null) {
                        return null;
                    }

                    // Handle array and single value cases
                    const valueArray = Array.isArray(objectValue)
                        ? objectValue
                        : [objectValue];

                    // Resolve auto names for each value
                    const autoNames = await Promise.all(
                        valueArray.map(async (value) => {
                            try {
                                const filters = [`${sourceid}:${value}`];
                                const sourceFilter =
                                    fieldDef.model?.sourcefilter || {};
                                const sourceFilterKeys =
                                    Object.keys(sourceFilter);
                                if (sourceFilterKeys.length > 0) {
                                    sourceFilterKeys.forEach((key) => {
                                        const filterConfig = sourceFilter[key];
                                        if (filterConfig.dynamic) {
                                            const values = Array.isArray(
                                                filterConfig.dynamic
                                            )
                                                ? filterConfig.dynamic
                                                : [filterConfig.dynamic];

                                            for (const val of values) {
                                                const match =
                                                    val.match(/\{(.*?)\}/);
                                                if (match) {
                                                    const objKey = match[1];
                                                    if (
                                                        objectValue[objKey] ===
                                                        undefined
                                                    ) {
                                                        return null;
                                                    }
                                                    filters.push(
                                                        `${key}:${objectValue[objKey]}`
                                                    );
                                                }
                                            }
                                        } else if (filterConfig.static) {
                                            const values = Array.isArray(
                                                filterConfig.static
                                            )
                                                ? filterConfig.static
                                                : [filterConfig.static];
                                            values.forEach((val) => {
                                                filters.push(`${key}:${val}`);
                                            });
                                        }
                                    });
                                }
                                const record = _.head(
                                    await shared.form.get.get_form(
                                        ctx,
                                        ctx.user,
                                        source,
                                        {
                                            filter: filters,
                                        }
                                    )
                                );
                                return record ? record.auto_name : null;
                            } catch (error) {
                                console.error(
                                    `Error resolving auto_name for ${fieldName}:`,
                                    error
                                );
                                return null;
                            }
                        })
                    );

                    // Add auto names to the object
                    const autoNameKey = `${fieldName}_auto_name`;
                    resolvedObject[autoNameKey] = Array.isArray(objectValue)
                        ? autoNames
                        : autoNames[0];

                    return null;
                });

            // Wait for all auto name resolutions
            await Promise.all(autoNamePromises);

            // Recursively process subforms
            for (const [fieldName, fieldDef] of Object.entries(
                formDefinition.fields
            )) {
                if (
                    fieldDef.model &&
                    fieldDef.model.type === "subform" &&
                    fieldDef.model.source
                ) {
                    const subformDef = shared.DSL[fieldDef.model.source];

                    // Handle array of subforms
                    if (Array.isArray(resolvedObject[fieldName])) {
                        resolvedObject[fieldName] = await Promise.all(
                            resolvedObject[fieldName].map((subObj) =>
                                processObject(subObj, subformDef)
                            )
                        );
                    }
                    // Handle single subform object
                    else if (resolvedObject[fieldName]) {
                        resolvedObject[fieldName] = await processObject(
                            resolvedObject[fieldName],
                            subformDef
                        );
                    }
                }
            }

            return resolvedObject;
        }

        // Start the resolution process
        return processObject(formObject, shared.DSL[form]);
    }

    /**
     * Retrieves information about the caller of the current function.
     * @param {number} [level] - The level of the caller to retrieve. Defaults to 2.
     * @returns {Object} An object containing information about the caller:
     *   - functionName: The name of the calling function
     *   - className: The name of the class containing the calling function
     *   - fileName: The name of the file containing the calling function
     *   - lineNumber: The line number where the function call occurred
     */
    getCallerInfo(level = 2) {
        const originalPrepareStackTrace = Error.prepareStackTrace;

        Error.prepareStackTrace = (err, stack) => stack;
        const err = new Error();
        const stack = err.stack;

        Error.prepareStackTrace = originalPrepareStackTrace;

        // stack[0] is this function itself, stack[1] is the caller
        if (level >= stack.length) level = stack.length - 1;
        const caller = stack[level];
        const callerFunctionName = caller.getFunctionName();
        const callerClassName = caller.getTypeName();
        const callerFileName = caller.getFileName();
        const callerLineNumber = caller.getLineNumber();

        return {
            functionName: callerFunctionName,
            className: callerClassName,
            fileName: callerFileName,
            lineNumber: callerLineNumber,
        };
    }

    /**
     * Validates a property value against a given schema.
     * @async
     * @param {string} propName - The name of the property to validate.
     * @param {Object} schema - The Joi schema to validate against.
     * @param {*} propValue - The value of the property to validate.
     * @param {string} [errorMessage] - The error message to throw if validation fails.
     * @param {Object} [options] - The options to pass to Joi.validate.
     * @throws {Error} If the validation fails or if the input parameters are invalid.
     */
    async validateSchema(
        propName,
        schema,
        propValue,
        errorMessage = null,
        options = { abortEarly: false, allowUnknown: true }
    ) {
        if (typeof propName !== "string") {
            throw this.getClaraError("propName must be a string");
        }

        if (!schema || typeof schema.validate !== "function") {
            throw this.getClaraError("schema must be a valid Joi schema");
        }

        const { error: resultError, value } = schema.validate(
            propValue,
            options
        );
        if (resultError) {
            const callerInfo = this.getCallerInfo(2);
            const calleeInfo = this.getCallerInfo(3);
            const filename = calleeInfo.fileName?.split("/")?.pop() || null;
            const errorHeader = `The following callee [${filename}:${calleeInfo.lineNumber}] property [${propName}] failed schema validation for caller: [${callerInfo.className}.${callerInfo.functionName}]\n`;
            const humanReadableError = await this.__parseJoiError(resultError);
            console.error(errorHeader + humanReadableError);
            const errMessage =
                errorMessage ??
                humanReadableError ??
                "Data validation failure. Please contact support.";
            throw this.getClaraError(errMessage);
        }
        return value;
    }

    async __parseJoiError(error) {
        const humanMessages = error.details.map(
            (detail) =>
                `${detail.message}: Found Value ${detail.value ? detail.value : "empty"}`
        );
        return humanMessages.join("\n");
    }

    /**
     * Validates function arguments against required properties.
     * @param {string} propName - The name of the property to validate.
     * @param {Object} propValue - The value of the property to validate.
     * @param {Object} [requiredProps] - An array of required property names.
     * @param {boolean} [optional] - Whether the property is optional.
     * @returns {boolean} True if all required properties are present.
     * @throws {Error} If any required properties are missing.
     */
    async validateProperty(
        propName,
        propValue,
        requiredProps = [],
        optional = false
    ) {
        const missingProps = [];

        // Check if the property itself is missing
        if (propValue === undefined && !optional) {
            missingProps.push(propName);
        } else if (Array.isArray(propValue)) {
            // Check if the array is empty
            if (propValue.length === 0) {
                missingProps.push(`${propName} (array is empty)`);
            } else {
                // Check each object in the array for required properties
                propValue.forEach((item, index) => {
                    if (requiredProps && requiredProps.length > 0) {
                        for (const requiredProp of requiredProps) {
                            if (!(requiredProp in item)) {
                                missingProps.push(
                                    `${propName}[${index}].${requiredProp}`
                                );
                            }
                        }
                    }
                });
            }
        } else if (requiredProps && requiredProps.length > 0 && !optional) {
            // Check for missing required sub-properties in non-array objects
            for (const requiredProp of requiredProps) {
                const fullPropPath = `${propName}.${requiredProp}`;
                if (!(requiredProp in propValue)) {
                    missingProps.push(fullPropPath);
                }
            }
        } else if (
            optional &&
            propValue !== undefined &&
            requiredProps &&
            requiredProps.length > 0
        ) {
            // If optional and the property is provided, check its sub-properties
            for (const requiredProp of requiredProps) {
                const fullPropPath = `${propName}.${requiredProp}`;
                if (!(requiredProp in propValue)) {
                    missingProps.push(fullPropPath);
                }
            }
        }

        // If there are missing properties, throw an error
        if (missingProps.length > 0) {
            const callerInfo = this.getCallerInfo(2);
            const calleeInfo = this.getCallerInfo(3);

            const errorMessage = `The following callee [${calleeInfo.className}.${calleeInfo.functionName}] property [${propName}] failed validation for caller: [${callerInfo.className}.${callerInfo.functionName}] : Error: ${missingProps.join(", ")}`;
            throw this.getClaraError(errorMessage);
        }
        return true;
    }

    /**
     * Formats an array as a PostgreSQL array string.
     * @param {Array} arr - The array to format.
     * @returns {string} The formatted PostgreSQL array string.
     */
    pgArrayFormat(arr) {
        if (!Array.isArray(arr) || arr.length === 0) return "{}";
        return "{" + arr.join(",") + "}";
    }
};
