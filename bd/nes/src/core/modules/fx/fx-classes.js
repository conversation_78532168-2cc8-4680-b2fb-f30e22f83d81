"use strict";

module.exports = class FxClass {
    /**
     * This function is used to dynamically get a property from a target object.
     * It is assumed data will be loaded in the .data property and will attempt
     * to fetch the property from there.
     * @param {Object} target - The target object
     * @returns {Proxy} - A proxy object with a get trap
     * @public
     */
    dynamicGet(target) {
        return new Proxy(target, {
            get(tgt, prop, receiver) {
                const value = Reflect.get(tgt, prop, receiver);

                // If the value is a promise (like from init), ensure it is handled correctly
                if (value instanceof Promise) {
                    return value.then((resolvedValue) => {
                        // Additional logic if needed when promise resolves
                        return resolvedValue;
                    });
                }

                // Existing logic for handling getters or data properties
                const descriptor = Object.getOwnPropertyDescriptor(
                    tgt.constructor.prototype,
                    prop
                );
                if (descriptor && typeof descriptor.get === "function") {
                    return value;
                }
                if (tgt.data && prop in tgt.data) {
                    return tgt.data[prop];
                }
                return value;
            },
        });
    }

    /**
     * Retrieves an instance of a class from the cache or creates a new one if it doesn't exist.
     * @param {Object} ctx - The context containing the cache
     * @param {Function} Class - The class of the instance to retrieve or create
     * @param {Boolean} useSubClass - Flag to determine if subclass checking is needed
     * @param {...any} args - Arguments to pass to the class constructor if a new instance is created
     * @returns {Object} The instance of the class
     */
    getInstance(ctx, Class, useSubClass, ...args) {
        if (!ctx?.cache) {
            ctx.cache = new Map();
        }

        const cache = ctx.cache;
        // First, try to find an exact match in the cache
        if (cache.has(Class)) {
            console.log("Returning cached instance of", Class.name);
            return cache.get(Class);
        }

        if (useSubClass) {
            // If no exact match, check for the most specific subclass instance
            let mostSpecificSubclassInstance = null;
            for (const [key, value] of cache) {
                if (value instanceof Class) {
                    if (
                        !mostSpecificSubclassInstance ||
                        key.prototype instanceof
                            mostSpecificSubclassInstance.constructor
                    ) {
                        mostSpecificSubclassInstance = value;
                    }
                }
            }

            if (mostSpecificSubclassInstance) {
                console.log(
                    "Returning cached subclass instance of",
                    mostSpecificSubclassInstance.constructor.name
                );
                return mostSpecificSubclassInstance;
            }
        }

        // If no instance is found, create a new one
        const instance = new Class(...args);
        cache.set(Class, instance);
        console.log("Creating new instance of", Class.name);
        return instance;
    }
};
