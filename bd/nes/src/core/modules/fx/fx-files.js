"use strict";

const fs = require("fs");
const fetch = require("node-fetch");
const gm = require("gm");
module.exports = class FxClass {
    getStat(filePath) {
        try {
            return fs.statSync(filePath);
        } catch (error) {
            throw new Error(error);
        }
    }

    async getDownloadFileStream(url) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
        }
        return response.body;
    }

    async downloadAndStreamFile(ctx, url) {
        const downloadStream = await this.fx.getDownloadFileStream(url);
        const writeStream = this.fx.getWriteStream(this.fileOnServer);
        for await (const chunk of downloadStream) {
            ctx.body.write(chunk);
            writeStream.write(chunk);
        }
        writeStream.end();
        return;
    }

    async deleteFileIfExists(path) {
        if (path && path !== "")
            if (await this.exists(path)) this.deleteFile(path);
            else console.log(path + " does not exist");
    }

    deleteFile(path) {
        try {
            fs.unlinkSync(path);
            console.log(path + " Deleted");
        } catch (error) {
            throw new Error(error);
        }
    }

    getReadStream(dest) {
        const stream = fs.createReadStream(dest).on("error", (error) => {
            throw new Error(error);
        });
        return stream;
    }

    getWriteStream(dest) {
        return fs.createWriteStream(dest);
    }

    async exists(path) {
        return new Promise(function (resolve, _) {
            fs.access(path, fs.constants.F_OK, (err) => {
                if (err) resolve(false);
                resolve(true);
            });
        });
    }

    async getModuleSource(callpath) {
        if (callpath.length < 2 || callpath[0] != "api")
            return [null, null, true];

        // validate path
        const mods = [];
        for (const m of callpath.slice(1))
            if (/^([a-z0-9_-]{1,32})$/i.test(m)) {
                mods.push(m);
            } else break;

        // return deepest module with .process() that exists for customer or base
        const base_js = global.nesroot + "src/api/";
        for (let i = mods.length; i > 0; i--) {
            try {
                const mod_call = mods.slice(0, i).join("/");
                // TRY: /print/cms-485, /print/cms-485/index, /print/cms-485/cms-485
                for (const mod_path of [base_js + mod_call])
                    for (const mod_sub of [
                        "",
                        "/index",
                        "/" + mods.slice(i - 1, i),
                    ]) {
                        if (await this.exists(mod_path + mod_sub + ".js")) {
                            const mod_obj = require(mod_path + mod_sub);
                            // only return modules that can run, not ones that simply extend
                            if ("process" in mod_obj.prototype)
                                return [
                                    mod_obj,
                                    mod_path + mod_sub,
                                    mod_path === base_js + mod_call,
                                ];
                        }
                    }
            } catch (e) {
                console.error(e);
            }
        }

        return [null, null, true];
    }

    /**
     * Asynchronously retrieves the contents of a directory at the specified path.
     *
     * @param {string} path - The path to the directory to retrieve the contents of.
     * @returns {Promise<string[]>} - A promise that resolves to an array of file and directory names in the specified path.
     */
    async get_folder_contents(path) {
        return await new Promise((resolve) => {
            fs.readdir(path, (err, data) => {
                if (err) resolve([]);
                else resolve(data);
            });
        });
    }

    async readFile(path) {
        return new Promise(function (resolve, _) {
            fs.readFile(path, (error, result) => {
                if (error) {
                    resolve({
                        result: "error",
                        data: error,
                    });
                } else {
                    resolve({
                        result: "success",
                        data: result,
                    });
                }
            });
        });
    }

    async copyFile(source, destination) {
        return new Promise(function (resolve, _) {
            fs.copyFile(source, destination, (error) => {
                if (error) {
                    resolve({
                        result: "error",
                        data: error,
                    });
                } else {
                    resolve({
                        result: "success",
                        data: destination,
                    });
                }
            });
        });
    }

    async getPages(inputFile) {
        return new Promise((resolve, reject) => {
            gm(inputFile).identify((err, data) => {
                if (err) return reject(err);
                const depth = data.Depth;
                const pages = Array.isArray(depth)
                    ? depth.length < 9
                        ? depth.length
                        : 9
                    : 1;
                resolve(pages);
            });
        });
    }
    async convertFileToImage(inputFile, outputPath, hash) {
        try {
            const pages = await this.getPages(inputFile);
            const images = {
                thumbnail: "",
                images: [],
            };

            // Create thumbnail if it's the first page
            if (pages > 0) {
                const thumb = await this.createThumbnail(
                    inputFile,
                    outputPath,
                    hash
                );
                if (thumb) {
                    images.thumbnail = thumb;
                }
            }

            // Process all pages
            const imagePromises = [];
            for (let i = 0; i < pages; i++) {
                const path = `${outputPath}/${hash}-images/${hash}-${i + 1}.png`;
                imagePromises.push(
                    this.createImage(`${inputFile}[${i}]`, path).then(
                        () => `${hash}/${i + 1}.png`
                    )
                );
            }

            // Wait for all images to be processed
            const processedImages = await Promise.all(imagePromises);
            images.images.push(...processedImages);
            return images;
        } catch (err) {
            console.error("Error converting file to image:", err);
            return [];
        }
    }

    async createImage(inputFile, outputPath) {
        return new Promise((resolve, reject) => {
            gm(inputFile)
                .density(150, 150)
                .quality(100)
                .write(outputPath, (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(true);
                    }
                });
        });
    }
    async createThumbnail(inputFile, outputPath, hash) {
        return new Promise((resolve, reject) => {
            gm(`${inputFile}[0]`)
                .density(40, 40)
                .quality(75)
                .resize(150, 150)
                .stream("png", function (err, stdout, stderr) {
                    if (err) {
                        reject(err);
                    }
                    const writeStream = fs.createWriteStream(
                        `${outputPath}/${hash}-images/${hash}-thumbnail.png`
                    );
                    stdout.pipe(writeStream);
                    resolve(`${hash}/thumbnail.png`);
                });
        });
    }
};
