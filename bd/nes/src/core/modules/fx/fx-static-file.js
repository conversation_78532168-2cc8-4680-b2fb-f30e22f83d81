"use strict";

const fs = require("fs");
const { S3 } = require("@aws-sdk/client-s3");
const stream = require("stream");
const { promisify } = require("util");

const pipeline = promisify(stream.pipeline);
module.exports = class FxClass {
    async getFile(hash) {
        this.s3 = new S3({
            credentials: {
                accessKeyId: this.shared.config.env["AWS_ACCESS_KEY"],
                secretAccessKey: this.shared.config.env["AWS_SECRET_KEY"],
            },
            region: this.shared.config.env["AWS_REGION"],
        });
        const params = {
            Bucket: this.shared.config.env["AWS_S3_BUCKET"],
            Key: hash,
        };
        try {
            return new Promise((resolve, reject) => {
                try {
                    this.s3.getObject(params, function (err, data) {
                        if (err) {
                            console.error(err);
                            reject(err);
                        } else {
                            resolve(data);
                        }
                    });
                } catch (e) {
                    console.log(e);
                }
            });
        } catch (e) {
            console.info(
                `Error occured while retrieving file from s3: ${hash}\n${e}`
            );
        }
    }

    async download(hash, ctx) {
        const { key, cachePath, cacheFile } = this.getFileKeyPath(
            "static",
            hash
        );
        const jsonFile = `${cacheFile}.json`;
        let jsonData = "";
        let metadata = "";
        let filename = "";
        let mime = "";
        if (!fs.existsSync(cachePath)) {
            fs.mkdirSync(cachePath, { recursive: true });
        }
        if (fs.existsSync(cacheFile)) {
            // Serve the existing file
            jsonData = fs.readFileSync(jsonFile, "utf8");
            metadata = JSON.parse(jsonData);
            filename = metadata.filename;
            mime = metadata.mimeType;
            this.set_headers(ctx, filename, mime);
            ctx.status = 200;
            ctx.body = fs.readFileSync(cacheFile);
            return;
        }
        // Fetch the file if not already local
        const file = await this.getFile(key);
        // Save the image data to the local file system
        await this.writeFileToFS(
            file.Body,
            { filename: file.Metadata.filename, mimeType: file.ContentType },
            cacheFile
        );
        this.set_headers(ctx, file.Metadata.filename, file.ContentType);
        ctx.status = 200;
        ctx.body = fs.readFileSync(cacheFile);
    }

    set_headers(ctx, filename, mimeType) {
        ctx.set("Content-Disposition", "attachment ; filename=" + filename);
        ctx.set("Content-Type", mimeType);
    }

    getFileKeyPath(type, hash) {
        const slug = hash.substring(0, 2);
        return {
            key: `${type}/${slug}/${hash}`,
            cachePath: `/tmp/${type}/${slug}`,
            cacheFile: `/tmp/${type}/${slug}/${hash}`,
        };
    }

    async writeFileToFS(file, json, path) {
        const chunks = [];
        await pipeline(
            file,
            new stream.Writable({
                write(chunk, encoding, callback) {
                    chunks.push(chunk);
                    callback();
                },
            })
        );

        const buffer = Buffer.concat(chunks);
        fs.writeFileSync(path, buffer);
        fs.writeFileSync(`${path}.json`, JSON.stringify(json)); // Save metadata
    }

    write_resp(callback, callback_id, data) {
        let response = "";
        if (!callback || !callback_id) {
            response = data;
        } else {
            response =
                "<script>" +
                callback +
                "('" +
                callback_id +
                "'," +
                data.toString() +
                ");</script>";
        }
        return response;
    }
};
