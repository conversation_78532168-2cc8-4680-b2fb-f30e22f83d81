"use strict";
module.exports = class FaxQueue {
    constructor(nes) {
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
    }

    /**
     * All the functions that are assosiated with events DSL can subscribe to
     * by adding event field on field or form. If the event name is some_event
     * then worker should be called worker_some_event.
     * @param {Object} ctx: admin ctx
     * @param {Object} job: {id,data}
     */
    worker_ledger_event = async (ctx, job) => {
        console.log("worker_ledger_event called");
    };
};
