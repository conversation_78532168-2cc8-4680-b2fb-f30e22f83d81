"use strict";

/**
 * @class
 * @classdesc DME rental billing manager with pgboss workers
 */
module.exports = class DmeManager {
    constructor(nes) {
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.nes = nes;
        this.auth = nes.modules.auth;
    }

    /**
     * DME rental billing worker - processes rentals due for billing
     * @param {Object} ctx: admin ctx
     * @param {Object} job: {id,data}
     */
    worker_dme_rental_billing = async (ctx, job) => {
        console.log("DME rental billing worker started:", job.id);

        try {
            const { data } = job;

            if (!data || data.action !== "process_dme_rental_billing") {
                throw new Error("Invalid job data for DME rental billing");
            }

            // Get all rentals due for billing with charge line data
            const sql = `
                SELECT 
                    rental_log_id,
                    patient_name,
                    equipment_name,
                    service_from_date,
                    service_to_date,
                    create_dme_rental_charge_line(
                        rental_log_id,
                        service_from_date::date,
                        service_to_date::date
                    ) as charge_line_data
                FROM vw_dme_rentals_due_for_billing
                WHERE due_for_billing = TRUE
                ORDER BY next_billing_date, patient_id
            `;

            const rentalsDue = await this.db.env.rw.query(sql);

            if (rentalsDue.length === 0) {
                console.log("No DME rentals due for billing at this time");
                return {
                    success: true,
                    message: "No rentals due for billing",
                    processed: 0,
                };
            }

            console.log(
                `Found ${rentalsDue.length} DME rentals due for billing`
            );

            const results = [];
            const transaction = this.db.env.rw.transaction(ctx);

            for (const rental of rentalsDue) {
                try {
                    console.log(
                        `Processing rental ${rental.rental_log_id} for patient ${rental.patient_name}`
                    );

                    if (!rental.charge_line_data) {
                        throw new Error(
                            `Failed to generate charge line data for rental ${rental.rental_log_id}`
                        );
                    }

                    const chargeNo = transaction.series_next_number("CHARGE");

                    // Extract charge line data and add required fields
                    const chargeLineData = {
                        ...rental.charge_line_data,
                        date_of_service: rental.service_from_date,
                        date_of_service_end: rental.service_to_date,
                        charge_no: chargeNo,
                    };

                    // Remove the id fields for insertion
                    delete chargeLineData.id;

                    // Insert the charge line
                    const chargeLineId = await transaction.insert(
                        "ledger_charge_line",
                        chargeLineData
                    );

                    results.push({
                        rental_log_id: rental.rental_log_id,
                        patient_name: rental.patient_name,
                        equipment_name: rental.equipment_name,
                        charge_line_id: chargeLineId,
                        service_from_date: rental.service_from_date,
                        service_to_date: rental.service_to_date,
                        status: "success",
                    });

                    console.log(
                        `Successfully created charge line ${chargeLineId} for rental ${rental.rental_log_id}`
                    );
                } catch (error) {
                    console.error(
                        `Error processing rental ${rental.rental_log_id}:`,
                        error
                    );

                    results.push({
                        rental_log_id: rental.rental_log_id,
                        patient_name: rental.patient_name,
                        equipment_name: rental.equipment_name,
                        error: error.message,
                        status: "error",
                    });
                }
            }

            // Commit all successful charge lines
            const commitResult = await transaction.commit();
            if (commitResult.error) {
                throw new Error(
                    `Transaction commit failed: ${commitResult.message}`
                );
            }

            const successCount = results.filter(
                (r) => r.status === "success"
            ).length;
            const errorCount = results.filter(
                (r) => r.status === "error"
            ).length;

            console.log(
                `DME rental billing complete: ${successCount} successful, ${errorCount} errors`
            );

            return {
                success: true,
                message: `Processed ${rentalsDue.length} rentals: ${successCount} successful, ${errorCount} errors`,
                processed: successCount,
                errors: errorCount,
                results: results,
            };
        } catch (error) {
            console.error("Error in DME rental billing worker:", error);
            console.error("Job details:", {
                job_id: job.id,
                job_data: job.data,
                error_stack: error.stack,
            });
            throw error;
        }
    };
}; 