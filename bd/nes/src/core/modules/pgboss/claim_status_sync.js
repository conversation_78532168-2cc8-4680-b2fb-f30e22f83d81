"use strict";

/**
 * @class
 * @classdesc Claim substatus sync if on-hold for DOS with pgboss workers
 */
module.exports = class ClaimStatusSync {
    constructor(nes) {
        this.shared = nes.shared;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.nes = nes;
        this.auth = nes.modules.auth;
    }

    /**
     * Claim status sync
     * @param {Object} ctx: admin ctx
     * @param {Object} job: {id,data}
     */
    worker_claim_status_sync = async (ctx, job) => {
        console.log("Claim status sync worker started:", job.id);

        try {
            const { data } = job;

            if (!data || data.action !== "process_claim_status_sync") {
                throw new Error("Invalid job data for claim status sync");
            }

            // Get all claims that are on-hold for DOS
            const sql = `
                SELECT DISTINCT
                    'med_claim' as form_name,
                    claim.claim_no,
                    claim.id as claim_id
                FROM form_med_claim claim
                INNER JOIN form_billing_invoice inv ON inv.invoice_no = claim.invoice_no 
                AND COALESCE(inv.void, 'No') = 'No'
                AND COALESCE(inv.zeroed, 'No') = 'No'
                AND inv.deleted IS NOT TRUE
                AND inv.archived IS NOT TRUE
                LEFT JOIN LATERAL (
                   SELECT MAX(COALESCE(sl.service_date_end,sl.service_date))::date as sd
                   FROM form_med_claim_sl sl 
                   WHERE sl.claim_no = claim.claim_no 
                   AND sl.archived IS NOT TRUE
                   AND sl.deleted IS NOT TRUE 
                ) max_dt ON TRUE
                WHERE claim.substatus_id = '114'
                AND claim.deleted IS NOT TRUE
                AND claim.archived IS NOT TRUE
                AND max_dt.sd <= CURRENT_TIMESTAMP::date

                UNION ALL

                SELECT DISTINCT
                    'med_claim_1500' as form_name,
                    claim.claim_no,
                    claim.id as claim_id
                FROM form_med_claim_1500 claim
                INNER JOIN form_billing_invoice inv ON inv.invoice_no = claim.invoice_no
                AND inv.deleted IS NOT TRUE
                AND inv.archived IS NOT TRUE
                AND COALESCE(inv.void, 'No') = 'No'
                AND COALESCE(inv.zeroed, 'No') = 'No'
                LEFT JOIN LATERAL (
                   SELECT MAX(COALESCE(sl.service_date_end,sl.service_date))::date as sd
                   FROM form_med_claim_1500_sl sl 
                   INNER JOIN sf_form_med_claim_1500_to_med_claim_1500_sl sfsl 
                   ON sfsl.form_med_claim_1500_sl_fk = sl.id 
                   AND sfsl.form_med_claim_1500_fk = claim.id
                   WHERE sl.archived IS NOT TRUE
                   AND sl.deleted IS NOT TRUE 
                ) max_dt ON TRUE
                WHERE claim.substatus_id = '114'
                AND claim.deleted IS NOT TRUE
                AND claim.archived IS NOT TRUE
                AND max_dt.sd <= CURRENT_TIMESTAMP::date
            `;

            const claims = await this.db.env.rw.query(sql);

            if (claims.length === 0) {
                console.log("No claims on hold for DOS at this time");
                return {
                    success: true,
                    message: "No rentals due for billing",
                    processed: 0,
                };
            }

            console.log(`Found ${claims.length} claims on hold for DOS`);

            const results = [];
            const transaction = this.db.env.rw.transaction(ctx);

            for (const claim of claims) {
                try {
                    console.log(
                        `Processing claim substatus sync for ${claim.claim_no}`
                    );

                    // Update the claim substatus to ready for billing
                    await transaction.update(
                        claim.form_name,
                        { substatus_id: "101" },
                        claim.claim_id
                    );

                    results.push({
                        claim_no: claim.claim_no,
                        claim_id: claim.claim_id,
                        status: "success",
                    });

                    console.log(
                        `Successfully updated claim substatus for ${claim.claim_no}`
                    );
                } catch (error) {
                    console.error(
                        `Error processing claim substatus sync for ${claim.claim_no}:`,
                        error
                    );

                    results.push({
                        claim_no: claim.claim_no,
                        claim_id: claim.claim_id,
                        error: error.message,
                        status: "error",
                    });
                }
            }

            // Commit all successful claims
            const commitResult = await transaction.commit();
            if (commitResult.error) {
                throw new Error(
                    `Transaction commit failed: ${commitResult.message}`
                );
            }

            const successCount = results.filter(
                (r) => r.status === "success"
            ).length;
            const errorCount = results.filter(
                (r) => r.status === "error"
            ).length;

            console.log(
                `DME rental billing complete: ${successCount} successful, ${errorCount} errors`
            );

            return {
                success: true,
                message: `Processed ${claims.length} claims: ${successCount} successful, ${errorCount} errors`,
                processed: successCount,
                errors: errorCount,
                results: results,
            };
        } catch (error) {
            console.error("Error in claim status sync worker:", error);
            console.error("Job details:", {
                job_id: job.id,
                job_data: job.data,
                error_stack: error.stack,
            });
            throw error;
        }
    };
}; 