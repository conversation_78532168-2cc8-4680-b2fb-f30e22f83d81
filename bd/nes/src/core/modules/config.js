"use strict";
const fs = require("fs");
const _ = require("lodash");
const { URL } = require("url");
const process = require("process");
const fetch = require("node-fetch");

const REQUIRED_ENV_VARS = [
    "AWS_ACCESS_KEY",
    "AWS_REGION",
    "AWS_S3_BUCKET",
    "AWS_SECRET_KEY",
    "BASE_URL",
    "DATABASE_URL",
    "EMAIL_API_KEY",
    "FLY_ID",
    "FLY_NES",
    "NES_API_KEY",
    "NODE_ENV",
    "SHIPPO_API_TOKEN",
];

const OPTIONAL_ENV_VARS = [
    "AUTHNET_API_ID",
    "AUTHNET_API_KEY",
    "AUTHNET_API_TN",
    "BTREE_API_ID",
    "BTREE_API_KEY",
    "BTREE_API_TN",
    "BTREE_API_TOKEN",
    "OAUTH_AZ_ID",
    "OAUTH_AZ_TN",
    "OAUTH_CR_ID",
    "OAUTH_GC_ID",
    "RC_API_ID",
    "RC_API_KEY",
    "STRIPE_API_ID",
    "STRIPE_API_KEY",
    // Needed for BI fly app
    "SS_EMBED_USER",
    "SS_GUEST_TOKEN_JWT_SECRET",
    "SS_GUEST_TOKEN_JWT_AUDIENCE",
];

const QUEUE_HANDLERS = [
    "@api/query/save",
    "@api/billing/close",
    "@core/modules/pgboss/ledger_workers",
    "@core/modules/pgboss/dme_manager",
    "@core/modules/pgboss/claim_status_sync",
];

module.exports = class NESConfig {
    constructor(nes) {
        this.shared = nes.shared;
    }

    async init() {
        this.load_env();
        this.shared.BASE_URL =
            this.shared.config.env["BASE_URL"] ||
            `https://${this.shared.config.env["FLY_ID"]}.fly.dev`;

        this.shared.SUPERSET_URL = `https://${this.shared.config.env["FLY_ID"].split("-nes")[0]}-bi.fly.dev`;
        await this.load_admin_console_config(
            this.shared.customer,
            this.shared.clara_env
                ? this.shared.clara_env
                : this.shared.config.env["NODE_ENV"]
        );
        try {
            const package_json = require("@root/package.json");
            Object.assign(this.shared.config.package, package_json);

            if (!fs.existsSync(this.shared.config.nes.tempdir)) {
                fs.mkdirSync(this.shared.config.nes.tempdir);
            }

            this.shared.config.queues = QUEUE_HANDLERS;
        } catch (e) {
            console.log(e);
        }
    }

    async load_admin_console_config(customer, env) {
        try {
            // NODE_ENV and Admin Console names for dev/prod are not exactly the same so manually match them
            if (env == "development") env = "dev";
            else if (env == "production") env = "prod";

            const admin_path = `?slug=${customer}&fly_nes=${this.shared.config.env["FLY_NES"]}&env=${env}`;
            const res = await fetch(
                new URL(this.shared.NES_CONFIG_ENDPOINT + admin_path)
            );
            if (res.ok) {
                this.shared.config.admin_console = await res.json();
                if ("key_store" in this.shared.config.admin_console) {
                    const ks = {};
                    for (const kv of this.shared.config.admin_console
                        .key_store) {
                        ks[kv.key] = kv;
                    }
                    this.shared.config.admin_console.key_store = ks;
                }
                return true;
            } else {
                console.error(await res.text());
                process.exit(1);
            }
        } catch (e) {
            console.error(
                `Failed to load configuration from Admin Console!\n${e}`
            );
            process.exit(1);
        }
    }

    load_env() {
        const set_env = () => {
            REQUIRED_ENV_VARS.forEach((variable) => {
                if (!(variable in process.env) || !process.env[variable]) {
                    throw new Error(
                        `Required environment variable ${variable} is not set!`
                    );
                }
                this.shared.config.env[variable] = process.env[variable];
            });
            OPTIONAL_ENV_VARS.forEach((variable) => {
                if (variable in process.env && process.env[variable]) {
                    this.shared.config.env[variable] = process.env[variable];
                }
            });
        };

        console.log("    Setting up environment variables...");
        set_env();

        // test credentials are required for dev/test environments
        if (
            this.shared.config.env["NODE_ENV"] == "development" ||
            this.shared.config.env["NODE_ENV"] == "testing"
        ) {
            REQUIRED_ENV_VARS.push("TEST_USERNAME");
            REQUIRED_ENV_VARS.push("TEST_PASSWORD");
            set_env();
        }
    }
};
