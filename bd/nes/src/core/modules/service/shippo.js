"use strict";

const fetch = require("node-fetch");
const _ = require("lodash");
const QueryClass = require("@api/query/index");
const api = "https://api.goshippo.com";
const shipmentsApi = api + "/shipments";
const transactionApi = api + "/transactions";
module.exports = class ShippoLabelService {
    constructor(nes) {
        this.nes = nes;
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.query = new QueryClass(nes);
        this.shared = nes.shared;
        this.country = "US";

        // enforce shippo token usage
        this.enfforceShippoTokenUsage();
    }

    enfforceShippoTokenUsage = () => {
        if (
            this.shared.config.env["NODE_ENV"] !== "production" &&
            !this.shared.config.env["SHIPPO_API_TOKEN"].startsWith(
                "shippo_test"
            )
        ) {
            console.error(
                "You should use Shippo sandbox token with non-production environments."
            );
        } else if (
            this.shared.config.env["NODE_ENV"] === "production" &&
            this.shared.config.env["SHIPPO_API_TOKEN"].startsWith("shippo_test")
        ) {
            console.error(
                "You cannot use sandbox shippo token with production environments!"
            );
        }
        this.headers = {
            "Content-Type": "application/json",
            Authorization: `ShippoToken ${this.shared.config.env["SHIPPO_API_TOKEN"]}`,
        };
    };

    purchaseShipmentLabel = async (carrierId, metadata = "") => {
        try {
            const transactionData = {
                rate: carrierId,
                label_file_type: "PDF_4x6",
                metadata: metadata,
                async: false,
            };
            try {
                const response = await fetch(transactionApi, {
                    method: "POST",
                    headers: this.headers,
                    body: JSON.stringify(transactionData),
                });
                const res = await response.json();
                if (res.error) {
                    throw new Error(res.messages[0].text);
                }
                return res;
            } catch (error) {
                console.error("Error creating shipment:", error.message);
                return { error: true, message: error.message };
            }
        } catch (error) {
            console.error("Error creating shipment:", error.message);
            return { error: true, message: error.message };
        }
    };
    /**
     * Creates a shipment label using the Shippo API.
     *
     * @param {object} ctx - The context object.
     * @param {object} shipment - The shipment data.
     * @returns {Promise<object>} - The result of the shipment label creation operation.
     */
    getShipmentRates = async (ctx, patientId) => {
        try {
            const data = await this.prepareShipmentData(ctx, patientId);
            return this.getRates(data);
        } catch (error) {
            console.error("Error creating shipment:", error.message);
            return { error: true, message: error.message };
        }
    };

    /**
     * Asynchronously retrieves shipping rates from the shipments API.
     *
     * This function sends a POST request to the shipments API with the provided data.
     * It expects the data to be in a format that the API can process. The function
     * uses the `fetch` API for the network request, including setting the appropriate
     * HTTP method, headers, and body content based on the input data.
     *
     * @param {Object} data - The data to be sent to the shipments API. This should
     *                        include any necessary information required by the API
     *                        to calculate shipping rates, such as package dimensions,
     *                        weight, origin, and destination.
     *
     * @returns {Promise<Object>} A promise that resolves to the JSON response from the
     *                            shipments API. If the request is successful, this will
     *                            be the shipping rates and any related information provided
     *                            by the API. In case of an error (e.g., network issues,
     *                            invalid data), the promise resolves to an object containing
     *                            an `error` flag set to `true` and a `message` property with
     *                            the error description.
     *
     */
    getRates = async (data) => {
        try {
            const response = await fetch(shipmentsApi, {
                method: "POST",
                headers: this.headers,
                body: JSON.stringify(data),
            });
            return await response.json();
        } catch (error) {
            console.error("Error creating shipment:", error.message);
            return { error: true, message: error.message };
        }
    };
    /**
     * Prepares the shipment data for creating a shipment label.
     *
     * @param {object} ctx - The context object.
     * @param {object} shipment - The shipment data.
     * @returns {Promise<object>} - The prepared shipment data.
     */
    prepareShipmentData = async (ctx, deliveryTicketId) => {
        try {
            const { shipment_date, metadata } = ctx.request.body;
            const to = await this.getShipmentToAddress(ctx, deliveryTicketId);
            const from = await this.getShipmentFromAddress(
                ctx,
                deliveryTicketId
            );
            const parcel = ctx.request.body.parcel;
            return {
                address_to: to,
                address_from: from,
                parcels: parcel,
                async: false,
                shipment_date: shipment_date,
                metadata: metadata || "",
            };
        } catch (error) {
            console.error("Error preparing shipment data:", error.message);
            return { error: true, message: error.message };
        }
    };
    getShipmentToAddress = async (ctx, dtId) => {
        const dtData = _.head(
            await this.query.run_query(
                ctx,
                ctx.user,
                "careplan_delivery_ticket_ship_address",
                {
                    x1: dtId,
                }
            )
        );
        if (!dtData.delivery_ticket_id) {
            throw new Error("Delivery ticket not found");
        }
        const shipLocation = dtData.ship_location;
        if (shipLocation === "Home") {
            return {
                name: dtData.ship_to || dtData.patient_name,
                street1: dtData.ship_street,
                city: dtData.ship_city,
                state: dtData.ship_state,
                zip: dtData.ship_zip,
                country: this.country,
            };
        } else if (shipLocation === "Infusion Center") {
            return {
                name: dtData.ship_to || dtData.patient_name,
                street1: dtData.physician_address,
                city: dtData.physician_city,
                state: dtData.physician_state,
                zip: dtData.physician_zip,
                country: this.country,
            };
        } else {
            return {
                name: dtData.ship_to || dtData.patient_name,
                street1: dtData.site_address,
                city: dtData.site_city,
                state: dtData.site_state,
                zip: dtData.site_zip,
                country: this.country,
            };
        }
    };

    getShipmentFromAddress = async (ctx, deliveryTicketId) => {
        try {
            const filters = [`id:${deliveryTicketId}`];
            const deliveryTicketRec = _.head(
                await this.form.get.get_form(
                    ctx,
                    ctx.user,
                    "careplan_delivery_tick",
                    {
                        filter: filters,
                    }
                )
            );
            if (!deliveryTicketRec) {
                throw new Error("Delivery ticket not found");
            }
            const site_id = deliveryTicketRec.site_id;
            const siteRec = _.head(
                await this.form.get.get_form(ctx, ctx.user, "site", {
                    filter: [`id:${site_id}`],
                })
            );
            if (!siteRec) {
                throw new Error("Site not found");
            }
            return {
                name: siteRec.name,
                street1: siteRec.address1,
                street2: siteRec.address2,
                city: siteRec.city,
                state: siteRec.state_id,
                zip: siteRec.zip,
                country: this.country,
                phone: siteRec.phone,
            };
        } catch (error) {
            console.error(
                "Error getting shipment from address:",
                error.message
            );
            return { error: true, message: error.message };
        }
    };

    getParcel = async (ctx, dtId) => {
        return {
            length: "5",
            width: "5",
            height: "5",
            distance_unit: "in",
            weight: "2",
            mass_unit: "lb",
        };
    };

    async voidLabel(labelId) {
        const url = `${api}/refunds`;
        const data = { transaction: labelId };
        const response = await fetch(url, {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify(data),
        });
        return response.json();
    }
};
