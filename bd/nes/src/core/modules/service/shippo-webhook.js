const QueryClass = require("@api/query/index");

module.exports = class ShippoWebhookService {
    constructor(nes) {
        this.shared = nes.shared;
        this.auth = nes.modules.auth;
        this.form = nes.shared.form;
        this.db = nes.modules.db;
        this.query = new QueryClass(nes);
    }

    process = async (ctx) => {
        const body = ctx.request.body;
        const eventType = body.event;

        try {
            // Determine the database table and fields to update
            switch (eventType) {
                case "transaction_created":
                    // Handle transaction creation
                    break;

                case "transaction_updated": {
                    const {
                        object_id,
                        status,
                        tracking_number,
                        tracking_status,
                        rate,
                    } = body.data;
                    // Handle transaction update
                    await this.updateTransaction(
                        object_id,
                        status,
                        tracking_number,
                        tracking_status,
                        rate
                    );
                    break;
                }

                case "track_updated": {
                    const {
                        tracking_status: { status, object_id },
                    } = body.data;
                    await this.updateTracking(object_id, status);
                    break;
                }

                default:
                    console.warn(`Unhandled event type: ${eventType}`);
                    break;
            }

            ctx.status = 200;
            return true;
        } catch (error) {
            console.error("Error processing webhook:", error);
            ctx.status = 500;
            return false;
        }
    };

    updateTransaction = async (
        shipmentId,
        status,
        trackingNumber,
        trackingStatus,
        rateId
    ) => {
        try {
            const voided = status === "REFUNDED" ? "voided" : status;
            const query = `
                UPDATE form_shipment
                SET shipment_status = %L,
                    tracking_status = %L,
                    updated_on = NOW()
                WHERE rate_id = %L AND tracking_number =  %L AND shipment_id = %L
            `;
            const values = [
                voided,
                trackingStatus,
                rateId,
                trackingNumber,
                shipmentId,
            ];

            await this.db.env.rw.query(query, values);
            console.log(`Transaction ${shipmentId} updated successfully`);
        } catch (error) {
            console.error("Error updating transaction:", error);
            throw error;
        }
    };

    updateTracking = async (shipmentId, trackingStatus) => {
        try {
            const query = `
                UPDATE form_shipment
                SET tracking_status = %L,
                    updated_on = NOW()
                WHERE shipment_id = %L
            `;
            const values = [trackingStatus, shipmentId];

            await this.db.env.rw.query(query, values);
            console.log(
                `Tracking for shipment ${shipmentId} updated successfully`
            );
        } catch (error) {
            console.error("Error updating database:", error);
            throw error;
        }
    };
};
