"use strict";

const fetch = require("node-fetch");
const smtp2goUrl = "https://api.smtp2go.com";
const smtpV3 = smtp2goUrl + "/v3";
const mustache = require("mustache");
module.exports = class EmailNotificationClass {
    constructor(nes) {
        this.nes = nes;
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.form = nes.shared.form;
        this.shared = nes.shared;
    }

    /**
     * Sends an email using a predefined template.
     *
     * @param {object} ctx - The context object.
     * @param {object} mailList - The email recipient list.
     * @param {string} subject - The email subject.
     * @param {string} templateCode - The code of the email template to use.
     * @param {object} templateData - The data to populate the email template.
     * @returns {Promise<object>} - The result of the email send operation.
     */
    sendTemplateEmail = async (
        ctx,
        mailList,
        subject,
        templateCode,
        templateData
    ) => {
        const apiUrl = smtpV3 + "/email/send";
        mailList = this.hasEmailError(mailList, subject);
        try {
            if (mailList.error)
                throw new Error(
                    `Errors in emailOptions: ${mailList.errors.join(", ")}`
                );
            const data = await this.prepareEmailData(ctx, mailList, subject, {
                templateCode,
                templateData,
            });
            return await this.sendEmail(apiUrl, data);
        } catch (error) {
            console.error("Error sending email:", error.message);
            return { error: true, message: error.message };
        }
    };

    /**
     * Sends an email using the SMTP2GO API with a raw HTML body.
     *
     * @param {object} mailList - The email recipient list.
     * @param {string} subject - The email subject.
     * @param {string} html_body - The raw HTML content of the email body.
     * @returns {Promise<object>} - The result of the email send operation.
     */
    sendRawEmail = async (ctx, mailList, subject, html_body) => {
        const apiUrl = smtpV3 + "/email/send";
        mailList = this.hasEmailError(mailList, subject);
        if (mailList.error)
            throw new Error(
                `Errors in emailOptions: ${mailList.errors.join(", ")}`
            );
        const data = await this.prepareEmailData(ctx, mailList, subject, {
            html_body,
        });
        return await this.sendEmail(apiUrl, data);
    };

    /**
     * Sends an email using the SMTP2GO API.
     *
     * @param {string} apiUrl - The URL of the SMTP2GO API endpoint.
     * @param {object} data - The email data to be sent, including the recipient list, subject, and email body.
     * @returns {Promise<object>} - The result of the email send operation, including any errors.
     */
    async sendEmail(apiUrl, data) {
        const apiKey = this.shared.config.env["EMAIL_API_KEY"];
        try {
            const response = await fetch(apiUrl, {
                method: "POST",
                body: JSON.stringify(data),
                headers: {
                    "Content-Type": "application/json",
                    "X-Smtp2go-Api-Key": apiKey,
                },
            });

            if (!response.ok) {
                console.error(await response.json());
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log("Email sent successfully:", data.to.join(","));
            return result;
        } catch (error) {
            return { error: true, message: error.message };
        }
    }

    /**
     * Validates the email options provided in the `mailList` object, including the `to`, `cc`, and `bcc` fields.
     *
     * @param {object} mailList - The email options object, containing the `to`, `cc`, and `bcc` fields.
     * @param {string} subject - The email subject.
     * @returns {object} - An object containing the validated `mailList` or an `error` property with a list of validation errors.
     */
    hasEmailError(mailList, subject) {
        const errors = [];
        if (!this.shared.config.env["EMAIL_API_KEY"])
            errors.push("Missing required field: subject");
        // Validate required fields
        if (!subject) {
            errors.push("Missing required field: subject");
        } else if (typeof subject !== "string") {
            errors.push(
                `Expected subject to be of type String got: ${typeof subject}`
            );
        }
        if (!mailList["to"]) {
            errors.push("Missing required field: to");
        }
        // Validate to
        if (!Array.isArray(mailList.to)) {
            mailList.to = [mailList.to];
        }
        if (mailList.to.length < 1 || mailList.to.length > 100) {
            errors.push("to must contain between 1 and 100 email addresses");
        }
        mailList.to.forEach((email) => {
            if (typeof email !== "string") {
                errors.push(
                    "Expected email to must be a string got: " + typeof email
                );
            }
        });

        // Validate cc and bcc
        ["cc", "bcc"].forEach((field) => {
            if (mailList[field]) {
                if (!Array.isArray(mailList[field])) {
                    mailList[field] = [mailList[field]];
                }
                if (mailList[field].length > 100) {
                    errors.push(
                        `${field} must contain at most 100 email addresses`
                    );
                }
                mailList[field].forEach((email) => {
                    if (typeof email !== "string") {
                        errors.push(
                            `Each email in ${field} must be a string got: ${typeof email}`
                        );
                    }
                });
            }
        });
        return errors.length > 0 ? { error: true, errors: errors } : mailList;
    }

    /**
     * Prepares the email data for sending, including the sender, recipient, subject, and HTML body.
     *
     * @param {object} ctx - The context object.
     * @param {object} mailList - The email options object, containing the `to`, `cc`, and `bcc` fields.
     * @param {string} subject - The email subject.
     * @param {object} templateOptions - Options for the email template, including `html_body`, `templateCode`, and `templateData`.
     * @returns {object} - An object containing the prepared email data, including `sender`, `to`, `cc`, `bcc`, `html_body`, and `subject`.
     */
    async prepareEmailData(ctx, mailList, subject, templateOptions) {
        const data = {
            sender: "<EMAIL>",
            to: mailList.to,
        };
        if (templateOptions?.html_body) {
            data.html_body = templateOptions["html_body"];
            data.subject = subject;
        } else {
            const { templateCode, templateData } = templateOptions;
            if (!templateCode) {
                throw new Error("Missing required field: templateCode");
            }
            if (!templateData) {
                throw new Error("Missing required field: templateData");
            }
            // get email template via code, mustache data
            let emailTemplate = await this.form.get.get_form(
                ctx,
                ctx.user,
                "template_email",
                { limit: 1, filter: "code:" + templateCode }
            );
            if (!emailTemplate || emailTemplate?.length === 0) {
                throw new Error("Email Template not found");
            }
            emailTemplate = emailTemplate[0];
            data.html_body = this.mustacheParse(
                emailTemplate.template_html,
                templateData
            );
            data.subject = this.mustacheParse(emailTemplate.subject, {
                subject,
            });
        }
        if (mailList.cc) data.cc = mailList.cc;
        if (mailList.bcc) data.bcc = mailList.bcc;
        return data;
    }

    mustacheParse(template, data) {
        return mustache.render(template, data);
    }
};
