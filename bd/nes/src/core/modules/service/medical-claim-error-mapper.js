"use strict";
const _ = require("lodash");
const flat = require("flat");

const SchemaErrorMapperClass = require("@utils/schema-error-mapper");

/**
 * @class
 * @classdesc Class for mapping errors from Medical claims within the pharmacy billing module.
 * @extends SchemaErrorMapperClass
 */
module.exports = class MedicalClaimErrorMapperClass extends (
    SchemaErrorMapperClass
) {
    constructor(nes) {
        super(nes, "medical-claim-error-mapper");
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.mapping = {};
        this.schemaMapping = {};
    }

    /**
     * Initializes the Medical Electronic Claims Error Mapper.
     * @async
     * @returns {Promise<void>}
     * @throws {Error} If an error occurs during initialization.
     */
    async init() {
        super.log(`Initializing Medical Electronic Claims Error Mapper...`);

        try {
            await this.__buildMappingObject();
        } catch (error) {
            super.error(
                `Error initializing Medical Electronic Claims Error Mapper: ${error}`
            );
            throw error;
        }
        return this;
    }

    /**
     * Maps errors from the medical claims to their corresponding fields.
     * @async
     * @param {Array} errors - The array of error objects to be mapped.
     * @param {Object} medClaimRec - The medical claim record object.
     * @returns {Promise<Object>} The result object containing mapped errors.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async mapErrors(errors, medClaimRec) {
        super.log(`Mapping medical claim errors...`);
        try {
            const result = {};

            const mappedErrors = await Promise.all(
                errors.map(async (error) => {
                    const mappedField = await this.__findMappedField(error);
                    if (mappedField) {
                        super.debug(
                            `Field found in results, attempting to map...`
                        );
                        const res = await this.__setErrorInResult(
                            result,
                            mappedField,
                            medClaimRec,
                            error
                        );
                        if (!res) {
                            super.warn(
                                `Error setting error in result, cannot determine index level: ${JSON.stringify(
                                    error
                                )}`
                            );
                            return { path: "form", error: error.description };
                        }
                        return res;
                    } else {
                        super.warn(
                            `Unmapped medical claim error: ${JSON.stringify(
                                error
                            )}`
                        );
                        return { path: "form", error: error.description };
                    }
                })
            );

            for (const mappedError of mappedErrors) {
                const path = mappedError.path;
                const error = mappedError.error;
                _.set(result, path, [..._.get(result, path, []), error]);
            }
            const dslErrorObject = flat.unflatten(result, { object: true });
            return dslErrorObject;
        } catch (error) {
            super.error(
                `Error mapping errors: ${error}, errors: ${JSON.stringify(
                    errors
                )}`
            );
            throw error;
        }
    }

    /**
     * Builds the mapping object for medical claim errors.
     *
     * @async
     * @returns {Promise<Object>} The mapping object for medical claim errors.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async __buildMappingObject() {
        try {
            const medClaimJSON = this.shared.DSL["med_claim"];
            await this.__processForm("med_claim", medClaimJSON, "med_claim");
        } catch (error) {
            super.error(
                `Error building medical claim error mapping object: ${error}`
            );
            throw error;
        }
    }

    /**
     * Processes the form data and builds the mapping object.
     *
     * @async
     * @param {string} formName - The name of the form being processed.
     * @param {Object} formData - The data of the form being processed.
     * @param {string} [errorDSLPath=""] - The prefix for nested fields.
     * @param {string} [dslPath=""] - The prefix for nested fields.
     * @param {boolean} [underMultiSubform=false] - Whether the form is under a multi-subform.
     */
    async __processForm(
        formName,
        formData,
        errorDSLPath = "",
        dslPath = "",
        underMultiSubform = false
    ) {
        try {
            await Promise.all(
                Object.entries(formData.fields).map(
                    async ([fieldName, fieldData]) => {
                        if (fieldData.model.type === "subform") {
                            const subformName = fieldData.model.source;
                            const subformData = this.shared.DSL[subformName];

                            if (fieldData.model.multi) {
                                const nextErrorPath =
                                    errorDSLPath && underMultiSubform
                                        ? `${errorDSLPath}.${subformName}.__index_{idx}.${subformName}`
                                        : `${subformName}.__index_{idx}.${subformName}`;
                                const nextDSLPath = dslPath
                                    ? `${dslPath}.${fieldName}[*]`
                                    : `${fieldName}[*]`;

                                await this.__processForm(
                                    subformName,
                                    subformData,
                                    nextErrorPath,
                                    nextDSLPath,
                                    true
                                );
                            } else {
                                const nextErrorPath =
                                    errorDSLPath && underMultiSubform
                                        ? `${errorDSLPath}.${subformName}`
                                        : `${subformName}`;
                                const nextDSLPath = dslPath
                                    ? `${dslPath}.${fieldName}`
                                    : `${fieldName}`;

                                await this.__processForm(
                                    subformName,
                                    subformData,
                                    nextErrorPath,
                                    nextDSLPath,
                                    underMultiSubform
                                );
                            }
                        } else if (fieldData.view && fieldData.view._meta) {
                            const metaData = fieldData.view._meta;
                            const nextErrorPath = errorDSLPath
                                ? `${errorDSLPath}.${fieldName}`
                                : `${fieldName}`;
                            const nextDslPath = dslPath
                                ? `${dslPath}.${fieldName}`
                                : fieldName;
                            const metaDataPath = metaData.path || "";
                            this.schemaMapping[nextDslPath] = nextErrorPath;
                            await Promise.all([
                                this.__buildFieldKeysList(
                                    metaData,
                                    nextErrorPath,
                                    nextDslPath
                                ),
                                this.__buildPathIndexKeys(
                                    metaDataPath,
                                    nextErrorPath
                                ),
                            ]);
                        }
                    }
                )
            );
        } catch (error) {
            super.error(
                `Error processing form: ${error}, form name: ${formName}, error: ${error.description} stack: ${error.stack}`
            );
            throw error;
        }
    }

    /**
     * Finds the mapped field for a given error.
     *
     * @async
     * @param {Object} error - The error object containing field information.
     * @param {string} error.field - The field name or path.
     * @param {string} [error.location] - The location of the field (optional).
     * @param {string} [error.code] - The code associated with the field (optional).
     * @returns {Promise<Object|null>} The mapped field object or null if not found.
     * @throws {Error} If an error occurs while finding the mapped field.
     */
    async __findMappedField(error) {
        try {
            let key = null;
            if (error.field.includes(".")) {
                key = `P:${error.field}`;
            } else {
                key = `L${error.location || ""}.F${error.field || ""}.C${
                    error.code || ""
                }`;
            }

            return this.mapping[key] || null;
        } catch (error) {
            super.error(
                `Error finding mapped field: ${error}, error: ${JSON.stringify(
                    error
                )}`
            );
            throw error;
        }
    }

    /**
     * Sets an error message in the result object at the location specified by the mapped field.
     *
     * @async
     * @param {string} mappedField - The mapped dsl path for the error.
     * @param {Object} medClaimRec - The medical claim record object.
     * @param {Object} error - The error object containing the field, location, and code.
     */
    async __setErrorInResult(mappedField, medClaimRec, error) {
        try {
            let errorResultPath = `${mappedField}`;
            if (errorResultPath.includes("{idx}")) {
                super.debug(
                    `Medical Claim Error is in multi-field, attempting to find index(es).`
                );

                const { errorDslPath, dslPath } = mappedField;
                if (!dslPath) {
                    throw new Error(
                        `No dslPath object found for result ${JSON.stringify(
                            errorDslPath
                        )} with error: ${JSON.stringify(error)}`
                    );
                }
                errorResultPath = await this.__handleErrorWithMissingIndexes(
                    medClaimRec,
                    errorDslPath,
                    dslPath,
                    error
                );
                if (!errorResultPath) {
                    return false;
                }
            }

            return {
                path: errorResultPath,
                error: error.description,
            };
        } catch (error) {
            super.error(
                `Error setting error in result: ${error}, mapped field: ${mappedField}, error message: ${error.description}`
            );
            throw error;
        }
    }

    /**
     * Handles errors with missing indexes in multi-subform fields.
     *
     * @async
     * @param {Object} medClaimRec - The medical claim record object.
     * @param {string} errorDslPath - The DSL path for the error.
     * @param {string} dslPath - The DSL path for the field.
     * @param {Object} error - The error object.
     * @returns {Promise<string>} The updated error DSL path with resolved indexes.
     */
    async __handleErrorWithMissingIndexes(
        medClaimRec,
        errorDslPath,
        dslPath,
        error
    ) {
        super.debug(
            `Medical Claim Error is in multi-subform field with missing indexes, attempting to find index(es).`
        );
        try {
            const missingIndexCount = (errorDslPath.match(/{idx}/g) || [])
                .length;
            const asteriskCount = (dslPath.match(/\[\*\]/g) || []).length;

            if (missingIndexCount !== asteriskCount) {
                throw new Error(
                    `Mismatch between {idx} count in errorDslPath and [*] count in dslPath`
                );
            }

            let currentPath = "";
            let updatedErrorDslPath = errorDslPath;
            let updatedDslPath = dslPath;

            for (let idx = 0; idx < missingIndexCount; idx++) {
                const nextAsteriskIndex = updatedDslPath.indexOf("[*]");
                currentPath += updatedDslPath.slice(0, nextAsteriskIndex);

                const arrayAtPath = _.get(medClaimRec, currentPath);
                if (!arrayAtPath) {
                    throw new Error(
                        `Path ${currentPath} does not exist in medClaimRec`
                    );
                }

                if (!_.isArray(arrayAtPath)) {
                    throw new Error(
                        `Path ${currentPath} in medClaimRec is not an array`
                    );
                }

                if (arrayAtPath.length !== 1) {
                    super.debug(
                        `Path ${currentPath} has more than one element, scrubbing for value.`
                    );
                    const result = await this.__checkAllIndexes(
                        medClaimRec,
                        arrayAtPath,
                        currentPath,
                        updatedErrorDslPath,
                        updatedDslPath,
                        error
                    );
                    return result;
                }

                updatedErrorDslPath = _.replace(
                    updatedErrorDslPath,
                    "{idx}",
                    "0"
                );
                updatedDslPath = _.replace(updatedDslPath, "[*]", "[0]");
                currentPath += "[0]";
            }

            const valueAtIndex = _.get(medClaimRec, updatedDslPath);
            if (
                (valueAtIndex && valueAtIndex === error.value) ||
                (!valueAtIndex && !error.value)
            ) {
                return updatedErrorDslPath;
            } else {
                return null;
            }
        } catch (error) {
            super.error(
                `Error finding missing index(es) in error path: ${errorDslPath}, dsl path: ${dslPath}`
            );
            throw error;
        }
    }

    /**
     * Checks all indexes in an array for a matching error condition.
     * @async
     * @param {Object} medClaimRec - The medical claim record object.
     * @param {Array} arrayAtPath - The array to check indexes in.
     * @param {string} currentPath - The current path in the object structure.
     * @param {string} errorDslPath - The DSL path for the error.
     * @param {string} dslPath - The DSL path for the field.
     * @param {Object} error - The error object to match against.
     * @returns {Promise<string|null>} The updated error DSL path if a match is found, or null if no match.
     */
    async __checkAllIndexes(
        medClaimRec,
        arrayAtPath,
        currentPath,
        errorDslPath,
        dslPath,
        error
    ) {
        super.debug(
            `Checking all indexes for dsl path: ${dslPath} for error: ${JSON.stringify(
                error
            )}`
        );

        try {
            const checkPaths = [];
            for (let idx = 0; idx < arrayAtPath.length; idx++) {
                const pathToCheck = `${currentPath}[${idx}]`;
                const valueAtIndex = _.get(medClaimRec, pathToCheck);
                if (valueAtIndex) {
                    const newDslPath = _.replace(dslPath, `[*]`, `[${idx}]`);
                    const newErrorDslPath = _.replace(
                        errorDslPath,
                        `{idx}`,
                        `${idx}`
                    );
                    checkPaths.push(
                        this.__handleErrorWithMissingIndexes(
                            medClaimRec,
                            newErrorDslPath,
                            newDslPath,
                            error
                        )
                    );
                }
            }
            const results = await Promise.all(checkPaths);
            const result = results.find((result) => result !== null);
            return result || null;
        } catch (error) {
            super.error(
                `Error checking all indexes on dsl path: ${dslPath} for error: ${JSON.stringify(
                    error
                )}, error: ${JSON.stringify(error)}`
            );
            throw error;
        }
    }

    /**
     * Builds a list of field keys based on the provided metadata.
     * @param {Object} metaData - The metadata object containing location, field, and code information.
     * @param {string|string[]} metaData.location - The location(s) to be used in the keys.
     * @param {string|string[]} [metaData.field] - Optional field(s) to be included in the keys.
     * @param {string|string[]} [metaData.code] - Optional code(s) to be included in the keys.
     * @param {string} errorDslPath - The path used in the DSL.
     * @param {string} fieldDslPath - The path used in the DSL.
     * @returns {Promise<string[]>} A promise that resolves to an array of generated field keys.
     */
    async __buildFieldKeysList(metaData, errorDslPath, fieldDslPath) {
        try {
            const locations = _.castArray(metaData.location);
            const fields = metaData.field ? _.castArray(metaData.field) : [""];
            const codes = metaData.code ? _.castArray(metaData.code) : [""];

            _.flatMap(locations, (location) =>
                _.flatMap(fields, (field) =>
                    _.map(codes, (code) => {
                        const key = `L${location}${field ? `.F${field}` : ""}${code ? `.C${code}` : ""}`;
                        if (metaData?.type === "index") {
                            const codeIndex = codes.indexOf(code);
                            const lastIdxIndex = _.lastIndexOf(
                                errorDslPath,
                                "{idx}"
                            );
                            let newPath = errorDslPath;
                            if (lastIdxIndex !== -1) {
                                newPath = _.replace(
                                    errorDslPath,
                                    /{idx}/,
                                    codeIndex.toString(),
                                    lastIdxIndex
                                );
                            }
                            this.mapping[key] = {
                                errorDslPath: newPath,
                                dslPath: fieldDslPath,
                            };
                        }
                        return key;
                    })
                )
            );

            return;
        } catch (error) {
            super.error(
                `Error building keys list: ${error}, meta data: ${JSON.stringify(
                    metaData
                )}`
            );
            throw error;
        }
    }

    /**
     * Builds an array of path index keys based on the given path.
     * @param {string} path - The path containing index placeholders.
     * @param {string} dslErrorPath - The path used in the DSL.
     * @returns {Promise<string[]>} A promise that resolves to an array of path strings with index placeholders replaced.
     */
    async __buildPathIndexKeys(path, dslErrorPath) {
        try {
            const idxRegex = /\{idx(\d+)-(\d+)\}/g;
            const matches = [...path.matchAll(idxRegex)];

            if (matches.length === 0 && path?.length > 0) {
                this.mapping[`P:${path}`] = dslErrorPath;
                return;
            }

            const ranges = matches.map(([, start, end]) =>
                _.range(parseInt(start) - 1, parseInt(end))
            );
            const combinations = ranges.reduce(
                (acc, curr) => acc.flatMap((x) => curr.map((y) => [...x, y])),
                [[]]
            );

            await Promise.all(
                combinations.map(async (combo) => {
                    let result = path;
                    matches.forEach(([fullMatch], index) => {
                        result = result.replace(
                            fullMatch,
                            combo[index].toString()
                        );
                    });
                    this.mapping[`P:${result}`] = this.__replaceFullPathIndexes(
                        result,
                        dslErrorPath
                    );
                })
            );

            return;
        } catch (error) {
            super.error(
                `Error building path index keys: ${error}, path: ${path}`
            );
            throw error;
        }
    }

    /**
     * Replaces index placeholders in the full path with actual indices from the error path.
     * @async
     * @param {string} dslPath - The path containing actual indices (e.g., "[0]", "[1]").
     * @param {string} dslErrorPath - The full path with index placeholders ("{idx}").
     * @returns {Promise<string>} A promise that resolves to the updated full path with placeholders replaced by actual indices.
     * @throws {Error} If there are unmatched {idx} placeholders in the full path.
     */
    __replaceFullPathIndexes(dslPath, dslErrorPath) {
        try {
            let updatedErrorPath = dslErrorPath;

            const indices =
                dslPath
                    .match(/\[(\d+)\]/g)
                    ?.map((index) => index.slice(1, -1)) || [];

            let indexCounter = 0;
            updatedErrorPath = updatedErrorPath.replace(/\{idx\}/g, () => {
                if (indexCounter < indices.length) {
                    return indices[indexCounter++];
                }
                return "{idx}";
            });
            if (updatedErrorPath.includes("{idx}")) {
                throw new Error(
                    `Unmatched {idx} placeholders in dslPath: ${dslPath} for errorPath: ${dslErrorPath} updated error path: ${updatedErrorPath}`
                );
            }

            return updatedErrorPath;
        } catch (error) {
            super.error(
                `Error replacing full path indexes: ${error}, error path: ${dslPath}, full path: ${dslErrorPath}`
            );
            throw error;
        }
    }
};
