"use strict";
const _ = require("lodash");
const flat = require("flat");

const SchemaErrorMapperClass = require("@utils/schema-error-mapper");

/**
 * @class
 * @classdesc Class for mapping errors from Medical claims within the pharmacy billing module.
 * @extends SchemaErrorMapperClass
 */
module.exports = class NcpdpClaimErrorMapperClass extends (
    SchemaErrorMapperClass
) {
    constructor(nes) {
        super(nes, "ncpdp-claim-error-mapper");
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.nes = nes;
        this.form = nes.shared.form;
        this.mapping = {};
        this.schemaMapping = {};
        this.fieldReference = null;
        this.eclReference = null;
    }

    /**
     * Initializes the NCPDP Claims Error Mapper.
     * @async
     * @returns {Promise<void>}
     * @throws {Error} If an error occurs during initialization.
     */
    async init() {
        super.log(`Initializing NCPDP Claims Error Mapper...`);

        try {
            await this.__buildMappingObject();
        } catch (error) {
            super.error(
                `Error initializing NCPDP Claims Error Mapper: ${error}`
            );
            throw error;
        }
        return this;
    }

    /**
     * Maps reject codes to their corresponding ECL error messages.
     * @async
     * @param {Object} ctx - The context object.
     * @param {Array<Object>} rejectCodes - Array of objects containing reject_code property.
     * @returns {Promise<Array<string>>} Array of mapped error messages.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async mapErrorCodes(ctx, rejectCodes) {
        super.log(`Mapping pharmacy claim error codes...`);
        try {
            if (!this.fieldReference || !this.eclReference) {
                await Promise.all([
                    this.__buildFieldReferenceObject(ctx),
                    this.__buildECLReferenceObject(ctx),
                ]);
            }

            return rejectCodes.flatMap(({ reject_code: codes }) => {
                return codes.map((code) => {
                    const mappedECL = this.eclReference[code] || null;
                    if (!mappedECL) {
                        super.error(
                            `Unknown ECL Payer Reject Code ${code}. ECL may need updating.`
                        );
                        return `Unknown Error Code: ${code}`;
                    }
                    return mappedECL;
                });
            });
        } catch (error) {
            super.error(
                `Error mapping errors: ${error}, errors: ${JSON.stringify(
                    rejectCodes
                )}`
            );
            throw error;
        }
    }

    /**
     * Maps errors from the pharmacy claims to their corresponding fields.
     * @async
     * @param {Object} ctx - The context object.
     * @param {Array} rejectCodes - The array of error codes to be mapped.
     * @param {Array} formLevelErrorMessages - The last response message segment.
     * @param {Object} ncpdpRec - The NCPDP record containing the claim data.
     * @returns {Promise<Object>} The result object containing mapped errors.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async mapErrors(ctx, rejectCodes, formLevelErrorMessages, ncpdpRec) {
        super.log(`Mapping pharmacy claim errors...`);
        try {
            if (!this.fieldReference || !this.eclReference) {
                await Promise.all([
                    this.__buildFieldReferenceObject(ctx),
                    this.__buildECLReferenceObject(ctx),
                ]);
            }
            const result = {};

            const mappedErrors = await Promise.all(
                rejectCodes.flatMap(
                    async ({
                        reject_code: codes,
                        reject_field_occ_ind: location,
                    }) => {
                        const results = [];
                        for (const code of codes) {
                            const mappedField =
                                await this.__findMappedField(code);
                            const mappedECL = this.eclReference[code] || null;
                            if (!mappedECL) {
                                super.error(
                                    `Unknown ECL Payer Reject Code ${code}. ECL may need updating.`
                                );
                            }
                            const error =
                                mappedECL ||
                                `Unknown Rejection Code ${code}. Please review the payer response for additional information.`;
                            if (mappedField) {
                                const res = await this.__setErrorInResult(
                                    mappedField,
                                    ncpdpRec,
                                    location,
                                    error
                                );

                                if (!res) {
                                    super.warn(
                                        `Error setting error in result, cannot determine index level: ${JSON.stringify(
                                            error
                                        )}`
                                    );
                                    results.push({
                                        path: "form",
                                        error: error,
                                    });
                                    continue;
                                }
                                results.push(res);
                            } else {
                                super.info(
                                    `Unmapped ncpdp claim error: ${JSON.stringify(
                                        error
                                    )}`
                                );
                                results.push({
                                    path: "form",
                                    error: error,
                                });
                            }
                        }
                        return results;
                    }
                )
            );

            for (const message of formLevelErrorMessages) {
                const path = "form";
                const error = message;
                _.set(result, path, [..._.get(result, path, []), error]);
            }
            for (const mappedError of mappedErrors.flat()) {
                const path = mappedError.path;
                const error = mappedError.error;
                _.set(result, path, [..._.get(result, path, []), error]);
            }
            const dslErrorObject = {
                ...result,
                ...Object.fromEntries(
                    Object.entries(result)
                        .filter(([key]) => key !== "form")
                        .map(([key, value]) => [
                            key,
                            flat.unflatten({ [key]: value }, { object: true })[
                                key
                            ],
                        ])
                ),
            };
            return dslErrorObject;
        } catch (error) {
            super.error(
                `Error mapping errors: ${error}, errors: ${JSON.stringify(
                    rejectCodes
                )}`
            );
            throw error;
        }
    }

    /**
     * Builds the mapping object for pharmacy claim errors.
     *
     * @async
     * @returns {Promise<Object>} The mapping object for pharmacy claim errors.
     * @throws {Error} If an error occurs during the mapping process.
     */
    async __buildMappingObject() {
        try {
            const ncpdpJSON = this.shared.DSL["ncpdp"];
            await this.__processForm("ncpdp", ncpdpJSON, "ncpdp");
        } catch (error) {
            super.error(
                `Error building ncpdp claim error mapping object: ${error}`
            );
            throw error;
        }
    }

    /**
     * Builds a reference object for NCPDP rejection codes.
     *
     * @async
     * @throws {Error} If an error occurs during the building process.
     */
    async __buildFieldReferenceObject(ctx) {
        try {
            const reference = {};
            const formRecs = await this.form.get.get_form(
                ctx,
                ctx.user,
                "list_ncpdp_rej_code_map",
                { limit: 999 }
            );
            if (formRecs.length === 0) {
                super.error("No records found for NCPDP Rejection Code Map");
                return;
            }
            formRecs.map((rec) => {
                reference[rec.code] = { name: rec.name, field: rec.field };
            });
            this.fieldReference = reference;
        } catch (error) {
            super.error(`Error building reference object: ${error}`);
            throw error;
        }
    }

    /**
     * Builds a reference object mapping NCPDP ECL codes to their corresponding names.
     *
     * @async
     * @returns {Promise<Object>} An object mapping ECL codes to their names.
     * @throws {Error} If an error occurs during the building process.
     */
    async __buildECLReferenceObject(ctx) {
        try {
            const reference = {};

            const filters = [`field:511-FB`];
            const eclRecs = await this.form.get.get_form(
                ctx,
                ctx.user,
                "list_ncpdp_ecl",
                { filter: filters }
            );
            if (eclRecs.length === 0) {
                super.error("No records found for NCPDP ECL (511-FB)");
                return;
            }
            eclRecs.map((rec) => {
                reference[rec.code] = rec.name;
            });
            this.eclReference = reference;
        } catch (error) {
            super.error(`Error building ECL reference object: ${error}`);
            throw error;
        }
    }

    /**
     * Processes the form data and builds the mapping object.
     *
     * @async
     * @param {Object} mapping - The mapping object to be populated.
     * @param {string} formName - The name of the form being processed.
     * @param {Object} formData - The data of the form being processed.
     * @param {string} [prefix=""] - The prefix for nested fields.
     */
    /**
     * Processes the form data and builds the mapping object.
     *
     * @async
     * @param {string} formName - The name of the form being processed.
     * @param {Object} formData - The data of the form being processed.
     * @param {string} [errorDSLPath=""] - The prefix for nested fields.
     * @param {string} [dslPath=""] - The prefix for nested fields.
     * @param {boolean} [underMultiSubform=false] - Whether the form is under a multi-subform.
     */
    async __processForm(
        formName,
        formData,
        errorDSLPath = "",
        dslPath = "",
        underMultiSubform = false
    ) {
        try {
            await Promise.all(
                Object.entries(formData.fields).map(
                    async ([fieldName, fieldData]) => {
                        if (fieldData.model.type === "subform") {
                            const subformName = fieldData.model.source;
                            const subformData = this.shared.DSL[subformName];

                            if (fieldData.model.multi) {
                                const nextErrorPath =
                                    errorDSLPath && underMultiSubform
                                        ? `${errorDSLPath}.${subformName}.__index_{idx}.${subformName}`
                                        : `${subformName}.__index_{idx}.${subformName}`;
                                const nextDSLPath = dslPath
                                    ? `${dslPath}.${fieldName}[*]`
                                    : `${fieldName}[*]`;

                                await this.__processForm(
                                    subformName,
                                    subformData,
                                    nextErrorPath,
                                    nextDSLPath,
                                    true
                                );
                            } else {
                                const nextPath =
                                    errorDSLPath && underMultiSubform
                                        ? `${errorDSLPath}.${subformName}`
                                        : subformName;
                                const nextDSLPath = dslPath
                                    ? `${dslPath}.${fieldName}`
                                    : `${fieldName}`;

                                await this.__processForm(
                                    subformName,
                                    subformData,
                                    nextPath,
                                    nextDSLPath,
                                    underMultiSubform
                                );
                            }
                        } else if (fieldData.view && fieldData.view.reference) {
                            const reference = fieldData.view.reference;
                            const nextErrorPath = errorDSLPath
                                ? `${errorDSLPath}.${fieldName}`
                                : fieldName;
                            const nextDslPath = dslPath
                                ? `${dslPath}.${fieldName}`
                                : fieldName;
                            this.mapping[reference] = {
                                errorDslPath: nextErrorPath,
                                dslPath: nextDslPath,
                            };
                            this.schemaMapping[nextDslPath] = nextErrorPath;
                        }
                    }
                )
            );
        } catch (error) {
            super.error(
                `Error processing form: ${error}, form name: ${formName}, form data: ${JSON.stringify(
                    formData
                )}`
            );
            throw error;
        }
    }

    /**
     * Finds the mapped field for a given error.
     *
     * @async
     * @param {string} code - The error code.
     * @returns {Promise<Object|null>} The mapped field object or null if not found.
     * @throws {Error} If an error occurs while finding the mapped field.
     */
    async __findMappedField(code) {
        try {
            const codeMapping = this.fieldReference[code] || null;
            if (!codeMapping) {
                return false;
            }
            const { field } = codeMapping;
            if (!field) {
                super.error(
                    `Unknown field for NCPDP Rejection Code ${code}. Coding map record present, but missing field.`
                );
                return false;
            }
            const { errorDslPath, dslPath } = this.mapping[field];
            if (!errorDslPath) {
                super.error(
                    `Unknown errorDslPath for NCPDP Rejection Code ${code}. Coding map record present, but missing errorDslPath.`
                );
                return false;
            }
            if (!dslPath) {
                super.error(
                    `Unknown dslPath for NCPDP Rejection Code ${code}. Coding map record present, but missing dslPath.`
                );
                return false;
            }

            return { errorDslPath, dslPath };
        } catch (error) {
            super.error(
                `Error finding mapped field: ${error}, error: ${JSON.stringify(
                    error
                )}`
            );
            throw error;
        }
    }

    /**
     * Handles errors with missing indexes in multi-subform fields.
     *
     * @async
     * @param {Object} ncpdpRec - The NCPDP record object.
     * @param {string} errorDslPath - The DSL path for the error.
     * @param {string} dslPath - The DSL path for the field.
     * @param {string} location - The location of the error.
     * @param {Object} error - The error object.
     * @returns {Promise<string>} The updated error DSL path with resolved indexes.
     */
    async __handleErrorWithMissingIndexes(
        ncpdpRec,
        errorDslPath,
        dslPath,
        location,
        error
    ) {
        super.debug(
            `Path ${errorDslPath} has more than one element, scrubbing for value.`
        );
        try {
            if (location && parseInt(location) > 0) {
                const [newDSLErrorPath, newDSLPath] = await Promise.all([
                    this.__replaceLastIndexInPath(
                        errorDslPath,
                        "{idx}",
                        location
                    ),
                    this.__replaceLastIndexInPath(
                        dslPath,
                        "[*]",
                        `[${location}]`
                    ),
                ]);
                errorDslPath = newDSLErrorPath;
                dslPath = newDSLPath;
            }

            const missingIndexCount = (errorDslPath.match(/{idx}/g) || [])
                .length;
            const asteriskCount = (dslPath.match(/\[\*\]/g) || []).length;

            if (missingIndexCount !== asteriskCount) {
                throw new Error(
                    `Mismatch between {idx} count in errorDslPath and [*] count in dslPath`
                );
            }

            let currentPath = "";
            let updatedErrorDslPath = errorDslPath;
            let updatedDslPath = dslPath;

            for (let idx = 0; idx < missingIndexCount; idx++) {
                const nextAsteriskIndex = updatedDslPath.indexOf("[*]");
                currentPath += updatedDslPath.slice(0, nextAsteriskIndex);

                const arrayAtPath = _.get(ncpdpRec, currentPath);
                if (!arrayAtPath) {
                    throw new Error(
                        `Path ${currentPath} does not exist in ncpdpRec`
                    );
                }

                if (!_.isArray(arrayAtPath)) {
                    throw new Error(
                        `Path ${currentPath} in ncpdpRec is not an array`
                    );
                }

                if (arrayAtPath.length !== 1) {
                    super.debug(
                        `Path ${currentPath} has more than one element, scrubbing for value.`
                    );
                    const result = await this.__checkAllIndexes(
                        ncpdpRec,
                        arrayAtPath,
                        currentPath,
                        updatedErrorDslPath,
                        updatedDslPath,
                        error
                    );
                    return result;
                }

                updatedErrorDslPath = _.replace(
                    updatedErrorDslPath,
                    "{idx}",
                    "0"
                );
                updatedDslPath = _.replace(updatedDslPath, "[*]", "[0]");
                currentPath += "[0]";
            }

            const valueAtIndex = _.get(ncpdpRec, updatedDslPath);
            const isMissingError = error && error.includes("M/I");
            if ((isMissingError && !valueAtIndex) || valueAtIndex?.length > 0) {
                return updatedErrorDslPath;
            } else {
                return null;
            }
        } catch (error) {
            super.error(
                `Error handling error with missing index: ${error}, error: ${JSON.stringify(
                    error
                )}`
            );
            throw error;
        }
    }

    /**
     * Checks all indexes in an array for a matching error condition.
     * @async
     * @param {Object} ncpdpRec - The ncpdp claim record object.
     * @param {Array} arrayAtPath - The array to check indexes in.
     * @param {string} currentPath - The current path in the object structure.
     * @param {string} errorDslPath - The DSL path for the error.
     * @param {string} dslPath - The DSL path for the field.
     * @param {Object} error - The error object to match against.
     * @returns {Promise<string|null>} The updated error DSL path if a match is found, or null if no match.
     */
    async __checkAllIndexes(
        ncpdpRec,
        arrayAtPath,
        currentPath,
        errorDslPath,
        dslPath,
        error
    ) {
        super.debug(
            `Checking all indexes for dsl path: ${dslPath} for error: ${JSON.stringify(
                error
            )}`
        );

        try {
            const checkPaths = [];
            for (let idx = 0; idx < arrayAtPath.length; idx++) {
                const pathToCheck = `${currentPath}[${idx}]`;
                const valueAtIndex = _.get(ncpdpRec, pathToCheck);
                if (valueAtIndex) {
                    const newDslPath = _.replace(dslPath, `[*]`, `[${idx}]`);
                    const newErrorDslPath = _.replace(
                        errorDslPath,
                        `{idx}`,
                        `${idx}`
                    );
                    checkPaths.push(
                        this.__handleErrorWithMissingIndexes(
                            ncpdpRec,
                            newErrorDslPath,
                            newDslPath,
                            null,
                            error
                        )
                    );
                }
            }
            const results = await Promise.all(checkPaths);
            const result = results.find((result) => result !== null);
            return result || null;
        } catch (error) {
            super.error(
                `Error checking all indexes on dsl path: ${dslPath} for error: ${JSON.stringify(
                    error
                )}, error: ${JSON.stringify(error)}`
            );
            throw error;
        }
    }

    /**
     * Sets an error message in the result object at the location specified by the mapped field.
     *
     * @async
     * @param {string} mappedField - The mapped dsl path for the error.
     * @param {Object} ncpdpRec - The pharmacy claim record object.
     * @param {string} location - The location of the error.
     * @param {Object} error - The error object containing the field, location, and code.
     */
    async __setErrorInResult(mappedField, ncpdpRec, location, error) {
        try {
            let errorResultPath = `${mappedField}`;
            if (errorResultPath.includes("{idx}")) {
                super.debug(
                    `Medical Claim Error is in multi-field, attempting to find index(es).`
                );

                const { errorDslPath, dslPath } = mappedField;
                if (!dslPath) {
                    throw new Error(
                        `No dslPath object found for result ${JSON.stringify(
                            errorDslPath
                        )} with error: ${JSON.stringify(error)}`
                    );
                }
                errorResultPath = await this.__handleErrorWithMissingIndexes(
                    ncpdpRec,
                    errorDslPath,
                    dslPath,
                    location,
                    error
                );
                if (!errorResultPath) {
                    return false;
                }
            }

            return {
                path: errorResultPath,
                error: error,
            };
        } catch (error) {
            super.error(
                `Error setting error in result: ${error}, mapped field: ${mappedField}, error message: ${error.description}`
            );
            throw error;
        }
    }

    /**
     * Replaces the last occurrence of a search string in a path with a replacement string.
     *
     * @async
     * @param {string} path - The original path string.
     * @param {string} search - The string to search for in the path.
     * @param {string} replacement - The string to replace the last occurrence of the search string.
     * @returns {Promise<string>} The modified path with the last occurrence replaced.
     */
    async __replaceLastIndexInPath(path, search, replacement) {
        try {
            const index = path.lastIndexOf(search);
            if (index === -1) {
                return path;
            }

            return (
                path.slice(0, index) +
                replacement +
                path.slice(index + search.length)
            );
        } catch (error) {
            super.error(
                `Error replacing last index of ${path} with ${replacement} Error: ${error.description}`
            );
            throw error;
        }
    }
};
