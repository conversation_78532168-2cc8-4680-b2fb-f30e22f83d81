const IFaxProvider = require("./providers/ifax");
const uuid = require("uuid");
const moment = require("moment");
const {
    S3,
    HeadObjectCommand,
    PutObjectCommand,
    GetObjectCommand,
} = require("@aws-sdk/client-s3");
module.exports = class FaxService {
    constructor(nes) {
        this.db = nes.modules.db;
        this.auth = nes.modules.auth;
        this.fx = nes.modules.fx;
        this.shared = nes.shared;

        this.s3 = new S3({
            credentials: {
                accessKeyId: this.shared.config.env["AWS_ACCESS_KEY"],
                secretAccessKey: this.shared.config.env["AWS_SECRET_KEY"],
            },
            region: this.shared.config.env["AWS_REGION"],
        });

        this.provider = null;
        this.fax_numbers = [];
        this.fax_api_key = null;
        this.fax_environment = "Development";
    }

    async init() {
        const configuration = this.shared?.config?.company;
        if (!configuration?.fax_provider) return;
        if (configuration.fax_provider === "N/A") return;

        if (configuration.fax_provider === "ifax") {
            console.log(
                "Loading IFax Integration... for fax_environment",
                configuration.fax_environment
            );
            this.provider = new IFaxProvider(configuration, this.s3, this.db);
            await this.provider.init();
        } else {
            throw new Error("Unsupported fax provider");
        }
    }

    async send(params) {
        const normalizePhoneNumber = (number) => {
            if (!number) return number;
            const cleaned = number.replace(/\D/g, "");
            const withCountryCode = cleaned.startsWith("1")
                ? cleaned
                : `1${cleaned}`;
            return `+${withCountryCode}`;
        };

        // --- Input Validation ---
        if (!params.callerId) {
            return {
                status: 0,
                message: "callerId is required for sending a fax.",
            };
        }
        params.callerId = normalizePhoneNumber(params.callerId);
        if (!params.user_id) {
            params.user_id = 1;
        }

        const hasRecipients =
            params.recipients &&
            Array.isArray(params.recipients) &&
            params.recipients.length > 0;
        const hasSingleFaxNumber =
            params.faxNumber && typeof params.faxNumber === "string";

        if (!hasRecipients && !hasSingleFaxNumber) {
            return {
                status: 0,
                message:
                    "Either recipients array or a single faxNumber is required.",
            };
        }

        // --- Document Processing ---
        // This section modifies params.files if documentIds are provided
        if (
            params.documentIds &&
            Array.isArray(params.documentIds) &&
            params.documentIds.length > 0
        ) {
            console.log(
                `Processing document IDs: ${params.documentIds.join(", ")}`
            );
            try {
                const idsString = params.documentIds
                    .map((id) => parseInt(id, 10))
                    .filter((id) => !isNaN(id))
                    .join(",");
                if (!idsString) {
                    throw new Error("Invalid document IDs provided.");
                }
                const query = `SELECT id, file_path FROM form_document WHERE id IN (${idsString})`;
                const documents = await this.db.env.ro.query(query);

                if (!documents || documents.length === 0) {
                    throw new Error(`No documents found for IDs: ${idsString}`);
                }

                const filesFromDocs = [];
                const foundIds = new Set();
                for (const doc of documents) {
                    foundIds.add(doc.id);
                    if (!doc.file_path) {
                        console.warn(
                            `Document ${doc.id} is missing file_path, skipping.`
                        );
                        continue;
                    }
                    let filePathData;
                    if (typeof doc.file_path === "string") {
                        try {
                            filePathData = JSON.parse(doc.file_path);
                        } catch (e) {
                            console.error(
                                `Failed to parse file_path for document ${doc.id}:`,
                                e,
                                `Value: ${doc.file_path}`
                            );
                            continue;
                        }
                    } else if (
                        typeof doc.file_path === "object" &&
                        doc.file_path !== null
                    ) {
                        filePathData = doc.file_path;
                    } else {
                        console.warn(
                            `Document ${doc.id} has invalid file_path format, skipping.`
                        );
                        continue;
                    }
                    if (!filePathData.filehash) {
                        console.warn(
                            `Document ${doc.id} file_path is missing filehash, skipping.`
                        );
                        continue;
                    }
                    const hash = filePathData.filehash;
                    const slug = hash.substring(0, 2);
                    const s3Key = `secure/${slug}/${hash}`;
                    const fileName =
                        filePathData.filename || `document_${doc.id}.pdf`;
                    filesFromDocs.push({ s3Key: s3Key, fileName: fileName });
                }
                const missingIds = params.documentIds.filter(
                    (id) => !foundIds.has(parseInt(id, 10))
                );
                if (missingIds.length > 0) {
                    console.warn(
                        `Could not find document records for IDs: ${missingIds.join(", ")}`
                    );
                }
                if (filesFromDocs.length === 0) {
                    throw new Error(
                        "No valid files could be processed from the provided document IDs."
                    );
                }
                params.files = filesFromDocs; // Overwrite params.files
                console.log(
                    `Prepared ${params.files.length} files from document IDs.`
                );
                delete params.documentIds;
                delete params.fileBuffer;
                delete params.s3Key;
            } catch (error) {
                console.error("Error processing document IDs:", error);
                return {
                    status: 0,
                    message: `Failed to process document IDs: ${error.message}`,
                };
            }
        }

        // --- File Preparation (Base64 for Provider) ---
        // This section populates fileData (base64 strings) and fileBuffers (binary)
        const fileData = [];
        const fileBuffers = [];

        if (params.files && Array.isArray(params.files)) {
            console.log(
                `Processing ${params.files.length} files from files array.`
            );
            if (params.files.length > 20) {
                return {
                    status: 0,
                    message: "Maximum 20 files can be submitted per fax",
                }; // Return error
            }
            for (const file of params.files) {
                let fileBuffer;
                let fileName = file.fileName || "document.pdf";
                if (file.buffer) {
                    if (!(file.buffer instanceof Buffer))
                        throw new Error("file.buffer must be a Buffer");
                    fileBuffer = file.buffer;
                } else if (file.s3Key) {
                    try {
                        fileBuffer = await this.getFileFromS3(file.s3Key);
                        // Try getting filename from metadata
                        const headParams = {
                            Bucket: this.shared.config.env["AWS_S3_BUCKET"],
                            Key: file.s3Key,
                        };
                        const command = new HeadObjectCommand(headParams);
                        const response = await this.s3.send(command);
                        if (response.Metadata && response.Metadata.filename)
                            fileName = response.Metadata.filename;
                    } catch (error) {
                        console.warn(
                            `Error fetching S3 file ${file.s3Key} or its metadata:`,
                            error
                        );
                        // Continue to next file if fetch fails
                        continue;
                    }
                } else {
                    return {
                        status: 0,
                        message: "Each file must have either buffer or s3Key",
                    }; // Return error
                }
                if (!fileBuffer) {
                    console.warn(
                        `Could not get file buffer for ${file.s3Key || "provided buffer"}, skipping file.`
                    );
                    continue;
                }
                fileBuffers.push(fileBuffer);
                fileData.push({
                    fileName: fileName,
                    fileData: fileBuffer.toString("base64"),
                });
            }
        } else if (!params.files && (params.fileBuffer || params.s3Key)) {
            // Handle single file case
            console.log("Processing single fileBuffer or s3Key");
            let fileBuffer;
            let fileName = params.fileName || "document.pdf";
            if (params.fileBuffer) {
                if (!(params.fileBuffer instanceof Buffer))
                    throw new Error("fileBuffer must be a Buffer");
                fileBuffer = params.fileBuffer;
            } else if (params.s3Key) {
                try {
                    fileBuffer = await this.getFileFromS3(params.s3Key);
                    // Try getting filename from metadata
                    const headParams = {
                        Bucket: this.shared.config.env["AWS_S3_BUCKET"],
                        Key: params.s3Key,
                    };
                    const command = new HeadObjectCommand(headParams);
                    const response = await this.s3.send(command);
                    if (response.Metadata && response.Metadata.filename)
                        fileName = response.Metadata.filename;
                } catch (error) {
                    console.warn(
                        `Error fetching S3 file ${params.s3Key} or its metadata:`,
                        error
                    );
                    // Fail if the single file cannot be fetched
                    return {
                        status: 0,
                        message: `Failed to fetch file from S3: ${params.s3Key}`,
                    };
                }
            }
            if (fileBuffer) {
                fileBuffers.push(fileBuffer);
                fileData.push({
                    fileName: fileName,
                    fileData: fileBuffer.toString("base64"),
                });
            } else {
                // This case should ideally not be reached if S3 fetch failed above
                return {
                    status: 0,
                    message:
                        "Could not process the provided file buffer or S3 key.",
                };
            }
        }
        // --- End File Preparation ---

        if (fileData.length === 0) {
            console.error("No files to send after processing inputs.");
            return {
                status: 0,
                message: "No files found or processed to send.",
            };
        }
        if (this.checkPageLimit(fileBuffers)) {
            return {
                status: 0,
                message: "Total pages exceed the 200 page limit",
            };
        }

        // Prepare base parameters for the provider, including the crucial fileData
        const baseProviderParams = {
            ...params, // Include original params like callerId, subject, message etc.
            fileData: fileData, // Pass the base64 encoded file data
            recipients: undefined, // Clear recipients array for provider call
            faxNumber: undefined, // Clear single faxNumber for provider call
            documentIds: undefined, // Clear helper fields
            fileBuffer: undefined,
            s3Key: undefined,
            files: undefined,
        };

        // --- Send Logic: Handle Single vs Multiple Recipients ---
        const overallResult = {
            status: 1,
            message: "Fax(es) submitted successfully.",
            data: { jobIds: [] },
        };
        let hasFailures = false;
        let failureMessage = "";
        const submittedJobIds = [];
        let recipientsToProcess = [];

        if (hasRecipients) {
            // Validate recipients array (limit and content)
            if (params.recipients.length > 15) {
                return {
                    status: 0,
                    message: "Maximum 15 recipients can be submitted per fax",
                };
            }
            recipientsToProcess = params.recipients.map((r, index) => {
                const normalized = normalizePhoneNumber(r.faxNumber);
                if (!r || !normalized) {
                    // Throw error immediately if a recipient is invalid
                    throw new Error(
                        `Recipient at index ${index} is invalid or missing faxNumber.`
                    );
                }
                return {
                    ...r,
                    faxNumber: normalized,
                    name: r.name || params.to_name || "",
                };
            });
        } else if (hasSingleFaxNumber) {
            const normalized = normalizePhoneNumber(params.faxNumber);
            if (!normalized) {
                return {
                    status: 0,
                    message: `Invalid faxNumber provided: ${params.faxNumber}`,
                };
            }
            recipientsToProcess.push({
                faxNumber: normalized,
                name: params.to_name || "",
            });
        }
        // At this point, recipientsToProcess is guaranteed to have at least one valid recipient

        console.log(
            `Attempting to send fax to ${recipientsToProcess.length} recipient(s).`
        );

        for (const recipient of recipientsToProcess) {
            // Create specific params for this provider call
            const providerParams = {
                ...baseProviderParams,
                faxNumber: recipient.faxNumber, // Provider expects singular faxNumber
                to_name: recipient.name, // Use specific recipient name
            };

            // Handle scheduled time if applicable
            if (params.send_at) {
                const scheduledDate = moment(params.send_at);
                if (!scheduledDate.isValid()) {
                    // Fail fast if schedule date is invalid
                    return {
                        status: 0,
                        message: `Invalid send_at date format: ${params.send_at}`,
                    };
                }
                providerParams.send_at = scheduledDate.utc().format();
                console.log(
                    `Scheduling fax for ${recipient.faxNumber} at ${providerParams.send_at} UTC`
                );
            }

            console.log(
                `Calling provider for recipient: ${recipient.faxNumber}`
            );

            try {
                const result = await this.provider.send(providerParams);

                if (result.status === 1 && result.data && result.data.jobId) {
                    console.log(
                        `Fax sent successfully to ${recipient.faxNumber}, jobId: ${result.data.jobId}`
                    );
                    submittedJobIds.push(result.data.jobId);
                    // Record success for this recipient
                    await this.recordOutboundFax({
                        job_id: result.data.jobId,
                        external_id: result.data.jobId,
                        fax_id: params.fax_id,
                        to_number: recipient.faxNumber,
                        from_number: params.callerId,
                        status: providerParams.send_at
                            ? "scheduled"
                            : "sending",
                        direction: "outbound",
                        patient_mrn: params.patientMrn,
                        subject: params.subject,
                        from_name: params.from_name,
                        to_name: recipient.name,
                        message: params.message,
                        scheduled_at: providerParams.send_at,
                        user_id: params.user_id,
                    });
                } else {
                    console.error(
                        `Failed to send fax to ${recipient.faxNumber}:`,
                        result.message || "Unknown provider error"
                    );
                    hasFailures = true;
                    failureMessage = result.message || "Unknown provider error";
                }
            } catch (error) {
                console.error(
                    `Error sending fax to ${recipient.faxNumber}:`,
                    error
                );
                hasFailures = true;
                failureMessage = error.message;
            }
        }

        // Adjust overall result based on individual sends
        if (hasFailures) {
            overallResult.status = submittedJobIds.length > 0 ? 2 : 0; // 2 = partial success, 0 = total failure
            overallResult.message =
                submittedJobIds.length > 0
                    ? `Fax submitted successfully for ${submittedJobIds.length}/${recipientsToProcess.length} recipients. Some failed.`
                    : `Failed to send fax to all recipients. ${failureMessage}`;
        }
        overallResult.data.jobIds = submittedJobIds;

        return overallResult;
    }

    async getFileFromS3(s3Key) {
        try {
            const getParams = {
                Bucket: this.shared.config.env["AWS_S3_BUCKET"],
                Key: s3Key,
            };
            const command = new GetObjectCommand(getParams);
            const response = await this.s3.send(command);

            const chunks = [];
            for await (const chunk of response.Body) {
                chunks.push(chunk);
            }
            const buffer = Buffer.concat(chunks);

            if (buffer.length > 0) {
                const header = buffer.slice(0, 5).toString();
                if (header !== "%PDF-") {
                    console.error(
                        "Invalid PDF header detected in S3 retrieved file:",
                        header
                    );
                    throw new Error(
                        "Invalid PDF format: Missing PDF header signature"
                    );
                }

                console.log("S3 retrieved PDF details:", {
                    size: buffer.length,
                    header: header,
                    firstBytes: buffer.slice(0, 20).toString("hex"),
                });
            }

            return buffer;
        } catch (error) {
            console.error(`Failed to fetch file from S3: ${s3Key}`, error);
            throw new Error(`Failed to fetch file from S3: ${error.message}`);
        }
    }

    async getFileContent(buffer, s3Key) {
        let fileBuffer;

        if (buffer) {
            if (!(buffer instanceof Buffer)) {
                throw new Error("buffer must be a Buffer instance");
            }

            fileBuffer = buffer;
            if (fileBuffer.length > 0) {
                const header = fileBuffer.slice(0, 5).toString();
                if (header !== "%PDF-") {
                    console.error("Invalid PDF header detected:", header);
                    throw new Error(
                        "Invalid PDF format: Missing PDF header signature"
                    );
                }

                console.log("PDF file details:", {
                    size: fileBuffer.length,
                    header: header,
                    firstBytes: fileBuffer.slice(0, 20).toString("hex"),
                });
            }
        } else if (s3Key) {
            fileBuffer = await this.getFileFromS3(s3Key);
        } else {
            throw new Error("Either buffer or S3 key must be provided");
        }

        return fileBuffer.toString("base64");
    }

    async getSiteIdFromNumber(faxNumber) {
        if (!faxNumber) return null;

        const cleanNumber = faxNumber.replace(/\D/g, "").replace(/^1/, "");

        if (this.provider && this.provider.numberMap) {
            const numberData = this.provider.numberMap[cleanNumber];
            if (numberData && numberData.site_id) {
                return numberData.site_id;
            }
        }

        return null;
    }

    async recordOutboundFax(faxData) {
        if (!faxData.created_at) {
            faxData.created_at = moment().format();
        }

        faxData.auto_name = `${moment().format("YYYY-MM-DD HH:mm:ss")} - outbound ${faxData.status} ${faxData.to_number} ${faxData.from_number}`;

        if (!faxData.site_id) {
            faxData.site_id = await this.getSiteIdFromNumber(
                faxData.from_number
            );
        }

        const adminUser = this.auth.admin_user_for_session();
        const transaction = this.db.env.rw.transaction({ user: adminUser });

        await transaction.insert("faxes", faxData);
        const result = await transaction.commit();

        if (result && result.error) {
            console.error(
                `Error creating fax record: ${result.message || result.error}`
            );
            throw new Error(
                `Failed to create fax record: ${result.message || result.error}`
            );
        }

        console.log(
            `Created fax record for job ${faxData.job_id} with site_id ${faxData.site_id || "none"}`
        );
        return result;
    }

    async getStatus(jobId) {
        return await this.provider.getStatus(jobId);
    }

    async retrieveFaxes(params) {
        if (Object.keys(params).length === 0) {
            params.all = true;
        }

        const faxes = await this.provider.retrieveFaxes(params);
        const faxArray = faxes?.data || faxes || [];

        if (!faxArray.length) {
            return { status: 1, message: "Success", count: 0, data: {} };
        }

        const jobIds = faxArray.map((fax) => fax.external_id);
        const statusResults = await this.provider.getStatus(jobIds);

        if (statusResults?.status === 1 && statusResults?.data) {
            const statusMap = {};
            statusResults.data.forEach((result) => {
                if (result.status === 1 && result.data) {
                    statusMap[result.data.jobId] = result.data;
                }
            });

            faxArray.forEach((fax) => {
                const status = statusMap[fax.external_id];
                if (status) {
                    fax.status = status.status;
                    fax.status_message = status.message;
                    fax.error_code = status.code;
                }
            });
        }

        const storedFaxes = await this.checkStored(faxArray);

        if (storedFaxes.length > 0) {
            await this.createRecords(storedFaxes);
        }

        const fax_external_ids = faxArray
            .map((fax) => `'${fax.external_id}'`)
            .join(",");
        const dbRecordsQuery = `
            SELECT 
                f.*,
                d.file_path,
                d.source as doc_source,
                d.name as doc_name,
                d.received as doc_received,
                d.to_phone as doc_to_phone,
                d.from_phone as doc_from_phone,
                d.from_name as doc_from_name,
                d.from_loc as doc_from_loc,
                d.sent_dt as doc_sent_dt,
                d.file_pages as doc_file_pages,
                d.orig_filename as doc_orig_filename,
                d.id as document_id
            FROM form_faxes f
            LEFT JOIN form_document d ON f.external_id = d.external_id
            WHERE f.external_id IN (${fax_external_ids})
        `;

        const dbRecords = await this.db.env.ro.query(dbRecordsQuery);

        const dbRecordsMap = {};
        dbRecords.forEach((record) => {
            const faxRecord = { ...record };
            const docRecord = {
                source: record.doc_source,
                name: record.doc_name,
                received: record.doc_received,
                to_phone: record.doc_to_phone,
                from_phone: record.doc_from_phone,
                from_name: record.doc_from_name,
                from_loc: record.doc_from_loc,
                sent_dt: record.doc_sent_dt,
                file_pages: record.doc_file_pages,
                orig_filename: record.doc_orig_filename,
                file_path: record.file_path,
                external_id: record.external_id,
                fax_id: record.fax_id,
            };

            Object.keys(docRecord).forEach((key) => {
                if (key !== "external_id" && key !== "fax_id") {
                    delete faxRecord[key];
                }
            });

            dbRecordsMap[record.external_id] = {
                fax: faxRecord,
                document: docRecord,
            };
        });

        const mergedData = faxArray.map((fax) => {
            const stored = storedFaxes.find(
                (sf) => sf.external_id === fax.external_id
            );

            const dbRecord = dbRecordsMap[fax.external_id];

            let filePath = stored?.file_path || dbRecord?.document?.file_path;
            if (typeof filePath === "string") {
                try {
                    filePath = JSON.parse(filePath);
                } catch (e) {
                    console.warn(
                        `Failed to parse file_path for fax ${fax.external_id}:`,
                        e
                    );
                }
            }

            return {
                ...fax,
                ...(dbRecord?.fax || {}),
                ...(stored || {}),
                file_path: filePath,
            };
        });

        const groupedData = {
            faxes: mergedData,
        };

        return {
            status: 1,
            message: "Success",
            count: mergedData.length,
            data: groupedData,
        };
    }

    async download(jobId) {
        return await this.provider.download(jobId);
    }

    async resend(jobId) {
        try {
            const existingFaxQuery = `SELECT * FROM form_faxes WHERE external_id = '${jobId}'`;
            const existingFaxes = await this.db.env.ro.query(existingFaxQuery);

            if (!existingFaxes || existingFaxes.length === 0) {
                throw new Error(`Fax record with job ID ${jobId} not found`);
            }

            const existingFax = existingFaxes[0];
            console.log(`Attempting to resend fax with job ID ${jobId}`);

            const result = await this.provider.resend(jobId);
            if (
                result.data &&
                result.data.jobId &&
                result.data.jobId !== jobId
            ) {
                const updateData = {
                    status: "sending",
                    error_code: null,
                    error_message: null,
                    retries: (existingFax.retries || 0) + 1,
                    updated_on: moment().format(),
                };

                await this.updateFaxRecord(updateData, existingFax.id);

                console.log(
                    `Fax job ID updated from ${jobId} to ${result.data.jobId}`
                );
                result.data.previousJobId = jobId;
            }

            return result;
        } catch (error) {
            console.error(`Error in resend operation for fax ${jobId}:`, error);
            throw error;
        }
    }

    async downloadAndStore(faxes) {
        console.log("S3 Client details:", {
            region: this.shared.config.env["AWS_REGION"],
            accessKeyPrefix:
                this.shared.config.env["AWS_ACCESS_KEY"]?.substring(0, 4) +
                "...",
            bucket: this.shared.config.env["AWS_S3_BUCKET"],
        });

        console.log("Downloading and storing faxes:", faxes.length);
        console.log("S3 Configuration:", {
            bucket: this.shared.config.env["AWS_S3_BUCKET"],
            region: this.shared.config.env["AWS_REGION"],
        });

        const storedFaxes = [];
        const BATCH_SIZE = 20;

        for (let i = 0; i < faxes.length; i += BATCH_SIZE) {
            const batch = faxes.slice(i, i + BATCH_SIZE);
            console.log(
                `Processing batch ${i / BATCH_SIZE + 1} of ${Math.ceil(faxes.length / BATCH_SIZE)}`
            );

            const batchResults = await Promise.all(
                batch.map(async (fax) => {
                    try {
                        const hash = Buffer.from(uuid.v4()).toString("hex");
                        const slug = hash.substring(0, 2);
                        const s3Key = `secure/${slug}/${hash}`;

                        console.log(
                            `Generated S3 key for fax ${fax.external_id}: ${s3Key}`
                        );

                        let fileExists = false;

                        try {
                            const headParams = {
                                Bucket: this.shared.config.env["AWS_S3_BUCKET"],
                                Key: s3Key,
                            };

                            const headCommand = new HeadObjectCommand(
                                headParams
                            );
                            const headResult = await this.s3.send(headCommand);

                            fileExists = true;

                            const metadata = headResult.Metadata || {};
                            return {
                                ...fax,
                                file_path: {
                                    filehash: hash,
                                    filename:
                                        metadata.filename ||
                                        `fax_${fax.external_id}.pdf`,
                                    filesize: headResult.ContentLength || 0,
                                    stored_dt:
                                        metadata.storedDt || moment().format(),
                                    mime_type:
                                        metadata.mimeType ||
                                        headResult.ContentType ||
                                        "application/pdf",
                                },
                            };
                        } catch (headError) {
                            if (
                                headError.name === "NotFound" ||
                                headError.$metadata?.httpStatusCode === 404
                            ) {
                                fileExists = false;
                            } else {
                                console.error(
                                    `S3 head error for fax ${fax.external_id}:`,
                                    {
                                        name: headError.name,
                                        code: headError.code,
                                        message: headError.message,
                                        statusCode:
                                            headError.$metadata?.httpStatusCode,
                                        requestId:
                                            headError.$metadata?.requestId,
                                    }
                                );
                                fileExists = false;
                            }
                        }

                        if (!fileExists) {
                            const downloadResult =
                                await this.provider.downloadFax(
                                    fax.external_id,
                                    fax.direction || "inbound",
                                    fax.transaction_id
                                );

                            if (!downloadResult) {
                                console.error(
                                    `Failed to download fax ${fax.external_id}`
                                );
                                return null;
                            }

                            const { fileData, fileName, contentType } =
                                downloadResult;
                            const mimeType = contentType || "application/pdf";
                            const storedDt = moment().format();

                            try {
                                const putParams = {
                                    Bucket: this.shared.config.env[
                                        "AWS_S3_BUCKET"
                                    ],
                                    Key: s3Key,
                                    Body: fileData,
                                    ServerSideEncryption: "AES256",
                                    StorageClass: "STANDARD_IA",
                                    ContentType: mimeType,
                                    Metadata: {
                                        filename: String(fileName || ""),
                                        faxId: String(fax.external_id || ""),
                                        transactionId: String(
                                            fax.transaction_id || ""
                                        ),
                                        direction: String(
                                            fax.direction || "inbound"
                                        ),
                                        fromNumber: String(
                                            fax.from_number || fax.sender || ""
                                        ),
                                        toNumber: String(
                                            fax.to_number || fax.receiver || ""
                                        ),
                                        mimeType: String(mimeType || ""),
                                        storedDt: String(storedDt || ""),
                                    },
                                };

                                const putCommand = new PutObjectCommand(
                                    putParams
                                );
                                await this.s3.send(putCommand);

                                return {
                                    ...fax,
                                    file_path: {
                                        filehash: hash,
                                        filename: fileName,
                                        filesize: fileData.length,
                                        stored_dt: storedDt,
                                        mime_type: mimeType,
                                    },
                                };
                            } catch (s3Error) {
                                console.error(
                                    `S3 upload error for fax ${fax.external_id}:`,
                                    {
                                        name: s3Error.name,
                                        code: s3Error.code,
                                        message: s3Error.message,
                                        statusCode:
                                            s3Error.$metadata?.httpStatusCode,
                                        requestId: s3Error.$metadata?.requestId,
                                        bucket: this.shared.config.env[
                                            "AWS_S3_BUCKET"
                                        ],
                                        key: s3Key,
                                    }
                                );
                                throw s3Error;
                            }
                        }
                    } catch (error) {
                        console.error(
                            `Error storing fax ${fax.external_id}:`,
                            error
                        );
                        return null;
                    }
                })
            );

            storedFaxes.push(
                ...batchResults.filter((result) => result !== null)
            );
        }

        return storedFaxes;
    }

    async checkStored(faxes) {
        if (typeof faxes !== "object" || !Array.isArray(faxes)) {
            return [];
        }
        const fax_ids = faxes.map((fax) => `'${fax.external_id}'`);
        const sql = `SELECT external_id FROM form_faxes WHERE external_id IN (${fax_ids.join(",")})`;

        const stored = await this.db.env.ro.query(sql);
        const stored_ids = stored.map((row) => String(row.external_id));
        let stored_in_s3 = [];
        const faxes_to_store = faxes.filter(
            (fax) => !stored_ids.includes(String(fax.external_id))
        );

        if (faxes_to_store.length > 0) {
            stored_in_s3 = await this.downloadAndStore(faxes_to_store);
        }

        return stored_in_s3;
    }

    async createRecords(faxes) {
        console.log("Creating records for faxes:", faxes.length);
        const resultRecords = [];

        if (!faxes.length) return resultRecords;

        try {
            const fax_external_ids = faxes
                .map((fax) => `'${fax.external_id}'`)
                .join(",");

            const existingFaxQuery = `SELECT external_id, fax_id FROM form_faxes WHERE external_id IN (${fax_external_ids})`;
            const existingFaxes = await this.db.env.ro.query(existingFaxQuery);

            const existingFaxMap = {};
            existingFaxes.forEach((row) => {
                existingFaxMap[row.external_id] = row.fax_id;
            });

            const existingDocQuery = `SELECT external_id, fax_id FROM form_document WHERE external_id IN (${fax_external_ids})`;
            const existingDocs = await this.db.env.ro.query(existingDocQuery);

            const existingDocMap = {};
            existingDocs.forEach((row) => {
                existingDocMap[row.external_id] = row.fax_id;
            });

            const recordsToCreate = [];

            for (const fax of faxes) {
                if (!fax.file_path) {
                    console.warn(
                        `Skipping fax ${fax.external_id} without file_path`
                    );
                    continue;
                }

                const isExistingFax = existingFaxMap[fax.external_id];
                const isExistingDoc = existingDocMap[fax.external_id];

                if (!isExistingFax || !isExistingDoc) {
                    const fax_id = isExistingFax || isExistingDoc || uuid.v4();

                    let site_id = null;
                    if (fax.direction === "outbound") {
                        site_id = await this.getSiteIdFromNumber(
                            fax.from_number
                        );
                    } else {
                        site_id = await this.getSiteIdFromNumber(fax.to_number);
                    }

                    recordsToCreate.push({
                        fax: {
                            ...fax,
                            site_id,
                            user_id: fax.user_id,
                        },
                        fax_id,
                        needsFaxRecord: !isExistingFax,
                        needsDocRecord: !isExistingDoc,
                        site_id,
                    });
                }
            }

            if (recordsToCreate.length === 0) {
                console.log("No new records to create");
                return [];
            }

            const adminUser = this.auth.admin_user_for_session();
            const transaction = this.db.env.rw.transaction({ user: adminUser });

            for (const record of recordsToCreate) {
                const { fax, fax_id, needsFaxRecord, needsDocRecord, site_id } =
                    record;

                if (needsFaxRecord) {
                    const faxRecord = {
                        fax_id: fax_id,
                        external_id: fax.external_id,
                        transaction_id: fax.transaction_id,
                        direction: fax.direction || "inbound",
                        status: fax.status || "received",
                        to_number: fax.to_number || fax.receiver,
                        from_number: fax.from_number || fax.sender,
                        total_pages: fax.pages || fax.total_pages,
                        total_duration: fax.duration || fax.total_duration,
                        received_dt: fax.received_dt,
                        status_message: fax.status_message,
                        error_code: fax.error_code,
                        error_message: fax.error_message,
                        site_id,
                        user_id: fax.user_id,
                    };

                    await transaction.insert("faxes", faxRecord);
                    console.log(
                        `Created fax record for ${fax.external_id} with fax_id ${fax_id} and site_id ${site_id || "none"}`
                    );
                }

                if (needsDocRecord) {
                    const documentRecord = {
                        source: "Fax",
                        external_id: fax.external_id,
                        fax_id: fax_id,
                        file_path: fax.file_path,
                        received: fax.received_dt,
                        to_phone: fax.to_number || fax.receiver,
                        from_phone: fax.from_number || fax.sender,
                        from_name: fax.from_name || "",
                        from_loc: fax.from_loc || "",
                        sent_dt: fax.sent_dt,
                        file_pages: fax.pages || fax.total_pages,
                        orig_filename: fax.file_path.filename,
                        site_id,
                        user_id: fax.user_id,
                    };

                    await transaction.insert("document", documentRecord);
                    console.log(
                        `Created document record for fax ${fax.external_id} with fax_id ${fax_id} and site_id ${site_id || "none"}`
                    );
                }

                resultRecords.push({
                    fax: {
                        external_id: fax.external_id,
                        fax_id: fax_id,
                        site_id,
                    },
                    document: {
                        external_id: fax.external_id,
                        fax_id: fax_id,
                        file_path: fax.file_path,
                        site_id,
                    },
                    created: needsFaxRecord || needsDocRecord,
                });
            }

            if (!transaction.can_commit()) {
                console.warn("Transaction has no queries to commit");
                return resultRecords;
            }

            const result = await transaction.commit();

            if (result.error) {
                console.error(
                    `Error encountered while inserting fax records: ${result.message || result.error}`
                );
                throw new Error(
                    `Failed to create fax records: ${result.message || result.error}`
                );
            }

            console.log(`Successfully created ${resultRecords.length} records`);
            return resultRecords;
        } catch (error) {
            console.error("Error creating fax records:", error);
            throw error;
        }
    }

    async getFaxRecord(jobId) {
        return await this.db.selectOne("fax_records", {
            where: { job_id: jobId },
        });
    }

    async getFaxList(params = {}) {
        const records = await this.db.select("fax_records", {
            where: params,
            order: [["created_at", "DESC"]],
        });

        return records.map((rec) => ({
            jobId: rec.job_id,
            fromNumber: rec.from_number,
            toNumber: rec.to_number,
            status: rec.status,
            created_at: rec.created_at,
            s3_key: rec.s3_key,
            patient_mrn: rec.patient_mrn,
        }));
    }

    async updateFaxRecord(updateData, existingId) {
        if (!updateData || Object.keys(updateData).length === 0) {
            console.log(
                `Fax ${existingId} has no changes, not updating status.`
            );
            return true;
        }

        updateData.updated_on = moment().format();

        const adminUser = this.auth.admin_user_for_session();
        const transaction = this.db.env.rw.transaction({ user: adminUser });
        console.log(`update data ${JSON.stringify(updateData)}`);
        await transaction.update("faxes", updateData, existingId);
        const result = await transaction.commit();
        if (result.error) {
            console.error(
                `Error encountered while updating fax record: ${result.message || result.error}`
            );
            throw new Error(
                `Failed to update fax record: ${result.message || result.error}`
            );
        }

        console.log(`Successfully updated fax record with id ${existingId}`);
        return result;
    }

    async webhook_callback(data, direction) {
        console.log(`Fax Webhook Callback received for: ${data.jobId}`);
        const mappedFax = await this.provider.mapFaxData(data, true);
        if (direction === "outbound") {
            if (data.success && data.faxStatus === "delivered") {
                mappedFax.sent_dt = moment().format();
                mappedFax.delivered_dt = moment().format();
            }
        } else {
            if (data.success && data.faxStatus === "received") {
                if (data.faxCallEnd) {
                    mappedFax.received_dt = moment
                        .unix(data.faxCallEnd)
                        .format();
                } else {
                    mappedFax.received_dt = moment().format();
                }
            }
        }
        console.log("mapped fax in webhook", mappedFax);

        const existingFaxSql = `SELECT * FROM form_faxes WHERE external_id = '${mappedFax.external_id}'`;
        const existingFaxes = await this.db.env.ro.query(existingFaxSql);

        const existingDocumentSql = `SELECT * FROM form_document WHERE external_id = '${mappedFax.external_id}'`;
        const existingDocuments =
            await this.db.env.ro.query(existingDocumentSql);

        let hasDocument = existingDocuments && existingDocuments.length > 0;

        const notStored = existingFaxes.length === 0;

        if (notStored || !hasDocument) {
            const faxesToProcess = [mappedFax];
            const stored = await this.downloadAndStore(faxesToProcess);

            if (stored.length > 0) {
                if (notStored) {
                    await this.createRecords(stored);
                } else if (!hasDocument) {
                    console.log(
                        "Creating missing document record for fax",
                        mappedFax.external_id
                    );
                    const storedFax = stored[0];

                    if (storedFax && storedFax.file_path) {
                        const documentRecord = {
                            source: "Fax",
                            external_id: mappedFax.external_id,
                            fax_id: existingFaxes[0].fax_id,
                            file_path: storedFax.file_path,
                            received: mappedFax.received_dt,
                            to_phone: mappedFax.to_number || mappedFax.receiver,
                            from_phone:
                                mappedFax.from_number || mappedFax.sender,
                            from_name: mappedFax.from_name || "",
                            from_loc: mappedFax.from_loc || "",
                            sent_dt: mappedFax.sent_dt
                                ? mappedFax.sent_dt
                                : moment().format(),
                            file_pages: mappedFax.total_pages,
                            orig_filename:
                                storedFax.file_path.filename ||
                                `fax_${mappedFax.external_id}.pdf`,
                            site_id: mappedFax.site_id,
                            user_id: mappedFax.user_id,
                        };

                        const adminUser = this.auth.admin_user_for_session();
                        const transaction = this.db.env.rw.transaction({
                            user: adminUser,
                        });
                        await transaction.insert("document", documentRecord);

                        const result = await transaction.commit();
                        if (result && !result.error) {
                            console.log(
                                `Successfully created document record for existing fax ${mappedFax.external_id}`
                            );
                            hasDocument = true;
                        } else {
                            console.error(
                                `Failed to create document record for fax ${mappedFax.external_id}:`,
                                result.error || result.message
                            );
                        }
                    } else {
                        console.error(
                            `Download succeeded but no file_path for fax ${mappedFax.external_id}`
                        );
                    }
                }
            }
        }

        if (!notStored) {
            console.log(
                `Fax already stored: ${JSON.stringify(mappedFax, null, 2)}`
            );

            const existingFax = existingFaxes[0];
            const updateData = {};
            const fieldsToUpdate = [
                "status",
                "status_message",
                "error_code",
                "error_message",
                "total_pages",
                "total_duration",
                "retries",
                "message",
            ];
            if (mappedFax.sent_dt) {
                updateData.sent_dt = mappedFax.sent_dt;
            }
            if (mappedFax.delivered_dt) {
                updateData.delivered_dt = mappedFax.delivered_dt;
            }
            if (mappedFax.received_dt) {
                updateData.received_dt = mappedFax.received_dt;
            }

            for (const field of fieldsToUpdate) {
                if (
                    mappedFax[field] !== undefined &&
                    mappedFax[field] !== null &&
                    String(existingFax[field]) !== String(mappedFax[field])
                ) {
                    updateData[field] = mappedFax[field];
                }
            }

            if (Object.keys(updateData).length > 0) {
                console.log(
                    `Updating fax record ${existingFax.external_id} with:`,
                    updateData
                );
                await this.updateFaxRecord(updateData, existingFax.id);
            } else {
                console.log(
                    `Fax ${existingFax.external_id} has no changes, not updating status.`
                );
            }
        }

        return { status: 200, message: "Fax processed successfully" };
    }

    async cancelFax(jobId) {
        const result = await this.provider.cancel(jobId);
        if (result.status === 1) {
            await this.updateStatus(jobId, "canceled");
        }
        return result;
    }

    async retryWebhook(jobId, retryCount = 0) {
        const maxRetries = 5;
        const backoffTimes = [5, 10, 20, 30, 60];

        if (retryCount >= maxRetries) {
            console.error(`Max retries reached for fax ${jobId}`);
            return;
        }

        try {
            await this.deliverWebhook(jobId);
        } catch (error) {
            const nextRetry = backoffTimes[retryCount];
            console.error(error);
            console.log(
                `Scheduling retry ${retryCount + 1} in ${nextRetry} minutes for fax ${jobId}`
            );

            setTimeout(
                () => {
                    this.retryWebhook(jobId, retryCount + 1);
                },
                nextRetry * 60 * 1000
            );
        }
    }

    async getInboundFaxes(params = {}) {
        return await this.provider.fetchInbound(params);
    }

    async getOutboundFaxes(params = {}) {
        return await this.provider.fetchOutbound(params);
    }

    async getAllFaxes(params = {}) {
        return await this.provider.fetchAll(params);
    }

    estimateDocumentPages(buffer) {
        if (!buffer) return 1;

        const sizeInKB = buffer.length / 1024;
        const estimatedPages = Math.max(1, Math.ceil(sizeInKB / 100));

        return Math.min(estimatedPages, 200);
    }

    checkPageLimit(buffers) {
        if (!buffers || !buffers.length) return false;

        const totalPages = buffers.reduce((sum, buffer) => {
            return sum + this.estimateDocumentPages(buffer);
        }, 0);

        return totalPages > 200;
    }
};
