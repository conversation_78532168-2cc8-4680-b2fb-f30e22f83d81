/**
 * iFax API error codes and their corresponding messages
 */
const IFAX_ERRORS = {
    // Permission and Authentication Errors
    "-3007": "No permission for this action",
    "-2014": "Username/password not found in request data",
    "-1062": "Wrong session ID",
    "-310": "Active user not found for this request",
    12015: "Wrong password",
    12016: "Wrong email",

    // File and Image Errors
    "-3004": "TIFF file is empty",
    "-3003": "Image does not exist",
    "-1063": "Total file size is too big",
    "-1011": "Image not available",
    "-1008": "File count and total file data/name are not equal",
    "-1004": "Fax file not compatible/found",
    12092: "File not found",

    // Credit and Payment Errors
    "-2037": "No credit",
    "-22": "Not enough credit for the user",
    12012: "Not enough credits",
    12017: "Credit not added",

    // Fax Status Codes
    "-11": "Pre-processing",
    "-3": "Fax processed for sending",
    0: "delivered",
    403: "Fax canceled",
    3931: "Busy",
    3935: "No answer (might be out of paper)",
    9024: "No answer (The Receiving Machine May Be Out Of Paper)",

    // System and Service Errors
    "-2036": "SERVICE_UNAVAILABLE",
    256: "Internal error",
    3072: "Telephony error",
    3211: "Fax machine incompatibility",
    9007: "NETWORK_OUT_OF_ORDER",
    204000: "Rendering error",
    205000: "Quota exceeded (Prepaid card depleted)",
    205001: "Internal System error (FindRoute)",
    206001: "Internal System Error (LocalSender)",

    // Number and Routing Errors
    "-2075": "INVALID_NUMBER_FORMAT",
    3912: "UNALLOCATED_NUMBER",
    6002: "NO_ROUTE_DESTINATION",
    120100: "Invalid faxNumber",
    120101: "You can send faxes only to US & CA destinations",
    120103: "Invalid caller ID",
    120104: "Provided caller ID is not verified",
};

module.exports = {
    IFAX_ERRORS,
    getErrorMessage: (code) => IFAX_ERRORS[code] || "Unknown error",
};
