"use strict";
const fs = require("fs").promises;
const path = require("path");
const class_path = __dirname + "/fx/";

module.exports = class FX {
    constructor(nes) {
        this.nes = nes;
        this.shared = nes.shared;
    }

    async init() {
        console.log(`    ${class_path}...`);
        await this.register_fx_funcs();
    }

    async register(FxClass) {
        let function_names = Object.getOwnPropertyNames(
            FxClass.prototype
        ).filter((name) => typeof FxClass.prototype[name] === "function");
        function_names = function_names.filter(
            (func) => func !== "constructor"
        );
        function_names.forEach((func) => {
            if (func in this) {
                throw new Error(
                    `Fx function ${func} is already registered. Fx function names must be unique!`
                );
            }
            this[func] = new FxClass()[func].bind(this);
        });
    }

    async register_fx_funcs() {
        const fx_classes = await fs.readdir(class_path);

        for (const file of fx_classes) {
            if (file.endsWith(".js")) {
                console.log(`      ${file}`);
                const FxClass = require(path.join(class_path, file));
                await this.register(FxClass);
            }
        }
    }
};
