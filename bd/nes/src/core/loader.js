"use strict";
const { initLogger } = require("@core/logger");

class NES {
    static ENVIRONMENTS = {
        d: "development",
        t: "testing",
        s: "staging",
        p: "production",
    };

    static MODULES = [
        "monitor", // sentry.io monitoring
        "config", // load config
        "fx", // common functions
        "db", // database
        "pgboss", // pgboss queueing for wfd
        "nescache", // nescache as a replacement for redis
        "dsl", // dsl
        "customer", // customer specific overrides
        "security", // user-related security
        "auth", // authentication (cookie-based)
        "service", // shared services like email, schema
    ];

    static RESERVED_DSL_FORMS = ["autoinsert", "dsl", "session", "changeset"];

    static TOKEN_NAME = "clara";
    static TOKEN_SECRET = "5pj95x38v1349e9rma34as78khq13x53";

    static ADMIN_BASE_URL = "https://admin.envoylabs.net/";
    static SERVICES_API = `${NES.ADMIN_BASE_URL}service/`;
    static CONFIG_ENDPOINT = `${NES.SERVICES_API}config/`;
    static NES_CONFIG_ENDPOINT = `${NES.CONFIG_ENDPOINT}nes/`;

    static INTEGRATION_BASE_URL = "https://is-prod.clararx.com/";
    static INTEGRATION_API = `${NES.INTEGRATION_BASE_URL}is/`;
    static TRANSFER_SITE_ID = 1000;

    shared = {
        // global static configuration
        TOKEN_NAME: NES.TOKEN_NAME,
        TOKEN_SECRET: NES.TOKEN_SECRET,

        ADMIN_BASE_URL: NES.ADMIN_BASE_URL,
        SERVICES_API: NES.SERVICES_API,
        CONFIG_ENDPOINT: NES.CONFIG_ENDPOINT,
        NES_CONFIG_ENDPOINT: NES.NES_CONFIG_ENDPOINT,
        INTEGRATION_BASE_URL: NES.INTEGRATION_BASE_URL,
        INTEGRATION_API: NES.INTEGRATION_API,
        TRANSFER_SITE_ID: NES.TRANSFER_SITE_ID,

        RESERVED_DSL_FORMS: NES.RESERVED_DSL_FORMS,

        BASE_URL: "http://localhost", // updated in config.js using env vars
        BASE_DOMAIN: "clararx.com", // Customer domain SSO goes to their Google Cloud

        config: {
            // hardcoded server defaults, no need to customize this per customer/env
            limits: {
                timeout: 600000, // 10 mins
                default_rows: 100,
                max_queue_rows: 1000,
                max_rows: 100000000,
                sync_mode: {
                    mix_start_id: 100000,
                },
            },
            nes: {
                username: "admin", // do not change!
                address: "0.0.0.0",
                port: 8080,
                tempdir: "/tmp/nes",
                feature: {
                    wss: false,
                    monitor: false,
                },
            },
            session: {
                key: "nes",
                maxAge: 86400000,
            },

            // loaded in customer.js
            company: {},

            // loaded in db.js
            db: {},

            // loaded in config.js
            env: {}, // from environment variables (via FLY or .env file)
            admin_console: {}, // pulled from admin console based on app + env
            package: {}, // from package.json file
        },

        customer: {}, // parsed from FLY_ID in constructor()

        // loaded in auth.js
        sec_json: {},
        dsl_sec_json: {},

        // loaded in customer.js
        view: {}, // via sql-get.js

        // loaded in dsl.js
        DSL: {},
        DSL_AutoInsert: {},
        formEvents: false,
        dslSearchColumn: false,
        vectorRankColumn: false,
        sourceFilterForms: [
            "list_ncpdp_ecl",
            "list_ncpdp_ext_ecl",
            "inventory_sk_item",
            "list_med_claim_ecl",
        ],
        sourceIds: ["id", "code", "mcr_ref"],

        rls: {
            // loaded in security.js
            global: {},
            // loaded in auth.js
            access: {}, // via get_acces.js per user
        },

        // loaded in server.js
        utils: false,
        transforms: false,
        AppDsl: false,
        crud: false,

        // loaded in service.js
        form: {},
        notify: {},
        schema: {},
    };

    modules = {};

    constructor() {
        // If you want to override the DATABASE_URL, do it here and ONLY here!
        // process.env["DATABASE_URL"] = "...";

        // If FLY_APP_NAME exists, make sure to use it as FLY_ID
        //   instead of whatever is manually set as the FLY_ID
        if ("FLY_APP_NAME" in process.env) {
            process.env["FLY_ID"] = process.env["FLY_APP_NAME"];
        }

        // Fail if FLY_ID not set
        if (!("FLY_ID" in process.env)) {
            throw new Error(`Missing or invalid FLY ID / APP NAME!`);
        }

        const [env, customer] = process.env["FLY_ID"].split("-");
        if (!(env in NES.ENVIRONMENTS)) {
            throw new Error(
                `Invalid FLY ID / APP NAME: ${this.shared.config.env["FLY_ID"]}.\nFirst character must be d, t, s, or p.`
            );
        } else {
            this.shared.customer = customer;
            this.shared.clara_env = NES.ENVIRONMENTS[env];
        }

        if (!process.env["NODE_ENV"]) {
            process.env["NODE_ENV"] = NES.ENVIRONMENTS[env];
        }
    }

    async start() {
        initLogger();

        console.log("Instantiate modules...");
        for (const m of NES.MODULES) {
            console.log(`  ${m}`);
            const ModuleClass = require("./modules/" + m);
            this.modules[m] = new ModuleClass(this);
        }

        // need config initialized before all other modules
        console.log("Initialize modules...");
        console.log("  config...");
        await this.modules.config.init();

        // init all modules
        for (const m of NES.MODULES) {
            if (m == "config")
                // already initialized above
                continue;
            console.log(`  ${m}...`);
            await this.modules[m].init();
        }

        //needed config loaded into `this` object to make sure we have credentials available in server constructor
        const m = "server";
        const ModuleClass = require(`./modules/${m}`);
        this.modules[m] = new ModuleClass(this);
        await this.modules[m].init();
    }
}

(async () => {
    const nes = (global.nes = new NES());
    await nes.start();

    // Subscribe fork workers to listen event
    // and send data to any nes module using function and its params method
    try {
        process.on("message", (msg) => {
            nes.modules[msg.module][msg.func](msg.params);
        });
    } catch (error) {
        console.log(error);
    }
})();
