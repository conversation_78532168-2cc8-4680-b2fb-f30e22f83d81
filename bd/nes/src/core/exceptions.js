module.exports = {
    BadRequestException: {
        status: 400,
        message: "Bad request.",
    },
    UnauthorizedException: {
        status: 401,
        message: "Unauthorized access.",
    },
    ForbiddenException: {
        status: 403,
        message: "Access denied.",
    },
    NotFoundException: {
        status: 404,
        message: "Resource not found.",
    },
    MethodNotAllowedException: {
        status: 405,
        message: "Method not allowed.",
    },
    RequestTimeoutException: {
        status: 408,
        message: "Request timed out.",
    },
    ConflictException: {
        status: 409,
        message: "Conflict with the current state of the resource.",
    },
    PayloadTooLargeException: {
        status: 413,
        message: "Payload too large. The maximum upload size is 100MB.",
    },
    URITooLongException: {
        status: 414,
        message: "URI too long.",
    },
    UnsupportedMediaTypeException: {
        status: 415,
        message: "Unsupported media type.",
    },
    InternalServerErrorException: {
        status: 500,
        message: "Internal server error.",
    },
    NotImplementedException: {
        status: 501,
        message: "Not implemented.",
    },
    BadGatewayException: {
        status: 502,
        message: "Bad gateway.",
    },
    ServiceUnavailableException: {
        status: 503,
        message: "Service unavailable.",
    },
    GatewayTimeoutException: {
        status: 504,
        message: "Gateway timeout.",
    },
    LoopDetectedException: {
        status: 508,
        message: "Loop detected.",
    },
};
