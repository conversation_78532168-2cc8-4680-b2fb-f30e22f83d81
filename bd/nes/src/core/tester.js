"use strict";
const fs = require("fs");
const path = require("path");
const mocks_path = global.nesroot + "__mocks__";
const _ = require("lodash");

/**
 * @class MocksLoaderClass
 * @description This class is responsible for loading mock data for the API for unit testing
 * Please pass in your context object with the following format
 * ctx.is_test = true if in a unit test environment
 * ctx.mocks.admin_request = [{ url: url string of mocked path, filename: filename of mock file in json format }]
 * ctx.mocks.form_request = [{ form_name: name of DSL form to fetch, filenames: [list of mock filenames to fetch]}]
 * i.e. __mocks__/surescripts/responses/ss_message.json will pass in ss_message
 */

class MocksLoaderClass {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * @function fetchMockView
     * @description Fetches the mock view for a given form name
     * @param {string} form_name - The name of the form to fetch
     * @param {Object} params - The parameters to apply to the view data, if respect_filters is true, the params will be used to filter the data
     * @returns {Object} The mock view data
     */
    fetchMockView(form_name, params) {
        const dsl_mocks = this.ctx.dsl_mocks;
        if (!this.ctx.is_test || !Array.isArray(dsl_mocks)) {
            return null;
        }

        const form_mocks = _.filter(this.ctx.dsl_mocks, {
            form_name: form_name,
        });
        if (form_mocks.length === 0) {
            console.warn(`Missing mock file for form request ${form_name}`);
            return null;
        }

        const results = [];
        const mock_data = form_mocks[0];
        let filters = [];
        if (mock_data.respect_filters) {
            if (Array.isArray(params.filter)) {
                filters = params.filter.reduce((acc, filter) => {
                    const [field, value] = filter.split(":");
                    acc[field] = value;
                    return acc;
                }, {});
                filters = Object.keys(filters)
                    .filter((field) =>
                        mock_data.respect_filters.includes(field)
                    )
                    .reduce((acc, field) => {
                        acc[field] = filters[field];
                        return acc;
                    }, {});
            } else if (typeof params.filter === "string") {
                const [field, value] = params.filter.split(":");
                if (mock_data.respect_filters.includes(field)) {
                    filters = { [field]: value };
                } else {
                    filters = {};
                }
            }
        }
        _.forEach(form_mocks[0].filenames, (filename) => {
            const json_data = this.loadFile(
                filename,
                mocks_path,
                filters,
                form_name
            );
            if (json_data) {
                results.push(json_data);
            }
        });
        return results;
    }

    /**
     * @function fetchMockAdminRequest
     * @description Fetches the mock admin request for a given URL
     * @param {string} url - The URL of the admin request to fetch
     * @returns {Object} The mock admin request data
     */
    fetchMockAdminRequest(url) {
        const admin_mocks = this.ctx.admin_mocks;
        if (!this.ctx.is_test || !Array.isArray(admin_mocks)) {
            return null;
        }

        // Parse the URL
        const parsed_url = new URL(url);

        // Get the path and remove the last segment
        const path_segments = parsed_url.pathname.split("/");
        path_segments.pop(); // Remove the last segment
        const new_path = path_segments.join("/");

        const admin_mock_forms = _.filter(this.ctx.admin_mocks, {
            url: new_path,
        });
        if (admin_mock_forms.length === 0) {
            console.warn(`Missing mock file for admin request ${url}`);
            return null;
        }

        return this.loadFile(admin_mock_forms[0].filename) || {};
    }

    /**
     * @function loadFile
     * @description Loads a file from the mocks directory
     * @param {string} filename - The name of the file to load
     * @param {string} directory_path - The path to the directory to load from
     * @param {Array} filters - The filters to apply to the file data
     * @param {string} form - The DSL form to load
     * @returns {Object} The file data
     */
    loadFile(filename, directory_path = mocks_path, filters = {}, form = null) {
        try {
            const files = fs.readdirSync(directory_path);
            for (const file of files) {
                const file_path = path.join(directory_path, file);
                const stats = fs.statSync(file_path);
                if (stats.isDirectory()) {
                    // If it's a directory, call load_file recursively
                    const data = this.loadFile(
                        filename,
                        file_path,
                        filters,
                        form
                    );
                    if (data) {
                        return data;
                    }
                } else if (path.extname(file) === ".json") {
                    const file_name_without_extension = path.basename(
                        file,
                        ".json"
                    );
                    if (file_name_without_extension === filename) {
                        const data = fs.readFileSync(file_path, "utf8");
                        let json_data = JSON.parse(data);
                        if (Object.keys(filters).length > 0) {
                            const filtered_data = _.filter(
                                [json_data],
                                filters
                            );
                            json_data =
                                filtered_data.length > 0
                                    ? filtered_data[0]
                                    : null;
                        }
                        const default_dsl_vars =
                            this.ctx?.test_vars?.default_dsl_vars || [];
                        const filtered_form_vars = default_dsl_vars.filter(
                            (vars) =>
                                vars.form === form || vars.form === undefined
                        );
                        filtered_form_vars.forEach((vars) => {
                            _.assignIn(json_data, vars);
                        });

                        return json_data;
                    }
                }
            }
        } catch (err) {
            console.log("Unable to scan directory: " + err);
        }
    }
}

/**
 * Detects circular references within an class object and throws an error if any are found.
 * This function is skipped in production environment for performance reasons.
 * @param {Object} objectClass - The class object to check for circular references.
 */
function detectCircleReferences(objectClass) {
    // Save traversed references here
    const traversedProps = new Set();
    const cycles = [];

    // Recursive function to go over objects/arrays
    const traverse = function (currentObj, path) {
        // If we saw a node it's a cycle, no need to traverse its entries
        if (traversedProps.has(currentObj)) {
            cycles.push(path);
            return;
        }

        traversedProps.add(currentObj);

        // Traversing the entries
        for (const key in currentObj) {
            const value = currentObj[key];
            // We don't want to care about the falsy values
            // Only objects and arrays can produce the cycles and they are truthy
            if (
                currentObj.hasOwnProperty(key) &&
                value &&
                typeof value === "object"
            ) {
                if (value.constructor === objectClass) {
                    const newPath =
                        currentObj.constructor === Array
                            ? `${path}[${key}]`
                            : `${path}.${key}`;
                    traverse(value, newPath);
                }
            }
        }
    };

    // Start traversal from the prototype of the class to check for circular references within static properties
    traverse(objectClass.prototype, "root");
    if (cycles.length > 0) {
        const error = `Circular class reference detected ${cycles.join(", ")}`;
        console.error(error);
        throw new Error(error);
    }
    return cycles;
}

/**
 * @function registerTestEnv
 * @description Registers the hooks for unit tests in the Koa middleware
 * @param {Object} options - configuration options
 */
function registerTestEnv(options = {}) {
    return async (ctx, next) => {
        if (
            ctx.headers["x-unit-test"] &&
            (options?.config?.env["NODE_ENV"] == "development" ||
                options?.config?.env["NODE_ENV"] == "testing")
        ) {
            ctx.is_test = true;
            ctx.dsl_mocks = ctx.get("x-dsl-mocks") || null;
            if (ctx.dsl_mocks) {
                ctx.dsl_mocks = JSON.parse(
                    new Buffer.from(ctx.dsl_mocks, "base64").toString()
                );
            }
            ctx.admin_mocks = ctx.get("x-admin-mocks") || null;
            if (ctx.admin_mocks) {
                ctx.admin_mocks = JSON.parse(
                    new Buffer.from(ctx.admin_mocks, "base64").toString()
                );
            }

            ctx.test_vars = ctx.get("x-test-vars") || null;
            if (ctx.test_vars) {
                ctx.test_vars = JSON.parse(
                    new Buffer.from(ctx.test_vars, "base64").toString()
                );
            }
        }
        await next();
    };
}

/**
 * Recursively collects all JavaScript files from a specified directory.
 * @param {string} dir - The directory to search within.
 * @param {Array} fileList - An array to accumulate the file paths.
 * @returns {Array} The list of JavaScript file paths.
 */
function getAllJSFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    files.forEach((file) => {
        const filePath = path.join(dir, file);
        if (fs.statSync(filePath).isDirectory()) {
            getAllJSFiles(filePath, fileList);
        } else if (file.endsWith(".js")) {
            fileList.push(filePath);
        }
    });
    return fileList;
}

/**
 * @function detectCircularReferencesInProject
 * @description Scans all JavaScript files in a given directory for circular references in class definitions.
 * @param {string} rootDir - The root directory to scan for JavaScript files.
 */
function detectCircularReferencesInProject(rootDir) {
    console.log(`Looking for circular class references in ${rootDir}`);
    const jsFiles = getAllJSFiles(rootDir);
    jsFiles.forEach((file) => {
        const moduleExports = require(file);
        Object.keys(moduleExports).forEach((exportKey) => {
            const exportValue = moduleExports[exportKey];
            if (typeof exportValue === "function" && exportValue.prototype) {
                try {
                    // Assuming detectCircleReferences is globally available or imported
                    detectCircleReferences(exportValue);
                } catch (error) {
                    console.error(
                        `Circular reference detected in class ${exportKey} from ${file}: ${error.message}`
                    );
                }
            }
        });
    });
}

module.exports = {
    registerTestEnv,
    MocksLoaderClass,
    detectCircularReferencesInProject,
};
