"use strict";
const Sequelize = require("sequelize");

const _ = require("lodash");

module.exports = class AppDsl {
    constructor(nes) {
        this.auto_insert = nes.shared.DSL_AutoInsert;
        this.db = nes.modules.db;
        this.fx = nes.modules.fx;
        this.get_type = nes.modules.dsl.get_type;
        this.shared = nes.shared;
        this.sourceFilterForms = nes.shared.sourceFilterForms; // source filter forms[include forms for which sourced column is code and code is not unique in base form]
        this.sourceIds = nes.shared.sourceIds; // source ids[allowed foreign keys]
        this.form = null;
    }
    /**
     * DSL deployment function. We validate, create/update cson
     * in dsl table and create/update schema for form here.
     * TODO TEST BEFORE MERGE TSVECTOR SCENARIOS
     */
    create = async (nes, js, create_only = false, force = false) => {
        const vFunExists = await this.check_version_function();
        for (const [form_name, newform] of Object.entries(js)) {
            this.form = form_name;
            try {
                newform.fields.archived = {
                    view: {
                        note: "",
                        transform: [],
                        readonly: false,
                        control: "select",
                        format: "",
                        label: "Archived",
                        validate: [],
                        class: "",
                    },
                    model: {
                        max: null,
                        rounding: null,
                        required: false,
                        min: null,
                        prefill: [],
                        type: "boolean",
                        autoinsert: true,
                        save: true,
                        subfields: {},
                        validate: [],
                        transform_filter: [],
                        sourceid: "id",
                        multi: false,
                        active: true,
                        transform: [],
                        if: {},
                        template: null,
                        source: null,
                        default: false,
                        transform_post: [],
                        sourcefilter: {},
                        subfields_sort: [],
                    },
                };
                // view.control = 'select';
                // newform.fields.archived.model.type = "boolean";
            } catch (e) {
                console.log(e);
            }
            let vectorSearchField = {};
            const vectorFields = [];
            const oldVectorFields = [];
            const form = { ...newform };
            const new_form = JSON.stringify({ ...form });
            let old_form = await nes.shared.DSL[form_name];
            const new_fields = Object.keys(form.fields);

            if (old_form == undefined) {
                old_form = null;
            } else {
                // Ensure old_form is parsed if it's a string
                if (typeof old_form === "string") {
                    try {
                        old_form = JSON.parse(old_form);
                    } catch (e) {
                        console.error(
                            `Error parsing old_form JSON for ${form_name}:`,
                            e
                        );
                        old_form = null; // Handle parsing error
                    }
                }
            }

            if (create_only && old_form != null) {
                console.warning(
                    form_name +
                        " already exists and set to create_only=" +
                        create_only +
                        ", skipping"
                );
                continue;
            }
            if (old_form) {
                for (const [field, value] of Object.entries(old_form.fields)) {
                    if (value.model.search && value.model.search !== "") {
                        oldVectorFields.push({
                            weight: value.model.search,
                            field: field,
                            // Add source info if available in old form for comparison
                            source: value.model.source || false,
                            isCode: ["code", "mcr_ref"].includes(
                                value.model.sourceid
                            ),
                            code: value.model.sourceid,
                            sourceFilter: value.model.sourcefilter || false,
                            sourceId: value.model.sourceid || "id",
                        });
                    }
                }
            }
            // Add auto_name to oldVectorFields if it existed implicitly
            if (oldVectorFields.length > 0) {
                oldVectorFields.push({
                    source: false,
                    weight: "D", // Assuming D was the implicit weight
                    field: "auto_name",
                });
            }

            console.log(
                `[DSL Create] Form: ${form_name}, Old Vector Fields:`,
                JSON.stringify(oldVectorFields)
            ); // Log old fields

            const gerund_tables_list = [];
            const new_model = {};
            const source_list = [];
            const subform_tables_list = [];

            //build model, loop through all form fields
            for (const [key, value] of Object.entries(form.fields)) {
                const vobj = {
                    source: false,
                    weight: value.model?.search,
                    field: key,
                    isCode: false,
                    sourceId: "id",
                    sourceFilter: false,
                };
                let type = value.model.type;
                if (type) {
                    type = this.get_column_type(type);
                }

                if (value.view.control == "area") {
                    type = Sequelize.TEXT;
                }
                //check for form associations
                if (key != "id" && key != "deleted" && key != "archived") {
                    if (
                        old_form?.fields[key]?.model?.source &&
                        !value.model.source
                    ) {
                        source_list.push({
                            [old_form.fields[key].model.source]: {
                                columnModel: {
                                    name: key,
                                    createFK: false,
                                },
                            },
                        });
                    }
                    if (
                        !_.isEmpty(value.model.source) &&
                        typeof value.model.source == "string" &&
                        value.model.sourceid &&
                        value.model.type != "subform" &&
                        !value.model.multi
                    ) {
                        type = Sequelize.INTEGER;
                        let cType = "int";
                        let tKey = "id";
                        if (
                            ["code", "mcr_ref"].includes(value.model.sourceid)
                        ) {
                            type = Sequelize.TEXT;
                            cType = "text";
                            tKey = value.model.sourceid;
                        }
                        source_list.push({
                            [value.model.source]: {
                                targetKey: tKey,
                                columnModel: {
                                    name: key,
                                    type: type,
                                    csonType: cType,
                                    Model: form_name,
                                    fieldName: key,
                                    createFK: value.model.fk,
                                    _modelAttribute: true,
                                    field: key,
                                },
                            },
                        });
                        if (value.model.search && value.model.search !== "") {
                            vobj.source = value.model?.source
                                ? `form_${value.model?.source}`
                                : false;
                            vobj.sourceFilter = value.model?.sourcefilter;
                            vobj.isCode = tKey !== "id";
                            vobj.code = tKey;
                            vectorFields.push(vobj);
                        }
                        continue;
                    }

                    //check if source and multi true, create gerund table entry
                    if (
                        !_.isEmpty(value.model.source) &&
                        typeof value.model.source == "string" &&
                        value.model.multi &&
                        value.model.type != "subform"
                    ) {
                        if (value.model.source.indexOf("{") == -1) {
                            gerund_tables_list.push({ [key]: value.model });
                        } else {
                            for (const [k, _] of Object.entries(
                                value.model.sourcefilter
                            )) {
                                gerund_tables_list.push({ [key]: k });
                            }
                        }
                        continue;
                    }

                    //check if source table is used as subform, create subform table entry
                    if (
                        !_.isEmpty(value.model.source) &&
                        typeof value.model.source == "string" &&
                        value.model.type == "subform"
                    ) {
                        if (value.model.source.indexOf("{") == -1) {
                            subform_tables_list.push({
                                [key]: value.model.source,
                            });
                        } else {
                            for (const [k, _] of Object.entries(
                                value.model.sourcefilter
                            )) {
                                subform_tables_list.push({ [key]: k });
                            }
                        }
                        continue;
                    }
                }

                if (value.model.search && value.model.search !== "") {
                    vobj.source = false;
                    vectorFields.push(vobj);
                }
                new_model[key] = {
                    type: type,
                    Model: form_name,
                    fieldName: key,
                    _modelAttribute: true,
                    field: key,
                    csonType: value.model.type,
                };

                if (key == "use_nes_auth") {
                    new_model[key].defaultValue = 1;
                }
                if (key == "archived" || key == "deleted") {
                    new_model[key].defaultValue = false;
                }
            }

            //create indexes and add it to indexes array
            const indexes = [];
            indexes.push({
                name: "form_" + form_name + "_pkey",
                unique: true,
                using: "BTREE",
                fields: ["id"],
            });
            for (const [key, value] of Object.entries(form.model.indexes)) {
                const unique = key === "unique" ? true : false;
                const that = this;
                value.forEach(function (val) {
                    if (Array.isArray(val)) {
                        val.forEach(function (field) {
                            if (!new_fields.includes(field)) {
                                throw {
                                    error:
                                        field +
                                        " doesn't exist, It can not be used as unique index",
                                };
                            }
                        });
                        let indexName = `${key === "unique" ? "u" : "m"}form_${form_name}_${val.sort().join("_")}`;
                        if (indexName.length >= 63) {
                            indexName = that.fx.md5(indexName);
                        }
                        indexes.push({
                            name: indexName,
                            unique: unique,
                            using: "BTREE",
                            fields: val,
                        });
                    } else {
                        console.error("not able to create " + key + " index");
                    }
                });
            }

            //Test form field and form levels model validators to make sure they are in NES
            const field_validators = Object.keys(js).flatMap((form) =>
                Object.keys(js[form].fields).flatMap((field) =>
                    js[form].fields[field].model.validate.map(
                        (validate) => validate.name
                    )
                )
            );
            const model_validators = Object.keys(js).flatMap((form) =>
                Object.keys(js[form].model.validate).flatMap(
                    (validate) => js[form].model.validate[validate].name
                )
            );
            const validators = [
                ...new Set(field_validators),
                ...new Set(model_validators),
            ];
            validators.forEach((validator) => {
                if (!(validator in nes.validators)) {
                    console.log(`Validator not found in NES: ${validator}`);
                    throw new Error(
                        `Validator ${validator} is not defined in NES. Please define under api/form/validators to fix.`
                    );
                }
            });

            // === Start: Vector Field Preparation ===
            // Add autoname to new vectorFields if necessary
            if (
                vectorFields.length > 0 &&
                !vectorFields.some((f) => f.field === "auto_name")
            ) {
                vectorFields.push({
                    source: false,
                    weight: "D",
                    field: "auto_name",
                });
            }

            // Add autoname to oldVectorFields if necessary
            if (
                oldVectorFields.length > 0 &&
                !oldVectorFields.some((f) => f.field === "auto_name")
            ) {
                oldVectorFields.push({
                    source: false,
                    weight: "D",
                    field: "auto_name",
                    isCode: false,
                    code: undefined,
                    sourceFilter: {},
                    sourceId: "id",
                });
            }
            // === End: Vector Field Preparation ===

            console.log(
                `[DSL Create] Form: ${form_name}, Old Vector Fields:`,
                JSON.stringify(oldVectorFields)
            ); // Log old fields

            // add/update tsvector column check if there are any changes
            vectorSearchField = {
                field: this.shared.dslSearchColumn,
                table: `form_${form_name}`,
                op: null,
            };
            console.log(
                `[DSL Create] Initialized vectorSearchField: ${JSON.stringify(vectorSearchField, null, 2)}`
            );

            // Calculate the type based on the final vectorFields list
            if (vectorFields.length > 0) {
                vectorSearchField.type =
                    "tsvector GENERATED ALWAYS AS (" +
                    vectorFields
                        .map((o) => {
                            const dbFilter = this.generateAutonameCall(o);
                            return `setweight(to_tsvector('simple'::regconfig, (COALESCE(${dbFilter}::character varying, ''::character varying))::text), '${o.weight}'::"char")`;
                        })
                        .join(" || ") +
                    ") STORED";
            } else {
                vectorSearchField.type = null; // No type if no fields
            }

            // Determine the operation based on comparison
            const hasNewVectors = vectorFields.length > 0;
            const hasOldVectors = oldVectorFields.length > 0;
            console.log(
                `[DSL Create] hasNewVectors: ${hasNewVectors}, hasOldVectors: ${hasOldVectors}`
            ); // Log vector presence

            if (hasNewVectors && !hasOldVectors) {
                console.log("[DSL Create] Setting op=addColumn"); // Log branch taken
                vectorSearchField["op"] = "addColumn";
            } else if (!hasNewVectors && hasOldVectors) {
                console.log("[DSL Create] Setting op=removeColumn"); // Log branch taken
                vectorSearchField["op"] = "removeColumn";
            } else if (hasNewVectors && hasOldVectors) {
                console.log(
                    "[DSL Create] Entering comparison block (hasNew && hasOld)"
                ); // Log branch taken
                // Sort fields consistently before comparison
                const sortCriteria = [
                    "field",
                    "weight",
                    "source",
                    "isCode",
                    "code",
                    "sourceId", // Added sourceId for more robust comparison
                ];
                const sortedNew = _.sortBy(vectorFields, sortCriteria);
                const sortedOld = _.sortBy(oldVectorFields, sortCriteria);

                // Log sorted arrays for detailed debugging
                console.log(
                    "[DSL Create] Sorted New for comparison:",
                    JSON.stringify(sortedNew, null, 2)
                );
                console.log(
                    "[DSL Create] Sorted Old for comparison:",
                    JSON.stringify(sortedOld, null, 2)
                );

                const areEqual = _.isEqual(sortedNew, sortedOld); // Store result
                console.log(
                    `[DSL Create] Result of _.isEqual(sortedNew, sortedOld): ${areEqual}`
                ); // Log comparison result

                if (!areEqual) {
                    // Use stored result
                    console.log(
                        "[DSL Create] Vector fields changed, setting op=changeColumn" // Log branch taken
                    );
                    vectorSearchField["op"] = "changeColumn";
                } else {
                    console.log(
                        "[DSL Create] Vector fields appear unchanged, op remains null." // Log branch taken
                    );
                }
            } else {
                console.log(
                    "[DSL Create] No new or old vectors, op remains null."
                ); // Log branch taken
            }

            console.log(
                `form - ${form_name} vector fields ${vectorFields.length > 0 ? vectorFields.map((o) => o.field).join(", ") : "None"}`
            );
            //add primary key contraint on id
            new_model.id["primaryKey"] = true;
            new_model.id["autoIncrement"] = true;
            new_model.id["_autoGenerated"] = true;

            //set type = boolean explicitly to make sure that these fields have one of the following as value [null, false, true]
            new_model.deleted["type"] = Sequelize.BOOLEAN;
            new_model.archived["type"] = Sequelize.BOOLEAN;

            //delete default value attribute from the model so that postgres can handle it internally
            delete new_model.id.defaultValue;

            console.log(
                `[DSL Create] vectorSearchField before passing to __set_model: ${JSON.stringify(vectorSearchField, null, 2)}`
            );

            const formMode = old_form != null ? "update" : "create";
            const syncMode = form.model.sync_mode ?? "none";
            const result = await nes.shared.crud.__set_model(
                form_name,
                new_model,
                {
                    source_list,
                    gerund_tables_list,
                    subform_tables_list,
                    indexes,
                },
                "form_",
                { vectorSearchField, vFunExists, formMode, syncMode },
                force
            );
            if (result) {
                //update or insert cson against form_name
                // change this to save.js create/update
                if (old_form != null) {
                    await nes.shared.crud.form_update(
                        "dsl",
                        { value: new_form },
                        { key: form_name },
                        ""
                    );
                } else {
                    await nes.shared.crud.form_create(
                        "dsl",
                        { key: form_name, value: new_form },
                        ""
                    );
                }
            }
            return true;
        }
    };

    generateAutonameCall = (vectorField) => {
        //TODO: add support for date and datetime
        let cond = "";
        if (!vectorField.source) {
            return vectorField.field;
        }
        const form = vectorField.source.replace("form_", "");
        if (
            this.sourceFilterForms.includes(this.form) &&
            vectorField.isCode &&
            vectorField.sourceFilter
        ) {
            const form_dsl = this.shared.DSL[form];
            if (
                (!form_dsl ||
                    !form_dsl.model.save ||
                    Object.keys(vectorField.sourceFilter).length === 0) &&
                vectorField.isCode
            ) {
                return `crx_get_auto_name('${vectorField.source}','${vectorField.code}',${vectorField.field})`;
            }
            const form_fields = Object.keys(form_dsl.fields);
            for (const [fk, fv] of Object.entries(vectorField.sourceFilter)) {
                if (!form_fields.includes(fk)) {
                    continue;
                }
                if (!fv.static) {
                    continue;
                }
                const ot = this.get_type(fv.static);
                if (ot === "int") {
                    cond += ` AND ${fk} = ${fv.static} `;
                } else if (ot === "string") {
                    cond += ` AND ${fk} = '${fv.static}' `;
                } else if (ot === "array") {
                    if (fv.static.length === 0) {
                        continue;
                    }
                    const avt = this.get_type(fv.static[0]);
                    if (avt === "int") {
                        cond += ` AND ${fk} IN (${fv.static.join(",")}) `;
                    } else if (avt === "string") {
                        cond += ` AND ${fk} IN ('${fv.static.join("','")}') `;
                    }
                }
            }
            return `crx_get_auto_name('${vectorField.source}','${vectorField.code}',${vectorField.field}, $where$${cond}$where$)`;
        } else if (vectorField.isCode) {
            return `crx_get_auto_name('${vectorField.source}','${vectorField.code}',${vectorField.field})`;
        } else if (vectorField.sourceId === "id") {
            return `crx_get_auto_name('${vectorField.source}','id',${vectorField.field})`;
        } else {
            console.warn("Filters can only have code or id as source field");
            return cond;
        }
    };

    check_version_function = async (funName = "crx_versioning") => {
        try {
            const res = await this.db.env.rw.query(
                `select oid,proname,pronamespace from pg_proc where proname = '${funName}';`
            );
            if (res.length > 0 && res[0].proname && res[0].proname !== "")
                return true;
            else return false;
        } catch (error) {
            throw Error(error);
        }
    };

    /**
     * returns sequelize datatype
     */
    get_column_type = (type) => {
        if (type == "int") {
            return Sequelize.INTEGER;
        } else if (type == "decimal") {
            return Sequelize.FLOAT;
        } else if (type == "password") {
            return Sequelize.TEXT;
        } else if (type == "date") {
            return "date";
        } else if (type == "datetime") {
            return "TIMESTAMP WITHOUT TIME ZONE";
        } else if (type == "boolean") {
            return Sequelize.BOOLEAN;
        } else {
            return Sequelize.TEXT;
        }
    };
};
