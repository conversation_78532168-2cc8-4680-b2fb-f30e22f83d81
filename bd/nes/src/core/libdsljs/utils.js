"use strict";
const moment = require("moment");
const _ = require("lodash");
const uuid = require("uuid").v4;
const forge = require("node-forge");
const btoa = require("btoa");

/**
 * @param form parent form name
 * @param key field name in parent form
 * @param srcForm nam of source table
 * Returns gerund link table name
 */

// TODO this should be in fx
exports.is_false = (v) => {
    if (typeof v == "number") {
        return false;
    }
    return v == false || v == 0 || v == "0" || v == "f" || _.isEmpty(v);
};

/**
 * Description.
 * @params str - string to be parsed.
 * @params startsWith - Enclosure start e.g {([.
 * @params endsWith - Enclosure end e.g ])}.
 ** Returns array of strings enclosed in "{}".
 */
exports.str_btw_enclosed = (str, startsWith = "{", endsWith = "}") => {
    const cols = [];
    str.split(endsWith).forEach((str) => {
        if (str.startsWith(startsWith)) {
            str.split(startsWith).forEach((str) => {
                if (str != "") cols.push(str);
            });
        } else if (str.indexOf(startsWith) != -1) {
            str.substring(str.indexOf(startsWith))
                .split(startsWith)
                .forEach((str) => {
                    if (str != "") cols.push(str);
                });
        }
    });
    return cols;
};

/**
 * Description.
 ** Returns array of strings seperated by spaces
 */
exports._space_seperated_strings = (str) => {
    return str.split(" ");
};

/**
 * Description.
 * @params auto_name_field - formatted auto_name string from cson.
 ** Returns array of columns in string.
 */
exports._get_column_names = (auto_name_field) => {
    const col_names = [];
    const columns = [];
    const autoname_strings = this._space_seperated_strings(auto_name_field);
    autoname_strings.forEach((str) => {
        col_names.push(this.str_btw_enclosed(str));
    });
    col_names.forEach((cols) => {
        cols.forEach((col) => {
            columns.push(col);
        });
    });
    return columns;
};

/**
 * @param form parent form name
 * @param key field name in parent form
 * @param srcForm nam of source table
 * Returns gerund link table name
 */
exports.getGerundTableName = (form, key, srcForm) => {
    return "gr_form_" + form + "_" + key + "_to_" + srcForm + "_id";
};

exports.get_client = (user) => {
    if (user.role == "patient") return "jspatient";
    else if (user.role == "payor") return "jspayor";
    else if (user.role == "physician") return "jsphysician";
    else return "jshomebase";
};

exports.get_version = () => {
    const pkg = require("./package.json");
    if ("version" in pkg) {
        return pkg.version;
    }
};

/**
 * @param form parent form name
 * @param srcForm nam of source table
 * Returns subform link table name
 */
exports.getSubformTableName = (form, srcForm) => {
    return "sf_form_" + form + "_to_" + srcForm;
};

exports._is_float = (val, rounding) => {
    if (val == null) {
        return "";
    }
    const v = parseFloat(val);
    if (isNaN(v)) {
        return 0;
    }
    const r = Math.round(v / rounding) * rounding;
    return Math.round(r * 100000) / 100000;
};

/**
 * @description Perform data cleansing and data type validation
 * @param key field name
 * @param type data type of field
 * @param value data in form for that field
 * @param field cson field
 * @param crud CRUD library
 */
exports.clean_data = async (key, type, value, field, crud) => {
    if (typeof value !== "number" && !this._sanitize(value)) {
        return null;
    }
    if (type == "text") {
        if (Array.isArray(value) && field.model.multi == true) {
            return value;
        } else if (Array.isArray(value) && field.model.multi != true) {
            return value;
        } else if (
            field.model.source &&
            typeof field.model.source == "string"
        ) {
            if (field.model.sourceid) {
                const source_form = await crud.__get_model(field.model.source);
                if (
                    source_form.rawAttributes[field.model.sourceid].type ==
                    "INTEGER"
                ) {
                    try {
                        return parseInt(value);
                    } catch (e) {
                        console.error(e);
                        throw new Error(
                            this.error(key, " must be of type integer")
                        );
                    }
                }
                return value;
            }
        } else if (typeof value == "string") {
            return value;
        }
        throw new Error(this.error(key, " must be of type string"));
    }
    if (type == "decimal") {
        if (typeof value == "number") {
            if (field.model.rounding) {
                return this._is_float(value, field.model.rounding);
            }
            return value;
        } else {
            throw new Error(this.error(key, " must be of type decimal"));
        }
    }
    if (type == "int") {
        if (field.model.source && typeof field.model.source == "string") {
            if (field.model.sourceid) {
                const source_form = await crud.__get_model(field.model.source);
                if (
                    source_form.rawAttributes[field.model.sourceid].type ==
                        "INTEGER" &&
                    !field.model.multi
                ) {
                    try {
                        return parseInt(value);
                    } catch (e) {
                        console.error(e);
                        throw new Error(
                            this.error(key, " must be of type integer")
                        );
                    }
                } else if (field.model.multi) {
                    if (!Array.isArray(value)) {
                        value = [value];
                    }
                    const that = this;
                    value.forEach((val) => {
                        try {
                            return parseInt(val);
                        } catch (e) {
                            console.error(e);
                            throw new Error(
                                that.error(
                                    key,
                                    " must be of type integer array"
                                )
                            );
                        }
                    });
                }
                return value;
            }
        }
        if (typeof value == "number") {
            return value;
        }
        throw new Error(this.error(key, " must be of type integer"));
    }
    if (type == "boolean") {
        // only is true if the value is of type Boolean
        // another way is to do Boolean(value) === value this is shorter and passes tests
        if (!!value === value) {
            return value;
        } else {
            throw new Error(
                this.error(key, " needs to be boolean true or false")
            );
        }
    }
    if (type == "datetime") {
        if (typeof value == "string") {
            try {
                return moment(value).utc().format("MM/DD/YYYY HH:mm:ss");
            } catch (e) {
                console.error(e);
                throw new Error(
                    this.error(key, " needs to be in MM/DD/YYYY HH:mm:ss")
                );
            }
        } else {
            throw new Error(
                this.error(key, " needs to be in MM/DD/YYYY HH:mm:ss")
            );
        }
    }
    if (type == "date") {
        // update this validation to use moment is Valid
        if (typeof value == "string") {
            const date = moment(value);
            if (date.isValid()) {
                return date.utc().format("MM/DD/YYYY");
            }
        }
        throw new Error(this.error(key, " needs to be in MM/DD/YYYY"));
    }
    if (type == "time") {
        if (typeof value == "string") {
            const newTime = moment(value, "HH:mm a", true);
            if (newTime.isValid()) return newTime.format("hh:mm a");
            else {
                console.error(
                    `Key:${key}'s value needs to be a in proper format HH:MM AM/PM got: ${value}`
                );
                throw new Error(
                    this.error(
                        key,
                        " invalid format Check Error logs for details"
                    )
                );
            }
        } else {
            console.error(
                `Key:${key}'s value needs to be a string got ${typeof value}`
            );
            throw new Error(
                this.error(
                    key,
                    " invalid datatype Check Error logs for details"
                )
            );
        }
    }
    if (type == "subform") {
        if (Array.isArray(value)) {
            return value;
        } else {
            throw new Error(
                this.error(key, " needs to be a list of form data")
            );
        }
    }
    if (type == "password") {
        return value;
    }
    if (type == "json") {
        if (_.isString(value)) {
            return value;
        }
        return JSON.stringify(value);
    }
    return value;
};

/**
 * @description Perform data validation e.g min, max, selected value
 * @param key field name
 * @param type data type of field
 * @param value data in form for that field
 * @param field cson field
 */
exports.validate = async (key, type, value, field) => {
    if (typeof value !== "number" && !this._sanitize(value)) {
        return null;
    }
    if (type == "text") {
        if (field.model.source && Array.isArray(field.model.source)) {
            let val = [];
            if (!Array.isArray(value) && field.model.multi == true) {
                val = value.split(",");
                val.forEach((el) => {
                    if (!field.model.source.find((element) => element == el))
                        throw (
                            value +
                            " is not an available selection for" +
                            field.model.source
                        );
                });
                return val;
            }
            return value;
        }
        if (field.model.source && typeof field.model.source == "object") {
            if (field.model.multi == true) {
                let val = value;
                if (typeof value == "string") val = value.split(",");
                if (Array.isArray(val)) {
                    val.forEach((el) => {
                        if (!field.model.source[el])
                            throw (
                                el +
                                " is not an available selection for" +
                                field.model.source
                            );
                    });
                    return val;
                }
            } else if (field.model.multi != true) {
                if (field.model.source[value]) {
                    return value;
                } else {
                    throw value + " is not a valid value";
                }
            }
        }
        if (field.model.source && typeof field.model.source == "string") {
            if (field.model.multi == true) {
                let val = value;
                if (typeof value == "string" && value.length > 1)
                    val = value.split(",");
                return val;
            }
        }
        if (typeof value == "string") {
            const length = value.length;
            if (field.model.max && length > field.model.max) {
                throw this.error(
                    key,
                    "should not exceed " + field.model.max + " characters"
                );
            }
            if (field.model.min && length < field.model.min) {
                throw this.error(
                    key,
                    "should consist of at least " +
                        field.model.min +
                        " characters"
                );
            }
            return value;
        } else if (field.model.source && typeof value == "number") {
            return value;
        }
        if (typeof value == "boolean") {
            return value;
        }
        if (Array.isArray(value)) {
            return value;
        }
        throw this.error(key, " must be of type string");
    }
    if (type == "decimal") {
        if (field.model.max && value > field.model.max) {
            throw this.error(key, value + " is larger than " + field.model.max);
        }
        if (field.model.min && value < field.model.min) {
            throw this.error(
                key,
                value + " is smaller than " + field.model.min
            );
        }
        return value;
    }
    if (type == "int") {
        if (field.model.max && value > field.model.max) {
            throw this.error(key, value + " is larger than " + field.model.max);
        }
        if (field.model.min && value < field.model.min) {
            throw this.error(
                key,
                value + " is smaller than " + field.model.min
            );
        }
        return value;
    }
    if (type == "subform") {
        return value;
    }
    if (type == "json") {
        if (Array.isArray(value)) {
            return value;
        }
        if (typeof value == "string") {
            return value;
        }
        throw this.error(key, " must be of type string");
    }
    return value;
};

exports.error = (field, msg, e) => {
    let err = {};
    try {
        if (!field) field = Object.keys(e.fields)[0];
        if (!msg) msg = e.errors[0].message;
        msg = msg.replace(field.toString(), "");
        err = `${field} : ${msg}`;
    } catch {
        err = "There was a problem while saving this form.";
    }
    return err;
};

exports._tracer = (data, ctx) => {
    if (
        !_.isEmpty(data) &&
        !_.isEmpty(data["_meta"]) &&
        !_.isEmpty(data["_meta"]["tracer"])
    ) {
        return data["_meta"]["tracer"];
    }
    return {
        id: uuid(),
        on: moment.utc().format(),
        source: ctx.headers
            ? ctx.headers["clara-client-app"]
                ? ctx.headers["clara-client-app"]
                : ctx.user.username
            : "nes",
    };
};

exports._handle_fields = async (form, cson, data, crud) => {
    for (const [k, v] of Object.entries(data)) {
        //sanitize data
        data[k] = this._sanitize(v);

        //clean data
        try {
            if (!_.isEmpty(cson.fields[k])) {
                data[k] = await this.clean_data(
                    k,
                    cson.fields[k].model.type,
                    data[k],
                    cson.fields[k],
                    crud
                );
                data[k] = await this.validate(
                    k,
                    cson.fields[k].model.type,
                    data[k],
                    cson.fields[k]
                );
                if (cson.fields[k].model.type == false) {
                    data[k] = null;
                }
            }
        } catch (e) {
            console.log(e);
            throw new Error(e);
        }

        //handle password fields
        if (
            !_.isEmpty(cson.fields[k]) &&
            cson.fields[k].model.type === "password"
        ) {
            if (v && v != "") data[k] = this.generate_password(v);
            else delete data[k];
        }
    }
    return data;
};

exports.generate_password = (v) => {
    const salt = "$p5k2$$" + Math.random().toString(36).slice(4);
    let rep =
        salt +
        "$" +
        btoa(forge.pkcs5.pbkdf2(v, salt, 400, 24, forge.md.sha1.create()));
    rep = rep.split("+").join(".");
    return rep;
};

exports._sanitize = (v) => {
    if (typeof v === "string") {
        v = v.trim();
    }
    if (
        v === "" ||
        v === "NULL" ||
        (typeof v === "object" &&
            !Array.isArray(v) &&
            v !== null &&
            Object.keys(v).length === 0)
    ) {
        v = null;
    }
    return v;
};

exports._remove_empty_brackets = (string) => {
    if (typeof string === "string") {
        string = string.replace("()", "");
        string = string.replace("{}", "");
        string = string.replace("[]", "");
        string = string.trim().replace(/^:|:$/g, "").trim();
        return string;
    }
};
