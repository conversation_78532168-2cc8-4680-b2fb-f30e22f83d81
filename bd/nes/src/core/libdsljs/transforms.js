"use strict";
const utils = require("./utils");
const _ = require("lodash");
/**
 * @param ctx ctx
 * @param form parent form's name
 * @param cson parent form's cson
 * @param data parent form data
 * @param field_name name of field on which transform is applied
 * @param get /api/form/operations/get Class instance
 * @param args args for some transforms not all
 * @param transaction core/db/postgresql/transaction Class instance
 */

module.exports.transforms = {
    OnCreateSetField: (ctx, form, cson, data, k, get, args) => {
        if (args && args.method == "CREATE") {
            const transf = args.transform;
            const tr_args = transf.arguments;
            if (this.transforms[tr_args["fun"]] instanceof Function) {
                data = this.transforms[tr_args["fun"]](
                    form,
                    cson,
                    data,
                    k,
                    get,
                    args
                );
            }
            if (tr_args["field"] && tr_args["value"])
                data[tr_args["field"]] = tr_args["value"];
        }
        return data;
    },
    OnUpdateSetField: (ctx, form, cson, data, k, get, args) => {
        if (args && args.method == "UPDATE") {
            const transf = args.transform;
            const tr_args = transf.arguments;
            if (this.transforms[tr_args["fun"]] instanceof Function) {
                data = this.transforms[tr_args["fun"]](
                    form,
                    cson,
                    data,
                    k,
                    get,
                    args
                );
            }
            if (tr_args["field"] && tr_args["value"])
                data[tr_args["field"]] = tr_args["value"];
        }
        return data;
    },
    AutoNameFk: async (ctx, form, cson, data, field_name, get) => {
        const field_value = data[field_name];
        if (utils.is_false(field_value)) {
            data[field_name] = "";
            return data;
        }

        //get form source
        const source_form = cson.fields[field_name].model.source;
        let auto_name = "";
        //get auto_name
        try {
            if (cson.fields[field_name].model.sourceid) {
                auto_name = (
                    await get.get_form(ctx, ctx.user, source_form, {
                        filter:
                            [cson.fields[field_name].model.sourceid] +
                            ":" +
                            field_value,
                    })
                )[0].auto_name;
            } else {
                auto_name = (
                    await get.get_form(ctx, ctx.user, source_form, {
                        filter: "id:" + field_value,
                    })
                )[0].auto_name;
            }
        } catch (e) {
            console.log(e);
        }
        const auto_field = field_name + "_auto_name";
        data[auto_field] = _.isEmpty(auto_name) ? "" : auto_name;

        return data;
    },
    AutoNote: async (
        ctx,
        form,
        cson,
        data,
        field_name,
        get,
        args = {},
        transaction
    ) => {
        const _custom_dsl_map = args["custom_map"];

        if (args["subject"] == undefined) {
            throw `DSL config missing *subject* argument for Auto Note for form ${form}`;
        }
        let note = "";
        try {
            note = data["_meta"]["text"];
        } catch {
            return data;
        }

        const form_data = {
            patient_id: data["patient_id"],
            user_id: args["user"].id,
            user_role: args["user"].role,
            subject: args["subject"],
            note: note,
            template_tag: `${form}:"_meta.text":${data["id"]}`,
        };

        //custom field mapping
        for (const [k, v] of Object.entries(_custom_dsl_map)) {
            if (!_.isEmpty(v)) {
                form_data[v] = data[k];
            }
        }

        let old_note = await get.get_form(ctx, ctx.user, "patient_note", {
            filter: "template_tag:" + form_data["template_tag"],
        });
        if (old_note.length > 0) {
            old_note = old_note[0];
            await transaction.update("patient_note", form_data, old_note["id"]);
        } else {
            await transaction.insert("patient_note", form_data);
        }
        return data;
    },
    validate: (ctx, form, cson, data, field_name, get) => {
        if (!form || !cson || !data || !data[field_name] || !get) {
            return false;
        }
        return true;
    },
    CreateBillingAccountOnInsert: async (
        ctx,
        form,
        cson,
        data,
        field_name,
        get,
        args,
        transaction
    ) => {
        if (!this.transforms.validate(ctx, form, cson, data, "id", get))
            return false;
        const form_data = {
            type: form.charAt(0).toUpperCase() + form.slice(1),
            patient_id: form == "patient" ? data.id : null,
            payer_id: form == "payer" ? data.id : null,
        };
        try {
            let filter;
            if (form === "patient") {
                filter = { filter: "patient_id:" + data.id };
            } else if (form === "payer") {
                filter = { filter: "payer_id:" + data.id };
            }
            let existing_billing = await get.get_form(
                ctx,
                ctx.user,
                "billing_account",
                filter
            );
            if (existing_billing.length > 0) {
                existing_billing = existing_billing[0];
                await transaction.update(
                    "billing_account",
                    form_data,
                    existing_billing["id"]
                );
            } else {
                await transaction.insert("billing_account", form_data);
            }
        } catch (error) {
            console.log("Error at CreatingBillingAccount", error);
        }
        return data;
    },
};
