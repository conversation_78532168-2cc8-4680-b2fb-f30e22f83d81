"use strict";
const utils = require("./utils");
const Sequelize = require("sequelize");
const { Op, QueryTypes } = require("sequelize");
const moment = require("moment");
const _ = require("lodash");

module.exports = class CRUD {
    constructor(nes) {
        this.fx = nes.modules.fx;
        this.shared = nes.shared;
        this.db = nes.modules.db;
        const config = nes.shared.config;
        //connection to db
        // changed dn to ds since ds is being used for writes
        this.sequelize = new Sequelize(
            "postgres://" +
                config.db.env.user +
                ":" +
                config.db.env.password +
                "@" +
                config.db.env.rw.host +
                ":" +
                config.db.env.port +
                "/" +
                config.db.env.database,
            {
                dialect: "postgres",
                dialectOptions: {
                    ssl: false,
                },
                // dialectOptions: {
                //     connectTimeout: 1000
                // },
                pool: {
                    max: config.db.env.pool.max,
                    min: config.db.env.pool.min,
                    idle: config.db.env.requestTimeout,
                },
                logging: false,
            }
        );
        this.op = Op;
        // //queryInterface to fetch table definition to use as model with sequelize
        this.queryInterface = this.sequelize.getQueryInterface();
    }

    /**
     * @description Return Model against form, if table doesn't exists, it creates and empty table and returns its model
     * @param {*} model_name
     * @param {*} prefix  pass null and subform explicitly to determine correct model
     *
     * @returns model for requested model
     */
    __get_model = async (model_name, prefix = "form") => {
        let model;
        if (!_.isEmpty(model_name)) {
            try {
                model = this.sequelize.model(model_name);
            } catch {
                try {
                    model = this.sequelize.model(prefix + model_name);
                } catch {
                    try {
                        const def = await this.queryInterface.describeTable(
                            (prefix ? prefix + "_" : "") + model_name
                        );
                        delete def.id;
                        for (const [key, value] of Object.entries(def)) {
                            value.field = key;
                            value.fieldName = key;
                        }
                        model = await this.sequelize.define(
                            (prefix ? prefix + "_" : "") + model_name,
                            def,
                            {
                                tableName:
                                    (prefix ? prefix + "_" : "") + model_name,
                                createdAt: "created_on",
                                updatedAt: "updated_on",
                                underscored: true,
                            }
                        );
                    } catch {
                        model = await this.sequelize.define(
                            (prefix ? prefix + "_" : "") + model_name,
                            {
                                id: {
                                    type: Sequelize.INTEGER,
                                    primaryKey: true,
                                    autoIncrement: true,
                                    _autoGenerated: true,
                                },
                            },
                            {
                                tableName:
                                    (prefix ? prefix + "_" : "") + model_name,
                                createdAt: "created_on",
                                updatedAt: "updated_on",
                                underscored: true,
                                timestamps: false,
                            }
                        );
                        await model.sync({ force: false, alter: true });
                    }
                }
            }
            return model;
        } else {
            throw { error: "Model name can not be empty" };
        }
    };

    _tableExists = async (tableName) => {
        const query = `SELECT to_regclass('${tableName}')`;
        const result = await this.db.env.ro.query(query);
        if (result.length > 0) return result[0]?.to_regclass !== null;
    };

    _describeTable = async (tableName) => {
        const query = `
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = '${tableName}'
        `;
        const result = await this.db.env.ro.query(query);
        const def = {};
        result.forEach((row) => {
            let type = row.data_type;
            const charTypes = [
                "character varying",
                "varchar",
                "character",
                "char",
                "text",
            ];
            const decimalTypes = [
                "double precision",
                "numeric",
                "decimal",
                "real",
            ];
            const intTypes = [
                "smallint",
                "integer",
                "bigint",
                "smallserial",
                "serial",
                "bigserial",
            ];

            if (charTypes.includes(type.toLowerCase())) {
                type = "text";
            } else if (decimalTypes.includes(type.toLowerCase())) {
                type = "decimal";
            } else if (intTypes.includes(type.toLowerCase())) {
                type = "int";
            }
            def[row.column_name] = {
                type: type,
                field: row.column_name,
                fieldName: row.column_name,
                allowNull:
                    row.is_nullable?.toLowerCase() == "yes"
                        ? true
                        : row.is_nullable?.toLowerCase() == "no"
                          ? false
                          : null,
            };
        });
        return def;
    };
    handle_subforms = async (form_name, parent_form_id, data, nes, ctx) => {
        const cson = this.shared.DSL[form_name];
        if (_.isEmpty(cson)) {
            return;
        }
        const fields = cson.fields;

        for (const [key, value] of Object.entries(fields)) {
            let fids = [];
            //check if source and multi true, create gerund table entry
            if (
                !_.isEmpty(value.model.source) &&
                typeof value.model.source == "string" &&
                value.model.multi &&
                value.model.type != "subform"
            ) {
                let source_form = value.model.source;
                if (source_form.indexOf("{") != -1) {
                    const cols = utils._get_column_names(source_form);
                    cols.forEach((col) => {
                        if (data[col])
                            source_form = source_form.replace(
                                "{" + col + "}",
                                data[col].toLowerCase()
                            );
                    });
                }
                try {
                    const gerund_name = utils.getGerundTableName(
                        form_name,
                        key,
                        source_form
                    );
                    if (data[key] != undefined && data[key].length > 0) {
                        let y = data[key];

                        // gerund link table entries
                        const gerund_data = [];
                        const options = [];
                        if (!_.isArray(y)) {
                            y = [y];
                        }
                        y.forEach((v) => {
                            if (v == undefined) {
                                return;
                            }
                            gerund_data.push({
                                ["form_" + form_name + "_fk"]: parent_form_id,
                                ["form_" + source_form + "_fk"]: v,
                            });
                            // add keys to options to see if entry already exists
                            options.push({
                                ["form_" + form_name + "_fk"]: parent_form_id,
                                ["form_" + source_form + "_fk"]: v,
                            });
                        });
                        // get existing entries and delete them. add new gerund entries on form update
                        const _gr_records = await this.form_delete(
                            gerund_name,
                            {
                                ["form_" + form_name + "_fk"]: parent_form_id,
                            },
                            ""
                        );
                        const _gr_result = await this.subform_gerund_entries(
                            gerund_name,
                            gerund_data,
                            options
                        );
                    }
                } catch (e) {
                    console.error(e);
                }
            }

            //check if source table is used as subform, create subform table entry
            else if (
                !_.isEmpty(value.model.source) &&
                typeof value.model.source == "string" &&
                value.model.type == "subform"
            ) {
                let source_form = value.model.source;
                if (source_form.indexOf("{") != -1) {
                    const cols = utils._get_column_names(source_form);
                    cols.forEach((col) => {
                        if (data[col]) {
                            source_form = source_form.replace(
                                "{" + col + "}",
                                data[col].toLowerCase()
                            );
                        }
                    });
                }
                if (
                    !_.isEmpty(data["_meta"]) &&
                    !_.isEmpty(data["_meta"][key])
                ) {
                    Object.keys(data["_meta"][key]).forEach((k) => {
                        try {
                            if (k == "delete") {
                                fids = data["_meta"][key][k];
                            }
                        } catch (e) {
                            console.error(e);
                        }
                    });
                }
                try {
                    const subform_name = utils.getSubformTableName(
                        form_name,
                        source_form
                    );
                    if (data[key] != undefined && data[key].length > 0) {
                        const subforms_array = [];
                        // handle subforms and gerund tables entries in subform
                        const source_cson = await this.shared.DSL[source_form];
                        data[key].forEach((element) => {
                            subforms_array.push(
                                utils._handle_fields(
                                    source_form,
                                    source_cson,
                                    element,
                                    this
                                )
                            );
                        });
                        data[key] = await Promise.all(subforms_array);
                        const y = await this.create_or_update(
                            source_form,
                            data[key],
                            fids,
                            nes,
                            ctx
                        );
                        for (let v of y) {
                            v["_meta"] = data["_meta"];
                            v = await this.handle_subforms(
                                source_form,
                                v.id,
                                v,
                                nes,
                                ctx
                            );
                        }

                        // subform link table entries
                        const subform_data = [];
                        const options = [];
                        y.forEach((v) => {
                            subform_data.push({
                                ["form_" + form_name + "_fk"]: parent_form_id,
                                ["form_" + source_form + "_fk"]: v.id,
                                archive: v.archived ? v.archived : false,
                                delete: false,
                            });
                            // add keys to options to see if entry already exists
                            options.push({
                                ["form_" + form_name + "_fk"]: parent_form_id,
                                ["form_" + source_form + "_fk"]: v.id,
                            });
                        });
                        data[key] = y;
                        const _sf_result = await this.subform_gerund_entries(
                            subform_name,
                            subform_data,
                            options
                        );
                    }
                } catch (e) {
                    console.error(e);
                }
            }
        }
        return data;
    };

    create_or_update = async (
        table,
        values = [],
        fids = [],
        nes,
        ctx,
        archived = null
    ) => {
        const data = [];
        try {
            const cson = this.shared.DSL[table];
            fids = [...new Set(fids)];
            if (archived == true) {
                const value = {
                    archived: true,
                };
                for (let i = 0; i < fids.length; i++) {
                    try {
                        await this.form_update(
                            table,
                            value,
                            {
                                id: fids[i],
                            },
                            undefined,
                            nes,
                            ctx
                        );
                    } catch (e) {
                        console.error(e);
                    }
                }
                return data;
            } else if (archived == false) {
                const value = {
                    archived: false,
                };
                for (let i = 0; i < fids.length; i++) {
                    try {
                        await this.form_update(
                            table,
                            value,
                            {
                                id: fids[i],
                            },
                            undefined,
                            nes,
                            ctx
                        );
                    } catch (e) {
                        console.error(e);
                    }
                }
                return data;
            }
            for (let index = 0; index < values.length; index++) {
                archived = false;
                let value = values[index];
                value = value["_meta"]
                    ? {
                          ...value,
                          ...value["_meta"]["client"],
                      }
                    : value;

                // handle fields
                value = await utils._handle_fields(table, cson, value, this);

                //generate auto_name
                this.form_auto_name(cson, value, table);
                if (value.id == undefined) {
                    try {
                        const res = await this.form_create(table, value);
                        data.push({
                            ...value,
                            ...res.dataValues,
                        });
                    } catch (e) {
                        console.error(e);
                    }
                } else {
                    const id = value.id;
                    try {
                        if (fids.includes(id)) {
                            console.log(
                                "Deleting form id: " +
                                    id +
                                    " from form : " +
                                    table
                            );
                            value.archived = true;
                            archived = true;
                        }
                    } catch (e) {
                        console.error(e);
                    }
                    delete value.id;
                    try {
                        const res = await this.form_update(
                            table,
                            value,
                            {
                                id: id,
                            },
                            undefined,
                            nes,
                            ctx
                        );
                        data.push({
                            ...value,
                            ...res.dataValues,
                        });
                    } catch (e) {
                        console.error(e);
                    }
                }
            }

            // data = await Promise.all(async);
            return data;
        } catch (e) {
            console.error(e);
        }
    };

    _is_float(str) {
        if (str.includes(":")) {
            return {
                [str.split(":")[0]]: parseInt(str.split(":")[1].substring(1)),
            };
        }
        return null;
    }

    form_auto_name = (fields, data, table) => {
        const auto_name_field = fields.model.name;
        try {
            if (auto_name_field.length > 0) {
                if (Array.isArray(auto_name_field)) {
                    //add all elements of array to create auto_name
                    let auto_name = "";
                    auto_name_field.forEach((col) => {
                        if (data[col] != "") {
                            auto_name += data[col] + " ";
                        }
                    });
                    auto_name = auto_name.trim();
                    data["auto_name"] = auto_name;
                } else {
                    // parse string to see if it follows a pattern or is it a name of column
                    if (auto_name_field.includes("{")) {
                        let auto_name = auto_name_field;
                        const cols = utils._get_column_names(auto_name_field);
                        cols.forEach((col) => {
                            if (col.includes(":")) {
                                for (const [k, v] of Object.entries(
                                    this._is_float(col)
                                )) {
                                    let val;
                                    if (data[k] != "") {
                                        val = parseFloat(data[k]).toFixed(v);
                                    } else {
                                        val = "0.00";
                                    }
                                    auto_name = auto_name.replace(
                                        "{" + col + "}",
                                        _.isEmpty(val) ? "" : val
                                    );
                                }
                            } else {
                                auto_name = auto_name.replace(
                                    "{" + col + "}",
                                    _.isEmpty(data[col]) ? "" : data[col]
                                );
                            }
                        });
                        auto_name = utils._remove_empty_brackets(auto_name);
                        data["auto_name"] = auto_name;
                    } else {
                        data["auto_name"] = data[auto_name_field];
                    }
                }
            } else if (fields.view.name) {
                data["auto_name"] = data.name;
            } else if (fields.view.code) {
                data["auto_name"] = data.code;
            } else {
                data["auto_name"] = "";
            }
            console.log("Generated auto_name", data["auto_name"]);
        } catch (e) {
            console.error(
                `Error encountered generating auto_name for table ${table}`,
                e
            );
        }
    };

    _add_auto_names = async (form_name, data) => {
        const cson = this.shared.DSL[form_name];
        if (_.isEmpty(cson)) {
            return;
        }
        for (const [k, v] of Object.entries(cson.fields)) {
            if (
                !_.isEmpty(v.model.source) &&
                typeof v.model.source == "string" &&
                data[k] != null
            ) {
                let source_form = v.model.source;
                if (source_form.indexOf("{") != -1) {
                    const cols = utils._get_column_names(source_form);
                    cols.forEach((col) => {
                        if (data[col]) {
                            source_form = source_form.replace(
                                "{" + col + "}",
                                data[col].toLowerCase()
                            );
                        }
                    });
                }
                for (let index = 0; index < data[k].length; index++) {
                    const element = data[k][index];
                    if (_.isEmpty(element)) {
                        continue;
                    }
                    delete element._meta;
                    try {
                        const formdata = (
                            await this.form_fetch(source_form, {
                                id: element.id,
                            })
                        )[0];
                        data[k + "_auto_name"] = formdata.auto_name;
                    } catch {
                        try {
                            const formdata = (
                                await this.form_fetch(source_form, {
                                    [v.model.sourceid]: element,
                                })
                            )[0];
                            data[k + "_auto_name"] = formdata.auto_name;
                        } catch {
                            try {
                                const formdata = (
                                    await this.form_fetch(source_form, {
                                        [v.model.sourceid]: data[k],
                                    })
                                )[0];
                                data[k + "_auto_name"] = formdata.auto_name;
                            } catch (e) {
                                console.log(
                                    "Error while generating auto_name for key:",
                                    k
                                );
                                throw e;
                            }
                        }
                    }
                }
            }
        }
    };

    handle_subforms_archive = async (
        form_name,
        parent_form_id,
        data,
        cv,
        ctx,
        archived,
        nes
    ) => {
        const cson = this.shared.DSL[form_name];
        if (_.isEmpty(cson)) {
            return;
        }
        const fields = cson.fields;
        for (const [key, value] of Object.entries(fields)) {
            const fids = [];

            //check if source table is used as subform, create subform table entry
            // else
            if (
                !_.isEmpty(value.model.source) &&
                typeof value.model.source == "string" &&
                value.model.type == "subform"
            ) {
                let source_form = value.model.source;
                if (source_form.indexOf("{") != -1) {
                    const cols = utils._get_column_names(source_form);
                    cols.forEach((col) => {
                        if (cv[col])
                            source_form = source_form.replace(
                                "{" + col + "}",
                                cv[col].toLowerCase()
                            );
                        else source_form = null;
                    });
                }
                if (source_form == null) continue;
                const subform_name = utils.getSubformTableName(
                    form_name,
                    source_form
                );
                const sf_records = await this.form_fetch(
                    subform_name,
                    {
                        ["form_" + form_name + "_fk"]: parent_form_id,
                    },
                    ""
                );
                if (!_.isEmpty(sf_records)) {
                    sf_records.forEach((res) => {
                        fids.push(res["form_" + source_form + "_fk"]);
                    });
                }
                try {
                    const _subform_name = utils.getSubformTableName(
                        form_name,
                        source_form
                    );
                    await this.create_or_update(
                        source_form,
                        data[key],
                        fids,
                        nes,
                        ctx,
                        archived
                    );
                } catch (e) {
                    console.error(e);
                }
            }
        }
        return data;
    };

    addLogTable(logArray, tableName, syncMode) {
        if (syncMode !== "full") {
            logArray.push(tableName);
        }
        return logArray;
    }

    async getOldIndexes(tableName) {
        let oldIndexes = [];
        try {
            oldIndexes = await this.db.env.rw.query(`select
                t.relname as table_name,
                i.relname as index_name,
                array_to_string(array_agg(a.attname), ', ') as column_names
            from
                pg_class t,
                pg_class i,
                pg_index ix,
                pg_attribute a
            where
                t.oid = ix.indrelid
                and i.oid = ix.indexrelid
                and a.attrelid = t.oid
                and a.attnum = ANY(ix.indkey)
                and t.relkind = 'r'
                and t.relname ='${tableName}'
            group by
                t.relname,
                i.relname
            order by
                t.relname,
                i.relname`);
            oldIndexes = oldIndexes.map((o) => {
                return {
                    index: o.index_name,
                    cols: o.column_names.replaceAll(" ", "").split(",").sort(),
                };
            });
        } catch (error) {
            console.log(error);
        }
        return oldIndexes;
    }

    async getFK(tableName) {
        let oldFKs = [];
        try {
            oldFKs = await this.db.env.rw.query(`
            SELECT
                tc.constraint_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema='public'
                AND tc.table_name='${tableName}';`);
        } catch (error) {
            console.log(error);
        }
        return oldFKs;
    }
    /**
     * @description Create gerund tables, subform tables, update model and sync with db to update tables in postgres
     * @param {*} model_name
     * @param {*} model new model
     * @param {*} prefix  pass null and subform explicitly to determine correct model
     *
     * @returns boolean (true if schema update successful and vice versa)
     */
    __set_model = async (
        model_name,
        model,
        references,
        prefix = "form_",
        updateObj = {},
        force = false
    ) => {
        //'text','date', 'datetime', 'decimal', 'image', 'int', 'json', 'xml', 'password', 'time', 'color'
        const alterTypes = {
            text: "text",
            date: "date",
            datetime: "timestamp",
            time: "text",
            decimal: "double precision",
            image: "text",
            int: "int",
            json: "text", // change this to jsonb after demo 8th july
            xml: "text",
            password: "text",
            color: "text",
        };
        const vFunExists = updateObj.vFunExists;
        const formMode = updateObj.formMode;
        const syncMode = updateObj.syncMode;
        const vectorSearchField = updateObj.vectorSearchField;

        const table_name = (prefix ? prefix : "") + model_name;
        let logTables = [];
        logTables = this.addLogTable(logTables, model_name, syncMode);
        //get existing indexes on table other than primary
        const oldIndexes = await this.getOldIndexes(table_name);
        const oldFKs = await this.getFK(table_name);
        //get old model, in case new model creation has some errors. Restore changes
        model.id.type = Sequelize.INTEGER;
        let table_def = await this._describeTable(table_name);
        if (Object.keys(table_def).length < 1) {
            await this.__get_model(table_name, "");
            table_def = await this._describeTable(table_name);
        }

        //set foreign key references
        try {
            for (let i = 0; i < references.source_list.length; i++) {
                const src = references.source_list[i];
                for (const [k, v] of Object.entries(src)) {
                    try {
                        const oldFK = oldFKs.filter(
                            (fk) => fk.column_name === v.columnModel.name
                        )[0];
                        let dropFK = "";
                        const createFK = `ALTER TABLE ${table_name} ADD CONSTRAINT ${table_name}_${v.columnModel.name}_fk FOREIGN KEY (${v.columnModel.name}) REFERENCES form_${k} (${v.targetKey});`;
                        if (oldFK)
                            dropFK = `ALTER TABLE ${table_name} DROP CONSTRAINT IF EXISTS ${oldFK.constraint_name}`;
                        if (_.isEmpty(table_def[v.columnModel.name])) {
                            await this._add_column_to_tables(
                                table_name,
                                v.columnModel.name,
                                v.columnModel.type
                            );
                        } else if (
                            (table_def[v.columnModel.name].type === "text" &&
                                v.columnModel.type !== Sequelize.STRING) ||
                            (table_def[v.columnModel.name].type === "int" &&
                                v.columnModel.type !== Sequelize.INTEGER) ||
                            (table_def[v.columnModel.name].type === "text" &&
                                v.columnModel.type !== Sequelize.TEXT)
                        ) {
                            if (oldFK) {
                                console.warn(
                                    "DROPPING FK: " + oldFK.constraint_name
                                );
                                await this.db.env.rw.query(dropFK);
                            }
                            const type = alterTypes[v.columnModel.csonType];
                            if (
                                type &&
                                table_def[v.columnModel.name].type !== type
                            ) {
                                await this._alter_column_of_tables(
                                    table_name,
                                    v.columnModel.name,
                                    type
                                );
                            }
                        }
                        if (v.columnModel.createFK) {
                            // if source is removed from field
                            console.log(
                                `Adding FK: ${table_name}_${v.columnModel.name}_fk on target table form_${k} and target key ${v.targetKey}`
                            );
                            if (!oldFK) {
                                await this.db.env.rw.query(createFK);
                            } else if (
                                oldFK &&
                                (oldFK.foreign_column_name !== v.targetKey ||
                                    oldFK.foreign_table_name !== `form_${k}`)
                            ) {
                                await this.db.env.rw.query(dropFK);
                                await this.db.env.rw.query(createFK);
                            }
                        } else if (oldFK) {
                            await this.db.env.rw.query(dropFK);
                        }
                    } catch (error) {
                        if (error.error?.code == "42804") {
                            console.warn(error.sql);
                            throw Error(error.error.detail);
                        } else {
                            throw Error(error.message);
                        }
                    }
                }
            }
        } catch (e) {
            console.log("Drop table form_" + model_name + " and deploy again");
            throw e;
        }

        //create gerund tables
        try {
            for (let i = 0; i < references.gerund_tables_list.length; i++) {
                const src = references.gerund_tables_list[i];
                for (const [k, vObj] of Object.entries(src)) {
                    const v = vObj.source;
                    const sourceId = vObj.sourceid ? vObj.sourceid : null;
                    const gerund_name =
                        "gr_" + table_name + "_" + k + "_to_" + v + "_id";
                    if (gerund_name.length > 63) {
                        throw (
                            "Gerund Table name : " +
                            gerund_name +
                            " exceeds 63 characters. Rename field " +
                            k +
                            " or form : " +
                            v +
                            " to make sure Table name is less than 63 characters."
                        );
                    }
                    let gerund_model;
                    let def;
                    try {
                        await this.queryInterface.describeTable(gerund_name);
                        if (force)
                            logTables = this.addLogTable(
                                logTables,
                                gerund_name,
                                syncMode
                            );
                    } catch {
                        console.log(
                            gerund_name + " doesn't exists, creating new table"
                        );
                        def = {
                            id: {
                                type: Sequelize.INTEGER,
                                primaryKey: true,
                                autoIncrement: true,
                                _autoGenerated: true,
                            },
                            [`${table_name}_fk`]: {
                                type: Sequelize.INTEGER,
                            },
                            [`form_${v}_fk`]: {
                                type:
                                    sourceId === "id"
                                        ? Sequelize.INTEGER
                                        : Sequelize.STRING,
                            },
                        };
                        gerund_model = await this.sequelize.define(
                            gerund_name,
                            def,
                            {
                                tableName: gerund_name,
                                timestamps: false,
                                schema: "public",
                            }
                        );
                        await gerund_model.sync({
                            force: false,
                            alter: true,
                        });
                        // push gerund to create log table for gerund
                        logTables = this.addLogTable(
                            logTables,
                            gerund_name,
                            syncMode
                        );
                        if (
                            !oldIndexes.includes(
                                `${gerund_name}_${table_name}_fkey`
                            )
                        ) {
                            try {
                                await this.queryInterface.addConstraint(
                                    gerund_name,
                                    {
                                        fields: [`${table_name}_fk`],
                                        type: "foreign key",
                                        references: {
                                            //Required field
                                            table: `${table_name}`,
                                            field: "id",
                                        },
                                    }
                                );
                            } catch {
                                console.log(
                                    `Foreign Key Constraint already exists`
                                );
                            }
                        }
                        // Check why this did not run
                        if (
                            !oldIndexes.includes(
                                `${gerund_name}_form_${v}_fkey`
                            )
                        ) {
                            try {
                                if (!sourceId) {
                                    await this.queryInterface.addConstraint(
                                        gerund_name,
                                        {
                                            fields: [`form_${v}_fk`],
                                            type: "foreign key",
                                            references: {
                                                //Required field
                                                table: `form_${v}`,
                                                field: "id",
                                            },
                                        }
                                    );
                                }
                            } catch {
                                console.log(
                                    `Foreign Key Constraint already exists`
                                );
                            }
                        }
                    }
                }
            }
        } catch (e) {
            console.error("unable to create gerund table");
            console.log("Table creation failed for: " + table_name);
            throw e;
        }

        //create subform tables
        try {
            for (let i = 0; i < references.subform_tables_list.length; i++) {
                const src = references.subform_tables_list[i];
                for (const [_, v] of Object.entries(src)) {
                    const subform_name = "sf_" + table_name + "_to_" + v;
                    if (subform_name.length > 63) {
                        throw (
                            "Subform Table name : " +
                            subform_name +
                            " exceeds 63 characters. Rename form : " +
                            v +
                            " to make sure Table name is less than 63 characters."
                        );
                    }
                    let gerund_model;
                    let def;
                    try {
                        await this.queryInterface.describeTable(subform_name);
                        // push subform_name to create log table for gerund and linked sf table
                        if (force) {
                            logTables = this.addLogTable(
                                logTables,
                                subform_name,
                                syncMode
                            );
                            logTables = this.addLogTable(
                                logTables,
                                v,
                                syncMode
                            );
                        }
                    } catch {
                        console.log(
                            subform_name + " doesn't exists, creating new table"
                        );
                        def = {
                            id: {
                                type: Sequelize.INTEGER,
                                primaryKey: true,
                                autoIncrement: true,
                                _autoGenerated: true,
                            },
                            delete: {
                                type: Sequelize.BOOLEAN,
                                allowNull: true,
                            },
                            archive: {
                                type: Sequelize.BOOLEAN,
                                allowNull: true,
                            },
                            [`${table_name}_fk`]: {
                                type: Sequelize.INTEGER,
                            },
                            [`form_${v}_fk`]: {
                                type: Sequelize.INTEGER,
                            },
                        };
                        gerund_model = await this.sequelize.define(
                            subform_name,
                            def,
                            {
                                tableName: subform_name,
                                timestamps: false,
                                schema: "public",
                            }
                        );
                        await gerund_model.sync({
                            force: false,
                            alter: true,
                        });

                        // push sf to create log table for gerund
                        logTables = this.addLogTable(
                            logTables,
                            subform_name,
                            syncMode
                        );
                        logTables = this.addLogTable(logTables, v, syncMode);

                        if (
                            !oldIndexes.includes(
                                `${subform_name}_${table_name}_fkey`
                            )
                        ) {
                            try {
                                await this.queryInterface.addConstraint(
                                    subform_name,
                                    {
                                        fields: [`${table_name}_fk`],
                                        type: "foreign key",
                                        references: {
                                            //Required field
                                            table: table_name,
                                            field: "id",
                                        },
                                    }
                                );
                            } catch {
                                console.log(
                                    `Foreign Key Constraint already exists`
                                );
                            }
                        }

                        if (
                            !oldIndexes.includes(
                                `${subform_name}_form_${v}_fkey`
                            )
                        ) {
                            try {
                                await this.queryInterface.addConstraint(
                                    subform_name,
                                    {
                                        fields: [`${v}_fk`],
                                        type: "foreign key",
                                        references: {
                                            //Required field
                                            table: `form_${v}`,
                                            field: "id",
                                        },
                                    }
                                );
                            } catch {
                                console.log(
                                    `Foreign Key Constraint already exists`
                                );
                            }
                        }

                        if (!oldIndexes.includes(`u_${subform_name}`)) {
                            try {
                                console.log(
                                    `Adding unique constraint for ${subform_name} and ${v} field form_${v}_fk ref table form_${v}`
                                );
                                await this.queryInterface.sequelize.query(`
                                    ALTER TABLE ${subform_name}
                                    ADD CONSTRAINT u_${subform_name} 
                                    UNIQUE (form_${v}_fk);`);
                            } catch (error) {
                                console.log(Object.keys(error));
                                console.error(error);
                            }
                        }
                    }
                }
            }
        } catch (e) {
            console.log(e);
            console.error("unable to create subform table");
        }

        //sync changes to db
        console.log(
            "Updating Schema for table: " +
                table_name +
                " against model: " +
                model_name
        );

        let cson = {};
        try {
            cson = this.shared.DSL[table_name.substr(5)];
        } catch {
            cson = {};
        }
        table_def = await this._describeTable(table_name);
        for (const [k, v] of Object.entries(model)) {
            // this is the reason to scrap the whole flow and rewrite it properly
            if (
                !_.isEmpty(table_def[k]) &&
                !_.isEmpty(cson) &&
                cson.fields &&
                (cson.fields[k]?.model?.type != v.csonType ||
                    (table_def[k].type === "text" &&
                        v.csonType !== "text" &&
                        v.csonType !== "json") ||
                    (table_def[k].type === "int" && v.csonType !== "int"))
            ) {
                try {
                    const type = alterTypes[v.csonType];
                    if (type && table_def[k].type !== type) {
                        await this._alter_column_of_tables(table_name, k, type);
                        console.log(
                            `Successfully changed column type for ${k} to ${type}`
                        );
                    }
                } catch (e) {
                    if (e.error?.code == "42804") {
                        console.warn(e.sql);
                        throw Error(e.error.detail);
                    } else {
                        throw Error(e.message);
                    }
                }
            } else if (
                _.isEmpty(table_def[k]) ||
                _.isEmpty(cson) ||
                _.isEmpty(cson.fields) ||
                _.isEmpty(cson?.fields[k])
            ) {
                try {
                    await this._add_column_to_tables(table_name, k, v.type);
                } catch (e) {
                    console.error(
                        `Unable to add column ${k} to table ${table_name}`
                    );
                    throw Error(e);
                }
            }
        }
        // update sync_mode
        console.log("Updating sync mode");
        await this.update_sync_mode(formMode, table_name, syncMode);

        //add indexes
        for (let i = 0; i < references.indexes.length; i++) {
            const index = references.indexes[i];
            try {
                const ind = oldIndexes.find((o) => {
                    return this.fx.arraysEqual(o.cols, index.fields);
                });
                if (ind !== undefined) continue;
                console.log(
                    `adding index for: ${index.name} for fields: ${index.fields.join(",")}`
                );
                await this.queryInterface.addIndex(
                    table_name,
                    index.fields,
                    {
                        fields: index.fields,
                        name: index.name,
                        unique: index.unique,
                    },
                    table_name
                );
            } catch (e) {
                if (e.original?.code == "42P07") {
                    console.log(
                        "Index " +
                            index.name +
                            " already exists in the list not adding error"
                    );
                } else {
                    console.error(e.original);
                }
            }
        }

        //remove indexes
        // for (let i = 0; i < oldIndexes.length; i++) {
        //     const oldIndex = oldIndexes[i];
        //     try {
        //         const isPresent = references.indexes.some(
        //             (newIndex) => newIndex.name === oldIndex.index
        //         );
        //         if (!isPresent) {
        //             console.log(`dropping index "${oldIndex.index}"`);
        //             await this.queryInterface.sequelize.query(
        //                 `DROP INDEX "${oldIndex.index}"`
        //             );
        //         }
        //     } catch (e) {
        //         console.log("Error dropping index removing constraint");
        //         // use error.code if sequlize provides in error message to properly handle catch block
        //         try {
        //             const csql = `ALTER TABLE ${table_name} DROP CONSTRAINT ${oldIndex.index}`;
        //             await this.queryInterface.sequelize.query(csql);
        //         } catch (error) {
        //             console.log(
        //                 `Error while removing index ${oldIndex.index} from table ${table_name} - ${error.message}`
        //             );
        //         }
        //         console.log(
        //             `Error while removing index ${oldIndex.index} from table ${table_name} - ${e.message}`
        //         );
        //     }
        // }

        // enable logging:
        if (vFunExists) {
            const promises = [];
            for (const form of logTables) {
                promises.push(
                    this.add_log_tables(
                        form,
                        form.startsWith("sf_") || form.startsWith("gr_")
                            ? ""
                            : "form_"
                    )
                );
            }
            await Promise.all(promises);
        }
        // handle tsvector
        console.log(
            `[CRUD SetModel] About to call add_tsvector_column with: ${JSON.stringify(vectorSearchField, null, 2)}`
        );
        await this.add_tsvector_column(vectorSearchField);
        console.log(
            "Schema Update Successful for table: " +
                table_name +
                " against model: " +
                table_name
        );
        return true;
    };

    _alter_column_type = async (table_name, column_name, type) => {
        const sql = `ALTER TABLE IF EXISTS ${table_name} ALTER COLUMN ${column_name} type ${type} using ${column_name}::${type}`;

        console.log(
            "Changing column type of column: " +
                column_name +
                " Table name: " +
                table_name
        );
        await this.db.env.rw.query(sql);
    };

    _add_column = async (table_name, column_name, type) => {
        const sql = `ALTER TABLE IF EXISTS ${table_name} ADD COLUMN IF NOT EXISTS ${column_name} ${type}`;

        console.log("Adding column: ", column_name);
        await this.db.env.rw.query(sql);
    };
    _add_column_to_tables = async (table_name, column_name, type) => {
        const log_table = "log_" + table_name.substr(5);
        await this._add_column(table_name, column_name, type);
        await this._add_column(log_table, column_name, type);
    };
    _alter_column_of_tables = async (table_name, column_name, type) => {
        const log_table = "log_" + table_name.substr(5);
        await this._alter_column_type(table_name, column_name, type);
        try {
            await this._alter_column_type(log_table, column_name, type);
        } catch (e) {
            // TODO: Slack Notify
            if (e.error?.code == "42703") {
                console.warn(
                    ` Column ${column_name}::${type} not exist in log table ${log_table}. For now adding new one but this should not happen`
                );
                await this._add_column(log_table, column_name, type);
            } else {
                console.log(" Error while changing log table column type ", e);
            }
        }
    };
    // dont create for sync mode full
    async add_log_tables(model_name, prefix = "") {
        console.log("adding log table for: " + model_name);
        let pLog = "log_";
        if (prefix === "") pLog = "l";
        // INCLUDING indexes
        const trigger = `
            ALTER TABLE IF EXISTS ${prefix}${model_name} ADD COLUMN IF NOT EXISTS sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, NULL);
            CREATE TABLE IF NOT EXISTS ${pLog}${model_name} (LIKE ${prefix}${model_name});
            CREATE OR REPLACE TRIGGER crx_versioning_trigger AFTER
            INSERT
            OR
            UPDATE
            OR
            DELETE
            ON ${prefix}${model_name} FOR EACH ROW EXECUTE PROCEDURE crx_versioning('sys_period', '${pLog}${model_name}', TRUE);
            CREATE OR REPLACE TRIGGER crx_pre_upesert_trigger BEFORE
            INSERT
            OR
            UPDATE
            OR
            DELETE
            ON ${prefix}${model_name} FOR EACH ROW EXECUTE PROCEDURE crx_pre_upsert_fun('sys_period','${pLog}${model_name}');`;
        try {
            await this.sequelize.query(trigger);
        } catch (error) {
            console.log(`ERROR OCCURED FOR MODEL NAME ${model_name}`);
            console.log(trigger);
            throw Error(error);
        }
    }
    async add_tsvector_column(vectorSearchField) {
        let sql = "";
        console.log(
            `[add_tsvector_column] Received vectorSearchField: ${JSON.stringify(vectorSearchField, null, 2)}`
        );
        if (!_.isEmpty(vectorSearchField) && vectorSearchField.op) {
            switch (vectorSearchField.op) {
                case "addColumn":
                    sql = `BEGIN;
                    ALTER TABLE ${vectorSearchField.table} ADD COLUMN IF NOT EXISTS ${vectorSearchField.field} ${vectorSearchField.type};
                    CREATE INDEX IF NOT EXISTS idx_${vectorSearchField.table}_search ON ${vectorSearchField.table} USING GIN(${vectorSearchField.field});
                    COMMIT;`;
                    break;
                case "removeColumn":
                    sql = `ALTER TABLE ${vectorSearchField.table} DROP COLUMN IF EXISTS ${vectorSearchField.field}`;
                    break;
                case "changeColumn":
                    sql = `BEGIN;
                    DROP INDEX IF EXISTS idx_${vectorSearchField.table}_search;
                    ALTER TABLE ${vectorSearchField.table} DROP COLUMN IF EXISTS ${vectorSearchField.field};
                    ALTER TABLE ${vectorSearchField.table} ADD COLUMN IF NOT EXISTS ${vectorSearchField.field} ${vectorSearchField.type};
                    CREATE INDEX IF NOT EXISTS idx_${vectorSearchField.table}_search ON ${vectorSearchField.table} USING GIN(${vectorSearchField.field});
                    COMMIT;`;
                    break;
                default:
                    console.log(
                        `[add_tsvector_column] No valid 'op' found in vectorSearchField. Operation: ${vectorSearchField.op}`
                    );
                    break;
            }
            if (sql) {
                console.log(`[add_tsvector_column] Executing SQL:\n${sql}`);
                try {
                    await this.db.env.rw.query(sql);
                    console.log(
                        `[add_tsvector_column] SQL executed successfully for table ${vectorSearchField.table}.`
                    );
                } catch (error) {
                    console.error(
                        `[add_tsvector_column] Error executing SQL for table ${vectorSearchField.table}:`,
                        error
                    );
                    console.error(
                        `[add_tsvector_column] Failed SQL was:\n${sql}`
                    );
                }
            }
        } else {
            console.log(
                `[add_tsvector_column] Skipping TSVector operation: vectorSearchField is empty or has no 'op'. Op: ${vectorSearchField?.op}`
            );
        }
    }
    /**
     * Returns cson against form_name e.g patient, user, patient_case
     */
    get_cson = async (tablename) => {
        let cson = {};
        try {
            cson = this.shared.DSL[tablename];
            return cson;
        } catch (e) {
            console.error(e);
            return {};
        }
    };

    /**
     * @description Deletes table from db
     * @param table {string} Name of Table
     * @param options {Object} JSON object containing where clause as key value pair.
     *
     * Example:
     * * id = 50 => { id: 50 }.
     * * id = 50 AND username = 'abc' => { id: 50, username:'abc' }.
     * * status = 'active' AND (id = 50 OR username = 'abc') =>{ status: 'active', [Op.or]: [{id: 50}, {username: 'abc'}] }.
     *
     * All supported operations are documented in 'findAll - Search for multiple elements in the database' section.
     * https://sequelize.org/master/manual/models-usage.html
     *
     * @param type {String} form types are => ['form', 'subform']
     */
    form_delete = async (table, options = {}, type = "form") => {
        const table_name = type + (type ? "_" : "") + table;
        let model;
        try {
            model = this.sequelize.model(table_name);
        } catch {
            const def = await this.queryInterface.describeTable(table_name);
            delete def.id;
            if (type != "form") {
                model = await this.sequelize.define(table_name, def, {
                    tableName: table_name,
                    timestamps: false,
                    underscored: true,
                });
            } else {
                model = await this.sequelize.define(table_name, def, {
                    tableName: table_name,
                    createdAt: "created_on",
                    updatedAt: "updated_on",
                    underscored: true,
                });
            }
        }
        const data = await model.destroy({
            where: options,
        });
        return data;
    };

    /**
     * @param table {string} Name of Table
     * @param where {Object} JSON object containing where clause as key value pair.
     * @param options {Object} JSON object containing other options.
     *
     * Example:
     * * id = 50 => { id: 50 }.
     * * id = 50 AND username = 'abc' => { id: 50, username:'abc' }.
     * * status = 'active' AND (id = 50 OR username = 'abc') =>{ status: 'active', [Op.or]: [{id: 50}, {username: 'abc'}] }.
     *
     * All supported operations are documented in 'findAll - Search for multiple elements in the database' section.
     * https://sequelize.org/master/manual/models-usage.html
     *
     * @param type {String} form types are => ['form', 'subform']
     */
    form_fetch = async (table, where = {}, type = "form", options = {}) => {
        let cson = {};
        try {
            cson = this.shared.DSL[table];
        } catch {
            cson = {};
        }
        const table_name = type + (type ? "_" : "") + table;
        let model;
        try {
            model = this.sequelize.model(table_name);
        } catch {
            const def = await this.queryInterface.describeTable(table_name);
            delete def.id;
            if (type != "form") {
                model = await this.sequelize.define(table_name, def, {
                    tableName: table_name,
                    timestamps: false,
                    underscored: true,
                });
            } else {
                model = await this.sequelize.define(table_name, def, {
                    tableName: table_name,
                    createdAt: "created_on",
                    updatedAt: "updated_on",
                    underscored: true,
                });
                console.log(model);
            }
        }
        const data = await model.findAll({
            where: where,
            ...options,
        });
        if (!_.isEmpty(cson)) {
            for (let i = 0; i < data.length; i++) {
                for (const [k, v] of Object.entries(cson.fields)) {
                    //check if source and multi true, read gerund table entry
                    if (
                        v.model.source != null &&
                        typeof v.model.source == "string" &&
                        v.model.multi &&
                        v.model.type != "subform"
                    ) {
                        const gerund_name =
                            "gr_" +
                            table_name +
                            "_" +
                            k +
                            "_to_" +
                            v.model.source +
                            "_id";
                        const subform_keys = await this.form_fetch(
                            gerund_name,
                            {
                                [table_name + "_fk"]: data[i].id,
                            },
                            ""
                        );
                        const source_keys = [];
                        for (let j = 0; j < subform_keys.length; j++) {
                            source_keys.push(
                                subform_keys[j][
                                    "form_" + v.model.source + "_fk"
                                ]
                            );
                        }
                        if (source_keys.length) {
                            const subform_data = await this.form_fetch(
                                "form_" + v.model.source,
                                {
                                    id: source_keys,
                                },
                                ""
                            );
                            data[i].dataValues[k] = subform_data;
                        } else {
                            data[i].dataValues[k] = [];
                        }
                    }
                    //check if source table is used as subform, read subform table entry
                    if (
                        v.model.source != null &&
                        typeof v.model.source == "string" &&
                        v.model.type == "subform"
                    ) {
                        let source_form = v.model.source;
                        if (source_form.indexOf("{") != -1) {
                            const cols = utils._get_column_names(source_form);
                            cols.forEach((col) => {
                                if (data[col]) {
                                    source_form = source_form.replace(
                                        "{" + col + "}",
                                        data[col].toLowerCase()
                                    );
                                }
                            });
                            if (source_form.indexOf("{") != -1) {
                                continue;
                            }
                        }
                        const subform_name =
                            "sf_" + table_name + "_to_" + source_form;
                        const subform_keys = await this.form_fetch(
                            subform_name,
                            {
                                [table_name + "_fk"]: data[i].id,
                            },
                            ""
                        );
                        const source_keys = [];
                        for (let j = 0; j < subform_keys.length; j++) {
                            source_keys.push(
                                subform_keys[j]["form_" + source_form + "_fk"]
                            );
                        }
                        if (source_keys.length) {
                            const subform_data = await this.form_fetch(
                                "form_" + source_form,
                                {
                                    id: source_keys,
                                },
                                ""
                            );
                            data[i].dataValues[k] = subform_data;
                        } else {
                            data[i].dataValues[k] = [];
                        }
                    }
                }
            }
        }
        await this._add_auto_names(table, data);
        return data;
    };

    /**
     * Updates a form in the database.
     *
     * @param {string} table - The name of the table to update.
     * @param {object} values - The new values to update in the form.
     * @param {object} options - Additional options for the update operation.
     * @param {string} type - The type of form being updated ('form', 'grid', etc.).
     * @param {object} nes - The NES object.
     * @param {object} ctx - The context object.
     * @returns {Promise<object>} The updated form data.
     */
    form_update = async (
        table,
        values = {},
        options = {},
        type = "form",
        nes,
        ctx
    ) => {
        let table_name;
        const isParent = !table.startsWith("gr_") && !table.startsWith("sf_");
        if (isParent) {
            table_name = type + (type ? "_" : "") + table;
        } else {
            table_name = table;
        }
        const model = await this.__get_model(table_name, null);
        const data = (
            await this.form_fetch(
                table,
                options,
                table == "dsl" ? "" : isParent && type ? "form" : "",
                {},
                true
            )
        )[0];
        if (type == "form") {
            await this.form_fetch(table, { id: options.id }, "form", {}, true);
        }
        values.updated_on = moment().utc()._d;
        await model.update(values, {
            where: options,
            returning: true,
        });
        let dv = {};
        if (values.archived == true || values.archived == false) {
            await this.handle_subforms_archive(
                table,
                options.id,
                values,
                data,
                ctx,
                true,
                nes
            );
        } else {
            dv = await this.handle_subforms(table, data.id, values, nes, ctx);
            if (!dv) {
                dv = data.dataValues;
            }
            if (dv.created_on) {
                const created_on_date = dv.created_on;
                dv.created_on = moment(dv.created_on).format(
                    "YYYY-MM-DD hh:mm:ss"
                );
                dv["created_on_date"] =
                    moment(created_on_date).format("YYYY/MM/DD");
            }
            if (dv.updated_on) {
                dv.updated_on = moment(dv.updated_on).format(
                    "YYYY/MM/DD hh:mm:ss"
                );
            }
            await this._add_auto_names(table, dv);
            dv.id = data.dataValues.id;
            data.dataValues = dv;
        }
        delete values._meta;
        if (!_.isEmpty(data["dataValues"])) {
            data.dataValues = {
                ...data.dataValues,
                ...values,
            };
        }
        return data;
    };

    /**
     * @param table {string} Name of Table.
     * @param values {Object} JSON object containing column name as key and new value as value.
     *
     * Example:
     * * id = 50 => { id: 50 }.
     * * id = 50 AND username = 'abc' => { id: 50, username:'abc' }.
     * * status = 'active' AND (id = 50 OR username = 'abc') =>{ status: 'active', [Op.or]: [{id: 50}, {username: 'abc'}] }.
     *
     * @param type: {string} form type => ['form','subform']
     */
    form_create = async (table, values = {}, type = "form", nes, ctx) => {
        let table_name;
        const isParent = !table.startsWith("gr_") && !table.startsWith("sf_");
        if (isParent) {
            table_name = type + (type ? "_" : "") + table;
        } else {
            table_name = table;
        }
        const model = await this.__get_model(table_name, null);
        if (!_.isEmpty(model.id)) {
            delete model.id;
        }
        let data = {};
        values.created_on = moment().utc()._d;
        try {
            data = await model.create(values, {
                whereisNewRecord: true,
            });
            let dv = await this.handle_subforms(
                table,
                data.dataValues.id,
                values,
                nes,
                ctx
            );
            if (!dv) {
                dv = data.dataValues;
            }
            if (dv.created_on) {
                const created_on_date = dv.created_on;
                dv.created_on = moment(dv.created_on).format(
                    "YYYY-MM-DD hh:mm:ss"
                );
                dv["created_on_date"] =
                    moment(created_on_date).format("YYYY/MM/DD");
            }
            if (dv.updated_on) {
                dv.updated_on = moment(dv.updated_on).format(
                    "YYYY/MM/DD hh:mm:ss"
                );
            }
            await this._add_auto_names(table, dv);
            dv.id = data.dataValues.id;
            data.dataValues = dv;
            return data;
        } catch (e) {
            throw ("New Record Creation Error : ", e);
        }
    };

    /**
     * @param table {string} Name of Table.
     * @param values {Object} JSON object containing column name as key and new value as value.
     *
     * Example:
     * * id = 50 => { id: 50 }.
     * * id = 50 AND username = 'abc' => { id: 50, username:'abc' }.
     * * status = 'active' AND (id = 50 OR username = 'abc') =>{ status: 'active', [Op.or]: [{id: 50}, {username: 'abc'}] }.
     *
     * @param options {Object} JSON object containing where clause as key value pair.
     */
    subform_gerund_entries = async (table, values = [], options = {}) => {
        const data = [];
        try {
            for (let i = 0; i < values.length; i++) {
                const record = await this.form_fetch(table, options[i], "");
                if (record.length < 1) {
                    const newRecord = await this.form_create(
                        table,
                        values[i],
                        ""
                    );
                    data.push(newRecord);
                } else {
                    const _newRecord = await this.form_update(
                        table,
                        values[i],
                        options[i]
                    );
                }
            }
            return data;
        } catch (e) {
            console.log(e);
        }
    };

    update_sync_mode = async (formMode, tableName, syncMode) => {
        try {
            const sequenceTableName = `${tableName}_id_seq`;
            const syncModeMixedstartId =
                this.shared.config.limits.sync_mode.mix_start_id;
            if (syncMode === "mixed") {
                const maxIdResult = await this.sequelize.query(
                    `SELECT max(id) as maxid FROM ${tableName};`,
                    {
                        type: QueryTypes.SELECT,
                    }
                );
                const maxId = maxIdResult[0]?.maxid ?? 1;
                if (maxId < syncModeMixedstartId) {
                    const query = `ALTER SEQUENCE "${sequenceTableName}" RESTART WITH ${syncModeMixedstartId};`;
                    await this.sequelize.query(query, {
                        type: QueryTypes.RAW,
                    });
                }
                await this.sequelize.query(
                    `UPDATE ${tableName} SET allow_sync = 'Yes' WHERE allow_sync IS NULL AND id < ${syncModeMixedstartId}`,
                    {
                        type: QueryTypes.RAW,
                    }
                );
            }
        } catch (e) {
            console.error(e);
            throw new Error(
                `Form Mode:${formMode}, Table Name:${tableName}, Sync Mode:${syncMode} Error:${e.message}`
            );
        }
    };
};
