const Sentry = (global.sentry = require("@sentry/node"));
const { nodeProfilingIntegration } = require("@sentry/profiling-node");

//CM:2024-09-17 https://docs.sentry.io/platforms/javascript/guides/node/
// We need to this run as early as possible
const sentryConfig = {
    dsn: "https://<EMAIL>/4507080374550528",
    integrations: [
        Sentry.anrIntegration({ captureStackTrace: true }),
        Sentry.captureConsoleIntegration(),
        nodeProfilingIntegration(),
    ],
    environment: process.env["NODE_ENV"] || "development",
    serverName:
        (process.env.FLY_APP_NAME || process.env.FLY_ID || "") +
        "/" +
        (process.env.FLY_MACHINE_ID || process.env.HOSTNAME || ""),

    autoSessionTracking: true,
    includeLocalVariables: true,
    tracesSampleRate: 0.1,
    profilesSampleRate: 1,
};
sentryConfig.attachStacktrace = sentryConfig["environment"] == "development";
Sentry.init(sentryConfig);
