{"name": "tshomebase", "private": true, "version": "1.0.0", "packageManager": "pnpm@9.5.0", "type": "module", "scripts": {"dev": "parcel serve --dist-dir dist/public/js --port 5173 --host 0.0.0.0", "build": "parcel build --no-scope-hoist --no-source-maps", "lint": "eslint -c .eslintrc.json --fix ."}, "source": "./dist/public/index.html", "sourceMaps": true, "@parcel/bundler-default": {"minBundles": 1, "minBundleSize": 65536, "maxParallelRequests": 10}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/resource-timeline": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@mescius/activereportsjs": "^5.0.3", "@mescius/activereportsjs-react": "^5.0.3", "@mui/icons-material": "^5.16.4", "@mui/material": "^5.15.20", "@mui/styles": "^5.15.10", "@mui/x-date-pickers": "^7.23.6", "@parcel/config-default": "^2.12.0", "@parcel/optimizer-data-url": "^2.12.0", "@parcel/packager-raw": "^2.12.0", "@parcel/plugin": "^2.12.0", "@parcel/resolver-default": "^2.12.0", "@parcel/resolver-glob": "^2.12.0", "@parcel/runtime-browser-hmr": "^2.12.0", "@parcel/source-map": "^2.1.1", "@parcel/transformer-inline-string": "^2.12.0", "@parcel/transformer-less": "2.12.0", "@parcel/transformer-raw": "^2.12.0", "@parcel/utils": "^2.12.0", "@superset-ui/embedded-sdk": "^0.1.3", "@types/react-slick": "^0.23.13", "@undecaf/zbar-wasm": "^0.11.0", "@usewaypoint/block-avatar": "^0.0.3", "@usewaypoint/block-button": "^0.0.3", "@usewaypoint/block-columns-container": "^0.0.3", "@usewaypoint/block-container": "^0.0.2", "@usewaypoint/block-divider": "^0.0.4", "@usewaypoint/block-heading": "^0.0.3", "@usewaypoint/block-html": "^0.0.3", "@usewaypoint/block-image": "^0.0.5", "@usewaypoint/block-spacer": "^0.0.3", "@usewaypoint/block-text": "^0.0.6", "@usewaypoint/document-core": "^0.0.6", "@usewaypoint/email-builder": "^0.0.8", "ag-charts-react": "^11.1.1", "ag-grid-enterprise": "^33.0.1", "ag-grid-react": "^33.0.1", "antd": "^5.22.7", "axios": "^1.5.1", "canvas": "^2.11.2", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "events": "^3.3.0", "gs1-parser": "^1.1.0", "jquery": "^3.6.3", "less": "^4.2.0", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-business-days": "^1.2.0", "pdfjs-dist": "3.4.120", "qz-tray": "^2.2.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chrono": "^2.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-json-view": "^1.21.3", "react-modal": "^3.16.1", "react-pdf": "^9.2.1", "react-select": "^5.8.0", "react-slick": "^0.30.2", "react-toastify": "^10.0.5", "react-xml-viewer": "^2.0.1", "reactflow": "^11.10.1", "slick-carousel": "^1.8.1", "sort-by": "^1.2.0", "usehooks-ts": "^2.9.1", "uuid": "^11.0.2", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/jquery": "^3.5.16", "@types/lodash": "^4.14.197", "@types/node": "^18.11.18", "@types/react": "^18.2.79", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-dom": "^18.2.25", "@types/react-modal": "^3.16.0", "@typescript-eslint/eslint-plugin": "^6.7.3", "@typescript-eslint/parser": "^6.7.3", "autoprefixer": "^10.4.14", "buffer": "^5.5.0||^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "parcel": "^2.12.0", "parcel-resolver-ignore": "^2.2.0", "process": "^0.11.10", "typescript": "^5.5.2"}}