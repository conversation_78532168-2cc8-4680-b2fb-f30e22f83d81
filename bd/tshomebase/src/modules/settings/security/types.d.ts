export interface Rule {
    module_auto_name: string;
    submodule: string;
    id: number;
    description: string;
}

export interface SecurityRule extends SecurityModule {
    description: string;
    parent: string;
    granted: boolean;
}

export interface SecurityModule {
    name: string;
    id: string;
    form: string;
    type: 'module' | 'submodule';
}
export interface SecurityRuleViewProps {
    headers: string[];
    rulesList: (SecurityModule | SecurityRule)[];
    sec_assign: SecAssign;
}
export interface SecAssign {
    url: string;
    foreign_id: string;
    foreign_id_key: string;
    type: string;
}
export interface SecurityDSLBundle {
    id: string,
    name: string;
    form: string;
    isPermitted?: boolean;
}
export interface SecurityDSLFormRules {
    [key: string]: number
}

export interface SecurityDslCheckboxProps {
    headers: string[];
    dslPermissionsObj: SecurityDSLFormRules;
    sec_assign: SecAssign;
    bundleList: SecurityDSLBundle[];
}
export interface DSLFormRule {
    id: string;
    type: 'header' | 'field';
    name: string;
    label: string;
    fields: string[];
}