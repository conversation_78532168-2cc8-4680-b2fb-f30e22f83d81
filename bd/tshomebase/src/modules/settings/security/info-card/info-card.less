.info-card-container {
    margin: 10px 0px;
    display: flex;
    flex-direction: column;
    background-color: #F5F7FA;
    border-radius: 10px;
    width: 30%;
    height: 92%;

    .info-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
    }

    .info-detail {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
    }

    .border {
        border-bottom: 0px;
        border-style: solid;
        border-color: #2098DC;
        margin: 10px 0px 10px 0px;
        width: 90%;
        align-self: center;
    }

    .phone-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
    }

    .cell .work {
        display: flex;
        flex-direction: column;
        margin: 5px;
    }

    .email-container {
        display: flex;
        margin: 5px;
        align-items: center;
        justify-content: flex-start;
        margin-top: 10px;
    }

    .email {
        display: flex;
        flex-direction: column;
        margin: 5px;
    }

    .bold {
        font-size: 16px;
        font-weight: 600;
    }

    .dob {
        color: #6487CE;
        font-size: 12px;
        font-weight: 500;
    }

    .image {
        width: 50px;
        height: 50px;
        border: 0.5px solid grey;
        border-radius: 50%;

        img {
            width: 49px;
            height: 49px;
            border-radius: 50%;
        }
    }

    .text {
        color: #68707F;
        font-size: 12px;
        font-weight: 500;
    }

}

.dsl-card-container {
    margin: 10px 0px;
    display: flex;
    flex-direction: column;
    background-color: #F5F7FA;
    border-radius: 10px;
    width: 30%;
    height: 92%;

    .info-container {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 10px;
    }

    .info-detail {
        display: flex;
        flex-direction: column;
        margin-left: 10px;

        .bold {
            font-size: 16px;
            font-weight: 600;
        }
    }

    .dsl-body {
        height: 100%;

    }

    .dsl-list-compact {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
        overflow-y: auto;

        table {
            margin: 0px !important;

            thead {
                tr {
                    background: transparent !important;

                    // background-color: #F5F7FA !important;
                    th {
                        background: transparent !important;
                        font-size: 16px;
                        font-weight: 600;
                    }
                }
            }

            tbody {
                tr {
                    background: transparent !important;

                    td {
                        background: transparent !important;

                    }
                }
            }
        }

        .dataTables_scrollHead {
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
        }

        .dataTables_scroll {
            thead {
                tr {
                    border-bottom: 1px solid #2098DC;
                }
            }
        }

        #application .dsl-grid-view .repeaterwrap .dataTables_wrapper .dataTables_scroll thead tr {
            background: #F5F7FA !important;
        }
    }

}