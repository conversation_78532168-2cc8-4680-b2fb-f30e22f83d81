import type { FC } from "react";
import React, { useEffect, useState } from "react";
import "./info-card.less";
import { request } from "@core/request/request";
import { DSLListCompactView } from "@blocks/dsl-list-compact-view/dsl-list-compact-view";
export interface InfoDetailCardProps {
	label: string;
	type: "info-card" | "dsl-card";
	data?: Record<string, unknown>;
	form: string;
	columnNames: string[];
	filters: object;
}

export const InfoCard: FC<InfoDetailCardProps> = (props) => {
	const { label, type, data, form, columnNames, filters } = props;
	const [user, setUser] = useState(type == "info-card" ? {} : []);

	const getUserDdata = async (id: number) => {
		if (type == "info-card") {
			request({
				url: "/form/" + form + "/?filter=id:" + id,
			})
				.then((resp) => {
					setUser(resp.data[0]);
				})
				.catch(() => {
					console.log("error while getting data");
				});
		} else if (type == "dsl-card") {
			request({
				url: "/form/" + form + "/?filter=id:" + id,
			})
				.then((resp) => {
					setUser(resp.data);
				})
				.catch(() => {
					console.log("error while getting data");
				});
		}
	};
	function toProperCase(str) {
		return str.replace(/_/g, " ").replace(/\w\S*/g, function (txt) {
			return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
		});
	}
	useEffect(() => {
		getUserDdata(data);
	}, [data]);

	return (
		<>
			{type == "info-card" ? (
				form == "user" ? (
					<div className="info-card-container">
						<div className="info-container">
							<div className="image">
								<img src={user?.image_url} />
							</div>
							<div className="info-detail">
								<div className="bold">{user?.auto_name}</div>
								<div className="dob">{user?.dob}</div>
							</div>
						</div>
						<div className="border" />
						{user?.phone_cell || user?.work_phone_cell ? (
							<div className="phone-container">
								{user?.phone_cell ? (
									<div className="cell">
										<div className="bold">Cell</div>
										<div className="text">{user?.phone_cell}</div>
									</div>
								) : null}
								{user?.work_phone_cell ? (
									<div className="work">
										<div className="bold">Work</div>
										<div className="text">{user?.work_phone_cell}</div>
									</div>
								) : null}
							</div>
						) : null}
						{user?.email ? (
							<div className="email-container">
								<div className="email">
									<div className="bold">Email</div>
									<div className="text">{user.email}</div>
								</div>
							</div>
						) : null}
						{user?.job_title ? (
							<div className="email-container">
								<div className="email">
									<div className="bold">Job Title</div>
									<div className="text">{user.job_title}</div>
								</div>
							</div>
						) : null}
					</div>
				) : (
					<div className="info-card-container">
						<div className="info-container">
							<div className="info-detail">
								<div className="bold">{user?.auto_name}</div>
							</div>
						</div>
						<div className="border" />
						{columnNames.map((column) => (
							<div className="phone-container" key={column}>
								<div className="cell">
									<div className="bold">{toProperCase(column)}</div>
									<div className="text">{user[column]}</div>
								</div>
							</div>
						))}
					</div>
				)
			) : (
				<>
					<div className="dsl-card-container">
						<div className="dsl-body">
							<div className="dsl-list-compact">
								<DSLListCompactView
									form={form}
									hideFilter={true}
									customColumns={[...columnNames]}
									filtersPresetFixed={{ ...filters }}
									readOnly={true}
								/>
							</div>
						</div>
					</div>
				</>
			)}
		</>
	);
};
