@import (reference) '../../../less/style/main.less';

.user-security-dsl-checkbox {
    display: flex;
    flex-direction: column;
    flex: 1;

    .header {
        border: 1px solid @light-gray !important;
        background-color: @white !important;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        flex: 0 0 36px;
    }

    .button-container {
        display: flex;
        flex-direction: row;
        width: 100%;
        min-height: fit-content;
        gap: 10px;
        justify-content: flex-end;
        padding: 2px;
        border-top: 1px solid lightgrey !important;
        background-color: #fff;


        .save-button {
            .btn-primary;
            display: flex;
            align-items: center;
            padding: 4px 8px !important;
            min-height: 28px !important;
        }
    }

    .heading {
        width: 50%;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        .para-six;
        justify-content: space-between;
        align-items: center;

        .submit-dsl {
            cursor: pointer;
        }

        .button-container {

            border-top: 0px !important;
        }
    }

    .module {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        font-size: var(--line-height-13);
        font-weight: bold;
        color: @black;
        text-transform: capitalize;
        margin: 10px;

        &.active-module {
            color: @purple;
        }

        &:hover {
            cursor: pointer;
        }

        .yes-no-btns {
            display: flex;
            flex-direction: row;

            .my-btn {
                cursor: pointer;
                background-color: white;
                border: 1px solid;
                padding: 5px 0;
                width: 40px;
                border-radius: 10px;
                text-align: center;
                margin: 0 5px;

                &.active-my-btn {
                    background-color: @purple;
                    color: white;
                }
            }
        }

        .module-heading {
            font-size: 16px;
            font-weight: 700;
            max-width: calc(100% - 120px);
        }

        .module-field {
            font-size: 14px;
            max-width: calc(100% - 120px);
            font-weight: initial;
        }
    }

    .submodule {
        cursor: inherit;
        display: flex;
        flex-direction: row;
        text-decoration: none;
        color: @black;
        .para-two;
        font-weight: 500;
        padding: 0px 30px;
        gap: 5px;
        margin: 2px 0px;

        img {
            margin-right: 5px;
        }

        &.active-submodule {
            color: @purple;
            font-weight: 600;
            margin-left: -2px;
            gap: 3px;
        }

        &.active {
            color: @purple;
            font-weight: 600;
        }

        &:hover {
            cursor: pointer;
        }
    }

    .module-box {
        border-right: 0px solid;
        border-top: 0px solid;
        width: 50%;
        display: flex;
        flex-direction: column;
        background-color: @light-gray;

        .module-box-upper {
            overflow: auto;
            height: 95%;

            .not-permitted-highlight {
                background-color: @brown;
            }
        }
    }

    .checkbox-box {
        border-top: 0px solid;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background-color: @light-gray;
        overflow-y: auto;
        flex: 1;

        .show-check-box {
            padding: 10px;
            overflow: auto;
            height: 92%;

            .form-level-label {
                font-size: 14px;
                font-weight: 600;
            }

            .security-dsl-nested-checkbox {
                display: flex;
                flex-direction: column;
                margin-left: 15px;
                gap: 5px;
                padding: 2px 0px 2px 0px;

                .form-level {
                    display: flex;
                    gap: 40px;

                    .checkbox-label {
                        cursor: pointer;
                        background-color: white;
                        border: 1px solid;
                        padding: 0px 5px 0px 5px;
                        border-radius: 10px;
                        width: 80px;
                        text-align: center;

                        &.active-button {
                            background-color: #C6DDF5;
                        }
                    }
                }
            }

            .security-dsl-nested-access-buttons {
                display: flex;
                flex-direction: column;
                margin-left: 15px;
                margin-top: 5px;
                gap: 4px;

                .security-dsl-nested-access-button-wrapper {
                    display: flex;
                    justify-content: space-between;

                    .section-label-wrapper {
                        width: 250px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 12px;
                    }

                    .access-buttons-wrapper {
                        display: flex;
                        width: 50%;
                        gap: 4px;
                        justify-content: space-between;

                        .read-button {
                            cursor: pointer;
                            user-select: none;
                            background-color: white;
                            border: 1px solid #AEAEAE;
                            border-radius: 10px;
                            width: 80px;
                            text-align: center;

                            &.active-button {
                                background-color: #C6DDF5;
                            }
                        }

                        .write-button {
                            cursor: pointer;
                            user-select: none;
                            background-color: white;
                            border: 1px solid #AEAEAE;
                            border-radius: 10px;
                            width: 80px;
                            text-align: center;

                            &.active-button {
                                background-color: #C6DDF5;
                            }
                        }
                    }
                }
            }

            .buttons-section {
                display: flex;
                justify-content: space-between;
                width: 100%;

                .section-label-wrapper {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 14px;
                    font-weight: 600;
                }

                .access-buttons-wrapper {
                    display: flex;
                    width: 37%;
                    justify-content: space-between;
                    gap: 4px;

                    .read-button {
                        cursor: pointer;
                        user-select: none;
                        background-color: white;
                        border: 1px solid #AEAEAE;
                        padding: 0px 5px 0px 5px;
                        border-radius: 10px;
                        width: 80px;
                        text-align: center;

                        &.active-button {
                            background-color: #C6DDF5;
                        }
                    }

                    .write-button {
                        cursor: pointer;
                        user-select: none;
                        background-color: white;
                        border: 1px solid #AEAEAE;
                        padding: 0px 5px 0px 5px;
                        border-radius: 10px;
                        width: 80px;
                        text-align: center;

                        &.active-button {
                            background-color: #C6DDF5;
                        }
                    }
                }
            }
        }


    }

    .searchbox {
        display: flex;
        flex-direction: column;
        padding: 2px 0px 2px 0px;

        .search-input {
            border: none;
            height: 30px;
            padding: 0px 0px 0px 10px;
            font-weight: 400;
        }
    }

    .perm-list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
        margin-bottom: 10px;

        .perm-button-container {
            display: flex;
            min-width: 45%;
        }

        .form-level-perms {
            cursor: pointer;
            background-color: white;
            border: 1px solid;
            padding: 0px 5px 0px 5px;
            border-radius: 10px;
            width: 100%;
            text-align: center;
            margin: 5px 0px 5px 0px;

            &.active-form-perm {
                background-color: @purple;
                color: white;
            }

            .perm-label {
                cursor: pointer;
                margin: 5px 0px 5px 0px;
            }
        }
    }

}