@import (reference) '../../../less/style/main.less';

.security-rule-view {
    display: flex;
    flex-direction: column;
    flex: 1;

    >div {
        gap: 2px;
    }

    .header {
        background-color: @white;
        border: 1px solid @light-gray !important;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        flex: 0 0 36px;
    }

    .heading {
        width: 50%;
        .para-six;
    }

    .module {
        padding: 4px 10px;
        font-size: var(--form-fs-md);
        font-weight: 600;
        color: @black;
        text-transform: capitalize;

        &.active {
            color: @purple;
        }

        &:hover {
            cursor: pointer;
        }
    }


    .submodule {
        cursor: inherit;
        display: flex;
        flex-direction: row;
        text-decoration: none;
        color: @black;
        .para-two;
        font-weight: 500;
        padding: 0px 30px;
        gap: 5px;

        img {
            margin-right: 5px;
        }

        &.active {
            color: @purple;
            font-weight: 600;
            margin-left: -2px;
            gap: 3px;
        }

        &:hover {
            cursor: pointer;
        }
    }


    .module-box {
        background-color: @light-gray;
        border-right: 0px solid;
        border-top: 0px solid;
        width: 50%;
        display: flex;
        flex-direction: column;

        .module-box-upper {
            overflow: auto;
            height: 95%;
        }

    }

    .checkbox-box {
        border-top: 0px solid;
        width: 50%;
        padding: 10px;
        overflow: auto;
        background-color: @light-gray;

        input {
            .checkbox-one;
            margin: 0;
        }

        label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }

        .show-check-box {
            cursor: pointer;
            margin: 5px 0px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

    }

    .searchbox {
        display: flex;
        flex-direction: column;
        padding: 2px 0px 2px 0px;

        .search-input {
            border: none;
            height: 30px;
            padding: 0px 0px 0px 10px;
            font-weight: 400;
        }
    }
}