import { useEffect, useState } from "react";
import "./user-security-editor.less";
import { request } from "@core/request/request";
import { DSLListCompactView } from "@blocks/dsl-list-compact-view/dsl-list-compact-view";
import { uuid } from "@utils/fx";
import { SecurityRuleView } from "../security-rule-view";
import { SecurityDslCheckbox } from "../security-dsl-checkbox";
import { useNavigation } from "@core/navigation";
import _ from "lodash";
import "@dsl/fields/field-embed.less";
import { Rule, SecurityRule, SecurityModule, SecurityDSLBundle } from "../types";
//WIP
export const UserSecurityEditorComponent = (props: any) => {
	const [rules, setRules] = useState<(SecurityModule | SecurityRule)[]>([]);
	const [dslList, setDSLList] = useState<SecurityDSLBundle[]>([]);
	const [dslPermissions, setDSLRules] = useState<{ [key: string]: number }>({});
	const headers = ["Module", "Rule"];
	const headers_right = ["Form", "Fields"];
	useNavigation(props, `/${props.id}/access`);

	const getDSLRules = async () => {
		const dslRules = await request({
			url: "/form/sec_assign/?filter=type:user_dsl&filter=user_id:" + props.user_id,
			method: "GET",
		});
		setDSLRules(dslRules.data[0]?.dsl || {});
	};
	const getData = async (user_id: number) => {
		const sec_assign = await request({
			url: "/form/sec_assign/?filter=user_id:" + user_id + "&filter=type:user_rule&limit=10000",
		});
		const rule_ids: number[] = sec_assign?.data.map((sec_assign: { rule: any }) => sec_assign.rule);
		const allRules = await request({
			url: "/form/sec_rule?filter=type:rule&filter=type:rls access&limit=10000",
		});

		// make a flat list of all rules and modules unsorted and non-grouped
		let rulesList: Array<SecurityModule | SecurityRule> = [];
		allRules?.data.forEach((rule: Rule) => {
			const to_a_chk: boolean = rule_ids.filter((r_id) => r_id == rule.id).length > 0;
			if (!rulesList.find((mod) => mod.id === rule.module_auto_name)) {
				const module: SecurityModule = {
					id: rule.module_auto_name,
					name: rule.module_auto_name,
					form: "sec_rule",
					type: "module",
				};
				rulesList.push(module);
			}
			if (!rulesList.find((sub) => parseInt(sub.id) === rule.id)) {
				const subRule: SecurityRule = {
					id: rule.id + "",
					parent: rule.module_auto_name,
					name: rule.description || rule.submodule,
					description: rule.description,
					type: "submodule",
					form: "sec_rule",
					granted: to_a_chk,
				};
				rulesList.push(subRule);
			}
		});
		// sort and group the list
		rulesList = sortAndGroupRules(rulesList);
		setRules(rulesList);
	};

	const getSecurityDSLData = async () => {
		const bundleList: SecurityDSLBundle[] = [
			{
				id: "bundle_patient",
				name: "Patient",
				form: "sec_assign",
			},
			{
				id: "bundle_manage",
				name: "Manage",
				form: "sec_assign",
			},
			{
				id: "bundle_other",
				name: "Other",
				form: "sec_assign",
			},
		];
		function toProperCase(str: string) {
			return str.replace(/_/g, " ").replace(/\w\S*/g, function (txt) {
				return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
			});
		}
		const insertAfterBundle = (bundleId: string, rules: SecurityDSLBundle[]) => {
			rules.sort((a, b) => a.name.localeCompare(b.name));
			const index = bundleList.findIndex((item) => item.id === bundleId);
			if (index !== -1) {
				bundleList.splice(index + 1, 0, ...rules);
			}
		};
		const rulesList: { patient: SecurityDSLBundle[]; manage: SecurityDSLBundle[]; other: SecurityDSLBundle[] } = {
			patient: [],
			manage: [],
			other: [],
		};
		for (const [form, dsl] of Object.entries(window.DSL)) {
			if (form.startsWith("__")) continue;
			const dslRule: SecurityDSLBundle = {
				id: form,
				name: dsl.view.label.length > 0 ? dsl.view.label : toProperCase(form), //TODO toPresentableLabel
				form: form,
			};
			if (dsl.model.bundle.includes("manage")) rulesList.manage.push(dslRule);
			else if (form.startsWith("patient")) rulesList.patient.push(dslRule);
			else rulesList.other.push(dslRule);
		}
		for (const [key, value] of Object.entries(rulesList)) {
			if (value.length > 0) {
				insertAfterBundle("bundle_" + key, value);
			}
		}
		setDSLList(bundleList);
	};

	const sortAndGroupRules = (items: (SecurityRule | SecurityModule)[]): (SecurityRule | SecurityModule)[] => {
		// First, sort all items by id
		const sortedItems = items.sort((a, b) => a.id.localeCompare(b.id));

		// Group SecurityRule by their parent (which should match a SecurityModule's id)
		const groupedRules: { [key: string]: SecurityRule[] } = {};
		sortedItems
			.filter((i): i is SecurityRule => i.type === "submodule")
			.map((item) => {
				const parentId: string = item.parent;
				if (!groupedRules[parentId]) {
					groupedRules[parentId] = [];
				}
				groupedRules[parentId].push(item);
			});

		// Sort each group of SecurityRule by name
		Object.values(groupedRules).map((group) => {
			group.sort((a, b) => a.name.localeCompare(b.name));
		});

		// Create the final sorted and grouped array
		const result: (SecurityRule | SecurityModule)[] = [];
		sortedItems.map((item) => {
			if (item.type === "module") {
				// This is a SecurityModule object
				result.push(item);
				if (groupedRules[item.id]) {
					result.push(...groupedRules[item.id]);
				}
			}
		});
		return result;
	};

	useEffect(() => {
		getData(props.user_id);
		getSecurityDSLData();
		getDSLRules();
	}, []);

	const secAssignDSL = {
		//WIP
		type: "user_dsl",
		foreign_id_key: "user_id",
		foreign_id: props.user_id,
		url: "/form/sec_assign/?filter=type:user_dsl&filter=user_id:" + props.user_id,
	};
	const secAssignRuleView = {
		//WIP
		type: "user_rule",
		foreign_id_key: "user_id",
		foreign_id: props.user_id,
		url: "/form/sec_assign?filter=type:user_rule&filter=user_id:" + props.user_id,
	};
	return (
		<div className="user-security-editor">
			<div className="editor-upper">
				<div className="editor-upper-left">
					<DSLListCompactView
						hideFilter={true}
						className="dsl-field-embed"
						form="sec_assign"
						customColumns={["sec_group", "start_date", "end_date"]}
						filtersPresetFixed={{ type: "user_group", user_id: props.user_id }}
						newPreset={{ type: "user_group", user_id: props.user_id }}
					/>
				</div>
				<div className="editor-upper-right">
					<DSLListCompactView
						hideFilter={true}
						className="dsl-field-embed"
						form="sec_assign"
						customColumns={["role", "start_date", "end_date"]}
						filtersPresetFixed={{ type: "user_role", user_id: props.user_id }}
						newPreset={{ type: "user_role", user_id: props.user_id }}
					/>
				</div>
			</div>
			<div className="editor-lower">
				<div className="editor-lower-left">
					<SecurityRuleView
						rulesList={rules}
						headers={headers}
						{...props}
						sec_assign={secAssignRuleView}
						key={rules.length}
					/>
				</div>
				<div className="editor-lower-right">
					<SecurityDslCheckbox
						dslPermissionsObj={dslPermissions}
						bundleList={dslList}
						headers={headers_right}
						{...props}
						sec_assign={secAssignDSL}
						key={dslList.length + "-" + Object.keys(dslPermissions).length}
					/>
				</div>
			</div>
		</div>
	);
};
