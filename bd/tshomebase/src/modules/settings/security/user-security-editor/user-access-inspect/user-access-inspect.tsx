import "./user-access-inspect.less";
import { InfoCard, InfoDetailCardProps } from "../../info-card/info-card";
import { UserSecurityEditorComponent } from "../user-security-editor";

export const UserAccessInspectComponent = (props: any) => {
	const actions = props.parentProps.tabViewActions;
	const { tabData } = props;
	const cards: any[] = [
		{
			label: "User",
			type: "info-card",
			form: "user",
		},
		{
			label: "Group",
			type: "dsl-card",
			form: "sec_assign",
			columnNames: ["sec_group"],
			filters: { type: "user_group", user_id: tabData?.id },
		},
		{
			label: "Role",
			type: "dsl-card",
			form: "sec_assign",
			columnNames: ["role"],
			filters: { type: "user_role", user_id: tabData?.id },
		},
		{
			label: "Rule",
			type: "dsl-card",
			form: "sec_assign",
			columnNames: ["rule"],
			filters: { type: "user_rule", user_id: tabData?.id },
		},
	];

	return (
		<div className="user-access-inspect-wrapper">
			{cards.map((card) => (
				<InfoCard
					label={card.label}
					type={card.type}
					data={tabData.id}
					form={card.form}
					key={card.label}
					columnNames={card?.columnNames}
					filters={card?.filters}
				/>
			))}
			<div className="button-container">
				<button
					className="edit-button"
					onClick={() => actions.openTab(tabData.id, tabData.label, "edit", tabData.form, {})}
				>
					Edit
				</button>
				<button
					className="access-button"
					onClick={() => actions.openTab(tabData.id, tabData.label, "access", tabData.form, {})}
				>
					Access
				</button>
			</div>
		</div>
	);
};

export const UserAccessInspectView = (props: any) => (
	<UserSecurityEditorComponent key={props.id} user_id={props.id} {...props} form={props.form} />
);
