@import (reference) "../../../../less/style/main.less";

.user-security-editor {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-grow: 1;
    flex-shrink: 1;
    margin-top: 10px;

    .editor-upper {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        flex: 1 1 50%;

        .dsl-field-embed {
            max-height: 100%;
        }

        .dataTables_scrollHead {
            border: 1px solid @light-gray !important;
            background-color: @white !important;
        }

        .editor-upper-left {
            width: 50%;
            background-color: @light-gray;
        }

        .editor-upper-right {
            width: 50%;
            background-color: @light-gray;
        }

        .dataTables_scrollBody {
            .tr-select {
                color: @purple;
                background-color: transparent !important;
            }
        }
    }

    .editor-lower {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        margin-top: 10px;
        flex: 1 1 50%;

        .editor-lower-left {
            width: 50%;
            display: flex;
        }

        .editor-lower-right {
            width: 50%;
            display: flex;
        }
    }

}