import "./role-access-inspect.less";
import { InfoCard } from "../../info-card/info-card";
import React from "react";
import { RoleSecurityEditorComponent } from "../role-security-editor";

interface Card {
	label: string;
	type: "info-card" | "dsl-card";
	form: string;
	columnName: string[];
	filters?: filters;
}
interface filters {
	type: string;
	role: string;
}
export const RoleAccessInspectComponent = (props) => {
	const actions = props.parentProps.tabViewActions;
	const { tabData } = props;

	const cards: Card[] = [
		{
			label: "Role",
			type: "info-card",
			form: "sec_role",
			columnName: ["name", "description"],
		},
		{
			label: "User",
			type: "dsl-card",
			form: "sec_assign",
			columnName: ["user_id"],
			filters: { type: "user_role", role: tabData?.id },
		},
		{
			label: "Group",
			type: "dsl-card",
			form: "sec_assign",
			columnName: ["sec_group"],
			filters: { type: "group_role", role: tabData?.id },
		},
		{
			label: "Rule",
			type: "dsl-card",
			form: "sec_assign",
			columnName: ["rule"],
			filters: { type: "role_rule", role: tabData?.id },
		},
	];
	return (
		<div className="role-access-inspect-wrapper">
			{cards.map((card) => (
				<InfoCard
					label={card.label}
					type={card.type}
					data={tabData.id}
					form={card.form}
					key={card.label}
					columnNames={card?.columnName}
					filters={card?.filters}
				/>
			))}
			<div className="button-container">
				<div
					className="edit-button"
					onClick={() => actions.openTab(tabData.id, tabData.label, "edit", tabData.form, {})}
				>
					Edit
				</div>
				<div
					className="access-button"
					onClick={() => actions.openTab(tabData.id, tabData.label, "access", tabData.form, {})}
				>
					Access
				</div>
			</div>
		</div>
	);
};
export const RoleAccessInspectView = (props) => (
	<RoleSecurityEditorComponent key={props.id} role_id={props.id} {...props} form={props.form} />
);
