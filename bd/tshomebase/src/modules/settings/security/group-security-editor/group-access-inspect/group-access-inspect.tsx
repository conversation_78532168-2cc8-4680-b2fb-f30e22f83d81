import "./group-access-inspect.less";
import { InfoCard } from "../../info-card/info-card";
import React from "react";
import { GroupSecurityEditorComponent } from "../group-security-editor";

export const GroupAccessInspectComponent = (props) => {
	const actions = props.parentProps.tabViewActions;
	const { tabData } = props;

	const cards = [
		{
			label: "Group",
			type: "info-card",
			form: "sec_group",
			columnName: ["name", "code", "description"],
		},
		{
			label: "User",
			type: "dsl-card",
			form: "sec_assign",
			columnName: ["user_id"],
			filters: { type: "user_group", sec_group: tabData?.id },
		},
		{
			label: "Role",
			type: "dsl-card",
			form: "sec_assign",
			columnName: ["role"],
			filters: { type: "group_role", sec_group: tabData?.id },
		},
		{
			label: "Rule",
			type: "dsl-card",
			form: "sec_assign",
			columnName: ["rule"],
			filters: { type: "group_rule", sec_group: tabData?.id },
		},
	];

	return (
		<div className="group-access-inspect-wrapper">
			{cards.map((card) => (
				<InfoCard
					label={card.label}
					type={card.type}
					data={tabData.id}
					form={card.form}
					key={card.label}
					columnNames={card?.columnName}
					filters={card?.filters}
				/>
			))}
			<div className="button-container">
				<div
					className="edit-button"
					onClick={() => actions.openTab(tabData.id, tabData.label, "edit", tabData.form, {})}
				>
					Edit
				</div>
				<div
					className="access-button"
					onClick={() => actions.openTab(tabData.id, tabData.label, "access", tabData.form, {})}
				>
					Access
				</div>
			</div>
		</div>
	);
};

export const GroupAccessInspectView = (props) => (
	<GroupSecurityEditorComponent key={props.id} group_id={props.id} {...props} form={props.form} />
);
