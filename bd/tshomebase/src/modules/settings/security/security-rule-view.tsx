import { useState, useMemo } from "react";
import type { FC } from "react";
import PublicIcon from '../../../public/icons';
import "./security-rule-view.less";
import { request } from "@core/request/request";
import { SecurityRule, SecurityModule, SecurityRuleViewProps } from './types'

export const SecurityRuleView: FC<SecurityRuleViewProps> = (props) => {
	const { headers, rulesList } = props;
	const [filter, setFilter] = useState<string>("");
	const [activeRulesList, SetRulesList] = useState<SecurityRule[]>([]);
	const [activeRuleID, setActiveRuleID] = useState<string>("");

	const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFilter(e.target.value);
	};

	const clickHandlerParentRule = (name: string): void => {
		const filtered = rulesList.filter((r): r is SecurityRule => "parent" in r && r.parent === name);
		if (filtered.length > 0)
			setActiveRuleID(name);
		else setActiveRuleID("");
		SetRulesList(filtered);
	};

	const clickHandlerSubRule = (name: string): void => {
		const filtered = rulesList.filter((r): r is SecurityRule => "parent" in r && r.name === name);
		if (filtered.length > 0)
			setActiveRuleID(filtered[0].id);
		else setActiveRuleID("");
		SetRulesList(filtered);
	};

	const filteredRules: (SecurityRule | SecurityModule)[] = useMemo(() => {
		const filtered: (SecurityRule | SecurityModule)[] = [];
		for (let r of rulesList) {
			if (r.name.toLowerCase().includes(filter.toLowerCase())) {
				filtered.push(r);
			}
		}
		if (filtered.length > 0) {
			let ar = filtered.find(r => r.type === 'module')
			if (!ar) {
				ar = filtered[0];
				clickHandlerSubRule(ar.name);
			} else {
				clickHandlerParentRule(ar.name);
			}
		}
		return filtered;
	}, [filter, rulesList]);

	const handleCheckboxChange = async (subrule: SecurityRule, grant: boolean) => {
		const sec_assign_with_rule = await request({
			url: props.sec_assign.url + "&filter=rule:" + subrule.id + "&archived=all",
			method: "GET"
		});
		if (sec_assign_with_rule.data.length > 0) {
			if (!grant) {
				await request({
					url: "/form/sec_assign/" + sec_assign_with_rule?.data[0]?.id,
					method: "PUT",
					data: {
						archived: true,
					}
				});
			} else {
				await request({
					url: "/form/sec_assign/" + sec_assign_with_rule?.data[0]?.id + "/unarchive",
					method: "PUT",
					data: {
						archived: false
					}
				});
			}
		} else {
			const obj = {
				type: props.sec_assign.type,
				rule: subrule.id,
				rule_auto_name: subrule.description,
				[props.sec_assign.foreign_id_key]: props.sec_assign.foreign_id
			};
			await request({
				url: "/form/sec_assign/",
				method: "POST",
				data: obj
			});
		}
	}

	return (
		<div className="security-rule-view">
			<div className="header">{
				headers.map((heading) => <div className="heading" key={heading}> {heading} </div>)
			}
			</div>
			<div style={{ display: "flex", flexDirection: "row", flex: 1 }}>
				<div className="module-box">
					<div className="module-box-upper"> {
						filteredRules.map(rule => {
							if (rule.type === 'module')
								return (
									<ParentModule rule={rule} key={rule.id} onClick={clickHandlerParentRule} isActive={rule.id === activeRuleID} />
								)
							else
								return (
									<Rules subrule={rule as SecurityRule} key={rule.id} onClick={clickHandlerSubRule} isActive={rule.id === activeRuleID} />
								)
						})
					}
					</div>
					<div className="searchbox">
						<input
							type="text"
							placeholder="Filter Module"
							value={filter}
							onChange={handleFilterChange}
							className="search-input"
						/>
					</div>
				</div>
				<div className="checkbox-box"> {
					activeRulesList.map((r) => <RuleAction key={r.id} subrule={r} onClick={handleCheckboxChange} />)
				}
				</div>
			</div>
		</div>
	);
};

const ParentModule = ({ rule, isActive, onClick }: { rule: SecurityModule, isActive: boolean, onClick: (name: string) => void }) => {
	return (
		<div className={isActive ? "module active" : "module"} key={rule.id} onClick={() => onClick(rule.name)}>{rule.name}</div>
	);
};

const Rules = ({ subrule, isActive, onClick }: { subrule: SecurityRule, isActive: boolean, onClick: (name: string) => void }) => {
	return (
		<div className={isActive ? "submodule active" : "submodule"} key={subrule.id} onClick={() => onClick(subrule.name)}>
			<img src={isActive ? PublicIcon.common.menuBulletAsteriskSingleFilledActive : PublicIcon.common.menuBulletAsteriskSingleFilled} alt="" /> {subrule?.name}
		</div>
	);
}

const RuleAction = ({ subrule, onClick }: { subrule: SecurityRule, onClick: (subrule: SecurityRule, grant: boolean) => void }) => {
	const [isChecked, setChecked] = useState<boolean>(subrule.granted);
	return (
		<div key={subrule.id} className="show-check-box" onClick={() => {
			const grant = !isChecked;
			setChecked(grant);
			onClick(subrule, grant);
		}} >
			<input type="checkbox" checked={isChecked} onChange={() => { }} />
			{"  " + subrule.description}
		</div>
	)
}