import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import "./delivery-ticket-scanner.less";
import { useEffect, useRef, useState } from "react";
import { fetchFormData, fetchFormFilters, IFormData } from "@hooks/index";
import { AgGridReact, CustomCellRendererProps } from "ag-grid-react";
import "../../../blocks/dsl-advanced-grid/dsl-advanced-grid.less";
import { request } from "@core/request";
import icons from "../../../public/icons/common";
import { CRErrorBoundary } from "@blocks/error-boundary";
import NoDataFound from "@components/common/no-data-found";
import { ColDef } from "ag-grid-enterprise";
import { showToastError } from "@utils/fx";
import { Switch, ThemeProvider } from "@mui/material";
import { baseTheme } from "@core/theme";

export const WTItemDetail = (props: any) => {
	const dtItem = props.data;
	const disabled: boolean = props.disabled;
	const handle: any = props.handle;
	const triggerParentFieldRefresh = props.triggerParentFieldRefresh;
	const dd = handle.parent as IDSLDrawSubForm;
	const [wtPulledItems, setWTPulledItems] = useState<IFormData[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const gridRef = useRef<AgGridReact>(null);

	const refresh = (refreshParent: boolean = true) => {
		refreshWTPulledItems();
		if (refreshParent) {
			triggerParentFieldRefresh();
			props.recalculatePullAllStatus();
		}
	};

	useEffect(() => {
		refreshWTPulledItems();
	}, []);

	const refreshWTPulledItems = async () => {
		fetchFormFilters("careplan_dt_wt_pulled", {
			filter: {
				ticket_item_no: dtItem.ticket_item_no || "-1",
				dispensed_unit_id: dtItem.dispense_unit || "-1",
				void: "!Yes",
			},
		})
			.then((resp) => {
				setWTPulledItems(resp.data);
				gridRef.current?.api?.setGridOption?.("rowData", wtPulledItems);
				setLoading(false);
			})
			.catch((e) => {
				console.log(e);
				setLoading(false);
			});
	};

	const updateRow = (id: string, data: any, refreshParent: boolean = true) => {
		if (disabled) {
			return;
		}
		let oldData: any = null;
		setWTPulledItems((prev) => prev.map((item) => {
			if (item.id === id) {
				oldData = item;
				return { ...item, ...data }
			} else return item
		}));
		request({
			url: "/form/careplan_dt_wt_pulled/" + id,
			method: "PUT",
			data: data,
		}).then((resp) => {
			if (resp.data) {
				setWTPulledItems((prev) => prev.map((item) => (item.id === id ? { ...item, ...resp.data } : item)));
				refresh(refreshParent);
			}
		}).catch((err) => {
			setWTPulledItems((prev) => prev.map((item) => (item.id === id ? { ...item, ...oldData } : item)));
			window.prettyError("Error", err?.data?.response?.data?.Error);
		});
	};

	const deleteRow = (id: string) => {
		if (['ready_to_fill', 'order_ver', 'pending_conf'].includes(dd.value_field_get("status", true))) {
			window.prettyConfirm(
				"Remove Item",
				"Are you sure you want to return this item to stock?",
				"Put Back",
				"Close",
				() => {
					updateRow(id, {
						archived: true,
						void: "Yes",
					});
				},
				() => { },
				window.BootstrapDialog.TYPE_DANGER
			);
		}
	};

	const [columnDefs] = useState<ColDef[]>([
		{ field: "inventory_id_auto_name", headerName: "Inventory", flex: 4, minWidth: 200 },
		{
			field: "dispensed_quantity",
			headerName: "Quantity",
			flex: 1,
			cellStyle: { textAlign: "right" },
		},
		{
			field: "lot_no",
			headerName: "Lot No",
			flex: 2,
		},
		{
			field: "serial_no",
			headerName: "Serial No",
			flex: 2,
		},
		{
			field: "expiration_date",
			headerName: "Expiration Date",
			flex: 2,
		},
		{
			field: "item_verified",
			headerName: "Item Verified",
			width: dd.value_field_get("status", true) === "order_ver" ? 200 : 0,
			cellRenderer: (props: CustomCellRendererProps) => {
				const status = dd.value_field_get("status", true);
				const canVerify = window.Auth.can_access_path("/careplan_delivery_tick/pulled/item/can/verify");
				if (status !== "order_ver") {
					return null;
				}
				return (
					<div
						style={{
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
						className="custom-cell-button"
					>
						<Switch
							className="custom-cell-button"
							checked={props.data.item_verified === "Yes"}
							disabled={disabled || !canVerify}
							onChange={(e) =>
								updateRow(props.data.id, { item_verified: e.target.checked ? "Yes" : null }, false)
							}
						/>
					</div>
				);
			},
		},
		{
			headerName: "",
			width: dd.value_field_get("status", true) !== "ready_to_fill" ? 0 : 100,
			cellRenderer: (props: CustomCellRendererProps) => {
				const status = dd.value_field_get("status", true);
				if (status !== "ready_to_fill") {
					return null;
				}
				const inventoryType = dd.value_field_get("inventory_type", true);
				const isInEditableState = ['ready_to_fill', 'order_ver', 'pending_conf'].includes(status);
				const showDelete = isInEditableState && inventoryType != 'Billable' && !disabled;

				return (
					<div
						className="custom-cell-button"
						style={{
							padding: 10,
							display: "flex",
							justifyContent: "flex-end",
							alignItems: "center",
							height: "100%",
							gap: 16,
						}}
						onClick={(e) => {
							e.stopPropagation();
							if (!showDelete) {
								return;
							}
							deleteRow(props.data.id);
						}}
					>
						{showDelete ? (
							<img
								src={icons.deleteOutline}
								alt="Delete"
								className="custom-cell-button"
								style={{ width: 20, height: 20 }}
							/>
						) : null}
					</div>
				);
			},
		},
	]);

	const addDispensedItem = async () => {
		const pulledRec = await request({
			url: `/form/careplan_dt_wt_pulled?
			filter=inventory_id:${dtItem.inventory_id || -1}&
			filter=ticket_item_no:${dtItem.ticket_item_no || -1}&
			filter=dispensed_unit_id:${dtItem.dispense_unit || -1}&
			filter=void:!Yes`,
		});
		if (pulledRec.data.length > 0) {
			const totalPulled = pulledRec.data.reduce(
				(acc: number, item: any) => acc + parseFloat(item.dispensed_quantity),
				0
			);
			if (totalPulled == dtItem.dispense_quantity) {
				showToastError(
					"Item already pulled from stock, please verify the item before adding to the delivery ticket."
				);
				return;
			}
		}
		fetchFormData("inventory", dtItem.inventory_id, true, "list")
			.then((resp) => {
				const inventoryRec = resp.data;
				const preset: Record<string, any> = {
					ticket_no: dtItem.ticket_no,
					patient_id: dtItem.patient_id || dd.options?.linkid?.patient,
					ticket_item_no: dtItem.ticket_item_no,
					inventory_id: dtItem.inventory_id,
					dispensed_unit_id: dtItem.dispense_unit,
					site_id: dtItem.site_id,
					quantity_needed: dtItem.quantity_needed,
					status: dtItem.status,
					inventory_type: inventoryRec.type,
					lot_tracking: inventoryRec.lot_tracking,
					serial_tracking: inventoryRec.serial_tracking,
					dispensed_quantity: inventoryRec.serial_tracking == "Yes" ? 1 : null,
				};
				window.Flyout.open({
					form: "careplan_dt_wt_pulled",
					mode: "addfill",
					preset: preset,
					form_override_url: "/dispense?func=add_dispensed_item",
					link: dd.options.link,
					links: dd.options.links,
					linkid: dd.options.linkid,
				}).done(() => {
					refresh();
				});
			})
			.catch((e) => {
				console.log(e);
			});
	};

	return (
		<ThemeProvider theme={baseTheme}>
			<div
				className="delivery-ticket-second-grid dsl-advanced-grid"
				style={{
					height: "unset",
					padding: 10,
				}}
			>
				{!dtItem.ticket_item_no || !dtItem.dispense_unit ? (
					<NoDataFound
						style={{
							height: "min-content",
							width: "100%",
							padding: 10,
						}}
						text="Delivery Ticket Item is Missing 'Ticket Item No' or 'Dispensed Unit', Fix this before Scanning/Adding Items."
					/>
				) : null}
				<div className="ag-grid-table-container">
					<CRErrorBoundary>
						<AgGridReact
							rowData={wtPulledItems}
							rowClassRules={{
								"even-background": (params) => params.rowIndex % 2 === 1,
								// "odd-background": (params) => params.rowIndex % 2 === 0,
							}}
							ref={gridRef}
							onRowDoubleClicked={(params) => {
								console.log(params);
								if (!params.data?.id) {
									return;
								}
								const dfd = window.Flyout.open({
									form: "careplan_dt_wt_pulled",
									mode: disabled ? "read" : "edit",
									record: params.data.id,
									link: dd.options.link,
									links: dd.options.links,
									linkid: dd.options.linkid,
								});
								dfd.done(() => {
									refresh();
								});
							}}
							suppressMenuHide={true}
							getRowId={(params) =>
								params.data.code +
								"_" +
								params.data.id +
								"_" +
								params.data.updated_on +
								"_" +
								params.data.created_on
							}
							columnDefs={columnDefs}
							domLayout="autoHeight"
							animateRows={true}
							defaultColDef={{
								sortable: false,
								filter: false,
								resizable: false,
							}}
							loading={loading}
						/>
					</CRErrorBoundary>
					{dd.value_field_get("status", true) === "ready_to_fill" && !disabled ? (
						<div className="add-dispensed-item-button" onClick={addDispensedItem}>
							+ Add Dispensed Item
						</div>
					) : null}
				</div>
			</div>
		</ThemeProvider>
	);
};
