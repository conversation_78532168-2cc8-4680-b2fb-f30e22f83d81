
.delivery-ticket-scanner {
    width: 100% !important;
    display: flex;
    flex-direction: column;
    .ag-body-viewport {
        min-height: 42px !important;
        .ag-center-cols-viewport {
            min-height: 42px !important;
        }
        .ag-center-cols-container {
            min-height: 42px !important;
        }
    }
    .delivery-ticket-second-grid {
        .ag-root-wrapper{
            border: none;
            border-radius: 0;
        }
        
        padding: 8px 8px 12px 8px !important;
        display: flex;
        flex-direction: column;
        box-shadow: 0px 1px 3px 0px #38383814 inset;
        border-radius: 6px;

        .ag-grid-table-container{
            background-color: #fff;
            border: 1px solid #E5E5E5;
            border-radius: 8px;
        }
        .ag-header-container {
            background-color: #E5E5E0;
            color:#949491;
            font-size: 12px;
            line-height: 18px;
            font-weight: 500;
        }

        .ag-row-odd{
            background-color: #fff;
            border-top: 1px solid gray;
        }

        .ag-cell-value{
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 700;
            color: #4D525C;
            line-height: 20px;
        }

        .quantity-column {
            padding: 0 8px;
            border: 1px solid #E3E5E8;
            border-radius: 8px;
            text-align: right;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) inset;
            outline: none;
        }

        .ag-icon-menu-alt{
            display: none;
        }
    }
    .add-dispensed-item-button {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0%;   
        color: #837BB2;
        padding: 10px;
    }

    .delivery-ticket-grid-footer {
        margin-top: 10px;
        justify-content: center;
        display: flex;
        align-items: center;
        .delivery-ticket-error-message {
            line-height: var(--line-height-medium);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-standard);
            color: #e58787;
            font-weight: 700;
            i {
                margin-right: 8px;
            }
        }
    }
}
