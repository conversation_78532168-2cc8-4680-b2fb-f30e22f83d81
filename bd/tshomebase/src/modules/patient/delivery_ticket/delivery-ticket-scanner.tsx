import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { CLJQueryObject, DSLFxTransforms } from "@typedefs/window";
import "./delivery-ticket-scanner.less";
import { useEffect, useRef, useState } from "react";
import { getFiltersFromSourceFilter } from "@utils/fx";
import { fetchFormFilters, IFormData } from "@hooks/index";
import { DSLField } from "@typedefs/coffee/dsl";
import { AgGridReact, CustomCellRendererProps } from "ag-grid-react";
import { ColDef, RowGroupOpenedEvent } from "ag-grid-enterprise";

import "../../../blocks/dsl-advanced-grid/dsl-advanced-grid.less";
import { request } from "@core/request";
import { Switch, ThemeProvider } from "@mui/material";
import { baseTheme } from "@core/theme";
import icons from "../../../public/icons/common";
import { WTItemDetail } from "@modules/patient/delivery_ticket/delivey-ticket-dt-puller";
import { getPresetDTPulled } from "@dsl-form/validators/careplan_dt_wt_pulled";
import { checkIfAllItemsPulled } from "@dsl-form/validators/careplan_dt_wt_pulled";
import useToast from "@hooks/use-toast";
import { ToastContainer } from "react-toastify";

interface DeliveryTicketScannerProps {
	handle: {
		nodes: () => {
			embed_item: CLJQueryObject;
		};
		field_nodes: Record<string, CLJQueryObject>;
		parent: IDSLDrawSubForm;
	};
	disabled: boolean;
}

export const DeliveryTicketScanner = (props: DeliveryTicketScannerProps) => {
	const { handle, disabled } = props;
	const dd = handle.parent;
	const [dtItems, setDTItems] = useState<IFormData[]>([]);
	const [loading, setLoading] = useState(false);
	const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
	const gridRef = useRef<AgGridReact>(null);
	const [showToast] = useToast();

	const refresh = () => {
		refreshDTItems();
		recalculatePullAllStatus();
	};

	const recalculatePullAllStatus = () => {
		checkIfAllItemsPulled(dd, dd.value_field_get("ticket_no", true));
	};

	useEffect(() => {
		refreshDTItems();
		recalculatePullAllStatus();
	}, []);

	const showToastError = (message: string) => {
		showToast({ type: "error", message }, true);
	};

	const onGridReady = () => {
		if (gridRef.current?.api) {
			// Set up row expansion tracking
			gridRef.current.api.addEventListener('rowGroupOpened', (event: RowGroupOpenedEvent) => {
				const rowId = event.data?.code + "_" + event.data?.id + "_" + event.data?.updated_on + "_" + event.data?.created_on;
				if (event.expanded) {
					setExpandedRows(prev => new Set([...prev, rowId]));
				} else {
					setExpandedRows(prev => {
						const newSet = new Set(prev);
						newSet.delete(rowId);
						return newSet;
					});
				}
			});

			// Restore expanded state
			setTimeout(() => {
				gridRef.current?.api?.forEachNode((node) => {
					const rowId = node.data?.code + "_" + node.data?.id + "_" + node.data?.updated_on + "_" + node.data?.created_on;
					if (expandedRows.has(rowId)) {
						node.setExpanded(true);
					}
				});
			}, 100);
		}
	};

	const barCodeScanner: DSLFxTransforms = async (form, dd, trn, f, k) => {
		const bv = trn.__barcode;
		if (!bv) {
			return;
		}
		if (bv.error) {
			showToastError(bv.error);
			return;
		}
		const values = bv.values;
		if (!values) {
			console.error("No values found");
			return;
		}
		if (!values.inventory) {
			showToastError("No inventory found against barcode");
			return;
		}
		const scanValues = values.scan_values;
		if (!scanValues) {
			showToastError("No scan values found");
			return;
		}
		if (!scanValues.gtin) {
			showToastError("No gtin found");
			return;
		}
		if (!scanValues.lot && !scanValues.serial) {
			showToastError("No lot or serial found.");
			return;
		}
		if (dd.value_field_get("status", true) === "order_ver") {
			request({
				url: "/dispense?func=scanner_verification",
				method: "POST",
				data: {
					inventory_id: values.inventory.id,
					ticket_no: dd.value_field_get("ticket_no", true),
					lot: scanValues.lot,
					serial: scanValues.serial,
				},
			})
				.then((resp) => {
					if (resp.data.success) {
						window.Flyout.open({
							form: "careplan_dt_wt_pulled",
							mode: "edit",
							record: resp.data.pulled_items.id,
							link: dd.options.link,
							links: dd.options.links,
							linkid: dd.options.linkid,
							on_loaded: () => {
								window.prettyAlert("Please verify the quantity before saving.");
							},
						}).done(() => {
							refresh();
							updateRowPulled(resp.data.pulled_items.id, {item_verified: 'Yes'}, false);
						});
					} else if (resp.data.popup) {
						window.Flyout.open({
							form: "careplan_dt_wt_pulled",
							mode: "edit",
							record: resp.data.pulled_items.id,
							preset: resp.data.pulled_items,
							link: dd.options.link,
							links: dd.options.links,
							linkid: dd.options.linkid,
						}).done(() => {
							refresh();
						});
						showToastError(resp.data.error);
					}
				})
				.catch((err) => {
					console.error(err);
					showToastError(
						"Unexpected error while creating delivery dispense ticket item. please try fixing it manually."
					);
				});
			return;
		}
		const preset = await getPresetDTPulled(
			values.inventory.id,
			dd.value_field_get("ticket_no", true),
			scanValues.lot,
			scanValues.serial,
			scanValues.expiration
		);
		if (preset.error) {
			showToastError(preset.message);
			return;
		}
		if (!preset.patient_id) {
			preset.patient_id = dd?.options?.linkid?.patient;
		}
		if (!preset.careplan_id) {
			preset.careplan_id = dd?.options?.linkid?.careplan;
		}
		if (preset.id) {
			window.Flyout.open({
				form: "careplan_dt_wt_pulled",
				mode: "edit",
				link: dd.options.link,
				links: dd.options.links,
				linkid: dd.options.linkid,
				record: preset.id,
				preset: preset,
			}).done(() => {
				refresh();
			});
		} else {
			request({
				url: "/form/careplan_dt_wt_pulled/",
				method: "POST",
				data: preset,
			})
				.then((resp) => {
					refresh();
				})
				.catch((err) => {
					console.error(err);
					showToastError(
						"Unexpected error while creating delivery dispense ticket item. please try fixing it manually."
					);
					window.Flyout.open({
						form: "careplan_dt_wt_pulled",
						mode: "addfill",
						link: dd.options.link,
						links: dd.options.links,
						linkid: dd.options.linkid,
						preset: preset,
					}).done(() => {
						refresh();
					});
				});
		}
	};

	const refreshDTItems = async () => {
		const f = dd.field_nodes.embed_item;
		if (!f) {
			console.error("No embed item field found");
			return;
		}
		const v = f.data("v") as DSLField;
		if (!v) {
			console.error("No embed item field found");
			return;
		}
		const { filters, gridParams } = getFiltersFromSourceFilter(dd, v.model.sourcefilter);
		fetchFormFilters("careplan_dt_item", {
			filter: filters as any,
		})
			.then((resp) => {
				setDTItems(resp.data);
				setLoading(false);
				gridRef.current?.api?.setGridOption?.("rowData", dtItems);
				// Restore expanded state after data refresh
				setTimeout(() => {
					gridRef.current?.api?.forEachNode((node) => {
						const rowId = node.data?.code + "_" + node.data?.id + "_" + node.data?.updated_on + "_" + node.data?.created_on;
						if (expandedRows.has(rowId)) {
							node.setExpanded(true);
						}
					});
				}, 100);
			})
			.catch((e) => {
				console.log(e);
				setLoading(false);
			});
	};

	const updateRow = (id: string, data: any, refresh: boolean = true) => {
		if (disabled) {
			return;
		}
		setDTItems((prev) => prev.map((item) => (item.id === id ? { ...item, ...data } : item)));
		request({
			url: "/form/careplan_dt_item/" + id,
			method: "PUT",
			data: data,
		}).then((resp) => {
			if (resp.data) {
				setDTItems((prev) => prev.map((item) => (item.id === id ? { ...item, ...resp.data } : item)));
				triggerParentFieldRefresh();
				recalculatePullAllStatus();
			}
		});
	};

	const updateRowPulled = (id: string, data: any, refresh: boolean = true) => {
		request({
			url: "/form/careplan_dt_wt_pulled/" + id,
			method: "PUT",
			data: data,
		}).then((resp) => {
			if (resp.data) {
				triggerParentFieldRefresh();
				recalculatePullAllStatus();
			}
		});
	};

	const triggerParentFieldRefresh = () => {
		const helper = dd?.field_nodes?.embed_item?.data?.("helper");
		if (helper) {
			helper.refreshGrid();
			helper.embedValidateTransform();
		}
	};

	const deleteRow = (id: string) => {
		window.prettyConfirm(
			"Remove Item",
			"Are you sure you want to remove this item from the delivery ticket?",
			"Remove",
			"Close",
			() => {
				updateRow(id, {
					archived: true,
				});
			},
			() => {},
			window.BootstrapDialog.TYPE_DANGER
		);
	};
	const [columnDefs] = useState<ColDef<IFormData>[]>([
		{
			field: "print",
			headerName: "Print",
			width: 70,
			cellRenderer: (params: CustomCellRendererProps) => {
				const isDisabled = ['ready_to_bill', 'billed', 'void'].includes(params?.data?.status) || disabled;
				return (
					<div
						style={{
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
						className="custom-cell-button"
					>
						<Switch
							className="custom-cell-button"
							checked={params.data.print === "Yes"}
							onChange={(e) => isDisabled ? null : updateRow(params.data.id, { print: e.target.checked ? "Yes" : null }, false)}
							disabled={isDisabled}
						/>
					</div>
				);
			},
		},
		{
			field: "bill",
			headerName: "Bill",
			width: 70,
			cellRenderer: (params: CustomCellRendererProps) => {
				const isDisabled = ['ready_to_bill', 'billed', 'void'].includes(params?.data?.status) || disabled;
				return (
					<div
						style={{
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
						className="custom-cell-button"
					>
						<Switch
							className="custom-cell-button"
							checked={params.data.bill === "Yes"}
							onChange={(e) => isDisabled ? null : updateRow(params.data.id, { bill: e.target.checked ? "Yes" : null }, false)}
							disabled={isDisabled}
						/>
					</div>
				);
			},
		},
		{ field: "inventory_id_auto_name", headerName: "Inventory", flex: 5 },
		{
			field: "hcpc_code",
			headerName: "HCPC",
			cellRenderer: (params: CustomCellRendererProps) => {
				return <div style={{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: '100%'}}>{params.data.hcpc_code}</div>;
			},
			flex: 2,
		},
		{
			field: "formatted_ndc",
			headerName: "NDC",
			flex: 3,
			cellRenderer: (params: CustomCellRendererProps) => {
				return <div style={{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: '100%'}}>{params.data.formatted_ndc}</div>;
			},
		},
		{
			field: "dispense_quantity",
			headerName: "Dispensed",
			flex: 3,
			cellRenderer: (params: CustomCellRendererProps) => {
				return <div style={{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: '100%'}}>{window.numeral(params.data.type === 'Billable' ? params.data.dispense_quantity : params.data.dispense_quantity-params.data.quantity_needed).format('0,0.[000000]')} of {window.numeral(params.data.dispense_quantity).format('0,0.[000000]')} {params.data.dispense_unit}</div>;
			},
		},
		{
			headerName: "",
			flex: 1,
			minWidth: 75,
			cellClass: "custom-cell-button",
			cellRenderer: (props: CustomCellRendererProps) => {
				const showDelete = props?.data?.is_primary_drug !== "Yes" && !disabled;
				const showChevron = props?.data?.type != 'Billable' && ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(props?.data?.status);
				
				// If neither delete nor chevron should be shown, hide the content of the cell and collapse the space
				if (!showDelete && !showChevron) {
					return <div style={{ visibility: "hidden", height: 0, width: 0, padding: 0, margin: 0 }} />;
				}

				return (
					<div
						className="custom-cell-button"
						style={{
							padding: 16,
							display: "flex",
							flex: 1,
							minWidth: 75,
							justifyContent: "flex-end",
							alignItems: "center",
							height: "100%",
							gap: 8,
						}}
					>
						{showDelete ? (
							<img
								src={icons.deleteOutline}
								alt="Delete"
								className="custom-cell-button"
								style={{ padding: 8 }}
								onClick={(e) => {
									e.stopPropagation();
									deleteRow(props.data.id);
								}}
							/>
						) : null}

						{showChevron ? (
							<i
								style={{ color: "#837BB2", padding: 8 }}
								className={`custom-cell-button fa-solid fa-chevron-${props.node.expanded ? "up" : "down"}`}
								onClick={(e) => {
									e.stopPropagation();
									props.node.setExpanded(!props.node.expanded);
								}}
							></i>
						): null}
					</div>
				);
			},
		},
	]);

	useEffect(() => {
		handle.nodes()?.embed_item?.data("refresh", refresh);
		handle.nodes()?.embed_item?.data("barCodeScanner", barCodeScanner);
		handle.nodes()?.embed_item?.parent()?.hide();
	}, [handle, disabled]);

	return (
		<>
			<ThemeProvider theme={baseTheme}>
				<ToastContainer stacked position="bottom-center" theme="dark" />
				<div className="delivery-ticket-grid dsl-advanced-grid">
					<AgGridReact
						rowData={dtItems}
						rowClassRules={{
							"even-background": (params) => params.rowIndex % 2 === 1,
							"odd-background": (params) => params.rowIndex % 2 === 0,
						}}
						detailCellRenderer={WTItemDetail}
						detailCellRendererParams={{
							handle: handle,
							disabled: disabled,
							autoHeight: true,
							recalculatePullAllStatus: recalculatePullAllStatus,
							triggerParentFieldRefresh: triggerParentFieldRefresh,
						}}
						loading={loading}
						detailRowAutoHeight={true}
						ref={gridRef}
						onGridReady={onGridReady}
						onRowDoubleClicked={(params) => {
							const isClickFromCustomElement = (params.event?.target as HTMLElement)?.closest(
								".custom-cell-button"
							);
							if (isClickFromCustomElement) {
								return;
							}
							if (!params.data?.id) {
								return;
							}
							const dfd = window.Flyout.open({
								form: "careplan_dt_item",
								mode: disabled ? "read" : "edit",
								record: params.data.id,
								link: dd.options.link,
								links: dd.options.links,
								linkid: dd.options.linkid,
							});
							dfd.done(() => {
								triggerParentFieldRefresh();
								refresh();
							});
						}}
						suppressMenuHide={true}
						getRowId={(params) =>
							params.data.code +
							"_" +
							params.data.id +
							"_" +
							params.data.updated_on +
							"_" +
							params.data.created_on
						}
						columnDefs={columnDefs}
						domLayout="autoHeight"
						masterDetail={true}
						animateRows={true}
						defaultColDef={{
							sortable: false,
							filter: false,
							resizable: false,
							suppressHeaderMenuButton: true,
						}}
					/>
					<div className="delivery-ticket-grid-footer" style={{display: dd.value_field_get("status", true) === "order_ver" && dd.value_field_get("check_verify_warning", true) ? "flex" : "none"}}>
						<div className="delivery-ticket-error-message">
							<i className="fa-regular fa-triangle-exclamation"></i>
							All pulled items must be scanned and verified before saving.
						</div>
					</div>
				</div>
			</ThemeProvider>
		</>
	);
};
