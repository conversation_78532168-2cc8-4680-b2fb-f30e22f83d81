@import (reference) '../../less/style/main.less';
@import '../../components/nav-header/header.vars.less';

.snap {
    display: flex;
    flex-direction: column;
    flex: 1;

    .no-order {
        display: flex;
        flex-direction: column;
        flex: 1;
        justify-content: center;
        align-items: center;

        >h1 {
            font-size: 18px;
            font-weight: 600;
            color: #989898;
        }

        >img {
            height: 50px;
            width: 50px;
        }

        .patient-drawer-chevron {
            display: none;
            rotate: 270deg;
            cursor: pointer;
            position: absolute;
            top: 10;
            right: 10;
            width: 24px;
            height: 24px;
        }
    }

    .snap-view {
        display: flex;
        flex: 1;
        gap: 8px;

        .snap-view-area {
            display: flex;
            flex-direction: column;
            background-color: transparent;
            flex: 1;
            overflow-y: scroll;

            .snap-tab-content {
                display: flex;
                flex-direction: column;
                flex: 1;
                .card-four;
                min-height: 300px;
                border-radius: 12px;
                background-color: transparent;

                .dsl-container {
                    padding: 0px;
                    gap: 0px;
                }

                .dsl-list-bottom {
                    padding: 5px;
                }

                .dsl-snap-list {
                    .dsl-list-tab-container {
                        padding: 0px;
                        padding-top: 10px;

                        .dsl-list-top {
                            padding-bottom: 0px;
                        }
                    }

                    padding: 0px 20px 20px 20px;
                    min-height: 50%;
                    overflow: auto;

                    .dt-buttons {
                        display: none;
                    }
                }

                &.ovr {
                    background-color: transparent;

                    .dsl-container {
                        padding: 0px;
                        gap: 10px;
                    }
                }
            }
        }
    }


    @media screen and (max-width: 1200px) {

        // Tablet
        .no-order {

            .patient-drawer-chevron {
                display: block;
            }
        }
    }

}