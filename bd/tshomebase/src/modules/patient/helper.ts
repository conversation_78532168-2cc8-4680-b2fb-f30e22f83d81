import { request } from "@core/request";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { fetchFormFilters } from "@hooks/form-data";
import _ from "lodash";
import { TabData } from "@blocks/tab-list/tab-list";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { serverActionPerform } from "@utils/dsl-fx";

export const getCarePlanLinkMap = (linkMap: DSLDrawLinkMap) =>
	new Promise<DSLDrawLinkMap | { error: string }>(async (resolve, reject) => {
		linkMap = _.cloneDeep(linkMap);
		if (!linkMap.linkid.patient) {
			resolve({ error: "No Linked Patient Found" });
			return;
		}
		fetchFormFilters("careplan", {
			filter: {
				patient_id: linkMap.linkid.patient,
				status_id: [1, 2, 3, "null"],
			},
			sortProperty: "id",
			sortDirection: "desc",
			limit: 1,
		})
			.then((resp) => {
				if (resp.data.length) {
					linkMap.linkid.careplan = resp.data[0].id;
					linkMap.links.push("careplan");
					resolve({
						...linkMap,
					});
				} else {
					request({
						url: "/form/careplan/",
						method: "POST",
						data: {
							careplan_optout: "Opt-In",
							status_id: "3",
							patient_id: linkMap.linkid.patient,
						},
					})
						.then((resp) => {
							if (!resp.success) {
								resolve({ error: "Unable to Initiate Care Plan" });
							}
							if (Array.isArray(resp.data)) {
								linkMap.linkid.careplan = resp.data[0].id;
							} else {
								linkMap.linkid.careplan = resp.data.id;
							}
							linkMap.links.push("careplan");
							resolve({
								...linkMap,
							});
						})
						.catch((err) => {
							resolve({ error: "Unable to Initiate Care Plan" });
						});
				}
			})
			.catch((err) => {
				resolve({ error: "Unable to Initiate Care Plan" });
			});
	});

export const canCreateDeliveryTicket = (orderId: string | number) =>
	new Promise<boolean>(async (resolve, reject) => {
		if (!orderId) {
			resolve(false);
		}
		request({
			url: `/form/careplan_order/${orderId}?get_actions=true`,
			pool: true,
		})
			.then((resp) => {
				if (!resp.data.length) {
					resolve(false);
					return;
				}
				const actions = resp.data[0]._meta.actions || [];
				if (!Array.isArray(actions)) {
					resolve(false);
					return;
				}
				for (const action of actions) {
					if (action.action == "fill") {
						resolve(true);
						return;
					}
				}
				resolve(false);
			})
			.catch((err) => {
				resolve(false);
			});
	});

/**
 * Fetches the test claim payer information for a given patient.
 * @param {string | number} patientId - The ID of the patient.
 * @returns {Promise<boolean | { id: string | number }>} A promise that resolves to the insurance record if found, or false otherwise.
 */
export const fetchTestClaimPayer = (patientId: string | number) =>
	new Promise<boolean | { id: string | number }>(async (resolve, reject) => {
		if (!patientId) {
			resolve(false);
		}
		request({
			url: `/form/patient_insurance?filter=patient_id:${patientId}&filter=billing_method_id:ncpdp&filter=active:Yes&limit=1&sort=rank`,
			pool: true,
		})
			.then((resp) => {
				if (!resp.data.length) {
					resolve(false);
					return;
				}
				const insuranceRecord = resp.data[0] || null;
				if (!insuranceRecord) {
					resolve(false);
					return;
				}
				resolve(insuranceRecord);
			})
			.catch((err) => {
				console.error(`Error Fetching Test Claim Payer: ${err.message}`);
				resolve(false);
			});
	});

export const createDeliveryTicketOverride = async (tabData: TabData, tabActions: PatientTabActions) => {
	const { componentProps } = tabData;
	const { linkMap = {}, preset = {} } = componentProps as Record<string, any>;
	if (!linkMap) {
		window.prettyError("Error", "No Link Map Found");
		return;
	}
	if (!linkMap.linkid.order || !preset.order_id) {
		window.prettyError("No Referral Link Found");
		return;
	}
	let orderId = linkMap.linkid.order || preset.order_id;

	const can = await canCreateDeliveryTicket(orderId);
	if (!can) {
		window.prettyError("Error", "New fill cannot be created for this referral.");
		return;
	}

	const r = request({
		url: `form/careplan_order/${orderId}?perform_action=fill`,
		method: "GET",
	});
	window.prettyNotify("Generating New Fill.");
	r.then((resp) => {
		window.prettyNotify();
		if (resp.data?.error) {
			window.prettyError("Error generating new fill", resp.data?.error);
			return;
		} else if (resp.data) {
			serverActionPerform(resp.data, "careplan_order", linkMap, null, tabActions);
		}
	}).catch((resp) => {
		window.prettyNotify();
		window.prettyError("Error Fetching new fill presets", resp.data.response.data.error);
	});
};

export const createAssessment = async (tabData: TabData, tabActions: PatientTabActions) => {
	const { componentProps, form } = tabData;
	const { linkMap = {}, preset = {} } = componentProps as Record<string, any>;
	if (!linkMap) {
		window.prettyError("Error", "No Link Map Found");
		return;
	}
	if (!linkMap.linkid.order || !preset.order_id) {
		window.prettyError("No Referral Link Found");
		return;
	}
	let orderId = linkMap.linkid.order || preset.order_id;

	const can = await canCreateDeliveryTicket(orderId);
	if (!can) {
		window.prettyError("Error", "New assessment be created for this referral.");
		return;
	}

	const r = request({
		url: `/api/dispense?func=get_form_presets&order_id=${orderId}&form_name=${form}`,
		method: "GET",
	});
	window.prettyNotify("Generating New Assessment.");
	r.then((resp) => {
		window.prettyNotify();
		if (resp.data?.error) {
			window.prettyError("Error generating new assessment", resp.data?.error);
			return;
		} else if (resp.data) {
			serverActionPerform(resp.data, "careplan_order", linkMap, null, tabActions);
		}
	}).catch((resp) => {
		window.prettyNotify();
		window.prettyError("Error Fetching new fill presets", resp.data.response.data.error);
	});
};

/**
 * Creates a test claim override for a patient.
 * @param {TabData} tabData - The tab data containing component props.
 * @param {PatientTabActions} tabActions - The actions available for the patient tab.
 * @returns {Promise<void>}
 */
export const createTestClaimOverride = async (tabData: TabData, tabActions: PatientTabActions) => {
	const { componentProps } = tabData;
	const { linkMap = {}, preset = {} } = componentProps as Record<string, any>;
	if (!linkMap) {
		window.prettyError("Error", "No Link Map Found");
		return;
	}

	if (!linkMap.linkid.patient || !preset.patient_id) {
		window.prettyError("No Patient Link Found");
		return;
	}
	let patientId = linkMap.linkid.patient || preset.patient_id;

	const insuranceRecord = await fetchTestClaimPayer(patientId);
	if (!insuranceRecord || typeof insuranceRecord !== "object") {
		window.prettyError("Error", "Test Claim cannot be created. Cannot find pharmacy payer on patient record.");
		return;
	}

	const insuranceId = insuranceRecord?.id || null;
	const r = request({
		url: `form/patient_insurance/${insuranceId}?perform_action=gen_claim`,
		method: "GET",
	});
	window.prettyNotify("Generating Test Claim.");
	r.then((resp) => {
		window.prettyNotify();
		if (resp.data?.error) {
			window.prettyError("Error generating test claim", resp.data?.error);
			return;
		} else if (resp.data) {
			serverActionPerform(resp.data, "patient_insurance", linkMap, null, tabActions);
		}
	}).catch((resp) => {
		window.prettyNotify();
		window.prettyError("Error Generating Test Claim", resp.data.response.data.error);
	});
};
