import React from "react";
import "./patient-status-cell.less";
import { CustomCellRendererProps } from "ag-grid-react";

const PatientStatusCell = (props: CustomCellRendererProps) => {
	const { data } = props;
	if (!data || !data.status_id) {
		return null;
	}

	const statusMap: any = {
		"1": "Pending",
		"2": "Intake Complete",
		"3": "Active",
		"4": "On-Hold",
		"5": "Inactive",
		"6": "Discharged - Therapy Complete",
		"7": "Cancelled",
		"8": "Deceased",
	};
	const get_status = data && statusMap[data?.status_id];

	const getStatusClass = (item: any) => {
		switch (item) {
			case "Active":
				return "active";
			case "Pending":
				return "pending";
			case "On-Hold":
				return "on-Hold";
			case "Intake Complete":
				return "intake_complete";
			default:
				return "intake_complete";
		}
	};

	return (
		<div className={`status-column-grid ${getStatusClass(statusMap[data?.status_id])}`}>
			{get_status && <p>{get_status}</p>}
		</div>
	);
};

export default PatientStatusCell;
