.ag-body-viewport {
    .ag-pinned-right-cols-container {
        div[role="row"]:has(> div[col-id="status"]) {
            div[role="gridcell"] {
                display: flex;
                justify-content: flex-start;
            }
        }
    }
}

.ag-body-viewport {
    .ag-center-cols-container {
        div[role="row"]:has(> div[col-id="status"]) {
            div[role="gridcell"] {
                display: flex;
                justify-content: flex-start;
            }
        }
    }
}

.status-column-grid {

    padding: 2px 8px;
    border-radius: 16px;
    max-width: fit-content;

    p {
        font-size: 12px;
        line-height: 18px;
        font-weight: 700;
    }

    &.active {
        box-shadow: inset 0 0 0 2px #689989 !important;
        color: #689989;
        background-color: white;
    }
    &.on-Hold {
        background-color: #837BB2;
        color: white;
    }

    &.pending,
    &.intake_complete {
        background-color: #949491 !important;

        p {
            color: #FFFFFF !important;
        }
    }
}