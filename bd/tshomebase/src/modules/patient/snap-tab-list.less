@import "../../blocks/tab-list/tab-list.vars.less";
@import (reference) "../../less/style/main.less";

[data-module-id="patient"] {
  .snap {
    .tab-list-default {
      gap: 0px !important;

      [data-intersect-id="navigator-tab"] {
        padding: 0px !important;
        padding-right: 10px !important;
      }
    }
  }
}

.snap-main-tab-list {
  .tab-bar-lvl-three;
  background-color: transparent;
  flex-grow: 0;
  flex-shrink: 0;

  &.tab-add {
    background: transparent;
    color: @tab-btn-active-color;

    &::after {
      background: transparent !important;
      color: @tab-btn-active-color;
    }
  }

  .tab-list-button {
    align-self: end;
    align-items: center;
    height: 34px;

    &.tab-active {
      &::after {
        background: #c6ddf5;
        color: @tab-btn-active-txt-color;
      }
    }
  }

  > .tab-list-button {
    &:first-child {
      border-top-left-radius: @border-radius-8;
    }

    &:last-child {
      border: 0;
      font-size: 25px;
    }
  }

  .dynamic-tab-list {
    .tab-list-button {
      &:last-child {
        border-top-right-radius: @border-radius-8;
      }
    }
  }

  @media (max-width: 768px) {
    padding-right: 0% !important;
  }
}

.snap-tab-list {
  vertical-align: bottom;
  justify-content: flex-start;
  flex-grow: 0;
  flex-shrink: 0;
  height: 16px;

  .tab-list-button {
    height: 17px;
    color: #989898;
    align-self: center;
    padding-left: 10px;
    font-weight: 600;
    font-size: 14px;
    margin-right: 20px;
    align-items: center;
    gap: 8px;
    .tab-list-button-common;

    .tab-label {
      text-transform: uppercase;
      height: 16px;
    }

    .tab-avatar {
      height: 16px;
      width: 16px;

      &.active {
        filter: brightness(0) saturate(100%);
      }
    }

    &.tab-active {
      color: @purple;
    }
  }
}
