@import (reference) "../../less/style/main.less";


.tab-list-default.lvl-2-tab-list {
  .dynamic-tab-list {
    .tab-list-button {
      &.has-avatar {
        > div:first-child {
          display: none;
        }
      }
    }
  }
}

.patient-snapshot-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    padding: 10px;
    border-radius: 8px;
    background: #FAFAFAAA;
    border-width: 1px;
    border-style: solid;
    border-color: #FFFFFF;
    .inc-container{
      background-color: transparent;
      border-color: transparent;
      padding: 16px 8px;
      margin: 0 8px 8px;
    }
    // gap: 10px;
    .snap-tab-container {
      display: flex;
      flex-direction: row;
      height: 100%;
      width: 100%;
      padding: 8px;
      .snap-tab-list-container {
        flex-direction: column;
        height: 100%;
        overflow-y: scroll;
        min-width: 216px;
        padding: 0 16px 0 0;
        gap: 16px;
        backdrop-filter: blur(25px);
        .pat-snap-tabs{
          padding: 0px 0 8px 0;
          gap: 8px;
          .tab-item{
            display: flex;
            padding: 8px 4px;
            border-radius: 8px;
            gap: 5px;
            height: 40px;
            .tab-item-label{
              color: #5E636B;
              padding: 0px;
              font-weight: 500;
              img{
                filter: invert(38%) sepia(17%) saturate(236%) hue-rotate(178deg) brightness(95%) contrast(88%);
              }
            }
            &.active {
              display: flex;
              padding: 8px 4px;
              border-radius: 8px;
              gap: 5px;
              background: #FFFFFFBF;
              border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
              box-shadow: 0px 1px 2px -1px #0000000F, 0px 0px 1px 0px #00000014;
              position: relative;
            
            &::before {
              content: '';
              position: absolute;
              inset: 0;
              border-radius: 8px;
              padding: 1px;
              background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
              -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
              -webkit-mask-composite: xor;
              mask-composite: exclude;
              pointer-events: none;
            }

              .tab-item-label {
                  padding-left: 8px;
                  color: #668EBA;
                  font-weight: var(--font-weight-bold);
                  transition: all 0.5s ease;
                  img{
                    filter: invert(53%) sepia(84%) saturate(225%) hue-rotate(170deg) brightness(87%) contrast(86%);
                    transition: all 0.3s ease;
                  }
              }

              &::after {
                  content: '';
                  position: absolute;
                  right: -9px;
                  top: 0;
                  height: 100%;
                  width: 3px;
                  background-color: #668EBA;
                  border-radius: var(--radius-xsmall);
                  transition: all 0.3s ease;
              }
            }

            &.pat-alert{
              display: flex;

              .tab-item-label{
                color: #D98080;
                img{
                  filter: none;
                }
              }
            }
          }
        }
        .pat-tabs-divided{
          margin: 16px 0;
          padding: 8px 0 8px 0;
          gap: 8px;
          .tab-item{
            box-shadow: none;
            height: 44px;
            display: flex;
            padding: 8px;
            border-bottom: 1px solid #D7DAE0;
            border-radius: 8px 8px 0 0;
            &:last-child{
              border: none;
            }
            .tab-item-label{
              color: #5E636B;
              padding: 0px;
            }
            &.active {
              display: flex;
              gap: 5px;

              .tab-item-label {
                  color: #668EBA;
                  font-weight: var(--font-weight-bold);
                  padding-left: 8px;
                  transition: all 0.5s ease;
              }

              &::after {
                  content: '';
                  position: absolute;
                  right: -9px;
                  top: 0;
                  height: 100%;
                  width: 3px;
                  background-color: #668EBA;
                  border-radius: var(--radius-xsmall);
                  transition: all 0.5s ease;
              }
            }
          }
        }
        .pat-removable-tabs{
          gap: 8px;
          .dynamic-tab-item{
            display: flex;
            padding: 8px 4px;
            gap: 5px;
            height: 40px;
            border-bottom: 1px solid #D7DAE0;
            border-radius: 5px 5px 0 0;
            &:last-child{
              border: none;
            }
            div{
              color: #5E636B;
              font-weight: 500;
            }

            &.active {
              display: flex;
              gap: 5px;

              div{
                padding-left: 8px;
                transition: all 0.5s ease;
              }
            }
          }
        }
        .dynamic-tab-item {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 10px;
          border-radius: 5px;
          &.active {
            background-color: white;
            color: black;
          }
        }
      }
     
      .snap-tab-content-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
        .snap-tab-content-item {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
        }
      }
    }
}
  