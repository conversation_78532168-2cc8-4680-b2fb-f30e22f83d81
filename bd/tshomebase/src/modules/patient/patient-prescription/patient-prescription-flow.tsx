import React, { FC, useEffect, useMemo, useRef, useState } from "react";

import { AgGridReact } from "ag-grid-react";
import { useQueryData } from "@hooks/query";
import "../../../blocks/dsl-advanced-grid/dsl-advanced-grid.less";
import "./patient-prescription-flow.less";
import _ from "lodash";
import { Switch } from "@mui/material";
import { GetContextMenuItems, RowClickedEvent } from "ag-grid-enterprise";
import { PatientSnapshotComponentProps } from "@modules/patient/patient-snapshot/snap-comp";
import { GRID_CONFIG } from "@modules/patient/patient-prescription/patient-prescription-helper";
import { PersistentWindowEventCallback } from "@blocks/persistent-windows/types";
import { getAutoName } from "@utils/dsl-fx";
import { PatientOrderSelection } from "@components/popups/order-selection";
import { createPortalModal } from "@blocks/portal-modal/portal-modal";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { usePersistentWindowsStore } from "@blocks/persistent-windows/store";
import { useShallow } from "zustand/react/shallow";
import { DSLCardViewRef } from "@blocks/dsl-card-view/dsl-card-view";
import {
	fetchFormCount,
	fetchFormFilters,
	getFormDataWithServerActions,
	IFormData,
	useFormCount,
} from "@hooks/form-data";
import { useNavigation } from "@core/navigation";
import icons from "@public/icons/snap/patient";
import { ServerActionButtons } from "@blocks/dsl-card-view/dsl-card-action-btns";
import { openCreateViewDT, performAction } from "@modules/patient/patient-prescription/prescription-helper";
import LoaderNoData from "@components/common/loader-no-data";
import generalIcons from "@public/icons/general";
import { openActivePrescriptionSelection } from "@components/popups/active-prescription-selection";

export interface PrescriptionData {
	referral_id: number;
	patient_id: number;
	careplan_id: number;
	prescriber_id: number;
	territory_id: number | null;
	disp_status: string | null;
	inventory_id: number;
	inventory_id_auto_name: string;
	Therapy: string;
	sales_rep_id: number | null;
	site_id: number;
	team_id: number;
	form_name: string;
	form_id: number;
	order_format: string;
	"Pri Payer": string | null;
	Frequency: string | null;
	Dose: string | null;
	"Disp Qty": string | null;
	"Next Fill Date": string | null;
	"Next Delivery Date": string | null;
	Status: string | null;
	prescription_id: number | null;
	"Refills Remaining": number | null;
	hide_discontinued: string | null;
	one_time_only: string | null;
	auth_flag: string | null;
	bv_flag: string | null;
	status_id: string;
	path: string[];
	uniqueId: string;
	action?: string;
}

export const PatientPrescriptionFlow = (props: PatientSnapshotComponentProps) => {
	const { linkMap, snapInternalActions } = props;
	const patientId = linkMap.linkid?.patient;
	const addWindow = usePersistentWindowsStore(useShallow((state) => state.addWindow));
	const [showDiscontinued, setShowDiscontinued] = useState(false);
	const [activeOrderRx, refreshActiveOrderRx] = useFormCount("careplan_order_rx", {
		filter: {
			patient_id: patientId || "0",
			status_id: [1, 5],
			site_id: "!1000",
		},
		sortProperty: "id",
		sortDirection: "desc",
		limit: 1,
	});

	const nav = useNavigation(props, `/${patientId}/`);

	const gridRef = useRef<AgGridReact>(null);
	const [prescriptionSnapshot, refreshPrescriptionSnapshot] = useQueryData("prescription_snapshot", [], {
		extraParams: `filter=patient_id:${patientId}`,
	});

	const refreshAll = async () => {
		refreshPrescriptionSnapshot();
		refreshActiveOrderRx();
	};

	const updatePrescriptionActions = async (prx: PrescriptionData[]) => {
		const promises = [];
		for (const p of prx) {
			if (p.form_name == "careplan_order_rx" && p.form_id) {
				promises.push(getFormDataWithServerActions("careplan_order_rx", p.form_id));
			}
		}
		const rxActionsMap: Record<string, ServerActionButtons[]> = {};
		const data = await Promise.allSettled(promises);
		for (const p of data) {
			if (p?.status == "fulfilled" && p?.value?.data && p?.value?.data?.id) {
				const actions = p.value.serverActions || [];
				if (!rxActionsMap[p.value.data.id]) {
					rxActionsMap[p.value.data.id] = [];
				}
				const discontinueAction = actions.find((a) => (a.action as any) == "discontinue_rx");
				if (discontinueAction) {
					discontinueAction.style = "error";
					rxActionsMap[p.value.data.id].push(discontinueAction);
				}
				const fillAction = actions.find((a) => (a.action as any) == "fill_rx");
				if (fillAction) {
					fillAction.style = "primary";
					rxActionsMap[p.value.data.id].push(fillAction);
				}
			}
		}
		rowData.forEach((row) => {
			if (row.form_id && rxActionsMap[row.form_id] && row.form_name == "careplan_order_rx") {
				row.action = JSON.stringify(rxActionsMap[row.form_id]);
			}
		});
		gridRef.current?.api?.setGridOption?.("rowData", rowData || []);
		gridRef.current?.api?.refreshCells();
	};

	const getDataPath = (data: PrescriptionData) => {
		return data.path ?? [];
	};

	useEffect(() => {
		refreshAll();
	}, [showDiscontinued]);

	const onSaved: PersistentWindowEventCallback = (event) => {
		if (event?.type == "saved") {
			refreshAll();
		}
	};

	const rowData = useMemo(() => {
		const orgData = [...(prescriptionSnapshot.data || [])] as unknown as PrescriptionData[];
		if (!orgData || orgData.length == 0) {
			return [];
		}
		const existingTherapySets: Record<string, PrescriptionData> = {};

		let groupedData = orgData
			.map((prescription) => {
				if (!showDiscontinued && prescription.hide_discontinued) {
					return null;
				}
				prescription.uniqueId = `${prescription.patient_id}-${prescription.prescription_id}-${prescription.referral_id}-${prescription.order_format}`;
				return prescription;
			})
			.filter((prescription) => prescription !== null)
			.map((prescription) => {
				// Skip if hiding discontinued and item is discontinued
				if (prescription.order_format === "Therapy Set") {
					if (!existingTherapySets[prescription.referral_id]) {
						if (prescription.form_name != "careplan_order") {
							const main = orgData.find(
								(item) =>
									item.form_name == "careplan_order" && item.referral_id == prescription.referral_id
							);
							if (main) {
								existingTherapySets[prescription.referral_id] = main;
							} else {
								existingTherapySets[prescription.referral_id] = prescription;
							}
						} else {
							existingTherapySets[prescription.referral_id] = prescription;
						}
					}
					const existing = existingTherapySets[prescription.referral_id];
					if (existing.uniqueId == prescription.uniqueId) {
						prescription.path = [prescription.uniqueId];
					} else {
						prescription.path = [existing.uniqueId, prescription.uniqueId];
					}
				} else {
					prescription.path = [prescription.uniqueId];
				}

				return prescription;
			});
		groupedData = groupedData.map((pr) => {
			if (pr.order_format == "Supply Order") {
				const parentRx = groupedData.find(
					(p) =>
						p.prescription_id == pr.prescription_id &&
						p.order_format != "Supply Order" &&
						p.form_name == "careplan_order_rx"
				);
				if (parentRx) {
					pr.path = [...parentRx.path, ...pr.path];
				}
			}
			return pr;
		});

		return groupedData;
	}, [prescriptionSnapshot.data, showDiscontinued]);

	useEffect(() => {
		gridRef.current?.api?.setGridOption?.("rowData", rowData || []);
		updatePrescriptionActions(rowData);
	}, [rowData]);

	if (prescriptionSnapshot.failed) {
		return <div>Error loading prescriptions Unexpected Error</div>;
	}

	const onNewTherapySet = () => {
		snapInternalActions.addNew(
			{
				form: "careplan_order",
				...linkMap,
				linkMap: linkMap,
				gridRef: null,
				filtersPresetFixed: {
					order_format: "Therapy Set",
				},
			},
			{
				onEvent: onSaved,
				title: "New Therapy Set",
			}
		);
	};

	const onPrescriptionSaved: PersistentWindowEventCallback = (event) => {
		if (event?.type == "saved" && event.cardRef) {
			try {
				const card = event.cardRef?.cardform || event.cardRef?.cardread;
			} catch (error) {
				console.error("Error closing card", error);
			}
		}
		onSaved(event);
		if (!event.fd) {
			return;
		}
		if (event.fd.order_format == "Therapy Set") {
			const orders = event.fd.subform_order || [];
			if (orders.length == 0) {
				return;
			}
			const newOrderItem = window._.sortByOrder(event.fd.subform_order, "id", "desc")[0];
			openPrescriptionDuel(newOrderItem);
		} else if ((event.fd.subform_single_order || []).length > 0) {
			const newOrderItem = event.fd.subform_single_order[0];
			openPrescriptionDuel(newOrderItem);
		}
	};

	const openPrescriptionDuel = (orderItem?: IFormData) => {
		if (!orderItem) {
			console.log("no order item");
			return;
		}
		if (!orderItem.order_complete) {
			console.log("order not complete");
			return;
		}
		const rxNo = orderItem.rx_no;

		if (!rxNo) {
			console.log("no rx no");
			return;
		}
		fetchFormFilters("careplan_order_rx", {
			filter: {
				rx_no: rxNo,
			},
		})
			.then((r) => {
				if (r.success && r.data.length > 0) {
					const rx = r.data[0];
					const { id, auto_name } = rx;

					addWindow({
						type: "form",
						form: "careplan_order_rx",
						title: auto_name,
						wid: "",
						rid: "",
						card: "edit",
						record: id,
						gridRef: undefined,
						preset: {},
						subtitle: snapInternalActions.getPatientName(),
						onEvent: onSaved,
					});
				}
			})
			.catch((error) => {
				console.log("error fetching filters", error);
			});
	};

	const createPrescription = async (selectOrder: boolean) => {
		let orderId = "";
		let lm: DSLDrawLinkMap | null = _.cloneDeep(linkMap);

		if (selectOrder) {
			orderId = await new Promise((resolve, reject) =>
				createPortalModal(
					PatientOrderSelection as FC<unknown>,
					{},
					{ patientId: patientId, promise: { resolve, reject }, type: "therapy" }
				)
			);
			if (!orderId) {
				return;
			}
		}
		if (orderId) {
			lm.link = "order";
			lm.linkid["order"] = orderId;
			lm.links.push("order");
		}
		lm = await snapInternalActions.generateLinkMap("careplan_order", lm);
		if (!lm) {
			return;
		}
		let preset: Record<string, any> = {};
		for (const [key, value] of Object.entries(lm.linkid || {})) {
			if (!preset[key + "_id"]) {
				preset[key + "_id"] = value;
			}
		}
		// Single Prescription
		if (!orderId) {
			addWindow({
				type: "form",
				form: "careplan_order",
				title: "New Prescription",
				wid: "",
				rid: "",
				...(lm || {}),
				card: "addfill",
				gridRef: undefined,
				preset: {
					...preset,
					order_format: "Single Prescription",
					subform_single_order: [{}],
				},
				subtitle: snapInternalActions.getPatientName(),
				onEvent: onPrescriptionSaved,
			});
			return;
		}
		// Therapy Set
		const orderAutoName = await getAutoName("careplan_order", orderId);
		addWindow({
			type: "form",
			form: "careplan_order",
			title: orderAutoName,
			wid: "",
			rid: "",
			...(lm || {}),
			card: "edit",
			record: orderId,
			gridRef: undefined,
			preset: {},
			refCallback: (cardView) => {
				const idx = cardView.subscribe("ready", (ref: DSLCardViewRef) => {
					requestIdleCallback(() => {
						const subform = (cardView?.cardform?.ddf || cardView?.cardread?.ddr)?.options?.wrapper?.subforms
							?.formmap["careplan_order_item"].field_nodes.subform_order;
						if (subform) {
							window.TabController.goToField(subform);
							window.FieldSubform.add_row(subform, {}, { force_flyout: true });
						}
					});
					cardView.unsubscribe("ready", idx);
				});
			},
			subtitle: snapInternalActions.getPatientName(),
			onEvent: onPrescriptionSaved,
		});
	};

	const onNewPrescription = async () => {
		const ordersCounter = await fetchFormCount("careplan_order", {
			filter: { patient_id: patientId, order_format: "Therapy Set" },
		});
		if (ordersCounter.count == 0) {
			createPrescription(false);
			return;
		}
		window.prettyConfirm(
			"Prescription",
			"Creating a new prescription",
			"Select Referral",
			"Skip",
			(dlg) => {
				createPrescription(true);
			},
			(dlg) => {
				createPrescription(false);
			},
			window.BootstrapDialog.TYPE_SUCCESS
		);
	};

	const onRowClicked = async (event: RowClickedEvent<PrescriptionData>) => {
		const form_name = event.data?.form_name || "";
		const isClickFromCustomElement = (event.event?.target as HTMLElement)?.closest(".custom-cell-button");
		if (isClickFromCustomElement) {
			return;
		}
		if (!form_name) {
			return;
		}
		if (!window.DSL[form_name]) {
			return;
		}
		let mode = window.DSL[form_name].view.open || "read";
		if (mode == "edit" && window.Auth.can_update_any(form_name) && window.Auth.not_blocked_update(form_name)) {
			mode = "edit";
		} else {
			mode = "read";
		}
		if (event.data?.form_id) {
			const autoName = await getAutoName(event.data.form_name, event.data.form_id);
			snapInternalActions.rowClicked(
				{
					form: event.data.form_name,
					id: event.data.form_id + "",
					mode: mode,
					label: autoName,
					tkey: "",
					gridRef: null,
					onEvent: onSaved,
				},
				event,
				{ onEvent: onSaved }
			);
		} else if (event.data?.referral_id) {
			const autoName = await getAutoName("careplan_order", event.data.referral_id);
			snapInternalActions.rowClicked(
				{
					form: "careplan_order",
					id: event.data.referral_id + "",
					mode: mode,
					label: autoName,
					tkey: "",
					gridRef: null,
					onEvent: onSaved,
				},
				event,
				{ onEvent: onSaved }
			);
		}
	};

	const onDTCreate = () => {
		openCreateViewDT(patientId, {
			patientName: snapInternalActions.getPatientName() || getAutoName("patient", patientId),
			createViewDT: true,
			addWindow: addWindow,
			onSaved: onSaved,
			refreshAll: refreshAll,
		});
	};

	const onNewSupplyOrder = async () => {
		const prescriptionId = await openActivePrescriptionSelection(patientId);
		snapInternalActions.addNew(
			{
				form: "careplan_order",
				...linkMap,
				linkMap: linkMap,
				gridRef: null,
				filtersPresetFixed: {
					order_format: "Supply Order",
					subform_single_supply: [
						{
							associated_rx_id: prescriptionId || null,
						},
					],
				},
			},
			{
				onEvent: onSaved,
				title: "New Supply Order",
			}
		);
	};

	const getContextMenuItems: GetContextMenuItems = (params: any) => {
		const rowData = params.node?.data || {};
		const contextMenuItems = [];
		const onActionClick = (action: ServerActionButtons) => {
			const doAction = () => {
				performAction(action, rowData, linkMap, {
					addWindow: addWindow,
					patientName: snapInternalActions.getPatientName() || getAutoName("patient", patientId),
					onSaved: onSaved,
					refreshAll: refreshAll,
				});
			};
			if (action.action == ("discontinue_rx" as any)) {
				window.prettyConfirm(
					"Discontinue",
					"Do you want to discontinue this prescription?",
					"Discontinue",
					"Close",
					doAction,
					() => { },
					window.BootstrapDialog.TYPE_DANGER
				);
			} else {
				doAction();
			}
		};
		contextMenuItems.push({
			name: "Open",
			icon: '<i class="fa-solid fa-file-contract"></i>',
			cssClasses: ["context-menu-action"],
			action: () => {
				params.data = rowData;
				onRowClicked(params);
			},
		});
		if (!rowData.action) {
			return contextMenuItems;
		}
		try {
			const action = JSON.parse(rowData.action) || [];
			action.forEach((a: ServerActionButtons) => {
				const discontinue = (a.action as any) == "discontinue_rx";
				contextMenuItems.push({
					name: a.label,
					icon: discontinue ? '<i class="fa-solid fa-ban"></i>' : '<i class="fa-solid fa-mortar-pestle"></i>',
					cssClasses: [
						"context-menu-action",
						discontinue ? "context-menu-action-danger" : "context-menu-action-primary",
					],
					action: () => onActionClick(a),
				});
			});
		} catch (err) {
			console.error("Error parsing action", err);
		}
		return contextMenuItems;
	};

	return (
		<div className="prescriptions-grid ag-theme-alpine">
			<div className="upper-grid">
				<div className="grid-header">
					<div className="grid-header-title">
						Orders and Prescriptions
						<div className="count">{rowData.length}</div>
					</div>
					<div className="grid-header-explanation">
						<div className="grid-header-explanation-item">
							<img src={generalIcons.shieldBlocked} />
							<div>Auth Required</div>
						</div>

						<div className="grid-header-explanation-item">
							<img src={generalIcons.noCurrent} />
							<div>BV Required</div>
						</div>
						<div className="grid-header-explanation-item">
							<img src={generalIcons.oneTime} />
							<div>One-time Fill</div>
						</div>
					</div>
					<div className="show-discontinued">
						<span className="discontinued-title">Show Discontinued?</span>
						<Switch checked={showDiscontinued} onChange={(e) => setShowDiscontinued(e.target.checked)} />
						<div className="refresh-prescription" onClick={() => refreshAll()}>
							↻
						</div>
					</div>
				</div>
				<div className="dsl-advanced-grid">
					<AgGridReact
						ref={gridRef}
						loadingOverlayComponent={LoaderNoData}
						headerHeight={32}
						loadingOverlayComponentParams={{
							loading: true,
							containerStyle: {
								paddingTop: "40px",
							},
						}}
						columnDefs={GRID_CONFIG.columnDefs}
						autoSizeStrategy={{
							type: "fitGridWidth",
						}}
						rowData={rowData}
						loading={prescriptionSnapshot.loading}
						getContextMenuItems={getContextMenuItems}
						defaultColDef={GRID_CONFIG.defaultColDef as any}
						treeData={true}
						rowHeight={40}
						suppressRowTransform={true}
						animateRows={true}
						getRowHeight={(params) => {
							if (params.data?.order_format == "Therapy Set") {
								if (params.data?.path?.length == 1) {
									return 60;
								}
							}
							return null;
						}}
						rowClassRules={{
							"even-background": (params) => params.rowIndex % 2 === 1,
							"odd-background": (params) => params.rowIndex % 2 === 0,
						}}
						onRowClicked={onRowClicked}
						groupDefaultExpanded={-1}
						getDataPath={getDataPath}
						getRowId={(params) => {
							return params.data.uniqueId;
						}}
						autoGroupColumnDef={GRID_CONFIG.autoGroupColumnDef}
					/>
				</div>
			</div>
			<div className="grid-footer">
				<div className="dt-btn-container">
					{
						<div
							className={`dsl-action-btn wizard-bottom-btn-default btn-save  btn-save base-form-button base-style-info ${activeOrderRx == 0 ? " disabled" : ""
								}`}
							onClick={() => {
								if (activeOrderRx == 0) {
									return;
								}
								onDTCreate();
							}}
						>
							<div className="inner-cont">
								<p className="label">Create Delivery Ticket</p>
							</div>
						</div>
					}
				</div>
				<div className="action-buttons">
					{window.Auth.can_write_any("careplan_order") && <div
						className="dsl-action-btn wizard-bottom-btn-default btn-save  btn-save base-form-button base-style-primary"
						onClick={onNewTherapySet}
					>
						<div className="inner-cont">
							<img className="icon" src={generalIcons.doubleWindow} />
							<p className="label">New Therapy Set</p>
						</div>
					</div>}
					{
						<div
							className="dsl-action-btn wizard-bottom-btn-default btn-save  btn-save base-form-button base-style-info"
							onClick={onNewSupplyOrder}
						>
							<div className="inner-cont">
								<img className="icon" src={generalIcons.box} />
								<p className="label">New Supply Order</p>
							</div>
						</div>
					}

					<div
						className="dsl-action-btn wizard-bottom-btn-default btn-save  btn-save base-form-button"
						onClick={onNewPrescription}
					>
						<div className="inner-cont">
							<img className="icon" src={icons.patRx} />
							<p className="label">New Prescription</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
