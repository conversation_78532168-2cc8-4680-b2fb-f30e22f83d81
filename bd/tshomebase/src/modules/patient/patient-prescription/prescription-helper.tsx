import { FC } from "react";

import _ from "lodash";
import { PersistentWindowEventCallback } from "@blocks/persistent-windows/types";
import { getAutoName, getCurrentDateString, isTempId, normalizeNESEndpoint, serverActionPerform } from "@utils/dsl-fx";
import { createPortalModal } from "@blocks/portal-modal/portal-modal";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { PersistentWindowsState } from "@blocks/persistent-windows/store";
import { TabModes } from "@blocks/dsl-card-view/dsl-card-view";
import { fetchFormData, IFormData } from "@hooks/form-data";
import { PrescriptionDispenseSelection } from "@components/popups/prescription-selection";
import { CRRequestOptions, request } from "@core/request";
import { performInteractionsCheck } from "@dsl-form/util/interactions";
import { ServerActionButtons } from "@blocks/dsl-card-view/dsl-card-action-btns";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { OpenTabCallback } from "@hooks/form-tab-controller";
import { PrescriptionData } from "@modules/patient/patient-prescription/patient-prescription-flow";
import { toast } from "@components/toast";

type PrescriptionOptions = {
	patientName: string;
	onSaved?: PersistentWindowEventCallback;
	addWindow?: PersistentWindowsState["addWindow"];
	refreshAll?: () => void;
	createViewDT?: boolean;
};

export const confirmDeliveryTick = async (dtId: string | number) => {
	return new Promise(async (resolve, reject) => {
		window.prettyNotify("Confirming Delivery Ticket...");
		const fd = await fetchFormData("careplan_delivery_tick", dtId, true, "all");
		if (!fd?.data?.id) {
			window.prettyNotify();
			window.prettyError("Error", "Invalid Delivery Ticket ID");
			reject(false);
			return;
		}
		const dt = fd.data;
		if (dt.confirmed == "Yes") {
			window.prettyNotify();
			window.prettyError("Error", "Delivery Ticket already confirmed");
			reject(false);
			return;
		}
		const exceptions: IFormData[] = dt.subform_confirmation?.[0]?.subform_exceptions || [];
		if (exceptions.some((i) => i.resolved != "Yes")) {
			window.prettyNotify();
			window.prettyError("Error", "Delivery Ticket has unresolved exceptions");
			reject(false);
			return;
		}
		request({
			url: `/form/careplan_delivery_tick/${dtId}/`,
			method: "PUT",
			data: {
				...dt,
				confirmed: "Yes",
				confirmed_by: window.App.user.id,
				confirmed_datetime: getCurrentDateString(true),
			},
		})
			.then((res) => {
				window.prettyNotify();
				toast({
					message: "Delivery Ticket confirmed",
					type: "success",
				});
				resolve(true);
			})
			.catch((err) => {
				window.prettyNotify();
				console.error(err);
				const resp = err?.data?.response?.data;
				window.prettyError(
					"Unexpected Error",
					resp?.message || resp?.Error || resp?.error || "Unable to confirm delivery ticket"
				);
				reject(false);
			});
	});
};

export const openCareplanDeliveryTick = async (
	patientId: number | string,
	dtId: string | number,
	options: PrescriptionOptions
) => {
	if (!dtId) {
		console.error("No record found in careplan_delivery_tick");
		return;
	}
	const autoName = await getAutoName("careplan_delivery_tick", dtId);
	const patientName = options?.patientName || (await getAutoName("patient", patientId));
	options?.addWindow?.({
		type: "form",
		form: "careplan_delivery_tick",
		title: autoName,
		wid: "",
		rid: "",
		card: "edit",
		record: dtId,
		gridRef: undefined,
		preset: {},
		subtitle: patientName,
		onEvent: options?.onSaved,
	});
};

export const startDispense = async (
	patientId: number | string,
	rxId: number | string,
	options: PrescriptionOptions
) => {
	let rxIdToUse = rxId;
	if (!rxIdToUse) {
		const selectDispenseRx = (await new Promise((resolve, reject) =>
			createPortalModal(
				PrescriptionDispenseSelection as FC<unknown>,
				{},
				{ patientId: patientId, promise: { resolve, reject } }
			)
		)) as IFormData | null | undefined;
		if (!selectDispenseRx || !selectDispenseRx.rx_id) {
			return;
		}
		rxIdToUse = selectDispenseRx.rx_id;
	}
	if (!rxIdToUse) {
		return;
	}
	request({
		url: "/dispense/?func=create_dt_view_presets&rx_id=" + rxIdToUse,
	})
		.then((res) => {
			if (!res.data) {
				window.prettyError("Error", "Unable to create delivery ticket view presets");
				return;
			}
			const d = res.data;
			if (d.form == "careplan_delivery_tick") {
				openCareplanDeliveryTick(patientId, d.record, options);
			} else if (d.form == "view_create_dt") {
				openCreateViewDT(patientId, options, res?.data?.presets || {});
			}
		})
		.catch((err) => {
			console.log("err", err);
			window.prettyError("Error", "Unable to create delivery ticket view presets");
		});
};

export const openCreateViewDT = async (
	patientId: number | string,
	options: PrescriptionOptions,
	presets: Record<string, string> = {}
) => {
	const fd = await fetchFormData("patient", patientId, true, "all");
	if (!fd?.data?.id) {
		window.prettyError(
			"Error",
			"Unable to fetch patient data, Please try again. If the problem persists, contact support."
		);
		return;
	}
	options?.addWindow?.({
		type: "form",
		form: "view_create_dt",
		title: "Delivery Ticket Creation",
		wid: "",
		rid: "",
		card: "addfill",
		record: "",
		gridRef: undefined,
		preset: {
			from_ready_to_contact: options?.createViewDT ? null : "Yes",
			patient_id: patientId,
			site_id: fd.data.site_id,
			...presets,
		},
		subtitle: options?.patientName || getAutoName("patient", patientId),
		onEvent: async (e) => {
			if (e.type !== "saved") {
				return;
			}
			options?.onSaved?.(e);
			const rt = e.fd;
			if (!rt?.careplan_delivery_tick || !rt.record) {
				window.prettyError("Error", "Unable to create delivery ticket view presets");
				return;
			}
			openCareplanDeliveryTick(patientId, rt?.careplan_delivery_tick || rt?.record, options);
		},
	});
};

export const performAction = async (
	action: ServerActionButtons,
	record: PrescriptionData,
	linkMap: DSLDrawLinkMap,
	options: PrescriptionOptions
) => {
	if (action.show_loading_spinner) {
		window.prettyNotify(action.loading_spinner_text || "Performing action...");
	}
	if (record.form_name != "careplan_order_rx") {
		console.log("not a prescription");
		window.prettyNotify();
		return;
	}
	if (!record.form_id) {
		console.log("no form id");
		window.prettyNotify();
		return;
	}
	const fd = await fetchFormData("careplan_order_rx", record.form_id, true, "all");
	if (!fd?.data?.id) {
		console.log("no form data");
		window.prettyNotify();
		return;
	}
	if (action.label == "Fill Rx") {
		window.prettyNotify();
		startDispense(record.patient_id, record.form_id, options);
		return;
	}
	const requestConfig: CRRequestOptions = {
		url: action.callback_url
			? normalizeNESEndpoint(action.callback_url)
			: `/form/careplan_order_rx/${record.form_id}/?perform_action=${action.action}`,
		method: action.callback_type == "POST" ? "POST" : "GET",
		data: fd.data,
	};
	const openTab: OpenTabCallback = (id, label, mode, form, componentProps, renderComponent, extraKV) => {
		options?.addWindow?.({
			wid: "",
			rid: "",
			type: "form",
			form: form,
			title: label,
			record: isTempId(id) ? undefined : id,
			card: mode as TabModes,
			subtitle: options?.patientName || getAutoName("patient", record.patient_id),
			...componentProps,
			...extraKV,
			onEvent: (event) => {
				if (event.type == "saved") {
					console.log("Server Action Completed: Form Saved, Refreshing Prescriptions Grid");
				}
				options?.onSaved?.(event);
			},
		});
	};
	const actions: PatientTabActions = {
		refreshSnap: (a, b) => {
			options?.refreshAll?.();
			console.log("Server Action Completed: Refresh Prescriptions Grid");
		},
		openTab: openTab,
	} as PatientTabActions;

	request(requestConfig)
		.then((sr) => {
			if (sr.data) {
				const lm = {
					...linkMap,
				} as DSLDrawLinkMap;
				serverActionPerform(sr.data, "careplan_order_rx", lm, null, actions);
			} else {
				window.prettyError("Action Failed", "Unsupported action contact support.");
			}
		})
		.catch((error) => {
			console.error(error);
			window.prettyError("Action Failed", "Unexpected error occurred while performing action");
		})
		.finally(() => {
			window.prettyNotify();
		});
};
