@import (reference) "../../../less/style/main.less";

.prescriptions-grid {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	min-height: 0;
	gap: 10px;
	.refresh-prescription {
		content: "\f2f9" !important;
		.font-icon;
	}
	.actions-container {
		display: flex;
		flex-direction: row;
		gap: 10px;
		align-items: center;
		justify-content: flex-start;
	}

	.btn-action-rx {
		color: white;
		padding: 4px 10px;
		border-radius: 8px;
		gap: 4px;
		height: 30px;
		border: none;
		cursor: pointer;
		background: #837bb2;
		color: white;
		box-shadow: 0px -1px 2px 0px #00000038 inset, 0px 1px 2px 0px #ffffff1f inset;
		font-weight: 500;
		font-size: 12px;
		line-height: 18px;
		&:disabled {
			background: #e0e0e0;
			color: #80807d;
			cursor: not-allowed;
		}
		&.danger {
			background: #e58787;
		}
	}

	.filter-act-btn {
		display: flex;
		user-select: none;
		flex-direction: row;
		align-items: center;
		background: #f8f5fc;
		cursor: pointer;
		border-radius: @border-radius-full;
		i {
			font-size: 18px;
			color: var(--color-primary);
		}
	}

	.filter-collapsed {
		// border-radius: 35px;

		.add-btn {
			cursor: pointer;

			height: 36px;
			gap: 4px;
			padding-top: 8px;
			padding-right: 12px;
			padding-bottom: 8px;
			padding-left: 12px;
			border-radius: 8px;
			background: var(--color-primary);
			box-shadow: 0px -1px 2px 0px #00000038 inset;
			box-shadow: 0px 1px 2px 0px #ffffff1f inset;

			font-weight: 500;
			font-size: 14px;
			line-height: 20px;
			letter-spacing: 0%;
			color: #ffffff;
		}
	}
	.upper-grid {
		border: 1px solid #d1d1cd;
		border-radius: 12px;
		flex: 1;
		display: flex;
		flex-direction: column;
		min-height: 0;

		.grid-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0px 16px;
			height: 46px;
			flex-shrink: 0;

			.grid-header-title {
				font-weight: 500;
				font-size: 14px;
				line-height: 20px;
				letter-spacing: 0px;
				color: #838894;
				display: flex;
				flex-direction: row;
				align-items: center;

				.count {
					width: 24;
					font-family: Inter;
					font-weight: 700;
					font-size: 12px;
					line-height: 18px;
					letter-spacing: 0px;
					text-align: center;
					color: #5e636b;
				}

				.discontinued-title {
					font-weight: 700;
					font-size: 12px;
					line-height: 18px;
					color: #5e636b;
				}
			}

			.grid-header-explanation {
				display: flex;
				flex-direction: row;
				gap: 4px;
				.grid-header-explanation-item {
					display: flex;
					flex-direction: row;
					align-items: center;
					height: 22px;
					gap: 6px;
					padding-top: 2px;
					padding-right: 6px;
					padding-bottom: 2px;
					padding-left: 6px;
					border-radius: 8px;
					img {
						width: 16px;
						height: 16px;
					}
					div {
						font-weight: 400;
						font-size: 12px;
						line-height: 18px;
						letter-spacing: 0%;
						text-align: center;
						color: #838894;
						height: 18px;
					}
				}
			}

			.show-discontinued {
				display: flex;
				align-items: center;

				font-family: Inter;
				font-weight: 500;

				line-height: 18px;
				letter-spacing: 0px;
				vertical-align: middle;

				span {
					font-size: 12px;
					color: #5e636b;
				}

				.MuiSwitch-root {
					width: 44px;
					height: 24px;
					padding: 0;
					margin: 8px;
					.MuiSwitch-track {
						background-color: var(--color-text-100);
						border-radius: 13px;
						opacity: 1;
						transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1);
					}
				}

				.MuiSwitch-switchBase {
					padding: 0;
					margin: 2px;
					transition-duration: 300ms;

					&.Mui-checked {
						transform: translateX(18px);
						color: #fff;

						+ .MuiSwitch-track {
							background-color: #837bb2;
							opacity: 1;
							border: 0;
						}

						.MuiSwitch-thumb {
							background-color: #fff;
						}
					}
				}

				.MuiSwitch-thumb {
					box-sizing: border-box;
					width: 20px;
					height: 20px;
					background-color: #fff;
				}
			}
		}

		.dsl-advanced-grid {
			flex: 1;
			min-height: 0;
			height: 100%;
			.ag-header,
			.ag-header-container,
			.ag-header-row,
			.ag-header-cell,
			.ag-header-group-cell,
			.ag-header-cell-comp-wrapper {
				border-radius: 0 !important;
			}
			.ag-header-cell-menu-button {
				display: none;
			}

			.default-style {
				padding-left: 0;
			}
			.ag-group-expanded {
				display: none;
			}
			.ag-row-group-leaf-indent {
				margin-left: 0px;
			}

			.ag-root-wrapper {
				border: none;
				border-radius: 12px;
				border-top-left-radius: 0px;
				border-top-right-radius: 0px;
				height: 100%;
			}

			.header-style {
				font-family: "Inter";
				color: #80807d;
				font-size: 12px;
				line-height: 18px;
			}

			.sorted-column {
				font-family: "Inter";
				color: #5e636b;
				font-size: 14px;
				font-weight: 500;
				line-height: 20px;
				.ag-icon {
					display: none;
				}
			}

			.unsorted-columns {
				font-weight: 400;
			}
		}

		.status-badge {
			border-radius: 16px;
			border: 2px solid #689989;
			display: flex;
			align-items: center;
			justify-content: center;
			max-width: fit-content !important;
			height: 22px;
			padding: 2px 8px;

			p {
				font-family: Inter;
				font-weight: 700;
				font-size: 12px;
				line-height: 18px;
				letter-spacing: 0;
				text-align: center;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}

			&.active {
				color: #689989;
			}

			&.pending {
				background-color: #949491;
				color: #ffffff;
				border: 2px solid #949491;
			}
		}
	}

	.grid-footer {
		display: flex;
		justify-content: space-between;
		padding-left: 8px;
		padding-right: 8px;
		flex-shrink: 0;
		.dt-btn-container{
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.action-buttons {
			display: flex;
			gap: 16px;
			.dsl-action-btn{
				height: 40px !important;
				.icon{
					width: 12px !important;
					height: 12px !important;
				}
			}
		}

		.start-dispense {
			color: white;
			padding: 8px 12px;
			border-radius: 8px;
			gap: 4px;
			height: 36px;
			border: none;
			cursor: pointer;
			background: #837bb2;
			color: white;
			box-shadow: 0px -1px 2px 0px #00000038 inset, 0px 1px 2px 0px #ffffff1f inset;
			font-weight: 500;
			font-size: 14px;
			line-height: 20px;
			&:disabled {
				background: #e0e0e0;
				color: #80807d;
				cursor: not-allowed;
			}
		}

		.new-therapy,
		.new-prescription {
			background: #fafafa;
			padding: 10px 14px;
			gap: 4px;
			border: none;
			border-radius: 8px;
			height: 40px;
			box-shadow: 0px -1px 2px 0px #00000038 inset, 0px 1px 2px 0px #ffffff1f inset;
			font-weight: 500;
			font-size: 14px;
			line-height: 20px;
			color: #5e636b;
			img {
				margin-right: 8px;
			}
		}
	}
}

.therapy-set-header {
	display: flex;
	align-items: center;
	padding: 8px 0;
	gap: 4px;
	.heading-section {
		display: flex;
		flex-direction: column;
		gap: 4px;
		.therapy-set-header-title {
			font-weight: 500;
			color: #5e636b;
			font-size: 14px;
			line-height: 20px;
		}
		.therapy-set-header-count {
			font-weight: 400;
			color: #838894;
			font-size: 14px;
			line-height: 20px;
		}
	}
}
