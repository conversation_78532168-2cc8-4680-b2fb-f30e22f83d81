import { ColDef } from "ag-grid-enterprise";
import { PrescriptionData } from "./patient-prescription-flow";
import "./patient-prescription-flow.less";
import icons from "@public/icons/snap/patient";
import { DynamicStatusCell } from "@components/dynamic-cell-status/dynamic-status-cell";
import general from "@public/icons/general";

const columnDefs: ColDef<PrescriptionData>[] = [
	{
		field: "Disp Qty",
		headerName: "Dispense",
		cellStyle: {
			textAlign: "right",
		},
	},
	{
		field: "Dose",
		headerName: "Unit",
	},
	{
		field: "Frequency",
		headerName: "Frequency",
	},
	{
		field: "Next Fill Date",
		headerName: "Next Fill",
		cellRenderer: (params: any) => {
			const nextFillDate = params?.data?.["Next Fill Date"];
			if (!nextFillDate) {
				return;
			}
			return <span>{window.moment(nextFillDate).format("MMM, DD, YYYY")}</span>;
		},
		valueGetter: (params: any) => {
			if (!params?.data?.["Next Fill Date"]) {
				return null;
			} else {
				return window.moment(params?.data?.["Next Fill Date"]).format("YYYY-MM-DD");
			}
		},
	},
	{
		field: "Next Delivery Date",
		headerName: "Next Delivery",
		cellRenderer: (params: any) => {
			const date = params?.data?.["Next Delivery Date"];
			if (!date) return null;
			return <span>{window.moment(date).format("MMM, DD, YYYY")}</span>;
		},
		valueGetter: (params: any) => {
			if (!params?.data?.["Next Delivery Date"]) {
				return null;
			} else {
				return window.moment(params?.data?.["Next Delivery Date"]).format("YYYY-MM-DD");
			}
		},
	},
	{
		field: "Refills Remaining",
		headerName: "Refills Remaining",
		cellStyle: {
			textAlign: "right",
		},
	},
	{
		field: "Status",
		headerName: "Status",
		cellStyle: {
			alignContent: "center",
		},
		valueGetter: (params: any) => {
			return params?.data?.["Status"];
		},
		cellRenderer: (params: any) => {
			const status = params?.data?.["Status"];
			if (!status) {
				return;
			}
			return <div className={`status-badge ${status?.toLowerCase()}`}><p>{status}</p></div>;
		},
	},
	{
		field: "disp_status",
		headerName: "Dispense Status",
		cellStyle: {
			alignContent: "center",
		},
		valueGetter: (params: any) => {
			return params?.data?.disp_status || null;
		},
		cellRenderer: (params: any) => {
			const status = params?.data?.disp_status;
			if (!status) {
				return;
			}
			return (
				<DynamicStatusCell
					value={params?.data?.disp_status}
					statusMap={{
						"Order Entry/Setup": "grey",
						"Claims to Adjudicate": "grey",
						"Test Claim": "grey",
						"Rx Verification": "grey",
						"Ready to Contact": "green",
						"Ready to Refill": "green",
						"Print Labels / Fill Rx": "grey",
						"Order Verification": "grey",
						"Delivery Ticket Confirmation": "grey",
						Confirmed: "green",
						Void: "red",
					}}
				/>
			);
		},
	},
	{
		field: "Pri Payer",
		headerName: "Billing Method",
	},
];

const defaultColDef = {
	sortable: true,
	resizable: true,
	filter: false,
	sortingOrder: ["asc", "desc", null],
	floatingFilter: false,
	enableCellChangeFlash: false,
	cellClass: (params: any) => {
		const isSorted = params.column.getSort();
		const baseClass = "";
		return isSorted === "asc" || isSorted === "desc" ? `${baseClass} sorted-column` : "unsorted-columns";
	},
	headerClass: () => {
		return "header-style";
	},
} as ColDef<PrescriptionData>;

const autoGroupColumnDef: ColDef<PrescriptionData> = {
	headerName: "Drug",
	flex: 1,
	minWidth: 300,
	maxWidth: 700000,
	sort: "asc",
	field: "inventory_id_auto_name",
	sortable: true,
	cellRenderer: "agGroupCellRenderer",
	valueGetter: (params: any) => {
		return params?.data?.inventory_id_auto_name || null;
	},
	cellRendererParams: {
		suppressCount: true,
		suppressDoubleClickExpand: true,
		innerRenderer: (params: any) => {
			const { data, node } = params;
			const { treeNode } = node;
			if (treeNode.level == 0 && data?.order_format == "Therapy Set") {
				const childrenCount = treeNode.children?.size || 0;
				return (
					<div className={`therapy-set-header ${childrenCount > 0 ? " has-children" : ""}`}>
						<img src={icons.copy} />
						<div className="heading-section">
							<div
								className="therapy-set-header-title"
								style={{
									height: 20,
									marginRight: 8,
								}}
							>
								{data.inventory_id_auto_name}
							</div>
							<div className="therapy-set-header-count">{childrenCount} Prescriptions</div>
						</div>
					</div>
				);
			} else {
				let icon = null;
				if (treeNode.level == 0) {
					if (data.order_format == "Supply Order") {
						icon = general.tag;
					} else if (data.form_name == "careplan_order_rx") {
						icon = icons.patRx;
					} else if (data.form_name == "careplan_order_supply") {
						icon = general.tag;
					}
				} else if (treeNode.level > 0) {
					icon = general.separator;
				}
				return (
					<span className="td-drug-name">
						{icon ? (
							<img
								src={icon}
								style={{
									height: 13,
									marginRight: 8,
								}}
							/>
						) : null}
						{data.inventory_id_auto_name}
						{data.auth_flag == "Yes" ? (
							<img
								src={general.shieldBlocked}
								style={{
									height: 13,
									marginLeft: 8,
								}}
							/>
						) : null}
						{data.one_time_only == "Yes" ? (
							<img
								src={general.oneTime}
								style={{
									height: 13,
									marginLeft: 8,
								}}
							/>
						) : null}
						{data.bv_flag == "Yes" ? (
							<img
								src={general.noCurrent}
								style={{
									height: 13,
									marginLeft: 8,
								}}
							/>
						) : null}
					</span>
				);
			}
		},
	},
};
export const GRID_CONFIG = { columnDefs, defaultColDef, autoGroupColumnDef };
