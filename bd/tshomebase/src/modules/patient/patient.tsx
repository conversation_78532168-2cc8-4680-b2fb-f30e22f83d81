import React, { useEffect } from "react";
import "./patient.less";
import "./snap-tab-list.less";
import type { FC } from "react";
import _ from "lodash";
import { RoutedComponentProps } from "@typedefs/routed-component";
import { useNavigation } from "@core/navigation";
import { DSLTabView } from "@blocks/dsl-tab-view/dsl-tab-view";
import { DSLListView } from "@blocks/dsl-list-view/dsl-list-view";
import { PatientSnapshotView } from "@modules/patient/patient-snapshot";
import DSLViewStatus from "@blocks/dsl-list-view/dsl-list-view-status";
import PatientStatusCell from "./patient-status/patient-status-cell";
import { fetchFormData } from "@hooks/index";

interface PatientProps extends RoutedComponentProps {
	setActiveTab: (module: string) => void;
}

export const Patient: FC<PatientProps> = (props) => {
	const enableHistory = true;

	const nav = useNavigation(props, "/patient");

	useEffect(() => {
		if (props.navToURL?.startsWith("/patient")) {
			props.setActiveTab("patient");
		}
	}, [nav.globalNav.lrct]);

	const getIdFromURL = (url: string) => {
		const parsedUrl = url.split("/");
		const id = parsedUrl[2];
		if (parsedUrl[parsedUrl.length - 1] != "add" && id != "0") {
			return id;
		}
		return null;
	};

	const onTabOpen = (path: string) => {
		const id = getIdFromURL(path);
		if (!id) return;
		nav.pushToHistory("patient", id, path);
	};

	const onTabClose = (id: string) => {
		nav.removeFromHistory("patient", id);
	};

	return (
		<DSLTabView
			{...props}
			CustomListView={DSLListView}
			customAutoName={(event) => {
				if (event.rowData && event.rowData?.lastname && event.rowData?.firstname) {
					return window.joinValid(
						[event.rowData?.lastname as string, event.rowData?.firstname as string],
						", "
					);
				}
				return event.label;
			}}
			initialAsAvatar={true}
			newFormOverride="view_create_patient"
			customListViewProps={{
				canPrint: false,
				canDownload: false,
				findWidgets: [
					{
						component: DSLViewStatus,
						id: "status",
						componentProps: { form: "patient", field: "status_id" },
					},
				],
			}}
			overrides={{
				onNavChange: (navPath, tabViewActions, props) => {
					if (!navPath.length) {
						return;
					}
					const id = parseInt(navPath[0]);
					if (isNaN(id)) {
						return;
					}
					fetchFormData("patient", id).then((resp) => {
						if (resp.success) {
							const patientData = resp.data;
							const autoName = window.joinValid([patientData?.lastname, patientData?.firstname], ", ");
							tabViewActions.openTab(id, autoName, "snap", "patient", {});
						}
					});
				},
			}}
			styles={{
				tabListStyle: "lvl-2-tab-list",
			}}
			customColumnDefs={[
				{
					field: "status",
					headerName: "Status",
					pinned: "right",
					type: "text",
					suppressSizeToFit: true,
					cellRenderer: PatientStatusCell,
					filter: false,
					resizable: false,
				},
			]}
			{...nav}
			form="patient"
			viewMode="snap"
			defaultReadMode="snap"
			tabLabel="List"
			ops={{ enableHistory, onTabOpen, onTabClose }}
			tabRenderComponents={{
				always: PatientSnapshotView,
			}}
		/>
	);
};
