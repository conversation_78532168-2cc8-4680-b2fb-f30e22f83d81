@import (reference) "../../../less/style/main.less";

.detail-packet {
  display: flex;
  flex-direction: column;
  flex: 1;
  flex-basis: 90px;
  color: #283742;

  .label {
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    padding: 0px;
    color: #283742;
  }

  .value {
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    color: #68707f;
  }
}

.wgt-content {

  &.prescriber,
  &.insurance {
    padding: 8px !important;
    gap: 0px !important;

    .wgt-sub-container:not(.wscp) {
      padding: 8px !important;
    }
  }

  .bullet {
    li {
      &:hover {
        background-color: fade(@sky-blue, 10%);
        @color: @black;
        border-radius: 4px;
      }
    }
  }
}

.patient-detail-packet {
  display: flex;
  flex-direction: row;
  flex: 1;
  flex-basis: 45%;
  justify-content: flex-start;
  gap: 5px;

  &.fw {
    flex-basis: 100%;
  }

  p {
    font-size: 12px;
    font-weight: 500;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0;
  }

  .icon-title {
    display: flex;
    gap: 5px;
    align-items: flex-start;

    img {
      width: 18px;
      height: 18px;
    }

    p.title {
      color: @purple;
      font-size: 14px;
      font-weight: 600;
      align-self: center;
    }

    &.clickable {}
  }

  .label {
    color: #5a5958;
  }

  .value {
    color: #111111;
  }
}


.measurement-log {
  display: flex;
  flex-direction: column;

  .ec-packet-poriton {
    .pdp-content {
      p {
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      align-items: flex-start;
      flex: 1;
      gap: 10px;
    }
  }
}

.detail-packet-inline {
  display: flex;
  flex-direction: row;
  flex-basis: 100%;
  color: #283742;

  .label {
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0em;
    width: 50%;
    text-align: left;
    padding: 0px;
    color: #283742;
  }

  .value {
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;
    width: 50%;

    letter-spacing: 0em;
    text-align: left;
    color: #68707f;
  }
}

.wgt-table,
.wgt-sub-container {
  &.hovering {
    background-color: fade(@sky-blue, 10%);
    border-radius: 4px;

    .title {
      color: @black !important;
    }

    img {
      filter: brightness(0) saturate(100%) invert(0%) sepia(4%) saturate(679%) hue-rotate(356deg) brightness(95%) contrast(87%) !important;
    }
  }
}

.detail-packet-flex {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 5px 0px;
  flex: 1 1;
  gap: 8px;

  .label {
    padding-right: 5px;
    line-height: 14px;
    color: @charcoal-gray;
  }

  .value {
    line-height: 14px;
  }
}

.detail-heading {
  .value {
    color: @purple;
    font-weight: 600;
    cursor: pointer;
  }
}

.txt-align-center {
  text-align: center !important;
}

.cr-pointer {
  cursor: pointer;
}

.phy-tab-col-1 {
  .pellipsis {
    .title {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      margin: 0;
      width: 140px;
    }
  }
}

.phy-tab-col-2 {
  display: flex;
  font-weight: normal !important;
  color: @black !important;
  width: 115px;
  text-align: end;
  justify-content: flex-end;

  >div {
    display: flex;
    justify-content: flex-start;
  }
}

.bullet {
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 5px;

  li {
    list-style-type: disc;
    padding: 2px 6px;
    color: @black;
  }
}

@import (reference) "../../../less/style/main.less";

.detail-packet {
  display: flex;
  flex-direction: column;
  flex: 1;
  flex-basis: 90px;
  color: #283742;

  .label {
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    padding: 0px;
    color: #283742;
  }

  .value {
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    color: #68707f;
  }
}

.wgt-content {

  &.prescriber,
  &.insurance {
    padding: 8px !important;
    gap: 0px !important;

    .wgt-sub-container:not(.wscp) {
      padding: 8px !important;
    }
  }

  .bullet {
    li {
      &:hover {
        background-color: fade(@sky-blue, 10%);
        @color: @black;
        border-radius: 4px;
      }
    }
  }
}

.patient-detail-packet {
  display: flex;
  flex-direction: row;
  flex: 1;
  flex-basis: 45%;
  justify-content: flex-start;
  gap: 5px;

  &.fw {
    flex-basis: 100%;
  }

  p {
    font-size: 12px;
    font-weight: 500;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0;
  }

  .icon-title {
    display: flex;
    gap: 5px;
    align-items: flex-start;

    img {
      width: 18px;
      height: 18px;
    }

    p.title {
      color: #9974ce;
      font-size: 14px;
      font-weight: 600;
      align-self: center;
    }

    &.clickable {}
  }

  .label {
    color: #5a5958;
  }

  .value {
    color: #111111;
  }
}

.emergency-contact {
  display: flex;
  flex-direction: column;

  .ec-title {
    display: flex;
    flex-direction: row;
    gap: 10px;
    text-align: left;
    align-items: center;
    width: 100%;
    justify-content: flex-start;
    gap: 10px;

    p,
    div {
      width: 48%;
    }

    p {
      text-align: left;
    }

    .rel-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .ec-value {
    font-size: 16px;
    color: #111111;
    text-align: center;
  }

  .ec-status {
    background-color: #f2f2f2;
    padding: 2px 12px;
    border-radius: 6px;
    color: #58505b;
    font-size: 14px;
    width: auto;
    width: fit-content !important;
  }

  .ec-packet-poriton {
    .pdp-content {
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      align-items: flex-start;
      flex: 1;
      gap: 10px;
    }
  }
}

.detail-packet-inline {
  display: flex;
  flex-direction: row;
  flex-basis: 100%;
  color: #283742;

  .label {
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0em;
    width: 50%;
    text-align: left;
    padding: 0px;
    color: #283742;
  }

  .value {
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;
    width: 50%;

    letter-spacing: 0em;
    text-align: left;
    color: #68707f;
  }
}

.wgt-table,
.wgt-sub-container {
  &.hovering {
    background-color: fade(@sky-blue, 10%);
    border-radius: 4px;

    .title {
      color: @black !important;
    }

    img {
      filter: brightness(0) saturate(100%) invert(0%) sepia(4%) saturate(679%) hue-rotate(356deg) brightness(95%) contrast(87%) !important;
    }
  }
}

// .detail-packet-flex {
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   padding: 5px 0px;
//   flex: 1 1;

//   .label {
//     padding-right: 5px;
//     line-height: 14px;
//   }

//   .value {
//     line-height: 14px;
//   }
// }

.detail-heading {
  .value {
    color: @purple;
    font-weight: 600;
    cursor: pointer;
  }
}

.txt-align-center {
  text-align: center !important;
}

.cr-pointer {
  cursor: pointer;
}

.phy-tab-col-1 {
  .pellipsis {
    .title {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      margin: 0;
      width: 140px;
    }
  }
}

.phy-tab-col-2 {
  display: flex;
  font-weight: normal !important;
  color: @black !important;
  width: 115px;
  text-align: end;
  justify-content: flex-end;

  >div {
    display: flex;
    justify-content: flex-start;
  }
}

.bullet {
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 5px;

  li {
    list-style-type: disc;
    padding: 2px 6px;
    color: @black;
    text-transform: lowercase;

    &::first-letter {
      text-transform: uppercase;
    }
  }
}