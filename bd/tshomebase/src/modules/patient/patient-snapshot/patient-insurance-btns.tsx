import React from "react";
import "./patient-insurance-btns.less";
import { DSLListWidgetProps } from "@blocks/dsl-list-view/dsl-list-view";
import { request } from "@core/request";

export const PatientInsuranceBtns: React.FC<DSLListWidgetProps> = (props) => {
	const patientId = props.linkMap?.linkid?.patient;
	if (!patientId) {
		return null;
	}
	const cardFinder = async () => {
		try {
			const resp = await request({
				url: `/changehealth/cardfinder/?patient_id=${patientId}`,
			});
			if (resp.data) {
				window.Flyout.open({
					form: "card_finder_req",
					card: "addfill",
					preset: resp.data,
					form_override_url: "/changehealth/cardfinder",
				}).done((data) => {
					window.Flyout.open({
						form: "ncpdp_response_cf_summary",
						card: "edit",
						record: data.id,
					});
				});
			}
		} catch (error) {
			console.error(error);
		}
	};

	const eligibilityCheck = async () => {
		alert("pVerify Pending...");
	};

	return (
		<div className="list-insurance-btns">
			{/* <div className="add-btn" onClick={() => eligibilityCheck()}>
				<i className="fa-solid fa-house-medical-circle-check"></i>
				<span className="btn-label">Medical Benefits Check</span>
			</div> */}
			<div className="add-btn" onClick={() => cardFinder()}>
				<i className="fa-solid fa-id-card"></i>
				<span className="btn-label">Card Finder</span>
			</div>
		</div>
	);
};
