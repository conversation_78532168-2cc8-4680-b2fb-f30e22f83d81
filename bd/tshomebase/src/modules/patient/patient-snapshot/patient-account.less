@import (reference) "../../../less/style/main.less";

.skeleton-pulse {
	display: inline-block;
	height: 1em;
	position: relative;
	overflow: hidden;
	background-color: #ededed;
	border-radius: 4px;

	&::after {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		transform: translateX(-100%);
		background: linear-gradient(
			90deg,
			rgba(255, 255, 255, 0) 0%,
			rgba(255, 255, 255, 0.2) 20%,
			rgba(255, 255, 255, 0.5) 60%,
			rgba(255, 255, 255, 0)
		);
		animation: shimmer 2s infinite;
		content: "";
	}

	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
}

.patient-account {
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	gap: 16px;
	padding: 16px;
	border-radius: 0 0 10px 10px !important;

	.dashboard-section {
		display: flex;
		.section-title {
			font-weight: 500;
			color: #5e636b;
			font-size: 18px;
			line-height: 24px;
			margin-bottom: 10px;
		}

		.cards-grid {
			display: flex;
			gap: 10px;
			flex: 1 1 auto;
		}
	}

	.metric-card {
		border: 1px solid #E9EAEB;
		border-radius: 12px;
		padding: 25px 20px 20px 20px;
		background-color: white;
		cursor: pointer;
		transition: box-shadow 0.2s ease-in-out, background-color 0.2s;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		flex:1;
		&:first-child {
			background-color: transparent;
			border: none;
			box-shadow: none;
			.card-menu {
				display: none;
			}
			&:hover {
				box-shadow: none;
				background-color: transparent;
			}
		}

		&:hover {
			box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
			background-color: #f8f9fa;
			border-color: #d1d5db;
		}

		&.selected {
			box-shadow: 0 6px 12px rgba(0, 0, 0, 0.22);
			background-color: #f1f3f5;
			border: none;
		}

		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 8px;

			.card-title {
				font-weight: 500;
				color: #838894;
				font-size: 14px;
				line-height: 20px;
				padding-bottom: 5px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.card-menu {
				color: #80807d;
				cursor: pointer;
				font-size: 16px;
				font-weight: 700;
			}
		}

		.card-value {
			font-weight: 600;
			font-size: 24px;
			line-height: 32px;
			color: #5e636b;
			display: flex;
			align-items: center;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space:nowrap;
			padding: 5px 5px 5px 0;
		}
	}
}
