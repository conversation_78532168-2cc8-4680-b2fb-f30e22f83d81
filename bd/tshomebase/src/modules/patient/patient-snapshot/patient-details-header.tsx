import type { FC } from "react";
import { useEffect, useState } from "react";
import "./patient-details-header.less";
import _ from "lodash";

import InitialsAvatar from "@components/common/initials-avatar";
import { SIZE_LG, SIZE_MD, SIZE_SM, SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import icons from "@public/icons";
import { TabData, TabList } from "@blocks/tab-list/tab-list";
import { getFormLabel, renderItemData } from "@utils/fx";
import { IFormData } from "@hooks/form-data";
import { useLocalStorage } from "usehooks-ts";
import LocalStore from "@enum/local-store";
interface HeadActionButtonProps {
	icon: string;
	label: string;
	onClick: () => void;
}

const HeadActionButton: FC<HeadActionButtonProps> = (props) => (
	<div className="act-btn" onClick={props.onClick}>
		<img src={props.icon} />
		<p>{props.label}</p>
	</div>
);

interface PatientDetailsHeaderProps {
	tabActions: PatientTabActions;
	timeline: boolean;
	patientData: IFormData;
	toggleTimeline: (e: boolean) => void;
}


export const PatientDetailsHeader: FC<PatientDetailsHeaderProps> = (props) => {
	const { tabActions, timeline, patientData, toggleTimeline } = props;
	const [patient, setPatient] = useState<IFormData>(patientData);
	const [isTogglePatientDrawer, setIsTogglePatientDrawer] = useLocalStorage(LocalStore.TOGGLE_PATIENT_DRAWER, false);
	const getPatientHeader = () => {

		const data = patientData;
		let age = "";
		let gender = "";
		let dob = "";
		if (data?.gender) {
			gender = data.gender == "Male" ? "M" : "F";
		}
		if (data?.dob) {
			age = window.getAge(data.dob);
			dob = window.moment(data.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
		}

		setPatient({
			...patientData,
			name: [data.lastname, data.firstname].filter(Boolean).join(", "),
			subtile: [age, gender].filter(Boolean).join("/").replace(`${gender}/`, `${gender} `),
			dob: dob
		});
	};


	useEffect(() => {
		getPatientHeader();
	}, [patientData]);

	const Tabs = [
		{
			tkey: "profile",
			id: "profile",
			label: "Profile",
			avatar: <img src={icons.common.profileOutlineActive} alt="" />
		},
		{
			tkey: "timeline",
			id: "timeline",
			label: "Timeline",
			avatar: <img src={icons.common.swapOutlineActive} alt="" />
		}
	];

	return (
		<div className="patient-details">
			<img className="patient-drawer-close-chevron" onClick={() => setIsTogglePatientDrawer(!isTogglePatientDrawer)} src={icons.common.arrowUpOutline} alt="" />
			<div className="pt-act-btns">
				<TabList
					openTabs={Tabs as TabData[]}
					styles={{
						tabListStyle: "lvl-2-tab-list",
					}}
					activeTabId={timeline ? "timeline" : "profile"}
					optionsProps={{
						enabled: false
					}}
					addProps={{
						enabled: false
					}}
					tabCanClose={false}
					draggable={false}
					onTabClick={(tab: TabData) => {
						tab?.tkey === "timeline" && toggleTimeline(true)
						tab?.tkey === "profile" && toggleTimeline(false)
					}}
				/>
			</div>
			<div className="pd">
				<div className="pd-hz-div">
					<div className="pd-vr-div">
						<div className={`pd-content`} >
							<div className="img">
								{
									false &&
									<img src="" alt="user" />
									||
									<InitialsAvatar name={patient.name} variant={VARIANT_PRIMARY} size={SIZE_XL} />
								}

							</div>
							<div className="basic">
								<p className="name">{renderItemData(patient.name)}</p>
								<div className="age-dob"><p className="age-gender">{patient.subtile}</p><p className="dob">{renderItemData(patient.dob)}</p></div>
								<p className="site">Site: {patient.site_id_auto_name}</p>
							</div>
						</div>

					</div>
					<div className="icon-container">
						<img className="edit-icon hover-icon" src={icons.common.editOutline}
							onClick={() => tabActions.openTab(patientData.id, patientData.auto_name, "edit", 'patient', {})}
							alt="" />
					</div>
				</div>
				{
					patient.clinical_alert
					&&
					<div className="note" >
						<img src={icons.common.handPointUpFilled} alt="icon" />
						<p className="note-text">{patient.clinical_alert}</p>
					</div>
				}

			</div>

		</div>
	);
};