@import "../../../less/utils/mixins.less";
@import (reference) "../../../less/style/main.less";

.patient-details {
  .fcc;
  justify-content: space-between;
  flex-shrink: 0;
  .card-four;
  border-radius: 12px;

  .act-btns {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-basis: 45px;
    user-select: none;

    .act-btn {
      background: #e2f1f8;
      width: 45px;
      height: 40px;
      border-radius: 5px;
      gap: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      >p {
        font-size: 8px;
        text-transform: capitalize;
        text-align: center;
        color: #68707f;
      }

      >img {
        height: 10px;
        width: 10px;
      }
    }
  }

  .pt-act-btns {
    width: 100%;

    .tab-list-button:hover {
      color: @purple;
    }

    .lvl-2-tab-list {
      box-shadow: none;
      margin: 0;
    }
  }

  .pd {
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    width: 100%;

    .icon-container {
      flex: 0 0 28px;
      display: flex;

      .edit-icon {
        align-self: flex-start;
        margin-top: 5px;
        margin-right: 10px;
        cursor: pointer;
        width: 18px;
      }
    }

    .pd-hz-div {
      display: flex;
      flex-direction: row;
      width: 100%;
    }

    .pd-vr-div {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .pd-content {
      display: flex;
      gap: 10px;
      padding: 20px 10px;
      width: 100%;

      .img {
        display: flex;
        flex-direction: column;
        gap: 5px;
        border-radius: 50px;
        justify-content: center;
        align-items: center;
      }

      .age-gender {
        font-weight: 500;
        font-size: 12px;
        color: #58505b;
        background: #f2f2f2;
        border-radius: 6px;
        padding: 3px 10px;
      }

      .basic {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        gap: 5px;
        flex: 1;
        align-items: flex-start;

        .name {
          font-weight: 500;
          font-size: 16px;
          color: #111;
        }

        .dob {
          font-weight: 500;
          font-size: 14px;
          color: #58505b;
          align-content: center;
        }

        .age-dob {
          display: flex;
          gap: 5px;
        }

        .site {
          color: #58505b;
          font-size: 14px;
        }
      }
    }

    .note {
      display: flex;
      flex-direction: row;
      padding: 5px 10px;
      text-align: left;
      gap: 8px;
      color: #b97e4c;
      font-size: 11px;
      font-weight: 500;
      background: #fff5e3;
      border-radius: 0px 0px 12px 12px;
      width: 100%;
      display: flex;
      align-items: center;

      span {
        font-weight: 600;
        font-size: 12px;
      }
    }
  }

  .intake {
    padding: 10px;
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    color: #6487ce;
  }

  @media (max-width: 1200px) {
    .pd {
      .img {
        width: 60px;
        height: 60px;
      }
    }

    .act-btns {
      align-self: flex-end;

      .act-btn {
        .flex-justify-center("", flex-end);
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .patient-drawer-close-chevron {
    display: block;
  }
}