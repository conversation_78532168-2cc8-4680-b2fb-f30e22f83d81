@import (reference) "../../../less/style/main.less";

.snap-comp-container {
	.card-one;
	display: flex;
	flex-direction: column;
	padding-top: 10px;
	padding-bottom: 10px;
	gap: 20px;
	min-height: 200px !important;
	flex-basis: 100%;

	.snap-comp {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		gap: 20px;
		height: 100%;
		overflow-y: auto;
		padding-left: 16px;
		padding-right: 16px;
		padding-top: 6px;
		padding-bottom: 6px;

		.no-order {
			cursor: pointer;
		}

		.past-orders {
			display: flex;
			justify-content: center;
			align-items: center;
			min-height: fit-content;

			.past-order-btn {
				background: transparent;
				border: transparent;
			}
		}
	}
}
