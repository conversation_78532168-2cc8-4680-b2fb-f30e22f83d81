import { fetchFormData } from "@hooks/form-data";
import React, { useRef, useEffect, useState } from "react";
import { CRErrorBoundary } from "@blocks/error-boundary";
import "./patient-account.less";
import { useQueryData } from "@hooks/query";
import _ from "lodash";
import QueryListView from "@blocks/query-list-view/query-list-view";
import { openAlertDialog } from "@blocks/dsl-card-view/dsl-field-alert";
import { useOnScreen } from "@hooks/on-screen";
import { request } from "@core/request";
import { DSLListOnAddCallback } from "@blocks/dsl-list-view";

interface PatientAccountQueryData {
	get_patient_dashboard: {
		total_revenue: string;
		dashboard_metrics: PatientAccountData[];
	};
}

interface PatientAccountData {
	name: string;
	type: "card";
	amount: string;
	amount_raw: number;
	patient_id: number;
	measure_key: string;
	drill_down_params?: Record<string, string>;
	details_view_query?: string;
	query_params?: Record<string, string>;
}

interface SkeletonProps {
	className?: string;
}

const Skeleton: React.FC<SkeletonProps> = ({ className }) => {
	return <div className={`skeleton-pulse ${className || ""}`} />;
};

const formatCurrency = (value: string | null) => {
	if (!value) return "$0.00";
	return value;
};

const MetricCard = ({
	metric,
	isLoading = false,
	defaultTitle = "",
	onListOpen,
	selected = false,
}: {
	metric: PatientAccountData | undefined;
	isLoading?: boolean;
	defaultTitle?: string;
	onListOpen?: (metric: PatientAccountData | undefined) => void;
	selected?: boolean;
}) => {
	const title = metric?.name;
	const value = metric?.amount || "";

	return (
		<div className={`metric-card ${selected ? "selected" : ""}`} onClick={() => onListOpen?.(metric)}>
			<div className="card-header">
				<div className="card-title">{title}</div>
				<div className="card-menu">⋮</div>
			</div>

			{isLoading ? (
				<Skeleton className="h-8 w-3/4" />
			) : (
				<div className={`card-value`}>{formatCurrency(value)}</div>
			)}
		</div>
	);
};

export const PatientAccount = React.memo(({ record }: { record: string | number }) => {
	const [dashboardData, refreshDashboardData] = useQueryData<PatientAccountQueryData>(
		"patient_account_dashboard",
		[record],
		{ pool: true }
	);

	const [isOnScreen, elementRef] = useOnScreen(true);

	const [currentOpenList, openList] = useState<PatientAccountData | null>(null);

	const loading = dashboardData.loading;

	const totalRevenue = dashboardData.data?.[0]?.get_patient_dashboard?.total_revenue || "$0.00";

	const alertShown = useRef(false);

	useEffect(() => {
		if (currentOpenList) return;
		const listItem = dashboardData.data?.[0]?.get_patient_dashboard?.dashboard_metrics?.find((r) => {
			return r.details_view_query;
		});
		if (listItem) {
			openList(listItem);
		}
	}, [dashboardData]);

	const findMetric = (measureKey: string) => {
		return (
			dashboardData.data?.[0]?.get_patient_dashboard?.dashboard_metrics?.find?.(
				(item) => item.measure_key === measureKey
			) || ({} as PatientAccountData)
		);
	};

	const onAddCashBalance: DSLListOnAddCallback = async (props) => {
		const linkMap = {
			link: "patient",
			links: ["patient"],
			linkid: { patient: record },
		};
		const openFlyout = (amount: number | null) => {
			const dfd = window.Flyout.open({
				form: "billing_cash",
				card: "addfill",
				...linkMap,
				preset: {
					patient_id: record,
					amount: amount,
				},
			});
			dfd.done((fd) => {
				props?.gridRef?.advanced.refresh();
				refreshDashboardData();
			});
		};
		request({
			url: `api/query/billing_cash_amount_calc/?x1=${record}`,
		})
			.then((resp) => {
				const data = resp.data as { sum: number; patient_id: number }[];
				openFlyout(_.head(data)?.sum || null);
			})
			.catch(() => {
				openFlyout(null);
			});
	};

	const onListOpen = (metric: PatientAccountData | undefined) => {
		refreshDashboardData();
		if (metric?.details_view_query) {
			openList(metric);
		}
	};

	useEffect(() => {
		if (!isOnScreen) return;
		if (alertShown.current) return;
		fetchFormData("patient", record.toString(), true)
			.then((data) => {
				const billingAlert = data.data.billing_alert;
				alertShown.current = true;
				if (!billingAlert) return;
				openAlertDialog({
					form: "patient",
					record: record.toString(),
					customTitle: "Billing Alert",
					customFieldLabel: "Billing Alert",
					field: "billing_alert",
					mode: "view",
					required: true,
				});
			})
			.catch(() => {});
	}, [isOnScreen]);

	return (
		<CRErrorBoundary>
			<div className="patient-account">
				<div className="dashboard-section" ref={elementRef}>
					<div className="cards-grid">
						<div className="metric-card total-revenue">
							<div className="card-header">
								<div className="card-title">Total Revenue</div>
								<div className="card-menu">⋮</div>
							</div>

							{loading ? (
								<Skeleton className="h-8 w-3/4" />
							) : (
								<div className={`card-value`}>{formatCurrency(totalRevenue)}</div>
							)}
						</div>
						<MetricCard
							metric={findMetric("patient_balance")}
							selected={currentOpenList?.measure_key === "patient_balance"}
							isLoading={loading}
							onListOpen={onListOpen}
						/>
						<MetricCard
							metric={findMetric("insurance_balance")}
							selected={currentOpenList?.measure_key === "insurance_balance"}
							isLoading={loading}
							onListOpen={onListOpen}
						/>
						<MetricCard
							metric={findMetric("total_balance")}
							selected={currentOpenList?.measure_key === "total_balance"}
							isLoading={loading}
							onListOpen={onListOpen}
						/>
						<MetricCard
							metric={findMetric("held_revenue")}
							selected={currentOpenList?.measure_key === "held_revenue"}
							isLoading={loading}
							onListOpen={onListOpen}
						/>
						<MetricCard
							metric={findMetric("unbilled_revenue")}
							selected={currentOpenList?.measure_key === "unbilled_revenue"}
							isLoading={loading}
							onListOpen={onListOpen}
						/>
						<MetricCard
							metric={findMetric("cash_balance")}
							selected={currentOpenList?.measure_key === "cash_balance"}
							isLoading={loading}
							onListOpen={onListOpen}
						/>
					</div>
				</div>
			</div>
			{currentOpenList && currentOpenList.details_view_query && (
				<CRErrorBoundary>
					<QueryListView
						key={currentOpenList.measure_key + currentOpenList.details_view_query}
						rowClicked={(row) => {
							if (!row.rowData) {
								console.log("No row data");
								return;
							}
							const { form_id, form_name } = row.rowData;
							if (!form_id || !form_name) {
								return;
							}
							if (!window.DSL[form_name as string]) {
								return;
							}
							window.Flyout.open({
								record: form_id as string,
								form: form_name as string,
								card: "read",
								tab_can_archive_override: () => false,
							});
						}}
						onAdd={currentOpenList.measure_key === "cash_balance" ? onAddCashBalance : undefined}
						code={currentOpenList.details_view_query}
						parameters={(() => {
							if (!currentOpenList.drill_down_params) {
								return "";
							}
							return Object.values(currentOpenList.drill_down_params)
								.map((value, i) => {
									return `x${i}=${value}`;
								})
								.join("&");
						})()}
					/>
				</CRErrorBoundary>
			)}
		</CRErrorBoundary>
	);
});

export default PatientAccount;
