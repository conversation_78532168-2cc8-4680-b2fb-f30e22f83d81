import type { FC } from "react";
import React from "react";
import "./patient-wgts.less";

import { UIComponentProps } from "@utils/interfaces";

export interface DetailPortionProps {
	label: string;
	value: string;
	styleClass?: string;
	onClick?: any;
}

export const DetailPortion: FC<DetailPortionProps> = (props) => {
	const { label, value, styleClass, onClick } = props;
	return (
		<div className={`detail-packet ${styleClass || ""}`}>
			<p className="label">{label}</p>
			<p className="value" onClick={onClick}>
				{value}
			</p>
		</div>
	);
};

export interface PatientDetailPortionProps extends UIComponentProps {
	label?: string | null;
	value?: string | null;
	icon?: string | null;
	styleClass?: string;
	onClick?: any;
	CustomTitle?: React.ReactNode | null;
	line2?: string | null;
	onMouseOver?: any;
	onMouseLeave?: any;
}

export const PatientDetailPortion: FC<PatientDetailPortionProps> = (props) => {
	const { label, value, line2, styleClass, onClick, title, icon, CustomTitle } = props;

	return (
		<div
			className={`patient-detail-packet ${styleClass || ""} ${(value?.length > 17 && "fw") || ""} `}
			onClick={onClick}
			onMouseLeave={props.onMouseLeave}
			onMouseOver={props.onMouseOver}
		>
			{CustomTitle || (
				<div className={`icon-title${onClick ? " clickable" : ""}`}>
					{(icon && <img src={icon} alt="" />) || ""}
					{title && <p className="title">{title}</p>}
				</div>
			)}
			<div className="pdp-content">
				<p className="value">{value}</p>
				{line2 && <p className="value">{line2}</p>}
				<p className="label">{label}</p>
			</div>
		</div>
	);
};
