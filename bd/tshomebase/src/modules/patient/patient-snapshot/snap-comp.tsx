import "./snap-comp.less";
import type { FC } from "react";
import React from "react";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { IFormData } from "@hooks/form-data";
import { PatientTabActions, SnapshotInternalActions } from "@modules/patient/patient-snapshot";
import { IFilterOpts } from "@utils/dsl-fx";
import { RoutedComponentProps } from "@typedefs/routed-component";
import _ from "lodash";
import { PatientPrescriptionFlow } from "../patient-prescription/patient-prescription-flow";
import { CRErrorBoundary } from "@blocks/error-boundary";
export interface PatientSnapshotComponentProps extends RoutedComponentProps {
	linkMap: DSLDrawLinkMap;
	carePlan: IFormData;
	patientData: IFormData;
	tabActions: PatientTabActions;
	snapInternalActions: SnapshotInternalActions;
	ddRef?: (ref: any) => void;
}

export const PatientSnapshotComponent: FC<PatientSnapshotComponentProps> = (props) => {
	return (
		<>
			<div className="snap-comp-container">
				<div className="snap-comp">
					<CRErrorBoundary>
						<PatientPrescriptionFlow {...props} />
					</CRErrorBoundary>
				</div>
			</div>
		</>
	);
};
