import './shipment.less';
import { createPortalModal } from '@blocks/portal-modal';

import { FC, useState } from 'react';
import GenericCardContainer from '@components/cards/generic-card-container/generic-card-container';
import icons from '@public/icons';
import { uuid } from '@utils/fx';
import { request } from '@core/request';
import SpinLoader from '@components/common/spin-loader';
import { toast } from '@components/toast';
import { selectTheme } from '@modules/erx/helper/select-config';
import { FieldSelect } from "@dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { ConfigProvider } from 'antd';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import Select, { StylesConfig } from 'react-select';
import { getNextZIndexFlyoutModelInception } from '@utils/dsl-fx';
dayjs.extend(customParseFormat);
//TODO:Will uncomment parcel selection
const selectStyleOver: StylesConfig = {
    control: (provided, state) => ({
        ...provided,
        height: '60px',
        borderRadius: '8px',
        border: `1px solid ${state.isFocused ? '#CBD5E0' : '#E3E5E8'}`,
        boxShadow: '0px 1px 2px 1px #38383814 inset',
        '&:hover': {
            borderColor: '#CBD5E0'
        },
        padding: '10px'
    }),
    valueContainer: (provided) => ({
        ...provided,
        marginTop: '18px',
        padding: '0px',
    }),
    placeholder: (provided) => ({
        ...provided,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        lineHeight: '1.2',
        color: '#9B9FA8'
    }),
    input: (provided) => ({
        ...provided,
        margin: '0',
        padding: '0',
        color: '#414651',
    }),
    indicatorsContainer: (provided) => ({
        ...provided,
        height: '100%',
        color: '#717680',
        '&:hover': {
            color: '#4A5568'
        }
    }),
    indicatorSeparator: (provided) => ({
        ...provided,
        display: 'none'
    }),
    clearIndicator: (provided) => ({
        ...provided,
        color: '#717680',
        '&:hover': {
            color: '#4A5568'
        }
    }),
    dropdownIndicator: (provided) => ({
        ...provided,
        color: '#717680',
        '&:hover': {
            color: '#4A5568'
        }
    }),
    menu: (provided) => ({
        ...provided,
        marginTop: '8px',
        borderRadius: '8px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        zIndex: 1100
    }),
    menuList: (provided) => ({
        ...provided,
        padding: '8px'
    }),
    menuPortal: (provided) => ({
        ...provided,
        zIndex: 9999
    }),
    option: (provided, state) => ({
        ...provided,
        padding: '12px 16px',
        borderRadius: '6px',
        backgroundColor: state.isSelected ? '#EDF2F7' : 'transparent',
        color: '#4A5568',
        '&:hover': {
            backgroundColor: '#F7FAFC'
        }
    })
};

const Parcel: FC<any> = (props: any) => {
    const { getModal, promise } = props;

    const [height, setHeight] = useState<number>(5);
    const [width, setWidth] = useState<number>(5);
    const [length, setLength] = useState<number>(5);
    const [distanceUnit, setDistanceUnit] = useState<any>('in');
    const [weight, setWeight] = useState<number>(2);
    const [weightUnit, setWeightUnit] = useState<any>('lb');

    const distanceUnitOptions = [
        { value: 'cm', label: 'cm' },
        { value: 'in', label: 'in' },
        { value: 'ft', label: 'ft' },
        { value: 'm', label: 'm' },
        { value: 'mm', label: 'mm' },
        { value: 'yd', label: 'yd' }
    ];

    const weightUnitOptions = [
        { value: 'g', label: 'g' },
        { value: 'kg', label: 'kg' },
        { value: 'lb', label: 'lb' },
        { value: 'oz', label: 'oz' }
    ];

    const onCancel = () => {
        getModal().closeModal();
        promise.resolve(null);
    };

    const onClick = () => {
        getModal().closeModal();
        promise.resolve({
            id: uuid(),
            height,
            width,
            length,
            distance_unit: distanceUnit?.value || 'in',
            weight,
            mass_unit: weightUnit?.value || 'lb',
        });
    };

    return (
        <div>
            <GenericCardContainer
                title="Select Parcel Details"
                onClick={onCancel}
                icon={icons.common.crossIcon}
                className='parcel-card'
            >
                <div>
                    <span>Select Height</span>
                    <input
                        type="number"
                        step={0.01}
                        value={height}
                        onChange={(e) => setHeight(Number(e.target.value))}
                        className='form-input-field'
                    />
                    <span>Select Width</span>
                    <input
                        type="number"
                        step={0.01}
                        value={width}
                        onChange={(e) => setWidth(Number(e.target.value))}
                        className="form-input-field"
                    />
                    <span>Select Length</span>
                    <input
                        type="number"
                        step={0.01}
                        value={length}
                        onChange={(e) => setLength(Number(e.target.value))}
                        className="form-input-field"
                    />
                    <span>Distance Unit</span>
                    <Select
                        options={distanceUnitOptions}
                        value={distanceUnit}
                        onChange={setDistanceUnit}
                        styles={{
                            ...selectStyleOver,
                            menuPortal: (provided) => ({
                                ...provided,
                                zIndex: getNextZIndexFlyoutModelInception(),
                            }),
                        }}
                        theme={selectTheme}
                    />
                    <span>Select Weight</span>
                    <input
                        type="number"
                        step={0.01}
                        value={weight}
                        onChange={(e) => setWeight(Number(e.target.value))}
                        className="form-input-field"
                    />
                    <span>Weight Unit</span>
                    <Select
                        options={weightUnitOptions}
                        value={weightUnit}
                        onChange={setWeightUnit}
                        styles={{
                            ...selectStyleOver,
                            menuPortal: (provided) => ({
                                ...provided,
                                zIndex: getNextZIndexFlyoutModelInception(),
                            }),
                        }}
                        theme={selectTheme}
                    />
                </div>
                <button onClick={onClick} className='btn-primary' style={{ marginTop: '10px' }}>Add</button>
            </GenericCardContainer>
        </div>
    );
}

interface ShipmentsProps {
    fd: Record<string, number>;
    getModal: () => { closeModal: () => void };
    promise: {
        resolve: (value: any) => void;
        reject: (reason: any) => void;
    };
}

export const Shipments: FC<ShipmentsProps> = ({ fd, getModal, promise }) => {
    if (!fd.id) {
        return;
    }
    const [parcels, setParcels] = useState<any[]>([{
        id: uuid(),
        height: 5,
        width: 5,
        length: 5,
        distance_unit: 'in',
        weight: 2,
        mass_unit: 'lb'
    }]);
    const [selectedDateTime, setSelectedDateTime] = useState<dayjs.Dayjs | null>(null);
    const [rates, setRates] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [selectedRate, setSelectedRate] = useState<any>(null);
    const [shipmentMeta, setShipmentMeta] = useState<any>({});
    const [dateTimeError, setDateTimeError] = useState<string>('');

    const handleClick = async () => {
        const parcel = await getParcel();
        if (!parcel) {
            return;
        }
        setParcels([...parcels, parcel]);
        setRates([]);
        setSelectedRate(null);
    }

    const removeParcel = (id: string) => {
        const updatedParcels = parcels.filter((parcel) => parcel.id !== id);
        setParcels(updatedParcels);
        setRates([]);
        setSelectedRate(null);
    }

    const getParcel = async () => {
        return new Promise((resolve, reject) => {
            createPortalModal(Parcel as FC<unknown>, {}, { parent: {}, promise: { resolve, reject }, }).catch((err) => console.error(err));
        }).catch((err) => console.error(err));
    }

    const handleDateTimeChange = (date: dayjs.Dayjs | null) => {
        setDateTimeError('');
        setSelectedRate(null);
        
        if (!date) {
            setDateTimeError('Please select date and time');
            return;
        }

        // Check if selected date is before current date
        if (date.isBefore(dayjs(), 'minute')) {
            setDateTimeError('Selected date and time cannot be in the past');
            return;
        }

        setSelectedDateTime(date);
        // Convert to UTC format for API
        loadShipmentRates(date);
    };

    const loadShipmentRates = async (dateTime: dayjs.Dayjs) => {
        setLoading(true);
        try {
            const formattedDate = dateTime.format("YYYY-MM-DDTHH:mm:ss.SSS[Z]");

            const response = await request({
                url: `/label/${fd.id}`,
                method: 'POST',
                data: {
                    shipment_date: formattedDate,
                    parcel: parcels.map(parcel => ({
                        height: parcel.height,
                        width: parcel.width,
                        length: parcel.length,
                        distance_unit: parcel.distance_unit,
                        weight: parcel.weight,
                        mass_unit: parcel.mass_unit,
                    })),
                },
            });

            if (response.data && response.data.shipment.rates) {
                setShipmentMeta(response.data.shipment);
                const formattedOptions = response.data.shipment.rates.map((rate: any) => ({
                    value: rate.object_id,
                    label: `${rate.provider}: ${rate.servicelevel.name} - $${rate.amount} (${rate.estimated_days} day${rate.estimated_days > 1 ? 's' : ''})`,
                }));
                setRates(formattedOptions);
            }
        } catch (err) {
            window.prettyError("Error loading shipment rates", (err as any).data?.response?.data?.error || "Unknown error");
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const createShipment = async () => {
        if (!selectedRate) return;
        
        setLoading(true);
        try {
            const formattedDate = selectedDateTime?.format("YYYY-MM-DDTHH:mm:ss.SSS[Z]") || '';

            await request({
                url: `/label/${fd.id}`,
                method: 'POST',
                data: {
                    shipment_meta: shipmentMeta,
                    shipment_date: formattedDate,
                    parcel: parcels,
                    rate: selectedRate.value,
                    rateMeta: shipmentMeta.rates.filter((rate: any) => rate.object_id === selectedRate.value)?.[0] || {},
                },
            });
            getModal().closeModal();
            promise.resolve({ success: true });
            toast({ type: 'success', position: "bottom-center", theme: "dark", message: 'Shipment Label created successfully' });
        } catch (err) {
            window.prettyError("Error creating shipment", (err as any).data?.response?.data?.error || "Unknown error");
            console.error(err); 
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        getModal().closeModal();
        promise.reject({ success: false, error: "Modal Closed" });
    };

    return (
        <div className='shipment-container'>
            <div className="modal-header">
                <h3>Create Shipment Label</h3>
                <img 
                    src={icons.common.crossIcon} 
                    alt="close" 
                    className="close-icon"
                    onClick={handleClose}
                />
            </div>
            {loading ? <SpinLoader loading={loading} /> : 
                <div className='shipments'>
                    <section className="date-time-section">
                        <label className="field-label">Select Shipment Date & Time</label>
                        <ConfigProvider
                            theme={{
                                components: {
                                    DatePicker: {
                                        zIndexPopup: 1100,
                                        controlHeight: 60,
                                        paddingBlock: 8,
                                        paddingInline: 12,
                                        borderRadius: 8,
                                        colorBorder: '#E5E5E0', // --color-border-500
                                        colorPrimaryHover: '#6C63A5', // --color-primary-700
                                        colorPrimary: '#837BB2', // --color-primary
                                        colorText: '#252B37', // --color-text-800
                                        colorTextPlaceholder: '#717680', // --color-text
                                        colorBgContainer: '#FFFFFF', // --white
                                        fontSize: 14,
                                    },
                                },
                            }}
                        >
                            <DatePicker
                                showTime={{
                                    format: 'hh:mm A',
                                    use12Hours: true,
                                    minuteStep: 1,
                                }}
                                format="MM/DD/YYYY hh:mm A" // 12-hour format for display
                                value={selectedDateTime}
                                onChange={handleDateTimeChange}
                                className={`datetime-picker ${dateTimeError ? 'has-error' : ''}`}
                                placeholder="Select date and time"
                                popupStyle={{ zIndex: 1100 }}
                                getPopupContainer={(trigger) => trigger.parentElement || document.body}
                                disabledDate={(current) => {
                                    return current && current.isBefore(dayjs(), 'day');
                                }}
                                disabledTime={(current) => {
                                    if (current && current.isSame(dayjs(), 'day')) {
                                        return {
                                            disabledHours: () => Array.from(Array(24).keys())
                                                .slice(0, dayjs().hour()),
                                            disabledMinutes: (hour) => {
                                                if (hour === dayjs().hour()) {
                                                    return Array.from(Array(60).keys())
                                                        .slice(0, dayjs().minute());
                                                }
                                                return [];
                                            }
                                        };
                                    }
                                    return {};
                                }}
                            />
                        </ConfigProvider>
                        {dateTimeError && <div className="error-message">{dateTimeError}</div>}
                    </section>

                    {rates.length > 0 ? (
                        <section className="rates-section">
                            <div className="rates-section-select-container">
                                <label className="rates-section-select-label">Select Shipping Rate</label>
                                <FieldSelect
                                    form="none"
                                    defaultValueSource={selectedRate?.value || ""}
                                    defaultValueAutoName={selectedRate?.label || ""}
                                    multi={false}
                                    disabled={false}
                                    options={rates}
                                    onChange={(val, label) => {
                                        setSelectedRate({ value: val, label });
                                    }}
                                    customStyles={selectStyleOver}
                                    theme={SelectConfig.dsl.theme}
                                    placeholder="Select a shipping rate..."
                                />
                            </div>
                            <button 
                                className='btn-primary create-shipment-btn'
                                disabled={!selectedRate} 
                                onClick={createShipment}
                            >
                                Create Shipment
                            </button>
                        </section>
                    ) : (
                        <div className="no-rates">
                            <p>No shipping rates available for the selected date and time.</p>
                        </div>
                    )}
                </div>
            }
        </div>
    );
}

export const openShipmentPopover = (fd: Record<string, number>) => {
    return new Promise((resolve, reject) => createPortalModal(
        Shipments as FC<unknown>,
        {
            onRequestClose: () => {
                reject({ success: false, error: "Unexpected Error" });
            },
        },
        { 
            fd,
            promise: { resolve, reject }
        }
    ));
}