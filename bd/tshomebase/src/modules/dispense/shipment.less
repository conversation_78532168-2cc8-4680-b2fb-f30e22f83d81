@import (reference) "../../less/style/main.less";

.shipment-container {
  width: 480px;
  min-height: 400px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--color-border-300);

    h3 {
      margin: 0;
      font-size: 20px;
      color: var(--color-text-800);
    }

    .close-icon {
      cursor: pointer;
      padding: 4px;
      &:hover {
        opacity: 0.7;
      }
    }
  }

  .shipments {
    padding: 24px;
    
    section {
      margin-bottom: 24px;
      
      .field-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-600);
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }

    .datetime-picker {
      width: 100%;
      height: 45px !important;
      border-radius: 8px;
      border: 1px solid var(--color-border-500);
      
      &.has-error {
        border-color: var(--color-error);
        
        &:hover {
          border-color: var(--color-error-600);
        }
        
        &.ant-picker-focused {
          box-shadow: 0 0 0 2px var(--color-error-100);
        }
      }

      .ant-picker-input {
        height: 100%;
        
        input {
          height: 100%;
          padding: 8px 12px;
          font-size: 14px;
          color: var(--color-text-800);

          &::placeholder {
            color: var(--color-text);
          }
        }
      }

      &:hover {
        border-color: var(--color-primary-700);
      }
      
      &.ant-picker-focused {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 2px var(--color-primary-100);
      }
    }

    .error-message {
      color: var(--color-error);
      font-size: 12px;
      margin-top: 4px;
      min-height: 20px;
    }

    .rates-section {
      .rates-section-select-container {
        flex: 1;
        box-sizing: border-box;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;

        .rates-section-select-label {
          position: absolute !important;
          top: var(--spacing-standard);
          left: var(--spacing-xlarge);
          font-size: var(--font-size-xsmall);
          font-weight: var(--font-weight-regular);
          color: var(--color-text);
          z-index: 1;
          pointer-events: none;
        }
      }
      .field-select {
        width: 100%;
        margin-bottom: 20px;
      }

      .create-shipment-btn {
        width: 100%;
        height: 40px;
        background: var(--color-primary);
      }
    }

    .no-rates {
      text-align: center;
      color: var(--color-text-500);
      padding: 16px;
    }
  }
}

.shipments {
  padding: 24px;
  height: calc(100% - 60px);
  overflow-y: auto;

  .form-input-field {
    width: 200px !important;
    padding: 8px 12px;
    border: 1px solid var(--color-border-500);
    border-radius: 8px;
    margin-right: 12px;
    
    &:focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-100);
    }
  }

  .input-group {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }

  .parcel {
    background: var(--white);
    border: 1px solid var(--color-border-500);
    border-radius: 12px;
    padding: 20px;

    .table-container {
      margin-bottom: 16px;
      max-height: 250px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: var(--color-background-200);
      }

      &::-webkit-scrollbar-thumb {
        background: var(--scroll-bar);
        border-radius: 4px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        
        th {
          background: var(--color-background-300);
          padding: 12px;
          text-align: left;
          font-weight: 600;
          color: var(--color-text-700);
        }

        td {
          padding: 12px;
          border-bottom: 1px solid var(--color-border-300);
          color: var(--color-text-600);
        }

        tbody tr {
          &:hover {
            background-color: var(--color-background-200);
          }
        }

        .remove-parcel {
          cursor: pointer;
          padding: 4px;
          &:hover {
            opacity: 0.7;
          }
        }
      }
    }
  }

  button.btn-primary {
    height: 40px;
    padding: 0 24px;
    border-radius: 8px;
    font-weight: 500;
    background: var(--color-primary);
    color: var(--white);
    border: none;
    transition: all 0.2s;
    
    &:hover:not(:disabled) {
      background: var(--color-primary-700);
    }

    &:disabled {
      background: var(--disabled-background-200);
      color: var(--color-text-400);
      cursor: not-allowed;
    }

    img {
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}

// Ant Design DatePicker dropdown customization
.ant-picker-dropdown {
  z-index: 1100 !important;

  .ant-picker-panel-container {
    border-radius: 8px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--color-border-300);
  }

  .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
    background: var(--color-primary);
  }

  .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected 
  .ant-picker-time-panel-cell-inner {
    background: var(--color-primary-100);
    color: var(--color-primary);
  }

  .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    border-color: var(--color-primary);
  }

  .ant-picker-ok {
    .ant-btn-primary {
      background: var(--color-primary);
      border-color: var(--color-primary);
      
      &:hover {
        background: var(--color-primary-700);
        border-color: var(--color-primary-700);
      }

      &:disabled {
        background: var(--disabled-background-200);
        border-color: var(--disabled-background-200);
      }
    }
  }

  .ant-picker-header {
    border-bottom: 1px solid var(--color-border-300);
    
    button {
      color: var(--color-text-800);
      
      &:hover {
        color: var(--color-primary);
      }
    }
  }

  .ant-picker-content {
    th {
      color: var(--color-text-500);
    }
  }

  .ant-picker-cell {
    color: var(--color-text-600);

    &-in-view {
      color: var(--color-text-800);
    }

    &-today .ant-picker-cell-inner::before {
      border-color: var(--color-primary);
    }

    &-disabled {
      color: var(--color-text-300);
    }

    &-selected .ant-picker-cell-inner {
      background: var(--color-primary);
    }
  }

  .ant-picker-time-panel {
    border-left: 1px solid var(--color-border-300);
    
    .ant-picker-time-panel-cell-selected {
      background: var(--color-primary-100);
      
      .ant-picker-time-panel-cell-inner {
        color: var(--color-primary);
      }
    }
  }
}
