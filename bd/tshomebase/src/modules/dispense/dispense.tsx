import React, { useEffect } from "react";
import { useNavigation } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import "./dispense.less";

export const Dispense = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/dispense");
	useEffect(() => {
		if (props.navToURL?.startsWith("/dispense")) {
			props.setActiveTab?.("dispense");
		}
	}, [nav.globalNav.lrct]);

	return (
		<div className='module-dispense'>
			TODO
		</div>
	);
};