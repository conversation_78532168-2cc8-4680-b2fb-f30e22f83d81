import type { FC } from "react";
import React, { useEffect, useMemo } from "react";
import "./manage.less";
import { getFormLabel, toPresentableLabel } from "@utils/fx";
import { useNavigation } from "@core/navigation";

import type { RoutedComponentProps } from "@typedefs/routed-component";
import { DSLTabView, DSLTabViewProps, TabViewActions } from "@blocks/dsl-tab-view/dsl-tab-view";
import { onDropOutsideCallback, TabList, type TabData } from "@blocks/tab-list/tab-list";
import { DSLListView } from "@blocks/dsl-list-view/dsl-list-view";
import { useFormTabController } from "@hooks/form-tab-controller";
import { getAutoName } from "@utils/dsl-fx";
import getFormIconAvatar from "@components/common/icon-avatar/icon-avatar";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

export interface ManageViewProps extends RoutedComponentProps {
	styles: Record<string, unknown> | null | undefined;
	ops?: Record<string, unknown> | null;
}

export const ManageView: FC<ManageViewProps> = (props) => {
	const { ops = null } = props;
	const nav = useNavigation(props, "/", ops);
	const [openTabs, active, controller] = useFormTabController([], "options");

	const lastActiveManage = React.useRef("manage:all");
	const tabContainer = React.useRef(null);

	let manageTabs = useMemo(() => {
		const tabs: TabData[] = [
			{
				tkey: "manage:all",
				id: "manage:all",
				form: "coffeedsl",
				mode: "list",
				label: "All",
				canClose: false,
				filters: {},
			},
		];

		const bundles: string[] = [
			"manage",
			"setup",
			"reference",
			"location",
			"assistance",
			"billing",
			"inventory",
			"status",
			"audit",
		];
		bundles.forEach((bundle) => {
			tabs.push({
				tkey: "manage:" + bundle,
				id: "manage:" + bundle,
				form: "coffeedsl",
				mode: "list",
				label: toPresentableLabel(bundle),
				canClose: false,
				filters: {
					bundle: bundle,
				},
			});
		});
		controller.setOpenTabs(tabs);
		if (tabs.length) {
			controller.setActiveTabId(tabs[0].tkey);
		}
		return tabs;
	}, []);

	useEffect(() => {
		if (!props.navPath?.length) {
			return;
		}
		const r = props.navPath;
		if (r.length) {
			const route = r[0];
			if (window.DSL[route]) {
				openTab(route, null);
			} else if (route.startsWith("manage:")) {
				const exists = manageTabs.filter((t) => t.tkey == route);
				if (exists.length) {
					openTab(route, null);
				}
			}
		}
	}, [nav.globalNav.lrct]);

	const openTab = (form: string, gridRef?: AdvancedGridRef | null) => {
		form = form + "";
		const tkey = form;
		let label = getFormLabel(form);
		controller.setOpenTabs((p) => {
			p = Array.from(p);
			const tindex = p.findIndex((t) => t.tkey == tkey);
			if (tindex > -1) {
				p[tindex].tkey = tkey;
				p[tindex].mode = "list";
				if (!p[tindex].label) p[tindex].label = label;
				p[tindex].gridRef = gridRef;
				return p;
			}
			if (form === "user") {
				p = [
					...p,
					{
						tkey,
						id: form,
						label,
						mode: "list",
						componentProps: {},
						form: form,
						gridRef: gridRef,
						avatar: getFormIconAvatar("user"),
					},
				];
				return p;
			} else {
				p = [...p, { tkey, id: form, label, mode: "list", componentProps: {}, form: form, gridRef: gridRef }];
			}
			return p;
		});
		controller.setActiveTabId(tkey);
	};

	const closeTab = (form: string, tab: TabData) => {
		controller.setOpenTabs((p) => {
			p = Array.from(p);
			p = p.filter((t) => t.tkey != tab.tkey);
			return p;
		});
		const sthere = openTabs.filter((t) => t.tkey == lastActiveManage.current);
		if (sthere) {
			controller.setActiveTabId(lastActiveManage.current);
		} else {
			if (openTabs.length) {
				controller.setActiveTabId(openTabs[0].tkey);
			}
		}
	};

	useEffect(() => {
		setTimeout(() => {
			if (!tabContainer.current) {
				return;
			}
			const ident = `[data-rbd-drag-handle-draggable-id="${active}"]`;
			const e = $(tabContainer.current).find(ident).first();
			if (e.length) {
				e[0].scrollIntoView();
			}
		}, 100);
	}, [active]);

	const rowClicked = (event: TabData) => {
		if (event.type == "dblclick") {
			// openInNewWindow(event.id, event.mode);
			openTab(event.rowData?.code as string, event.gridRef);
		} else {
			openTab(event.rowData?.code as string, event.gridRef);
		}
	};

	const openInNewWindow = (id: string, mode: string) => false;

	const onDropOutside: onDropOutsideCallback = (dragEvent) => {
		const id = dragEvent.draggableId;
		if (!id) {
			return;
		}
		const ci = openTabs.find((t) => t.id == id);
		if (!ci?.id) {
			return;
		}
		if (openInNewWindow(ci.id, ci.mode)) {
			closeTab(ci.id, ci);
		}
	};

	const onTabCancel = (tab: TabData) => {
		closeTab(tab.tkey, tab);
		return;
	};

	const rxOpts = {
		onArchive: controller.onArchive,
		onUnArchive: controller.onUnArchive,
		rowClicked: rowClicked,
		onCancel: closeTab,
		...nav,
	};

	const onTabViewNavChange = (nav: string[], actions: TabViewActions, tvProps: DSLTabViewProps) => {
		if (!tvProps.navPath?.length) {
			return;
		}
		const r = tvProps.navPath;
		let id = r.splice(0, 1).toString();
		if (window.DSL[id]) {
			id = r.splice(0, 1).toString();
		}
		const mode = r.splice(0, 1).toString();
		if (["add", "addfill"].includes(mode)) {
			if (!id.includes("-")) {
				id = "new-entry";
			}
			actions.openNewTab(id);
			return;
		} else if (!mode) {
			actions.setActive("options");
			return;
		} else if (id > "0") {
			const autoName = getAutoName(tvProps.form, id);
			actions.openTab(id, autoName, mode || "read", tvProps.form, {});
		}
	};

	return (
		<div className="dsl-tab-view-container" ref={tabContainer}>
			<TabList
				activeTabId={active}
				openTabs={openTabs}
				onDragEnded={(dragEvent, updateList) => {
					controller.setOpenTabs((tabs) => updateList(tabs));
				}}
				onDropOutside={onDropOutside}
				styles={{
					tabListStyle: "lvl-2-tab-list",
				}}
				optionsProps={{
					enabled: false,
				}}
				addProps={{
					enabled: false,
				}}
				tabCanClose={true}
				draggable={true}
				onTabClick={(tab: TabData) => {
					if (tab.tkey.startsWith("manage:")) {
						lastActiveManage.current = tab.tkey;
					}
					controller.setActiveTabId(tab.tkey);
				}}
				onTabClose={onTabCancel}
			/>
			{openTabs.map((tab) => {
				const tkey = tab.tkey;
				const pr = `/${tab.form}`;
				if (tab.tkey.startsWith("manage:")) {
					return (
						<div
							key={tkey}
							className="dsl-container"
							style={{ display: active == tkey ? undefined : "none" }}
						>
							<DSLListView
								{...rxOpts}
								{...nav}
								routePrepend={"/" + tab.tkey}
								form="coffeedsl"
								isActive={active == tkey}
								isParentActive={props.isActive}
								filtersPresetFixed={tab.filters as Record<string, unknown>}
							/>
						</div>
					);
				}
				return (
					<div key={tkey} className="dsl-container" style={{ display: active == tkey ? undefined : "none" }}>
						<DSLTabView
							{...nav}
							tabLabel={`${tab.label} List`}
							initialAsAvatar={(tab?.label == "Users" && true) || false}
							routePrepend={pr}
							form={tab.form}
							isActive={active == tkey}
							isParentActive={props.isActive}
							overrides={{
								onNavChange: onTabViewNavChange,
							}}
						/>
					</div>
				);
			})}
		</div>
	);
};
