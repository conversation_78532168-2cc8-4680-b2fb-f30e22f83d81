import { request } from "@core/request";
import type { ReportDescriptor } from "@typedefs/arjs";
import { fetchFormData } from "@hooks/form-data";

export const updateReport = async (id: number | string, data: ReportDescriptor) => {
	return request({
		url: `/form/report/${id}`,
		method: "PUT",
		data: { json_data: data.json_data, ...data },
	})
};

export const getReportInfo = async (reportId: string | number) => new Promise((resolve, reject) => {
	fetchFormData("report", reportId).then((r) => {
		resolve(r.data);
	}).catch((error) => {
		reject(error);
	});
});


export const getReportBasedOnCode = async (code: string) => new Promise((resolve, reject) => {
	request({
		url: `/form/report/?limit=1&filter=code:${code}`,
	}).then((r) => {
		if (Array.isArray(r.data) && r.data.length > 0) {
			resolve(r.data[0]);
		} else {
			reject("Report Not Found Report Code: " + code);
		}
	}).catch((error) => {
		reject(error);
	});
})


export const getReportsByCode = async (code: string) => new Promise((resolve, reject) => {
	request({
		url: `/form/report/?filter=code:${code}`,
	}).then((r) => {
		if (Array.isArray(r.data) && r.data.length > 0) {
			resolve(r.data);
		} else {
			reject("No reports found for Report Code: " + code);
		}
	}).catch((error) => {
		reject(error);
	});
});


export const getReportTemplate = async (type: unknown) => new Promise((resolve, reject) => {
	request({
		url: `/form/template_report/?limit=1&filter=type:${type}`,
	}).then((r) => {
		if (Array.isArray(r.data) && r.data.length > 0) {
			resolve(r.data[0]);
		} else {
			reject("Report Template Not Found Report Type: " + type);
		}
	}).catch((error) => {
		reject(error);
	});
});
