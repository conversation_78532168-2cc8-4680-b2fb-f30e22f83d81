/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable indent */
/* eslint-disable @typescript-eslint/no-floating-promises */
import type { FC } from "react";
import { useEffect, useMemo, useState } from "react";
import { useNavigation } from "@core/navigation";
import { NestedTabView } from "@blocks/nested-tab-view/nested-tab-view";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import type { ReportDescriptor } from "@typedefs/arjs";
import { ReportDesignerViewer } from "./report-designer-viewer";
import { getReportInfo } from "./report-actions";
import { DSLTabView } from "@blocks/dsl-tab-view/dsl-tab-view";
type ReportSnapshotProps = RoutedComponentProps
export const ReportSnapshotView: FC<ReportSnapshotProps> = (props) => {
	const nav = useNavigation(props, `/${props.id}/snap/`);
	const [report, setReport] = useState<ReportDescriptor | null>(null);
	const linkMap = useMemo(() => ({
		link: "report",
		links: ["report"],
		linkid: { id: props.id }
	}), []);

	const map = useMemo(() => {
		const snapshotComponent = {
			label: "Snapshot",
			id: "scanner",
			component: {
				renderComponent: ReportDesignerViewer,
				componentProps: { ...props, id: props.id, data: report }, // Pass report as it is initially, before data is fetched
				path: "/snap"
			}
		};
		// If report is available, update the componentProps
		if (report) {
			snapshotComponent.component.componentProps = { ...props, id: props.id, data: report };
		}
		return [snapshotComponent];
	}, [report]);


	const getReportInfoData = async (id: unknown) => {
		try {
			const data = await getReportInfo(id);
			if (data) {
				setReport(data);
			} else {
				console.info("No Report Founded:",);
			}
		}
		catch (err) {
			console.error("Error fetching report info:", err);
		}
	};

	useEffect(() => {
		getReportInfoData(props.id);
	}, []);
	// eslint-disable-next-line react/react-in-jsx-scope
	return <NestedTabView {...{ ...props, id: props.id, map, linkMap }} {...nav} />;
};


export const Report = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/report");

	useEffect(() => {
		if (props.navToURL?.startsWith("/report")) {
			props.setActiveTab?.("report");
		}
	}, [nav.globalNav.lrct]);


	return (
		// eslint-disable-next-line react/react-in-jsx-scope
		<div>
			<DSLTabView styles={undefined} {...props} {...nav} form="report" viewMode='snap' TabRenderComponent={ReportSnapshotView} />
		</div>
	);
};
