import type { FC } from "react";
import React, { useCallback, useEffect, useState } from "react";
import { getReportInfo, getReportTemplate, updateReport } from "./report-actions";

import "@mescius/activereportsjs/styles/ar-js-ui.css";
import "@mescius/activereportsjs/styles/ar-js-designer.css";
import "@mescius/activereportsjs/styles/ar-js-viewer.css";

import type { Designer as ReportDesigner, Viewer as ReportViewer } from "@mescius/activereportsjs-react";
import type { SaveNewReportInfo } from "@mescius/activereportsjs/reportdesigner";

import { useNavigation } from "@core/navigation";
import { RoutedComponentProps } from "@typedefs/routed-component";
import type { AppMode, ReportDescriptor } from "@typedefs/arjs";

import { generateAndPrintReport } from "@components/arjs/services";
import { Viewer } from "@components/arjs/viewer";
import { Designer } from "@components/arjs";
import { fetchFormData } from "@hooks/index";
import { request } from "@core/request";
import { toast } from "@components/toast";

interface ReportDesignerViewerProps extends RoutedComponentProps {
	id: number | string
}

export const ReportDesignerViewer: FC<ReportDesignerViewerProps> = (props) => {
	useNavigation(props, `/${props.id}/design`);
	// const [setError] = useState<Error | unknown>(null);
	const viewerRef = React.useRef<ReportViewer>(null);
	const [report, setReport] = useState<ReportDescriptor | null>(null);
	const [mode, setMode] = useState<AppMode>("designer");

	const onPrint = async () => {
		try {
			generateAndPrintReport(report as ReportDescriptor, "browser", {}, {})
		} catch (error) {
			console.error("An error occurred:", error);
		}
	};

	const onQuery = async () => {
		const r = await fetchFormData("report", props.id)
		if (!r.success) {
			return;
		}
		const opts = {
			action: 'FlyoutCardSubformView',
			form: 'view_report_query',
			preset: {
				query: Object.values(r.data.query || {})
			}
		}
		window.Flyout.open(opts).done((d) => {
			if (!Array.isArray(d?.values?.query)) {
				return
			}
			const bdy: any = {}
			for (const item of d.values.query) {
				bdy[item.code] = item;
			}
			request({
				url: `/form/report/${props.id}`,
				method: "PUT",
				data: { query: bdy },
			}).then(() => {
				toast({ type: 'success', position: "bottom-center", theme: "dark", message: 'Queries updated..', autoClose: 2000 });
			}).catch(() => {
				toast({ type: 'error', position: "bottom-center", theme: "dark", message: 'Error creating shipment' });
			})

		})

	}

	const onCreateReport = () => {
		const reporName = report?.name || "New Report";

		const CPLReport = {
			Name: reporName,
			Body: {
				Width: "11in",
				Height: "11in",
			},
			Page: {
				PageWidth: report?.json_data?.Page?.PageWidth,
				PageHeight: report?.json_data?.Page?.PageHeight
			}
		};
		return Promise.resolve({
			definition: CPLReport,
			id: props.id,
			displayName: reporName,
		});
	};

	const onSaveReport = async (info: SaveNewReportInfo) => {
		window.prettyNotify("Saving Report...")
		const definition = info.definition;
		const body: ReportDescriptor & { json_data: Report } = {
			...report,
			json_data: definition // Assign the json_data property
		};

		if (definition.ReportParameters) {
			definition.ReportParameters.forEach((param, index) => {
				body[`parameter_name_${index + 1}`] = param.Name;
				body[`parameter_prompt_${index + 1}`] = param.Name;
			});
		}
		setReport(body);
		updateReport(props.id, body).then((data) => {
		}).catch(_error => Promise.reject({ success: false, error: _error })).finally(() => {
			window.prettyNotify("")
		});
		return Promise.resolve({ displayName: report?.name });

	};

	const onSaveAsReport = async (info: SaveNewReportInfo) => {
		window.prettyNotify("Saving Report...")
		const definition = info.definition;
		const body: ReportDescriptor = {
			json_data: definition // Assign the json_data property
		};
		if (definition.ReportParameters) {
			definition.ReportParameters.forEach((param, index) => {
				body[`parameter_name_${index + 1}`] = param.Name;
				body[`parameter_prompt_${index + 1}`] = param.Prompt ? param.Prompt : "";
			});
		}
		updateReport(props.id, body).then((data) => {
		}).catch(_error => Promise.reject({ success: false, error: _error })).finally(() => {
			window.prettyNotify("")
		});
		return Promise.resolve({ id: props.id, displayName: report?.name });
	};
	const openReport = useCallback(async (id: number | string) => {
		try {
			window.prettyNotify("Loading Report...")
			let data: ReportDescriptor = await getReportInfo(id) as ReportDescriptor;
			if (!data.json_data || Object.keys(data.json_data).length === 0) {
				data = await getReportTemplate(data.type) as ReportDescriptor;
			}
			setReport(data);
			window.prettyNotify("");
			return Promise.resolve();
		} catch (err) {
			console.log(err);
			window.prettyNotify("");
			return Promise.reject(err);
		}
	}, []);

	useEffect(() => {
		openReport(props.id);
	}, []);


	const onRenderPreview = (info: any) => {
		const definition = info.definition;
		const body: ReportDescriptor & { json_data: Report } = {
			...report,
			json_data: definition // Assign the json_data property
		};

		if (definition.ReportParameters) {
			definition.ReportParameters.forEach((param, index) => {
				body[`parameter_name_${index + 1}`] = param.Name;
				body[`parameter_prompt_${index + 1}`] = param.Name;
			});
		}
		setReport(body);
		setMode("viewer");
		return Promise.resolve();
	};

	return (
		<>

			{
				mode == "designer" && (
					<div id="viewer-host">
						{
							report?.json_data && Object.keys(report.json_data).length > 0 ? <Designer report={report} onSave={onSaveReport} onSaveAs={onSaveAsReport} onRender={onRenderPreview} onQuery={onQuery} /> :
								<Designer onCreate={onCreateReport} onSave={onSaveReport} onSaveAs={onSaveAsReport} onRender={onRenderPreview} onQuery={onQuery} />
						}
					</div>

				)
			}
			{
				mode == "viewer" && report && (
					<div id="viewer-host">
						<Viewer report={report} sidebarVisible={true} toolbarVisible={true} zoom="FitPage" onEdit={() => setMode("designer")} onPrint={onPrint} />
					</div>)
			}

		</>
	);
};