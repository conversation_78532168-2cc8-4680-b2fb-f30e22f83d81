import React, { useEffect, useMemo } from "react";
import { NestedTabView } from "@blocks/nested-tab-view/nested-tab-view";
import { useNavigation } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import { SalesAccountSnapshot } from "./userentity-snapshot/sales-account-snapshot";
import "./sales.less";
import { DSLTabView } from "@blocks/dsl-tab-view/dsl-tab-view";
import { reports } from "./reports/index";

export const Sales = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/sales");
	useEffect(() => {
		if (props.navToURL?.startsWith("/sales")) {
			props.setActiveTab?.("sales");
		}
	}, [nav.globalNav.lrct]);

	const map = useMemo(() => [
		{
			label: "Sales Reps",
			id: "sales_reps",
			path: "/sales/sales_reps",
			component: {
				renderComponent: reports.SalesRepReports,
			}
		},
		{
			label: "My Sales",
			id: "my_sales",
			path: "/sales/my_sales",
			component: {
				renderComponent: reports.MySalesReport,
			}
		},
		{
			label: "Account",
			id: "account",
			path: "/sales/account",
			view: [
				{
					label: "Care Candidate",
					id: "care_candidate",
					path: "/sales/account/care_candidate",
					component: {
						renderComponent: DSLTabView,
						componentProps: {
							tabLabel: "List",
							initialAsAvatar: true,
							newTabLabel: "Care Candidate",
							viewMode: "snap",
							multiMode: "id_form_mode",
							tabRenderComponents: {
								"read": SalesAccountSnapshot,
								type: "patient"
							},
							form: "sales_account",
							filtersPresetFixed: {
								"type": "Candidate",
							},
							newPreset: {
								"type": "Candidate",
							}
						},
					}
				},
				{
					label: "Contact",
					id: "contact",
					path: "/sales/account/contact",
					component: {
						renderComponent: DSLTabView,
						componentProps: {
							initialAsAvatar: true,
							tabLabel: "List",
							newTabLabel: "Contact",
							viewMode: "snap",
							multiMode: "id_form_mode",
							tabRenderComponents: {
								"read": SalesAccountSnapshot,
								type: "contact"
							},
							form: "sales_account",
							filtersPresetFixed: {
								"type": "Contact",
							},
							newPreset: {
								"type": "Contact",
							}
						},
					}
				},
				{
					label: "Physician",
					id: "physician",
					path: "/sales/account/physician",
					component: {
						renderComponent: DSLTabView,
						componentProps: {
							initialAsAvatar: true,
							tabLabel: "List",
							newTabLabel: "Physician",
							viewMode: "snap",
							multiMode: "id_form_mode",
							tabRenderComponents: {
								"read": SalesAccountSnapshot,
								type: "physician"
							},
							form: "sales_account",
							filtersPresetFixed: {
								"type": "Physician",
							},
							newPreset: {
								"type": "Physician",
							}
						},
					}
				},
				{
					label: "Organization",
					id: "organization",
					path: "/sales/account/organization",
					component: {
						renderComponent: DSLTabView,
						componentProps: {
							tabLabel: "List",
							newTabLabel: "Organization",
							viewMode: "snap",
							multiMode: "id_form_mode",
							tabRenderComponents: {
								"read": SalesAccountSnapshot,
								type: "organization"
							},
							form: "sales_account",
							filtersPresetFixed: {
								"type": "Organization",
							},
							newPreset: {
								"type": "Organization",
							}
						},
					}
				}
			]
		},
		{
			label: "Track",
			id: "track",
			path: "/sales/track",
			view: [
				{
					label: "Call",
					id: "call_log",
					path: "/sales/call_log",
					form: "sales_call_log"
				},
				{
					label: "Tasks",
					id: "sales_task",
					path: "/sales/task",
					form: "sales_task"
				},
			]
		},
		{
			label: "Reports",
			id: "reports",
			path: "/sales/reports",
			view: [
				{
					label: "Drug Dispense",
					id: "drug_dispense",
					path: "/sales/reports/drug_dispense",
					component: {
						renderComponent: reports.DrugDispenseReport,
					}
				},
				{
					label: "Patient Billed",
					id: "patient_billed",
					path: "/sales/reports/patient_billed",
					component: {
						renderComponent: reports.PatientBilledReport,
					}
				},
				{
					label: "Missing Information",
					id: "missing_information",
					path: "/sales/reports/missing_information",
					component: {
						renderComponent: reports.MissingInformationReport,
					}
				}
			]
		},
		{
			label: "Manage",
			id: "manage",
			path: "/sales/manage",
			view: [
				{
					label: "Territories",
					id: "territories",
					path: "/sales/manage/territories",
					form: "sales_territory"
				},
				{
					label: "Goals",
					id: "goals",
					path: "/sales/manage/goals",
					form: "sales_rep_goals"
				}
			]
		}
	], []);

	const tabLevelProps = { levels: {}, dsl: {} };

	return (
		<NestedTabView {...{ ...props, ...nav, map, tabLevelProps }} />
	);
};