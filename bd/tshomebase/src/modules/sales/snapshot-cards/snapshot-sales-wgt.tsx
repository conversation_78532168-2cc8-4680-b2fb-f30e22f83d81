import type { FC } from "react";
import React from "react";
import "@components/wgts/snapshot-wgt/snapshot-wgt.less";
import icons from "@public/icons";
import { useToggle } from "usehooks-ts";
import { WgtData } from "@components/wgts/types";
import { edit } from "@public/icons/actions";
export interface SnapshotWgtProps {
    title: string;
    mode: string;
    action?: () => void;
    children?: React.ReactNode;
}

export const SnapshotSalesWgt: FC<SnapshotWgtProps> = (props) => {
    const { title, mode = 'edit', action, children } = props;
    const iconMap: Record<string, string> = {
        edit: icons.common.editOutline,
        list: icons.common.list,
        add: icons.snap.patient.addform,
    }

    const [show, toggle] = useToggle(true);
    return (
        <div className="wgt">
            <div className={`wgt-header${!show ? ' closed' : ''}`}>
                <div className="title">
                    {title}
                </div>
                <div className="wgt-btns">
                    {
                        action &&
                        mode &&
                        <img
                            className='wgt-ind'
                            src={iconMap[mode]}
                            onClick={action}
                        />
                    }
                    <img className={`wgt-ind ${!show ? "flip" : ""}`} src={icons.common.arrowUpOutline} onClick={toggle} />
                </div>
            </div>
            {show && children}
        </div>
    );
};