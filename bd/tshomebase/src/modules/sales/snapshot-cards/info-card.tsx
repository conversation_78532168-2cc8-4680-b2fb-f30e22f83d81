import "./info-card.less";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import InitialsAvatar from "@components/common/initials-avatar";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import CommonIcons from "@public/icons/common";
import patient from "@public/icons/snap/patient";

export const SalesSnapshotPatientInfo = (props: any) => {
	const { formData, onIconClick } = props;
	return (
		<div className="sales-snapshot-patient-info">
			<div className="user-img">
				{
					false ?
						<img />
						:
						<InitialsAvatar
							name={`${formData?.firstname || ''} ${formData?.lastname || ''}`}
							variant={VARIANT_PRIMARY}
							size={SIZE_XL}
						/>
				}
			</div>
			<div className="sales-patient-info-container">
				<SalesPatientInfo {...props} />
			</div>
		</div>
	);
};

interface InfoCardData {
	lastname?: string;
	firstname?: string;
	middlename?: string;
	status?: string;
	email?: string;
	phone?: string;
	phone_alt?: string;
	phone_office?: string;
	street?: string;
	street2?: string;
	city?: string;
	zip?: string;
	territories_auto_name?: string;
	npi_number?: string;
	npi_number_auto_name?: string;
	inhibitors?: string;
}

interface DataObject {
	name: string | number | null | undefined;
	status: string | number | undefined;
	email: string | number | undefined;
	cell: string | number | undefined;
	work_phone: string | number | undefined;
	address: string | number | undefined;
	territory: string | number | undefined;
	npi: string | number | undefined;
	inhibitors: string | number | undefined;
}

export const SalesPatientInfo = (props: any) => {
	const { tabViewActions, tabData, formData, onIconClick } = props;
	// const patName = (data?.lastname ? data?.lastname : "" + " ") + " "  + (data?.firstname ? data?.firstname : "" + " " ) + " " + (data?.middlename ? data?.middlename : "") ;
	const patAge = formData?.dob ? window.getAge(formData.dob) : '';
	const patGender = formData?.gender ? formData?.gender == 'Male' ? 'M' : 'F' : "";
	const patDob = formData?.dob ? window.moment(formData.dob, "MM/DD/YYYY").format("MM/DD/YYYY") : '';
	const dataObj: DataObject = {
		name: `${formData.firstname ?? ""} ${formData.lastname ?? ""}  ${formData.middlename ?? ""}`.trim() || null,
		status: formData.status ?? null,
		email: formData.email ?? null,
		cell: formData.phone ?? formData.phone_alt ?? null,
		work_phone: formData.phone_office ?? null,
		address: `${formData.street ?? ""} ${formData.street2 ?? ""} ${formData.city ?? ""} ${formData.zip ?? ""}`.trim() || undefined,
		territory: formData.territories_auto_name ? formData.territories_auto_name.filter(Boolean).join(', ') : null,
		npi: formData.npi_number_auto_name ?? null,
		inhibitors: formData.inhibitors ?? null,
	};
	return (
		<div className="sales-patient-info">
			<div className="info-header">
				<p className="patient-name">{dataObj.name}</p>
				<div className="age-dob">
					<p className="age-gender">{patAge}/{patGender}</p>
					<p className="dob">{patDob}</p>
				</div>
				<p className="pat-status">Status
					{dataObj.status ? <span className="pat-status-stage">{dataObj.status}</span> : ""}
				</p>
				<div className="edit-btn" onClick={() => tabViewActions.openTab(tabData.id, tabData.label, "edit", {})}>
					<img src={icons.snap.patient.editsm} />
				</div>
			</div>
			<div className="info-lower-section">
				<div className="info-lower-section-content">
					<PatientDetailPortion icon={CommonIcons.phoneOutlineActive} label="CELL" styleClass="fw" value={renderItemData(dataObj.cell)} />
					<PatientDetailPortion icon={CommonIcons.locationOutlineActive} label="ADDRESS" value={renderItemData(dataObj.address)} />
					<PatientDetailPortion icon={patient.blank} label="NPI" value={renderItemData(dataObj.npi ?? "")} />



				</div>
				<div className="info-lower-section-content">
					<PatientDetailPortion icon={CommonIcons.callOutlineActive} label="WORK" line2={renderItemData(dataObj.work_phone)} />
					<PatientDetailPortion icon={CommonIcons.locationOutlineActive} label="TERRITORY" value={renderItemData(dataObj.territory)} />
					<PatientDetailPortion icon={patient.blank} label="Inhibitors" value={renderItemData(dataObj.inhibitors)} />

				</div>
				<div className="info-lower-section-content">
					<PatientDetailPortion icon={icons.form.emailActive} label="EMAIL" line2={renderItemData(dataObj.email)} />
					<PatientDetailPortion icon={icons.form.calenderActive} label="DATE ADDED" value='07/02/2023' />
					<PatientDetailPortion />
				</div>
			</div>
		</div>
	);
};