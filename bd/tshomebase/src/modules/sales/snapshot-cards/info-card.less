// Variables
@import (reference) "../../../less/style/main.less";

// Mixins
.flex-center() {
	display: flex;
	align-items: center;
	justify-content: center;
}

.text-truncate() {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

// Breakpoints
@screen-sm: 480px;
@screen-md: 768px;
@screen-lg: 1024px;
@screen-xl: 1200px;

.sales-snapshot-patient-info {
	display: flex;
	background-color: @white;
	border-radius: 10px;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	padding: 24px;
	position: relative;
	gap: 24px;
	transition: all 0.3s ease;

	.user-img {
		flex-shrink: 0;
		width: 95px;
		height: 95px;
		border-radius: 50%;
		overflow: hidden;
		.flex-center();

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.sales-patient-info-container {
		display: flex;
		flex-direction: column;
		flex: 1;
		min-width: 0; // Prevents flexbox overflow
	}

	// Responsive adjustments
	@media (max-width: @screen-lg) {
		padding: 20px;
		gap: 20px;

		.user-img {
			width: 80px;
			height: 80px;
		}
	}

	@media (max-width: @screen-md) {
		flex-direction: column;
		align-items: center;
		padding: 16px;

		.user-img {
			width: 120px;
			height: 120px;
		}

		.sales-patient-info-container {
			width: 100%;
		}
	}
}

.sales-patient-info {
	display: flex;
	flex-direction: column;
	gap: 20px;

	.info-header {
		display: flex;
		align-items: flex-start;
		border-bottom: 1px solid #cfcfcf;
		padding-bottom: 16px;
		gap: 16px;
		position: relative;

		.patient-name {
			font-size: 16px;
			font-weight: 600;
			.text-truncate();
			flex: 1;
		}

		.age-dob {
			display: flex;
			align-items: center;
			gap: 8px;
			flex-wrap: nowrap;

			.dob {
				font-weight: 500;
				font-size: 14px;
				color: #58505b;
			}

			.age-gender {
				font-weight: 500;
				font-size: 12px;
				color: #58505b;
				background: #f2f2f2;
				border-radius: 6px;
				padding: 3px 10px;
				white-space: nowrap;
			}
		}

		.pat-status {
			display: flex;
			align-items: center;
			margin-left: auto;
			white-space: nowrap;
			gap: 4px;

			&-stage {
				background-color: #fc7d7d;
				color: @white;
				font-weight: 600;
				padding: 3px 8px;
				border-radius: 3px;
				transition: background-color 0.2s ease;
			}
		}

		.edit-btn {
			padding: 8px;
			cursor: pointer;
			border-radius: 50%;
			transition: all 0.2s ease;
			.flex-center();

			&:hover {
				background-color: rgba(0, 0, 0, 0.05);
			}

			img {
				width: 16px;
				height: 16px;
			}
		}
	}

	.info-lower-section {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 24px;
		padding-top: 16px;

		.info-lower-section-content {
			display: flex;
			flex-direction: column;
			gap: 16px;

			// Common styles for info items
			> div {
				display: flex;
				gap: 12px;
				align-items: flex-start;

				img {
					width: 16px;
					height: 16px;
					margin-top: 2px;
				}

				> div {
					// flex: 1;
					min-width: 0;
					p {
						font-size: 14px;
						color: #333;
						.text-truncate();

						&:hover {
							white-space: normal;
							word-wrap: break-word;
						}
					}
					.label {
						display: block;
						font-size: 12px;
						color: #5a5958;
						margin-bottom: 4px;
						font-weight: 500;
					}
				}
			}
		}
	}

	// Tablet styles
	@media (max-width: @screen-lg) {
		.info-header {
			gap: 12px;

			.patient-name {
				font-size: 15px;
			}
		}

		.info-lower-section {
			grid-template-columns: repeat(2, 1fr);
			gap: 20px;
		}
	}

	// Mobile styles
	@media (max-width: @screen-md) {
		gap: 16px;

		.info-header {
			flex-wrap: wrap;
			padding-right: 40px;

			.patient-name {
				width: 100%;
			}

			.age-dob {
				order: 2;
			}

			.pat-status {
				order: 3;
				margin-left: 0;
			}

			.edit-btn {
				position: absolute;
				top: 0;
				right: 0;
			}
		}

		.info-lower-section {
			grid-template-columns: 1fr;
			gap: 16px;
		}
	}

	// Small mobile styles
	@media (max-width: @screen-sm) {
		.info-header {
			.age-dob {
				flex-direction: column;
				align-items: flex-start;
				gap: 4px;
			}
		}

		.info-lower-section {
			.info-lower-section-content > div {
				flex-direction: column;
				gap: 8px;

				img {
					margin-top: 0;
				}
			}
		}
	}
}

// Animations
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

.sales-snapshot-patient-info {
	animation: fadeIn 0.3s ease-in-out;
}
