import React, { useEffect, useState, useRef } from "react";
import "./small-cards.less";
import icons from "@public/icons";
import { DetailPortion, PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { request } from "@core/request/request";
import { useNavigation } from "@core/navigation";
import $ from "jquery";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";
import { renderItemData } from "@utils/fx";
import CommonIcons from "@public/icons/common";

export const SalesSnapshotContactCard = (props) => {
	const { tabViewActions, tabData, newPreset, linkedType } = props;
	const [contacts, setContacts] = useState([]);
	const [entity, setEntity] = useState();
	const [flyoutSave, setFlyoutSave] = useState(false);
	const nav = useNavigation(props, "/");

	const onLink = () => {
		window.Flyout.open({
			form: "sales_account_link",
			preset: newPreset,
			autoRecoverEnabled: false,
			preset_manage:
			{
				entity_contact_id: {
					type: "Contact",
				}
			}
		});
	};

	const openLinkedEntity = (obj) => {
		const arr = entity.filter((a) => {
			if (a.entity_contact_id == obj.id) {
				return a;
			}
		});
		const dfd = window.Flyout.open({
			form: "sales_account_link",
			record: arr[0].id,
			autoRecoverEnabled: false,
			card: "read"
		})
		dfd.done((data) => {
			console.log(data);
		})
		dfd.fail((error) => {
			console.log(error, "cancel");
		})
	};

	const observer = new MutationObserver(() => {
		if (!($("body").hasClass("has-flyout"))) {
			setFlyoutSave((prevFlyoutSave) => !prevFlyoutSave);
		}
	});
	observer.observe(document.getElementById("flyout"), { attributes: true, childList: true, subtree: true });

	useEffect(() => {
		let mounted = true;
		const fetchContactData = async () => {
			try {
				const linkedResp = await request({
					url: `/form/sales_account_link/?filter=${linkedType}:${tabData.id}&filter=type:${newPreset.type}`,
				});
				if (mounted) {
					const linkedData = linkedResp.data;
					setEntity(linkedData);
					const flt = linkedData?.map((a: any) => "filter=id:" + a.entity_contact_id + "&");
					if (flt.length > 0) {
						const resp = await request({
							url: "/form/sales_account/?filter=type:contact&" + flt.join(""),
						});
						const arr = resp.data?.map((obj) => {
							const linkedContact = linkedData?.find((a) => a.entity_contact_id === obj.id);
							if (linkedContact) {
								obj["priority"] = linkedContact.priority;
								obj["relation"] = linkedContact.relation;
								return obj;
							}
						}) || [];
						setContacts(arr);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		};
		fetchContactData();
		return () => (mounted = false);
	}, [flyoutSave]);

	const openLinkedSnapshot = (contact: object) => {
		const url = `/sales/account/contact/${contact?.id}/read`;
		nav.goTo(url);
	};

	return (
		<>
			<div className="card">
				<SnapshotSalesWgt title="Patient Contact" mode="add" action={onLink}>
					<div className="wgt-content flex-row">
						{contacts.length === 0 &&
							<span className="value">N/A</span>
						}
						{contacts.map((contact, i) => {
							// Extract and format values
							const priority = contact?.priority || "";
							const lastName = contact?.lastname || "";
							const firstName = contact?.firstname || "";
							const middleName = contact?.middlename || "";
							const relation = contact.relation ? contact.relation : "";
							const phone = contact?.phone || "";
							const phoneAlt = contact?.phone_alt || "";

							const fullName = `${lastName}, ${firstName} ${middleName}`;

							return (
								<React.Fragment key={i}>
									<div style={{ width: '100%' }}>

										<div className="detail-packet flex-none">
											<div className="flex-row" style={{ justifyContent: 'space-between', cursor: 'pointer' }}>
												<p className="label">{priority}</p>
												<img src={icons.snap.patient.edit} className="edit-icon" alt="Edit" onClick={() => openLinkedEntity(contact)} />
											</div>
											<div className="header-info">
												<span className="value name" onClick={() => openLinkedSnapshot(contact)} style={{ cursor: 'pointer' }}>{fullName}</span>
												<span className="age-dob">
													<span className="age-gender">{relation}</span>
												</span>
											</div>
										</div>

										<div style={{ display: 'flex', gap: '8px' }}>
											<PatientDetailPortion icon={CommonIcons.phoneOutlineActive} label="Cell" value={renderItemData(phone)} styleClass="flex-col fw" />
											<PatientDetailPortion icon={CommonIcons.callOutlineActive} label="Work" value={renderItemData(phoneAlt)} styleClass="flex-col fw" />
										</div>

									</div>
								</React.Fragment>
							);
						})}

					</div>
				</SnapshotSalesWgt>
			</div>
		</>

	);
};