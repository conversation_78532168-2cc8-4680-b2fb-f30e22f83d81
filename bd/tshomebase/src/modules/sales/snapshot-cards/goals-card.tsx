import React from "react";
import "./small-cards.less";
import icons from "@public/icons";
import { DetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotGoalsCard = (props: any) => {
	const openFormHandler = () => {
		window.Flyout.open({
			form: "sales_rep_goals",
			autoRecoverEnabled: false,
		});
	};
	return (
		<>
			<div className="card">
				<SnapshotSalesWgt title="Goals" mode="add" action={openFormHandler}>
					<div className="wgt-content">
						<DetailPortion label="GRAMS OF IG / UNITS OF FACTOR" value="15" styleClass="detail-packet-flex" />
						<DetailPortion label="DOLLAR AMOUNTS" value="$252" styleClass="detail-packet-flex" />
					</div>
				</SnapshotSalesWgt>
			</div>
		</>
	);
};