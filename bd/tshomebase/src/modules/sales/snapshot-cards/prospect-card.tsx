import React from "react";
import "./small-cards.less";
import "./large-cards.less";
import { DetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotProspectCard = (props: any) => {
	const { tabData, formData } = props;

	const removeBraces = (input) => {
		if (Array.isArray(input)) {
			return input.map(str => str?.replace(/{|}/g, ""));
		} else if (typeof input === 'string') {
			return input?.replace(/{|}/g, "");
		} else {
			return input;
		}
	};

	const therapies = removeBraces(formData.therapies);

	return (
		<>
			<div className="card">
				<SnapshotSalesWgt title="Prospect" mode="edit" action={() => props.onIconClick(tabData.id, tabData.label, "edit", {})}>
					<div className="wgt-content">
						<DetailPortion label="GRAMS OF IG / UNITS OF FACTOR" value={formData.ig_grams || '-'} styleClass="detail-packet-flex" />
						<DetailPortion label="INDUSTRY" value={formData.industry || '-'} styleClass="detail-packet-flex" />
						<DetailPortion label="Size" value={
							<span className="activity-btns-container" >
								<span className={`activity-btn ${formData.size == "Local" ? "active-btn" : ""}`}>LOCAL</span>
								<span className={`activity-btn ${formData.size == "Regional" ? "active-btn" : ""}`}>REGIONAL</span>
								<span className={`activity-btn ${formData.size == "National" ? "active-btn" : ""}`}>NATIONAL</span>
							</span>} styleClass="detail-packet-flex"
						/>
						<DetailPortion label="ANNUAL REVENUE" value={formData.revenue || '-'} styleClass="detail-packet-flex" />
						<DetailPortion label="NUMBER OF LOCATIONS" value={formData.locations || '-'} styleClass="detail-packet-flex" />
						<DetailPortion label="THERAPIES" value={therapies || '-'} styleClass="detail-packet-flex" />
					</div>
				</SnapshotSalesWgt>
			</div >
			{/* 
			<div className="card-container large-card">

				<div className="card-header">
					<h5 className="card-heading">PROSPECT</h5>
					<div className="divider" />
					<div className="edit-btn">
						<img src={icons.snap.patient.editsm} onClick={() => props.onIconClick(tabData.id, tabData.label, "edit", {})} />
					</div>
				</div>
				<div className="card-lower-portion">
					{true ? <DetailPortion label="GRAMS OF IG / UNITS OF FACTOR" value={formData.ig_grams} styleClass="detail-packet-flex" /> : ""}
					{true ? <DetailPortion label="INDUSTRY" value={formData.industry} styleClass="detail-packet-flex" /> : ""}
					<DetailPortion label="Size" value={
						<span className="activity-btns-container" >
							<span className={`activity-btn ${formData.size == "Local" ? "active-btn" : ""}`}>LOCAL</span>
							<span className={`activity-btn ${formData.size == "Regional" ? "active-btn" : ""}`}>REGIONAL</span>
							<span className={`activity-btn ${formData.size == "National" ? "active-btn" : ""}`}>NATIONAL</span>
						</span>} styleClass="detail-packet-flex"
					/>
					{<DetailPortion label="ANNUAL REVENUE" value={formData.revenue || '-'} styleClass="detail-packet-flex" />}
					{true ? <DetailPortion label="NUMBER OF LOCATIONS" value={formData.locations || '-'} styleClass="detail-packet-flex" /> : ""}
					{true ? <DetailPortion label="THERAPIES" value={therapies || '-'} styleClass="detail-packet-flex" /> : ""}
				</div>
			</div> */}
		</>
	);
};