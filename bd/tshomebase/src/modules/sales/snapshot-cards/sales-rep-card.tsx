import React from "react";
import "./small-cards.less";
import { DetailPortion, PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { useFormData } from "@hooks/form-data";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotSalesRepCard = (props) => {
	const { tabData, formData } = props;
	return (
		<>
			<div className="stacked-cards">

				<div className="stacked-card-30">
					<SnapshotSalesWgt title="Sales Rep" mode="edit" action={() => props.onIconClick(tabData.id, tabData.label, "edit", {})} >
						<div className="wgt-content">
							<p className="name">{formData.sales_rep_auto_name || "-"}</p>
						</div>
					</SnapshotSalesWgt>
				</div>
				<div className="stacked-card-70">
					<SnapshotSalesWgt title='Opportunity' mode="edit" action={() => props.onIconClick(tabData.id, tabData.label, "edit", {})} >
						<div className="wgt-content">
							{formData.status == "Opportunity" ?
								<React.Fragment>
									{formData.likelihood ?
										<div className="activity-btns-container p-2">
											<p className={`activity-btn ${formData.likelihood == "Low" ? "active-btn" : ""}`}>Low</p>
											<p className={`activity-btn ${formData.likelihood == "Medium" ? "active-btn" : ""}`}>Medium</p>
											<p className={`activity-btn ${formData.likelihood == "High" ? "active-btn" : ""}`}>High</p>
										</div> : "N/A"
									}
								</ React.Fragment>
								: ""}
							{formData.close_date && <PatientDetailPortion label="Target Close Date" value={formData.close_date || ""} icon={icons.form.calenderActive} styleClass="flex-col p-2" />}
						</div>
					</SnapshotSalesWgt>
				</div>
			</div>

		</>
	);
};