@import (reference) "../../../less/style/main.less";

.card-container {
  // display: flex;
  min-height: 120px !important;
  max-height: 175px;
  overflow-y: auto;
  flex: 1 0 24%;
  min-width: 200px !important;
  flex-direction: column;
  width: 25%;
  background-color: @white;
  border: 1px solid #cfcfcf;
  border-radius: 10px;
  padding: 10px 15px;

  .card-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-heading {
    font-weight: 600;
  }

  .divider {
    flex-grow: 1;
    height: 1px;
    background-color: #b7b7b7;
    margin: 0 10px;
  }

  .edit-btn {
    cursor: pointer;
  }

  .card-lower-section {
    display: flex;
    flex-direction: column;

    .btns-container {
      display: flex;
      align-items: center;

      .entity_btns {
        cursor: pointer;
        display: flex;
        gap: 5px;
      }

      .my_input {
        position: absolute;
      }

      .dropdown {
        position: relative;
      }
    }

    .custom-table {
      font-size: 12px;
      border-collapse: collapse;
      width: 100%;

      .table-header {
        border: 1px solid #bebebe;

        th {
          color: #42437b;
          padding: 5px;
          font-weight: 600;
        }
      }

      tbody {
        tr {
          font-weight: 400;
        }

        .table-row-odd {
          background-color: #fafafa;
        }

        .table-cell {
          color: #254d5b;
          padding: 5px;
        }
      }
    }
  }

  .activity-btns-container {
    display: flex;
    flex-direction: row;

    .activity-btn {
      background-color: #efefef;
      padding: 2px 4px;
      border-radius: 3px;
      margin: 3px;
      width: 80px;
      text-align: center;
      cursor: default;
    }

    .active-btn {
      background-color: @dark-gray;
      color: @white;
    }
  }
}

.card-lower-portion {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 0;
  background-color: #fbfbfb;
}

.sales-snapshot-cards-sub-section {

  .wgt-header {
    .title {
      font-weight: 500;
    }
  }

  .wgt-content {
    background-color: #fbfbfb;
    border-top: 1px solid #e0e0e0;
    border-radius: 0 0 12px 12px;
    flex-wrap: wrap;
    gap: 12px;
    // padding: 24px;
    display: flex;
    box-shadow: 0 1px 4px #18100a14;
    flex-direction: column;
  }

  .edit-icon {
    cursor: pointer;
    width: 14px;
  }

  .genric-card {
    display: flex;
    gap: 10px;
    flex: 0 1 45%;
  }

  .small-card {
    .card-container;
    min-height: 359px !important;
    max-height: 359px !important;
    overflow: auto;
    background-color: transparent;
    border: none;
    padding: 0;
    display: flex;
    flex: 0 1 27%;
  }

  .wgt {
    height: 100%;

    .wgt-content {
      height: 100%;
      overflow-x: auto;
    }
  }

  .age-dob {
    display: flex;
    gap: 5px;

    .dob {
      font-weight: 500;
      font-size: 14px;
      color: #58505b;
      align-content: center;
    }

    .age-gender {
      font-weight: 500;
      font-size: 12px;
      color: #58505b;
      background: #f2f2f2;
      border-radius: 6px;
      padding: 3px 10px;
    }

    .name {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .flex-none {
    flex: none !important;
  }
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row !important;
}