import React, { useEffect, useState } from "react";
import icons from "@public/icons";
import { DetailPortion, PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import "./small-cards.less";
import CommonIcons from "@public/icons/common";
import { request } from "@core/request/request";
import { useNavigation } from "@core/navigation";
import $ from "jquery";
import { renderItemData } from "@utils/fx";

import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotPhysicianCard = (props) => {
	const { tabData, newPreset, linkedType } = props;
	const [entity, setEntity] = useState();
	const [physician, setPhysician] = useState([]);
	const [flyoutSave, setFlyoutSave] = useState(false);
	const nav = useNavigation(props, "/");

	const onLink = () => {
		window.Flyout.open({
			form: "sales_account_link",
			preset: newPreset,
			autoRecoverEnabled: false,
			preset_manage:
			{
				entity_physician_id: {
					type: "Physician",
				}
			}
		});
	};

	const openLinkedEntity = (obj) => {
		const arr = entity.filter((a) => {
			if (a.entity_physician_id == obj.id) {
				return a;
			}
		});
		window.Flyout.open({
			form: "sales_account_link",
			record: arr[0].id,
			card: "read",
			autoRecoverEnabled: false,
		});
	};

	useEffect(() => {
		let mounted = true;
		const fetchPhysicianData = async () => {
			try {
				const linkedResp = await request({
					url: `/form/sales_account_link/?filter=${linkedType}:${tabData.id}&filter=type:${newPreset.type}`,
				});
				if (mounted) {
					const linkedData = linkedResp.data;
					setEntity(linkedData);
					const flt = linkedData?.map((a: any) => "filter=id:" + a.entity_physician_id + "&");
					if (flt.length > 0) {
						const resp = await request({
							url: "/form/sales_account/?filter=type:physician&" + flt.join(""),
						});
						const arr = resp.data?.map((obj) => {
							const linkedPhy = linkedData?.find((a) => a.entity_physician_id === obj.id);
							if (linkedPhy) {
								obj["priority"] = linkedPhy.priority;
								obj["relation"] = linkedPhy.relation;
								return obj;
							}
						}) || [];
						setPhysician(arr);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		};
		fetchPhysicianData();
		return () => (mounted = false);
	}, [flyoutSave]);

	const openLinkedSnapshot = (obj: object) => {
		const url = `sales/account/physician/${obj?.id}/read`;
		nav.goTo(url);
	};

	const observer = new MutationObserver(() => {
		if (!($("body").hasClass("has-flyout"))) {
			setFlyoutSave((prevFlyoutSave) => !prevFlyoutSave);
		}
	});
	observer.observe(document.getElementById("flyout"), { attributes: true, childList: true, subtree: true });

	return (
		<>
			<div className="card">
				<SnapshotSalesWgt title="Physician / Group" mode="add" action={onLink}>
					<div className="wgt-content flex-row">
						{physician.map((contact, index) => {
							const priority = contact?.priority || "";
							const lastName = contact?.lastname || "";
							const firstName = contact?.firstname || "";
							const middleName = contact?.middlename || "";
							const relation = contact.relation ? contact.relation : "";
							const phone = contact?.phone || "";
							const phoneAlt = contact?.phone_alt || "";
							const fullName = `${lastName}, ${firstName} ${middleName}`;

							return (<React.Fragment key={index}>
								<div style={{ width: '100%' }}>
									<div className="detail-packet flex-none">
										<div className="flex-row" style={{ justifyContent: 'space-between', cursor: 'pointer' }}>
											<p className="label">{priority}</p>
											<img src={icons.snap.patient.edit} className="edit-icon" alt="Edit" onClick={() => openLinkedEntity(contact)} />
										</div>
										<div className="header-info">
											<span className="value name mb-2" onClick={() => openLinkedSnapshot(contact)} style={{ cursor: 'pointer' }}>{fullName}</span>
											{/* <span className="age-dob">
													<span className="age-gender">{relation}</span>
												</span> */}
										</div>
									</div>
									<div style={{ display: 'flex', gap: '8px' }}>
										<PatientDetailPortion icon={CommonIcons.phoneOutlineActive} label="Cell" value={renderItemData(phone)} styleClass="flex-col fw" />
										<PatientDetailPortion icon={CommonIcons.callOutlineActive} label="Work" value={renderItemData(phoneAlt)} styleClass="flex-col fw" />
									</div>
								</div>
							</React.Fragment>)
						})}
					</div>
				</SnapshotSalesWgt>
			</div>
		</>

	);
};