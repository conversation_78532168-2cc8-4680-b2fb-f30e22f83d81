import React from "react";
import "./small-cards.less";
import "./large-cards.less";
import { DetailPortion, PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotDiagnosisCard = (props: any) => {
	const { tabData, formData, tabViewActions } = props;
	let p = 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Iste, molestias nemo quisquam provident ratione distinctio accusantium! Modi minima ut voluptatem possimus pariatur, voluptate tenetur quae? Error quo aperiam velit similique.'
	const diagnosisData = {
		dx1: formData.dx_1_auto_name || "-" || p,
		dx2: formData.dx_2_auto_name || "-" || p,
		dx3: formData.dx_3_auto_name || "-" || p,
		dx4: formData.dx_4_auto_name || "-" || p,
		dx5: formData.dx_5_auto_name || "-" || p,
	};
	return (
		<div className="card">
			<SnapshotSalesWgt title="DIAGNOSIS" mode="edit" action={() => props.onIconClick(tabData.id, tabData.label, "edit", {})}>
				<div className="wgt-content">
					<DetailPortion label="PRIMARY DX" value={diagnosisData.dx1} styleClass="detail-packet-flex" />
					<DetailPortion label="SECONDARY DX" value={diagnosisData.dx2} styleClass="detail-packet-flex" />
					<DetailPortion label="TERTIARY DX" value={diagnosisData.dx3} styleClass="detail-packet-flex" />
					<DetailPortion label="QUATERNARY DX" value={diagnosisData.dx4} styleClass="detail-packet-flex" />
					<DetailPortion label="QUINARY DX" value={diagnosisData.dx5} styleClass="detail-packet-flex" />
				</div>
			</SnapshotSalesWgt>
		</div >
	);
};