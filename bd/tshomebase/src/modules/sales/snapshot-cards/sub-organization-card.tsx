import React, { useEffect, useState } from "react";
import "./small-cards.less";
import { DetailPortion, PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { request } from "@core/request/request";
import { useNavigation } from "@core/navigation";
import $ from "jquery";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";
import { renderItemData } from "@utils/fx";
import CommonIcons from "@public/icons/common";


export const SalesSnapshotSubOrganizationCard = (props) => {
	const { tabViewActions, tabData, newPreset, linkedType } = props;
	const [entity, setEntity] = useState();
	const [organization, setOrganization] = useState([]);
	const [flyoutSave, setFlyoutSave] = useState(false);
	const nav = useNavigation(props, "/");

	const onLink = () => {
		window.Flyout.open({
			form: "sales_account_link",
			preset: newPreset,
			autoRecoverEnabled: false,
		});
	};

	const openLinkedEntity = (obj) => {
		const arr = entity.filter((a) => {
			if (a.entity_organization_id == obj.id) {
				return a;
			}
		});
		window.Flyout.open({
			form: "sales_account_link",
			record: arr[0].id,
			card: "read",
			autoRecoverEnabled: false,
		});
	};

	const observer = new MutationObserver(() => {
		if (!($("body").hasClass("has-flyout"))) {
			setFlyoutSave(!flyoutSave);
		}
	});
	observer.observe(document.getElementById("flyout"), { attributes: true, childList: true, subtree: true });

	useEffect(() => {
		let mounted = true;
		const fetchOrganizationData = async () => {
			try {
				const linkedResp = await request({
					url: `/form/sales_account_link/?filter=entity_organization_id:${tabData.id}&filter=type:organization_suborganization`,
				});
				if (mounted) {
					const linkedData = linkedResp.data;
					setEntity(linkedData);
					const flt = linkedData?.map((a: any) => "filter=id:" + a.entity_sub_organization_id + "&");
					if (flt.length > 0) {
						const resp = await request({
							url: "/form/sales_account/?filter=type:organization&" + flt.join(""),
						});
						const arr = resp.data?.map((obj) => {
							const linkedOrg = linkedData?.find((a) => a.entity_sub_organization_id === obj.id);
							if (linkedOrg) {
								obj["priority"] = linkedOrg.priority;
								obj["relation"] = linkedOrg.relation;
								return obj;
							}
						}) || [];
						setOrganization(arr);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		};
		fetchOrganizationData();
		return () => (mounted = false);
	}, [flyoutSave]);

	const openLinkedSnapshot = (obj: object) => {
		const url = `sales/account/organization/${obj?.id}/read`;
		nav.goTo(url);
	};

	return (
		<>
			<div className="card">
				<SnapshotSalesWgt title="Sub Organization" mode="add" action={onLink}>
					<div className="wgt-content flex-row">
						{organization.map((org, i) => {
							// Extract and format values
							const priority = org?.priority || "";
							const relation = org.relation ? org.relation : "";
							const phone = org?.phone || "";
							const phoneAlt = org?.phone_alt || "";
							const name = org?.name;

							return (
								<React.Fragment key={i}>
									<div style={{ width: '100%' }}>

										<div className="detail-packet flex-none">
											<div className="flex-row" style={{ justifyContent: 'space-between', cursor: 'pointer' }}>
												<p className="label">{priority}</p>
												<img src={icons.snap.patient.edit} className="edit-icon" alt="Edit" onClick={() => openLinkedEntity(org)} />
											</div>
											<div className="header-info">
												<span className="value name" onClick={() => openLinkedSnapshot(org)} style={{ cursor: 'pointer' }}>{name}</span>
												<span className="age-dob">
													<span className="age-gender">{relation}</span>
												</span>
											</div>
										</div>

										<div style={{ display: 'flex', gap: '8px' }}>
											<PatientDetailPortion icon={CommonIcons.phoneOutlineActive} label="Cell" value={renderItemData(phone)} styleClass="flex-col fw" />
											<PatientDetailPortion icon={CommonIcons.callOutlineActive} label="Work" value={renderItemData(phoneAlt)} styleClass="flex-col fw" />
										</div>

									</div>
								</React.Fragment>
							);
						})}

					</div>
				</SnapshotSalesWgt>
			</div>
			{/* <div className="card-container" >
				<div className="card-header">
					<h5 className="card-heading">
						SUB ORGANIZATION
					</h5>
					<div className="divider" />
					<div className="edit-btn" onClick={onLink}>
						<img src={icons.snap.patient.addform} />
					</div>
				</div>
				<div className="card-lower-section">
					{organization.map((obj, index) => (
						<React.Fragment key={index}>
							<div className="btns-container">
								<div>
									<DetailPortion onClick={() => openLinkedSnapshot(obj)} label={obj?.priority || ""} value={(obj?.lastname || "") + ", " + (obj?.firstname || "") + (obj?.middlename || "") + " " + (obj?.relation ? `(${obj?.relation})` : "")} styleClass="detail-packet-flex detail-heading" />
								</div>
								<div className="divider" />
								<div className="entity_btns">
									<div onClick={() => openLinkedEntity(obj)}><img src={icons.snap.sales.link} /></div>
								</div>
							</div>
							<DetailPortion label="WORK 1" value={obj?.phone || ""} styleClass="detail-packet-flex" />
							<DetailPortion label="WORK 2" value={obj?.phone_alt || ""} styleClass="detail-packet-flex" />
							<br />
						</React.Fragment>
					))}
				</div>
			</div> */}
		</>
	);
};