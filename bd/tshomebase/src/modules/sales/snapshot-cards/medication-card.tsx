import React from "react";
import "./large-cards.less";
import "./small-cards.less";
import { DetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotMedicationCard = (props: any) => {
	const { tabData, formData } = props;
	return (
		<>
			<div className="stacked-cards">
				<div className="stacked-card-60">
					<SnapshotSalesWgt title="Medication" mode="edit" action={() => props.onIconClick(tabData.id, tabData.label, "edit", {})}>
						<div className="wgt-content">
							<p className="value">{formData.medication_auto_name || "-"}</p>
						</div>
					</SnapshotSalesWgt>
				</div>

				<div className="stacked-card-40">
					<SnapshotSalesWgt title="Allergies" mode="edit" action={() => props.onIconClick(tabData.id, tabData.label, "edit", {})}>
						<div className="wgt-content">
							<p className="value">{formData.allergies_auto_name || "-"}</p>
						</div>
					</SnapshotSalesWgt>
				</div>
			</div >
		</>
	);
};