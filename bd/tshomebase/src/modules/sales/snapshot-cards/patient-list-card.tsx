import React, { useEffect, useState } from "react";
import "./small-cards.less";
import "./large-cards.less";
import icons from "@public/icons";
import { request } from "@core/request/request";
import { useNavigation } from "@core/navigation";
import { DetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { SnapshotSalesWgt } from "@modules/sales/snapshot-cards/snapshot-sales-wgt";

export const SalesSnapshotPatientListCard = (props: any) => {
	const { tabViewActions, tabData, newPreset, linkedType } = props;
	const [entity, setEntity] = useState();
	const [candidate, setCandidate] = useState([]);
	const [flyoutSave, setFlyoutSave] = useState(false);
	const nav = useNavigation(props, "/");

	const onLink = () => {
		window.Flyout.open({
			form: "sales_account_link",
			preset: newPreset,
			autoRecoverEnabled: false,
		});
	};

	const observer = new MutationObserver(() => {
		if (!($("body").hasClass("has-flyout"))) {
			setFlyoutSave((prevFlyoutSave) => !prevFlyoutSave);
		}
	});
	observer.observe(document.getElementById("flyout"), { attributes: true, childList: true, subtree: true });

	useEffect(() => {
		let mounted = true;
		const fetchOrganizationData = async () => {
			try {
				const linkedResp = await request({
					url: `/form/sales_account_link/?filter=${linkedType}:${tabData.id}&filter=type:${newPreset.type}`,
				});
				if (mounted) {
					const linkedData = linkedResp.data;
					setEntity(linkedData);
					const flt = linkedData?.map((a: any) => "filter=id:" + a.entity_patient_id + "&");
					if (flt.length > 0) {
						const resp = await request({
							url: "/form/sales_account/?filter=type:candidate&" + flt.join(""),
						});
						const arr = resp.data?.map((obj) => {
							const linkedPatient = linkedData?.find((a) => a.entity_patient_id === obj.id);
							if (linkedPatient) {
								obj["priority"] = linkedPatient.priority;
								obj["relation"] = linkedPatient.relation;
								return obj;
							}
						}) || [];
						setCandidate(arr);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		};
		fetchOrganizationData();
		return () => (mounted = false);
	}, [flyoutSave]);

	const openLinkedSnapshot = (obj) => {
		const url = `sales/account/care_candidate/${obj.id}/read`;
		nav.goTo(url);
	};

	return (
		<>
			<div className="card">
				<SnapshotSalesWgt title="Candidate List" mode="add" action={onLink}>
					<div className="wgt-content">
						<table className="custom-table">
							<thead className="table-header">
								<tr>
									<th className="table-cell">CANDIDATE NAME</th>
									<th className="table-cell">PHONE</th>
									<th className="table-cell">SEX</th>
									<th className="table-cell">STATUS</th>
								</tr>
							</thead>
							<tbody>
								{candidate.map((obj, index) => (
									<tr className={`table-row ${index % 2 == 0 ? "table-row-odd" : ""}`} key={index} onClick={() => openLinkedSnapshot(obj)}>
										<td className="table-cell">{(obj?.lastname || "") + ", " + (obj?.firstname || "") + (obj?.middlename || "")}</td>
										<td className="table-cell">{obj?.phone || ""}</td>
										<td className="table-cell">{obj?.gender || ""}</td>
										<td className="table-cell">{obj?.status || ""}</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</SnapshotSalesWgt>
			</div>
		</>

	);
};