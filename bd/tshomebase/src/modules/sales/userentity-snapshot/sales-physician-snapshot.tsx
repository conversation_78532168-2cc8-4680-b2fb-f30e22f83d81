import React, { useEffect, useState } from "react";
import "./sales-account-snapshot.less";
import { request } from "@core/request/request";
import { SalesSnapshotDiagnosisCard, SalesSnapshotContactCard, SalesSnapshotPatientInfo, SalesSnapshotMedicationCard, SalesSnapshotOrganizationCard, SalesSnapshotPhysicianCard, SalesSnapshotSalesRepCard, SalesSnapshotPatientListCard, SalesSnapshotProspectCard } from "../snapshot-cards";
import { SalesSnapshotGoalsCard } from "../snapshot-cards/goals-card";
import { useFormData } from "@hooks/form-data";


export const SalesPhysicianSnapshot = (props: any) => {
	const { tabData, id } = props;
	const [formData, refresh] = useFormData(tabData.form, tabData.id);
	return (
		<div className="sales-snapshot">
			<div className="sales-snapshot-container">
				<SalesSnapshotPatientInfo {...props} formData={formData.data} />
				<div className="sales-snapshot-cards-section">
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotProspectCard {...props} formData={formData.data} />
						<SalesSnapshotGoalsCard {...props} formData={formData?.data} />
						<SalesSnapshotSalesRepCard {...props} formData={formData?.data} />
					</div>
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotPatientListCard {...props} newPreset={{ type: "candidate_physician", entity_physician_id: tabData.id }} linkedType="entity_physician_id" />
						<SalesSnapshotOrganizationCard {...props} newPreset={{ type: "physician_organization", entity_physician_id: tabData.id }} linkedType="entity_physician_id" />
						<SalesSnapshotContactCard {...props} newPreset={{ type: "contact_physician", entity_physician_id: tabData.id }} linkedType="entity_physician_id" />

					</div>
				</div>
			</div>
		</div>
	);
};