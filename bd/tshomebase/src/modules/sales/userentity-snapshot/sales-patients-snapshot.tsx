import React, { useEffect, useState } from "react";
import "./sales-account-snapshot.less";
import { SalesSnapshotDiagnosisCard, SalesSnapshotContactCard, SalesSnapshotPatientInfo, SalesSnapshotMedicationCard, SalesSnapshotOrganizationCard, SalesSnapshotPhysicianCard, SalesSnapshotSalesRepCard } from "../snapshot-cards";
import { useFormData } from "@hooks/form-data";

export const SalesPatientSnapshot = (props: any) => {
	const { tabViewActions, tabData, id } = props;
	const [formData, refresh] = useFormData(tabData.form, tabData.id);
	return (
		<div className="sales-snapshot">
			<div className="sales-snapshot-container">
				<SalesSnapshotPatientInfo {...props} formData={formData?.data} />
				<div className="sales-snapshot-cards-section">
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotDiagnosisCard {...props} formData={formData?.data} />
						<SalesSnapshotContactCard {...props} newPreset={{ type: "candidate_contact", entity_patient_id: parseInt(tabData.id) }} linkedType="entity_patient_id" />
						<SalesSnapshotSalesRepCard {...props} formData={formData?.data} />
					</div>
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotMedicationCard {...props} formData={formData?.data} />
						<SalesSnapshotPhysicianCard {...props} newPreset={{ type: "candidate_physician", entity_patient_id: parseInt(tabData.id) }} linkedType="entity_patient_id" />
						<SalesSnapshotOrganizationCard {...props} newPreset={{ type: "candidate_organization", entity_patient_id: parseInt(tabData.id) }} linkedType="entity_patient_id" />
					</div>
				</div>
			</div>
		</div>
	);
};
