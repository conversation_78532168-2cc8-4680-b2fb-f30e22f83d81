
import React, { useState, useRef } from "react";
import "./sales-account-snapshot.less";
import { SalesPatientSnapshot, SalesContactSnapshot, SalesOrganizationSnapshot, SalesPhysicianSnapshot } from "./index";
import { useNavigation } from "@core/navigation";
import { useUpdateHash } from "@hooks/update-hash";
import type { TabData } from "@blocks/tab-list/tab-list";
import icons from "@public/icons";
import { DSLListView } from "@blocks/dsl-list-view/dsl-list-view";
import { TabList, getTabKey } from "@blocks/tab-list/tab-list";
import DSLCardView, { TabModes } from "@blocks/dsl-card-view/dsl-card-view";


export const SalesAccountSnapshot = (props) => {
	const { tabViewActions, tabData, tabRenderComponents } = props;
	const nav = useNavigation(props, `/${props.id}/`);
	const [activeTabId, setActiveTabId] = useState("snapshot");
	const [demographics, demographicsShow] = useState<boolean>(true);
	const [openTabs, setOpenTabs] = useState<TabData[]>(defaultTabs);
	const [updateNeed, updateRefreshHash] = useUpdateHash();

	const dslRefs = useRef<Record<string, unknown>>({});
	const setActiveTab = (tabId: string) => {
		setActiveTabId(tabId);
		if (tabId == "snapshot") {
			demographicsShow(true);
		} else {
			if (demographics) {
				demographicsShow(false);
			}
		}
	};

	const openTab = (id: string, label: string, mode: string, form: string, componentProps: Record<string, unknown>) => {
		id = id + "";
		setOpenTabs((p) => {
			p = Array.from(p);
			if (p.some((t) => t.id == id)) {
				return p;
			}
			p = [...p, { id: id, label: label, mode: mode, componentProps, form }];
			return p;
		});
		setActiveTab(id);
	};

	const onTabCancel = (tab: TabData) => {
		if (tab.form) {
			const rkey = getTabKey(tab.id, tab.form);
			dslRefs[rkey].tab_do("cancel");
			return;
		}
	};

	const closeTab = (id, tab: TabData, ref: unknown) => {
		const rkey = getTabKey(id, ref.form);
		delete dslRefs[rkey];
		setOpenTabs(p => {
			p = Array.from(p);
			p = p.filter((t) => getTabKey(t.id, t.form) != rkey);
			return p;
		});
		setActiveTab("snapshot");
	};

	const onSaved = (fd, tabData, ref) => {
		if (tabData?.gridRef) {
			tabData?.gridRef?.ddg?.reload?.();
		}
		setOpenTabs((p: TabData[]) => {
			let rkey;
			if (ref.xid.toString().includes("-")) { // Add/Addfill Delete Temporary Ref
				rkey = getTabKey(ref.xid, ref.form);
				delete dslRefs[rkey];
			}
			rkey = getTabKey(ref.record, ref.form);
			dslRefs[rkey] = ref;
			const ci = p.findIndex((t) => getTabKey(t.id, t.form) == getTabKey(ref.xid, ref.form));
			return p.map((t, ti) => {
				if (ti == ci) {
					return { ...t, id: fd.record, label: fd.values.auto_name, mode: ref["card" + ref.card].mode, form: ref.form };
				}
				return t;
			});
		});
		setActiveTab(fd.record);
		updateRefreshHash();
	};

	const rowClicked = (event: TabData) => {
		if (event.type == "dblclick") {
			openTab(event.id, "Contact Logs", event.mode, "sales_contact_log", {});

		} else {
			openTab(event.id, "Contact Logs", event.mode, "sales_contact_log", {});
		}
	};

	const rxOpts = {
		onSaved: onSaved,
		onCancel: closeTab,
		rowClicked,
		openTab,
		...nav
	};

	return (
		<div className="sales-account-snapshot">
			{/* <TabList
				activeTabId={activeTabId}
				openTabs={openTabs}
				optionsProps={{
					enabled: false
				}}
				addProps={{
					enabled: false
				}}
				tabCanClose={true}
				draggable={false}
				onTabClick={(tab: TabData) => {
					setActiveTab(tab.id);
				}}
				styles={{
					tabListStyle: "snap-tab-list",
				}}
				onTabClose={(tab: TabData) => {
					onTabCancel(tab);
				}}
			/> */}
			{openTabs.map((tab) => {
				const id = tab.id;
				return (
					<div key={id} className='dsl-container' style={{ display: activeTabId == id ? undefined : "none" }}>
						{!tab.renderComponent ?
							<DSLCardView
								isActive={activeTabId == id}
								isParentActive={props.isActive}
								routePrepend={"/" + tab.form}
								tabData={tab}
								key={id}
								card={(tab.mode || "read") as TabModes}
								xid={id}
								form={tab.form}
								record={["add", "addfill"].includes(tab.mode) ? undefined : id}
								ddRef={(ref) => {
									const rkey = getTabKey(tab.id, tab.form);
									dslRefs[rkey] = ref;
								}}
								{...rxOpts}
								{...tab.componentProps}
							/>
							:
							(
								<>
									{(() => {
										if (tabRenderComponents.type === "patient") {
											return <SalesPatientSnapshot onIconClick={(id, label, mode, { }) => openTab(id, label, mode, "sales_account", {})} {...props} />;
										} else if (tabRenderComponents.type === "contact") {
											return <SalesContactSnapshot {...props} onIconClick={(id, label, mode, { }) => tabViewActions.openTab(id, label, mode, "sales_account", {})} {...props} />;
										} else if (tabRenderComponents.type === "physician") {
											return <SalesPhysicianSnapshot {...props} onIconClick={(id, label, mode, { }) => openTab(id, label, mode, "sales_account", {})} {...props} />;
										} else if (tabRenderComponents.type === "organization") {
											return <SalesOrganizationSnapshot {...props} onIconClick={(id, label, mode, { }) => openTab(id, label, mode, "sales_account", {})} {...props} />;
										}
									})()}
									<div className="contact-logs-form">
										<DSLListView
											{...rxOpts}
											label="Contact Logs"
											form='sales_contact_log' {...rxOpts}
											canAdd={true}
										/>
									</div>
								</>
							)
						}
					</div>
				);
			})}


		</div>
	);
};

const defaultTabs = [{
	tkey: "snapshot",
	id: "snapshot",
	mode: "snap",
	label: "Snapshot",
	canClose: false,
	renderComponent: SalesAccountSnapshot,
	avatar: <img src={icons.snap.patient.userplus} alt="icon" />
}];
