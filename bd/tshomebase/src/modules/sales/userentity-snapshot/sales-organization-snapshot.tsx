import "./sales-account-snapshot.less";
import React, { useState, useEffect } from "react";
import { request } from "@core/request/request";
import {SalesSnapshotContactCard, SalesSnapshotPatientInfo, SalesSnapshotMedicationCard, SalesSnapshotOrganizationCard, SalesSnapshotPhysicianCard, SalesSnapshotSalesRepCard, SalesSnapshotPatientListCard, SalesSnapshotPatientCard, SalesSnapshotProspectCard, SalesSnapshotSubOrganizationCard } from "../snapshot-cards";
import { useFormData } from "@hooks/form-data";

export const SalesOrganizationSnapshot = (props) => {
	const {tabData} = props;
	const [formData, refresh] = useFormData(tabData.form, tabData.id);
	return(
		<div className="sales-snapshot">
			<div className="sales-snapshot-container">
				<SalesSnapshotPatientInfo {...{...props}} formData={formData.data}/>
				<div className="sales-snapshot-cards-section">
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotProspectCard {...props} formData={formData.data} />
						<SalesSnapshotContactCard {...props} newPreset={{type: "contact_organization", entity_organization_id:tabData.id}} linkedType="entity_organization_id" />
						<SalesSnapshotSalesRepCard {...props} formData={formData?.data} />
					</div>
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotPatientListCard {...props} newPreset={{type: "candidate_organization", entity_organization_id:tabData.id}} linkedType="entity_organization_id" />
						<SalesSnapshotPhysicianCard {...props} newPreset={{type: "physician_organization", entity_organization_id:tabData.id}} linkedType="entity_organization_id" />
						<SalesSnapshotSubOrganizationCard {...props} newPreset={{type: "organization_suborganization", entity_organization_id:tabData.id}} linkedType="entity_organization_id" />
					</div>
				</div>
			</div>
		</div>
	);
};