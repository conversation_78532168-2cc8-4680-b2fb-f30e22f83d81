.sales-snapshot {
	display: flex;
	flex-direction: column;
	// padding: 15px 12px;
	max-height: 60%;
	margin-bottom: 10px;
	// background-color: #fbfbfb;
	border-radius: 10px;

	.sales-snapshot-container {
		overflow-y: auto;

		.sales-snapshot-cards-section {
			margin-top: 8px;
			display: flex;
			flex-direction: column;
			gap: 10px;
			.sales-container {
				min-height: 260px;
			}
			.sales-snapshot-cards-sub-section {
				// display: flex;
				// flex-wrap: wrap;
				// flex-direction: row;
				display: grid;
				grid-template-columns: 45% calc(27% - 5px) calc(27% - 5px);
				gap: 11px;
				.card {
					height: 360px;
					width: 100%;
				}
				.stacked-cards {
					height: 360px;
					width: 100%;
					display: flex;
					flex-direction: column;
					gap: 10px;
					.stacked-card-30 {
						height: calc(30% - 5px);
					}
					.stacked-card-40 {
						height: calc(40% - 5px);
					}
					.stacked-card-60 {
						height: calc(60% - 5px);
					}
					.stacked-card-70 {
						height: calc(70% - 5px);
					}
				}
				.detail-packet {
					flex: 1;
					gap: 8px;
					.label {
						font-size: 14px;
						font-weight: 500;
						color: #5a5958;
					}
					.value {
						font-size: 12px;
						font-weight: 500;
						color: #111111;
					}
					.header-info {
						display: flex;
						align-items: center;
						gap: 8px;
					}
				}
			}
		}
	}

	@media (max-width: 1200px) {
		.sales-snapshot-container {
			.sales-snapshot-cards-section {
				.sales-snapshot-cards-sub-section {
					display: flex;
					flex-direction: column;
					gap: 10px;
					// margin-bottom: 10px;

					.card {
						height: auto;
					}
					.stacked-cards {
						height: auto;
						display: flex;
						flex-direction: column;
						gap: 10px;
					}
				}
			}
		}
	}
	@media (min-width: 2000) {
		.sales-snapshot-cards-sub-section {
			grid-template-columns: 45% calc(27.3% - 5px) calc(27.2% - 5px) !important;
		}
	}
}

.sales-account-snapshot {
	display: flex;
	flex-direction: column;
	height: 100%;

	.contact-logs-form {
		min-height: 40%;
		overflow: auto;
	}
}
