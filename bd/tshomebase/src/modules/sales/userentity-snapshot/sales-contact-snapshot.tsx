import React, { useState, useEffect } from "react";
import "./sales-account-snapshot.less";
import { request } from "@core/request/request";
import { SalesSnapshotDiagnosisCard, SalesSnapshotContactCard, SalesSnapshotPatientInfo, SalesSnapshotMedicationCard, SalesSnapshotOrganizationCard, SalesSnapshotPhysicianCard, SalesSnapshotSalesRepCard, SalesSnapshotPatientCard } from "../snapshot-cards";
import { useFormData } from "@hooks/form-data";

export const SalesContactSnapshot = (props) => {
	const { tabViewActions, tabData, id } = props;
	const [formData, refresh] = useFormData(tabData.form, tabData.id);
	return (
		<div className="sales-snapshot">
			<div className="sales-snapshot-container">
				<SalesSnapshotPatientInfo {...{ ...props }} formData={formData?.data} />
				<div className="sales-snapshot-cards-section">
					<div className="sales-snapshot-cards-sub-section">
						<SalesSnapshotPatientCard {...props} newPreset={{ type: "candidate_contact", entity_contact_id: tabData.id }} linkedType="entity_contact_id" />
						<SalesSnapshotSalesRepCard {...props} formData={formData?.data} />
						<SalesSnapshotPhysicianCard {...props} newPreset={{ type: "contact_physician", entity_contact_id: tabData.id }} linkedType="entity_contact_id" />
						<SalesSnapshotOrganizationCard {...props} newPreset={{ type: "contact_organization", entity_contact_id: tabData.id }} linkedType="entity_contact_id" />

					</div>
				</div>
			</div>
		</div>
	);
};