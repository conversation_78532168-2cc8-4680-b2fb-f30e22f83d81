import React, { useEffect, useMemo } from "react";
import { useNavigation } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import "./referral.less";

export const Referral = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/referral");

	useEffect(() => {
		if (props.navToURL?.startsWith("/referral")) {
			props.setActiveTab?.("referral");
		}
	}, [nav.globalNav.lrct]);

	return <div>TODO</div>;
};
