import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react"
import FullCalendar from "@fullcalendar/react"
import dayGridPlugin from "@fullcalendar/daygrid"
import timeGridPlugin from "@fullcalendar/timegrid"
import interactionPlugin from "@fullcalendar/interaction"
import type { EventSourceInput, EventClickArg, DateSelectArg, EventContentArg } from "@fullcalendar/core"
import { Switch, Button } from "antd"
import "./calendar.less"
import { getSchedule, transformData, updateSchedule } from "@modules/schedule/schedule-utils"
import moment from "moment"
import { DSLListView } from "@blocks/dsl-list-view"
import { CRErrorBoundary } from "@blocks/error-boundary"
import { FieldSelect } from "@dsl/fields/field-select"
import { StylesConfig } from "react-select"
import { CalenderPopover } from "./popover/calender-popover"

const viewOptions = [
    { label: "Month", value: "dayGridMonth" },
    { label: "Week", value: "timeGridWeek" },
    { label: "Day", value: "timeGridDay" },
]
interface ScheduleState {
    weekendsVisible: boolean;
    currentEvents: EventSourceInput[] | unknown
}

const selectOptions = {
    status: [
        { label: "Confirmed", value: "Confirmed" },
        { label: "Scheduled", value: "Scheduled" },
        { label: "Need Orders", value: "Need Orders" },
        { label: "Hold/Pending", value: "Hold/Pending" },
        { label: "Missed", value: "Missed" },
        { label: "Canceled", value: "Canceled" },
    ]
}

export function CalendarView() {
    const [state, setState] = useState<ScheduleState>({
        weekendsVisible: true,
        currentEvents: [],
    });
    const calendarRef = useRef<FullCalendar>(null);
    const [dateRange, setDateRange] = useState({
        title: "",
        range: "",
    })
    const [view, setView] = useState("dayGridMonth")
    const [isCondensed, setIsCondensed] = useState(false)
    const [popoverOpen, setPopoverOpen] = useState(false);
    const [event, setEvent] = useState<EventClickArg | null>(null);
    const formatScheduleTime = "hh:mm A";
    const [filters, setFilters] = useState<Record<string, unknown>>({});
    const [activeTab, setActiveTab] = useState<"calendar" | "list">("calendar")
    useEffect(() => {
        fetchSchedule();
    }, [calendarRef, filters]);

    const fetchSchedule = async () => {
        try {
            const calendarApi = calendarRef.current?.getApi();
            const data = await getSchedule(
                "schedule_event",
                calendarApi?.getCurrentData()?.dateProfile.activeRange.start,
                calendarApi?.getCurrentData().dateProfile.activeRange.end,
                filters,
            );
            if (data) {
                const transformedEvents = data.map(transformData);
                setState((prev) => ({ ...prev, currentEvents: transformedEvents }));
            }

        } catch (error) {
            console.error("Error fetching events:", error);
        }
    }

    useEffect(() => {
        const calendarApi = calendarRef.current?.getApi();
        const datesSetHandler = () => {
            fetchSchedule();
        };
        if (calendarApi) {
            calendarApi.on("datesSet", datesSetHandler);
        }
        return () => {
            if (calendarApi) {
                calendarApi.off("datesSet", datesSetHandler);
            }
        };
    }, [fetchSchedule]);


    const openSchedule = (clickInfo: EventClickArg) => {
        const { extendedProps } = clickInfo.event._def;
        setPopoverOpen(true);
        setEvent(clickInfo);
    }

    const addEvent = () => {
        const dfd = window.Flyout.open({
            form: "schedule_event",
            card: 'add',
        });
        dfd.done(() => {
            fetchSchedule();
        });
    };

    const onEventDrop = (data: EventClickArg) => {
        const { id } = data.event._def.extendedProps.form_data;
        const putdata = {
            ...data.event._def.extendedProps.form_data,
            override_start_date: moment(data.event.start).format("MM/DD/YYYY"),
            override_start_time: moment(data.event.start).utc().format(formatScheduleTime),
            override_end_date: (data.event.end) ? moment(data.event.end).format("MM/DD/YYYY") : null,
            ovverride_end_time: (data.event.end) ? moment(data.event.end).utc().format(formatScheduleTime) : null,
            effective_start_date: moment(data.event.start).format("MM/DD/YYYY"),
            effective_start_time: moment(data.event.start).utc().format(formatScheduleTime),
            effective_end_date: (data.event.end) ? moment(data.event.end).format("MM/DD/YYYY") : null,
            effective_end_time: (data.event.end) ? moment(data.event.end).utc().format(formatScheduleTime) : null,
        };
        updateSchedule(id, putdata, "schedule_event").then((data) => { }).catch((err) => console.error(err));
    };

    const handleViewChange = (newView: string) => {
        setView(newView)
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi()
            calendarApi.changeView(newView)
        }
    }

    const handleTodayClick = () => {
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi()
            calendarApi.today()
            updateDateRange(calendarApi)
        }
    }

    const updateDateRange = (calendarApi: any) => {
        const view = calendarApi.view
        const start = view.currentStart
        const end = view.currentEnd

        const formatter = new Intl.DateTimeFormat("en-US", { month: "long", year: "numeric" })
        const rangeFormatter = new Intl.DateTimeFormat("en-US", { month: "short", day: "numeric", year: "numeric" })

        setDateRange({
            title: formatter.format(start),
            range: `${rangeFormatter.format(start)} - ${rangeFormatter.format(new Date(end.getTime() - 86400000))}`,
        })
    }


    const handlePrevClick = () => {
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi()
            calendarApi.prev()
            updateDateRange(calendarApi)
        }
    }

    const handleNextClick = () => {
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi()
            calendarApi.next()
            updateDateRange(calendarApi)
        }
    }

    const handleDateSelect = (info: DateSelectArg) => {
        const { start, end } = info;
        const date = new Date(start.toISOString().slice(0, -1));
        const dfd = window.Flyout.open({
            form: "schedule_event",
            preset: {
                event_start_date: date.toLocaleDateString("en-us"),
                event_start_time: date.toLocaleTimeString("en-us"),
                event_end_date: end.toLocaleDateString("en-us"),
                event_end_time: window.moment(date).add(1, "hours").format(formatScheduleTime)
            },
            autoRecoverEnabled: false,
        });
        dfd.done(() => {
            fetchSchedule();
        });
    };


    const generateFilters = (roleObject: any) => {
        if (!roleObject.role || !roleObject.role.static) {
            return "";
        }
        const staticRoles = roleObject.role.static;
        const filters = staticRoles.map((role: any) => `filter=role:${role}`).join("&");
        return filters;
    }
    const customStyles: StylesConfig = {
        control: (provided, state) => ({
            ...provided,
            height: '60px',
            borderRadius: '8px',
            border: `1px solid ${state.isFocused ? '#CBD5E0' : '#E3E5E8'}`,
            boxShadow: '0px 1px 2px 1px #38383814 inset',
            '&:hover': {
                borderColor: '#CBD5E0'
            },
            padding: '10px'
        }),
        valueContainer: (provided) => ({
            ...provided,
            marginTop: '18px',
            padding: '0px',
        }),
        placeholder: (provided) => ({
            ...provided,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            lineHeight: '1.2',
            color: '#9B9FA8'
        }),
        input: (provided) => ({
            ...provided,
            margin: '0',
            padding: '0',
            color: '#414651',
        }),
        indicatorsContainer: (provided) => ({
            ...provided,
            height: '100%',
            color: '#717680',
            '&:hover': {
                color: '#4A5568'
            }
        }),
        indicatorSeparator: (provided) => ({
            ...provided,
            display: 'none'
        }),
        clearIndicator: (provided) => ({
            ...provided,
            color: '#717680',
            '&:hover': {
                color: '#4A5568'
            }
        }),
        dropdownIndicator: (provided) => ({
            ...provided,
            color: '#717680',
            '&:hover': {
                color: '#4A5568'
            }
        }),
        menu: (provided) => ({
            ...provided,
            marginTop: '8px',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }),
        menuList: (provided) => ({
            ...provided,
            padding: '8px'
        }),
        menuPortal: (provided) => ({
            ...provided,
            zIndex: 9999
        }),
        option: (provided, state) => ({
            ...provided,
            padding: '12px 16px',
            borderRadius: '6px',
            backgroundColor: state.isSelected ? '#EDF2F7' : 'transparent',
            color: '#4A5568',
            '&:hover': {
                backgroundColor: '#F7FAFC'
            }
        })
    }

    const eventClassNames = (event: any) => {
        const status = event.event.extendedProps.appointment_status;
        switch (status) {
            case 'Confirmed':
                return ['confirmed-event'];
            case 'Scheduled':
                return ['scheduled-event'];
            case 'Hold/Pending':
                return ['pending-event'];
            case 'Canceled':
                return ['canceled-event'];
            default:
                return [];
        }
    };

    const onOpen = (event: EventClickArg, type: string) => {
        const recurring = type === "series" ? "Yes" : "No";
        setPopoverOpen(false);
        const dfd = window.Flyout.open({
            form: "schedule_event",
            record: event.event._def.publicId,
            override_field_data: { series: recurring, hide_series: "No" },
            autoRecoverEnabled: false,
        });
        dfd.done((data) => {
            void fetchSchedule();
        });
    };

    const renderEventContent = (eventInfo: EventContentArg) => {
        const timeText = eventInfo.timeText.replace(/am|pm/i, (match) => match.toUpperCase())
        const status = eventInfo.event.extendedProps.appointment_status
        let statusIcons = null
        if (status === "Hold/Pending") {
            statusIcons = <div className="event-status-icons"></div>
        }
        return (
            <div className="fc-event-main-frame">
                <div className="fc-event-title">{eventInfo.event.extendedProps.patient_name}</div>
                <div className="fc-event-time">{timeText} {statusIcons}</div>
            </div>
        )
    }

    return (
        <>
            <div className="tabContainer">
                <div className="tabButtons">
                    <button className={`tab ${activeTab === "calendar" ? "active" : ""}`} onClick={() => setActiveTab("calendar")}>
                        <i className="fa-light fa-calendar-days"></i> Calendar View
                    </button>
                    <button className={`tab ${activeTab === "list" ? "active" : ""}`} onClick={() => setActiveTab("list")}>
                        <i className="fa-light fa-list-ul"></i> List View
                    </button>
                </div>
            </div>
            <div className="calendarContainer">
                {
                    activeTab === "calendar" ?
                        <>
                            <div className="calendarHeader">
                                <div className="headerTop">
                                    <div className="dateInfo">
                                        <h2>{dateRange.title}</h2>
                                        <p>{dateRange.range}</p>
                                    </div>
                                    <div className="viewControls">
                                        <div className="navigationButtons">
                                            <div className="viewOptions">
                                                {viewOptions.map((option) => (
                                                    <div
                                                        key={option.value}
                                                        className={view === option.value ? "active" : ""}
                                                        onClick={() => handleViewChange(option.value)}
                                                    >
                                                        {option.value === view ? (
                                                            <div className="monthViewOption">
                                                                <Button
                                                                    className="navButton prevButton"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation()
                                                                        handlePrevClick()
                                                                    }}
                                                                    icon={<i className="fa-light fa-chevron-left" />}
                                                                />
                                                                <span>{option.label}</span>
                                                                <Button
                                                                    className="navButton nextButton"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation()
                                                                        handleNextClick()
                                                                    }}
                                                                    icon={<i className="fa-light fa-chevron-right" />}
                                                                />
                                                            </div>
                                                        ) : (
                                                            option.label
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                        <div className="todayButtonContainer">
                                            <Button className="todayButton" onClick={handleTodayClick}>
                                                Today
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="rightControls">
                                        {/* <div className="condensedViewToggle">
                                        <span>Condensed View?</span>
                                        <Switch
                                            checked={isCondensed}
                                            onChange={setIsCondensed}
                                            style={{
                                                backgroundColor: isCondensed ? "var(--color-primary-600)" : "var(--color-background-400)",
                                            }}
                                        />
                                    </div> */}
                                        <Button type="primary" className="addEventButton" onClick={() => addEvent()} icon={<i className="fa-thin fa-calendar-circle-plus" />}>
                                            Add Event
                                        </Button>
                                    </div>
                                </div>
                                <div className="headerBottom">
                                    <div className="select-field-container">
                                        <label className="select-field-label">Calendar</label>
                                        <FieldSelect
                                            form="calendar"
                                            defaultValueSource={filters.calendar_id || ""}
                                            defaultValueAutoName={filters.calendar_id_auto_name || ""}
                                            multi={false}
                                            disabled={false}
                                            onChange={(val, an) => {
                                                setFilters((filters) => ({ ...filters, calendar_id: val, calendar_id_auto_name: an }));
                                            }}
                                            customStyles={customStyles}
                                            placeholder="Select Calendar"
                                        />
                                    </div>

                                    <div className="select-field-container">
                                        <label className="select-field-label">Schedule For</label>
                                        <FieldSelect
                                            form="user"
                                            defaultValueSource={filters.user_id || ""}
                                            defaultValueAutoName={filters.user_id_auto_name || ""}
                                            extraParams={generateFilters(window.DSL.schedule_event.fields.user_id.model.sourcefilter)}
                                            multi={false}
                                            disabled={false}
                                            onChange={(val, an) => {
                                                setFilters((filters) => ({ ...filters, user_id: val, user_id_auto_name: an }));
                                            }}
                                            customStyles={customStyles}
                                            placeholder="Select Nurse"
                                        />
                                    </div>

                                    <div className="select-field-container">
                                        <label className="select-field-label">Patients</label>
                                        <FieldSelect
                                            form="patient"
                                            defaultValueSource={filters.patient_id || ""}
                                            defaultValueAutoName={filters.patient_id_auto_name || ""}
                                            multi={false}
                                            disabled={false}
                                            onChange={(val, an) => {
                                                setFilters((filters) => ({ ...filters, patient_id: val, patient_id_auto_name: an }));
                                            }}
                                            customStyles={customStyles}
                                            placeholder="Select Patient"
                                        />
                                    </div>

                                    <div className="select-field-container">
                                        <label className="select-field-label">Appointment Status</label>
                                        <FieldSelect
                                            form="none"
                                            defaultValueSource={filters.appointment_status || ""}
                                            defaultValueAutoName={filters.appointment_status_auto_name || ""}
                                            multi={false}
                                            disabled={false}
                                            options={selectOptions.status}
                                            onChange={(val, an) => {
                                                setFilters((filters) => ({ ...filters, appointment_status: val, appointment_status_auto_name: an }));
                                            }}
                                            customStyles={customStyles}
                                            placeholder="Select Status"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className={`calendarWrapper ${isCondensed ? "condensed" : ""}`}>
                                <FullCalendar
                                    ref={calendarRef}
                                    plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                                    initialView={view}
                                    timeZone="UTC"
                                    editable={true}
                                    selectable={true}
                                    selectMirror={true}
                                    weekends={state.weekendsVisible}
                                    events={state?.currentEvents || []}
                                    dayMaxEvents={isCondensed ? 2 : 3}
                                    firstDay={1}
                                    headerToolbar={false}
                                    eventClick={(event) => openSchedule(event)}
                                    eventDrop={onEventDrop}
                                    select={handleDateSelect}
                                    eventResize={onEventDrop}
                                    contentHeight="auto"
                                    height="100%"
                                    eventContent={renderEventContent}
                                    eventTimeFormat={{
                                        hour: "numeric",
                                        minute: "2-digit",
                                        meridiem: "short",
                                    }}
                                    eventClassNames={eventClassNames}
                                    datesSet={(arg) => updateDateRange(arg.view.calendar)}
                                    stickyHeaderDates={true}
                                    dayCellContent={({ date }) => <div className="fc-daygrid-day-number">{date.getDate()}</div>}
                                />

                                {
                                    event?.el && <CalenderPopover
                                        anchorEl={event.el}
                                        onClose={() => setPopoverOpen(false)} open={popoverOpen}
                                        data={event}
                                        onOpen={onOpen}
                                    />
                                }
                            </div>
                        </>
                        :
                        <CRErrorBoundary>
                            <DSLListView
                                form='schedule_event'
                                label='Schedule Event'
                                disableRouting={true}
                                hideFilter={false}
                                onRef={(ref) => {
                                    console.log(ref)
                                }}
                                rowClicked={(opts) => {
                                    window.Flyout.open({
                                        record: opts.id,
                                        form: 'schedule_event',
                                        card: "read",
                                        tab_can_edit_override() {
                                            return false;
                                        },
                                        tab_can_archive_override() {
                                            return false;
                                        },
                                    });
                                }}
                            />
                        </CRErrorBoundary>
                }
            </div>
        </>
    )
}