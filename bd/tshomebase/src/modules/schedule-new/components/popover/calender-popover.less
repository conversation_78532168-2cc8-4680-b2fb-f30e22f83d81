@import (reference) "../../../../less/style/main.less";

.sc-popover {
    border-radius: 8px;
    margin: 20px;

    .button-container {
        display: flex;
        flex-direction: column;
        background-color: #E1E1E1;
        border-radius: 8px;
        position: relative;
        border: 5px solid rgba(217, 217, 217, 0.8);
        overflow: visible;
        border-width: 1px;
        border-color: white;
        backdrop-filter: blur(8px);
        box-shadow: 0px -1px 3px 0px rgba(255, 255, 255, 0.12) inset;

        &::before {
            content: "";
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            left: -15px;
            top: 50%;
            border: 11px solid transparent;
            border-left: 0;
            border-right: 15px solid rgba(217, 217, 217, 0.8);
            transform: translateY(-50%);
            z-index: 2;
        }

        .sc-popover-actions-btn {
            display: flex;
            flex-direction: row;
            gap: 4px;
            justify-content: center;
            padding: 14px 12px 0 10px;

            .pop-btn {
                display: flex;
                align-items: center;
                flex-direction: row;
                background-color: var(--color-text-50);
                border-color: transparent;
                justify-content: center;
                border-radius: 8px;
                padding: 10px 26px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.3s ease, color 0.3s ease;
                box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.22) inset,
                    0px 1px 2px 0px rgba(255, 255, 255, 0.12) inset;
                background-clip: padding-box;

                &:hover {
                    background: var(--color-primary-600);
                    color: var(--color-text-50);
                    background-clip: padding-box;
                }

                .series-icon::before,
                .event-icon::before {
                    font-family: "Font Awesome 6 Pro";
                    font-weight: 600;
                    font-size: 15px;
                    color: var(--color-primary-600);
                    margin-right: 6px;
                }

                .series-icon::before {
                    content: "\f364";
                }

                .event-icon::before {
                    content: "\f133";
                }

                &:hover .series-icon::before,
                &:hover .event-icon::before {
                    color: var(--color-text-50);
                    background-clip: padding-box;
                }
            }
        }

        .pt-container {
            padding: 4px 12px 10px 10px;

            .pt-icon::before {
                content: "\f830";
                font-family: "Font Awesome 6 Pro";
                font-weight: 600;
                font-size: 15px;
                color: var(--color-primary-600);
                margin-right: 6px;
            }

            .view-pt {
                justify-content: center;
                border-radius: 8px;
                padding: 10px 24px;
                width: 100%;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--color-text-50);
                border-color: transparent;
                box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.22) inset,
                    0px 1px 2px 0px rgba(255, 255, 255, 0.12) inset;
                background-clip: padding-box;

                &:hover {
                    background: var(--color-primary-600);
                    color: var(--color-text-50);
                    background-clip: padding-box;
                }

                &:hover .pt-icon::before {
                    background: var(--color-primary-600);
                    color: var(--color-text-50);
                    background-clip: padding-box;
                }
            }
        }
    }

    .wrapper-cond {
        flex-direction: row !important;
    }

    .pt-wrapper {
        padding: 10px 12px 10px 2px !important;
    }

    .sc-wrapper {
        display: inline !important;
        padding: 10px 2px 0 10px !important;
    }

    .wrapper-events {
        display: inline !important;
    }
}

.css-3bmhjh-MuiPaper-root-MuiPopover-paper {
    background-color: transparent !important;
    box-shadow: none !important;
}