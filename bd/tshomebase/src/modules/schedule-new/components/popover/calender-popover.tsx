/* eslint-disable react/jsx-closing-tag-location */
import type { FC } from "react";
import React from "react";
import type { EventClickArg } from "@fullcalendar/core";
import "./calender-popover.less";
import Popover from "@mui/material/Popover";

interface PopoverProps {
	onClose: () => void;
	anchorEl: HTMLElement;
	open: boolean;
	data?: EventClickArg;
	onOpen: (data: EventClickArg, type: string) => void;
}

export const CalenderPopover: FC<PopoverProps> = (props) => {

	const { onClose, anchorEl, open, data, onOpen } = props;
	const { extendedProps } = data?.event._def;
	const get_pt_id = extendedProps?.form_data?.patient_id

	if (!open) {
		return null;
	}

	const goToPatient = () => {
		if (get_pt_id) {
			window.App.reactNav.goTo(`/patient/${get_pt_id}/snap`);
		}
		onClose()
	};

	return (
		<>
			<Popover
				id="sc-popover"
				open={open}
				anchorEl={anchorEl}
				onClose={onClose}
				anchorOrigin={{
					vertical: "top",
					horizontal: "right",
				}}
				transformOrigin={{
					vertical: "center",
					horizontal: "left",
				}}
			>
				<div className="sc-popover">
					<div
						className={`button-container ${
							!extendedProps?.form_data?.event_series_id ? "wrapper-cond" : ""
						}`}
					>
						<div
							className={`sc-popover-actions-btn ${
								!extendedProps?.form_data?.event_series_id ? "sc-wrapper" : ""
							}`}
						>
							{data && (
								<>
									{extendedProps?.form_data?.event_series_id && (
										<button className="pop-btn btn-series" onClick={() => onOpen(data, "series")}>
											<span className="series-icon"></span> Edit Series
										</button>
									)}
									<button
										className={`pop-btn btn-events ${
											extendedProps?.form_data?.event_series_id ? "" : "wrapper-events"
										}`}
										onClick={() => onOpen(data, "individual")}
									>
										<span className="event-icon"></span> Edit Events
									</button>
								</>
							)}
						</div>
						<div
							className={`pt-container ${!extendedProps?.form_data?.event_series_id ? "pt-wrapper" : ""}`}
						>
							<button className="view-pt" onClick={goToPatient}>
								<span className="pt-icon"></span> View Patient
							</button>
						</div>
					</div>
				</div>
			</Popover>
		</>
	);
};
