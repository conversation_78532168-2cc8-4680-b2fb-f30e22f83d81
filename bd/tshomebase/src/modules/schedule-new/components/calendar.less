@import "../../../styles/base/less/colors.less";
@import "../../../styles/base/less/dimensions.less";
@import "../../../styles/base/less/fonts.less";

.tabContainer {
	display: flex;
	background-color: #fbfbfbb8;
	border-bottom: 1px solid var(--color-border-500);
	border-radius: 8px;
	gap: 16px;
	padding: 0 4px;
	width: fit-content;
	border: 1px solid #fff;
	margin-bottom: 8px;
	align-items: center;
	overflow: auto clip;
	height: 46px;

	.tabButtons {
		gap: 8px;

		.tab {
			padding: 6px 8px;
			font-size: 14px;
			font-weight: 500;
			color: var(--gray-700);
			background-color: transparent;
			border: none;
			cursor: pointer;
			transition: all 0.2s;

			&.active {
				color: #fff;
				background-color: var(--color-tertiary);
				border-top: 1px solid #FAFAFAE5;
				border-radius: 8px;
				font-weight: 700;
				box-shadow: inset 0 -1px 2px #00000038, inset 0 1px 2px #ffffff1f;
			}

			i {
				margin-right: 4px;
			}
		}
	}
}

.calendarContainer {
	background-color: var(--color-background-100);
	border-radius: 8px;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	height: 100%;
	display: flex;
	flex-direction: column;
	min-height: 0;

	.calendarHeader {
		padding: 16px 24px;
		min-height: fit-content;

		.headerTop {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;

			.dateInfo {
				width: 200px;

				h2 {
					font-size: 18px;
					font-weight: 600;
					color: var(--color-text-800);
					margin: 0;
					line-height: 1.2;
				}

				p {
					font-size: 12px;
					color: var(--color-text-500);
					margin: 4px 0 0;
				}
			}

			.viewControls {
				display: flex;
				align-items: center;
				gap: 8px;

				.navigationButtons {
					display: flex;
					align-items: center;
					box-shadow: 0px 1px 2px 1px #38383814 inset;
					border-radius: 8px;
					padding: 0px 6px;
					height: 42px;

					.navButton {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 32px;
						height: 32px;
						padding: 12px 14px;
						border: none;
						color: var(--color-text-500);
						background: white;

						&:hover {
							background-color: white;
						}
					}

					.viewOptions {
						display: flex;
						gap: 4px;

						> div {
							border-radius: 4px;
							font-size: 16px;
							font-weight: 400;
							border: none;
							background: none;
							cursor: pointer;
							transition: all 0.2s;
							color: #838894;
							display: flex;
							align-items: center;
							margin: 0px 2px 0px 2px;
							&:hover {
								color: var(--color-primary-600);
							}
							&.active {
								background-color: white;
								color: var(--color-primary-600);
								box-shadow: inset 0 1px 2px #ffffff1f, inset 0 -1px 2px #00000038, 0 0 2px #0000001f;
								border-radius: 8px;

								span {
									color: var(--color-primary-600);
									font-weight: 700;
								}
							}
						}

						.monthViewOption {
							display: flex;
							align-items: center;

							span {
								font-size: 14px;
								font-weight: 500;
								color: #374151;
							}

							.navButton {
								display: flex;
								align-items: center;
								justify-content: center;
								width: 28px;
								height: 28px;
								padding: 0;
								border: none;
								color: #6b7280;
								background: transparent;

								&:hover {
									color: var(--color-primary-600);
								}

								i {
									font-size: 14px;
									color: var(--color-primary-600);
									font-weight: 900;
								}
							}
						}
					}
				}

				.todayButtonContainer {
					.todayButton {
						height: 36px;
						border: none;
						color: #414651;
						background: white;
						box-shadow: inset 0 1px 2px #ffffff1f, inset 0 -1px 2px #00000038, 0 0 2px #0000001f;
						padding: 8px 12px;
						font-size: 14px;
						font-weight: 500;

						&:hover {
							background-color: var(--color-background-200);
						}
					}
				}
			}

			.rightControls {
				display: flex;
				align-items: center;
				gap: 16px;
				justify-content: flex-end;

				.condensedViewToggle {
					display: flex;
					align-items: center;
					gap: 8px;

					span {
						font-size: 14px;
						color: var(--color-text-500);
					}

					.ant-switch {
						min-width: 44px;
						height: 22px;
						background: var(--color-background-400);

						&.ant-switch-checked {
							background: var(--color-primary-600);
						}

						.ant-switch-handle {
							width: 18px;
							height: 18px;
						}

						&.ant-switch-checked .ant-switch-handle {
							inset-inline-start: calc(100% - 20px);
						}
					}
				}

				.addEventButton {
					height: 34px;
					background: var(--color-primary-600);
					border: none;
					display: flex;
					align-items: center;
					gap: 8px;
					padding: 0 16px;
					font-size: 14px;
					color: white;

					i {
						font-size: 16px;
						font-weight: 400;
					}

					&:hover {
						background: var(--color-primary-700) !important;
					}
				}
			}
		}

		.headerBottom {
			display: flex;
			flex-wrap: wrap;
			width: 100%;
			gap: 16px;

			.select-field-container {
				flex: 1;
				box-sizing: border-box;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				position: relative;

				.select-field-label {
					position: absolute !important;
					top: var(--spacing-standard);
					left: var(--spacing-xlarge);
					font-size: var(--font-size-xsmall);
					font-weight: var(--font-weight-regular);
					color: var(--color-text);
					z-index: 1;
					pointer-events: none;
				}
			}
		}
	}

	.calendarWrapper {
		display: flex;
		width: 100%;
		overflow: hidden;
		padding: 0px 12px;

		.fc {
			--fc-border-color: var(--color-border-500);
			--fc-event-bg-color: var(--color-primary-600);
			--fc-event-border-color: var(--color-primary-600);
			--fc-today-bg-color: var(--color-background-300);
			--fc-page-bg-color: var(--color-background-100);
			--fc-neutral-bg-color: var(--color-background-100);
			--fc-list-event-hover-bg-color: var(--color-background-300);
			width: 100%;
			height: 100%;
			overflow: auto;
			padding: 0 10px;

			.fc-view-harness {
				height: 100% !important;
			}

			.fc-scroller {
				height: auto !important;
			}

			.fc-daygrid-body {
				width: 100% !important;
			}

			.fc-col-header,
			.fc-daygrid-body {
				width: 100% !important;
			}

			.fc-daygrid-day-top {
				flex-direction: row;
			}

			.fc-header-toolbar {
				display: flex;
				align-items: center;
			}

			.fc-toolbar-chunk {
				display: flex;
				align-items: center;
			}

			.fc-toolbar-title {
				margin: 0;
			}

			.fc-button-active {
				text-decoration: underline;
				text-decoration-thickness: 2px;
				// text-decoration-color: @purple;
				text-underline-offset: 5px;
			}

			.fc-button-primary {
				border-radius: 0px !important;
				border: 0px !important;
				text-shadow: none !important;
				box-shadow: none !important;
				outline: none !important;
			}

			.fc-button:not(:last-child) {
				opacity: 1;
			}

			.fc .fc-button:disabled {
				opacity: 1;
				.fc-button-active;
			}

			.fc .fc-button:disabled {
				opacity: 1;
			}

			.fc-col-header {
				width: 100% !important;
			}

			.fc-theme-standard {
				td,
				th {
					border-color: var(--color-border-500);
				}
			}

			.fc-col-header-cell {
				background-color: var(--color-background-200);
				font-weight: 600;
				text-transform: uppercase;
				color: var(--color-text-500);
				padding: 12px;
			}

			.fc-daygrid-day-number,
			.fc-col-header-cell-cushion {
				color: var(--color-text-700);
				font-size: 0.875rem;
				padding: 8px;
			}

			.fc-event {
				font-size: 14px;
				border-radius: 6px;
				// height: 24px;
				display: flex !important;
				align-items: flex-start;
				gap: 8px;
				border: none;
				flex-direction: column;
				overflow: hidden;

				.fc-event-main {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding-left: 6px;
					flex: 1;
				}

				.fc-event-time {
					font-size: 10px;
					font-weight: 400;
					color: #6b7280;
					margin: 0;
					order: 2;
					flex-shrink: 0;
					margin: 3px 3px;
				}

				.fc-event-title {
					font-size: 12px;
					font-weight: 500;
					line-height: 20px;
					padding-left: 6px;
					text-overflow: ellipsis;

					&:before {
						margin-right: 8px;
						font-family: "Font Awesome 5 Pro";
						font-weight: 400;
					}
				}

				.fc-daygrid-event-dot {
					display: none !important;
				}

				&.scheduled-event {
					background-color: #f6f5ff;
					border: 1px solid #d6d3eb;
					height: auto;

					.fc-event-title {
						color: #746d9e;

						&:before {
							content: "\f00c";
							color: var(--color-success);
						}
					}

					.fc-event-time {
						color: var(--color-tertiary);
					}
				}

				&.pending-event {
					background-color: #ffffff;
					border: 1px solid #e5e7eb;

					.event-icon {
						color: var(--color-warning);
					}

					.fc-event-title {
						color: #414651;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;

						&::before {
							content: "\f111";
							font-family: "Font Awesome 6 Pro";
							color: var(--color-warning);
							font-weight: 600;
							font-size: 9px;
							margin-right: 8px;
						}
					}

					.fc-event-time {
						color: #6b7280;
						display: flex;
						align-items: center;
						gap: 4px;
						.event-status-icons {
							&::before {
								content: "\f4a6";
								font-family: "Font Awesome 6 Pro";
								color: var(--color-error);
								margin-right: 4px;
							}
							&::after {
								content: "\f021";
								font-family: "Font Awesome 6 Pro";
								color: #838894;
							}
						}
					}
				}

				&.canceled-event {
					background-color: #f5f9ff;
					border: 1px solid #e3ebf7;
					opacity: 0.8;

					.fc-event-title {
						color: #50749c;
						text-decoration: line-through;

						&:before {
							text-decoration: none;
							content: "\f00d";
							color: var(--color-error);
						}
					}

					.fc-event-time {
						color: #50749c;
						text-decoration: line-through;
					}
				}

				&.confirmed-event {
					background-color: #f6f5ff;
					border: 1px solid #e5e3f5;

					.fc-event-title {
						color: #746d9e;

						&:before {
							content: "\f00c";
							color: var(--color-success);
						}
					}

					.fc-event-time {
						color: var(--color-tertiary);
					}
				}
			}

			.fc-day-today {
				background-color: var(--color-primary-100) !important;
			}

			.fc-daygrid-day {
				height: 140px !important;
			}

			.fc-daygrid-day-frame {
				height: 100%;
			}

			.fc-daygrid-day-events {
				padding: 4px;
			}
		}

		&.condensed {
			.fc-event {
				margin: 1px 0;
			}
		}
	}

	.fc-header-toolbar {
		display: flex;
		align-items: center;
		margin: 0;
		position: relative;
		width: 100%;
		height: 65px;
		justify-content: flex-start;

		.fc-toolbar-chunk {
			display: flex;
			align-items: center;

			&:first-child {
				flex: 1;
			}

			&:nth-child(2),
			&:nth-child(3) {
				.fc-button-group {
					display: flex;
					align-items: center;

					.fc-button {
						height: 44px;
						padding: 0px 20px;
					}
				}
			}
		}
	}
}
