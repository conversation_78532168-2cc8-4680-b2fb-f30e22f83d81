import { RoutedComponentProps } from "@typedefs/routed-component"
import { CalendarView } from "./components/calendar"
import { useNavigation } from "@core/navigation";
import { useEffect } from "react";

export function Schedule(props: RoutedComponentProps) {

    const nav = useNavigation(props, "/schedule");
    useEffect(() => {
        if (props.navToURL?.startsWith("/schedule")) {
            props.setActiveTab?.("schedule");
        }
    }, [nav.globalNav.lrct]);

    return <CalendarView />
}