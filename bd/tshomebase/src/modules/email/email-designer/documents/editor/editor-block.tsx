import React, { createContext, useContext } from 'react';
import { useDocument } from '@modules/email/email-designer/documents/editor/editor-context';
import { EditorBlock as CoreEditorBlock } from '@modules/email/email-designer/documents/editor/core';

const EditorBlockContext = createContext<string | null>(null);
export const useCurrentBlockId = () => useContext(EditorBlockContext)!;

type EditorBlockProps = {
  id: string;
};

/**
 *
 * @param id - Block id
 * @returns EditorBlock component that loads data from the EditorDocumentContext
 */
export default function EditorBlock({ id }: EditorBlockProps) {
  const document = useDocument();
  const block = document[id];
  if (!block) {
    // throw new Error('Could not find block');
    console.log('Could not find block', id);
  }
  return (
    <EditorBlockContext.Provider value={id}>
      <CoreEditorBlock {...block} />
    </EditorBlockContext.Provider>
  );
}
