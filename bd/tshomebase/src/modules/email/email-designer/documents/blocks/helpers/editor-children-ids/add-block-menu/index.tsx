import { useState } from 'react';

import { TEditorBlock } from '@modules/email/email-designer/documents/editor/core';
import DividerButton from '@modules/email/email-designer/documents/blocks/helpers/editor-children-ids/add-block-menu/divider-button';
import PlaceholderButton from '@modules/email/email-designer/documents/blocks/helpers/editor-children-ids/add-block-menu/placeholder-button';
import BlocksMenu from '@modules/email/email-designer/documents/blocks/helpers/editor-children-ids/add-block-menu/blocks-menu';

type Props = {
  placeholder?: boolean;
  onSelect: (block: TEditorBlock) => void;
};
export default function AddBlockButton({ onSelect, placeholder }: Props) {
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [buttonElement, setButtonElement] = useState<HTMLElement | null>(null);

  const handleButtonClick = () => {
    setMenuAnchorEl(buttonElement);
  };

  const renderButton = () => {
    if (placeholder) {
      return <PlaceholderButton onClick={handleButtonClick} />;
    } else {
      return <DividerButton buttonElement={buttonElement} onClick={handleButtonClick} />;
    }
  };

  return (
    <>
      <div ref={setButtonElement} style={{ position: 'relative' }}>
        {renderButton()}
      </div>
      <BlocksMenu anchorEl={menuAnchorEl} setAnchorEl={setMenuAnchorEl} onSelect={onSelect} />
    </>
  );
}
