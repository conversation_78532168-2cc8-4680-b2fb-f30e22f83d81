import { Container as BaseContainer } from '@usewaypoint/block-container';

import { ContainerProps } from '@modules/email/email-designer/documents/blocks/container/container-props-schema';
import { useCurrentBlockId } from '@modules/email/email-designer/documents/editor/editor-block';
import { useDocument, setDocument, setSelectedBlockId } from '@modules/email/email-designer/documents/editor/editor-context';
import EditorChildrenIds from '@modules/email/email-designer/documents/blocks/helpers/editor-children-ids';


export default function ContainerEditor({ style, props }: ContainerProps) {
  const childrenIds = props?.childrenIds ?? [];

  const document = useDocument();
  const currentBlockId = useCurrentBlockId();

  return (
    <BaseContainer style={style}>
      <EditorChildrenIds
        childrenIds={childrenIds}
        onChange={({ block, blockId, childrenIds }) => {
          setDocument({
            [blockId]: block,
            [currentBlockId]: {
              type: 'Container',
              data: {
                ...document[currentBlockId].data,
                props: { childrenIds: childrenIds },
              },
            },
          });
          setSelectedBlockId(blockId);
        }}
      />
    </BaseContainer>
  );
}
