import { TEditorConfiguration } from "@modules/email/email/documents/editor/core";

export const config: TEditorConfiguration = {
    "root": {
        "type": "EmailLayout",
        "data": {
            "backdropColor": "#F5F5F5",
            "canvasColor": "#FFFFFF",
            "textColor": "#262626",
            "fontFamily": "MODERN_SANS",
            "childrenIds": [
                "block-1713199011299",
                "block-0000000000001"
            ]
        }
    },
    "block-1713199011299": {
        "type": "Text",
        "data": {
            "style": {
                "fontWeight": "normal",
                "padding": {
                    "top": 16,
                    "bottom": 16,
                    "right": 24,
                    "left": 24
                }
            },
            "props": {
                "text": ""
            }
        }
    },
    "block-0000000000001": {
        "type": "Html",
        "data": {
            "style": {
                "fontSize": 11,
                "textAlign": "center",
                "padding": {
                    "top": 0,
                    "bottom": 0,
                    "right": 24,
                    "left": 24
                }
            },
            "props": {
                "contents": "Powered By <a class='footer' href=\"//envoylabs.net\" target=\"_blank\">Envoy</a>"
            }
        }
    }
}