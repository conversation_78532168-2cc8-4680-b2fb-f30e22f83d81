import { TEditorConfiguration } from '../../documents/editor/core';

const SUBSCRIPTION_RECEIPT: TEditorConfiguration = {
  root: {
    type: 'EmailLayout',
    data: {
      backdropColor: '#F5F5F5',
      canvasColor: '#F5F5F5',
      textColor: '#242424',
      fontFamily: 'MODERN_SANS',
      childrenIds: [
        'block_URcE7RiYB227zNraU1Nujd',
        'block_TisRUSez8uPYr6bgHLKQeg',
        'block_UQHMPb5NFLrY9PkWUckmHb',
        'block_FLTQdJVBNsmRxurTZTSC2V',
        'block_Qq64GeHw7K24Fgz5oX81kt',
      ],
    },
  },
  block_DNGbxXXMnwkRrecLDTP6VR: {
    type: 'Image',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
        textAlign: 'left',
      },
      props: {
        height: 18,
        url: 'https://d1iiu589g39o6c.cloudfront.net/live/platforms/platform_A9wwKSL6EV6orh6f/images/wptemplateimage_9TcdHLq5SpEkRADB/REMIX.png',
        alt: 'Remix',
        linkHref: 'https://remix.example.com',
        contentAlignment: 'middle',
      },
    },
  },
  block_ULKdXApwJ1dsJCa5fntAXV: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_DNGbxXXMnwkRrecLDTP6VR'],
      },
    },
  },
  block_FBxe99baQhH1dptybLYVrF: {
    type: 'Button',
    data: {
      style: {
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        buttonBackgroundColor: '#FFFFFF',
        buttonStyle: 'pill',
        buttonTextColor: '#242424',
        fullWidth: false,
        size: 'medium',
        text: 'Sign in',
        url: 'https://remix.example.com/dashboard',
      },
    },
  },
  block_WDjxjPcraFGwiBHj5vjhdV: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_FBxe99baQhH1dptybLYVrF'],
      },
    },
  },
  block_JUxxjtK5TSUk9MV6Rtra4Q: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_URcE7RiYB227zNraU1Nujd: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: '#f5f5f5',
        padding: {
          top: 16,
          bottom: 16,
          left: 24,
          right: 24,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_ULKdXApwJ1dsJCa5fntAXV'],
          },
          {
            childrenIds: ['block_WDjxjPcraFGwiBHj5vjhdV'],
          },
          {
            childrenIds: ['block_JUxxjtK5TSUk9MV6Rtra4Q'],
          },
        ],
      },
    },
  },
  block_9mYZ55v7d29WRjNgPf5cuw: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Receipt from Remix Studios, Inc.',
      },
    },
  },
  block_R5vyrdxj7v4FqyFbQ7iDBM: {
    type: 'Heading',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'left',
        padding: {
          top: 16,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        level: 'h1',
        text: '$99.75',
      },
    },
  },
  block_7vHVGWiRQYr8sigcW9nJvD: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Paid August 1, 2023',
      },
    },
  },
  block_AL2uK2hkvCnaT6JtDuvE5n: {
    type: 'Divider',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 16,
          bottom: 16,
          left: 0,
          right: 0,
        },
      },
      props: {
        lineHeight: 1,
        lineColor: '#EEEEEE',
      },
    },
  },
  block_ADvJa3qqbBH1TB84VxbFwC: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Download receipt',
      },
    },
  },
  block_9NpGWp5DnLHA2gbTUzsWHX: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [
          'block_9mYZ55v7d29WRjNgPf5cuw',
          'block_R5vyrdxj7v4FqyFbQ7iDBM',
          'block_7vHVGWiRQYr8sigcW9nJvD',
          'block_AL2uK2hkvCnaT6JtDuvE5n',
          'block_ADvJa3qqbBH1TB84VxbFwC',
        ],
      },
    },
  },
  block_FMRV8DJAAWqpoJta6ivKEe: {
    type: 'Image',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
        textAlign: 'right',
      },
      props: {
        url: 'https://d1iiu589g39o6c.cloudfront.net/live/platforms/platform_A9wwKSL6EV6orh6f/images/wptemplateimage_8yUGBZcXaAtTEofB/invoice-skeleton.png',
        alt: 'Your invoice has been paid.',
        linkHref: 'http://remix.example.com/receipt/1923-2093',
        contentAlignment: 'middle',
      },
    },
  },
  block_SGQxVVB8bXVmVg9NyDY3Fu: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_FMRV8DJAAWqpoJta6ivKEe'],
      },
    },
  },
  block_KY8rgT1mqUBiCm2uranpvx: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_4TVeBRBPut2oZoQpG9FV4J: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 16,
          bottom: 16,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_9NpGWp5DnLHA2gbTUzsWHX'],
          },
          {
            childrenIds: ['block_SGQxVVB8bXVmVg9NyDY3Fu'],
          },
          {
            childrenIds: ['block_KY8rgT1mqUBiCm2uranpvx'],
          },
        ],
      },
    },
  },
  block_67EyoqnbtLAHWXiobN39NX: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Receipt number',
      },
    },
  },
  block_UCrU3np77mWqQgTfYHG1NL: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_67EyoqnbtLAHWXiobN39NX'],
      },
    },
  },
  block_LQ93SUN8XxG6uaLbGpsAx1: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: '1923-2093',
      },
    },
  },
  block_6KX5ggTcFs5ckbt9wZppac: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_LQ93SUN8XxG6uaLbGpsAx1'],
      },
    },
  },
  block_AsFj6GNHWFUhG6GgM7Ww9r: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_SfGDZ1NhRmtjFJS6qs4Zpc: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_UCrU3np77mWqQgTfYHG1NL'],
          },
          {
            childrenIds: ['block_6KX5ggTcFs5ckbt9wZppac'],
          },
          {
            childrenIds: ['block_AsFj6GNHWFUhG6GgM7Ww9r'],
          },
        ],
      },
    },
  },
  block_6UhQGq7NwSAhRog8yRYjJo: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Invoice number',
      },
    },
  },
  block_F3FdxCgPYxnH8SYgFgBsuY: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_6UhQGq7NwSAhRog8yRYjJo'],
      },
    },
  },
  block_L5on9HCEU6BRvx25RBRgS2: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: '2ABC1234-20923',
      },
    },
  },
  block_WrzeLHiJs9VnXSkLXXD5ED: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_L5on9HCEU6BRvx25RBRgS2'],
      },
    },
  },
  block_Wb8pxrh2frxYZWM6hmfc5L: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_XiNoAViyVVEWafdMW4x4TL: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_F3FdxCgPYxnH8SYgFgBsuY'],
          },
          {
            childrenIds: ['block_WrzeLHiJs9VnXSkLXXD5ED'],
          },
          {
            childrenIds: ['block_Wb8pxrh2frxYZWM6hmfc5L'],
          },
        ],
      },
    },
  },
  block_LbwZcoeGigXpK4PTVTyZ8E: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Payment method',
      },
    },
  },
  block_ETbxLryzrgoKbVBnw1ieyi: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_LbwZcoeGigXpK4PTVTyZ8E'],
      },
    },
  },
  block_P8fn3PavFZVTrEwufKHKEF: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'VISA – 4252',
      },
    },
  },
  block_2Bx6KDcv1nT4hPiQ8Eabfv: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_P8fn3PavFZVTrEwufKHKEF'],
      },
    },
  },
  block_WSzbB3PKqRUxCuUqp9wuK6: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_NYNuDnrs6ZnZ6kj927yv7W: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_ETbxLryzrgoKbVBnw1ieyi'],
          },
          {
            childrenIds: ['block_2Bx6KDcv1nT4hPiQ8Eabfv'],
          },
          {
            childrenIds: ['block_WSzbB3PKqRUxCuUqp9wuK6'],
          },
        ],
      },
    },
  },
  block_TisRUSez8uPYr6bgHLKQeg: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: '#ffffff',
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 16,
          bottom: 24,
          left: 24,
          right: 24,
        },
      },
      props: {
        childrenIds: [
          'block_4TVeBRBPut2oZoQpG9FV4J',
          'block_SfGDZ1NhRmtjFJS6qs4Zpc',
          'block_XiNoAViyVVEWafdMW4x4TL',
          'block_NYNuDnrs6ZnZ6kj927yv7W',
        ],
      },
    },
  },
  block_UQHMPb5NFLrY9PkWUckmHb: {
    type: 'Spacer',
    data: {
      props: {
        height: 24,
      },
    },
  },
  block_RNsVmDsY33ipzGLtRUsYys: {
    type: 'Heading',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 16,
          bottom: 16,
          left: 0,
          right: 0,
        },
      },
      props: {
        level: 'h3',
        text: 'Receipt #1923-2093',
      },
    },
  },
  block_Y7W2h9xDuNreQdgMrv82KZ: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 16,
          bottom: 16,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: `JULY 10, 2023 – AUGUST 1, 2023`,
      },
    },
  },
  block_4m1h6Xzhvi9oDrFMgWajjH: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Remix Pro',
      },
    },
  },
  block_7egXzHguX1zPm5CwYiKuPh: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 12,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 8,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: '5 seats',
      },
    },
  },
  block_WbxCFjDKk1Ev11prfgLhvX: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_4m1h6Xzhvi9oDrFMgWajjH', 'block_7egXzHguX1zPm5CwYiKuPh'],
      },
    },
  },
  block_FUaNAn8h7DzU7kVzeFqsug: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: '$99.75',
      },
    },
  },
  block_9gDnB1o17vBrS4usmyAVUr: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_FUaNAn8h7DzU7kVzeFqsug'],
      },
    },
  },
  block_5wDvatGEixESNpVrjWbAGi: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_KFadGhRvS4kHVRr41XMiRs: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_WbxCFjDKk1Ev11prfgLhvX'],
          },
          {
            childrenIds: ['block_9gDnB1o17vBrS4usmyAVUr'],
          },
          {
            childrenIds: ['block_5wDvatGEixESNpVrjWbAGi'],
          },
        ],
      },
    },
  },
  block_3rTWcrZLSt1nnCYW5BdiU9: {
    type: 'Divider',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        lineHeight: 1,
        lineColor: '#EEEEEE',
      },
    },
  },
  block_CSV8NQS6gBMQcFXvFAFo3Q: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_KFadGhRvS4kHVRr41XMiRs', 'block_3rTWcrZLSt1nnCYW5BdiU9'],
      },
    },
  },
  block_XZZUC61s2jBfaMfjcFYSo2: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Total',
      },
    },
  },
  block_LfhgxAyqkNPTGubxqYJNZ5: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_XZZUC61s2jBfaMfjcFYSo2'],
      },
    },
  },
  block_MsXkdwkAWAbvJ5EcwvqSqv: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: '$99.75',
      },
    },
  },
  block_PiKRrj91HQKM1qUW4gDJt1: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_MsXkdwkAWAbvJ5EcwvqSqv'],
      },
    },
  },
  block_WhzFAeoTPhhgNcG9rdcd4K: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_LJcwZCh6z1EyFeeKH8Egts: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_LfhgxAyqkNPTGubxqYJNZ5'],
          },
          {
            childrenIds: ['block_PiKRrj91HQKM1qUW4gDJt1'],
          },
          {
            childrenIds: ['block_WhzFAeoTPhhgNcG9rdcd4K'],
          },
        ],
      },
    },
  },
  block_HSbX9aqHvSXw9GAB361vpM: {
    type: 'Divider',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        lineHeight: 1,
        lineColor: '#EEEEEE',
      },
    },
  },
  block_3of5ne4hc1PFiSKHz59FP8: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'left',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: 'Amount paid',
      },
    },
  },
  block_GTPnMRuHqf1bCLk8VLAr4z: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_3of5ne4hc1PFiSKHz59FP8'],
      },
    },
  },
  block_XvojX8xDiQRr4VNHDwtyXZ: {
    type: 'Text',
    data: {
      style: {
        color: null,
        backgroundColor: null,
        fontSize: 14,
        fontFamily: null,
        fontWeight: 'bold',
        textAlign: 'right',
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        text: '$99.75',
      },
    },
  },
  block_LbEheyx16SX5S8GK2zY3sJ: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: ['block_XvojX8xDiQRr4VNHDwtyXZ'],
      },
    },
  },
  block_M6BwLBjomRNsdee4rdoA4y: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: null,
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      },
      props: {
        childrenIds: [],
      },
    },
  },
  block_DUQnBfK11aHN7ZypDPfeug: {
    type: 'ColumnsContainer',
    data: {
      style: {
        backgroundColor: null,
        padding: {
          top: 8,
          bottom: 8,
          left: 0,
          right: 0,
        },
      },
      props: {
        columnsCount: 2,
        columns: [
          {
            childrenIds: ['block_GTPnMRuHqf1bCLk8VLAr4z'],
          },
          {
            childrenIds: ['block_LbEheyx16SX5S8GK2zY3sJ'],
          },
          {
            childrenIds: ['block_M6BwLBjomRNsdee4rdoA4y'],
          },
        ],
      },
    },
  },
  block_FLTQdJVBNsmRxurTZTSC2V: {
    type: 'Container',
    data: {
      style: {
        backgroundColor: '#ffffff',
        borderColor: null,
        borderRadius: null,
        padding: {
          top: 16,
          bottom: 24,
          left: 24,
          right: 24,
        },
      },
      props: {
        childrenIds: [
          'block_RNsVmDsY33ipzGLtRUsYys',
          'block_Y7W2h9xDuNreQdgMrv82KZ',
          'block_CSV8NQS6gBMQcFXvFAFo3Q',
          'block_LJcwZCh6z1EyFeeKH8Egts',
          'block_HSbX9aqHvSXw9GAB361vpM',
          'block_DUQnBfK11aHN7ZypDPfeug',
        ],
      },
    },
  },
  block_Qq64GeHw7K24Fgz5oX81kt: {
    type: 'Text',
    data: {
      style: {
        color: '#474849',
        backgroundColor: null,
        fontSize: 12,
        fontFamily: null,
        fontWeight: 'normal',
        textAlign: 'left',
        padding: {
          top: 24,
          bottom: 16,
          left: 24,
          right: 24,
        },
      },
      props: {
        text: 'Can we help? Just reply to this email.',
      },
    },
  },
};

export default SUBSCRIPTION_RECEIPT;
