import React, { FC, useState } from 'react';

import {
  VerticalAlignBottomOutlined,
  VerticalAlignCenterOutlined,
  VerticalAlignTopOutlined
} from '@mui/icons-material';
import { Stack, ToggleButton, Button } from '@mui/material';
import { ImageProps, ImagePropsSchema } from '@usewaypoint/block-image';

import BaseSidebarPanel from './helpers/base-sidebar-panel';
import RadioGroupInput from './helpers/inputs/radio-group-input';
import TextDimensionInput from './helpers/inputs/text-dimension-input';
import TextInput from './helpers/inputs/text-input';
import MultiStylePropertyPanel from './helpers/style-inputs/multi-style-property-panel';
import { createPortalModal } from "@blocks/portal-modal/portal-modal"
import { ImageSelector } from '@modules/email/email-designer/editor/inspector-drawer/configuration-panel/input-panels/helpers/image-selector';


type ImageSidebarPanelProps = {
  data: ImageProps;
  setData: (v: ImageProps) => void;
};

export default function ImageSidebarPanel({ data, setData }: ImageSidebarPanelProps) {
  const [, setErrors] = useState<Zod.ZodError | null>(null);

  const updateData = (d: unknown) => {
    const res = ImagePropsSchema.safeParse(d);
    if (res.success) {
      setData(res.data);
      setErrors(null);
    } else {
      setErrors(res.error);
    }
  };

  const handleChooseFile = async () => {
    return new Promise((resolve, reject) => {
      createPortalModal(ImageSelector as FC<unknown>, {}, { parent: {}, promise: { resolve, reject }, }).catch((err) => console.error(err));
    }).catch((err) => console.error(err));

  }

  const chooseFile = async () => {
    const hash = await handleChooseFile();
    if (hash)
      updateData({ ...data, props: { ...data.props, url: `${window.CRBaseURL}/api/file/static/${hash}` } });
  }

  return (
    <BaseSidebarPanel title="Image block">
      <Button
        component="label"
        role={undefined}
        variant="contained"
        tabIndex={-1}
        onClick={chooseFile}
      >
        Select Image
      </Button>

      <TextInput
        label="Alt text"
        defaultValue={data.props?.alt ?? ''}
        onChange={(alt) => updateData({ ...data, props: { ...data.props, alt } })}
      />
      <TextInput
        label="Click through URL"
        defaultValue={data.props?.linkHref ?? ''}
        onChange={(v) => {
          const linkHref = v.trim().length === 0 ? null : v.trim();
          updateData({ ...data, props: { ...data.props, linkHref } });
        }}
      />
      <Stack direction="row" spacing={2}>
        <TextDimensionInput
          label="Width"
          defaultValue={data.props?.width}
          onChange={(width) => updateData({ ...data, props: { ...data.props, width } })}
        />
        <TextDimensionInput
          label="Height"
          defaultValue={data.props?.height}
          onChange={(height) => updateData({ ...data, props: { ...data.props, height } })}
        />
      </Stack>

      <RadioGroupInput
        label="Alignment"
        defaultValue={data.props?.contentAlignment ?? 'middle'}
        onChange={(contentAlignment) => updateData({ ...data, props: { ...data.props, contentAlignment } })}
      >
        <ToggleButton value="top">
          <VerticalAlignTopOutlined fontSize="small" />
        </ToggleButton>
        <ToggleButton value="middle">
          <VerticalAlignCenterOutlined fontSize="small" />
        </ToggleButton>
        <ToggleButton value="bottom">
          <VerticalAlignBottomOutlined fontSize="small" />
        </ToggleButton>
      </RadioGroupInput>

      <MultiStylePropertyPanel
        names={['backgroundColor', 'textAlign', 'padding']}
        value={data.style}
        onChange={(style) => updateData({ ...data, style })}
      />
    </BaseSidebarPanel>
  );
}
