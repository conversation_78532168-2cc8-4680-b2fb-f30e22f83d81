
import { useEffect, useState, type FC } from "react";
import './image-selector.less';
import { Stack, ToggleButton, Button, styled } from '@mui/material';
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

import { PopupModalRef } from "@blocks/portal-modal/portal-modal";
import { fetchFormFilters, IFormData } from "@hooks/form-data";
import { request } from "@core/request/request";
import SpinLoader from "@components/common/spin-loader";
const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
});
interface ImageSelectorProps {
    getModal: () => PopupModalRef;
    promise: {
        resolve: (data: any) => void;
        reject: (reason: any) => void;
    };
}
type UploadType = IFormData & {
    name: string,
}
type Image = {
    name: string;
    id: number;
    hash_key: string;
}
export const ImageSelector: FC<ImageSelectorProps> = (props) => {
    const { getModal, promise } = props;
    const [images, setImages] = useState<IFormData[] | IFormData>([])
    const [uploading, setUploading] = useState(false)

    const onCancel = () => {
        getModal().closeModal();
        promise.resolve(null);
    };
    const onClick = (hash: any) => {
        getModal().closeModal();
        promise.resolve(hash);
    }
    const fetchData = async () => {

        await fetchFormFilters('upload', { pool: true, filter: { upload_type: 'static' } }).then(r => setImages(r.data));
    };
    useEffect(() => {
        fetchData();
    }, []);
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const body = new FormData();
            body.append('file', file, file.name);

            try {
                setUploading(true);
                await request({
                    url: `file/static`,
                    method: 'POST',
                    data: body,
                    pool: false,
                    headers: {
                        'accept': 'application/json',
                        'Accept-Language': 'en-US,en;q=0.8',
                        'Content-Type': `multipart/form-data;`,
                    }
                }).then(({ data }) => {
                    const { filehash } = JSON.parse(data);
                    onClick(filehash);
                }).catch((error) => {
                    promise.reject(error);
                    console.log(error);
                });
            } catch (error) {
                console.error('Error uploading image:', error);
            } finally {
                setUploading(false);
            }
        }
    };
    const handleDelete = async (hash: string) => {
        try {
            setUploading(true);
            await request({
                url: `file/static/${hash}`,
                method: 'DELETE',
                pool: false,
            }).then(({ data }) => {
                setImages(images => images.filter((image: Image) => image.hash_key !== hash))
            }).catch((error) => {
            });
        } catch (error) {
            console.error('Error deleting image:', error);
        } finally {
            setUploading(false);
        }

    };
    return (
        <GenericCardContainer
            title="Select or Upload Image"
            onClick={onCancel}
            icon={icons.common.crossIcon}
        >
            <>
                <div className="image-gallery-layout">
                    {uploading ? <SpinLoader loading={true} /> :
                        <>
                            <div className="gallery">
                                <div className="img-gallery">
                                    {
                                        images?.map((image: IFormData | UploadType) => (
                                            <div className="img-gallery-item" key={image.id}>
                                                <img src={icons.form.trash} className="icon" onClick={() => handleDelete(image.hash_key)} />
                                                <figure>
                                                    <img src={`${(window as any).CRBaseURL}/api/file/static/${image.hash_key}`} width="1280" height="1920" alt="Image #1"
                                                        onClick={() => onClick(image.hash_key)}
                                                    />
                                                    <figcaption>{image.name}</figcaption>
                                                </figure>
                                            </div>
                                        ))
                                    }

                                </div>
                            </div>
                            <div className="image-uploader">
                                <div>
                                    <Button
                                        component="label"
                                        role={undefined}
                                        variant="contained"
                                        tabIndex={-1}
                                        startIcon={<CloudUploadIcon />}
                                        onClick={() => { }}
                                    >
                                        Upload Image
                                        <VisuallyHiddenInput accept="image/*" type="file" onChange={handleFileUpload} />
                                    </Button>
                                </div>
                            </div>
                        </>

                    }

                </div>
            </>


        </GenericCardContainer>
    );
};
