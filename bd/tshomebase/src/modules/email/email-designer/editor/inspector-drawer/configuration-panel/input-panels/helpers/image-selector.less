/* Gallery layout */
:root {
  --gallery-items-per-row: 4;
}
.image-gallery-layout {
  height: 80vh;
  width: 80vw;
  display: flex;
  padding: 10px;
  .gallery {
    width: 90%;
    height: 100%;
    padding: 10px;
    border-right: 1px solid gray;
    overflow: auto;
  }
  .image-uploader {
    flex: 1;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: end;
  }

  .img-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .img-gallery-item {
    //   height: 20%;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.75);
    flex: 0 0
      calc(
        (100% - (10px * (var(--gallery-items-per-row) - 1))) /
          var(--gallery-items-per-row)
      );

    img {
      max-width: 100%;
      vertical-align: middle;
      height: auto;
      border-radius: 6px;
      aspect-ratio: 3 / 2;
      object-fit: cover;
    }
  }

  /* Gallery styles */

  .img-gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    height: 200px;

    figure {
      margin: 0;
      cursor: pointer;
    }

    figcaption {
      position: absolute;
      inset: auto auto 0 0;
      cursor: default;
      width: 100%;
      padding: 1rem;

      color: #ffffff;
      background-color: hsl(0 0% 0% / 90%);

      transition: opacity 0.25s ease-in-out;
      opacity: 0;
    }
    .icon {
      position: absolute;
      top: 10px;
      right: 10px;
      opacity: 0;
      cursor: pointer;
      filter: invert(65%) sepia(61%) saturate(5112%) hue-rotate(320deg)
        brightness(102%) contrast(101%);
    }

    &:hover {
      figcaption,
      .icon {
        opacity: 1;
      }
    }
  }
}
@media only screen and (width >= 1024px) {
  .img-gallery {
    --gallery-items-per-row: 4;
  }
}

@media only screen and (768px < width < 1024px) {
  .img-gallery {
    --gallery-items-per-row: 3;
  }
}

@media only screen and (540px < width < 768px) {
  .img-gallery {
    --gallery-items-per-row: 2;
  }
}
