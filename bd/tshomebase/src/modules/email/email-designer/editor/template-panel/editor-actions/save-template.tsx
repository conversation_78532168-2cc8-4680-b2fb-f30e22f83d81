import React, { useMemo } from 'react';

import SaveIcon from '@mui/icons-material/Save';
import { IconButton, Tooltip } from '@mui/material';

import { useDocument } from '../../../documents/editor/editor-context';
import { renderToStaticMarkup } from '@usewaypoint/email-builder';
import { request } from '@core/request/request';

export default function SaveTemplate(props: any) {
  const { update } = props;
  const document = useDocument();

  const html = useMemo(() => renderToStaticMarkup(document, { rootBlockId: 'root' }), [document]);
  const json = useMemo(() => JSON.stringify(document), [document]);

  const handleSave = () => {
    request({
      url: `form/template_email/${props.id}`,
      method: 'PUT',
      data: {
        ...props.email,
        template_data: json,
        template_html: html,
      },
      pool: false,
    }).then(resp => {
    }).catch((error) => {
    });

  };


  return (
    <Tooltip title="Save Template">
      <IconButton onClick={handleSave} >
        <SaveIcon fontSize="medium" />
      </IconButton>
    </Tooltip>
  );
}
