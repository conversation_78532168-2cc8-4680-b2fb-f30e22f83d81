import { MonitorOutlined, PhoneIphoneOutlined } from '@mui/icons-material';
import { Box, Stack, SxProps, Tab, Tabs, ToggleButton, ToggleButtonGroup, Tooltip } from '@mui/material';
import { Reader } from '@usewaypoint/email-builder';

import EditorBlock from '../../documents/editor/editor-block';
import {
  resetDocument,
  setDocument,
  setSelectedScreenSize,
  useDocument,
  useSelectedMainTab,
  useSelectedScreenSize,
} from '../../documents/editor/editor-context';
import ToggleInspectorPanelButton from '../inspector-drawer/toggle-inspector-panel-button';

import MainTabsGroup from './main-tabs-group';
import SaveTemplate from './editor-actions/save-template';
// import SendTest from '@modules/email/email/App/TemplatePanel/EditorActions/SendTest';
// import ToggleSamplesPanelButton from '@modules/email/email/App/SamplesDrawer/ToggleSamplesPanelButton';
// import EMPTY_EMAIL_MESSAGE from '@modules/email/email/getConfiguration/sample/empty-email-message';
import { useNavigation } from '@core/navigation';
import { useEffect } from 'react';

export default function TemplatePanel(props: any) {
  const selectedMainTab = props.preview ? 'preview' : useSelectedMainTab();
  const { email } = props;
  const selectedScreenSize = useSelectedScreenSize();
  let document = useDocument();

  useEffect(() => {
    if (email?.template_data) {
      resetDocument(email?.template_data);
      document = email?.template_data;
    }
  }, [email?.template_data])
  let mainBoxSx: SxProps = {
    height: '100%',
  };
  if (selectedScreenSize === 'mobile') {
    mainBoxSx = {
      ...mainBoxSx,
      margin: '32px auto',
      width: 370,
      height: 800,
      boxShadow:
        'rgba(33, 36, 67, 0.04) 0px 10px 20px, rgba(33, 36, 67, 0.04) 0px 2px 6px, rgba(33, 36, 67, 0.04) 0px 0px 1px',
    };
  }

  const handleScreenSizeChange = (_: unknown, value: unknown) => {
    switch (value) {
      case 'mobile':
      case 'desktop':
        setSelectedScreenSize(value);
        return;
      default:
        setSelectedScreenSize('desktop');
    }
  };

  const renderMainPanel = () => {
    switch (selectedMainTab) {
      case 'editor':
        return (
          <Box sx={mainBoxSx}>
            <EditorBlock id="root" />
          </Box>
        );
      case 'preview':
        return (
          <Box sx={mainBoxSx}>
            <Reader document={document} rootBlockId="root" />
          </Box>
        );
    }
  };

  return (
    <>
      {!props.preview && (<Stack
        sx={{
          height: 49,
          borderBottom: 1,
          borderColor: 'divider',
          backgroundColor: 'white',
          position: 'sticky',
          top: 0,
          zIndex: 'appBar',
          px: 1,
        }}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        {/* <ToggleSamplesPanelButton /> */}
        <Stack px={1} direction="row" gap={1} width="100%" justifyContent="space-between" alignItems="center">
          <Stack direction="row" spacing={1} width="30%">
            <MainTabsGroup />
          </Stack>
          <Stack direction="row" spacing={1} width='90%'>
            <ToggleButtonGroup value={selectedScreenSize} exclusive size="small" onChange={handleScreenSizeChange}>
              <ToggleButton value="desktop">
                <Tooltip title="Desktop view">
                  <MonitorOutlined fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="mobile">
                <Tooltip title="Mobile view">
                  <PhoneIphoneOutlined fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
          </Stack>

          <Stack direction="row" spacing={2}>
            <SaveTemplate {...props} />
          </Stack>
          {
            // props.mode === 'edit' && (
            //   <Stack direction="row" spacing={2}>
            //     <SendTest {...props} />
            //   </Stack>
            // )
          }
        </Stack>
        <ToggleInspectorPanelButton />
      </Stack>)}
      <Box sx={{ height: 'calc(100vh - 175px)', overflow: 'auto', minWidth: 370 }}>{renderMainPanel()}</Box>
    </>
  );
}
