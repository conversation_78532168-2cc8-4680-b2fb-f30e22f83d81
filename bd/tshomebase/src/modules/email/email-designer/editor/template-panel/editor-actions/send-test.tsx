import React, { useMemo } from 'react';

import ForwardToInboxIcon from '@mui/icons-material/ForwardToInbox';
import { Icon<PERSON>utton, Tooltip } from '@mui/material';

import { useDocument } from '../../../documents/editor/editor-context';
import { renderToStaticMarkup } from '@usewaypoint/email-builder';
import { useFormData } from '../../../../../../hooks/form-data';
import { request } from '@core/request/request';

export default function SendTest(props: any) {
    const { tabData } = props;
    // const [fd, refresh, update] = useFormData(tabData.form, tabData.id);
    const document = useDocument();
    const code = useMemo(() => renderToStaticMarkup(document, { rootBlockId: 'root' }), [document]);
    const json = useMemo(() => JSON.stringify(document), [document]);
    const data = {
        to: 'm<PERSON><EMAIL>',
        code: '',
        inv_no: 123,
        subject: 'Test send email'
    }
    const handleSend = () => {
        // update({ template_data: json, template_html: code });
        // // console.log(code, json, props);
        request({
            url: `api/email`,
            method: 'POST',

            pool: false,
        }).then(resp => {
            // console.log(resp)
            // resolve({
            //     success: true,
            //     data: resp.data?.length ? resp.data[0] : {},
            // });
        }).catch((error) => {
            // console.log(error)
            // reject({
            //     success: false,
            //     data: error,
            // });
        });

    };
    {/*  */ }
    return (
        <Tooltip title="Send Test Email">
            <IconButton onClick={handleSend} >
                <ForwardToInboxIcon fontSize="medium" />
            </IconButton>
        </Tooltip>
    );
}
