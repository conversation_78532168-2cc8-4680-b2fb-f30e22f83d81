import React, { useEffect, useState } from "react";

import { Stack, useTheme } from "@mui/material";

import {
  resetDocument,
  setDocument,
  useInspectorDrawerOpen,
} from "../documents/editor/editor-context";

import InspectorDrawer, { INSPECTOR_DRAWER_WIDTH } from "./inspector-drawer";
// import TemplatePanel from "./TemplatePanel";

function useDrawerTransition(
  cssProperty: "margin-left" | "margin-right",
  open: boolean
) {
  const { transitions } = useTheme();
  return transitions.create(cssProperty, {
    easing: !open ? transitions.easing.sharp : transitions.easing.easeOut,
    duration: !open
      ? transitions.duration.leavingScreen
      : transitions.duration.enteringScreen,
  });
}
import { useNavigation } from "@core/navigation";
import { TEditorConfiguration } from "@modules/email/email-designer/documents/editor/core";
import EMPTY_EMAIL_MESSAGE from "@modules/email/email-designer/get-configuration/sample/empty-email-message";
import { useFormData } from "@hooks/form-data";
import TemplatePanel from "@modules/email/email-designer/editor/template-panel";
const EditorJS = (props: any) => {
  const nav = useNavigation(props, '/');
  nav.history.current = `/${props.tabData.id}`
  const inspectorDrawerOpen = useInspectorDrawerOpen();
  const samplesDrawerOpen = true;
  const [mode, setMode] = useState("add");
  const { tabData } = props;

  const [fd, , update] = useFormData(tabData.form, tabData.id);

  const email = fd?.data;
  const marginLeftTransition = useDrawerTransition(
    "margin-left",
    samplesDrawerOpen
  );
  const marginRightTransition = useDrawerTransition(
    "margin-right",
    inspectorDrawerOpen
  );
  useEffect(() => {
    if (email?.template_data && !props.preview) {
      resetDocument(email?.template_data as TEditorConfiguration);
      setMode("edit");
    } else {
      setDocument(EMPTY_EMAIL_MESSAGE as TEditorConfiguration)
    }
  }, [email.loading, tabData.id]);
  return (
    !email.loading && (
      <div key={email?.id} id={tabData.id}>
        {!props.preview && (
          <>
            <InspectorDrawer id={tabData.id} />
            {/* <SamplesDrawer /> */}
          </>
        )}

        <Stack
          sx={{
            marginRight:
              !props.preview && inspectorDrawerOpen
                ? `${INSPECTOR_DRAWER_WIDTH}px`
                : 0,
            // marginLeft: samplesDrawerOpen ? `${SAMPLES_DRAWER_WIDTH}px` : 0,
            marginLeft: 0,
            transition: [marginLeftTransition, marginRightTransition].join(
              ", "
            ),
          }}
        >
          <TemplatePanel
            {...props}
            email={email}
            mode={mode}
            update={update}
          />
        </Stack>
      </div>
    )
  );
};
export default EditorJS;
