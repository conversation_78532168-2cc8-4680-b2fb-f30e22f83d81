import "./email-inspect.less";
import { useFormData } from "@hooks/form-data";
import EditorJ<PERSON> from "@modules/email/email-designer/editor";
import { TabData } from "@blocks/tab-list/tab-list";
import { request } from "@core/request/request";
import { toast } from "@components/toast";

type EmailInspectComponentProps = {
	parentProps: any;
	tabData: any;
	rowData: any;
};
export type EmailTemplate = {
	name: string;
	code: string;
	subject: string;
	data?: EmailTemplate;
	template_data: any;
};
export const EmailInspectComponent = (props: EmailInspectComponentProps) => {
	const { tabData, rowData } = props;
	const actions = props.parentProps.tabViewActions;
	const cloneTemplate = async () => {
		return request({
			url: `form/template_email`,
			method: "POST",
			data: {
				code: `${rowData.code}_clone`,
				subject: rowData.subject,
				active: rowData.active,
				allow_sync: rowData.allow_sync,
				module: rowData.module,
				name: rowData.name,
				template_data: rowData.template_data,
				template_html: rowData.template_html,
			},
			pool: false,
		})
			.then(({ data }) => {
				toast({
					type: "success",
					message: "Successfully cloned template",
					autoClose: 2000,
					position: "bottom-center",
					theme: "dark"
				});
				return data as TabData;
			})
			.catch((_) => {
				toast({
					type: "error",
					message: "Unable to Clone Template",
					autoClose: 2000,
					position: "bottom-center",
					theme: "dark"
				}); //error
				console.error("Unable to Clone Template");
				return null;
			});
	};
	const [email, refresh] = useFormData(tabData.form, tabData.id) as unknown as [
		EmailTemplate | undefined,
		() => void
	];
	return (
		<>
			<div className="wf-inspect-comp">
				<div className="wf-info-cards" style={{ height: "100%" }}>
					{/* <div className="wf-card">
                        <div className="header">
                            <>Template Information</>
                        </div>
                        <div className="body">
                            <DetailPortion label="Name" value={rowData?.name || ""} styleClass="detail-packet-inline" />
                            <DetailPortion label="Code" value={rowData?.code || ""} styleClass="detail-packet-inline" />
                        </div>
                    </div> */}

					{email?.data?.template_data && (
						<div className="wf-card">
							<div className="header">
								<>Preview</>
							</div>
							<div className="body">
								<EditorJS {...props} preview={true} email={rowData} key={tabData.id} />
							</div>
						</div>
					)}
				</div>
				<div className="wf-actions">
					<button
						className="wf-btn wf-edit"
						onClick={() => {
							actions.openTab(
								tabData.id,
								tabData.label,
								props.rowData?.archived ? "read" : "edit",
								tabData.form,
								{}
							);
						}}
					>
						{props.rowData?.archived ? "View" : "Edit"}
					</button>
					<button
						className="wf-btn wf-design"
						onClick={() => {
							let tabs = actions?.getOpenTabs()?.filter((f: TabData) => f.mode != "design") || [];
							actions.overrideOpenTabs(tabs);
							actions.openTab(tabData.id, tabData.label, "design", tabData.form, {});
						}}
					>
						Design
					</button>
					<button
						className="wf-btn wf-clone"
						onClick={async () => {
							const clone = await cloneTemplate();
							if (clone) {
								actions.openTab(clone.id, clone.name, "edit", tabData.form, {});
							}
						}}
					>
						Clone
					</button>
				</div>
			</div>
		</>
	);
};
