import React, { FC, useEffect, useRef } from "react";
import "./price-matrix-editor.less";
import { DSLListCompactView } from "@blocks/dsl-list-compact-view/dsl-list-compact-view";
import DSLCardView, { DSLCardViewProps } from "@blocks/dsl-card-view/dsl-card-view";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { createPortalModal } from "@blocks/portal-modal";
import { MatrixAssignment } from "@modules/billing/helper/matrix-assignment";
import { toast } from "react-toastify";
import { request } from "@core/request";
import { fetchFormFilters, IFormData, useFormData } from "@hooks/index";
import { useQueryForm } from "@hooks/query";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import NoDataFound from "@components/common/no-data-found";
import LoaderNoData from "@components/common/loader-no-data";

interface PriceMatrixEditorProps extends DSLCardViewProps {}

const PriceMatrixEditor: React.FC<PriceMatrixEditorProps> = (props) => {
	const { record: contractId, form } = props;

	if (form !== "payer_contract" || !contractId) {
		// Let is crash i don't care about react hooks rules
		return null;
	}

	const [contract] = useFormData(form, contractId);
	const [currentInventory, setCurrentInventory] = React.useState<{
		id: string | number;
		matrixItemId: string | number;
		rowData: IFormData | null;
	}>({
		id: "",
		matrixItemId: "",
		rowData: null,
	});
	//x1=${contract.data.assigned_matrix_id}&sort=name
	const inventoryGrid = useQueryForm("list_pricing_matrix_inv");
	const [inventorySearch, setInventorySearch] = React.useState("");

	const inventoryListRef = useRef<AdvancedGridRef | null>(null);
	const payerListRef = useRef<AdvancedGridRef | null>(null);

	useEffect(() => {
		if (inventoryListRef.current) {
			inventoryListRef.current.advanced.search(inventorySearch);
		}
	}, [inventorySearch]);

	const matrixId = contract?.data?.assigned_matrix_id;

	const columns = {
		inventory: ["name", "generic_name", "short_brand_name"],
		payer: ["organization", "short_code", "type_id"],
	};

	const linkMapInventory: DSLDrawLinkMap = {
		link: "payer_price_matrix",
		links: ["payer_price_matrix"],
		linkid: { payer_price_matrix: matrixId },
	};

	if (currentInventory.id) {
		linkMapInventory.linkid.inventory = currentInventory.id;
		linkMapInventory.links.push("inventory");
	}

	const openForm = (form: string, id: string | number) => {
		window.Flyout.open({
			form: form,
			record: id,
			card: "read",
			tab_can_edit_override: () => false,
			tab_can_archive_override: () => false,
			autoRecoverEnabled: false,
		});
	};
	console.log(linkMapInventory);

	const assignToPayer = async () => {
		if (contract.data.contract_type == "Contract") {
			const payers = await fetchFormFilters("payer", {
				filter: {
					assigned_contract_id: contract.data.id,
				},
			});
			if (payers.data.length > 0) {
				toast.error("Cannot assign more than one payer to this contract (non-shared contracts)");
				return;
			}
		}
		const payerId = await new Promise((resolve, reject) => {
			createPortalModal(
				MatrixAssignment as FC<unknown>,
				{},
				{
					contractId: contract.data.id,
					promise: { resolve, reject },
				}
			);
		});
		if (!payerId) return;
		const p = request({
			url: `/form/payer/${payerId}/`,
			method: "PUT",
			data: {
				assigned_contract_id: contract.data.id,
			},
		});
		const result = toast.promise(
			p,
			{
				pending: "Assigning to payer",
				success: "Assigned to payer",
				error: "Failed to assign to payer",
			},
			{
				autoClose: 2000,
				hideProgressBar: true,
				position: "bottom-center",
				theme: "dark",
			}
		);
		result.then((d) => {
			payerListRef.current?.advanced.refresh(true);
		});
	};

	const removeFromPayer = async (payerId: number) => {
		if (!payerId) return;
		const p = request({
			url: `/form/payer/${payerId}/`,
			method: "PUT",
			data: {
				assigned_contract_id: null,
			},
		});
		const result = toast.promise(
			p,
			{
				pending: "Removing from payer.",
				success: "Removed from payer.",
				error: "Failed to remove from payer.",
			},
			{
				autoClose: 2000,
				hideProgressBar: true,
				position: "bottom-center",
				theme: "dark",
			}
		);
		result.then((d) => {
			payerListRef.current?.advanced.refresh(true);
		});
	};

	if (contract.loading) {
		return <LoaderNoData loading />;
	}
	if (contract.failed) {
		return <NoDataFound text="Error Opening Contract." />;
	}
	if (!contract?.data?.id) {
		return <NoDataFound text="Contract Not Found." />;
	}
	if (!contract?.data?.assigned_matrix_id) {
		return <NoDataFound text="Contract Does Not Have a Pricing Matrix. Contact Support." />;
	}

	return (
		<div className="matrix-editor">
			<div className="matrix-editor-row">
				<div className="matrix-editor-item inventory-compact-list card-one">
					{inventoryGrid.state == "success" ? (
						<DSLListCompactView
							form={inventoryGrid.form}
							hideFilter={true}
							customColumns={inventoryGrid.customColumns}
							onRef={(ref) => {
								inventoryListRef.current = ref;
							}}
							rowClassRules={{
								"row-mark-danger-inv": (params) => {
									if (!params.data) {
										return false;
									}
									if (params?.data?.matrix_id) return false;
									return true;
								},
							}}
							gridSourceOverrideURL={inventoryGrid.gridSourceOverrideURL + `?x1=${matrixId}&sort=name`}
							rowClickCallback={(data) => {
								const rowData = data.rowData as IFormData;
								if (!rowData) return;
								setCurrentInventory((invItem) => {
									if (invItem.id == rowData.id) {
										return {
											id: "",
											matrixItemId: "",
											rowData: null,
										};
									}
									return {
										id: rowData.id,
										matrixItemId: rowData.matrix_id,
										rowData: rowData,
									};
								});
							}}
						/>
					) : (
						<div className="no-item-cont">
							{inventoryGrid.state == "loading" ? "Loading..." : "Failed: unable to load grid."}
						</div>
					)}
					<div className="searchbox">
						<input
							type="text"
							placeholder="Search Inventory"
							value={inventorySearch}
							onChange={(e) => {
								setInventorySearch(e.target.value);
								inventoryListRef.current?.advanced.search(e.target.value || "");
							}}
							className="form-input-field"
						/>
					</div>
				</div>
				<div className="matrix-editor-item dsl-editor">
					{currentInventory.id ? (
						<DSLCardView
							key={JSON.stringify(currentInventory)}
							form="payer_price_matrix_item"
							disableRouting={true}
							card={currentInventory.matrixItemId ? "edit" : "addfill"}
							xid={
								(currentInventory.matrixItemId
									? currentInventory.matrixItemId
									: "payer_price_matrix_item") as string
							}
							preset={
								currentInventory.matrixItemId
									? {}
									: {
											inventory_id: currentInventory.id,
											payer_price_matrix_id: matrixId,
											contract_type: contract.data.contract_type,
									  }
							}
							tabData={currentInventory}
							{...linkMapInventory}
							record={currentInventory.matrixItemId || undefined}
							ddRef={(ref) => {}}
							overrideDos={{
								cancel: {
									enabled: false,
								},
								verify: {
									enabled: false,
								},
							}}
							onSaved={(fd, tab, ref) => {
								const newRowData = {
									...currentInventory.rowData,
									matrix_id: fd.record,
								} as IFormData;
								setCurrentInventory({
									...currentInventory,
									matrixItemId: fd.record,
									rowData: newRowData || null,
								});
								inventoryListRef.current?.advanced.row("edit", newRowData);
							}}
						/>
					) : (
						<div className="no-item-cont card-one">No Inventory Item Selected</div>
					)}
				</div>
			</div>
			<div className="matrix-editor-row">
				<DSLListCompactView
					form="payer"
					className="card-one"
					onRef={(ref) => {
						payerListRef.current = ref;
					}}
					hideFilter={true}
					customColumns={columns.payer}
					filtersPresetFixed={{ assigned_contract_id: contractId }}
					newPreset={{ assigned_contract_id: contractId }}
					rowClickCallback={(event) => {
						if (event.type === "dblclick") {
							openForm("payer", event.id);
						}
					}}
					buttonOverrides={{
						add: {
							onClick: () => assignToPayer(),
						},
						edit: {
							enabled: false,
						},
						delete: {
							label: "Remove",
							onClick: (action: string, id: number) => {
								removeFromPayer(id);
							},
						},
					}}
				/>
			</div>
		</div>
	);
};

export default PriceMatrixEditor;
