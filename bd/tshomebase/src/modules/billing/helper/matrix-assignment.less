@import "../../../components/cards/generic-card-container/generic-card-container.less";

.ReactModalPortal {
	.generic-card-container;

	.matrix-assignment {
		display: flex;
		flex-direction: column;
		gap: 10px;
		max-width: 500px;
		min-width: 320px;

		.error {
			color: #f56d63;
		}

		.btn-primary {
			width: 50%;

			&:disabled {
				background-color: @light-gray;
				color: @light-gray;
			}

			&:disabled:hover {
				color: @light-gray;
			}
		}

		.matrix-assignment-footer {
			display: flex;
			justify-content: flex-end;
		}
	}
}
