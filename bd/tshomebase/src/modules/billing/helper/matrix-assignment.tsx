import React, { useEffect, useState } from "react";
import type { FC } from "react";
import type { PopupModalRef } from "@blocks/portal-modal/portal-modal";
import "./matrix-assignment.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { toPresentableLabel } from "@utils/fx";
import { StylesConfig } from "react-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { FieldSelect } from "@dsl/fields/field-select";

interface MatrixAssignmentProps {
	getModal: () => PopupModalRef;
	contractId: string;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
}

export const MatrixAssignment: FC<MatrixAssignmentProps> = (props) => {
	const { getModal, contractId, promise } = props;
	const [record, setRecord] = useState({ id: "", auto_name: "", row: {} });
	const [warning, setWarning] = useState("");
	useEffect(() => {
		if (!contractId) {
			getModal().closeModal();
			promise.resolve(null);
		}
	}, []);

	if (!contractId) {
		return null;
	}
	const f = toPresentableLabel("payer");
	return (
		<GenericCardContainer
			title={`Add ${f}`}
			onClick={() => {
				getModal().closeModal();
				props.promise.resolve(null);
			}}
			icon={icons.common.crossIcon}
		>
			<div className="matrix-assignment">
				<div className="info-detail-card">{`Please select an ${f} to continue.`}</div>
				<FieldSelect
					form="payer"
					defaultValueSource={record.id}
					defaultValueAutoName={record.auto_name || ""}
					multi={false}
					extraParams={`filter=assigned_contract_id:!${contractId}&fields=list`}
					sort="auto_name"
					disabled={false}
					onChange={(val, an, row) => {
						if (row?._raw?.assigned_contract_id) {
							let error = "Another contract is already assigned to selected record.";
							if (row._raw["assigned_contract_id_auto_name"]) {
								error =
									row._raw["assigned_contract_id_auto_name"] +
									" contract is already assigned to selected record.";
							}
							setWarning(error);
						} else {
							setWarning("");
						}
						setRecord({ id: val, auto_name: an, row });
					}}
					customStyles={
						{
							...SelectConfig.dsl.style,
							valueContainer: (provided, state) => ({
								...provided,
								maxWidth: "320px",
								fontWeight: "bold",
							}),
						} as StylesConfig
					}
					theme={SelectConfig.dsl.theme}
					placeholder={`Select ${f}`}
				/>
				<div className="info-detail-card error">{warning}</div>
				<div className="matrix-assignment-footer">
					<button
						className="btn-primary"
						disabled={!record.id}
						onClick={() => {
							getModal().closeModal();
							promise.resolve(record.id || null);
						}}
					>
						Add
					</button>
				</div>
			</div>
		</GenericCardContainer>
	);
};
