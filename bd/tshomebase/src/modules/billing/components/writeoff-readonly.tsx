import React from "react";
import "./shared.less";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	TablePagination,
	Toolbar,
	alpha,
	Typography,
	ThemeProvider,
	Button,
} from "@mui/material";

import { useState } from "react";
import { baseTheme } from "@core/theme";
import currency from "currency.js";
import { ChargeLinesData } from "@modules/billing/components/types";

export const WriteOffsGridReadOnly = (props: { data: ChargeLinesData[] }) => {
	const { data } = props;
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(25);

	const totalApplied = data.reduce((sum, item) => currency(sum).add(item.amount).value, 0);

	const openCharge = async (id: number) => {
		window.Flyout.open({
			form: "ledger_charge_line",
			tab_can_edit_override: () => false,
			card: "read",
			record: id,
		});
	};

	const headerStyle = {
		fontWeight: "bold",
		fontSize: "1.2rem",
	};
	const bodyStyle = {
		fontSize: "1.2rem",
	};
	const btnStyleBody = {
		...bodyStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};
	const btnStyleHeader = {
		...headerStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};

	return (
		<div className="billing-writeoff-grid">
			<ThemeProvider theme={baseTheme}>
				<Paper sx={{ width: "100%", mb: 2, borderRadius: 2 }}>
					<Toolbar
						sx={[
							{
								justifyContent: "space-between",
								minHeight: "35px !important",
								borderTopLeftRadius: 2,
								borderTopRightRadius: 2,
							},
							{
								bgcolor: (theme) =>
									alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
							},
						]}
					>
						<div className="toolbar-columns go-left">
							{data.length === 0 ? (
								<Typography
									style={bodyStyle}
									sx={{ flex: "1 1 100%" }}
									color="inherit"
									variant="subtitle1"
									component="div"
								>
									No records to display.
								</Typography>
							) : null}
						</div>
						<div className="toolbar-columns go-right">
							<Typography style={bodyStyle} color="inherit" variant="subtitle1" component="div">
								Total Applied: {window.numeral(totalApplied).format("$0,0.00")}
							</Typography>
						</div>
					</Toolbar>

					<TableContainer
						sx={{
							borderRadius: 2,
							fontSize: "6rem",
						}}
					>
						<Table size="small">
							<TableHead>
								<TableRow>
									<TableCell size="small" style={btnStyleHeader}></TableCell>
									<TableCell size="medium" style={headerStyle}>
										Charge No
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Item
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Charge Qty
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Charge Unit
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Balance Due
									</TableCell>
									<TableCell
										size="medium"
										style={headerStyle}
										sx={{ width: "150px", textAlign: "center" }}
									>
										WriteOff Amount
									</TableCell>
								</TableRow>
							</TableHead>
							<TableBody>
								{data.slice(page * rowsPerPage, (page + 1) * rowsPerPage).map((row) => {
									return (
										<TableRow key={row.id}>
											<TableCell
												sx={{
													justifyContent: "center",
													textAlign: "center",
												}}
												style={btnStyleBody}
											>
												{row.id && (
													<Button
														onClick={() => openCharge(row.id)}
														variant="outlined"
														size="small"
													>
														View Charge
													</Button>
												)}
											</TableCell>
											<TableCell style={bodyStyle}>{row.charge_no}</TableCell>
											<TableCell style={bodyStyle}>{row.inventory_id_auto_name}</TableCell>
											<TableCell style={bodyStyle}>{row.charge_quantity}</TableCell>
											<TableCell style={bodyStyle}>{row.charge_unit}</TableCell>
											<TableCell style={bodyStyle}>
												{window.numeral(row.balance_due || 0).format("$0,0.00")}
											</TableCell>
											<TableCell style={bodyStyle} sx={{ width: "150px" }}>
												{window.numeral(row.amount || 0).format("$0,0.00")}
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					</TableContainer>
					<TablePagination
						rowsPerPageOptions={[25, 50, 100]}
						component="div"
						size="medium"
						count={data.length}
						rowsPerPage={rowsPerPage}
						page={page}
						onPageChange={(_, newPage) => setPage(newPage)}
						onRowsPerPageChange={(event) => {
							setRowsPerPage(parseInt(event.target.value, 10));
							setPage(0);
						}}
					/>
				</Paper>
			</ThemeProvider>
		</div>
	);
};
