import React, { useEffect } from "react";
import "./shared.less";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	TextField,
	TablePagination,
	Checkbox,
	Toolbar,
	alpha,
	Typography,
	IconButton,
	Tooltip,
	ThemeProvider,
	<PERSON><PERSON>,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";

import { useState } from "react";
import { baseTheme } from "@core/theme";
import { request } from "@core/request";
import { toast } from "react-toastify";
import currency from "currency.js";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { ChargeLinesData } from "@modules/billing/components/types";

type AdjustmentGridProps = {
	handle: {
		parent: IDSLDrawSubForm;
	};
	patient_id: number;
	invoice_no: string;
};

export const AdjustmentGrid = (props: AdjustmentGridProps) => {
	const { handle } = props;
	const [data, setData] = useState<ChargeLinesData[]>([]);
	const [dataIdMap, setDataIdMap] = useState<{ [key: number]: ChargeLinesData }>({});
	const [adjustments, setAdjustments] = useState<{ [key: number]: number }>({});
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(25);
	const [selected, setSelected] = useState<number[]>([]);
	const [totalApplied, setTotalApplied] = useState(0);

	const updateDSLJSONField = async () => {
		if (!handle?.parent?.value_field) return;
		const dataToSave: ChargeLinesData[] = [];
		selected.forEach((id) => {
			const row = dataIdMap[id];
			if (!row) {
				console.error("AdjustmentGrid: Row not found for id:", id);
			}
			dataToSave.push({
				...row,
				charge_no: row.charge_no,
				amount: currency(adjustments[id] || 0).value,
			});
		});
		const totalApplied = dataToSave.reduce((sum, item) => currency(sum).add(item.amount).value, 0);
		setTotalApplied(totalApplied);
		handle.parent.value_field("charge_lines_applied", JSON.stringify(dataToSave), true, true);
	};

	const handleAdjustmentChange = React.useCallback(
		(id: number, value: string) => {
			const numValue = parseFloat(value);
			const row = data.find((item) => item.id === id);
			if (!row || isNaN(numValue)) {
				setAdjustments((prev) => ({ ...prev, [id]: 0 }));
				return;
			}

			if (numValue >= 0 && numValue <= row.balance_due) {
				setAdjustments((prev) => ({ ...prev, [id]: numValue }));
			}
		},
		[data, dataIdMap]
	);

	const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.checked) {
			const newSelected = data.map((row) => row.id);
			setSelected(newSelected);
			return;
		}
		setSelected([]);
	};

	const clearSelections = () => {
		setSelected([]);
		setAdjustments({});
	};

	const fetchData = () => {
		const p = request({
			url: `/form/ledger_charge_line/?filter=patient_id:${props.patient_id || -1}&filter=invoice_no:${
				props.invoice_no || -1
			}`,
		});
		const result = toast.promise(
			p,
			{
				pending: "Please wait while we pull charge lines...",
				success: "Successfully pulled charge lines",
				error: "An unexpected error occurred: Please contact support!",
			},
			{
				autoClose: 2000,
				hideProgressBar: true,
				position: "bottom-center",
				theme: "dark",
			}
		);
		result.then((d) => {
			if (d.data && Array.isArray(d.data) && d.data.length > 0) {
				setData(d.data);
				setDataIdMap(
					d.data.reduce((acc, item) => {
						acc[item.id] = item;
						return acc;
					}, {})
				);
			}
		});
		result.catch((e) => {
			console.error("AdjustmentGrid:", e);
		});
	};

	// Add handler for selecting individual rows
	const handleSelect = (id: number) => {
		const selectedIndex = selected.indexOf(id);
		let newSelected: number[] = [];

		if (selectedIndex === -1) {
			newSelected = [...selected, id];
			setAdjustments((prev) => {
				delete prev[id];
				return prev;
			});
		} else {
			newSelected = selected.filter((item) => item !== id);
			setAdjustments((prev) => {
				if (prev[id] !== undefined || prev[id] !== null) {
					return prev;
				} else {
					prev[id] = 0;
					return prev;
				}
			});
		}

		setSelected(newSelected);
	};

	const openCharge = async (id: number) => {
		window.Flyout.open({
			form: "ledger_charge_line",
			tab_can_edit_override: () => false,
			card: "read",
			record: id,
		});
	};

	useEffect(() => {
		fetchData();
	}, []);

	useEffect(() => {
		updateDSLJSONField();
	}, [selected, adjustments]);

	const AdjustmentOptions = {
		clearSelections,
	};
	if (handle?.parent) {
		// update the coffee function ref after each render
		handle.parent.rxFieldOpts = { adjustments: AdjustmentOptions };
	}

	const numSelected = selected.length;

	const headerStyle = {
		fontWeight: "bold",
		fontSize: "1.2rem",
	};
	const bodyStyle = {
		fontSize: "1.2rem",
	};
	const btnStyleBody = {
		...bodyStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};
	const btnStyleHeader = {
		...headerStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};

	return (
		<div className="billing-adjustment-grid">
			<ThemeProvider theme={baseTheme}>
				<Paper sx={{ width: "100%", mb: 2, borderRadius: 2 }}>
					<Toolbar
						sx={[
							{
								justifyContent: "space-between",
								minHeight: "35px !important",
								borderTopLeftRadius: 2,
								borderTopRightRadius: 2,
							},
							{
								bgcolor: (theme) =>
									alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
							},
						]}
					>
						<div className="toolbar-columns go-left">
							{numSelected > 0 ? (
								<Typography
									style={bodyStyle}
									sx={{ flex: "1 1 100%" }}
									color="inherit"
									variant="subtitle1"
									component="div"
								>
									{numSelected} selected.
								</Typography>
							) : null}
							{data.length === 0 ? (
								<Typography
									style={bodyStyle}
									sx={{ flex: "1 1 100%" }}
									color="inherit"
									variant="subtitle1"
									component="div"
								>
									No records to display.
								</Typography>
							) : null}
						</div>
						<div className="toolbar-columns go-right">
							<Typography style={bodyStyle} color="inherit" variant="subtitle1" component="div">
								Total Applied: ${totalApplied.toFixed(2)}
							</Typography>
							{numSelected > 0 && (
								<Tooltip title="Delete">
									<IconButton onClick={clearSelections}>
										<DeleteIcon />
									</IconButton>
								</Tooltip>
							)}
						</div>
					</Toolbar>

					<TableContainer
						sx={{
							borderRadius: 2,
							fontSize: "6rem",
						}}
					>
						<Table size="small">
							<TableHead>
								<TableRow>
									<TableCell padding="checkbox">
										<Checkbox
											indeterminate={selected.length > 0 && selected.length < data.length}
											checked={data.length > 0 && selected.length === data.length}
											onChange={handleSelectAll}
										/>
									</TableCell>
									<TableCell size="small" style={btnStyleHeader}></TableCell>
									<TableCell size="medium" style={headerStyle}>
										Charge No
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Item
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Charge Qty
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Charge Unit
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Balance Due
									</TableCell>
									<TableCell
										size="medium"
										style={headerStyle}
										sx={{ width: "150px", textAlign: "center" }}
									>
										Adjustment Amount
									</TableCell>
								</TableRow>
							</TableHead>
							<TableBody>
								{data.slice(page * rowsPerPage, (page + 1) * rowsPerPage).map((row) => {
									const isSelected = selected.indexOf(row.id) !== -1;

									return (
										<TableRow key={row.id} selected={isSelected}>
											<TableCell padding="checkbox">
												<Checkbox checked={isSelected} onChange={() => handleSelect(row.id)} />
											</TableCell>
											<TableCell
												sx={{
													justifyContent: "center",
													textAlign: "center",
												}}
												style={btnStyleBody}
											>
												{row.id && (
													<Button
														onClick={() => openCharge(row.id)}
														variant="outlined"
														size="small"
													>
														View Charge
													</Button>
												)}
											</TableCell>
											<TableCell style={bodyStyle}>{row.charge_no}</TableCell>
											<TableCell style={bodyStyle}>{row.inventory_id_auto_name}</TableCell>
											<TableCell style={bodyStyle}>{row.charge_quantity}</TableCell>
											<TableCell style={bodyStyle}>{row.charge_unit}</TableCell>
											<TableCell style={bodyStyle}>
												{window.numeral(row.balance_due || 0).format("$0,0.00")}
											</TableCell>
											<TableCell sx={{ width: "150px" }}>
												{isSelected && (
													<TextField
														type="number"
														variant="standard"
														style={{ ...bodyStyle, width: "100%" }}
														value={adjustments[row.id] || ""}
														onChange={(e) => handleAdjustmentChange(row.id, e.target.value)}
														inputProps={{
															min: 0,
															max: row.balance_due,
															step: "0.01",
														}}
														size="small"
													/>
												)}
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					</TableContainer>
					<TablePagination
						rowsPerPageOptions={[25, 50, 100]}
						component="div"
						size="medium"
						count={data.length}
						rowsPerPage={rowsPerPage}
						page={page}
						onPageChange={(_, newPage) => setPage(newPage)}
						onRowsPerPageChange={(event) => {
							setRowsPerPage(parseInt(event.target.value, 10));
							setPage(0);
						}}
					/>
				</Paper>
			</ThemeProvider>
		</div>
	);
};
