import "./shared.less";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	TablePagination,
	Toolbar,
	alpha,
	Typography,
	ThemeProvider,
	Button,
} from "@mui/material";

import { useState } from "react";
import { baseTheme } from "@core/theme";
import { DataAR } from "@modules/billing/components/outstanding-ar";

export const OutstandingARReadOnly = (props: any) => {
	const [data, setData] = useState<DataAR[]>(props.data || []);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(25);

	const filteredData = data;

	const openInvoice = async (id: number) => {
		window.App.reactNav.goTo(`/billing/invoices/${id}/read`);
	};

	const headerStyle = {
		fontWeight: "bold",
		fontSize: "1.2rem",
	};
	const bodyStyle = {
		fontSize: "1.2rem",
	};
	const btnStyleBody = {
		...bodyStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};
	const btnStyleHeader = {
		...headerStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};

	return (
		<div className="billing-outstanding-ar">
			<ThemeProvider theme={baseTheme}>
				<Paper sx={{ width: "100%", mb: 2, borderRadius: 2 }}>
					<Toolbar
						sx={[
							{
								justifyContent: "space-between",
								minHeight: "35px !important",
								borderTopLeftRadius: 2,
								borderTopRightRadius: 2,
							},
							{
								bgcolor: (theme) =>
									alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
							},
						]}
					>
						<div className="toolbar-columns go-left">
							{data.length > 0 ? (
								<Typography
									style={bodyStyle}
									sx={{ flex: "1 1 100%" }}
									color="inherit"
									variant="subtitle1"
									component="div"
								>
									{data.length} record(s) to display.
								</Typography>
							) : null}
						</div>
						<div className="toolbar-columns go-right">
							<Typography style={bodyStyle} color="inherit" variant="subtitle1" component="div">
								Total Applied:{" "}
								{window
									.numeral(data.reduce((acc, curr) => acc + curr.amount || 0, 0))
									.format("$0,0.00")}
							</Typography>
						</div>
					</Toolbar>

					<TableContainer
						sx={{
							borderRadius: 2,
							fontSize: "6rem",
						}}
					>
						<Table size="small">
							<TableHead>
								<TableRow>
									<TableCell size="small" style={btnStyleHeader}></TableCell>
									<TableCell size="medium" style={headerStyle}>
										Patient Name
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Site Name
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Invoice No
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Item
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Bill Type
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Outstanding Balance
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Amount Paid
									</TableCell>
								</TableRow>
							</TableHead>
							<TableBody>
								{filteredData.slice(page * rowsPerPage, (page + 1) * rowsPerPage).map((row) => {
									return (
										<TableRow key={row.id}>
											<TableCell
												sx={{
													justifyContent: "center",
													textAlign: "center",
												}}
												style={btnStyleBody}
											>
												{row.invoice_id && (
													<Button
														onClick={() => openInvoice(row.invoice_id)}
														variant="outlined"
														size="small"
													>
														Open Invoice
													</Button>
												)}
											</TableCell>
											<TableCell style={bodyStyle}>{row.patient_name || "-"}</TableCell>
											<TableCell style={bodyStyle}>{row.site_name || "-"}</TableCell>
											<TableCell style={bodyStyle}>{row.invoice_no || "-"}</TableCell>
											<TableCell style={bodyStyle}>{row.inventory_name || "-"}</TableCell>
											<TableCell style={bodyStyle}>{row.bill_type || "-"}</TableCell>
											<TableCell style={bodyStyle}>
												{window.numeral(row.outstanding_balance || 0).format("$0,0.00")}
											</TableCell>
											<TableCell style={bodyStyle}>
												{window.numeral(row.amount || 0).format("$0,0.00")}
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					</TableContainer>
					<TablePagination
						rowsPerPageOptions={[25, 50, 100]}
						component="div"
						size="medium"
						count={filteredData.length}
						rowsPerPage={rowsPerPage}
						page={page}
						onPageChange={(_, newPage) => setPage(newPage)}
						onRowsPerPageChange={(event) => {
							setRowsPerPage(parseInt(event.target.value, 10));
							setPage(0);
						}}
					/>
				</Paper>
			</ThemeProvider>
		</div>
	);
};
