import React from 'react';
import { AgGridReact, CustomCellRendererProps } from 'ag-grid-react';
import './invoice-data-view.less';
import { useFormData } from '@hooks/index';
import { useNavigation } from '@core/navigation';
import { useForm } from 'antd/es/form/Form';

interface SummaryData {
  totalBilled: number;
  totalCost: number;
  totalExpected: number;
  totalPaid: number;
  totalProfit: number;
  margin: number;
  balanceDue: number;
  claimError?: string;
}

interface GridRow {
  charge: string;
  quantity: number;
  expected: number;
  billed: number;
  paid: number;
  pricingFormula: string;
  pricingSource: string;
}

const InvoiceDataView = (props: CustomCellRendererProps) => {
const {data} = props;
const [fd] = useFormData("billing_invoice", data.id)
const invoice = fd?.data;

  const summaryData: SummaryData = {
    totalBilled: 50150.25,
    totalCost: 32.5942,
    totalExpected: 41121.23,
    totalPaid: 0.00,
    totalProfit: 0.00,
    margin: 0.00,
    balanceDue: 50150.25,
    claimError: 'Claim is Post Date'
  };

  const rowData: GridRow[] = [
    {
      charge: 'Gammagard 10GM/10ML',
      quantity: 4,
      expected: 41121.43,
      billed: 50150.25,
      paid: 0.00,
      pricingFormula: 'AWP * 1.25',
      pricingSource: 'Contract'
    }
  ];

  function currencyFormatter(params: any) {
    return params.value ? `$${params.value.toFixed(2)}` : '$0.00';
  }

  function percentageFormatter(value: number) {
    return `${value.toFixed(2)}%`;
  }
//   return (<div>Hello</div>)

  return (
    <div className='invoice-data-container'>
      <div className="invoice-list-container">
        <div className="summary-section">

          <div className="summary-column">
            <div className="summary-item">
              <span className="label">Total Billed</span>
              <span className="value">${invoice.total_billed || 0 }</span>
            </div>
            <div className="summary-item">
              <span className="label">Total Cost</span>
              <span className="value">${invoice.total_cost || 0}</span>
            </div>
            <div className="summary-item">
              <span className="label">Total Expected</span>
              <span className="value">${invoice.total_expected || 0}</span>
            </div>
          </div>

          <div className="summary-column">
              <div className="summary-item">
                <span className="label">Total Billed</span>
                <span className="value">${invoice.total_billed || 0 }</span>
              </div>
              <div className="summary-item">
                <span className="label">Balance Due</span>
                <span className="value">${invoice.total_balance_due || 0}</span>
              </div>
          </div>
          
          <div className='summary-column'>
            <div className="summary-item">
              <span className="label">Total Paid</span>
              <span className="value">${invoice.total_paid || 0 }</span>
            </div>
            <div className="summary-item">
              <span className="label">Total Profit</span>
              <span className="value">${summaryData.totalProfit}</span>
            </div>
            <div className="summary-item">
                <span className="label">Margin</span>
                <span className="value">{percentageFormatter(summaryData.margin)}</span>
            </div>
          </div>

          <div className="summary-column">
            <div className="summary-item error">
              <span className="label">Claim Error</span>
              <span className="value">{summaryData.claimError}</span> 
            </div>
          </div>


        </div>
        <div className="table-container">
          <table className="invoice-table">
            <thead>
              <tr>
                <th>Charge</th>
                <th>Quantity</th>
                <th>Expected</th>
                <th>Billed</th>
                <th>Paid</th>
                <th>Pricing Formula</th>
                <th>Pricing Source</th>
              </tr>
            </thead>
            <tbody>
              {rowData.map((row, index) => (
                <tr key={index}>
                  <td><p>{row.charge}</p></td>
                  <td><p>{row.quantity}</p></td>
                  <td><p>${invoice.total_expected || 0}</p></td>
                  <td><p>${invoice.total_billed || 0}</p></td>
                  <td><p>${invoice.total_paid || 0}</p></td>
                  <td><p>{row.pricingFormula}</p></td>
                  <td><p>{row.pricingSource}</p></td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDataView;