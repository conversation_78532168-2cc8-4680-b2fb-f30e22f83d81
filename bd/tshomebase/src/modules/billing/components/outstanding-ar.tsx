import React, { useEffect } from "react";
import "./shared.less";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	TextField,
	TablePagination,
	Checkbox,
	Toolbar,
	alpha,
	Typography,
	IconButton,
	Tooltip,
	ThemeProvider,
	But<PERSON>,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";

import { useState } from "react";
import { baseTheme } from "@core/theme";
import { request } from "@core/request";
import { toast } from "react-toastify";
import currency from "currency.js";

export type DataAR = {
	id: number;
	tkey: string;
	invoice_no: string;
	charge_no: string;
	patient_id: number;
	payer_id: number;
	site_id: number;
	date_of_service: string;
	bill_type: string;
	charge_quantity: number;
	charge_unit: string;
	post_datetime: string | null;
	billed_datetime: string;
	amount_paid: number;
	outstanding_balance: number;
	ticket_no: string;
	rx_no: string;
	inventory_id: number;
	inventory_name: string;
	payer_name: string;
	mrn: string;
	patient_last_name: string;
	patient_first_name: string;
	patient_name: string;
	site_name: string;
	invoice_id: number;
	amount: number;
};

type ChargeLinePosting = {
	charge_no: string;
	amount: number;
	outstanding_balance: number;
};

export const OutstandingAR = (props: any) => {
	const [filters, setFilters] = useState({
		patient_id: "",
		site_id: "",
		payer_id: "",
		bill_type: "",
	});
	const { handle } = props;
	const [data, setData] = useState<DataAR[]>([]);
	const [dataIdMap, setDataIdMap] = useState<{ [key: number]: DataAR }>({});
	const [payments, setPayments] = useState<{ [key: number]: number }>({});
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(25);
	const [selected, setSelected] = useState<number[]>([]);
	const [totalApplied, setTotalApplied] = useState(0);

	const [filteredData, setFilteredData] = useState<DataAR[]>([]);

	useEffect(() => {
		const activeFilters = Object.entries(filters).filter(([_, value]) => value);

		if (activeFilters.length === 0) {
			setFilteredData([...data]);
			return;
		}

		setFilteredData([
			...data.filter((row) => {
				return activeFilters.every(([key, value]) => {
					switch (key) {
						case "patient_id":
							return row.patient_id == (value as any);
						case "site_id":
							return row.site_id == (value as any);
						case "payer_id":
							return row.payer_id == (value as any);
						case "bill_type":
							return row.bill_type === value;
						default:
							return true;
					}
				});
			}),
		]);
	}, [filters, data]);

	const applyFilter = (filterType: keyof typeof filters, value: string) => {
		if (value == filters[filterType]) {
			return;
		}
		setFilters((prev) => ({ ...prev, [filterType]: value }));
	};

	const updateDSLJSONField = async () => {
		if (!handle?.parent?.value_field) return;
		const dataToSave: ChargeLinePosting[] = [];
		selected.forEach((id) => {
			const row = dataIdMap[id];
			if (!row) {
				console.error("OutstandingAR: Row not found for id:", id);
			}
			dataToSave.push({
				...row,
				charge_no: row.charge_no,
				amount: currency(payments[id] || 0).value,
				outstanding_balance: row.outstanding_balance,
			});
		});
		const totalApplied = dataToSave.reduce((sum, item) => currency(sum).add(item.amount).value, 0);
		setTotalApplied(totalApplied);
		handle.parent.value_field("charge_line_postings", JSON.stringify(dataToSave), true, true);
	};

	const handlePaymentChange = React.useCallback(
		(id: number, value: string) => {
			const numValue = parseFloat(value);
			const row = data.find((item) => item.id === id);
			if (!row || isNaN(numValue)) {
				setPayments((prev) => ({ ...prev, [id]: 0 }));
				return;
			}

			if (numValue >= 0 && numValue <= row.outstanding_balance) {
				setPayments((prev) => ({ ...prev, [id]: numValue }));
			}
		},
		[data, dataIdMap]
	);

	const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.checked) {
			const newSelected = filteredData.map((row) => row.id);
			setSelected(newSelected);
			return;
		}
		setSelected([]);
	};

	const clearSelections = () => {
		setSelected([]);
		setPayments({});
	};
	const clearFilters = () => {
		setFilters({
			patient_id: "",
			site_id: "",
			payer_id: "",
			bill_type: "",
		});
	};

	const fetchData = () => {
		const p = request({
			url: "/query/outstanding_ar?limit=9999999&fields=list",
		});
		const result = toast.promise(
			p,
			{
				pending: "Please wait while we pull outstanding AR...",
				success: "Successfully pulled outstanding AR",
				error: "An Unexpected Error Occurred",
			},
			{
				autoClose: 2000,
				hideProgressBar: true,
				position: "bottom-center",
				theme: "dark",
			}
		);
		result.then((d) => {
			if (d.data && Array.isArray(d.data) && d.data.length > 0) {
				d.data = d.data.map((item) => ({
					...item,
					tkey: window.generateUUID(),
				})) as DataAR[];
				setData(d.data);
				setFilteredData(d.data);
				setDataIdMap(
					d.data.reduce((acc, item) => {
						acc[item.id] = item;
						return acc;
					}, {})
				);
			}
		});
		result.catch((e) => {
			console.error("OutstandingAR:", e);
		});
	};

	// Add handler for selecting individual rows
	const handleSelect = (id: number) => {
		const selectedIndex = selected.indexOf(id);
		let newSelected: number[] = [];

		if (selectedIndex === -1) {
			newSelected = [...selected, id];
			setPayments((prev) => {
				delete prev[id];
				return prev;
			});
		} else {
			newSelected = selected.filter((item) => item !== id);
			setPayments((prev) => {
				if (prev[id] !== undefined || prev[id] !== null) {
					return prev;
				} else {
					prev[id] = 0;
					return prev;
				}
			});
		}

		setSelected(newSelected);
	};

	const payInFull = (id: number) => {
		handlePaymentChange(id, dataIdMap[id].outstanding_balance.toString());
	};

	const openInvoice = async (id: number) => {
		window.App.reactNav.goTo(`/billing/invoices/${id}/read`);
	};

	useEffect(() => {
		fetchData();
	}, []);

	useEffect(() => {
		updateDSLJSONField();
	}, [selected, payments]);

	const AROptions = {
		clearSelections,
		applyFilter,
		clearFilters,
	};
	if (handle?.parent) {
		// update the coffee function ref after each render
		handle.parent.arOptions = AROptions;
	}

	const numSelected = selected.length;

	const headerStyle = {
		fontWeight: "bold",
		fontSize: "1.2rem",
	};
	const bodyStyle = {
		fontSize: "1.2rem",
	};
	const btnStyleBody = {
		...bodyStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};
	const btnStyleHeader = {
		...headerStyle,
		width: "100px",
		paddingLeft: "5px",
		paddingRight: "5px",
	};

	return (
		<div className="billing-outstanding-ar">
			<ThemeProvider theme={baseTheme}>
				<Paper sx={{ width: "100%", mb: 2, borderRadius: 2 }}>
					<Toolbar
						sx={[
							{
								justifyContent: "space-between",
								minHeight: "35px !important",
								borderTopLeftRadius: 2,
								borderTopRightRadius: 2,
							},
							{
								bgcolor: (theme) =>
									alpha(theme.palette.primary.main, theme.palette.action.activatedOpacity),
							},
						]}
					>
						<div className="toolbar-columns go-left">
							{numSelected > 0 ? (
								<Typography
									style={bodyStyle}
									sx={{ flex: "1 1 100%" }}
									color="inherit"
									variant="subtitle1"
									component="div"
								>
									{numSelected} selected.
								</Typography>
							) : null}
							{data.length === 0 ? (
								<Typography
									style={bodyStyle}
									sx={{ flex: "1 1 100%" }}
									color="inherit"
									variant="subtitle1"
									component="div"
								>
									No records to display.
								</Typography>
							) : null}
						</div>
						<div className="toolbar-columns go-right">
							<Typography style={bodyStyle} color="inherit" variant="subtitle1" component="div">
								Total Applied: ${totalApplied.toFixed(2)}
							</Typography>
							{numSelected > 0 && (
								<Tooltip title="Delete">
									<IconButton onClick={clearSelections}>
										<DeleteIcon />
									</IconButton>
								</Tooltip>
							)}
						</div>
					</Toolbar>

					<TableContainer
						sx={{
							borderRadius: 2,
							fontSize: "6rem",
						}}
					>
						<Table size="small">
							<TableHead>
								<TableRow>
									<TableCell padding="checkbox">
										<Checkbox
											indeterminate={selected.length > 0 && selected.length < filteredData.length}
											checked={filteredData.length > 0 && selected.length === filteredData.length}
											onChange={handleSelectAll}
										/>
									</TableCell>
									<TableCell size="small" style={btnStyleHeader}></TableCell>
									<TableCell size="medium" style={headerStyle}>
										Patient Name
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Site Name
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Invoice No
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Item
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Bill Type
									</TableCell>
									<TableCell size="medium" style={headerStyle}>
										Outstanding Balance
									</TableCell>
									<TableCell
										size="medium"
										style={headerStyle}
										sx={{ width: "150px", textAlign: "center" }}
									>
										Amount Paid
									</TableCell>
									<TableCell size="small" style={btnStyleHeader}></TableCell>
								</TableRow>
							</TableHead>
							<TableBody>
								{filteredData.slice(page * rowsPerPage, (page + 1) * rowsPerPage).map((row) => {
									const isSelected = selected.indexOf(row.id) !== -1;

									return (
										<TableRow key={row.tkey} selected={isSelected}>
											<TableCell padding="checkbox">
												<Checkbox checked={isSelected} onChange={() => handleSelect(row.id)} />
											</TableCell>
											<TableCell
												sx={{
													justifyContent: "center",
													textAlign: "center",
												}}
												style={btnStyleBody}
											>
												{row.invoice_id && (
													<Button
														onClick={() => openInvoice(row.invoice_id)}
														variant="outlined"
														size="small"
													>
														Open Invoice
													</Button>
												)}
											</TableCell>
											<TableCell style={bodyStyle}>{row.patient_name}</TableCell>
											<TableCell style={bodyStyle}>{row.site_name}</TableCell>
											<TableCell style={bodyStyle}>{row.invoice_no}</TableCell>
											<TableCell style={bodyStyle}>{row.inventory_name || "-"}</TableCell>
											<TableCell style={bodyStyle}>{row.bill_type}</TableCell>
											<TableCell style={bodyStyle}>
												{window.numeral(row.outstanding_balance || 0).format("$0,0.00")}
											</TableCell>
											<TableCell sx={{ width: "150px" }}>
												{isSelected && (
													<TextField
														type="number"
														variant="standard"
														style={{ ...bodyStyle, width: "100%" }}
														value={payments[row.id] || ""}
														onChange={(e) => handlePaymentChange(row.id, e.target.value)}
														inputProps={{
															min: 0,
															max: row.outstanding_balance,
															step: "0.01",
														}}
														size="small"
													/>
												)}
											</TableCell>
											<TableCell
												sx={{
													justifyContent: "center",
													textAlign: "center",
												}}
												style={btnStyleBody}
											>
												{isSelected && (
													<Button
														onClick={() => payInFull(row.id)}
														variant="outlined"
														size="small"
													>
														Pay In Full
													</Button>
												)}
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					</TableContainer>
					<TablePagination
						rowsPerPageOptions={[25, 50, 100]}
						component="div"
						size="medium"
						count={filteredData.length}
						rowsPerPage={rowsPerPage}
						page={page}
						onPageChange={(_, newPage) => setPage(newPage)}
						onRowsPerPageChange={(event) => {
							setRowsPerPage(parseInt(event.target.value, 10));
							setPage(0);
						}}
					/>
				</Paper>
			</ThemeProvider>
		</div>
	);
};
