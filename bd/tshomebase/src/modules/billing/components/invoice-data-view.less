.invoice-data-container{
  background-color: #fff;
  padding: 0 10px 10px 10px;
  gap: 10px;
  .invoice-list-container {
      display: flex;
      flex-direction: column;
      border-radius: 6px;
      padding: 12px 8px;
      gap: 16px;
      box-shadow: 0px 1px 3px 0px #38383814 inset;
      background-color:#FAFAFA;
    
      .summary-section {
          display: flex;
          gap: 8px;
  
    
        .summary-column {
          display: flex;
          flex-direction: column;
          gap: 4px;
    
          .summary-item {
              display: flex;
              justify-content: space-between;
              gap: 10px;
              height: 24px;
              padding-right: 10px;
    
            .label {
              font-family: "inter";
              font-weight: 400;
              color: #5E636B;
              font-size: 12px;
            }
    
            .value {
              font-family: "inter";
              font-weight: 700;
              color: #4D525C;
              font-size: 12px;
              text-align: right;
            }
    
            &.error {
              .value {
                color: #E58787;
              }
            }
          }
        }
      }
    
      // .grid-section {
      //   border: 1px solid #f0f0f0;
      //   border-radius: 4px;
        
      //   :global {
      //     .ag-header-cell {
      //       background-color: #f5f5f5;
      //       font-weight: 500;
      //     }
    
      //     .ag-row {
      //       &:hover {
      //         background-color: #fafafa;
      //       }
      //     }
    
      //     .ag-cell {
      //       padding: 8px 16px;
      //     }
      //   }
      // }
      .table-container {
          width: 100%;
          border-radius: 4px;
          overflow: hidden;
          margin-top: 20px;
          border: 1px solid #E0E0DC;
          border-radius: 8px;
        }
      
        .invoice-table {
          width: 100%;
          border-collapse: collapse;
          background-color: #ffffff;
          font-size: 14px;
      
          th, td {
            height: 36px;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
          }
      
          th {
            background: #E5E5E0;
            color: #80807D;
            font-weight: 500;
            font-size: 12px;
            line-height: 18px;
            white-space: nowrap;
      
            &:first-child {
              padding-left: 24px;
            }
      
            &:last-child {
              padding-right: 24px;
            }
          }
      
          td {
            color: #333333;
      
            &:first-child {
              padding-left: 24px;
            }
      
            &:last-child {
              padding-right: 24px;
            }
          }
      
          tbody {
            tr {
              height: 52px;
              &:hover {
                background-color: #fafafa;
              }
      
              &:last-child {
                td {
                  font-size: 14px;
                  font-weight: 700;
                  color: #5E636B;
                  line-height: 20px;
                  border-bottom: none;
                }
              }
            }
          }
        }
    }
}