.matrix-editor {
	display: flex;
	flex-direction: column;
	min-height: 100%;
	gap: 10px;
	padding-bottom: 10px;

	.dsl-grid-view {
		height: 100% !important;
	}

	.row-mark-danger-inv {
		background-color: #f4999b29 !important;
	}

	.matrix-assigned {
	}

	.matrix-unassigned:not(.tr-select) {
		background-color: fade(#f4999b, 30%) !important;
	}

	.matrix-editor-row {
		display: flex;
		gap: 10px;

		&:first-child {
			height: 60% !important;
			min-height: 60% !important;
			max-height: 60% !important;
			.no-item-cont {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				flex: 1;
			}

			.container-label {
				width: 100%;
				display: flex;
				min-height: 34px;
				flex-shrink: 0;
				justify-content: flex-start;
				align-items: center;
				background: #dfe1ef;
				color: #42437b;
				font-size: 12px;
				font-weight: 600;
				line-height: 14px;
				letter-spacing: 0em;
				text-align: left;
				padding-left: 18px;
				text-transform: uppercase;
			}
		}

		&:last-child {
			height: 40% !important;
			min-height: 40% !important;
			max-height: 40% !important;
		}

		.matrix-editor-item {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 100%;
			min-height: 100%;
			max-height: 100%;
			flex-direction: column;

			&.inventory-compact-list {
				.compact-btns-container {
					display: none;
				}

				.searchbox {
					display: flex;
					flex-direction: column;
					padding: 2px 0px 2px 0px;
					width: 100%;

					.search-input {
						border: none;
						height: 30px;
						width: 100%;
						padding: 0px 0px 0px 10px;
						font-weight: 400;
					}
				}
			}

			&.dsl-editor {
				.form-container {
					margin: 0px !important;
					min-width: 100%;
					min-height: calc(100% - var(--wizard-bottom-bar-height) + var(--form-container-g));

					.cardarea {
						> h3:first-child {
							display: none;
						}

						h4 {
							display: none;
						}
					}
				}

				.wizard-bottom-bar {
					min-height: fit-content;
					padding: 10px;
				}
			}
		}
	}
}
