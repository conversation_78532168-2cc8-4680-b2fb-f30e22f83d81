import React, { useState, useEffect } from "react";
import "./billing-dashboard.less";
import { AgCharts } from "ag-charts-react";
import { BillingData, useBillingData } from "./billing-data";
import { CRErrorBoundary } from "@blocks/error-boundary";
import { openListQueryPopover } from "@blocks/dsl-list-view/query-list-popover";

interface SkeletonProps {
	className?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({ className }) => {
	return <div className={`skeleton-pulse ${className || ""}`} />;
};

const formatCurrency = (value: string | null) => {
	if (!value) return "$0.00";
	return value;
};

const formatPercentage = (value: string | null) => {
	if (!value) return "0%";
	return value;
};

export const BillingDashboardReport = () => {
	const [billingDataResponse, refetchBillingData] = useBillingData();
	const loading = billingDataResponse.loading;
	const billingData = billingDataResponse.data || [];

	const findMetric = (key: string) => {
		return billingData.find((item) => item.measure_key === key);
	};

	const GrowthIndicator = ({
		value,
		direction,
	}: {
		value: number | null;
		direction: "positive" | "negative" | "neutral";
	}) => {
		if (value === null || value === 0 || direction === "neutral") return null;

		const isPositive = direction === "positive";
		const className = `growth-indicator ${isPositive ? "positive" : "negative"}`;
		const arrow = isPositive ? "↑" : "↓";

		return (
			<span className={className}>
				<span className="arrow">{arrow}</span> {Math.abs(value).toFixed(2)}%
			</span>
		);
	};

	const MetricCard = ({
		metric,
		formatter = (v: any) => v,
		isLoading = false,
		defaultTitle = "",
	}: {
		metric: BillingData | undefined;
		formatter?: (value: any) => string;
		isLoading?: boolean;
		defaultTitle?: string;
	}) => {
		if (!isLoading && !metric) return null;

		const title = metric?.name || defaultTitle;
		const value = metric?.amount;
		const growth = metric?.previous_amount_perc;
		const status = metric?.trend_direction || "neutral";

		return (
			<div
				className="metric-card"
				onClick={() => {
					const params = metric?.query_params?.join("&").replaceAll(" 00:00:00+00", "");
					if (metric?.details_view_query) {
						openListQueryPopover({
							title: title,
							query: { code: metric.details_view_query, parameters: params },
						});
					}
				}}
			>
				<div className="card-header">
					<div className="card-title">{title}</div>
					<div className="card-menu">⋮</div>
				</div>

				{isLoading ? (
					<Skeleton className="h-8 w-3/4" />
				) : (
					<div className={`card-value ${status}`}>
						{formatter(value)}
						{growth !== undefined && <GrowthIndicator value={growth} direction={status} />}
					</div>
				)}
			</div>
		);
	};
	const pharmacyClaim = findMetric("pending_pharmacy_claims");
	const medicalClaim = findMetric("pending_medical_claims");

	return (
		<CRErrorBoundary>
			<div className="billing-dashboard">
				<div className="dashboard-section">
					<div className="cards-grid">
						<MetricCard
							metric={findMetric("tot_revenue_fy")}
							formatter={formatCurrency}
							isLoading={loading}
							defaultTitle="Total Revenue FY"
						/>
						<MetricCard
							metric={findMetric("revenue_mtd")}
							formatter={formatCurrency}
							isLoading={loading}
							defaultTitle="Revenue MTD"
						/>
						<MetricCard
							metric={findMetric("pending_revenue")}
							formatter={formatCurrency}
							isLoading={loading}
							defaultTitle="Pending Revenue"
						/>
					</div>
				</div>

				<div className="dashboard-section">
					<div className="section-title">KPIs</div>
					<div className="cards-grid">
						<MetricCard
							metric={findMetric("denials_rate_qtd_final")}
							formatter={formatPercentage}
							isLoading={loading}
							defaultTitle="Denial Rate QTD"
						/>
						<MetricCard
							metric={findMetric("net_collections_qtd")}
							formatter={formatPercentage}
							isLoading={loading}
							defaultTitle="Net Collections Ratio QTD"
						/>
						<MetricCard
							metric={findMetric("net_collections_fy")}
							formatter={formatPercentage}
							isLoading={loading}
							defaultTitle="Net Collections Ratio FY"
						/>
						<MetricCard
							metric={findMetric("bad_debit_qtd")}
							formatter={formatPercentage}
							isLoading={loading}
							defaultTitle="Bad Debt QTD"
						/>
						<MetricCard
							metric={findMetric("denials_rate_qtd_reworked")}
							formatter={formatPercentage}
							isLoading={loading}
							defaultTitle="Reworked Claims Rate QTD"
						/>
						<MetricCard
							metric={findMetric("clean_claims_qtd")}
							formatter={formatPercentage}
							isLoading={loading}
							defaultTitle="Clean Claims QTD"
						/>
					</div>
				</div>

				<div className="dashboard-section">
					<div className="section-title">Pending Claims</div>
					<div className="charts-grid">
						{loading ? (
							<>
								<div className="chart-container">
									<Skeleton className="h-full w-full" />
								</div>
								<div className="chart-container">
									<Skeleton className="h-full w-full" />
								</div>
							</>
						) : (
							<>
								<div className="chart-container">
									{pharmacyClaim?.type === "pie_chart" && pharmacyClaim?.ag_chart_options && (
										<AgCharts
											options={{
												...pharmacyClaim?.ag_chart_options,
												background: {
													fill: 'transparent'
												},
												series: pharmacyClaim?.ag_chart_options?.series?.map((s) => ({
													...s,
													sectorLabel: {
													  ...(s.sectorLabel || {}),
													  color: "black", 
													  fontSize: 12
													},
												  })),
												listeners: {
													click: (event) => {
														openListQueryPopover({
															title: "Pharmacy Claims",
															query: {
																code: pharmacyClaim.details_view_query,
																parameters: pharmacyClaim?.query_params
																	?.join("&")
																	.replaceAll(" 00:00:00+00", ""),
															},
														});
													},
												},
											}}
										/>
									)}
								</div>
								<div className="chart-container">
									{medicalClaim?.type === "pie_chart" && medicalClaim?.ag_chart_options && (
										<AgCharts
											options={{
												...medicalClaim?.ag_chart_options,
												background: {
													fill: 'transparent'
												},
												listeners: {
													click: (event) => {
														openListQueryPopover({
															title: "Medical Claims",
															query: {
																code: medicalClaim.details_view_query,
																parameters: medicalClaim?.query_params
																	?.join("&")
																	.replaceAll(" 00:00:00+00", ""),
															},
														});
													},
												},
											}}
										/>
									)}
								</div>
							</>
						)}
					</div>
				</div>
			</div>
		</CRErrorBoundary>
	);
};
