import { IFormFiltersResponse } from "@hooks/form-data";
import { useQueryData } from "@hooks/query";
import { AgChartProps } from "ag-charts-react";

export type BillingData = {
	measure_key: string;
	name: string;
	amount: string | null;
	previous_amount_perc: number | null;
	trend_direction: "positive" | "negative" | "neutral";
	details_view_query: string;
	drill_down_params: Record<string, string | null>;
	query_params?: string[];
} & (
	| { type: "card"; data: null }
	| {
			type: "pie_chart";
			data: { color: string; value: number; selectorValue: string; description: string }[];
			ag_chart_options: AgChartProps["options"] | null;
	  }
);

const generateAgChartOptions = (billingData: BillingData) => {
	if (billingData.type !== "pie_chart") return null;
	const opts: AgChartProps["options"] = {
		data: billingData.data,
		series: [
			{
				type: "donut",
				angleKey: "value",
				calloutLabelKey: "description",
				sectorLabelKey: "value",
				innerRadiusRatio: 0.7,
				fills: billingData.data.map((d) => d.color),
				tooltip: {
					enabled: true,
					renderer: (params: any) => {
						const value = params.datum.value;
						const displayValue = typeof value === "number" ? `${(value * 100).toFixed(0)}%` : value;
						const title = params.datum.description + " " + displayValue;
						return title;
					},
				},
				sectorLabel: {
					formatter: (params: any) => {
						const value = params.datum.value;
						return typeof value === "number" ? `${(value * 100).toFixed(0)}%` : value;
					},
				},
			},
		],
		title: {
			text: billingData.name,
			fontSize: 16,
		},
		legend: {
			position: "right",
		},
	};
	return opts;
};

const generateParams = (billingData: BillingData) => {
	return Array.from({ length: 7 }, (_, i) => {
		if (i in billingData.drill_down_params) {
			if (billingData.drill_down_params[i] == null) {
				return `${i}=null`;
			}
			return `${i}=${billingData.drill_down_params[i]}`;
		}
		return null;
	}).filter(Boolean) as string[];
};

const addLabelsToData = (billingData: BillingData) => {
	if (billingData.type !== "pie_chart") return billingData.data;
	return billingData.data.map((d) => ({
		...d,
		value: d.value && d.value < 0 ? (d.value * 100).toFixed(2) : d.value,
	}));
};

export const useBillingData: () => [IFormFiltersResponse<BillingData>, () => void] = () => {
	const [r, refetch] = useQueryData<BillingData>("billing_dashboard", []);
	r.data = (r.data || []).map((item) => ({
		...item,
		query_params: generateParams(item),
		ag_chart_options: generateAgChartOptions(item),
		data: addLabelsToData(item) as any,
	}));
	return [r, refetch];
};
