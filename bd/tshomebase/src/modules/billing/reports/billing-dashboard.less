@import (reference) "../../../less/style/main.less";

.skeleton-pulse {
	display: inline-block;
	height: 1em;
	position: relative;
	overflow: hidden;
	background-color: #ededed;
	border-radius: 4px;

	&::after {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		transform: translateX(-100%);
		background: linear-gradient(90deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.2) 20%,
				rgba(255, 255, 255, 0.5) 60%,
				rgba(255, 255, 255, 0));
		animation: shimmer 2s infinite;
		content: "";
	}

	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
}

.billing-dashboard {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	gap: 16px;
	padding: 16px;
	flex: 1;
	gap: 10px;
	min-height: 160px;
	display: flex;
	position: relative;
	box-shadow: none !important;
	background-color: rgba(250, 250, 250, 0.5) !important;
	border-radius: 8px;
	overflow: auto;
	padding-bottom: 0px;
	border: 1px solid white;
	border-top-width: 0;

	>div.dashboard-section {
		flex-shrink: 0;
	}
	.dashboard-section {
		&:first-of-type {
			.cards-grid {
				margin: 10px 16px;
			}
		}
	
		&:nth-of-type(2) {
			.section-title{
				margin-inline: 16px;
			}
			.cards-grid {
				margin: 10px 16px;
				.metric-card {
					&:first-of-type {
						.card-value {
							color: #E58787;
						}
					}
					&:nth-of-type(4) {
						.card-value {
							color: #E58787; 
						}
					}
				}
			}
		}
	}

	.dashboard-section {
		.section-title {
			font-weight: 500;
			color: #43464D;
			font-size: 18px;
			line-height: 24px;
			margin-inline: 16px;
			padding-top: 24px;
		}

		.cards-grid {
			display: grid;
			grid-template-columns: repeat(1, 1fr);
			gap: 10px;

			@media (min-width: 768px) {
				grid-template-columns: repeat(3, 1fr);
			}
		}

		.charts-grid {
			display: grid;
			grid-template-columns: repeat(1, 1fr);
			gap: 32px;

			@media (min-width: 768px) {
				grid-template-columns: repeat(2, 1fr);
			}
		}
	}

	.metric-card {
		border: 1px solid #E9EAEB;
		border-radius: 12px;
		padding: 20px;
		background-color: white;
		box-shadow:
		0px 1px 1px 0px #8C8C8C1A,
		0px 3px 3px 0px #8C8C8C17,
		0px 6px 3px 0px #8C8C8C0D,
		0px 10px 4px 0px #8C8C8C03,
		0px 16px 4px 0px #8C8C8C00;
		cursor: pointer;

		&:hover {
			box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
			background-color: #f8f9fa;
			border-color: #d1d5db;
		}

		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 8px;

			.card-title {
				font-weight: 500;
				color: #535862;
				font-size: 14px;
				line-height: 20px;
			}

			.card-menu {
				color: #A4A7AE;
				cursor: pointer;
				font-size: 20px;
				font-weight: 700;
			}
		}

		.card-value {
			font-weight: 500;
			font-size: 30px;
			line-height: 32px;
			color: #43464D;
			display: flex;
			align-items: center;

			&.positive {
				color: #2e7d32;
			}

			&.negative {
				color: #d32f2f;
			}

			.growth-indicator {
				margin-left: 8px;
				font-size: 14px;
				font-weight: 500;
				display: flex;
				align-items: center;

				&.positive {
					color: #2e7d32;
				}

				&.negative {
					color: #d32f2f;
				}

				.arrow {
					margin-right: 2px;
				}
			}
		}
	}

	.chart-container {
		border-radius: 12px;
		padding: 16px;
		height: 350px;

		.chart-title {
			font-weight: 500;
			color: #5e636b;
			font-size: 16px;
			line-height: 22px;
			margin-bottom: 16px;
			text-align: center;
		}
	}
}