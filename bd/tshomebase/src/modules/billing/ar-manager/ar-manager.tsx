import React, { useEffect, useRef, useState } from "react";
import { AgGridReact, CustomCellRendererProps } from "ag-grid-react";
import "./ar-manager.less";
import "../../../blocks/dsl-advanced-grid/dsl-advanced-grid.less";
import currency from "currency.js";

import _ from "lodash";
import { ARManagerColumns, ChargeLineColumns, DEFAULT_CONFIG, TransactionColumns } from "./helper";
import { useQueryData } from "@hooks/query";
import { billingIcons } from "@public/icons/billing";
import list from "@public/icons/list";
import { CRErrorBoundary } from "@blocks/error-boundary";
import { useOnScreen } from "../../../hooks/on-screen";
import LoaderNoData from "@components/common/loader-no-data";
import { GetContextMenuItems, GridApi } from "ag-grid-enterprise";
import { usePersistentWindowsStore } from "@blocks/persistent-windows/store";
import { useShallow } from "zustand/react/shallow";
import { getAutoName } from "@utils/dsl-fx";
import { openListPopover } from "@blocks/dsl-list-view/list-popover";
import { openBillingStatusSelector } from "@components/popups/billing-status-selector";
import { fetchFormData, fetchFormFilters } from "@hooks/index";
import useToast from "@hooks/use-toast";
import { getRowStyle } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

const ARButtons = ({ icon, label, onClick }: { icon: string; label: string; onClick: () => void }) => {
	return (
		<div className="ar-button" onClick={onClick}>
			<img src={icon} alt={label} />
			{label}
		</div>
	);
};

interface ARManagerProps {
	patientId?: string;
}

type SelectedRows = {
	invoiceIds: string[];
	map: Record<string, any>;
};

export const ARManagerInternal = React.memo(({ patientId }: ARManagerProps) => {
	const addWindow = usePersistentWindowsStore(useShallow((state) => state.addWindow));
	const [showToast] = useToast();

	const [rowDataARManager, refreshARManager, resetARManager] = useQueryData("ar_manager_invoice_group", [], {
		limit: 999999999,
	});

	const [rowDataChargeLines, refreshChargeLines, resetChargeLines] = useQueryData(
		"ar_manager_charge_lines",
		[],
		{
			pool: true,
			limit: 999999999,
		},
		true
	);

	const arManagerRef = useRef<AgGridReact>(null);
	const [selectedRows, setSelectedRows] = useState<SelectedRows>({
		invoiceIds: [],
		map: {},
	});

	const [search, setSearch] = useState("");
	const [highlight, setHighlight] = useState(false);
	const [totals, setTotals] = useState({
		totalExpected: 0,
		totalBilled: 0,
		totalPaid: 0,
		totalCost: 0,
		totalAdjusted: 0,
		totalBalanceDue: 0,
		totalProfit: 0,
		margin: 0,
	});

	const refreshAll = async () => {
		await refreshARManager();
		if (selectedRows.invoiceIds.length == 1) {
			await refreshChargeLines();
		} else {
			resetChargeLines();
		}
	};

	const calculateTotals = (api: GridApi) => {
		const topLevelRows: any[] = [];
		api.forEachNodeAfterFilter((node: any) => {
			if (node.data && node.data.path && node.data.path.length > 1) {
				topLevelRows.push(node.data);
			}
		});

		const expectedSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_expected).value, 0)
		);
		const billedSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_billed).value, 0)
		);
		const paidSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_paid).value, 0)
		);
		const costSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_cost).value, 0)
		);
		const adjustedSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_adjusted).value, 0)
		);
		const balanceDueSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_balance_due).value, 0)
		);
		const profitSum = currency(
			topLevelRows.reduce((sum: number, row: any) => currency(sum).add(row.total_profit).value, 0)
		);
		// Store currency values properly
		const newTotals = {
			totalExpected: expectedSum.value,
			totalBilled: billedSum.value,
			totalPaid: paidSum.value,
			totalCost: costSum.value,
			totalAdjusted: adjustedSum.value,
			totalBalanceDue: balanceDueSum.value,
			totalProfit: profitSum.value,
			margin: 0,
		};

		if (newTotals.totalExpected > 0) {
			newTotals.margin = (newTotals.totalProfit / newTotals.totalExpected) * 100;
		}

		setTotals(newTotals);
	};

	const syncSelectedRows = (api: GridApi) => {
		const selectedNodes = api.getSelectedNodes();
		const newSelectedRows: SelectedRows = {
			invoiceIds: [],
			map: {},
		};

		selectedNodes.forEach((node) => {
			if (node.id === "ROOT_NODE_ID") {
				return;
			}
			if (!node.id) {
				return;
			}
			if (node.data) {
				if (node.data.invoice_id) {
					newSelectedRows.invoiceIds.push(node.data.invoice_id as any);
				}
				newSelectedRows.map[node.id as string] = node.data;
			}
		});
		newSelectedRows.invoiceIds = newSelectedRows.invoiceIds.filter(Boolean);

		if (!_.isEqual(selectedRows.invoiceIds.sort(), newSelectedRows.invoiceIds.sort())) {
			setSelectedRows(newSelectedRows);
		}
	};

	const onActionClick = async (
		source: "charge" | "transaction",
		action: "ar_transaction" | "zero" | "reverse",
		params: CustomCellRendererProps
	) => {
		const { data } = params;
		if (!data) {
			return;
		}
		if (source === "charge") {
			if (action === "ar_transaction") {
				const selectedInvoice = Object.values(selectedRows.map).find((row) => row.invoice_id);
				if (!selectedInvoice) {
					showToast(
						{
							type: "error",
							message: "Unable to find appropriate invoice for charge line in grid",
							autoClose: 1500,
						},
						true
					);
					return;
				}
				const patient = await fetchFormData("patient", selectedInvoice.patient_id, true, "list");
				if (!patient.success) {
					showToast({ type: "error", message: "Unable to locate patient", autoClose: 1500 }, true);
					return;
				}
				const chargeLines = await fetchFormFilters("ledger_charge_line", {
					filter: {
						charge_no: data.charge_no,
					},
				});
				if (!chargeLines.success || chargeLines.data.length === 0) {
					showToast({ type: "error", message: "Unable to locate charge line", autoClose: 1500 }, true);
					return;
				}
				const chargeLine = chargeLines.data[0];
				const patientData = patient.data;
				const presets = {
					payer_id: selectedInvoice.payer_id,
					patient_id: selectedInvoice.patient_id,
					first_name: patientData.firstname,
					last_name: patientData.lastname,
					mrn: patientData.mrn,
					site_id: patientData.site_id,
					dob: patientData.dob,
					ssn: patientData.ssn,
					charge_no: data.charge_no,
					invoice_no: data.invoice_no || selectedInvoice.invoice_no,
					charge_line_id: chargeLine.id,
					inventory_id: data.inventory_id,
					hcpc_code: data.hcpc_code,
					formatted_ndc: data.ndc,
					billed: data.billed,
					expected: data.expected,
					total_cost: data.total_cost,
					paid: data.paid,
					total_adjusted: data.total_adjusted,
					transaction_type: "Payment",
					amount: data.amount,
				};
				addWindow({
					type: "form",
					form: "billing_ar_transaction",
					preset: presets,
					card: "addfill",
					rid: "",
					wid: "",
					title: "AR Transaction",
					subtitle: "AR Manager",
					onEvent: (event) => {
						if (event.type === "saved") {
							refreshAll();
						}
					},
				});
			} else if (action === "zero") {
				alert("Pending: API call to zero " + data.charge_no);
			}
		} else if (source === "transaction") {
			if (action === "reverse") {
				alert("Pending: API call to reverse " + data.charge_no);
			}
		}
		refreshAll();
	};

	const onActionButtonClick = (action: "assign_claim" | "bulk_writeoff" | "bulk_posting" | "upload_posting") => {
		if (action === "bulk_posting") {
			alert("Pending: API call to bulk posting ");
			return;
		} else if (action === "upload_posting") {
			alert("Pending: API call to upload posting ");
			return;
		}
		if (selectedRows.invoiceIds.length === 0) {
			console.error("Unexpected Error: No invoices selected");
			return;
		}
		if (action === "assign_claim") {
			openBillingStatusSelector().then((status) => {
				if (!status) {
					return;
				}
				const blob = {
					status_id: status.id, // Its code from select field
					invoice_ids: selectedRows.invoiceIds,
				};
				alert("Pending: API call to assign claim " + JSON.stringify(blob));
			});
		} else if (action === "bulk_writeoff") {
			window.prettyConfirm(
				"Writeoff",
				"Do you want to writeoff all selected invoices?",
				"Writeoff",
				"Close",
				() => {
					alert("Pending: API call to bulk writeoff ");
				},
				() => {},
				window.BootstrapDialog.TYPE_DANGER
			);
		}
	};
	useEffect(() => {
		if (selectedRows.invoiceIds.length == 1) {
			refreshChargeLines([selectedRows.invoiceIds[0]]);
		} else {
			resetChargeLines();
		}
	}, [selectedRows]);

	const openForm = async (formName: string, formId: string, mode: "edit" | "read") => {
		if (!formName || !formId) {
			return;
		}
		if (!window.DSL[formName]) {
			console.error(`Form ${formName} not found`);
			return;
		}

		const autoName = await getAutoName(formName, formId);
		addWindow({
			type: "form",
			form: formName,
			title: autoName,
			wid: "",
			rid: "",
			card: mode,
			record: formId,
			gridRef: undefined,
			preset: {},
			subtitle: "AR Manager",
			onEvent: (event) => {
				if (event.type === "saved") {
					refreshAll();
				}
			},
		});
	};

	const getContextMenuItems: GetContextMenuItems = (params: any) => {
		const rowData = params.node?.data || {};
		const contextMenuItems = [];
		if (rowData.delivery_ticket_id) {
			contextMenuItems.push({
				name: "View Delivery Ticket",
				action: () => {
					openForm("careplan_delivery_tick", rowData.delivery_ticket_id, "read");
				},
			});
		}
		if (rowData.invoice_id) {
			contextMenuItems.push({
				name: "View Invoice",
				action: () => {
					openForm("billing_invoice", rowData.invoice_id, "read");
				},
			});
			if (rowData.patient_id) {
				const presetBillNote = {
					patient_id: rowData.patient_id,
					patient_form_id: rowData.patient_form_id,
					link_type: "Invoice",
					embed_invoice: rowData.invoice_id,
					invoice_ids: [rowData.invoice_id],
				};
				contextMenuItems.push({
					name: "Add Billing Note",
					action: () => {
						window.Flyout.open({
							form: "patient_bill_note",
							mode: "addfill",
							preset: presetBillNote,
						});
					},
				});

				contextMenuItems.push({
					name: "View Bill Notes",
					action: () => {
						openListPopover({
							listConfig: {
								canAdd: true,
								form: "patient_bill_note",
								filtersPresetFixed: {
									patient_id: rowData.patient_id,
									invoice_ids: [rowData.invoice_id],
								},
							},
							title: "Billing Notes",
							addConfig: {
								form: "patient_bill_note",
								preset: presetBillNote,
							},
						});
					},
				});
			}
		}
		if (rowData.patient_id) {
			contextMenuItems.push({
				name: "Open Patient Chart",
				action: () => {
					window.App.reactNav.goTo(`/patient/${rowData.patient_id}/snapshot`);
				},
			});
		}
		return contextMenuItems;
	};

	return (
		<CRErrorBoundary>
			<div className="ar-manager-container">
				<div className="ar-manager-header">
					<div className="ar-manager-header-left">
						<div className={`search-bar ${highlight ? "highlight" : ""}`}>
							<img src={list.magnifier} />
							<div>
								<input
									placeholder="Search All Invoices"
									value={search}
									onKeyDown={(e) => {
										if (e.key === "Enter") {
										}
									}}
									onFocus={(e) => setHighlight(true)}
									onBlur={() => setHighlight(false)}
									onChange={(e) => {
										setSearch(e.target.value);
										arManagerRef.current?.api.setGridOption("quickFilterText", e.target.value);
									}}
								/>
							</div>
						</div>
						<div className="ar-manager-header-left-subtitle">
							Select multiple invoices to perform batch operations
						</div>
					</div>
					<div className="ar-buttons">
						{selectedRows.invoiceIds.length > 1 ? (
							<>
								<ARButtons
									icon={billingIcons.flag}
									label="Assign Claim"
									onClick={() => {
										onActionButtonClick("assign_claim");
									}}
								/>
								<ARButtons
									icon={billingIcons.eraser}
									label="Bulk Writeoff"
									onClick={() => {
										onActionButtonClick("bulk_writeoff");
									}}
								/>
							</>
						) : null}
						<ARButtons
							icon={billingIcons.table}
							label="Bulk Posting"
							onClick={() => {
								onActionButtonClick("bulk_posting");
							}}
						/>
						<ARButtons
							icon={billingIcons.upload}
							label="Upload Posting"
							onClick={() => {
								onActionButtonClick("upload_posting");
							}}
						/>
						<img
							src={billingIcons.download}
							onClick={() => {
								if (arManagerRef.current) {
									arManagerRef.current.api.exportDataAsExcel();
								}
							}}
						></img>
						<img
							src={billingIcons.print}
							onClick={() => {
								if (arManagerRef.current) {
									console.log("Export PDF Pending");
								}
							}}
						></img>
						<div className="refresh-all" onClick={() => refreshAll()}>
							↻
						</div>
					</div>
				</div>
				<div className="dsl-advanced-grid">
					<AgGridReact
						rowSelection={{
							mode: "multiRow",
							groupSelects: "descendants",
							enableClickSelection: true,
						}}
						getRowId={(params) => {
							return params.data.path.toString();
						}}
						loading={rowDataARManager.loading}
						loadingOverlayComponent={LoaderNoData}
						loadingOverlayComponentParams={{
							loading: rowDataARManager.loading,
							containerStyle: {
								paddingTop: "40px",
							},
						}}
						getContextMenuItems={getContextMenuItems}
						onRowDoubleClicked={async (event) => {
							openForm(event.data?.form_name, event.data?.form_id, "edit");
						}}
						onModelUpdated={(event) => {
							calculateTotals(event.api);
						}}
						onRowDataUpdated={(event) => {
							syncSelectedRows(event.api);
						}}
						ref={arManagerRef}
						rowClassRules={{
							"even-background": (params) => params.rowIndex % 2 === 1,
							"odd-background": (params) => params.rowIndex % 2 === 0,
						}}
						onRowSelected={(params) => {
							syncSelectedRows(params.api);
						}}
						getRowStyle={getRowStyle}
						getDataPath={(data) => {
							return data?.path || [];
						}}
						rowData={rowDataARManager.data}
						treeData={true}
						groupDefaultExpanded={-1}
						rowHeight={40}
						rowModelType="clientSide"
						columnDefs={ARManagerColumns}
						sideBar={{
							toolPanels: ["columns", "filters"],
						}}
						multiSortKey={"ctrl"}
						defaultColDef={DEFAULT_CONFIG}
						suppressHorizontalScroll={false}
						enableCellTextSelection={true}
						autoGroupColumnDef={{
							headerName: "Invoice No",
							field: "invoice_no",
							rowGroup: true,
							sortable: true,
							filter: "agTextColumnFilter",
							flex: 1,
							minWidth: 100,
							cellRenderer: "agGroupCellRenderer",
							cellRendererParams: {
								suppressCount: true,
								suppressDoubleClickExpand: true,
							},
						}}
						groupDisplayType="groupRows"
						groupHideParentOfSingleChild={true}
					/>
					<div className="ar-manager-sticky-row">
						<div className="totals-label">
							Total Expected: <span>{currency(totals.totalExpected).format()}</span>
						</div>
						<div className="totals-label">
							Total Billed: <span>{currency(totals.totalBilled).format()}</span>
						</div>
						<div className="totals-label">
							Total Paid: <span>{currency(totals.totalPaid).format()}</span>
						</div>
						<div className="totals-label">
							Total Cost: <span>{currency(totals.totalCost).format()}</span>
						</div>
						<div className="totals-label">
							Total Adjusted: <span>{currency(totals.totalAdjusted).format()}</span>
						</div>
						<div className="totals-label">
							Total Balance Due: <span>{currency(totals.totalBalanceDue).format()}</span>
						</div>
						<div className="totals-label">
							Total Profit: <span>{currency(totals.totalProfit).format()}</span>
						</div>
						<div className="totals-label">
							Margin: <span>{totals.margin.toFixed(2)}%</span>
						</div>
					</div>
				</div>
				{selectedRows.invoiceIds.length == 1 ? (
					<div className="ar-manager-sub-grids">
						<div className="dsl-advanced-grid">
							<AgGridReact
								rowData={rowDataChargeLines.data}
								loading={rowDataChargeLines.loading}
								loadingOverlayComponent={LoaderNoData}
								loadingOverlayComponentParams={{
									loading: rowDataChargeLines.loading,
									containerStyle: {
										paddingTop: "40px",
									},
								}}
								getRowId={(params) => {
									return params.data.charge_no + "_" + params.data.invoice_no;
								}}
								columnDefs={[
									{
										headerName: "",
										field: "actions",
										sortable: false,
										filter: false,
										width: 70,
										flex: 0,
										cellRenderer: (params: any) => {
											const availableActions = params?.data?.available_actions || [];

											return (
												<div className="actions-cell">
													{availableActions.includes("ar_transaction") && (
														<img
															src={billingIcons.addPrimary}
															onClick={() =>
																onActionClick("charge", "ar_transaction", params)
															}
														/>
													)}
													{availableActions.includes("zero") && (
														<img
															src={billingIcons.zeroDollar}
															onClick={() => onActionClick("charge", "zero", params)}
														/>
													)}
												</div>
											);
										},
									},
									...ChargeLineColumns,
								]}
								rowHeight={44}
								defaultColDef={DEFAULT_CONFIG}
								rowModelType="clientSide"
							/>
							<div className="ar-manager-sticky-row">
								<div className="bottom-label">Charge Lines</div>
							</div>
						</div>
					</div>
				) : null}
			</div>
		</CRErrorBoundary>
	);
});

export const ARManager = React.memo((props: ARManagerProps) => {
	const [isOnScreen, elementRef] = useOnScreen(true);
	return (
		<div className="ar-manager-on-screen" ref={elementRef}>
			{isOnScreen ? <ARManagerInternal {...props} /> : null}
		</div>
	);
});
