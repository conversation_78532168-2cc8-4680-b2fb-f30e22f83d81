@import (reference) "../../../less/style/main.less";

.ag-right-aligned-header {
	text-align: right !important;
}
.ar-manager-on-screen {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
}
.ar-manager-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	border-width: 0px, 1px, 1px, 1px;
	border-style: solid;
	border-color: #ffffff;
	background: #fafafabf;
	padding: 8px;
	border-radius: 8px;
	gap: 8px;
	flex-shrink: 0px;
	overflow-y: auto;

	.refresh-all {
		content: "\f2f9" !important;
		.font-icon;
	}
	.actions-cell {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		gap: 8px;
		height: 100%;
		img {
			cursor: pointer;
		}
	}
	.ag-root-wrapper {
		border-bottom-left-radius: 0px !important;
		border-bottom-right-radius: 0px !important;
		border-bottom: 0px !important;
	}
	.ar-manager-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		min-height: fit-content;
		padding-right: 8px;
		.ar-manager-header-left {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
			flex: 1;
			gap: 18px;
			.search-bar {
				display: flex;
				gap: 8px;
				flex: 1;
				align-items: center;
				height: 40px !important;
				width: 358px;
				max-width: 358px;
				font-weight: 400px !important;
				background-color: #ffffff !important;

				.search-box-one;
				border: 1px solid #e5e5e5;
				box-shadow: 0px 1px 2px -1px #0000000a;
				box-shadow: 0px 0px 1px 0px #0000000f;

				> div {
					flex: 1;

					input {
						width: 100%;
						background-color: #ffffff !important;
						.search-box-one;
						border: none;
						padding-left: 0px;
						&::placeholder {
							color: var(--color-text);
						}

						&:focus {
							border: none;
						}
					}
				}

				img {
					width: 20px;
					height: 18px;
					cursor: pointer;
				}
				&.highlight {
					.form-input-field-focus;
				}
			}
			.ar-manager-header-left-subtitle {
				font-weight: 400;
				font-style: italic;
				font-size: 14px;
				line-height: 20px;
				letter-spacing: 0;
				color: #9b9fa8;
			}
		}
		.ar-buttons {
			display: flex;
			flex-direction: row;
			justify-content: flex-end;
			align-items: center;
			gap: 10px;
			img {
				cursor: pointer;
			}
			.ar-button {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				height: 36;
				border-radius: 8px;
				background: #fafafa;
				box-shadow: 0px -1px 2px 0px #00000038 inset;
				font-weight: 500;
				font-size: 14px;
				line-height: 20px;
				letter-spacing: 0;
				color: #5e636b;

				gap: 4px;
				padding-top: 8px;
				padding-right: 12px;
				padding-bottom: 8px;
				padding-left: 12px;
				border-radius: 8px;
				cursor: pointer;
			}
		}
	}
	.ar-manager-sub-grids {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		gap: 16px;
		width: 100%;
		height: 413px;
		gap: 12px;
		width: 100%;
		.ar-manager-sub-grid {
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;
			height: 100%;
			&.charge-lines {
				width: 60%;
			}
			&.transactions {
				width: 40%;
			}
		}
	}
}

.ar-manager-sticky-row {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	align-items: center;
	padding: 12px;
	background-color: #f9f9f9;
	font-size: 0.85rem;
	gap: 16px;
	border-radius: 8px;
	border: 1px solid #e5e5e5;
	background-color: #fff;
	border-top: 0px !important;
	border-top-left-radius: 0px !important;
	border-top-right-radius: 0px !important;
	.bottom-label {
		font-weight: 700;
		font-size: 14px;
		line-height: 19.5px;
		letter-spacing: 0;
		vertical-align: middle;
		color: #5e636b;
	}

	.totals-label {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		gap: 4px;
		font-weight: 400;
		font-size: 13px;
		line-height: 19.5px;
		letter-spacing: 0;
		vertical-align: middle;
		color: #5e636b;

		span {
			font-weight: 600;
			padding: 2px 4px;
			font-weight: 700;
			font-size: 14px;
			line-height: 20px;
			letter-spacing: 0%;
			vertical-align: middle;
			color: #5e636b;
		}
	}
}

.dsl-advanced-grid {
	display: flex;
	flex-direction: column;
	height: 100%;

	.ag-root-wrapper {
		flex: 1;
	}
}

.totals-label span {
	display: inline-block;
	padding: 2px 4px;
	border-radius: 2px;

	&:hover {
		background-color: rgba(0, 100, 255, 0.1);
		box-shadow: 0 0 0 1px rgba(0, 100, 255, 0.5);
	}
}
