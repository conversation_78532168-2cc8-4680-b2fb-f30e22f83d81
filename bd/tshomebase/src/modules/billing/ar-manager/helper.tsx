import { CRResponse, request } from "@core/request";
import { DynamicStatusCell } from "@components/dynamic-cell-status/dynamic-status-cell";
import { ColDef } from "ag-grid-enterprise";
import currency from "currency.js";
import { getCellStyle } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
export const getARManagerGroupData = async () => {
	const response = (await request({
		url: "/api/query/ar_manager_invoice_group",
	})) as CRResponse;
	return response;
};

const ClaimStatusCellRenderer = (params: any) => {
	return (
		<DynamicStatusCell
			value={params.value}
			statusMap={{
				Approved: "green",
				Benefit: "green",
				Payable: "green",
				Margin: "red",
				Captured: "grey",
				Rejected: "red",
				Reversed: "grey",
				"Reversal Rejected": "red",
				"Rebill Rejected": "red",
				"PA Deferred": "red",
				Duplicate: "red",
			}}
		/>
	);
};
const TransactionTypeCellRenderer = (params: any) => {
	return (
		<DynamicStatusCell
			value={params.value}
			statusMap={{
				Adjustment: "red",
				Writeoff: "red",
				Payment: "green",
				"Cash Allocation": "grey",
			}}
		/>
	);
};

export const ARManagerColumns: ColDef[] = [
	{
		headerName: "Date Billed",
		field: "post_datetime",
		sortable: true,
		filter: "agDateColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? new Date(params.value).toLocaleDateString() : "";
		},
	},
	{
		headerName: "Patient",
		field: "patient_id_auto_name",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 120,
	},
	{
		headerName: "Payer",
		field: "payer_id_auto_name",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 150,
	},
	{
		headerName: "Item",
		field: "inventory_id_auto_name",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 150,
	},
	{
		headerName: "Expected",
		field: "total_expected",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Cost",
		field: "total_cost",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 80,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Paid",
		field: "total_paid",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 80,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Balance",
		field: "total_balance_due",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Profit",
		field: "total_profit",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 80,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Profit %",
		field: "total_margin",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 80,
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Claim Status",
		field: "claim_status",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 100,
		cellStyle: (params) => {
			return {
				display: "flex",
				justifyContent: "center",
				alignItems: "center",
			};
		},
		cellRenderer: ClaimStatusCellRenderer,
	},
	{
		headerName: "Aging Days",
		field: "ar_aging_days",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		cellStyle: { textAlign: "right" },
		initialHide: true,
	},
	{
		headerName: "Aging Bucket",
		field: "ar_aging_bucket",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 100,
		initialHide: true,
	},
	{
		headerName: "DOS",
		field: "dos_start",
		sortable: true,
		filter: "agDateColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? new Date(params.value).toLocaleDateString() : "";
		},
		initialHide: true,
	},
	{
		headerName: "Site",
		field: "site_id_auto_name",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 150,
		initialHide: true,
	},
];

export const ChargeLineColumns: ColDef[] = [
	{
		headerName: "Item",
		field: "inventory_id_auto_name",
		sortable: true,
		filter: "agTextColumnFilter",
		flex: 1,
		minWidth: 100,
	},
	{
		headerName: "Expected",
		field: "expected",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Cost",
		field: "total_cost",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
	},
	{
		headerName: "Paid",
		field: "total_paid",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "Balance",
		field: "total_balance",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
	{
		headerName: "NDC",
		field: "ndc",
		sortable: true,
		filter: "agTextColumnFilter",
		flex: 1,
		minWidth: 100,
	},
];

export const TransactionColumns: ColDef[] = [
	{
		headerName: "Date/Time",
		field: "transaction_date_time",
		sortable: true,
		filter: "agDateColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? new Date(params.value).toLocaleDateString() : "";
		},
	},
	{
		headerName: "Type",
		field: "transaction_type",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 100,
		cellRenderer: TransactionTypeCellRenderer,
	},
	{
		headerName: "Account",
		field: "account_type",
		sortable: true,
		filter: "agMultiColumnFilter",
		flex: 1,
		minWidth: 100,
	},
	{
		headerName: "Amount",
		field: "amount",
		sortable: true,
		filter: "agNumberColumnFilter",
		flex: 1,
		minWidth: 100,
		valueFormatter: (params) => {
			return params.value ? currency(params.value).format() : "$0.00";
		},
		cellStyle: { textAlign: "right" },
	},
];

export const DEFAULT_CONFIG: ColDef = {
	filter: true,
	flex: 1,
	minWidth: 100,
	resizable: true,
	floatingFilter: false,
	enableCellChangeFlash: true,
	sortable: true,
	suppressHeaderMenuButton: true,
	cellClass: (params) => {
		const isSorted = params.column.getSort();
		const baseClass = "default-style";
		return isSorted === "asc" || isSorted === "desc" ? `${baseClass} sorted-column` : "unsorted-columns";
	},
	cellStyle: getCellStyle,
	headerClass: () => {
		return "header-style";
	},
};
