@import (reference) "../../less/style/main.less";

.module-schedule {
  display: flex;
  height: 100%;
  flex-direction: column;
  .gap-5;
}

.schedule-bar {
  vertical-align: bottom;
  justify-content: flex-start;
  height: 65px;
  padding-left: 10px;
  padding-right: 10px;
  flex-grow: 0;
  flex-shrink: 0;
  gap: 40px;
  background-color: @transparent;
}

.full-calender-wrapper {
  display: flex;
  width: 100%;
  overflow: hidden;
}

.fc {
  width: 100%;
  margin-top: 1px;
  padding: 0 10px;
  margin: 10px;
}

.fc-daygrid-body {
  width: 100%;
  height: 100%;

  .fc-scrollgrid-sync-table {
    height: 100%;
  }
}

.fc-header-toolbar {
  display: flex;
  align-items: center;
}

.fc-toolbar-chunk {
  display: flex;
  align-items: center;
}

.fc-toolbar-title {
  margin: 0;
}

.fc-button-active {
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-decoration-color: @purple;
  text-underline-offset: 5px;
}

.fc-button-primary {
  border-radius: 0px !important;
  border: 0px !important;
  text-shadow: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.fc-button:not(:last-child) {
  opacity: 1;
}

.fc .fc-button:disabled {
  opacity: 1;
  .fc-button-active;
}

.fc .fc-button:disabled {
  opacity: 1;
}

.fc-col-header {
  width: 100% !important;
}

#schedule {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: scroll;
  background: #fff;
  display: flex;
  flex-direction: column;
  .card-eight;

  .full-calender-wrapper {
    height: 100%;

    .fc-scrollgrid {
      border: 1px solid @soft-gray;
      border-radius: @border-radius-12;

      th,
      td {
        .para-two;
      }

      th {
        a {
          color: @charcoal-gray;
        }
      }

      td {
        cursor: pointer;

        a,
        .fc-event-time,
        .fc-event-title {
          color: @black;
        }
      }

      .fc-day,
      .fc-timegrid-slot-lane {
        &:hover {
          background-color: @semi-transparent-light-gray-25-five;
        }
      }

      .fc-scrollgrid-section-header {
        background-color: @semi-transparent-light-gray-08;
      }

      .fc-day-today {
        background-color: @purple-08;
      }
    }

    .fc-toolbar.fc-header-toolbar {
      margin: 0;
    }

    .fc-theme-standard {
      .fc-header-toolbar {
        position: absolute;
        top: 0px;
        width: 63%;
        height: 65px;
        justify-content: flex-start;

        .fc-toolbar-chunk {
          .fc-toolbar-title {
            .heading-two;
            color: @black;
          }

          &:first-child {
            flex: 1;
          }

          &:nth-child(2),
          &:nth-child(3) {
            .fc-button-group {
              .fc-button {
                background-color: transparent;
                text-transform: capitalize;
                color: @black;
                .para-two;
                font-weight: 600;
                height: 44px;

                &:hover {
                  .fc-button-active;
                }
              }
            }
          }

          &:nth-child(2) {
            .card-one;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            height: 44px;

            .fc-button-group {
              display: flex;
              .centered-row;
              padding-left: 50px;

              &:first-child {
                >button:first-child {
                  width: 30px !important;
                  padding: 0;
                }

                >button:last-child {
                  width: 30px !important;
                  padding: 0;
                }
              }
            }
          }

          &:nth-child(3) {
            .card-one;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;

            .fc-button-group {
              padding-right: 50px;
            }

            .fc-button {
              padding: 0px 20px;
            }
          }
        }
      }
    }
  }
}

.schedule-wrapper {
  display: flex;
  flex-direction: column;

  .find-view {
    height: auto !important;
    min-height: auto !important;
    justify-content: center !important;
    .card-seven !important;

    .dsl-find-view {
      width: 100%;
      padding: @padding-10;

      .findbar {
        width: 100%;
      }

      .find-action-btn {
        display: none !important;
      }
    }

    .findbasic {
      display: flex;
      justify-content: space-between;

      gap: 10px;

      label {
        display: flex;
        flex-direction: column;
        flex: 1;

        .control-label {
          width: 100%;
        }

        .select2-container {
          width: 95% !important;
          padding: 0;
        }
      }
    }

    .findadvanced {
      display: none;
    }

    .find-action-btn {
      .action {
        display: none !important;
      }
    }
  }
}

.fc-h-event {
  background-color: #f3f0f8 !important;
  display: block;
  border: 1px solid #9975cf;
}

.fc-daygrid-event-dot {
  border: 4px solid #9975cf;
  display: none;
}