/* eslint-disable no-mixed-spaces-and-tabs */
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useNavigation } from "@core/navigation";
import type {
	EventContentArg,
	EventClickArg,
	DateSelectArg,
	EventSourceInput,
} from "@fullcalendar/core";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import "./schedule.less";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import DSLFindView from "@blocks/dsl-find-view/dsl-find-view";
import { useToggle } from "usehooks-ts";
import { getSchedule, transformData, updateSchedule } from "./schedule-utils";
import { SchedulePopover } from "./schedule-popover/schedule-popover";
import SpinLoader from "@components/common/spin-loader";

interface ScheduleState {
	weekendsVisible: boolean;
	currentEvents: EventSourceInput[] | unknown
}

export const Schedule = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/schedule");
	useEffect(() => {
		if (props.navToURL?.startsWith("/schedule")) {
			props.setActiveTab?.("schedule");
		}
	}, [nav.globalNav.lrct]);


	return (
		<ScheduleViewWrapper />
	);
};

const ScheduleViewWrapper = (props) => {
	const [state, setState] = useState<ScheduleState>({
		weekendsVisible: true,
		currentEvents: [],
	});
	const [toggleFilter] = useToggle(false);
	const [setFilterApplied] = useState(false);
	const calendarRef = useRef<FullCalendar>(null);
	const [popoverOpen, setPopoverOpen] = useState(false);
	const [event, setEvent] = useState<EventClickArg | null>(null);
	const formatScheduleTime = "hh:mm A";
	const defScheduleDuration = 1;
	const [isLoading, setIsLoading] = useState(false);

	const nav = useNavigation(props, "/schedule");
	useEffect(() => {
		// if (props.navToURL?.startsWith("/schedule")) {
		// 	props.setActiveTab?.("schedule");
		// 	void fetchSchedule();
		// }
		void fetchSchedule();
	}, [nav.globalNav.lrct]);

	const fetchSchedule = useCallback(async () => {
		setIsLoading(true);
		try {
			const calendarApi = calendarRef.current?.getApi();
			const data = await getSchedule(
				"schedule_event",
				calendarApi?.getCurrentData()?.dateProfile.activeRange.start,
				calendarApi?.getCurrentData().dateProfile.activeRange.end
			);
			if (data) {
				const transformedEvents = data.map(transformData);
				setState((prev) => ({ ...prev, currentEvents: transformedEvents }));
			}

		} catch (error) {
			console.error("Error fetching events:", error);
		} finally {
			setIsLoading(false)
		}
	}, [calendarRef]);

	useEffect(() => {
		const calendarApi = calendarRef.current?.getApi();
		const datesSetHandler = () => {
			void fetchSchedule();
		};
		if (calendarApi) {
			calendarApi.on("datesSet", datesSetHandler);
		}
		return () => {
			if (calendarApi) {
				calendarApi.off("datesSet", datesSetHandler);
			}
		};
	}, [fetchSchedule]);


	const handleDateSelect = (info: DateSelectArg) => {
		const { start, end } = info;
		const date = new Date(start.toISOString().slice(0, -1));
		const dfd = window.Flyout.open({
			form: "schedule_event",
			preset: {
				event_start_date: date.toLocaleDateString("en-us"),
				event_start_time: date.toLocaleTimeString("en-us"),
				event_end_date: end.toLocaleDateString("en-us"),
				event_end_time: window.moment(date).add(defScheduleDuration, "hours").format(formatScheduleTime)
			},
			autoRecoverEnabled: false,
		});
		dfd.done(() => {
			void fetchSchedule();
		});
	};

	const renderEventContent = (eventContent: EventContentArg) => {
		console.log(eventContent);
		return (
			<>
				<b>{eventContent.event._def.extendedProps.patient_name}</b>
				<p><i>{eventContent.event.title}</i> <b>{" " + eventContent.event._def.extendedProps.appointment_status}</b></p>
			</>
		);
	}

	const openSchedule = (clickInfo: EventClickArg) => {
		const { extendedProps } = clickInfo.event._def;
		if (extendedProps.form_data.event_series_id) {
			setPopoverOpen(true);
			setEvent(clickInfo);
			return;
		} else {
			const dfd = window.Flyout.open({
				form: "schedule_event",
				record: clickInfo.event._def.publicId,
				autoRecoverEnabled: false,
			});
			dfd.done(() => {
				void fetchSchedule();
			});
		}


	};

	const handleOnFind = async (event: unknown) => {
		try {
			if (event.filter) {
				// const filterApply = createFilterUrl(event.filter)
				const calendarApi = calendarRef.current.getApi();
				const data = await getSchedule("schedule_event", calendarApi.getCurrentData().dateProfile.activeRange.start, calendarApi.getCurrentData().dateProfile.activeRange.end, event.filter);
				if (data) {
					const transformedEvents = data.map(transformData);
					setState((prev) => ({ ...prev, currentEvents: transformedEvents }));
				}
			}
		} catch (err) {
			console.error(err);
		}

	};

	const onEventDrop = (data: EventClickArg) => {
		const { id } = data.event._def.extendedProps.form_data;
		const putdata = {
			...data.event._def.extendedProps.form_data,
			override_start_date: moment(data.event.start).format("MM/DD/YYYY"),
			override_start_time: moment(data.event.start).utc().format(formatScheduleTime),
			override_end_date: (data.event.end) ? moment(data.event.end).format("MM/DD/YYYY") : null,
			ovverride_end_time: (data.event.end) ? moment(data.event.end).utc().format(formatScheduleTime) : null,
			effective_start_date: moment(data.event.start).format("MM/DD/YYYY"),
			effective_start_time: moment(data.event.start).utc().format(formatScheduleTime),
			effective_end_date: (data.event.end) ? moment(data.event.end).format("MM/DD/YYYY") : null,
			effective_end_time: (data.event.end) ? moment(data.event.end).utc().format(formatScheduleTime) : null,
		};
		updateSchedule(id, putdata, "schedule_event").then((data) => { }).catch((err) => console.error(err));

	};

	const onOpen = (event: EventClickArg, type: string) => {
		console.info(type);
		const recurring = type === "series" ? "Yes" : "No";
		setPopoverOpen(false);
		const dfd = window.Flyout.open({
			form: "schedule_event",
			record: event.event._def.publicId,
			override_field_data: { series: recurring, hide_series: "No" },
			autoRecoverEnabled: false,
		});
		dfd.done((data) => {
			void fetchSchedule();
		});

	};

	const refs = useRef({});

	return (
		<>
			<div className="schedule-bar" />
			<div className='module-schedule'>
				<div className="schedule-wrapper">
					<DSLFindView
						{...props}
						form="schedule_event"
						setRef={(ref) => {
							refs.find = ref;
						}}
						// onFind={(event)=>handleOnFind(event)}
						onChange={(event) => handleOnFind(event)}
						onClear={(event) => {
							refs.grid.applyFilters({});
							setFilterApplied(false);
							toggleFilter();
						}}
					/>
				</div>
				<div id="schedule">
					<div className="full-calender-wrapper">
						{isLoading && <SpinLoader
							loading={isLoading}
							style={{
								position: "absolute",
								top: "60%",
								left: "50%",
								transform: "translate(-50%, -50%)",
							}}
						/>}
						<FullCalendar
							ref={calendarRef}
							plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
							headerToolbar={{
								left: "title",
								center: "prev,today,next",
								right: "dayGridMonth,timeGridWeek,timeGridDay",
							}}
							initialView='dayGridMonth'
							timeZone="UTC"
							editable={true}
							selectable={true}
							selectMirror={true}
							dayMaxEvents={true}
							weekends={state.weekendsVisible}
							select={handleDateSelect}
							events={state?.currentEvents || []}
							//eventContent={renderEventContent}
							eventClick={(event) => openSchedule(event)}
							eventDrop={onEventDrop}
							eventResize={onEventDrop}
							firstDay={1}
						/>
						{

							event?.el && <SchedulePopover
								anchorEl={event.el}
								onClose={() => setPopoverOpen(false)} open={popoverOpen}
								data={event}
								onOpen={onOpen}
							/>
						}
					</div>
				</div>
			</div>
		</>
	);
};
