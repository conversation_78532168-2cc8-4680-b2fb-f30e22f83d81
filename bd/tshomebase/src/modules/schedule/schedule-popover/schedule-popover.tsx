/* eslint-disable react/jsx-closing-tag-location */
import type { FC } from "react";
import React from "react";
import type { EventClickArg } from "@fullcalendar/core";
import "./schedule-popover.less";
import snap from "@public/icons/snap";
import Popover from "@mui/material/Popover";

interface SchedulePopoverProps {
	onClose: () => void;
	anchorEl: HTMLElement;
	open: boolean;
	data?: EventClickArg;
	onOpen: (data: EventClickArg, type: string) => void;
}

export const SchedulePopover: FC<SchedulePopoverProps> = (props) => {
	const { onClose, anchorEl, open, data, onOpen } = props;

	if (!open) {
		return null;
	}

	let str = "";

	if (data?.event._def.extendedProps.form_data) {
		const formData = data.event._def.extendedProps.form_data;

		if (formData.repeat_period === "Daily") {
			str = `Repeat Daily, Every ${formData.repeat_daily} Day Until ${formData.event_end_date}`;
		} else if (formData.repeat_period === "Weekly") {
			str = `Repeat Every ${formData.repeat_weekly} Weeks On ${formData.repeat_on} Until ${formData.event_end_date}`;
		} else if (formData.repeat_period === "Monthly") {
			str = `Repeat Every Month ${formData.repeat_monthly} Repeat on the Same ${formData.repeat_by}`;
		}
	}

	return (
		<Popover
			id="sc-popover"
			open={open}
			anchorEl={anchorEl}
			onClose={onClose}
			anchorOrigin={{
				vertical: "top",
				horizontal: "right",
			}}
			transformOrigin={{
				vertical: "center",
				horizontal: "left",
			}}
		>
			<div className="sc-popover">
				<div className="sc-popover-header">
					<h4>Actions</h4>
					<img src={snap.patient.close} alt="Close" onClick={onClose} />
				</div>
				<div className="sc-popover-details">
					<p>{data?.event._def.title}</p>
					<p className="sub-title">{str}</p>
				</div>
				<div className="sc-popover-actions-btn" >
					{
						data && <>
							<button className="btn-individual pop-btn" onClick={() => onOpen(data, "individual")}>Edit this event</button>
							<button className="btn-series pop-btn" onClick={() => onOpen(data, "series")} >Edit this and following events</button>
						</>
					}

				</div>
			</div>

		</Popover>
	);
};
