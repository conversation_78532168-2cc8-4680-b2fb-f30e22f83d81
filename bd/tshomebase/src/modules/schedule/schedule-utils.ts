import { fetchFormFilters } from "@hooks/form-data";

/* eslint-disable @typescript-eslint/no-explicit-any */
const defScheduleTime = "08:00:00";
const defScheduleDuration = 1;
interface Filters {
	[key: string]: unknown | string;
}

interface Event {
	id: number;
	auto_name: string;
	patient_id_auto_name: string;
	appointment_status: string;
	effective_end_time: string;
	effective_end_date: string;
	effective_start_date: string;
	effective_start_time: string;

}

interface EventData {
	effective_start_date: string;
	effective_start_time: string;
	effective_end_time: string;
	effective_end_date: string;
}

export const createFilterUrl = (filters: Filters, form: string, start: string, end: string) => {
	let dfl;
	if (start && end) {
		dfl = start + ".." + end;
	}
	filters["effective_start_date"] = dfl;
	const url = "/form/" + form + "/?" + window.DSLDraw.default_params(true, window.MAX_DATA_ROWS) + "&" + window.joinFilters(filters) + "&sort=-created_on&sort=-updated_on";
	return url;
};

const parseEventDatetime = (d: EventData) => {
	const date = d.effective_start_date;
	const start = d.effective_start_time;
	const end = d.effective_end_time;
	const endDate = d.effective_end_date;
	let ts: Date, te: Date, de: Date;
	if (start && end) {
		ts = new Date(`${date} ${start} GMT+00:00`);
		te = new Date(`${date} ${end} GMT+00:00`);
		de = new Date(`${endDate} ${end} GMT+00:00`);
	} else {
		ts = new Date(`${date} ${start || defScheduleTime} GMT+00:00`);
		te = moment.utc(ts).add(defScheduleDuration, "hours").toDate();
		de = te;
	}

	return { start: ts, end: te, end_date: de };
};


export const transformData = (event: Event) => {
	const time = parseEventDatetime(event);
	return {
		id: event.id,
		// title: event.auto_name,
		title: `${event.appointment_status} ${event.patient_id_auto_name}`,
		patient_name: event.patient_id_auto_name,
		appointment_status: event.appointment_status,
		form_data: event,
		start: time.start,
		end: time.end,
		classNames: ["fc-h-event"]
	};
};


export const getSchedule = async (form: string, start: object, end: object, filters = {}) => new Promise((resolve, reject) => {
	let dfl;
	if (start && end) {
		dfl = start.format() + ".." + end.format();
	}
	filters["effective_start_date"] = dfl;
	fetchFormFilters("schedule_event", { filter: filters }).then((res) => {
		if (typeof res.data === "object" && Array.isArray(res.data)) {
			resolve(res.data);
		} else {
			console.error("Invalid res received:", res);
			reject("Invalid res received");
		}
	}).catch((error) => {
		console.error("Error:", error);
		reject(error);
	})
});


export const updateSchedule = async (id: number | string, data: unknown, form: string) => {
	window.Ajax.async({ url: `/schedule/event/${id}`, type: "PUT", data: data, contentType: "application/json" });
};

