import moment from 'moment';

export function parseDueDate(input: string): string {
  const inputDate = moment(input, 'MM/DD/YYYY', true);
  
  if (!inputDate.isValid()) {
    console.error('Invalid date format. Please use MM/DD/YYYY');
    return '--';
  }

  const today = moment().startOf('day');
  const tomorrow = moment().add(1, 'day').startOf('day');
  const yesterday = moment().subtract(1, 'day').startOf('day');

  if (inputDate.isSame(today, 'day')) {
    return 'Today';
  } else if (inputDate.isSame(tomorrow, 'day')) {
    return 'Tomorrow';
  } else if (inputDate.isSame(yesterday, 'day')) {
    return 'Yesterday';
  } else {
    return input;
  }
}