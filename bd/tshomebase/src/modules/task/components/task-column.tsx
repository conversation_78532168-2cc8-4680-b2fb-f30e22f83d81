import { useDroppable } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { Column } from "../types/task"
import { TaskCard } from "./task-card"
import '../styles/tasks.less'

interface TaskColumnProps {
  column: Column
  onAddNew?: () => void
}

export function TaskColumn({ column, onAddNew}: TaskColumnProps) {
  const { setNodeRef } = useDroppable({
    id: column.type
  })

  return (
    <div className={`task-column ${column.type}`}>
      <div className={`column-header ${column.type}`}>
        <h2>{column.title}</h2>
        {column.type === 'todo' && (
          <button className="add-new-button" onClick={onAddNew}>
            <span>+</span> Add New
          </button>
        )}
      </div>
      <SortableContext 
        id={column.type}
        items={column.tasks}
        strategy={verticalListSortingStrategy}
      >
        <div ref={setNodeRef} className="tasks">
          {column.tasks.map((task) => (
            <TaskCard key={task.id} task={task} />
          ))}
        </div>
      </SortableContext>
    </div>
  )
}