import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Task } from "../types/task"
import '../styles/tasks.less'
import { parseDueDate } from '../utils/dateParser'
import { RoutedComponentProps } from '@typedefs/routed-component'

interface TaskCardProps {
  task: Task
}

export function TaskCard({ task }: TaskCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    setActivatorNodeRef,
  } = useSortable({
    id: task.id,
    disabled: false
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const handleTaskClick = (e: React.MouseEvent) => {
    if (!(e.target as HTMLElement).closest(".patient-link")) {
      window.Flyout.open({
        card: "read",
        record: task.id,
        form: "todo",
      })
    }
  }

  const handlePatientClick = (e: React.MouseEvent, task: Task) => {
    e.preventDefault();
    e.stopPropagation();
    window.App.reactNav.goTo(`/patient/${task.patient_id}/snap`);
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`task-card ${task.status.toLowerCase()}`}
      onClick={(e) => handleTaskClick(e)}
    >
      <div className="card-wrapper">
        <div
          className={`card-header ${task.status.toLowerCase()}`}
          ref={setActivatorNodeRef}
          {...listeners}>
          <h3>{task.title}</h3>
          <button className="more-button">
            <i className={`fa-regular fa-ellipsis-vertical ${task.status.toLowerCase()}`}></i>
          </button>
        </div>
        <div className="card-content">
          <p className="description">{task.description}</p>
          {task.patient_id_auto_name && (
            <div className="reference">
              <div className='reference-info'>
                <i className="fa-light fa-arrow-up-right-from-square patient-link" onClick={(e) => handlePatientClick(e, task)}></i>
                <a className='patient-link' onClick={(e) => handlePatientClick(e, task)}>
                  {task.patient_id_auto_name}
                </a>
              </div>
              <div className="due-date">{parseDueDate(task.due_date)}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}