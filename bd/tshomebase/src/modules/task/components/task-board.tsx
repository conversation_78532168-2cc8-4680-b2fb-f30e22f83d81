import React, { useEffect, useState } from 'react'
import { DndContext, DragOverlay, closestCorners, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable'
import { Task, Column } from "../types/task"
import { TaskColumn } from "./task-column"
import { TaskCard } from "./task-card"
import '../styles/tasks.less'
import { request } from '@core/request'
import { showToastError } from '@utils/fx'
import moment from 'moment'
import { RoutedComponentProps } from '@typedefs/routed-component'

export function TaskBoard(props: RoutedComponentProps) {

  const [columns, setColumns] = useState<Column[]>([
    {
      title: 'To Do',
      type: 'todo',
      tasks: []
    },
    {
      title: 'Pending',
      type: 'pending',
      tasks: []
    },
    {
      title: 'Complete',
      type: 'complete',
      tasks: []
    }
  ])
  const [activeId, setActiveId] = useState<string | null>(null)
  const [previousState, setPreviousState] = useState<Column[]>([])

  useEffect(() => {
    getTasks();
  }, [])

  const getTasks = async () => {
    try {
      const tasks = await request({
        url: '/my/todo'
      })
      if (tasks.data.length > 0) {
        setColumns([
          {
            title: 'To Do',
            type: 'todo',
            tasks: tasks.data.filter((task: Task) => task.status.toLowerCase() === 'todo')
          },
          {
            title: 'Pending',
            type: 'pending',
            tasks: tasks.data.filter((task: Task) => task.status.toLowerCase() === 'pending')
          },
          {
            title: 'Complete',
            type: 'complete',
            tasks: tasks.data.filter((task: Task) => task.status.toLowerCase() === 'complete')
          }
        ])
      }
    } catch (error) {
      console.error('Error fetching tasks:', error)
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const handleDragStart = (event: any) => {
    const { active } = event
    setActiveId(active.id)
    setPreviousState(columns);
  }

  const handleDragOver = (event: any) => {
    const { active, over } = event
    if (!over) {
      setPreviousState(columns);
      return
    }

    const activeColumn = columns.find(col => col.tasks.some(task => task.id === active.id))
    const overColumn = columns.find(col => col.type === over.id || col.tasks.some(task => task.id === over.id))

    if (!activeColumn || !overColumn || activeColumn === overColumn) {
      return
    }

    setColumns(prev => {
      const activeItems = activeColumn.tasks
      const overItems = overColumn.tasks

      const activeIndex = activeItems.findIndex(item => item.id === active.id)
      const overIndex = overItems.findIndex(item => item.id === over.id)

      let newIndex: number
      if (over.id in columns) {
        newIndex = overItems.length + 1
      } else {
        const isBelowLastItem = over && overIndex === overItems.length - 1 && active.rect.current.translated && active.rect.current.translated.top > over.rect.top + over.rect.height
        const modifier = isBelowLastItem ? 1 : 0
        newIndex = overIndex >= 0 ? overIndex + modifier : overItems.length + 1
      }

      const updatedColumns = prev.map(col => {
        if (col.type === activeColumn.type) {
          return {
            ...col,
            tasks: [...col.tasks.filter(item => item.id !== active.id)]
          }
        } else if (col.type === overColumn.type) {
          const updatedTask = { ...activeItems[activeIndex], status: overColumn.type as Task['status'] }
          return {
            ...col,
            tasks: [
              ...col.tasks.slice(0, newIndex),
              updatedTask,
              ...col.tasks.slice(newIndex)
            ]
          }
        }
        return col
      })

      return updatedColumns
    })
  }

  const handleDragEnd = async (event: any) => {
    const { active, over } = event
    if (!over) return

    const activeColumn = columns.find(col => col.tasks.some(task => task.id === active.id))
    const overColumn = columns.find(col => col.type === over.id || col.tasks.some(task => task.id === over.id))

    if (!activeColumn || !overColumn) return

    const activeIndex = activeColumn.tasks.findIndex(task => task.id === active.id)
    const overIndex = overColumn.tasks.findIndex(task => task.id === over.id)

    if (activeColumn !== overColumn) {
      setColumns(prev => {
        return prev.map(col => {
          if (col.type === activeColumn.type) {
            return {
              ...col,
              tasks: col.tasks.filter(task => task.id !== active.id)
            }
          } else if (col.type === overColumn.type) {
            const updatedTask = { ...activeColumn.tasks[activeIndex], status: overColumn.type as Task['status'] }
            const newTasks = Array.from(col.tasks)
            newTasks.splice(overIndex, 0, updatedTask)
            return {
              ...col,
              tasks: newTasks
            }
          }
          return col
        })
      })
    } else {
      setColumns(prev => {
        return prev.map(col => {
          if (col.type === activeColumn.type) {
            const newTasks = Array.from(col.tasks)
            const [reorderedItem] = newTasks.splice(activeIndex, 1)
            newTasks.splice(overIndex, 0, reorderedItem)
            return {
              ...col,
              tasks: newTasks
            }
          }
          return col
        })
      })
    }

    const newStatus = overColumn.type === 'todo' ? 'Todo' : overColumn.type.charAt(0).toUpperCase() + overColumn.type.slice(1)
    try {
      const activeTask = activeColumn.tasks.find(task => task.id === active.id)
      await request({
        url: `/my/todo/`,
        method: 'PUT',
        data: {
          ...activeTask,
          status: newStatus,
        }
      })
      getTasks();
    } catch (error) {
      setColumns(previousState);
      console.error('Error while updating task status:', error);
      showToastError('Error while updating task status');
    }

    setActiveId(null)
  }

  const handleNewTask = async () => {
    try {
      window.Flyout.open({
        mode: 'add',
        form: 'todo',
        preset: {
          status: 'Todo',
          due_date: moment().format('MM/DD/YYYY'),
          user_id: window.UserPreference.user_id
        }
      }).done(() => {
        getTasks();
      })
    } catch (error) {
      console.error('Error while creating task:', error);
      showToastError('Error while creating task');
    }
  }

  const task = activeId ? columns.flatMap(col => col.tasks).find(task => task.id === activeId) : null

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="task-board">
        {columns.map((column) => (
          <TaskColumn
            key={column.type}
            column={column}
            onAddNew={column.type === 'todo' ? () => handleNewTask() : undefined}
          />
        ))}
      </div>
      <DragOverlay>
        {task ? <TaskCard task={task} /> : null}
      </DragOverlay>
    </DndContext>
  )
}
