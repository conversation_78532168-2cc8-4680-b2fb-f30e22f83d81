@import "../../../styles/base/less/colors.less";
@import "../../../styles/base/less/dimensions.less";
@import "../../../styles/base/less/fonts.less";

/// Colors not in Pallete ///
@purple-100: #EBE8F6;
@orange-100: #FBE4C6;
@green-100: #D5EFE8;
@gray-300: #d1d5db;
@gray-500: #6c6c78;
@gray-700: #374151;
@gray-100: #f3f4f6;
@white: #ffffff;
@green: #5E927E;

.task-board {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xxxlarge);
  max-width: 100%;
  border-radius: var(--radius-xlarge);
  margin: 0 auto;
  height: calc(100vh - 105px);
  background-color: @white;
  padding: var(--spacing-xxxlarge);
}

.task-column {
  display: flex;
  flex-direction: column;
  min-width: 300px;
  max-height: 100%;

  .column-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xxxlarge);
    flex-shrink: 0;
    height: 46px;

    &.todo {
      h2 {
        margin: 0px;
        font-family: Inter;
        font-weight: 700;
        font-size: var(--font-size-small);
        color: #7C74A8;
      }

      .add-new-button {
        color: #7C74A8;
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
        border: none;
        border-radius: var(--radius-medium);
        padding: var(--radius-medium) var(--radius-xlarge);
        font-family: Inter;
        font-weight: var(--font-weight-medium);
        font-size: var(--font-size-xxsmall);
        line-height: 20px;
        background: @white;
        cursor: pointer;
        transition: all 0.2s ease;
        gap: var(--spacing-xsmall);
        height: 36px;
        display: inline-flex;
        align-items: center;

        span {
          font-weight: var(--font-weight-medium);
          font-size: var(--font-size-standard);
          padding: 0px var(--radius-xxsmall);
          line-height: 1;
        }

        &:hover {
          background-color: @purple-100;
          transition: background-color 0.3s ease-in-out;
        }
      }
    }

    &.pending h2 {
      margin: 0px;
      font-family: Inter;
      font-weight: 700;
      font-size: var(--font-size-small);
      color: var(--color-secondary-500);
    }

    &.complete h2 {
      margin: 0px;
      font-family: Inter;
      font-weight: 700;
      font-size: var(--font-size-small);
      color: @green;
    }
  }

  .tasks {
    display: flex;
    flex-direction: column;
    gap: var(--radius-large);
    overflow-y: auto;
    flex-grow: 1;
    padding-top: var(--radius-large);
    // Show scrollbar only when scrolling
    &::-webkit-scrollbar {
      width: 8px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
      transition: background-color 0.3s ease-in-out;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: @gray-300;
      border-radius: var(--radius-xsmall);
    }

    &:hover::-webkit-scrollbar-thumb:hover {
      background-color: @gray-500;
    }
  }
}

.task-card {
  cursor: grab;
  border-radius: var(--radius-large);
  transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 0.3s ease;
  transform-origin: center;
  overflow: hidden;
  background: @white;
  border: 2px solid;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &.todo {
    border: 2px solid #7C74A8;
  }

  &.pending {
    border: 2px solid var(--color-secondary-500);
  }

  &.complete {
    border: 2px solid #689989;
  }

  &:hover {
    transform: rotate(-2deg) scale(0.95);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  &:active {
    cursor: grabbing;
  }

  &.is-dragging {
    opacity: 1;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .card-wrapper {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-large);
      border-radius: var(--radius-xlarge);
      background: @white;

    .card-header {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: space-between;
      padding: 0px var(--radius-small) 0px var(--radius-xlarge);

      &.todo {
        h3 {
          font-size: var(--font-size-small);
          font-weight: 600;
          font-family: Inter;
          margin: 0;
          color: #7C74A8;
        }
      }

      &.pending {
        h3 {
          font-size: var(--font-size-small);
          font-weight: 600;
          font-family: Inter;
          margin: 0;
          color: var(--color-secondary-500);
        }
      }

      &.complete {
        h3 {
          font-size: var(--font-size-small);
          font-weight: 600;
          font-family: Inter;
          margin: 0;
          color: #689989;
        }
      }

      .more-button {
        background: none;
        border: none;
        cursor: pointer;
        i {
          font-size: 20px;
          cursor: pointer;
          &.todo {
            color: #7C74A8;
          }
          &.pending {
            color: var(--color-secondary-500);
          }
          &.complete {
            color: #689989;
          }
        }
      }

    }

    .card-content {
      display: flex;
      flex-direction: column;
      gap: var(--radius-xxxlarge);
      background: @white;
      padding: var(--radius-large) var(--radius-xxxlarge) var(--radius-xxxlarge) var(--radius-xxxlarge);
      border-radius: var(--radius-medium);

      .description {
        font-size: var(--font-size-xsmall);
        color: #4C4B5B;
        line-height: 16.94px; //used here because of odd line height
        font-family: Inter;
        font-weight: 500;
      }

      .reference {
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: calc(var(--radius-medium) -1px);
        .reference-info {
          display: flex;
          gap: var(--spacing-standard);
          align-items: center;
          a {
            font-size: var(--font-size-xsmall);
            font-weight: 500;
            font-family: Inter;
            color: #7C74A8;
            text-decoration: underline;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }
        }

      }

      .due-date {
        font-size: var(--font-size-xsmall);
        line-height: var(--line-height-medium);
        font-weight: 500;
        font-family: Inter;
        color: @gray-500;
        ;
      }
    }
  }
}

.complete {
  .task-card {
    .card-header h3,
    .card-content .description {
      text-decoration: line-through;
      color: #9ca3af;
    }
  }
}

// Drag and drop styles
.sortable-ghost {
  opacity: 1;
}

.sortable-drag {
  opacity: 1;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}