import { NestedTabView } from "@blocks/nested-tab-view/nested-tab-view";
import { useEffect, useMemo } from "react";
import { useNavigation } from "@core/navigation";
import type { FC } from "react";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import { Preference } from "./preference/preference";
import { filterArrayByAccess } from "@utils/fx";
import { createPortalModal } from "@blocks/portal-modal/portal-modal";
import { LogoutConfirmationBox } from "@components/popups/logout";
import * as style from "./style.module.less";
import "@blocks/nested-tab-view/nested-tab-view.less";

type SettingProps = RoutedComponentProps;

export const My: FC<SettingProps> = (props) => {
	const nav = useNavigation(props, "/my");
	useEffect(() => {
		if (props.navToURL?.startsWith("/my")) {
			props.setActiveTab?.("my");
		}
	}, [nav.globalNav.lrct]);

	const map = useMemo(() => {
		const defaultMyArray = [
			{
				label: "Preferences",
				id: "preferences",
				path: "/my/preferences",
				component: {
					renderComponent: Preference,
				},
				tabListProps: {
					customTab: (
						<div
							onClick={(key) => {
								new Promise((resolve, reject) => {
									createPortalModal(
										LogoutConfirmationBox as FC<unknown>,
										{},
										{ parent: {}, promise: { resolve, reject } }
									).catch((err) => console.error(err));
								}).catch((err) => console.error(err));
							}}
							className={`${style.logoutBtn} tab-list-button`}
						>
							Logout
						</div>
					),
				},
			},
		];
		return filterArrayByAccess(defaultMyArray);
	}, []);

	const levelStyle = {
		levels: {
			0: {
				styles: {
					tabListStyle: "lvl-2-tab-list",
				},
			},
		},
		dsl: {},
	};

	return <NestedTabView {...{ ...props, ...nav, map, levelStyle }} />;
};
