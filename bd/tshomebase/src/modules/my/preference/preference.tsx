import type { FC } from "react";
import React, { useEffect, useState, useContext } from "react";
import "./preference.less";
import { PreferenceSettingContext } from "@contexts/preference/provider";
import { useOnScreen } from "@hooks/on-screen";
import ViewMode from "@enum/view-mode";
import { getPrinters } from "@utils/qz-utils";
import { StylesConfig } from "react-select";
import SpinLoader from "@components/common/spin-loader";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { FieldSelect } from "@dsl/fields/field-select";

interface PreferenceProps {}

const viewModes = [
	{
		id: ViewMode.NORMAL,
		text: "Standard",
	},
	{
		id: ViewMode.COMPACT,
		text: "Rob Mode",
	},
];

const searchShortcuts: string[] = ["F1", "F2", "F3"];

const fragmentModes: string[] = ["Yes", "No"];

const selectStyleOver: StylesConfig = {
	control: (provided, state) => ({
		...provided,
		height: "60px",
		borderRadius: "8px",
		border: `1px solid ${state.isFocused ? "#CBD5E0" : "#E3E5E8"}`,
		boxShadow: "0px 1px 2px 1px #38383814 inset",
		"&:hover": {
			borderColor: "#CBD5E0",
		},
		padding: "10px",
	}),
	valueContainer: (provided) => ({
		...provided,
		marginTop: "18px",
		padding: "0px",
	}),
	placeholder: (provided) => ({
		...provided,
		display: "flex",
		flexDirection: "column",
		alignItems: "flex-start",
		lineHeight: "1.2",
		color: "#9B9FA8",
	}),
	input: (provided) => ({
		...provided,
		margin: "0",
		padding: "0",
		color: "#414651",
	}),
	indicatorsContainer: (provided) => ({
		...provided,
		height: "100%",
		color: "#717680",
		"&:hover": {
			color: "#4A5568",
		},
	}),
	indicatorSeparator: (provided) => ({
		...provided,
		display: "none",
	}),
	clearIndicator: (provided) => ({
		...provided,
		color: "#717680",
		"&:hover": {
			color: "#4A5568",
		},
	}),
	dropdownIndicator: (provided) => ({
		...provided,
		color: "#717680",
		"&:hover": {
			color: "#4A5568",
		},
	}),
	menu: (provided) => ({
		...provided,
		marginTop: "8px",
		borderRadius: "8px",
		boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
	}),
	menuList: (provided) => ({
		...provided,
		padding: "8px",
	}),
	menuPortal: (provided) => ({
		...provided,
		zIndex: 9999,
	}),
	option: (provided, state) => ({
		...provided,
		padding: "12px 16px",
		borderRadius: "6px",
		backgroundColor: state.isSelected ? "#EDF2F7" : "transparent",
		color: "#4A5568",
		"&:hover": {
			backgroundColor: "#F7FAFC",
		},
	}),
};

export const Preference: FC<PreferenceProps> = React.memo((props) => {
	const [isOnScreen, refElement] = useOnScreen(true);
	const [isLoading, preference, updatePref, refreshPref] = useContext(PreferenceSettingContext);
	const [qzState, setQzState] = useState("checking");
	const [siteId, setSiteId] = useState<{ site_id: string; site_id_auto_name: string } | null>(null);
	const [printers, setPrinters] = useState<{
		document: Record<string, string>[];
		cpr_report: Record<string, string>[];
		cpr_form: Record<string, string>[];
		pharmacy_po_label: Record<string, string>[];
		pharmacy_work_order: Record<string, string>[];
		delivery_ticket: Record<string, string>[];
		labelsship: Record<string, string>[];
		pharmacy_label: Record<string, string>[];
		cms_1500_form: Record<string, string>[];
		physician_cover_letter: Record<string, string>[];
		syringe_label: Record<string, string>[];
		pharmacy_tpn_label: Record<string, string>[];
		picklist_printer: Record<string, string>[];
		verbal_order_printer: Record<string, string>[];
	}>({
		document: [],
		cpr_report: [],
		cpr_form: [],
		pharmacy_po_label: [],
		pharmacy_work_order: [],
		delivery_ticket: [],
		labelsship: [],
		pharmacy_label: [],
		cms_1500_form: [],
		physician_cover_letter: [],
		syringe_label: [],
		pharmacy_tpn_label: [],
		picklist_printer: [],
		verbal_order_printer: [],
	});

	type PrinterType = keyof typeof printers;

	const printersData: Array<{
		id: string;
		text: string;
		printerType: PrinterType;
	}> = [
		{
			id: "document_printer",
			text: "Document Printer",
			printerType: "document",
		},
		{
			id: "pharmacy_label_printer",
			text: "Pharmacy Label Printer",
			printerType: "pharmacy_label",
		},
		{
			id: "cpr_report_printer",
			text: "CPR+ Report Printer",
			printerType: "cpr_report",
		},
		{
			id: "cpr_form_printer",
			text: "CPR+ Form Printer",
			printerType: "cpr_form",
		},
		{
			id: "pharmacy_po_label_printer",
			text: "Pharmacy PO Label Printer",
			printerType: "pharmacy_po_label",
		},
		{
			id: "pharmacy_work_order_printer",
			text: "Pharmacy Work Order Printer",
			printerType: "pharmacy_work_order",
		},
		{
			id: "delivery_ticket_printer",
			text: "Delivery Ticket Printer",
			printerType: "delivery_ticket",
		},
		{
			id: "labelsship_printer",
			text: "Labelsship Printer",
			printerType: "labelsship",
		},
		{
			id: "cms_1500_form_printer",
			text: "CMS 1500 Form Printer",
			printerType: "cms_1500_form",
		},
		{
			id: "physician_cover_letter_printer",
			text: "Physician Cover Letter Printer",
			printerType: "physician_cover_letter",
		},
		{
			id: "syringe_label_printer",
			text: "Syringe Label Printer",
			printerType: "syringe_label",
		},
		{
			id: "pharmacy_tpn_label_printer",
			text: "Pharmacy TPN Label Printer",
			printerType: "pharmacy_tpn_label",
		},
		{
			id: "picklist_printer",
			text: "Pick List Printer",
			printerType: "picklist_printer",
		},
		{
			id: "verbal_order_printer",
			text: "Verbal Order Printer",
			printerType: "verbal_order_printer"
		},
	];

	useEffect(() => {
		if (!isOnScreen) {
			return;
		}
		if (!window.location.hash.endsWith("/preferences")) {
			return;
		}
		refreshPref();
		getPrinters()
			.then((prntr) => {
				if (!prntr) {
					setQzState("no-qztray");
					return;
				}
				let dp = [];
				let lp = [];
				for (const p of prntr) {
					let size = null;
					if (p.sizes && p.sizes.length > 0) {
						size = p.sizes[0].in;
					}
					p.value = p.name;
					p.label = p.name;
					if (size?.height && size.width) {
						if ((size.width > 9 && size.height > 7) || (size.width > 7 && size.height > 9)) {
							dp.push(p);
						} else {
							lp.push(p);
						}
					} else {
						dp.push(p);
						lp.push(p);
					}
				}
				dp = dp.length == 0 ? lp : dp;
				lp = lp.length == 0 ? dp : lp;
				setQzState("found");
				setPrinters({
					document: dp,
					pharmacy_label: lp,
					cpr_report: dp,
					cpr_form: dp,
					pharmacy_po_label: dp,
					pharmacy_work_order: dp,
					delivery_ticket: dp,
					labelsship: dp,
					cms_1500_form: dp,
					physician_cover_letter: dp,
					syringe_label: dp,
					pharmacy_tpn_label: dp,
					picklist_printer: dp,
					verbal_order_printer: dp
				});
			})
			.catch((error) => {
				console.error("QZ Tray: Unable to Fetch Printer List", error);
			});
	}, [isOnScreen]);
	const subformPrinters =
		preference.subform_printers?.length > 0
			? preference?.subform_printers?.find((p: any) => {
					if (siteId) {
						return p.site_id == siteId.site_id;
					} else {
						return p.site_id == null;
					}
			  })
			: null;

	useEffect(() => {
		refreshPref();
	}, [siteId]);
	return (
		(isLoading && !preference?.id && <SpinLoader loading={isLoading} />) || (
			<div className="pf-cnt" ref={refElement}>
				<div className="pf-op">
					<div className="pf-op-heading">Layout</div>
					<div className="op-cnt">
						<span className="op-label">Select Layout :</span>
						<div className="op-content">
							<span className="pf-op-btns">
								{viewModes.map((viewMode) => (
									<button
										key={viewMode.id}
										onClick={() => updatePref("view_mode", viewMode.id)}
										className={`pf-op-btn ${viewMode.id == preference.view_mode && "slt-op-btn"}`}
									>
										{viewMode.text}
									</button>
								))}
							</span>
						</div>
					</div>
				</div>
				<hr className="pf-sep-line" />
				<div className="pf-op">
					<div className="pf-op-heading">Search</div>
					<div className="op-cnt">
						<span className="op-label"> Keyboard Shortcut :</span>
						<div className="op-content">
							<div className="pf-op-btns">
								{searchShortcuts.map((shortcut) => (
									<button
										key={shortcut}
										onClick={() => updatePref("search_shortcut_key", shortcut)}
										className={`pf-op-btn ${
											shortcut == preference.search_shortcut_key && "slt-op-btn"
										}`}
									>
										{shortcut}
									</button>
								))}
							</div>
						</div>
					</div>
				</div>

				<hr className="pf-sep-line" />
				<div className="pf-op">
					<div className="site-id-select-field-container" style={{ width: "450px" }}>
						<label className="site-id-label">Default Prefill Site</label>
						<FieldSelect
							form="site"
							defaultValueSource={preference?.default_prefill_site_id || ""}
							defaultValueAutoName={preference?.default_prefill_site_id_auto_name || ""}
							multi={false}
							disabled={false}
							onChange={(val, an) => {
								updatePref("default_prefill_site_id", val);
								updatePref("default_prefill_site_id_auto_name", an);
							}}
							customStyles={selectStyleOver}
							theme={SelectConfig.dsl.theme}
							placeholder="Default Prefill Site"
						/>
					</div>
				</div>

				<hr className="pf-sep-line" />
				{qzState == "no-qztray" ? (
					<div className="qz-msg">
						If you have installed qz.io app then start it otherwise download and install to access printers:{" "}
						<a className="qz-link" href="https://qz.io/download/" target="_blank" rel="noopener noreferrer">
							[Download]
						</a>
					</div>
				) : (
					<>
						<div className="site-id-container">
							<div className="site-id-select-field-container">
								<label className="site-id-label">Site ID</label>
								<FieldSelect
									form="site"
									defaultValueSource={siteId?.site_id || ""}
									defaultValueAutoName={siteId?.site_id_auto_name || ""}
									multi={false}
									disabled={false}
									onChange={(val, an) => {
										setSiteId({ site_id: val, site_id_auto_name: an });
									}}
									customStyles={selectStyleOver}
									theme={SelectConfig.dsl.theme}
									placeholder="Select Site"
								/>
							</div>
						</div>
						<div className="printers-container">
							{printersData.map((printer) => (
								<div key={printer.id} className="printer-container">
									<div className="printer-select-field-container">
										<label className="printer-select-field-label">{printer.text}</label>
										<FieldSelect
											form="none"
											defaultValueSource={subformPrinters?.[printer.id] || ""}
											defaultValueAutoName={subformPrinters?.[printer.id] || ""}
											multi={false}
											disabled={false}
											options={printers[printer.printerType] || []}
											onChange={(val, an) => {
												updatePref(
													"subform_printers",
													[
														{
															...subformPrinters,
															[printer.id]: val,
															site_id: siteId?.site_id || null,
														},
													],
													siteId?.site_id || ""
												);
											}}
											customStyles={selectStyleOver}
											theme={SelectConfig.dsl.theme}
											placeholder="Select Printer"
										/>
									</div>
								</div>
							))}
						</div>
					</>
				)}
			</div>
		)
	);
});
