@import "../../../less/style/main.less";

.pf-cnt {
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
	gap: var(--layout-pf-cnt-g);
	padding: var(--layout-pf-cnt-tmp-p);
	align-items: stretch;
	background-color: white;
	overflow-y: auto;
	overflow-x: hidden;
	box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.1);

	.site-id-container {
		width: 19%;

		.site-id-select-field-container {
			flex: 1;
			box-sizing: border-box;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			position: relative;

			.site-id-label {
				position: absolute !important;
				top: var(--spacing-standard);
				left: var(--spacing-xlarge);
				font-size: var(--font-size-xsmall);
				font-weight: var(--font-weight-regular);
				color: var(--color-text);
				z-index: 1;
				pointer-events: none;
			}
		}
	}

	.printers-container {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		gap: 16px;

		.printer-container {
			width: 19%;

			.printer-select-field-container {
				flex: 1;
				box-sizing: border-box;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				position: relative;

				.printer-select-field-label {
					position: absolute !important;
					top: var(--spacing-standard);
					left: var(--spacing-xlarge);
					font-size: var(--font-size-xsmall);
					font-weight: var(--font-weight-regular);
					color: var(--color-text);
					z-index: 1;
					pointer-events: none;
				}
			}
		}
	}

	.pf-op {
		display: flex;
		flex-direction: column;
		gap: var(--pf-op-gap);
		justify-content: flex-start;
		flex-wrap: wrap;
		flex-shrink: 0;

		.pf-op-heading {
			height: 22px;
			font-weight: 600;
			font-size: var(--pf-op-heading-font-size);
			line-height: 21.6px;
		}

		.op-cnt {
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			align-items: flex-start;
			align-items: center;
			min-width: 950px;

			.op-label {
				height: 17px;
				font-weight: 500;
				font-size: 14px;
				width: 15%;
			}

			.op-content {
				cursor: pointer;

				.pf-op-btns {
					display: flex;
					gap: var(--pf-op-btns-gap);

					.pf-op-btn {
						min-width: var(--pf-op-btn-min-width);
						font-weight: var(--pf-op-btn-font-weight);
						font-size: var(--pf-op-btn-font-size);
						line-height: var(--pf-op-btn-line-height);
						align-items: center;
						border: 1px solid transparent;
						border-radius: 5px;
						padding: var(--pf-of-btn-p);
					}
				}
			}
		}
	}

	.slt-op-btn {
		background-color: rgba(83, 178, 222, 1);
		color: white;
	}

	.pf-sep-line {
		height: 1px;
		background-color: black;
		margin: 0 0;
	}

	.qz-msg {
		font-weight: 500;
	}

	.qz-link {
		font-weight: 600;
	}
}
