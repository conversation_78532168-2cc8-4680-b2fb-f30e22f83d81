import "./erx-filters.less";
import { removeFalsyValues } from "@utils/dsl-fx";
import React, { FC, useCallback, useEffect, useState } from "react";
import { useERXCounts } from "./erx-hooks";
import { FieldSelect } from "@dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

type ERXFiltersProps = {
	filterRefCallBack: (ref: { reCount: () => void }) => void;
	getListViewRefs: () => AdvancedGridRef | null;
};
export const ERXFilters = (props: ERXFiltersProps) => {
	const [filters, setFilters] = useState<Record<string, unknown>>({});
	const [status, setStatus] = useState("");
	const [counts, reCount] = useERXCounts();
	const statusButtonClick = useCallback(
		(cs: string) => {
			let flt = {
				processed: "",
				direction: "",
				direction_auto_name: "",
				message_type: [],
			} as Record<string, unknown>;
			if (status == cs) {
				setFilters({ ...filters, ...flt });
				setStatus("");
				return;
			}
			if (cs === "new") {
				flt = {
					processed: "No",
					direction: "IN",
					direction_auto_name: "Inbox",
					message_type: [],
				};
			} else if (cs === "replies") {
				flt = {
					processed: "No",
					direction: "IN",
					direction_auto_name: "Inbox",
					message_type: ["RxChangeResponse", "RxRenewalResponse"],
				};
			} else if (cs === "cancel") {
				flt = {
					processed: "No",
					direction: "IN",
					direction_auto_name: "Inbox",
					message_type: ["CancelRx"],
				};
			}
			setStatus(cs);
			setFilters({ ...filters, ...flt });
		},
		[status, filters]
	);

	if (props.filterRefCallBack) {
		props.filterRefCallBack({ reCount });
	}

	useEffect(() => {
		let refs = props.getListViewRefs();
		if (!refs) return;
		let fv = removeFalsyValues(filters);
		refs.advanced.filter.apply(fv);
		reCount();
	}, [filters]);

	return (
		<div className="erx-filters">
			<div className="erx-status-options">
				<div className="erx-label">Status</div>
				<div className="erx-filter-status">
					<div
						className={`erx-status-btn${status == "new" ? " active" : ""}`}
						onClick={() => {
							statusButtonClick("new");
						}}
					>
						<div>{counts.new}</div>
						<p>New Messages</p>
					</div>
					<div
						className={`erx-status-btn${status == "replies" ? " active" : ""}`}
						onClick={() => {
							statusButtonClick("replies");
						}}
					>
						<div>{counts.replies}</div>
						<p>New Replies</p>
					</div>
					<div
						className={`erx-status-btn danger${status == "cancel" ? " active" : ""}`}
						onClick={() => {
							statusButtonClick("cancel");
						}}
					>
						<div>{counts.cancel}</div>
						<p>Cancel Request</p>
					</div>
				</div>
			</div>

			<div className="erx-select">
				<div className="erx-label">Patient</div>
				<FieldSelect
					form="patient"
					defaultValueSource={filters.patient_id || ""}
					defaultValueAutoName={filters.patient_id_auto_name || ""}
					multi={false}
					disabled={false}
					onChange={(val, an) => {
						setFilters({ ...filters, patient_id: val, patient_id_auto_name: an });
					}}
					customStyles={SelectConfig.dsl.style}
					theme={SelectConfig.dsl.theme}
					placeholder="Patient"
				/>
			</div>
			<div className="erx-select">
				<div className="erx-label">Site</div>
				<FieldSelect
					form="site"
					defaultValueSource={filters.site_id || ""}
					defaultValueAutoName={filters.site_id_auto_name || ""}
					multi={false}
					disabled={false}
					onChange={(val, an) => {
						setFilters({ ...filters, site_id: val, site_id_auto_name: an });
					}}
					customStyles={SelectConfig.dsl.style}
					theme={SelectConfig.dsl.theme}
					placeholder="Site"
				/>
			</div>
			<div className="erx-select">
				<div className="erx-label">Directions</div>
				<FieldSelect
					form="none"
					defaultValueSource={filters.direction || ""}
					defaultValueAutoName={filters.direction_auto_name || ""}
					multi={false}
					disabled={false}
					options={[
						{ value: "IN", label: "Inbox" },
						{ value: "OUT", label: "Outbox" },
					]}
					onChange={(val, an) => {
						setFilters({ ...filters, direction: val, direction_auto_name: an });
					}}
					customStyles={SelectConfig.dsl.style}
					theme={SelectConfig.dsl.theme}
					placeholder="Directions"
				/>
			</div>
		</div>
	);
};
