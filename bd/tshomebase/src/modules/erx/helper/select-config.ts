import Select, { StylesConfig, Theme } from 'react-select';

export const selectStyles: StylesConfig = {
    container(base, props) {
        return {
            ...base,
            width: "100%",
        };
    },
    control: (provided, state) => ({
        ...provided,
        borderRadius: "8px",
        backgroundColor: state.isFocused ? "#FFFFFF" : "#FCFCFC",
        borderColor: state.isFocused ? "#58505B" : "rgba(88, 80, 91, 0.251)",
        boxShadow: state.isFocused ? "0px 0px 0px 0.5px #58505B" : "0px 0px 0px 0.5px rgba(88, 80, 91, 0.251)",
        borderWidth: "1px",
        '&:hover': {
            borderColor: "rgba(88, 80, 91, 0.5)", // Change border color on hover
            boxShadow: state.isFocused ? "#58505B" : "rgba(88, 80, 91, 0.251)",
        }
    }),

    valueContainer: (provided, state) => ({
        ...provided,
        maxWidth: "120px",
        fontWeight: "bold",
    }),
    placeholder: (provided, state) => ({
        ...provided,
        color: "#bdbbbb",
        fontWeight: "700",
    }),
    input: (provided, state) => ({
        ...provided,
    }),
    indicatorSeparator: state => ({
        display: "none",
    }),
    dropdownIndicator: base => ({
        ...base,
        color: "#111",
        opacity: "0.5",
        cursor: 'pointer',
    }),
    clearIndicator: base => ({
        ...base,
        color: "#111",
        padding: "unset",
        opacity: "0.5",
        cursor: 'pointer',
        marginRight: "-7px",
    }),
    menu: (provided) => ({
        ...provided,
        borderRadius: "8px",
        backgroundColor: "#FFFFFF", // Background color of the dropdown menu
        border: "1px solid rgba(88, 80, 91, 0.251)", // Border of the dropdown menu
        boxShadow: "0 2px 10px rgba(0, 0, 0, 0.1)", // Shadow for the dropdown menu
        marginTop: "4px",
    }),
    menuList: (provided) => ({
        ...provided,
        padding: "4px",
        borderRadius: "8px",
        borderWidth: "2px",
        border: "1px solid #9974CE"
    }),
    option: (provided, state) => ({
        ...provided,
        backgroundColor: state.isFocused ? "#58505B" : "#FFFFFF", // Background color for selected and focused options
        borderRadius: "6px",
        color: state.isFocused ? "#fff" : "#111", // Background color for selected and focused options
        padding: "2px 10px",
        fontSize: "14px",
        lineHeight: "24px",
        fontWeight: "500",
        fontFamily: 'Inter',
        margin: "5px 0px",
    }),
};

export const selectTheme = (theme: Theme) => {
    return {
        ...theme,
        colors: {
            ...theme.colors,
            primary: "#007bff",
            primary25: "#f0eff0",
            primary50: "#d1d1d1",
            primary75: "#9e9d9c",
        },
    }
}