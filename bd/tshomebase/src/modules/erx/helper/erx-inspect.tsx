import { FC, useEffect, useState } from "react";
import ReactDOM<PERSON>erver from "react-dom/server";
import XML<PERSON>iewer from "react-xml-viewer";
import { GenericInspectComponentProps } from "@blocks/dsl-inspect-view/dsl-inspect-view";
import "./erx-inspect.less";
import { IFormData, fetchFormFilters, useFormData } from "@hooks/form-data";
import statusIcons from "@public/icons/erx";
import { TabbedCardView } from "@blocks/dsl-card-view/tabbed-card-view";
import { request } from "@core/request/request";
import { getType, uuid } from "@utils/fx";
import { TabData } from "@blocks/tab-list/tab-list";
import _ from "lodash";

type SSActionTypes =
	| "resend"
	| "refill"
	| "followup"
	| "create"
	| "pa"
	| "clarification"
	| "change"
	| "provider"
	| "approve_cancel"
	| "deny_cancel";

type SSRequestFuncTypes =
	| "check_edit"
	| "fetch_actions"
	| "create_order"
	| "save_workflow"
	| "generate_records"
	| "request_pa"
	| "send_followup"
	| "request_refill"
	| "request_clarification"
	| "request_change"
	| "request_info"
	| "approve_cancel"
	| "deny_cancel";

type ActionButtonMap = Record<
	SSActionTypes,
	{
		onClick: (sd: IFormData) => void;
		label: string;
	}
>;
type IWFRecs = {
	[key: string]: {
		prefill: Record<string, any>;
		wf_step: string;
	};
} & {
	generation_results?: {
		messages: Array<string>;
		result_keys: Array<string>;
	};
	ss_message?: number;
};

const sendSSClientRequest = (func: SSRequestFuncTypes, form_id: string | number, data?: any) => {
	return request({
		url: `/surescripts/?func=${func}&form_id=${form_id}`,
		method: "POST",
		data: data || {},
	});
};

const handleErrorsAlert = (action: SSRequestFuncTypes, error?: any) => {
	const unknownError = "Server communication issues. Please try again and contact support if issues persist";
	const errorHeader = "Message Creation Error!";
	const errorMsg = error?.data?.response?.data?.error || unknownError;
	window.prettyError(errorHeader, errorMsg);
	console.error("Error in action: ", action, error);
};

const getWFPresets = (wfRecs: IWFRecs) => {
	delete wfRecs.generation_results;
	delete wfRecs.ss_message;
	let preset: Record<string, unknown> = {};
	let prefillArray = [];
	for (let key in wfRecs) {
		prefillArray.push({
			form: key,
			prefill: wfRecs[key].prefill,
			wf_step: wfRecs[key].wf_step,
		});
	}
	prefillArray = prefillArray.sort((a, b) => {
		return a > b ? -1 : 1;
	});
	let customStepOrder = [];
	for (let i = 0; i < prefillArray.length; i++) {
		// Make sure always objects
		let fd = prefillArray[i];
		let pft = getType(fd.prefill);
		if (pft == "object") {
			preset[fd.form] = fd.prefill;
			customStepOrder.push(fd.form);
		} else if ((pft = "array" && fd.prefill.length)) {
			if (getType(fd.prefill[0]) == "object") {
				preset[fd.form] = fd.prefill[0];
				customStepOrder.push(fd.form);
			}
		}
	}
	return {
		preset,
		customStepOrder,
	};
};

export const ERXInspectView: FC<GenericInspectComponentProps> = (props) => {
	const tabData = props.tabData;
	const form = "ss_message";

	const PAYER_ADD_MESSAGE =
		"The payer from the message is not in the system yet. You will need to first create the payer record before we are able to generate the order.";
	const PAYER_NEEDED_NO_ACCESS_MESSAGE =
		"The payer from the message is not in the system yet. Please contact your administrator to add the payer.";

	const [fd, refreshSD] = useFormData(form, tabData.id);
	const [xml, setXml] = useState<string>("");
	const [availableActions, setAvailableActions] = useState<SSActionTypes[]>([]);
	const sd: Record<string, string> = (fd.data || {}) as any;

	const openPatient = (id: string | number) => {
		window.prettyConfirm("Success", "Do you want to open patient chart", "Yes", "No", () => {
			window.App.reactNav.goTo(`/patient/${id}/snap`);
		});
	};

	const onWorkFlowSaved = (tab: TabData, wfData: Record<string, unknown>) => {
		window.prettyNotify("Saving Workflow...");
		let tabActions = props.parentProps.tabViewActions;
		wfData["ss_message"] = sd.id;
		sendSSClientRequest("save_workflow", sd.id, { wf_recs: wfData })
			.then((resp) => {
				if (resp?.data.error) {
					window.prettyError("Error", resp.data.error);
					return;
				} else {
					tabActions.closeTab(tabData.id, tab, null);
					if (resp?.data.patient_id || sd.patient_id) {
						openPatient(resp?.data.patient_id || sd.patient_id);
					}
					refreshSD();
					getActionButtons(sd.id);
					props?.parentProps?.inspectRenderComponentProps?.getFilterRef()?.reCount?.();
				}
			})
			.catch((error) => {
				handleErrorsAlert("save_workflow", error);
			})
			.finally(() => {
				window.prettyNotify();
			});
	};

	const createOrder = (sd: IFormData, preData: Record<string, string> | null = null) => {
		if (preData) {
			if (preData.error) {
				window.prettyError("Error", preData.error);
				return;
			}
			if ("payer" in preData || "view_ss_order_generator" in preData) {
				if ("view_ss_order_generator" in preData) {
					preData.view_ss_order_generator.filter_auto_name = preData.view_ss_order_generator.filter;
					if (!preData.view_ss_order_generator.event) {
						preData.view_ss_order_generator.event = preData.view_ss_order_generator.filter;
					}
				}
				if ("payer" in preData && window.Auth.can_create("payer")) {
					window.prettyAlert("Payer Not Found.", PAYER_ADD_MESSAGE);
				} else if (!window.Auth.can_create("payer")) {
					window.prettyAlert("Payer Not Found.", PAYER_NEEDED_NO_ACCESS_MESSAGE);
					return;
				}
				const dfd = window.Flyout.open({
					form: Object.keys(preData)[0],
					mode: "add",
					preset: preData[Object.keys(preData)[0]] || {},
					autoRecoverEnabled: false,
				});
				dfd.done((data: Record<string, unknown>) => {
					if ("wf_recs" in data) {
						const wfRecs = data["wf_recs"] as IWFRecs;
						const wfProps = getWFPresets(_.cloneDeep(wfRecs));
						let tabActions = props.parentProps.tabViewActions;
						const tid = sd.message_id + "_" + sd.id;

						tabActions?.closeTab(tid, { id: tid, tkey: tid } as TabData);
						const label =
							(sd.patient_name_display as string) ||
							window.joinValid([sd.patient_name_display, sd.patient_dob]) ||
							"Wizard";

						tabActions?.openTab(tid, label, "wizard", "", {
							key: uuid(),
							wizardCode: "ss_create_order",
							...wfProps,
							removeStepIfNoPreset: true,
							skipStep: false,
							noFormSubmission: true,
							onSave: onWorkFlowSaved,
						});
					} else {
						createOrder(sd, null);
					}
				});
				dfd.fail((err: unknown) => {
					if (err) console.error(err);
				});
				return;
			}
		}
		window.prettyNotify("Generating Associated Records...");
		sendSSClientRequest("create_order", sd.id)
			.then((resp) => {
				if (resp?.data.error) {
					window.prettyError("Error", resp.data.error);
					return;
				}
				createOrder(sd, resp?.data);
				return;
			})
			.catch((error) => {
				handleErrorsAlert("create_order", error);
			})
			.finally(() => {
				window.prettyNotify();
			});
	};

	const ACTION_BUTTONS: ActionButtonMap = {
		resend: {
			onClick: (sd) => {
				window.prettyNotify("Resending Message...");
				request({
					url: "/form/ss_message/",
					method: "POST",
					data: sd,
				}).catch((err) => {
					window.prettyError(
						"Auto-resending failed",
						"Please try resending message manually by reconfirming message information."
					);
					newSSMessage("resend", sd);
				});
			},
			label: "Resend",
		},
		refill: {
			onClick: (sd) => {
				window.prettyNotify("Generating Refill Request...");
				sendSSClientRequest("request_refill", sd.id)
					.then((resp) => {
						newSSMessage("refill", resp?.data.ss_message, resp?.data.focus_fields || null);
					})
					.catch((error) => {
						handleErrorsAlert("request_refill", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Refill",
		},
		followup: {
			onClick: (sd) => {
				window.prettyNotify("Generating Followup Message...");
				sendSSClientRequest("send_followup", sd.id)
					.then((resp) => {
						window.prettyAlert("", `Follow message sent successfully.`);
						getActionButtons(sd.id);
					})
					.catch((error) => {
						handleErrorsAlert("send_followup", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Followup",
		},
		create: {
			onClick: (sd) => {
				createOrder(sd, null);
			},
			label: "Create Referral",
		},
		pa: {
			onClick: (sd) => {
				window.prettyNotify("Generating PA Request...");
				sendSSClientRequest("request_pa", sd.id)
					.then((resp) => {
						newSSMessage("pa", resp?.data.ss_message);
					})
					.catch((error) => {
						handleErrorsAlert("request_pa", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Request PA",
		},
		clarification: {
			onClick: (sd) => {
				window.prettyNotify("Generating Clarification Message...");
				sendSSClientRequest("request_clarification", sd.id)
					.then((resp) => {
						newSSMessage("clarification", resp?.data.ss_message);
					})
					.catch((error) => {
						handleErrorsAlert("request_clarification", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Request Clarification",
		},
		change: {
			onClick: (sd) => {
				window.prettyNotify("Generating Change Request Message...");
				sendSSClientRequest("request_change", sd.id)
					.then((resp) => {
						newSSMessage("change", resp?.data.ss_message);
					})
					.catch((error) => {
						handleErrorsAlert("request_change", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Request Change",
		},
		provider: {
			onClick: (sd) => {
				window.prettyNotify("Generating Information Request Message...");
				sendSSClientRequest("request_info", sd.id)
					.then((resp) => {
						newSSMessage("provider", resp?.data.ss_message);
					})
					.catch((error) => {
						handleErrorsAlert("request_info", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Request Info",
		},
		approve_cancel: {
			onClick: (sd) => {
				window.prettyNotify("Generating Cancelation Approval Message...");
				sendSSClientRequest("approve_cancel", sd.id)
					.then((resp) => {
						newSSMessage("approve_cancel", resp?.data.ss_message);
					})
					.catch((error) => {
						handleErrorsAlert("approve_cancel", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Approve Cancel",
		},
		deny_cancel: {
			onClick: (sd) => {
				window.prettyNotify("Generating Cancelation Denial Message...");
				sendSSClientRequest("deny_cancel", sd.id)
					.then((resp) => {
						newSSMessage("deny_cancel", resp?.data.ss_message);
					})
					.catch((error) => {
						handleErrorsAlert("deny_cancel", error);
					})
					.finally(() => {
						window.prettyNotify();
					});
			},
			label: "Deny Cancel",
		},
	};
	const colorizedHTMLString = () => {
		return ReactDOMServer.renderToString(<XMLViewer xml={xml} />);
	};
	const newSSMessage = (action: SSActionTypes, preset: any, focus_fields?: Array<String>) => {
		// TODO: Should highlight focus_fields and scroll to first one
		const dfd = window.Flyout.open({
			form: "ss_message",
			mode: "add",
			preset: preset,
			autoRecoverEnabled: false,
		});
		dfd.done((data: unknown) => {
			window.prettyAlert("", `Action ${ACTION_BUTTONS[action].label} completed successfully.`);
			// @ts-ignore
			props?.parentProps?.inspectRenderComponentProps?.getFilterRef()?.reCount?.();
		});
		dfd.fail((err: unknown) => {
			if (err) console.error(err);
		});
		dfd.always(() => {
			getActionButtons(tabData.id);
		});
	};

	const getWarnings = () => {
		if (!window.DSL[form]) return;
		const dsl = window.DSL[form];
		let warnings = (sd.warnings || []) as string[];
		const source = dsl.fields.warning.model.source as Record<string, string>;
		return warnings
			.map((warning: string) => source[warning] || "")
			.filter(Boolean)
			.join(", ");
	};

	const getActionButtons = (id: string | number) => {
		request({
			url: `/surescripts/?func=fetch_actions&form_id=${id}`,
			method: "GET",
		})
			.then((resp) => {
				setAvailableActions(resp?.data?.available_actions || []);
			})
			.catch((error) => {
				console.error(error);
			});
	};

	useEffect(() => {
		if (!sd.message_id) {
			return;
		}
		getActionButtons(sd.id);
		fetchFormFilters("ss_log", {
			filter: {
				message_id: sd.message_id,
			},
			sortDirection: "desc",
			sortProperty: "id",
			limit: 1,
		})
			.then((resp) => {
				if (resp.success && resp.data.length) {
					setXml(resp?.data[0].message_content_xml);
				}
			})
			.catch((error) => {
				console.error(error);
			});
	}, [fd.data]);

	const openXMLViewer = () => {
		window.Flyout.open({
			action: "FlyoutHTMLView",
			label: "XML Viewer",
			html: colorizedHTMLString(),
		});
	};

	return (
		<div className="erx-inspect">
			<div className="erx-inspect-header">
				<div className="erx-inspect-header-info">
					<div className="erx-patient">
						<div className="label-info-container">
							<div className="bold-text">
								{window.joinValid([sd.patient_name_display, sd.patient_dob])}
							</div>
						</div>
						<div className="label-info-container">
							<div className="label-text">From</div>
							<div className="bold-text">{sd.prescriber_name_display || ""}</div>
						</div>
						<div className="label-info-container">
							<div className="label-text">Drug</div>
							<div className="bold-text">{sd.description || ""}</div>
						</div>
					</div>
					<div className="erx-verified-cnt">
						<div className="erx-verified">
							{xml && (
								<div className="erx-action-btn" onClick={openXMLViewer}>
									View XML
								</div>
							)}
							<div className="erx-link-btn">{sd.message_status || ""}</div>
						</div>
					</div>
				</div>
				<div className="erx-warning">{getWarnings()}</div>
			</div>
			<div className="erx-inspect-section">
				<div className="erx-form">
					<TabbedCardView key={form + "_" + tabData.id} id={tabData.id} form={form} mode="read" />
				</div>
				<div className="erx-logo">
					<img
						src={
							sd.digital_signature_indicator == "true"
								? statusIcons.epcssigned
								: statusIcons.epcsnotsigned
						}
					/>

					{!sd.pharmacy_order_id && availableActions.includes("create") && (
						<div className="erx-link-btn-underlined" onClick={() => ACTION_BUTTONS["create"].onClick(sd)}>
							Link Order
						</div>
					)}
					{sd.pharmacy_order_id && (
						<div
							className="erx-link-btn-underlined"
							onClick={() => {
								window.Flyout.open({
									form: "careplan_order",
									mode: "read",
									record: sd.pharmacy_order_id,
									autoRecoverEnabled: false,
								});
							}}
						>
							View Order
						</div>
					)}
					<div className="erx-link-btn">{sd.prescriber_name_display || ""}</div>
					<div className="erx-link-btn">
						{window.joinValid([sd.patient_first_name, sd.patient_last_name]) + " " + (sd.patient_mrn || "")}
					</div>
				</div>
			</div>
			{availableActions?.filter((item) => item?.action !== "create")?.length > 0 && (
				<div className="erx-action-btns">
					{availableActions.map((action) => {
						if (action == "create") return null;
						return (
							<div
								key={action}
								className="erx-action-btn"
								onClick={() => ACTION_BUTTONS[action].onClick(sd)}
							>
								{ACTION_BUTTONS[action].label}
							</div>
						);
					})}
				</div>
			)}
		</div>
	);
};
