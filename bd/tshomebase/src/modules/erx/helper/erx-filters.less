@import (reference) '../../../less/style/main.less';

.erx-filters {
    @erxPrimary: #af79da;
    @erxDanger: #f15d5d;
    @erxGrey: #c9c8c7;
    @erxSeconday: #00c0d7;

    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;
    flex-wrap: wrap;
    gap: 10px;

    .erx-label {
        font-size: 14px;
        color: gray;
        font-weight: bold;
        line-height: 18px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .form-label-one;
    }

    .erx-status-options {
        display: flex;
        flex-direction: column;
        flex: 1 1 48%;
        height: 100%;
        gap: 10px;
        justify-content: space-between;


        .erx-filter-status {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            cursor: pointer;
            gap: 10px;

            .erx-status-btn {
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                color: @erxPrimary;
                background: fade(@erxPrimary, 10%);
                padding: 10px;
                gap: 10px;
                height: 36px;
                flex: 1 1 31%;
                border: 2px solid transparent;
                min-width: fit-content;

                border-radius: 10px;

                >div {
                    font-size: 24px;
                    font-weight: 800;
                }

                >p {
                    font-size: 16px;
                    font-weight: bold;
                    line-height: 18px;
                }

                &.danger {
                    color: @erxDanger;
                    background: fade(@erxDanger, 10%);
                }

                &.active {
                    border: 2px solid @erxGrey;
                }
            }
        }
    }


    .erx-select {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        min-width: fit-content;
        gap: 10px;
        flex: 1 1 16%;

        .field-select {
            >div:first-child {
                .form-input-field;
                padding: 2px 5px;
                height: 36px;
            }
        }

    }

}