import { useFormCount, useFormFilters } from "@hooks/form-data";

export const useERXCounts = (): [{
    new: number,
    replies: number,
    cancel: number
}, () => void] => {
    const [newCount, refreshNewCount] = useFormCount("ss_message", {
        filter: {
            processed: "No",
            direction: "IN",
        },
        fields: "count",
    });
    const [repliesCount, refreshRepliesCount] = useFormCount("ss_message", {
        filter: {
            processed: "No",
            direction: "IN",
            message_type: ["RxChangeResponse", "RxRenewalResponse"],
        },
        fields: "count",
    });
    const [cancelCount, refreshCancelCount] = useFormCount("ss_message", {
        filter: {
            processed: "No",
            direction: "IN",
            message_type: ["CancelRx"],
        },
        fields: "count",
    });
    const reloadCount = () => {
        refreshNewCount();
        refreshRepliesCount();
        refreshCancelCount();
    }
    return [{
        new: newCount,
        replies: repliesCount,
        cancel: cancelCount,
    }, reloadCount]
}
