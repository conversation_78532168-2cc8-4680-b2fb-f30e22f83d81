.xml-erx-popover {
    margin: 20px;
    height: 60%;
    width: 60%;
    min-height: 60%;
    min-width: 60%;
}

.erx-inspect {
    @colorTextDark: #656463;
    @colorTextLight: #8e8d8c;
    @colorTextInverted: #fbfbfb;
    @colorTextAccent: #0bc1d8;
    @colorLine: #ededee;

    display: flex;
    flex-direction: column;
    background-color: white;
    padding: 20px;
    border-radius: 0px 0px 8px 8px;
    flex: 1;
    overflow-y: auto;
    justify-content: space-between;

    .flex-row-sc {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: max-content
    }


    .erx-link-btn {
        .flex-row-sc;
        color: @colorTextAccent;
        font-weight: 800;
        width: auto;
        justify-content: center;
        align-items: center;
        text-align: center;

    }

    .erx-link-btn-underlined {
        .erx-link-btn;
        text-decoration: underline;
        cursor: pointer;
    }

    .erx-inspect-header {
        display: flex;
        flex-direction: column;

        .erx-inspect-header-info {
            .flex-row-sc;
            justify-content: space-between;
            width: 100%;
            flex-wrap: wrap;

            .erx-patient {
                .flex-row-sc;
                gap: 30px;
                flex: 1 1 70%;
                flex-wrap: wrap;

                .label-info-container {
                    .flex-row-sc;
                    gap: 10px;

                    .bold-text {
                        .flex-row-sc;
                        font-weight: 800;
                        color: @colorTextDark;
                    }

                    .label-text {
                        .flex-row-sc;
                        font-weight: bold;
                        color: @colorTextLight;
                    }
                }
            }

            .erx-verified {
                .flex-row-sc;
                flex: 1 1 30%;
                gap: 10px;

                .erx-verified {
                    .flex-row-sc;
                    flex: 1 0;
                    max-width: fit-content;

                }
            }
        }

        .erx-warning {
            .flex-row-sc;
            width: 100%;
            flex-wrap: wrap;
            color: #f15d5d;
            font-weight: bold;
            font-style: italic;
        }
    }

    .erx-inspect-section {
        .flex-row-sc;
        width: 100%;
        flex-wrap: wrap;
        min-height: 100px;
        flex: 1;
        margin-top: 10px;
        margin-bottom: 10px;

        .erx-form {
            .flex-row-sc;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex: 1;
            height: 100%;

            .cardread {
                height: 100%;
            }
        }

        .erx-logo {
            .flex-row-sc;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex: 0;
            height: 100%;
            min-width: 250px;
            margin-left: 30px;
            margin-right: 20px;
            overflow-y: auto;

            >div {
                min-height: fit-content;
            }
        }
    }

    .section-tabs {
        max-height: 31px !important;
    }

    .erx-action-btns {
        .flex-row-sc;
        width: 100%;
        flex-wrap: wrap;
        gap: 10px;
        padding: 12px;
        justify-content: center;
        background: #9974CE14;
        box-shadow: 0px 1px 4px 0px #18100A14;

        .erx-action-btn {
            border-radius: 8px;
            height: 36px;
            padding: 0px 16px;
            color: #ffff;

            &:first-child {
                background: #58505B;
            }

            &:nth-child(2) {
                background-color: #17B3CE;
            }

            &:nth-child(3) {
                background-color: #F56D63;
            }

            &:last-child {
                background-color: #58505B1F;
                color: #58505B;
            }
        }
    }

    .audit-trail {
        display: none;
    }
}