.module-erx {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    width: 100%;
    justify-content: flex-start;
    align-items: flex-end !important;

    .dsl-tab-view-container {
        width: 100%;
    }

    .dsl-list-tab-container {
        padding-top: 0px;

        .dsl-list-top {
            flex: 0 0 100px;
            justify-content: flex-start;
            align-items: center;
            padding: 15px 10px;

            .filter-act-btn {
                align-self: flex-end;
            }
        }
    }

    .tr-select {
        background-color: fade(grey, 50%) !important;

        th {
            background-color: fade(grey, 50%) !important;
        }
    }

    .tab-list-default {
        width: 100%;
    }

    .erx-grid-cnt {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
    }


    .erx-grid-order-btn,
    .erx-action-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        background: #d8d8d8;
        color: #676665;
        padding-left: 10px;
        padding-right: 10px;
        border-radius: 4px;
        font-weight: bold;
        padding: 4px;
        width: max-content;
        cursor: pointer;
    }

    .erx-row-hp {

        >td {
            font-weight: bold;
            background-color: fade(#F4999B, 30%);
        }
    }

    .erx-row-new {
        font-weight: bold;

        >td {
            background-color: fade(#BC9BD7, 30%);
        }
    }

}