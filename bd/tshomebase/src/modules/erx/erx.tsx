import _ from "lodash";
import "./erx.less";
import { CRErrorBoundary } from "@blocks/error-boundary/cr-error-boundary";
import { DSLListInspectView } from "@blocks/dsl-list-inspect-view/dsl-list-inspect-view";
import { ERXFilters } from "./helper/erx-filters";
import { useNavigation } from "@core/navigation";
import React, { FC, useEffect } from "react";
import statusIcons from "@public/icons/erx";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import { ERXInspectView } from "./helper/erx-inspect";
import { DSLTabView } from "@blocks/dsl-tab-view/dsl-tab-view";
import { DSLListViewProps } from "@blocks/dsl-list-view/dsl-list-view";
import WizardView from "@blocks/wizard/wizard";
import { getType } from "@utils/fx";
import { GirdMakeRowHTMLFunc, GridRowColumnEventFunc } from "@blocks/dsl-grid-view";

export const ERX = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/erx");
	const filterRef = React.useRef<{
		reCount?: () => void;
	}>({});
	useEffect(() => {
		if (props.navToURL?.startsWith("/erx")) {
			props.setActiveTab?.("erx");
		}
	}, [nav.globalNav.lrct]);

	const gridMakeRowHTML: GirdMakeRowHTMLFunc = (dt, selcols, fields, superFunc) => {
		dt = _.cloneDeep(dt);
		const icList: string[] = [];
		let classes = [];
		let icons: string[] = [];
		if (dt.status_icons && getType(dt.status_icons) === "array") {
			icons = (dt.status_icons || []) as string[];
		} else if (dt.status_icons_to_array && getType(dt.status_icons_to_array) === "array") {
			icons = (dt.status_icons_to_array || []) as string[];
		}
		if (icons.includes("pending")) {
			icList.push(statusIcons.pending);
		}
		if (icons.includes("sent")) {
			icList.push(statusIcons.sent);
		}
		if (icons.includes("error")) {
			icList.push(statusIcons.error);
			icList.push(statusIcons.sent);
		}
		if (icons.includes("replied")) {
			icList.push(statusIcons.replied);
		}
		if (icons.includes("new") || (dt.direction == "IN" && dt.processed == "No" && dt.message_type == "NewRx")) {
			icList.push(statusIcons.newMsg);
			classes.push("erx-row-new");
		}
		if (icons.includes("info")) {
			icList.push(statusIcons.info);
		}
		if (icons.includes("denied")) {
			icList.push(statusIcons.denied);
		}
		if (icons.includes("approved")) {
			icList.push(statusIcons.approved);
		}
		if (icons.includes("high_priority")) {
			icList.push(statusIcons.priority);
			classes.push("erx-row-hp");
		}

		if (icons.includes("high_priority") || icons.includes("canceled")) {
			classes = ["erx-row-hp"];
			if (!icList.includes(statusIcons.priority)) {
				icList.push(statusIcons.priority);
			}
		}

		let statusIconsHTML = icList
			.map((icon) => {
				return `<div><img src=${icon} /></div>`;
			})
			.join("");
		let pharmacyOrderBtn = "";
		if (dt.pharmacy_order_id) {
			pharmacyOrderBtn = `<div class="erx-grid-cnt"><div class="erx-grid-order-btn" event="erx-order-click">Open Referral</div></div>`;
		}

		const opts = {
			preRenderedColumns: {
				status_icons: `<div class="erx-grid-cnt">${statusIconsHTML}</div>`,
				pharmacy_order_id: pharmacyOrderBtn,
			},
			rowAttributes: {
				classes: classes,
			},
		};
		return superFunc(dt, selcols, fields, opts);
	};

	const gridRowColumnEvent: GridRowColumnEventFunc = (event, rowData) => {
		if (event === "erx-order-click") {
			window.Flyout.open({
				form: "careplan_order",
				mode: "read",
				record: rowData._meta.pharmacy_order_id,
				autoRecoverEnabled: false,
			});
		}
	};
	const filterRefCallBack = (ref: { reCount?: () => void }) => {
		filterRef.current = ref;
	};

	const getFilterRef = () => {
		return filterRef.current;
	};

	return (
		<div className="module-erx">
			<CRErrorBoundary>
				<DSLTabView
					CustomListView={DSLListInspectView as FC<DSLListViewProps>}
					customListViewProps={{
						form: "ss_message",
						disableRouting: true,
						canAddNew: false,
						hideFilter: true,
						noGridSearchBar: true,
						findWidgets: [
							{ component: ERXFilters, id: "description", componentProps: { filterRefCallBack } },
						],
						inspectStyles: {
							minHeight: "250px",
							height: "40%",
						},
						gridMakeRowHTML: gridMakeRowHTML,
						gridRowColumnEvent: gridRowColumnEvent,
						InspectRenderComponent: ERXInspectView,
						inspectRenderComponentProps: {
							form: "ss_message",
							mode: "edit",
							getFilterRef: getFilterRef,
						},
						inspectIDField: "id",
						inspectLabelField: "auto_name",
					}}
					styles={{
						tabListStyle: "lvl-2-tab-list",
					}}
					{...nav}
					tabLabel="Message List"
					noAddNew={true}
					form="ss_message"
					viewMode="snap"
					tabRenderComponents={{
						wizard: WizardView,
					}}
				/>
			</CRErrorBoundary>
		</div>
	);
};
