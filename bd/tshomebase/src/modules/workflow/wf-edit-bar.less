@import (reference) '../../less/style/main.less';


.wf-edit-bar {
    @border-color: #DCDCDC;
    display: flex;
    flex-direction: column;
    display: flex;
    flex: 0 0 30%;
    min-width: 550px;
    background: @white;
    border-left: 1.5px solid @border-color;

    .wf-d-topbar,
    .wf-d-bottombar {
        display: flex;
        padding: 18px;
        flex-direction: row;
        border-bottom: 1.5px solid @border-color;
        border-top: 1px solid @purple;
        justify-content: space-between;

        .wf-d-btn {
            padding: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 700;
            border: none;
            width: 140px;
            height: 35px;
            min-width: 70px;
        }

        .wf-save {
            background-color: #E69B9B;
            color: white;
        }

        .wf-new {
            background-color: #53B2DE;
            color: white;
        }
    }

    .wf-d-bottombar {
        border-top-color: @border-color;
    }

    .node-details {
        display: flex;
        flex-direction: column;
        flex: 1;
        margin: 18px;
        margin: 18px;

        .fields {
            .wf-section-label:not(:first-child) {
                padding-top: 20px;
            }

            display: flex;
            flex-direction: column;
            flex:1;
            gap: 10px;
            overflow-y: auto;

            >div {
                flex-shrink: 0;
            }
        }
    }
}

#wf-create-popover {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 10px;
    border-radius: 10px;
    .wf-popover-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        gap: 10px;
    }
    .wf-popover-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
}
