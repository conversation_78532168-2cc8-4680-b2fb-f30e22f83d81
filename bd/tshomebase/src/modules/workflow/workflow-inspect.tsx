import type { FC } from "react";
import React, { useEffect } from "react";
import "./workflow-inspect.less";
import { useFormData } from "@hooks/form-data";
import type { GenericInspectComponentProps } from "@blocks/dsl-inspect-view/dsl-inspect-view";
import { DetailPortion } from "../patient/patient-snapshot/patient-wgts";

export const WorkFlowInspectComponent: FC<GenericInspectComponentProps> = (props) => {
	const actions = props.parentProps.tabViewActions;
	const tabData = props.tabData;
	const [fd, refresh] = useFormData(tabData.form, tabData.id);
	return (
		<div className="wf-inspect-comp">
			<div className="wf-info-cards">
				<div className="wf-card">
					<div className="header">
						<>{fd?.data?.auto_name || ""}</>
					</div>
					<div className="body">
						<DetailPortion
							label="Workflow Type"
							value={fd?.data?.type || ""}
							styleClass="detail-packet-inline"
						/>
						<DetailPortion label="Code" value={fd?.data?.code || ""} styleClass="detail-packet-inline" />
					</div>
				</div>
			</div>

			<div className="wf-actions">
				<button
					className="wf-btn wf-edit"
					onClick={() => {
						actions?.openTab(
							tabData.id,
							tabData.label,
							props.rowData?.archived ? "read" : "edit",
							tabData.form,
							{}
						);
					}}
				>
					{props.rowData?.archived ? "View" : "Edit"}
				</button>
				<button
					className="wf-btn wf-design"
					onClick={() => {
						actions?.openTab(tabData.id, tabData.label, "design", tabData.form, {});
					}}
				>
					Design
				</button>
			</div>
		</div>
	);
};
