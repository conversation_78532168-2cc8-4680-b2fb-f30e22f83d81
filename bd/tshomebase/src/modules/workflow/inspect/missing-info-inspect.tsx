import React from "react";
import "./patient-inspect.less";
import { BaseInspectView, type BaseInspectViewProps } from "./base-inspect";
import { useFormData } from "@hooks/form-data";
import InitialsAvatar from "@components/common/initials-avatar";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { renderItemData, uuid } from "@utils/fx";
import icons from "@public/icons";

type MissingInfoInspectViewProps = BaseInspectViewProps;

const MissingInfoInspectView: React.FC<MissingInfoInspectViewProps> = (props) => {
    // Implement your component logic here
    const patientId = props?.rowData?.patient_id;
    const missing_forms = props?.rowData?.Missing?.split('/') || []
    const { form_name, form_id } = (props?.rowData || {}) as any;
    const [fd] = useFormData("patient", patientId);
    const patient = fd.data;
    if (patient.id) {
        let age = "";
        let gender = "";
        let dob = "";
        if (patient?.gender) {
            gender = patient.gender == "Male" ? "M" : "F";
        }
        if (patient?.dob) {
            age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
            dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
            patient["age"] = age;
        }
    }
    const goToPatient = () => {
        if (patientId) {
            window.App.reactNav.goTo(`/patient/${patientId}/snap`);
            //passing patient id to patient details page
        }
    };
    const goToRecord = (form_name: string) => {
        if (form_name && form_id) {
            window.App.reactNav.goTo(`/patient/${patientId}/${form_name}/${form_id}/edit`);
        } else if (form_name) {
            window.App.reactNav.goTo(`/patient/${patientId}/${form_name}/${uuid()}/addfill`);
        }
    };


    return (
        <BaseInspectView {...props}>
            <div className={`pat-snap-inspect${props.isPopover ? " mode-popover" : ""}`}>
                <div className="patient-details">
                    {/* <p className="section-heading">Patient Info</p> */}
                    <div className="details-section">
                        {patient && (
                            <div className="patient-top-container">
                                <div className="image-container">
                                    {(patient?.profile_image?.length > 0 && (
                                        <img className="img-profile" src={patient?.profile_image} alt="Profile" />
                                    )) || (
                                            <InitialsAvatar
                                                name={patient?.auto_name}
                                                variant={VARIANT_PRIMARY}
                                                size={SIZE_XL}
                                            />
                                        )}
                                </div>
                                <div className="info">
                                    <div className="info-header">
                                        <p className="patient-name">{patient?.auto_name}</p>
                                        <div className="age-dob">
                                            <p className="age-gender">
                                                {patient?.age}/{patient?.gender}
                                            </p>
                                            <p className="dob">{patient?.dob}</p>
                                        </div>
                                        <div className="mrn-details">
                                            <span className="mrn-field">MRN:</span>
                                            <span className="mrn-value">
                                                {patient?.mrNumber ? patient?.mrNumber : "N/A"}
                                            </span>
                                        </div>
                                    </div>
                                    <div className="patient-bottom-container">
                                        <div className="left">
                                            <PatientDetailPortion
                                                icon={icons.snap.patient.blank}
                                                label="Code Status"
                                                value={renderItemData(patient?.code_status)}
                                            />
                                            <PatientDetailPortion
                                                icon={icons.form.calenderActive}
                                                label="Referral Date"
                                                value={renderItemData(patient?.referral_date)}
                                            />
                                            <PatientDetailPortion
                                                icon={icons.common.locationOutlineActive}
                                                label="Address"
                                                value={renderItemData(patient?.address)}
                                            />
                                        </div>
                                        <div className="right">
                                            <PatientDetailPortion
                                                icon={icons.snap.patient.blank}
                                                label="Status"
                                                value={renderItemData(patient?.patient_status)}
                                            />
                                            <PatientDetailPortion
                                                icon={icons.form.emailActive}
                                                label="Email"
                                                value={renderItemData(patient?.email)}
                                            />
                                            <PatientDetailPortion
                                                icon={icons.common.phoneOutlineActive}
                                                label="Cell"
                                                value={renderItemData(patient?.phone_cell)}
                                            />
                                            <PatientDetailPortion
                                                icon={icons.common.callOutlineActive}
                                                label="Home"
                                                value={renderItemData(patient?.phone_home)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <div className="inspect-action-btns">
                    <button className="btn-dark-light" onClick={goToPatient}>
                        View Patient Chart
                    </button>

                    {missing_forms.map((form: string, index: number) => {
                        let form_name = form.includes('Careplan') ? form.replace(' ', '_').toLowerCase() : 'patient_' + form.replace(' ', '_').toLowerCase();
                        return (
                            <button key={index} className="btn-dark-light" onClick={() => goToRecord(form_name)}>
                                Create {form}
                            </button>
                        )
                    })}

                    <div className="btn-hide">
                        <button className="inspect-btn">View Orders</button>
                        <button className="inspect-btn">View Demographics</button>
                        <button className="inspect-btn">Patient Notes</button>
                        <button className="inspect-btn">New Appointment</button>
                        <button className="inspect-btn">Print / Fax / Email</button>
                    </div>
                </div>
            </div>
        </BaseInspectView>
    );
};

export default MissingInfoInspectView;
