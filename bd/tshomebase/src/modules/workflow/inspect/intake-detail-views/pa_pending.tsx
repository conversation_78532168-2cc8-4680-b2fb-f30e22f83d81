import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"


const PAPendingInfo = (props:any) => {
	const {patient_id: patientId, patient_name, "Therapy":therapy, "Patient Insurance":patient_insurance, insurance_type, "PA Status":pa_status} = (props?.rowData || {}) as any;
	const [patientData] = useFormData("patient", patientId);
	const patient = patientData?.data || {};
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
		if(!patient_name){
			patient["patient_name"] = (patient.lastname || '') + (patient.firstname ? ', ' + patient.firstname : '');
		}
	}
	if (!patient) return null;
	return (
		<div className="view-top-container-intake">
			<div className="image-container">
				{patient?.profile_image?.length > 0 ? (
					<img className="img-profile" src={patient?.profile_image} alt="Profile" />
				) : (
					<InitialsAvatar
						name={patient?.auto_name}
						variant={VARIANT_PRIMARY}
						size={SIZE_XL}
					/>
				)}
			</div>
			<div className="info">
				<div className="info-header">
					<p className="patient-name">{patient['patient_name']}</p>
					<div className="age-dob">
						<p className="age-gender">{patient?.age}/{patient?.gender}</p>
						<p className="dob">{patient?.dob}</p>
					</div>
					<div className="mrn-details">
						<span className="mrn-field">MRN:</span>
						<span className="mrn-value">{patient?.mrn || "N/A"}</span>
					</div>
				</div>
				<div className="patient-bottom-container-intake">
					<div className="left">
					<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="Therapy"
							value={renderItemData(therapy)}
						/>
						<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="Code Status"
							value={renderItemData(patient?.code_status)}
						/>
						<PatientDetailPortion
							icon={icons.form.calenderActive}
							label="Referral Date"
							value={renderItemData(patient?.referral_date)}
						/>
						<PatientDetailPortion
							icon={icons.common.locationOutlineActive}
							label="Address"
							value={renderItemData(patient?.address)}
						/>
						<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="Patient Insurance"
							value={renderItemData(patient_insurance)}
						/>

						<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="PA Status"
							value={renderItemData(pa_status)}
						/>
	
					</div>
					<div className="right">
					<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="Site"
							value={renderItemData(patient['site_id_auto_name'])}
						/>

						<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="Status"
							value={renderItemData(patient?.patient_status)}
						/>
						<PatientDetailPortion
							icon={icons.form.emailActive}
							label="Email"
							value={renderItemData(patient?.email)}
						/>
						<PatientDetailPortion
							icon={icons.common.phoneOutlineActive}
							label="Cell"
							value={renderItemData(patient?.phone_cell)}
						/>
						<PatientDetailPortion
							icon={icons.common.callOutlineActive}
							label="Home"
							value={renderItemData(patient?.phone_home)}
						/>
						<PatientDetailPortion
							icon={icons.form.detailsActive}
							label="Patient Insurance Type"
							value={renderItemData(insurance_type)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};


export default PAPendingInfo;

