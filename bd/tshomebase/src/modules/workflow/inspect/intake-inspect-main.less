@import (reference) "../../../less/style/main.less";

.inspect {
    display: flex;
    justify-content: space-between;
    width: 100%;
    flex: 40%;
    align-items: center;
    height: 100%;
    .inspect-action-btns {
        display: flex;
        flex: 1 1 40%;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
        align-items: center;
        height: fit-content;
        width: 100%;
        padding: 1px;

        .inspect-action-btn{
            padding: 10px 14px;
            border-radius: 8px;
            display: flex;
            gap: 4px;
            border: none;
            color: #fff;
            font-weight: 500;
            font-size: 14px;
            box-shadow: 
                inset 0px -1px 2px 0px #00000038, 
                inset 0px 1px 2px 0px #FFFFFF1F;

            &:hover,
            &:focus {
                box-shadow:0px 0px 0px 1px #0A0D122E inset , 0px -2px 0px 0px #0A0D120D inset,  0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #837BB2,0px 1px 2px 0px #0A0D120D;
            }

            &.act-btn-primary{
                background-color: #837BB2;
            }

            &.act-btn-secondary{
                background-color: #FAFAFA;
                color: #414651;
            }

            &.act-btn-tertiary{
                background-color: #F6F5FF;
                color: #252B37;
            }

            &:hover{
                opacity: 0.9;
            }
        }

        .btn-hide {
            display: none;
        }

        .inspect-btn {
            display: flex;
            width: 80%;
            height: 30px;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 33px;
            border-radius: 5px;
            background: #eef1f4;
            box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.1);
            color: @dark-gray;
            font-size: 14px;
            font-weight: 600;
            border: unset;
        }
    }

    &.mode-popover {
        display: flex;
        justify-content: space-between;
        width: 100%;
        flex: 1;
        align-items: center;
        height: 100%;
        flex-direction: column;


        .inspect-action-btns {
            .btn-dark-light {
                width: 100%;
            }

            .inspect-btn {
                width: 225px !important;
                padding-left: 10px;
                width: 100%;
                min-height: 24px;
                color: #254d5b;
                font-weight: 500px;
                font-size: 12px;
                gap: 10px;
            }
        }

        .inspect-cfg {
            border-left: none;
            padding: 0px;
            width: 100%;
            display: flex;
        }
    }
}

.view-record-btn {
    background-color: @dark-gray !important;
    color: @white !important;
    width: 100%;

    i {
        color: @white !important;
        font-size: 16px;
    }
}