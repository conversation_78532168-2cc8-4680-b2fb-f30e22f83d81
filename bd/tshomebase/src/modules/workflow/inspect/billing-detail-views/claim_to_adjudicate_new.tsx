import { FC, useEffect } from "react";
import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { useTabController } from "@hooks/tab-controller";
import QueryListView from "@blocks/query-list-view/query-list-view";
import SpinLoader from "@components/common/spin-loader";




const PatientView = ({props}: any) => {
	const { delivery_ticket_id, order_id, patient_id, prescriber, description,
        service_from, service_to, total_expected, cost, payers, site, delivery_date,
        patient_name, billing_method, payer, total_paid,invoice_status, claim_status,
		total_cost, total_balance_due, copay, billed_datetime, sub_query_type
	} = (props || {}) as any;
	const [patientData, refreshPatient] = useFormData("patient", patient_id);
	const patient = patientData?.data || {};

	const billingStatusMap: any = {
		'mm': 'Medical',
		'ncpdp': 'Pharmacy',
		'cms1500': 'Paper (1500)',
		'generic': 'Copay'
	}
	
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
		if(!patient_name){
			patient["patient_name"] = (patient.lastname || '') + (patient.firstname ? ', ' + patient.firstname : '');
		}
	}
	useEffect(() => {
		refreshPatient();
	}, [patient_id])

	if (!patient || !patient.id) return <SpinLoader loading={true} fontSize="2.5em" />;

	return (
	  <div className="view-top-container">
		<div className="image-container">
		  {patient?.profile_image?.length > 0 ? (
			<img className="img-profile" src={patient?.profile_image} alt="Profile" />
		  ) : (
			<InitialsAvatar
			  name={patient?.auto_name}
			  variant={VARIANT_PRIMARY}
			  size={SIZE_XL}
			/>
		  )}
		</div>
		<div className="info">
		  <div className="info-header">
			<p className="patient-name">{patient_name || patient.patient_name}</p>
			<div className="mrn-details">
			  <span className="mrn-field">DOB:</span>
			  <span className="mrn-value">{patient?.dob}</span>
			</div>
			<div className="mrn-details">
			  <span className="mrn-field">Gender:</span>
			  <span className="mrn-value">{patient?.gender}</span>
			</div>
		  </div>
		  <div className="patient-bottom-container">
			<div className="left">
			  <PatientDetailPortion
				icon={icons?.form?.personActive}
				label="Payer"
				value={renderItemData(payer)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Date/Time Billed"
				value={renderItemData(billed_datetime)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Billing Method"
				value={renderItemData(billingStatusMap[billing_method])}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Invoice Status"
				value={renderItemData(invoice_status)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Claim Status"
				value={renderItemData(claim_status)}
			  />

			</div>
			<div className="right">
			<PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Expected"
				value={renderItemData(total_expected)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Cost"
				value={renderItemData(total_cost)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Paid"
				value={renderItemData(total_paid)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Copay"
				value={renderItemData(copay)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Balance Due"
				value={renderItemData(total_balance_due)}
			  />
			</div>
		  </div>
		</div>
	  </div>
	);
  };
