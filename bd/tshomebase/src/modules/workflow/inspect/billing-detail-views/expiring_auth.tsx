import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"
import { useEffect } from "react";
import SpinLoader from "@components/common/spin-loader";

const ExpiringAuthInfo = (props:any) => {
	const {patient_id: patientId, order_id, order_name, order_no, order_status, site_name,
        order_start_date, order_received_date, order_exp_date, "PA Status": pa_status
    } = (props?.rowData || {}) as any;
	const [patientData, refreshPatient] = useFormData("patient", patientId);
	const patient = patientData?.data || {};
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
	}
    useEffect(()=>{
        refreshPatient();
    }, [patientId, order_id])
	if (!patient || !patient.id) return <SpinLoader loading={true} fontSize="2.5em" />;
	return (
		<div className="view-top-container">
			<div className="image-container">
				{patient?.profile_image?.length > 0 ? (
					<img className="img-profile" src={patient?.profile_image} alt="Profile" />
				) : (
					<InitialsAvatar
						name={patient?.auto_name}
						variant={VARIANT_PRIMARY}
						size={SIZE_XL}
					/>
				)}
			</div>
			<div className="info">
				<div className="info-header">
					<p className="patient-name">{order_name}</p>
					<div className="mrn-details">
						<span className="mrn-field">Order #:</span>
						<span className="mrn-value">{order_no}</span>
					</div>
				</div>
				<div className="patient-bottom-container">
					<div className="left">
						<PatientDetailPortion
							icon={icons.snap.patient.billingStatus}
							label="Order Status"
							value={renderItemData(order_status)}
						/>
						<PatientDetailPortion
							icon={icons.form.calenderActive}
							label="Order Start Date"
							value={renderItemData(order_start_date)}
						/>
						<PatientDetailPortion
							icon={icons.form.calenderActive}
							label="Order Receive Date"
							value={renderItemData(order_received_date)}
						/>

					</div>
					<div className="right">
                    <PatientDetailPortion
							icon={icons.snap.patient.billingStatus}
							label="PA Status"
							value={renderItemData(pa_status)}
						/>
                    <PatientDetailPortion
							icon={icons.form.calenderActive}
							label="Order Expiring"
							value={renderItemData(order_exp_date)}
						/>

						<PatientDetailPortion
							icon={icons.common.locationOutlineActive}
							label="Site"
							value={renderItemData(site_name)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};


export default ExpiringAuthInfo;

