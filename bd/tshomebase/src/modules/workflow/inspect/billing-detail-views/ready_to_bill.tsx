import { FC, useEffect } from "react";
import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { useTabController } from "@hooks/tab-controller";
import QueryListView from "@blocks/query-list-view/query-list-view";
import SpinLoader from "@components/common/spin-loader";




const PatientView = ({props}: any) => {
	const { delivery_ticket_id, order_id, patient_id, prescriber, description,
        service_from, service_to, total_expected, cost, payers, site, delivery_date,
        patient_name, billing_method, payer
	} = (props || {}) as any;
	const [patientData, refreshPatient] = useFormData("patient", patient_id);
	const patient = patientData?.data || {};
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
	}
	useEffect(() => {
		refreshPatient();
	}, [patient_id])
	if (!patient || !patient.id) return <SpinLoader loading={true} fontSize="2.5em" />;

	return (
	  <div className="view-top-container">
		<div className="image-container">
		  {patient?.profile_image?.length > 0 ? (
			<img className="img-profile" src={patient?.profile_image} alt="Profile" />
		  ) : (
			<InitialsAvatar
			  name={patient?.auto_name}
			  variant={VARIANT_PRIMARY}
			  size={SIZE_XL}
			/>
		  )}
		</div>
		<div className="info">
		  <div className="info-header">
			<p className="patient-name">{patient_name}</p>
			<div className="mrn-details">
			  <span className="mrn-field">DOB:</span>
			  <span className="mrn-value">{patient?.dob}</span>
			</div>
			<div className="mrn-details">
			  <span className="mrn-field">Gender:</span>
			  <span className="mrn-value">{patient?.gender}</span>
			</div>
		  </div>
		  <div className="patient-bottom-container">
			<div className="left">
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Service From"
				value={renderItemData(service_from)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Service To"
				value={renderItemData(service_to)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Delivery Date"
				value={renderItemData(delivery_date)}
			  />
			</div>
			<div className="right">
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Promise Date"
				value="Missing"
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Expected"
				value={total_expected}
			  />
			</div>
		  </div>
		</div>
	  </div>
	);
  };






const ReadyToBillInfo = (props: any) => {
	const actionComponent = ({ row }: { row: any }) => {
		useEffect(() => {
			const { rowData } = row || {};
	
			if (rowData && rowData.form_id) {
				const dfd = window.Flyout.open({
					form: "ledger_charge_line",
					record: rowData.form_id,
					card: "read",
					tab_can_edit_override() {
						return false;
					},
					tab_can_archive_override() {
						return false;
					},
				});
	
				dfd.done((fd) => {
					console.log("Flyout result:", fd);
				});
			}
		}, [row]);
	
		return null;
	};
	const queryListParams = `1=${props.rowData.form_id}`

    const GRAPH_TABS: Partial<TabData>[] = [
        {
            tkey: "billing_details",
            id: "billing_details",
            label: "Billing Details",
            componentProps: {
                props: props?.rowData,
            },
            renderComponent: PatientView
        },
        {
            tkey: "charge_lines_K",
            id: "charge_lines_K",
            label: "Charge Lines",
			componentProps: {
                code: "ready_to_bill_charge_lines",
                parameters: queryListParams,
				hideFilter: true,
				noGridSearchBar: true,
                ActionComponent: actionComponent,
            },
            renderComponent:QueryListView
        },
    ];

    let [openGraphTabs, activeGraphTab, controllerGraph] = useTabController(GRAPH_TABS as TabData[], GRAPH_TABS[0].tkey);

	return (
		<div className="view-top-container">
                    <div className="hz-sec charge-line-table" style={{width: "100%"}}>
                        <div className='list-bar'>
                            <TabList
                                activeTabId={activeGraphTab}
                                openTabs={openGraphTabs}
                                optionsProps={{
                                    enabled: false
                                }}
                                addProps={{
                                    enabled: false
                                }}
                                tabCanClose={false}
                                draggable={false}
                                onTabClick={(tab: TabData) => {
                                    controllerGraph.setActiveTabId(tab.tkey);
                                }}
                            />
                        </div>
                        {
                            openGraphTabs.map((tab, index) => {
                                const RenderComponent = tab.renderComponent as FC<unknown>;
                                return (
                                    <div className='qm-container' key={tab.tkey} style={activeGraphTab == tab.tkey ? undefined : { display: "none" }}>

                                        <RenderComponent {...(tab.componentProps || {})} />
                                    </div>
                                );
                            })
                        }
                    </div>
		</div>
	);
};


export default ReadyToBillInfo;

