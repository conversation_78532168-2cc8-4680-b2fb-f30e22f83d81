import { useEffect } from "react";
import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"
import SpinLoader from "@components/common/spin-loader";


const AuthorizationTrackingInfo = (props: any) => {
	const { patient_id: patientId, "Expiry Date": expiry_date, "Therapy": therapy, "Payer Phone": payer_phone, "Patient": patient_auto_name,
		"Patient Insurance": patient_insurance, "PA Status": pa_status
	} = (props?.rowData || {}) as any;
	const [patientData, refreshPatient] = useFormData("patient", patientId);
	const patient = patientData?.data || {};
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
	}
	useEffect(() => {
		refreshPatient();
	}, [patientId])
	if (!patient || !patient.id) return <SpinLoader loading={true} fontSize="2.5em" />;
	return (
		<div className="view-top-container">
			<div className="image-container">
				{patient?.profile_image?.length > 0 ? (
					<img className="img-profile" src={patient?.profile_image} alt="Profile" />
				) : (
					<InitialsAvatar
						name={patient?.auto_name}
						variant={VARIANT_PRIMARY}
						size={SIZE_XL}
					/>
				)}
			</div>
			<div className="info">
				<div className="info-header">
					<p className="patient-name">{patient_auto_name}</p>
					<div className="mrn-details">
						<span className="mrn-field">PA Status:</span>
						<span className="mrn-value">{pa_status}</span>
					</div>
				</div>
				<div className="patient-bottom-container">
					<div className="left">
						<PatientDetailPortion
							icon={icons.form.demographics}
							label="Therapy"
							value={renderItemData(therapy)}
						/>
						<PatientDetailPortion
							icon={icons.form.calenderActive}
							label="Expiry Date"
							value={renderItemData(expiry_date)}
						/>
						<PatientDetailPortion
							icon={icons.form.phoneActive}
							label="Payer Phone"
							value={renderItemData(payer_phone)}
						/>

					</div>
					<div className="right">
						<PatientDetailPortion
							icon={icons.snap.patient.prescriberPrimary}
							label="Patient Insurance"
							value={renderItemData(patient_insurance)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};


export default AuthorizationTrackingInfo;

