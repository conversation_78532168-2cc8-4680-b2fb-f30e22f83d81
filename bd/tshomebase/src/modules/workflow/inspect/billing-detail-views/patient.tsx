import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"


const PatientInfo = (props:any) => {
	const {patient_id: patientId} = (props?.rowData || {}) as any;
	const [patientData] = useFormData("patient", patientId);
	const patient = patientData?.data || {};
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
	}
	if (!patient) return null;
	return (
		<div className="view-top-container">
			<div className="image-container">
				{patient?.profile_image?.length > 0 ? (
					<img className="img-profile" src={patient?.profile_image} alt="Profile" />
				) : (
					<InitialsAvatar
						name={patient?.auto_name}
						variant={VARIANT_PRIMARY}
						size={SIZE_XL}
					/>
				)}
			</div>
			<div className="info">
				<div className="info-header">
					<p className="patient-name">{patient?.auto_name}</p>
					<div className="age-dob">
						<p className="age-gender">{patient?.age}/{patient?.gender}</p>
						<p className="dob">{patient?.dob}</p>
					</div>
					<div className="mrn-details">
						<span className="mrn-field">MRN:</span>
						<span className="mrn-value">{patient?.mrNumber || "N/A"}</span>
					</div>
				</div>
				<div className="patient-bottom-container">
					<div className="left">
						<PatientDetailPortion
							icon={icons.snap.patient.blank}
							label="Code Status"
							value={renderItemData(patient?.code_status)}
						/>
						<PatientDetailPortion
							icon={icons.form.calenderActive}
							label="Referral Date"
							value={renderItemData(patient?.referral_date)}
						/>
						<PatientDetailPortion
							icon={icons.common.locationOutlineActive}
							label="Address"
							value={renderItemData(patient?.address)}
						/>
					</div>
					<div className="right">
						<PatientDetailPortion
							icon={icons.snap.patient.blank}
							label="Status"
							value={renderItemData(patient?.patient_status)}
						/>
						<PatientDetailPortion
							icon={icons.form.emailActive}
							label="Email"
							value={renderItemData(patient?.email)}
						/>
						<PatientDetailPortion
							icon={icons.common.phoneOutlineActive}
							label="Cell"
							value={renderItemData(patient?.phone_cell)}
						/>
						<PatientDetailPortion
							icon={icons.common.callOutlineActive}
							label="Home"
							value={renderItemData(patient?.phone_home)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};


export default PatientInfo;

