import { FC, useEffect } from "react";
import InitialsAvatar from "@components/common/initials-avatar";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import icons from "@public/icons";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { renderItemData } from "@utils/fx";
import "./view-details.less"
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { useTabController } from "@hooks/tab-controller";
import QueryListView from "@blocks/query-list-view/query-list-view";
import SpinLoader from "@components/common/spin-loader";




const PatientView = ({props}: any) => {
	const { delivery_ticket_id, order_id, patient_id,
        service_from, service_to, total_expected,
        patient_name, billing_method, payer, invoice_status, total_paid, billed_datetime, claim_status,
		total_balance_due, copay, total_cost,
	} = (props || {}) as any;
	const [patientData, refreshPatient] = useFormData("patient", patient_id);
	const patient = patientData?.data || {};

	const billingStatusMap: any = {
		'mm': 'Medical',
		'ncpdp': 'Pharmacy',
		'cms1500': 'Paper (1500)',
		'generic': 'Copay'
	}
	
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
		if(!patient_name){
			patient["patient_name"] = (patient.lastname || '') + (patient.firstname ? ', ' + patient.firstname : '');
		}
	}
	useEffect(() => {
		refreshPatient();
	}, [patient_id])
	if (!patient || !patient.id) return <SpinLoader loading={true} fontSize="2.5em" />;

	return (
	  <div className="view-top-container">
		<div className="image-container">
		  {patient?.profile_image?.length > 0 ? (
			<img className="img-profile" src={patient?.profile_image} alt="Profile" />
		  ) : (
			<InitialsAvatar
			  name={patient?.auto_name}
			  variant={VARIANT_PRIMARY}
			  size={SIZE_XL}
			/>
		  )}
		</div>
		<div className="info">
		  <div className="info-header">
			<p className="patient-name">{patient_name || patient.patient_name}</p>
			<div className="mrn-details">
			  <span className="mrn-field">DOB:</span>
			  <span className="mrn-value">{patient?.dob}</span>
			</div>
			<div className="mrn-details">
			  <span className="mrn-field">Gender:</span>
			  <span className="mrn-value">{patient?.gender}</span>
			</div>
		  </div>
		  <div className="patient-bottom-container">
			<div className="left">
			  <PatientDetailPortion
				icon={icons?.form?.personActive}
				label="Payer"
				value={renderItemData(payer)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Date/Time Billed"
				value={renderItemData(billed_datetime)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Billing Method"
				value={renderItemData(billingStatusMap[billing_method])}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.calenderActive}
				label="Invoice Status"
				value={renderItemData(invoice_status)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Claim Status"
				value={renderItemData(claim_status)}
			  />

			</div>
			<div className="right">
			<PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Expected"
				value={renderItemData(total_expected)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Cost"
				value={renderItemData(total_cost)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Paid"
				value={renderItemData(total_paid)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Copay"
				value={renderItemData(copay)}
			  />
			  <PatientDetailPortion
				icon={icons?.form?.moneyActive}
				label="Total Balance Due"
				value={renderItemData(total_balance_due)}
			  />
			</div>
		  </div>
		</div>
	  </div>
	);
  };


const ClaimToAdjudicateRefillInfo = (props: any) => {

	const actionComponent = ({ row }: { row: any }) => {
		useEffect(() => {
			const { rowData } = row || {};
	
			if (rowData && rowData.form_id) {
				const dfd = window.Flyout.open({
					form: "ledger_charge_line",
					record: rowData.form_id,
					card: "read",
					tab_can_edit_override() {
						return false;
					},
					tab_can_archive_override() {
						return false;
					},
				});
	
				dfd.done((fd) => {
					console.log("Flyout result:", fd);
				});
			}
		}, [row]);
	
		return null;
	};

	const queryListParams = `1=${props.rowData.dt_id}&2=${props.rowData.billing_method}&3=${props.rowData.invoice_no}` // neeed to be careplan_delivery_tick.id
    const GRAPH_TABS: Partial<TabData>[] = [
        {
            tkey: "billing_details_refill",
            id: "billing_details_refill",
            label: "Billing Details",
            componentProps: {
                props: props?.rowData,
            },
            renderComponent: PatientView
        },
        {
            tkey: "charge_lines_refill",
            id: "charge_lines_refill",
            label: "Charge Lines",
			componentProps: {
                code: "claim_to_adjudicate",
                parameters: queryListParams,
				hideFilter: true,
				noGridSearchBar: true,
                ActionComponent: actionComponent,
            },
            renderComponent:QueryListView
        },
    ];

    let [openGraphTabs, activeGraphTab, controllerGraph] = useTabController(GRAPH_TABS as TabData[], GRAPH_TABS[0].tkey);

	return (
		<div className="view-top-container">
                    <div className="hz-sec charge-line-table" style={{width: "100%"}}>
                        <div className='list-bar'>
                            <TabList
                                activeTabId={activeGraphTab}
                                openTabs={openGraphTabs}
                                optionsProps={{
                                    enabled: false
                                }}
                                addProps={{
                                    enabled: false
                                }}
                                tabCanClose={false}
                                draggable={false}
                                onTabClick={(tab: TabData) => {
                                    controllerGraph.setActiveTabId(tab.tkey);
                                }}
                            />
                        </div>
                        {
                            openGraphTabs.map((tab, index) => {
                                const RenderComponent = tab.renderComponent as FC<unknown>;
                                return (
                                    <div className='qm-container' key={tab.tkey} style={activeGraphTab == tab.tkey ? undefined : { display: "none" }}>

                                        <RenderComponent {...(tab.componentProps || {})} />
                                    </div>
                                );
                            })
                        }
                    </div>
		</div>
	);
};


export default ClaimToAdjudicateRefillInfo;

