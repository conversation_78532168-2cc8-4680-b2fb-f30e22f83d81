
import React, { useState } from "react";
import "../patient-inspect.less";
import { BaseInspectView, type BaseInspectViewProps } from "../base-inspect";
import { useFormData } from "@hooks/form-data";
import InitialsAvatar from "@components/common/initials-avatar";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { renderItemData, toPresentableLabel, uuid } from "@utils/fx";
import icons from "@public/icons";

type CreateAssistanceInsuranceProps = BaseInspectViewProps;

const CreateAssistanceInsurance: React.FC<CreateAssistanceInsuranceProps> = (props) => {
	// Implement your component logic here
	const patientId = props?.rowData?.patient_id;
	const { form_name, pa_id:form_id, patient_assistance_status, pa_eligible, site, drug, date, therapy } = (props?.rowData || {}) as any;
	const [fd] = useFormData("patient", patientId);
	const patient = fd.data;
	if (patient.id) {
		let age = "";
		let gender = "";
		let dob = "";
		if (patient?.gender) {
			gender = patient.gender == "Male" ? "M" : "F";
		}
		if (patient?.dob) {
			age = window.moment(patient.dob, "MM/DD/YYYY").fromNow().split(" ")[0];
			dob = window.moment(patient.dob, "MM/DD/YYYY").format("MM/DD/YYYY");
			patient["age"] = age;
		}
	}
	const goToPatient = () => {
		if (patientId) {
			window.App.reactNav.goTo(`/patient/${patientId}/snap`);
			//passing patient id to patient details page
		}
	};
	const goToRecord = () => {
		if (form_name && form_id) {
			window.App.reactNav.goTo(`/patient/${patientId}/${form_name}/${form_id}/edit`);
		} else if (form_name) {
			window.App.reactNav.goTo(`/patient/${patientId}/${form_name}/${uuid()}/addfill`);
		}
	};
	let form = toPresentableLabel(form_name);
	return (
					<div className="details-section">
						{patient && (
							<div className="patient-top-container">
								<div className="image-container">
									{(patient?.profile_image?.length > 0 && (
										<img className="img-profile" src={patient?.profile_image} alt="Profile" />
									)) || (
										<InitialsAvatar
											name={patient?.auto_name}
											variant={VARIANT_PRIMARY}
											size={SIZE_XL}
										/>
									)}
								</div>
								<div className="info">
									<div className="info-header">
										<p className="patient-name">{patient?.auto_name}</p>
										<div className="age-dob">
											<p className="age-gender">
												{patient?.age}/{patient?.gender}
											</p>
											<p className="dob">{patient?.dob}</p>
										</div>
										<div className="mrn-details">
											<span className="mrn-field">MRN:</span>
											<span className="mrn-value">
												{patient?.mrNumber ? patient?.mrNumber : "N/A"}
											</span>
										</div>
									</div>
									<div className="patient-bottom-container">
										<div className="left">
											<PatientDetailPortion
												icon={icons.form.detailsActive}
												label="Assistance Status"
												value={renderItemData(patient_assistance_status)}
											/>
											<PatientDetailPortion
												icon={icons.form.detailsActive}
												label="Therapy"
												value={renderItemData(therapy)}
											/>
											<PatientDetailPortion
												icon={icons.form.detailsActive}
												label="Drug"
												value={renderItemData(drug)}
											/>
										</div>
										<div className="right">
											<PatientDetailPortion
												icon={icons.form.detailsActive}
												label="Is Patient Eligible"
												value={renderItemData(pa_eligible)}
											/>
											<PatientDetailPortion
												icon={icons.form.detailsActive}
												label="Site"
												value={renderItemData(site)}
											/>
											<PatientDetailPortion
												icon={icons.form.calenderActive}
												label="Assessment Date"
												value={renderItemData(date)}
											/>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
	);
};

export default CreateAssistanceInsurance;

