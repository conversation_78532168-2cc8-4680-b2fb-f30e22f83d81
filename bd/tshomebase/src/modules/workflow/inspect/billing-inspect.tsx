import React, { useState } from "react";
import "./billing.less";
import { BaseInspectView, type BaseInspectViewProps } from "./base-inspect";
import inspect from "@public/icons/workflow/inspect";
import { DSLListCompactView } from "@blocks/dsl-list-compact-view";

type BillingInspectViewProps = BaseInspectViewProps;

const BillingInspectView: React.FC<BillingInspectViewProps> = (props) => {
	const [selectedRow, setSelectedRow] = useState({});
	// Implement your component logic here]
	return (
		<BaseInspectView {...props}>
			<div className="billing-inspect">
				<div className="billing-inspect-top">
					<DSLListCompactView
						key={"CHANGEMEASDSAD"}
						label={"BILLING QUEUE"}
						form={"careplan_delivery_tick"}
						customColumns={window.DSL["careplan_delivery_tick"].view.grid.fields}
						buttonOverrides={{
							add: {
								enabled: false,
							},
							edit: {
								enabled: false,
							},
							delete: {
								enabled: false,
							},
						}}
						filtersPresetFixed={{}}
						sort={{
							direction: "desc",
							property: "id",
						}}
						rowClickCallback={(e) => {
							setSelectedRow(e.rowData);
						}}
					/>
					<div className="info-parent">
						<div className="info-container">
							<div className="info-row">
								<div className="info-label">Patient:</div> {selectedRow?.patient_id_auto_name}
							</div>
							<div className="info-row">
								<div className="info-label">Site:</div> {selectedRow?.site_id_auto_name}
							</div>
							<div className="info-row">
								<div className="info-label">DOS:</div> {selectedRow?.service_from}
							</div>
						</div>
						<div className="btns-container">
							<div className="btn-container">
								<div
									className="link-btn"
									onClick={() => {
										console.log("clicked");
									}}
								>
									<img src={inspect.link} />
									<span>BTN 1</span>
								</div>
							</div>
							<div className="btn-container">
								<div
									className="link-btn"
									onClick={() => {
										console.log("clicked");
									}}
								>
									<img src={inspect.link} />
									<span>BTN 2</span>
								</div>
							</div>
							<div className="btn-container">
								<div
									className="link-btn"
									onClick={() => {
										console.log("clicked");
									}}
								>
									<img src={inspect.link} />
									<span>Link to Patient</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="billing-inspect-bottom">
					<div className="amount-container">
						<div className="info-label">Expected:</div> $
						{parseFloat(selectedRow?.total_expected).toFixed(2)}
						<div className="info-label">Paid:</div> ${parseFloat(selectedRow?.total_cost).toFixed(2)}
					</div>
				</div>
			</div>
		</BaseInspectView>
	);
};

export default BillingInspectView;
