import React, { FC } from "react";
import "./intake-inspect-main.less";
import BaseInspectView, { type BaseInspectViewProps } from "./base-inspect";
type PharmacyInspectMainViewProps = BaseInspectViewProps;
import { showToastError, } from "@utils/fx";
import { request } from "@core/request";
import moment from "moment";
import { openListPopover } from "@blocks/dsl-list-view/list-popover";
import { createPortalModal } from "@blocks/portal-modal";
import { EventSelection } from "@components/popups/event-selection";
import { UserSelection } from "@components/popups/user-selection";
import { confirmDeliveryTick, startDispense } from "@modules/patient/patient-prescription/prescription-helper";
import { usePersistentWindowsStore } from "@blocks/persistent-windows/store";
import { useShallow } from "zustand/react/shallow";

type ButtonConfig = {
	label: string;
	onClick: () => void;
	disabled?: boolean;
};

const PharmacyInspectMainView: React.FC<PharmacyInspectMainViewProps> = (props) => {
	const { ["First Name"]:first_name, ["Last Name"]:last_name, patient_id, form_id, order_id, careplan_id, careplan_order_item_id, careplan_orderp_item_id, pa_id, delivery_ticket_id, item_id, physician_id, rx_id, document_id, response_id, summary_id } = (props?.rowData || {}) as any;
	const { tag } = (props?.rowData || {}) as any;

	const addWindow = usePersistentWindowsStore(useShallow((state) => state.addWindow));

	const goToPatient = () => {
		if (patient_id) {
			window.App.reactNav.goTo(`/patient/${patient_id}/snapshot`);
		}
	};

	const contactPatient = () => {
		if (delivery_ticket_id){
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {
				}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	};

	const viewPatientAccount = () => {
		if (patient_id) {
			window.App.reactNav.goTo(`/patient/${patient_id}/patient_account`);
		}
	};

	const createPrescription = () => {
		const preset = { patient_id: patient_id, careplan_id: careplan_id }
		if (order_id) {
			const dfd = window.Flyout.open({
				form: 'careplan_order',
				mode: "addfill",
				preset: {
					...preset,
					order_format: "Single Prescription",
				},
				link: "patient",
				links: [
					"patient", "careplan"
				],
				linkid: {
					"patient": patient_id,
					"careplan": careplan_id
				},
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			props.api.refreshServerSide({ purge: false });
			return
		}
	};

	const createTherapySet = () => {
		const preset = { patient_id: patient_id, careplan_id: careplan_id }
		if (order_id) {
			const dfd = window.Flyout.open({
				form: 'careplan_order',
				mode: "addfill",
				preset: {
					...preset,
					order_format: "Therapy Set",
				},
				link: "patient",
				links: [
					"patient", "careplan"
				],
				linkid: {
					"patient": patient_id,
					"careplan": careplan_id
				},
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			props.api.refreshServerSide({ purge: false });
			return
		}
	};

	const linkRx = () => {
		if (patient_id) {
			console.log("Link rx clicked!")
		}
	};

	const fillRx = () => {
		const options = {patientName: `${last_name}, ${first_name}`, addWindow:addWindow }
		startDispense(patient_id, rx_id, options)
	};

	const verifyRx = () => {
		window.prettyNotify('Verifying RX...')
		request({
			url: "/form/careplan_order_rx/" + form_id,
			method: "PUT",
			data: {
					status: "Verified",
					rx_verified: "Yes",
					verified_by: window?.UserPreference?.user_id,
					verified_datetime: moment().format('MM/DD/YYYY hh:mm A'),
					rx_denied: null,
					denied_by: null,
					denied_datetime: null
				},
		}).then((resp) => {
			window.prettyNotify()
		}).catch((error) => {
			window.prettyNotify()
			console.error(error)
		});
	};

	const editRx = () => {
		console.log(rx_id, "this is rx id")
		if (rx_id){
			const dfd = window.Flyout.open({
				form: 'careplan_order_rx',
				record: rx_id,
				card: "edit",
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		} else {
			showToastError("Not getting RX id")
			return
		}
	};

	const rejectRx = () => {
		console.log(form_id)
		window.prettyNotify('Rejecting RX...')
		request({
			url: "/form/careplan_order_rx/" + form_id,
			method: "PUT",
			data: {
					status: "Denied",
					rx_verified: null,
					verified_by: null,
					verified_datetime: null,
					rx_denied: "Yes",
					denied_by: window?.UserPreference?.user_id,
					denied_datetime: moment().format('MM/DD/YYYY hh:mm A')
				},
		}).then((resp) => {
			window.prettyNotify()
		}).catch((error) => {
			window.prettyNotify()
			console.error(error)
		});
	};

	const rejectClaim = () => {
		if (patient_id) {
			console.log("Reject Claim clicked!")
		}
	};

	const viewClaimResponse = () => {
		if (response_id){
			const dfd = window.Flyout.open({
				form: 'ncpdp_response',
				record: response_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		} else {
			showToastError("No Claim for this record")
			return
		}
	};
	const viewResponseSummary = () => {
		if (summary_id){
			const dfd = window.Flyout.open({
				form: 'ncpdp_response_summary',
				record: summary_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		} else {
			showToastError("No Claim for this record")
			return
		}
	}

	const viewFax = () => {
		let attachment = props.rowData.document_file_path;
		let documentId = props.rowData.document_id;
		console.log(props.rowData, "this is row data in fax")
		if (!documentId) {
			showToastError("No document selected");
			return;
		}
		let file = null;
		try {
			file = attachment ? JSON.parse(attachment) : {};
		} catch (e) {
			return undefined;
		}
		window.Flyout.open({
			action: "PreviewFillCardView",
			form: "careplan_order_item",
			card: "edit",
			flyoutAllowWidget: true,
			record: item_id,
			preview: {
				type: "MediaRenderer",
				file: file || {},
			},
			on_loaded:()=> {}
		});
	};

	const progressNote = () => {
		if (!patient_id) {
			showToastError("Patient Id not found!")
			return
		}
		window.Flyout.open({
			form: 'patient_note',
			mode: "addfill",
			link: "patient",
			links: [
				"patient"
			],
			linkid: {
				"patient": patient_id
			},
			on_loaded:()=> {}
		});
	};

	const moveToList = () => {	
		console.log(props.rowData, "this is props")
	}

	const createEditDt = () => {
		if (delivery_ticket_id){
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		} else {
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				mode: "addfill",
				link: "patient",
				links: [
					"patient"
				],
				linkid: {
					"patient": patient_id
				},
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const viewDt = () => {
		if (delivery_ticket_id){
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {

				}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const viewRxRecord = () => {
		if (rx_id) {
			const dfd = window.Flyout.open({
				form: 'careplan_order_rx',
				record: rx_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const printPickTick = () => {
		console.log("print pick tick clicked")
	}

	const reprintDocs = () => {
		console.log("reprint docs clicked")
	}

	const verifyOrder = () => {
		if (delivery_ticket_id) {
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				mode: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const setupDeliveryTick = () => {
		if (delivery_ticket_id) {
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				mode: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const fixTicket = () => {
		if (delivery_ticket_id){
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				card: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const confirmBatch = () => {
		if (physician_id) {
			window.App.reactNav.goTo(`/patient/${patient_id}/physician/${physician_id}/read`);
		}
	}

	const assignBiller = async () => {
		const userId = await new Promise((resolve, reject) =>
			createPortalModal(
				UserSelection as FC<unknown>,
				{},
				{ promise: { resolve, reject } }
			)
		);
		if (delivery_ticket_id && userId) {
			window.prettyNotify('Assigning User...')
			request({
				url: `/form/careplan_delivery_tick/${delivery_ticket_id}`,
				method: "PUT",
				data: {
					biller_id: userId
				}
			}).then((resp) => {
				window.prettyNotify()
				console.log(resp)
			}).catch((error) =>{
				window.prettyNotify()
				console.error(error)
			})
		}
	}

	const viewRxList = () => {
		openListPopover(
			{
				title: "RX List",
				listConfig: {
					form: "careplan_order_rx",
					filtersPresetFixed: {
						patient_id: patient_id,
						void: null,
					},
				},
			}
		);
	}

	const changeEvent = async () => {
		const item_id = careplan_order_item_id || careplan_orderp_item_id;
		const form_name = careplan_order_item_id ? "careplan_order_item" : careplan_orderp_item_id ? "careplan_orderp_item" : null;

		const eventId = await new Promise((resolve, reject) =>
			createPortalModal(
				EventSelection as FC<unknown>,
				{},
				{ promise: { resolve, reject } }
			)
		);
		if (eventId && form_name && item_id) {
			window.prettyNotify('Assigning Event...')
			request({
				url: `/form/${form_name}/${item_id}`,
				method: "PUT",
				data: {
					last_event_id: eventId
				}
			}).then((resp) => {
				window.prettyNotify()
				console.log(resp)
			}).catch((error) => {
				window.prettyNotify()
				console.error(error)
			})
		}
	}

	const forceRefreshDetails = () => {
		if (props.api && props.node) {
			props.refreshDetailsView()
		} else {
		  showToastError("Unable to expand row - missing grid API or node reference");
		}
	  };

	const confirmTicket = () => {
		if (!delivery_ticket_id) {
			showToastError("No delivery ticket id found")
			return
		}

		const conDT = () => {
			confirmDeliveryTick(delivery_ticket_id).then(() => {
				props.api.refreshServerSide({ purge: false })
			}).catch((error) => {
				window.prettyNotify()
				forceRefreshDetails()
				console.error(error)
			})
		}
		window.prettyConfirm(
			"Confirm Delivery Ticket",
			"Do you want to confirm this delivery ticket?",
			"Confirm",
			"Close",
			conDT,
			() => {},
			window.BootstrapDialog.TYPE_SUCCESS
		);

	}

	const patientInteraction = async() => {
		try {
			if (!patient_id) {
				showToastError("Patient Id not found!")
				return
			}
			window.prettyNotify("Checking for patient interaction...")
			const interaction = await request({
				url: `/form/interactions?filter=patient_id:${patient_id}&sort=-created_on`,
				method: "GET",
			})
			window.prettyNotify()
			if (!interaction.data.length) {
				showToastError("No patient interaction found!")
				return
			}
			window.Flyout.open({
				form: 'interactions',
				record: interaction.data[0].id,
				card: "read",
			});
		} catch (error) {
			window.prettyNotify()
			showToastError("Unable to check patient interaction")
			console.error(error)
		}
	}

	const actionButtons = {
		patient: { label: "View Patient Chart", onClick: goToPatient },
		contact_patient: { label: "Contact Patient", onClick: contactPatient },
		patient_account: { label: "View Patient Account", onClick: viewPatientAccount },
		create_prescription: { label: "Create Prescription", onClick: createPrescription },
		create_therapy_set: { label: "Create Therapy Set", onClick: createTherapySet },
		view_rx: { label: "View RX", onClick: viewRxRecord },
		view_rx_list: { label: "View RX List", onClick: viewRxList },
		link_rx: { label: "Link RX", onClick: linkRx },
		fill_rx: { label: "Fill RX", onClick: fillRx },
		edit_rx: { label: "Edit RX", onClick: editRx },
		reject_claim: { label: "Reject Claim", onClick: rejectClaim },
		view_claim: { label: "View Claim Response", onClick: viewClaimResponse },
		view_claim_summary: { label: "View Response Summary", onClick: viewResponseSummary },
		view_fax: { label: "View Fax", onClick: viewFax },
		progress_note: { label: "Progress Notes", onClick: progressNote },
		change_event: { label: "Change Event", onClick: changeEvent },
		create_edit_dt: { label: delivery_ticket_id ? "Edit DT" : "Create DT", onClick: createEditDt },
		view_dt: { label: "View Delivery Ticket", onClick: viewDt },
		confirm_ticket: { label: "Confirm Ticket", onClick: confirmTicket },
		fix_ticket: { label: "Fix Ticket", onClick: fixTicket },
		// confirm_batch: { label: "Confirm Batch", onClick: confirmBatch },
		assign_biller: { label: "Assign Biller/Collector", onClick: assignBiller },
		move_to_list: { label: "Move To List", onClick: moveToList },
		print_pick_tick: { label: "Print Pick Ticket", onClick: printPickTick },
		setup_dt: { label: "Setup DT", onClick: setupDeliveryTick },
		reprint_docs: { label: "Re-Print Documents", onClick: reprintDocs },
		verify_order: { label: "Verify Order", onClick: verifyOrder },
		patient_interaction: { label: "Check Patient Interaction", onClick: patientInteraction },
	};

	const buttonConfig: Record<string,
		Array<{ key: keyof typeof actionButtons; className: string; req_id: any }>
	> = {
		order_prescription: [
			{ key: "create_prescription", className: "act-btn-primary", req_id: "" },
			{ key: "create_therapy_set", className: "act-btn-primary", req_id: "" },
			// { key: "link_rx", className: "act-btn-secondary", req_id: "" },
			{ key: "patient", className: "act-btn-secondary", req_id: patient_id },
			{ key: "view_fax", className: "act-btn-secondary", req_id: document_id },
			{ key: "progress_note", className: "act-btn-tertiary", req_id: "" },
			// { key: "move_to_list", className: "act-btn-tertiary", req_id: "" },
			{ key: "change_event", className: "act-btn-tertiary", req_id: item_id },
			{ key: "create_edit_dt", className: "act-btn-tertiary", req_id: "" },
		],
		rx_verification: [
			{ key: "edit_rx", className: "act-btn-secondary", req_id: rx_id },
			// { key: "reject_claim", className: "act-btn-secondary", req_id: "" },
			{ key: "view_claim", className: "act-btn-secondary", req_id: response_id },
			{ key: "view_claim_summary", className: "act-btn-secondary", req_id: summary_id },
			{ key: "patient", className: "act-btn-secondary", req_id: patient_id },
			{ key: "change_event", className: "act-btn-tertiary", req_id: item_id },
			// { key: "move_to_list", className: "act-btn-tertiary", req_id: "" },
		],
		ready_to_contact: [
			{ key: "contact_patient", className: "act-btn-primary", req_id: delivery_ticket_id },
			{ key: "edit_rx", className: "act-btn-secondary", req_id: rx_id },
			{ key: "view_rx_list", className: "act-btn-secondary", req_id: patient_id },
			{ key: "view_fax", className: "act-btn-secondary", req_id: "" },
			{ key: "progress_note", className: "act-btn-secondary", req_id: "" },
			{ key: "change_event", className: "act-btn-tertiary", req_id: item_id },
			{ key: "patient", className: "act-btn-tertiary", req_id: patient_id },
			{ key: "patient_account", className: "act-btn-tertiary", req_id: "" },
			// { key: "move_to_list", className: "act-btn-tertiary", req_id: "" },
		],
		ready_to_print: [
			{ key: "fill_rx", className: "act-btn-primary", req_id: rx_id },
			// { key: "print_pick_tick", className: "act-btn-primary", req_id: "" },
			{ key: "setup_dt", className: "act-btn-primary", req_id: delivery_ticket_id },
			{ key: "view_rx", className: "act-btn-secondary", req_id: rx_id },
			// { key: "reprint_docs", className: "act-btn-secondary", req_id: "" },
			{ key: "view_fax", className: "act-btn-secondary", req_id: "" },
			{ key: "view_rx_list", className: "act-btn-secondary", req_id: patient_id },
			{ key: "progress_note", className: "act-btn-secondary", req_id: "" },
			{ key: "patient", className: "act-btn-tertiary", req_id: patient_id },
			// { key: "move_to_list", className: "act-btn-tertiary", req_id: "" },
		],
		order_verification: [
			{ key: "verify_order", className: "act-btn-primary", req_id: delivery_ticket_id },
			{ key: "view_dt", className: "act-btn-secondary", req_id: delivery_ticket_id },
			// { key: "reprint_docs", className: "act-btn-secondary", req_id: "" },
			{ key: "view_fax", className: "act-btn-secondary", req_id: document_id },
			{ key: "view_rx_list", className: "act-btn-secondary", req_id: patient_id },
			{ key: "progress_note", className: "act-btn-secondary", req_id: "" },
			{ key: "patient", className: "act-btn-tertiary", req_id: patient_id },
			{ key: "patient_interaction", className: "act-btn-tertiary", req_id: patient_id },
		],
		confirm_delivery_tick: [
			{ key: "confirm_ticket", className: "act-btn-primary", req_id: delivery_ticket_id },
			{ key: "fix_ticket", className: "act-btn-primary", req_id: delivery_ticket_id },
			// { key: "confirm_batch", className: "act-btn-primary", req_id: "" },
			{ key: "view_dt", className: "act-btn-secondary", req_id: delivery_ticket_id },
			// { key: "reprint_docs", className: "act-btn-secondary", req_id: "" },
			{ key: "assign_biller", className: "act-btn-secondary", req_id: delivery_ticket_id },
			{ key: "progress_note", className: "act-btn-secondary", req_id: "" },
			{ key: "patient", className: "act-btn-tertiary", req_id: patient_id },
		],
		default: [
			{ key: "patient", className: "act-btn-primary", req_id: patient_id },
		],
	};

	const buttonsToShow = buttonConfig[tag?.split(":")[0]] || buttonConfig.default;

	// filer buttons if needed
	const filteredButtons = buttonsToShow.filter((key) => {
		return true;
	});


	return (
		<>
			<BaseInspectView {...props}>
				<div className={`inspect${props.isPopover ? " mode-popover" : ""}`}>
					<div className="inspect-action-btns">
						{filteredButtons.map(({ key, className, req_id }) => {
							const button = actionButtons[key];
							return (
								<button
									className={`inspect-action-btn ${className} ${req_id === null || req_id === undefined ? "hide" : ""}`}
									onClick={button.onClick}
									key={key}
								>
									{button.label}
								</button>
							);
						})}
					</div>
				</div>
			</BaseInspectView>
		</>
	);
};

export default PharmacyInspectMainView;
