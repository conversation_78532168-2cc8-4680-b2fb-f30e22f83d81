@import (reference) "../../../less/style/main.less";

.pat-snap-inspect {
  display: flex;
  justify-content: space-between;
  width: 100%;
  flex: 1;
  align-items: center;
  height: 100%;

  // .sales-patient-info {
  //   display: flex;
  //   flex-direction: column;
  //   gap: 20px;
  .info {
    width: 100%;
  }

  .info-header {
    display: flex;
    border-bottom: 1px solid @purple;
    padding-bottom: 16px;
    gap: 16px;
    align-items: center;

    .age-dob {
      display: flex;
      gap: 5px;

      .dob {
        font-weight: 500;
        font-size: 14px;
        color: #58505b;
        align-content: center;
      }

      .age-gender {
        font-weight: 500;
        font-size: 12px;
        color: #58505b;
        background: #f2f2f2;
        border-radius: 6px;
        padding: 3px 10px;
      }
    }

    .patient-name {
      font-size: 16px;
      font-weight: 600;
    }

    .mrn-details {
      font-weight: 600;
      font-size: var(--patient-details-font-size);

      .mrn-field {
        color: @purple;
      }

      .mrn-value {
        margin-left: 5px;
        color: @dark-gray;
      }
    }
  }

  // }
  .patient-details {
    display: flex;
    flex: 1 1 90%;
    height: 100%;
    justify-content: flex-start;
    align-items: start;
    flex-direction: column;
    padding: 24px;

    .section-heading {
      font-weight: 600;
      line-height: 24px;
      font-size: var(--patient-details-header-font-size-1);
      color: @charcoal-gray;
    }

    .details-section {
      // border-bottom: 1px solid @purple;
      gap: 10px;
      width: 100%;
    }

    .patient-top-container {
      display: flex;
      justify-content: flex-start;
      align-items: start;
      flex-direction: row;
      width: 100%;
      // border-bottom: 1px solid @purple;
      // margin-bottom: 5px;
      gap: 10px;

      .image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: var(--patient-details-image-conttainer-width);
        height: var(--patient-details-image-conttainer-height);

        .img-profile {
          height: 100px;
          width: 90px;
          object-fit: contain;
        }
      }

      .patient-details-top {
        background-color: white;
        margin-left: 10px;
        margin-top: var(--patient-details-mt);
        display: flex;
        flex-direction: column;

        .patient-name-label {
          color: @dark-gray;
          font-weight: 600;
          font-size: var(--patient-name-label-font-size-temp);
        }

        .details {
          color: @dark-gray;
          font-weight: 600;
          line-height: 30px;
          font-size: var(--patient-details-font-size);
        }
      }
    }

    .patient-bottom-container {
      display: flex;
      flex-direction: row;
      margin-top: 10px;
      justify-content: center;
      gap: 10px;
      width: 100%;

      .left,
      .right {
        display: flex;
        flex-direction: column;
        width: 45%;
        gap: 10px;

        .inner-conatainer {
          width: 100%;
          display: flex;
          line-height: 25px;
          justify-content: space-between;

          .field {
            color: @purple;
            font-weight: 600;
            font-size: var(--patient-details-font-size);
          }

          .value {
            color: @dark-gray;
            font-size: var(--patient-details-font-size);
            font-weight: 500px;
          }
        }
      }
    }
  }

  .inspect-action-btns {
    display: flex;
    flex: 1 1 40%;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    align-items: center;
    height: fit-content;
    flex-direction: column;

    .btn-dark-light {
      width: 80%;
    }

    .btn-hide {
      display: none;
    }



    .inspect-btn {
      display: flex;
      width: 80%;
      height: 30px;
      padding: 10px;
      justify-content: center;
      align-items: center;
      gap: 33px;
      border-radius: 5px;
      background: #eef1f4;
      box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.1);
      color: @dark-gray;
      font-size: 14px;
      font-weight: 600;
      border: unset;
    }
  }

  &.mode-popover {
    display: flex;
    justify-content: space-between;
    width: 100%;
    flex: 1;
    align-items: center;
    height: 100%;
    flex-direction: column;


    .patient-details {
      padding-top: 0px;
      width: 100%;
      margin-left: 0px;
      color: #254d5b;
      font-weight: 600;
      padding: 0;

      p {
        font-weight: 600;
        line-height: 36px;
        font-size: 14px;
        color: #254d5b;
      }

      .patient-top-container {
        display: none;
      }

      .patient-bottom-container {
        display: none;
      }
    }

    .inspect-action-btns {
      width: 100%;
      padding: 1px;

      .btn-dark-light {
        width: 100%;
      }

      .inspect-btn {
        width: 225px !important;
        padding-left: 10px;
        width: 100%;
        min-height: 24px;
        color: #254d5b;
        font-weight: 500px;
        font-size: 12px;
        gap: 10px;
      }
    }

    .inspect-cfg {
      border-left: none;
      padding: 0px;
      width: 100%;
      display: flex;
    }
  }
}

.view-record-btn {
  background-color: @dark-gray !important;
  color: @white !important;
  width: 100%;
  img {
    filter: brightness(100) !important;
    &hover{
      filter: brightness(0.5) !important;
    }
  }
}