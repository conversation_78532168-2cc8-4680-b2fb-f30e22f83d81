@import (reference) "../../../less/style/main.less";

.delivery-ticket-inspect {
    display: flex;
    justify-content: space-between;
    width: 100%;
    flex: 40%;
    align-items: center;
    height: 100%;

    .info {
        width: 100%;
    }

    .info-header {
        display: flex;
        border-bottom: 1px solid @purple;
        padding-top: 16px;
        padding-bottom: 6px;
        justify-content: space-between; // Changed from gap: 16px
        align-items: center;

        .delivery-ticket-summary {
            font-size: 16px;
            font-weight: 600;
            font-size: var(--delivery-ticket-header-font-size-1);
            color: @charcoal-gray;
            flex: 1; // Added to allow the text to take available space
        }

        .patient-name {
            display: flex;
            margin-left: auto; // Added to push it to the right

            .patient-name-value {
                font-weight: 500;
                font-size: 12px;
                color: #58505b;
                background: #f2f2f2;
                border-radius: 6px;
                padding: 3px 10px;
            }
        }
    }

    .delivery-ticket-details {
        display: flex;
        flex: 1 1 90%;
        height: 100%;
        justify-content: flex-start;
        align-items: start;
        flex-direction: column;
        padding: 4px;

        .section-heading {
            font-weight: 600;
            line-height: 24px;
            font-size: var(--delivery-ticket-header-font-size-1);
            color: @charcoal-gray;
        }

        .details-section {
            gap: 10px;
            width: 100%;
        }

        .delivery-ticket-top-container {
            display: flex;
            justify-content: flex-start;
            align-items: start;
            flex-direction: row;
            width: 100%;
            gap: 10px;

            .image-container {
                display: flex;
                justify-content: center;
                align-items: center;
                width: var(--patient-details-image-conttainer-width);
                height: var(--patient-details-image-conttainer-height);

                .img-profile {
                    height: 100px;
                    width: 90px;
                    object-fit: contain;
                }
            }
        }

        .delivery-ticket-bottom-container {
            display: flex;
            flex-direction: row;
            margin-top: 10px;
            justify-content: center;
            gap: 10px;
            width: 100%;

            .left,
            .right {
                display: flex;
                flex-direction: column;
                width: 45%;
                gap: 10px;

                .inner-container {
                    width: 100%;
                    display: flex;
                    line-height: 25px;
                    justify-content: space-between;

                    .field {
                        color: @purple;
                        font-weight: 600;
                        font-size: var(--patient-details-font-size);
                    }

                    .value {
                        color: @dark-gray;
                        font-size: var(--patient-details-font-size);
                        font-weight: 500px;
                    }
                }
            }

            .patient-detail-packet {
                &.fw {
                    flex-basis: 50% !important;
                }
            }
        }
    }

    .inspect-action-btns {
        display: flex;
        flex: 1 1 40%;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
        align-items: center;
        height: fit-content;
        flex-direction: column;
        width: 100%;
        padding: 1px;

        .btn-dark-light {
            width: 80%;
        }

        .btn-hide {
            display: none;
        }

        .inspect-btn {
            display: flex;
            width: 80%;
            height: 30px;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 33px;
            border-radius: 5px;
            background: #eef1f4;
            box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.1);
            color: @dark-gray;
            font-size: 14px;
            font-weight: 600;
            border: unset;
        }
    }

    &.mode-popover {
        display: flex;
        justify-content: space-between;
        width: 100%;
        flex: 1;
        align-items: center;
        height: 100%;
        flex-direction: column;

        .delivery-ticket-details {
            display: none;
        }

        .patient-details {
            padding: 0px !important;
        }


        .delivery-ticket {
            padding-top: 0px;
            width: 100%;
            margin-left: 0px;
            color: #254d5b;
            font-weight: 600;

            p {
                font-weight: 600;
                line-height: 36px;
                font-size: 14px;
                color: #254d5b;
            }

            .delivery-ticket-bottom-container {
                display: none;
            }
        }

        .inspect-action-btns {
            .btn-dark-light {
                width: 100%;
            }

            .inspect-btn {
                width: 225px !important;
                padding-left: 10px;
                width: 100%;
                min-height: 24px;
                color: #254d5b;
                font-weight: 500px;
                font-size: 12px;
                gap: 10px;
            }
        }

        .inspect-cfg {
            border-left: none;
            padding: 0px;
            width: 100%;
            display: flex;
        }
    }

    .charge-lines-outer-container {
        margin: 0 -24px; // Negative margin to break out of parent padding
        padding: 16px 24px;
        background: #f8f9fa;
    }

    .charge-lines-container {
        margin-top: 16px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .charge-li-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;

        .table-header {
            background: white;

            th {
                padding: 12px 16px;
                text-align: left;
                font-size: 12px;
                font-weight: 600;
                color: #374151;
                border-bottom: 1px solid #e5e7eb;
            }
        }

        .table-row {
            &:hover {
                background-color: #f3f4f6;
            }

            &.table-row-odd {
                background-color: #f9fafb;
            }
        }

        .table-cell {
            padding: 12px 16px;
            font-size: 14px;
            color: #4b5563;
            border: none;

            // Align number columns to the right
            &:nth-child(3),
            &:nth-child(5),
            &:nth-child(6) {
                text-align: right;
            }
        }
    }
}

.view-record-btn {
    background-color: @dark-gray !important;
    color: @white !important;
    width: 100%;

    i {
        color: @white !important;
        font-size: 16px;
    }
}