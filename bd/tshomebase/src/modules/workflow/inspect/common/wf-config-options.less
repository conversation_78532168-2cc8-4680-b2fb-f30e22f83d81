@import (reference) '../../../../less/style/main.less';

.wf-config-options {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  justify-content: flex-start;
  align-items: center;
  overflow: auto;
  width: 100% !important;

  .wf-label-container {
    width: 100%;
  }

  .wf-input-grey {
    background: rgba(88, 80, 91, 0.08);
    padding: 2px 10px;
  }

  .wf-label-field-container {
    border: none;
  }

  .wf-cfg-label {
    font-size: var(--wf-cfg-label-font-size);
    font-weight: 600;
    line-height: 30px;
    color: #777;
    justify-content: flex-start;
    display: flex;
    width: 100%;
  }

  .wf-priority-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding-top: 5px;
    

    .wf-p-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    div {
      margin-left: 5px;

      p {
        height: 25px;
        width: 50px;
        border-radius: @border-radius-common-12;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        cursor: pointer;
      }

      &.priority {
        p {
          .priority-peach-purple-color !important;
          font-weight: bolder;
        }
      }
    }
  }

  &.mode-popover {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    overflow: auto;

    .wf-cfg-label {
      display: none;
    }


  }
}