import type { FC } from "react";
import React, { useEffect } from "react";
import { WFLabelContainer } from "@modules/workflow/inspect/inspect-fields/inspect-fields";
import "./wf-config-options.less";
import { useFormData } from "@hooks/form-data";
import { PriorityDesign } from "@modules/queue/helper";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { FieldSelect } from "@dsl/fields/field-select";
import { FieldDatePick } from "@dsl/fields/field-picker";
import icons from "@public/icons";

const priorityStyle: PriorityDesign = {
	1: {
		backgroundColor: "#E5E5E5",
		color: "#77797D",
	},
	2: {
		backgroundColor: "#CDCDCD",
		color: "#77797D",
	},
	3: {
		backgroundColor: "#ADADAD",
		color: "#FFFFFF",
	},
	4: {
		backgroundColor: "#FF9C55",
		color: "#FFFFFF",
	},
	5: {
		backgroundColor: "#E2655D",
		color: "#FFFFFF",
	},
};

interface WFConfigOptionsProps {
	queueId: number;
	isPopover?: boolean;
	updatePriority?: () => void;
}

export const WFConfigOptions: FC<WFConfigOptionsProps> = (props) => {
	const { queueId, isPopover, updatePriority } = props;
	const [fd, refresh, update] = useFormData("wf_queue", queueId);
	const [showAssignTask, setShowAssignTask] = React.useState<boolean>(() => {
		if (props.isPopover) {
			return false;
		}
		return true;
	});
	const toggleAssignTask = () => {
		setShowAssignTask((prev) => !prev);
	};
	const assignTaskClass = showAssignTask ? "" : "hide";
	useEffect(() => {
		refresh();
	}, [queueId]);
	const priority = Array.from({ length: 5 }, (_, i) => i + 1);
	return (
		<>
			{props.isPopover && (
				<button onClick={toggleAssignTask} className="btn-primary view-record-btn">
					Assign Task
					<img
						className="arrow"
						src={showAssignTask ? icons.common.arrowUpOutline : icons.common.arrowDownOutlineIcon}
					></img>
				</button>
			)}
			<div className={`wf-config-options${isPopover ? " mode-popover" : ""} ${assignTaskClass}`}>
				{/* <div className="wf-cfg-label">Assign Task1</div> */}

				<WFLabelContainer label="Assign To a Team">
					<FieldSelect
						form="wf_queue_team"
						multi={false}
						defaultValueSource={fd.data.assigned_team}
						defaultValueAutoName={fd.data.assigned_team_auto_name}
						customStyles={SelectConfig.inspect.style}
						onChange={(val, an) => {
							update({ assigned_team: val, assigned_team_auto_name: an }, true);
						}}
						placeholder="Select Team"
					/>
				</WFLabelContainer>
				<WFLabelContainer label="Assign To a User">
					<FieldSelect
						form="user"
						defaultValueSource={fd.data.assigned_user}
						defaultValueAutoName={fd.data.assigned_user_auto_name}
						multi={false}
						customStyles={SelectConfig.inspect.style}
						onChange={(val, an) => {
							update({ assigned_user: val, assigned_user_auto_name: an }, true);
						}}
						placeholder="Select User"
					/>
				</WFLabelContainer>

				<WFLabelContainer label="Set Status">
					<FieldSelect
						form="list_wf_queue_status"
						multi={false}
						defaultValueSource={fd.data.status}
						defaultValueAutoName={fd.data.status_auto_name}
						customStyles={SelectConfig.inspect.style}
						onChange={(val, an) => {
							update({ status: val, status_auto_name: an }, true);
						}}
						placeholder="Status"
					/>
				</WFLabelContainer>

				<WFLabelContainer label="Set Due Date">
					<FieldDatePick
						className="wf-input-grey"
						defaultValue={fd.data.due_date}
						onChange={(val) => {
							update({ due_date: val }, true);
						}}
					/>
				</WFLabelContainer>

				<WFLabelContainer label="Snooze Until">
					<FieldDatePick
						className="wf-input-grey"
						defaultValue={fd.data.snooze_until}
						onChange={(val) => {
							update({ snooze_until: val }, true);
						}}
					/>
				</WFLabelContainer>

				<div className="wf-label-container wf-priority-container ">
					<span className="wf-flabel">Priority</span>
					<div className="wf-p-container">
						<div
							className={`priority`}
							onClick={() => {
								update({ priority: 5 }, true);
								if (updatePriority) updatePriority();
							}}
						>
							<p
								style={{
									backgroundColor: "#E2655D",
									color: "#FFFFFF",
								}}
							>
								High
							</p>
						</div>
					</div>
				</div>
			</div>
		</>
	);
};
