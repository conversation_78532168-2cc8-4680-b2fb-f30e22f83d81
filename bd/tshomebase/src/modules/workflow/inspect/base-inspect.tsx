import React from "react";
import "./base-inspect.less";
import type { TabData } from "@blocks/tab-list/tab-list";
import { WFConfigOptions } from "./common/wf-config-options";
import { IFormData } from "@hooks/index";
import { WFQueueResponse } from "@modules/queue/helper";
import { GridApi, IDetailCellRendererParams } from "ag-grid-enterprise";
import { GridRowNode } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

export interface WFInspectRowData extends WFQueueResponse {}

export interface BaseInspectViewProps extends IDetailCellRendererParams {
	tabData: TabData;
	rowData: WFInspectRowData;
	refreshDetailsView: () => void;
	api: GridApi<any>;
	node: GridRowNode;
	form: string; // temporary queue form name
	queueId: string;
	className: string;
	children: React.ReactNode;
	parentProps: {
		tabID: string;
		goTo: (url: string) => void;
	};
}

export const BaseInspectView: React.FC<BaseInspectViewProps> = (props) => {
	return props.children;
};

export default BaseInspectView;
