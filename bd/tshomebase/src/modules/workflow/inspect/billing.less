.billing-inspect {
  display: flex;
  flex-direction: column;

  .billing-inspect-top {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-start;


    .info-parent {
      display: flex;
      flex-direction: column;

      .info-row {
        display: flex;
        flex-direction: row;
      }

      .info-container {
        display: flex;
        flex-direction: column;
      }

      .btn-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 0 0 200px;
        height: 100%;
        min-width: fit-content;
        border-left: 1px solid #ebeaea;

        .link-btn {
          display: flex;
          min-height: fit-content;
          min-width: fit-content;
          padding: var(--link-btn-p);
          margin: var(--link-btn-m);
          border-radius: 5px;
          gap: var(--link-btn-gap);
          text-transform: uppercase;
          background-color: #d0d4d4;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          max-width: 200px;

          >img {
            width: 18px;
            height: 14px;
          }

          span {
            font-size: var(--link-btn-font-size);
          }
        }
      }
    }
  }

  .info-label {
    font-weight: 600;
  }

  .billing-inspect-bottom {
    display: flex;
    flex-direction: row;

    .amount-container {
      display: flex;
      flex-direction: row;
    }
  }
}