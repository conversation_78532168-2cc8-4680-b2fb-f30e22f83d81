import React, { useState, useEffect } from 'react';
import "./delivery-ticket-inspect.less";
import { BaseInspectView, type BaseInspectViewProps } from "./base-inspect";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { renderItemData, formatCurrency } from "@utils/fx";
import InitialsAvatar from "@components/common/initials-avatar";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";

import { useOnScreen } from '@hooks/on-screen';

import icons from "@public/icons";
import moment from 'moment';

type ClaimExceptionInspectViewProps = BaseInspectViewProps;

const ClaimExceptionInspectView: React.FC<ClaimExceptionInspectViewProps> = (props) => {
	const {
		Insurance,
		total_expected,
		total_cost,
		patient_id: patientId,
		order_id,
		__created_on,
		billing_method,
		total_paid,
		total_balance_due,
		total_pt_pay,
		invoice_no
	} = (props?.rowData || {}) as any;
	const { form_name, form_id } = (props?.rowData || {}) as any;
	const { tag } = (props?.tabData || "") as any;
	const [patient, refreshPatient] = useFormData("patient", patientId);

	const [isOnScreen, elementRef] = useOnScreen();

	useEffect(() => {
		if (isOnScreen) {
			refreshPatient();
		}
	}, [isOnScreen])

	const goToPatient = () => {
		if (patientId) {
			window.App.reactNav.goTo(`/patient/${patientId}/snap`);
		}
	};
	const goToRecord = () => {
		if (form_name && form_id) {
			window.App.reactNav.goTo(`/patient/${patientId}/${form_name}/${form_id}/edit`);
		}
	};

	const goToPatientOrder = () => {
		if (patientId && order_id) {
			window.App.reactNav.goTo(`/patient/${patientId}/careplan_order/${order_id}/read`);
		}
	};

	const actionButtons = {
		patient: { label: "View Patient Charts", onClick: goToPatient },
		start_renewal: { label: "Start Renewal", onClick: goToRecord },
		authorization: { label: "View Authorization", onClick: goToRecord },
		accept_adjust: { label: "Accept And Adjust", onClick: goToRecord },
		view_delivery_ticket: { label: "View Delivery Ticket", onClick: goToRecord },
		view_nursing_vist: { label: "View Nursing Vist", onClick: goToRecord },
		order: { label: "View Order", onClick: goToPatientOrder },
	};

	const buttonConfig: Record<string, Array<keyof typeof actionButtons>> = {
		expiring_auths: ["patient", "start_renewal", "authorization", "order"],
		default: ["patient", "order"],
	};

	const buttonsToShow = buttonConfig[tag?.split(":")[0]] || buttonConfig.default;

	return (
		<BaseInspectView {...props}>
			<div className={`delivery-ticket-inspect${props.isPopover ? " mode-popover" : ""}`} ref={elementRef}>
				<div className="delivery-ticket-details">
					<div className="details-section">

						<div className="delivery-ticket-top-container">
							<div className="image-container">
								{(patient?.data?.profile_image?.length > 0 && (
									<img className="img-profile" src={patient?.data?.profile_image} alt="Profile" />
								)) || (
										<InitialsAvatar
											name={patient?.data?.auto_name}
											variant={VARIANT_PRIMARY}
											size={SIZE_XL}
										/>
									)}
							</div>
							<div className="info">
								<div className="info-header">
									<p className="delivery-ticket-summary">{invoice_no}</p>
									<div className="patient-name">
										<span className="patient-name-value">
											{patient?.data?.auto_name}
										</span>
									</div>
								</div>
								<div className="delivery-ticket-bottom-container">
									<div className="left">
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="First Name"
											value={renderItemData(patient?.data?.firstname)}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="DOB"
											value={renderItemData(patient?.data?.dob)}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Organization"
											value={renderItemData(Insurance)}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Billing Method"
											value={renderItemData(billing_method)}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Total Expected"
											value={renderItemData(formatCurrency(total_expected))}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Total Copay"
											value={renderItemData(formatCurrency(total_pt_pay))}
										/>

									</div>
									<div className="right">
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Last Name"
											value={renderItemData(patient?.data?.lastname)}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Gender"
											value={renderItemData(patient?.data?.gender)}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Date Time / Billed"
											value={renderItemData(moment(__created_on).format('MM/DD/YYYY H:mm:ss'))}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Total Paid"
											value={renderItemData(formatCurrency(total_paid))}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Total Cost"
											value={renderItemData(formatCurrency(total_cost))}
										/>
										<PatientDetailPortion
											icon={icons.form.cashActive}
											label="Total Balance Due"
											value={renderItemData(formatCurrency(total_balance_due))}
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className={`delivery-ticket-inspect${props.isPopover ? " mode-popover" : ""}`}>
					<div className="inspect-action-btns">
						{buttonsToShow.map((key) => {
							const button = actionButtons[key];
							return (
								<button className="btn-dark-light" onClick={button.onClick} key={key}>
									{button.label}
								</button>
							);
						})}
					</div>
				</div>
				<div className="inspect-action-btns">
					<button className="btn-dark-light" onClick={goToPatient}>
						View Patient Chart
					</button>

					<button className="btn-dark-light" onClick={goToRecord}>
						Open Delivery Ticket
					</button>
				</div>
			</div>
		</BaseInspectView>
	);
};

export default ClaimExceptionInspectView;
