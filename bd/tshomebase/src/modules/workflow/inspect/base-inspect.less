.wf-inspect-view-container {
  display: flex;
  flex-grow: 1;
  flex-shrink: 0;
  background-color: white;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 10px;
  max-height: fit-content;

  .inspect-ph {
    overflow: auto;
    display: flex;
    flex: 1;
    overflow-y: auto;
  }

  .inspect-cfg {
    display: flex;
    // flex: 1 0;
    padding: var(--inspect-cfg-p);
    // max-width: fit-content;
    width: 25%;
    border-left: 1px solid #ebeaea;
    flex-wrap: wrap;
  }
}
