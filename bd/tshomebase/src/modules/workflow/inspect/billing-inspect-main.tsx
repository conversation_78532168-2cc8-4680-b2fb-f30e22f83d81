import React, { useState } from "react";
import "./intake-inspect-main.less";
import { BaseInspectView, type BaseInspectViewProps } from "./base-inspect";
type BillingInspectMainViewProps = BaseInspectViewProps;
import { request } from "@core/request";
import moment from "moment";
import { openListPopover } from "@blocks/dsl-list-view/list-popover";
import { safeDismissToast, showToastError, showToastSuccess } from "@utils/fx";
import { ServerActionButtons } from "@blocks/dsl-card-view";
import { getFormDataWithServerActions } from "@hooks/index";
import { serverActionPerform } from "@utils/dsl-fx";
import { PatientTabActions } from "@modules/patient/patient-snapshot";

type ButtonConfig = {
	label: string;
	onClick: () => void;
	disabled?: boolean;
};

const BillingInspectMainView: React.FC<BillingInspectMainViewProps> = (props) => {
	const { order_id, patient_id, delivery_ticket_id, insurance_id, invoice_id, is_rebillable, is_reversable, can_be_adjudicated, claim_no, payer_id} = (props?.rowData || {}) as any;
	const { form_name, form_id, mode } = (props?.rowData || {}) as any;
	const { tag } = (props?.rowData || {}) as any;
	const goToPatient = () => {
		if (patient_id) {
			window.App.reactNav.goTo(`/patient/${patient_id}/snapshot`);
		}
	};

	const viewPatientAccount = () => {
		if (patient_id) {
			window.App.reactNav.goTo(`/patient/${patient_id}/patient_account`);
		}
	};

	const goToPatientOrder = () => {
		if (patient_id && order_id) {
			window.App.reactNav.goTo(`/patient/${patient_id}/careplan_order/${order_id}/read`);
		}
	};

	const viewBillingNotes = () => {
		openListPopover(
			{
				title: "Billing Notes",
				listConfig: {
					form: "patient_bill_note",
					filtersPresetFixed: {
						patient_id: patient_id,
					},
				},
			}
		);
	}

	const linkTicket  = () => {
		console.log("view billing notes clicked")
	}

	const viewTicket  = () => {
		if (delivery_ticket_id) {
			const dfd = window.Flyout.open({
				form: 'careplan_delivery_tick',
				record: delivery_ticket_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}

		console.log(props.rowData, "this is row data")
	}

	const unConfirmTicket  = () => {
		console.log("view billing notes clicked")
	}

	const voidTicket  = () => {
		console.log("view billing notes clicked")
	}

	const createInvoiceBatch  = () => {
		console.log("createInvoiceBatch clicked")
	}

	const changeStatus  = () => {
		console.log("changeStatus clicked")
	}

	const editAuthorization  = () => {
		if (form_id) {
			const dfd = window.Flyout.open({
				form: form_name,
				record: form_id,
				card: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const renewAuthorization  = () => {
		console.log("renewAuthorization clicked")
	}

	const discontinueAuthorization  = () => {
		console.log("discontinueAuthorization clicked")
	}

	const editPayerRecord  = () => {
		console.log(props.rowData,"editPayerRecord clicked")
		if (payer_id) {
			const dfd = window.Flyout.open({
				form: 'payer',
				record: payer_id,
				card: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});	
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const viewClaimResponse = () => {
		if (claim_no){
			const dfd = window.Flyout.open({
				form: 'ncpdp_response',
				record: claim_no,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const acceptClaim =()=>{
		window.prettyNotify('Accepting Claim...')
		request({
			url: "/form/billing_invoice/" + invoice_id,
			method: "PUT",
			data: {
					status: "Accepted",
					revenue_accepted: "Yes",
					accepted_by: window?.UserPreference?.user_id,
					accepted_datetime: moment().format('MM/DD/YYYY hh:mm A'),
				},
		}).then((resp) => {
			window.prettyNotify()
			showToastSuccess("Claim accepted")
			props.api.refreshServerSide({ purge: true });
		}).catch((error) => {
			window.prettyNotify()
			showToastError("There was an error while accepting the claim")
			console.error("Error while accepting the claim", error)
		});
	}

	const resubmitClaim = async ()=>{
		console.log(props.rowData, "row data here")
		console.log(props.rowData.is_rebillable , "resubmit action")
		// if (is_rebillable == 'No') return;
		const promises = []
		promises.push(getFormDataWithServerActions("billing_invoice", invoice_id));
		const actionsMap: Record<string, ServerActionButtons> = {};
		const settledPromises = await Promise.allSettled(promises);
		for (const p of settledPromises) {
			if (p?.status == "fulfilled" && p?.value?.data && p?.value?.data?.id) {
				const actions = p.value.serverActions || [];
				console.log(actions, "these are actions")
				// const action = actions.find((a) => (a.action as any) == "submit");

				// if (action) {
				// 	const fid = p.value.data.id
				// 	const actions: PatientTabActions = {
				// 		refreshSnap: (a, b) => {
				// 			props.api.refreshServerSide({ purge: false });
				// 			console.log("Server Action Completed: Refresh Prescriptions Grid");
				// 		},
				// 	} as PatientTabActions;
				// 	actionsMap[p.value.data.id] = action;
				// 	window.prettyNotify('Resubmitting Claim...')
				// 	request({
				// 		url: `/api/form/billing_invoice/${fid}?perform_action=submit`,
				// 	}).then((res) => {
				// 			window.prettyNotify()
				// 			serverActionPerform(
				// 				res.data,
				// 				"billing_invoice",
				// 				{
				// 					link: "patient",
				// 					links: ["patient"],
				// 					linkid: {"patient": patient_id},
				// 				},
				// 				null,
				// 				actions )
				// 			console.log(res, "res here")
				// 	}).catch((err) => {
				// 			window.prettyNotify()
				// 			console.error(err);
				// 	});
				// }
			}
		}
	}

	const adjudicateClaim = async () =>{
		console.log(props.rowData, "row data here")
		if (can_be_adjudicated == 'No') return;
		const promises = []
		promises.push(getFormDataWithServerActions("billing_invoice", invoice_id));
		const actionsMap: Record<string, ServerActionButtons> = {};
		const settledPromises = await Promise.allSettled(promises);
		for (const p of settledPromises) {
			if (p?.status == "fulfilled" && p?.value?.data && p?.value?.data?.id) {
				const actions = p.value.serverActions || [];
				const action = actions.find((a) => (a.action as any) == "submit");
				if (action) {
					const fid = p.value.data.id
					const actions: PatientTabActions = {
						refreshSnap: (a, b) => {
							props.api.refreshServerSide({ purge: false });
							console.log("Server Action Completed: Refresh Prescriptions Grid");
						},
					} as PatientTabActions;
					actionsMap[p.value.data.id] = action;
					window.prettyNotify('Adjudicating Claim...')
					request({
						url: `/api/form/billing_invoice/${fid}?perform_action=submit`,
					}).then((res) => {
							window.prettyNotify()
							serverActionPerform(
								res.data,
								"billing_invoice",
								{
									link: "patient",
									links: ["patient"],
									linkid: {"patient": patient_id},
								},
								null,
								actions )
							console.log(res, "res here")
					}).catch((err) => {
							window.prettyNotify()
							console.error(err);
					});
				}
			}
		}
	}

	const reverseClaim = async ()=>{
		console.log(props.rowData.is_reversable , "reverable action")
		// if (is_reversable == 'No') return;
		const promises = []
		promises.push(getFormDataWithServerActions("billing_invoice", invoice_id));
		const actionsMap: Record<string, ServerActionButtons> = {};
		const settledPromises = await Promise.allSettled(promises);
		for (const p of settledPromises) {
			if (p?.status == "fulfilled" && p?.value?.data && p?.value?.data?.id) {
				const actions = p.value.serverActions || [];
				console.log(actions, "these are actions")
				// const action = actions.find((a) => (a.action as any) == "submit");

				// if (action) {
				// 	const fid = p.value.data.id
				// 	const actions: PatientTabActions = {
				// 		refreshSnap: (a, b) => {
				// 			props.api.refreshServerSide({ purge: false });
				// 			console.log("Server Action Completed: Refresh Prescriptions Grid");
				// 		},
				// 	} as PatientTabActions;
				// 	actionsMap[p.value.data.id] = action;
				// 	window.prettyNotify('Reversing Claim...')
				// 	request({
				// 		url: `/api/form/billing_invoice/${fid}?perform_action=submit`,
				// 	}).then((res) => {
				// 			window.prettyNotify()
				// 			serverActionPerform(
				// 				res.data,
				// 				"billing_invoice",
				// 				{
				// 					link: "patient",
				// 					links: ["patient"],
				// 					linkid: {"patient": patient_id},
				// 				},
				// 				null,
				// 				actions )
				// 			console.log(res, "res here")
				// 	}).catch((err) => {
				// 			window.prettyNotify()
				// 			console.error(err);
				// 	});
				// }
			}
		}
	}

	const editClaim =()=>{
		console.log(invoice_id, "invoice claim id here")
		if (invoice_id){
			const dfd = window.Flyout.open({
				form: 'billing_invoice',
				record: invoice_id,
				card: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const viewInsuranceRecord = () => {
		if (insurance_id) {
			const dfd = window.Flyout.open({
				form: 'patient_insurance',
				record: insurance_id,
				card: "read",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const editInsuranceRecord = () => {
		if (insurance_id) {
			const dfd = window.Flyout.open({
				form: 'patient_insurance',
				record: insurance_id,
				card: "edit",
				flyoutAllowWidget: true,
				on_loaded:()=> {}
			});
			dfd.done((fd) => {
				console.log(fd)
			})
			return
		}
	}

	const actionButtons = {
		patient: { label: "View Patient Charts", onClick: goToPatient },
		patient_account: { label: "View Patient Account", onClick: viewPatientAccount },
		accept_claim: {label: "Accept Claim", onClick: acceptClaim},
		resubmit_claim: {label: "Resubmit Claim", onClick: resubmitClaim},
		adjudicate_claim: {label: "Adjudicate Claim", onClick: adjudicateClaim},
		reverse_claim: {label: "Reverse Claim", onClick: reverseClaim},
		edit_claim: {label: "Edit Claim", onClick: editClaim},
		view_insurance: {label: "View Insurance", onClick: viewInsuranceRecord},
		view_claim_response: { label: "View Claim Response", onClick: viewClaimResponse },
		view_billing_notes: { label: "View Billing Notes", onClick: viewBillingNotes },
		link_ticket: { label: "Link Ticket", onClick: linkTicket },
		view_ticket: { label: "View Ticket", onClick: viewTicket },
		un_confirm_ticket: { label: "Un-confirm Ticket", onClick: unConfirmTicket },
		void_ticket: { label: "Void Ticket", onClick: voidTicket },
		change_status: { label: "Change Status", onClick: changeStatus },
		edit_authorization: { label: "Edit Authorization", onClick: editAuthorization },
		renew_authorization: { label: "Renew Authorization", onClick: renewAuthorization },
		discontinue_authorization: { label: "Discontinue Authorization", onClick: discontinueAuthorization },
		edit_payer_record: { label: "Edit Payer Record", onClick: editPayerRecord },
		edit_insurance_record: { label: "Edit Insurance Record", onClick: editInsuranceRecord },
		create_invoice_batch: { label: "Generate Invoice", onClick: createInvoiceBatch },
		order: { label: "View Order", onClick: goToPatientOrder },
	};

	const buttonConfig: Record<string, Array<{ key: keyof typeof actionButtons; className: string; req_id: any }>> = {
		claims_to_adjudicate_mstr: [
			{ key: "accept_claim", className: "act-btn-primary", req_id: invoice_id },
			// { key: "resubmit_claim", className: "act-btn-primary", req_id: invoice_id },
			{ key: "adjudicate_claim", className: "act-btn-primary", req_id: can_be_adjudicated == 'Yes' ? invoice_id : null },
			// { key: "reverse_claim", className: "act-btn-secondary", req_id: invoice_id },
			{ key: "edit_claim", className: "act-btn-secondary", req_id: invoice_id },
			{ key: "view_claim_response", className: "act-btn-secondary", req_id: "" },
			{ key: "view_insurance", className: "act-btn-secondary", req_id: insurance_id },
			// { key: "patient_account", className: "act-btn-tertiary", req_id: "" },
			{ key: "view_billing_notes", className: "act-btn-tertiary", req_id: patient_id },
			// { key: "link_ticket", className: "act-btn-tertiary", req_id: "" },
		],
		claims_to_adjudicate: [
			{ key: "accept_claim", className: "act-btn-primary", req_id: invoice_id },
			// { key: "resubmit_claim", className: "act-btn-primary", req_id: invoice_id },
			{ key: "adjudicate_claim", className: "act-btn-primary", req_id: invoice_id },
			// { key: "reverse_claim", className: "act-btn-secondary", req_id: invoice_id },
			{ key: "edit_claim", className: "act-btn-secondary", req_id: invoice_id },
			{ key: "view_claim_response", className: "act-btn-secondary", req_id: "" },
			{ key: "view_insurance", className: "act-btn-secondary", req_id: insurance_id },
			// { key: "patient_account", className: "act-btn-tertiary", req_id: "" },
			{ key: "view_billing_notes", className: "act-btn-tertiary", req_id: patient_id },
			// { key: "link_ticket", className: "act-btn-tertiary", req_id: "" },
		],
		claims_to_adjudicate_copay: [
			{ key: "accept_claim", className: "act-btn-primary", req_id: invoice_id },
			// { key: "resubmit_claim", className: "act-btn-primary", req_id: invoice_id },
			{ key: "adjudicate_claim", className: "act-btn-primary", req_id: invoice_id },
			// { key: "reverse_claim", className: "act-btn-secondary", req_id: invoice_id },
			{ key: "edit_claim", className: "act-btn-secondary", req_id: invoice_id },
			{ key: "view_claim_response", className: "act-btn-secondary", req_id: "" },
			{ key: "view_insurance", className: "act-btn-secondary", req_id: insurance_id },
			// { key: "patient_account", className: "act-btn-tertiary", req_id: "" },
			{ key: "view_billing_notes", className: "act-btn-tertiary", req_id: patient_id },
			// { key: "link_ticket", className: "act-btn-tertiary", req_id: "" },
		],
		ready_to_bill: [
			{ key: "view_ticket", className: "act-btn-secondary", req_id: delivery_ticket_id },
			// { key: "create_invoice_batch", className: "act-btn-secondary", req_id: "" },
			// { key: "change_status", className: "act-btn-tertiary", req_id: "" },
			// { key: "patient_account", className: "act-btn-tertiary", req_id: "" },
			{ key: "patient", className: "act-btn-tertiary", req_id: patient_id },
			// { key: "un_confirm_ticket", className: "act-btn-tertiary", req_id: "" },
			// { key: "void_ticket", className: "act-btn-tertiary", req_id: "" },
			{ key: "view_billing_notes", className: "act-btn-tertiary", req_id: patient_id },
		],
		authorization_tracking: [
			{ key: "edit_authorization", className: "act-btn-primary", req_id: form_id },
			// { key: "renew_authorization", className: "act-btn-secondary", req_id: "" },
			// { key: "discontinue_authorization", className: "act-btn-secondary", req_id: ""},
			{ key: "patient", className: "act-btn-tertiary", req_id: patient_id },
			{ key: "edit_payer_record", className: "act-btn-tertiary", req_id: payer_id },
			{ key: "edit_insurance_record", className: "act-btn-tertiary", req_id: insurance_id },
			{ key: "view_billing_notes", className: "act-btn-tertiary", req_id: patient_id },
		],
		default: [
			{ key: "patient", className: "act-btn-primary", req_id: patient_id },
		],
	};

	const buttonsToShow = buttonConfig[tag?.split(":")[0]] || buttonConfig.default;
	const filteredButtons = buttonsToShow.filter((key) => {
		return true;
	});


	return (
		<>
			<BaseInspectView {...props}>
				<div className={`inspect${props.isPopover ? " mode-popover" : ""}`}>
					<div className="inspect-action-btns">
						{filteredButtons.map(({ key, className, req_id }) => {
							const button = actionButtons[key];
							return (
								<button
									className={`inspect-action-btn ${className} ${req_id === null || req_id === undefined ? "hide" : ""}`}
									onClick={button.onClick}
									key={key}
								>
									{button.label}
								</button>
							);
						})}
					</div>
				</div>
			</BaseInspectView>
		</>
	);
};

export default BillingInspectMainView;
