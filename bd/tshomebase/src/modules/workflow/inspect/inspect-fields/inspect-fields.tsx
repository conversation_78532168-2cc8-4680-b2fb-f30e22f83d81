import React from "react";
import "./inspect-fields.less";

export const WFLabelContainer: React.FC<{ label: string; noLabel?: boolean; children?: JSX.Element }> = (props) => {
	const { label, children, noLabel } = props;
	if (noLabel) return children;
	return (
		<div className="wf-label-container">
			<div className="wf-flabel">{label}</div>
			<div className="wf-label-field-container">{children}</div>
		</div>
	);
};
export interface WFPriorityPickerProps {
	label: string;
	max?: number;
	defaultValue?: any;
	onChange?: (value: any) => void;
}

export const WFPriorityPicker: React.FC<WFPriorityPickerProps> = (props) => {
	const { label, max, defaultValue, onChange } = props;
	const pl = Array.from({ length: max }, (_, i) => i + 1);
	return (
		<WFLabelContainer label={label}>
			<div className="wf-priority">
				{pl.map((p) => (
					<div
						key={p as string}
						className={`wf-p-item ${p === defaultValue ? "active" : ""}`}
						onClick={() => {
							if (onChange) {
								onChange(p);
							}
						}}
					>
						<p>{p as string}</p>
					</div>
				))}
			</div>
		</WFLabelContainer>
	);
};
