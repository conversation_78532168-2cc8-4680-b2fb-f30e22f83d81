@import (reference) "../../../../less/style/main.less";

.wf-label-container {
  display: flex;
  flex-direction: column;
  width: 160px;
  padding: 1px;

  .wf-flabel {
    .form-label-one;
  }

  .wf-label-field-container {
    display: flex;
    flex-shrink: 0;
    min-height: 40px;
    border: 1px solid #91a2a8;
    border-radius: 8px;
    flex-direction: column;
    flex-direction: column;
    justify-content: center;
  }
}

.wf-priority {
  display: flex;
  gap: 10px;
  padding-right: 5px;
  padding-left: 5px;

  .wf-p-item {
    flex: 1;
    min-height: 16px;
    min-width: 16px;
    padding: 2px;
    border-radius: 15px;
    font-size: 10px;
    text-align: center;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
    align-items: center;
    cursor: pointer;

    &.active {
      &:first-child {
        background: #f3f3f3;
        color: #77797d;
      }

      &:nth-child(2) {
        background: #e0e0e0;
        color: #77797d;
      }

      &:nth-child(3) {
        background: #c9c9c9;
        color: #77797d;
      }

      &:nth-child(4) {
        background: #ff9c55;
        color: @white;
      }

      &:last-child {
        background: #e2655d;
        color: @white;
      }
    }

    &:first-child {
      border: 1px solid #e0e0e0;
      color: #e0e0e0;
    }

    &:nth-child(2) {
      border: 1px solid #e0e0e0;
      color: #e0e0e0;
    }

    &:nth-child(3) {
      border: 1px solid #c9c9c9;
      color: #c9c9c9;
    }

    &:nth-child(4) {
      border: 1px solid #ff9c55;
      color: #ff9c55;
    }

    &:last-child {
      border: 1px solid #e2655d;
      color: #e2655d;
    }
  }
}