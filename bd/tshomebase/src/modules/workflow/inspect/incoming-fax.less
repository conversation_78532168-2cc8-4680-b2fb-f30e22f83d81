.wf-inspect-view-container.inspect-popover-content-container {
  .incoming-fax-inspect {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .fax-content {
    margin: 2px;
    padding: 0px;
    width: 100%;
    .btn-container {
      width: 100%;
    }
  }
}

.incoming-fax-inspect {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
  .fax-label {
    display: flex;
    flex-direction: row;
    gap: 10px;
  }
  .fax-content {
    display: flex;
    padding: 10px;
    flex: 1;
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    .preview {
        flex: 1;
        justify-content: center;
        display: flex;
        flex-direction: row;
        align-items: center;
        .slick-slide.slick-active {
          width: 150px;
          min-width: 150px;
        }
    }
    .btn-container {
      min-width: fit-content;
      margin: 2px;
    }
  }
}