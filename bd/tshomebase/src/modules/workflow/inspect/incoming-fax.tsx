import React, { useEffect, useState } from "react";
import "./incoming-fax.less";
import { BaseInspectView, type BaseInspectViewProps } from "./base-inspect";
import inspect from "@public/icons/workflow/inspect";
import { ThumbnailCarousel } from "@components/common/thumbnail-carousel/thumbnail-carousel";
import { CLFile } from "@typedefs/window";
import { toast } from "@components/toast";
import { handleDownloadFile, showToastSuccess } from "@utils/fx";
import { useUpdateHash } from "@hooks/update-hash";

type InComingFaxInspectViewProps = BaseInspectViewProps;

const InComingFaxInspectView: React.FC<InComingFaxInspectViewProps> = (props) => {
	const [file, setFile] = useState<CLFile>({
		filehash: "",
		mimetype: "",
		filename: "",
		filesize: 0,
		images: [],
	});
	const [files, setFiles] = useState<CLFile[]>([]);

	useEffect(() => {
		let file = null;
		try {
			file = props?.rowData?.attachment ? JSON.parse(props?.rowData.attachment) : {};
			if (file.images) {
				file.images = file.images.map((image: string, i: number): CLFile => {
					return {
						filehash: image,
						mimetype: "image/png",
						filename: `Preview Page ${i + 1}.png`,
						filesize: 0,
						images: [],
					};
				});
			}
			setFile(file);
			setFiles(file?.mimetype?.includes("pdf") ? file.images : [file]);
		} catch (e) {
			console.error("Not able to resolve file");
			return undefined;
		}
	}, []);
	const linkPatientWithIncomingFax = async () => {
		const documentId = props.rowData.id;
		if (!documentId) {
			toast({
				type: "error",
				position: "bottom-center",
				theme: "dark",
				message: "Document ID not found",
				autoClose: 5000,
			});
			return;
		}
		window.Flyout.open({
			action: "PreviewFillCardView",
			form: "document",
			card: "edit",
			record: documentId,
			preview: {
				type: "MediaRenderer",
				file: file,
			},
		});
	};
	return (
		<BaseInspectView {...props}>
			<div className="incoming-fax-inspect">
				<div className="fax-label">
					{!props.isPopover && "Fax Preview"}
					<img className="header-icon" src={inspect.print} />
					<img
						className="header-icon"
						src={inspect.download}
						onClick={() => {
							showToastSuccess("Downloading file...");
							handleDownloadFile(file);
						}}
					/>
				</div>
				<div className="fax-content">
					{!props.isPopover && (
						<div className="preview">
							<div className="pages">
								<div className="page">
									<ThumbnailCarousel
										files={files}
										config={{
											slidesToShow: files?.length <= 3 ? files.length : 3,
										}}
									/>
								</div>
							</div>
						</div>
					)}
					<div className="btn-container">
						<div className="btn-dark-light" onClick={linkPatientWithIncomingFax}>
							<img src={inspect.link} />
							<span>Link to Patient</span>
						</div>
					</div>
				</div>
			</div>
		</BaseInspectView>
	);
};

export default InComingFaxInspectView;
