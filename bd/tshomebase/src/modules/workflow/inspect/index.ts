import InComingFaxInspectView from "./incoming-fax";
import BillingInspectView from "./billing-inspect";
import BaseInspectView from "./base-inspect";
import PatientInspectView from "./patient-inspect";
import DeliveryTicketInspectView from "./delivery-ticket-inspect";
import InvoiceInspectView from "./invoice-inspect";
import MissingInfoInspectView from "./missing-info-inspect";
import BillingInspectMainView from "./billing-inspect-main";
import ClaimExceptionInspectView from "./claim-exception-inspect";
import CopayQualifyInspect from "./copay_qualify_inspect";
import IntakeInspectMainView from "./intake-inspect-main";
import PharmacyInspectMainView from "./pharmacy-inspect-main";

const INSPECT_VIEWS = {
	InComingFaxInspectView,
	BaseInspectView,
	PatientInspectView,
	BillingInspectView,
	DeliveryTicketInspectView,
	InvoiceInspectView,
	MissingInfoInspectView,
	BillingInspectMainView,
	ClaimExceptionInspectView,
	CopayQualifyInspect,
	IntakeInspectMainView,
	PharmacyInspectMainView,
};

export default INSPECT_VIEWS;

export type IInspectElement = keyof typeof INSPECT_VIEWS;

export const INSPECT_ELEMENT_COMPONENTS = [
	// {
	// 	value: "InComingFaxInspectView",
	// 	label: "In Coming Fax",
	// },
	// {
	// 	value: "BaseInspectView",
	// 	label: "Base Inspect",
	// },
	// {
	// 	value: "PatientInspectView",
	// 	label: "Patient Snap",
	// },
	// {
	// 	value: "BillingInspectView",
	// 	label: "Billing Snap",
	// },
	// {
	// 	value: "DeliveryTicketInspectView",
	// 	label: "Delivery Ticket",
	// },
	// {
	// 	value: "InvoiceInspectView",
	// 	label: "Invoice Snap",
	// },
	// {
	// 	value: "MissingInfoInspectView",
	// 	label: "Missing Info Snap",
	// },
	{
		value: "BillingInspectMainView",
		label: "Billing Inspect",
	},
	// {
	// 	value: "ClaimExceptionInspectView",
	// 	label: "Claim Exception Snap",
	// },
	// {
	// 	value: "CopayQualifyInspect",
	// 	label: "Copay Qualify",
	// },
	{
		value: "IntakeInspectMainView",
		label: "Intake Inspect",
	},
	{
		value: "PharmacyInspectMainView",
		label: "Pharmacy Inspect",
	},

];

export const QueueRowSelect = [
	{
		value: "singleRow",
		label: "Single Row Select",
	},
	{
		value: "multiRow",
		label: "Multi Row Select",
	},
];

export enum QueueRowClickActions {
	None = "none",
	Expand = "expand",
	FormEdit = "form_edit",
	FormView = "form_view",
	FunctionCall = "function_call",
}

export const ROW_CLICK_ACTIONS = [
	{
		value: QueueRowClickActions.None,
		label: "None",
	},
	{
		value: QueueRowClickActions.Expand,
		label: "Expand",
	},
	{
		value: QueueRowClickActions.FormEdit,
		label: "Form Edit",
	},
	{
		value: QueueRowClickActions.FormView,
		label: "Form View",
	},
	{
		value: QueueRowClickActions.FunctionCall,
		label: "Function Call",
	},
];
