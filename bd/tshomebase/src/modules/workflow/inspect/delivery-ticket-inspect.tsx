import React, { useState, useEffect } from 'react';
import "./delivery-ticket-inspect.less";
import { BaseInspectView, type BaseInspectViewProps } from "./base-inspect";
import { useFormData } from "@hooks/form-data";
import { PatientDetailPortion } from "@modules/patient/patient-snapshot/patient-wgts";
import { renderItemData } from "@utils/fx";
import InitialsAvatar from "@components/common/initials-avatar";
import { SIZE_XL, VARIANT_PRIMARY } from "@utils/const";
import { useOnScreen } from '@hooks/on-screen';

import icons from "@public/icons";

type DeliveryTicketInspectViewProps = BaseInspectViewProps;

const DeliveryTicketInspectView: React.FC<DeliveryTicketInspectViewProps> = (props) => {
	const {
		patient: patientName,
		description: deliveryTicketSummary,
		total_expected,
		cost,
		site,
		patient_id: patientId,
		delivery_ticket_id: deliveryTicketId,
		prescriber
	} = (props?.rowData || {}) as any;
	const { form_name, form_id } = (props?.rowData || {}) as any;
	const [deliveryTicket, refreshDeliveryTicket] = useFormData("careplan_delivery_tick", deliveryTicketId);
	const [patient, refreshPatient] = useFormData("patient", patientId);

    const [isOnScreen, elementRef] = useOnScreen();

    useEffect(() => {
        if (isOnScreen) {
            refreshDeliveryTicket();
			refreshPatient();
        }
	}, [isOnScreen])

	const goToPatient = () => {
		if (patientId) {
			window.App.reactNav.goTo(`/patient/${patientId}/snap`);
		}
	};
	const goToRecord = () => {
		if (form_name && form_id) {
			window.App.reactNav.goTo(`/patient/${patientId}/${form_name}/${form_id}/edit`);
		}
	};

	return (
		<BaseInspectView {...props}>
			<div className={`delivery-ticket-inspect${props.isPopover ? " mode-popover" : ""}`} ref={elementRef}>
				<div className="delivery-ticket-details">
					<div className="details-section">
						{deliveryTicket && (
							<div className="delivery-ticket-top-container">
								<div className="image-container">
									{(patient?.data?.profile_image?.length > 0 && (
										<img className="img-profile" src={patient?.data?.profile_image} alt="Profile" />
									)) || (
										<InitialsAvatar
											name={patient?.data?.auto_name}
											variant={VARIANT_PRIMARY}
											size={SIZE_XL}
										/>
									)}
								</div>
								<div className="info">
									<div className="info-header">
										<p className="delivery-ticket-summary">{deliveryTicketSummary}</p>
										<div className="patient-name">
											<span className="patient-name-value">
												{patientName}
											</span>
										</div>
									</div>
									<div className="delivery-ticket-bottom-container">
										<div className="left">
											<PatientDetailPortion
												icon={icons.form.pharmacyActive}
												label="Site"
												value={renderItemData(site)}
											/>
											<PatientDetailPortion
												icon={icons.form.calenderActive}
												label="Service From"
												value={renderItemData(deliveryTicket?.data?.service_from)}
											/>
											<PatientDetailPortion
												icon={icons.form.calenderActive}
												label="Shipment Status"
												value={renderItemData(deliveryTicket?.data?.subform_delivery_log?.[0]?.shipment_status_id_auto_name)}
											/>
											<PatientDetailPortion
												icon={icons.form.medicalActive}
												label="Physician"
												value={renderItemData(prescriber)}
											/>
											<PatientDetailPortion
												icon={icons.form.cashActive}
												label="Total Expected"
												value={renderItemData(total_expected)}
											/>
										</div>
										<div className="right">
											<PatientDetailPortion
												icon={icons.form.dispenseActive}
												label="Fill #"
												value={renderItemData(deliveryTicket?.data?.fill_number)}
											/>
											<PatientDetailPortion
												icon={icons.form.calenderActive}
												label="Service To"
												value={renderItemData(deliveryTicket?.data?.service_to)}
											/>
											<PatientDetailPortion
												icon={icons.form.calenderActive}
												label="Ship Date"
												value={renderItemData(deliveryTicket?.data?.subform_delivery_log?.[0]?.ship_date)}
											/>
											<PatientDetailPortion
												icon={icons.form.calenderActive}
												label="Delivered Date"
												value={renderItemData(deliveryTicket?.data?.subform_delivery_log?.[0]?.delivered_date)}
											/>
											<PatientDetailPortion
												icon={icons.form.cashActive}
												label="Cost"
												value={renderItemData(cost)}
											/>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
				<div className="inspect-action-btns">
					<button className="btn-dark-light" onClick={goToPatient}>
						View Patient Chart
					</button>

					<button className="btn-dark-light" onClick={goToRecord}>
						Open Delivery Ticket
					</button>

				</div>
			</div>
		</BaseInspectView>
	);
};

export default DeliveryTicketInspectView;
