import type { <PERSON><PERSON>, <PERSON>, EdgeChange, NodeChange } from "reactflow";
import type { DSLEditorColumn } from "./workflow-cson-builder/wf-cson-editor";
import type { DSLForm } from "@typedefs/coffee/dsl";
import { IFormData } from "@hooks/index";
import { IInspectElement, QueueRowClickActions } from "@modules/workflow/inspect";

export interface EdgeData {
	type?: string;
	[key: string]: unknown;
}

export interface CardStruct {
	header: string[];
	footer: string[];
	lines: string[][];
}

type QueueClickConfig =
	| {
			action?: QueueRowClickActions.Expand;
	  }
	| {
			action?: QueueRowClickActions.None;
	  }
	| {
			action?: QueueRowClickActions.FormEdit;
			config?: {
				id_field: string;
				form_field: string;
				form_name: string;
			};
	  }
	| {
			action?: QueueRowClickActions.FormView;
			config?: {
				id_field: string;
				form_field: string;
				form_name: string;
			};
	  }
	| {
			action?: QueueRowClickActions.FunctionCall;
			config?: {
				function_name: string;
			};
	  };

export type NodeDataType = "sqlRaw" | "sqlURL" | "wizardForm" | "wizardTable" | "queueGroup" | "none";

export type NodeData = {
	groups?: string[];
	icon?: string;
	label: string | undefined;
	code: string | undefined;
	dsl_columns?: DSLEditorColumn[];
	card_struct?: CardStruct;
	dsl_struct: DSLForm | null;
	inspect_element?: IInspectElement;
	inspect_field: string;
	inspect_label_field?: string;
	inspect_form: string;
	description?: string;
	type?: NodeDataType;
	[key: string]: unknown;
	single_row_click?: QueueClickConfig;
	double_row_click?: QueueClickConfig;
	rowSelect?: "singleRow" | "multiRow";
};

export interface WorkFlowData extends IFormData {
	name: string;
	code: string;
	type: "Queue" | "Wizard" | "Review";
	active: "Yes" | "No" | null;
	sort_order: number;
	node_order: string[];
	graph_data: {
		nodes: CRNode[];
		group_order: string[];
		edges: CREdge[];
		node_order: string[];
	};
}

export type CREdge = Edge<EdgeData>;
export type CRNode = Node<NodeData, string | undefined>;
export type CREdgeChange = EdgeChange;
export type CRNodeChange = NodeChange;

export interface GraphData {
	config: Record<string, unknown>;
	edges: CREdge[];
	nodes: CRNode[];
	node_order: string[];
}
