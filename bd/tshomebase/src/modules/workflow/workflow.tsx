import type { FC, SyntheticEvent } from "react";
import React, { useState, useEffect, useCallback, useRef } from "react";
import ReactFlow, {
	useNodesState,
	useEdgesState,
	addEdge,
	MiniMap,
	Controls,
	useReactFlow,
	ReactFlowProvider,
	Background,
} from "reactflow";
import "reactflow/dist/style.css";
import { uuid } from "@utils/fx";
import type { EdgeData, NodeData, CREdge, CRNode, GraphData, WorkFlowData, NodeDataType } from "./types";
import sqlRaw from "./nodes/sql-raw";
import sqlURL from "./nodes/sql-url";
import wizardForm from "./nodes/wizard-form";
import wizardTable from "./nodes/wizard-table";

import "./workflow.less";
import type { TabData } from "@blocks/tab-list/tab-list";
import type { TabViewActions } from "@blocks/dsl-tab-view/dsl-tab-view";
import { useFormData } from "@hooks/form-data";
import { useNavigation } from "@core/navigation";
import { WFEditBar } from "./wf-edit-bar";
import { generateDSL } from "./workflow-cson-builder/wf-cson-generator";
import { generateCardStruct } from "./workflow-cson-builder/wf-card-struct-generator";
import { RoutedComponentProps } from "@typedefs/routed-component";
import useToast from "@hooks/use-toast";
import { toast } from "react-toastify";
import QueueGroup from "@modules/workflow/nodes/queue-group";

const snapGrid = [20, 20];

const nodeTypes = {
	sqlRaw: sqlRaw,
	sqlURL: sqlURL,
	queueGroup: QueueGroup,
	wizardForm: wizardForm,
	wizardTable: wizardTable,
};

const nodeTypeColorMini = {
	stroke: {
		sqlRaw: "#ABCAD9",
		sqlURL: "#FF9C55",
		queueGroup: "#FF9C55",
		wizardForm: "#00AE8F",
		wizardTable: "#444e4c",
	},
	background: {
		sqlRaw: "#ABCAD9",
		sqlURL: "#FF9C55",
		queueGroup: "#FF9C55",
		wizardForm: "#00AE8F",
		wizardTable: "#444e4c",
	},
};

const defaultViewport = { x: 0, y: 0, zoom: 1.5 };
interface AppFlowProps extends RoutedComponentProps {
	record: string;
	card: string;
	form: string;
	tabData: TabData;
	tabViewActions: TabViewActions;
}

const updateNodeContent = (nodeID: string, k: string, v: string) => {
	$(`[data-id="${nodeID}"]`).find(`.wf-${k}`).html(v);
};

const AppFlow: FC<AppFlowProps> = (props) => {
	const { record, form } = props;
	useNavigation(props, `/${record}/design/`, null);

	const [fd, refresh, updateForm] = useFormData(form, record);
	const [wfConfig, setWFConfig] = useState({});

	const [selectedNode, setSelectedNode] = useState("");
	const [selectedEdge, setSelectedEdge] = useState("");

	const [nodes, setNodes, onNodesChange] = useNodesState<NodeData>([]);
	const [edges, setEdges, onEdgesChange] = useEdgesState<EdgeData>([]);

	const [showToast] = useToast();

	const reactFlow = useReactFlow();

	const reactFlowWrapper = useRef(null);

	useEffect(() => {
		if (fd.loading) {
			showToast(
				{
					type: "info",
					message: "Fetching Updated Workflow!",
					autoClose: 6000,
					position: "bottom-center",
					theme: "dark",
				},
				true
			);
			return;
		}
		if (fd.failed) {
			console.error("Failed Loading Workflow Data");
			showToast(
				{
					type: "error",
					message: "Unable to load Workflow",
					autoClose: 6000,
					position: "bottom-center",
					theme: "dark",
				},
				true
			);
			return;
		}

		const graphData: GraphData = fd.data?.graph_data as GraphData;
		const ed = graphData?.edges || [];
		const nd = graphData?.nodes || [];
		setNodes(nd);
		setEdges(ed);
		setWFConfig(graphData?.config || {});
		showToast(
			{
				type: "info",
				message: "Workflow Loaded!",
				autoClose: 6000,
				position: "bottom-center",
				theme: "dark",
			},
			true
		);
	}, [fd.loading, fd.failed]);

	const createNewNode = (nodeType: NodeDataType) => {
		if (!nodeType || nodeType == "none") {
			return;
		}
		const nodeID = uuid();
		setNodes([
			...nodes,
			{
				id: nodeID,
				type: nodeType,
				data: {
					type: nodeType,
				} as NodeData,
				position: { x: 0, y: 0 },
			},
		]);
		setSelectedNode(nodeID);
	};

	const duplicateNode = (originalId: string) => {
		if (!originalId) {
			return;
		}
		const nd = nodes.filter((node) => node.id == originalId);
		if (!nd.length) {
			return;
		}
		const dplNode = nd[0];
		const nodeID = uuid();
		setNodes([
			...nodes,
			{
				id: nodeID,
				type: dplNode.type,
				data: {
					...dplNode.data,
					label: "Duplicate " + (dplNode.data.label || "Node"),
					code: "",
				},
				position: { x: 0, y: 0 },
			},
		]);
		setSelectedNode(nodeID);
	};

	const clearSelection = () => {
		setSelectedNode("");
		setSelectedEdge("");
	};

	const calculateGroupMap = (nodes: CRNode[], edges: CREdge[], groupOrder: string[]) => {
		const groupMap: Record<string, string[]> = {
			Other: [],
		};
		groupOrder.forEach((group) => {
			groupMap[group] = [];
		});
		const alreadyGrouped = new Set<string>();
		const nodeIds = nodes.map((node) => node.id);
		return groupMap;
	};

	const removeEdge = useCallback((edgeId: string, event?: SyntheticEvent) => {
		setEdges((nedges) => {
			nedges = nedges
				.map((edge) => {
					if (edge.id == edgeId) {
						return null;
					}
					return edge;
				})
				.filter((edge) => edge != null);
			return nedges;
		});
		clearSelection();
	}, []);

	const removeNode = useCallback((nodeId: string, event?: SyntheticEvent) => {
		setNodes((nnodes) => {
			nnodes = nnodes
				.map((node) => {
					if (node.id == nodeId) {
						return null;
					}
					return node;
				})
				.filter((node) => node != null);
			return nnodes;
		});

		setEdges((nedges) => {
			nedges = nedges
				.map((edge) => {
					if (edge.source == nodeId || edge.target == nodeId) {
						return null;
					}
					return edge;
				})
				.filter((edge) => edge != null);
			return nedges;
		});
		clearSelection();
	}, []);

	const isValidGraph = (nodeFinal: CRNode[]) => {
		const missingCode: string[] = [];
		const nodeCodes: string[] = [];
		nodeFinal.forEach((node) => {
			nodeCodes.push(node?.data?.code || "");
			if (!node?.data?.code) {
				missingCode.push(node.data.label || "New Node");
			}
		});
		if (missingCode.length > 0) {
			showToast(
				{
					type: "error",
					message: `Node(s) missing code: ${missingCode.filter((code) => code != "").join(", ")}`,
					autoClose: 6000,
					position: "bottom-center",
					theme: "dark",
				},
				false
			);
			return false;
		}
		if (new Set(nodeCodes).size !== nodeCodes.length) {
			showToast(
				{
					type: "error",
					message: "Multiple Nodes with Same Node Code",
					autoClose: 6000,
					position: "bottom-center",
					theme: "dark",
				},
				false
			);
			return false;
		}

		return true;
	};

	const addGroupToNodes = (nodes: CRNode[], edges: CREdge[]) => {
		const groupMap: Record<string, string> = {};
		nodes.forEach((node) => {
			if (node.data.type != "queueGroup") {
				return;
			}
			const code = node.data?.code;
			if (typeof code == "string") {
				groupMap[node.id] = code;
			}
		});

		edges.forEach((edge) => {
			const nodeId = groupMap[edge.source] ? edge.target : undefined;
			if (!nodeId) {
				return;
			}
			const node = nodes.find((node) => node.id == nodeId && node.data.type != "queueGroup");
			if (!node || !node.data) {
				return;
			}
			if (Array.isArray(node.data.groups)) {
				if (!node.data.groups.includes(groupMap[edge.source])) {
					node.data.groups.push(groupMap[edge.source]);
				}
			} else {
				node.data.groups = [groupMap[edge.source]];
			}
		});
		return nodes;
	};

	const onSaveClick = () => {
		const nodeFinal = nodes.map((node) => {
			if (node.data.code) {
				node.data.code = node.data.code.trim().replace(/\s+/g, "");
			}
			if (node.data.label) {
				node.data.label = node.data.label.trim().replace(/\s+/g, " ");
			}
			return node;
		});

		if (!isValidGraph(nodeFinal)) {
			return;
		}

		const nodeOrder = nodeFinal
			.filter((node) => node.data.type != "queueGroup")
			.sort((a, b) => {
				if (a.position.x !== b.position.x) {
					return a.position.x - b.position.x;
				}
				return a.position.y - b.position.y;
			})
			.map((node) => node.data.code);

		const groupOrder = nodeFinal
			.filter((node) => node.data.type == "queueGroup")
			.sort((a, b) => {
				if (a.position.x !== b.position.x) {
					return a.position.x - b.position.x;
				}
				return a.position.y - b.position.y;
			})
			.map((node) => node.data.code)
			.filter(Boolean) as string[];

		const updatedNodes = nodeFinal.map((node) => {
			if (node.data.dsl_columns?.length) {
				node.data.dsl_struct = generateDSL(node.data.dsl_columns);
				node.data.card_struct = generateCardStruct(node.data.dsl_columns);
			} else {
				node.data.dsl_struct = null;
			}
			return node;
		});

		const data = {
			graph_data: {
				edges,
				nodes: addGroupToNodes(updatedNodes, edges),
				node_order: nodeOrder,
				group_order: groupOrder,
				config: wfConfig,
			},
		};
		const p = updateForm(data);
		const result = toast.promise(
			p,
			{
				pending: "Saving Workflow!",
				success: "Workflow Saved!",
				error: "Failed to Save Workflow!",
			},
			{
				autoClose: 2000,
				hideProgressBar: true,
				position: "bottom-center",
				theme: "dark",
			}
		);
		result
			.then(() => {
				setNodes(updatedNodes);
			})
			.catch((error) => {
				console.error("Workflow Save Error", error);
			});
	};

	const onNodeClick = useCallback((event: SyntheticEvent, node: CRNode) => {
		event.stopPropagation();
		setSelectedNode(node.id);
		setSelectedEdge("");
	}, []);

	const onEdgeClick = useCallback((event: SyntheticEvent, edge: CREdge) => {
		event.stopPropagation();
		setSelectedNode("");
		setSelectedEdge(edge.id);
	}, []);

	const onClickCanvas = useCallback(() => {
		clearSelection();
	}, []);

	const updateConfigData = useCallback((k: string, v: unknown) => {
		setWFConfig((config) => ({ ...config, [k]: v }));
	}, []);

	const updateNodeData = useCallback((nodeId: string, k: string, v: unknown) => {
		setNodes((nnodes) => {
			nnodes = nnodes.map((node) => {
				if (node.id != nodeId) {
					return node;
				}
				if (!node.data) {
					node.data = {} as NodeData;
				}
				if (["label", "code"].includes(k)) {
					updateNodeContent(nodeId, k, v as string);
				}
				node.data[k] = v;
				return node;
			});
			return [...nnodes];
		});
	}, []);

	const updateEdgeData = useCallback((edgeId: string, k: string, v: unknown) => {
		setEdges((edges) =>
			edges.map((edge) => {
				if (edge.id != edgeId) {
					return edge;
				}
				if (!edge.data) {
					edge.data = {};
				}
				edge.data[k] = v;
				return edge;
			})
		);
	}, []);

	const getNodeData = (nodeId: string) => reactFlow.getNode(nodeId)?.data || {};

	const getEdgeData = (edgeId: string) => reactFlow.getEdge(edgeId)?.data || {};

	const onConnect = useCallback((params: any) => setEdges((eds) => addEdge(params, eds)), []);

	const nodeData = getNodeData(selectedNode);
	const edgeData = getEdgeData(selectedEdge);
	const selectedType = selectedNode ? "node" : selectedEdge ? "edge" : "";

	return (
		<div className="wf-design-container">
			<div className="wrapper" ref={reactFlowWrapper}>
				<ReactFlow
					nodes={nodes}
					edges={edges}
					onNodesChange={onNodesChange}
					onEdgesChange={onEdgesChange}
					nodeTypes={nodeTypes}
					snapToGrid={true}
					snapGrid={snapGrid as [number, number]}
					defaultViewport={defaultViewport}
					onClick={onClickCanvas}
					fitView
					attributionPosition="bottom-left"
					onConnect={onConnect}
					onNodeClick={onNodeClick}
					onEdgeClick={onEdgeClick}
				>
					<Background />
					<Controls />
					<MiniMap
						nodeStrokeColor={(n) => {
							if (!n || !n.type) return "#0041d0";

							if (nodeTypeColorMini.stroke[n.type as keyof typeof nodeTypeColorMini.stroke]) {
								return nodeTypeColorMini.stroke[n.type as keyof typeof nodeTypeColorMini.stroke];
							}
							return "#0041d0";
						}}
						nodeColor={(n) => {
							if (nodeTypeColorMini.background[n.type as keyof typeof nodeTypeColorMini.background]) {
								return nodeTypeColorMini.background[
									n.type as keyof typeof nodeTypeColorMini.background
								];
							}
							return "#fff";
						}}
					/>
				</ReactFlow>
			</div>
			<WFEditBar
				createNewNode={createNewNode}
				updateNodeData={updateNodeData}
				updateEdgeData={updateEdgeData}
				updateConfigData={updateConfigData}
				onSaveClick={onSaveClick}
				duplicateNode={duplicateNode}
				removeEdge={removeEdge}
				removeNode={removeNode}
				workFlowData={fd.data as WorkFlowData}
				wfConfig={wfConfig}
				nodeData={nodeData}
				edgeData={edgeData}
				id={selectedType == "edge" ? selectedEdge : selectedType == "node" ? selectedNode : ""}
				type={selectedType}
			/>
		</div>
	);
};

const WorkFlowDesign = (props: AppFlowProps) => (
	<ReactFlowProvider>
		<AppFlow {...props} />
	</ReactFlowProvider>
);

export default WorkFlowDesign;
