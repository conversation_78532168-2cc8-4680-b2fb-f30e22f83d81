import { Popover } from "@mui/material";
import type { EdgeData, NodeData, NodeDataType, WorkFlowData } from "./types";
import "./wf-edit-bar.less";
import type { FC, SyntheticEvent } from "react";
import React, { useMemo, useState } from "react";
import { findNode<PERSON>itle, getWFN<PERSON>List } from "./nodes";
import { getWFBaseFormComponent, getWFConfigFormComponent } from "./forms/base-from";
import getWFNodeCustomForm from "./forms/node-form";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { WFEdgeFromProps, WFNodeFromProps } from "@modules/workflow/forms/base-from/types";
import { getLabelFromSelect } from "@modules/workflow/forms/form-fields/fields";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { FieldSelect } from "@dsl/fields/field-select";

type CreateNewNodeFunc = (nodeType: NodeDataType) => void;
interface WFEditBarProps {
	createNewNode: CreateNewNodeFunc;
	duplicateNode: (nodeID: string) => void;
	updateNodeData: (selectedNode: string, key: string, value: string) => void;
	updateEdgeData: (selectedEdge: string, key: string, value: string) => void;
	updateConfigData: (key: string, value: string) => void;
	wfConfig: Record<string, unknown>;
	removeEdge: (edgeId: string, event?: SyntheticEvent) => void;
	removeNode: (nodeId: string, event?: SyntheticEvent) => void;
	onSaveClick: () => void;
	workFlowData: WorkFlowData;
	nodeData: NodeData;
	edgeData: EdgeData;
	id: string;
	type: "edge" | "node" | "";
}

interface WFEditBarProps {
	createNewNode: CreateNewNodeFunc;
	workFlowData: WorkFlowData;
}

interface WFAddNewPopoverProps {
	open: boolean;
	onTabOpenChange: (open: boolean) => void;
	workFlowData: WorkFlowData;
	createNewNode: CreateNewNodeFunc;
}

const WFAddNewPopover: FC<WFAddNewPopoverProps> = ({ open, onTabOpenChange, workFlowData, createNewNode }) => {
	const [type, setType] = useState<NodeDataType | "">("");
	const AVAILABLE_NODES = getWFNodesList(workFlowData.type) || [];
	return (
		<Popover
			id="wf-create-popover"
			anchorEl={document.body}
			open={open}
			onClose={() => {
				onTabOpenChange(false);
			}}
			anchorOrigin={{
				vertical: "center",
				horizontal: "center",
			}}
			transformOrigin={{
				vertical: "center",
				horizontal: "center",
			}}
		>
			<GenericCardContainer
				title="Create New Node"
				className="wf-create-popover"
				onClick={() => {
					onTabOpenChange(false);
				}}
				icon={icons.common.crossIcon}
			>
				<div className="wf-popover-container">
					<FieldSelect
						form={"none"}
						options={AVAILABLE_NODES}
						defaultValueSource={type as string}
						defaultValueAutoName={getLabelFromSelect(type as string, AVAILABLE_NODES)}
						onChange={(v) => setType(v as NodeDataType)}
						customStyles={SelectConfig.wf.style}
						theme={SelectConfig.wf.theme}
					/>
					<div className="wf-popover-footer">
						<button
							className="btn-primary logout"
							disabled={!type || type == "none" ? true : false}
							onClick={() => {
								if (type) {
									createNewNode(type);
									onTabOpenChange(false);
								}
							}}
						>
							Create
						</button>
					</div>
				</div>
			</GenericCardContainer>
		</Popover>
	);
};
export const WFEditBar: FC<WFEditBarProps> = (props) => {
	const {
		createNewNode,
		type,
		onSaveClick,
		id,
		removeEdge,
		removeNode,
		duplicateNode,
		workFlowData,
		wfConfig,
		updateConfigData,
	} = props;
	const [open, setOpen] = useState(false);

	const onTabOpenChange = (openf: boolean) => {
		setOpen(openf);
	};
	const ConfigForm = getWFConfigFormComponent(workFlowData.type);
	return (
		<div className="wf-edit-bar">
			<div className="wf-d-topbar">
				<button
					className="wf-d-btn wf-new"
					onClick={() => {
						onTabOpenChange(true);
					}}
				>
					New
				</button>
				<WFAddNewPopover
					open={open}
					onTabOpenChange={onTabOpenChange}
					createNewNode={createNewNode}
					workFlowData={workFlowData}
				/>
				<button className="wf-d-btn wf-save" onClick={onSaveClick}>
					Save
				</button>
			</div>
			<div className="node-details" key={id}>
				{id && type == "node" && <NodeFields {...props} />}
				{id && type == "edge" && <EdgeFields {...props} />}
				{!id && !type && ConfigForm && <ConfigForm updateConfigData={updateConfigData} wfConfig={wfConfig} />}
			</div>
			<div className="wf-d-bottombar">
				<button
					className="wf-d-btn wf-save"
					onClick={(event: SyntheticEvent) => {
						if (type == "edge") {
							removeEdge(id, event);
						} else if (type == "node") {
							removeNode(id, event);
						}
					}}
				>
					Delete
				</button>
				{type == "node" && (
					<button className="wf-d-btn wf-new" onClick={() => duplicateNode(id)}>
						Duplicate
					</button>
				)}
			</div>
		</div>
	);
};

type NodeFieldsProps = WFEditBarProps;

const NodeFields: FC<NodeFieldsProps> = (props) => {
	const { updateNodeData, nodeData, id } = props;
	const CustomForm = getWFNodeCustomForm(nodeData.type, "Node");
	const secLabel = useMemo(() => findNodeTitle(nodeData.type), [nodeData.type]);
	const Form = getWFBaseFormComponent(props?.workFlowData?.type, "Node") as React.FC<WFNodeFromProps> | undefined;
	return (
		<div className="fields" style={id ? undefined : { display: "none" }}>
			{Form && <Form updateNodeData={updateNodeData} nodeData={nodeData} id={id} sectionLabel={secLabel} />}
			{CustomForm && <CustomForm {...props} />}
		</div>
	);
};

type EdgeFieldsProps = WFEditBarProps;

const EdgeFields: FC<EdgeFieldsProps> = (props) => {
	const { updateEdgeData, edgeData, id } = props;
	const Form = getWFBaseFormComponent(props?.workFlowData?.type, "Edge") as React.FC<WFEdgeFromProps> | undefined;
	const CustomForm = getWFNodeCustomForm(edgeData.type as string, "Edge") as React.FC<WFEdgeFromProps> | undefined;

	return (
		<div className="fields" style={id ? undefined : { display: "none" }}>
			{Form && <Form updateEdgeData={updateEdgeData} edgeData={edgeData} id={id} sectionLabel="" />}
			{CustomForm && <CustomForm {...props} />}
		</div>
	);
};
