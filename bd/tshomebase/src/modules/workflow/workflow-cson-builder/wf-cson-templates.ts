import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@typedefs/coffee/dsl";

export const getDSLTemplate = (): DSLForm => JSON.parse(JSON.stringify(BaseDSL));
export const getDSLFieldTemplate = (): DSLField => JSON.parse(JSON.stringify(BaseField));

const BaseDSL = {
	"fields": {
		"reviewed_on": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "datetime",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "picker",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Reviewed On",
				"note": "",
				"offscreen": false,
				"readonly": false,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"deleted": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "text",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "input",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Deleted",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"archived": {
			"view": {
				"note": "",
				"transform": [],
				"readonly": false,
				"control": "select",
				"format": "",
				"label": "Archived",
				"validate": [],
				"class": "",
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			},
			"model": {
				"max": null,
				"rounding": null,
				"required": false,
				"min": null,
				"prefill": [],
				"type": "boolean",
				"autoinsert": true,
				"save": false,
				"subfields": {},
				"validate": [],
				"transform_filter": [],
				"sourceid": "id",
				"multi": false,
				"active": true,
				"transform": [],
				"if": {},
				"template": null,
				"source": null,
				"default": false,
				"transform_post": [],
				"sourcefilter": {},
				"subfields_sort": []
			}
		},
		"created_by": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": "user",
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "int",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "select",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Created By",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"id": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "int",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "input",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "ID",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"updated_on": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "datetime",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "picker",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Updated On",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"auto_name": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "text",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "input",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Auto Name",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"change_type": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": [
					null,
					"create",
					"read",
					"update",
					"delete"
				],
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "text",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "select",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Change Type",
				"note": "",
				"offscreen": false,
				"readonly": false,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"change_data": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "json",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "input",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Change Request",
				"note": "",
				"offscreen": false,
				"readonly": false,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"created_on": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "datetime",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "picker",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Created On",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"change_by": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": "user",
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "int",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "select",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Change By",
				"note": "",
				"offscreen": false,
				"readonly": false,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"updated_by": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": "user",
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "int",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "select",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Updated By",
				"note": "",
				"offscreen": false,
				"readonly": true,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"change_on": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": null,
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "datetime",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "picker",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Change On",
				"note": "",
				"offscreen": false,
				"readonly": false,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		},
		"reviewed_by": {
			"model": {
				"access": {
					"read": [],
					"if": null,
					"write": []
				},
				"active": true,
				"autoinsert": true,
				"default": null,
				"if": {},
				"max": null,
				"min": null,
				"multi": false,
				"prefill": [],
				"required": false,
				"required_all": false,
				"rounding": null,
				"save": false,
				"search": null,
				"source": "user",
				"sourceid": "id",
				"sourcefilter": {},
				"subfields": {},
				"subfields_sort": [],
				"template": null,
				"transform": [],
				"transform_filter": [],
				"transform_post": [],
				"type": "int",
				"validate": [],
				"dynamic": {
					"source": null,
					"type": "text"
				}
			},
			"view": {
				"class": "",
				"control": "select",
				"highlight": null,
				"findfilter": null,
				"findmulti": false,
				"findunique": false,
				"findrange": false,
				"requireall_bypass": false,
				"requireif_bypass": false,
				"format": "",
				"label": "Reviewed By",
				"note": "",
				"offscreen": false,
				"readonly": false,
				"template": null,
				"transform": [],
				"validate": [],
				"max_count": null,
				"grid": {
					"add": "flyout",
					"copy": [],
					"edit": false,
					"fields": [],
					"split": false,
					"text_trim": null,
					"tooltip": [],
					"width": []
				}
			}
		}
	},
	"model": {
		"access": {
			"create": [],
			"create_all": [],
			"read": [],
			"read_all": [],
			"update": [],
			"update_all": [],
			"delete": [],
			"request": [],
			"review": [],
			"rereview": [],
			"write": []
		},
		"bundle": [],
		"collections": [],
		"indexes": {
			"gin": [],
			"fulltext": [],
			"lower": [],
			"many": [],
			"unique": []
		},
		"name": [],
		"prefill": {},
		"reportable": true,
		"required_if": null,
		"save": false,
		"sections_group": [],
		"sections": {
			"Main": {
				"fields": [],
				"group": {},
				"note": "",
				"area": "header",
				"prefill": null
			}
		},
		"transform": [],
		"transform_filter": [],
		"transform_post": [],
		"validate": [],
		"sections_order": [
			"Main"
		],
		"search": []
	},
	"view": {
		"block": {
			"print": {
				"if": null,
				"except": []
			},
			"update": {
				"if": null,
				"except": []
			},
			"validate": []
		},
		"comment": "Workflow Dashboard",
		"find": {
			"advanced": [],
			"basic": []
		},
		"grid": {
			"fields": [],
			"sort": [],
			"style": {}
		},
		"label": "Workflow Dashboard",
		"max_rows": null,
		"open": "read",
		"transform": [],
		"validate": [],
		"timestamp": "2023-11-29T14:27:39.884Z"
	}
};

const BaseField = {
	"model": {
		"access": {
			"read": [],
			"if": null,
			"write": []
		},
		"active": true,
		"autoinsert": false,
		"default": null,
		"if": {},
		"max": null,
		"min": null,
		"multi": false,
		"prefill": [],
		"required": false,
		"required_all": false,
		"rounding": null,
		"save": false,
		"search": null,
		"source": null,
		"sourceid": "id",
		"sourcefilter": {},
		"subfields": {},
		"subfields_sort": [],
		"template": null,
		"transform": [],
		"transform_filter": [],
		"transform_post": [],
		"type": "text",
		"validate": [],
		"dynamic": {
			"source": null,
			"type": "text"
		}
	},
	"view": {
		"class": "",
		"control": "input",
		"highlight": null,
		"findfilter": null,
		"findmulti": false,
		"findunique": false,
		"findrange": false,
		"requireall_bypass": false,
		"requireif_bypass": false,
		"format": "",
		"label": "",
		"note": "",
		"offscreen": false,
		"readonly": false,
		"template": null,
		"transform": [],
		"validate": [],
		"max_count": null,
		"grid": {
			"add": "flyout",
			"copy": [],
			"edit": false,
			"fields": [],
			"split": false,
			"text_trim": null,
			"tooltip": [],
			"width": []
		}
	},
	"_meta": {
		sql_type: ""
	}
};