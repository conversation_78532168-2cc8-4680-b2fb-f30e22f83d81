import type { DSLEditorColumn } from "./wf-cson-editor";
import { CardStruct } from "@modules/workflow/types";

export const generateCardStruct = (columns: DSLEditorColumn[]): CardStruct => {
	const posField: Record<string, string> = {};
	const exclude = ["HL", "HR", "FL", "FR"];
	const totalLines = 3;
	columns.forEach((column) => {
		if (column.on_card) {
			posField[column.on_card] = column.field;
			if (exclude.includes(column.on_card)) return;
		}
	});

	const cs: CardStruct = {
		header: [],
		lines: [],
		footer: [],
	};
	for (let i = 1; i < totalLines + 1; i++) {
		const leftField = posField[`L${i}L`];
		const rightField = posField[`L${i}R`];
		if (!leftField && !rightField) {
			continue;
		}
		if (!leftField) {
			cs.lines.push(["", rightField]);
		} else if (!rightField) {
			cs.lines.push([leftField]);
		} else {
			cs.lines.push([leftField, rightField]);
		}
	}

	if (posField["HL"]) cs.header.push(posField["HL"]);
	else cs.header.push("");
	if (posField["HR"]) cs.header.push(posField["HR"]);
	if (posField["FL"]) cs.footer.push(posField["FL"]);
	else cs.footer.push("");
	if (posField["FR"]) cs.footer.push(posField["FR"]);

	return cs;
};
