import React, { useEffect, use<PERSON>emo, useState } from "react";
import { DragDrop<PERSON>ontext, Draggable } from "react-beautiful-dnd";
import { StrictModeDroppable } from "@blocks/dnd/strict-mode-droppable";
import type { DSLFieldModelTypes } from "@typedefs/coffee/dsl";
import { <PERSON><PERSON>, Tooltip } from "@mui/material";
import type { NodeData } from "../types";
import { request } from "@core/request/request";
import "./wf-cson-editor.less";
import { defaultWfColumns } from "./default-wf-columns";
import { generateDSL, getFilterValue } from "./wf-cson-generator";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { CLJQueryObject } from "@typedefs/window";
import { FieldSelect, SelectOptions } from "@dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { createFilter } from "react-select";

export interface DSLEditorColumn {
	id: string;
	field: string;
	type: <PERSON><PERSON>ieldModelTypes;
	sql_column: string;
	sql_type: string;
	label: string;
	grid: boolean;
	filter: undefined | "N" | "S" | "M";
	hide?: boolean;
	source?: string;
	additional?: true;
	on_card?: string;
}

interface DSLEditorProps {
	onChange: (columns: DSLEditorColumn[]) => void;
	suChange?: (k: string, v: unknown) => void;
	columns: DSLEditorColumn[];
	nodeData: NodeData;
	ignoreWFChecks?: boolean;
	noOnCard?: boolean;
	disabled?: boolean;
	className?: string;
}

export const CARD_POSITION = [
	{
		label: "None",
		value: "",
	},
	{
		label: "Header Left",
		value: "HL",
	},
	{
		label: "Header Right",
		value: "HR",
	},
	{
		label: "Line 1 Left",
		value: "L1L",
	},
	{
		label: "Line 1 Right",
		value: "L1R",
	},
	{
		label: "Line 2 Left",
		value: "L2L",
	},
	{
		label: "Line 2 Right",
		value: "L2R",
	},
	{
		label: "Line 3 Left",
		value: "L3L",
	},
	{
		label: "Line 3 Right",
		value: "L3R",
	},
	{
		label: "Footer Left",
		value: "FL",
	},
	{
		label: "Footer Right",
		value: "FR",
	},
];

const calculateColumns = (exsiting: DSLEditorColumn[], incoming: DSLEditorColumn[]): DSLEditorColumn[] => {
	for (let i = 0; i < incoming.length; i++) {
		const newColumn = incoming[i];

		const oldColumnIndex = exsiting.findIndex((oldColumn) => oldColumn.field === newColumn.field);

		if (oldColumnIndex !== -1) {
			exsiting[oldColumnIndex].sql_column = newColumn.sql_column;
			exsiting[oldColumnIndex].sql_type = newColumn.sql_type;
			exsiting[oldColumnIndex].type = ["date", "datetime", "time"]?.includes(newColumn.type)
				? newColumn.type
				: exsiting[oldColumnIndex].type;
			exsiting[oldColumnIndex].label = newColumn.label;
			exsiting[oldColumnIndex].hide = newColumn.hide;
			exsiting[oldColumnIndex].additional = newColumn.additional;
		} else {
			exsiting.push({
				id: newColumn.id,
				field: newColumn.field,
				label: newColumn.label,
				type: newColumn.type,
				sql_column: newColumn.sql_column,
				sql_type: newColumn.sql_type,
				grid: newColumn.grid,
				filter: newColumn.filter,
				source: newColumn.source,
				hide: newColumn.hide,
				additional: newColumn.additional,
			});
		}
	}
	exsiting = exsiting.filter((oldColumn) => incoming.some((newColumn) => newColumn.field === oldColumn.field));
	return exsiting;
};
interface CoffeeQueryDSLEditorProps {
	handle: {
		nodes: () => {
			report_sql: CLJQueryObject;
		};
		parent: IDSLDrawSubForm & {
			preset: {
				report_sql: string;
				column_struct: DSLEditorColumn[];
			};
		};
	};
	disabled: boolean;
}

export const CoffeeQueryDSLEditor = (props: CoffeeQueryDSLEditorProps) => {
	const { handle, disabled } = props;
	let [nodeData, setNodeData] = useState({
		type: "sqlRaw",
		query: handle?.parent?.preset?.report_sql || "",
	});
	let [columns, setColumns] = useState(handle?.parent?.preset?.column_struct || []);
	const onChange = async (r: DSLEditorColumn[]) => {
		setColumns(r);
		handle.parent.value_field("column_struct", JSON.stringify(r), true, true);
		let dsl = generateDSL(r);
		let fname = handle.parent.value_field("name");
		dsl.view.comment = "Generated by CoffeeQueryDSLEditor for Report: " + fname;
		dsl.view.label = fname;
		handle.parent.value_field("dsl_struct", JSON.stringify(dsl), true, true);
	};
	useEffect(() => {
		handle.nodes().report_sql.on("input", () => {
			setNodeData({ ...nodeData, query: handle.parent.value_field("report_sql") || "" });
		});
		handle.parent.value_field(
			"column_struct",
			JSON.stringify(handle?.parent?.preset?.column_struct || []),
			true,
			true
		);
		handle.parent.value_field("dsl_struct", JSON.stringify(handle?.parent?.preset?.dsl_struct || ""), true, true);
	}, []);

	return (
		<>
			<div className="form-group wf-coffee-cson-editor">
				<label className="col-md-4 col-xs-12 control-label" htmlFor="fld_134"></label>
				<div className="control">
					<div className="col-md-offset-0 col-md-6 col-xs-offset-1 col-xs-10">
						<DSLEditor
							nodeData={nodeData as any}
							onChange={onChange}
							columns={columns}
							ignoreWFChecks={true}
							noOnCard={true}
							disabled={disabled}
						/>
					</div>
				</div>
			</div>
		</>
	);
};

export const DSLEditor: React.FC<DSLEditorProps> = (props) => {
	const { onChange, columns, nodeData, noOnCard, ignoreWFChecks, disabled, className } = props;
	const [rows, setRows] = useState<DSLEditorColumn[]>(columns || []);

	const suUpdate = (columns: DSLEditorColumn[]) => {
		if (!ignoreWFChecks) {
			columns = [...columns, ...defaultWfColumns];
		}
		const r = calculateColumns(rows, columns);
		onChange(r);
		setRows(r);
	};

	const suggestClick = () => {
		if (nodeData.type == "sqlRaw") {
			const q = nodeData.query;
			request({
				url: `/dsl/schema/dynamic?ignoreWFChecks=${ignoreWFChecks ? "true" : "false"}`,
				method: "POST",
				data: { query: q },
			})
				.then((res) => {
					const columns: DSLEditorColumn[] = res.data;
					suUpdate(columns);
				})
				.catch((err) => {
					handleSuggestErrors(err);
				});
		}
		if (nodeData.type == "sqlURL") {
			request({
				url: `/dsl/schema/static/${nodeData?.query_code}`,
				method: "GET",
			})
				.then((res) => {
					const columns: DSLEditorColumn[] = res.data;
					suUpdate(columns);
				})
				.catch((err) => {
					handleSuggestErrors(err);
				});
		}
	};
	const handleSuggestErrors = (err: any) => {
		console.log(err);
		window.prettyError("Error", err.data.response.data.error);
	};

	const updateRow = (id: string, key: keyof DSLEditorColumn, value: any) => {
		const nr = rows.map((row) => (row.id === id ? { ...row, [key]: value } : row));
		setRows(nr);
		onChange(nr);
	};

	const onDragEnd = (result: any) => {
		if (!result.destination) return;
		const [removed] = rows.splice(result.source.index, 1);
		rows.splice(result.destination.index, 0, removed);
		let nrows = [...rows];
		setRows(nrows);
		onChange(nrows);
	};
	const selectOnCardOpts = rows.map((row) => row.on_card).filter(Boolean);

	const filterIconMap = {
		S: "filter-checked",
		M: "fa-th",
		N: "filter-unchecked",
	};
	const filterIconTooltip = {
		S: "Filter Enabled",
		M: "Multi Select Filter",
		N: "Disabled",
	};

	const getSourceOptions = () => {
		if (window.DSL_OPTS) {
			return window.DSL_OPTS;
		}
		let opts = [];
		for (const key in window.DSL) {
			opts.push({ value: key, label: key });
		}
		window.DSL_OPTS = opts;
		return opts;
	};
	const formSource = useMemo(() => getSourceOptions(), []);

	const getItemStyle = (draggableStyle: any) => ({
		...draggableStyle,
		position: "static",
	});

	return (
		<DragDropContext onDragEnd={onDragEnd}>
			<StrictModeDroppable droppableId="droppable">
				{(provided) => (
					<div
						{...provided.droppableProps}
						ref={provided.innerRef}
						className={`wf-cson-editor${className ? ` ${className}` : ""}`}
					>
						{!disabled && (
							<div className="suggest">
								<Button onClick={suggestClick}>Suggest</Button>
							</div>
						)}
						<table className="wf-cson-editor-table">
							<thead>
								<tr>
									<th className="col-label">Field Label</th>
									<th className="col-grid">Grid</th>
									<th className="col-filter">Filter</th>
									{noOnCard ? null : <th className="col-on-card">On Card</th>}
									<th className="col-source">Source</th>
								</tr>
							</thead>
							<tbody>
								{rows.map((row, index) =>
									row.hide ? null : (
										<Draggable
											key={row.id}
											draggableId={`${row.id}`}
											index={index}
											isDragDisabled={disabled}
										>
											{(provided, snapshot) => (
												<tr
													ref={provided.innerRef}
													{...provided.draggableProps}
													{...provided.dragHandleProps}
													style={getItemStyle(provided.draggableProps.style)}
												>
													<td className={`col-label${row.additional ? " dim" : ""}`}>
														{row.label}
													</td>
													<td className={`col-grid${disabled ? " disabled-grid" : ""}`}>
														<input
															type="checkbox"
															checked={row.grid || false}
															onChange={(e) => {
																if (disabled) {
																	return;
																}
																updateRow(row.id, "grid", e.target.checked || false);
															}}
														/>
													</td>
													<td className="col-filter">
														<div
															className="filter-type"
															onClick={() => {
																if (disabled) {
																	return;
																}
																let filter = getFilterValue(row.filter);
																let map = {
																	N: "S",
																	S: "M",
																	M: "N",
																};
																updateRow(row.id, "filter", map[filter]);
															}}
														>
															<Tooltip
																title={filterIconTooltip[getFilterValue(row.filter)]}
															>
																<i
																	className={`fa ${
																		filterIconMap[getFilterValue(row.filter)]
																	}${disabled ? " filter-disabled" : ""}`}
																></i>
															</Tooltip>
														</div>
													</td>
													{noOnCard ? null : (
														<td className="col-on-card">
															<FieldSelect
																key={row.sql_column}
																form="none"
																defaultValueSource={row.on_card || ""}
																defaultValueAutoName={row.on_card || ""}
																multi={false}
																disabled={disabled}
																filterOption={createFilter({ ignoreAccents: false })}
																sourceId="code"
																options={CARD_POSITION}
																onChange={(val, an) => {
																	updateRow(row.id, "on_card", val);
																}}
																customStyles={SelectConfig.wf.style}
																theme={SelectConfig.wf.theme}
																placeholder="Position"
															/>
														</td>
													)}
													<td className="col-source">
														<FieldSelect
															key={row.sql_column}
															form="none"
															defaultValueSource={row.source || ""}
															defaultValueAutoName={row.source || ""}
															multi={false}
															disabled={disabled}
															sourceId="code"
															loadOptions={(value, callback) => {
																if (value) {
																	callback(
																		formSource
																			.filter((o: SelectOptions) => {
																				if (o?.label) {
																					return o.label.includes(value);
																				}
																				return false;
																			})
																			.slice(0, 20)
																	);
																} else {
																	callback(formSource.slice(0, 20));
																}
															}}
															onChange={(val, an) => {
																updateRow(row.id, "source", val);
															}}
															customStyles={SelectConfig.wf.style}
															theme={SelectConfig.wf.theme}
															placeholder="Form"
														/>
													</td>
												</tr>
											)}
										</Draggable>
									)
								)}
								{provided.placeholder}
							</tbody>
						</table>
					</div>
				)}
			</StrictModeDroppable>
		</DragDropContext>
	);
};

export default DSLEditor;
