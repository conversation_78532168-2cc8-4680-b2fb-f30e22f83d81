import type { DSLEditorColumn } from "./wf-cson-editor";

export const defaultWfColumns: DSLEditorColumn[] = [
	{
		id: "__assigned_team",
		field: "__assigned_team",
		label: "Assigned Team",
		source: "wf_queue_team",
		type: "int",
		sql_column: "assigned_team",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
	},
	{
		id: "__status",
		field: "__status",
		label: "Status",
		source: "list_wf_queue_status",
		type: "int",
		sql_column: "__status",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
	},
	{
		id: "__assigned_user",
		field: "__assigned_user",
		label: "Assigned User",
		source: "user",
		type: "int",
		sql_column: "assigned_user",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
	},
	{
		id: "__priority",
		field: "__priority",
		label: "Priority",
		type: "int",
		sql_column: "priority",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
	},
	{
		id: "__due_date",
		field: "__due_date",
		label: "Due Date",
		type: "datetime",
		sql_column: "due_date",
		sql_type: "timestamp without time zone",
		grid: false,
		filter: "N",
		additional: true,
	},
	{
		id: "__wf_queue_node_id",
		field: "__wf_queue_node_id",
		label: "Queue Node ID",
		type: "int",
		sql_column: "__wf_queue_node_id",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__wf_queue_id",
		field: "__wf_queue_id",
		label: "Queue ID",
		type: "int",
		sql_column: "__wf_queue_id",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "tag",
		field: "tag",
		label: "Tag",
		type: "text",
		sql_column: "tag",
		sql_type: "character varying",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__tag",
		field: "__tag",
		label: "Tag",
		type: "text",
		sql_column: "tag",
		sql_type: "character varying",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__created_on",
		field: "__created_on",
		label: "Created On",
		type: "datetime",
		sql_column: "created_on",
		sql_type: "timestamp without time zone",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__updated_on",
		field: "__updated_on",
		label: "Updated On",
		type: "datetime",
		sql_column: "updated_on",
		sql_type: "timestamp without time zone",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__cleared_on",
		field: "__cleared_on",
		label: "Cleared On",
		type: "datetime",
		sql_column: "cleared_on",
		sql_type: "timestamp without time zone",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__row_data",
		field: "__row_data",
		label: "Row Data",
		type: "json",
		sql_column: "row_data",
		sql_type: "json",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__assigned_team_auto_name",
		field: "__assigned_team_auto_name",
		label: "Assigned Team (Auto Name)",
		source: "wf_queue_team",
		type: "int",
		sql_column: "assigned_team",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
	{
		id: "__assigned_user_auto_name",
		field: "__assigned_user_auto_name",
		label: "Assigned User (Auto Name)",
		source: "user",
		type: "int",
		sql_column: "assigned_user",
		sql_type: "integer",
		grid: false,
		filter: "N",
		additional: true,
		hide: true,
	},
];
