.wf-cson-editor {
    .suggest {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
    }
    
    .wf-cson-editor-table {
        width: 100%;
        table-layout: auto;
        tr {
            border-bottom: 1px solid #e0e0e0;
        }
        td,
        th {
            padding: 2px;
            text-align: center;
        }
        tr td.col-label {
            font-weight: 500;
        }

        .col-label {
            width: 40%;
            vertical-align: middle;
            text-align: left;
            text-wrap: balance;
            color: black;
            span {
                width: 100%;
                cursor: grab;
            }
            &.dim {
                font-weight: normal;
            }
            
        }

        .col-grid {
            width: 5%;
        }

        .col-filter {
            width: 5%;
            .filter-type {
                cursor: pointer;
            }
            .filter-unchecked::before {
                font-size: 24px;
                content: "\2610";
                font-weight: 700;
            }
        
            .filter-checked::before {
                font-size: 24px;
                content: "\2611";
                font-weight: 700;
            }
        
            .filter-disabled {
                color: lightgray;
            }
        }
        .col-on-card {
            width: 20%;
            >div {
                width: 100%;
            }
        }

        .col-source {
            width: 30%;
        }
    }
}


.form-horizontal.form-col-1 {
    .wf-cson-editor {
        margin-bottom: 20px;
    }
}

.form-group.wf-coffee-cson-editor {
    .control {
        >div {
            padding: 10;
            border: 2px solid #E2E2E2;
        }

        .suggest {
            padding-bottom: 10px;
            padding-top: 10px;
        }

        td.col-grid.disabled-grid {
            input[type="checkbox"] {
                accent-color: lightgrey;
            }
        }
    }
    .col-label {
        width: 45%;
    }
    .col-filter {
        width: 10%;
    }
    .col-grid {
        width: 10%;
    }
    .col-on-card {
        width: 0%;
    }
    .col-source {
        width: 30%;
        padding: 5px;
        padding-left: 0px;
    }
}

