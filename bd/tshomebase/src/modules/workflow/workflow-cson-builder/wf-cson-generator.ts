import type { DSLForm } from "@typedefs/coffee/dsl";
import { getType, toPresentableLabel } from "@utils/fx";
import type { DSLEditorColumn } from "./wf-cson-editor";
import { getDSLFieldTemplate, getDSLTemplate } from "./wf-cson-templates";

export const getFilterValue = (filter: string | undefined): 'N' | 'S' | 'M' => {
	if(getType(filter) === "string") {
		return filter || "N";
	}else if(getType(filter) === "boolean") {
		return filter ? "S" : "N";
	}
	return "N";
}

export const generateDSL = (columns: DSLEditorColumn[]): DSLForm => {

	const dsl = getDSLTemplate();
	columns.forEach((column) => {
		if (!column.field) return;
		const { field, type, label, source } = column;
		const ft = getDSLFieldTemplate();
		column.filter = getFilterValue(column.filter);
		ft.model.queue = true;
		if (type) {
			ft.model.type = type;
		}
		ft._meta.sql_type = column.sql_type;
		if (source) {
			ft.view.control = "select";
			ft.model.source = source;
			ft.model.type = "int";
		} else if (["date", "datetime", "time"].includes(type)) {
			ft.view.control = "picker";
		} else {
			ft.view.control = "input";
		}
		if(column.filter === "M") {
			ft.model.multi = true;
		}
		ft.view.label = label || toPresentableLabel(field);
		dsl.fields[field] = ft;
		dsl.model.sections["Main"].fields.push(field);
	});

	const fieldsUsed = columns.filter((column) => column.field).filter(Boolean);
	dsl.view.find.basic = fieldsUsed.filter((column) => ["S","M"].includes(column.filter || "N")).map((column) => column.field);

	const visiableGridFields = fieldsUsed.filter((column) => column.grid).map((column) => column.field);
	const allGridFields = columns.map((column) => column.field).filter(Boolean);

	allGridFields.sort((a, b) => {
		if (visiableGridFields.includes(a) && visiableGridFields.includes(b)) {
			return visiableGridFields.indexOf(a) - visiableGridFields.indexOf(b);
		} else if (visiableGridFields.includes(a)) {
			return -1;
		} else if (visiableGridFields.includes(b)) {
			return 1;
		} else {
			return 0;
		}
	});
	const hiddenGridFields = allGridFields.filter((field) => !visiableGridFields.includes(field));
	dsl.view.grid.fields = allGridFields;
	dsl.view.grid.sort = [];
	dsl.view.grid.hide_columns = hiddenGridFields;
	return dsl;
};