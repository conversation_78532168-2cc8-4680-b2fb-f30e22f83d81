import React, { memo } from "react";
import "./queue-group.less";
import "./base-nodes/wf-node.less";
import { NodeContent } from "./base-nodes/wf-node-content";
import { CustomWFNode } from "@modules/workflow/nodes/types";
import { Handle, Position } from "reactflow";

const QueueGroup = memo((props: CustomWFNode) => {
	const { data, isConnectable, selected } = props;
	return (
		<div className={`wf-node queue-group${selected ? " wf-node-selected" : ""}`}>
			<NodeContent data={data} defaultLabel="New Group" />
			<Handle
				type="source"
				position={Position.Bottom}
				id="out-bottom"
				style={{ background: "#C7DCA7" }}
				isConnectable={isConnectable}
			/>
		</div>
	);
});

export default QueueGroup;
