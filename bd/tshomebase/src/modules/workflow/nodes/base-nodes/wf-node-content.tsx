import type { <PERSON>} from "react";
import React, { memo } from "react";
import type { NodeData } from "../../types";

interface NodeContentProps {
    data: NodeData;
    defaultLabel: string;
}
export const NodeContent: FC<NodeContentProps> = ({ data, defaultLabel }) => (
	<>
		<div className='wf-label'>
			{data?.label || defaultLabel }
		</div>
		<div className='wf-code'>
			{data?.code || ""}
		</div>
	</>
);

NodeContent.defaultProps = {
	defaultLabel: "New Node",
};