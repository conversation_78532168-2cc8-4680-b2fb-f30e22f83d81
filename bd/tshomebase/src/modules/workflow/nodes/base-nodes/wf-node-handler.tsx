import type { <PERSON> } from "react";
import React, { memo } from "react";
import { Handle, Position } from "reactflow";
interface NodeHandlerProps {
	isConnectable: boolean;
	children: React.JSX.IntrinsicAttributes;
}
export const NodeHandler: FC<NodeHandlerProps> = ({ isConnectable, children }: NodeHandlerProps) => (
	<>
		<Handle
			type="target"
			id="in-top"
			position={Position.Top}
			style={{ background: "#FFC5C5" }}
			isConnectable={isConnectable}
		/>
		<Handle
			type="target"
			id="in"
			position={Position.Left}
			style={{ background: "#FFC5C5" }}
			isConnectable={isConnectable}
		/>
		{children}
		<Handle
			type="source"
			position={Position.Bottom}
			id="out-bottom"
			style={{ background: "#C7DCA7" }}
			isConnectable={isConnectable}
		/>
		<Handle
			type="source"
			position={Position.Right}
			id="out"
			style={{ background: "#C7DCA7" }}
			isConnectable={isConnectable}
		/>
	</>
);
