import React, { memo } from "react";
import "./wizard-table.less";
import "./base-nodes/wf-node.less";
import { <PERSON>de<PERSON>and<PERSON> } from "./base-nodes/wf-node-handler";
import { NodeContent } from "./base-nodes/wf-node-content";
import type { CustomWFNode } from "./types";

const WizardTable = memo((props: CustomWFNode) => {
	const { data, isConnectable, selected } = props;
	return (
		<div className={`wf-node wizard-table${selected ? " wf-node-selected" : ""}`}>
			<NodeHandler isConnectable={isConnectable}>
				<NodeContent data={data} defaultLabel="New Table Node" />
			</NodeHandler>
		</div>
	);
});

export default WizardTable;
