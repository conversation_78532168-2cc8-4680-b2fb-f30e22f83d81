import SQLURL from "./sql-url";
import SQLRaw from "./sql-raw";
import WizardForm from "./wizard-form";
import QueueGroup from "@modules/workflow/nodes/queue-group";

const NODE_TYPES = {
	Queue: [
		{
			value: "sqlRaw",
			label: "SQL Node",
		},
		// {
		// 	value: "sqlURL",
		// 	label: "Query Node",
		// },
		{
			value: "queueGroup",
			label: "Group",
		},
	],
	Wizard: [
		{
			value: "wizardForm",
			label: "Form",
		},
		{
			value: "wizardTable",
			label: "Table",
		},
	],
	Review: [],
};

const findNodeTitle = (type: string | undefined) => {
	if (!type) return undefined;
	let types: { value: string; label: string }[] = [];
	Object.values(NODE_TYPES).forEach((node) => {
		types = types.concat(node);
	});
	let title = "";
	for (let i = 0; i < types.length; i++) {
		if (types[i].value == type) {
			title = types[i].label;
			return title;
		}
	}
	return title;
};

const getWFNodesList = (type: "Queue" | "Wizard" | "Review") => NODE_TYPES[type];

export { SQLURL, SQLRaw, QueueGroup, WizardForm, NODE_TYPES, findNodeTitle, getWFNodesList };
