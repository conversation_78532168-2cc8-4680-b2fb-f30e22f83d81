import React, { memo } from "react";
import "./sql-raw.less";
import "./base-nodes/wf-node.less";
import { <PERSON>de<PERSON>and<PERSON> } from "./base-nodes/wf-node-handler";
import { NodeContent } from "./base-nodes/wf-node-content";
import { CustomWFNode } from "@modules/workflow/nodes/types";

const SQLRaw = memo((props: CustomWFNode) => {
	const { data, isConnectable, selected } = props;
	return (
		<div className={`wf-node sql-raw${selected ? " wf-node-selected" : ""}`}>
			<NodeHandler isConnectable={isConnectable}>
				<NodeContent data={data} defaultLabel="New SQL Node" />
			</NodeHandler>
		</div>
	);
});

export default SQLRaw;
