import React, { memo } from "react";
import "./wizard-form.less";
import "./base-nodes/wf-node.less";
import { <PERSON>de<PERSON><PERSON><PERSON> } from "./base-nodes/wf-node-handler";
import { NodeContent } from "./base-nodes/wf-node-content";
import type { NodeData } from "../types";
import type { CustomWFNode } from "./types";

const WizardForm = memo((props: CustomWFNode) => {
	const { data, isConnectable, selected } = props;
	return (
		<div className={`wf-node wizard-form${selected ? " wf-node-selected" : ""}`}>
			<NodeHandler isConnectable={isConnectable}>
				<NodeContent data={data} defaultLabel="New Form Node" />
			</NodeHandler>
		</div>
	);
});

export default WizardForm;
