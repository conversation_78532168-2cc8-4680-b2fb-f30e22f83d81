import React, { memo } from "react";
import "./sql-url.less";
import "./base-nodes/wf-node.less";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./base-nodes/wf-node-handler";
import { NodeContent } from "./base-nodes/wf-node-content";
import { CustomWFNode } from "@modules/workflow/nodes/types";

const SQLURL = memo((props: CustomWFNode) => {
	const { data, isConnectable, selected } = props;
	return (
		<div className={`wf-node sql-url${selected ? " wf-node-selected" : ""}`}>
			<NodeHandler isConnectable={isConnectable}>
				<NodeContent data={data} defaultLabel="New Query Node" />
			</NodeHandler>
		</div>
	);
});

export default SQLURL;
