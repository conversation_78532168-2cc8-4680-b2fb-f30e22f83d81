import type { FC } from "react";
import type { WFNodeFromProps } from "./types";
import React from "react";
import { INSPECT_ELEMENT_COMPONENTS, ROW_CLICK_ACTIONS, QueueRowClickActions, QueueRowSelect } from "../../inspect";
import { AreaField, getLabelFromSelect, InputField, SectionLabel } from "../form-fields/fields";
import { FieldSelect } from "@dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { QueueClickConfig } from "@modules/workflow/types";

export const QueueActionConfiguration: FC<
	WFNodeFromProps & {
		type: "single" | "double";
	}
> = (props) => {
	const { nodeData, id, updateNodeData, type, sectionLabel } = props;
	const config = (nodeData[`${type}_row_click`] || {}) as QueueClickConfig;
	if (!config.action) {
		config.action = QueueRowClickActions.None;
	}
	const handleRowClickActionConfigChange = (
		field: "function_name" | "id_field" | "form_field" | "form_name",
		value: string
	) => {
		updateNodeData(id, `${type}_row_click`, {
			...config,
			config: {
				...((config as any).config || {}),
				[field]: value,
			},
		});
	};
	const handleRowClickActionChange = (action: QueueRowClickActions) => {
		updateNodeData(id, `${type}_row_click`, {
			...config,
			action: action,
		});
	};

	return (
		<div
			style={{ display: "flex", flexDirection: "column", gap: "10px", border: "1px solid #ccc", padding: "10px" }}
		>
			<SectionLabel label={sectionLabel || `Row Click Configuration (${type})`} />
			<FieldSelect
				form=""
				multi={false}
				options={ROW_CLICK_ACTIONS}
				defaultValueSource={config.action}
				defaultValueAutoName={getLabelFromSelect(config.action, ROW_CLICK_ACTIONS)}
				onChange={(value) => handleRowClickActionChange(value as QueueRowClickActions)}
				theme={SelectConfig.wf.theme}
				placeholder="Action"
			/>
			{config.action === QueueRowClickActions.FunctionCall && (
				<InputField
					label="Function Name"
					value={config.config?.function_name || ""}
					onChange={(value) => handleRowClickActionConfigChange("function_name", value)}
				/>
			)}
			{(config.action === QueueRowClickActions.FormEdit || config.action === QueueRowClickActions.FormView) && (
				<>
					<InputField
						label="ID Field"
						value={config.config?.id_field || ""}
						onChange={(value) => handleRowClickActionConfigChange("id_field", value)}
					/>
					{!config?.config?.form_name && (
						<InputField
							label="Form Name Field"
							value={config.config?.form_field || ""}
							onChange={(value) => handleRowClickActionConfigChange("form_field", value)}
						/>
					)}
					<InputField
						label="Form Name"
						value={config.config?.form_name || ""}
						onChange={(value) => handleRowClickActionConfigChange("form_name", value)}
					/>
					{config?.config?.form_name && (
						<p style={{ color: "#f07474" }}>
							If 'Form Name' field is set, getting form name from query will be ignored.
						</p>
					)}
				</>
			)}
		</div>
	);
};
export const QueueNodeForm: FC<WFNodeFromProps> = (props) => {
	const { nodeData, id, updateNodeData, sectionLabel } = props;

	return (
		<>
			<SectionLabel label={sectionLabel || "Identity"} />
			<InputField
				label="Label"
				value={nodeData?.label || ""}
				onChange={(value) => updateNodeData(id, "label", value)}
			/>

			<InputField
				label="Code"
				value={nodeData?.code || ""}
				onChange={(value) => updateNodeData(id, "code", value)}
			/>

			<AreaField
				label="Description"
				value={nodeData?.description || ""}
				onChange={(value) => updateNodeData(id, "description", value)}
			/>
			{(nodeData.type as any) != "queueGroup" && (
				<>
					<SectionLabel label="Inspect" />

					<InputField
						label="Inspect Field"
						value={nodeData?.inspect_field || ""}
						onChange={(value) => updateNodeData(id, "inspect_field", value)}
					/>

					<InputField
						label="Inspect Label Field"
						value={nodeData?.inspect_label_field || ""}
						onChange={(value) => updateNodeData(id, "inspect_label_field", value)}
					/>

					<FieldSelect
						form="wf_queue_team"
						defaultValueSource={nodeData.df_team || null}
						defaultValueAutoName={nodeData.df_team_auto_name || null}
						multi={false}
						onChange={(val, an) => {
							updateNodeData(id, "df_team", val);
							updateNodeData(id, "df_team_auto_name", an);
						}}
						theme={SelectConfig.wf.theme}
						placeholder="Default Team"
					/>

					<FieldSelect
						form="user"
						defaultValueSource={nodeData.df_user || null}
						defaultValueAutoName={nodeData.df_user_auto_name || null}
						multi={false}
						onChange={(val, an) => {
							updateNodeData(id, "df_user", val);
							updateNodeData(id, "df_user_auto_name", an);
						}}
						theme={SelectConfig.wf.theme}
						placeholder="Default User"
					/>

					<FieldSelect
						form=""
						defaultValueSource={nodeData.rowSelect || null}
						defaultValueAutoName={nodeData.rowSelect_auto_name || null}
						options={QueueRowSelect}
						multi={false}
						onChange={(val, an) => {
							updateNodeData(id, "rowSelect", val);
							updateNodeData(id, "rowSelect_auto_name", an);
						}}
						theme={SelectConfig.wf.theme}
						placeholder="Row Selection"
					/>

					<FieldSelect
						form=""
						multi={false}
						options={INSPECT_ELEMENT_COMPONENTS}
						defaultValueSource={nodeData.inspect_element || ""}
						defaultValueAutoName={getLabelFromSelect(
							nodeData.inspect_element || "",
							INSPECT_ELEMENT_COMPONENTS
						)}
						onChange={(value) => updateNodeData(id, "inspect_element", value)}
						theme={SelectConfig.wf.theme}
						placeholder="Inspect Element"
					/>
					<QueueActionConfiguration
						type="single"
						nodeData={nodeData}
						sectionLabel="Single Row Click Configuration"
						id={id}
						updateNodeData={updateNodeData}
					/>
					<QueueActionConfiguration
						type="double"
						nodeData={nodeData}
						sectionLabel="Double Row Click Configuration"
						id={id}
						updateNodeData={updateNodeData}
					/>
				</>
			)}
		</>
	);
};
