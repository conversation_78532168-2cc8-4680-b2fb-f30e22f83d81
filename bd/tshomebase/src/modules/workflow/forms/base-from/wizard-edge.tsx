import type { FC } from "react";
import type { WFEdgeFromProps } from "./types";
import React from "react";
import { InputField, SectionLabel } from "../form-fields/fields";

export const WizardEdgeForm: FC<WFEdgeFromProps> = (props) => {
	const { edgeData, id, updateEdgeData } = props;
	return (
		<>
			<SectionLabel label="Identity" />
			<InputField
				label="Label"
				value={(edgeData?.label as string) || ""}
				onChange={(value) => updateEdgeData(id, "label", value)}
			/>
			<InputField
				label="Code"
				value={(edgeData?.code as string) || ""}
				onChange={(value) => updateEdgeData(id, "code", value)}
			/>
		</>
	);
};
