import type { <PERSON> } from "react";
import type { WFConfigFromProps } from "./types";
import React from "react";
import { AreaField, InputField, SectionLabel } from "../form-fields/fields";

export const WizardConfigForm: FC<WFConfigFromProps> = (props) => {
	const { wfConfig, updateConfigData } = props;
	return (
		<>
			<SectionLabel label="Wizard Config" />
			<InputField
				label="Wizard Label"
				value={(wfConfig?.wizard_name as string) || ""}
				onChange={(value) => updateConfigData("wizard_name", value)}
			/>
			<AreaField
				label="Description"
				value={(wfConfig?.description as string) || ""}
				onChange={(value) => updateConfigData("description", value)}
			/>
		</>
	);
};
