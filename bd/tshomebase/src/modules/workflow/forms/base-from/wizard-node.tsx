import type { FC } from "react";
import type { WFNodeFromProps } from "./types";
import React from "react";
import { AreaField, InputField, SectionLabel } from "../form-fields/fields";

export const WizardNodeForm: FC<WFNodeFromProps> = (props) => {
	const { nodeData, id, updateNodeData, sectionLabel } = props;
	return (
		<>
			<SectionLabel label={sectionLabel || "Identity"} />
			<InputField
				label="Label"
				value={nodeData?.label || ""}
				onChange={(value) => updateNodeData(id, "label", value)}
			/>

			<InputField
				label="Code"
				value={nodeData?.code || ""}
				onChange={(value) => updateNodeData(id, "code", value)}
			/>

			<AreaField
				label="Description"
				value={nodeData?.description || ""}
				onChange={(value) => updateNodeData(id, "description", value)}
			/>
		</>
	);
};
