import { WizardEdgeForm } from "./wizard-edge";
import { WizardNodeForm } from "./wizard-node";
import { QueueEdgeForm } from "./queue-edge";
import { QueueNodeForm } from "./queue-node";
import { WizardConfigForm } from "./wizard-config";
import type { WFConfigFromProps, WFEdgeFromProps, WFNodeFromProps } from "./types";

const TypeNodeFrom: Record<string, React.FC<WFNodeFromProps>> = {
	WizardNodeForm,
	QueueNodeForm,
};
const TypeEdgeFrom: Record<string, React.FC<WFEdgeFromProps>> = {
	WizardEdgeForm,
	QueueEdgeForm,
};

const WFConfigForms: Record<string, React.FC<WFConfigFromProps>> = {
	Wizard: WizardConfigForm,
};

const getWFBaseFormComponent = (wfType: string, subType: "Edge" | "Node") => {
	const name = `${wfType}${subType}Form`;
	if (subType) if (subType === "Edge") return TypeEdge<PERSON>rom[name] satisfies React.FC<WFEdgeFromProps>;
	if (subType === "Node") return TypeNodeFrom[name] satisfies React.FC<WFNodeFromProps>;

	return undefined;
};

const getWFConfigFormComponent = (wfType: string): React.FC<WFConfigFromProps> | undefined => WFConfigForms[wfType];

export { TypeNodeFrom, TypeEdgeFrom, WFConfigForms, getWFBaseFormComponent, getWFConfigFormComponent };
