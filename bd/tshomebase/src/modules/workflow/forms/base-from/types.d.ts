import type { EdgeData, NodeData } from "../../types";

export interface WFNodeFromProps {
	updateNodeData: (selectedNode: string, key: string, value: string | any) => void;
	onSaveClick?: () => void;
	nodeData: NodeData;
	id: string;
	sectionLabel?: string;
}

export interface WFEdgeFromProps {
	updateEdgeData: (selectedEdge: string, key: string, value: string) => void;
	onSaveClick?: () => void;
	edgeData: EdgeData;
	id: string;
	sectionLabel?: string;
}

export interface WFConfigFromProps {
	updateConfigData: (key: string, value: string) => void;
	wfConfig: Record<string, unknown>;
}
