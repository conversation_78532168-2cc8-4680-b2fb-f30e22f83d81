import SQLRawForm from "./sql-raw-form";
import SQLURLForm from "./sql-url-form";
import WizardDSLForm from "./wizard-dsl-form";
import WizardFormTable from "./wizard-form-table";
import type { WFEdgeFromProps, WFNodeFromProps } from "../base-from/types";
import QueueGroupForm from "@modules/workflow/forms/node-form/queue-group-form";

const WFNodeFrom = {
	sqlRawNode: SQLRawForm,
	sqlURLNode: SQLURLForm,
	wizardFormNode: WizardDSLForm,
	wizardTableNode: WizardFormTable,
	queueGroupNode: QueueGroupForm,
};

export const getWFNodeCustomForm = (nodeType: string | undefined, type: "Edge" | "Node") => {
	if (!nodeType) return undefined;
	return WFNodeFrom[`${nodeType}${type}` as keyof typeof WFNodeFrom] as React.FC<WFNodeFromProps> | undefined;
};

export default getWFNodeCustomForm;
