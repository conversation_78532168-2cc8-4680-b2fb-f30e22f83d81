import type { NodeFromProps } from "./types";
import type { FC } from "react";
import React from "react";
import DSLEditor from "../../workflow-cson-builder/wf-cson-editor";
import { AreaField, SectionLabel, InputField } from "../form-fields/fields";

const SQLRawForm: FC<NodeFromProps> = (props) => {
	const { updateNodeData, nodeData, id } = props;
	return (
		<>
			<SectionLabel label="Source" />
			<InputField
				label="Source"
				value={(nodeData?.source as string) || ""}
				onChange={(value) => updateNodeData(id, "source", value)}
			/>
			<AreaField
				label="Query"
				value={(nodeData?.query as string) || ""}
				onChange={(value) => updateNodeData(id, "query", value)}
			/>
			<InputField
				label="Cache (seconds)"
				type="number"
				value={(nodeData?.cache as string) || ""}
				onChange={(value) => updateNodeData(id, "cache", value)}
			/>
			<DSLEditor
				nodeData={nodeData}
				className="wf-sidebar"
				columns={nodeData?.dsl_columns || []}
				onChange={(columns) => {
					updateNodeData(id, "dsl_columns", columns as any);
				}}
				suChange={(k: string, v: unknown) => {
					updateNodeData(id, k, v as any);
				}}
			/>
		</>
	);
};
export default SQLRawForm;
