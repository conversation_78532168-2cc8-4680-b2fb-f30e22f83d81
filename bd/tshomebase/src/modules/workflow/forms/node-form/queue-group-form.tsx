import type { NodeFromProps } from "./types";
import type { FC } from "react";
import React from "react";
import { InputField } from "../form-fields/fields";

const QueueGroupForm: FC<NodeFromProps> = (props) => {
	const { updateNodeData, nodeData, id } = props;
	return (
		<>
			<InputField
				label="Icon (Font Awesome)"
				value={(nodeData?.icon as string) || ""}
				onChange={(value) => updateNodeData(id, "icon", value)}
			/>
		</>
	);
};
export default QueueGroupForm;
