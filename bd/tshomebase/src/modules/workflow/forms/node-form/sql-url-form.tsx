import type { NodeFromProps } from "./types";
import type { FC } from "react";
import React, { useMemo } from "react";
import DSLEditor from "../../workflow-cson-builder/wf-cson-editor";
import { useFormFilters } from "@hooks/form-data";
import { getLabelFromSelect, InputField, SectionLabel } from "../form-fields/fields";
import { FieldSelect } from "@dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";

const SQLURLForm: FC<NodeFromProps> = (props) => {
	const { updateNodeData, nodeData, id } = props;
	const [queryCodes, refresh] = useFormFilters("query", {
		limit: 1000,
	});
	const queryCodeOpts = useMemo(
		() => queryCodes.data.map((opt) => ({ label: opt.name, value: opt.code })),
		[queryCodes.data]
	);
	return (
		<>
			<SectionLabel label="Source" />
			<InputField
				label="Source"
				value={(nodeData?.source as string) || ""}
				onChange={(value) => updateNodeData(id, "source", value)}
			/>
			<FieldSelect
				form=""
				options={queryCodeOpts}
				defaultValueSource={nodeData.query_code || ""}
				defaultValueAutoName={getLabelFromSelect((nodeData.query_code || "") as string, queryCodeOpts)}
				onChange={(value) => updateNodeData(id, "query_code", value)}
				theme={SelectConfig.wf.theme}
				placeholder="Query Code"
			/>
			<InputField
				label="Cache (seconds)"
				type="number"
				value={(nodeData?.cache as string) || ""}
				onChange={(value) => updateNodeData(id, "cache", value)}
			/>
			<InputField
				label="Query Params"
				value={(nodeData?.query_params as string) || ""}
				onChange={(value) => updateNodeData(id, "query_params", value)}
			/>
			<DSLEditor
				nodeData={nodeData}
				columns={nodeData?.dsl_columns || []}
				className="wf-sidebar"
				onChange={(columns) => {
					updateNodeData(id, "dsl_columns", columns as any);
				}}
				suChange={(k: string, v: unknown) => {
					updateNodeData(id, k, v as any);
				}}
			/>
		</>
	);
};

export default SQLURLForm;
