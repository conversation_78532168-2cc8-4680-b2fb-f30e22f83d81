import type { NodeFromProps } from "./types";
import type { FC } from "react";
import React from "react";
import { InputField, SectionLabel } from "../form-fields/fields";

const WizardDSLForm: FC<NodeFromProps> = (props) => {
	const { updateNodeData, nodeData, id } = props;

	return (
		<>
			<SectionLabel label="DSL Source" />
			<InputField
				label="DSL Form"
				value={(nodeData?.dsl_form as string) || ""}
				onChange={(value) => updateNodeData(id, "dsl_form", value)}
			/>
			<InputField
				label="Form Sections"
				value={(nodeData?.dsl_form_section as string) || ""}
				onChange={(value) => updateNodeData(id, "dsl_form_section", value)}
			/>
		</>
	);
};

export default WizardDSLForm;
