import type { NodeFromProps } from "./types";
import type { <PERSON> } from "react";
import React, { useMemo } from "react";
import { InputField, SectionLabel, SelectField } from "../form-fields/fields";

const WizardFormTable: FC<NodeFromProps> = (props) => {
	const { updateNodeData, nodeData, id } = props;

	return (
		<>
			<SectionLabel label="DSL Source" />
			<InputField
				label="DSL Form"
				value={(nodeData?.dsl_form as string) || ""}
				onChange={(value) => updateNodeData(id, "dsl_form", value)}
			/>
			<InputField
				label="Form Sections"
				value={(nodeData?.dsl_form_section as string) || ""}
				onChange={(value) => updateNodeData(id, "dsl_form_section", value)}
			/>
			<InputField
				label="Table Columns (comma Sperated)"
				value={(nodeData?.wizard_table_column as string) || ""}
				onChange={(value) => updateNodeData(id, "wizard_table_column", value)}
			/>
			<InputField
				label="Filters Query Params"
				value={(nodeData?.wizard_query_param as string) || ""}
				onChange={(value) => updateNodeData(id, "wizard_query_param", value)}
			/>
		</>
	);
};

export default WizardFormTable;
