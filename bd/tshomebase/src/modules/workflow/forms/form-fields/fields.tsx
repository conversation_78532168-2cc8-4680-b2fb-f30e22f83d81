import type { <PERSON> } from "react";
import React from "react";
import "./fields.less";

interface IInputFieldProps {
	label: string;
	value: string;
	type?: string;
	onChange: (value: string) => void;
}
export const InputField: FC<IInputFieldProps> = (props) => {
	const { label, value, onChange, type } = props;
	return (
		<div className="wf-input">
			<span>{label}</span>
			<input
				value={value}
				type={type || "text"}
				onChange={(event) => {
					onChange(event.target.value);
				}}
			/>
		</div>
	);
};

type IAreaFieldProps = IInputFieldProps;

export const AreaField: FC<IAreaFieldProps> = (props) => {
	const { label, value, onChange } = props;
	return (
		<div className="wf-area">
			<span>{label}</span>
			<textarea
				value={value}
				onChange={(event) => {
					onChange(event.target.value);
				}}
			/>
		</div>
	);
};

export const getLabelFromSelect = (value: string, options: { value: string; label: string }[]) => {
	const option = options.find((opt) => opt.value === value);
	return option?.label || "";
};

export const SectionLabel: FC<{ label: string }> = (props) => {
	const { label } = props;
	return <div className="wf-section-label">{label}</div>;
};
