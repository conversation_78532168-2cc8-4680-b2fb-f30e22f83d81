.wf-inspect-comp {
  background: white;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  display: flex;
  flex-direction: row;
  flex: 1;
  gap: 20px;
  padding: 20px;
  width: 100%;
  height: 100%;
  flex-wrap: wrap-reverse;
  justify-content: space-between;

  .wf-info-cards {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
    align-items: center;

    .wf-card {
      display: flex;
      flex-direction: column;
      background-color: #f5f7fa;
      border-radius: 10px;
      min-width: 300px;
      height: 100%;

      .header {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 10px;
        font-weight: 600;
        border-bottom: 1px solid #2098dc;
      }

      .body {
        overflow-y: auto;
        flex: 1 0;
        .detail-packet-inline {
          .value,
          .label {
            display: flex;
            flex-direction: column;
            justify-content: center;
          }
        }
        display: flex;
        flex-direction: column;
        padding: 10px;
      }
    }
  }

  .wf-actions {
    display: flex;
    flex-direction: column;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    max-width: fit-content;

    .wf-btn {
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      font-size: 14px;
      font-weight: 700;
      border: none;
      min-width: 90px;
    }

    .wf-edit {
      background-color: #17b3ce;
      color: white;
    }

    .wf-design {
      background-color: #9974ce;
      color: white;
    }
    .wf-clone {
      background-color: #58505b;
      color: white;
    }
  }
}
