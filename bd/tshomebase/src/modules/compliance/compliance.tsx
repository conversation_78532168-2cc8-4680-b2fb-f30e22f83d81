import React, { useEffect, useMemo } from "react";
import { useNavigation } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import "./compliance.less";
import { filterArrayByAccess } from "@utils/fx";
import QueryListView from "@blocks/query-list-view/query-list-view";
import { NestedTabView } from "@blocks/nested-tab-view";
import NoDataFound from "@components/common/no-data-found";

export const Compliance = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, "/compliance");
	useEffect(() => {
		if (props.navToURL?.startsWith("/compliance")) {
			props.setActiveTab?.("compliance");
		}
	}, [nav.globalNav.lrct]);

	const map = useMemo(() => {
		const defaultSettingsArray = [
			{
				label: "PECOS",
				id: "pecos",
				path: "/compliance/pecos",
				component: {
					id: "pecos",
					renderComponent: QueryListView,
					componentProps: {
						code: "pecos_provider_issues",
						parameters: '',
						ActionComponent: null,
					},
				}
			},
			{
				label: "URAC",
				id: "urac",
				path: "/compliance/urac",
				component: {
					id: "urac",
					renderComponent: NoDataFound,
					componentProps: {
						text: "URAC Report Pending"
					},
				}
			}
		];
		return filterArrayByAccess(defaultSettingsArray);
	}, []);


	const tabLevelProps = { levels: {}, dsl: {} };

	return (<NestedTabView {...{ ...props, ...nav, map, tabLevelProps }} />);

};