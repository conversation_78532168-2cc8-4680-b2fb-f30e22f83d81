.report-inspect-wrapper {
    display: flex;
    flex-direction: row;
    background-color: white;
    gap: 20px;
    padding: 20px;
    height: 100%;

    .dsl-card-container-p {
        width: 30%;
        max-width: 320px;
    }

    .report_info_componet_wrapper {
        display: flex;
        flex: 1;
        height: 90%;
    }

    .info-container-wrapper {
        background: #f5f5f5;
        border-radius: 5px;
        height: 100%;
        overflow: scroll;

        .info-detail-c {
            padding: 10px;
            display: flex;
            justify-content: space-between;
            line-height: 14px;
            font-size: 12px;
            text-align: left;
            color: #283742;

            .bold {
                font-weight: 600;
            }
        }

        .info-container-title {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 10px;
            font-weight: 600;
        }
    }

    .border {
        width: 100%;
        border-bottom: 0px;
        border-style: solid;
        border-color: #2098DC;
        margin: 0px;
    }

    .report-inspect-actions-wrapper {
        display: flex;
        justify-content: flex-end;
        flex: auto;

        .report-inspect-actions {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;

            .report-btn {
                padding: 10px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 5px;
                font-size: 14px;
                font-weight: 700;
                border: none;
                min-width: 90px;
            }

            .rep-edit {
                background-color: #53B2DE;
                color: white;
            }

            .rep-access {
                background-color: #E69B9B;
                color: white;
            }
        }
    }
}
