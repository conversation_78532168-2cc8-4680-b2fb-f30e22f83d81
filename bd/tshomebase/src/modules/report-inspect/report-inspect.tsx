import React, { useCallback, useEffect, useState } from "react";
import "./report-inspect.less";
import type { ReportDescriptor } from "@typedefs/arjs";
import { getReportInfo } from "../report/report-actions";
import { getReportInfo as getReportInfoTemplate } from "../report-template/report-actions";
import { ReportDesignerViewer } from "../report/report-designer-viewer";
import { ReportDesignerViewerTemplate } from "../report-template/report-designer-viewer-template";

export const ReportInspectComponent = (props) => {
	const actions = props.parentProps.tabViewActions;
	const { tabData } = props;

	const [report, setReport] = useState<ReportDescriptor | null>(null);

	const openReport = useCallback(
		async (id: number) => {
			try {
				const data = await (tabData.form === "template_report" ? getReportInfoTemplate(id) : getReportInfo(id));
				setReport(data as ReportDescriptor);
			} catch (err) {
				console.log(err);
			}
		},
		[tabData.id]
	);

	useEffect(() => {
		// eslint-disable-next-line @typescript-eslint/no-floating-promises
		openReport(parseInt(tabData.id));
	}, [tabData.id]);
	return (
		<>
			<div className="report-inspect-wrapper">
				<div className="dsl-card-container-p">
					<div className="info-container-wrapper">
						<div className="info-container-title">
							<div className="info-detail">
								<div className="bold">
									<p>Report Information</p>
								</div>
							</div>
						</div>
						<div className="border" />
						<div className="info-detail-c">
							<div className="bold">
								<p>Name</p>
							</div>
							<p>{report?.name ? report?.name : " "}</p>
						</div>
						<div className="info-detail-c">
							<div className="bold">
								<p>Type</p>
							</div>
							<p>{report?.type ? report?.type : " "}</p>
						</div>
						<div className="info-detail-c">
							<div className="bold">
								<p>Code</p>
							</div>
							<p>{report?.code ? report?.code : " "}</p>
						</div>
					</div>
				</div>
				<div className="dsl-card-container-p">
					<div className="info-container-wrapper">
						<div className="info-container-title">
							<div className="info-detail">
								<div className="bold">
									<p>Page Information</p>
								</div>
							</div>
						</div>
						<div className="border" />
						<div className="info-detail-c">
							<div className="bold">
								<p>Page Width</p>
							</div>
							<p>{report?.page_width ? report?.page_width + "in" : report?.json_data?.Page?.PageWidth}</p>
						</div>
						<div className="info-detail-c">
							<div className="bold">
								<p>Page Height</p>
							</div>
							<p>
								{report?.page_height ? report?.page_height + "in" : report?.json_data?.Page?.PageHeight}
							</p>
						</div>
					</div>
				</div>
				<div className="dsl-card-container-p">
					<div className="info-container-wrapper">
						<div className="info-container-title">
							<div className="info-detail">
								<div className="bold">
									<p>Report Parameters</p>
								</div>
							</div>
						</div>
						<div className="border" />
						<div className="info-detail-c">
							<div className="bold">
								<p>Parameters Name</p>
							</div>
							<p>
								{report &&
									Array.from(
										{ length: 4 },
										(_, index) => report[`parameter_name_${index + 1}`] || " "
									).join(" ")}
							</p>
						</div>
						<div className="info-detail-c">
							<div className="bold">
								<p>Parameters Prompt</p>
							</div>
							<p>
								{report &&
									Array.from(
										{ length: 4 },
										(_, index) => report[`parameter_prompt_${index + 1}`] || " "
									).join(" ")}
							</p>
						</div>
					</div>
				</div>
				<div className="report-inspect-actions-wrapper">
					<div className="report-inspect-actions">
						<button
							className="report-btn rep-edit"
							onClick={() => actions.openTab(tabData?.id, tabData?.label, "edit", tabData.form, {})}
						>
							Edit
						</button>
						<button
							className="report-btn rep-access"
							onClick={() => actions.openTab(tabData?.id, tabData?.label, "design", tabData.form, {})}
						>
							Design
						</button>
					</div>
				</div>
			</div>
		</>
	);
};

export const ReportDesignView = (props) => {
	if (props?.tabData?.form === "report") {
		return <ReportDesignerViewer {...props} id={props.id} />;
	} else if (props?.tabData?.form === "template_report") {
		return <ReportDesignerViewerTemplate {...props} id={props.id} />;
	}
	return <div>Invalid route.</div>;
};
