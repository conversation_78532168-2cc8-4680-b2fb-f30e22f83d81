@import (reference) "../../../less/style/main.less";
@INVEN_SNAP_COLOR_VAL: #4b3818;

.snap-detail-item {
  display: flex;
  justify-content: center;
  gap: 4px;
  color: @INVEN_SNAP_COLOR_VAL;
  flex-direction: column-reverse;
  font-size: 13px;

  .left-v {
    display: flex;
    flex: 1;
    // justify-content: flex-end;

    .snap-label {
      display: flex;
      justify-content: flex-end;
      flex-direction: row;
      gap: 4px;
      color: @charcoal-gray;
      align-items: baseline;

      .each {
        display: flex;
        font-style: italic;
        justify-content: flex-end;
        font-size: 10px;
      }
    }
  }

  .right-v {
    display: flex;
    flex: 1;
    font-weight: 500;
    @color: @black;
  }
}

.snap-detail-item-v {
  flex-direction: column;

  .right-v {
    padding-left: 10px;
  }
}

.inventory-snap {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  justify-content: center;
  gap: 10px;

  .info-items {
    display: flex;
    flex-direction: row;
    gap: 10px;
    flex-wrap: wrap;
    flex-shrink: 0;

    > div {
      flex: 0 0 48%;
    }
  }

  .item-column {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1 0;
  }

  .item-area {
    .list-item-area;
    overflow: scroll;
    padding: 1px 9px;
    gap: 24px;
  }

  .info-pill {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: @semi-transparent-light-gray-12;
    color: @dark-gray;
    border-radius: 6px;
    font-weight: 500;
    padding: 0 8px;
    font-size: 14px;
    height: 28px;
  }

  .header-bar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid lightgrey;
    align-items: center;
    padding-bottom: 6px;

    .title {
      font-size: 14px;
      color: @INVEN_SNAP_COLOR_VAL;
      display: flex;
      justify-content: flex-start;
      font-weight: 600;
    }

    .info {
      display: flex;
      justify-content: flex-end;
      gap: 10px;

      > img {
        height: 18px;
        width: 18px;
        margin: auto;
      }

      .go-green {
        display: flex;
        background: @semi-transparent-sky-blue-08;
        color: @sky-blue;
        font-size: 14px;
        font-weight: 600;
        justify-content: space-between;
        gap: 4px;
      }
    }
  }

  .list-bar {
    display: flex;
    width: 100%;
    margin-bottom: -10px;

    .tab-list-default {
      padding-left: 0px;
      // width: 100%;
    }
  }

  .hz-sec {
    gap: 10px;
  }

  .qm-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    // flex: 1;
    flex: 1 1 calc(50% - 10px);
    min-height: 150px;
    padding: 10px;
    width: 100%;
    border: 1px solid lightgray;
    border-radius: 10px;
    background-color: white;
    overflow-y: auto;
  }

  .query-list-view {
    padding: unset;
    padding-bottom: 10px;

    .query-list {
      min-height: 10px !important;

      .dsl-list-bottom {
        min-height: 10px !important;
      }
    }

    .dsl-list-export {
      display: none;
    }

    .dsl-list-tab-container {
      padding: 0px;

      .dsl-list-top {
        padding-top: 10px;
      }

      .search-bar {
        border: 1px solid lightgray;

        > img {
          display: none;
        }
      }

      .grid-label {
        display: none;
      }
    }
  }

  .list-item-area {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 10px;
    width: 100%;
  }

  .bdr-rnd {
    // border-radius: 10px;
    border-radius: 0 0 12px 12px;
    // border: 1px solid lightgray;
    background-color: white;
    padding: 15px;
  }

  .area {
    display: flex;
    // flex-direction: column;
    justify-content: center;
    // align-items: center;
    flex: 1 0;
    // padding: 10px;
    gap: 10px;
    position: relative;
    flex-wrap: wrap;

    .area-hz-cont {
      display: flex;
      flex-direction: row;
      //   flex: 1;
      justify-content: center;
      width: 100%;
      align-items: center;
      gap: 10px;

      .hz-sec {
        display: flex;
        flex-direction: column;
        // flex: 1 0 50%;
        // flex: 1;
        flex: 1 1 calc(50% - 10px);
        height: 100%;
        width: 100%;
        justify-content: space-between;
        align-items: flex-start;
      }
    }
  }
}
