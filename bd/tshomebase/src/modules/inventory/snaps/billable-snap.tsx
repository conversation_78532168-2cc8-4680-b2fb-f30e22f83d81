import React, { FC, useEffect } from 'react';
import { useFormData } from '@hooks/form-data';
import "./shared.less";
import { BaseSnapRenderComponentProps } from '@blocks/generic-snapshot/generic-snapshot';
import { useTabController } from '@hooks/tab-controller';
import patient from '@public/icons/snap/patient';
import QueryListView from '@blocks/query-list-view/query-list-view';
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { SnapDetailItem, SnapNote } from './shared';
import { getPrettyNumber } from '@utils/fx';
import { useNavigation } from '@core/navigation';
import { InventoryItem } from './shared-snap';
import PublicIcon from '@public/icons';
import { useOnScreen } from '@hooks/on-screen';

interface InventoryBillableSnapshotProps extends BaseSnapRenderComponentProps {

}

const InventoryBillableSnapshot: React.FC<InventoryBillableSnapshotProps> = (props) => {

    useNavigation(props, `/${props.id}/snap`);

    const reportParams = `1=Billable&2=${props.id}`

    const REPORT_TABS: Partial<TabData>[] = [
        {
            tkey: "audit_log",
            id: "audit_log",
            label: "Audit Log",
            componentProps: {
                code: "inv_item_audit_log",
                parameters: reportParams,
                ActionComponent: null,
            },
            renderComponent: QueryListView
        },
        {
            tkey: "contract_pricing",
            id: "contract_pricing",
            label: "Contract Pricing",
            componentProps: {
                code: "inv_item_contract_pricing",
                parameters: reportParams,
                ActionComponent: null,
                label: " ",
            },
            renderComponent: QueryListView
        },
    ];

    const [fd, refresh] = useFormData(props.form, props.id, true, true);

    const [isOnScreen, elementRef] = useOnScreen();

    useEffect(() => {
        if (isOnScreen) {
            refresh();
        }
    }, [isOnScreen])

    const item = (fd?.data || {}) as unknown as InventoryItem;

    let [openListTabs, activeListTab, controllerList] = useTabController(REPORT_TABS as TabData[], REPORT_TABS[0].tkey);

    return (

        <div className="inventory-snap" ref={elementRef}>
            <div className="area">
                <div className="area-hz-cont">
                    <div className="hz-sec bdr-rnd">
                        <div className="header-bar">
                            <div className='title'>
                                {item.auto_name || "-"}
                            </div>
                            <div className='info'>
                                {item.category_id_auto_name ? <div className='info-pill'>{item.category_id_auto_name}</div> : null}
                                <div className={`info-pill ${item.active ? "go-green" : "go-red"}`}>
                                    {item.active == 'Yes' ? (
                                        <>
                                            <img src={PublicIcon?.common.filledDoneIcon} alt="icon" />
                                            <span>Active</span>
                                        </>
                                    ) : "Inactive"}
                                </div>
                                <img
                                    src={patient.editsm}
                                    onClick={() => { props.tabViewActions.openTab(props.id, item.auto_name, "edit",props.form, {}) }}
                                />
                            </div>
                        </div>
                        <div className='item-area'>
                            <div className='info-items'>
                                <div className='item-column'>
                                    <SnapDetailItem label={"HCPC"} value={item.billable_code_id || "-"} />
                                    <SnapDetailItem label={"CODE TYPE"} value={item.billable_code_type || "-"} />
                                </div>
                                <div className='item-column'>
                                    <SnapDetailItem label={"NURSING RELATED?"} value={item.nursing_related || "-"} />
                                    {item.nursing_related == 'Yes' &&
                                        <>
                                            <SnapDetailItem label={"INITIAL VISIT?"} value={item.initial_visit || "-"} />
                                            <SnapDetailItem label={"NURSING HOURS"} value={getPrettyNumber(item.nursing_hours)} />
                                        </>
                                    }
                                </div>
                            </div>
                            <SnapNote label={"NOTES"} value={item.note || ""} />
                        </div>
                    </div>
                    <div className="hz-sec bdr-rnd">
                        <div className="header-bar">
                            <div className='title'>
                                Pricing
                            </div>
                            <div className='info'>
                                {item.price_code_id_auto_name ? <div className='info-pill'>{item.price_code_id_auto_name}</div> : null}
                                {item.revenue_code_id_auto_name ? <div className='info-pill'>{item.revenue_code_id_auto_name}</div> : null}
                            </div>
                        </div>
                        <div className='item-area'>
                            <div className='info-items'>
                                <div className='item-column'>
                                    <SnapDetailItem label={"LIST PRICE"} sub={"each"} value={getPrettyNumber(item.list_price, "currency")} />
                                </div>
                                <div className='item-column'>
                                    <SnapDetailItem label={"ADDTL PRICE 1"} value={getPrettyNumber(item.add_price1, "currency")} />
                                    <SnapDetailItem label={"ADDTL PRICE 2"} value={getPrettyNumber(item.add_price2, "currency")} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="area-hz-cont">
                    <div className="hz-sec">
                        <div className='list-bar'>
                            <TabList
                                activeTabId={activeListTab}
                                openTabs={openListTabs}
                                styles={{
                                    tabListStyle: "lvl-2-tab-list",
                                }}
                                optionsProps={{
                                    enabled: false
                                }}
                                addProps={{
                                    enabled: false
                                }}
                                tabCanClose={false}
                                draggable={false}
                                onTabClick={(tab: TabData) => {
                                    controllerList.setActiveTabId(tab.tkey);
                                }}
                            />
                        </div>
                        {
                            openListTabs.map((tab, index) => {
                                const RenderComponent = tab.renderComponent as FC<unknown>;
                                return (
                                    <div className='qm-container' key={tab.tkey} style={activeListTab == tab.tkey ? undefined : { display: "none" }}>
                                        <RenderComponent {...(tab.componentProps || {})} />
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InventoryBillableSnapshot;