export interface InventoryItem {
    auto_name: string;
    type_id: string;
    active: string;
    ndc: string;
    concentration: string;
    manufacturer_id: string;
    manufacturer_id_auto_name: string;
    quantity_each: string;
    hcpc_id_auto_name: string;
    hcpc_quantity: string;
    billable_code_id: string;
    billable_code_id_auto_name: string;
    list_price_code: string;
    list_price_code_auto_name: string;
    billable_code_type: string;
    nursing_related: string;
    initial_visit: string;
    nursing_hours: string;
    category_id_auto_name: string;
    daily_rental_list_price: string;
    monthly_rental_list_price: string;
    purchase_list_price: string;
    daily_rental_cost: string;
    monthly_rental_cost: string;
    purchase_cost: string;
    lot_tracking: string;
    serial_tracking: string;
    allow_fuzzy_matching: string;
    note: string;
    price_code_id: string;
    price_code_id_auto_name: string;
    revenue_code_id: string;
    revenue_code_id_auto_name: string;
    asp_price: string;
    wac_price: string;
    awp_price: string;
    list_price: string;
    mcr_charge: string;
    add_price1: string;
    add_price2: string;
}