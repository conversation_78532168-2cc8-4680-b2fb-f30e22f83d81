import React, { FC, useEffect } from 'react';
import { useFormData } from '@hooks/form-data';
import "./shared.less";
import { BaseSnapRenderComponentProps } from '@blocks/generic-snapshot/generic-snapshot';
import { useTabController } from '@hooks/tab-controller';
import patient from '@public/icons/snap/patient';
import QueryListView from '@blocks/query-list-view/query-list-view';
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { getPrettyNumber } from '@utils/fx';
import { SnapDetailItem, SnapNote } from './shared';
import { useNavigation } from '@core/navigation';
import { InventoryItem } from './shared-snap';
import PublicIcon from '@public/icons';
import { useOnScreen } from '@hooks/on-screen';

interface InventoryRentalSnapshotProps extends BaseSnapRenderComponentProps {

}

const InventoryRentalSnapshot: React.FC<InventoryRentalSnapshotProps> = (props) => {
    useNavigation(props, `/${props.id}/snap`);

    const reportParams = `1=Equipment Rental&2=${props.id}`

    const REPORT_TABS: Partial<TabData>[] = [
        {
            tkey: "status",
            id: "status",
            label: "Status",
            componentProps: {
                code: "inv_item_rental_status",
                parameters: reportParams,
                ActionComponent: null,
                label: "",
            },
            renderComponent: QueryListView
        },
        {
            tkey: "audit_log",
            id: "audit_log",
            label: "Audit Log",
            componentProps: {
                code: "inv_item_audit_log",
                parameters: reportParams,
                ActionComponent: null,
            },
            renderComponent: QueryListView
        },
    ];


    const [fd, refresh] = useFormData(props.form, props.id, true, true);

    const [isOnScreen, elementRef] = useOnScreen();

    useEffect(() => {
        if (isOnScreen) {
            refresh();
        }
    }, [isOnScreen])

    const item = (fd?.data || {}) as unknown as InventoryItem;
    let [openListTabs, activeListTab, controllerList] = useTabController(REPORT_TABS as TabData[], REPORT_TABS[0].tkey);

    return (

        <div className="inventory-snap" ref={elementRef}>
            <div className="area">
                <div className="area-hz-cont">
                    <div className="hz-sec bdr-rnd">
                        <div className="header-bar">
                            <div className='title'>
                                {item.auto_name || "-"}
                            </div>
                            <div className='info'>
                                {item.category_id_auto_name ? <div className='info-pill'>{item.category_id_auto_name}</div> : null}
                                <div className={`info-pill ${item.active ? "go-green" : "go-red"}`}>
                                    {item.active == 'Yes' ? (
                                        <>
                                            <img src={PublicIcon?.common.filledDoneIcon} alt="icon" />
                                            <span>Active</span>
                                        </>
                                    ) : "Inactive"}
                                </div>
                                <img
                                    src={patient.editsm}
                                    onClick={() => { props.tabViewActions.openTab(props.id, item.auto_name, "edit", props.form, {})}}
                                />
                            </div>
                        </div>
                        <div className='item-area'>
                            <div className='info-items'>
                                <div className='item-column'>
                                    <SnapDetailItem label={"MANUFACTURER"} value={item.manufacturer_id_auto_name || ""} />
                                    <SnapDetailItem label={"QUANTITY"} sub={"per each"} value={getPrettyNumber(item.quantity_each)} />

                                </div>
                                <div className='item-column'>
                                    <SnapDetailItem label={"HCPC"} value={item.hcpc_id_auto_name || ""} />
                                </div>
                            </div>
                            <SnapNote label={"NOTES"} value={item.note || ""} />
                        </div>
                    </div>
                    <div className="hz-sec bdr-rnd">
                        <div className="header-bar">
                            <div className='title'>
                                Pricing
                            </div>
                            <div className='info'>
                                {item.price_code_id_auto_name ? <div className='info-pill'>{item.price_code_id_auto_name}</div> : null}
                                {item.revenue_code_id_auto_name ? <div className='info-pill'>{item.revenue_code_id_auto_name}</div> : null}
                            </div>
                        </div>
                        <div className='item-area'>
                            <div className='info-items'>
                                <div className='item-column'>
                                    <SnapDetailItem label={"DAILY"} sub={"list"} value={getPrettyNumber(item.daily_rental_list_price, "currency")} />
                                    <SnapDetailItem label={"MONTHLY"} sub={"list"} value={getPrettyNumber(item.monthly_rental_list_price, "currency")} />
                                    <SnapDetailItem label={"PURCHASE"} sub={"list"} value={getPrettyNumber(item.purchase_list_price, "currency")} />
                                    <SnapDetailItem label={"DAILY"} sub={"cost"} value={getPrettyNumber(item.daily_rental_cost, "currency")} />
                                    <SnapDetailItem label={"MONTHLY"} sub={"cost"} value={getPrettyNumber(item.monthly_rental_cost, "currency")} />
                                    <SnapDetailItem label={"PURCHASE"} sub={"cost"} value={getPrettyNumber(item.purchase_cost, "currency")} />
                                </div>
                                <div className='item-column'>
                                    <SnapDetailItem label={"ADDTL PRICE 1"} value={getPrettyNumber(item.add_price1, "currency")} />
                                    <SnapDetailItem label={"ADDTL PRICE 2"} value={getPrettyNumber(item.add_price2, "currency")} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="area-hz-cont">
                    <div className="hz-sec">
                        <div className='list-bar'>
                            <TabList
                                activeTabId={activeListTab}
                                openTabs={openListTabs}
                                styles={{
                                    tabListStyle: "lvl-2-tab-list",
                                }}
                                optionsProps={{
                                    enabled: false
                                }}
                                addProps={{
                                    enabled: false
                                }}
                                tabCanClose={false}
                                draggable={false}
                                onTabClick={(tab: TabData) => {
                                    controllerList.setActiveTabId(tab.tkey);
                                }}
                            />
                        </div>
                        {
                            openListTabs.map((tab, index) => {
                                const RenderComponent = tab.renderComponent as FC<unknown>;
                                return (
                                    <div className='qm-container' key={tab.tkey} style={activeListTab == tab.tkey ? undefined : { display: "none" }}>
                                        <RenderComponent {...(tab.componentProps || {})} />
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InventoryRentalSnapshot;