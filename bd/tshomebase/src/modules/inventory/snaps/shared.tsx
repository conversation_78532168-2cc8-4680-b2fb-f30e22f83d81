
import { renderItemData } from "@utils/fx";
import "./shared.less"

export const SnapDetailItem = (props: { label?: string, value?: string, sub?: string }) => {
    return (
        <div className="snap-detail-item">
            <div className="left-v">
                <div className='snap-label'>
                    {renderItemData(props.label)}
                    {props.sub &&
                        <div className='each'>
                            {props.sub}
                        </div>
                    }
                </div>

            </div>
            <div className="right-v">{renderItemData(props.value)}</div>
        </div>
    );
}
export const SnapNote = (props: { label: string, value: string }) => {
    return (
        <div className="snap-detail-item snap-detail-item-v">
            <div className='snap-label'>
                {renderItemData(props.label)}
            </div>
            <div className="right-v">{renderItemData(props.value)}</div>
        </div>
    );
}