import React, { FC } from 'react';
import { useFormData } from '@hooks/form-data';
import "./shared.less";
import { BaseSnapRenderComponentProps } from '@blocks/generic-snapshot/generic-snapshot';
import { useTabController } from '@hooks/tab-controller';
import patient from '@public/icons/snap/patient';
import QueryListView from '@blocks/query-list-view/query-list-view';
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { SnapDetailItem, SnapNote } from './shared';
import { getPrettyNumber, getPrettyNDC } from '@utils/fx';
import { ReportManager } from '@components/arjs/report-manager';
import { useNavigation } from '@core/navigation';
import { InventoryItem } from './shared-snap';
import PublicIcon from '@public/icons';

interface InventorySupplySnapshotProps extends BaseSnapRenderComponentProps {

}

const InventorySupplySnapshot: React.FC<InventorySupplySnapshotProps> = (props) => {
    useNavigation(props, `/${props.id}/snap`);

    let queryListParams = `1=Supply&2=${props.id}`

    let reportParmaters = {
        type: 'Supply',
        id: props.id
    }

    const REPORT_TABS: Partial<TabData>[] = [
        {
            tkey: "on_hand",
            id: "on_hand",
            label: "On Hand",
            componentProps: {
                code: "inv_item_on_hand",
                parameters: queryListParams,
                ActionComponent: null,
            },
            renderComponent: QueryListView
        },
        {
            tkey: "audit_log",
            id: "audit_log",
            label: "Audit Log",
            componentProps: {
                code: "inv_item_audit_log",
                parameters: queryListParams,
                ActionComponent: null,
            },
            renderComponent: QueryListView
        },
        {
            tkey: "purchases",
            id: "purchases",
            label: "Purchases",
            componentProps: {
                code: "inv_item_purchases",
                parameters: queryListParams,
                ActionComponent: null,
            },
            renderComponent: QueryListView
        },
        {
            tkey: "on_order",
            id: "on_order",
            label: "On Order",
            componentProps: {
                code: "inv_item_on_order",
                parameters: queryListParams,
                ActionComponent: null,
            },
            renderComponent: QueryListView
        },
        {
            tkey: "contract_pricing",
            id: "contract_pricing",
            label: "Contract Pricing",
            componentProps: {
                code: "inv_item_contract_pricing",
                parameters: queryListParams,
                ActionComponent: null,
                label: " ",
            },
            renderComponent: QueryListView
        },
    ];
    const GRAPH_TABS: Partial<TabData>[] = [
        {
            tkey: "utilization",
            id: "utilization",
            label: "Utilization",
            componentProps: {
                actionType: "view",
                code: "inv_snap_utilization",
                parameters: reportParmaters,
                toolbarVisible: false
            },
            renderComponent: ReportManager
        },
        {
            tkey: "forecast",
            id: "forecast",
            label: "Forecast",
            componentProps: {
                actionType: "view",
                code: "inv_snap_forecast",
                parameters: reportParmaters,
                toolbarVisible: false
            },
            renderComponent: ReportManager
        },
        {
            tkey: "margin",
            id: "margin",
            label: "Margin",
            componentProps: {
                actionType: "view",
                code: "inv_snap_margin",
                parameters: reportParmaters,
                toolbarVisible: false
            },
            renderComponent: ReportManager
        }
    ];

    const [fd, refresh] = useFormData(props.form, props.id);
    const item = (fd?.data || {}) as InventoryItem;
    let [openListTabs, activeListTab, controllerList] = useTabController(REPORT_TABS as TabData[], REPORT_TABS[0].tkey);
    let [openGraphTabs, activeGraphTab, controllerGraph] = useTabController(GRAPH_TABS as TabData[], GRAPH_TABS[0].tkey);


    return (

        <div className="inventory-snap">
            <div className="area">
                <div className="area-hz-cont">
                    <div className="hz-sec bdr-rnd">
                        <div className="header-bar">
                            <div className='title'>
                                {item.auto_name || "-"}
                            </div>
                            <div className='info'>
                                {item.category_id_auto_name ? <div className='info-pill'>{item.category_id_auto_name}</div> : null}
                                <div className={`info-pill ${item.active ? "go-green" : "go-red"}`}>
                                    {item.active == 'Yes' ? (
                                        <>
                                            <img src={PublicIcon?.common.filledDoneIcon} alt="icon" />
                                            <span>Active</span>
                                        </>
                                    ) : "Inactive"}
                                </div>
                                <img
                                    src={patient.editsm}
                                    onClick={() => { props.tabViewActions.openTab(props.id, item.auto_name, "edit", props.form, {}) }}
                                />
                            </div>
                        </div>
                        <div className='item-area'>
                            <div className='info-items'>
                                <div className='item-column'>
                                    <SnapDetailItem label={"NDC"} value={getPrettyNDC(item.ndc)} />
                                    <SnapDetailItem label={"MANUFACTURER"} value={item.manufacturer_id_auto_name} />
                                    <SnapDetailItem label={"QUANTITY"} sub={"per each"} value={getPrettyNumber(item.quantity_each)} />
                                </div>
                                <div className='item-column'>
                                    <SnapDetailItem label={"HCPC"} value={item.hcpc_id_auto_name || ""} />
                                    <SnapDetailItem label={"HCPC Units"} sub={'each'} value={getPrettyNumber(item.hcpc_quantity)} />
                                </div>
                            </div>
                            <SnapNote label={"NOTES"} value={item.note || ""} />
                        </div>
                    </div>
                    <div className="hz-sec bdr-rnd">
                        <div className="header-bar">
                            <div className='title'>
                                Pricing
                            </div>
                            <div className='info'>
                                {item.price_code_id_auto_name ? <div className='info-pill'>{item.price_code_id_auto_name}</div> : null}
                                {item.revenue_code_id_auto_name ? <div className='info-pill'>{item.revenue_code_id_auto_name}</div> : null}
                            </div>
                        </div>
                        <div className='item-area'>
                            <div className='info-items'>
                                <div className='item-column'>
                                    <SnapDetailItem label={"ASP"} sub={"each"} value={getPrettyNumber(item.asp_price, "currency")} />
                                    <SnapDetailItem label={"WAC"} sub={"each"} value={getPrettyNumber(item.wac_price, "currency")} />
                                    <SnapDetailItem label={"AWP"} sub={"each"} value={getPrettyNumber(item.awp_price, "currency")} />

                                    <SnapDetailItem label={"LIST PRICE"} sub={"each"} value={getPrettyNumber(item.list_price, "currency")} />
                                    <SnapDetailItem label={"MCR CHARGE"} sub={"unit"} value={getPrettyNumber(item.mcr_charge, "currency")} />
                                </div>
                                <div className='item-column'>
                                    <SnapDetailItem label={"ADDTL PRICE 1"} value={getPrettyNumber(item.add_price1, "currency")} />
                                    <SnapDetailItem label={"ADDTL PRICE 2"} value={getPrettyNumber(item.add_price2, "currency")} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="area-hz-cont">
                    <div className="hz-sec">
                        <div className='list-bar'>
                            <TabList
                                activeTabId={activeListTab}
                                openTabs={openListTabs}
                                styles={{
                                    tabListStyle: "lvl-2-tab-list",
                                }}
                                optionsProps={{
                                    enabled: false
                                }}
                                addProps={{
                                    enabled: false
                                }}
                                tabCanClose={false}
                                draggable={false}
                                onTabClick={(tab: TabData) => {
                                    controllerList.setActiveTabId(tab.tkey);
                                }}
                            />
                        </div>
                        {
                            openListTabs.map((tab, index) => {
                                const RenderComponent = tab.renderComponent as FC<unknown>;
                                return (
                                    <div className='qm-container' key={tab.tkey} style={activeListTab == tab.tkey ? undefined : { display: "none" }}>
                                        <RenderComponent {...(tab.componentProps || {})} />
                                    </div>
                                );
                            })
                        }
                    </div>
                    <div className="hz-sec">
                        <div className='list-bar'>
                            <TabList
                                activeTabId={activeGraphTab}
                                openTabs={openGraphTabs}
                                styles={{
                                    tabListStyle: "lvl-2-tab-list",
                                }}
                                optionsProps={{
                                    enabled: false
                                }}
                                addProps={{
                                    enabled: false
                                }}
                                tabCanClose={false}
                                draggable={false}
                                onTabClick={(tab: TabData) => {
                                    controllerGraph.setActiveTabId(tab.tkey);
                                }}
                            />
                        </div>
                        {
                            openGraphTabs.map((tab, index) => {
                                const RenderComponent = tab.renderComponent as FC<unknown>;
                                return (
                                    <div className='qm-container' key={tab.tkey} style={activeGraphTab == tab.tkey ? undefined : { display: "none" }}>
                                        <RenderComponent {...(tab.componentProps || {})} />
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InventorySupplySnapshot;