@import (reference) '../../less/style/main.less';

.site-selector {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    height: fit-content;
    width: fit-content;
    border: 1px solid @purple;
    gap: 10px;

    .site-search>input {
        .form-input-field;
    }

    .site-selector-checkbox {
        .checkbox-one;
    }

    .site-selector-label {
        align-items: center;

        span {
            .para-two;
            font-weight: 500;
        }
    }

    label {
        display: flex;
        gap: 10px;
    }

    .select-opts {
        display: flex;
        flex-direction: column;
        overflow: auto;
        max-height: 150px;
        min-height: 150px;
    }

    .site-action {
        display: flex;
        justify-content: flex-end;

        button {
            .btn-primary;
        }
    }
}

.site-selector-tab-btn {
    color: #000000 !important;
    background-color: #FEC866;
    border-radius: 20px;

    img {
        width: 20px;
        height: 20px;
    }

    .partial-sites-selected {
        display: flex;
        width: 10px;
        height: 10px;
        background: red;
        z-index: 1;
        border-radius: 10px;
        flex-shrink: 0;
        margin-right: -9px;
        margin-top: -25px;
    }
}