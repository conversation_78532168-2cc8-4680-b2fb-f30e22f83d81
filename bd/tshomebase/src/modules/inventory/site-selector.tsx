import React, { useEffect, useRef, useState } from "react";
import "./site-selector.less";
import { IFormData, useFormFilters } from "@hooks/form-data";
import { Popover } from "@mui/material";
import { useToggle } from "usehooks-ts";
import filterIcon from 'data-url:@public/icons/list/filter.svg'

type SiteSelectorProps = {
    onSiteChange: (ids: (string | number)[], autoName: string[]) => void;
}

export const SiteSelector = (props: SiteSelectorProps) => {
    const [fd] = useFormFilters("site", {
        filter: {},
    });
    const allSiteIds = fd.data.map(s => s.id as number)

    const [selectedSites, setSelectedSites] = useState(allSiteIds || []);
    const [previousSelectedSites, setPreviousSelectedSites] = useState(allSiteIds || []);
    const [searchTerm, setSearchTerm] = useState("");
    const [filterOpen, toggleFilterOpen] = useToggle(false);
    const anchorEl = useRef(null);

    useEffect(() => {
        const allSiteIds = fd.data.map((site) => site.id as number);
        const allSiteAutoNames = fd.data.map((site) => site.auto_name);
        setSelectedSites(allSiteIds);
        props.onSiteChange(allSiteIds, allSiteAutoNames)
        setPreviousSelectedSites(allSiteIds);
    }, [fd?.data?.length]);


    const handleSiteSelection = (siteId: number) => {
        const updatedSelectedSites = selectedSites.includes(siteId)
            ? selectedSites.filter((id) => id !== siteId)
            : [...selectedSites, siteId];
        setSelectedSites(updatedSelectedSites);
    };

    const handleSelectAll = () => {
        setSelectedSites(allSiteIds);
    };

    const getSiteName = (siteId: number) => {
        const site = fd.data.find((site) => site.id === siteId);
        return site ? site.name : "";
    }

    const handleUnselectAll = () => {
        setSelectedSites([]);
    };



    const filteredSites = fd.data.filter((site) =>
        site?.name?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleApply = () => {
        if (selectedSites.length > 0) {
            // Perform the action here
            toggleFilterOpen();
            setPreviousSelectedSites(selectedSites);
            const siteAutoNames = selectedSites.map((siteId) => {
                const site = fd.data.find((site) => site.id === siteId);
                return site ? site.auto_name : "";
            });
            props.onSiteChange(selectedSites, siteAutoNames)
        } else {
            // window.prettyError("No site selected", "Plesase select atleast one site")
            // Show an error message or handle the case where no site is selected
        }
    };

    const handlePopoverClose = () => {
        setSelectedSites(previousSelectedSites);
        toggleFilterOpen();
    };

    return (
        <>
            <div className="tab-list-button site-selector-tab-btn" ref={anchorEl} onClick={handlePopoverClose}>
                <img className="tab-avatar active" src={filterIcon} />
                <div className="tab-label" aria-label="Site Selector">{selectedSites.length == 1 ? getSiteName(selectedSites[0]) : 'Sites'}</div>
                {fd.data.length != selectedSites.length && <div className="partial-sites-selected"></div>}
            </div>
            <Popover
                id="sc-popover"
                open={filterOpen}
                anchorEl={anchorEl.current}
                onClose={handlePopoverClose}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                }}

            >
                <div className="site-selector">
                    <div className="site-search">
                        <input type="text" placeholder="Search" value={searchTerm} onChange={(event) => {
                            setSearchTerm(event.target.value);
                        }} />
                    </div>
                    <div className="select-opts">
                        <label className="site-selector-label">
                            <input
                                type="checkbox"
                                className="site-selector-checkbox"
                                checked={selectedSites.length === fd.data.length}
                                onChange={selectedSites.length === fd.data.length ? handleUnselectAll : handleSelectAll}
                            />
                            <span>Select/Unselect All</span>
                        </label>
                        {filteredSites.map((site) => (
                            <label key={site.id} className="site-selector-label">
                                <input
                                    type="checkbox"
                                    className="site-selector-checkbox"
                                    checked={selectedSites.includes(site.id as number)}
                                    onChange={() => handleSiteSelection(site.id as number)}
                                />
                                <span>{site.name}</span>
                            </label>
                        ))}
                    </div>
                    <div className="site-action">
                        <button onClick={handleApply}>Apply</button>
                    </div>
                </div>
            </Popover>
        </>
    );
};