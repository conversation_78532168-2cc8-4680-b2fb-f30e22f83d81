import { useEffect } from "react";
import { useNavigation } from "@core/navigation";
import { RoutedComponentProps } from "@typedefs/routed-component";
import "./in-stock-view.less";
import QueryListView from "@blocks/query-list-view/query-list-view";

interface InStockViewProps extends RoutedComponentProps {
	name?: string;
}

export const InStockView: React.FC<InStockViewProps> = (props) => {
	const nav = useNavigation(props, "/");

	useEffect(() => {
		if (!props.navPath?.length) {
			return;
		}
		const r = props.navPath;
	}, [nav.globalNav.lrct]);
	return <QueryListView {...props} code="inv_in_stock_report" label={props.name} />;
};
