@import (reference) '../../../less/style/main.less';


.barcodeScannerContainer {
    height: 100%;
    padding: 12px 20px;
    display: flex;
    flex-direction: column;
    gap: 40px;

    .scanItemContainer {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding-bottom: 12px;

        .itemBox {
            width: auto;
            place-self: flex-start;
            .pills-secondary();
            background-color: #D6D6D6;
            color: #747477;

            &.active {
                .pills-secondary();
                background-color: @pills-bg-color;
                color: @pills-text-color;
            }
        }


        @media (max-width: 576px) {
            gap: 5px;
            height: auto;
            justify-content: center;


            .itemBox {
                .centered-col;
                background-color: transparent;
                min-width: 22%;
                gap: 10px;

                &.active {
                    .pills-secondary();
                    background-color: transparent;
                    color: @pills-text-color;

                    .iconContainer {
                        background-color: @pills-bg-color;


                    }

                }


                .iconContainer {

                    padding: 10px;
                    background-color: #D6D6D6;
                    border-radius: @border-radius-6;

                    i {
                        font-size: 32px;
                    }
                }
            }

        }

    }


    .moduleTodo {
        display: flex;
        flex-direction: column;
        flex: 1;
        height: 80vh;
        width: 100%;
        justify-content: center;
        align-items: center;
        font-size: 50px;
    }


}