import { useEffect, useState } from "react";
import * as style from "./style.module.less";
import { Button } from "@mui/material";
import ZbarWasmScanner from "@components/common/zbar-wasm-scanner";
import { convertToAscii } from "@utils/fx";

interface MoveInventoryItemPropsTypes {
    variant?: string;
    className?: string;
    children?: string;
    title?: string;
    loading?: boolean;
}

const MoveInventoryItem = (props: MoveInventoryItemPropsTypes) => {
    const { title, className } = props;
    const [isScanning, setIsScanning] = useState(false);
    const [result, setResult] = useState<any[]>([]);
    const [readScannedData, setReadScannedData] = useState<string>("");


    const onScan = () => {
        setIsScanning(true);
    };


    useEffect(() => {
        if (result?.length) {
            if (convertToAscii(result)?.length) {
                setReadScannedData(convertToAscii(result));
            }
        }
    }, [result]);


    return (
        <div className={`${style.barCodeScannerContainer} ${className}`} >
            <div className={style.heading}>{title}</div>
            <br />
            <table className={style.barCodeScannerLayout} >
                <tbody>
                    <tr>
                        <td> <p>Item</p> </td>
                        <td><input type="text" placeholder="Item" value={readScannedData} onChange={(e) => {
                            setReadScannedData(e.target.value);
                        }}
                        />
                        </td>
                        <td>
                            <Button onClick={onScan}>Scan</Button>
                        </td>
                    </tr>
                    <tr>
                        <td> <p>Quantity</p> </td>
                        <td><input type="text" placeholder="Quantity" /></td>
                        <td />
                    </tr>
                    <tr>
                        <td><p>From Bin</p></td>
                        <td>
                            <select id="frombin" >
                                <option value="bin1">Bin 1</option>
                                <option value="bin2">Bin 2</option>
                                <option value="bin3">Bin 3</option>
                                <option value="bin4">Bin 4</option>
                                <option value="bin5">Bin 5</option>
                                <option value="bin6">Bin 6</option>
                                <option value="bin7">Bin 7</option>
                                <option value="bin8">Bin 8</option>
                                <option value="bin9">Bin 9</option>
                                <option value="bin10">Bin 10</option>
                            </select>
                        </td>
                        <td>
                            <Button onClick={onScan}>Scan</Button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p>To Bin</p>
                        </td>
                        <td>
                            <select id="frombin" >
                                <option value="bin1">Bin 1</option>
                                <option value="bin2">Bin 2</option>
                                <option value="bin3">Bin 3</option>
                                <option value="bin4">Bin 4</option>
                                <option value="bin5">Bin 5</option>
                                <option value="bin6">Bin 6</option>
                                <option value="bin7">Bin 7</option>
                                <option value="bin8">Bin 8</option>
                                <option value="bin9">Bin 9</option>
                                <option value="bin10">Bin 10</option>
                            </select>
                        </td>
                        <td>
                            <Button onClick={onScan}>Scan</Button>
                        </td>
                    </tr>
                    <tr>
                        <td />
                        <td>
                            <Button className={style.moveBtn} onClick={onScan}>Move</Button>
                        </td>
                        <td />
                    </tr>
                </tbody>
            </table>

            {
                isScanning && (
                    <div>
                        <ZbarWasmScanner onClose={setIsScanning} setScannedData={setResult} open={isScanning} />
                        {/* <Button className={style.moveBtn} onClick={() => setIsScanning(false)}>Move</Button> */}
                    </div>
                ) || null
            }
        </div>
    );
};


export default MoveInventoryItem;

