import React, { useState } from "react";
import * as style from "./style.module.less";
import NoDataFound from "@components/common/no-data-found";
import { useWindowSize } from "@hooks/index";
import MoveInventoryItem from "./move-scan-inventory/index";

const ScanInventoryItems = () => {
	const { width } = useWindowSize();
	const [activeItem, setActiveItem] = useState(null);
	const [itemIndex, setItemIndex] = useState(0);

	const scanItems = [
		{
			icon: <i className="fa fa-hospital-o" aria-hidden="true" />,
			label: "Stock"
		},
		{
			icon: <i className="fa fa-exchange" aria-hidden="true" />,
			label: "Move"
		},
		{
			icon: <i className="fa fa-th-large" />,
			label: "Receive"
		},
		{
			icon: <i className="fa fa-ticket" />,
			label: "Pick"
		},
		{
			icon: <i className="fa fa-truck" />,
			label: "Ship"
		},
		{
			icon: <i className="fa fa-sliders" />,
			label: "Adjust"
		},
		{
			icon: <i className="fa fa-sliders" />,
			label: "Cycle Count"
		},
	];

	return (
		<div className={style.barcodeScannerContainer} >
			<div className={style.scanItemContainer}>
				{
					scanItems?.length > 0 && scanItems?.map((item, index) => (
						<div key={item.label} className={`${style.itemBox} ${itemIndex === index && style.active}`} onClick={() => {
							setItemIndex(index);
							setActiveItem(item);
						}}
						>
							{width < 576 &&
								<div className={style.iconContainer} >
									{item?.icon}
								</div>
							}
							<span>{item?.label}</span>
						</div>
					))
					||
					<NoDataFound />
				}
			</div>

			{
				activeItem?.label === "Move" &&
				<div className={style.itemTodo}>
					<MoveInventoryItem title={activeItem?.label} />
				</div>
				||
				<div className={style.moduleTodo}>
					TODO
				</div>
			}

		</div>
	);
};

export default ScanInventoryItems;