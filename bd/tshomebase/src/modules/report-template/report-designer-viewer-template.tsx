/* eslint-disable @typescript-eslint/no-floating-promises */
import type { FC } from "react";
import React, { useCallback, useEffect, useState } from "react";
import type { Viewer as ReportViewer } from "@mescius/activereportsjs-react";
import { Designer } from "@components/arjs";
import type { AppMode, ReportDescriptor } from "@typedefs/arjs";
import type { SaveNewReportInfo, ReportDefinition } from "@mescius/activereportsjs/reportdesigner";
import { getReportInfo, updateReport } from "./report-actions";
import "@mescius/activereportsjs/styles/ar-js-ui.css";
import "@mescius/activereportsjs/styles/ar-js-designer.css";
import "@mescius/activereportsjs/styles/ar-js-viewer.css";
import { useNavigation } from "@core/navigation";
import { Viewer } from "@components/arjs/viewer";

interface propsObj {
	id: number | string
}

export const ReportDesignerViewerTemplate: FC<propsObj> = (props) => {
	useNavigation(props, `/${props.id}/design`);
	const counter = React.useRef(0);
	// useNavigation(props)
	const viewerRef = React.useRef<ReportViewer>(null);
	const [report, setReport] = useState<ReportDescriptor | null>(null);
	const [mode, setMode] = useState<AppMode>("designer");

	const onCreateReport = () => {
		const isPageLess = report?.is_page_less === 'Yes' || false;
		const CPLReportTemplate = {
			Name: report?.name,
			Body: {
				Width: isPageLess ? null : `${report?.page_width}in`,
				Height: isPageLess ? null : `${report?.page_height}in`
			},
			Page: {
				"PageWidth": isPageLess ? null : `${report?.page_width}in`,
				"PageHeight": isPageLess ? null : `${report?.page_height}in`,
				"RightMargin": "0in",
				"LeftMargin": "0in",
				"TopMargin": "0in",
				"BottomMargin": "0in",
				"Columns": 1,
				"ColumnSpacing": "0.5in"
			}
		};
		const reportId = report?.name;
		const reportObj = {
			definition: CPLReportTemplate,
			id: reportId,
			displayName: reportId,
		}
		if (isPageLess) {
			reportObj['reportType'] = "Pageless";
		}
		return Promise.resolve(reportObj);
	};

	const onSaveReport = async (info: SaveNewReportInfo) => {
		// return
		const definition = info.definition;
		const body: ReportDescriptor = {
			json_data: definition
		};
		updateReport(props.id, body).then((data) => {
		}).catch(_error => Promise.reject({ success: false, error: _error }));
		return Promise.resolve({ displayName: report?.name });

	};

	const onSaveAsReport = async (info: SaveNewReportInfo) => {
		// return
		const reportId = `NewReport${++counter.current}`;
		const definition = info.definition;
		const body: ReportDescriptor = {
			json_data: definition
		};
		updateReport(props.id, body).then((data) => {
		}).catch(_error => Promise.reject({ success: false, error: _error }));
		return Promise.resolve({ id: reportId, displayName: reportId });
	};
	const openReport = useCallback(async (id: number) => {
		try {
			const data = await getReportInfo(id) as ReportDescriptor;
			setReport(data);
			if (mode === "viewer" && data.json_data) {
				viewerRef?.current?.Viewer?.open(data.json_data);
			}
			return Promise.resolve();
		} catch (err) {
			console.log(err);
		}
	}, [mode]);

	const onReportPreview = async (report: ReportDefinition) => {
		if (report) {
			setMode("viewer");
			viewerRef?.current?.Viewer?.open(report.definition);
		}
	};

	useEffect(() => {
		openReport(props.id);
	}, [mode, props.id, openReport]);

	return (
		<div>
			{
				mode == "designer" && (
					<div id="viewer-host">
						{
							report?.json_data && Object.keys(report.json_data).length > 0 ? <Designer report={report} onSave={onSaveReport} onSaveAs={onSaveAsReport} onRender={onReportPreview} /> :
								<Designer onCreate={onCreateReport} onSave={onSaveReport} onSaveAs={onSaveAsReport} />
						}
					</div>

				)
			}
			{
				mode == "viewer" && report && (
					<div id="viewer-host">
						<Viewer report={report} toolbarVisible={true} sidebarVisible={true} zoom="FitPage" onEdit={() => setMode("designer")} />
						{/* <ReportViewer ref={viewerRef} /> */}
					</div>)
			}

		</div>
	);
};