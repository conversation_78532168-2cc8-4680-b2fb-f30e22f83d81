import type { ReportDescriptor } from '@typedefs/arjs'

export const updateReport = async (id: number, data: ReportDescriptor) => {
	window.Ajax.sync({ url: `/form/template_report/${id}`, type: 'PUT', data: JSON.stringify({ json_data: data.json_data }), contentType: 'application/json' })
}

export const getReportInfo = async (report_id: unknown) => new Promise((resolve, reject) => {
	window.Ajax.async({
		url: `/form/template_report/${report_id}`,
		success: function (data: unknown) {
			if (typeof data === 'object' && Array.isArray(data) && data.length > 0) {
				resolve(data[0]);
			} else {
				console.error('Invalid data received:', data);
				reject("Invalid data received");
			}
		},
		error: function (error: unknown) {
			console.error('Error:', error);
			reject(error);
		},
	});
});


