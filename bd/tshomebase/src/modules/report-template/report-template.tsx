/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable indent */
/* eslint-disable @typescript-eslint/no-floating-promises */
import { useEffect } from "react"
import { useNavigation } from "@core/navigation"
import type { RoutedComponentProps } from "@typedefs/routed-component"
import { DSLTabView } from "@blocks/dsl-tab-view/dsl-tab-view";

export const ReportTemplate = (props: RoutedComponentProps) => {
	const nav = useNavigation(props, '/template_report')

	useEffect(() => {
		if (props.navToURL?.startsWith('/template_report')) {
			props.setActiveTab?.('template_report')
		}
	}, [props, props.navToURL])


	return (
		// eslint-disable-next-line react/react-in-jsx-scope
		<div>
			<DSLTabView styles={undefined}  {...props} {...nav} form="report" viewMode='snap' TabRenderComponent={ReportTemplate} />
		</div>
	)
}
