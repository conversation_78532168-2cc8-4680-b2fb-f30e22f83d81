.queue-master-details {
	background: #fff;
	display: flex;
	flex-direction: column;
	gap: 16px;
	.dsl-advanced-grid {
		.ag-header-icon {
			visibility: hidden;
		}
		.ag-center-cols-viewport {
			min-height: 44px !important;
		}
	}

	.queue-tabs {
		display: flex;
		flex-direction: row;
		gap: 12px;
		padding-bottom: 10px;
		.tab {
			padding: 0 4px 8px 4px;
			gap: 8px;
			cursor: pointer;
			color: #717680;
			font-size: 14px;
			position: relative;

			&.active {
				color: #535862;
				font-weight: 500;

				&:after {
					content: "";
					position: absolute;
					bottom: -1px;
					left: 0;
					right: 0;
					height: 2px;
					background-color: #535862;
				}
			}
		}
	}

	.node-config-options {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		padding: 8px 16px;
		gap: 10px;

		.config-options-container {
			background-color: #fff;
			display: flex;
			flex-direction: column;
			display: flex;
			flex-direction: row;
			box-shadow: 0px 1px 2px 1px #38383814 inset;
			padding: 8px 16px;
			gap: 8px;
			border-radius: 8px;

			.switch-container {
				display: flex;
				align-items: center;

				span {
					font-size: 14px;
					color: var(--color-text-500);
				}

				// Updated MUI Switch styling
				.MuiSwitch-root {
					width: 44px;
					height: 26px;
					padding: 0;
					margin: 8px;

					.MuiSwitch-track {
						background-color: var(--color-text-100);
						border-radius: 13px;
						opacity: 1;
						transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1);
					}
				}

				.MuiSwitch-switchBase {
					padding: 0;
					margin: 2px;
					transition-duration: 300ms;

					&.Mui-checked {
						transform: translateX(18px);
						color: #fff;

						+ .MuiSwitch-track {
							background-color: var(--color-tertiary);
							opacity: 1;
							border: 0;
						}

						.MuiSwitch-thumb {
							background-color: #fff;
						}
					}
				}

				.MuiSwitch-thumb {
					box-sizing: border-box;
					width: 22px;
					height: 22px;
					background-color: #fff;
				}
			}

			.config-separators {
				flex-grow: 2;
				flex: 1;
				display: flex;
				flex-direction: row;
				padding: 10px 0 40px 0;

				.wf-label-container {
					flex: 1;
					box-sizing: border-box;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					position: relative;
					width: 100%;
					margin-top: auto;

					.wf-flabel {
						position: absolute !important;
						top: var(--spacing-standard);
						left: var(--spacing-xlarge);
						font-size: var(--font-size-xsmall);
						font-weight: var(--font-weight-regular);
						color: var(--color-text);
						z-index: 1;
						pointer-events: none;
					}

					.wf-label-field-container {
						border: none;
					}
				}
			}
		}
	}

	.assign-to-container {
		display: flex;
		flex-direction: column;
		gap: 16px;
		flex-grow: 1;
	}

	.field-select {
		width: 100%;
	}
}

.queue-preview-icon-container {
	cursor: pointer;

	* {
		cursor: pointer;
	}
}

.attachment-viewer-popover {
	border-radius: 12px;

	.MuiPaper-root {
		border-radius: 12px;
	}
}

.attachment-viewer-container {
	background: white;
	max-height: calc(100vh - 300px);
	width: 500px;
	border-radius: 12px;
	border: 1px solid #B6BAC2;
	padding: 16px;

	.pdf-viewer {
		height: 100%;

		.pdf-controls {
			display: flex;
			align-items: center;
			gap: 8px;

			button {
				// Add button styles if needed
				&:disabled {
					// Add disabled button styles if needed
				}
			}

			span {
				// Add page counter styles if needed
			}

			i {
				// Add icon styles if needed
			}
		}
	}
}
