import { useContext, useEffect, useMemo, useState } from "react";
import { useNavigation } from "@core/navigation";
import React from "react";
import type { FC } from "react";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import type { WorkFlowData } from "../workflow/types";
import "./queue.less";
import { SharedDataStore } from "@contexts/index";
import NoDataFound from "@components/common/no-data-found";
import LoaderNoData from "@components/common/loader-no-data";
import { blockUntilTimePassed } from "@utils/fx";
import { toast } from "@components/toast";
import { generateBoardSchema, QueueListSchema } from "@modules/queue/helper";
import { QueueViewer } from "@modules/queue/queue-viewer";
import { Task } from "@modules/task/Index";
import { SubTab, Tab, Tabs } from "@blocks/vertical-tab-view/components/tabs";
import NavIcon from "@public/icons/nav";

type QueueProps = RoutedComponentProps;

export type QueueNodeSchema = QueueListSchema;

export interface GroupSchema {
	label: string;
	id: string;
	icon?: string;
	nodes: QueueNodeSchema[];
	path: string;
}
export interface BoardSchema {
	label: string;
	id: string;
	path: string;
	icon?: string;
	groups: GroupSchema[];
}


export const Queue: FC<QueueProps> = (props) => {
	const [activeURL, setActiveURL] = useState("");

	const nav = useNavigation(props, `/queue/`);

	const wfData = useContext(SharedDataStore).queues;

	const [activeTab, setActiveTab] = useState<{
		type: "workflow" | "other";
		mainId: string;
		subId?: string;
	}>({
		type: "workflow",
		mainId: "",
		subId: "",
	});

	const [focusedBoard, setFocusedBoard] = useState<string | null>(null);

	const [openedBoards, setOpenedBoards] = useState<string[]>([]);

	const [activeSubTab, setActiveSubTab] = useState<Record<string, string>>({});
	const changeTab = (type: "workflow" | "other", mainId: string, subId?: string) => {
		if (subId) {
			setActiveSubTab({
				...activeSubTab,
				[mainId]: subId,
			});
		}

		setActiveTab({
			type,
			mainId,
			subId,
		});
	};

	useEffect(() => {
		if (!props.navToURL?.startsWith("/queue")) {
			return;
		}
		props.setActiveTab?.("queue");
		const [type, mainId, subId] = props.navToURL?.split("/").slice(1) || [];
		if (type && mainId) {
			changeTab(type as "workflow" | "other", mainId, subId);
		}
	}, [nav.globalNav.lrct]);

	const generateMap = () => {
		const boards: BoardSchema[] = [];
		const workFlows = wfData.data as WorkFlowData[];
		workFlows.forEach((wf) => {
			const board = generateBoardSchema(wf);
			boards.push(board);
		});
		return boards;
	};
	const map = useMemo(() => {
		const currentMap = _.cloneDeep(generateMap());
		for (const boardIndex in currentMap) {
			if (window.Auth?.can_access_path(currentMap[boardIndex].path)) continue;
			for (const groupIndex in currentMap[boardIndex].groups) {
				if (window.Auth?.can_access_path(currentMap[boardIndex].groups[groupIndex].path)) continue;
				const filteredNodes = currentMap[boardIndex].groups[groupIndex].nodes.filter((node) => window.Auth?.can_access_path(node.path));
				currentMap[boardIndex].groups[groupIndex].nodes = filteredNodes;
			}
			currentMap[boardIndex].groups = currentMap[boardIndex].groups.filter((group) => group.nodes.length > 0);
		}
		return currentMap.filter((board) => board.groups.length > 0);
	}, [wfData.data, wfData.state]);

	useEffect(() => {
		if (map.length > 0) {
			changeTab("workflow", map[0].id, map[0].groups?.[0]?.id);
			setOpenedBoards([map[0].id]);
			setFocusedBoard(map[0].id);
		}
	}, [map]);

	if (wfData.state === "loading") {
		return <LoaderNoData loading />;
	}
	if (wfData.state === "error") {
		return <NoDataFound text="Error fetching data." />;
	}
	if (map.length === 0) {
		return <NoDataFound text="No queues available." />;
	}

	return (
		<div className="workflow-queue-container">
			<div className="workflow-nav-bar">
				<div className="top-bar">
					<div className="header">
						<img src={NavIcon.workflow} alt="workflow" />
						<div className="title">Workflow</div>
					</div>
					<Tabs>
						{map.map((board) => {
							const isOpened = openedBoards.includes(board.id);
							return (
								<Tab
									key={board.id}
									label={board.label}
									count={board.groups.length}
									icon={board.icon || "fa-solid fa-database"}
									onClick={() => {
										let boardsState = [...openedBoards];
										let boardNow = focusedBoard;

										if (boardsState.length === 1 && boardsState.includes(board.id)) {
											boardNow = board.id;
										} else if (boardsState.includes(board.id)) {
											boardsState = boardsState.filter((id) => id !== board.id);
											boardNow = boardsState[boardsState.length - 1] || null;
										} else {
											boardsState.push(board.id);
											boardNow = board.id;
										}
										setOpenedBoards(boardsState);
										setFocusedBoard(boardNow);

										if (boardNow) {
											changeTab(
												"workflow",
												boardNow,
												activeSubTab[boardNow] ||
												map.find((b) => b.id === boardNow)?.groups?.[0]?.id
											);
										}
									}}
									active={isOpened}
									hasActiveSubTab={isOpened}
								>
									{board.groups.map((group) => (
										<SubTab
											key={group.id}
											label={group.label}
											count={group.nodes.length}
											icon={group.icon || "fa-solid fa-database"}
											active={activeSubTab[board.id] === group.id && focusedBoard === board.id}
											parentActive={isOpened}
											onClick={() => {
												setFocusedBoard(board.id);
												changeTab("workflow", board.id, group.id);
											}}
										/>
									))}
								</Tab>
							);
						})}
					</Tabs>
				</div>
				<div className="bottom-bar">
					<Tabs>
						<Tab
							label="To-Do"
							count={5}
							active={activeTab.type === "other" && activeTab.mainId === "todo"}
							icon=""
							onClick={() => {
								changeTab("other", "todo");
							}}
						/>
					</Tabs>
				</div>
			</div>
			<div className="workflow-area">
				{map.map((board) =>
					board.groups.map((group) => (
						<div
							key={board.id + group.id}
							style={{
								height: "100%",
								display:
									activeTab.mainId === board.id && activeTab.subId === group.id ? undefined : "none",
							}}
						>
							<QueueViewer
								key={board.id + group.id}
								{...nav}
								isActive={activeSubTab[board.id] === group.id}
								isParentActive={activeTab.type === "workflow" && activeTab.mainId === board.id}
								swimlanes={group.nodes}
							/>
						</div>
					))
				)}
				<div
					style={{
						height: "100%",
						display: activeTab.type === "other" && activeTab.mainId === "todo" ? undefined : "none",
					}}
				>
					<Task
						nav={nav}
						isActive={activeTab.type === "other" && activeTab.mainId === "todo"}
						isParentActive={true}
					/>
				</div>
			</div>
		</div>
	);
};
