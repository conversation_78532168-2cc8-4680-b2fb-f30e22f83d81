import React, { useContext } from "react";
import "./print_labels_queue.less";
import { DSLListViewProps } from "../../../blocks/dsl-list-view/dsl-list-view";
import { showToastError } from "@utils/fx";
import { request } from "@core/request";
import { openARJsPopover } from "@components/popups/arjs-popover/arjs-popover";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import { openPrinterSelectionModal } from "./select-printer-picklist";
import { PreferenceSettingContext } from "@contexts/preference/provider";
interface QueueActionButton {
    label: string;
    icon?: string;
}
interface PrintLabelsQueueActionsProps {
    buttons?: QueueActionButton[];
    selectedRowId?: string;
    gridRef?: AdvancedGridRef;
    getSelectedRows?: () => any[];
}

export const PrintLabelsQueueActions: React.FC<PrintLabelsQueueActionsProps & DSLListViewProps> = (props) => {
    const { buttons = [], selectedRowId } = props;
    const [isLoading, preference, updatePref, refreshPref] = useContext(PreferenceSettingContext);

    const getPreferencePrinter = (printerType: string) => {
        try {
            const preference = window.UserPreference;
            const subformPrinters =
                preference.subform_printers?.length > 0
                    ? preference?.subform_printers?.find((p: any) => {
                        if (window?.sitesSelected && window?.sitesSelected?.length > 0 && window?.sitesSelected[0]) {
                            return p.site_id == window?.sitesSelected[0];
                        } else {
                            return p.site_id == null;
                        }
                    })
                    : null;
            return subformPrinters?.[printerType] || null;
        } catch (error) {
            console.error("Error getting preference printer:", error);
            return null;
        }
    }

    const setPreferencePrinter = async (printerType: string, printerId: string) => {
        try {
            const preference = window.UserPreference;
            const subformPrinters =
                preference.subform_printers?.length > 0
                    ? preference?.subform_printers?.find((p: any) => {
                        if (window?.sitesSelected && window?.sitesSelected?.length > 0 && window?.sitesSelected[0]) {
                            return p.site_id == window?.sitesSelected[0];
                        } else {
                            return p.site_id == null;
                        }
                    })
                    : null;
            updatePref(
                "subform_printers",
                [
                    {
                        ...subformPrinters,
                        [printerType]: printerId,
                    },
                ],
            );
        } catch (error) {
            console.error("Error setting preference printer:", error);
            showToastError("An error occurred while setting the preference printer");
        }
    }

    const onButtonClick = async () => {
        try {
            const selectedRows = props?.getSelectedRows?.() || [];
            if (!selectedRows || selectedRows.length === 0) {
                showToastError("Please select a row to generate pick list");
                return;
            }
            const deliveryTicketIds = selectedRows.map((row: any) => {
                if (selectedRowId) {
                    if (row.__row_data && row.__row_data?.[selectedRowId]) {
                        return row.__row_data[selectedRowId];
                    }
                }
                return null;
            }).filter((id: any) => id !== null);

            if (deliveryTicketIds.length === 0) {
                showToastError("No valid delivery ticket IDs found in selected rows");
                return;
            }

            if (!window.App?.user?.id) {
                showToastError("Please login to generate pick list");
                return;
            }

            const pickList = await request({
                url: "/query?legacy=true",
                method: "POST",
                data: {
                    code: "get_pick_list",
                    params: {
                        x1: `${window.App.user.id}`,
                        x2: deliveryTicketIds.join(",")
                    }
                }
            })
            if (pickList.data.length === 0) {
                showToastError("No pick list found for the selected delivery tickets");
                return;
            }

            const pickListData = pickList.data?.[0]?.result;

            if (!pickListData) {
                showToastError("No pick list found for the selected delivery tickets");
                return;
            }
            const data = {
                dataSource: {
                    pickList: pickListData
                }
            }
            const label = pickListData.user_name || null;
            const defaultPrinter = getPreferencePrinter("picklist_printer");
            const printer = await openPrinterSelectionModal(defaultPrinter);
            if (!printer || !printer.id) {
                showToastError("No printer selected");
                return;
            }
            await setPreferencePrinter("picklist_printer", printer.id);
            openARJsPopover(label, "pick_list", {}, { ...data, printer: printer?.auto_name || '' });

        } catch (error) {
            console.error("Error generating pick list:", error);
            showToastError("An error occurred while generating the pick list");
        }
    }

    return (
        <div className="queue-actions">
            {buttons.map((button, index) => (
                <div key={index} className="queue-action-btns" onClick={() => onButtonClick()}>
                    {button.icon && <i className={button.icon}></i>}
                    <span className="btn-label">{button.label}</span>
                </div>
            ))}
        </div>
    );
};

