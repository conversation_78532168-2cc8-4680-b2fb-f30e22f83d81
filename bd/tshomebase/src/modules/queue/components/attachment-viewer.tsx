import type React from "react"
import { useState, useEffect, useRef } from "react"
import "./attachment-viewer.less"
import type { CLFile } from "@typedefs/window"
import NoDataFound from "@components/common/no-data-found"
import type * as pdfjsLib from "pdfjs-dist"
import type { PDFDocumentProxy } from "pdfjs-dist/types/src/display/api"

import "react-pdf/dist/esm/Page/AnnotationLayer.css"
import "react-pdf/dist/esm/Page/TextLayer.css"
import { Height } from "@mui/icons-material"

export interface MediaRendererProps {
  file: CLFile
}

export const MediaRenderer: React.FC<MediaRendererProps> = ({ file }) => {
  const { filehash, filename, mimetype } = file || {}
  const ext = /(?:\.([^.]+))?$/.exec(filename)?.[1]?.toLowerCase()
  const link = `/api/file/secure/${filehash}`

  const containerRef = useRef<HTMLDivElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [pdf, setPdf] = useState<PDFDocumentProxy | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [numPages, setNumPages] = useState(0)
  const [scale, setScale] = useState(1)
  const [pdfData, setPdfData] = useState<string | null>(null)
  const [pdfjs, setPdfjs] = useState<typeof pdfjsLib | null>(null)

  useEffect(() => {
    const loadPdfJs = async () => {
      const pdfjs = await import("pdfjs-dist")
      const pdfjsWorker = await import("pdfjs-dist/build/pdf.worker.entry")
      pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
      setPdfjs(pdfjs)
    }
    loadPdfJs()
  }, [])

  useEffect(() => {
    const fetchPdf = async () => {
      try {
        const response = await fetch(link)
        const blob = await response.blob()
        const reader = new FileReader()
        reader.onloadend = () => {
          const base64data = reader.result as string
          setPdfData(base64data.split(",")[1])
        }
        reader.readAsDataURL(blob)
      } catch (error) {
        console.error("Error fetching PDF:", error)
      }
    }
    if (ext === "pdf") fetchPdf()
  }, [link, ext])

  useEffect(() => {
    if (!pdfData || !pdfjs) return

    const loadPDF = async () => {
      try {
        const loadingTask = pdfjs.getDocument({ data: atob(pdfData) })
        const pdf = await loadingTask.promise
        setPdf(pdf)
        setNumPages(pdf.numPages)
      } catch (error) {
        console.error("Error loading PDF:", error)
      }
    }

    loadPDF()
  }, [pdfData, pdfjs])

  useEffect(() => {
    const renderPage = async () => {
      if (pdf && canvasRef.current && containerRef.current) {
        const page = await pdf.getPage(currentPage)
        const viewport = page.getViewport({ scale: 1 })
        const canvas = canvasRef.current
        const context = canvas.getContext("2d")

        const containerWidth = containerRef.current.clientWidth
        const scaleX = containerWidth / viewport.width
        setScale(scaleX)

        const scaledViewport = page.getViewport({ scale: scaleX })

        canvas.height = scaledViewport.height
        canvas.width = scaledViewport.width

        const renderContext = {
          canvasContext: context!,
          viewport: scaledViewport,
        }

        await page.render(renderContext)
      }
    }

    renderPage()
  }, [pdf, currentPage])

  const changePage = (delta: number) => {
    setCurrentPage((prevPage) => {
      const newPage = prevPage + delta
      return newPage > 0 && newPage <= numPages ? newPage : prevPage
    })
  }

  const handleZoom = (delta: number) => {
    setScale((prevScale) => {
      const newScale = prevScale + delta
      return newScale >= 0.5 && newScale <= 3 ? newScale : prevScale
    })
  }

  if (ext === "pdf") {
    if (!pdfjs || !pdfData) {
      return <div>Loading PDF...</div>
    }

    return (
      <div style={{height: '100%'}}>
      <div className="pdf-viewer">
        <div className="pdf-container" ref={containerRef}>
          <canvas ref={canvasRef} className="pdf-canvas" />
        </div>
        <div className="pdf-controls">
          <button onClick={() => changePage(-1)} disabled={currentPage === 1}>
            <i className="fa-regular fa-arrow-left"></i>
          </button>
          <span>
            Page {currentPage} of {numPages}
          </span>
          <button onClick={() => changePage(1)} disabled={currentPage === numPages}>
            <i className="fa-regular fa-arrow-right"></i>
          </button>
          <button onClick={() => handleZoom(0.1)}>
            <i className="fa-regular fa-magnifying-glass-plus"></i>
          </button>
          <button onClick={() => handleZoom(-0.1)}>
            <i className="fa-regular fa-magnifying-glass-minus"></i>
          </button>
        </div>
      </div>
      </div>
    )
  } else if (ext && ["gif", "jpeg", "jpg", "png"].includes(ext)) {
    return <img src={link.trim() || "/placeholder.svg"} alt={filename} className="media-image" />
  } else if (ext && ["doc", "docx"].includes(ext)) {
    return (
      <iframe
        src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(window.location.origin + link)}`}
        width="100%"
        height="100%"
      ></iframe>
    )
  } else {
    return <NoDataFound text="Attachment unavailable!" />
  }
}