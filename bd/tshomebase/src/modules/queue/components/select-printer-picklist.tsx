import { SelectConfig } from "@dsl/fields/field-select-config";
import { useState, useEffect, FC } from "react";
import { getPrinters } from "@utils/qz-utils";
import { StylesConfig } from "react-select";
import { FieldSelect } from "@dsl/fields/field-select";
import { createPortalModal } from "@blocks/portal-modal/portal-modal";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import "./select-printer-picklist.less";
import { PopupModalRef } from "@blocks/portal-modal/portal-modal";

interface SelectedPrinter {
    id: string;
    auto_name: string;
}

interface PrinterSelectionModalProps {
    getModal: () => PopupModalRef;
    promise?: {
        resolve: (value: SelectedPrinter | undefined) => void;
        reject: (reason?: any) => void;
    };
    defaultPrinter?: string;
}

interface PrinterSelectionResponse {
    success: boolean;
    error?: string;
}

const PrinterSelectionModal: FC<PrinterSelectionModalProps> = (props) => {
    const [printers, setPrinters] = useState<Record<string, string>[]>([]);
    const [selectedPrinter, setSelectedPrinter] = useState<SelectedPrinter>({
        id: props.defaultPrinter || "",
        auto_name: props.defaultPrinter || ""
    });

    useEffect(() => {
        getPrinters()
            .then((prntr: any[]) => {
                const dp: Record<string, string>[] = prntr.map(p => ({
                    value: p.name,
                    label: p.name
                }));
                setPrinters(dp);
            })
            .catch((error: Error) => {
                console.error("QZ Tray: Unable to Fetch Printer List", error);
            });
    }, []);

    const handleApply = (): void => {
        if (selectedPrinter.id) {
            props.getModal().closeModal();
            props.promise?.resolve(selectedPrinter);
        }
    };

    const handleClose = (): void => {
        props.getModal().closeModal();
        props.promise?.resolve(undefined);
    };

    const selectStyleOver: StylesConfig = {
        control: (provided, state) => ({
            ...provided,
            height: "60px",
            borderRadius: "8px",
            border: `1px solid ${state.isFocused ? "#CBD5E0" : "#E3E5E8"}`,
            boxShadow: "0px 1px 2px 1px #38383814 inset",
            "&:hover": {
                borderColor: "#CBD5E0",
            },
            padding: "10px",
        }),
        valueContainer: (provided) => ({
            ...provided,
            marginTop: "18px",
            padding: "0px",
        }),
        placeholder: (provided) => ({
            ...provided,
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-start",
            lineHeight: "1.2",
            color: "#9B9FA8",
        }),
        input: (provided) => ({
            ...provided,
            margin: "0",
            padding: "0",
            color: "#414651",
        }),
        indicatorsContainer: (provided) => ({
            ...provided,
            height: "100%",
            color: "#717680",
            "&:hover": {
                color: "#4A5568",
            },
        }),
        indicatorSeparator: (provided) => ({
            ...provided,
            display: "none",
        }),
        clearIndicator: (provided) => ({
            ...provided,
            color: "#717680",
            "&:hover": {
                color: "#4A5568",
            },
        }),
        dropdownIndicator: (provided) => ({
            ...provided,
            color: "#717680",
            "&:hover": {
                color: "#4A5568",
            },
        }),
        menu: (provided) => ({
            ...provided,
            marginTop: "8px",
            borderRadius: "8px",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        }),
        menuList: (provided) => ({
            ...provided,
            padding: "8px",
        }),
        menuPortal: (provided) => ({
            ...provided,
            zIndex: 9999,
        }),
        option: (provided, state) => ({
            ...provided,
            padding: "12px 16px",
            borderRadius: "6px",
            backgroundColor: state.isSelected ? "#EDF2F7" : "transparent",
            color: "#4A5568",
            "&:hover": {
                backgroundColor: "#F7FAFC",
            },
        }),
    };

    return (
        <GenericCardContainer
            title="Select Pick List Printer"
            onClick={handleClose}
            icon={icons.common.crossIcon}
        >
            <div className="printer-selection-modal">
                <div className="printer-select-field-container">
                    <label className="printer-select-field-label">Select Pick List Printer</label>
                    <FieldSelect
                        form="none"
                        defaultValueSource={selectedPrinter.id}
                        defaultValueAutoName={selectedPrinter.auto_name}
                        multi={false}
                        disabled={false}
                        options={printers}
                        onChange={(val: string, an: string) => {
                            setSelectedPrinter({ id: val, auto_name: an });
                        }}
                        customStyles={selectStyleOver}
                        theme={SelectConfig.dsl.theme}
                        placeholder="Select Pick List Printer"
                    />
                </div>

                <div className="footer">
                    <button
                        className="btn-secondary"
                        onClick={handleClose}
                        type="button"
                    >
                        Close
                    </button>
                    <button
                        className="btn-primary"
                        onClick={handleApply}
                        disabled={!selectedPrinter.id}
                        type="button"
                    >
                        Apply
                    </button>
                </div>
            </div>
        </GenericCardContainer>
    );
};

export const openPrinterSelectionModal = (defaultPrinter: string): Promise<SelectedPrinter> => {
    return new Promise((resolve, reject) => createPortalModal(
        PrinterSelectionModal as FC<unknown>,
        {
            onRequestClose: () => {
                reject({
                    success: false,
                    error: "Selection cancelled"
                } as PrinterSelectionResponse);
            },
        },
        {
            promise: { resolve, reject },
            title: "Select Printer",
            size: "sm",
            defaultPrinter: defaultPrinter
        }
    ));
};