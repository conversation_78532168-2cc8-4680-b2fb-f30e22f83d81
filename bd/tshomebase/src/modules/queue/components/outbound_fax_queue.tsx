import React from "react";
import "./print_labels_queue.less";
import { DSLListViewProps } from "../../../blocks/dsl-list-view/dsl-list-view";
interface QueueActionButton {
	label: string;
	icon?: string;
}

interface SendFaxQueueActionsProps {
	buttons?: QueueActionButton[];
	selectedRowId?: string;
	gridRef?: any;
}

export const SendFaxQueueActions: React.FC<SendFaxQueueActionsProps & DSLListViewProps> = (props) => {
	const { buttons = [] } = props;
	const onButtonClick = async () => {
		let siteselected =
			window.sitesSelected?.length > 0 ? window.sitesSelected[0] : window.App.user?.session?.site_id?.[0];
		if (siteselected == 0 || siteselected == null) {
			siteselected = 4;
		}
		const dfd = window.Flyout.open({
			form: "fax_send",
			mode: "addfill",
			preset: {
				sent_dt: new Date().toISOString(),
				site_id: siteselected,
				user_id: window.App.user?.id,
				fax_type: "Document",
				status: "new",
			},
		});
		dfd.done((data) => {
			props.gridRef?.advanced?.refresh?.();
		});
		dfd.fail((data) => {
			props.gridRef?.advanced?.refresh?.();
		});
	};

	return (
		<div className="queue-actions">
			{buttons.map((button, index) => (
				<div key={index} className="queue-action-btns" onClick={() => onButtonClick()}>
					{button.icon && <i className={button.icon}></i>}
					<span className="btn-label">{button.label}</span>
				</div>
			))}
		</div>
	);
};
