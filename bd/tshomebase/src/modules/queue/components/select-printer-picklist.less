@import "../../../styles/base/less/colors.less";

.printer-selection-modal {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 320px;

    .printer-select-field-container {
        flex: 1;
        box-sizing: border-box;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;

        .printer-select-field-label {
            position: absolute !important;
            top: var(--spacing-standard);
            left: var(--spacing-xlarge);
            font-size: var(--font-size-xsmall);
            font-weight: var(--font-weight-regular);
            color: var(--color-text);
            z-index: 1;
            pointer-events: none;
        }
    }

    .btn-primary {
        width: max-content;
        color: white;
        background-color: var(--color-primary);

        &:hover {
            opacity: 0.8;
        }
    }

    .btn-secondary {
        width: max-content;
        background-color: var(--color-secondary);
        color: white;
        opacity: 1;

        &:hover {
            opacity: 0.8;
        }
    }

    .footer {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
}