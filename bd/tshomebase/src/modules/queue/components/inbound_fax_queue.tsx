import React from "react";
import "./print_labels_queue.less";
import { DSLListViewProps } from "../../../blocks/dsl-list-view/dsl-list-view";
interface QueueActionButton {
    label: string;
    icon?: string;
}

interface InboundFaxQueueActionsProps {
    buttons?: QueueActionButton[];
    selectedRowId?: string;
    gridRef?: any;
}

export const InboundFaxQueueActions: React.FC<InboundFaxQueueActionsProps & DSLListViewProps> = (props) => {
    const { buttons = [] } = props;
    const onButtonClick = async () => {
        window.Flyout.open({
            form: "document",
            mode: "addfill",
            preset: {
                form_filter: "careplan_order",
            },
        })
    }

    return (
        <div className="queue-actions">
            {buttons.map((button, index) => (
                <div key={index} className="queue-action-btns" onClick={() => onButtonClick()}>
                    {button.icon && <i className={button.icon}></i>}
                    <span className="btn-label">{button.label}</span>
                </div>
            ))}
        </div>
    );
};

