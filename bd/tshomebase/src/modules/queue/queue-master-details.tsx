import React, { useEffect, useState } from "react";

import type { CustomCellRendererProps } from "ag-grid-react";
import { StylesConfig } from "react-select";
import { fetchFormData, useFormData } from "@hooks/form-data";
import { Popover, Tooltip } from "@mui/material";
import "./queue-master-details.less";
import { MediaRenderer } from "./components/attachment-viewer";
import { CRErrorBoundary } from "@blocks/error-boundary";
import { getTooltipStyles } from "@utils/style-helper";
import { IDetailCellRendererParams } from "ag-grid-enterprise";
import { DSLAdvancedGrid } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import LoaderNoData from "@components/common/loader-no-data";
import { useUpdateHash } from "@hooks/update-hash";

const DrugVisiblityList = ({ rowData = {} }: { rowData: any }) => {
	const [ticketNo, setTicketNo] = useState<string | null>(null);
	useEffect(() => {
		if (!rowData?.delivery_ticket_id) {
			return;
		}
		fetchFormData("careplan_delivery_tick", rowData.delivery_ticket_id)
			.then((resp) => {
				setTicketNo(resp?.data?.ticket_no);
			})
			.catch((error) => {
				console.log(error);
			});
	}, [rowData.delivery_ticket_id]);

	if (!rowData) {
		return null;
	}
	if (!rowData.delivery_ticket_id) {
		return null;
	}
	if (!ticketNo) {
		return null;
	}
	return (
		<DSLAdvancedGrid
			form="careplan_dt_item"
			domLayout="autoHeight"
			hideFilter={true}
			suppressLoadOnScreen={true}
			suppressUserConfig={true}
			rowClicked={(row) => {
				window.Flyout.open({
					form: "careplan_dt_item",
					record: row.id,
					card: "read",
					flyoutAllowWidget: true,
					tab_can_edit_override: () => {
						return false;
					},
				});
			}}
			filtersPresetFixed={{
				type: "Drug",
				ticket_no: ticketNo,
			}}
		/>
	);
};

const ConfirmDeliveryTick = ({ rowData = {} }: { rowData: any }) => {
	const [fd] = useFormData("careplan_delivery_tick", rowData.delivery_ticket_id);
	const [activeQueue, setActiveQueue] = useState("items");

	if (fd.loading || fd.failed || !fd?.data?.id) {
		if (fd.loading) {
			return <LoaderNoData loading={fd.loading} />;
		}
		return null;
	}
	const dt = fd.data;
	if (!dt.ticket_no) {
		return null;
	}

	const tabData = [
		{
			id: "items",
			tkey: "items",
			label: "Items",
			form: "careplan_dt_item",
			parentForm: "",
			parentId: "",
			suppressUserConfig: true,
			filtersPresetFixed: {
				type: "Drug",
				ticket_no: dt.ticket_no,
			},
		},
		{
			id: "exceptions",
			tkey: "exceptions",
			label: "Exceptions",
			form: "careplan_dt_exception",
			rowModelType: "clientSide",
			suppressUserConfig: true,
			rowData: dt?.subform_confirmation?.[0]?.subform_exceptions || [],
			filtersPresetFixed: {},
			rowClicked: (row: any) => {
				window.Flyout.open({
					form: "careplan_dt_exception",
					card: "addfill",
					preset: row.rowData || {},
					forceReadOnly: true,
					flyoutAllowWidget: true,
					closeWithOutAck: true,
					tab_can_edit_override: () => {
						return false;
					},
					tab_can_save_override: () => {
						return false;
					},
				});
			},
		},
	];
	return (
		<div className="queue-tab-container">
			<div className="queue-tabs">
				<div
					className={`tab ${activeQueue === "items" ? "active" : ""}`}
					onClick={() => setActiveQueue("items")}
				>
					Inventory Items
				</div>
				<div
					className={`tab ${activeQueue === "exceptions" ? "active" : ""}`}
					onClick={() => setActiveQueue("exceptions")}
				>
					Exceptions
				</div>
			</div>
			{tabData.map((tab) => (
				<div
					key={tab.id}
					className="queue-tab-cont-container"
					style={{ display: activeQueue == tab.id ? undefined : "none" }}
				>
					<DSLAdvancedGrid
						domLayout="autoHeight"
						hideFilter={true}
						suppressLoadOnScreen={true}
						rowClicked={(row) => {
							window.Flyout.open({
								form: tab.form,
								record: row.id,
								card: "read",
								flyoutAllowWidget: true,
								tab_can_edit_override: () => {
									return false;
								},
							});
						}}
						{...tab}
					/>
				</div>
			))}
		</div>
	);
};

export const CustomAttachmentCellRenderer = React.memo((props: CustomCellRendererProps) => {
	const { data, node } = props;

	const open = data?._gridConfig?.open || false;
	const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
	const setOpen = (state: boolean) => {
		if (!data._gridConfig) {
			data._gridConfig = {};
		}
		data._gridConfig.open = state;
		node.setData(data);
	};
	return (
		<CRErrorBoundary>
			<Tooltip arrow title={"Preview File"} componentsProps={getTooltipStyles("primary")}>
				<div
					className="queue-preview-icon-container"
					ref={(r) => {
						setAnchorEl(r);
					}}
					onMouseOver={(e) => {
						e.stopPropagation();
						setOpen(!open);
					}}
				>
					{data.attachment && <i className="fa-solid fa-paperclip"></i>}
				</div>
			</Tooltip>
			{open && data.attachment && (
				<Popover
					id="attachment-viewer"
					open={open}
					anchorEl={anchorEl}
					anchorOrigin={{
						vertical: "bottom",
						horizontal: "left",
					}}
					onClose={() => {
						setOpen(false);
					}}
					transformOrigin={{
						vertical: "top",
						horizontal: "left",
					}}
					className="attachment-viewer-popover"
				>
					<div className="attachment-viewer-container">
						<MediaRenderer
							file={(() => {
								if (typeof data.attachment == "object") {
									return data.attachment;
								} else {
									try {
										return JSON.parse(data.attachment);
									} catch (err) {
										return {};
									}
								}
							})()}
						/>
					</div>
				</Popover>
			)}
		</CRErrorBoundary>
	);
});

const QueueMasterDetails = (props: IDetailCellRendererParams) => {
	const { data, node, api, swimlane, tab } = props;

	const [fd, refresh, update] = useFormData("wf_queue", data.__wf_queue_id);
	const [activeTab, setActiveTab] = useState("team");
	const [hash, refreshHash] = useUpdateHash();

	const { InspectElement, form } = swimlane;

	const customInspectProps = {
		tabData: tab,
		rowData: data,
		form: form,
		queueId: data.__wf_queue_id,
	};

	const updateRowAndForm = (updates: Record<string, any>, refreshForm = true) => {
		update(updates, refreshForm);

		const mutatedData: Record<string, unknown> = {};
		mutatedData.__updated_on = new Date().toISOString();
		mutatedData.__updated_by = window.App.user.id;
		mutatedData.__updated_by_auto_name = window.App.user.displayname;
		for (const key in updates) {
			mutatedData[`__${key}`] = updates[key];
		}

		const updatedData = { ...data, ...mutatedData };
		node.setData(updatedData);
		if (api) {
			api.refreshCells({
				rowNodes: [node],
				columns: Object.keys(mutatedData),
				force: true,
			});
		}
	};

	const customStyles: StylesConfig = {
		control: (provided, state) => ({
			...provided,
			height: "60px",
			borderRadius: "8px",
			border: `1px solid ${state.isFocused ? "#CBD5E0" : "#E3E5E8"}`,
			boxShadow: "none",
			"&:hover": {
				borderColor: "#CBD5E0",
			},
			padding: "10px",
		}),
		valueContainer: (provided) => ({
			...provided,
			marginTop: "18px",
			padding: "0px",
		}),
		placeholder: (provided) => ({
			...provided,
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			lineHeight: "1.2",
			color: "#9B9FA8",
		}),
		input: (provided) => ({
			...provided,
			margin: "0",
			padding: "0",
			color: "#5E636B",
			fontWeight: "600",
			fontSize: "16px",
		}),
		indicatorsContainer: (provided) => ({
			...provided,
			height: "100%",
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		indicatorSeparator: (provided) => ({
			...provided,
			display: "none",
		}),
		clearIndicator: (provided) => ({
			...provided,
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		dropdownIndicator: (provided) => ({
			...provided,
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		menu: (provided) => ({
			...provided,
			marginTop: "8px",
			borderRadius: "8px",
			boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
		}),
		menuList: (provided) => ({
			...provided,
			padding: "8px",
		}),
		menuPortal: (provided) => ({
			...provided,
			zIndex: 9999,
		}),
		option: (provided, state) => ({
			...provided,
			padding: "12px 16px",
			borderRadius: "6px",
			backgroundColor: state.isSelected ? "#EDF2F7" : "transparent",
			color: "#4A5568",
			"&:hover": {
				backgroundColor: "#F7FAFC",
			},
		}),
	};

	const renderSpecialGrid: Record<string, React.ComponentType<{ rowData: any }>> = {
		__wf_queue_pharmacy_order_verification: DrugVisiblityList,
		__wf_queue_pharmacy_CDT: ConfirmDeliveryTick,
	};
	const refreshDetailsView = () => {
		refreshHash();
		refresh();
	};

	const SpecialGrid = renderSpecialGrid[swimlane?.form] || null;
	return (
		<div className="queue-master-details" key={hash}>
			<div className="node-config-options">
				{InspectElement && (
					<InspectElement {...props} {...customInspectProps} refreshDetailsView={refreshDetailsView} />
				)}
				<CRErrorBoundary>{SpecialGrid && <SpecialGrid rowData={data} />}</CRErrorBoundary>
			</div>
		</div>
	);
};

export default QueueMasterDetails;
