import React, { useRef, useState } from "react";
import type { FC } from "react";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import "./queue-viewer.less";
import { DSLListInspectView } from "@blocks/dsl-list-inspect-view";
import { TabData, TabList } from "@blocks/tab-list";
import _ from "lodash";
import { QueueNodeSchema } from "@modules/queue/queue";
import QueueMasterDetails, { CustomAttachmentCellRenderer } from "@modules/queue/queue-master-details";
import { AdvancedGridRef, GridRowNode } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import { WFQueueResponse } from "@modules/queue/helper";
import { NodeData, QueueClickConfig } from "@modules/workflow/types";
import { QueueRowClickActions } from "@modules/workflow/inspect";
import { CLICK_ACTION_HANDLERS } from "@modules/queue/click-action-handlers";
import { SelectGridFilter } from "@blocks/dsl-advanced-grid/custom-filters/select";

interface QueueViewerProps extends RoutedComponentProps {
	tabListStyle?: string;
	swimlanes: QueueNodeSchema[];
}

interface QueueListViewerProps extends RoutedComponentProps {
	tab: QueueNodeSchema;
	swimlane: QueueNodeSchema;
}

export type QueueRowClickHandler = (
	event: {
		gridRef?: AdvancedGridRef | null;
		queueForm: string;
		rowNode?: GridRowNode;
		rowData: WFQueueResponse;
		nodeData: NodeData;
		clickAction?: QueueClickConfig;
	},
	clickType: "click" | "dblclick"
) => void;

export const QueueListViewer: FC<QueueListViewerProps> = (props) => {
	const { tab, swimlane } = props;

	const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	const handleRowClick: QueueRowClickHandler = (event, clickType) => {
		const { nodeData, rowData } = event;
		const clickAction = ((clickType == "dblclick" ? nodeData.double_row_click : nodeData.single_row_click) ||
			{}) as QueueClickConfig;
		if (_.isEmpty(clickAction)) {
			return;
		}
		const { action } = clickAction;
		if (!action || action === QueueRowClickActions.None) {
			return;
		} else if (action === QueueRowClickActions.Expand) {
			if (event.rowNode) {
				if (event.rowNode.expanded) {
					event.rowNode.setExpanded(false);
				} else {
					event.rowNode.setExpanded(true);
				}
			}
		} else if (action === QueueRowClickActions.FormEdit || action === QueueRowClickActions.FormView) {
			const mode = action === QueueRowClickActions.FormEdit ? "edit" : "read";
			const { id_field, form_field, form_name } = clickAction.config || {};
			if (!id_field) {
				console.error("Node :", nodeData, "Click Action :", clickAction.action, "Missing id_field");
				return;
			}
			if (!form_field && !form_name) {
				console.error(
					"Node :",
					nodeData,
					"Click Action :",
					clickAction.action,
					"Missing form_field or form_name (at least one required)"
				);
				return;
			}
			const formId = rowData?.[id_field] || rowData?.__row_data?.[id_field];
			if (!formId) {
				console.error("Node :", nodeData, "Click Action :", clickAction.action, "Missing formId");
				return;
			}

			let formName = form_name;
			if (!formName && form_field) {
				formName = rowData[form_field] || rowData?.__row_data?.[form_field];
			}
			if (!formName) {
				console.error(
					"Node :",
					nodeData,
					"Click Action :",
					clickAction.action,
					"Unable to find form name (to be opened) from query data"
				);
				return;
			}
			if (!window.DSL[formName]) {
				console.error("Node :", nodeData, "Click Action :", clickType, "Form not found :", formName);
				return;
			}
			window.Flyout.open({
				form: formName,
				record: formId,
				flyoutAllowWidget: true,
				card: mode,
			});
		} else if (action === QueueRowClickActions.FunctionCall) {
			if (!clickAction.config?.function_name) {
				console.error("Node :", nodeData, "Click Action :", clickType, "Missing function_name");
				return;
			}
			const { function_name } = clickAction.config;
			const handler = CLICK_ACTION_HANDLERS[function_name];
			if (handler) {
				handler(event, clickType);
			}
		} else {
			console.log("Unknown action :", action);
		}
	};

	const onRowClick = (event?: TabData) => {
		if (!event) return;
		const context = {
			gridRef: event.gridRef,
			queueForm: tab.form,
			rowData: event.rowData,
			nodeData: tab.node_data,
			rowNode: event.rowNode,
		};
		if (event?.type === "dblclick") {
			if (clickTimeoutRef.current) {
				clearTimeout(clickTimeoutRef.current);
				clickTimeoutRef.current = null;
			}
			handleRowClick(context as any, "dblclick");
			return;
		}
		if (clickTimeoutRef.current) {
			clearTimeout(clickTimeoutRef.current);
		}
		clickTimeoutRef.current = setTimeout(() => {
			handleRowClick(context as any, "click");
		}, 250);
	};

	return (
		<DSLListInspectView
			disableRouting={true}
			form={tab.form}
			inspectType="detail"
			wf={swimlane.formData}
			inspectIDField={tab.inspectIDField}
			inspectLabelField={tab.inspectLabelField}
			gridSourceOverrideURL={tab.gridSourceOverrideURL}
			InspectRenderComponent={tab.InspectElement}
			inspectRenderComponentProps={tab.node_data}
			rowSelection={swimlane.node_data.rowSelect}
			// noGridSearchBar={true}
			customColumns={tab.customColumns}
			customFindFields={tab.customFindFields}
			canAddNew={false}
			detailCellRendererParams={{
				tab: tab,
				swimlane: swimlane,
			}}
			initialFilters={{
				...(tab.initialFilters || {}),
				__status: "!4",
			}}
			detailCellRenderer={QueueMasterDetails}
			rowClicked={onRowClick}
			customColumnDefs={[
				{
					field: "attachment",
					headerName: "  ",
					pinned: "left",
					type: "text",
					maxWidth: 45,
					suppressSizeToFit: true,
					cellRenderer: CustomAttachmentCellRenderer,
					filter: false,
					suppressHeaderMenuButton: true,
					resizable: false,
				},
				{
					field: "__status",
					headerName: "Queue Status",
					resizable: true,
					type: "object",
					filter: SelectGridFilter,
					enablePivot: true,
					enableRowGroup: true,
					enableValue: true,
					filterParams: {
						defaultToNothingSelected: true,
						v: {
							model: {
								access: {
									read: [],
									if: null,
									write: [],
								},
								active: true,
								autoinsert: false,
								default: null,
								if: {},
								max: null,
								min: null,
								multi: false,
								prefill: [],
								required: false,
								required_all: false,
								rounding: null,
								save: false,
								search: null,
								source: "list_wf_queue_status",
								sourceid: "id",
								sourcefilter: {},
								subfields: {},
								subfields_sort: [],
								template: null,
								transform: [],
								transform_filter: [],
								transform_post: [],
								type: "int",
								validate: [],
								dynamic: {
									source: null,
									type: "text",
								},
								queue: true,
							},
							view: {
								class: "",
								control: "select",
								highlight: null,
								findfilter: null,
								findmulti: false,
								findunique: false,
								findrange: false,
								requireall_bypass: false,
								requireif_bypass: false,
								format: "",
								label: "Queue Status",
								note: "",
								offscreen: false,
								readonly: false,
								template: null,
								transform: [],
								validate: [],
								max_count: null,
								grid: {
									add: "flyout",
									copy: [],
									edit: false,
									fields: [],
									split: false,
									text_trim: null,
									tooltip: [],
									width: [],
								},
							},
							_meta: {
								sql_type: "integer",
							},
						},
						type: "multiSelect",
					},
				},
			]}
		/>
	);
};

export const QueueViewer: FC<QueueViewerProps> = (props) => {
	const { swimlanes, tabListStyle = "lvl-2-tab-list" } = props;
	const [activeQueue, setActiveQueue] = useState(swimlanes[0].id);
	return (
		<>
			<div className="queue-tab-container">
				<TabList
					activeTabId={activeQueue}
					openTabs={swimlanes}
					optionsProps={{
						enabled: false,
					}}
					styles={{
						tabListStyle: tabListStyle,
					}}
					addProps={{
						enabled: false,
					}}
					tabCanClose={false}
					draggable={false}
					onTabClick={(tab) => {
						setActiveQueue(tab.id);
					}}
				/>
				{swimlanes.map((swimlane) => (
					<div
						key={swimlane.id}
						className="queue-tab-cont-container"
						style={{ display: activeQueue == swimlane.id ? undefined : "none" }}
					>
						<QueueListViewer
							isActive={activeQueue == swimlane.id}
							isParentActive={props.isActive}
							tab={swimlane}
							swimlane={swimlane}
						/>
					</div>
				))}
			</div>
		</>
	);
};
