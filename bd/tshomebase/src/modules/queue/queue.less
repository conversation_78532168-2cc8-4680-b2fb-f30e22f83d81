@import "../../styles/base/less/colors.less";
@import "../../styles/base/less/dimensions.less";
@import "../../styles/base/less/fonts.less";

.workflow-queue-container {
  display: flex;
  height: 100%;
  gap: var(--spacing-standard);

  .workflow-nav-bar {
    background-color: #ffffffb8;
    width: 250px;
    border-radius: var(--radius-medium);
    padding: var(--spacing-xxxlarge);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto;
    backdrop-filter: blur(25px);

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 8px;
      padding: 2px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }

    &::-webkit-scrollbar {
      width: var(--spacing-xsmall);
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #D7DAE0;
      border-radius: var(--radius-xsmall);
    }

    .top-bar {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;

      .header {
        display: flex;
        gap: var(--spacing-large);
        border-bottom: 1px solid #D7DAE0;
        padding: var(--spacing-standard) 0 var(--spacing-xxxlarge);
        color: #5E636B;
        align-items: center;

        .title {
          font-family: 'Inter';
          font-size: var(--font-size-xsmall);
          font-weight: var(--font-weight-medium);
        }
      }
    }

    .tabs {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-standard);
      position: relative;

      .tab-item {
        padding: var(--spacing-standard) var(--spacing-standard) var(--spacing-standard) 0;
        cursor: pointer;
        border-radius: var(--radius-medium);
        transition: all 0.2s ease;
        position: relative;

        &:hover:not(.active) {
          // can add hover for parent tab item here
        }

        &.active {
          background-color: #FFFFFFBF;
          padding: var(--spacing-xlarge) var(--spacing-standard);
          box-shadow: 0px 1px 2px -1px #0000000F inset,
              0px 0px 1px 0px #00000014 inset !important;


          .tab-item-label {
            color: var(--color-tertiary);
            font-weight: var(--font-weight-bold);
          }

          &::after {
            content: '';
            position: absolute;
            right: -10px;
            top: 0;
            height: 100%;
            width: 5px;
            background-color: var(--color-tertiary);
            border-radius: var(--radius-xsmall);
            transition: all 0.3s ease;
          }

          .count {
            color: var(--gray-700) !important;
          }
        }

        .tab-item-label {
          font-family: 'Inter';
          font-weight: var(--font-weight-medium);
          line-height: var(--line-height-medium);
          font-size: var(--font-size-xsmall);
          color: var(--gray-700);
          display: flex;
          align-items: center;
          gap: 5px;



          i {
            width: var(--spacing-xxxxxlarge);
            font-size: var(--font-size-xsmall);
            font-weight: 400;
          }

          .count {
            padding: var(--spacing-xxsmall) var(--spacing-standard);
            border-radius: var(--radius-xxxlarge);
            font-weight: var(--font-weight-bold);
            line-height: var(--line-height-small);
            color: var(--color-text-500);
            margin-left: auto;
            font-size: var(--font-size-xxsmall);
          }
        }

        &.with-subtabs {
          .subtabs {
            margin-left: 22px;
            position: relative;
            left: var(--spacing-standard);


            &::before {
              content: '';
              position: absolute;
              left: -20px;
              top: 0;
              bottom: 33px;
              width: 2px;
              background-color: #D7DAE0;

            }

            .subtab-item {
              padding: var(--spacing-large) var(--spacing-standard) var(--spacing-large) 0;
              cursor: pointer;
              border-radius: var(--radius-medium);
              position: relative;
              margin: 0;
              transition: all 0.2s ease;

              &:first-child {
                margin-top: 8px;
              }

              &::before {
                content: '';
                position: absolute;
                left: -20px;
                top: 12%;
                width: 11px;
                height: var(--spacing-xxlarge);
                border-left: 2px solid #D7DAE0;
                border-bottom: 2px solid #D7DAE0;
                border-bottom-left-radius: var(--radius-xxxlarge);
              }

              &:first-child::before {
                top: 0;
                height: 50%;
                border-bottom-left-radius: var(--radius-xxxlarge);
              }

              &:last-child::before {
                top: auto;
                bottom: 18px;
                height: 50%;
              }

              &.active {
                background-color: #fff;
                // box-shadow: 0px -1px 2px 0px #00000038 inset,
                //   0px 1px 2px 0px #FFFFFF1F inset;
                box-shadow: 0px 1px 2px -1px #0000000F,0px 0px 1px 0px #00000014;
                padding-left: var(--spacing-standard);

                i {
                  transform: rotate(-20deg);
                  transition: all 0.3 ease;
                  font-size: var(--font-size-xsmall);
                  font-weight: 400;
                  animation: rotateSwing 0.7s ease-in-out forwards;
                }

                .subtab-item-label {
                  color: var(--color-tertiary);
                  font-weight: var(--font-weight-bold);
                }

                .count {
                  color: var(--gray-700) !important; 
                }

                &::after {
                  content: '';
                  position: absolute;
                  right: -9px;
                  top: 0;
                  height: 100%;
                  width: 4px;
                  background-color: var(--color-tertiary);
                  border-radius: var(--radius-xsmall);
                  transition: all 0.3s ease;
                }
              }

              &:hover:not(.active) {
                // can add child sub-tab item hover here
              }

              .subtab-item-label {
                font-family: 'Inter';
                font-size: var(--font-size-xsmall);
                font-weight: var(--font-weight-medium);
                color: var(--gray-700);
                display: flex;
                align-items: center;
                gap: var(--spacing-large);

                i {
                  width: var(--spacing-xxxlarge);
                  font-size: var(--font-size-xsmall);
                  font-weight: 400;
                }

                .count {
                  padding: var(--spacing-xxsmall) var(--spacing-standard);
                  border-radius: var(--radius-xxxlarge);
                  font-weight: var(--font-weight-bold);
                  line-height: var(--line-height-small);
                  color: #6b5e68;
                  margin-left: auto;
                  font-size: var(--font-size-xxsmall);
                }
              }
            }
          }
        }
      }
    }

    .bottom-bar {
      padding-top: var(--spacing-xxxlarge);
      margin-top: var(--spacing-xxxlarge);
    }
  }

  .workflow-area {
    flex: 1;
    display: flex;
    flex-direction: column;

    .workflow-header {
      padding: var(--spacing-xxxxxlarge);
      border-bottom: 1px solid #e0e0e0;

      .workflow-tabs {
        display: flex;
        gap: var(--spacing-large);

        .tab {
          padding: var(--spacing-standard) var(--spacing-xxxlarge);
          cursor: pointer;
          border-radius: var(--radius-xsmall);
          display: flex;
          align-items: center;
          gap: var(--spacing-standard);

          &.active {
            background-color: #6b5b95;
            color: white;

            .badge {
              background-color: white;
              color: #6b5b95;
            }
          }

          .badge {
            background-color: #e0e0e0;
            padding: var(--spacing-xxsmall) var(--spacing-standard);
            border-radius: var(--radius-large);
            font-size: var(--font-size-xxsmall);
          }
        }
      }
    }

    .workflow-content {
      flex: 1;
      padding: var(--spacing-xxxxxlarge);
    }
  }

  &:has(.workflow-nav-bar) {
    .queue-tab-container {
      >div.tab-list-default {
        margin-top: 0px;
      }
    }
  }
}

@keyframes rotateSwing {
  0% {
    transform: rotate(-20deg);
  }
  25% {
    transform: rotate(20deg);
  }
  50% {
    transform: rotate(-20deg);
  }
  75% {
    transform: rotate(10deg);
  }
  100% {
    transform: rotate(-20deg);
  }
}