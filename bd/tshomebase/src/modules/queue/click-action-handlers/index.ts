import { CLFile } from "@typedefs/window";
import { QueueRowClickHandler } from "../queue-viewer";
import { toast } from "@components/toast";
import {
    showToastError
} from "@utils/fx";
import { createInvoiceFromCreationView } from "./ready_to_bill";
import { startDispense } from "@modules/patient/patient-prescription/prescription-helper";
import { usePersistentWindowsStore } from "@blocks/persistent-windows/store";
import { request } from "@core/request";

const addWindow = usePersistentWindowsStore.getState().addWindow;

export const CLICK_ACTION_HANDLERS: Record<string, QueueRowClickHandler> = {
	CustomHandlerTest: (event, clickType) => {
		console.log(event, clickType);
	},
	FaxMediaViewer: (event, clickType) => {
		const documentId = event.rowData.__row_data?.document_id || event.rowData.id;
		if (!documentId) {
			showToastError("Document ID not found");
			return;
		}
		let file = null;
		let attachment = event.rowData.__row_data?.document_id ? event.rowData.__row_data?.attachment : event.rowData.attachment ? event.rowData.attachment : null;
		try {
			file = attachment ? JSON.parse(attachment) : {};
		} catch (e) {
			console.error(e);
			return undefined;
		}
 		window.Flyout.open({
			action: "PreviewFillCardView",
			form: "document",
			card: "edit",
			flyoutAllowWidget: true,
			record: documentId,
			preview: {
				type: "MediaRenderer",
				file: file || {},
			},
		});
	},
	ReadyToBillCreate: (event, clickType) => {
		try {
			const invoiceId = event.rowData?.form_id || null;
			const formName = event.rowData?.form_name || null;
			const patientId = event.rowData?.patient_id || null;
			const linkMap: any = {
				link: "patient",
				links: ["patient"],
				linkid: { patient: patientId },
			};
			if (formName === "billing_invoice") {
				window.Flyout.open({
					action: 'FlyoutCardView',
					form: "billing_invoice",
					flyoutAllowWidget: true,
					card: "edit",
					record: invoiceId,
					linkMap,
					...linkMap
				});
				return;
			}
			const siteId = event.rowData?.site_id || null;
			const ticketNo = event.rowData?.ticket_no || null;
			const calcSplitNo = event.rowData?.calc_invoice_split_no || null;
			const insuranceId = event.rowData?.insurance_id || null;
			const payerId = event.rowData?.payer_id || null;
			const billingMethodId = event.rowData?.billing_method_id || null;
			const addNewTypeFilter = ['Billable'];
			if (!calcSplitNo) {
			toast({
				type: "error",
					position: "bottom-center",
					theme: "dark",
					message: "Missing split number to generate invoice",
					autoClose: 5000,
				});
				return;
			}
			const dfd = window.Flyout.open({
				action: 'FlyoutCardSubformView',
				form: "view_create_invoice",
				card: "addfill",
				preset: {
					patient_id: patientId,
					site_id: siteId,
					ticket_no: ticketNo,
					insurance_id: insuranceId,
					payer_id: payerId,
					billing_method_id: billingMethodId,
					calc_invoice_split_no: calcSplitNo,
					add_new_type_filter: addNewTypeFilter
				 },
				autoRecoverEnabled: false,
			});
			dfd.done((data) => {
				createInvoiceFromCreationView(data);
			});
			dfd.fail((error) => {
				console.log(error, "cancel");
			});
		} catch (e) {
			console.error(e);
			showToastError("Error creating invoice");
			return undefined;
		}

	},
	ViewPatientSnapshot: (event, clickType) => {
		const patientId = event.rowData.patient_id;
		if (patientId) {
			window.App.reactNav.goTo(`/patient/${patientId}`);
		}
	},
	ViewDeliveryTicket: (event, clickType) => {
		const deliveryTicketId = event.rowData.form_id;
		if (deliveryTicketId) {
			window.App.reactNav.goTo(`/delivery-ticket/${deliveryTicketId}`);
		}
	},
	OpenCreateDeliveryTicket: (event, clickType) => {
		const patientId = event.rowData.patient_id;
		const rxId = event.rowData.rx_id;

		if (!patientId) return;

		startDispense(patientId, rxId, {
			addWindow,
			patientName: event.rowData.patient_name || "",
			onSaved: (e) => {
				console.log("Dispense created!", e);
			},
		});
	},
	StartRefill: (event, clickType) => {
		const patientId = event.rowData.patient_id;
		if (patientId) {
			window.App.reactNav.goTo(`/refill/create/${patientId}`);
		}
	},
	ReplaceItems: (event, clickType) => {
		const {patient_id:patientId} = event.rowData;
		const deliveryTicketId = event.rowData.__row_data?.delivery_ticket_id;
		if (!patientId || !deliveryTicketId) return;
		try {
			const fro = {
				url: `/api/query/replace_dt_items?x1=${deliveryTicketId}`,
				method: "GET"
			}
			request(fro)
				.then((resp) => {
					const itemData = resp.data;
					// Prepare arrays for all items at once
					const preset = {
						patient_id: patientId,
						delivery_ticket_id: deliveryTicketId,
						subform_original_items: itemData.map((item:any) => ({
							rx_no: item.rx_no,
							drug: item.drug,
							hcpc_code: item.hcpc_code,
							dispense_quantity: item.dispense_quantity,
							ndc_code: item.formatted_ndc
						})),
						subform_replaced_items: itemData.map((item:any) => ({
							drug: item.drug,
							dispense_quantity: item.dispense_quantity,
							rx_id: item.rx_id,
						}))
					};

					// Open single Flyout with all items
					const dfd = window.Flyout.open({
						form: 'careplan_dt_items_rplc',
						mode: "addfill",
						preset,
						link: "patient",
						links: ["patient", "careplan"],
						linkid: {
							"patient": patientId,
						},
						on_loaded: () => {}
					});

					dfd.done((fd) => {
						window.Flyout.open({
							action: "FlyoutCardView",
							form: "careplan_delivery_tick",
							card: "edit",
							record: fd.id,
						});
					});
				})
				.catch((error) => {
					console.error('Error fetching items:', error);
					showToastError('Failed to fetch replacement items');
				});
		} catch (error) {
			console.error('Error fetching items:', error);
			showToastError('Failed to fetch replacement items');
		}
	},
};
