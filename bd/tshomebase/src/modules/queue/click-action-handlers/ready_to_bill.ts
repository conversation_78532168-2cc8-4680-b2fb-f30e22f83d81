import { request } from "@core/request";
import {
    showToastError
} from "@utils/fx";

export const createInvoiceFromCreationView = (data: any) => {
	try {
		window.prettyNotify("Generating Invoice...");

		request({
			url: `/billing?func=generate_charge_lines_invoice`,
			method: 'POST',
			data: data
		}).then((res) => {
			const invoiceId = res?.data?.edit?.billing_invoice?.id || null;
			if (!invoiceId) {
				console.error("No invoice ID found", res);
				window.prettyNotify();
				window.prettyError("Error Generating Record", "An error occurred while generating the invoice. Please contact support.");
				return;
			}
			const linkMap: any = {
				link: "patient",
				links: ["patient"],
				linkid: { patient: data.patient_id },
			};
			window.Flyout.open({
				action: 'FlyoutCardView',
				form: "billing_invoice",
				card: "edit",
				flyoutAllowWidget: true,
				record: invoiceId,
				linkMap,
				...linkMap
			});
			window.prettyNotify();
		}).catch((error) => {
			window.prettyNotify();
			console.error("Error generating invoice:", error);
			showToastError(`Error generating invoice`);
		})
	} catch (e) {
		window.prettyNotify();
		console.error(e);
		window.prettyError("Error", "An error occurred while generating the invoice. Please contact support.");
	}
}