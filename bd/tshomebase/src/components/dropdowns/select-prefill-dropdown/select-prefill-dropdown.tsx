import React, { useEffect } from "react";
import _, { escape } from "lodash";
import { formData, formDataObj, PrevRow, rowObj, sourceConditionalObj, Subform, SubformField, SubformSourceObj, TransformFieldsObj } from "./select-prefill-dropdown-types";
import { request } from "@core/request";
import { checkIfFieldHasChanged } from "@utils/fx";
import { fetchFormData } from "@hooks/index";


export const SelectPrefillDropdown = (props: any) => {
	const { dd, v, k } = props;
	let transform: TransformFieldsObj = v.view.transform.find((o: any) => o.name === 'SelectPrefill');
	let DSLObject = window.DSL[dd.options.form]

	if (!(transform && Object.keys(transform).length > 0)) {
		return null;
	}
	let errorMessage = {};

	const doPrefilling = async () => {
		if (!dd) {
			console.info('Field Unmounted');
			return;
		}
		const field = dd.field_nodes[k];
		const fd = field?.data()?.select2?.data() || [];
		errorMessage = {};
		if (!dd.prev_value) {
			dd.prev_value = {};
		}
		if (dd.preset?.id || transform.stop_backend_revalidation) {
			if(!(k in dd.prev_value)) {
				dd.prev_value[k] = dd.preset[k];
			}
		}

		const grid = dd.options?.wrapper?.options?.parent?.parent?.grid;
		const isGridEdit = grid?.editable ?? grid?.edit ?? false;
		const fieldValue = dd.value_field(k);
		const hasFieldChanged = isGridEdit ? checkIfFieldHasChanged(dd, 'SelectPrefill', k) : fieldValue != dd.prev_value[k];
		if (hasFieldChanged) {
			dd.prev_value[k] = fieldValue;
			if (fd?.fd) {
				if(v.model.query && fd.id && v.model.source){
					if(!transform.url) {
						fetchFormData(v.model.source, fd.id, true, "all")
						.then((res) => {
								fd.fd = res.data
								mapData([fd])
							})
							.catch((err) => {
								console.error(err);
								mapData([fd]);
							});
					} else {
						request({
							url: `${transform.url}${fd.id}`,
							method: "GET",
						}).then((res) => {
							if (res.data.length > 0) {
								mapData([{ fd: res.data[0] }])
							}else {
								mapData([fd]);
							}
						}).catch((err) => {
							console.error(err);
							mapData([fd]);
						});
					}
				} else {
					mapData([fd]);
				}
			} else if (Array.isArray(fd)) {
				mapData(fd);
			}
		} else {
			dd.prev_value[k] = fieldValue;
		}
	};

	useEffect(() => {
		let field = dd.field_nodes[k];
		if (field.length > 0) {
			field.on('change', () => {
				setTimeout(() => {
					doPrefilling()
				}, 300)
			});
			props.onReady?.(doPrefilling);
			let fd = field.data()?.select2?.data()
			if (!fd?.fd && fd?.id && dd.prefill[k]) {
				if (!transform.url) return;
				request({
					url: `${transform.url}${fd.id}`,
					method: 'GET',
				}).then((res) => {
					if (res.data.length > 0) {
						mapData([{ fd: res.data[0] }])
					}
				})
			}
		}
	}, [])

	const Multiply = (fields: String[], sf: Subform): number | null => {
		let values = fields.map((e) => {
			let [relName, fieldName] = e.split('.');
			let value = sf[relName][fieldName];
			if (value === null || value === undefined) {
				return null;
			}
			return value as number;
		}).reduce((acc: number | null, curr: number | null) => {
			if (curr === null) {
				return null;
			}
			return acc * curr;
		});
		return values === null ? null : values;
	}

	const Divide = (fields: String[], sf: Subform): number | null => {
		let values = fields.map((e) => {
			let [relName, fieldName] = e.split('.');
			let value = sf[relName][fieldName];
			if (value === null || value === undefined) {
				return null;
			}
			return value as number;
		}).reduce((acc: number | null, curr: number | null) => {
			if (curr === null) {
				return null;
			}
			return acc / curr;
		});

		return values === null ? null : values;
	}

	const transforms = {
		'Multiply': Multiply,
		'Divide': Divide,
	};

	const clearFieldsBeforeUpdating = () => {
		for (let [destField, srcField] of Object.entries(transform.fields)) {
			if(!DSLObject.fields[destField]) {
				console.error(`SelectPrefill Invalid Destination Field: ${dd.options.form}.${destField}`)
				continue;
			}
			if (srcField && !_.isArray(srcField)) {
				if (!['json', 'subform'].includes(srcField.type)) {
					dd.value_field(destField, "", true, true, true);
					continue;
				}
				const subform_field = getFieldNode(srcField, destField);
				if (!subform_field) {
					console.error(`Unable to resolve subform field ${destField}`);
					continue;
				}
				if (srcField.type === 'subform') {
					if (DSLObject.fields[destField].model.multi == false) {
						let to_update_field_obj: SubformSourceObj = srcField.fields
						for (let key of Object.keys(to_update_field_obj)) {
							subform_field.value_field(key, '', true, true, true);
						}
					} else {
						const existingRows = subform_field.data('rowdata');
						const rows_to_delete = existingRows.filter((row: any) => row?._meta?.prefill_row == true);
						window.FieldSubform.mark_rows_delete(subform_field, rows_to_delete);
					}
					continue;
				}
				window.FieldGrid.clear(subform_field);
			} else if (_.isArray(srcField)) {
				dd.value_field(destField, "", true, true, true)
			}
		}
	};

	const getFieldNode = (srcField: SubformField, destField: string) => {
		let node;
		if (srcField.type === 'subform') {
			let sourceForm = DSLObject.fields[destField].model.source || '';
			const regex = /\{(.*?)\}/;
			const match = sourceForm.match(regex);
			if (typeof sourceForm === 'string' && match) {
					if (match && match[1]) {
						const dynamicSourceForm = match[1].trim();
						sourceForm = dd.value_field(dynamicSourceForm) || '';
						if (!sourceForm) {
							console.error(`Unable to resolve ${dynamicSourceForm}`);
							return;
						}
					}
			}
			if (DSLObject.fields[destField].model.multi == false) {
				node = dd.options.wrapper.subforms.formmap[sourceForm]
			} else {
				node = dd.options.wrapper.subforms.formmap[sourceForm].field_nodes[destField];
			}
		} else if (srcField.type === 'json') {
			node = dd.field_nodes[destField];
		}
		return node
	}

	const conditionalMapping = (srcField: sourceConditionalObj, destField: string, pr: formDataObj) => {
		let foundFlag = false;
		for (let [k, v] of Object.entries(srcField)) {
			if (foundFlag) break;
			let val = pr[k]
			if (typeof v == 'object' && v !== null && !_.isArray(v)) {
				for (let [srcVal, destVal] of Object.entries(v.if)) {
					if (srcVal == val) {
						dd.value_field(destField, destVal, true, true, true)
						foundFlag = true;
						break;
					} else if (!val) {
						handlingVerboseError(pr, k, destField)
					}
				}
			}
		}
	}

	const getFieldValue = (field: string, that: any, key: string, destField: string) => {
		const fieldType = DSLObject.fields[destField]?.model?.subfields?.[key]?.type ?? DSLObject.fields[destField]?.model?.type;
		let formData = (dd.options.parent.ddf || dd.options.parent.ddr).get_formdata()
		if (field.includes('.')) {
			const [relName, fieldName] = field.split('.');
			return that[relName]?.[fieldName] ?? formData[fieldName] ?? '';
		}
		return window.DSLDraw.parse_template_field(`{{${field}}}`, fieldType) ?? '';
	}

	const Templating = (val: string, that: any, key: string, destField: string) => {
		const regex = /{{\s*([^\s}]+)\s*}}/g;
		const result = val.replace(regex, (match, field) => {
			const value = getFieldValue(field, that, key, destField);
			return value || '';
		});
		return result || val
	}

	const handlingVerboseError = (pr, e, destField) => {
		if (!(DSLObject.fields[destField].model?.required && DSLObject.fields[destField].view?.readonly && window.DSLFields.is_visible(dd.field_nodes[destField]))) return;
		let form = DSLObject.fields[k]?.model?.source;
		errorMessage.fields = [];
		errorMessage.fields.push(`${form.charAt(0).toUpperCase() + form.slice(1)} > ${window.DSL[form].fields[e]?.view?.label}`);
		errorMessage.instructions = `Selected Item ${pr.auto_name} does not have value of fields: <br><br>&nbsp; &nbsp;`;
		errorMessage.advice = `<br><br>Try updating the selected item in ${window.DSL[DSLObject.fields[k]?.model?.source].view.comment} and then try again.`;
		errorMessage.title = 'Prefill Error';
	}

	const mapParentDataInSubform = (srcField: SubformField, destField: string, that: any, pr: formDataObj) => {
		if(!DSLObject.fields[destField]) {
			console.error(`SelectPrefill Invalid Destination Field: ${dd.options.form}.${destField}`)
			return
		}
		let subform_data_from_fd = [];
		if (pr[srcField.field] && pr[srcField.field].length > 0)
			subform_data_from_fd = pr[srcField.field][0];
		let to_update_field_obj: SubformSourceObj = srcField.fields
		let subform_field = getFieldNode(srcField, destField)
		if (!subform_field) {
			return;
		}
		that['sf'] = subform_data_from_fd;
		for (let [key, val] of Object.entries(to_update_field_obj)) {
			let value = '';
			if (_.isArray(val)) {
				for (let e of val) {
					let [relName, fieldName] = e.split('.');
					value = that[relName][fieldName];
					if (value) break;
				}
			} else if (typeof val == 'string') {
				value = Templating(val, that, key, destField)
			}
			subform_field.value_field(key, value, true, true, true);
		}
	}

	const mapSubFormData = (srcField: SubformField, destField: string, that: any, pr: formDataObj) => {
		let subform_data_from_fd = pr[srcField.field] || []
		let to_update_field_obj: SubformSourceObj = srcField.fields
		let subform_field = getFieldNode(srcField, destField)
			if (!subform_field) {
			return;
		}
		for (let sf of subform_data_from_fd) {
			that['sf'] = sf;
			let row: rowObj = {};
			for (let [key, val] of Object.entries(to_update_field_obj)) {
				if (val.transform) {
					let result;
					if (transforms.hasOwnProperty(val.transform)) {
						result = transforms[val.transform](val.fields, that);
					} else {
						result = null;
					}
					row[key] = result;
				} else {
					let value = '';
					if (_.isArray(val)) {
						for (let e of val) {
							let [relName, fieldName] = e.split('.');
							value = that[relName][fieldName];
							if (value) break;
						}
						row[key] = value
						val.filter(e => !e.includes('pr.')).map(e => {
							let fieldName = e.split('.')[1];
							if (_.isArray(sf[fieldName])) {
								let vf = window.DSL[DSLObject.fields[destField].model.source].fields[fieldName]
								if (vf.model.multi && vf.model.multi == true) {
									row[key] = sf[fieldName]
									row[key + '_auto_name'] = sf[fieldName + '_auto_name']
								}
							}
							if (sf[fieldName + '_auto_name'])
								row[key + '_auto_name'] = sf[fieldName + '_auto_name'];
						});
					} else if (typeof val == 'string') {
						let value = Templating(val, that, key, destField)
						row[key] = value
					}
					row._meta = {}
					row._meta['prefill_row'] = true
				}

			}
			if (srcField.type === 'subform') {
				window.FieldSubform.add_row(subform_field, row)
			} else if (srcField.type === 'json') {
				window.FieldGrid.add_row(subform_field, row)
			}
		}
	}

	const mapFormData = (srcField: Array<any>, destField: string, pr: formDataObj, setCode = false) => {
		let val: Array<any> | string = '';
		for (let i = 0; i < srcField.length; i++) {
			let e = srcField[i];
			if (_.isArray(pr[e])) {
				let vf = DSLObject.fields[destField];
				if (vf.model.multi && vf.model.multi == true) {
					let id = pr[e];
					let text = pr[e + "_auto_name"];
					val = id.map((idValue, index) => {
						return {
							id: idValue,
							text: text[index] || ''
						};
					});
				}
			} else if (!_.isArray(pr[e])) {
				if (setCode)
					val = pr['code']
				else
					val = pr[e];
			}
			if (val) {
				break;
			} else if (!val) {
				handlingVerboseError(pr, e, destField)
			}
		}
		dd.value_field(destField, val, true, true, true)
	}

	const mapData = (data: formData[]) => {
		clearFieldsBeforeUpdating()
		for (let pr of data) {
			pr = pr.fd
			let that = {};
			that['pr'] = pr;
			for (let [destField, srcField] of Object.entries(transform.fields)) {
				if(!DSLObject.fields[destField]) {
					console.error(`SelectPrefill Invalid Destination Field: ${dd.options.form}.${destField}`)
					continue;
				}
				if (srcField && typeof srcField == 'object' && !_.isArray(srcField)) {
					if (srcField.type == 'subform' || srcField.type == 'json') {
						if (srcField.type == 'subform' && DSLObject.fields[destField].model.multi == false) {
							mapParentDataInSubform(srcField, destField, that, pr)
						} else {
							mapSubFormData(srcField, destField, that, pr)
						}
					} else if (srcField.type === 'conditional') {
						conditionalMapping(srcField, destField, pr)
					}
				} else if (_.isArray(srcField)) {
					if (
						DSLObject.fields[destField].model.source && DSLObject.fields[destField].model.sourceid == 'code' &&
						pr[srcField[0]] &&
						typeof pr[srcField[0]] == 'number'
					) {
						request({
							url: `/form/${DSLObject.fields[destField].model.source}?filter=${srcField[0]}:${pr[srcField[0]]}`,
						}).then((res) => {
							if (res.data.length > 0) {
								mapFormData(srcField, destField, res.data[0], true)
							}
						})
					} else {
						mapFormData(srcField, destField, pr)
					}
				} else if (typeof srcField == 'string') {
					let value = Templating(srcField, that, '', destField)
					dd.value_field(destField, value, true, true, true)
				} else {
					console.log("Invalid Format of Fields")
				}
			}
		}
		if (Object.keys(errorMessage).length > 0) {
			let fieldsString = errorMessage.fields.join('<br>&nbsp; &nbsp; ');
			let errorString = errorMessage.instructions + fieldsString + errorMessage.advice;
			window.prettyError(errorMessage.title, errorString);
		}
	}
};
