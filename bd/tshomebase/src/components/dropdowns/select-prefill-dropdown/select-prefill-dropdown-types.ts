export interface Subform {
    [key: string]: {
        [key: string]: number | undefined;
    };
}
export interface PrevRow {
    f_node: any;
    f_type: string;
}
export interface conditionalObj {
    if: {
        [key: string]: string
    }
}
export interface sourceConditionalObj {
    [key: string]: conditionalObj | string;
}
export interface formDataObj {
    [key: string]: Array<any> | string | number | any[];
}
export interface formData {
    [key: string]: formDataObj[];
}
export interface rowObj {
    [key: string]: string | number | any[];
}
export interface SubformField {
    type: 'subform' | 'json';
    field: string;
    fields: SubformSourceObj
}
export interface SubformSourceObj {
    [key: string]: {
        transform: string;
        fields: string[] | string;
    } | string[] | string;
}
export interface TransformFieldsObj {
    [key: string]: {
        fields: string[] | string | SubformField | sourceConditionalObj;
    } | string[] | sourceConditionalObj | SubformField;
}