import React, { useEffect, useState, useRef } from "react";
import "./input-prefill-dropdown.less";
import $ from "jquery";
import _ from "lodash";
import { request } from "@core/request/request";

// Add validations v,dd,transform
export const InputPrefillDropDown = (props) => {
    const containerRef = useRef();
    const { dd, v } = props;
    let transform = v.view.transform.find((o) => o.name === 'APIPrefill');
    const [query, setQuery] = useState("");
    const [openList, setOpenList] = useState(true);
    const [suggestions, setSuggestions] = useState([]);
    const [authKey, setAuthKey] = useState("")

    const suggestionClickHandler = (e, obj) => {
        for (let [destField, srcField] of Object.entries(transform.fields)) {
            let val = ''
            for (let e of srcField) {
                val = obj[e]
                if (val) {
                    break
                }
            }
            dd.value_field(destField, val, true, true)
        }
        setOpenList(!openList)
    }

    useEffect(() => {
        let field = $(containerRef.current).parent().parent().find('input');
        if (field.length > 0) {
            field.on('input', (e) => {
                setQuery(e.target.value)
                setOpenList(true)
            });
            field.on('blur', (e) => {
                console.log('Blur')
                setTimeout(() => {
                    setOpenList(false)
                }, 500);
            });
        }
        getAuthKey()
    }, [containerRef])
    const getAuthKey = () => {
        try {
            request({
                url: `/form/list_auth_keys?filter=code:${transform.authkey || -1}`,
                method: 'GET'
            }).then((res) => {
                setAuthKey(res?.data[0]?.authkey || '')
            }).catch((err) => { })
        } catch (error) {
            console.log("Error at list_auth_keys:", error)
        }
    }
    useEffect(() => {
        const debounceTimeout = setTimeout(() => {
            const fetchData = async () => {
                try {
                    let authObj = {}
                    if (authKey) {
                        authObj['authKey'] = authKey
                    } else {
                        authObj['authKey'] = ''
                    }
                    let url = getUrl();
                    await getAPIData(url, query, authObj)
                } catch (error) {
                    console.error("Error fetching data:", error);
                }
            };
            if (query.trim() !== "") {
                fetchData();
            } else {
                setSuggestions([]);
            }
        }, 250);
        return () => clearTimeout(debounceTimeout);
    }, [query]);

    const getAPIData = async (url, query, authObj) => {
        let response;
        try {
            if (authObj.authKey) {
                response = await fetch(url + query, {
                    headers: {
                        Authorization: authObj.authKey
                    }
                });
                if (response.ok) {
                    let data = await response.json();
                    if (transform?.robj)
                        data = data[transform.robj]
                    if (transform.uniqueby)
                        data = _.uniqBy(data, (obj) => obj[transform.uniqueby])
                    setSuggestions(data);
                } else {
                    console.log("Error fetching data:", response.status);
                }
            } else {
                // nes only
                request({
                    url: url + query,
                    method: "GET"
                }).then((res) => {
                    let data = res.data
                    if (transform?.robj)
                        data = data[transform.robj]
                    if (transform.uniqueby)
                        data = _.uniqBy(data, (obj) => obj[transform.uniqueby])
                    setSuggestions(data);
                }).catch((err) => { console.log("=err===", err) })
            }
        } catch (error) {
            console.log("Error at geting API Data:", error)
        }
    }

    const getUrl = () => {
        let url = '';
        url = transform.url;
        return url
    }
    return (
        <div className={`dropdown-container ${suggestions?.length && "suggestions" || ""}`} ref={containerRef}>
            {openList &&
                <ul className={`dropdown`}>
                    {suggestions.map((suggestion, index) => {
                        let display = '';
                        display = transform.display.map((o) => suggestion[o]).filter(Boolean).join(', ');
                        if (display !== '') {
                            return (
                                <li
                                    className="address-suggestion"
                                    key={index}
                                    onClick={(e) => suggestionClickHandler(e, suggestion)}>
                                    {display}
                                </li>
                            )
                        }
                        else
                            return null;
                    })}
                </ul>
            }
        </div >
    );
};
