import React, { useRef, useState } from "react";
import "./dropdown-navigator.less";
import common from "@public/icons/common";
import { Popover } from "@mui/material";
import { TabData, TabItemDraw } from "@blocks/tab-list/tab-list";
import icons from "@public/icons";

export interface DropDownNavigatorListItem {
	id: string;
	label: string;
	meta: Record<string, any>;
}
export interface DropDownNavigatorList {
	id: string;
	label: string;
	items: DropDownNavigatorListItem[];
	link: string;
}

export type NavigatorOnClick = (link: string, item: DropDownNavigatorListItem) => void;

interface DropDownNavigatorProps {
	lists: DropDownNavigatorList[];
	icon: string;
	menuLabel?: string;
	btnClass?: string;
	onClick?: NavigatorOnClick;
}

interface NavigatorListRendererProps {
	list: DropDownNavigatorList;
	onClick?: NavigatorOnClick;
	closeNavigator: () => void;
}

const NavigatorListRenderer: React.FC<NavigatorListRendererProps> = ({ onClick, closeNavigator, list }) => {
	return (
		<div className="navigator-list">
			<div className="list-label">{list.label}</div>
			<div className="list-items">
				{list.items.map((item) => (
					<div
						key={item.id}
						className="item"
						onClick={() => {
							if (!onClick) return;
							onClick(list.link, item);
							closeNavigator();
						}}
					>
						{item.label}
					</div>
				))}
			</div>
		</div>
	);
};

const DropDownNavigator: React.FC<DropDownNavigatorProps> = ({
	lists = [],
	menuLabel = "Navigator Menu",
	btnClass = "",
	icon = common.navigatorOutline,
	onClick,
}) => {
	const anchorEl = useRef(null);
	const [open, setOpen] = useState(false);
	if (!icon) {
		icon = common.navigatorOutline;
	}

	return (
		<>
			<img ref={anchorEl} className={btnClass} src={icon} onClick={() => setOpen(true)} />
			{open && (
				<Popover
					id="navigator-menu"
					open={open}
					anchorEl={anchorEl.current}
					onClose={() => setOpen(false)}
					anchorOrigin={{
						vertical: "bottom",
						horizontal: "left",
					}}
					PaperProps={{
						style: {
							width: "50%",
							height: "auto",
							borderRadius: "12px",
							minHeight: "260px",
							maxHeight: "75%",
						},
					}}
					transformOrigin={{
						vertical: "top",
						horizontal: "left",
					}}
				>
					<div
						className="navigator-menu-list"
						onMouseOver={() => {
							setOpen(true);
						}}
						onMouseOut={() => {
							setOpen(false);
						}}
					>
						<div className={`header`}>
							<div className="title">{menuLabel}</div>
							<div className="arrow-container">
								<img
									className="close"
									src={icons.common.crossIcon}
									alt="Close Navigator"
									onClick={() => setOpen(false)}
								/>
							</div>
						</div>
						<div className="body">
							{lists.map((list) => {
								if (list.items.length === 0) return null;
								return (
									<NavigatorListRenderer
										key={list.id}
										list={list}
										onClick={onClick}
										closeNavigator={() => {
											setOpen(false);
										}}
									/>
								);
							})}
						</div>
					</div>
				</Popover>
			)}
		</>
	);
};

export default DropDownNavigator;
