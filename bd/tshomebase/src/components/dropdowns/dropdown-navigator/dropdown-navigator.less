@import (reference) "../../../less/style/main.less";

.navigator-menu-list {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	.card-collapsible;
	overflow: unset;
	background-color: #d4d4d4;

	.header {
		padding: 16px 24px 16px 24px;
		background-color: #d4d4d4;
		border-bottom: 0;

		.title {
			color: #668eba;
		}
	}

	.body {
		padding: 0px;
		display: flex;
		flex-wrap: wrap;
		width: calc(100% - 20px);
		height: 100%;
		overflow-y: auto;
		overflow-x: auto;
		justify-content: flex-start;
		align-items: flex-start;
		align-content: flex-start;
		background-color: #fafafa;
		border-radius: 12px;
		margin: 0 10px 10px 10px;

		.navigator-list {
			padding: 16px 24px 16px 24px;
			width: 32%;
			height: fit-content;
			display: flex;
			flex-direction: column;
			gap: 1%;

			.list-label {
				color: @black;
				font-size: @font-size-14;
				font-weight: 600;
				line-height: @line-height-24;
			}

			.list-items {
				display: flex;
				flex-direction: column;

				.item {
					font-size: @font-size-12;
					color: @charcoal-gray;
					line-height: @line-height-24;
					cursor: pointer;

					&:hover {
						color: @black;
					}
				}
			}
		}
	}

	@media (max-width: 1000px) {
		.body {
			.navigator-list {
				width: 50%;
			}
		}
	}

	@media (max-width: 576px) {
		.body {
			.navigator-list {
				width: 100%;
			}
		}
	}
}
