@import (reference) '../../../less/style/main.less';

.dropdown-menu-selector {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 4px;
    height: fit-content;
    width: fit-content;
    padding: 5px 10px 5px 10px;
    max-height: 250px;
    overflow-y: auto;

    button {
        .btn-secondary;
        background: transparent;
        width: 100%;
        text-align: start;
        text-transform: none;
        padding: 5px;
        font-weight: 500;
        border-bottom: 1px solid fade(@charcoal-gray, 10%);
        border-radius: 0px !important;
        justify-content: flex-start;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            border-radius: none;
            background-color: unset;
            color: unset;
        }
    }
}

.dropdown-menu-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 5px;
    padding-right: 5px;
    cursor: pointer;

    &.active,
    &:hover {
        background: fade(@charcoal-gray, 10%);
        height: 100%;

        border-radius: 8px;
    }
}