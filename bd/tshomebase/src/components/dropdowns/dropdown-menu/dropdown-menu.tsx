import React, { useRef, useState } from 'react';
import './dropdown-menu.less';
import common from '@public/icons/common';
import { Button, Popover } from '@mui/material';

export interface DropdownMenuItem {
    id: string;
    label: string;
    newLabel?: string;
    meta: Record<string, any>;
}

interface DropdownMenuProps {
    items: DropdownMenuItem[];
    icon?: string;
    onClick?: (item: DropdownMenuItem) => void;
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({ items, icon, onClick }) => {
    const anchorEl = useRef(null);
    const [open, setOpen] = useState(false);
    if (!icon) {
        icon = common.threeVerticalDotsOutline;
    }

    return (
        <>
            <div className={`dropdown-menu-btn${open ? ' active' : ''}`}>
                <img src={icon} ref={anchorEl} onClick={() => setOpen(true)} />
            </div>
            <Popover
                id="sc-popover"
                open={open}
                anchorEl={anchorEl.current}
                onClose={() => setOpen(false)}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                }}
            >
                <div className="dropdown-menu-selector">
                    {items.map((item) => (
                        <Button
                            className="dropdown-item"
                            key={item.id}
                            onClick={() => {
                                if (!onClick) return
                                onClick(item);
                                setOpen(false);
                            }}
                        >
                            {item.label}
                        </Button>
                    ))}
                </div>
            </Popover>
        </>
    );
};

export default DropdownMenu;