import React from 'react';
import "./ellipses-loader.less";

interface EllipsesLoaderProps {
    // Define any props for the component here
}

const EllipsesLoader: React.FC<EllipsesLoaderProps> = (props) => {
    return (
        <div className="ellipses-loader">
            <div className="first dot"></div>
            <div className="second dot"></div>
            <div className="third dot"></div>
        </div>
    );
};

export default EllipsesLoader;