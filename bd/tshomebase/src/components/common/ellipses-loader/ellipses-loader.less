@import (reference) "../../../less/style/main.less";

.ellipses-loader {
    display: flex;
    align-items: center;
    justify-content: center;


    .dot {
        margin: 2px;
        border-radius: 100%;
        width: 6px;
        height: 6px;
    }

    .first {
        -webkit-animation:
            pulse_first 1s ease-in-out infinite,
            nudge 1s ease-in-out infinite;
    }

    .second {
        -webkit-animation:
            pulse_second 1s ease-in-out .1s infinite,
            nudge 1s ease-in-out .1s infinite;
    }

    .third {
        -webkit-animation:
            pulse_third 1s ease-in-out .2s infinite,
            nudge 1s ease-in-out .2s infinite;
    }

    @-webkit-keyframes pulse_first {

        0%,
        80%,
        100% {
            background-color: #b4bec8;
        }

        40% {
            background-color: @purple;
        }
    }

    @-webkit-keyframes pulse_second {

        0%,
        80%,
        100% {
            background-color: #b4bec8;
        }

        40% {
            background-color: @purple;
        }
    }

    @-webkit-keyframes pulse_third {

        0%,
        80%,
        100% {
            background-color: #b4bec8;
        }

        40% {
            background-color: @purple;
        }
    }

    @-webkit-keyframes nudge {

        0%,
        80% {
            transform: translate(0, 0);
        }

        40% {
            transform: translate(0, -4px);
        }
    }
}