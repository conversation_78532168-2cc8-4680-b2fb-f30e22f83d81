import "./styles.less";

export type StatusIndicatorColor = "salmon" | "skyblue" | "purple" | "gray";

export interface StatusIndicatorProps {
    icon?: string;
    initials?: string;
    statusLabel: string;
    statusColor: StatusIndicatorColor;
}

const StatusIndicator = (props: StatusIndicatorProps) => {
    const { icon, initials, statusLabel, statusColor } = props;
    return (
        <div className={`status-indicator ${statusColor}`}>
            {icon && <img src={icon} />}
            {initials && <span className="initials">{initials}</span>}
            <span>{statusLabel}</span>
        </div>
    );
};

export default StatusIndicator;
