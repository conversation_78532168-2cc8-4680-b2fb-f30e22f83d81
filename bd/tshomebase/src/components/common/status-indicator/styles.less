@import (reference) "../../../less/style/main.less";

.status-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: @border-radius-8;
    background-color: fade(@reddish, 10%);
    color: @reddish;
    height: 100%;
    min-width: 90px;
    gap: 5px;
    padding: 6px 12px 6px 12px;

    >img {
        height: 24px;
        width: 24px;
    }

    .initials {
        min-width: 24px;
        width: 24px;
        min-height: 24px;
        height: 24px;
        border-radius: 50%;
        text-align: center;
        font-size: 12px;
    }

    >span {
        font-size: @font-size-14;
        font-weight: 600;
        line-height: @line-height-24;
    }

    &.salmon {
        background-color: fade(@reddish, 10%);
        color: @reddish;

        .initials {
            background-color: @reddish;
            color: @white;
        }
    }

    &.skyblue {
        background-color: fade(@sky-blue, 10%);
        color: @sky-blue;

        .initials {
            background-color: @sky-blue;
            color: @white;
        }
    }

    &.purple {
        background-color: fade(@purple, 10%);
        color: @purple;

        .initials {
            background-color: @purple;
            color: @white;
        }
    }

    &.gray {
        background-color: fade(@charcoal-gray, 10%);
        color: @charcoal-gray;

        .initials {
            background-color: @charcoal-gray;
            color: @white;
        }
    }
}