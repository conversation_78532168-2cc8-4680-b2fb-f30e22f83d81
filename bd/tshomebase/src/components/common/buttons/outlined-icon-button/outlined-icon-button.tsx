import type { FC, SyntheticEvent } from "react";
import React from "react";

import "./outlined-icon-button.less";

type OutlinedIconButtonProps = {
	label: string;
	icon?: string;
	onClick?: ((e?: SyntheticEvent) => void) | (() => void);
}


export const OutlinedIconButton: FC<OutlinedIconButtonProps> = (props) => {
	const { label, icon, onClick } = props;
	return (
		<div className={`outlined-icon-button cr-pointer`} onClick={onClick}>
			{icon && <img src={icon} />}
			<p>{label}</p>
		</div>
	);
};