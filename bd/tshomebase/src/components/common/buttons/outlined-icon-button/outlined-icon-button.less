@import (reference) '../../../../less/style/main.less';


.outlined-icon-button {
    display: flex;
    color: @purple;
    border: 2px solid fade(@purple, 40%);
    border-style: dotted;
    background: transparent;
    border-radius: @border-radius-10;
    text-align: start;
    min-height: 45px;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    padding: 6px 16px 6px 16px;
    gap: 6px;
    width: fit-content;

    >img {
        width: 24px;
        height: 24px;
    }

    >p {
        font-size: @font-size-13;
        line-height: @line-height-16;
    }

    @media (max-width: 425px) {
        flex-basis: 48%;
        gap: 5px;
    }

    @media (max-width: 375px) {
        flex-basis: 100%;
        gap: 5px;
    }
}