import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./thumbnail-carousel.less";
import React, { FC } from "react";
import Slider, { Settings } from "react-slick";
import { CLFile } from "@typedefs/window";
import { openMediaViewerPopup } from "@components/popups/media-viewer";
import { CRErrorBoundary } from "@blocks/error-boundary";
import { getThumbnailUrl } from "@utils/fx";

interface ThumbnailCarouselProps {
	files: CLFile[];
	config?: Settings;
}

export const ThumbnailCarousel: FC<ThumbnailCarouselProps> = (props) => {
	const files = props.files;
	return (
		<CRErrorBoundary>
			<Slider
				dots={true}
				infinite={false}
				{...(props?.config || {})}
				slidesToShow={props.config?.slidesToShow || 1}
				slidesToScroll={props.config?.slidesToScroll || 1}
				className="thumbnail-carousel-slick-slider"
			>
				{files?.map((file, i) => {
					return (
						<div
							key={file.filehash + i}
							onClick={() => openMediaViewerPopup(file)}
							className="tmb-crsl-slk-in"
						>
							<img style={{ width: "100%", height: "150px" }} src={getThumbnailUrl(file)} />
						</div>
					);
				})}
			</Slider>
		</CRErrorBoundary>
	);
};
