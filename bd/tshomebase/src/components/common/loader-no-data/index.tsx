import NoDataFound, { NoDataFoundPropsTypes } from "@components/common/no-data-found";
import SpinLoader, { SpinLoaderPropsTypes } from "@components/common/spin-loader";

interface LoaderNoDataPT {
	loaderProps?: SpinLoaderPropsTypes;
	noDataProps?: NoDataFoundPropsTypes;
	loading: boolean;
	text?: string;
	containerStyle?: React.CSSProperties;
}

const LoaderNoData = (props: LoaderNoDataPT) => {
	const { loading = false, text = "", noDataProps = {}, loaderProps = {}, containerStyle } = props;
	return (
		(loading && <SpinLoader loading={loading} {...loaderProps} style={containerStyle} />) || (
			<NoDataFound text={text} {...noDataProps} />
		)
	);
};

export default LoaderNoData;
