import { UIComponentProps } from "@utils/interfaces";
import * as styles from "./style.module.less";

export interface SpinLoaderPropsTypes extends UIComponentProps {
	loading?: boolean;
	width?: string;
	height?: string;
	fontSize?: string;
}

const SpinLoader = (props: SpinLoaderPropsTypes) => {
	const { className, loading = false, width = "100%", height = "100%", fontSize = "3em", style } = props;
	return (
		<>
			{loading && (
				<div
					className={`${styles.loadingContainer}${(className && " " + className) || ""}`}
					style={{ ...style, height, width }}
				>
					<i className="fa-duotone fa-solid fa-circle-notch fa-spin" style={{ fontSize }} />
				</div>
			)}
		</>
	);
};

export default SpinLoader;
