import { UIComponentProps } from "@utils/interfaces";
import * as styles from "./style.module.less";
import  "./style.module.less";
import React, { useState } from "react";

interface BottomDrawerPropsTypes extends UIComponentProps {
	variant?: string;
	show?: boolean;
	className?: string;
	children?: React.ReactNode;
	header?: string;
	footer?: string;
	position?: string;
}

const BottomDrawer = ({ className, children, show, style, position = 'bottom' }: BottomDrawerPropsTypes) => {

	if (!show) {
		return children
	}

	const [collapseDrawer, setCollapseDrawer] = useState(false);

	const toggleDrawer = () => {
		setCollapseDrawer(prevState => !prevState);
	};

	return (
		<div style={style} className={`${className} ${position} ${styles.bottomDrawerContainer} ${show && styles.show} ${collapseDrawer && `${styles.expanded} expanded` || ""}`}>
			<div onClick={toggleDrawer} className={styles.collapseBtn} />
			<div className={styles.contentContainer}>
				{children}
			</div>
		</div>
	);
};

export default BottomDrawer;