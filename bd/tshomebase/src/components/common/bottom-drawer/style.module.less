@import (reference) '../../../less/style/main.less';

.bottomDrawerContainer {
    &.show {
        position: absolute;
        border: 1px solid lightgrey;
        z-index: 1;
        bottom: 0;
        max-height: 30px;
        border-radius: 25px 25px 0px 0px;
        padding: var(--bottom-drawer-p);
        width: 25%;
        background-color: @brown;
        box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        transition: max-height 0.5s, visibility 0.1;

        .collapseBtn {
            height: 10px;
            background-color: #D6D6D6;
            width: 60%;
            margin: auto;
            border-radius: 25px;
            cursor: pointer;
            margin-bottom: var(--bottom-drawer-p);
        }

        .contentContainer {
            width: 100%;
            padding: var(--bottom-drawer-p);
            display: none;
        }

    }

    &.expanded {
        max-height: 50%;
        width: 100%;

        .contentContainer {
            width: 100%;
            display: block;
        }
    }

}