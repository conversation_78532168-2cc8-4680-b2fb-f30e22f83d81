import React, { FC, useEffect, useRef } from "react";
import "./continues-scanner.less";
import { useDebounceValue } from "usehooks-ts";
import { DSLField } from "@typedefs/coffee/dsl";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { fetchFormFilters, IFormData } from "@hooks/index";
import { GS1MedicalParser, GS1ProductCategory } from "@utils/scanner";
import _ from "lodash";
import useToast from "@hooks/use-toast";

export type ContinuesScannerScannerCallBack = (text: string) => void;

type ContinuesScannerScannerProps = {
	k: string;
	v: DSLField;
	form: string;
	dd: IDSLDrawSubForm;
	fld: string;
};

export const ContinuesScannerScanner: FC<ContinuesScannerScannerProps> = (props) => {
	const { k, v, form, dd } = props;
	const [isScanning, setIsScanning] = useDebounceValue<boolean>(false, 100);
	const [showToast] = useToast();

	const stopIsScanningAll = () => {
		if (isScanning) {
			showToast({ type: "info", message: "Scanning is disabled. Click to enable.", autoClose: 1500 }, true);
		}
		setIsScanning(false);
		keepScanning.current = false;
	};

	const textRef = useRef<HTMLInputElement>(null);

	const keepScanning = useRef<boolean>(true);

	useEffect(() => {
		keepScanning.current = true;
		if (!isScanning) {
			return;
		}
		const keepMeFocused = async () => {
			if (!keepScanning.current) return;
			if (textRef.current) {
				if (document.activeElement !== textRef.current) {
					textRef.current.focus();
					if (document.activeElement == textRef.current) {
						textRef.current.value = "";
						setIsScanning(true);
					}
				}
			}
			requestAnimationFrame(keepMeFocused);
		};
		keepMeFocused();
		return () => {
			keepScanning.current = false;
		};
	}, [isScanning]);

	const transformBarcodeData = async (bc: string) => {
		let values: {
			raw: string;
			scan_values: Record<string, string | number | null>;
			inventory: null | IFormData;
		} = {
			raw: bc,
			scan_values: {},
			inventory: null,
		};

		const parser = new GS1MedicalParser();
		const parsedResult = parser.parseGS1Barcode(bc);

		if (
			(parsedResult.errors && parsedResult.errors.length > 0) ||
			!parsedResult.gtin ||
			!parsedResult.lotNumber
		) {
			console.error("Barcode parsing failed or missing essential data:", parsedResult);
			return {
				values,
				error: "Unable to parse required values from barcode",
			};
		}

		values.scan_values.gtin = parsedResult.gtin;
		values.scan_values.lot = parsedResult.lotNumber;
		values.scan_values.serial = parsedResult.serialNumber;
		values.scan_values.serial_no = parsedResult.serialNumber;


		if (parsedResult.expirationDate && typeof parsedResult.expirationDate === 'string') {
			const parts = parsedResult.expirationDate.split('-');
			if (parts.length === 3) {
				values.scan_values.expiration = `${parts[1]}/${parts[2]}/${parts[0]}`;
			} else {
				values.scan_values.expiration = parsedResult.expirationDate;
			}
		} else {
			values.scan_values.expiration = null;
		}

		if (parsedResult.elements['30']) {
			try {
				values.scan_values.quantity = parseFloat(parsedResult.elements['30']);
			} catch (e) {
				console.warn("Could not parse quantity:", parsedResult.elements['30']);
				values.scan_values.quantity = null;
			}
		}

		const originalGtin = values.scan_values.gtin as string;
		const gtinCategory = parsedResult.category;

		const strippedGtin = originalGtin ? originalGtin.replace(/^0+/, '') : null; 

		const inventoryFilter = { item_barcode: [strippedGtin, originalGtin] };
		let inv = await fetchFormFilters("inventory", {
			filter: inventoryFilter,
			limit: 1,
			pool: true,
		});

		console.log("Inventory record:", inv);
		if (inv.success && inv.data.length) {
			values.inventory = inv.data[0];
			values.cost_each = inv.data[0]?.last_cost_ea;
		}
		console.log("Returning values:", values);
		return {
			values,
			error: null,
		};
	};

	const onScannedComplete = async (text: string) => {
		if (textRef.current) {
			textRef.current.value = "";
		}
		if (!text || text.substr(-1) !== "\n") {
			return;
		}

		try {
			const bc = text.trim();
			dd.value_field(k, bc, true, true);
			const frm = window.DSL[form];
			const tfd = await transformBarcodeData(bc);
			const f = dd.field_nodes[k];

			for (const trn of v.view.transform) {
				if (trn.type == "barcode-continues" && window.DSLFx.Transforms[trn.name]) {
					const tup = _.cloneDeep(trn);
					tup["__barcode"] = tfd;
					window.DSLFx.Transforms[trn.name](frm, dd, tup, f, k);
				}
			}
		} catch (e) {
			showToast(
				{
					type: "error",
					position: "bottom-center",
					theme: "dark",
					message: "Invalid barcode. Please try again.",
					prodIgnore: false,
					autoClose: 1500,
				},
				true
			);
		}
	};

	return (
		<div className="continues-scanner-scanner">
			{isScanning ? (
				<>
					<div className="scanner-layover" onClick={stopIsScanningAll}>
						<div className="scanner-layover-inner">
							<div className="fa-3x">
								<i
									className="far fa-scanner fa-shake"
									style={
										{
											"--fa-animation-duration": "2s",
										} as any
									}
								></i>
							</div>
							<div className="center waiting">Waiting for items to be scanned.</div>
							<p>
								Press <span>ESC</span> to stop scanning.
							</p>
						</div>
					</div>
				</>
			) : (
				<div
					className="center start-scan"
					onClick={() => {
						setIsScanning(true);
					}}
				>
					Click here to start scanning
				</div>
			)}
			<input
				className="scanner-input"
				onBlur={(event) => {
					if (event.target && "value" in event.target) {
						event.target.value = "";
					}
				}}
				onKeyDown={(event) => {
					if (event.key == "Enter" && event.target && "value" in event.target)
						onScannedComplete(event.target.value + "\n");
					else if (event.key == "Escape") {
						stopIsScanningAll();
					}
				}}
				ref={textRef}
			></input>
		</div>
	);
};
