@import "../../../components/cards/generic-card-container/generic-card-container.less";
@import (reference) "../../../less/style/main.less";

.continues-scanner-scanner {
	display: flex;
	flex-direction: column;
	gap: 10px;

	.scanner-layover {
		position: fixed;
		width: 100vw;
		height: 100vh;
		text-align: center;
		top: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999999999;

		.scanner-layover-inner {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			gap: 10px;
			padding: 30px;
			border-radius: 12px;
			background-color: rgba(117, 116, 116, 0.7);
			color: @white;

			.waiting {
				font-weight: bold;
				font-size: 16px;
			}

			>p {
				font-size: 16px;
				z-index: 9999999999;
				opacity: 1;

				span {
					background-color: white;
					border-radius: 5px;
					font-size: 14px;
					padding: 5px;
					font-weight: bold;
					color: @coral-red;
				}
			}
		}
	}

	.center {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
		width: 100%;
	}

	.start-scan {
		cursor: pointer;
		padding: var(--spacing-large);
		position: relative;
		border-radius: var(--radius-medium);
		font-size: var(--font-size-xsmall);
		font-weight: var(--font-weight-medium);
		gap: var(--spacing-large);
		background-color: var(--color-tertiary);
		color: var(--white);
		position: relative;
		border: none;

		&::before {
			content: "";
			background-repeat: no-repeat;
			background-image: url("../../../public/icons/common/outlined/scanner.png");
			background-color: transparent;
			background-size: contain;
			width: 20px;
			height: 20px;
		}

	}

	.scanner-input {
		border: none;
		width: 0;
		height: 0;
		position: absolute;
	}

	.fa-spinner {
		font-size: 2em !important;
	}

	.error {
		color: @coral-red;
		text-align: center;
		font-weight: bold;
	}
}