import { getFormIcon } from "@utils/fx";

interface IconAvatarPropsTypes {
    icon?: string;
}

const IconAvatar = (props: IconAvatarPropsTypes) => {
    return (
        <div className={`icon-avatar-container`} >
            <img src={props.icon} alt="" />
        </div>
    );
};

const getFormIconAvatar = (form = "", props = {}) => {
    const icon = getFormIcon(form)
    if (!icon) {
        return
    }
    return <IconAvatar icon={icon} {...props} />
}

export default getFormIconAvatar;