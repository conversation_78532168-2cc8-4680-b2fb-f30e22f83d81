import React, { useState } from 'react';
import './collapsible-card.less';
import icons from '@public/icons';
import { UIComponentProps } from '@utils/interfaces';
import { getSize, getVariant } from '@utils/style-helper';

interface CollapsibleCardProps extends UIComponentProps {
    isCollapsed?: boolean;
}

const CollapsibleCard: React.FC<CollapsibleCardProps> = (props) => {
    const { title, children, className, variant, size, isCollapsed = true } = props;
    const [isOpen, setIsOpen] = useState<boolean>(isCollapsed);

    const toggleCard = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className={`collapsible-card-container ${isOpen ? 'open' : ''} ${className || ""} ${getVariant(variant)} ${getSize(size)}`} >
            <div className={`header ${!isOpen ? 'collapsed' : ''}`}>
                <div className='title' >{title && title || ""}</div>
                <div className='arrow-container' >
                    <img className='arrow' src={isOpen && icons.common.arrowUpOutline || icons.common.arrowDownOutlineIcon} alt="" onClick={toggleCard} />
                </div>
            </div>
            {isOpen && <div className="body">{children}</div>}
        </div >
    );
};

export default CollapsibleCard;
