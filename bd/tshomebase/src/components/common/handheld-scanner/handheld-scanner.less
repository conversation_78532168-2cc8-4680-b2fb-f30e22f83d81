@import "../../../components/cards/generic-card-container/generic-card-container.less";
@import (reference) "../../../less/style/main.less";

.ReactModalPortal {
	.generic-card-container;

	.center {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
	}

	.waiting {
		color: @mid-gray;
	}

	.start-scan {
		cursor: pointer;
		border: 2px solid @mid-gray;
		padding: 17px;
		border-style: dashed;
		font-size: 14px;
		color: @mid-gray;
		border-radius: 12px;
	}

	.scanner-input {
		border: none;
		width: 0;
		height: 0;
		position: absolute;
	}

	.handheld-scanner {
		display: flex;
		flex-direction: column;
		gap: 10px;
		min-width: 320px;

		.fa-spinner {
			font-size: 2em !important;
		}

		.error {
			color: @coral-red;
			text-align: center;
			font-weight: bold;
		}
	}
}
