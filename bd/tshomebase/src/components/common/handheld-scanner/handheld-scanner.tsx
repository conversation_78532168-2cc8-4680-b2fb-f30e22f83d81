import React, { FC, useEffect, useRef, useState } from "react";
import './handheld-scanner.less';
import { createPortalModal, PopupModalRef } from "@blocks/portal-modal";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { useDebounceValue } from "usehooks-ts";
import LoaderNoData from "@components/common/loader-no-data";

type CallBackAction = {
    setError: (error: string) => void,
    closeModal: () => void,
}

export type HandHeldScannerCallBack = (text: string, actions: CallBackAction) => void;

type HandHeldScannerProps = {
    title: string;
    promise: {
        resolve: (data: any) => void;
        reject: (reason: any) => void;
    };
    getModal: () => PopupModalRef;
    onScannerEvent: HandHeldScannerCallBack;
};

const HandHeldScanner: FC<HandHeldScannerProps> = (props) => {
    const { title, promise, getModal, onScannerEvent } = props;

    const [error, setError] = useState<string>("");
    const [isScanning, setIsScanning] = useDebounceValue<boolean>(true, 100);

    const textRef = useRef<HTMLInputElement>(null);
    const keepScanning = useRef<boolean>(true);

    const closeModal = () => {
        keepScanning.current = false;
        promise.resolve({ error: "User Cancelled" });
        getModal().closeModal();
    }

    const onTextInputChange = (text: string) => {
        onScannerEvent(text, {
            closeModal: closeModal,
            setError: (error: string) => {
                if (error && textRef.current) {
                    textRef.current.value = "";
                }
                setError(error);
            }
        });
    }

    useEffect(() => {
        keepScanning.current = true;
        const keepMeFocused = async () => {
            if (!keepScanning.current) return;
            if (textRef.current) {
                if (document.activeElement !== textRef.current) {
                    textRef.current.focus();
                    if (document.activeElement == textRef.current) {
                        textRef.current.value = "";
                        setError("");
                        setIsScanning(true);
                    }
                }
            };
            requestAnimationFrame(keepMeFocused);
        };
        keepMeFocused();
        return () => {
            keepScanning.current = false;
        }
    }, []);

    return (
        <GenericCardContainer
            title={title || "Scan Item"}
            onClick={() => {
                closeModal();
            }}
            icon={icons.common.crossIcon}
        >
            <div className="handheld-scanner">
                {error && <div className="error">{error}</div>}
                {isScanning ? <>
                    <LoaderNoData loading />
                    <div className="center waiting">Waiting for items to be scanned.</div>
                </> :
                    <div className="center start-scan">Click here to start scanning</div>
                }
                <input
                    className="scanner-input"
                    onBlur={(event) => {
                        if (event.target && "value" in event.target) {
                            event.target.value = "";
                        }
                        setIsScanning(false);
                    }}
                    onKeyDown={(event) => {
                        if (event.key == "Enter" &&
                            event.target && "value" in event.target)
                            onTextInputChange(event.target.value + "\n");
                        else if (event.key == "Escape")
                            closeModal();
                    }}
                    ref={textRef}
                    onChange={(event) => {
                        onTextInputChange(event.target.value);
                    }}>
                </input>
            </div>
        </GenericCardContainer>
    );
};


export const textScanner = (title: string, onScannerEvent: HandHeldScannerCallBack) => {
    const modalProps = {
        style: {
            overlay: {
                zIndex: 29999,
            },
            content: {
                zIndex: 29999,
            },
        }
    }
    return new Promise((resolve, reject) => createPortalModal(HandHeldScanner as FC<unknown>, modalProps, { title, onScannerEvent, promise: { resolve, reject } }))
}