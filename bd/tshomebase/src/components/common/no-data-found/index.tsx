import { UIComponentProps } from "@utils/interfaces";
import * as style from "./style.module.less";

export interface NoDataFoundPropsTypes extends UIComponentProps {
    width?: string;
    height?: string;
    text?: string;
}

const NoDataFound = (props: NoDataFoundPropsTypes) => {
    const { width = "100%", height = "100%", text = "No data found", children, onClick, } = props;
    return (
        <div className={style.noDataFoundContainer} style={{ height, width, ...props.style }} onClick={onClick}  >
            {
                children && children
                ||
                <span className={style.noDataFondText}>{text}</span>
            }
        </div>
    );
};

export default NoDataFound;
