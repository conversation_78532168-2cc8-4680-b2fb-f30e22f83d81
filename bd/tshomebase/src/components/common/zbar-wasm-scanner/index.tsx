import React, { FC, useEffect, useRef } from "react";
import * as style from "./style.module.less";
import { ZBarSymbol } from "@undecaf/zbar-wasm";
import { createPortalModal, PopupModalRef } from "@blocks/portal-modal";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import './camera-scanner.less';
import icons from "@public/icons";

export type ScannerResultsCallBack = (results: Array<ZBarSymbol> | null, error: any) => void;

type ZbarWasmScannerProps = {
    title: string;
    promise: {
        resolve: (data: any) => void;
        reject: (reason: any) => void;
    };
    getModal: () => PopupModalRef;
    onScannerResults: ScannerResultsCallBack;
};

const ZbarWasmScanner: FC<ZbarWasmScannerProps> = (props) => {
    const { title, promise, getModal, onScannerResults } = props;
    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    const onAction = (results: Array<ZBarSymbol> | null, error: any) => {
        onScannerResults(results, error);
        promise.resolve({ results, error });
        getModal().closeModal();
    }

    useEffect(() => {
        let stream: MediaStream | null = null;
        const getStream = async () => {
            if (stream) {
                return stream;
            }
            try {
                stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" } });
                return stream;
            } catch (e: any) {
                onAction(null, { error: e.message });
                return null;
            }
        }
        const runScanner = async () => {
            stream = await getStream();
            if (!stream) return;
            if (videoRef.current) videoRef.current.srcObject = stream;

            let stopScanning = false;

            const loop = async () => {
                if (stopScanning) return;
                if (!canvasRef.current) return;
                if (!videoRef.current) return;
                const video = videoRef.current;
                const canvas = canvasRef.current;
                const context = canvas?.getContext("2d", { willReadFrequently: true });

                if (context) {
                    context.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
                }

                if (context && video.videoWidth && video.videoHeight) {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;

                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

                    window.zbarWasm.scanImageData(imageData)
                        .then((symbols: Array<ZBarSymbol>) => {
                            if (symbols && symbols.length > 0) {
                                symbols.forEach(s => s.rawValue = s.decode("utf-8"))
                                onAction(symbols, null);
                                stopScanning = true; // Update scanning flag to stop the loop
                            }
                        })
                        .catch((error: any) => {
                            console.error("Error during barcode scanning:", error);
                        }).finally(() => {
                            // log("run or not");
                        });
                }
                requestAnimationFrame(loop);
            };
            requestAnimationFrame(loop);
        };
        runScanner();
        return () => {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            if (videoRef.current?.srcObject) {
                (videoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
            }
            if (videoRef.current) {
                videoRef.current.srcObject = null;
                videoRef.current.parentNode?.removeChild(videoRef.current);
            }
            canvasRef.current?.parentNode?.removeChild(canvasRef.current);
            stream = null;
        };
    }, []);

    return (
        <GenericCardContainer
            title={title || "Scan Item"}
            className="camera-scanner-popover"
            icon={icons.snap.patient.close}
            onClick={() => {
                onAction(null, { error: "User Cancelled" });
            }}
        >
            <video ref={videoRef} autoPlay playsInline style={{ width: "100%", height: "100%" }} />
            <canvas ref={canvasRef} style={{ display: "none", width: "100%", height: "100%" }} />
        </GenericCardContainer>
    );
};

export default ZbarWasmScanner;


export const cameraScanner = (title: string, onScannerResults: ScannerResultsCallBack) => {
    return new Promise((resolve, reject) => createPortalModal(ZbarWasmScanner as FC<unknown>, {
        style: {
            overlay: {
                zIndex: 999999,
            },
            content: {
                zIndex: 999999,
                padding: 0,
                width: '100%',
                height: '100%',
            },
        },
    }, { title, onScannerResults, promise: { resolve, reject } }))
}

export const inlineCameraScanner = (title: string, onScannerResults: ScannerResultsCallBack) => {
    return new Promise((resolve, reject) => createPortalModal(ZbarWasmScanner as FC<unknown>, {
        style: {
            overlay: {
                zIndex: 999999,
            },
            content: {
                zIndex: 999999,
                padding: 0,
                width: '100%',
                height: '100%',
            },
        },
    }, { title, onScannerResults, promise: { resolve, reject } }))
}