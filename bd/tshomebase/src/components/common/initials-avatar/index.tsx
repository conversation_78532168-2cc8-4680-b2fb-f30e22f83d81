import { UIComponentProps } from '@utils/interfaces';
import * as style from "./style.module.less";
import { getSize, getVariant } from '@utils/style-helper';
import { getInitials } from '@utils/fx';

type InitialsAvatarPropsTypes = UIComponentProps & {
    name?: string | any;
    className?: string;
}

const InitialsAvatar = ({ name, className, variant, size }: InitialsAvatarPropsTypes) => {

    return (
        <div className={`${style.initialAvatarContainer} ${className || ""}  ${getVariant(variant, style)} ${getSize(size, style)}`}>
            <span>{getInitials(name)}</span>
        </div>
    )
}

export default InitialsAvatar;