.initialAvatarContainer {
  border-radius: 50%;
  font-weight: 600;
  color: #5a595880;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  &.primary {
    background: #f8f1ec;
    .sm;
    font-size: var(--sm-avatar-font-size);
  }

  &.secondary {
    background-color: #fff;
    .sm;
    font-size: var(--sm-avatar-font-size);
  }

  &.xs {
    min-width: var(--xs-avatar-width);
    width: var(--xs-avatar-width);
    min-height: var(--xs-avatar-height);
    height: var(--xs-avatar-height);
  }

  &.sm {
    min-width: var(--sm-avatar-width);
    width: var(--sm-avatar-width);
    min-height: var(--sm-avatar-height);
    height: var(--sm-avatar-height);
  }

  &.xl {
    min-width: var(--xl-avatar-width);
    width: var(--xl-avatar-width);
    min-height: var(--xl-avatar-height);
    height: var(--xl-avatar-height);
    font-size: var(--xl-avatar-font-size);
  }
}