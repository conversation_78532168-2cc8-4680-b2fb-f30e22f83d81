
.wf-ph-tab {
    background: white;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 10px 15px 12px;
    width: 100%;
    height: 100%;
    >h2{
        line-height: 30px;
        font-size: 20px;
        font-weight: 600;
        margin: unset;
    }
    .action{
        display: flex;
        justify-content: flex-start;
        gap: 15px;
        flex-wrap: wrap;
        .bl-wl-btn{
            flex-direction: row;
            display: flex;
            border-radius: 15px;
            padding: 5px 18px 5px 18px;
            justify-content: center;
            align-items: center;
            height: 40px;
            background-color: #4D4E8D;
            border: none;
            gap: 13px;
            >img {
                height: 20px;
                width: 20px;
            }
            >p{
                font-size: 14px;
                line-height: 21px;
                color: white;
                font-weight: 600;
            }
        }
    }
}

