import React from "react";
import "./wf-ph-tab.less";

export const WorkFlowPlaceHolderTab = ({form, tabData}) => (
	<div className="wf-ph-tab">
		<div>
			<h2>{`Table: ${window.DSL[form].view.label}`}</h2>
			<div className="action">
				<button className="bl-wl-btn"
					onClick={() => {
						alert(JSON.stringify(tabData));
					}}
				>
					<p>Open Record</p>
				</button>
			</div>
		</div>
	</div>
);