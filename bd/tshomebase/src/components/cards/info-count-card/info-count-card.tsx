import type { FC, SyntheticEvent } from "react";
import React from "react";
import "./info-count-card.less";
type InfoCountCardDataProps = {
	label: string;
	data: Record<string, unknown>;
	dataLoc: (data: Record<string, unknown>) => string | number;
	onClick?: ((e?: SyntheticEvent) => void) | (() => void);
}
type InfoCountCardCountProps = {
	label: string;
	count: string | number;
	onClick?: ((e?: SyntheticEvent) => void) | (() => void);
}

type InfoCountCardProps = InfoCountCardDataProps | InfoCountCardCountProps;

export const InfoCountCard: FC<InfoCountCardProps> = (props) => {
	const { label, dataLoc } = props;
	let { count } = props;
	if (!count && dataLoc) {
		const { data, dataLoc } = props;
		count = dataLoc(data);
	}
	return (
		<div className={`info-count-card`} onClick={props.onClick}>
			<p className="count">{count}</p>
			<p className="field">{label}</p>
		</div>
	);
};