@import (reference) '../../../less/style/main.less';


.info-count-card {
    display: flex;
    color: @purple;
    background: fade(@purple, 8%);
    border-radius: @border-radius-10;
    text-align: start;
    min-height: 45px;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    padding: 6px 16px 6px 16px;
    gap: 6px;
    width: min-content;


    .count {
        font-size: @font-size-32;
        line-height: @line-height-32;

    }

    .field {
        font-size: @font-size-13;
        line-height: @line-height-16;
    }

    @media (max-width: 425px) {
        flex-basis: 48%;
        gap: 5px;
    }

    @media (max-width: 375px) {
        flex-basis: 100%;
        gap: 5px;
    }
}