import type { <PERSON> } from "react";
import React from "react";
import snap from "@public/icons/snap";
import "./warning-card.less";

export interface WarningCardProps {
	warning: string;
}
export const WarningCard: FC<WarningCardProps> = (props) => {
	const { warning } = props;
	return (
		<div className="warning-card">
			<div className="warning-text">{warning}</div>
			<img src={snap.patient.caution} />
		</div>
	);
};
