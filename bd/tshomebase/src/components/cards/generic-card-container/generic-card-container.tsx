import React, { useState } from "react";
import "./generic-card-container.less";
import { UIComponentProps } from "@utils/interfaces";
import PublicIcon from "@public/icons";
interface GenericCardContainerProps extends UIComponentProps {
	icon?: string;
	containerStyle?: React.CSSProperties;
	bodyStyle?: React.CSSProperties;
	headerStyle?: React.CSSProperties;
}

const GenericCardContainer: React.FC<GenericCardContainerProps> = ({
	title = "",
	icon = "",
	className = "",
	containerStyle = {},
	bodyStyle = {},
	headerStyle = {},
	onClick,
	children,
}) => {
	return (
		<div className={`generic-card-container${className ? ` ${className}` : ""}`} style={containerStyle}>
			<div className={`header`} style={headerStyle}>
				<div className="title">{(title && title) || ""}</div>
				{icon && (
					<div className="arrow-container">
						<img src={PublicIcon.general.closeWindow} alt={"Close"} onClick={onClick} />
					</div>
				)}
			</div>
			<div className="body" style={bodyStyle}>
				{children}
			</div>
		</div>
	);
};

export default GenericCardContainer;
