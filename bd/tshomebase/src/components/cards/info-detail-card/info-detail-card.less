@import (reference) '../../../less/style/main.less';

.info-detail-card {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: fit-content;
    font-size: @font-size-13;
    line-height: @line-height-16;
    font-weight: 500;
    gap: 4px;

    .value {
        color: @black;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 24px;
    }

    .field {
        color: @charcoal-gray;
    }

    &.flex-col-100 {
        flex-basis: 100%;
    }

    &.flex-col-50 {
        flex-basis: 50%;
    }

    &.flex-col-25 {
        flex-basis: 25%;
    }

}