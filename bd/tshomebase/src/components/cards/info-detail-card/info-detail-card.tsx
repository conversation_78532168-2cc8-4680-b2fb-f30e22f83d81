import type { FC, SyntheticEvent } from "react";
import React from "react";
import "./info-detail-card.less";

type InfoDetailCardProps = {
	label: string;
	value: string | number;
	className?: string;
	onClick?: ((e?: SyntheticEvent) => void) | (() => void);
}

export const InfoDetailCard: FC<InfoDetailCardProps> = (props) => {
	const { label, value, className } = props;
	return (
		<div className={`info-detail-card${className ? ` ${className}` : ""}`}>
			<div className="value">{value}</div>
			<div className="field">{label}</div>
		</div>
	);
};