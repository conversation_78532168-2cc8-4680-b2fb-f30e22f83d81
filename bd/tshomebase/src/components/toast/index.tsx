import React from "react";
import { toast as Toast, ToastOptions, ToastContent } from "react-toastify";
import "./toast.less";
export interface ToastProps {
	type?: "success" | "error" | "info" | "warning"; // Toast types
	message: string | React.ReactNode; // Message to display
	position?: "top-left" | "top-right" | "bottom-left" | "bottom-right" | "top-center" | "bottom-center"; // Toast position
	autoClose?: number; // Auto-close duration in milliseconds
	hideProgressBar?: boolean; // Show or hide the progress bar
	closeOnClick?: boolean; // Close on click
	pauseOnHover?: boolean; // Pause on hover
	draggable?: boolean; // Draggable
	theme?: "light" | "dark"; // Theme for the toast
	closeButton?: boolean; // Either to show the close button or not.
	prodIgnore?: boolean; // Ignore in production
}
// Function to show the toast
export const toastDismiss = (toastId: number | string) => {
	Toast.dismiss(toastId);
};
export const toast = ({
	type = "info",
	message,
	position = "bottom-center",
	autoClose = 5000,
	hideProgressBar = true,
	closeOnClick = true,
	pauseOnHover = false,
	draggable = false,
	theme = "dark",
	closeButton = true,
	prodIgnore = false,
}: ToastProps) => {
	const toastOptions: ToastOptions = {
		position,
		autoClose,
		hideProgressBar,
		closeOnClick,
		pauseOnHover,
		draggable,
		theme,
		closeButton,
	};
	if (prodIgnore && window.App.version.nes.is_prod) return;
	switch (type) {
		case "success":
			return Toast.success(message as ToastContent, toastOptions);
		case "error":
			return Toast.error(message as ToastContent, toastOptions);
		case "info":
			return Toast.info(message as ToastContent, toastOptions);
		case "warning":
			return Toast.warning(message as ToastContent, toastOptions);
		default:
			return Toast(message as ToastContent, toastOptions);
	}
};
