import { SnapshotWgtActionFunc } from "@components/wgts/snapshot-wgt/snapshot-wgt";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { IFormData } from "@hooks/form-data";
import { FC } from "react";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";

export interface WgtProps {
    linkMap: DSLDrawLinkMap;
    data: IFormData;
    parentId?: string | number;
    tabActions: PatientTabActions;
    wgtData: WgtData;
    index: number;
    onAction?: SnapshotWgtActionFunc;
}

export interface WgtData {
    label: string;
    component: FC<WgtProps>;
    tabActions?: PatientTabActions | null;
    rkey: string;
    open: {
        form: string;
        mode: "list" | "edit" | "read";
        link: string;
    }

}