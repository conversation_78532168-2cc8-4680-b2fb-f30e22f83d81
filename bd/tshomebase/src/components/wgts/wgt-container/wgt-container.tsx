
import type { FC } from "react";
import React, { useState, useEffect } from "react";
import "./wgt-container.less";
import { DragDropContext, Draggable } from "react-beautiful-dnd";
import { SnapshotWgt, SnapshotWgtActionFunc } from "../snapshot-wgt/snapshot-wgt";
import { useLocalStorage } from "usehooks-ts";
import LocalStore from "@enum/local-store";
import _ from "lodash";
import type { WgtData } from "../types";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { StrictModeDroppable } from "@blocks/dnd/strict-mode-droppable";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { IFormData } from "hooks/form-data";

const reorder = (list: unknown[], startIndex: number, endIndex: number) => {
	const result = Array.from(list);
	const [removed] = result.splice(startIndex, 1);
	result.splice(endIndex, 0, removed);
	return result;
};

interface SnapshotWgtsContainerProps {
	wgts: WgtData[];
	tabActions: PatientTabActions;
	linkMap: DSLDrawLinkMap;
	data: IFormData;
	parentId?: string | number;
	onAction?: SnapshotWgtActionFunc;
}

export const SnapshotWgtsContainer: FC<SnapshotWgtsContainerProps> = (props) => {
	const [wgts, setWgts] = useState(props.wgts);
	const defaultOrder = props.wgts.map((item) => item.rkey);
	const [wgtOrder, setWgtOrder] = useLocalStorage<string[]>(LocalStore.PATIENT_WGT_ORDER, defaultOrder); // TODO set default order here

	const onDragEnd = (result) => {
		// dropped outside the list
		if (!result.destination) {
			return;
		}
		const items = reorder(
			wgts,
			result.source.index,
			result.destination.index
		);
		const order: string[] = items.map((item) => item.rkey);
		setWgtOrder(order);
		setWgts([...items]);
	};

	const sortFunc = (a, b) => {
		const sortingArr = wgtOrder;
		return sortingArr.indexOf(a.rkey) - sortingArr.indexOf(b.rkey);
	};

	const updateOrder = (setWegts = false) => {
		wgts.sort(sortFunc);
		const order: string[] = wgts.map((item) => item.rkey);
		if (!_.isEqual(wgtOrder, order)) {
			setWgtOrder(order);
		}
	};

	useEffect(() => {
		updateOrder();
	}, [wgtOrder]);

	return (
		<DragDropContext onDragEnd={onDragEnd}>
			<StrictModeDroppable droppableId="droppable-wgts" direction="vertical">
				{(provided, snapshot) => (
					<div
						className=''
						ref={provided.innerRef}
						{...provided.droppableProps}
					>
						{wgts.map((wgt, index) => (
							<Draggable key={wgt.rkey} draggableId={wgt.rkey} index={index}>
								{(provided, snapshot) => (
									<SnapshotWgt
										key={wgt.rkey}
										title={wgt.label}
										provided={provided}
										snapshot={snapshot}
										wgtData={wgt}
										tabActions={props.tabActions}
										linkMap={props.linkMap}
										onAction={props.onAction}
									>
										<wgt.component
											tabActions={props.tabActions}
											linkMap={props.linkMap}
											wgtData={wgt}
											data={props.data}
											onAction={props.onAction}
											parentId={props.parentId}
											index={index}
										/>
									</SnapshotWgt>
								)}
							</Draggable>
						))}
						{provided.placeholder}
					</div>
				)}
			</StrictModeDroppable>
		</DragDropContext>
	);
};