import type { FC } from "react";
import React from "react";
import "./snapshot-wgt.less";
import icons from "@public/icons";
import { useToggle } from "usehooks-ts";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { WgtData } from "@components/wgts/types";

const getItemStyle = (isDragging: boolean, draggableStyle) => {
	let transform = draggableStyle.transform;
	if (isDragging && transform) {
		transform = `translate(0px, ${transform.split(",")[1]}`;
	}
	return {
		...draggableStyle,
		transform
	};
};
export type SnapshotWgtActionFunc = (action: "list" | "edit", data: WgtData) => void;
export interface SnapshotWgtProps {
	title: string;
	provided?: object;
	snapshot?: object;
	wgtData: WgtData;
	tabActions?: object;
	linkMap?: DSLDrawLinkMap
	children: React.ReactNode;
	onAction?: SnapshotWgtActionFunc;
}

export const SnapshotWgt: FC<SnapshotWgtProps> = (props) => {
	const { title, children, provided = {}, snapshot = {}, wgtData = {}, tabActions = {}, linkMap = {} } = props;

	const [show, toggle] = useToggle(true);

	return (
		<div className="wgt" {...{
			ref: provided?.innerRef,
			...provided?.draggableProps,
		}}
			style={snapshot && provided ? getItemStyle(
				snapshot.isDragging,
				provided.draggableProps.style
			) : {}}
		>
			<div className={`wgt-header${!show ? ' closed' : ''}`} {...provided?.dragHandleProps}>
				<div className="title">
					{title}
				</div>
				<div className="wgt-btns">
					{
						props.onAction &&
						wgtData.open.mode &&
						<img
							className='wgt-ind'
							src={wgtData.open.mode == "edit" ? icons.common.editOutline : icons.common.list}
							onClick={() => {
								if (!props.onAction) return;
								props.onAction(wgtData.open.mode, wgtData);
							}}
						/>
					}
					<img className={`wgt-ind ${!show ? "flip" : ""}`} src={icons.common.arrowUpOutline} onClick={toggle} />
				</div>
			</div>
			{show && children}
		</div>
	);
};