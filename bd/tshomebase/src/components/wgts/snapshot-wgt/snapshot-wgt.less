@import (reference) '../../../less/style/main.less';

.wgt {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    .flip {
        transform: rotate(180deg);
        transition-duration: 500ms;
        transition-property: 'transform';
    }

    .wgt-ind {
        transition-duration: 500ms;
        transition-property: 'transform';
        cursor: pointer;
        width: 16px;
        height: 24px;
        .hover-icon;
    }

    .wgt-content {
        display: flex;
        flex-wrap: wrap;
        .card-four;
        border-top: 1px solid #E0E0E0;
        border-radius: 0px 0px 12px 12px;
        gap: 8px;
        .p-3_5;

        .no-data {
            >a {
                cursor: pointer;
            }
        }

        table {

            width: 100%;

            th,
            td {
                .py-1;
            }

            th {
                color: #9974CE;
                font-size: 14px;
                font-weight: 600;
                align-self: center;
            }

            td {
                font-size: 12px;
                font-weight: 500;
                letter-spacing: 0em;
                text-align: left;
                color: #111111;
            }
        }

        &.wgt-scroll {
            overflow-y: auto;
            max-height: 250px;
        }
    }

    .wgt-sub-container {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        gap: 10px;

        &.wscp {
            padding-left: 18px;
        }
    }

    .wgt-header {
        .card-four;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-radius: 12px 12px 0px 0px;
        height: 52px;
        .p-3_5;

        &.closed {
            border-radius: 12px;
        }

        .title {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            color: #111111;
            font-size: 16px;
            line-height: 24px;
            font-weight: 600;
            text-transform: capitalize;


            >img {
                padding-right: 5px;
            }
        }

        .wgt-btns {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;

            .wgt-btn {
                cursor: pointer;
                width: 14px;
                height: 14px;
                background-color: grey;
            }

            .hold {
                cursor: grabbing !important;
            }
        }
    }
}