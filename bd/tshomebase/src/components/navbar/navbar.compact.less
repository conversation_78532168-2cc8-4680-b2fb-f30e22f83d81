@import (reference) './navbar.vars.less';

.app.compact {
    .nav-main {

        flex: 0 0 (@nav-bar-width-compact);
        background-color: @soft-pearl;
        gap: var(--nav-bar-tmp1-g);

        .nav-top {
            gap: var(--nav-bar-tmp1-g);
        }

        .nav-header-cnt {
            height: auto;
        }

        .nav-btn-cnt,
        .nav-config-btn-cnt {
            .nav-btn {

                .nav-btn-pill {
                    height: auto;

                    .nav-btn-pill-in {
                        .nav-btn-label {
                            font-size: 13px;
                        }
                    }
                }

                &.active {
                    .nav-btn-sep {
                        >div {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }

            .nav-control {
                height: auto;
                font-size: 13px;
            }
        }

        &.nav-opened {
            visibility: visible;
            opacity: 1;
            width: @nav-bar-width-toggled-compact;
            box-shadow: @drawer-box-shadow-four;

            .user-info-inner {
                p {
                    font-size: 13px;
                }
            }

            .nav-btn-cnt,
            .nav-config-btn-cnt {

                .nav-btn .nav-btn-pill .nav-btn-pill-in .nav-btn-label {
                    padding-left: 0px !important;
                }

            }

        }

        &.nav-collapsed {
            flex: 0 0 (@nav-bar-width-collapsed-compact);
            width: @nav-bar-width-collapsed-compact;

            .nav-header-cnt {

                .nav-comp-logo {
                    display: block;
                    padding: @padding-2-5;

                }

            }

            .nav-btn-cnt,
            .nav-config-btn-cnt {

                .nav-btn {
                    .nav-btn-pill {
                        margin: auto;
                        width: 30px;
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        border-radius: 6px;
                        padding: 3px;
                    }
                }

            }

            .nav-control {
                margin: auto;
            }
        }
    }
}