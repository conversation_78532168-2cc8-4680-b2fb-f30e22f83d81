.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    padding: 0 16px 0px 31px;
    background: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .navbar-left {
        &::after {
            content: "beta";
            color: #fff;
            background: rgb(240, 116, 116);
            border-radius: 14px;
            margin-top: -10px;
            margin-left: -10px;
            font-size: 12px;
            position: fixed;
            padding: 0px 4px;
            line-height: 14px;
            font-weight: 600;
        }
        .logo {
            width: 127px;
            height: auto;
        }
    }

    .nav-container {
        flex-grow: 1;
        display: flex;
        justify-content: center;
        margin: 0 20px;
        height: 44px;
    }

    .navbar-center {
        display: flex;
        align-items: center;
        background: white;
        border-radius: 8px;
        padding: 2px 8px;
        height: 44px;
        gap: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.1);
        justify-content: space-between;
      

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border: none;
            background: transparent;
            color: #666;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 6px;
            min-width: 32px;

            .icon-container {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                flex-shrink: 0;
                position: relative;

                img {
                    width: 20px;
                    height: 20px;

                    &.active {
                        filter: grayscale(1) brightness(10);
                    }
                }

                .tooltiptext {
                    visibility: hidden;
                    padding: 10px;
                    background-color: var(--color-tertiary);
                    color: #fff;
                    text-align: center;
                    border-radius: 10px;
                    position: absolute;
                    z-index: 1;
                    top: 150%;
                    opacity: 0;
                    transition: opacity 0.3s;
                    font-family: inter;
                    font-weight: 700;

                }
            }

            .tooltiptext::after {
                content: "";
                position: absolute;
                bottom: 100%;
                left: 50%;
                margin-left: -5px;
                border-width: 5px;
                border-style: solid;
                border-color: transparent transparent  var(--color-tertiary) transparent;
            }

            .label {
                font-size: 14px;
                font-weight: 700;
                max-width: 0;
                overflow: hidden;
                white-space: nowrap;
                opacity: 0;
                padding: 0px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .count {
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 20px;
                height: 20px;
                padding: 0 6px;
                border-radius: 10px;
                background: #f0f0f0;
                color: #666;
                font-size: 12px;
                font-weight: 600;
                margin-left: 0;
                opacity: 0;
                transform: scale(0.8);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            &:hover {
                .label {
                    opacity: 1;
                }

                .count {
                    //   opacity: 1;
                    //   transform: scale(1);
                    //   margin-left: 8px;
                }

                .tooltiptext {
                    visibility: visible;
                    opacity: 1;
                }
            }

            &.active {
                background: var(--color-tertiary);
                box-shadow: 0px 2px 4px 2px #6868681F,
                           0px 1px 2px 0px #00000014,
                           0px -1px 2px 0px #0000001F inset,
                           0px 1px 2px 0px #FFFFFF38 inset;
                color: white;
                padding: 8px 14px;
                animation: expandWidth 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                height: 48px;
                margin: 0 8px;
                border-radius: 8px;

                .label {
                    max-width: 100px;
                    opacity: 1;
                    margin-left: 8px;
                }

                .count {
                    background: rgba(255, 255, 255, 0.9);
                    color:  var(--color-tertiary);
                    opacity: 1;
                    transform: scale(1);
                    margin-left: 8px;
                }
                .tooltiptext {
                    visibility: none;
                    opacity: 0;
                }

            }
        }
    }

    .navbar-right {
        display: flex;
        align-items: center;
        background: white;
        border-radius: 8px;
        padding: 8px 16px; 
        height: 52px;
        gap: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #EBEBE8;

        .search-btn,
        .settings-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 20px;

            &:hover {
                background: rgba(0, 0, 0, 0.05);
            }
        }

        .profile-dropdown {
            position: relative;
            background: white;

            .profile-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 4px 8px 4px 4px;
                border: none;
                background: transparent;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    background: rgba(0, 0, 0, 0.02);
                }

                .avatar {
                    display: none;
                    // width: 32px;
                    // height: 32px;
                    // border-radius: 50%;
                    // object-fit: cover;
                }

                .profile-info {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    margin-right: 4px;

                    .name {
                        font-size: 15px;
                        font-weight: 700;
                        line-height: 18px;
                        color: #1a1a1a;
                    }

                    .role {
                        font-size: 12px;
                        font-weight: 500;
                        line-height: 18px;
                        color: #5E636B;
                       

                    }
                }

                .chevron {
                    transition: transform 0.2s ease;
                    color: #666;

                    &.open {
                        transform: rotate(180deg);
                    }
                }
            }
        }
    }
}

.dropdown-menu {
    position: fixed;
    top: 70px;
    right: 24px;
    width: 200px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 8px;
    z-index: 1000;
    animation: slideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    .dropdown-item {
        width: 100%;
        padding: 8px 16px;
        border: none;
        background: transparent;
        text-align: left;
        cursor: pointer;
        border-radius: 6px;
        color: #1a1a1a;
        font-size: 14px;
        transition: all 0.2s ease;

        &:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        &.logout {
            color: #dc2626;
        }
    }

    .divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 8px 0;
    }
}

@keyframes expandWidth {
    from {
        padding: 8px;
    }

    to {
        padding: 8px 14px;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 12px;

        .nav-container {
            margin: 0 12px;
        }

        .navbar-center {
            .nav-item {
                padding: 8px;

                &:hover,
                &.active {
                    padding: 8px;

                    .label {
                        display: none;
                        max-width: 0;
                        margin-left: 0;
                    }
                }
            }
        }

        .navbar-right {
            .profile-dropdown {
                .profile-btn {
                    .profile-info {
                        display: none;
                    }
                }
            }
        }
    }
}