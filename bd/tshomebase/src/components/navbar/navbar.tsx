import React, { useState, useEffect } from "react";
import "./navbar.less";
import { ModuleItem } from "@typedefs/shared";
import logo from "data-url:@public/logo.png";
import InitialsAvatar from "@components/common/initials-avatar";
import { VARIANT_SECONDARY } from "@utils/const";
import { Tooltip } from "@mui/material";
import { getTooltipStyles } from "@utils/style-helper";
interface NavBarProps {
	modules: ModuleItem[];
	activeModule: string | null;
	onNavChange: (mkey: string) => void;
	handleSiteSelectorClick: () => void;
	nav: any;
}

const Navbar: React.FC<NavBarProps> = ({ modules, activeModule, onNavChange, nav, handleSiteSelectorClick }) => {
	const [activeItem, setActiveItem] = useState<string | null>(null);

	useEffect(() => {
		setActiveItem(activeModule);
	}, [activeModule]);

	const handleItemClick = (item: ModuleItem) => {
		setActiveItem(item.key);
		onNavChange(item.key);
	};

	let url = null;

	if (window.App.company.logo) {
		try {
			const file = window.App.company.logo;
			if (file?.filehash) {
				url = "/api/file/secure/" + file.filehash;
			}
		} catch (e) {
			//pass
		}
	}

	const navigationModules = modules.filter((module) => module.key !== "settings");
	const settingsModule = modules.find((module) => module.key === "settings");
	const userName = [window.App.user.firstname, window.App.user.lastname].filter(Boolean).join(" ");

	return (
		<nav className="navbar">
			<div className="navbar-left">
				<img src={url || logo} alt="Clara" className="logo" />
			</div>

			<div className="nav-container">
				<div className="navbar-center">
					{navigationModules.map((item) => {
						const label =
							item.label === "Patient" ? "Patients" : item.label === "Queue" ? "Workflow" : item.label;

						return (
							<button
								key={item.key}
								className={`nav-item ${activeItem === item.key ? "active" : ""}`}
								onClick={() => handleItemClick(item)}
							>
								<div className="icon-container">
									<Tooltip arrow title={label} componentsProps={getTooltipStyles("primary")}>
										<img
											src={item.icon}
											alt={label}
											className={`nav-icon ${activeItem === item.key ? "active" : ""}`}
										/>
									</Tooltip>
								</div>
								<span className="label">{label}</span>
								{/* {item.count > 0 && <span className="count">{item.count}</span>} */}
							</button>
						);
					})}
				</div>
			</div>

			<div className="navbar-right">
				{/* <SearchBarView nav={nav} /> */}
				{settingsModule && (
					<Tooltip
						arrow
						title={"Site Selector"}
						componentsProps={getTooltipStyles("primary", "var(--color-tertiary)")}
					>
						<button
							className="settings-btn"
							onClick={() => handleSiteSelectorClick()}
							aria-label="Site Selector"
						>
							<i
								className="fa-sharp fa-regular fa-object-group"
								style={{ fontSize: "20px", paddingTop: "2px" }}
							></i>
						</button>
					</Tooltip>
				)}

				{settingsModule && (
					<Tooltip
						arrow
						title={"Settings"}
						componentsProps={getTooltipStyles("primary", "var(--color-tertiary)")}
					>
						<button
							className="settings-btn"
							onClick={() => handleItemClick(settingsModule)}
							aria-label="Settings"
						>
							<svg
								width="20"
								height="20"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
							>
								<circle cx="12" cy="12" r="3" />
								<path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
							</svg>
						</button>
					</Tooltip>
				)}

				<div className="profile-dropdown">
					<button className="profile-btn" onClick={() => onNavChange("my")}>
						<InitialsAvatar
							name={userName}
							variant={VARIANT_SECONDARY}
							className="avatar"
							style={{
								border: "1px solid #fff",
							}}
						/>
						<div className="profile-info">
							<span className="name">{`${window.App.user.firstname} ${window.App.user.lastname}`}</span>
							<span className="role">{window.App.user.role_auto_name}</span>
						</div>
					</button>
				</div>
			</div>
		</nav>
	);
};
export default Navbar;
