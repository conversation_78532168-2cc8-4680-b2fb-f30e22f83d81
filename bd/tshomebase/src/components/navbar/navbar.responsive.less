@import (reference) '../../less/style/main.less';
@import (reference) './navbar.vars.less';

@media (max-width: 768px) {
    .nav-main {

        position: absolute;
        left: 0;
        z-index: 4;
        background-color: @soft-pearl;
        height: 100vh;
        width: 0;
        visibility: hidden;
        opacity: 0;
        overflow: hidden;
        transition: width 0.5s, visibility 0.5s, opacity 0.5s;

        &.nav-opened {
            visibility: visible;
            opacity: 1;
            width: @nav-bar-width-toggled;
            box-shadow: @drawer-box-shadow-four;

            &.nav-collapsed {
                .nav-top {

                    .nav-close {}
                }

            }
        }

        &.nav-collapsed {
            width: @nav-bar-width-collapsed;
            padding-top: 7.5px !important;
            gap: var(--nav-bar-tmp1-g);

            .nav-top {
                gap: 8px;
            }
        }

        p {
            font-size: 13px;
        }

        .nav-top {

            .nav-close {
                min-height: fit-content;
                display: flex;
                justify-content: flex-end;

                .nav-toggle-cross {

                    img {
                        width: 12px !important;
                        height: 12px !important;
                    }
                }
            }
        }


        .nav-header-cnt {
            flex-direction: row-reverse;
            justify-content: space-between;
            align-items: flex-start;
            padding: 5px 0px;


            .nav-comp-logo {
                width: 100%;
                .centered-col;

                img {
                    width: 100%;
                }
            }

        }

        .nav-config-btn-cnt {
            .nav-control {
                width: 95%;
                margin: auto;
            }
        }

        .nav-btn-cnt {
            .nav-btn .nav-btn-pill {
                padding-left: 0px;
            }
        }

        .nav-btn-cnt .nav-btn.active .nav-btn-pill {
            border-radius: @border-radius;
        }

        .nav-btn-cnt .nav-btn .nav-btn-pill .nav-btn-pill-in .nav-btn-label,
        .nav-main .nav-config-btn-cnt .nav-btn .nav-btn-pill .nav-btn-pill-in .nav-btn-label {
            padding-left: 0px;
        }

        .nav-btn-cnt .nav-btn.active .nav-btn-sep:last-child>div,
        .nav-config-btn-cnt .nav-btn.active .nav-btn-sep:last-child>div {
            border-radius: 0px;
        }

        .nav-config-btn-cnt .nav-btn.active .nav-btn-pill {
            border-radius: @border-radius;
        }

        .nav-btn-cnt .nav-btn .nav-btn-sep,
        .nav-config-btn-cnt .nav-btn .nav-btn-sep {
            height: 0px;
        }

        .nav-btn-cnt .nav-btn .nav-btn-pill,
        .nav-config-btn-cnt .nav-btn .nav-btn-pill {
            width: 95%;
            margin: auto;
            gap: 10px;
            padding: 5px 10px;
        }

        .nav-btn-cnt .nav-btn .nav-btn-pill .nav-btn-label {
            font-size: 12px !important;
        }

        .nav-config-btn-cnt .nav-btn .nav-btn-pill .nav-btn-pill-in .nav-btn-label {
            padding-left: 0px;
        }

        .nav-config-btn-cnt .nav-btn-pill .nav-btn-pill-in:last-child {
            left: -20px;
        }
    }

}