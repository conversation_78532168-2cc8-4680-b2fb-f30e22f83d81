/* eslint-disable no-async-promise-executor */
import type { PageDocument, ParameterVariant } from "@mescius/activereportsjs/core";
import { PageReport } from "@mescius/activereportsjs/core";
import { exportDocument as pdfExport, PdfExportResult } from "@mescius/activereportsjs/pdfexport";
import qz from "qz-tray";
import { getReportBasedOnCode, getReportsByCode } from "@modules/report/report-actions";
import type { ReportDescriptor } from "@typedefs/arjs";
import { uuid } from "@utils/fx";
import { getPrinterConfigByName, getPrinters } from "@utils/qz-utils";
import LocalStore from "@enum/local-store";

export type ReportParams = {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[key: string]: ParameterVariant[] | any;
};

type OutputPropsItem = {
	Name: string;
	Value: ParameterVariant[];
};

type DocumentActions = "print" | "download" | "browser";
type ARJsRawPDF = { base64: string; pdfRaw: PdfExportResult; filename: string }

export type DocumentOptions = {
	filename: string;
	printer: string;
	copies: number;
	dataSource: Record<string, object>;
	title: string | null
	site_id: string | null;
	onPrint?: (data: ARJsRawPDF | {
		error: string;
	}) => void;
};

export const getARJSPrint = (
	r: string | ReportDescriptor,
	reportParameters?: ReportParams,
	options?: Partial<DocumentOptions>
) =>
	new Promise<ARJsRawPDF>(async (resolve, reject) => {
		try {
			let report: ReportDescriptor | null = null;
			if (typeof r === "string") {
				report = (await getReportBasedOnCode(r)) as ReportDescriptor;
			} else {
				report = r;
			}
			report = updateReportDataSource(report, options?.dataSource);
			if (!report || !report.json_data) {
				reject({ error: "Failed to fetch report" });
				return;
			}

			const doc = await generateDocument(report, reportParameters);
			if (!doc) {
				reject({ error: "Failed to generate report" });
				return;
			}
			if (!options) {
				options = {};
			}
			if (!options.filename) {
				options.filename = `${report.code}-${uuid()}`;
			}

			const pdf = await pdfExport(doc);
			const base64 = await blobToBase64(pdf.data);
			if (!base64) {
				return reject({ error: "An error occurred while creating the PDF" });
			}
			resolve({
				base64,
				pdfRaw: pdf,
				filename: options.filename,
			});
		} catch (err) {
			console.error(err);
			reject({ error: err });
		}
	});

export const generateDocument = async (report: ReportDescriptor, reportParameters?: ReportParams) => {
	if (!report.json_data) {
		console.error('Report definition not found...');
		return false;
	}
	const reportPage = new PageReport();
	let params: OutputPropsItem[] = [];
	if (reportParameters) {
		params = convertParamsToProps(reportParameters);
	}
	await reportPage.load(report.json_data, {
		reportParameters: params,
	});
	return await reportPage.run();
}

const blobToBase64 = (blob: Blob): Promise<string> => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	return new Promise((resolve, _) => {
		const reader = new FileReader();
		reader.onloadend = () => {
			if (typeof reader.result === "string") {
				resolve(reader.result);
			} else {
				resolve("");
			}
		};
		reader.readAsDataURL(blob);
	});
}

export const performDocumentAction = async (docx: PageDocument, action: DocumentActions, options?: Partial<DocumentOptions>) => new Promise(async (resolve, reject) => {
	if (action === "browser") {
		docx.print();
		return resolve({});
	} else if (action === "download") {
		const pdf = await pdfExport(docx);
		pdf.download(options?.filename);
		return resolve({});
	} else if (action === "print") {
		const printers = await getPrinters();
		if (!printers) {
			return reject({ error: "Failed to connect to printers/ is QZ-Try installed and running?" });
		}
		const pdf = await pdfExport(docx);
		const base64 = await blobToBase64(pdf.data);
		if (!base64) {
			return reject({ error: "An error occurred while creating the PDF" });
		}
		const pconfig = await getPrinterConfigByName(options?.printer || "", {
			copies: options?.copies || 1
		});
		if (!pconfig) {
			reject({ error: "Failed to fetch printer, check if the printer is connected or configure another printer in preferences" });
			return;
		}
		qz.print(pconfig, [{
			type: "pixel",
			format: "pdf",
			flavor: "base64",
			data: base64.split(",")[1],
		}]).then(() => {
			resolve({});
		}).catch((err: unknown) => {
			reject({ error: err });
		});
	}
})
// I'm too lazy to return values so I'm just going to update report by reference
export const updateReportDataSource = (report: ReportDescriptor | null, dataSource?: Record<string, any>) => {
	if (!report || !report.json_data) {
		return report;
	}
	if (!(typeof report.json_data == "object")) {
		return report;
	}
	if (!dataSource) {
		return report;
	}
	if (!(typeof dataSource == "object")) {
		return report;
	}
	report.json_data?.DataSources?.forEach((ds) => {
		if (ds && ds.Name && dataSource?.[ds.Name] && ds?.ConnectionProperties?.ConnectString) {
			ds.ConnectionProperties.ConnectString = "jsondata=" + JSON.stringify(dataSource?.[ds.Name]);
		}
	});
	return report;
}

export const generateAndPrintReport = (r: string | ReportDescriptor, action: DocumentActions, reportParameters?: ReportParams, options?: Partial<DocumentOptions>) => new Promise(async (resolve, reject) => {
	try {
		window.prettyNotify("Printing Report(s)...",);
		let report: ReportDescriptor | null = null;
		if (typeof r === "string") {
			report = await getReportBasedOnCode(r) as ReportDescriptor;
		} else {
			report = r;
		}
		report = updateReportDataSource(report, options?.dataSource);
		if (!report || !report.json_data) {
			window.prettyNotify();
			window.prettyError("Error", "Failed to fetch report");
			reject({ error: "Failed to fetch report" });
			return;
		}

		const doc = await generateDocument(report, reportParameters);
		if (!doc) {
			window.prettyNotify();
			window.prettyError("Error", "Failed to generate report")
			reject({ error: "Failed to generate report" });
			return;
		}
		if (!options) {
			options = {}
		}
		if (!options.filename) {
			options.filename = `${report.code}-${uuid()}`;
		}
		if (!options.printer && action === "print") {
			const defaultPrinter = window.UserPreference.subform_printers.filter((p: any) => p.site_id === null) || [];
			if (options.site_id) {
				const printers = window.UserPreference.subform_printers.filter((p: any) => p.site_id === options?.site_id) || [];
				if (printers.length > 0) {
					options.printer = printers[0][reportParameters?.printer] || "";
				} else if (defaultPrinter.length > 0) {
					options.printer = defaultPrinter[0][reportParameters?.printer] || "";
				}
			} else if (defaultPrinter.length > 0) {
				options.printer = defaultPrinter[0][reportParameters?.printer] || "";
			} else if (reportParameters?.printer) {
				options.printer = reportParameters?.printer;
			}
		}
		await performDocumentAction(doc, action, options);
		window.prettyNotify();
		resolve({});
	} catch (err) {
		window.prettyNotify();
		console.error(err);
		reject({ error: err });
	}
});

function convertParamsToProps<T extends ReportParams>(inputObject: T): OutputPropsItem[] {
	const outputArray: OutputPropsItem[] = [];
	for (const key in inputObject) {
		outputArray.push({ Name: key, Value: inputObject[key] });
	}
	return outputArray;
}