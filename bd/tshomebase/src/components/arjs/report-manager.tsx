import "./report-manager.less"
import React, { FC, ReactNode, useEffect, useState } from "react";
import { Viewer } from "./viewer";
import { useReport } from "./useReport";
import type { ReportMangerProps } from "@typedefs/arjs";
import NoDataFound from "@components/common/no-data-found";
import SpinLoader from "@components/common/spin-loader";
import { useOnScreen } from "@hooks/on-screen";
import { CRErrorBoundary } from "@blocks/error-boundary/cr-error-boundary";
import { generateAndPrintReport, getARJSPrint } from "@components/arjs";
import { request } from "@core/request";

const ErrorComponent = ({ message }: { message: string }) => <div>{message}</div>;


const ReportManager = (props: ReportMangerProps) => {
	const { report, fetchReportInfo } = useReport(props.code);
	const [isOnScreen, divRef] = useOnScreen();

	useEffect(() => {
		if (report || !isOnScreen) {
			return;
		}
		if (props.code) {
			void fetchReportInfo();
		} else {
			console.error("Report Code is required");
		}
	}, [isOnScreen]);

	const onPrint = async () => {
		if (props?.options?.onPrint) {
			getARJSPrint(report || props.code, props.parameters, props.options).then((data) => {
				props?.options?.onPrint?.(data);
			}).catch((error) => {
				props?.options?.onPrint?.(error);
			});
			return;
		}
		try {
			let site_id = null;
			if (props.parameters?.patient_id) {
				const resp = await request({
					url: `/form/patient/${props.parameters?.patient_id}`,
				});
				if (resp.data.length > 0) {
					site_id = resp.data[0].site_id;
				}
			}
			generateAndPrintReport(props.code, "print", props.parameters, {...props.options, site_id: site_id});
		} catch (error) {
			console.error("Error generating and printing report:", error);
		}
	}

	return (
		<CRErrorBoundary>
			<div className="report-manager-cnt" ref={divRef}>
				{
					(report && report.error) ? (<NoDataFound><ErrorComponent message={report.error} /></NoDataFound>) : null
				}
				{
					(report && !report.error) ? <Viewer
						report={report}
						parameters={props.parameters}
						options={props.options}
						toolbarVisible={report.type == 'Chart' ? false : props.toolbarVisible}
						fullscreen={props.fullscreen}
						toolbarBtns={props.toolbarBtns}
						onPrint={onPrint}
					/> : null
				}
				{
					!report && <SpinLoader loading={true} fontSize="2em" />
				}
			</div>
		</CRErrorBoundary>

	)
};
ReportManager.defaultProps = {
	toolbarVisible: true,
	fullscreen: true,
	toolbarBtns: [
		"$printPDF",
		"$split",
		"$zoom",
		"$split",
		"$navigation"
	]
};

const MemoizedReportManager = React.memo(ReportManager);

export { MemoizedReportManager as ReportManager };





