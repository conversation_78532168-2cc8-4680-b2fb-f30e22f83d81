
import React, { useCallback, useState } from "react";
import type { ParameterVariant } from "@mescius/activereportsjs-react";
import type { ErrorHandler, ErrorMessage, ReportDescriptor, ViewerProps } from "@typedefs/arjs";
import { Viewer as ReportViewer } from "@mescius/activereportsjs-react";
import { edit, print } from "@public/icons/actions";
import { updateReportDataSource } from "@components/arjs";

type OutputPropsItem = {
	Name: string;
	Value: ParameterVariant[];
};
type ReportParams = {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[key: string]: ParameterVariant[] | any;
};


export const Viewer = ({ report, parameters, onEdit, onPrint, sidebarVisible = false, toolbarVisible = false, zoom = "FitToWidth", onError, options, fullscreen, toolbarBtns }: ViewerProps) => {
	const ref = React.useRef<ReportViewer>(null);
	const [error, setError] = useState<ErrorMessage | null>(null);
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const reportUri: (report: ReportDescriptor) => any = useCallback(report => {
		if (report.json_data) {
			report = updateReportDataSource(report, options?.dataSource) as ReportDescriptor;
			return report.json_data;
		} else if (report.url) {
			return report.url;
		}
		return [];
	}, []);

	const errorHandler: ErrorHandler = (err) => {
		setError(err);
		onError?.(err);
		console.error(err);
	};

	const unexpectedError = () => {
		const error: ErrorMessage = { details: "Unable to load ARJs Viewer", severity: "error", message: "Unexpected error" }
		errorHandler(error);
	}

	React.useEffect(() => {
		const viewerInstance = ref.current?.Viewer;
		if (!viewerInstance) {
			unexpectedError();
			return
		};

		viewerInstance.zoom = "FitPage";
		viewerInstance.toolbar.addItem({
			key: "$openDesigner",
			text: "Design",
			icon: {
				type: "svg",
				content: <><img src={edit} /></>
			},
			enabled: true,
			action: () => (onEdit ? onEdit() : undefined),
		});

		viewerInstance.toolbar.addItem({
			key: "$printPDF",
			text: "Print",
			icon: {
				type: "svg",
				content: <><img className="print-btn-custom" src={print} /></>
			},
			enabled: onPrint && !error ? true : false,
			action: () => (onPrint ? onPrint() : undefined),
		});
		const btns = toolbarBtns || [
			"$openDesigner",
			"$split",
			"$printPDF",
			"$split",
			"$navigation",
			"$split",
			"$refresh",
			"$split",
			"$history",
			"$split",
			"$zoom",
			"$fullscreen",
			"$split"
		]
		viewerInstance.toolbar.updateLayout({
			default: btns,
			fullscreen: btns,
		});
	}, [ref.current]);

	function convertParamsToProps<T extends ReportParams>(inputObject: T): OutputPropsItem[] {
		if (!inputObject) {
			return [];
		}
		const outputArray: OutputPropsItem[] = [];
		for (const key in inputObject) {
			outputArray.push({ Name: key, Value: inputObject[key] });
		}
		return outputArray;
	}


	React.useEffect(() => {
		if (!ref.current) {
			unexpectedError();
			return;
		}

		if (report && parameters) {
			const params = convertParamsToProps(parameters);
			void ref.current.Viewer.open(reportUri(report), { ReportParams: params });
		}
		else if (report && !report.error) {
			void ref.current.Viewer.open(reportUri(report));
		} else {
			unexpectedError();
		}
	}, [ref?.current]);

	return <ReportViewer ref={ref} zoom={zoom} sidebarVisible={sidebarVisible} toolbarVisible={toolbarVisible} fullscreen={fullscreen || false} errorHandler={errorHandler as any} />;
};
