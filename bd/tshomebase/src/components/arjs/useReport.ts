
import { useState, useCallback } from 'react';
import { getReportBasedOnCode } from '@modules/report/report-actions';
import type { ReportDescriptor } from '@typedefs/arjs';

export function useReport(code: string) {
	const [report, setReport] = useState<ReportDescriptor | null>(null);

	const fetchReportInfo = useCallback(async () => {
		try {
			const data = await getReportBasedOnCode(code) as ReportDescriptor;
			setReport(data);
		} catch (error) {
			setReport({
				error: error,
			})
			console.error(error);
		}
	}, [code]);

	return { report, fetchReportInfo };
}
