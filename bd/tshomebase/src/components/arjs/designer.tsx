/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect } from "react";
import type { DesignerProps, ReportDescriptor } from "@typedefs/arjs";
import {
	Designer as ReportDesigner,
} from "@mescius/activereportsjs-react";

const reportUri: (report: ReportDescriptor) => any = (report) =>
	report.json_data
		? { definition: report.json_data, displayName: report.name }
		: { id: report.url, displayName: report.name };

export const Designer = ({ report, onRender, onSave, onCreate, onSaveAs, onQuery }: DesignerProps) => {
	const designerRef = React.useRef<ReportDesigner>(null);
	const queryBtnName = "queries-button"


	useEffect(() => {
		setTimeout(() => {
			addQueryBtn()
		}, 1000)
	}, [designerRef?.current])

	useEffect(() => {
		return () => {
			removeQueryBindings()
		}
	}, [])

	const addQueryBtn = () => {
		if (!onQuery) return;
		const container = $(designerRef?.current?._host?.current)
		const anchor = container.find('[data-aid="preview-button"]')
		if (anchor.length == 0) return;
		if (container.find(`[data-aid="${queryBtnName}"]`).length) return;

		const queryBtn = `
		<div id="main_toolbar_Item_99999" class="gc-toolbar__item-container gc-toolbar__item-container--padding">
			<button type="button" class="gc-btn gc-btn--block gc-btn--with-icon gc-btn--transparent gc-size-sm" data-aid="${queryBtnName}">
				<i class="gc-icon gc-icon--core gc-btn__icon gc-accent-color">
					<img height="16px" width="16px" src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22%23205f78%22%20d%3D%22M20.71%207.04c.39-.39.39-1.04%200-1.41l-2.34-2.34c-.37-.39-1.02-.39-1.41%200l-1.84%201.83l3.75%203.75M3%2017.25V21h3.75L17.81%209.93l-3.75-3.75L3%2017.25Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E">
				</i>
				<span class="gc-btn__text gc-accent-color">
				Queries
				</span>
			</button>
		</div>
		`
		anchor.parent().before(queryBtn);
		container.find(`[data-aid="${queryBtnName}"]`).on('click', onQuery)
	}

	const removeQueryBindings = () => {
		if (!onQuery) return;
		$(designerRef?.current?._host?.current).find(`[data-aid="${queryBtnName}"]`).off()
	}



	return (
		<ReportDesigner
			report={report ? reportUri(report) : null}
			onRender={onRender}
			onSave={onSave}
			onCreate={onCreate}
			onSaveAs={onSaveAs}
			ref={designerRef}
		/>
	)
}

