@import "../cards/generic-card-container/generic-card-container.less";

.ReactModalPortal {
  .generic-card-container;

  .logout-confirmation {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 320px;

    .btn-primary {
      width: max-content;
      color: white;

      &.logout {
        background-color: var(--color-tertiary);
      }

      &.cancel {
        background-color: #CCCCCC;
        color: #5e636b;
      }

      &:hover {
        opacity: 0.8;
      }
    }

    .footer {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }
  }
}
