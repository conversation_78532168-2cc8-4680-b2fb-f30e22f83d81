import React, { useContext, useEffect, useState } from "react";
import "./arjs-popover.less";
import type { FC } from "react";
import { createPortalModal, type PopupModalRef } from "@blocks/portal-modal/portal-modal";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { ReportMangerProps } from "@typedefs/arjs";
import { DocumentOptions, ReportManager, ReportParams } from "@components/arjs";
import { openPartialFormFillPopup } from "@blocks/dsl-card-view";
import { generateAndPrintReport, Viewer } from "@components/arjs";
import { getPrinters } from "@utils/qz-utils";
import { showToastError } from "@utils/fx";
import { request } from "@core/request";

interface ARJSViewerPopupProps extends ReportMangerProps {
	getModal: () => PopupModalRef;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
	title: string;
}

interface PrintRequest {
	query_head: string;
	report: string;
	printer: string;
	no_of_copies: number;
	query_params: string;
	header: string;
}

interface PrinterPreference {
	delivery_ticket_printer: string;
	pharmacy_label_printer: string;
	pharmacy_work_order_printer: string;
}

interface ReportItem {
	report_id: string;
	rx_id: number;
}

const MAPPINGS = {
	queryHead: {
		delivery_ticket_info: "delivery_ticket_info",
		po_label: "po_labels",
		Pharmacy_PO_Work_Order: "work_order",
	},
	printer: {
		delivery_ticket_info: "delivery_ticket_printer",
		po_label: "labels_ticket_printer",
		Pharmacy_PO_Work_Order: "work_order_printer",
	},
	copies: {
		delivery_ticket_info: "no_of_delivery_ticket",
		po_label: "no_of_labels",
		Pharmacy_PO_Work_Order: "no_of_work_order",
	},
	headers: {
		delivery_ticket_info: "Deliver Ticket",
		po_label: "Po Label",
		Pharmacy_PO_Work_Order: "Work Order",
	}
};

export const ARJSViewerPopup: FC<ARJSViewerPopupProps> = (props) => {
	const { getModal, promise, title, code, parameters, options } = props;
	const onCancel = () => {
		getModal().closeModal();
		promise.resolve(null);
	};

	useEffect(() => {
		if (window._ARJPopover?.onCancel) {
			try {
				window._ARJPopover.onCancel();
			} catch (e) { }
		}
		window._ARJPopover = { onCancel };
	}, []);

	return (
		<GenericCardContainer
			title={title || "Viewer"}
			className="arjs-popover"
			icon={icons.snap.patient.close}
			onClick={onCancel}
		>
			<ReportManager
				actionType="view"
				code={code}
				parameters={parameters || {}}
				options={options || {}}
			/>
		</GenericCardContainer>
	);
};

export const PrintQuantityPopup: FC<ARJSViewerPopupProps> = (props) => {
	const { getModal, promise, title, code, parameters, options } = props;
	const onCancel = () => {
		getModal().closeModal();
		promise.resolve(null);
	};

	useEffect(() => {
		if (window._ARJPopover?.onCancel) {
			try {
				window._ARJPopover.onCancel();
			} catch (e) { }
		}
		window._ARJPopover = { onCancel };
	}, []);

	return (
		<GenericCardContainer
			title={title || "Viewer"}
			className="print-quantity-popover"
			icon={icons.snap.patient.close}
			onClick={onCancel}
		/>
	);
};

export const openARJsPopover = (
	title: string,
	code: string,
	parameters?: ReportParams,
	options?: Partial<DocumentOptions>
) => {
	return new Promise((resolve, reject) =>
		createPortalModal(
			ARJSViewerPopup as FC<unknown>,
			{
				style: {
					overlay: {
						width: "0px",
						height: "0px",
						zIndex: 999999,
					},
					content: {
						zIndex: 999999,
					},
				},
				onRequestClose: () => {
					reject({ success: false, error: "Unexpected Error" });
				},
			},
			{ title, code, parameters, options, promise: { resolve, reject } }
		)
	);
};

export const getPrintData = async ({ url, method, header, report, printer, apiform, no_of_copies }: any) => {
	try {
		const response = await fetch(url, { method });
		if (!response.ok) {
			showToastError(`Failed to fetch report data: ${header}`);
			throw new Error(`Failed to fetch print data: ${response.statusText}`);
		}
		const data = await response.json();
		return {
			title: header,
			report,
			options: {
				title: header,
				id: data?.id || null,
				printer,
				patient_id: data?.patient_id || null,
				dataSource: { apiform: data },
				copies: no_of_copies,
			},
			data: {
				dataSource: { apiform: data },
			},
		};
	} catch (error) {
		console.error("Error fetching print data:", error);
		return null;
	}
};

export const updatePref = async (key: string, value: any, site_id?: string) => {
	try {
		const response = await request({
			url: `/my/preference/`,
			method: "PUT",
			data: {
				[key]: value,
				site_id,
			},
		});

		if (response.data?.id) {
			window.UserPreference = { ...window.UserPreference, ...response.data };
		}
	} catch (error) {
		console.error("PreferenceContextProvider: Error updating preferences:", error);
	}
};

export const getPreferencePrinter = (printerType: string) => {
	try {
		const preference = window.UserPreference;
		const currentSiteId = window?.sitesSelected?.[0];
		const subformPrinters = preference.subform_printers?.find((p: any) =>
			currentSiteId ? p.site_id === currentSiteId : p.site_id === null
		);
		return subformPrinters?.[printerType] || null;
	} catch (error) {
		console.error("Error getting preference printer:", error);
		return null;
	}
};

export const setPreferencePrinter = async (printerPrefs: PrinterPreference) => {
	try {
		const preference = window.UserPreference;
		const currentSiteId = window?.sitesSelected?.[0];
		const subformPrinters = preference.subform_printers?.find((p: any) =>
			currentSiteId ? p.site_id === currentSiteId : p.site_id === null
		);

		await updatePref("subform_printers", [{
			...subformPrinters,
			...printerPrefs,
		}]);
	} catch (error) {
		console.error("Error setting preference printer:", error);
		showToastError("An error occurred while setting the preference printer");
	}
};

export const getPrintDataForAllReports = async (
	reports: PrintRequest[],
	labelsReports: ReportItem[],
	workOrderReports: ReportItem[],
) => {
	try {
		const requests: Promise<any>[] = [];
		
		reports.forEach(item => {
			if (item.report === 'delivery_ticket_info') {
				requests.push(getPrintData({
					url: `/api/query/${item.query_head}?${item.query_params}`,
					method: "GET",
					header: item.header,
					report: item.report,
					printer: item.printer,
					apiform: item.query_head,
					no_of_copies: item.no_of_copies
				}));
				return;
			}

			if (item.report === 'po_label' || item.report === 'Pharmacy_PO_Work_Order') {
				let reportList: ReportItem[] = [];
				
				if (item.report === 'po_label') {
					reportList = labelsReports;
				} else if (item.report === 'Pharmacy_PO_Work_Order') {
					reportList = workOrderReports;
				}

				reportList.forEach(rx => {
					requests.push(getPrintData({
						url: `/api/query/${item.query_head}?x1=${rx.rx_id}`,
						method: "GET",
						header: item.header,
						report: rx.report_id,
						printer: item.printer,
						apiform: item.query_head,
						no_of_copies: item.no_of_copies,
					}));
				});
			}
		});

		return requests;
	} catch (error) {
		console.error("Error fetching print data:", error);
		return [];
	}
};

export const openPrintQuantityPopover = async (formData: any, parameters: any) => {
	try {
		const printers = await getPrinters();
		if (!printers) return;

		const dslFields = window.DSL["view_print_copies"].fields;
		['delivery_ticket_printer', 'labels_ticket_printer', 'work_order_printer'].forEach(field => {
			dslFields[field].model.source = printers.map((p: any) => p.name);
		});

		const presetDefaultPrinter = {
			delivery_ticket_printer: getPreferencePrinter("delivery_ticket_printer"),
			labels_ticket_printer: getPreferencePrinter("pharmacy_label_printer"),
			work_order_printer: getPreferencePrinter("pharmacy_work_order_printer"),
			no_of_delivery_ticket: 2,
			no_of_labels: 1,
			no_of_work_order: 1,
		};

		let labelsReports: ReportItem[] = [];
		let workOrderReports: ReportItem[] = [];

		if (!parameters?.query_parameters?.rx_ids?.length) {
			showToastError("No Printable Items Found");
			return;
		}

		try {
			const response = await request({
				url: `/query/labels_reports?x1=${parameters.query_parameters.rx_ids.join(',')}`,
				method: "GET",
			});

			if (response.data?.length) {
				response.data.forEach((rx: any) => {
					if (rx.template_type === 'PO') {
						workOrderReports.push({
							report_id: 'Pharmacy_PO_Work_Order',
							rx_id: rx.rx_id,
						});
					} else if (rx.template_type === 'Injection') {
						workOrderReports.push({
							report_id: 'work_order_injectable',
							rx_id: rx.rx_id,
						});
					} else if (rx.template_type === 'IV') {
						workOrderReports.push({
							report_id: 'work_order_iv',
							rx_id: rx.rx_id,
						});
					}
					labelsReports.push({
						report_id: rx.report_id,
						rx_id: rx.rx_id,
					});
				});

			}
		} catch (error) {
			console.error("Error fetching print data:", error);
			showToastError("Failed to fetch print data");
			return;
		}

		openPartialFormFillPopup({
			form: "view_print_copies",
			preset: presetDefaultPrinter,
			sections: ["Print Options"],
			containerStyle: {
				maxWidth: "40%",
				width: "40%",
				minWidth: "40%",
			},
			bodyStyle: {
				marginTop: "0px",
			},
			btnLabels: {
				save: "Print Selected",
			},
			btnConfig: {
				save: {
					onClick: async (props, formRef, fd, close) => {
						if (!fd) return;
						try {
							const printRequests = fd.list.map((item: string) => ({
								query_head: MAPPINGS.queryHead[item as keyof typeof MAPPINGS.queryHead] || item,
								report: item,
								printer: fd[MAPPINGS.printer[item as keyof typeof MAPPINGS.printer]] || "",
								no_of_copies: fd[MAPPINGS.copies[item as keyof typeof MAPPINGS.copies]] || 1,
								query_params: parameters.query_parameters[item] || "",
								header: MAPPINGS.headers[item as keyof typeof MAPPINGS.headers] || "Unknown Header",
							}));
							props.getModal().closeModal();
							props.promise.resolve(fd);

							setTimeout(async () => {
								const results = await getPrintDataForAllReports(
									printRequests,
									labelsReports,
									workOrderReports,
								);
								window.prettyNotify("Collecting Data to Print....")
								results.forEach(async printData => {
									if (printData) {
										try {
											const resolvedData = await printData;
											generateAndPrintReport(resolvedData.report, "print", {}, resolvedData.options);
										} catch (error) {
											console.error("Error generating and printing report:", error);
											showToastError("An error occurred while generating and printing the report");
										}
									}
								});
							}, 100);
							window.prettyNotify()
							const prefPrinter: PrinterPreference = {
								delivery_ticket_printer: fd?.delivery_ticket_printer,
								pharmacy_label_printer: fd?.labels_ticket_printer,
								pharmacy_work_order_printer: fd?.work_order_printer,
							};
							await setPreferencePrinter(prefPrinter);
						} catch (error) {
							window.prettyNotify()
							console.error("Error processing print request:", error);
							showToastError("An error occurred while processing the print request");
						}
					},
				},
				cancel: {
					onClick: (props, formRef, fd, close) => {
						window.prettyNotify()
						props.getModal().closeModal();
						props.promise.resolve(fd);
					},
				},
			},
		});
	} catch (error) {
		console.error("QZ Tray: Unable to Fetch Printer List", error);
		showToastError("Failed to fetch printer list");
	}
};