import * as React from "react";
import Dialog from "@mui/material/Dialog";
import "./swim-lane.less";

interface SwimLanePopUpBoxProps {
    open: boolean;
    onClose: () => void;
    dragItem: {}
}

const SwimLanePopUpBox: React.FC<SwimLanePopUpBoxProps> = (props) => {
    const { open, onClose } = props;
    const handleClose = () => {
        onClose();
    };

    return (
        <>
            <Dialog
                open={open}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <div onClick={(event) => event.stopPropagation()} className="main-container-swim-lane">
                    <div className="header">
                        <strong>Details</strong>
                        <i onClick={() => handleClose()} className="fa fa-times-circle pull-right close-popover" />
                    </div>
                    <div className="footer">
                        <div className="logout">
                            <button onClick={handleClose} className='btn btn-danger'>Close</button>
                        </div>
                    </div>
                </div>
            </Dialog>
        </>
    );
};

export default SwimLanePopUpBox;
