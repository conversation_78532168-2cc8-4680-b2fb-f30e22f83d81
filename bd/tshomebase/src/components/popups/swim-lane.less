.main-container-swim-lane {
    background-color: white;
    width: 500px;
    padding: 10px;

    .header {
        display: flex;
        flex-direction: row;
        background-color: white;
        justify-content: space-between;
        padding: 4px 8px 4px 8px;
        font-weight: 500;
        border-top-right-radius: -40px;
        border-top-left-radius: 4px;
        border-bottom: 1px solid #eee;

        i {
            font-size: 24px;
            cursor: pointer;
        }

    }

    .footer {
        width: 100%;
        display: flex;
        justify-content: end;
        align-items: center;
        padding: 10px 8px 4px 8px;
        background-color: #eee;
        font-weight: 500;
        background-color: white;

        button {
            margin-left: 5px;
        }

    }
}