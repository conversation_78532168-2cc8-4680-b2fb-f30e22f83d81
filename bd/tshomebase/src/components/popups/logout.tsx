
import React from "react";
import type { FC } from "react";
import type { PopupModalRef } from "@blocks/portal-modal/portal-modal";
import "./logout.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";

interface LogoutConfirmationBoxProps {
	getModal: () => PopupModalRef;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
}
export const LogoutConfirmationBox: FC<LogoutConfirmationBoxProps> = (props) => {
	const { getModal, promise } = props;

	const onLogout = () => {
		window.Auth.logout();
		onCancel();
	};
	const onCancel = () => {
		getModal().closeModal();
		promise.resolve(null);
	};
	return (
		<GenericCardContainer
			title="Confirm Logout"
			onClick={onCancel}
			icon={icons.common.crossIcon}
		>
			<div className="logout-confirmation">
				<div className="info-detail-card">Do you want to logout of your account?</div>
				<div className="footer">
					<button
						className="btn-primary cancel"
						onClick={() => {
							getModal().closeModal();
						}}
					>
						Close
					</button>
					<button
						className="btn-primary logout"
						onClick={() => {
							onLogout();
						}}
					>
						Logout
					</button>
				</div>
			</div>
		</GenericCardContainer>
	);
};
