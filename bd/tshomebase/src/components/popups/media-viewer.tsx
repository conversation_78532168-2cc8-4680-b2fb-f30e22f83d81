import React from "react";
import type { FC } from "react";
import { createPortalModal, type PopupModalRef } from "@blocks/portal-modal/portal-modal";
import "./media-viewer.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { CLFile } from "@typedefs/window";
import NoDataFound from "@components/common/no-data-found";
// import { Document } from "react-pdf";

interface MediaViewerPopupProps {
	getModal: () => PopupModalRef;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
	file: CLFile;
}
export interface MediaRendererProps {
	file: CLFile;
	v1?: boolean;
}

export const MediaRenderer: FC<MediaRendererProps> = ({ file, v1 = false }) => {
	const { filehash, filename, mimetype } = file || {};
	const link = `/api/file/secure/${filehash}`;
	const src = encodeURIComponent(link.trim());
	const ext = /(?:\.([^.]+))?$/.exec(filename)?.[1]?.toLowerCase();
	if (ext == "pdf") {
		if (v1) {
			// return <Document file={src} />;
			return <iframe src={`public/viewer/web/viewer.html?file=${src}`} width="100%" height="100%"></iframe>;
		} else {
			return <iframe src={`public/viewer/web/viewer.html?file=${src}`} width="100%" height="100%"></iframe>;
		}
	} else if (ext && ["gif", "jpeg", "jpg", "png"].includes(ext)) {
		return <img src={link.trim()} />;
	} else if (ext && ["doc", "docx"].includes(ext)) {
		return (
			<iframe
				src={`https://view.officeapps.live.com/op/embed.aspx?src='${window.CRBaseURL + link}'`}
				width="100%"
				height="100%"
			></iframe>
		);
	} else {
		return <NoDataFound text="Attachment unavailable!" />;
	}
};

export const MediaViewerPopup: FC<MediaViewerPopupProps> = (props) => {
	const { getModal, promise, file } = props;
	const { filename } = file || {};

	const onCancel = () => {
		getModal().closeModal();
		promise.resolve(null);
	};
	return (
		<>
			<GenericCardContainer
				title={filename || "Media Viewer"}
				className="media-viewer"
				icon={icons.snap.patient.close}
				onClick={() => {
					onCancel();
				}}
			>
				{<MediaRenderer file={file} />}
			</GenericCardContainer>
		</>
	);
};

export const openMediaViewerPopup = (file: CLFile) => {
	new Promise((resolve, reject) =>
		createPortalModal(
			MediaViewerPopup as FC<unknown>,
			{
				style: {
					overlay: {
						width: "0px",
						height: "0px",
					},
				},
			},
			{ file, promise: { resolve, reject } }
		)
	);
};
