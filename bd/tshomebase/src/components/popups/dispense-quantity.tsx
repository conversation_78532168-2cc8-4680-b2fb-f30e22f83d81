import React, { useEffect, useState } from "react";
import type { FC } from "react";
import type { PopupModalRef } from "@blocks/portal-modal/portal-modal";
import "./order-selection.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";

interface PrescriptionDispenseQuantityProps {
	getModal: () => PopupModalRef;
	quantityToPull: number;
	lotQuantity: number;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
}

export const PrescriptionDispenseQuantity: FC<PrescriptionDispenseQuantityProps> = (props) => {
	const { getModal, promise } = props;
	const [quantityToDispense, setQuantityToDispense] = useState(0);
	const [error, setError] = useState("");

	return (
		<GenericCardContainer
			title="Quantity to Dispense"
			onClick={() => {
				getModal().closeModal();
				props.promise.resolve(null);
			}}
			icon={icons.common.crossIcon}
		>
			<div className="patient-order-selector">
				<div className="quantity-to-pull">
					<div className="quantity-to-pull-label">Quantity to Pull</div>
					<div className="quantity-to-pull-value">{props.quantityToPull}</div>
				</div>
				<div className="lot-quantity">
					<div className="lot-quantity-label">Lot Quantity</div>
					<div className="lot-quantity-value">{props.lotQuantity}</div>
				</div>
				<div className="quantity-to-dispense">
					<div className="quantity-to-dispense-label">Quantity to Dispense</div>
					<input type="number" className="quantity-to-dispense-input" onChange={(e) => {
						const value = parseInt(e.target.value);
						if (isNaN(value) || value <= 0) {
							setError("Invalid quantity entered");
						} else if (value > props.lotQuantity) {
							setError("Quantity exceeds available amount in lot");
						} else if (value > props.quantityToPull) {
							setError("Quantity exceeds remaining amount to be pulled");
						} else {
							setQuantityToDispense(value);
							setError("");
						}
					}} />
					{error && <div className="error-message">{error}</div>}
				</div>

				<div className="order-select-footer">
					<button
						className="btn-primary"
						disabled={quantityToDispense === 0 || error !== ""}
						onClick={() => {
							getModal().closeModal();
							promise.resolve(quantityToDispense);
						}}
					>
						Select
					</button>
				</div>
			</div>
		</GenericCardContainer>
	);
};