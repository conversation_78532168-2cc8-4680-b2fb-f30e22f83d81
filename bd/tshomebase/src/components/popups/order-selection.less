@import "../cards/generic-card-container/generic-card-container.less";

.order-select-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.ReactModalPortal {
	.generic-card-container {
		background-color: #d4d4d4;

		.header {
			gap: 10px;
			border-bottom: none;
			background-color: #d4d4d4;

			.title {
				font-size: 18px;
				font-weight: 500;
				color: #5b83b0;
			}
		}

		.body {
			border-radius: 10px;
			background: #ffffff;
			min-width: 420px;

			.patient-order-selector {
				min-width: 420px;

				.field-select {
					min-width: 420px;
				}

				.order-select-footer {
					.btn-primary {
						font-size: 14px;
						font-weight: 500;

						&:hover {
							background: var(--color-tertiary);
						}

						&:disabled {
							background-color: #ffffff;
						}
					}
				}
			}

			.info-detail-card {
				&:not(disabled) {
					color: #5e636b;
					font-size: 14px;
					font-weight: 700;
				}
			}
		}
	}

	.patient-order-selector {
		display: flex;
		flex-direction: column;
		gap: 10px;
		max-width: 420px;
		min-width: 420px;

		.select-field-container {
			flex: 1;
			box-sizing: border-box;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			position: relative;

			.select-field-label {
				position: absolute !important;
				top: var(--spacing-standard);
				left: var(--spacing-xlarge);
				font-size: var(--font-size-xsmall);
				font-weight: var(--font-weight-regular);
				color: var(--color-text);
				z-index: 1;
				pointer-events: none;
			}
		}

		.btn-primary {
			width: 50%;

			&:disabled {
				background-color: @light-gray;
				color: @light-gray;
			}

			&:disabled:hover {
				color: @light-gray;
			}
		}

		.order-select-footer {
			display: flex;
			justify-content: flex-end;
			gap: 10px;
		}
	}
}
