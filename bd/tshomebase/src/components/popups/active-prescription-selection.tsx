import React, { useEffect, useState } from "react";
import type { FC } from "react";
import type { PopupModalRef } from "@blocks/portal-modal/portal-modal";
import "./order-selection.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { FieldSelect } from "@dsl/fields/field-select";
import { StylesConfig } from "react-select";
import { createPortalModal } from "@blocks/portal-modal/portal-modal";

interface ActivePrescriptionSelectionProps {
	getModal: () => PopupModalRef;
	patientId: string;
	additionalParams?: string[];
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
}

export const ActivePrescriptionSelection: FC<ActivePrescriptionSelectionProps> = (props) => {
	const { getModal, patientId, promise, additionalParams = [] } = props;
	const [prescription, setPrescription] = useState({ id: "", auto_name: "", record: {} });
	useEffect(() => {
		if (!patientId) {
			getModal().closeModal();
			promise.resolve(null);
		}
	}, []);

	if (!patientId) {
		return null;
	}
	let filters = [
		`filter=patient_id:${patientId}`,
		`filter=status_id:1`,
		`filter=status_id:5`,
		`filter=site_id:!1000`,
		...additionalParams,
	];

	const customStyles: StylesConfig = {
		control: (provided, state) => ({
			...provided,
			height: "60px",
			borderRadius: "8px",
			border: `1px solid ${state.isFocused ? "#CBD5E0" : "#E3E5E8"}`,
			boxShadow: "0px 1px 2px 1px #38383814 inset",
			"&:hover": {
				borderColor: "#CBD5E0",
			},
			padding: "10px",
		}),
		valueContainer: (provided) => ({
			...provided,
			marginTop: "18px",
			padding: "0px",
		}),
		placeholder: (provided) => ({
			...provided,
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			lineHeight: "1.2",
			color: "#9B9FA8",
		}),
		input: (provided) => ({
			...provided,
			margin: "0",
			padding: "0",
			color: "#414651",
		}),
		indicatorsContainer: (provided) => ({
			...provided,
			height: "100%",
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		indicatorSeparator: (provided) => ({
			...provided,
			display: "none",
		}),
		clearIndicator: (provided) => ({
			...provided,
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		dropdownIndicator: (provided) => ({
			...provided,
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		menu: (provided) => ({
			...provided,
			marginTop: "8px",
			borderRadius: "8px",
			boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
		}),
		menuList: (provided) => ({
			...provided,
			padding: "8px",
		}),
		menuPortal: (provided) => ({
			...provided,
			zIndex: 9999,
		}),
		option: (provided, state) => ({
			...provided,
			padding: "12px 16px",
			borderRadius: "6px",
			backgroundColor: state.isSelected ? "#EDF2F7" : "transparent",
			color: "#4A5568",
			"&:hover": {
				backgroundColor: "#F7FAFC",
			},
		}),
	};

	return (
		<GenericCardContainer
			title="Prescription Selection"
			onClick={() => {
				getModal().closeModal();
				props.promise.resolve(null);
			}}
			icon={icons.common.crossIcon}
		>
			<div className="patient-order-selector">
				<div className="select-field-container">
					<label className="select-field-label">New supply order for prescription:</label>
					<FieldSelect
						form="careplan_order_rx"
						defaultValueSource={prescription.id}
						defaultValueAutoName={prescription.auto_name || ""}
						multi={false}
						extraParams={`filter=patient_id:${patientId}&${filters.join("&")}`}
						sort="-id"
						disabled={false}
						onChange={(val, an, record) => {
							setPrescription({ id: val, auto_name: an, record });
						}}
						customStyles={customStyles}
						placeholder="Please Select Prescription"
					/>
				</div>

				<div className="order-select-footer">
					<button
						className="btn-primary"
						disabled={!prescription.id}
						onClick={() => {
							getModal().closeModal();
							promise.resolve(prescription.id);
						}}
					>
						Select
					</button>
					<button
						className="btn-primary"
						onClick={() => {
							getModal().closeModal();
							promise.resolve(null);
						}}
					>
						Skip
					</button>
				</div>
			</div>
		</GenericCardContainer>
	);
};

export const openActivePrescriptionSelection = async (patientId: string | number, additionalParams?: string[]) => {
	return new Promise<string | null>((resolve, reject) =>
		createPortalModal(
			ActivePrescriptionSelection as FC<unknown>,
			{},
			{ promise: { resolve, reject }, patientId, additionalParams }
		)
	);
};
