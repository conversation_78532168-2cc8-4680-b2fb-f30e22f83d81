import React, { useEffect, useState } from "react";
import type { FC } from "react";
import type { PopupModalRef } from "@blocks/portal-modal/portal-modal";
import "./order-selection.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { FieldSelect } from "@dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { StylesConfig } from "react-select";
import { fetchFormData } from "@hooks/index";

interface PrescriptionDispenseSelectionProps {
	getModal: () => PopupModalRef;
	patientId: string;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
}

export const PrescriptionDispenseSelection: FC<PrescriptionDispenseSelectionProps> = (props) => {
	const { getModal, patientId, promise } = props;
	const [rxDispense, setRxDispense] = useState({ id: "", auto_name: "", record: null });
	useEffect(() => {
		if (!patientId) {
			getModal().closeModal();
			promise.resolve(null);
		}
	}, []);

	if (!patientId) {
		return null;
	}
	let addFilters = "filter=status:Ready to Contact&filter=delivery_ticket_id:null";

	const customStyles: StylesConfig = {
		control: (provided, state) => ({
			...provided,
			height: "60px",
			borderRadius: "8px",
			border: `1px solid ${state.isFocused ? "#CBD5E0" : "#E3E5E8"}`,
			boxShadow: "0px 1px 2px 1px #38383814 inset",
			"&:hover": {
				borderColor: "#CBD5E0",
			},
			padding: "10px",
		}),
		valueContainer: (provided) => ({
			...provided,
			marginTop: "18px",
			padding: "0px",
		}),
		placeholder: (provided) => ({
			...provided,
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			lineHeight: "1.2",
			color: "#9B9FA8",
		}),
		input: (provided) => ({
			...provided,
			margin: "0",
			padding: "0",
			color: "#414651",
		}),
		indicatorsContainer: (provided) => ({
			...provided,
			height: "100%",
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		indicatorSeparator: (provided) => ({
			...provided,
			display: "none",
		}),
		clearIndicator: (provided) => ({
			...provided,
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		dropdownIndicator: (provided) => ({
			...provided,
			color: "#717680",
			"&:hover": {
				color: "#4A5568",
			},
		}),
		menu: (provided) => ({
			...provided,
			marginTop: "8px",
			borderRadius: "8px",
			boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
		}),
		menuList: (provided) => ({
			...provided,
			padding: "8px",
		}),
		menuPortal: (provided) => ({
			...provided,
			zIndex: 9999,
		}),
		option: (provided, state) => ({
			...provided,
			padding: "12px 16px",
			borderRadius: "6px",
			backgroundColor: state.isSelected ? "#EDF2F7" : "transparent",
			color: "#4A5568",
			"&:hover": {
				backgroundColor: "#F7FAFC",
			},
		}),
	};

	return (
		<GenericCardContainer
			title="Prescription Selection"
			onClick={() => {
				getModal().closeModal();
				props.promise.resolve(null);
			}}
			icon={icons.common.crossIcon}
		>
			<div className="patient-order-selector">
				<div className="select-field-container">
					<label className="select-field-label">Please select a prescription to continue.</label>
					<FieldSelect
						form="careplan_order_rx_disp"
						defaultValueSource={rxDispense.id}
						defaultValueAutoName={rxDispense.auto_name || ""}
						multi={false}
						extraParams={`filter=patient_id:${patientId}&${addFilters}`}
						sort="-id"
						disabled={false}
						onChange={(val, an, record) => {
							setRxDispense({ id: val, auto_name: an, record: record });
						}}
						customStyles={customStyles}
						placeholder="Please Select Prescription"
					/>
				</div>

				<div className="order-select-footer">
					<button
						className="btn-primary"
						disabled={!rxDispense.id}
						onClick={() => {
							getModal().closeModal();
							fetchFormData("careplan_order_rx_disp", rxDispense.id, true, "all")
								.then((res) => {
									if (res.success) {
										promise.resolve(res.data);
									} else {
										promise.resolve(null);
									}
								})
								.catch((err) => {
									console.log("err", err);
									promise.resolve(null);
								});
						}}
					>
						Select
					</button>
				</div>
			</div>
		</GenericCardContainer>
	);
};
