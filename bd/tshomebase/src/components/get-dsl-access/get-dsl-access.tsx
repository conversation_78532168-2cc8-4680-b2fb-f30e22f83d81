import { request } from "@core/request/request";

export const getDslAccess = async (dbRoleId, role) => {
	const resultObject: { [key: string]: number } = {};
	const processAccess = (access: string[], role: string, table_name: string, prefix: string) => {
		if (access && access.includes(role)) {
			resultObject[`${table_name}.${prefix}`] = 1;
		}
	};

	const got_role = role;

	for (let i = 0; i < Object.keys(window.DSL).length; i++) {
		const table_name = Object.keys(window.DSL)[i];
		const parsedData = JSON.parse(JSON.stringify(window.DSL[table_name]));

		if (parsedData?.model?.access?.read_all?.includes(got_role)) {
			resultObject[`${table_name}.model.read_all`] = 1;
			Object.keys(parsedData.fields).forEach((fieldName) => {
				resultObject[`${table_name}.${fieldName}.read`] = 1;
			});
		}

		if (parsedData?.model?.access?.create_all?.includes(got_role)) {
			resultObject[`${table_name}.model.create_all`] = 1;
			Object.keys(parsedData.fields).forEach((fieldName) => {
				resultObject[`${table_name}.${fieldName}.create`] = 1;
			});
		}
		// Process field-level access
		Object.keys(parsedData.fields).forEach((fieldName) => {
			const fieldAccess = parsedData.fields[fieldName].model.access;
			if (fieldAccess) {
				processAccess(fieldAccess.read, got_role, table_name, `${fieldName}.read`);
				processAccess(fieldAccess.read_all, got_role, table_name, `${fieldName}.read_all`);
				processAccess(fieldAccess.create, got_role, table_name, `${fieldName}.create`);
				processAccess(fieldAccess.create_all, got_role, table_name, `${fieldName}.create_all`);
				processAccess(fieldAccess.update, got_role, table_name, `${fieldName}.update`);
				processAccess(fieldAccess.update_all, got_role, table_name, `${fieldName}.update_all`);
				processAccess(fieldAccess.request, got_role, table_name, `${fieldName}.request`);
				processAccess(fieldAccess.delete, got_role, table_name, `${fieldName}.delete`);
				processAccess(fieldAccess.review, got_role, table_name, `${fieldName}.review`);
				processAccess(fieldAccess.rereview, got_role, table_name, `${fieldName}.rereview`);
				processAccess(fieldAccess.write, got_role, table_name, `${fieldName}.create`);
			}
		});

		// Process model-level access
		const modelAccess = parsedData.model.access;
		if (modelAccess) {
			processAccess(modelAccess.read, got_role, table_name, 'model.read');
			processAccess(modelAccess.read_all, got_role, table_name, 'model.read_all');
			processAccess(modelAccess.create, got_role, table_name, 'model.create');
			processAccess(modelAccess.create_all, got_role, table_name, 'model.create_all');
			processAccess(modelAccess.update, got_role, table_name, 'model.update');
			processAccess(modelAccess.update_all, got_role, table_name, 'model.update_all');
			processAccess(modelAccess.request, got_role, table_name, 'model.request');
			processAccess(modelAccess.delete, got_role, table_name, 'model.delete');
			processAccess(modelAccess.review, got_role, table_name, 'model.review');
			processAccess(modelAccess.rereview, got_role, table_name, 'model.rereview');
			processAccess(modelAccess.write, got_role, table_name, 'model.create');
		}
	}

	if (role && dbRoleId && Object.keys(resultObject).length > 0) { //if result object exist
		let sa = await request({
			url: '/form/sec_assign/?filter=type:role_dsl&filter=role:' + dbRoleId,
			method: 'GET',
		})
		if (sa.data?.length > 0 && sa?.data[0]?.id) {
			//update
			await request({
				url: '/form/sec_assign/' + sa.data[0].id,
				method: 'PUT',
				data: {
					dsl: resultObject
				}
			})
		} else {
			//create
			await request({
				url: '/form/sec_assign/',
				method: 'POST',
				data: {
					dsl: resultObject,
					type: 'role_dsl',
					role: dbRoleId
				}
			});
		}
	}
};
