@import (reference) "../../less/style/main.less";

.suggestion-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  max-height: 80vh;
  position: absolute;
  top: 42px;
  right: 3%;
  padding: 10px;
  background-color: white;
  gap: 10px;
  width: 94%;
  border-radius: @border-radius-8;
  z-index: 13;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);

  .suggestion-heading-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: -webkit-fill-available;
    height: 24px;
    gap: 3px;
    margin-bottom: 5px;
    margin-top: 5px;
    padding: 0;
    min-height: min-content;

    .heading-text-container {
      text-align: start;
      white-space: nowrap;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: @purple;
    }

    .hr-line {
      width: inherit;
      height: 0px;
      margin: 0px;
      border: 1px solid rgba(235, 234, 234, 1);
    }

    .active-tab {
      text-decoration: underline;
      text-decoration-thickness: 3px;
      text-underline-offset: 7.5px;
      line-height: 50px;
      text-decoration-color: @purple;
    }
  }

  .tab-bar {
    width: 100%;
    height: 50px;
    min-height: 50px;
    background: white;
    position: sticky;
    top: 0;
    z-index: 15;
    margin-bottom: 0;
    margin-top: 0;
    padding-top: 0;
    box-shadow: 0 5px 4px -4px rgba(0, 0, 0, 0.5);
  }

  @media (max-width: 768px) {
    width: 300px;
    right: 0;
  }
}

.padding-top-0 {
  padding-top: 0 !important;
}

.scrollable {
  overflow-x: hidden;
}

.suggestion-container-item:last-child {
  margin-bottom: 0px;
}

.custom-hr::after {
  content: "";
  flex-grow: 1;
  border-top: 1px solid #ebeaea;
  margin: 0 3px;
}