@import (reference) "../../less/style/main.less";

@gray-color: rgb(218, 215, 215);
@text-font-size: 15px;
@selected-text-color: @charcoal-gray;

.search-result-container {
  .fc;
  .p-1_5;
  gap: 10px;
  cursor: pointer;
  border: 1px solid @lighter-gray;
  border-radius: @border-radius-4;
  width: 100%;

  position: relative;
  flex: 0 0 auto;
  height: 86px;

  .record-status {
    .fr;
    .p-1_5;
    .tc;
    border: 1px solid transparent;
    color: @white;
    position: absolute;
    top: 0;
    right: 0;
    background-color: @semi-transparent-light-gray-25;
    height: 21px;
    width: 21px;
    border-radius: @border-radius-6;
    align-items: center;
    justify-content: center;

    .record-status-text {
      font-weight: 600;
      font-size: 12px;
      // line-height: 12px;
    }
  }

  .record-type {
    .record-status;
    width: auto;

    .record-type-text {
      .record-status-text;
      width: auto;
    }
  }

  .selected-record {
    .p-1_5;
    height: 21px;
    width: auto;
    border-radius: @border-radius-6;
    background-color: @purple;
  }

  .search-text-container {
    .fc;
    .gap-2;
    width: 100%;
    height: 100%;
    justify-content: space-between;

    .search-info-container {
      .fr;
      font-size: 14px;
      color: @charcoal-gray;
      line-height: @line-height-14;
      font-weight: 600;
      width: inherit;
      position: relative;
      flex-wrap: wrap;
      height: 24px;

      .patient-name {
        flex-grow: 1;
        width: 50%;
      }

      .date-container {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        position: relative;
        font-size: 14px;

        .patient-ag {
          width: auto;
          white-space: nowrap;
        }
      }

      .patient-id {
        width: auto;
        white-space: nowrap;
        font-weight: 500;
        font-size: 14px;
        line-height: 13.2px;
        color: rgba(145, 162, 168, 1);
        flex-grow: 1;
      }
    }

    .address-container {
      .fc;
      align-items: start;
      width: inherit;
      height: 12px;

      .patient-addr {
        height: inherit;
        width: auto;
        white-space: nowrap;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: @semi-transparent-light-brown-5;
      }
    }

    .updated-on-container {
      width: inherit;
      height: 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 5px;

      .updated-on-text {
        height: inherit;
        width: auto;
        white-space: nowrap;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: @semi-transparent-light-brown-5;
      }

      .updated-on-date {
        width: auto;
        white-space: nowrap;
        font-weight: 600;
        line-height: 13.2px;
        color: @charcoal-gray;
        font-size: 12px;
      }
    }
  }

  .buttons-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .suggestion-button {
      width: fit-content;
      padding: 5px;
      background-color: @semi-transparent-light-gray-25;
      border-radius: 4px;
      border: none;

      .suggestion-button-text {
        color: @dark-gray;
        font-size: 11px;
        font-weight: 500;
        text-align: center;
        line-height: 16px;

        .bold-text {
          font-weight: 600;
        }

        .underline {
          border-bottom: 2px solid @purple;
        }
      }
    }
  }
}

.selected-suggestion {
  background-color: @lighter-gray;
  border-color: @purple;

  .selected-record-status {
    background-color: @purple;
  }

  .search-text-container {
    .address-container {
      .selected-addr {
        color: @charcoal-gray;
      }
    }
  }

  .buttons-container {
    .selected-suggestion-button {
      background-color: @semi-transparent-purple-4;

      .selected-button-text {
        color: @dark-gray;
      }
    }

    .selected-button {
      background-color: @purple;

      .selected-button-text {
        color: @white;

        .underline {
          border-bottom: 2px solid @white;
        }
      }
    }
  }
}

.pst-tooltip-container {
  visibility: hidden;
  width: 40px;
  height: 15px;
  background-color: @purple;
  border-radius: 2px;
  padding: 1px 3px;
  position: absolute;
  z-index: 10001;
  bottom: -18px;
  left: 10px;
  opacity: 0;
  transition: opacity 0.3s;
  line-height: 24px;

  .pst-tooltip-text {
    width: 33px;
    height: 13px;
    font-weight: 500;
    font-size: 12px;
    line-height: 13.2px;
    color: rgba(255, 255, 255, 1);
    text-align: center;
  }
}

.pst-tooltiptext-hover {
  visibility: visible;
  opacity: 1;
}

.pst-highlighted-text {
  background-color: @purple-08;
}