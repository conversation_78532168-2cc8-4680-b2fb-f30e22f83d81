import React, {
	useState,
	useEffect,
	use<PERSON>emo,
	use<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	MouseEventHandler,
} from "react";
import "./search-result.less";
import { calculateAge, getFormattedDate } from "@utils/fx";
interface SearchResultProps {
	item: {
		data: Record<string, string>;
		selectedButtonIndex?: number;
		keyword?: string;
		path?: string;
	};
	source: string;
	mouseDownEvent: (path: string) => void;
	changeFocus: (arg: number) => void;
	isSelected: boolean;
	suggestionContainerRef: React.RefObject<HTMLDivElement>;
	index: number;
}

const SearchResult: React.FC<SearchResultProps> = ({
	item,
	mouseDownEvent,
	changeFocus,
	isSelected,
	suggestionContainerRef,
	index,
	source,
}) => {
	const { data, selectedButtonIndex, keyword } = item;
	const [selectedButton, setSelectedButton] = useState<number>(
		selectedButtonIndex ? selectedButtonIndex : 0
	);
	const [showToolTip, setShowToolTip] = useState<boolean>(false);
	const [name, setName] = useState<string>("");
	const [dob, setDob] = useState("");
	const [description, setDescription] = useState<string>("");
	const [externalId, setExternalId] = useState("");
	const [recordStatus, setRecordStatus] = useState("");
	const [gender, setGender] = useState("");
	const [lastUpdated, setLastUpdated] = useState("");
	const [npi, setNpi] = useState("");
	const [hcpc, hcpcVal] = useState<string | null>(null);
	const [ndc, ndcVal] = useState<string | null>(null);
	const [recordType, setRecordType] = useState<string | null>(null);
	const [manufacturer, setManufacturer] = useState<string | null>(null);
	const { id } = data;
	const patientButtons = [
		{ name: "Snapshot" },
		{ name: "Insurance" },
		{ name: "Prescriber" },
		{ name: "Meds" },
		{ name: "Allergies" },
		{ name: "Diagnosis" } /*{name: "Fill"}*/,
	];
	const buttonMap: never[] = [];
	const buttons = source === "Patient" ? patientButtons : buttonMap;
	const Buttons = () => {
		const patientButtons = [
			{ name: "Snapshot" },
			{ name: "Insurance" },
			{ name: "Prescriber" },
			{ name: "Meds" },
			{ name: "Allergies" },
			{ name: "Diagnosis" },
		];
		return (
			<div id={`button-container-${index}`} className="buttons-container">
				{source === "Patient" &&
					patientButtons.map((button, buttonIndex) => (
						<button
							className={`suggestion-button${isSelected ? " selected-suggestion-button" : ""
								} ${isSelected && selectedButton === buttonIndex
									? "selected-button"
									: ""
								}`}
							key={buttonIndex}
							data-index={buttonIndex}
							onMouseDown={(e) => handleMouseDownEvent(e, buttonIndex)}
						>
							<div
								className={`suggestion-button-text${isSelected ? " selected-button-text" : ""
									}`}
							>
								<span className="bold-text underline">{button.name[0]}</span>
								{button.name.slice(1)}
							</div>
						</button>
					))}
			</div>
		);
	};
	useEffect(() => {
		switch (source) {
			case "Patient":
				setName(`${data?.lastname}, ${data?.firstname}`);
				setDescription(
					`${data.home_street} ${data.home_street2}. ${data.home_city}, ${data.home_state} ${data.home_zip}`.replaceAll('null', '')
				);
				setDob(data?.dob);
				setExternalId(data?.external_id);
				setRecordStatus(data?.patient_status);
				setGender(data?.gender?.split("")[0]);
				break;
			case "Inventory":
				setName(data.name || data.auto_name);
				setDescription(data?.auto_name);
				if (data.updated_on !== "" || data.updated_on !== null) {
					setLastUpdated(data.updated_on);
				} else {
					setLastUpdated(data.created_on);
				}
				// setRecordStatus(data?.active);
				hcpcVal(data.hcpc);
				ndcVal(data.ndc);
				setRecordType(data?.type);
				setManufacturer(data?.manufacturer_id_auto_name.replaceAll('null', ''));
				break;
			case "Physician":
				setName(`${data.last}, ${data.first}`);
				setDescription(
					`${data.address} ${data.address2}. ${data.city}, ${data.state} ${data.zip}`.replaceAll('null', '')
				);
				setNpi(data?.npi);
				break;
			case "Sales":
				setName(`${data?.lastname}, ${data?.firstname}`);
				setDob(data?.dob);
				setDescription(
					`${data.street} ${data.street2}. ${data.city}, ${data.state} ${data.zip}`.replaceAll('null', '')
				);
				setRecordType(data?.type);
				break;
		}
	}, []);
	const status: Record<string, string> = {
		Active: "A",
		Cancelled: "C",
		Deceased: "D",
		Inactive: "I",
		'On Hold': "H",
		Pending: "P",
		Referred: "R"
	};
	const checkParentNodes = (element: EventTarget | null, id: string) => {
		let currentElement = element as HTMLElement;
		while (currentElement != null) {
			if (currentElement.id == id) {
				return true;
			}
			currentElement = currentElement.parentNode as HTMLElement;
		}
		return false;
	};
	const handleFocus: MouseEventHandler<HTMLDivElement> = (e) => {
		if (
			!checkParentNodes(e.target, `button-container-${index}`) &&
			!isSelected
		) {
			changeFocus(index);
		}
	};
	useEffect(() => {
		if (selectedButtonIndex) {
			setSelectedButton(selectedButtonIndex);
		} else {
			setSelectedButton(0);
		}
	}, [selectedButtonIndex]);

	const highlightText = (text: string, keyword: string) => {
		const keywords = keyword ? keyword.split(" ").filter(Boolean) : [];

		if (keywords.length === 0) {
			return text;
		}

		const parts = text?.split(
			new RegExp(`(${keywords.map(_.escapeRegExp).join("|")})`, "gi")
		);

		return (
			parts && (
				<span>
					{parts.map((part, index) => {
						const matchedKeyword = keywords.find(
							(kw) => part.toLowerCase() === kw.toLowerCase()
						);
						return matchedKeyword ? (
							<span key={index} className="pst-highlighted-text">
								{part}
							</span>
						) : (
							<span key={index}>{part}</span>
						);
					})}
				</span>
			)
		);
	};

	const handleMouseDownEvent = (
		event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
		buttonIndex: number = 0
	) => {
		setSelectedButton(buttonIndex);
		if (!isSelected) {
			changeFocus(index);
		}
		// @ts-ignore
		mouseDownEvent(id, data, buttonIndex);
	};
	const handleMouseOver = () => {
		setShowToolTip(true);
	};
	const handleMouseOut = () => {
		setShowToolTip(false);
	};
	const calculatedAge = useMemo(() => calculateAge(dob), [dob]);
	const age = dob !== "" ? calculatedAge : "Nil";
	return (
		<>
			<div
				className={`search-result-container${isSelected ? " selected-suggestion" : ""
					}`}
				onMouseDown={handleFocus}
				ref={isSelected ? suggestionContainerRef : null}
				data-index={index}
			>
				{recordType && recordType != "" && (
					<div className={`record-type ${isSelected && "selected-record"}`}>
						<p className="record-type-text">{recordType}</p>
					</div>
				)}
				{status[recordStatus] && (
					<div
						className={`record-status ${isSelected && "selected-record-status"
							}`}
					>
						<p className="record-status-text">{status[recordStatus]}</p>
					</div>
				)}
				<div className="search-text-container">
					<div className="search-info-container">
						<p
							className={`patient-name ${isSelected && "selected-name"}`}
							onMouseDown={handleMouseDownEvent}
						>
							{highlightText(name, keyword)}
						</p>
						{dob != "" && gender != "" && (
							<div className="date-container">
								<p
									className={`patient-ag ${isSelected && "selected-ag"}`}
									onMouseOver={handleMouseOver}
									onMouseOut={handleMouseOut}
								>
									{highlightText(getFormattedDate(dob), keyword)}
								</p>
								<span
									className={`pst-tooltip-container ${showToolTip && "pst-tooltiptext-hover"
										}`}
								>
									<div className="pst-tooltip-text">{`${age}/${gender}`}</div>
								</span>
							</div>
						)}
						{externalId !== "" && (
							<p
								className={`patient-id ${isSelected && "selected-id"}`}
								onMouseDown={handleMouseDownEvent}
							>
								{externalId ? highlightText(`${externalId}`, keyword) : ""}
							</p>
						)}
					</div>
					{hcpc && hcpc !== "" && (
						<div
							className="updated-on-container"
							onMouseDown={handleMouseDownEvent}
						>
							<span className="updated-on-text">HCPC </span>
							<span className="updated-on-date">
								{highlightText(hcpc, keyword)}
							</span>
						</div>
					)}
					{ndc && ndc !== "" && (
						<div
							className="updated-on-container"
							onMouseDown={handleMouseDownEvent}
						>
							<span className="updated-on-text">NDC </span>
							<span className="updated-on-date">
								{highlightText(ndc, keyword)}
							</span>
						</div>
					)}
					{manufacturer && manufacturer !== "" && (
						<div
							className="updated-on-container"
							onMouseDown={handleMouseDownEvent}
						>
							<span className="updated-on-text">Manufacturer </span>
							<span className="updated-on-date">
								{highlightText(manufacturer, keyword)}
							</span>
						</div>
					)}
					{npi !== "" && (
						<div
							className="updated-on-container"
							onMouseDown={handleMouseDownEvent}
						>
							<span className="updated-on-text">NPI </span>
							<span className="updated-on-date">
								{highlightText(parseInt(npi).toString(), keyword)}
							</span>
						</div>
					)}
					{lastUpdated !== "" && (
						<div
							className="updated-on-container"
							onMouseDown={handleMouseDownEvent}
						>
							<span className="updated-on-text">Last Updatetd </span>
							<span className="updated-on-date">
								{highlightText(getFormattedDate(lastUpdated), keyword)}
							</span>
						</div>
					)}
					{description.trim() !== "" && (
						<div
							className="address-container"
							onMouseDown={handleMouseDownEvent}
						>
							<p className={`patient-addr ${isSelected && "selected-addr"}`}>
								{highlightText(description, keyword)}
							</p>
						</div>
					)}
					<Buttons />
				</div>
			</div>
		</>
	);
};

export default SearchResult;