// SearchResult.tsx
import React, { useEffect, useState, useRef } from "react";
import "./search-results.less";
import PatientSuggestionTab from "@components/global-search/search-result";
import SpinLoader from "@components/common/spin-loader";
import NoDataFound from "@components/common/no-data-found";
import { SearchSuggestionTab } from "../../blocks/search-view/search-tab";
import { DEFAULT_TAB_HISTORY, HistoryModule, TabHistory } from "@contexts/history/provider";
import { processSearchString, getCurrentIndex, getUrl } from "@utils/search";
import { useDebouncedSearch, useKeyDownHandler, useUpdateScroll } from "@hooks/search";

interface SearchResultProps {
	clickHandler: (url: string) => void;
	blurHandler: () => void;
	keyword: string;
	tabHistory: TabHistory;
}
type TabHistoryKey = keyof TabHistory;

const SearchResult: React.FC<SearchResultProps> = ({
	clickHandler,
	blurHandler,
	keyword,
	tabHistory,
}) => {
	const [selectedSuggestion, setSelectedSuggestion] = useState<number>(0);
	const [processedString, setProcessedString] = useState<string>("");
	const [searchResult, setSearchResult] = useState<Record<string, any>>({});
	const [filterKey, setFilterKey] = useState<string>("Patient");
	const [searchLoading, setSearchLoading] = useState<boolean>(false);
	const [patientUri, setPatientUri] = useState<string>("snap");
	const scrollerRef = useRef<HTMLDivElement | null>(null);
	const highlightedRef = useRef<HTMLDivElement | null>(null);

	const selectedButtonIndex = useRef<number>(0);
	const urlMap: Record<string, string> = {
		s: "snap",
		i: "patient_insurance",
		p: "patient_prescriber",
		m: "patient_medication",
		a: "patient_allergy",
		d: "patient_diagnosis",
	};
	const typeMap: Record<string, string> = {
		Patient: "patient",
		Inventory: "inventory",
		Physician: "physician",
		Sales: "salesAccount",
	};

	const debouncedRequest = useDebouncedSearch(setSearchResult, setSearchLoading, setFilterKey);
	useEffect(() => {
		const { characterIndex, words, slug } = processSearchString(keyword, urlMap);
		selectedButtonIndex.current = characterIndex;
		setPatientUri((prev) => (slug ? slug : prev));
		setProcessedString(words);
		if (words.length > 1 && words.length !== processedString.length) {
			debouncedRequest(words);
		} else if (words.length > 1) {
			setSearchLoading(false);
		}
		else {
			setSearchResult({});
			setFilterKey("Patient");
			setSelectedSuggestion(0);
			setSearchLoading(false);
		}
	}, [keyword]);




	useUpdateScroll(highlightedRef, scrollerRef);

	const handleFilterItems = (key: string) => {
		setFilterKey(key);
		setSelectedSuggestion(0);
	};

	const keysData: TabHistory = DEFAULT_TAB_HISTORY;
	let openCloseCount: Record<string, number> = {};
	const moduleKeys = Object.keys(tabHistory.open) as HistoryModule[];
	for (const key of moduleKeys) {
		const openKeyData = Object.values(tabHistory.open[key]) as HistoryModule[];
		const closeKeyData = Object.values(tabHistory.close[key]) as HistoryModule[];
		openCloseCount[key] = openKeyData.length + closeKeyData.length;
		keysData.open[key] = [...openKeyData];
		keysData.close[key] = [...closeKeyData];
	}

	const openCloseKeyCount =
		typeMap[filterKey] && openCloseCount[typeMap[filterKey]]
			? openCloseCount[typeMap[filterKey]]
			: 0;

	const totalCount =
		(searchResult[filterKey]?.length ?? 0) + openCloseKeyCount;
	useKeyDownHandler({
		filterKey,
		searchResult,
		selectedSuggestion,
		clickHandler,
		setSelectedSuggestion,
		blurHandler,
		highlightedRef,
		patientUri,
		totalCount,
		tabHistory,
	});
	const getCurrentIndex = (
		type: string,
		moduleKey: HistoryModule,
		index: any
	) => {
		if (type === "open") {
			return searchResult[filterKey]?.length + index;
		} else {
			return (
				Object.values(tabHistory.open[moduleKey]).length +
				searchResult[filterKey]?.length +
				index
			);
		}
	};

	const handleClick = (
		id: string,
		data: {},
		buttonIndex: number | null = null
	) => {
		let uriSegment: string | null = null;
		if (buttonIndex !== null) {
			uriSegment = Object.values(urlMap)[buttonIndex];
		}
		const url = getUrl(id, data, uriSegment, filterKey, patientUri);
		if (url) {
			clickHandler(url);
		}
	};
	const OpenCloseResults = ({ text = '' }) => {
		return (keysData.open.patient.length || keysData.close.patient.length) && (
			<div className={text.length ? "suggestion-container scrollable" : ''} ref={text.length ? scrollerRef : null}>
				{["open", "close"].map((type) => {
					const moduleKey = typeMap[filterKey];
					const itemData = keysData[type][moduleKey]
						? keysData[type][moduleKey]
						: [];
					return (
						itemData.length !== 0 && (
							<React.Fragment key={`${type}-${moduleKey}`}>
								<div className="suggestion-heading-container">
									<span className="heading-text-container">
										{`${type === "open"
											? `Currently Open ${text}`
											: `Recently Accessed ${text}`
											}`}
									</span>
								</div>
								{itemData.map((item: number, index: number) => {
									return (
										<PatientSuggestionTab
											key={index}
											index={getCurrentIndex(type, moduleKey, index)}
											source={filterKey}
											item={{
												data: item.data,
												selectedButtonIndex: selectedButtonIndex.current,
											}}
											mouseDownEvent={handleClick}
											changeFocus={setSelectedSuggestion}
											isSelected={
												selectedSuggestion ===
												getCurrentIndex(type, moduleKey, index)
											}
											suggestionContainerRef={highlightedRef}
										/>
									);
								})}
							</React.Fragment>
						)
					);
				})}
			</div>
		)
	}
	return (
		<React.Fragment>
			{keyword.length > 1 ? (
				<div className="suggestion-container padding-top-0 scrollable" ref={scrollerRef}>
					<>
						<div className="suggestion-heading-container tab-bar">
							<SearchSuggestionTab
								results={searchResult}
								handleFilterItems={handleFilterItems}
								filter={filterKey}
							/>
						</div>
						{searchLoading ? (
							<SpinLoader loading={searchLoading} fontSize="2em" />
						) : searchResult[filterKey] && searchResult[filterKey]?.length ? (
							<div>
								{searchResult[filterKey].map((fsr, i) => (
									<PatientSuggestionTab
										key={`${filterKey + i}`}
										index={i}
										item={{
											data: fsr,
											selectedButtonIndex: selectedButtonIndex.current,
											keyword,
										}}
										source={filterKey}
										mouseDownEvent={handleClick}
										changeFocus={setSelectedSuggestion}
										isSelected={selectedSuggestion === i}
										suggestionContainerRef={highlightedRef}
									/>
								))}
								<OpenCloseResults />
							</div>
						) : (
							<>
								<NoDataFound text="No results found." />
								<OpenCloseResults />
							</>
						)}
					</>
				</div>
			) : (

				keyword.length < 2 && (
					<>
						{/* <NoDataFound text="Please type at least 2 characters." /> */}
						<OpenCloseResults text={'Patient'} />
					</>
				)
			)}
		</React.Fragment>
	);

};

export default React.memo(SearchResult);
