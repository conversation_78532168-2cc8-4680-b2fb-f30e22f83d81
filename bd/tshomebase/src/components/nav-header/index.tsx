import React, { useLocalStorage } from "usehooks-ts";
import LocalStore from "@enum/local-store";
import * as style from "./style.module.less";
import nav from "@public/icons/nav";
import { capitalizeFirstCharacter } from "@utils/fx";


const NavHeader = (props) => {
	const [isToggle, setIsToggle] = useLocalStorage(LocalStore.NAV_BAR_TOGGLE, false);
	const [currentNavItem] = useLocalStorage(LocalStore.CURRENT_NAV_ITEM, "");
	const [collapsed, setCollapsed] = useLocalStorage(LocalStore.NAV_BAR_COLLAPSE, false);

	return (
		<div className={style.navHeaderContainer}>
			<div onClick={() => {
				setIsToggle(!isToggle);
				setCollapsed(false);
			}}
			>
				<img src={nav.hamburgerIcon} width={20} height={20} className={style.hamburgerIcon} />
			</div>
			<p>{capitalizeFirstCharacter(currentNavItem)}</p>
		</div>
	);
};


export default NavHeader;