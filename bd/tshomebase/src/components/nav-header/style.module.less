@import (reference) '../../less/style/main.less';
@import (reference) './header.vars.less';

.navHeaderContainer {
    height: @nav-header-height;
    width: 100%;
    padding: 0px 20px;
    background-color: @nav-header-bg-color;
    color: @nav-header-text-color;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    flex-direction: row;
    justify-items: flex-start;
    align-items: center;
    display: none;
    gap: 10px;
    font-size: 18px;


    .hamburgerIcon {
        cursor: pointer;
    }

    @media (max-width: 768px) {
        display: flex;
    }
}