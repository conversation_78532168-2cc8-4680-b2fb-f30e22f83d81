import { useState } from 'react'
import "./style.less";
import { Tooltip } from '@mui/material';
import { RoutedComponentProps } from '@typedefs/routed-component';
import { staticModules } from '@modules/const';
import { UIComponentProps } from '@utils/interfaces';
import InitialsAvatar from '@components/common/initials-avatar';
import { VARIANT_SECONDARY } from '@utils/const';

type UserAvatarProps = Partial<RoutedComponentProps> & UIComponentProps & {
};

const UserAvatar = (props: UserAvatarProps) => {
    const { className, setActiveTab } = props;
    const [imageExists, setImageExists] = useState(false);
    const userName = [window.App.user.firstname, window.App.user.lastname].filter(Boolean).join(" ")

    return (
        <div className={`user-info ${className || ""}`} onClick={() => {
            setActiveTab?.(staticModules[0]?.key);
        }}
        >
            <div className="user-info-inner" >
                <Tooltip
                    classes={{ tooltip: "user-tooltip" }}
                    title={userName}
                >
                    <div className="user-avatar" >
                        {
                            imageExists &&
                            <img src="/api/my/photo" alt="" onError={() => setImageExists(false)} />
                            ||
                            <InitialsAvatar name={userName} variant={VARIANT_SECONDARY} className="user-avatar-right" />
                        }
                    </div>
                </Tooltip>
            </div>
        </div>
    )
}

export default UserAvatar