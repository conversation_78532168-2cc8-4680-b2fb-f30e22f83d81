@import '../../less/style/main.less';


.user-info {
    min-height: fit-content;
    position: absolute;
    top: var(--top-right-icons-tmp1-pt);
    right: 20px;

    .user-info-inner {
        cursor: pointer;
        display: flex;
        flex-direction: row;

        gap: 10px;
        min-height: 28px;
        align-items: center;


        .user-avatar {
            border-radius: @border-radius-full;
            background-position: center;
            background-size: cover;

            img {
                width: 100%;
                height: 100%;
                border-radius: @border-radius-full;
            }

            p {
                margin: 0;
            }

            .user-avatar-right {
                width: 40px;
                height: 40px;
            }
        }

    }



    @media (max-width: 768px) {
        .user-info-inner {
            justify-content: space-between;
            align-items: center;
            gap: 5px;

        }
    }

}

&.nav-collapsed {
    .user-info {
        padding-left: 18px;
    }
}

.user-tooltip {
    font-size: 14px !important;
    margin-top: 6px !important;
}

.user-tooltip {

    @media (max-width: 768px) {
        font-size: 10px !important;
        font-weight: bold;
    }
}