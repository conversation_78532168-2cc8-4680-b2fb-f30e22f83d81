import React from "react";
import "./dynamic-status-cell.less";

interface DynamicStatusCellProps {
	value: string | number;
	statusMap: Record<string, "red" | "green" | "grey">;
	containerClassName?: string;
}

export const DynamicStatusCell: React.FC<DynamicStatusCellProps> = ({ value, statusMap, containerClassName = "" }) => {
	if (!value) {
		return null;
	}
	const statusClass = statusMap[value] || "default";
	if (!statusClass) {
		return null;
	}
	return (
		<div className={`status-cell ${statusClass} ${containerClassName}`}>
			<p>{value}</p>
		</div>
	);
};
