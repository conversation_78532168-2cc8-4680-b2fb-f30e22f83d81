.status-cell {
	display: flex;
	align-items: center;
	justify-content: center;
	max-width: fit-content !important;

	height: 22px;
	padding-top: 2px;
	padding-right: 8px;
	padding-bottom: 2px;
	padding-left: 8px;
	border-radius: 16px;

	p {
		font-family: Inter;
		font-weight: 700;
		font-size: 12px;
		line-height: 18px;
		letter-spacing: 0;
		text-align: center;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}

	&.default {
		border: none;
		background-color: transparent;
		p {
			font-weight: unset;
		}
	}

	&.green {
		background-color: transparent;
		border: 2px solid #689989;
		p {
			color: #689989;
		}
	}

	&.red {
		background-color: #d98080;
		border: 2px solid #d98080;
		p {
			color: white;
		}
	}

	&.grey {
		background-color: #949491;
		border: 2px solid #949491;
		p {
			color: white;
		}
	}
}
