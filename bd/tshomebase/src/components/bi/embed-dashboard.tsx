import { memo, useEffect, useState } from "react";
import "./embed-dashboard.less";
import { request } from "@core/request";
import { embedDashboard } from "@superset-ui/embedded-sdk";
import SpinLoader from "@components/common/spin-loader";
import { UIComponentProps } from "@utils/interfaces";
import axios from "axios";
import { ClaraError } from "@utils/fx";
import { CRErrorBoundary } from "@blocks/error-boundary/cr-error-boundary";

interface EmbedDashboardPropTypes extends UIComponentProps {
	isLoading?: boolean;
	slug: string;
	dashboardUiConfig?: {
		hideTitle: boolean;
		hideTab: boolean;
		hideChartControls: boolean;
	};
}

const EmbedDashboard = (props: EmbedDashboardPropTypes) => {
	const {
		slug,
		className,
		style,
		dashboardUiConfig = {
			hideTitle: true,
			hideTab: false,
			hideChartControls: false,
		},
	} = props;

	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		fetchDataEmbedDashboard();
	}, []);

	const fetchGuestToken = async (dashboardId: string) => {
		try {
			const resp = await request({
				url: `/api/bi/auth?dashboardId=${dashboardId}`,
			});
			if (resp.data?.length) {
				return resp.data;
			} else {
				setError("BI Guest token not found");
				return resp;
			}
		} catch (error: any) {
			setError(error);
			throw error;
		}
	};

	const fetchDataEmbedDashboard = async () => {
		try {
			setIsLoading(true);
			const biRes = await request({
				url: `/api/bi?slug=${slug}`,
			})
				.then((resp) => {
					if (Object.keys(resp.data)?.length) {
						return resp.data;
					} else {
						setError("Failed to get BI dashboard data.");
						return resp;
					}
				})
				.catch((e) => {
					throw e;
				});

			const { supersetUrl, dashboardId } = biRes;

			const missingFields = [];
			if (!supersetUrl) missingFields.push("supersetUrl");
			if (!dashboardId) missingFields.push("dashboardId");

			if (missingFields.length > 0) {
				const missingFieldsStr = missingFields.join(", ");
				throw new ClaraError(`BI dashboard details fetch failed: Missing required fields: ${missingFieldsStr}`);
			}

			embedDashboard({
				id: dashboardId,
				supersetDomain: supersetUrl,
				mountPoint: document.getElementById(`bi-dashboard-${slug}`),
				fetchGuestToken: () => fetchGuestToken(dashboardId),
				dashboardUiConfig: { ...dashboardUiConfig },
			});
		} catch (error: any) {
			console.error("error", error);
			if (error instanceof ClaraError) {
				setError(error?.message || "Something went wrong. Please try again later.");
			} else if (axios.isAxiosError(error.data)) {
				setError(
					error?.data?.response?.message ||
						error?.data?.response?.data.message ||
						"Server Error: " + (error.data.message || "Something went wrong on the server")
				);
			} else {
				setError("An unexpected error occurred. Please try again later.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<CRErrorBoundary fallbackMessage={error || ""}>
			<div style={style} className={`embed-dashboard-wrapper${className ? " " + className : ""}`}>
				{isLoading && <SpinLoader className="spinner-at-embed-dashboard" loading={isLoading} />}
				<div style={{}} id={`bi-dashboard-${slug}`} className="superset-embed-dashboard" />
			</div>
		</CRErrorBoundary>
	);
};

export default memo(EmbedDashboard);
