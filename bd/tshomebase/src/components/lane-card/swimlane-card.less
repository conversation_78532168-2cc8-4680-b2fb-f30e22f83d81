@import (reference) '../../less/style/main.less';

.card-main-container {
  display: flex;
  flex-direction: column;
  clear: both;
  margin-top: 8px;
  background-color: white;
  border-radius: 4px;
  padding: 0px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
  cursor: context-menu;
  min-height: fit-content !important;

  @media (max-width: 768px) {
    width: 100%;
  }

  &:hover {
    border-color: #0090c9;
  }

  .card-header {
    margin-bottom: 8px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    background-color: transparent;
    padding: 10px;
    font-weight: bold;

    .header-left {
      float: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: fit-content;
      margin-right: 50px;
      font-size: 18px;
      color: @purple;
      background-color: transparent;
      border-radius: 20px;
    }

    .header-right {
      float: right;
      font-size: 14px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      color: @dark-gray;
    }
  }

  .card-body {
    padding: 10px;
    display: flex;
    flex-direction: column;

    .body-content {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      p {
        float: left;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #58505b;
      }
    }
  }

  .card-footer {
    position: relative;
    padding: 10px;
    border-top: 1px solid #ddd;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    color: #58505b;
    font-size: 14px;
    line-height: 18px;
    align-items: center;

    .footer-left {
      float: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: bold;
      width: 50%;
      margin-right: 50px;
    }

    .footer-right {
      float: right;
      background: #fff;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      gap: 10px;

      >div {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center
      }

      .high-priority {
        .priority-badge;
      }
    }
  }
}