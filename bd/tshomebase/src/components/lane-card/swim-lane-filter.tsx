import "./swim-lane-filter.less";
import { removeFalsyValues } from "@utils/dsl-fx";
import React, { useEffect, useState } from "react";
import { StylesConfig } from "react-select";
import { FieldSelect } from "../../dsl/fields/field-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { FieldDatePick } from "@dsl/fields/field-picker";

type SwimLaneFiltersProps = {
	onChange: (filters: Record<string, unknown>) => void;
	onRefresh: () => void;
	onModeToggle: () => void;
};
export const SwimLaneFilters = (props: SwimLaneFiltersProps) => {
	const { onModeToggle, onRefresh, onChange } = props;
	const [filters, setFilters] = useState<Record<string, unknown>>({});

	useEffect(() => {
		onChange(removeFalsyValues(filters));
	}, [filters]);

	const overStyle: StylesConfig = {
		...SelectConfig.dsl.style,
		valueContainer: (provided, state) => ({
			...provided,
			maxWidth: "170px",
			fontWeight: "bold",
		}),
	};

	return (
		<div className="swimlane-filters">
			<div className="swimlane-select">
				<div className="swimlane-label">Assigned Team</div>
				<FieldSelect
					form="wf_queue_team"
					defaultValueSource={filters.__assigned_team || ""}
					defaultValueAutoName={filters.__assigned_team_auto_name || ""}
					multi={false}
					disabled={false}
					onChange={(val, an) => {
						setFilters({ ...filters, __assigned_team: val, __assigned_team_auto_name: an });
					}}
					customStyles={overStyle}
					theme={SelectConfig.dsl.theme}
					placeholder="Assigned Team"
				/>
			</div>
			<div className="swimlane-select">
				<div className="swimlane-label">Assigned User</div>
				<FieldSelect
					form="user"
					defaultValueSource={filters.__assigned_user || ""}
					defaultValueAutoName={filters.__assigned_user_auto_name || ""}
					multi={false}
					disabled={false}
					onChange={(val, an) => {
						setFilters({ ...filters, __assigned_user: val, __assigned_user_auto_name: an });
					}}
					customStyles={overStyle}
					theme={SelectConfig.dsl.theme}
					placeholder="Assigned User"
				/>
			</div>
			<div className="swimlane-select">
				<div className="swimlane-label">Patient</div>
				<FieldSelect
					form="patient"
					defaultValueSource={filters.patient_id || ""}
					defaultValueAutoName={filters.patient_id_auto_name || ""}
					multi={false}
					disabled={false}
					onChange={(val, an) => {
						setFilters({ ...filters, patient_id: val, patient_id_auto_name: an });
					}}
					customStyles={overStyle}
					theme={SelectConfig.dsl.theme}
					placeholder="Assigned User"
				/>
			</div>
			<div className="swimlane-select">
				<div className="swimlane-label">Priority</div>
				<FieldSelect
					form="none"
					defaultValueSource={filters.__priority || ""}
					defaultValueAutoName={filters.__priority_auto_name || ""}
					multi={false}
					disabled={false}
					options={[{ value: "5", label: "High" }]}
					onChange={(val, an) => {
						console.log(val);
						setFilters({ ...filters, __priority: val, __priority_auto_name: an });
					}}
					customStyles={overStyle}
					theme={SelectConfig.dsl.theme}
					placeholder="Priority"
				/>
			</div>
			<div className="swimlane-date">
				<div className="swimlane-label">Due Date</div>
				<FieldDatePick
					defaultValue={filters.__due_date || ""}
					onChange={(val) => {
						if (val == "Invalid date") {
							val = null;
						}
						setFilters({ ...filters, __due_date: val });
					}}
				/>
			</div>
			<div className="filter-act-btn" onClick={onRefresh}>
				<div className="add-refresh">↻</div>
			</div>
			<div className="filter-act-btn" onClick={onModeToggle}>
				<i style={{ fontSize: "22px" }} className="fa fa-table" aria-hidden="true" />
			</div>
		</div>
	);
};
