@import (reference) '../../less/style/main.less';

.swimlane-filters {

    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;
    flex-wrap: wrap;
    gap: 10px;

    .filter-act-btn {
        display: flex;
        user-select: none;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        background: #f8f5fc;
        width: 35px;
        height: 35px;
        cursor: pointer;
        border-radius: @border-radius-full;

        .add-refresh {
            font-size: 24px;
            font-weight: 600;
            cursor: pointer;
        }
    }

    .swimlane-label {
        font-size: 14px;
        color: gray;
        font-weight: bold;
        line-height: 18px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .form-label-one;
    }

    .swimlane-select {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        min-width: fit-content;
        gap: 10px;
        flex: 1 1 16%;

        .field-select {
            >div:first-child {
                .form-input-field;
                padding: 2px 5px;
                height: 36px;
            }
        }
    }

    .swimlane-date {
        .swimlane-select;

        .field-date-picker {
            .form-input-field;
            width: 100%;
            height: 36px;
            padding: 4px;
        }
    }

}