import "./swim-lane-card.less";
import Card from "./swimlane-card";
import MenuItem from "@mui/material/MenuItem";
import list from "@public/icons/list";
import SwimLanePopUpBox from "../popups/swim-lane";
import type { DropR<PERSON>ult, OnDragStartResponder } from "react-beautiful-dnd";
import { Click<PERSON>wayListener, Pop<PERSON>, Tooltip } from "@mui/material";
import React, { useState, useEffect, useRef } from "react";
import { DragDropContext, Draggable } from "react-beautiful-dnd";
import { StrictModeDroppable } from "@blocks/dnd/strict-mode-droppable";
import { CardStruct, WorkFlowData } from "@modules/workflow/types";
import { fetchQueueData, QueueSwimlaneSchema, WFQueueResponse } from "@modules/queue/helper";
import { IFilterOpts } from "@utils/dsl-fx";
import _ from "lodash";
import { useNavigation } from "@core/navigation";
import { RoutedComponentProps } from "@typedefs/routed-component";
import { SwimLaneFilters } from "@components/lane-card/swim-lane-filter";

interface SwimlanesStore {
	[key: string]: WFQueueResponse[];
}

interface QueueSwimlaneViewerProps extends RoutedComponentProps {
	swimlanes: QueueSwimlaneSchema[];
	setMode: (mode: "card" | "list") => void;
	wf: WorkFlowData;
}

interface SortingProperties {
	key: string;
	label: string;
	sortOrder: "asc" | "desc";
}

export const QueueSwimlaneViewer: React.FC<QueueSwimlaneViewerProps> = (props) => {
	const { swimlanes, wf, setMode } = props;
	const { code: wfCode } = wf;

	useNavigation(props, `/swimlane/`);

	// Popover
	const [isOpen, setIsOpen] = useState<boolean>(false);
	const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
	const [sortingFor, setSortingFor] = useState<string | null>(null);

	const searchFilter = useRef<Record<string, unknown>>({});

	const [swimlanesData, setSwimlaneData] = useState<SwimlanesStore>({});

	const [sortSwimlanesConfig, setSortSwimLanesConfig] = useState<{
		[key: string]: {
			[key: string]: "asc" | "desc";
		};
	}>({});
	const [draggedItem, setDraggedItem] = useState<WFQueueResponse | null>(null);

	const open = Boolean(anchorEl);
	const id = open ? "simple-popover" : undefined;

	const filters: SortingProperties[] = [
		{ key: "__due_date", label: "Due Date", sortOrder: "asc" },
		{ key: "auto_name", label: "Name", sortOrder: "asc" },
	];

	useEffect(() => {
		fetchCards();
	}, []);

	const handleSortClick = (event: React.MouseEvent<HTMLElement>, item: QueueSwimlaneSchema) => {
		setAnchorEl(event.currentTarget);
		setSortingFor(item.id);
	};

	const handleSortClose = () => {
		setSortingFor(null);
		setAnchorEl(null);
	};

	const handleOnCloseModal = () => {
		setIsOpen(!isOpen);
	};

	const updateSwimLaneCard = async (result: DropResult) => {
		if (result?.destination?.droppableId !== result?.source.droppableId) {
			setIsOpen(!isOpen);
		}
		const draggedSwimLane = [];
		const destSwimCol = swimlanes.find((obj) => obj.id === result?.destination?.droppableId);
		const sourceSwimCol = swimlanes.find((obj) => obj.id === result?.source?.droppableId);
		if (destSwimCol && sourceSwimCol) {
			draggedSwimLane.push(destSwimCol, sourceSwimCol);
			fetchCards(draggedSwimLane);
		}
	};

	const onSortClick = (sorting: SortingProperties) => {
		if (!sortingFor) return;
		setSortSwimLanesConfig((prev) => ({
			...prev,
			[sortingFor]: {
				[sorting.key]: sorting.sortOrder,
			},
		}));
		const sl = swimlanes.find((item) => item.id === sortingFor);
		if (sl) {
			fetchCards([sl]);
		}
	};

	const handleDragStart: OnDragStartResponder = (result) => {
		const { draggableId, source } = result;
		const item = swimlanesData[source?.droppableId]?.find(
			(singleItem) => singleItem?.__wf_queue_node_id.toString() === draggableId
		);
		if (item) {
			setDraggedItem(item);
		}
	};

	const onDragEnd = (result: DropResult) => {
		if (!result.destination) return;
		const { source, destination } = result;
		if (source.droppableId === destination.droppableId) {
			const column = swimlanesData[source.droppableId];
			const copiedItems = [...column];
			const [removed] = copiedItems.splice(source.index, 1);
			copiedItems.splice(destination.index, 0, removed);
			setSwimlaneData((prevColumns) => ({
				...prevColumns,
				[source.droppableId]: copiedItems,
			}));
		} else {
			const sourceColumn = swimlanesData[source.droppableId];
			const destColumn = swimlanesData[destination.droppableId];
			const sourceItems = [...sourceColumn];
			const destItems = [...destColumn];
			const [removed] = sourceItems.splice(source.index, 1);
			destItems.splice(destination.index, 0, removed);
			setSwimlaneData((prevColumns) => ({
				...prevColumns,
				[source.droppableId]: sourceItems,
				[destination.droppableId]: destItems,
			}));
		}
		updateSwimLaneCard(result);
	};

	const fetchCards = (slist?: QueueSwimlaneSchema[]) => {
		const filterOpts: IFilterOpts = {
			filter: {},
		};
		filterOpts.filter = { ...searchFilter.current };
		let fetchFor = slist ? slist : swimlanes;
		for (const item of fetchFor) {
			filterOpts.sortProperty = "";
			filterOpts.sortDirection = "asc";
			if (sortSwimlanesConfig[item.id]) {
				Object.keys(sortSwimlanesConfig[item.id]).forEach((key) => {
					filterOpts.sortProperty = key;
					filterOpts.sortDirection = sortSwimlanesConfig[item.id][key];
				});
			}
			fetchQueueData(item?.form, wfCode, item?.node_code, filterOpts)
				.then((res) => {
					setSwimlaneData((prevColumns) => ({
						...prevColumns,
						[item.id]: res.data,
					}));
				})
				.catch((error) => {
					console.error(error);
				});
		}
	};

	const SortPopover = () => (
		<Popper
			disablePortal={false}
			anchorEl={anchorEl}
			placement="bottom"
			className="popover_class"
			open={open}
			modifiers={[
				{
					name: "flip",
					enabled: false,
					options: {
						altBoundary: true,
						rootBoundary: "viewport",
						padding: 8,
					},
				},
				{
					name: "preventOverflow",
					enabled: true,
					options: {
						altAxis: true,
						altBoundary: true,
						tether: false,
						rootBoundary: "viewport",
						padding: 8,
					},
				},
			]}
		>
			<ClickAwayListener onClickAway={handleSortClose}>
				<div className="filter-card">
					{filters.map((filter) => {
						let csd = "";
						if (
							sortingFor &&
							sortSwimlanesConfig[sortingFor] &&
							sortSwimlanesConfig[sortingFor][filter.key]
						) {
							csd = sortSwimlanesConfig[sortingFor][filter.key];
						}
						const sto = csd == "" ? filter.sortOrder : csd == "asc" ? "desc" : "asc";
						return (
							<MenuItem
								onClick={() => onSortClick({ ...filter, sortOrder: sto })}
								key={filter.key + sto}
								className="filter-option"
								value={filter.key}
							>
								<span className="menu-option">{filter?.label}</span>
								{csd && csd === "asc" && <i className="fa fa-caret-up" />}
								{csd && csd == "desc" && <i className="fa fa-caret-down" />}
							</MenuItem>
						);
					})}
				</div>
			</ClickAwayListener>
		</Popper>
	);

	return (
		<>
			<div>
				<div className="main-container">
					<div className="top-filter-container">
						<SwimLaneFilters
							onChange={(flt) => {
								searchFilter.current = flt;
								fetchCards();
							}}
							onModeToggle={() => setMode("list")}
							onRefresh={() => fetchCards()}
						/>
					</div>
					<div className="swim-lane-container">
						<DragDropContext onDragStart={handleDragStart} onDragEnd={(result) => onDragEnd(result)}>
							{swimlanes?.map((item) => (
								<div className="swim-lane-column-container" key={item.id}>
									<div className="top-container">
										<div className="title-container">
											<Tooltip
												title={
													item?.node_data?.description ? (
														<span style={{ fontSize: "14px" }}>
															{item?.node_data?.description}
														</span>
													) : (
														""
													)
												}
												arrow
												placement="top"
											>
												<i className="fa fa-info-circle" aria-hidden="true" />
											</Tooltip>
											<h5>{item.node_label}</h5>
										</div>
										<div className="filter-container">
											<img className="filter" src={list.filter} />
											{swimlanesData[item?.id]?.length ? (
												<i
													aria-describedby={id}
													onClick={(event) => handleSortClick(event, item)}
													className="fa fa-sort pull-right sort"
												/>
											) : null}
										</div>
									</div>
									<div className="card-lane">
										<StrictModeDroppable droppableId={item?.id} key={item.id}>
											{(provided) => (
												<div
													{...provided.droppableProps}
													ref={provided.innerRef}
													className="card-inner-lane"
												>
													{swimlanesData[item.id]?.map((singleItem, index) => (
														<Draggable
															draggableId={singleItem?.__wf_queue_node_id.toString()}
															index={index}
															key={singleItem?.__wf_queue_node_id.toString()}
														>
															{(provided) => (
																<div
																	{...provided.dragHandleProps}
																	{...provided.draggableProps}
																	ref={provided.innerRef}
																>
																	<Card
																		card_struct={item?.card_struct as CardStruct}
																		item={singleItem}
																		index={index}
																		show={false}
																		length={swimlanesData[item.id]?.length}
																		InspectView={item?.InspectElement}
																		{...props}
																		fetchCards={() => fetchCards()}
																	/>
																</div>
															)}
														</Draggable>
													))}
													{provided.placeholder}
												</div>
											)}
										</StrictModeDroppable>
									</div>
								</div>
							))}
						</DragDropContext>
					</div>
				</div>
				<SortPopover />
				{draggedItem && (
					<SwimLanePopUpBox dragItem={draggedItem} open={isOpen} onClose={() => handleOnCloseModal()} />
				)}
			</div>
		</>
	);
};
