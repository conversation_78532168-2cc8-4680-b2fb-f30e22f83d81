import "./swimlane.less";
import { SwimlaneCard } from "./swimlane-card";
import list from "@public/icons/list";
import { ClickAwayListener, Menu, MenuItem, Pop<PERSON>, Tooltip } from "@mui/material";
import React, { useState, useEffect, useRef } from "react";
import { CardStruct, WorkFlowData } from "@modules/workflow/types";
import { fetchQueueData, QueueSwimlaneSchema, WFQueueResponse } from "@modules/queue/helper";
import { IFilterOpts } from "@utils/dsl-fx";
import _, { set } from "lodash";
import { useNavigation } from "@core/navigation";
import { RoutedComponentProps } from "@typedefs/routed-component";
import { SwimLaneFilters } from "@components/lane-card/swim-lane-filter";
import { useOnScreen } from "@hooks/on-screen";

interface SwimlanesStore {
	[key: string]: WFQueueResponse[];
}

interface QueueSwimlaneViewerProps extends RoutedComponentProps {
	swimlanes: QueueSwimlaneSchema[];
	setMode: (mode: "card" | "list") => void;
	wf: WorkFlowData;
}

interface SortingProperties {
	key: string;
	label: string;
	sortOrder: "asc" | "desc";
}

const filters: SortingProperties[] = [
	{ key: "__due_date", label: "Due Date", sortOrder: "asc" },
	{ key: "auto_name", label: "Name", sortOrder: "asc" },
];

export const QueueSwimlaneViewer: React.FC<QueueSwimlaneViewerProps> = React.memo(
	(props) => {
		// useNavigation(props, `/swimlane/`);
		const { swimlanes, wf, setMode } = props;
		const { code: wfCode } = wf;
		const [swimlanesData, setSwimlaneData] = useState<SwimlanesStore>({});

		const [isOnScreen, elementRef] = useOnScreen(true);

		const searchFilter = useRef<Record<string, unknown>>({});

		// purely for sorting
		const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
		const [sortingFor, setSortingFor] = useState<string | null>(null);

		const handleSortClick = (event: React.MouseEvent<HTMLElement>, item: QueueSwimlaneSchema) => {
			setAnchorEl(event.currentTarget);
			setSortingFor(item.id);
		};

		const [sortSwimlanesConfig, setSortSwimLanesConfig] = useState<{
			[key: string]: {
				[key: string]: "asc" | "desc";
			};
		}>({});

		useEffect(() => {
			fetchCards();
			const intervalId = setInterval(() => {
				if (isOnScreen) {
					fetchCards();
				}
			}, 10000);
			() => {
				clearInterval(intervalId);
			};
		}, []);

		const SortPopover = () => {
			const onSortClick = (sorting: SortingProperties) => {
				if (!sortingFor) return;
				setSortSwimLanesConfig((prev) => ({
					...prev,
					[sortingFor]: {
						[sorting.key]: sorting.sortOrder,
					},
				}));
				const sl = swimlanes.find((item) => item.id === sortingFor);
				if (sl) {
					fetchCards([sl]);
				}
			};

			return (
				<Popper
					disablePortal={false}
					anchorEl={anchorEl}
					placement="bottom"
					className="popover_class"
					open={Boolean(anchorEl)}
					modifiers={[
						{
							name: "flip",
							enabled: false,
							options: {
								altBoundary: true,
								rootBoundary: "viewport",
								padding: 8,
							},
						},
						{
							name: "preventOverflow",
							enabled: true,
							options: {
								altAxis: true,
								altBoundary: true,
								tether: false,
								rootBoundary: "viewport",
								padding: 8,
							},
						},
					]}
				>
					<ClickAwayListener
						onClickAway={() => {
							setAnchorEl(null);
						}}
					>
						<div className="filter-card">
							{filters.map((filter) => {
								let csd = "";
								if (
									sortingFor &&
									sortSwimlanesConfig[sortingFor] &&
									sortSwimlanesConfig[sortingFor][filter.key]
								) {
									csd = sortSwimlanesConfig[sortingFor][filter.key];
								}
								const sto = csd == "" ? filter.sortOrder : csd == "asc" ? "desc" : "asc";
								return (
									<MenuItem
										onClick={() => onSortClick({ ...filter, sortOrder: sto })}
										key={filter.key + sto}
										className="filter-option"
										value={filter.key}
									>
										<span className="menu-option">{filter?.label}</span>
										{csd && csd === "asc" && <i className="fa fa-caret-up" />}
										{csd && csd == "desc" && <i className="fa fa-caret-down" />}
									</MenuItem>
								);
							})}
						</div>
					</ClickAwayListener>
				</Popper>
			);
		};

		const fetchCards = async (slist?: QueueSwimlaneSchema[]) => {
			const filterOpts: IFilterOpts = {
				filter: {},
			};
			filterOpts.filter = { ...searchFilter.current };
			let fetchFor = slist ? slist : swimlanes;
			for (const item of fetchFor) {
				filterOpts.sortProperty = "";
				filterOpts.sortDirection = "asc";
				if (sortSwimlanesConfig[item.id]) {
					Object.keys(sortSwimlanesConfig[item.id]).forEach((key) => {
						filterOpts.sortProperty = key;
						filterOpts.sortDirection = sortSwimlanesConfig[item.id][key];
					});
				}
				fetchQueueData(item?.form, wfCode, item?.node_code, filterOpts)
					.then((res) => {
						if (res.success) {
							setSwimlaneData((prevColumns) => ({
								...prevColumns,
								[item.id]: res.data,
							}));
						}
					})
					.catch((err) => {
						console.error(err);
					});
			}
		};

		return (
			<>
				<div className="main-container" ref={elementRef}>
					<div className="top-filter-container">
						<SwimLaneFilters
							onChange={(flt) => {
								searchFilter.current = flt;
								fetchCards();
							}}
							onModeToggle={() => setMode("list")}
							onRefresh={() => fetchCards()}
						/>
					</div>
					<div className="swim-lane-container">
						{swimlanes?.map((item) => (
							<div className="swim-lane-column-container" key={item.id}>
								<div className="top-container">
									<div className="title-container">
										<h5>{item.node_label}</h5>
										<Tooltip
											title={
												item?.node_data?.description ? (
													<span style={{ fontSize: "14px" }}>
														{item?.node_data?.description}
													</span>
												) : (
													""
												)
											}
											arrow
											placement="top"
										>
											<i className="fa fa-info-circle" aria-hidden="true" />
										</Tooltip>
									</div>
									<div className="filter-container">
										{swimlanesData[item?.id]?.length ? (
											<i
												onClick={(event) => handleSortClick(event, item)}
												className="fa fa-sort pull-right sort"
											/>
										) : null}
									</div>
								</div>
								<div className="card-lane">
									{swimlanesData[item.id]?.map((data) => (
										<SwimlaneCard
											key={data.__wf_queue_node_id}
											card_struct={item?.card_struct as CardStruct}
											item={data}
											show={false}
											InspectView={item?.InspectElement}
											refreshSwimlane={() => {
												fetchCards([item]);
											}}
										/>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
				<SortPopover />
			</>
		);
	},
	(prevProps, nextProps) => {
		return prevProps.swimlanes == nextProps.swimlanes;
	}
);
