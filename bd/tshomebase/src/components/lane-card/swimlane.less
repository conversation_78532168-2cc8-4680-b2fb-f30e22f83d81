@import (reference) "../../less/style/main.less";

@filter-action-btn-color: #4d4e8d;

.main-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;

  .top-filter-container {
    position: sticky;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px;
    background-color: @white;
    border-bottom: 1px solid #cdcdcd;
    min-height: fit-content;
    .card-one;

    .toggle-button-container {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #aeaeae;
      cursor: pointer;
    }
  }

  .swim-lane-container {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: auto;
    overflow-x: auto;
    overflow-y: scroll;
    flex-grow: 1;

    @media (max-width: 768px) {
      flex-direction: column;
      overflow-y: scroll;
      min-height: max-content;
    }


    .swimlane-description {
      font-size: 13px;
      color: white;
    }
  }

  .swim-lane-column-container {
    display: flex;
    flex-direction: column;
    align-items: "center";
    margin-top: 10px;
    position: relative;
    min-width: 400px;
    max-width: 400px;
    width: 400px;

    @media (max-width: 768px) {
      min-width: 100%;
      max-width: 100%;
      min-height: 200px;
      flex-grow: 1;
    }

    .top-container {
      padding: 15px 10px 5px 10px;
      display: flex;
      flex-direction: row;
      min-height: fit-content;
      // background-color: #f3f3f3;
      justify-content: space-between;
      border-top-right-radius: 8px;
      border-top-left-radius: 8px;
      // box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);

      .sort {
        cursor: pointer;
      }

      i {
        font-size: 19px;
        cursor: pointer;
      }

      .title-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        i {
          display: none;
        }

        &:hover {
          i {
            display: block;
          }
        }
      }

      h5 {
        margin-left: 10px;
        text-overflow: ellipsis;
        display: inline-block;
        margin: 0;
        font-size: 24px;
        cursor: default;
        font-weight: bold;
        padding-right: 10px;
        color: #2e2e2e;
      }

      .filter-container {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          cursor: pointer;
        }

        i {
          font-size: 20px;
          cursor: pointer;
        }
      }
    }

    .card-lane {
      // background-color: #f3f3f3;
      padding: 0px 10px 15px 10px;
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
      margin-bottom: 10px;

      // box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
      height: auto !important;
      overflow-y: scroll;
      display: flex;
      flex-direction: column;

      @media (max-width: 768px) {
        width: 100%;
        max-height: 70vh;
      }
    }
  }

  .dsl-swim-lane-view {
    min-height: 50px !important;
    text-align: center;
    border-radius: 8px !important;
    padding: 0 20px;

    .dsl-find-view {
      .findbar {
        .findtab label {
          margin-left: 0px !important;
        }
      }
    }

    .find-action-btn {
      .action {
        display: none !important;
      }
    }
  }

  @media (max-width: 768px) {
    .top-filter-container {
      flex-direction: column-reverse;

      .toggle-button-container {
        justify-content: end;
      }
    }
  }
}

.patient-details-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  width: 300px;
  border: 1px solid #ccc;
  border-radius: 5px;

  .modal-content-container {
    position: relative;

    .modal-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      background-color: #eee;
      padding: 5px;
    }

    .modal-body {
      padding: 5px;
    }
  }

  .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 8px;
    cursor: pointer;
  }
}

.popover-parent-container {
  max-width: 250px;
  background-color: white;
  padding-bottom: 5px;
  border-radius: 4px;
  margin-left: 0px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.3);
  position: absolute;
  z-index: 9999;

  .popover-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 4px 8px 4px 8px;
    background-color: #668195;
    font-weight: 500;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    color: white;
    border-bottom: 1px solid lightgray;

    i {
      font-size: 20px;
      cursor: pointer;
    }
  }

  .poper-main-content {
    width: 100%;

    .wf-inspect-view-container {
      min-width: 200px;
      flex-direction: column;
      padding: 10px 5px;

      .inspect-ph {
        padding: 0px;
        margin-bottom: 10px;
        border-bottom: 1px solid #f1f1f1;
      }

      .inspect-cfg {
        border-left: none;
        padding: 0px;
        width: 100%;
        display: flex;
      }
    }
  }

  .status-container {
    background-color: #eee;
    font-weight: 500;
    width: 100%;
    padding: 4px 8px 4px 8px;
    font-weight: 900;
  }

  .popover-body {
    width: 100%;
    display: flex;
    padding: 4px 8px 4px 8px;
    background-color: #eee;
    font-weight: 500;
    z-index: 30px;

    .arrow-div>.arrow:after {
      content: " ";
      left: 1px;
      bottom: -10px;
      border-left-width: 0;
      border-right-color: #fff;
    }

    background-color: white;

    h5 {
      color: black;
      font-size: 14px;
      font-weight: 700;

      span {
        color: #808080;
        font-weight: 700;
        font-size: 14px;
      }
    }
  }

  .popover-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 8px 4px 8px;
    background-color: #eee;
    font-weight: 500;
    background-color: white;

    .reassign {
      padding: 5px 14px;
      background-image: none;
      box-shadow: none;
      -webkit-box-shadow: none;
      text-shadow: none;
      background-color: #0090c9;
      color: white;
      width: 100%;
    }
  }
}

.box.arrow-left:after {
  content: " ";
  position: absolute;
  left: -15px;
  top: 15px;
  border-top: 15px solid transparent;
  border-right: 15px solid red;
  border-left: none;
  border-bottom: 15px solid transparent;
}

.filter-card {
  width: 160px;
  padding: 6px 20px 5px;
  box-shadow: none;
  background-color: white;

  .filter-option {
    color: #333;
    font-size: 14px;
    display: flex;
    font-weight: normal;
    line-height: 1.42857143;
    justify-content: space-between;
  }
}

.circular-progress {
  width: 100%;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
}