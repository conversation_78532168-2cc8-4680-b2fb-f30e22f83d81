import "./swimlane-card.less";
import { SwimlaneCardPopoverPopper } from "./swimlane-card-popover";
import type { FC } from "react";
import React, { useRef } from "react";
import { Tooltip } from "@mui/material";
import { PriorityDesign, WFQueueResponse } from "@modules/queue/helper";

interface SwimlaneCardProps {
	item: WFQueueResponse;
	card_struct: CardStruct;
	show: boolean;
	InspectView: FC<any>;
	refreshSwimlane: () => void;
}

interface InspectPopoverProps {}

interface CardStruct {
	header: string[];
	footer: string[];
	lines: string[][];
}

const priorityBaseHeaderStyle: PriorityDesign = {
	1: {
		backgroundColor: "#EEEDEE80",
		color: "#FFFFFF",
	},
	2: {
		backgroundColor: "#EEEDEE",
		color: "#FFFFFF",
	},
	3: {
		backgroundColor: "#58505B40",
		color: "#FFFFFF",
	},
	4: {
		backgroundColor: "#B97E4CB2",
		color: "#FFFFFF",
	},
	5: {
		backgroundColor: "#F56D63",
		color: "#FFFFFF",
	},
};

const convertTimeStampToDate = (timeStamp: string) => {
	const date = new Date(timeStamp);
	const month = (date.getMonth() + 1).toString();
	const day = date.getDate().toString().padStart(2, "0");
	const year = date.getFullYear();
	const formattedDate = `${month}/${day}/${year}`;
	return formattedDate;
};

export const SwimlaneCard: React.FC<SwimlaneCardProps> = (props) => {
	const { item, card_struct, InspectView, refreshSwimlane } = props;
	const { header, footer, lines } = card_struct as CardStruct;
	// console.log("card_struct", card_struct, item);

	const [isOpen, setIsOpen] = React.useState<boolean>(false);
	const node = useRef<HTMLDivElement>(null);

	const handleClose = () => {
		setIsOpen(!isOpen);
	};

	const updatePriority = () => {
		handleClose();
		refreshSwimlane();
	};

	const tooltip = (str: string) => {
		return (
			str
				?.replace("auto_name", "")
				?.replace(/_/g, " ")
				?.replace(/\b\w/g, (char) => char.toUpperCase())
				.trim() || ""
		);
	};

	// `remind_on`

	const getValue = (key: any) => {
		const autoName = `${key}_auto_name`;
		if (item[autoName]) {
			return item[autoName];
		}
		let v = item[key] || "";
		if (["__due_date", "__created_on", "__updated_on", "__cleared_on"].includes(key)) {
			v = convertTimeStampToDate(v);
		}
		return v;
	};

	const InspectPopover: React.FC<InspectPopoverProps> = () => (
		<div className="popover-parent-container">
			<div className="popover-header">
				<strong>Details</strong>
				<i onClick={handleClose} className="fa fa-times-circle pull-right close-popover" />
			</div>
			<div onClick={(event) => event.stopPropagation()} className="poper-main-content">
				<div>
					<InspectView
						updatePriority={updatePriority}
						isPopover={true}
						tabData={item}
						parentProps={props}
						rowData={props.item}
					/>
				</div>
			</div>
		</div>
	);
	return (
		<SwimlaneCardPopoverPopper onClose={() => handleClose()} content={<InspectPopover />} open={isOpen}>
			<div onClick={() => setIsOpen(true)} ref={node} className="card-main-container">
				<div
					className="card-header"
					style={{
						boxShadow: "0px 1px 3px 0px rgba(0, 0, 0, 0.2)",
						display: "flex",
					}}
				>
					{header?.map((headerField: string, headerIndex: number) => {
						const vs = getValue(headerField);
						if (!vs) {
							return <div key={headerField}></div>;
						}
						return (
							<Tooltip key={headerField} title={tooltip(headerField)} arrow>
								<p className={headerIndex == 0 ? "header-left" : "header-right"} key={headerIndex}>
									{vs}
								</p>
							</Tooltip>
						);
					})}
				</div>
				{lines && lines.length > 0 && (
					<div className="card-body">
						{lines.filter(Boolean).map((line: string[], lineIndex: number) => (
							<div key={lineIndex} className="body-content">
								<p>{line[0] === null ? "" : getValue(line[0])?.toString()}</p>
								{line[1] && <p>{line[1] === null ? "" : getValue(line[1])?.toString()}</p>}
							</div>
						))}
					</div>
				)}
				<div className={footer && (getValue(footer[0]) || getValue(footer[1])) ? "card-footer" : ""}>
					{footer?.map((footerKey: string, footerIndex: number) => {
						return (
							<div className={footerIndex == 1 ? "footer-right" : "footer-left"} key={footerIndex}>
								<div>{getValue(footerKey)}</div>
								{footerIndex == 1 && item?.__priority === 5 && (
									<div className="high-priority">
										<span>High</span>
									</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
		</SwimlaneCardPopoverPopper>
	);
};
