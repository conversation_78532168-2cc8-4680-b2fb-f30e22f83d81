import type { ReactElement } from "react";
import React, { useState } from "react";
import { Popper, Paper, Grow, ClickAwayListener } from "@mui/material";
import { makeStyles } from "@mui/styles";

interface SwimlaneCardPopoverPopperProps {
	content: ReactElement;
	children: ReactElement;
	open: boolean;
	onClose?: () => void;
	arrow?: boolean;
	placement?: "top" | "bottom" | "left" | "right";
}

const useStyles = makeStyles(() => {
	const color = "#ffffff";
	return {
		popoverRoot: {
			maxWidth: 500,
			borderRadius: "10px",
		},
		popper: {
			zIndex: 2000,
			marginLeft: 10,
			'&[data-popper-placement*="bottom"] $arrow': {
				top: 0,
				left: 0,
				marginTop: "-0.71em",
				marginRight: 4,
				"&::before": {
					transformOrigin: "0 100%",
				},
			},
			'&[data-popper-placement*="top"] $arrow': {
				bottom: 0,
				left: 0,
				marginBottom: "-0.71em",
				marginLeft: 4,
				marginRight: 4,
				"&::before": {
					transformOrigin: "100% 0",
				},
			},
			'&[data-popper-placement*="right"] $arrow': {
				left: 0,
				marginLeft: "-1.3em",
				width: "18px",
				height: "18px",
				marginTop: 4,
				marginBottom: 4,
				boxShadow: "2px 2px 5px rgba(0, 0, 0, 0.)",
				"&::before": {
					transformOrigin: "100% 100%",
				},
			},
			'&[data-popper-placement*="left"] $arrow': {
				right: 0,
				marginRight: "-0.71em",
				height: "1em",
				width: "0.71em",
				marginTop: 4,
				marginBottom: 4,
				borderRight: "1px solid yellow",
				"&::before": {
					transformOrigin: "0 0",
				},
			},
		},
		arrow: {
			position: "absolute",
			boxSizing: "border-box",

			color,
			"&::before": {
				content: '" "',
				margin: "auto",
				display: "block",
				width: "100%",
				height: "100%",
				marginTop: "30px",
				backgroundColor: "white",
				transform: "rotate(45deg)",
				boxShadow: "0px 2px 4px 0px rgba(0, 0, 0, 0.2)",
			},
		},
	};
});

export const SwimlaneCardPopoverPopper = ({
	placement = "top",
	arrow = true,
	open,
	content,
	children,
	onClose,
}: SwimlaneCardPopoverPopperProps) => {
	const classes = useStyles();
	const [arrowRef, setArrowRef] = useState<HTMLElement | null>(null);
	const [childNode, setChildNode] = useState<HTMLElement | null>(null);

	return (
		<>
			{React.cloneElement(children, { ...children.props, ref: setChildNode })}
			<Popper
				open={open}
				anchorEl={childNode}
				placement={placement}
				transition
				className={classes.popper}
				disablePortal={true}
				modifiers={[
					{
						name: "flip",
						enabled: false,
						options: {
							altBoundary: true,
							rootBoundary: "viewport",
							padding: 8,
						},
					},
					{
						name: "preventOverflow",
						enabled: true,
						options: {
							altAxis: true,
							altBoundary: true,
							tether: false,
							rootBoundary: "viewport",
							padding: 8,
						},
					},
					{
						name: "arrow",
						enabled: true,
						options: {
							element: arrowRef,
						},
					},
				]}
			>
				{({ TransitionProps }) => (
					<Grow {...TransitionProps} timeout={350}>
						<Paper>
							<ClickAwayListener
								onClickAway={() => {
									onClose;
								}}
							>
								<Paper className={classes.popoverRoot}>
									{arrow ? <span className={classes.arrow} ref={setArrowRef} /> : null}
									<div>{content}</div>
								</Paper>
							</ClickAwayListener>
						</Paper>
					</Grow>
				)}
			</Popper>
		</>
	);
};

export default SwimlaneCardPopoverPopper;
