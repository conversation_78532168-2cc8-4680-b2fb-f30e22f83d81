
import type { FC, SyntheticEvent } from "react";
import React from "react";
import "./snap-timeline.less";
import { Chrono } from "react-chrono";
export interface TimeLineItem {
	type: string;
	title: string;
	event: "create" | "update" | "archive";
	date: Date;
	form_id: number | string;
	form: string;
	rkey: string;
	form_data: Record<string, unknown>
}
export interface SnapTimeLineProps {
	items: TimeLineItem[];
	onItemClick: (ic: TimeLineItem, el: SyntheticEvent) => void;
}
export const SnapTimeLine: FC<SnapTimeLineProps> = (props) => {
	const { items, onItemClick } = props;
	if (!items.length) {
		return null;
	}
	const onClickEvent = (ic: TimeLineItem, el: SyntheticEvent) => {
		onItemClick(ic, el);
	};
	return (
		<div className="timeline-viewer">
			<Chrono
				mode="VERTICAL_ALTERNATING"
				items={items}
				titleDateFormat='MMM DD'
				scrollable={{ scrollbar: false }}
				disableToolbar={true}
				scrollbar={false}
				fontSizes={{
					title: "1rem"
				}}
				noUniqueId={true}
				hideControls={true}
				disableAutoScrollOnClick={false}
			>
				{items.map((d) => {
					const { date, title, type } = d;
					return (
						<div key={d.rkey} className="tl-card">
							<div className="tl-type">
								<p>{type}</p>
							</div>
							<div className="tl-cont top" onClick={(el) => onClickEvent(d, el)}>{window.moment(date).format("MMM DD")}</div>
							<div className="sep" />
							<div className="tl-cont bottom">{title}</div>
						</div>
					);
				})}
			</Chrono>
		</div>
	);
};
