.timeline-viewer {
    display: flex;
    flex-direction: column;
    flex: 1;
    color: #C0C1C9;

    .timeline-main-wrapper {
        padding: 0px;
        height: 100%;

        .timeline-item-title {
            display: none;
        }

        .vertical-item-row {
            height: 30px;
        }

        .timeline-vertical-circle {
            >div {
                background: #C0C1C9;

                &.active {
                    background: #53B2DE;
                }

                &::before {
                    background: #C0C1C9;
                }

                &::after {
                    background: #C0C1C9;
                }
            }
        }

        .card-content-wrapper {
            .tl-card {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                flex: 1;

                .tl-cont {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 14px;
                    letter-spacing: 0em;
                    text-align: center;
                    padding-left: 25px;
                    cursor: pointer;
                    width: 100%;
                    position: absolute;

                    &.top {
                        padding-bottom: 20px;
                    }

                    &.bottom {
                        top: 102px;
                    }
                }

                .tl-type {
                    height: 25px;
                    width: 25px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 25px;
                    border: 3px solid #C0C1C9;
                    background-color: #283742;
                    font-weight: 600;
                    flex-shrink: 0;
                }

                .tl-text-cont {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }

                .sep {
                    height: 3px;
                    width: 71px;
                    background: #C0C1C9;
                }
            }

            &.right {
                .tl-card {
                    display: flex;
                    justify-content: flex-end;
                    flex-direction: row-reverse;
                    align-items: center;

                    .tl-type {
                        height: 25px;
                        width: 25px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 25px;
                        border: 3px solid #C0C1C9;
                        background-color: #283742;

                    }

                    .tl-cont {
                        padding-right: 25px;
                        padding-left: unset;
                    }
                }
            }

            .timeline-card-content {
                align-items: center;
                justify-content: center;
                background: transparent;
                filter: none !important;
                outline: none;

                .card-description {
                    margin: 0px;
                    padding: 0px;
                    height: 100%;
                    display: flex;
                    flex: 1;
                    justify-content: center;

                }

                >span {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 2.5rem;
                    height: 3px;
                    position: absolute;
                    top: calc(50%);
                    transform: translateY(-49%) rotate(180deg);
                    z-index: -1;
                    right: -8px;
                    filter: unset;
                    background: #C0C1C9;
                }
            }
        }

        .hfqrxG {
            filter: none;
        }

        .laVdxH::after {
            background: #C0C1C9
        }

        .laVdxH::before {
            background: #C0C1C9
        }

        .bOPcbp {
            background: #C0C1C9
        }

        .bOPcbp:not(.using-icon).active {
            border: 3px solid #C0C1C9;
        }

        >div {
            padding: 0px;

            div[role="listitem"] {}
        }
    }
}