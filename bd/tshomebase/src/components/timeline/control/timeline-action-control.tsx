import type { <PERSON> } from "react";
import React from "react";
import "./timeline-action-control.less";

export interface TimeLineActionControlProps {
	onClear: () => void;
	onApply: () => void;
}
export const TimeLineActionControl: FC<TimeLineActionControlProps> = (props) => {
	const { onApply, onClear } = props;
	return (
		<div className="tl-action-control">
			<button className="apply" onClick={onApply}>
				APPLY
			</button>
			<button className="clear" onClick={onClear}>
				CLEAR
			</button>
		</div>
	);
};