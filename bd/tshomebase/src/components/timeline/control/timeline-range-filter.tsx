import type { FC } from "react";
import React from "react";
import "./timeline-range-filter.less";
import { StylesConfig } from "react-select";
import { SelectConfig } from "@dsl/fields/field-select-config";
import { FieldSelect } from "@dsl/fields/field-select";
export interface TimeLineRangeFilterOptions {
	duraton: number;
	unit: "months" | "days" | "years";
}
export interface TimeRangeFilterProps {
	value: TimeLineRangeFilterOptions;
	onChange: (object: Partial<TimeLineRangeFilterOptions>) => void;
}

const selectStyleOver: StylesConfig = {
	...SelectConfig.dsl.style,
	control: (provided, state) => ({
		...provided,
		borderRadius: "8px",
		backgroundColor: state.isFocused ? "#FFFFFF" : "#FCFCFC",
		borderColor: state.isFocused ? "#58505B" : "rgba(88, 80, 91, 0.251)",
		boxShadow: state.isFocused ? "0px 0px 0px 0.5px #58505B" : "0px 0px 0px 0.5px rgba(88, 80, 91, 0.251)",
		borderWidth: "1px",
		"&:hover": {
			borderColor: "rgba(88, 80, 91, 0.5)", // Change border color on hover
			boxShadow: state.isFocused ? "#58505B" : "rgba(88, 80, 91, 0.251)",
		},
		minWidth: "150px !important",
		maxWidth: "150px !important",
	}),
	container: (base, state) => ({
		...base,
		width: "fit-content !important",
	}),
	menu: (base, state) => ({
		...base,
		zIndex: 99999999,
	}),
};
const DURATION_OPTIONS = [
	{ label: "Days", value: "days" },
	{ label: "Months", value: "months" },
	{ label: "Years", value: "years" },
];

export const TimeRangeFilter: FC<TimeRangeFilterProps> = (props) => {
	const { value, onChange } = props;
	return (
		<div className="tl-filter">
			<p>Last</p>
			<input
				type="number"
				value={value.duraton}
				onChange={(e) => {
					onChange({ duraton: e.target.value ? parseInt(e.target.value) : 0 });
				}}
			/>
			<FieldSelect
				form="none"
				defaultValueSource={value.unit || ""}
				defaultValueAutoName={DURATION_OPTIONS.filter((v) => v.value == value.unit)?.[0]?.label || ""}
				multi={false}
				disabled={false}
				isClearable={false}
				options={DURATION_OPTIONS}
				onChange={(val, an) => {
					onChange({ unit: val });
				}}
				customStyles={selectStyleOver}
				theme={SelectConfig.dsl.theme}
				placeholder="Duration..."
			/>
		</div>
	);
};
