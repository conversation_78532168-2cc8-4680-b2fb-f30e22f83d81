import type { <PERSON> } from "react";
import React from "react";
import "./timeline-btn.less";

interface TimeLineButtonProps {
	label: string;
	sub: string;
	rkey: string;
	onTypeClick: (type: string) => void;
	selectedTypes: string[];
}
export const TimeLineButton: FC<TimeLineButtonProps> = (props) => {
	const { label, sub, selectedTypes, rkey, onTypeClick } = props;
	const active = selectedTypes.includes(rkey);
	return (
		<div
			className={`tl-act-btn ${active ? "active" : ""}`}
			onClick={() => { onTypeClick(rkey); }}
		>
			<p>{label}</p>
			<p>{sub}</p>
		</div>
	);
};