@import (reference) '../../../less/style/main.less';

.tl-filter {
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    color: #283742;
    align-items: center;
    gap: 10px;

    >p {
        font-size: 12px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
    }

    >input {
        .form-input-field;
        width: 70px;
        height: 36px;
        text-align: center;
        background-color: @semi-transparent-light-gray-08;
        box-shadow: 0px 0px 0px 0.5px rgba(88, 80, 91, 0.251);
        border-color: rgba(88, 80, 91, 0.251);
        background-color: white;

        &:focus {
            .form-input-field-focus;
            box-shadow: 0px 0px 0px 0.5px #58505B;
            border-color: #58505B;
        }
    }

    >select {
        .select-box;
        width: 155px;
        height: 36px;
        background-color: @semi-transparent-light-gray-08;
    }
}