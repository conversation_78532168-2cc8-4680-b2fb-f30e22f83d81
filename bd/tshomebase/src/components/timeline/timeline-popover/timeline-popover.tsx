
import type { <PERSON> } from "react";
import React from "react";
import "./timeline-popover.less";
import Popover from "@mui/material/Popover";
import snap from "@public/icons/snap";
import { getAutoName } from "@utils/dsl-fx";
import type { TimeLineItem } from "../snap-timeline/snap-timeline";
import icons from "@public/icons";
interface TimeLinePopover {
	onClose: () => void
	data: TimeLineItem;
	anchorEl: HTMLElement;
	open: boolean;
	onOpen: (id: string | number, label: string, mode: string, form: string, componentProps: Record<string, unknown>) => void;
}
export const TimeLinePopover: FC<TimeLinePopover> = (props) => {
	const { onClose, onOpen, data, anchorEl, open } = props;
	if (!data) {
		return;
	}
	if (!open) {
		return;
	}
	return (
		<Popover
			id="popover"
			open={open}
			anchorEl={anchorEl}
			onClose={() => {
				onClose();
			}}
			anchorOrigin={{
				vertical: "center",
				horizontal: "left",
			}}
			transformOrigin={{
				vertical: "center",
				horizontal: "right",
			}}
			PaperProps={{
				sx: {
					borderRadius: '8px',
					boxShadow: '0px 4px 8px 0px #35201714',
				},
			}}
		>
			<div className="tl-popover">
				<div className="po-head">
					<div className="po-info-ts">{`${moment(data.date).format("MM/DD/YYYY @ HH:MM a")}${data.form_data.created_by_auto_name ? ` by ${data.form_data.created_by_auto_name}` : ""}`}</div>
					<img src={icons.common.crossOutline}
						onClick={onClose}
					/>
				</div>
				<div className="po-body">
					<div className="po-details">
						{moment(data.date).format("MM/DD/YYYY @ HH:MM a")}{data.form_data.created_by_auto_name ? ` by ${data.form_data.created_by_auto_name}` : ""}
					</div>

				</div>
				<div className="po-footer">
					<div className="po-open" onClick={() => {
						const label = getAutoName(data.form, data.form_id);
						onOpen(data.form_id, label, "read", data.form, {});
						onClose();
					}}
					>
						<img src={snap.patient.open} />
						Open
					</div>
				</div>
			</div>
		</Popover>
	);
};
