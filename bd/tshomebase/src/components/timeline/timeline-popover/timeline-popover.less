@import (reference) '../../../less/style/main.less';

.tl-popover {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    background: #fff;
    gap: 15px;
    border: 1px solid #9974CE;
    border-radius: 8px;

    .po-head {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .po-info-ts {
            font-size: 12px;
            font-weight: 600;
            line-height: 14px;
            letter-spacing: 0em;
            text-align: left;
            color: #5A595880;


        }

        img {
            cursor: pointer;
        }
    }

    .po-body {
        display: flex;
        justify-content: space-between;
        gap: 15px;
        background: #9974CE14;
        border-radius: 9px 12px;

        .po-details {
            width: 320px;
            height: 118px;
            padding: 10px;
            font-weight: 500;
            font-size: 12px;
            color: #5A5958;
            border-radius: 2px;
            gap: 10px;
            line-height: 24px;
        }


    }

    .po-footer {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;

        .po-open {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
            cursor: pointer;

            .btn-primary;

            img {
                width: 15px;
                height: 15px;
            }


        }
    }


}