import { useContext, useEffect, useRef } from "react";
import { joinPaths } from "@utils/fx";
import { NavigationContext, type NavigationContextType, } from "./provider";
import { HistoryModule, OpenTabContext, TabHistory } from "@contexts/history/provider";
import { fetchFormData } from "@hooks/form-data";

export const getCurrentLocation = () => "/" + window.location.hash.replace("#", "").split("/").filter(Boolean).join("/");

export interface UseNavigationProps {
	urlCallback?: (cpath: string, cops: Record<string, unknown> | null) => void;
	isActive: boolean;
	isParentActive: boolean;
	navToURL?: string;
	navPath?: string[];
	goTo: (url: string) => void;
	data?: undefined
	ops?: Record<string, unknown> | null;
	tabHistory: TabHistory;
}

export const useNavigation = (props: UseNavigationProps, route: string, ops?: Record<string, unknown> | null) => {
	const [globalNav, setGlobalNav] = useContext(NavigationContext) as unknown as NavigationContextType;
	const tabHistory = useContext<TabHistory>(OpenTabContext);
	const history = useRef("");

	let navToURL = "";
	if (props.navToURL && props.navToURL.startsWith(route)) {
		navToURL = joinPaths(props.navToURL.replace(route, ""), "");
	}

	useEffect(() => {
		setURL(undefined, ops);
	}, [props.isActive, props.isParentActive]);

	const goTo = (url: string): void => {
		if (url.startsWith("#")) {
			url = url.substring(1);
		}
		window.location.hash = url;
		window.initNavToURL = joinPaths(url, "");
		setGlobalNav((nav) => ({ lrct: Date.now().toString(), route: window.initNavToURL }));
	};



	const removeFromHistory = (module: HistoryModule, id: string) => {
		if (!Array.isArray(tabHistory.open[module])) {
			tabHistory.open[module] = [];
		}

		if (!Array.isArray(tabHistory.close[module])) {
			tabHistory.close[module] = [];
		}
		// Find the index of the item with the matching id in the open history
		const openIndex = tabHistory.open[module]?.findIndex((item: { id: string; }) => item.id === id);

		if (openIndex !== -1) {
			// Move the item from open history to closed history
			const [item] = tabHistory.open[module].splice(openIndex, 1);
			tabHistory.close[module].push(item);
		}
	}

	const pushToHistory = async (module: HistoryModule, id: string, path: string) => {
		if (!Array.isArray(tabHistory.open[module])) {
			tabHistory.open[module] = [];
		}
		if (!Array.isArray(tabHistory.close[module])) {
			tabHistory.close[module] = [];
		}
		const resp = await fetchFormData(module, id, true);
		if (!resp.success)
			return;

		// Check if the item already exists in open history
		const openIndex = tabHistory.open[module]?.findIndex((item: { id: string; }) => item.id === id);

		if (openIndex === -1) {
			// Add the new item to open history
			tabHistory.open[module].push({ id, data: resp.data, path });
		} else {
			// Update the existing item
			tabHistory.open[module][openIndex] = { id, data: resp.data, path };
		}

		// Remove item from closed history if it exists
		const closeIndex = tabHistory.close[module].findIndex((item: { id: string; }) => item.id === id);
		if (closeIndex !== -1) {
			tabHistory.close[module].splice(closeIndex, 1);
		}
	}

	const setURL = (cpath?: string, callBackOps: Record<string, unknown> | null = null) => {
		if (cpath)
			history.current = cpath;
		else
			cpath = history.current;

		if (!props.isActive || !props.isParentActive) {
			return;
		}
		const op = ops || callBackOps;

		const np = joinPaths(route, cpath);
		if (props.urlCallback) {
			props.urlCallback(np, op);
			return;
		}
		if (globalNav.route && "#" + globalNav.route == window.location.hash) {
			setTimeout(() => {
				globalNav.route = "";
				setGlobalNav((nav) => ({ ...nav, route: "" }));
			}, 3000);
		}
		if (op) {
			if (op.enableHistory) {
				op.onTabOpen(np);
			}
		}
		window.location.hash = np;
		return;
	};

	let navPath = navToURL.split("?")[0].split('/').filter(Boolean) || [];
	return {
		urlCallback: setURL,
		pushToHistory,
		removeFromHistory,
		goTo,
		history,
		navToURL,
		navPath,
		globalNav,
		tabHistory,
	};
};
