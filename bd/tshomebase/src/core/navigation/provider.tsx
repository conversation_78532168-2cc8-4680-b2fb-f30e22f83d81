import type { FC } from "react";
import React, { useState, createContext } from "react";

export const defaultRoutes = {
	lrct: "",
	route: "",
};

export const NavigationContext = createContext(defaultRoutes);

type Props = {
	children?: React.ReactNode
};

export type NavigationContextType = [{ lrct: string, route: string }, (nav: Record<string, string>) => void]
export const NavigationProvider: FC<Props> = (props) => {
	const { children } = props;
	const [navigation, setNavigation] = useState(defaultRoutes);
	return (
		<NavigationContext.Provider value={[navigation, setNavigation]}>
			{children}
		</NavigationContext.Provider>
	);
};
