import { createTheme } from "@mui/material/styles";

export const baseTheme = createTheme({
	palette: {
		primary: {
			main: "#837BB2",
			light: "#9A94BF",
			dark: "#6C63A5",
			contrastText: "#fff",
		},
		secondary: {
			main: "#5B83B0",
			light: "#6D95BF",
			dark: "#4971A1",
			contrastText: "#fff",
		},
		error: {
			main: "#E58787",
			light: "#EB9D9D",
			dark: "#D07676",
		},
		warning: {
			main: "#E89E64",
			light: "#EEAA7A",
			dark: "#D48656",
		},
		success: {
			main: "#7DB8A5",
			light: "#89C9B5",
			dark: "#529A82",
		},
		info: {
			main: "#668eba",
			light: "#91B9DE",
			dark: "#406591",
		},
		text: {
			primary: "#717680",
			secondary: "#535862",
			disabled: "#A4A7AE",
		},
		background: {
			default: "#F7F7F5",
			paper: "#FCFCFC",
		},
		divider: "#D9D9D4",
	},
	components: {
		MuiButton: {
			styleOverrides: {
				root: {
					borderRadius: 4,
				},
			},
		},
		MuiSwitch: {
			styleOverrides: {
				switchBase: {
					color: "#D5D7DA",
					"&.Mui-checked": {
						color: "#837BB2",
					},
					"&.Mui-checked + .MuiSwitch-track": {
						backgroundColor: "#B1ACC9",
					},
				},
				track: {
					backgroundColor: "#A4A7AE",
				},
			},
		},
	},
	typography: {
		fontFamily: [
			"-apple-system",
			"BlinkMacSystemFont",
			'"Segoe UI"',
			"Roboto",
			'"Helvetica Neue"',
			"Arial",
			"sans-serif",
		].join(","),
	},
});
