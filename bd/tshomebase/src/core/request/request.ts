import axios, { AxiosResponse } from "axios";

export interface CRRequestOptions {
	method?: string;
	url: string;
	data?: object;
	headers?: object;
	params?: object;
	pool?: boolean;
	raw?: boolean;
}
type ResolveFunction = (value: AxiosResponse | CRResponse | PromiseLike<CRResponse>) => void;
export interface PooledRequest {
	opts: CRRequestOptions;
	resolve: ResolveFunction;
	reject: (reason?: any) => void;
}

type CRResponseData = Record<string | number, unknown> | Record<string | number, unknown>[]

export interface CRResponse {
	success: boolean;
	data?: CRResponseData;
}

const poolRequest = (p: PooledRequest) => {
	if (!window.pooledRequest) {
		window.pooledRequest = {};
	}
	const url = p.opts.url;

	if (!window.pooledRequest[url]) {
		window.pooledRequest[url] = [];
	}
	window.pooledRequest[url].push(p);

	if (window.pooledRequest[url].length == 1) {
		let raw = p.opts.raw || false;
		setTimeout(() => {
			axios(p.opts).then(response => {
				const r = processSuccess(response, raw);
				let count = 1;
				while (window.pooledRequest?.[url].length) {
					const pe = window.pooledRequest[url].shift();
					if (pe)
						handleResponse(r, pe.resolve, pe.reject)
				}
			}).catch(err => {
				const r = processError(err);
				while (window.pooledRequest?.[url].length) {
					const pe = window.pooledRequest[url].shift();
					if (pe)
						handleResponse(r, null, pe.reject);
				}
			});
		}, 10)

	}
}

const handleResponse = (r: CRResponse | AxiosResponse, resolve: ResolveFunction | null, reject: (reason?: any) => void) => {
	if (!resolve && reject) {
		return reject(r);
	}
	if (!("success" in r) || r.success) {
		if (resolve)
			return resolve(r);
	}
	return reject(r);
}

const processSuccess = (response: AxiosResponse, raw: boolean): CRResponse | AxiosResponse => {
	if (raw) {
		return response;
	}
	const r = {
		success: false,
		data: response.data,
	};
	if (response.status % 200 < 10) {
		r.success = true
	}
	return r;
}

const processError = (error: CRResponseData): CRResponse => {
	const r = {
		success: false,
		data: error,
	};
	return r;
}

export const request = (opts: CRRequestOptions) => new Promise<CRResponse | AxiosResponse>((resolve, reject) => {
	opts.method = opts.method || "GET";
	opts.raw = opts.raw || false;
	if (opts.url.startsWith("api/") || opts.url.startsWith("/api/")) {
		opts.url = opts.url.replace("api/", "");
	}
	if (!opts.pool || opts.method !== "GET") {
		return axios(opts).then(response => resolve(processSuccess(response, opts.raw as boolean)))
			.catch(err => reject(processError(err)));
	}
	let p: PooledRequest = {
		opts: opts,
		resolve,
		reject,
	};
	poolRequest(p)
});

export const loadAxiosConfigs = () => {
	axios.defaults.withCredentials = true;
	axios.defaults.baseURL = window.CRConfig.CRBackend;
	axios.defaults.headers.common["Content-Type"] = "application/json";
};