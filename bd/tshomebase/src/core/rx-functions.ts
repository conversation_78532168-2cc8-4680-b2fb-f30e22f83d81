import { openPartialFormFillPopup } from "@blocks/dsl-card-view/dsl-card-section-popup";
import { fetchFormData, fetchFormFilters } from "@hooks/form-data";
import { request } from "./request/request";
import {
	fieldReadOnly,
	generateDSLFormQueryParams,
	generateLinkMapFromPreset,
	markFieldRequiredIf,
	mergeIfFalsy,
	sectionReadOnly,
	sectionShowOnly,
	updateDSLLinkMap,
	updateLinkIds,
	subformSectionShowOnly,
	openSiteSelector,
	generatePreset,
	showPartialFormWithId,
	getMaxZIndex,
	getNextZIndexFlyoutModelInception,
	uploadToS3,
} from "@utils/dsl-fx";
import { generateAndPrintReport } from "@components/arjs";
import { openARJsPopover, openPrintQuantityPopover } from "@components/popups/arjs-popover/arjs-popover";
import { toast } from "@components/toast";
import { cameraScanner } from "@components/common";
import { textScanner } from "@components/common/handheld-scanner/handheld-scanner";
import { getFormConfig } from "@dsl-form/config";
import { openMediaViewerPopup } from "@components/popups/media-viewer";
import { getMediaViewerHTML } from "@dsl/fields/field-file";
import { createFormBuss } from "@dsl-form/buss/buss";
import { getFormLabel, getPresetFromLinkIds, areEqual, valueIsEmpty } from "@utils/fx";
import { showGridSumColumn } from "@blocks/dsl-grid-view/utils";
import { getCarePlanLinkMap } from "@modules/patient/helper";
import { openAlertDialog } from "@blocks/dsl-card-view/dsl-field-alert";
import { openShipmentPopover } from "@modules/dispense/shipments";
import { getUserPreference } from "@contexts/preference/provider";
import { blockUntilTimePassed } from "@utils/fx";

export const bindRXFunctions = () => {
	window["blockUntilTimePassed"] = blockUntilTimePassed;
	window["request"] = request;
	window["fetchFormData"] = fetchFormData;
	window["fetchFormFilters"] = fetchFormFilters;
	window["openPartialFormFillPopup"] = openPartialFormFillPopup;
	window["openPrintQuantityPopover"] = openPrintQuantityPopover;
	window["generateDSLFormQueryParams"] = generateDSLFormQueryParams;
	window["sectionReadOnly"] = sectionReadOnly;
	window["fieldReadOnly"] = fieldReadOnly;
	window["markFieldRequiredIf"] = markFieldRequiredIf;
	window["sectionShowOnly"] = sectionShowOnly;
	window["generateAndPrintReport"] = generateAndPrintReport;
	window["openARJsPopover"] = openARJsPopover;
	window["getFormConfig"] = getFormConfig;
	window["areEqual"] = areEqual;
	window["valueIsEmpty"] = valueIsEmpty;
	window["cameraScanner"] = cameraScanner;
	window["textScanner"] = textScanner;
	window["openMediaViewerPopup"] = openMediaViewerPopup;
	window["getMediaViewerHTML"] = getMediaViewerHTML;
	window["createFormBuss"] = createFormBuss;
	window["generateLinkMapFromPreset"] = generateLinkMapFromPreset;
	window["updateDSLLinkMap"] = updateDSLLinkMap;
	window["updateLinkIds"] = updateLinkIds;
	window["mergeIfFalsy"] = mergeIfFalsy;
	window["subformSectionShowOnly"] = subformSectionShowOnly;
	window["getPresetFromLinkIds"] = getPresetFromLinkIds;
	window["showGridSumColumn"] = showGridSumColumn;
	window["toast"] = toast;
	window["getFormLabel"] = getFormLabel;
	window["getCarePlanLinkMap"] = getCarePlanLinkMap;
	window["openSiteSelector"] = openSiteSelector;
	window["generatePreset"] = generatePreset;
	window["showPartialFormWithId"] = showPartialFormWithId;
	window["openAlertDialog"] = openAlertDialog;
	window["openShipmentPopup"] = openShipmentPopover;
	window["getMaxZIndex"] = getMaxZIndex;
	window["getNextZIndexFlyoutModelInception"] = getNextZIndexFlyoutModelInception;
	window["uploadToS3"] = uploadToS3;
	window["getUserPreference"] = getUserPreference;
};
