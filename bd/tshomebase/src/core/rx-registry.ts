import { <PERSON><PERSON>ieldEmbed } from "@dsl/fields/field-embed";
import DS<PERSON>ardView, { FlyoutHTMLViewer } from "@blocks/dsl-card-view/dsl-card-view";
import { AppView } from "../main";
import { CoffeeQueryDSLEditor } from "@modules/workflow/workflow-cson-builder/wf-cson-editor";
import { registerComponent } from "./coffee";
import { InputPrefillDropDown, SelectPrefillDropdown } from "@components/dropdowns";
import { DSLCardWithPreview, DSLCardMultiForm } from "@blocks/dsl-card-view";
import FieldAuditHistory from "@blocks/dsl-audit-history/field-audit-history";
import FormAuditHistory from "@blocks/dsl-audit-history/form-audit-history";
import { ContinuesScannerScanner } from "@components/common/continues-scanner/continues-scanner";
import { OutstandingAR } from "@modules/billing/components/outstanding-ar";
import { OutstandingARReadOnly } from "@modules/billing/components/outstanding-ar-readonly";
import { FieldJSON } from "@dsl/fields/field-json";
import { WriteOffsGrid } from "@modules/billing/components/writeoff";
import { WriteOffsGridReadOnly } from "@modules/billing/components/writeoff-readonly";
import { AdjustmentGrid } from "@modules/billing/components/adjustment";
import { AdjustmentGridReadOnly } from "@modules/billing/components/adjustment-readonly";
import { DSLFieldDateTime } from "@dsl/fields/field-datetime";
import { DeliveryTicketScanner } from "@modules/patient/delivery_ticket/delivery-ticket-scanner";
import { listConfigs } from "@dsl-form/config";

export const initiateRXRegistry = () => {
	const rxComponents = {
		App: AppView,
		ApiPrefill: InputPrefillDropDown,
		SelectPrefill: SelectPrefillDropdown,
		CoffeeQueryDSLEditor,
		DeliveryTicketScanner,
		DSLCardView,
		FlyoutHTMLViewer,
		DSLFieldEmbed,
		DSLCardWithPreview,
		DSLCardMultiForm,
		FieldAuditHistory,
		FormAuditHistory,
		ContinuesScannerScanner,
		OutstandingAR,
		OutstandingARReadOnly,
		WriteOffsGrid,
		WriteOffsGridReadOnly,
		AdjustmentGrid,
		AdjustmentGridReadOnly,
		FieldJSON,
		DSLFieldDateTime,
	};
	for (const [key, value] of Object.entries(rxComponents)) {
		registerComponent(key, value);
	}
	for (const [key, value] of Object.entries(listConfigs)) {
		if (value?.renderComponents) {
			for (const [mode, component] of Object.entries(value.renderComponents)) {
				registerComponent(key + ":" + mode, component);
			}
		}
	}
};
