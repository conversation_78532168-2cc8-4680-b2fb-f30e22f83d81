import qz from "qz-tray";


export const initializeQZTray = () => {
	const qzb64 = "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"
	qz.security.setCertificatePromise((resolve, reject) => {
		const qzCert = Buffer.from(qzb64, "base64").toString("utf-8");
		resolve(qzCert);
	});

	qz.security.setSignatureAlgorithm("SHA512");

	qz.security.setSignaturePromise((toSign) => (resolve, reject) => {
		fetch(`/api/print/qz/sign?req=${toSign}`, {
			method: "GET",
			cache: "no-store",
			headers: {
				"Content-Type": "text/plain"
			}
		}).then(async (data) => {
			if (data.ok) {
				const d = await data.text();
				resolve(d);
			} else {
				reject(data.text());
			}
		}).catch(e => {
			console.error(e);
		});
	});
};