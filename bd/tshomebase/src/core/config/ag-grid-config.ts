import { LicenseManager } from "ag-grid-enterprise";
import { agGrid<PERSON><PERSON> } from "@public/cert/ag-grid";
import {
	AllCommunityModule,
	ServerSideRowModelModule,
	ClientSideRowModelModule,
	ModuleRegistry,
	AdvancedFilterModule,
	ColumnsToolPanelModule,
	FiltersToolPanelModule,
	SideBarModule,
	TextFilterModule,
	DateFilterModule,
	NumberFilterModule,
	SetFilterModule,
	MultiFilterModule,
	ServerSideRowModelApiModule,
	TreeDataModule,
	RowGroupingModule,
	HighlightChangesModule,
	MasterDetailModule,
	ColumnMenuModule,
	ColumnAutoSizeModule,
	ExcelExportModule,
	RowGroupingPanelModule,
	ContextMenuModule,
} from "ag-grid-enterprise";

export const loadAGGridConfigs = () => {
	LicenseManager.setLicenseKey(agGridKey);
	ModuleRegistry.registerModules([
		AllCommunityModule,
		ServerSideRowModelModule,
		ClientSideRowModelModule,
		AdvancedFilterModule,
		ColumnsToolPanelModule,
		HighlightChangesModule,
		RowGroupingModule,
		FiltersToolPanelModule,
		ServerSideRowModelApiModule,
		SideBarModule,
		TextFilterModule,
		DateFilterModule,
		NumberFilterModule,
		SetFilterModule,
		MultiFilterModule,
		MasterDetailModule,
		ColumnMenuModule,
		ColumnAutoSizeModule,
		ExcelExportModule,
		RowGroupingPanelModule,
		TreeDataModule,
		ContextMenuModule,
	]);
};
