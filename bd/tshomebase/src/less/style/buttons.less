@import "../utils/mixins.less";
@import "../utils/vars/common.less";
@import "./common.less";

.btn-layout {
  font-weight: 500;
  cursor: pointer;
  min-height: var(--button-min-height);

  img {
    height: var(--btn-img-height);
    width: var(--btn-img-width);
  }
}

.btn-default {
  .btn-primary;
}

.btn-primary {
  .custom-button-variant(@soft-dark-gray, @dark-gray);
  .btn-layout;

  img {
    filter: brightness(0.5);
  }

  &:hover {
    background-color: @black;
    color: @white;

    img {
      filter: brightness(400%);
    }
  }
}

.btn-count {
  .btn-primary;
  display: flex;
  gap: 10px;
  align-items: center;

  span {
    background-color: @charcoal-gray;
    height: 16px;
    width: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    padding: 10px;
    font-size: 12px;
    color: white;
    flex-shrink: 0;
  }

  &.active,
  &:hover {
    background-color: @black;
    color: white;

    span {
      background-color: @white;
      color: @black;
    }
  }
}

.btn-secondary {
  .btn-primary;
  color: @dark-gray;
  background-color: fade(@sky-blue, 8%);
}

.btn-tertiary {
  .custom-button-variant(@bright-teal, @white);
  .btn-layout;

  img {
    filter: brightness(400%);
  }

  &:hover {
    background-color: @black;
    color: @white;

    img {
      filter: brightness(0.5);
    }
  }
}

.btn-coral-red {
  .custom-button-variant(@coral-red, @white);
  .btn-layout;

  img {
    filter: brightness(400%);
  }

  &:hover {
    background-color: @black;
    color: @white;

    img {
      filter: brightness(0.5);
    }
  }
}

.btn-rustic-brown {
  .custom-button-variant(@rustic-brown, @white);
  .btn-layout;

  img {
    filter: brightness(400%);
  }

  &:hover {
    background-color: @black;
    color: @white;

    img {
      filter: brightness(0.5);
    }
  }
}

.btn-dark-light {
  .custom-button-variant(@dark-gray, @white);
  .btn-layout;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: var(--btn-g);
  outline: 1px solid @dark-gray;

  img {
    filter: brightness(400%);
  }

  &:hover {
    background-color: @white;
    color: @dark-gray;

    img {
      filter: brightness(0.5);
    }
  }
}