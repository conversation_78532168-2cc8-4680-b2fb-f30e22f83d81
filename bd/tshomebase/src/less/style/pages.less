@import "./text.less";
@import (reference) "./main.less";

.page-one {
  background-color: @brown;
}

.page-two {
  //// forms cardarea bg / .dsl-templ-area as form
  .card-five;
}

.drawer-one {
  border-radius: @border-radius-12;
  background-color: @brown;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  height: 100vh;
}

.drawer-one-header {
  color: @white;
  .heading-five;
  font-weight: 500;
  background-color: @dark-gray;
  padding: var(--drawer-one-header-p);
  border-top-left-radius: @border-radius-12;
}

.table-contaner {
  .card-four;
  padding: var(--dsl-list-bottom-p);
}
