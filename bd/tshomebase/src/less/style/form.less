@import (reference) "./text.less";
@import (reference) "../utils/vars/common.less";
@import (reference) "./main.less";

.form-section {
    .heading-three;
    text-transform: capitalize;
    color: var(--color-tertiary);
}

.form-section-group {
    .heading-two;
    text-transform: capitalize;
    border-bottom: 1px solid @soft-gray;
    font-weight: 500;
    color: @mid-gray;
    font-size: var(--form-section-group-fs);
}

.form-label-one {
    font-weight: 600;
    .para-two;
    color: @midnight-gray;
}

.required-asterisk-icon-style {
    padding-left: var(--required-asterisk-pl);
    margin-top: -1px;
}

.required-asterisk {
    &::after {
        content: "*";
        .required-asterisk-icon-style;
    }
}

.form-input-unit {
    font-weight: 500;
    .para-two;
}

.unit-show-unit {
    font-weight: 500;
    .para-two;
    width: 50%;
    background-color: transparent;
    color: @charcoal-gray;
    border: none;
}

.form-input-field {
    background-color: @cloud-white;
    border: 1px solid @semi-transparent-light-gray-25;
    border-radius: @border-radius-8;
    .para-two;
    color: @dark-black;
    padding: var(--form-input-field-tmp-p);
    width: 100%;
    font-weight: 500;
    min-height: var(--form-input-field-min-height);

    &.unit {
        color: @dark-black;
    }

    &::placeholder {
        color: @semi-transparent-light-brown-5;
    }

    &:focus {
        .form-input-field-focus;
    }
}

.form-input-field-focus {
    border: 1px solid @dark-gray;
    background-color: @white;
}

.field-required-title {
    .para-four;
    color: @reddish;
    font-weight: 500;
}

.field-required-note-helping-block {
    .para-four;
    color: @semi-transparent-light-brown-5;
    font-weight: 500;
    line-height: 0;
}

.field-error-msg {
    color: @reddish;
    .para-five;
    font-weight: 600;
}

.field-warning-msg {
    color: @orangish;
    .para-five;
    font-weight: 600;
}

.form-icon-one {
    .icon-one;
}

.add-manage-btn {
    .para-five;
    font-weight: 500;
    color: @semi-transparent-light-brown-5;
}

.form-radio-btn-group {
    color: @dark-gray;
    background: @white;
    border: 1px solid @dark-gray;
}

.form-radio-btn-group-active {
    color: @white;
    background: @dark-gray;
}

.search-box-one {
    .para-two;
    padding: var(--search-box-one-p);
    width: 100%;
    font-weight: 500;
    .form-input-field;
}

.search-box-two {
    background-color: @white;
    padding: var(--search-box-two-p);
    border-radius: @border-radius-22;

    input {
        background-color: @white;
        color: @dark-black;
    }
}

.font-icon {
    font-family: 'Font Awesome 6 Pro';
    font-weight: 800;
    font-size: 20px;
    color: #938CBD;
    cursor: pointer;
    padding: 0 8px;
}

.input-checkbox-defualt {
    input[type="checkbox"] {
        .checkbox-one;
    }
}


.checkbox-one {
    .checkbox-one-inner;

    &:hover::before {
        .checkbox-one-hover-before;
    }

    &::before {
        .checkbox-one-before;
    }

    &:checked::before {
        .checkbox-one-ckecked-before;
    }
}

.checkbox-one-inner {
    width: 22px;
    height: 22px;
    -webkit-appearance: none;
    vertical-align: middle;
    border: 0;
    // border-radius: @border-radius-4;
    border-radius: 4px;
    background-color: @white; /// #ffffff
    transition: background @transition-time-one;
    cursor: pointer;
}

.checkbox-one-hover-before {
    border: 2px solid @dark-gray;
}

.checkbox-one-before {
    content: "";
    display: block;
    width: inherit;
    height: inherit;
    border-radius: inherit;
    border: 2px solid @semi-transparent-light-gray-25-six;
}

.checkbox-one-ckecked-before {
    background-image: url("../../public/icons/common/checkbox.svg");
    background-size: inherit;
    background-position: center;
    border: 2px solid @semi-transparent-light-gray-25-six;
}

.select-box {
    .form-input-field;

    &::after {
        content: "";
        border: 1px solid blue !important;
        margin-right: var(--select-box-after-mr);
    }
}

.claim-field-vc {
    color: @purple;

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/claim.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

.money-vc {
    color: #679989;

    .input {
        color: #679989 !important;
    }

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/money.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

input[class*="money"][readonly="readonly"] {
    color: #679989 !important;
}

.discount-vc {
    color: #50749C;

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/discount.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

input[class*="discount"][readonly="readonly"] {
    color: #50749C !important;
}

.status-vc {
    color: #746D9E;

    .input {
        color: #746D9E !important;
    }

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/status.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

input[class*="status"][readonly="readonly"] {
    color: #746D9E !important;
}

.important-vc {
    color: #746D9E;

    .input {
        color: #746D9E !important;
    }

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/important.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

input[class*="important"][readonly="readonly"] {
    color: #746D9E !important;
}

.check-field-vc {
    color: #f57066;

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/checkmark.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

.fdb-field-vc {
    color: @aqua-blue !important;

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/outlined/fdb.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

.cms-1500-field-vc {
    color: @bright-cyan-blue;

    &::before {
        content: " ";
        background-image: url(../../public/icons/common/filled/cms-1500.png);
        background-size: contain;
        width: 16px;
        background-repeat: no-repeat;
        background-position: center;
    }
}

.dotted-field-style {
    border: 0px;
    border-radius: 0px;
    border-bottom: 2px solid @soft-light-gray;
    border-style: dotted;
    background-color: transparent;
}