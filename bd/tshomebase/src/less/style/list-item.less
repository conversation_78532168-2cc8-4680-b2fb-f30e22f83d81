@import '../utils/vars/common.less';
@import (reference) './main.less';

.drop-down-selet-popup {
    .drowp-down-one;

    li {
        a {
            .list-item-one;
        }
    }


    li.active {
        .list-item-one-active-colors;

        a {
            .list-item-one-active;
        }
    }

}

.drowp-down-one {
    border: 1px solid @purple;
    box-shadow: @dropdown-box-shadow-one;
    padding: var(--drop-down-active-p);
    border-radius: @border-radius-8;
    background-color: @white;
}

.list-item-one {
    .para-two;
    font-weight: 500;
    border-radius: @border-radius-6;
    padding: var(--list-item-one-p);
    background-color: transparent;
    color: @dark-black;

    &:hover {
        .list-item-one-active-colors;

        a {
            .list-item-one-active-colors;
        }
    }

    &.active,
    &.select2-highlighted {
        .list-item-one-active-colors;
    }

}

.list-item-one-active-colors {
    background-color: @dark-gray;
    color: @white;
}

.list-item-one-active {
    font-weight: 500;
    border-radius: @border-radius-6;
    padding: var(--list-item-one-p);
    .list-item-one-active-colors;
}