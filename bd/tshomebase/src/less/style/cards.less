@import (reference) './main.less';

.card-one {
    //// border-radius: 12px; and box-shadow none;
    .custom-card-one(none)
}

.card-two {
    //// radius 12px / bg #fff /   ;
    .custom-card-one(@card-box-shadow-two)
}

.card-three {
    //// tabs / white #fff / radius 12px 
    .custom-card-one(@tab-box-shadow-one)
}

.card-four {
    ////   
    //box-shadow: 0px 1px 4px 0px #18100A14; 
    // border-radius: 8px 
    // background-color:  #FBFBFB;
    .custom-card-one(@card-box-shadow-two, @border-radius-8, @light-gray);
}

.card-five {
    //// White #ffff / 8px / box-shadow: 0px 1px 4px 0px #18100A14;
    //// forms cards / 
    .custom-card-one(@card-box-shadow-two);
}

.card-six {
    //// tabs / white / radius 22px 
    .custom-card-one(@box-shadow-three, @border-radius-22)
}

.card-seven {
    //// tabs / white / 0px 2px 4px 0px #18100A14 / radius 10px
    .custom-card-one(@box-shadow-five, @border-radius-10)
}

.card-eight {
    //// #FBFBFB / 8px / box-shadow: 0px 1px 4px 0px #18100A14;
    .custom-card-one(@card-box-shadow-two, @border-radius-8, @light-gray);
}

.card-collapsible {
    display: flex;
    flex-direction: column;
    .custom-card-one(@card-box-shadow-two);

    .header {
        .card-collapsible-header;
    }

    .body {
        .card-collapsible-body;
    }
}

.card-collapsible-header {
    background-color: @white;
    border-bottom: 1px solid @soft-gray;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
    min-height: 52px;
    .p-3_5;

    &.collapsed {
        border-radius: 12px;
        border-bottom: none;
    }

    .title {
        font-size: 16px;
        color: @black;
        font-weight: 600;
    }

    .arrow {
        width: 14px;
        height: 24px;
        cursor: pointer;
    }

}

.card-collapsible-body {
    padding: var(--card-collapsible-body-p);
    background-color: @light-gray;
    border-radius: inherit;
    height: 100%;
}

.priority-peach-purple-color {
    font-size: 14px;
    font-weight: bold;
    color: @purple;
    background-color: @Warm-peach-glow;
}