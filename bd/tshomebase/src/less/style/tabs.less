@import "../utils/gap.less";

.tab-bar-lvl-one {
  background-color: @transparent;
  display: flex;
  flex-direction: row;
  gap: var(--l1-tab-list-button-gap);
  height: var(--tab-lvl-1-tmp1-h);
}

.tab-bar-lvl-three {
  margin-bottom: 0;
  height: 34px;
}

.tab-list-button-common {
  font-weight: 500;
  font-size: 14px;
  color: #5E636B;
  padding: 8px;
  gap: var(--tab-list-button-common-gap);
  border-radius: 12px;

  &:hover {
    transform: scale(1);
  }

  .tab-close-btn {
    cursor: pointer;
    display: flex;

    s img {
      width: 10px;
      height: 10px;
    }
  }

  .tab-counter {
    .count {
      background-color: #888888; // Bubble background color
      color: white; // Text color inside the bubble
      font-size: 11px; // Adjust font size as needed
      font-weight: bold; // Make the text bold
      border-radius: 50%; // Create the circular shape
      padding: 2px 6px; // Padding for the bubble
      display: flex;
      min-width: 20px;
      align-items: center;
      justify-content: center;
      overflow-y: visible;
    }
  }

  &.tab-active {
    background-color: var(--color-tertiary);
    color: #fff;
    border-radius: 8px;
    border-top: 1px solid #FAFAFAE5;
    box-shadow:
      0px -1px 2px 0px #00000038 inset,
      0px 1px 2px 0px #FFFFFF1F inset;
    font-weight: 700;
    gap: 8px;

    .tab-counter {
      .count {
        background-color: @purple; // Bubble background color
        color: white; // Text color inside the bubble
      }
    }

    &:hover {
      transform: scale(1);
      cursor: default;
    }

    .tab-label {
      font-size: 14px;

      &:hover {
        cursor: default;
      }
    }
  }

}

.tab-item-lvl-one {
  background-color: @semi-transparent-light-gray-04;
  border-radius: @border-radius-12;
  min-height: var(--l1-tab-list-min-height);
  font-size: @font-size-18;

  &.tab-active {
    .card-six;
    background-color: var(--color-tertiary);
  }
}

.tab-item-lvl-two {

  &.tab-active {
    background-color: var(--color-tertiary);

    &:first-child {
      margin-left: 3px;
    }

    &:last-child {
      margin-right: 3px;
    }

    .tab-label {
      font-size: 14px;
    }
  }
}

.tab-item-lvl-three {
  .para-three;
  padding: var(--l3-tab-list-button-p);
  background-color: @purple-08;
  border: 1px solid @soft-gray;

  // &.no-open-tabs {
  //   border-top-right-radius: @border-radius-8;
  // }

  &.has-tab-form {
    border-radius: @border-radius-8;
  }

  &.tab-active {
    // background-color: @white;
    background-color: var(--color-tertiary);
    border-bottom-left-radius: inherit;
  }

}