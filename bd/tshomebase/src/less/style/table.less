@import "./text.less";
@import "./common.less";
@import "../utils/vars/common.less";

.table-one {
}

.table-status-filter-header {
  .card-five;
  padding: @padding-5;
  padding-left: @padding-10;
  padding-right: @padding-10;
  gap: 10px;
}

.table-status-text {
  .para-two;
  font-weight: 500;
  color: @black;
}

.table-status-count {
  .centered-col;
  border-radius: @border-radius-full;
  padding: @padding-5;
  height: 28px;
  // width: 28px;

  span {
    .para-five;
    font-weight: 500;
  }
}
.table-status-text-2 {
  font-weight: var(--font-weight-semibold) !important;
  font-size: var(--font-size-xsmall) !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-status-text-span {
  color: #414651;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-xsmall);
  padding: 2px 8px 2px 8px;

}

.table-header {
  background-color: @lighter-gray;
  border-top-left-radius: @border-radius-8;
  border-top-right-radius: @border-radius-8;
  .heading-four;
  font-weight: 500;
  color: @charcoal-gray;
}

.table-body {
  .heading-four;
  font-weight: 500;
  color: @black;

  tr {
    border-bottom: 1px solid @soft-gray;
    background-color: @white;

    td {
      padding: var(--dt-td-padding);
    }
  }
}

.table-paginatin-text {
  .para-two;
  font-weight: 400;
  color: @semi-transparent-light-brown-5;
}

.table-pagination-input-text-field {
  color: @semi-transparent-light-brown-5;
  background-color: @white;
  font-weight: 400;
  .para-six;
}
