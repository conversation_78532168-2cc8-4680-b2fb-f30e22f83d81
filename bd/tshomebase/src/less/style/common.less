@import '../utils/mixins.less';
@import (reference) '../utils/vars/common.less';

.ellipsis-style {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // display: inline-block;
}

.centered-col {
    .flex-justify-center()
}

.centered-row {
    .flex-justify-center(row)
}


.hover-icon {
    &:hover {
        transform: scale(1.3)
    }
}

.default-scroll {

    &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0);
        border-radius: 4px;
    }

    &:hover {

        &::-webkit-scrollbar-thumb {
            background-color: lightgrey;
        }
    }

}