/* Padding all sides */
.p-0 {
  padding: 0;
}

.p-0_25 {
  padding: 1px;
}

.p-0_5 {
  padding: 2px;
}

.p-1 {
  padding: 4px;
}

.p-1_5 {
  padding: 6px;
}

.p-2 {
  padding: 8px;
}

.p-2_5 {
  padding: 10px;
}

.p-3 {
  padding: 12px;
}

.p-3_5 {
  padding: 14px;
}

.p-4 {
  padding: 16px;
}

.p-5 {
  padding: 20px;
}

.p-6 {
  padding: 24px;
}

.p-7 {
  padding: 28px;
}

.p-8 {
  padding: 32px;
}

.p-9 {
  padding: 36px;
}

.p-10 {
  padding: 40px;
}

.p-11 {
  padding: 44px;
}

.p-12 {
  padding: 48px;
}

.p-14 {
  padding: 56px;
}

.p-16 {
  padding: 64px;
}

.p-20 {
  padding: 80px;
}

/* Padding X-axis */
.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-0_25 {
  padding-left: 1px;
  padding-right: 1px;
}

.px-0_5 {
  padding-left: 2px;
  padding-right: 2px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-1_5 {
  padding-left: 6px;
  padding-right: 6px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.px-2_5 {
  padding-left: 10px;
  padding-right: 10px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.px-3_5 {
  padding-left: 14px;
  padding-right: 14px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.px-5 {
  padding-left: 20px;
  padding-right: 20px;
}

.px-6 {
  padding-left: 24px;
  padding-right: 24px;
}

.px-7 {
  padding-left: 28px;
  padding-right: 28px;
}

.px-8 {
  padding-left: 32px;
  padding-right: 32px;
}

.px-9 {
  padding-left: 36px;
  padding-right: 36px;
}

.px-10 {
  padding-left: 40px;
  padding-right: 40px;
}

.px-11 {
  padding-left: 44px;
  padding-right: 44px;
}

.px-12 {
  padding-left: 48px;
  padding-right: 48px;
}

.px-14 {
  padding-left: 56px;
  padding-right: 56px;
}

.px-16 {
  padding-left: 64px;
  padding-right: 64px;
}

.px-20 {
  padding-left: 80px;
  padding-right: 80px;
}

/* Padding Y-axis */
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.py-0_25 {
  padding-top: 1px;
  padding-bottom: 1px;
}

.py-0_5 {
  padding-top: 2px;
  padding-bottom: 2px;
}

.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.py-1_5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2_5 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-3_5 {
  padding-top: 14px;
  padding-bottom: 14px;
}

.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.py-5 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.py-6 {
  padding-top: 24px;
  padding-bottom: 24px;
}

.py-7 {
  padding-top: 28px;
  padding-bottom: 28px;
}

.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.py-9 {
  padding-top: 36px;
  padding-bottom: 36px;
}

.py-10 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.py-11 {
  padding-top: 44px;
  padding-bottom: 44px;
}

.py-12 {
  padding-top: 48px;
  padding-bottom: 48px;
}

.py-14 {
  padding-top: 56px;
  padding-bottom: 56px;
}

.py-16 {
  padding-top: 64px;
  padding-bottom: 64px;
}

.py-20 {
  padding-top: 80px;
  padding-bottom: 80px;
}

/* Padding Bottom */
.pb-0 {
  padding-bottom: 0;
}

.pb-0_25 {
  padding-bottom: 1px;
}

.pb-0_5 {
  padding-bottom: 2px;
}

.pb-1 {
  padding-bottom: 4px;
}

.pb-1_5 {
  padding-bottom: 6px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pb-2_5 {
  padding-bottom: 10px;
}

.pb-3 {
  padding-bottom: 12px;
}

.pb-3_5 {
  padding-bottom: 14px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pb-6 {
  padding-bottom: 24px;
}

.pb-7 {
  padding-bottom: 28px;
}

.pb-8 {
  padding-bottom: 32px;
}

.pb-9 {
  padding-bottom: 36px;
}

.pb-10 {
  padding-bottom: 40px;
}

.pb-11 {
  padding-bottom: 44px;
}

.pb-12 {
  padding-bottom: 48px;
}

.pb-14 {
  padding-bottom: 56px;
}

.pb-16 {
  padding-bottom: 64px;
}

.pb-20 {
  padding-bottom: 80px;
}

/* Padding Left */
.pl-0 {
  padding-left: 0;
}

.pl-0_25 {
  padding-left: 1px;
}

.pl-0_5 {
  padding-left: 2px;
}

.pl-1 {
  padding-left: 4px;
}

.pl-1_5 {
  padding-left: 6px;
}

.pl-2 {
  padding-left: 8px;
}

.pl-2_5 {
  padding-left: 10px;
}

.pl-3 {
  padding-left: 12px;
}

.pl-3_5 {
  padding-left: 14px;
}

.pl-4 {
  padding-left: 16px;
}

.pl-5 {
  padding-left: 20px;
}

.pl-6 {
  padding-left: 24px;
}

.pl-7 {
  padding-left: 28px;
}

.pl-8 {
  padding-left: 32px;
}

.pl-9 {
  padding-left: 36px;
}

.pl-10 {
  padding-left: 40px;
}

.pl-11 {
  padding-left: 44px;
}

.pl-12 {
  padding-left: 48px;
}

.pl-14 {
  padding-left: 56px;
}

.pl-16 {
  padding-left: 64px;
}

.pl-20 {
  padding-left: 80px;
}

/* Padding Right */
.pr-0 {
  padding-right: 0;
}

.pr-0_25 {
  padding-right: 1px;
}

.pr-0_5 {
  padding-right: 2px;
}

.pr-1 {
  padding-right: 4px;
}

.pr-1_5 {
  padding-right: 6px;
}

.pr-2 {
  padding-right: 8px;
}

.pr-2_5 {
  padding-right: 10px;
}

.pr-3 {
  padding-right: 12px;
}

.pr-3_5 {
  padding-right: 14px;
}

.pr-4 {
  padding-right: 16px;
}

.pr-5 {
  padding-right: 20px;
}

.pr-6 {
  padding-right: 24px;
}

.pr-7 {
  padding-right: 28px;
}

.pr-8 {
  padding-right: 32px;
}

.pr-9 {
  padding-right: 36px;
}

.pr-10 {
  padding-right: 40px;
}

.pr-11 {
  padding-right: 44px;
}

.pr-12 {
  padding-right: 48px;
}

.pr-14 {
  padding-right: 56px;
}

.pr-16 {
  padding-right: 64px;
}

.pr-20 {
  padding-right: 80px;
}

/* Padding Top */
.pt-0 {
  padding-top: 0;
}

.pt-0_25 {
  padding-top: 1px;
}

.pt-0_5 {
  padding-top: 2px;
}

.pt-1 {
  padding-top: 4px;
}

.pt-1_5 {
  padding-top: 6px;
}

.pt-2 {
  padding-top: 8px;
}

.pt-2_5 {
  padding-top: 10px;
}

.pt-3 {
  padding-top: 12px;
}

.pt-3_5 {
  padding-top: 14px;
}

.pt-4 {
  padding-top: 16px;
}

.pt-5 {
  padding-top: 20px;
}

.pt-6 {
  padding-top: 24px;
}

.pt-7 {
  padding-top: 28px;
}

.pt-8 {
  padding-top: 32px;
}

.pt-9 {
  padding-top: 36px;
}

.pt-10 {
  padding-top: 40px;
}

.pt-11 {
  padding-top: 44px;
}

.pt-12 {
  padding-top: 48px;
}

.pt-14 {
  padding-top: 56px;
}

.pt-16 {
  padding-top: 64px;
}

.pt-20 {
  padding-top: 80px;
}

.compact {

  /* Padding all sides */
  .p-0 {
    padding: 0;
  }

  .p-0_25 {
    padding: 0.5px;
  }

  .p-0_5 {
    padding: 1px;
  }

  .p-1 {
    padding: 2px;
  }

  .p-1_5 {
    padding: 3px;
  }

  .p-2 {
    padding: 4px;
  }

  .p-2_5 {
    padding: 5px;
  }

  .p-3 {
    padding: 6px;
  }

  .p-3_5 {
    padding: 7px;
  }

  .p-4 {
    padding: 8px;
  }

  .p-5 {
    padding: 10px;
  }

  .p-6 {
    padding: 12px;
  }

  .p-7 {
    padding: 14px;
  }

  .p-8 {
    padding: 16px;
  }

  .p-9 {
    padding: 18px;
  }

  .p-10 {
    padding: 20px;
  }

  .p-11 {
    padding: 22px;
  }

  .p-12 {
    padding: 24px;
  }

  .p-14 {
    padding: 28px;
  }

  .p-16 {
    padding: 32px;
  }

  .p-20 {
    padding: 40px;
  }

  /* Padding X-axis */
  .px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .px-0_25 {
    padding-left: 0.5px;
    padding-right: 0.5px;
  }

  .px-0_5 {
    padding-left: 1px;
    padding-right: 1px;
  }

  .px-1 {
    padding-left: 2px;
    padding-right: 2px;
  }

  .px-1_5 {
    padding-left: 3px;
    padding-right: 3px;
  }

  .px-2 {
    padding-left: 4px;
    padding-right: 4px;
  }

  .px-2_5 {
    padding-left: 5px;
    padding-right: 5px;
  }

  .px-3 {
    padding-left: 6px;
    padding-right: 6px;
  }

  .px-3_5 {
    padding-left: 7px;
    padding-right: 7px;
  }

  .px-4 {
    padding-left: 8px;
    padding-right: 8px;
  }

  .px-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .px-6 {
    padding-left: 12px;
    padding-right: 12px;
  }

  .px-7 {
    padding-left: 14px;
    padding-right: 14px;
  }

  .px-8 {
    padding-left: 16px;
    padding-right: 16px;
  }

  .px-9 {
    padding-left: 18px;
    padding-right: 18px;
  }

  .px-10 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .px-11 {
    padding-left: 22px;
    padding-right: 22px;
  }

  .px-12 {
    padding-left: 24px;
    padding-right: 24px;
  }

  .px-14 {
    padding-left: 28px;
    padding-right: 28px;
  }

  .px-16 {
    padding-left: 32px;
    padding-right: 32px;
  }

  .px-20 {
    padding-left: 40px;
    padding-right: 40px;
  }

  /* Padding Y-axis */
  .py-0 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .py-0_25 {
    padding-top: 0.5px;
    padding-bottom: 0.5px;
  }

  .py-0_5 {
    padding-top: 1px;
    padding-bottom: 1px;
  }

  .py-1 {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .py-1_5 {
    padding-top: 3px;
    padding-bottom: 3px;
  }

  .py-2 {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .py-2_5 {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .py-3 {
    padding-top: 6px;
    padding-bottom: 6px;
  }

  .py-3_5 {
    padding-top: 7px;
    padding-bottom: 7px;
  }

  .py-4 {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .py-5 {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .py-6 {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .py-7 {
    padding-top: 14px;
    padding-bottom: 14px;
  }

  .py-8 {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .py-9 {
    padding-top: 18px;
    padding-bottom: 18px;
  }

  .py-10 {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .py-11 {
    padding-top: 22px;
    padding-bottom: 22px;
  }

  .py-12 {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .py-14 {
    padding-top: 28px;
    padding-bottom: 28px;
  }

  .py-16 {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .py-20 {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}