@import (reference) '../../style/main.less';

// Black Shades
@black: #111111;
@dark-black: #000000;

// Orange
@orangish: #FFAA5B;

// Red Shades 
@brick-red: #AE4D4D;
@coral-red: #D26158;
@reddish: #f56d63;

// Brown Shades
@brown: #f8f1ec;
@charcoal-gray: #5a5958;
@copper-brown: #b97e4c;
@rustic-brown: #B97E4CBF;

// Gray Shades
@dark-gray: #58505b;
@soft-dark-gray: #58505B1F;
@gray: #595a5a;
@soft-light-gray: rgba(89, 90, 90, 0.5); //// #595A5A80
@light-soft-gray: #f4f4f4;
@light-gray: #fbfbfb;
@lighter-gray: #f2f2f2;
@midnight-gray: #333;
@mid-gray: #888888;
@pale-gray: #e6e6e6;
@soft-gray: #dbdbdd;
@charcoal-gray: #595959; // Assuming value


// Lavender Purple Shades
@purple: #9974ce;
@purple-light: #cab0ee;
@purple-08: rgba(153, 116, 206, 0.08);
@semi-transparent-purple-4: rgba(153, 116, 206, 0.4);


// Light Pink Shades
@light-pink: #f7f4f4;
@cloud-white: #fcfcfc;

// pearl
@soft-pearl: rgb(241, 233, 228);

// Peach
@Warm-peach-glow : #f1b895b3;

// Sky Blue
@sky-blue: #17b3ce;
@aqua-blue: #18ADC5;
// Sky Blue (with opacity) Shade
@semi-transparent-sky-blue-08: rgba(23, 179, 206, 0.08);
@bright-cyan-blue: #18adc6;

// Transparent Shade
@transparent: transparent;

// White Shades
@white: #ffffff;
@off-white: #eeedee;
@bright-teal: #17B3CEBF;

// Light Gray (with opacity) Shades
@semi-transparent-light-gray: rgba(238, 237, 238, 0.5);
@semi-transparent-light-gray-25: rgba(88, 80, 91, 0.25);
@semi-transparent-light-gray-04: rgba(88, 80, 91, 0.04);
@semi-transparent-light-gray-12: rgba(88, 80, 91, 0.12);
@semi-transparent-light-gray-08: rgba(88, 80, 91, 0.08); //// #58505B14 


// Light Brown (with opacity) Shades
@semi-transparent-light-brown-5: rgba(90, 89, 88, 0.5); //// #5A595880

@semi-transparent-light-brown-1: rgba(51, 36, 35, 0.1);

@semi-transparent-light-brown-75: rgba(164, 156, 159, 0.75);

@semi-transparent-light-gray-25-five: #ebf9fb;
@semi-transparent-light-gray-25-six: #9974cf;
@semi-transparent-light-gray-25-seven: #dddddd; //// box border


//// Font Sizes ////

@font-size-32: var(--font-size-32);
@font-size-24: var(--font-size-24);
@font-size-18: var(--font-size-18);
@font-size-16: var(--font-size-16);
@font-size-14: var(--font-size-14);
@font-size-13: var(--font-size-13);
@font-size-10: var(--font-size-10);
@font-size-12: var(--font-size-12);
@font-size-15: var(--font-size-15);


//// Line Height ////
@line-height-36: var(--line-height-36);
@line-height-24: var(--line-height-24);
@line-height-13: var(--line-height-13);
@line-height-28: var(--line-height-28);
@line-height-16: var(--line-height-16);
@line-height-14: var(--line-height-14);
@line-height-32: var(--line-height-32);


//// Box Shadow ////
@box-shadow-one: 0px 1px 4px 0px rgba(26, 37, 53, 0.12); ////  #1A25351F /     ////
@box-shadow-two: 0px 1px 4px 0px rgba(24, 16, 10, 0.08); //// #18100A14 /    ////
@box-shadow-three: 0px 4px 8px 0px rgba(24, 16, 10, 0.08); ////  #35201714 /    ////
@box-shadow-four: 4px 1px 4px 0px rgba(24, 16, 10, 0.08); //// #18100A14 /    ////
@box-shadow-five: 0px 2px 4px 0px rgba(24, 16, 10, 0.08); //// #18100A14 /    ////
@box-shadow-six: 0px 0px 8px 1px rgba(24, 16, 10, 0.3); //// find-view-popup



// use the paddings from the utilty instead: replacement -> @padding-one: 8px 16px; .py2;.px4;
@padding-5-10: 5px 10px;
@padding-10-20: 10px 20px; /// lvl-3-tab-list item / lvl-2-tab-list item
@padding-2-5: 2px 5px;
@padding-10-15: 10px 15px;
@padding-10: 10px;
@padding-5: 5px;

@margin-10-0: 10px 0px;

@border-radius-10: 12px; //// cards
@border-radius-6: 12px; //// icon box
@border-radius-12: 12px; //// cards / flyout drawer
@border-radius-full: 50%; //// circles icons / profile photo
@border-radius-8: 12px; /// tab-lvl-3 /
@border-radius-22: 12px; /// field / tab item / pills
@border-radius-28: 12px; //// drawer
@border-radius-4: 12px; //// checkbox
@border-radius-common-12: 12px;

//// Transition Time /////
@transition-time-one: 0.5s;

//// Animation ////
@initial-common-animation-duration: 0.5s;


//// Icons ////
@fa-var-exclamation-triangle: "\f071";

//// height /////
@field-height-standard: 30px;