@import (reference) './vars/common.less';
@import (reference) './padding.less';
@import (reference) '../utils/vars/dimensions.less';

.flex-justify-center(@direction: column, @justifyContent: center) {
    display: flex;
    flex-direction: @direction;
    justify-content: @justifyContent;
    align-items: center;
}

.pills-secondary {
    .py-0;
    .px-2_5;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    align-items: center;
    cursor: pointer;
}

.custom-text-one (@fontSize: @font-size-14, @lineHeight: @line-height-24) {
    //// default values are @fontSize: 14px and @lineHeight: 24px

    font-size: @fontSize;
    line-height: @lineHeight;

}

.custom-card-one (@boxShadow: none, @borderRadius: @border-radius-12, @bgColor: @white) {
    //// intial values: shadow: none, border-radius: 12px and color: white 
    border-radius: @borderRadius;
    box-shadow: @boxShadow;
    background-color: @bgColor;
}


.custom-button-variant(@background; @color; @fontSize: @font-size-13; @lineHeight: @line-height-16; @borderRadius: @border-radius-8; @border: 0px solid transparent; @padding: var(--btn-primary-p); @fontWeight: 500) {
    color: @color;
    background-color: @background;
    border: @border;
    font-weight: @fontWeight;
    padding: @padding;
    border-radius: @borderRadius;
    font-size: @fontSize;
    line-height: @lineHeight;

    &:hover,
    &:focus,
    &.active,
    .open>.dropdown-toggle& {
        color: @color;
        .darken(@background, 10%, background-color);
        border: @border;
        border-radius: @borderRadius;
    }

    &.active,
    .open>.dropdown-toggle& {
        background-image: none;
    }

    &.disabled,
    &[disabled],
    fieldset[disabled] & {

        &,
        &:hover,
        &:focus,
        &.active {
            .darken(@background, 20%, background-color);
            border: @border;
        }
    }

    .badge {
        color: @background;
        background-color: @color;
    }
}

.darken(@color, @darken-percent, @property) {

    // Check if the color is a HEX color with opacity
    .hex {
        & when (iscolor(@color)=false) and (extract(@color, 1)='#') {
            // Convert HEX color with opacity to RGBA
            @rgba-color: fade(@color, 1);
            // Darken the color
            @darkened-color: darken(@rgba-color, @darken-percent);
            // Apply the darkened color
            @{property}: @darkened-color;
        }
    }

    // Handle other color formats
    & when not (iscolor(@color)=false) {
        // Directly apply darken for other color formats
        @darkened-color: darken(@color, @darken-percent);
        @{property}: @darkened-color;
    }
}


.vendorPrefix(@property, @value) {
    //////// Uses Examples /////////

    // .vendorPrefix(animation-name, slideHeight);
    // .vendorPrefix(animation-duration, 1s);
    // .vendorPrefix(animation-timing-fusnction, ease);
    // .vendorPrefix(animation-fill-mode, forwards);

    // .vendorPrefix(border-radius, 10px);
    // .vendorPrefix(box-shadow, 0 0 10px rgba(0, 0, 0, 0.5));
    // .vendorPrefix(transform, rotate(45deg));
    // .vendorPrefix(transition, all 0.3s ease);
    // .vendorPrefix(animation, slide-in 1s infinite);
    // .vendorPrefix(background, linear-gradient(to right, red, blue));
    // .vendorPrefix(display, flex);
    // .vendorPrefix(flex-direction, column);
    // .vendorPrefix(justify-content, center);
    // .vendorPrefix(align-items, center);
    // .vendorPrefix(user-select, none);
    // .vendorPrefix(background-clip, padding-box);
    // .vendorPrefix(column-count, 3);
    // .vendorPrefix(column-gap, 15px);
    // .vendorPrefix(filter, blur(5px));
    // .vendorPrefix(backdrop-filter, blur(5px));
    // .vendorPrefix(clip-path, circle(50%));
    // .vendorPrefix(appearance, none);
    // .vendorPrefix(box-sizing, border-box);
    // .vendorPrefix(display, grid);
    // .vendorPrefix(mask-image, linear-gradient(to right, black, transparent));
    // .vendorPrefix(object-fit, cover);
    // .vendorPrefix(text-decoration, underline);
    // .vendorPrefix(hyphens, auto);

    @{property}: @value;
    -webkit-@{property}: @value;
    -moz-@{property}: @value;
    -o-@{property}: @value;
}


//--------------------------------------------------------------------------------------------------------//



// Mixin for slideHeight
.slideHeight(@fromMaxHeight: 0, @toMaxHeight: 100vh, @animationDuration: @initial-common-animation-duration) {
    //// Uses Examples ////

    // .slideHeight();
    // .slideHeight(0px, 100vh);
    // .slideHeight(0px, 100vh);
    // .slideHeight(0px, 1000px);

    .slideHeightKeyframes(@fromMaxHeight, @toMaxHeight);

    overflow: hidden;
    max-height: @toMaxHeight;

    .vendorPrefix(animation, slideHeight @animationDuration);
}

// Mixin for slideWidth
.slideWidth(@fromWidth: 0, @toWidth: "inherit", @animationDuration: @initial-common-animation-duration) {
    //// Uses Examples ////
    // .slideWidth();
    // .slideWidth(0px, "inherit", 2s);
    // .slideWidth(0px, 500px, 1s);

    .slideWidthKeyframes(@fromWidth, @toWidth);

    width: @toWidth;
    overflow: hidden;

    .vendorPrefix(animation, slideWidth @animationDuration);
}

.slideHeightWidth(@fromMaxHeight: 0, @toMaxHeight: 100vh, @animationDurationHeight: 1s, @fromWidth: 0, @toWidth: "inherit", @animationDurationWidth: 1s) {
    //// Uses Examples ////
    // .slideHeightWidth(0, 100vh, 2s, 0, 'inherit', 1s);
    // .slideHeightWidth();

    .slideHeightKeyframes(@fromMaxHeight, @toMaxHeight);
    .slideWidthKeyframes(@fromWidth, @toWidth);

    overflow: hidden;
    width: @toWidth;
    max-height: @toMaxHeight;

    -webkit-animation: slideHeight @animationDurationHeight, slideWidth @animationDurationWidth;
    -moz-animation: slideHeight @animationDurationHeight, slideWidth @animationDurationWidth;
    -o-animation: slideHeight @animationDurationHeight, slideWidth @animationDurationWidth;
    animation: slideHeight @animationDurationHeight, slideWidth @animationDurationWidth;

}

// Mixin for fadeIn
.fadeIn(@animationDuration: @initial-common-animation-duration, @toDisplay: flex) {
    @-webkit-keyframes fadeIn {
        from {
            opacity: 0;
            display: none;
        }

        to {
            opacity: 1;
            display: @toDisplay;
        }
    }

    @-moz-keyframes fadeIn {
        from {
            opacity: 0;
            display: none;
        }

        to {
            opacity: 1;
            display: @toDisplay;
        }
    }

    @-o-keyframes fadeIn {
        from {
            opacity: 0;
            display: none;
        }

        to {
            opacity: 1;
            display: @toDisplay;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            display: none;
        }

        to {
            opacity: 1;
            display: @toDisplay;
        }
    }

    opacity: 1;
    display: @toDisplay;

    .vendorPrefix(animation, fadeIn @animationDuration);
}


.fadeInLoop(@animationDuration: @initial-common-animation-duration, @num-children: 20) {
    //// Uses Examples ////
    // .fadeInLoop(0.2s, 15);        

    .loop(@i) when (@i > 0) {
        &:nth-child(@{i}) {
            .fadeIn((@i * @animationDuration));
        }

        .loop(@i - 1);
    }

    .loop(@num-children);
}


// Mixin for fadeOut
.fadeOut(@animationDuration: @initial-common-animation-duration) {
    @-webkit-keyframes fadeOut {
        from {
            opacity: 1;
            display: block;
        }

        to {
            opacity: 0;
            display: none;
        }
    }

    @-moz-keyframes fadeOut {
        from {
            opacity: 1;
            display: block;
        }

        to {
            opacity: 0;
            display: none;
        }
    }

    @-o-keyframes fadeOut {
        from {
            opacity: 1;
            display: block;
        }

        to {
            opacity: 0;
            display: none;
        }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
            display: block;
        }

        to {
            opacity: 0;
            display: none;
        }
    }

    opacity: 0;
    display: block;

    .vendorPrefix(animation, fadeOut @animationDuration)
}



////////////////////////////////////////////////////////
///////////////// Common Animation End /////////////////
////////////////////////////////////////////////////////


//--------------------------------------------------------------------------------------------------------//

////////////////////////////////////////////////////////
///////////////// Mixins helpers Start /////////////////
////////////////////////////////////////////////////////


.slideHeightKeyframes(@fromMaxHeight, @toMaxHeight) {
    @-webkit-keyframes slideHeight {
        from {
            max-height: @fromMaxHeight;
        }

        to {
            max-height: @toMaxHeight;
        }
    }

    @-moz-keyframes slideHeight {
        from {
            max-height: @fromMaxHeight;
        }

        to {
            max-height: @toMaxHeight;
        }
    }

    @-o-keyframes slideHeight {
        from {
            max-height: @fromMaxHeight;
        }

        to {
            max-height: @toMaxHeight;
        }
    }

    @keyframes slideHeight {
        from {
            max-height: @fromMaxHeight;
        }

        to {
            max-height: @toMaxHeight;
        }
    }
}

// Mixin for defining keyframes for width animation
.slideWidthKeyframes(@fromWidth: 0px, @toWidth: "inherit") {
    @-webkit-keyframes slideWidth {
        from {
            width: @fromWidth;
        }

        to {
            width: @toWidth;
        }
    }

    @-moz-keyframes slideWidth {
        from {
            width: @fromWidth;
        }

        to {
            width: @toWidth;
        }
    }

    @-o-keyframes slideWidth {
        from {
            width: @fromWidth;
        }

        to {
            width: @toWidth;
        }
    }

    @keyframes slideWidth {
        from {
            width: @fromWidth;
        }

        to {
            width: @toWidth;
        }
    }
}

////////////////////////////////////////////////////////
////////////////// Mixins helpers End //////////////////
////////////////////////////////////////////////////////

//--------------------------------------------------------------------------------------------------------//