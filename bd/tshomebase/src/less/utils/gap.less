/* Standard gap settings */
.gap-0 {
  gap: 0;
}

.gap-px {
  gap: 1px;
}

.gap-0_5 {
  gap: 2px;
}

.gap-1 {
  gap: 4px;
}

.gap-1_5 {
  gap: 6px;
}

.gap-2 {
  gap: 8px;
}

.gap-2_5 {
  gap: 10px;
}

.gap-3 {
  gap: 12px;
}

.gap-3_5 {
  gap: 14px;
}

.gap-4 {
  gap: 16px;
}

.gap-5 {
  gap: 20px;
}

.gap-6 {
  gap: 24px;
}

.gap-7 {
  gap: 28px;
}

.gap-8 {
  gap: 32px;
}

.gap-9 {
  gap: 36px;
}

.gap-10 {
  gap: 40px;
}

/* Compact gap settings under the .compact class */
.compact {
  .gap-0_5 {
    gap: 1px;
  }

  .gap-1 {
    gap: 2px;
  }

  .gap-1_5 {
    gap: 3px;
  }

  .gap-2 {
    gap: 4px;
  }

  .gap-2_5 {
    gap: 5px;
  }

  .gap-3 {
    gap: 6px;
  }

  .gap-3_5 {
    gap: 7px;
  }

  .gap-4 {
    gap: 8px;
  }

  .gap-5 {
    gap: 10px;
  }

  .gap-6 {
    gap: 12px;
  }

  .gap-7 {
    gap: 14px;
  }

  .gap-8 {
    gap: 16px;
  }

  .gap-9 {
    gap: 18px;
  }

  .gap-10 {
    gap: 20px;
  }

  .gap-px {
    gap: 0.5px;
  }
}