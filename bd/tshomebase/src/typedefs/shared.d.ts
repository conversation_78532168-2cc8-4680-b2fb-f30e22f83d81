import NavIcon from "@public/icons/nav";

interface RemoteModuleItem {
    label: string;
    name: string;
    code: keyof typeof NavIcon;
    key: string;
    icon?: string;
    path: string;
    active: string;
    placement: "top" | "bottom";
    nav_placement: "top" | "bottom";
    sort_order: number;
}

interface ModuleItem {
    label: string;
    name: string;
    key: string;
    code: string;
    icon: string;
    activeIcon: string;
    path: string;
    placement: "top" | "bottom";
    sort_order: number;
}