import { CLJQueryObject } from "@typedefs/window";
import type { <PERSON><PERSON>ield } from "./dsl";

export interface IDSLDraw {
	attach_data(): void;
	clear(): void;
	default(k: string, v: DSLField): any;
	default_params(join?: boolean, max_rows_override?: number): string[];
	draw_base_field(form: any, view: any, fc: any, fx: any, id: any, k: any, v: any, df: any): any;
	get_field_control(v: any): string;
	initialize(options: any): void;
	load(): void;
	make_row_html(row: any, cols: any, fields: any, opts?: any): any;
	offscreen_field(k: string): void;
	parse_template_field(template: string, type: string): any;
	toggle_subform(show?: boolean, force?: boolean): void;
	unload(): void;
	value_field(k: string, n?: any, allow_null?: boolean, allow_hidden?: boolean, ignore_if?: boolean): any;
	value_field_get(k: string, ignore_if?: boolean): any;
	value_field_fixed(k: string, v: any): any;
	value_parse(v: any, vl: any, opt: any): any;
	values(opt?: any): any;
}

export type FieldSubFormEventInfo =
	| {
			user_event: boolean;
			event: "update" | "add" | "delete";
			row_index: number; // row index in value_get_dirty
			field: null;
	  }
	| {
			user_event: boolean;
			event: "field_change";
			row_index: number; // row index in value_get_dirty
			field: string;
	  }
	| undefined;

export interface IDSLDrawSubForm extends IDSLDraw {
	options: any;
	preset: Record<string, unknown>;
	fields: Record<string, any>;
	field_map: Record<string, CLJQueryObject[]>;
	field_nodes: Record<string, CLJQueryObject>;
	sections: Record<string, CLJQueryObject>;
	section_fields: Record<string, any[]>;
	section_map: Record<string, any>;
	section_tab_map: Record<string, any>;
	shmap: {
		fields: Record<string, any>;
		sections: Record<string, any>;
	};
	rxFieldOpts: Record<string, any>;
	readonly_map: Record<string, Record<string, number>>;
	required_if_map: Record<string, Record<string, number>>;
	has_changed: (cm: string, df: string, with_value?: boolean) => boolean;
	compare_field(k: string): any;
	get_subform_parent(): any;
	draw_field(k: string, vs: any, sec: string): any;
	field_node_value(v: any, d: any): any;
	get_parent_form: () => string | undefined;
	if_by: string[];
	get_field_in_parent: () => string | undefined;
	get_container_fieldinline(d: any, k: string, v: any, fx: string, cm: any, df: any, id: string, sec: string): any;
	get_container_field(d: any, k: string, v: any, fx: string, cm: any, df: any, id: string, sec: string): any;
	get_container_grid(d: any, k: string, v: any, fx: string, cm: any, df: any, id: string, sec: string): any;
	get_record_data(k?: string): any;
	has_new_value(cm: any, df: any): boolean;
	load(): any;
	load_handler(dfd: any): any;
	note(f: any, tx: any): any;
	section_check(s: any): any;
	section_check_all(): any;
	set_record(drs: any): any;
	set_section_map(id: any, k: any, data?: any): any;
	toggle_field(k?: any, show?: boolean): any;
	toggle_section(k?: any, show?: boolean): any;
	toggle_subforms(show?: boolean): any;
	update_section_tabs(): any;
}
