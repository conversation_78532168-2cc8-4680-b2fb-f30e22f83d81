export interface DSLDrawLinkMap {
	link: string;
	links: string[];
	linkid: Record<string, number | string>;
}

export type DSLModeTypes = "add" | "edit" | "addfill" | "read";
export type DSLStructTypes = "field" | "section_group" | "section_group_note" | "section" | "section_note";
export type DSLFieldModelTypes =
	| "date"
	| "datetime"
	| "decimal"
	| "image"
	| "int"
	| "json"
	| "password"
	| "subform"
	| "text"
	| "time";
export type DSLSubFieldTypes =
	| "date"
	| "datenow"
	| "datetime"
	| "decimal"
	| "int"
	| "text"
	| "time"
	| "timenow"
	| "timestamp"
	| "checkbox";
export type DSLFieldTypes =
	| "area"
	| "checkbox"
	| "esign"
	| "file"
	| "grid"
	| "inline"
	| "input"
	| "link"
	| "radio"
	| "picker"
	| "select"
	| "subform"
	| "tree";
export type DSLFieldFormatTypes = "" | "hic" | "ssn" | "us_phone" | "us_zip";

export interface DSLForm {
	fields: Record<string, DSLField>;
	model: DSLModel;
	view: DSLView;
}

export interface DSLField {
	model: DSLFieldModel;
	view: DSLFieldView;
}

export interface DSLFieldModel {
	access: DSLFieldAccess;
	active: boolean;
	autoinsert: boolean;
	default: unknown;
	if: Record<string, DSLIf>;
	max: null | number | string;
	min: null | number | string;
	multi: boolean;
	prefill: Array<unknown>;
	required: boolean;
	required_all: boolean;
	rounding: null | number;
	save: boolean;
	source: null | string | Array<unknown> | Record<string, string>;
	sourceid: string;
	sourcefilter: null | Record<string, DSLSourceFilter>;
	subfields: Record<string, DSLSubField>;
	subfields_sort: string[];
	template: string | null;
	transform: object[];
	transform_filter: object[];
	transform_post: object[];
	type: DSLFieldModelTypes;
	validate: object[];
	dynamic: DSLFieldDynamic;
}

export interface DSLFieldAccess {
	read: string[];
	if: null | string;
	write: string[];
}
export interface DSLIf {
	fields: string[];
	sections: string[];
	note: string;
	require_fields: string[];
	readonly: {
		fields: string[];
		sections: string[];
	};
	prefill: Record<string, any>;
	highlight: string;
}
export interface DSLFieldDynamic {
	source: string | null;
	type: string;
	query: string | null;
}

export interface DSLSourceFilter {
	dynamic: null | string;
	static: null | number | string | Array<string>;
	source: null | Array<string>;
	default: null | number | string;
}

export interface DSLSubField {
	label: string;
	style: object[];
	readonly: boolean;
	required: boolean;
	source: null | string | Array<unknown> | object;
	dynamic: string;
	sourcefilter: DSLSourceFilter;
	type: DSLSubFieldTypes;
	class: string;
}
export interface DSLTransform {
	name: string;
	type?: string;
	fields?: string[];
	[key: string]: any;
}

export interface DSLValidate {
	name: string;
	fields?: string[];
	require?: string;
	error?: string;
	[key: string]: any;
}

export interface DSLFieldView {
	class: string;
	embed: {
		form: null | string;
		query: null | string;
		selectable: boolean;
		request_type: "GET" | "POST";
		add_preset: false | Record<string, unknown>;
		add_form: null | string;
	};
	control: DSLFieldTypes;
	highlight: string | null;
	findfilter: string | null;
	findmulti: boolean;
	findunique: boolean;
	findrange: boolean;
	requireall_bypass: boolean;
	requireif_bypass: boolean;
	format: "" | "hic" | "ssn" | "us_phone" | "us_zip";
	label: string;
	note: string;
	offscreen: boolean;
	readonly: boolean;
	template: string | null;
	transform: DSLTransform[];
	validate: DSLValidate[];
	max_count: null | number;
	grid: {
		add: "flyout" | "inline" | "none";
		copy: string[];
		edit: boolean;
		fields: string[];
		split: boolean;
		text_trim: null | number;
		tooltip: string[];
		width: string[];
		label: (string | null)[];
		rank: "local" | "global" | "none";
		selectall: boolean;
		hide_cardmenu: boolean;
	};
}

export interface DSLAccess {
	create: string[];
	create_all: string[];
	read: string[];
	read_all: string[];
	update: string[];
	update_all: string[];
	delete: string[];
	request: string[];
	review: string[];
	rereview: string[];
	write: string[];
}
export interface DSLModel {
	access: DSLAccess;
	bundle: string[];
	collections: unknown[];
	sections_order: string[];
	indexes: {
		gin: unknown[];
		lower: unknown[];
		many: unknown[];
		unique: unknown[];
	};
	name: string[] | string;
	prefill: Record<string, unknown>;
	reportable: boolean;
	required_if: null | string;
	save: boolean;
	sections_group: object[];
	sections: Record<string, DSLSection>;
	transform: DSLTransform[];
	transform_filter: DSLTransform[];
	transform_post: DSLTransform[];
	validate: DSLValidate[];
}
export interface DSLSection {
	fields: string[];
	group: DSLGroup;
	note: string;
	area: string;
	tab: string;
	modal: boolean;
	prefill: string | null;
}

export interface DSLGroup {
	label: string;
	note: string;
}

export interface DSLView {
	grid: DSLGrid;
	icon: string;
	label: string;
	dimensions: object;
	max_rows: number | null;
	open: "read" | "edit";
	transform: object[];
	hide_cardmenu: boolean;
	validate: object[];
	find: DSLGridFind;
	comment: string;
}

export interface DSLGridFind {
	advanced: string[];
	basic: string[];
}

export interface DSLStructure {
	data: Record<string, unknown>;
	type: DSLStructTypes;
	key: string;
	form: string;
}

export interface DSLGrid {
	fields: string[];
	label: (string | null)[];
	width: number[];
	hide_columns: string[];
	sort: string[];
	style: object;
}

export interface DSLFormState {
	form: string;
	dsl?: DSLForm;
	id: null | number | string;
	mode: DSLModeTypes;
	preset: Record<string, unknown>;
	links?: Record<string, unknown>;
	prefill?: Record<string, unknown>;
	formdata?: Record<string, unknown>;
	show_hide?: Record<string, boolean>;
}

export interface DSLFormProps {
	form: string;
	id: null | number | string;
	mode: DSLModeTypes;
	preset?: Record<string, unknown>;
	links?: Record<string, unknown>;
	prefill?: Record<string, unknown>;
	formdata?: Record<string, unknown>;
}
