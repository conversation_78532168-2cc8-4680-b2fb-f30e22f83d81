import type { Rdl } from "@mescius/activereportsjs/core";
import type { ReportDefinition, SaveReportInfo, SaveResult, NewReport, Report, SaveAsResult, SaveNewReportInfo } from "@mescius/activereportsjs/reportdesigner";
import type { Designer as ReportDesigner } from "@mescius/activereportsjs-react";
import { DocumentOptions, ReportParams } from "@components/arjs";

export type DesignerMode = "designer";
export type ViewerMode = "viewer";

export type AppMode = DesignerMode | ViewerMode;

export type ReportParameter = {
  DataType: string;
  DefaultValue: { Values: string[] };
  Hidden: boolean;
  Name: string;
  Prompt: string;
  ValidValues: {
    ParameterValues: { Value: string; Label: string }[];
    OrderBy: { Condition: string };
  };
};
export type ReportDescriptor = {
  name?: string;
  url?: string;
  json_data?: Rdl.Report | string;
  paginated?: boolean;
  code?: string;
  type?: "Chart" | "Label" | "Page";
} & {
  [key: string]: string; // Dynamic parameter properties
};

export type DesignerProps = {
  report?: ReportDescriptor;
  onRender?: (report: ReportDefinition) => Promise<void>;
  // onSave: (report: SaveReportInfo) => Promise<void>;
  onSave?(options: SaveReportInfo): Promise<SaveResult | undefined>;
  onCreate?(): Promise<Report | NewReport | undefined>;
  onSaveAs?(options: SaveNewReportInfo): Promise<SaveAsResult | undefined>;
  onQuery?: () => void;
  designerRef?: React.LegacyRef<ReportDesigner> | undefined;
};

type ViewerToolbarBtns = "$openDesigner" | "$split" | "$printPDF" | "$split" | "$navigation" | "$split" | "$refresh" | "$split" | "$history" | "$split" | "$zoom" | "$fullscreen" | "$split"
export type ViewerProps = {
  report?: ReportDescriptor;
  onEdit?: () => void;
  onPrint?: () => void;
  onError?: ErrorHandler;
  parameters?: ReportParams;
  zoom?: "FitToWidth" | "FitPage";
  sidebarVisible?: boolean;
  toolbarVisible?: boolean;
  options?: Partial<DocumentOptions>;
  toolbarBtns?: ViewerToolbarBtns[];
  fullscreen?: boolean;
};

export type ListProps = {
  title: string;
  items: string[];
  currentItemIndex?: number;
  selectionChanged: (index: number) => void;
};


export type ReportMangerProps = {
  code: string
  actionType: "download" | "print" | "view";
  toolbarVisible?: boolean;
  parameters?: ReportParams;
  showActionButtons?: boolean;
  fullscreen?: boolean;
  toolbarBtns?: ViewerToolbarBtns[];
  options?: Partial<DocumentOptions>;
}

export type ErrorHandler = (error: ErrorMessage) => void;

export type ErrorMessage = {
  severity: "error" | "warn" | "info" | "debug";
  message: string;
  details: string;
};
