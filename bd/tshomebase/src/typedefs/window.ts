import type { I<PERSON>Auth } from "./coffee/auth";
import type { <PERSON><PERSON><PERSON>, DS<PERSON>orm, DSLTransform, DSLValidate } from "./coffee/dsl";
import type ReactDOM from "react-dom/client";
import type { IDSLDraw, IDSLDrawSubForm } from "./coffee/dsl-draw";
import { FunctionComponent } from "react";
import { PooledRequest } from "@core/request/request";
import { DSLCardViewProps, TabModes } from "@blocks/dsl-card-view/dsl-card-view";
import { IFormData } from "@hooks/form-data";
import { RemoteModuleItem } from "@typedefs/shared";
import { CardViewPreview } from "@blocks/dsl-card-view";
import { DSLCardViewRef } from "@blocks/dsl-card-view";
export interface RXRegisteredComponent {
	[key: string]: JSX.Element;
}

export interface CRConfig {
	CRBackend: string;
	CRBaseURL: string;
	CREnvironment: string;
	CRLocal: boolean;
	CRLoginURL: string;
}

export type CLFile = {
	filehash: string;
	filename: string;
	filesize: number;
	mimetype: string;
	thumbnail?: string;
	image?: string;
	images?: string[];
};

export interface FlyoutProps extends Partial<DSLCardViewProps> {
	form?: string;
	action?: "FlyoutCardView" | "FlyoutHTMLView" | "FlyoutCardSubformView" | string;
	label?: string;
	style?: React.CSSProperties;
	html?: string;
	mode?: TabModes;
	forceReadOnly?: boolean;
	on_loaded?: (ref: DSLCardViewRef) => void;
	record?: string | number;
	preview?: CardViewPreview["preview"];
}

interface Version {
	app: {
		version: string;
		files: number;
		buildtime: string;
		buildenv: "dev" | "prod";
		buildhost: string;
		branch: string;
		hash: string;
	};
	nes: {
		version: string;
		wss: boolean;
		monitor: boolean;
		is_prod: boolean;
		environment: string;
		appname: string;
		applink: number;
		flags: Record<string, any>;
		active: boolean;
	};
}

interface CompanyConfig extends IFormData {
	name: string;
	logo?: CLFile;
	street: string;
	street2: string;
	city: string;
	state_id: string;
	zip: string;
	fax: string;
	supmail: string;
	bi_refresh: number;
	kb_refresh: number;
	snap_refresh: number;
	jwt_timeout: number;
	ui_lock: number;
	filter_wc: "Yes" | "No";
	prevent_datetime_focus_popup?: "add" | "addfill" | "edit";
	no_download: "Yes" | "No";
	toast_form_level_errors: "Yes" | "No";
	separate_numbers: "Yes" | "No";
	number_separator: string;
	decimal_separator: string;
	format_currency: "Yes" | "No";
	currency_prefix: string;
	esign_title: "Yes" | "No";
	pdf_logo: CLFile;
	max_collections: number;
	max_grid_columns: number;
	max_grid_rows: number;
	max_filter_rows: "Yes" | "No";
	max_data_rows: number;
	error_msg: string;
	default_event_duration: number;
	default_event_span: number;
	cal_default_view: "Month" | "Week" | "Day";
	weekends: "Yes" | "No";
	hidden_days: ("Mon" | "Tue" | "Wed" | "Thu" | "Fri" | "Sat" | "Sun")[];
	first_day: "Mon" | "Tue" | "Wed" | "Thu" | "Fri" | "Sat" | "Sun";
	password_strength: "Medium" | "Strong" | "Very Strong";
	default_lot_expire_days: number;
	default_order_days: number;
	default_order_expire_days: number;
	next_delivery_day: number;
	next_compound_days: number;
	dose_start_days: number;
	refill_days_out: number;
	clinical_assmts: string[];
	pmp_program_name: string;
	intake_inter_types: string[];
	intake_inter_primary_type: string;
	nurse_inter_types: string[];
	nurse_careplan_type: "Nursing Care Plan" | "Multidisciplinary Care Plan";
	dt_inventory_check: "Yes";
	est_qty_inv_check: "Yes";
	auto_test_claim: "Yes";
	mm_ready_pref: "Delivery Ticket Confirmation" | "Work Ticket Verification" | "Delivery Confirmation";
	adjustment_threshold: number;
	allow_invoicing_before_delivery_ticket: "Yes" | "No";
	allow_billing_before_verification: "Yes" | "No";
	bg_color: string;
	fg_color: string;
	px_survey_type: string[];
}

export interface EditingUser {
	firstname: string;
	lastname: string;
	name: string;
	id: number;
	role: string;
	photo: string | null;
	sso: string | null;
}

export interface User {
	id: number;
	auto_name: string;
	displayname?: string;
	firstname?: string;
	lastname?: string;
	role_auto_name?: string;
	role: string;
	editing?: {
		forms?: {
			[key: string]: {
				// form
				[key: number]: {
					// id
					[key: number]: number; // user_id => timestamp
				};
			};
		};
		users?: Record<number | string, EditingUser>;
	};
}

export type CLJQueryObject = JQuery<HTMLElement> & { select2: any };

export interface Flyout {
	open: (opts: FlyoutProps) => {
		done: (cb: (data: IFormData) => void) => void;
		fail: (cb: (error: unknown) => void) => void;
		always: (cb: () => void) => void;
	};
}

type DSLFxValidators = (
	form: DSLForm,
	dd: IDSLDrawSubForm,
	vld: DSLValidate,
	f: CLJQueryObject,
	k: string,
	flv?: boolean
) => any;

export type DSLFxFormValidator = (form: DSLForm, dd: IDSLDrawSubForm, vld: DSLValidate, vals: IFormData) => any;

export type DSLFxTransforms = (
	form: DSLForm,
	dd: IDSLDrawSubForm,
	trn: DSLTransform,
	f: CLJQueryObject,
	k: string
) => any;

export type DSLFxFormTransForm = (form: DSLForm, dd: IDSLDrawSubForm, trn: DSLTransform) => any;

declare global {
	interface Window {
		//React
		__REACT_DEVTOOLS_GLOBAL_HOOK__: boolean | null;
		$: object;
		initNavToURL: string | null;
		rxRegistry: RXRegisteredComponent | null;
		pooledRequest?: Record<string, PooledRequest[]>;
		ReactDOM: any;

		//Coffee
		DSL_BUNDLE?: Record<string, string[]>;
		fillDSLDefaults: <T>(form: string, data: T) => T;
		MODULES: RemoteModuleItem[];
		MAX_GRID_ROWS: number;
		DSL: Record<string, DSLForm>;
		DSLDraw: IDSLDraw;
		Flyout: Flyout;
		Auth: ICRAuth;
		CRConfig: CRConfig;
		DSLFx: {
			Validators: Record<string, DSLFxValidators>;
			Transforms: Record<string, DSLFxTransforms>;
			TransformField: (
				form: DSLForm,
				dd: IDSLDrawSubForm,
				trns: DSLTransform[],
				d: CLJQueryObject,
				k: string,
				flv: boolean
			) => any;
			ValidateFieldRules: (
				form: DSLForm,
				dd: IDSLDrawSubForm,
				v: DSLField,
				d: CLJQueryObject,
				k: string,
				flv: boolean
			) => any;
			ValidateFieldError: (f: CLJQueryObject, err?: string | false) => void;
			ValidateFieldWarning: (f: CLJQueryObject, warn?: string | false) => void;
		};
		App: {
			user: User;
			reactNav: any;
			company: CompanyConfig;
			version: Version;
			feature: {
				debug: boolean;
				qa: boolean;
				wip: boolean;
			};
		};
		// React Binder For Coffee
		loadComponent: (component: string, container: HTMLElement, id: string, props: Record<string, any>) => void;
		unloadComponent: (component: string, id: string) => void;

		// Coffee Utils
		parseBarcode: (raw: string) => any | false;
		joinValid: (args: (string | undefined)[], sep?: string) => string;
		registerComponent: (name: string | null, Component: FunctionComponent<unknown> | null) => void;
		boundBox(mw: number, mh: number, iw: number, ih: number): [number, number];
		dataImage(id: string, url: string, outputFormat: string): Promise<[string, string, number, number]>;
		dateTimeFromNow(dt: string): string;
		dateTimeFromTZ(dt: string, fmt?: string): string;
		dateTimeFromUTC(dt: string, fmt?: string): string;
		dateTimeInUTC(): Date;
		dateTimeNow(): string;
		dateTimeToUTC(dt: string): string;
		dateTimetoLocal(dt: string): string;
		dateYMD(dt?: string): string;
		escapeHTML(val: string): string;
		escapeHTMLGrid(val: string): string;
		fileUploaded(id: string, file: any): void;
		findArrayObject(arr: any[], key: string, val: any): any | false;
		formatDecimal(n: number, z: number): string;
		formatFullName(lb: string[]): string;
		formatNote(n: string): string;
		generateUUID(): string;
		getAge(dateString: string, today?: Date): string;
		getDynamicClass(form: string, base?: string): any;
		formatForReporting: (data: any, fields: string[]) => any;
		openWindow(link: string): Window;
		getLinkID(link: string, linkid: any): any;
		isBrowserMobile(): boolean;
		loadScript(url: string): void;
		next_id(series: string): string;
		roundTo(val: number, rounding: number): number;
		fixTrailingDecimalZeros(val: number, rounding: number): string;
		scrollTop(): void;
		textAll(val: string): string;
		textOnly(val: string): string;
		verticalHeight(): number;
		dataURItoBlob(dataURI: string): Blob;
		dateRangeOverlaps(start_a: string, end_a: string, start_b: string, end_b: string): boolean;

		// Coffee Notifiers
		prettyAlert(title?: string, msg?: string, err?: string, fxOk?: (() => void) | null): void;
		prettyAlertCustom(title?: string, msg?: string, err?: string, fxOk?: (() => void) | null): void;
		prettyConfirm(
			title?: string,
			msg?: string,
			txTrue?: string,
			txFalse?: string,
			fxTrue?: (txTrue: string) => void,
			fxFalse?: (txFalse: string) => void,
			type?: string
		): void;
		prettyError(title?: string | boolean, err?: string, fxErr?: (() => void) | null): void;
		prettyForm(
			title?: string,
			html?: string,
			txTrue?: string,
			txFalse?: string,
			fxInit?: (dialog: any) => void,
			fxTrue?: (txTrue: string, dialog: any) => void,
			fxFalse?: (txFalse: string, dialog: any) => void,
			type?: any
		): void;
		prettyFormCustom(
			title?: string,
			html?: string,
			txTrue?: string,
			txFalse?: string,
			fxInit?: (dialog: any) => void,
			fxTrue?: (txTrue: string, dialog: any) => void,
			fxFalse?: (txFalse: string, dialog: any) => void,
			type?: any
		): void;
		prettyMessage(title?: string, msg?: string, fxMsg?: (() => void) | null): void;
		prettyNotify(title?: string): void;
		prettyYesNo(
			title?: string,
			msg?: string,
			fxTrue?: (txTrue: string) => void,
			fxFalse?: (txFalse: string) => void
		): void;
		moment: Function;
		[key: string]: any;
	}
	type globalThis = Window;
}
