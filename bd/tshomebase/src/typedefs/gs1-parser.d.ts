// Basic type declaration for gs1-parser module
// Allows TypeScript to recognize the module without full type safety
declare module 'gs1-parser' {
    interface DecodedResult {
        GTIN?: string;
        serial?: string;
        exp?: string | Date | undefined; // Allow string or Date
        lot?: string;
        formatedCode?: string;
        // Add other potential fields if known
        [key: string]: any; // Allow other properties
    }

    export function decode(barcode: string): DecodedResult;
    // Add other exported functions if needed
    export function isDIBarcode(barcode: string): boolean;
    export function isPIBarcode(barcode: string): boolean;
    export function isDIPIBarcode(barcode: string): boolean;
    export function getAIMap(): any;
}