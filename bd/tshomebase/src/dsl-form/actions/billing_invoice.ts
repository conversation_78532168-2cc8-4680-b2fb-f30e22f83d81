import { ServerActionCallBackFunction } from "@dsl-form/actions";
import { request } from "@core/request";

import {
    showToastError,
    safeDismissToast,
} from "@utils/fx";
import { toast, ToastOptions } from "react-toastify";

export const billing_invoice: Record<string, ServerActionCallBackFunction> = {
    showCMS1500View: (_act, formMap, fd, _opts) => {

        if (!formMap) return;
        const medClaimRec = fd?.subform_medical?.[0] || fd?.subform_1500?.[0];
        if (!medClaimRec) {
            window.prettyError("Missing Claim Data", "Unable to find medical claim data on invoice.");
            return;
        }
        const generatingToastId = 'generating-cms-1500-alert';
        const showToastGenerating = (message: string) => {
            safeDismissToast(generatingToastId);
            const toastOptions = {
                toastId: generatingToastId,
                position: "bottom-center",
                hideProgressBar: true,
                closeOnClick: true,
                pauseOnHover: false,
                draggable: false,
                progress: 0,
                theme: "dark"
            } as ToastOptions;
            toast.info(message, toastOptions);
        }
        showToastGenerating("Generating CMS 1500...");

        request({
            url: `/api/print/1500/med_claim`,
            method: 'POST',
            data: medClaimRec
        }).then((res) => {
            if (!res?.data) {
                window.prettyError("Error", "Unable to generate CMS 1500. Please contact support.");
                return;
            }
            const cms1500Data = {
                dataSource: {
                    cms: window.formatForReporting(res.data, ["f24"]) || {}
                }
            };
            window.openARJsPopover("CMS 1500", "cms1500", {}, cms1500Data);
        }).catch((error) => {
            console.error("Error mapping CMS 1500 data:", error);
            showToastError(`Error mapping CMS 1500 data`);
        });
    }
};
