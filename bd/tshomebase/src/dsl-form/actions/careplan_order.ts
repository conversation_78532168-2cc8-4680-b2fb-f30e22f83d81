import _ from "lodash";

import { ServerActionCallBackFunction } from "@dsl-form/actions";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { IFormData } from "@hooks/index";

export const careplan_order: Record<string, ServerActionCallBackFunction> = {

    showFactorOrderView: (_act, formMap, fd, _opts) => {

        if (!formMap) return;
        const orderFieldDD = formMap?.careplan_order_item;
        const orderItemsFieldNode = orderFieldDD?.field_nodes?.subform_order;
        const orderItemsSubform = orderItemsFieldNode?.parent().parent().find('.add-subrow')

        const linkMap: DSLDrawLinkMap = {
            link: formMap.options?.link || "",
            links: formMap.options?.links || [],
            linkid: formMap.options?.linkid || {},
        };

        const NormalizeFactorOrder = (data: Omit<IFormData, "id">) => {
            try {
                const orderItemRec = data;
                (orderItemRec as any).frequency_id = "UAD";
                return {
                    ..._.omit(orderItemRec, ['dose_str', 'dose_unit']),
                    frequency_id: "UAD",
                    rx_template_id: "Factor",
                };
            } catch (error) {
                console.error("Error converting factor order to prescription ", error);
                window.prettyError("Error", "Error occurred while converting factor order to order set item. Please contact support.");
            }
        }

        const dfd = window.Flyout.open({
            action: 'FlyoutCardView',
            form: "view_order_factor_drugs",
            card: "addfill",
            preset: {},
            link: linkMap.link,
            linkid: linkMap.linkid,
            links: linkMap.links,
            autoRecoverEnabled: false,
        });
        dfd.done((data) => {
            const factorOrders: Array<Omit<IFormData, "id">> = data.subform_factor || [];
            if (factorOrders.length === 0) {
                console.error("Error no orders found in factor order creation view", data);
                window.prettyError("Error", "Unable to find any orders on the creation view.");
            }
            for (const factorOrder of factorOrders) {
                const orderItemRec = NormalizeFactorOrder(factorOrder);
                if (orderItemRec) {
                    window.FieldSubform.add_row(orderItemsSubform, orderItemRec);
                }
            }
        });
    }
};
