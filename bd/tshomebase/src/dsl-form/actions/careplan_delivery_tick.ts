import _ from "lodash";
import { ServerActionCallBackFunction } from "@dsl-form/actions";
import { openPartialFormFillPopup } from "@blocks/dsl-card-view/dsl-card-section-popup";
import { showToastError } from "@utils/fx";
import { request } from "@core/request";

export const careplan_delivery_tick: Record<string, ServerActionCallBackFunction> = {
	addDTItem: (_act, formMap, fd, _opts) => {
		if (!formMap) return;
		const deliveryTickDD = formMap?.careplan_delivery_tick;
		const dt_data = deliveryTickDD?.preset;

		const addNewDTItem = async (data: any) => {
			console.log("Adding new delivery ticket item with data:", JSON.stringify(data, null, 2));
			window.prettyNotify("Adding Item to Delivery Ticket...");
			await new Promise((resolve) => setTimeout(resolve, 3000));

			// Set a general timeout to clear any notifications after 10 seconds
			const generalTimeout = setTimeout(() => {
				window.prettyNotify();
			}, 10000);

			try {
				const resp = await request({
					url: `/dispense?func=add_new_dt_item`,
					method: "POST",
					data: data,
				});
				console.log("Full response from add_new_dt_item:", JSON.stringify(resp, null, 2));

				if (!resp) {
					console.error("No response received from server");
					showToastError(`Error adding new delivery ticket item: No response from server`);
					window.prettyNotify();
					return null;
				}

				const response = resp as { success: boolean; message?: string; data: any };
				if (response.success === true) {
					console.log("Successfully added delivery ticket item");
					window.prettyNotify();
					return response.data;
				} else {
					console.error("Server returned error:", response);
					showToastError(
						`Error adding new delivery ticket item: ${response.message || "Unknown error occurred"}`
					);
					window.prettyNotify();
					return null;
				}
			} catch (error: unknown) {
				console.error("Error adding new delivery ticket item:", error);
				const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
				showToastError(`Error adding new delivery ticket item: ${errorMessage}`);
				window.prettyNotify();
				return null;
			} finally {
				clearTimeout(generalTimeout);
			}
		};

		const promptUserToAddItem = async (dtData: any) => {
			const formSections = ["Add Item", 'Supply Kit Preview'];
			const presets: any = {
				patient_id: dtData.patient_id,
				ticket_no: dtData.ticket_no,
				site_id: dtData.site_id,
				rx_fltr: dtData.rx_id,
				rx_id: dtData.rx_id ? dtData.rx_id[0] : null,
			};
			try {
				const user_data = (await openPartialFormFillPopup({
					label: "Add Item To Ticket",
					form: "careplan_dt_add_item",
					preset: presets,
					btnLabels: {
						cancel: "Close",
						save: "Add",
					},
					containerStyle: {
						width: "85%",
						height: "65%",
					},
					sections: formSections,
				}).catch((error) => {
					if (error === "close") {
						console.log("User closed the popup");
						return null;
					}
					console.error("Error getting item:", error);
					showToastError(`Error getting item`);
					return null;
				})) as any;

				if (!user_data) {
					console.log("No user data received");
					return;
				}

				if (user_data.cancelled) {
					console.log("Cancelled adding item");
					showToastError(`Cancelled adding item.`);
					return;
				}

				const pickedDtData = _.pick(dtData, ["patient_id", 'careplan_id', "ticket_no", "site_id"]);
				const mergedData = {
					...pickedDtData,
					...user_data,
				};
				const result = await addNewDTItem(mergedData);
				if (result) {
					// Refresh the delivery ticket grid after successful addition
					const deliveryTickDD = formMap?.careplan_delivery_tick;
					if (deliveryTickDD?.options?.parent?.tab_do_refresh) {
						deliveryTickDD.options.parent.tab_do_refresh();
					}
				}
			} catch (error: unknown) {
				console.error("Error prompting user to add item:", error);
				const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
				showToastError(`Error generating item selection: ${errorMessage}`);
			}
		};
		promptUserToAddItem(dt_data);

	},
};
