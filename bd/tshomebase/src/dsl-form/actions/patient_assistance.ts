import _ from "lodash";

import { ServerActionCallBackFunction } from "@dsl-form/actions";

export const patient_assistance: Record<string, ServerActionCallBackFunction> = {


	patientAssistanceAddPayer: (_act, _formMap, fd, _opts) => {

		const patientAssistanceRec = fd;
		if (patientAssistanceRec.payer_id) {
			window.prettyAlert("Payer Already Exist.", "A payer record already exists for this assistance form. Please remove before adding a new payer.");
			return;
		}
		const patientAssistanceFieldDD = _formMap?.patient_assistance;
		const preset = patientAssistanceFieldDD?.options?.preset;
		const payerPreset = {
			type_id: patientAssistanceRec.assistance_type === 'copay' ? "COPAY" :
				patientAssistanceRec.assistance_type === 'pap' ? 'PAP' :
					patientAssistanceRec.assistance_type === 'hardship' ? 'HARD' :
					'FOUND',
			pap_program_id: patientAssistanceRec.embed_pap_program ? patientAssistanceRec.embed_pap_program[0]?.id : null,
			cap_program_id: patientAssistanceRec.embed_cap_program ? patientAssistanceRec.embed_cap_program[0]?.id : null,
			other_program_id: patientAssistanceRec.embed_other_program ? patientAssistanceRec.embed_other_program[0]?.id : null,
		}

		const CreatePayerRecord = async () => {
			try {
				const ps = preset || {}
				const linkMap = {
					link: "",
					linkid: {
						order: "",
						patient: "",
						careplan: ""
					},
					links: [] as string[]
				};
				if (ps.order_id) {
					linkMap.link = "order";
					linkMap.links.push("order");
					linkMap.linkid["order"] = ps.order_id;
				}
				if (ps.patient_id) {
					linkMap.link = "patient";
					linkMap.links.push("patient");
					linkMap.linkid["patient"] = ps.patient_id;
				}
				if (ps.careplan_id) {
					if (!linkMap.link) {
						linkMap.link = "careplan";
					}
					linkMap.links.push("careplan");
					linkMap.linkid["careplan"] = ps.careplan_id;
				}
				const dfd = window.Flyout.open({
					action: 'FlyoutCardView',
					form: "payer",
					card: "addfill",
					preset: payerPreset,
					link: linkMap.link,
					linkid: linkMap.linkid,
					links: linkMap.links,
					autoRecoverEnabled: false,
				})
				dfd.done((r) => {
					if (!r) {
						console.error('Unexpected save response while creating payer record')
						return;
					}

					if (r?.id) {
						patientAssistanceFieldDD?.value_field('payer_id', r?.id, true, true);
					}
				});
			} catch (error) {
				console.error("Error while creating payer record", error);
				window.prettyError("Error", "Error occurred while creating payer record. Please contact support.");
			}
		}
		CreatePayerRecord();
	},

	patientAssistanceAddInsurance: (_act, _formMap, fd, _opts) => {
		const patientAssistanceRec = fd;
		if (!patientAssistanceRec.payer_id) {
			window.prettyAlert("Payer Not Found.", "A payer record not found for this assistance form. Please add a payer before adding an insurance.");
			return;
		}
		if (patientAssistanceRec.assistance_status !== 'Applied') {
			window.prettyAlert("Assistance Status Not Applied.", "The assistance status is not 'Applied'. Please apply for assistance before adding an insurance.");
			return;
		}
		if (patientAssistanceRec.insurance_id) {
			window.prettyAlert("Insurance Already Exist.", "An insurance record already exists for this assistance form. Please remove before adding a new insurance.");
			return;
		}
		const patientAssistanceFieldDD = _formMap?.patient_assistance;
		const preset = patientAssistanceFieldDD?.options?.preset;
		const insurancePreset = {
			payer_id: patientAssistanceRec.payer_id
		}

		const CreateInsuranceRecord = async () => {
			try {
				const ps = preset || {}
				const linkMap = {
					link: "",
					linkid: {
						order: "",
						patient: "",
						careplan: ""
					},
					links: [] as string[]
				};
				if (ps.patient_id) {
					linkMap.link = "patient";
					linkMap.links.push("patient");
					linkMap.linkid["patient"] = ps.patient_id;
				}

				const dfd = window.Flyout.open({
					action: 'FlyoutCardView',
					form: "payer",
					card: "addfill",
					preset: insurancePreset,
					link: linkMap.link,
					linkid: linkMap.linkid,
					links: linkMap.links,
					autoRecoverEnabled: false,
				})
				dfd.done((r) => {
					if (!r) {
						console.error('Unexpected save response while creating insurance record')
						return;
					}

					if (r?.id) {
						patientAssistanceFieldDD?.value_field('insurance_id', r?.id, true, true);
					}
				});
			} catch (error) {
				console.error("Error while creating insurance record", error);
				window.prettyError("Error", "Error occurred while creating insurance record. Please contact support.");
			}
		}
		CreateInsuranceRecord();
	}
};
