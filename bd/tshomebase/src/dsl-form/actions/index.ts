import { assessment } from "./assessment";
import { ongoing } from "./ongoing";
import { encounter } from "./encounter";
import { careplan_order } from "./careplan_order";
import { careplan_delivery_tick } from "./careplan_delivery_tick";
import { billing_invoice } from "./billing_invoice";
import { patient_assistance } from "./patient_assistance";
import { patient_insurance } from "./patient_insurance";
import { IFormData } from "@hooks/index";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { DSLCardViewRef } from "@blocks/dsl-card-view";
import { common } from "@dsl-form/actions/common";

export type ServerActionCallBackFunction = (
	act: object,
	formMap: Record<string, IDSLDrawSubForm> | undefined,
	fd: Omit<IFormData, "id">,
	opts: Record<string, unknown>,
	card?: DSLCardViewRef | null
) => void;

export const SERVER_ACTIONS_CALLBACK: Record<string, ServerActionCallBackFunction> = {
	...common,
	...assessment,
	...ongoing,
	...encounter,
	...patient_assistance,
	...careplan_order,
	...careplan_delivery_tick,
	...billing_invoice,
	...patient_insurance,
};
