import _ from "lodash";

import { ServerActionCallBackFunction } from "@dsl-form/actions";
import { request } from "@core/request/request";
import { IFormData } from "@hooks/index";

export const patient_insurance: Record<string, ServerActionCallBackFunction> = {

    addPayers: (act, formMap, fd, opts: { [key: string]: any }) => {

        if (
            opts.insurances?.length === 0 &&
            opts.payers?.length === 0 &&
            opts?.foundPayers
        ) {
            if (!opts.cardFinderFound && opts.foundPayers.length === 0 && opts.error){
                window.prettyError('Error while checking PBM coverage', opts.error);
            } else if (opts.cardFinderFound && opts.foundPayers.length > 0) {
                let message = opts.foundPayers.join('<b>');
                window.prettyAlert(
                    "Covered PBM",
                    `Found insurance coverage for these payer(s): <br> ${message}`
                );
            } else if (!opts.cardFinderFound && opts.foundPayers.length === 0) {
                window.prettyAlert(
                    "Process Completed",
                    "No PBM coverage found for this patient."
                );
            }
        }

        window.Flyout.open({
            form: 'card_finder_resp',
            card: 'addfill',
            preset: {
                patient_id: opts.patient_id,
            },
        });

    },
    viewBV: (act, formMap, fd, opts: { [key: string]: any }) => {
        request({
			url: `/form/careplan_order_bv/${fd.id}`,
			method: "GET",
		})
        .then((res: any) => {
            let resData = null;
            if (res.data && res.data.length) {
                resData = res.data[0];
            }

            if (!resData) return;

            window.Flyout.open({
                form: "careplan_order_bv",
                card: "read",
                preset: resData,
                audit_mode: true,
                autoRecoverEnabled: false,
            });
        })
        .catch((err) => {
            console.log(err);
        })
        
    }
};
