import { DSLCardViewRef, IDSLDrawForm } from "@blocks/dsl-card-view";
import { ServerActionCallBackFunction } from "@dsl-form/actions";
import { getDSLDrawSubform, presetWithCardRef } from "@utils/dsl-fx";

export const common: Record<string, ServerActionCallBackFunction> = {
	editWithPreset: (act, formMap, fd, opts, card) => {
		if (!card) {
			console.error("ServerActionCallBackFunction.editWithPreset: No card found, You sure are in read mode?");
			return;
		}
		const ddr = card.cardread?.ddr;
		if (!ddr) {
			console.error("ServerActionCallBackFunction.editWithPreset: No ddr found, You sure are in read mode?");
			return;
		}
		if (!card.cardread.tab_can_edit()) {
			console.error(
				"ServerActionCallBackFunction.editWithPreset: Tab is not editable, could be permission issue!"
			);
		}
		const ps = (opts?.preset || {}) as Record<string, unknown>;
		const idx = card.subscribe("ready", async (ref: DSLCardViewRef) => {
			presetWithCardRef(ref, ref.form, ps);
			card.unsubscribe("ready", idx);
		});
		card.cardread.tab_do_edit();
	},
};
