import { DSLField, DSLSection } from "@typedefs/coffee/dsl";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import _ from "lodash";
import { SyntheticEvent } from "react";

export const TabController = {
	goToField: (fld: JQuery) => {
		const sectionId = fld.closest(".fieldgroup").attr("id");
		if (!sectionId) return;
		TabController.openSection(sectionId);
		fld.focus();
		if (fld.length) {
			try {
				fld[0].scrollIntoView();
			} catch (error) {
				console.error("TabController.goToField: error scrolling to field:", error);
			}
		}
	},
	handleSectionTabsIf: (dd: IDSLDrawSubForm) => {
		if (!dd.section_map) return;
		if (!dd.section_tab_map) return;
		const controllerSections = [];
		for (const [secID, sectionName] of Object.entries(dd.section_map || {})) {
			if (sectionName.includes("__tg__")) {
				controllerSections.push(secID);
			}
		}
		if (controllerSections.length === 0) return;
		controllerSections.forEach((secID) => {
			const tabMap = dd.section_tab_map[secID];
			const secEl = $("#" + secID);
			if (!secEl.length) return;
			for (const [tab, sections] of Object.entries(tabMap)) {
				const tabBtn = secEl.find('.tab-list-button[tab="' + tab + '"]');
				if (tabBtn.find("input").length) return; // Don't touch if btn contains checkbox (means its toggle tab)

				let tabVisible = [];
				for (const section of sections as Array<string>) {
					let isVisible = window.DSLFields.is_section_visible(dd.shmap?.sections || {}, section) as boolean;
					const dsl = window.DSL[dd.options.form];
					const secDef = dsl.model.sections[section];
					const area = secDef.area;
					if (isVisible && area && area !== "header" && secDef.fields.length == 1) {
						const sfField = dsl.fields[secDef.fields[0]];
						if (sfField.model.type === "subform" && !sfField.model.multi) {
							isVisible = false;
							let subform = sfField.model.source as string;
							if (subform.includes("{") || subform.includes("}")) {
								let ssm = window.DSLRecordSubform.get_subform_source(
									dd.options.form,
									secDef.fields[0],
									dd.values()
								);
								if (!ssm) {
									if (window.App.feature.debug) {
										console.info("Subform sourcefilter not found:", subform, dd.options.form);
									}
								}
								subform = ssm[0];
							}
							if (subform) {
								for (const [fk, fv] of Object.entries(window.DSL[subform].model.sections)) {
									if (fv.area === area) {
										isVisible = true;
										break;
									}
								}
							}
						}
					}
					tabVisible.push(isVisible);
				}
				tabVisible = tabVisible.filter(Boolean);
				if (tabVisible.length == 0) {
					tabBtn.addClass("hide").removeClass("tab-active");
					for (const section of sections as Array<string>) {
						TabController.handleSectionShowHide(dd, dd.options.form, section, false);
					}
				} else {
					tabBtn.removeClass("hide");
				}
			}
			const toBeShownTabController = secEl.find(".tab-list-button:not(.hide)");
			if (
				toBeShownTabController.length === 0 ||
				!TabController.isParentTabActive(toBeShownTabController.first())
			) {
				secEl.css("display", "none");
			} else {
				secEl.css("display", "");
			}
			TabController.roundBtns(secEl);
		});
		controllerSections.forEach((secID) => {
			const secEl = $("#" + secID);
			if (!secEl.length) return;
			TabController.makeTabActiveIfNone(secEl);
		});
	},
	openSection: (secID: string) => {
		const secEl = $("#" + secID);
		if (!secEl.length) return;
		const controller = secEl.attr("tabgroupid") as string;
		const tab = secEl.attr("parent_tab") as string;
		TabController.openTab(controller, tab, true);
	},
	checkTabControllers: (controller: JQuery) => {
		const sectionID = controller.data("id");
		const dd = controller.data("dd");
		if (!dd.tab_toggle_map[sectionID]) return;
		for (const [tab, sections] of Object.entries(dd.tab_toggle_map[sectionID])) {
			const input = controller.find('.tab-list-button:not(.hide)[tab="' + tab + '"]').find("input");
			let checked = false;
			for (const section of sections as Array<string>) {
				let f = "tabif_" + section.replace(/[^\w]|\s/gi, "").toLowerCase();
				if (dd.value_field(f) == "Yes") {
					checked = true;
				}
			}
			if (checked === input.prop("checked")) {
				continue;
			}
			input.prop("checked", checked);
		}
	},
	isParentTabActive: (tabBtn: JQuery) => {
		const controller = tabBtn.closest("[tabcontroller]");
		if (controller.length == 0) return false;
		const parentTab = controller.attr("parent_tab");
		const parentControllerId = controller.attr("parent_tabgroupid");
		if (!parentTab || !parentControllerId) {
			return true;
		}
		const parentController = $(`[tabcontroller="${parentControllerId}"]`);
		const isParentVisible = parentController.is(":visible");
		if (parentController.length == 0 || !isParentVisible) {
			return true;
		}
		const parentBtn = parentController.find('.tab-list-button[tab="' + parentTab + '"]');
		if (parentBtn.length == 0) {
			return false;
		}
		if (!parentBtn.is(":visible") || !parentBtn.hasClass("tab-active")) {
			return false;
		}
		return true;
	},
	triggerIfOnAllFieldsTabToggle: (dd: IDSLDrawSubForm, field: string) => {
		const runValidatorAndTransform = async (ddx: IDSLDrawSubForm, vx: DSLField, formx: string, kx: string) => {
			if (!ddx) return;
			if (!vx) return;
			if (!kx) return;
			if (!formx) return;
			const d = ddx.field_nodes[kx];
			if (!d) return;
			if (vx.model.transform.length > 0)
				window.DSLFx.TransformField(window.DSL[formx], ddx, vx.model.transform as any, d, kx, true);
			if (vx.view.transform.length > 0)
				window.DSLFx.TransformField(window.DSL[formx], ddx, vx.view.transform as any, d, kx, true);
			const ferr = await window.DSLFx.ValidateFieldRules(window.DSL[formx], ddx, vx, d, kx, true);
		};

		const mainForm = dd.options.form;
		if (!mainForm) return;
		const dsl = window.DSL[mainForm];
		if (!dsl) return;
		const dslSections = dsl.model.sections;
		const dslFields = dsl.fields;
		const sections = dsl.fields[field]?.model?.if?.["Yes"]?.sections || [];
		if (sections.length === 0) return;
		sections.forEach((section) => {
			const sectionDef = dslSections[section];
			if (sectionDef.fields.length === 0) return;
			sectionDef.fields.forEach((field) => {
				const fieldDef = dslFields[field];
				if (!fieldDef) return;
				if (fieldDef.model.type !== "subform") {
					runValidatorAndTransform(dd, fieldDef, mainForm, field);
				} else if (fieldDef.model.type === "subform" && !fieldDef.model.multi) {
					const subformSource = fieldDef.model.source as string;
					const subformDSL = window.DSL[subformSource];
					if (!subformDSL) return;
					const subformDD = dd.options.wrapper.subforms.formmap[subformSource] as IDSLDrawSubForm;
					if (!subformDD) return;

					for (const [subField, node] of Object.entries(subformDD.field_nodes)) {
						runValidatorAndTransform(subformDD, subformDSL.fields[subField], subformSource, subField);
					}
				}
			});
		});
	},
	unCheckAllChildTabs: (controllerSecId: JQuery, tab: string) => {
		const controller = $("#" + controllerSecId);
		const groupId = controller.attr("tabcontroller");
		const childControllers = $('[tabcontroller][parent_tabgroupid="' + groupId + '"]');
		childControllers.each((i, el) => {
			const cc = $(el);
			if (cc.attr("parent_tab") !== tab) return;
			cc.find(".tab-list-button").find("input").prop("checked", false).trigger("change");
		});
	},
	openTab: (controllerId: string, tab: string, forceOpen: boolean = false) => {
		if (!controllerId || !tab) return;
		const controller = $('[tabcontroller="' + controllerId + '"]');
		const btn = controller.find('.tab-list-button:not(.hide)[tab="' + tab + '"]');
		if (btn.length === 0) return;
		if (!forceOpen && !TabController.isParentTabActive(btn)) return;
		btn.click();
		const pcontroller = controller.attr("parent_tabgroupid") as string;
		const ptab = controller.attr("parent_tab") as string;
		TabController.openTab(pcontroller, ptab);
	},
	sameArea: (a: DSLSection, b: DSLSection) => {
		const areaA = a.area || "header";
		const areaB = b.area || "header";
		return areaA === areaB;
	},
	handleSectionShowHide: (dd: any, form: string, section: string, show: boolean) => {
		const dsl = window.DSL[form];
		if (!dsl) {
			console.error("DSL not found:", form);
		}
		if (!dsl.model.sections[section]) {
			console.error("Section not found:", section, form);
			return;
		}

		const secDef = dsl.model.sections[section];
		if (secDef.modal) {
			// This is a modal section
			return;
		}
		if (secDef.fields.length === 0) {
			// This is a section break
			return;
		}
		if (dd.sections[section]) {
			TabController.hideSection(dd.sections[section], show);
		} else if (
			dsl.model.sections[section].fields.length === 1 &&
			dsl.fields[dsl.model.sections[section].fields[0]].model.type === "subform"
		) {
			const sfv = dsl.fields[dsl.model.sections[section].fields[0]];
			let subform = sfv.model.source as string;

			if (subform.includes("{") || subform.includes("}")) {
				let ssm = window.DSLRecordSubform.get_subform_source(
					form,
					dsl.model.sections[section].fields[0],
					dd.values()
				);
				if (!ssm) {
					if (window.App.feature.debug) {
						console.info("Subform sourcefilter not found:", subform, form);
					}
					return;
				}
				subform = ssm[0];
			}
			const subformDD = dd.options.wrapper.subforms.formmap[subform];
			if (!subformDD) {
				console.error("Subform DD Not Found:", `${form}.${subform}`);
				return;
			}
			const subformDSL = window.DSL[subform];
			if (sfv.model.multi) {
				Object.values(subformDD.sections).forEach((secEl) => {
					TabController.hideSection(secEl as JQuery, show);
				});
				return;
			}
			for (const [secK, secV] of Object.entries(subformDSL.model.sections)) {
				if (TabController.sameArea(secDef, secV)) {
					TabController.handleSectionShowHide(subformDD, subform, secK, show);
				}
			}
			TabController.childTabControllersHideShow(subformDD, show);
		} else {
			console.error("This should never be called i messed something");
		}
	},
	childTabControllersHideShow: (dd: any, show: boolean) => {
		for (const [sectionName, secEl] of Object.entries(dd.sections || {})) {
			if (sectionName && sectionName.includes("__tg__")) {
				TabController.tabControllerHideShow(secEl as JQuery, show);
			}
		}
	},
	roundBtns: (tabController: JQuery) => {
		return;
		const tabs = tabController.find(".tab-list-button:not(.hide)");
		tabs.css("border-top-left-radius", "unset");
		tabs.css("border-top-right-radius", "unset");
		tabs.first().css("border-top-left-radius", "8px");
		tabs.last().css("border-top-right-radius", "8px");
	},
	tabControllerHideShow: (tabController: JQuery, show: boolean) => {
		TabController.roundBtns(tabController);
		if (!show) {
			tabController.css("display", "none");
			return;
		}
		const tabs = tabController.find(".tab-list-button:not(.hide)");
		if (tabs.length === 0) {
			tabController.css("display", "none");
			return;
		}
		if (!TabController.isParentTabActive(tabs.first())) {
			tabController.css("display", "none");
			return;
		}
		tabController.css("display", "");
		TabController.makeTabActiveIfNone(tabController);
	},
	findActiveBtn: (tabBtns: JQuery) => {
		let btn = null;
		let activeBtn = tabBtns.siblings(".tab-active").first();
		if (
			activeBtn.length == 1 &&
			(!activeBtn.find("input.tab-toggle").length || activeBtn.find("input.tab-toggle").is(":checked"))
		) {
			return activeBtn;
		}
		for (let i = 0; i < tabBtns.length; i++) {
			const tabEl = $(tabBtns[i]);
			if (tabEl.find("input.tab-toggle").length == 0) {
				btn = tabEl;
				break;
			}
			if (tabEl.find("input.tab-toggle").is(":checked")) {
				btn = tabEl;
				break;
			}
		}
		if (btn) {
			return btn.first();
		} else {
			return tabBtns.first();
		}
	},
	makeTabActiveIfNone: (tabController: JQuery) => {
		if (tabController.css("display") == "none") return;
		const tabBtnsVisible = tabController.find(".tab-list-button:not(.hide)");
		if (tabBtnsVisible.length === 0) return;
		TabController.findActiveBtn(tabBtnsVisible).click();
	},
	handleSectionTabEvent: (tabButton: JQuery, event: SyntheticEvent) => {
		const isParentTabActive = TabController.isParentTabActive(tabButton);
		if (!isParentTabActive) {
			return false;
		}

		tabButton.addClass("tab-active").siblings().removeClass("tab-active");
		const controller = tabButton.parent();
		if (!controller.data("id")) return;
		TabController.checkTabControllers(controller);

		const controllerData = controller.data();
		const activeTab = tabButton.attr("tab");
		if (tabButton.hasClass("toggle-tab") && event?.hasOwnProperty("originalEvent")) {
			const toggleCheckbox = tabButton.find("input");
			if (!toggleCheckbox.is(":disabled") && !toggleCheckbox.is(":checked")) toggleCheckbox.click();
		}
		// const tabControllerID = controller.attr("tabcontroller");
		if (!controllerData || _.isEmpty(controllerData)) return;
		const dd = controllerData.dd;
		const tabMap = dd?.section_tab_map?.[controllerData.id];
		if (!tabMap || _.isEmpty(tabMap)) return;
		for (const [tab, sections] of Object.entries(tabMap || {})) {
			for (const section of sections as Array<string>) {
				TabController.handleSectionShowHide(dd, dd.options.form, section, tab === activeTab);
			}
		}
	},
	hideSection: (secEl: JQuery | null, show: boolean) => {
		if (!secEl) return;
		const groupId = secEl.attr("secgroup");
		const gh = $("#" + groupId);
		const ghnote = $("#" + groupId + "_note");
		if (show) {
			secEl.css("display", "");
			gh.css("display", "");
			ghnote.css("display", "");
		} else {
			secEl.css("display", "none");
			gh.css("display", "none");
			ghnote.css("display", "none");
		}
	},
};

window.TabController = TabController;
