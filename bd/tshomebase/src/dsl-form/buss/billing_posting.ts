import currency from "currency.js";
import { FormBussCallbackCollection } from "./buss";

const billing_posting: FormBussCallbackCollection[] = [
	{
		mode: ["add", "addfill"],
		func: (data) => {
			const k = data.k;
			if (!k) {
				// how paranoid can we be? i'm 100% sure this will never happen (never say never)
				return;
			}
			if (k === "charge_line_postings" || k == "amount") {
				const amount = currency(data.dd.value_field("amount") || 0).value;
				const chargeLinePostings = JSON.parse(data.dd.value_field("charge_line_postings") || "[]");
				const appliedAmount = chargeLinePostings.reduce(
					(sum: number, item: any) => currency(sum).add(item.amount).value,
					0
				);
				let unappliedAmount = currency(amount).subtract(appliedAmount).value;
				if (unappliedAmount < 0) unappliedAmount = 0;
				data.dd.value_field("applied_amount", appliedAmount);
				data.dd.value_field("unapplied_amount", unappliedAmount);
				return;
			}
			if (
				!["patient_id", "payer_id", "site_id", "bill_type", "patient_id_filter", "payer_id_filter"].includes(k)
			) {
				return;
			}
			const dd = data.dd as any;
			const val = data.value;
			if (k === "patient_id" || k === "payer_id") {
				dd.arOptions?.clearSelections?.();
				return;
			}
			if (["site_id", "bill_type", "patient_id_filter", "payer_id_filter"].includes(k)) {
				const keyMap: Record<string, string> = {
					site_id: "site_id",
					patient_id_filter: "patient_id",
					payer_id_filter: "payer_id",
					bill_type: "bill_type",
				};
				dd.arOptions?.applyFilter?.(keyMap[k] || k, val || "");
			}
		},
	},
];

export default billing_posting;
