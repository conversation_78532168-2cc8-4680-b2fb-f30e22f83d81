import { FieldSubFormEventInfo, IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { CLJQueryObject } from "@typedefs/window";
import _ from "lodash";
import forms from "./index";
import { TabModes } from "@blocks/dsl-card-view";

type FormBussEvent = {
	dd: IDSLDrawSubForm; // dd of form
	form: string; // form name
	k: string; // field name
	d: CLJQueryObject; // d field object
	value: any; // value of field
	subformEvent?: FieldSubFormEventInfo; // subform event info object on available if field is subform
};

export type FormBussCallbackCollection = {
	mode: TabModes[];
	func: FormBussCallback;
};

export type FormBussCallback = (event: FormBussEvent) => void;

export class FormBuss {
	subscriptions: Array<FormBussCallback> = [];
	constructor() {}

	subscribe(fn: FormBussCallback) {
		this.subscriptions.push(fn);
	}

	subscribeFormCallbacks(form: string, mode: TabModes) {
		const formBuss = forms[form];
		if (!formBuss) return;
		const callbacks = formBuss.filter((cb) => cb.mode?.includes(mode));
		callbacks.forEach((cb) => this.subscribe(cb.func));
	}

	clear() {
		this.subscriptions = [];
	}

	async hopOnFormBuss(d: CLJQueryObject, subformEvent?: FieldSubFormEventInfo) {
		if (this.subscriptions.length === 0) return;
		const data = d.data();
		if (!data || _.isEmpty(data)) return;
		const dd = data.dd as IDSLDrawSubForm;
		if (!dd) return;
		const { changed, value } = dd.has_changed("EventBuss", data.k, true) as unknown as {
			value: any;
			changed: boolean;
		};
		if (!window.DSLDrawSubform.HAS_CHANGED_IGNORE_FIELD_TYPE.includes(data.fieldtype)) {
			if (!changed) {
				return;
			}
		}
		const event: FormBussEvent = {
			dd,
			form: data.form,
			k: data.k,
			d,
			value,
			subformEvent,
		};
		this.publishEvents(event);
	}

	publishEvents(event: FormBussEvent) {
		this.subscriptions.forEach((fn) => fn(event));
	}
}

export const createFormBuss = () => {
	return new FormBuss();
};
