import { <PERSON><PERSON>ield } from "@typedefs/coffee/dsl";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { CLJQueryObject } from "@typedefs/window";
import { areEqual } from "@utils/fx";
import _ from "lodash";

export const triggerIfConditions = (formmap: Record<string, IDSLDrawSubForm>, form: string, field: string) => {
	const parentDD = formmap[form];
	if (!parentDD) {
		return false;
	}
	const subFormField = window.DSL[form].fields[field];
	if (!subFormField) {
		return false;
	}
	if (subFormField.model.type != "subform") {
		return false;
	}

	let show = parentDD?.shmap?.fields?.[field]?.current;
	if (typeof show != "boolean") {
		show = true;
	}
	prefillSubFormIf(formmap, form, field, subFormField, show);
};

export const generatePrefillValues = (dd: IDSLDrawSubForm) => {
	if (!dd.if_by || dd.if_by.length == 0) {
		return {};
	}
	const pf = dd.get_parent_form();
	const pk = dd.get_field_in_parent();
	if (!pf || !pk) {
		return {};
	}
	const formmap = dd?.options?.wrapper?.subforms?.formmap;
	if (!formmap) {
		return {};
	}
	const parentDD = formmap[pf];
	if (!parentDD) {
		return {};
	}
	const preset: Record<string, any> = {};
	for (const ifk of dd.if_by) {
		const pf_field = window.DSL[pf].fields[ifk];
		if (!pf_field) {
			continue;
		}
		const if_condition = _.cloneDeep(pf_field.model.if);
		if (!if_condition || _.isEmpty(if_condition)) {
			continue;
		}
		let show = parentDD?.shmap?.fields?.[ifk]?.current;
		if (typeof show != "boolean") {
			show = true;
		}
		const val = show ? if_condition["*"]?.prefill?.[pk] : if_condition["!"]?.prefill?.[pk];
		if (!val || _.isEmpty(val)) {
			continue;
		}
		if (!Array.isArray(val) || val.length == 0) {
			continue;
		}
		const data = val[0];
		for (const [k, v] of Object.entries(data)) {
			if (k != "id" && k != "_meta" && k != "__index") {
				preset[k] = v;
			}
		}
	}
	return preset;
};

export const allSubformIfTriggersFields = (form: string, k: string) => {
	const dsl = window.DSL[form];
	if (!dsl) {
		return [];
	}
	if (!dsl.fields[k] || dsl.fields[k].model.type != "subform") {
		return [];
	}
	const fields: string[] = [];
	for (const [kx, vx] of Object.entries(dsl.fields)) {
		if (kx == k) {
			continue;
		}
		if (vx.model.type != "subform") {
			continue;
		}
		if (vx.model.if?.["*"]?.prefill?.[k] || vx.model.if?.["!"]?.prefill?.[k]) {
			fields.push(kx);
		}
	}
	return fields;
};
const getSubformActiveRowCount = (d: CLJQueryObject) => {
	const rows = (window.FieldSubform.value_get(d) || []).filter(
		(row: Record<string, any>) => !row?._meta?.delete && !row?._meta?.softdelete
	);
	return rows.length;
};

const shouldRun = (formmap: Record<string, IDSLDrawSubForm>, v: DSLField, field: string, show: boolean) => {
	if (!v.model.multi) {
		return show;
	}
	const dd = formmap[v.model.source as string]; // could be undefined if sourcefilter based subform
	if (!dd) {
		return false;
	}
	const field_nodes = dd.field_nodes;
	if (!field_nodes) {
		return false;
	}
	const d = field_nodes[field];
	if (!d) {
		return false;
	}
	const rows = getSubformActiveRowCount(d);
	if (rows > 0) {
		if (show) {
			return true;
		}
		return false;
	}
	return false;
};

// formmap is of forms at the same level
// form belongs to form in which subform is preset (i.e. parent form)
// field and v are the subform field and and DSL field definition
// show tell weather is visible by if condition
export const prefillSubFormIf = (
	formmap: Record<string, IDSLDrawSubForm>,
	form: string,
	field: string,
	v: DSLField,
	show: boolean
) => {
	if (!formmap) {
		console.error("Subform prefill: formmap not found");
		return;
	}
	if (!formmap[form]) {
		console.error("Subform prefill: form not found in formmap", form);
		return;
	}
	show = shouldRun(formmap, v, field, show);
	const if_condition = _.cloneDeep(v.model.if);
	if (!if_condition || _.isEmpty(if_condition)) {
		return;
	}
	const fields = window.DSL[form].fields;
	const prefill = show ? if_condition["*"]?.prefill : if_condition["!"]?.prefill;
	if (!prefill || _.isEmpty(prefill)) {
		return;
	}

	for (const key in prefill) {
		const field = fields[key];
		if (!field) {
			continue;
		}
		if (field.model.type != "subform") {
			continue;
		}
		if (Array.isArray(prefill[key])) {
			// Guess, Im mutating the original DSL
			if (prefill[key].length == 0) {
				prefill[key] = {};
			} else {
				prefill[key] = prefill[key][0];
			}
		}
		const value = prefill[key];
		if (typeof value != "object" || _.isEmpty(value)) {
			continue;
		}
		const sf_form = field.model.source as string;
		const source = window.DSL[sf_form];
		if (!source) {
			console.error(
				"SubForm DSL not found",
				sf_form,
				"It could be sourcefilter based subform form which is currently not supported"
			);
			continue;
		}
		const subformDD = formmap[sf_form];
		if (!subformDD) {
			console.error("SubForm DSLDraw not found", sf_form);
			continue;
		}
		if (field.model.multi) {
			prefillMultiTrueSubform(subformDD, key, value);
		} else {
			prefillEmbeddedSubform(subformDD, value);
		}
	}
};

const prefillMultiTrueSubform = (dd: IDSLDrawSubForm, key: string, preset: Record<string, any> = {}) => {
	const field_nodes = dd.field_nodes;
	if (!field_nodes) {
		return;
	}
	const d = field_nodes[key];
	if (!d) {
		return;
	}
	const rows = window.FieldSubform.value_get(d) || [];
	for (const row of rows) {
		if (!row.__index) {
			console.error("Subform prefill: row index not found");
			continue;
		}
		updateSubformRowIfNeeded(d, row.__index, row, preset);
	}
};

const updateSubformRowIfNeeded = (
	d: JQuery,
	index: number,
	row: Record<string, unknown>,
	preset: Record<string, unknown>
) => {
	let updateNeeded = false;
	for (const [k, v] of Object.entries(preset)) {
		if (k == "__index" || k == "_meta" || k == "id" || areEqual(row[k], v)) {
			// better to do a deep comparison then ending up with form rendered
			continue;
		}
		row[k] = v;
		updateNeeded = true;
	}
	if (updateNeeded) {
		window.FieldSubform.update_row_by_index(d, index, row, true);
	}
};

const prefillEmbeddedSubform = (dd: IDSLDrawSubForm, preset: Record<string, any> = {}) => {
	for (const [k, v] of Object.entries(preset)) {
		if (k == "id" || k == "_meta" || k == "__index") {
			continue;
		}
		const prev = dd.value_field(k);
		if (areEqual(prev, v)) {
			continue;
		}
		dd.value_field(k, v, true, true);
	}
};

window.getSubformActiveRowCount = getSubformActiveRowCount;
window.triggerIfConditions = triggerIfConditions;
window.prefillSubFormIf = prefillSubFormIf;
window.generatePrefillValues = generatePrefillValues;
window.allSubformIfTriggersFields = allSubformIfTriggersFields;
