import { DSLFormConfig } from "@dsl-form/config/types";
import _ from "lodash";

export const site: DSLFormConfig = {
	renderComponents: {},
	selectConfig: {
		filterFunction: (formData, dd, form, k, v) => {
			if (!formData) return [];
			if (formData.length === 0) return [];
			return formData
				.map((fd) => {
					if (fd.id == 1000 || fd.code == "TRANSFER") {
						return null;
					}
					if (form == "view_site_selector" || window.sitesSelected.length == 0) {
						return fd;
					}
					if (window.sitesSelected.length == 1 && window.sitesSelected[0] == 0) {
						return fd;
					}
					if (window.sitesSelected.includes(fd.id)) {
						return fd;
					}
					return null;
				})
				.filter((fd) => fd !== null);
		},
	},
};