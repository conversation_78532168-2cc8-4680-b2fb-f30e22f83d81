import _ from "lodash";
import { useMemo } from "react";
import { DSLFormConfig } from "@dsl-form/config/types";

import { billing_invoice } from "@dsl-form/config/billing_invoice";
import { ncpdp } from "@dsl-form/config/ncpdp";
import { patient_prior_auth } from "@dsl-form/config/patient_prior_auth";
import { inventory_serial } from "@dsl-form/config/inventory_serial";
import { site } from "@dsl-form/config/site";
import { careplan_order_rx } from "@dsl-form/config/careplan_order_rx";
import { ledger_charge_line } from "@dsl-form/config/ledger_charge_line";
import { print_labels_queue } from "@dsl-form/config/print_labels_queue";
import { inbound_fax_queue } from "./inbound_fax_queue";
import { send_fax_queue } from "./send_fax_queue";
import { cardfinder_payers_query } from "./cardfinder_payers_query";
import { payer_contract } from "@dsl-form/config/payer_contract";
import { careplan_delivery_tick } from "@dsl-form/config/careplan_delivery_tick";
import { patient_medication } from "@dsl-form/config/patient_medication";
export const listConfigs: Record<string, DSLFormConfig> = {
	patient_prior_auth: patient_prior_auth,
	billing_invoice: billing_invoice,
	ncpdp: ncpdp,
	inventory_serial: inventory_serial,
	careplan_order_rx: careplan_order_rx,
	site: site,
	payer_contract: payer_contract,
	ledger_charge_line: ledger_charge_line,
	careplan_delivery_tick: careplan_delivery_tick,
	__wf_queue_pharmacy_print_labels: print_labels_queue,
	__wf_queue_intake_inbound_fax: inbound_fax_queue,
	__wf_queue_faxes_fax_received: send_fax_queue,
	__wf_queue_faxes_fax_sent: send_fax_queue,
	__wf_queue_faxes_fax_scheduled: send_fax_queue,
	__query_module_cardfinder_payers: cardfinder_payers_query,
	patient_medication: patient_medication,
};

const defaultConfig: Required<DSLFormConfig> = Object.freeze({
	allowAdd: true,
	renderComponents: {},
	selectConfig: {},
	embedConfig: {},
	listWidget: null,
	listWidgetProps: {},
});

export const getFormConfig = (key: string): Required<DSLFormConfig> => {
	const formListConfig = listConfigs[key];
	if (!formListConfig) {
		return defaultConfig;
	}
	return _.merge({}, defaultConfig, formListConfig);
};

export const useFormConfig = (key: string): Required<DSLFormConfig> => {
	return useMemo(() => getFormConfig(key), [key]);
};
