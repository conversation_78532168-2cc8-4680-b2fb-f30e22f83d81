import type { TabData } from "@blocks/tab-list";
import { DSLFieldEmbedBtnsComponentProps } from "@dsl/fields/field-embed";
import { IFormData } from "@hooks/index";
import { DS<PERSON>ield } from "@typedefs/coffee/dsl";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";

export interface DSLFormConfig {
	allowAdd?: boolean;
	renderComponents?: Record<TabData["mode"], React.FC<any>>;
	selectConfig?: {
		fields?: "min" | "list" | "all";
		filterFunction?: (
			formData: IFormData[],
			dd: IDSLDrawSubForm,
			form: string,
			k: string,
			v: DSLField
		) => IFormData[];
	};
	embedConfig?: {
		[form: string]: {
			[field: string]: {
				btnComponent?: React.FC<DSLFieldEmbedBtnsComponentProps>;
			};
		};
	};
	listWidget?: React.FC<any> | null;
	listWidgetProps?: Record<string, any> | undefined;
}
