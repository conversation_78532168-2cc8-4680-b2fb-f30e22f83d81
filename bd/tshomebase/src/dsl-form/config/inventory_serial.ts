import { DSLFormConfig } from "@dsl-form/config/types";
import _ from "lodash";

export const inventory_serial: DSLFormConfig = {
	renderComponents: {},
	selectConfig: {
		fields: "all",
		filterFunction: (formData, dd, form, k, v) => {
			if (!formData) return [];
			if (formData.length === 0) return [];
			return _.uniqBy(formData, (fd) => {
				return fd.serial_no + "_" + fd.site_id;
			}).filter((fd) => {
				return fd.site_id != 1000;
			});
		},
	},
};
