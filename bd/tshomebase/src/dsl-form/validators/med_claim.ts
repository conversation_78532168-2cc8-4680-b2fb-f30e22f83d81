import { request } from "@core/request";
import {
    getParentFormNode,
    getFormNodeValues,
    getParentSubformNode,
} from "@utils/fx";

const getMMHealthcareInformationLoop = (dd: any) => {
    return getParentSubformNode(dd, 'med_claim_dx', 'health_care_code_information');
}

const getMMServicesSubformNode = (dd: any) => {
    return getParentSubformNode(dd, 'med_claim_sl', 'service_lines');
};

window.DSLFx.Validators.CheckDiagnoses = (form, dd, vld, f, k) => {
    try {
        const healthcareInformationLoopNode = getMMHealthcareInformationLoop(dd);
        const healthcareInformationValues = getFormNodeValues(healthcareInformationLoopNode);
        const claimLevelDxIds = (healthcareInformationValues || []).map(item => item.dx_id);

        const serviceLinesNode = getMMServicesSubformNode(dd);
        const serviceLinesValues = getFormNodeValues(serviceLinesNode);
        const serviceDxIds = [...new Set(
            (serviceLinesValues || []).flatMap(line => [
                line?.professional_service?.[0]?.dx_id_1,
                line?.professional_service?.[0]?.dx_id_2,
                line?.professional_service?.[0]?.dx_id_3,
                line?.professional_service?.[0]?.dx_id_4
            ]).filter(Boolean)
        )];

        const missingDxIds = serviceDxIds.filter(id => !claimLevelDxIds.includes(id));
        if (missingDxIds.length > 0) {
            return `The following diagnoses are used in service lines but missing from claim level diagnoses:\n${missingDxIds.join(', ')}`
        } else {
            return false;
        }
    } catch (error) {
        console.error("Error at CheckDiagnoses:", error);
        return `Error validating service line diagnoses.`
    }
}

window.DSLFx.Validators.LoadDependentInformation = (form, dd, vld, f, k) => {
    const LoadDependentInformation = async () => {
        let dependent_required = dd.value_field(k);
        let insurance_id = dd.value_field('insurance_id')
        let patient_id = dd.value_field('patient_id')
        if (dependent_required !== 'Yes' || !insurance_id || !patient_id) return;
        try {
            let obj_update_dep: any = {}
            let obj_update_dep_addr: any = {}
            let obj_update_dep_cont: any = {}
            let dependent_sf_ref = dd.options?.wrapper?.subforms?.formmap?.med_claim_dep
            let address_sf_ref = dependent_sf_ref.options?.wrapper?.subforms?.formmap?.med_claim_address_dep
            let contact_sf_ref = dependent_sf_ref.options?.wrapper?.subforms?.formmap?.med_claim_dep_cont
            const insurance = await request({
                url: `/form/patient_insurance/${insurance_id}`,
                method: 'GET',
            });
            if (insurance.data.length > 0) {
                obj_update_dep["relationship_to_subscriber_code"] = insurance.data[0].medical_relationship_id
                obj_update_dep["member_id"] = insurance.data[0].id
            }
            const patient = await request({
                url: `/form/patient/${patient_id}`,
                method: 'GET',
            });
            if (patient.data.length > 0) {
                obj_update_dep["ssn"] = patient.data[0].ssn
                obj_update_dep["first_name"] = patient.data[0].firstname
                obj_update_dep["last_name"] = patient.data[0].lastname
                obj_update_dep["middle_name"] = patient.data[0].middlename
                obj_update_dep["date_of_birth"] = patient.data[0].dob
                obj_update_dep["gender"] = patient.data[0].gender == 'Male' ? 'M' : patient.data[0].gender == 'Female' ? 'F' : 'U'
                obj_update_dep_addr["address1"] = patient.data[0].home_street1
                obj_update_dep_addr["address2"] = patient.data[0].home_street2
                obj_update_dep_addr["city"] = patient.data[0].home_city
                obj_update_dep_addr["state"] = patient.data[0].home_state_id
                obj_update_dep_addr["postal_code"] = patient.data[0].home_zip
                obj_update_dep_cont["name"] = `${patient.data[0].firstname} ${patient.data[0].lastname}`
                obj_update_dep_cont["phone"] = patient.data[0].phone_cell ? patient.data[0].phone_cell : patient.data[0].phone_home
                obj_update_dep_cont["email"] = patient.data[0].email
            }
            for (let key in obj_update_dep) {
                dependent_sf_ref.value_field(key, obj_update_dep[key], true, true);
            }
            for (let key in obj_update_dep_addr) {
                address_sf_ref.value_field(key, obj_update_dep_addr[key], true, true);
            }
            for (let key in obj_update_dep_cont) {
                contact_sf_ref.value_field(key, obj_update_dep_cont[key], true, true);
            }
        } catch (error) {
            console.log("Error while LoadDependentInformation:", error);
        }
    }
    LoadDependentInformation();
}

window.DSLFx.Validators.MedClaimBlock = (form, dd, vld, vals: any, k: string) => {
    if (vals?.invoice_no) {
        // Never edit a claim directly if tied to an invoice and not editing the invoice
        const parentNode = getParentFormNode(dd);
        if (!parentNode) return false;
    }
    return true;
}