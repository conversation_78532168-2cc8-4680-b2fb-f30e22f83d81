const SetValueField = (dd:any, field:string, value:string | number) => {
    dd.value_field(field, value, true, true);
};

const getMultiplier = (dd:any, baseMultiplierField:string, overrideMultiplierField:string) => {
    const baseMultiplier = parseFloat(dd.value_field(baseMultiplierField)) ?? 0;
    const overrideMultiplier = parseFloat(dd.value_field(overrideMultiplierField)) ?? 0;
    return overrideMultiplier || baseMultiplier;
};

const getPriceByFormula = (dd:any, formulaId:string, priceFields:any) => {
    const priceField = priceFields[formulaId] || null;
    return priceField ? parseFloat(dd.value_field(priceField)) : 0;
};

const calculatePrice = (dd:any, formulaIdField:string, baseMultiplierField:string, overrideFormulaIdField:string, overrideMultiplierField:string, resultField:string, priceFields:any) => {
    let formulaId = dd.value_field(formulaIdField);
    const overrideFormulaId = dd.value_field(overrideFormulaIdField);
    if (overrideFormulaId) {
        formulaId = overrideFormulaId;
    }

    const multiplier = getMultiplier(dd, baseMultiplierField, overrideMultiplierField);
    const price = getPriceByFormula(dd, formulaId, priceFields);

    SetValueField(dd, resultField, price * multiplier);
};

window.DSLFx.Validators.CalculateSpecialPrice = (form, dd, vld, f, k) => {
    const specialPriceFields = {
        'A': 'awp_price',
        'W': 'wac_price',
        'L': 'list_price',
        'C': 'last_cost',
        '1': 'add_price1',
        '2': 'add_price2'
    };

    calculatePrice(
        dd,
        'special_price_formula_id',
        'special_price_multiplier',
        'override_special_formula_id',
        'override_special_price_multiplier',
        'special_price',
        specialPriceFields
    );
};

window.DSLFx.Validators.CalculateExpectedPrice = (form, dd, vld, f, k) => {
    const expectedPriceFields = {
        'A': 'awp_price',
        'W': 'wac_price',
        'L': 'list_price',
        'C': 'last_cost',
        'ASP': 'asp_price',
        '1': 'add_price1',
        '2': 'add_price2'
    };

    calculatePrice(
        dd,
        'expected_price_formula_id',
        'expected_price_multiplier',
        'override_expected_formula_id',
        'override_expected_price_multiplier',
        'expected_price',
        expectedPriceFields
    );
};
