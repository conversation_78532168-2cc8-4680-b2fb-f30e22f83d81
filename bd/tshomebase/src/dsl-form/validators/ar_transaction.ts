import { request } from "@core/request";
import currency from "currency.js";

import {
    showToastError,
    justGiveMeAFuckingNumber,
} from "@utils/fx";

const setValueField = (dd: any, field: string, value: any) => {
    dd.value_field(field, value, true, true);
};

window.DSLFx.Validators.LoadARChargeLineInfo = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    const chargeLineId = dd.value_field(k);
    if (!chargeLineId) return;
    const LoadARChargeLineInfo = async () => {
        try {
            const response = await request({
                url: `/query/prefill_charge_line_ar_transaction/?x1=${chargeLineId}`,
                method: 'GET'
            });
            if (response.data.length > 0) {
                const item = response.data[0];
                for (const [key, value] of Object.entries(item)) {
                    setValueField(dd, key, value);
                }
            }
        } catch (error) {
            showToastError("Error fetching charge line information");
            console.log("Error at LoadARChargeLineInfo", error);
        }
    }
    LoadARChargeLineInfo();
}

window.DSLFx.Validators.ValidateARActionAmount = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    const amount = dd.value_field(k);
    const transactionType = dd.value_field('transaction_type');
    const adjustmentType = dd.value_field('adjustment_type');
    if (!amount || !transactionType) return;
    const totalBalanceDue = currency(justGiveMeAFuckingNumber(dd.value_field('total_balance_due'))).value;
    const toCheckAmount = currency(justGiveMeAFuckingNumber(amount)).value;
    const unappliedCashBalance = currency(justGiveMeAFuckingNumber(dd.value_field('unapplied_cash_balance') || 0.0)).value;

    const ValidateARActionAmount = async () => {
        try {
            switch(transactionType) {
                case "Adjustment":
                case "Writeoff":
                    if ((adjustmentType === "Credit" || adjustmentType === "Writeoff") && toCheckAmount > totalBalanceDue) {
                        return window.DSLFx.ValidateFieldError(dd.field_nodes["amount"], 'Total adjustment amount cannot exceed balance.');
                    }
                    break;
                case "Payment":
                    if (toCheckAmount > totalBalanceDue) {
                        return window.DSLFx.ValidateFieldWarning(dd.field_nodes["amount"], 'Payment amount exceeds balance due.');
                    }
                    break;
                case "Cash Allocation": {
                    if (toCheckAmount > totalBalanceDue) {
                        return window.DSLFx.ValidateFieldError(dd.field_nodes["unapplied_cash_applied"], 'Amount applied cannot be greater than balance.');
                    } else if (toCheckAmount > unappliedCashBalance) {
                        return window.DSLFx.ValidateFieldError(dd.field_nodes["unapplied_cash_applied"], 'Amount applied cannot be greater than amount available');
                    }
                    break;
                }
            }
        } catch (error) {
            showToastError("Error checking AR amount against transaction type");
            console.log("Error at ValidateARActionAmount", error);
        }
    }
    ValidateARActionAmount();
}

window.DSLFx.Validators.ARApplyUnappliedCash = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    const amount = dd.value_field(k);
    if (amount) return;
    const totalBalanceDue = currency(justGiveMeAFuckingNumber(dd.value_field('total_balance_due'))).value;
    const unappliedCashBalance = currency(justGiveMeAFuckingNumber(dd.value_field('unapplied_cash_balance') || 0.0)).value;
    if (totalBalanceDue > unappliedCashBalance) {
        dd.value_field('amount', unappliedCashBalance, true, true);
    } else {
        dd.value_field('amount', totalBalanceDue, true, true);
    }
}