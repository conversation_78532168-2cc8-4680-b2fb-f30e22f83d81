import { showToastError, checkIfFieldHasChanged, getFormNodeValuesWithMatchingProperties } from "@utils/fx";
import { getWTPulledFormNode, getWTToPullFormNode } from "./careplan_delivery_tick";
import { request } from "@core/request";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { fetchFormFilters, FetchFormResponse } from "@hooks/index";
import moment from "moment";

window.DSLFx.Validators.ValidateUniquePulledInventory = (form, dd, v, d, k) => {
    if (!checkIfFieldHasChanged(dd, 'ValidateUniquePulledInventory', k)) return;
    try {
        const uuid = dd.value_field('uuid');
        const inventoryId = dd.value_field('inventory_id');
        if (!inventoryId) return;
        if (!uuid) {
            showToastError("Cannot validate inventory item uniqueness.");
            return;
        }
        const pulledFormNode = getWTPulledFormNode(dd);
        const pulledRecords = getFormNodeValuesWithMatchingProperties(pulledFormNode, { uuid: uuid }) || [];
        const lotId = dd.value_field('lot_id') || null;
        const serialId = dd.value_field('serial_id') || null;
        const duplicateEntry = pulledRecords.find((record: any) => record.inventory_id === inventoryId && record?.lot_id === lotId && record?.serial_id === serialId);

        if (duplicateEntry) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes['inventory_id'], `Duplicate pulled item on ticket.`);
        }
    } catch (error) {
        console.error("Error validating unique pulled inventory:", error);
        showToastError("Error validating unique pulled inventory.");
    }
}
window.DSLFx.Transforms.LoadPulledDeliveryTicketItemData = (form, dd, trn, f, k) => {
    if (dd.options.mode == 'read') return false;
    const LoadPulledDeliveryTicketItemData = async () => {
        if (!checkIfFieldHasChanged(dd, 'LoadPulledDeliveryTicketItemData', k)) return;

        const clearFields = () => {
            dd.value_field('inventory_id', '', true, true);
            dd.value_field('delivery_ticket_item_uuid', '', true, true);
            dd.value_field('order_item_id', '', true, true);
            dd.value_field('ftr_dt_inv_id', '', true, true);
        }
        const dtItemId = dd.value_field('delivery_ticket_item_id');
        if (!dtItemId) {
            clearFields();
            return;
        }

        const response = await request({
            url: `/api/form/careplan_dt_item/?filter=id:${dtItemId}`,
        });
        if (response.data.length === 0) {
            showToastError("Associated delivery ticket item record not found.");
            clearFields();
            return;
        }

        const dtItemData = response.data[0];
        const inventoryFilter = Array.isArray(dtItemData.inv_fltr_id) ? (dtItemData.inv_fltr_id || []) : (JSON.parse(dtItemData.inv_fltr_id) || []);
        dd.value_field('ftr_dt_inv_id', inventoryFilter, true, true);
        const newInventoryId = dtItemData.inventory_id || '';
        dd.value_field('inventory_id', newInventoryId, true, true);

        const uuid = dtItemData.uuid;
        const orderItemId = dtItemData.order_item_id || '';
        dd.value_field('delivery_ticket_item_uuid', uuid, true, true);
        dd.value_field('order_item_id', orderItemId, true, true);

        const toPullFormNode = getWTToPullFormNode(dd);
        const toPullRecords = getFormNodeValuesWithMatchingProperties(toPullFormNode, { uuid: uuid }) || [];
        if (toPullRecords.length > 0) {
            const toPullRecord = toPullRecords[0];
            dd.value_field('to_pull_summary', toPullRecord.summary, true, true);
        }
    }
    LoadPulledDeliveryTicketItemData();
}

window.DSLFx.Transforms.LoadInventoryData = (form, dd, trn, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!checkIfFieldHasChanged(dd, 'LoadInventoryData', k)) return;
    const inventoryId = dd.value_field('inventory_id');
    if (!inventoryId) {
        dd.value_field('lot_tracking', '', true, true);
        dd.value_field('serial_tracking', '', true, true);
        return;
    };
    const LoadInventoryData = async () => {
        try {
            const inventory = await request({
                url: `/form/inventory/${inventoryId}`
            })
            if (inventory.data.length > 0) {
                const { lot_tracking, serial_tracking, type } = inventory.data[0]
                dd.value_field('lot_tracking', lot_tracking, true, true);
                dd.value_field('serial_tracking', serial_tracking, true, true);
                dd.value_field('inventory_type', type, true, true);
            }
        } catch (error) {
            console.error("Error while loading inventory data", error);
            showToastError(`Error loading inventory data`);
        }
    }
    LoadInventoryData();
}

window.DSLFx.Validators.AddAvailableLots = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'AddAvailableLots', k)) return;
    const lot_tracking = dd.value_field(k);
    const AddAvailableLots = async () => {
        if (lot_tracking === 'Yes') {
            try {
                const site_id = dd.value_field('site_id')
                const inventory_id = dd.value_field('inventory_id')
                const inStockLotSerialQueryData = await request({
                    url: `/query/get_instock_lot_and_serial_records/?x1=${inventory_id}&x2=${site_id}`,
                });
                if (inStockLotSerialQueryData.data.length > 0) {
                    const inStockLotSerialData = inStockLotSerialQueryData.data[0];
                    dd.value_field('flt_lot', inStockLotSerialData.lot_id, true, true);
                }
            } catch (error) {
                console.error("Error while adding available lots", error);
                showToastError(`Error adding available lots`);
            }
        } else {
            dd.value_field('flt_lot', '', true, true);
        }
    }
    AddAvailableLots();
}

window.DSLFx.Validators.AddAvailableSerials = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'AddAvailableSerials', k)) return;
    const serial_tracking = dd.value_field(k);
    const AddAvailableSerials = async () => {
        if (serial_tracking === 'Yes') {
            try {
                const site_id = dd.value_field('site_id')
                const inventory_id = dd.value_field('inventory_id')
                const inStockLotSerialQueryData = await request({
                    url: `/query/get_instock_lot_and_serial_records/?x1=${inventory_id}&x2=${site_id}`,
                });
                if (inStockLotSerialQueryData.data.length > 0) {
                    const inStockLotSerialData = inStockLotSerialQueryData.data[0];
                    dd.value_field('flt_ser', inStockLotSerialData.serial_id, true, true);
                }
            } catch (error) {
                console.error("Error while adding available lots", error);
                showToastError(`Error adding available lots`);
            }
        } else {
            dd.value_field('flt_ser', '', true, true);
        }
    }
    AddAvailableSerials();
}

window.DSLFx.Validators.CheckPulledData = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckPulledData', k)) return;
    try {
        const inventoryId = dd.value_field('inventory_id');
        if (!inventoryId) return;
        const pulledFormNode = getWTPulledFormNode(dd);
        const pulledRecords = getFormNodeValuesWithMatchingProperties(pulledFormNode, { inventory_id: inventoryId }) || [];
        const lotId = dd.value_field('lot_id') || null;
        const serialId = dd.value_field('serial_id') || null;
        const duplicateEntry = pulledRecords.find((record: any) => record.inventory_id === inventoryId && record?.lot_id === lotId && record?.serial_id === serialId);

        if (duplicateEntry) {
            window.prettyError('Duplicate inventory detected.', 'Proceeding to remove selected inventory for consistency.')
            dd.value_field('inventory_id', '', true, true);
        }
    } catch (error) {
        console.error("Error validating unique pulled inventory:", error);
        showToastError("Error validating unique pulled inventory.");
    }
}
// AutoFillQuantityPulled - load dispensed_quantity with either quantity_needed or quantity_on_hand, whichever is less. (2 hours)
window.DSLFx.Validators.AutoFillQuantityPulled = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckPulledData', k)) return;
    try {
        const quantityNeeded = dd.value_field('quantity_needed');
        const quantityOnHand = dd.value_field('quantity_on_hand');
        const ticketItemNo = dd.value_field('ticket_item_no');
        const dispenseUnitId = dd.value_field('dispensed_unit_id');
        if (parseFloat(quantityNeeded) == 0 || parseFloat(quantityOnHand) <= 0 || !quantityOnHand || !quantityNeeded) {
            showToastError("Quantity on hand or Quantity needed is missing");
        }
        const AutoFillQuantityPulled = async () => {
            const careplanDtWtPulled = await request({
                url: `/form/careplan_dt_wt_pulled/?filter=ticket_item_no:${ticketItemNo}&dispensed_unit_id:${dispenseUnitId}&void:!Yes`,
            })
            if (careplanDtWtPulled.data.length > 0) {
                const totalPulled = careplanDtWtPulled.data.reduce((acc: number, item: any) => acc + parseFloat(item.dispensed_quantity), 0);
                const totalNeeded = quantityNeeded;
                const quantityToPull = Math.max(
                    totalNeeded - totalPulled,
                    0
                );
                if (quantityToPull === 0)
                    return;
                if (quantityToPull < parseInt(quantityOnHand)) {
                    dd.value_field('dispensed_quantity', quantityToPull, true, true);
                } else if (parseInt(quantityOnHand) < parseInt(quantityNeeded)) {
                    dd.value_field('dispensed_quantity', quantityOnHand, true, true);
                }
            } else {
                if (parseFloat(quantityNeeded) < parseFloat(quantityOnHand)) {
                    dd.value_field('dispensed_quantity', quantityNeeded, true, true);
                } else if (parseFloat(quantityOnHand) < parseFloat(quantityNeeded)) {
                    dd.value_field('dispensed_quantity', quantityOnHand, true, true);
                }
            }
        }
        AutoFillQuantityPulled();
    } catch (error) {
        console.error("Error validating AutoFillQuantityPulled:", error);
        showToastError("Error validating AutoFillQuantityPulled.");
    }
}

window.DSLFx.Validators.LoadSerialData = (form, dd, vld, f, k) => {
    try {
        const LoadQuantityOnHand = async () => {

            const inventoryId = dd.value_field('inventory_id');
            if (!inventoryId) {
                dd.value_field('quantity_on_hand', '', true, true);
                return;
            }
            const siteId = dd.value_field('site_id');
            const lotId = dd.value_field('lot_id');
            const serialId = dd.value_field('serial_id');
            const queryLotId = lotId || 0;
            const querySerialId = serialId || 0;
            const response = await request({
                url: `/query/work_ticket_inventory_on_hand/?x1=${inventoryId}&x2=${siteId}&x3=${queryLotId}&x4=${querySerialId}`,
            });
            if (response.data.length === 0) {
                showToastError("Inventory quantity on hand not found.");
                return;
            }
            const inventoryData = response.data[0];
            dd.value_field('quantity_on_hand', inventoryData.quantity_on_hand, true, true);
            dd.value_field('serial_no', inventoryData.serial_no, true, true);
            dd.value_field('expiration_date', inventoryData.sl_expiration_date, true, true);
        }
        LoadQuantityOnHand();
    } catch (error) {
        console.error("Error validating LoadSerialData:", error);
        showToastError("Error validating Loading Quantity On Hand.");
    }
}

window.DSLFx.Validators.LoadLotData = (form, dd, vld, f, k) => {
    try {
        const LoadQuantityOnHand = async () => {
            const inventoryId = dd.value_field('inventory_id');
            if (!inventoryId) {
                dd.value_field('quantity_on_hand', '', true, true);
                return;
            }
            const siteId = dd.value_field('site_id');
            const lotId = dd.value_field('lot_id');
            const serialId = dd.value_field('serial_id');
            const queryLotId = lotId || 0;
            const querySerialId = serialId || 0;
            const response = await request({
                url: `/query/work_ticket_inventory_on_hand/?x1=${inventoryId}&x2=${siteId}&x3=${queryLotId}&x4=${querySerialId}`,
            });
            if (response.data.length === 0) {
                showToastError("Inventory quantity on hand not found.");
                return;
            }
            const inventoryData = response.data[0];
            dd.value_field('quantity_on_hand', inventoryData.quantity_on_hand, true, true);
            dd.value_field('lot_no', inventoryData.lot_no, true, true);
            if (inventoryData.lot_tracking == 'Yes') {
                dd.value_field('expiration_date', moment(inventoryData.il_expiration_date).isValid() ? moment(inventoryData.il_expiration_date).format('MM/DD/YYYY') : '', true, true);
            } else {
                dd.value_field('expiration_date', moment(inventoryData.sl_expiration_date).isValid() ? moment(inventoryData.sl_expiration_date).format('MM/DD/YYYY') : '', true, true);
            }
        }
        LoadQuantityOnHand();
    } catch (error) {
        console.error("Error validating LoadLotData:", error);
        showToastError("Error validating Loading Lot Quantity On Hand.");
    }
}

export const checkIfAllItemsPulled = async (dd: IDSLDrawSubForm, ticketNo: string) => {
    if (!ticketNo) {
        return;
    }
    if (!dd) {
        return;
    }
    try {

        fetchFormFilters('careplan_dt_item', {
            filter: {
                'ticket_no': ticketNo
            }
        }).then((resp) => {
            const allItemsPulled = (resp.data || []).every((item: any) => {
                return item.quantity_needed === 0;
            })
            dd.value_field('pulled_all_items', allItemsPulled ? 'Yes' : '', true, true, true);
            dd.value_field('tech_verified', allItemsPulled ? 'Yes' : '', true, true, true);
        })
    } catch (error) {
        console.error("Error in checkIfAllItemsPulled:", error);
    }
}

export const getPresetDTPulled = async (inventory_id: any, ticket_no: any, lot_no: any, serial_no: any, expiration_date: any) => {
    if (!inventory_id) {
        return {
            error: true,
            message: "Missing required parameters: inventory_id"
        };
    }

    try {
        const pulledRecord = await request({
            url: `/dispense?func=scan_dispensed_item`,
            method: "POST",
            data: {
                inventory_id,
                lot_no,
                serial_no,
                ticket_no,
                expiration_date
            }
        });
        return pulledRecord.data;
    } catch (error) {
        console.error("Error in getPresetDTPulled:", error);
        showToastError("Error preparing pulled inventory data");
        return {
            error: true,
            message: "Error preparing pulled inventory data"
        };
    }
}