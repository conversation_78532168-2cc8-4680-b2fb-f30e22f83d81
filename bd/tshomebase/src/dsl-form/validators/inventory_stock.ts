
import { checkIfFieldHasChanged, justGiveMeAFuckingNumber } from "@utils/fx";

window.DSLFx.Validators.ValidatedInventoryTransferQuantity = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'ValidatedInventoryTransferQuantity', k)) return;
    const quantity = window.DSLFields.value(dd.field_nodes['quantity']);
    const transferQuantity = window.DSLFields.value(dd.field_nodes['transfer_quantity']);

    if (justGiveMeAFuckingNumber(quantity) < justGiveMeAFuckingNumber(transferQuantity)) {
        return window.DSLFx.ValidateFieldError(dd.field_nodes['transfer_quantity'], `The transfer quantity cannot be greater than the quantity on hand`);
    }
}

window.DSLFx.Validators.ValidateConfirmedQuantity = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'ValidatedInventoryTransferQuantity', k)) return;
    const transferQuantity = window.DSLFields.value(dd.field_nodes['transfer_quantity']);
    const confirmedQuantity = window.DSLFields.value(dd.field_nodes['confirmed_quantity']);

    if (justGiveMeAFuckingNumber(confirmedQuantity) !== justGiveMeAFuckingNumber(transferQuantity)) {
        window.prettyError("Error", "The confirmed quantity must match the transfer quantity. Please add adjustments to the quantity to match the received quantity.");
        return window.DSLFx.ValidateFieldError(dd.field_nodes['confirmed_quantity'], `The confirmed quantity must match the transfer quantity.`);
    }
}

window.DSLFx.Validators.ValidatedInventoryAdjustmentQuantity = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'ValidatedInventoryAdjustmentQuantity', k)) return;
    const quantity = window.DSLFields.value(dd.field_nodes['quantity']);
    const adjustedQuantity = window.DSLFields.value(dd.field_nodes['adjusted_quantity']);

    if ((justGiveMeAFuckingNumber(adjustedQuantity) + justGiveMeAFuckingNumber(quantity)) < 0) {
        return window.DSLFx.ValidateFieldError(dd.field_nodes['adjusted_quantity'], `The adjusted quantity cannot be less than the quantity on hand.`);
    }
}