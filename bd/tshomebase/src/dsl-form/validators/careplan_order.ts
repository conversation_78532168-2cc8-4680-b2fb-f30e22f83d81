import moment from 'moment';
import _ from 'lodash';
import { request } from "@core/request";
import { showToastError,checkIfFieldHasChanged,justGiveMeAFuckingNumber } from "@utils/fx";

const processData = (data) => {
    let val = [];
    let arrId = [];
    let arrAutoName = [];
    for (let obj of data) {
        arrId.push(obj.id);
        arrAutoName.push(obj.auto_name);
    }
    arrId.map((idValue, index) => {
        val.push({
            id: idValue,
            text: arrAutoName[index] || '',
        });
    });

    return val;
}

window.DSLFx.Transforms.CalculateExpirationDate = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateExpirationDate', k)) return;

    try {
        const writtenDate = dd.value_field('written_date');
        if (!writtenDate) return;
        const expirationDate = moment(writtenDate).add(364, 'days').format('MM/DD/YYYY');
        dd.value_field('expiration_date', expirationDate, true, true);
    } catch (error) {
        console.error("Error while calculating expiration date:", error);
        showToastError(`Error calculating expiration date`);
    }
};

window.DSLFx.Transforms.PreventLongExpiration = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'PreventLongExpirationDate', k)) return;
    console.log('prevent expiry worked')
    try {
        const writtenDate = dd.value_field('written_date');
        const maxExpirationDate = dd.value_field('expiration_date');
        if (!writtenDate) return;
        const expirationDate = moment(writtenDate).add(365, 'days').format('MM/DD/YYYY');
        if (moment(maxExpirationDate).isAfter(expirationDate)) {
            showToastError(`Expiration date cannot be greater than one year from the written date`);
            dd.value_field('expiration_date', expirationDate, true, true);
        }
    } catch (error) {
        console.error("Error while preventing long expiration date:", error);
        showToastError(`Error preventing long expiration date`);
    }
}

window.DSLFx.Transforms.CalculateRefillsRemaining = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateRefillsRemaining', k)) return;
    const refills = dd.value_field('refills');
    if (!refills || refills === '0') return;
    try {
        const nextFillNumber = justGiveMeAFuckingNumber(dd.value_field('next_fill_number')) || 1;
        const refills = justGiveMeAFuckingNumber(dd.value_field('refills')) || 0;
        const refillsRemaining = refills - nextFillNumber + 1;
        dd.value_field('refills_remaining', refillsRemaining, true, true);
    } catch (error) {
        console.error("Error while calculating refills remaining:", error);
        showToastError(`Error calculating refills remaining`);
    }
};

window.DSLFx.Transforms.CalculateDosesRemaining = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateDosesRemaining', k)) return;

    try {
        const dosesAllowedVal = dd.value_field('doses_allowed');
        if (!dosesAllowedVal) return;
        const dosesAllowed = justGiveMeAFuckingNumber(dosesAllowedVal) || 0;
        const dosesDispensed = justGiveMeAFuckingNumber(dd.value_field('doses_dispensed')) || 0;
        const dosesRemaining = dosesAllowed - dosesDispensed;
        dd.value_field('doses_remaining', dosesRemaining, true, true);
    } catch (error) {
        console.error("Error while calculating doses remaining:", error);
        showToastError(`Error calculating doses remaining`);
    }
};

window.DSLFx.Transforms.LoadDefaultPayerAndDx = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LoadDefaultPayerAndDx', k)) return;

    try {
        const payerIds = dd?.field_nodes?.payer_ids?.data('helper')?.getAllData()?.data || [];
        const dxIds = dd?.field_nodes?.dx_ids?.data('helper')?.getAllData()?.data || [];
        if (payerIds.length > 0 || dxIds.length > 0) return;
        const patientId = dd.value_field('patient_id');

        const LoadDefaultPayerAndDx = async () => {
            const response = await request({
                url: `/query/prefill_order_payers_and_dx/?x1=${patientId}`,
            });
            if (response.data.length === 0) {
                console.error("Error fetching default payer and diagnosis");
                return null;
            }
            const defaultPayer = response.data[0].payer_ids;
            const defaultDx = response.data[0].dx_ids;
            window.FieldEmbed.value_set(dd.field_nodes.payer_ids, defaultPayer);
            window.FieldEmbed.value_set(dd.field_nodes.dx_ids, defaultDx);
        }
        LoadDefaultPayerAndDx();
    } catch (error) {
        console.error("Error while loading default payers and diagnosis:", error);
        showToastError(`Error loading default payer and diagnosis`);
    }
};

window.DSLFx.Validators.AggregateInsuranceIds = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;

    try {
        let payerIds = dd?.field_nodes?.payer_ids?.data('helper')?.getAllData()?.data || [];
        let payer_ids = dd.value_field('payer_ids')
        let mergedArray = []
        for (let obj of payerIds) {
            let samePayer = payer_ids.find((o:any) => o.id == obj.id);
            if (samePayer) {
                mergedArray.push(obj);
            }
        }
        const insuranceIds = processData(mergedArray)
        dd.value_field('insurance_id', insuranceIds, true, true);
    } catch (error) {
        console.error("Error while setting insurance ids:", error);
        showToastError(`Error aggregating order insurance records`);
    }
};


window.DSLFx.Validators.CheckInsuranceDenial = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckInsuranceDenial', k)) return;

    const getMedicalPayerIds = (careplan_order: any): Array<any> => {
        // Validate payer_ids field node exists
        if (!dd?.field_nodes?.payer_ids) return [];

        const data = window.DSLFields.value(dd.field_nodes.payer_ids);
        if (Array.isArray(data) && data.length > 0) return data;

        // Validate careplan_order and its properties
        if (!careplan_order || typeof careplan_order !== 'object') return [];

        // Handle Single Prescription format
        if (careplan_order.order_format === 'Single Prescription' && 
            Array.isArray(careplan_order.subform_prescription) && 
            careplan_order.subform_prescription.length > 0) {
            return careplan_order.subform_prescription[0]?.payer_ids || [];
        }

        // Handle Therapy Set format
        if (careplan_order.order_format === 'Therapy Set' && 
            Array.isArray(careplan_order.subform_order)) {
            return careplan_order.subform_order
                .filter((item:any) => item && typeof item === 'object')
                .flatMap((item:any) => Array.isArray(item.payer_ids) ? item.payer_ids : []);
        }

        return [];
    };

    try {
        const careplan_order = dd.values();
        const medical_payer_ids_node = dd?.field_nodes?.payer_ids;

        // Validate medical_payer_ids_node and its helper
        if (!medical_payer_ids_node?.data('helper')?.getAllData) {
            console.warn('Medical payer IDs node or helper not properly initialized');
            return false;
        }

        const allData = medical_payer_ids_node.data('helper').getAllData();

        // Validate allData structure
        if (!allData || 
            allData.state === 'pending' || 
            !Array.isArray(allData.data) || 
            allData.data.length === 0) {
            return false;
        }

        // Get and validate medical payer IDs
        let data = window.DSLFields.value(medical_payer_ids_node);
        if (!Array.isArray(data) || data.length === 0) {
            data = careplan_order?.subform_order
                ?.filter((item:any) => item && typeof item === 'object')
                ?.flatMap((item:any) => Array.isArray(item.medical_payer_ids) ? item.medical_payer_ids : []) 
                || [];
        }

        const payerIds = getMedicalPayerIds(careplan_order);
        if (!Array.isArray(payerIds)) {
            console.error("Invalid payer IDs format");
            return false;
        }

        // Check for denial billing
        const hasDenialBilling = payerIds.some(payer =>
            payer?.id && allData.data.some((data: any) =>
                data?.id === payer.id && data.bill_for_denial === 'Yes'
            )
        );

        if (hasDenialBilling && !dd.insuranceErrorShowed) {
            const errorMessage = 'Selected medical insurance is set to bill for denial. Please select another insurance to continue.';
            window.prettyError('Error', errorMessage);
            dd.insuranceErrorShowed = true;
            return errorMessage;
        }

        dd.insuranceErrorShowed = false;
        return false;

    } catch (error) {
        console.error("Error while checking the insurance denial:", error);
        return false;
    }
}

window.DSLFx.Validators.CopyForwardValueToParent = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CopyForwardValueToParent', k)) return;
    const {field, overwrite} = vld;
    if (!field || !overwrite) {
        console.warn('Field or overwrite is not set in CopyForwardValueToParent validator');
        return;
    }
    const value = dd.value_field(k);
    try {
        const parentForm = dd?.options?.wrapper?.subforms?.formmap?.careplan_order_rx;
        if (parentForm) {
            if (overwrite && value !== parentForm.value_field(field)) {
                parentForm.value_field(field, value, true, true);
            } else if (!parentForm.value_field(field)) {
                parentForm.value_field(field, value, true, true);
            }
        }
    } catch (error) {
        console.error("Error while copying value to parent:", error);
        showToastError(`Error copying value to parent`);
    }
}

window.DSLFx.Validators.CopyForwardValueToChild = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CopyForwardValueToChild', k)) return;
    const {field, subform_field, overwrite} = vld;
    if (!field || !subform_field || !overwrite) {
        console.warn('Field or subform_field or overwrite is not set in CopyForwardValueToChild validator');
        return;
    }
    const value = dd.value_field(k);
    try {
        const formName = dd?.options?.wrapper?.subforms?.field?.[subform_field]
        if (!formName) {
            console.warn('Form name is not set in CopyForwardValueToChild validator');
            return;
        }
        const subform = dd?.options?.wrapper?.subforms?.formmap?.[formName]
        if (subform) {
            if (overwrite && value !== subform.value_field(field)) {
                subform.value_field(field, value, true, true);
            } else if (!subform.value_field(field)) {
                subform.value_field(field, value, true, true);
            }
        }
    } catch (error) {
        console.error("Error while copying value to child:", error);
        showToastError(`Error copying value to child`);
    }
}