import { toast } from "@components/toast";
import { request } from "@core/request";
import { fieldReadOnly } from "@utils/dsl-fx";
import { checkIfFieldHasChanged, getFormData, justGiveMeAFuckingNumber } from "@utils/fx";
import _, { set } from "lodash";

const setValueField = (dd: any, field: string, value: any) => {
    dd.value_field(field, value, true, true);
};

window.DSLFx.Validators.LoadFDBData = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LoadFDBData', k)) return;

    const LoadFDBData = async () => {
        const ndc = dd.value_field('fdb_id');
        if (!ndc) {
            const fieldsToClear = [
                'ndc',
                'formatted_ndc',
                'gcn_seqno',
                'medid',
                'manufacturer_id',
                'active',
                'name',
                'dea_schedule_id',
                'therapy_class',
                'hcpc_id',
                'hcpc_code',
                'brand_name_id',
                'generic_name',
                'obsolete_date',
                'cmn_description',
                'hcpc_quantity',
                'hcpc_price',
                'hcpc_unit',
                'quantity_each',
                'available_routes',
                'route_id',
                'billing_unit_id',
                'metric_unit_each',
                'additional_descriptor',
                'dosage_form',
                'strength',
                'strength_unit_id',
                'volume',
                'volume_unit_id',
                'dosage_form_type',
                'concentration',
                'concentration_unit',
                'wac_price',
                'avg_acq_cost_brand',
                'avg_acq_cost_gen',
                'ful',
                'awp_price',
                'awp_basis',
                'storage_id',
                'quantity_per_case',
                'dosing_unit_per_each',
                'default_dosing_unit_id',
                'quantity_per_package',
                'awp_price_pkg',
                'wac_price_pkg',
                'asp_price',
                'report_unit_id',
                'requires_nursing'
            ];
            for (const field of fieldsToClear) {
                setValueField(dd, field, '');
            }
            return;
        }
        try {
            const response = await request({
                url: `/query/inventory_fill_up_query/?x1=${ndc}`,
                method: 'GET'
            });
            if (response.data.length > 0) {
                const item = response.data[0];
                for (const [key, value] of Object.entries(item)) {
                    setValueField(dd, key, value);
                }
            }
        } catch (error) {
            console.error("Error at LoadFDBData:", error);
            window.prettyError("Concentration Error", "Unable to support selected concentration. Please contact support for assistance");
        }
    }
    LoadFDBData();
}

window.DSLFx.Validators.LoadFDBSupplyData = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LoadFDBSupplyData', k)) return;

    const LoadFDBSupplyData = async () => {
        const ndc = dd.value_field('supply_fdb_id');
        if (!ndc) {
            const fieldsToClear = [
                'quantity_per_container',
                'manufacturer_id',
                'purchase_unit',
                'container_unit',
                'each_per_container',
                'last_cost_pu',
                'last_cost_cont',
                'hcpc_unit_limit',
                'hcpc_limit_freq',
                'iv_container',
                'iv_container_volume',
                'active',
                'supply_billable',
                'hcpc_billable',
                'lot_tracking',
                'hcpc_not_covered',
                'accept_assignment',
                'hcpc_span_dates',
                'note',
                'wac_price_pkg',
                'list_price',
                'name',
                'awp_price_pkg'
            ];
            for (const field of fieldsToClear) {
                setValueField(dd, field, '');
            }
            return
        };
        try {
            const response = await request({
                url: `/query/load_inv_fdb_supply/?x1=${ndc}`,
                method: 'GET'
            });
            if (response.data.length > 0) {
                const item = response.data[0];
                for (const [key, value] of Object.entries(item)) {
                    setValueField(dd, key, value);
                }
            }
        } catch (error) {
            console.error("Error at LoadFDBSupplyData:", error);
            window.prettyError("Concentration Error", "Unable to support selected concentration. Please contact support for assistance");
        }
    }
    LoadFDBSupplyData();
}

window.DSLFx.Validators.SettingCautionLabels = (form, dd, vld, f, k) => {
    if (dd?.field_nodes?.embed_labels?.data('helper')) {
        dd?.field_nodes?.embed_labels.data('helper')?.refreshGrid()
    }
};


window.DSLFx.Validators.RecalcAWPWACPackage = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'RecalcAWPWACPackage', k)) return;

    const RecalcAWPWACPackage = async () => {
        const awpPriceEach = justGiveMeAFuckingNumber(dd.value_field('awp_price')) || 0.0;
        const quantityPerPackage = justGiveMeAFuckingNumber(dd.value_field('quantity_per_package')) || 0.0;
        const awpPricePackage = awpPriceEach * quantityPerPackage;
        setValueField(dd, 'awp_price_pkg', awpPricePackage);
        const wacPriceEach = justGiveMeAFuckingNumber(dd.value_field('wac_price')) || 0.0;
        const wacPricePackage = wacPriceEach * quantityPerPackage;
        setValueField(dd, 'wac_price_pkg', wacPricePackage);
    }
    RecalcAWPWACPackage();
}

window.DSLFx.Validators.SetCautionLabelsInv = (form, dd, vld, f, k) => {
    // Validate required inputs
    if (!f || !dd) {
        console.error('SetCautionLabelsInv: Missing required parameters');
        return;
    }

    // Validate helper data
    const helper = f.data('helper');
    if (!helper) {
        console.warn('SetCautionLabelsInv: No helper data found');
        return;
    }

    const data = window.DSLFields.value(f);
    if (!Array.isArray(data)) {
        console.error('SetCautionLabelsInv: Invalid data format');
        return;
    }

    // Get all data and validate state
    const allData = helper.getAllData();
    if (!allData || allData.state === 'pending' || !Array.isArray(allData.data) || allData.data.length === 0) {
        return;
    }

    // Process caution labels
    const cautionLabels = allData.data
        .filter(obj => data.some((o: any) => o.id === obj.id))
        .map(obj => obj.lbl_desc)
        .filter(label => label && label.trim() !== '');

    // Get field configuration
    const field = 'caution_label';
    if (!window.DSL['inventory']?.fields?.[field]) {
        console.warn('SetCautionLabelsInv: Unable to determine max characters');
        return;
    }
    const maxChars = Number(window.DSL['inventory']?.fields[field]?.model?.max) || 256;
    if (!maxChars) {
        console.warn('SetCautionLabelsInv: Unable to determine max characters');
        return;
    }

    const threshold = 0.8;
    let toRemove = null;
    
    // Combine caution labels with validation
    const combinedCautions = _.reduce(cautionLabels, (result, label) => {
        if (!label) return result;

        const newLength = (result + (result ? ' ' : '') + label).length;
        
        if (newLength <= maxChars && label.endsWith('.')) {
            return result + (result ? ' ' : '') + label;
        } else if (newLength <= parseInt((maxChars * threshold).toString())) {
            return result + (result ? ' ' : '') + label;
        } else {
            toRemove = allData.data.find((o: any) => o.lbl_desc === label) || null;
            toast({ 
                type: 'warning', 
                message: `Selected label exceeds the allowed length of ${maxChars} characters.`
            });
        }
        return result;
    }, '');

    // Update values
    if (toRemove) {
        const updated = data.filter((obj: any) => obj.id !== toRemove.id);
        window.DSLFields.value(f, updated);
    }

    // Validate field node exists before setting value
    if (!dd.field_nodes[field]) {
        console.error('SetCautionLabelsInv: Field node not found');
        return;
    }

    window.DSLFields.value(dd.field_nodes[field], combinedCautions);
};

window.DSLFx.Validators.SettingListPrice = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'SettingListPrice', k)) return;
    const SettingListPrice = async () => {
        let price_code_id = dd.value_field('price_code_id');
        if (!price_code_id) {
            setValueField(dd, 'list_price', 0);
            return
        };
        try {
            const site_price_code_item = await request({
                url: `/view/site_price_code_item?filter=price_code_id:${price_code_id || -1}`,
                method: 'GET'
            });
            const multiplier = site_price_code_item?.data[0]?.multiplier ? parseFloat(site_price_code_item.data[0].multiplier) : 0
            switch (site_price_code_item?.data[0]?.price_formula_id) {
                case 'A':
                    const awpPrice = dd.value_field('awp_price') || 0.0;
                    setValueField(dd, 'list_price', parseFloat(awpPrice) * multiplier);
                    break;
                case 'W':
                    const wacPrice = dd.value_field('wac_price') || 0.0;
                    setValueField(dd, 'list_price', parseFloat(wacPrice) * multiplier);
                    break;
                case '1':
                    const addPrice1 = dd.value_field('add_price1') || 0.0;
                    setValueField(dd, 'list_price', parseFloat(addPrice1) * multiplier);
                    break;
                case '2':
                    const addPrice2 = dd.value_field('add_price2') || 0.0;
                    setValueField(dd, 'list_price', parseFloat(addPrice2) * multiplier);
                    break;
                case 'ASP':
                    const aspPrice = dd.value_field('asp_price') || 0.0;
                    setValueField(dd, 'list_price', parseFloat(aspPrice) * multiplier);
                    break;
                case 'C':
                    const cost = dd.value_field('cost_ea') || 0.0;
                    setValueField(dd, 'list_price', parseFloat(cost) * multiplier);
                    break;
                default:
                    setValueField(dd, 'list_price', 0);
            }
        } catch (error) {
            console.log("Error at Setting List Price:", error);
        }
    }
    SettingListPrice();
}

window.DSLFx.Validators.InventoryType = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'InventoryType', k)) return;
    let type = dd?.preset?.type
    if (type == 'Drug') {
        dd.value_field('lot_tracking', 'Yes', true, true);
        fieldReadOnly(dd, dd.field_nodes['lot_tracking'], true,  'type');
        fieldReadOnly(dd, dd.field_nodes['billing_unit_id'], true, 'type');
    } else if (type == 'Supply') {
        let field_node = dd.field_nodes['ndc'];
        field_node.removeAttr('readonly');
    } else if (type == 'Compound') {
        let field_node = dd.field_nodes['quantity_each'];
        field_node.removeAttr('readonly');
        fieldReadOnly(dd, dd.field_nodes['is_billed_compound'], true, 'type');
        dd.field_nodes['is_billed_compound'].closest('.form-horizontal').addClass('offscreen')
    } else if (type == 'Equipment Rental') {
        dd.value_field('serial_tracking', 'Yes', true, true);
        fieldReadOnly(dd, dd.field_nodes['serial_tracking'], true, 'type');
    }
}

window.DSLFx.Validators.SetCompoundDEASchedule = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'SetCompoundDEASchedule', k)) return;
    const SetCompoundDEASchedule = async () => {
        let cmp_ingredients = dd.value_field('cmp_ingredients');
        if (!cmp_ingredients) {
            return
        }
        try {
            let inventory_data = await request({
                url: `/form/inventory/`,
                method: 'GET'
            });
            if (inventory_data.data.length > 0) {
                const priority = ['Schedule 5', "Schedule 4", "Schedule 3", "Schedule 2", "Schedule 1"];
                for (let p of priority) {
                    const item = inventory_data.data.find(item => item.dea_schedule_id_auto_name === p);
                    if (item) {
                        dd.value_field('dea_schedule_id', item.dea_schedule_id, true, true);
                    }
                }
            }
        } catch (e) {
            console.log("Error at Set Compound DEA Schedule:", e);
        }
    }
    SetCompoundDEASchedule();
}

window.DSLFx.Validators.CheckCautionLabel = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckCautionLabel', k)) return;
    let val = window.DSLFields.value(f)
    if (val && val?.length > 58) {
        window.DSLFx.ValidateFieldWarning(f, "Caution label exceeds 58 characters and might not fit on the label. Please adjust appropriately.")
    } else {
        window.DSLFx.ValidateFieldWarning(f, false)
    }
}


window.DSLFx.Transforms.BrandNameSplit = (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'BrandNameSplit', k)) return;
    let val = dd.value_field(k);
    if (!val || dd.brand_name == val) return;
    dd.brand_name = ''
    dd.brand_name = val.split(' ')[0];
    dd.value_field(k, val.split(' ')[0], true, true);
}

window.DSLFx.Validators.CalcPercentages = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalcPercentages', k)) return;
    if (!vld.ing_field || !vld.percent_field) return;
    let node;
    if (typeof form == 'string' && window.DSL[form]?.fields[vld.ing_field]?.model?.type === 'subform') {
        node = dd?.options?.wrapper?.subforms?.formmap[window.DSL[form].fields[vld.ing_field].model.source]?.field_nodes[vld.ing_field]
    } else {
        node = dd?.field_nodes[vld.ing_field];
    }
    if (!node) return;
    let ingredients = window.DSLFields.value(node)
    if (!ingredients || !Array.isArray(ingredients) || ingredients.length === 0) return;
    const totalPercent = ingredients.reduce((acc, item) => acc + parseInt(item.percent || 0), 0);
    dd.value_field(vld.percent_field, totalPercent, true, true);
}

window.DSLFx.Validators.ValidatePercentage = (form, dd, vld, f, k, flv) => {
    if (!checkIfFieldHasChanged(dd, 'ValidatePercentage', k)) return;
    let val = parseInt(dd.value_field(k))
    if (!val) return;
    if (val !== 100) {
        return window.DSLFx.ValidateFieldError(f, 'The total percentage of ingredients must be 100%. Please adjust the percentages of the ingredients and try again.')
    } else {
        return false
    }
}
const generateInventoryNotes = (inventory) => {
    let noteIndex = 1;
    let notes = "";
    inventory.forEach((obj: any) => {
        notes += `Ingredient: ${obj.auto_name}\n`;
        for (let i = 1; i <= 6; i++) {
            const cautionLabel = obj[`caution_label${i}`];
            if (cautionLabel) {
                notes += `Warning Label ${i}: ${cautionLabel}\n`;
            }
        }
        noteIndex++;
    });

    return notes || null;
}

window.DSLFx.Validators.SetWarningLabels = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'SetWarningLabels', k)) return;
    let ingredients = window.DSLFields.value(dd.field_nodes['cmp_ingredients'])
    dd.value_field('ing_warning_labels', '', true, true);
    if (!ingredients || !Array.isArray(ingredients) || ingredients.length === 0) return;
    const activeIngredents = ingredients.filter((obj) => obj.active_ingredient == 'yes')
    if (activeIngredents.length == 0) return;
    const SetWarningLabels = async () => {
        try {
            const filterString = activeIngredents.map(obj => `filter=id:${obj.inventory_id}`).join('&');
            const response = await request({
                url: `/form/inventory?${filterString}`,
            });
            if (response.data.length > 0) {
                let notes = generateInventoryNotes(response.data);
                if (notes)
                    dd.value_field('ing_warning_labels', notes, true, true);
            } else {
                console.log("No data found for the given filter");
            }
        } catch (error) {
            console.log("Error at SetWarningLabels", error);
        }
    }
    SetWarningLabels();
}