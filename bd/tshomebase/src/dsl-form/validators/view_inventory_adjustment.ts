import { request } from "@core/request";
import { checkIfFieldHasChanged, showToastError } from "@utils/fx";

window.DSLFx.Validators.CheckInventoryOnHand = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckInventoryOnHand', k)) return;
    const lot_id = dd.value_field('lot_id');
    const serial_id = dd.value_field('serial_id');
    const site_id = dd.value_field('site_id');
    const inventory_id = dd.value_field('inventory_id');
    dd.value_field('quantity', '', true, true)
    window.DSLFields.readonly(dd.field_nodes['quantity'], false);
    try {
        if (!inventory_id || !site_id) return;
        const CheckInventoryOnHand = async () => {
            let url = '/inventory?func=check_stock&site_id=' + site_id + '&inventory_id=' + inventory_id
            if (lot_id)
                url += '&lot_id=' + lot_id
            if (serial_id)
                url += '&serial_id=' + serial_id
            const quantity_on_hand_site = await request({
                url: url
            })

            const setQuantity = (value:number) => {
                dd.value_field('quantity', value, true, true);
            };

            const results = quantity_on_hand_site?.data?.results;
            
            if (serial_id && results?.sni_quantity === 0) {
                setQuantity(1);
                window.DSLFields.readonly(dd.field_nodes['quantity'], true);
            } else if (lot_id && results?.lot_quantity === 0) {
                setQuantity(1);
            } else if (results?.inv_quantity === 0) {
                setQuantity(1);
            } else if (serial_id && results?.sni_quantity > 0) {
                setQuantity(-1);
                window.DSLFields.readonly(dd.field_nodes['quantity'], true);
            } else if (lot_id && results?.lot_quantity > 0) {
                setQuantity(-1);
            } else if (results?.inv_quantity > 0) {
                setQuantity(-1);
            } else {
                setQuantity(1);
            }
        }
        CheckInventoryOnHand()
    } catch (error) {
        console.log("Error while fetching quantity on hand of desired item.")
        showToastError('Error while fetching quantity on hand of desired item.')
    }
}

window.DSLFx.Validators.SetSerialAndLots = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'SetSerialAndLots', k)) return;
    const hide_serial_lots = dd.value_field('hide_serial_lots')
    if (hide_serial_lots == 'Yes') {
        dd.value_field('serial', [], true, true);
        dd.value_field('lot', [], true, true);
        return
    };

    try {
        const GetSerialsAndLots = async () => {
            const [serial, lots] = await Promise.all([
                request({
                    url: '/form/inventory_serial'
                }),
                request({
                    url: '/form/inventory_lot'
                })
            ])
            if (serial?.data && serial?.data?.length > 0) {
                dd.value_field('serial', serial.data, true, true);
            }
            if (serial?.data && lots?.data?.length > 0) {
                dd.value_field('lot', lots.data, true, true);
            }
        }
        GetSerialsAndLots()
    } catch (error) {
        console.log('Error while fetching serials and lots', error);
        showToastError('Error while fetching serials and lots')
    }
}
