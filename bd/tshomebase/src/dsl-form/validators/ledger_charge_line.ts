import { request } from "@core/request";
import { showToastError, checkIfFieldHasChanged, justGiveMeAFuckingNumber } from "@utils/fx";
import currency from "currency.js";

const setValueField = (dd: any, field: string, value: any) => {
    dd.value_field(field, value, true, true);
};

const fetchChargeBalances = async (dd: any) => {
        try {
            const invoiceNo = dd.value_field('invoice_no');
            const response = await request({
                url: `/query/fetch_charge_line_balances/?x1=${invoiceNo}`,
                method: 'GET'
            });
            if (response.data.length === 0) {
                setValueField(dd, 'view_total_paid', 0.00);
                setValueField(dd, 'view_total_adjusted', 0.00);
                setValueField(dd, 'view_total_balance_due', 0.00);
                setValueField(dd, 'view_total_cost', 0.00);
                setValueField(dd, 'view_total_expected', 0.00);
                showToastError("Error fetching charge line balances");
                return;
            }

            const item = response.data[0];
            for (const [key, value] of Object.entries(item)) {
                setValueField(dd, key, value);
            }
        } catch (error) {
            showToastError("Error fetching charge line balance information");
            console.log("Error at fetchChargeBalances", error);
        }
}
const loadPricingInfo = async (dd: any, inventoryId: string | number, insuranceId: string | number, siteId: string | number, patientId: string | number) => {
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const response = await request({
            url: `/query/inventory_payer_pricing/?x1=${inventoryId}&x2=${insuranceId}&x3=${siteId}&x4=${patientId}`,
            method: 'GET'
        });

        const result = response?.data?.[0] || { error: 'Missing payer pricing data'};
        if (result.error) {
            console.error("Error at loadPricingInfo:", result.error);
            showToastError(result.error);
            return;
        }
        const parentChargeNo = dd.value_field('parent_charge_no') || null;

        if (dd?.options?.form === 'ledger_charge_line') {
            if (result.billable !== 'Yes' && !parentChargeNo) {
                showToastError(`Item is not billable for the selected insurance.`);
                return;
            }
        }

        const hcpcCode = result.hcpc_code || null;
        setValueField(dd, 'hcpc_code', hcpcCode);
        if (dd?.options?.form === 'ledger_charge_line') {
            const revenueCodeId = result.revenue_code_id || null;
            setValueField(dd, 'revenue_code_id', revenueCodeId);
        }

        const billingUnitId = result.billing_unit_id || null;
        setValueField(dd, 'billing_unit_id', billingUnitId);
        const chargeUnit = result.charge_unit || null;
        setValueField(dd, 'charge_unit', chargeUnit);
        const billingMethodId = result.billing_method_id || null;
        setValueField(dd, 'billing_method_id', billingMethodId);

        const invType = dd.value_field('inventory_type') || null;
        const rentalType = dd.value_field('rental_type') || null;
        if (parentChargeNo) {
            return;
        }

        if (invType === 'Equipment Rental' && rentalType === 'Rental') {
            const rentalFrequency = dd.value_field('frequency_code') || null;
            if (!rentalFrequency) {
                showToastError('Rental frequency is required to calculate rental pricing.');
                return;
            }
            // Daily
            switch (rentalFrequency) {
                case '6': // Daily
                    const dailyRentalListPrice = currency(result.daily_rental_list_price || 0.00).value;
                    setValueField(dd, 'calc_list_ea', dailyRentalListPrice);
                    if (dd?.options?.form === 'ledger_charge_line') {
                        const dailyRentalCost = currency(result.daily_rental_cost || 0.00).value;
                            setValueField(dd, 'calc_cost_ea', dailyRentalCost);
                    }
 
                    const dailyRentalExpected = currency(result.rental_expected_daily_ea || 0.00).value;
                    setValueField(dd, 'calc_expected_ea', dailyRentalExpected);
                    break;
                case '4': // Monthly
                    const monthlyRentalListPrice = currency(result.monthly_rental_list_price || 0.00).value;
                    setValueField(dd, 'calc_list_ea', monthlyRentalListPrice);
                    if (dd?.options?.form === 'ledger_charge_line') {
                        const monthlyRentalCost = currency(result.monthly_rental_cost || 0.00).value;
                        setValueField(dd, 'calc_cost_ea', monthlyRentalCost);
                    }

                    const monthlyRentalExpected = currency(result.rental_expected_monthly_ea || 0.00).value;
                    setValueField(dd, 'calc_expected_ea', monthlyRentalExpected);
                    break;
                case '1': // Weekly
                    const weeklyRentalListPrice = currency((result.monthly_rental_list_price || 0.00) / 4).value;
                    setValueField(dd, 'calc_list_ea', weeklyRentalListPrice);
                    if (dd?.options?.form === 'ledger_charge_line') {
                        const weeklyRentalCost = currency((result.monthly_rental_cost || 0.00) / 4).value;
                        setValueField(dd, 'calc_cost_ea', weeklyRentalCost);
                    }

                    const weeklyRentalExpected = currency((result.rental_expected_monthly_ea || 0.00) / 4).value;
                    setValueField(dd, 'calc_expected_ea', weeklyRentalExpected);
                    break;
            }
        } else {
            const expectedEa = currency(result.expected_ea || 0.0).value;
            setValueField(dd, 'calc_expected_ea', expectedEa);
            const billedEa = currency(result.bill_ea || 0.0).value;
            setValueField(dd, 'calc_billed_ea', billedEa);
            if (dd?.options?.form === 'ledger_charge_line') {
                const costEa = currency(result.cost_ea || 0.0).value;
                setValueField(dd, 'calc_cost_ea', costEa);
            }

            const listEa = currency(result.list_ea || 0.0).value;
            setValueField(dd, 'calc_list_ea', listEa);
        }

        const chargeQuantityEa = result.charge_quantity_ea || 1;
        setValueField(dd, 'charge_quantity_ea', chargeQuantityEa);
        const pricingSource = result.pricing_source || null;
        setValueField(dd, 'pricing_source', pricingSource);
        if (dd?.options?.form === 'ledger_charge_line') {
                const modifier1 = result.modifier_1 || null;
                setValueField(dd, 'modifier_1', modifier1);
                const modifier2 = result.modifier_2 || null;
                setValueField(dd, 'modifier_2', modifier2);
                const modifier3 = result.modifier_3 || null;
                setValueField(dd, 'modifier_3', modifier3);
                const modifier4 = result.modifier_4 || null;
                setValueField(dd, 'modifier_4', modifier4);
        }

        const sharedContractId = result.shared_contract_id || null;
        setValueField(dd, 'shared_contract_id', sharedContractId);
        const costBasis = result.cost_basis || null;
        setValueField(dd, 'cost_basis', costBasis);

        await updatePricingCalcs(dd, 'item');
    } catch (error) {
        console.error("Error at loadPricingInfo:", error);
        showToastError('Error while updating insurance pricing.');
    }
}

const updatePricingCalcs = async (dd: any, context: any) => {
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const billQuantity = justGiveMeAFuckingNumber(dd.value_field('bill_quantity')) || 1;

        if (!billQuantity || billQuantity <= 0) return;

        const inventoryType = dd.value_field('inventory_type') || null;
        const parentChargeNo = dd.value_field('parent_charge_no') || null;
        if (parentChargeNo) return;
        const chargeQuantityEa = justGiveMeAFuckingNumber(dd.value_field('charge_quantity_ea')) || 1;
        const chargeQuantity = billQuantity * chargeQuantityEa;
        setValueField(dd, 'charge_quantity', chargeQuantity);
        const expectedEa = justGiveMeAFuckingNumber(dd.value_field('calc_expected_ea')) || 0.0;
        const expected = currency(expectedEa * billQuantity).value;
        if (context !== 'expected') {
            setValueField(dd, 'expected', expected);
        }
        const billedEa = justGiveMeAFuckingNumber(dd.value_field('calc_billed_ea')) || 0.0;
        const billed = currency(billedEa * billQuantity).value;
        if (context !== 'billed') {
            setValueField(dd, 'billed', billed);
        }
        if (dd?.options?.form === 'ledger_charge_line' && inventoryType === 'Billable') {
                const costEa = justGiveMeAFuckingNumber(dd.value_field('calc_cost_ea')) || 0.0;
                const cost = currency(costEa * billQuantity).value;
                setValueField(dd, 'total_cost', cost);
        }

        const listEa = justGiveMeAFuckingNumber(dd.value_field('calc_list_ea')) || 0.0;
        const list = currency(listEa * billQuantity).value;
        setValueField(dd, 'list_price', list);

        const dispenseFee = justGiveMeAFuckingNumber(dd.value_field('dispense_fee')) || 0.0;
        const incentiveFee = justGiveMeAFuckingNumber(dd.value_field('incv_amt_sub')) || 0.0;
        const grossAmountDue = currency(dispenseFee + incentiveFee + billed).value;
        setValueField(dd, 'gross_amount_due', grossAmountDue);

    } catch (error) {
        console.error("Error at updatePricingCalcs:", error);
        showToastError('Error while updating pricing calculations.');
    }
}

window.DSLFx.Transforms.UpdateInventoryPricing = (form, dd, trn, f, k) => {
    if(!dd?.options?.wrapper?.form_rendered){
        return;
    }
    const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
    if (revenueAlreadyPosted === 'Yes') return;
    if (!checkIfFieldHasChanged(dd, 'UpdateInventoryPricing', k)) return;
    const insuranceId = dd.value_field('insurance_id');
    const payerId = justGiveMeAFuckingNumber(dd.value_field('payer_id')) || null;
    if (payerId === 1) {
        console.log("Copay charge line, ignoring pricing updates");
        return;
    }
    const pricingSource = dd.value_field('pricing_source') || null;
    if (pricingSource === 'cob_override') {
        // If COB price is set, don't update the pricing
        return;
    }
    const patientId = dd.value_field('patient_id');
    const inventoryId = dd.value_field('inventory_id');
    const siteId = dd.value_field('site_id');

    if ( !inventoryId || !siteId || !insuranceId || !patientId) {
        setValueField(dd, 'list_ea', 0.0);
        setValueField(dd, 'expected_ea', 0.0);
        return;
    }
    const UpdateInventoryPricing = async () => {
        await loadPricingInfo(dd, inventoryId, insuranceId, siteId, patientId);
    }
    UpdateInventoryPricing();
}

window.DSLFx.Validators.UpdateBilledEa = (_form, dd, _vld, _f, k) => {
    if (!checkIfFieldHasChanged(dd, 'UpdateBilledEa', k)) return;
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const billed = justGiveMeAFuckingNumber(dd.value_field('billed')) || 0.0;
        const billQty = justGiveMeAFuckingNumber(dd.value_field('bill_quantity')) || 1;
        const billEa = justGiveMeAFuckingNumber(dd.value_field('calc_billed_ea')) || 1;
        const newBilledEa = currency(billed / billQty).value;
        if (billEa === newBilledEa) {
            return;
        }
        setValueField(dd, 'calc_billed_ea', newBilledEa);
        const UpdateTotals = async () => {
            try {
                await updatePricingCalcs(dd, 'billed');
            } catch (error) {
                console.error("Error at UpdateTotals:", error);
                showToastError('Error while updating totals.');
            }
        }
        UpdateTotals();
    } catch (error) {
        console.error("Error at UpdateTotals", error);
        showToastError(`Error setting totals`);
    }
}

window.DSLFx.Validators.UpdateExpectedEa = (_form, dd, _vld, _f, k) => {
    if (!checkIfFieldHasChanged(dd, 'UpdateExpectedEa', k)) return;
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const expected = justGiveMeAFuckingNumber(dd.value_field('expected')) || 0.0;
        const billQty = justGiveMeAFuckingNumber(dd.value_field('bill_quantity')) || 1;
        const expectedEa = justGiveMeAFuckingNumber(dd.value_field('calc_expected_ea')) || 1;
        const newExpectedEa = currency(expected / billQty).value;
        if (expectedEa === newExpectedEa) {
            return;
        }
        setValueField(dd, 'calc_expected_ea', newExpectedEa);
        const UpdateTotals = async () => {
            try {
                await updatePricingCalcs(dd, 'expected');
            } catch (error) {
                console.error("Error at UpdateTotals:", error);
                showToastError('Error while updating totals.');
            }
        }
        UpdateTotals();
    } catch (error) {
        console.error("Error at UpdateTotals", error);
        showToastError(`Error setting totals`);
    }
}

window.DSLFx.Validators.UpdateTotals = (_form, dd, _vld, _f, k) => {
    if (!checkIfFieldHasChanged(dd, 'UpdateTotals', k)) return;
    try {
    const UpdateTotals = async () => {
        try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
            await updatePricingCalcs(dd, 'other');
        } catch (error) {
            console.error("Error at UpdateTotals:", error);
            showToastError('Error while updating totals.');
        }
    }
    UpdateTotals();
    } catch (error) {
        console.error("Error at UpdateTotals", error);
        showToastError(`Error setting totals`);
    }
}

window.DSLFx.Validators.ChargeLineCalcTotalBalanceDue = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted !== 'Yes') return;
        const ChargeLineCalcTotalBalanceDue = async () => {
            await fetchChargeBalances(dd);
        }
        ChargeLineCalcTotalBalanceDue();
    } catch (error) {
        console.error("Error while checking available charge line balances:", error);
        showToastError(`Error while checking available charge line balances`);
    }
}

window.DSLFx.Validators.ChargeLineBlock = (form, dd, vld, vals, k) => {
    if (vals.close_no || vals.void === 'Yes' || vals.zeroed === 'Yes' || vals.revenue_accepted_posted === 'Yes' || vals.locked === 'Yes') return false;
    return true;
}
