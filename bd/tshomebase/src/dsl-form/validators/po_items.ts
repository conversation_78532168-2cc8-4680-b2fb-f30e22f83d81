import { fetchFormData } from "@hooks/index";
import { showToastError } from "@utils/fx";

window.DSLFx.Validators.InventoryPOItemLastCost = (form, dd, trns, f, k) => {
	if (!dd.has_changed("InventoryPOItemLastCost", "inventory_id")) {
		return;
	}
	dd.value_field("package_cost", 0, true, true);
	const inventoryId = dd.value_field("inventory_id");
	if (!inventoryId) {
		return;
	}
	fetchFormData("inventory", inventoryId, true).then((res) => {
		dd.value_field("package_cost", res?.data?.last_cost || 0, true, true);
	});
};

window.DSLFx.Validators.SetPurchaseUnitForSupply = (form, dd, trns, f, k) => {
	if (!dd.has_changed("SetPurchaseUnitForSupply", "inventory_id")) {
		return;
	}
	const inventoryId = dd.value_field("inventory_id");
	if (!inventoryId) {
		return;
	}
	fetchFormData("inventory", inventoryId, true).then((res) => {
		if (res?.data?.type === "Supply") {
			dd.value_field("purchase_unit", res?.data?.purchase_unit || "Each", true, true);
		} else {
			dd.value_field("purchase_unit", "Each", true, true);
		}
	}).catch((err) => {
		console.error("Error fetching inventory data", err);
		showToastError("Error fetching inventory data");
	});
};
