
import {
    showToastError,
    getParentFormDD
} from "@utils/fx";
import { request } from "@core/request";

window.DSLFx.Validators.LoadActivePayers = (form, dd: any, vld, f, k) => {
    if (dd.options.mode == 'read') return false;

    try {
        if (dd?.setActivePayers) return;
        const parentFormDD = getParentFormDD(dd, 'patient_insurance');
        if (!parentFormDD) return;
        const currentPayerId = window.DSLFields.value(parentFormDD.field_nodes['payer_id']);
        const currentBillingMethod = window.DSLFields.value(parentFormDD.field_nodes['billing_method_id']);
        if (!currentPayerId || !currentBillingMethod) return;
        const patientId = dd.value_field('patient_id');
        const LoadActivePayers = async () => {
            try {
                const activeInsuranceRecs = await request({
                    url: `/api/form/patient_insurance?filter=patient_id:${patientId}&filter=active:Yes&filter=payer_id:!${currentPayerId}&filter=billing_method_id:${currentBillingMethod}`
                });
                if (activeInsuranceRecs?.data?.length > 0) {
                    const availablePayers = activeInsuranceRecs.data.map((rec: any) => rec.payer_id);
                    dd.value_field('active_payer_id', availablePayers, true, true);
                } else {
                    dd.value_field('active_payer_id', [], true, true);
                }
                dd.setActivePayers = true;
            } catch (error) {
                console.error("Error checking patient current payer list:", error);
                showToastError("Error checking patient current payer list.");
            }
        }
        LoadActivePayers();
    } catch (error) {
        console.log("Error while loading active payers", error);
        showToastError(`Error loading active payers`);
    }
}