

import currency from 'currency.js';
import _ from "lodash";
import { request } from "@core/request";
import {
    showToastError,
    justGiveMeAFuckingNumber,
    checkIfFieldHasChanged
} from "@utils/fx";


window.DSLFx.Validators.FetchRefundUnappliedCashBalance = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const FetchRefundUnappliedCashBalance = async () => {
            const payer_id = dd.value_field('payer_id');
            const patient_id = dd.value_field('patient_id');
            if (!payer_id || !patient_id) {
                console.error("Payer or patient ID not found. Cannot fetch unapplied cash balance.");
                return;
            } 
            const response = await request({
                url: `/query/account_unapplied_cash/?x1=${payer_id}&x2=${patient_id}&x3=${payer_id}`,
            });
            if (response.data.length === 0) {
                console.error("Error fetching unapplied cash balance data");
                return null;
            }
            const unappliedCashBalance = response.data[0].unapplied_cash_balance;
            if (unappliedCashBalance) {
                const parsedUnappliedCashBalance = currency(justGiveMeAFuckingNumber(unappliedCashBalance)).value;
                if (parsedUnappliedCashBalance > 0) {
                    dd.value_field('available_unapplied_cash', parsedUnappliedCashBalance, true, true);
                } else {
                    dd.value_field('available_unapplied_cash', 0.00, true, true);
                }
            }
        }
        FetchRefundUnappliedCashBalance();
    } catch (error) {
        console.error("Error while checking available unapplied cash:", error);
        showToastError(`Error while checking for unallocated cash`);
    }
}

window.DSLFx.Validators.ValidateAllocatedCash = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!checkIfFieldHasChanged(dd, 'ValidateAllocatedCash', k)) return;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const availableCash = currency(justGiveMeAFuckingNumber(dd.value_field('available_unapplied_cash') || 0.0)).value;
        const allocatedCash = currency(justGiveMeAFuckingNumber(dd.value_field('amount') || 0.0)).value;

        if (allocatedCash > 0 && allocatedCash > availableCash) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes['amount'], `The total allocated cash cannot be greater than the amount available (${currency(availableCash).format()})`);
        }
    } catch (error) {
        console.error("Error while checking cash allocation:", error);
        showToastError(`Error checking cash allocation`);
    }
}