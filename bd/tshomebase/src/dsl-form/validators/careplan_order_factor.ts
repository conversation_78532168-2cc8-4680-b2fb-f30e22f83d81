import { checkIfFieldHasChanged,showToastError } from "@utils/fx";

window.DSLFx.Validators.BuildFactorDoseString = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'BuildFactorDoseString', k)) return;

    try {
        const doseType = dd.value_field('prescription_provided_in') || null;
        if (!doseType) return;

        if (doseType === 'Range') {
            const doseRange1 = dd.value_field('dose_range_1') || null;
            const doseRange2 = dd.value_field('dose_range_2') || null;
            const doseUnit = dd.value_field('dose_unit') || null;
            if (doseRange1 && doseRange2 && doseUnit) {
                dd.value_field('dose_str', `${window.numeral(doseRange1).format('0,0')}-${window.numeral(doseRange2).format('0,0')} ${doseUnit}`)
            }
        } else {
            const dose = dd.value_field('dose') || null;
            const doseUnit = dd.value_field('dose_unit') || null;
            if (dose && doseUnit) {
                dd.value_field('dose_str', `${window.numeral(dose).format('0,0')} ${doseUnit}`)
            }
        }
    } catch (error) {
        console.error(`Error building factor dosing sig`, error);
        showToastError(`Error building dosing sig.`);
    }

}
