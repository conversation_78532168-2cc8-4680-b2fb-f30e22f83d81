
import { showToastError, checkIfFieldHasChanged } from "@utils/fx";


const slMap = {
    1: "Contraindicated",
    2: "Severe Interaction",
    3: "Moderate Interaction",
    9: "Undetermined Severity",
};

const generateInteractionNotes = (interactions: any[]) => {

    let noteIndex = 1;
    let notes = "";
    interactions.forEach((interaction: any) => {
        let severityKey = Object.keys(slMap).find(key => slMap[key] === interaction.sl);

        if (severityKey == "1" || severityKey == "3") {
            notes += `${noteIndex} Interaction: ${interaction.sl}\n`;
            notes += `Drug 1: ${interaction.ndc_1}\n`;
            notes += `Drug 2: ${interaction.ndc_2}\n\n`;
            noteIndex++;
        }
    });
    return notes || null;
}

window.DSLFx.Validators.InteractionsReason = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'InteractionsReason', k)) return;

    try {
        let dd_interactions = window.DSLFields.value(dd.field_nodes['dd_interaction']);
        if (!dd_interactions || dd_interactions.length == 0) return;
        let notes = generateInteractionNotes(dd_interactions);
        if (!notes) return;
        dd.value_field('check_dd_interaction', 'Yes', true, true);
        dd.value_field('dd_interaction_note', notes, true, true);
    } catch (error) {
        console.error("Error while setting interactions reason:", error);
        showToastError(`Error setting interactions reason`);
    }
}