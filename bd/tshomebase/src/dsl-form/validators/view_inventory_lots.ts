import { checkIfFieldHasChanged, getSubformNode, showToastError } from "@utils/fx";

window.DSLFx.Transforms.ContinuousBarcodePO = (form, dd, trn, f, k) => {
    if (!trn?.__barcode) return;

    try {
        const subformNode = getSubformNode(dd, 'view_inventory_lot', 'subform_lots');
        const siteId = dd.value_field('site_id') || null;
        const scanError = trn.__barcode?.error || null;
        if (scanError) {
            showToastError(scanError);
            return;
        }
        const barcodeValues = trn.__barcode?.values || {};
        const scanValues = barcodeValues.scan_values || {};
        const inventoryRec = barcodeValues.inventory || null;
        if (!inventoryRec || !inventoryRec.id) {
            showToastError('Invalid inventory data');
            return;
        }
        const { lot_tracking, serial_tracking, id: inventoryId, type, last_cost_ea } = inventoryRec;
        const { gtin, expiration, lot, serial, serial_no } = scanValues;
        const scannedItemObj = {
            site_id: siteId,
            inventory_id: inventoryId,
            type,
            lot_tracking: lot_tracking || "No",
            serial_tracking: serial_tracking || "No",
            lot_no: lot_tracking === "Yes" ? lot || null : null,
            serial_no: serial_tracking === "Yes" ? serial ? serial : serial_no : null,
            gtin: gtin || null,
            expiration: expiration || null,
            cost_each: last_cost_ea || null,
        };
        const lots = Array.isArray(window.DSLFields.value(subformNode)) 
            ? window.DSLFields.value(subformNode) 
            : [];
        if (!Array.isArray(lots)) {
            showToastError('Subform data is invalid');
            return;
        }
        const existingLot = lots.find((o) => o.inventory_id === inventoryId);
        if (existingLot) {
            const updatedRow = {
                ...existingLot,
                ...scannedItemObj,
            };
            window.FieldSubform.update_row_by_index(subformNode, existingLot?.__index, updatedRow, true);
        } else {
            window.FieldSubform.add_row(subformNode, scannedItemObj);
        }
    } catch (error) {
        console.error("Error in ContinuousBarcodePO:", error);
        showToastError('Error while scanning item');
    }
};

window.DSLFx.Validators.LockLotInventory = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LockLotInventory', k)) return;
    const lot_no = dd.value_field('lot_no');
    const serial_no = dd.value_field('serial_no');
    if (lot_no) return;
    try {
        dd.value_field('quantity', '', true, true)
        if (serial_no) {
            dd.value_field('quantity', 1, true, true)
        }
    } catch (error) {
        console.log("Error while locking the quantity in inventory lot", error)
        showToastError("Error while locking the quantity in inventory lot")
    }
}