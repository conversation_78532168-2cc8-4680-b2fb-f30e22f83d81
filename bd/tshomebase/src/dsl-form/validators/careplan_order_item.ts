import { request } from "@core/request";
import _ from 'lodash';
import { showToastError, checkIfFieldHasChanged, getParentFormData, updateLabel } from "@utils/fx";
import { sectionReadOnly } from "@utils/dsl-fx";

window.DSLFx.Validators.CheckPayerPARequirements = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckPayerPARequirements', k)) return;

    const billing_method = dd.value_field('billing_method');
    if (!billing_method || !['Insurance'].includes(billing_method)) {
        console.warn('Invalid or missing billing method');
        return;
    }

    const careplan_order = getParentFormData(dd);
    const site_id = careplan_order?.site_id;
    const patient_id = careplan_order?.patient_id;
    const inventory_id = dd.value_field('inventory_id');
    // const patient_id = dd.value_field('patient_id');
    if (!careplan_order) {
        console.error('Unable to get parent form data');
        return;
    }

    const getRankedPayerIds = () => {
        const payer_type = billing_method === 'Insurance' ?
            'payer_ids' : '';

        // Check form values first, then fallback to careplan order
        const form_payer_ids = dd.values()?.[payer_type] || [];
        const order_payer_ids = careplan_order[payer_type] || [];

        return form_payer_ids.length > 0 ? form_payer_ids : order_payer_ids;
    };

    const ranked_payer_ids = getRankedPayerIds();
    if (!Array.isArray(ranked_payer_ids) || ranked_payer_ids.length === 0) {
        console.warn(`No ${billing_method.toLowerCase()} payer IDs found`);
        dd.value_field('auth_flag', '', true, true);
        return;
    }

    const primary_payer = ranked_payer_ids.find((payer: any) => payer?.rank === 1);
    if (!primary_payer?.id) {
        console.warn('No primary payer found or invalid payer ID');
        dd.value_field('auth_flag', '', true, true);
        dd.value_field('insurance_id', '', true, true);
        return;
    }

    dd.value_field('insurance_id', primary_payer.id, true, true);
    const CheckPayerPARequirements = async () => {
        try {

            if (!inventory_id) {
                console.warn('No Inventory ID found in careplan order item');
                return;
            }

            const response = await request({
                url: `/query/inventory_payer_pricing/?x1=${inventory_id}&x2=${primary_payer.id}&x3=${site_id}&x4=${patient_id}`,
            });
            if (!response?.data?.length) {
                console.warn('No pricing data returned');
                return;
            }

            let blockOrder = false;
            const pricingData = response.data[0];
            if (['Yes', 'Block'].includes(pricingData?.auth_required)) {
                const paId = dd.value_field('drug_pa_id');
                if (!paId) {
                    dd.value_field('auth_flag', 'Yes', true, true);
                    if (pricingData?.auth_required === 'Block') {
                        window.prettyError('Authorization Required', 'The selected primary payer is set to block this drug without a prior authorization. Cannot complete until authorization is attached.');
                        blockOrder = true;
                    } else {
                        blockOrder = false;
                    }
                }

            } else {
                dd.value_field('auth_flag', '', true, true);
            }
            if (pricingData?.not_covered === 'Yes') {
                window.prettyError('Not Covered', 'The selected primary payer is exclusively set to not cover this drug. Please select a different payer or change the drug for the order.');
                blockOrder = true;
            } else {
                blockOrder = blockOrder ? true : false;
            }

            if (blockOrder) {
                dd.value_field('payer_order_block', 'Yes', true, true);
            } else {
                dd.value_field('payer_order_block', '', true, true);
            }
        } catch (error) {
            console.error("Error at CheckPayerPARequirements:", error);
            showToastError(`Error checking payer PA requirements`);
            dd.value_field('auth_flag', '', true, true);
        }
    };

    CheckPayerPARequirements();
};

window.DSLFx.Transforms.SetDoseLabel = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'SetDoseLabel', k)) return;

    try {
        const template_type = dd.value_field('template_type') || 'Default';
        const doseLabel = template_type === 'Factor' ? 'Target Dose' : 'Dose';
        updateLabel('dose', doseLabel, dd);
    } catch (error) {
        console.error("Error while setting dose label:", error);
        showToastError(`Error setting dose label`);
    }
};

window.DSLFx.Transforms.CalculateAllowedVariance = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateAllowedVariance', k)) return;

    try {
        const doseRange1 = dd.value_field('dose_range_1');
        const doseRange2 = dd.value_field('dose_range_2');
        if (!doseRange1 || !doseRange2) {
            dd.value_field('allowed_variance', '', true, true);
            return;
        }
        const dose1 = parseFloat(doseRange1);
        const dose2 = parseFloat(doseRange2);
        const allowedVariance = Math.abs(((dose2 - dose1) / dose1) * 100);
        dd.value_field('allowed_variance', allowedVariance, true, true);
    } catch (error) {
        console.error("Error while calculating allowed variance:", error);
        showToastError(`Error calculating allowed variance`);
    }
};

window.DSLFx.Transforms.CalculateTargetDose = async (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateTargetDose', k)) return;

    try {
        const doseRange1 = dd.value_field('dose_range_1');
        const doseRange2 = dd.value_field('dose_range_2');
        if (!doseRange1 || !doseRange2) {
            dd.value_field('dose', '', true, true);
            return;
        }
        const targetDose = Math.ceil((parseFloat(doseRange1) + parseFloat(doseRange2)) / 2);
        dd.value_field('dose', targetDose, true, true);
    } catch (error) {
        console.error("Error while calculating target dose:", error);
        showToastError(`Error calculating target dose`);
    }
};

window.DSLFx.Validators.BlockRXOnDiscontinued = (form, dd, vld, f, k) => {
    try {
        const status_id = dd.value_field('status_id');
        if (!status_id) return;
        if (status_id === '2') {
            sectionReadOnly(dd, 'Rx Info', true, 'status_id');
        } else {
            sectionReadOnly(dd, 'Rx Info', false, 'status_id');
        }
    } catch (error) {
        console.error("Error while blocking RX on discontinued:", error);
        showToastError(`Error blocking RX on discontinued`);
    }
}

window.DSLFx.Validators.SetDosingUnitFilter = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'SetDosingUnitFilter', k)) return;

    const inventory_id = dd.value_field('inventory_id');
    if (!inventory_id) return;

    const getDosingUnitId = async (inventory_id: any) => {
        try {
            const response = await request({
                url: `/form/inventory/${inventory_id}`
            })
            return response.data.length > 0 ? response.data[0].default_dosing_unit_id : response.data[0].billing_unit_id;
        } catch (error) {
            console.error("Error while getting inventory ID:", error);
            return null;
        }
    }

    const SetDosingUnitFilter = async () => {
        try {
            const dosing_unit_id = await getDosingUnitId(inventory_id);
            if (!dosing_unit_id) return;
            const response = await request({
                url: `/query/load_dosing_unit/?x1=${dosing_unit_id}`
            })
            if (response.data.length > 0) {
                const compatible_dosing_units = response.data[0].compatible_dosing_units;
                dd.value_field('dunit_filter_id', compatible_dosing_units, true, true);
                dd.field_nodes['dose_unit_id'].trigger('change');
            }
        } catch (error) {
            console.error("Error while setting dosing unit filter:", error);
            showToastError(`Error setting dosing unit filter`);
        }
    }

    SetDosingUnitFilter();
}