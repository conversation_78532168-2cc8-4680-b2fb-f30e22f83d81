import { getMasterWrapper } from "@utils/fx";
window.DSLFx.Transforms.RefreshInvoice = (form, dd, trn, f, k) => {
    if (dd.options.mode === 'read') return false;
    console.log(`Refreshing invoice after charge line updates`);
    getMasterWrapper(dd).options.parent.tab_do_refresh();
    return true;
}


window.DSLFx.Validators.InvoiceChargeLinesBlock = (form, dd, vld, vals: any, k: string) => {
    if (vals?.void === 'Yes') {
        // Never edit a voided delivery ticket
        return false;
    }

    if (vals?.zeroed === 'Yes') {
        // Never edit a confirmed delivery ticket
        return false;
    }

    if (vals?.close_no === 'Yes') {
        // Never edit a confirmed delivery ticket
        return false;
    }

    return true;
}
