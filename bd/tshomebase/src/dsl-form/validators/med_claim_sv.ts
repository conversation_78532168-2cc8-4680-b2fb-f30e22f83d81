import { request } from "@core/request";
import {
    getFormNodeValues,
    getParentSubformNode,
    showToastError
} from "@utils/fx";
const getHealthcareInformationLoop = (dd: any) => {
    return getParentSubformNode(dd, 'med_claim_dx', 'health_care_code_information');
}

window.DSLFx.Validators.CalcSalesTax = (form, dd, vld, f, k) => {
    const CalcSalesTax = async () => {
        let med_claim_sl = dd.options.wrapper.subforms.formmap.med_claim_sl
        let med_claim_sl_data = med_claim_sl.values();
        let med_claim = dd?.options?.parent?.parent?.subform_parent?.parent?.ddf?.options?.wrapper?.subforms?.formmap?.med_claim?.values()
        let line_item_charge_amount = 0
        if (med_claim_sl_data?.professional_service && med_claim_sl_data?.professional_service.length > 0) {
            med_claim_sl_data.professional_service.forEach((line: any) => {
                line_item_charge_amount += line.line_item_charge_amount;
            });
        }
        if (med_claim_sl_data.mm_calc_perc_sales_tax !== 'Yes' || !med_claim_sl_data.site_id || !med_claim_sl_data.patient_id || !med_claim.insurance_id) return;
        try {
            const patient = await request({
                url: `/form/patient/${med_claim_sl_data.patient_id}`
            })
            const response = await request({
                url: `/billing?func=calc_salestax`,
                method: 'POST',
                data: {
                    site_id: med_claim_sl_data.site_id,
                    ship_state_id: patient.data[0].ship_state_id,
                    expected: line_item_charge_amount,
                    dispense_fee: 0,
                    insurance_id: med_claim.insurance_id,
                    patient_id: med_claim_sl_data.patient_id,
                }
            });
            if (response.data.length) {
                let sales_tax = response.data.sales_tax;
                med_claim_sl.value_field('sales_tax_amount', sales_tax, true, true)
            }
        } catch (error) {
            console.log("Error while calculating sales tax", error)
        }
    }
    CalcSalesTax();
}

window.DSLFx.Validators.AddClaimDiagnoses = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;

    try {
        const cdxIds = dd.value_field(k);
        const healthcareInformationLoopNode = getHealthcareInformationLoop(dd);
        const healthcareInformationValues = getFormNodeValues(healthcareInformationLoopNode);
        const dxIds = (healthcareInformationValues || []).map(item => item.dx_id);
        const filteredDxIds = dxIds.filter(id => !cdxIds.includes(id));
        if (filteredDxIds.length) {
            dd.value_field(k, dxIds, true, true);
        }
    } catch (error) {
        console.log("Error while adding claim diagnoses", error);
        showToastError(`Error adding claim diagnoses to service line filter`);
    }
}