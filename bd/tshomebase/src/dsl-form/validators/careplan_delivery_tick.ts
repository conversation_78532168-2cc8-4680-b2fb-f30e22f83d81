import _ from "lodash";
import currency from "currency.js";
import { request } from "@core/request";
import {
    showToastError,
    justGiveMeAFuckingNumber,
    checkIfFieldHasChanged,
    getSubformNode,
    getFormNodeValues,
    getParentSubformNode
} from "@utils/fx";

import { DSLCardViewRef } from '@blocks/dsl-card-view';

const StatusToBlockedFieldsMap = {
    "order_ver": {
        allowedFields: ['subform_item'],
        blockedFields: ['tech_verified', 'tech_supplies_verified'],
    },
    "pending_conf": {
        allowedFields: [],
        blockedFields: ['tech_verified', 'tech_supplies_verified', 'verified'],
    },
    "ready_to_bill": {
        allowedFields: [],
        blockedFields: ['tech_verified', 'tech_supplies_verified', 'verified', 'confirmed']
    },
    "billed": { blockedFields: ['*'], allowedFields: [] },
    "voided": { blockedFields: ['*'], allowedFields: [] }
}

window.DSLFx.Validators.DeliveryTicketBlock = (form, dd, vld, vals: any, k: string) => {
    if (vals?.void === 'Yes') {
        // Never edit a voided delivery ticket
        return false;
    }

    if (vals?.confirmed === 'Yes') {
        // Never edit a confirmed delivery ticket
        return false;
    }

    const status = vals.status || 'delivery_ticket';
    const { blockedFields = [], allowedFields = [] } = StatusToBlockedFieldsMap[status as keyof typeof StatusToBlockedFieldsMap] || {};
    if (blockedFields.includes('*')) {
        return false;
    }
    if (allowedFields.includes('*')) {
        return true;
    }
    if (blockedFields.length > 0) {
        if (blockedFields.includes(k)) {
            return false;
        }
        return true;
    }
    if (allowedFields.length > 0) {
        if (allowedFields.includes(k)) {
            return true;
        }
        return false;
    }
    return true;
}


window.DSLFx.Transforms.OpenDeliveryTicketTab = (form, dd, trns, f, k) => {

    const status = dd.value_field(k);
    if (!status) return;
    const goToField = dd.options.wrapper.get_controls().goToField;
    if (!goToField) return;
    const cardView = dd.options.parent.parent;
    if (!cardView.subscribe) return;
    const idx = cardView.subscribe("ready", (ref: DSLCardViewRef) => {
        requestIdleCallback(() => { // tab-controller is still doing stuff
            dd.value_field('status', status);
            switch (status) {
                case 'pending_conf':
                    goToField('careplan_delivery_tick', 'confirmed');
                    break;
                case 'order_ver':
                    goToField('careplan_delivery_tick', 'tech_verified');
                    break;
                case 'ready_to_fill':
                    goToField('careplan_delivery_tick', 'raw_barcode');
                    break;
            };

            dd.value_field('status', status);
        })
        cardView.unsubscribe("ready", idx);
    });
}

/**
 * Gets the work ticket "pulled" form node based on whether the current form is the parent or child
 * @param {any} dd - The form data object 
 * @returns {any} The work ticket "pulled" form node
 */
export const getWTPulledFormNode = (dd: any) => {
    const ddIsParent = dd.options.form === 'careplan_delivery_tick';
    if (ddIsParent) {
        return getSubformNode(dd, 'careplan_dt_wt_pulled', 'subform_wt_pulled');
    } else {
        return getParentSubformNode(dd, 'careplan_dt_wt_pulled', 'subform_wt_pulled');
    }
}

/**
 * Gets the work ticket "to pull" form node based on whether the current form is the parent or child
 * @param {any} dd - The form data object
 * @returns {any} The work ticket "to pull" form node
 */
export const getWTToPullFormNode = (dd: any) => {
    const ddIsParent = dd.options.form === 'careplan_delivery_tick';
    if (ddIsParent) {
        return getSubformNode(dd, 'careplan_dt_wt_pull', 'subform_wt_pull');
    } else {
        return getParentSubformNode(dd, 'careplan_dt_wt_pull', 'subform_wt_pull');
    }
}

window.DSLFx.Validators.LoadShippingAddress = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    const selectedAddress = window.DSLFields.value(dd?.field_nodes?.embed_ship_address);

    if (selectedAddress && selectedAddress.length > 0) {
        const addressObj = selectedAddress.find((address: any) => address.id);
        if (!addressObj) return;
        const LoadShippingAddress = async () => {
            try {
                const response = await request({
                    url: `/api/form/patient_address/${addressObj.id}`
                });
                const address = response?.data?.[0] || null;
                if (address) {
                    dd.value_field('ship_location', address.ship_location, true, true)
                    dd.value_field('ship_to', address.ship_to, true, true)
                    dd.value_field('ship_street', address.street, true, true)
                    dd.value_field('ship_street2', address.street2, true, true)
                    dd.value_field('ship_city', address.city, true, true)
                    dd.value_field('ship_state_id', address.state_id, true, true)
                    dd.value_field('ship_zip', address.zip, true, true)
                }

            } catch (error) {
                console.error("Error at LoadShippingAddress:", error);
                showToastError("Error loading shipping address");
            }
        }
        LoadShippingAddress();
    }
}

window.DSLFx.Validators.CheckPrescriptionReadyToFill = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!checkIfFieldHasChanged(dd, 'CheckPrescriptionReadyToFill', k)) return;

    const readyToFill = window.DSLFields.value(dd?.field_nodes?.ready_to_fill);
    const rxIds = window.DSLFields.value(dd?.field_nodes?.rx_id);
    const patientId = window.DSLFields.value(dd?.field_nodes?.patient_id);

    if (readyToFill == 'Yes') {
        const CheckPrescriptionReadyToFill = async () => {
            try {
                const response = await request({
                    url: `/api/query/select_active_prescriptions?x1=${patientId}`
                });
                const readyToFillPrescriptions = response?.data || null;
                if (!Array.isArray(rxIds) || !Array.isArray(readyToFillPrescriptions)) {
                    window.prettyError("Prescription on the ticket are not in a ready to fill state.");
                    dd.value_field(k, '', true, true);
                    return;
                }

                const missingRxIds = rxIds.filter(rxId => 
                    !readyToFillPrescriptions.some(prescription => prescription.id === rxId)
                );

                if (missingRxIds.length > 0) {
                    window.prettyError("Some of the prescriptions listed on the ticket are not in a ready to fill state.");
                    dd.value_field(k, '', true, true);
                    return;
                }

            } catch (error) {
                console.error("Error at CheckPrescriptionReadyToFill:", error);
                showToastError("Error checking prescriptions are ready to fill");
            }
        }
        CheckPrescriptionReadyToFill();
    }
}

window.DSLFx.Transforms.ContinuousBarcode = (form, dd, trn, f, k) => {
    if (!trn?.__barcode) return;

    dd?.field_nodes?.embed_item?.data("barCodeScanner")?.(form, dd, trn, f, k);
    return;
}

const calculatePatientResponsibility = (subformItems: any[]): number => {
    try {
        if (!Array.isArray(subformItems)) {
            console.error("Invalid subform items data - expected array");
            return 0;
        }

        let total = 0;
        for (const item of subformItems) {
            if (item?.bill !== 'Yes') {
                continue;
            }

            const isPatientResponsible = item.billing_method === 'Self Pay' ||
                (item.accept_assignment !== 'Yes');

            if (!isPatientResponsible) {
                continue;
            }

            const listEa = justGiveMeAFuckingNumber(item.list_ea) || 0;
            const quantity = justGiveMeAFuckingNumber(item.quantity) || 0;
            const copay = justGiveMeAFuckingNumber(item.copay) || 0;

            total += (listEa * quantity);

            // Add copay if present
            total += copay;
        }

        // Return formatted currency value
        return currency(total).value;

    } catch (error) {
        console.error("Error calculating patient responsibility:", error);
        showToastError("Error calculating patient responsibility");
        return 0;
    }
}

window.DSLFx.Transforms.SumPatientResponsibility = (form, dd, trns, f, k) => {
    try {
        const SumPatientResponsibility = async () => {
            const ticketNo = dd.value_field('ticket_no');
            if (!ticketNo) return;
            const subformItems = await request({
                url: `/form/careplan_dt_item?filter=ticket_no:${ticketNo}`
            });
            if (!subformItems || subformItems.data.length === 0) {
                console.error("No subform items found");
                return;
            }
            const total = calculatePatientResponsibility(subformItems.data);

            dd.value_field('total_pt_responsibility', total, true, true);
        }
        SumPatientResponsibility();
    } catch (error) {
        console.error("Error in SumPatientResponsibility:", error);
        showToastError("Error calculating patient responsibility");
    }
}
window.DSLFx.Validators.ValidateExceptionsResolved = (form, dd, vld, f, k)=> {
    if (!checkIfFieldHasChanged(dd, 'ValidateExceptionsResolved', k)) return;
    try {
        if (dd.value_field(k) !== 'Yes') return;
        
        const ValidateExceptionsResolved = async () => {
            try {
                const subformNode =  getSubformNode(dd, 'careplan_dt_exception', 'subform_exceptions');

                if (!subformNode) {
                    showToastError("Cannot access confirmation subform.");
                    dd.value_field(k, '', true, true);
                    return;
                }
                
                const confirmSubformValues = getFormNodeValues(subformNode) || [];
                if (confirmSubformValues.length === 0) {
                    console.log('No exceptions found. Confirmation is valid.')
                    return;
                }
            
                const unresolvedExceptions = confirmSubformValues.filter((exception) => 
                    exception && exception.resolved !== 'Yes'
                );
                
                if (unresolvedExceptions.length > 0) {
                    const errorMessage = `All exceptions must be resolved before confirmation.`;
                    window.prettyError(errorMessage);
                    dd.value_field(k, '', true, true);
                }
            } catch (error) {
                console.error("Error validating exceptions:", error);
                showToastError("Error validating exceptions");
                dd.value_field(k, '', true, true);
            }
        }
        
        ValidateExceptionsResolved();
    } catch (error) {
        console.error("Error in ValidateExceptionsResolved:", error);
        showToastError("Error checking exceptions");
        dd.value_field(k, '', true, true);
    }
}

window.DSLFx.Validators.CheckPulledItemsVerified = (form, dd, vld, f, k) => {
    try {
        const dtRec = dd.values();
        if (!dtRec) return;
        if (dtRec.status !== 'order_ver') return;
        const CheckPulledItemsVerified = async () => {
            const response = await request({
                url: `/dispense?func=check_pulled_items_verified`,
                data: dtRec,
                method: 'POST'
            });
            if (response.data.error) {
                dd.value_field('check_verify_warning', 'Yes', true, true);
            } else {
                dd.value_field('check_verify_warning', '', true, true);
            }
        }
        CheckPulledItemsVerified();
    } catch (error) {
        console.error("Error in CheckPulledItemsVerified:", error);
        showToastError("Error checking pulled items verified");
    }
}