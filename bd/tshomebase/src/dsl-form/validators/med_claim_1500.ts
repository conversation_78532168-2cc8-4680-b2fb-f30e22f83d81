import {
    getFormNodeValues,
    getParentSubformNode,
    getParentFormNode,
} from "@utils/fx";

const get1500DiagnosisLoop = (dd: any) => {
    return getParentSubformNode(dd, 'med_claim_1500_dx', 'subform_dx');
}

const get1500SLSubformNode = (dd: any) => {
    return getParentSubformNode(dd, 'med_claim_1500_sl', 'subform_sl');
};

window.DSLFx.Validators.CheckDiagnoses1500 = (form, dd, vld, f, k) => {
    try {
        const diagnosisLoopNode = get1500DiagnosisLoop(dd);
        const diagnosisValues = getFormNodeValues(diagnosisLoopNode);
        const claimLevelDxIds = (diagnosisValues || []).map(item => item.dx_id);

        const serviceLinesNode = get1500SLSubformNode(dd);
        const serviceLinesValues = getFormNodeValues(serviceLinesNode);
        const serviceDxIds = [...new Set(
            (serviceLinesValues || []).flatMap(line => [
                line.dx_id_1,
                line.dx_id_2, 
                line.dx_id_3,
                line.dx_id_4
            ]).filter(Boolean)
        )];

        const missingDxIds = serviceDxIds.filter(id => !claimLevelDxIds.includes(id));
        if (missingDxIds.length > 0) {
            return `The following diagnoses are used in service lines but missing from claim level diagnoses:\n${missingDxIds.join(', ')}`
        } else {
            return false;
        }
    } catch (error) {
        console.error("Error at CheckDiagnoses1500:", error);
        return `Error validating service line diagnoses.`
    }
}

window.DSLFx.Validators.CMS1500Block = (form, dd, vld, vals: any, k: string) => {
    if (vals?.invoice_no) {
        // Never edit a claim directly if tied to an invoice and not editing the invoice
        const parentNode = getParentFormNode(dd);
        if (!parentNode) return false;
    }

    return true;
}