import { request } from "@core/request";
import {
    showToastError,
    checkIfFieldHasChanged,
} from "@utils/fx";

window.DSLFx.Validators.LoadPatientDX = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LoadPatientDX', k)) return;
    let dxId = dd.value_field(k)
    dd.value_field('diagnosis_code', '', true, true);
    if (!dxId) return;
    const LoadPatientDX = async () => {
        try {
            const patientDiagnosis = await request({
                url: `/form/patient_diagnosis/${dxId}`,
                method: 'GET',
            });
            if (patientDiagnosis.data.length > 0) {
                dd.value_field('diagnosis_code', patientDiagnosis.data[0].code, true, true);
            } else {
                showToastError("No patient diagnosis found");
            }
        } catch (error) {
            console.log("Error while LoadPatientDx:", error);
            showToastError("Error loading patient diagnosis");
        }
    }
    LoadPatientDX();
}