window.DSLFx.Transforms.LoadExpectedPrice = (form, dd, trn, f, k) => {
    let order_id = dd.value_field('order_id');
    let insurance_id = dd.value_field('insurance_id');
    let order_item_id = dd.value_field('order_item_id');
    let status_id = dd.value_field('status_id');
    if (!order_id || !insurance_id ||!order_item_id || status_id !== '5') return;
    try {
        // TODO: Patrick Helper Function API to get expected price 
    } catch (error) {
        console.log("Error at LoadExpectedPrice", error);
    }
}