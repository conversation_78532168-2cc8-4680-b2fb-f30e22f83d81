import { request } from "@core/request";
import {
    checkIfFieldHasChanged,
} from "@utils/fx";

const SetOSubValues = (dd: any, other_insured: any) => {
    let other_subscriber_name = dd.options?.wrapper?.subforms?.formmap?.med_claim_osub_nm;
    let other_insured_address = other_subscriber_name?.options?.wrapper?.subforms?.formmap?.med_claim_address_osub;
    for (let key in other_insured) {
        if (key === 'address') {
            for (let k in other_insured.address) {
                other_insured_address.value_field(k, other_insured.address[k], true, true);
            }
        } else {
            other_subscriber_name.value_field(key, other_insured[key], true, true);
        }
    }
}

window.DSLFx.Validators.LoadCOBPtInfo = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LoadCOBPtInfo', k)) return;
    const LoadCOBPtInfo = async () => {
        let insurance_id = dd.value_field('insurance_id')
        let patient_id = dd.value_field('patient_id')
        let individual_relationship_code = dd.value_field('individual_relationship_code')
        let other_insured = {
            other_insured_first_name: '',
            other_insured_last_name: '',
            other_insured_middle_name: '',
            other_insured_identifier_type_code: 'MI',
            other_insured_identifier: insurance_id.insurance_id,
            address: {
                address1: '',
                address2: '',
                city: '',
                state: '',
                postal_code: ''
            }
        };
        SetOSubValues(dd, other_insured);
        if (!patient_id || !insurance_id || !individual_relationship_code) return;
        try {
            let patient = await request({
                url: `/form/patient/${patient_id}`,
                method: 'GET'
            })
            let insurance = await request({
                url: `/form/patient_insurance/${insurance_id}`,
                method: 'GET'
            })
            if (patient.data.length > 0)
                patient = patient.data[0];
            if (insurance.data.length > 0)
                insurance = insurance.data[0];
            if (individual_relationship_code === '18') {
                other_insured.other_insured_first_name = patient.firstname;
                other_insured.other_insured_last_name = patient.lastname;
                other_insured.other_insured_middle_name = patient.middlename;
                other_insured.address.address1 = patient.home_street;
                other_insured.address.address2 = patient.home_street2;
                other_insured.address.city = patient.home_city;
                other_insured.address.state = patient.home_state_id;
                other_insured.address.postal_code = patient.home_zip;
            } else {
                other_insured.other_insured_first_name = insurance.beneficiary_fname;
                other_insured.other_insured_last_name = insurance.beneficiary_lname;
                other_insured.other_insured_middle_name = insurance.beneficiary_mname;
                other_insured.address.address1 = '';
                other_insured.address.address2 = '';
                other_insured.address.city = '';
                other_insured.address.state = '';
                other_insured.address.postal_code = '';
            }
            SetOSubValues(dd, other_insured);
        } catch (error) {
            console.log("Error in loading patient and insurance data", error);
        }
    }
    LoadCOBPtInfo();
}
