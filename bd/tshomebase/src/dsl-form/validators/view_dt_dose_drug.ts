window.DSLFx.Validators.CheckDosesAllowed = (form, dd, vld, f, k) => {
    const dispenseDoses = dd.value_field('dispense_doses');
    const dosesRemaining = dd.value_field('doses_remaining');
    if (!dispenseDoses) {
        return;
    }
    if (parseInt(dispenseDoses) > parseInt(dosesRemaining)) {
        window.prettyError("Error", "Dispense doses cannot be greater than doses remaining");
        dd.value_field('dispense_doses', dosesRemaining);
    }
}
