import { request } from "@core/request";
const setValueField = (dd: any, field: any, value: any) => {
    dd.value_field(field, value, true, true);
};

window.DSLFx.Validators.CalculateHeightWeightMeasures = (_form: any, dd: any, _vld: any, _f: any, _k: any) => {
    const CalculateHeightWeightMeasures = async () => {
        const patientId = dd.value_field('patient_id');
        const weight = dd.value_field('weight') || 0;
        const height = dd.value_field('height') || 0;

        const clearFields = () => {
            setValueField(dd, 'ibw', '');
            setValueField(dd, 'lbw', '');
            setValueField(dd, 'bsa', '');
        }
        if (!weight || !height) {
            clearFields();
            return;
        }
        const calculateIBW = (heightInCm: number, gender: string) => {
            if (!heightInCm || !gender) return '';
            const heightInInches = heightInCm / 2.54; // Convert height from cm to inches
            
            if (gender === 'Male') {
                return 50 + 2.3 * (heightInInches - 60);
            }
            return 45.5 + 2.3 * (heightInInches - 60);
        }

        const calculateLBW = (weightInKg: number, heightInCm: number, gender: string) => {
            if (!weightInKg || !heightInCm || !gender) return '';
            if (gender === 'Male') {
                return 0.407 * weightInKg + 0.267 * heightInCm - 19.2;
            }
            return 0.252 * weightInKg + 0.473 * heightInCm - 48.3;
        }

        const calculateBSA = (weightInKg: number, heightInCm: number) => {
            if (!weightInKg || !heightInCm) return '';
            return Math.sqrt((heightInCm * weightInKg) / 3600).toFixed(2);
        }

        const calculateIBWPercent = (actualWeightKg: number, heightCm: number, gender: string) => {
            // Convert height from cm to inches
            const heightInInches = heightCm / 2.54;
            
            // Calculate Ideal Body Weight (IBW) based on gender
            let ibw;
            if (gender === 'Male') {
                ibw = 50 + 2.3 * (heightInInches - 60);
            } else {
                ibw = 45.5 + 2.3 * (heightInInches - 60);
            }

            const percentIBW = (actualWeightKg / ibw) * 100;
            return percentIBW.toFixed(2);
        }

        const calculateABW = (actualWeightKg: number, idealBodyWeight: number) => {
            const adjustedBodyWeight = idealBodyWeight + 0.4 * (actualWeightKg - idealBodyWeight);
            return adjustedBodyWeight.toFixed(2);
        }
        try {
            const patientRecs = await request({
                url: `/form/patient/${patientId}`
            });
            const patientRec = patientRecs.data[0];
            const gender = patientRec.gender || null;
            if (!gender) {
                console.error('Patient gender not found, cannot calculate height weight measures');
                clearFields();
                return;
            }

            const ibw = calculateIBW(height, gender);
            const lbw = calculateLBW(weight, height, gender);
            const bsa = calculateBSA(weight, height);
            const ibwPercent = calculateIBWPercent(weight, height, gender);
            if (ibw) {
                const abw = calculateABW(weight, ibw);
                setValueField(dd, 'abw', abw);
            } else {
                setValueField(dd, 'abw', '');
            }
            setValueField(dd, 'ibw', ibw);
            setValueField(dd, 'lbw', lbw);
            setValueField(dd, 'bsa', bsa);
            setValueField(dd, 'ibw_percent', ibwPercent);
        } catch (error) {
            console.log("Error at Calculate Height Weight Measures Validator:", error)
        }
    }
    CalculateHeightWeightMeasures();
};