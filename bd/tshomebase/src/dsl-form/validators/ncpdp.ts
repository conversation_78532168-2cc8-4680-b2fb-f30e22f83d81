import {
    justGiveMeAFuckingNumber,
    getSubformNode,
    getFormNodeValues,
    getParentFormDD,
    getParentSubformNode,
    getParentFormData
} from "@utils/fx";

import currency from "currency.js";
const getParentDD = (dd: any) => {
    const parentForm = dd.get_parent_form();
    const ddIsParent = parentForm === 'ncpdp' || !parentForm;
    if (ddIsParent) {
        return dd;
    } else {
        return getParentFormDD(dd, 'ncpdp');
    }
}

const getPricingSegmentSubformNode = (dd: any) => {
    const parentForm = dd.get_parent_form();
    const ddIsParent = parentForm === 'ncpdp' || !parentForm;
    if (ddIsParent) {
        return getSubformNode(dd, 'ncpdp_pricing', 'segment_pricing');
    } else {
        return getParentSubformNode(dd, 'ncpdp_pricing', 'segment_pricing');
    }
};
const getPPricingCOBNode = (dd: any) => {
    const parentForm = dd.get_parent_form();
    const ddIsParent = parentForm === 'ncpdp' || !parentForm;
    if (ddIsParent) {
        return getSubformNode(dd, 'ncpdp_pricing_cob', 'subform_oclaim');
    } else {
        return getParentSubformNode(dd, 'ncpdp_pricing_cob', 'subform_oclaim');
    }
};

window.DSLFx.Transforms.CalcGrossAmountDue = (form, dd, trn, f, k) => {
    if (dd.options.mode === 'read') return false;
    console.log(`Calculating gross amount due`);
    const ncpdpDD = getParentDD(dd);
    const pricingSegmentNode = getPricingSegmentSubformNode(ncpdpDD);
    const COBPricingNode = getPPricingCOBNode(dd);
    const COBPricingRows = getFormNodeValues(COBPricingNode) || [];
    const pricingSegmentFieldNodes = pricingSegmentNode.field_nodes;

    const salesTax = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['sales_tax']) || 0.00);
    const flatTax = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['flat_tax']) || 0.00);
    const incentiveAmount = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['incv_amt_sub']) || 0.00);
    const dispenseFee = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['disp_fee_sub']) || 0.00);
    const ingredientCost = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['ing_cst_sub']) || 0.00);
    const COBSumAmount = COBPricingRows.reduce((acc, row) => acc + justGiveMeAFuckingNumber(row.o_amt_sub) || 0.00, 0);
    const totalAmountDue = currency(ingredientCost + salesTax + flatTax + incentiveAmount + dispenseFee + COBSumAmount).value;
    window.DSLFields.value(pricingSegmentFieldNodes['gross_amount_due'], totalAmountDue);
    return true;
}

window.DSLFx.Validators.NCPDPBlock = (form, dd, vld, vals: any, k: string) => {
    try {
        if (vals?.invoice_no) {

            if (vals?.close_no?.length > 0) {
                return false;
            }
    
            if (vals?.on_hold === 'Yes') {
                return false;
            }
        }
    
        if (vals?.void === 'Yes') return false;
        return true;
    } catch (error) {
        console.error("Error at Validators.NCPDPBlock:", error); 
        return false;
    }
}


window.DSLFx.Transforms.CalcGrossAmountDue = (form, dd, trn, f, k) => {
    if (dd.options.mode === 'read') return false;
    console.log(`Calculating gross amount due`);
    const ncpdpDD = getParentDD(dd);
    const pricingSegmentNode = getPricingSegmentSubformNode(ncpdpDD);
    const COBPricingNode = getPPricingCOBNode(dd);
    const COBPricingRows = getFormNodeValues(COBPricingNode) || [];
    const pricingSegmentFieldNodes = pricingSegmentNode.field_nodes;

    const salesTax = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['sales_tax']) || 0.00);
    const flatTax = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['flat_tax']) || 0.00);
    const incentiveAmount = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['incv_amt_sub']) || 0.00);
    const dispenseFee = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['disp_fee_sub']) || 0.00);
    const ingredientCost = justGiveMeAFuckingNumber(window.DSLFields.value(pricingSegmentFieldNodes['ing_cst_sub']) || 0.00);
    const COBSumAmount = COBPricingRows.reduce((acc, row) => acc + justGiveMeAFuckingNumber(row.o_amt_sub) || 0.00, 0);
    const totalAmountDue = currency(ingredientCost + salesTax + flatTax + incentiveAmount + dispenseFee + COBSumAmount).value;
    window.DSLFields.value(pricingSegmentFieldNodes['gross_amount_due'], totalAmountDue);
    return true;
}