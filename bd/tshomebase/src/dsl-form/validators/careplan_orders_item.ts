import { request } from "@core/request";
import _ from 'lodash';
import { showToastError, checkIfFieldHasChanged } from "@utils/fx";

window.DSLFx.Validators.CheckPayerSupplyPARequirements = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckPayerSupplyPARequirements', k)) return;

    const billing_method = dd.value_field('billing_method');
    if (!billing_method || !['Insurance'].includes(billing_method)) {
        console.warn('Invalid or missing billing method');
        return;
    }

    const status_id = dd.value_field('status_id');
    if (status_id === '5') {
        return;
    }

    const site_id = dd.value_field('site_id');
    const patient_id = dd.value_field('patient_id');
    const inventory_id = dd.value_field('inventory_id');

    const primary_payer = dd.values()?.['insurance_id'] || [];
    if (!primary_payer?.id) {
        console.warn('No primary payer found or invalid payer ID');
        dd.value_field('auth_flag', '', true, true);
        return;
    }

    const CheckPayerSupplyPARequirements = async () => {
        try {

            if (!inventory_id) {
                console.warn('No Inventory ID found in careplan order item');
                return;
            }

            const response = await request({
                url: `/query/inventory_payer_pricing/?x1=${inventory_id}&x2=${primary_payer.id}&x3=${site_id}&x4=${patient_id}`,
            });
            if (!response?.data?.length) {
                console.warn('No pricing data returned');
                return;
            }

            let blockOrder = false;
            const pricingData = response.data[0];
            if (['Yes', 'Block'].includes(pricingData?.auth_required)) {
                const paId = dd.value_field('supplies_pa_id');
                if (!paId) {
                    dd.value_field('auth_flag', 'Yes', true, true);
                    if (pricingData?.auth_required === 'Block') {
                        window.prettyError('Authorization Required', 'The selected primary payer is set to block this item without a prior authorization. Cannot complete until authorization is attached.');
                        blockOrder = true;
                    } else {
                        blockOrder = false;
                    }
                }
            } else {
                dd.value_field('auth_flag', '', true, true);
            }
            if (pricingData?.not_covered === 'Yes') {
                window.prettyError('Not Covered', 'The selected primary payer is exclusively set to not cover this item. Please select a different payer or change the item for the order.');
                blockOrder = true;
            } else {
                blockOrder = blockOrder ? true : false;
            }

            if (blockOrder) {
                dd.value_field('payer_order_block', 'Yes', true, true);
            } else {
                dd.value_field('payer_order_block', '', true, true);
            }
        } catch (error) {
            console.error("Error at CheckPayerSupplyPARequirements:", error);
            showToastError(`Error checking payer PA requirements`);
            dd.value_field('auth_flag', '', true, true);
        }
    };

    CheckPayerSupplyPARequirements();
};
