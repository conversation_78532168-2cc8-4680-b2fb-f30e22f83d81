import { showToastError, checkIfFieldHasChanged } from "@utils/fx";
import { request } from "@core/request";

window.DSLFx.Validators.CloseOfMonthFillUp = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CloseOfMonthFillUp', k)) return;
    const endDate = dd.value_field('end_date');
    const startDate = dd.value_field('start_date');
    if (!endDate || !startDate) return;

    const CloseOfMonthFillUp = async () => {
        try {
            const endDate = dd.value_field('end_date');
            const response = await request({
                url: `/query/calculate_com_metrics/?x1=${startDate}&x2=${endDate}`,
                method: 'GET'
            });
            const comData = response.data[0].result;
            for (const [key, value] of Object.entries(comData)) {
                dd.value_field(key, (value !== null ? value : ''), true, true);
            };

        } catch (error) {
            console.log("Error at Close Of Month Fill Up Validator:", error);
            showToastError("Error running close of month calculations")
        }
    }
    CloseOfMonthFillUp();
};
