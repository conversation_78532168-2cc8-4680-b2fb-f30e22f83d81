import { request } from "@core/request";
import {
    showToastError
} from "@utils/fx";

window.DSLFx.Validators.DTCreateBlockMissingItems = (form, dd, vld, vals: any, k: string) => {
    if (vals?.from_ready_to_contact === 'Yes') {
        if (vals?.dispense_prescription?.length === 0) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes['dispense_prescription'], 'Please select a prescription to dispense');
        }
    } else {
        if (vals?.dispense_sorder?.length === 0 && vals?.dispense_prescription?.length === 0) {
            return 'Please select either supplies orders or a supply kit to dispense';
        }
    }

    return false;
}


window.DSLFx.Validators.LoadNextFillPrescriptionData = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    const rxId = window.DSLFields.value(dd?.field_nodes?.rx_id);
    const patientId = window.DSLFields.value(dd?.field_nodes?.patient_id);

    if (rxId) {
        return;
    }
    const LoadNextFillPrescriptionData = async () => {
        try {
            const response = await request({
                url: `/api/query/load_dt_create_rx_data?x1=${patientId}`
            });
            const responseData = response?.data?.[0] || null;
            if (responseData) {
                dd.value_field('rx_id', responseData.rx_id, true, true)
                dd.value_field('service_from', responseData.service_from, true, true)
                dd.value_field('service_to', responseData.service_to, true, true)
            }

        } catch (error) {
            console.error("Error at LoadNextFillPrescriptionData:", error);
            showToastError("Error loading shipping address");
        }
    }
    LoadNextFillPrescriptionData();
}
