import { request } from "@core/request";
import {
    showToastError
} from "@utils/fx";


window.DSLFx.Validators.FillOrderRXStartDate = async (form, dd, vld, f, k) => {
    const s = dd.value_field(k)
    if (s) return;
    let pid = dd.value_field('patient_id')
    if (!pid) {
        console.error('FillOrderRXStartDate: PID Not Found')
        window.prettyError('Not Found', 'Patient not found. Please contact support')
        return
    }
    try {
        const rx_res = await request({
            url: `/api/query/verbal_order_start_of_care?x1=${pid}`,
        })
        dd.value_field(k, rx_res?.data[0]?.start_of_care);
    } catch (error) {
        console.log("Error while fetching rx ", error);
        showToastError(`Error while fetching start of care date`);
    }
}
