import { request } from "@core/request";
import { showToastError } from "@utils/fx";

window.DSLFx.Validators.AddVisitNumber = (form: any, dd: any, vld: any, f: any, k: any) => {
    if (dd.options.mode == 'read') return false;

    try {
        const visitNumber = dd.value_field('visit_number');
        if (visitNumber) return;
            const patientId = dd.preset?.patient_id || null;
        if (!patientId) {
            console.log("No patient ID found in preset");
            return;
        }
        const AddVisitNumber = async () => {
            try {
                const response = await request({
                    url: `/encounter?filter=patient_id:${patientId}&limit=1&sort=-visit_number`,
                    method: 'GET',
                });
                if (response.data?.length > 0) {
                    const lastVisitNumber = response.data[0].visit_number;
                    const newVisitNumber = parseInt(lastVisitNumber, 10) + 1;
                    dd.value_field('visit_number', newVisitNumber, true, true);
                } else {
                    dd.value_field('visit_number', 1, true, true);
                }
            } catch (error) {
                console.error("Error while checking previous visits to generate visit number", error);
                showToastError(`Error calculating visit number`);
            }
        }
        AddVisitNumber();
    } catch (error) {
        console.error("Error while setting visit number", error);
        showToastError(`Error setting visit number`);
    }

}

window.DSLFx.Validators.PrefillNursingNotes = (form: any, dd: any, vld: any, f: any, k: any) => {
    if (dd.options.mode == 'read') return false;
    try {
        const orderId = dd.value_field('order_id') || null;
        if (!orderId) return;
        const AddNursingNotes = async () => {
            try {
                const order = await request({
                    url: `form/careplan_order/${orderId}`,
                    method: 'GET',
                });
                if(order.data?.length > 0){
                    const orderNo = order.data[0].order_no;
                    let orderItem = await request({
                        url: `form/careplan_order_rx/?filter=order_no:${orderNo}&filter=discontinued:!Yes&sort=-created_on`,
                        method: 'GET',
                    });
                    if(orderItem.data?.length === 0) return;
                    dd.value_field('subjective', orderItem.data[0]?.nursing_notes, true, true);
                    console.log(order.data, 'Order data here')
                    console.log(orderItem.data, 'Order item data here')
                } else {
                    console.log('Could not find order!')
                }
            } catch (error) {
                console.error("Error while getting nursing notes from order", error);
            }
        }
        AddNursingNotes();
    } catch (error) {
        console.error("Error while setting nursing notes", error);
    }
}