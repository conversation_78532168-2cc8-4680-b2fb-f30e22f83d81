import { request } from "@core/request";
import {
    checkIfFieldHasChanged,
    showToastError
} from "@utils/fx";

window.DSLFx.Validators.CheckIfDependentRequired = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckIfDependentRequired', k)) return;

    const CheckIfDependentRequired = async () => {
        let insurance_id = dd.value_field("insurance_id");
        if (!insurance_id) return;
        try {
            const insurance = await request({
                url: `/form/patient_insurance/${insurance_id}`,
                method: 'GET',
            });
            if (insurance.data.length > 0) {
                if (insurance.data[0].medical_relationship_id !== '18') {
                    let parent_ref = dd?.options?.wrapper?.subforms?.formmap?.med_claim
                    parent_ref.value_field('dependent_required', 'Yes', true, true);
                }
            }
        } catch (error) {
            console.log("Error while fetching insurance data:", error);
        }
    }
    CheckIfDependentRequired();
}
