import { request } from "@core/request"
import { checkIfFieldHasChanged, showToastError } from "@utils/fx"

window.DSLFx.Validators.CheckSites = (form, dd, vld, f, k) => {
    let from_site = dd.value_field('site_id')
    let to_site = dd.value_field('to_site_id')
    if (from_site && to_site && from_site == to_site) {
        window.prettyError('Error', 'From Site and To Site cannot be same')
        dd.value_field(k, '', true, true)
    }
}
window.DSLFx.Validators.TransferCheckQuantity = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'TransferCheckQuantity', k)) return;
    const site_id = dd.value_field('site_id');
    const inventory_id = dd.value_field('inventory_id');
    const quantity = dd.value_field('quantity');
    const lot_id = dd.value_field('lot_id');
    const serial_id = dd.value_field('serial_id');
    if (!site_id || !inventory_id || !quantity) return;
    if (quantity == 0) {
        return window.DSLFx.ValidateFieldError(dd.field_nodes['quantity'], 'Quantity must be greater than 0.')
    }
    try {
        const CheckStock = async () => {
            let url = '/inventory?func=check_stock&site_id=' + site_id + '&inventory_id=' + inventory_id
            if (lot_id)
                url += '&lot_id=' + lot_id
            if (serial_id)
                url += '&serial_id=' + serial_id
            const stock = await request({
                url: url
            })
            if (stock?.data.results?.inv_quantity) {
                const inv_quantity = stock?.data.results.inv_quantity
                const lot_quantity = stock?.data.results.lot_quantity
                const serial_quantity = stock?.data.results.sni_quantity
                if (serial_id && (quantity > serial_quantity)) {
                    return window.DSLFx.ValidateFieldError(dd.field_nodes['quantity'], 'Quantity in stock exceeds transfer amount. (Serial Stock: ' + serial_quantity + ')')
                } else if (lot_id && (quantity > lot_quantity)) {
                    return window.DSLFx.ValidateFieldError(dd.field_nodes['quantity'], 'Quantity in stock exceeds transfer amount. (Lot Stock: ' + lot_quantity + ')')
                } else if (quantity > inv_quantity) {
                    return window.DSLFx.ValidateFieldError(dd.field_nodes['quantity'], 'Quantity in stock exceeds transfer amount. (Stock: ' + inv_quantity + ')')
                }
            } else {
                window.prettyError('Error Fetching In-Stock', 'We encountered an error fetching the stock information. Please try again and contact support if the problem persists.')
                dd.value_field('quantity', '', true, true)
            }
        }
        CheckStock()
    } catch (error) {
        console.log("Error while fetching stock.", error)
        showToastError("Error while fetching stock.")
    }
}

window.DSLFx.Validators.LockQuantity = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'LockQuantity', k)) return;
    const lot_id = dd.value_field('lot_id');
    const serial_id = dd.value_field('serial_id');
    if (lot_id) {
        window.DSLFields.readonly(dd.field_nodes['quantity'], false);
    };
    try {
        if (serial_id) {
            dd.value_field('quantity', 1, true, true)
            window.DSLFields.readonly(dd.field_nodes['quantity'], true);
        }
    } catch (error) {
        console.log("Error while locking the quantity", error)
        showToastError("Error while locking the quantity")
    }
}