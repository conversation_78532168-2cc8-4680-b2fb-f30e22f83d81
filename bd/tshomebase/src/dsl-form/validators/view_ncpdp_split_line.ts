window.DSLFx.Validators.ValidateLineQuantity = (form, dd, vld, f, k) =>{
    let max_quantity = dd.value_field('max_quantity');
    let quantity = dd.value_field('quantity');
    if (!max_quantity || !quantity) return;
    if (parseFloat(quantity) > parseFloat(max_quantity)){
        return window.DSLFx.ValidateFieldError(f, form.fields[k].view.label + ' must be less then max quantity')
    }else{
        return false
    }
}