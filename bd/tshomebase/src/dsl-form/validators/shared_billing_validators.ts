
import currency from "currency.js";

window.DSLFx.Validators.BalanceAmountValidator = (form, dd, vld, f, k, flv) => {

    const amount = dd.value_field('amount');
    if (amount <= 0) return;
    const chargesApplied = dd.value_field('charges_applied') || [];

    const totalChargesApplied = chargesApplied.reduce((sum: number, charge: any) => {
        const chargeCurrency = charge?.field_int || 0.00;
        return sum + currency(chargeCurrency).value;
    }, 0);

    if (currency(amount).value !== totalChargesApplied) {
        return window.DSLFx.ValidateFieldError(dd.field_nodes['amount'], `The total amount (${currency(amount).format()}) must equal the sum of all charges applied (${currency(totalChargesApplied).format()})`);
    }
    return false;
}