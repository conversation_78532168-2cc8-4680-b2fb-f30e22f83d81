
import { request } from "@core/request";
import { showToastError, getParentFormData, checkIfFieldHasChanged, updateLabel, justGiveMeAFuckingNumber } from "@utils/fx";
import moment from "moment";
import currency from "currency.js";

const setValueField = (dd: any, field: string, value: any) => {
    dd.value_field(field, value, true, true);
};

const loadDtItemPricingInfo = async (dd: any, inventoryId: string | number, insuranceId: string | number, siteId: string | number, patientId: string | number) => {
    try {

        const response = await request({
            url: `/query/inventory_payer_pricing/?x1=${inventoryId}&x2=${insuranceId}&x3=${siteId}&x4=${patientId}`,
            method: 'GET'
        });

        const result = response?.data?.[0] || { error: 'Missing payer pricing data'};
        if (result.error) {
            console.error("Error at loadDtItemPricingInfo:", result.error);
            showToastError(result.error);
            return;
        }
        const billEa = currency(result.bill_ea || 0.0).value;
        setValueField(dd, 'billed_ea', billEa);

        const expectedEa = currency(result.expected_ea || 0.0).value;
        setValueField(dd, 'expected_ea', expectedEa);

        const listEa = currency(result.list_ea || 0.0).value;
        setValueField(dd, 'list_ea', listEa);

    } catch (error) {
        console.error("Error at loadDtItemPricingInfo:", error);
        showToastError('Error while updating insurance pricing.');
    }
}

const loadDispenseQuantityNeeded = async (dd: any, dispenseQuantity: number) => {

    const ticketNo = dd.value_field('ticket_no');
    const ticketItemNo = dd.value_field('ticket_item_no');
    if (!ticketNo || !ticketItemNo) {
        console.error("Error at loadDispenseQuantityNeeded:", `Missing ticket no (${ticketNo}) or ticket item no (${ticketItemNo})`);
        return;
    }
    const response = await request({
        url: `/query/dt_dispensed_quantity/?x1=${ticketNo}&x2=${ticketItemNo}`,
        method: 'GET'
    });
    const result = response?.data?.[0] || { error: 'Missing dispense quantity needed data'};
    if (result.error) {
        console.error("Error at loadDispenseQuantityNeeded:", result.error);
        showToastError(result.error);
        return;
    }
    const remainingQty = dispenseQuantity - (result.dispensed_quantity || 0);
    setValueField(dd, 'dispense_quantity', remainingQty);
    if (remainingQty <= 0) {
        setValueField(dd, 'pulled_all_items', 'Yes');
    } else {
        setValueField(dd, 'pulled_all_items', '');
    }
}

const checkForCompoundingInstrChanges = async (dd: any, rxId: string, compoundingInstructions: string) => {
    const response = await request({
        url: `/api/form/careplan_order_rx?filter=id:${rxId}`
    });
    const rxRec = response?.data?.[0] || { error: 'Missing careplan order rx data'};
    if (rxRec.error) {
        console.error("Error at checkForCompoundingInstrChanges:", rxRec.error);
        showToastError(rxRec.error);
        return;
    }
    const rxCompoundingInstructions = rxRec.compounding_instructions;
    if (rxCompoundingInstructions !== compoundingInstructions) {
        setValueField(dd, 'compound_instructions_changed', 'Yes');
    } else {
        setValueField(dd, 'compound_instructions_changed', '');
    }
}

window.DSLFx.Validators.ChangeDispensedLabelToChargeQuantity = (form, dd, vld, f, k) => {
    try {
        if(dd.value_field('type') == "Billable"){
            updateLabel('dispense_quantity', 'Charged Quantity', dd, false);
        }else{
            updateLabel('dispense_quantity', 'Dispensed Quantity', dd, false);
        }
    } catch (error) {
        console.error("Error at ChangeDispensedLabelToChargeQuantity", error);
        showToastError("Error checking delivery ticket");
    }
}


window.DSLFx.Validators.CalculateLastThroughDate = (form, dd, vld, f, k) => {
    try {
        const careplanDeliveryTick = dd?.options?.wrapper?.subforms?.formmap?.careplan_delivery_tick?.values();
        let deliveryDate = careplanDeliveryTick?.delivery_date;
        if (deliveryDate) {
            const daySupply = dd.value_field('day_supply');
            const lastThroughDate = moment(deliveryDate).add(daySupply, 'days').toDate();
            dd.value_field('last_through_date', lastThroughDate, true, true);
        }
    } catch (error) {
        console.error("Error at CalculateLastThroughDate:", error);
        showToastError("Error calculating last through date");
    }
}


window.DSLFx.Validators.UpdateInsuranceEstimatedTotals = (form, dd, vld, f, k) => {
    console.log('UpdateInsuranceEstimatedTotals');
    if (!checkIfFieldHasChanged(dd, 'UpdateInsuranceEstimatedTotals', k)) return;
    try {
        const bill = dd.value_field('bill');
        if (bill !== 'Yes') return;
        const careplanDeliveryTick = dd?.options?.wrapper?.subforms?.formmap?.careplan_delivery_tick?.values();
        const siteId = careplanDeliveryTick?.site_id;
        const patientId = careplanDeliveryTick?.patient_id;
        const insuranceId = dd.value_field('insurance_id');
        const inventoryId = dd.value_field('inventory_id');
        if (!siteId || !patientId || !insuranceId || !inventoryId) {
            console.log('Missing required fields to calculate pricing.');
            return;
        }

        const UpdateInsuranceEstimatedTotals = async () => {
            try {
                await loadDtItemPricingInfo(dd, inventoryId, insuranceId, siteId, patientId);
            } catch (error) {
                console.error("Error at UpdateInsuranceEstimatedTotals:", error);
                showToastError('Error while calculating item pricing.');
            }
        }
        UpdateInsuranceEstimatedTotals();
    } catch (error) {
        console.error("Error at UpdateInsuranceEstimatedTotals:", error);
        showToastError("Error while looking up item pricing");
    }
}

window.DSLFx.Validators.UpdateQuantityNeeded = (form, dd, vld, f, k) => {
    console.log('UpdateQuantityNeeded');
    if (!checkIfFieldHasChanged(dd, 'UpdateQuantityNeeded', k)) return;
    try {
        const dispenseQuantity = justGiveMeAFuckingNumber(dd.value_field('dispense_quantity'));
        if (dispenseQuantity <= 0) return;

        const UpdateQuantityNeeded = async () => {
            try {
                await loadDispenseQuantityNeeded(dd, dispenseQuantity);
            } catch (error) {
                console.error("Error at UpdateQuantityNeeded:", error);
                showToastError('Error while updating dispense quantity needed.');
            }
        }
        UpdateQuantityNeeded();
    } catch (error) {
        console.error("Error at UpdateQuantityNeeded:", error);
        showToastError("Error while looking up dispense quantity needed");
    }
}

window.DSLFx.Validators.LoadDeliveryTicketStatus = (form, dd, vld, f, k) => {
    // try {
    //     let careplanDeliveryTick = dd?.options?.wrapper?.subforms?.formmap?.careplan_delivery_tick?.values();
    //     let careplanDeliveryTickStatus = careplanDeliveryTick?.status;
    //     if(careplanDeliveryTickStatus && careplanDeliveryTickStatus != dd.value_field('status') ){
    //         dd.value_field('status', careplanDeliveryTickStatus, true, true);
    //     }
    // } catch (error) {
    //     console.error("Error at LoadDeliveryTicketStatus:", error);
    //     showToastError("Error loading delivery ticket status");
    // }
}

window.DSLFx.Validators.RunChangedPayerAlerts = (form, dd, vld, f, k) => {
    // TODO: Implement this
}

window.DSLFx.Validators.CheckForCompoundingInstrChanges = (form, dd, vld, f, k) => {
    console.log('CheckForCompoundingInstrChanges');
    try {
        const templateType = dd.value_field('template_type');
        if (!templateType) return;
        if (!['Compound', 'IV', 'TPN'].includes(templateType)) return;
        const compoundingInstructions = dd.value_field('compounding_instructions');
        if (!compoundingInstructions || compoundingInstructions.length === 0) return;
        const rxId = dd.value_field('rx_id');
        if (!rxId) {
            console.error("Error at CheckForCompoundingInstrChanges:", "Missing rx_id");
            showToastError("Error while checking compounding instructions. Missing prescription id.");
            return;
        }
        const CheckForCompoundingInstrChanges = async () => {
            try {
                await checkForCompoundingInstrChanges(dd, rxId, compoundingInstructions);
            } catch (error) {
                console.error("Error at CheckForCompoundingInstrChanges:", error);
                showToastError('Error while checking compounding instructions.');
            }
        }
        CheckForCompoundingInstrChanges();
    } catch (error) {
        console.error("Error at CheckForCompoundingInstrChanges:", error);
        showToastError("Error while checking compounding instructions");
    }
}

window.DSLFx.Validators.CheckPulledComplete = (form, dd, vld, f, k) => {
    console.log('CheckPulledComplete');
    try {
        const dispenseQuantity = justGiveMeAFuckingNumber(dd.value_field('dispense_quantity'));
        if (dispenseQuantity <= 0) return;

        const CheckPulledComplete = async () => {
            try {
                await loadDispenseQuantityNeeded(dd, dispenseQuantity);
            } catch (error) {
                console.error("Error at CheckPulledComplete:", error);
                showToastError('Error while checking pulled complete.');
            }
        }
        CheckPulledComplete();
    } catch (error) {
        console.error("Error at CheckPulledComplete:", error);
        showToastError("Error while checking pulled complete");
    }
}