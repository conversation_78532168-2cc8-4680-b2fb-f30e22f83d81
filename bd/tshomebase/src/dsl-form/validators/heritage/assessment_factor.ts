import _ from "lodash";
import moment from "moment";
import { showToastError, checkIfFieldHasChanged } from "@utils/fx";


window.DSLFx.Validators.AddHTCAddress = (_form :any, dd: any, _vld: any, _f: any, k: any) => {

    try {
        if (!checkIfFieldHasChanged(dd, 'AddHTCAddress', k)) return;
        const htc = dd.value_field(k);
        const dob = dd.value_field('dob');
        if (!dob) return;
        const age = moment().diff(moment(dob), 'years');
        if (!htc) {
            dd.value_field('cust_referrer_address', '', true, true);
        } else if (htc == 'Oklahoma HTC') {
            dd.value_field('cust_referrer_address', "1200 Children's Ave. Suite 10000", true);
        } else if (htc == 'Kansas City HTC' && age < 18) {
            dd.value_field('cust_referrer_address', '2401 Gillham Rd.', true);
        } else if (htc == 'Kansas City HTC' && age >= 18) {
            dd.value_field('cust_referrer_address', '2301 Holmes St.', true);
        } else if (htc == 'St. Louis HTC') {
            dd.value_field('cust_referrer_address', '3655 Vista Ave.', true);
        } else if (htc == 'Arkansas HTC') {
            dd.value_field('cust_referrer_address', '1 Children’s Way', true);
        } else if (htc == 'Nebraska HTC') {
            dd.value_field('cust_referrer_address', '505 S 45th St.', true);
        } else if (htc == 'Cure 4 the kids') {
            dd.value_field('cust_referrer_address', '1 Breakthrough Way STE 300 Las Vegas NV 89135', true);
        } else if (htc == 'Sugar House Hematology') {
            dd.value_field('cust_referrer_address', '1280 East Stringham Avenue Salt Lake City, UT 84106', true);
        } else if (htc == 'Children’s Hospital New Orleans – Louisiana HTC') {
            dd.value_field('cust_referrer_address', '1430 Tulane Avenue #8027 New Orleans, LA 70112', true);
        } else if (htc == 'Iowa City HTC') {
            dd.value_field('cust_referrer_address', '200 Hawkins Drive Iowa City, IA 52242', true);
        }
    } catch (error) {
        console.error("Error while setting HTC address:", error);
        showToastError(`Error setting HTC address`);
    }

}