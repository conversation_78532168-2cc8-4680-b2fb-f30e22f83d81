import {
    getFormNodeValues,
    getParentSubformNode,
    showToastError
} from "@utils/fx";

const getDiagnosisLoop = (dd: any) => {
    return getParentSubformNode(dd, 'med_claim_1500_dx', 'subform_dx');
}

window.DSLFx.Validators.AddClaimDiagnoses1500 = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;

    try {
        const cdxIds = dd.value_field(k);
        const diagnosisLoopNode = getDiagnosisLoop(dd);
        const diagnosisValues = getFormNodeValues(diagnosisLoopNode);
        const dxIds = (diagnosisValues || []).map(item => item.dx_id);
        const filteredDxIds = dxIds.filter(id => !cdxIds.includes(id));
        if (filteredDxIds.length) {
            dd.value_field(k, dxIds, true, true);
        }
    } catch (error) {
        console.log("Error while adding claim diagnoses", error);
        showToastError(`Error adding claim diagnoses to service lines filter`);
    }
}
