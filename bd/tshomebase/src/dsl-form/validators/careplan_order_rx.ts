import { request } from "@core/request";
import { performInteractionsCheck } from "@dsl-form/util/interactions";
import {showToastError, checkIfFieldHasChanged, uuid } from "@utils/fx";
import moment from 'moment';
window.DSLFx.Validators.UpdateMedicationReview = (form, dd, vld, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'UpdateMedicationReview', k)) return;
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const UpdateMedicationReview = async () => {
            
            const height = dd.value_field('height');
            const weight = dd.value_field('weight');
            const volumePerDose = dd.value_field('volume_per_dose');
            const daySupply = dd.value_field('day_supply');
            const dosesToPrep = dd.value_field('doses_to_prep');
            if (!weight || !volumePerDose || !daySupply || !dosesToPrep) return;
            const response = await request({
                url: `/query/medication_review/?x1=${volumePerDose}&x2=${height}&x3=${weight}&x4=${daySupply}&x5=${dosesToPrep}`,
            });
            if (response.data.length === 0) {
                console.error("Error fetching medication review data");
                return null;
            }
            const medicationReview = response.data[0].result;
            dd.value_field('bsa', medicationReview.bsa, true, true);
            dd.value_field('ml_per_day', medicationReview.ml_per_day, true, true);
            dd.value_field('ml_m2_day', medicationReview.ml_m2_day, true, true);
            dd.value_field('final_dose_quantity', medicationReview.final_dose_quantity, true, true);
            dd.value_field('final_day_quantity', medicationReview.final_day_quantity, true, true);
            dd.value_field('final_m2_day_quantity', medicationReview.final_m2_day_quantity, true, true);
            dd.value_field('final_kg_day_quantity', medicationReview.final_kg_day_quantity, true, true);
        }
        UpdateMedicationReview();
    } catch (error) {
        console.error("Error while calculating medication review metrics:", error);
        showToastError(`Error calculating medication review metrics`);
    }
}

window.DSLFx.Transforms.CalculateContainersToPrep = (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateContainersToPrep', k)) return;

    try {
        const dosesToPrep = dd.value_field('doses_to_prep');
        const dosesPerContainer = dd.value_field('doses_per_container');
        if (!dosesToPrep || !dosesPerContainer) return;
        const containersToPrep = Math.ceil(dosesToPrep / dosesPerContainer);
        dd.value_field('containers_to_prep', containersToPrep, true, true);
    } catch (error) {
        console.error("Error while calculating containers to prep:", error);
        showToastError(`Error calculating containers to prep`);
    }
};

window.DSLFx.Transforms.CalculateNextFillDate = (form, dd, trn, f, k) => {
    if (!checkIfFieldHasChanged(dd, 'CalculateNextFillDate', k)) return;

    try {
        const daySupply = dd.value_field('day_supply');
        const start_date = dd.value_field('start_date');
        const nextFillDate = moment(start_date).add(daySupply, 'days').format('MM/DD/YYYY')
        dd.value_field('next_fill_date', nextFillDate, true, true);
    } catch (error) {  
        console.error("Error while calculating next fill date:", error);
        showToastError(`Error calculating next fill date`);
    }
    return;
}

window.DSLFx.Validators.CheckUniqueItems = (form, dd, v, d, k) => {
    if (!checkIfFieldHasChanged(dd, 'CheckUniqueItems', k)) return;
    try {
        const fieldData = dd?.field_nodes?.[k]?.data('helper')?.getAllData()?.data || [];
        const inventoryIds = fieldData.map((item: any) => item.inventory_id);
        const uniqueInventoryIds = [...new Set(inventoryIds)];
        if (uniqueInventoryIds.length !== inventoryIds.length) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes[k], `Duplicate drugs in work ticket. Please ensure each drug is unique.`);
        }
    } catch (error) {
        console.error("Error validating unique items:", error);
        showToastError("Error validating unique items.");
    }
}

window.DSLFx.Validators.GenerateMedicationReview = (form, dd, v, d, k) => {
    if (!checkIfFieldHasChanged(dd, 'GenerateMedicationReview', k)) return;
    try {
        const fieldData = dd?.field_nodes?.[k]?.data('helper')?.getAllData()?.data || [];
        const inventoryIds = fieldData.map((item: any) => item.inventory_id);
        const uniqueInventoryIds = [...new Set(inventoryIds)];
        if (uniqueInventoryIds.length !== inventoryIds.length) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes[k], `Duplicate drugs in work ticket. Please ensure each drug is unique.`);
        }
    } catch (error) {
        console.error("Error validating unique items:", error);
        showToastError("Error validating unique items.");
    }
}

window.DSLFx.Validators.DrugAllergyInteractionRx = (form, dd, v, d, k) => {
    if (!checkIfFieldHasChanged(dd, 'DrugAllergyInteractionRx', k)) return;
    try {
        if (!dd.value_field(k)) return;
        const patient_id = dd.value_field('patient_id');
        const rx_id = dd?.values()?.id
        if (!patient_id || !rx_id) return;
        const InteractionsCheck = async () => {
            const res = await performInteractionsCheck(patient_id, rx_id, true)
            if (!res.accepted) {
                return
            }
        }
        InteractionsCheck();
    } catch (error) {
        console.error("Error validating drug allergy interaction:", error);
        showToastError("Error validating drug allergy interaction.");
    }
}