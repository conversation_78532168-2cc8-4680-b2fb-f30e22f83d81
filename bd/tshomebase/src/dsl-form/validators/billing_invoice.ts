
import moment from 'moment-business-days';
import currency from 'currency.js';
import _ from "lodash";
import { request } from "@core/request";
import {
    showToastError,
    checkIfFieldHasChanged,
    justGiveMeAFuckingNumber
} from "@utils/fx";

const setValueField = (dd: any, field: string, value: any) => {
    dd.value_field(field, value, true, true);
};

const fetchInvoiceBalances = async (dd: any) => {
        try {
            const invoiceNo = dd.value_field('invoice_no');
            const response = await request({
                url: `/query/fetch_invoice_balances/?x1=${invoiceNo}`,
                method: 'GET'
            });
            if (response.data.length === 0) {
                setValueField(dd, 'view_total_paid', 0.00);
                setValueField(dd, 'view_total_adjusted', 0.00);
                setValueField(dd, 'view_total_balance_due', 0.00);
                setValueField(dd, 'view_total_cost', 0.00);
                setValueField(dd, 'view_total_expected', 0.00);
                showToastError("Error fetching invoice balances");
                return;
            }

            const item = response.data[0];
            for (const [key, value] of Object.entries(item)) {
                setValueField(dd, key, value);
            }
        } catch (error) {
            showToastError("Error fetching invoice balance information");
            console.log("Error at fetchInvoiceBalances", error);
        }
}

window.DSLFx.Validators.CheckClosedPeriod = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!checkIfFieldHasChanged(dd, 'CheckClosedPeriod', k)) return false;
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const postDateTime = dd.value_field('post_datetime');
        if (!postDateTime) return false;
        const postDate = moment(postDateTime).format('MM/DD/YYYY');

        const CheckClosedPeriod = async () => {
            try {
                const response = await request({
                    url: `/query/check_closed_period/?x1=${postDate}`,
                    method: 'GET'
                });
                if (response.data.length === 0) {
                    return false;
                }
                const isClosedPeriod = response.data[0].is_closed_period;
                if (isClosedPeriod) {
                    return 'Post date is in a closed period.';
                }
                return false;
            } catch (error) {
                console.error("Error at CheckClosedPeriod:", error);
                showToastError('Error while checking closed period.');
            }
        }
        CheckClosedPeriod();
    } catch (error) {
        console.error("Error checking is post date is valid:", error);
        showToastError("Error checking is post date is valid");
    }
}


window.DSLFx.Validators.FetchUnappliedCashBalance = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const FetchUnappliedCashBalance = async () => {
            const payer_id = dd.value_field('payer_id');
            const patient_id = dd.value_field('patient_id');
            if (!payer_id || !patient_id) {
                console.error("Payer or patient ID not found. Cannot fetch unapplied cash balance.");
                return;
            } 
            const response = await request({
                url: `/query/account_unapplied_cash/?x1=${payer_id}&x2=${patient_id}&x3=${payer_id}`,
            });
            if (response.data.length === 0) {
                console.error("Error fetching unapplied cash balance data");
                return null;
            }
            const unappliedCashBalance = response.data[0].unapplied_cash_balance;
            if (unappliedCashBalance) {
                const parsedUnappliedCashBalance = currency(justGiveMeAFuckingNumber(unappliedCashBalance)).value;
                if (parsedUnappliedCashBalance > 0) {
                    dd.value_field('unapplied_cash_available', 'Yes', true, true);
                    dd.value_field('available_unapplied_cash', parsedUnappliedCashBalance, true, true);
                    const totalBalance = currency(justGiveMeAFuckingNumber(dd.value_field('total_balance_due') || 0.0)).value;
                    const totalPaid = currency(justGiveMeAFuckingNumber(dd.value_field('total_paid') || 0.0)).value;
                    const remainingBalance = totalBalance - totalPaid;
                    if (parsedUnappliedCashBalance > remainingBalance) {
                        dd.value_field('allocated_unapplied_cash', remainingBalance, true, true);
                    } else {
                        dd.value_field('allocated_unapplied_cash', parsedUnappliedCashBalance, true, true);
                    }
                } else {
                    dd.value_field('unapplied_cash_available', '', true, true);
                    dd.value_field('available_unapplied_cash', 0.00, true, true);
                    dd.value_field('allocated_unapplied_cash', 0.00, true, true);
                }
            }
        }
        FetchUnappliedCashBalance();
    } catch (error) {
        console.error("Error while checking available unapplied cash:", error);
        showToastError(`Error while checking for unallocated cash`);
    }
}

window.DSLFx.Validators.FetchInvoiceBalanceDue = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted !== 'Yes') return;
        const FetchInvoiceBalanceDue = async () => {
            await fetchInvoiceBalances(dd);
        }
        FetchInvoiceBalanceDue();
    } catch (error) {
        console.error("Error while checking available invoice balances:", error);
        showToastError(`Error while checking available invoice balances`);
    }
}

window.DSLFx.Validators.ValidateAllocatedCash = (form, dd, vld, f, k) => {
    if (dd.options.mode == 'read') return false;
    if (!checkIfFieldHasChanged(dd, 'ValidateAllocatedCash', k)) return;

    if (!dd?.options?.wrapper?.form_rendered) {
        return;
    }
    try {
        const revenueAlreadyPosted = dd.value_field('revenue_accepted_posted');
        if (revenueAlreadyPosted === 'Yes') return;
        const totalBalance = currency(justGiveMeAFuckingNumber(dd.value_field('total_balance_due') || 0.0)).value;
        const totalPaid = currency(justGiveMeAFuckingNumber(dd.value_field('total_paid') || 0.0)).value;
        const remainingBalance = totalBalance - totalPaid;
        const availableCash = currency(justGiveMeAFuckingNumber(dd.value_field('available_unapplied_cash') || 0.0)).value;
        const allocatedCash = currency(justGiveMeAFuckingNumber(dd.value_field('allocated_unapplied_cash') || 0.0)).value;

        if (allocatedCash > 0 && allocatedCash > remainingBalance) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes['allocated_unapplied_cash'], `The total allocated cash cannot be greater than the remaining balance (${currency(remainingBalance).format()})`);
        } else if (allocatedCash > 0 && allocatedCash > availableCash) {
            return window.DSLFx.ValidateFieldError(dd.field_nodes['allocated_unapplied_cash'], `The total allocated cash cannot be greater than the amount available (${currency(availableCash).format()})`);
        }
    } catch (error) {
        console.error("Error while checking cash allocation:", error);
        showToastError(`Error checking cash allocation`);
    }
}