import { request } from "@core/request";
import { getCarePlanLinkMap } from "@modules/patient/helper";
import { justGiveMeAFuckingNumber } from "@utils/fx";

export const performInteractionsCheck = async (patientId: number | string, rxId: number | string, popups = true) => {
	return new Promise<{ success: boolean; accepted: boolean; error: string | null; stack: any | null }>(
		(resolve, reject) => {
			patientId = parseInt(patientId as string);
			rxId = parseInt(rxId as string);
			if (isNaN(patientId) || isNaN(rxId)) {
				resolve({
					success: false,
					accepted: false,
					error: "Invalid patient or rx id",
					stack: null,
				});
				console.error("Invalid patient or rx id", patientId, rxId);
				return;
			}
			window.prettyNotify("Checking Interactions");
			request({
				url: "/dispense/?func=check_interactions&patient_id=" + patientId + "&rx_id=" + rxId,
			})
				.then(async (res) => {
					if (res?.data?.create.interactions) {
						let linkMap: any = {
							link: "patient",
							links: ["patient"],
							linkid: { patient: patientId },
						};
						const lm = await getCarePlanLinkMap(linkMap);
						if ("error" in lm) {
							const e = {
								success: false,
								accepted: false,
								error: lm.error,
								stack: null,
							};
							if (popups) {
								window.prettyError("Error", e.error);
							}
							resolve(e);
							return;
						}
						linkMap = lm;
						const dfd = window.Flyout.open({
							form: "interactions",
							preset: {
								...res?.data?.create?.interactions?.preset || {},
								patient_id: patientId,
								rx_id: rxId,
							},
							linkMap,
							...linkMap,
						});
						dfd.done((data) => {
							resolve({
								success: true,
								accepted: true,
								error: null,
								stack: null,
							});
						});
						dfd.fail((err) => {
							resolve({
								success: true,
								accepted: false,
								error: null,
								stack: null,
							});
						});
					} else {
						const e = {
							success: false,
							accepted: false,
							error: "Unexpected response from interactions check",
							stack: null,
						};
						if (popups) {
							window.prettyError("Error", e.error);
						}
						resolve(e);
					}
				})
				.catch((err) => {
					console.error(err);
					const e = {
						success: false,
						accepted: false,
						error: "Error running interactions check",
						stack: err,
					};
					if (popups) {
						window.prettyError("Error", e.error);
					}
					resolve(e);
				})
				.finally(() => {
					window.prettyNotify("");
				});
		}
	);
};

window.performInteractionsCheck = performInteractionsCheck;
