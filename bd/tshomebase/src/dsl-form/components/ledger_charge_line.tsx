import { FC, useMemo } from "react";
import { DSLFieldEmbedBtnsComponentProps } from "@dsl/fields/field-embed";
import { DSLCardActionButton, DSLCardViewRef, openPartialFormFillPopup } from "@blocks/dsl-card-view";
import _ from "lodash";
import { IFormData } from "@hooks/index";
import { presetWithCardRef } from "@utils/dsl-fx";
import { getMasterWrapper } from "@utils/fx";
import { request } from "@core/request";

export const LedgerChargeLineEmbedBtns: FC<DSLFieldEmbedBtnsComponentProps> = (props) => {
	const { tableRef, disabled, k, form, dd, getLinkMapFromPreset, v, hash, selected, refreshGridInternal } = props;

	const getPreset = () => {
		if (!v.view?.embed?.add_preset) return {};
		let preset = _.cloneDeep(v.view?.embed?.add_preset);
		for (let key in preset) {
			let v = preset[key];
			if (v && typeof v === "string" && v.startsWith("{") && v.endsWith("}")) {
				const vv = v.replace(/[{}]/g, "") as string;
				const vf = dd.preset[vv] || dd.value_field_get(vv, true);
				preset[key] = vf;
			}
		}
		delete preset.id;
		return preset;
	};

	const { record, invoice, master } = useMemo(() => {
		const data = ((dd?.field_nodes?.[k].data("helper")?.getAllData()?.data || []) as IFormData[]).find(
			(r) => r.id == selected
		);
		const master = getMasterWrapper(dd);
		const invoice = master?.subforms?.records?.[0]?.preset || {};
		return { record: data, invoice, master };
	}, [selected, hash]);

	const onNew = () => {
		const preset = getPreset();
		preset.inventory_type_filter = ["Billable"];
		const firstRecord = dd?.field_nodes?.charge_line.data("helper")?.getAllData()?.data?.[0] || {};
		preset.claim_no = firstRecord.claim_no;
		preset.ticket_no = firstRecord.ticket_no;

		const errorFields = [];
		for (let key in preset) {
			let v = preset[key];
			if (!v && !["calc_invoice_split_no", "ticket_no"].includes(key)) {
				errorFields.push(key);
			}
		}
		if (errorFields.length > 0) {
			window.prettyError(false, `Error Generating Preset for Charge Line: ${errorFields.join(", ")}`);
			return;
		}
		const linkMap = getLinkMapFromPreset(preset);
		const dfd = window.Flyout.open({
			form: v.view.embed.add_form || form,
			...linkMap,
			autoRecoverEnabled: false,
			preset: preset,
			on_loaded: (cardView) => {
				const idx = cardView.subscribe("ready", (ref: DSLCardViewRef) => {
					requestIdleCallback(() => {
						presetWithCardRef(ref, ref.form, {
							inventory_type_filter: ["Billable"],
						});
					});
					cardView.unsubscribe("ready", idx);
				});
			},
		});
		dfd.done((data) => {
			refreshGridInternal();
		});
	};

	const sendPUTRequest = (data = {}) => {
		return request({
			url: `/form/${form}/${record?.id}/archive/`,
			method: "PUT",
			data: data,
		});
	};

	const onEdit = () => {
		props.onEdit();
	};

	const refreshMaster = () => {
		refreshGridInternal();
		try {
			getMasterWrapper(dd).options.parent.tab_do_refresh();
		} catch (err) {
			console.error(err);
		}
	};

	const onDelete = () => {
		// mark as void and archive
		const callDelete = () => {
			sendPUTRequest({
				void: "Yes",
				archived: true,
			})
				.then((data) => {
					refreshMaster();
				})
				.catch((err) => {
					window.prettyError(false, "Error Deleting Charge Line");
					console.error(err);
				});
		};
		window.prettyConfirm(
			"Delete",
			"Do you want to delete charge line" + record?.auto_name ? ": " + record?.auto_name : "",
			"Delete",
			"Close",
			callDelete,
			() => {},
			window.BootstrapDialog.TYPE_DANGER
		);
	};
	const onUnlink = () => {
		const callUnlink = () => {
			sendPUTRequest({
				invoice_no: null,
				status: null,
				claim_no: null,
			})
				.then((data) => {
					refreshMaster();
				})
				.catch((err) => {
					window.prettyError(false, "Error Unlinking Charge Line");
					console.error(err);
				});
		};
		window.prettyConfirm(
			"Unlink",
			"Do you want to unlink charge line" + record?.auto_name ? ": " + record?.auto_name : "",
			"Unlink",
			"Close",
			callUnlink,
			() => {},
			window.BootstrapDialog.TYPE_DANGER
		);
	};

	const whatToDo = () => {
		if (!record) return false;
		if (!window.Auth.can_update_any(form)) return false;
		if (record.is_primary_drug_ncpdp === "Yes") return false;
		if (invoice.delivery_ticket_id) return false;
		if (invoice.parent_invoice_no || record.inventory_type === "Billable") return "delete";
		return "unlink";
	};

	return (
		<>
			{window.Auth.can_create(form) && v.view.grid.add !== "none" && (
				<DSLCardActionButton label="Add" icon={"plusOutline"} action="add" onClick={onNew} />
			)}
			{window.Auth.can_update_any(form) && (
				<DSLCardActionButton
					label="Edit"
					icon={"editOutline"}
					action="edit"
					disabled={disabled}
					onClick={onEdit}
				/>
			)}
			{whatToDo() === "delete" && window.Auth.can_update_any(form) && (
				<DSLCardActionButton
					label="Delete"
					icon={"archiveOutline"}
					action="archive"
					disabled={disabled}
					onClick={onDelete}
				/>
			)}
			{whatToDo() === "unlink" && window.Auth.can_update_any(form) && (
				<DSLCardActionButton
					label="Unlink"
					icon={"archiveOutline"}
					action="archive"
					disabled={disabled}
					onClick={onUnlink}
				/>
			)}
		</>
	);
};
