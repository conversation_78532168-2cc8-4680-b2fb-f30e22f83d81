import { FC, useMemo } from "react";
import { DSLFieldEmbedBtnsComponentProps } from "@dsl/fields/field-embed";
import { DSLCardActionButton } from "@blocks/dsl-card-view";
import { IFormData } from "@hooks/form-data";

export const CardFinderPayersQueryEmbedBtns: FC<DSLFieldEmbedBtnsComponentProps> = (props) => {
	const { tableRef, disabled, k, form, dd, getLinkMapFromPreset, v, hash, selected, refreshGridInternal } = props;

	const data = useMemo(() => {
		return (
			((dd?.field_nodes?.[k].data("helper")?.getAllData()?.data || []) as IFormData[]).find(
				(r) => r.id == selected
			) || ({} as any)
		);
	}, [selected, hash]);

	const onNew = () => {
		if (!data.query_id) {
			return;
		}
		if (data.query_form !== "ncpdp_response_cf_payer") {
			return;
		}
		if (disabled) {
			return;
		}
		const preset = {
			patient_id: data.patient_id,
			payer_id: data.payer_id,
			group_number: data.group_number,
			person_code: data.person_code,
			bin: data.bin,
			pcn: data.pcn,
			cardholder_id: data.cardholder_id,
			effective_date: data.effective_date,
			termination_date: data.termination_date,
			pharmacy_relationship_id: data.pharmacy_relationship_id,
		};
		if (!preset.patient_id) {
			console.log("CardFinderPayersQueryEmbedBtns: No patient id found");
			return;
		}
		if (!preset.payer_id) {
			console.log("CardFinderPayersQueryEmbedBtns: No payer id found");
			return;
		}
		console.log(preset);
		const linkMap = getLinkMapFromPreset(preset);

		const dfd = window.Flyout.open({
			form: "patient_insurance",
			...linkMap,
			autoRecoverEnabled: false,
			preset: preset,
		});
		dfd.done((data) => {
			refreshGridInternal();
		});
	};

	return (
		<>
			{window.Auth.can_create(form) && v.view.grid.add !== "none" && (
				<DSLCardActionButton
					label="Add Insurance"
					icon={"plusOutline"}
					disabled={disabled || data.query_form !== "ncpdp_response_cf_payer"}
					action="add"
					onClick={onNew}
				/>
			)}
		</>
	);
};
