import { DSLCardViewProps } from "@blocks/dsl-card-view";
import DS<PERSON>ardView from "@blocks/dsl-card-view/dsl-card-view";
import DSLDuelCard from "@blocks/dsl-card-view/dsl-duel-card";
import LoaderNoData from "@components/common/loader-no-data";
import NoDataFound from "@components/common/no-data-found";
import { fetchFormData, fetchFormFilters } from "@hooks/index";
import { useUpdateHash } from "@hooks/update-hash";
import { isTempId } from "@utils/dsl-fx";
import { useEffect, useRef, useState } from "react";

export const BillingNCPDPCard = (props: DSLCardViewProps) => {
	if (props.form !== "ncpdp") {
		return <DSLCardView {...props} />;
	}
	const [hash, refreshHash] = useUpdateHash();
	const [card, setCard] = useState(props.card);

	const [record, setRecord] = useState(() => {
		if (isTempId(props.record)) {
			return undefined;
		}
		return props.record;
	});

	const validationErrors = useRef<any>(props.validation_errors || {});
	const [state, setState] = useState<"loading" | "failed" | "done">("loading");
	const [preset, setPreset] = useState<any>({});
	const [salveForm, setSalveForm] = useState<any>("");

	const fetchChargeLines = async () => {
		setState("loading");
		if (!record) {
			setState("done");
			setPreset({});
		} else {
			fetchFormData("ncpdp", record)
				.then((data) => {
					if (!data.success) {
						setState("failed");
						return;
					}
					const ncpdpRec = data.data;
					const { claim_no, patient_id, insurance_id, site_id, payer_id, parent_id } = ncpdpRec;
					if (!claim_no) {
						setState("failed");
						setPreset({});
						return;
					}
					if (parent_id) {
						setSalveForm('view_ledger_charge_line');
						fetchFormFilters("billing_invoice", {
						filter: { id: parent_id }
						})
						.then((data) => {
							if (!data.success) {
								setState("failed");
								return;
							}
						const invoiceRec = data.data[0];
						const {
							status: invoice_status,
							delivery_ticket_id,
							invoice_no,
							parent_invoice_no,
							patient_id,
							insurance_id,
							billing_method_id,
							site_id,
							payer_id,
						} = invoiceRec;
						if (!invoice_no) {
							setState("failed");
							setPreset({});
							return;
						}
						setState("done");
						setPreset({
							patient_id: patient_id,
							invoice_no: invoice_no,
							parent_invoice_no: parent_invoice_no,
							invoice_status: invoice_status,
							insurance_id: insurance_id,
							billing_method_id: billing_method_id,
							site_id: site_id,
							payer_id: payer_id,
							delivery_ticket_id: delivery_ticket_id,
						});
						})
						.catch(() => { 
							setSalveForm('view_test_charge_line');
					setState("done");
						setPreset({
							patient_id: patient_id,
							claim_no: claim_no,
							insurance_id: insurance_id,
							site_id: site_id,
							payer_id: payer_id,
						});
						});
					} else {
					setSalveForm('view_test_charge_line');
					setState("done");
						setPreset({
							patient_id: patient_id,
							claim_no: claim_no,
							insurance_id: insurance_id,
							site_id: site_id,
							payer_id: payer_id,
						});
					}

					return;
				})
				.catch(() => {
					setState("failed");
				});
		}
	};

	useEffect(() => {
		fetchChargeLines();
	}, []);

	if (state === "loading") {
		return <LoaderNoData loading={true} />;
	}

	if (state === "failed") {
		return <NoDataFound text={"Unexpected System Error"} />;
	}

	return (
		<DSLDuelCard
			flip={false}
			key={hash}
			validationErrors={validationErrors.current}
			setValidationErrors={(errors) => {
				validationErrors.current = errors;
			}}
			master={{
				...props,
				card: card,
				disableRouting: false,
				changeMode: (mode, id, td, ref) => {
					if (record != id && id && !isTempId(id)) {
						setRecord(id);
					}
					if (card != mode) {
						setCard(mode);
						refreshHash();
						fetchChargeLines();
					}
					props?.changeMode?.(mode, id, td, ref);
				},
			}}
			slave={{
				form: salveForm,
				preset: preset,
				card: "add",
				forceReadOnly: card == "read" ? true : false,
				record: undefined,
				disableRouting: true,
			}}
		/>
	);
};