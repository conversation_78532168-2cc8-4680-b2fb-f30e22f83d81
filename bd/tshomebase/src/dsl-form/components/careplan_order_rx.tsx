import { DSLCardViewProps } from "@blocks/dsl-card-view";
import DS<PERSON>ardView from "@blocks/dsl-card-view/dsl-card-view";
import DSLDuelCard from "@blocks/dsl-card-view/dsl-duel-card";
import LoaderNoData from "@components/common/loader-no-data";
import NoDataFound from "@components/common/no-data-found";
import { CRResponse, request } from "@core/request";
import { fetchFormData, fetchFormFilters, IFormData } from "@hooks/index";
import { useUpdateHash } from "@hooks/update-hash";
import { isTempId } from "@utils/dsl-fx";
import _ from "lodash";
import { useEffect, useRef, useState } from "react";

export const CareplanOrderRxCard = (props: DSLCardViewProps) => {
	const [errorMessage, setErrorMessage] = useState<string>("");
	const [salveForm, setSalveForm] = useState<any>("view_careplan_order_item");
	if (props.form !== "careplan_order_rx") {
		return <DSLCardView {...props} />;
	}
	const [hash, refreshHash] = useUpdateHash();
	const [card, setCard] = useState(props.card);

	const [record, setRecord] = useState(() => {
		if (isTempId(props.record)) {
			return undefined;
		}
		return props.record;
	});

	const validationErrors = useRef<any>(props.validation_errors || {});

	const [state, setState] = useState<"loading" | "failed" | "done">("loading");

	const [preset, setPreset] = useState<any>({});

	const fetchOrderRecord = async () => {
		setState("loading");
		if (!record) {
			setState("failed");
			setErrorMessage(`Prescription Record Not Found: ${record}`);
			setPreset({});
			return;
		} else {
			fetchFormData("careplan_order_rx", record)
				.then((data) => {
					if (!data.success) {
						setState("failed");
						setErrorMessage(`Unable to fetch Prescription Record Id: ${record}`);
						return;
					}
					const rxRec = data.data;
					const { rx_no } = rxRec;
					if (!rx_no) {
						setState("failed");
						setErrorMessage(`RX No Not Found: ${rx_no}`);
						return;
					}
					console.log(`/api/query/vw_ord_rx_split_view?x1=${rx_no}`);
					request({
						method: "GET",
						url: `/api/query/vw_ord_rx_split_view?x1=${rx_no}`,
					}).then((res) => {
						const { data, success } = res as CRResponse;
						if (!success || !data) {
							setState("failed");
							setErrorMessage(`Unable to fetch Prescription Record Id: ${record}`);
							return;
						}
						let preset = undefined;
						let id = undefined;
						if (Array.isArray(data) && data.length) {
							id = data[0].id as number;
							preset = data[0];
						} else if (!Array.isArray(data) && typeof data === "object" && Object.keys(data).length) {
							id = data.id as number;
							preset = data;
						}
						if (!preset) {
							setState("failed");
							setErrorMessage(`Unable to fetch Prescription Record Id: ${record}`);
							return;
						}
						setPreset(preset);
						setState("done");
						setErrorMessage("");
					});
				})
				.catch(() => {
					setState("failed");
					setErrorMessage(`Unable to fetch Prescription Record Id: ${record}`);
				});
		}
	};

	useEffect(() => {
		fetchOrderRecord();
	}, []);

	if (state === "loading") {
		return <LoaderNoData loading={true} />;
	}

	if (state === "failed") {
		return <NoDataFound text={errorMessage || "Unexpected System Error"} />;
	}

	return (
		<DSLDuelCard
			flip={false}
			key={hash}
			validationErrors={validationErrors.current}
			setValidationErrors={(errors) => {
				validationErrors.current = errors;
			}}
			master={{
				...props,
				card: card,
				disableRouting: false,
				changeMode: (mode, id, td, ref) => {
					if (record != id && id && !isTempId(id)) {
						setRecord(id);
					}
					if (card != mode) {
						setCard(mode);
						refreshHash();
						fetchOrderRecord();
					}
					props?.changeMode?.(mode, id, td, ref);
				},
			}}
			slave={{
				form: salveForm,
				preset: preset,
				card: "add",
				forceReadOnly: card == "read" ? true : false,
				record: undefined,
				disableRouting: true,
			}}
		/>
	);
};
