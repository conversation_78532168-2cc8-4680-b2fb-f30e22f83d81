import { DSLCardViewProps } from "@blocks/dsl-card-view";
import DS<PERSON>ardView from "@blocks/dsl-card-view/dsl-card-view";
import DSLDuelCard from "@blocks/dsl-card-view/dsl-duel-card";
import LoaderNoData from "@components/common/loader-no-data";
import NoDataFound from "@components/common/no-data-found";
import { toast } from "@components/toast";
import { fetchFormData, IFormData } from "@hooks/index";
import { useUpdateHash } from "@hooks/update-hash";
import { isTempId } from "@utils/dsl-fx";
import _ from "lodash";
import { useEffect, useRef, useState } from "react";

export const CareplanDeliveryTickCard = (props: DSLCardViewProps) => {
	if (props.form !== "careplan_delivery_tick") {
		return <DSLCardView {...props} />;
	}
	const [errorMessage, setErrorMessage] = useState<string>("");
	const [hash, refreshHash] = useUpdateHash();
	const [card, setCard] = useState(props.card);
	const [record, setRecord] = useState(() => {
		if (isTempId(props.record)) {
			return undefined;
		}
		return props.record;
	});
	const validationErrors = useRef<any>(props.validation_errors || {});

	const [state, setState] = useState<"loading" | "failed" | "done">("loading");
	const [salveForm, setSalveForm] = useState<string>("");
	const [slavePreset, setSlavePreset] = useState<IFormData>({} as IFormData);
	const [salveRecord, setSalveRecord] = useState<string | number | undefined>(undefined);

	const setSlaveInfo = ({
		sForm,
		sRecord,
		sPreset,
		sStatus,
		sErrorMessage,
	}: {
		sForm: string;
		sRecord: string | number | undefined;
		sPreset: IFormData;
		sStatus: "loading" | "failed" | "done";
		sErrorMessage: string;
	}) => {
		setSalveForm(sForm);
		setSalveRecord(sRecord);
		setSlavePreset(sPreset);
		setState(sStatus);
		setErrorMessage(sErrorMessage);
	};

	const fetchSlaveFormInfo = async () => {
		setState("loading");
		if (!record) {
			setSlaveInfo({
				sForm: "",
				sRecord: undefined,
				sPreset: {} as IFormData,
				sStatus: "failed",
				sErrorMessage: `Careplan Delivery Ticket Record Not Found: ${record}`,
			});
		} else {
			fetchFormData("careplan_delivery_tick", record, true, "all")
				.then((res) => {
					if (!res.success) {
						setSlaveInfo({
							sForm: "",
							sRecord: undefined,
							sPreset: {} as IFormData,
							sStatus: "failed",
							sErrorMessage: `Unable to fetch Careplan Delivery Ticket Record: ${record}`,
						});
						return;
					}
					const { assessment_form = "", subform_assessment = [], status } = res.data;
					if (["delivery_ticket", "order_ver"].includes(status)) {
						if (assessment_form && subform_assessment && subform_assessment.length == 1) {
							setSlaveInfo({
								sForm: assessment_form,
								sRecord: subform_assessment[0].id,
								sPreset: subform_assessment[0],
								sStatus: "done",
								sErrorMessage: "",
							});
						} else {
							setSlaveInfo({
								sForm: "",
								sRecord: undefined,
								sPreset: {} as IFormData,
								sStatus: "done",
								sErrorMessage: "",
							});
							// toast({
							// 	type: "error",
							// 	message: `Assessment Form Not Found.`,
							// });
							console.error(
								`Careplan Delivery Ticket Assessment Form Not Found / Partial Data Found: ${record}`
							);
							console.error(res.data);
						}
					} else {
						setSlaveInfo({
							sForm: "",
							sRecord: undefined,
							sPreset: {} as IFormData,
							sStatus: "done",
							sErrorMessage: "",
						});
					}
				})
				.catch((err) => {
					setSlaveInfo({
						sForm: "",
						sRecord: undefined,
						sPreset: {} as IFormData,
						sStatus: "failed",
						sErrorMessage: `Unable to fetch CarePlan Delivery Ticket Record: ${record}`,
					});
				});
		}
	};

	useEffect(() => {
		fetchSlaveFormInfo();
	}, []);

	if (state === "loading") {
		return <LoaderNoData loading={true} />;
	}

	if (state === "failed") {
		return <NoDataFound text={errorMessage || "Unexpected System Error"} />;
	}

	if (!salveForm) {
		return <DSLCardView {...props} />;
	}

	return (
		<DSLDuelCard
			flip={false}
			key={hash}
			validationErrors={validationErrors.current}
			setValidationErrors={(errors) => {
				validationErrors.current = errors;
			}}
			master={{
				...props,
				card: card,
				disableRouting: false,
				changeMode: (mode, id, td, ref) => {
					if (record != id && id && !isTempId(id)) {
						setRecord(id);
					}
					if (card != mode) {
						setCard(mode);
						refreshHash();
						fetchSlaveFormInfo();
					}
					props?.changeMode?.(mode, id, td, ref);
				},
			}}
			slave={{
				form: salveForm,
				preset: slavePreset,
				card: "addfill",
				forceReadOnly: card == "read" ? true : false,
				record: salveRecord,
				disableRouting: true,
			}}
		/>
	);
};
