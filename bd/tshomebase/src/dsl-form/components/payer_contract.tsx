import { DSLCardView, DSLCardViewProps } from "@blocks/dsl-card-view";
import CardMaxRowOne from "@blocks/dsl-card-view/card-max-row-one";
import { GenericTabList } from "@blocks/generic-tab-list/generic-tab-list";
import PriceMatrixEditor from "@modules/billing/price-matrix-editor";
import "./payer_contract.less";
import _ from "lodash";

export const PayerContractCard = (props: DSLCardViewProps) => {
	if (props.form !== "payer_contract" || !props.record) {
		return <DSLCardView {...props} />;
	}
	const defaultSettingsArray = [
		{
			label: "Contract Information",
			id: "contract_information",
			path: "/billing/contract_information/",
			form: "payer_contract",
			renderComponent: CardMaxRowOne,
			renderComponentProps: {
				form: "payer_contract",
				maxRowFilter: {
					id: props.record,
				},
			},
		},
		{
			label: "Pricing Matrix",
			id: "pricing_matrix",
			path: "/billing/pricing_matrix/",
			form: "",
			renderComponent: PriceMatrixEditor,
			renderComponentProps: {
				form: "payer_contract",
				record: props.record,
				card: "edit",
			},
		},
	];

	return (
		<GenericTabList
			navigation={defaultSettingsArray}
			isActive={true}
			isParentActive={true}
			goTo={() => {}}
			tabHistory={{
				open: {
					patient: {},
					inventory: {},
				},
				close: {
					patient: {},
					inventory: {},
				},
			}}
		/>
	);
};
