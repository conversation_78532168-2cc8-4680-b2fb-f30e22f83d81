import React from "react";
import "./patient_medication.less";
import { DSLListViewProps } from "@blocks/dsl-list-view/dsl-list-view";
import { showToastError } from "@utils/fx";
import { request } from "@core/request";
import { SnapshotHeaderProps } from "@blocks/snapshot-header/snapshot-header";
interface QueueActionButton {
    label: string;
    icon?: string;
}
interface PrintLabelsQueueActionsProps {
    buttons?: QueueActionButton[];
}

export const PatientMedicationActions: React.FC<PrintLabelsQueueActionsProps & DSLListViewProps & SnapshotHeaderProps> = (props) => {
    const { buttons = [] } = props;
    const onButtonClick = async () => {
        const pid = props?.patientData?.id
        if (!pid) return;
        try {
            window.prettyNotify("Checking for allergy interactions...");
            request({
                url: `/interactions/?pid=${pid}`,
            }).then((interaction) => {
                const { da_interaction = [], dd_interaction = [] } = interaction.data;
                const presets = {
                    patient_id: pid,
                    has_da: da_interaction.length ? 'Yes' : 'No',
                    has_dd: dd_interaction.length ? 'Yes' : 'No',
                    dd_interaction: dd_interaction,
                    da_interaction: da_interaction,
                }
                window.Flyout.open({
                    form: "interactions",
                    mode: "add",
                    preset: presets,
                    autoRecoverEnabled: false,
                })
                window.prettyNotify("");
            }).catch((error) => {
                window.prettyNotify("");
                console.error("Error in Drug/Allergy interaction validator", error);
            })
        } catch (error) {
            window.prettyNotify()
            console.error('DrugAllergyInitInteraction: Interaction Failed', error)
            showToastError('Error initializing drug/allergy interactions');
        }
    }

    return (
        <div className="queue-actions">
            {buttons.map((button, index) => (
                <div key={index} className="queue-action-btns" onClick={() => onButtonClick()}>
                    {button.icon && <i className={button.icon}></i>}
                    <span className="btn-label">{button.label}</span>
                </div>
            ))}
        </div>
    );
};

