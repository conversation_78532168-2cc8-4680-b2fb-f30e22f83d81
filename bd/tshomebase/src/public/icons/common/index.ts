import SearchOutlinedIcon from "data-url:./outlined/search-outline-icon.svg";
import CancelOutLinedIcon from "data-url:./outlined/cancel-outline-icon.svg";
import CircleOutlineIcon from "data-url:./outlined/circle-icon.svg";
import ArrowDownOutlineIcon from "data-url:./outlined/arrow-down.svg";
import ArrowUpOutline from "data-url:./outlined/arrow-up-outline.svg";
import CrossOutline from "data-url:./outlined/cross-outline.svg";
import PlusOutlined from "data-url:./outlined/plus.svg";
import ThreeVerticalDotsOutline from "data-url:./outlined/three-vertical-dots-outline.svg";
import EditOutline from "data-url:./outlined/edit-outline.svg";
import NavigatorOutline from "data-url:./outlined/navigator-outline.svg";
import SnapshotOutline from "data-url:./outlined/snapshot-outline.svg";
import ClaimOutline from "data-url:./outlined/claim.png";
import FDBOutline from "data-url:./outlined/fdb.png";
import DeleteOutline from "data-url:./outlined/delete-outline.svg";
import DecorativeCircles from "data-url:./decorative-circles.svg";
import ExclIconRed from "data-url:./excl-icon-red.svg";

import CrossIcon from "data-url:./outlined/cross-icon.svg";
import FilledDone from "data-url:./icon-filled-done.svg";
import Checkbox from "data-url:./checkbox.svg";

import SortingArrowUpFilled from "data-url:./filled/sorting-arrow-up.svg";
import SortingArrowDownFilled from "data-url:./filled/sorting-arrow-down.svg";
import HandPointUpFilled from "data-url:./filled/hand-point-up.svg";
import MenuBulletAsteriskFilled from "data-url:./filled/menu-icon.svg";
import MenuBulletAsteriskSingleFilled from "data-url:./filled/menu-single-icon.svg";
import CMS1500Filled from "data-url:./filled/cms-1500.png";

import CrossActive from "data-url:./outlined/cross-icon-active.svg";

import SortingArrowDownFilledActive from "data-url:./filled/active/sorting-arrow-down-active.svg";
import SortingArrowUpFilledActive from "data-url:./filled/active/sorting-arrow-up-active.svg";
import MenuBulletAsteriskFilledActive from "data-url:./filled/active/menu-icon-active.svg";
import MenuBulletAsteriskSingleFilledActive from "data-url:./filled/active/menu-single-icon-active.svg";

import ProfileOutlineActive from "data-url:./outlined/active/profile-active.svg";
import SwapOutlineActive from "data-url:./outlined/active/swap-active.svg";
import CallOutlineActive from "data-url:./outlined/active/call-outline-active.svg";
import LocationOutlineActive from "data-url:./outlined/active/location-outline-active.svg";
import PhoneOutlineActive from "data-url:./outlined/active/phone-outline-active.svg";
import TabletsOutlineActive from "data-url:./outlined/active/tablets-outline-active.svg";
import MedicalOutlineActive from "data-url:./outlined/active/medical-outline-active.svg";
import PharmacyOutlineActive from "data-url:./outlined/active/pharmacy-outline-active.svg";
import InsuranceOutlineActive from "data-url:./outlined/active/insurance-outline-active.svg";

import List from "data-url:./outlined/list.svg";
import { billingIcons } from "../billing";

export default {
	searchOutlinedIcon: SearchOutlinedIcon,
	cancelOutlinedIcon: CancelOutLinedIcon,
	circleOutlineIcon: CircleOutlineIcon,
	arrowDownOutlineIcon: ArrowDownOutlineIcon,
	arrowUpOutline: ArrowUpOutline,
	crossIcon: CrossIcon,
	filledDoneIcon: FilledDone,
	checkboxIcon: Checkbox,
	crossActive: CrossActive,
	plusOutlined: PlusOutlined,
	sortingArrowUpFilled: SortingArrowUpFilled,
	sortingArrowDownFilled: SortingArrowDownFilled,
	sortingArrowDownFilledActive: SortingArrowDownFilledActive,
	sortingArrowUpFilledActive: SortingArrowUpFilledActive,
	handPointUpFilled: HandPointUpFilled,
	crossOutline: CrossOutline,
	swapOutlineActive: SwapOutlineActive,
	profileOutlineActive: ProfileOutlineActive,
	threeVerticalDotsOutline: ThreeVerticalDotsOutline,
	callOutlineActive: CallOutlineActive,
	locationOutlineActive: LocationOutlineActive,
	phoneOutlineActive: PhoneOutlineActive,
	tabletsOutlineActive: TabletsOutlineActive,
	medicalOutlineActive: MedicalOutlineActive,
	pharmacyOutlineActive: PharmacyOutlineActive,
	insuranceOutlineActive: InsuranceOutlineActive,
	editOutline: EditOutline,
	list: List,
	navigatorOutline: NavigatorOutline,
	snapshotOutline: SnapshotOutline,
	menuBulletAsteriskFilled: MenuBulletAsteriskFilled,
	menuBulletAsteriskFilledActive: MenuBulletAsteriskFilledActive,
	menuBulletAsteriskSingleFilled: MenuBulletAsteriskSingleFilled,
	menuBulletAsteriskSingleFilledActive: MenuBulletAsteriskSingleFilledActive,
	claimOutline: ClaimOutline,
	FDBOutline: FDBOutline,
	CMS1500Filled: CMS1500Filled,
	deleteOutline: DeleteOutline,
	decorativeCircles: DecorativeCircles,
	exclIconRed: ExclIconRed,
	billingIcons: billingIcons,
};
