import navclose from "data-url:./collapse-left.svg";
import navopen from "data-url:./collapse-right.svg";
import navCross from "data-url:./cross-icon.svg";
import dashboard from "data-url:./dashboard.svg";
import erx from "data-url:./erx.svg";
import hamburgerIcon from "data-url:./hamburger-icon.svg";
import logout from "data-url:./logout.svg";
import referral from "data-url:./referral.svg";
import sales from "data-url:./sales.svg";
import settings from "data-url:./settings.svg";

import analyticsActive from "data-url:./active/analytics-active.svg";
import billingActive from "data-url:./active/billing-active.svg";
import complianceActive from "data-url:./active/compliance-active.svg";
import dispenseActive from "data-url:./active/dispense-active.svg";
import erxActive from "data-url:./active/erx-active.svg";
import inventoryActive from "data-url:./active/inventory-active.svg";
import patientActive from "data-url:./active/patient-active.svg";
import queueActive from "data-url:./active/queue-active.svg";
import referralActive from "data-url:./active/referral-active.svg";
import salesActive from "data-url:./active/sales-active.svg";
import scheduleActive from "data-url:./active/schedule-active.svg";
import settingsActive from "data-url:./active/settings-active.svg";
import compliance from "data-url:./compliance.svg";
import dispense from "data-url:./dispense.svg";

import NavAnalytics from "data-url:./nav-analytics.svg";
import NavBilling from "data-url:./nav-billing.svg";
import NavInventory from "data-url:./nav-inventory.svg";
import NavPatient from "data-url:./nav-patient.svg";
import NavQueue from "data-url:./nav-queue.svg";
import NavSchedule from "data-url:./nav-schedule.svg";
import NavWorkflow from "data-url:./workflow.svg";
import tabArtBoard from "data-url:./tabs/tab-artBoard.svg";
import tabBandaid from "data-url:./tabs/tab-bandaid.svg";
import tabInvoice from "data-url:./tabs/tab-invoice.svg";
import tabPharmacy from "data-url:./tabs/tab-pharmacy.svg";
import tabPill from "data-url:./tabs/tab-pill.svg";

import tabAccept from "data-url:./tabs/tab-accept.svg";
import tabAdjustment from "data-url:./tabs/tab-adjustment.svg";
import tabCart from "data-url:./tabs/tab-cart.svg";
import tabReceive from "data-url:./tabs/tab-receive.svg";
import tabTransfer from "data-url:./tabs/tab-transfer.svg";

export default {
	inventoryActive,
	billingActive,
	dispense,
	dispenseActive,
	dashboard,
	inventory: NavInventory,
	patient: NavPatient,
	schedule: NavSchedule,
	workflow: NavWorkflow,
	sales,
	settings,
	logout,
	navopen,
	navclose,
	navCross,
	hamburgerIcon,
	queue: NavQueue,
	referral,
	analytics: NavAnalytics,
	queueActive,
	salesActive,
	patientActive,
	scheduleActive,
	analyticsActive,
	referralActive,
	settingsActive,
	billing: NavBilling,
	compliance,
	complianceActive,
	erx,
	erxActive,
	tabPill,
	tabInvoice,
	tabArtBoard,
	tabBandaid,
	tabPharmacy,
	tabAccept,
	tabAdjustment,
	tabCart,
	tabReceive,
	tabTransfer,
};
