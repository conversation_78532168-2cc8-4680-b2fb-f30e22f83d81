
import person from "data-url:./patient.svg";
import arrowsCollapseLeft from "data-url:./collapse-left.svg";
import arrowsCollapseRight from "data-url:./collapse-right.svg";
import cross from "data-url:./cross-icon.svg";
import eprescribe from "data-url:./erx.svg";
import alert from "data-url:./alert.svg";
import archieve from "data-url:./archive.svg";
import arrowDown from "data-url:./arrow-down.svg";
import arrowUp from "data-url:./arrow-up.svg";
import cancel from "data-url:./cancel.svg";
import comment from "data-url:./comment.svg";
import trash from "data-url:./delete.svg";
import manage from "data-url:./manage.svg";
import move from "data-url:./move.svg";
import mySales from "data-url:./mysales.svg";
import open from "data-url:./open.svg";
import plusBig from "data-url:./plus-big.svg";
import print from "data-url:./print.svg";
import refresh from "data-url:./refresh.svg";
import readonly from "data-url:./readonly.svg";
import save from "data-url:./save.svg";
import time from "data-url:./time.svg";
import searchBig from "data-url:./search-big.svg";
import Diagnosis from "data-url:./diagnosis.svg";
import Insurance from "data-url:./insurance.svg";
import Demographics from "data-url:./demographics.svg";
import EmergencyCall from "data-url:./emergency_call.svg";
import Medication from "data-url:./medication.svg";
import Prescriber from "data-url:./prescriber.svg";


import personActive from "data-url:./active/patient-active.svg";
import calenderActive from "data-url:./active/calendar-active.svg";
import eprescribeActive from "data-url:./active/erx-active.svg";
import arrowsCollapseLeftActive from "data-url:./active/arrows-collapse-left-active.svg";
import arrowsCollapseRightActive from "data-url:./active/arrows-collapse-right-active.svg";
import callActive from "data-url:./active/call-active.svg";
import cashActive from "data-url:./active/cash-active.svg";
import detailsActive from "data-url:./active/details-active.svg";
import emailActive from "data-url:./active/email-active.svg";
import locationActive from "data-url:./active/email-active.svg";
import medicalActive from "data-url:./active/medical-active.svg";
import moneyActive from "data-url:./active/money-active.svg";
import nurseActive from "data-url:./active/nurse-active.svg";
import pharmacyActive from "data-url:./active/pharmacy-active.svg";
import phoneActive from "data-url:./active/phone-active.svg";
import premiumActive from "data-url:./active/premium-active.svg";
import profileActive from "data-url:./active/profile-active.svg";
import receiptActive from "data-url:./active/premium-active.svg";
import shieldActive from "data-url:./active/shield-active.svg";
import swapActive from "data-url:./active/swap-active.svg";
import tableActive from "data-url:./active/table-active.svg";
import tabletsActive from "data-url:./active/tablets-active.svg";

export default {
	alert,
	archieve,
	arrowDown,
	arrowUp,
	cancel,
	comment,
	manage,
	move,
	mySales,
	open,
	plusBig,
	print,
	refresh,
	readonly,
	save,
	arrowsCollapseRight,
	arrowsCollapseLeft,
	arrowsCollapseLeftActive,
	arrowsCollapseRightActive,
	cross,
	personActive,
	calenderActive,
	person,
	eprescribe,
	eprescribeActive,
	callActive,
	cashActive,
	detailsActive,
	emailActive,
	locationActive,
	medicalActive,
	moneyActive,
	nurseActive,
	pharmacyActive,
	phoneActive,
	premiumActive,
	profileActive,
	receiptActive,
	shieldActive,
	swapActive,
	tableActive,
	tabletsActive,
	time,
	searchBig,
	trash,
	diagnosis: Diagnosis,
	insurance: Insurance,
	emergencyCall: EmergencyCall,
	demographics: Demographics,
	medication: Medication,
	prescriber: Prescriber

};
