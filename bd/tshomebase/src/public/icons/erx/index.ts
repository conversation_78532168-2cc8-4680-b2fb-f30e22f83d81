import approved2x from "./approved-2x.png";
import approved from "./approved.png";
import denied2x from "./denied-2x.png";
import denied from "./denied.png";
import epcsnotsigned2x from "./epcs-not-signed-2x.png";
import epcsnotsigned from "./epcs-not-signed.png";
import epcssigned2x from "./epcs-signed-2x.png";
import epcssigned from "./epcs-signed.png";
import error2x from "./error-2x.png";
import error from "./error.png";
import info2x from "./info-2x.png";
import info from "./info.png";
import newMsg2x from "./new-2x.png";
import newMsg from "./new.png";
import pending2x from "./pending-2x.png";
import pending from "./pending.png";
import priority2x from "./priority-2x.png";
import priority from "./priority.png";
import replied2x from "./replied-2x.png";
import replied from "./replied.png";
import sent2x from "./sent-2x.png";
import sent from "./sent.png";
import xml2x from "./xml-2x.png";
import xml from "./xml.png";

export default {
    approved2x,
    approved,
    denied2x,
    denied,
    epcsnotsigned2x,
    epcsnotsigned,
    epcssigned2x,
    epcssigned,
    error2x,
    error,
    info2x,
    info,
    newMsg2x,
    newMsg,
    pending2x,
    pending,
    priority2x,
    priority,
    replied2x,
    replied,
    sent2x,
    sent,
    xml2x,
    xml
}