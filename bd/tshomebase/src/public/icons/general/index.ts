import arrowLeftCirle from "data-url:./arrow-left-circle.svg";
import arrowRightCircle from "data-url:./arrow-right-circle.svg";
import iconEdit from "data-url:./icon-edit.svg";
import iconCrossExit from "data-url:./icon-cancel.svg";
import iconRequired from "data-url:./required.svg";
import minimizeWindow from "data-url:./minimize-window.svg";
import closeWindow from "data-url:./close-window.svg";
import oneTime from "data-url:./one-time.svg";
import noCurrent from "data-url:./no-current.svg";
import shieldBlocked from "data-url:./shield-blocked.svg";
import box from "data-url:./box.svg";
import separator from "data-url:./separator.svg";
import tag from "data-url:./tag.svg";
import doubleWindow from "data-url:./double-window.svg";

export default {
	arrowLeftCirle,
	arrowRightCircle,
	iconEdit,
	iconRequired,
	iconCrossExit,
	minimizeWindow,
	closeWindow,
	oneTime,
	noCurrent,
	shieldBlocked,
	box,
	separator,
	tag,
	doubleWindow,
};
