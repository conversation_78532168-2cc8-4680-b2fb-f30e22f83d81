import userplus from "data-url:./user-plus.svg";
import usershield from "data-url:./user-shield.svg";
import heathshield from "data-url:./health-shield.svg";
import addsnap from "data-url:./add-snap.svg";
import caution from "data-url:./caution.svg";
import editlg from "data-url:./edit-lg.svg";
import editsm from "data-url:./edit-sm.svg";
import edit from "data-url:./edit.svg";
import move from "data-url:./move.svg";
import timeline from "data-url:./timeline.svg";
import wgtind from "data-url:./wgt-ind.svg";
import open from "data-url:./open.svg";
import addcircle from "data-url:./add-circle.svg";
import historylist from "data-url:./order-history.svg";
import profile from "data-url:./profile.svg";
import close from "data-url:./close.svg";
import addform from "data-url:./add-form.svg";
import personIcon from "data-url:./person.svg";
import prescriberPrimary from "data-url:./prescriber-primary.svg";
import fax from "data-url:./fax.svg";
import blank from "data-url:./blank.svg";
import hash from "data-url:./hash.svg";

// Patient Snap Top Bar

import allFiles from "data-url:./all-files.svg";
import billingStatus from "data-url:./billing-status.svg";
import deliveryTickets from "data-url:./delivery-tickets.svg";
import historicalOrders from "data-url:./historical-orders.svg";
import patientNotes from "data-url:./patient-notes.svg";
import schedule from "data-url:./schedule.svg";

import errorAlertIcon from "data-url:./error-alert-icon.svg";
import newExp from "data-url:./new-exp.svg";
import originalRx from "data-url:./original-rx.svg";
import tickOk from "data-url:./tick-ok.svg";
import settingsButton from "data-url:./setting-button.svg";

import patSnap from "data-url:./pat-snapshot.svg";
import patAccount from "data-url:./pat-account-dollar.svg"
import patAlert from "data-url:./pat-alert.svg"
import patShield from "data-url:./pat-shield.svg"
import pill from "data-url:./pill-bolder.svg"

import patMed from "data-url:./pat-med.svg"
import copy from "data-url:./copy.svg"
import patRx from "data-url:./pat-rx.svg"
import patPlus from "data-url:./pat-plus.svg"

export default {
	userplus,
	usershield,
	heathshield,
	addsnap,
	caution,
	editlg,
	editsm,
	edit,
	move,
	timeline,
	wgtind,
	open,
	addcircle,
	historylist,
	profile,
	close,
	addform,
	personIcon,
	prescriberPrimary,
	fax,
	blank,
	hash,
	copy,
	patRx,
	patPlus,
	// Patient Snap Top Bar
	allFiles,
	billingStatus,
	deliveryTickets,
	historicalOrders,
	patientNotes,
	schedule,

	//Patient Snap side bar
	patSnap,
	patAccount,
	patAlert,
	patShield,
	patMed,
	pill,

	errorAlertIcon,
	newExp,
	originalRx,
	tickOk,
	settingsButton,
};
