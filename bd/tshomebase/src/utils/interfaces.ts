import { CSSProperties } from 'react';

export type FunctionType = (...params: unknown[]) => void;

export type AnyObjectType = { [key: string | number | symbol]: any }

export type StyleModulePropType = { [key: string]: string } | string;

export interface UIComponentProps {
    className?: string;
    style?: CSSProperties;
    children?: React.ReactNode;
    onClick?: (event: React.MouseEvent<HTMLElement>) => void;
    onFocus?: (event: React.FocusEvent<HTMLElement>) => void;
    onBlur?: (event: React.FocusEvent<HTMLElement>) => void;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    id?: string;
    role?: string;
    variant?: string;
    size?: string;
    title?: string;
}
