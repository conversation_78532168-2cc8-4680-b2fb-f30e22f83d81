import { decode } from 'gs1-parser'; // Use the decode function

/**
 * Class for parsing GS1 barcodes for drug products and determining category
 */
interface ParsedGS1Result {
    gtin: string | null;
    expirationDate: string | null; // AI 17 (YYMMDD format from library, or formatted)
    lotNumber: string | null;
    serialNumber: string | null;
    rawData: string;
    processedData: string;
    category?: GS1ProductCategory;
    elements: { [key: string]: string }; // Store all found elements AI: value
    errors?: string[];
}

/**
 * Class for parsing lot/serial numbers from GS1 barcodes for drug products
 */
interface LotSerialParseResult {
    lotNumber: string | null;
    serialNumber: string | null;
    confidence: number;
    matchType: {
        lot: string;
        serial: string;
    } | null;
}

export enum GS1ProductCategory {
    PHARMACEUTICAL = 'PHARMACEUTICAL',
    MEDICAL_DEVICE = 'MEDICAL_DEVICE',
    MEDICAL_SUPPLY = 'MEDICAL_SUPPLY',
    UNKNOWN = 'UNKNOWN'
}

export class GS1MedicalParser {
    private readonly lotPatterns: Record<string, RegExp>;
    private readonly serialPatterns: Record<string, RegExp>;
    
    // Source: https://www.gs1.org/standards/id-keys/company-prefix
    private readonly productPrefixes = {
        pharmaceutical: [
            // National Drug Codes (NDC) converted to GTIN
            { start: '003', end: '003' },  // Pharmaceuticals
            { start: '300', end: '379' },  // Drugs
        ],
        medicalDevice: [
            { start: '080', end: '090' },  // Medical devices
            { start: '381', end: '382' },  // Medical equipment
        ],
        medicalSupply: [
            { start: '603', end: '607' },  // Medical/Surgical supplies
            { start: '901', end: '912' },  // Healthcare items
        ]
    };

    constructor() {
        // GS1 Lot Number Standards
        this.lotPatterns = {
            yearMonthSequence: /^[0-9]{2}[0-1][0-9][0-9]{2,3}$/,
            letterYearSequence: /^[A-Z][0-9]{2}[0-9]{3,4}$/,
            yearDayOfYear: /^[0-9]{2}[0-3][0-9]{2}$/,
            plantYearMonth: /^[A-Z]{1,2}[0-9]{2}[0-1][0-9][0-9]{2,3}$/,
            pharmaAlphaNumeric: /^[A-Z][A-Z0-9]{5,8}$/,
            pharmaDateBatch: /^[A-Z]{1,2}[0-9]{4,6}$/
        };

        // GS1 Serial Number Standards
        this.serialPatterns = {
            numericSequence: /^[0-9]{6,10}$/,
            yearSequence: /^[0-9]{2}[0-9]{4,8}$/,
            alphanumericCheck: /^[A-Z0-9]{8,12}[A-Z]$/,
            pharmaComplex: /^[0-9]{5}[A-Z]{3}[0-9][A-Z][0-9][A-Z][0-9]$/,
            generalAlphanumeric: /^[A-Z0-9]{8,20}$/
        };
    }

    /**
     * Parses a full GS1-128 barcode string using the decode function from gs1-parser.
     * Handles potential ESC character and '029' as a separator if standard GS not present.
     */
    public parseGS1Barcode(rawBarcode: string): ParsedGS1Result {
        const result: ParsedGS1Result = {
            gtin: null,
            expirationDate: null,
            lotNumber: null,
            serialNumber: null,
            rawData: rawBarcode,
            processedData: '',
            elements: {},
            errors: []
        };

        if (!rawBarcode || typeof rawBarcode !== 'string') {
            result.errors?.push('Invalid input: Barcode must be a non-empty string.');
            return result;
        }

        const cleanedInput = rawBarcode.trim().replace(/\x1b/g, ''); // Remove ESC
        let processed = cleanedInput.replace(/\x1d/g, '\u001d'); 

        // 2. TARGETED replacement for '029' separator if standard GS wasn't found.
        const gsSeparator = '\u001d';
        if (!processed.includes(gsSeparator)) { 
            const firstSeparatorIndex = processed.indexOf('029', 16); // Search after GTIN (AI 01 data length = 14)
            if (firstSeparatorIndex > 16 && processed.length > firstSeparatorIndex + 3) { 
                 processed = processed.substring(0, firstSeparatorIndex) + 
                                     gsSeparator + 
                                     processed.substring(firstSeparatorIndex + 3);
            }
        }
        result.processedData = processed; 

        try {
            // Call the imported decode function
            const decodedData = decode(processed);
            result.elements = { ...decodedData }; // Copy all elements found

            if (decodedData.GTIN) {
                result.gtin = decodedData.GTIN;
                if (result.gtin) {
                    result.category = this.determineProductCategory(result.gtin);
                }
            }
            if (decodedData.exp) {
                if (decodedData.exp instanceof Date) {
                    const year = decodedData.exp.getFullYear();
                    const month = (decodedData.exp.getMonth() + 1).toString().padStart(2, '0');
                    const day = decodedData.exp.getDate().toString().padStart(2, '0');
                    result.expirationDate = `${year}-${month}-${day}`;
                } else if (typeof decodedData.exp === 'string') {

                    if (decodedData.exp.includes('T')) { 
                         result.expirationDate = decodedData.exp.substring(0, 10); // Extract YYYY-MM-DD
                    } else {
                         result.expirationDate = decodedData.exp; 
                    }
                }
            }
            if (decodedData.lot) {
                result.lotNumber = decodedData.lot;
            }
            if (decodedData.serial) {
                result.serialNumber = decodedData.serial;
            }

        } catch (error: any) {
            console.error("Error parsing GS1 barcode in GS1MedicalParser:", error);
            result.errors?.push(`Parsing failed: ${error.message || error}`);
        }
 
        if (result.errors && result.errors.length > 0) {
            console.error("GS1MedicalParser Errors:", result.errors);
        }

        return result;
    }

    /**
     * Determine product category from GTIN
     */
    public determineProductCategory(gtin: string): GS1ProductCategory {
        // Ensure GTIN is 14 digits
        const paddedGtin = gtin.padStart(14, '0');
        
        // Extract the first three digits for prefix checking
        const prefix = paddedGtin.substring(0, 3);
        
        
        // Check pharmaceutical ranges
        if (this.isInRange(prefix, this.productPrefixes.pharmaceutical)) {
            return GS1ProductCategory.PHARMACEUTICAL;
        }
        
        // Check medical device ranges
        if (this.isInRange(prefix, this.productPrefixes.medicalDevice)) {
            return GS1ProductCategory.MEDICAL_DEVICE;
        }
        
        // Check medical supply ranges
        if (this.isInRange(prefix, this.productPrefixes.medicalSupply)) {
            return GS1ProductCategory.MEDICAL_SUPPLY;
        }
        
        return GS1ProductCategory.UNKNOWN;
    }

    private getPossibleSplits(data: string): Array<{firstPart: string, secondPart: string}> {
        const splits: Array<{firstPart: string, secondPart: string}> = [];
        
        // Try different split positions based on common lengths
        for (let i = 4; i <= Math.min(12, data.length - 4); i++) {
            splits.push({
                firstPart: data.slice(0, i),
                secondPart: data.slice(i)
            });
        }

        // Add splits at pattern transitions
        const transitionPoints = this.findPatternTransitions(data);
        for (const point of transitionPoints) {
            splits.push({
                firstPart: data.slice(0, point),
                secondPart: data.slice(point)
            });
        }

        return splits;
    }

    private findPatternTransitions(data: string): number[] {
        const transitions: number[] = [];

        for (let i = 1; i < data.length; i++) {
            const prev = data[i-1];
            const curr = data[i];
            
            if ((/[A-Z]/.test(prev) && /[0-9]/.test(curr)) ||
                (/[0-9]/.test(prev) && /[A-Z]/.test(curr))) {
                transitions.push(i);
            }
        }

        return transitions;
    }

    private isInRange(prefix: string, ranges: Array<{start: string, end: string}>): boolean {
        return ranges.some(range => 
            prefix >= range.start && prefix <= range.end
        );
    }
}