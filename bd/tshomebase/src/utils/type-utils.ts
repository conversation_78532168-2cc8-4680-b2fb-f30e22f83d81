export type RecursivePartial<T> = {
	[P in keyof T]?: RecursivePartial<T[P]>;
};

export type PartialExcept<T, K extends keyof T> = RecursivePartial<T> & Pick<T, K>;

export type Simplify<T> = { [KeyType in keyof T]: T[KeyType] } & {};

export type IsEqual<A, B> = (<G>() => G extends A ? 1 : 2) extends <G>() => G extends B ? 1 : 2 ? true : false;

type Filter<KeyType, ExcludeType> = IsEqual<KeyType, ExcludeType> extends true
	? never
	: KeyType extends ExcludeType
	? never
	: KeyType;

type ExceptOptions = {
	requireExactProps?: boolean;
};

export type Except<
	ObjectType,
	KeysType extends keyof ObjectType,
	Options extends ExceptOptions = { requireExactProps: false }
> = {
	[KeyType in keyof ObjectType as Filter<KeyType, KeysType>]: ObjectType[KeyType];
} & (Options["requireExactProps"] extends true ? Partial<Record<KeysType, never>> : {});

export type PartialBy<BaseType, <PERSON> extends keyof BaseType> = Simplify<
	Except<BaseType, Keys> & Partial<Pick<BaseType, Keys>>
>;
