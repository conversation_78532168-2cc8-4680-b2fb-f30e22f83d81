import { StyleModulePropType, FunctionType } from "@utils/interfaces";
import { POSITION_BOTTOM, POSITION_LEFT, POSITION_RIGHT, POSITION_TOP, SIZE_LG, SIZE_MD, SIZE_SM, SIZE_XL, SIZE_XS, SIZE_XXL, VARIANT_DARK, VARIANT_DEFAULT, VARIANT_LIGHT, VARIANT_PRIMARY, VARIANT_SECONDARY, VARIANT_TERTIARY } from "./const";

export const getSize = (size?: string, styleFile: StyleModulePropType = "", additionalCases: FunctionType = () => { }) => {
	const isStyleModule = !(typeof styleFile === 'string');
	let sizeClass: string | null | undefined | void = additionalCases()

	switch (size) {
		case SIZE_XXL:
			sizeClass = isStyleModule ? styleFile?.[SIZE_XXL] : SIZE_XXL;
			break;
		case SIZE_XL:
			sizeClass = isStyleModule ? styleFile?.[SIZE_XL] : SIZE_XL;
			break;
		case SIZE_LG:
			sizeClass = isStyleModule ? styleFile?.[SIZE_LG] : SIZE_LG;
			break;
		case SIZE_MD:
			sizeClass = isStyleModule ? styleFile?.[SIZE_MD] : SIZE_MD;
			break;
		case SIZE_SM:
			sizeClass = isStyleModule ? styleFile?.[SIZE_SM] : SIZE_SM;
			break;
		case SIZE_XS:
			sizeClass = isStyleModule ? styleFile?.[SIZE_XS] : SIZE_XS;
			break;
		default:
			sizeClass = ""
	}

	return sizeClass
};

export const getVariant = (variant?: string, styleFile: StyleModulePropType = "", additionalCases: FunctionType = () => "") => {
	const isStyleModule = !(typeof styleFile === 'string');
	var variantClass: string | null | undefined | void = additionalCases()

	switch (variant) {
		case VARIANT_PRIMARY:
			variantClass = isStyleModule ? styleFile?.[VARIANT_PRIMARY] : VARIANT_PRIMARY;
			break;
		case VARIANT_SECONDARY:
			variantClass = isStyleModule ? styleFile?.[VARIANT_SECONDARY] : VARIANT_SECONDARY;
			break
		case VARIANT_TERTIARY:
			variantClass = isStyleModule ? styleFile?.[VARIANT_TERTIARY] : VARIANT_TERTIARY;
			break;
		case VARIANT_DARK:
			variantClass = isStyleModule ? styleFile?.[VARIANT_DARK] : VARIANT_DARK;
			break;
		case VARIANT_LIGHT:
			variantClass = isStyleModule ? styleFile?.[VARIANT_LIGHT] : VARIANT_LIGHT;
			break;
		case VARIANT_DEFAULT:
			variantClass = isStyleModule ? styleFile?.[VARIANT_DEFAULT] : VARIANT_DEFAULT;
			break
		default:
			variantClass = ""
	}

	return variantClass
};

export const getPosition = (position?: string, styleFile: StyleModulePropType = "", additionalCases: FunctionType = () => { }) => {
	const isStyleModule = !(typeof styleFile === 'string');
	let positionClass: string | null | undefined | void = additionalCases()

	switch (position) {
		case POSITION_TOP:
			positionClass = isStyleModule ? styleFile?.[POSITION_TOP] : POSITION_TOP;
			break;
		case POSITION_RIGHT:
			positionClass = isStyleModule ? styleFile?.[POSITION_RIGHT] : POSITION_RIGHT;
			break;
		case POSITION_BOTTOM:
			positionClass = isStyleModule ? styleFile?.[POSITION_BOTTOM] : POSITION_BOTTOM;
			break;
		case POSITION_LEFT:
			positionClass = isStyleModule ? styleFile?.[POSITION_LEFT] : POSITION_LEFT;
			break;
		default:
			positionClass = POSITION_BOTTOM
	}

	return positionClass
};

export const getTooltipStyles = (variant: string, bgColor?: string) => {
	switch (variant) {
		case "primary":
			return {
				tooltip: {
					sx: {
						borderRadius: "var(--radius-medium)",
						padding: "var(--spacing-xlarge)",
						fontSize: "var(--font-size-xxsmall)",
						fontWeight: "var(--font-weight-medium)",
						lineHeight: "var(--line-height-standard)",
						bgcolor: bgColor || "var(--color-tertiary)",
						color: "var(--white)",
					},
				},
				arrow: {
					sx: { color: bgColor || "var(--color-tertiary)" },
				},
			};
		default:
			return {
				tooltip: {
					sx: {
						height: "var(--line-height-xxxlarge)",
						fontSize: "var(--font-size-xsmall)",
						fontWeight: "var(--font-weight-bold)",
						padding: "4px 8px",
						borderRadius: "var(--radius-xxxxxxlarge)",
						lineHeight: "var(--line-height-medium)",
						bgcolor: bgColor || "var(--color-tertiary)",
						color: "var(--white)",
					},
				},
				arrow: {
					sx: {
						color: bgColor || "var(--color-tertiary)",
					},
				},
			};
	}
};