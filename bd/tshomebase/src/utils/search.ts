/**
 * Search Utils
 */
import { HistoryModule, TabHistory } from "@contexts/history/provider";
import { request } from "@core/request";

export const sendRequest = async (url: string) => {
    const resp = await request({ url });
    const result = resp?.data?.result || {};
    return result;
};
export const processSearchString = (
    searchString: string,
    urlMap: Record<string, string>,
): { characterIndex: number; words: string, slug: string } => {
    let characterIndex = 0;
    let slug = '';
    const buttonCharArray = Object.keys(urlMap)
    const words = searchString
        .split(" ")
        .filter((word) => {
            const key = word.toLowerCase();
            if (word.length <= 1 && buttonCharArray.includes(key)) {
                slug = urlMap[key];
                characterIndex = buttonCharArray.indexOf(key);
                return false;
            }
            return true;
        })
        .join(" ")
        .trim();
    return { characterIndex, words, slug };
};

export const getUrl = (
    id: number | string,
    data: { [key: string]: string },
    uriSegment: string | null = null,
    filterKey: string,
    patientUri: string
) => {
    if (filterKey === "Patient") {
        return `/patient/${id}/${uriSegment || patientUri}`;
    } else if (filterKey === "Inventory") {
        const { type } = data;
        let inventoryType = type.toLowerCase();
        if (type === "Supply") {
            inventoryType = "supplies";
        } else if (type === "Equipment Rental") {
            inventoryType = "rentals";
        }
        return `/inventory/stock/${inventoryType}/${id}/snap`;
    } else if (filterKey === "Sales Account") {
        const { type } = data;
        let salesType = type.toLowerCase();
        if (type === "Sales") {
            salesType = "care_candidate";
        }
        return `/sales/account/${salesType}/${id}`;
    } else {
        return false;
    }
};

export const getCurrentIndex = (
    type: string,
    moduleKey: HistoryModule,
    index: number,
    searchResult: Record<string, any>,
    filterKey: string,
    tabHistory: TabHistory
) => {
    if (type === "open") {
        return (searchResult[filterKey]?.length ?? 0) + index;
    } else {
        return (
            Object.values(tabHistory.open[moduleKey]).length +
            (searchResult[filterKey]?.length ?? 0) +
            index
        );
    }
};
