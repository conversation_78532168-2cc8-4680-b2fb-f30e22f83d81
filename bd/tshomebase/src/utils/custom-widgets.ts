import { GridApi } from "ag-grid-enterprise";


const getAllRows = (gridRef) => {
    let rowData = [];
    gridRef.current.api.forEachNode(node => rowData.push(node));
    return rowData;
  };

export const AgGridPrint = (gridRef) => {
	if (!gridRef?.current?.api) {
		console.error("Grid API is not initialized.");
		return;
	}

	const columns = gridRef.current.api.getColumns().slice(0, 10);
	const getallrowsdata = getAllRows(gridRef);
	const headers = columns.map((col) => col.getColDef().headerName);
	const visibleRows = gridRef.current.api.getRenderedNodes();
	const rowData = getallrowsdata.map((rowNode) => {
		return columns.map((col) => {
			const field = col.getColDef().field;
			return rowNode.data?.[field] ?? "";
		});
	});

	let tableHTML = `
    <style>
        body { font-family: Arial, sans-serif; text-align: center; }
        h1 { font-size: 24px; font-weight: bold; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; font-size: 14px; }
        th, td { padding: 10px; text-align: left; }
        th { font-weight: bold; border-bottom: 2px solid #000; } /* Bold header with a bottom border */
        tr:not(:last-child) td { border-bottom: 1px solid #ddd; } /* Subtle line under each row */
    </style>
    <table>
    `;

	tableHTML += "<tr>" + headers.map((header) => `<th>${header}</th>`).join("") + "</tr>";

	rowData.forEach((row) => {
		tableHTML += "<tr>" + row.map((cell) => `<td>${cell}</td>`).join("") + "</tr>";
	});

	tableHTML += "</table>";
	const printFrame = document.createElement("iframe");
	document.body.appendChild(printFrame);
	const printDoc = printFrame.contentDocument || printFrame.contentWindow.document;

	printDoc.open();
	printDoc.write(`
        <html>
          <head>
            <title></title>
          </head>
          <body">
            ${tableHTML}
          </body>
        </html>
      `);

	printDoc.close();
	printFrame.contentWindow.print();
	document.body.removeChild(printFrame);
};


export const exportDataAsExcel = (gridRef) => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.exportDataAsExcel({
        fileName: 'grid_data.xlsx',
        columnKeys: gridRef.current.api.getColumns()
      });
    } else {
      console.error("Grid API is not initialized.");
    }
  };


export const exportDataAsCsv =  (gridRef) => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.exportDataAsCsv({
        fileName: 'grid_data.csv',
        columnKeys: gridRef.current.api.getColumns()
      });
    } else {
      console.error("Grid API is not initialized.");
    }
  };

  export const copyVisibleRows = (gridRef) => {
    if (gridRef.current && gridRef.current.api) {
      const visibleRows = gridRef.current.api.getRenderedNodes();
      const getallrowsdata = getAllRows(gridRef);
      const columns = gridRef.current.api.getColumns().slice(0, 10);

      const rowData = getallrowsdata.map((rowNode) => {
        return columns.map((col) => {
          const field = col.getColDef().field;
          return rowNode.data?.[field] ?? "";
        });
      });

      navigator.clipboard.writeText(
        rowData.map(node =>
          Object.values(node).join("\t")
        ).join("\n")
      ).then(() => {
        console.log("Copied visible rows to clipboard!");
      }).catch(err => console.error("Failed to copy:", err));
    } else {
      console.error("Grid API is not initialized.");
    }
  };
