import qz from "qz-tray";

export const getPrinters = async () => {
    try {
        if (!qz.websocket.isActive()) {
            await qz.websocket.connect();
        }
        return await qz.printers.details();
    } catch (error) {
        console.log(error);
        return false;
    }
};

export const getPrinterConfigByName = async (name: string, options = {}) => {
    try {
        if (!qz.websocket.isActive()) {
            await qz.websocket.connect();
        }

        const printer = await qz.printers.find(name);
        if (printer)
            return await qz.configs.create(printer, options)
    } catch (error) {
        console.log(error);
        return;
    }
}