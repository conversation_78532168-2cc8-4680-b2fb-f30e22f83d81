:root {

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    /* Body and Root */
    html {
        font-size: 16px;
        line-height: 1.5;
        color: #000;
        font-family: Arial, sans-serif;
    }

    body {
        margin: 0;
        background-color: #fff;
    }

    /* Headings */
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-weight: bold;
        line-height: 1.2;
        margin: 0;
    }

    h1 {
        font-size: 2em;
    }

    h2 {
        font-size: 1.5em;
    }

    h3 {
        font-size: 1.17em;
    }

    h4 {
        font-size: 1em;
    }

    h5 {
        font-size: 0.83em;
    }

    h6 {
        font-size: 0.67em;
    }

    p {
        margin: 1em 0;
    }

    ul,
    ol {
        margin: 1em 0;
        padding-left: 40px;
    }

    li {
        margin: 0.5em 0;
    }

    /* Links */
    a {
        color: #007bff;
        /* Default link color */
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }

    /* Tables */
    table {
        border-collapse: collapse;
        width: 100%;
    }

    th,
    td {
        padding: 8px;
        border: 1px solid #ddd;
        text-align: left;
    }

    /* Forms */
    input,
    textarea,
    select,
    button {
        font: inherit;
        margin: 0;
    }

    button {
        background-color: #007bff;
        color: #fff;
        border: none;
        padding: 0.5em 1em;
        cursor: pointer;
    }

    button:hover {
        background-color: #0056b3;
    }

    textarea {
        resize: vertical;
    }

    /* Images */
    img {
        max-width: 100%;
        height: auto;
    }

}