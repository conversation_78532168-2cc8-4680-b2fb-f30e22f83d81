:root {

    //// Basic Dimensions ////
    --spacing-xxsmall: 2px;
    --spacing-xsmall: 4px;
    --spacing-small: 6px;
    --spacing-standard: 8px;
    --spacing-large: 10px;
    --spacing-xlarge: 12px;
    --spacing-xxlarge: 14px;
    --spacing-xxxlarge: 16px;
    --spacing-xxxxlarge: 18px;
    --spacing-xxxxxlarge: 20px;
    --spacing-xxxxxxlarge: 24px;
    --spacing-xxxxxxxlarge: 26px;
    --spacing-xxxxxxxxlarge: 28px;
    --spacing-xxxxxxxxxlarge: 32px;

    //// Padding needs to define ////
    --spacing-xxsmall-xsmall: 3px;
    --spacing-xsmall-small: 5px;
    --spacing-small-standard: 7px;
    --spacing-xlarge-xxlarge: 13px;

    //// Basic Radiuses ////
    --radius-xxsmall: 2px;
    --radius-xsmall: 4px;
    --radius-small: 6px;
    --radius-medium: 8px;
    --radius-large: 10px;
    --radius-xlarge: 12px;
    --radius-xxlarge: 14px;
    --radius-xxxlarge: 16px;
    --radius-xxxxlarge: 18px;
    --radius-xxxxxlarge: 20px;
    --radius-xxxxxxlarge: 24px;
    --radius-xxxxxxxlarge: 28px;
    --radius-xxxxxxxxlarge: 32px;

}