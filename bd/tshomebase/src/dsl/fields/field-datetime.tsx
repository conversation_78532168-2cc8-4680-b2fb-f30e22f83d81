import React, { useState, useEffect, useRef } from 'react';
import { DatePicker, TimePicker, ConfigProvider } from 'antd';
import type { DatePickerProps } from 'antd';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import './field-datetime.less';
import { DSLListViewProps } from '@blocks/dsl-list-view';
import { DSLField } from '@typedefs/coffee/dsl';

dayjs.extend(customParseFormat);

interface DSLFieldDateTimeProps extends DSLListViewProps {
  v: DSLField;
  readOnly: boolean;
  k: string;
  df: any;
  dd: any;
  dslFieldRef: (helper: any) => void;
}

export const DSLFieldDateTime: React.FC<DSLFieldDateTimeProps> = (props) => {
  const { v, readOnly, k, dd, dslFieldRef, df } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const pickerRef = useRef<HTMLDivElement>(null);

  const getDateFormat = () => {
    switch (v.model.type) {
      case 'date':
        return 'MM/DD/YYYY';
      case 'time':
        return 'hh:mm A';
      case 'datetime':
        return 'MM/DD/YYYY hh:mm A';
      default:
        return 'MM/DD/YYYY';
    }
  };

  const handleChange = (date: dayjs.Dayjs | null, dateString: string | string[]) => {
    setSelectedDate(date);
    updateInputValue(dateString);
  };

  const handleOk = (date: dayjs.Dayjs) => {
    setSelectedDate(date);
    updateInputValue(date.format(getDateFormat()));
    setIsOpen(false);
  };

  const updateInputValue = (value: string | string[]) => {
    const inputElement = dd?.field_nodes?.[k];
    if (inputElement) {
      inputElement.val(value);
      inputElement.trigger('change');
    }
  };

  const pickerStyle: React.CSSProperties = {
    position: 'absolute',
    opacity: 0,
    width: '100%',
    height: '100%',
  };

  const handleBlur = (event: React.FocusEvent) => {
  }

  const updateIconClass = (open: boolean) => {
    const iconElement = dd?.field_nodes?.[k]?.siblings('.field-icon');
    if (open) {
      iconElement?.addClass('open');
    } else {
      iconElement?.removeClass('open');
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    updateIconClass(open);
  }

  const commonProps: DatePickerProps = {
    style: pickerStyle,
    open: isOpen,
    value: selectedDate,
    popupClassName: 'datetime-picker-dropdown',
    placement: 'bottomRight',
    disabled: dd?.field_nodes?.[k]?.attr('readonly') ? true : false,
    onOpenChange: handleOpenChange,
    onBlur: handleBlur,
    onChange: handleChange,
    onOk: handleOk,
	getPopupContainer: () => document.body,
};

  const togglePicker = (event?: React.MouseEvent) => {
    event?.stopPropagation();
    if (isOpen) {
      setIsOpen(false);
      updateIconClass(false);
    } else {
      setIsOpen(true);
      updateIconClass(false);
    }
  };

  useEffect(() => {
    dslFieldRef({
      togglePicker,
      setDate: (dateString: string) => {
        const format = getDateFormat();
        const parsedDate = dayjs(dateString, format);
        if (parsedDate.isValid()) {
          setSelectedDate(parsedDate);
          updateInputValue(dateString);
        }
      },
      isShown: () => {
        return isOpen;
      }
    });
  }, [dslFieldRef, isOpen]);

  const renderPicker = () => {
    switch (v.model.type) {
      case 'date':
        return (
          <DatePicker
            {...commonProps}
            format="MM/DD/YYYY"
          />
        );
      case 'time':
        return (
          <TimePicker
            {...commonProps}
            format="hh:mm A"
            use12Hours
            minuteStep={1}
            showSecond={false}
          />
        );
      case 'datetime':
        return (
          <DatePicker
            {...commonProps}
            showTime={{
              format: 'hh:mm A',
              use12Hours: true,
              minuteStep: 1,
              showSecond: false,
            }}
            format="MM/DD/YYYY hh:mm A"
          />
        );
      default:
        return null;
    }
  };

  return (
    <ConfigProvider>
      <div ref={pickerRef} style={{ position: 'relative', width: 0, height: 0 }}>
        {renderPicker()}
      </div>
    </ConfigProvider>
  );
};