import type { FC } from "react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import "./field-embed.less";
import { request } from "@core/request/request";
import { DSLListViewProps } from "@blocks/dsl-list-view/dsl-list-view";
import DSL<PERSON>ridView, { DSLGridViewRef } from "@blocks/dsl-grid-view/dsl-grid-view";
import { CRErrorBoundary } from "@blocks/error-boundary/cr-error-boundary";
import { DSLDrawLinkMap, DSLField, DSLForm } from "@typedefs/coffee/dsl";
import { getFiltersFromSourceFilter } from "@utils/fx";
import { IFormData, fetchFormFilters, IFormFiltersResponse } from "@hooks/form-data";
import { DSLCardActionButton } from "@blocks/dsl-card-view/dsl-card-action-btns";
import _ from "lodash";
import { getFormConfig } from "@dsl-form/config";
import { TabData } from "@blocks/tab-list";
import { generateLinkMapFromPreset } from "@utils/dsl-fx";
import { useUpdateHash } from "@hooks/update-hash";

interface DSLFieldEmbedProps extends DSLListViewProps {
	v: DSLField;
	readOnly: boolean;
	k: string;
	df: any;
	dd: any;
	dslFieldRef: (helper: any) => void;
}

export interface DSLFieldEmbedBtnsComponentProps extends DSLFieldEmbedProps {
	form: string;
	hash: string;
	canAddPreset: boolean;
	selected: number | string | null;
	disabled: boolean;
	onNew: () => void;
	getPreset: () => Record<string, unknown> | false;
	onEdit: () => void;
	onArchive: () => void;
	tableRef: DSLGridViewRef;
	refreshGridInternal: () => void;
	getLinkMapFromPreset: (preset: Record<string, unknown>) => DSLDrawLinkMap;
}

export const DSLFieldEmbed: FC<DSLFieldEmbedProps> = (props) => {
	let { v, k, dd, dslFieldRef, df } = props;
	const [readOnly, setReadOnly] = useState(props.readOnly);
	const [hash, updateHash] = useUpdateHash();
	const [fd, setFd] = useState<IFormFiltersResponse>({
		loading: true,
		failed: false,
		data: [],
	});
	const [canAddPreset, setCanAddPreset] = useState(true);
	if (!df || !Array.isArray(df)) {
		df = [];
	}

	let code = v.view.embed.query;
	let type = "form";
	let rowSelection = false;
	if (v.view.embed.selectable) {
		rowSelection = true;
	}
	let form = v.view.embed.form || "";
	if (code) {
		form = `__query_module_${code}`;
		type = "query";
	}

	let rowSelectionMode: "single" | "multi" = "single";
	rowSelectionMode = v.model.multi ? "multi" : "single";
	if (v.view.readonly && !v.model.save) {
		rowSelection = false;
	}
	const allowEmptyArray = v.view.embed.request_type == "POST" ? true : false;
	useEffect(() => {
		if (code) {
			fetchFormFilters("query", { filter: { code: code } })
				.then((d) => {
					setFd({
						loading: false,
						failed: false,
						data: d.data,
					});
				})
				.catch((d) => {
					setFd({
						loading: false,
						failed: true,
						data: [],
					});
				});
		}
		updateCanAddPreset();
	}, []);

	const updateCanAddPreset = () => {
		if (!getPreset()) {
			setCanAddPreset(false);
		} else if (!getFormConfig(form).allowAdd) {
			setCanAddPreset(false);
		} else {
			setCanAddPreset(true);
		}
	};

	const getColumns = () => {
		return window.get_grid_fields(window.DSL[form], v) as string[];
	};

	const getWidths = () => {
		return window.get_grid_width(window.DSL[form], v) as number[];
	};

	const getLabels = () => {
		return window.get_grid_labels(window.DSL[form], v) as (string | null)[];
	};

	let columnsProperties = useMemo(() => {
		if (type == "form") {
			return {
				customColumns: getColumns(),
				columnsWidth: getWidths(),
				columnsLabel: getLabels(),
				btnComponent: getFormConfig(form).embedConfig?.[dd.options.form]?.[k]?.btnComponent,
			};
		}
		if (fd.loading || fd.failed) return false;
		if (fd.data.length == 0) return false;
		const query = fd.data[0];

		if (!window.DSL[form]) {
			window.DSL[form] = query.dsl_struct as DSLForm;
		}
		if (!window.DSL[form]) {
			return null;
		}

		return {
			customColumns: getColumns(),
			columnsWidth: getWidths(),
			columnsLabel: getLabels(),
			btnComponent: getFormConfig(form).embedConfig?.[dd.options.form]?.[k]?.btnComponent,
		};
	}, [fd.data]);

	let sourceFilter = v.model.sourcefilter;
	let { filters, gridParams } = getFiltersFromSourceFilter(dd, sourceFilter, {}, allowEmptyArray);

	const gridRef = useRef<DSLGridViewRef>({} as any);
	const [selected, setSelected] = useState<(number | string)[]>([]); // Doing this for multi support in future

	const onRowClick = (opts: TabData) => {
		if (!readOnly) {
			setSelected((p) => {
				p = Array.from(p);
				if (p.some((id) => id == opts.id)) {
					return [];
				}

				return [opts.id];
			});
		}
		if (opts.type == "click") {
			return;
		}
		if (v.view.embed.form == "billing_invoice") {
			window.App.reactNav.goTo(`/billing/invoices/${opts.id}/read`);
			return;
		}

		window.Flyout.open({
			form: form,
			record: opts.id,
			card: readOnly ? "read" : "edit",
			tab_can_edit_override: () => {
				return false;
			},
		});
	};

	const getPreset = () => {
		if (!v.view?.embed?.add_preset) return {};
		let preset = _.cloneDeep(v.view?.embed?.add_preset);
		for (let key in preset) {
			let v = preset[key];
			if (v && typeof v === "string" && v.startsWith("{") && v.endsWith("}")) {
				const vv = v.replace(/[{}]/g, "") as string;
				const vf = dd.preset[vv] || dd.value_field(vv);
				if (!vf) {
					return false;
				}
				preset[key] = vf;
			}
		}
		delete preset.id; // I'm pretty sure someone is gonna try to pass id at some point
		return preset;
	};

	const getLinkMapFromPreset = (preset: Record<string, unknown>) => {
		const linkMap: DSLDrawLinkMap = {
			link: dd.options.link || "",
			links: dd.options.links || [],
			linkid: dd.options.linkid || {},
		};
		return generateLinkMapFromPreset(form, linkMap, preset);
	};

	const onNew = () => {
		let preset = getPreset() || {};
		const linkMap = getLinkMapFromPreset(preset); // Just in case fields are not part of section which is usually the  case with linkids
		const dfd = window.Flyout.open({
			form: v.view.embed.add_form || form,
			...linkMap,
			autoRecoverEnabled: false,
			preset: preset,
		});
		dfd.done((data) => {
			gridRef.current?.ddg?.push_embed(data.id);
		});
	};

	const queryFormOpen = (opts: TabData) => {
		if (!readOnly) {
			setSelected((p) => {
				p = Array.from(p);
				if (p.some((id) => id == opts.id)) {
					return [];
				}

				return [opts.id];
			});
		}
		if (opts.type == "click") {
			return;
		}
		const rowData = opts.rowData;
		const query_id = rowData?.query_id as number;
		const query_form = rowData?.query_form as string;
		if (!query_id || !query_form) {
			return;
		}
		if (!window.DSL[query_form]) {
			return;
		}
		if (query_form == "billing_invoice") {
			window.App.reactNav.goTo(`/billing/invoices/${query_id}/read`);
			return;
		}
		const dfd = window.Flyout.open({
			form: query_form,
			record: query_id,
			card: readOnly ? "read" : "edit",
		});
		dfd.done((data) => {
			refreshGridInternal();
		});
	};

	const onArchive = () => {
		request({
			url: `/form/${form}/${selected[0]}/archive/`,
			method: "PUT",
			data: {
				archived: true,
			},
		})
			.then((data) => {
				refreshGridInternal();
			})
			.catch((err) => {
				console.log(err);
			});
	};

	const embedValidateTransform = async () => {
		const d = dd.field_nodes[k];
		if (v.model.transform.length > 0)
			window.DSLFx.TransformField(window.DSL[form], dd, v.model.transform as any, d, k, true);
		if (v.view.transform.length > 0)
			window.DSLFx.TransformField(window.DSL[form], dd, v.view.transform as any, d, k, true);
		const ferr = await window.DSLFx.ValidateFieldRules(window.DSL[form], dd, v, d, k, true);
		dd?.options?.parent?.ddf?.check_missing_required_fields?.();
	};

	const onEdit = () => {
		const dfd = window.Flyout.open({
			form: form,
			record: selected[0],
			link: dd.options.link,
			linkid: dd.options.linkid,
			links: dd.options.links,
			autoRecoverEnabled: false,
		});
		dfd.done((data) => {
			refreshGridInternal();
		});
	};

	const disable = selected.length != 1;
	if (!form) return <div>Unexpected Error...</div>;
	if (type == "query") {
		if (fd.loading && fd.data.length == 0) return <div>Loading...</div>;
		if (fd.failed) return <div>Error...</div>;
	}
	if (!columnsProperties) return <div>DSL Info Not Found...</div>;

	const refreshGridInternal = () => {
		try {
			updateCanAddPreset();
			dd?.field_nodes?.[k]?.data("refresh")?.();
		} catch (err) {
			console.log(err);
		}
		const ref = (gridRef as any).ref;
		if (!ref.ddg) {
			return;
		}
		const { filters: f, gridParams: gp } = getFiltersFromSourceFilter(dd, sourceFilter, {}, allowEmptyArray);
		filters = f;
		gridParams = gp;
		ref.ddg.options.filtersPresetFixed = f;
		ref.ddg.options.gridparams = gp;
		ref.ddg?.table_grid?.draw(false);
		setTimeout(() => {
			updateHash();
		}, 100);
	};

	const EmbedBtnsComponent = columnsProperties.btnComponent;
	const hasEmbedBtns = EmbedBtnsComponent ? true : false;
	return (
		<div className="dsl-field-embed">
			<i
				className="fa-solid fa-arrows-rotate refresh-icon"
				style={{ zIndex: 6 }}
				onClick={refreshGridInternal}
			></i>
			<CRErrorBoundary>
				<DSLGridView
					key={readOnly ? "read" : "edit"}
					embedValues={df}
					embedValidateTransform={embedValidateTransform}
					fieldEmbed={v}
					compact={true}
					{...columnsProperties}
					link={dd.options.link}
					linkid={dd.options.linkid}
					rowSelectionMode={rowSelectionMode}
					rowSelection={rowSelection}
					readOnly={readOnly}
					rankLevel={v.view.grid.rank || "none"}
					optimisticRender={true}
					links={dd.options.links}
					filtersPresetFixed={filters}
					form={form}
					gridSourceOverrideURL={v.view.embed.query ? `/query/${code}` : undefined}
					gridparams={gridParams}
					onReadyGrid={() => {
						const dx = dd.field_nodes[k];
						if (!dx) return;
						const pendingValue = dx.data("pendingValue");
						if (!pendingValue) return;
						window.FieldEmbed.value_set(dx, pendingValue);
						dx.data("pendingValue", null);
					}}
					setRef={(ref) => {
						gridRef.ref = ref;
						gridRef.current = ref;
						try {
							dd?.options?.parent?.ddf?.check_missing_required_fields?.();
						} catch (err) {
							console.log(err);
						}
						const getValue = () => {
							let ddg = gridRef.current?.ddg;
							if (!ddg || !ddg.table_grid || !ddg.table_grid?.rows) return df;
							let fv: any[] = [];
							let selector = ddg.table_grid.rows({ selected: true });
							if (v.view.grid.selectall && !rowSelection) {
								selector = ddg.table_grid.rows();
							}
							selector[0].forEach((i: number) => {
								const row = ddg.table_grid.row(i);
								const data = row.data()?._meta || {};
								let p = ddg.options?.embedValues?.find((r: any) => r.id == data.id) || null;
								if (p) fv.push(p);
							});
							if (!v.model.multi && fv.length > 1) {
								fv = [fv[0]];
							}
							return fv;
						};
						const setValue = (val = []) => {
							if (!Array.isArray(val)) {
								return;
							}
							let ddg = gridRef.current?.ddg;
							if (!ddg) {
								return;
							}
							ddg.options.embedValues = val;
							ddg.table_grid?.draw?.(false);
							return;
						};
						const refreshGrid = (selectAll: boolean = false) => {
							if (selectAll && gridRef?.current?.ddg) {
								gridRef.current.ddg.first_render = true;
							}
							try {
								updateCanAddPreset();
								dd?.field_nodes?.[k]?.data("refresh")?.();
							} catch (err) {
								console.log(err);
							}
							if (!gridRef.current.ddg) {
								return;
							}
							const { filters: f, gridParams: gp } = getFiltersFromSourceFilter(
								dd,
								sourceFilter,
								{},
								allowEmptyArray
							);
							filters = f;
							gridParams = gp;
							gridRef.current.ddg.options.filtersPresetFixed = f;
							gridRef.current.ddg.options.gridparams = gp;
							gridRef?.current?.ddg?.table_grid?.draw(false);
						};
						const getById = (id: number) => {
							let ddg = gridRef.current.ddg;
							const d: {
								state: "ready" | "pending";
								data: IFormData | null;
							} = {
								state: "pending",
								data: null,
							};
							if (!ddg || !ddg.original_data_copy) {
								return d;
							}
							for (let i = 0; i < ddg.original_data_copy.length; i++) {
								if (ddg.original_data_copy[i].id == id) {
									d.state = "ready";
									d.data = ddg.original_data_copy[i];
									break;
								}
							}
							return d;
						};
						const getAllData = () => {
							let ddg = gridRef.current.ddg;
							const d: {
								state: "ready" | "pending";
								data: IFormData[] | null;
							} = {
								state: "pending",
								data: null,
							};
							if (!ddg || !ddg.original_data_copy) {
								return d;
							}
							d.state = "ready";
							d.data = ddg.original_data_copy;
							return d;
						};
						const validateField = () => {
							if (readOnly) return false;
							const vals = getValue() || [];
							if (vals.length == 0 && v.model.required) {
								return "is required";
							}
							const subfields = v.model.subfields;
							if (vals.length && Object.keys(subfields).length) {
								for (let i = 0; i < vals.length; i++) {
									const val = vals[i];
									for (let key in subfields) {
										const subfield = subfields[key];
										if (subfield.required) {
											if (!val[key]) {
												return "fill in missing required subfields";
											}
										}
									}
								}
							}
							return false;
						};
						dslFieldRef({
							getValue,
							setValue,
							getById,
							validateField,
							refreshGrid,
							getAllData,
							embedValidateTransform,
							updateReadOnly: (r: boolean) => {
								setReadOnly(r);
							},
						});
					}}
					onRowEvent={(opts) => {
						if (type == "query") {
							queryFormOpen(opts);
							return;
						}
						opts.gridRef = (gridRef.current || undefined) as any;
						onRowClick(opts);
					}}
				/>
			</CRErrorBoundary>
			{type == "query" && !readOnly && EmbedBtnsComponent && hasEmbedBtns && (
				<div className="compact-btns-container">
					<EmbedBtnsComponent
						{...props}
						hash={hash}
						form={form}
						canAddPreset={canAddPreset}
						disabled={disable}
						selected={disable ? null : selected[0]}
						onNew={onNew}
						getPreset={getPreset}
						onEdit={onEdit}
						getLinkMapFromPreset={getLinkMapFromPreset}
						onArchive={onArchive}
						refreshGridInternal={refreshGridInternal}
						tableRef={gridRef.current}
					/>
				</div>
			)}
			{readOnly || type == "query" || v.view.grid.add == "none" ? null : (
				<div className="compact-btns-container">
					{EmbedBtnsComponent && hasEmbedBtns ? (
						<EmbedBtnsComponent
							{...props}
							hash={hash}
							form={form}
							canAddPreset={canAddPreset}
							disabled={disable}
							selected={disable ? null : selected[0]}
							onNew={onNew}
							getPreset={getPreset}
							onEdit={onEdit}
							getLinkMapFromPreset={getLinkMapFromPreset}
							onArchive={onArchive}
							refreshGridInternal={refreshGridInternal}
							tableRef={gridRef.current}
						/>
					) : null}
					{!hasEmbedBtns && window.Auth.can_create(form) && canAddPreset && (
						<DSLCardActionButton label="Add" icon={"plusOutline"} action="add" onClick={onNew} />
					)}
					{!hasEmbedBtns && window.Auth.can_update_any(form) && (
						<DSLCardActionButton
							label="Edit"
							icon={"editOutline"}
							action="edit"
							disabled={disable}
							onClick={onEdit}
						/>
					)}
					{!hasEmbedBtns && window.Auth.can_update_any(form) && (
						<DSLCardActionButton
							label="Archive"
							icon={"archiveOutline"}
							action="archive"
							disabled={disable}
							onClick={onArchive}
						/>
					)}
				</div>
			)}
			{!readOnly && type != "query" && v.view.grid.add == "none" && v.view.grid.edit ? (
				<div className="compact-btns-container">
					{window.Auth.can_update_any(form) && (
						<DSLCardActionButton
							label="Edit"
							icon={"editOutline"}
							action="edit"
							disabled={disable}
							onClick={onEdit}
						/>
					)}
				</div>
			) : null}
		</div>
	);
};
