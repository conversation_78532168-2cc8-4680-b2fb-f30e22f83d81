import "./field-picker.less";

export interface FieldDatePickerProps {
	placeholder?: string;
	defaultValue?: any;
	className?: string;
	onChange?: (value: any) => void;
}
export const FieldDatePick: React.FC<FieldDatePickerProps> = (props) => {
	const { defaultValue, className } = props;
	const dv = window.moment(defaultValue, "MM/DD/YYYY").format("YYYY-MM-DD");
	return (
		<input
			className={"field-date-picker" + (className ? " " + className : "")}
			type="date"
			value={dv}
			onChange={(event) => {
				if (props.onChange) {
					if (event.target.value) {
						const date = window.moment(event.target.value, "YYYY-MM-DD").format("MM/DD/YYYY");
						if (date == "Invalid date") {
							props.onChange(null);
						} else {
							props.onChange(date);
						}
					} else {
						props.onChange(null);
					}
				}
			}}
		/>
	);
};
