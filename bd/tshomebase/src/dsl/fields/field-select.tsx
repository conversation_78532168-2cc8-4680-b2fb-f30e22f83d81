import React from "react";
import AsyncSelect from "react-select/async";
import Select from "react-select";
import { Theme } from "react-select";
import { request } from "@core/request/request";
import "./field-select.less";
import { getNextZIndexFlyoutModelInception } from "@utils/dsl-fx";
import { IFormData } from "@hooks/index";
export interface FieldSelectProps {
	form: string;
	sourceId?: string;
	placeholder?: string;
	multi?: boolean;
	sort?: string;
	defaultValueSource?: any;
	defaultValueAutoName?: any;
	customStyles?: any;
	disabled?: boolean;
	isClearable?: boolean;
	onChange?: (value: any, autoName: any, record?: any) => void;
	onChangeSource?: (value: any) => void;
	onChangeAutoName?: (value: any) => void;
	filterOption?: (option: any, input: string) => boolean;
	loadOptions?: (inputValue: string, callback: (options: SelectOptions[]) => void) => void;
	options?: Record<string, string>[];
	extraParams?: string;
	theme?: (theme: Theme) => Theme;
}
export interface SelectOptions {
	value: string;
	label: string;
	_raw?: IFormData | IFormData[];
}

export const FieldSelect: React.FC<FieldSelectProps> = (props) => {
	const {
		form,
		multi,
		sort,
		placeholder,
		defaultValueSource,
		defaultValueAutoName,
		onChange,
		onChangeSource,
		onChangeAutoName,
		filterOption,
		customStyles,
		disabled,
		options,
		theme,
	} = props;
	let { isClearable } = props;
	if (isClearable === undefined) {
		isClearable = true;
	}
	const sourceId = props.sourceId || "id";
	const loadOptions = (inputValue: string, callback: (options: SelectOptions[]) => void) => {
		if (props.loadOptions) {
			props.loadOptions(inputValue, callback);
			return;
		}
		let ss = "&filter=auto_name:!..";
		if (inputValue) {
			ss = "&filter=auto_name:*" + inputValue + "*";
		}
		if (props.extraParams) {
			ss += "&" + props.extraParams;
		}
		request({
			url: `/form/${form}/?fields=${sourceId == "id" ? "min" : "list"}&sort=${
				sort || "auto_name"
			}&limit=100${ss}`,
		})
			.then((res) => {
				const opts = res.data.map((row: any) => ({
					value: row[sourceId],
					label: row.auto_name,
					_raw: row,
				}));
				callback(opts);
			})
			.catch((err) => {
				console.log(err);
			});
	};
	const mutateDefaultToSelect = () => {
		if (defaultValueSource) {
			if (Array.isArray(defaultValueSource)) {
				return defaultValueSource.map((v: any, index) => ({
					value: v,
					label: defaultValueAutoName[index],
				}));
			} else {
				return {
					value: defaultValueSource,
					label: defaultValueAutoName,
				};
			}
		}
		return null;
	};

	const updateValue = (current: SelectOptions | SelectOptions[] | null) => {
		let v = null;
		let an = null;
		if (current) {
			if (Array.isArray(current) && current.length > 0) {
				v = current.map((v) => v.value);
				an = current.map((v) => v.label);
			} else if (!Array.isArray(current) && current && typeof current === "object") {
				v = current.value;
				an = current.label;
			}
		}
		if (onChange) {
			onChange(v || null, an || null, Array.isArray(current) ? current.map((c) => c._raw) : current?._raw);
		}
		if (onChangeSource) {
			onChangeSource(v || null);
		}
		if (onChangeAutoName) {
			onChangeAutoName(an || null);
		}
	};
	if (options) {
		return (
			<Select
				className="field-select"
				menuPosition={"fixed"}
				menuPortalTarget={document.body}
				options={options as any}
				isDisabled={disabled}
				isMulti={multi}
				filterOption={filterOption as any}
				placeholder={placeholder}
				onChange={updateValue as any}
				isClearable={isClearable}
				isSearchable={true}
				value={mutateDefaultToSelect()}
				styles={{
					...(customStyles || {}),
					menuPortal: (provided) => ({
						...provided,
						zIndex: getNextZIndexFlyoutModelInception(),
					}),
				}}
				theme={theme}
			/>
		);
	}

	return (
		<AsyncSelect
			className="field-select"
			menuPosition={"fixed"}
			loadOptions={loadOptions}
			menuPortalTarget={document.body}
			defaultOptions
			isDisabled={disabled}
			isMulti={multi}
			placeholder={placeholder}
			onChange={updateValue as any}
			isClearable={isClearable}
			value={mutateDefaultToSelect()}
			styles={{
				...(customStyles || {}),
				menuPortal: (provided) => ({
					...provided,
					zIndex: getNextZIndexFlyoutModelInception(),
				}),
			}}
			theme={theme}
		/>
	);
};

FieldSelect.defaultProps = {
	theme: (theme: Theme) => ({
		...theme,
		borderRadius: 0,
		colors: {
			...theme.colors,
			primary: "#4D4E8D",
			primary25: "#91A2A8",
		},
	}),
};
