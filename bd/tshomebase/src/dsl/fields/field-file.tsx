import { MediaRendererProps } from "@components/popups/media-viewer";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import { MediaRenderer } from "@components/popups/media-viewer";
import { FC } from "react";
import ReactDOMServer from "react-dom/server";
import { CLFile } from "@typedefs/window";
import _ from "lodash";
import "./field-file.less";

export const FileFieldMediaViewer: FC<{ file: CLFile; ext: string | undefined }> = ({ file, ext }) => {
	return (
		<GenericCardContainer title={file.filename || "Viewer"} className={`field-field-media-viewer ${ext}`}>
			<MediaRenderer file={file} />
		</GenericCardContainer>
	);
};
// Need to show just non react components in the html
// Change this to loadComponent if reactivity is needed
export const getMediaViewerHTML = (file: CLFile) => {
	if (!file || _.isEmpty(file)) {
		return null;
	}
	const ext = /(?:\.([^.]+))?$/.exec(file.filename)?.[1]?.toLowerCase();

	return ReactDOMServer.renderToString(<FileFieldMediaViewer file={file} ext={ext} />);
};
