import { <PERSON><PERSON><PERSON> } from "@typedefs/coffee/dsl";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { useEffect } from "react";
import ReactJson from "react-json-view";
import "./field-json.less";

interface FieldJSONViewerProps {
	dd: IDSLDrawSubForm;
	v: DSLField;
	k: string;
}

export const FieldJSON = (props: FieldJSONViewerProps) => {
	const { dd, v, k } = props;
	const mode = dd.options.mode;
	const src = dd.preset[k] || {};
	useEffect(() => {
		setTimeout(() => {
			if (dd.field_nodes[k]) {
				dd.field_nodes[k].css("display", "none");
			}
			if (typeof src == "string") {
				dd.value_field(k, src, true, true);
			} else if (typeof src == "object") {
				dd.value_field(k, JSON.stringify(src), true, true);
			}
		}, 1500);
	}, []);
	let json = {};
	if (src && typeof src == "string") {
		try {
			json = JSON.parse(src);
		} catch (e) {
			json = { src: "Invalid JSON" };
		}
	} else if (src && typeof src == "object") {
		json = src;
	}
	return (
		<div className={`field-json-react-json json-display-${mode}`}>
			<ReactJson
				theme={"monokai"}
				src={json}
				style={{
					padding: "10px",
					borderRadius: "12px",
					width: "100%",
					maxHeight: "400px",
					overflow: "auto",
				}}
			/>
		</div>
	);
};
