.controls {
  position: relative;

  .datepicker[data-format="MM/dd/yyyy"] {
    &+*::before {
      content: '\f133';
      font-weight: 300;
    }
  }

  .datepicker[data-format="MM/dd/yyyy HH:mm pp"] {
    &+*::before {
      content: '\e0d2';
      font-weight: 300;
    }
  }

  .datepicker[data-format="HH:mm pp"] {
    &+*::before {
      content: '\f017';
      font-weight: 300;
    }
  }

  >input.datepicker[readonly],
  >input.datepicker[disabled] {
    &+*::before {
      content: '';
    }
  }

  .field-icon {
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: #667085;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.3s ease;
    font-family: 'Font Awesome 6 Pro';

    &:hover {
      color: #7F56D9;
    }

    &.open {
      color: #7F56D9;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .controls {
    input.datepicker {
      font-size: 14px;
    }

    .field-icon {
      font-size: 14px;
    }
  }
}

// Additional styles for other elements
.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #344054;
  margin-bottom: 6px;

  .required {
    color: #F04438;
    margin-left: 4px;
  }
}

.field-notes {
  margin-top: 6px;
  font-size: 14px;
  line-height: 20px;
  color: #667085;
}

// Error state
.error {
  input.datepicker {
    border-color: #F04438;

    &:focus {
      box-shadow: 0 0 0 4px rgba(240, 68, 56, 0.1);
    }
  }

  .field-error {
    margin-top: 6px;
    font-size: 14px;
    line-height: 20px;
    color: #F04438;
  }
}

.datetime-picker-dropdown {
  background: #FFFFFF;
  border-color: #E5E7EB;

  .ant-picker-header {
    border-color: #E5E7EB;

    button {
      color: #6B7280 !important;

      &:hover {
        color: #9A94BF;
      }
    }

    .ant-picker-header-view {
      color: #111827;
    }
  }

  .ant-picker-body {
    table {
      th {
        color: #6B7280;
      }

      td {
        .ant-picker-cell-inner {
          color: #111827 !important;

          &:hover {
            background: #EEF2FF !important;
          }
        }

        &.ant-picker-cell-selected .ant-picker-cell-inner {
          background: #9A94BF !important;
          color: white !important;
        }

        &.ant-picker-cell-today .ant-picker-cell-inner::before {
          border-color: #9A94BF;
        }

        &.ant-picker-cell-disabled .ant-picker-cell-inner {
          color: #9CA3AF;
        }
      }
    }
  }

  .ant-picker-time-panel {
    border-color: #E5E7EB;

    .ant-picker-time-panel-column:not(:first-child) {
      border-color: #E5E7EB;
    }

    .ant-picker-time-panel-cell-inner {
      color: #111827 !important;

      &:hover {
        background: #EEF2FF;
      }
    }

    .ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
      background: #9A94BF !important;
      color: #FFFFFF !important;
    }
  }

  .ant-picker-footer {
    border-color: #E5E7EB;

    .ant-picker-ranges {

      .ant-picker-now-btn,
      .ant-picker-today-btn {
        color: #9A94BF;

        &:hover {
          color: #8A84AF;
        }
      }
    }

    .ant-picker-ok {
      .ant-btn {
        background: #9A94BF;
        border-color: #9A94BF;
        color: #FFFFFF !important;

        &:hover {
          background: #8A84AF;
          border-color: #8A84AF;
        }

        &:active {
          background: #7A7493;
          border-color: #7A7493;
        }
      }
    }
  }
}