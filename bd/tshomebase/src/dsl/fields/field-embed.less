@import (reference) "../../less/style/main.less";
@import (reference) '../../blocks/dsl-list-compact-view/dsl-list-compact-view.less';

#application {
    .form-horizontal {
        .form-group {
            .controls {
                .dsl-field-embed {
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    width: 100%;
                    background-color: var(--white);
                    border: 1px solid var(--gray-100);
                    border-radius: var(--radius-medium);
                    overflow-y: auto;
                    max-height: 300px;

                    &:has(.dsl-action-btn) {
                        margin-bottom: 55px;
                    }

                    .dsl-grid-view {
                        overflow-y: auto;
                        .tr-select {
                            background: #d4d4d4 !important;
                        }
                    }


                    .refresh-icon {
                        position: absolute;
                        right: 10px;
                        padding: 2px;
                        top: -26px;
                        cursor: pointer;
                    }

                    max-height: 300px;


                    .subfield-required {
                        border-color: @coral-red !important;
                    }

                    .dataTables_scroll {
                        box-shadow: none !important;
                        min-height: 50px !important;
                        max-height: calc(100%) !important;
                        padding: var(--spacing-standard);
                        padding-top: 0px;

                        table {
                            tr {
                                &:not(:last-child) {
                                    border-bottom: 1px solid var(--gray-100);
                                }

                                th,
                                td {
                                    color: var(--gray-700);
                                }
                            }
                        }

                        .dataTables_scrollHead {
                            position: sticky !important;
                            z-index: 1;
                            top: 0px;

                            table {
                                margin: 0px !important;

                                thead {
                                    tr {
                                        th {
                                            background-color: var(--white);
                                            font-size: var(--font-size-xxsmall);
                                            padding: var(--spacing-standard);
                                            font-weight: var(--font-weight-medium);
                                        }
                                    }
                                }
                            }
                        }

                        .dataTables_scrollBody {
                            border-bottom-left-radius: 8px !important;
                            border-bottom-right-radius: 8px !important;
                            padding-bottom: 2px;
                            background-color: transparent;

                            table {
                                tbody {
                                    tr {
                                        td {

                                            font-weight: var(--font-weight-bold);
                                            font-size: var(--font-size-xsmall);

                                            &.select-checkbox::before {
                                                width: 16px !important;
                                                height: 16px !important;
                                                border: 1px solid var(--gray-300);
                                                border-radius: var(--radius-xsmall);
                                                background-color: var(--white);

                                                &:hover {
                                                    border: 1px solid var(--color-primary);
                                                }
                                            }


                                            &.rank {
                                                text-align: right;
                                                display: flex;
                                                justify-content: flex-end;
                                            }
                                        }

                                        &.even {
                                            background-color: var(--color-text-50);
                                        }

                                        .select-checkbox {
                                            min-width: 40px;
                                        }

                                        &.selected:last-child {
                                            .move-down {
                                                color: lightgrey !important;
                                            }
                                        }

                                        &.selected:first-child {
                                            .move-up {
                                                color: lightgrey !important;
                                            }
                                        }

                                        &.selected {
                                            color: var(--color-primary);
                                            background: var(--brand-50);

                                            .select-checkbox::before {
                                                border: 1px solid var(--color-primary);
                                                background-color: var(--color-primary);
                                            }

                                            .select-checkbox::after {
                                                background-size: inherit;
                                                background-position: center;
                                                background-image: url(../../public/icons/common/outlined/yes-tick-outline.svg);
                                                background-repeat: no-repeat;
                                                background-size: contain;
                                                content: "" !important;
                                                position: absolute;
                                                top: calc(50% + 4px);
                                                bottom: 50%;
                                            }

                                            .move-up,
                                            .move-down {
                                                opacity: 0;
                                            }

                                            &:hover {

                                                .move-up,
                                                .move-down {
                                                    opacity: 1;
                                                }
                                            }

                                            td.rank {
                                                padding: 0px !important;

                                                .move-up,
                                                .move-down {
                                                    cursor: pointer;
                                                    font-weight: bolder;
                                                    padding: 10px;
                                                    color: #a3a3a3;
                                                    font-size: 1em;
                                                }

                                                >div {
                                                    display: flex;
                                                    justify-content: center;
                                                    align-items: center;
                                                    gap: 10px;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }

                    .dataTables_info {
                        display: none !important;
                    }

                    .dataTables_paginate {
                        display: none !important;
                    }

                    .compact-btns-container {
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        position: absolute;
                        bottom: 0px;
                        gap: var(--spacing-xlarge);

                        .dsl-action-btn {
                            margin-top: 0px !important;
                            margin: var(--spacing-large) 0px 4px 4px; // to show outline in focus state
                            cursor: pointer;
                            padding: var(--spacing-standard) var(--spacing-xlarge);
                            text-transform: capitalize;
                            gap: var(--spacing-xsmall);
                            border-radius: var(--radius-medium);
                            width: auto;
                            min-width: 30px !important;
                            max-width: fit-content;
                            min-height: 36px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            flex-shrink: 0;

                            .inner-cont {
                                .icon {
                                    width: 20px;
                                    height: 20px;
                                    background-repeat: no-repeat;
                                    background-position: center;
                                    background-size: contain;
                                    filter: none !important;
                                }

                                .label {
                                    font-size: var(--font-size-xsmall) !important;
                                    font-weight: var(--font-weight-medium);
                                    background-color: transparent;
                                    filter: none !important;
                                }
                            }

                            &.disabled {
                                cursor: not-allowed !important;
                                opacity: 1 !important;
                                filter: none !important;
                            }

                            &.btn-add,
                            &.btn-archive {
                                box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                background-color: var(--color-text-50) !important;

                                .inner-cont {

                                    .label {
                                        color: var(--brand-700) !important;
                                    }
                                }

                                &:hover {
                                    background-color: var(--brand-50) !important;

                                    .inner-cont {

                                        .label {
                                            color: var(--brand-800) !important;
                                            background-color: transparent;
                                        }
                                    }
                                }

                                &:focus {
                                    background-color: var(--brand-100);
                                    box-shadow: 0px 0px 0px 1px #0A0D122E inset, 0px -2px 0px 0px #0A0D120D inset, 0px 1px 2px 0px #0A0D120D, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px var(--color-tertiary);

                                    .inner-cont {

                                        .label {
                                            color: var(--brand-700);
                                        }
                                    }
                                }

                                &.disabled {
                                    background-color: var(--white) !important;
                                    outline: 1px solid var(--brand-100);
                                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(65%) sepia(6%) saturate(120%) hue-rotate(180deg) brightness(95%) contrast(85%) !important;
                                        }

                                        .label {
                                            color: var(--color-text-400) !important;
                                        }
                                    }
                                }
                            }

                            &.btn-edit {
                                background-color: var(--color-text-50);
                                box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;

                                .inner-cont {

                                    .icon {
                                        filter: invert(16%) sepia(8%) saturate(300%) hue-rotate(190deg) brightness(90%) contrast(95%) !important;
                                    }

                                    .label {
                                        color: var(--color-text-700) !important;
                                    }
                                }

                                &:hover {
                                    background-color: var(--brand-50) !important;

                                    .inner-cont {
                                        .icon {
                                            filter: invert(12%) sepia(10%) saturate(500%) hue-rotate(200deg) brightness(90%) contrast(100%) !important;
                                            mix-blend-mode: difference;
                                        }

                                        .label {
                                            color: var(--color-text-800) !important;
                                            background-color: transparent;
                                        }
                                    }
                                }

                                &:focus {
                                    background-color: var(--white);
                                    box-shadow: 0px -2px 0px 0px #0A0D120D inset, 0px 0px 0px 1px #0A0D122E inset, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #837BB2;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(25%) sepia(6%) saturate(300%) hue-rotate(180deg) brightness(90%) contrast(95%) !important;
                                            mix-blend-mode: luminosity;
                                        }

                                        .label {
                                            color: var(--brand-700);
                                        }
                                    }
                                }

                                &.disabled {
                                    background-color: var(--color-text-100) !important;
                                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                    opacity: 1 !important;
                                    outline: none;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(77%) sepia(6%) saturate(150%) hue-rotate(180deg) brightness(90%) contrast(90%) !important;
                                            mix-blend-mode: luminosity;
                                            opacity: 0.5;
                                        }

                                        .label {
                                            color: var(--color-text-400) !important;
                                            filter: none !important;
                                        }
                                    }
                                }
                            }

                            &.btn-archive {
                                background-color: var(--color-error-25);
                                box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;

                                .inner-cont {

                                    .icon {
                                        filter: invert(57%) sepia(36%) saturate(320%) hue-rotate(330deg) brightness(90%) contrast(85%) !important;
                                    }

                                    .label {
                                        color: var(--color-error-700) !important;
                                    }
                                }

                                &:hover {
                                    background-color: var(--color-error-50) !important;

                                    .inner-cont {

                                        .label {
                                            color: var(--color-error-700) !important;
                                            background-color: transparent;
                                        }
                                    }
                                }

                                &:focus {
                                    background-color: var(--white);
                                    box-shadow: 0px -2px 0px 0px #0A0D120D inset, 0px 0px 0px 1px #0A0D122E inset, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #837BB2;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(57%) sepia(36%) saturate(320%) hue-rotate(330deg) brightness(90%) contrast(85%) !important;
                                        }

                                        .label {
                                            color: var(--color-error-700);
                                        }
                                    }
                                }

                                &.disabled {
                                    background-color: var(--color-text-100) !important;
                                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                    opacity: 1 !important;
                                    outline: none;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(57%) sepia(36%) saturate(320%) hue-rotate(330deg) brightness(90%) contrast(85%) !important;
                                            opacity: 0.5;
                                        }

                                        .label {
                                            color: var(--color-error-400) !important;
                                            filter: none !important;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            &:has(.dsl-field-embed) {}
        }
    }
}