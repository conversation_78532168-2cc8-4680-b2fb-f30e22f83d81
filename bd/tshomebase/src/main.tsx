import "./main.less";
import "react-toastify/dist/ReactToastify.css";
import "./dsl-form/validators";
import "./dsl-form/buss";
import $ from "jquery";
import "@dsl-form/tab-controller";
import "@dsl-form/subform-handler";

import type { FC } from "react";
import React, { useEffect, useLayoutEffect, useState } from "react";
import ReactDOM from "react-dom/client";
import { useLocalStorage } from "usehooks-ts";
import Modal from "react-modal";
import { ThemeProvider } from "@mui/material/styles";

import { TabContextProvider } from "@contexts/history/provider";
import { PreferenceContextProvider } from "@contexts/preference/provider";
import { SharedDataContextProvider } from "@contexts/shared-data/provider";

import modules from "@modules/modules";
import { staticModules } from "@modules/const";

import { getCurrentLocation, useNavigation } from "@core/navigation";
import { NavigationProvider } from "@core/navigation/provider";
import { loadConfigs } from "@core/config/config-loader";
import { initiateRXRegistry } from "@core/rx-registry";
import { bindRXFunctions } from "@core/rx-functions";
import { baseTheme } from "@core/theme";
import NavIcon from "@public/icons/nav";
import { useUpdateHash, useWindowsClass } from "@hooks/index";
import { ViewMode, LocalStore } from "@enum/index";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import { ModuleItem, RemoteModuleItem } from "@typedefs/shared";
import { ToastContainer } from "react-toastify";
import Navbar from "@components/navbar/navbar";
import PersistentWindows from "@blocks/persistent-windows/persistent-windows";
import { useActiveWindows } from "@blocks/persistent-windows/store";
import { openSiteSelector } from "@utils/dsl-fx";

if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
	if (!window.$) {
		window.$ = $;
	}
}
window.global = window;
window.React = React;
window.ReactDOM = ReactDOM;

loadConfigs();

const AppContent: FC<Partial<RoutedComponentProps>> = (props) => {
	useWindowsClass();
	const [currentNavItem] = useLocalStorage(LocalStore.CURRENT_NAV_ITEM, "");
	const hasTabs = useActiveWindows();
	const [vmode] = useLocalStorage(LocalStore.VIEW_MODE, ViewMode.NORMAL);
	const nav = useNavigation(props as RoutedComponentProps, "/");
	const [mods, setMods] = useState<ModuleItem[]>([]);
	const [siteSelectorHash, updateSiteSelectorHash] = useUpdateHash();

	window.App.reactNav = nav;

	const [active, setActive] = useState<string | null>(null);

	const onNavChange = (key: string): void => {
		setActive((k) => key);
		window.App.reactNav = nav;
	};

	useLayoutEffect(() => {
		let modules = (window.MODULES || [])
			.map((module: RemoteModuleItem) => {
				if (module.active != "Yes") {
					return;
				}
				const icon = NavIcon[module.code] || NavIcon.settings;
				return {
					label: module.name,
					key: module.code,
					code: module.code,
					icon: icon,
					activeIcon: NavIcon[`${module.code}Active` as keyof typeof NavIcon] || icon,
					path: module.path?.length > 0 ? module.path : "",
					placement: module.nav_placement,
				};
			})
			.filter(Boolean) as ModuleItem[];
		modules.sort((a, b) => a.placement.localeCompare(b.placement));
		modules.sort((a, b) => b.sort_order - a.sort_order);
		modules = modules.filter((m) => {
			const allowedPaths = ["/settings", "/my"];
			const hasAccess = window.Auth.can_access_path(m?.path);
			if (hasAccess || allowedPaths.includes(m?.path)) {
				return m;
			}
		});
		const top = modules.filter((m) => m?.placement === "top");
		setMods(modules);
		if (!nav.globalNav.route || nav.globalNav.route == "/") {
			const cnt = modules.findIndex((m) => {
				m.key == currentNavItem;
			});
			if (cnt > -1) {
				nav.globalNav.route = `/${currentNavItem}/`;
				onNavChange(currentNavItem);
			} else {
				if (top.length > 0) {
					onNavChange(top[0]?.key);
					nav.globalNav.route = `/${top[0].key}/`;
				}
			}
		}
	}, []);

	if (!nav.globalNav.route) {
		nav.globalNav.route = getCurrentLocation();
	}

	return (
		<>
			<div className="min-h-screen bg-gray-100">
				<Navbar
					modules={mods}
					activeModule={active}
					onNavChange={onNavChange}
					nav={nav}
					handleSiteSelectorClick={() =>
						openSiteSelector(
							(siteIds) => {
								updateSiteSelectorHash();
								console.log("Site Changed IDs: ", siteIds);
							},
							false,
							false
						)
					}
				/>
			</div>
			<div key={siteSelectorHash} className={"app " + vmode + (hasTabs ? " has-tabs" : "")}>
				<div className={`module-area`}>
					{([...mods, ...staticModules] as ModuleItem[]).map((cm) => {
						const isActive = active == cm.key;
						const Module = modules[cm.key];
						return (
							<div
								className="module-container"
								data-module-id={`${cm.key}`}
								style={isActive ? undefined : { display: "none" }}
								key={cm.key}
							>
								{Module ? (
									<Module
										{...nav}
										lrct={nav.globalNav.lrct}
										setActiveTab={onNavChange}
										navToURL={nav.globalNav.route}
										isActive={isActive}
										isParentActive
										{...cm}
									/>
								) : (
									<div>Invalid Route</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
			<PersistentWindows />
			<div id="flyout" />
			<ToastContainer stacked position="bottom-center" theme="dark" />
		</>
	);
};

export const AppView = () => (
	<NavigationProvider>
		<SharedDataContextProvider>
			<PreferenceContextProvider>
				<TabContextProvider>
					<ThemeProvider theme={baseTheme}>
						<AppContent isActive={true} isParentActive={true} />
					</ThemeProvider>
				</TabContextProvider>
			</PreferenceContextProvider>
		</SharedDataContextProvider>
	</NavigationProvider>
);

initiateRXRegistry();
bindRXFunctions();

Modal.setAppElement("#application");
