@import (reference) "../../less/style/main.less";

.suggestion-container {
  .search-tab-statuses-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin: 5px;

    .nav-box {
      display: flex;
      flex-direction: row;
      gap: 5px;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .result-count {
        .table-status-count;
      }

      .result-count {
        background-color: @semi-transparent-light-gray-08;
      }

      &.active {
        .result-count {
          background-color: @black;

          span {
            color: @white;
          }
        }
      }

      p {
        .table-status-text;
      }
    }
  }
}