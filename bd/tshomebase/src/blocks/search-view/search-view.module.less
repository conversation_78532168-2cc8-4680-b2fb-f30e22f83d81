@import (reference) "./search-view.responsive.less";
@import (reference) "../../less/style/main.less";

@focus-border-color: red;
@animation-duration: 0.3s;
@button-load-animation-duration: 0.32s;
@button-unload-animation-duration: 0.25s;

.active {
  width: 460px !important;
  transition: width @animation-duration ease-in-out;
}

.inner {
  display: inline-flex;
  height: 100%;
}

.searchBoxMainContainer {
  position: absolute;
  border: none;
  z-index: 5;
  background-color: transparent;
  top: var(--top-right-icons-tmp1-pt);
  right: 70px;
  min-height: 32px;
  z-index: 5;
  height: 40px;
  width: 40px;
  transition: width @animation-duration ease-in-out;

  &:focus-within {
    .inner {
      height: 100%;
    }
  }

  .searchBarContainer {
    display: none;
    display: flex;
    background-color: transparent;
    width: 100%;
    height: 100%;

    .searchVarInput {
      background-color: white;
      border-radius: 50px;
      position: absolute;
      border: none;
      margin-right: 10px;
      color: @dark-black;
      height: 100%;
      transition: width @animation-duration ease-in-out;
      width: 100%;
      height: 100%;
      padding-left: 10%;

      &:focus {
        border: none;
        outline: none;
      }
    }

    .searchBarButton {
      background-color: white;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      margin: 0;
      position: absolute;
      height: 40px;
      width: 40px;

      &:focus {
        outline: none;
        border: none;
      }

      .searchIcon {
        width: 24px;
        height: 23px;
      }
    }

    @media (max-width: 768px) {
      top: -39.5px;
    }
  }
}