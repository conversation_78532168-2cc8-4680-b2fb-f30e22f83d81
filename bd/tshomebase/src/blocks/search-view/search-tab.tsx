import React, { useEffect, useState } from "react";

import './search-tab.less';

// Define the type for the result object
type ResultObject = Record<string, unknown[]>;

// Define the type for the props
export type SearchSuggestionTabProps = {
    results: ResultObject;
    filter: string;
    handleFilterItems: (key: string) => void;
};

export const SearchSuggestionTab: React.FC<SearchSuggestionTabProps> = ({
    results,
    filter,
    handleFilterItems,
}) => {
    // Define state for menu items
    const [menuItems, setMenuItems] = useState<ResultObject>({
        Patient: [],
        Inventory: [],
        Physician: [],
        Sales: [],
    });

    // Update menu items when results change
    useEffect(() => {
        setMenuItems(prevMenuItems => {
            const updatedMenuItems: ResultObject = {};

            // Update the counts for tabs with data and reset counts for tabs without data
            Object.keys(prevMenuItems).forEach(tab => {
                updatedMenuItems[tab] = results[tab]?.length ? results[tab] : [];
            });

            return updatedMenuItems;
        });
    }, [results]);

    return (
        <div className="search-tab-statuses-container">
            {/* Render menu items */}
            {Object.keys(menuItems).map((tab, index) => (
                <div
                    className={`nav-box ${tab === filter ? 'active' : ''}`}
                    key={`${tab}-index`}
                    onClick={() => handleFilterItems(tab)}
                >
                    <div className="result-count">
                        <span>{menuItems[tab].length}</span>
                    </div>
                    <p className={`${tab === filter ? 'active-tab' : ''}`}>{tab}</p>
                </div>
            ))}
        </div>
    );
};
