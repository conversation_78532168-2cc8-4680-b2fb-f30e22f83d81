import React, { useState, useRef, useEffect, useContext, SyntheticEvent } from "react";
import * as styles from "./search-view.module.less";
import SearchResults from "@components/global-search/search-results";
import { PreferenceSettingContext } from "@contexts/preference/provider";
import PublicIcon from "@public/icons";
import { UseNavigationProps } from "@core/navigation";
import { TabHistory } from "@contexts/history/provider";

// Props interface for the component
interface SearchBarProps {
	nav: Partial<UseNavigationProps>;
}
const SearchBarView: React.FC<SearchBarProps> = ({ nav }) => {
	const [pref] = useContext(PreferenceSettingContext);
	const [searchTerm, setSearchTerm] = useState<string>("");
	const [suggestion, setSuggestion] = useState<boolean>(false);
	const [animationComplete, setAnimationComplete] = useState(false);
	const inputRef = useRef<HTMLInputElement | null>(null);
	const parentRef = useRef<HTMLDivElement | null>(null);
	const [activeSearch, setActiveSearch] = useState<boolean>(false);

	// Handle keyboard events using the window's keydown event
	const handleKeyDown = (e: KeyboardEvent) => {
		if ((pref.search_shortcut_key && e.key === pref.search_shortcut_key) || e.key === "F1") {
			if (inputRef.current) {
				inputRef.current.focus();
				setActiveSearch(true);
				setSuggestion(true);
			}
		} else if (e.key === "Escape") {
			setActiveSearch(false);
			setSearchTerm("");
			setSuggestion(false);
		}
	};

	useEffect(() => {
		// Reset animation state when activeSearch is turned off
		if (!activeSearch) {
			setAnimationComplete(false);
		}
	}, [activeSearch]);
	useEffect(() => {
		const handleTransitionEnd = () => {
			setAnimationComplete(true);
		};

		const parentElement = parentRef.current;
		if (parentElement) {
			parentElement.addEventListener('transitionend', handleTransitionEnd);
		}

		return () => {
			if (parentElement) {
				parentElement.removeEventListener('transitionend', handleTransitionEnd);
			}
		};
	}, []);

	useEffect(() => {
		// Add keydown event listener on mount
		window.addEventListener("keydown", handleKeyDown);
		return () => {
			// Clean up keydown event listener on unmount
			window.removeEventListener("keydown", handleKeyDown);
		};
	}, [pref.search_shortcut_key]);

	// Handle clicks inside and outside the search bar
	const handleClick = (e: MouseEvent) => {
		const target = e.target as Node;
		if (parentRef.current && !parentRef.current.contains(target)) {
			setActiveSearch(false);
			setSearchTerm("");
			setSuggestion(false);
		}
	};

	useEffect(() => {
		document.addEventListener("click", handleClick);
		return () => {
			document.removeEventListener("click", handleClick);
		};
	}, []);

	// Function to handle suggestion clicks
	const handleSuggestionClick = (url: string) => {
		nav.goTo?.(url);
		setActiveSearch(false);
		setSearchTerm("");
		setSuggestion(false);
		inputRef.current?.blur();
	};

	return (
		<div
			className={`${styles.searchBoxMainContainer} ${activeSearch ? styles.active : ''}`}
			onClick={() => {
				inputRef.current?.focus();
			}} // Focus input when clicking on the container
			ref={parentRef}
			tabIndex={0} // Make the div focusable for accessibility
		>
			<div className={styles.inner}>
				<div className={styles.searchBarContainer}>
					<input
						className={styles.searchVarInput}
						type="text"
						placeholder="Search"
						value={searchTerm}
						onChange={(e) => {
							setSearchTerm(e.target.value);
							setSuggestion(true);
						}}
						onFocus={() => setSuggestion(true)}
						ref={inputRef}
					/>
					<button className={styles.searchBarButton} onClick={() => setActiveSearch(true)}>
						<img className={styles.searchIcon} src={PublicIcon.common.searchOutlinedIcon} alt="Search" />
					</button>
				</div>
				{suggestion && animationComplete && (
					<div style={{
						display: animationComplete ? "block" : "none",
					}}>
						<SearchResults
							clickHandler={handleSuggestionClick}
							blurHandler={() => {
								setActiveSearch(false);
								setSearchTerm("");
								setSuggestion(false);
								setAnimationComplete(false);
								inputRef.current?.blur();
							}}
							keyword={searchTerm.trim()}
							tabHistory={nav.tabHistory as TabHistory}
						/>
					</div>
				)}
			</div>
		</div>
	);
};

export default React.memo(SearchBarView);
