import type { FC } from "react";
import React, { useEffect, useRef, useState } from "react";
import type {
	DSLCardOnArchiveCallBack,
	DSLCardOnChangeModeCallback,
	DSLCardOnSaveCallBack,
	DSLCardOnUnArchiveCallBack,
	DSLCardViewRef,
	TabModes,
} from "../dsl-card-view/dsl-card-view";
import DSLCardView, { CARD_MODES } from "../dsl-card-view/dsl-card-view";
import "./dsl-tab-view.less";
import type { TabData } from "../tab-list/tab-list";
import { TabList } from "../tab-list/tab-list";
import { uuid, capitalizeFirstCharacter, joinPaths } from "@utils/fx";
import { useNavigation, UseNavigationProps } from "@core/navigation";
import { getAutoName, getNewMode } from "@utils/dsl-fx";

import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import type { DSLListViewProps } from "../dsl-list-view/dsl-list-view";
import { DSLListView } from "../dsl-list-view/dsl-list-view";
import _ from "lodash";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";
import WizardView from "../wizard/wizard";
import { useFormConfig } from "@dsl-form/config";
import { DSLCloseTabCallback, IFormData, OpenTabCallback } from "@hooks/index";
import { TabLevelProps } from "@blocks/nested-tab-view";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

export interface DSLTabViewProps extends DSLListViewProps {
	form: string;
	styles?: TabLevelProps["style"];
	linkMap?: DSLDrawLinkMap;
	customAutoName?: (event: TabData) => string;
	viewMode?: string;
	CustomListView?: FC<any>;
	customListViewProps?: Record<string, unknown>;
	tabRenderComponents?: Record<string, FC<any>>;
	filtersPresetFixed?: Record<string, unknown>;
	newPreset?: Record<string, unknown>;
	newFormOverride?: string;
	tabLabel?: string;
	newTabLabel?: string;
	multiMode?: "id" | "id_form" | "id_form_mode" | null;
	defaultReadMode?: "read" | "snap";
	noAddNew?: boolean;
	ops?: {
		enableHistory?: boolean;
		onTabOpen?: (path: string) => void;
		onTabClose?: (id: string) => void;
	};
	routePrepend?: string;
	initialAsAvatar?: boolean;
	DSLTabViewProps?: boolean;
	overrides?: {
		rowClicked?: (event: TabData, actions: TabViewActions) => void;
		onSaved?: (fd: Record<string, unknown>, tab: TabData, ref: unknown, actions: TabViewActions) => void;
		onNavChange?: (nav: string[], actions: TabViewActions, props: DSLTabViewProps) => void;
	};
}

export interface TabViewActions {
	openTab: OpenTabCallback;
	closeTab: DSLCloseTabCallback;
	openNewTab: (id: string, _form?: string) => void;
	changeMode: DSLCardOnChangeModeCallback;
	onSaved?: DSLCardOnSaveCallBack;
	setActive: (tkey: string) => void;
	getOpenTabs?: () => TabData[];
	overrideOpenTabs?: (tabs: TabData[]) => void;
}

export const DSLTabView: FC<DSLTabViewProps> = (props) => {
	const {
		form,
		styles,
		linkMap,
		viewMode,
		CustomListView,
		newTabLabel,
		tabLabel,
		customListViewProps,
		multiMode,
		ops = null,
		customAutoName,
	} = props;
	let { tabRenderComponents } = props;
	const MODES = ["edit", "read", "snap", "add", "addfill"];

	const { enableHistory, onTabClose } = ops || {};

	const nav = useNavigation(props as UseNavigationProps, joinPaths(props?.routePrepend || "", "/"), ops);
	const formConfig = useFormConfig(form);
	const tabRenderTypes = Object.keys(tabRenderComponents || {});
	const listViewRef = useRef<AdvancedGridRef | null>(null);
	MODES.push(...tabRenderTypes);
	if (viewMode == "snap" && tabRenderTypes.length == 0) {
		console.error("looks like you  forgot to pass tabRenderComponents with viewMode set to snap");
		return null;
	}

	tabRenderComponents = { ...DSLTabView.defaultProps?.tabRenderComponents, ...tabRenderComponents };
	if (!form) {
		return null;
	}
	if (!window.DSL[form]) {
		return null;
	}

	useEffect(() => {
		if (!props.navPath?.length) {
			return;
		}
		if (props.overrides?.onNavChange) {
			props.overrides.onNavChange(props.navPath, tabViewActions, props as any);
			return;
		}
		const r = props.navPath;
		let id = r.splice(0, 1).toString();
		const mode = r.splice(0, 1).toString();
		if (["add", "addfill"].includes(mode)) {
			if (!id.includes("-")) {
				id = "new-entry";
			}
			openNewTab(id);
			return;
		} else if (!mode) {
			setActive("options");
			return;
		} else if (id > "0") {
			const autoName = getAutoName(form, id);
			openTab(id, autoName, mode, form, {});
		}
	}, [nav.globalNav.lrct]);

	const [openTabs, setOpenTabs] = useState<TabData[]>([]);

	const [active, setActive] = useState("options");

	const dslRefs = useRef({}) as any as Record<string, DSLCardViewRef | null>;

	const openTab: OpenTabCallback = (id, label, mode, _form, componentProps, renderComponent, extraKV) => {
		id = id + "";
		let cform = _form;
		if (!cform) {
			cform = form;
		}
		const tkey = tabKey({ id, form: cform, mode } as TabData);

		setOpenTabs((p) => {
			p = Array.from(p);
			const tindex = p.findIndex((t) => t.tkey == tkey);
			if (tindex > -1) {
				let forceRender = false;
				if (p[tindex].id == id && p[tindex].form == cform && p[tindex].mode != mode) {
					forceRender = true;
				}
				p[tindex].tkey = tkey;
				p[tindex].mode = mode;
				p[tindex].componentProps = { ...componentProps, ...extraKV };
				p[tindex].form = cform;
				p[tindex].label = mutateLabel(label, mode);
				p[tindex].gridRef = listViewRef.current;
				if (forceRender) {
					p[tindex].forceRenderId = window.generateUUID();
				}
				return p;
			}
			p = [
				...p,
				{
					tkey,
					id: id,
					label: mutateLabel(label, mode),
					mode: mode,
					componentProps: { ...componentProps, ...extraKV },
					form: cform,
					gridRef: listViewRef.current,
				},
			];
			return p;
		});
		setActive(tkey);
	};

	const openNewTab = (id?: string, _form?: string): void => {
		if (!id) {
			id = uuid();
		}
		const cform = _form || form;
		getNewMode(cform)
			.then((mode: unknown) => {
				const m = mode as string;
				const ntLabel = newTabLabel ? newTabLabel : window.DSL[cform].view.label;
				openTab(id, "New " + ntLabel, m, cform, {});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	const getOpenTabs = () => {
		return openTabs;
	};

	const overrideOpenTabs = (tabs: TabData[]) => {
		setOpenTabs((p) => tabs);
	};

	const onTabCancel = (tab: TabData) => {
		if (dslRefs[tab.tkey]?.tab_do && CARD_MODES.includes(tab.mode)) {
			dslRefs[tab.tkey]?.tab_do("cancel");
		} else {
			closeTab(tab.tkey, tab);
		}
	};

	const closeTab: DSLCloseTabCallback = (id, tab, ref) => {
		//coffee Will call this function automatically
		delete dslRefs[tab.tkey];
		setOpenTabs((p) => {
			p = Array.from(p);
			p = p.filter((t) => t.tkey != tab.tkey);
			return p;
		});
		if (enableHistory && onTabClose) {
			onTabClose(id || tab.id);
		}
		setActive("options");
	};

	const tabKey = (tab: TabData) => {
		let mode = tab.mode;
		if (["add", "addfill", "edit"].includes(tab.mode)) {
			mode = "form";
		}
		if (multiMode == "id_form_mode") {
			return `${tab.id}_${tab.form || form}_${tab.mode}`;
		}
		if (multiMode == "id_form") {
			return `${tab.id}_${tab.form || form}`;
		}
		return tab.id;
	};

	const onArchive: DSLCardOnArchiveCallBack = (fd, tab, ref) => {
		if (tab?.gridRef) {
			tab.gridRef.advanced.row("archive", fd);
		}
		closeTab(tab.tkey, tab);
	};

	const onUnArchive: DSLCardOnUnArchiveCallBack = (fd, tab, ref) => {
		if (tab.gridRef) {
			tab.gridRef.advanced.refresh();
		}
		openTab(tab.id, tab.label, "read", form, {});
	};

	const onSaved: DSLCardOnSaveCallBack = (fd, tab, ref) => {
		if (tab?.gridRef) {
			const cc = props?.customListViewProps?.customColumns;
			let cols: string[] | undefined = undefined;
			if (cc && Array.isArray(cc) && cc.length > 0) {
				cols = cc as string[];
			}

			tab.gridRef.advanced.row(tab.mode as any, fd.values);
		}

		if (props.overrides?.onSaved) {
			props.overrides.onSaved(fd, tab, ref, tabViewActions);
			return;
		}

		if (!["add", "addfill"].includes(tab.mode)) {
			setOpenTabs((p: TabData[]) => {
				p = Array.from(p);
				const ci = p.findIndex((t) => t.tkey == tab.tkey);
				if (ci > -1) {
					p[ci] = { ...tab, label: mutateLabel(fd.values.auto_name, tab.mode) };
				}
				return _.uniqBy(p, "tkey");
			});
			return;
		}
		let nmode = ref?.["card" + ref?.card].mode;
		if (props.defaultReadMode) {
			nmode = props.defaultReadMode;
		}
		const ntkey = tabKey({ ...tab, id: fd.record, mode: nmode });
		setOpenTabs((p: TabData[]) => {
			if (ref?.xid.toString().includes("-")) {
				// Add/Addfill Delete Temporary Ref
				delete dslRefs[tab.tkey];
			}
			if (tab.form == ref?.["card" + ref?.card].form) {
				dslRefs[ntkey] = ref as DSLCardViewRef;
			}
			p = Array.from(p);
			const ci = p.findIndex((t) => t.tkey == tab.tkey);
			if (ci > -1) {
				p[ci] = {
					...tab,
					tkey: ntkey,
					id: fd.record,
					label: mutateLabel(fd.values.auto_name, nmode),
					mode: nmode,
				};
			}

			return _.uniqBy(p, "tkey");
		});
		setActive(ntkey);
	};

	const rowClicked = (event: TabData) => {
		if (props.defaultReadMode) {
			event.mode = props.defaultReadMode;
		}
		if (props.overrides?.rowClicked) {
			props.overrides.rowClicked(event, tabViewActions);
			return;
		}
		if (customAutoName) {
			event.label = customAutoName(event) || event.label;
		}
		if (event.type == "dblclick") {
			openTab(event.id, event.label, event.mode, form, { gridRef: event.gridRef });
			// openInNewWindow(event.id, event.mode);
		} else {
			openTab(event.id, event.label, event.mode, form, { gridRef: event.gridRef });
		}
	};

	const openInNewWindow = (id: string, mode: string) => false;

	const mutateLabel = (label: string, mode: string) => {
		label = (label || "").trim();
		if (multiMode != "id_form_mode") {
			return label;
		}
		if (label.startsWith("New")) {
			return label;
		}
		const md = ` | ${capitalizeFirstCharacter(mode)}`;
		const i = label.lastIndexOf("|");
		if (i > -1) {
			label = label.substr(0, i);
		}
		return label + md;
	};

	const onDropOutside = (dragEvent: any) => {
		const id = dragEvent.draggableId;
		if (!id) {
			return;
		}
		const ci = openTabs.find((t) => t.id == id);
		if (!ci?.id) {
			return;
		}
		if (openInNewWindow(ci.id, ci.mode)) {
			closeTab(ci.id, ci);
		}
	};

	const changeMode: DSLCardOnChangeModeCallback = (mode: string, id, tab, ref) => {
		if (["add", "addfill"].includes(tab.mode)) {
			return;
		}
		if (mode == "list") {
			closeTab(id, tab, ref);
			if (id) {
				mode = "read";
			}
		}
		if (mode == "read" && props.defaultReadMode) {
			mode = props.defaultReadMode;
		}
		const label = getAutoName(tab.form || form, id);
		const ntkey = tabKey({ ...tab, mode, id });
		setOpenTabs((p: TabData[]) => {
			delete dslRefs[tab.tkey];
			if (ref) dslRefs[ntkey] = ref;
			p = Array.from(p);
			const ci = p.findIndex((t) => t.tkey == tab.tkey);
			if (ci > -1) {
				p[ci] = { ...tab, tkey: ntkey, id, label: mutateLabel(label || tab.label, mode), mode: mode };
			}
			return _.uniqBy(p, "tkey");
		});
		setActive(ntkey);
	};
	const tabViewActions = {
		openTab,
		closeTab,
		openNewTab,
		changeMode,
		onSaved,
		setActive,
		getOpenTabs,
		overrideOpenTabs,
	} as any;

	const rxOpts = {
		rowClicked: rowClicked,
		onSaved: onSaved,
		onCancel: closeTab,
		changeMode,
		onArchive,
		onUnArchive,
		...nav,
	};
	const childProps = _.omit(props, "routePrepend");

	const detailCellRendererParams = {
		...props.detailCellRendererParams,
		tabViewActions,
		parentProps: { ...props, tabViewActions },
	};
	return (
		<div className="dsl-tab-view-container">
			<TabList
				activeTabId={active}
				openTabs={openTabs}
				initialAsAvatar={props.initialAsAvatar}
				openNewTab={() => {
					if (props.newFormOverride && window.DSL[props.newFormOverride]) {
						openNewTab(undefined, props.newFormOverride);
					} else {
						openNewTab();
					}
				}}
				onDragEnded={(dragEvent, updateList) => {
					setOpenTabs((tabs) => updateList(tabs));
				}}
				onDropOutside={onDropOutside}
				styles={styles || {}}
				optionsProps={{
					label: tabLabel ? tabLabel : window.DSL[form].view.label,
					enabled: true,
				}}
				addProps={{
					label: "+",
					enabled: (() => {
						if (props.noAddNew) {
							return false;
						}
						return window.Auth.can_create(form) && formConfig.allowAdd;
					})(),
				}}
				tabCanClose={true}
				draggable={true}
				onTabClick={(tab: TabData) => {
					setActive(tab.tkey);
				}}
				onTabClose={onTabCancel}
			/>
			<div className="dsl-container" style={{ display: active == "options" ? undefined : "none" }}>
				{!CustomListView ? (
					<DSLListView
						key="options"
						form={form}
						{...rxOpts}
						linkMap={linkMap}
						isActive={active == "options"}
						customColumnDefs={props.customColumnDefs}
						isParentActive={props.isActive}
						initialFilters={props.initialFilters}
						filtersPresetFixed={props.filtersPresetFixed || {}}
						detailCellRendererParams={{ ...detailCellRendererParams, form: form }}
						refCallback={(ref) => {
							listViewRef.current = ref;
						}}
					/>
				) : (
					<CustomListView
						key="options"
						form={form}
						{...rxOpts}
						linkMap={linkMap}
						isActive={active == "options"}
						isParentActive={props.isActive}
						tabViewActions={tabViewActions}
						filtersPresetFixed={props.filtersPresetFixed || {}}
						refCallback={(ref: AdvancedGridRef | null) => {
							listViewRef.current = ref;
						}}
						{...customListViewProps}
						customColumnDefs={props.customColumnDefs}
						initialFilters={props.initialFilters}
						detailCellRendererParams={{ ...detailCellRendererParams, form: form }}
					/>
				)}
			</div>
			{openTabs.map((tab) => {
				const tkey = tab.tkey;
				const renderKey = tkey + (tab.forceRenderId || "");
				let ModeRenderComponent = tabRenderComponents[tab.mode] as FC<any>;
				if (formConfig.renderComponents?.[tab.mode]) {
					ModeRenderComponent = formConfig.renderComponents[tab.mode];
				}
				return (
					<div
						key={renderKey}
						className="dsl-container"
						style={{ display: active == tkey ? undefined : "none" }}
					>
						<CRErrorBoundary>
							{("add" == tab.mode || "addfill" == tab.mode) && !ModeRenderComponent ? (
								<DSLCardView
									{...linkMap}
									{...rxOpts}
									key={renderKey}
									preset={(props.newPreset || {}) as IFormData}
									{...(tab.componentProps || {})}
									tabData={tab}
									isActive={active == tkey}
									isParentActive={props.isActive}
									xid={tab.id}
									card={tab.mode}
									form={tab.form || form}
									ddRef={(ref) => {
										ref.xid = tab.id;
										dslRefs[tkey] = ref;
									}}
									tabViewActions={tabViewActions}
								/>
							) : tabRenderComponents?.always ? (
								<tabRenderComponents.always
									{...childProps}
									{...linkMap}
									{...rxOpts}
									isActive={active == tkey}
									isParentActive={props.isActive}
									key={renderKey}
									tabData={tab}
									card={tab.mode}
									xid={tab.id}
									id={tab.id}
									form={tab.form || form}
									record={tab.id}
									ddRef={(ref: DSLCardViewRef) => {
										ref.xid = tab.id;
										dslRefs[tkey] = ref;
									}}
									linkMap={linkMap}
									tabViewActions={tabViewActions}
									customColumnDefs={props.customColumnDefs}
								/>
							) : (
								<ModeRenderComponent
									{...childProps}
									{...linkMap}
									{...rxOpts}
									isActive={active == tkey}
									isParentActive={props.isActive}
									key={renderKey}
									tabData={tab}
									card={tab.mode as TabModes}
									xid={tab.id}
									id={tab.id as string}
									form={tab.form || form}
									record={tab.id}
									ddRef={(ref: DSLCardViewRef) => {
										ref.xid = tab.id;
										dslRefs[tkey] = ref;
									}}
									linkMap={linkMap}
									tabViewActions={tabViewActions}
									{...(tab.componentProps || {})}
									customColumnDefs={props.customColumnDefs}
								/>
							)}
						</CRErrorBoundary>
					</div>
				);
			})}
		</div>
	);
};

DSLTabView.defaultProps = {
	tabRenderComponents: {
		edit: DSLCardView,
		read: DSLCardView,
		wizard: WizardView,
	},
	multiMode: "id",
	initialAsAvatar: false,
};
