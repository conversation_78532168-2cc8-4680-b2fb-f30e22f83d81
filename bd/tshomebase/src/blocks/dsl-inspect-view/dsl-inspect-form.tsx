import { FC, useEffect, useRef, useState } from "react";
import { GenericInspectComponentProps } from "./dsl-inspect-view";
import './dsl-inspect-form.less';
import DSLCardView, { DSLCardOnChangeModeCallback, DSLCardOnSaveCallBack, DSLCardViewRef, TabModes } from "../dsl-card-view/dsl-card-view";
import { TabData } from "../tab-list/tab-list";

export const DSLFormInspectView: FC<GenericInspectComponentProps> = (props) => {
    const tabData = props.tabData;
    const form = props.form || tabData.form || "";
    const dslRef = useRef<DSLCardViewRef | null>(null);
    const containerRef = useRef(null);
    const [mode, setMode] = useState<TabModes>(props.mode as TabModes || "read");
    const [Id, setID] = useState<string>((tabData.rowData?.id || tabData.id || "") as string);

    const onSaved: DSLCardOnSaveCallBack = (fd, tab, ref) => {
        let nmode: TabModes = "read";
        if (ref) {
            let v = "card" + ref.card as "cardform" | "cardread";
            nmode = ref[v].mode;
        }
        setID(fd.record);
        setMode(nmode);
    };

    const changeMode: DSLCardOnChangeModeCallback = (mode, id, tab, ref) => {
        if (mode == "list" as TabModes) {
            setMode("read");
            return;
        }
        setMode(mode);
        setID(`${id}`);
    };

    const rxOpts = {
        onSaved: onSaved,
        changeMode,
    };

    if (!form || !Id) {
        return (
            <div className="wf-inspect-comp dsl-inspect-form-view dsl-fi-error">
                Invalid data: Unable to load.
            </div >
        );
    }
    return (
        <div className="wf-inspect-comp dsl-inspect-form-view" ref={containerRef}>

            <DSLCardView
                key={`card-${Id}`}
                {...props}
                isActive={true}
                isParentActive={true}
                disableRouting={true}
                card={mode}
                xid={Id}
                form={props.form}
                record={["add", "addfill"].includes(mode) ? undefined : Id}
                ddRef={(ref) => {
                    dslRef.current = ref;
                }}
                tabData={{
                    tkey: Id,
                    id: Id,
                    form: props.form,
                    mode: mode,
                } as TabData}
                overrideDos={
                    {
                        archive: {
                            enabled: false
                        },
                        unarchive: {
                            enabled: false
                        },
                        print: {
                            enabled: false
                        },
                    }
                }
                {...rxOpts}
            />
        </div >
    );
};