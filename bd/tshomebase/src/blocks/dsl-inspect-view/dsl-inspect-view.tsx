import type { FC } from "react";
import React, { forwardRef, useEffect, useImperativeHandle } from "react";
import type { TabData } from "@blocks/tab-list/tab-list";
import { TabList } from "@blocks/tab-list/tab-list";
import "./dsl-inspect-view.less";
import type { TabViewActions } from "../dsl-tab-view/dsl-tab-view";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";
import { useTabController } from "@hooks/tab-controller";

interface DSLInspectViewProps {
	form: string;
	TabComponent: FC<GenericInspectComponentProps>;
	tabViewActions?: TabViewActions;
	containerStyles?: React.CSSProperties;
	isOpen: (open: boolean) => void;
}

export interface GenericInspectComponentProps {
	tabData: TabData;
	form: string;
	parentProps: DSLInspectViewProps;
	rowData: any;
	[key: string]: unknown;
}

export interface InspectViewRef {
	openTabs: TabData[];
	activeTab: string;
	openTab: (id: string, label: string, data: { [key: string]: unknown }) => void;
	setActiveTab: (id: string) => void;
	closeTab: (tab: TabData) => void;
}

export const DSLInspectView = forwardRef<InspectViewRef, DSLInspectViewProps>((props, ref) => {
	const { form, containerStyles, TabComponent } = props;
	const [openTabs, activeTab, controller] = useTabController([], "empty");

	const openTab = (id: string, label: string, data: { [key: string]: unknown }) => {
		id = id + "";
		controller.setOpenTabs([
			{
				tkey: id,
				id,
				label,
				componentProps: {},
				renderComponent: undefined,
				rowData: data,
				form,
				mode: "inspect",
			},
		]);
		controller.setActiveTabId(id);
	};

	useImperativeHandle(
		ref,
		() => ({
			openTabs,
			activeTab,
			openTab,
			setActiveTab: controller.setActiveTabId,
			closeTab,
		}),
		[openTabs, activeTab]
	);

	useEffect(() => {
		props.isOpen(openTabs.length > 0);
	}, [openTabs]);

	const closeTab = (tab: TabData) => {
		controller.setOpenTabs((p: TabData[]) => {
			p = Array.from(p);
			p = p.filter((t) => t.id != tab.id);
			if (p.length) {
				controller.setActiveTabId(p[p.length - 1].id);
			} else {
				controller.setActiveTabId("empty");
			}
			return p;
		});
	};

	if (openTabs.length == 0) {
		return null;
	}
	const tab = openTabs[0];
	return (
		<div className="dsl-inspect-view" style={containerStyles}>
			<TabList
				activeTabId={activeTab}
				openTabs={openTabs}
				optionsProps={{
					enabled: false,
				}}
				addProps={{
					enabled: false,
				}}
				styles={{
					tabListStyle: "snap-main-tab-list",
				}}
				nullAutoName={true}
				tabCanClose={true}
				draggable={false}
				onTabClick={(tab) => {}}
				onTabClose={closeTab}
			/>
			<div className="inspect-tab-content">
				<div key={tab.tkey || tab.id || tab?.rowData?.tag} className={"inspect-tab-cont-cnt"}>
					{TabComponent ? (
						<CRErrorBoundary>
							<TabComponent parentProps={props} form={form} tabData={tab} rowData={tab.rowData || {}} />
						</CRErrorBoundary>
					) : null}
				</div>
			</div>
		</div>
	);
});
