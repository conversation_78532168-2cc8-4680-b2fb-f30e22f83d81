@import '../../modules/patient/snap-tab-list.less';
@import (reference) '../tab-list/tab-list.less';

.dsl-inspect-view {
    display: flex;
    flex-direction: column;
    min-height: fit-content;
    width: 100%;

    .no-selection-msg {
        display: flex;
        flex: 1;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        color: lightgray;
        font-weight: 600;
        margin-bottom: 44px;
    }

    .snap-main-tab-list {
        padding-left: 0px !important;
        .tab-list;
    }

    .inspect-tab-content {
        display: flex;
        flex: 1;
        width: 100%;
        flex-direction: column;
        max-height: fit-content;

        .inspect-tab-cont-cnt {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            border-radius: 0px 0px 8px 8px;
            flex: 1;
            width: 100%;
            overflow-y: auto;
            max-height: fit-content;
        }
    }
}