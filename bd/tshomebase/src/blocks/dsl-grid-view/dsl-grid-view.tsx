import type { SyntheticEvent } from "react";
import React, { useEffect, useRef } from "react";

import { getType, uuid } from "@utils/fx";
import "./dsl-grid-view.less";
import type { DSLDrawLinkMap, DSLField } from "@typedefs/coffee/dsl";
import { useOnScreen } from "@hooks/on-screen";
import { TabData } from "../tab-list/tab-list";
import { RoutedComponentProps } from "@typedefs/routed-component";
import { IFormData } from "@hooks/index";
import { RowClickedEvent } from "ag-grid-enterprise";

export interface IDSLDrawGridRef {
	print: Function;
	first_render: boolean;
	callback: Function;
	original_data_copy?: IFormData[];
	set_custom_columns: Function;
	set_custom_dsl_fields: Function;
	column_list: Function;
	customDraw: Function;
	init: Function;
	init_basic_filters: Function;
	params: Function;
	filter: Record<string, unknown>;
	param_link: Function;
	reload: Function;
	reload_all: Function;
	show_colstatus_change: Function;
	unload: Function;
	draw: Function;
	options: DSLGridViewProps;
	push_embed: (id: string | number) => void;
	table_grid: {
		buttons: (selector: string) => { trigger: Function };
		columns: { adjust: Function };
		draw: (dg?: boolean) => void;
		rows: (opts?: object) => any[];
		row: (index: number) => any;
	};
	$el: JQuery;
	trigger_row_changed_event: Function;
	make_row_html: GridMakeRowHTMLSuperFunc;
	dg: JQuery;
}

export type DSLGridOnRowEventCallBack = (row: TabData, event: RowClickedEvent<any>) => void;

export type DSLGridSetRefCallBack = (ref: DSLGridViewRef) => void;

export type DSLGridViewRef = {
	ddg: IDSLDrawGridRef | null;
	applyFilters: (filters: Record<string, unknown>) => void;
	refresh: () => void;
	doExport: (type: string) => void;
	doPrint: () => void;
};

type GridRowOpts = {
	preRenderedColumns: Record<string, string>;
	rowAttributes: {
		classes: string[];
	};
};
type GridMakeRowResponse = {
	fr: Record<string, string>;
	rowOpts: GridRowOpts;
};

type GridMakeRowHTMLSuperFunc = (
	dt: IFormData,
	selcols: string[],
	fields: Record<string, DSLField>,
	opts?: GridRowOpts
) => GridMakeRowResponse;

export type GirdMakeRowHTMLFunc = (
	dt: IFormData,
	selcols: string[],
	fields: Record<string, DSLField>,
	superFunc: GridMakeRowHTMLSuperFunc
) => GridMakeRowResponse;

type GridRowColumnEventData = {
	[key: string]: any;
	rowOpts: GridRowOpts;
	_meta: IFormData;
};

export type OnRowCountChangeEvent = (ddg: IDSLDrawGridRef) => void;

export type GridRowColumnEventFunc = (event: string, data: GridRowColumnEventData) => void;

export interface DSLGridViewProps extends Partial<DSLDrawLinkMap>, Partial<RoutedComponentProps> {
	form: string;
	fieldEmbed?: DSLField;
	embedValidateTransform?: () => void;
	onReadyGrid?: () => void;
	gridMakeRowHTML?: GirdMakeRowHTMLFunc;
	gridRowColumnEvent?: GridRowColumnEventFunc;
	onRowCountChangeEvent?: OnRowCountChangeEvent; // Implementation still pending
	compact?: boolean;
	customColumns?: string[];
	columnsWidth?: number[];
	columnsLabel?: (string | null)[];
	filtersPresetFixed?: Record<string, unknown>;
	ignoreFindFilter?: boolean;
	gridSourceOverrideURL?: string;
	initialFilters?: Record<string, unknown>;
	gridparams?: object;
	setRef: DSLGridSetRefCallBack;
	onRowEvent?: DSLGridOnRowEventCallBack;
	sortProperty?: string;
	sortDirection?: "asc" | "desc";
	// Field-Embed Options
	embedValues?: unknown[];
	rowSelectionMode?: "single" | "multi";
	rowSelection?: boolean;
	rankLevel?: "local" | "global" | "none";
	readOnly?: boolean;
	optimisticRender?: boolean;
}

const DSLGridTemplate = () => (
	<div className="repeaterwrap" id="test">
		<div className="col-lg-12 col-sm-12" style={{ display: "flex" }}>
			<table className="display repeater hover" width="100%" />
		</div>
	</div>
);

const DSLGridView = React.memo(
	(props: DSLGridViewProps) => {
		const { form, optimisticRender } = props;
		const ddg = useRef<IDSLDrawGridRef | null>(null);
		let clickTimeout = useRef<number | null>(null);
		const domUID = useRef(uuid());

		const [isOnScreen, elementRef] = useOnScreen(true);
		const isRendered = useRef(false);

		const applyFilters = (filters = {}) => {
			ddg?.current?.reload(filters);
		};

		const refresh = () => {
			ddg?.current?.reload?.(ddg.current?.filter || {});
		};

		const doExport = (type: string) => {
			if (["csv", "excel", "pdf", "copy"].includes(type)) {
				ddg.current?.table_grid.buttons(".buttons-" + type).trigger();
			}
		};

		useEffect(() => {
			if (!isOnScreen) {
				return;
			}
			ddg.current?.table_grid?.columns?.adjust?.();
		}, [isOnScreen]);

		const doPrint = () => {
			ddg.current?.print();
		};

		const parseRowClickEvent = (e: SyntheticEvent, data: any) => {
			const tr = $(e.target).closest("tr");
			let id = tr.find("u.id").attr("tr_id");
			let label = tr.find("u.id").attr("tr_auto_name");
			if (!id) {
				id = data?._meta?.id;
				label = data?._meta?.label;
			}
			const isArchived = data?._meta?.archived || data?.archived || false;
			if (!id) {
				console.error("parseRowClickEvent: Unable to Find Row Record Id");
				return;
			}
			let mode = "read";
			if (
				window.DSL[form].view.open == "edit" &&
				!isArchived &&
				window.Auth.can_update_any(form) &&
				!tr.hasClass("disable_editing") &&
				window.Auth.not_blocked_update(form)
			) {
				mode = "edit";
			}
			return {
				id,
				label,
				mode,
			};
		};

		const hightLightRow = (event: any, noSelection: boolean) => {
			const acs = "tr-select";
			const tr = $(event.target.closest("tr"));
			if (tr.hasClass(acs)) {
				tr.removeClass(acs);
			} else {
				$(event.target.closest("table"))
					.find("." + acs)
					.removeClass(acs);
				if (!noSelection) tr.addClass(acs);
			}
		};

		const overrideClickEvent = (event: any, data: Record<string, unknown>) => {
			const d = parseRowClickEvent(event, data);
			if (!d) {
				return;
			}

			if (getType(clickTimeout.current) == "number") {
				if (clickTimeout.current) clearTimeout(clickTimeout.current);
				clickTimeout.current = null;
				hightLightRow(event, true);
				props.onRowEvent?.({ ...d, type: "dblclick", form: form, rowData: data._meta || {} });
			} else {
				clickTimeout.current = setTimeout(() => {
					if (clickTimeout.current) clearTimeout(clickTimeout.current);
					clickTimeout.current = null;
					hightLightRow(event, false);
					props.onRowEvent?.({ ...d, type: "click", form: form, rowData: data._meta || {} });
				}, 300) as unknown as number;
			}
		};

		const init = () => {
			if (isRendered.current) {
				return;
			}

			if (!isOnScreen && !optimisticRender) {
				return;
			}

			const parent = {
				...props,
				overrideClickEvent,
			};

			ddg.current = new window.DSLDrawGrid({
				...props,
				viewid: domUID.current,
				id: `${domUID.current}`,
				el: $(`#${domUID.current}`).find(".repeaterwrap"),
				parent: parent,
			}) as IDSLDrawGridRef;
			ddg.current?.draw(false);
			ddg.current?.init();
			isRendered.current = true;
			props.setRef?.({
				ddg: ddg.current,
				applyFilters,
				refresh,
				doExport,
				doPrint,
			});
			return;
		};

		useEffect(() => {
			setTimeout(() => {
				init();
			}, 100);
		}, [isOnScreen, props.isActive]);

		useEffect(() => {
			return () => {
				ddg?.current?.unload?.();
				isRendered.current = false;
			};
		}, []);

		return (
			<div id={domUID.current} className="dsl-grid-view" ref={elementRef}>
				<DSLGridTemplate />
			</div>
		);
	},
	(prev, next) => {
		if (
			prev.form != next.form ||
			prev.links != next.links ||
			prev.link != next.link ||
			prev.linkid != next.linkid
		) {
			return false;
		}
		return true;
	}
);

export default DSLGridView;
