@import (reference) "../../less/style/main.less";

#application {
  .dsl-grid-view {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 10px);

    .sum-info {
      display: flex;
      flex-direction: row;
      gap: 10px;
      align-items: center;
      justify-content: center;
      padding: 20px;
      border-radius: @border-radius-6;
      background-color: @semi-transparent-light-gray-25-five;
      padding: 18px;
      display: flex;
      position: relative;
      bottom: 20px;

      .sum-label {
        font-weight: normal;
      }

      .sum-value {
        font-weight: bold;
      }
    }

    .dt-buttons {
      display: none !important;
    }

    .dataTables_info {
      display: flex;
      width: 100%;
      justify-content: center;
      height: auto;
      align-items: center;
      padding-top: 0px;
      position: absolute;
      bottom: -15px;
      .table-paginatin-text;
    }

    .dataTables_paginate {
      display: flex;
      height: 0px;
      position: absolute;
      bottom: -8px;
      z-index: 1;

      .paginate_button {
        height: 0px;
        padding: 0px;
      }

      .pagination {
        display: flex;
        position: relative !important;
        gap: 10px;
        bottom: 25px !important;
        margin-bottom: -1px;

        >li {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 5px;

          .per-page-btn {
            .btn-primary;
            .centered-col();
            padding: 10px 16px;
          }
        }

        >li>a {
          .btn-primary;
          .frc;
        }

        >li>span {
          border: none;
          border-radius: @border-radius-6;

          >input {
            border: none;
            height: 24px;
            width: 24px;
            text-align: center;
            border-radius: @border-radius-6;
            .table-paginatin-text;
          }

          input[type="number"]::-webkit-inner-spin-button,
          input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
          }
        }

        >li {
          >select {
            border: none;
            -webkit-appearance: none;
            /* Safari and Chrome */
            -moz-appearance: none;
            /* Firefox */
            appearance: none;
            margin-right: -50px;
            z-index: 1;
            background-color: transparent;
            height: 35px;
            padding: 5px;
            padding-right: 50px;
            .table-paginatin-text;
            cursor: pointer;
          }

          .per-page {
            .table-paginatin-text;
          }
        }
      }
    }

    .repeaterwrap {
      table {
        width: 100% !important;
      }

      >div {
        display: flex;
        width: 100%;
        height: 100%;
        padding: 0px;
        min-height: 80px;
      }

      display: flex;
      flex-direction: column;
      width: 100%;
      flex: 1;

      .dataTables_wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;
        align-items: end;

        .dataTables_scroll {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          border-top-left-radius: @border-radius-8;
          border-top-right-radius: @border-radius-8;
          width: 100%;
          min-height: 25px;
          max-height: var(--dt-scroll-max-height);
          overflow-y: unset;

          .dataTables_scrollHead {
            height: auto;
            .table-header;
            display: flex;
            flex: none;
            flex-direction: column;
            justify-content: center;

            thead {
              tr {
                th {
                  padding-right: var(--dt-th-pr);
                  text-wrap: nowrap;
                }

                .sorting,
                .sorting_desc,
                .sorting_asc {
                  position: relative;
                  cursor: pointer;

                  &::before,
                  &::after {
                    position: absolute;
                    content: "";
                    width: 15px;
                    height: 10px;
                    background-repeat: no-repeat;
                    right: var(--dt-th-before-right);
                    background-size: var(--dt-th-before-bg);
                    background-position: center;
                    top: var(--dt-th-before-top);
                  }

                  &::after {
                    top: var(--dt-th-after-top);
                  }
                }

                &:hover {

                  .sorting,
                  .sorting_desc,
                  .sorting_asc {
                    &::before {
                      background-image: url("../../public/icons/common/filled/sorting-arrow-up.svg");
                    }

                    &::after {
                      background-image: url("../../public/icons/common/filled/sorting-arrow-down.svg");
                    }
                  }

                  .sorting_desc {
                    &::after {
                      background-image: url("../../public/icons/common/filled/active/sorting-arrow-down-active.svg");
                    }
                  }

                  .sorting_asc {
                    &::before {
                      background-image: url("../../public/icons/common/filled/active/sorting-arrow-up-active.svg");
                    }
                  }
                }

                .sorting_desc {
                  &::before {
                    background-image: url("../../public/icons/common/filled/sorting-arrow-up.svg");
                  }

                  &::after {
                    background-image: url("../../public/icons/common/filled/active/sorting-arrow-down-active.svg");
                  }
                }

                .sorting_asc {
                  &::before {
                    background-image: url("../../public/icons/common/filled/active/sorting-arrow-up-active.svg");
                  }

                  &::after {
                    background-image: url("../../public/icons/common/filled/sorting-arrow-down.svg");
                  }
                }
              }
            }
          }

          thead {
            tr {
              th {
                // color: #42437B;
                padding: var(--dt-th-p);
              }

              th:first-child {
                // border-top-left-radius: 20px;
              }

              th:last-child {
                // border-top-right-radius: 20px;
              }
            }
          }

          tbody {
            .odd {
              // background: @white;

              .dataTables_empty {
                text-align: center;
              }
            }

            .even {
              // background: #F5F7FA;
            }

            tr {
              cursor: pointer;

              &:hover {
                background-color: @semi-transparent-light-gray-25-five;
              }
            }
          }

          .dataTables_scrollBody {
            height: auto !important;
            border-bottom: none;
            overflow-x: unset;
            overflow-y: auto;
            min-height: 30px;
            flex-grow: 1;
            .table-body;
          }

          >div {
            width: 100% !important;

            .dataTables_scrollHeadInner {
              width: 100% !important;

              .dataTable {
                margin-top: 0px !important;
              }

              .repeater {
                width: 100% !important;
              }
            }
          }
        }

        .dataTables_processing {
          background: transparent !important;
          position: absolute;
          z-index: 1;
          top: 50%;
          left: 50%;
        }
      }
    }

    @media (max-width: 1200px) {
      .dataTables_info {
        width: auto;
        left: calc(50% - 130px);
      }

      .repeaterwrap {
        .dataTables_wrapper {
          .dataTables_scroll {
            .dataTables_scrollHead {
              min-height: inherit !important;
            }
          }
        }
      }
    }

    @media (max-width: 992px) {
      .dataTables_info {
        left: 15px;
        bottom: 2px;
      }
    }

  }
}