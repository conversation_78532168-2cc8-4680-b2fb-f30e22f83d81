import { IFormData } from "@hooks/form-data";
import { IDSLDrawGridRef } from "./dsl-grid-view";
import { GridSumInfo } from "@hooks/query";
import { toPresentableLabel } from "@utils/fx";

export const showGridSumColumn = async (ddg: IDSLDrawGridRef, sumInfo: GridSumInfo, data: IFormData[]) => {
	if (!ddg || !ddg.table_grid) {
		return;
	}
	const sumColumn = sumInfo.sumColumn;
	if (!sumColumn) {
		return;
	}
	let sum = data.reduce((acc, d) => {
		if (d[sumInfo.sumColumn]) {
			acc += d[sumInfo.sumColumn] || 0;
		}
		return acc;
	}, 0);
	const label = sumInfo.sumColumnLabel || toPresentableLabel(sumInfo.sumColumn);
	if (sumInfo.sumFormat) {
		sum = window.numeral(sum).format(sumInfo.sumFormat);
	}
	ddg.$el?.find?.(".dataTables_paginate.paging_simple").find(".sum-info").remove();

	ddg.$el
		?.find?.(".dataTables_paginate.paging_simple")
		.append(`<div class="sum-info"><div class="sum-label">${label}</div><div class="sum-value">${sum}</div></div>`);
};
