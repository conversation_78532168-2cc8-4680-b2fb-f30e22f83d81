import type { FC } from "react";
import React, { useMemo, useEffect } from "react";
import type { TabData } from "../tab-list/tab-list";
import { TabList } from "../tab-list/tab-list";
import { DSLTabView } from "../dsl-tab-view/dsl-tab-view";
import { getType } from "@utils/fx";
import { useNavigation } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import "./generic-tab-view.less";
import { useTabController } from "@hooks/tab-controller";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { NestedTabLevelProps, TabLevelProps } from "../nested-tab-view/nested-tab-view";

interface TabContentDrawProps extends RoutedComponentProps {
	tabID: string;
	tab: unknown;
	tabLevelProps: unknown;
	view: unknown[] | unknown;
	form?: string;
	component: unknown;
	level: number;
	linkMap: DSLDrawLinkMap;
	setActiveTab: (tabId: string) => void;
}

const TabContentDraw: FC<TabContentDrawProps> = (props) => {
	const { tabID, tab, tabLevelProps, view, form, component, level, linkMap, setActiveTab } = props;
	const nav = useNavigation(props, `/${tabID}`);
	useEffect(() => {
		if (!props.navPath?.length) {
			return;
		}
		if (props.navPath[0] == tabID) {
			setActiveTab(props.navPath[0]);
		}
	}, [nav.globalNav.lrct]);

	if (view && Array.isArray(view) && view.length) {
		// @ts-ignore
		return <GenericTabView {...{ ...props, ...nav, linkMap, map: view, tabLevelProps, level: level }} />;
	} else if (form) {
		// @ts-ignore
		const additionalProps = tab?.componentProps || {};
		// @ts-ignore
		return <DSLTabView {...{ ...props, ...nav, linkMap, form, styles: tabLevelProps.dsl, ...additionalProps }} />;
	} else if (component) {
		// @ts-ignore
		return <component.renderComponent {...{ ...props, ...component.componentProps, ...nav, linkMap }} />;
	}
	return null;
};

const getTabLevelProps = (tabLevelProps: NestedTabLevelProps, lvl: number): TabLevelProps => {
	if (tabLevelProps?.levels?.[lvl]) {
		return tabLevelProps.levels[lvl];
	}
	let tlp = {};
	while (lvl != 0) {
		if (tabLevelProps?.levels?.[lvl]) {
			tlp = tabLevelProps.levels[lvl];
		}
		lvl = lvl - 1;
	}
	return tlp;
};

interface GenericTabViewProps extends RoutedComponentProps {
	map: TabData[];
	linkMap: DSLDrawLinkMap;
	level: undefined | number;
	tabLevelProps: NestedTabLevelProps;
}

export const GenericTabView: FC<GenericTabViewProps> = (props) => {
	const { map, linkMap, tabLevelProps } = props;
	if (!map) {
		return null;
	}
	const [openTabs, activeTabId, controller] = useTabController(map, map[0].id);
	const nav = useNavigation(props, "/");

	useEffect(() => {
		controller.setOpenTabs(map);
	}, [map]);

	const tabProps = useMemo(() => getTabLevelProps, [props.level]);

	let tabListProps = {};
	if (getType(map) == "array" && map.length) {
		tabListProps = map[0].tabListProps || {};
	}

	const level = props?.level || 0;
	const levelProps = tabProps(tabLevelProps, level);
	return (
		<div className="gen-tab-container">
			<TabList
				activeTabId={activeTabId}
				openTabs={openTabs}
				optionsProps={{
					enabled: false,
				}}
				styles={levelProps.style}
				addProps={{
					enabled: false,
				}}
				{...(levelProps?.listProps || {})}
				tabCanClose={false}
				draggable={false}
				onTabClick={(tab) => {
					controller.setActiveTabId(tab.id);
				}}
				{...tabListProps}
			/>
			{openTabs.map((tab) => (
				<div
					key={tab.key ? `${tab.id}-${tab.key}` : tab.id}
					className="gen-tab-cont-container"
					style={{ display: activeTabId == tab.id ? undefined : "none" }}
				>
					<TabContentDraw
						isActive={activeTabId == tab.id}
						setActiveTab={controller.setActiveTabId}
						isParentActive={props.isActive}
						tabID={tab.id}
						{...nav}
						linkMap={linkMap}
						view={tab.view}
						tab={tab}
						form={tab.form}
						component={tab.component}
						tabLevelProps={tabLevelProps}
						level={level + 1}
					/>
				</div>
			))}
		</div>
	);
};
