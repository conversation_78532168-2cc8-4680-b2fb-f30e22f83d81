import "./form-list-group.less";
import React, { FC, useRef, useState } from "react";
import { useTabController } from "@hooks/tab-controller";
import { TabData } from "@blocks/tab-list/tab-list";
import { CRErrorBoundary } from "@blocks/error-boundary/cr-error-boundary";
import { DSLListView } from "@blocks/dsl-list-view/dsl-list-view";
import icons from "@public/icons";
import { uuid } from "@utils/fx";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

interface FormListGroupProps {
	tabs: TabData[];
	linkMap: DSLDrawLinkMap;
	tabActions: PatientTabActions;
	defaultOpenTab?: string;
	style?: {
		listContainer?: React.CSSProperties;
	};
}

export const FormListGroup = (props: FormListGroupProps) => {
	const { tabs, linkMap, tabActions, defaultOpenTab } = props;
	const [openTabs, activeTabId, tabController] = useTabController(tabs, defaultOpenTab || "");
	const [isOpen, setDropDownState] = useState(false);

	const listViewRef = useRef<AdvancedGridRef | null>(null);

	const formCanAdd = (f: string) => {
		if (!f) return false;
		const allowed = window.Auth.can_create(f);
		if (!allowed) return false;
		return true;
	};

	const onClickAdd = ({ form: f, label: l }: TabData) => {
		if (!f) return;
		if (!formCanAdd(f)) return;
		const componentProps = { linkMap: linkMap, gridRef: listViewRef.current };
		tabActions.openTab(uuid(), "New " + l, "addfill", f, componentProps);
	};

	const onClickDropDown = () => {
		setDropDownState((prev) => !prev);
		if (!activeTabId) {
			tabController.setActiveTabId(openTabs[0].tkey);
		}
	};

	return (
		<div className="form-list-group">
			<div className={`list-tab-bar${isOpen ? " open" : ""}`}>
				<div className="tabs">
					{openTabs.map((tab) => {
						return (
							<button
								key={tab.tkey}
								onClick={() => {
									if (tab.tkey === activeTabId) {
										tabController.setActiveTabId("");
										setDropDownState(false);
										if (!isOpen) {
											setDropDownState(true);
											setDropDownState(true);
											tabController.setActiveTabId(tab.tkey);
										}
										return;
									}
									setDropDownState(true);
									tabController.setActiveTabId(tab.tkey);
								}}
								className={`btn-count${isOpen && tab.tkey === activeTabId ? " active" : ""}`}
							>
								{tab.label}
							</button>
						);
					})}
				</div>
				<div className="dropdown-control">
					<div className="filter-act-btn filter-collapsed" onClick={onClickDropDown}>
						<img
							style={{ width: "25px", height: "25px", cursor: "pointer" }}
							src={isOpen ? icons.common.arrowDownOutlineIcon : icons.common.arrowUpOutline}
						/>
					</div>
				</div>
			</div>
			{openTabs.map((tab) => {
				return (
					<div
						key={tab.tkey}
						className={`list-tab-content${tab.tkey !== activeTabId || !isOpen ? " hide" : ""}`}
						style={props.style?.listContainer}
					>
						<CRErrorBoundary>
							<DSLListView
								key={tab.tkey}
								label={tab.label}
								form={tab.form}
								hideFilter={true}
								refCallback={(ref: AdvancedGridRef | null) => {
									listViewRef.current = ref;
								}}
								canAdd={true}
								filtersPresetFixed={{}}
								sort={{
									direction: "desc",
									property: "id",
								}}
								linkMap={linkMap}
								rowClicked={(row) => {
									tabActions.rowClicked(row);
								}}
								onAdd={() => {
									onClickAdd(tab);
								}}
							/>
						</CRErrorBoundary>
					</div>
				);
			})}
		</div>
	);
};
