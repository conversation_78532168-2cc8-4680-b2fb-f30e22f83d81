@import (reference) '../../less/style/main.less';

.form-list-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    flex-basis: min-content;
    flex-shrink: 0;

    .list-tab-bar {
        .card-one;
        padding: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        min-height: fit-content;
        overflow: hidden;

        &.open {
            border-bottom-left-radius: 0px;
            border-bottom-right-radius: 0px;
        }

        .tabs {
            display: flex;
            flex-direction: row;
            gap: 10px;
            overflow-x: scroll;
        }
    }

    .list-tab-content {
        .dsl-list-tab-container {
            padding-left: 0px;
            padding-right: 0px;
            width: 100%;
            overflow: unset;

            .dsl-list-export {
                >div {
                    margin-bottom: 0px !important;
                }
            }
        }

        .card-one;
        padding: 10px;
        width: 100%;
        max-height: 250px;
        height: 250px;
        min-height: fit-content;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: 10px;

        &:not(.hide) {
            border-top-left-radius: 0px;
            border-top-right-radius: 0px;
            border-top: 1px solid lightgray;
        }
    }

}