import React, { FunctionComponent, useState } from "react";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";
import { DSLListView, DSLListViewProps } from "../dsl-list-view/dsl-list-view";
import "./query-list-view.less";
import { TabData } from "../tab-list/tab-list";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import _ from "lodash";
import { useQueryForm } from "@hooks/query";
import NoDataFound from "@components/common/no-data-found";
import SpinLoader from "@components/common/spin-loader";
import { GridRowClickedEvent } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import { openFlyoutFromQueryData } from "@utils/dsl-fx";
import { IFormData } from "@hooks/form-data";
interface ActionComponentProps {
	row: TabData | Record<string, unknown>;
}
interface QueryListViewProps extends Omit<DSLListViewProps, "form" | "openTab"> {
	code: string;
	parameters?: string;
	ActionComponent?: FunctionComponent<ActionComponentProps>;
	label?: string;
	filtersPresetFixed?: Record<string, unknown>;
	linkMap?: DSLDrawLinkMap;
	rowClicked?: GridRowClickedEvent;
}

const QueryListView: React.FC<QueryListViewProps> = (props) => {
	const { ActionComponent, code, parameters, filtersPresetFixed, rowClicked } = props;
	const gridInfo = useQueryForm(code, parameters, props.label);
	const [selectedRow, setSelectedRow] = useState<TabData | null>(null);

	const onRowClick: GridRowClickedEvent = (row: TabData, event) => {
		delete row.gridRef;
		event = _.cloneDeep(event);
		if (rowClicked) {
			rowClicked(row, event);
		} else {
			openFlyoutFromQueryData(row.rowData as IFormData);
		}
		setSelectedRow((c) => {
			if (JSON.stringify(row.rowData) === JSON.stringify(c?.rowData || {})) {
				return null;
			} else {
				return row;
			}
		});
	};

	if (gridInfo.state == "loading") return <SpinLoader loading={true} fontSize="2em" />;
	if (gridInfo.state == "failed") return <NoDataFound text="Query Code Not Found" />;
	if (!window.DSL[gridInfo?.form]) return <NoDataFound text="DSL Info Not Found..." />;
	return (
		<div className="query-list-view">
			<CRErrorBoundary>
				<div className="query-list">
					<DSLListView
						{...props}
						disableRouting={true}
						label={gridInfo.label}
						form={gridInfo.form as string}
						gridSourceOverrideURL={gridInfo.gridSourceOverrideURL}
						filtersPresetFixed={filtersPresetFixed}
						rowClicked={onRowClick}
						customColumns={gridInfo.customColumns}
						customFindFields={gridInfo.customFindFields}
						linkMap={props.linkMap}
						isActive
						isParentActive={props.isActive}
						canAdd={props.canAdd || false}
					/>
				</div>
				<div className="query-action">
					{ActionComponent ? <ActionComponent row={selectedRow || {}} /> : null}
				</div>
			</CRErrorBoundary>
		</div>
	);
};

QueryListView.defaultProps = {
	label: "",
};

export default QueryListView;
