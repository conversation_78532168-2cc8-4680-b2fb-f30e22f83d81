.query-list-view {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow-y: unset;

    .tr-select {
        background: #C6DDF5 !important;

        th {
            color: #2098DC !important;
        }
    }

    .dsl-list-tab-container {
        padding-left: 0px;
        padding-right: 0px;
        overflow: unset;
    }

    .query-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 250px;
    }

    .query-action {
        display: flex;
        flex-direction: column;
        flex: 0;
        min-height: fit-content;
    }
}