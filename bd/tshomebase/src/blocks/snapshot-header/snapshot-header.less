@import (reference) '../../less/style/main.less';

.icon(@content, @color: rgba(155, 159, 168, 1), @size: 14px, @margin-right: 0) {
    content: @content;
    font-family: 'Font Awesome 6 Pro';
    color: @color;
    font-size: @size;
    margin-right: @margin-right;
}

.text-style(@size: 14px, @weight: 500, @color: rgba(94, 99, 107, 1)) {
    font-size: @size;
    font-weight: @weight;
    color: @color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

@keyframes wiggleIcon {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(10deg); }
    50% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
    100% { transform: rotate(0deg); }
}

@keyframes iconClickBounce {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.85);
    }
    100% {
      transform: scale(1);
    }
  }

.badge(@bg-color, @color: white) {
    background-color: @bg-color;
    color: @color;
    padding: 2px 8px;
    border-radius: 16px;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dsl-container:has(.arrowDownContainer.open) {
    .header-container {
        .insurance-info {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            box-shadow: 
            inset 2px 0 0 0 var(--color-tertiary),
            inset -2px 0 0 0 var(--color-tertiary),
            inset 0 2px 0 0 var(--color-tertiary);
        }
    }
}

.inc-container {
    display: flex;
    flex-direction: column;
    flex-shrink: 0 !important;
    margin: 8px 8px 0 8px;
    background: rgba( 255, 255, 255, 0.25 );
    border: 1px solid rgba(255, 255, 255, 0.3);
    .header-container {
        .flex-row;
        gap: 8px;
        justify-content: space-between;

        .pt-info {
            min-width: 20%;
            .flex-column;

            .status-container {
                .flex-row;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;

                span:first-of-type {
                    font-size: 22px;
                    font-weight: 700;
                    color: #5E636B;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .status {
                    margin-right: 10px;
                    background-color: #949492;
                    color: white;
                    padding: 2px 8px;
                    border-radius: 16px;
                    font-weight: 700;
                    white-space: nowrap;
                }
                .active {
                    background-color: white;
                    color: #689989;
                    box-shadow: inset 0 0 0 2px #689989;
                }
                .on-hold {
                    background-color: #837BB2;
                }
                .pending {
                    background-color: #838894;
                }


                .status-icon::before {
                    .icon("\f06a", rgba(229, 135, 135, 1), 20px);
                }

                .status-active::before {
                    .icon("\f06a", rgba(131, 123, 178, 1), 18px);
                }

                .default-status::before {
                    .icon("\f06a", #949492, 18px);
                }

                .status-icon::before,
                .status-active::before,
                .default-status::before {
                    transition: transform 0.2s ease;
                    display: inline-block;
                    cursor: pointer;
                }
                .status-icon:hover::before,
                .status-active:hover::before,
                .default-status:hover::before {
                    animation: wiggleIcon 0.5s ease;
                }

                >h1 {
                    .text-style(20px, 700);
                }
            }

            .info-nav {
                .flex-row;
                gap: 10px;

                .setting-bcontainer {
                    align-self: center;
                    flex: 0 0 auto;

                    .setting-button {
                        height: 36px;
                        width: 36px;
                        background-color: rgba(102, 142, 186, 1);
                        border-radius: 8px;
                        font-size: 18px;
                        border: none !important;
                        color: white;
                        transition: transform 0.1s ease;
                        cursor: pointer;
                    }
                    
                    &:active {
                        transform: scale(0.9);
                    }

                    .setting-cond {
                        display: none;
                    }

                }

                .general-info {
                    .flex-column;
                    gap: 4px;

                    .info-child {
                        display: flex;
                        gap: 16px;

                        >p {
                            .text-style(14px, 500);
                        }
                    }

                    .phone::before {
                        .icon("\f095", rgba(155, 159, 168, 1), 14px, 8px);
                    }

                    .dob::before {
                        .icon("\f333", rgba(155, 159, 168, 1), 14px, 8px);
                    }

                    .address-container {
                        display: flex;

                        >p {
                            .text-style(14px, 500);
                        }
                    }
                }

                .address-container::before {
                    .icon("\f015", rgba(155, 159, 168, 1), 14px, 8px);
                }
            }
        }

        .med-info {
            max-width: 20%;
            .flex-column;
            gap: 4px;
            padding: 8px;

            .med-name {
                .text-style(14px, 700);
            }

            .med-name::before {
                .icon("\f004", rgba(155, 159, 168, 1), 14, 8px);
            }

            .med-desc {
                .text-style(14px, 500, rgba(229, 135, 135, 1));
            }

            .med-desc::before {
                .icon("\e4a8", rgba(155, 159, 168, 1), 14px, 8px);
            }

            .therapy-container {
                .flex-row;
            }

            .med-team {
                background-color: #838894 !important;
                .badge(rgba(163, 163, 160, 1));
            }

            .flag-icon::before {
                .icon("\f024", rgba(155, 159, 168, 1), 14px, 8px);
            }
        }

        .primary-phy-info {
            .flex-column;
            gap: 4px;
            max-width: fit-content;
            padding: 8px;

            .primary-phy-name {
                .text-style(14px, 700);
            }

            .primary-phy-name::before {
                .icon("\f0f1", rgba(155, 159, 168, 1), 14px, 8px);
            }

            .primary-phy-pcontact {
                .text-style(14px, 400);
            }

            .primary-phy-pcontact::before {
                .icon("\f095", rgba(155, 159, 168, 1), 14px, 8px);
            }

            .primary-phy-fax {
                .text-style(14px, 400);
            }

            .primary-phy-fax::before {
                .icon("\f02f", rgba(155, 159, 168, 1), 14px, 8px);
            }
        }

        .secondary-phy-info {
            .flex-column;
            gap: 4px;
            max-width: 20%;
            padding: 8px;

            .secondary-phy-name {
                .text-style(14px, 700);
            }

            .secondary-phy-name::before {
                .icon("\f4c0", rgba(155, 159, 168, 1), 14px, 8px);
            }

            .pharmacy {
                .text-style(14px, 400);
            }

            .pharmacy::before {
                .icon("\f0fe", rgba(155, 159, 168, 1), 14px, 8px);

            }

            .secondary-location {
                .text-style(14px, 400);
            }

            .secondary-location::before {
                .icon("\f3c5", rgba(155, 159, 168, 1), 14px, 8px);
            }
        }

        .insurance-info {
            background-color: #FAFAFAD9;
            position: relative;
            
            &:not(:has(.arrowDownContainer.open))::before {
              content: '';
              position: absolute;
              inset: 0;
              border-radius: 8px;
              padding: 1px;
              background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
              -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
              -webkit-mask-composite: xor;
              mask-composite: exclude;
              pointer-events: none;
            }
            box-shadow:
                0px -1px 2px 0px rgba(0, 0, 0, 0.22) inset,
                0px 1px 2px 0px rgba(255, 255, 255, 0.12) inset;
            padding: var(--Component-Padding), var(--Inner-Padding), var(--Component-Padding), var(--Inner-Padding);
            border-radius: 8px;
            max-width: 20%;
            width: 100%;
            flex: unset;

            .ins-top {
                border-top-right-radius: 8px;
                border-top-left-radius: 8px;
                padding: 6px 8px 0;
                .flex-row;
                justify-content: space-between;

                .content {
                    .flex-column;
                    width: 85%;

                    .ins-header {
                        .flex-row;
                        justify-content: space-between;
                        align-items: center;
                        padding: 5px 0;

                        span {
                            .text-style(14px, 700);
                        }

                        .title::before {
                            .icon("\f111", rgba(229, 135, 135, 1), 14px, 8px);
                        }

                    }

                    .insurance-details {
                        .flex-row;
                        gap: 8px;

                        span {
                            .text-style(14px, 400, rgba(94, 99, 107, 1));
                        }

                        .p-box,
                        .fax-box {
                            display: flex;
                            width: 50%;
                            align-items: center;
                        }

                        .insurnace-phone {
                            color: rgba(155, 159, 168, 1);
                            font-size: 14px;
                            margin-right: 8px;
                            align-self: center;
                        }

                        .insurnace-fax {
                            color: rgba(155, 159, 168, 1);
                            font-size: 14px;
                            margin-right: 8px;
                            align-self: center;
                        }
                    }

                    .des-section {
                        .flex-row;
                        gap: 12px;

                        .id-container {
                            .flex-row;
                            gap: 8px;
                            width: 46%;

                            >p {
                                .text-style(12px, 700, rgba(155, 159, 168, 1));
                                align-self: center;
                                overflow: visible;
                            }

                            span {
                                .text-style(14px, 400, rgba(94, 99, 107, 1));
                            }
                        }

                        .group-container {
                            .flex-row;
                            gap: 8px;
                            width: 40%;

                            >p {
                                .text-style(12px, 700, rgba(155, 159, 168, 1));
                                align-self: center;
                                overflow: visible;
                            }

                            span {
                                .text-style(14px, 400, rgba(94, 99, 107, 1));
                            }
                        }
                    }
                }

                .wgdt-container {
                    .flex-column;
                    align-items: center;
                    justify-content: space-between;
                    margin: auto;

                    .external-link{
                        display: inline-block;
                        transition: transform 0.2s ease;
                        display: none;

                        &:active {
                            animation: iconClickBounce 0.3s ease;
                        }

                        &::before {
                            .icon("\f08e", rgba(131, 123, 178, 1), 18px);
                            display: inline-block; // important so it can transform
                            cursor: pointer;
                            transition: transform 0.2s ease;
                        }
                }

                    .arrow-down::before {
                        .icon("\f078", rgba(112, 117, 128, 1), 18px);
                        cursor: pointer;
                    }

                    .arrow-up::before {
                        .icon("\f077", rgba(112, 117, 128, 1), 18px);
                    }

                    .external-link-cond {
                        display: none;
                    }

                    .arrow-down-cond {
                        display: none;
                    }
                }
            }
        }
    }

}

.cond-background {
    border-radius: 12px;
    .status-container {
        padding-left: 10px;
    }
    padding: 8px;
    background: rgba(236, 236, 236, 0.1);
    opacity: 0.94;
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.15);
}

.arrowDownContainer {
    position: absolute;
    background-color: #f4f4f4d9;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: 10;
    pointer-events: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    opacity: 0;
    overflow: visible !important;
    transform: translateY(-20px);
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .ins-item {
        display: flex;
        margin-inline: 10px;

        >p {
            font-size: 14px;
            font-weight: 400;
            color: #5e636b;
            padding: 6px 6px 6px 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &:hover {
                color: var(--color-tertiary);
            }
        }

        &::before {
            content: '';
            font-family: 'Font Awesome 6 Pro';
            color: #76ad9b;
            font-size: 14px;
            align-self: center;
            padding-left: 5px;
            font-weight: 900;
        }
    }

    &.open {
        opacity: 1;
        transform: translateY(0);
        pointer-events: auto;
        border-top-width: 0;
        box-sizing: border-box;
        box-shadow: 
        inset 2px 0 0 0 var(--color-tertiary),
        inset -2px 0 0 0 var(--color-tertiary),
        inset 0 -2px 0 0 var(--color-tertiary), 
        0 6px 12px rgba(0, 0, 0, 0.08);
    }

    .ins-box {
        margin: 2px 8px 8px;
        padding: 4px 0;
        border-radius: 6px;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        background: #FAFAFA;
        box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.22) inset;
        border-top: 1px solid #7b7b7b22;
    }
}