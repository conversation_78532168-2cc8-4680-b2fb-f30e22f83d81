import React, { useEffect, useRef, useState } from "react";

import { fetchFormFilters, IFormData, useFormFilters } from "hooks/form-data";
import "./snapshot-header.less";
import HeaderBar from "@blocks/vertical-tab-view/components/header-bar/header-bar";
import { renderItemData } from "@utils/fx";
import icons from "@public/icons";
import DropDownNavigator, { NavigatorOnClick } from "@components/dropdowns/dropdown-navigator/dropdown-navigator";
import { getPatientNavigatorList } from "@modules/patient/defaults";
import { request } from "@core/request";
import { isTempId } from "@utils/dsl-fx";
import { openAlertDialog } from "@blocks/dsl-card-view/dsl-field-alert";

export interface SnapshotHeaderProps {
	settingBar: boolean;
	externalLink: boolean;
	patientId: string | number;
	patientData: IFormData;
	background: boolean;
	onNavClick?: NavigatorOnClick;
}

interface InsuranceItem {
	payer_id: string;
	payer_id_auto_name: string;
}

interface SnapshotHeaderFormProps {
	formName: string;
	formId?: number | string;
}

export const SnapshotHeaderForm: React.FC<SnapshotHeaderFormProps> = (props) => {
	const { formName, formId } = props;
	const [patient, setPatient] = useState<IFormData | null>(null);

	useEffect(() => {
		if (formName === "patient") return;
		if (!formName || !formId) return;
		if (isTempId(formId)) return;
		request({
			method: "GET",
			url: `/api/patient/patient_id?form_name=${formName}&form_id=${formId}`,
		}).then((res) => {
			setPatient(res.data);
		});
	}, [formName, formId]);

	if (formName === "patient") return null;
	if (!patient) return null;
	if (!patient?.id) return null;

	return (
		<SnapshotHeader
			settingBar={false}
			externalLink={false}
			patientId={patient.id}
			patientData={patient}
			background={true}
		/>
	);
};

const SnapshotHeader: React.FC<SnapshotHeaderProps> = (props) => {
	const { settingBar, externalLink, patientData, patientId, background, onNavClick } = props;

	const [clickarrowDown, setClickArrowDown] = useState(false);
	const [dropdownStyle, setDropdownStyle] = useState({});
	const insuranceRef = useRef<HTMLDivElement>(null);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const alertShown = useRef(false);
	const [width, setWidth] = useState(0);
	const [physicianInfo, setPhysicianInfo] = useState(null);

	useEffect(() => {
		if (!insuranceRef.current) return;

		const updateDropdownStyle = () => {
			if (insuranceRef.current) {
				const { width } = insuranceRef.current.getBoundingClientRect();
				setWidth(width);
				setDropdownStyle({
					width: `calc(${width}px)`,
				});
			}
		};
		updateDropdownStyle();
		const observer = new ResizeObserver(updateDropdownStyle);
		observer.observe(insuranceRef.current);

		return () => observer.disconnect();
	}, [clickarrowDown]);

	const openAlertDialogInternal = (mode: "edit" | "view") => {
		openAlertDialog({
			form: "patient",
			record: patientId.toString(),
			customTitle: "Clinical Alert",
			customFieldLabel: "Clinical Alert",
			field: "clinical_alert",
			mode: mode as any,
			required: true,
		});
	};

	useEffect(() => {
		if (!props.settingBar) {
			return;
		}
		if (patientData?.clinical_alert && !alertShown.current) {
			alertShown.current = true;
			openAlertDialogInternal("view");
		}
	}, [patientData]);

	const statusMap: any = {
		"1": "Pending",
		"2": "Intake Complete",
		"3": "Active",
		"4": "On-Hold",
		"5": "Inactive",
		"6": "Discharged - Therapy Complete",
		"7": "Cancelled",
		"8": "Deceased",
	};
	const get_status = patientData && statusMap[patientData?.status_id];

	const [diagnosis] = useFormFilters("patient_diagnosis", {
		filter: {
			patient_id: patientId,
			active: "Yes",
		},
		pool: true,
		sortDirection: "asc",
		sortProperty: "rank",
	});

	const latest_diagnosis = diagnosis?.data[0];
	const [diagnosis_name] = useFormFilters("list_diagnosis", {
		filter: {
			code: latest_diagnosis?.dx_id,
		},
	});

	const get_diagnosis_name = diagnosis_name?.data[0];
	const [allergies] = useFormFilters("patient_allergy", {
		filter: {
			patient_id: patientId,
			active: 'Yes'
		},
		pool: true,
		sortDirection: "desc",
		sortProperty: "id",
	});

	const allergyNames =
		allergies?.data
			?.filter(
				(alg) => typeof alg.auto_name === "string" && isNaN(alg.auto_name.trim()) // Exclude numbers
			)
			?.map((alg) => {
				const trimmedName = alg.auto_name.trim();
				return trimmedName.charAt(0).toUpperCase() + trimmedName.slice(1);
			})
			.join(", ") || "";

	const [patient_address] = useFormFilters("patient_address", {
		filter: {
			patient_id: patientId,
		},
		pool: true,
		sortDirection: "desc",
		sortProperty: "id",
	});
	const get_patient_address = patient_address?.data[0];

	const [order] = useFormFilters("careplan_order", {
		filter: {
			patient_id: patientId,
		},
		pool: true,
		sortDirection: "desc",
		sortProperty: "id",
	});

	const get_order = order.data && order?.data[0];

	useEffect(() => {
		if (patientData?.primary_physician_id || patientData?.referrer_id) {
			getphysician();
		}
	}, [patientData]);

	const getphysician = (orderItem?: IFormData) => {
		fetchFormFilters("physician", {
			filter: {
				id: patientData?.primary_physician_id || patientData?.referrer_id,
			},
		})
			.then((resp) => {
				setPhysicianInfo(resp.data[0]);
			})
			.catch((error) => {
				console.log("error fetching filters", error);
			});
	};

	const [insurance] = useFormFilters("patient_insurance", {
		filter: {
			patient_id: patientId,
		},
		pool: true,
		sortDirection: "desc",
		sortProperty: "id",
	});

	let { get_primary_insurance, other_insurance } = insurance.data
		? insurance.data.reduce(
				(acc, item) => {
					if (item?.payer_level === "Primary" && item?.rank === 1) {
						acc.get_primary_insurance.push(item);
					} else {
						acc.other_insurance.push(item);
					}
					return acc;
				},
				{ get_primary_insurance: [], other_insurance: [] }
		  )
		: { get_primary_insurance: [], other_insurance: [] };

	if (get_primary_insurance.length == 0 && insurance && insurance?.data?.length > 0) {
		get_primary_insurance = insurance.data[0];
		other_insurance = insurance.data.slice(1);
	}

	let get_insurance_object = null;
	if (
		Array.isArray(get_primary_insurance) &&
		typeof get_primary_insurance[0] === "object" &&
		get_primary_insurance[0] !== null
	) {
		get_insurance_object = get_primary_insurance[0];
	} else {
		get_insurance_object = get_primary_insurance;
	}

	const [SalesAccountSnapshot] = useFormFilters("sales_account", {
		filter: {
			patient_id: patientId,
		},
		pool: true,
		sortDirection: "desc",
		sortProperty: "id",
	});
	const get_sales_rep = SalesAccountSnapshot?.data[0];

	const formatFields = (...fields: any[]) => {
		const validFields = fields.filter(Boolean);
		return validFields.length > 0 ? validFields.join(" ") : "";
	};

	const capitalizeFirstLetter = (str) => {
		if (!str || typeof str !== "string") return "";
		return str.charAt(0).toUpperCase() + str.slice(1);
	};

	const onClicked = () => {
		if (insurance && insurance?.data?.length > 1) {
			setClickArrowDown(!clickarrowDown);
		}
	};

	const ArrowDownContainer = ({
		other_insurance,
		dropdownStyle,
	}: {
		other_insurance: any[];
		dropdownStyle: React.CSSProperties;
		patientId: number | string;
	}) => {
		const handleInsuranceClick = (id: number | string) => {
			window.Flyout.open({
				form: "patient_insurance",
				mode: "read",
				record: id,
				autoRecoverEnabled: false,
			});
		};

		return (
			<div className="ins-box">
				{other_insurance?.map((obj: any, index: number) => (
					<div
						className="ins-item"
						key={`insurance-${index}`}
						onClick={() => handleInsuranceClick(obj.id)}
						style={{ cursor: "pointer" }}
					>
						<p>{obj?.payer_id_auto_name}</p>
					</div>
				))}
			</div>
		);
	};

	const handleStatusClicked = () => {
		openAlertDialogInternal("edit");
		return;
	};

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (insuranceRef.current && !insuranceRef.current.contains(event.target)) {
				setClickArrowDown(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const handleExternalLink = () => {
		window.App.reactNav.goTo(`/patient/${patientId}/patient_insurance`);
	};

	const calculateAge = (dob) => {
		if (!dob) return "";
		const [month, day, year] = dob.split('/').map(Number);
		const birthDate = new Date(year, month - 1, day);
		const today = new Date();
		let age = today.getFullYear() - birthDate.getFullYear();
		const m = today.getMonth() - birthDate.getMonth();
		
		if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
			age--;
		}
		return age;
	}

	return (
		<>
			<div className={`inc-container ${background ? "cond-background" : ""}`}>
				<div className={"header-container"}>
					<div className="pt-info">
						<div className="status-container">
							<span>
								{formatFields(window.joinValid([patientData?.lastname, patientData?.firstname], ", "))}
							</span>
							{patientData.status_id != null && (
								<span
									onClick={handleStatusClicked}
									className="status-icon"
								></span>
							)}
							{patientData.status_id != null && (
								<span className={`status ${get_status.toLowerCase()}`}>
									{renderItemData(get_status)}
								</span>
							)}
						</div>
						<div className="info-nav">
							<div className="setting-bcontainer">
								<DropDownNavigator
									lists={getPatientNavigatorList()}
									menuLabel="Patient Navigator"
									icon={icons.snap.patient.settingsButton}
									onClick={onNavClick}
									btnClass={`setting-button ${!settingBar ? "setting-cond" : ""}`}
								/>
							</div>
							<div className="general-info">
								<div className="info-child">
									<p className="phone">{renderItemData(patientData.phone_cell)}</p>
									<p className="dob">{`${renderItemData(patientData.dob)} (${calculateAge(patientData.dob)} y/o)`}</p>
								</div>
								<div className="address-container">
									<p className="address">{formatFields((get_patient_address?.street ||get_patient_address?.street2),get_patient_address?.city, get_patient_address?.state , get_patient_address?.zip )}</p>
								</div>
							</div>
						</div>
					</div>
					<div className="med-info">
						<span className="med-name">{renderItemData(latest_diagnosis?.dx_id_auto_name)}</span>
						<span className="med-desc">{renderItemData(allergyNames)}</span>
						<div className="therapy-container">
							<span className="flag-icon"></span>
							{patientData?.team_id_auto_name && patientData?.team_id_auto_name != null && (
								<span className="med-team">
									{renderItemData(capitalizeFirstLetter(patientData?.team_id_auto_name))}
								</span>
							)}
						</div>
					</div>
					<div className="primary-phy-info">
						<span className="primary-phy-name">
							{`${renderItemData(physicianInfo?.first)} ${renderItemData(
								physicianInfo?.last
							)} ${renderItemData(physicianInfo?.title)}`}
						</span>
						<span className="primary-phy-pcontact">{renderItemData(physicianInfo?.primary_phone)}</span>
						<span className="primary-phy-fax">{renderItemData(physicianInfo?.primary_fax)}</span>
					</div>
					<div className="secondary-phy-info">
						<span className="secondary-phy-name">{"-"}</span>
						<span className="pharmacy">{renderItemData(patientData?.site_id_auto_name)}</span>
						<p className="secondary-location">{renderItemData(patientData?.territory_id_auto_name)}</p>
					</div>
					<div className="insurance-info" ref={insuranceRef}>
						<div className="ins-top">
							<div className="content">
								<div className="ins-header">
									<span className="title">
										{renderItemData(get_insurance_object?.payer_id_auto_name)}
									</span>
								</div>
								<div className="insurance-details">
									<div className="p-box">
										<i className="fa-light fa-phone insurnace-phone" />
										<span>{renderItemData(get_insurance_object?.contact_phone)}</span>
									</div>
									<div className="fax-box">
										<i className="fa-regular fa-print insurnace-fax" />
										<span>{renderItemData(get_insurance_object?.fax)}</span>
									</div>
								</div>
								<div className="des-section">
									<div className="id-container">
										<p>ID</p>
										<span>
										{renderItemData(get_insurance_object?.insurance_id)}
									  </span>
									</div>
									<div className="group-container">
										<p>Group</p>
										<span>{renderItemData(get_insurance_object?.group_number)}</span>
									</div>
								</div>
							</div>
							<div className="wgdt-container">
								<span
									onClick={handleExternalLink}
									className={`external-link ${!externalLink ? "external-link-cond" : ""}`}
								></span>
								{insurance && insurance?.data?.length > 1 ? (
									<span
										onClick={onClicked}
										className={` ${clickarrowDown ? "arrow-up" : "arrow-down"} `}
									></span>
								) : (
									""
								)}
							</div>
						</div>
						<div
							ref={dropdownRef}
							className={`arrowDownContainer ${clickarrowDown ? "open" : ""}`}
							style={dropdownStyle}
						>
							{clickarrowDown ? (
								<ArrowDownContainer other_insurance={other_insurance} dropdownStyle={dropdownStyle} />
							) : (
								""
							)}
						</div>
					</div>
				</div>
				{get_order && get_order != null && <HeaderBar order={get_order} />}
			</div>
		</>
	);
};

export default SnapshotHeader;
