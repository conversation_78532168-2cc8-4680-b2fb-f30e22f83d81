import type { <PERSON> } from "react";
import React from "react";
import { ErrorBoundary } from "react-error-boundary";
import "./cr-error-boundary.less";

interface CRErrorBoundaryProps {
	fallbackMessage?: string;
	children: React.ReactNode;
	resetKeys?: string[];
}
export const CRErrorBoundary: FC<CRErrorBoundaryProps> = ({ fallbackMessage, children, resetKeys }) => (
	<ErrorBoundary
		resetKeys={resetKeys}
		fallback={
			<div className="cr-error-boundary">⚠️ {fallbackMessage ? fallbackMessage : "Something went wrong!"}</div>
		}
	>
		{children ? children : null}
	</ErrorBoundary>
);
