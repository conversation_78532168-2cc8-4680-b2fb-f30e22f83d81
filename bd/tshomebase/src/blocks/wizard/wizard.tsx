import React, { useEffect, useMemo, useRef, useState } from "react";
import "./wizard.less";
import { IFormData, useFormFilters } from "@hooks/form-data";
import _ from "lodash";
import type { GraphData, NodeData } from "@modules/workflow/types";
import DSLCardView from "../dsl-card-view/dsl-card-view";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import wizardIcons from "@public/icons/wizard";
import { request } from "@core/request/request";
import { DSLListCompactView } from "../dsl-list-compact-view/dsl-list-compact-view";
import { sectionShowOnly } from "@utils/dsl-fx";
import type { TabData } from "../tab-list/tab-list";
import SpinLoader from "@components/common/spin-loader";

interface WizardProps {
	wizardCode: string;
	patientId?: number;
	linkMap?: DSLDrawLinkMap;
	wizardId?: number;
	preset?: object;
	removeStepIfNoPreset?: boolean;
	skipStep?: boolean;
	noFormSubmission?: boolean;
	customStepOrder?: string[];
	onSave?: (tab: TabData, data?: unknown) => void;
	tabData?: TabData;
	presetFormIds?: Record<string, number>;
}

interface IWorkflowWizardStep extends IFormData {
	id: number;
	created_on: string;
	updated_on: string;
	created_by_auto_name: string;
	updated_by_auto_name: string;
	code: string;
	dsl_form: string;
	form_id: null | number;
	form_data: object;
	description: string;
	label: string;
	type: "wizardForm" | "wizardTable";
	wizard_table_column: string;
	dsl_form_section: string;
	wizard_query_param: string;
}

interface IWorkflowWizard {
	id: number;
	created_on: string;
	updated_on: string;
	created_by_auto_name: string;
	updated_by_auto_name: string;
	wizard_code: string;
	patient_id: number | null;
	link_ids: DSLDrawLinkMap;
	prefill_data: object;
	graph_data: GraphData;
	step_order: string[];
	wizard_step: IWorkflowWizardStep[];
	form_map: Record<string, string>;
}

const WizardView: React.FC<WizardProps> = (props) => {
	const {
		preset: presetData = {},
		removeStepIfNoPreset,
		presetFormIds,
		skipStep,
		noFormSubmission,
		customStepOrder,
	} = props;

	const dslRefs = useRef<any>({});
	const fromRendered: string[] = [];
	const [loadingState, setLoadingState] = useState("Loading...");
	const [isFinalStep, setIsFinalStep] = useState(false);
	const [isFirstStep, setIsFirstStep] = useState(false);
	const [filledData, setFilledData] = useState({});
	const [wizardData, setWizardData] = useState<IWorkflowWizard | null>(null);
	const [formMap, setFormMap] = useState<Record<string, string>>({});
	const [wizardSteps, setWizardSteps] = useState<IWorkflowWizardStep[]>([]);
	const [activeStep, setActiveStep] = useState<string>("");
	const [stepErrors, setStepError] = useState<Record<string, number>>({});
	console.log(filledData, "filledData");
	const [wizardTemplate] = useFormFilters("workflow", {
		filter: {
			type: "Wizard",
			active: "Yes",
			code: props.wizardCode,
		},
		sortDirection: "asc",
		sortProperty: "sort_order",
		limit: 2,
	});

	const onClickNext = () => {
		if (isFinalStep) {
			if (skipStep) {
				wizardSteps.forEach((step) => {
					// updateStep(step.code);
					if (step.type == "wizardForm") {
						verify(step.code);
					}
				});
			} else {
				if (verify(activeStep)) {
					return;
				}
			}
			setTimeout(() => {
				if (_.some(stepErrors)) {
					// window.prettyError("Wizard Error!", "Missing Required Fields.");
					return;
				} else {
					if (props.onSave) {
						props.onSave(props.tabData as any, filledData);
					}
				}
			}, 500);
			return;
		}
		// updateStep(activeStep);
		let index = wizardSteps.findIndex((v) => v.code == activeStep);
		if (wizardSteps[index].type == "wizardForm") {
			if (verify(activeStep) && !skipStep) {
				return;
			}
		}
		if (index < 0) {
			return;
		}
		index = index + 1;
		if (index < wizardSteps.length) {
			setActiveStep(wizardSteps[index].code);
		}
		setButtonFlags(index);
	};

	const verify = (curActiveStep) => {
		if (!curActiveStep) {
			curActiveStep = activeStep;
		}
		const index = wizardSteps.findIndex((v) => v.code == curActiveStep);

		const dslRef = dslRefs[wizardSteps[index].dsl_form];
		let hasErrors = 0;
		if (!dslRef?.cardform?.ddf?.verify()) {
			hasErrors = dslRef?.cardform?.ddf?.count_missing_required_fields() || 1;
		}
		setFilledData((d) => {
			return { ...d, [wizardSteps[index].dsl_form]: dslRef?.cardform?.ddf?.get_formdata() || {} };
		});
		if (!hasErrors && !noFormSubmission) {
			if (dslRef?.cardform?.ddf?.form_rendered) dslRef.tab_do("save");
		}
		setStepError({ ...stepErrors, [curActiveStep]: hasErrors });
		return hasErrors;
	};

	const setButtonFlags = (index: number) => {
		if (index == 0) {
			setIsFirstStep(true);
		} else {
			setIsFirstStep(false);
		}
		if (index >= wizardSteps.length - 1) {
			setIsFinalStep(true);
		} else {
			setIsFinalStep(false);
		}
	};

	const onClickPrevious = () => {
		let index = wizardSteps.findIndex((v) => v.code == activeStep);
		if (wizardSteps[index].type == "wizardForm") {
			verify(activeStep);
		}
		if (index < 1) {
			return;
		}

		index = index - 1;
		setActiveStep(wizardSteps[index].code);
		setButtonFlags(index);
	};

	const updateStep = (curActiveStep: string, form_id = null) => {
		if (!curActiveStep) {
			curActiveStep = activeStep;
		}

		const index = wizardSteps.findIndex((v) => v.code == curActiveStep);
		const form = wizardSteps[index].dsl_form;
		const dslRef = dslRefs[form];

		let fd = {};
		try {
			fd = dslRef?.cardform?.ddf.get_formdata();
		} catch (e) {
			//pass
		}
		wizardSteps[index].form_data = fd;
		if (form_id) {
			wizardSteps[index].form_id = form_id;
			setFormMap((prev) => ({ ...prev, [form]: form_id }));
		}
		const stps = _.cloneDeep(wizardSteps);
		request({
			url: `/form/wf_wizard/${wizardData?.id}`,
			method: "PUT",
			data: {
				updated_by: window.App.user.id,
				form_map: formMap,
				wizard_step: [wizardSteps[index]],
			},
		})
			.then((resp) => {
				setWizardData({
					...wizardData,
					wizard_step: stps,
					form_map: { ...formMap, [form]: form_id },
					updated_on: resp.data.updated_on,
					updated_by_auto_name: resp.data.updated_by_auto_name,
				});
			})
			.catch((err) => {
				console.log(err);
			});
	};

	useEffect(() => {
		if (wizardTemplate.loading && !wizardTemplate.data?.length) {
			return;
		} else if (!wizardTemplate.loading && !wizardTemplate.data?.length) {
			setLoadingState("No Wizard Found");
			return;
		}

		const graphData = _.head(wizardTemplate.data)?.graph_data as GraphData;
		if (!props.wizardId) {
			let linkMap: DSLDrawLinkMap = {
				link: "",
				links: [],
				linkid: {},
			};
			if (props.linkMap) {
				linkMap = props.linkMap;
			} else if (props.patientId) {
				linkMap = {
					link: "patient",
					links: ["patient"],
					linkid: { patient: props.patientId },
				};
			}
			const wizardInProgress: IWorkflowWizard = {
				wizard_code: wizardTemplate.data[0].code as string,
				patient_id: props.patientId || null,
				link_ids: linkMap,
				prefill_data: {},
				graph_data: graphData,
				step_order: customStepOrder || graphData.node_order,
				wizard_step: [],
			};
			const form_map = {};
			const steps: IWorkflowWizardStep[] = [];
			_.forEach(graphData.node_order, (nc: string) => {
				_.forEach(graphData.nodes, (node) => {
					if (node.data.code == nc) {
						steps.push(node.data);
						if (node.data.type == "wizardForm") {
							form_map[node.data.dsl_form] = "";
							if (node.data.dsl_form == "patient" && props.patientId) {
								form_map[node.data.dsl_form] = props.patientId;
							} else if (presetFormIds[node.data.dsl_form]) {
								form_map[node.data.dsl_form] = presetFormIds[node.data.dsl_form];
							}
						}
					}
				});
			});
			wizardInProgress.wizard_step = steps;
			wizardInProgress.form_map = form_map;
			request({
				url: "/form/wf_wizard/",
				method: "POST",
				data: wizardInProgress,
			})
				.then((resp) => {
					if (!resp.success) {
						setLoadingState("Unexpected Error");
					}
					const data = resp.data as IWorkflowWizard;
					initialize(data.id);
				})
				.catch((err) => {
					console.log(err);
					setLoadingState("Unexpected Error");
				});
		} else {
			initialize(props.wizardId);
		}
	}, [wizardTemplate.loading]);

	const initialize = async (id: number) => {
		request({
			url: `/form/wf_wizard/${id}`,
			method: "GET",
		})
			.then((resp) => {
				const data: IWorkflowWizard = _.head(resp.data);
				if (!data) {
					setLoadingState("Unexpected Error");
				}
				const steps: IWorkflowWizardStep[] = [];
				_.forEach(data.step_order, (nc: string) => {
					_.forEach(data.wizard_step, (step) => {
						if (step.code == nc) {
							if (removeStepIfNoPreset && !presetData[step.dsl_form] && step.type == "wizardForm") {
								return;
							}
							steps.push(step);
						}
					});
				});
				setWizardData(data);
				setWizardSteps(steps);
				setFormMap(data.form_map);
				if (steps.length && !activeStep) {
					setActiveStep(steps[0].code);
				}
				setLoadingState("");
			})
			.catch((err) => {
				setLoadingState("Unexpected Error: Fetching Data");
			});
	};

	const onSaved = (fd, tab, ref) => {
		if (tab?.gridRef) {
			tab?.gridRef?.ddg?.reload?.();
		}
		ref.xid = fd.record;
		ref.record = fd.record;
		dslRefs[tab.dsl_form] = ref;
		ref.mode("edit", fd.record);
		updateStep(tab.code, fd.record);
	};
	const closeTab = (id: string, tab: TabData, ref?: unknown) => {};

	const changeMode = (mode: string, id: string, tab: TabData, ref?: unknown) => {};

	const rxOpts = {
		onSaved: onSaved,
		onCancel: closeTab,
		changeMode,
	};
	useEffect(() => {
		const index = wizardSteps.findIndex((v) => v.code == activeStep);
		if (index < 0) return;
		const step = wizardSteps[index];
		if (step.type == "wizardForm") {
			const sections = step?.dsl_form_section?.split(",").map((v) => v.trim()) || null;
			const dslRef = dslRefs[step.dsl_form];
			if (!dslRef) return;

			sectionShowOnly(dslRef, step.dsl_form, sections);
		}
	}, [activeStep, formMap, wizardSteps, wizardData, isFinalStep, isFirstStep, stepErrors]);

	if (loadingState == "Loading...") {
		return <SpinLoader fontSize={"2em"} />;
	}
	if (loadingState) {
		return <div className="cr-error-boundary">⚠️ {loadingState}</div>;
	}
	return (
		<div className="wizard-view">
			<div className="wizard-container">
				<div className="wizard-nav">
					{wizardSteps.map((step) => {
						const a = 5;
						return (
							<div
								key={step.code}
								onClick={() => {
									if (skipStep) {
										setActiveStep(step.code);
									}
								}}
								className={`wizard-nav-item ${step.code == activeStep ? "active" : ""}`}
							>
								<div className="wizard-label">{step.label}</div>

								{stepErrors[step.code] ? (
									<div className="wizard-error">
										<img src={wizardIcons.error} />
										<div className="error-count">{stepErrors[step.code]}</div>
									</div>
								) : (
									""
								)}
							</div>
						);
					})}
				</div>
				<div className="wizard-section">
					{wizardSteps.map((step) => {
						if (!window.DSL[step.dsl_form]) {
							return (
								<div
									key={step.code}
									className="dsl-container wizard-table-cont"
									style={{ display: step.code == activeStep ? undefined : "none" }}
								>
									DSL Not Found!
								</div>
							);
						}
						if (step.type == "wizardTable") {
							let compact_columns: string[] = [];
							if (step.wizard_table_column)
								compact_columns = step.wizard_table_column.split(",").map((v) => v.trim());
							const fields = Object.keys(window.DSL[step.dsl_form].fields);
							compact_columns = _.intersection(compact_columns, fields);
							if (!compact_columns.length) {
								return (
									<div
										key={step.code}
										className="dsl-container wizard-table-cont"
										style={{ display: step.code == activeStep ? undefined : "none" }}
									>
										Missing/Invalid Column List!
									</div>
								);
							}

							return (
								<div
									key={step.code}
									className="dsl-container wizard-table-cont"
									style={{ display: step.code == activeStep ? undefined : "none" }}
								>
									<DSLListCompactView
										form={step.dsl_form}
										customColumns={compact_columns}
										linkMap={wizardData?.link_ids}
										{...wizardData?.link_ids}
									/>
								</div>
							);
						}
						const formID = formMap[step.dsl_form] + "";
						if (fromRendered.includes(step.dsl_form)) {
							return null;
						} else {
							fromRendered.push(step.dsl_form);
						}
						const index = wizardSteps.findIndex((v) => v.code == activeStep);
						const sections = step?.dsl_form_section?.split(",").map((v) => v.trim()) || null;
						return (
							<div
								key={step.dsl_form + formID}
								className="dsl-container"
								style={{
									display:
										step.dsl_form == wizardSteps[index].dsl_form && step.type == "wizardForm"
											? undefined
											: "none",
								}}
							>
								<DSLCardView
									{...wizardData?.link_ids}
									disableRouting={true}
									key={step.dsl_form + formID}
									card={formID ? "edit" : "addfill"}
									xid={step.dsl_form}
									form={step.dsl_form}
									showSections={sections}
									preset={formID ? {} : presetData[step.dsl_form] || {}}
									tabData={step}
									autoRecoverEnabled={false}
									record={formID ? formID : undefined}
									ddRef={(ref) => {
										ref.xid = step.dsl_form;
										dslRefs[step.dsl_form] = ref;
										verify(step.code);
									}}
									{...rxOpts}
								/>
							</div>
						);
					})}
				</div>
			</div>
			<div className="wizard-bottom-nav">
				<div className="wizard-meta-info">
					<div className="meta-top">
						{wizardData?.id
							? `${
									wizardData.created_by_auto_name ||
									window.App.user.auto_name ||
									window.joinValid([window.App.user.lastname, window.App.user.firstname], ", ")
							  } started on ${window
									.moment(wizardData.created_on + " UTC")
									.format("MM/DD/YYYY hh:mm a")}`
							: ""}
					</div>
					{/* <div className="meta-bottom">
						{wizardData.updated_by ? `${wizardData?.updated_by_auto_name} updating since ${moment(wizardData.updated_on + " UTC").fromNow()}` : ""}
					</div> */}
				</div>
				<div className="wizard-controls">
					<div className={`ct-btn btn-prev ${isFirstStep ? "disabled" : ""}`} onClick={onClickPrevious}>
						<img src={wizardIcons.back} />
						<p>BACK</p>
					</div>
					<div className="ct-btn btn-verify" onClick={() => verify(activeStep)}>
						<img src={wizardIcons.verify} />
						<p>VERIFY</p>
					</div>
					<div className="ct-btn btn-next" onClick={onClickNext}>
						<img src={wizardIcons.forward} />
						<p>{isFinalStep ? "DONE" : "NEXT"}</p>
					</div>
				</div>
			</div>
		</div>
	);
};
WizardView.defaultProps = {
	removeStepIfNoPreset: false,
	skipStep: false,
	noFormSubmission: false,
	presetFormIds: {},
	preset: {},
};

export default WizardView;
