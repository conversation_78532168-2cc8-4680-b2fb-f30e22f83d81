@import "../dsl-card-view/mini-card-view.less";

.wizard-error {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.wizard-view {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .wizard-table-cont {
        padding: 10px;
        padding-top: unset;

    }

    .wizard-bottom-nav {
        flex: 0 1 60px;
        width: 100%;
        height: 100%;
        display: flex;
        border-radius: 8px;
        box-shadow: 0 1px 4px 0 rgba(24, 16, 10, 0.08);
        background-color: #FBFBFB;


        .wizard-meta-info {
            display: flex;
            // flex: 0 1 25%;
            gap: 10px;
            flex-direction: column;
            padding-left: 20px;
            justify-content: center;

            .meta-top {
                height: 17px;
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                letter-spacing: 0em;
                text-align: left;
                color: #9A9A9A;
            }

            .meta-bottom {
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                letter-spacing: 0em;
                text-align: left;
                color: #00AE8F;
            }
        }

        .wizard-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            justify-content: flex-end;
            gap: 15px;
            flex: 1;

            .ct-btn {
                cursor: pointer;
                justify-content: center;
                align-items: center;
                display: flex;
                flex-direction: column;
                gap: 2px;
                height: 50px;
                width: 56px;

                img {
                    height: 20px;
                    width: 20px;
                }

                p {
                    font-size: 11px;
                    font-weight: 600;
                    line-height: 13px;
                    letter-spacing: 0em;
                    text-align: left;
                }

            }


            .btn-prev,
            .btn-next {
                background: #53B2DE;
                color: white;
                border-radius: 5px;
            }

            .btn-verify {
                color: #747474;
            }
        }

    }

    .wizard-container {
        padding: 10px;
        display: flex;
        flex: 1;
        width: 100%;
        height: 100%;

        .wizard-nav {
            display: flex;
            flex-direction: column;
            margin-right: 10px;
            flex: 0 1 20%;
            overflow-y: auto;
            gap: 6px;


            .wizard-nav-item {
                display: flex;
                justify-content: space-between;
                padding: 10px 26px 10px 26px;
                align-items: center;
                width: 100%;
                min-height: 40px;
                letter-spacing: 0em;
                text-align: left;
                border-radius: 8px;
                box-shadow: 0 1px 4px 0 rgba(24, 16, 10, 0.08);
                background-color: #FBFBFB;
                font-size: 14px;
                line-height: 24px;
                color: #111111;


                &.active {
                    color: #254D5B;
                    font-weight: 600;
                }

                .wizard-error {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex: 0;
                    gap: 5px;

                    img {
                        height: 20px;
                        width: 20px;
                    }

                    .error-count {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 14px;
                        min-width: 17px;
                        font-weight: 500;
                        line-height: 17px;
                        letter-spacing: 0em;
                        text-align: left;
                        color: #FF6B6B;
                    }


                }

            }
        }

        .wizard-section {
            flex: 1;

            .form-container {
                margin-top: 0px !important;
            }

            .only-show-form-area;
        }
    }
}