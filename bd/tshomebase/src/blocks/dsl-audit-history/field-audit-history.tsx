import { useEffect, useState } from "react";
import { Popover } from "@mui/material";
import { request } from "@core/request/request";
import "./field-audit-history.less";
import LoaderNoData from "@components/common/loader-no-data";
import { renderItemData } from "@utils/fx";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import { AgGridReact } from "ag-grid-react";

interface FieldHistoryStateType {
	fd: string;
	data: any[];
}

const FieldAuditHistory = (props: any) => {
	const { el, pfm, fid, fld_id } = props;
	const [open, setOpen] = useState(true);
	const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
	const [fieldHistory, setFieldHistory] = useState<FieldHistoryStateType>({ fd: "", data: [] });
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		fetchFieldHistory();
	}, []);

	const fetchFieldHistory = () => {
		setIsLoading(true);
		const fd = el.attr("field");
		let rh: [];
		request({
			url: `/audit/log?form_name=${pfm}&form_id=${fid}&field_name=${fd}`,
			method: "GET",
		})
			.then((data) => {
				if (data?.data.length > 0) {
					rh = data?.data;
				}
			})
			.catch((err) => {
				console.log(err);
			})
			.finally(() => {
				setFieldHistory((prevState) => ({ ...prevState, data: rh, fd }));
				setIsLoading(false);
			});
	};

	const getFiledLabel = () => {
		const fd = el.attr("field");
		const fieldLabel = window?.DSL?.[pfm]?.fields?.[fd]?.view?.label;
		return fieldLabel;
	};

	return (
		<div
			className="field-history-container"
			key={fld_id}
			ref={(ref) => {
				setAnchorEl(ref);
			}}
		>
			{anchorEl ? (
				<Popover
					id={fld_id}
					open={open}
					anchorEl={anchorEl}
					onClose={() => {
						window.unloadComponent("FieldAuditHistory", fld_id);
						setOpen(false);
					}}
					style={{ zIndex: 9999999 }}
					anchorOrigin={{
						vertical: "top",
						horizontal: "right",
					}}
					PaperProps={{
						style: {
							width: "auto",
							height: "auto",
							borderRadius: "12px",
							maxHeight: "75%",
							maxWidth: "500px",
							padding: "5px 15px",
						},
					}}
					transformOrigin={{
						vertical: "top",
						horizontal: "left",
					}}
				>
					<GenericCardContainer
						containerStyle={{ minWidth: 400, maxWidth: 400 }}
						bodyStyle={{
							margin: 0,
						}}
						title={window.joinValid([getFiledLabel(), "History"], " ")}
					>
						{(fieldHistory.data?.length > 0 && (
							<div className="ag-theme-alpine" style={{ maxHeight: 400, height: 200, width: "100%" }}>
								<AgGridReact
									rowData={fieldHistory.data
										.map((item: any) => {
											let val;
											if (typeof item[fieldHistory?.fd] === "object") {
												val = item[fieldHistory.fd].map((i) => `${i}`).join(", ");
												if (item[fieldHistory.fd].length > 1) {
													const lastCommaIndex = val.lastIndexOf(", ");
													val =
														val.slice(0, lastCommaIndex) +
														" and" +
														val.slice(lastCommaIndex + 1);
												}
											} else {
												val = item[fieldHistory.fd];
											}
											let local_time =
												(item?.lfupdated_on && item.lfupdated_on) || item?.created_on;
											local_time = (local_time && window.dateTimetoLocal(local_time)) || "";
											let update_insert_by = `${item.lfdisplayname}`;

											return {
												value: renderItemData(val),
												user: renderItemData(update_insert_by),
												date: renderItemData(local_time, { format: "MM/DD/YYYY" }),
											};
										})
										.filter((item: any) => item.value)}
									columnDefs={[
										{ field: "date", headerName: "Date", flex: 1 },
										{ field: "user", headerName: "User", flex: 1 },
										{
											field: "value",
											headerName: "Value",
											flex: 2,
											cellRenderer: (params: any) => renderItemData(params.value),
										},
									]}
									defaultColDef={{
										sortable: false,
										filter: false,
										resizable: true,
									}}
								/>
							</div>
						)) || (
							<LoaderNoData
								loading={isLoading}
								text="No history found"
								loaderProps={{ fontSize: "2em" }}
							/>
						)}
					</GenericCardContainer>
				</Popover>
			) : null}
		</div>
	);
};

export default FieldAuditHistory;
