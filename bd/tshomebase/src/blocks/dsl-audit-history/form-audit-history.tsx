import React, { useEffect, useRef, useState } from "react";
import { Popover } from "@mui/material";
import { request } from "@core/request/request";
import "./form-audit-history.less";
import LoaderNoData from "@components/common/loader-no-data";
import { renderItemData } from "@utils/fx";

interface FormHistoryTypes {
	data: any[];
}

interface InitialHistoryPropsTypes {
	displayname: string;
	created_on: string | null;
	updated_on: string | null;
}

const FormAuditHistory = (props: any) => {
	const { form, fid, fld_id } = props;
	const [open, setOpen] = useState(true);
	const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
	const [formHistory, setFormHistory] = useState<FormHistoryTypes>({ data: [] });
	const [isLoading, setIsLoading] = useState(false);
	const [initialHistory, setInitialHistory] = useState<InitialHistoryPropsTypes>();

	useEffect(() => {
		fetchFormHistory();
	}, []);

	const fetchFormHistory = () => {
		setIsLoading(true);
		let rh: [];
		request({
			url: `/form/${form}/${fid}/log`,
			method: "GET",
		})
			.then((data) => {
				if (data?.data.length > 0) {
					rh = data?.data;
				}
				const iH = data?.data?.find(
					(item: { created_on: string | null; updated_on: string | null }) =>
						item?.updated_on === null && item?.created_on
				);
				setInitialHistory(iH);
			})
			.catch((err) => {
				console.log(err);
			})
			.finally(() => {
				setFormHistory((prevState) => ({ ...prevState, data: rh }));
				setIsLoading(false);
			});
	};

	const fillFormDataFlyout = (data: any) => {
		setIsLoading(true);
		const date_sys = `${JSON.parse(data.sys_period.slice(0, -1) + "]")[0].slice(0, -3)}`;
		request({
			url: `/form/${form}/${fid}?asof=${date_sys}`,
			method: "GET",
		})
			.then((res: any) => {
				let resData = null;
				if (res.data && res.data.length) {
					resData = res.data[0];
				}
				if (!resData) return;
				let local_time = (data?.updated_on && data.updated_on) || data?.created_on;
				data.show_audit_date = (local_time && window.dateTimetoLocal(local_time)) || "";
				data.show_audit_name = `${data.displayname}`;

				window.Flyout.open({
					form: form,
					card: "read",
					preset: resData,
					audit_mode: true,
					autoRecoverEnabled: false,
				});
			})
			.catch((err) => {
				console.log(err);
			})
			.finally(() => {
				setIsLoading(false);
				window.unloadComponent("FormAuditHistory", fld_id);
			});
	};

	return (
		<div
			className="form-history-con-inner"
			key={fld_id}
			ref={(ref) => {
				setAnchorEl(ref);
			}}
		>
			{anchorEl ? (
				<Popover
					id={fld_id}
					open={open}
					anchorEl={anchorEl}
					onClose={() => {
						window.unloadComponent("FormAuditHistory", fld_id);
						setOpen(false);
					}}
					anchorOrigin={{
						vertical: "top",
						horizontal: "right",
					}}
					style={{ zIndex: 9999999 }}
					PaperProps={{
						style: {
							width: "auto",
							height: "auto",
							borderRadius: "12px",
							maxHeight: "75%",
							maxWidth: "500px",
							padding: "5px 15px",
						},
					}}
					transformOrigin={{
						vertical: "top",
						horizontal: "right",
					}}
					className="form-history-poppover"
				>
					<div className="poppover-inner-content">
						<h4>History </h4>
						{(formHistory.data?.length > 0 &&
							formHistory.data?.map((item: any, idx) => {
								let local_time = (item?.updated_on && item.updated_on) || item?.created_on;
								local_time = (local_time && window.dateTimetoLocal(local_time)) || "";
								let update_insert_by = `${item.displayname}`;
								if (!update_insert_by || !local_time) {
									return;
								}
								return (
									<div
										key={idx}
										className="fh-cc frc gap-4 px-2"
										onClick={() => fillFormDataFlyout(item)}
									>
										<div className="frc gap-2">
											<p>{update_insert_by}</p>
										</div>
										<div className="frc gap-2">
											<p>{window.moment(local_time).format("MMMM DD YY")}</p>
										</div>
									</div>
								);
							})) || (
							<LoaderNoData
								loading={isLoading}
								text="No history found"
								loaderProps={{ fontSize: "2em" }}
							/>
						)}
						{initialHistory && Object.keys(initialHistory).length > 0 && (
							<>
								<h5>Initial Insert </h5>
								<div
									className="fh-cc frc gap-4 px-2"
									onClick={() => fillFormDataFlyout(initialHistory)}
								>
									<div className="frc gap-2">
										<p>{renderItemData(initialHistory?.displayname)}</p>
									</div>
									<div className="frc gap-2">
										<p>{window.moment(initialHistory?.created_on).format("MMMM DD YY")}</p>
									</div>
								</div>
							</>
						)}
					</div>
				</Popover>
			) : null}
		</div>
	);
};

export default FormAuditHistory;
