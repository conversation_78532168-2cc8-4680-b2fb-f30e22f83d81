import React from "react";
import "./dsl-export.less";
import "./dsl-list-view.responsive.less";
import list from "@public/icons/list";
import { DSLGridViewRef } from "../dsl-grid-view/dsl-grid-view";

interface GridDataExportProps {
	getGrid: () => DSLGridViewRef | null;
};
export const MinGridDataExport = (props: GridDataExportProps) => {
	const getGrid = props.getGrid;
	if (!getGrid) {
		console.warn("GridDataExport: missing grid prop");
		return;
	}
	return (

		<div className="min-grid-export">
			<button className="btn-export" onClick={() => { getGrid()?.doExport("copy"); }}>
				<p>Copy</p>
			</button>
			<button className="btn-export" onClick={() => { getGrid()?.doExport("csv"); }}>
				<p>CSV</p>
			</button>
			<button className="btn-export" onClick={() => { getGrid()?.doExport("excel"); }}>
				<p>Excel</p>
			</button>
			<button className="btn-export" onClick={() => { getGrid()?.doExport("pdf"); }}>
				<p>PDF</p>
			</button>
			<button className="btn-export bl-wl-btn" onClick={() => { getGrid()?.doPrint(); }}>
				<div>
					<img src={list.print} />
				</div>
				<p>Print</p>
			</button>
		</div>
	);
};

