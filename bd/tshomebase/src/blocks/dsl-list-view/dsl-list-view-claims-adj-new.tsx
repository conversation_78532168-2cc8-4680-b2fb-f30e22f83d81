import React, { useEffect, useState } from "react";
import "./dsl-list-view-status.less";
import "./dsl-list-view.responsive.less";
import { DSLListFindWidgets } from "@blocks/dsl-list-view/dsl-list-view";
import { getSelectFieldData, renderItemData } from "@utils/fx";
import { fetchFormCount } from "@hooks/form-data";

interface StatusItem {
	name: string;
	code: string;
}

interface DSLViewClaimAdjNewProps extends DSLListFindWidgets {
	form: string;
	field: string;
}

const DSLViewClaimAdjNew: React.FC<DSLViewClaimAdjNewProps> = (props) => {
	const { form, field, widgetRenderHash }: any = props;

	const [counts, setCounts] = useState<{
		[key: string]: number | string;
	}>({});
	const [active, setActive] = useState("");

	const statusOptionsAduj: StatusItem[] = [
		{ name: "Medical", code: "mm" },
		{ name: "Pharmacy", code: "ncpdp" },
		{ name: "Paper (1500)", code: "cms1500" },
		{ name: "<PERSON><PERSON>", code: "generic" },
	];

	const statusOptionsMedClaims: StatusItem[] = [
		{ name: "Status Report (277)", code: "277" },
		{ name: "Payer Response (835)", code: "835" },
	];

	const statusOptionsMap: { [key: string]: StatusItem[] } = {
		CTAN: statusOptionsAduj,
		CTAR: statusOptionsAduj,
		MED_RESP: statusOptionsMedClaims,
	};

	const statusOptions = statusOptionsMap[form] || [];

	// useEffect(() => {
	// 	statusOptions.forEach((item) => {
	// 		fetchFormCount(form, {
	// 			filter: { [field]: item.code },
	// 		})
	// 			.then((resp) => {
	// 				setCounts((prev) => {
	// 					let c = resp.count.toString();
	// 					if (c.length >= 3) {
	// 						c = "99+";
	// 					}
	// 					return {
	// 						...prev,
	// 						[item.code]: resp?.count ? c : "0",
	// 					};
	// 				});
	// 			})
	// 			.catch((error) => {
	// 				// Handle errors gracefully
	// 				console.error("Error fetching form count:", error);
	// 			});
	// 	});
	// }, [form, field]);

	useEffect(() => {
		const ref = props?.getListViewRefs?.();
		if (!ref) return;
		const filters = ref.advanced.filter.applied();
		setActive((filters[field] || "") as string);
	}, [widgetRenderHash]);

	return (
		<div className="dsl-list-statuses-container-wf">
			{statusOptions.map((item, index) => (
				<React.Fragment key={item.code}>
					<div
						className={`status-box-wf ${item.code === active ? "active-wf" : ""}`}
						onClick={() => {
							const ref = props?.getListViewRefs?.();
							if (!ref) {
								console.error("DSLListViewStatus: No list view ref found.");
								return;
							}

							if (item.code === active) {
								ref.advanced.filter.apply({});
								setActive("");
							} else {
								ref.advanced.filter.apply({ [field]: item.code });
								setActive(item.code);
							}
						}}
					>
						<p>{item.name}</p>
					</div>
					{index < statusOptions.length - 1 && <span className="menu-divider-wf">|</span>}
				</React.Fragment>
			))}
		</div>
	);
};

export default DSLViewClaimAdjNew;
