import React, { useEffect, useState } from "react";
import "./dsl-list-view-status.less";
import "./dsl-list-view.responsive.less";
import { DSLListFindWidgets } from "@blocks/dsl-list-view/dsl-list-view";
import { getSelectFieldData, renderItemData } from "@utils/fx";
import { fetchFormCount } from "@hooks/form-data";

interface StatusItem {
	id: string;
	code: string;
	auto_name: string;
	name : string;
}

interface DSLViewStatusProps extends DSLListFindWidgets {
	form: string;
	field: string;
	max?: number;
}

const DSLViewStatus: React.FC<DSLViewStatusProps> = (props) => {
	const { form, field, max, widgetRenderHash }: any = props;
	const [options, setOptions] = useState<StatusItem[]>([]);
	const [counts, setCounts] = useState<{
		[key: string]: number;
	}>({});
	const [sourceId, setSourceId] = useState("id");
	const [active, setActive] = useState("");

	useEffect(() => {
		let dslfm = window?.DSL?.[form]?.fields?.[field]?.model;

		const fetchData = async () => {
			const output = await getSelectFieldData(dslfm);
			if (output?.sourceid) {
				setSourceId(output?.sourceid);
			}
			setOptions(output.data?.slice(0, max || 3));
		};
		fetchData();
	}, []);

	useEffect(() => {
		const ref = props?.getListViewRefs?.();
		if (!ref) return;
		const filters = ref.advanced.filter.applied() || {};
		if (!filters) return;
		setActive((filters[field] || "") as string);
	}, [widgetRenderHash]);

	useEffect(() => {
		options.forEach((item) => {
			let value = item.id;
			if (sourceId == "code") {
				value = item.code;
			}
			fetchFormCount(form, {
				filter: { [field]: value },
			})
				.then((resp) => {
					setCounts((prev) => {
						let c = resp.count.toString();
						if (c.length >= 3) {
							c = "99+";
						}
						return {
							...prev,
							[item.code]: resp?.count ? c : "0",
						};
					});
				})
				.catch((error) => {
					//pass
				});
		});
	}, [options]);

	const getStatusClass = (item: any) => {
		switch (item.name) {
			case "Active":
				return "active";
			case "Pending":
				return "pending";
			case "Intake Complete":
				return "intake_complete";
			default:
				return "intake_complete";
		}
	};

	return (
		<div className="dsl-list-statuses-container">
			{options.map((item) => {
				return (
					<div
						key={JSON.stringify(item)}
						className={`status-box ${getStatusClass(item)}`}
						onClick={() => {
							let value = item.id;
							if (sourceId == "code") {
								value = item.code;
							}
							setActive(value);
							const ref = props?.getListViewRefs?.();
							if (!ref) {
								console.error("DSLListViewStatus: No list view ref not found.");
								return;
							}
							ref.advanced.filter.apply({ [field]: value });
							if (item.code == active || item.id == "active") {
								ref.advanced.filter.apply({});
								setActive("");
							}
						}}
					>
					<p>{renderItemData(item?.auto_name?.split("-")[1])}</p>
						<div key={JSON.stringify(item)} className="status-count">
							<span>{counts[item.code] || 0}</span>
						</div>
					</div>
				);
			})}
		</div>
	);
};

export default DSLViewStatus;
