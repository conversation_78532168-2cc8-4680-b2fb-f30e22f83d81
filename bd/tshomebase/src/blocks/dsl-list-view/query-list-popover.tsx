import React, { useEffect } from "react";
import "./list-popover.less";
import type { FC } from "react";
import { createPortalModal, type PopupModalRef } from "@blocks/portal-modal/portal-modal";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { CRErrorBoundary } from "@blocks/error-boundary";
import QueryListView from "@blocks/query-list-view/query-list-view";
import { openFlyoutFromQueryData } from "@utils/dsl-fx";
import { IFormData } from "@hooks/form-data";

type ListQueryViewerPopupConfig = {
	title: string;
	query: {
		code: string;
		parameters?: string | string[];
	};
};

interface ListQueryViewerPopupProps extends ListQueryViewerPopupConfig {
	getModal: () => PopupModalRef;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
	title: string;
}

export const ListQueryViewerPopup: FC<ListQueryViewerPopupProps> = (props) => {
	const { getModal, promise, title, query } = props;

	const onCancel = () => {
		getModal().closeModal();
		promise.resolve(null);
	};

	useEffect(() => {
		if (window._ListQueryViewerPopup) {
			if (window._ListQueryViewerPopup.onCancel) {
				try {
					window._ListQueryViewerPopup.onCancel();
				} catch (e) {}
			}
		}
		window._ListQueryViewerPopup = {
			onCancel,
		};
	}, []);

	return (
		<>
			<GenericCardContainer
				title={title || "List View"}
				className="list-viewer-popover"
				icon={icons.snap.patient.close}
				onClick={() => {
					onCancel();
				}}
			>
				<CRErrorBoundary>
					<QueryListView
						rowClicked={(row) => {
							openFlyoutFromQueryData(row.rowData as IFormData);
						}}
						code={query.code}
						parameters={Array.isArray(query.parameters) ? query.parameters.join("&") : query.parameters}
					/>
				</CRErrorBoundary>
			</GenericCardContainer>
		</>
	);
};

export const openListQueryPopover = (props: ListQueryViewerPopupConfig) => {
	return new Promise((resolve, reject) =>
		createPortalModal(
			ListQueryViewerPopup as FC<unknown>,
			{
				style: {
					overlay: {},
					content: {},
				},
			},
			{ ...props, promise: { resolve, reject } }
		)
	);
};
