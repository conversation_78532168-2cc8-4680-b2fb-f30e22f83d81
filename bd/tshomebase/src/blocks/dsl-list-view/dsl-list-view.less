@import (reference) '../../less/style/main.less';

.dsl-list-tab-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: auto;

    .non-popup {
        min-height: 90px;
        display: flex;
        min-height: fit-content;
        width: 100%;

        .find-view {
            border-radius: 8px !important;
            display: flex;
            flex-direction: column;

            .dsl-find-view {
                flex-direction: column;

                .findbar .findtab {

                    .findbasic,
                    .findadvanced {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: flex-start;
                    }
                }
            }

            .find-action-btn {
                .action {
                    flex-direction: row;

                    .filter-btn {
                        .btn-primary;
                    }

                    button {
                        .btn-primary;
                    }
                }
            }


        }
    }

    >div {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .dsl-list-top {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        justify-content: space-between;
        border-radius: 10px 10px 0 0 !important;
        padding: 16px!important;
        background-color: rgba(250, 250, 250, 0.85) !important;
        .table-status-filter-header;
        min-height: auto;

        &.has-left-sharp-corner {
            border-top-left-radius: 0px;
        }

        >div {
            display: flex;
            flex-direction: row;
            gap: 10px;
            align-items: center;
        }

        .dsl-grid-top-bar-left {
            flex: 1;
        }

        .grid-top-bar {
            display: flex;
            flex: 1;

            .grid-label {
                font-size: 14px;
                font-weight: 600;
                display: flex;
                text-transform: uppercase;
                justify-content: center;
                align-items: center;
                margin-right: 10px;
            }

            .search-bar {
                display: flex;
                gap: 8px;
                flex: 1;
                align-items: center;
                height: 40px !important;
                font-weight: 400px !important;
                background-color: #FFFFFF !important;
                max-width: 40%;
                .search-box-one;
                padding: 8px 12px;
                border-radius: 8px;
                border: 1px solid #E5E5E5;
                box-shadow: 0px 1px 2px -1px #0000000A,
                    0px 0px 1px 0px #0000000F;

                >div {
                    flex: 1;

                    input {
                        width: 100%;
                        background-color: #FFFFFF !important;
                        .search-box-one;
                        border: none;

                        &::placeholder {
                            color:var(--color-text);
                        }

                        &:focus {
                            border: none;
                        }


                    }
                }

                img {
                    width: 20px;
                    height: 18px;
                    cursor: pointer;
                }
            }

            .highlight {
                .form-input-field-focus;
            }
        }


        .wgdts-container{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin: 0 8px;

            .icon-download::before {
                content: '\e094' !important; 
                .font-icon
            }

            .icon-upload::before {
                content: '\f093' !important;
                .font-icon
            }
            .icon-fax::before {
                content: '\f1ac' !important;
                .font-icon
            }
            .icon-print::before {
                content: '\f02f' !important;
                .font-icon
            }

            .filter-act-btn {
                display: flex;
                user-select: none;
                flex-direction: row;
                align-items: center;
                background: #f8f5fc;
                cursor: pointer;
                border-radius: @border-radius-full;
                i {
                    font-size: 18px;
                    color: var(--color-primary);
                }
            }
    
            .filter-collapsed {
                .add-btn {

                    cursor: pointer;

                    height: 36px;
                    gap: 4px;
                    padding-top: 8px;
                    padding-right: 12px;
                    padding-bottom: 8px;
                    padding-left: 12px;
                    border-radius: 8px;
                    background: var(--color-primary);
                    box-shadow: 0px -1px 2px 0px #00000038 inset;
                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset;

                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    letter-spacing: 0%;
                    color: #FFFFFF;

                }

                .add-refresh {
                    content: '\f2f9' !important; 
                    .font-icon
                }
    
                .filter-applied {
                    display: flex;
                    width: 10px;
                    height: 10px;
                    background: red;
                    position: absolute;
                    z-index: 1;
                    border-radius: 10px;
                    flex-shrink: 0;
                    margin-right: -25px;
                    margin-top: -25px;
                }
    
            }
    
            .filter-visible {
                border-radius: 35px 35px 0px 0px;
    
                .filter-applied {
                    display: none;
                }
    
                &::after {
                    content: "";
                    display: flex;
                    z-index: 1;
                    position: absolute;
                    margin-top: 45px;
                    background: #f8f5fc;
                    width: 35px;
                    height: 12px;
                }
    
                .close-btn {
                    font-size: 18px;
                    transform: rotate(45deg);
                    font-weight: 600;
                }
            }
        }

    }

    .dsl-list-export {
        height: auto;
        width: 100%;
        margin-top: -25px;

        .data-grid-bottom-drawer {
            &.expanded {
                width: calc(100% - var(--dsl-list-bottom-p) * 2) !important;
            }
        }
    }

    .dsl-list-bottom {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 160px;
        gap: 10px;
        position: relative;
        border-radius: 0 0 10px 10px !important;
        box-shadow: none !important;
        background-color: rgba(250, 250, 250, 0.85) !important;
        padding: 0 16px !important;
        padding-bottom: 16px !important;
        .table-contaner
    }

    @media (max-width: 768px) {
        .dsl-list-export {
            .min-grid-export {
                justify-content: space-around;
            }
        }
    }
}