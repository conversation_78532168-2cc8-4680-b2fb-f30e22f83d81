import React, { useEffect, useRef } from "react";
import "./list-popover.less";
import type { FC } from "react";
import { createPortalModal, type PopupModalRef } from "@blocks/portal-modal/portal-modal";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import { DSLListView, DSLListViewProps } from "@blocks/dsl-list-view/dsl-list-view";
import { CRErrorBoundary } from "@blocks/error-boundary";
import { getFormLabel, toPresentableLabel, uuid } from "@utils/fx";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";

type ListViewerPopupConfig = {
	listConfig: DSLListViewProps;
	title: string;
	tabViewActions?: PatientTabActions;
	addConfig?: {
		form: string;
		preset?: Record<string, unknown>;
		linkMap?: DSLDrawLinkMap | null | undefined;
	};
};

interface ListViewerPopupProps extends ListViewerPopupConfig {
	getModal: () => PopupModalRef;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
	title: string;
}

export const ListViewerPopup: FC<ListViewerPopupProps> = (props) => {
	const { getModal, promise, title, listConfig, tabViewActions, addConfig } = props;

	const onCancel = () => {
		getModal().closeModal();
		promise.resolve(null);
	};
	const listViewRef = useRef<AdvancedGridRef | null>(null);

	useEffect(() => {
		if (window._ListViewerPopup) {
			if (window._ListViewerPopup.onCancel) {
				try {
					window._ListViewerPopup.onCancel();
				} catch (e) {}
			}
		}
		window._ListViewerPopup = {
			onCancel,
		};
	}, []);

	const {
		form,
		filtersPresetFixed = {},
		sort = {
			direction: "desc",
			property: "id",
		},
		canAdd,
		linkMap = {
			link: "",
			links: [""],
			linkid: {},
		},
	} = listConfig;

	let { form: addForm, preset: addPreset = {}, linkMap: addLinkMap } = addConfig || {};
	if (!addForm) {
		addForm = form;
	}
	if (!addLinkMap) {
		addLinkMap = linkMap;
	}

	return (
		<>
			<GenericCardContainer
				title={getFormLabel(form) || "List View"}
				className="list-viewer-popover"
				icon={icons.snap.patient.close}
				onClick={() => {
					onCancel();
				}}
			>
				<CRErrorBoundary>
					<DSLListView
						form={form || ""}
						filtersPresetFixed={filtersPresetFixed || {}}
						sort={sort}
						canAdd={canAdd || false}
						removeCanAdd={!canAdd}
						disableRouting={true}
						label=""
						linkMap={listConfig.linkMap}
						refCallback={(ref) => {
							listViewRef.current = ref;
						}}
						{...linkMap}
						rowClicked={(opts) => {
							window.Flyout.open({
								record: opts.id,
								form: form,
								card: "read",
								tab_can_edit_override() {
									return false;
								},
								tab_can_archive_override() {
									return false;
								},
							});
						}}
						onAdd={() => {
							try {
								if (tabViewActions?.openTab) {
									//(id: string | number, label: string, mode: string, form: string, componentProps: Record<string, unknown>, renderComponent?: unknown, extraKV?: Record<string, unknown>)
									const label = getFormLabel(form) || "New " + toPresentableLabel(form);
									tabViewActions.openTab(uuid(), "New " + label, "addfill", form, {
										linkMap: linkMap,
										preset: addPreset,
										autoRecoverEnabled: false,
										...addLinkMap,
									});
									onCancel();
									return;
								}
							} catch (e) {}
							const dfd = window.Flyout.open({
								form: form,
								autoRecoverEnabled: false,
								preset: addPreset,
								...addLinkMap,
							});
							dfd.done((data) => {
								listViewRef.current?.advanced.row("add", data);
							});
						}}
					/>
				</CRErrorBoundary>
			</GenericCardContainer>
		</>
	);
};

export const openListPopover = (props: ListViewerPopupConfig) => {
	return new Promise((resolve, reject) =>
		createPortalModal(
			ListViewerPopup as FC<unknown>,
			{
				style: {
					overlay: {},
					content: {},
				},
			},
			{ ...props, promise: { resolve, reject } }
		)
	);
};
