import type { FC } from "react";
import React, { useRef, useState } from "react";
import "./dsl-list-view.less";
import "./dsl-list-view.responsive.less";
import { useNavigation, UseNavigationProps } from "@core/navigation";
import list from "@public/icons/list";
import { uuid } from "@utils/fx";
import { getNewMode } from "@utils/dsl-fx";
import { joinPaths } from "@utils/fx";
import _ from "lodash";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";
import { AdditionTabItem } from "../tab-list/tab-list";
import { useUpdateHash } from "@hooks/update-hash";
import { useFormConfig } from "@dsl-form/config";
import {
	AdvancedGridRef,
	DSLAdvancedGrid,
	DSLAdvancedGridProps,
	GridRowClickedEvent,
} from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import { DSLGridOnRowEventCallBack } from "@blocks/dsl-grid-view";
import { AgGridPrint, exportDataAsCsv, exportDataAsExcel, copyVisibleRows } from "@utils/custom-widgets";
import { Tooltip } from "@mui/material";
import { getTooltipStyles } from "@utils/style-helper";
import { IFormData } from "@hooks/form-data";

export interface DSLListFindWidgets extends AdditionTabItem {
	getListViewRefs?: () => AdvancedGridRef | null | undefined;
	widgetRenderHash: string;
}

export type DSLListRowClickedCallback = DSLGridOnRowEventCallBack;

export type DSLListOnAddCallbackParams = DSLListViewProps & {
	gridRef: AdvancedGridRef | null;
};

export type DSLListOnAddCallback = (props?: DSLListOnAddCallbackParams) => void;

export type DSLListWidgetProps = DSLListViewProps & {
	getSelectedRows: () => IFormData[];
	gridRef: AdvancedGridRef | null;
	[key: string]: any;
};

export interface DSLListViewProps extends DSLAdvancedGridProps, Partial<RoutedComponentProps> {
	canAdd?: boolean;
	removeCanAdd?: boolean;
	onAdd?: DSLListOnAddCallback;
	addButtonLabel?: string;

	disableRouting?: boolean;
	canPrint?: boolean;
	canDownload?: boolean;
	routePrepend?: string;
	newPreset?: Record<string, unknown>;

	noGridSearchBar?: boolean;
	findWidgets?: DSLListFindWidgets[];
	label?: string;
	listWidget?: React.ComponentType<DSLListWidgetProps>;
	listWidgetProps?: Record<string, any>;

	rowClicked?: GridRowClickedEvent;
	refCallback?: (ref: AdvancedGridRef | null) => void;
	openTab?: (id: string, title: string, mode: string, form: string, linkMap: DSLDrawLinkMap | {}) => void;
}

export const DSLListView: FC<DSLListViewProps> = (props) => {
	const {
		form,
		canPrint = true,
		canDownload = true,
		label,
		disableRouting,
		addButtonLabel,
		routePrepend,
		linkMap,
		hideFilter,
		openTab,
		onAdd,
		onRef,
		rowClicked,
		noGridSearchBar = false,
		findWidgets = [],
		customColumnDefs,
		...rest
	} = props;

	if (!disableRouting) useNavigation(props as UseNavigationProps, joinPaths(routePrepend || "", "/"));

	const gridRef = useRef<AdvancedGridRef | null>(null);
	const linksStruct = linkMap || {};
	const [widgetRenderHash, updateWidgetRenderHash] = useUpdateHash();
	const [search, setSearch] = useState("");
	const [highlight, setHighlight] = useState(false);
	const formConfig = useFormConfig(form);

	if (!form || !window.DSL[form]) {
		return null;
	}

	const onEnter = () => {
		gridRef.current?.advanced.search(search.trim());
	};

	const getListViewRefs = () => {
		return gridRef.current;
	};

	const onClickAdd = () => {
		if (props.onAdd) {
			props.onAdd({
				...props,
				gridRef: gridRef.current,
			});
			return;
		}
		getNewMode(form)
			.then((mode: unknown) => {
				const m = mode as string;
				props.openTab?.(uuid(), "New " + window.DSL[form].view.label, m, form, {
					...(props.linkMap || {}),
				});
			})
			.catch((error) => {
				console.log(error);
			});
	};

	const addLabel = props.addButtonLabel || "+ Add New";

	const renderFindWidgets = (direction: "left" | "right") => {
		if (!findWidgets) return null;
		return findWidgets.map((item, index) => {
			if (direction === "left") {
				if (item.position === "right") return null;
			} else {
				if (item.position !== "right") return null;
			}
			const FindWidget = item.component as FC<DSLListFindWidgets>;
			return (
				<FindWidget
					key={index}
					{...item}
					{...(item.componentProps || {})}
					widgetRenderHash={widgetRenderHash}
					getListViewRefs={getListViewRefs}
				/>
			);
		});
	};

	const renderSearchBar = () => {
		if (noGridSearchBar) return null;
		return (
			<div className={`search-bar ${highlight ? "highlight" : ""}`}>
				<img src={list.magnifier} />
				<div>
					<input
						placeholder="Search..."
						value={search}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								onEnter();
							}
						}}
						onFocus={(e) => setHighlight(true)}
						onBlur={() => setHighlight(false)}
						onChange={(e) => {
							setSearch(e.target.value);
							gridRef.current?.advanced.search(e.target.value.trim());
						}}
					/>
				</div>
			</div>
		);
	};

	const clickPrint = () => {
		const get_ref = gridRef?.current?.api;
		if (!get_ref) {
			console.error("Grid API is not initialized.");
			return;
		}
		window.BootstrapDialog.show({
			title: "Select Print View",
			message:
				"Do you want to print the list in a grid layout, all the individual forms in detail, or a blank template form?",
			type: window.BootstrapDialog.TYPE_INFO,
			buttons: [
				{
					label: "Print List",
					action: (dialog: any) => {
						dialog.close();
						AgGridPrint(gridRef);
						return;
					},
				},
				{
					label: "Print All Forms",
					action: (dialog: any) => {
						dialog.close();
						AgGridPrint(gridRef);
						return;
					},
				},
				{
					label: "Print Blank Templete",
					action: (dialog: any) => {
						dialog.close();
						AgGridPrint(gridRef);
						return;
					},
				},
			],
		});
	};

	const clickDownload = () => {
		const get_ref = gridRef?.current?.api;
		if (!get_ref) {
			console.error("Grid API is not initialized.");
			return;
		}
		window.BootstrapDialog.show({
			title: "Please Select",
			type: window.BootstrapDialog.TYPE_SUCCESS,
			message: "Please select which type of file you want to download",
			buttons: [
				{
					label: "Download Excel",
					action: (dialog: any) => {
						dialog.close();
						exportDataAsExcel(gridRef);
						return;
					},
				},
				{
					label: "Download Csv",
					action: (dialog: any) => {
						dialog.close();
						exportDataAsCsv(gridRef);
						return;
					},
				},
				{
					label: "Copy",
					action: (dialog: any) => {
						dialog.close();
						copyVisibleRows(gridRef);
						return;
					},
				},
			],
		});
	};

	const clickFax = () => {
		console.log("clickFax");
	};

	const handleSelectedRow = () => {
		return gridRef.current?.advanced.selectedRows() || [];
	};

	return (
		<div className="dsl-list-tab-container">
			<div className={`dsl-list-top`}>
				<div className="grid-top-bar">
					{/* {label && <div className="grid-label">{label}</div>} */}
					{renderSearchBar()}
					{renderFindWidgets("left")}
				</div>
				<div className="wgdts-container">
					{props.form.startsWith("__wf_queue_intake_fax_") && (
						<Tooltip
							arrow
							title={"Send Fax"}
							componentsProps={getTooltipStyles("primary", "var(--color-primary)")}
						>
							<div className="icon-fax" onClick={clickFax}></div>
						</Tooltip>
					)}
					{canDownload && (
						<Tooltip
							arrow
							title={"Download"}
							componentsProps={getTooltipStyles("primary", "var(--color-primary)")}
						>
							<div className="icon-download" onClick={clickDownload}></div>
						</Tooltip>
					)}
					{canPrint && (
						<Tooltip
							arrow
							title={"Print"}
							componentsProps={getTooltipStyles("primary", "var(--color-primary)")}
						>
							<div className="icon-print" onClick={clickPrint}></div>
						</Tooltip>
					)}
					<Tooltip
						arrow
						title={"Refresh"}
						componentsProps={getTooltipStyles("primary", "var(--color-primary)")}
					>
						<div
							className="filter-act-btn filter-collapsed"
							onClick={() => {
								gridRef.current?.advanced.refresh();
							}}
						>
							<div className="add-refresh">↻</div>
						</div>
					</Tooltip>
					{props.listWidget && (
						<props.listWidget
							{...props}
							{...(props.listWidgetProps || {})}
							getSelectedRows={handleSelectedRow}
							gridRef={gridRef.current}
						/>
					)}
					{window.Auth.can_create(form) &&
						formConfig.allowAdd &&
						(props.canAdd || props.onAdd) &&
						!props.removeCanAdd && (
							<div className="filter-act-btn filter-collapsed" onClick={onClickAdd}>
								<div className="add-btn">{addLabel}</div>
							</div>
						)}

					{formConfig.listWidget && (
						<formConfig.listWidget
							{...props}
							{...formConfig.listWidgetProps}
							getSelectedRows={handleSelectedRow}
							gridRef={gridRef.current}
						/>
					)}
				</div>
			</div>
			<div className="dsl-list-bottom">
				<CRErrorBoundary>
					<DSLAdvancedGrid
						{...props}
						{...linksStruct}
						selectedHighlight={props.selectedHighlight}
						keyField={props.keyField}
						hideFilter={hideFilter}
						form={form}
						customColumnDefs={customColumnDefs}
						onRef={(r) => {
							gridRef.current = r;
							props.refCallback?.(getListViewRefs());
						}}
						rowClicked={(row, event) => {
							props.rowClicked?.(row, event as any);
						}}
					/>
				</CRErrorBoundary>
			</div>
		</div>
	);
};
