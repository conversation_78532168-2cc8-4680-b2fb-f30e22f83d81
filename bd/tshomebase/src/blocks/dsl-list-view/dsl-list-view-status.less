@import (reference) '../../less/style/main.less';

.dsl-list-statuses-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    padding-left: @padding-10;
    justify-content: center;
    flex:1;

    .status-box {
        display: flex;
        flex-direction: row;
        gap: 5px;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .status-count {
            .table-status-count;
        }

        .status-count {
            background-color: transparent;
        }

        p {
            .table-status-text-2;
        }

        &.active {
            p {
                color: #76AD9B;
            }
            .status-count {
                background-color: transparent;
                span {
                    .table-status-text-span
                }
            }
        }

        &.pending {
            p {
                color: var(--color-warning);
            }
            .status-count {
                background-color: transparent;
                span {
                    .table-status-text-span
                }
            }

        }

        &.intake_complete {
            p {
                color: #838894;
            }
            .status-count {
                background-color: transparent;
                span {
                    .table-status-text-span
                }
            }

        }


        // &:nth-child(3) {
        //     .status-count {

        //         background-color: @soft-light-gray;


        //     }

        // }

        p {
            .table-status-text;
        }
    }

}


.dsl-list-statuses-container-wf {
    display: flex;
    align-items: center;
    justify-content: center;
    // font-family: Arial, sans-serif;
    // padding: 10px;
    gap: 5px; 
}

.status-box-wf {
    cursor: pointer;
    // padding: 5px 10px;
    font-size: 11px;
    color: black;
    transition: color 0.3s, background-color 0.3s;
    text-align: center;
    border-radius: 5px;
}

// .status-box-wf:hover {
//     background-color: #eaeaea; 
// }

.status-box-wf.active-wf {
    // font-weight: bold;
    color: #6200ea; 
}

.menu-divider-wf {
    color: #ccc; 
    font-size: 16px;
    margin: 0 5px; 
}
