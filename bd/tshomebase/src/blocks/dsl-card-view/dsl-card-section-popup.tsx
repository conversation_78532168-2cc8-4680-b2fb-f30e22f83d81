import React, { useEffect, useRef } from "react";
import type { FC } from "react";
import { createPortalModal, type PopupModalRef } from "../portal-modal/portal-modal";
import "./dsl-card-section-popup.less";
import GenericCardContainer from "@components/cards/generic-card-container/generic-card-container";
import icons from "@public/icons";
import DSLCardView, { DSLCardViewRef } from "@blocks/dsl-card-view/dsl-card-view";
import { sectionShowOnly, updateSelectOptions } from "@utils/dsl-fx";
import { request } from "@core/request/request";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { IFormData } from "@hooks/index";

interface PartialFormFillPopupProps {
	form: string;
	sections: string[];
	className?: string;
	forceSingleColumn?: boolean;
	label?: string;
	preset?: object;
	btnLabels?: {
		save?: string;
		cancel?: string;
	};
	suppressCrossBtn?: boolean;
	btnConfig?: {
		save?: {
			className?: string;
			onClick?: (
				props: DSLCardSectionPopUpProps,
				formRef: DSLCardViewRef,
				fd: IFormData | false,
				close?: boolean
			) => void;
		};
		cancel?: {
			className?: string;
			onClick?: (
				props: DSLCardSectionPopUpProps,
				formRef: DSLCardViewRef,
				fd: IFormData | false,
				close?: boolean
			) => void;
		};
	};
	saveUrl?: string;
	record?: string | number;
	linkMap?: DSLDrawLinkMap;
	containerStyle?: React.CSSProperties;
	bodyStyle?: React.CSSProperties;
	headerStyle?: React.CSSProperties;
	fullRender?: boolean;
	selectOptions?: { fieldName: string; options: string[] };
	mode?: "edit";
	forceReadOnly?: boolean;
}
interface DSLCardSectionPopUpProps extends PartialFormFillPopupProps {
	getModal: () => PopupModalRef;
	promise: {
		resolve: (data: any) => void;
		reject: (reason: any) => void;
	};
}

export const DSLCardSectionPopUp: FC<DSLCardSectionPopUpProps> = (props) => {
	const {
		getModal,
		promise,
		form,
		suppressCrossBtn,
		sections,
		label,
		btnLabels,
		saveUrl,
		linkMap,
		preset,
		fullRender,
		containerStyle,
		bodyStyle,
		headerStyle,
		btnConfig,
		selectOptions,
	} = props;

	const view = useRef<any>({});

	const onCancel = (close?: boolean) => {
		if (btnConfig?.cancel?.onClick) {
			btnConfig.cancel.onClick(props, view.current, false, typeof close === "boolean" ? true : false);
			return;
		}
		getModal().closeModal();
		promise.reject("close");
	};

	const onSave = () => {
		const val = view.current?.cardform.ddf.verify();
		if (btnConfig?.save?.onClick) {
			btnConfig.save.onClick(props, view.current, val);
			return;
		}
		if (!val) return;
		if (props.mode === "edit" && (props.record || saveUrl)) {
			window.prettyNotify("Saving...");
			request({
				url: saveUrl ? saveUrl : `/form/${form}/${props.record}`,
				method: "PUT",
				data: val,
			})
				.then((res) => {
					getModal().closeModal();
					promise.resolve(res.data);
				})
				.catch((err) => {
					console.error(err);
					window.prettyError("Error saving", err?.error || err?.message);
				})
				.finally(() => {
					window.prettyNotify();
				});
		} else if (saveUrl) {
			window.prettyNotify("Saving...");
			request({
				url: saveUrl,
				method: "POST",
				data: val,
			})
				.then((res) => {
					getModal().closeModal();
					promise.resolve(res.data);
				})
				.catch((err) => {
					console.error(err);
					window.prettyError("Error saving", err?.error || err?.message);
				})
				.finally(() => {
					window.prettyNotify();
				});
		} else {
			getModal().closeModal();
			delete val._meta;
			promise.resolve(val);
		}
	};

	const onLoad = (ref: any) => {
		ref.cardform.cancel = onCancel;
		ref.cardform.tab_do_cancel = onCancel;
		ref.cancel = onCancel;
		ref.tab_can_cancel_override = () => true;
		ref.cardform.save = onSave;
		ref.cardform.tab_do_save = onSave;
		ref.save = onSave;
		if (selectOptions && selectOptions.options.length > 0) {
			setTimeout(() => {
				try {
					updateSelectOptions(ref, form, selectOptions.fieldName, selectOptions.options);
				} catch (error) {
					console.error(`Error updating select options:`, error);
				}
			}, 200);
		}
	};
	useEffect(() => {
		if (!form || !window.DSL[form]) {
			console.error("Form not found");
			getModal().closeModal();
			promise.reject(null);
		}
	}, []);

	if (!form || !window.DSL[form]) {
		return null;
	}
	const title = label || window.DSL[form].view.label;
	return (
		<GenericCardContainer
			title={title}
			containerStyle={containerStyle}
			bodyStyle={{ display: "flex", flexDirection: "column", ...bodyStyle, marginTop: "0px" }}
			headerStyle={headerStyle}
			className={`dsl-card-section-popup${sections && sections.length == 1 ? " single-section" : ""} ${
				props.className || ""
			} ${props.forceSingleColumn ? "force-single-column" : ""}`.replaceAll("  ", " ")}
			onClick={
				suppressCrossBtn
					? undefined
					: () => {
							onCancel(true);
					  }
			}
			icon={suppressCrossBtn ? undefined : icons.common.crossIcon}
		>
			<DSLCardView
				form={form}
				card="addfill"
				preset={{ ...(preset || {}) }}
				{...(linkMap || {})}
				showSections={sections}
				disableRouting={true}
				allowModal={true}
				forceReadOnly={props.forceReadOnly}
				ddRef={(ref) => {
					view.current = ref;
					onLoad(ref);
					if (fullRender) return;
					sectionShowOnly(ref, form, sections);
					try {
						// Incase of race condition, we need to wait for the card to be ready
						const idx = ref.subscribe("ready", (cardView: DSLCardViewRef) => {
							setTimeout(() => {
								sectionShowOnly(cardView, form, sections);
							}, 0);
							cardView.unsubscribe("ready", idx);
						});
					} catch (error) {
						console.error("Error subscribing to ready event", error);
					}
				}}
				overrideDos={{
					save: {
						label: btnLabels?.save || "Save",
						class: btnConfig?.save?.className,
						enabled: btnLabels?.save !== "Disable",
					},
					cancel: {
						label: btnLabels?.cancel || "Close",
						class: btnConfig?.cancel?.className,
						enabled: btnLabels?.cancel !== "Disable",
					},
					verify: {
						enabled: false,
					},
				}}
			/>
		</GenericCardContainer>
	);
};

export const openPartialFormFillPopup = (props: PartialFormFillPopupProps) => {
	const style = {
		content: {
			display: "flex",
			justifyContent: "center",
			height: "100%",
			width: "100%",
			position: "unset !important",
			inset: "unset !important",
			border: "unset !important",
			background: "unset !important",
			overflow: "auto !important",
			borderRadius: "unset !important",
			outline: "unset !important",
			padding: "unset !important",
			zIndex: "9999",
			alignItems: "center",
		},
		overlay: {
			zIndex: "9999",
			backgroundColor: "rgba(67, 67, 67, 0.50)",
		},
	};
	return new Promise((resolve, reject) =>
		createPortalModal(DSLCardSectionPopUp as FC<unknown>, { style }, { ...props, promise: { resolve, reject } })
	);
};
