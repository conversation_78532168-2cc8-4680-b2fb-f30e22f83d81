#application {
    .form-group {
        .btn-group {
            background: #00000005; // color not found
            box-shadow: 0px 1px 2px 0px #0000001F inset;
            border-radius: var(--radius-medium);
            padding: var(--spacing-xsmall);

            .btn {
                padding: var(--spacing-large) var(--spacing-xxlarge);
                font-size: var(--font-size-small);
                color: var(--color-tertiary);
                border: none;
                background: transparent;
                min-height: 32px;
                line-height: 12px;

                &.active {
                    color: #FFFFFF; // color not found
                    background-color: var(--color-tertiary);
                    font-weight: 600;
                    border-radius: var(--radius-small);
                }

                &:last-child {
                    margin: 0px !important;
                }
            }

            .btn+.btn {
                margin: 0px;
            }
        }

        &.has-error {
            .btn-group {
                box-shadow: 0px 1px 2px 0px #0000001F inset, 0px 0px 0px 2px #E18585;

                .btn {
                    color: var(--color-error);

                    &.active {
                        color: #FFFFFF; // color not found
                        background-color: var(--color-error);
                    }
                }
            }
        }

        .btn-group:has(.disabled) {
            background-color: transparent;
            box-shadow: none;
            cursor: not-allowed;

            * {
                cursor: not-allowed;
            }

            .btn {
                color: #9B9FA8 !important;

                &.active {
                    color: #FFFFFF !important; // color not found
                    background-color: #9B9FA8; // color not found
                }

                &.disabled {
                    background-color: transparent !important;
                }
            }
        }
    }
}