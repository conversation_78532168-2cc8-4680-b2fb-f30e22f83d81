#application {
    .form-horizontal {
        .form-group {

            .controls,
            .controls-grid {

                &.control-nonfocusable {
                    .select2-container.select2-allowclear {
                        .select2-choice abbr.select2-search-choice-close {
                            display: none;
                        }
                    }
                }

                .select2-container,
                .select2-container-multi {
                    margin: 0;
                    position: relative;
                    display: inline-block;
                    vertical-align: middle;

                    .select2-choice abbr,
                    .select2-search-choice-close {
                        display: none;
                        position: absolute;
                        outline: none;
                        background-image: url('../../../public/icons/common/outlined/x-close-outline.svg');
                        background-repeat: no-repeat;
                        background-position: center;
                        right: 30px;
                        width: 12px;
                        height: 12px;
                        padding: var(--spacing-large);
                        text-decoration: none;
                        border: 0;
                        cursor: pointer;
                    }

                    .select2-choice,
                    .select2-choices {
                        display: block;
                        width: 100%;
                        border: none;
                        outline: 1px solid var(--gray-100);
                        min-height: 60px;
                        border-radius: var(--radius-medium);
                        padding: var(--spacing-large);
                        padding-top: 30px;
                        line-height: 0px;
                        box-sizing: border-box;
                        color: var(--gray-700);
                        background-color: var(--white);
                        font-size: var(--font-size-small);
                        font-weight: var(--font-weight-semibold);
                        box-shadow: 0 -0.5px 3px 1px #38383814;
                        text-decoration: none;
                        overflow: hidden;
                        position: relative;
                        white-space: nowrap;
                        background-clip: padding-box;
                        -webkit-touch-callout: none;
                        -webkit-user-select: none;
                        -moz-user-select: none;
                        -ms-user-select: none;
                        user-select: none;

                        .select2-arrow {
                            display: inline-block;
                            height: 100%;
                            position: absolute;
                            top: 0;
                            background-image: url('../../../public/icons/common/outlined/chevron-down-outline.svg') !important;
                            background-repeat: no-repeat !important;
                            background-position: center;
                            background-clip: padding-box;
                            width: 10px;
                            right: var(--spacing-xxlarge);
                        }

                        >.select2-chosen {
                            display: block;
                            overflow: hidden;
                            padding: 10px 0px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            float: none;
                            width: calc(100% - 25px);
                            translate: 0px 2px;
                            font-weight: var(--font-weight-semibold);
                        }

                        &:hover {
                            outline: 2px solid var(--color-tertiary);
                            box-shadow: 0 -0.5px 4px 1px #38383814;
                        }

                        &.select2-default {
                            color: var(--gray-300);
                            font-size: var(--font-size-xsmall);
                            >.select2-chosen {
                                font-weight: var(--font-weight-regular);
                            }
                        }
                    }

                    &.select2-allowclear {

                        .select2-choice {

                            .select2-chosen {
                                width: calc(100% - 35px);
                            }

                            abbr {
                                display: inline-block;
                                top: var(--spacing-xxxxxlarge);
                            }
                        }
                    }

                    &.select2-dropdown-open {

                        .select2-choice {
                            outline: 2px solid var(--blue-200);
                            box-shadow: 0 -0.5px 3px 1px #38383814;
                            background-color: var(--color-text-100) !important;
                        }
                    }

                    &.select2-container-disabled {

                        .select2-search-choice-close {
                            display: none;
                            background: none;
                        }

                        .select2-choice {
                            outline: none;
                            box-shadow: none;
                            padding-bottom: 0px;
                            background-color: transparent;

                            >.select2-chosen {
                                margin-left: -10px;
                                margin-top: var(--spacing-large);
                                color: inherit !important;
                                translate: 0px;

                                &:empty,
                                &:blank {
                                    margin-top: var(--spacing-small);

                                    &::before {
                                        content: "-";
                                        color: var(--gray-700) !important;
                                        font-weight: var(--font-weight-bold) !important;
                                        font-size: var(--font-size-small) !important;
                                    }
                                }
                            }

                            .select2-arrow {
                                display: none;
                            }
                        }

                        &.select2-allowclear {
                            .select2-choice {
                                >.select2-chosen {
                                    margin-left: 0px;
                                }
                            }
                        }
                    }
                }

                //// Multi Select Field //// 
                .select2-container-multi {

                    .select2-choices {
                        min-height: 54.5px;
                        gap: var(--spacing-standard);
                        width: 100%;
                        padding-top: 26px;
                        line-height: 0px;
                        box-sizing: border-box;
                        background-color: var(--white);
                        list-style: none;
                        box-shadow: none !important;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        overflow-x: scroll;
                        .default-scroll;
                        height: auto;
                        margin: 0;
                        position: relative;
                        cursor: text;

                        li {
                            translate: 0px 6px;
                        }

                        .select2-search-choice {
                            gap: 4px;
                            color: var(--white);
                            background-color: var(--color-tertiary);
                            padding: 1px 6px;
                            font-size: var(--font-size-xxsmall);
                            font-weight: var(--font-weight-bold);
                            border-radius: var(--spacing-xxxlarge);
                            box-shadow: 0px 0px 1px 0px #0000000F, 0px 1px 2px -1px #0000000A;
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            text-wrap: nowrap;
                            position: relative;
                            cursor: default;
                            background-clip: padding-box;
                            -webkit-touch-callout: none;
                            -webkit-user-select: none;
                            -moz-user-select: none;
                            -ms-user-select: none;
                            user-select: none;

                            .select2-search-choice-close {
                                display: block;
                                position: relative;
                                filter: brightness(0) invert(1);
                                right: 0;
                                padding: 0px var(--spacing-xsmall);
                            }

                            .select2-chosen {
                                cursor: default;

                            }

                        }

                        .select2-search-field {
                            margin: 0;
                            padding: 0;
                            white-space: nowrap;

                            input {
                                color: var(--gray-700);
                                border: 0;
                                width: 100%;
                                border-radius: var(--radius-xxsmall);
                                box-sizing: border-box;
                                font-size: var(--font-size-small);
                                font-weight: var(--font-weight-semibold);
                                padding: 0px;
                                outline: 0;
                                min-height: auto;
                                -webkit-box-shadow: none;
                                box-shadow: none;
                                background: transparent !important;
                            }
                        }

                        .select2-search-choice-focus {
                            // background: #d4d4d4; // back space focus
                        }

                    }

                    &.select2-container-active {
                        .select2-choices {
                            outline: 2px solid var(--gray-100);
                            -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
                            box-shadow: 0 0 5px rgba(0, 0, 0, .3);
                        }
                    }


                    &.select2-container-disabled {

                        .select2-choices {
                            background-color: transparent;
                            cursor: default;
                            outline: none;
                            padding-left: 0px;
                            padding-bottom: 0px;
                            padding-top: 26px;
                            margin-bottom: -10px;
                            gap: 0px;

                            li {
                                translate: none;
                            }

                            .select2-search-choice {
                                outline: 0;
                                background-color: transparent;
                                color: var(--gray-700);
                                padding-left: 0px;
                                padding-bottom: 0px;
                                gap: 0px;
                                translate: 0px 2px;

                                .select2-search-choice-close {
                                    display: none;
                                    background: none;
                                }

                                &:not(:nth-last-child(2))::after {
                                    content: ",";
                                }
                            }

                            &:hover {
                                outline: none;
                                box-shadow: none;
                            }

                            &:has(li.select2-search-field:only-child) {

                                &::before {
                                    content: "-";
                                    translate: 0px 2px;
                                    color: var(--gray-700) !important;
                                    font-weight: var(--font-weight-bold) !important;
                                    font-size: var(--font-size-small) !important;
                                }
                            }
                        }
                    }
                }
            }

            &:has(.label-line) {

                .control-label {
                    width: 94px !important;
                    left: 0 !important;
                    top: 11px;
                    justify-content: flex-end;
                    padding-right: var(--spacing-large);
                }

                .select2-container {

                    .select2-choice {
                        outline: none !important;
                        box-shadow: none !important;
                        border-bottom: 1px solid var(--gray-300) !important;
                        padding-top: 10px !important;
                        min-height: 40px !important;
                        border-radius: unset !important;

                        abbr {
                            top: 10px !important;
                        }

                        >.select2-chosen {
                            translate: unset !important;
                        }
                    }

                    &.select2-container-disabled {
                        .select2-choice {
                            >.select2-chosen {
                                margin-top: 0px;
                            }
                        }
                    }
                }
            }

            &:has(.label-line):has(.select2-dropdown-open) {
                .control-label {
                    left: 0px !important;
                }
            }

            &:has(.manage) {
                .controls {
                    width: calc(100% - 28px);
                }
            }

            &.has-error {
                .controls {
                    .select2-container {
                        .select2-choice {
                            outline: 2px solid var(--color-error);
                        }

                        &.select2-container-disabled {
                            .select2-choice>.select2-chosen {
                                margin-left: 0px;
                                margin-top: 0px;
                            }
                        }
                    }
                }
            }

            &:has(.select2-container) {
                .control-label {
                    width: calc(100% - 80px);
                }
            }

            &:has(.select2-dropdown-open) {
                .control-label {
                    left: 20px !important;
                }
            }
        }
    }

    .cardread {

        .form-horizontal {
            .form-group {

                .controls,
                .controls-grid {

                    .select2-container,
                    .select2-container-multi {

                        .select2-choice,
                        .select2-choices {
                            border: none;
                            min-height: auto;
                            margin-bottom: 0px;
                            padding: 0;
                            overflow-x: auto;
                            min-height: 17px;

                            li {
                                translate: 0px;
                            }

                            >.select2-chosen {
                                margin-left: 0px;
                                margin-top: 0px;
                                translate: 0px;

                                &:empty,
                                &:blank {
                                    margin-top: 0px;
                                }
                            }

                            .select2-search-choice {
                                line-height: normal;
                                padding-top: 0px;
                                color: var(--gray-700) !important;
                                font-weight: var(--font-weight-bold) !important;
                                font-size: var(--font-size-small) !important;
                            }

                            .select2-search-field {
                                display: none;
                            }

                            .select2-arrow {
                                display: none;
                            }

                        }

                        .select2-container-disabled {

                            .select2-choice,
                            .select2-choices {
                                border: none;
                                min-height: auto;
                            }
                        }
                    }

                    .select2-container-multi {

                        &.select2-container-disabled {

                            .select2-choices {
                                border: none;
                                background-color: transparent;

                                .select2-search-choice {
                                    translate: 0px;
                                }
                            }
                        }
                    }
                }

                &:has(.label-line) {
                    .select2-choice {
                        margin-top: 0;
                        padding-top: 0 !important;
                        height: auto;
                        min-height: auto !important;
                        margin-left: 0;
                        width: 100% !important;
                        border-bottom: none !important;
                    }
                }

                &:has(.manage) {
                    .controls {
                        width: 100%;
                    }
                }
            }
        }
    }
}

.select2-drop {
    width: 100%;
    position: absolute;
    z-index: 999999;
    top: 100%;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-medium);
    box-shadow: 0px 3px 5px -2px #0000000A, 0px 0px 1px -2px #0000000A, 0px 3px 6px 2px #00000014;

    &.select2-drop-active.select2-drop-above {
        margin-top: -2px;
    }

    &.select2-drop-active {
        margin-top: 1px;

        .select2-search {
            padding: var(--spacing-small);

            input {
                margin-bottom: var(--spacing-small);
                width: 100%;
                min-height: 44px;
                border: none;
                box-sizing: border-box;
                outline: 1px solid var(--gray-100);
                border-radius: var(--radius-medium);
                padding: var(--spacing-large);
                color: var(--gray-700);
                background-color: var(--white);
                font-size: var(--font-size-small);
                font-weight: var(--font-weight-semibold);
                line-height: 0px;
                box-shadow: 0px 1px 2px 1px #38383814 inset;

                &::placeholder {
                    color: var(--gray-300);
                }

                &:hover {
                    outline: 2px solid var(--gray-300);
                }

                &:focus {
                    outline: 2px solid var(--blue-200);
                }
            }
        }

        .select2-results {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-small);
            padding: var(--spacing-small);

            .select2-result-label {
                background-color: transparent;
                min-height: 32px;
                cursor: pointer;
                border-radius: var(--radius-small);
                font-size: var(--font-size-xsmall);
                padding: var(--spacing-large) var(--spacing-large) var(--spacing-large) var(--spacing-standard);
                font-weight: var(--font-weight-semibold);
                color: var(--gray-700);
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;

                .select2-inventory-template {
                    display: flex;
                    flex-direction: column;
                    width: 100%;

                    .select2-inventory-template-stock {
                        color: var(--gray-500);
                        padding-left: 4px;
                        margin-top: 4px;
                    }

                    .select2-inventory-template-stock.warning {
                        color: var(--color-error-500) !important;
                    }

                    .select2-inventory-template-right-label {
                        float: right;
                        color: var(--gray-500);
                        font-size: var(--font-size-xxsmall);
                        white-space: no-wrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 50%;
                        padding-left: 4px;
                    }

                    .select2-inventory-template-right-sublabel {
                        float: right;
                        color: var(--gray-500);
                        font-size: var(--font-size-xxsmall);
                        white-space: no-wrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 50%;
                        padding-left: 4px;
                    }
                }

                .select2-label-template {
                    display: flex;
                    flex-direction: column;

                    .select2-label-template-sublabel {
                        display: --webkit-box;
                        --webkit-line-clamp: 2;
                        --webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 1.2em;
                        word-wrap: break-word;
                        max-width: 100%;
                        padding-left: 4px;
                        margin-top: 4px;
                        font-size: var(--font-size-xxsmall);
                        white-space: no-wrap;
                        color: var(--gray-500);
                    }
                }

                .select2-cmp-instr-template {
                    display: flex;
                    flex-direction: column;

                    .select2-label-template-sublabel {
                        display: --webkit-box;
                        --webkit-line-clamp: 2;
                        --webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 1.2em;
                        word-wrap: break-word;
                        max-width: 100%;
                        padding-left: 4px;
                        margin-top: 4px;
                        font-size: var(--font-size-xxsmall);
                        white-space: no-wrap;
                        color: var(--gray-500);
                    }
                }

                &:hover {
                    background-color: var(--brand-100);
                    color: var(--gray-700);

                    a {
                        background-color: var(--brand-100);
                        color: var(--gray-700);
                    }
                }

                &.active,
                &.select2-highlighted {
                    background-color: var(--brand-100);
                    color: var(--gray-700);
                }
            }

            .select2-highlighted {

                .select2-result-label {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    background-color: var(--brand-100);
                    color: var(--gray-700);

                    &::after {
                        content: "";
                        float: right;
                        background-image: url('../../../public/icons/common/outlined/check.svg');
                        background-repeat: no-repeat;
                        background-size: contain;
                        background-position: center;
                        width: 20px;
                        height: 20px;
                    }
                }
            }
        }
    }
}
.select2-drop.select2-drop-active .select2-results .select2-highlighted .select2-result-label:has(.select2-inventory-template-stock)::after {
	display: none !important;
}