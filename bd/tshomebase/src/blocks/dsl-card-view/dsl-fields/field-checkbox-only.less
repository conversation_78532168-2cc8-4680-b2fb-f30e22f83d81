.form-group {

    &:has(.controls > div.checkbox-only)>.control-label {
        top: 17px !important;
        left: 70px !important;
        width: calc(100% - 70px) !important;
    }

    .controls {

        &:has(> div.checkbox-only) {
            width: 55px;
        }

        >.checkboxes {
            &.checkbox-only {
                label.checkbox {
                    padding: var(--spacing-xsmall) !important;
                    color: var(--color-text) !important;

                    input+span {
                        display: none;
                    }

                    span {
                        width: 20px;
                        height: 20px;
                    }

                    .slider-checkbox-container {
                        position: relative;
                        width: 44px !important;
                        height: 24px;
                        background-color: var(--color-text-200);
                        border-radius: var(--radius-xlarge);
                        -webkit-transition: all 0.2s ease;
                        -moz-transition: all 0.2s ease;
                        -o-transition: all 0.2s ease;
                        transition: all 0.2s ease;
                        box-shadow: 0px 1px 2px 0px #38383814 inset;

                        &.slider-checkbox-container-toggled {
                            background-color: var(--color-tertiary);
                            box-shadow: 0px 1px 2px 0px #38383814 inset, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px var(--color-tertiary);
                        }

                        &.checkbox-only-disabled {
                            background-color: var(--color-text-100);
                            cursor: not-allowed;
                            box-shadow: none;

                            &.slider-checkbox-container-toggled {
                                background-color: var(--disabled-background-200);

                                .slider-checkbox {
                                    &.slider-checkbox-toggled {
                                        background-color: var(--color-text-100);
                                    }
                                }
                            }

                            &:hover {
                                box-shadow: none;
                            }

                            .slider-checkbox {
                                background-color: var(--disabled-background-200);
                            }
                        }

                        &:hover {
                            box-shadow: inset 0px 1px 2px 0px #38383814, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #91B9DE;
                        }

                        .slider-checkbox {
                            position: relative;
                            cursor: pointer;
                            -webkit-transition: left 0.2s ease;
                            -moz-transition: left 0.2s ease;
                            -o-transition: left 0.2s ease;
                            transition: left 0.2s ease;
                            background-color: #ffffff;
                            border-radius: 50% !important;
                            width: 20px !important;
                            height: 20px !important;
                            box-shadow: 0px 1px 3px 0px #0A0D121A, 0px 1px 2px -1px #0A0D121A;
                            top: var(--spacing-xxsmall);
                            left: var(--spacing-xxsmall);
                            bottom: var(--spacing-xxsmall);
                        }

                        .slider-checkbox-toggled {
                            left: 22px;
                        }
                    }

                    &:has(.checkbox-only-disabled) {
                        * {
                            cursor: not-allowed !important;
                        }
                    }
                }
            }
        }
    }

    &.has-error {
        .checkbox-only {
            .slider-checkbox-container {
                background-color: var(--color-error) !important;
                box-shadow: 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #E58787;

                &.slider-checkbox-container-toggled {
                    background-color: var(--color-error) !important;
                    box-shadow: 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #E58787 !important;
                }
            }
        }
    }
}

.cardread {
    .form-group {

        &:has(.controls > div.checkbox-only)>.control-label {
            top: 0 !important;
            left: 0 !important;
            width: calc(35% - 8px) !important;
        }

        .controls {

            >.checkboxes {
                &.checkbox-only {
                    label.checkbox {

                        >input {
                            margin: 0px;
                        }

                        &:not(:has(input:checked)) {
                            display: block !important;
                        }

                        .slider-checkbox-container,
                        .slider-checkbox-container-toggled,
                        .checkbox-only-disabled,
                        .slider-checkbox,
                        .slider-checkbox-toggled {
                            all: unset !important;
                        }

                        .slider-checkbox {

                            &::before {
                                content: "-";
                                color: var(--gray-700);
                                font-weight: var(--font-weight-bold);
                                font-size: var(--font-size-small);
                            }

                            &.slider-checkbox-toggled {
                                &::before {
                                    content: "ab";
                                    color: transparent;
                                    background-image: url('../../../public/icons/common/outlined/field-checkbox-only-checked-value.svg');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    background-position: center;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}