#application {
    .form-horizontal {
        .form-group {
            .controls-grid {

                &.subform {
                    .control-label {
                        display: none;
                    }

                    .table {

                        :focus-visible {
                            outline: none;
                        }

                        :focus {
                            outline: none;
                        }

                        .has-error-grid {
                            border: 2px solid var(--color-error) !important;
                            border-color: var(--color-error) !important;
                            border-radius: 4px;
                        }

                        input {
                            min-width: 50px !important;
                        }

                        .numeral-readonly {
                            text-align: right !important;
                            padding-right: var(--spacing-standard) !important;
                        }

                        input[type="checkbox"] {
                            min-width: fit-content !important;
                        }

                        textarea {
                            max-height: 60px;
                            min-width: 80px !important;
                            min-height: 39px;
                            height: 39px;
                        }

                        .select2-container {
                            width: 100%;
                        }

                        select {
                            width: 100%;
                        }

                        .btn-group {
                            min-width: 100px !important;
                        }

                        .checkboxes:not(.checkbox-only) {
                            padding: 0px !important;
                            margin: 0px !important;
                        }

                        .checkboxes.checkbox-only {
                            min-width: 80px !important;
                            padding: 0px !important;
                            margin: 0px !important;
                        }

                        tbody {
                            tr {
                                td {
                                    color: var(--gray-700);
                                    font-weight: var(--font-weight-bold);
                                    font-size: var(--spacing-standard) var(--font-size-xsmall);
                                    height: 44px;
                                    vertical-align: top;

                                    &.delete-subrow {
                                        vertical-align: middle;
                                    }

                                    &.edit-subrow {
                                        display: table-cell;
                                        align-items: center;
                                        vertical-align: middle;

                                        i {
                                            text-align: center;
                                            vertical-align: middle;

                                            &::before {
                                                content: "abc";
                                                display: flex;
                                                color: transparent;
                                                background-image: url('../../../public/icons/common/outlined/edit-row-outline.svg');
                                                background-repeat: no-repeat;
                                                background-position: center;
                                                width: 28px;
                                            }
                                        }
                                    }

                                    .form-horizontal {
                                        padding: 0px;
                                        width: 100%;

                                        .form-group {
                                            padding: 0;

                                            .controls {

                                                select.select2field {
                                                    appearance: none;
                                                    -webkit-appearance: none;
                                                    -moz-appearance: none;
                                                    background: url("../../../public/icons/common/outlined/chevron-down-outline.svg") no-repeat right 10px center;

                                                    option {
                                                        background-color: transparent;
                                                        min-height: 32px;
                                                        line-height: var(--line-height-medium);
                                                        cursor: pointer;
                                                        border-radius: var(--radius-small);
                                                        font-size: var(--font-size-small);
                                                        padding: var(--spacing-large) var(--spacing-large) var(--spacing-large) var(--spacing-standard);
                                                        font-weight: var(--font-weight-semibold);
                                                        color: var(--gray-700);
                                                        -webkit-touch-callout: none;
                                                        -webkit-user-select: none;
                                                        -moz-user-select: none;
                                                        -ms-user-select: none;
                                                        user-select: none;

                                                        &:hover {
                                                            background-color: var(--brand-100);
                                                            color: var(--gray-700);
                                                        }
                                                    }
                                                }

                                                .select2-container.select2-container-disabled {
                                                    .select2-choice {
                                                        >.select2-chosen {
                                                            margin-left: 0px;
                                                            margin-top: 0px;
                                                        }
                                                    }
                                                }

                                                .esignfield {
                                                    min-height: 40px;
                                                    padding: 0px var(--spacing-large);


                                                    .esign-link {
                                                        height: 40px;

                                                        .esign-text-container {
                                                            width: 160px;
                                                            translate: 0px;

                                                            >div {
                                                                font-size: 10px;
                                                                line-height: var(--line-height-xxsmall);
                                                            }

                                                            >div:first-child {
                                                                translate: 0px;
                                                            }
                                                        }

                                                        .esign-image {
                                                            height: 50px;
                                                        }

                                                        .edit-esign-icon {
                                                            width: 20px;
                                                            height: 20px;
                                                        }
                                                    }

                                                    &:has(.has-signed) {
                                                        box-shadow: none;
                                                        outline: none;
                                                        min-height: 44px;

                                                        .esign-image {
                                                            height: 50px;
                                                        }
                                                    }
                                                }

                                                .barcode-hid,
                                                .barcode-cam {
                                                    font-size: 10px;
                                                    padding: var(--spacing-small) var(--spacing-xsmall);
                                                    gap: var(--spacing-xsmall);

                                                    span {

                                                        &.icon-handheld-scanner,
                                                        &.icon-camera-scanner {
                                                            width: 18px;
                                                            height: 18px;
                                                        }
                                                    }
                                                }

                                            }

                                            &.has-error {
                                                .help-container {
                                                    margin-top: var(--spacing-large);
                                                }
                                            }

                                        }
                                    }
                                }
                            }
                        }

                        tfoot {
                            tr {
                                th {
                                    padding: 0px;

                                    .dsl-action-btn {

                                        &.add-subrow {

                                            .inner-cont {

                                                .icon {}

                                                .label {
                                                    color: var(--brand-700) !important;
                                                }
                                            }

                                            &:hover {

                                                .inner-cont {

                                                    .label {}
                                                }
                                            }

                                            &:focus {

                                                .inner-cont {

                                                    .label {}
                                                }
                                            }

                                            &.disabled {

                                                .inner-cont {

                                                    .icon {}

                                                    .label {}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }

                    .suform-editable-add {
                        display: flex;
                        justify-content: flex-start;
                        gap: var(--spacing-standard);

                        .dsl-action-btn {
                            margin-top: var(--spacing-large);
                            margin: var(--spacing-large) 0px 4px 4px; // to show outline in focus state
                            cursor: pointer;
                            padding: var(--spacing-standard) var(--spacing-xlarge);
                            text-transform: capitalize;
                            gap: var(--spacing-xsmall);
                            border-radius: var(--radius-medium);
                            width: auto;
                            min-width: 30px !important;
                            max-width: fit-content;
                            min-height: 36px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            flex-shrink: 0;

                            .inner-cont {
                                .icon {
                                    width: 20px;
                                    height: 20px;
                                    background-repeat: no-repeat;
                                    background-position: center;
                                    background-size: contain;
                                    filter: none !important;
                                }

                                .label {
                                    font-size: var(--font-size-xsmall) !important;
                                    font-weight: var(--font-weight-medium);
                                    background-color: transparent;
                                    filter: none !important;
                                }
                            }

                            &.disabled {
                                cursor: not-allowed !important;
                                opacity: 1 !important;
                                filter: none !important;
                            }

                            &.add-subrow-dum,
                            &.add-subrow-flyout {
                                box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                background-color: var(--color-text-50) !important;
                                margin-top: 0px;

                                .inner-cont {

                                    .icon {
                                        background-image: url('../../../public/icons/common/outlined/plus-outlined.svg');
                                    }

                                    .label {
                                        color: var(--brand-700) !important;
                                    }
                                }

                                &:hover {
                                    background-color: var(--brand-50) !important;

                                    .inner-cont {
                                        .icon {
                                            filter: none;
                                        }

                                        .label {
                                            color: var(--brand-800) !important;
                                            background-color: transparent;
                                        }
                                    }
                                }

                                &:focus {
                                    background-color: var(--brand-100);
                                    box-shadow: 0px 0px 0px 1px #0A0D122E inset, 0px -2px 0px 0px #0A0D120D inset, 0px 1px 2px 0px #0A0D120D, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px var(--color-tertiary);

                                    .inner-cont {


                                        .label {
                                            color: var(--brand-700);
                                        }
                                    }
                                }

                                &.disabled {
                                    background-color: var(--white) !important;
                                    outline: 1px solid var(--brand-100);
                                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                    opacity: 1 !important;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(65%) sepia(6%) saturate(120%) hue-rotate(180deg) brightness(95%) contrast(85%) !important;
                                        }

                                        .label {
                                            color: var(--color-text-400) !important;
                                            filter: none !important;
                                        }
                                    }
                                }
                            }

                            &.add-subrow-flyout {

                                .inner-cont {

                                    .icon {
                                        background-image: url('../../../public/icons/common/outlined/add-full-form-outline.svg');
                                    }

                                    .label {
                                        color: var(--color-text-700) !important;
                                    }
                                }

                                &:hover {
                                    background-color: var(--brand-50) !important;

                                    .inner-cont {
                                        .icon {
                                            filter: invert(12%) sepia(10%) saturate(500%) hue-rotate(200deg) brightness(90%) contrast(100%) !important;
                                            mix-blend-mode: difference;
                                        }

                                        .label {
                                            color: var(--color-text-800) !important;
                                            background-color: transparent;
                                        }
                                    }
                                }

                                &:focus {
                                    background-color: var(--white);
                                    box-shadow: 0px -2px 0px 0px #0A0D120D inset, 0px 0px 0px 1px #0A0D122E inset, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #837BB2;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(25%) sepia(6%) saturate(300%) hue-rotate(180deg) brightness(90%) contrast(95%) !important;
                                            mix-blend-mode: luminosity;
                                        }

                                        .label {
                                            color: var(--brand-700);
                                        }
                                    }
                                }

                                &.disabled {
                                    background-color: var(--color-text-100) !important;
                                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                    opacity: 1 !important;
                                    outline: none;

                                    .inner-cont {

                                        .icon {
                                            filter: invert(25%) sepia(6%) saturate(300%) hue-rotate(180deg) brightness(99%) contrast(95%) !important;
                                            mix-blend-mode: luminosity;
                                        }

                                        .label {
                                            color: var(--color-text-400) !important;
                                            filter: none !important;
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }

            &.has-error {

                .controls-grid {}

            }
        }
    }

}