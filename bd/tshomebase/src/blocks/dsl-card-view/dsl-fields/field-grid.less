#application {
    .form-horizontal {
        .form-group {
            &:has(.table, .dataTable) {
                .control-label {
                    text-transform: capitalize;
                    font-size: var(--font-size-small);
                    margin-bottom: 0px;
                    margin-top: var(--card-area-h3-tmp-mt);
                    color: var(--color-tertiary) !important;
                    border-bottom: none;
                    line-height: 31px;
                }
            }

            .controls-grid {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-large);
                width: 100%;
                overflow-x: auto;

                .table {
                    overflow: auto;
                    border-collapse: separate;
                    border-spacing: var(--spacing-standard) var(--spacing-xxsmall-xsmall);
                    margin-bottom: auto;

                    thead,
                    tbody {
                        tr {

                            th {
                                &:not(.offscreen) {
                                    min-width: 80px;
                                    padding-right: var(--spacing-standard) !important;
                                    background-color: transparent !important;

                                    &:last-child {
                                        padding-right: 0px !important;
                                    }

                                    &.icon-placeholder {
                                        min-width: 30px !important;
                                    }
                                }
                            }

                            td {
                                &:not(.offscreen) {
                                    background-color: transparent !important;
                                }
                            }


                        }
                    }

                    thead {
                        tr {
                            th {
                                font-weight: var(--font-weight-medium);
                                color: var(--gray-400);
                                font-size: var(--font-size-xxsmall);
                                padding: var(--spacing-small-standard) 0px;
                                line-height: var(--line-height-xsmall-small);
                                word-break: break-word;
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;

                                &.claim-field {
                                    .claim-field-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.important {
                                    .important-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.money {
                                    .money-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.discount {
                                    .discount-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.status {
                                    .status-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.check-field {
                                    .check-field-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.fdb-field {
                                    .fdb-field-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.cms-1500-field {
                                    .cms-1500-field-vc;

                                    &::before {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }

                                &.sorting,
                                &.sorting_desc,
                                &.sorting_asc {
                                    padding-right: 20px !important;
                                    position: relative;

                                    &::after {
                                        position: absolute;
                                        right: 5px;
                                    }
                                }


                                &.handle,
                                &.delete-row,
                                &.delete-subrow {
                                    width: 30px;
                                }

                                &.required-th {
                                    color: var(--color-error) !important;

                                    * {
                                        color: var(--color-error) !important;
                                    }

                                    &::before {
                                        filter: invert(70%) sepia(40%) saturate(500%) hue-rotate(330deg) brightness(140%) contrast(90%);
                                    }
                                }
                            }
                        }
                    }

                    tbody {
                        tr {

                            td {
                                background-color: transparent;
                                vertical-align: top;
                                padding: 0px;

                                >i {
                                    cursor: pointer;
                                }

                                &.handle,
                                &.delete-row,
                                &.delete-subrow {
                                    vertical-align: middle;
                                    text-align: center;
                                    display: table-cell;

                                    i {
                                        content: "abc";
                                        color: transparent;
                                    }
                                }


                                &.handle {
                                    i.glyphicon-resize-vertical {
                                        -webkit-text-stroke: 1px #e58888;
                                        color: transparent;

                                        &::before {
                                            font-size: var(--font-size-xsmall);
                                        }
                                    }
                                }

                                .select2-container,
                                .select2-container-multi {

                                    .select2-choice,
                                    .select2-choices {
                                        outline: none;
                                        border: 1px solid var(--gray-100);
                                        padding-top: var(--spacing-large);
                                        min-height: 44px;

                                        &.select2-default {
                                            color: var(--gray-400);
                                        }

                                        &:hover {
                                            outline: none;
                                            border: 1px solid var(--color-tertiary);
                                        }
                                    }

                                    &.select2-allowclear {
                                        .select2-choice {
                                            abbr {
                                                top: var(--spacing-xlarge);
                                            }
                                        }
                                    }
                                }

                                .select2-container-multi {
                                    .select2-choices {
                                        padding: 5px 8px;
                                        padding-bottom: 0px;

                                        li {
                                            translate: 0px;
                                        }
                                    }
                                }

                                &.delete-row,
                                &.delete-subrow {
                                    content: "";

                                    i {
                                        &::before {
                                            content: "";
                                            padding: 1px 9px;
                                            background-image: url('../../../public/icons/common/outlined/delete-outline.svg');
                                            background-repeat: no-repeat;
                                            background-size: 18px;
                                        }
                                    }
                                }

                                input,
                                select,
                                textarea {
                                    width: 100%;
                                    border: none;
                                    border: 1px solid var(--gray-100);
                                    min-height: 44px;
                                    border-radius: var(--radius-medium);
                                    padding: var(--spacing-large);
                                    padding-top: var(--spacing-large);
                                    line-height: 20px;
                                    box-sizing: border-box;
                                    color: var(--gray-700);
                                    background-color: var(--white);
                                    font-size: var(--font-size-small);
                                    font-weight: var(--font-weight-semibold);
                                    box-shadow: 0px 1px 2px 1px #38383814 inset;

                                    &::placeholder {
                                        color: var(--gray-400);
                                    }

                                    &:hover {
                                        border: 1px solid var(--gray-300);
                                    }

                                    &:focus {
                                        border: 1px solid var(--blue-200);
                                    }

                                }

                                &[subfield-key],
                                &.gridedit-subrow {
                                    .form-group {
                                        padding: 0px;

                                        .controls {
                                            cursor: pointer;

                                            * {
                                                cursor: pointer;
                                            }

                                            >input[readonly] {
                                                padding-top: 0px !important;
                                                margin-bottom: 0;
                                                margin-left: 0;
                                            }

                                            &:has(input.no-val):before {
                                                top: var(--spacing-large);
                                                left: var(--spacing-large);
                                            }

                                            .checkboxes {
                                                flex-wrap: nowrap;
                                                overflow-x: auto;

                                                &::-webkit-scrollbar {
                                                    width: 2px;
                                                    height: 2px;
                                                }

                                                &:hover {

                                                    &::-webkit-scrollbar-thumb {
                                                        background-color: lightgrey;
                                                    }
                                                }


                                                label.checkbox {
                                                    padding: 0px;
                                                    min-height: auto;
                                                    min-width: min-content;
                                                    padding: var(--spacing-large) var(--spacing-xxxxlarge) !important;

                                                    input {
                                                        display: none;
                                                    }
                                                }

                                                &.checkbox-only {
                                                    label.checkbox {
                                                        padding: var(--spacing-large) 4px !important;
                                                    }
                                                }
                                            }
                                        }



                                        &:has(input[readonly], input[disabled], textarea[readonly], textarea[disabled], select[readonly], select[disabled]) {
                                            .controls {
                                                cursor: not-allowed;

                                                * {
                                                    cursor: not-allowed;
                                                }
                                            }
                                        }
                                    }
                                }

                                &[subfield-key] {

                                    >input[readonly],
                                    >input[disabled] {
                                        background-color: var(--white);
                                        cursor: not-allowed;
                                        color: var(--gray-600);
                                    }
                                }

                                textarea {
                                    height: 44px;
                                    padding: 10px;
                                    translate: 0px 3px;
                                }

                                >i.glyphicon-zoom-in {
                                    top: var(--table-tr-th-td-i-zoom-top);
                                    cursor: zoom-in;
                                }
                            }
                        }
                    }

                }

                .form-group {
                    gap: 0;
                }

                tfoot {
                    tr {
                        th {
                            padding: 0px;

                            .dsl-action-btn {
                                margin: var(--spacing-large) 0px 4px 4px;
                                cursor: pointer;
                                padding: var(--spacing-standard) var(--spacing-xlarge);
                                text-transform: capitalize;
                                gap: var(--spacing-xsmall);
                                border-radius: var(--radius-medium);
                                width: auto;
                                min-width: 30px !important;
                                max-width: fit-content;
                                min-height: 36px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                flex-shrink: 0;

                                .inner-cont {
                                    .icon {
                                        width: 20px;
                                        height: 20px;
                                        background-repeat: no-repeat;
                                        background-position: center;
                                        background-size: contain;
                                        filter: none !important;
                                    }

                                    .label {
                                        font-size: var(--font-size-xsmall) !important;
                                        font-weight: var(--font-weight-medium);
                                        background-color: transparent;
                                        filter: none !important;
                                    }
                                }

                                &.disabled {
                                    cursor: not-allowed !important;
                                    opacity: 1 !important;
                                    filter: none !important;
                                }

                                &.add-row,
                                &.add-subrow {
                                    box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;
                                    background-color: var(--color-text-50) !important;

                                    .inner-cont {

                                        .icon {
                                            background-image: url('../../../public/icons/common/outlined/plus-outlined.svg');
                                            background-repeat: no-repeat;
                                            background-position: center;
                                        }

                                        .label {
                                            color: var(--brand-700) !important;
                                        }
                                    }

                                    &:hover {
                                        background-color: var(--brand-50) !important;

                                        .inner-cont {

                                            .label {
                                                color: var(--brand-800) !important;
                                                background-color: transparent;
                                            }
                                        }
                                    }

                                    &:focus {
                                        background-color: var(--brand-100);
                                        box-shadow: 0px 0px 0px 1px #0A0D122E inset, 0px -2px 0px 0px #0A0D120D inset, 0px 1px 2px 0px #0A0D120D, 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px var(--color-tertiary);

                                        .inner-cont {


                                            .label {
                                                color: var(--brand-700);
                                            }
                                        }
                                    }

                                    &.disabled {
                                        background-color: var(--white) !important;
                                        outline: 1px solid var(--brand-100);
                                        box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset;

                                        .inner-cont {

                                            .icon {
                                                filter: invert(65%) sepia(6%) saturate(120%) hue-rotate(180deg) brightness(95%) contrast(85%) !important;
                                            }

                                            .label {
                                                color: var(--color-text-400) !important;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}