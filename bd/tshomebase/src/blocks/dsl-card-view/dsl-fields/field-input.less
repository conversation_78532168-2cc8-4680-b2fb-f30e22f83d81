#application {
    .form-horizontal {
        .form-group {

            .controls {

                >input,
                input.unit {
                    width: 100%;
                    border: none;
                    outline: 1px solid var(--gray-100);
                    min-height: 60px;
                    border-radius: var(--radius-medium);
                    padding: var(--spacing-large);
                    padding-top: 30px;
                    line-height: 0px;
                    box-sizing: border-box;
                    color: var(--gray-700);
                    background-color: #FDFDFD;
                    font-size: var(--font-size-small);
                    font-weight: var(--font-weight-semibold);
                    box-shadow: 0px 1px 2px 1px #38383814 inset;

                    &::placeholder {
                        color: var(--gray-300);
                    }

                    &:hover {
                        outline: 2px solid var(--gray-300);
                    }

                    &:focus {
                        outline: 2px solid var(--blue-200);
                    }
                }

                input.label-line {
                    outline: none;
                    box-shadow: none;
                    border-radius: unset;
                    border-bottom: 1px solid var(--gray-300);
                    padding-top: 10px;
                    min-height: auto;
                }

                >input[readonly],
                >input[disabled] {
                    background-color: transparent !important;
                    color: var(--gray-600);
                    outline: none;
                    box-shadow: none;
                    margin-left: -10px;
                    padding-left: var(--spacing-large) !important;
                    padding-top: 35px !important;
                    padding-bottom: 0px !important;
                    margin-bottom: -10px;
                }

                input[disabled].label-line,
                input[readonly].label-line {
                    padding-top: 10px !important;
                    min-height: auto;
                    margin-left: 0;
                    padding-bottom: 10px !important;
                    border-bottom: 1px solid var(--gray-300);
                    margin-bottom: 0px;
                }
            }

            .unit-field-control {
                .input-group {
                    width: 100%;
                    display: flex;
                    gap: 20px;

                    input.unit {
                        width: 100%;
                    }

                    input[readonly],
                    input[disabled] {
                        background-color: transparent !important;
                        color: var(--gray-600);
                        outline: none;
                        box-shadow: none;
                        margin-left: -10px;
                        padding-left: var(--spacing-large) !important;
                        padding-top: 35px !important;
                        padding-bottom: 0px !important;
                        margin-bottom: -10px;
                    }

                    .input-group-addon {
                        text-align: left;
                        min-width: 44px;
                        width: auto;
                        outline: 1px solid var(--gray-100);
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &:empty {
                            display: none;
                        }

                        &:last-child {
                            border-radius: var(--radius-medium) !important;
                        }
                    }
                }
            }

            &:has(.unit-field-control) {
                .control-label {
                    width: calc(50% - 30px);
                }
            }

            &:has(.label-line) {
                padding: 0px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                padding: 0px var(--spacing-large);

                .control-label {
                    position: relative;
                    width: 94px;
                    left: 0 !important;
                    justify-content: flex-end;
                    padding-right: var(--spacing-large);
                }

                .controls {
                    width: calc(100% - 110px);

                    &:has(input.no-val) {
                        &::before {
                            top: 7px;
                        }
                    }
                }

                &:has(.no-label) {
                    .controls {
                        width: 100%;
                    }
                }
            }

            &:has(input[readonly].unit, input[disabled].unit) {

                .control-label {
                    left: 20px !important;
                }

                input {
                    outline: 1px solid var(--gray-100) !important;
                    margin-left: 0px !important;
                }

                input[readonly],
                input[disabled] {
                    padding-top: 25px !important;
                    margin-bottom: 0px;
                }

                .input-group-addon {
                    height: 60px;
                    text-wrap: inherit;
                }
            }

            &:has(.datepicker) {
                .controls {
                    >div.picker {
                        width: calc(100% - 28px);
                    }
                }
            }

            &.has-error {
                .controls {
                    >input {
                        outline: 2px solid var(--color-error);
                    }

                    >input[readonly],
                    >input[disabled] {
                        outline: none;
                    }
                }

            }
        }
    }

    .cardread {
        .form-horizontal {
            .form-group {
                .controls {

                    >input,
                    input.unit,
                    >input[readonly],
                    >input[disabled] {
                        padding-top: 0px !important;
                        padding-left: 0px !important;
                        margin-left: 0px;
                    }

                    input.label-line {
                        border-bottom: unset;
                    }
                }

                &:has(.label-line) {
                    padding: 0px;

                    .controls {

                        input.label-line,
                        input.label-line,
                        input[disabled].label-line,
                        input[readonly].label-line {
                            margin-left: 0px !important;
                            width: 100%;
                            padding-bottom: 0px !important;
                            padding-left: 0px !important;
                        }

                        &:has(input.no-val) {
                            &::before {
                                top: 0;
                                left: 0;
                            }
                        }
                    }

                    .control-label {
                        justify-content: flex-start;
                        width: calc(45% - 14px) !important;
                        padding-right: 0px;
                    }
                }

                &:has(.unit-field-control input[readonly].unit, .unit-field-control input[disabled].unit) {

                    .control-label {
                        left: 0px;
                    }

                    .controls {

                        input {
                            outline: none;
                            margin-left: 0px;
                            width: 50%;
                            height: 17px;
                            outline: none !important;

                        }

                        input[readonly],
                        input[disabled] {
                            padding-top: 0px !important;
                            margin-bottom: 0px !important;
                        }

                        .input-group-addon {
                            height: 17px;
                            text-wrap: nowrap;
                            outline: none;
                            padding: 0px;
                        }
                    }
                }
            }
        }
    }
}