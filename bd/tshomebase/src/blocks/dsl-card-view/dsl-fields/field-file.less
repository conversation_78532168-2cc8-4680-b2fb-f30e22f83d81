#application {
    .form-group {
        .controls {

            .fileupload {
                padding: 0;

                iframe {
                    border: 0;
                    opacity: 0;
                    float: left;
                    z-index: 2;
                    width: 110px;
                    height: 75px;
                    top: 10px;
                    position: absolute;
                    left: calc(50% - 60px);
                }


                .file-upload-area-container {
                    outline: 1px solid var(--gray-100);
                    border-radius: var(--radius-xlarge);
                    width: 100%;
                    min-height: 112px;
                    padding: var(--spacing-xxxlarge) var(--spacing-xxxxxxlarge);
                    display: flex;
                    flex-direction: column;
                    gap: var(--spacing-large);
                    justify-content: space-between;

                    .file-icon-area {
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .icon-container {
                            outline: 1px solid var(--gray-100);
                            border-radius: var(--radius-medium);
                            width: 40px;
                            height: 40px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            cursor: pointer;

                            .upload-icon {
                                width: 20px;
                                height: 20px;
                                background-image: url("../../../public/icons/common/outlined/file-upload-outline.svg");
                                background-repeat: no-repeat;
                                background-position: center;
                            }
                        }
                    }

                    .file-text-area {
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .text-container {
                            width: auto;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            span.file-upload-text {
                                line-height: 1.2;

                                a {
                                    color: var(--color-tertiary);
                                    font-size: var(--font-size-xsmall);
                                    font-weight: var(--font-weight-bold);
                                    cursor: pointer;
                                }
                            }

                            span,
                            p {
                                color: var(--gray-700);
                                font-size: var(--font-size-xsmall);
                                font-weight: var(--font-weight-regular);
                            }
                        }
                    }
                }

                >span {
                    display: none;
                }

                &.has-file {

                    iframe {
                        display: none;
                    }

                    .file-upload-area-container {
                        display: none;
                    }

                    >span {
                        display: flex;
                        align-items: center;
                        gap: var(--spacing-xsmall);

                        a {
                            color: var(--color-primary-600);
                            font-size: var(--font-size-small);
                            font-weight: var(--font-weight-semibold);
                        }

                        .delete-file-icon {
                            width: 18px;
                            height: 18px;
                            background-image: url("../../../public/icons/common/outlined/trash-outline.svg");
                            background-repeat: no-repeat;
                            background-position: center;
                            cursor: pointer;
                        }

                    }
                }
            }

            &.has-error {}
        }
    }

}