.form-group {
    .controls {
        .checkbox-group {
            display: flex;
            gap: var(--spacing-large);
            flex-wrap: wrap;

            label.checkbox {
                background-color: var(--color-text-50);
                border-radius: var(--radius-xxxxxxlarge);
                padding: var(--spacing-xlarge) var(--spacing-xxxxlarge) !important;
                box-shadow: inset 0 1px 2px #ffffff1f, inset 0 -1px 2px #00000038, 0 0 2px #0000001f;
                width: auto !important;

                span {
                    font-weight: var(--font-weight-regular);
                    color: #838894; // color not found
                    font-size: var(--font-size-small);
                    background-color: unset !important;
                    line-height: 0px;
                }

                input:checked+span {
                    font-size: var(--font-size-small);
                    font-weight: var(--font-weight-semibold) !important;
                    color: #7C74A8 !important; // color not found
                }

                input+span:before {
                    border-radius: 50% !important;
                    border: none !important;
                    width: 16px !important;
                    height: 16px !important;
                    margin-right: 10px !important;
                    box-shadow: 0px -2px 2px 0px #FFFFFF14 inset, 0px 1px 2px 0px #0000001C inset;
                    background-color: var(--color-text-100);
                }

                input:checked+span:before {
                    background-image: url(../../../public/icons/common/checkbox-checked.svg) !important;
                    background-repeat: no-repeat;
                }


                &:has(input:disabled) {
                    cursor: not-allowed !important;
                    background-color: unset;
                    box-shadow: unset;

                    * {
                        cursor: not-allowed !important;
                    }

                    span {
                        background-color: transparent !important;
                    }

                    input:checked+span {
                        color: #5E636B !important; // color not found
                    }

                    input+span:before {
                        box-shadow: none;
                        background-color: var(--color-text-100) !important;
                    }

                    input:checked+span:before {
                        background-image: url(../../../public/icons/common/checkbox-checked-disabled.svg) !important;
                    }
                }
            }
        }
    }

    &.has-error {
        .checkbox-group {
            label.checkbox {
                border: 2px solid var(--color-error);

                span {
                    color: #D98080 !important; // color not found
                }

                input:checked+span {
                    color: #D98080 !important; // color not found
                }

                input:checked+span:before {
                    background-image: url(../../../public/icons/common/checkbox-checked-error.svg) !important;
                }
            }
        }
    }
}