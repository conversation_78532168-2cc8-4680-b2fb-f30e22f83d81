#application {

    .fieldgroup {

        .form-horizontal {

            .form-group:has(.barcode) {
                >label.control-label {
                    color: var(--gray-700);
                    font-weight: var(--font-weight-bold);
                    font-size: var(--font-size-xsmall);
                }

                .controls {
                    &:has(.barcode) {
                        >input {
                            all: unset;

                            &.barcode {
                                outline: none;
                                width: 100%;
                                padding: 0;
                                margin: 0;
                                border: 0;
                                color: #fff;
                                background: transparent !important;
                                position: absolute;
                            }
                        }
                    }

                    .barcode-cam,
                    .barcode-hid {
                        width: calc(50% - var(--spacing-large));
                        padding: var(--spacing-large);
                        position: relative;
                        border-radius: var(--radius-medium);
                        font-size: var(--font-size-xsmall);
                        font-weight: var(--font-weight-medium);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        float: left;
                        flex-wrap: wrap;
                        gap: var(--spacing-large);

                    }

                    .barcode-cam {
                        margin-right: var(--spacing-large);
                        background-color: var(--color-text-50);
                        color: var(--brand-700);
                        box-shadow: 0px 1px 2px 0px hsla(0, 0%, 100%, 0.122) inset, 0px -1px 2px 0px #00000038 inset;
                        cursor: pointer;
                        border: none;

                        .icon-camera-scanner {
                            background-image: url("../../../public/icons/common/outlined/phone.svg");
                            background-repeat: no-repeat;
                            background-size: contain;
                            width: 20px;
                            height: 20px;
                        }
                    }

                    .barcode-hid {
                        background-color: var(--color-tertiary);
                        cursor: pointer;
                        color: var(--white);
                        position: relative;
                        border: none;

                        .icon-handheld-scanner {
                            background-repeat: no-repeat;
                            background-image: url("../../../public/icons/common/outlined/scanner.png");
                            background-color: transparent;
                            background-size: contain;
                            width: 20px;
                            height: 20px;
                        }
                    }
                }
            }
        }
    }
}