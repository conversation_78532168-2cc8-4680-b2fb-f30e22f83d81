#application {
    .form-horizontal {
        .form-group {
            .controls {
                >textarea {
                    max-width: 100%;
                    width: 100%;
                    min-height: 60px;
                    height: 128px;
                    max-height: calc(100vh - 250px);
                    border: none;
                    outline: 1px solid var(--gray-100);
                    box-sizing: border-box;
                    color: var(--gray-700);
                    background-color: var(--white);
                    font-size: var(--font-size-small);
                    font-weight: var(--font-weight-semibold);
                    border-radius: var(--radius-medium);
                    padding: var(--spacing-large);
                    padding-top: 30px;
                    box-shadow: 0px 1px 2px 1px #38383814 inset;
                    resize: vertical;

                    &::placeholder {
                        color: var(--gray-300);
                    }

                    &:hover {
                        outline: 2px solid var(--gray-300);
                    }

                    &:focus {
                        outline: 2px solid var(--blue-200);
                    }
                }

                >textarea[readonly],
                >textarea[disabled] {
                    background-color: transparent !important;
                    outline: none;
                    box-shadow: none;
                    margin-left: -14px;
                    margin-bottom: -10px;
                    padding-left: var(--spacing-large) !important;
                    padding-top: 35px !important;
                    padding-bottom: 0px !important;
                    height: 42.5px;
                }
            }

            &.has-error {
                .controls {
                    >textarea {
                        outline: 2px solid var(--color-error);
                    }

                    >textarea[readonly],
                    >textarea[disabled] {
                        outline: none;
                    }
                }

            }

        }

        &:has(.controls textarea) .control-label {
            top: 10;
            width: calc(100% - 40px);
            padding-top: 8px;
            background: var(--white);
        }
    }


    .cardread {
        .form-horizontal {
            .form-group {
                .controls {

                    >textarea,
                    >textarea[readonly],
                    >textarea[disabled] {
                        padding-top: 0px !important;
                    }
                }

                &:has(.controls textarea) {
                    align-items: flex-start !important;

                    .control-label {
                        padding-top: 0px;
                        min-height: 17px;
                    }
                }
            }
        }
    }
}